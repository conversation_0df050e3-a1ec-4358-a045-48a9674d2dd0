{"cells": [{"cell_type": "code", "execution_count": null, "id": "f1d25657-0b6d-4cc8-891e-61ac42f27b08", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "!pip install tqdm\n", "from tqdm import tqdm\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)"]}, {"cell_type": "code", "execution_count": null, "id": "ae421508-7eff-496f-aceb-e69b51a226f5", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "8f085ce1-8344-4dae-97ef-e3bb31993bab", "metadata": {}, "outputs": [], "source": ["def final():\n", "    final = \"\"\"\n", "    \n", "   with\n", "run_id_details as\n", "    (select facility_id, run_id, date(completed_at + interval '330' minute) as run_date,\n", "        row_number() over(partition by facility_id, date(completed_at + interval '330' minute) order by (started_at + interval '330' minute)) as rnk\n", "            from ars.job_run\n", "                where started_at >= cast(current_date - interval '1' day as date)\n", "                    and json_extract_scalar(simulation_params, '$.run') = 'multi_business'\n", "                    -- and json_extract_path_text(simulation_params, 'type',true) = 'any_day_po'\n", "                    and success = 1\n", "    ),\n", "    final_indent as \n", "    (select fi.outlet_id,fi.item_id as pp,\n", "        physical_facility_id as facility_id, fi.facility_name, fi.item_id,cast(fi.trigger_doi_date as date) trigger_doi_date ,rid.run_date,error_type,error_details,\n", "        auto_po_eligible,current_inventory,open_po_quantity,final_indent_quantity,final_indent_quantity*landing_price as Indent_value,open_po_quantity*landing_price as Open_PO_value\n", "        ,be_cpd,fe_net_inventory,l7_sales_cpd\n", "            from  ars.final_indent fi join run_id_details rid on rid.run_id =  fi.run_id and fi.run_id in (select distinct run_id from run_id_details where rnk = 1)\n", "            left join(select distinct be_hot_outlet_id,item_id, sum(fe_net_inventory) as fe_net_inventory, Sum(be_cpd) as be_cpd,sum(l7_avg_sales_quantity) as l7_sales_cpd from supply_etls.packaged_goods_backend_facility_performance group by 1,2) sm on fi.item_id = sm.item_id and be_hot_outlet_id = outlet_id\n", "                \n", "                where l0_id in (13)\n", "                    \n", "                    -- group by 1,2,3,4,5,6,7,8,9,10,11\n", "    ),\n", "\n", "-- cpd as\n", "--     (select item_id, run_id, avg_cpd from ars.case_incorporation_result\n", "--         where (insert_ds_ist = cast(current_date  as varchar))\n", "--         and run_id in (select distinct run_id from run_id_details where rnk = 1)\n", "    -- ),\n", "\n", "final_view as\n", "    (select fi.run_date, fi.outlet_id, facility_id, facility_name, fi.item_id,fi.trigger_doi_date,error_type,error_details,pp,auto_po_eligible\n", "    ,current_inventory,open_po_quantity,final_indent_quantity,Indent_value,Open_PO_value,be_cpd,fe_net_inventory,l7_sales_cpd\n", "        \n", "            from final_indent fi\n", "            \n", "                -- join\n", "                --     cpd on cpd.item_id = fi.item_id and cpd.run_id = fi.run_id\n", "                    \n", "    )\n", "    select   REPLACE(REPLACE(REPLACE(REPLACE(facility_name, '- SR Feeder Warehouse', ''), 'Super Store ', ''), '- Feeder Warehouse', ''), '- Warehouse', '') AS facility_name, \n", "    Sum(case when auto_po_eligible = 1 then final_indent_quantity end) as Indent_qty,\n", "    Sum(case when auto_po_eligible = 1 then indent_value end) as Indent_value,\n", "    Sum(open_po_quantity) as Open_PO_qty,\n", "    Sum(open_po_Value) as Open_PO_value,\n", "    Sum(ma.current_inventory) as Be_inv,\n", "    Sum(ma.fe_net_inventory) as Fe_inv,\n", "    Sum(ma.l7_sales_cpd) as l7_sales_cpd,\n", "    Sum(ma.current_inventory)/Sum(ma.l7_sales_cpd)  as Be_inv_doi,\n", "    Sum(ma.fe_net_inventory)/Sum(ma.l7_sales_cpd) as Fe_inv_doi,\n", "    (Sum(case when auto_po_eligible = 1 then final_indent_quantity end) + Sum(open_po_quantity))/Sum(ma.l7_sales_cpd) as Open_raised_PO_doi\n", "\n", "    from final_view  ma\n", "    where run_date = Cast(current_date as date)\n", "    -- 2076,2078,1320\n", "    group by 1\n", "    -- order by 2 desc\n", "    union \n", "    select  'Grand Total' as Backend_name,\n", "    Sum(case when auto_po_eligible = 1 then final_indent_quantity end) as Indent_qty,\n", "    Sum(case when auto_po_eligible = 1 then indent_value end) as Indent_value,\n", "    Sum(open_po_quantity) as Open_PO_qty,\n", "    Sum(open_po_Value) as Open_PO_value,\n", "    Sum(ma.current_inventory) as Be_inv,\n", "    Sum(ma.fe_net_inventory) as Fe_inv,\n", "    Sum(ma.l7_sales_cpd) as l7_sales_cpd,\n", "    case when Sum(ma.l7_sales_cpd) < 1 then 0.00 else Sum(ma.current_inventory)/Sum(ma.l7_sales_cpd) end as Be_inv_doi,\n", "    case when Sum(ma.l7_sales_cpd) < 1 then 0.00 else Sum(ma.fe_net_inventory)/Sum(ma.l7_sales_cpd) end  as Fe_inv_doi,\n", "    case when Sum(ma.l7_sales_cpd) < 1 then 0.00 else (Sum(case when auto_po_eligible = 1 then final_indent_quantity end) + Sum(open_po_quantity))/Sum(ma.l7_sales_cpd)end as Open_raised_PO_doi\n", "\n", "    from final_view  ma\n", "    where run_date = Cast(current_date as date)\n", "    order by 2\n", "    \n", "    \"\"\"\n", "    return read_sql_query(final, trino)\n", "\n", "\n", "final_view = final()"]}, {"cell_type": "code", "execution_count": null, "id": "1de8ac1a-6040-4c80-a361-a30aa677eadd", "metadata": {}, "outputs": [], "source": ["final = final_view.fillna(0)\n", "# final_view = final_view.replace({'NaN': 'Infinity', 0.00: 0.00})\n", "final.head(22)\n", "# final.head(22)"]}, {"cell_type": "code", "execution_count": null, "id": "fff72578-b41e-412a-b5d2-51b6f422cc99", "metadata": {}, "outputs": [], "source": ["final[\"Be_inv_doi\"] = pd.to_numeric(final[\"Be_inv_doi\"], errors=\"coerce\")\n", "final[\"Fe_inv_doi\"] = pd.to_numeric(final[\"Fe_inv_doi\"], errors=\"coerce\")\n", "final[\"Open_raised_PO_doi\"] = pd.to_numeric(final[\"Open_raised_PO_doi\"], errors=\"coerce\")\n", "\n", "# Round specified columns to 2 decimal places\n", "final[[\"Fe_inv_doi\", \"Be_inv_doi\", \"Open_raised_PO_doi\"]] = final[\n", "    [\"Fe_inv_doi\", \"Be_inv_doi\", \"Open_raised_PO_doi\"]\n", "].round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "ead99154-aeaa-40aa-9404-8e9dbb342d7f", "metadata": {}, "outputs": [], "source": ["# final[\"Fe_inv_doi\"] = round(final[\"Fe_inv_doi\"], 2)\n", "# final[\"Be_inv_doi\"] = round(final[\"Be_inv_doi\"], 2)\n", "# final[\"Open_raised_PO_doi\"] = round(final[\"Open_raised_PO_doi\"], 2)"]}, {"cell_type": "code", "execution_count": null, "id": "2b913613-e28c-4364-95cc-b99e2fc651a2", "metadata": {}, "outputs": [], "source": ["from babel.numbers import format_currency\n", "\n", "# Define a function to format the po_quantity column as currency without decimal\n", "def without_decimal_format_po_quantity(quantity):\n", "    formatted = format_currency(quantity, \"INR\", locale=\"en_IN\").replace(u\"\\xa0\", u\" \")[1:]\n", "    formatted_without_decimal = formatted.split(\".\")[0]  # Remove decimal part\n", "    return formatted_without_decimal\n", "\n", "\n", "def format_po_quantity(quantity):\n", "    return format_currency(quantity, \"INR\", locale=\"en_IN\").replace(u\"\\xa0\", u\" \")[1:]\n", "\n", "\n", "final[\"Indent_qty\"] = final[\"Indent_qty\"].apply(without_decimal_format_po_quantity)\n", "final[\"Indent_value\"] = final[\"Indent_value\"].apply(without_decimal_format_po_quantity)\n", "final[\"Open_PO_qty\"] = final[\"Open_PO_qty\"].apply(without_decimal_format_po_quantity)\n", "final[\"Open_PO_value\"] = final[\"Open_PO_value\"].apply(without_decimal_format_po_quantity)\n", "\n", "final[\"Be_inv\"] = final[\"Be_inv\"].apply(without_decimal_format_po_quantity)\n", "final[\"Fe_inv\"] = final[\"Fe_inv\"].apply(without_decimal_format_po_quantity)\n", "final[\"l7_sales_cpd\"] = final[\"l7_sales_cpd\"].apply(without_decimal_format_po_quantity)\n", "# final[\"Fe_inv_doi\"] = final[\"Fe_inv_doi\"].apply(without_decimal_format_po_quantity)\n", "# final[\"Be_inv_doi\"] = final[\"Be_inv_doi\"].apply(without_decimal_format_po_quantity)\n", "# final[\"Open_raised_PO_doi\"] = final[\"Open_raised_PO_doi\"].apply(without_decimal_format_po_quantity)\n", "\n", "# final = final.rename(\n", "#     columns={\n", "#         \"facility_name\": \"Facility Name\",\n", "#         \"po_quantity\": \"Indent Quantity\",\n", "#         \"indent_value\": \" Indent Value\",\n", "#         \"open_po_quantity\": \"Open PO Quantity\",\n", "#         \"open_po_value\": \"Open PO Value\",\n", "#         \"net_inventory\": \"BE Inventory\",\n", "#         \"fe_net_inv\": \"FE Inventory\",\n", "#         \"avg_sales\": \"CPD\",\n", "#         \"fe_inv_doi\": \"FE DOI\",\n", "#         \"inventory_doi\": \"BE Inv DOI\",\n", "#         \"indent_and_open_po\": \"Open and Raised PO DOI\",\n", "#     }\n", "# )\n", "\n", "final"]}, {"cell_type": "code", "execution_count": null, "id": "0ae9f65f-42d5-43be-9ee5-4e2e17734ba5", "metadata": {}, "outputs": [], "source": ["slack_channel = \"bl-beauty_alerts\"\n", "#\n", "\n", "slack_channel = list(slack_channel.split(\",\"))\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "248928f2-30cb-4fe8-aa2e-e4b4e9afc62b", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.825,\n", "    font_size=22,\n", "    header_color=\"#93befa\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == data.shape[0]:  # La última fila\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "        elif k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "fig, ax = render_mpl_table(final, header_columns=0)\n", "fig.savefig(\"beauty_ordering_\" + datetime.now().strftime(\"%Y-%m-%d\") + \".png\")"]}, {"cell_type": "code", "execution_count": null, "id": "56ecd244-b71e-4da3-82a0-b5cf3787f831", "metadata": {}, "outputs": [], "source": ["for i in range(len(slack_channel)):\n", "    channel = slack_channel[i]\n", "    if final.shape[0] > 0:\n", "        text_req = (\n", "            f\" Auto PO Summary for \"\n", "            + datetime.now().strftime(\"%Y-%m-%d\")\n", "            # + \"\\n\"\n", "            # + f\"For details please refer this <https://docs.google.com/spreadsheets/d/1toscqGLjyhRqu99s_k1P-oJeWvg_Pd7cYz9iojdXl4I/edit#gid=0| sheet>\"\n", "            # + \"\\n\"\n", "        )\n", "        pb.send_slack_message(\n", "            channel=channel,\n", "            text=text_req,\n", "            files=[\n", "                # \"raw_data.csv\",\n", "                \"./beauty_ordering_\"\n", "                + datetime.now().strftime(\"%Y-%m-%d\")\n", "                + \".png\",\n", "            ],\n", "        )\n", "    else:\n", "        print(\"ARS Issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "8eaaf326-118d-449b-90e3-e7afac715355", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "61f6a098-8ad2-4b78-99fc-af2b13f927c5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f628e714-51c7-4819-8b9e-f1be77e56500", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "15d422f0-31ba-4a8a-ae26-ffa79fe013c2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5579f853-5077-4922-927e-6407053d5fca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07014fa3-8281-4e4f-9f60-bf0f74d88656", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "55786d7c-5842-4759-ae2b-59e1f80f511d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "454c1d02-f5e2-4797-a28b-1fe19d1e872f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b65311a5-2543-47dc-8548-12028a03ba35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "08b22a69-7069-49d1-a5f1-a9f30c494707", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "11bbe754-cb7f-42ff-b0f4-15a185d1a4ba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "116f7949-1b77-4e25-9103-d9eee909f8e0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d413626-d227-4bb9-9f1a-2d9ee8e6e595", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95c52dd6-51f6-4a94-951f-527a8ea8e5d4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c78969b1-e920-408f-a4bd-2ac1cfec5e26", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "05be495e-c18d-4911-8e9c-a85b708a8cc1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "49ef96da-d253-4016-b925-a16c8a1f505a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "131c98db-8d0f-4018-bcd6-cf234088ae75", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0744d6ab-7b11-4d6e-b2ab-81138c9af037", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf774f91-b8f5-4e95-b8db-b5f80d7b41cd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd9dd1da-4af9-47a0-b5d8-be8c4a388eb6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d13c5371-f68f-49dc-944a-2309e4b18756", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7de60529-b2f9-4392-adea-6278a9006a5d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1886b955-3581-48ac-a2ae-bb55f3e495c0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af762e90-0331-435e-a681-1a1c6bb8a5ed", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "48e69c41-5f95-4d07-b93a-8bb8ddbf0826", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e5257743-9396-4520-aca4-01b8cf8a90ee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a9eaf0d5-0c3e-47fa-9a56-beab209d4aa2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e378e232-0ab1-4ac1-a934-1fdf11a5f591", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e49f8105-45b0-4355-96dd-3680236659d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e3b21cdf-6670-468e-8eea-bb5914e685c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "69fc379f-28ca-4402-915f-dba00f5fbfaf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c77c6d84-3a5c-4bf2-8c58-c8cef68050f6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab26502e-6435-4634-887e-4fad13449810", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}