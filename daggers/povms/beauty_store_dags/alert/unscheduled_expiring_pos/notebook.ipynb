{"cells": [{"cell_type": "code", "execution_count": null, "id": "0fc7469e-1b2d-49d7-8855-7c6181928dac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d0b607f-818f-4f4a-b673-2f76b6fc229f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "62df6b9d-f91f-4e51-b805-0c509bd99aff", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1sXnYtESJhQU8rNvhEuVvXnsLMfurTJ043bw4rDaOwWc\"\n", "\n", "df_1 = pb.from_sheets(sheet_id, \"Unscheduled Summary\")\n", "df_2 = pb.from_sheets(sheet_id, \"Expirinng today\")"]}, {"cell_type": "code", "execution_count": null, "id": "d4388c46-3161-4daa-a176-49b0c9c36dc3", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.5,\n", "    row_height=1.2,\n", "    font_size=40,\n", "    header_color=\"#dfb1fe\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "14581093-ae4e-4b99-8e8a-ad3bc1560008", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"Unscheduled_POs.png\")\n", "file_check = \"./Unscheduled_POs.png\"\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "b195792a-e590-499b-b89f-f11c8f8cb95d", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_2,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"Today_expiring_POs.png\")\n", "file_check = \"./Today_expiring_POs.png\"\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "4fb1ce2c-b416-4e15-b8f6-e3b495698b77", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dbac4d79-e050-4475-a2bb-08889ed4f756", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b63d702a-d94f-4089-876e-1811f29d16ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "227f9c61-e04a-4673-87f4-ef2479295da3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ac7c68c0-5d31-4e3e-b335-9d3f87f82013", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9c9283e2-d7b0-46fc-9f1d-143e88b586be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9fda941b-7ef0-4760-8070-e9ca48fbfb42", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bab2695b-b697-40ec-a5f2-a9148770ee84", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}