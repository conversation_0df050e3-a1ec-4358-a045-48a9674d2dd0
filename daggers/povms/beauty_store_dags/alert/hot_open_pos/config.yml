alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: hot_open_pos
dag_type: alert
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03R91S1LKH
path: povms/beauty_store_dags/alert/hot_open_pos
paused: false
pool: povms_pool
project_name: beauty_store_dags
schedule:
  end_date: '2024-11-05T00:00:00'
  interval: 0 9 * * *
  start_date: '2024-05-21T08:08:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
