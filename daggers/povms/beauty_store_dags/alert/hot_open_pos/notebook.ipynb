{"cells": [{"cell_type": "code", "execution_count": null, "id": "cdd1ec51-e312-43f3-9f9a-c20a6a6a1c55", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f6a31205-da6f-476c-902b-aa243204ec9a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a31323aa-b15a-4208-af63-0d54c5623a1f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "67b85b86-059d-4840-82b1-bcde73b0db2c", "metadata": {}, "outputs": [], "source": ["sheet_id = \"16ajb8CtaVa8JHqvBKuflrJJ9VKGzB-rHxdR4JLgJEAk\"\n", "\n", "df_2 = pb.from_sheets(sheet_id, \"alert\")"]}, {"cell_type": "code", "execution_count": null, "id": "ff779f6c-7005-48d3-a4ed-e18dd90e7c9e", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=15.5,\n", "    row_height=1,\n", "    font_size=35,\n", "    header_color=\"#dfb1fe\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "6fa5f9d3-8848-4e42-8d67-6177fe4562ff", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_2,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"HOT_open_POs.png\")\n", "file_check = \"./HOT_open_POs.png\"\n", "text_req = (\n", "    f\"HOT_open_POs\"\n", "    # + datetime.now().strftime(\"%Y-%m-%d\")\n", "    # + \"\\n\"\n", "    + \"\\n\"\n", "    + f\"For mored details please refer this <https://docs.google.com/spreadsheets/d/16ajb8CtaVa8JHqvBKuflrJJ9VKGzB-rHxdR4JLgJEAk/edit#gid=0&fvid=605743497| sheet>\"\n", "    # + \"\\n\"\n", ")\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, text=text_req, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "a1808fbb-58a5-4d75-9504-26fe165e75a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b3c5ce53-4d90-4fab-a18c-fa4adaf89c62", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "093898e5-a395-4f0e-9683-fcaca6b22a49", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7eb91809-ba8b-4838-a04f-b5537123b8c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "932f1d7b-746a-40cc-af94-99c11f580d4b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16c7bd78-c1f6-49e7-a00a-5cef393ac8ce", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "754e2aaa-801e-491f-863b-525712fdf8f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1252efae-67c9-4750-8427-979d68a797bf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}