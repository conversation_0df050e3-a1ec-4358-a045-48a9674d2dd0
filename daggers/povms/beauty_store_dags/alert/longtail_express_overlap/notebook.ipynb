{"cells": [{"cell_type": "code", "execution_count": null, "id": "0b08ec06-41b6-4368-b074-5c03b852a8fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "df416616-2fb9-487f-ac5b-09072ca5ab58", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ba7d8f5f-db5b-4e8f-bb75-fb1a7bfbf7c2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "204f7e06-21a9-47e7-85c4-a3b241e2fb5b", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1aUKnOVrJNVO2Jz2my7JhJTWcOqBaBnLT09gsc4orCUY\"\n", "\n", "df_1 = pb.from_sheets(sheet_id, \"summary\")\n", "df_2 = pb.from_sheets(sheet_id, \"beauty_summary\")"]}, {"cell_type": "code", "execution_count": null, "id": "b727a297-8ac4-476f-85ec-feae1d3ca1b9", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=15.5,\n", "    row_height=1,\n", "    font_size=35,\n", "    header_color=\"#b1c6fe\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "c58b4706-6584-4f82-8774-40248cb81f7b", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"Longtail_express_overlap.png\")\n", "file_check = \"./Longtail_express_overlap.png\"\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "88d63349-a37f-417a-9c77-d162b34e8d72", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_2,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"longtal_express_overlap_beauty.png\")\n", "file_check = \"./longtal_express_overlap_beauty.png\"\n", "text_req = (\n", "    f\"longtal-express overlapping beauty skus\"\n", "    # + datetime.now().strftime(\"%Y-%m-%d\")\n", "    # + \"\\n\"\n", "    + \"\\n\"\n", "    + f\"For mored details please refer this <https://docs.google.com/spreadsheets/d/1aUKnOVrJNVO2Jz2my7JhJTWcOqBaBnLT09gsc4orCUY/edit#gid=1105263155| sheet>\"\n", "    # + \"\\n\"\n", ")\n", "filepath = file_check\n", "channel = \"bl-beauty_alerts\"\n", "pb.send_slack_message(channel=channel, text=text_req, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "7b093d0d-9e64-4245-90b5-7ff26b9f58af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "38b8a836-3dac-4b9a-a86f-50a3739603cd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "82db1c34-ceb6-4882-8350-25ad1f93e05e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ae49dc3-40f0-4574-b75b-4541809aa2a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7abf2774-a86f-40f5-aa0b-affbf214f8c3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4c9fd8bd-ef99-49cd-8d19-1db74891f7ab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2dbfd6b6-f8b6-41e2-8f75-6f49846fa648", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "713ae69c-36d5-4135-ab13-a85230593a3b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}