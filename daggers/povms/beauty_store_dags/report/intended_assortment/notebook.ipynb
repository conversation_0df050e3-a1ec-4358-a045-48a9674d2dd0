{"cells": [{"cell_type": "code", "execution_count": null, "id": "cd2f86c6-9b75-46d3-8b39-d7b833f8f438", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "from datetime import datetime, timedelta, date\n", "import gc\n", "\n", "# !pip install pandasql\n", "# import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "id": "39803f77-cb3c-4e44-ba4f-68cfcb22bda3", "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "895b4c11-c7f8-4c80-b759-98b3d570020b", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "64eea2ae-ea65-490a-b4c6-5e82bf4ce056", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib==3.8"]}, {"cell_type": "code", "execution_count": null, "id": "ca94d9fc-073b-46cb-9260-116852d13af4", "metadata": {}, "outputs": [], "source": ["# df = pd.read_csv('Intended.csv')\n", "df = pb.from_sheets(\n", "    \"1yG93UfV_rc6X83i87Vy6tV5sumkdYDfZfWtYx6i6UvY\",\n", "    \"input_sheet\",\n", "    clear_cache=True,\n", ")\n", "df = df[[\"item_id\", \"city\", \"assortment_type\", \"remarks\", \"poc\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "6734578c-574a-4099-a84a-6f76e011b2d2", "metadata": {}, "outputs": [], "source": ["df[\"updated_at\"] = str((datetime.now() + timedelta(hours=5.5)).date())"]}, {"cell_type": "code", "execution_count": null, "id": "3efe29d4-ba5b-4a7d-bd8e-e74f7b57805d", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "48e680fd-010d-4019-8876-95db59dc71e6", "metadata": {}, "outputs": [], "source": ["df[\"item_id\"] = df[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "ff5cc0e2-9fc6-4702-a694-51ed829747fa", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"dim_product_key\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"city\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"assortment_type\"},\n", "    {\"name\": \"remarks\", \"type\": \"varchar\", \"description\": \"remarks\"},\n", "    {\"name\": \"poc\", \"type\": \"varchar\", \"description\": \"User_name\"},\n", "    {\"name\": \"updated_at\", \"type\": \"varchar\", \"description\": \"updated_at\"},\n", "]\n", "\n", "asd = df.head(0)\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"beauty_intended_assortment_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"city\"],\n", "    # \"partition_key\": [\"Assortment_type\"],\n", "    # \"incremental_key\": \"City\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"beauty_intended_assortment\",\n", "}\n", "\n", "pb.to_trino(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "168a2b84-0a97-4fa9-82e8-61fdeeb19680", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}