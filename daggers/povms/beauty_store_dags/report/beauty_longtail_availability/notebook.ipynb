{"cells": [{"cell_type": "code", "execution_count": null, "id": "ba080523-8d48-4389-842a-15e1c0f2b861", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d133fc5-23f9-47d9-bf12-e55c6a8fe20b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d3705bb-31f6-4157-a7a5-349d7e507a10", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "5aa35b4f-e33b-42e9-92ea-1dd4951d27a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "121407df-a47b-4d55-83ec-715c283baca2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1gAT4XXjAcWzcpuvECKSzsYh4HEE_iDkQR3mLaX8YjCA\"\n", "\n", "df_1 = pb.from_sheets(sheet_id, \"FE Summary longtail\")"]}, {"cell_type": "code", "execution_count": null, "id": "8e8652de-38f3-43f4-bf8d-59909ed27e62", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "d0fc7f91-3048-4bad-b970-8878b7e0cebb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f5ab80de-0127-4e71-b798-6023dd2b89b4", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.8,\n", "    row_height=1.3,\n", "    font_size=40,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[-0.15, -0.13, 1.27, 1.2],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"left\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "89eaee3c-9e27-4009-946c-16fc1cd9e8b5", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    df_1,\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"beauty_longtail_availability.png\")\n", "file_check = \"./beauty_longtail_availability.png\"\n", "filepath = file_check\n", "channel = \"beauty-internal\"\n", "pb.send_slack_message(channel=channel, files=[filepath])"]}, {"cell_type": "code", "execution_count": null, "id": "e618b300-0a1b-4e92-9550-4c4a12726b23", "metadata": {}, "outputs": [], "source": ["# beauty_longtail_availability=\"/beauty_longtail_availability.png\""]}, {"cell_type": "code", "execution_count": null, "id": "a99aa249-56b7-408d-8a6f-d5b15018e6fc", "metadata": {}, "outputs": [], "source": ["# fig, ax = render_mpl_table(df_1)\n", "# fig.savefig(beauty_longtail_availability)"]}, {"cell_type": "code", "execution_count": null, "id": "b75ef487-9ee1-4f28-9a50-73772326dddd", "metadata": {}, "outputs": [], "source": ["# pb.send_slack_message(\n", "#     channel=\"beauty-internal\",\n", "#     text=\"Beauty_longtail_availability\",\n", "#     files=[\"/beauty_longtail_availability.png\"],\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "2e2247e9-2f24-43f3-8517-237e060e696d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}