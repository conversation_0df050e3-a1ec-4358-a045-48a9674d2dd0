alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: new_master
dag_type: workflow
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 2
      request: 1
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 8G
      request: 1G
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U03R91S1LKH
path: povms/beauty_store_dags/workflow/new_master
paused: true
project_name: beauty_store_dags
schedule:
  end_date: '2023-06-06T00:00:00'
  interval: 0 */6 * * *
  start_date: '2023-04-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
pool: povms_pool
