{"cells": [{"cell_type": "code", "execution_count": null, "id": "8ce9803a-fe5b-4f3d-8250-cd87d0cfc162", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "1de14b8b-a95c-41f3-b6b0-72ec5ffb2c32", "metadata": {}, "outputs": [], "source": ["def sales():\n", "    sales = f\"\"\"\n", "   with\n", "\n", "sales as\n", "    (select\n", "        (oi.install_ts + interval '5.5 Hours') as order_date,\n", "        city_name,\n", "        od.facility_id,\n", "        pl.item_id,\n", "        oi.selling_price,\n", "        (oi.selling_price*1.0000/pl.multiplier+0.000) as item_selling_price,\n", "        oi.order_id,\n", "        od.customer_id,\n", "        pl.multiplier,\n", "        ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) as sales_quantity,\n", "        ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) * (oi.selling_price*1.0000/pl.multiplier) as sales_value\n", "\n", "            from lake_oms_bifrost.oms_order_item oi\n", "\n", "                left join\n", "                    (select distinct ipr.product_id,\n", "                        case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "                        case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else COALESCE(ipom_0.multiplier,1) end as multiplier\n", "                            from lake_rpc.item_product_mapping ipr\n", "\n", "                                left join\n", "                                    dwh.dim_item_product_offer_mapping ipom on ipom.product_id = ipr.product_id\n", "                                        and ipr.item_id = ipom.item_id\n", "                                left join\n", "                                    dwh.dim_item_product_offer_mapping ipom_0 on ipom_0.product_id = ipr.product_id\n", "                    ) pl on pl.product_id = oi.product_id\n", "                \n", "                join\n", "                    lake_oms_bifrost.oms_order oo on oo.id = oi.order_id and oo.type in ('RetailForwardOrder','RetailSuborder','')\n", "                        and oo.install_ts > current_date-31\n", "\n", "                join \n", "                    (select distinct facility_id, cast(ancestor as bigint) as order_id, customer_id, cl.name as city_name\n", "                        from lake_ims.ims_order_details od\n", "\n", "                            join\n", "                                lake_retail.console_outlet rco on rco.id = outlet\n", "                            left join\n", "                                lake_retail.console_location cl on cl.id = rco.tax_location_id\n", "                                    where status_id not in (3,4,5)\n", "                                        and od.created_at > current_date-31\n", "                    ) od on od.order_id = oi.order_id\n", "\n", "                        where\n", "                            oi.install_ts  > current_date-31\n", "    ),\n", "\n", "final as\n", "    (select city_name, date(order_date) as date_, order_id, facility_id, customer_id,\n", "        s.item_id, sales_quantity, sales_value\n", "            from sales s\n", "                \n", "\n", "                    where sales_quantity > 0 and s.item_id is not null\n", "    )\n", "    \n", "        select distinct\n", "            city_name,\n", "            facility_id,\n", "            cf.name as facility_name,\n", "            item_id,\n", "            \n", "            count (distinct case when date_ = (current_date-1) then customer_id end) as T_1_customer_count,\n", "             count (distinct case when date_ =  (current_date-1) then order_id end) as T_1_carts,\n", "             Sum (distinct case when date_ =  (current_date-1) then sales_quantity end) as T_1_sales_quantity,\n", "             Sum (distinct case when date_ =  (current_date-1) then sales_value end) as T_1_sales_value,\n", "             count (distinct case when date_ = (current_date-8) then customer_id end) as T_8_customer_count,\n", "             count (distinct case when date_ =  (current_date-8) then order_id end) as T_8_carts,\n", "             Sum (distinct case when date_ =  (current_date-8) then sales_quantity end) as T_8_sales_quantity,\n", "             Sum (distinct case when date_ =  (current_date-8) then sales_value end) as T_8_sales_value,\n", "             \n", "             count (distinct case when date_ between current_date-7  and current_date then customer_id end) as L7_customer_count,\n", "             count (distinct case when date_ between current_date-7  and current_date then order_id end) as L7_carts,\n", "             Sum (distinct case when date_ between current_date-7  and current_date then sales_quantity end) as L7_sales_quantity,\n", "             Sum (distinct case when date_ between current_date-7  and current_date then sales_value end) as L7_sales_value,\n", "            count(distinct customer_id) as L31_customer_count,\n", "            count(distinct order_id) as L31_carts,\n", "            sum(sales_quantity) as L31_sales_quantity,\n", "            sum(sales_value) as L31_sales_value\n", "            \n", "                from final f\n", "                \n", "                join\n", "                    lake_crates.facility cf on cf.id = f.facility_id\n", "                    \n", "                \n", "\n", "                    group by 1,2,3,4\n", "    \"\"\"\n", "    return pd.read_sql_query(sales, redshift)\n", "\n", "\n", "sales = sales()"]}, {"cell_type": "code", "execution_count": null, "id": "ba13837f-ff58-4eec-9fa5-a365de525b90", "metadata": {}, "outputs": [], "source": ["def pi_logic():\n", "    pi_logic = f\"\"\"\n", "\n", "select distinct va.item_id,case when ig.outlet_id is null then '0' else ig.outlet_id end as backend_outlet_id , va.facility_id as be_facility_id, va.Vendor_id, vendor_name, case_sensitivity_type,load_type,load_size,tat_days,on_invoice_margin_value, op.remaining_quantity as open_po\n", ",(iii.quantity -iibi.quantity) as be_inv,pen_put\n", "    from lake_vms.vms_vendor_facility_alignment va \n", "    left join lake_vms.vms_vendor vd on vd.id = va.vendor_id\n", "    Left join (Select item_id,facility_id,case_sensitivity_type,vendor_id, active from lake_vms.vendor_item_physical_facility_attributes) cs on va.item_id = cs.item_id and cs.vendor_id = va.vendor_id and va.facility_id = cs.facility_id and cs.active = 1\n", "    left join lake_vms.vendor_physical_facility_attributes vs on  va.facility_id = vs.facility_id and va.vendor_id = vs.vendor_id\n", "    left join lake_po.physical_facility kk on va.facility_id = kk.facility_id\n", "    left join lake_po.physical_facility_outlet_mapping ig on va.facility_id = ig.facility_id\n", "    left join lake_rpc.tot_margin tot on va.facility_id = tot.facility_id and va.item_id = tot.item_id\n", "    left join (select item_id,outlet_id,vendor_id,tat_days,active from lake_po.item_outlet_vendor_tat) tt on va.item_id = tt.item_id and va.vendor_id = tt.vendor_id  and ig.outlet_id = tt.outlet_id and tt.active =1\n", "    left join (select p.outlet_id,poi.item_id,sum (remaining_quantity) as remaining_quantity\n", "       from  lake_po.purchase_order_items poi\n", "     inner join lake_po.purchase_order p on p.id=poi.po_id\n", "     Left Join lake_po.po_schedule ps on p.id = ps.po_id_id\n", "     inner join lake_po.purchase_order_status posa on posa.po_id = p.id\n", "     inner join lake_po.purchase_order_state posta on posta.id = posa.po_state_id\n", "     Where  (posta.id in (2,3,4,10,13,14,15) or p.is_multiple_grn = 1)\n", "     and posta.id not in (4,5,8,10) and p.active = 1\n", "      group by 1,2) op on op.outlet_id =  ig.outlet_id and op.item_id = va.item_id\n", "     left join lake_retail.warehouse_outlet_mapping ot on ot.warehouse_id =  ig.outlet_id\n", "     left join lake_ims.ims_item_inventory iii ON iii.item_id = va.item_id AND iii.outlet_id = ot.cloud_store_id and iii.active =1\n", "     LEFT JOIN lake_ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id and iibi.active =1\n", "     left join (Select outlet_id,rpc.item_id,\n", "         sum(quantity) as pen_put from lake_ims.ims_good_inventory igi\n", "        left join lake_rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "        where igi.active = 1 and igi.inventory_update_type_id in (28,76)group by 1,2)hg on hg.item_id = va.item_id and hg.outlet_id =ot.cloud_store_id\n", "      \n", "     \n", "            \"\"\"\n", "    return pd.read_sql_query(pi_logic, redshift)\n", "\n", "\n", "pi_logic = pi_logic()"]}, {"cell_type": "code", "execution_count": null, "id": "1087d4dc-fe93-42a2-a05b-dabc8a5d0d98", "metadata": {}, "outputs": [], "source": ["pi_logic[\"backend_outlet_id\"] = pi_logic[\"backend_outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "a692f194-e5c5-40d9-9c7b-1efb359bc12b", "metadata": {}, "outputs": [], "source": ["pi_logic.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8efc998c-4a1e-44b5-9d41-e7425ce1821d", "metadata": {}, "outputs": [], "source": ["def item():\n", "    item = f\"\"\"\n", "\n", "select distinct\n", "(ipm.product_id) as pid,\n", "id.item_id,\n", "concat(id.name,' ',id.variant_description) as item_name,\n", "pb.name as brand_name,\n", "pb.manufacturer_id,\n", "pm.name as manufacturer_name,\n", "id.variant_mrp,\n", "(id.weight_in_gm) as uom,\n", "cd.l0,\n", "cd.l1,\n", "cd.l2,\n", "pt.name as p_type,\n", "id.shelf_life,\n", "id.inner_case_size,\n", "id.outer_case_size\n", "\n", "\n", "from rpc.product_product id\n", "\n", "left join (select item_id, l0, l1, l2 from rpc.item_category_details) cd on cd.item_id = id.item_id\n", "\n", "left join (select id, name, manufacturer_id from rpc.product_brand) pb on pb.id = id.brand_id\n", "left join (select id, name from rpc.product_manufacturer) pm on pm.id = pb.manufacturer_id\n", "\n", "left join (select item_id, product_id from rpc.item_product_mapping) ipm on ipm.item_id = id.item_id\n", "left join (select id, type_id from lake_cms.gr_product) gp on gp.id = ipm.product_id\n", "left join (select id, name from lake_cms.gr_product_type) pt on pt.id = gp.type_id\n", "\n", "where id.id in (select max(id) as id from rpc.product_product pp where pp.active = 1 and pp.approved = 1 group by item_id)\n", "and cd.l0 in ('Beauty & Cosmetics')\n", "            \"\"\"\n", "    return pd.read_sql_query(item, trino)\n", "\n", "\n", "item = item()"]}, {"cell_type": "code", "execution_count": null, "id": "41b4d50e-c544-4a1a-8317-bd4068b8958d", "metadata": {}, "outputs": [], "source": ["item.head(6)"]}, {"cell_type": "code", "execution_count": null, "id": "ca43130e-bf48-453b-989a-00576b118d27", "metadata": {}, "outputs": [], "source": ["def item_facility():\n", "    item_facility = f\"\"\"\n", "select distinct ag.item_id,o.facility_id,o.name as facility,o.location as city,o.id as outlet_id,(iii.quantity -iibi.quantity) as fe_inv,case when ag.master_assortment_substate_id = 1 then 'active' when ag.master_assortment_substate_id = 2 then 'inactive' when ag.master_assortment_substate_id = 3 then 'temp-inactive' end as fe_status,sto.reserved_quantity as open_sto,min_quantity,max_quantity,case when tag_value is null then '6' else tag_value end as backend_outlet_id\n", "    FROM lake_rpc.product_facility_master_assortment ag  \n", "     LEFT JOIN lake_retail.console_outlet o ON ag.facility_id = o.facility_id\n", "     left join lake_ims.ims_item_inventory iii ON iii.item_id = ag.item_id AND iii.outlet_id = o.id and iii.active =1\n", "     LEFT JOIN lake_ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id and iibi.active =1\n", "     left join (select facility_id,item_id,min_quantity,max_quantity from lake_ars.item_min_max_quantity) mmx on mmx.item_id = ag.item_id and ag.facility_id = mmx.facility_id\n", "left join (select item_id,receiving_outlet_id, reserved_quantity from metrics.esto_details where  date(grn_started_at) is NULL and date(sto_invoice_created_at)>current_date - 10 and sto_state in ('Created','Billed','Partial-Billed')) sto on sto.receiving_outlet_id = o.id and ag.item_id = sto.item_id\n", "left join  lake_rpc.item_outlet_tag_mapping fd on fd.outlet_id = o.id and fd.item_id = ag.item_id and fd.active =1 and fd.tag_type_id =8\n", "left join (select item_id, l0, l1, l2 from lake_rpc.item_category_details) cd on cd.item_id = ag.item_id\n", "where ag.active = 1 and cd.l0 in ('Beauty & Cosmetics') and ag.master_assortment_substate_id = 1\n", "            \"\"\"\n", "    return pd.read_sql_query(item_facility, redshift)\n", "\n", "\n", "item_facility = item_facility()"]}, {"cell_type": "code", "execution_count": null, "id": "5f34af35-b851-4e36-9a6b-e3ac7c6047cb", "metadata": {}, "outputs": [], "source": ["item_facility.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1f3ed02d-1bc6-4cb8-b2d5-808ec9256a46", "metadata": {}, "outputs": [], "source": ["def item_imp():\n", "    item_imp = f\"\"\"\n", "       select * from (With imp as(\n", "select \n", "    sp.at_date_ist,\n", "    coalesce(sp.properties__child_widget_id,sp.properties__widget_id, sp.properties__product_id) as pid,\n", "    -- properties__widget_title as  pname,\n", "    mm.facility_id,\n", "    mm.pos_outlet_id,\n", "    count(distinct sp.device_uuid) as unique_users,\n", "    count(sp.at_ist) as impressions\n", "from \n", "    spectrum.mobile_impression_data sp\n", "join\n", "    dwh.dim_merchant_outlet_facility_mapping mm\n", "  on mm.frontend_merchant_id = sp.traits__merchant_id\n", "where \n", "    sp.at_date_ist >= current_date -31\n", "    and sp.properties__l0_category in ('Beauty & Cosmetics')\n", "    and sp.traits__user_id is not NULL\n", "    and sp.traits__user_id not in (-1,0)\n", "    and sp.traits__city_id is not NULL\n", "\tand sp.traits__merchant_id is not NULL\n", "\t--and sp.traits__merchant_name is not NULL\n", "\tand sp.name in ('Product Shown')\n", "\n", "group by 1,2,3,4)\n", "\n", "Select \n", "    pid,\n", "    product_name,\n", "    facility_id,\n", "    pos_outlet_id,\n", "    sum (case when at_date_ist = current_date-1 then unique_users end) as unique_users_ld,\n", "    sum (case when at_date_ist = current_date-1 then impressions end) as impression_ld,\n", "    sum (case when at_date_ist between current_date-7 and current_date then unique_users end) as unique_users_7,\n", "    sum (case when at_date_ist between current_date-7 and current_date then impressions end) as impression_7,\n", "    sum (case when at_date_ist between current_date-31 and current_date then unique_users end) as unique_users_31,\n", "    sum (case when at_date_ist between current_date-31 and current_date then impressions end) as impression_31\n", " from imp i\n", " join dwh.dim_product dp \n", "    on dp.product_id=i.pid\n", "    and is_current=1 \n", "    and is_product_enabled=1 \n", "group by 1,2,3,4)imp \n", " left join (With imp as(\n", "select \n", "    sp.at_date_ist,\n", "    coalesce(sp.properties__child_widget_id,sp.properties__widget_id, sp.properties__product_id) as pid,\n", "    -- properties__widget_title as  pname,\n", "    longtail_facility_id,\n", "    longtail_outlet_id,\n", "    count(distinct sp.device_uuid) as unique_users,\n", "    count(sp.at_ist) as impressions\n", "from \n", "    spectrum.mobile_impression_data sp\n", "join\n", "    consumer.longtail_express_merchant_mapping mm\n", "  on mm.express_frontend_merchant_id = sp.traits__merchant_id\n", "where \n", "    sp.at_date_ist >= current_date -31\n", "    and sp.properties__l0_category in ('Beauty & Cosmetics')\n", "    and sp.traits__user_id is not NULL\n", "    and sp.traits__user_id not in (-1,0)\n", "    and sp.traits__city_id is not NULL\n", "\tand sp.traits__merchant_id is not NULL\n", "\t--and sp.traits__merchant_name is not NULL\n", "\tand sp.name in ('Product Shown')\n", "\n", "group by 1,2,3,4)\n", "\n", "Select \n", "    pid as pidl,\n", "    product_name,\n", "    longtail_facility_id,\n", "    case when longtail_outlet_id= 3670 then '2340' else longtail_outlet_id end as longtail_outlet_id,\n", "    sum (case when at_date_ist = current_date-1 then unique_users end) as ltunique_users_ld,\n", "    sum (case when at_date_ist = current_date-1 then impressions end) as ltimpression_ld,\n", "    sum (case when at_date_ist between current_date-7 and current_date then unique_users end) as ltunique_users_7,\n", "    sum (case when at_date_ist between current_date-7 and current_date then impressions end) as ltimpression_7,\n", "    sum (case when at_date_ist between current_date-31 and current_date then unique_users end) as ltunique_users_31,\n", "    sum (case when at_date_ist between current_date-31 and current_date then impressions end) as ltimpression_31\n", " from imp i\n", " join dwh.dim_product dp \n", "    on dp.product_id=i.pid\n", "    and is_current=1 \n", "    and is_product_enabled=1 \n", "group by 1,2,3,4) tt on imp.pid = tt.pidl and tt.longtail_facility_id =imp.facility_id\n", "            \"\"\"\n", "    return pd.read_sql_query(item_imp, redshift)\n", "\n", "\n", "item_imp = item_imp()"]}, {"cell_type": "code", "execution_count": null, "id": "bbfc36ba-2267-4788-a3fe-0f9b4d128626", "metadata": {}, "outputs": [], "source": ["item_imp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0785159b-bb2b-406b-8ff0-60b62bb5667f", "metadata": {}, "outputs": [], "source": ["item_facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "aeb9272d-4464-4269-8ecd-3c2b842db92c", "metadata": {}, "outputs": [], "source": ["pi_logic.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b0995e35-f6da-4734-b2fa-14333e0540fd", "metadata": {}, "outputs": [], "source": ["item_facility[\"backend_outlet_id\"] = item_facility[\"backend_outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "e02b5e0b-e053-44f8-a536-19d9722efe18", "metadata": {}, "outputs": [], "source": ["newo = pd.merge(item_facility, pi_logic, on=[\"item_id\", \"backend_outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4225f938-0059-4534-8cc3-8c9400f40539", "metadata": {}, "outputs": [], "source": ["newst = pd.merge(newo, item, on=\"item_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "c22424a0-b730-4910-b12b-eb9c5ee88fe1", "metadata": {}, "outputs": [], "source": ["newrt = pd.merge(newst, sales, on=[\"item_id\", \"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "648f0061-db83-44b7-84eb-706a49256d41", "metadata": {}, "outputs": [], "source": ["newo.head()"]}, {"cell_type": "code", "execution_count": null, "id": "448a07fb-3474-411e-9911-98bdb81b81ca", "metadata": {}, "outputs": [], "source": ["newrt[\"pid\"] = newrt[\"pid\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "0ad066ad-c9e7-4a57-8f2e-2086f223e70d", "metadata": {}, "outputs": [], "source": ["item_imp[\"pid\"] = item_imp[\"pid\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "0520f24a-41fb-43c5-ab5d-ec91ee9d4518", "metadata": {}, "outputs": [], "source": ["newnew = pd.merge(newrt, item_imp, on=[\"pid\", \"facility_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "3cde648b-ebc9-457d-8a9f-66faeea82a99", "metadata": {}, "outputs": [], "source": ["newnew.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c73b8732-a654-4978-be7f-66c888317acc", "metadata": {}, "outputs": [], "source": ["newnew = newnew[\n", "    [\n", "        \"item_id\",\n", "        \"pid\",\n", "        \"item_name\",\n", "        \"brand_name\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"variant_mrp\",\n", "        \"uom\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"p_type\",\n", "        \"shelf_life\",\n", "        \"inner_case_size\",\n", "        \"outer_case_size\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility\",\n", "        \"outlet_id\",\n", "        \"facility_name\",\n", "        \"fe_inv\",\n", "        \"fe_status\",\n", "        \"open_sto\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"backend_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"case_sensitivity_type\",\n", "        \"load_type\",\n", "        \"load_size\",\n", "        \"tat_days\",\n", "        \"on_invoice_margin_value\",\n", "        \"open_po\",\n", "        \"be_inv\",\n", "        \"pen_put\",\n", "        \"t_1_customer_count\",\n", "        \"t_1_carts\",\n", "        \"t_1_sales_quantity\",\n", "        \"t_1_sales_value\",\n", "        \"t_8_customer_count\",\n", "        \"t_8_carts\",\n", "        \"t_8_sales_quantity\",\n", "        \"t_8_sales_value\",\n", "        \"l7_customer_count\",\n", "        \"l7_carts\",\n", "        \"l7_sales_quantity\",\n", "        \"l7_sales_value\",\n", "        \"l31_customer_count\",\n", "        \"l31_carts\",\n", "        \"l31_sales_quantity\",\n", "        \"l31_sales_value\",\n", "        \"product_name\",\n", "        \"pos_outlet_id\",\n", "        \"unique_users_ld\",\n", "        \"impression_ld\",\n", "        \"unique_users_7\",\n", "        \"impression_7\",\n", "        \"unique_users_31\",\n", "        \"impression_31\",\n", "        \"product_name\",\n", "        \"longtail_facility_id\",\n", "        \"ltunique_users_ld\",\n", "        \"ltimpression_ld\",\n", "        \"ltunique_users_7\",\n", "        \"ltimpression_7\",\n", "        \"ltunique_users_31\",\n", "        \"ltimpression_31\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1570d094-ff53-4d50-9437-5ac8631e83b6", "metadata": {}, "outputs": [], "source": ["newnew.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6e4d2040-589e-4250-8657-6c93a3fe4fb1", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(newnew, \"1TMm9jkJHzfP0Tfls5y7DGZMGqeWG2R9DYT3HJnPD1AI\", \"data\")"]}, {"cell_type": "code", "execution_count": null, "id": "e208ae3f-b610-4aef-bdfc-aea019a0373b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "91aaecc9-5111-4ea7-bcec-a3b9db5c5132", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9f906f37-103c-411d-abc5-488ba46a50ed", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}