{"cells": [{"cell_type": "code", "execution_count": null, "id": "fc25acb8-3e2c-44e0-b7ce-b1f0f8c37ece", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "74a6ed98-f264-4b4f-8330-84452b7dbecb", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\n", "    \"197uvM4Vcsv8Ov6TEk6VjVSI0OmDkC_RCJrwC_aYd39o\", \"Input\", clear_cache=True\n", ")\n", "\n", "df = df.rename(\n", "    columns={\n", "        \"Item _id\": \"item_id\",\n", "        \"City_id\": \"city_id\",\n", "        \"City\": \"city_name\",\n", "    }\n", ")\n", "\n", "df = df[~(((df[\"item_id\"] == \"\") | (df[\"city\"] == \"\") | df[\"city_name\"] == \"\"))]\n", "\n", "df[\"city_name\"] = df[\"city_name\"].astype(object)\n", "df[[\"item_id\", \"city_id\"]] = df[[\"item_id\", \"city_id\"]].fillna(0).astype(float)\n", "\n", "df = df[df[\"item_id\"] >= 0]\n", "df = df[df[\"city_id\"] >= 0]\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "674f8603-75a3-437e-96b3-2d360d9adf95", "metadata": {}, "outputs": [], "source": ["df[\"city_name\"] = df[\"city_name\"].astype(object)\n", "df[[\"item_id\", \"city_id\"]] = df[[\"item_id\", \"city_id\"]].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "877e5517-a534-49c0-8966-dc4753937461", "metadata": {}, "outputs": [], "source": ["df[\"updated_at_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "11ae5bbc-6966-48b9-a7ac-92eb81cd650a", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"unique item id\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"city id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city name\"},\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"updated at timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "49c12b50-f12a-463c-8c61-217d8e1c0108", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"overlapping_sku\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\"],\n", "    \"sortkey\": [\"item_id\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"overlapping_sku\",\n", "}\n", "\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "c41fab2f-21e2-4f28-a357-5a0cb51e3b7e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}