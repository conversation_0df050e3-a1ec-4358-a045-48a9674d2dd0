{"cells": [{"cell_type": "code", "execution_count": null, "id": "1e9e223a-a18f-4cdc-acff-9a3b8712736a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from calendar import monthrange\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "c591a045-2185-44db-b2ff-e27fbcb811b0", "metadata": {}, "outputs": [], "source": ["# item_id = pb.from_sheets(\"1ilsulT9ZDH5Zcc9Vge3t4o7qCgGvZvODxQXupdi3rX4\", \"item\")"]}, {"cell_type": "code", "execution_count": null, "id": "8d421fe8-75bd-42e5-b0dd-0e1e51cfbe8f", "metadata": {}, "outputs": [], "source": ["# item_id_list = tuple(list(item_id[\"item_id\"].unique().astype(int)))"]}, {"cell_type": "code", "execution_count": null, "id": "178d0bc3-bca2-4bbc-a2a2-bd099e83698e", "metadata": {}, "outputs": [], "source": ["def Beauty():\n", "    Beauty = f\"\"\"\n", "    \n", "With outlets as (select om.outlet_id as hot_outlet_id, om.outlet_name, \n", "        om.facility_id, pf.internal_facility_identifier as facility_name,\n", "        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id\n", "        -- om.active,\n", "        -- om.ars_active,\n", "        -- rco.business_type_id\n", "        \n", "        from lake_po.physical_facility_outlet_mapping om\n", "        \n", "        left join (select id, tax_location_id,\n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                from lake_retail.console_outlet) rco on rco.id = om.outlet_id\n", "        \n", "        left join (select pf.* from lake_po.physical_facility pf\n", "            join (select facility_id, max(updated_at) as updated_at from lake_po.physical_facility group by 1)\n", "            pf2 on pf2.facility_id = pf.facility_id and pf2.updated_at = pf.updated_at)\n", "            pf on pf.facility_id = om.facility_id\n", "        \n", "        left join (select distinct warehouse_id, cloud_store_id from lake_retail.warehouse_outlet_mapping \n", "        where active = 1) wom on wom.warehouse_id = om.outlet_id\n", "        \n", "        left join lake_retail.console_location rcl on rcl.id = rco.tax_location_id\n", "        \n", "        where rco.business_type_id not in (8)\n", "        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "        and om.outlet_name not like '%%SSC%%'\n", "        and om.outlet_name not like '%%MODI%%'\n", "        and om.outlet_name not like '%%hot ff%%'\n", "        )\n", "        \n", "        \n", "Select Distinct df.facility_id,o.outlet_name as facility_name,df.item_id,net_inv_qty_fr,esto_in_transit_fr,esto_need_to_pick_fr,sender_facility_id,s.facility_name as Backend,esto_in_transit_bk,esto_need_to_pick_bk,net_inv_qtybk,open_po_qty_bk,\n", "min_qty_fr,max_qty_fr,item_name,df.manufacturer,df.perishable,last30_chk_qty_ds,va.vendor_id,vendor_name_bk,\n", "-- case_sensitivity_type,load_type,load_size,po_cycle,PO_days,\n", "on_invoice_margin_value,pc.variant_mrp, (on_invoice_margin_value*pc.variant_mrp)/100  as landing_price,\n", "-- case when case_sensitivity_type = 2 then outer_case_size when case_sensitivity_type =1 then inner_case_size else '1' end as case_size,\n", "-- facility_fr,sender_facility,\n", "pending_putaway_qty_fr,\n", "oh_ind,in_transit_ind,sto_ind,get_required_min,item_store_demand,backend_demand,backend_oh_ind,l0,l1,l2,ptype,ipm.product_id,pb.name as brand,bucket_a_b,bucket_x,pending_putaway_qty_bk,bkend_asst_status,backend_on_order_ind,\n", "case when substate_reason_id =1 then 'longtail' else '10_minutes' end as item_type,\n", "df.updated_at,t_1_customer_count,t_1_carts,t_1_sales_quantity,t_1_sales_value,unique_users_ld,impression_ld,l7_customer_count,l7_carts,l7_sales_quantity,l7_sales_value,unique_users_7,impression_7,l31_customer_count,l31_carts,l31_sales_quantity,l31_sales_value,unique_users_31,impression_31,\n", "ltunique_users_ld,ltimpression_ld,ltunique_users_7,ltimpression_7,ltunique_users_31,ltimpression_31,\n", "case when df.facility_id in (418,779,910,918,1014,1028,1032,1034,1175,1178,1185,1238,1260,1324,1515,1612,1792,1995,1301) then 'Longtail_store' else '10_minutes' end as Store_type\n", ", applive_days_t_1 , applive_days_l_7,applive_days_l_31,active_days_t_1,active_days_l_7,active_days_l_31\n", "\n", "\n", "from metrics.backend_on_hand_availability_df df\n", "left join outlets o on o.facility_id = df.facility_id\n", "left join outlets s on s.facility_id = df.Sender_facility_id\n", "left join lake_rpc.product_facility_master_assortment ma on df.facility_id = ma.facility_id and df.item_id = ma.item_id\n", "left join lake_vms.vms_vendor_facility_alignment va on df.sender_facility_id = va.facility_id and df.item_id = va.item_id\n", "-- left join (Select item_id,facility_id,case_sensitivity_type,vendor_id, active from lake_vms.vendor_item_physical_facility_attributes) cs on df.item_id = cs.item_id and cs.vendor_id = va.vendor_id and df.sender_facility_id = cs.facility_id and cs.active = 1\n", "left join lake_vms.vendor_physical_facility_attributes vs on  df.sender_facility_id = vs.facility_id and va.vendor_id = vs.vendor_id and vs.active =1\n", "left join (select max(variant_mrp) as variant_mrp,inner_case_size,outer_case_size,item_id,brand_id from lake_rpc.product_product where active = 1 and approved = 1 group by  2,3,4,5) pc on pc.item_id =df.item_id \n", "left join (select item_id, product_id from lake_rpc.item_product_mapping) ipm on ipm.item_id = pc.item_id\n", "left join (select id, name, manufacturer_id from lake_rpc.product_brand) pb on pb.id = pc.brand_id\n", "-- left join (select vendor_id,manufacturer_id,facility_id,po_days,po_cycle,active from lake_vms.vendor_manufacturer_physical_facility_attributes) mpfa on mpfa.vendor_id = va.vendor_id and mpfa.manufacturer_id = pb.manufacturer_id and mpfa.facility_id = df.sender_facility_id  and mpfa.active = 1\n", "left join lake_rpc.tot_margin tot on df.sender_facility_id = tot.facility_id and df.item_id = tot.item_id\n", "left join (select Distinct item_id,facility_id\n", ",count (Distinct case when order_date = (current_date -1) then app_lives end) as applive_days_t_1\n", ",Sum( case when order_date between current_date -7  and current_date  -1 then app_lives end) as applive_days_l_7\n", ",Sum( case when order_date between current_date - 31 and current_date   then app_lives end) as applive_days_l_31\n", ",count (Distinct case when order_date = (current_date -1) then active_flags end) as active_days_t_1\n", ",Sum( case when order_date between current_date -7 and current_date -1 then active_flags end) as active_days_l_7\n", ",Sum( case when order_date between current_date -31 and current_date   then active_flags end) as active_days_l_31\n", "\n", "from (\n", "select distinct date(order_date) as order_date, item_id,facility_id,case when count(active_flag) >16 then '1' else '0' end as active_flags,case when sum(app_live) >16 then '1' else '0' end as app_lives from consumer.rpc_daily_availability where order_date > current_date -31 and l0 in ('Beauty & Cosmetics')  group by 1,2,3 \n", ")group by 1,2) c on df.item_id = c.item_id and df.facility_id =c.facility_id\n", "left join (With imp as(\n", "select \n", "    sp.at_date_ist,\n", "    coalesce(sp.properties__child_widget_id,sp.properties__widget_id, sp.properties__product_id) as pid,\n", "    -- properties__widget_title as  pname,\n", "    mm.facility_id,\n", "    mm.pos_outlet_id,\n", "    count(distinct sp.device_uuid) as unique_users,\n", "    count(sp.at_ist) as impressions\n", "from \n", "    spectrum.mobile_impression_data sp\n", "join\n", "    dwh.dim_merchant_outlet_facility_mapping mm\n", "  on mm.frontend_merchant_id = sp.traits__merchant_id\n", "where \n", "    sp.at_date_ist >= current_date -31\n", "    and sp.properties__l0_category in ('Beauty & Cosmetics')\n", "    and sp.traits__user_id is not NULL\n", "    and sp.traits__user_id not in (-1,0)\n", "    and sp.traits__city_id is not NULL\n", "\tand sp.traits__merchant_id is not NULL\n", "\t--and sp.traits__merchant_name is not NULL\n", "\tand sp.name in ('Product Shown')\n", "\n", "group by 1,2,3,4)\n", "\n", "Select \n", "    pid,\n", "    product_name,\n", "    facility_id,\n", "    pos_outlet_id,\n", "    sum (case when at_date_ist = current_date-1 then unique_users end) as unique_users_ld,\n", "    sum (case when at_date_ist = current_date-1 then impressions end) as impression_ld,\n", "    sum (case when at_date_ist between current_date-7 and current_date then unique_users end) as unique_users_7,\n", "    sum (case when at_date_ist between current_date-7 and current_date then impressions end) as impression_7,\n", "    sum (case when at_date_ist between current_date-31 and current_date then unique_users end) as unique_users_31,\n", "    sum (case when at_date_ist between current_date-31 and current_date then impressions end) as impression_31\n", " from imp i\n", " join dwh.dim_product dp \n", "    on dp.product_id=i.pid\n", "    and is_current=1 \n", "    and is_product_enabled=1 \n", "group by 1,2,3,4)imp on imp.pid = ipm.product_id and df.facility_id = imp.facility_id\n", " left join (With imp as(\n", "select \n", "    sp.at_date_ist,\n", "    coalesce(sp.properties__child_widget_id,sp.properties__widget_id, sp.properties__product_id) as pid,\n", "    -- properties__widget_title as  pname,\n", "    longtail_facility_id,\n", "    longtail_outlet_id,\n", "    count(distinct sp.device_uuid) as unique_users,\n", "    count(sp.at_ist) as impressions\n", "from \n", "    spectrum.mobile_impression_data sp\n", "join\n", "    consumer.longtail_express_merchant_mapping mm\n", "  on mm.express_frontend_merchant_id = sp.traits__merchant_id\n", "where \n", "    sp.at_date_ist >= current_date -31\n", "    and sp.properties__l0_category in ('Beauty & Cosmetics')\n", "    and sp.traits__user_id is not NULL\n", "    and sp.traits__user_id not in (-1,0)\n", "    and sp.traits__city_id is not NULL\n", "\tand sp.traits__merchant_id is not NULL\n", "\t--and sp.traits__merchant_name is not NULL\n", "\tand sp.name in ('Product Shown')\n", "\n", "group by 1,2,3,4)\n", "\n", "Select \n", "    pid,\n", "    product_name,\n", "    longtail_facility_id,\n", "    case when longtail_outlet_id= 3670 then '2340' else longtail_outlet_id end as longtail_outlet_id,\n", "    sum (case when at_date_ist = current_date-1 then unique_users end) as ltunique_users_ld,\n", "    sum (case when at_date_ist = current_date-1 then impressions end) as ltimpression_ld,\n", "    sum (case when at_date_ist between current_date-7 and current_date then unique_users end) as ltunique_users_7,\n", "    sum (case when at_date_ist between current_date-7 and current_date then impressions end) as ltimpression_7,\n", "    sum (case when at_date_ist between current_date-31 and current_date then unique_users end) as ltunique_users_31,\n", "    sum (case when at_date_ist between current_date-31 and current_date then impressions end) as ltimpression_31\n", " from imp i\n", " join dwh.dim_product dp \n", "    on dp.product_id=i.pid\n", "    and is_current=1 \n", "    and is_product_enabled=1 \n", "group by 1,2,3,4) imt on imt.pid = ipm.product_id and df.facility_id = imt.longtail_facility_id\n", "\n", "left join (with\n", "\n", "sales as\n", "    (select\n", "        (oi.install_ts + interval '5.5 Hours') as order_date,\n", "        city_name,\n", "        od.facility_id,\n", "        pl.item_id,\n", "        oi.selling_price,\n", "        (oi.selling_price*1.0000/pl.multiplier+0.000) as item_selling_price,\n", "        oi.order_id,\n", "        od.customer_id,\n", "        pl.multiplier,\n", "        ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) as sales_quantity,\n", "        ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) * (oi.selling_price*1.0000/pl.multiplier) as sales_value\n", "\n", "            from lake_oms_bifrost.oms_order_item oi\n", "\n", "                left join\n", "                    (select distinct ipr.product_id,\n", "                        case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "                        case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else COALESCE(ipom_0.multiplier,1) end as multiplier\n", "                            from lake_rpc.item_product_mapping ipr\n", "\n", "                                left join\n", "                                    dwh.dim_item_product_offer_mapping ipom on ipom.product_id = ipr.product_id\n", "                                        and ipr.item_id = ipom.item_id\n", "                                left join\n", "                                    dwh.dim_item_product_offer_mapping ipom_0 on ipom_0.product_id = ipr.product_id\n", "                    ) pl on pl.product_id = oi.product_id\n", "                \n", "                join\n", "                    lake_oms_bifrost.oms_order oo on oo.id = oi.order_id and oo.type in ('RetailForwardOrder','RetailSuborder','')\n", "                        and oo.install_ts > current_date-31\n", "\n", "                join \n", "                    (select distinct facility_id, cast(ancestor as bigint) as order_id, customer_id, cl.name as city_name\n", "                        from lake_ims.ims_order_details od\n", "\n", "                            join\n", "                                lake_retail.console_outlet rco on rco.id = outlet\n", "                            left join\n", "                                lake_retail.console_location cl on cl.id = rco.tax_location_id\n", "                                    where status_id not in (3,4,5)\n", "                                        and od.created_at > current_date-31\n", "                    ) od on od.order_id = oi.order_id\n", "\n", "                        where\n", "                            oi.install_ts  > current_date-31\n", "    ),\n", "\n", "final as\n", "    (select city_name, date(order_date) as date_, order_id, facility_id, customer_id,\n", "        s.item_id, sales_quantity, sales_value\n", "            from sales s\n", "                \n", "\n", "                    where sales_quantity > 0 and s.item_id is not null\n", "    )\n", "    \n", "        select\n", "            city_name,\n", "            facility_id,\n", "            cf.name as facility_name,\n", "            item_id,\n", "            \n", "            count (distinct case when date_ = (current_date-1) then customer_id end) as T_1_customer_count,\n", "             count (distinct case when date_ =  (current_date-1) then order_id end) as T_1_carts,\n", "             Sum (distinct case when date_ =  (current_date-1) then sales_quantity end) as T_1_sales_quantity,\n", "             Sum (distinct case when date_ =  (current_date-1) then sales_value end) as T_1_sales_value,\n", "             \n", "             count (distinct case when date_ between current_date-7  and current_date then customer_id end) as L7_customer_count,\n", "             count (distinct case when date_ between current_date-7  and current_date then order_id end) as L7_carts,\n", "             Sum (distinct case when date_ between current_date-7  and current_date then sales_quantity end) as L7_sales_quantity,\n", "             Sum (distinct case when date_ between current_date-7  and current_date then sales_value end) as L7_sales_value,\n", "            count(distinct customer_id) as L31_customer_count,\n", "            count(distinct order_id) as L31_carts,\n", "            sum(sales_quantity) as L31_sales_quantity,\n", "            sum(sales_value) as L31_sales_value\n", "            \n", "                from final f\n", "                \n", "                join\n", "                    lake_crates.facility cf on cf.id = f.facility_id\n", "                    \n", "                \n", "\n", "                    group by 1,2,3,4) gfh on gfh.facility_id = df.facility_id and gfh.item_id = df.item_id\n", "\n", "\n", "\n", "where l0 in ('Beauty & Cosmetics')\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(Beauty, redshift)\n", "\n", "\n", "Beauty = Beauty()"]}, {"cell_type": "code", "execution_count": null, "id": "96352573-7e79-4e63-8608-493ef038c3b4", "metadata": {}, "outputs": [], "source": ["Beauty.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eee4e9e4-c244-48b0-984b-20ce5cb279e3", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(Beauty, \"1ilsulT9ZDH5Zcc9Vge3t4o7qCgGvZvODxQXupdi3rX4\", \"data\")"]}, {"cell_type": "code", "execution_count": null, "id": "a8b8ed70-45fe-4e93-b205-6418b08d45f1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}