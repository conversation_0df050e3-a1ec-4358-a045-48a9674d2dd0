alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: fe_assortment_type_mapping
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06LLMJHFCK
path: povms/events/etl/fe_assortment_type_mapping
paused: false
pool: povms_pool
project_name: events
schedule:
  end_date: '2025-05-22T00:00:00'
  interval: 31 3,12 * * *
  start_date: '2025-03-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
