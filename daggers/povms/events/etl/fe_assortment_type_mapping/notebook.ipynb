{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(# f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH carts_cnt AS (\n", "SELECT  outlet_id, COUNT(DISTINCT cart_id) AS num_of_carts\n", "FROM    dwh.fact_sales_order_details\n", "WHERE   order_create_dt_ist >= current_date - interval '3' day AND order_current_status = 'DELIVERED' and is_internal_order = false \n", "        and cart_checkout_ts_ist >= current_date - interval '3' day AND org_channel_name = 'BLINKIT'  \n", "GROUP BY 1 ),\n", "\n", "fo AS (\n", "SELECT      cl.cluster_id, cl.cluster_name, rcl.id AS city_id, rcl.name as city_name, om.facility_id, om.outlet_id, om.outlet_name, type_id -- type_id = 3 Longtail only\n", "            ,   ( CASE \n", "                    WHEN COUNT(polygon_type) = 2 THEN 'Both' \n", "                    WHEN COUNT(polygon_type) = 1 AND MIN(polygon_type) = 'EXPRESS' THEN 'EXPRESS'\n", "                    WHEN COUNT(polygon_type) = 1 AND MIN(polygon_type) = 'LONGTAIL' THEN 'LONGTAIL'\n", "                END) AS store_assortment_state\n", "FROM        rpc.ams_cluster cl\n", "INNER JOIN  rpc.ams_city_cluster_mapping ccm\n", "            ON  cl.cluster_id = ccm.cluster_id AND ccm.lake_active_record AND cl.lake_active_record AND ccm.active = true AND cl.cluster_id > 15000 \n", "INNER JOIN  retail.console_location rcl\n", "            ON rcl.id = ccm.city_id AND rcl.lake_active_record\n", "INNER JOIN  retail.console_outlet rco\n", "            ON  rco.tax_location_id = rcl.id AND rco.lake_active_record AND rco.business_type_id IN (7)\n", "INNER JOIN  po.physical_facility_outlet_mapping om\n", "            ON rco.id = om.outlet_id AND om.active = 1 AND om.ars_active = 1 AND om.lake_active_record AND om.facility_id != 3253 -- <PERSON><PERSON><PERSON><PERSON>\n", "            AND om.outlet_name NOT LIKE '%%Test%%' AND om.outlet_name NOT LIKE '%%Draft%%' AND om.outlet_name NOT LIKE '%%Dummy%%' \n", "            AND om.outlet_name NOT LIKE '%%Digital Goods%%' AND SUBSTRING(om.outlet_name,1,2) != 'KS'\n", "            AND SUBSTRING(om.outlet_name,1,2) != 'HP'\n", "INNER JOIN  dwh.dim_merchant_outlet_facility_mapping mo_map \n", "            ON  mo_map.facility_id = om.facility_id  AND mo_map.is_current AND mo_map.is_current_mapping_active \n", "                AND mo_map.is_backend_merchant_active AND mo_map.is_frontend_merchant_active \n", "INNER JOIN  (\n", "              SELECT cast(merchant_id AS int) merchant_id, (CASE WHEN TYPE = 'STORE_POLYGON' THEN 'EXPRESS' ELSE 'LONGTAIL'END) AS polygon_type\n", "              FROM serviceability.ser_store_polygons\n", "              WHERE TYPE IN ('STORE_POLYGON', 'LONGTAIL_POLYGON') AND is_active = TRUE AND lake_active_record\n", "            ) poly \n", "            ON poly.merchant_id = mo_map.frontend_merchant_id \n", "LEFT JOIN  (\n", "                SELECT  outlet_id, (CASE WHEN DATE_DIFF('DAY', CURRENT_DATE, DATE(MIN(created_at))) <= 7 \n", "                        AND DATE_DIFF('DAY', CURRENT_DATE, DATE(MIN(created_at))) >= 0 THEN 'New Store' ELSE 'Old Store' END) AS store_remarks\n", "                FROM    po.bulk_facility_outlet_mapping \n", "                WHERE active and lake_active_record\n", "                GROUP BY 1\n", "            ) bfom\n", "            ON om.outlet_id =  bfom.outlet_id \n", "LEFT JOIN  carts_cnt c\n", "            ON c.outlet_id = bfom.outlet_id \n", "WHERE       ((c.num_of_carts > 15 AND bfom.store_remarks = 'Old Store') OR bfom.store_remarks = 'New Store')\n", "GROUP BY    1,2,3,4,5,6,7,8 ),\n", "\n", "be_mapping_fe_city_rule as (\n", "SELECT  fo.cluster_id, fo.cluster_name, fo.city_id, fo.city_name, fo.facility_id, fo.outlet_id, fo.outlet_name, fo.type_id\n", "        , COALESCE(TRY_CAST(JSON_EXTRACT(fr.backend_outlet_ids,'$.EXPRESS_BACKEND_ID') as int),\n", "          TRY_CAST(JSON_EXTRACT(cr.backend_outlet_ids,'$.EXPRESS_BACKEND_ID') as int)) AS be_outlet_id\n", "\n", "FROM        fo\n", "LEFT JOIN   rpc.transfer_tag_rules fr \n", "            ON  fr.frontend_facility_id = fo.facility_id AND fr.active AND fr.category in ('GROCERY') AND fr.frontend_facility_id > 0 \n", "                and fr.item_id = 0 and (fr.manufacturer_id is null or fr.manufacturer_id = 0) \n", "LEFT JOIN   rpc.transfer_tag_rules cr \n", "            ON  cr.city_id = fo.city_id AND cr.active AND cr.category in ('GROCERY') and cr.frontend_facility_id = 0 and cr.item_id = 0 \n", "                and (cr.manufacturer_id is null or cr.manufacturer_id = 0)\n", "GROUP BY    1,2,3,4,5,6,7,8,9 ),\n", "\n", "cl_fe_be_mapping AS (\n", "SELECT      b.cluster_id, b.cluster_name, b.city_id, b.city_name, b.facility_id AS fe_facility_id, b.outlet_id AS fe_outlet_id\n", "            , b.outlet_name AS fe_name, b.type_id, b.be_outlet_id, be.outlet_name AS be_outlet_name\n", "\n", "FROM        be_mapping_fe_city_rule b\n", "INNER JOIN  po.physical_facility_outlet_mapping be\n", "            ON  b.be_outlet_id = be.outlet_id AND be.active = 1 AND be.ars_active = 1 and be.outlet_name not like '%%Test%%' \n", "                and be.outlet_name not like '%%Dummy%%' ),\n", "\n", "express_data AS (\n", "SELECT  cluster_id, cluster_name, city_id, city_name, fe_outlet_id, fe_name, fe_facility_id, be_outlet_id, be_outlet_name\n", "        , 'Express' AS assortment_type\n", "FROM    cl_fe_be_mapping\n", "WHERE   type_id != 3 ),\n", "\n", "longtail_raw AS (\n", "SELECT      fo.cluster_id, fo.cluster_name, fo.city_id, fo.city_name, fo.fe_outlet_id, fo.fe_name, fo.fe_facility_id, b.adjacent_facility_id\n", "            , fo.be_outlet_id, fo.be_outlet_name, 'Longtail' AS assortment_type \n", "FROM        cl_fe_be_mapping fo \n", "INNER JOIN  po.long_tail_facility_store_mapping b\n", "            ON fo.fe_facility_id = b.long_tail_facility_id AND b.active = 1 AND b.lake_active_record = true ),\n", "\n", "longtail_data AS (\n", "SELECT      cluster_id, cluster_name, city_id, city_name, fe_outlet_id, fe_name, fe_facility_id, be_outlet_id, be_outlet_name, assortment_type\n", "FROM        longtail_raw \n", "GROUP BY    1,2,3,4,5,6,7,8,9,10 ),\n", "\n", "hybrid_data AS (\n", "SELECT      cluster_id, cluster_name, city_id, city_name, fe_outlet_id, fe_name, fe_facility_id, be_outlet_id, be_outlet_name\n", "            , 'Longtail' AS assortment_type\n", "FROM        longtail_data\n", "\n", "UNION ALL\n", "\n", "SELECT      fo.cluster_id, fo.cluster_name, fo.city_id, fo.city_name, fo.fe_outlet_id, fo.fe_name, fo.fe_facility_id, fo.be_outlet_id\n", "            , fo.be_outlet_name, 'EXPRESS' AS assortment_type\n", "FROM        cl_fe_be_mapping fo\n", "LEFT JOIN   longtail_raw b\n", "            ON fo.fe_facility_id = b.adjacent_facility_id  \n", "WHERE       b.adjacent_facility_id IS NULL ),\n", "\n", "fe_assortment_type_mapping AS (\n", "SELECT  be_outlet_id, be_outlet_name, cluster_id, cluster_name, city_id, city_name, fe_outlet_id, fe_facility_id, fe_name, 'EXPRESS' AS assortment_type FROM express_data \n", "UNION ALL\n", "SELECT  be_outlet_id, be_outlet_name, cluster_id, cluster_name, city_id, city_name, fe_outlet_id, fe_facility_id, fe_name, 'LONGTAIL' AS assortment_type FROM longtail_data \n", "UNION ALL\n", "SELECT  be_outlet_id, be_outlet_name, cluster_id, cluster_name, city_id, city_name, fe_outlet_id, fe_facility_id, fe_name, 'HYBRID' AS assortment_type FROM hybrid_data )\n", "\n", "SELECT cluster_id, cluster_name, city_id, city_name, fe_outlet_id, fe_facility_id, fe_name, be_outlet_id, be_outlet_name AS be_name, assortment_type\n", "        , CURRENT_DATE AS updated_dt_ist\n", "FROM    fe_assortment_type_mapping\n", "\n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {"tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_dt_ist\", \"type\": \"date\", \"description\": \"updated_dt_ist\"},\n", "    {\"name\": \"cluster_id\", \"type\": \"integer\", \"description\": \"cluster_id\"},\n", "    {\"name\": \"cluster_name\", \"type\": \"varchar\", \"description\": \"cluster_name\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"city_id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"fe_outlet_id\", \"type\": \"integer\", \"description\": \"fe_outlet_id\"},\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"fe_facility_id\"},\n", "    {\"name\": \"fe_name\", \"type\": \"varchar\", \"description\": \"fe_name\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"be_name\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"assortment_type\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fe_assortment_type_mapping\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"fe_outlet_id\", \"assortment_type\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains mapping of cluster-city-frontend-backend-assortmentType\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "32710e19-963f-4df4-bdd6-2c2394ea8004", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}