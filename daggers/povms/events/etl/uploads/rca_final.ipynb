{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(# f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH cluster_fe_be AS (\n", "SELECT DISTINCT be_outlet_id, cluster_id, cluster_name, fe_facility_id, fe_outlet_id FROM supply_etls.fe_assortment_type_mapping ),\n", "\n", "fe_assort_details AS (\n", "SELECT      pfma.facility_id, pfma.item_id, pfma.master_assortment_substate_id\n", "            , (CASE \n", "                    WHEN CAST(json_extract(pfma.meta, '$.launch_type' ) AS VARCHAR) = 'HYBRID' THEN 'HYBRID' ELSE pfma.assortment_type\n", "                END) AS assortment_type\n", "FROM        rpc.product_facility_master_assortment pfma\n", "WHERE       pfma.lake_active_record = true AND pfma.active = 1 AND pfma.master_assortment_substate_id IN (1,3) ),\n", "\n", "cl_assortment_defined AS ( \n", "SELECT      up.cluster_id, up.item_id\n", "            , ( CASE \n", "                    WHEN MAX(CASE WHEN pfma.assortment_type = 'HYBRID' THEN 1 ELSE 0 END) = 1 THEN 'HYBRID'\n", "                    WHEN MAX(CASE WHEN pfma.assortment_type = 'LONGTAIL' THEN 1 ELSE 0 END) = 1 THEN 'LONGTAIL'\n", "                    WHEN MAX(CASE WHEN pfma.assortment_type = 'EXPRESS' THEN 1 ELSE 0 END) = 1 THEN 'EXPRESS'\n", "                END) AS assortment_type\n", "            , ( CASE \n", "                    WHEN COUNT(CASE WHEN pfma.master_assortment_substate_id = 1 THEN fe_facility_id END) <\n", "                        COUNT(CASE WHEN pfma.master_assortment_substate_id = 3 THEN fe_facility_id END) THEN 'TEMP INACTIVE'\n", "\n", "                    WHEN COUNT(CASE WHEN pfma.master_assortment_substate_id = 1 THEN fe_facility_id END) >\n", "                        COUNT(CASE WHEN pfma.master_assortment_substate_id = 3 THEN fe_facility_id END) THEN 'ACTIVE'\n", "\n", "                    WHEN COUNT(CASE WHEN pfma.master_assortment_substate_id = 1 THEN fe_facility_id END) =\n", "                        COUNT(CASE WHEN pfma.master_assortment_substate_id = 3 THEN fe_facility_id END) \n", "                        AND COUNT(CASE WHEN pfma.master_assortment_substate_id = 1 THEN fe_facility_id END) != 0 THEN 'ACTIVE'\n", "                \n", "                END) AS assortment_state\n", "\n", "FROM        (SELECT cluster_id, item_id FROM supply_etls.event_uploads_rca_v1 GROUP BY 1,2 ) up\n", "LEFT JOIN   (SELECT cluster_id, fe_facility_id FROM supply_etls.fe_assortment_type_mapping GROUP BY 1,2) m\n", "            ON m.cluster_id = up.cluster_id  \n", "LEFT JOIN   fe_assort_details pfma\n", "            ON  pfma.item_id = up.item_id AND pfma.facility_id = m.fe_facility_id \n", "GROUP BY    1,2 ),\n", "\n", "be_cl_ideal_assort_stores AS (\n", "SELECT      be_outlet_id, cluster_id, assortment_type, COUNT(fe_facility_id) AS ideal_assortment_stores\n", "FROM        supply_etls.fe_assortment_type_mapping am\n", "GROUP BY    1,2,3 ),\n", "\n", "be_cl_total_stores AS (\n", "SELECT      be_outlet_id, cluster_id, COUNT(fe_facility_id) AS exp_stores_cnt\n", "FROM        supply_etls.fe_assortment_type_mapping am\n", "WHERE       assortment_type = 'EXPRESS'\n", "GROUP BY    1,2 ),\n", "\n", "be_cl_actual_assort_stores AS (\n", "SELECT      b.event_id, b.be_outlet_id, b.cluster_id, b.item_id\n", "            , COUNT(CASE WHEN fe.master_assortment_substate_id IN (1,3) THEN fe.facility_id END) AS actual_activation_stores\n", "            , COUNT(CASE WHEN fe.master_assortment_substate_id = 1 THEN fe.facility_id END) AS active_stores\n", "            , COUNT(CASE WHEN fe.master_assortment_substate_id = 3 THEN fe.facility_id END) AS temp_inactive_stores\n", "\n", "FROM        supply_etls.event_uploads_rca_v1  b\n", "LEFT JOIN   cluster_fe_be m\n", "            ON b.be_outlet_id = m.be_outlet_id AND b.cluster_id = m.cluster_id \n", "LEFT JOIN   fe_assort_details fe\n", "            ON fe.facility_id = m.fe_facility_id AND fe.item_id = b.item_id\n", "GROUP BY    1,2,3,4 )\n", "\n", "SELECT      CURRENT_DATE AS updated_dt, CURRENT_TIMESTAMP AS updated_at, b.event_id, b.festival_name, b.be_outlet_id, b.be_name\n", "            , b.cluster_id, b.cluster_name, b.p_type, b.item_id, b.item_name, b.item_status, b.rnk\n", "            , ( CASE \n", "                WHEN b.rnk IN (1,2,3) AND b.item_status = 'Item Not planned' AND b.suggested_qty > 0 THEN b.suggested_qty \n", "                WHEN b.rnk IN (1,2,3) AND b.item_status = 'Item Not planned' AND d.assortment_type IS NULL \n", "                        THEN (ts.exp_stores_cnt * 3)\n", "                WHEN b.rnk IN (1,2,3) AND b.item_status = 'Item Not planned'  \n", "                    THEN COALESCE((ias.ideal_assortment_stores*3),0)\n", "            END) AS suggested_qty\n", "        , COALESCE(d.assortment_type, 'EXPRESS') AS assortment_type, COALESCE(d.assortment_state, 'TEMP INACTIVE') AS assortment_state\n", "        , (CASE \n", "                WHEN d.assortment_type IS NULL THEN exp_stores_cnt \n", "                ELSE COALESCE(ias.ideal_assortment_stores,0)\n", "            END) AS ideal_assortment_stores, aas.actual_activation_stores, aas.active_stores, aas.temp_inactive_stores\n", "\n", "FROM        supply_etls.event_uploads_rca_v1 b\n", "LEFT JOIN   cl_assortment_defined d\n", "            ON d.cluster_id = b.cluster_id AND d.item_id = b.item_id\n", "LEFT JOIN   be_cl_ideal_assort_stores ias\n", "            ON b.be_outlet_id = ias.be_outlet_id AND b.cluster_id = ias.cluster_id AND d.assortment_type = ias.assortment_type \n", "LEFT JOIN   be_cl_actual_assort_stores aas\n", "            ON b.be_outlet_id = aas.be_outlet_id AND b.cluster_id = aas.cluster_id AND b.item_id = aas.item_id \n", "                AND b.event_id = aas.event_id \n", "LEFT JOIN   be_cl_total_stores ts\n", "            ON ts.be_outlet_id = b.be_outlet_id AND ts.cluster_id = b.cluster_id\n", "            \n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {"tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp(6)\", \"description\": \"updated_at\"},\n", "    {\"name\": \"updated_dt\", \"type\": \"date\", \"description\": \"updated_dt\"},\n", "    {\"name\": \"event_id\", \"type\": \"integer\", \"description\": \"event_id\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"festival_name\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"be_name\"},\n", "    {\"name\": \"cluster_id\", \"type\": \"integer\", \"description\": \"cluster_id\"},\n", "    {\"name\": \"cluster_name\", \"type\": \"varchar\", \"description\": \"cluster_name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"p_type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"rnk\", \"type\": \"integer\", \"description\": \"rnk\"},\n", "    {\"name\": \"suggested_qty\", \"type\": \"integer\", \"description\": \"suggested_qty\"},\n", "    {\"name\": \"item_status\", \"type\": \"varchar\", \"description\": \"item_status\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"assortment_type\"},\n", "    {\"name\": \"assortment_state\", \"type\": \"varchar\", \"description\": \"assortment_state\"},\n", "    {\n", "        \"name\": \"ideal_assortment_stores\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"ideal_assortment_stores\",\n", "    },\n", "    {\n", "        \"name\": \"actual_activation_stores\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"actual_activation_stores\",\n", "    },\n", "    {\"name\": \"active_stores\", \"type\": \"integer\", \"description\": \"active_stores\"},\n", "    {\"name\": \"temp_inactive_stores\", \"type\": \"integer\", \"description\": \"temp_inactive_stores\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"event_uploads_rca\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"event_id\", \"be_outlet_id\", \"cluster_id\", \"item_id\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table use for bulk uploads rca\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "32710e19-963f-4df4-bdd6-2c2394ea8004", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}