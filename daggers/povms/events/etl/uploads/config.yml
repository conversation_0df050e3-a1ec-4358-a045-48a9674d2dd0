alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: uploads
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: rca_v1
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: rca_final
  parameters: null
  tag: second
owner:
  email: <EMAIL>
  slack_id: U06LLMJHFCK
path: povms/events/etl/uploads
paused: false
pool: povms_pool
project_name: events
schedule:
  end_date: '2025-05-27T00:00:00'
  interval: 16 2-18/2 * * *
  start_date: '2025-02-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 2
concurrency: 3
