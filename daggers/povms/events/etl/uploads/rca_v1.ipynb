{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(# f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH cluster_upload as (\n", "SELECT  event_id, location_id AS cluster_id, item_id, planned_qty\n", "FROM    ars.bulk_process_event_planner\n", "WHERE   partition_field is not null and active = 1 AND sale_end_date >= CURRENT_DATE AND planned_qty > 0 ),\n", "\n", "items AS (\n", "select  item_id, item_name, l0_id, l0_category, l1_id, l1_category, l2_id, l2_category, p_type, assortment_type AS category\n", "FROM    supply_etls.item_details\n", "WHERE   handling_type = 'Non Packaging Material' ),\n", "\n", "event_name as (\n", "SELECT  sei.id AS event_id, CONCAT(name, '_', cast(start_date as varchar)) AS festival_name\n", "FROM rpc.supply_event_info sei\n", "JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "WHERE sei.active = TRUE AND se.active = TRUE ),\n", "\n", "cluster_be_mapping AS (\n", "SELECT cluster_id, cluster_name, be_outlet_id, be_name FROM supply_etls.fe_assortment_type_mapping GROUP BY 1,2,3,4),\n", "\n", "uploads AS (\n", "SELECT      e.event_id, en.festival_name, m.be_outlet_id, m.be_name, m.cluster_id, m.cluster_name, up.item_id, i.item_name, i.p_type\n", "            , up.planned_qty\n", "FROM        cluster_be_mapping m\n", "CROSS JOIN  (SELECT event_id FROM cluster_upload GROUP BY 1) e \n", "LEFT JOIN   event_name en \n", "            ON en.event_id = e.event_id\n", "LEFT JOIN   cluster_upload up\n", "            ON m.cluster_id = up.cluster_id AND e.event_id = up.event_id\n", "LEFT JOIN   items i\n", "            ON  up.item_id = i.item_id ),\n", "\n", "cl_universe AS (\n", "SELECT      a.event_id, a.festival_name, a.be_outlet_id, a.be_name, a.cluster_id, a.cluster_name, b.p_type, b.item_id\n", "            , b.item_name, MAX(c.planned_qty) AS cl_planned_qty\n", "            , (CASE WHEN MAX(c.planned_qty) IS NULL THEN 'Item Not planned' ELSE 'Item Planned' END) AS item_status\n", "            \n", "FROM        uploads a\n", "INNER JOIN  uploads b\n", "            ON  a.event_id = b.event_id AND a.be_outlet_id = b.be_outlet_id AND b.item_id IS NOT NULL\n", "LEFT JOIN   uploads c\n", "            ON a.event_id = c.event_id AND a.be_outlet_id = c.be_outlet_id AND a.cluster_id = c.cluster_id AND b.item_id = c.item_id \n", "GROUP BY    1,2,3,4,5,6,7,8,9 ),\n", "\n", "cluster_fe_be AS (\n", "SELECT DISTINCT be_outlet_id, cluster_id, cluster_name, fe_facility_id, fe_outlet_id FROM supply_etls.fe_assortment_type_mapping ),\n", "\n", "outlet_ptype_wise_searches AS (\n", "SELECT      event_id, outlet_id, product_type, final_val\n", "FROM        supply_etls.event_final_store_ptype_search_distribution\n", "WHERE       event_id IS NOT NULL\n", "GROUP BY    1,2,3,4 ),\n", "\n", "outlet_ptype_searches AS (\n", "SELECT      s.event_id, m.be_outlet_id, m.cluster_id, s.product_type AS p_type, m.fe_outlet_id, s.final_val AS fe_searches\n", "            , SUM(s.final_val) OVER (PARTITION BY s.event_id, m.cluster_id, s.product_type) AS cl_searches\n", "\n", "FROM        cluster_fe_be m\n", "INNER JOIN  outlet_ptype_wise_searches s\n", "            ON m.fe_outlet_id = s.outlet_id ),\n", "\n", "be_cl_universe AS (\n", "SELECT      cl.event_id, cl.festival_name, cl.be_outlet_id, cl.be_name, cl.cluster_id, cl.cluster_name, cl.p_type, cl.item_id\n", "            , cl.item_name, cl.item_status -- , cl.cl_planned_qty, s.fe_searches, s.cl_searches\n", "            , SUM(s.fe_searches) AS be_cl_searches\n", "            , ROUND(SUM((CAST((s.fe_searches / s.cl_searches) AS DOUBLE) * cl.cl_planned_qty)),1) AS be_cl_planned_qty -- s_pq\n", "            \n", "FROM        cl_universe cl\n", "LEFT JOIN   outlet_ptype_searches s\n", "            ON cl.event_id = s.event_id AND cl.be_outlet_id = s.be_outlet_id AND cl.cluster_id = s.cluster_id AND cl.p_type = s.p_type\n", "GROUP BY    1,2,3,4,5,6,7,8,9,10 ),\n", "\n", "be_universe AS (\n", "SELECT       event_id, be_outlet_id, p_type, item_id, SUM(be_cl_planned_qty) AS be_planned_qty, SUM(be_cl_searches) AS be_searches\n", "            , DENSE_RANK() OVER (PARTITION BY event_id, be_outlet_id, p_type ORDER BY SUM(be_cl_planned_qty) DESC) AS rnk\n", "FROM        be_cl_universe\n", "GROUP BY    1,2,3,4 )\n", "\n", "-- SELECT      be_cl.event_id, be_cl.festival_name, be_cl.cluster_id, be_cl.cluster_name, be_cl.p_type, be_cl.item_id, be_cl.item_name\n", "--             , be_cl.item_status, ROUND( SUM((CAST((be_cl.be_cl_searches * be.be_planned_qty) AS DOUBLE) / \n", "--                 (be.be_searches - be_CL.be_cl_searches) )),0) AS suggested_qty\n", "            \n", "-- FROM        be_cl_universe be_cl\n", "-- INNER JOIN  be_universe be\n", "--             ON be_cl.event_id = be.event_id AND be_cl.be_outlet_id = be.be_outlet_id AND be_cl.item_id = be.item_id\n", "-- WHERE       be.rnk IN (1,2,3) AND be_cl.item_status = 'Item Not planned' \n", "-- GROUP BY    1,2,3,4,5,6,7,8 \n", "\n", "\n", "SELECT      CURRENT_DATE AS updated_dt, CURRENT_TIMESTAMP AS updated_at, be_cl.event_id, be_cl.festival_name, be_cl.be_outlet_id\n", "            , be_cl.be_name, be_cl.cluster_id, be_cl.cluster_name, be_cl.p_type, be_cl.item_id, be_cl.item_name, be_cl.item_status, be.rnk\n", "            --, be.be_planned_qty, be_cl.be_cl_planned_qty, be.be_searches, be_cl.be_cl_searches\n", "            , ROUND((CAST((be_cl.be_cl_searches * be.be_planned_qty) AS DOUBLE) / (be.be_searches - be_CL.be_cl_searches) )) AS suggested_qty\n", "            \n", "FROM        be_cl_universe be_cl\n", "INNER JOIN  be_universe be\n", "            ON be_cl.event_id = be.event_id AND be_cl.be_outlet_id = be.be_outlet_id AND be_cl.item_id = be.item_id \n", "            \n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {"tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp(6)\", \"description\": \"updated_at\"},\n", "    {\"name\": \"updated_dt\", \"type\": \"date\", \"description\": \"updated_dt\"},\n", "    {\"name\": \"event_id\", \"type\": \"integer\", \"description\": \"event_id\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"festival_name\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"be_name\"},\n", "    {\"name\": \"cluster_id\", \"type\": \"integer\", \"description\": \"cluster_id\"},\n", "    {\"name\": \"cluster_name\", \"type\": \"varchar\", \"description\": \"cluster_name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"p_type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"rnk\", \"type\": \"integer\", \"description\": \"rnk\"},\n", "    {\"name\": \"suggested_qty\", \"type\": \"integer\", \"description\": \"suggested_qty\"},\n", "    {\"name\": \"item_status\", \"type\": \"varchar\", \"description\": \"item_status\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"event_uploads_rca_v1\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"event_id\", \"be_outlet_id\", \"cluster_id\", \"item_id\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table use for bulk uploads rca\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "32710e19-963f-4df4-bdd6-2c2394ea8004", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}