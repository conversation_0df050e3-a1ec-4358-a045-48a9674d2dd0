alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: preparation
dag_type: etl
escalation_priority: low
execution_timeout: 120
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: store_dis
  parameters: null
  tag: first
owner:
  email: <EMAIL>
  slack_id: U06LLMJHFCK
path: povms/events/etl/preparation
paused: false
pool: povms_pool
project_name: events
schedule:
  end_date: '2025-04-20T00:00:00'
  interval: 5 4-16 * * *
  start_date: '2025-02-13T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 4
concurrency: 3
