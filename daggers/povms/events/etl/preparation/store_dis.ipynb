{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b0b952e-ceff-4cba-909d-e0dd4e694e0d", "metadata": {}, "outputs": [], "source": ["# festive_metrics_df = read_sql_query(# f\"\"\"\n", "festive_metrics_query = f\"\"\"\n", "\n", "WITH outlets AS (\n", "SELECT  facility_id, facility_name, inv_outlet_id, hot_outlet_id, city_name, taggings, store_type, ars_check \n", "FROM    supply_etls.outlet_details\n", "WHERE   taggings IN ('be', 'fe') ),\n", "\n", "items AS (\n", "select  item_id, item_name, l0_id, l0_category, l1_id, l1_category, l2_id, l2_category, p_type\n", "FROM    supply_etls.item_details\n", "WHERE   handling_type = 'Non Packaging Material' ),\n", "\n", "cluster_city_mapping AS (\n", "SELECT  m.city_id, c.cluster_id, c.cluster_name\n", "FROM        rpc.ams_city_cluster_mapping m\n", "INNER JOIN  rpc.ams_cluster c \n", "            ON  c.cluster_id = m.cluster_id and c.cluster_id > 15000 AND m.lake_active_record = true AND m.active = true \n", "                AND c.lake_active_record = true ),\n", "\n", "event_name as (\n", "SELECT sei.id AS id,\n", "    CONCAT(name, '_', cast(start_date as varchar)) AS event_name, \n", "    start_date,\n", "    end_date\n", "FROM rpc.supply_event_info sei\n", "JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "WHERE sei.active = TRUE\n", "  AND se.active = TRUE ),\n", "\n", "planner as (\n", "select \n", "    location_id,\n", "    event_id,\n", "    item_id, \n", "    event_name as festival_name,\n", "    start_date AS festival_start_date,\n", "    end_date AS festival_end_date,\n", "    sale_start_date, \n", "    sale_end_date, \n", "    cut_off_date, \n", "    assortment_type, \n", "    assortment_launch_type, \n", "    substitute_ptype\n", "from ars.bulk_process_event_planner bpep\n", "join event_name en on bpep.event_id=en.id\n", "where partition_field is not null and active = 1 AND sale_end_date >= CURRENT_DATE ), \n", "\n", "store_level_data as (\n", "select event_id, city_id, outlet_id, item_id, quantity\n", "from ars.event_outlet_item_distribution \n", "where partition_field is not null and active = 1 AND quantity > 0 ),\n", "\n", "city_rule_default_be AS (\n", "select  city_id, CAST(JSON_EXTRACT(backend_outlet_ids,'$.EXPRESS_BACKEND_ID') as int) AS express_be_outlet_id\n", "        , CAST(JSON_EXTRACT(backend_outlet_ids,'$.LONGTAIL_BACKEND_ID') as int) AS longtail_be_outlet_id\n", "from    rpc.transfer_tag_rules\n", "where   active = true AND lake_active_record = true AND frontend_facility_id = 0 AND category = 'GROCERY'\n", "        and (manufacturer_id = 0 OR manufacturer_id IS NULL) and item_id = 0 ),\n", "\n", "fe_rule_default_be AS (\n", "select  frontend_facility_id, CAST(JSON_EXTRACT(backend_outlet_ids,'$.EXPRESS_BACKEND_ID') as int) AS express_be_outlet_id\n", "        , CAST(JSON_EXTRACT(backend_outlet_ids,'$.LONGTAIL_BACKEND_ID') as int) AS longtail_be_outlet_id\n", "        \n", "from    rpc.transfer_tag_rules\n", "where   active = true AND lake_active_record = true AND frontend_facility_id > 0 AND category = 'GROCERY'\n", "        and manufacturer_id = 0 and item_id = 0 ),\n", "\n", "proposed_store_movement as (\n", "SELECT      t.outlet_id, t.festival_name, t.proposed_be_facility_id as be_facility_id, be.hot_outlet_id AS be_outlet_id\n", "FROM        supply_etls.festival_tea_tagging_v2 t\n", "LEFT JOIN   outlets be\n", "            ON be.taggings = 'be' AND t.proposed_be_facility_id = be.facility_id\n", "WHERE t.active = 1 AND t.festival_name IS NOT NULL ),\n", "\n", "store_item_lvl AS (\n", "SELECT      p.event_id, p.festival_name, m.cluster_id, m.cluster_name, m.city_id, oid.outlet_id AS fe_outlet_id, fe.facility_id AS fe_facility_id\n", "            , oid.item_id, i.item_name, i.l0_id, i.l0_category, i.l1_id, i.l1_category, i.l2_id, i.l2_category, i.p_type, p.assortment_type\n", "            , p.festival_start_date, p.festival_end_date, p.sale_start_date, p.sale_end_date, p.cut_off_date, oid.quantity AS planned_qty\n", "            , (CASE WHEN iotm.item_id IS NOT NULL AND pfma.master_assortment_substate_id IN (1,3) THEN 1 ELSE 0 END) AS tea_flag\n", "            , (CASE \n", "                    WHEN iotm.item_id IS NOT NULL AND pfma.master_assortment_substate_id IN (1,3) THEN CAST(iotm.tag_value AS INT) \n", "                    ELSE NULL\n", "               END) AS be_outlet_id\n", "            , pfma.master_assortment_substate_id\n", "            , p.assortment_launch_type, p.substitute_ptype\n", "FROM        planner p \n", "INNER JOIN  cluster_city_mapping m \n", "            ON  m.cluster_id = p.location_id  \n", "INNER JOIN  store_level_data oid \n", "            ON  oid.city_id = m.city_id and oid.item_id = p.item_id AND oid.event_id = p.event_id \n", "LEFT JOIN   outlets fe\n", "            ON fe.hot_outlet_id = oid.outlet_id AND fe.taggings = 'fe' \n", "LEFT JOIN   items i\n", "            ON i.item_id = oid.item_id\n", "LEFT JOIN   rpc.item_outlet_tag_mapping iotm \n", "            ON  iotm.item_id = oid.item_id AND iotm.outlet_id = oid.outlet_id AND iotm.tag_type_id = 8 AND iotm.lake_active_record = true \n", "                AND iotm.active = 1\n", "LEFT JOIN   rpc.product_facility_master_assortment pfma\n", "            ON  pfma.lake_active_record = true AND pfma.active = 1 AND pfma.item_id = oid.item_id AND pfma.facility_id = fe.facility_id  )\n", "\n", "SELECT      s.event_id, s.festival_name, s.cluster_id, s.cluster_name, s.city_id, fe.city_name, fe_outlet_id, s.fe_facility_id\n", "            , fe.facility_name AS fe_outlet_name, s.item_id, s.item_name, s.l0_category, s.l1_category, s.l2_category\n", "            , s.p_type, s.assortment_type, s.assortment_launch_type, s.substitute_ptype, s.festival_start_date, s.festival_end_date, s.sale_start_date\n", "            , s.sale_end_date, s.cut_off_date, s.planned_qty, s.tea_flag, s.master_assortment_substate_id\n", "            , fe.ars_check, fe.store_type\n", "             , COALESCE(psm.be_outlet_id, s.be_outlet_id, fr_exp.express_be_outlet_id, cr_exp.express_be_outlet_id\n", "                 , fr_longtail.longtail_be_outlet_id, cr_longtail.longtail_be_outlet_id) AS ordering_be_outlet_id\n", "            \n", "             , COALESCE(s.be_outlet_id, fr_exp.express_be_outlet_id, cr_exp.express_be_outlet_id\n", "                 , fr_longtail.longtail_be_outlet_id, cr_longtail.longtail_be_outlet_id) AS transfers_be_outlet_id\n", "            \n", "             , CURRENT_DATE AS date_ , CURRENT_TIMESTAMP AS updated_at_ist\n", "\n", "\n", "FROM        store_item_lvl s\n", "LEFT JOIN   proposed_store_movement psm\n", "            ON psm.outlet_id = s.fe_outlet_id AND psm.festival_name = s.festival_name \n", "LEFT JOIN   fe_rule_default_be fr_exp \n", "            ON s.fe_facility_id = fr_exp.frontend_facility_id AND s.assortment_type IN ('EXPRESS', 'HYBRID')\n", "LEFT JOIN   city_rule_default_be cr_exp \n", "            ON s.city_id = cr_exp.city_id AND s.assortment_type IN ('EXPRESS', 'HYBRID')\n", "LEFT JOIN   fe_rule_default_be fr_longtail \n", "            ON s.fe_facility_id = fr_longtail.frontend_facility_id AND s.assortment_type = 'LONGTAIL'\n", "LEFT JOIN   city_rule_default_be cr_longtail  \n", "            ON s.city_id = cr_longtail.city_id AND s.assortment_type = 'LONGTAIL'\n", "LEFT JOIN   outlets fe\n", "            ON s.fe_outlet_id = fe.hot_outlet_id AND fe.taggings = 'fe'\n", "LEFT JOIN   items i\n", "            ON s.item_id = i.item_id\n", "            \n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "5520c15c-0ed7-465f-8c17-ea466a656c1f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"cluster_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"cluster_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_outlet_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"transfers_be_outlet_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"ordering_be_outlet_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"festival_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_start_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"sale_end_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"cut_off_date\", \"type\": \"date\", \"description\": \"sample description\"},\n", "    {\"name\": \"planned_qty\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"tea_flag\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\n", "        \"name\": \"master_assortment_substate_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"sample description\",\n", "    },\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1ecb3a26-ad62-40f4-b1c6-dc25cf32f750", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"replenish_store_dis\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"festival_name\", \"fe_outlet_id\", \"item_id\", \"date_\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": \"date_\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive distribution\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "d1284057-11b3-49b3-9ba4-8b3a3ac4a145", "metadata": {}, "outputs": [], "source": ["to_trino(festive_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}