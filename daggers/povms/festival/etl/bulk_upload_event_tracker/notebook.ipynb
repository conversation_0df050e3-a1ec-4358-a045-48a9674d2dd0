{"cells": [{"cell_type": "code", "execution_count": null, "id": "e518156d-88b8-4449-a88b-25ed8385791b", "metadata": {}, "outputs": [], "source": ["!pip install joblib==1.1.0"]}, {"cell_type": "code", "execution_count": null, "id": "5473701a-9a4b-44e4-9bf1-e8c456d28e8e", "metadata": {}, "outputs": [], "source": ["import sys\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "from datetime import date, timedelta\n", "import time\n", "\n", "st0 = time.time()\n", "st = time.time()\n", "\n", "USE_LOCAL_CSV_DATA = False\n", "tableName1 = \"event_bulk_upload_live_tea_outlet_level_tracker\"\n", "tableName2 = \"event_bulk_upload_live_tea_be_level_tracker\"\n", "tableName3 = \"event_bulk_upload_order_tea_be_level_tracker\"\n", "\n", "logTableName1 = \"event_bulk_upload_live_tea_outlet_level_tracker_log_v2\"\n", "logTableName2 = \"event_bulk_upload_live_tea_be_level_tracker_log\"\n", "logTableName3 = \"event_bulk_upload_order_tea_be_level_tracker_log\"\n", "\n", "ENVIRON = \"supply_etls\"\n", "CSV_FLAG = False\n", "CLEAR_MEMORY_FLAG = True\n", "EXECUTION_MODE = \"PROD\"\n", "spreadSheetId = \"1XeMHIjIg7JMyeij36Va0u67Ksq3jimnwqoJDxsrwm8k\"\n", "sheetName = \"exclusion_list\"\n", "configSheet = \"config\"\n", "slack_alert_channel = \"workdesk-event-bulk-upload-tracker-alerts\"\n", "max_processes = 3\n", "from joblib import Parallel, delayed\n", "\n", "\n", "def run_parallel_joblib(tasks_config):\n", "    # sample tasks config\n", "    # tasks_config = [\n", "    #     {'func': getCityClusterMapping, 'params': {}},  # Empty params dict\n", "    #     {'func': getEventNames, 'params': {'event_type': 'workshop', 'date_range': '2024-01-01'}},\n", "    #     {'func': fetchPlannerData, 'params': {'user_id': 456, 'start_date': '2024-01-01'}}\n", "    # ]\n", "    tasks = [delayed(config[\"func\"])(**config[\"params\"]) for config in tasks_config]\n", "    results = Parallel(n_jobs=max_processes, verbose=50)(tasks)\n", "    return results\n", "\n", "\n", "def writeCsv(df, csv_name):\n", "    if CSV_FLAG:\n", "        df.to_csv(csv_name, index=False)\n", "    return\n", "\n", "\n", "def fetchDataFromDB(query):\n", "    import pencilbox as pb\n", "    import pandas as pd\n", "    import time\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pd.read_sql_query(sql=query, con=con)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                return pd.DataFrame()\n", "\n", "\n", "def clear_memory(*args):\n", "    \"\"\"\n", "    Delete objects from memory and run garbage collection\n", "\n", "    Args:\n", "        *args: Variable names to be deleted\n", "    \"\"\"\n", "    if CLEAR_MEMORY_FLAG:\n", "        import gc\n", "\n", "        for arg in args:\n", "            if arg in globals():\n", "                del globals()[arg]\n", "        gc.collect()\n", "\n", "\n", "def convert_df_to_json_schema(df):\n", "    # Create the list of dictionaries for the \"column_dtypes\" field\n", "    column_dicts = []\n", "\n", "    for _, row in df.iterrows():\n", "        column_name = row[\"Column\"]\n", "        column_type = row[\"Type\"]\n", "\n", "        # Create a dictionary for each column with default description\n", "        column_dict = {\n", "            \"name\": column_name,\n", "            \"type\": column_type,\n", "            \"description\": f\"Description for {column_name}\",\n", "        }\n", "\n", "        column_dicts.append(column_dict)\n", "\n", "    # Create the final JSON structure\n", "    json_schema = column_dicts\n", "\n", "    return json_schema\n", "\n", "\n", "def createLogTable(\n", "    tableName,\n", "    sourceTable,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "\n", "    query = f\"describe {environ}.{sourceTable}\"\n", "    source_columnTypes = fetchDataFromDB(query)\n", "    print(\"sourceTable columnTypes\", source_columnTypes.shape)\n", "\n", "    query = f\"describe {environ}.{tableName}\"\n", "    columnTypes = fetchDataFromDB(query)\n", "    print(\"columnTypes\", columnTypes.shape)\n", "    if columnTypes.shape[0] > 0:\n", "        column_dtypes = convert_df_to_json_schema(columnTypes)\n", "    else:\n", "        if source_columnTypes.shape[0] == 0:\n", "            print(\"error - createLogTable source table format not found\")\n", "            return\n", "        else:\n", "            column_dtypes = convert_df_to_json_schema(source_columnTypes)\n", "\n", "    partition_key.extend([\"insert_ds_ist\", \"hour_\"])\n", "\n", "    ## assumption is that insert_ds_ist is always present in the original table\n", "    ## hour_ will get populated if it not available in the sql creation\n", "\n", "    if \"hour_\" not in source_columnTypes[\"Column\"].unique():\n", "        sql = f\"select *, HOUR(updated_at) AS hour_ from {environ}.{sourceTable}\"\n", "        if columnTypes.shape[0] == 0 or (\n", "            \"Column\" in columnTypes.columns and \"hour_\" not in columnTypes[\"Column\"].unique()\n", "        ):\n", "            column_dtypes.append(\n", "                {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Description for hour_\"}\n", "            )\n", "    else:\n", "        sql = f\"select * from {environ}.{sourceTable}\"\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "        \"partition_key\": partition_key,\n", "    }\n", "    print(\"kwargs\", kwargs)\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=sql, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error createLogTable (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to createLogTable after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import gc\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "\n", "    query = f\"describe {environ}.{tableName}\"\n", "    columnTypes = fetchDataFromDB(query)\n", "    print(\"columnTypes\", columnTypes.shape)\n", "    if columnTypes.shape[0] > 0:\n", "        columnTypes = columnTypes[[\"Column\", \"Type\"]]\n", "        current_dtypes = df.dtypes\n", "        for index, row in columnTypes.iterrows():\n", "            column = row[\"Column\"]\n", "            dtype = row[\"Type\"]\n", "            if column in df.columns:\n", "                if \"varchar\" in dtype or \"char\" in dtype:\n", "                    if current_dtypes[column] != \"object\":\n", "                        df[column] = df[column].astype(\n", "                            \"object\"\n", "                        )  # Pandas doesn't have a direct char type, using object instead\n", "                elif \"integer\" in dtype or \"bigint\" in dtype or \"int\" in dtype:\n", "                    if current_dtypes[column] not in [\"int64\", \"int32\"]:\n", "                        df[column] = pd.to_numeric(df[column], errors=\"coerce\", downcast=\"integer\")\n", "                elif \"double\" in dtype or \"float\" in dtype or \"real\" in dtype:\n", "                    if current_dtypes[column] not in [\"float64\", \"float32\"]:\n", "                        df[column] = pd.to_numeric(df[column], errors=\"coerce\", downcast=\"float\")\n", "                elif \"timestamp\" in dtype:\n", "                    if current_dtypes[column] != \"datetime64[ns]\":\n", "                        df[column] = pd.to_datetime(df[column])\n", "                gc.collect()\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "    print(\"kwargs\", kwargs)\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return\n", "\n", "\n", "def getCityClusterMapping():\n", "    query = \"\"\"\n", "    SELECT m.city_id,\n", "       c.cluster_id,\n", "       c.cluster_name\n", "FROM rpc.ams_city_cluster_mapping m\n", "INNER JOIN rpc.ams_cluster c ON c.cluster_id = m.cluster_id\n", "AND c.cluster_id > 15000\n", "AND m.lake_active_record = TRUE\n", "AND m.active = TRUE\n", "AND c.lake_active_record = TRUE\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"city_cluster_mapping_df.csv\")\n", "    return df\n", "\n", "\n", "def getEventNames():\n", "    query = \"\"\"\n", "    SELECT sei.id AS id,\n", "       CONCAT(name, '_', cast(start_date AS varchar)) AS event_name,\n", "       start_date as event_start_date,\n", "       end_date as event_end_date\n", "FROM rpc.supply_event_info sei\n", "JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "WHERE sei.active = TRUE\n", "  AND se.active = TRUE\n", "  AND end_date + interval '40' DAY >= CURRENT_DATE \n", "  AND start_date - interval '100' DAY <= CURRENT_DATE \n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"event_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchPlannerData():\n", "    query = \"\"\"\n", "        SELECT\n", "  location_id,\n", "  event_id,\n", "  item_id,\n", "  sale_start_date,\n", "  sale_end_date,\n", "  cut_off_date,\n", "  assortment_type,\n", "  assortment_launch_type,\n", "  substitute_ptype,\n", "  planned_qty,\n", "  location_type\n", "FROM\n", "  ars.bulk_process_event_planner bpep\n", "WHERE\n", "  partition_field IS NOT NULL\n", "  AND active = 1\n", "  AND sale_end_date + interval '30' DAY >= CURRENT_DATE \n", "  AND sale_start_date - interval '100' DAY <= CURRENT_DATE \n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"planned_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchConfiguredOutletItemDistribution(events):\n", "    event_ids = \",\".join(map(str, events[\"id\"].unique()))\n", "    query = f\"\"\"\n", "    SELECT *\n", "FROM ars_etls.festival_store_distribution_multi_ptype_active_v1\n", "WHERE festival_name IS NOT NULL\n", "  AND lake_active_record\n", "  and event_id in ({event_ids})\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"configured_distribution_df.csv\")\n", "    return df\n", "\n", "\n", "def expressAndLtTeaMappings():\n", "    query = \"\"\"\n", "    WITH fo AS (\n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.name AS city_name,\n", "    rcs.name AS state,\n", "    polygon_type\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.company_type_id NOT IN (771, 767)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "    INNER JOIN (\n", "      SELECT\n", "        cast(merchant_id AS int) merchant_id,\n", "        CASE\n", "          WHEN TYPE = 'STORE_POLYGON' THEN 'EXPRESS'\n", "          ELSE 'LONGTAIL'\n", "        END AS polygon_type\n", "      FROM\n", "        serviceability.ser_store_polygons\n", "      WHERE\n", "        TYPE IN (\n", "          'STORE_POLYGON',\n", "          'LONGTAIL_POLYGON'\n", "        )\n", "        AND is_active = TRUE\n", "        AND lake_active_record\n", "    ) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.outlet_name NOT LIKE '%%Draft%%'\n", "    AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "    AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "    AND om.facility_id != 273\n", "    AND om.outlet_id IN (\n", "      SELECT\n", "        DISTINCT outlet_id\n", "      FROM\n", "        po.bulk_facility_outlet_mapping\n", "      WHERE\n", "        active\n", "        AND lake_active_record\n", "    )\n", "),\n", "pbo AS (\n", "  WITH bo AS (\n", "    SELECT\n", "      om.facility_id,\n", "      om.outlet_id AS outlet_id,\n", "      om.outlet_name AS outlet_name,\n", "      rcl.name AS city_name,\n", "      rcs.name AS state,\n", "      wom.cloud_store_id AS inv_outlet_id\n", "    FROM\n", "      po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id != 7\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      INNER JOIN (\n", "        SELECT\n", "          DISTINCT warehouse_id,\n", "          cloud_store_id\n", "        FROM\n", "          retail.warehouse_outlet_mapping\n", "        WHERE\n", "          active = 1\n", "          AND lake_active_record\n", "      ) wom ON wom.warehouse_id = om.outlet_id\n", "    WHERE\n", "      om.active = 1\n", "      AND om.ars_active = 1\n", "      AND om.lake_active_record\n", "      AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "      AND om.facility_id != 273\n", "  )\n", "  SELECT\n", "    *\n", "  FROM\n", "    bo\n", "  WHERE\n", "    outlet_id IN (\n", "      SELECT\n", "        DISTINCT backend_outlet_id\n", "      FROM\n", "        rpc.transfer_tag_rules\n", "      WHERE\n", "        active\n", "        AND lake_active_record\n", "        AND category = 'GROCERY'\n", "    )\n", "),\n", "slots AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    ars.backend_slot\n", "  WHERE\n", "    lake_active_record\n", "    AND active\n", "    AND MODE = 'normal'\n", "),\n", "slot_store AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    ars.auto_sto_slot\n", "  WHERE\n", "    lake_active_record\n", "    AND active\n", "),\n", "slot_mapping AS (\n", "  SELECT\n", "    DISTINCT backend_facility_id,\n", "    frontend_outlet_id\n", "  FROM\n", "    slots s\n", "    INNER JOIN slot_store ss ON ss.backend_slot_id = s.id\n", "),\n", "lt_stores as (\n", "  SELECT\n", "    distinct long_tail_facility_id,\n", "    'LT' as lt_flag\n", "  FROM\n", "    po.long_tail_facility_store_mapping\n", "  WHERE\n", "    active = 1\n", "    AND lake_active_record\n", "),\n", "base as (\n", "  SELECT\n", "    pbo.outlet_id AS be_outlet_id,\n", "    pbo.facility_id AS be_facility_id,\n", "    pbo.inv_outlet_id AS be_inv_outlet_id,\n", "    pbo.outlet_name AS be_name,\n", "    fo.facility_id,\n", "    fo.outlet_id,\n", "    fo.outlet_name AS fe_name,\n", "    fo.city_name AS fe_city_name,\n", "    assortment_type,\n", "    count(pfma.item_id) as num_items\n", "  FROM\n", "    rpc.product_facility_master_assortment pfma\n", "    INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "    AND fo.polygon_type = pfma.assortment_type\n", "    INNER JOIN rpc.item_outlet_tag_mapping iotm ON iotm.tag_type_id = 8\n", "    AND iotm.active = 1\n", "    AND iotm.lake_active_record\n", "    AND iotm.outlet_id = fo.outlet_id\n", "    AND iotm.item_id = pfma.item_id\n", "    INNER JOIN pbo ON coalesce(try_cast(iotm.tag_value AS int), 0) = pbo.outlet_id\n", "    INNER JOIN slot_mapping ON slot_mapping.backend_facility_id = pbo.facility_id\n", "    AND slot_mapping.frontend_outlet_id = fo.outlet_id\n", "    INNER JOIN po.bulk_facility_outlet_mapping bfom ON bfom.facility_id = pbo.facility_id\n", "    AND bfom.outlet_id = fo.outlet_id\n", "    AND bfom.active\n", "    AND bfom.lake_active_record\n", "  WHERE\n", "    pfma.lake_active_record\n", "    AND pfma.active = 1\n", "    AND pfma.master_assortment_substate_id IN (1, 3)\n", "  group by\n", "    1,\n", "    2,\n", "    3,\n", "    4,\n", "    5,\n", "    6,\n", "    7,\n", "    8,\n", "    9\n", "),\n", "base_rank as (\n", "  select\n", "    base.*,\n", "    rank() over (\n", "      partition by outlet_id,\n", "      assortment_type\n", "      order by\n", "        num_items desc\n", "    ) as rank_\n", "  from\n", "    base\n", ")\n", "select\n", "  base_rank.*\n", "from\n", "  base_rank\n", "  LEFT JOIN lt_stores on lt_stores.long_tail_facility_id = base_rank.facility_id\n", "where\n", "  rank_ = 1\n", "  and (\n", "    assortment_type = 'EXPRESS'\n", "    or (assortment_type = 'LONGTAIL'\n", "    and lt_flag is not null)\n", "  )\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"express_lt_tea_map_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchBeItemPoMetrics(planner_df):\n", "    item_ids = planner_df.item_id.unique()\n", "    item_ids = \",\".join(map(str, item_ids))\n", "    query = f\"\"\"\n", "    with bo as (\n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.name AS city_name,\n", "    rcs.name AS state,\n", "    wom.cloud_store_id AS inv_outlet_id\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id != 7\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "    INNER JOIN (\n", "      SELECT\n", "        DISTINCT warehouse_id,\n", "        cloud_store_id\n", "      FROM\n", "        retail.warehouse_outlet_mapping\n", "      WHERE\n", "        active = 1\n", "        AND lake_active_record\n", "    ) wom ON wom.warehouse_id = om.outlet_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.facility_id != 273\n", "),\n", "block_invt AS (\n", "  SELECT\n", "    outlet_id,\n", "    item_id,\n", "    SUM(quantity) AS blocked_inventory\n", "  FROM\n", "    ims.ims_item_blocked_inventory\n", "  WHERE\n", "    lake_active_record = true\n", "    AND active = 1\n", "    and quantity > 0\n", "  GROUP BY\n", "    1,\n", "    2\n", "),\n", "inventory AS (\n", "  SELECT\n", "    i.outlet_id,\n", "    i.item_id,\n", "    SUM(\n", "      CASE\n", "        WHEN i.quantity > 0 THEN i.quantity\n", "        ELSE 0\n", "      END\n", "    ) AS onshelf_invt,\n", "    SUM(b.blocked_inventory) AS block_invt,\n", "    SUM(\n", "      CASE\n", "        WHEN (\n", "          i.quantity - coalesce(b.blocked_inventory, 0) > 0\n", "        ) THEN (i.quantity - coalesce(b.blocked_inventory, 0))\n", "        ELSE 0\n", "      END\n", "    ) AS net_inv\n", "  FROM\n", "    ims.ims_item_inventory i\n", "    LEFT JOIN block_invt b ON i.item_id = b.item_id\n", "    AND i.outlet_id = b.outlet_id\n", "  WHERE\n", "    i.active = 1\n", "    and i.lake_active_record = true\n", "  GROUP BY\n", "    1,\n", "    2\n", "),\n", "open_pos AS (\n", "  SELECT\n", "  po.outlet_id,\n", "  poi.item_id,\n", "  sum(poi.units_ordered) AS open_po,\n", "  sum(\n", "    case\n", "      when date(ps.schedule_date_time + interval '330' MINUTE) = CURRENT_DATE then poi.units_ordered\n", "      else 0\n", "    end\n", "  ) as scheduled_today,\n", "  sum(\n", "    case\n", "      when date(ps.schedule_date_time + interval '330' MINUTE) = CURRENT_DATE + interval '1' day then poi.units_ordered\n", "      else 0\n", "    end\n", "  ) as scheduled_t1,\n", "  sum(\n", "    case\n", "      when date(ps.schedule_date_time + interval '330' MINUTE) = CURRENT_DATE + interval '2' day then poi.units_ordered\n", "      else 0\n", "    end\n", "  ) as scheduled_t2,\n", "  sum(\n", "    case\n", "      when date(ps.schedule_date_time + interval '330' MINUTE) = CURRENT_DATE + interval '3' day then poi.units_ordered\n", "      else 0\n", "    end\n", "  ) as scheduled_t3,\n", "  sum(\n", "    case\n", "      when date(ps.schedule_date_time + interval '330' MINUTE) > CURRENT_DATE + interval '3' day then poi.units_ordered\n", "      else 0\n", "    end\n", "  ) as scheduled_future,\n", "  sum(\n", "    case\n", "      when ps.schedule_date_time is null then poi.units_ordered\n", "      else 0\n", "    end\n", "  ) as unscheduled\n", "FROM\n", "  po.purchase_order_items poi\n", "  INNER JOIN po.purchase_order po ON po.id = poi.po_id\n", "  AND po.active = 1\n", "  AND po.lake_active_record\n", "  LEFT JOIN po.po_schedule ps ON ps.po_id_id = po.id\n", "  AND ps.active = 1\n", "  AND ps.lake_active_record\n", "  AND (ps.schedule_date_time + interval '330' MINUTE) >= CURRENT_DATE\n", "  INNER JOIN po.purchase_order_status posa ON posa.po_id = po.id\n", "  INNER JOIN po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "WHERE\n", "  poi.remaining_quantity != 0\n", "  AND poi.active = 1\n", "  AND poi.lake_active_record\n", "  AND po.expiry_date + INTERVAL '330' MINUTE + interval '2' day >= CURRENT_DATE -- consider Open POs till expiry date + 2 days\n", "  and posta.name not in ('Cancelled post Creation', 'Rejected')\n", "GROUP BY\n", "  1,\n", "  2\n", ")\n", "select\n", "  bo.outlet_id,\n", "  inv.item_id,\n", "  inv.net_inv as quantity,\n", "  coalesce(op.open_po, 0) as open_po,\n", "  coalesce(op.scheduled_today, 0) as scheduled_today,\n", "  coalesce(op.scheduled_t1, 0) as scheduled_t1,\n", "  coalesce(op.scheduled_t2, 0) as scheduled_t2,\n", "  coalesce(op.scheduled_t3, 0) as scheduled_t3,\n", "  coalesce(op.scheduled_future, 0) as scheduled_future,\n", "  coalesce(op.unscheduled, 0) as unscheduled\n", "from\n", "  inventory inv\n", "  INNER JOIN bo on bo.inv_outlet_id = inv.outlet_id\n", "  LEFT JOIN open_pos op on op.item_id = inv.item_id\n", "  and op.outlet_id = bo.outlet_id\n", "Where\n", "  inv.item_id in ({item_ids})\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"be_item_po_metrics_df.csv\")\n", "    return df\n", "\n", "\n", "def getOrderingTea():\n", "    query = \"\"\"\n", "    SELECT outlet_id,\n", "       festival_name,\n", "       proposed_be_facility_id AS be_facility_id\n", "FROM supply_etls.festival_tea_tagging_v2\n", "WHERE active = 1\n", "  AND festival_name IS NOT NULL\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"ordering_tea_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchDefaultDistribution():\n", "    query = \"\"\"\n", "    WITH fo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          mo_map.frontend_merchant_id AS merchant_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id IN (7)\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "   AND rco.business_type_id = 7\n", "   AND is_current\n", "   AND is_current_mapping_active\n", "   AND is_backend_merchant_active\n", "   AND is_frontend_merchant_active\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.facility_id != 273 )\n", "SELECT fo.outlet_id,\n", "       fo.facility_id,\n", "       fo.outlet_name,\n", "       sum(multiplier * product_quantity) AS quantity \n", "FROM dwh.fact_sales_order_item_details ord\n", "INNER JOIN fo ON fo.outlet_id = ord.outlet_id\n", "INNER JOIN dwh.dim_item_product_offer_mapping ipo ON ipo.product_id = ord.product_id\n", "AND is_current\n", "WHERE order_create_dt_ist BETWEEN CURRENT_DATE - interval '5' DAY AND CURRENT_DATE\n", "  AND order_current_status = 'DELIVERED'\n", "  AND is_internal_order = FALSE\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"default_distribution_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchItemOutletSalesData(planner_df):\n", "    item_ids = planner_df.item_id.unique()\n", "    item_ids = \",\".join(map(str, item_ids))\n", "    query = f\"\"\"\n", "    SELECT\n", "  order_create_dt_ist,\n", "  outlet_id,\n", "  ipo.item_id,\n", "  sum(ord.product_quantity * ipo.multiplier) AS quantity\n", "FROM\n", "  dwh.fact_sales_order_item_details AS ord\n", "  INNER JOIN dwh.dim_item_product_offer_mapping ipo ON ipo.product_id = ord.product_id\n", "  AND ipo.is_current and ipo.item_id in ({item_ids})\n", "WHERE\n", "  order_current_status = 'DELIVERED'\n", "  AND is_internal_order = FALSE\n", "  AND order_create_dt_ist >= CURRENT_DATE - interval '2' day\n", "  AND order_create_dt_ist <= CURRENT_DATE\n", "GROUP BY\n", "  1,\n", "  2,\n", "  3\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"item_outlet_sales_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchItemOutletSalesDataSoFar(planner_df):\n", "    event_ids = planner_df.event_id.unique()\n", "    event_ids = \",\".join(map(str, event_ids))\n", "    query = f\"\"\"\n", "    with base as (\n", "  SELECT\n", "    distinct event_id,\n", "    item_id,\n", "    date(sale_start_date) as sale_start_date\n", "  FROM\n", "    ars.bulk_process_event_planner bpep\n", "  WHERE\n", "    partition_field IS NOT NULL\n", "    AND active = 1\n", "    AND event_id in ({event_ids})\n", "),\n", "sales as (\n", "  SELECT\n", "    order_create_dt_ist,\n", "    outlet_id,\n", "    ipo.item_id,\n", "    sum(ord.product_quantity * ipo.multiplier) AS quantity\n", "  FROM\n", "    dwh.fact_sales_order_item_details AS ord\n", "    INNER JOIN dwh.dim_item_product_offer_mapping ipo ON ipo.product_id = ord.product_id\n", "    AND ipo.is_current\n", "    and ipo.item_id in (\n", "      select\n", "        distinct item_id\n", "      from\n", "        base\n", "    )\n", "  WHERE\n", "    order_current_status = 'DELIVERED'\n", "    AND is_internal_order = FALSE\n", "    AND order_create_dt_ist >= (\n", "      select\n", "        min(sale_start_date) as sale_start_date\n", "      from\n", "        base\n", "    )\n", "    and order_create_dt_ist <= current_date\n", "  GROUP BY\n", "    1,\n", "    2,\n", "    3\n", ")\n", "select\n", "  b.event_id,\n", "  b.item_id,\n", "  s.outlet_id,\n", "  b.sale_start_date,\n", "  sum(\n", "    case\n", "      when order_create_dt_ist >= sale_start_date then coalesce(quantity, 0)\n", "      else 0\n", "    end\n", "  ) as sales_so_far\n", "from\n", "  base b\n", "  left join sales s on b.item_id = s.item_id\n", "where s.outlet_id is not null\n", "group by\n", "  1,\n", "  2,\n", "  3,\n", "  4\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"item_outlet_sales_so_far_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchLiveOutletItemLevelSnapshot(planner_df):\n", "    item_ids = planner_df.item_id.unique()\n", "    item_ids = \",\".join(map(str, item_ids))\n", "    query = f\"\"\"\n", "  WITH fo AS (\n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    mo_map.frontend_merchant_id AS merchant_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.id AS city_id,\n", "    rcl.name AS city_name,\n", "    rcs.name AS state\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id IN (7)\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "    AND rco.business_type_id = 7\n", "    AND is_current\n", "    AND is_current_mapping_active\n", "    AND is_backend_merchant_active\n", "    AND is_frontend_merchant_active\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.facility_id != 273\n", "),\n", "bo AS (\n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.name AS city_name,\n", "    rcs.name AS state,\n", "    wom.cloud_store_id AS inv_outlet_id\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id != 7\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "    INNER JOIN (\n", "      SELECT\n", "        DISTINCT warehouse_id,\n", "        cloud_store_id\n", "      FROM\n", "        retail.warehouse_outlet_mapping\n", "      WHERE\n", "        active = 1\n", "        AND lake_active_record\n", "    ) wom ON wom.warehouse_id = om.outlet_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.facility_id != 273\n", "),\n", "cpd_base AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    (\n", "      SELECT\n", "        cpd.*,\n", "        rank() OVER (\n", "          PARTITION BY cpd.item_id,\n", "          cpd.outlet_id\n", "          ORDER BY\n", "            cpd.updated_at DESC\n", "        ) AS cpd_rank\n", "      FROM\n", "        ars.outlet_item_aps_derived_cpd cpd\n", "        INNER JOIN fo ON fo.outlet_id = cpd.outlet_id\n", "      WHERE\n", "        created_at >= CURRENT_DATE - interval '2' DAY\n", "        AND insert_ds_ist >= cast(CURRENT_DATE - interval '2' DAY AS varchar)\n", "        AND aps_adjusted > 0\n", "        AND lake_active_record\n", "    ) AS x\n", "  WHERE\n", "    cpd_rank = 1\n", "),\n", "inventory_fe_all as(\n", "    select try_cast(item_id as int) as item_id, \n", "        try_cast(outlet_id as int) as outlet_id, \n", "        sum(quantity) as quantity\n", "    from dynamodb.blinkit_store_inventory_service_oi_rt_view_v2\n", "    where quantity > 0 \n", "        and state = 'GOOD' \n", "    group by 1,2\n", "),\n", "inventory_fe_blocked as(\n", "    select try_cast(item_id as int) as item_id, \n", "        try_cast(outlet_id as int) as outlet_id,\n", "        sum(quantity) as blocked_inv\n", "    from dynamodb.blinkit_store_inventory_service_blk_rt_view_v2\n", "    where status = 'BLOCKED'\n", "        and reference_type not in ('DL_VALIDATION_CRON', 'PNA')\n", "        and quantity > 0\n", "    group by 1, 2\n", "),\n", "inventory as(\n", "    select ifa.item_id,\n", "        ifa.outlet_id,\n", "        greatest(ifa.quantity - coalesce(ifb.blocked_inv, 0), 0) as net_inv  \n", "    from inventory_fe_all ifa  \n", "    left join inventory_fe_blocked ifb on ifb.item_id = ifa.item_id\n", "        and ifb.outlet_id = ifa.outlet_id\n", "),\n", " hybrid_city_item_combinations AS\n", "  (SELECT DISTINCT fo.city_id,\n", "                   pfma.item_id,\n", "                   TRUE as hybrid_flag\n", "   FROM rpc.product_facility_master_assortment pfma\n", "   INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "   WHERE json_extract_scalar(meta, '$.launch_type') = 'HYBRID'\n", "     AND master_assortment_substate_id IN (1,\n", "                                           3)\n", "     AND lake_active_record\n", "     AND active = 1 ),\n", "base AS (\n", "  SELECT\n", "    fo.outlet_id,\n", "    fo.facility_id,\n", "    coalesce(try_cast(iotm.tag_value AS int), 0) as be_outlet_id,\n", "    pfma.item_id,\n", "    pfma.master_assortment_substate_id,\n", "    pfma.assortment_type,\n", "    coalesce(cpd.aps_adjusted, 0) AS aps_cpd,\n", "    coalesce(cpd.cpd, 0) AS cpd,\n", "    coalesce(ims_fe.net_inv, 0) AS fe_inv,\n", "    coalesce(hcic.hybrid_flag, FALSE) as hybrid_flag\n", "  FROM\n", "    rpc.product_facility_master_assortment pfma\n", "    INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "    INNER JOIN rpc.item_outlet_tag_mapping iotm ON iotm.tag_type_id = 8\n", "    AND iotm.active = 1\n", "    AND iotm.lake_active_record\n", "    AND iotm.outlet_id = fo.outlet_id\n", "    AND iotm.item_id = pfma.item_id\n", "    LEFT JOIN cpd_base cpd ON cpd.item_id = pfma.item_id\n", "    AND cpd.outlet_id = fo.outlet_id\n", "    LEFT JOIN inventory ims_fe ON ims_fe.outlet_id = fo.outlet_id\n", "    AND ims_fe.item_id = pfma.item_id\n", "    AND ims_fe.net_inv > 0\n", "    LEFT JOIN hybrid_city_item_combinations hcic on hcic.item_id = pfma.item_id and hcic.city_id = fo.city_id\n", "  WHERE\n", "    pfma.lake_active_record\n", "    AND pfma.active = 1\n", "    AND pfma.master_assortment_substate_id IN (1, 2, 3)\n", "    and (\n", "      pfma.master_assortment_substate_id in (1, 3)\n", "      or coalesce(ims_fe.net_inv, 0) > 0\n", "    )\n", "    and pfma.item_id in ({item_ids})\n", ")\n", "SELECT\n", "  *\n", "FROM\n", "  base\n", "  \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"outlet_item_live_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchFrontEndOutletDetails():\n", "    # https://redash-queries.grofer.io/queries/332215\n", "    query = \"\"\"\n", "        WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.id as city_id,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.company_type_id NOT IN (771, 767)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      INNER JOIN\n", "        (SELECT distinct cast(merchant_id AS int) merchant_id\n", "          FROM serviceability.ser_store_polygons\n", "          WHERE TYPE in ('STORE_POLYGON','LONGTAIL_POLYGON')\n", "            AND is_active = TRUE\n", "            AND lake_active_record) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.outlet_name NOT LIKE '%%Draft%%'\n", "        AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "        AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "        AND om.facility_id != 273\n", "        AND om.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "            FROM po.bulk_facility_outlet_mapping\n", "            WHERE active\n", "              AND lake_active_record) )\n", "    SELECT *\n", "    FROM fo\n", "    \"\"\"\n", "    # 273 is a test dark store mapped to multiple merchant ids leading to duplicate rows\n", "    # print(query)\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"fo_df.csv\")\n", "    return df\n", "\n", "\n", "def fetchPackagedItemUniverse():\n", "    query = \"\"\"\n", " SELECT *\n", "FROM\n", "  (SELECT icd.item_id,\n", "          icd.name,\n", "          icd.l0_id,\n", "          icd.l0,\n", "          icd.l1_id,\n", "          icd.l1,\n", "          icd.l2_id,\n", "          icd.l2,\n", "          icd.product_type_id,\n", "          icd.product_type,\n", "          CASE\n", "              WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "              ELSE 'PACKAGED'\n", "          END AS item_type,\n", "          id.storage_type AS storage_type_raw,\n", "          CASE\n", "              WHEN id.storage_type IN ('1',\n", "                                       '8',\n", "                                       '11') THEN 'REGULAR'\n", "              WHEN id.storage_type IN ('4',\n", "                                       '5') THEN 'HEAVY'\n", "              WHEN id.storage_type IN ('2',\n", "                                       '6') THEN 'COLD'\n", "              WHEN id.storage_type IN ('3',\n", "                                       '7') THEN 'FROZEN'\n", "              ELSE 'REGULAR'\n", "          END AS storage_type,\n", "          CASE\n", "              WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "              WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "              ELSE 'REGULAR'\n", "          END AS handling_type,\n", "          id.variant_mrp AS mrp,\n", "          id.variant_description,\n", "          id.weight_in_gm,\n", "          id.length_in_cm,\n", "          id.height_in_cm,\n", "          id.breadth_in_cm,\n", "          id.shelf_life,\n", "          coalesce(itf.item_factor, 0.01) AS item_factor,\n", "          DATE_DIFF('day',date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "          rank() OVER (PARTITION BY id.item_id\n", "                       ORDER BY id.id DESC) AS variant_rank,\n", "                      itm.tag_value AS custom_storage_type_raw,\n", "                      CASE\n", "                          WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                          WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                          WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                          WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                          WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                          WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                          WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                          WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                          WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                          WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                          WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                          WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                          WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                          WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                          WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                          WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                          WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                          WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                          WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                          WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                          ELSE 'UNKNOWN_CONFIG'\n", "                      END AS custom_storage_type,\n", "                      pb.manufacturer_id,\n", "                      id.manufacturer AS manufacturer_name,\n", "                      pb.name AS brand_name,\n", "                      coalesce(id.outer_case_size,1) AS outer_case_size,\n", "                      coalesce(id.inner_case_size,1) AS inner_case_size,\n", "                      CASE\n", "                          WHEN itm2.tag_value = '1' THEN TRUE\n", "                          ELSE FALSE\n", "                      END AS is_high_value,\n", "                      itm2.tag_value AS high_value_tag_raw,\n", "                      CASE\n", "                          WHEN itm3.tag_value = '1' THEN 'upc scan'\n", "                          WHEN itm3.tag_value = '2' THEN 'serial scan'\n", "                          WHEN itm3.tag_value = '3' THEN 'qr scan'\n", "                          WHEN itm3.tag_value = '4' THEN 'no scan'\n", "                          ELSE 'unknown'\n", "                      END AS scan_type,\n", "                      itm3.tag_value AS scan_type_raw\n", "   FROM rpc.item_category_details icd\n", "   INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "   AND id.active = 1\n", "   AND id.approved = 1\n", "   AND id.lake_active_record\n", "   LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "   LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "   AND itm.active = TRUE\n", "   AND itm.lake_active_record\n", "   AND itm.item_id = icd.item_id\n", "   LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "   AND pb.lake_active_record\n", "   AND pb.active = 1\n", "   LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "   AND itm2.active\n", "   AND itm2.tag_type_id = 3\n", "   AND itm2.lake_active_record\n", "   LEFT JOIN rpc.item_tag_mapping itm3 ON itm3.item_id = icd.item_id\n", "   AND itm3.active\n", "   AND itm3.tag_type_id = 5\n", "   AND itm3.lake_active_record\n", "   WHERE icd.lake_active_record\n", "     AND perishable != 1\n", "     AND id.handling_type != '8'\n", "--     AND id.storage_type NOT IN ('3',\n", "--                                 '7')\n", "     AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "\n", "     AND icd.l0 NOT IN ('wholesale store',\n", "                        'Trial new tree',\n", "                        'Specials')-- removing test and flyer/freebie l0s\n", " ) AS x\n", "WHERE variant_rank = 1\n", "\"\"\"\n", "    # print(query)\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"catalog_df.csv\")\n", "    return df\n", "\n", "\n", "def getOpenStoDetails(planned_df):\n", "    item_ids = planned_df.item_id.unique()\n", "    item_ids = \",\".join(map(str, item_ids))\n", "    query = f\"\"\"\n", "  WITH sto_info AS (\n", "SELECT  id, frontend_outlet_id, DATE(created_at) AS sto_date \n", "FROM    po.sto\n", "WHERE   active = 1 AND lake_active_record = true \n", "        AND created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '14' DAY - INTERVAL '330' MINUTE) ),\n", "\n", "sto_items_info AS (\n", "SELECT  run_id, sto_id, item_id, name AS item_name, expected_quantity AS sto, reserved_quantity\n", "FROM    po.sto_items\n", "WHERE   active = 1 AND lake_active_record = true AND item_id in ({item_ids})\n", "        AND created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '14' DAY - INTERVAL '330' MINUTE) ),\n", "\n", "ims_item_info AS (\n", "SELECT  sto_id, item_id, expected_quantity AS sto, reserved_quantity, billed_quantity, inward_quantity\n", "        , released_reserved_quantity, released_billed_quantity\n", "FROM    ims.ims_sto_item \n", "WHERE   lake_active_record = true AND item_id in ({item_ids})\n", "        AND created_at >= (CAST(CURRENT_DATE AS TIMESTAMP) - INTERVAL '14' DAY - INTERVAL '330' MINUTE) )\n", "\n", "SELECT  s.frontend_outlet_id as outlet_id, si.item_id, SUM(si.sto) AS sto\n", "        , SUM(si.reserved_quantity) AS reserved_qty, SUM(ii.billed_quantity) AS billed_qty, SUM(ii.inward_quantity) AS inward_qty\n", "        , SUM(ii.released_reserved_quantity) AS released_reserved_quantity, SUM(ii.released_billed_quantity) AS released_billed_quantity\n", "\n", "FROM        sto_info s\n", "INNER JOIN  sto_items_info si ON s.id = si.sto_id \n", "LEFT JOIN   ims_item_info ii ON ii.sto_id = si.sto_id AND ii.item_id = si.item_id\n", "LEFT JOIN   retail.console_outlet o ON o.id = s.frontend_outlet_id AND o.active = 1 AND o.lake_active_record = true\n", "GROUP BY    1,2\n", "  \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"open_sto_details_df.csv\")\n", "    return df\n", "\n", "\n", "def getOpenB2bDetails(planned_df):\n", "    item_ids = planned_df.item_id.unique()\n", "    item_ids = \",\".join(map(str, item_ids))\n", "    query = f\"\"\"\n", "  WITH bo AS (\n", "  SELECT\n", "    om.facility_id,\n", "    om.outlet_id AS outlet_id,\n", "    om.outlet_name AS outlet_name,\n", "    rcl.name AS city_name,\n", "    rcs.name AS state,\n", "    wom.cloud_store_id AS inv_outlet_id\n", "  FROM\n", "    po.physical_facility_outlet_mapping om\n", "    INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "    AND rco.business_type_id != 7\n", "    AND rco.lake_active_record\n", "    INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "    AND rcl.lake_active_record\n", "    INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "    AND rcs.lake_active_record\n", "    INNER JOIN (\n", "      SELECT\n", "        DISTINCT warehouse_id,\n", "        cloud_store_id\n", "      FROM\n", "        retail.warehouse_outlet_mapping\n", "      WHERE\n", "        active = 1\n", "        AND lake_active_record\n", "    ) wom ON wom.warehouse_id = om.outlet_id\n", "  WHERE\n", "    om.active = 1\n", "    AND om.ars_active = 1\n", "    AND om.lake_active_record\n", "    AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "    AND om.facility_id != 273\n", "),\n", "sto_items as (\n", "  select\n", "    *\n", "  from\n", "    ims.ims_sto_item\n", "  where\n", "    created_at > CURRENT_DATE - interval '10' day\n", "    and item_id in ({ item_ids })\n", "),\n", "sto_details as (\n", "  select\n", "    *\n", "  from\n", "    ims.ims_sto_details\n", "  where\n", "    created_at > CURRENT_DATE - interval '10' day\n", "    and sto_state IN (1, 2, 5)\n", "),\n", "final as (\n", "  SELECT\n", "    o1.facility_id as sender_be_facility_id,\n", "    o1.outlet_id as sender_be_outlet_id,\n", "    o1.outlet_name as mother_be_name,\n", "    o2.facility_id as child_be_facility_id,\n", "    o2.outlet_id as child_be_outlet_id,\n", "    o2.outlet_name as child_be_name,\n", "    isi.item_id,\n", "    sum(\n", "      reserved_quantity - inward_quantity - released_billed_quantity - released_reserved_quantity\n", "    ) AS open_b2b_transfer\n", "  FROM\n", "    sto_items isi\n", "    INNER JOIN sto_details s ON isi.sto_id = s.sto_id\n", "    INNER JOIN bo o1 on o1.inv_outlet_id = s.outlet_id\n", "    INNER JOIN bo o2 on o2.inv_outlet_id = s.merchant_outlet_id\n", "    group by 1,2,3,4,5,6,7\n", ")\n", "select\n", "  *\n", "from\n", "  final\n", "  where open_b2b_transfer > 0\n", "  \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"open_b2b_details_df.csv\")\n", "    return df\n", "\n", "\n", "def getStoreOverlaps():\n", "    query = \"\"\"SELECT longtail_outlet_id,\n", "                express_outlet_id,\n", "                perc_area_covered_of_express_merchant\n", "            FROM consumer_etls.longtail_express_merchant_mapping\n", "            WHERE is_valid = 1\n", "            and longtail_facility_id != express_facility_id\n", "            \"\"\"\n", "    # print('getStoreCoverage ',query)\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"overlaps_df.csv\")\n", "    return df\n", "\n", "\n", "def getHybridOutletsARSData():\n", "    query = \"\"\"\n", "  WITH fo AS\n", "      (SELECT om.facility_id,\n", "              om.outlet_id AS outlet_id,\n", "              mo_map.frontend_merchant_id AS merchant_id,\n", "              om.outlet_name AS outlet_name,\n", "              rcl.name AS city_name,\n", "              rcs.name AS state\n", "      FROM po.physical_facility_outlet_mapping om\n", "      INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "      AND rco.business_type_id IN (7)\n", "      AND rco.company_type_id NOT IN (771, 767)\n", "      AND rco.lake_active_record\n", "      INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "      AND rcl.lake_active_record\n", "      INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "      AND rco.business_type_id = 7\n", "      AND is_current\n", "      AND is_current_mapping_active\n", "      AND is_backend_merchant_active\n", "      AND is_frontend_merchant_active\n", "      INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "      AND rcs.lake_active_record\n", "      INNER JOIN\n", "        (SELECT distinct cast(merchant_id AS int) merchant_id\n", "          FROM serviceability.ser_store_polygons\n", "          WHERE TYPE in ('STORE_POLYGON','LONGTAIL_POLYGON')\n", "            AND is_active = TRUE\n", "            AND lake_active_record) poly ON poly.merchant_id = mo_map.frontend_merchant_id\n", "      WHERE om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.outlet_name NOT LIKE '%%Draft%%'\n", "        AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "        AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "        AND om.facility_id != 273\n", "        AND om.outlet_id IN\n", "          (SELECT DISTINCT outlet_id\n", "            FROM po.bulk_facility_outlet_mapping\n", "            WHERE active\n", "              AND lake_active_record) ),\n", "     coverage AS\n", "  (SELECT long_tail_facility_id,\n", "          adjacent_facility_id\n", "   FROM po.long_tail_facility_store_mapping\n", "   WHERE active = 1\n", "     AND lake_active_record),\n", "     base AS\n", "  (SELECT *,\n", "          'express_stores' AS tagging\n", "   FROM fo\n", "   WHERE facility_id NOT IN\n", "       (SELECT adjacent_facility_id\n", "        FROM coverage)\n", "     AND facility_id NOT IN\n", "       (SELECT long_tail_facility_id\n", "        FROM coverage)\n", "   UNION ALL SELECT *,\n", "                    'longtail_stores' AS tagging\n", "   FROM fo\n", "   WHERE facility_id IN\n", "       (SELECT long_tail_facility_id\n", "        FROM coverage) )\n", "SELECT *\n", "FROM base\n", "  \"\"\"\n", "    # print(query)\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"hybrid_outlets.csv\")\n", "    return df\n", "\n", "\n", "def distributeRatios(express_distribution, store_footprint_df, overlaps_df_org, group_cols=None):\n", "    \"\"\"\n", "    Efficiently distributes ratios using fully vectorized operations.\n", "\n", "    Parameters:\n", "    -----------\n", "    express_distribution : pandas DataFrame\n", "        Contains outlet_id and weighted_val columns\n", "    store_footprint_df : pandas DataFrame\n", "        Contains outlet_id column\n", "    overlaps_df : pandas DataFrame\n", "        Contains express_outlet_id, longtail_outlet_id, and perc_area_covered_of_express_merchant columns\n", "    group_cols : list, optional\n", "        Columns to group by when performing operations\n", "\n", "    Returns:\n", "    --------\n", "    pandas DataFrame\n", "        Final distribution with outlet_id and weighted_val columns\n", "    \"\"\"\n", "\n", "    # Create a copy to avoid modifying original dataframes\n", "    express_dist = express_distribution.copy()\n", "\n", "    # Initialize group handling\n", "    if group_cols is None:\n", "        group_cols = []\n", "\n", "    # Filter overlaps to only include existing longtail outlets\n", "    overlaps_df = overlaps_df_org[\n", "        overlaps_df_org[\"longtail_outlet_id\"].isin(store_footprint_df[\"outlet_id\"].unique())\n", "    ].copy()\n", "\n", "    # Normalize percentage area covered\n", "    overlaps_df[\"perc_area_covered_of_express_merchant\"] = (\n", "        overlaps_df[\"perc_area_covered_of_express_merchant\"] / 100\n", "    )\n", "\n", "    # Create a mask for present and missing outlets\n", "    present_mask = express_dist[\"outlet_id\"].isin(store_footprint_df[\"outlet_id\"])\n", "\n", "    # Split the DataFrame based on the mask\n", "    present_outlets = express_dist[present_mask].copy()\n", "    missing_outlets = express_dist[~present_mask].copy()\n", "    missing_outlet_ids = missing_outlets[\"outlet_id\"].unique()\n", "\n", "    # handling situations when sum of area covered is > 1\n", "    express_outlet_sums = overlaps_df.groupby(\"express_outlet_id\")[\n", "        \"perc_area_covered_of_express_merchant\"\n", "    ].sum()\n", "    needs_normalization = express_outlet_sums > 1\n", "\n", "    if needs_normalization.any():\n", "        # Create a normalization factor Series\n", "        normalization_factors = pd.Series(1.0, index=express_outlet_sums.index)\n", "        normalization_factors[needs_normalization] = 1 / express_outlet_sums[needs_normalization]\n", "\n", "        # Join the normalization factors back to overlaps_df\n", "        overlaps_df = overlaps_df.join(\n", "            normalization_factors.rename(\"norm_factor\"), on=\"express_outlet_id\"\n", "        )\n", "\n", "        # Apply normalization\n", "        overlaps_df[\"perc_area_covered_of_express_merchant\"] *= overlaps_df[\"norm_factor\"]\n", "        overlaps_df.drop(\"norm_factor\", axis=1, inplace=True)\n", "\n", "    # final overlapping maths\n", "\n", "    missing_with_overlaps = pd.merge(\n", "        missing_outlets, overlaps_df, left_on=\"outlet_id\", right_on=\"express_outlet_id\", how=\"inner\"\n", "    )\n", "\n", "    # Calculate transferred demand\n", "    missing_with_overlaps[\"transferred_demand\"] = (\n", "        missing_with_overlaps[\"weighted_val\"]\n", "        * missing_with_overlaps[\"perc_area_covered_of_express_merchant\"]\n", "    )\n", "\n", "    # Sum transferred demand by longtail outlet (and group_cols if provided)\n", "    groupby_cols = [\"longtail_outlet_id\"]\n", "    if group_cols:\n", "        groupby_cols.extend(group_cols)\n", "\n", "    transferred_demand = (\n", "        missing_with_overlaps.groupby(groupby_cols)[[\"transferred_demand\"]].sum().reset_index()\n", "    )\n", "\n", "    result = pd.merge(\n", "        present_outlets,\n", "        transferred_demand,\n", "        left_on=[\"outlet_id\"] if not group_cols else [\"outlet_id\"] + group_cols,\n", "        right_on=[\"longtail_outlet_id\"] if not group_cols else [\"longtail_outlet_id\"] + group_cols,\n", "        how=\"left\",\n", "    )\n", "\n", "    # Fill NA values and add transferred demand to weighted_val\n", "    result[\"transferred_demand\"] = result[\"transferred_demand\"].fillna(0)\n", "    result[\"weighted_val\"] = result[\"weighted_val\"] + result[\"transferred_demand\"]\n", "\n", "    # Keep only relevant columns\n", "    cols_to_keep = [\"outlet_id\", \"weighted_val\"]\n", "    if group_cols:\n", "        cols_to_keep.extend(group_cols)\n", "\n", "    result = result[cols_to_keep]\n", "\n", "    # Create DataFrame for missing outlets with zero values\n", "    if len(missing_outlet_ids) > 0:\n", "        # Get the unique combinations of outlet_id and group values for missing outlets\n", "        if group_cols:\n", "            missing_outlets_unique = missing_outlets[[\"outlet_id\"] + group_cols].drop_duplicates()\n", "            missing_outlets_unique[\"weighted_val\"] = 0\n", "            missing_zeros = missing_outlets_unique\n", "        else:\n", "            missing_zeros = pd.DataFrame({\"outlet_id\": missing_outlet_ids, \"weighted_val\": 0})\n", "\n", "        # Combine present outlets with modified weighted_val and missing outlets with zeros\n", "        result = pd.concat([result, missing_zeros])\n", "\n", "    return result\n", "\n", "\n", "def fetchBackEndOutletDetails():\n", "    # https://reports.grofer.io/queries/186767/source#204197\n", "    query = \"\"\"\n", "        WITH bo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state,\n", "          wom.cloud_store_id AS inv_outlet_id\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id != 7\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   INNER JOIN\n", "     (SELECT DISTINCT warehouse_id,\n", "                      cloud_store_id\n", "      FROM retail.warehouse_outlet_mapping\n", "      WHERE active = 1\n", "        AND lake_active_record) wom ON wom.warehouse_id = om.outlet_id\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.facility_id != 273 )\n", "    SELECT * FROM bo\n", "    \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"bo_df.csv\")\n", "    return df\n", "\n", "\n", "def getDoDTransferCurve(planned_df):\n", "    event_ids = planned_df.event_id.unique()\n", "    event_ids = \",\".join(map(str, event_ids))\n", "    query = f\"\"\"\n", "  with base as (\n", "  SELECT\n", "    partition_field,\n", "    run_id\n", "  FROM\n", "    (\n", "      SELECT\n", "        partition_field,\n", "        run_id,\n", "        ROW_NUMBER() OVER (\n", "          PARTITION BY partition_field,\n", "          run_id\n", "          ORDER BY\n", "            updated_at DESC\n", "        ) AS row_num\n", "      FROM\n", "        supply_etls.event_dod_inv_buildup\n", "      WHERE\n", "        partition_field in ({event_ids})\n", "    ) t\n", "  WHERE\n", "    row_num = 1\n", ")\n", "select\n", "  dod.date_,\n", "  dod.partition_field as event_id,\n", "  dod.be_facility_id,\n", "  dod.p_type as product_type,\n", "  sum(dod.transfer_cpd_adj) as cpd\n", "from\n", "  supply_etls.event_dod_inv_buildup dod\n", "  INNER JOIN base on base.partition_field = dod.partition_field\n", "  and base.run_id = dod.run_id\n", "where\n", "  dod.partition_field in ({event_ids})\n", "  and dod.transfer_cpd_adj > 0\n", "group by\n", "  1,\n", "  2,\n", "  3,\n", "  4\n", "  \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"dod_curve_df.csv\")\n", "    return df\n", "\n", "\n", "def sendSlackAlert(execution_mode, text):\n", "    if execution_mode == \"PROD\":\n", "        try:\n", "            import pencilbox as pb\n", "\n", "            pb.send_slack_message(channel=slack_alert_channel, text=text)\n", "        except Exception as e:\n", "            print(f\"Error sending Slack message: {e}, {text}\")\n", "        # handle the error further if needed\n", "    else:\n", "        print(\"local environment, just printing to console\")\n", "        print(text)\n", "\n", "\n", "def readFromSheetsWithRetry(spreadSheetId, sheetName, raiseExceptionFlag=True, mode=\"PROD\"):\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            if mode == \"LOCAL\":\n", "                script_dir = \"/home/<USER>/inventory-planning-playbooks/\"\n", "                sys.path.insert(0, script_dir)\n", "                from utils.commonFunc import readSpreadsheet\n", "\n", "                values = readSpreadsheet(spreadSheetId, sheetName)\n", "                df = pd.DataFrame(values[1:], columns=values[0])\n", "                return df\n", "            else:\n", "                import pencilbox as pb\n", "\n", "                df = pb.from_sheets(spreadSheetId, sheetName)\n", "                return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                if raiseExceptionFlag:\n", "                    raise Exception(\n", "                        \"Failed to readFromSheetsWithRetry after {} attempts\".format(max_retries)\n", "                    ) from e\n", "                else:\n", "                    sendSlackAlert(\n", "                        mode,\n", "                        \"festival tracker dag failed to read sheet\" + spreadSheetId + sheetName,\n", "                    )\n", "                    return pd.DataFrame()\n", "\n", "\n", "def getStoreDistribution(df):\n", "    event_ids = df.id.unique()\n", "    event_ids = \",\".join(map(str, event_ids))\n", "    query = f\"\"\"\n", "  select\n", "  event_id,\n", "  product_type_id,\n", "  outlet_id,\n", "  avg(final_val) as weighted_val\n", "from\n", "  supply_etls.event_final_store_ptype_search_distribution\n", "where\n", "  event_id in ({event_ids})\n", "group by\n", "  1,\n", "  2,\n", "  3\n", "  \"\"\"\n", "    df = fetchDataFromDB(query)\n", "    writeCsv(df, \"store_distribution_df.csv\")\n", "    return df\n", "\n", "\n", "config = readFromSheetsWithRetry(\n", "    spreadSheetId, configSheet, raiseExceptionFlag=False, mode=EXECUTION_MODE\n", ")\n", "\n", "try:\n", "    EXPRESS_PER_STORE_ITEM_COUNT = int(config[config[\"param\"] == \"EXPRESS\"][\"value\"].values[0])\n", "except Exception as e:\n", "    print(\"config error EXPRESS_PER_STORE_ITEM_COUNT\", e)\n", "    EXPRESS_PER_STORE_ITEM_COUNT = 10\n", "\n", "try:\n", "    HYBRID_PER_STORE_ITEM_COUNT = int(config[config[\"param\"] == \"HYBRID\"][\"value\"].values[0])\n", "except Exception as e:\n", "    print(\"config error HYBRID_PER_STORE_ITEM_COUNT\", e)\n", "    HYBRID_PER_STORE_ITEM_COUNT = 5\n", "\n", "if not EXPRESS_PER_STORE_ITEM_COUNT:\n", "    EXPRESS_PER_STORE_ITEM_COUNT = 10\n", "if not HYBRID_PER_STORE_ITEM_COUNT:\n", "    HYBRID_PER_STORE_ITEM_COUNT = 5\n", "\n", "print(\"final config values\")\n", "print(\"EXPRESS_PER_STORE_ITEM_COUNT\", EXPRESS_PER_STORE_ITEM_COUNT)\n", "print(\"HYBRID_PER_STORE_ITEM_COUNT\", HYBRID_PER_STORE_ITEM_COUNT)\n", "\n", "exclusion_event_ids = readFromSheetsWithRetry(\n", "    spreadSheetId, sheetName, raiseExceptionFlag=False, mode=EXECUTION_MODE\n", ")\n", "exclusion_event_ids[\"event_id\"] = pd.to_numeric(exclusion_event_ids[\"event_id\"], errors=\"coerce\")\n", "exclusion_event_ids.dropna(inplace=True)\n", "\n", "if exclusion_event_ids.shape[0] > 0 and \"event_id\" in exclusion_event_ids.columns:\n", "    exclusion_event_ids = exclusion_event_ids[\"event_id\"].unique()\n", "else:\n", "    exclusion_event_ids = []\n", "\n", "if not USE_LOCAL_CSV_DATA:\n", "    tasks_config = [\n", "        {\"func\": getCityClusterMapping, \"params\": {}},\n", "        {\"func\": getEventNames, \"params\": {}},\n", "        {\"func\": fetchPlannerData, \"params\": {}},\n", "        {\"func\": expressAndLtTeaMappings, \"params\": {}},\n", "        {\"func\": getS<PERSON><PERSON><PERSON>laps, \"params\": {}},\n", "        {\"func\": getHybridOutletsARSData, \"params\": {}},\n", "        {\"func\": fetchPackagedItemUniverse, \"params\": {}},\n", "        {\"func\": fetchFrontEndOutletDetails, \"params\": {}},\n", "        {\"func\": fetchBackEndOutletDetails, \"params\": {}},\n", "    ]\n", "    (\n", "        city_cluster_mapping_df,\n", "        event_df,\n", "        planned_df,\n", "        express_lt_tea_map_df,\n", "        overlaps_df,\n", "        hybrid_outlets,\n", "        catalog_df,\n", "        fo_df,\n", "        bo_df,\n", "    ) = run_parallel_joblib(tasks_config)\n", "else:\n", "    city_cluster_mapping_df = pd.read_csv(\"city_cluster_mapping_df.csv\")\n", "    event_df = pd.read_csv(\"event_df.csv\")\n", "    planned_df = pd.read_csv(\"planned_df.csv\")\n", "    express_lt_tea_map_df = pd.read_csv(\"express_lt_tea_map_df.csv\")\n", "    overlaps_df = pd.read_csv(\"overlaps_df.csv\")\n", "    hybrid_outlets = pd.read_csv(\"hybrid_outlets.csv\")\n", "    catalog_df = pd.read_csv(\"catalog_df.csv\")\n", "    fo_df = pd.read_csv(\"fo_df.csv\")\n", "    bo_df = pd.read_csv(\"bo_df.csv\")\n", "\n", "# # # testing handlings\n", "# city_cluster_mapping_df = city_cluster_mapping_df[city_cluster_mapping_df['cluster_id'] > 15000]\n", "# planned_df = planned_df[planned_df['item_id'].isin([10119810,10073578])]\n", "# planned_df = planned_df[planned_df['location_id'] == 15002]\n", "# planned_df = planned_df[planned_df['event_id'].isin([60,72])]\n", "# planned_df = planned_df[planned_df['event_id'].isin([48])]\n", "# # testing handlings ^^\n", "\n", "event_df = event_df[~event_df[\"id\"].isin(exclusion_event_ids)]\n", "planned_df = planned_df[~planned_df[\"event_id\"].isin(exclusion_event_ids)]\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 1 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "valid_outlets = express_lt_tea_map_df[\"outlet_id\"].unique()\n", "fo_df = fo_df[fo_df[\"outlet_id\"].isin(valid_outlets)]\n", "hybrid_outlets = hybrid_outlets[hybrid_outlets[\"outlet_id\"].isin(valid_outlets)]\n", "print(\"city_cluster_mapping_df\", city_cluster_mapping_df.shape)\n", "city_cluster_mapping_df = city_cluster_mapping_df[\n", "    city_cluster_mapping_df[\"city_id\"].isin(fo_df[\"city_id\"].unique())\n", "]\n", "print(\"city_cluster_mapping_df\", city_cluster_mapping_df.shape)\n", "\n", "\n", "if planned_df.shape[0] == 0:\n", "    print(\"nothing to execute in planner\")\n", "    sys.exit()\n", "\n", "if (\n", "    \"assortment_type\" not in planned_df.columns\n", "):  # safety handling if the column is removed from the table\n", "    planned_df[\"assortment_type\"] = None\n", "\n", "event_id_list = planned_df.event_id.unique()\n", "\n", "unique_cluster_ids = city_cluster_mapping_df[\"cluster_id\"].unique()\n", "city_planned_qty_df = planned_df[~planned_df[\"location_id\"].isin(unique_cluster_ids)]\n", "city_planned_qty_df = city_planned_qty_df[\n", "    (planned_df[\"location_id\"].isin(city_cluster_mapping_df[\"city_id\"].unique()))\n", "    & (planned_df[\"location_type\"] == \"CITY\")\n", "]\n", "city_planned_qty_df.rename(\n", "    columns={\n", "        \"planned_qty\": \"city_planned_qty\",\n", "        \"location_id\": \"city_id\",\n", "        \"assortment_type\": \"bu_assortment_type\",\n", "    },\n", "    inplace=True,\n", ")\n", "city_planned_qty_df = city_planned_qty_df.merge(city_cluster_mapping_df, on=\"city_id\", how=\"inner\")\n", "city_planned_qty_df = pd.merge(\n", "    city_planned_qty_df, event_df, left_on=[\"event_id\"], right_on=[\"id\"], how=\"inner\"\n", ")\n", "city_planned_qty_df.drop(columns=[\"id\"], inplace=True)\n", "\n", "#### CLUSTER LEVEL MATHS\n", "\n", "# unique item ids after removing city level uploads to filter regional assortment avoid imputing its rows\n", "event_item_id_combination_df = planned_df[\n", "    (planned_df[\"location_id\"].isin(unique_cluster_ids))\n", "    & (planned_df[\"location_type\"] == \"CLUSTER\")\n", "][[\"event_id\", \"item_id\"]].drop_duplicates()\n", "\n", "cross_product_df = pd.DataFrame(\n", "    {\n", "        \"event_id\": np.repeat(event_item_id_combination_df[\"event_id\"], len(unique_cluster_ids)),\n", "        \"item_id\": np.repeat(event_item_id_combination_df[\"item_id\"], len(unique_cluster_ids)),\n", "        \"location_id\": np.tile(unique_cluster_ids, len(event_item_id_combination_df)),\n", "    }\n", ")\n", "\n", "# only keeping cluster level inputs here\n", "planned_df = cross_product_df.merge(\n", "    planned_df, on=[\"location_id\", \"item_id\", \"event_id\"], how=\"left\"\n", ")\n", "planned_df[\"planned_qty\"].fillna(-1, inplace=True)\n", "planned_df[\"assortment_type\"].fillna(\"EXPRESS\", inplace=True)\n", "planned_df = planned_df.groupby([\"event_id\", \"item_id\"]).apply(lambda x: x.ffill().bfill())\n", "planned_df = planned_df.reset_index(drop=True)\n", "planned_df = planned_df.groupby([\"event_id\"]).apply(lambda x: x.ffill().bfill())\n", "planned_df = planned_df.reset_index(drop=True)\n", "\n", "# setting up the planned df\n", "final_df = pd.merge(planned_df, event_df, left_on=[\"event_id\"], right_on=[\"id\"], how=\"inner\")\n", "\n", "if final_df.shape[0] == 0:\n", "    print(\"nothing to execute in planner + event file\")\n", "    sys.exit()\n", "\n", "final_df = final_df.drop(columns=[\"id\"])\n", "final_df.rename(\n", "    columns={\"assortment_type\": \"bu_assortment_type\", \"location_id\": \"cluster_id\"}, inplace=True\n", ")\n", "\n", "print(\"events under consideration\", event_id_list)\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 2 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "# creating the final dataframe combining both cluster and city data\n", "final_city_cluster_item_map = final_df.drop_duplicates()\n", "final_city_cluster_item_map = final_city_cluster_item_map.merge(\n", "    city_cluster_mapping_df, on=\"cluster_id\", how=\"inner\"\n", ")\n", "# inner join since each cluster must be part of this city_cluster_mapping_df df\n", "\n", "# adding city level planned data to this frame\n", "if city_planned_qty_df.shape[0] > 0:\n", "    final_city_cluster_item_map = pd.concat([final_city_cluster_item_map, city_planned_qty_df])\n", "    # keeping only one row, city level row in the sheet\n", "    final_city_cluster_item_map.drop_duplicates(\n", "        subset=[\"city_id\", \"item_id\", \"event_id\"], keep=\"last\", inplace=True\n", "    )\n", "    final_city_cluster_item_map[\"planned_qty\"].fillna(-1, inplace=True)\n", "    final_city_cluster_item_map[\"city_planned_qty\"].fillna(-1, inplace=True)\n", "    mask = (final_city_cluster_item_map[\"city_planned_qty\"] >= 0) & (\n", "        final_city_cluster_item_map[\"planned_qty\"] < 0\n", "    )\n", "    final_city_cluster_item_map[\"planned_qty\"] = np.where(\n", "        mask, 0, final_city_cluster_item_map[\"planned_qty\"]\n", "    )\n", "else:\n", "    final_city_cluster_item_map[\"city_planned_qty\"] = -1\n", "\n", "final_city_cluster_item_map[\"sale_duration\"] = np.maximum(\n", "    (\n", "        pd.to_datetime(final_city_cluster_item_map[\"sale_end_date\"])\n", "        - pd.to_datetime(final_city_cluster_item_map[\"sale_start_date\"])\n", "    ).dt.days\n", "    + 1,\n", "    1,\n", ")\n", "\n", "\n", "def normalize_and_fill(\n", "    df, grouping_columns, output_column_name=\"renormalized_val\", input_column_name=\"weighted_val\"\n", "):\n", "\n", "    # Create a groupby object (no need to copy the DataFrame)\n", "    grouped = df.groupby(grouping_columns)\n", "\n", "    # Calculate sums for each group\n", "    group_sums = grouped[input_column_name].transform(\"sum\")\n", "\n", "    # Normalize\n", "    df[\"normalized_val\"] = df[input_column_name] / group_sums\n", "\n", "    # Calculate medians for filling NaN values\n", "    group_medians = grouped[\"normalized_val\"].transform(\"median\")\n", "\n", "    # Fill NaN values\n", "    df[\"normalized_val\"] = df[\"normalized_val\"].fillna(group_medians)\n", "\n", "    # Recalculate sums after filling NaNs\n", "    new_group_sums = grouped[\"normalized_val\"].transform(\"sum\")\n", "\n", "    # Renormalize and assign directly to the DataFrame with the final name\n", "    df[output_column_name] = df[\"normalized_val\"] / new_group_sums\n", "    df.drop(columns=\"normalized_val\", inplace=True)\n", "\n", "    return df\n", "\n", "\n", "def createDistributionDf():\n", "    if not USE_LOCAL_CSV_DATA:\n", "        tasks_config = [\n", "            {\"func\": fetchDefaultDistribution, \"params\": {}},\n", "            {\"func\": fetchConfiguredOutletItemDistribution, \"params\": {\"events\": event_df}},\n", "            {\"func\": getStoreDistribution, \"params\": {\"df\": event_df}},\n", "        ]\n", "        (\n", "            default_distribution_df,\n", "            configured_distribution_df,\n", "            store_distribution_df,\n", "        ) = run_parallel_joblib(tasks_config)\n", "    else:\n", "        default_distribution_df = pd.read_csv(\"default_distribution_df.csv\")\n", "        configured_distribution_df = pd.read_csv(\"configured_distribution_df.csv\")\n", "        store_distribution_df = pd.read_csv(\"store_distribution_df.csv\")\n", "\n", "    default_distribution_df = default_distribution_df[\n", "        default_distribution_df[\"outlet_id\"].isin(valid_outlets)\n", "    ]\n", "    configured_distribution_df = configured_distribution_df[\n", "        configured_distribution_df[\"outlet_id\"].isin(valid_outlets)\n", "    ]\n", "    # setting up distributions\n", "    lt_outlets = hybrid_outlets[hybrid_outlets[\"tagging\"] == \"longtail_stores\"]\n", "    default_distribution_df[\"weighted_val\"] = (\n", "        default_distribution_df[\"quantity\"] / default_distribution_df[\"quantity\"].sum()\n", "    )\n", "\n", "    default_distribution_df = default_distribution_df[[\"outlet_id\", \"weighted_val\"]]\n", "    medianValue = default_distribution_df.weighted_val.median()\n", "    base_df = fo_df[[\"outlet_id\"]]\n", "    default_distribution_df = base_df.merge(default_distribution_df, on=\"outlet_id\", how=\"left\")\n", "    default_distribution_df.weighted_val.fillna(medianValue, inplace=True)\n", "    default_distribution_df[\"assortment_type\"] = \"EXPRESS\"\n", "\n", "    default_hybrid_distribution = distributeRatios(\n", "        default_distribution_df[[\"outlet_id\", \"weighted_val\"]], hybrid_outlets, overlaps_df\n", "    )\n", "    default_hybrid_distribution[\"assortment_type\"] = \"HYBRID\"\n", "\n", "    default_lt_distribution = distributeRatios(\n", "        default_distribution_df[[\"outlet_id\", \"weighted_val\"]], lt_outlets, overlaps_df\n", "    )\n", "    default_lt_distribution[\"assortment_type\"] = \"LONGTAIL\"\n", "\n", "    default_distribution_df = pd.concat(\n", "        [default_distribution_df, default_hybrid_distribution, default_lt_distribution]\n", "    )\n", "    default_distribution_df.reset_index(drop=True, inplace=True)\n", "\n", "    # setting up configured distributions\n", "    configured_distribution_df = configured_distribution_df[\n", "        configured_distribution_df[\"assortment_type\"] == \"EXPRESS\"\n", "    ]\n", "    configured_distribution_df = configured_distribution_df[\n", "        [\"event_id\", \"outlet_id\", \"weighted_val\", \"assortment_type\"]\n", "    ]\n", "    base_df = pd.DataFrame(\n", "        [(e, o) for e in configured_distribution_df[\"event_id\"].unique() for o in valid_outlets],\n", "        columns=[\"event_id\", \"outlet_id\"],\n", "    )\n", "    base_df[\"assortment_type\"] = \"EXPRESS\"\n", "    medianValues = (\n", "        configured_distribution_df.groupby(\"event_id\").agg({\"weighted_val\": \"median\"}).reset_index()\n", "    )\n", "    medianValues.rename(columns={\"weighted_val\": \"fallback\"}, inplace=True)\n", "    configured_distribution_df = base_df.merge(\n", "        configured_distribution_df, on=[\"event_id\", \"assortment_type\", \"outlet_id\"], how=\"left\"\n", "    )\n", "    configured_distribution_df = configured_distribution_df.merge(\n", "        medianValues, on=[\"event_id\"], how=\"left\"\n", "    )\n", "    configured_distribution_df.weighted_val.fillna(\n", "        configured_distribution_df[\"fallback\"], inplace=True\n", "    )\n", "    configured_distribution_df = configured_distribution_df[\n", "        [\"event_id\", \"outlet_id\", \"weighted_val\", \"assortment_type\"]\n", "    ]\n", "\n", "    if configured_distribution_df.shape[0] > 0:\n", "        configured_distribution_df_hybrid = distributeRatios(\n", "            configured_distribution_df, hybrid_outlets, overlaps_df, group_cols=[\"event_id\"]\n", "        )\n", "        configured_distribution_df_lt = distributeRatios(\n", "            configured_distribution_df, lt_outlets, overlaps_df, group_cols=[\"event_id\"]\n", "        )\n", "        configured_distribution_df_hybrid[\"assortment_type\"] = \"HYBRID\"\n", "        configured_distribution_df_lt[\"assortment_type\"] = \"LONGTAIL\"\n", "        configured_distribution_df = pd.concat(\n", "            [\n", "                configured_distribution_df,\n", "                configured_distribution_df_hybrid,\n", "                configured_distribution_df_lt,\n", "            ]\n", "        )\n", "        configured_distribution_df.reset_index(drop=True, inplace=True)\n", "\n", "    configured_distribution_df = normalize_and_fill(\n", "        configured_distribution_df, [\"event_id\", \"assortment_type\"]\n", "    )\n", "    configured_distribution_df = configured_distribution_df[\n", "        [\"event_id\", \"assortment_type\", \"outlet_id\", \"renormalized_val\"]\n", "    ]\n", "    configured_distribution_df.columns = [\n", "        \"event_id\",\n", "        \"assortment_type\",\n", "        \"outlet_id\",\n", "        \"weighted_val\",\n", "    ]\n", "    default_distribution_df = normalize_and_fill(default_distribution_df, [\"assortment_type\"])\n", "    default_distribution_df = default_distribution_df[\n", "        [\"assortment_type\", \"outlet_id\", \"renormalized_val\"]\n", "    ]\n", "    default_distribution_df.columns = [\"assortment_type\", \"outlet_id\", \"weighted_val\"]\n", "\n", "    def populate_missing_combinations(\n", "        configured_distribution_df, default_distribution_df, event_id_list\n", "    ):\n", "        \"\"\"\n", "        Populate missing event ID and assortment type combinations in configured_distribution_df using default_distribution_df.\n", "\n", "        Args:\n", "        - configured_distribution_df (pd.DataFrame): Configured distribution DataFrame.\n", "        - default_distribution_df (pd.DataFrame): Default distribution DataFrame.\n", "        - event_id_list (list): List of event IDs.\n", "\n", "        Returns:\n", "        - updated_configured_distribution_df (pd.DataFrame): Updated configured distribution DataFrame with missing combinations populated.\n", "        \"\"\"\n", "\n", "        # Create a DataFrame with all possible event ID and assortment type combinations\n", "        all_combinations_df = pd.DataFrame(\n", "            {\n", "                \"event_id\": [\n", "                    event_id\n", "                    for event_id in event_id_list\n", "                    for assortment_type in [\"EXPRESS\", \"LONGTAIL\", \"HYBRID\"]\n", "                ],\n", "                \"assortment_type\": [\n", "                    assortment_type\n", "                    for _ in event_id_list\n", "                    for assortment_type in [\"EXPRESS\", \"LONGTAIL\", \"HYBRID\"]\n", "                ],\n", "            }\n", "        )\n", "\n", "        # Merge all combinations with default distribution DataFrame\n", "        all_combinations_with_default_df = all_combinations_df.merge(\n", "            default_distribution_df, how=\"left\", on=[\"assortment_type\"]\n", "        )\n", "\n", "        # Merge configured distribution DataFrame with the result\n", "        updated_configured_distribution_df = all_combinations_with_default_df.merge(\n", "            configured_distribution_df,\n", "            how=\"left\",\n", "            on=[\"event_id\", \"assortment_type\", \"outlet_id\"],\n", "            suffixes=(\"_default\", \"_configured\"),\n", "        )\n", "\n", "        # Fill missing values in weighted_val with default values\n", "        updated_configured_distribution_df[\"weighted_val\"] = updated_configured_distribution_df[\n", "            \"weighted_val_configured\"\n", "        ].fillna(updated_configured_distribution_df[\"weighted_val_default\"])\n", "\n", "        # Drop default and configured columns\n", "        updated_configured_distribution_df = updated_configured_distribution_df.drop(\n", "            columns=[\"weighted_val_configured\", \"weighted_val_default\"]\n", "        )\n", "\n", "        return updated_configured_distribution_df\n", "\n", "    final_distribution_df = populate_missing_combinations(\n", "        configured_distribution_df, default_distribution_df, event_id_list\n", "    )\n", "    final_distribution_df.reset_index(drop=True, inplace=True)\n", "\n", "    store_distribution_df = store_distribution_df[\n", "        store_distribution_df[\"outlet_id\"].isin(valid_outlets)\n", "    ]\n", "\n", "    if store_distribution_df.shape[0] > 0:\n", "\n", "        store_distribution_df = store_distribution_df[\n", "            [\"outlet_id\", \"event_id\", \"product_type_id\", \"weighted_val\"]\n", "        ]\n", "\n", "        base_df = store_distribution_df[[\"event_id\", \"product_type_id\"]].drop_duplicates()\n", "        base_df[\"tempKey\"] = 1\n", "        temp_df = fo_df[[\"outlet_id\"]]\n", "        temp_df[\"tempKey\"] = 1\n", "        base_df = base_df.merge(temp_df, on=\"tempKey\", how=\"outer\")\n", "        base_df.drop(columns=\"tempKey\", inplace=True)\n", "\n", "        store_distribution_df = base_df.merge(\n", "            store_distribution_df, on=[\"event_id\", \"outlet_id\", \"product_type_id\"], how=\"left\"\n", "        )\n", "\n", "        temp_df = default_distribution_df[\n", "            default_distribution_df[\"assortment_type\"] == \"EXPRESS\"\n", "        ].copy()\n", "        temp_df.rename(columns={\"weighted_val\": \"default_weighted_val\"}, inplace=True)\n", "        temp_df.drop(columns=[\"assortment_type\"], inplace=True)\n", "        store_distribution_df = store_distribution_df.merge(temp_df, on=\"outlet_id\", how=\"left\")\n", "\n", "        grouped = store_distribution_df.groupby([\"event_id\", \"product_type_id\"])\n", "        group_sums = grouped[\"weighted_val\"].transform(\"sum\")\n", "        store_distribution_df[\"weighted_val\"] = store_distribution_df[\"weighted_val\"] / group_sums\n", "        store_distribution_df[\"cartPen\"] = (\n", "            store_distribution_df[\"weighted_val\"] / store_distribution_df[\"default_weighted_val\"]\n", "        )\n", "\n", "        grouped = store_distribution_df.groupby([\"event_id\", \"product_type_id\"])\n", "        group_medians = grouped[\"cartPen\"].transform(\"median\")\n", "        store_distribution_df[\"cartPen\"].fillna(group_medians, inplace=True)\n", "\n", "        store_distribution_df[\"weighted_val\"].fillna(\n", "            store_distribution_df[\"cartPen\"] * store_distribution_df[\"default_weighted_val\"],\n", "            inplace=True,\n", "        )\n", "        store_distribution_df = store_distribution_df[\n", "            [\"event_id\", \"outlet_id\", \"product_type_id\", \"weighted_val\"]\n", "        ]\n", "        store_distribution_df = normalize_and_fill(\n", "            store_distribution_df, [\"event_id\", \"product_type_id\"]\n", "        )\n", "        store_distribution_df.drop(columns=\"weighted_val\", inplace=True)\n", "        store_distribution_df.rename(columns={\"renormalized_val\": \"weighted_val\"}, inplace=True)\n", "        store_distribution_df[\"assortment_type\"] = \"EXPRESS\"\n", "        store_distribution_df_hybrid = distributeRatios(\n", "            store_distribution_df,\n", "            hybrid_outlets,\n", "            overlaps_df,\n", "            group_cols=[\"event_id\", \"product_type_id\"],\n", "        )\n", "        store_distribution_df_lt = distributeRatios(\n", "            store_distribution_df,\n", "            lt_outlets,\n", "            overlaps_df,\n", "            group_cols=[\"event_id\", \"product_type_id\"],\n", "        )\n", "        store_distribution_df_hybrid[\"assortment_type\"] = \"HYBRID\"\n", "        store_distribution_df_lt[\"assortment_type\"] = \"LONGTAIL\"\n", "        store_distribution_df = pd.concat(\n", "            [store_distribution_df, store_distribution_df_hybrid, store_distribution_df_lt]\n", "        )\n", "        store_distribution_df.reset_index(drop=True, inplace=True)\n", "    else:\n", "        store_distribution_df = pd.DataFrame()\n", "    return final_distribution_df, store_distribution_df\n", "\n", "\n", "final_distribution_df, store_ptype_distribution_df = createDistributionDf()\n", "\"\"\"\n", "NOTE: in final_distribution_df, each assortment type has all the outlets, \n", "just that the outlets which are not supposed to have any value, their weighted val is being set as 0\n", "\n", "Recon\n", "final_distribution_df.groupby('assortment_type').agg({'outlet_id':'count'})\n", "final_distribution_df[final_distribution_df['weighted_val'] > 0].groupby('assortment_type').agg({'outlet_id':'count'})\n", "\n", "\"\"\"\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 3 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "# secondary queries\n", "if not USE_LOCAL_CSV_DATA:\n", "    tasks_config = [\n", "        {\n", "            \"func\": fetchLiveOutletItemLevelSnapshot,\n", "            \"params\": {\"planner_df\": final_city_cluster_item_map},\n", "        },\n", "        {\"func\": fetchBeItemPoMetrics, \"params\": {\"planner_df\": final_city_cluster_item_map}},\n", "        {\"func\": getOpenStoDetails, \"params\": {\"planned_df\": final_city_cluster_item_map}},\n", "        {\"func\": getOpenB2bDetails, \"params\": {\"planned_df\": final_city_cluster_item_map}},\n", "    ]\n", "    (\n", "        outlet_item_live_df,\n", "        be_item_po_metrics_df,\n", "        open_sto_details_df,\n", "        open_b2b_details_df,\n", "    ) = run_parallel_joblib(tasks_config)\n", "else:\n", "    outlet_item_live_df = pd.read_csv(\"outlet_item_live_df.csv\")\n", "    be_item_po_metrics_df = pd.read_csv(\"be_item_po_metrics_df.csv\")\n", "    open_sto_details_df = pd.read_csv(\"open_sto_details_df.csv\")\n", "    open_b2b_details_df = pd.read_csv(\"open_b2b_details_df.csv\")\n", "\n", "# assortment type identification\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 4 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "\n", "def createLiveCityItemAssortmentMap():\n", "    live_city_item_assortment_map = outlet_item_live_df[\n", "        outlet_item_live_df[\"master_assortment_substate_id\"].isin([1, 3])\n", "    ]\n", "    live_city_item_assortment_map = live_city_item_assortment_map.merge(\n", "        fo_df[[\"city_id\", \"outlet_id\"]], on=\"outlet_id\", how=\"inner\"\n", "    )\n", "    hybrid_tagged_city_items = live_city_item_assortment_map[\n", "        live_city_item_assortment_map[\"hybrid_flag\"]\n", "    ][[\"city_id\", \"item_id\"]].drop_duplicates()\n", "    hybrid_tagged_city_items[\"hybrid_flag\"] = True\n", "    live_city_item_assortment_map = live_city_item_assortment_map[\n", "        [\"city_id\", \"item_id\", \"assortment_type\", \"outlet_id\", \"hybrid_flag\"]\n", "    ]\n", "    live_city_item_assortment_map = (\n", "        live_city_item_assortment_map.groupby([\"city_id\", \"item_id\", \"assortment_type\"])\n", "        .agg(count_stores=(\"outlet_id\", \"count\"))\n", "        .reset_index()\n", "    )\n", "\n", "    live_city_item_assortment_map = live_city_item_assortment_map.pivot_table(\n", "        index=[\"city_id\", \"item_id\"],\n", "        columns=\"assortment_type\",\n", "        values=[\"count_stores\"],\n", "        aggfunc={\"count_stores\": \"sum\"},\n", "    )\n", "\n", "    live_city_item_assortment_map = live_city_item_assortment_map.fillna(0)\n", "    live_city_item_assortment_map = live_city_item_assortment_map.reset_index()\n", "    flat_columns = [\n", "        col[0] + \"_\" + col[1] if col[1] != \"\" else col[0]\n", "        for col in live_city_item_assortment_map.columns.values\n", "    ]\n", "    live_city_item_assortment_map.columns = flat_columns\n", "    if \"count_stores_EXPRESS\" not in flat_columns:\n", "        live_city_item_assortment_map[\"count_stores_EXPRESS\"] = 0\n", "    if \"count_stores_LONGTAIL\" not in flat_columns:\n", "        live_city_item_assortment_map[\"count_stores_LONGTAIL\"] = 0\n", "\n", "    live_city_item_assortment_map[\"total_stores\"] = (\n", "        live_city_item_assortment_map[\"count_stores_EXPRESS\"]\n", "        + live_city_item_assortment_map[\"count_stores_LONGTAIL\"]\n", "    )\n", "    live_city_item_assortment_map = live_city_item_assortment_map.merge(\n", "        hybrid_tagged_city_items, on=[\"city_id\", \"item_id\"], how=\"left\"\n", "    )\n", "    live_city_item_assortment_map[\"hybrid_flag\"].fillna(False, inplace=True)\n", "\n", "    def assignAssortmentType(row):\n", "        if row[\"total_stores\"] > 0 and row[\"hybrid_flag\"]:\n", "            return \"HYBRID\"\n", "        elif row[\"total_stores\"] > 0 and row[\"count_stores_EXPRESS\"] == row[\"total_stores\"]:\n", "            return \"EXPRESS\"\n", "        elif row[\"total_stores\"] > 0 and row[\"count_stores_LONGTAIL\"] == row[\"total_stores\"]:\n", "            return \"LONGTAIL\"\n", "        elif (\n", "            row[\"total_stores\"] > 0 and row[\"count_stores_LONGTAIL\"] >= row[\"count_stores_EXPRESS\"]\n", "        ):\n", "            return \"LONGTAIL\"\n", "        else:\n", "            return \"EXPRESS\"\n", "\n", "    live_city_item_assortment_map[\"assortment_type\"] = live_city_item_assortment_map.apply(\n", "        assignAssortmentType, axis=1\n", "    )\n", "    live_city_item_assortment_map = live_city_item_assortment_map[\n", "        [\"city_id\", \"item_id\", \"assortment_type\"]\n", "    ]\n", "    return live_city_item_assortment_map\n", "\n", "\n", "live_city_item_assortment_map = createLiveCityItemAssortmentMap()\n", "live_city_item_assortment_map_outlet_level = live_city_item_assortment_map.merge(\n", "    fo_df[[\"outlet_id\", \"city_id\"]], on=\"city_id\", how=\"inner\"\n", ")\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 5 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "\n", "def createLiveTeaDf():\n", "    # ideal tea\n", "    live_tea = express_lt_tea_map_df[[\"outlet_id\", \"be_outlet_id\", \"assortment_type\"]]\n", "    live_tea[\"polygon_type\"] = live_tea[\"assortment_type\"]\n", "    hybrid_tea = hybrid_outlets[[\"outlet_id\", \"tagging\"]].copy()\n", "    hybrid_tea[\"polygon_type\"] = hybrid_tea[\"tagging\"].apply(\n", "        lambda x: \"LONGTAIL\" if x == \"longtail_stores\" else \"EXPRESS\"\n", "    )\n", "    hybrid_tea[\"assortment_type\"] = \"HYBRID\"\n", "    # standalone lt stores will only have LT polygon, so below logic selects EXPRESS be_outlet_id if present\n", "    # otherwise use the LONGTAIL polygon be_outlet_id\n", "    def sort_assortment_type(x):\n", "        if x == \"EXPRESS\":\n", "            return 0\n", "        else:\n", "            return 1\n", "\n", "    temp = live_tea.copy()\n", "    temp[\"sort_key\"] = temp[\"assortment_type\"].apply(sort_assortment_type)\n", "    temp = temp.sort_values([\"outlet_id\", \"sort_key\"])\n", "    result = temp.drop_duplicates(\"outlet_id\", keep=\"first\")[[\"outlet_id\", \"be_outlet_id\"]]\n", "    hybrid_tea = hybrid_tea.merge(result, on=\"outlet_id\", how=\"inner\")\n", "    hybrid_tea.drop(columns=\"tagging\", inplace=True)\n", "    live_tea = pd.concat([live_tea, hybrid_tea])\n", "    live_tea.reset_index(drop=True, inplace=True)\n", "    return live_tea\n", "\n", "\n", "live_tea = createLiveTeaDf()\n", "\n", "\n", "def createCityAssortmentTypeStoreCounts():\n", "    city_store_counts_df = express_lt_tea_map_df.merge(\n", "        fo_df[[\"outlet_id\", \"city_id\"]], on=\"outlet_id\", how=\"inner\"\n", "    )\n", "    city_store_counts_df = (\n", "        city_store_counts_df.groupby([\"city_id\", \"fe_city_name\", \"assortment_type\"])\n", "        .agg({\"outlet_id\": \"count\"})\n", "        .reset_index()\n", "    )\n", "    city_store_counts_df.rename(\n", "        columns={\"fe_city_name\": \"city_name\", \"outlet_id\": \"count_stores\"}, inplace=True\n", "    )\n", "    city_store_counts_df = city_store_counts_df.pivot_table(\n", "        index=[\"city_id\", \"city_name\"],\n", "        columns=\"assortment_type\",\n", "        values=[\"count_stores\"],\n", "        aggfunc={\"count_stores\": \"sum\"},\n", "    )\n", "    city_store_counts_df = city_store_counts_df.fillna(0)\n", "    city_store_counts_df = city_store_counts_df.reset_index()\n", "    flat_columns = [\n", "        \"count_of_stores_\" + col[1] if col[1] != \"\" else col[0]\n", "        for col in city_store_counts_df.columns.values\n", "    ]\n", "    city_store_counts_df.columns = flat_columns\n", "    return city_store_counts_df\n", "\n", "\n", "city_store_count_df = createCityAssortmentTypeStoreCounts()\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 6 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "# creating a live tea based distribution file for all be outlet combination for all events\n", "live_tea_w_distribution_ratios = final_distribution_df.merge(\n", "    live_tea, on=[\"outlet_id\", \"assortment_type\"], how=\"left\"\n", ")\n", "live_tea_w_distribution_ratios = normalize_and_fill(\n", "    live_tea_w_distribution_ratios, [\"event_id\", \"assortment_type\"]\n", ")\n", "live_tea_w_distribution_ratios = live_tea_w_distribution_ratios[\n", "    [\"be_outlet_id\", \"event_id\", \"assortment_type\", \"outlet_id\", \"renormalized_val\", \"polygon_type\"]\n", "]\n", "live_tea_w_distribution_ratios.rename(columns={\"renormalized_val\": \"weighted_val\"}, inplace=True)\n", "live_tea_w_distribution_ratios = live_tea_w_distribution_ratios.merge(\n", "    fo_df[[\"outlet_id\", \"city_id\"]], on=\"outlet_id\", how=\"inner\"\n", ")\n", "# note - doing an inner join with fo - which ideally should be fine\n", "\n", "\n", "def calculateDefaultCityItemAssortmentTypeMap():\n", "    event_city_ratios = (\n", "        live_tea_w_distribution_ratios[\n", "            live_tea_w_distribution_ratios[\"assortment_type\"] == \"EXPRESS\"\n", "        ]\n", "        .groupby([\"event_id\", \"city_id\"])\n", "        .agg({\"weighted_val\": \"sum\", \"outlet_id\": \"count\"})\n", "        .reset_index()\n", "    )\n", "    event_city_ratios.rename(columns={\"outlet_id\": \"store_count\"}, inplace=True)\n", "    event_city_ratios = event_city_ratios.merge(city_cluster_mapping_df, on=\"city_id\", how=\"inner\")\n", "    event_city_ratios[\"weighted_val\"] = event_city_ratios.groupby([\"event_id\", \"cluster_id\"])[\n", "        \"weighted_val\"\n", "    ].transform(lambda x: x / x.sum())\n", "    default_city_cluster_item_map = final_city_cluster_item_map.merge(\n", "        event_city_ratios[[\"event_id\", \"cluster_id\", \"city_id\", \"store_count\", \"weighted_val\"]],\n", "        on=[\"event_id\", \"cluster_id\", \"city_id\"],\n", "        how=\"inner\",\n", "    )\n", "    default_city_cluster_item_map[\"city_planned_qty\"] = np.maximum(\n", "        default_city_cluster_item_map[\"planned_qty\"]\n", "        * default_city_cluster_item_map[\"weighted_val\"],\n", "        default_city_cluster_item_map[\"city_planned_qty\"],\n", "    )\n", "    default_city_cluster_item_map = default_city_cluster_item_map.merge(\n", "        catalog_df[[\"item_id\", \"mrp\"]], on=\"item_id\", how=\"inner\"\n", "    )\n", "\n", "    def assignDefaultAt(row):\n", "        if (\n", "            row[\"city_planned_qty\"] / row[\"store_count\"] / row[\"sale_duration\"]\n", "            < HYBRID_PER_STORE_ITEM_COUNT\n", "            and row[\"mrp\"] > 500\n", "        ):\n", "            if (\n", "                city_store_count_df[city_store_count_df[\"city_id\"] == row[\"city_id\"]].shape[0] > 0\n", "                and city_store_count_df[\n", "                    city_store_count_df[\"city_id\"] == row[\"city_id\"]\n", "                ].count_of_stores_LONGTAIL.values[0]\n", "                > 0\n", "            ):\n", "                return \"LONGTAIL\"\n", "            else:\n", "                return \"HYBRID\"\n", "        elif (\n", "            row[\"city_planned_qty\"] / row[\"store_count\"] / row[\"sale_duration\"]\n", "            >= HYBRID_PER_STORE_ITEM_COUNT\n", "            and row[\"city_planned_qty\"] / row[\"store_count\"] / row[\"sale_duration\"]\n", "            < EXPRESS_PER_STORE_ITEM_COUNT\n", "            and row[\"mrp\"] > 250\n", "        ):\n", "            return \"HYBRID\"\n", "        else:\n", "            return \"EXPRESS\"\n", "\n", "    default_city_cluster_item_map[\"default_assortment_type\"] = default_city_cluster_item_map.apply(\n", "        assignDefaultAt, axis=1\n", "    )\n", "    return default_city_cluster_item_map[\n", "        [\"event_id\", \"city_id\", \"item_id\", \"default_assortment_type\"]\n", "    ]\n", "\n", "\n", "default_city_item_assortment_type_map_df = calculateDefaultCityItemAssortmentTypeMap()\n", "final_city_cluster_item_assortment_type_map = final_city_cluster_item_map.merge(\n", "    live_city_item_assortment_map, on=[\"city_id\", \"item_id\"], how=\"left\"\n", ")\n", "final_city_cluster_item_assortment_type_map = final_city_cluster_item_assortment_type_map.merge(\n", "    default_city_item_assortment_type_map_df, on=[\"event_id\", \"city_id\", \"item_id\"], how=\"left\"\n", ")\n", "final_city_cluster_item_assortment_type_map[\"assortment_type\"].fillna(\n", "    final_city_cluster_item_assortment_type_map[\"default_assortment_type\"], inplace=True\n", ")\n", "final_city_cluster_item_assortment_type_map[\"assortment_type\"].fillna(\"EXPRESS\", inplace=True)\n", "final_city_cluster_item_assortment_type_map.drop(columns=\"default_assortment_type\", inplace=True)\n", "\n", "# this is the main step - which decides which be item fe combinations from the uploaded file\n", "live_tea_w_distribution_ratios_event_item = final_city_cluster_item_assortment_type_map.merge(\n", "    live_tea_w_distribution_ratios, on=[\"city_id\", \"event_id\", \"assortment_type\"], how=\"inner\"\n", ")\n", "\n", "output_df = live_tea_w_distribution_ratios_event_item.copy()\n", "\n", "clear_memory(\n", "    \"live_tea_w_distribution_ratios_event_item\",\n", "    \"final_city_cluster_item_assortment_type_map\",\n", "    \"live_tea_w_distribution_ratios\",\n", "    \"final_distribution_df\",\n", "    \"final_df\",\n", "    \"live_city_item_assortment_map\",\n", "    \"live_city_item_assortment_map_outlet_level\",\n", "    \"final_city_cluster_item_map\",\n", ")\n", "time_taken = time.time() - st\n", "print(f\"Step 7 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "\n", "def generateSalesNumbers(output_df):\n", "    if not USE_LOCAL_CSV_DATA:\n", "        tasks_config = [\n", "            {\"func\": fetchItemOutletSalesData, \"params\": {\"planner_df\": output_df}},\n", "            {\"func\": fetchItemOutletSalesDataSoFar, \"params\": {\"planner_df\": output_df}},\n", "        ]\n", "        item_outlet_sales_df, item_outlet_sales_so_far_df = run_parallel_joblib(tasks_config)\n", "    else:\n", "        item_outlet_sales_df = pd.read_csv(\"item_outlet_sales_df.csv\")\n", "        item_outlet_sales_so_far_df = pd.read_csv(\"item_outlet_sales_so_far_df.csv\")\n", "\n", "    today_sales = item_outlet_sales_df[\n", "        item_outlet_sales_df[\"order_create_dt_ist\"] == str(date.today())\n", "    ]\n", "    today_sales = today_sales[[\"outlet_id\", \"item_id\", \"quantity\"]]\n", "    today_sales.columns = [\"outlet_id\", \"item_id\", \"today_sales\"]\n", "    if today_sales.shape[0] > 0:\n", "        output_df = output_df.merge(today_sales, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "    else:\n", "        output_df[\"today_sales\"] = 0\n", "\n", "    yesterday_sales = item_outlet_sales_df[\n", "        item_outlet_sales_df[\"order_create_dt_ist\"] == str(date.today() - timedelta(days=1))\n", "    ]\n", "    yesterday_sales = yesterday_sales[[\"outlet_id\", \"item_id\", \"quantity\"]]\n", "    yesterday_sales.columns = [\"outlet_id\", \"item_id\", \"yesterday_sales\"]\n", "\n", "    if yesterday_sales.shape[0] > 0:\n", "        output_df = output_df.merge(yesterday_sales, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "    else:\n", "        output_df[\"yesterday_sales\"] = 0\n", "\n", "    if item_outlet_sales_so_far_df.shape[0] > 0:\n", "        output_df = output_df.merge(\n", "            item_outlet_sales_so_far_df,\n", "            on=[\"event_id\", \"item_id\", \"outlet_id\", \"sale_start_date\"],\n", "            how=\"left\",\n", "        )\n", "    else:\n", "        output_df[\"sales_so_far\"] = 0\n", "\n", "    return output_df\n", "\n", "\n", "output_df = generateSalesNumbers(output_df)\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 8 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "\n", "outlet_item_live_df.rename(columns={\"assortment_type\": \"actual_pfma_assortment_type\"}, inplace=True)\n", "outlet_item_live_df.drop(columns=[\"hybrid_flag\", \"facility_id\"], inplace=True)\n", "output_df.rename(columns={\"be_outlet_id\": \"default_be_outlet_id\"}, inplace=True)\n", "output_df = output_df.merge(outlet_item_live_df, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "# using item level tea is present, otherwise defaulting to default tea\n", "output_df[\"be_outlet_id\"].fillna(output_df[\"default_be_outlet_id\"], inplace=True)\n", "output_df.drop(columns=\"default_be_outlet_id\", inplace=True)\n", "\n", "be_item_po_metrics_df.rename(\n", "    columns={\"outlet_id\": \"be_outlet_id\", \"quantity\": \"be_inv\"}, inplace=True\n", ")\n", "output_df = output_df.merge(be_item_po_metrics_df, on=[\"be_outlet_id\", \"item_id\"], how=\"left\")\n", "clear_memory(\"be_item_po_metrics_df\", \"outlet_item_live_df\")\n", "\n", "if open_sto_details_df.shape[0] > 0:\n", "    open_sto_details_df[\"open_sto_under_process\"] = (\n", "        open_sto_details_df[\"reserved_qty\"]\n", "        - open_sto_details_df[\"released_reserved_quantity\"]\n", "        - open_sto_details_df[\"billed_qty\"]\n", "    )\n", "    open_sto_details_df[\"open_sto_in_transit\"] = (\n", "        open_sto_details_df[\"billed_qty\"]\n", "        - open_sto_details_df[\"inward_qty\"]\n", "        - open_sto_details_df[\"released_billed_quantity\"]\n", "    )\n", "    output_df = output_df.merge(\n", "        open_sto_details_df[\n", "            [\"outlet_id\", \"item_id\", \"open_sto_under_process\", \"open_sto_in_transit\"]\n", "        ],\n", "        on=[\"outlet_id\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "else:\n", "    output_df[\"open_sto_under_process\"] = 0\n", "    output_df[\"open_sto_in_transit\"] = 0\n", "clear_memory(\"open_sto_details_df\")\n", "time_taken = time.time() - st\n", "print(f\"Step 9 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "\n", "if open_b2b_details_df.shape[0] > 0:\n", "    open_b2b_details_df = open_b2b_details_df[\n", "        [\"child_be_outlet_id\", \"item_id\", \"open_b2b_transfer\"]\n", "    ]\n", "    open_b2b_details_df.columns = [\"be_outlet_id\", \"item_id\", \"open_b2b_transfer\"]\n", "    open_b2b_details_df = (\n", "        open_b2b_details_df.groupby([\"be_outlet_id\", \"item_id\"])\n", "        .agg({\"open_b2b_transfer\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "    output_df = output_df.merge(open_b2b_details_df, on=[\"be_outlet_id\", \"item_id\"], how=\"left\")\n", "else:\n", "    output_df[\"open_b2b_transfer\"] = 0\n", "clear_memory(\"open_b2b_details_df\")\n", "time_taken = time.time() - st\n", "print(f\"Step 10 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "# filtering the table\n", "bo_df.columns = [\"be_\" + col for col in bo_df.columns]\n", "output_df = output_df.merge(bo_df[[\"be_outlet_id\"]], on=\"be_outlet_id\", how=\"inner\")\n", "output_df = output_df.merge(fo_df[[\"outlet_id\"]], on=\"outlet_id\", how=\"inner\")\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 10.0 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "if store_ptype_distribution_df.shape[0] > 0:\n", "    output_df = output_df.merge(\n", "        catalog_df[[\"item_id\", \"product_type_id\"]], on=\"item_id\", how=\"inner\"\n", "    )\n", "    output_df = output_df.merge(\n", "        store_ptype_distribution_df,\n", "        on=[\"event_id\", \"product_type_id\", \"assortment_type\", \"outlet_id\"],\n", "        how=\"left\",\n", "        suffixes=(\"_old\", \"\"),\n", "    )\n", "    output_df[\"weighted_val\"].fillna(output_df[\"weighted_val_old\"], inplace=True)\n", "    output_df.drop(columns=[\"weighted_val_old\", \"product_type_id\"], inplace=True)\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 10.05 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "output_df = normalize_and_fill(output_df, [\"cluster_id\", \"event_id\", \"item_id\"])\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 10.1 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "output_df = normalize_and_fill(\n", "    output_df,\n", "    [\"cluster_id\", \"city_id\", \"event_id\", \"item_id\"],\n", "    output_column_name=\"city_level_renormalized_val\",\n", ")\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 10.2 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "# Create masks for conditions\n", "mask_outlet = (output_df[\"renormalized_val\"] == 0) | (output_df[\"planned_qty\"] <= 0)\n", "mask_city = (output_df[\"city_level_renormalized_val\"] == 0) | (output_df[\"city_planned_qty\"] <= 0)\n", "\n", "# Initialize columns with zeros\n", "output_df[\"outlet_planned_qty\"] = 0\n", "output_df[\"city_outlet_planned_qty\"] = 0\n", "output_df[\"ordering_outlet_planned_qty\"] = 0\n", "output_df[\"ordering_city_outlet_planned_qty\"] = 0\n", "\n", "# Only calculate values where masks are False (condition not met)\n", "# For outlet_planned_qty\n", "output_df.loc[~mask_outlet, \"outlet_planned_qty\"] = np.maximum(\n", "    1,\n", "    np.round(\n", "        output_df.loc[~mask_outlet, \"planned_qty\"] * output_df.loc[~mask_outlet, \"renormalized_val\"]\n", "    ),\n", ")\n", "\n", "# For city_outlet_planned_qty\n", "output_df.loc[~mask_city, \"city_outlet_planned_qty\"] = np.maximum(\n", "    1,\n", "    np.round(\n", "        output_df.loc[~mask_city, \"city_planned_qty\"]\n", "        * output_df.loc[~mask_city, \"city_level_renormalized_val\"]\n", "    ),\n", ")\n", "\n", "# Continue with the rest of your operations\n", "output_df[\"outlet_planned_qty\"] = np.maximum(\n", "    output_df[\"outlet_planned_qty\"], output_df[\"city_outlet_planned_qty\"]\n", ")\n", "output_df.drop(columns=[\"city_outlet_planned_qty\"], inplace=True)\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 10.3 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "output_df.loc[~mask_outlet, \"ordering_outlet_planned_qty\"] = (\n", "    output_df.loc[~mask_outlet, \"planned_qty\"] * output_df.loc[~mask_outlet, \"renormalized_val\"]\n", ")\n", "\n", "\n", "# For city_outlet_planned_qty\n", "output_df.loc[~mask_city, \"ordering_city_outlet_planned_qty\"] = (\n", "    output_df.loc[~mask_city, \"city_planned_qty\"]\n", "    * output_df.loc[~mask_city, \"city_level_renormalized_val\"]\n", ")\n", "\n", "# Continue with the rest of your operations\n", "output_df[\"ordering_outlet_planned_qty\"] = np.maximum(\n", "    output_df[\"ordering_outlet_planned_qty\"], output_df[\"ordering_city_outlet_planned_qty\"]\n", ")\n", "output_df.drop(columns=[\"ordering_city_outlet_planned_qty\"], inplace=True)\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 10.3.1 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "output_df = output_df.merge(bo_df, on=\"be_outlet_id\", how=\"inner\")\n", "output_df = output_df.merge(\n", "    catalog_df[\n", "        [\n", "            \"item_id\",\n", "            \"name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"product_type\",\n", "            \"storage_type\",\n", "            \"weight_in_gm\",\n", "            \"item_factor\",\n", "            \"custom_storage_type\",\n", "            \"brand_name\",\n", "            \"mrp\",\n", "        ]\n", "    ],\n", "    on=\"item_id\",\n", "    how=\"inner\",\n", ")\n", "output_df = output_df.merge(\n", "    fo_df[[\"outlet_id\", \"facility_id\", \"outlet_name\", \"city_name\", \"state\"]],\n", "    on=\"outlet_id\",\n", "    how=\"inner\",\n", ")\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 10.4 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "\n", "output_df[\"filter_flag\"] = (output_df[\"renormalized_val\"] > 0) | (\n", "    ~output_df[\"master_assortment_substate_id\"].isna()\n", ")\n", "output_df = output_df[output_df[\"filter_flag\"]]\n", "output_df.drop(columns=\"filter_flag\", inplace=True)\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 11 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "\n", "def generateDoDBasedLeftOverPlannedQtyEstimate(output_df):\n", "    from datetime import date\n", "\n", "    if not USE_LOCAL_CSV_DATA:\n", "        dod_curve_df = getDoDTransferCurve(planned_df)\n", "    else:\n", "        dod_curve_df = pd.read_csv(\"dod_curve_df.csv\")\n", "\n", "    # Prepare DoD curve DataFrame\n", "    dod_curve_df[\"date_\"] = pd.to_datetime(dod_curve_df[\"date_\"])\n", "\n", "    # Extract the unique items with their date ranges\n", "    temp_df = output_df[\n", "        [\n", "            \"be_facility_id\",\n", "            \"event_id\",\n", "            \"item_id\",\n", "            \"product_type\",\n", "            \"sale_start_date\",\n", "            \"sale_end_date\",\n", "        ]\n", "    ].drop_duplicates()\n", "    temp_df[\"sale_start_date\"] = pd.to_datetime(temp_df[\"sale_start_date\"])\n", "    temp_df[\"sale_end_date\"] = pd.to_datetime(temp_df[\"sale_end_date\"])\n", "\n", "    # Get current date for filtering\n", "    current_date = pd.to_datetime(date.today())\n", "\n", "    # Initialize the final results DataFrame with the right structure\n", "    final_results = pd.DataFrame(\n", "        columns=[\n", "            \"event_id\",\n", "            \"be_facility_id\",\n", "            \"item_id\",\n", "            \"sale_start_date\",\n", "            \"sale_end_date\",\n", "            \"pending_ratio\",\n", "            \"t0_ratio\",\n", "            \"t1_ratio\",\n", "            \"t2_ratio\",\n", "        ]\n", "    )\n", "\n", "    # Process each facility in chunks to reduce memory usage\n", "    for facility_id in temp_df[\"be_facility_id\"].unique():\n", "        facility_df = temp_df[temp_df[\"be_facility_id\"] == facility_id]\n", "\n", "        # Process each event within the facility\n", "        for event_id in facility_df[\"event_id\"].unique():\n", "            group_df = facility_df[facility_df[\"event_id\"] == event_id]\n", "\n", "            # Process each product type within the event\n", "            # for product_type in event_df['product_type'].unique():\n", "            # Filter items for this facility, event and product type\n", "            # group_df = event_df[event_df['product_type'] == product_type]\n", "\n", "            if group_df.empty:\n", "                continue\n", "\n", "            # Calculate the min/max dates specifically for this chunk\n", "            chunk_min_date = group_df[\"sale_start_date\"].min()\n", "            chunk_max_date = group_df[\"sale_end_date\"].max()\n", "\n", "            # Create date range specific to this chunk\n", "            chunk_dates = pd.date_range(start=chunk_min_date, end=chunk_max_date)\n", "\n", "            # Create a 'dummy' column for cartesian merge\n", "            chunk_dates_df = pd.DataFrame({\"date_\": chunk_dates, \"key\": 1})\n", "            group_df_with_key = group_df.copy()\n", "            group_df_with_key[\"key\"] = 1\n", "\n", "            # Perform the cartesian merge for this chunk\n", "            cart_df = pd.merge(group_df_with_key, chunk_dates_df, on=\"key\")\n", "\n", "            # Filter to only include dates within the sale period\n", "            date_level_df = cart_df[\n", "                (cart_df[\"date_\"] >= cart_df[\"sale_start_date\"])\n", "                & (cart_df[\"date_\"] <= cart_df[\"sale_end_date\"])\n", "            ]\n", "\n", "            # Drop the key column\n", "            date_level_df = date_level_df.drop(columns=[\"key\"])\n", "\n", "            if date_level_df.empty:\n", "                continue\n", "\n", "            # Merge with the DoD curve to get CPD values\n", "            date_level_df = pd.merge(\n", "                date_level_df,\n", "                dod_curve_df,\n", "                on=[\"be_facility_id\", \"event_id\", \"product_type\", \"date_\"],\n", "                how=\"left\",\n", "            )\n", "\n", "            if date_level_df.empty:\n", "                continue\n", "\n", "            # Process normalization using pandas operations\n", "            # Group by item identifier columns\n", "            grouped_df = date_level_df.groupby(\n", "                [\"be_facility_id\", \"event_id\", \"item_id\", \"sale_start_date\", \"sale_end_date\"]\n", "            )\n", "\n", "            # Define normalization function to apply to each group\n", "            def normalize_group(group):\n", "                # Calculate the sum of 'cpd' values in the group, ignoring NaN values\n", "                cpd_sum = group[\"cpd\"].sum(skipna=True)\n", "\n", "                # If the sum of 'cpd' values is zero (all NaN), distribute equally\n", "                if cpd_sum == 0:\n", "                    # Count the number of rows in the group\n", "                    num_rows = len(group)\n", "                    # Assign equal 'cpd' values to each row in the group\n", "                    group[\"cpd\"] = 1 / num_rows\n", "                else:\n", "                    # Normalize 'cpd' values within the group\n", "                    median_cpd = group[\"cpd\"].median(skipna=True)\n", "                    group[\"cpd\"] = group[\"cpd\"].fillna(median_cpd)\n", "                    cpd_sum = group[\"cpd\"].sum(skipna=True)\n", "                    group[\"cpd\"] = group[\"cpd\"] / cpd_sum\n", "\n", "                return group\n", "\n", "            # Apply normalization\n", "            normalized_df = grouped_df.apply(normalize_group)\n", "            normalized_df = normalized_df.reset_index(drop=True)\n", "\n", "            # Calculate pending_ratio using pandas operations (sum of CPD for dates >= current_date)\n", "            pending_df = normalized_df[normalized_df[\"date_\"] >= current_date].copy()\n", "            pending_result = (\n", "                pending_df.groupby(\n", "                    [\"event_id\", \"be_facility_id\", \"item_id\", \"sale_start_date\", \"sale_end_date\"]\n", "                )\n", "                .agg({\"cpd\": \"sum\"})\n", "                .reset_index()\n", "            )\n", "            pending_result.rename(columns={\"cpd\": \"pending_ratio\"}, inplace=True)\n", "\n", "            # Calculate t0, t1, t2 ratios using pandas operations\n", "            for i, day_offset in enumerate([0, 1, 2]):\n", "                target_date = current_date + pd.Timedelta(days=day_offset)\n", "                ratio_column = f\"t{day_offset}_ratio\"\n", "\n", "                # Filter for the specific day\n", "                day_df = normalized_df[normalized_df[\"date_\"] == target_date].copy()\n", "\n", "                if not day_df.empty:\n", "                    day_df.rename(columns={\"cpd\": ratio_column}, inplace=True)\n", "\n", "                    # Merge with pending_result\n", "                    pending_result = pending_result.merge(\n", "                        day_df[\n", "                            [\n", "                                \"event_id\",\n", "                                \"be_facility_id\",\n", "                                \"item_id\",\n", "                                \"sale_start_date\",\n", "                                \"sale_end_date\",\n", "                                ratio_column,\n", "                            ]\n", "                        ],\n", "                        on=[\n", "                            \"event_id\",\n", "                            \"be_facility_id\",\n", "                            \"item_id\",\n", "                            \"sale_start_date\",\n", "                            \"sale_end_date\",\n", "                        ],\n", "                        how=\"left\",\n", "                    )\n", "                else:\n", "                    # If no data for this day, add column with zeros\n", "                    pending_result[ratio_column] = 0\n", "\n", "                # Fill NaN values with 0\n", "                pending_result[ratio_column].fillna(0, inplace=True)\n", "\n", "            # Append to final results\n", "            final_results = pd.concat([final_results, pending_result], ignore_index=True)\n", "\n", "    # Convert dates back to string format\n", "    if not final_results.empty:\n", "        final_results[\"sale_start_date\"] = final_results[\"sale_start_date\"].dt.strftime(\"%Y-%m-%d\")\n", "        final_results[\"sale_end_date\"] = final_results[\"sale_end_date\"].dt.strftime(\"%Y-%m-%d\")\n", "\n", "    return final_results\n", "\n", "\n", "pending_dod_ratio_df = generateDoDBasedLeftOverPlannedQtyEstimate(output_df)\n", "pending_dod_ratio_df[\"event_id\"] = pd.to_numeric(pending_dod_ratio_df[\"event_id\"], errors=\"coerce\")\n", "pending_dod_ratio_df[\"be_facility_id\"] = pd.to_numeric(\n", "    pending_dod_ratio_df[\"be_facility_id\"], errors=\"coerce\"\n", ")\n", "pending_dod_ratio_df[\"item_id\"] = pd.to_numeric(pending_dod_ratio_df[\"item_id\"], errors=\"coerce\")\n", "pending_dod_ratio_df[\"t0_ratio\"] = pd.to_numeric(pending_dod_ratio_df[\"t0_ratio\"], errors=\"coerce\")\n", "pending_dod_ratio_df[\"t1_ratio\"] = pd.to_numeric(pending_dod_ratio_df[\"t1_ratio\"], errors=\"coerce\")\n", "pending_dod_ratio_df[\"t2_ratio\"] = pd.to_numeric(pending_dod_ratio_df[\"t2_ratio\"], errors=\"coerce\")\n", "\n", "output_df = output_df.merge(\n", "    pending_dod_ratio_df,\n", "    on=[\"be_facility_id\", \"item_id\", \"event_id\", \"sale_start_date\", \"sale_end_date\"],\n", "    how=\"left\",\n", ")\n", "clear_memory(\"pending_dod_ratio_df\")\n", "output_df[\"pending_ratio\"].fillna(0, inplace=True)\n", "output_df[\"t0_ratio\"].fillna(0, inplace=True)\n", "output_df[\"t1_ratio\"].fillna(0, inplace=True)\n", "output_df[\"t2_ratio\"].fillna(0, inplace=True)\n", "output_df[\"outlet_planned_qty\"] = pd.to_numeric(output_df[\"outlet_planned_qty\"], errors=\"coerce\")\n", "output_df[\"outlet_planned_qty\"].fillna(0, inplace=True)\n", "output_df[\"pending_outlet_planned_qty\"] = (\n", "    output_df[\"outlet_planned_qty\"] * output_df[\"pending_ratio\"]\n", ").round()\n", "output_df[\"t0_outlet_planned_qty\"] = (\n", "    output_df[\"outlet_planned_qty\"] * output_df[\"t0_ratio\"]\n", ").round()\n", "output_df[\"t1_outlet_planned_qty\"] = (\n", "    output_df[\"outlet_planned_qty\"] * output_df[\"t1_ratio\"]\n", ").round()\n", "output_df[\"t2_outlet_planned_qty\"] = (\n", "    output_df[\"outlet_planned_qty\"] * output_df[\"t2_ratio\"]\n", ").round()\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 11.5 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "clear_memory(\n", "    \"planned_df\",\n", "    \"event_df\",\n", "    \"fo_df\",\n", "    \"city_cluster_mapping_df\",\n", "    \"hybrid_tagged_city_items\",\n", "    \"express_lt_tea_map_df\",\n", "    \"overlaps_df\",\n", "    \"hybrid_outlets\",\n", ")\n", "print(\"output_df\", output_df.shape)\n", "\n", "\n", "def generateOrderingTeaBeDf():\n", "    if not USE_LOCAL_CSV_DATA:\n", "        ordering_tea_df = getOrderingTea()\n", "    else:\n", "        ordering_tea_df = pd.read_csv(\"ordering_tea_df.csv\")\n", "\n", "    ordering_tea_df[\"assortment_type\"] = \"EXPRESS\"\n", "    ordering_tea_df = ordering_tea_df.merge(\n", "        bo_df[[\"be_facility_id\", \"be_outlet_id\"]], on=\"be_facility_id\", how=\"inner\"\n", "    )\n", "    ordering_tea_df.drop(columns=\"be_facility_id\", inplace=True)\n", "    ordering_tea_df_hybrid = ordering_tea_df.copy()\n", "    ordering_tea_df_hybrid[\"assortment_type\"] = \"HYBRID\"\n", "    ordering_tea_df = pd.concat([ordering_tea_df, ordering_tea_df_hybrid])\n", "    ordering_tea_df.reset_index(drop=True, inplace=True)\n", "    ordering_tea_df.rename(\n", "        columns={\"festival_name\": \"event_name\", \"be_outlet_id\": \"ordering_be_outlet_id\"},\n", "        inplace=True,\n", "    )\n", "\n", "    return ordering_tea_df\n", "\n", "\n", "ordering_tea_df = generateOrderingTeaBeDf()\n", "output_df = output_df.merge(\n", "    ordering_tea_df, on=[\"event_name\", \"outlet_id\", \"assortment_type\"], how=\"left\"\n", ")\n", "output_df[\"ordering_be_outlet_id\"].fillna(output_df[\"be_outlet_id\"], inplace=True)\n", "output_df[\"ordering_be_outlet_id\"] = output_df[\"ordering_be_outlet_id\"].astype(int)\n", "\n", "pushDfToTrino(\n", "    output_df,\n", "    tableName1,\n", "    description=\"outlet item level metrics with live tea for all live events configured in bulk upload\",\n", "    primaryKeys=[\"be_outlet_id\", \"outlet_id\", \"event_id\", \"item_id\"],\n", "    load_type=\"truncate\",\n", "    environ=ENVIRON,\n", ")\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 12 in {time_taken:.2f} seconds\")\n", "st = time.time()\n", "\n", "grouping_cols = [\n", "    \"be_outlet_id\",\n", "    \"event_id\",\n", "    \"item_id\",\n", "    \"event_name\",\n", "    \"event_start_date\",\n", "    \"event_end_date\",\n", "]\n", "output_df[grouping_cols] = output_df[grouping_cols].fillna(\"\")\n", "\n", "output_df[\"ideal_count_express_stores\"] = (output_df[\"polygon_type\"] == \"EXPRESS\").astype(int)\n", "output_df[\"ideal_count_lt_stores\"] = (output_df[\"polygon_type\"] == \"LONGTAIL\").astype(int)\n", "output_df[\"actual_count_express_stores\"] = (\n", "    output_df[\"actual_pfma_assortment_type\"] == \"EXPRESS\"\n", ").astype(int)\n", "output_df[\"actual_count_lt_stores\"] = (\n", "    output_df[\"actual_pfma_assortment_type\"] == \"LONGTAIL\"\n", ").astype(int)\n", "\n", "# Create other binary indicator columns\n", "output_df[\"zero_planned_qty_stores\"] = (output_df[\"outlet_planned_qty\"] == 0).astype(int)\n", "output_df[\"oos_stores\"] = (output_df[\"fe_inv\"] == 0).astype(int)\n", "output_df[\"oos_stores_w_planned_qty\"] = (\n", "    (output_df[\"fe_inv\"] == 0) & (output_df[\"outlet_planned_qty\"] > 0)\n", ").astype(int)\n", "output_df[\"minus_1_planned_stores\"] = (output_df[\"planned_qty\"] < 0).astype(int)\n", "\n", "# Calculate inventory with STO\n", "output_df[\"fe_inv_w_sto\"] = (\n", "    output_df[\"fe_inv\"] + output_df[\"open_sto_under_process\"] + output_df[\"open_sto_in_transit\"]\n", ")\n", "output_df[\"oos_w_sto_stores\"] = (output_df[\"fe_inv_w_sto\"] == 0).astype(int)\n", "output_df[\"oos_w_sto_stores_w_planned_qty\"] = (\n", "    (output_df[\"fe_inv_w_sto\"] == 0) & (output_df[\"outlet_planned_qty\"] > 0)\n", ").astype(int)\n", "\n", "# Convert date columns in one go\n", "date_columns = [\"sale_start_date\", \"sale_end_date\", \"cut_off_date\"]\n", "output_df[date_columns] = output_df[date_columns].apply(pd.to_datetime, errors=\"coerce\")\n", "\n", "# incorrect name of the df, it should be actually live_store_counts_df\n", "ideal_store_counts_df = (\n", "    live_tea.groupby([\"be_outlet_id\", \"assortment_type\"]).agg({\"outlet_id\": \"count\"}).reset_index()\n", ")\n", "ideal_store_counts_df = ideal_store_counts_df.pivot_table(\n", "    index=[\"be_outlet_id\"],\n", "    columns=\"assortment_type\",\n", "    values=[\"outlet_id\"],\n", "    aggfunc={\"outlet_id\": \"sum\"},\n", ")\n", "ideal_store_counts_df = ideal_store_counts_df.fillna(0)\n", "ideal_store_counts_df = ideal_store_counts_df.reset_index()\n", "flat_columns = [\n", "    \"live_count_of_stores_\" + col[1] if col[1] != \"\" else col[0]\n", "    for col in ideal_store_counts_df.columns.values\n", "]\n", "ideal_store_counts_df.columns = flat_columns\n", "\n", "\n", "def generateLiveTeaBeOutputDf():\n", "    xt = time.time()\n", "    be_output_df = (\n", "        output_df.groupby(grouping_cols)\n", "        .agg(\n", "            {\n", "                \"cluster_id\": \"nunique\",\n", "                \"city_id\": \"nunique\",\n", "                \"sale_start_date\": \"min\",\n", "                \"sale_end_date\": \"max\",\n", "                \"cut_off_date\": \"min\",\n", "                \"outlet_planned_qty\": \"sum\",\n", "                \"ordering_outlet_planned_qty\": \"sum\",\n", "                \"ideal_count_express_stores\": \"sum\",\n", "                \"ideal_count_lt_stores\": \"sum\",\n", "                \"actual_count_express_stores\": \"sum\",\n", "                \"actual_count_lt_stores\": \"sum\",\n", "                \"minus_1_planned_stores\": \"sum\",\n", "                \"oos_stores\": \"sum\",\n", "                \"zero_planned_qty_stores\": \"sum\",\n", "                \"oos_w_sto_stores\": \"sum\",\n", "                \"oos_stores_w_planned_qty\": \"sum\",\n", "                \"oos_w_sto_stores_w_planned_qty\": \"sum\",\n", "                \"today_sales\": \"sum\",\n", "                \"yesterday_sales\": \"sum\",\n", "                \"sales_so_far\": \"sum\",\n", "                \"aps_cpd\": \"sum\",\n", "                \"cpd\": \"sum\",\n", "                \"fe_inv\": \"sum\",\n", "                \"open_sto_under_process\": \"sum\",\n", "                \"open_sto_in_transit\": \"sum\",\n", "                \"be_inv\": \"max\",\n", "                \"open_po\": \"max\",\n", "                \"scheduled_today\": \"max\",\n", "                \"scheduled_t1\": \"max\",\n", "                \"scheduled_t2\": \"max\",\n", "                \"scheduled_t3\": \"max\",\n", "                \"scheduled_future\": \"max\",\n", "                \"unscheduled\": \"max\",\n", "                \"open_b2b_transfer\": \"max\",\n", "                \"pending_outlet_planned_qty\": \"sum\",\n", "                \"t0_outlet_planned_qty\": \"sum\",\n", "                \"t1_outlet_planned_qty\": \"sum\",\n", "                \"t2_outlet_planned_qty\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "    # todo add store counts by assortment type\n", "    be_output_df.rename(\n", "        columns={\n", "            \"outlet_planned_qty\": \"planned_qty\",\n", "            \"city_id\": \"count_of_cities\",\n", "            \"cluster_id\": \"count_of_clusters\",\n", "            \"ordering_outlet_planned_qty\": \"ordering_planned_qty\",\n", "        },\n", "        inplace=True,\n", "    )\n", "    be_output_df = be_output_df.merge(bo_df, on=\"be_outlet_id\", how=\"inner\")\n", "    be_output_df = be_output_df.merge(\n", "        catalog_df[\n", "            [\n", "                \"item_id\",\n", "                \"name\",\n", "                \"l0\",\n", "                \"l1\",\n", "                \"l2\",\n", "                \"product_type\",\n", "                \"storage_type\",\n", "                \"weight_in_gm\",\n", "                \"item_factor\",\n", "                \"custom_storage_type\",\n", "                \"brand_name\",\n", "                \"mrp\",\n", "            ]\n", "        ],\n", "        on=\"item_id\",\n", "        how=\"inner\",\n", "    )\n", "    be_output_df[\"cluster_id\"] = None\n", "    be_output_df[\"city_id\"] = None\n", "    be_output_df = be_output_df.merge(ideal_store_counts_df, on=\"be_outlet_id\", how=\"left\")\n", "    print(\"be_output_df\", be_output_df.shape)\n", "\n", "    pushDfToTrino(\n", "        be_output_df,\n", "        tableName2,\n", "        description=\"be item level metrics with live tea for all live events configured in bulk upload\",\n", "        primaryKeys=[\"be_outlet_id\", \"event_id\", \"item_id\"],\n", "        load_type=\"truncate\",\n", "        environ=ENVIRON,\n", "    )\n", "\n", "    time_taken = time.time() - xt\n", "    print(f\"Step generateLiveTeaBeOutputDf in {time_taken:.2f} seconds\")\n", "    xt = time.time()\n", "\n", "    # be_output_df['hour_'] = time.localtime().tm_hour\n", "    createLogTable(\n", "        logTableName2,\n", "        tableName2,\n", "        description=\"log table for be item level metrics with live tea for all live events configured in bulk upload\",\n", "        primaryKeys=[\"be_outlet_id\", \"event_id\", \"item_id\"],\n", "        load_type=\"upsert\",\n", "        environ=ENVIRON,\n", "        partition_key=[],\n", "    )\n", "\n", "    time_taken = time.time() - xt\n", "    print(f\"Step generateLiveTeaBeOutputDf 2 in {time_taken:.2f} seconds\")\n", "\n", "\n", "# recon\n", "# temp = be_output_df.groupby(['event_id','item_id']).agg({'planned_qty':'sum'}).reset_index()\n", "# temp2 = planned_df.groupby(['event_id','item_id']).agg({'planned_qty':'sum'}).reset_index()\n", "# temp = temp.merge(temp2, on = ['event_id','item_id'], how='outer')\n", "# temp['delta'] = temp['planned_qty_x'] - temp['planned_qty_y']\n", "# print(temp.head())\n", "# print(temp['planned_qty_x'].sum())\n", "# print(temp['planned_qty_y'].sum())\n", "\n", "# ordering_tea_df\n", "\n", "\n", "def generateOrderingTeaBeOutputDf(output_df):\n", "    xt = time.time()\n", "    grouping_cols = [\n", "        \"ordering_be_outlet_id\",\n", "        \"event_id\",\n", "        \"item_id\",\n", "        \"event_name\",\n", "        \"event_start_date\",\n", "        \"event_end_date\",\n", "    ]\n", "    ordering_be_output_df = (\n", "        output_df.groupby(grouping_cols)\n", "        .agg(\n", "            {\n", "                \"city_id\": \"nunique\",\n", "                \"cluster_id\": \"nunique\",\n", "                \"sale_start_date\": \"min\",\n", "                \"sale_end_date\": \"max\",\n", "                \"cut_off_date\": \"min\",\n", "                \"outlet_planned_qty\": \"sum\",\n", "                \"ordering_outlet_planned_qty\": \"sum\",\n", "                \"ideal_count_express_stores\": \"sum\",\n", "                \"ideal_count_lt_stores\": \"sum\",\n", "                \"actual_count_express_stores\": \"sum\",\n", "                \"actual_count_lt_stores\": \"sum\",\n", "                \"be_inv\": \"max\",\n", "                \"fe_inv\": \"sum\",\n", "                \"open_sto_under_process\": \"sum\",\n", "                \"open_sto_in_transit\": \"sum\",\n", "                \"fe_inv_w_sto\": \"sum\",\n", "                \"aps_cpd\": \"sum\",\n", "                \"cpd\": \"sum\",\n", "                \"open_po\": \"max\",\n", "                \"scheduled_today\": \"max\",\n", "                \"scheduled_t1\": \"max\",\n", "                \"scheduled_t2\": \"max\",\n", "                \"scheduled_t3\": \"max\",\n", "                \"scheduled_future\": \"max\",\n", "                \"unscheduled\": \"max\",\n", "                \"open_b2b_transfer\": \"max\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "    # todo add store counts by assortment type\n", "    ordering_be_output_df.rename(\n", "        columns={\n", "            \"outlet_planned_qty\": \"planned_qty\",\n", "            \"city_id\": \"count_of_cities\",\n", "            \"cluster_id\": \"count_of_clusters\",\n", "            \"ordering_be_outlet_id\": \"be_outlet_id\",\n", "            \"ordering_outlet_planned_qty\": \"ordering_planned_qty\",\n", "        },\n", "        inplace=True,\n", "    )\n", "    ordering_be_output_df = ordering_be_output_df.merge(bo_df, on=\"be_outlet_id\", how=\"inner\")\n", "    ordering_be_output_df = ordering_be_output_df.merge(\n", "        catalog_df[\n", "            [\n", "                \"item_id\",\n", "                \"name\",\n", "                \"l0\",\n", "                \"l1\",\n", "                \"l2\",\n", "                \"product_type\",\n", "                \"storage_type\",\n", "                \"weight_in_gm\",\n", "                \"item_factor\",\n", "                \"custom_storage_type\",\n", "                \"brand_name\",\n", "                \"mrp\",\n", "            ]\n", "        ],\n", "        on=\"item_id\",\n", "        how=\"inner\",\n", "    )\n", "    ordering_be_output_df[\"cluster_id\"] = None\n", "    ordering_be_output_df[\"city_id\"] = None\n", "    print(\"ordering_be_output_df\", ordering_be_output_df.shape)\n", "    pushDfToTrino(\n", "        ordering_be_output_df,\n", "        tableName3,\n", "        description=\"be item level metrics with ordering tea for all live events configured in bulk upload\",\n", "        primaryKeys=[\"be_outlet_id\", \"event_id\", \"item_id\"],\n", "        load_type=\"truncate\",\n", "        environ=ENVIRON,\n", "    )\n", "\n", "    time_taken = time.time() - xt\n", "    print(f\"generateOrderingTeaBeOutputDf in {time_taken:.2f} seconds\")\n", "    xt = time.time()\n", "\n", "    # ordering_be_output_df['hour_'] = time.localtime().tm_hour\n", "    createLogTable(\n", "        logTableName3,\n", "        tableName3,\n", "        description=\"log table of be item level metrics with ordering tea for all live events configured in bulk upload\",\n", "        primaryKeys=[\"be_outlet_id\", \"event_id\", \"item_id\"],\n", "        load_type=\"upsert\",\n", "        environ=ENVIRON,\n", "        partition_key=[],\n", "    )\n", "\n", "    time_taken = time.time() - xt\n", "    print(f\"generateOrderingTeaBeOutputDf 2 in {time_taken:.2f} seconds\")\n", "\n", "\n", "tasks_config = [\n", "    {\"func\": generateLiveTeaBeOutputDf, \"params\": {}},\n", "    {\"func\": generateOrderingTeaBeOutputDf, \"params\": {\"output_df\": output_df}},\n", "    {\n", "        \"func\": createLogTable,\n", "        \"params\": {\n", "            \"tableName\": logTableName1,\n", "            \"sourceTable\": tableName1,\n", "            \"description\": \"log table for outlet item level metrics with live tea for all live events configured in bulk upload\",\n", "            \"primaryKeys\": [\"be_outlet_id\", \"outlet_id\", \"event_id\", \"item_id\"],\n", "            \"load_type\": \"upsert\",\n", "            \"environ\": ENVIRON,\n", "            \"partition_key\": [],\n", "        },\n", "    },\n", "]\n", "\n", "run_parallel_joblib(tasks_config)\n", "\n", "time_taken = time.time() - st\n", "print(f\"Step 15 in {time_taken:.2f} seconds\")\n", "total_time_taken = time.time() - st0\n", "print(f\"Total Dag execution time {time_taken:.2f} seconds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}