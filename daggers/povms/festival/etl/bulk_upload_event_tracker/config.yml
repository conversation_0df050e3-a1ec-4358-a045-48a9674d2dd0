alert_configs:
  slack:
  - channel: workdesk-event-bulk-upload-tracker-alerts
dag_name: bulk_upload_event_tracker
dag_type: etl
escalation_priority: low
execution_timeout: 115
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06RTRAMJQ4
path: povms/festival/etl/bulk_upload_event_tracker
paused: false
pool: povms_pool
project_name: festival
schedule:
  end_date: '2025-08-18T00:00:00'
  interval: 0 */2 * * *
  start_date: '2025-06-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 46
