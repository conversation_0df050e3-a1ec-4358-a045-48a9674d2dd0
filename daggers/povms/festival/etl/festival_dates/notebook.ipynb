{"cells": [{"cell_type": "code", "execution_count": null, "id": "e5ef2dec-96ba-48b7-b929-6d25a78c32f0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "89947f9f-a6fc-4f4d-be1e-82c2dd8bba5c", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "21e06c91-6961-471f-809a-944e2303aba6", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"calendar_events_India.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "9273e4a6-60ea-46b7-9655-2ac58f9faf52", "metadata": {}, "outputs": [], "source": ["df[\"date\"] = pd.to_datetime(df[\"date\"])\n", "df[\"week_start_date\"] = pd.to_datetime(df[\"week_start_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "b6be46b5-eaeb-4251-a9ed-6af727ea8294", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a2f28a16-07c4-4fd3-aca5-698e509ac759", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "ab5ee18c-2cf6-43dd-baf1-257b6481feb7", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"week_day\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Weekday name\",\n", "    },\n", "    {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date of the festival\"},\n", "    {\n", "        \"name\": \"week_start_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"Week start date\",\n", "    },\n", "    {\n", "        \"name\": \"event\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"event name\",\n", "    },\n", "    {\n", "        \"name\": \"weekend_flag\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"event name\",\n", "    },\n", "    {\n", "        \"name\": \"multi_festive_flag\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"If multiple festivals are on the same date\",\n", "    },\n", "    {\n", "        \"name\": \"region\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"Region it's celebrated\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "84b5bce0-2a72-44a8-b1a5-bfd7593bb7d8", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_dates\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"date\", \"event\"],\n", "    # \"sortkey\": [\"hot_outlet_id\", \"be_hot_outlet_id\", \"item_id\"],\n", "    # \"incremental_key\": \"updated_at\",\n", "    \"partition_key\": [\"date\"],\n", "    \"load_type\": \"truncate\",  # , # append, rebuild, truncate or upsert\n", "    \"table_description\": \"festival dates\",  # Description of the table being sent to redshift\n", "}\n", "pb.to_trino(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "f2e6e9e9-57dd-4367-be70-8680f193f833", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}