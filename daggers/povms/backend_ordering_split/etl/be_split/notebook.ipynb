{"cells": [{"cell_type": "code", "execution_count": null, "id": "815ffe75-e6cb-48de-a245-84afe9ea772c", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "import requests\n", "from requests.exceptions import HTTPError\n", "from tabulate import tabulate\n", "\n", "# start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "83f38d3e-029b-4b7d-9d4f-5edfdd8eb595", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "3ab42301-b354-4d75-aa4e-b4fc31fc6688", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "            except:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "a4661138-a186-4a93-8d7d-129a8bd31eb6", "metadata": {}, "outputs": [], "source": ["def to_sheets(df, sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "            except:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed to sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed to sheet in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "80fb6e15-7888-4c6d-a96c-2609f84b3d38", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-data-inventory-pvt\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c7a9450c-56e9-4d58-9a4d-f0b1afb76e0f", "metadata": {}, "outputs": [], "source": ["def tabulate_print(df):\n", "    print(tabulate(df, headers=\"keys\", tablefmt=\"psql\"))"]}, {"cell_type": "code", "execution_count": null, "id": "7bae2e3a-79fc-46c6-a004-78eafe96bfc9", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "900be5aa-f6dc-4376-9251-e975ac1bd73b", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "f66c56cf-6b33-468f-891f-ac775eb49dc3", "metadata": {}, "outputs": [], "source": ["city_item = from_sheets(\"1tfKQmvTJkqXCWPKxC5XAHjW4UyBAaulK8-cZbH4K2SU\", \"city<>item<>qty - input\")\n", "# city_item = pd.read_csv('Book3.csv')\n", "item_id_all = city_item[\"item_id\"].tolist()\n", "item_id_all_str = \", \".join([str(i_id) for i_id in item_id_all])\n", "# item_id_all_str"]}, {"cell_type": "code", "execution_count": null, "id": "1763dd65-f224-4800-b45a-a6b22ef282a6", "metadata": {}, "outputs": [], "source": ["be_city_split = from_sheets(\n", "    \"1tfKQmvTJkqXCWPKxC5XAHjW4UyBAaulK8-cZbH4K2SU\", \"city<>be<>split - input\"\n", ")\n", "# be_city_split = pd.read_csv('Book2.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "3cc433f4-ec42-4632-810c-668f1f40b15c", "metadata": {}, "outputs": [], "source": ["dag_run_time = pd.to_datetime(datetime.today() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "b24d42a5-6a19-4745-923e-86c1ed314360", "metadata": {}, "outputs": [], "source": ["df1 = pd.DataFrame(city_item)\n", "df2 = pd.DataFrame(be_city_split)\n", "\n", "if df1.shape[0] > 0:\n", "    if df2.shape[0] > 0:\n", "\n", "        df1_new = df1.dropna()\n", "        df2_new = df2.dropna()\n", "\n", "        df1_new[\"total_quantity\"] = df1_new[\"total_quantity\"].astype(float)\n", "        df2_new[\"split\"] = df2_new[\"split\"].astype(float)\n", "\n", "        split_sum_by_city = df2_new.groupby(\"city_name\")[\"split\"].sum().reset_index()\n", "        invalid_splits = split_sum_by_city[\n", "            (split_sum_by_city[\"split\"] < 0.98) | (split_sum_by_city[\"split\"] > 1.02)\n", "        ]\n", "\n", "        if df1.shape[0] == df1_new.shape[0]:\n", "            if df2.shape[0] == df2_new.shape[0]:\n", "                if invalid_splits.shape[0] == 0:\n", "                    merged_df = pd.merge(df1, df2, on=[\"city_name\"], how=\"left\")\n", "\n", "                    merged_df[\"total_quantity\"] = merged_df[\"total_quantity\"].astype(float)\n", "                    merged_df[\"split\"] = merged_df[\"split\"].astype(float)\n", "\n", "                    merged_df[\"total_quantity\"] = merged_df[\"total_quantity\"] * merged_df[\"split\"]\n", "\n", "                    split_df = merged_df[[\"be_facility_id\", \"item_id\", \"total_quantity\"]]\n", "\n", "                    to_sheets(\n", "                        split_df,\n", "                        \"1tfKQmvTJkqXCWPKxC5XAHjW4UyBAaulK8-cZbH4K2SU\",\n", "                        \"be<>item<>qty - output\",\n", "                    )\n", "                    # split_df.to_csv(f\"Book4.csv\", index=False)\n", "\n", "                else:\n", "                    pb.send_slack_message(\n", "                        channel=\"backend-ordering-split\",\n", "                        text=(\n", "                            f\"<!here>\\n\\U0001f61E Invalid split for {invalid_splits} at the time of the run on {dag_run_time}\"\n", "                        ),\n", "                    )\n", "            else:\n", "                pb.send_slack_message(\n", "                    channel=\"backend-ordering-split\",\n", "                    text=(\n", "                        f\"<!here>\\n\\U0001f61E No data or incorrect input for city<>be<>split df was received at the time of the run on {dag_run_time}\"\n", "                    ),\n", "                )\n", "        else:\n", "            pb.send_slack_message(\n", "                channel=\"backend-ordering-split\",\n", "                text=(\n", "                    f\"<!here>\\n\\U0001f61E No data or incorrect input for city<>item df was received at the time of the run on {dag_run_time}\"\n", "                ),\n", "            )\n", "    else:\n", "        print(\"No Input\")\n", "else:\n", "    print(\"No Input\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}