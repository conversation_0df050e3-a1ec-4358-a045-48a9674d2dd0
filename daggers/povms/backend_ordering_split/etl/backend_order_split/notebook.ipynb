{"cells": [{"cell_type": "code", "execution_count": null, "id": "794331b5-4df6-4c86-bfcc-bfa9ebb21ce9", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "import requests\n", "from requests.exceptions import HTTPError\n", "from tabulate import tabulate\n", "\n", "# start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "2b42f715-6006-4267-93b1-5aecdded796b", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "448b6397-b195-4dee-b3d5-2ba1069f8fe8", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "            except:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "3cb84017-aaaf-4dea-a698-9564ff6edebb", "metadata": {}, "outputs": [], "source": ["def to_sheets(df, sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "            except:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed to sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed to sheet in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "a1e88960-7f35-42a6-a1cf-9a32c869df54", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-data-inventory-pvt\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "495fa0cd-8711-4076-b06d-84aa3d223a00", "metadata": {}, "outputs": [], "source": ["def tabulate_print(df):\n", "    print(tabulate(df, headers=\"keys\", tablefmt=\"psql\"))"]}, {"cell_type": "code", "execution_count": null, "id": "26e061c5-fc9a-40c3-ac84-ec7067d139e3", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5d304cde-2bc3-4829-a20a-7d67abb698bc", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "a712d732-1f79-4966-8e83-d859599c80c1", "metadata": {}, "outputs": [], "source": ["city_item = from_sheets(\"1J3fGL3KiPRCAsLIK9uKOsyea_qPASt8SdRsvwQEpClo\", \"city_item_input_main\")\n", "# city_item = pd.read_csv('city_item.csv')\n", "item_id_all = city_item[\"item_id\"].tolist()\n", "item_id_all_str = \", \".join([str(i_id) for i_id in item_id_all])\n", "item_id_all_str"]}, {"cell_type": "code", "execution_count": null, "id": "c7054ab8-97d0-4d49-8083-50c6cdede829", "metadata": {}, "outputs": [], "source": ["raw_data = read_sql_query(\n", "    f\"\"\"\n", "with sd as (\n", "    select * from ars_etls.festival_store_distribution_multi_ptype_active_v2 where festival_name is not null \n", "),\n", "\n", "latest_values as (\n", "    select outlet_id, festival_name, assortment_type, max(updated_at) as updated_at from sd group by 1,2,3\n", "),\n", "\n", "final_store_distribution as (\n", "    select distinct sd.outlet_id,\n", "        tax_location_id city_id,\n", "        city_name,\n", "        lv.festival_name,\n", "        lv.assortment_type,\n", "        avg_orders,\n", "        weighted_val\n", "    from latest_values lv\n", "    left join sd on lv.updated_at = sd.updated_at\n", "        and lv.festival_name = sd.festival_name\n", "        and lv.outlet_id = sd.outlet_id\n", "        and lv.assortment_type = sd.assortment_type\n", "),\n", "\n", "proposed_store_movement as (\n", "    select distinct outlet_id,\n", "        festival_name,\n", "        proposed_be_facility_id\n", "    from supply_etls.festival_tea_tagging\n", "    where festival_name is not null\n", "    and active = 1\n", "),\n", "\n", "\n", "be_fo as (\n", "    select distinct co.facility_id as be_facility_id,\n", "        be_outlet_id,\n", "        co.name as be_name,\n", "        fe_facility_id,\n", "        fe_outlet_id\n", "    from (\n", "        select coalesce(st.be_outlet, ct.be_outlet) as be_outlet_id, \n", "            pfom.facility_id as fe_facility_id, \n", "            pfom.outlet_id as fe_outlet_id\n", "        from po.physical_facility_outlet_mapping pfom\n", "        inner join retail.console_outlet co on co.id = pfom.outlet_id and business_type_id = 7\n", "        left join (select distinct frontend_facility_id as facility_id, backend_outlet_id as be_outlet from rpc.transfer_tag_rules st where category in ('GROCERY') and item_id = 0 and st.active = True) st on st.facility_id = pfom.facility_id\n", "        left join (select distinct city_id, backend_outlet_id as be_outlet from rpc.transfer_tag_rules st where category in ('GROCERY') and frontend_facility_id = 0 and item_id = 0 and st.active = True) ct on ct.city_id = pfom.city_id\n", "        where pfom.active = 1 \n", "            and co.active = 1\n", "            and pfom.ars_active = 1\n", "        ) t1\n", "    left join retail.console_outlet co on co.id = t1.be_outlet_id \n", "),\n", "\n", "tea_tagging0 as (\n", "    select be_facility_id,\n", "        be_outlet_id,\n", "        be_name,\n", "        fe_facility_id,\n", "        fe_outlet_id,\n", "        co.name as fe_name,\n", "        count(be_facility_id) over (partition by fe_facility_id, fe_outlet_id, co.name) as count_teas\n", "    from be_fo\n", "    left join retail.console_outlet co on co.id = be_fo.fe_outlet_id \n", "    \n", "),\n", "\n", "tea_tagging1 as (\n", "    select be_facility_id,\n", "        be_outlet_id,\n", "        be_name,\n", "        fe_facility_id,\n", "        fe_outlet_id,\n", "        fe_name,\n", "        count_teas\n", "    from tea_tagging0\n", "    where \n", "        (count_teas = 1) or (count_teas = 2 and (be_facility_id = 2076 or be_facility_id = 1872)) -- Split assortment case in Dehradun, Pune\n", "        \n", "),\n", "\n", "tea_tagging2 as (\n", "    select * from \n", "    (select * from tea_tagging1) \n", "    cross join \n", "    (select distinct festival_name, assortment_type from latest_values)\n", "),\n", "\n", "\n", "\n", "tea_tagging3 as (\n", "    select be_facility_id as curr_be_facility_id,\n", "        proposed_be_facility_id,\n", "        tea_tagging2.festival_name,\n", "        tea_tagging2.assortment_type,\n", "        coalesce(proposed_be_facility_id, be_facility_id) as be_facility_id,\n", "        fe_outlet_id,\n", "        fe_name,\n", "        count_teas\n", "    from tea_tagging2\n", "    left join proposed_store_movement psm on psm.outlet_id = tea_tagging2.fe_outlet_id \n", "        and psm.festival_name = tea_tagging2.festival_name\n", "        -- and psm.assortment_type = be_mapping2.assortment_type\n", "),\n", "\n", "\n", "split as (\n", "    select tea_tagging3.festival_name,\n", "        tea_tagging3.assortment_type,\n", "        be_facility_id,\n", "        city_id,\n", "        city_name, \n", "        sum(weighted_val) as weighted_val\n", "    from tea_tagging3\n", "    inner join final_store_distribution fsd on fsd.outlet_id = tea_tagging3.fe_outlet_id\n", "        and fsd.festival_name = tea_tagging3.festival_name\n", "        and fsd.assortment_type = tea_tagging3.assortment_type\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "select festival_name,\n", "    assortment_type,\n", "    city_id,\n", "    city_name, \n", "    be_facility_id,\n", "    -- weighted_val,\n", "    -- sum(weighted_val) over (partition by city_id, city_name,festival_name) as fc_weighted_val,\n", "    ((1.0000 * weighted_val) / sum(weighted_val) over (partition by city_id, city_name,festival_name,assortment_type)) as split \n", "from split\n", "where assortment_type in ('HYBRID', 'EXPRESS')    \n", "    \"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "550b3c07-30df-4a41-9445-c930b407e2fd", "metadata": {}, "outputs": [], "source": ["df1 = pd.DataFrame(city_item)\n", "df2 = pd.DataFrame(raw_data)\n", "\n", "merged_df = pd.merge(df1, df2, on=[\"festival_name\", \"assortment_type\", \"city_name\"], how=\"inner\")\n", "\n", "merged_df[\"total_quantity\"] = merged_df[\"total_quantity\"] * merged_df[\"split\"]\n", "\n", "split_df = merged_df[[\"be_facility_id\", \"item_id\", \"total_quantity\"]]\n", "\n", "split_df"]}, {"cell_type": "code", "execution_count": null, "id": "4c4945df-383d-453d-a889-6145055a2de9", "metadata": {}, "outputs": [], "source": ["# split_df.to_csv(f\"be_iten_output.csv\", index=False)\n", "to_sheets(split_df, \"1J3fGL3KiPRCAsLIK9uKOsyea_qPASt8SdRsvwQEpClo\", \"fc_item_output_main\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}