alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: backend_order_split
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07KUHSCH8W
path: povms/backend_ordering_split/etl/backend_order_split
paused: true
pool: povms_pool
project_name: backend_ordering_split
schedule:
  end_date: '2024-12-16T00:00:00'
  interval: 0 0 1 8 *
  start_date: '2024-09-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
