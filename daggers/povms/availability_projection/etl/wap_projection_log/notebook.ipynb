{"cells": [{"cell_type": "code", "execution_count": null, "id": "23c5a4da-40bf-4705-917a-ab88e0d92b00", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "import time\n", "import gc\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "a4f82289-0c27-41d6-bf5e-942dc1ea2381", "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "7fbd4367-2a94-47d6-8d69-f073d0994157", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", round((end - start) / 60, 2), \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b3c1e89f-f312-4777-aa09-8079a15a5f31", "metadata": {}, "outputs": [], "source": ["def avl():\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "    sql = \"\"\"\n", "--Filtering Stores to be considered \n", "    -- Cosidering only stores with carts >= 100\n", "with store_carts as (\n", "    select outlet_id as outlet_id, \n", "        cast(avg(c) as int) as carts \n", "    from (\n", "            select outlet_id, \n", "                date(cart_checkout_ts_ist) dt, \n", "                count(distinct order_id) c \n", "            from dwh.fact_sales_order_details x \n", "            where cart_checkout_ts_ist  >= current_date-interval '7' day\n", "                and order_create_ts_ist >=  current_date-interval '7' day\n", "                and order_create_dt_ist >= current_date-interval '7' day\n", "                and order_current_status = 'DELIVERED' \n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "            group by 1,2\n", "        ) \n", "    group by 1\n", "),\n", "\n", "active_outlets as (\n", "\n", "select * from store_carts where carts>=100\n", "\n", "),\n", "\n", "-- Raw data at be<>fe<>item<>inv\n", "    -- Considering only packaged goods\n", "    -- And also not considering blocked inv\n", "pg_ass as (\n", "    select item_id from supply_etls.item_details where assortment_type = 'Packaged Goods'\n", "),\n", "    \n", "    -- Assortment Details of only packaged goods and active outlets\n", "pfma_ass as (\n", "    select pfom.facility_id, pfom.outlet_id, pcma.item_id\n", "    from \n", "    (select item_id,facility_id from rpc.product_facility_master_assortment \n", "    where master_assortment_substate_id in (1) and active = 1) pcma \n", "        inner join\n", "    pg_ass on pg_ass.item_id = pcma.item_id\n", "        inner join \n", "    (select * from po.physical_facility_outlet_mapping where active = 1) pfom on pcma.facility_id = pfom.facility_id\n", "        inner join\n", "    active_outlets ao on ao.outlet_id = pfom.outlet_id\n", "   \n", "),\n", "\n", "be_fe_item as\n", "(select iotm.outlet_id,iotm.item_id,cast(iotm.tag_value as integer) as be_outlet_id,pa.facility_id,pfom.facility_id as be_facility_id\n", "    from \n", "    (select item_id,tag_value,outlet_id\n", "    from rpc.item_outlet_tag_mapping where tag_type_id = 8 and active = 1 and lake_active_record = True) iotm \n", "        inner join \n", "    pfma_ass pa on pa.outlet_id = iotm.outlet_id and pa.item_id = iotm.item_id\n", "        inner join\n", "    (select * from po.physical_facility_outlet_mapping where active = 1) pfom on  cast(iotm.tag_value as integer) = pfom.outlet_id\n", "),\n", "-- Inventory part starts\n", "\n", "actual_inv as (\n", "    select item_id, outlet_id, quantity from ims.ims_item_inventory where active = 1 and lake_active_record\n", "),\n", "\n", "blocked_inv as (\n", "    select outlet_id, item_id, quantity from ims.ims_item_blocked_inventory where blocked_type in (1,2,5) and lake_active_record\n", "),\n", "\n", "fbi as (\n", "    select outlet_id, item_id, sum(quantity) as bq\n", "    from blocked_inv\n", "    --where blocked_type in (1,2,5) and lake_active_record  --and outlet_id in (3232)\n", "    group by 1,2\n", "),\n", "\n", "final_inv as (\n", "    select ai.outlet_id,bfi.be_outlet_id, pfom.facility_id as be_facility_id ,ai.item_id, ai.quantity as aq, \n", "    bq, case when ai.quantity - coalesce(bq,0) < 0 then 0 else ai.quantity - coalesce(bq,0) end as current_inv,\n", "            case when ai.quantity - coalesce(bq,0) <= 0 then 0 else 1 end inv_flag\n", "    from actual_inv ai\n", "    left join fbi bi on ai.item_id = bi.item_id and ai.outlet_id = bi.outlet_id\n", "    inner join \n", "    be_fe_item bfi on bfi.item_id = ai.item_id and bfi.outlet_id = ai.outlet_id\n", "    inner join \n", "    (select * from po.physical_facility_outlet_mapping where active =1) pfom on pfom.outlet_id = bfi.be_outlet_id\n", "    -- where ai.item_id in (10000357)\n", "),\n", "\n", "\n", "\n", "-- be_hot,fe,item,inv\n", "\n", "-- <PERSON><PERSON>\n", "    -- Considering only sto's which are created one day before current_date\n", "\n", "sto_transit_time as (\n", "with node_facility_mapping as (\n", "    select cast(logistic_node_id as varchar) as store_id,\n", "            facility_id, f.name as facility_name\n", "    from retail.console_outlet_logistic_mapping lm\n", "    inner join retail.console_outlet co on co.id = lm.outlet_id\n", "    inner join retail.warehouse_facility f on f.id = co.facility_id\n", "    where lm.lake_active_record and co.lake_active_record and f.lake_active_record\n", "),\n", "\n", "in_transit_time as (\n", "    select consignment_id, source_store_id, destination_store_id, \n", "            date_diff('hour', min(logs.install_ts), max(logs.install_ts)) + 3  as delivery_tat ---- add 3 for grn_time and 5 hours for 90%% accuracy\n", "    from transit_server.transit_consignment cons\n", "    inner join transit_server.transit_consignment_state_logs logs on cons.id = logs.consignment_id\n", "    where logs.lake_active_record and cons.lake_active_record\n", "    and cons.insert_ds_ist >= cast(current_date - interval '27' day as varchar)\n", "    and logs.insert_ds_ist >= cast(current_date - interval '27' day as varchar)\n", "    and cons.state = 'COMPLETED'\n", "    and logs.to_state in ('COMPLETED','READY_FOR_DISPATCH')\n", "    GROUP BY 1,2,3\n", "    HAVING count(*) > 1\n", "    AND date_diff('hour', min(logs.install_ts), max(logs.install_ts)) > 0\n", "),\n", "cleaned_data as (\n", "    select iit.source_store_id, iit.destination_store_id, \n", "            approx_percentile(delivery_tat, 0.5) as median_tat\n", "    from in_transit_time iit\n", "    group by 1,2\n", "),\n", "\n", "final_tt as (\n", "    select sn.facility_id as sender_facility_id, sn.facility_name as sender_facility_name, \n", "            dn.facility_id as destination_facility_id, dn.facility_name as destination_facility_name, \n", "            median_tat\n", "    from cleaned_data cd\n", "    inner join node_facility_mapping sn on sn.store_id = cd.source_store_id\n", "    inner join node_facility_mapping dn on dn.store_id = cd.destination_store_id\n", "    -- inner join retail.console_outlet co on cast(co.id as varchar) = dn.store_id and co.lake_active_record and co.active = 1 and co.business_type_id = 7\n", ")\n", "select * from final_tt),\n", "dt AS\n", "    (SELECT id as sto_id,\n", "            max(dispatch_time) as dispatch_time\n", "     FROM po.sto\n", "     WHERE created_at >= current_date - interval '2' day \n", "     and lake_active_record\n", "     and active = 1\n", "     group by 1\n", "     ) ,\n", "\n", "\n", "\n", "-- sto_time as\n", "-- (SELECT date_diff('minute',min_by(ssl.created_at,ssl.sto_state),max_by(ssl.created_at,ssl.sto_state))/60 AS time_in_hrs,ssl.sto_id,\n", "--         sd.merchant_outlet_id\n", "--         from ims.ims_sto_state_log ssl inner join ims.ims_sto_details sd on ssl.sto_id = sd.sto_id\n", "--         where ssl.created_at <=current_date - interval '2' day and ssl.created_at >= current_date - interval '27' day\n", "--         and ssl.sto_state in (1,4) group by ssl.sto_id,sd.merchant_outlet_id\n", "-- ),\n", "   \n", "-- sto_avg_time as\n", "-- (select avg(time_in_hrs) as time_avg,approx_percentile(time_in_hrs, 0.5) AS median_value,\n", "-- min_by(sto_id,time_in_hrs),min(time_in_hrs),max_by(sto_id,time_in_hrs),\n", "-- max(time_in_hrs), merchant_outlet_id from sto_time where time_in_hrs>0\n", "-- group by merchant_outlet_id \n", "-- ),\n", "-- fe,item,expected\n", "sto_details as (\n", "    select isd.merchant_outlet_id as outlet_id, isi.item_id, isi.reserved_quantity, isi.billed_quantity,\n", "    isd.created_at,\n", "    date_add('minute',330,isd.created_at) as created_at_ist,\n", "    date_add('hour',coalesce(stt.median_tat,15),dt.dispatch_time+ interval '330' minute) as expected_time_stamp_ist,\n", "    -- + interval '330' minute as expected_time_stamp_ist,\n", "    sequence(hour(current_timestamp), hour(current_timestamp)+ \n", "    (case \n", "    when date_diff('hour', current_timestamp, date_add('hour',coalesce(stt.median_tat,15),dt.dispatch_time + interval '330' minute)) >0 then\n", "    date_diff('hour', current_timestamp, date_add('hour',coalesce(stt.median_tat,15),dt.dispatch_time + interval '330' minute))\n", "    else 0 end)\n", "    )\n", "     AS hour_tuple,\n", "    coalesce(bfi.be_outlet_id,-1) as be_outlet_id,\n", "    coalesce(bfi.be_facility_id,-1) as be_facility_id\n", "    from (select sto_id,merchant_outlet_id ,created_at\n", "    from ims.ims_sto_details where created_at >= current_date - interval '2' day and lake_active_record and sto_state in (1,2,5)) isd\n", "    inner join \n", "    (select sto_id,item_id, reserved_quantity,billed_quantity\n", "    from ims.ims_sto_item where created_at >= current_date - interval '2' day and lake_active_record) isi\n", "    on isd.sto_id = isi.sto_id\n", "    inner join \n", "    (select sto_id,dispatch_time from dt \n", "    ) dt on dt.sto_id = isi.sto_id\n", "    inner join be_fe_item bfi on bfi.outlet_id = isd.merchant_outlet_id and bfi.item_id = isi.item_id\n", "    left join\n", "    sto_transit_time stt on stt.sender_facility_id = bfi.be_facility_id and stt.destination_facility_id = bfi.facility_id\n", "    -- sto_avg_time sat on sat.merchant_outlet_id = isd.merchant_outlet_id\n", "    -- inner join be_fe_item bfi on bfi.outlet_id = isd.merchant_outlet_id and bfi.item_id = isi.item_id\n", "),\n", "sto_details_max as (\n", "    select be_outlet_id,outlet_id,max(expected_time_stamp_ist) as max_expected_time_stamp_ist,\n", "    (sequence(hour(current_timestamp), hour(current_timestamp)+\n", "    case\n", "    when date_diff('hour', current_timestamp, max(expected_time_stamp_ist)) >0 then\n", "    date_diff('hour', current_timestamp, max(expected_time_stamp_ist))\n", "    else 0 end)\n", "    )\n", "    AS hour_tuple_max\n", "    from sto_details where reserved_quantity>0 or billed_quantity>0\n", "    group by 1,2\n", "    having max(expected_time_stamp_ist) >=current_timestamp\n", "),\n", "sto_details_max_final as (\n", "    select be_outlet_id,outlet_id,max_expected_time_stamp_ist,\n", "     hour_value %% 24 as hour_final\n", "    from\n", "        sto_details_max,UNNEST(hour_tuple_max) as t(hour_value)\n", ") ,\n", "\n", "-- cpd details\n", "cpd_cte as (\n", "select outlet_id,item_id,aps_adjusted as aqs,cpd\n", "from ars.outlet_item_aps_derived_cpd where insert_ds_ist = cast(current_date as varchar)\n", "),\n", "bhw_cte as (\n", "select backend_facility_id as be_facility_id,order_hour as hour_,cast(weights as real) as bhw,\n", "pfom.outlet_id as be_outlet_id\n", "from (select * from supply_etls.backend_hour_weights\n", "where updated_at = (select max(updated_at) from supply_etls.backend_hour_weights)) bhw_\n", "inner join \n", "(select * from po.physical_facility_outlet_mapping where active = 1) pfom on cast(cast(bhw_.backend_facility_id as double) as integer) = pfom.facility_id\n", "\n", "),\n", "biw_cte as (\n", "select cast(backend_facility_id as integer) as be_facility_id,item_id,cast(weights as real)  as biw,weights\n", "from supply_etls.backend_item_weights\n", "where updated_at = (select max(updated_at) from supply_etls.backend_item_weights)\n", "),\n", "\n", "\n", "sto_depl as (\n", "select bc.be_outlet_id,outlet_id,sum(bhw) as mul\n", "    -- select sdmf.be_outlet_id,sdmf.outlet_id,sdmf.hour_final,count(*) as no\n", "    from sto_details_max_final sdmf inner join\n", "    bhw_cte bc on\n", "            bc.hour_ = sdmf.hour_final and bc.be_outlet_id = sdmf.be_outlet_id\n", "    group by\n", "    1,2\n", "),\n", "\n", "\n", "hour_multiplier as (\n", "select cast(be_facility_id as integer) as be_facility_id , \n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp)+1)%%24, \n", "    (extract(hour from current_timestamp)+2)%%24,\n", "    (extract(hour from current_timestamp)+3)%%24, \n", "    (extract(hour from current_timestamp)+4)%%24)\n", "then bhw else 0 end) as w_4,\n", "\n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp) + 1) %% 24,\n", "    (extract(hour from current_timestamp) + 2) %% 24,\n", "    (extract(hour from current_timestamp) + 3) %% 24,\n", "    (extract(hour from current_timestamp) + 4) %% 24,\n", "    (extract(hour from current_timestamp) + 5) %% 24,\n", "    (extract(hour from current_timestamp) + 6) %% 24,\n", "    (extract(hour from current_timestamp) + 7) %% 24,\n", "    (extract(hour from current_timestamp) + 8) %% 24\n", ")\n", "then bhw else 0 end) as w_8,\n", "\n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp) + 1) %% 24,\n", "    (extract(hour from current_timestamp) + 2) %% 24,\n", "    (extract(hour from current_timestamp) + 3) %% 24,\n", "    (extract(hour from current_timestamp) + 4) %% 24,\n", "    (extract(hour from current_timestamp) + 5) %% 24,\n", "    (extract(hour from current_timestamp) + 6) %% 24,\n", "    (extract(hour from current_timestamp) + 7) %% 24,\n", "    (extract(hour from current_timestamp) + 8) %% 24,\n", "    (extract(hour from current_timestamp) + 9) %% 24,\n", "    (extract(hour from current_timestamp) + 10) %% 24,\n", "    (extract(hour from current_timestamp) + 11) %% 24,\n", "    (extract(hour from current_timestamp) + 12) %% 24\n", ")\n", "then bhw else 0 end) as w_12,\n", "\n", "sum(case when hour_ in (\n", "    extract(hour from current_timestamp),\n", "    (extract(hour from current_timestamp) + 1) %% 24,\n", "    (extract(hour from current_timestamp) + 2) %% 24,\n", "    (extract(hour from current_timestamp) + 3) %% 24,\n", "    (extract(hour from current_timestamp) + 4) %% 24,\n", "    (extract(hour from current_timestamp) + 5) %% 24,\n", "    (extract(hour from current_timestamp) + 6) %% 24,\n", "    (extract(hour from current_timestamp) + 7) %% 24,\n", "    (extract(hour from current_timestamp) + 8) %% 24,\n", "    (extract(hour from current_timestamp) + 9) %% 24,\n", "    (extract(hour from current_timestamp) + 10) %% 24,\n", "    (extract(hour from current_timestamp) + 11) %% 24,\n", "    (extract(hour from current_timestamp) + 12) %% 24,\n", "    (extract(hour from current_timestamp) + 13) %% 24,\n", "    (extract(hour from current_timestamp) + 14) %% 24,\n", "    (extract(hour from current_timestamp) + 15) %% 24,\n", "    (extract(hour from current_timestamp) + 16) %% 24\n", ")\n", "then bhw else 0 end) as w_16\n", "\n", "from bhw_cte group by be_facility_id\n", "),\n", "\n", "raw_depl_sto as (\n", "select fi.be_facility_id, fi.be_outlet_id,fi.outlet_id,fi.item_id, fi.inv_flag,fi.current_inv,\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_4,0) <0.5 then 0 else 1 end as depl_4_flag,\n", "--case when fi.current_inv=1 and coalesce(cpd_cte.aqs,0)<1 and fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_4,0) <0.5\n", "--then fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_16,0) else Null end as temp_test,\n", "\n", "cpd_cte.aqs,\n", "\n", "hm.w_4,\n", "hm.w_8,\n", "hm.w_12,\n", "hm.w_16,\n", "\n", "\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_8,0) <0.5 then 0 else 1 end as depl_8_flag,\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_12,0) <0.5 then 0 else 1 end as depl_12_flag,\n", "case when fi.current_inv-coalesce(cpd_cte.aqs,0)*coalesce(hm.w_16,0) <0.5 then 0 else 1 end as depl_16_flag,\n", "case when coalesce(expected_time_stamp_ist,current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '4' hour  and \n", "    coalesce(greatest(sd.reserved_quantity,sd.billed_quantity),0) > 0 then 1 else 0 end as sto_4_flag,\n", "case when coalesce(expected_time_stamp_ist,current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '8' hour  and \n", "    coalesce(greatest(sd.reserved_quantity,sd.billed_quantity),0) > 0 then 1 else 0 end as sto_8_flag,\n", "case when coalesce(expected_time_stamp_ist,current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '12' hour  and \n", "    coalesce(greatest(sd.reserved_quantity,sd.billed_quantity),0) > 0 then 1 else 0 end as sto_12_flag,\n", "case when coalesce(expected_time_stamp_ist,current_timestamp - interval '1' hour) \n", "    between current_timestamp and current_timestamp + interval '16' hour  and \n", "    coalesce(greatest(sd.reserved_quantity,sd.billed_quantity),0) > 0 then 1 else 0 end as sto_16_flag,\n", "case when \n", "    fi.current_inv\n", "     -coalesce(cpd_cte.aqs,0)*\n", "       coalesce(sdp.mul,0)\n", "        <0.5 \n", "    then 0 else 1 end as sto_connect_flag_depl,\n", "case when \n", "    coalesce(greatest(sd.reserved_quantity,sd.billed_quantity),0) > 0 \n", "    and\n", "    coalesce(expected_time_stamp_ist,current_timestamp - interval '1' hour) > current_timestamp\n", "    then 1 else 0 end as sto_connect,\n", "coalesce(max_expected_time_stamp_ist,date '1990-01-01' )     as open_sto_connect_time_stamp\n", "  \n", "from \n", "final_inv fi left join\n", "cpd_cte on cpd_cte.outlet_id = fi.outlet_id and cpd_cte.item_id = fi.item_id\n", "left join hour_multiplier hm on hm.be_facility_id = fi.be_facility_id\n", "left join sto_details sd on sd.outlet_id = fi.outlet_id and sd.item_id = fi.item_id\n", "left join sto_details_max sdm on sdm.outlet_id = fi.outlet_id and sdm.be_outlet_id = fi.be_outlet_id\n", "left join sto_depl sdp on sdp.be_outlet_id = fi.be_outlet_id and sdp.outlet_id = fi.outlet_id\n", "\n", "\n", "),\n", "wap_agg as (\n", "select rds.be_facility_id, rds.be_outlet_id as hot_be_outlet_id, rds.outlet_id, \n", "case when sum(coalesce(biw,0)) = 0 then sum(inv_flag*coalesce(biw,0)) else sum(inv_flag*coalesce(biw,0))/sum(coalesce(biw,0)) end as on_hand_avl,\n", "-- sum(inv_flag*coalesce(biw,0))/sum(coalesce(biw,0)) as on_hand_avl,\n", "sum(inv_flag) as inv_flag,\n", "case when sum(coalesce(biw,0)) = 0 then\n", "sum(greatest(sto_connect_flag_depl,sto_connect)*coalesce(biw,0))\n", "else sum(greatest(sto_connect_flag_depl,sto_connect)*coalesce(biw,0))/sum(coalesce(biw,0)) end as sto_connect,\n", "\n", "max(open_sto_connect_time_stamp) as last_sto_connect_time,\n", "\n", "\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_4_flag,sto_4_flag)*coalesce(biw,0)) \n", "else sum(greatest(depl_4_flag,sto_4_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_4_avl,\n", "sum(case when inv_flag>0 and depl_4_flag<0.5 then 1 else 0 end) as ct_4_oos,\n", "sum(case when rds.current_inv=1 and depl_4_flag<0.5 then 1 else 0 end) as ct_4_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_4_flag<0.5 and sto_4_flag>0 then 1 else 0 end ) as sto_4_oos,\n", "sum(case when rds.current_inv=1 and depl_4_flag<0.5 and sto_4_flag > 0 then 1 else 0 end) as sto_4_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_4_flag>0 then 1 else 0 end ) as sto_4_oos_on_hand,\n", "sum(depl_4_flag) as depl_4_flag,\n", "sum(sto_4_flag) as sto_4_flag,\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_8_flag,sto_8_flag)*coalesce(biw,0)) else \n", "sum(greatest(depl_8_flag,sto_8_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_8_avl,\n", "sum(case when inv_flag>0 and depl_8_flag<0.5 then 1 else 0 end) as ct_8_oos,\n", "sum(case when rds.current_inv=1 and depl_8_flag<0.5 then 1 else 0 end) as ct_8_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_8_flag<0.5 and sto_8_flag>0 then 1 else 0 end ) as sto_8_oos,\n", "sum(case when rds.current_inv=1 and depl_8_flag<0.5 and sto_8_flag > 0 then 1 else 0 end) as sto_8_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_8_flag>0 then 1 else 0 end ) as sto_8_oos_on_hand,\n", "sum(depl_8_flag) as depl_8_flag,\n", "sum(sto_8_flag) as sto_8_flag,\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_12_flag,sto_12_flag)*coalesce(biw,0)) else \n", "sum(greatest(depl_12_flag,sto_12_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_12_avl,\n", "sum(case when inv_flag>0 and depl_12_flag<0.5 then 1 else 0 end) as ct_12_oos,\n", "sum(case when rds.current_inv=1 and depl_12_flag<0.5 then 1 else 0 end) as ct_12_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_12_flag<0.5 and sto_12_flag>0 then 1 else 0 end ) as sto_12_oos,\n", "sum(case when rds.current_inv=1 and depl_12_flag<0.5 and sto_12_flag > 0 then 1 else 0 end) as sto_12_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_12_flag>0 then 1 else 0 end ) as sto_12_oos_on_hand,\n", "sum(depl_12_flag) as depl_12_flag,\n", "sum(sto_12_flag) as sto_12_flag,\n", "case when sum(coalesce(biw,0)) = 0 then sum(greatest(depl_16_flag,sto_16_flag)*coalesce(biw,0)) else \n", "sum(greatest(depl_16_flag,sto_16_flag)*coalesce(biw,0))/sum(coalesce(biw,0)) end as ct_16_avl,\n", "sum(case when inv_flag>0 and depl_16_flag<0.5 then 1 else 0 end) as ct_16_oos,\n", "sum(case when rds.current_inv=1 and depl_16_flag<0.5 then 1 else 0 end) as ct_16_on_hand_1_oos,\n", "sum(case when inv_flag>0 and depl_16_flag<0.5 and sto_16_flag>0 then 1 else 0 end ) as sto_16_oos,\n", "sum(case when rds.current_inv=1 and depl_16_flag<0.5 and sto_16_flag > 0 then 1 else 0 end) as sto_16_on_hand_1_oos,\n", "sum(case when inv_flag=0 and sto_16_flag>0 then 1 else 0 end ) as sto_16_oos_on_hand,\n", "sum(depl_16_flag) as depl_16_flag,\n", "sum(sto_16_flag) as sto_16_flag\n", "from \n", "raw_depl_sto rds left join biw_cte bc\n", "on rds.be_facility_id = bc.be_facility_id and rds.item_id = bc.item_id\n", "group by 1,2,3\n", ")\n", "select * from wap_agg \n", "--where test<0.5\n", "--select * from raw_depl_sto\n", "--where be_outlet_id = 4991 and outlet_id = 2244\n", "--and temp_test is not null\n", "\n", "    \"\"\"\n", "    return read_sql_query(sql, trino)\n", "\n", "\n", "# read_sql_query(sql, trino)\n", "df = avl()\n", "print(df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "62a88941-2ce6-48dd-ad02-ec3cec3cc259", "metadata": {}, "outputs": [], "source": ["df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "941ae833-05c5-42a9-8f93-e27b5bd57565", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "edda0c13-db2f-465b-82fd-58ffc711fad0", "metadata": {}, "outputs": [], "source": ["df.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b0aea25f-b4ec-409f-bb34-20f29fb136f8", "metadata": {}, "outputs": [], "source": ["col = [\n", "    \"be_facility_id\",\n", "    \"hot_be_outlet_id\",\n", "    \"outlet_id\",\n", "    \"on_hand_avl\",\n", "    \"inv_flag\",\n", "    \"ct_4_avl\",\n", "    \"ct_4_on_hand_1_oos\",\n", "    \"sto_4_oos\",\n", "    \"sto_4_on_hand_1_oos\",\n", "    \"sto_4_oos_on_hand\",\n", "    \"depl_4_flag\",\n", "    \"sto_4_flag\",\n", "    \"ct_8_avl\",\n", "    \"ct_8_on_hand_1_oos\",\n", "    \"sto_8_oos\",\n", "    \"sto_8_on_hand_1_oos\",\n", "    \"sto_8_oos_on_hand\",\n", "    \"depl_8_flag\",\n", "    \"sto_8_flag\",\n", "    \"ct_12_avl\",\n", "    \"ct_12_on_hand_1_oos\",\n", "    \"sto_12_oos\",\n", "    \"sto_12_on_hand_1_oos\",\n", "    \"sto_12_oos_on_hand\",\n", "    \"depl_12_flag\",\n", "    \"sto_12_flag\",\n", "    \"ct_16_avl\",\n", "    \"ct_16_on_hand_1_oos\",\n", "    \"sto_16_oos\",\n", "    \"sto_16_on_hand_1_oos\",\n", "    \"sto_16_oos_on_hand\",\n", "    \"depl_16_flag\",\n", "    \"sto_16_flag\",\n", "    \"updated_at\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "49a93b04-a355-41c3-b640-4a088d67baa8", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"be_facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "    {\"name\": \"hot_be_outlet_id\", \"type\": \"INTEGER\", \"description\": \"Hot outlet ID\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Outlet ID\"},\n", "    {\"name\": \"on_hand_avl\", \"type\": \"DOUBLE\", \"description\": \"Available on hand\"},\n", "    {\"name\": \"inv_flag\", \"type\": \"INTEGER\", \"description\": \"Inventory flag\"},\n", "    {\"name\": \"ct_4_avl\", \"type\": \"DOUBLE\", \"description\": \"Count 4 available\"},\n", "    {\"name\": \"ct_4_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"ct_4_on_hand_1_oos\"},\n", "    {\"name\": \"sto_4_oos\", \"type\": \"INTEGER\", \"description\": \"sto_4_oos\"},\n", "    {\"name\": \"sto_4_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"sto_4_on_hand_1_oos\"},\n", "    {\"name\": \"sto_4_oos_on_hand\", \"type\": \"INTEGER\", \"description\": \"sto_4_oos_on_hand\"},\n", "    {\"name\": \"depl_4_flag\", \"type\": \"INTEGER\", \"description\": \"Depletion 4 flag\"},\n", "    {\"name\": \"sto_4_flag\", \"type\": \"INTEGER\", \"description\": \"Stock 4 flag\"},\n", "    {\"name\": \"ct_8_avl\", \"type\": \"DOUBLE\", \"description\": \"Count 8 available\"},\n", "    {\"name\": \"ct_8_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"ct_8_on_hand_1_oos\"},\n", "    {\"name\": \"sto_8_oos\", \"type\": \"INTEGER\", \"description\": \"sto_8_oos\"},\n", "    {\"name\": \"sto_8_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"sto_8_on_hand_1_oos\"},\n", "    {\"name\": \"sto_8_oos_on_hand\", \"type\": \"INTEGER\", \"description\": \"sto_8_oos_on_hand\"},\n", "    {\"name\": \"depl_8_flag\", \"type\": \"INTEGER\", \"description\": \"Depletion 8 flag\"},\n", "    {\"name\": \"sto_8_flag\", \"type\": \"INTEGER\", \"description\": \"Stock 8 flag\"},\n", "    {\"name\": \"ct_12_avl\", \"type\": \"DOUBLE\", \"description\": \"Count 12 available\"},\n", "    {\"name\": \"ct_12_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"ct_12_on_hand_1_oos\"},\n", "    {\"name\": \"sto_12_oos\", \"type\": \"INTEGER\", \"description\": \"sto_12_oos\"},\n", "    {\"name\": \"sto_12_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"sto_12_on_hand_1_oos\"},\n", "    {\"name\": \"sto_12_oos_on_hand\", \"type\": \"INTEGER\", \"description\": \"sto_12_oos_on_hand\"},\n", "    {\"name\": \"depl_12_flag\", \"type\": \"INTEGER\", \"description\": \"Depletion 12 flag\"},\n", "    {\"name\": \"sto_12_flag\", \"type\": \"INTEGER\", \"description\": \"Stock 12 flag\"},\n", "    {\"name\": \"ct_16_avl\", \"type\": \"DOUBLE\", \"description\": \"Count 16 available\"},\n", "    {\"name\": \"ct_16_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"ct_16_on_hand_1_oos\"},\n", "    {\"name\": \"sto_16_oos\", \"type\": \"INTEGER\", \"description\": \"sto_16_oos\"},\n", "    {\"name\": \"sto_16_on_hand_1_oos\", \"type\": \"INTEGER\", \"description\": \"sto_16_on_hand_1_oos\"},\n", "    {\"name\": \"sto_16_oos_on_hand\", \"type\": \"INTEGER\", \"description\": \"sto_16_oos_on_hand\"},\n", "    {\"name\": \"depl_16_flag\", \"type\": \"INTEGER\", \"description\": \"Depletion 16 flag\"},\n", "    {\"name\": \"sto_16_flag\", \"type\": \"INTEGER\", \"description\": \"Stock 16 flag\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Last updated timestamp\"},\n", "]\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"wap_logs_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"hot_be_outlet_id\", \"outlet_id\", \"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains wap\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c1129bd5-31c0-4065-8916-8ad78359f241", "metadata": {}, "outputs": [], "source": ["start_time = time.time()\n", "pb.to_trino(df[col], **kwargs)\n", "end_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "7d669610-98d0-4f90-bdc7-ebdfb5ea6d46", "metadata": {}, "outputs": [], "source": ["execution_time = end_time - start_time"]}, {"cell_type": "code", "execution_count": null, "id": "fe69a570-042a-444f-901f-4d42b2139b68", "metadata": {}, "outputs": [], "source": ["print(\"Execution time: in sec\", execution_time)"]}, {"cell_type": "code", "execution_count": null, "id": "82537dbf-2be9-4e80-9d65-6f9668cdccd6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}