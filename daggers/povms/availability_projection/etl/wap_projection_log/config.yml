alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: wap_projection_log
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07F2C300H5
path: povms/availability_projection/etl/wap_projection_log
paused: false
pool: povms_pool
project_name: availability_projection
schedule:
  end_date: '2025-02-07T00:00:00'
  interval: 3 0,4,8,12,16,20 * * *
  start_date: '2024-11-27T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
