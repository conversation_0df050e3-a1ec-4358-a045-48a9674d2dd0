dag_name: perishables_cpd
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: povms
notebook:
  parameters:
owner:
  email: <EMAIL>
  slack_id: U028A1HH0TD
path: povms/inventorymgt/etl/perishables_cpd
paused: true
project_name: inventorymgt
schedule:
  interval: 30 9 * * *
  start_date: '2021-09-03T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 12
