{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Perishables working:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas as pd\n", "import logging\n", "import numpy as np\n", "import os as os\n", "import datetime as dt\n", "from calendar import monthrange\n", "from datetime import timedelta\n", "import pencilbox as pb\n", "from dateutil.relativedelta import relativedelta\n", "from email.message import EmailMessage\n", "import boto3\n", "from botocore.exceptions import ClientError\n", "\n", "secrets = pb.get_secret(\"retail/snorlax/aws/snorlax.aws.credential\")\n", "retail = pb.get_connection(\"retail\")\n", "redshift_con = pb.get_connection(\"[Warehouse] Redshift\").connect()\n", "td = dt.datetime.now().strftime(\"%Y-%m-%d\")\n", "now = dt.datetime.now()\n", "SD = (pd.to_datetime(now) - pd.DateOffset(days=14)).strftime(\"%Y-%m-%d\")\n", "ED = (pd.to_datetime(now) - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def date_ba(CPD_DATA_Base, nld, now_m, nxt_m):\n", "    inter = len(now_m) + len(nxt_m) + 5\n", "    y = dt.datetime.strptime(now_m[0], \"%Y-%m-%d\")\n", "\n", "    CPD_DATA_Base1 = CPD_DATA_Base.iloc[:, :inter]\n", "    l6 = len(now_m + nxt_m)\n", "    j = 3\n", "    i = 0\n", "    while i < l6:\n", "        d1 = y + dt.<PERSON><PERSON><PERSON>(i)\n", "        d1 = d1.strftime(\"%Y-%m-%d\")\n", "        CPD_DATA_Base1 = CPD_DATA_Base1.rename(\n", "            columns={CPD_DATA_Base1.iloc[:, j].name: d1}\n", "        )\n", "        i += 1\n", "        j += 1\n", "    CPD_DATA_Base2 = CPD_DATA_Base1.rename(\n", "        columns={\"item_id\": \"Item ID\", \"name\": \"Item Name\"}\n", "    )\n", "    CPD_DATA_Base2[\"Item Name\"].fillna(\"-\", inplace=True)\n", "    CPD_DATA_Base2[\"Item ID\"] = CPD_DATA_Base2[\"Item ID\"].astype(str)\n", "    CPD_DATA_Base2[\"Union\"] = CPD_DATA_Base2[\"Item ID\"].str.cat(\n", "        CPD_DATA_Base2[\"facility\"].copy()\n", "    )\n", "    CPD_DATA_Base4 = CPD_DATA_Base2.loc[\n", "        :, [\"Union\", \"facility\", \"Item ID\", \"Item Name\"] + now_m + nxt_m\n", "    ]\n", "    return CPD_DATA_Base4\n", "\n", "\n", "def actual_live70(actual_base, n):\n", "    date_b = r_date(n)\n", "    actual_base_r = pd.merge(\n", "        left=actual_base,\n", "        right=date_b[{\"date_\"}],\n", "        left_on=[\"date_\"],\n", "        right_on=[\"date_\"],\n", "        how=\"inner\",\n", "    )\n", "    actual_base_r[\"live_days\"] = 1\n", "    actual_base_r1 = (\n", "        actual_base_r.groupby([\"item_id\", \"facility\"])[\"qty\", \"live_days\"]\n", "        .agg(\"sum\")\n", "        .reset_index()\n", "    )\n", "    actual_base_r2 = actual_base_r1.rename(columns={\"qty\": \"sales_qty\"})\n", "    return actual_base_r2\n", "\n", "\n", "def create_presigned_url(bucket_name, object_name, expiration=3600):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        logging.error(e)\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response\n", "\n", "\n", "def r_date(pick_day):\n", "    date_b = pd.DataFrame({\"date_\": [], \"sdate\": []})\n", "    for zm in list(range(1, pick_day + 1)):\n", "        date_m = pd.DataFrame(\n", "            {\n", "                \"date_\": [\n", "                    (\n", "                        (pd.to_datetime(now) - pd.DateOffset(days=zm)).strftime(\n", "                            \"%Y-%m-%d\"\n", "                        )\n", "                    )\n", "                ],\n", "                \"sdate\": [\n", "                    (\n", "                        (\n", "                            dt.datetime.now(),\n", "                            (pd.to_datetime(now) - pd.DateOffset(days=zm)),\n", "                        )[1]\n", "                    )\n", "                ],\n", "            }\n", "        )\n", "        date_b = date_b.append(date_m)\n", "    return date_b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mapping sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q2 = \"\"\"select facility_id from city_facility_split\n", "    where type <> 'franchise'\n", "    group by 1\n", "    \"\"\"\n", "tag1 = pd.read_sql(q2, con=redshift_con)\n", "tag1[\"facility_id\"] = tag1[\"facility_id\"].astype(str)\n", "\n", "q2 = \"\"\"Select  a.facility_id\n", "    from lake_rpc.product_facility_master_assortment a\n", "    where active = 1 and master_assortment_substate_id = 1\n", "    group by 1\n", "    \"\"\"\n", "tag2 = pd.read_sql(q2, con=redshift_con)\n", "tag2[\"facility_id\"] = tag2[\"facility_id\"].astype(str)\n", "\n", "sheet_id = \"1LkzcTCjt_J5ILplOZ-iK3wmCAklqbYlCV-0zad-syVM\"\n", "mappings = pb.from_sheets(sheet_id, \"Mappings\")\n", "mappings = pb.from_sheets(sheet_id, \"Mappings\")\n", "\n", "facility_HC_mapping = mappings.loc[\n", "    :, [\"3.Storage_type\", \"3.facility\", \"3.outlet_id\", \"3.outlet_name\"]\n", "]\n", "facility_HC_mapping1 = facility_HC_mapping.rename(\n", "    columns={\n", "        \"3.Storage_type\": \"storage_type\",\n", "        \"3.facility\": \"facility\",\n", "        \"3.outlet_id\": \"outlet_id\",\n", "        \"3.outlet_name\": \"outlet_name\",\n", "    }\n", ")\n", "facility_cold_mapping1 = facility_HC_mapping1.loc[\n", "    (facility_HC_mapping1.storage_type == \"COLD\")\n", "]\n", "facility_cold_mapping = facility_cold_mapping1.drop(columns={\"storage_type\"})\n", "facility_hot_mapping1 = facility_HC_mapping1.loc[\n", "    (facility_HC_mapping1.storage_type == \"HOT\")\n", "]\n", "facility_hot_mapping = facility_hot_mapping1.drop(columns={\"storage_type\"})\n", "COLD = tuple(facility_cold_mapping[\"outlet_id\"])\n", "HOT = tuple(facility_hot_mapping[\"outlet_id\"])\n", "faci_faci = mappings.loc[mappings[\"5.facility\"] != \"\", [\"5.f_id\", \"5.facility\"]]\n", "faci_faci1 = faci_faci.rename(\n", "    columns={\"5.f_id\": \"facility_id\", \"5.facility\": \"facility\"}\n", ")\n", "faci_faci1 = pd.merge(\n", "    left=faci_faci1,\n", "    right=tag1,\n", "    left_on=\"facility_id\",\n", "    right_on=\"facility_id\",\n", "    how=\"inner\",\n", ")\n", "faci_faci1 = pd.merge(\n", "    left=faci_faci1,\n", "    right=tag2,\n", "    left_on=\"facility_id\",\n", "    right_on=\"facility_id\",\n", "    how=\"inner\",\n", ")\n", "f_id = tuple(faci_faci1[\"facility_id\"].astype(int))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Date Data Base\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Date range for 3 months\n", "sdt = dt.datetime.now() + relativedelta(day=1)\n", "last_day = dt.datetime.now() + relativedelta(day=1, months=+1, days=-1)\n", "sd = sdt.strftime(\"%Y-%m-%d\")\n", "date_m1 = pd.DataFrame({\"date_\": sdt, \"sdate\": [sd]})\n", "n = 1\n", "o = (last_day - sdt).days + 1\n", "while n < o:\n", "    snd = (sdt + pd.DateOffset(days=n)).strftime(\"%Y-%m-%d\")\n", "    sndt = sdt + pd.DateOffset(days=n)\n", "    date_b2 = pd.DataFrame({\"date_\": sdt, \"sdate\": [snd]})\n", "    date_m1 = date_m1.append(date_b2)\n", "    n += 1\n", "now_m = list(date_m1[\"sdate\"])  # 1st month date range\n", "\n", "sdt = dt.datetime.now() + relativedelta(day=1, months=+1)\n", "last_day = dt.datetime.now() + relativedelta(day=1, months=+2, days=-1)\n", "sd = sdt.strftime(\"%Y-%m-%d\")\n", "date_m2 = pd.DataFrame({\"date_\": sdt, \"sdate\": [sd]})\n", "n = 1\n", "o = (last_day - sdt).days + 1\n", "while n < o:\n", "    snd = (sdt + pd.DateOffset(days=n)).strftime(\"%Y-%m-%d\")\n", "    sndt = sdt + pd.DateOffset(days=n)\n", "    date_b2 = pd.DataFrame({\"date_\": sdt, \"sdate\": [snd]})\n", "    date_m2 = date_m2.append(date_b2)\n", "    n += 1\n", "nxt_m = list(date_m2[\"sdate\"])  # 2nd month date range\n", "nld = nxt_m[-1]\n", "\n", "sdt = dt.datetime.now()\n", "last_day = dt.datetime.now() + relativedelta(day=1, months=+1, days=-1)\n", "sd = sdt.strftime(\"%Y-%m-%d\")\n", "date_m1 = pd.DataFrame({\"date_\": sdt, \"sdate\": [sd]})\n", "n = 1\n", "o = (last_day - sdt).days + 1\n", "while n < o:\n", "    snd = (sdt + pd.DateOffset(days=n)).strftime(\"%Y-%m-%d\")\n", "    sndt = sdt + pd.DateOffset(days=n)\n", "    date_b2 = pd.DataFrame({\"date_\": sdt, \"sdate\": [snd]})\n", "    date_m1 = date_m1.append(date_b2)\n", "    n += 1\n", "remd_m = list(date_m1[\"sdate\"])  # Remaning month date range"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Carts data\n", "Q6 = \"\"\"\n", "with data as (\n", "select * from ( select *, row_number() over (partition by Merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts)\n", "where rnk =1 and date(f_date) between date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date)))) and (SELECT date(dateadd(month , +1,DATEADD(day , 1,LAST_DAY(current_date)))))-1) \n", "\n", "select \n", "p.facility_id,facility,date(f_date) as forecast_date,sum(forecast_carts) as forecast_carts\n", "from data p\n", "inner join (Select  a.facility_id\n", "    from lake_rpc.product_facility_master_assortment a\n", "    where active = 1 and master_assortment_substate_id = 1\n", "    group by 1) o  on o.facility_id =p.facility_id\n", "where forecast_carts>0\n", "\n", "group by 1,2,3\n", "order by 3,2\n", "\n", "\"\"\"\n", "fcarts = pd.read_sql(Q6, con=redshift_con)\n", "fcarts[\"forecast_date\"] = fcarts[\"forecast_date\"].apply(\n", "    lambda x: x.strftime(\"%Y-%m-%d\")\n", ")\n", "for nop in now_m + nxt_m:\n", "    fcarts[nop] = np.where(fcarts.forecast_date == nop, fcarts[\"forecast_carts\"], 0)\n", "carts = (\n", "    fcarts.groupby([\"facility\", \"facility_id\"])[now_m + nxt_m].agg(\"sum\").reset_index()\n", ")\n", "\n", "\n", "carts_remd_1 = carts.loc[:, [\"facility\", \"facility_id\"] + remd_m + nxt_m]\n", "carts_remd_1[\"sum1\"] = np.sum(carts_remd_1.loc[:, remd_m + nxt_m], axis=1)\n", "carts_remd2 = carts_remd_1.loc[carts_remd_1.sum1 != 0]\n", "carts_remd = carts_remd2.loc[:, [\"facility\", \"facility_id\"] + remd_m + nxt_m]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data calls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Q3 = \"\"\"\n", "WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ) \n", "  SELECT item_id,\n", "       (cat.product || ' ' || cat.unit) AS name\n", "      \n", "FROM lake_rpc.item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "cat = pd.read_sql(Q3, con=redshift_con)\n", "cat[\"item_id\"] = cat[\"item_id\"].astype(str)\n", "cat1 = cat.rename(columns={\"item_id\": \"Item ID\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual = \"\"\"\n", "\n", "with  peri_base as(select item_id,facility_id from consumer.perishable_sku\n", "    where update_at =(select max(update_at) from consumer.perishable_sku)\n", "    group by 1,2),\n", "\n", "mapping as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "coalesce(multiplier,1) as multiplier\n", "from (select distinct item_id,product_id,multiplier,updated_on,\n", "    max(updated_on) over (partition by product_id) as last_updated from it_pd_log)\n", "where last_updated = updated_on\n", "),\n", "pre_price as\n", "(select * from  gr_product_price_history\n", "where  install_ts  >= current_date-220\n", "),\n", "\n", "price_history as (\n", "select\n", "date(gp.install_ts+interval '5.5 hours') as datee,\n", "bb.facility_id,\n", "c.item_id,\n", "gp.product_id\n", "from pre_price gp\n", "left join mapping c on gp.product_id = c.product_id\n", "left join ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) bb on gp.merchant_id = bb.merchant_id and date(gp.install_ts+interval '5.5 hours') = bb.f_date\n", "where rnk =1\n", "and  date(gp.install_ts+interval '5.5 hours')  between current_date-7 and current_date-1\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "\n", "final_live_days2 as\n", "(\n", "select aa.item_id, aa.facility_id, bb.facility,\n", "count(distinct case when (datee between current_date-7 and current_date-1 ) Then datee end ) as live_days\n", "from price_history aa\n", "left join ( select * from city_facility_split) bb on aa.facility_id = bb.facility_id\n", "inner join peri_base p on p.item_id=aa.item_id and aa.facility_id=p.facility_id\n", "group by 1,2,3\n", "),\n", "\n", "\n", "\n", "\n", "\n", "\n", "----- sales data \n", "\n", "\n", "da1 as (\n", "select \n", "date(a.install_ts + interval '5.5 Hours') as date_,\n", "c.item_id,\n", "om.external_id,\n", "sum(b.quantity*multiplier) as qty\n", "from lake_oms_bifrost.oms_order a\n", "left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and install_ts > current_date - 30 ) b \n", "on a.id = b.order_id\n", "left join lake_oms_bifrost.oms_merchant om on a.merchant_id = om.id\n", "left join mapping c on b.product_id = c.product_id\n", "--left  join (select * from all_margins lq where lq_margin >= 0.01 )lq on lq.pid = b.product_id and lq.city = om.city_name and \n", "--(a.install_ts + interval '5.5 Hours') >= lq.start_date and date(a.install_ts + interval '5.5 Hours') <= lq.end_date\n", "where a.install_ts >  current_date-9 \n", "and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "AND om.city_name not ilike '%%b2b%%'\n", "--and lq.pid is null\n", "and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)\n", "group by 1,2,3),\n", "\n", "sales_da1 as (\n", "select bb.facility_id,a.item_id,sum(qty) as qty from da1 a\n", "left join ( select * from (select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) where rnk =1  ) bb on a.external_id = bb.merchant_id and date(a.date_) = bb.f_date\n", "inner join peri_base cc on a.item_id=cc.item_id and bb.facility_id=cc.facility_id\n", "where date(a.date_) between current_date-7 and current_date-1\n", "group by 1,2),\n", "\n", "\n", "---- carts data\n", "actual_carts as (with it_pd as\n", "(select distinct item_id, product_id,multiplier, updated_on, max(updated_on) over (partition by product_id) as last_updated from it_pd_log ),\n", "\n", "mapping as (select distinct item_id, product_id,coalesce(multiplier,1) as multiplier from it_pd\n", "    where last_updated = updated_on ),\n", "\n", "tbl_treatment as\n", "    (select a.*,date(a.install_ts + interval '5.5 Hours') as checkout_date,om.city_name,b.product_id,c.item_id,om.external_id as virtual_merchant_id\n", "    from lake_oms_bifrost.oms_order a\n", "    left join  lake_oms_bifrost.oms_merchant om on a.merchant_id = om.id\n", "    left join (select * from  lake_oms_bifrost.oms_order_item where freebie_id is null and install_ts> current_date - 61 ) b on a.id = b.order_id\n", "    left join mapping c on b.product_id = c.product_id\n", "    left join lake_rpc.item_details d on c.item_id = d.item_id\n", "    where a.install_ts>current_date-9\n", "        and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "        and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "        and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)),\n", "\n", "merchant_and_store_join as (\n", "    select a.*,b.real_merchant_id,c.outlet_id,d.warehouse_id as hot_outlet_id,e.facility_id,f.name as facility_name from tbl_treatment a\n", "    left join (select * from lake_cms.gr_virtual_to_real_merchant_mapping where priority_order = 1 AND enabled_flag = true) b on a.virtual_merchant_id = b.virtual_merchant_id\n", "    left join (select * from lake_retail.console_outlet_cms_store where active = true) c on b.real_merchant_id = c.cms_store\n", "    left join lake_retail.warehouse_outlet_mapping d on c.outlet_id = d.cloud_store_id\n", "    left join lake_retail.console_outlet e on d.warehouse_id = e.id\n", "    left join lake_crates.facility f on e.facility_id = f.id),\n", "\n", "actual1_cart as( select \n", "checkout_date as date_,\n", "virtual_merchant_id as merchant_id,\n", " count(distinct id) as orders\n", " from merchant_and_store_join\n", " where date(checkout_date) between current_date-9 AND current_date\n", "group by 1,2),\n", "\n", " data as (  select * from ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "            from Merchant_forecast_carts)\n", "            where rnk =1 and date(f_date) between current_date-60 AND current_date+30)\n", "            \n", "select mo.facility,mo.facility_id,\n", "sum(case when date_ between current_date-7 and current_date-1 then ac.orders else 0 end) as L7_Aactual_Carts\n", "from data mo\n", "left join actual1_cart ac on mo.f_date=ac.date_ and mo.merchant_id = ac.merchant_id\n", "\n", "group by 1,2\n", "having L7_Aactual_Carts>0\n", "order by 1),\n", "\n", "\n", "\n", "data as (\n", "select a.facility_id,\n", "a.facility,a.item_id,p.L7_Aactual_Carts,sum(live_days) as live_days,sum(qty) as sales_qty\n", "from final_live_days2 a\n", "left join sales_da1 b on a. item_id = b.item_id and a.facility_id = b.facility_id \n", "left join actual_carts p on a.facility_id = p.facility_id\n", "\n", "where a.item_id is not null\n", "group by 1,2,3,4)\n", "\n", "select facility_id, facility, item_id,L7_Aactual_Carts, live_days ,\n", "sales_qty, (sales_qty/(live_days+0.00001)) as L7_cpd,(((sales_qty/(live_days+0.00001))*7)/(L7_Aactual_Carts+0.00001)) as potential_qpc from data\n", "\n", "\"\"\"\n", "actual_cpd = pd.read_sql(actual, con=redshift_con)\n", "actual_cpd[\"facility_id\"] = actual_cpd[\"facility_id\"].astype(str)\n", "actual_cpd = pd.merge(\n", "    left=actual_cpd,\n", "    right=faci_faci1[{\"facility_id\"}],\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"inner\",\n", ")\n", "actual_cpd[\"l7_cpd\"].fillna(0, inplace=True)\n", "actual_cpd = actual_cpd.loc[actual_cpd.l7_cpd != 0]\n", "# for upload"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual1 = \"\"\"\n", "with  peri_base as(select item_id,facility_id from consumer.perishable_sku\n", "    where update_at =(select max(update_at) from consumer.perishable_sku)\n", "    group by 1,2),\n", "\n", "mapping as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "coalesce(multiplier,1) as multiplier\n", "from (select distinct item_id,product_id,multiplier,updated_on,\n", "    max(updated_on) over (partition by product_id) as last_updated from it_pd_log)\n", "where last_updated = updated_on\n", "),\n", "pre_price as\n", "(select * from  gr_product_price_history\n", "where  install_ts  >= current_date-220\n", "),\n", "\n", "price_history as (\n", "select\n", "date(gp.install_ts+interval '5.5 hours') as datee,\n", "bb.facility_id,\n", "c.item_id,\n", "gp.product_id\n", "from pre_price gp\n", "left join mapping c on gp.product_id = c.product_id\n", "left join ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) bb on gp.merchant_id = bb.merchant_id and date(gp.install_ts+interval '5.5 hours') = bb.f_date\n", "where rnk =1\n", "and  date(gp.install_ts+interval '5.5 hours')  between current_date-30 and current_date-1\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "\n", "final_live_days2 as\n", "(\n", "select aa.item_id, aa.facility_id, bb.facility,\n", "count(distinct case when (datee between current_date-30 and current_date-1 ) Then datee end ) as live_days\n", "from price_history aa\n", "left join ( select * from city_facility_split) bb on aa.facility_id = bb.facility_id\n", "inner join peri_base p on p.item_id=aa.item_id and aa.facility_id=p.facility_id\n", "group by 1,2,3\n", "),\n", "--- sales data \n", "da1 as (\n", "select \n", "date(a.install_ts + interval '5.5 Hours') as date_,\n", "c.item_id,\n", "om.external_id,\n", "sum(b.quantity*multiplier) as qty\n", "from lake_oms_bifrost.oms_order a\n", "left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and install_ts > current_date - 60 ) b \n", "on a.id = b.order_id\n", "left join lake_oms_bifrost.oms_merchant om on a.merchant_id = om.id\n", "left join mapping c on b.product_id = c.product_id\n", "--left  join (select * from all_margins lq where lq_margin >= 0.01 )lq on lq.pid = b.product_id and lq.city = om.city_name and \n", "--(a.install_ts + interval '5.5 Hours') >= lq.start_date and date(a.install_ts + interval '5.5 Hours') <= lq.end_date\n", "where a.install_ts >  current_date-32 \n", "and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "AND om.city_name not ilike '%%b2b%%'\n", "--and lq.pid is null\n", "and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)\n", "group by 1,2,3),\n", "\n", "sales_da1 as (\n", "select bb.facility_id,a.item_id,sum(qty) as qty from da1 a\n", "left join ( select * from (select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) where rnk =1  ) bb on a.external_id = bb.merchant_id and date(a.date_) = bb.f_date\n", "inner join peri_base cc on a.item_id=cc.item_id and bb.facility_id=cc.facility_id\n", "where date(a.date_) between current_date-30 and current_date-1\n", "group by 1,2),\n", "\n", "data as (\n", "select a.facility_id,a.item_id,sum(live_days) as live_days,sum(qty) as sales_qty\n", "from final_live_days2 a\n", "left join sales_da1 b on a. item_id = b.item_id and a.facility_id = b.facility_id \n", "where a.item_id is not null\n", "group by 1,2)\n", "\n", "select facility_id,item_id, (sales_qty/(live_days+0.00001)) as L30_cpd from data\n", "    \n", "\n", "\"\"\"\n", "actual30_cpd = pd.read_sql(actual1, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual30_cpd[\"facility_id\"] = actual30_cpd[\"facility_id\"].astype(str)\n", "\n", "actual_cpd_1 = pd.merge(\n", "    left=actual_cpd,\n", "    right=actual30_cpd,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "actual_cpd_1[\"item_id\"] = actual_cpd_1[\"item_id\"].astype(int)\n", "actual_cpd_1[\"facility_id\"] = actual_cpd_1[\"facility_id\"].astype(int)\n", "actual_cpd_1[\"grid_capping\"] = 0\n", "actual_cpd1 = pd.merge(\n", "    left=actual_cpd_1,\n", "    right=carts_remd,\n", "    left_on=[\"facility\", \"facility_id\"],\n", "    right_on=[\"facility\", \"facility_id\"],\n", "    how=\"inner\",\n", ")\n", "sheet_id1 = \"1TPQOU5J-K_2M0mK1fqXM8l7Dz_1J6oJkJaoV4R_mtSY\"\n", "capping_grid = pb.from_sheets(sheet_id1, \"capping_grid\")\n", "capping_grid[\"Capping\"] = capping_grid[\"Capping\"].astype(float)\n", "for no in remd_m + nxt_m:\n", "    actual_cpd1[no] = actual_cpd1[\"l7_cpd\"].copy()\n", "    # actual_cpd1[no] = actual_cpd1[no] * actual_cpd1[\"potential_qpc\"] for L7 cpd actual cpd\n", "    actual_cpd1[\"grid\"] = np.where(\n", "        actual_cpd1[no] < 5,\n", "        \"0_to_5\",\n", "        np.where(\n", "            actual_cpd1[no] < 20,\n", "            \"5_to_20\",\n", "            np.where(\n", "                actual_cpd1[no] < 50,\n", "                \"20_to_50\",\n", "                np.where(actual_cpd1[no] < 200, \"50_to_200\", \"200_to_1000\"),\n", "            ),\n", "        ),\n", "    )  # Need to update grid\n", "    actual_cpd1 = pd.merge(\n", "        left=actual_cpd1,\n", "        right=capping_grid,\n", "        left_on=[\"grid\"],\n", "        right_on=[\"grid\"],\n", "        how=\"left\",\n", "    )\n", "    actual_cpd1[\"actual_l30_cap\"] = actual_cpd1[\"Capping\"] * actual_cpd1[\"l30_cpd\"]\n", "    actual_cpd1[\"grid_capping\"] = np.where(\n", "        actual_cpd1[\"actual_l30_cap\"] <= actual_cpd1[no], 1, actual_cpd1[\"grid_capping\"]\n", "    )\n", "    actual_cpd1[no] = np.where(\n", "        actual_cpd1[\"actual_l30_cap\"] <= actual_cpd1[no],\n", "        actual_cpd1[\"actual_l30_cap\"],\n", "        actual_cpd1[no],\n", "    )\n", "    actual_cpd1 = actual_cpd1.drop(columns={\"actual_l30_cap\", \"grid\", \"Capping\"})\n", "actual_cpd1[\"item_id\"] = actual_cpd1[\"item_id\"].astype(str)\n", "actual_b = actual_cpd1.loc[:, [\"item_id\", \"facility\"] + remd_m + nxt_m]\n", "actual_b1 = actual_b.rename(columns={\"item_id\": \"Item ID\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QB = \"\"\"\n", "with peri_base as(select item_id,facility_id from consumer.perishable_sku\n", "where update_at =(select max(update_at) from consumer.perishable_sku)\n", "group by 1,2),\n", "\n", "snorlax_tbl_treatment as(\n", "select\n", "b.item_id,\n", "mp.facility,op.name,\n", "po.facility_id,\n", "date(date) as date_of_prediction,\n", "sum(consumption) as qty\n", "from\n", "lake_snorlax.date_wise_consumption b\n", "\n", "inner join (select id as Outlet_id, facility_id, rnk from \n", "            (select m.*,row_number() over(partition by updated_at,id order by updated_at desc) as rnk \n", "            from lake_retail.console_outlet m\n", "            ) where rnk =1 ) po on po.Outlet_id= b.outlet_id\n", "            \n", "inner join(select facility_id,facility\n", "            from city_facility_split\n", "            where facility_id in {fid}) mp on mp.facility_id=po.facility_id\n", "            \n", "left join(select distinct item_id,name from(select item_id, name,row_number() over(partition by item_id order by updated_at desc) as rnk from rpc_product_product \n", "        where active =1) where rnk =1) op on  op.item_id=b.item_id\n", "inner join peri_base p on p.item_id=b.item_id and po.facility_id=p.facility_id\n", "where date_of_prediction between '{start_date}'   and '{end_date}'  and consumption>0 \n", "Group by 1,2,3,4,5\n", ")\n", "\n", "select item_id, facility,name,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +0 then qty  else 0 end) as Date_plus0,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +1 then qty  else 0 end) as Date_plus1,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +2 then qty  else 0 end) as Date_plus2,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +3 then qty  else 0 end) as Date_plus3,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +4 then qty  else 0 end) as Date_plus4,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +5 then qty  else 0 end) as Date_plus5,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +6 then qty  else 0 end) as Date_plus6,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +7 then qty  else 0 end) as Date_plus7,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +8 then qty  else 0 end) as Date_plus8,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +9 then qty  else 0 end) as Date_plus9,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +10 then qty  else 0 end) as Date_plus10,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +11 then qty  else 0 end) as Date_plus11,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +12 then qty  else 0 end) as Date_plus12,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +13 then qty  else 0 end) as Date_plus13,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +14 then qty  else 0 end) as Date_plus14,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +15 then qty  else 0 end) as Date_plus15,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +16 then qty  else 0 end) as Date_plus16,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +17 then qty  else 0 end) as Date_plus17,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +18 then qty  else 0 end) as Date_plus18,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +19 then qty  else 0 end) as Date_plus19,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +20 then qty  else 0 end) as Date_plus20,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +21 then qty  else 0 end) as Date_plus21,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +22 then qty  else 0 end) as Date_plus22,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +23 then qty  else 0 end) as Date_plus23,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +24 then qty  else 0 end) as Date_plus24,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +25 then qty  else 0 end) as Date_plus25,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +26 then qty  else 0 end) as Date_plus26,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +27 then qty  else 0 end) as Date_plus27,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +28 then qty  else 0 end) as Date_plus28,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +29 then qty  else 0 end) as Date_plus29,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +30 then qty  else 0 end) as Date_plus30,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +31 then qty  else 0 end) as Date_plus31,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +32 then qty  else 0 end) as Date_plus32,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +33 then qty  else 0 end) as Date_plus33,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +34 then qty  else 0 end) as Date_plus34,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +35 then qty  else 0 end) as Date_plus35,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +36 then qty  else 0 end) as Date_plus36,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +37 then qty  else 0 end) as Date_plus37,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +38 then qty  else 0 end) as Date_plus38,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +39 then qty  else 0 end) as Date_plus39,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +40 then qty  else 0 end) as Date_plus40,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +41 then qty  else 0 end) as Date_plus41,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +42 then qty  else 0 end) as Date_plus42,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +43 then qty  else 0 end) as Date_plus43,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +44 then qty  else 0 end) as Date_plus44,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +45 then qty  else 0 end) as Date_plus45,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +46 then qty  else 0 end) as Date_plus46,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +47 then qty  else 0 end) as Date_plus47,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +48 then qty  else 0 end) as Date_plus48,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +49 then qty  else 0 end) as Date_plus49,\n", "sum(case when date(date_of_prediction) = '{start_date}'   +50 then qty  else 0 end) as Date_plus50,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +51 then qty  else 0 end) as Date_plus51,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +52 then qty  else 0 end) as Date_plus52,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +53 then qty  else 0 end) as Date_plus53,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +54 then qty  else 0 end) as Date_plus54,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +55 then qty  else 0 end) as Date_plus55,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +56 then qty  else 0 end) as Date_plus56,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +57 then qty  else 0 end) as Date_plus57,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +58 then qty  else 0 end) as Date_plus58,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +59 then qty  else 0 end) as Date_plus59,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +60 then qty  else 0 end) as Date_plus60,\n", "sum(case when date(date_of_prediction) = '{start_date}'    +61 then qty  else 0 end) as Date_plus61\n", "from snorlax_tbl_treatment \n", "where facility is not null \n", "group by 1,2,3\n", "\n", "\n", "\n", "\"\"\".format(\n", "    start_date=now_m[0], end_date=nld, fid=f_id\n", ")\n", "#\n", "CPD_DATA_Base = pd.read_sql(QB, con=redshift_con)\n", "CPD_DATA_Base4 = date_ba(CPD_DATA_Base, nld, now_m, nxt_m)  # CPD_DATA_Base4\n", "CPD_DATA_Base5 = CPD_DATA_Base4.loc[:, [\"Item ID\", \"facility\"] + remd_m + nxt_m]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_base_1 = actual_b1.append(CPD_DATA_Base5)\n", "f_base_1.drop_duplicates(subset=[\"Item ID\", \"facility\"], keep=\"first\", inplace=True)\n", "# base alinement\n", "Qm = \"\"\"\n", "select item_id,facility_id from consumer.perishable_sku\n", "where update_at =(select max(update_at) from consumer.perishable_sku)\n", "group by 1,2\"\"\"\n", "QM1 = pd.read_sql(Qm, con=redshift_con)\n", "QM1[\"item_id\"] = QM1[\"item_id\"].astype(str)\n", "QM1[\"facility_id\"] = QM1[\"facility_id\"].astype(str)\n", "QM2 = pd.merge(\n", "    left=QM1,\n", "    right=faci_faci1,\n", "    left_on=\"facility_id\",\n", "    right_on=\"facility_id\",\n", "    how=\"inner\",\n", ")\n", "QM3 = QM2.rename(columns={\"item_id\": \"Item ID\"})\n", "for n in remd_m + nxt_m:\n", "    QM3[n] = 0.1\n", "QM3 = QM3.loc[:, [\"Item ID\", \"facility\"] + remd_m + nxt_m]\n", "f_base1 = f_base_1.append(QM3)\n", "f_base1.drop_duplicates(subset=[\"Item ID\", \"facility\"], keep=\"first\", inplace=True)\n", "\n", "# for upload\n", "f_base1.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Week on Week Working "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w1 = now_m[0:10]  # 1 to 10\n", "w2 = now_m[10:20]  # 11 to 20\n", "w3 = now_m[20:]  # 21-\n", "w4 = nxt_m[0:10]  # 1 to 10\n", "w5 = nxt_m[10:20]  # 11 to 20\n", "w6 = nxt_m[20:]  # 21\n", "week_base = pd.DataFrame({\"Week\": [], \"date\": []})\n", "for m1 in w1:\n", "    week_base2 = pd.DataFrame({\"Week\": [\"w1\"], \"date\": [m1]})\n", "    week_base = week_base.append(week_base2)\n", "for m1 in w2:\n", "    week_base2 = pd.DataFrame({\"Week\": [\"w2\"], \"date\": [m1]})\n", "    week_base = week_base.append(week_base2)\n", "for m1 in w3:\n", "    week_base2 = pd.DataFrame({\"Week\": [\"w3\"], \"date\": [m1]})\n", "    week_base = week_base.append(week_base2)\n", "for m1 in w4:\n", "    week_base2 = pd.DataFrame({\"Week\": [\"w4\"], \"date\": [m1]})\n", "    week_base = week_base.append(week_base2)\n", "for m1 in w5:\n", "    week_base2 = pd.DataFrame({\"Week\": [\"w5\"], \"date\": [m1]})\n", "    week_base = week_base.append(week_base2)\n", "for m1 in w6:\n", "    week_base2 = pd.DataFrame({\"Week\": [\"w6\"], \"date\": [m1]})\n", "    week_base = week_base.append(week_base2)\n", "\n", "week_base[\"date\"] = week_base[\"date\"].apply(\n", "    lambda x: dt.datetime.strptime(x, \"%Y-%m-%d\")\n", ")\n", "week_base1 = week_base.loc[\n", "    (week_base.date > (pd.to_datetime(now) - pd.DateOffset(days=1)))\n", "]\n", "week_base1[\"tag\"] = \"active\"\n", "temp = week_base1.groupby([\"Week\"])\n", "res = temp[[\"date\"]].agg(\"count\").reset_index()\n", "res1 = res.rename(columns={\"date\": \"day\"})  # Number of days in week\n", "r_es1 = res.rename(columns={\"date\": \"day\"})  # Number of days in week\n", "\n", "week_month = pd.DataFrame(\n", "    {\n", "        \"Week\": [\"w1\", \"w2\", \"w3\", \"w4\", \"w5\", \"w6\"],\n", "        \"month\": [\n", "            \"Now_M\",\n", "            \"Now_M\",\n", "            \"Now_M\",\n", "            \"Nxt_M\",\n", "            \"Nxt_M\",\n", "            \"Nxt_M\",\n", "        ],\n", "    }\n", ")\n", "res2 = pd.merge(\n", "    left=res1, right=week_month, left_on=[\"Week\"], right_on=[\"Week\"], how=\"left\"\n", ")\n", "resNow = res2.loc[(res2.month == \"Now_M\")]\n", "resNxt = res2.loc[(res2.month == \"Nxt_M\")]\n", "Rem_week = list(resNow[\"Week\"])  # now months weeks\n", "Nxt_week = list(resNxt[\"Week\"])  # Next Months weeks\n", "week1_base = pd.merge(\n", "    left=week_base,\n", "    right=week_base1.loc[:, [\"date\", \"tag\"]],\n", "    left_on=[\"date\"],\n", "    right_on=[\"date\"],\n", "    how=\"left\",\n", ")\n", "week1_base[\"tag\"].fillna(\"inactive\", inplace=True)\n", "active_day = week1_base.loc[week1_base.tag == \"active\"]\n", "active_day[\"date\"] = active_day[\"date\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "\n", "final_base = f_base1.loc[:, [\"Item ID\", \"facility\"]]\n", "final1_base = final_base.copy()\n", "cpd_base = final_base.copy()\n", "for mmo in Rem_week + Nxt_week:\n", "    days1 = active_day.loc[(active_day.Week == mmo)]\n", "    D1 = list(days1[\"date\"])\n", "    week_d = f_base1.loc[:, [\"Item ID\", \"facility\"] + D1]\n", "    carts_w = carts_remd.loc[:, [\"facility\"] + D1]\n", "    for mo in D1:\n", "        week_d[mo] = np.where(week_d[mo] == 0, np.nan, week_d[mo])\n", "        carts_w[mo] = np.where(carts_w[mo] == 0, np.nan, carts_w[mo])\n", "    carts_w[\"avg_cart\"] = np.mean(carts_w.loc[:, D1], axis=1)\n", "    carts_w[\"avg_cart\"].fillna(0, inplace=True)\n", "    week_d[\"fcpd\"] = np.mean(week_d.loc[:, D1], axis=1)\n", "    week_d[\"fcpd\"].fillna(0.01, inplace=True)\n", "    week_d1 = pd.merge(\n", "        left=final1_base,\n", "        right=week_d.loc[:, [\"Item ID\", \"facility\", \"fcpd\"]],\n", "        left_on=[\"Item ID\", \"facility\"],\n", "        right_on=[\"Item ID\", \"facility\"],\n", "        how=\"left\",\n", "    )\n", "    week_d2 = pd.merge(\n", "        left=week_d1,\n", "        right=carts_w.loc[:, [\"facility\", \"avg_cart\"]],\n", "        left_on=[\"facility\"],\n", "        right_on=[\"facility\"],\n", "        how=\"left\",\n", "    )\n", "    week_d3 = week_d2.rename(\n", "        columns={\"avg_cart\": mmo + \"_avg_cart\", \"fcpd\": mmo + \"_fcpd\"}\n", "    )\n", "    final_base = pd.merge(\n", "        left=final_base,\n", "        right=week_d3,\n", "        left_on=[\"Item ID\", \"facility\"],\n", "        right_on=[\"Item ID\", \"facility\"],\n", "        how=\"left\",\n", "    )  # combine results\n", "    week_d11 = week_d1.rename(columns={\"fcpd\": mmo + \"_fcpd\"})\n", "    cpd_base = pd.merge(\n", "        left=cpd_base,\n", "        right=week_d11,\n", "        left_on=[\"Item ID\", \"facility\"],\n", "        right_on=[\"Item ID\", \"facility\"],\n", "        how=\"left\",\n", "    )  # only cpd results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Google sheet upload files "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual_cpd1[\"item_id\"] = actual_cpd1[\"item_id\"].astype(str)\n", "actual_cpd_1 = actual_cpd1.rename(columns={\"item_id\": \"Item ID\"})\n", "actual_cpd_2 = pd.merge(\n", "    left=actual_cpd_1, right=cat1, left_on=[\"Item ID\"], right_on=[\"Item ID\"], how=\"left\"\n", ")\n", "actual_cpd_3 = actual_cpd_2.loc[\n", "    :,\n", "    [\n", "        \"facility\",\n", "        \"facility_id\",\n", "        \"Item ID\",\n", "        \"name\",\n", "        \"l7_aactual_carts\",\n", "        \"live_days\",\n", "        \"sales_qty\",\n", "        \"l7_cpd\",\n", "        \"l30_cpd\",\n", "        \"grid_capping\",\n", "        \"potential_qpc\",\n", "    ],\n", "]\n", "sheet_id1 = \"1TPQOU5J-K_2M0mK1fqXM8l7Dz_1J6oJkJaoV4R_mtSY\"\n", "Multiplier = pb.from_sheets(sheet_id1, \"Multiplier\")\n", "Multiplier1 = Multiplier.loc[Multiplier.facility_id != \"\"]\n", "Multiplier2 = Multiplier1.loc[:, [\"facility_id\", \"Item ID\", \"Multiplier\"]]\n", "Multiplier2[\"facility_id\"] = Multiplier2[\"facility_id\"].astype(int)\n", "Multiplier2[\"Multiplier\"] = Multiplier2[\"Multiplier\"].astype(float)\n", "Multiplier2.drop_duplicates(\n", "    subset=[\"Item ID\", \"facility_id\"], keep=\"first\", inplace=True\n", ")\n", "\n", "actual_cpd_3 = pd.merge(\n", "    left=actual_cpd_3,\n", "    right=Multiplier2,\n", "    left_on=[\"Item ID\", \"facility_id\"],\n", "    right_on=[\"Item ID\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "actual_cpd_3[\"Multiplier\"].fillna(1, inplace=True)\n", "actual_cpd_4 = pd.merge(\n", "    left=actual_cpd_3,\n", "    right=final_base,\n", "    left_on=[\"Item ID\", \"facility\"],\n", "    right_on=[\"Item ID\", \"facility\"],\n", "    how=\"left\",\n", ")  # working file\n", "actual_cpd_4 = actual_cpd_4.sort_values(\n", "    [\"facility\", \"Item ID\"], ascending=[True, False]\n", ")\n", "sheet_id1 = \"1TPQOU5J-K_2M0mK1fqXM8l7Dz_1J6oJkJaoV4R_mtSY\"\n", "pb.to_sheets(actual_cpd_4, sheetid=sheet_id1, sheetname=\"Perishable_forecast_working\")\n", "actual_cpd_4.to_csv(td + \"_Perishable_forecast_working.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_file = f_base1.loc[:, [\"Item ID\", \"facility\"]]\n", "cpd_file2 = pd.merge(\n", "    left=cpd_file, right=cat1, left_on=[\"Item ID\"], right_on=[\"Item ID\"], how=\"left\"\n", ")\n", "cpd_file3 = pd.merge(\n", "    left=cpd_file2,\n", "    right=faci_faci1,\n", "    left_on=[\"facility\"],\n", "    right_on=[\"facility\"],\n", "    how=\"left\",\n", ")\n", "cpd_file4 = cpd_file3.loc[:, [\"facility\", \"facility_id\", \"Item ID\", \"name\"]]\n", "cpd_file5 = pd.merge(\n", "    left=cpd_file4,\n", "    right=cpd_base,\n", "    left_on=[\"Item ID\", \"facility\"],\n", "    right_on=[\"Item ID\", \"facility\"],\n", "    how=\"left\",\n", ")  # cpd file\n", "cpd_file5 = cpd_file5.sort_values([\"facility\", \"Item ID\"], ascending=[True, False])\n", "update_at = pd.DataFrame(\n", "    {\n", "        \"Update_at\": [\n", "            (dt.datetime.now() + timedelta(hours=5) + timedelta(minutes=30)).strftime(\n", "                \"%Y-%m-%d %H:%M:%S\"\n", "            )\n", "        ]\n", "    }\n", ")\n", "sheet_id1 = \"1TPQOU5J-K_2M0mK1fqXM8l7Dz_1J6oJkJaoV4R_mtSY\"\n", "pb.to_sheets(cpd_file5, sheetid=sheet_id1, sheetname=\"forecast_perishable\")\n", "cpd_file5.to_csv(td + \"_Perishable_forecast1.csv\", index=False)\n", "pb.to_sheets(update_at, sheetid=sheet_id1, sheetname=\"updated_at\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1TPQOU5J-K_2M0mK1fqXM8l7Dz_1J6oJkJaoV4R_mtSY\"\n", "da1 = pb.from_sheets(sheet_id, \"perishable_items1\")\n", "data_su = pd.DataFrame(\n", "    {\n", "        \"_\": [\n", "            \"Total skus provide for forecasting\",\n", "            \"Total skus come unber forecasting\",\n", "            \"Total skus having no L7 sales \",\n", "            \"Total skus having wrong facility id error\",\n", "        ],\n", "        \"Sku_count\": [\n", "            da1.shape[0],\n", "            actual_cpd_4.shape[0],\n", "            len(da1.loc[da1.checks == \"ok\", \"checks\"]) - actual_cpd_4.shape[0],\n", "            len(da1.loc[da1.checks == \"wrong facility id\", \"checks\"]),\n", "        ],\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_p = pd.DataFrame(\n", "    {\n", "        \"Column_name\": [\"w1\", \"w2\", \"w3\", \"w4\", \"w5\", \"w6\"],\n", "        \"Column_definition\": [\n", "            \"Date 1st-10th of current Month\",\n", "            \"Date 11th-20th of current Month\",\n", "            \"Date 21th-till end of current Month\",\n", "            \"Date 1st-10th of next Month\",\n", "            \"Date 11th-20th of next Month\",\n", "            \"Date 21th-till end of next Month\",\n", "        ],\n", "    }\n", ")\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "local_filename = td + \"_Perishable_forecast_working.csv\"\n", "cloud_filepath = \"pencilbox/gaurav/availability/\" + local_filename\n", "pb.to_s3(local_filename, bucket_name, cloud_filepath)\n", "working_f = create_presigned_url(\n", "    bucket_name, object_name=cloud_filepath, expiration=60 * 60 * 96\n", ")\n", "local_filename1 = td + \"_Perishable_forecast1.csv\"\n", "cloud_filepath1 = \"pencilbox/gaurav/availability/\" + local_filename1\n", "pb.to_s3(local_filename1, bucket_name, cloud_filepath1)\n", "forecast_f = create_presigned_url(\n", "    bucket_name, object_name=cloud_filepath1, expiration=60 * 60 * 96\n", ")\n", "html_content = (\n", "    \"\"\"Hi Team,<br/><br/>\n", "                           <p> Please find a link of perishable forecast wroking file and facility-wise CperD below. <br/> \n", "                             Kindly go through the same and let me know in case of any query.<br/> \n", "                             <br/> <br/>\n", "                            <b> Peroshable forecast wroking file link </b><br/></p>\"\"\"\n", "    + working_f\n", "    + \"\"\"<br/> <br/><br/><p> <b> Facility level avg CperD link  </b><br/></p>\n", "                            \"\"\"\n", "    + forecast_f\n", "    + \"\"\"\n", "                            <p style=\"color:red\"><br/><br/> Note: Above link will be expire in next 96 hours <br/></p>\n", "                            <p><br/> Columns name definition <br/>\n", "                            </p>\n", "                            {0}\n", "                            <br/>\n", "                            <p><br/> Sku count for forecasting  <br/>\n", "                            </p>\n", "                            {1}\n", "                            <br/>\n", "                            \"\"\".format(\n", "        data_p.to_html(index=False, justify=\"center\", classes=\"table table-bordered\"),\n", "        data_su.to_html(index=False, justify=\"center\", classes=\"table table-bordered\"),\n", "    )\n", ")\n", "from_email = \"<EMAIL>\"\n", "sheet_id = \"1VBcmA-8XkeYpVNn6BlOttzWLklAJYuBE8tDzDP7sE8I\"\n", "email_id = pb.from_sheets(sheet_id, \"forecast_Perishable\")\n", "to_email = list(email_id[\"email\"])\n", "\n", "subject = \"Perishable forecast \" + td\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Q2 = \"\"\"\n", "SELECT \n", "item_id,(CASE\n", "    WHEN storage_type = 1 THEN 'HOT'\n", "\tWHEN storage_type = 2 THEN 'COLD'\n", "\tWHEN storage_type = 3 THEN 'COLD'\n", "\tWHEN storage_type = 4 THEN 'HOT'\n", "\tWHEN storage_type = 5 THEN 'HOT'\n", "\tWHEN storage_type = 6 THEN 'COLD'\n", "\tWHEN storage_type = 7 THEN 'COLD'\n", "\tWHEN storage_type = 8 THEN 'HOT'\n", "     ELSE 'HOT'\n", "     END) AS Cold_Hot\n", "from  lake_rpc.product_product  p\n", "WHERE p.id in ( SELECT max(id) as id from  lake_rpc.product_product  pp WHERE pp.approved=1 GROUP BY item_id) \n", "\"\"\"\n", "item1_code = pd.read_sql(Q2, con=redshift_con)\n", "item_code = item1_code.rename(columns={\"item_id\": \"Item ID\"})\n", "Multiplier1 = Multiplier.loc[Multiplier.facility_id != \"\"]\n", "Multiplier2 = Multiplier1.loc[:, [\"Facility\", \"Item ID\", \"Multiplier\"]]\n", "Multiplier2 = Multiplier2.rename(columns={\"Facility\": \"facility\"})\n", "Multiplier2[\"Multiplier\"] = Multiplier2[\"Multiplier\"].astype(float)\n", "f_base1 = pd.merge(\n", "    left=f_base1,\n", "    right=Multiplier2,\n", "    left_on=[\"Item ID\", \"facility\"],\n", "    right_on=[\"Item ID\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "f_base1[\"Multiplier\"].fillna(1, inplace=True)\n", "for mm in remd_m + nxt_m:\n", "    f_base1[mm] = f_base1[mm] * f_base1[\"Multiplier\"]\n", "\n", "f_base1[\"Item ID\"] = f_base1[\"Item ID\"].astype(float)\n", "f_base1[\"Item ID\"] = f_base1[\"Item ID\"].astype(int)\n", "\n", "\n", "cperd = f_base1.loc[:, [\"facility\", \"Item ID\"] + remd_m + nxt_m]\n", "\n", "item_code1 = item_code.loc[:, [\"Item ID\", \"cold_hot\"]]\n", "item_code1 = item_code1.rename(columns={\"cold_hot\": \"Storage_type\"})\n", "item_code1.drop_duplicates(subset=[\"Item ID\"], inplace=True)\n", "item_code1[\"Item ID\"] = item_code1[\"Item ID\"].astype(str)\n", "cperd[\"Item ID\"] = cperd[\"Item ID\"].astype(str)\n", "cperd1 = pd.merge(\n", "    left=cperd, right=item_code1, left_on=\"Item ID\", right_on=\"Item ID\", how=\"left\"\n", ")\n", "cperd1[\"Storage_type\"].fillna(\"HOT\", inplace=True)\n", "cperd1[\"Storage_type\"] = np.where(\n", "    cperd1[\"Storage_type\"] == \"Not Assigned\", \"HOT\", cperd1[\"Storage_type\"]\n", ")\n", "cperd2 = pd.merge(\n", "    left=cperd1,\n", "    right=facility_HC_mapping1,\n", "    left_on=[\"Storage_type\", \"facility\"],\n", "    right_on=[\"storage_type\", \"facility\"],\n", "    how=\"left\",\n", ")\n", "cperd5 = cperd2.rename(columns={\"outlet_id\": \"Outlet ID\"})\n", "cperd5[\"Default CPD\"] = np.mean(cperd5.loc[:, remd_m + nxt_m], axis=1)\n", "cperd5[\"Default CPD\"].fillna(0.001, inplace=True)\n", "cperd6 = cperd5.loc[:, [\"Item ID\", \"Outlet ID\", \"Default CPD\"] + remd_m + nxt_m]\n", "for mo in remd_m + nxt_m:\n", "    cperd6[mo] = np.where(cperd6[mo] < 0.01, 0.01, cperd6[mo])\n", "    cperd6[mo] = round(cperd6[mo], 3).astype(float)\n", "cperd6[\"Default CPD\"] = round(cperd6[\"Default CPD\"], 3).astype(float)\n", "cperd6[\"Default CPD\"] = np.where(\n", "    cperd6[\"Default CPD\"] < 0.01, 0.01, cperd6[\"Default CPD\"]\n", ")\n", "cperd6.to_csv(td + \"_Perishables_forecast.csv\", index=False)\n", "file_name = td + \"_Perishables_forecast.csv\"\n", "chunk_file_name = file_name + \"_1_.csv\"\n", "cperd6.to_csv(chunk_file_name, index=False)\n", "s3_file_path = \"po/\" + chunk_file_name\n", "\n", "# Push to S3\n", "bucket_name = secrets.get(\"aws_bucket_name\")\n", "aws_key = secrets.get(\"aws_access_key\")\n", "aws_secret = secrets.get(\"aws_secret_key\")\n", "region_name = secrets.get(\"aws_region_name\")\n", "session = boto3.Session(\n", "    aws_access_key_id=aws_key, aws_secret_access_key=aws_secret, region_name=region_name\n", ")\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "bucket_obj.upload_file(chunk_file_name, s3_file_path)\n", "snorlax_response = requests.post(\n", "    \"https://retail-internal.grofer.io/snorlax/v3/upload/submit/\",\n", "    json={\"file_path\": s3_file_path, \"user_id\": 1887},\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}