{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "# retail = pb.get_connection(\"retail\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "rds_ims = pb.get_connection(\"[Replica] RDS IMS\")\n", "rds_po = pb.get_connection(\"[Replica] RDS PO\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 5\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Base Input | Item <> City"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    item_city_ip = pb.from_sheets(\n", "        \"1-8E4mDVA9f8OoNPnDGMn5PnnwGszwaepjGUAFsryWKY\", \"ip_new\"\n", "    )\n", "    item_city_state = (\n", "        item_city_ip[[\"item_id\", \"city\", \"state\"]]\n", "        .drop_duplicates()\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    item_city_state = (\n", "        item_city_state[\n", "            ~(\n", "                (item_city_state.item_id.isna())\n", "                | (item_city_state.city.isna())\n", "                | (item_city_state.state.isna())\n", "            )\n", "        ]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    item_city_state = (\n", "        item_city_state[\n", "            ~(\n", "                (item_city_state.item_id == \"\")\n", "                | (item_city_state.city == \"\")\n", "                | (item_city_state.state == \"\")\n", "            )\n", "        ]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "    item_city_state[[\"item_id\", \"state\"]] = item_city_state[\n", "        [\"item_id\", \"state\"]\n", "    ].astype(int)\n", "\n", "    active_facility_ip = (\n", "        item_city_ip[[\"facility_id\"]]\n", "        .drop_duplicates()\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    active_facility_ip = active_facility_ip[\n", "        ~(\n", "            active_facility_ip.facility_id.isna()\n", "            | (active_facility_ip.facility_id == \"\")\n", "        )\n", "    ]\n", "    active_facility_ip[\"facility_id\"] = active_facility_ip[\"facility_id\"].astype(int)\n", "\n", "    distinct_cities = tuple(list(item_city_state[\"city\"].unique()))\n", "\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# item_city_state\n", "item_city_state\n", "distinct_cities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Base Input | Item <> Facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    item_facility_ip = pb.from_sheets(\n", "        \"1-8E4mDVA9f8OoNPnDGMn5PnnwGszwaepjGUAFsryWKY\", \"ip_fac\"\n", "    )\n", "    item_facility_state_01 = (\n", "        item_facility_ip[[\"item_id\", \"outlet_id\", \"facility_id\", \"state\"]]\n", "        .drop_duplicates()\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    item_facility_state_01 = (\n", "        item_facility_state_01[\n", "            ~(\n", "                (item_facility_state_01.item_id.isna())\n", "                | (item_facility_state_01.outlet_id.isna())\n", "                | (item_facility_state_01.facility_id.isna())\n", "                | (item_facility_state_01.state.isna())\n", "            )\n", "        ]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    item_facility_state_01 = (\n", "        item_facility_state_01[\n", "            ~(\n", "                (item_facility_state_01.item_id == \"\")\n", "                | (item_facility_state_01.outlet_id == \"\")\n", "                | (item_facility_state_01.facility_id == \"\")\n", "                | (item_facility_state_01.state == \"\")\n", "            )\n", "        ]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "    item_facility_state_01[\n", "        [\"item_id\", \"outlet_id\", \"facility_id\", \"state\"]\n", "    ] = item_facility_state_01[[\"item_id\", \"outlet_id\", \"facility_id\", \"state\"]].astype(\n", "        int\n", "    )\n", "    item_facility_state_01 = item_facility_state_01[\n", "        [\"item_id\", \"state\", \"outlet_id\", \"facility_id\"]\n", "    ].drop_duplicates()\n", "\n", "\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_facility_state_01"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### City - DS Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    city_ds_sql = f\"\"\"\n", "    with base as (\n", "    select co.id as outlet_id, co.facility_id,cl.name as city\n", "    from lake_view_retail.console_outlet co\n", "    inner join (select facility_id, max(updated_at) as updated_at\n", "                from lake_view_retail.console_outlet\n", "                group by 1) cc on cc.facility_id=co.facility_id  and cc.updated_at=co.updated_at\n", "    left join lake_view_retail.console_location cl on cl.id=co.tax_location_id\n", "    where cl.name in {distinct_cities}\n", "    GROUP BY 1,2,3\n", "    ),\n", "    \n", "    eligible_ds as (\n", "        select outlet_id from\n", "            (select a.outlet as outlet_id, count(distinct order_id) as order_count\n", "            from lake_view_ims.ims_order_details a\n", "            left join lake_view_retail.console_outlet o on a.outlet = o.id\n", "            where a.insert_ds_ist >= cast(current_date-interval '2' day as varchar)\n", "            and a.status_id <> 5\n", "            and business_type not like '%%b2b%%'\n", "            group by 1) b\n", "        where order_count > 10\n", "        GROUP BY 1\n", "        )\n", "\n", "    SELECT b.* FROM base b\n", "    INNER JOIN eligible_ds ed on ed.outlet_id = b.outlet_id\n", "    \"\"\"\n", "\n", "    city_ds = read_sql_query(city_ds_sql, presto)\n", "    # city_ds = city_ds[['facility_id','city']].drop_duplicates().reset_index().drop(columns={'index'})\n", "    # city_ds\n", "\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_ds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "\n", "    item_city_state_pan_india = (\n", "        item_city_state[item_city_state[\"city\"] == \"pan_india\"]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    item_city_state_city_wise = (\n", "        item_city_state[~(item_city_state[\"city\"] == \"pan_india\")]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "    print(\n", "        item_city_state.shape[0],\n", "        item_city_state_pan_india.shape[0] + item_city_state_city_wise.shape[0],\n", "    )\n", "\n", "    item_city_state_city_wise = pd.merge(\n", "        item_city_state_city_wise, city_ds, on=[\"city\"], how=\"inner\"\n", "    )\n", "\n", "    active_facility_pan_india = city_ds.copy()\n", "    active_facility_pan_india[\"city\"] = \"pan_india\"\n", "    item_city_state_pan_india = pd.merge(\n", "        item_city_state_pan_india, active_facility_pan_india, on=[\"city\"], how=\"inner\"\n", "    )\n", "\n", "    item_facility_state = (\n", "        pd.concat([item_city_state_city_wise, item_city_state_pan_india])\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    item_facility_state = item_facility_state.drop(columns={\"city\"})\n", "    try:\n", "        item_facility_state = (\n", "            pd.concat([item_facility_state, item_facility_state_01])\n", "            .reset_index()\n", "            .drop(columns={\"index\"})\n", "        )\n", "    except:\n", "        pass\n", "    item_facility_state[\"updated_at\"] = pd.to_datetime(\n", "        datetime.today() + timedelta(hours=5.5) - timedelta(days=0)\n", "    ).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    item_facility_state[\"updated_at\"] = item_facility_state[\"updated_at\"].astype(\n", "        \"datetime64[ns]\"\n", "    )\n", "    item_facility_state = item_facility_state[\n", "        [\"item_id\", \"outlet_id\", \"facility_id\", \"state\", \"updated_at\"]\n", "    ]\n", "    item_facility_state.dtypes\n", "\n", "    df = item_facility_state.copy()\n", "    df.dtypes\n", "\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"special_events_input\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "            {\n", "                \"name\": \"outlet_id\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"Dark Store and Backend Outlet ID\",\n", "            },\n", "            {\n", "                \"name\": \"facility_id\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"Dark Store and Backend Facility ID\",\n", "            },\n", "            {\n", "                \"name\": \"state\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"Distinct State of Events\",\n", "            },\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"Time when the table was last updated\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"item_id\", \"outlet_id\", \"facility_id\"],\n", "        \"sortkey\": [\"item_id\", \"outlet_id\", \"facility_id\"],\n", "        \"incremental_key\": \"item_id\",\n", "        \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"Special Events Input\",  # Description of the table being sent to redshift\n", "    }\n", "\n", "    pb.to_redshift(df, **kwargs)\n", "\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_sql = \"\"\"\n", "select distinct state from metrics.special_events_input\n", "order by 1\n", "\"\"\"\n", "query = read_sql_query(query_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}