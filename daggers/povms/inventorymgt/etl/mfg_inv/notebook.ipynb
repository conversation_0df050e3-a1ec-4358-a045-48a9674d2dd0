{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "redshift = pb.get_connection(\"redshift\").connect()\n", "import pandas as pd\n", "import boto3\n", "import os\n", "import smtplib\n", "from email.message import EmailMessage\n", "import numpy as np\n", "import pencilbox as pb\n", "import datetime as dt\n", "from botocore.exceptions import ClientError\n", "\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "ld = dt.datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "\n", "def create_presigned_url(file_name, object_name, expiration=3600):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\"s3\")\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "with\n", "\n", "fac_city as\n", "(\n", "select distinct co.facility_id,cl.name as city\n", "from lake_retail.console_outlet co\n", "inner join (select facility_id, max(updated_at) as updated_at\n", "            from lake_retail.console_outlet\n", "            group by 1) cc on cc.facility_id=co.facility_id  and cc.updated_at=co.updated_at\n", "left join lake_retail.console_location cl on cl.id=co.tax_location_id\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "a.item_id,\n", "a.name as item_name,\n", "a.brand,\n", "a.brand_id,\n", "pb.manufacturer_id as mfg_id,\n", "pm.name as mfg,\n", "a.variant_description,\n", "a.storage_type,\n", "a.outer_case_size,\n", "a.inner_case_size,\n", "a.is_pl,\n", "a.variant_mrp,\n", "row_number() over (partition by a.item_id order by a.updated_at desc) as row_rank\n", "from\n", "laKe_rpc.product_product a\n", "LEFT JOIN lake_rpc.product_brand pb ON pb.id = a.brand_id\n", "LEFT JOIN lake_rpc.product_manufacturer pm ON pm.id=pb.manufacturer_id \n", "),\n", "\n", "\n", "lp_treatment_tbl as\n", "(\n", "select \n", "a.item_id,\n", "outlet_id,\n", "b.warehouse_id,\n", "facility_id,\n", "landing_price,\n", "row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "row_number() over (partition by a.item_id order by dt_ist desc) as row_rank1\n", "from weighted_landing_price a\n", "left join (select * from  lake_retail.warehouse_outlet_mapping  where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "left join (select * from  lake_retail.view_console_outlet  where active = true) c on b.warehouse_id = c.id\n", "where landing_price is not null\n", "),\n", "\n", "\n", "tbl_treatment as\n", "(\n", "select \n", "*, (actual_quantity - blocked_quantity ) as unblocked_quantity\n", "from rpc_daily_availability\n", "where \n", "order_date = (select max(order_date) from rpc_daily_availability)\n", "),\n", "\n", "final as\n", "(\n", "select\n", "a.item_id,\n", "a.facility_id,\n", "a.facility_name,\n", "sum(coalesce(actual_quantity,0)) as total_quantity,\n", "sum(blocked_quantity) as blocked_quantity,\n", "sum(unblocked_quantity) as unblocked_quantity,\n", "sum(case \n", "when b.item_id is not null then actual_quantity*b.landing_price*1.000\n", "when c.item_id is not null then actual_quantity*c.landing_price*1.000\n", "else d.variant_mrp*0.7000 end) as total_value,\n", "sum(case \n", "when b.item_id is not null then blocked_quantity*b.landing_price*1.000\n", "when c.item_id is not null then blocked_quantity*c.landing_price*1.000\n", "else d.variant_mrp*0.7000 end) as blocked_value,\n", "sum(case \n", "when b.item_id is not null then unblocked_quantity*b.landing_price*1.000\n", "when c.item_id is not null then unblocked_quantity*c.landing_price*1.000\n", "else d.variant_mrp*0.7000 end) as unblocked_value\n", "from tbl_treatment a\n", "left join (select * from lp_treatment_tbl where row_rank = 1) b on a.facility_id = b.facility_id  AND a.item_id = b.item_id\n", "left join (select * from lp_treatment_tbl where row_rank1 = 1) c on a.item_id = c.item_id\n", "left join (select * from item_level_info where row_rank = 1) d on a.item_id = d.item_id\n", "group by 1,2,3\n", ")\n", "\n", "select \n", "cl.city,\n", "a.item_id, rp.item_name, rp.variant_description,\n", "vv.vendor_id, vn.vendor_name,\n", "rp.mfg_id, rp.mfg,\n", "--sum(a.total_quantity) as total_qty,\n", "--sum(a.total_value) as total_val,\n", "--sum(a.blocked_quantity) as blocked_qty, \n", "--sum(a.blocked_value) as blocked_val,\n", "sum(a.unblocked_quantity) as unblocked_qty, \n", "sum(a.unblocked_value) as unblocked_val\n", "from final a\n", "left join (select item_id, facility_id , vendor_id from  lake_vms.vms_vendor_facility_alignment \n", "where active = 1  group by 1,2,3)vv on vv.item_id = a.item_id and vv. facility_id = a.facility_id\n", "left join  lake_vms.vms_vendor  vn on vn.id = vv. vendor_id \n", "\n", "--inner join metrics.soh_city_facility_mapping cl on a.facility_id = cl.facility_id\n", "inner join fac_city cl  on a.facility_id = cl.facility_id\n", "\n", "left join (select * from item_level_info where row_rank = 1) rp on a.item_id = rp.item_id\n", "where\n", "total_quantity > 0\n", "group by 1,2,3,4,5,6,7,8\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_LO = pd.read_sql_query(q, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapped_LO.mfg_id.csv('mm.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id = \"17mj8eSGdLUhz0cQd4WcXR99eemYgFB2zFjBzgdrv8Ho\"\n", "# sheet_name = 'Facility - City Mapping'\n", "# fc_map= pb.from_sheets(sheet_id, sheet_name)\n", "# fc_map[\"facility_id\"] = fc_map[\"facility_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapped_LO=pd.merge(left=mfg_inv,right=fc_map,left_on=['facility_id'],right_on=['facility_id'],how='inner')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapped_LO[\"total_qty\"] = round(Mapped_LO[\"total_qty\"], 0).astype(float)\n", "# Mapped_LO[\"total_val\"] = round(Mapped_LO[\"total_val\"], 0).astype(float)\n", "# Mapped_LO[\"blocked_qty\"] = round(Mapped_LO[\"blocked_qty\"], 0).astype(float)\n", "# Mapped_LO[\"blocked_val\"] = round(Mapped_LO[\"blocked_val\"], 0).astype(float)\n", "Mapped_LO[\"unblocked_qty\"] = round(Mapped_LO[\"unblocked_qty\"], 0).astype(float)\n", "Mapped_LO[\"unblocked_val\"] = round(Mapped_LO[\"unblocked_val\"], 0).astype(float)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Email Mail Body"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "import ast"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NOW = dt.datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")\n", "NOW_str = NOW\n", "NOW = dt.datetime.strptime(NOW, \"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = NOW - dt.<PERSON><PERSON><PERSON>(days=7)\n", "start_date_str = dt.datetime.strftime(start_date, \"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"17mj8eSGdLUhz0cQd4WcXR99eemYgFB2zFjBzgdrv8Ho\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_df = pb.from_sheets(sheet_id, \"vendor_email_id_soh\")\n", "vendor_df = vendor_df[vendor_df[\"send_mail_flag\"] == \"1\"]\n", "vendor_df.rename(\n", "    columns={\"Email ID # TO\": \"email_id\", \"Vendor_Name\": \"Vendor_Name2\"}, inplace=True\n", ")\n", "vendor_df[\"mfg_id\"] = pd.to_numeric(vendor_df[\"mfg_id\"])\n", "vendor_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_df[\"email_list\"] = vendor_df[\"email_id\"].str.lower()\n", "vendor_df[\"email_list\"].replace(r\"\\s\", \"\", regex=True, inplace=True)\n", "vendor_df[\"email_list\"] = vendor_df.email_list.str.split(\",\")\n", "vendor_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mft_email_list = (\n", "    vendor_df.groupby([\"mfg_id\", \"mfg_name\"])[\"email_list\"].sum().reset_index()\n", ")\n", "\n", "mft_email_dict = dict(zip(mft_email_list.mfg_id, mft_email_list.email_list))\n", "mft_id_name = dict(zip(mft_email_list.mfg_id, mft_email_list.mfg_name))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mft_email_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inhouse_mail = pb.from_sheets(sheet_id, \"inhouse_mail SOH\")\n", "inhouse_mail_list = list(inhouse_mail[\"email\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def summary1(x):\n", "    names = {\n", "        #         \"Act_Qty\": x[\"total_qty\"].sum(),\n", "        #         \"Act_Val\": x[\"total_val\"].sum(),\n", "        #         \"Blk_Qty\": x[\"blocked_qty\"].sum(),\n", "        #         \"Blk_Val\": x[\"blocked_val\"].sum(),\n", "        \"Net_Qty\": x[\"unblocked_qty\"].sum(),\n", "        \"Net_Val\": x[\"unblocked_val\"].sum(),\n", "    }\n", "    return pd.Series(names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "\n", "# path = \"/usr/local/airflow/dags/repo/dags/povms/purchase_mis_report/report/po_fillrate_report/\"\n", "path = \"/povms/inventorymgt/etl/mfg_inv/\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=path,\n", ")\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(\"email_template.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mft_email_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in mft_email_dict.keys():\n", "    if Mapped_LO[Mapped_LO[\"mfg_id\"] == k].shape[0] != 0:\n", "        df = Mapped_LO[Mapped_LO[\"mfg_id\"] == k]\n", "\n", "        summary_table1 = df.groupby([\"city\"]).apply(summary1).reset_index()\n", "\n", "        d = {\n", "            \"city\": [\"Pan India\"],\n", "            \"Net_Qty\": df[\"unblocked_qty\"].sum(),\n", "            \"Net_Val\": df[\"unblocked_val\"].sum(),\n", "        }\n", "\n", "        p = pd.DataFrame(data=d)\n", "\n", "        summary_table = pd.concat([p.fillna(\"Pan India\"), summary_table1])\n", "\n", "        df.to_csv(mft_id_name[k] + ld + \"Inventory.csv\", index=False)\n", "\n", "        sum1 = summary_table.to_dict(orient=\"rows\")\n", "        subject = \"Inventory Report for \" + mft_id_name[k]\n", "        to_email = list(set(mft_email_dict[k])) + inhouse_mail_list\n", "        # to_email= ['<EMAIL>,']\n", "        # to_email = \"<EMAIL>\"\n", "        from_email = \"<EMAIL>\"\n", "        # from_email = \"<EMAIL>,\"\n", "        message_text = template.render(products=sum1, manufacturer=mft_id_name[k])\n", "\n", "        pb.send_email(\n", "            from_email,\n", "            to_email,\n", "            subject=subject,\n", "            html_content=message_text,\n", "            files=[mft_id_name[k] + ld + \"Inventory.csv\"],\n", "        )\n", "        print(to_email)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}