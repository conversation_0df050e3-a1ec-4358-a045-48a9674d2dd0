{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import time\n", "\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "rds_ims = pb.get_connection(\"[Replica] RDS IMS\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. state = pb.from_sheeets('')\n", "# 2. pick only active assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1G4MmzMNk_JnCqU-rEa7HFZBhYZ7E313tkEAjPTD6uKc\"\n", "sheet_name = \"state_mapping\"\n", "df1 = pb.from_sheets(sheet_id, sheet_name)\n", "# df.head(10)\n", "\n", "df1[\"ID\"] = df1[\"ID\"].astype(int)\n", "df1[\"Name\"] = df1[\"Name\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(df1[\"ID\"]) > 1:\n", "    state = tuple(list(df1[\"ID\"].astype(int).unique()))\n", "else:\n", "    k = df1[\"ID\"][0]\n", "    state = (k, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_query = f\"\"\"\n", "with\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "manufacturer as mfg,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", ")\n", "\n", "\n", "select \n", "si.state,\n", "o.facility_id, cf.name as facility,\n", "poi.item_id as Item_Id, d.item_name, d.mfg,\n", "sum(poi.remaining_quantity) as open_po_qty,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') < current_date then p.po_number end ,  ' , ') as scht_less_today,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') = current_date then p.po_number end ,  ' , ') as today_scht,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') = current_date+1 then p.po_number end ,  ' , ') as sch_t_1,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') = current_date+2 then p.po_number end ,  ' , ') as sch_t_2,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') = current_date+3 then p.po_number end ,  ' , ') as sch_t_3,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') = current_date+4 then p.po_number end ,  ' , ') as sch_t_4,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') = current_date+5 then p.po_number end ,  ' , ') as sch_t_5,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') > current_date+5 then p.po_number end ,  ' , ') as sch_t_plus5,\n", "listagg(case when date(ps.schedule_date_time  + interval '5.5 Hours') is null then p.po_number end ,  ' , ') as no_scht \n", "\n", "from  lake_po.purchase_order  p\n", "Left Join lake_po.po_schedule ps on p.id = ps.po_id_id\n", "inner join lake_po.purchase_order_items poi on p.id=poi.po_id\n", "inner join lake_retail.console_outlet o on o.id=p.outlet_id\n", "inner join lake_po.purchase_order_status posa on posa.po_id = p.id\n", "inner join lake_po.purchase_order_state posta on posta.id = posa.po_state_id\n", "left join (select po_id, item_id, sum(quantity) as grn_qty from lake_po.po_grn group by 1,2)pg \n", "on pg.po_id = poi.po_id and pg.item_id = poi.item_id\n", "inner join metrics.special_events_input si on poi.item_id = si.item_id and o.facility_id = si.facility_id\n", "left join lake_crates.facility cf on cf.id = o.facility_id\n", "left join (select * from item_level_info where row_rank = 1) d on poi.item_id = d.item_id\n", "where\n", "( posta.name in ('Created','Scheduled','Unscheduled','Rescheduled') or p.is_multiple_grn = 1) and posta.name <> 'Expired'\n", "and date(p.issue_date + interval '5.5 Hours') between current_date-30 and Current_date\n", "and posta.name <> 'Cancelled'\n", "and posa.po_state_id  Not in (4,5,10)\n", "and si.state in {state}\n", "group by 1,2,3,4,5,6\n", "having sum(poi.remaining_quantity)>0\n", "order by 2,3\n", "\"\"\"\n", "open_po_view = read_sql_query(open_po_query, redshift)\n", "open_po_view.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_view.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    open_po_view, \"13IVWGgJBaP0yjjyDS3_ew9QPlw-_qp_gOW5nixD4U4g\", \"open_po_raw\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fillrate_query = f\"\"\"\n", "with\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "manufacturer as mfg,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", ")\n", "\n", "select state, A.FACILITY_ID, A.FACILITY_NAME, a.ITEM_ID, item_name, mfg, SUM(PO_QTY) AS PO_QTY,  SUM(GRN_QTY) AS GRN_QTY\n", "from\n", "(\n", "select\n", "si.state,\n", "po.id as PO_Id,\n", "po.po_number as PO_Number,\n", "(case\n", "when po.po_type_id in (1,2) then 'Parent_PO'\n", "when po.po_type_id in (3,4,5) then 'Child_PO' else 'Internal_PO' end) as PO_Type,\n", "\n", "por.parent_po_id as Parent_PO_Id,\n", "\n", "date(po.issue_date + interval '5.5 Hours') as PO_Issue_Date,\n", "date(po.expiry_date + interval '5.5 Hours') as PO_Expiry_Date,  \n", "\n", "po.outlet_id as Outlet_Id,\n", "(Case when (poi.run_id) = 0 then 'Adhoc_PO'\n", "when poi.run_id is NULL then 'Adhoc_PO'\n", "else poi.run_id end) as  ARS_Id,\n", "\n", "co.name as Outlet_Name,\n", "\n", "co.facility_id as facility_id,\n", "cf.name as Facility_Name,\n", "\n", "po.vendor_id as Vendor_Id,\n", "po.vendor_name as Vendor_Name,\n", "poi.item_id as item_id,\n", "poi.name as Product_Name,\n", "d.item_name, d.mfg,\n", "poi.uom_text as VARIANT_UOM_TEXT,\n", "poi.brand as Brand_Name,\n", "poi.units_ordered as PO_QTY,\n", "poi.cost_price as Cost_Price,\n", "poi.landing_rate as Landing_Price,\n", "poi.total_amount as PO_Value,\n", "poi.mrp as PO_MRP,\n", "\n", "case\n", "when pos.po_state_id in (4,5,10) then 'Invalid'\n", "when (pos.po_state_id in (8) or date(po.expiry_date + interval '5.5 Hours') < current_date) and pgn.po_id is null  then 'Expired' \n", "when pos.po_state_id not in (4,5,10) and pgn.po_id is not null and po.is_multiple_grn = 1 then 'MultiGRN_Serviced'\n", "when  pos.po_state_id not in (4,5,10) and pgn.po_id is not null and is_multiple_grn <> 1  then 'Serviced'\n", "else 'Pending' end as PO_Status,\n", "\n", "(case when pos.po_state_id not in (4,5,10) then 'Not_Cancelled'\n", "else 'Cancelled' end) as Cancel_Status,\n", "\n", "(case when pos.po_state_id in (4,5,10) then 'Invalid'\n", "when pgn.po_id is not null or pos.po_state_id in (8) then 'Closed' else 'Open' end) as Open_Status,\n", "\n", "case when pos.po_state_id not in (4,5,10) then coalesce(pg.quantity,0) else 0 end as GRN_QTY,\n", "case when pos.po_state_id not in (4,5,10) then coalesce(pg.grn_val,0) else 0 end as GRN_Value,\n", "case when pos.po_state_id not in (4,5,10) then date(pg.grn_d) end as GRN_Date,\n", "\n", "po.is_multiple_grn as Is_Multiple_GRN\n", "\n", "from lake_po.purchase_order po\n", "inner join lake_po.purchase_order_items poi on po.id = poi.po_id\n", "and date(po.issue_date  + interval '5.5 Hours') between  current_date-45 and current_date\n", "\n", "left join lake_po.purchase_order_relations por on por.child_po_id = po.id\n", "left join lake_po.purchase_order_status pos on pos.po_id = po.id\n", "inner join lake_retail.view_console_outlet co on co.id = po.outlet_id\n", "\n", "left join (select po_id, item_id, max(date(created_at  + interval '5.5 Hours')) as grn_d, \n", "           sum(quantity) as quantity, sum(quantity*landing_price) as grn_val\n", "          from lake_po.po_grn  where date(created_at  + interval '5.5 Hours')  between  current_date-60 and current_date\n", "          group by 1,2)pg on pg.po_id = poi.po_id and pg.item_id = poi.item_id\n", "\n", "left join (select distinct po_id from lake_po.po_grn)pgn on po.id = pgn.po_id\n", "\n", "left join lake_crates.facility cf on cf.id = co.facility_id\n", "inner join metrics.special_events_input si on poi.item_id = si.item_id and co.facility_id = si.facility_id\n", "left join (select * from item_level_info where row_rank = 1) d on poi.item_id = d.item_id\n", "\n", "WHERE SI.STATE in {state} and pos.po_state_id not in (4,5,10) \n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32\n", "\n", ")a\n", "where PO_Expiry_Date = current_date-1 or (is_multiple_grn = 0 and GRN_Date = current_date-1)\n", "GROUP BY 1,2,3,4,5,6\n", "order by 2,3\n", "\"\"\"\n", "fill_rate_view = read_sql_query(fillrate_query, redshift)\n", "fill_rate_view.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_view.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(fill_rate_view, \"13IVWGgJBaP0yjjyDS3_ew9QPlw-_qp_gOW5nixD4U4g\", \"fr_raw\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_facility_item_sql1 = f\"\"\"\n", "with base as (\n", "        select distinct se.item_id, se.facility_id, se.state, ma.master_assortment_substate_id as active, ma.active as active1\n", "                \n", "        from metrics.special_events_input se\n", "        left join lake_rpc.product_facility_master_assortment ma on ma.item_id = se.item_id and ma.facility_id = se.facility_id \n", "        where se.state in {state} and ma.master_assortment_substate_id = 1 and ma.active = 1\n", "    )\n", "    \n", "select distinct item_id, facility_id, active1 , active, state\n", "from base\n", "\n", "\"\"\"\n", "input_facility_item1 = read_sql_query(input_facility_item_sql1, redshift)\n", "input_facility_item1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# input_facility_item_sql1 = f\"\"\"\n", "# with base as (select a.item_id, a.facility_id, a.master_assortment_substate_id as active, b.state,\n", "#         (case when b.state = 35 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 36 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 39 and a.master_assortment_substate_id = 1 then 1\n", "#         when b.state = 40 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 41 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 42 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 43 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 44 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 45 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 46 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 47 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 48 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         when b.state = 49 and a.master_assortment_substate_id in (1,2,3) then 1\n", "#         else 0 end) as check_flag\n", "# from lake_rpc.product_facility_master_assortment a\n", "# inner join (select distinct item_id, facility_id, state from metrics.special_events_input where state in (35,36,39,40,41,42,43,44,45,46,47,48,49)) b on a.item_id = b.item_id and a.facility_id = b.facility_id\n", "# and active=1\n", "# and master_assortment_substate_id in (1,2,3)\n", "# group by 1,2,3,4,5\n", "# )\n", "\n", "# SELECT distinct state, item_id, facility_id, active\n", "# from base\n", "# where check_flag = 1\n", "# \"\"\"\n", "# input_facility_item1 = read_sql_query(input_facility_item_sql1, redshift)\n", "\n", "# # input_facility_item1.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_facility_item1[\n", "    \"active\"\n", "] = 1  ###considering default active = 1 to calculate current availability"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item = tuple(list(input_facility_item1[\"item_id\"].astype(int).unique()))\n", "facility = tuple(list(input_facility_item1[\"facility_id\"].astype(int).unique()))\n", "\n", "\n", "base = input_facility_item1.copy()\n", "\n", "## Get facility name\n", "facility_name_sql = f\"\"\"\n", "select distinct facility_id, \n", "        REPLACE(cf.name, 'Super Store ', '') as facility_name\n", "from lake_retail.console_outlet co\n", "left join lake_crates.facility cf on cf.id=co.facility_id\n", "where co.facility_id in {facility}\n", "\"\"\"\n", "facility_name = read_sql_query(facility_name_sql, redshift)\n", "\n", "base_facility = pd.merge(base, facility_name, on=[\"facility_id\"], how=\"left\")\n", "\n", "## Get item name and category details\n", "item_name_sql = f\"\"\"\n", "SELECT distinct product.item_id, product.item_name, dp.l0_category as l0, dp.l1_category as l1, dp.l2_category as l2, product.brand\n", "FROM (SELECT item_id, name as item_name, brand FROM lake_rpc.product_product pp2\n", "        WHERE pp2.item_id in {item}\n", "        AND pp2.id in (\n", "        SELECT max(id) as id FROM lake_rpc.product_product pp \n", "        WHERE pp.active = 1 and pp.approved = 1 GROUP BY item_id)\n", "        ) product \n", "\n", "INNER JOIN (select item_id, product_id from lake_rpc.item_product_mapping) as ipm on product.item_id = ipm.item_id\n", "INNER JOIN (select product_id, product_name, l0_category, l1_category, l2_category from dwh.dim_product\n", "                where is_current='true') dp on ipm.product_id = dp.product_id\n", "\n", "\"\"\"\n", "item_name = read_sql_query(item_name_sql, redshift)\n", "\n", "base_item = pd.merge(base_facility, item_name, on=[\"item_id\"], how=\"left\")\n", "\n", "\n", "print(\n", "    \"\\n\", input_facility_item1.shape, base.shape, base_facility.shape, base_item.shape\n", ")\n", "# base_item.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### TEA Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_mapping_query = f\"\"\"SELECT DISTINCT item_id,\n", "                rco.facility_id as physical_facility_id,\n", "                pf.internal_facility_identifier AS frontend_name,\n", "                tag_value AS ordering_outlet,\n", "                rcob.name AS ordering_outlet_name,\n", "                rcob.facility_id as backend_facility_id\n", "FROM lake_rpc.item_outlet_tag_mapping tm\n", "LEFT JOIN lake_retail.console_outlet rco ON rco.id = tm.outlet_id\n", "INNER JOIN lake_po.physical_facility pf ON pf.facility_id = rco.facility_id\n", "LEFT JOIN lake_retail.console_outlet rcob ON rcob.id = tm.tag_value\n", "INNER JOIN lake_po.physical_facility pfb ON pfb.facility_id = rcob.facility_id\n", "WHERE tag_type_id = 8 and\n", "tm.active = 1 --and frontend_name not in ('Super Store Chennai C2 - Warehouse','Kolkata_ES2')\n", "ORDER BY pf.internal_facility_identifier ASC\"\"\"\n", "tea_mapping = read_sql_query(sql=tea_mapping_query, con=redshift)\n", "tea_mapping = tea_mapping[\n", "    [\"physical_facility_id\", \"item_id\", \"backend_facility_id\"]\n", "].drop_duplicates()\n", "\n", "variable = (\n", "    tea_mapping.groupby([\"physical_facility_id\", \"item_id\"])\n", "    .agg({\"backend_facility_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "variable = variable[variable.backend_facility_id > 1]\n", "variable[\"key\"] = (\n", "    variable[\"physical_facility_id\"].astype(str) + \"_\" + variable[\"item_id\"].astype(str)\n", ")\n", "variable = variable[[\"key\"]]\n", "\n", "tea_mapping[\"o_key\"] = (\n", "    tea_mapping[\"physical_facility_id\"].astype(str)\n", "    + \"_\"\n", "    + tea_mapping[\"item_id\"].astype(str)\n", ")\n", "\n", "tea_mapping_temp = pd.merge(\n", "    tea_mapping, variable, left_on=[\"o_key\"], right_on=[\"key\"], how=\"left\"\n", ")\n", "\n", "tea_mapping_non_dup = tea_mapping_temp[tea_mapping_temp.key.isna()]\n", "tea_mapping_dup = tea_mapping_temp[~tea_mapping_temp.key.isna()]\n", "\n", "duplicate_set = tea_mapping_dup[\n", "    [\"physical_facility_id\", \"backend_facility_id\"]\n", "].drop_duplicates()  # 517 for 428, 299?,  140?\n", "\n", "req_back = (\n", "    tea_mapping_non_dup.groupby([\"physical_facility_id\", \"backend_facility_id\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "# req_back[req_back.facility_id.isin([428, 299, 140])]\n", "\n", "req_back = pd.merge(\n", "    req_back,\n", "    duplicate_set,\n", "    on=[\"physical_facility_id\", \"backend_facility_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "req_back[\"rank\"] = req_back.groupby([\"physical_facility_id\"])[\"item_id\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "req_back = req_back[req_back[\"rank\"] == 1]\n", "req_back = req_back[[\"physical_facility_id\", \"backend_facility_id\"]].drop_duplicates()\n", "\n", "tea_mapping_dup = pd.merge(\n", "    tea_mapping_dup,\n", "    req_back,\n", "    on=[\"physical_facility_id\", \"backend_facility_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "tea_mapping = pd.concat([tea_mapping_dup, tea_mapping_non_dup])\n", "tea_mapping = tea_mapping.drop(columns={\"o_key\", \"key\"})\n", "tea_mapping.rename(columns={\"physical_facility_id\": \"facility_id\"}, inplace=True)\n", "\n", "# tea_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_tea = pd.merge(\n", "    base_item.rename(\n", "        columns={\n", "            \"facility_id\": \"frontend_facility_id\",\n", "            \"facility_name\": \"frontend_facility_name\",\n", "        }\n", "    ),\n", "    tea_mapping.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=[\"item_id\", \"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "base_tea.backend_facility_id.fillna(0, inplace=True)\n", "backend_facility = tuple(\n", "    filter(None, base_tea[\"backend_facility_id\"].astype(int).unique())\n", ")\n", "\n", "## Get backend facility name\n", "bf_name_sql = f\"\"\"\n", "select distinct facility_id as backend_facility_id, \n", "        REPLACE(cf.name, 'Super Store ', '') as backend_facility_name\n", "from lake_retail.console_outlet co\n", "left join lake_crates.facility cf on cf.id=co.facility_id\n", "where co.facility_id in {backend_facility}\n", "\"\"\"\n", "bf_name = read_sql_query(bf_name_sql, redshift)\n", "\n", "base_bf_name = pd.merge(base_tea, bf_name, on=[\"backend_facility_id\"], how=\"left\")\n", "base_bf_name.backend_facility_name.fillna(\"-\", inplace=True)\n", "\n", "\n", "item = tuple(filter(None, base_bf_name[\"item_id\"].astype(int).unique()))\n", "frontend_facility = tuple(\n", "    filter(None, base_bf_name[\"frontend_facility_id\"].astype(int).unique())\n", ")\n", "backend_facility = tuple(\n", "    filter(None, base_bf_name[\"backend_facility_id\"].astype(int).unique())\n", ")\n", "print(\"items-\", len(item), \"ff-\", len(frontend_facility), \"bf-\", len(backend_facility))\n", "\n", "check_facility = tuple(set(frontend_facility + backend_facility))\n", "\n", "eligible_facilities_query = f\"\"\"\n", "select distinct facility_id as frontend_facility_id from\n", "    (select o.facility_id, count(distinct order_id) as order_count\n", "    from lake_ims.ims_order_details a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where date(a.created_at) between current_date-interval '6 days' and current_date-interval '1 days'\n", "    and o.facility_id in {check_facility}\n", "    and a.status_id <> 5\n", "    and business_type not ilike '%%b2b%%'\n", "    group by 1) b\n", "where order_count > 1\n", "\n", "\"\"\"\n", "\n", "eligible_facilities = read_sql_query(eligible_facilities_query, redshift)\n", "\n", "base_elg = pd.merge(\n", "    base_bf_name, eligible_facilities, on=[\"frontend_facility_id\"], how=\"inner\"\n", ")\n", "\n", "item = tuple(list(base_elg[\"item_id\"].astype(int).unique()))\n", "fe_f = tuple(list(base_elg[\"frontend_facility_id\"].astype(int).unique()))\n", "be_f = tuple(list(base_elg[\"backend_facility_id\"].astype(int).unique()))\n", "print(\"items-\", len(item), \"ff-\", len(fe_f), \"bf-\", len(be_f))\n", "\n", "print(base_tea.shape, base_bf_name.shape, base_elg.shape)\n", "\n", "\n", "base_elg = base_elg[\n", "    [\n", "        \"state\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility_name\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"active\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "    ]\n", "].rename(columns={\"l0\": \"L0\", \"l1\": \"L1\", \"l2\": \"L2\"})\n", "base_elg.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_elg.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### City - DS Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_ds_sql = f\"\"\"\n", "select distinct co.facility_id,cl.name as city\n", "from lake_retail.console_outlet co\n", "inner join (select facility_id, max(updated_at) as updated_at\n", "            from lake_retail.console_outlet\n", "            group by 1) cc on cc.facility_id=co.facility_id  and cc.updated_at=co.updated_at\n", "left join lake_retail.console_location cl on cl.id=co.tax_location_id\n", "where co.facility_id in {fe_f}\n", "\"\"\"\n", "\n", "city_ds = read_sql_query(city_ds_sql, redshift)\n", "city_ds = (\n", "    city_ds[[\"facility_id\", \"city\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "base_city = pd.merge(\n", "    base_elg,\n", "    city_ds.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=[\"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "base_city.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Facility-Outlet Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_out_sql = f\"\"\"\n", "select distinct o.facility_id,o.id as outlet_id\n", "        from lake_retail.console_outlet o\n", "        inner join lake_po.physical_facility_outlet_mapping om on om.outlet_id=o.id and om.ars_active=1\n", "        where o.active=1\n", "        and o.facility_id in {fe_f}\n", "        \n", "\"\"\"\n", "\n", "fac_out = read_sql_query(fac_out_sql, redshift)\n", "fac_out_c = (\n", "    fac_out.groupby([\"facility_id\"])\n", "    .agg({\"outlet_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"outlet_id\": \"outlet_count\"})\n", ")\n", "\n", "if fac_out_c[fac_out_c.outlet_count > 1].shape[0] > 0:\n", "    print(\"Mutiple Mapping\")\n", "else:\n", "    print(\"Correct Mapping\")\n", "outlet = tuple(list(fac_out[\"outlet_id\"].astype(int).unique()))\n", "len(outlet)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(fe_f), len(outlet), fac_out.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_sql = f\"\"\"select outlet_id,item_id, sum(current_stock) current_stock,max(is_live) is_live\n", "from\n", "(SELECT\n", "    iii.outlet_id, \n", "    iii.item_id,\n", "    iii.quantity - COALESCE(sum(case when iibi.blocked_type in (1,2,4) then iibi.quantity else 0 end),0) as current_stock,\n", "    case when iii.quantity - COALESCE(sum(case when iibi.blocked_type in (1,2,4) then iibi.quantity else 0 end),0) > 0 then 1 else 0 end as is_live\n", "FROM\n", "(select item_id, outlet_id, quantity\n", "from ims.ims_item_inventory\n", "where item_id in {item}\n", "and outlet_id in {outlet}\n", "and active = 1\n", "AND outlet_id not in (1638,1449,1405)\n", ") iii\n", "\n", "LEFT JOIN\n", "ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id and iii.outlet_id=iibi.outlet_id\n", "\n", "GROUP BY 1,2, iii.quantity ) t \n", "group by 1,2\n", "\"\"\"\n", "\n", "inv = read_sql_query(inv_sql, rds_ims)\n", "\n", "\n", "# open_sto_data_query = f\"\"\"\n", "#     select item_id, outlet_id, sum(expected_sto_qty) as expected_sto_qty\n", "#         from(\n", "#             select convert_tz(s.created_at,'+00:00','+05:30') as issue_date, s.sto_id, item_id,\n", "#                     s.merchant_outlet_id as outlet_id, sto_state, sum(expected_quantity) as expected_sto_qty, sum(inward_quantity) as grn_qty\n", "#             from ims.ims_sto_details s\n", "#             left join ims.ims_sto_item si on s.sto_id = si.sto_id\n", "\n", "#             where sto_state not in (3,4)\n", "#             and s.merchant_outlet_id in {outlet}\n", "#             and item_id in {item}\n", "#             group by 1,2,3,4,5\n", "#             ) a\n", "#         where grn_qty = 0\n", "#         group by 1,2\n", "#     \"\"\"\n", "# open_sto_data = read_sql_query(open_sto_data_query, con=rds_ims)\n", "# open_sto_data[['item_id','outlet_id','expected_sto_qty']] = open_sto_data[['item_id','outlet_id','expected_sto_qty']].astype(int)\n", "\n", "\n", "# inv = pd.merge(inv, open_sto_data, on = ['item_id','outlet_id'], how = 'left')\n", "# inv['expected_sto_qty'] = inv['expected_sto_qty'].fillna(0)\n", "# inv['c_stock'] = inv['current_stock']+inv['current_stock']\n", "\n", "inv_fac = pd.merge(inv, fac_out, on=[\"outlet_id\"], how=\"left\")\n", "# inv_fac_00 = (\n", "#     inv_fac.groupby([\"item_id\", \"facility_id\"])\n", "#     .agg({\"c_stock\": \"sum\"})\n", "#     .reset_index().rename(columns={'c_stock':'current_stock'})\n", "# )\n", "\n", "inv_fac_00 = (\n", "    inv_fac.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"current_stock\": \"sum\"})\n", "    .reset_index()\n", ")\n", "inv_fac_00[\"is_live\"] = np.where(inv_fac_00[\"current_stock\"] > 0, 1, 0)\n", "\n", "\n", "base_inv = pd.merge(\n", "    base_city,\n", "    inv_fac_00.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=[\"frontend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "print(base_inv.shape)\n", "\n", "base_inv.current_stock.fillna(0, inplace=True)\n", "base_inv.is_live.fillna(0, inplace=True)\n", "\n", "base_inv[\"c_avail\"] = np.where(\n", "    base_inv[\"active\"] == 0, 0, round(base_inv[\"is_live\"] / base_inv[\"active\"], 2)\n", ")\n", "print(base_inv.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Past Availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["past_avail_sql = f\"\"\"\n", "with hourly_availability as (\n", "select date(a.order_date) as order_date, \n", "    extract(hour from a.order_date)::int as hour, \n", "    a.item_id,\n", "    a.\"item_name \" as item_name,\n", "    a.facility_id,\n", "    actual_quantity-blocked_quantity as net_inv,\n", "    app_live\n", "from rpc_daily_availability a\n", "inner join\n", "    (select date(order_date) as date_, extract(hour from order_date), max(order_date) as order_date\n", "    from rpc_daily_availability\n", "    where date(order_date) between current_date-interval '31 days' and current_date\n", "    group by 1,2) b on a.order_date = b.order_date \n", "\n", "where date(a.order_date) between current_date-interval '31 days' and current_date\n", "and a.item_id in {item}\n", "and a.facility_id in {fe_f}\n", "),\n", "\n", "\n", "avail as (\n", "select distinct item_id, facility_id,\n", "            round(case when t_tot = 0 then 0 else t_live*1.00/t_tot end,2) as t_avail,\n", "            round(case when t_1_tot = 0 then 0 else t_1_live*1.00/t_1_tot end,2) as t_1_avail,\n", "            round(case when t_2_tot = 0 then 0 else t_2_live*1.00/t_2_tot end,2) as t_2_avail,\n", "            round(case when t_3_tot = 0 then 0 else t_3_live*1.00/t_3_tot end,2) as t_3_avail,\n", "            round(case when t_4_tot = 0 then 0 else t_4_live*1.00/t_4_tot end,2) as t_4_avail,\n", "            round(case when t_5_tot = 0 then 0 else t_5_live*1.00/t_5_tot end,2) as t_5_avail,\n", "            round(case when t_6_tot = 0 then 0 else t_6_live*1.00/t_6_tot end,2) as t_6_avail,\n", "            round(case when t_7_tot = 0 then 0 else t_7_live*1.00/t_7_tot end,2) as t_7_avail,\n", "            round(case when mtd_tot = 0 then 0 else mtd_live*1.00/mtd_tot end,2) as mtd_avail\n", "                from(\n", "                select distinct item_id, facility_id,\n", "                    count(case when order_date = current_date and (net_inv>0 or app_live=1) then hour else null end) as t_live,\n", "                    count(case when order_date = current_date then hour end) as t_tot,\n", "                    \n", "                    count(case when order_date = current_date - interval '1 days' and (net_inv>0 or app_live=1) then hour else null end) as t_1_live,\n", "                    count(case when order_date = current_date - interval '1 days' then hour end) as t_1_tot,\n", "\n", "                    count(case when order_date = current_date - interval '2 days' and (net_inv>0 or app_live=1) then hour else null end) as t_2_live,\n", "                    count(case when order_date = current_date - interval '2 days' then hour end) as t_2_tot,\n", "\n", "                    count(case when order_date = current_date - interval '3 days' and (net_inv>0 or app_live=1) then hour else null end) as t_3_live,\n", "                    count(case when order_date = current_date - interval '3 days' then hour end) as t_3_tot,\n", "\n", "                    count(case when order_date = current_date - interval '4 days' and (net_inv>0 or app_live=1) then hour else null end) as t_4_live,\n", "                    count(case when order_date = current_date - interval '4 days' then hour end) as t_4_tot,\n", "\n", "                    count(case when order_date = current_date - interval '5 days' and (net_inv>0 or app_live=1) then hour else null end) as t_5_live,\n", "                    count(case when order_date = current_date - interval '5 days' then hour end) as t_5_tot,\n", "\n", "                    count(case when order_date = current_date - interval '6 days' and (net_inv>0 or app_live=1) then hour else null end) as t_6_live,\n", "                    count(case when order_date = current_date - interval '6 days' then hour end) as t_6_tot,\n", "\n", "                    count(case when order_date = current_date - interval '7 days' and (net_inv>0 or app_live=1) then hour else null end) as t_7_live,\n", "                    count(case when order_date = current_date - interval '7 days' then hour end) as t_7_tot,\n", "                    \n", "                    count(case when order_date>=date_trunc('month', current_date) and order_date<=current_date and (net_inv>0 or app_live=1) then hour else null end) as mtd_live,\n", "                    count(case when order_date>=date_trunc('month', current_date) and order_date<=current_date then hour end) as mtd_tot\n", "\n", "                from hourly_availability\n", "                where item_name <> ''\n", "                group by 1,2\n", "                )\n", ")\n", "\n", "select * from avail\n", "\"\"\"\n", "\n", "past_avail = read_sql_query(past_avail_sql, redshift)\n", "\n", "print(\n", "    past_avail[[\"item_id\", \"facility_id\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", "    .shape,\n", "    past_avail.shape,\n", ")\n", "# past_avail"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_avail = pd.merge(\n", "    base_inv[\n", "        [\n", "            \"state\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"city\",\n", "            \"frontend_facility_id\",\n", "            \"frontend_facility_name\",\n", "            \"L0\",\n", "            \"L1\",\n", "            \"L2\",\n", "            \"brand\",\n", "            \"current_stock\",\n", "            \"is_live\",\n", "            \"active\",\n", "            \"c_avail\",\n", "        ]\n", "    ].rename(\n", "        columns={\n", "            \"frontend_facility_id\": \"facility_id\",\n", "            \"frontend_facility_name\": \"facility_name\",\n", "        }\n", "    ),\n", "    past_avail,\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "print(base_avail.shape)\n", "base_avail.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_avail.count()\n", "# base_avail.isna().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Past Availability - Critical Hours"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["critical_avail_sql = f\"\"\"\n", "with hourly_availability as (\n", "select date(a.order_date) as order_date, \n", "    extract(hour from a.order_date)::int as hour, \n", "    a.item_id,\n", "    a.\"item_name \" as item_name,\n", "    a.facility_id,\n", "    actual_quantity-blocked_quantity as net_inv,\n", "    app_live\n", "from rpc_daily_availability a\n", "inner join\n", "    (select date(order_date) as date_, extract(hour from order_date), max(order_date) as order_date\n", "    from rpc_daily_availability\n", "    where date(order_date) between current_date-interval '31 days' and current_date\n", "    group by 1,2) b on a.order_date = b.order_date \n", "\n", "where date(a.order_date) between current_date-interval '31 days' and current_date\n", "and a.item_id in {item}\n", "and a.facility_id in {fe_f}\n", "),\n", "\n", "\n", "avail as (\n", "select distinct item_id, facility_id,\n", "            round(case when t_tot = 0 then 0 else t_live*1.00/t_tot end,2) as crit_avail,\n", "            round(case when t_1_tot = 0 then 0 else t_1_live*1.00/t_1_tot end,2) as crit_1_avail,\n", "            round(case when t_2_tot = 0 then 0 else t_2_live*1.00/t_2_tot end,2) as crit_2_avail,\n", "            round(case when t_3_tot = 0 then 0 else t_3_live*1.00/t_3_tot end,2) as crit_3_avail,\n", "            round(case when t_4_tot = 0 then 0 else t_4_live*1.00/t_4_tot end,2) as crit_4_avail,\n", "            round(case when t_5_tot = 0 then 0 else t_5_live*1.00/t_5_tot end,2) as crit_5_avail,\n", "            round(case when t_6_tot = 0 then 0 else t_6_live*1.00/t_6_tot end,2) as crit_6_avail,\n", "            round(case when t_7_tot = 0 then 0 else t_7_live*1.00/t_7_tot end,2) as crit_7_avail,\n", "            round(case when mtd_tot = 0 then 0 else mtd_live*1.00/mtd_tot end,2) as crit_mtd_avail\n", "                from(\n", "                select distinct item_id, facility_id,\n", "                    count(case when order_date = current_date and (net_inv>0 or app_live=1) then hour else null end) as t_live,\n", "                    count(case when order_date = current_date then hour end) as t_tot,\n", "                    \n", "                    count(case when order_date = current_date - interval '1 days' and (net_inv>0 or app_live=1) then hour else null end) as t_1_live,\n", "                    count(case when order_date = current_date - interval '1 days' then hour end) as t_1_tot,\n", "\n", "                    count(case when order_date = current_date - interval '2 days' and (net_inv>0 or app_live=1) then hour else null end) as t_2_live,\n", "                    count(case when order_date = current_date - interval '2 days' then hour end) as t_2_tot,\n", "\n", "                    count(case when order_date = current_date - interval '3 days' and (net_inv>0 or app_live=1) then hour else null end) as t_3_live,\n", "                    count(case when order_date = current_date - interval '3 days' then hour end) as t_3_tot,\n", "\n", "                    count(case when order_date = current_date - interval '4 days' and (net_inv>0 or app_live=1) then hour else null end) as t_4_live,\n", "                    count(case when order_date = current_date - interval '4 days' then hour end) as t_4_tot,\n", "\n", "                    count(case when order_date = current_date - interval '5 days' and (net_inv>0 or app_live=1) then hour else null end) as t_5_live,\n", "                    count(case when order_date = current_date - interval '5 days' then hour end) as t_5_tot,\n", "\n", "                    count(case when order_date = current_date - interval '6 days' and (net_inv>0 or app_live=1) then hour else null end) as t_6_live,\n", "                    count(case when order_date = current_date - interval '6 days' then hour end) as t_6_tot,\n", "\n", "                    count(case when order_date = current_date - interval '7 days' and (net_inv>0 or app_live=1) then hour else null end) as t_7_live,\n", "                    count(case when order_date = current_date - interval '7 days' then hour end) as t_7_tot,\n", "                    \n", "                    count(case when order_date>=date_trunc('month', current_date) and order_date<=current_date and (net_inv>0 or app_live=1) then hour else null end) as mtd_live,\n", "                    count(case when order_date>=date_trunc('month', current_date) and order_date<=current_date then hour end) as mtd_tot\n", "\n", "                from hourly_availability\n", "                where item_name <> ''\n", "                and hour between 17 and 22\n", "                group by 1,2\n", "                )\n", ")\n", "\n", "select * from avail\n", "\"\"\"\n", "\n", "critical_avail = read_sql_query(critical_avail_sql, redshift)\n", "print(\n", "    critical_avail[[\"item_id\", \"facility_id\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", "    .shape,\n", "    critical_avail.shape,\n", ")\n", "# critical_avail.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_crit_avail = pd.merge(\n", "    base_avail, critical_avail, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "print(base_crit_avail.shape)\n", "base_crit_avail.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_crit_avail.count()\n", "# base_crit_avail.isna().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Past Sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["past_sales_sql = f\"\"\"\n", "with mer_outlet_mapping as (select frontend_merchant_id, frontend_merchant_name,\n", "backend_merchant_id, backend_merchant_name,\n", "pos_outlet_id as outlet_id, pos_outlet_name as store,\n", "facility_id\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "and facility_id in {fe_f}\n", "),\n", "\n", "sales as (\n", "SELECT mom.facility_id,\n", "    ipm.item_id,\n", "    date(o.install_ts + interval '5.5 Hours') as order_date,\n", "    sum(quantity-cancelled_quantity) as qty_sold,\n", "    sum((quantity-cancelled_quantity)*selling_price) as gmv    \n", "FROM lake_oms_bifrost.oms_order o\n", "INNER JOIN lake_oms_bifrost.oms_merchant M ON o.MERCHANT_ID = M.ID\n", "INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "LEFT JOIN lake_rpc.item_product_mapping ipm ON oi.product_id = ipm.product_id AND ipm.active=1\n", "INNER JOIN mer_outlet_mapping mom on m.external_id = mom.frontend_merchant_id\n", "\n", "WHERE date(o.install_ts + interval '5.5 Hours') between current_date - interval '31 days' and current_date\n", "AND ipm.item_id in {item}\n", "AND o.current_Status <> 'CANCELLED'\n", "AND (\"type\" IS NULL OR \"type\" IN ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder'))\n", "GROUP BY 1,2,3\n", "ORDER BY 1,2,3\n", ")\n", "\n", "\n", "\n", "select item_id, facility_id,\n", "        coalesce(sum(case when order_date = current_date then qty_sold else null end),0) as t_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '1 days' then qty_sold else null end),0) as t_1_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '2 days' then qty_sold else null end),0) as t_2_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '3 days' then qty_sold else null end),0) as t_3_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '4 days' then qty_sold else null end),0) as t_4_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '5 days' then qty_sold else null end),0) as t_5_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '6 days' then qty_sold else null end),0) as t_6_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '7 days' then qty_sold else null end),0) as t_7_sale,\n", "        coalesce(sum(case when order_date >= date_trunc('month', current_date) and order_date <= current_date then qty_sold else null end),0) as mtd_sale\n", "    \n", "    from sales\n", "    group by 1,2\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "past_sales = read_sql_query(past_sales_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_sales = pd.merge(\n", "    base_crit_avail, past_sales, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "base_sales.t_sale.fillna(0, inplace=True)\n", "base_sales.t_1_sale.fillna(0, inplace=True)\n", "base_sales.t_2_sale.fillna(0, inplace=True)\n", "base_sales.t_3_sale.fillna(0, inplace=True)\n", "base_sales.t_4_sale.fillna(0, inplace=True)\n", "base_sales.t_5_sale.fillna(0, inplace=True)\n", "base_sales.t_6_sale.fillna(0, inplace=True)\n", "base_sales.t_7_sale.fillna(0, inplace=True)\n", "base_sales.mtd_sale.fillna(0, inplace=True)\n", "\n", "print(base_sales.shape)\n", "base_sales.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_sales.isna().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Past Critical Sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["past_crit_sales_sql = f\"\"\"\n", "with mer_outlet_mapping as (select frontend_merchant_id, frontend_merchant_name,\n", "backend_merchant_id, backend_merchant_name,\n", "pos_outlet_id as outlet_id, pos_outlet_name as store,\n", "facility_id\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "and facility_id in {fe_f}\n", "),\n", "\n", "sales as (\n", "SELECT mom.facility_id,\n", "    ipm.item_id,\n", "    date(o.install_ts + interval '5.5 Hours') as order_date,\n", "    sum(quantity-cancelled_quantity) as qty_sold,\n", "    sum((quantity-cancelled_quantity)*selling_price) as gmv    \n", "FROM lake_oms_bifrost.oms_order o\n", "INNER JOIN lake_oms_bifrost.oms_merchant M ON o.MERCHANT_ID = M.ID\n", "INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "LEFT JOIN lake_rpc.item_product_mapping ipm ON oi.product_id = ipm.product_id AND ipm.active=1\n", "INNER JOIN mer_outlet_mapping mom on m.external_id = mom.frontend_merchant_id\n", "\n", "WHERE date(o.install_ts + interval '5.5 Hours') between current_date - interval '31 days' and current_date\n", "AND extract(hour from o.install_ts + interval '5.5 Hours') between 17 and 22\n", "AND ipm.item_id in {item}\n", "AND o.current_Status <> 'CANCELLED'\n", "AND (\"type\" IS NULL OR \"type\" IN ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder'))\n", "GROUP BY 1,2,3\n", "ORDER BY 1,2,3\n", ")\n", "\n", "\n", "\n", "select item_id, facility_id,\n", "        coalesce(sum(case when order_date = current_date then qty_sold else null end),0) as crit_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '1 days' then qty_sold else null end),0) as crit_1_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '2 days' then qty_sold else null end),0) as crit_2_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '3 days' then qty_sold else null end),0) as crit_3_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '4 days' then qty_sold else null end),0) as crit_4_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '5 days' then qty_sold else null end),0) as crit_5_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '6 days' then qty_sold else null end),0) as crit_6_sale,\n", "        coalesce(sum(case when order_date = current_date  - interval '7 days' then qty_sold else null end),0) as crit_7_sale,\n", "        coalesce(sum(case when order_date >= date_trunc('month', current_date) and order_date <= current_date then qty_sold else null end),0) as crit_mtd_sale\n", "    \n", "    from sales\n", "    group by 1,2\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "past_crit_sales = read_sql_query(past_crit_sales_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_crit_sales = pd.merge(\n", "    base_sales, past_crit_sales, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "base_crit_sales.crit_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_1_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_2_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_3_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_4_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_5_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_6_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_7_sale.fillna(0, inplace=True)\n", "base_crit_sales.crit_mtd_sale.fillna(0, inplace=True)\n", "\n", "print(base_crit_sales.shape)\n", "base_crit_sales.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_crit_sales.isna().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### City Aggregate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_ds_00 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\", \"L2\", \"brand\"])\n", "    .agg({\"facility_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"facility_id\": \"ds_count\"})\n", ")\n", "\n", "\n", "city_ds_01 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\", \"L2\"])\n", "    .agg({\"facility_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"facility_id\": \"ds_count\"})\n", ")\n", "city_ds_01[\"brand\"] = \"All\"\n", "city_ds_01 = city_ds_01[[\"state\", \"city\", \"L0\", \"L2\", \"brand\", \"ds_count\"]]\n", "\n", "\n", "city_ds_02 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\"])\n", "    .agg({\"facility_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"facility_id\": \"ds_count\"})\n", ")\n", "city_ds_02[\"brand\"] = \"All\"\n", "city_ds_02[\"L2\"] = \"All\"\n", "city_ds_02 = city_ds_02[[\"state\", \"city\", \"L0\", \"L2\", \"brand\", \"ds_count\"]]\n", "\n", "\n", "city_ds_03 = (\n", "    base_crit_sales.groupby([\"state\", \"city\"])\n", "    .agg({\"facility_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"facility_id\": \"ds_count\"})\n", ")\n", "city_ds_03[\"brand\"] = \"All\"\n", "city_ds_03[\"L2\"] = \"All\"\n", "city_ds_03[\"L0\"] = \"All\"\n", "city_ds_03 = city_ds_03[[\"state\", \"city\", \"L0\", \"L2\", \"brand\", \"ds_count\"]]\n", "\n", "\n", "city_ds_04 = (\n", "    pd.concat(\n", "        [\n", "            city_ds_03,\n", "            city_ds_02,\n", "            city_ds_01,\n", "            city_ds_00,\n", "        ]\n", "    )\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "city_ds_05 = (\n", "    city_ds_04.groupby([\"state\", \"L0\", \"L2\", \"brand\"])\n", "    .agg({\"ds_count\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"ds_count\": \"total_ds\"})\n", ")\n", "city_ds_06 = pd.merge(\n", "    city_ds_04, city_ds_05, on=[\"state\", \"L0\", \"L2\", \"brand\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_sku_00 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\", \"L2\", \"brand\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "\n", "\n", "city_sku_01 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\", \"L2\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "city_sku_01[\"brand\"] = \"All\"\n", "city_sku_01 = city_sku_01[[\"state\", \"city\", \"L0\", \"L2\", \"brand\", \"sku_count\"]]\n", "\n", "\n", "city_sku_02 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "city_sku_02[\"brand\"] = \"All\"\n", "city_sku_02[\"L2\"] = \"All\"\n", "city_sku_02 = city_sku_02[[\"state\", \"city\", \"L0\", \"L2\", \"brand\", \"sku_count\"]]\n", "\n", "\n", "city_sku_03 = (\n", "    base_crit_sales.groupby([\"state\", \"city\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "city_sku_03[\"brand\"] = \"All\"\n", "city_sku_03[\"L2\"] = \"All\"\n", "city_sku_03[\"L0\"] = \"All\"\n", "city_sku_03 = city_sku_03[[\"state\", \"city\", \"L0\", \"L2\", \"brand\", \"sku_count\"]]\n", "\n", "city_sku_04 = (\n", "    pd.concat(\n", "        [\n", "            city_sku_03,\n", "            city_sku_02,\n", "            city_sku_01,\n", "            city_sku_00,\n", "        ]\n", "    )\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "\n", "pan_india_sku_00 = (\n", "    base_crit_sales.groupby([\"state\", \"L0\", \"L2\", \"brand\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "\n", "\n", "pan_india_sku_01 = (\n", "    base_crit_sales.groupby([\"state\", \"L0\", \"L2\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "pan_india_sku_01[\"brand\"] = \"All\"\n", "pan_india_sku_01 = pan_india_sku_01[[\"state\", \"L0\", \"L2\", \"brand\", \"sku_count\"]]\n", "\n", "\n", "pan_india_sku_02 = (\n", "    base_crit_sales.groupby([\"state\", \"L0\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "pan_india_sku_02[\"brand\"] = \"All\"\n", "pan_india_sku_02[\"L2\"] = \"All\"\n", "pan_india_sku_02 = pan_india_sku_02[[\"state\", \"L0\", \"L2\", \"brand\", \"sku_count\"]]\n", "\n", "\n", "pan_india_sku_03 = (\n", "    base_crit_sales.groupby([\"state\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"sku_count\"})\n", ")\n", "pan_india_sku_03[\"brand\"] = \"All\"\n", "pan_india_sku_03[\"L2\"] = \"All\"\n", "pan_india_sku_03[\"L0\"] = \"All\"\n", "pan_india_sku_03 = pan_india_sku_03[[\"state\", \"L0\", \"L2\", \"brand\", \"sku_count\"]]\n", "\n", "\n", "pan_india_sku_04 = (\n", "    pd.concat(\n", "        [\n", "            pan_india_sku_03,\n", "            pan_india_sku_02,\n", "            pan_india_sku_01,\n", "            pan_india_sku_00,\n", "        ]\n", "    )\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "pan_india_sku_04[\"city\"] = \"PAN India\"\n", "pan_india_sku_04 = pan_india_sku_04[[\"state\", \"city\", \"L0\", \"L2\", \"brand\", \"sku_count\"]]\n", "\n", "city_sku_05 = (\n", "    pd.concat([pan_india_sku_04, city_sku_04]).reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "city_sku_05.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# city_ds_sku.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_avail_00 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\", \"L2\", \"brand\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "city_avail_01 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\", \"L2\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "city_avail_01[\"brand\"] = \"All\"\n", "city_avail_01 = city_avail_01[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "    ]\n", "]\n", "\n", "city_avail_02 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"L0\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "city_avail_02[\"L2\"] = \"All\"\n", "city_avail_02[\"brand\"] = \"All\"\n", "city_avail_02 = city_avail_02[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "    ]\n", "]\n", "\n", "city_avail_03 = (\n", "    base_crit_sales.groupby([\"state\", \"city\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "city_avail_03[\"L0\"] = \"All\"\n", "city_avail_03[\"L2\"] = \"All\"\n", "city_avail_03[\"brand\"] = \"All\"\n", "city_avail_03 = city_avail_03[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "    ]\n", "]\n", "\n", "\n", "city_avail_04 = pd.concat(\n", "    [city_avail_03, city_avail_02, city_avail_01, city_avail_00], sort=False\n", ")\n", "\n", "print(city_avail_04.shape)\n", "city_avail_04.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# city_avail_04.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_crit_sales[(base_crit_sales.city=='Ahmedabad')&(base_crit_sales.L0=='breakfast & instant food')&(base_crit_sales.L2=='cup noodles')&(base_crit_sales.brand=='Maggi')]\n", "# .agg({'mtd_sale':'sum'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_avail_00 = pd.merge(\n", "    city_avail_04, city_ds_06, on=[\"state\", \"city\", \"L0\", \"L2\", \"brand\"], how=\"left\"\n", ")\n", "\n", "pan_india_avail_00[\"c_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"c_avail\"]\n", "pan_india_avail_00[\"t_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_avail\"]\n", "pan_india_avail_00[\"t_1_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_1_avail\"]\n", "pan_india_avail_00[\"t_2_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_2_avail\"]\n", "pan_india_avail_00[\"t_3_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_3_avail\"]\n", "pan_india_avail_00[\"t_4_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_4_avail\"]\n", "pan_india_avail_00[\"t_5_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_5_avail\"]\n", "pan_india_avail_00[\"t_6_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_6_avail\"]\n", "pan_india_avail_00[\"t_7_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"t_7_avail\"]\n", "pan_india_avail_00[\"mtd_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"mtd_avail\"]\n", "\n", "pan_india_avail_00[\"crit_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_avail\"]\n", "pan_india_avail_00[\"crit_1_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_1_avail\"]\n", "pan_india_avail_00[\"crit_2_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_2_avail\"]\n", "pan_india_avail_00[\"crit_3_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_3_avail\"]\n", "pan_india_avail_00[\"crit_4_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_4_avail\"]\n", "pan_india_avail_00[\"crit_5_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_5_avail\"]\n", "pan_india_avail_00[\"crit_6_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_6_avail\"]\n", "pan_india_avail_00[\"crit_7_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_7_avail\"]\n", "pan_india_avail_00[\"crit_mtd_avail\"] = (\n", "    pan_india_avail_00[\"ds_count\"] / pan_india_avail_00[\"total_ds\"]\n", ") * pan_india_avail_00[\"crit_mtd_avail\"]\n", "\n", "\n", "pan_india_avail_00[\"pan_india\"] = \"PAN India\"\n", "\n", "pan_india_avail_01 = (\n", "    pan_india_avail_00.groupby([\"state\", \"pan_india\", \"L0\", \"L2\", \"brand\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"sum\",\n", "            \"t_avail\": \"sum\",\n", "            \"t_1_avail\": \"sum\",\n", "            \"t_2_avail\": \"sum\",\n", "            \"t_3_avail\": \"sum\",\n", "            \"t_4_avail\": \"sum\",\n", "            \"t_5_avail\": \"sum\",\n", "            \"t_6_avail\": \"sum\",\n", "            \"t_7_avail\": \"sum\",\n", "            \"mtd_avail\": \"sum\",\n", "            \"crit_avail\": \"sum\",\n", "            \"crit_1_avail\": \"sum\",\n", "            \"crit_2_avail\": \"sum\",\n", "            \"crit_3_avail\": \"sum\",\n", "            \"crit_4_avail\": \"sum\",\n", "            \"crit_5_avail\": \"sum\",\n", "            \"crit_6_avail\": \"sum\",\n", "            \"crit_7_avail\": \"sum\",\n", "            \"crit_mtd_avail\": \"sum\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "pan_india_avail_01 = pan_india_avail_01.rename(columns={\"pan_india\": \"city\"})\n", "\n", "print(pan_india_avail_01.shape)\n", "# pan_india_avail_01.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_avail_01.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_avail_05 = (\n", "    pd.concat([pan_india_avail_01, city_avail_04], sort=False)\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "city_avail_05 = pd.merge(\n", "    city_avail_05, city_sku_05, on=[\"state\", \"city\", \"L0\", \"L2\", \"brand\"], how=\"left\"\n", ")\n", "print(city_avail_05.shape)\n", "city_avail_05.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# city_avail_05.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1G4MmzMNk_JnCqU-rEa7HFZBhYZ7E313tkEAjPTD6uKc\"\n", "sheet_name = \"state_mapping\"\n", "state_mapping = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state_mapping[\"ID\"] = state_mapping[\"ID\"].astype(int)\n", "state_mapping = state_mapping.rename(columns={\"ID\": \"state\"})\n", "# state_mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# city_avail_05.state.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_avail_05 = pd.merge(city_avail_05, state_mapping, on=[\"state\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_avail_05 = city_avail_05[\n", "    [\n", "        \"city\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "        \"Name\",\n", "        \"sku_count\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# city_avail_05.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### make changes in the sheet\n", "pb.to_sheets(\n", "    city_avail_05, \"1G4MmzMNk_JnCqU-rEa7HFZBhYZ7E313tkEAjPTD6uKc\", \"city_raw_final\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Facility Aggregate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_crit_sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_avail_00 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\", \"L0\", \"L2\", \"brand\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "fac_avail_01 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\", \"L0\", \"L2\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "fac_avail_01[\"brand\"] = \"All\"\n", "fac_avail_01 = fac_avail_01[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "    ]\n", "]\n", "\n", "\n", "fac_avail_02 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\", \"L0\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "fac_avail_02[\"L2\"] = \"All\"\n", "fac_avail_02[\"brand\"] = \"All\"\n", "fac_avail_02 = fac_avail_02[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "    ]\n", "]\n", "\n", "\n", "fac_avail_03 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\"])\n", "    .agg(\n", "        {\n", "            \"c_avail\": \"mean\",\n", "            \"t_avail\": \"mean\",\n", "            \"t_1_avail\": \"mean\",\n", "            \"t_2_avail\": \"mean\",\n", "            \"t_3_avail\": \"mean\",\n", "            \"t_4_avail\": \"mean\",\n", "            \"t_5_avail\": \"mean\",\n", "            \"t_6_avail\": \"mean\",\n", "            \"t_7_avail\": \"mean\",\n", "            \"mtd_avail\": \"mean\",\n", "            \"crit_avail\": \"mean\",\n", "            \"crit_1_avail\": \"mean\",\n", "            \"crit_2_avail\": \"mean\",\n", "            \"crit_3_avail\": \"mean\",\n", "            \"crit_4_avail\": \"mean\",\n", "            \"crit_5_avail\": \"mean\",\n", "            \"crit_6_avail\": \"mean\",\n", "            \"crit_7_avail\": \"mean\",\n", "            \"crit_mtd_avail\": \"mean\",\n", "            \"t_sale\": \"sum\",\n", "            \"t_1_sale\": \"sum\",\n", "            \"t_2_sale\": \"sum\",\n", "            \"t_3_sale\": \"sum\",\n", "            \"t_4_sale\": \"sum\",\n", "            \"t_5_sale\": \"sum\",\n", "            \"t_6_sale\": \"sum\",\n", "            \"t_7_sale\": \"sum\",\n", "            \"mtd_sale\": \"sum\",\n", "            \"crit_sale\": \"sum\",\n", "            \"crit_1_sale\": \"sum\",\n", "            \"crit_2_sale\": \"sum\",\n", "            \"crit_3_sale\": \"sum\",\n", "            \"crit_4_sale\": \"sum\",\n", "            \"crit_5_sale\": \"sum\",\n", "            \"crit_6_sale\": \"sum\",\n", "            \"crit_7_sale\": \"sum\",\n", "            \"crit_mtd_sale\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "fac_avail_03[\"L0\"] = \"All\"\n", "fac_avail_03[\"L2\"] = \"All\"\n", "fac_avail_03[\"brand\"] = \"All\"\n", "fac_avail_03 = fac_avail_03[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "    ]\n", "]\n", "\n", "\n", "fac_avail_04 = pd.concat(\n", "    [fac_avail_03, fac_avail_02, fac_avail_01, fac_avail_00], sort=False\n", ")\n", "\n", "print(fac_avail_04.shape)\n", "fac_avail_04.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_avail_07 = (\n", "    base_crit_sales[base_crit_sales.is_live == 1].reset_index().drop(columns={\"index\"})\n", ")\n", "fac_avail_08 = (\n", "    fac_avail_07.groupby([\"state\", \"city\", \"facility_name\", \"L0\", \"L2\", \"brand\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"live_count\"})\n", ")\n", "fac_avail_09 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\", \"L0\", \"L2\", \"brand\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"tot_count\"})\n", ")\n", "fac_avail_10 = pd.merge(\n", "    fac_avail_09,\n", "    fac_avail_08,\n", "    on=[\"state\", \"city\", \"facility_name\", \"L0\", \"L2\", \"brand\"],\n", "    how=\"left\",\n", ")\n", "fac_avail_10.live_count.fillna(0, inplace=True)\n", "fac_avail_10 = fac_avail_10[\n", "    [\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"live_count\",\n", "        \"tot_count\",\n", "    ]\n", "]\n", "\n", "fac_avail_11 = (\n", "    fac_avail_07.groupby([\"state\", \"city\", \"facility_name\", \"L0\", \"L2\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"live_count\"})\n", ")\n", "fac_avail_12 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\", \"L0\", \"L2\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"tot_count\"})\n", ")\n", "fac_avail_13 = pd.merge(\n", "    fac_avail_12,\n", "    fac_avail_11,\n", "    on=[\"state\", \"city\", \"facility_name\", \"L0\", \"L2\"],\n", "    how=\"left\",\n", ")\n", "fac_avail_13.live_count.fillna(0, inplace=True)\n", "fac_avail_13[\"brand\"] = \"All\"\n", "fac_avail_13 = fac_avail_13[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"live_count\",\n", "        \"tot_count\",\n", "    ]\n", "]\n", "\n", "fac_avail_14 = (\n", "    fac_avail_07.groupby([\"state\", \"city\", \"facility_name\", \"L0\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"live_count\"})\n", ")\n", "fac_avail_15 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\", \"L0\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"tot_count\"})\n", ")\n", "fac_avail_16 = pd.merge(\n", "    fac_avail_15, fac_avail_14, on=[\"state\", \"city\", \"facility_name\", \"L0\"], how=\"left\"\n", ")\n", "fac_avail_16.live_count.fillna(0, inplace=True)\n", "fac_avail_16[\"L2\"] = \"All\"\n", "fac_avail_16[\"brand\"] = \"All\"\n", "fac_avail_16 = fac_avail_16[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"live_count\",\n", "        \"tot_count\",\n", "    ]\n", "]\n", "\n", "fac_avail_17 = (\n", "    fac_avail_07.groupby([\"state\", \"city\", \"facility_name\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"live_count\"})\n", ")\n", "fac_avail_18 = (\n", "    base_crit_sales.groupby([\"state\", \"city\", \"facility_name\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"tot_count\"})\n", ")\n", "fac_avail_19 = pd.merge(\n", "    fac_avail_18, fac_avail_17, on=[\"state\", \"city\", \"facility_name\"], how=\"left\"\n", ")\n", "fac_avail_19.live_count.fillna(0, inplace=True)\n", "fac_avail_19[\"L0\"] = \"All\"\n", "fac_avail_19[\"L2\"] = \"All\"\n", "fac_avail_19[\"brand\"] = \"All\"\n", "fac_avail_19 = fac_avail_19[\n", "    [\n", "        \"state\",\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"live_count\",\n", "        \"tot_count\",\n", "    ]\n", "]\n", "\n", "fac_avail_20 = pd.concat(\n", "    [fac_avail_19, fac_avail_16, fac_avail_13, fac_avail_10], sort=False\n", ")\n", "\n", "fac_avail_21 = pd.merge(\n", "    fac_avail_04,\n", "    fac_avail_20,\n", "    on=[\"state\", \"city\", \"facility_name\", \"L0\", \"L2\", \"brand\"],\n", "    how=\"left\",\n", ")\n", "\n", "fac_avail_21.live_count.fillna(0, inplace=True)\n", "fac_avail_21.tot_count.fillna(0, inplace=True)\n", "\n", "print(fac_avail_21.shape)\n", "fac_avail_21.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fac_avail_21.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_avail_21 = fac_avail_21[\n", "    [\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "        \"live_count\",\n", "        \"tot_count\",\n", "        \"state\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fac_avail_21.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_avail_21 = pd.merge(fac_avail_21, state_mapping, on=[\"state\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fac_avail_21 = fac_avail_21[\n", "    [\n", "        \"city\",\n", "        \"facility_name\",\n", "        \"L0\",\n", "        \"L2\",\n", "        \"brand\",\n", "        \"c_avail\",\n", "        \"t_avail\",\n", "        \"t_1_avail\",\n", "        \"t_2_avail\",\n", "        \"t_3_avail\",\n", "        \"t_4_avail\",\n", "        \"t_5_avail\",\n", "        \"t_6_avail\",\n", "        \"t_7_avail\",\n", "        \"mtd_avail\",\n", "        \"crit_avail\",\n", "        \"crit_1_avail\",\n", "        \"crit_2_avail\",\n", "        \"crit_3_avail\",\n", "        \"crit_4_avail\",\n", "        \"crit_5_avail\",\n", "        \"crit_6_avail\",\n", "        \"crit_7_avail\",\n", "        \"crit_mtd_avail\",\n", "        \"t_sale\",\n", "        \"t_1_sale\",\n", "        \"t_2_sale\",\n", "        \"t_3_sale\",\n", "        \"t_4_sale\",\n", "        \"t_5_sale\",\n", "        \"t_6_sale\",\n", "        \"t_7_sale\",\n", "        \"mtd_sale\",\n", "        \"crit_sale\",\n", "        \"crit_1_sale\",\n", "        \"crit_2_sale\",\n", "        \"crit_3_sale\",\n", "        \"crit_4_sale\",\n", "        \"crit_5_sale\",\n", "        \"crit_6_sale\",\n", "        \"crit_7_sale\",\n", "        \"crit_mtd_sale\",\n", "        \"live_count\",\n", "        \"tot_count\",\n", "        \"Name\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### make changes in the sheet\n", "# pb.to_sheets(fac_avail_21,\"1W1ODEiKa7TYoUkUG4sGL2Ms3AmaaB89dGixFZM-yTgY\",\"ds_raw\")\n", "pb.to_sheets(\n", "    fac_avail_21, \"1G4MmzMNk_JnCqU-rEa7HFZBhYZ7E313tkEAjPTD6uKc\", \"ds_raw_final\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_time = time.time()\n", "if (end_time - start_time) > 60:\n", "    print(\"Time: \", (end_time - start_time) / 60, \"min\")\n", "else:\n", "    print(\"Time: \", end_time - start_time, \"s\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}