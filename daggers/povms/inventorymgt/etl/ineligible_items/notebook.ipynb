{"cells": [{"cell_type": "code", "execution_count": null, "id": "66f25ca1-c02b-488c-be82-e2db79b52637", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "\n", "# import seaborn as sns\n", "import datetime as dt\n", "import time\n", "from calendar import monthrange\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "19941b87-a9cd-4234-8e85-b2faee232b46", "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "7800549e-5c11-4be9-a718-df85101970b4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bb591a9b-1b3f-4e57-96ab-412012810a65", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "343d1b72-89a8-4905-9486-3a4ad15ae860", "metadata": {}, "outputs": [], "source": ["final = f\"\"\"\n", "with\n", "\n", "pre_sales_del as\n", "(\n", "select created_at, order_id,outlet, business_type from lake_ims.ims_order_details\n", "where created_at > current_date-2\n", "and status_id <> 5\n", "),\n", "\n", "sales_del as\n", "(select  facility_id, count(distinct order_id) as order_count\n", "    from pre_sales_del a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where date(a.created_at) = current_date-1\n", "    --and business_type not ilike '%%b2b%%'\n", "    and o.business_type_id in (7)\n", "    and facility_id not in (29,139,26)\n", "    group by 1\n", "    having count(distinct order_id) > 5\n", "),\n", "\n", "tea_map as\n", "(\n", "select\n", "a.item_id,\n", "c.facility_id as receiver_facility_id,\n", "min(b.facility_id) as sender_facility_id\n", "from lake_rpc.item_outlet_tag_mapping a\n", "left join (select id, facility_id from lake_retail.console_outlet group by 1,2) b on a.tag_value = b.id\n", "left join (select id, facility_id from lake_retail.console_outlet group by 1,2) c on a.outlet_id = c.id\n", "where a.tag_type_id = 8 and a.active = 1\n", "group by 1,2\n", "),\n", "\n", "status as\n", "( \n", "Select\n", "a.item_id, a.facility_id, \n", "a.master_assortment_substate_id as ds_status,\n", "b.sender_facility_id,\n", "c.master_assortment_substate_id as bk_status\n", "from \n", "lake_rpc.product_facility_master_assortment a\n", "left join tea_map b on a.item_id = b.item_id and a.facility_id = b.receiver_facility_id\n", "left join lake_rpc.product_facility_master_assortment c on a.item_id = c.item_id and b.sender_facility_id = c.facility_id\n", "inner join sales_del sd on sd.facility_id = a.facility_id\n", "where a.active = 1 and sender_facility_id is not null --and c.master_assortment_substate_id =1\n", "group by 1,2,3,4,5\n", "),\n", "list_bk0 as (\n", "select sender_facility_id, item_id, count( distinct case when bk_status != 1 then facility_id end) as total_ds,\n", "count( distinct case when ds_status = 1 and bk_status != 1 then facility_id end) as total_ds_\n", "from status \n", "group by 1,2 having count( distinct case when ds_status = 1 and bk_status != 1 then facility_id end) > 0\n", "),\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "rpc_daily_availability as \n", "(\n", "select \n", "b.zone,\n", "a.facility_id,\n", "a.facility_name,\n", "a.item_id,\n", "date(order_date) as order_date,\n", "is_gb,\n", "l0,\n", "buckets,\n", "bucket_x,\n", "app_live,\n", "inv_flag, \n", "date_of_activation,\n", "assortment_date\n", "from  rpc_daily_availability  a\n", "left join zone_tbl_treatment b on a.facility_id = b.facility_id\n", "where\n", "order_date = (select max(order_date) from rpc_daily_availability)\n", "--and new_substate = 1\n", "and (date_of_activation > order_date )\n", "-- AND (((unreliable is null or unreliable = 0 or unreliable = 'NA') AND (unorderable is null or unorderable = 0 or unorderable = 'NA')) \n", "-- or coalesce(actual_quantity,0) - coalesce(blocked_quantity,0) > 0) \n", "),\n", "list_bk1 as (\n", "select rda.facility_id as sender_facility_id, rda.item_id, k.receiver_facility_id as facility_id from rpc_daily_availability rda \n", "inner join (select distinct sender_facility_id, item_id, receiver_facility_id from tea_map ) k on rda.facility_id = k.sender_facility_id \n", "and rda.item_id = k.item_id\n", "),\n", "list_12 as (\n", "\n", "select s.facility_id, s.item_id from status s \n", "inner join list_bk0 lb on s.item_id = lb.item_id and s.sender_facility_id = lb.sender_facility_id\n", "inner join sales_del sd on s.facility_id = sd.facility_id\n", "group by 1,2\n", "union all\n", "select a.facility_id, a.item_id from list_bk1 a\n", "inner join sales_del sd on a.facility_id = sd.facility_id\n", "group by 1,2\n", "\n", "),\n", "master_view as (\n", "select facility_id, item_id  from (\n", "select *, rank() over(partition by facility_id, item_id order by created_by) rk from lake_rpc.product_facility_master_assortment_log\n", "where date(created_at) not in (\n", "select max(date(created_at)) from lake_rpc.product_facility_master_assortment_log)\n", ")\n", "where rk = 1 and new_substate = 1\n", "),\n", "base as (\n", "select ma.facility_id, ma.item_id, tm.sender_facility_id, new_substate from lake_rpc.product_facility_master_assortment_log ma \n", "inner join tea_map tm on tm.receiver_facility_id = ma.facility_id and tm.item_id = ma.item_id\n", "where date(created_at) in (\n", "select max(date(created_at)) from lake_rpc.product_facility_master_assortment_log)\n", "and new_substate in ( 1,2)\n", "),\n", "base2 as (\n", "select mv.*, b.sender_facility_id from master_view mv\n", "inner join (select distinct facility_id, sender_facility_id from base) b on mv.facility_id = b.facility_id\n", "),\n", "base_view as (\n", "select *, 20 as new_substate from base2 \n", "union \n", "select * from base\n", "),\n", "total_ds_cal as (\n", "\n", "select sender_facility_id, item_id, \n", "count(distinct case when new_substate = 20 then facility_id end) AS t_1_total_ds,\n", "count(distinct case when new_substate = 1 then facility_id end) AS total_ds_add,\n", "count(distinct case when new_substate = 2 then facility_id end) AS total_ds_remove,\n", "case when count(distinct case when new_substate = 1 then facility_id end) > \n", "count(distinct case when new_substate = 2 then facility_id end) then 'Yes' else 'No' end as status\n", "from base_view \n", "group by 1,2\n", "),\n", "final_items as (\n", "select dc.sender_facility_id, dc.item_id, bv.facility_id  from total_ds_cal dc \n", "left join (select * from base_view where new_substate in (1,20)) bv on \n", "bv.item_id = dc.item_id and bv.sender_facility_id = dc.sender_facility_id\n", "where dc.status = 'Yes'\n", "),\n", "a as (\n", "\n", "WITH\n", "\n", "ven_det as\n", "(\n", "select vv.item_id, vv.facility_id , vv.vendor_id, vn.vendor_name\n", "from  lake_vms.vms_vendor_facility_alignment vv\n", "left join lake_vms.vms_vendor vn on vn.id = vv.vendor_id \n", "where vv.active = 1 \n", "group by 1,2,3,4\n", "),\n", "\n", "case_senst as\n", "(\n", "select item_id, vendor_id,facility_id, max(case_sensitivity_type) as case_sensitivity_type\n", "from lake_vms.vendor_item_physical_facility_attributes\n", "where active = 1\n", "group by 1,2,3\n", "),\n", "\n", "Vend_TAT as\n", "(\n", "select item_id, vendor_id, facility_id,  max(tat_days) as tat_days\n", "from lake_po.item_outlet_vendor_tat a\n", "left join lake_retail.console_outlet b on a.outlet_id = b.id\n", "--where b.name like '%%HOT%%'\n", "group by 1,2,3\n", "),\n", "\n", "po_cy as\n", "(\n", "SELECT vendor_id, manufacturer_id, facility_id, po_cycle_type_id,  b.identifier AS po_cycle_name, po_days\n", "from  lake_vms.vendor_manufacturer_physical_facility_attributes a\n", "LEFT join  lake_vms.po_cycle_types b ON a.po_cycle_type_id = b.id\n", "GROUP BY 1,2,3,4,5,6\n", "),\n", "\n", "load as\n", "(\n", "select facility_id, vendor_id, load_size, load_type\n", "from lake_vms.vendor_physical_facility_attributes\n", "where active = 1\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "PRODUCT_Category AS\n", "  (\n", "      SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   ),\n", "    \n", "category_pre as\n", "(\n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type,\n", "       cat.unit as UOM\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "final_item as\n", "(\n", "select \n", "a.item_id as item_id,\n", "max(a.l0) as l0,\n", "max(a.l1) as l1,\n", "max(a.l2) as l2,\n", "max(pm.name) as mfg_name,\n", "max(pb.manufacturer_id) as mfg_id\n", "from category_pre a\n", "left join lake_rpc.product_product  pp2 on pp2.item_id = a.item_id\n", "LEFT JOIN lake_rpc.product_brand pb ON pb.id = pp2.brand_id\n", "LEFT JOIN lake_rpc.product_manufacturer pm ON pm.id=pb.manufacturer_id \n", "WHERE pp2.id in ( SELECT max(id) as id from  lake_rpc.product_product  pp WHERE pp.active=1 and pp.approved=1 \n", "GROUP BY item_id)  \n", "group by 1\n", "),\n", "\n", "final_res as\n", "(\n", "select a.facility_id, cf.name as facility, a.item_id, vv.vendor_id, vendor_name, vt.tat_days as tat_days_hot, \n", "i.mfg_id, i.mfg_name, \n", "ld.load_size , ld.load_type, py.po_cycle_name, cs.case_sensitivity_type, py.po_days\n", "from lake_rpc.product_facility_master_assortment a\n", "left join ven_det vv on vv.item_id = a.item_id and vv. facility_id = a.facility_id\n", "left join Vend_TAT vt on vt.item_id = a.item_id AND VT.FACILITY_ID = a.facility_id and vv.vendor_id=  vt.vendor_id\n", "left join case_senst cs on cs.item_id = a.item_id AND cs.FACILITY_ID = a.facility_id and cs.vendor_id= vv.vendor_id\n", "left join lake_crates.facility cf on cf.id = a.facility_id \n", "left join load ld on ld.facility_id = a.facility_id and ld.vendor_id = vv.vendor_id\n", "left join final_item i on a.item_id = i.item_id \n", "left join po_cy py on py.facility_id = a.facility_id and py.vendor_id = vv.vendor_id and py.manufacturer_id = i.mfg_id\n", "--where master_assortment_substate_id = 1\n", "--and a.facility_id in (1206,92,268,15,264,554,555,1320,3,32,42,43,1,22,513,24,34,517)\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", ")\n", "\n", "select facility_id, facility, item_id, vendor_id, vendor_name, tat_days_hot, mfg_id, mfg_name,\n", "load_size, load_type, po_cycle_name, case_sensitivity_type, po_days\n", "from final_res \n", "-- where\n", "--  vendor_id is null  or  vendor_name is null or  tat_days_hot is null or mfg_id is  null or  mfg_name is null or \n", "-- load_size is null or  load_type is null or  po_cycle_name is null or  case_sensitivity_type is null or \n", "-- po_days is null\n", "),\n", "casew as (\n", " select replace(lower(po_days),',','') as t,\n", " case when t like '%%monday%%' then 1 else 20 end as \"monday\",\n", " case when t like '%%tuesday%%' then 2 else 20 end as \"tuesday\",\n", " case when t like '%%wednesday%%' then 3 else 20 end as \"wednesday\",\n", " case when t like '%%thursday%%' then 4 else 20 end as \"thursday\",\n", " case when t like '%%friday%%' then 5 else 20 end as \"friday\",\n", " case when t like '%%saturday%%' then 6 else 20 end as \"saturday\",\n", " case when t like '%%sunday%%' then 0 else 20 end as \"sunday\"\n", " from a\n", ") ,\n", "casew2 as (\n", " select t, 'monday' as day, \"monday\" as day_num from casew where \"monday\" !=20\n", " union \n", " select t, 'tuesday' as day, \"tuesday\" as day_num from casew where \"tuesday\" !=20\n", " union \n", " select t, 'wednesday' as day, \"wednesday\" as day_num from casew where \"wednesday\" !=20\n", " union \n", " select t, 'thursday' as day, \"thursday\" as day_num from casew where \"thursday\" !=20\n", " union \n", " select t, 'friday' as day, \"friday\" as day_num from casew where \"friday\" !=20\n", " union \n", " select t, 'saturday' as day, \"saturday\" as day_num from casew where \"saturday\" !=20\n", " union \n", " select t, 'sunday' as day, \"sunday\" as day_num from casew where \"sunday\" !=20\n", " \n", "),\n", "add_date as (\n", "select *, datepart(weekday, current_date) as today_day, \n", "case when today_day < day_num then day_num - today_day  \n", "when today_day >= day_num then day_num - today_day + 7 end as days_add\n", "from casew2\n", "),\n", "po_time as (\n", "select t, min(days_add) as next_po_day_count from add_date group by 1\n", "),\n", "new_a as (\n", "select a.*, replace(lower(a.po_days),',','') as t from a\n", "), \n", "final as (\n", "select n.*, pt.next_po_day_count, pt.next_po_day_count + n.tat_days_hot + 1 as days_before_activation\n", "from new_a n left join po_time pt on n.t = pt.t \n", ")\n", "\n", "select fi.*, f.days_before_activation, date(dateadd(day,f.days_before_activation, current_date)) as date_of_activation\n", "from final_items fi left join \n", "final f on f.facility_id = fi.sender_facility_id  and f.item_id = fi.item_id\n", "\n", "\n", "\"\"\"\n", "ineligible_list = read_sql_query(sql=final, con=redshift)\n", "ineligible_list.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b0248f68-9e4f-4734-9fd8-3e88bd8c8782", "metadata": {}, "outputs": [], "source": ["ineligible_list.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f642e5a6-c7ea-4663-be3d-72c00ffc8e11", "metadata": {}, "outputs": [], "source": ["df2 = ineligible_list"]}, {"cell_type": "code", "execution_count": null, "id": "e2b5bd31-176a-49ad-a0ff-f2ac5dcefb8f", "metadata": {}, "outputs": [], "source": ["df2[\"days_before_activation\"] = df2[\"days_before_activation\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "85cb5d43-1253-4f9d-9554-d58969d7e59f", "metadata": {}, "outputs": [], "source": ["df2[\"days_before_activation\"] = df2[\"days_before_activation\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "f059ce12-5349-452f-b29f-ba187142f820", "metadata": {}, "outputs": [], "source": ["df2[\"Update_at\"] = dt.datetime.now().strftime(\"%Y-%m-%d %H:%M\")\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"ineligible_item_logs\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"sender_facility_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"sender_facility_id\",\n", "        },\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "        {\n", "            \"name\": \"days_before_activation\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"days_before_activation\",\n", "        },\n", "        {\n", "            \"name\": \"date_of_activation\",\n", "            \"type\": \"varchar(1000)\",\n", "            \"description\": \"date_of_activation\",\n", "        },\n", "        {\"name\": \"Update_at\", \"type\": \"timestamp\", \"description\": \"update_timestamp\"},\n", "    ],\n", "    \"primary_key\": [\"facility_id\"],\n", "    \"sortkey\": [\"facility_id\"],\n", "    \"incremental_key\": \"facility_id\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"ineligible items logs\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(df2, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "423f3a5b-ce5a-4a1b-bc3b-18a13a2a5f95", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "22a498e6-5071-47c5-9a89-76b92781d2d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}