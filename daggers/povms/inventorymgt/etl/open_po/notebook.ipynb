{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "# retail = pb.get_connection(\"retail\").connect()\n", "import pandas as pd\n", "import boto3\n", "import logging\n", "from email.message import EmailMessage\n", "import numpy as np\n", "import datetime as dt\n", "from datetime import timedelta\n", "from botocore.exceptions import ClientError\n", "\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "ld = (dt.datetime.now() + timedelta(hours=5) + timedelta(minutes=30)).strftime(\n", "    \"%Y-%m-%d %H:%M\"\n", ")\n", "ld1 = dt.datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "\n", "def create_presigned_url(bucket_name, object_name, expiration=3600):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        logging.error(e)\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_cn = pb.get_connection(\"redshift\").connect()\n", "q1 = \"\"\"\n", "with\n", "bucket_info as (\n", "    select \n", "        item_id, \n", "        facility_id,\n", "        count(distinct case when tag_value = 'Y' then tag_value end) as tag_x,  \n", "        count(distinct case when tag_value = 'A' then tag_value end) as tag_A,\n", "        count(distinct case when tag_value = 'B' then tag_value end) as tag_b,\n", "        count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "    from ( \n", "        Select \n", "            b.item_id, \n", "            po.facility_id,\n", "            tag_value,\n", "            tag_type_id \n", "        from   lake_rpc.item_outlet_tag_mapping  b\n", "        join lake_retail.view_console_outlet po ON b.outlet_id = po.id\n", "        group by 1,2,3,4\n", "        )\n", "    Group By 1,2\n", ")\n", "    select \n", "        item_id as items_ids,\n", "        facility_id as fac_ids,\n", "        case when tag_A = 1 then 'A'\n", "             when tag_b = 1 then 'B'\n", "             else 'B' end as bucket_a_b\n", "    from bucket_info\n", "\n", "\"\"\"\n", "bucketab = pd.read_sql(q1, con=redshift_cn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_co = pb.get_connection(\"redshift\").connect()\n", "q2 = \"\"\"\n", "with\n", "bucket_info as (\n", "    select \n", "        item_id, \n", "        facility_id,\n", "        count(distinct case when tag_value = 'Y' then tag_value end) as tag_x,  \n", "        count(distinct case when tag_value = 'A' then tag_value end) as tag_A,\n", "        count(distinct case when tag_value = 'B' then tag_value end) as tag_b,\n", "        count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "    from ( \n", "        Select \n", "            b.item_id, \n", "            po.facility_id,\n", "            tag_value,\n", "            tag_type_id \n", "        from   lake_rpc.item_outlet_tag_mapping  b\n", "        join lake_retail.view_console_outlet po ON b.outlet_id = po.id\n", "        group by 1,2,3,4\n", "        )\n", "    Group By 1,2\n", ")\n", "    \n", "    select \n", "        item_id as items_id,\n", "        facility_id as fac_id,\n", "        case when tag_x = 1 then 'Y' else 'N' end as bucket_x\n", "    from bucket_info\n", "\n", "\n", "\"\"\"\n", "bucketx = pd.read_sql(q2, con=redshift_co)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_con = pb.get_connection(\"redshift\").connect()\n", "q3 = \"\"\"\n", "with\n", "PRODUCT_Category AS\n", "  (\n", "      SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   ),\n", "    \n", "category_pre as\n", "(\n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type,\n", "       cat.unit as UOM\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", ")\n", "\n", "select \n", "a.item_id as items_id,\n", "max(a.l0) as l0,\n", "max(a.l1) as l1,\n", "max(a.l2) as l2,\n", "max(pm.name) as mfg_name\n", "from category_pre a\n", "left join lake_rpc.product_product  pp2 on pp2.item_id = a.item_id\n", "LEFT JOIN lake_rpc.product_brand pb ON pb.id = pp2.brand_id\n", "LEFT JOIN lake_rpc.product_manufacturer pm ON pm.id=pb.manufacturer_id \n", "WHERE pp2.id in ( SELECT max(id) as id from  lake_rpc.product_product  pp WHERE pp.active=1 and pp.approved=1 \n", "GROUP BY item_id)  \n", "group by 1\n", "\n", "\"\"\"\n", "Category = pd.read_sql(q3, con=redshift_con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\").connect()\n", "sql = \"\"\"\n", "\n", "select \n", "o.name as 'Store Name',\n", "o.tax_location_id as 'City_Id',\n", "p.outlet_id as 'Outlet Id',\n", "Case when (poi.run_id) = 0 then 'Adhoc PO'\n", "when poi.run_id is NULL then 'Adhoc PO' else poi.run_id end as  'ARS Id',\n", "\n", "o.facility_id as facility_id,\n", "cf.name as 'Facility Name',\n", "ipf.internal_facility_identifier as facility_identifier,\n", "p.id as 'PO_Id',\n", "p.po_number as 'PO Number',\n", "case when p.po_type_id in (1,2) then 'Parent PO' when p.po_type_id in (3,4,5) then 'Child PO' else 'Internal PO' \n", "end as 'PO Type',\n", "p.vendor_name as 'Vendor Name',\n", "poi.Manufacturer_name,\n", "date(convert_tz(p.issue_date,'+00:00','+05:30')) as 'PO issue date',\n", "date(convert_tz(p.expiry_date,'+00:00','+05:30')) as 'Expiry Date',\n", "date(convert_tz(p.delivery_date,'+00:00','+05:30')) as 'Delivery Date',\n", "case when date(convert_tz(ps.schedule_date_time,'+00:00','+05:30')) >= current_date then \n", "date(convert_tz(ps.schedule_date_time,'+00:00','+05:30'))  else ' ' end as 'Schedule Date',\n", "a.`email` as 'Email' ,\n", "poi.item_id as 'item_id',\n", "poi.`units_ordered` as 'PO Qty',\n", "case when pg.po_id is not null then coalesce(pg.grn_qty,0) end as 'GRN Qty',\n", "poi.remaining_quantity as 'OPEN PO Qty',\n", "poi.`name` as 'Product Name',\n", "poi.uom_text as 'VARIANT UOM TEXT',\n", "poi.mrp as 'MR<PERSON>',\n", "poi.`cost_price` as 'Cost Price',\n", "poi.`landing_rate` as 'Landing Price',\n", "poi.`landing_rate`* poi.`units_ordered` as 'PO Value',\n", "case when pg.po_id is not null then (coalesce(pg.grn_qty,0)*poi.`landing_rate`) end as 'GRN Val',\n", "(poi.remaining_quantity*poi.landing_rate) as 'OPEN PO Val',\n", "poi.`tax_value` as 'Tax',\n", "Round(((poi.mrp-poi.`landing_rate`)/poi.mrp)*100,2) as <PERSON><PERSON>,\n", "posta.name as 'PO State Type',\n", "case when posta.id = 5 then 'Cancelled From Draft' else 'Pending' end as 'State',\n", "p.is_multiple_grn\n", "from po.`purchase_order` p\n", "Left Join po.po_schedule ps on p.id = ps.po_id_id\n", "inner join po.`purchase_order_items` poi on p.id=poi.po_id\n", "inner join retail.console_outlet o on o.id=p.outlet_id\n", "inner join retail.console_location cl on cl.id=o.tax_location_id\n", "inner join retail.auth_user a on a.id=p.`created_by`\n", "inner join po.purchase_order_status posa on posa.po_id = p.id\n", "inner join po.purchase_order_state posta on posta.id = posa.po_state_id\n", "left join (select po_id, item_id, sum(quantity) as grn_qty from po.po_grn group by 1,2)pg \n", "on pg.po_id = poi.po_id and pg.item_id = poi.item_id\n", "left join crates.facility cf on cf.id = o.facility_id\n", "left JOIN (SELECT internal_facility_identifier, facility_id from po.physical_facility group by 1,2)\n", "ipf on ipf.facility_id = o.facility_id \n", "where \n", "( posta.name in ('Created','Scheduled','Unscheduled','Rescheduled','Edit Pending') or p.is_multiple_grn = 1) \n", "and posta.name <> 'Expired'\n", "and date(convert_tz(p.issue_date,'+00:00','+05:30')) between '2021-04-01' and Current_date\n", "and posta.name <> 'Cancelled'\n", "and posa.po_state_id  Not in (4,5,10)\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OPENPO = pd.read_sql_query(sql, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_OP1 = pd.merge(\n", "    left=OPENPO,\n", "    right=bucketx,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"items_id\", \"fac_id\"],\n", "    how=\"left\",\n", ")\n", "Mapped_OP2 = pd.merge(\n", "    left=Mapped_OP1,\n", "    right=bucketab,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"items_ids\", \"fac_ids\"],\n", "    how=\"left\",\n", ")\n", "Mapped_OP = pd.merge(\n", "    left=Mapped_OP2,\n", "    right=Category,\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"items_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_OP[\"bucket_x\"].fillna(\"N\", inplace=True)\n", "Mapped_OP[\"bucket_a_b\"].fillna(\"B\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_OP = Mapped_OP.loc[\n", "    :,\n", "    [\n", "        \"Store Name\",\n", "        \"City_Id\",\n", "        \"Outlet Id\",\n", "        \"ARS Id\",\n", "        \"facility_id\",\n", "        \"Facility Name\",\n", "        \"facility_identifier\",\n", "        \"PO_Id\",\n", "        \"PO Number\",\n", "        \"PO Type\",\n", "        \"Vendor Name\",\n", "        \"Manufacturer_name\",\n", "        \"PO issue date\",\n", "        \"Expiry Date\",\n", "        \"Delivery Date\",\n", "        \"Schedule Date\",\n", "        \"Email\",\n", "        \"item_id\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"PO Qty\",\n", "        \"GRN Qty\",\n", "        \"OPEN PO Qty\",\n", "        \"Product Name\",\n", "        \"VARIANT UOM TEXT\",\n", "        \"MRP\",\n", "        \"Cost Price\",\n", "        \"Landing Price\",\n", "        \"PO Value\",\n", "        \"GRN Val\",\n", "        \"OPEN PO Val\",\n", "        \"Tax\",\n", "        \"Margin\",\n", "        \"PO State Type\",\n", "        \"State\",\n", "        \"is_multiple_grn\",\n", "    ],\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_OP.to_csv(ld + \"_OPEN_PO.csv\", index=False)\n", "sheet_id = \"1VBcmA-8XkeYpVNn6BlOttzWLklAJYuBE8tDzDP7sE8I\"\n", "email_id = pb.from_sheets(sheet_id, \"email_pur_mis\")\n", "local_filename1 = ld + \"_OPEN_PO.csv\"\n", "cloud_filepath = \"pencilbox/varun/bucketing/\" + local_filename1\n", "pb.to_s3(local_filename1, bucket_name, cloud_filepath)\n", "file_name_link = create_presigned_url(\n", "    bucket_name, object_name=cloud_filepath, expiration=60 * 60 * 96\n", ")\n", "from_email = \"<EMAIL>\"\n", "to_email = list(email_id[\"email\"])\n", "subject = \"OPEN PO \" + ld1\n", "html_content = (\n", "    \"\"\"Hi Team,<br/><br/>\n", "                    <p> Please find a link of OPEN PO below. <br/> \n", "                         Kindly go through the same and let me know in case of any query.<br/> \n", "                             <br/> <br/><b> link: </b><br/></p>\"\"\"\n", "    + file_name_link\n", "    + \"\"\"\n", "                            <p style=\"color:red\"><br/><br/> Note: Above link will be expire in next 96 hours <br/></p>\n", "                            <p><br/> <br/>\"\"\"\n", ")\n", "\n", "pb.send_email(from_email, to_email, subject, html_content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}