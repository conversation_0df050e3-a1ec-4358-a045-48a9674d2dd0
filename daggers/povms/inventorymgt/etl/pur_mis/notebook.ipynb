{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "# retail = pb.get_connection(\"retail\").connect()\n", "import pandas as pd\n", "import logging\n", "import boto3\n", "from tqdm.notebook import tqdm\n", "import gc\n", "import time\n", "\n", "from email.message import EmailMessage\n", "import numpy as np\n", "import datetime as dt\n", "from botocore.exceptions import ClientError\n", "\n", "bucket_name = \"grofers-prod-dse-sgp\"\n", "ld = dt.datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "\n", "def create_presigned_url(bucket_name, object_name, expiration=3600):\n", "    # Generate a presigned URL for the S3 object\n", "    s3_client = boto3.client(\n", "        \"s3\", **pb.get_secret(\"dse/iam_users/application-pencilbox-s3-access\")\n", "    )\n", "    try:\n", "        response = s3_client.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": object_name},\n", "            ExpiresIn=expiration,\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        logging.error(e)\n", "        return None\n", "    # The response contains the presigned URL\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def retry_function(func):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = func()\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift_connection = pb.get_connection(\"redpen\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = \"\"\"\n", "with\n", "PRODUCT_Category AS\n", "  (\n", "      SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   ),\n", "    \n", "category_pre as\n", "(\n", "SELECT item_id,\n", "       product_id,\n", "       (cat.product || ' ' || cat.unit) AS name,\n", "       cat.L0,\n", "       cat.l1,\n", "       cat.l2 ,\n", "       cat.brand,\n", "       cat.manf,\n", "       cat.product_type,\n", "       cat.unit as UOM\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", ")\n", "\n", "select \n", "a.item_id as item_ids,\n", "max(a.l0) as l0,\n", "max(a.l1) as l1,\n", "max(a.l2) as l2,\n", "max(pm.name) as mfg_name\n", "from category_pre a\n", "left join lake_rpc.product_product  pp2 on pp2.item_id = a.item_id\n", "LEFT JOIN lake_rpc.product_brand pb ON pb.id = pp2.brand_id\n", "LEFT JOIN lake_rpc.product_manufacturer pm ON pm.id=pb.manufacturer_id \n", "WHERE pp2.id in ( SELECT max(id) as id from  lake_rpc.product_product  pp WHERE pp.active=1 and pp.approved=1 \n", "GROUP BY item_id)  \n", "group by 1\n", "\n", "\"\"\"\n", "Category = read_sql_query(q, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q1 = \"\"\"\n", "with\n", "bucket_info as (\n", "    select \n", "        item_id, \n", "        facility_id,\n", "        count(distinct case when tag_value = 'Y' then tag_value end) as tag_x,  \n", "        count(distinct case when tag_value = 'A' then tag_value end) as tag_A,\n", "        count(distinct case when tag_value = 'B' then tag_value end) as tag_b,\n", "        count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "    from ( \n", "        Select \n", "            b.item_id, \n", "            po.facility_id,\n", "            tag_value,\n", "            tag_type_id \n", "        from   lake_rpc.item_outlet_tag_mapping  b\n", "        join lake_retail.view_console_outlet po ON b.outlet_id = po.id\n", "        group by 1,2,3,4\n", "        )\n", "    Group By 1,2\n", ")\n", "    select \n", "        item_id as items_ids,\n", "        facility_id as fac_ids,\n", "        case when tag_A = 1 then 'A'\n", "             when tag_b = 1 then 'B'\n", "             else 'B' end as bucket_a_b\n", "    from bucket_info\n", "\n", "\"\"\"\n", "bucketab = read_sql_query(q1, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q2 = \"\"\"\n", "with\n", "bucket_info as (\n", "    select \n", "        item_id, \n", "        facility_id,\n", "        count(distinct case when tag_value = 'Y' then tag_value end) as tag_x,  \n", "        count(distinct case when tag_value = 'A' then tag_value end) as tag_A,\n", "        count(distinct case when tag_value = 'B' then tag_value end) as tag_b,\n", "        count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "    from ( \n", "        Select \n", "            b.item_id, \n", "            po.facility_id,\n", "            tag_value,\n", "            tag_type_id \n", "        from   lake_rpc.item_outlet_tag_mapping  b\n", "        join lake_retail.view_console_outlet po ON b.outlet_id = po.id\n", "        group by 1,2,3,4\n", "        )\n", "    Group By 1,2\n", ")\n", "    \n", "    select \n", "        item_id as items_id,\n", "        facility_id as fac_id,\n", "        case when tag_x = 1 then 'Y' else 'N' end as bucket_x\n", "    from bucket_info\n", "\n", "\n", "\"\"\"\n", "bucketx = read_sql_query(q2, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmp = \"\"\"\n", "\n", "select  pv.item_id as item_idm, pv.po_id as po_idm,  max(rp.variant_mrp) as grn_mrp\n", "from lake_po.po_grn pv\n", "left join(select variant_id, max(variant_mrp) as variant_mrp from lake_rpc.product_product group by 1 )rp \n", "ON rp.variant_id = pv.variant_id\n", "--where\n", "--date(convert_tz(pv.created_at,'+00:00','+05:30')) between date(curdate()-interval 46 day) and date(curdate())\n", "group by 1,2\n", "\n", "\"\"\"\n", "grnmrp = read_sql_query(gmp, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grnmrp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_item_hour_date_combination_query = \"\"\"\n", "\n", "DROP TABLE IF EXISTS playground.daily_purchase_mis;\n", "CREATE TABLE playground.daily_purchase_mis AS\n", "\n", "with entity_df as\n", "(\n", "select \n", "    distinct entity_vendor_type_id, entity_vendor_id, vendor_name as entity_name\n", "from \n", "    lake_retail.outlet_entity_vendor_mapping base\n", "inner join \n", "    lake_vms.vms_vendor vms_vendor on base.entity_vendor_id = vms_vendor.id and vms_vendor.active = 1\n", "where\n", "    base.active = 1\n", "),\n", "\n", "base as (\n", "        select\n", "        destination_entity_vendor_id, entity_name, invoice_id,\n", "        po.id as po_id, po.po_number as PO_Number,\n", "        (\n", "        case \n", "            when po.po_type_id in (1,2) then 'Parent_PO'\n", "            when po.po_type_id in (3,4,5) then 'Child_PO' \n", "            else 'Internal_PO' end\n", "        ) as PO_Type,\n", "        por.parent_po_id,\n", "        date(po.issue_date + interval '5.5 Hours') as PO_Issue_Date,\n", "        date(po.expiry_date + interval '5.5 Hours') as PO_Expiry_Date,  \n", "        date(ps.schedule_date + interval '5.5 Hours') as PO_Schedule_Date,\n", "        po.outlet_id as Outlet_Id,\n", "        (\n", "        Case \n", "            when (poi.run_id) = 0 then 'Adhoc_PO'\n", "            when poi.run_id is NULL then 'Adhoc_PO'\n", "            else poi.run_id end\n", "        ) as  ARS_Id,\n", "        co.name as Outlet_Name,\n", "\n", "        co.facility_id as facility_id,\n", "        cf.name as Facility_Name,\n", "\n", "        po.vendor_id as Vendor_Id,\n", "        po.vendor_name as Vendor_Name,\n", "        poi.item_id as item_id,\n", "        poi.name as Product_Name,\n", "        poi.uom_text as VARIANT_UOM_TEXT,\n", "        poi.brand as Brand_Name,\n", "        (case when p.is_pl = 0 then 'NON_PL' else 'PL' end) as PL_NONPL,\n", "        poi.units_ordered as PO_QTY,\n", "        poi.cost_price as Cost_Price,\n", "        poi.landing_rate as Landing_Price,\n", "        pg.landing_price as GRN_Landing_Price,\n", "        poi.total_amount as PO_Value,\n", "        poi.mrp as PO_MRP,\n", "\n", "        case\n", "        when pos.po_state_id in (4,5,10) then 'Invalid'\n", "        when (pos.po_state_id in (8) or date(po.expiry_date + interval '5.5 Hours') < current_date) and pg.po_id is null  then 'Expired' \n", "        when pos.po_state_id not in (4,5,10) and pg.po_id is not null and po.is_multiple_grn = 1 then 'MultiGRN_Serviced'\n", "        when  pos.po_state_id not in (4,5,10) and pg.po_id is not null and is_multiple_grn <> 1  then 'Serviced'\n", "        else 'Pending' end as PO_Status,\n", "\n", "        (case when pos.po_state_id not in (4,5,10) then 'Not_Cancelled'\n", "        else 'Cancelled' end) as Cancel_Status,\n", "        (case when pos.po_state_id in (4,5,10) then 'Invalid'\n", "        when pg.po_id is not null or pos.po_state_id in (8) then 'Closed' else 'Open' end) as Open_Status,\n", "\n", "        case when pos.po_state_id not in (4,5,10) then coalesce(pg.quantity,0) else 0 end as GRN_QTY,\n", "        case when pos.po_state_id not in (4,5,10) then coalesce(pg.grn_val,0) else 0 end as GRN_Value,\n", "        case when pos.po_state_id not in (4,5,10) then date(pg.grn_d) end as GRN_Date,\n", "        pg.grn_id,\n", "        po.is_multiple_grn as Is_Multiple_GRN\n", "\n", "        from \n", "            lake_po.purchase_order po\n", "        inner join \n", "            (select * from lake_po.purchase_order_items where created_at >= current_date - 46)  poi on po.id = poi.po_id\n", "        Left Join\n", "            (select po_id_id, max(date(schedule_date_time)) as schedule_date from lake_po.po_schedule group by 1) ps on po.id = ps.po_id_id\n", "        inner join \n", "            (select distinct item_id,is_pl from lake_rpc.product_product where active = 1) p on poi.item_id = p.item_id\n", "        left join \n", "            lake_po.purchase_order_relations por on por.child_po_id = po.id\n", "        inner join \n", "            lake_po.purchase_order_status pos on pos.po_id = po.id\n", "        inner join \n", "            lake_retail.console_outlet co on co.id = po.outlet_id\n", "        inner join\n", "            lake_retail.console_location cl on cl.id = co.tax_location_id\n", "        left join \n", "            (\n", "                select\n", "                    po_number, LISTAGG(invoice_id, ' | ') as invoice_id\n", "                    from (\n", "                        select\n", "                            sd.purchase_order_id as po_number, il.merchant_invoice_id as invoice_id\n", "                        from\n", "                            lake_ims.ims_inventory_log il\n", "                        left join \n", "                            lake_ims.ims_inventory_stock_details sd on sd.inventory_update_id = il.inventory_update_id\n", "                        where\n", "                            il.inventory_update_type_id in (1,28,76,90,93)\n", "                        and\n", "                            il.created_at >= current_date - 46\n", "                        and \n", "                            sd.purchase_order_id is not null\n", "                        and\n", "                            sd.purchase_order_id <> ' '\n", "                        \n", "                        group by\n", "                            1,2\n", "                        )\n", "                    group by \n", "                        1\n", "            ) invoice on invoice.po_number = po.po_number\n", "        left join \n", "            (\n", "                select \n", "                    po_id, item_id, grn_id, landing_price, max(date(created_at + interval '5.5 Hours')) as grn_d, \n", "                    sum(quantity) as quantity, sum(quantity*landing_price) as grn_val\n", "                from lake_po.po_grn\n", "                WHERE created_at >= current_date - 46\n", "                group by 1,2,3,4\n", "            ) pg on pg.po_id = poi.po_id and pg.item_id = poi.item_id\n", "\n", "        left join lake_crates.facility cf on cf.id = co.facility_id\n", "\n", "        left join entity_df df on destination_entity_vendor_id = df.entity_vendor_id\n", "\n", "        -- p.destination_entity_vendor_id in (select distinct entity_vendor_id from lake_retail.outlet_entity_vendor_mapping where entity_vendor_type_id = 5 and active = 1)\n", "        where po.issue_date >= current_date - 46\n", "\n", "        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36\n", "    )\n", "\n", "\n", "SELECT\n", "    a.*, \n", "    ((GRN_QTY*1.00000/PO_QTY+0.00001)*100) as Fill_Rate\n", "FROM (\n", "\n", "    SELECT destination_entity_vendor_id, entity_name, invoice_id, po_id, po_number, po_type, parent_po_id, po_issue_date, po_expiry_date, po_schedule_date, outlet_id, ars_id, outlet_name, facility_id,\n", "            facility_name, vendor_id, vendor_name, item_id, product_name, variant_uom_text, brand_name, pl_nonpl, po_qty, cost_price, \n", "            landing_price, grn_landing_price, po_value, po_mrp, po_status, cancel_status, open_status, grn_date, is_multiple_grn,\n", "            sum(grn_qty) as grn_qty, sum(grn_value) as grn_value\n", "    FROM base\n", "    GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33\n", ") a\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "redshift_connection.execute(outlet_item_hour_date_combination_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_list_sql = f\"\"\"\n", "    SELECT po_number FROM playground.daily_purchase_mis\n", "    GROUP BY 1\n", "    \"\"\"\n", "po_list_df = read_sql_query(po_list_sql, CON_REDSHIFT)\n", "len(po_list_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "po_list_df_split = np.array_split(po_list_df, np.ceil(po_list_df.shape[0] / 30000))\n", "print(\"po_list_df_split\", len(po_list_df_split))\n", "\n", "\n", "def po_details_func():\n", "    empty_df = pd.DataFrame()\n", "    for i in range(len(po_list_df_split)):\n", "        po_list = tuple(list(po_list_df_split[i][\"po_number\"].astype(str).unique()))\n", "\n", "        mis_sql = f\"\"\"\n", "            SELECT * FROM playground.daily_purchase_mis\n", "            WHERE po_number in {po_list}\n", "            \"\"\"\n", "        mis_df = read_sql_query(mis_sql, CON_REDSHIFT)\n", "\n", "        # empty_df.append\n", "        empty_df = pd.concat([mis_df, empty_df])\n", "        del mis_df\n", "        print(len(po_list))\n", "        gc.collect()\n", "\n", "    return empty_df\n", "\n", "\n", "empty_df = retry_function(po_details_func)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "Purchase_MIS = empty_df.copy()\n", "del empty_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "Purchase_MIS.shape[0], Category.shape[0], bucketx.shape[0], bucketab.shape[\n", "    0\n", "], grnmrp.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "Purchase_MIS[\"cost_price\"] = round(Purchase_MIS[\"cost_price\"], 1).astype(float)\n", "Purchase_MIS[\"landing_price\"] = round(Purchase_MIS[\"landing_price\"], 1).astype(float)\n", "Purchase_MIS[\"po_value\"] = round(Purchase_MIS[\"po_value\"], 1).astype(float)\n", "Purchase_MIS[\"po_mrp\"] = round(Purchase_MIS[\"po_mrp\"], 1).astype(float)\n", "Purchase_MIS[\"grn_qty\"] = round(Purchase_MIS[\"grn_qty\"], 1).astype(float)\n", "Purchase_MIS[\"grn_value\"] = round(Purchase_MIS[\"grn_value\"], 1).astype(float)\n", "Purchase_MIS[\"fill_rate\"] = round(Purchase_MIS[\"fill_rate\"], 0).astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FOR insert % at end\n", "Purchase_MIS[\"fill_rate\"] = Purchase_MIS[\"fill_rate\"].astype(str)\n", "Purchase_MIS[\"fill_rate\"] = Purchase_MIS[\"fill_rate\"] + \"%\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Purchase_MIS.shape[0], Category.shape[0], bucketx.shape[0], bucketab.shape[\n", "    0\n", "], grnmrp.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mis_category():\n", "\n", "    Mapped_MIS1 = pd.merge(\n", "        left=Purchase_MIS,\n", "        right=Category,\n", "        left_on=[\"item_id\"],\n", "        right_on=[\"item_ids\"],\n", "        how=\"left\",\n", "    )\n", "    return Mapped_MIS1\n", "\n", "\n", "Mapped_MIS1 = retry_function(mis_category)\n", "\n", "\n", "del Purchase_MIS, Category\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mis_bucketx():\n", "\n", "    Mapped_MIS2 = pd.merge(\n", "        left=Mapped_MIS1,\n", "        right=bucketx,\n", "        left_on=[\"item_id\", \"facility_id\"],\n", "        right_on=[\"items_id\", \"fac_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    return Mapped_MIS2\n", "\n", "\n", "Mapped_MIS2 = retry_function(mis_bucketx)\n", "\n", "del Mapped_MIS1, bucketx\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mis_bucketab():\n", "\n", "    Mapped_MIS3 = pd.merge(\n", "        left=Mapped_MIS2,\n", "        right=bucketab,\n", "        left_on=[\"item_id\", \"facility_id\"],\n", "        right_on=[\"items_ids\", \"fac_ids\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    return Mapped_MIS3\n", "\n", "\n", "Mapped_MIS3 = retry_function(mis_bucketab)\n", "\n", "\n", "del Mapped_MIS2, bucketab\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mis_grnmrp():\n", "\n", "    Mapped_MIS = pd.merge(\n", "        left=Mapped_MIS3,\n", "        right=grnmrp,\n", "        left_on=[\"item_id\", \"po_id\"],\n", "        right_on=[\"item_idm\", \"po_idm\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    return Mapped_MIS\n", "\n", "\n", "Mapped_MIS = retry_function(mis_grnmrp)\n", "\n", "del Mapped_MIS3, grnmrp\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "# like coalesce fun , in place of blank val\n", "Mapped_MIS[\"bucket_x\"].fillna(\"N\", inplace=True)\n", "Mapped_MIS[\"bucket_a_b\"].fillna(\"B\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "Mapped_LO = Mapped_MIS.loc[\n", "    :,\n", "    [\n", "        \"destination_entity_vendor_id\",\n", "        \"entity_name\",\n", "        \"invoice_id\",\n", "        \"po_id\",\n", "        \"po_number\",\n", "        \"po_type\",\n", "        \"parent_po_id\",\n", "        \"po_issue_date\",\n", "        \"po_expiry_date\",\n", "        \"po_schedule_date\",\n", "        \"outlet_id\",\n", "        \"ars_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"item_id\",\n", "        \"product_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"mfg_name\",\n", "        \"variant_uom_text\",\n", "        \"brand_name\",\n", "        \"pl_nonpl\",\n", "        \"po_qty\",\n", "        \"cost_price\",\n", "        \"landing_price\",\n", "        \"grn_landing_price\",\n", "        \"po_value\",\n", "        \"po_mrp\",\n", "        \"po_status\",\n", "        \"cancel_status\",\n", "        \"is_multiple_grn\",\n", "        \"grn_mrp\",\n", "        \"grn_qty\",\n", "        \"grn_value\",\n", "        \"fill_rate\",\n", "        \"grn_date\",\n", "        \"bucket_x\",\n", "        \"bucket_a_b\",\n", "    ],\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_LO.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "# tmp for temp create file in folder\n", "\n", "sheet_id = \"1mTdNJFkFqjM00j8DTb68OvrLXEtpr6-1_iylXPa_bvQ\"\n", "email_id = pb.from_sheets(sheet_id, \"email_pur_mis_new\")\n", "# email_id['email'] = '<EMAIL>'\n", "email_id = email_id.drop_duplicates()\n", "email_id\n", "# list(email_id[\"email\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id = dt.datetime.now().strftime(\"%Y-%m-%d\")\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "import os\n", "\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "bucket_name = \"grofers-retail-test\"\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "s3c = boto3.client(\"s3\", aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# aws_key"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "log_files_created = []\n", "\n", "\n", "def to_S3(file, name):\n", "    try:\n", "\n", "        bucket_obj.upload_file(file, f\"reports/{file.split('/')[-1]}\")\n", "        print({file.split(\"/\")[-1]})\n", "        log_files_created.append(file)\n", "        presigned_url = s3c.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": f\"reports/{file.split('/')[-1]}\"},\n", "            HttpMethod=\"GET\",\n", "            ExpiresIn=172800,\n", "        )\n", "        return presigned_url\n", "    except Exception as e:\n", "        error = f\"\"\":red_circle: *Alert*: Smart Indent - Simulation, Error uploading log file `{name}` to S3 for Run ID: `{uid}`\n", "        \\n```{str(e)}```\\n Download Link - `{file}`\"\"\"\n", "        send_to_slack(error)\n", "        raise Exception(e)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "Tag = \"Purchase_MIS/datapulls\"\n", "cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "# directories\n", "GLOBAL_BASE_DIR = cwd\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "outlet_type_sql = f\"\"\"\n", "    select id as outlet_id, business_type_id from lake_retail.console_outlet\n", "    where active = 1\n", "    \"\"\"\n", "outlet_type = pd.read_sql_query(outlet_type_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "item_type_sql = f\"\"\"\n", "with base as \n", "(\n", "    SELECT \n", "        distinct item_id, 'FnV' as assortment_type\n", "    FROM \n", "        lake_rpc.item_category_details\n", "    WHERE l0_id = 1487\n", "\n", "    UNION\n", "\n", "    SELECT\n", "        distinct item_id, 'Perishable' as assortment_type\n", "    FROM\n", "        lake_rpc.item_details\n", "    WHERE\n", "       perishable = 1\n", "    AND item_id not in \n", "        (SELECT \n", "            distinct item_id\n", "        FROM \n", "            lake_rpc.item_category_details\n", "        WHERE l0_id = 1487)\n", ")\n", "\n", "SELECT * FROM base\"\"\"\n", "item_type = pd.read_sql_query(item_type_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "item_type_list = tuple(list(item_type[\"item_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mapped_l0_outlet():\n", "    Mapped_LO_new = pd.merge(Mapped_LO, outlet_type, on=[\"outlet_id\"], how=\"left\")\n", "    return Mapped_LO_new\n", "\n", "\n", "Mapped_LO = retry_function(mapped_l0_outlet)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Mapped_LO.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mapped_backend_frontend():\n", "    print(\"1\")\n", "    print(Mapped_LO.shape)\n", "\n", "    Mapped_LO_frontend = Mapped_LO[Mapped_LO.business_type_id == 7]\n", "    Mapped_LO_backend = Mapped_LO[Mapped_LO.business_type_id != 7]\n", "\n", "    # del Mapped_LO\n", "\n", "    Mapped_LO_backend_fnv_per = Mapped_LO_backend[\n", "        Mapped_LO_backend.item_id.isin(item_type_list)\n", "    ]\n", "    Mapped_LO_backend_packaged = Mapped_LO_backend[\n", "        ~Mapped_LO_backend.item_id.isin(item_type_list)\n", "    ]\n", "\n", "    del Mapped_LO_backend\n", "\n", "    return Mapped_LO_backend_fnv_per, Mapped_LO_backend_packaged, Mapped_LO_frontend\n", "\n", "\n", "(\n", "    Mapped_LO_backend_fnv_per,\n", "    Mapped_LO_backend_packaged,\n", "    Mapped_LO_frontend,\n", ") = retry_function(mapped_backend_frontend)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapped_LO.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mapped_l0_frontend_s3():\n", "\n", "    Mapped_LO_frontend.to_csv(ld + \"_Purchase_MIS_Frontend.csv\", index=False)\n", "    file_name = ld + \"_Purchase_MIS_Frontend.csv\"\n", "    cpd_dataframe = pd.read_csv(file_name, iterator=True, chunksize=1000000)\n", "    count = 0\n", "    empty_string = \"\"\n", "    for chunk in cpd_dataframe:\n", "        # Keep track of chunk count\n", "        # print(chunk)\n", "        count = count + 1\n", "        # chunk_file_name = str(count)+'_'+file_name\n", "        chunk_file_name = f\"{outputs}/{file_name.replace('.csv','')}_{str(count)}.csv\"\n", "\n", "        chunk.to_csv(chunk_file_name, index=False)\n", "        name = \"Purchase MIS - Frontend - \" + run_id + \"-\" + str(count)\n", "        print(name)\n", "        smart_attachment = to_S3(chunk_file_name, name)\n", "\n", "        string_com = f\"<a href={smart_attachment}> Purchase MIS - Frontend - {str(count)} </a><br>\"\n", "        empty_string = empty_string + string_com\n", "\n", "    return empty_string\n", "\n", "\n", "empty_string = retry_function(mapped_l0_frontend_s3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mapped_l0_backend_fnv_per_s3():\n", "\n", "    Mapped_LO_backend_fnv_per.to_csv(\n", "        ld + \"_Purchase_MIS_Backend_Non_Packaged.csv\", index=False\n", "    )\n", "    file_name = ld + \"_Purchase_MIS_Backend_Non_Packaged.csv\"\n", "    cpd_dataframe = pd.read_csv(file_name, iterator=True, chunksize=1000000)\n", "    count = 0\n", "    empty_string = \"\"\n", "    for chunk in cpd_dataframe:\n", "        # Keep track of chunk count\n", "        # print(chunk)\n", "        count = count + 1\n", "        # chunk_file_name = str(count)+'_'+file_name\n", "        chunk_file_name = f\"{outputs}/{file_name.replace('.csv','')}_{str(count)}.csv\"\n", "\n", "        chunk.to_csv(chunk_file_name, index=False)\n", "        name = \"Purchase MIS - Backend - Non Packaged - \" + run_id + \"-\" + str(count)\n", "        print(name)\n", "        smart_attachment = to_S3(chunk_file_name, name)\n", "\n", "        string_com = f\"<a href={smart_attachment}> Purchase MIS - Backend - Non Packaged - {str(count)} </a><br>\"\n", "        empty_string = empty_string + string_com\n", "\n", "    return empty_string\n", "\n", "\n", "empty_string_new = retry_function(mapped_l0_backend_fnv_per_s3)\n", "empty_string = empty_string + empty_string_new"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%time\n", "\n", "\n", "def mapped_l0_backend_packaged_s3():\n", "\n", "    Mapped_LO_backend_packaged.to_csv(\n", "        ld + \"_Purchase_MIS_Backend_Packaged.csv\", index=False\n", "    )\n", "    file_name = ld + \"_Purchase_MIS_Backend_Packaged.csv\"\n", "    cpd_dataframe = pd.read_csv(file_name, iterator=True, chunksize=1000000)\n", "    count = 0\n", "    empty_string = \"\"\n", "    for chunk in cpd_dataframe:\n", "        # Keep track of chunk count\n", "        # print(chunk)\n", "        count = count + 1\n", "        # chunk_file_name = str(count)+'_'+file_name\n", "        chunk_file_name = f\"{outputs}/{file_name.replace('.csv','')}_{str(count)}.csv\"\n", "\n", "        chunk.to_csv(chunk_file_name, index=False)\n", "        name = \"Purchase MIS - Backend - Packaged - \" + run_id + \"-\" + str(count)\n", "        print(name)\n", "        smart_attachment = to_S3(chunk_file_name, name)\n", "\n", "        string_com = f\"<a href={smart_attachment}> Purchase MIS - Backend - Packaged - {str(count)} </a><br>\"\n", "        empty_string = empty_string + string_com\n", "\n", "    return empty_string\n", "\n", "\n", "empty_string_new = retry_function(mapped_l0_backend_packaged_s3)\n", "empty_string = empty_string + empty_string_new"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["empty_string  # Only 2 Days"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = f\"\"\"\n", "    Hi Team,<br/><br/>\n", "    <p> Please find a link of Purchase MIS below. <br/> \n", "     Kindly go through the same and let me know in case of any query.<br/> \n", "     <br/> <br/><b> link: </b><br/></p>\n", "    {empty_string}\n", "    <p style=\"color:red\"><br/><br/> Note: Above link(s) will be expire in next 48 hrs <br/></p>\n", "    <p><br/> <br/>\n", "\n", "   <PERSON><PERSON>,<br>\n", "    Data Inventory \n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = list(email_id[\"email\"])\n", "subject = \"Purchase MIS \" + ld\n", "\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}