alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: hourly_storage_change_alert
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SA4412F4
path: povms/daily_storage_alerts/etl/hourly_storage_change_alert
paused: true
pool: povms_pool
project_name: daily_storage_alerts
schedule:
  end_date: '2025-03-13T00:00:00'
  interval: 0 */6 * * *
  start_date: '2025-01-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
