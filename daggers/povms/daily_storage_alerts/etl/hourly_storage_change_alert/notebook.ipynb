{"cells": [{"cell_type": "code", "execution_count": null, "id": "a340f61c-4eb7-48b7-9daf-b865df5df9b9", "metadata": {}, "outputs": [], "source": ["!pip install -U \"numpy>=1.23,<1.29.0\"\n", "!pip install pymysql"]}, {"cell_type": "code", "execution_count": null, "id": "53814758-163a-4631-bc0b-69958bceeb68", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "\n", "# import numpy>=1.23 as np\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "import pymysql\n", "import pytz\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "c197dc28-3721-4f5b-b7ff-c727fe651c48", "metadata": {}, "outputs": [], "source": ["def fetch_storage_changes():\n", "    sql = f\"\"\"\n", "        With fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "                and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "                group by 1,2,3,4\n", "        ),\n", "\n", "        base as (\n", "        select hsu.*, rank() over(partition by hsu.final_storage_type, hsu.outlet_id order by updated_At desc) as rnk \n", "        from supply_etls.hourly_storage_util hsu \n", "        inner join fo\n", "        on fo.outlet_id = hsu.outlet_id\n", "        where  updated_at >= current_date - interval '1' day),\n", "\n", "        final as (\n", "        select b.outlet_id, b.facility_id, b.facility_name, b.final_storage_type, b.threshold, b.storage_cap, b.rnk , b2.storage_cap as t_1_cap, b2.threshold as t_1_threshold, b2.rnk\n", "        from base b\n", "        left join base b2\n", "        on b2.outlet_id = b.outlet_id\n", "        and b2.final_storage_type = b.final_storage_type\n", "        and b.rnk = (b2.rnk - 6)\n", "        where b.rnk = 1\n", "        ),\n", "\n", "        report as (\n", "        select \n", "            outlet_id, facility_id, facility_name, final_storage_type, threshold as current_threshold, t_1_threshold as old_threshold, storage_cap, t_1_cap,  threshold - t_1_threshold as delta_threshold, storage_cap - t_1_cap as delta_caps\n", "        from\n", "            final)\n", "\n", "        select * from report\n", "       where\n", "           (delta_threshold<0\n", "        or\n", "            delta_caps < -1000)    \n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "\n", "\n", "raw_data = fetch_storage_changes()\n", "print(raw_data.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "3ebf26f8-8945-4485-a794-e934a5a421cf", "metadata": {}, "outputs": [], "source": ["raw_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "69cdc912-1152-4efa-baad-3a38005fb6d8", "metadata": {}, "outputs": [], "source": ["base_df = raw_data.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "a3c9a9e0-0c12-4faf-9083-4a480dbe6fdf", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.2,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"right\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "ee6796a7-9e1f-4a08-9b03-3f6183d16529", "metadata": {}, "outputs": [], "source": ["base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "9d2c7220-76ee-4d68-9bba-0b156e64ed14", "metadata": {}, "outputs": [], "source": ["base_df = base_df.rename(\n", "    columns={\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"facility_id\": \"Facility ID\",\n", "        \"facility_name\": \"Outlet Name\",\n", "        \"final_storage_type\": \"Storage type\",\n", "        \"current_threshold\": \"New Threshold\",\n", "        \"old_threshold\": \"Old Threshold\",\n", "        \"delta_threshold\": \"Delta threshold\",\n", "        \"storage_cap\": \"Current Cap\",\n", "        \"t_1_cap\": \"Old Cap\",\n", "        \"delta_caps\": \"Delta Cap\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b4e54df1-21d3-4a4c-905d-7c7ca0da8ef0", "metadata": {}, "outputs": [], "source": ["base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "491b03ba-959d-4c14-aeb3-1648e9ad6c9b", "metadata": {}, "outputs": [], "source": ["base_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "f92f0500-fe21-4389-8809-67604b04e847", "metadata": {}, "outputs": [], "source": ["if base_df.shape[0] != 0:\n", "    fig, ax = render_mpl_table(\n", "        base_df[\n", "            [\n", "                \"Outlet ID\",\n", "                \"Facility ID\",\n", "                \"Outlet Name\",\n", "                \"Storage type\",\n", "                \"New Threshold\",\n", "                \"Old Threshold\",\n", "                \"Current Cap\",\n", "                \"Old Cap\",\n", "                \"Delta threshold\",\n", "                \"Delta Cap\",\n", "            ]\n", "        ],\n", "        header_columns=0,\n", "    )\n", "    fig.savefig(\"Last hour storage changes.png\")\n", "    utc_now = datetime.now(pytz.UTC)\n", "    ist_now = utc_now.astimezone(pytz.timezone(\"Asia/Kolkata\"))\n", "    ist_timestamp = ist_now.strftime(\"%Y-%m-%d %H:00:00\")\n", "    text_req = \"Storage changes in last 1 hour: \" + ist_timestamp\n", "    ### Regular utilisation\n", "    pb.send_slack_message(\n", "        channel=\"bl-rep-ops-core\",\n", "        text=text_req,\n", "        files=[\"Last hour storage changes.png\"],\n", "    )\n", "else:\n", "    print(\"No changes\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}