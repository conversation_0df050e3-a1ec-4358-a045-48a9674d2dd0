{"cells": [{"cell_type": "code", "execution_count": null, "id": "08315912-0ab7-42a8-b9a4-b6314192ab30", "metadata": {}, "outputs": [], "source": ["!pip install -U \"numpy>=1.23,<1.29.0\"\n", "!pip install pymysql"]}, {"cell_type": "code", "execution_count": null, "id": "7e516e99-4c1c-463e-9c54-cdcf8ae46398", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "\n", "# import numpy>=1.23 as np\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "511871d1-ed6f-4e87-ba86-b4da1658ca33", "metadata": {}, "outputs": [], "source": ["def fetch_raw_data():\n", "    sql = f\"\"\"\n", "        WITH fo AS\n", "          ( SELECT DISTINCT x.facility_id,\n", "                            x.outlet_id,\n", "                            frontend_merchant_id,\n", "                            frontend_merchant_name,\n", "                            x.city_id,\n", "                            outlet_name\n", "           FROM po.physical_facility_outlet_mapping x\n", "           INNER JOIN dwh.dim_merchant_outlet_facility_mapping m ON m.pos_outlet_id = x.outlet_id\n", "           AND is_pos_outlet_active=1\n", "           AND is_backend_merchant_active=TRUE\n", "           AND is_frontend_merchant_active=TRUE\n", "           AND is_current\n", "           AND is_mapping_enabled=TRUE\n", "           AND x.active=1\n", "           AND ars_active=1\n", "           AND x.facility_id IN\n", "             (SELECT facility_id\n", "              FROM retail.console_outlet\n", "              WHERE business_type_id = 7)\n", "           INNER JOIN\n", "             (SELECT cast(merchant_id AS int) merchant_id,\n", "                     polygon\n", "              FROM serviceability.ser_store_polygons\n", "              WHERE TYPE = 'STORE_POLYGON'\n", "                AND is_active = TRUE) poly ON poly.merchant_id = m.frontend_merchant_id ),\n", "                \n", "             FINAL AS\n", "          (SELECT date(hsu.updated_at) AS date_,\n", "                  hsu.outlet_id,\n", "                  hsu.facility_id,\n", "                  hsu.facility_name,\n", "                  final_storage_type,\n", "                  pfsc.threshold as current_threshold,\n", "                  avg(hsu.threshold) as avg_threshold,\n", "                  max(onshelf_utilisation) AS max_osh_util,\n", "                  avg(onshelf_utilisation) as avg_osh_util\n", "           FROM supply_etls.hourly_storage_util hsu\n", "           INNER JOIN fo ON fo.facility_id = hsu.facility_id\n", "           AND hsu.updated_at >= CURRENT_DATE - interval '7' DAY\n", "           AND hsu.updated_at < CURRENT_DATE\n", "           AND final_storage_type IN ('REGULAR',\n", "                                      'HEAVY')\n", "            LEFT JOIN ars.physical_facility_storage_capacity pfsc\n", "            on pfsc.facility_id = hsu.facility_id\n", "            and pfsc.active\n", "            and pfsc.lake_active_record\n", "            and pfsc.storage_type = hsu.final_storage_type\n", "           GROUP BY 1,\n", "                    2,\n", "                    3,\n", "                    4,\n", "                    5,\n", "                    6)\n", "        SELECT *\n", "        FROM FINAL\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "\n", "\n", "raw_data = fetch_raw_data()\n", "print(raw_data.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "8eea694b-12d0-4627-8bf1-41546423112a", "metadata": {}, "outputs": [], "source": ["base_df = raw_data.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "72143dbf-87fc-4158-ac9b-889661cae891", "metadata": {}, "outputs": [], "source": ["base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f8a73344-0203-414c-bef9-da8d5fa606cc", "metadata": {}, "outputs": [], "source": ["base_df[\"date_\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "978c24b8-3740-411b-a6c8-fb3081a6e899", "metadata": {}, "outputs": [], "source": ["base_df[\"Max utilization above 110%\"] = 0\n", "base_df[\"Max utilization above 100%\"] = 0\n", "base_df[\"Max utilization between 80%-100%\"] = 0\n", "base_df[\"Max utilization below 80%\"] = 0\n", "\n", "base_df[\"Avg utilization above 110%\"] = 0\n", "base_df[\"Avg utilization above 100%\"] = 0\n", "base_df[\"Avg utilization between 80%-100%\"] = 0\n", "base_df[\"Avg utilization below 80%\"] = 0\n", "\n", "\n", "base_df[\"Max utilization above 110%\"] = np.where(base_df[\"max_osh_util\"] >= 110, 1, 0)\n", "base_df[\"Max utilization above 100%\"] = np.where(base_df[\"max_osh_util\"] >= 100, 1, 0)\n", "base_df[\"Max utilization between 80%-100%\"] = np.where(\n", "    (base_df[\"max_osh_util\"] >= 80) & (base_df[\"max_osh_util\"] < 100), 1, 0\n", ")\n", "base_df[\"Max utilization below 80%\"] = np.where((base_df[\"max_osh_util\"] < 80), 1, 0)\n", "\n", "\n", "base_df[\"Avg utilization above 110%\"] = np.where(base_df[\"avg_osh_util\"] >= 110, 1, 0)\n", "base_df[\"Avg utilization above 100%\"] = np.where(base_df[\"avg_osh_util\"] >= 100, 1, 0)\n", "base_df[\"Avg utilization between 80%-100%\"] = np.where(\n", "    (base_df[\"avg_osh_util\"] >= 80) & (base_df[\"avg_osh_util\"] < 100), 1, 0\n", ")\n", "base_df[\"Avg utilization below 80%\"] = np.where((base_df[\"avg_osh_util\"] < 80), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "62f1c56b-82ad-4229-abf1-fc86b8ca9d6a", "metadata": {}, "outputs": [], "source": ["base_df[(base_df[\"facility_id\"] == 1755) & (base_df[\"final_storage_type\"] == \"HEAVY\")].sort_values(\n", "    by=\"final_storage_type\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d4f09492-36f2-4f6c-bdb2-0776691bce11", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.2,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"right\", **kwargs\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "markdown", "id": "8997e2cc-1c42-4b13-b43e-822d12e48703", "metadata": {}, "source": ["##### Push raw to sheet"]}, {"cell_type": "code", "execution_count": null, "id": "e37b06a3-b068-4d32-a35c-3b23eac72fb5", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(base_df, \"1CNtCEfH8HYUhJuZxJDwcLt58aOnCqwzuU5rY1zNSwfo\", \"raw_data\")"]}, {"cell_type": "markdown", "id": "071bb876-5f4d-4a8a-bcf4-d40bd0c3c1c7", "metadata": {}, "source": ["##### Create trend graph"]}, {"cell_type": "code", "execution_count": null, "id": "a24b486d-8eef-48e8-ba06-dc78759c2ada", "metadata": {}, "outputs": [], "source": ["base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "31ba9e06-8048-4acf-9906-5a14683d1dc4", "metadata": {}, "outputs": [], "source": ["trend_df = (\n", "    base_df.groupby([\"date_\", \"final_storage_type\"])\n", "    .agg(\n", "        {\n", "            \"Max utilization above 110%\": \"sum\",\n", "            \"Max utilization above 100%\": \"sum\",\n", "            \"Max utilization between 80%-100%\": \"sum\",\n", "            \"Max utilization below 80%\": \"sum\",\n", "            \"Avg utilization above 110%\": \"sum\",\n", "            \"Avg utilization above 100%\": \"sum\",\n", "            \"Avg utilization between 80%-100%\": \"sum\",\n", "            \"Avg utilization below 80%\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(columns={\"storage_type\": \"final_storage_type\", \"date_\": \"Date\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2886d801-f7f8-43a8-bc5b-625d6b2ad504", "metadata": {}, "outputs": [], "source": ["trend_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e80c6953-aaf0-478c-ba87-51fcb1a59512", "metadata": {}, "outputs": [], "source": ["regular_trend = trend_df[trend_df[\"final_storage_type\"] == \"REGULAR\"]\n", "heavy_trend = trend_df[trend_df[\"final_storage_type\"] == \"HEAVY\"]"]}, {"cell_type": "code", "execution_count": null, "id": "907cc121-7378-4a22-864a-b7f7e330a54c", "metadata": {}, "outputs": [], "source": ["regular_trend.columns"]}, {"cell_type": "code", "execution_count": null, "id": "02dde280-c631-4775-86d5-3780c4eb54fb", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    regular_trend[\n", "        [\n", "            \"Date\",\n", "            \"final_storage_type\",\n", "            \"Max utilization above 110%\",\n", "            \"Max utilization above 100%\",\n", "            \"Max utilization between 80%-100%\",\n", "            \"Max utilization below 80%\",\n", "            \"Avg utilization above 110%\",\n", "            \"Avg utilization above 100%\",\n", "            \"Avg utilization between 80%-100%\",\n", "            \"Avg utilization below 80%\",\n", "        ]\n", "    ],\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"Utilisation trend for regular storage type.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "1774d10a-0bfc-48aa-8086-bce9f3c16c81", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(\n", "    heavy_trend[\n", "        [\n", "            \"Date\",\n", "            \"final_storage_type\",\n", "            \"Max utilization above 110%\",\n", "            \"Max utilization above 100%\",\n", "            \"Max utilization between 80%-100%\",\n", "            \"Max utilization below 80%\",\n", "            \"Avg utilization above 110%\",\n", "            \"Avg utilization above 100%\",\n", "            \"Avg utilization between 80%-100%\",\n", "            \"Avg utilization below 80%\",\n", "        ]\n", "    ],\n", "    header_columns=0,\n", ")\n", "fig.savefig(\"Utilisation trend for heavy storage type.png\")"]}, {"cell_type": "markdown", "id": "07f3f59f-dd0b-45d9-be12-eedf2b5652ac", "metadata": {}, "source": ["##### Consecutive utilisation buckets"]}, {"cell_type": "code", "execution_count": null, "id": "4307644a-239f-417d-857b-c577297d4acf", "metadata": {}, "outputs": [], "source": ["base_df[\"date_\"] = pd.to_datetime(base_df[\"date_\"], format=\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "12a17748-a6dc-4de8-970d-dcae23b49b1c", "metadata": {}, "outputs": [], "source": ["from datetime import date\n", "\n", "# Returns the current local date\n", "today = datetime.now() - <PERSON><PERSON><PERSON>(days=2)\n", "print(today)"]}, {"cell_type": "code", "execution_count": null, "id": "f0b1368f-904e-49bc-bdad-07fe345c7482", "metadata": {}, "outputs": [], "source": ["regular_df = pd.DataFrame(\n", "    columns=[\n", "        \"Consecutive day count\",\n", "        \"Max utilization above 110%\",\n", "        \"Avg utilization above 110%\",\n", "        \"Max utilization below 80%\",\n", "        \"Avg utilization below 80%\",\n", "    ]\n", ")\n", "heavy_df = pd.DataFrame(\n", "    columns=[\n", "        \"Consecutive day count\",\n", "        \"Max utilization above 110%\",\n", "        \"Avg utilization above 110%\",\n", "        \"Max utilization below 80%\",\n", "        \"Avg utilization below 80%\",\n", "    ]\n", ")\n", "\n", "for i in range(1, 8):\n", "    temp_date = datetime.now() - <PERSON><PERSON>ta(days=i + 1)\n", "    temp_df = base_df[base_df[\"date_\"] > temp_date]\n", "    temp_df_agg = (\n", "        temp_df.groupby([\"outlet_id\", \"facility_id\", \"facility_name\", \"final_storage_type\"])\n", "        .agg(\n", "            {\n", "                \"Max utilization above 110%\": \"sum\",\n", "                \"Max utilization below 80%\": \"sum\",\n", "                \"Avg utilization above 110%\": \"sum\",\n", "                \"Avg utilization below 80%\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    temp_df_agg[\"Max utilization above 110% flag\"] = 0\n", "    temp_df_agg[\"Max utilization below 80% flag\"] = 0\n", "    temp_df_agg[\"Avg utilization above 110% flag\"] = 0\n", "    temp_df_agg[\"Avg utilization below 80% flag\"] = 0\n", "\n", "    temp_df_agg[\"Max utilization above 110% flag\"] = np.where(\n", "        temp_df_agg[\"Max utilization above 110%\"] == i, 1, 0\n", "    )\n", "    temp_df_agg[\"Max utilization below 80% flag\"] = np.where(\n", "        temp_df_agg[\"Max utilization below 80%\"] == i, 1, 0\n", "    )\n", "    temp_df_agg[\"Avg utilization above 110% flag\"] = np.where(\n", "        temp_df_agg[\"Avg utilization above 110%\"] == i, 1, 0\n", "    )\n", "    temp_df_agg[\"Avg utilization below 80% flag\"] = np.where(\n", "        temp_df_agg[\"Avg utilization below 80%\"] == i, 1, 0\n", "    )\n", "\n", "    pb.to_sheets(temp_df_agg, \"1CNtCEfH8HYUhJuZxJDwcLt58aOnCqwzuU5rY1zNSwfo\", f\"day_{i}\")\n", "    loop_final = (\n", "        temp_df_agg.groupby(\"final_storage_type\")\n", "        .agg(\n", "            {\n", "                \"Max utilization above 110% flag\": \"sum\",\n", "                \"Max utilization below 80% flag\": \"sum\",\n", "                \"Avg utilization above 110% flag\": \"sum\",\n", "                \"Avg utilization below 80% flag\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "        .rename(\n", "            columns={\n", "                \"Max utilization above 110% flag\": \"Max utilization above 110%\",\n", "                \"Max utilization below 80% flag\": \"Max utilization below 80%\",\n", "                \"Avg utilization above 110% flag\": \"Avg utilization above 110%\",\n", "                \"Avg utilization below 80% flag\": \"Avg utilization below 80%\",\n", "            }\n", "        )\n", "    )\n", "    loop_final[\"Consecutive day count\"] = i\n", "    loop_final_regular = loop_final[loop_final[\"final_storage_type\"].isin([\"REGULAR\"])]\n", "    loop_final_heavy = loop_final[loop_final[\"final_storage_type\"].isin([\"HEAVY\"])]\n", "    regular_df = regular_df.append(\n", "        loop_final_regular[\n", "            [\n", "                \"Consecutive day count\",\n", "                \"Max utilization above 110%\",\n", "                \"Avg utilization above 110%\",\n", "                \"Max utilization below 80%\",\n", "                \"Avg utilization below 80%\",\n", "            ]\n", "        ]\n", "    )\n", "    heavy_df = heavy_df.append(\n", "        loop_final_heavy[\n", "            [\n", "                \"Consecutive day count\",\n", "                \"Max utilization above 110%\",\n", "                \"Avg utilization above 110%\",\n", "                \"Max utilization below 80%\",\n", "                \"Avg utilization below 80%\",\n", "            ]\n", "        ]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "d4f4a067-c79c-4381-9ba8-97fab199728f", "metadata": {}, "outputs": [], "source": ["# regular_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9d2bd9ab-ab2e-4f74-9b5a-7550a79cf469", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(regular_df, header_columns=0)\n", "fig.savefig(\"Consective days in a given utilisation bucket in regular storage type.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "82b6e295-45c2-4a70-9e53-2ab4b9350d5a", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(heavy_df, header_columns=0)\n", "fig.savefig(\"Consective days in a given utilisation bucket in heavy storage type.png\")"]}, {"cell_type": "markdown", "id": "9906d1e6-2aa2-4af2-91cb-8974b34ea9a3", "metadata": {"tags": []}, "source": ["##### Critical stores "]}, {"cell_type": "code", "execution_count": null, "id": "3807d047-0c7d-499d-95ef-8f336366b673", "metadata": {}, "outputs": [], "source": ["def fetch_current_util():\n", "    sql = f\"\"\"\n", "        With fo as (\n", "                with\n", "                outlets as \n", "                    (select \n", "                        om.facility_id, pf.internal_facility_identifier as facility_name, wf.name as warehouse_name,\n", "                        om.outlet_id as hot_outlet_id, om.outlet_name as hot_outlet_name,\n", "                        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "                        rcl.name as city_name,\n", "                        bt.name as business_type_name,\n", "                        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "\n", "                            from po.physical_facility_outlet_mapping om\n", "\n", "                                join \n", "                                    retail.console_outlet rco on rco.id = om.outlet_id \n", "\n", "                                join\n", "                                    retail.console_business_type bt on bt.id = rco.business_type_id\n", "\n", "                                left join \n", "                                    po.physical_facility pf on pf.facility_id = om.facility_id\n", "\n", "                                left join \n", "                                    retail.warehouse_facility wf on wf.id = om.facility_id\n", "\n", "                                left join \n", "                                    (select distinct warehouse_id, cloud_store_id \n", "                                        from retail.warehouse_outlet_mapping where active = 1\n", "                                    ) wom on wom.warehouse_id = om.outlet_id\n", "\n", "                                left join \n", "                                    retail.console_location rcl on rcl.id = rco.tax_location_id\n", "\n", "                                    where rco.business_type_id in (1,12,7,19,20,21)\n", "                                        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                                        and om.outlet_name not like '%%SSC%%'\n", "                                        and om.outlet_name not like '%%MODI%%'\n", "                                        and om.outlet_name not like '%%hot ff%%'\n", "                                        and om.lake_active_record\n", "                                        and rcl.lake_active_record\n", "                                        and wf.lake_active_record\n", "\n", "                                        order by (case when rco.business_type_id in (1,12) then 1\n", "                                            when rco.business_type_id in (19) then 2\n", "                                            when rco.business_type_id in (20) then 3 else 4 end), om.facility_id\n", "                    )\n", "\n", "                        select \n", "                            facility_id,\n", "                            hot_outlet_name as outlet_name,\n", "                            inv_outlet_id as outlet_id,\n", "                            taggings\n", "                        from outlets\n", "                        where taggings = 'fe'),\n", "\n", "                inv as (\n", "                select \n", "                    fo.outlet_id,\n", "                    inv.facility_id,\n", "                    storage_type,\n", "                    base_storage_type,\n", "                    inventory_quantity,\n", "                    store_quantity,\n", "                    orders_consume_quantity,\n", "                    in_transit_quantity,\n", "                    adjusted_inventory_quantity,\n", "                    adjusted_store_quantity,\n", "                    adjusted_orders_consume_quantity,\n", "                    adjusted_in_transit_quantity\n", "                from\n", "                    ars.physical_facility_inventory_quantity inv\n", "                inner join\n", "                    fo\n", "                on\n", "                    fo.facility_id = inv.facility_id\n", "                and\n", "                    inv.lake_active_record),\n", "\n", "                storage as (\n", "                    select \n", "                        distinct\n", "                        pfsc.facility_id,\n", "                        pfsc.facility_name,\n", "                        pfsc.storage_type,\n", "                        pfsc.base_storage_type,\n", "                        pfsc.threshold,\n", "                        pfsc.storage_capacity as storage_cap\n", "                    from\n", "                        lake_ars.physical_facility_storage_capacity pfsc\n", "                    inner join\n", "                        (\n", "                            select \n", "                                distinct\n", "                                facility_id,\n", "                                storage_type,\n", "                                base_storage_type,\n", "                                max(updated_at) as updated_at_\n", "                            from \n", "                                lake_ars.physical_facility_storage_capacity \n", "                            group by 1,2,3\n", "                        ) sb2 on sb2.facility_id = pfsc.facility_id\n", "                                and sb2.storage_type = pfsc.storage_type\n", "                                and sb2.base_storage_type = pfsc.base_storage_type\n", "                                and sb2.updated_at_ = pfsc.updated_at\n", "                    where active \n", "                    and pfsc.lake_active_record\n", "                ),\n", "\n", "                inv_agg as (\n", "                        select\n", "                            inv.*,\n", "                            s.facility_name,\n", "                            case when s.storage_type is null then inv.base_storage_type\n", "                                else s.storage_type end as final_storage_type\n", "                        from\n", "                            inv\n", "                        inner join\n", "                            fo\n", "                        on\n", "                            fo.facility_id = inv.facility_id\n", "                        left join\n", "                            storage s\n", "                        on\n", "                            s.facility_id = inv.facility_id\n", "                        and\n", "                            s.storage_type = inv.storage_type\n", "                )\n", "                ,\n", "\n", "                final_agg as (\n", "                    select\n", "                        ia.outlet_id,\n", "                        facility_id,\n", "                        final_storage_type,\n", "                        max(ia.facility_name) as facility_name,\n", "                        sum(inventory_quantity) as inv_qty,\n", "                        sum(store_quantity) as store_qty,\n", "                        sum(orders_consume_quantity) as ocq,\n", "                        sum(in_transit_quantity) as itq,\n", "                        sum(adjusted_inventory_quantity) as adj_inv_qty,\n", "                        sum(adjusted_store_quantity) as adj_store_qty,\n", "                        sum(adjusted_orders_consume_quantity) as adj_ocq,\n", "                        sum(adjusted_in_transit_quantity) as adj_itq\n", "                    from\n", "                        inv_agg ia\n", "                    -- where\n", "                    --     facility_id = 1632\n", "                    group by\n", "                        1,2,3\n", "\n", "                )\n", "\n", "\n", "                select\n", "                    fa.outlet_id,\n", "                    fa.facility_id,\n", "                    fa.facility_name,\n", "                    fa.final_storage_type as storage_type,\n", "                    s.threshold,\n", "                    CAST(s.storage_cap as REAL) as storage_cap,\n", "                    CAST(fa.store_qty AS REAL) as onshelf_inventory,\n", "                    CAST(fa.itq AS REAL) as in_transit_quantity,\n", "                    CAST(fa.adj_store_qty AS REAL) as scaled_onshelf_inventory,\n", "                    CAST(fa.adj_itq AS REAL) as scaled_open_po_qty,\n", "                    100.0*CAST(fa.adj_store_qty AS REAL)/CAST(s.storage_cap as REAL) as current_on_shelf_utilisation,\n", "                    100.0*(CAST(fa.adj_store_qty AS REAL)+CAST(fa.adj_itq AS REAL) )/CAST(s.storage_cap as REAL) as utilisation_with_open_stos\n", "                from\n", "                    storage s\n", "                inner join\n", "                    final_agg fa \n", "                on\n", "                    fa.facility_id = s.facility_id\n", "                and\n", "                    fa.final_storage_type = s.storage_type\n", "                where\n", "                    s.storage_cap>0\n", "                order by \n", "                   facility_name\n", "                -- limit 10\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=pb.get_connection(\"[Warehouse] Trino\"))\n", "\n", "\n", "current_util = fetch_current_util()\n", "print(current_util.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "edd7ad73-4040-4e91-a43c-fe658c98f94f", "metadata": {}, "outputs": [], "source": ["current_util.columns"]}, {"cell_type": "code", "execution_count": null, "id": "f7f999b7-9d85-4490-882f-cf13bea778de", "metadata": {}, "outputs": [], "source": ["current_util = current_util[\n", "    [\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"current_on_shelf_utilisation\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "c3297040-a8a4-4a26-a791-52166c3bbaad", "metadata": {}, "outputs": [], "source": ["critical_stores = current_util[\n", "    (current_util[\"storage_type\"].isin([\"REGULAR\", \"HEAVY\"]))\n", "    & (current_util[\"current_on_shelf_utilisation\"] > 120)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "bfe73bf6-e1d4-4d41-99ae-0821ad59b7b2", "metadata": {}, "outputs": [], "source": ["critical_stores = critical_stores.sort_values(by=\"current_on_shelf_utilisation\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "22bbb0c3-045b-4774-90a1-4b859cf3a271", "metadata": {}, "outputs": [], "source": ["critical_stores = critical_stores.rename(\n", "    columns={\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"facility_id\": \"Facility ID\",\n", "        \"storage_type\": \"Storage type\",\n", "        \"current_on_shelf_utilisation\": \"Current on-shelf utilisation\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3ea2e546-44b9-4911-8015-9a9e6871e5b5", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(critical_stores, header_columns=0)\n", "fig.savefig(\"Stores with critical utilisation.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "a8cf7873-a96d-4fbb-bfa5-56461b6ded9d", "metadata": {}, "outputs": [], "source": ["base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "86c90c1c-d775-4867-9126-c12989ce7c95", "metadata": {}, "outputs": [], "source": ["### Avg Utilisation above 100% yesterday\n", "avg_util = base_df[\n", "    (base_df[\"avg_osh_util\"] >= 105) & (base_df[\"date_\"] > (datetime.now() - <PERSON><PERSON><PERSON>(days=2)))\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "315ba788-8694-4265-adc9-54cfa1f4adb1", "metadata": {}, "outputs": [], "source": ["avg_util.columns"]}, {"cell_type": "code", "execution_count": null, "id": "3808d51f-ef9a-4da2-b5f9-7aa73a303b63", "metadata": {}, "outputs": [], "source": ["avg_util_trend = (\n", "    avg_util[\n", "        [\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"final_storage_type\",\n", "            \"current_threshold\",\n", "            \"avg_threshold\",\n", "            \"max_osh_util\",\n", "            \"avg_osh_util\",\n", "        ]\n", "    ]\n", "    .sort_values(by=\"avg_osh_util\", ascending=False)\n", "    .head(20)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e8cbe9f0-107b-410b-8253-97568662cd0e", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(avg_util_trend, header_columns=0)\n", "fig.savefig(\"Stores with average utilisation.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "9c60762f-b377-432d-80ae-c77c04e196d0", "metadata": {}, "outputs": [], "source": ["text_req = (\n", "    \"Storage utilisation alert for: \"\n", "    + datetime.now().strftime(\"%Y-%m-%d\")\n", "    + \"\\n\"\n", "    + \"For store level summary please refer this <https://docs.google.com/spreadsheets/d/1CNtCEfH8HYUhJuZxJDwcLt58aOnCqwzuU5rY1zNSwfo/edit#gid=1900249877| sheet>\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5fffe3ac-1105-4f60-8e20-397f04c0e1ac", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(channel=\"bl-solving-storage-cap\", text=text_req)"]}, {"cell_type": "code", "execution_count": null, "id": "0242f970-0286-45f5-b114-caa6bee5338d", "metadata": {}, "outputs": [], "source": ["### Regular utilisation\n", "pb.send_slack_message(\n", "    channel=\"bl-solving-storage-cap\",\n", "    text=\"Count of overutilised and underutilised stores for REGULAR Storage type in last 7 days\",\n", "    files=[\"Utilisation trend for regular storage type.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "193271c2-8abb-4f24-bc22-18d9e94db735", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-solving-storage-cap\",\n", "    text=\"Count of overutilised and underutilised stores for HEAVY Storage type in last 7 days\",\n", "    files=[\"Utilisation trend for heavy storage type.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0a0a9ae6-f0b4-4d51-949f-8c904d0e9744", "metadata": {}, "outputs": [], "source": ["### Regular utilisation\n", "pb.send_slack_message(\n", "    channel=\"bl-solving-storage-cap\",\n", "    text=\"Count of stores that are continously overutilised or underutilised for REGULAR Storage type\",\n", "    files=[\"Consective days in a given utilisation bucket in regular storage type.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9e6e8454-f4a3-4537-899e-f6de1639e520", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-solving-storage-cap\",\n", "    text=\"Count of stores that are continously overutilised or underutilised for HEAVY Storage type\",\n", "    files=[\"Consective days in a given utilisation bucket in heavy storage type.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5d5a92e5-b9be-4b85-a91a-83b58c39ad53", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-solving-storage-cap\",\n", "    text=\"List of critical stores attached below, please refer <https://reports.grofer.io/dashboard/storage_tracker| this query> for detailed information\",\n", "    files=[\"Stores with critical utilisation.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c4f92261-6fc4-4837-ac3f-f1dda88a3547", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-solving-storage-cap\",\n", "    text=\"List of critical stores with average overutilisation attached below, please refer <https://reports.grofer.io/dashboard/storage_tracker| this query> for detailed information\",\n", "    files=[\"Stores with average utilisation.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2ba85ffb-8dc9-4a00-ac4d-ce3360216eab", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}