{"cells": [{"cell_type": "code", "execution_count": null, "id": "0b5f8d0e-a661-4b9d-ae16-e63246388629", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import math\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# from matplotlib import pyplot as plt\n", "import io\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "a1fb5ea3-4d5e-472e-84db-c987e40adb53", "metadata": {}, "outputs": [], "source": ["def carts_prediction():\n", "    sql = \"\"\"\n", "            select outletid as outlet_id,date,carts from metrics.cart_projections\n", "            where updated_on = (SELECT max(updated_on) FROM metrics.cart_projections)\n", "            and date > current_date - 6\n", "            order by outlet_id, date\n", "            \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "0fa2096d-fdcc-4739-8445-57d05ecb3c9e", "metadata": {}, "outputs": [], "source": ["df_prediction = carts_prediction()"]}, {"cell_type": "code", "execution_count": null, "id": "dd804c95-6548-4a59-981d-6e95a4bde26e", "metadata": {}, "outputs": [], "source": ["df_prediction[\"outlet_id\"] = df_prediction[\"outlet_id\"].astype(int)\n", "df_prediction"]}, {"cell_type": "code", "execution_count": null, "id": "a09df2c2-5904-430f-b9a0-76e79c3dd3b3", "metadata": {}, "outputs": [], "source": ["def actual_carts():\n", "    sql_2 = \"\"\"with main as(SELECT \n", "            date(od.created_at + interval '5.5 Hours') AS date,\n", "                  co.id as outlet_id,\n", "                   count(DISTINCT ancestor)\n", "                --   case when icd.l0_id in (1487) then 'fnv'  \n", "                --   case when icd.l2_id in ()\n", "                --   sum(oi.quantity) as qty_sold,\n", "                --   sum(CASE\n", "                --           WHEN price IS NULL THEN 0\n", "                --           ELSE oi.quantity*price\n", "                --       END) AS GMV\n", "            FROM lake_ims.ims_order_details od\n", "            LEFT JOIN lake_ims.ims_order_items oi ON od.id = oi.order_details_id\n", "            LEFT JOIN lake_ims.ims_order_actuals oa ON od.id = oa.order_details_id\n", "            AND oi.item_id=oa.item_id\n", "            INNER JOIN lake_retail.console_outlet co ON co.id = od.outlet\n", "            LEFT JOIN lake_rpc.item_category_details icd on oi.item_id = icd.item_id\n", "            AND business_type_id = 7 and active = 1\n", "            WHERE status_id IN (1,2)\n", "              AND od.created_at between current_date- 6 and current_date\n", "              AND co.business_type_id = 7\n", "            GROUP BY 1,2\n", "            order by outlet_id,date)\n", "            \n", "            select * from main\n", "            where date < current_date\n", "            \n", "            \"\"\"\n", "    return pd.read_sql_query(sql=sql_2, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "39b0d6eb-df9f-434d-bdaf-5fd49e581a3b", "metadata": {}, "outputs": [], "source": ["df_carts = actual_carts()"]}, {"cell_type": "code", "execution_count": null, "id": "ad785c56-bf79-40d8-831c-a5081a12fc8d", "metadata": {}, "outputs": [], "source": ["df_carts.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "52bec3da-a4a4-46e5-9e94-11223ff1a036", "metadata": {}, "outputs": [], "source": ["df_comparison = pd.merge(df_carts, df_prediction, how=\"left\", on=[\"outlet_id\", \"date\"])\n", "df_comparison[\"carts\"] = df_comparison[\"carts\"].replace(r\"\", np.NaN)\n", "df_comparison[\"carts\"] = df_comparison[\"carts\"].fillna(\"na\")\n", "df_comparison.to_csv(\"test.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "47451994-3c63-4df2-b83b-82c24d3d30b6", "metadata": {}, "outputs": [], "source": ["df_comparison[\"carts\"] = np.where(\n", "    df_comparison[\"carts\"] == \"na\", df_comparison[\"count\"], df_comparison[\"carts\"]\n", ")\n", "df_comparison[\"carts\"] = df_comparison[\"carts\"].astype(float)\n", "df_comparison[\"carts\"] = df_comparison[\"carts\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "17730c84-7eae-4fa7-bb5f-8b9afacdba62", "metadata": {}, "outputs": [], "source": ["df_comparison.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e2655b8b-b29e-46c9-877f-a4ffc1a42336", "metadata": {}, "outputs": [], "source": ["df_comparison[\"delta\"] = (df_comparison[\"carts\"] - df_comparison[\"count\"]) / df_comparison[\"count\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1471b88a-d9ec-41bd-975c-e372161ed32e", "metadata": {}, "outputs": [], "source": ["df_comparison[\"baseline_shift_required\"] = np.where(\n", "    (df_comparison[\"delta\"] > 0.10) | (df_comparison[\"delta\"] < -0.10), 1, 0\n", ")\n", "# df_comparison['baseline_shift_required'] = np.where((df_comparison['delta'] > 0.10), 1,0)"]}, {"cell_type": "code", "execution_count": null, "id": "6d9e9b08-7974-4f5a-b0e8-2e0d1b69c561", "metadata": {}, "outputs": [], "source": ["df_comparison[\"date\"] = pd.to_datetime(df_comparison[\"date\"])\n", "df_comparison[\"rnk\"] = np.where(\n", "    df_comparison[\"date\"].astype(str)\n", "    == (pd.to_datetime(datetime.today() + timedelta(hours=5.5) - timedelta(days=1))).strftime(\n", "        \"%Y-%m-%d\"\n", "    ),\n", "    5,\n", "    np.where(\n", "        df_comparison[\"date\"].astype(str)\n", "        == (pd.to_datetime(datetime.today() + timedelta(hours=5.5) - timedelta(days=2))).strftime(\n", "            \"%Y-%m-%d\"\n", "        ),\n", "        4,\n", "        np.where(\n", "            df_comparison[\"date\"].astype(str)\n", "            == (\n", "                pd.to_datetime(datetime.today() + timedelta(hours=5.5) - timedelta(days=3))\n", "            ).strftime(\"%Y-%m-%d\"),\n", "            3,\n", "            np.where(\n", "                df_comparison[\"date\"].astype(str)\n", "                == (\n", "                    pd.to_datetime(datetime.today() + timedelta(hours=5.5) - timedelta(days=4))\n", "                ).strftime(\"%Y-%m-%d\"),\n", "                2,\n", "                1,\n", "            ),\n", "        ),\n", "    ),\n", ")\n", "\n", "df_comparison[[\"rnk\", \"date\", \"rnk\"]].drop_duplicates()\n", "df_comparison.head(7)\n", "# df_comparison"]}, {"cell_type": "code", "execution_count": null, "id": "ff0a8121-5bb7-496b-b824-43977698a59a", "metadata": {}, "outputs": [], "source": ["df_comparison[df_comparison[\"outlet_id\"] == 2735]"]}, {"cell_type": "code", "execution_count": null, "id": "eb823e39-06cd-44e9-88f2-15492208c61c", "metadata": {}, "outputs": [], "source": ["# df_comparison.to_csv(\"delta_check.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "bcb49479-7703-43cb-9737-ec889f4a5f08", "metadata": {}, "outputs": [], "source": ["df_comparison[\"weighted_shift_required\"] = (\n", "    df_comparison[\"baseline_shift_required\"] * df_comparison[\"rnk\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a094b09b-92d4-4009-b710-f88a205e014d", "metadata": {}, "outputs": [], "source": ["df_comparison_agg = (\n", "    df_comparison.groupby([\"outlet_id\"])\n", "    .agg({\"weighted_shift_required\": \"sum\", \"delta\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "73973fc7-8e80-492b-b68f-d6ab864a01d0", "metadata": {}, "outputs": [], "source": ["df_comparison_agg = df_comparison_agg[df_comparison_agg[\"weighted_shift_required\"] > 8]"]}, {"cell_type": "code", "execution_count": null, "id": "96414792-a30d-41f9-ba28-f2238d7b7265", "metadata": {}, "outputs": [], "source": ["df_comparison_agg.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9ef90be9-fcb5-40ee-a149-12deb66692d4", "metadata": {}, "outputs": [], "source": ["baseline_outlet_list = tuple(df_comparison_agg[\"outlet_id\"].unique())\n", "base_list = df_comparison_agg[\"outlet_id\"].unique().astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "4da0f241-3410-4f71-b373-2907ebc61f94", "metadata": {}, "outputs": [], "source": ["def carts_prediction_baseline(baseline_outlet_list):\n", "    sql = f\"\"\"\n", "            select outletid as outlet_id,date,carts from metrics.cart_projections\n", "            where updated_on = (SELECT max(updated_on) FROM metrics.cart_projections)\n", "            and date > current_date - 6\n", "            and date < current_date + 7\n", "            and outlet_id in {baseline_outlet_list}\n", "            order by outlet_id, date\n", "            \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "1a9400d5-3c53-48cb-ac9d-44d0ab065cac", "metadata": {}, "outputs": [], "source": ["df_baseline_prediction = carts_prediction_baseline(baseline_outlet_list)\n", "df_baseline_prediction[[\"outlet_id\", \"carts\"]] = df_baseline_prediction[\n", "    [\"outlet_id\", \"carts\"]\n", "].astype(float)\n", "df_baseline_prediction[[\"outlet_id\", \"carts\"]] = df_baseline_prediction[\n", "    [\"outlet_id\", \"carts\"]\n", "].astype(int)\n", "df_baseline_prediction[\"date\"] = pd.to_datetime(df_baseline_prediction[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "50d79671-42b5-4c0a-9707-ca915dc42b89", "metadata": {}, "outputs": [], "source": ["def cal(outlets):\n", "    # for outlets in base_list:\n", "    df_baseline_prediction_1 = df_baseline_prediction[\n", "        df_baseline_prediction[\"outlet_id\"] == outlets\n", "    ]\n", "    df_baseline_prediction_date = df_baseline_prediction_1.copy()\n", "    df_baseline_prediction_date[\"new_date\"] = df_baseline_prediction_1[\"date\"] + pd.Timedelta(\n", "        days=1\n", "    )\n", "    df_prediction_delta = df_baseline_prediction_date[[\"new_date\", \"outlet_id\", \"carts\"]]\n", "    df_prediction_delta.rename(\n", "        columns={\"new_date\": \"date\", \"carts\": \"earlier_day_carts\"}, inplace=True\n", "    )\n", "    df_trendline = pd.merge(\n", "        df_baseline_prediction_1,\n", "        df_prediction_delta,\n", "        how=\"left\",\n", "        on=[\"outlet_id\", \"date\"],\n", "    )\n", "    df_trendline[\"trend_percentage\"] = (\n", "        df_trendline[\"carts\"] - df_trendline[\"earlier_day_carts\"]\n", "    ) / df_trendline[\"carts\"]\n", "\n", "    df_trendline[\"trend_percentage\"] = np.where(\n", "        df_trendline[\"trend_percentage\"] > 0.25,\n", "        0.25 + ((df_trendline[\"trend_percentage\"] - 0.25) / 5),\n", "        df_trendline[\"trend_percentage\"],\n", "    )\n", "    df_trendline[\"trend_percentage\"] = np.where(\n", "        df_trendline[\"trend_percentage\"] < -0.25,\n", "        -0.25,\n", "        df_trendline[\"trend_percentage\"],\n", "    )\n", "\n", "    df_carts[\"date\"] = pd.to_datetime(df_carts[\"date\"])\n", "    df_trend_add = pd.merge(df_trendline, df_carts, how=\"left\", on=[\"outlet_id\", \"date\"])\n", "    df = df_trend_add.copy()\n", "    # print(df_trend_add)\n", "    for x in range(7):\n", "        df[\"count\"] = df[\"count\"].fillna(method=\"ffill\", limit=1)\n", "        df[\"count\"] = np.where(\n", "            df[\"date\"].astype(str)\n", "            == (pd.to_datetime(datetime.today() + timedelta(days=x))).strftime(\"%Y-%m-%d\"),\n", "            df[\"count\"] * (1 + df[\"trend_percentage\"]),\n", "            df[\"count\"],\n", "        )\n", "        df[\"count\"] = df[\"count\"].apply(np.ceil)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "6c184f73-2174-420c-b71e-0df45fbfbf39", "metadata": {}, "outputs": [], "source": ["df_0 = pd.DataFrame()\n", "final_df = pd.DataFrame()\n", "for outlets in base_list:\n", "    df_0 = cal(outlets)\n", "    final_df = pd.concat([final_df, df_0])"]}, {"cell_type": "code", "execution_count": null, "id": "6f4e1058-857f-42bd-a456-e0c17a705e98", "metadata": {}, "outputs": [], "source": ["# final_df.to_csv(\"final_data.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "43a03d6f-97fe-4548-98f9-f6d2dcb21ac9", "metadata": {}, "outputs": [], "source": ["final_df.rename(columns={\"count\": \"corrected_carts\"}, inplace=True)\n", "final_df"]}, {"cell_type": "code", "execution_count": null, "id": "29e933a3-88b2-40b9-93f4-f6cb226e0638", "metadata": {}, "outputs": [], "source": ["df_carts_correction = final_df[[\"outlet_id\", \"date\", \"corrected_carts\"]]\n", "df_carts_correction[\"updated_at\"] = datetime.today() + timed<PERSON>ta(hours=5.5)\n", "df_carts_correction"]}, {"cell_type": "code", "execution_count": null, "id": "ce8ee98e-d4ff-4878-bd54-ea89100a549f", "metadata": {}, "outputs": [], "source": ["# Creating Table for logs\n", "daily_carts_prediction_correction = [\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"Outlet ID\"},\n", "    {\"name\": \"date\", \"type\": \"datetime\", \"description\": \"Date\"},\n", "    {\n", "        \"name\": \"corrected_carts\",\n", "        \"type\": \"float\",\n", "        \"description\": \"For Date > T-1, Corrected Predicted Carts\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"datetime\", \"description\": \"Carts Correction Date\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"carts_prediction_correction\",\n", "    \"column_dtypes\": daily_carts_prediction_correction,\n", "    \"primary_key\": [\"outlet_id\", \"date\"],\n", "    \"sortkey\": [\"outlet_id\", \"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Carts Correction for outlets having deviation greater than 10 percent\",  # Description of the table being sent to redshift\n", "}\n", "pb.to_redshift(df_carts_correction, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "1a225534-1b12-4142-ae56-2496aca514aa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0567888d-5be6-43f3-b6a3-3a3aaab619a9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7264f1dc-fcfd-4fb5-901d-6fcd79f88088", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "09b4924f-4960-495d-8724-6dc1d8e8b53f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}