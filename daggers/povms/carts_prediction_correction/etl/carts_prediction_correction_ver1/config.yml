alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: carts_prediction_correction_ver1
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03R8Q6UZ71
path: povms/carts_prediction_correction/etl/carts_prediction_correction_ver1
paused: true
pool: povms_pool
project_name: carts_prediction_correction
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 0 * * *
  start_date: '2023-01-24T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
