{"cells": [{"cell_type": "code", "execution_count": null, "id": "722c8d84-fdaf-4fbb-8897-567ba48f1630", "metadata": {}, "outputs": [], "source": ["# !pip install pandas"]}, {"cell_type": "code", "execution_count": null, "id": "bf842d0e-d652-4854-ab70-16d97d87a2a7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "from tqdm import tqdm\n", "import json\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "\n", "# Connect to data warehouse\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "config_sheet_id = \"11OewoYLWa7Y00A0uKcFswXKS3J8T6P79Dh3YrKW13pk\"\n", "\n", "### Default values\n", "debug = False\n", "default_qmax = 1\n", "default_quantity = 3\n", "default_doi = 6\n", "default_item_factor = 1\n", "default_rtv_sor_flag = \"No\"\n", "default_asp = 122\n", "default_margin = 0.22\n", "default_cpd = 0.01\n", "exclude_substate = tuple([9, 81])\n", "default_intransit_tat = 8\n", "minimum_doi = 4\n", "minimum_quantity = 1\n", "buffer_doi = 14\n", "\n", "### Cost parameters\n", "average_rent = 200000\n", "daily_rental = average_rent / 30\n", "average_storage_capacity = 50000\n", "storage_cost_per_if = daily_rental / average_storage_capacity\n", "ir = 0.12 / 365\n", "truncation_lookback_days = 8\n", "minimum_storage_truncation_contribution = 0.05\n", "rtv_contribution_perc = 0.2\n", "min_cpd_of_unavailable_skus = 0.01\n", "sku_cutoff_on_potential_sales_of_unavail_skus = 0.85\n", "\n", "# sku_cutoff_on_potential_sales_of_unaval_suks\n", "opportunity_cost_multiplier = 1\n", "default_asp_lp_conversion_factor = 0.8\n", "default_asp_margin_conversion_factor = 0.2\n", "minimum_intransit_loss_value = 0.03\n", "\n", "### RSTO exclusion parameters\n", "minimum_avail_day_count_check = 15"]}, {"cell_type": "code", "execution_count": null, "id": "362402c4-7e01-49f6-a30e-7a17e1c826c5", "metadata": {}, "outputs": [], "source": ["def fetch_facility_config():\n", "    \"\"\"Retrieve facility configuration settings.\"\"\"\n", "    if debug:\n", "        # Load configuration from local CSV (for debugging)\n", "        return pd.read_csv(\"RSTO cost function config - facility_config.csv\")\n", "    else:\n", "        # Load configuration from Google Sheet\n", "        return pb.from_sheets(config_sheet_id, \"facility_config\")\n", "\n", "\n", "# Fetch the configuration of facilities\n", "facility_config = fetch_facility_config()\n", "facility_config = facility_config.astype(\n", "    {\n", "        \"backend_facility_id\": int,\n", "        \"create_rsto\": int,\n", "        \"risk_reward_threshold\": int,\n", "        \"inward_capacity_quantity\": int,\n", "        \"inward_capacity_sku\": int,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4f00e078-1b1e-46c2-85be-9c006022870b", "metadata": {}, "outputs": [], "source": ["# facility_config['backend_facility_id'] = 264\n", "# facility_config= facility_config.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "120805e8-4411-449f-bb47-4fbeba417221", "metadata": {}, "outputs": [], "source": ["facility_config"]}, {"cell_type": "code", "execution_count": null, "id": "636eebbf-331e-4a97-9933-42ea4f150b5f", "metadata": {}, "outputs": [], "source": ["def fetch_outlet_list():\n", "    \"\"\"Get all active outlets for BE facilities (business_type_id = 7).\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT facility_id, outlet_id, pfom.city_id, outlet_name, cl.name AS city\n", "        FROM lake_po.physical_facility_outlet_mapping pfom\n", "        LEFT JOIN retail.console_location cl ON cl.id = pfom.city_id\n", "        WHERE pfom.active = 1 AND pfom.ars_active = 1\n", "          AND facility_id IN (\n", "                SELECT facility_id FROM lake_retail.console_outlet\n", "                WHERE business_type_id = 7\n", "          )\n", "        GROUP BY 1,2,3,4,5\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_outlet_age():\n", "    \"\"\"Categorize outlets by number of days with at least 5 orders in last year.\"\"\"\n", "    sql = f\"\"\"\n", "        WITH sales AS (\n", "            SELECT outlet_id, DATE(cart_checkout_ts_ist) AS date_, COUNT(DISTINCT cart_id) AS cart_count\n", "            FROM dwh.fact_sales_order_details\n", "            WHERE order_create_dt_ist > current_date - interval '365' day\n", "            GROUP BY 1, 2\n", "        ),\n", "        live_day_count AS (\n", "            SELECT outlet_id, COUNT(date_) AS live_day_count\n", "            FROM sales\n", "            WHERE cart_count >= 5\n", "            GROUP BY 1\n", "        )\n", "        SELECT\n", "            outlet_id, live_day_count,\n", "            CASE\n", "                WHEN live_day_count < 30 THEN '30_day'\n", "                WHEN live_day_count >= 30 AND live_day_count < 60 THEN '30_60_day'\n", "                WHEN live_day_count >= 60 AND live_day_count < 120 THEN '60_120_day'\n", "                WHEN live_day_count >= 120 THEN 'more_than_120_day'\n", "            END AS live_day_count_bucket\n", "        FROM live_day_count\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_ars_slot_config(backend_facility_id):\n", "    \"\"\"Retrieve reverse slot configuration for a given BE facility.\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT backend_facility_id, slot_config\n", "        FROM ars.auto_sto_slot_config\n", "        WHERE active = 1\n", "          AND backend_facility_id = {backend_facility_id}\n", "          AND mode = 'reverse'\n", "          AND lake_active_record\n", "    \"\"\"\n", "    df = pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "    return df\n", "\n", "\n", "def fetch_outlet_exclusion_config():\n", "    \"\"\"Fetch list of frontend outlets to exclude from RSTO by backend facility.\"\"\"\n", "    if debug:\n", "        return pd.read_csv(\"RSTO cost function config - exclude_outlet_from_rsto.csv\")\n", "    else:\n", "        df = pb.from_sheets(config_sheet_id, \"exclude_outlet_from_rsto\")\n", "        df = df.astype({\"backend_facility_id\": int, \"frontend_outlet_id\": int})\n", "        return df\n", "\n", "\n", "def fetch_inv(backend_facility_id, outlet_list_tuple):\n", "    \"\"\"Fetch current inventory and assortment details for specified BE facility and outlets.\"\"\"\n", "    sql = f\"\"\"\n", "        WITH fo AS (\n", "            SELECT facility_id, outlet_id, city_id, outlet_name\n", "            FROM lake_po.physical_facility_outlet_mapping\n", "            WHERE active = 1 AND ars_active = 1\n", "              AND facility_id IN (\n", "                  SELECT facility_id FROM lake_retail.console_outlet WHERE business_type_id = 7\n", "              )\n", "            GROUP BY 1,2,3,4\n", "        ),\n", "        item_outlet_base AS (\n", "            SELECT\n", "                fo.outlet_id,\n", "                pfma.facility_id,\n", "                fo.outlet_name,\n", "                pfma.item_id,\n", "                pfma.master_assortment_substate_id,\n", "                pfma.substate_reason_id,\n", "                COALESCE(pmasr.reason_type, 'BAU') AS reason_type,\n", "                COALESCE(pmasr.reason_text, 'BAU') AS reason_text,\n", "                COALESCE(iii.quantity, 0) AS quantity\n", "            FROM rpc.product_facility_master_assortment pfma\n", "            INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "            LEFT JOIN rpc.product_master_assortment_substate_reasons pmasr\n", "                ON pmasr.id = pfma.substate_reason_id\n", "                AND pmasr.active = 1 AND pmasr.lake_active_record\n", "            INNER JOIN rpc.item_details id ON id.item_id = pfma.item_id\n", "                AND id.active = 1 AND id.approved = 1\n", "                AND id.perishable != 1\n", "                AND id.handling_type NOT IN ('8', '9')\n", "                AND id.storage_type NOT IN ('2', '3', '6', '7')\n", "            INNER JOIN rpc.item_category_details icd ON icd.item_id = pfma.item_id\n", "                AND icd.l0_id != 1487\n", "            INNER JOIN rpc.item_outlet_tag_mapping iotm ON iotm.outlet_id = fo.outlet_id\n", "                AND iotm.item_id = pfma.item_id AND iotm.active = 1 AND iotm.tag_type_id = 8\n", "                AND iotm.lake_active_record\n", "                AND iotm.tag_value IN (\n", "                    SELECT DISTINCT CAST(outlet_id AS VARCHAR)\n", "                    FROM po.physical_facility_outlet_mapping pfom\n", "                    WHERE pfom.active = 1 AND pfom.ars_active = 1\n", "                      AND pfom.facility_id = {backend_facility_id}\n", "                )\n", "                AND iotm.outlet_id IN {outlet_list_tuple}\n", "            LEFT JOIN (\n", "                SELECT CAST(ii.item_id AS INT) AS item_id,\n", "                       CAST(ii.outlet_id AS INT) AS outlet_id,\n", "                       SUM(quantity - COALESCE(blocked_inv, 0)) AS quantity\n", "                FROM dynamodb.blinkit_store_inventory_service_oi_rt_view ii\n", "                INNER JOIN fo ON fo.outlet_id = CAST(ii.outlet_id AS INT)\n", "                LEFT JOIN (\n", "                    SELECT item_id, outlet_id, SUM(quantity) AS blocked_inv\n", "                    FROM dynamodb.blinkit_store_inventory_service_blk_rt_view\n", "                    WHERE status = 'BLOCKED' AND reference_type NOT IN ('DL_VALIDATION_CRON', 'PNA')\n", "                    GROUP BY 1, 2\n", "                ) ib ON ib.item_id = ii.item_id AND ib.outlet_id = ii.outlet_id\n", "                WHERE ii.state = 'GOOD'\n", "                GROUP BY 1, 2\n", "            ) iii ON iii.item_id = pfma.item_id AND iii.outlet_id = fo.outlet_id\n", "            WHERE pfma.active = 1\n", "              AND (iii.quantity > 0 OR pfma.master_assortment_substate_id NOT IN (2, 4))\n", "              AND pfma.substate_reason_id != 9\n", "        )\n", "        SELECT * FROM item_outlet_base\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_item_exclusions(backend_facility_id):\n", "    \"\"\"Fetch SKUs to exclude from RSTO for given facility (cold/frozen, serialized, etc.).\"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Exclude cold/frozen items\n", "    sql = f\"\"\"\n", "        SELECT DISTINCT item_id, 'COLD_FROZEN' AS exclusion_type\n", "        FROM rpc.item_details\n", "        WHERE storage_type IN ('2', '3', '6', '7')\n", "    \"\"\"\n", "    cold_frozen_df = pd.read_sql_query(sql=sql, con=con)\n", "    print(f\"COLD_FROZEN SKU count {cold_frozen_df.shape[0]}\")\n", "\n", "    # Exclude serialized inventory items\n", "    sql = f\"\"\"\n", "        SELECT DISTINCT item_id, 'SERIALISED_INV' AS exclusion_type\n", "        FROM rpc.item_tag_mapping itm\n", "        WHERE itm.tag_type_id = 5 AND itm.tag_value IN ('2','3')\n", "    \"\"\"\n", "    serialised_inv = pd.read_sql_query(sql=sql, con=con)\n", "    print(f\"SERIALISED_INV SKU count {serialised_inv.shape[0]}\")\n", "\n", "    # Exclude items with no RSTO flag\n", "    sql = f\"\"\"\n", "        WITH base AS (\n", "            SELECT value\n", "            FROM ars.ars_config\n", "            WHERE name = 'no_rsto_items'\n", "        ),\n", "        final AS (\n", "            SELECT CAST(trimmed_value AS BIGINT) AS value\n", "            FROM base, UNNEST(SPLIT(TRIM(BOTH '[]' FROM value), ',')) AS t(trimmed_value)\n", "        )\n", "        SELECT DISTINCT value AS item_id, 'NO_RSTO_SKUS' AS exclusion_type\n", "        FROM final\n", "    \"\"\"\n", "    no_rsto_inv = pd.read_sql_query(sql=sql, con=con)\n", "    print(f\"NO_RSTO_SKUS SKU count {no_rsto_inv.shape[0]}\")\n", "\n", "    # Exclude SKUs with low shelf life (<= 90 days)\n", "    sql = f\"\"\"\n", "        SELECT DISTINCT item_id, 'LOW_SHELF_LIFE' AS exclusion_type\n", "        FROM rpc.item_details\n", "        WHERE active = 1 AND approved = 1 AND shelf_life <= 90\n", "    \"\"\"\n", "    low_shelf_life_inv = pd.read_sql_query(sql=sql, con=con)\n", "    print(f\"LOW_SHELF_LIFE SKU count {low_shelf_life_inv.shape[0]}\")\n", "\n", "    # Exclude SKUs flushed in last 7 days for this facility\n", "    sql = f\"\"\"\n", "        SELECT DISTINCT item_id, 'RECENTLY_FLUSHED_SKU' AS exclusion_type\n", "        FROM ars.flushing_transfer_config ftc\n", "        LEFT JOIN po.physical_facility_outlet_mapping pfom ON pfom.facility_id = ftc.backend_facility_id\n", "        WHERE ftc.for_date BETWEEN current_date - interval '7' day AND current_date + interval '3' day\n", "          AND ftc.partition_field BETWEEN CAST(current_date - interval '7' day AS VARCHAR)\n", "                                      AND CAST(current_date + interval '14' day AS VARCHAR)\n", "          AND ftc.backend_facility_id = {backend_facility_id}\n", "    \"\"\"\n", "    l7_flushed_skus = pd.read_sql_query(sql=sql, con=con)\n", "    print(f\"RECENTLY_FLUSHED_SKU SKU count {l7_flushed_skus.shape[0]}\")\n", "\n", "    # Exclude SKUs from running and upcoming festivals\n", "    sql = f\"\"\"\n", "            SELECT\n", "              DISTINCT item_id,\n", "              'FESTIVE_BLOCKED_INV' as exclusion_type\n", "            --   assortment_launch_type,\n", "            --   event_id,\n", "            --   sale_start_date,\n", "            --   sale_end_date,\n", "            --   bpec.active,\n", "            --   planned_qty\n", "            FROM\n", "              ars.bulk_process_event_planner bpec\n", "              INNER JOIN rpc.supply_event_info ON bpec.event_id = supply_event_info.id\n", "              and supply_event_info.active\n", "            WHERE\n", "              bpec.partition_field is not null\n", "              AND bpec.planned_qty > 0\n", "              AND supply_event_info.lake_active_record\n", "              and current_date between sale_start_date - interval '10' day\n", "              and sale_end_date + interval '3' day\n", "              and bpec.active = 1\n", "        \"\"\"\n", "    festive_blocked_inv = pd.read_sql_query(sql=sql, con=con)\n", "    print(f\"FESTIVE_BLOCKED_INV SKU count\", {festive_blocked_inv.shape[0]})\n", "\n", "    # Combine all exclusion lists\n", "    df = pd.DataFrame(columns=[\"item_id\", \"exclusion_type\"])\n", "    df = pd.concat(\n", "        [\n", "            df,\n", "            cold_frozen_df,\n", "            serialised_inv,\n", "            no_rsto_inv,\n", "            low_shelf_life_inv,\n", "            l7_flushed_skus,\n", "            festive_blocked_inv,\n", "        ],\n", "        ignore_index=True,\n", "    )\n", "    df = df.drop_duplicates()\n", "    print(f\"TOTAL_SKUS_EXCLUDED {df.shape[0]}\")\n", "    return df\n", "\n", "\n", "def fetch_item_outlet_exclusion():\n", "    sql = f\"\"\"\n", "        SELECT\n", "              CAST(item_id AS INT) as item_id,\n", "              CAST(outlet_id AS INT) as outlet_id,\n", "              'ITEM_OUTLET_RSTO_BLOCK' as line_exclusion_type\n", "            FROM\n", "              supply_etls.auto_rsto_blocked_item_outlet\n", "            WHERE\n", "              rsto_block = '1'\n", "\n", "    \"\"\"\n", "    df = pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "    print(f\"TOTAL_ITEM_OUTLET_EXCLUDED {df.shape[0]}\")\n", "    return df\n", "\n", "\n", "def fetch_active_days(outlet_list_tuple):\n", "    \"\"\"Fetch number of active days (with sufficient availability) for each item in last 60 days.\"\"\"\n", "    sql = f\"\"\"\n", "        WITH base AS (\n", "            SELECT doa.item_id, doa.facility_id, pfom.outlet_id, COUNT(doa.available_hours) AS day_count\n", "            FROM ars.daily_orders_and_availability doa\n", "            LEFT JOIN po.physical_facility_outlet_mapping pfom\n", "                ON pfom.facility_id = doa.facility_id AND pfom.active = 1 AND pfom.ars_active = 1\n", "            WHERE doa.available_hours > 6\n", "              AND doa.facility_id IN (\n", "                  SELECT DISTINCT facility_id\n", "                  FROM po.physical_facility_outlet_mapping pfom\n", "                  WHERE pfom.outlet_id IN {outlet_list_tuple}\n", "              )\n", "              AND order_date >= current_date - interval '60' day\n", "              AND doa.lake_active_record\n", "              AND insert_ds_ist >= CAST(current_date - interval '60' day AS VARCHAR)\n", "            GROUP BY 1,2,3\n", "        )\n", "        SELECT * FROM base\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_cpd(outlet_list_tuple, item_list):\n", "    \"\"\"Fetch CPD (consumption per day) and APS for recent days for given outlets and items.\"\"\"\n", "    sql = f\"\"\"\n", "        WITH cpd_base AS (\n", "            SELECT item_id, outlet_id, cpd, aps_adjusted, potential_sale, for_date\n", "            FROM ars.outlet_item_aps_derived_cpd\n", "            WHERE for_date >= current_date - interval '2' day\n", "              AND outlet_id IN {outlet_list_tuple}\n", "              AND item_id IN {item_list}\n", "              AND insert_ds_ist >= CAST(current_date - interval '2' day AS VARCHAR)\n", "        ),\n", "        latest_cpd AS (\n", "            SELECT *,\n", "                   RANK() OVER (PARTITION BY item_id, outlet_id ORDER BY for_date DESC) AS rnk\n", "            FROM cpd_base\n", "        )\n", "        SELECT * FROM latest_cpd WHERE rnk = 1\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_cat():\n", "    \"\"\"Fetch item category tags and derive storage type for each item.\"\"\"\n", "    sql = f\"\"\"\n", "           With base as (\n", "                SELECT DISTINCT id.item_id,\n", "                                id.name,\n", "                                icd.l0,\n", "                                icd.l1,\n", "                                icd.l2,\n", "                                id.weight_in_gm,\n", "                                CASE\n", "                                    WHEN storage_type IN ('1',\n", "                                                          '8') THEN 'REGULAR'\n", "                                    WHEN storage_type IN ('4',\n", "                                                          '5') THEN 'HEAVY'\n", "                                    WHEN storage_type IN ('2',\n", "                                                          '6') THEN 'COLD'\n", "                                    WHEN storage_type IN ('3',\n", "                                                          '6') THEN 'FROZEN'\n", "                                    ELSE 'REGULAR'\n", "                                END AS storage_type,\n", "                                 case when itm.tag_value = '1' then 'BEAUTY'\n", "                                    when itm.tag_value = '2' then 'BOUQUET' \n", "                                    when itm.tag_value = '3' then 'PREMIUM'\n", "                                    when itm.tag_value = '4' then 'BOOKS'\n", "                                    when itm.tag_value = '5' then 'NON_VEG' \n", "                                    when itm.tag_value = '6' then 'ICE_CREAM'\n", "                                    when itm.tag_value = '7' then 'TOYS'\n", "                                    when itm.tag_value = '8' then 'ELECTRONICS'\n", "                                    -- when itm.tag_value = '9' then 'FESTIVE'\n", "                                    when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "                                    when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "                                    when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "                                    when itm.tag_value = '13' then 'LARGE'\n", "                                    when itm.tag_value = '14' then 'APPAREL'\n", "                                    when itm.tag_value = '15' then 'SPORTS'\n", "                                    when itm.tag_value = '16' then 'PET_CARE'\n", "                                    when itm.tag_value = '17' then 'HOME_DECOR'\n", "                                    when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "                                    when itm.tag_value = '19' then 'HOME_FURNISHING'\n", "                                    when itm.tag_value = '20' then 'LONGTAIL_OTHERS'\n", "                                    when itm.tag_value = '21' then 'PHARMA'\n", "                                    when itm.tag_value = '22' then 'PASS'\n", "                                    when itm.tag_value = '23' then 'PALLET'\n", "                                    when itm.tag_value = '24' then 'LARGE_FRAGILE'\n", "                                    else 'NORMAL_GROCERY'\n", "                                end as current_tagging\n", "                FROM rpc.item_details id\n", "                LEFT JOIN lake_rpc.item_category_details icd ON icd.item_id = id.item_id\n", "                AND icd.lake_active_record\n", "                 left join\n", "                        rpc.item_tag_mapping itm\n", "                    on\n", "                        itm.item_id  = id.item_id\n", "                    and\n", "                        itm.tag_type_id = 7\n", "                    and\n", "                        itm.active    \n", "                where id.active =1 \n", "                and id.lake_active_record\n", "                and id.approved = 1\n", "                )\n", "\n", "                select\n", "                    b.*, concat(current_tagging, '_', storage_type) as derived_storage_type\n", "                from\n", "                    base b\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_if():\n", "    \"\"\"Fetch item factor (size factor for storage) for all items.\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT item_id, item_factor\n", "        FROM supply_etls.item_factor\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_tag_value(backend_facility_id, outlet_list_tuple):\n", "    \"\"\"Fetch mapping of outlets to BE (backend) outlet tag values for a facility.\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT iotm.outlet_id, item_id, CAST(tag_value AS INT) AS tag_value,\n", "               outlet_name AS be_name, pfom.facility_id AS be_facility_id, pfom.outlet_id AS inv_id\n", "        FROM rpc.item_outlet_tag_mapping iotm\n", "        INNER JOIN po.physical_facility_outlet_mapping pfom\n", "            ON pfom.outlet_id = CAST(iotm.tag_value AS INT) AND pfom.active = 1\n", "        WHERE iotm.active = 1 AND tag_type_id = 8\n", "          AND iotm.outlet_id IN {outlet_list_tuple}\n", "          AND iotm.tag_value = (\n", "                SELECT DISTINCT CAST(outlet_id AS VARCHAR)\n", "                FROM po.physical_facility_outlet_mapping pfom\n", "                WHERE pfom.active = 1 AND pfom.ars_active = 1\n", "                  AND pfom.facility_id = {backend_facility_id}\n", "          )\n", "          AND iotm.lake_active_record\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_be_inv(tag_list):\n", "    \"\"\"Fetch backend (warehouse) inventory for given BE warehouse ID (tag value).\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT warehouse_id AS tag_value, item_id, quantity AS be_inv\n", "        FROM ims.ims_item_inventory iii\n", "        INNER JOIN retail.warehouse_outlet_mapping wom ON wom.cloud_store_id = iii.outlet_id\n", "        LEFT JOIN po.physical_facility_outlet_mapping pfom ON pfom.outlet_id = warehouse_id AND pfom.active = 1\n", "        WHERE warehouse_id = {tag_list}\n", "          AND pfom.lake_active_record AND wom.lake_active_record\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_rtv_map():\n", "    \"\"\"Fetch mapping of SKUs eligible for RTV or SOR.\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT DISTINCT item_id, sor, rtv\n", "        FROM interim.category_etls_rtv_sor_for_v3\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_lp(outlet_list_tuple, item_list):\n", "    \"\"\"Fetch current list price (WLP), MRP, and selling price for items and outlets.\"\"\"\n", "    sql = f\"\"\"\n", "         select\n", "                    retail_item_id as item_id,\n", "                    pos_outlet_id as outlet_id,\n", "                    wlp,\n", "                    mrp,\n", "                    price\n", "                from pricing_v3.pricing_domain_product pdp\n", "                   JOIN (\n", "                        SELECT DISTINCT pos_outlet_id, frontend_merchant_id, frontend_merchant_city_name AS city_name, frontend_merchant_city_id as city_id\n", "                        FROM dwh.dim_merchant_outlet_facility_mapping\n", "                        WHERE is_frontend_merchant_active\n", "                          AND is_backend_merchant_active\n", "                          AND is_pos_outlet_active = 1\n", "                          AND is_mapping_enabled\n", "                          AND is_express_store\n", "                          AND is_current_mapping_active\n", "                          AND is_current = true\n", "                          AND pos_outlet_name <> 'SS Gurgaon Test Store'\n", "                    ) mm ON mm.frontend_merchant_id = pdp.retail_outlet_id\n", "                where retail_item_id in {item_list}\n", "                and pos_outlet_id in {outlet_list_tuple}\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_qmax(outlet_list, item_list):\n", "    \"\"\"Fetch Qmax (maximum quantity) for given outlet and item combination.\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT outlet_id, item_id, qmax_final AS qmax\n", "        FROM supply_etls.ars_qmax\n", "        WHERE item_id IN {item_list} AND outlet_id IN {outlet_list} AND updated_at IS NOT NULL\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_rep_count(outlet_list_tuple):\n", "    \"\"\"Fetch maximum daily run count for each frontend facility (dark store) in given outlets list.\"\"\"\n", "    sql = f\"\"\"\n", "         WITH slots AS\n", "             (SELECT *\n", "              FROM ars.backend_slot\n", "              WHERE lake_active_record\n", "                AND active ),\n", "                slot_store AS\n", "             (SELECT *\n", "              FROM ars.auto_sto_slot\n", "              WHERE lake_active_record\n", "                AND active ),\n", "                slot_mapping AS\n", "             (SELECT backend_facility_id,\n", "                     slot,\n", "                     frontend_outlet_id,\n", "                     MODE\n", "              FROM slots s\n", "              INNER JOIN slot_store ss ON ss.backend_slot_id = s.id),\n", "                vehicle_mapping AS\n", "             (SELECT *\n", "              FROM ars.vehicle_dark_store_group_v2\n", "              WHERE lake_active_record\n", "                AND dispatch_time IS NOT NULL),\n", "                TEMP AS\n", "             (SELECT ars_mode,\n", "                     backend_facility_id,\n", "                     capacity_in_kg,\n", "                     created_at,\n", "                     slot,\n", "                     dispatch_time,\n", "                     cast(trim(']['\n", "                               FROM (array_join(split(value, ','), ','))) AS int) AS frontend_facility_id\n", "              FROM vehicle_mapping\n", "              CROSS JOIN UNNEST(split(frontend_facility_id, ',')) AS t(value)), vd AS\n", "             (SELECT temp.*,\n", "                     pfom.outlet_id,\n", "                     outlet_name\n", "              FROM TEMP\n", "              INNER JOIN po.physical_facility_outlet_mapping pfom ON pfom.facility_id = temp.frontend_facility_id\n", "              and pfom.outlet_id in {outlet_list_tuple}\n", "              ),\n", "                                                                                base AS\n", "             (\n", "                SELECT vd.backend_facility_id,\n", "                     frontend_facility_id,\n", "                     count(vd.slot) AS run_count\n", "              FROM vd\n", "              INNER JOIN slot_mapping sm ON sm.backend_facility_id = vd.backend_facility_id\n", "              AND sm.mode = vd.ars_mode\n", "              AND sm.frontend_outlet_id = vd.outlet_id\n", "              AND sm.slot = cast(format('%%02d:%%02d',hour(vd.slot), minute(vd.slot)) AS varchar)\n", "              WHERE ars_mode = 'normal'\n", "              GROUP BY 1,\n", "                       2) \n", "\n", "            SELECT frontend_facility_id,\n", "                 max(run_count) AS run_count\n", "           FROM base\n", "           GROUP BY 1\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "8708222e-1fe9-4cf3-9164-3be8ce7b5ed9", "metadata": {}, "outputs": [], "source": ["def fetch_tat():\n", "    \"\"\"Compute transit times (TAT) between source and destination facilities (past 14 days).\"\"\"\n", "    sql = f\"\"\"\n", "        WITH node_facility_mapping AS (\n", "            SELECT CAST(logistic_node_id AS VARCHAR) AS store_id, facility_id, f.name AS facility_name\n", "            FROM retail.console_outlet_logistic_mapping logm\n", "            INNER JOIN retail.console_outlet co ON co.id = logm.outlet_id AND logm.lake_active_record = TRUE AND co.lake_active_record = TRUE\n", "            INNER JOIN retail.warehouse_facility f ON f.id = co.facility_id AND f.lake_active_record = TRUE\n", "        ),\n", "        base AS (\n", "            SELECT consignment_id, source_store_id, destination_store_id,\n", "                   DATE_DIFF('hour', MIN(logs.install_ts), MAX(logs.install_ts)) + 3 AS delivery_tat\n", "            FROM transit_server.transit_consignment cons\n", "            INNER JOIN transit_server.transit_consignment_state_logs logs ON cons.id = logs.consignment_id\n", "            WHERE cons.lake_active_record = TRUE AND logs.lake_active_record = TRUE\n", "              AND cons.insert_ds_ist >= CAST(CURRENT_DATE - interval '14' day AS VARCHAR)\n", "              AND logs.insert_ds_ist >= CAST(CURRENT_DATE - interval '14' day AS VARCHAR)\n", "              AND cons.insert_ds_ist < CAST(CURRENT_DATE AS VARCHAR)\n", "              AND logs.insert_ds_ist < CAST(CURRENT_DATE AS VARCHAR)\n", "              AND cons.state = 'COMPLETED'\n", "              AND logs.to_state IN ('COMPLETED', 'READY_FOR_DISPATCH')\n", "            GROUP BY 1,2,3\n", "            HAVING COUNT(*) > 1 AND DATE_DIFF('hour', MIN(logs.install_ts), MAX(logs.install_ts)) > 0\n", "        ),\n", "        outliers AS (\n", "            SELECT source_store_id, destination_store_id,\n", "                   APPROX_PERCENTILE(delivery_tat, 0.80) AS top_outlier,\n", "                   APPROX_PERCENTILE(delivery_tat, 0.10) AS bottom_outlier,\n", "                   COUNT(*) AS data_points\n", "            FROM base\n", "            GROUP BY 1, 2\n", "        ),\n", "        cleaned_data AS (\n", "            SELECT base.source_store_id, base.destination_store_id,\n", "                   MAX(delivery_tat) AS max_tat, MIN(delivery_tat) AS min_tat,\n", "                   AVG(delivery_tat) AS avg_tat, APPROX_PERCENTILE(delivery_tat, 0.50) AS median_tat,\n", "                   COUNT(*) AS data_points\n", "            FROM base\n", "            INNER JOIN outliers ON base.source_store_id = outliers.source_store_id AND base.destination_store_id = outliers.destination_store_id\n", "            WHERE delivery_tat <= top_outlier AND delivery_tat >= bottom_outlier\n", "            GROUP BY 1, 2\n", "        ),\n", "        add_manual_values AS (\n", "            SELECT source_node.facility_id AS source_facility_id, source_node.facility_name AS source_facility_name,\n", "                   dest_node.facility_id AS destination_facility_id, dest_node.facility_name AS destination_facility_name,\n", "                   cleaned_data.*\n", "            FROM cleaned_data\n", "            INNER JOIN node_facility_mapping source_node ON source_node.store_id = cleaned_data.source_store_id\n", "            INNER JOIN node_facility_mapping dest_node ON dest_node.store_id = cleaned_data.destination_store_id\n", "        ),\n", "        manual_tat AS (\n", "            SELECT destination_facility_id, source_facility_id, MAX(manual_tat_in_hr) AS manual_tat_in_hr\n", "            FROM supply_etls.manual_transit_time\n", "            WHERE active = 1 AND updated_at >= (CURRENT_DATE - interval '14' day)\n", "            GROUP BY 1, 2\n", "        )\n", "        SELECT amv.*, CASE WHEN mtt.manual_tat_in_hr IS NOT NULL THEN mtt.manual_tat_in_hr ELSE avg_tat END AS final_tat,\n", "               CASE WHEN mtt.manual_tat_in_hr IS NOT NULL THEN 0 ELSE data_points END AS final_data_points\n", "        FROM add_manual_values amv\n", "        LEFT JOIN manual_tat mtt ON mtt.source_facility_id = amv.source_facility_id AND mtt.destination_facility_id = amv.destination_facility_id\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_open_stos(outlet_list_tuple, item_list):\n", "    \"\"\"Fetch open STOs (transfer orders) for given outlets and items.\"\"\"\n", "    sql = f\"\"\"\n", "        WITH fo AS (\n", "            SELECT facility_id, outlet_id, city_id, outlet_name\n", "            FROM lake_po.physical_facility_outlet_mapping\n", "            WHERE active = 1 AND ars_active = 1 AND facility_id IN (\n", "                  SELECT facility_id FROM lake_retail.console_outlet WHERE business_type_id = 7\n", "            ) AND outlet_id IN {outlet_list_tuple}\n", "            GROUP BY 1,2,3,4\n", "        )\n", "        SELECT fo.facility_id, fo.outlet_name, isd.outlet_id, isd.sto_id, isd.sto_state, isi.item_id, isi.expected_quantity\n", "        FROM ims.ims_sto_details isd\n", "        INNER JOIN ims.ims_sto_item isi ON isi.sto_id = isd.sto_id\n", "        INNER JOIN fo ON fo.outlet_id = isd.outlet_id\n", "        WHERE isd.sto_state IN (1, 2, 5) AND isi.item_id IN {item_list}\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_truncations(outlet_list_tuple, lookback_days=8):\n", "    \"\"\"Fetch storage drops (truncations) from simulation runs in the last given days for specified outlets.\"\"\"\n", "    sql = f\"\"\"\n", "             WITH runs AS\n", "                  (SELECT run_id\n", "                   FROM ars.job_run\n", "                    WHERE (started_at+interval'5'hour+interval'30'MINUTE) between (CURRENT_DATE-interval'8'DAY) and (CURRENT_DATE-interval'1'DAY)\n", "                     AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "                     AND simulation_params NOT LIKE '%%mode%%'\n", "                     AND simulation_params NOT LIKE '%%hyperpure_run%%'\n", "                     AND simulation_params NOT LIKE '%%test%%'\n", "                     AND simulation_params NOT LIKE '%%type%%'\n", "                     AND simulation_params NOT LIKE '%%manual%%'\n", "                     AND simulation_params NOT LIKE '%%any_day_po%%'\n", "                     AND simulation_params NOT LIKE '%%spr_migration_simulation%%'\n", "                   ORDER BY 1),\n", "\n", "                agg as (   \n", "                select\n", "                    sto_date,\n", "                    torv.frontend_facility_id,\n", "                    torv.frontend_outlet_id,\n", "                    case when id.storage_type in ('2', '6') then 'COLD'\n", "                            when id.storage_type in ('4', '5') then 'HEAVY'\n", "                            when id.storage_type in ('3', '7') then 'FROZEN'\n", "                            -- when id.storage_type in ('11') then 'BEST_SERVED_COLD'\n", "                            ELSE 'REGULAR' end as storage_type,\n", "                    case when itm.tag_value = '1' then 'BEAUTY'\n", "                        when itm.tag_value = '2' then 'BOUQUET' \n", "                        when itm.tag_value = '3' then 'PREMIUM'\n", "                        when itm.tag_value = '4' then 'BOOKS'\n", "                        when itm.tag_value = '5' then 'NON_VEG' \n", "                        when itm.tag_value = '6' then 'ICE_CREAM'\n", "                        when itm.tag_value = '7' then 'TOYS'\n", "                        when itm.tag_value = '8' then 'ELECTRONICS'\n", "                        -- when itm.tag_value = '9' then 'FESTIVE'\n", "                        when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "                        when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "                        when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "                        when itm.tag_value = '13' then 'LARGE'\n", "                        when itm.tag_value = '14' then 'APPAREL'\n", "                        when itm.tag_value = '15' then 'SPORTS'\n", "                        when itm.tag_value = '16' then 'PET_CARE'\n", "                        when itm.tag_value = '17' then 'HOME_DECOR'\n", "                        when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "                        when itm.tag_value = '19' then 'HOME_FURNISHING'\n", "                        when itm.tag_value = '20' then 'LONGTAIL_OTHERS'\n", "                        when itm.tag_value = '21' then 'PHARMA'\n", "                        when itm.tag_value = '22' then 'PASS'\n", "                        when itm.tag_value = '23' then 'PALLET'\n", "                        when itm.tag_value = '24' then 'LARGE_FRAGILE'\n", "                        else 'NORMAL_GROCERY'\n", "                    end as current_tagging,\n", "                    sum(coalesce(torv.sto_quantity_post_truncation,0)) as v2,\n", "                    sum(try_cast(json_extract(torv.quantity_drops, '$.inward_drop') AS INTEGER)) AS inward_drop,\n", "                    sum(try_cast(json_extract(torv.quantity_drops, '$.storage_drop') AS INTEGER)) AS storage_drop,\n", "                    sum(try_cast(json_extract(torv.quantity_drops, '$.truck_load_drop') AS INTEGER)) AS truck_load_drop,\n", "                    sum(try_cast(json_extract(torv.quantity_drops, '$.picking_capacity_quantity_drop') AS INTEGER)) AS picking_capacity_quantity_drop,\n", "                    sum(try_cast(json_extract(torv.quantity_drops, '$.picking_capacity_sku_drop') AS INTEGER)) AS picking_capacity_sku_drop\n", "                from\n", "                    ars.transfers_optimization_results_v2 torv\n", "                left join\n", "                    rpc.item_details id \n", "                on\n", "                    id.item_id = torv.item_id \n", "                left join\n", "                    rpc.item_tag_mapping itm\n", "                on\n", "                    itm.item_id  = torv.item_id\n", "                and\n", "                    itm.tag_type_id = 7\n", "                and\n", "                    itm.active    \n", "                where\n", "                     torv.run_id IN (SELECT run_id FROM runs)\n", "                and\n", "                    frontend_outlet_id in {outlet_list_tuple}\n", "                and\n", "                    torv.insert_ds_ist >= cast(current_date - interval '{truncation_lookback_days}' day as varchar)\n", "                group by 1,2,3,4,5\n", "                ),\n", "\n", "                ds_storage_type as (\n", "                    select\n", "                        facility_id,\n", "                        storage_type\n", "                    from\n", "                        ars.physical_facility_storage_capacity pfsc\n", "                    where\n", "                        pfsc.active\n", "                    and\n", "                        pfsc.storage_capacity > 0 \n", "                    and\n", "                        pfsc.lake_active_record\n", "                ),\n", "\n", "                avail_storage_types as (\n", "                    select *, concat(current_tagging,'_',storage_type) as derived_storage_type\n", "                    from agg\n", "                ),\n", "\n", "                final_storage_type_mapping as (\n", "                    select\n", "                        avt.sto_date,\n", "                        avt.frontend_facility_id,\n", "                        avt.frontend_outlet_id,\n", "                        avt.storage_type,\n", "                        avt.derived_storage_type,\n", "                        ds.storage_type as available_storage_type,\n", "                        case when ds.storage_type is null then avt.storage_type else avt.derived_storage_type end as final_storage_type,\n", "                        v2,\n", "                        inward_drop,\n", "                        storage_drop,\n", "                        truck_load_drop,\n", "                        picking_capacity_quantity_drop,\n", "                        picking_capacity_sku_drop\n", "                    from\n", "                        avail_storage_types avt\n", "                    left join\n", "                        ds_storage_type ds\n", "                    on\n", "                        ds.storage_type = avt.derived_storage_type\n", "                    and\n", "                        ds.facility_id = avt.frontend_facility_id\n", "                ),\n", "\n", "\n", "                agg_drops_v2 as (\n", "                    select \n", "                        sto_date,\n", "                        frontend_facility_id,\n", "                        frontend_outlet_id,\n", "                        final_storage_type,\n", "                        sum(v2) as v2,\n", "                        sum(inward_drop) as inward_drop,\n", "                        sum(storage_drop) as storage_drop,\n", "                        sum(truck_load_drop) as truck_load_drop,\n", "                        sum(picking_capacity_quantity_drop) as picking_capacity_quantity_drop,\n", "                        sum(picking_capacity_sku_drop) as picking_capacity_sku_drop\n", "                    from\n", "                         final_storage_type_mapping\n", "                    group by\n", "                        1,2,3,4\n", "                ),\n", "\n", "\n", "                rank_ as (\n", "                    select\n", "                        agg_drops_v2.*, rank() OVER(partition by frontend_facility_id, final_storage_type order by sto_date) as rnk\n", "                    from\n", "                        agg_drops_v2\n", "                ),\n", "\n", "                -- select * from rank_\n", "\n", "                storage_drop_contribution as (\n", "                    select\n", "                        frontend_facility_id, frontend_outlet_id, final_storage_type, \n", "                        1.000*sum(rnk*storage_drop)/(case when sum(rnk*(greatest(inward_drop,truck_load_drop,storage_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop))) = 0 then 1 else sum(rnk*(greatest(inward_drop,truck_load_drop,storage_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop))) end) as storage_drop_contribution\n", "                    from\n", "                        rank_\n", "                    group by 1,2,3\n", "                    )\n", "\n", "                select\n", "                    *\n", "                from\n", "                    storage_drop_contribution sdc  \n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_rsto_dump():\n", "    \"\"\"Fetch data on RSTO dumps (inventory transferred from FE to BE) for last 30 days.\"\"\"\n", "    sql = f\"\"\"\n", "        WITH base AS (\n", "            SELECT p.item_id, p.name AS item_name, COALESCE(m.name, '') AS manufacturer, l0, l1, l2, infra, b.name AS brand, item_type,\n", "                   ROW_NUMBER() OVER (PARTITION BY p.item_id ORDER BY COALESCE(m.name, '')) AS rn\n", "            FROM rpc.product_product p\n", "            LEFT JOIN rpc.product_brand b ON b.id = p.brand_id\n", "            LEFT JOIN rpc.product_manufacturer m ON m.id = b.manufacturer_id AND m.active = 1 AND m.lake_active_record = TRUE\n", "            LEFT JOIN (\n", "                SELECT item_id, l0, l1, l2, infra, item_type FROM ba_etls.item_details GROUP BY 1,2,3,4,5,6\n", "            ) i ON i.item_id = p.item_id\n", "            WHERE p.lake_active_record = TRUE\n", "        ),\n", "        base2 AS (\n", "            SELECT sto_id, invoice_id, consignment_id, t.state AS consignment_status, trip_id\n", "            FROM dwh.flat_invoice_consignment_details b\n", "            LEFT JOIN transit_server.transit_consignment t ON t.id = b.consignment_id\n", "            WHERE consignment_date_ist >= current_date - interval '60' day\n", "              AND t.insert_ds_ist >= CAST(current_date - interval '60' day AS VARCHAR)\n", "            GROUP BY 1,2,3,4,5\n", "        ),\n", "        base3 AS (\n", "            SELECT sto_id, invoice_id,\n", "                   LISTAGG(CAST(consignment_id AS VARCHAR), ', ') WITHIN GROUP (ORDER BY consignment_id) AS consignment_id,\n", "                   LISTAGG(consignment_status, ', ') WITHIN GROUP (ORDER BY consignment_id) AS consignment_status,\n", "                   LISTAGG(trip_id, ', ') WITHIN GROUP (ORDER BY trip_id) AS trip_id\n", "            FROM base2\n", "            GROUP BY 1, 2\n", "        ),\n", "        Ultra_Base AS (\n", "            SELECT EXTRACT(week FROM invoice_billed_date_ist) AS week_num, DATE(invoice_billed_date_ist) AS invoice_billed_date_ist,\n", "                   sender_outlet_id, o1.outlet_name AS sender_outlet_name, o1.outlet_type AS sender_outlet_type,\n", "                   receiver_outlet_id, o2.outlet_name AS receiver_outlet_name, o2.outlet_type AS receiver_outlet_type,\n", "                   o1.city AS sender_outlet_city, o2.city AS receiver_outlet_city,\n", "                   CONCAT(o1.outlet_type, '-', o2.outlet_type) AS transaction_type,\n", "                   trip_id, consignment_id, consignment_status,\n", "                   tl.sto_id, tl.invoice_id, tl.item_id, item_name, l0, l1, l2, brand, manufacturer, item_type, infra AS is_infra,\n", "                   CASE WHEN o1.outlet_type = 'BE' THEN o1.outlet_name ELSE o1.city END AS Sender_New,\n", "                   CASE WHEN o2.outlet_type = 'BE' THEN o2.outlet_name ELSE o2.city END AS Receiver_New,\n", "                   ROUND(SUM(rsto_dump_amt)) AS rsto_dump_amt,\n", "                   ROUND(SUM(rsto_dump_dmg_amt)) AS rsto_dump_dmg_amt,\n", "                   ROUND(SUM(rsto_dump_near_expiry_amt)) AS rsto_dump_near_expiry_amt,\n", "                   ROUND(SUM(rsto_dump_expired_amt)) AS rsto_dump_expired_amt,\n", "                   ROUND(SUM(billed_amt)) AS billed_amt\n", "            FROM dwh.fact_inventory_transfer_details tl\n", "            LEFT JOIN base b ON b.item_id = tl.item_id AND b.rn = 1\n", "            LEFT JOIN base3 b3 ON b3.sto_id = tl.sto_id AND b3.invoice_id = tl.invoice_id\n", "            LEFT JOIN ba_etls.outlet_details o1 ON o1.outlet_id = tl.sender_outlet_id\n", "            LEFT JOIN ba_etls.outlet_details o2 ON o2.outlet_id = tl.receiver_outlet_id\n", "            WHERE invoice_billed_date_ist >= current_date - interval '30' day\n", "              AND item_type NOT IN ('infra')\n", "              AND (sender_outlet_id IS NOT NULL OR receiver_outlet_id IS NOT NULL)\n", "            GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27\n", "        ),\n", "        reverse_base AS (\n", "            SELECT rc.consignment_id, rt.transit_trip_id,\n", "                   MAX(CASE WHEN rtl.state = 'UNLOADING_COMPLETED' THEN rtl.created_at + interval '330' minute END) AS trip_uncom,\n", "                   MAX(CASE WHEN rtl.state = 'QC_COMPLETED' THEN rtl.created_at + interval '330' minute END) AS trip_com\n", "            FROM wms.reverse_consignment rc\n", "            INNER JOIN wms.reverse_trip rt ON rc.trip_id = rt.id\n", "            LEFT JOIN wms.reverse_trip_log rtl ON rtl.trip_id = rt.id\n", "            WHERE rc.created_at >= current_date - interval '33' day\n", "              AND rtl.insert_ds_ist >= CAST(current_date - interval '33' day AS VARCHAR)\n", "            GROUP BY 1, 2\n", "        ),\n", "        new_new_base AS (\n", "            SELECT invoice_billed_date_ist, sender_outlet_id, sender_outlet_name, sender_outlet_city, l1, l2, consignment_id,\n", "                   receiver_outlet_id, receiver_outlet_name, receiver_outlet_city,\n", "                   SUM(rsto_dump_amt) AS rsto_total_dump_amt,\n", "                   SUM(rsto_dump_dmg_amt) AS rsto_dump_dmg_amt,\n", "                   SUM(rsto_dump_near_expiry_amt) AS rsto_dump_near_expiry_amt,\n", "                   SUM(rsto_dump_expired_amt) AS rsto_dump_expired_amt,\n", "                   SUM(billed_amt) AS rsto_billed_amt\n", "            FROM Ultra_Base\n", "            WHERE transaction_type = 'FE-BE'\n", "            GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "        )\n", "        SELECT nb.invoice_billed_date_ist, nb.sender_outlet_id, nb.sender_outlet_name, nb.sender_outlet_city, nb.l1, nb.l2, nb.consignment_id,\n", "               nb.receiver_outlet_id, nb.receiver_outlet_name, nb.receiver_outlet_city, nb.rsto_total_dump_amt, rb.transit_trip_id,\n", "               DATE_DIFF('minute', trip_uncom, trip_com) / 60.0 AS trip_closure_tat,\n", "               nb.rsto_dump_dmg_amt, nb.rsto_dump_near_expiry_amt, nb.rsto_dump_expired_amt, nb.rsto_billed_amt\n", "        FROM new_new_base nb\n", "        LEFT JOIN reverse_base rb ON nb.consignment_id = CAST(rb.consignment_id AS VARCHAR)\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_ds_available_storage_type():\n", "    \"\"\"Fetch storage types available at each dark store (frontend facility).\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT facility_id, storage_type AS derived_storage_type, 1 AS is_storage_type_avail\n", "        FROM ars.physical_facility_storage_capacity pfsc\n", "        WHERE pfsc.active AND pfsc.storage_capacity > 0 AND pfsc.lake_active_record\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_no_truncation_skus():\n", "    \"\"\"Fetch list of SKUs not to truncate (global list).\"\"\"\n", "    sql = f\"\"\"\n", "        WITH base AS (\n", "            SELECT value\n", "            FROM ars.ars_config\n", "            WHERE name = 'no_truncation_items' AND lake_active_record AND facility_id = 0\n", "        )\n", "        SELECT CAST(trimmed_value AS BIGINT) AS value\n", "        FROM base, UNNEST(SPLIT(TRIM(BOTH '[]' FROM value), ',')) AS t(trimmed_value)\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "def fetch_seller_hub_skus():\n", "    \"\"\"Fetch list of SKUs from seller hub mapping (global list).\"\"\"\n", "    sql = f\"\"\"\n", "        SELECT DISTINCT item_id\n", "        FROM seller.seller_product_mappings\n", "        WHERE lake_active_record\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "0bb5883b-7113-4a5f-8ca7-023b23a894b9", "metadata": {}, "outputs": [], "source": ["def fetch_upper_cap_config(backend_facility_id):\n", "    backend_facility_id = backend_facility_id\n", "    sql = f\"\"\"\n", "        select\n", "              facility_id as backend_facility_id, name, value\n", "            from\n", "              ars.ars_config\n", "            where\n", "              name = 'rsto_upper_cap_config'\n", "              and lake_active_record\n", "            and facility_id  = {backend_facility_id}\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "\n", "    if df.shape[0] == 0:\n", "        sql = f\"\"\"\n", "            select\n", "              facility_id as backend_facility_id, name, value\n", "            from\n", "              ars.ars_config\n", "            where\n", "              name = 'rsto_upper_cap_config'\n", "              and lake_active_record\n", "              and facility_id = 0\n", "        \"\"\"\n", "        con = pb.get_connection(\"[Warehouse] Trino\")\n", "        df = pd.read_sql_query(sql=sql, con=con)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "451ee3cb-2150-4e2d-91ed-c947b6d26a40", "metadata": {}, "outputs": [], "source": ["# import pandas as pd\n", "from datetime import datetime, timezone, timedelta\n", "\n", "\n", "def upload_to_trino_table_raw(df: pd.DataFrame):\n", "    \"\"\"\n", "    Upload a pandas DataFrame to the Trino table `supply_etls.rsto_cost_function_output` with append mode.\n", "\n", "    Steps:\n", "    1. Copy the input DataFrame to avoid modifying the original.\n", "    2. Add an 'updated_at' column with the current timestamp (IST, UTC+5:30).\n", "    3. Define primary keys, partition key, incremental key, load type, and other load parameters.\n", "    4. Map pandas dtypes to Trino dtypes and prepare the column_dtypes schema list (including updated_at).\n", "    5. (Placeholder) Upload the data to the Trino table using the above configuration.\n", "    \"\"\"\n", "    # Copy the input DataFrame\n", "    df_copy = df.copy()\n", "\n", "    # Add updated_at column with current timestamp (IST timezone, UTC+5:30)\n", "    ist_timezone = timezone(timedelta(hours=5, minutes=30))\n", "    df_copy[\"updated_at\"] = datetime.now(ist_timezone).replace(tzinfo=None)\n", "    df_copy[\"date_ist\"] = df_copy[\"updated_at\"].dt.date.astype(str)\n", "\n", "    # Define table and schema names\n", "    table_name = \"rsto_cost_function_output_v2\"\n", "    schema_name = \"supply_etls\"\n", "\n", "    # Define primary key, partition key, incremental key, and load parameters\n", "    primary_keys = [\"outlet_id\", \"item_id\", \"updated_at\", \"date_ist\"]\n", "    partition_key = [\"date_ist\"]\n", "    incremental_key = \"updated_at\"\n", "    load_type = \"append\"\n", "    force_upsert_without_increment_check = False\n", "    table_description = \"cost function output from rsto pipeline\"\n", "\n", "    # Build column_dtypes list by mapping pandas dtypes to Trino data types\n", "    # (Using mapping: int64->integer, float64->real, object->varchar, datetime64[ns]->timestamp(6), bool->boolean)\n", "    column_dtypes = [\n", "        {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "        {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "        {\n", "            \"name\": \"master_assortment_substate_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"master_assortment_substate_id\",\n", "        },\n", "        {\"name\": \"substate_reason_id\", \"type\": \"integer\", \"description\": \"substate_reason_id\"},\n", "        {\"name\": \"reason_type\", \"type\": \"varchar\", \"description\": \"reason_type\"},\n", "        {\"name\": \"reason_text\", \"type\": \"varchar\", \"description\": \"reason_text\"},\n", "        {\"name\": \"quantity\", \"type\": \"integer\", \"description\": \"quantity\"},\n", "        {\"name\": \"exclusion_type\", \"type\": \"varchar\", \"description\": \"exclusion_type\"},\n", "        {\"name\": \"day_count\", \"type\": \"real\", \"description\": \"day_count\"},\n", "        {\"name\": \"cpd\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"aps_adjusted\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_sale\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"for_date\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"rnk\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"storage_type\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"current_tagging\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"derived_storage_type\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"weight_in_gm\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"live_day_count\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"live_day_count_bucket\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"item_factor\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"tag_value\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"inv_id\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"be_inv\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"sor\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"rtv\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"rtv_flag\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"avg_lp\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"mrp\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"avg_asp\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"margin\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"qmax\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"run_count\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"intransit_tat\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"expected_quantity\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"is_storage_type_avail\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"final_storage_type\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"adj_margin\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"buffer_doi\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"upper_cap\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"seq_inv\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"excess_inv\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"storage_cost_of_excess_inv\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"capital_cost_of_excess_inv\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"holding_cost_of_excess_inv\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"unavl_sku_count\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_cpd\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_aps\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_if_\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_margin\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_asp\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_lp\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"opp\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"min_inv_storage\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"storage_drop_contribution\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"minimum_storage_drop_contribution\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_inventory\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_return\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_storage_cost\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_capital_cost\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"base_holding_cost\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"opp_cost\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"opp_cost_due_to_storage\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"keeping_cost\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"rsto_dump_dmg_amt\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"rsto_billed_amt\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"perc_damage\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"adj_perc_damage\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"mp_cost\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"exp_intransit_dmg\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"cost_of_rsto\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"rtv_potential\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"total_rtv_potential_line\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"total_lines\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"rtv_elg\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"rtv_contribution\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"cost_of_rsto_inc_rtv\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"final_cost_of_rsto_inc_rtv\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"rsto_rtv_reward_risk_ratio\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"rsto_reward_risk_ratio\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"rsto_storage\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"cumsum_storage_created\", \"type\": \"real\", \"description\": \"NA\"},\n", "        {\"name\": \"create_rsto\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"eligible_for_rsto\", \"type\": \"integer\", \"description\": \"NA\"},\n", "        {\"name\": \"reason_for_ineligibility\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"updated_at\", \"type\": \"timestamp(6)\", \"description\": \"NA\"},  # added column\n", "        {\"name\": \"date_ist\", \"type\": \"varchar\", \"description\": \"Date in IST from updated_at\"},\n", "    ]\n", "\n", "    # (Placeholder for actual upload logic)\n", "    # Example: connect to Trino and insert data, or use an ETL framework's function to handle the upload.\n", "    # For example, one might use a Trino Python client or write to an external storage that Trino reads.\n", "    # Here we just print a confirmation that data is ready to be uploaded.\n", "    print(\n", "        f\"DataFrame prepared with {len(df_copy)} rows for upload to {schema_name}.{table_name} (load_type={load_type}).\"\n", "    )\n", "    # TODO: Implement the actual upload to Trino using the configurations above.\n", "    kwargs_log = {\n", "        \"schema_name\": schema_name,\n", "        \"table_name\": table_name,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary_keys,\n", "        \"incremental_key\": incremental_key,\n", "        \"load_type\": load_type,\n", "        \"table_description\": table_description,\n", "        \"force_upsert_without_increment_check\": force_upsert_without_increment_check,\n", "    }\n", "    pb.to_trino(df_copy, **kwargs_log)"]}, {"cell_type": "code", "execution_count": null, "id": "7e9d9c6f-76d7-416c-9576-cd3a001d94a8", "metadata": {}, "outputs": [], "source": ["# Iterate over each facility in the configuration and compute RSTO metrics\n", "for _, row in facility_config.iterrows():\n", "    backend_facility_id = row.get(\"backend_facility_id\")\n", "    create_rsto = row.get(\"create_rsto\")\n", "    risk_reward_threshold = row.get(\"risk_reward_threshold\")\n", "    print(f\"Loop starting for facility id:{backend_facility_id}\")\n", "    print(f\"BE config, create_rsto: {create_rsto}, risk_reward_threshold: {risk_reward_threshold}\")\n", "\n", "    # Fetch and prepare all required data for this facility\n", "    print(\"STARTING DATA FETCHING\")\n", "\n", "    # 1. Outlet list\n", "    print(\"Running fetch_outlet_list\")\n", "    outlet_list_df = fetch_outlet_list()\n", "    outlet_list = tuple(outlet_list_df.outlet_id.unique())\n", "    print(f\"Unique outlet_list created with length: {len(outlet_list)}\")\n", "\n", "    # 2. Outlet age\n", "    print(\"Running fetch_outlet_age\")\n", "    outlet_age = fetch_outlet_age()\n", "    outlet_age = outlet_age.dropna()\n", "    outlet_age = outlet_age.astype({\"outlet_id\": int})\n", "    # (Not excluding new outlets for now)\n", "    outlet_list_df = outlet_list_df.merge(outlet_age, how=\"left\", on=\"outlet_id\")\n", "\n", "    # 3. ARS slot configuration\n", "    print(\"Checking if outlets are added in ARS reverse config or not\")\n", "    print(\"Running fetch_ars_slot_config\")\n", "    ars_slot_config = fetch_ars_slot_config(backend_facility_id)\n", "    data = ars_slot_config[\"slot_config\"].iloc[0] if not ars_slot_config.empty else \"{}\"\n", "    parsed = json.loads(data)\n", "    unique_outlets = set()\n", "    for _, info in parsed.items():\n", "        unique_outlets.update(info.get(\"frontend_outlets\", []))\n", "    unique_outlets_list = tuple(unique_outlets)\n", "    print(\n", "        f\"Number of unique outlets found in slot config for BE {backend_facility_id}: {len(unique_outlets_list)}\"\n", "    )\n", "\n", "    # 4. Exclude specific frontend outlets\n", "    print(\"Running fetch_outlet_exclusion_config\")\n", "    exclude_outlet_config = fetch_outlet_exclusion_config()\n", "    print(f\"exclude_outlet_config: {exclude_outlet_config.frontend_outlet_id.unique()}\")\n", "    exclude_outlet_config = exclude_outlet_config[\n", "        exclude_outlet_config[\"backend_facility_id\"] == backend_facility_id\n", "    ]\n", "    outlet_exclude_list = tuple(exclude_outlet_config[\"frontend_outlet_id\"].unique())\n", "    # Filter outlet list by slot config and exclusion list\n", "    outlet_list_df = outlet_list_df[outlet_list_df[\"outlet_id\"].isin(unique_outlets_list)]\n", "    outlet_list_df = outlet_list_df[~outlet_list_df[\"outlet_id\"].isin(outlet_exclude_list)]\n", "    outlet_list_tuple = tuple(outlet_list_df[\"outlet_id\"].unique())\n", "    print(f\"final outlet count:{len(outlet_list_tuple)}\")\n", "\n", "    # 5. Inventory and assortment\n", "    print(\"Running fetch_inv\")\n", "    inv = fetch_inv(backend_facility_id, outlet_list_tuple)\n", "    print(inv.columns)\n", "    print(\"inv assortment distribution\")\n", "    print(inv.groupby([\"master_assortment_substate_id\"]).agg({\"quantity\": \"sum\"}))\n", "    print(inv[inv[\"quantity\"] == 0].shape)\n", "\n", "    # 6. <PERSON><PERSON> exclusions\n", "    print(\"Running fetch_item_exclusions\")\n", "    rsto_excluded_items = fetch_item_exclusions(backend_facility_id)\n", "    inv = inv.merge(rsto_excluded_items, how=\"left\", on=\"item_id\")\n", "    inv[\"exclusion_type\"] = inv[\"exclusion_type\"].fillna(\"NO_RSTO_EXCLUSION\")\n", "\n", "    ### 6.2 Item outlet exclusions\n", "    ioue = fetch_item_outlet_exclusion()\n", "    inv = inv.merge(\n", "        ioue[[\"item_id\", \"outlet_id\", \"line_exclusion_type\"]],\n", "        on=[\"item_id\", \"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "    inv[\"line_exclusion_type\"] = inv[\"line_exclusion_type\"].fillna(\"NO_LINE_BASED_EXCLUSIONS\")\n", "\n", "    ### 6.3 final exclusion type\n", "    inv[\"exclusion_type\"] = np.where(\n", "        inv[\"line_exclusion_type\"] != \"NO_LINE_BASED_EXCLUSIONS\",\n", "        inv[\"line_exclusion_type\"],\n", "        inv[\"exclusion_type\"],\n", "    )\n", "    # df = df.drop(columns=['B'])\n", "    inv = inv.drop(columns=[\"line_exclusion_type\"])\n", "    print(\"EXCLUDED LINE COUNTS\")\n", "    print(inv.groupby(\"exclusion_type\").agg({\"item_id\": \"count\"}).reset_index())\n", "\n", "    # 7. Active days of availability\n", "    print(\"Running fetch_active_days\")\n", "    active_days = fetch_active_days(outlet_list_tuple)\n", "    print(active_days.columns)\n", "    inv = inv.merge(active_days, how=\"left\", on=[\"item_id\", \"facility_id\", \"outlet_id\"])\n", "    item_list = tuple(inv.item_id.unique())\n", "\n", "    # 8. Recent consumption (CPD and APS)\n", "    print(\"Running fetch_cpd\")\n", "    cpd = fetch_cpd(outlet_list_tuple, item_list)\n", "    inv = inv.merge(cpd, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "    inv[[\"cpd\", \"aps_adjusted\", \"potential_sale\"]] = inv[\n", "        [\"cpd\", \"aps_adjusted\", \"potential_sale\"]\n", "    ].fillna(0)\n", "    inv[\"cpd\"] = np.where(\n", "        inv[\"cpd\"] < min_cpd_of_unavailable_skus, min_cpd_of_unavailable_skus, inv[\"cpd\"]\n", "    )\n", "    inv[\"aps_adjusted\"] = np.where(\n", "        inv[\"aps_adjusted\"] < min_cpd_of_unavailable_skus,\n", "        min_cpd_of_unavailable_skus,\n", "        inv[\"aps_adjusted\"],\n", "    )\n", "    inv[\"potential_sale\"] = np.where(\n", "        inv[\"potential_sale\"] < min_cpd_of_unavailable_skus,\n", "        min_cpd_of_unavailable_skus,\n", "        inv[\"potential_sale\"],\n", "    )\n", "\n", "    # 9. Item categories and tagging\n", "    print(\"Running fetch_cat\")\n", "    df_cat = fetch_cat()\n", "    inv = inv.merge(\n", "        df_cat[\n", "            [\n", "                \"item_id\",\n", "                \"name\",\n", "                \"storage_type\",\n", "                \"current_tagging\",\n", "                \"derived_storage_type\",\n", "                \"weight_in_gm\",\n", "            ]\n", "        ],\n", "        how=\"left\",\n", "        on=\"item_id\",\n", "    )\n", "    # inv = inv.merge(outlet_list_df, how='left', on='outlet_id')\n", "\n", "    # 10. Item factor (size factor)\n", "    print(\"Running fetch_if\")\n", "    if_df = fetch_if()\n", "    inv = inv.merge(if_df, how=\"left\", on=\"item_id\")\n", "    inv[\"item_factor\"] = inv[\"item_factor\"].fillna(default_item_factor)\n", "\n", "    # 11. BE outlet tag mapping\n", "    print(\"Running fetch_tag_value\")\n", "    tag_values = fetch_tag_value(backend_facility_id, outlet_list_tuple)\n", "    tag_values = tag_values.astype({\"tag_value\": int})\n", "    tag_list = tag_values[\"tag_value\"].unique()[0] if not tag_values.empty else None\n", "    inv = inv.merge(tag_values, how=\"left\", on=[\"outlet_id\", \"item_id\"])\n", "\n", "    # 12. Backend inventory for BE facility\n", "    print(\"Running fetch_be_inv\")\n", "    be_inv = (\n", "        fetch_be_inv(tag_list)\n", "        if tag_list is not None\n", "        else pd.DataFrame(columns=[\"tag_value\", \"item_id\", \"be_inv\"])\n", "    )\n", "    inv = inv.merge(be_inv, how=\"left\", on=[\"item_id\", \"tag_value\"])\n", "    inv[\"be_inv\"] = inv[\"be_inv\"].fillna(0)\n", "    print(f\"SKUs with BE inv >0: {be_inv[be_inv['be_inv']>0].shape[0] if not be_inv.empty else 0}\")\n", "\n", "    # 13. RTV/SOR mapping\n", "    print(\"Running fetch_rtv_map\")\n", "    rtv = fetch_rtv_map()\n", "    rtv = rtv[(rtv[\"sor\"] == \"Yes\") | (rtv[\"rtv\"] == \"Yes\")]\n", "    rtv[\"rtv_flag\"] = 1\n", "    inv = inv.merge(rtv, how=\"left\", on=\"item_id\")\n", "    inv[\"rtv_flag\"] = inv[\"rtv_flag\"].fillna(0)\n", "    inv[[\"sor\", \"rtv\"]] = inv[[\"sor\", \"rtv\"]].fillna(default_rtv_sor_flag)\n", "\n", "    # 14. Pricing information (LP, MRP, ASP)\n", "    print(\"Running fetch_lp\")\n", "    df_lp = fetch_lp(outlet_list_tuple, item_list)\n", "    print(\"LP fetched\")\n", "    print(df_lp.columns)\n", "    inv = inv.merge(df_lp, how=\"left\", on=[\"item_id\", \"outlet_id\"])\n", "    inv = inv.rename(columns={\"wlp\": \"avg_lp\", \"price\": \"avg_asp\"})\n", "    inv[\"margin\"] = inv[\"avg_asp\"] - inv[\"avg_lp\"]\n", "    inv[\"avg_lp\"] = np.where(\n", "        inv[\"margin\"] < 0, inv[\"avg_asp\"] * default_asp_lp_conversion_factor, inv[\"avg_lp\"]\n", "    )\n", "    inv[\"margin\"] = np.where(\n", "        inv[\"margin\"] < 0, inv[\"avg_asp\"] * default_asp_margin_conversion_factor, inv[\"margin\"]\n", "    )\n", "\n", "    # 15. Qmax configuration\n", "    print(\"Running fetch_qmax\")\n", "    df_qmax = fetch_qmax(outlet_list, item_list)\n", "    print(\"qmax fetched\")\n", "    print(df_qmax.columns)\n", "    inv = inv.merge(df_qmax, how=\"left\", on=[\"item_id\", \"outlet_id\"])\n", "\n", "    # 16. Replenishment run count\n", "    print(\"Running fetch_rep_count\")\n", "    rep_count_df = fetch_rep_count(outlet_list_tuple)\n", "    print(\"Rep count fetched\")\n", "    print(rep_count_df.columns)\n", "    rep_count_df = rep_count_df.rename(columns={\"frontend_facility_id\": \"facility_id\"})\n", "    inv = inv.merge(rep_count_df, on=\"facility_id\", how=\"left\")\n", "\n", "    # 17. Transit time (TAT)\n", "    print(\"Running fetch_tat\")\n", "    tat_df = fetch_tat()\n", "    print(\"TAT fetched\")\n", "    print(tat_df.columns)\n", "    tat_df = tat_df.rename(\n", "        columns={\n", "            \"source_facility_id\": \"be_facility_id\",\n", "            \"destination_facility_id\": \"facility_id\",\n", "            \"median_tat\": \"intransit_tat\",\n", "        }\n", "    )\n", "    inv = inv.merge(\n", "        tat_df[[\"be_facility_id\", \"facility_id\", \"intransit_tat\"]],\n", "        on=[\"be_facility_id\", \"facility_id\"],\n", "        how=\"left\",\n", "    )\n", "    inv[\"intransit_tat\"] = inv[\"intransit_tat\"].fillna(default_intransit_tat)\n", "    inv[\"run_count\"] = inv[\"run_count\"].fillna(1)\n", "\n", "    # 18. Filter inventory to relevant SKUs\n", "    inv = inv[\n", "        (inv[\"quantity\"] > 0)\n", "        | (inv[\"master_assortment_substate_id\"] == 1)\n", "        | ((inv[\"master_assortment_substate_id\"] == 3) & (inv[\"be_inv\"] > 0))\n", "    ]\n", "\n", "    # 19. Open STOs in pipeline\n", "    print(\"Running fetch_open_stos\")\n", "    open_sto_df = fetch_open_stos(outlet_list_tuple, item_list)\n", "    print(\"Open STO fetched\")\n", "    print(open_sto_df.columns)\n", "    open_sto_df = (\n", "        open_sto_df.groupby([\"outlet_id\", \"item_id\"])\n", "        .agg({\"expected_quantity\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "    inv = inv.merge(open_sto_df, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "    inv[\"expected_quantity\"] = inv[\"expected_quantity\"].fillna(0)\n", "\n", "    # 20. Storage truncation contributions\n", "    print(\"Running fetch_truncations\")\n", "    truncation_df = fetch_truncations(outlet_list_tuple, truncation_lookback_days)\n", "    truncation_df = truncation_df.astype({\"storage_drop_contribution\": float})\n", "    truncation_df = truncation_df.rename(\n", "        columns={\"frontend_facility_id\": \"facility_id\", \"frontend_outlet_id\": \"outlet_id\"}\n", "    )\n", "    print(\"Truncation storage type distribution\")\n", "    print(truncation_df.groupby(\"final_storage_type\")[\"storage_drop_contribution\"].mean())\n", "\n", "    # 20.2 Ds available storage\n", "    ds_avail_storage_type = fetch_ds_available_storage_type()\n", "    print(f\"Distinct available storage types:{ds_avail_storage_type.derived_storage_type.unique()}\")\n", "\n", "    inv = inv.merge(ds_avail_storage_type, on=[\"facility_id\", \"derived_storage_type\"], how=\"left\")\n", "\n", "    inv[\"is_storage_type_avail\"] = inv[\"is_storage_type_avail\"].fillna(0)\n", "    inv[\"final_storage_type\"] = np.where(\n", "        inv[\"is_storage_type_avail\"] == 0, inv[\"storage_type\"], inv[\"derived_storage_type\"]\n", "    )\n", "\n", "    # 21. Compute final RSTO cost and benefits\n", "    base = inv.copy()\n", "    base[[\"tag_value\", \"be_name\", \"be_inv\", \"day_count\"]] = base[\n", "        [\"tag_value\", \"be_name\", \"be_inv\", \"day_count\"]\n", "    ].fillna(0)\n", "    base[[\"aps_adjusted\", \"cpd\"]] = base[[\"aps_adjusted\", \"cpd\"]].fillna(default_cpd)\n", "    base[\"cpd\"] = np.where(base[\"cpd\"] < default_cpd, default_cpd, base[\"cpd\"])\n", "    base[\"aps_adjusted\"] = np.where(\n", "        base[\"aps_adjusted\"] < default_cpd, default_cpd, base[\"aps_adjusted\"]\n", "    )\n", "    base[\"avg_asp\"] = base[\"avg_asp\"].fillna(default_asp)\n", "    base[\"margin\"] = base[\"margin\"].fillna(base[\"avg_asp\"] * default_asp_margin_conversion_factor)\n", "    base[\"avg_lp\"] = base[\"avg_lp\"].fillna(base[\"avg_asp\"] * default_asp_lp_conversion_factor)\n", "    base[\"avg_lp\"] = np.where(\n", "        base[\"margin\"] < 0, base[\"avg_asp\"] * default_asp_lp_conversion_factor, base[\"avg_lp\"]\n", "    )\n", "    base[\"margin\"] = np.where(\n", "        base[\"margin\"] < 0, base[\"avg_asp\"] * default_asp_margin_conversion_factor, base[\"margin\"]\n", "    )\n", "    base[\"adj_margin\"] = np.where(\n", "        base[\"margin\"] <= 0, np.maximum(base[\"avg_asp\"] * default_margin, 0), base[\"margin\"]\n", "    )\n", "\n", "    # Upper cap calculations\n", "    print(\"Fetching ARS upper cap config\")\n", "    # ars_upper_cap_config = pb.from_sheets(config_sheet_id, 'ars_config')\n", "    ars_upper_cap_config = fetch_upper_cap_config(backend_facility_id=backend_facility_id)\n", "    ars_upper_cap_config = ars_upper_cap_config[\n", "        (ars_upper_cap_config[\"name\"] == \"rsto_upper_cap_config\")\n", "        & (ars_upper_cap_config[\"backend_facility_id\"].isin([backend_facility_id, 0]))\n", "    ]\n", "    upper_cap_values = json.loads(\n", "        ars_upper_cap_config.sort_values(\"backend_facility_id\", ascending=False)[\"value\"].iloc[0]\n", "    )\n", "    ars_default_doi = upper_cap_values.get(\"default_doi\")\n", "    ars_buffer_cap_doi = upper_cap_values.get(\"buffer_cap_doi\")\n", "    ars_buffer_quantity = upper_cap_values.get(\"buffer_quantity\")\n", "    ars_default_quantity = upper_cap_values.get(\"default_quantity\")\n", "    ars_qmax_default_doi_max = upper_cap_values.get(\"qmax_default_doi_max\")\n", "    ars_qmax_default_quantity = upper_cap_values.get(\"qmax_default_quantity\")\n", "\n", "    # No-truncation SKUs: override upper cap\n", "    print(\"Running fetch_no_truncation_skus\")  # global list already fetched\n", "    no_tuncation_skus = fetch_no_truncation_skus()\n", "    print(f\"Total number of no-truncation SKUs: {len(no_tuncation_skus.value.unique())}\")\n", "    seller_skus = fetch_seller_hub_skus()\n", "    print(f\"Total number of seller_skus:{len(seller_skus.item_id.unique())}\")\n", "\n", "    no_tuncation_skus = no_tuncation_skus.rename(columns={\"value\": \"item_id\"})\n", "    no_tuncation_skus = no_tuncation_skus.append(seller_skus)\n", "    no_tuncation_skus = no_tuncation_skus.drop_duplicates(keep=\"last\")\n", "    no_tuncation_skus = no_tuncation_skus.dropna()\n", "    no_tuncation_skus = no_tuncation_skus.astype({\"item_id\": int})\n", "    no_truncation_sku_list = tuple(no_tuncation_skus.item_id.unique())\n", "    print(f\"Total number of no-truncation SKUs: {len(no_truncation_sku_list)}\")\n", "    # (Seller SKUs included in no_truncation_sku_list)\n", "\n", "    base[\"qmax\"] = base[\"qmax\"].fillna(\n", "        np.maximum(base[\"aps_adjusted\"] * ars_default_doi, ars_default_quantity)\n", "    )\n", "    base[\"qmax\"] = np.where(base[\"qmax\"] == 0, ars_qmax_default_quantity, base[\"qmax\"])\n", "    base[\"buffer_doi\"] = ars_buffer_cap_doi * base[\"aps_adjusted\"]\n", "    base[\"upper_cap\"] = base[\"qmax\"] + np.maximum(base[\"buffer_doi\"], ars_default_quantity)\n", "    base[\"upper_cap\"] = np.where(\n", "        base[\"item_id\"].isin(no_truncation_sku_list),\n", "        np.maximum(base[\"aps_adjusted\"] * ars_default_doi, ars_default_quantity),\n", "        base[\"upper_cap\"],\n", "    )\n", "    base[\"upper_cap\"] = np.where(\n", "        base[\"master_assortment_substate_id\"].isin([1, 3]), base[\"upper_cap\"], 0\n", "    )\n", "\n", "    ### Excess inventory calculation\n", "    base[\"seq_inv\"] = base[\n", "        \"quantity\"\n", "    ]  # + base['expected_quantity'] - base['aps_adjusted']*(24.0/base['run_count'] + base['intransit_tat'])*(1.0/24)\n", "    base[\"excess_inv\"] = np.maximum(base[\"seq_inv\"] - base[\"upper_cap\"], 0)\n", "    base[\"excess_inv\"] = np.floor(base[\"excess_inv\"])\n", "    print(f\"Total excess inventory:{base['excess_inv'].sum()}\")\n", "\n", "    ### Cost calculation\n", "    base[\"storage_cost_of_excess_inv\"] = (\n", "        base[\"excess_inv\"]\n", "        * base[\"item_factor\"]\n", "        * storage_cost_per_if\n", "        * (base[\"excess_inv\"] / base[\"cpd\"])\n", "        * 0.5\n", "    )\n", "    base[\"capital_cost_of_excess_inv\"] = (\n", "        base[\"excess_inv\"] * base[\"avg_lp\"] * (1 + ir) ** (base[\"excess_inv\"] * 0.5 / base[\"cpd\"])\n", "        - base[\"excess_inv\"] * base[\"avg_lp\"]\n", "    )\n", "    base[\"holding_cost_of_excess_inv\"] = base[\n", "        \"storage_cost_of_excess_inv\"\n", "    ]  # + base['capital_cost_of_excess_inv']\n", "\n", "    ### Total_opportunity_cost\n", "    tot_opp_cost_df = base[base[\"seq_inv\"] == 0]\n", "    tot_opp_cost_df[\"min_inv\"] = np.maximum(\n", "        1, tot_opp_cost_df[\"potential_sale\"] * (1 / tot_opp_cost_df[\"run_count\"])\n", "    )\n", "    tot_opp_cost_df[\"min_inv_storage\"] = tot_opp_cost_df[\"min_inv\"] * tot_opp_cost_df[\"item_factor\"]\n", "    tot_opp_cost_df[\"opp\"] = tot_opp_cost_df[\"potential_sale\"] * tot_opp_cost_df[\"avg_asp\"]\n", "\n", "    tot_opp_cost_df = tot_opp_cost_df.sort_values(by=\"potential_sale\", ascending=False)\n", "    tot_opp_cost_df[\"cumsum_potential_sales\"] = tot_opp_cost_df.groupby(\n", "        [\"outlet_id\", \"final_storage_type\"]\n", "    )[\"potential_sale\"].cumsum()\n", "    tot_opp_cost_df[\"cumsum_min_storage_req\"] = tot_opp_cost_df.groupby(\n", "        [\"outlet_id\", \"final_storage_type\"]\n", "    )[\"min_inv_storage\"].cumsum()\n", "    temp_tot_opp_cost_df = (\n", "        tot_opp_cost_df.groupby([\"outlet_id\", \"final_storage_type\"])\n", "        .agg({\"potential_sale\": \"sum\", \"min_inv_storage\": \"sum\"})\n", "        .reset_index()\n", "        .rename(\n", "            columns={\n", "                \"potential_sale\": \"total_potential_sum\",\n", "                \"min_inv_storage\": \"total_min_inv_storage_sum\",\n", "            }\n", "        )\n", "    )\n", "    tot_opp_cost_df = tot_opp_cost_df.merge(\n", "        temp_tot_opp_cost_df[\n", "            [\"outlet_id\", \"final_storage_type\", \"total_potential_sum\", \"total_min_inv_storage_sum\"]\n", "        ],\n", "        on=[\"outlet_id\", \"final_storage_type\"],\n", "    )\n", "    tot_opp_cost_df[\"potential_sale_as_perc_of_total\"] = (\n", "        tot_opp_cost_df[\"cumsum_potential_sales\"] / tot_opp_cost_df[\"total_potential_sum\"]\n", "    )\n", "    tot_opp_cost_df[\"min_inv_storage_as_perc_of_total\"] = (\n", "        tot_opp_cost_df[\"cumsum_min_storage_req\"] / tot_opp_cost_df[\"total_min_inv_storage_sum\"]\n", "    )\n", "\n", "    tot_opp_cost_df.shape, tot_opp_cost_df.min_inv_storage.sum(), tot_opp_cost_df[\n", "        tot_opp_cost_df[\"cumsum_potential_sales\"]\n", "        < tot_opp_cost_df[\"total_potential_sum\"] * sku_cutoff_on_potential_sales_of_unavail_skus\n", "    ].shape, tot_opp_cost_df[\n", "        tot_opp_cost_df[\"cumsum_potential_sales\"]\n", "        < tot_opp_cost_df[\"total_potential_sum\"] * sku_cutoff_on_potential_sales_of_unavail_skus\n", "    ].min_inv_storage.sum()\n", "    tot_opp_cost_df = tot_opp_cost_df[\n", "        tot_opp_cost_df[\"cumsum_potential_sales\"]\n", "        < tot_opp_cost_df[\"total_potential_sum\"] * sku_cutoff_on_potential_sales_of_unavail_skus\n", "    ]\n", "\n", "    opp_cost_df = (\n", "        tot_opp_cost_df.groupby([\"outlet_id\", \"final_storage_type\"])\n", "        .agg(\n", "            {\n", "                \"item_id\": \"count\",\n", "                \"cpd\": \"median\",\n", "                \"aps_adjusted\": \"median\",\n", "                \"item_factor\": \"median\",\n", "                \"adj_margin\": \"median\",\n", "                \"avg_asp\": \"median\",\n", "                \"avg_lp\": \"median\",\n", "                \"opp\": \"sum\",\n", "                \"item_id\": \"count\",\n", "                \"min_inv_storage\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "        .rename(\n", "            columns={\n", "                \"item_id\": \"unavl_sku_count\",\n", "                \"cpd\": \"base_cpd\",\n", "                \"aps_adjusted\": \"base_aps\",\n", "                \"item_factor\": \"base_if_\",\n", "                \"adj_margin\": \"base_margin\",\n", "                \"avg_asp\": \"base_asp\",\n", "                \"avg_lp\": \"base_lp\",\n", "            }\n", "        )\n", "    )\n", "\n", "    opp_cost_df = opp_cost_df.merge(\n", "        truncation_df[[\"outlet_id\", \"storage_drop_contribution\", \"final_storage_type\"]],\n", "        on=[\"outlet_id\", \"final_storage_type\"],\n", "        how=\"left\",\n", "    )\n", "    opp_cost_df = opp_cost_df.astype({\"storage_drop_contribution\": \"float\"})\n", "    opp_cost_df[\"storage_drop_contribution\"] = opp_cost_df[\"storage_drop_contribution\"].fillna(\n", "        minimum_storage_truncation_contribution\n", "    )\n", "    opp_cost_df[\"minimum_storage_drop_contribution\"] = np.maximum(\n", "        opp_cost_df[\"storage_drop_contribution\"], minimum_storage_truncation_contribution\n", "    )\n", "\n", "    print(\n", "        opp_cost_df.groupby([\"final_storage_type\"])\n", "        .minimum_storage_drop_contribution.mean()\n", "        .sort_values()\n", "    )\n", "\n", "    base = base.merge(opp_cost_df, how=\"left\", on=[\"outlet_id\", \"final_storage_type\"])\n", "    base[\"base_inventory\"] = base[\"excess_inv\"] * base[\"item_factor\"] * 0.5 / base[\"base_if_\"]\n", "    base[\"base_return\"] = base[\"base_inventory\"] * base[\"base_margin\"]\n", "    base[\"base_storage_cost\"] = (\n", "        base[\"base_inventory\"]\n", "        * base[\"base_if_\"]\n", "        * storage_cost_per_if\n", "        * (base[\"base_inventory\"] / base[\"base_aps\"])\n", "        * 0.5\n", "    )\n", "    base[\"base_capital_cost\"] = (\n", "        base[\"base_inventory\"]\n", "        * base[\"base_lp\"]\n", "        * (1 + ir) ** ((base[\"base_inventory\"] / base[\"base_aps\"]) * 0.5)\n", "        - base[\"base_inventory\"] * base[\"base_lp\"]\n", "    )\n", "    base[\"base_holding_cost\"] = base[\"base_storage_cost\"]  # + base['base_capital_cost']\n", "    base = base.astype(\n", "        {\"minimum_storage_drop_contribution\": float, \"holding_cost_of_excess_inv\": float}\n", "    )\n", "    base[\"opp_cost\"] = base[\"base_return\"] - base[\"base_holding_cost\"]\n", "    base[\"opp_cost_due_to_storage\"] = (\n", "        opportunity_cost_multiplier * base[\"opp_cost\"] * base[\"minimum_storage_drop_contribution\"]\n", "    )\n", "    base[\"keeping_cost\"] = base[\"opp_cost_due_to_storage\"] + base[\"holding_cost_of_excess_inv\"]\n", "    opp_cost_df[\"storage_drop_contribution\"] = (\n", "        opp_cost_df[\"opp\"] * opp_cost_df[\"storage_drop_contribution\"]\n", "    )\n", "    opp_cost_df.sort_values(by=\"minimum_storage_drop_contribution\", ascending=False).head()\n", "\n", "    ###Cost of rsto till WH = picking at DS + transportation cost to WH + IB cost at WH (QC + GRN) + intransit_cost\n", "    ###\n", "    ###Intransit_damage_cost = Landing_price*excess_inventory*(Avg % damge for L2 in past x days)\n", "    ###\n", "    ###Transportation cost to WH  = 0\n", "    ###\n", "    ###If RSTO is for RTV (RTV elgibile item and inactive across give BE<>FE network)\n", "    ###\n", "    ###Cost of RTV = Cost of rsto till WH - Excess_Quantity*(Avg % reliased inventory at BE)*(Landing_price)*(return % from vendor)\n", "\n", "    # mp_cost = (Mp_monthly_cost/26)*( 1/(ds_ipp) + 1/(wh_ipp) + (1/wh_grn_ipp))\n", "    mp_cost_rtso = (18000 / 26) * (1 / 320 + 1 / 1800 + 1 / 600)\n", "\n", "    base = base.merge(outlet_list_df)\n", "\n", "    dump_df = fetch_rsto_dump()\n", "    print(\"Dump fetched\")\n", "    print(dump_df.columns)\n", "\n", "    dump_df_agg = (\n", "        dump_df.groupby([\"l1\"])\n", "        .agg({\"rsto_dump_dmg_amt\": \"sum\", \"rsto_billed_amt\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "    dump_df_agg[\"perc_damage\"] = (\n", "        1.0 * dump_df_agg[\"rsto_dump_dmg_amt\"].fillna(0) / dump_df_agg[\"rsto_billed_amt\"]\n", "    )\n", "    dump_df_agg[\"perc_damage\"] = dump_df_agg[\"perc_damage\"].fillna(minimum_intransit_loss_value)\n", "    dump_df_agg[\"adj_perc_damage\"] = np.maximum(\n", "        dump_df_agg[\"perc_damage\"], minimum_intransit_loss_value\n", "    )\n", "    base = base.merge(df_cat[[\"item_id\", \"l1\", \"l2\"]], on=[\"item_id\"], how=\"left\")\n", "    base = base.merge(dump_df_agg, on=[\"l1\"], how=\"left\")\n", "    base[[\"rsto_dump_dmg_amt\", \"rsto_billed_amt\"]] = base[\n", "        [\"rsto_dump_dmg_amt\", \"rsto_billed_amt\"]\n", "    ].fillna(0)\n", "\n", "    base[\"mp_cost\"] = mp_cost_rtso\n", "    base[\"exp_intransit_dmg\"] = base[\"excess_inv\"] * base[\"avg_lp\"] * base[\"adj_perc_damage\"]\n", "    base[\"cost_of_rsto\"] = mp_cost_rtso + base[\"exp_intransit_dmg\"]\n", "    base.sort_values(by=\"cost_of_rsto\", ascending=False).shape\n", "\n", "    #### Add cost for RTV/ Bias for RSTOs of RTV\n", "    ##### Cost of RTV = Cost of rsto till WH - Excess_Quantity*(Avg % reliased inventory at BE)*(Landing_price)*(return % from vendor)\n", "\n", "    base[\"rtv_potential\"] = np.where(\n", "        (base[\"rtv_flag\"] == 1 | base[\"master_assortment_substate_id\"].isin([2, 4])), 1, 0\n", "    )\n", "    rtv_elg = (\n", "        base[base[\"excess_inv\"] > 0]\n", "        .groupby([\"item_id\", \"be_facility_id\"])\n", "        .agg({\"rtv_potential\": \"sum\", \"outlet_id\": \"count\"})\n", "        .reset_index()\n", "        .rename(columns={\"rtv_potential\": \"total_rtv_potential_line\", \"outlet_id\": \"total_lines\"})\n", "    )\n", "    base = base.merge(rtv_elg, on=[\"item_id\", \"be_facility_id\"], how=\"left\")\n", "    base[[\"total_rtv_potential_line\", \"total_lines\"]] = base[\n", "        [\"total_rtv_potential_line\", \"total_lines\"]\n", "    ].fillna(0)\n", "\n", "    base[\"rtv_elg\"] = np.where(\n", "        (base[\"total_rtv_potential_line\"] == base[\"total_lines\"])\n", "        & (base[\"total_rtv_potential_line\"] > 0),\n", "        1,\n", "        0,\n", "    )\n", "    base[\"rtv_contribution\"] = (\n", "        base[\"excess_inv\"] * base[\"rtv_elg\"] * base[\"avg_lp\"] * rtv_contribution_perc\n", "    )\n", "    base[\"cost_of_rsto_inc_rtv\"] = base[\"cost_of_rsto\"] - base[\"rtv_contribution\"]\n", "    # base.sort_values(by = 'cost_of_rsto_inc_rtv', ascending = False).shape\n", "\n", "    base[\"final_cost_of_rsto_inc_rtv\"] = np.maximum(base[\"cost_of_rsto_inc_rtv\"], mp_cost_rtso)\n", "    base[\"rsto_rtv_reward_risk_ratio\"] = base[\"keeping_cost\"] / base[\"final_cost_of_rsto_inc_rtv\"]\n", "    base[\"rsto_reward_risk_ratio\"] = base[\"keeping_cost\"] / base[\"cost_of_rsto\"]\n", "\n", "    base[\"eligible_for_rsto\"] = 1\n", "    base[\"eligible_for_rsto\"] = np.where(\n", "        (base[\"excess_inv\"] < 1)\n", "        | (base[\"exclusion_type\"] != \"NO_RSTO_EXCLUSION\")\n", "        | (base[\"day_count\"] < minimum_avail_day_count_check),\n", "        0,\n", "        base[\"eligible_for_rsto\"],\n", "    )\n", "\n", "    base = base.sort_values(\n", "        by=[\"eligible_for_rsto\", \"rsto_reward_risk_ratio\"], ascending=[False, False]\n", "    )\n", "    # base = base[(base['exclusion_type']=='NO_RSTO_EXCLUSION')&(base['day_count']>=minimum_avail_day_count_check)]\n", "\n", "    base[\"rnk\"] = base[\"rsto_reward_risk_ratio\"].rank()\n", "    max_rank = base[\"rnk\"].max()\n", "    base[\"rnk\"] = max_rank + 1 - base[\"rnk\"]\n", "\n", "    base[\"rsto_storage\"] = base[\"excess_inv\"] * base[\"item_factor\"]\n", "    base[\"cumsum_storage_created\"] = base.groupby([\"outlet_id\"])[\"rsto_storage\"].cumsum()\n", "    # base['create_rsto'] = np.where(base['cumsum_storage_created']<=base['min_inv_storage'], 1, 0)\n", "\n", "    base[\"create_rsto\"] = np.where(\n", "        (base[\"cumsum_storage_created\"] <= base[\"min_inv_storage\"])\n", "        & (base[\"rsto_reward_risk_ratio\"] >= risk_reward_threshold)\n", "        & (base[\"eligible_for_rsto\"] == 1),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    base[\"reason_for_ineligibility\"] = \"NA\"\n", "    base[\"reason_for_ineligibility\"] = np.where(\n", "        base[\"eligible_for_rsto\"] == 1,\n", "        \"NA\",\n", "        np.where(\n", "            base[\"excess_inv\"] < 1,\n", "            \"NO_EXCESS_INVENTORY\",\n", "            np.where(\n", "                base[\"exclusion_type\"] != \"NO_RSTO_EXCLUSION\",\n", "                base[\"exclusion_type\"],\n", "                np.where(\n", "                    base[\"day_count\"] < minimum_avail_day_count_check,\n", "                    \"ITEM_NOT_LIVE\",\n", "                    base[\"reason_for_ineligibility\"],\n", "                ),\n", "            ),\n", "        ),\n", "    )\n", "\n", "    print(\n", "        base.groupby([\"create_rsto\", \"reason_for_ineligibility\"])\n", "        .agg({\"item_id\": \"count\", \"rsto_storage\": \"sum\", \"rsto_storage\": \"sum\"})\n", "        .sort_values(by=[\"create_rsto\"])\n", "    )\n", "\n", "    print(\"-------FINAL RSTO ELIGIBILE SKUs-------\")\n", "    print(\n", "        base.groupby([\"create_rsto\", \"final_storage_type\"])\n", "        .agg({\"item_id\": \"count\", \"rsto_storage\": \"sum\", \"rsto_storage\": \"sum\"})\n", "        .sort_values(by=[\"create_rsto\", \"final_storage_type\"])\n", "    )\n", "\n", "    base[\"create_rsto\"] = create_rsto\n", "    # print(base.dtypes)\n", "\n", "    base = base[\n", "        [\n", "            \"outlet_id\",\n", "            \"facility_id\",\n", "            \"outlet_name\",\n", "            \"item_id\",\n", "            \"master_assortment_substate_id\",\n", "            \"substate_reason_id\",\n", "            \"reason_type\",\n", "            \"reason_text\",\n", "            \"quantity\",\n", "            \"exclusion_type\",\n", "            \"day_count\",\n", "            \"cpd\",\n", "            \"aps_adjusted\",\n", "            \"potential_sale\",\n", "            \"for_date\",\n", "            \"rnk\",\n", "            \"name\",\n", "            \"storage_type\",\n", "            \"current_tagging\",\n", "            \"derived_storage_type\",\n", "            \"weight_in_gm\",\n", "            \"city_id\",\n", "            \"city\",\n", "            \"live_day_count\",\n", "            \"live_day_count_bucket\",\n", "            \"item_factor\",\n", "            \"tag_value\",\n", "            \"be_name\",\n", "            \"be_facility_id\",\n", "            \"inv_id\",\n", "            \"be_inv\",\n", "            \"sor\",\n", "            \"rtv\",\n", "            \"rtv_flag\",\n", "            \"avg_lp\",\n", "            \"mrp\",\n", "            \"avg_asp\",\n", "            \"margin\",\n", "            \"qmax\",\n", "            \"run_count\",\n", "            \"intransit_tat\",\n", "            \"expected_quantity\",\n", "            \"is_storage_type_avail\",\n", "            \"final_storage_type\",\n", "            \"adj_margin\",\n", "            \"buffer_doi\",\n", "            \"upper_cap\",\n", "            \"seq_inv\",\n", "            \"excess_inv\",\n", "            \"storage_cost_of_excess_inv\",\n", "            \"capital_cost_of_excess_inv\",\n", "            \"holding_cost_of_excess_inv\",\n", "            \"unavl_sku_count\",\n", "            \"base_cpd\",\n", "            \"base_aps\",\n", "            \"base_if_\",\n", "            \"base_margin\",\n", "            \"base_asp\",\n", "            \"base_lp\",\n", "            \"opp\",\n", "            \"min_inv_storage\",\n", "            \"storage_drop_contribution\",\n", "            \"minimum_storage_drop_contribution\",\n", "            \"base_inventory\",\n", "            \"base_return\",\n", "            \"base_storage_cost\",\n", "            \"base_capital_cost\",\n", "            \"base_holding_cost\",\n", "            \"opp_cost\",\n", "            \"opp_cost_due_to_storage\",\n", "            \"keeping_cost\",\n", "            \"rsto_dump_dmg_amt\",\n", "            \"rsto_billed_amt\",\n", "            \"perc_damage\",\n", "            \"adj_perc_damage\",\n", "            \"mp_cost\",\n", "            \"exp_intransit_dmg\",\n", "            \"cost_of_rsto\",\n", "            \"rtv_potential\",\n", "            \"total_rtv_potential_line\",\n", "            \"total_lines\",\n", "            \"rtv_elg\",\n", "            \"rtv_contribution\",\n", "            \"cost_of_rsto_inc_rtv\",\n", "            \"final_cost_of_rsto_inc_rtv\",\n", "            \"rsto_rtv_reward_risk_ratio\",\n", "            \"rsto_reward_risk_ratio\",\n", "            \"rsto_storage\",\n", "            \"cumsum_storage_created\",\n", "            \"create_rsto\",\n", "            \"eligible_for_rsto\",\n", "            \"reason_for_ineligibility\",\n", "        ]\n", "    ]\n", "\n", "    upload_to_trino_table_raw(base)\n", "    print(\"DATA UPLOADED!\")\n", "    print(f\"--------------------RUN COMPLETED FOR {backend_facility_id}---------------\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}