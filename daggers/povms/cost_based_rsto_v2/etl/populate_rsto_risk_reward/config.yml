alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: populate_rsto_risk_reward
dag_type: etl
escalation_priority: low
execution_timeout: 240
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03SA4412F4
path: povms/cost_based_rsto_v2/etl/populate_rsto_risk_reward
paused: false
pool: povms_pool
project_name: cost_based_rsto_v2
schedule:
  end_date: '2025-08-19T00:00:00'
  interval: 5 19 * * *
  start_date: '2025-06-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
