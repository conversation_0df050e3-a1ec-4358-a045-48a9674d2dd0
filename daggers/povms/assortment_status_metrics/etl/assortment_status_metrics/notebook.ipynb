{"cells": [{"cell_type": "code", "execution_count": null, "id": "ca31b155-65ca-48ea-8a57-5ab734bb0ffb", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "fe6957c4-840e-4aef-8ccc-4d3fb41f31a8", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "d0446f05-cad9-4874-ac9d-8833919b1dad", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b97db247-694b-4753-916f-203a1c8cce25", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e29a451d-947a-4894-9714-f96aaf6ddf60", "metadata": {}, "outputs": [], "source": ["final_query = f\"\"\"\n", "with \n", "outlet_details as (\n", "    select --current_date as updated_at, \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        and taggings = 'be' \n", "        -- and store_type = 'Packaged Goods'\n", "        and facility_id <> 1)\n", "),\n", "\n", "\n", "items as (\n", "    select --current_date as updated_at, \n", "        item_id, item_name, l0_id, l0_category, \n", "        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type, \n", "        manufacturer_name, manufacturer_id, brand_id, brand_name\n", "    from supply_etls.item_details\n", "    where \n", "    -- assortment_type='Packaged Goods'\n", "    handling_type = 'Non Packaging Material'\n", "),\n", "\n", "item_details as(\n", "    select *\n", "    from(\n", "        select\n", "            icd.item_id, icd.name as item_name, \n", "            icd.l0_id, icd.l0 as l0_category, \n", "            icd.l1_id, icd.l1 as l1_category, \n", "            icd.l2_id, icd.l2 as l2_category,\n", "            icd.product_type_id, icd.product_type as p_type,\n", "            case\n", "                when id.perishable = 1 then 'PERISHABLE'\n", "                else 'PACKAGED'\n", "            end as item_type,\n", "            id.storage_type as storage_type_raw,\n", "            case\n", "                when id.storage_type in ('1','8','11') then 'REGULAR'\n", "                when id.storage_type in ('4', '5') then 'HEAVY'\n", "                when id.storage_type in ('2', '6') then 'COLD'\n", "                when id.storage_type in ('3', '7') then 'FROZEN'\n", "                else 'REGULAR'\n", "            end as storage_type,\n", "            case\n", "                when id.handling_type in ('8') then 'PACKAGING MATERIAL'\n", "                when id.handling_type in ('6') then 'MEDICINAL'\n", "                else 'REGULAR'\n", "            end as handling_type,\n", "            id.variant_mrp as mrp, id.variant_description, id.weight_in_gm, id.length_in_cm, id.height_in_cm, id.breadth_in_cm, id.shelf_life,\n", "            coalesce(itf.item_factor, 0.01) as item_factor,\n", "            date_diff('day', date(icd.created_at), current_date) as item_catalog_age,\n", "            rank() over (partition by id.item_id order by id.id desc) as variant_rank,\n", "            itm.tag_value as custom_storage_type_raw,\n", "            case\n", "                when itm.tag_value = '1' then 'BEAUTY'\n", "                when itm.tag_value = '2' then 'BOUQUET'\n", "                when itm.tag_value = '3' then 'PREMIUM'\n", "                when itm.tag_value = '4' then 'BOOKS'\n", "                when itm.tag_value = '5' then 'NON_VEG'\n", "                when itm.tag_value = '6' then 'ICE_CREAM'\n", "                when itm.tag_value = '7' then 'TOYS'\n", "                when itm.tag_value = '8' then 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "                when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "                when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "                when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "                when itm.tag_value = '13' then 'LARGE'\n", "                when itm.tag_value = '14' then 'APPAREL'\n", "                when itm.tag_value = '15' then 'SPORTS'\n", "                when itm.tag_value = '16' then 'PET_CARE'\n", "                when itm.tag_value = '17' then 'HOME_DECOR'\n", "                when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "                when itm.tag_value = '19' then 'HOME_FURNISHING'\n", "                when itm.tag_value = '20' then 'LONGTAIL_OTHERS'\n", "                when itm.tag_value = '21' then 'PHARMA'\n", "                when itm.tag_value = '22' then 'PAAS'\n", "                when itm.tag_value = '23' then 'PALLET'\n", "                when itm.tag_value = '24' then 'LARGE_FRAGILE'\n", "                when itm.tag_value is null then 'NO_CONFIG'\n", "                else 'UNKNOWN_CONFIG'\n", "            end as custom_storage_type,\n", "            pb.manufacturer_id, id.manufacturer as manufacturer_name, pb.name as brand_name,\n", "            coalesce(id.outer_case_size, 1) as outer_case_size,\n", "            coalesce(id.inner_case_size, 1) as inner_case_size,\n", "            case\n", "                when itm2.tag_value = '1' then true\n", "                else false\n", "            end as is_high_value,\n", "            itm2.tag_value as high_value_tag_raw,\n", "            case\n", "                when itm3.tag_value = '1' then 'upc scan'\n", "                when itm3.tag_value = '2' then 'serial scan'\n", "                when itm3.tag_value = '3' then 'qr scan'\n", "                when itm3.tag_value = '4' then 'no scan'\n", "                else 'unknown'\n", "            end as scan_type,\n", "            itm3.tag_value as scan_type_raw, cms_food_type\n", "        from rpc.item_category_details icd\n", "        join rpc.product_product id on id.item_id = icd.item_id\n", "            and id.active = 1\n", "            and id.approved = 1\n", "            and id.lake_active_record\n", "        left join supply_etls.item_factor itf on itf.item_id = icd.item_id\n", "        left join rpc.item_tag_mapping itm on itm.tag_type_id = 7\n", "            and itm.active = true\n", "            and itm.lake_active_record\n", "            and itm.item_id = icd.item_id\n", "        left join rpc.product_brand pb on id.brand_id = pb.id\n", "            and pb.lake_active_record\n", "            and pb.active = 1\n", "        left join rpc.item_tag_mapping itm2 on itm2.item_id = icd.item_id\n", "            and itm2.active\n", "            and itm2.tag_type_id = 3\n", "            and itm2.lake_active_record\n", "        left join rpc.item_tag_mapping itm3 on itm3.item_id = icd.item_id\n", "            and itm3.active\n", "            and itm3.tag_type_id = 5\n", "            and itm3.lake_active_record\n", "        where icd.lake_active_record\n", "            and perishable != 1\n", "            and id.handling_type != '8'\n", "            and id.storage_type NOT IN ('3', '7')\n", "            and icd.l0_id != 1487 -- removing vegetables and fruits\n", "            and icd.l0 not in ('wholesale store','Trial new tree','Specials') -- removing test and flyer/freebie l0s\n", "        ) x\n", "    where variant_rank = 1\n", "),\n", "\n", "tea_tagging as (\n", "    select --current_date as updated_at, \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, fe_city_id, fe_city_name, fe_facility_name, entry_updated_at_ist, entry_updated_by,\n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id, store_assortment_type\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "block_invt as ( \n", "    select \n", "        outlet_id, item_id, sum(quantity) as blocked_inventory\n", "    from ims.ims_item_blocked_inventory\n", "    where lake_active_record = true and active = 1 and quantity > 0\n", "    group by 1,2\n", "),\n", "\n", "outlet_inventory_info_base as ( \n", "    select  i.outlet_id, i.item_id, \n", "        sum(case when i.quantity > 0 THEN i.quantity else 0 end) as onshelf_invt,\n", "        sum(b.blocked_inventory) as block_invt,\n", "        sum(case when (i.quantity - coalesce(b.blocked_inventory,0) > 0) then (i.quantity - coalesce(b.blocked_inventory,0)) else 0 end) as net_inv,\n", "        sum((case when (i.quantity - coalesce(b.blocked_inventory,0) > 0) then (i.quantity - coalesce(b.blocked_inventory,0)) else 0 end) * imis.weighted_lp) as net_inv_value\n", "    from ims.ims_item_inventory i\n", "    left join block_invt b on i.item_id = b.item_id \n", "        and i.outlet_id = b.outlet_id\n", "    left join supply_etls.inventory_metrics_inventory_snapshot imis on imis.insert_ds_ist=current_date \n", "        and hour_=hour(current_timestamp) - 1 \n", "        and store_type<>'Dark Store' \n", "        and imis.inv_outlet_id=i.outlet_id \n", "        and imis.item_id=i.item_id\n", "    where i.active = 1 and i.lake_active_record = true\n", "    group by 1,2\n", "),\n", "\n", "inventory_fe_all as(\n", "    select try_cast(item_id as int) as item_id, \n", "        try_cast(outlet_id as int) as outlet_id, \n", "        sum(quantity) as quantity\n", "    from dynamodb.blinkit_store_inventory_service_oi_rt_view_v2\n", "    where quantity > 0 \n", "        and state = 'GOOD' \n", "    group by 1,2\n", "),\n", "\n", "inventory_fe_blocked as(\n", "    select try_cast(item_id as int) as item_id, \n", "        try_cast(outlet_id as int) as outlet_id,\n", "        sum(quantity) as blocked_inv\n", "    from dynamodb.blinkit_store_inventory_service_blk_rt_view_v2\n", "    where status = 'BLOCKED'\n", "        and quantity > 0\n", "    group by 1, 2\n", "),\n", "\n", "outlet_inventory_info_fe as(\n", "    select ifa.item_id,\n", "        ifa.outlet_id,\n", "        greatest(ifa.quantity - coalesce(ifb.blocked_inv, 0), 0) as net_inv_fe  \n", "    from inventory_fe_all ifa  \n", "    left join inventory_fe_blocked ifb on ifb.item_id = ifa.item_id and ifb.outlet_id = ifa.outlet_id\n", "),\n", "\n", "po_open as (\n", "    select item_id, outlet_id,\n", "        sum(remaining_po_quantity) as total_po_quantity,\n", "        sum(remaining_po_quantity*po_landing_price) as total_po_value,\n", "        sum(case when (date(po_schedule_at_ist) = current_date) then remaining_po_quantity else 0 end) as t_scheduled_quantity,\n", "        sum(case when (date(po_schedule_at_ist) = current_date + interval '1' day) then remaining_po_quantity else 0 end) as t1_scheduled_quantity,\n", "        sum(case when (date(po_schedule_at_ist) = current_date + interval '2' day) then remaining_po_quantity else 0 end) as t2_scheduled_quantity,\n", "        sum(case when (date(po_schedule_at_ist) = current_date + interval '3' day) then remaining_po_quantity else 0 end) as t3_scheduled_quantity\n", "    from supply_etls.inventory_metrics_open_po\n", "    where remaining_po_quantity > 0 -- not needed\n", "    group by 1,2\n", "),\n", "\n", "merchant_outlet_mapping as(\n", "    select distinct frontend_merchant_id, facility_id \n", "    from dwh.dim_merchant_outlet_facility_mapping\n", "    where is_current \n", "        and is_mapping_enabled\n", "),\n", "\n", "assortment_status_be as (\n", "    select tea.item_id, tea.be_facility_id,\n", "        case when sum(case when assortment_status_id = 1 then 1 else 0 end) >= 1 then 1\n", "            when sum(case when assortment_status_id = 3 then 1 else 0 end) >= 1 then 3\n", "            when sum(case when assortment_status_id = 2 then 1 else 0 end) >= 1 then 2\n", "            else 4 end as assortment_status_id,\n", "        case when sum(case when pfma.launch_type = 'HYBRID' then 1 else 0 end) >= 1 then 'HYBRID'\n", "            when sum(case when tea.store_assortment_type = 'UNICORN' then 1 else 0 end) >= 1 then 'UNICORN'\n", "            when sum(case when tea.store_assortment_type = 'SUPER_LONGTAIL' then 1 else 0 end) >= 1 then 'SUPER_LONGTAIL'\n", "            when sum(case when tea.store_assortment_type = 'LONGTAIL' then 1 else 0 end) >= 1 then 'LONGTAIL'\n", "            else 'EXPRESS' end as store_assortment_type\n", "    from tea_tagging tea\n", "    left join rpc.product_facility_master_assortment pfma on tea.item_id = pfma.item_id and tea.fe_facility_id = pfma.facility_id\n", "    group by 1,2\n", "),\n", "\n", "base as(\n", "    select\n", "        id.item_id,\n", "        id.item_name,\n", "        od.facility_id as be_facility_id,\n", "        od.facility_name as be_facility_name,\n", "        tea.fe_city_id as city_id,\n", "        tea.fe_city_name as city_name,\n", "        tea.fe_facility_id,\n", "        tea.fe_facility_name,\n", "        mom.frontend_merchant_id,\n", "        tea.store_assortment_type,\n", "        tea.assortment_status_id,\n", "        -- tea.entry_updated_at_ist,\n", "        -- tea.entry_updated_by,\n", "        oiif.net_inv_fe,\n", "        oiib.net_inv as net_inv_be,\n", "        poo.total_po_quantity as open_po_qty,\n", "        asb.assortment_status_id as assortment_status_id_be,\n", "        asb.store_assortment_type as store_assortment_type_be\n", "    from outlet_details od\n", "    cross join items id\n", "    left join tea_tagging tea on tea.item_id = id.item_id \n", "        and tea.be_facility_id = od.facility_id\n", "    left join outlet_inventory_info_base oiib on oiib.outlet_id = od.inv_outlet_id \n", "        and id.item_id = oiib.item_id\n", "    left join outlet_inventory_info_fe oiif on oiif.outlet_id = tea.fe_outlet_id\n", "        and oiif.item_id = id.item_id    \n", "    left join po_open poo on poo.item_id = id.item_id\n", "        and poo.outlet_id = od.hot_outlet_id\n", "    left join merchant_outlet_mapping mom on mom.facility_id = tea.fe_facility_id\n", "    left join assortment_status_be asb on asb.item_id = id.item_id \n", "        and asb.be_facility_id = od.facility_id\n", "    where oiib.item_id is not null\n", "        or oiif.item_id is not null\n", "        or poo.item_id is not null\n", "        or asb.assortment_status_id = 1\n", "),\n", "\n", "request as (\n", "    select item_id, facility_id, max_by(reason, updated_at) as reason, max_by(job_id, updated_at) as job_id\n", "    from(\n", "        select  \n", "            item_id,\n", "            facility_id,\n", "            state,\n", "            date(updated_at) as updated_at,\n", "            assortment_type,\n", "            request_type,\n", "            try_cast(json_extract(meta, '$.request_reason_type') as varchar) as reason,\n", "            request_id as job_id,\n", "            rank() over(partition by item_id, facility_id order by updated_at desc) as update_rank,\n", "            created_by\n", "        from rpc.item_facility_state_request\n", "        where insert_ds_ist >= cast(current_date - interval '365' day as varchar)\n", "            and active = 1\n", "            and state in (1,2,3,4)\n", "            and status = 'COMPLETED'\n", "            and not (state = 4 and created_by = 14)\n", "    ) as x\n", "    where update_rank = 1\n", "    group by 1,2\n", "),\n", "\n", "entry_update as(\n", "select \n", "    pfma.item_id, \n", "    pfma.facility_id, \n", "    pfma.updated_at, \n", "    au.email\n", "from rpc.product_facility_master_assortment pfma\n", "left join (select distinct id, email from retail.auth_user) au on au.id = pfma.updated_by\n", "),\n", "\n", "item_fe as(\n", "    select\n", "        b.item_id,\n", "        b.item_name,\n", "        b.be_facility_id,\n", "        b.be_facility_name,\n", "        b.city_id,\n", "        b.city_name,\n", "        b.fe_facility_id,\n", "        b.fe_facility_name,\n", "        b.store_assortment_type,\n", "        b.assortment_status_id,\n", "        (eu.updated_at + interval '330' minute) as entry_updated_at_ist,\n", "        eu.email as entry_updated_by,\n", "        r.reason as request_reason,\n", "        r.job_id,\n", "        b.net_inv_fe,\n", "        b.net_inv_be,\n", "        b.open_po_qty\n", "\n", "    from base b\n", "    left join request r on r.item_id = b.item_id \n", "        and r.facility_id = b.fe_facility_id\n", "    left join entry_update eu on eu.item_id = b.item_id\n", "        and eu.facility_id = b.fe_facility_id\n", "),\n", "\n", "city_item_coverage as (\n", "    select\n", "        cac.insert_ds_ist,\n", "        case when cac.city_name = 'UP-NCR' then 'Ghaziabad'\n", "            when cac.city_name = 'HR-NCR' then 'Gurgaon'\n", "            else cac.city_name\n", "        end as city_name,\n", "        cac.item_id,\n", "        cast(cac.item_coverage as decimal(10, 4)) as item_coverage,\n", "        case\n", "            when coalesce(cac.num_unicorn_merchants, 0) > 0 then 'UNICORN'\n", "            when coalesce(cac.num_super_longtail_merchants, 0) > 0 then 'SUPER_LONGTAIL'\n", "            when (coalesce(cac.num_longtail_merchants, 0) > 0) and (coalesce(cac.num_express_merchants, 0) > 0) then 'HYBRID'\n", "            when coalesce(cac.num_longtail_merchants, 0) > 0 then 'LONGTAIL'\n", "            when coalesce(cac.num_express_merchants, 0) > 0 then 'EXPRESS'\n", "        end as assortment_type,\n", "        max(item_coverage) over (partition by city_name) as max_city_coverage,\n", "        max(item_coverage) over (partition by city_name, case\n", "                                                            when coalesce(cac.num_unicorn_merchants, 0) > 0 then 'UNICORN'\n", "                                                            when coalesce(cac.num_super_longtail_merchants, 0) > 0 then 'SUPER_LONGTAIL'\n", "                                                            when (coalesce(cac.num_longtail_merchants, 0) > 0) and (coalesce(cac.num_express_merchants, 0) > 0) then 'HYBRID'\n", "                                                            when coalesce(cac.num_longtail_merchants, 0) > 0 then 'LONGTAIL'\n", "                                                            when coalesce(cac.num_express_merchants, 0) > 0 then 'EXPRESS'\n", "                                                         end) as max_city_assortment_coverage\n", "    from supply_etls.city_assortment_coverage cac\n", "    where cac.insert_ds_ist = current_date\n", "        and cac.city_name is not null\n", "        and (coalesce(cac.num_express_merchants, 0) + coalesce(cac.num_longtail_merchants, 0) + coalesce(cac.num_super_longtail_merchants, 0) + coalesce(cac.num_unicorn_merchants, 0)) > 0\n", "        and cac.item_id not in (\n", "          select\n", "            distinct item_id\n", "          from\n", "            rpc.item_tag_mapping\n", "          where\n", "            tag_type_id = 11\n", "            and tag_value = '2'\n", "            and active\n", "            and lake_active_record\n", "        )\n", "),\n", "\n", "item_city as(\n", "    select\n", "        b.item_id,\n", "        b.item_name,\n", "        b.city_id,\n", "        b.city_name,\n", "        case when sum(case when store_assortment_type = 'UNICORN' then 1 else 0 end) > 0 then 'UNICORN'\n", "             when sum(case when store_assortment_type = 'SUPER_LONGTAIL' then 1 else 0 end) > 0 then 'SUPER_LONGTAIL'\n", "             when (sum(case when store_assortment_type = 'LONGTAIL' then 1 else 0 end) > 0) and (sum(case when store_assortment_type = 'EXPRESS' then 1 else 0 end) > 0) then 'HYBRID'\n", "             when sum(case when store_assortment_type = 'LONGTAIL' then 1 else 0 end) > 0 then 'LONGTAIL'\n", "             else 'EXPRESS'\n", "        end as store_assortment_type,\n", "        case when sum(case when assortment_status_id = 1 then 1 else 0 end) > 0 then 1\n", "             when sum(case when assortment_status_id = 3 then 1 else 0 end) > 0 then 3\n", "             when sum(case when assortment_status_id = 2 then 1 else 0 end) > 0 then 2\n", "             else 4\n", "        end as assortment_status_id,\n", "        sum(b.net_inv_fe) as net_inv_fe,\n", "        max(b.net_inv_be) as net_inv_be,\n", "        max(b.open_po_qty) as open_po_qty,\n", "        \n", "        count(b.fe_facility_id) as ds_conn_city,\n", "        sum(case when b.assortment_status_id in (2,4) then 1 else 0 end) as inactive_ds_conn_city,\n", "        sum(case when b.store_assortment_type = 'EXPRESS' and b.assortment_status_id in (1,3) then 1 else 0 end) as express_ds_conn_city,\n", "        sum(case when b.store_assortment_type = 'LONGTAIL' and b.assortment_status_id in (1,3) then 1 else 0 end) as longtail_ds_conn_city,\n", "        sum(case when b.store_assortment_type = 'SUPER_LONGTAIL' and b.assortment_status_id in (1,3) then 1 else 0 end) as super_longtail_ds_conn_city,\n", "        sum(case when b.store_assortment_type = 'UNICORN' and b.assortment_status_id in (1,3) then 1 else 0 end) as unicorn_ds_conn_city,\n", "        sum(case when b.assortment_status_id = 1 then 1 else 0 end) as active_ds_conn_city,\n", "        sum(case when b.assortment_status_id in (1,3) then 1 else 0 end) as rep_ds_conn_city\n", "        \n", "    from base b\n", "    group by 1,2,3,4\n", "),\n", "\n", "final as(\n", "    select\n", "        ic.*,\n", "        (cic.item_coverage / cic.max_city_coverage) as item_city_coverage,\n", "        (cic.max_city_assortment_coverage / cic.max_city_coverage) as assortment_city_coverage\n", "    from item_city ic\n", "    left join city_item_coverage cic on cic.item_id = ic.item_id\n", "        and cic.city_name = ic.city_name\n", "),\n", "\n", "city_store_count as(\n", "    select\n", "        ife.city_id,\n", "        f.store_assortment_type,\n", "        count(distinct ife.fe_facility_id) as total_ds_city\n", "    from base ife\n", "    left join item_city f on f.item_id = ife.item_id\n", "        and f.city_id = ife.city_id\n", "    group by 1,2\n", ")\n", "\n", "select \n", "    cast(current_timestamp as date) as insert_ds_ist,\n", "    cast(current_timestamp as timestamp) as updated_at_ist,\n", "    ife.item_id,\n", "    ife.item_name,\n", "    ife.be_facility_id,\n", "    ife.be_facility_name,\n", "    ife.city_id,\n", "    ife.city_name,\n", "    ife.fe_facility_id,\n", "    ife.fe_facility_name,\n", "    ife.store_assortment_type,\n", "    ife.assortment_status_id,\n", "    ife.entry_updated_at_ist,\n", "    ife.entry_updated_by,\n", "    ife.request_reason,\n", "    ife.job_id,\n", "    ife.net_inv_fe,\n", "    ife.net_inv_be,\n", "    ife.open_po_qty,\n", "    f.store_assortment_type as city_assortment_type,\n", "    f.assortment_status_id as city_assortment_status_id,\n", "    f.net_inv_fe as city_net_inv_fe,\n", "    f.net_inv_be as city_net_inv_be,\n", "    f.open_po_qty as city_open_po_qty,\n", "    f.ds_conn_city as city_store_count,\n", "    f.express_ds_conn_city as express_city_store_count,\n", "    f.longtail_ds_conn_city as longtail_city_store_count,\n", "    f.super_longtail_ds_conn_city as super_longatil_city_store_count,\n", "    f.unicorn_ds_conn_city as unicorn_city_store_count,\n", "    f.active_ds_conn_city as active_city_store_count,\n", "    f.rep_ds_conn_city as repl_city_store_count,\n", "    coalesce(f.item_city_coverage, 0) as item_city_coverage,\n", "    coalesce(f.assortment_city_coverage, 0) as assortment_city_coverage,\n", "    id.l0_id,\n", "    id.l0_category,\n", "    id.l1_id,\n", "    id.l1_category,\n", "    id.l2_id,\n", "    id.l2_category,\n", "    id.product_type_id,\n", "    id.p_type,\n", "    id.item_type,\n", "    cast(id.storage_type_raw as int) as storage_type_raw,\n", "    id.storage_type,\n", "    id.handling_type,\n", "    id.mrp,\n", "    id.variant_description,\n", "    id.weight_in_gm,\n", "    id.length_in_cm,\n", "    id.height_in_cm,\n", "    id.breadth_in_cm,\n", "    id.shelf_life,\n", "    id.item_factor,\n", "    id.item_catalog_age,\n", "    id.variant_rank,\n", "    cast(id.custom_storage_type_raw as int) as custom_storage_type_raw,\n", "    id.custom_storage_type,\n", "    id.manufacturer_id,\n", "    id.manufacturer_name,\n", "    id.brand_name,\n", "    id.outer_case_size,\n", "    id.inner_case_size,\n", "    id.is_high_value,\n", "    cast(id.high_value_tag_raw as int) as high_value_tag_raw,\n", "    id.scan_type,\n", "    cast(id.scan_type_raw as int) as scan_type_raw,\n", "    id.cms_food_type,\n", "    csc.total_ds_city,\n", "    f.inactive_ds_conn_city as inactive_city_store_count\n", "from item_fe ife\n", "left join item_details id on ife.item_id = id.item_id\n", "left join final f on f.item_id = ife.item_id\n", "    and f.city_id = ife.city_id\n", "left join city_store_count csc on csc.city_id = ife.city_id\n", "    and csc.store_assortment_type = f.store_assortment_type\n", "\"\"\"\n", "# , CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "1dc1b4f5-7d9c-4049-a77c-38ea9b911d74", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"Timestamp of Data\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"Item Name\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"L0 ID\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"L0 Category\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"L1 ID\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"L1 Category\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"L2 ID\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"L2 Category\"},\n", "    {\"name\": \"product_type_id\", \"type\": \"integer\", \"description\": \"Product Type ID\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"item_type\", \"type\": \"varchar\", \"description\": \"Item Type\"},\n", "    {\"name\": \"storage_type_raw\", \"type\": \"integer\", \"description\": \"Storage Type (Raw)\"},\n", "    {\"name\": \"storage_type\", \"type\": \"varchar\", \"description\": \"Storage Type\"},\n", "    {\"name\": \"handling_type\", \"type\": \"varchar\", \"description\": \"Handling Type\"},\n", "    {\"name\": \"mrp\", \"type\": \"real\", \"description\": \"MRP\"},\n", "    {\"name\": \"variant_description\", \"type\": \"varchar\", \"description\": \"Variant Description\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"real\", \"description\": \"Weight in Grams\"},\n", "    {\"name\": \"length_in_cm\", \"type\": \"real\", \"description\": \"Length in Centimeters\"},\n", "    {\"name\": \"height_in_cm\", \"type\": \"real\", \"description\": \"Height in Centimeters\"},\n", "    {\"name\": \"breadth_in_cm\", \"type\": \"real\", \"description\": \"Breadth in Centimeters\"},\n", "    {\"name\": \"shelf_life\", \"type\": \"real\", \"description\": \"Shelf Life\"},\n", "    {\"name\": \"item_factor\", \"type\": \"real\", \"description\": \"Item Factor\"},\n", "    {\"name\": \"item_catalog_age\", \"type\": \"real\", \"description\": \"Item Catalog Age\"},\n", "    {\"name\": \"variant_rank\", \"type\": \"integer\", \"description\": \"Variant Rank\"},\n", "    {\n", "        \"name\": \"custom_storage_type_raw\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Custom Storage Type (Raw)\",\n", "    },\n", "    {\"name\": \"custom_storage_type\", \"type\": \"varchar\", \"description\": \"Custom Storage Type\"},\n", "    {\"name\": \"manufacturer_id\", \"type\": \"integer\", \"description\": \"Manufacturer ID\"},\n", "    {\"name\": \"manufacturer_name\", \"type\": \"varchar\", \"description\": \"Manufacturer Name\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar\", \"description\": \"Brand Name\"},\n", "    {\"name\": \"outer_case_size\", \"type\": \"real\", \"description\": \"Outer Case Size\"},\n", "    {\"name\": \"inner_case_size\", \"type\": \"real\", \"description\": \"Inner Case Size\"},\n", "    {\"name\": \"is_high_value\", \"type\": \"boolean\", \"description\": \"Is High Value\"},\n", "    {\"name\": \"high_value_tag_raw\", \"type\": \"integer\", \"description\": \"High Value Tag (Raw)\"},\n", "    {\"name\": \"scan_type\", \"type\": \"varchar\", \"description\": \"Scan Type\"},\n", "    {\"name\": \"scan_type_raw\", \"type\": \"integer\", \"description\": \"Scan Type (Raw)\"},\n", "    {\"name\": \"cms_food_type\", \"type\": \"varchar\", \"description\": \"CMS Food Type\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"BE Facility ID\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"BE Facility Name\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"City ID\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\n", "        \"name\": \"fe_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"FE Facility ID\",\n", "    },\n", "    {\"name\": \"fe_facility_name\", \"type\": \"varchar\", \"description\": \"FE Facility Name\"},\n", "    {\n", "        \"name\": \"store_assortment_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Express, LT, SLT, Unicorn (at FE)\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_status_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Active, Inactive, Temp-Inactive or Discontiued (at FE)\",\n", "    },\n", "    {\n", "        \"name\": \"entry_updated_at_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"Entry Updated Timestamp\",\n", "    },\n", "    {\"name\": \"entry_updated_by\", \"type\": \"varchar\", \"description\": \"Entry Updated by\"},\n", "    {\n", "        \"name\": \"request_reason\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Request Reason for entry update)\",\n", "    },\n", "    {\n", "        \"name\": \"job_id\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Job ID\",\n", "    },\n", "    {\n", "        \"name\": \"net_inv_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Net Inv at FE\",\n", "    },\n", "    {\n", "        \"name\": \"net_inv_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Net Inv at BE\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Open PO Qty\",\n", "    },\n", "    {\n", "        \"name\": \"city_assortment_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Express, LT, SLT, Unicorn (at City)\",\n", "    },\n", "    {\n", "        \"name\": \"city_assortment_status_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Active, Inactive, Temp-Inactive or Discontiued (at City)\",\n", "    },\n", "    {\"name\": \"city_net_inv_fe\", \"type\": \"real\", \"description\": \"FE Net Inv aggr to City\"},\n", "    {\"name\": \"city_net_inv_be\", \"type\": \"real\", \"description\": \"BE Net Inv aggr to City\"},\n", "    {\"name\": \"city_open_po_qty\", \"type\": \"real\", \"description\": \"Open PO Qty aggr to City\"},\n", "    {\n", "        \"name\": \"city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected Dark Stores in the city for the item\",\n", "    },\n", "    {\n", "        \"name\": \"express_city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected Express Dark Stores in the city for the item\",\n", "    },\n", "    {\n", "        \"name\": \"longtail_city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected LT Dark Stores in the city for the item\",\n", "    },\n", "    {\n", "        \"name\": \"super_longatil_city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected SLT Dark Stores in the city for the item\",\n", "    },\n", "    {\n", "        \"name\": \"unicorn_city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected Unicorn Dark Stores in the city for the item\",\n", "    },\n", "    {\n", "        \"name\": \"active_city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected Active Dark Stores in the city for the item\",\n", "    },\n", "    {\n", "        \"name\": \"repl_city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected Active/ Temp-Inactive Dark Stores in the city for the item\",\n", "    },\n", "    {\n", "        \"name\": \"item_city_coverage\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Coverage of the item in the city\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_city_coverage\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Max Coverage of the same assortment in the city\",\n", "    },\n", "    {\n", "        \"name\": \"total_ds_city\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total DS of the same assortment in the city\",\n", "    },\n", "    {\n", "        \"name\": \"inactive_city_store_count\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Connected Inactive Dark Stores in the city for the item\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2309752a-9190-4677-87aa-59f71ab94b9d", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"assortment_status_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"item_id\",\n", "        \"fe_facility_id\",\n", "        \"be_facility_id\",\n", "        \"city_id\",\n", "    ],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains inventory metrics\",\n", "}\n", "\n", "to_trino(final_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "ec303176-3d7e-4c1f-9895-4517822627e9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}