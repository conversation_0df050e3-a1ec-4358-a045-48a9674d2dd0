alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: assortment_status_metrics
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07KUHSCH8W
path: povms/assortment_status_metrics/etl/assortment_status_metrics
paused: false
pool: povms_pool
project_name: assortment_status_metrics
schedule:
  end_date: '2025-09-15T00:00:00'
  interval: 30 2,8 * * *
  start_date: '2025-06-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
