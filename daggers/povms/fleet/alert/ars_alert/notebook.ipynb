{"cells": [{"cell_type": "code", "execution_count": null, "id": "33064aaa-8650-44d3-9343-702985c5a1e2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "7508a308-e3df-4b3f-aefa-1ea607ef82f4", "metadata": {}, "outputs": [], "source": ["ARS_df = pb.from_sheets(\"1Yqje92UP5ekug77yiijJWocKiUmDds4m-QK69JZgW7E\", \"ARS_Summary\")"]}, {"cell_type": "code", "execution_count": null, "id": "95e3412d-974f-46fa-a0e3-34a880df118d", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "b73f54e8-1218-4cdb-8dfc-9cf5320620d9", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.6,\n", "    font_size=12,\n", "    header_color=\"#25e9c8\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "ee80138f-a7a0-4a73-a5cb-3352dcb2fd33", "metadata": {}, "outputs": [], "source": ["if ARS_df.shape[0] > 0:\n", "    fig, ax = render_mpl_table(ARS_df, header_columns=0)\n", "    fig.savefig(\"ARS_alert.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "2cec3fbf-82ba-49b9-9b46-72779b91a2c3", "metadata": {}, "outputs": [], "source": ["if ARS_df.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if ARS_df.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6> These are the warehouses having Ars util >90%.\"\n", "                + \"\\n\"\n", "                + f\"FYI - <@U07P1BX4EH3>\"\n", "                + \"\\n\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    \"./ARS_alert.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}