{"cells": [{"cell_type": "code", "execution_count": null, "id": "8b5eed7e-4907-4eb2-9308-55e54b98904a", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "b8e2e847-bee4-4d7c-abda-7a2c4f5c04f9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import time\n", "import numpy as np\n", "from datetime import timedelta, datetime, date\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "b00b0b5d-f243-492d-b63d-ed29d1e9b8f0", "metadata": {}, "outputs": [], "source": ["# Connecting with Redshift\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "0df9efbe-9f19-4042-ae6f-6128ecc2274d", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(0, max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9123b81f-6d89-41b0-9870-04d003c2d190", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.1,\n", "    row_height=0.625,\n", "    font_size=10,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "54344929-1153-4bbf-8b3a-1b3aa7dd45fa", "metadata": {"tags": []}, "outputs": [], "source": ["query_1 = f\"\"\"\n", "WITH base AS\n", "  (SELECT *,\n", "          coalesce(vehicle_type2,vehicle_type1) AS truck_type\n", "   FROM\n", "     (SELECT t.id AS consignment_id,\n", "             t.state AS current_state,\n", "             trip.trip_id,\n", "             t.source_store_id::int AS facility_id,\n", "             n.external_name AS facility_name,\n", "             m.outlet_id AS ds_outlet_id,\n", "             co.name AS ds_outlet_name,\n", "             UPPER(split_part(json_extract_path_text(t.metadata,'truck_number'),'/',1)) AS truck_number,\n", "             split_part(json_extract_path_text(t.metadata,'truck_number'),'/',2) AS vehicle_type1,\n", "             json_extract_path_text(t.metadata,'vehicle_type') AS vehicle_type2,\n", "             count(DISTINCT d.external_id) AS num_invoices,\n", "             count(DISTINCT c.external_id) AS num_containers,\n", "             trip.truck_entry_wh,\n", "             trip.truck_handshake,\n", "             max(CASE\n", "                     WHEN (to_state='LOADING') THEN tl.install_ts\n", "                 END) AS loading_start,\n", "             max(CASE\n", "                     WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts\n", "                 END) AS ready_for_dispatch,\n", "             max(CASE\n", "                     WHEN (to_state='ENROUTE') THEN tl.install_ts\n", "                 END) AS enroute,\n", "             max(CASE\n", "                     WHEN (to_state='REACHED') THEN tl.install_ts\n", "                 END) AS ds_reached,\n", "             max(CASE\n", "                     WHEN (to_state='UNLOADING') THEN tl.install_ts\n", "                 END) AS unloading_start,\n", "             max(CASE\n", "                     WHEN (to_state='COMPLETED') THEN tl.install_ts\n", "                 END) AS unloading_completed,\n", "             trip.truck_return_wh\n", "      FROM lake_transit_server.transit_consignment t\n", "      JOIN lake_transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "      JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "      JOIN lake_transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "      JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "      JOIN lake_retail.console_outlet_logistic_mapping m ON m.logistic_node_id = t.destination_store_id\n", "      JOIN lake_retail.console_outlet co ON co.id = m.outlet_id\n", "      JOIN\n", "        (SELECT DISTINCT t.id AS consignment_id,\n", "                         tsl.label_value AS trip_id\n", "         FROM lake_transit_server.transit_consignment t\n", "         JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "         JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "         AND tsl.label_type = 'TRIP_ID'\n", "         WHERE t.install_ts >= cast((CURRENT_DATE - 2) AS TIMESTAMP)) tc ON tc.consignment_id = t.id\n", "      JOIN\n", "        (SELECT tsl.label_value AS trip_id,\n", "                min(q.entry_ts) AS truck_entry_wh,\n", "                min(tts.install_ts) AS truck_return_wh,\n", "                min(t.install_ts) AS truck_handshake\n", "         FROM lake_transit_server.transit_consignment t\n", "         JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "         JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "         AND tsl.label_type = 'TRIP_ID'\n", "         JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value\n", "         AND tt.shipment_label_type = 'TRIP_ID'\n", "         LEFT JOIN lake_transit_server.task_management_taskstatelog tts ON tts.task_id = tt.id\n", "         AND tts.to_state = 'COMPLETED'\n", "         JOIN lake_transit_server.transit_express_allocation_field_executive_queue q ON (q.user_profile_id || '_' || q.id) = tsl.label_value\n", "         WHERE t.install_ts >= cast((CURRENT_DATE - 2) AS TIMESTAMP)\n", "         GROUP BY 1) trip ON trip.trip_id = tc.trip_id\n", "      WHERE t.install_ts >= cast((CURRENT_DATE - 2) AS TIMESTAMP)\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               13,\n", "               14,\n", "               21,\n", "               t.metadata)),\n", "     pos AS\n", "  (SELECT DISTINCT td.consignment_id AS csmt_id,\n", "                   max(s.dispatch_time) AS dispatch_time\n", "   FROM lake_pos.pos_invoice pi\n", "   JOIN lake_transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "   JOIN lake_po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "   JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id\n", "   AND co.business_type_id IN (1,\n", "                               12,20)\n", "   WHERE pi.created_at >= cast((CURRENT_DATE - 2) AS TIMESTAMP)\n", "     AND invoice_type_id IN (5,\n", "                             14,\n", "                             16)\n", "   GROUP BY 1)\n", "   \n", ", vendor_details as (\n", "    \n", "    SELECT distinct\n", "    (tt.shipment_label_value) as trip_id,\n", "    -- fmu.name as driver_name,\n", "    lower(trim(fhs.name)) as vendor_name\n", "\n", "    FROM \n", "        lake_transit_server.transit_user tu\n", "    JOIN \n", "        lake_transit_server.transit_user_profile tup \n", "        ON tup.user_id = tu.id\n", "    JOIN \n", "        lake_transit_server.transit_task tt \n", "        ON cast(tt.user_profile_id as int) = tup.id\n", "    join \n", "        lake_fleet_management.fleet_management_user fmu\n", "        on fmu.phone = tu.phone\n", "    join \n", "        lake_fleet_management.fleet_management_user_detail as fmud\n", "        on fmu.id = fmud.user_id \n", "        and fmud.employee_type = 'TRUCK_DRIVER'\n", "    join\n", "        lake_fleet_management.fleet_management_hiring_source as fhs\n", "        on fmud.hiring_source_id = fhs.id\n", "        \n", "    WHERE tt.shipment_label_type = 'TRIP_ID'\n", "    AND TT.install_ts >= CURRENT_DATE - 3\n", "\n", ")\n", "   \n", "\n", "    \n", "    SELECT consignment_id,\n", "       base.trip_id,\n", "       CASE \n", "        WHEN vendor_name is null then '-'\n", "        else vendor_name\n", "        end as vendor_name,\n", "       (truck_handshake + interval '5.5 hrs')::date AS consignment_date,\n", "       case \n", "            when facility_id = 1743 then 264 \n", "            when facility_id = 4306 then 1983\n", "            else facility_id\n", "        end as source_facility_id,\n", "       facility_name,\n", "       ds_outlet_id,\n", "       ds_outlet_name,\n", "       truck_number,\n", "       (dispatch_time + interval '4.5 hr') AS scheduled_truck_arrival,\n", "       (truck_entry_wh + interval '5.5 hrs') AS truck_entry_wh,\n", "       (ready_for_dispatch + interval '5.5 hrs') AS ready_for_dispatch,\n", "       (enroute + interval '5.5 hrs') AS enroute,\n", "       (dispatch_time + interval '5.5 hrs') AS scheduled_dispatch_time,\n", "       (ds_reached + interval '5.5 hrs') AS ds_reached,\n", "       \n", "       datediff(MINUTE,ready_for_dispatch,enroute) AS driver_delay_min,\n", "       datediff(MINUTE,enroute,ds_reached) as enroute_to_ds_reached_min\n", "       \n", "       \n", "FROM base\n", "JOIN pos ON base.consignment_id = pos.csmt_id\n", "left join vendor_details as vd on base.trip_id = vd.trip_id\n", "WHERE consignment_date = current_date - 1\n", "\"\"\"\n", "\n", "df = read_sql_query(query_1, redshift_connection)\n", "df.head()"]}, {"cell_type": "markdown", "id": "d3572dbc-1a7e-45ea-86e7-76019195e481", "metadata": {}, "source": ["### FACILITY LEVEL CALC"]}, {"cell_type": "code", "execution_count": null, "id": "8be42db9-3e28-4213-bd21-8618d084c60b", "metadata": {}, "outputs": [], "source": ["# Create a new column 'anomaly_trips' based on the values of 'datediff' columns\n", "df[\"anomaly_trips\"] = np.where(\n", "    (df[\"driver_delay_min\"] > 10) & (df[\"enroute_to_ds_reached_min\"] <= 20),\n", "    df[\"trip_id\"],\n", "    np.nan,\n", ")\n", "\n", "# Group the data by 'source_facility_id' and 'facility_name'\n", "grouped = df.groupby([\"source_facility_id\", \"facility_name\"])\n", "\n", "# Count the number of unique trip IDs in the 'anomaly_trips' column\n", "anomaly_trips = grouped[\"anomaly_trips\"].nunique()\n", "\n", "# Count the number of unique trip IDs in the 'trip_id' column\n", "total_trips = grouped[\"trip_id\"].nunique()\n", "\n", "# Calculate the percentage of anomaly trips\n", "percent_anomaly_trips = anomaly_trips * 100.0 / total_trips\n", "\n", "# Add the calculated values to the grouped dataframe\n", "facility_grouped_df = pd.concat(\n", "    [anomaly_trips, total_trips, percent_anomaly_trips], axis=1\n", ")\n", "\n", "# Rename the columns\n", "facility_grouped_df.columns = [\"anomaly_trips\", \"total_trips\", \"% of anomaly trips\"]\n", "facility_grouped_df = facility_grouped_df.sort_values(\"anomaly_trips\", ascending=False)\n", "facility_grouped_df[\"% of anomaly trips\"] = facility_grouped_df[\n", "    \"% of anomaly trips\"\n", "].round(1)\n", "facility_grouped_df = facility_grouped_df.reset_index()"]}, {"cell_type": "markdown", "id": "f49cbace-0c6c-4a51-9d32-f6505c8bbbbc", "metadata": {}, "source": ["### VENDOR LEVEL CALC"]}, {"cell_type": "code", "execution_count": null, "id": "db3f8554-078e-4e7f-a00e-79b3cf6bdc50", "metadata": {"tags": []}, "outputs": [], "source": ["# Create a new column 'anomaly_trips' based on the values of 'datediff' columns\n", "df[\"anomaly_trips\"] = np.where(\n", "    (df[\"driver_delay_min\"] > 10) & (df[\"enroute_to_ds_reached_min\"] <= 20),\n", "    df[\"trip_id\"],\n", "    np.nan,\n", ")\n", "\n", "# Group the data by 'source_facility_id' and 'facility_name'\n", "grouped = df.groupby([\"vendor_name\"])\n", "\n", "# Count the number of unique trip IDs in the 'anomaly_trips' column\n", "anomaly_trips = grouped[\"anomaly_trips\"].nunique()\n", "\n", "# Count the number of unique trip IDs in the 'trip_id' column\n", "total_trips = grouped[\"trip_id\"].nunique()\n", "\n", "# Calculate the percentage of anomaly trips\n", "percent_anomaly_trips = anomaly_trips * 100.0 / total_trips\n", "\n", "# Add the calculated values to the grouped dataframe\n", "vendor_grouped_df = pd.concat(\n", "    [anomaly_trips, total_trips, percent_anomaly_trips], axis=1\n", ")\n", "\n", "# Rename the columns\n", "vendor_grouped_df.columns = [\"anomaly_trips\", \"total_trips\", \"% of anomaly trips\"]\n", "\n", "vendor_grouped_df = vendor_grouped_df.query('anomaly_trips !=0 and vendor_name != \"-\" ')\n", "vendor_grouped_df = vendor_grouped_df.sort_values(\"anomaly_trips\", ascending=False)\n", "vendor_grouped_df[\"% of anomaly trips\"] = vendor_grouped_df[\"% of anomaly trips\"].round(\n", "    1\n", ")\n", "vendor_grouped_df = vendor_grouped_df.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "4ec0cf9f-be37-43bc-a575-fc337af38259", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(facility_grouped_df)\n", "fig.savefig(\"facility_view.png\")\n", "\n", "fig, ax = render_mpl_table(vendor_grouped_df)\n", "fig.savefig(\"vendor_view.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "adda4014-60e0-402e-a7d3-73cdcaed8f78", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-fleet-core-team\",\n", "    text=\"Enroute Marked at DS (T-1) \\nRaw data for RCA: https://docs.google.com/spreadsheets/d/1EfyePptxdJpzeVfaiEwlefUA2L6sbq00_aRF4z-25as/edit#gid=0 \\n<@U03RH7BJVGF|cal> <@U03RP8S4Y2X|cal> <@U03SVCW19H6|cal> <@U03S83MKT52|cal> <@U03RRR2TAGL|cal>\",\n", "    files=[\"./facility_view.png\", \"./vendor_view.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7a9e5a48-c3e8-412f-adf2-a664edca4686", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3c7c4523-87ba-4472-ad65-f9d3b5b6af4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}