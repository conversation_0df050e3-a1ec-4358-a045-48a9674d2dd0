alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: enroute_marked_at_ds
dag_type: alert
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 1
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 16G
      request: 16G
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U057LU69XUZ
path: povms/fleet/alert/enroute_marked_at_ds
paused: true
project_name: fleet
schedule:
  end_date: '2023-10-09T00:00:00'
  interval: 54 1 * * *
  start_date: '2023-08-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
pool: povms_pool
