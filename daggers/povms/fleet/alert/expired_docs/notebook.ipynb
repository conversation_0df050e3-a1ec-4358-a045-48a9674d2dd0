{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta\n", "import requests\n", "import warnings\n", "import numpy as np\n", "from tqdm.notebook import tqdm\n", "\n", "tqdm.pandas()\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "05b7c2fb-ffed-49e2-b58e-bd3f1e6be4f2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1CeqxWEmNdB5cv76Wfyzs-MIB_cHkoGHoEhnDRb2AwPg\"\n", "sheet_name = \"MASTER SHEET\"\n", "df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "5ada0bfe-067e-4833-a983-61fdae888bf8", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fb275a5c-2ebd-4947-88dd-39eddd4e374f", "metadata": {}, "outputs": [], "source": ["temp = df.iloc[1].to_list()\n", "new_elements = [\"FC\", \"VENDOR NAME\", \"VEHICLE NO.\"]\n", "temp[:3] = new_elements\n", "df.iloc[1] = temp\n", "\n", "df.columns = df.iloc[1]\n", "df = df.drop(df.index[0]).reset_index(drop=True)\n", "df = df.drop(df.index[0]).reset_index(drop=True)\n", "df = df.drop(df.index[0]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e592f5b9-95e7-4aaa-8cc3-a5341d58dbd2", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4e874e7d-947a-4d73-8a63-1f1f24123551", "metadata": {}, "outputs": [], "source": ["current_date_ist = datetime.now() + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "\n", "# Helper function to parse dates and compare\n", "def check_date(row, column_name, current_date):\n", "    date_str = row[column_name]\n", "    try:\n", "        # Try to convert string to datetime object\n", "        date = pd.to_datetime(date_str, dayfirst=True, errors=\"coerce\")\n", "        # If conversion is successful and date is less than current date\n", "        if pd.notnull(date) and date <= current_date:\n", "            return date\n", "    except ValueError:\n", "        # If it's a special string, handle accordingly\n", "        if date_str == \"LTT\":\n", "            reg_date = pd.to_datetime(row[\"REGISTERATION DATE\"], dayfirst=True)\n", "            future_date = reg_date + pd.DateOffset(years=15)\n", "            if future_date <= current_date:\n", "                return future_date\n", "    # In case of NA or any other string, return None\n", "    return None\n", "\n", "\n", "# Iterate over the date columns to apply the check\n", "date_columns = [\n", "    \"REGISTERATION DATE\",\n", "    \"FITNESS VALID UPTO\",\n", "    \"PUCC VALID UPTO\",\n", "    \"TAX VALID UPTO\",\n", "    \"INSURANCE VALID UPTO\",\n", "    \"PERMIT VALID UPTO\",\n", "]\n", "result_rows = []\n", "\n", "for column in date_columns:\n", "    for index, row in df.iterrows():\n", "        date_result = check_date(row, column, current_date_ist)\n", "        if date_result is not None:\n", "            # Add relevant details to the result, including the date value\n", "            result_rows.append(\n", "                (\n", "                    row[\"FC\"],\n", "                    row[\"VENDOR NAME\"],\n", "                    row[\"VEHICLE NO.\"],\n", "                    column,\n", "                    date_result.strftime(\"%d-%b-%Y\"),\n", "                )\n", "            )\n", "\n", "result_df = pd.DataFrame(\n", "    result_rows,\n", "    columns=[\"FC\", \"VENDOR NAME\", \"VEHICLE NO.\", \"BREACH DOC\", \"BREACH DATE\"],\n", ")\n", "result_df = result_df.sort_values([\"FC\", \"VENDOR NAME\", \"VEHICLE NO.\"]).reset_index(\n", "    drop=True\n", ")\n", "result_df"]}, {"cell_type": "code", "execution_count": null, "id": "88acfe19-e64c-4f70-a95e-073ac2dc77b6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1RitaDBohedZYK5jCOgv7tBykQKQXVIkmGv25R63BtIY\"\n", "pb.to_sheets(result_df, sheet_id, \"main\")"]}, {"cell_type": "code", "execution_count": null, "id": "db3a8d14-a579-4f0f-b058-eb07c81d9627", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-fleet-core-team\",\n", "    text=f\"Fleet Audit Expired Docs Alert: There are {result_df.shape[0]} cases \\nRaw data for RCA: https://docs.google.com/spreadsheets/d/1RitaDBohedZYK5jCOgv7tBykQKQXVIkmGv25R63BtIY/edit#gid=0 \\n<@U04ULJ3RDJR|cal> <@U051HT13WN8|cal>\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bbf43b32-4e5a-4899-ba8b-458fa8a5edfb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}