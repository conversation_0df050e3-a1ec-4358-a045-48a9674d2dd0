{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "5b2efbe8-0839-433c-9f9b-42339e1c2bb7", "metadata": {}, "outputs": [], "source": ["# pend = pd.read_csv('pend.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b8be5da7-2943-4b07-92d5-b6aeb6886858", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1zeS8jy40ZHK32LHR9Qb1ze56_hrjd01DhEsXqp3aze4\"\n", "sheet_name = \"alert\"\n", "\n", "pend = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "783dbd1d-53df-42dc-9e46-6158ca2495ad", "metadata": {}, "outputs": [], "source": ["pend.head()"]}, {"cell_type": "code", "execution_count": null, "id": "982cd8db-4249-4104-be52-bc1e1c8424af", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "# slack_channel = [\"test_channel_bot\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "2b6c0106-3a53-49b4-873b-c51c8d506548", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#af08f8\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_text_props(ha=\"center\")\n", "\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "b6011b65-60b0-4be5-aacf-99938720ac18", "metadata": {}, "outputs": [], "source": ["if pend.shape[0] > 0:\n", "    fig, ax = render_mpl_table(pend, header_columns=0)\n", "    fig.savefig(\"pend.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "5742965d-8a85-4248-8148-561ed8fe1b3e", "metadata": {}, "outputs": [], "source": ["date_two_days_ago = (datetime.now() - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")\n", "if pend.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if pend.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6>\" + \"\\n\"\n", "                f\"Below FCs have high pendency. Kindly share action plan for respective FCs.\"\n", "                + \"\\n\"\n", "                + f\"FYI - <@U07P1BX4EH3> <@U07JG40NJ3V> <@U051HT13WN8>\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    # \"raw_data.csv\",\n", "                    \"./pend.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "1093d5d2-5b4f-4be4-a05c-e620ab621418", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}