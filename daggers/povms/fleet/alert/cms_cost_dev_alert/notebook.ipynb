{"cells": [{"cell_type": "code", "execution_count": null, "id": "96b3317f-2fbe-4150-bc43-82b8d5453c4c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "6c8310e3-d5ec-455d-9204-ee109451c7bb", "metadata": {}, "outputs": [], "source": ["# cost_dev_pos = pd.read_csv('cost_dev_pos.csv')\n", "\n", "sheet_id = \"1HDdW6wY6h_JPVEbyygowmVQUUfXVuPuhxFXY5lSlDkQ\"\n", "sheet_name = \"cost_dev_pos\"\n", "\n", "cost_dev_pos = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "0d202a3d-3fd6-4a7c-860e-6eb8644332b8", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "8c697734-d326-4375-bcb7-b9d1450b973c", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.6,\n", "    font_size=12,\n", "    header_color=\"#cc2400\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "5b9bb06a-14f1-4f35-9817-b9711231da65", "metadata": {}, "outputs": [], "source": ["if cost_dev_pos.shape[0] > 0:\n", "    fig, ax = render_mpl_table(cost_dev_pos, header_columns=0)\n", "    fig.savefig(\"cost_dev_pos.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "2f2d87c3-b16c-4c17-85ab-15ab73102ea5", "metadata": {}, "outputs": [], "source": ["date_two_days_ago = (datetime.now() - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "if cost_dev_pos.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if cost_dev_pos.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6> The following stores show an increase in the total_cost per day for the date {date_two_days_ago} as compared to the last 7 days. \"\n", "                + \"\\n\"\n", "                + f\"For Raw data, please refer this <https://docs.google.com/spreadsheets/d/1HDdW6wY6h_JPVEbyygowmVQUUfXVuPuhxFXY5lSlDkQ/edit?gid=0#gid=0| sheet>\"\n", "                + \"\\n\"\n", "                + f\"<@U082AGFU4R3> please ask for the RCA from fleet managers.\"\n", "                + \"\\n\"\n", "                + f\"CC: <@U03S602NC82> \"\n", "                + \"\\n\"\n", "                + f\"FYI- <@U07M417DP1D> <@U07P1BX4EH3> <@U07JG40NJ3V>\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    # \"raw_data.csv\",\n", "                    \"./cost_dev_pos.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "d839ee8a-e0f6-4123-a974-7b8fd5580bb1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}