alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: cpu_alert
dag_type: alert
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0882AQN864
path: povms/fleet/alert/cpu_alert
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2025-06-22T00:00:00'
  interval: 0 3 * * *
  start_date: '2025-03-24T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
