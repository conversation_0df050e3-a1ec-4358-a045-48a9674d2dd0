{"cells": [{"cell_type": "code", "execution_count": null, "id": "6db06831-8a2b-4171-8571-c48328bc333f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "74287353-1d57-419d-bb9b-1d3e7ac99176", "metadata": {}, "outputs": [], "source": ["cpu_df = pb.from_sheets(\"1Yqje92UP5ekug77yiijJWocKiUmDds4m-QK69JZgW7E\", \"cpu_summary\")"]}, {"cell_type": "code", "execution_count": null, "id": "0a0e75f2-7f8a-4b52-882a-7a73c90d63d2", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "92eab504-55c7-43d1-a4e2-d82faf419088", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.6,\n", "    font_size=12,\n", "    header_color=\"#25e9c8\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "d18ba781-1972-4286-9be7-9f42d1db57a5", "metadata": {}, "outputs": [], "source": ["if cpu_df.shape[0] > 0:\n", "    fig, ax = render_mpl_table(cpu_df, header_columns=0)\n", "    fig.savefig(\"cpu_alert.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "e4552cc0-1ac6-4646-8480-42a81bce3381", "metadata": {}, "outputs": [], "source": ["if cpu_df.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if cpu_df.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6> These are the warehouses x city where CPU has increased by 5%\"\n", "                + \"\\n\"\n", "                + f\"FYI - <@U07P1BX4EH3>\"\n", "                + \"\\n\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    \"./cpu_alert.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}