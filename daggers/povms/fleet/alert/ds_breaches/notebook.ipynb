{"cells": [{"cell_type": "code", "execution_count": null, "id": "8b5eed7e-4907-4eb2-9308-55e54b98904a", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "b8e2e847-bee4-4d7c-abda-7a2c4f5c04f9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import time\n", "import numpy as np\n", "from datetime import timedelta, datetime, date\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "b00b0b5d-f243-492d-b63d-ed29d1e9b8f0", "metadata": {}, "outputs": [], "source": ["# Connecting with Redshift\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "0df9efbe-9f19-4042-ae6f-6128ecc2274d", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(0, max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9123b81f-6d89-41b0-9870-04d003c2d190", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=5.5,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "54344929-1153-4bbf-8b3a-1b3aa7dd45fa", "metadata": {}, "outputs": [], "source": ["query_1 = f\"\"\"\n", "\n", "WITH base AS\n", "  (SELECT *,\n", "          coalesce(vehicle_type2,vehicle_type1) AS truck_type\n", "   FROM\n", "     (SELECT t.id AS consignment_id,\n", "             t.state AS current_state,\n", "             trip.trip_id,\n", "             t.source_store_id::int AS facility_id,\n", "             n.external_name AS facility_name,\n", "             m.outlet_id AS ds_outlet_id,\n", "             co.name AS ds_outlet_name,\n", "             UPPER(split_part(json_extract_path_text(t.metadata,'truck_number'),'/',1)) AS truck_number,\n", "             split_part(json_extract_path_text(t.metadata,'truck_number'),'/',2) AS vehicle_type1,\n", "             json_extract_path_text(t.metadata,'vehicle_type') AS vehicle_type2,\n", "             count(DISTINCT d.external_id) AS num_invoices,\n", "             count(DISTINCT c.external_id) AS num_containers,\n", "             trip.truck_entry_wh,\n", "             trip.truck_handshake,\n", "             max(CASE\n", "                     WHEN (to_state='LOADING') THEN tl.install_ts\n", "                 END) AS loading_start,\n", "             max(CASE\n", "                     WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts\n", "                 END) AS ready_for_dispatch,\n", "             max(CASE\n", "                     WHEN (to_state='ENROUTE') THEN tl.install_ts\n", "                 END) AS enroute,\n", "             max(CASE\n", "                     WHEN (to_state='REACHED') THEN tl.install_ts\n", "                 END) AS ds_reached,\n", "             max(CASE\n", "                     WHEN (to_state='UNLOADING') THEN tl.install_ts\n", "                 END) AS unloading_start,\n", "             max(CASE\n", "                     WHEN (to_state='COMPLETED') THEN tl.install_ts\n", "                 END) AS unloading_completed,\n", "             trip.truck_return_wh\n", "      FROM lake_transit_server.transit_consignment t\n", "      JOIN lake_transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "      JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "      JOIN lake_transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "      JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "      JOIN lake_retail.console_outlet_logistic_mapping m ON m.logistic_node_id = t.destination_store_id\n", "      JOIN lake_retail.console_outlet co ON co.id = m.outlet_id\n", "      JOIN\n", "        (SELECT DISTINCT t.id AS consignment_id,\n", "                         tsl.label_value AS trip_id\n", "         FROM lake_transit_server.transit_consignment t\n", "         JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "         JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "         AND tsl.label_type = 'TRIP_ID'\n", "         WHERE t.install_ts >= current_date - 2) tc ON tc.consignment_id = t.id\n", "      JOIN\n", "        (SELECT tsl.label_value AS trip_id,\n", "                min(q.entry_ts) AS truck_entry_wh,\n", "                min(tts.install_ts) AS truck_return_wh,\n", "                min(t.install_ts) AS truck_handshake\n", "         FROM lake_transit_server.transit_consignment t\n", "         JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "         JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "         AND tsl.label_type = 'TRIP_ID'\n", "         JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value\n", "         AND tt.shipment_label_type = 'TRIP_ID'\n", "         LEFT JOIN lake_transit_server.task_management_taskstatelog tts ON tts.task_id = tt.id\n", "         AND tts.to_state = 'COMPLETED'\n", "         JOIN lake_transit_server.transit_express_allocation_field_executive_queue q ON (q.user_profile_id || '_' || q.id) = tsl.label_value\n", "         WHERE t.install_ts >= current_date - 2\n", "         GROUP BY 1) trip ON trip.trip_id = tc.trip_id\n", "      WHERE t.install_ts >= current_date - 2\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               13,\n", "               14,\n", "               21,\n", "               t.metadata)),\n", "   \n", "     pos AS\n", "  (SELECT DISTINCT td.consignment_id AS csmt_id,\n", "                   max(s.dispatch_time) AS dispatch_time\n", "   FROM lake_pos.pos_invoice pi\n", "   JOIN lake_transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "   JOIN lake_po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "   JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id\n", "   AND co.business_type_id IN (1, \n", "   \n", "                               12,20)\n", "   WHERE pi.created_at >= current_date - 2\n", "     AND invoice_type_id IN (5,\n", "                             14,\n", "                             16)\n", "   GROUP BY 1),\n", "     \n", "     grn AS\n", "  (SELECT DISTINCT t.id AS c_id,\n", "                   min(il.pos_timestamp) AS grn_started_at,\n", "                   max(il.pos_timestamp) AS grn_completed_at\n", "   FROM lake_transit_server.transit_consignment t\n", "   JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "   JOIN lake_ims.ims_inventory_log il ON il.merchant_invoice_id = d.external_id\n", "   WHERE t.install_ts >= current_date - 2\n", "     AND inventory_update_type_id IN (1,\n", "                                      28,\n", "                                      76,\n", "                                      90,\n", "                                      93)\n", "   GROUP BY 1)\n", "   \n", ", stores_with_unloading_app as (\n", "    select distinct site_id from lake_storeops.er where er_type = 'FORWARD_CONSIGNMENT'\n", ")   \n", "\n", ", excpected_timelines as (\n", "\n", "    with base_ as (\n", "        select \n", "            trip_id,\n", "            event_type,\n", "            \n", "            (last_value(scheduled_ts) \n", "                over(partition by trip_id,event_type \n", "                     order by update_ts rows between unbounded preceding and unbounded following) ) + interval '5.5 hrs'\n", "                 as scheduled_ts\n", "        from \n", "            lake_transit_server.transit_projection_trip_event_timeline as tp\n", "        where \n", "        event_type in ('DS_ARRIVAL')\n", "        and install_ts >= current_date-3\n", "    )\n", "    \n", "    select\n", "        trip_id, \n", "        max(CASE WHEN event_type = 'DS_ARRIVAL' THEN scheduled_ts ELSE NULL END ) AS scheduled_ds_reach\n", "        \n", "    FROM base_ \n", "    group by 1\n", "    \n", ")\n", "  \n", "SELECT consignment_id,\n", "       base.trip_id,\n", "       current_state,\n", "       (truck_handshake + interval '5.5 hrs')::date AS consignment_date,\n", "       case \n", "            when facility_id = 1743 then 264 \n", "            when facility_id = 4306 then 1983\n", "            else facility_id\n", "        end as source_facility_id,\n", "       facility_name,\n", "       ds_outlet_id,\n", "       ds_outlet_name,\n", "       truck_type,\n", "       truck_number,\n", "       (ds_reached + interval '5.5 hrs') AS ds_reached_,\n", "       (unloading_start + interval '5.5 hrs') AS unloading_start_,\n", "       (unloading_completed + interval '5.5 hrs') AS unloading_completed_,\n", "       (grn_started_at + interval '5.5 hrs') AS grn_started_at_,\n", "       (grn_completed_at + interval '5.5 hrs') AS grn_completed_at_,\n", "       \n", "       datediff(MINUTE,ds_reached_,unloading_start_) AS ds_reached_to_unloading,\n", "       datediff(MINUTE,unloading_start_,unloading_completed_) AS unloading_time_min,\n", "       datediff(MINUTE,grn_started_at_,grn_completed_at_) AS grn_time_min,\n", "       \n", "       case when ds_reached_to_unloading > 15 then 'YES' else 'NO' end as unloading_start_breach,\n", "      case when unloading_time_min > 120 then 'YES' else 'NO' end as unloading_time_breach,\n", "      case when grn_time_min > 300 then 'YES' else 'NO' end as grn_time_breach,\n", "       \n", "       \n", "       scheduled_ds_reach,\n", "       (scheduled_ds_reach + interval '0.25 hrs') ds_reached_breach_threshold,\n", "       case \n", "            when ds_reached_ <= (scheduled_ds_reach + interval '0.25 hrs') then 1\n", "            else 0\n", "       end as \"vehicle reached ds on time\",\n", "       \n", "       case \n", "        when ds_reached_ > (scheduled_ds_reach + interval '0.25 hrs') \n", "        then  ds_reached_- (scheduled_ds_reach + interval '0.25 hrs')  \n", "        END AS \"deviation from scheduled arrival time + 15 minutes\"\n", "    \n", "       \n", "FROM base\n", "JOIN pos ON base.consignment_id = pos.csmt_id\n", "LEFT JOIN grn ON grn.c_id = base.consignment_id\n", "join stores_with_unloading_app as ua\n", "on base.ds_outlet_id = ua.site_id\n", "join excpected_timelines as et\n", "on base.trip_id = et.trip_id\n", "where consignment_date = current_date - 1\n", "and ds_reached is not null\n", "\n", "\"\"\"\n", "\n", "df = read_sql_query(query_1, redshift_connection)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b406e7e2-bb7b-44bf-b710-119465f1b59e", "metadata": {}, "outputs": [], "source": ["on_time_df = df[\n", "    (df[\"vehicle reached ds on time\"] == 1) & (df[\"unloading_time_breach\"] == \"YES\")\n", "]\n", "not_on_time_df = df[\n", "    (df[\"vehicle reached ds on time\"] != 1) & (df[\"unloading_time_breach\"] == \"YES\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e6334952-7ef0-4424-848c-affcc035bc9e", "metadata": {"tags": []}, "outputs": [], "source": ["on_time_grouped = (\n", "    on_time_df.groupby([\"ds_outlet_id\", \"ds_outlet_name\"])\n", "    .agg(\n", "        avg_unloading_start_tat=(\"ds_reached_to_unloading\", \"mean\"),\n", "        avg_unloading_complete_tat=(\"unloading_time_min\", \"mean\"),\n", "        unloading_breaches=(\"trip_id\", \"nunique\"),\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "on_time_grouped.columns = [\n", "    \"Ds outlet id\",\n", "    \"Ds outlet name\",\n", "    \"avg. unloading start TAT\",\n", "    \"avg unloading complete TAT\",\n", "    \"No. of Trips with unloading complete breach\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d0dab77d-f43d-4149-b353-d9806b35273e", "metadata": {}, "outputs": [], "source": ["not_on_time_grouped = (\n", "    not_on_time_df.groupby([\"ds_outlet_id\", \"ds_outlet_name\"])\n", "    .agg(\n", "        avg_deviation=(\"deviation from scheduled arrival time + 15 minutes\", \"mean\"),\n", "        avg_unloading_complete_tat=(\"unloading_time_min\", \"mean\"),\n", "        unloading_breaches=(\"trip_id\", \"nunique\"),\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "not_on_time_grouped.columns = [\n", "    \"Ds outlet id\",\n", "    \"Ds outlet name\",\n", "    \"avg deviation in vehicle arrival\",\n", "    \"avg unloading complete TAT\",\n", "    \"No. of Trips with unloading complete breach\",\n", "]\n", "not_on_time_grouped[\"avg deviation in vehicle arrival\"] = (\n", "    not_on_time_grouped[\"avg deviation in vehicle arrival\"].dt.total_seconds() / 60\n", ")\n", "not_on_time_grouped[\"avg deviation in vehicle arrival\"] = not_on_time_grouped[\n", "    \"avg deviation in vehicle arrival\"\n", "].round(0)"]}, {"cell_type": "code", "execution_count": null, "id": "4ec0cf9f-be37-43bc-a575-fc337af38259", "metadata": {"tags": []}, "outputs": [], "source": ["fig, ax = render_mpl_table(on_time_grouped)\n", "fig.savefig(\"Vehicle reached DS on time.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "59e8df1d-197d-49c3-9683-1f160af3b2ef", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(not_on_time_grouped)\n", "fig.savefig(\"Vehicle did not reach DS on time.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "adda4014-60e0-402e-a7d3-73cdcaed8f78", "metadata": {"tags": []}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"#bl-unloading-and-consignment-grn-issues\",\n", "    text=\"DS level breaches (T-1) \\nRaw data for RCA: https://docs.google.com/spreadsheets/d/1pNWRW3dgwrzsySkilMZC4ElYQsWMvWyEqUgX1nv1vBM/edit#gid=0 \\n<@U03RLJ6AYVA|cal> <@U03S2M78X53|cal> <@U03SCGRJ7HA|cal>\",\n", "    files=[\"Vehicle reached DS on time.png\", \"Vehicle did not reach DS on time.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3c7c4523-87ba-4472-ad65-f9d3b5b6af4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}