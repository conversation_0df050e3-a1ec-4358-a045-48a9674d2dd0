{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "5b2efbe8-0839-433c-9f9b-42339e1c2bb7", "metadata": {}, "outputs": [], "source": ["# gps = pd.read_csv('gps.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b8be5da7-2943-4b07-92d5-b6aeb6886858", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Az1NU_GMy2MTZHu5Yg6v9XvONAqJVGMxE7Pvhbtv2Ig\"\n", "sheet_name = \"alert\"\n", "\n", "gps = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "783dbd1d-53df-42dc-9e46-6158ca2495ad", "metadata": {}, "outputs": [], "source": ["gps.head()"]}, {"cell_type": "code", "execution_count": null, "id": "982cd8db-4249-4104-be52-bc1e1c8424af", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "# slack_channel = [\"test_channel_bot\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "2b6c0106-3a53-49b4-873b-c51c8d506548", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.8,\n", "    header_row_height=0.4,  # NEW PARAMETER: height of header row\n", "    font_size=16,\n", "    header_color=\"#C70039\",\n", "    row_colors=\"#ffffff\",\n", "    alternate_row_color=\"#ffffff\",\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for (row, col), cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if row == 0 or col < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "            # Set header row height\n", "            cell.set_height(header_row_height / (np.array(data.shape[::-1])[0] + 1) / row_height)\n", "        else:\n", "            cell.set_text_props(ha=\"center\")\n", "            if row % 2 == 0:\n", "                cell.set_facecolor(alternate_row_color)\n", "            else:\n", "                cell.set_facecolor(row_colors)\n", "\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "b6011b65-60b0-4be5-aacf-99938720ac18", "metadata": {}, "outputs": [], "source": ["if gps.shape[0] > 0:\n", "    fig, ax = render_mpl_table(gps, header_columns=0)\n", "    fig.savefig(\"gps.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "5742965d-8a85-4248-8148-561ed8fe1b3e", "metadata": {}, "outputs": [], "source": ["date_two_days_ago = (datetime.now() - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")\n", "if gps.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if gps.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6>\" + \"\\n\"\n", "                f\":small_red_triangle_down: GPS Pings (<70%) report each FC for the date {date_two_days_ago}. Kindly share the action plan for the same. \"\n", "                + \"\\n\"\n", "                + f\"FYI - <@U07P1BX4EH3> <@U07JG40NJ3V> <@U083263NXFA>\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    # \"raw_data.csv\",\n", "                    \"./gps.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "59f1aefe-8c17-4973-80fa-83a5bc0d3c75", "metadata": {}, "outputs": [], "source": ["# import time\n", "\n", "# time.sleep(15)"]}, {"cell_type": "code", "execution_count": null, "id": "dae9c2d7-e0eb-4bf6-815f-a742f4ec88db", "metadata": {}, "outputs": [], "source": ["# gps2 = pd.read_csv('gps2.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "1093d5d2-5b4f-4be4-a05c-e620ab621418", "metadata": {}, "outputs": [], "source": ["# sheet_id = \"1Az1NU_GMy2MTZHu5Yg6v9XvONAqJVGMxE7Pvhbtv2Ig\"\n", "# sheet_name = \"alert2\"\n", "\n", "# gps2 = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "fca0bd2c-f136-4728-b837-56954abae097", "metadata": {}, "outputs": [], "source": ["# gps2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e1af7e72-161a-4ed2-a58d-4a885620ee0f", "metadata": {}, "outputs": [], "source": ["# slack_channel = [\"bl-fleet-core-team\"]\n", "# # slack_channel = [\"test_channel_bot\"]\n", "# slack_channel = list(slack_channel)\n", "# slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "912bf760-a095-4cf5-96cc-aabbf9274cd7", "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# import matplotlib.pyplot as plt\n", "\n", "\n", "# def render_mpl_table(\n", "#     data,\n", "#     col_width=3.5,\n", "#     row_height=0.8,\n", "#     header_row_height=0.4,  # NEW PARAMETER: height of header row\n", "#     font_size=16,\n", "#     header_color=\"#196f3d\",\n", "#     row_colors=\"#ffffff\",\n", "#     alternate_row_color=\"#ffffff\",\n", "#     edge_color=\"black\",\n", "#     bbox=[0, 0, 1, 1],\n", "#     header_columns=0,\n", "#     ax=None,\n", "#     **kwargs,\n", "# ):\n", "#     if ax is None:\n", "#         size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "#         fig, ax = plt.subplots(figsize=size)\n", "#         ax.axis(\"off\")\n", "\n", "#     mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "#     mpl_table.auto_set_font_size(False)\n", "#     mpl_table.set_fontsize(font_size)\n", "\n", "#     for (row, col), cell in mpl_table._cells.items():\n", "#         cell.set_edgecolor(edge_color)\n", "#         if row == 0 or col < header_columns:\n", "#             cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "#             cell.set_facecolor(header_color)\n", "#             # Set header row height\n", "#             cell.set_height(header_row_height / (np.array(data.shape[::-1])[0] + 1) / row_height)\n", "#         else:\n", "#             cell.set_text_props(ha=\"center\")\n", "#             if row % 2 == 0:\n", "#                 cell.set_facecolor(alternate_row_color)\n", "#             else:\n", "#                 cell.set_facecolor(row_colors)\n", "\n", "#     mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "#     return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "a400ed94-703a-472a-9d9a-45674c3893c3", "metadata": {}, "outputs": [], "source": ["# if gps2.shape[0] > 0:\n", "#     fig, ax = render_mpl_table(gps2, header_columns=0)\n", "#     fig.savefig(\"gps2.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "91b1f3a8-3e61-4383-9ef3-24e8fd6d13e7", "metadata": {}, "outputs": [], "source": ["# date_two_days_ago = (datetime.now() - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")\n", "# if gps2.shape[0] > 0:\n", "#     for i in range(len(slack_channel)):\n", "#         channel = slack_channel[i]\n", "#         if gps2.shape[0] > 0:\n", "#             text_req = (\n", "#                 f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6>\" + \"\\n\"\n", "#                 f\":sparkles: GPS Pings (>90%) report each FC for the date {date_two_days_ago}. Kindly share the action plan for the same. \"\n", "#                 + \"\\n\"\n", "#                 + f\"FYI - <@U07P1BX4EH3> <@U07JG40NJ3V> <@U083263NXFA>\"\n", "#                 # + \"\\n\"\n", "#             )\n", "#             pb.send_slack_message(\n", "#                 channel=channel,\n", "#                 text=text_req,\n", "#                 files=[\n", "#                     # \"raw_data.csv\",\n", "#                     \"./gps2.png\",\n", "#                 ],\n", "#             )\n", "#         else:\n", "#             print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "ebfc324c-1a14-4a6c-b64a-7083a58814d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cac0f5b6-29ac-4931-8474-10773aae0a9d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}