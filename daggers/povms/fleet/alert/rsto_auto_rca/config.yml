dag_name: rsto_auto_rca
dag_type: alert
escalation_priority: low
execution_timeout: 840
executor:
  config:
    load_type: medium
    node_type: spot
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0787NDLS4E
path: povms/fleet/alert/rsto_auto_rca
paused: false
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2025-01-16T23:00:00'
  interval: 0 6 * * MON
  start_date: '2024-11-14T00:00:00'
schedule_type: fixed
sla: 122 minutes
support_files: []
tags: []
template_name: notebook
version: 1
