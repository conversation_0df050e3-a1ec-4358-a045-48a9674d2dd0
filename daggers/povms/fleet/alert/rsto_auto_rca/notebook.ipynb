{"cells": [{"cell_type": "code", "execution_count": null, "id": "85f8a991-3d46-4de4-976d-431a34fcb7a3", "metadata": {}, "outputs": [], "source": ["!pip install numpy==1.23.1"]}, {"cell_type": "code", "execution_count": null, "id": "efea65e1-1fcf-416b-a5b4-c83f87f1042e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import numpy as npq\n", "import itertools\n", "import time\n", "import math\n", "from datetime import datetime, timedelta, date\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "3360af47-a183-4588-888d-7d481a519b66", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, conn):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, conn)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b14e171-e0e7-4b58-9a00-c2bec1252aef", "metadata": {}, "outputs": [], "source": ["ptype = \"\"\"\n", "\n", "with crwi_data\n", "as \n", "(\n", "with t as\n", "  (select c.trip_id,\n", "          t.transit_trip_id,\n", "          date(t.created_at+interval '330' minute) as trip_date,\n", "          t.created_at,\n", "          f.name,\n", "          s.id as sourc_id,\n", "          s.name as sourc_name,\n", "          t.outlet_id as dest_id,\n", "          array_join(array_agg(DISTINCT consignment_id), ', ') consignment_ids,\n", "          array_join(array_agg(DISTINCT replace(replace(s.name, 'Super Store '), 'SS ')), ', ') source_outlet_names,\n", "          max(case when tl.state = 'UNLOADING_COMPLETED' then (tl.created_at+interval '330' minute) end) as unloading_completed_at,\n", "          max(case when tl.state = 'QC_COMPLETED' then (tl.created_at+interval '330' minute) end) trip_com\n", "          \n", "   from wms.reverse_trip t\n", "   join wms.reverse_trip_log tl on t.id = tl.trip_id\n", "   join wms.reverse_consignment c on c.trip_id = t.id\n", "   join retail.console_outlet s on s.id = source_outlet_id\n", "   join retail.console_outlet f on f.id = t.outlet_id \n", "   where tl.insert_ds_ist >= cast(current_date - interval '60' day as varchar) \n", "        --  and c.trip_id = 323992\n", "   group by 1,2,3,4,5,6,7,8),\n", "   \n", "   fo as (\n", "    \n", "    select \n", "        facility_id, \n", "        outlet_id, \n", "        city_id, \n", "        outlet_name, \n", "        cl.name as city\n", "        \n", "        from lake_po.physical_facility_outlet_mapping pfom\n", "        left join retail.console_location cl on cl.id = pfom.city_id\n", "        where pfom.active=1 and ars_active=1\n", "        -- and pfom.facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "        group by 1,2,3,4,5),\n", "\n", "\n", "inv as\n", "(select   reverse_trip_id,\n", "          type,\n", "          document_id,\n", "          variant_id,\n", "          expected_quantity,\n", "          insert_ds_ist\n", "          \n", "   from lake_wms.reverse_consignment_item ci\n", "   left join lake_wms.reverse_consignment_document cd on cd.id = ci.reverse_consignment_document_id\n", "   where insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "--   and reverse_trip_id = 323992\n", "--   and reverse_trip_id in (select trip_id from t)\n", "),\n", "\n", "\n", "inw as \n", "  (select trip_id,\n", "          type,\n", "          document_id,\n", "          variant_id,\n", "          created_at,\n", "          grn_id,\n", "          sum(quantity) quantity,\n", "          sum(case when item_type = 'GOOD' then quantity end) good_grn_quantity,\n", "          sum(case when item_type = 'DAMAGED' then quantity end) damaged_grn_quantity,\n", "          sum(case when item_type = 'NEAR_EXPIRY' then quantity end) near_expiry_grn_quantity,\n", "          sum(case when item_type = 'EXPIRED' then quantity end) expired_grn_quantity\n", "          \n", "    from (select trip_id,\n", "              document_id,\n", "              type,\n", "            --   qit.created_at as created_at,\n", "              (qit.created_at+interval '330' minute) as created_at,\n", "              CAST(json_extract(qit.meta, '$.grn_id') AS VARCHAR) grn_id,\n", "              CAST(json_extract(qit.meta, '$.return_invoice_id') AS VARCHAR) return_invoice_id,\n", "              cast(json_parse(json_query(qit.meta, 'strict $.items[*].item_type' WITH ARRAY WRAPPER)) as array(varchar)) item_types,\n", "              cast(json_parse(json_query(qit.meta, 'strict $.items[*].variant_id' WITH ARRAY WRAPPER)) as array(varchar)) variants,\n", "              cast(json_parse(json_query(qit.meta, 'strict $.items[*].quantity' WITH ARRAY WRAPPER)) as array(integer)) quantities\n", "              \n", "   from wms.reverse_qc_inward_transaction qit\n", "   join wms.reverse_qc_inward_activity qia on qia.id = qit.reverse_qc_inward_activity_id\n", "   and (qit.created_at) >= cast((current_date - interval '60' day) as timestamp)\n", "   and qit.state = 'GRN_COMPLETE'\n", "   join lake_wms.reverse_qc_activity qa on qa.id = qia.reverse_qc_activity_id\n", "--   and trip_id = 323992\n", "--   and trip_id in (select trip_id from t)\n", "   left join lake_wms.reverse_consignment_document cd on cd.id = qit.reverse_consignment_document_id)\n", "   left join UNNEST(item_types, variants, quantities) AS t(item_type, variant_id, quantity) on true\n", "   group by 1,2,3,4,5,6)\n", "  ,\n", "\n", "\n", "-- qc as\n", "-- (select   trip_id,\n", "--           variant_id,\n", "--           array_join(array_agg(DISTINCT qi.updated_by), ', ') qc_by\n", "          \n", "--       from wms.reverse_qc_item qi\n", "--       join lake_wms.reverse_consignment_container cc on cc.id = qi.reverse_consignment_container_id\n", "--       join lake_wms.reverse_qc_putaway_container pc on pc.id = qi.reverse_qc_putaway_container_id\n", "--       join lake_wms.reverse_consignment c on c.id = cc.reverse_consignment_id\n", "--     --   and trip_id = 323992\n", "--     --   and trip_id in (select trip_id from t)\n", "--       where cc.insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "--       and qi.insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "--       group by 1,2),\n", "\n", "ptype as(\n", "SELECT id.item_id,\n", "       pt.p_type as ptype,\n", "       l0,\n", "       l1,\n", "       l2\n", "\n", "FROM (\n", "        (SELECT item_id, name AS item_name, l0,l1,l2 FROM rpc.item_category_details where lake_active_record = True group by 1,2,3,4,5) id\n", "          LEFT JOIN (SELECT item_id, product_id FROM rpc.item_product_mapping where lake_active_record = True) ipm ON ipm.item_id = id.item_id\n", "          LEFT JOIN (SELECT id, type_id FROM cms.gr_product where lake_active_record = True) gp ON gp.id = ipm.product_id\n", "          LEFT JOIN (SELECT id, name AS p_type FROM cms.gr_product_type where lake_active_record = True) pt ON pt.id = gp.type_id\n", "     )\n", "group by 1,2,3,4,5\n", "),\n", "\n", "\n", "\n", "final as (  \n", "select t.transit_trip_id trip_id,\n", "           trip_date,\n", "           inw.created_at,\n", "           DATE_FORMAT(unloading_completed_at, '%%Y-%%m-%%d %%l:%%i %%p') unloading_completed_at,\n", "           DATE_FORMAT(trip_com, '%%Y-%%m-%%d %%l:%%i %%p') qc_completed_at,\n", "           t.name receiver_outlet_name,\n", "           dest_id,\n", "           consignment_ids,\n", "           inw.grn_id,\n", "           source_outlet_names,\n", "           sourc_id,\n", "           sourc_name,\n", "           inv.type expected_type,\n", "           case when inw.variant_id IS NULL then NULL else coalesce(inw.type, 'EXCESS') end attributed_type,\n", "           inv.document_id,\n", "           id.item_id,\n", "           p.l0 as l0,\n", "           p.l1 as l1,\n", "           p.l2 as l2,\n", "        --   p.ptype,\n", "           id.name item_name,\n", "           inv.variant_id,\n", "        --   v.variant_description,\n", "           ilp.landing_price,\n", "        --   qc_by,\n", "           expected_quantity,\n", "           coalesce(quantity, 0) grn_quantity,\n", "           coalesce(good_grn_quantity, 0) good_grn_quantity,\n", "           coalesce(damaged_grn_quantity, 0) damaged_grn_quantity,\n", "           coalesce(near_expiry_grn_quantity, 0) near_expiry_grn_quantity,\n", "           coalesce(expired_grn_quantity, 0) expired_grn_quantity,\n", "           case when expected_quantity > 0 then expected_quantity - coalesce(quantity, 0) end short_quantity\n", "       \n", "        from t\n", "        join inv on inv.reverse_trip_id = t.trip_id\n", "        left join inw on inw.trip_id = inv.reverse_trip_id and inw.variant_id = inv.variant_id\n", "        and coalesce(inw.document_id, 'NULL') = coalesce(inv.document_id, 'NULL')\n", "        -- left join qc on qc.trip_id = inv.reverse_trip_id and qc.variant_id = inv.variant_id\n", "        -- join lake_rpc.product_product v on v.variant_id = inv.variant_id\n", "        join (select item_id, variant_id from rpc.product_product where lake_active_record = True and coalesce(handling_type,'0') <> '8' group by 1,2) v on v.variant_id = inv.variant_id\n", "\n", "        join lake_rpc.item_details id on id.item_id = v.item_id\n", "        join retail.console_outlet co on co.name = t.name\n", "        left join ims.ims_inventory_landing_price ilp on ilp.outlet_id = co.id and ilp.variant_id = inv.variant_id\n", "        \n", "        left join ptype p on p.item_id =  id.item_id\n", "        \n", "        -- where dest_id = 1104  \n", "        -- and sourc_id in (3768,2733,4318)\n", "        -- and sourc_id in (3768)\n", "        -- and transit_trip_id = '10561_1008114'\n", "        order by 1,18 DESC\n", "        -- limit 1000\n", "),\n", "\n", "f as (select  date(created_at) as trans_created_at,\n", "        trip_id,\n", "        l0,\n", "        l1,\n", "        l2,\n", "        -- grn_id,\n", "        dest_id,\n", "        rid.outlet_name as receiver_outlet_name,\n", "        sourc_id,\n", "        sid.outlet_name as sender_outlet_name,\n", "        sum(case when ((expected_type = 'RSTO' or expected_type is null) and attributed_type = 'EXCESS') then (good_grn_quantity*landing_price) else 0 end) as crwi_good,\n", "        sum(case when ((expected_type = 'RSTO' or expected_type is null) and attributed_type = 'EXCESS') then (good_grn_quantity) else 0 end) as crwi_good_qty\n", "from final \n", "left join (select outlet_id, outlet_name, outlet_type, case when city = 'Gurgaon' then 'HR-NCR' else city end as sender_outlet_city \n", "            from ba_etls.outlet_details group by 1,2,3,4) as sid on final.sourc_id=sid.outlet_id\n", "left join (select outlet_id, outlet_name, outlet_type, case when city = 'Gurgaon' then 'HR-NCR' else city end as receiver_outlet_city \n", "            from ba_etls.outlet_details group by 1,2,3,4) as rid on final.dest_id=rid.outlet_id\n", "group by 1,2,3,4,5,6,7,8,9),\n", "\n", "temp as (select trans_created_at, trip_id, dest_id, count(distinct sourc_id) as n_source from f group by 1,2,3),\n", "\n", "ff as (select f.*,t.n_source from f left join temp t on t.trans_created_at =  f. trans_created_at and t.trip_id = f.trip_id and t.dest_id = f.dest_id ),\n", "\n", "fff as (select trans_created_at, trip_id,l0,l1,l2,dest_id, receiver_outlet_name, sourc_id, sender_outlet_name, (crwi_good/n_source) as crwi_good, (crwi_good_qty/n_source) as crwi_good_qty from ff)\n", "-- ,\n", "\n", "-- ffff as (\n", "select trans_created_at,l0,l1,l2,dest_id, receiver_outlet_name, sourc_id, sender_outlet_name,sum(crwi_good) as crwi_good, sum(crwi_good_qty) as crwi_good_qty  from fff  group by 1,2,3,4,5,6,7,8\n", "-- )\n", "\n", "-- select extract(week from trans_created_at) as week, sum(crwi_good), sum(crwi_good_qty) from ffff group by 1\n", "),\n", "\n", "ptype as(\n", "SELECT id.item_id,\n", "       pt.p_type as ptype,\n", "       l0,\n", "       l1,\n", "       l2\n", "\n", "FROM (\n", "        (SELECT item_id, name AS item_name, l0, l1, l2 FROM rpc.item_category_details where lake_active_record = True group by 1,2,3,4,5) id\n", "          LEFT JOIN (SELECT item_id, product_id FROM rpc.item_product_mapping where lake_active_record = True) ipm ON ipm.item_id = id.item_id\n", "          LEFT JOIN (SELECT id, type_id FROM cms.gr_product where lake_active_record = True) gp ON gp.id = ipm.product_id\n", "          LEFT JOIN (SELECT id, name AS p_type FROM cms.gr_product_type where lake_active_record = True) pt ON pt.id = gp.type_id\n", "     )\n", "group by 1,2,3,4,5\n", "),\n", "\n", "\n", "dispatch_and_grn as\n", "(\n", "SELECT \n", "    date(invoice_billed_date_ist) as date,\n", "    case when open_dispatch_qty > 0 then 'qc_open' else 'qc_closed' end as grn_status,\n", "    sender_outlet_id,\n", "    o1.outlet_name as sender_outlet_name,\n", "    o1.outlet_type as sender_outlet_type,\n", "    l0,\n", "    l1,\n", "    l2,\n", "    receiver_outlet_id,\n", "    o2.outlet_name as receiver_outlet_name,\n", "    o2.outlet_type as receiver_outlet_type,\n", "    sender_outlet_city,\n", "    receiver_outlet_city,\n", "    \n", "    cast((sum(billed_qty)) as integer)   billed_qty,\n", "    cast((sum(dispatch_qty)) as integer)  quantity_dispatched_total,\n", "    cast((sum(grn_qty)) as integer) grn_qty_actual,\n", "    cast((sum(dn_short_qty)) as integer) dn_short_qty,\n", "    cast((sum(dn_other_qty)) as integer) dn_other_qty,\n", "    cast((sum(b2b_qty)) as integer) b2b_qty,\n", "    \n", "    cast(Round(sum( billed_amt)) as integer) billed_amt,\n", "    cast(Round(sum( dispatch_amt)) as integer) amt_dispatched_total,\n", "    cast(Round(sum( grn_amt)) as integer) grn_amt_actual,\n", "    cast(Round(sum( dn_short_amt)) as integer) dn_short_amt,\n", "    cast(Round(sum( dn_other_amt)) as integer) dn_other_amt,\n", "    cast(Round(sum( b2b_amt)) as integer) b2b_amt,\n", "\n", "    sum(open_billed_qty) as open_billed_qty,\n", "    sum(open_dispatch_qty) as open_dispatch_qty,\n", "    \n", "    cast(Round(sum(open_dn_short_qty)) as integer)open_dn_short_qty,\n", "    cast(Round(sum(open_dn_other_qty)) as integer)open_dn_other_qty,\n", "    cast(Round(sum(transfer_loss_qty)) as integer)transfer_loss_qty,\n", "    cast(sum(open_billed_amt) as double) as open_billed_amt,\n", "    cast(sum(open_dispatch_amt) as double) as open_dispatch_amt,\n", "    cast(Round(sum(open_dn_short_amt)) as integer)open_dn_short_amt,\n", "    cast(Round(sum(open_dn_other_amt)) as integer)open_dn_other_amt,\n", "    cast(Round(sum(transfer_loss_amt)) as integer)transfer_loss_amt\n", "     \n", "FROM dwh.fact_inventory_transfer_details tl --dwh.fact_sto_invoice_item_details tl\n", "left join (select outlet_id, outlet_name, outlet_type, case when city = 'Gurgaon' then 'HR-NCR' else city end as sender_outlet_city \n", "            from ba_etls.outlet_details group by 1,2,3,4) o1 on o1.outlet_id = tl.sender_outlet_id\n", "left join (select outlet_id, outlet_name, outlet_type, case when city = 'Gurgaon' then 'HR-NCR' else city end as receiver_outlet_city \n", "            from ba_etls.outlet_details group by 1,2,3,4) o2 on o2.outlet_id = tl.receiver_outlet_id\n", "left join (select item_id, item_name, item_type from ba_etls.item_details group by 1,2,3) i on i.item_id = tl.item_id\n", "\n", "left join ptype p on p.item_id = tl.item_id\n", "\n", "WHERE invoice_billed_date_ist >= current_date- interval '60' day --and current_date - interval '1' day\n", "    and item_type in ('packaged', 'perishable')\n", "    and (sender_outlet_id is not null or receiver_outlet_id is not null)\n", "    and o1.outlet_type='FE' and o2.outlet_type='BE'\n", "    \n", "    -- and receiver_outlet_id = 1104 and sender_outlet_id in (3768,2733,4318) and date(invoice_billed_date_ist) = date('2024-10-23')\n", "    -- and date(invoice_billed_date_ist) = '2024'\n", "       \n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "       )\n", "-- ,   \n", "\n", "\n", "-- yo as (\n", "select case when ag.date is null then cd.trans_created_at else ag.date end as date,\n", "        grn_status,\n", "        case when ag.receiver_outlet_name is null then cd.receiver_outlet_name else ag.receiver_outlet_name end as receiver_outlet_name,\n", "        case when ag.receiver_outlet_id is null then cd.dest_id else ag.receiver_outlet_id end as receiver_outlet_id,\n", "        case when ag.sender_outlet_name is null then cd.sender_outlet_name else ag.sender_outlet_name end as sender_outlet_name,\n", "        case when ag.sender_outlet_id is null then cd.sourc_id else ag.sender_outlet_id end as sender_outlet_id,\n", "        case when ag.l0 is null then cd.l0 else ag.l0 end as l0,\n", "        case when ag.l1 is null then cd.l1 else ag.l1 end as l1,\n", "        case when ag.l2 is null then cd.l2 else ag.l2 end as l2,\n", "        case when quantity_dispatched_total is null then 0 else quantity_dispatched_total end as quantity_dispatched_total,\n", "        case when amt_dispatched_total is null then 0 else amt_dispatched_total end as amt_dispatched_total,\n", "        case when grn_qty_actual is null then 0 else grn_qty_actual end as grn_qty_actual,\n", "        case when grn_amt_actual is null then 0 else grn_amt_actual end as grn_amt_actual,\n", "        case when crwi_good is null then 0 else crwi_good end as crwi_good,\n", "        case when crwi_good_qty is null then 0 else crwi_good_qty end as crwi_good_qty,\n", "        case when billed_qty is null then 0 else billed_qty end as billed_qty,\n", "        case when billed_amt is null then 0 else billed_amt end as billed_amt,\n", "        case when transfer_loss_amt is null then 0 else transfer_loss_amt end as transfer_loss_amt\n", "from dispatch_and_grn ag\n", "full outer join crwi_data as cd on (ag.receiver_outlet_id = cd.dest_id\n", "                                    and ag.sender_outlet_id = cd.sourc_id\n", "                                    and ag.date = cd.trans_created_at\n", "                                    and ag.l0 = cd.l0\n", "                                    and ag.l1 = cd.l1\n", "                                    and ag.l2 = cd.l2\n", "                                    and grn_status = 'qc_closed'\n", "                                    )\n", "                                    \n", "-- )\n", "\n", "\n", "-- select extract(week from date) as week, sum(transfer_loss_amt),sum (crwi_good) as crwi_good, sum(crwi_good_qty) as crwi_good_qty from yo group by 1\n", "-- where trip_id = ''\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e32525a5-e365-4c4c-b340-878c8e0467f3", "metadata": {}, "outputs": [], "source": ["ptype = read_sql_query(ptype, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "d119334e-56ca-44f1-be4e-65e89a1c55a9", "metadata": {}, "outputs": [], "source": ["# ptype.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b7741fdd-0f0a-4ca5-a2c1-2e425eda68e2", "metadata": {}, "outputs": [], "source": ["# Convert columns to float\n", "ptype[\"transfer_loss_amt\"] = pd.to_numeric(ptype[\"transfer_loss_amt\"], errors=\"coerce\").astype(\n", "    float\n", ")\n", "ptype[\"crwi_good\"] = pd.to_numeric(ptype[\"crwi_good\"], errors=\"coerce\").astype(float)\n", "\n", "ptype[\"net_transfer_loss\"] = ptype[\"transfer_loss_amt\"] - ptype[\"crwi_good\"]"]}, {"cell_type": "code", "execution_count": null, "id": "4b74b4e3-2929-41a3-b947-a276bfeb69f9", "metadata": {}, "outputs": [], "source": ["# ptype.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c6945730-7967-40b0-88a3-85020114e21b", "metadata": {}, "outputs": [], "source": ["# Convert the 'date' column to datetime format\n", "ptype[\"date\"] = pd.to_datetime(ptype[\"date\"], errors=\"coerce\")\n", "\n", "# Now create the 'week' column with the week number\n", "ptype[\"week\"] = ptype[\"date\"].dt.isocalendar().week"]}, {"cell_type": "code", "execution_count": null, "id": "a8b726df-2dda-43b5-9275-cd6a8196d7a7", "metadata": {}, "outputs": [], "source": ["# ptype.head()"]}, {"cell_type": "code", "execution_count": null, "id": "67e6fc2f-cb22-49b6-bf06-9b93b0a1f4c9", "metadata": {}, "outputs": [], "source": ["ptype[\"date\"].max()"]}, {"cell_type": "code", "execution_count": null, "id": "436103c1-b845-421a-9a3e-7606d180ca67", "metadata": {}, "outputs": [], "source": ["# Define current date as today\n", "current_Date = pd.Timestamp.today()\n", "ptype = ptype[ptype[\"date\"] <= current_Date - pd.Timedelta(days=2)]"]}, {"cell_type": "code", "execution_count": null, "id": "4de9e7e8-0063-4266-88c8-9cab77116fab", "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "!pip install matplotlib\n", "\n", "from tabulate import tabulate\n", "import textwrap as twp\n", "import matplotlib.pyplot as plt\n", "from pencilbox.connections import get_slack_client\n", "\n", "codsworth_client = get_slack_client(\"bl-analytics-bot\")"]}, {"cell_type": "code", "execution_count": null, "id": "a418ffb4-4cb3-42c8-9a11-3c7151df0c59", "metadata": {}, "outputs": [], "source": ["# def render_mpl_table(\n", "#     data,\n", "#     columns,\n", "#     #title,\n", "#     col_width=20,\n", "#     row_height=2,\n", "#     font_size=30,\n", "#     header_color=\"#6F19F3\",\n", "#     row_colors=[\"#f1f1f2\", \"w\"],\n", "#     edge_color=\"w\",\n", "#     bbox=[0, 0, 1, 1],\n", "#     header_columns=1,\n", "#     ax=None,\n", "#     **kwargs\n", "# ):\n", "#     if ax is None:\n", "#         size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "#             [col_width, row_height]\n", "#         )\n", "#         fig, ax = plt.subplots(figsize=size)\n", "#         ax.axis(\"off\")\n", "#         # ax.set_title(\n", "#         #     title, fontsize=30, weight=\"bold\", backgroundcolor=header_color, color=\"w\"\n", "#         # )\n", "#     mpl_table = ax.table(\n", "#         cellText=data.values, bbox=bbox, colLabels=columns, cellLoc=\"center\", **kwargs\n", "#     )\n", "#     mpl_table.scale(2, 4)\n", "#     mpl_table.auto_set_font_size(False)\n", "#     mpl_table.set_fontsize(font_size)\n", "#     mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "#     for k, cell in mpl_table._cells.items():\n", "#         cell.set_edgecolor(edge_color)\n", "#         if k[0] == 0 or k[1] < header_columns:\n", "#             cell.set_text_props(weight=\"bold\", color=\"w\")\n", "#             cell.set_facecolor(header_color)\n", "#         else:\n", "#             cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "#             cell.set_text_props(ha=\"center\")\n", "#     return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "2b35013b-a44e-4aba-bf1c-16d728907410", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import textwrap\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    columns,\n", "    min_col_width=1.5,  # Minimum column width\n", "    col_width=2.5,\n", "    row_height=2,\n", "    font_size=10,\n", "    header_color=\"#6F19F3\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"w\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=1,\n", "    padding=0.3,  # Left and right padding as a proportion of col_width\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        # Calculate column widths based on the maximum text length of cell contents only\n", "        col_widths = [\n", "            max(min_col_width, max([len(str(cell)) for cell in data[col]]) * 0.1)\n", "            for col in data.columns\n", "        ]\n", "        size = np.array([sum(col_widths), len(data) + 1]) * np.array([1, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    # Wrap column headers to fit within column widths\n", "    wrapped_columns = [\n", "        \"\\n\".join(textwrap.wrap(columns[i], width=int(col_widths[i] * 10)))\n", "        for i in range(len(columns))\n", "    ]\n", "\n", "    # Set up table\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=wrapped_columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    # Scale table to fit content\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Adjust each column's width with minimum padding\n", "    for i, width in enumerate(col_widths):\n", "        adjusted_width = width + padding  # Add padding to each column\n", "        for row in range(len(data) + 1):\n", "            cell = mpl_table[(row, i)]\n", "            cell.set_width(adjusted_width)\n", "            if row == 0:  # Wrap header text\n", "                cell.set_text_props(ha=\"center\")\n", "\n", "    # Style the header and cells\n", "    for (i, j), cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if i == 0:\n", "            cell.set_text_props(weight=\"bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[i % len(row_colors)])\n", "            cell.set_text_props(ha=\"center\")\n", "    return ax.get_figure(), ax"]}, {"cell_type": "markdown", "id": "968da919-25a7-4688-9309-e3a8e70a5a2a", "metadata": {}, "source": ["# top_warehouses_loss"]}, {"cell_type": "code", "execution_count": null, "id": "b01af3ca-b154-4472-83be-509b9767a178", "metadata": {}, "outputs": [], "source": ["# grouped_ptype_data = ptype[ptype.grn_status == 'qc_closed'].groupby(['l0','l1','l2'])[['net_transfer_loss','billed_amt']].sum().reset_index()\n", "grouped_whlevel_data = (\n", "    ptype[(ptype[\"grn_status\"] == \"qc_closed\") | (ptype[\"grn_status\"].isnull())]\n", "    .groupby([\"week\", \"receiver_outlet_name\", \"receiver_outlet_id\"])[[\"net_transfer_loss\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "# grouped_whlevel_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9bdefb9e-9c8e-4801-ad8b-ec2e4d84b63f", "metadata": {}, "outputs": [], "source": ["# grouped_whlevel_data['total_loss'] = grouped_whlevel_data.groupby(['receiver_outlet_name','receiver_outlet_id'])['net_transfer_loss'].transform('sum')"]}, {"cell_type": "code", "execution_count": null, "id": "d71004c2-a9d1-4980-ac15-21564e364ec5", "metadata": {}, "outputs": [], "source": ["# grouped_whlevel_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d03f5687-4a5b-497e-bfed-64bcea03dc10", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "# Set float format to avoid scientific notation\n", "pd.options.display.float_format = \"{:.2f}\".format"]}, {"cell_type": "code", "execution_count": null, "id": "f03c24ba-383a-4f94-ac37-6955d612c7e4", "metadata": {}, "outputs": [], "source": ["# grouped_whlevel_data[grouped_whlevel_data['receiver_outlet_name']=='Pune P2 - Feeder']"]}, {"cell_type": "code", "execution_count": null, "id": "350b4f2f-464a-459b-a6d5-7376e10de673", "metadata": {}, "outputs": [], "source": ["# Pivot table by 'week'\n", "pivot_whlevel = grouped_whlevel_data.pivot(\n", "    index=[\"receiver_outlet_name\", \"receiver_outlet_id\"], columns=\"week\", values=\"net_transfer_loss\"\n", ")\n", "\n", "# Optional: reset index if you want a cleaner look or easier access to outlet names as columns\n", "pivot_whlevel = pivot_whlevel.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "4123b8b9-9e8d-4767-a622-a6e5b28806cf", "metadata": {}, "outputs": [], "source": ["del grouped_whlevel_data"]}, {"cell_type": "code", "execution_count": null, "id": "38c09fe2-e0dd-40fe-aad4-60c9a4c7493d", "metadata": {}, "outputs": [], "source": ["# pivot_whlevel"]}, {"cell_type": "code", "execution_count": null, "id": "191c956f-49e3-4ef6-973e-4de62d34851b", "metadata": {}, "outputs": [], "source": ["# Select columns that represent weeks by checking if their names are numeric\n", "week_columns = [col for col in pivot_whlevel.columns if str(col).isnumeric()]\n", "\n", "# Add a new column 'total_net_transfer_loss' that contains the sum of values across the week columns\n", "pivot_whlevel[\"total_net_transfer_loss\"] = pivot_whlevel[week_columns].sum(axis=1)\n", "pivot_whlevel[\"median_net_transfer_loss_weekly\"] = pivot_whlevel[week_columns].median(axis=1)\n", "pivot_whlevel[\"avg_net_transfer_loss_weekly\"] = pivot_whlevel[week_columns].mean(axis=1)\n", "\n", "# print(pivot_whlevel[['receiver_outlet_name', 'receiver_outlet_id', 'total_net_transfer_loss']])"]}, {"cell_type": "code", "execution_count": null, "id": "3dc9c754-e73f-4043-bfa9-893192d2addf", "metadata": {}, "outputs": [], "source": ["# pivot_whlevel"]}, {"cell_type": "code", "execution_count": null, "id": "b62c11eb-f1c8-4923-91ac-cee852a46521", "metadata": {}, "outputs": [], "source": ["# Define the columns representing weekly losses\n", "week_columns = [col for col in pivot_whlevel.columns if str(col).isnumeric()]\n", "\n", "# Calculate the percentage of positive weekly losses for each outlet\n", "pivot_whlevel[\"percent_positive_losses\"] = (\n", "    pivot_whlevel[week_columns].gt(0).sum(axis=1)\n", "    / pivot_whlevel[week_columns].notna().sum(axis=1)\n", "    * 100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fdc0beb9-3186-428d-b571-b525484d7301", "metadata": {}, "outputs": [], "source": ["# pivot_whlevel"]}, {"cell_type": "code", "execution_count": null, "id": "2ce4a508-4a59-4702-b462-1e73be8dbef7", "metadata": {}, "outputs": [], "source": ["# pivot_whlevel.sort_values(by='total_net_transfer_loss',ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "deeb66bf-4847-4163-a581-d31331829fbb", "metadata": {}, "outputs": [], "source": ["# pivot_whlevel.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "dec3be5d-9585-41fc-ba54-62ae662674f2", "metadata": {}, "outputs": [], "source": ["t = pivot_whlevel[\n", "    (pivot_whlevel[\"total_net_transfer_loss\"] > 50000)\n", "    & (pivot_whlevel[\"percent_positive_losses\"] > 70)\n", "    & (pivot_whlevel[\"median_net_transfer_loss_weekly\"] > 50000)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "774c2687-be3e-4490-a94d-40c069b22bed", "metadata": {}, "outputs": [], "source": ["final_whlevel = t.sort_values(by=\"total_net_transfer_loss\", ascending=False).head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "bf161c52-0bee-43fa-b637-71980deffb69", "metadata": {}, "outputs": [], "source": ["del pivot_whlevel\n", "del t"]}, {"cell_type": "code", "execution_count": null, "id": "c13c8e85-9653-4870-bfaf-b781376bfad7", "metadata": {}, "outputs": [], "source": ["# final_whlevel"]}, {"cell_type": "code", "execution_count": null, "id": "b2f10d2d-85ab-4617-b44f-7cd35da228b9", "metadata": {}, "outputs": [], "source": ["# Drop columns 'l1' and 'l2' from finalwhl2\n", "# final_whlevel = final_whlevel.drop(columns=['l1', 'l2'])\n", "final_whlevel[final_whlevel.select_dtypes(include=[\"number\"]).columns] = (\n", "    final_whlevel.select_dtypes(include=[\"number\"]).round().astype(int)\n", ")\n", "\n", "final_whlevelp = final_whlevel\n", "\n", "# Rename the column\n", "final_whlevelp = final_whlevelp.rename(columns={\"receiver_outlet_name\": \"Receiver Outlet Name\"})\n", "final_whlevelp = final_whlevelp.rename(columns={\"receiver_outlet_id\": \"Receiver Outlet ID\"})\n", "\n", "final_whlevelp = final_whlevelp.rename(\n", "    columns={\"total_net_transfer_loss\": \"Total Loss Past 60 Days\"}\n", ")\n", "final_whlevelp = final_whlevelp.rename(\n", "    columns={\"median_net_transfer_loss_weekly\": \"Median Loss Weekly\"}\n", ")\n", "final_whlevelp = final_whlevelp.rename(\n", "    columns={\"avg_net_transfer_loss_weekly\": \"Average Loss Weekly\"}\n", ")\n", "final_whlevelp = final_whlevelp.rename(\n", "    columns={\"percent_positive_losses\": \"% Positive Losses Past Weeks\"}\n", ")\n", "\n", "# # Round columns to integers\n", "# final_whlevel['total_net_transfer_loss_past_60days'] = final_whlevel['total_net_transfer_loss_past_60days'].round().astype(int)\n", "# final_whlevel['avg_net_transfer_loss_weekly'] = final_whlevel['avg_net_transfer_loss_weekly'].round().astype(int)\n", "# Round all numeric columns to the nearest integer and convert to int\n", "# final_whlevel = final_whlevel.round().astype(int)\n", "# Apply rounding and integer conversion only to numeric columns\n", "# final_whlevel[final_whlevel.select_dtypes(include=['number']).columns] = final_whlevel.select_dtypes(include=['number']).round().astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "4e90bbe2-9cfb-495f-8bd7-54c93dd73528", "metadata": {}, "outputs": [], "source": ["# final_whlevel = final_whlevel[['receiver_outlet_name','receiver_outlet_id','total_net_transfer_loss_past_60days','avg_net_transfer_loss_weekly','percent_positive_losses_past_weeks']]"]}, {"cell_type": "code", "execution_count": null, "id": "90530aef-21de-4f86-b2b1-8adfe7a6b05d", "metadata": {}, "outputs": [], "source": ["# Format all integer columns with commas for thousands separators\n", "final_whlevelp = final_whlevelp.applymap(lambda x: f\"{x:,}\" if isinstance(x, (int, float)) else x)"]}, {"cell_type": "code", "execution_count": null, "id": "1a117547-7057-4647-b28e-f75fca2017de", "metadata": {}, "outputs": [], "source": ["# Example usage\n", "if final_whlevelp.shape[0] > 0:\n", "    columns = [str(j) for j in final_whlevelp.columns]\n", "\n", "    fig, ax = render_mpl_table(\n", "        final_whlevelp,\n", "        header_columns=0,\n", "        columns=columns,\n", "        min_col_width=1.5,  # Define minimum column width\n", "        padding=0.3,  # Define padding for cell values as proportion of col_width\n", "    )\n", "\n", "    # DF_IMAGE_FILENAME = \"top_warehouses_loss.png\"\n", "    # fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "    # print(DF_IMAGE_FILENAME)\n", "\n", "    DF_IMAGE_FILENAME = \"/tmp/top_warehouses_loss.png\"\n", "    fig.savefig(DF_IMAGE_FILENAME)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        files=[\"/tmp/top_warehouses_loss.png\"],\n", "        text=\"Top Warehouses\",\n", "    )\n", "\n", "\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        text=\"No warehouse with losses\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "f6e12d82-b575-477a-b579-72e0dea83934", "metadata": {}, "outputs": [], "source": ["del final_whlevelp"]}, {"cell_type": "code", "execution_count": null, "id": "63799671-2316-4d89-ac83-79ea194be2f6", "metadata": {}, "outputs": [], "source": ["# # Example usage\n", "# if final_whlevelp.shape[0] > 0:\n", "#     columns = [str(j) for j in final_whlevelp.columns]\n", "\n", "#     fig, ax = render_mpl_table(\n", "#         final_whlevelp,\n", "#         header_columns=0,\n", "#         columns=columns,\n", "#         min_col_width=1.5,  # Define minimum column width\n", "#         padding=0.3  # Define padding for cell values as proportion of col_width\n", "#     )\n", "\n", "#     DF_IMAGE_FILENAME = \"top_warehouses_loss.png\"\n", "#     fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "#     print(DF_IMAGE_FILENAME)\n", "\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         files=[DF_IMAGE_FILENAME],\n", "#         text=\"Top Warehouses\",\n", "#     )\n", "# else:\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         text=\"No warehouse with losses\",\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "e3333a3c-212f-4ef1-a28d-8228984be594", "metadata": {}, "outputs": [], "source": ["# if final_whlevel.shape[0] > 0:\n", "#     columns = []\n", "#     for j in final_whlevel.columns:\n", "#         columns.append(str(j).l<PERSON><PERSON>(15))\n", "\n", "#     fig, ax = render_mpl_table(\n", "#         final_whlevel,\n", "#         header_columns=0,\n", "#         col_width=10,\n", "#         columns=columns,\n", "#     )\n", "\n", "#     DF_IMAGE_FILENAME = \"top_warehouses_loss.png\"\n", "#     fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "#     print(DF_IMAGE_FILENAME)\n", "\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         files=[DF_IMAGE_FILENAME],\n", "#         text=\"Top warehouses contributing to maximum consistent loss in reverse flow\",\n", "#     )\n", "# else:\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         text=\"No warehouse with losses\",\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "b1482012-0eec-4bb4-9003-0088cf8db861", "metadata": {}, "outputs": [], "source": ["# final_whlevel.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "317ede40-b546-43fa-9b54-09d573625bf9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ab5e2d2-91af-49d3-bef7-0b1b5f8321b1", "metadata": {}, "outputs": [], "source": ["# grouped_ptype_data = ptype[ptype.grn_status == 'qc_closed'].groupby(['l0','l1','l2'])[['net_transfer_loss','billed_amt']].sum().reset_index()\n", "grouped_whl2level_data = (\n", "    ptype[(ptype[\"grn_status\"] == \"qc_closed\") | (ptype[\"grn_status\"].isnull())]\n", "    .groupby([\"week\", \"receiver_outlet_name\", \"receiver_outlet_id\", \"l0\", \"l1\", \"l2\"])[\n", "        [\"net_transfer_loss\"]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "edd3c96d-e5f1-427e-93f2-2afd9151261c", "metadata": {}, "outputs": [], "source": ["grouped_whl2level_data = grouped_whl2level_data[\n", "    grouped_whl2level_data[\"receiver_outlet_id\"].isin(final_whlevel[\"receiver_outlet_id\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "90f60877-8f47-4b94-9b34-dd8992b7ec8c", "metadata": {}, "outputs": [], "source": ["# Pivot table by 'week'\n", "pivot_whl2level = grouped_whl2level_data.pivot(\n", "    index=[\"receiver_outlet_name\", \"receiver_outlet_id\", \"l0\", \"l1\", \"l2\"],\n", "    columns=\"week\",\n", "    values=\"net_transfer_loss\",\n", ")\n", "\n", "# Optional: reset index if you want a cleaner look or easier access to outlet names as columns\n", "pivot_whl2level = pivot_whl2level.reset_index()\n", "\n", "# Select columns that represent weeks by checking if their names are numeric\n", "week_columns = [col for col in pivot_whl2level.columns if str(col).isnumeric()]\n", "\n", "# Add a new column 'total_net_transfer_loss' that contains the sum of values across the week columns\n", "pivot_whl2level[\"total_net_transfer_loss\"] = pivot_whl2level[week_columns].sum(axis=1)\n", "pivot_whl2level[\"median_net_transfer_loss_weekly\"] = pivot_whl2level[week_columns].median(axis=1)\n", "pivot_whl2level[\"avg_net_transfer_loss_weekly\"] = pivot_whl2level[week_columns].mean(axis=1)\n", "\n", "# print(pivot_whlevel[['receiver_outlet_name', 'receiver_outlet_id', 'total_net_transfer_loss']])\n", "\n", "# Define the columns representing weekly losses\n", "week_columns = [col for col in pivot_whl2level.columns if str(col).isnumeric()]\n", "\n", "# Calculate the percentage of positive weekly losses for each outlet\n", "pivot_whl2level[\"percent_positive_losses\"] = (\n", "    pivot_whl2level[week_columns].gt(0).sum(axis=1)\n", "    / pivot_whl2level[week_columns].notna().sum(axis=1)\n", "    * 100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "02544ad1-4f58-48b3-841f-858e7ec8bab4", "metadata": {}, "outputs": [], "source": ["del grouped_whl2level_data\n", "del final_whlevel"]}, {"cell_type": "code", "execution_count": null, "id": "360a5d5b-675a-46e7-ab81-2238aae8f310", "metadata": {}, "outputs": [], "source": ["# pivot_whl2level.head()"]}, {"cell_type": "code", "execution_count": null, "id": "72d0c653-033e-4d1a-a296-a87a8068e0ca", "metadata": {}, "outputs": [], "source": ["# pivot_whl2level[pivot_whl2level['receiver_outlet_name'] == 'Dasna D3 - Feeder'].sort_values(by='total_net_transfer_loss',ascending = False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "135ae745-b598-422d-8937-542362457376", "metadata": {}, "outputs": [], "source": ["# pivot_whl2level.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "4cf62393-d498-40ad-9f2b-0dd8f117f4ff", "metadata": {}, "outputs": [], "source": ["# Calculate specific percentiles (85th, 90th, 95th, 99th) for each column\n", "# pivot_whl2level.quantile([0.85, 0.90, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "id": "30ae1e82-daaa-4868-9288-6e462cd7f5e2", "metadata": {}, "outputs": [], "source": ["pivot_whl2level1 = pivot_whl2level[\n", "    (pivot_whl2level[\"percent_positive_losses\"] >= 50)\n", "    & (pivot_whl2level[week_columns].notna().sum(axis=1) > 3)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a85eb328-b013-4cd3-a56e-e301a88cc61d", "metadata": {}, "outputs": [], "source": ["# Rank 'l2' within each 'receiver_outlet_id' by 'total_net_transfer_loss' in descending order\n", "pivot_whl2level1[\"l2_rank\"] = pivot_whl2level1.groupby(\"receiver_outlet_id\")[\n", "    \"total_net_transfer_loss\"\n", "].rank(method=\"dense\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d52feb74-d3ac-4ff0-b299-96c0a3dddf13", "metadata": {}, "outputs": [], "source": ["del pivot_whl2level"]}, {"cell_type": "code", "execution_count": null, "id": "605f30fc-2da5-472e-bcb1-1087361733c7", "metadata": {}, "outputs": [], "source": ["finalwhl2 = pivot_whl2level1[pivot_whl2level1[\"l2_rank\"] <= 3].sort_values(\n", "    by=[\"receiver_outlet_name\", \"l2_rank\"], ascending=[True, True]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "137bd5d6-9fc3-4904-8b40-4c3b29a7ac67", "metadata": {}, "outputs": [], "source": ["del pivot_whl2level1"]}, {"cell_type": "code", "execution_count": null, "id": "127fad7f-8c01-4892-b5cd-5d9123f99e6c", "metadata": {}, "outputs": [], "source": ["# finalwhl2"]}, {"cell_type": "code", "execution_count": null, "id": "1ff3793c-25ee-49c0-b670-26a3a441c331", "metadata": {}, "outputs": [], "source": ["# Drop columns 'l1' and 'l2' from finalwhl2\n", "finalwhl2 = finalwhl2.drop(columns=[\"l0\", \"l1\"])\n", "# Round only non-NaN numeric values to integers, leave NaNs as is\n", "finalwhl2[finalwhl2.select_dtypes(include=[\"number\"]).columns] = finalwhl2.select_dtypes(\n", "    include=[\"number\"]\n", ").applymap(lambda x: int(round(x)) if pd.notnull(x) else x)\n", "\n", "\n", "# Rename the column\n", "finalwhl2 = finalwhl2.rename(columns={\"receiver_outlet_name\": \"Receiver Outlet Name\"})\n", "finalwhl2 = finalwhl2.rename(columns={\"receiver_outlet_id\": \"Receiver Outlet ID\"})\n", "\n", "finalwhl2 = finalwhl2.rename(columns={\"total_net_transfer_loss\": \"Total Loss Past 60 Days\"})\n", "finalwhl2 = finalwhl2.rename(columns={\"median_net_transfer_loss_weekly\": \"Median Loss Weekly\"})\n", "finalwhl2 = finalwhl2.rename(columns={\"avg_net_transfer_loss_weekly\": \"Average Loss Weekly\"})\n", "finalwhl2 = finalwhl2.rename(columns={\"percent_positive_losses\": \"% Positive Losses Past Weeks\"})"]}, {"cell_type": "code", "execution_count": null, "id": "c84e0ce0-876b-455b-8ba3-2c18328ff9a6", "metadata": {}, "outputs": [], "source": ["# Format all integer columns with commas for thousands separators\n", "finalwhl2 = finalwhl2.applymap(lambda x: f\"{x:,}\" if isinstance(x, (int, float)) else x)"]}, {"cell_type": "code", "execution_count": null, "id": "6e04dcbf-ba46-42ae-bac8-f5aa2b200edb", "metadata": {}, "outputs": [], "source": ["# Example usage\n", "if finalwhl2.shape[0] > 0:\n", "    columns = [str(j) for j in finalwhl2.columns]\n", "\n", "    fig, ax = render_mpl_table(\n", "        finalwhl2,\n", "        header_columns=0,\n", "        columns=columns,\n", "        min_col_width=1.5,  # Define minimum column width\n", "        padding=0.3,  # Define padding for cell values as proportion of col_width\n", "    )\n", "\n", "    # DF_IMAGE_FILENAME = \"top_warehouses_L2_loss.png\"\n", "    # fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "    # print(DF_IMAGE_FILENAME)\n", "\n", "    DF_IMAGE_FILENAME = \"/tmp/top_warehouses_L2_loss.png\"\n", "    fig.savefig(DF_IMAGE_FILENAME)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        files=[\"/tmp/top_warehouses_L2_loss.png\"],\n", "        text=\"Top Warehouses x L2\",\n", "    )\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        text=\"No warehouse with losses\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "9c3623a5-2669-4596-8548-12aaf3784b34", "metadata": {}, "outputs": [], "source": ["del finalwhl2"]}, {"cell_type": "code", "execution_count": null, "id": "e57c377f-5e90-4c6f-b7b1-514297898c7c", "metadata": {}, "outputs": [], "source": ["import gc\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "c44b1286-7e5f-4fe5-bb78-01d99daeda34", "metadata": {}, "outputs": [], "source": ["# if finalwhl2.shape[0] > 0:\n", "#     columns = []\n", "#     for j in finalwhl2.columns:\n", "#         columns.append(str(j).l<PERSON><PERSON>(15))\n", "\n", "#     fig, ax = render_mpl_table(\n", "#         finalwhl2,\n", "#         header_columns=0,\n", "#         col_width=10,\n", "#         columns=columns,\n", "#     )\n", "\n", "#     DF_IMAGE_FILENAME = \"top_5warehouses_5l2_categories_loss.png\"\n", "#     fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "#     print(DF_IMAGE_FILENAME)\n", "\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         files=[DF_IMAGE_FILENAME],\n", "#         text=\"Top 5 warehouses and their 5 l2 categories contributing to maximum consistent loss in reverse flow\",\n", "#     )\n", "# else:\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         text=\"No warehouse and l2 category with losses\",\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "ff2b6850-39ba-433c-95b0-62cb38b0da41", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1f748002-3cb6-4dbd-bd5d-14fcfa051e5a", "metadata": {}, "source": ["# top_stores_losses"]}, {"cell_type": "code", "execution_count": null, "id": "715c70e2-09cb-4da1-b559-d3670bf150f4", "metadata": {}, "outputs": [], "source": ["# grouped_ptype_data = ptype[ptype.grn_status == 'qc_closed'].groupby(['l0','l1','l2'])[['net_transfer_loss','billed_amt']].sum().reset_index()\n", "grouped_dslevel_data = (\n", "    ptype[(ptype[\"grn_status\"] == \"qc_closed\") | (ptype[\"grn_status\"].isnull())]\n", "    .groupby([\"week\", \"sender_outlet_name\", \"sender_outlet_id\"])[[\"net_transfer_loss\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "# grouped_dslevel_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fa96c219-1389-4001-b147-bf2e32d8562e", "metadata": {}, "outputs": [], "source": ["# Pivot table by 'week'\n", "pivot_dslevel = grouped_dslevel_data.pivot(\n", "    index=[\"sender_outlet_name\", \"sender_outlet_id\"], columns=\"week\", values=\"net_transfer_loss\"\n", ")\n", "\n", "# Optional: reset index if you want a cleaner look or easier access to outlet names as columns\n", "pivot_dslevel = pivot_dslevel.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "63bb998c-b38a-4d71-821f-12db69c11a75", "metadata": {}, "outputs": [], "source": ["del grouped_dslevel_data"]}, {"cell_type": "code", "execution_count": null, "id": "5c37eddc-0e30-4270-841b-5199519f8dd9", "metadata": {}, "outputs": [], "source": ["# pivot_dslevel.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a13dec0c-657d-4dea-a2c3-7289529e053f", "metadata": {}, "outputs": [], "source": ["# Select columns that represent weeks by checking if their names are numeric\n", "week_columns = [col for col in pivot_dslevel.columns if str(col).isnumeric()]\n", "\n", "# Add a new column 'total_net_transfer_loss' that contains the sum of values across the week columns\n", "pivot_dslevel[\"total_net_transfer_loss\"] = pivot_dslevel[week_columns].sum(axis=1)\n", "pivot_dslevel[\"median_net_transfer_loss_weekly\"] = pivot_dslevel[week_columns].median(axis=1)\n", "pivot_dslevel[\"avg_net_transfer_loss_weekly\"] = pivot_dslevel[week_columns].mean(axis=1)\n", "\n", "# print(pivot_whlevel[['receiver_outlet_name', 'receiver_outlet_id', 'total_net_transfer_loss']])"]}, {"cell_type": "code", "execution_count": null, "id": "0a168657-c3a6-457c-8f18-1e923777c2c1", "metadata": {}, "outputs": [], "source": ["# pivot_dslevel.sort_values(by = 'total_net_transfer_loss', ascending = False).head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "590b5435-33a7-4060-962f-e92417a89d02", "metadata": {}, "outputs": [], "source": ["# Define the columns representing weekly losses\n", "week_columns = [col for col in pivot_dslevel.columns if str(col).isnumeric()]\n", "\n", "# Calculate the percentage of positive weekly losses for each outlet\n", "pivot_dslevel[\"percent_positive_losses\"] = (\n", "    pivot_dslevel[week_columns].gt(0).sum(axis=1)\n", "    / pivot_dslevel[week_columns].notna().sum(axis=1)\n", "    * 100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4d13c653-b6ba-4a3d-8733-987648fca08e", "metadata": {}, "outputs": [], "source": ["# pivot_dslevel.sort_values(by = 'total_net_transfer_loss', ascending = False).head(50)"]}, {"cell_type": "code", "execution_count": null, "id": "4d0bce21-0f29-417a-8204-72358d14d947", "metadata": {}, "outputs": [], "source": ["# pivot_dslevel.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "aebf77ce-8ae8-4189-996a-d15cc3175147", "metadata": {}, "outputs": [], "source": ["t_ds = pivot_dslevel[\n", "    (pivot_dslevel[\"total_net_transfer_loss\"] > 50000)\n", "    & (pivot_dslevel[\"percent_positive_losses\"] >= 50)\n", "    & (pivot_dslevel[\"median_net_transfer_loss_weekly\"] > 10000)\n", "    & (pivot_dslevel[week_columns].notna().sum(axis=1) > 3)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0ab9f2f3-664c-4409-9b4a-80123<PERSON>dee9a", "metadata": {}, "outputs": [], "source": ["finalds = t_ds.sort_values(by=\"total_net_transfer_loss\", ascending=False).head(30)"]}, {"cell_type": "code", "execution_count": null, "id": "2fc3d097-ee56-44cc-952b-32ffd2f6103e", "metadata": {}, "outputs": [], "source": ["del pivot_dslevel\n", "del t_ds"]}, {"cell_type": "code", "execution_count": null, "id": "86743642-8fe5-4804-92f1-5e0acd127bc3", "metadata": {}, "outputs": [], "source": ["# finalds"]}, {"cell_type": "code", "execution_count": null, "id": "d300dbe0-dde7-491c-9f7c-4fc5021865d9", "metadata": {}, "outputs": [], "source": ["# Drop columns 'l1' and 'l2' from finalwhl2\n", "# finalwhl2 = finalwhl2.drop(columns=['l0', 'l1'])\n", "# Round only non-NaN numeric values to integers, leave NaNs as is\n", "finalds[finalds.select_dtypes(include=[\"number\"]).columns] = finalds.select_dtypes(\n", "    include=[\"number\"]\n", ").applymap(lambda x: int(round(x)) if pd.notnull(x) else x)\n", "\n", "finaldsp = finalds\n", "\n", "# Rename the column\n", "finaldsp = finaldsp.rename(columns={\"sender_outlet_name\": \"Sender Outlet Name\"})\n", "finaldsp = finaldsp.rename(columns={\"sender_outlet_id\": \"Sender Outlet ID\"})\n", "\n", "finaldsp = finaldsp.rename(columns={\"total_net_transfer_loss\": \"Total Loss Past 60 Days\"})\n", "finaldsp = finaldsp.rename(columns={\"median_net_transfer_loss_weekly\": \"Median Loss Weekly\"})\n", "finaldsp = finaldsp.rename(columns={\"avg_net_transfer_loss_weekly\": \"Average Loss Weekly\"})\n", "finaldsp = finaldsp.rename(columns={\"percent_positive_losses\": \"% Positive Losses Past Weeks\"})"]}, {"cell_type": "code", "execution_count": null, "id": "d8abc699-9fc1-4589-ac28-0fff6759eceb", "metadata": {}, "outputs": [], "source": ["# finalds[finalds.select_dtypes(include=['number']).columns] = finalds.select_dtypes(include=['number']).round().astype(int)\n", "\n", "# # Rename the column\n", "# finalds = finalds.rename(columns={'total_net_transfer_loss': 'Total Loss Past 60 Days'})\n", "# finalds = finalds.rename(columns={'median_net_transfer_loss_weekly': 'Median Loss Weekly'})\n", "# finalds = finalds.rename(columns={'avg_net_transfer_loss_weekly': 'Average Loss Weekly'})\n", "# finalds = finalds.rename(columns={'percent_positive_losses': '% Positive Losses Past Weeks'})"]}, {"cell_type": "code", "execution_count": null, "id": "1c0d64e0-2ad9-406f-8bf0-9aba65410929", "metadata": {}, "outputs": [], "source": ["# Format all integer columns with commas for thousands separators\n", "finaldsp = finaldsp.applymap(lambda x: f\"{x:,}\" if isinstance(x, (int, float)) else x)"]}, {"cell_type": "code", "execution_count": null, "id": "b6e23701-1f0f-486b-b8d4-65b281125091", "metadata": {}, "outputs": [], "source": ["finaldsp = finaldsp.head(30)"]}, {"cell_type": "code", "execution_count": null, "id": "804299d4-2ee8-4595-b6aa-738313136dc6", "metadata": {}, "outputs": [], "source": ["# Example usage\n", "if finaldsp.shape[0] > 0:\n", "    columns = [str(j) for j in finaldsp.columns]\n", "\n", "    fig, ax = render_mpl_table(\n", "        finaldsp,\n", "        header_columns=0,\n", "        columns=columns,\n", "        min_col_width=1.5,  # Define minimum column width\n", "        padding=0.3,  # Define padding for cell values as proportion of col_width\n", "    )\n", "\n", "    # DF_IMAGE_FILENAME = \"top_darkstores_loss.png\"\n", "    # fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "    # print(DF_IMAGE_FILENAME)\n", "\n", "    DF_IMAGE_FILENAME = \"/tmp/top_store_loss.png\"\n", "    fig.savefig(DF_IMAGE_FILENAME)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        files=[\"/tmp/top_store_loss.png\"],\n", "        text=\"Top Dark Stores\",\n", "    )\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        text=\"No warehouse with losses\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "434cd247-a09c-4a25-921b-1603b34a41e3", "metadata": {}, "outputs": [], "source": ["# if finaldsp.shape[0] > 0:\n", "#     columns = []\n", "#     for j in finaldsp.columns:\n", "#         columns.append(str(j).l<PERSON><PERSON>(15))\n", "\n", "#     fig, ax = render_mpl_table(\n", "#         finaldsp,\n", "#         header_columns=0,\n", "#         col_width=10,\n", "#         columns=columns,\n", "#     )\n", "\n", "#     DF_IMAGE_FILENAME = \"top_5warehouses_5l2_categories_loss.png\"\n", "#     fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "#     print(DF_IMAGE_FILENAME)\n", "\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         files=[DF_IMAGE_FILENAME],\n", "#         text=\"Top Dark Stores contributing to maximum consistent loss in reverse flow\",\n", "#     )\n", "# else:\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         text=\"No Dark Store with losses\",\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "c8e5842a-9c2c-4140-a755-4aed73e36420", "metadata": {}, "outputs": [], "source": ["del finaldsp"]}, {"cell_type": "code", "execution_count": null, "id": "b3ab8733-7506-441c-9efd-ede254c549b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d8d0f2e8-c230-4efc-8dd4-ede7a9782e40", "metadata": {}, "outputs": [], "source": ["# grouped_ptype_data = ptype[ptype.grn_status == 'qc_closed'].groupby(['l0','l1','l2'])[['net_transfer_loss','billed_amt']].sum().reset_index()\n", "grouped_dsl2level_data = (\n", "    ptype[(ptype[\"grn_status\"] == \"qc_closed\") | (ptype[\"grn_status\"].isnull())]\n", "    .groupby([\"week\", \"sender_outlet_name\", \"sender_outlet_id\", \"l0\", \"l1\"])[[\"net_transfer_loss\"]]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8a743a86-5639-4543-a2f3-d73b1b0f6c15", "metadata": {}, "outputs": [], "source": ["grouped_dsl2level_data = grouped_dsl2level_data[\n", "    grouped_dsl2level_data[\"sender_outlet_id\"].isin(finalds[\"sender_outlet_id\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "8cfebdf4-9e26-47e1-bd97-3cf787448e61", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "00921561-f759-4e02-bfeb-6548c94ccec7", "metadata": {}, "outputs": [], "source": ["# Pivot table by 'week'\n", "pivot_dsl2level = grouped_dsl2level_data.pivot(\n", "    index=[\"sender_outlet_name\", \"sender_outlet_id\", \"l0\", \"l1\"],\n", "    columns=\"week\",\n", "    values=\"net_transfer_loss\",\n", ")\n", "\n", "# Optional: reset index if you want a cleaner look or easier access to outlet names as columns\n", "pivot_dsl2level = pivot_dsl2level.reset_index()\n", "\n", "# Select columns that represent weeks by checking if their names are numeric\n", "week_columns = [col for col in pivot_dsl2level.columns if str(col).isnumeric()]\n", "\n", "# Add a new column 'total_net_transfer_loss' that contains the sum of values across the week columns\n", "pivot_dsl2level[\"total_net_transfer_loss\"] = pivot_dsl2level[week_columns].sum(axis=1)\n", "pivot_dsl2level[\"median_net_transfer_loss_weekly\"] = pivot_dsl2level[week_columns].median(axis=1)\n", "pivot_dsl2level[\"avg_net_transfer_loss_weekly\"] = pivot_dsl2level[week_columns].mean(axis=1)\n", "\n", "# print(pivot_whlevel[['receiver_outlet_name', 'receiver_outlet_id', 'total_net_transfer_loss']])\n", "\n", "# Define the columns representing weekly losses\n", "week_columns = [col for col in pivot_dsl2level.columns if str(col).isnumeric()]\n", "\n", "# Calculate the percentage of positive weekly losses for each outlet\n", "pivot_dsl2level[\"percent_positive_losses\"] = (\n", "    pivot_dsl2level[week_columns].gt(0).sum(axis=1)\n", "    / pivot_dsl2level[week_columns].notna().sum(axis=1)\n", "    * 100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "181e59ae-4868-4acd-a4e5-8e0bdc0ba249", "metadata": {}, "outputs": [], "source": ["del grouped_dsl2level_data\n", "del finalds"]}, {"cell_type": "code", "execution_count": null, "id": "8b4c4d48-8481-4dad-b9e7-1e9e57e978d9", "metadata": {}, "outputs": [], "source": ["# pivot_dsl2level[pivot_dsl2level['sender_outlet_name'] == 'Super Store Faridabad Sector 10 ES8 PR'].sort_values(by='total_net_transfer_loss',ascending = False).head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "73e0a91d-28b1-4250-9f77-9bbbea03a775", "metadata": {}, "outputs": [], "source": ["pivot_dsl2level1 = pivot_dsl2level[\n", "    (pivot_dsl2level[week_columns].notna().sum(axis=1) > 3)\n", "    & (pivot_dsl2level[\"percent_positive_losses\"] >= 40)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7752548d-df10-4b60-8ed1-8d2aabe44d12", "metadata": {}, "outputs": [], "source": ["# Rank 'l2' within each 'receiver_outlet_id' by 'total_net_transfer_loss' in descending order\n", "pivot_dsl2level1[\"l2_rank\"] = pivot_dsl2level1.groupby(\"sender_outlet_id\")[\n", "    \"total_net_transfer_loss\"\n", "].rank(method=\"dense\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "e4b5f04c-3148-4593-8938-9b16133f613a", "metadata": {}, "outputs": [], "source": ["del pivot_dsl2level"]}, {"cell_type": "code", "execution_count": null, "id": "8b0cc565-4a85-45d9-b68b-da8add205355", "metadata": {}, "outputs": [], "source": ["# pivot_dsl2level1[pivot_dsl2level1['sender_outlet_name'] == 'Super Store Faridabad Sector 10 ES8 PR'].sort_values(by='total_net_transfer_loss',ascending = False).head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "ec119730-7597-4d36-a132-d0ff867014fc", "metadata": {}, "outputs": [], "source": ["finaldsl2p = pivot_dsl2level1[(pivot_dsl2level1[\"l2_rank\"] == 1)].sort_values(\n", "    by=\"total_net_transfer_loss\", ascending=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c47bd68c-ce7f-4434-9c60-2c93f1319f16", "metadata": {}, "outputs": [], "source": ["del pivot_dsl2level1"]}, {"cell_type": "code", "execution_count": null, "id": "dee109b7-32a8-4adc-a1fa-e282be9cfbe4", "metadata": {}, "outputs": [], "source": ["# Drop columns 'l1' and 'l2' from finalwhl2\n", "finaldsl2p = finaldsl2p.drop(columns=[\"l0\"])\n", "# Round only non-NaN numeric values to integers, leave NaNs as is\n", "finaldsl2p[finaldsl2p.select_dtypes(include=[\"number\"]).columns] = finaldsl2p.select_dtypes(\n", "    include=[\"number\"]\n", ").applymap(lambda x: int(round(x)) if pd.notnull(x) else x)\n", "\n", "# finaldsp = finalds\n", "\n", "# Rename the column\n", "finaldsl2p = finaldsl2p.rename(columns={\"sender_outlet_name\": \"Sender Outlet Name\"})\n", "finaldsl2p = finaldsl2p.rename(columns={\"sender_outlet_id\": \"Sender Outlet ID\"})\n", "\n", "finaldsl2p = finaldsl2p.rename(columns={\"total_net_transfer_loss\": \"Total Loss Past 60 Days\"})\n", "finaldsl2p = finaldsl2p.rename(columns={\"median_net_transfer_loss_weekly\": \"Median Loss Weekly\"})\n", "finaldsl2p = finaldsl2p.rename(columns={\"avg_net_transfer_loss_weekly\": \"Average Loss Weekly\"})\n", "finaldsl2p = finaldsl2p.rename(columns={\"percent_positive_losses\": \"% Positive Losses Past Weeks\"})"]}, {"cell_type": "code", "execution_count": null, "id": "781c2873-4e66-4a78-a570-98605181c0b5", "metadata": {}, "outputs": [], "source": ["# Format all integer columns with commas for thousands separators\n", "finaldsl2p = finaldsl2p.applymap(lambda x: f\"{x:,}\" if isinstance(x, (int, float)) else x)"]}, {"cell_type": "code", "execution_count": null, "id": "9e26a63b-291d-4b47-b4a3-363aeac72257", "metadata": {}, "outputs": [], "source": ["# finaldsl2p"]}, {"cell_type": "code", "execution_count": null, "id": "72bdd058-09eb-408a-a9c8-acddabc1fadf", "metadata": {}, "outputs": [], "source": ["# Example usage\n", "if finaldsl2p.shape[0] > 0:\n", "    columns = [str(j) for j in finaldsl2p.columns]\n", "\n", "    fig, ax = render_mpl_table(\n", "        finaldsl2p,\n", "        header_columns=0,\n", "        columns=columns,\n", "        min_col_width=1.5,  # Define minimum column width\n", "        padding=0.3,  # Define padding for cell values as proportion of col_width\n", "    )\n", "\n", "    # DF_IMAGE_FILENAME = \"top_dark_stores_l2_loss.png\"\n", "    # fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "    # print(DF_IMAGE_FILENAME)\n", "\n", "    DF_IMAGE_FILENAME = \"/tmp/top_store_L2_loss.png\"\n", "    fig.savefig(DF_IMAGE_FILENAME)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        files=[DF_IMAGE_FILENAME],\n", "        text=\"Top Dark Stores x L2\",\n", "    )\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        text=\"No warehouse with losses\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "72ca0ef2-3c19-4c85-813c-c14238d138b3", "metadata": {}, "outputs": [], "source": ["# if finalwhl2.shape[0] > 0:\n", "#     columns = []\n", "#     for j in finalwhl2.columns:\n", "#         columns.append(str(j).l<PERSON><PERSON>(15))\n", "\n", "#     fig, ax = render_mpl_table(\n", "#         finalwhl2,\n", "#         header_columns=0,\n", "#         col_width=10,\n", "#         columns=columns,\n", "#     )\n", "\n", "#     DF_IMAGE_FILENAME = \"top_5warehouses_5l2_categories_loss.png\"\n", "#     fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "#     print(DF_IMAGE_FILENAME)\n", "\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         files=[DF_IMAGE_FILENAME],\n", "#         text=\"Top 5 warehouses and their 5 l2 categories contributing to maximum consistent loss in reverse flow\",\n", "#     )\n", "# else:\n", "#     pb.send_slack_message(\n", "#         channel=\"bl-rsto-top-loss-alerts\",\n", "#         text=\"No warehouse and l2 category with losses\",\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "6d767d14-95d7-469b-9ad9-b3bd2844d194", "metadata": {}, "outputs": [], "source": ["del finaldsl2p"]}, {"cell_type": "code", "execution_count": null, "id": "318d213b-4fdd-4e86-a176-e73c133794ef", "metadata": {}, "outputs": [], "source": ["import gc\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "0baf9d2b-d556-461c-99ec-ae8930cbbf0b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8f57bc19-2929-41c2-876e-0175e655c2d1", "metadata": {}, "source": ["# top_p_types_l0"]}, {"cell_type": "code", "execution_count": null, "id": "480e43cc-90c7-4502-937e-d122558615d4", "metadata": {}, "outputs": [], "source": ["# grouped_ptype_data = ptype[ptype.grn_status == 'qc_closed'].groupby(['l0','l1','l2'])[['net_transfer_loss','billed_amt']].sum().reset_index()\n", "grouped_ptype_level_data = (\n", "    ptype[(ptype[\"grn_status\"] == \"qc_closed\") | (ptype[\"grn_status\"].isnull())]\n", "    .groupby([\"week\", \"l0\"])[[\"net_transfer_loss\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "# grouped_ptype_level_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a0ffc4ea-55c1-441d-b488-386ffd8cc70e", "metadata": {}, "outputs": [], "source": ["# Pivot table by 'week'\n", "pivot_ptype_level = grouped_ptype_level_data.pivot(\n", "    index=[\"l0\"], columns=\"week\", values=\"net_transfer_loss\"\n", ")\n", "\n", "# Optional: reset index if you want a cleaner look or easier access to outlet names as columns\n", "pivot_ptype_level = pivot_ptype_level.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "df37bcc5-f9bf-4e3a-af37-4855fb4ab7a1", "metadata": {}, "outputs": [], "source": ["del grouped_ptype_level_data"]}, {"cell_type": "code", "execution_count": null, "id": "e0fcc0f1-63e6-49ce-95a7-0fac074f402b", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level"]}, {"cell_type": "code", "execution_count": null, "id": "b0fcfa1e-a1fe-4230-993c-9c41cecfa4c2", "metadata": {}, "outputs": [], "source": ["# Select columns that represent weeks by checking if their names are numeric\n", "week_columns = [col for col in pivot_ptype_level.columns if str(col).isnumeric()]\n", "\n", "# Add a new column 'total_net_transfer_loss' that contains the sum of values across the week columns\n", "pivot_ptype_level[\"total_net_transfer_loss\"] = pivot_ptype_level[week_columns].sum(axis=1)\n", "pivot_ptype_level[\"median_net_transfer_loss_weekly\"] = pivot_ptype_level[week_columns].median(\n", "    axis=1\n", ")\n", "pivot_ptype_level[\"avg_net_transfer_loss_weekly\"] = pivot_ptype_level[week_columns].mean(axis=1)\n", "\n", "# print(pivot_whlevel[['receiver_outlet_name', 'receiver_outlet_id', 'total_net_transfer_loss']])"]}, {"cell_type": "code", "execution_count": null, "id": "a7d9c2e3-f3d6-4373-b126-feef1b204b8b", "metadata": {}, "outputs": [], "source": ["# Define the columns representing weekly losses\n", "week_columns = [col for col in pivot_ptype_level.columns if str(col).isnumeric()]\n", "\n", "# Calculate the percentage of positive weekly losses for each outlet\n", "pivot_ptype_level[\"percent_positive_losses\"] = (\n", "    pivot_ptype_level[week_columns].gt(0).sum(axis=1)\n", "    / pivot_ptype_level[week_columns].notna().sum(axis=1)\n", "    * 100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c21151b2-9460-439a-9ec3-d8ae31606af7", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level.sort_values(by = 'total_net_transfer_loss', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "302f2e43-cb40-45db-aaa7-ef9193e98317", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "0736e44a-82dd-45d9-8942-6e046275feb4", "metadata": {}, "outputs": [], "source": ["# Calculate specific percentiles (85th, 90th, 95th, 99th) for each column\n", "# pivot_ptype_level.quantile([0.85, 0.90, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "id": "99666af7-7bd6-4564-a7d2-1364f0fdbe89", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level.sort_values(by = 'total_net_transfer_loss', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "92faa84d-1a47-4907-b3c6-60cc27381bd6", "metadata": {}, "outputs": [], "source": ["t_ptype = pivot_ptype_level[\n", "    (pivot_ptype_level[\"total_net_transfer_loss\"] > 50000)\n", "    & (pivot_ptype_level[\"percent_positive_losses\"] >= 50)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "54601b26-b0aa-4c62-89f6-f8d9d6ff8107", "metadata": {}, "outputs": [], "source": ["finall0 = t_ptype.sort_values(by=\"total_net_transfer_loss\", ascending=False).head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "acc11ae9-caa6-45bd-b25b-eedf5181201e", "metadata": {}, "outputs": [], "source": ["del pivot_ptype_level\n", "del t_ptype"]}, {"cell_type": "code", "execution_count": null, "id": "39e5b58b-092d-4359-8b60-bd6efe303dd1", "metadata": {}, "outputs": [], "source": ["# Round only non-NaN numeric values to integers, leave NaNs as is\n", "finall0[finall0.select_dtypes(include=[\"number\"]).columns] = finall0.select_dtypes(\n", "    include=[\"number\"]\n", ").applymap(lambda x: int(round(x)) if pd.notnull(x) else x)\n", "\n", "# finaldsp = finalds\n", "finall0 = finall0.rename(columns={\"total_net_transfer_loss\": \"Total Loss Past 60 Days\"})\n", "finall0 = finall0.rename(columns={\"median_net_transfer_loss_weekly\": \"Median Loss Weekly\"})\n", "finall0 = finall0.rename(columns={\"avg_net_transfer_loss_weekly\": \"Average Loss Weekly\"})\n", "finall0 = finall0.rename(columns={\"percent_positive_losses\": \"% Positive Losses Past Weeks\"})"]}, {"cell_type": "code", "execution_count": null, "id": "5642da0e-f7bf-49c8-8fb1-c987bee67c80", "metadata": {}, "outputs": [], "source": ["# finall0"]}, {"cell_type": "code", "execution_count": null, "id": "84dc42d5-4cf2-40e4-b076-f5084d6b9211", "metadata": {}, "outputs": [], "source": ["# Format all integer columns with commas for thousands separators\n", "finall0 = finall0.applymap(lambda x: f\"{x:,}\" if isinstance(x, (int, float)) else x)"]}, {"cell_type": "code", "execution_count": null, "id": "aea464cd-5950-4532-af07-31ac1df78884", "metadata": {}, "outputs": [], "source": ["# Example usage\n", "if finall0.shape[0] > 0:\n", "    columns = [str(j) for j in finall0.columns]\n", "\n", "    fig, ax = render_mpl_table(\n", "        finall0,\n", "        header_columns=0,\n", "        columns=columns,\n", "        min_col_width=1.5,  # Define minimum column width\n", "        padding=0.3,  # Define padding for cell values as proportion of col_width\n", "    )\n", "\n", "    # DF_IMAGE_FILENAME = \"top_L0_loss.png\"\n", "    # fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "    # print(DF_IMAGE_FILENAME)\n", "\n", "    DF_IMAGE_FILENAME = \"/tmp/top_L0_loss.png\"\n", "    fig.savefig(DF_IMAGE_FILENAME)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        files=[DF_IMAGE_FILENAME],\n", "        text=\"Top L0\",\n", "    )\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        text=\"No warehouse with losses\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "aafae680-0d2f-4c94-8a37-caf90a9ed88a", "metadata": {}, "outputs": [], "source": ["del finall0"]}, {"cell_type": "code", "execution_count": null, "id": "3b4f4bd8-b2bf-4ad9-b397-7febd18721c8", "metadata": {}, "outputs": [], "source": ["import gc\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "6e54aa76-1db0-4296-8053-72a9175c68bf", "metadata": {}, "source": ["# top_p_type_l1"]}, {"cell_type": "code", "execution_count": null, "id": "cd245718-b975-49fa-ae7c-2f16aa848fb3", "metadata": {}, "outputs": [], "source": ["# grouped_ptype_data = ptype[ptype.grn_status == 'qc_closed'].groupby(['l0','l1','l2'])[['net_transfer_loss','billed_amt']].sum().reset_index()\n", "grouped_ptype_level_data_l1 = (\n", "    ptype[(ptype[\"grn_status\"] == \"qc_closed\") | (ptype[\"grn_status\"].isnull())]\n", "    .groupby([\"week\", \"l0\", \"l1\"])[[\"net_transfer_loss\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "# grouped_ptype_level_data_l1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "abc36ce1-4d9e-4bec-9d09-2e574a9c344f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eeda0cd7-bd1a-4bf0-abeb-2d5029416bd2", "metadata": {}, "outputs": [], "source": ["# Pivot table by 'week'\n", "pivot_ptype_level_l1 = grouped_ptype_level_data_l1.pivot(\n", "    index=[\"l0\", \"l1\"], columns=\"week\", values=\"net_transfer_loss\"\n", ")\n", "\n", "# Optional: reset index if you want a cleaner look or easier access to outlet names as columns\n", "pivot_ptype_level_l1 = pivot_ptype_level_l1.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "0001c471-9479-4768-adcb-7dc7afb53395", "metadata": {}, "outputs": [], "source": ["del grouped_ptype_level_data_l1"]}, {"cell_type": "code", "execution_count": null, "id": "bf3ad86b-2542-4746-97c7-af89602ccf91", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level_l1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fe5c9f0c-83e9-4e14-9c61-a5b41b89d6ec", "metadata": {}, "outputs": [], "source": ["# Select columns that represent weeks by checking if their names are numeric\n", "week_columns = [col for col in pivot_ptype_level_l1.columns if str(col).isnumeric()]\n", "\n", "# Add a new column 'total_net_transfer_loss' that contains the sum of values across the week columns\n", "pivot_ptype_level_l1[\"total_net_transfer_loss\"] = pivot_ptype_level_l1[week_columns].sum(axis=1)\n", "pivot_ptype_level_l1[\"median_net_transfer_loss_weekly\"] = pivot_ptype_level_l1[week_columns].median(\n", "    axis=1\n", ")\n", "pivot_ptype_level_l1[\"avg_net_transfer_loss_weekly\"] = pivot_ptype_level_l1[week_columns].mean(\n", "    axis=1\n", ")\n", "\n", "# print(pivot_whlevel[['receiver_outlet_name', 'receiver_outlet_id', 'total_net_transfer_loss']])\n", "\n", "# Define the columns representing weekly losses\n", "week_columns = [col for col in pivot_ptype_level_l1.columns if str(col).isnumeric()]\n", "\n", "# Calculate the percentage of positive weekly losses for each outlet\n", "pivot_ptype_level_l1[\"percent_positive_losses\"] = (\n", "    pivot_ptype_level_l1[week_columns].gt(0).sum(axis=1)\n", "    / pivot_ptype_level_l1[week_columns].notna().sum(axis=1)\n", "    * 100\n", ")\n", "\n", "# pivot_ptype_level_l1.sort_values(by = 'total_net_transfer_loss', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "1c1cd54e-c8ab-49cc-8a83-40de37bad12c", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level_l1.sort_values(by = 'total_net_transfer_loss', ascending = False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "2f3312d0-1b7d-4e10-9585-cd514a68246b", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level_l1.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "8fb71407-5f6e-4c39-beae-2417fe0b64fe", "metadata": {}, "outputs": [], "source": ["# Calculate specific percentiles (85th, 90th, 95th, 99th) for each column\n", "# pivot_ptype_level_l1.quantile([0.85, 0.90, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "id": "72ee9b83-479b-4067-be58-e600b19c7f61", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level_l1.sort_values(by = 'total_net_transfer_loss', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "59f69fb7-c4df-4a72-9e3f-11b1a715b908", "metadata": {}, "outputs": [], "source": ["t_ptype_l1 = pivot_ptype_level_l1[\n", "    (pivot_ptype_level_l1[\"total_net_transfer_loss\"] > 150000)\n", "    & (pivot_ptype_level_l1[\"percent_positive_losses\"] >= 50)\n", "]\n", "finall1 = t_ptype_l1.sort_values(by=\"total_net_transfer_loss\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2fc2380-495e-4833-9a5f-c7700eb01044", "metadata": {}, "outputs": [], "source": ["del t_ptype_l1\n", "del pivot_ptype_level_l1"]}, {"cell_type": "code", "execution_count": null, "id": "806412e7-4133-4391-9525-d5385ab24dfa", "metadata": {}, "outputs": [], "source": ["finall1 = finall1.drop(columns=[\"l0\"])\n", "\n", "# Round only non-NaN numeric values to integers, leave NaNs as is\n", "finall1[finall1.select_dtypes(include=[\"number\"]).columns] = finall1.select_dtypes(\n", "    include=[\"number\"]\n", ").applymap(lambda x: int(round(x)) if pd.notnull(x) else x)\n", "\n", "# finaldsp = finalds\n", "finall1 = finall1.rename(columns={\"total_net_transfer_loss\": \"Total Loss Past 60 Days\"})\n", "finall1 = finall1.rename(columns={\"median_net_transfer_loss_weekly\": \"Median Loss Weekly\"})\n", "finall1 = finall1.rename(columns={\"avg_net_transfer_loss_weekly\": \"Average Loss Weekly\"})\n", "finall1 = finall1.rename(columns={\"percent_positive_losses\": \"% Positive Losses Past Weeks\"})"]}, {"cell_type": "code", "execution_count": null, "id": "78744bb0-3a04-458c-b8bf-bea1829a5585", "metadata": {}, "outputs": [], "source": ["# Format all integer columns with commas for thousands separators\n", "finall1 = finall1.applymap(lambda x: f\"{x:,}\" if isinstance(x, (int, float)) else x)"]}, {"cell_type": "code", "execution_count": null, "id": "02d11ef3-b13e-406d-b40f-71dee1702ff3", "metadata": {}, "outputs": [], "source": ["finall1 = finall1.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "ecf34ecf-842c-4bc4-904b-a6229bf284d7", "metadata": {}, "outputs": [], "source": ["# Example usage\n", "if finall1.shape[0] > 0:\n", "    columns = [str(j) for j in finall1.columns]\n", "\n", "    fig, ax = render_mpl_table(\n", "        finall1,\n", "        header_columns=0,\n", "        columns=columns,\n", "        min_col_width=1.5,  # Define minimum column width\n", "        padding=0.3,  # Define padding for cell values as proportion of col_width\n", "    )\n", "\n", "    # DF_IMAGE_FILENAME = \"top_L1_loss.png\"\n", "    # fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "    # print(DF_IMAGE_FILENAME)\n", "\n", "    DF_IMAGE_FILENAME = \"/tmp/top_L1_loss.png\"\n", "    fig.savefig(DF_IMAGE_FILENAME)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        files=[DF_IMAGE_FILENAME],\n", "        text=\"Top L1\",\n", "    )\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        text=\"No warehouse with losses\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "3183033f-1595-461c-91b2-53900ba07ce8", "metadata": {}, "outputs": [], "source": ["del finall1"]}, {"cell_type": "code", "execution_count": null, "id": "60a9aa8f-1f25-4464-b4e0-03f5e5c55f63", "metadata": {}, "outputs": [], "source": ["import gc\n", "\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "c9001b4e-ec6a-4bec-a13c-065f522c413a", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "95711f66-d06d-40eb-a833-2c86d628234f", "metadata": {}, "source": ["# top_ptype_level_l2"]}, {"cell_type": "code", "execution_count": null, "id": "9ac89639-eb55-4fbd-b966-5f52eff4829d", "metadata": {}, "outputs": [], "source": ["# grouped_ptype_data = ptype[ptype.grn_status == 'qc_closed'].groupby(['l0','l1','l2'])[['net_transfer_loss','billed_amt']].sum().reset_index()\n", "grouped_ptype_level_data_l2 = (\n", "    ptype[(ptype[\"grn_status\"] == \"qc_closed\") | (ptype[\"grn_status\"].isnull())]\n", "    .groupby([\"week\", \"l0\", \"l1\", \"l2\"])[[\"net_transfer_loss\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "grouped_ptype_level_data_l2.head()\n", "\n", "# Pivot table by 'week'\n", "pivot_ptype_level_l2 = grouped_ptype_level_data_l2.pivot(\n", "    index=[\"l0\", \"l1\", \"l2\"], columns=\"week\", values=\"net_transfer_loss\"\n", ")\n", "\n", "# Optional: reset index if you want a cleaner look or easier access to outlet names as columns\n", "pivot_ptype_level_l2 = pivot_ptype_level_l2.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "a00f97c0-2394-4d03-a92a-67296d65755c", "metadata": {}, "outputs": [], "source": ["del grouped_ptype_level_data_l2"]}, {"cell_type": "code", "execution_count": null, "id": "1c354519-4757-46a2-a177-9cd4dfecd49b", "metadata": {}, "outputs": [], "source": ["# Select columns that represent weeks by checking if their names are numeric\n", "week_columns = [col for col in pivot_ptype_level_l2.columns if str(col).isnumeric()]\n", "\n", "# Add a new column 'total_net_transfer_loss' that contains the sum of values across the week columns\n", "pivot_ptype_level_l2[\"total_net_transfer_loss\"] = pivot_ptype_level_l2[week_columns].sum(axis=1)\n", "pivot_ptype_level_l2[\"median_net_transfer_loss_weekly\"] = pivot_ptype_level_l2[week_columns].median(\n", "    axis=1\n", ")\n", "pivot_ptype_level_l2[\"avg_net_transfer_loss_weekly\"] = pivot_ptype_level_l2[week_columns].mean(\n", "    axis=1\n", ")\n", "\n", "# print(pivot_whlevel[['receiver_outlet_name', 'receiver_outlet_id', 'total_net_transfer_loss']])\n", "\n", "# Define the columns representing weekly losses\n", "week_columns = [col for col in pivot_ptype_level_l2.columns if str(col).isnumeric()]\n", "\n", "# Calculate the percentage of positive weekly losses for each outlet\n", "pivot_ptype_level_l2[\"percent_positive_losses\"] = (\n", "    pivot_ptype_level_l2[week_columns].gt(0).sum(axis=1)\n", "    / pivot_ptype_level_l2[week_columns].notna().sum(axis=1)\n", "    * 100\n", ")\n", "\n", "# pivot_ptype_level_l1.sort_values(by = 'total_net_transfer_loss', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "b30b3203-805a-47a3-a657-2d185a15b76a", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level_l2.sort_values(by = 'total_net_transfer_loss', ascending = False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "d6259d3d-2a5f-4d91-9a18-164b2eb05b01", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level_l2.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "54972b19-6916-450e-b4ce-0e4388433cf8", "metadata": {}, "outputs": [], "source": ["# Calculate specific percentiles (85th, 90th, 95th, 99th) for each column\n", "# pivot_ptype_level_l2.quantile([0.85, 0.90, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "id": "4a366823-f30f-4f9c-abfd-2317679f0db0", "metadata": {}, "outputs": [], "source": ["# pivot_ptype_level_l2.sort_values(by = 'total_net_transfer_loss', ascending = False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "83c0dbae-418f-43b4-a219-68bc1a1d39f4", "metadata": {}, "outputs": [], "source": ["t_ptype_l2 = pivot_ptype_level_l2[\n", "    (pivot_ptype_level_l2[\"total_net_transfer_loss\"] > 80000)\n", "    & (pivot_ptype_level_l2[\"percent_positive_losses\"] >= 40)\n", "    & (pivot_ptype_level_l2[\"avg_net_transfer_loss_weekly\"] >= 20000)\n", "]\n", "finall2 = t_ptype_l2.sort_values(by=\"total_net_transfer_loss\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "3fdcea05-f8bb-407d-8c25-2407e6b5c485", "metadata": {}, "outputs": [], "source": ["del pivot_ptype_level_l2\n", "del t_ptype_l2"]}, {"cell_type": "code", "execution_count": null, "id": "7354b8b3-cdfc-4259-a4ea-5f7687277cb9", "metadata": {}, "outputs": [], "source": ["finall2 = finall2.drop(columns=[\"l0\", \"l1\"])\n", "\n", "# Round only non-NaN numeric values to integers, leave NaNs as is\n", "finall2[finall2.select_dtypes(include=[\"number\"]).columns] = finall2.select_dtypes(\n", "    include=[\"number\"]\n", ").applymap(lambda x: int(round(x)) if pd.notnull(x) else x)\n", "\n", "# finaldsp = finalds\n", "finall2 = finall2.rename(columns={\"total_net_transfer_loss\": \"Total Loss Past 60 Days\"})\n", "finall2 = finall2.rename(columns={\"median_net_transfer_loss_weekly\": \"Median Loss Weekly\"})\n", "finall2 = finall2.rename(columns={\"avg_net_transfer_loss_weekly\": \"Average Loss Weekly\"})\n", "finall2 = finall2.rename(columns={\"percent_positive_losses\": \"% Positive Losses Past Weeks\"})"]}, {"cell_type": "code", "execution_count": null, "id": "122e2bab-9ce5-4f66-ad4e-7cc1cd25714e", "metadata": {}, "outputs": [], "source": ["# Format all integer columns with commas for thousands separators\n", "finall2 = finall2.applymap(lambda x: f\"{x:,}\" if isinstance(x, (int, float)) else x)\n", "finall2 = finall2.head(20)\n", "# finall2"]}, {"cell_type": "code", "execution_count": null, "id": "618e2afc-6610-4b9c-9278-1f08792b1a30", "metadata": {}, "outputs": [], "source": ["# Example usage\n", "if finall2.shape[0] > 0:\n", "    columns = [str(j) for j in finall2.columns]\n", "\n", "    fig, ax = render_mpl_table(\n", "        finall2,\n", "        header_columns=0,\n", "        columns=columns,\n", "        min_col_width=1.5,  # Define minimum column width\n", "        padding=0.3,  # Define padding for cell values as proportion of col_width\n", "    )\n", "\n", "    # DF_IMAGE_FILENAME = \"top_L2_loss.png\"\n", "    # fig.savefig(DF_IMAGE_FILENAME, bbox_inches=\"tight\")\n", "    # print(DF_IMAGE_FILENAME)\n", "\n", "    DF_IMAGE_FILENAME = \"/tmp/top_L2_loss.png\"\n", "    fig.savefig(DF_IMAGE_FILENAME)\n", "\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        files=[DF_IMAGE_FILENAME],\n", "        text=\"Top L2\",\n", "    )\n", "else:\n", "    pb.send_slack_message(\n", "        channel=\"bl-rsto-top-loss-alerts\",\n", "        text=\"No warehouse with losses\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "1aebc672-0eac-4b46-9a48-dfbe9026cf9e", "metadata": {}, "outputs": [], "source": ["del finall2"]}, {"cell_type": "code", "execution_count": null, "id": "4d1af225-2bb0-4b97-87c9-6ba0c0742aa5", "metadata": {}, "outputs": [], "source": ["import gc\n", "\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "7af5ed23-ed74-42c5-82b3-4fc79aa2c369", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e063c818-ecff-47b3-a92a-2b95e3769ca3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b1b0b78a-59a6-42ed-8f3e-25f7690e97ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a347a202-da6b-4e93-9c50-45519df51de9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}