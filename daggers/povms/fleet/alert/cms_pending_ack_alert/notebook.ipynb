{"cells": [{"cell_type": "code", "execution_count": null, "id": "22c6c6e3-84bb-480b-b1bb-42570fe7b79d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "5804d027-cfbb-4ce6-b0a2-af8fdb0aae31", "metadata": {}, "outputs": [], "source": ["# pending_app = pd.read_csv('pending_app.csv')\n", "\n", "\n", "sheet_id = \"1HDdW6wY6h_JPVEbyygowmVQUUfXVuPuhxFXY5lSlDkQ\"\n", "\n", "sheet_name = \"Pending_Ack\"\n", "\n", "pending_app = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "f839daa6-6675-48bc-adb2-065f9f5b24a1", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "dd9ae9ea-7a5d-402c-993d-530f6c88f687", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.6,\n", "    font_size=12,\n", "    header_color=\"#0056ca\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    # Replace NaN values with empty strings\n", "    data = data.fillna(\"\")\n", "\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    # Create the table\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "\n", "    # Set font size and remove the default column width\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Loop over cells to format them\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "\n", "    # Adjust column width to fit content\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "57f7d0fe-de81-4647-bdfe-2c9bdefcc73a", "metadata": {}, "outputs": [], "source": ["if pending_app.shape[0] > 0:\n", "    fig, ax = render_mpl_table(pending_app, header_columns=0)\n", "    fig.savefig(\"pending_app.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "f0bbdd05-0646-4cc3-bba9-f2f0cb5c6fed", "metadata": {}, "outputs": [], "source": ["date_two_days_ago = (datetime.now() - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "if pending_app.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if pending_app.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> The following warehouses have trip cost pending acknowledgement in CMS for the date {date_two_days_ago}. \"\n", "                + \"\\n\"\n", "                + f\"<!subteam^S084AM180D6> Please acknowledge the pending trips and update here the rate cards wherever they are absent.\"\n", "                + \"\\n\"\n", "                + f\"<@U082AGFU4R3> <@U08791R8CA0> Please upload the rate card if there are any pending uploads. \"\n", "                + \"\\n\"\n", "                + f\"<@U08BERND7QB> Please acknowledge the BE - BE trips and provide the reasoning. \"\n", "                + \"\\n\"\n", "                + f\"For Raw data, please refer this <https://docs.google.com/spreadsheets/d/1HDdW6wY6h_JPVEbyygowmVQUUfXVuPuhxFXY5lSlDkQ/edit?gid=1553151186#gid=1553151186| sheet>\"\n", "                + \"\\n\"\n", "                + f\"CC: <@U03S602NC82> <@U06TMEZKKMX> <@U08HYN5EZRB> \"\n", "                + \"\\n\"\n", "                + f\"FYI- <@U07M417DP1D> <@U07P1BX4EH3> <@U07JG40NJ3V>\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    # \"raw_data.csv\",\n", "                    \"./pending_app.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "65d7495a-95eb-481a-8218-14b715b552cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}