{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e256a0c9-ed4d-4d5a-bfa2-b7fe9e9ab7a1", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5b2efbe8-0839-433c-9f9b-42339e1c2bb7", "metadata": {}, "outputs": [], "source": ["# halt = pd.read_csv('halt.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b8be5da7-2943-4b07-92d5-b6aeb6886858", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1YMV8YzXi_V__L2HrHvHx3oNnexRnLfNzN6LrEmJJtoo\"\n", "sheet_name = \"alert-group count\"\n", "\n", "halt = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "51452533-bee1-4981-8c5e-cc99dfd7ce29", "metadata": {}, "outputs": [], "source": ["# irs_error.head()"]}, {"cell_type": "code", "execution_count": null, "id": "783dbd1d-53df-42dc-9e46-6158ca2495ad", "metadata": {}, "outputs": [], "source": ["halt.head()"]}, {"cell_type": "code", "execution_count": null, "id": "982cd8db-4249-4104-be52-bc1e1c8424af", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "2b6c0106-3a53-49b4-873b-c51c8d506548", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.6,\n", "    font_size=12,\n", "    header_color_1=\"#0000FF\",  # Color for the first header row\n", "    header_color_2=\"#FF2C2C\",  # Color for the second header row\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    # Create the table\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "\n", "    # Set font size\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    # Iterate through all cells to modify the headers and rows\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "\n", "        # If it's the first row (header row 1), set its color and bold text\n", "        if k[0] == 0:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color_1)\n", "        # If it's the second header row (header row 2), set its color and bold text\n", "        elif k[0] == 1:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color_2)\n", "        else:\n", "            # For the data rows, alternate row colors\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "\n", "    # Adjust column widths\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "b6011b65-60b0-4be5-aacf-99938720ac18", "metadata": {}, "outputs": [], "source": ["if halt.shape[0] > 0:\n", "    fig, ax = render_mpl_table(halt, header_columns=0)\n", "    fig.savefig(\"halt.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "5742965d-8a85-4248-8148-561ed8fe1b3e", "metadata": {}, "outputs": [], "source": ["if halt.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if halt.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6> Have you shared the RCA for yesterday's severe halt (> 40 min)?\"\n", "                + \"\\n\"\n", "                + f\"If not, please share it in this thread : <https://z.slack.com/archives/C03SZNLEWV6/p1740393567489449>\"\n", "                + \"\\n\"\n", "                + f\"Sheet link for halt route and halt metrics : <https://docs.google.com/spreadsheets/d/1YMV8YzXi_V__L2HrHvHx3oNnexRnLfNzN6LrEmJJtoo/edit?gid=1664531257#gid=1664531257| sheet>\"\n", "                + \"\\n\"\n", "                + f\"<@U083263NXFA>\"\n", "                + \"\\n\"\n", "                + f\"FYI - <@U07M417DP1D> <@U07P1BX4EH3> <@U07JG40NJ3V> <@U051HT13WN8>\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    # \"raw_data.csv\",\n", "                    \"./halt.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "1093d5d2-5b4f-4be4-a05c-e620ab621418", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}