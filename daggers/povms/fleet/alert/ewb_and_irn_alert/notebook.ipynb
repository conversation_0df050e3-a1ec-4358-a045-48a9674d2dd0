{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e256a0c9-ed4d-4d5a-bfa2-b7fe9e9ab7a1", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5b2efbe8-0839-433c-9f9b-42339e1c2bb7", "metadata": {}, "outputs": [], "source": ["# finance = pd.read_csv('irn.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "b8be5da7-2943-4b07-92d5-b6aeb6886858", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1i51j_pWWgLq-BlKPuqalC5Vz0x82rSZjzez0Zk8KAO8\"\n", "sheet_name = \"irnraw\"\n", "\n", "finance = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "783dbd1d-53df-42dc-9e46-6158ca2495ad", "metadata": {}, "outputs": [], "source": ["finance.head()"]}, {"cell_type": "code", "execution_count": null, "id": "982cd8db-4249-4104-be52-bc1e1c8424af", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-finance-data-analytics\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "b0b58b56-a595-4966-85cf-5504c1936401", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "from datetime import datetime\n", "\n", "if finance.shape[0] > 0:\n", "    for channel in slack_channel:\n", "        if finance.shape[0] > 0:\n", "            # Get today's date in the desired format (e.g., \"2025-03-12\")\n", "            current_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "            # Add introductory text with a link for the word \"sheet\" and current date\n", "            intro_text = f\"<!channel> Please find below the list of errors during IRN generation from Cleartax/PWC (T-1): {current_date}\\n\"\n", "            # Corrected syntax for Slack hyperlink\n", "            intro_text += f\"For Raw data, please refer to this <https://docs.google.com/spreadsheets/d/1i51j_pWWgLq-BlKPuqalC5Vz0x82rSZjzez0Zk8KAO8/edit?gid=*********#gid=*********|sheet>.\\n\\n\"\n", "\n", "            # Fixed column widths (adjust as needed)\n", "            col_widths = [5, 80, 15, 30]  # Adjusted for serial number and data\n", "\n", "            # Create the table string\n", "            table_string = \"```\\n\"\n", "            header_cols = [\"S.No.\"] + list(finance.columns)  # Add S.No to header\n", "            header = \" | \".join(f\"{col:<{col_widths[i]}}\" for i, col in enumerate(header_cols))\n", "            separator = \"+\".join(\"-\" * width for width in col_widths)\n", "            separator = separator.replace(\"-\", \"=\")\n", "            table_string += header + \"\\n\" + separator + \"\\n\"\n", "\n", "            for index, row in finance.iterrows():\n", "                row_values = [str(index + 1)]  # Add serial number\n", "                for i, val in enumerate(row.values):\n", "                    val_str = str(val)\n", "                    # Normalize spaces\n", "                    val_str = re.sub(r\"\\s+\", \" \", val_str).strip()\n", "\n", "                    if (\n", "                        i == 0 and len(val_str) >= col_widths[1]\n", "                    ):  # Index 1 because S.No. is at index 0\n", "                        val_str = val_str[: col_widths[1] - 3] + \"...\"  # Truncate\n", "                    row_values.append(val_str)\n", "\n", "                row_str = \" | \".join(f\"{val:<{col_widths[i]}}\" for i, val in enumerate(row_values))\n", "                table_string += row_str + \"\\n\"\n", "            table_string += \"```\"\n", "\n", "            # Construct the full message with introductory text and table\n", "            full_text = intro_text + table_string\n", "\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=full_text,\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "5a62e7a6-2413-48d8-b5a7-bd65862d420b", "metadata": {}, "outputs": [], "source": ["# finance2 = pd.read_csv('ewb.csv')\n", "\n", "sheet_id = \"1i51j_pWWgLq-BlKPuqalC5Vz0x82rSZjzez0Zk8KAO8\"\n", "sheet_name = \"ewbraw\"\n", "\n", "finance2 = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "9f5e52bb-bc48-40bb-8a5a-1bea23eae488", "metadata": {}, "outputs": [], "source": ["finance2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "83578739-1649-455a-abf9-0b8d4b6c034b", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-finance-data-analytics\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "c3c65be4-f567-4a64-8a18-6d72e309569b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "from datetime import datetime\n", "\n", "if finance2.shape[0] > 0:\n", "    for channel in slack_channel:\n", "        if finance2.shape[0] > 0:\n", "            # Get today's date in the desired format (e.g., \"2025-03-12\")\n", "            current_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "            # Add introductory text with a link for the word \"sheet\" and current date\n", "            intro_text = f\"<!channel> Please find below the list of errors during EWB generation from Cleartax/PWC (T-1): {current_date}\\n\"\n", "            # Corrected syntax for Slack hyperlink\n", "            intro_text += f\"For Raw data, please refer to this <https://docs.google.com/spreadsheets/d/1i51j_pWWgLq-BlKPuqalC5Vz0x82rSZjzez0Zk8KAO8/edit?gid=*********#gid=*********|sheet>.\\n\\n\"\n", "\n", "            # Fixed column widths (adjust as needed)\n", "            col_widths = [5, 80, 15, 30]  # Adjusted for serial number and data\n", "\n", "            # Create the table string\n", "            table_string = \"```\\n\"\n", "            header_cols = [\"S.No.\"] + list(finance2.columns)  # Add S.No to header\n", "            header = \" | \".join(f\"{col:<{col_widths[i]}}\" for i, col in enumerate(header_cols))\n", "            separator = \"+\".join(\"-\" * width for width in col_widths)\n", "            separator = separator.replace(\"-\", \"=\")\n", "            table_string += header + \"\\n\" + separator + \"\\n\"\n", "\n", "            for index, row in finance2.iterrows():\n", "                row_values = [str(index + 1)]  # Add serial number\n", "                for i, val in enumerate(row.values):\n", "                    val_str = str(val)\n", "                    # Normalize spaces\n", "                    val_str = re.sub(r\"\\s+\", \" \", val_str).strip()\n", "\n", "                    if (\n", "                        i == 0 and len(val_str) >= col_widths[1]\n", "                    ):  # Index 1 because S.No. is at index 0\n", "                        val_str = val_str[: col_widths[1] - 3] + \"...\"  # Truncate\n", "                    row_values.append(val_str)\n", "\n", "                row_str = \" | \".join(f\"{val:<{col_widths[i]}}\" for i, val in enumerate(row_values))\n", "                table_string += row_str + \"\\n\"\n", "            table_string += \"```\"\n", "\n", "            # Construct the full message with introductory text and table\n", "            full_text = intro_text + table_string\n", "\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=full_text,\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "0e565481-1851-41a6-b605-1db60f8219a0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}