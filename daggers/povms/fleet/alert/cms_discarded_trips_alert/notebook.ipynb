{"cells": [{"cell_type": "code", "execution_count": null, "id": "767c3bf7-86a3-4b05-a60a-d1dc06759ed3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "73853bce-59f8-43b6-a843-771a8ecde4d0", "metadata": {}, "outputs": [], "source": ["# discarded = pd.read_csv('discarded.csv')\n", "\n", "\n", "sheet_id = \"1HDdW6wY6h_JPVEbyygowmVQUUfXVuPuhxFXY5lSlDkQ\"\n", "\n", "sheet_name = \"Discarded_Trips\"\n", "\n", "discarded = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "4fd59436-4d0b-45d8-a26d-79504cb2a61d", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-fleet-core-team\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "c2eaeb4a-2647-45fe-a898-24f4db3d5a93", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.6,\n", "    font_size=12,\n", "    header_color=\"#CC5500\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "80227a92-fb7e-44ec-81a7-cfc0eecdd073", "metadata": {}, "outputs": [], "source": ["if discarded.shape[0] > 0:\n", "    fig, ax = render_mpl_table(discarded, header_columns=0)\n", "    fig.savefig(\"discarded.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "e2cc90b8-ad1a-4a7e-905b-cd42071b98ca", "metadata": {}, "outputs": [], "source": ["date_one_days_ago = (datetime.now() - timed<PERSON>ta(days=1)).strftime(\"%Y-%m-%d\")\n", "if discarded.shape[0] > 0:\n", "    for i in range(len(slack_channel)):\n", "        channel = slack_channel[i]\n", "        if discarded.shape[0] > 0:\n", "            text_req = (\n", "                f\"<!subteam^S0837CFRP1V> <!subteam^S084AM180D6> The following warehouses have discarded retail trips for the date {date_one_days_ago}.  \"\n", "                + \"\\n\"\n", "                + f\"For Raw data, please refer this <https://docs.google.com/spreadsheets/d/1HDdW6wY6h_JPVEbyygowmVQUUfXVuPuhxFXY5lSlDkQ/edit?gid=1117648129#gid=1117648129| sheet>\"\n", "                + \"\\n\"\n", "                + f\"<@U08BERND7QB> please share the RCA for these discarded BE - BE trips. \"\n", "                + \"\\n\"\n", "                + f\"CC: <@U082AGFU4R3> \"\n", "                + \"\\n\"\n", "                + f\"FYI- <@U07M417DP1D> <@U07P1BX4EH3> <@U07JG40NJ3V>\"\n", "                # + \"\\n\"\n", "            )\n", "            pb.send_slack_message(\n", "                channel=channel,\n", "                text=text_req,\n", "                files=[\n", "                    # \"raw_data.csv\",\n", "                    \"./discarded.png\",\n", "                ],\n", "            )\n", "        else:\n", "            print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "1b6da602-f947-4fbc-a20c-29e20a4a496f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}