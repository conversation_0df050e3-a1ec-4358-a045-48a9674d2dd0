{"cells": [{"cell_type": "code", "execution_count": null, "id": "6af6aa04-a828-43c6-b89a-7fc6ceea0f79", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "432383a3-a316-4db8-97af-61a622bd1f53", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "36f1fe21-d012-41a3-9a82-29f636bad2e8", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "1956460a-db1c-4377-83a0-332a2c5e715c", "metadata": {}, "outputs": [], "source": ["frozen = pb.from_sheets(sheetid=\"1N78TGqX6VMZm-V9uvlGZ-oBnuLWiUP1Xv_zUILPICog\", sheetname=\"Sheet2\")"]}, {"cell_type": "code", "execution_count": null, "id": "eefdddbb-1d10-4fa6-b632-ea37fedfc517", "metadata": {}, "outputs": [], "source": ["# frozen = pd.read_csv('frozen.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "2e92f9a2-ef0a-415e-b458-8202f730d6cd", "metadata": {}, "outputs": [], "source": ["frozen.head()"]}, {"cell_type": "code", "execution_count": null, "id": "149ea7f0-4860-4f7e-8fba-294298dec228", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-frozen-chiller-fleet-team\"]\n", "\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "703506de-b49e-4f8a-9a30-b9503a2585bd", "metadata": {}, "outputs": [], "source": ["frozen = frozen.fillna(\"\")\n", "frozen.columns = [\"\" if col.startswith(\"Unnamed\") else col for col in frozen.columns]\n", "frozen.head()"]}, {"cell_type": "code", "execution_count": null, "id": "17e49e46-7e43-42d7-87e9-7c8a0f6df86a", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.5,\n", "    row_height=0.6,\n", "    font_size=12,\n", "    header_color=\"#06402B\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_rows=1,\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for (row, col), cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        cell.get_text().set_horizontalalignment(\"center\")\n", "        cell.get_text().set_verticalalignment(\"center\")\n", "\n", "        if row < header_rows or col < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[row % len(row_colors)])\n", "\n", "        if col == 4:  # 4th column\n", "            cell.set_text_props(color=\"red\")\n", "\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "29e9885f-e372-48d7-a57b-bc5dbf2bb700", "metadata": {}, "outputs": [], "source": ["if frozen.shape[0] > 0:\n", "    fig, ax = render_mpl_table(frozen, header_columns=0)\n", "    fig.savefig(\"frozen.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "a34cdff7-b030-4eea-b3bf-44faeb55b523", "metadata": {}, "outputs": [], "source": ["for i in range(len(slack_channel)):\n", "    channel = slack_channel[i]\n", "    if frozen.shape[0] > 0:\n", "        text_req = (\n", "            f\"<!channel> *Temperature Alert* \"\n", "            + (datetime.now() - <PERSON><PERSON><PERSON>(days=1)).strftime(\"%Y-%m-%d\")\n", "            + \"\\n\"\n", "            + \"Please find the count of trips where an increase in temperature was detected.\\n\"\n", "            + f\"For Raw data, please refer this <https://docs.google.com/spreadsheets/d/1g2-bYyK419L8_BKbOo4II8J4lbwwbIOpDjmRD0JXy2c/edit?gid=1332077539#gid=1332077539| sheet>\"\n", "            + \"\\n\"\n", "            + f\"CC:- <@U08M89TTNF2>\"\n", "        )\n", "        pb.send_slack_message(\n", "            channel=channel,\n", "            text=text_req,\n", "            files=[\n", "                # \"raw_data.csv\",\n", "                \"./frozen.png\",\n", "            ],\n", "        )\n", "    else:\n", "        print(\"code issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "a4ddcb26-7071-4282-8ee8-4f871a2290a5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}