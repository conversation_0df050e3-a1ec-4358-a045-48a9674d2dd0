{"cells": [{"cell_type": "code", "execution_count": null, "id": "b07edef0-7b23-4f2b-97d7-d158d595e96d", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "c6d66839-d055-486e-8d20-b217d9fe011a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "import json\n", "import requests\n", "\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "b16befd0-bb32-437c-a996-68b2b1c2070e", "metadata": {}, "outputs": [], "source": ["## New table\n", "# trino_schema_name = \"supply_etls\"  # \"supply_etls\"  # \"povms\"\n", "# trino_table_name = \"fleet_trips\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4e795fe6-de07-4056-946c-2c2a3846dcf3", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "8fe10121-f844-4db6-afa1-91cd9c0274ff", "metadata": {}, "outputs": [], "source": ["trip_level_query = \"\"\"\n", "SELECT\n", "    Distinct\n", "    s.trip_id,\n", "    s.trip_date,\n", "    s.wh_outlet_id,\n", "    s.wh_outlet_name,\n", "    s.vendor_name,\n", "    s.driver_name,\n", "    s.driver_mobile,\n", "    s.driver_id,\n", "    s.truck_number,\n", "    s.dn_amt,\n", "    s.billed_amt,\n", "    CASE \n", "        WHEN s.dn_amt > 15000 THEN 4\n", "        WHEN s.dn_amt > 8000 THEN 3\n", "        WHEN s.dn_amt > 5000 THEN 2\n", "        WHEN s.dn_amt > 2000 THEN 1\n", "        ELSE 0\n", "    END AS dn_category,\n", "    (\n", "        (CASE WHEN s.frwd_lock_status != 'MATCHING' THEN 1 ELSE 0 END) \n", "      + (CASE WHEN s.rev_lock_status != 'MATCHING' THEN 1 ELSE 0 END)\n", "    ) AS lock_mismatch,\n", "    CASE\n", "        WHEN s.halts_sum < 15 THEN 1\n", "        WHEN s.halts_sum < 40 THEN 2\n", "        WHEN s.halts_sum < 60 THEN 3\n", "        ELSE 4\n", "    END AS halts_category,\n", "    \n", "    (\n", "        ((CASE \n", "            WHEN s.dn_amt > 15000 THEN 4\n", "            WHEN s.dn_amt > 8000 THEN 3\n", "            WHEN s.dn_amt > 5000 THEN 2\n", "            WHEN s.dn_amt > 2000 THEN 1\n", "            ELSE 0\n", "        END) * 16)\n", "        + (CASE\n", "            WHEN s.halts_sum < 15 THEN 1\n", "            WHEN s.halts_sum < 40 THEN 2\n", "            WHEN s.halts_sum < 60 THEN 3\n", "            ELSE 4\n", "           END) * 3\n", "           \n", "        + (\n", "            (\n", "              (CASE WHEN s.frwd_lock_status != 'MATCHING' THEN 1 ELSE 0 END) \n", "              + (CASE WHEN s.rev_lock_status != 'MATCHING' THEN 1 ELSE 0 END)\n", "            ) * 8\n", "        )\n", "        \n", "        + ((CASE WHEN s.ds_reporting_status IN ('Not_reached_DS', 'Delay') THEN 1 ELSE 0 END) * 7)\n", "    ) AS score\n", "    \n", "    \n", "FROM supply_etls.fleet_middle_mile_suspects AS s\n", "LEFT JOIN supply_etls.fleet_master_data f \n", "    ON f.trip_id = s.trip_id\n", "WHERE s.trip_date >= current_date - INTERVAL '7' DAY\n", "and f.trip_date >= current_date - INTERVAL '7' DAY\n", "\n", "\"\"\"\n", "\n", "trip_level_df = read_sql_query(trip_level_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "d4b74191-6223-496f-a896-ce4aaf1df6b8", "metadata": {}, "outputs": [], "source": ["trip_level_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dd15a9a6-0e54-487b-9b13-acf136b84d10", "metadata": {}, "outputs": [], "source": ["trip_level_driver_score = trip_level_df[\n", "    [\n", "        \"trip_date\",\n", "        \"trip_id\",\n", "        \"wh_outlet_id\",\n", "        \"wh_outlet_name\",\n", "        \"vendor_name\",\n", "        \"driver_id\",\n", "        \"driver_name\",\n", "        \"driver_mobile\",\n", "        \"truck_number\",\n", "        \"score\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "c315be10-1db7-4f5f-8306-80d8a60d756f", "metadata": {}, "outputs": [], "source": ["# trip_level_driver_score[trip_level_driver_score['driver_mobile']=='9664474912']"]}, {"cell_type": "code", "execution_count": null, "id": "cc2a2024-7617-46cb-9e15-ab1b2e00acc7", "metadata": {}, "outputs": [], "source": ["trip_level_driver_score.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dac66df4-1c0f-4e0f-b68c-72b43a297bc3", "metadata": {}, "outputs": [], "source": ["trip_level_driver_score_agg = (\n", "    trip_level_driver_score.groupby([\"driver_mobile\"])\n", "    .agg(max_score=(\"score\", \"mean\"))  # Name the aggregated column as 'max_score'\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c1253239-09ab-40a2-888e-8caa3721867e", "metadata": {}, "outputs": [], "source": ["trip_level_driver_score_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2106522b-a034-4a8d-abcc-f44f2978d52e", "metadata": {}, "outputs": [], "source": ["trip_level_driver_score_agg.shape"]}, {"cell_type": "code", "execution_count": null, "id": "10da77f8-edd0-4737-ba15-3245a0bd9ab7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7b1623a7-119f-4bd2-a1ff-4f2af3d331a2", "metadata": {}, "outputs": [], "source": ["wow_query = \"\"\"\n", "with base as(\n", "select * \n", "from supply_etls.fleet_middle_mile_suspects \n", "where trip_date >= cast(current_date - interval '60' day as date)\n", "),\n", "wow as(\n", "select week,\n", "        max_week,\n", "        wh_outlet_id,\n", "        wh_outlet_name,\n", "        vendor_id,\n", "        vendor_name,\n", "        truck_number,\n", "        truck_registration_state,\n", "        vehicle_age_in_month,\n", "        driver_id,\n", "        driver_name,\n", "        age_of_employee,\n", "        driver_mobile,\n", "        count(distinct trip_id) as trip_count,\n", "        count(distinct case when dispatch_slot ='Day' then trip_id end) as count_day_trips,\n", "        count(distinct case when dispatch_slot ='Night' then trip_id end) as count_night_trips,\n", "        count(distinct case when ds_reporting_status='Delay' then trip_id end) as delay_trips,\n", "        sum(distinct case when (frwd_lock_status!='MATCHING' AND rev_lock_status!='MATCHING') then 1 \n", "                          when (frwd_lock_status!='MATCHING' OR rev_lock_status!='MATCHING') then 0.5\n", "                          else 0 end) as lock_mismatch_count,\n", "        count(distinct case when dn_amt>5000 then trip_id end) as trip_dn_more_than_5000,\n", "        sum(billed_amt) as billed_amt,\n", "        SUM(dn_qty) AS dn_qty,\n", "        SUM(dn_amt) AS dn_amt,\n", "        sum(expected_distance_frwd) as expected_distance_frwd,\n", "        sum(travelled_distance_kms) travelled_distance_kms,\n", "        sum(halts_sum) as halts_sum,\n", "        6 - (max_week - week_num) AS rnk\n", "        \n", "from base\n", "group by \n", "        week,\n", "        max_week,\n", "        wh_outlet_id,\n", "        wh_outlet_name,\n", "        vendor_id,\n", "        vendor_name,\n", "        truck_number,\n", "        truck_registration_state,\n", "        vehicle_age_in_month,\n", "        driver_id,\n", "        driver_name,\n", "        age_of_employee,\n", "        driver_mobile,\n", "        6 - (max_week - week_num)\n", "),\n", "\n", "wow_agg_data as(\n", "  SELECT\n", "        wh_outlet_id,\n", "        wh_outlet_name,\n", "        vendor_id,\n", "        vendor_name,\n", "        truck_number,\n", "        truck_registration_state,\n", "        vehicle_age_in_month,\n", "        driver_id,\n", "        driver_name,\n", "        age_of_employee,\n", "        driver_mobile,\n", "        sum(trip_count) as trip_count,\n", "        sum(trip_dn_more_than_5000) as trip_dn_more_than_5000,\n", "        sum(count_day_trips) as count_day_trips,\n", "        sum(count_night_trips) as count_night_trips,\n", "        sum(delay_trips) as delay_trips,\n", "        sum(halts_sum) as halts_sum,\n", "        -- sum(wh_to_ds_distance) as wh_to_ds_distance_all_week,\n", "        sum(travelled_distance_kms) as travelled_distance_kms_all_week_frwd,\n", "        sum(expected_distance_frwd) as expected_distance_frwd,\n", "        sum(lock_mismatch_count) as lock_mismatch_count,\n", "        SUM(CASE WHEN rnk = 1 THEN trip_count END) AS total_trip_1st_week,\n", "        SUM(CASE WHEN rnk = 2 THEN trip_count END) AS total_trip_2nd_week,\n", "        SUM(CASE WHEN rnk = 3 THEN trip_count END) AS total_trip_3rd_week,\n", "        SUM(CASE WHEN rnk = 4 THEN trip_count END) AS total_trip_4th_week,\n", "        SUM(CASE WHEN rnk = 5 THEN trip_count END) AS total_trip_5th_week,\n", "        SUM(CASE WHEN rnk = 6 THEN trip_count END) AS total_trip_6th_week,\n", "        \n", "        SUM(CASE WHEN rnk = 1 THEN billed_amt END) AS billed_amt_1st_week,\n", "        SUM(CASE WHEN rnk = 2 THEN billed_amt END) AS billed_amt_2nd_week,\n", "        SUM(CASE WHEN rnk = 3 THEN billed_amt END) AS billed_amt_3rd_week,\n", "        SUM(CASE WHEN rnk = 4 THEN billed_amt END) AS billed_amt_4th_week,\n", "        SUM(CASE WHEN rnk = 5 THEN billed_amt END) AS billed_amt_5th_week,\n", "        SUM(CASE WHEN rnk = 6 THEN billed_amt END) AS billed_amt_6th_week,\n", "        \n", "        \n", "        SUM(CASE WHEN rnk = 1 THEN dn_amt END) AS dn_amt_1st_week,\n", "        SUM(CASE WHEN rnk = 2 THEN dn_amt END) AS dn_amt_2nd_week,\n", "        SUM(CASE WHEN rnk = 3 THEN dn_amt END) AS dn_amt_3rd_week,\n", "        SUM(CASE WHEN rnk = 4 THEN dn_amt END) AS dn_amt_4th_week,\n", "        SUM(CASE WHEN rnk = 5 THEN dn_amt END) AS dn_amt_5th_week,\n", "        SUM(CASE WHEN rnk = 6 THEN dn_amt END) AS dn_amt_6th_week,\n", "        \n", "        -- SUM(CASE WHEN rnk = 1 THEN wh_to_ds_distance END) AS wh_to_ds_distance_1st_week,\n", "        -- SUM(CASE WHEN rnk = 2 THEN wh_to_ds_distance END) AS wh_to_ds_distance_2nd_week,\n", "        -- SUM(CASE WHEN rnk = 3 THEN wh_to_ds_distance END) AS wh_to_ds_distance_3rd_week,\n", "        -- SUM(CASE WHEN rnk = 4 THEN wh_to_ds_distance END) AS wh_to_ds_distance_4th_week,\n", "        -- SUM(CASE WHEN rnk = 5 THEN wh_to_ds_distance END) AS wh_to_ds_distance_5th_week,\n", "        -- SUM(CASE WHEN rnk = 6 THEN wh_to_ds_distance END) AS wh_to_ds_distance_6th_week,\n", "        \n", "        SUM(CASE WHEN rnk = 1 THEN expected_distance_frwd END) AS expected_distance_frwd_1st_week,\n", "        SUM(CASE WHEN rnk = 2 THEN expected_distance_frwd END) AS expected_distance_frwd_2nd_week,\n", "        SUM(CASE WHEN rnk = 3 THEN expected_distance_frwd END) AS expected_distance_frwd_3rd_week,\n", "        SUM(CASE WHEN rnk = 4 THEN expected_distance_frwd END) AS expected_distance_frwd_4th_week,\n", "        SUM(CASE WHEN rnk = 5 THEN expected_distance_frwd END) AS expected_distance_frwd_5th_week,\n", "        SUM(CASE WHEN rnk = 6 THEN expected_distance_frwd END) AS expected_distance_frwd_6th_week,\n", "\n", "        SUM(CASE WHEN rnk = 1 THEN travelled_distance_kms END) AS travelled_distance_kms_1st_week,\n", "        SUM(CASE WHEN rnk = 2 THEN travelled_distance_kms END) AS travelled_distance_kms_2nd_week,\n", "        SUM(CASE WHEN rnk = 3 THEN travelled_distance_kms END) AS travelled_distance_kms_3rd_week,\n", "        SUM(CASE WHEN rnk = 4 THEN travelled_distance_kms END) AS travelled_distance_kms_4th_week,\n", "        SUM(CASE WHEN rnk = 5 THEN travelled_distance_kms END) AS travelled_distance_kms_5th_week,\n", "        SUM(CASE WHEN rnk = 6 THEN travelled_distance_kms END) AS travelled_distance_kms_6th_week,\n", "        \n", "        SUM(dn_amt) / NULLIF(SUM(trip_count), 0) AS avg_dn_amt\n", "        \n", "        \n", "    FROM wow\n", "    GROUP BY\n", "        wh_outlet_id,\n", "        wh_outlet_name,\n", "        vendor_id,\n", "        vendor_name,\n", "        truck_number,\n", "        truck_registration_state,\n", "        vehicle_age_in_month,\n", "        driver_id,\n", "        driver_name,\n", "        age_of_employee,\n", "        driver_mobile\n", "),\n", "\n", "final_with_totals AS (\n", "    SELECT\n", "        fd.*,\n", "        -- Calculate total_dn_amt over six weeks\n", "        COALESCE(fd.dn_amt_1st_week, 0) + COALESCE(fd.dn_amt_2nd_week, 0) + COALESCE(fd.dn_amt_3rd_week, 0) +\n", "        COALESCE(fd.dn_amt_4th_week, 0) + COALESCE(fd.dn_amt_5th_week, 0) + COALESCE(fd.dn_amt_6th_week, 0) AS total_dn_amt,\n", "        \n", "        COALESCE(fd.billed_amt_1st_week, 0) + COALESCE(fd.billed_amt_2nd_week, 0) + COALESCE(fd.billed_amt_3rd_week, 0) +\n", "        COALESCE(fd.billed_amt_4th_week, 0) + COALESCE(fd.billed_amt_5th_week, 0) + COALESCE(fd.billed_amt_6th_week, 0) AS total_billed_amt,\n", "        \n", "        COALESCE(fd.dn_amt_4th_week, 0) + COALESCE(fd.dn_amt_5th_week, 0) as dn_amount_last2week,\n", "        COALESCE(fd.billed_amt_4th_week, 0) + COALESCE(fd.billed_amt_5th_week, 0) as billed_amount_last2week,\n", "\n", "        COALESCE(fd.total_trip_1st_week, 0) + COALESCE(fd.total_trip_2nd_week, 0) + COALESCE(fd.total_trip_3rd_week, 0) +\n", "        COALESCE(fd.total_trip_4th_week, 0) + COALESCE(fd.total_trip_5th_week, 0) + COALESCE(fd.total_trip_6th_week, 0) AS total_trips,\n", "        \n", "        COALESCE(fd.expected_distance_frwd_1st_week, 0) + COALESCE(fd.expected_distance_frwd_2nd_week, 0) + COALESCE(fd.expected_distance_frwd_3rd_week, 0) +\n", "        COALESCE(fd.expected_distance_frwd_4th_week, 0) + COALESCE(fd.expected_distance_frwd_5th_week, 0) + COALESCE(fd.expected_distance_frwd_6th_week, 0) AS total_expected_distance_frwd,\n", "        COALESCE(fd.expected_distance_frwd_4th_week, 0) + COALESCE(fd.expected_distance_frwd_5th_week, 0) as  total_expected_distance_frwd_last2week,\n", "        \n", "        COALESCE(fd.travelled_distance_kms_1st_week, 0) + COALESCE(fd.travelled_distance_kms_2nd_week, 0) + COALESCE(fd.travelled_distance_kms_3rd_week, 0) +\n", "        COALESCE(fd.travelled_distance_kms_4th_week, 0) + COALESCE(fd.travelled_distance_kms_5th_week, 0) + COALESCE(fd.travelled_distance_kms_6th_week, 0) AS total_travelled_distance_kms_frwd,\n", "        COALESCE(fd.travelled_distance_kms_4th_week, 0) + COALESCE(fd.travelled_distance_kms_5th_week, 0) as total_travelled_distance_kms_frwd_last2week\n", "        \n", "        \n", "    FROM wow_agg_data fd\n", "    \n", "),\n", "current_drivers AS (\n", "    SELECT\n", "        fwt.*\n", "    FROM final_with_totals fwt\n", "),\n", "suspects_data as(\n", "SELECT\n", "    wh_outlet_id,\n", "    wh_outlet_name,\n", "    vendor_id,\n", "    vendor_name,\n", "    fr.truck_number,\n", "    truck_registration_state,\n", "    vehicle_age_in_month,\n", "    driver_id,\n", "    driver_name,\n", "    age_of_employee,\n", "    driver_mobile,\n", "    trip_dn_more_than_5000,\n", "    total_trips,\n", "    count_day_trips,\n", "    count_night_trips,\n", "    delay_trips,\n", "    lock_mismatch_count,\n", "    billed_amt_1st_week,\n", "    billed_amt_2nd_week,\n", "    billed_amt_3rd_week,\n", "    billed_amt_4th_week,\n", "    billed_amt_5th_week,\n", "    billed_amt_6th_week,\n", "    total_billed_amt,\n", "    billed_amount_last2week,\n", "    \n", "    dn_amt_1st_week,\n", "    dn_amt_2nd_week,\n", "    dn_amt_3rd_week,\n", "    dn_amt_4th_week,\n", "    dn_amt_5th_week,\n", "    dn_amt_6th_week,\n", "    total_dn_amt,\n", "    dn_amount_last2week,\n", "    \n", "    -- wh_to_ds_distance_1st_week,\n", "    -- wh_to_ds_distance_2nd_week,\n", "    -- wh_to_ds_distance_3rd_week,\n", "    -- wh_to_ds_distance_4th_week,\n", "    -- wh_to_ds_distance_5th_week,\n", "    -- wh_to_ds_distance_6th_week,\n", "    \n", "    expected_distance_frwd_1st_week,\n", "    expected_distance_frwd_2nd_week,\n", "    expected_distance_frwd_3rd_week,\n", "    expected_distance_frwd_4th_week,\n", "    expected_distance_frwd_5th_week,\n", "    expected_distance_frwd_6th_week,\n", "    total_expected_distance_frwd,\n", "    total_expected_distance_frwd_last2week,\n", "\n", "    \n", "    \n", "    travelled_distance_kms_1st_week,\n", "    travelled_distance_kms_2nd_week,\n", "    travelled_distance_kms_3rd_week,\n", "    travelled_distance_kms_4th_week,\n", "    travelled_distance_kms_5th_week,\n", "    travelled_distance_kms_6th_week,\n", "    total_travelled_distance_kms_frwd,\n", "    total_travelled_distance_kms_frwd_last2week,\n", "    travelled_distance_kms_all_week_frwd as total_travelled_distance_frwd,\n", "    halts_sum,\n", "    halts_sum/total_trips as halts_per_trip,\n", "    (fr.trip_dn_more_than_5000 * 100.0) / NULLIF(fr.total_trips, 0) AS trip_dn_percent,\n", "    CASE\n", "        WHEN (fr.trip_dn_more_than_5000 * 100.0) / NULLIF(fr.total_trips, 0) > 50 THEN 5\n", "        WHEN (fr.trip_dn_more_than_5000 * 100.0) / NULLIF(fr.total_trips, 0) > 40 THEN 5\n", "        WHEN (fr.trip_dn_more_than_5000 * 100.0) / NULLIF(fr.total_trips, 0) > 30 THEN 4\n", "        WHEN (fr.trip_dn_more_than_5000 * 100.0) / NULLIF(fr.total_trips, 0) > 20 THEN 2\n", "        WHEN (fr.trip_dn_more_than_5000 * 100.0) / NULLIF(fr.total_trips, 0) > 5 THEN 1\n", "        ELSE 0\n", "    END AS trip_dn_category,\n", "  -- (trip_dn_category * 4.5) AS trip_dn_score,\n", "    (fr.count_day_trips * 1.0 / NULLIF(fr.total_trips, 0)) * 3 AS day_trips_score,\n", "    (fr.count_night_trips * 1.0 / NULLIF(fr.total_trips, 0)) * 10 AS night_trips_score,\n", "    (fr.total_dn_amt * 100.0) / NULLIF(fr.total_billed_amt, 0) AS dn_billed_percent,\n", "    CASE\n", "        WHEN (fr.total_dn_amt * 100.0) / NULLIF(fr.total_billed_amt, 0) > 4 THEN 4\n", "        WHEN (fr.total_dn_amt * 100.0) / NULLIF(fr.total_billed_amt, 0) > 2.5 THEN 3\n", "        WHEN (fr.total_dn_amt * 100.0) / NULLIF(fr.total_billed_amt, 0) > 1 THEN 2\n", "        WHEN (fr.total_dn_amt * 100.0) / NULLIF(fr.total_billed_amt, 0) > 0.5 THEN 1\n", "        ELSE 0\n", "    END AS dn_billed_category,\n", "    --(dn_billed_category * 4.5) AS dn_billed_score,\n", "    CASE\n", "        WHEN fr.dn_amount_last2week > 60000 THEN 5\n", "        WHEN fr.dn_amount_last2week > 52000 THEN 4\n", "        WHEN fr.dn_amount_last2week > 45000 THEN 3\n", "        WHEN fr.dn_amount_last2week > 30000 THEN 2\n", "        WHEN fr.dn_amount_last2week > 19000 THEN 1\n", "        ELSE 0\n", "    END AS dn_amount_last2week_category,\n", "    CASE\n", "        WHEN fr.dn_amt_5th_week > 30000 THEN 5\n", "        WHEN fr.dn_amt_5th_week > 24000 THEN 4\n", "        WHEN fr.dn_amt_5th_week > 20000 THEN 3\n", "        WHEN fr.dn_amt_5th_week > 15000 THEN 2\n", "        WHEN fr.dn_amt_5th_week > 10000 THEN 1\n", "    ELSE 0\n", "    END AS dn_amount_last1week_category,\n", "   -- (dn_amount_last2week_category * 8) AS dn_amount_last2week_score,\n", "    (fr.total_travelled_distance_kms_frwd ) / NULLIF(fr.total_expected_distance_frwd, 0) AS frwd_diversion_percent,\n", "    CASE\n", "        WHEN (fr.total_travelled_distance_kms_frwd ) / NULLIF(fr.total_expected_distance_frwd, 0) <= 0 then 0\n", "        WHEN (fr.total_travelled_distance_kms_frwd ) / NULLIF(fr.total_expected_distance_frwd, 0) < 5 then 1\n", "        WHEN (fr.total_travelled_distance_kms_frwd ) / NULLIF(fr.total_expected_distance_frwd, 0) < 10 then 2\n", "        WHEN (fr.total_travelled_distance_kms_frwd ) / NULLIF(fr.total_expected_distance_frwd, 0) < 15 then 3\n", "        WHEN (fr.total_travelled_distance_kms_frwd ) / NULLIF(fr.total_expected_distance_frwd, 0) < 25 then 4\n", "    ELSE 5 end as frwd_diversion_category,\n", "    \n", "    CASE\n", "        WHEN (fr.total_travelled_distance_kms_frwd_last2week ) / NULLIF(fr.total_expected_distance_frwd_last2week, 0) <= 0 then 0\n", "        WHEN (fr.total_travelled_distance_kms_frwd_last2week ) / NULLIF(fr.total_expected_distance_frwd_last2week, 0) < 5 then 1\n", "        WHEN (fr.total_travelled_distance_kms_frwd_last2week ) / NULLIF(fr.total_expected_distance_frwd_last2week, 0) < 10 then 2\n", "        WHEN (fr.total_travelled_distance_kms_frwd_last2week ) / NULLIF(fr.total_expected_distance_frwd_last2week, 0) < 15 then 3\n", "        WHEN (fr.total_travelled_distance_kms_frwd_last2week ) / NULLIF(fr.total_expected_distance_frwd_last2week, 0) < 25 then 4\n", "    ELSE 5 end as diversion_category_last2week_frwd,\n", "        \n", "    -- ((fr.total_travelled_distance_kms_frwd ) / NULLIF(fr.total_expected_distance_frwd, 0)) * 3 AS frwd_diversion_category,\n", "\n", "    (fr.total_travelled_distance_kms_frwd_last2week ) / NULLIF(fr.total_expected_distance_frwd_last2week, 0) AS frwd_last2w_diversion_percent,\n", "    -- ((fr.total_travelled_distance_kms_frwd_last2week) / NULLIF(fr.total_expected_distance_frwd_last2week, 0)) * 2 AS frwd_last2w_diversion_score,\n", "    CASE\n", "        WHEN (fr.delay_trips * 100.0) / NULLIF(fr.total_trips, 0) > 80 THEN 5\n", "        WHEN (fr.delay_trips * 100.0) / NULLIF(fr.total_trips, 0) > 60 THEN 4\n", "        WHEN (fr.delay_trips * 100.0) / NULLIF(fr.total_trips, 0) > 40 THEN 3\n", "        WHEN (fr.delay_trips * 100.0) / NULLIF(fr.total_trips, 0) > 20 THEN 2\n", "        WHEN (fr.delay_trips * 100.0) / NULLIF(fr.total_trips, 0) > 5 THEN 1\n", "    ELSE 0 END AS ds_delay_category,\n", "    \n", "    CASE\n", "        WHEN (fr.lock_mismatch_count * 100.0) / NULLIF(fr.total_trips, 0) < 5 THEN 1\n", "        WHEN (fr.lock_mismatch_count * 100.0) / NULLIF(fr.total_trips, 0) < 12  THEN 2\n", "        ELSE 3\n", "    END AS lock_mismatch_category,\n", "    \n", "    case when halts_sum/total_trips > 30 then 1 else 0 end as halt_per_trip_flag\n", "FROM current_drivers fr\n", "),\n", "scores as(\n", "\n", "select \n", "    wh_outlet_id,\n", "    wh_outlet_name,\n", "    vendor_id,\n", "    vendor_name,\n", "    truck_number,                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           \n", "    truck_registration_state,\n", "    vehicle_age_in_month,\n", "    driver_id,\n", "    driver_name as employee_name,\n", "    age_of_employee,\n", "    driver_mobile,\n", "    trip_dn_more_than_5000,\n", "    total_trips,\n", "    count_day_trips,\n", "    count_night_trips,\n", "    lock_mismatch_count,\n", "    delay_trips,\n", "    billed_amt_1st_week,\n", "    billed_amt_2nd_week,\n", "    billed_amt_3rd_week,\n", "    billed_amt_4th_week,\n", "    billed_amt_5th_week,\n", "    billed_amt_6th_week,\n", "    total_billed_amt,\n", "    billed_amount_last2week,\n", "    \n", "    dn_amt_1st_week,\n", "    dn_amt_2nd_week,\n", "    dn_amt_3rd_week,\n", "    dn_amt_4th_week,\n", "    dn_amt_5th_week,\n", "    dn_amt_6th_week,\n", "    total_dn_amt,\n", "    dn_amount_last2week,\n", "    \n", "    -- wh_to_ds_distance_1st_week,\n", "    -- wh_to_ds_distance_2nd_week,\n", "    -- wh_to_ds_distance_3rd_week,\n", "    -- wh_to_ds_distance_4th_week,\n", "    -- wh_to_ds_distance_5th_week,\n", "    -- wh_to_ds_distance_6th_week,\n", "    \n", "    expected_distance_frwd_1st_week,\n", "    expected_distance_frwd_2nd_week,\n", "    expected_distance_frwd_3rd_week,\n", "    expected_distance_frwd_4th_week,\n", "    expected_distance_frwd_5th_week,\n", "    expected_distance_frwd_6th_week,\n", "    total_expected_distance_frwd,\n", "    total_expected_distance_frwd_last2week,\n", "    \n", "\n", "    travelled_distance_kms_1st_week as actual_travelled_distance_kms_1st_week_frwd,\n", "    travelled_distance_kms_2nd_week actual_travelled_distance_kms_2nd_week_frwd,\n", "    travelled_distance_kms_3rd_week actual_travelled_distance_kms_3rd_week_frwd,\n", "    travelled_distance_kms_4th_week actual_travelled_distance_kms_4th_week_frwd,\n", "    travelled_distance_kms_5th_week actual_travelled_distance_kms_5th_week_frwd,\n", "    travelled_distance_kms_6th_week actual_travelled_distance_kms_6th_week_frwd,\n", "    total_travelled_distance_kms_frwd as actual_total_travelled_distance_kms_frwd,\n", "    total_travelled_distance_kms_frwd_last2week as actual_total_travelled_distance_kms_frwd_last2week,\n", "    \n", "    halts_sum as halts_sum_frwd,\n", "    halts_per_trip as halts_per_trip_frwd,\n", "    trip_dn_percent,\n", "    (trip_dn_category * 3.0) AS trip_dn_score,\n", "    day_trips_score,\n", "    night_trips_score,\n", "    dn_billed_percent,\n", "    dn_billed_category,\n", "    (dn_billed_category * 3.0) AS dn_billed_score,\n", "    dn_amount_last2week_category,\n", "    (dn_amount_last2week_category * 3) AS dn_amount_last2week_score,\n", "    (dn_amount_last1week_category * 3) AS dn_amount_last1week_score,\n", "    frwd_diversion_percent as diversion_percent_frwd,\n", "    frwd_diversion_category * 2 as diversion_score_frwd,\n", "    frwd_last2w_diversion_percent as diversion_percent_last2week_frwd,\n", "    diversion_category_last2week_frwd * 2 as diversion_score_last2week_frwd,\n", "    ds_delay_category,\n", "    ds_delay_category * 3 as ota_score,\n", "    (COALESCE(halt_per_trip_flag, 0) * 7)  as halt_score,\n", "    lock_mismatch_category * 3 as lock_mismatch_score,\n", "    case when (night_trips_score - day_trips_score) >=0 then (night_trips_score - day_trips_score) else 0 end AS dispatch_slot_score\n", "    \n", "    -- ((trip_dn_category * 3) +  night_trips_score - day_trips_score + (dn_billed_category * 3) + (dn_amount_last2week_category * 3) + (dn_amount_last1week_category * 3) + \n", "    --  frwd_diversion_score + frwd_last2w_diversion_score  + (COALESCE(halt_per_trip_flag, 0) * 7)) + ((lock_mismatch_count*100.00/total_trips) * 3) AS raw_score\n", "from suspects_data\n", ")\n", "\n", "select *, cast((trip_dn_score\n", "            + dispatch_slot_score\n", "            + dn_billed_score\n", "            + dn_amount_last2week_score\n", "            + dn_amount_last1week_score\n", "            + diversion_score_frwd\n", "            + diversion_score_last2week_frwd\n", "            + ota_score\n", "            + halt_score\n", "            + lock_mismatch_score)\n", "             as int) score\n", "from scores\n", "\"\"\"\n", "wow_df = read_sql_query(wow_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "2483e966-a141-4394-82bd-1797f2741988", "metadata": {}, "outputs": [], "source": ["wow_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "144dcb5f-b6a1-41d7-b0d4-3b6fce914fff", "metadata": {}, "outputs": [], "source": ["# wow_df[wow_df['driver_mobile']=='9664474912']"]}, {"cell_type": "code", "execution_count": null, "id": "7bfcc994-5fbc-4aba-bc01-dbfb73db32b6", "metadata": {}, "outputs": [], "source": ["wow_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bcefaf25-2505-4d1d-8bfc-ff1a4386638a", "metadata": {}, "outputs": [], "source": ["# wow_df.driver_mobile.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "3bdacdf3-2461-45fe-8976-46fe4ac68c1f", "metadata": {}, "outputs": [], "source": ["wow_df[wow_df[\"driver_id\"] == \"27084\"][[\"driver_mobile\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "b9975ad2-23ee-4576-86ae-63e3f0b59877", "metadata": {}, "outputs": [], "source": ["# wow_df.driver_mobile.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "25765d8b-6c33-440c-ab1f-1f91bbd5b1a0", "metadata": {}, "outputs": [], "source": ["wow_driver_score_agg = (\n", "    wow_df.groupby([\"driver_mobile\"])\n", "    .agg(max_score=(\"score\", \"mean\"))  # Name the aggregated column as 'max_score'\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "90358f6d-c24a-49b7-8167-2066ed706b70", "metadata": {}, "outputs": [], "source": ["# wow_df[wow_df['driver_mobile']=='9528218002']"]}, {"cell_type": "code", "execution_count": null, "id": "21503630-fbdc-4a38-9d8b-f9a31c730995", "metadata": {}, "outputs": [], "source": ["# wow_driver_score_agg[wow_driver_score_agg['driver_mobile']=='9664474912']"]}, {"cell_type": "code", "execution_count": null, "id": "27210eb7-2c89-4c14-b9ae-d85b4081dfd4", "metadata": {}, "outputs": [], "source": ["# wow_driver_score_agg[wow_driver_score_agg['driver_mobile']=='8123554875']"]}, {"cell_type": "code", "execution_count": null, "id": "9d37020a-8073-4c97-9a1a-ca230027bbaf", "metadata": {}, "outputs": [], "source": ["# wow_df[wow_df['driver_id']=='26978']"]}, {"cell_type": "code", "execution_count": null, "id": "96376dcd-a065-4968-997b-558ca6f9c4db", "metadata": {}, "outputs": [], "source": ["suspects_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "10dd3c3e-4974-466c-9c34-4bafd6c78a8b", "metadata": {}, "outputs": [], "source": ["trip_level_driver_score_agg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e36ee13d-36ff-4021-843f-ee2ae3aef78e", "metadata": {}, "outputs": [], "source": ["# trip_level_driver_score_agg[trip_level_driver_score_agg['driver_mobile']=='9664474912']"]}, {"cell_type": "code", "execution_count": null, "id": "dd8c674f-fc46-4b0a-af49-784f3022cac7", "metadata": {}, "outputs": [], "source": ["# trip_level_driver_score_agg.rename(columns={\n", "#     'driver_name':'employee_name'\n", "# },inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "eb3b7925-5af4-4c5f-a777-1a30994a358b", "metadata": {}, "outputs": [], "source": ["suspects_df = wow_driver_score_agg.append(trip_level_driver_score_agg)"]}, {"cell_type": "code", "execution_count": null, "id": "606bf9a0-e7bd-4416-8078-90ed3e68cc37", "metadata": {}, "outputs": [], "source": ["suspects_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3cf8e579-bcb2-4abd-9d54-418a857f225c", "metadata": {}, "outputs": [], "source": ["suspects_df.max_score.max()"]}, {"cell_type": "code", "execution_count": null, "id": "f0af715d-a25a-40cc-8403-8608967e6224", "metadata": {}, "outputs": [], "source": ["# suspects_df[suspects_df['max_score']==102]"]}, {"cell_type": "code", "execution_count": null, "id": "a9bbebe2-0328-408e-ace0-ac49be377945", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7cd54036-05c0-4388-8b2a-1ece1180e982", "metadata": {}, "outputs": [], "source": ["suspects_df.driver_mobile.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "8f983d19-9249-4d88-838f-0555b126e4e6", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score = (\n", "    suspects_df.groupby([\"driver_mobile\"]).agg(max_score=(\"max_score\", \"max\")).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "18c4cfc3-d536-4723-9757-b7fc8d4344f5", "metadata": {}, "outputs": [], "source": ["# suspects_df_final_score[suspects_df_final_score['driver_mobile']=='7358650957']"]}, {"cell_type": "code", "execution_count": null, "id": "287b7837-0e12-409f-ab2f-848c8f49e031", "metadata": {}, "outputs": [], "source": ["# suspects_df_final_score.max_score.max()"]}, {"cell_type": "code", "execution_count": null, "id": "43f69d6c-be16-4f66-8220-c520ef2a2897", "metadata": {}, "outputs": [], "source": ["current_date = pd.Timestamp.now().date()"]}, {"cell_type": "code", "execution_count": null, "id": "76a3d223-4348-4e15-9448-07d9887385b3", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score[\"date_\"] = current_date"]}, {"cell_type": "code", "execution_count": null, "id": "aa33c198-ee6f-416c-961c-7e767095e000", "metadata": {}, "outputs": [], "source": ["# suspects_df=suspects_df[['date_','driver_id','employee_name','max_score']]"]}, {"cell_type": "code", "execution_count": null, "id": "5171da42-2dce-48dc-9db7-a0e27b1d2421", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score.sort_values(by=\"max_score\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4b7bc004-716b-49b1-b012-8b343b73856f", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score[\"normalised_score\"] = suspects_df_final_score[\"max_score\"] / 20"]}, {"cell_type": "code", "execution_count": null, "id": "86d5e31b-468b-4b02-849b-5b5680f3b244", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f07bf40f-79a3-4742-be8d-e2d87f6d6e99", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score = suspects_df_final_score[[\"date_\", \"driver_mobile\", \"normalised_score\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "3f8a3542-c14e-4457-9b74-e96bf8f575d7", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ff3b4d11-2867-4a1c-916a-320d4d325446", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score.normalised_score.max()"]}, {"cell_type": "code", "execution_count": null, "id": "57e777c2-168f-460b-b235-e22329024510", "metadata": {}, "outputs": [], "source": ["# suspects_df_final_score[suspects_df_final_score['normalised_score']==3.9]"]}, {"cell_type": "code", "execution_count": null, "id": "5c41967d-0e7e-4f29-99c2-c5e54376e3a2", "metadata": {}, "outputs": [], "source": ["# wow_df[wow_df['driver_id']=='25168']"]}, {"cell_type": "code", "execution_count": null, "id": "b1c0dce5-5c7f-4c5e-a43f-60323e550549", "metadata": {}, "outputs": [], "source": ["# trip_level_df[trip_level_df['driver_id']=='100']"]}, {"cell_type": "code", "execution_count": null, "id": "f506e6cd-6a56-46a6-a69c-8d765e5b63e8", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score_with_dn = sqldf(\n", "    \"\"\"\n", "select fs.date_,fs.driver_mobile,round(fs.normalised_score,1) as threat_score, sum(dn_amt) as dn_amt\n", "from suspects_df_final_score fs\n", "left join trip_level_df tl on tl.driver_mobile=fs.driver_mobile\n", "group by 1,2,3\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ef2d2ee9-c2ed-421d-87c5-0c94ed627c0a", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score_with_dn.head()"]}, {"cell_type": "code", "execution_count": null, "id": "65e5afc1-b983-4a76-8d7a-c1fb6e7949eb", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score_with_dn.driver_mobile.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "c6108961-a55a-4ada-a8a4-2c543f936491", "metadata": {}, "outputs": [], "source": ["suspects_df_final_score_with_dn.shape"]}, {"cell_type": "code", "execution_count": null, "id": "838fcabb-eb17-42ca-ae4d-99c244cb3f96", "metadata": {}, "outputs": [], "source": ["# suspects_df_final_score_with_dn1 = suspects_df_final_score_with_dn[suspects_df_final_score_with_dn['']]"]}, {"cell_type": "code", "execution_count": null, "id": "4af8ff9f-f7c4-4c33-bbd4-eccabb3d7163", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dae85600-b9be-468b-9b0d-c3735f328cad", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "da364e67-214d-495c-9bae-495585ff3e28", "metadata": {}, "outputs": [], "source": ["df = suspects_df_final_score_with_dn[[\"driver_mobile\", \"threat_score\", \"dn_amt\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "cd2e4dcd-9cb7-4cfa-b12b-3cb6c3a4ca30", "metadata": {}, "outputs": [], "source": ["df.columns = [\"driver_mobile\", \"threat_score\", \"dn_weekly_breach_amount\"]"]}, {"cell_type": "code", "execution_count": null, "id": "d8ac9708-f929-4642-9c0a-d5d084f1d274", "metadata": {}, "outputs": [], "source": ["df = df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "2123c727-2173-4342-a007-e832b97a1122", "metadata": {}, "outputs": [], "source": ["df.rename(columns={\"driver_mobile\": \"phone_number\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "75a35452-0e8b-4774-85e9-6622392c1429", "metadata": {}, "outputs": [], "source": ["df[\"dn_weekly_breach_amount\"] = df[\"dn_weekly_breach_amount\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "39f0cb4d-9a4e-4457-a938-ff3599ea6f02", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f10522c3-08b5-49e6-a9de-73a14d1e7d33", "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "904f28ec-bd46-45db-9294-e402b084d699", "metadata": {}, "outputs": [], "source": ["# df[df['phone_number']=='9664474912']"]}, {"cell_type": "code", "execution_count": null, "id": "83c7591f-7535-4711-a486-0e9603fd2db3", "metadata": {}, "outputs": [], "source": ["# df[df['threat_score']==3.6].shape"]}, {"cell_type": "code", "execution_count": null, "id": "119f9739-6bb4-4be0-9e4e-7bd66ca916e8", "metadata": {}, "outputs": [], "source": ["# Convert DataFrame to the required JSON format for API\n", "api_payload = []\n", "\n", "# Iterate over the DataFrame rows\n", "for index, row in df.iterrows():\n", "    api_payload.append(\n", "        {\n", "            \"phone_number\": str(row[\"phone_number\"]),  # Convert to string if needed\n", "            \"threat_details\": {\n", "                \"threat_score\": row[\"threat_score\"],\n", "                \"threat_meta\": {\"dn_weekly_breach_amount\": row[\"dn_weekly_breach_amount\"]},\n", "            },\n", "        }\n", "    )\n", "\n", "# Print the formatted API payload\n", "# print(json.dumps(api_payload, indent=4))"]}, {"cell_type": "code", "execution_count": null, "id": "2c450c9e-04a2-49ae-9ffa-fded934d40f7", "metadata": {}, "outputs": [], "source": ["# print(api_payload)"]}, {"cell_type": "code", "execution_count": null, "id": "7eafee8e-736a-4907-bac9-fbb58a7b9e7e", "metadata": {}, "outputs": [], "source": ["# Headers (same as in your example, but ensure they are correct)\n", "headers = {\n", "    \"Content-Type\": \"application/json\",\n", "    \"x-api-key\": \"GwcM049U.DCTm6jxvPFtbIdoDUVl2dYSaDTzX1TAn\",  # Replace with your actual API key\n", "    \"Cookie\": \"csrftoken=TQgryTggqcZhHufajl8S9949WNKUsov9eqgZyA2DkIgpp374DwL0ckGBBvwdaTzN\",  # Replace with your actual cookie\n", "}\n", "\n", "# The URL for the API endpoint (ensure it's correct)\n", "url = \"https://fleet-management-canary-fleet-management.prod-sgp-k8s.grofer.io/user/bulk-update-drivers/\"\n", "\n", "\n", "# Make the PUT API request instead of POST\n", "response = requests.put(\n", "    url, headers=headers, data=json.dumps(api_payload).replace(\"\"\"'\"\"\", '''\"''')\n", ")\n", "\n", "\n", "if response.status_code == 200:\n", "    print(\"Request was successful.\")\n", "    # print(\"Response JSON:\", response.json())\n", "else:\n", "    print(f\"Request failed with status code {response.status_code}.\")\n", "    # print(\"Response text:\", response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "77fd93f0-8286-4752-9585-49e4b0e78573", "metadata": {}, "outputs": [], "source": ["# if response.status_code == 200:x\n", "#     print(\"table\")"]}, {"cell_type": "code", "execution_count": null, "id": "e4670ebd-05c7-412e-88cb-63676b0034f8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}