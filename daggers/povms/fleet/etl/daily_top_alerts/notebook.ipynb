{"cells": [{"cell_type": "code", "execution_count": null, "id": "59caf057-f0dc-4ec7-bb03-366ed5fce620", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "98e009e0-5f26-4497-8593-7ac106a3af0e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "bcdfc792-ace2-4f3b-b2c3-3942db72934b", "metadata": {}, "outputs": [], "source": ["def be_highest_cpo():\n", "    sql = \"\"\"\n", "    with ru as (select (date(date_)) as dt, source_facility_id as be,source_name as be_name,\n", "    sum(date_cost) as cost from metrics.base_fleet_costs\n", "    where date(date_) = current_date -1\n", "    and source_facility_id >0\n", "    and source_facility_id != 1212\n", "    group by 1,2,3),\n", "\n", "    bla as (select distinct a.facility_id, cloud_store_id as outlet_id from lake_po.physical_facility_outlet_mapping a\n", "        join lake_po.bulk_facility_outlet_mapping b on a.facility_id=b.facility_id\n", "        join lake_retail.warehouse_outlet_mapping \n", "        on warehouse_id=a.outlet_id\n", "        where a.active=1 and a.ars_active=1\n", "        group by 1,2),\n", "\n", "    chico as (SELECT dt, facility_id ,SUM(billed_qty) AS outbound_quan\n", "        FROM (\n", "                SELECT DISTINCT p.grofers_order_id AS sto_id ,DATE(p.created_at) as dt,bla.facility_id, pid.invoice_id, pp.item_id, pid.quantity AS billed_qty\n", "                FROM lake_pos.pos_invoice_product_details pid\n", "                JOIN lake_rpc.product_product pp ON pp.variant_id = pid.variant_id\n", "                JOIN lake_pos.pos_invoice p ON p.id = pid.invoice_id\n", "                join bla on bla.outlet_id = p.outlet_id\n", "                join metrics.esto_details dm on p.grofers_order_id=dm.sto_id\n", "                WHERE pp.active = 1 AND pp.approved = 1\n", "                AND p.invoice_type_id IN (5,14,16)\n", "                AND DATE(p.created_at) = current_date - 1\n", "                and dm.receiving_outlet_id != bla.outlet_id\n", "            )\n", "        where item_id not in (select distinct item_id from lake_rpc.product_product where handling_type =8)\n", "        GROUP BY 1,2\n", "        )\n", "\n", "\n", "        select ru.*, outbound_quan, (cost/outbound_quan) as cost_per_outbound from ru\n", "        join chico on ru.dt=chico.dt and ru.be=chico.facility_id\n", "        order by 2,1\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "be = be_highest_cpo()"]}, {"cell_type": "code", "execution_count": null, "id": "82ab6211-b85a-4a65-9491-853fedb651b5", "metadata": {}, "outputs": [], "source": ["be"]}, {"cell_type": "code", "execution_count": null, "id": "fff2cfc8-8bcc-4984-abd3-9116a94f5d1e", "metadata": {}, "outputs": [], "source": ["be = be.sort_values(by=\"cost_per_outbound\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "537126b8-2184-48ee-910a-2d2897efc64b", "metadata": {}, "outputs": [], "source": ["w = be.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "c9d98f6f-279b-405e-9603-edbd14c68866", "metadata": {}, "outputs": [], "source": ["w = w.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "0c1c0622-d741-477d-b250-435ac6d818a9", "metadata": {}, "outputs": [], "source": ["w.columns"]}, {"cell_type": "code", "execution_count": null, "id": "9f63bd6d-a7df-4ce5-9dc8-c854373dd3c0", "metadata": {}, "outputs": [], "source": ["w = w[[\"dt\", \"be\", \"be_name\", \"cost\", \"outbound_quan\", \"cost_per_outbound\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "c5af0fb0-a9a4-47e7-b1e6-4b2c03bdf0f7", "metadata": {}, "outputs": [], "source": ["w"]}, {"cell_type": "code", "execution_count": null, "id": "384d7601-8a3a-4755-b9e2-220516e60ee6", "metadata": {}, "outputs": [], "source": ["w.to_csv(\"be_highest_cpo.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "cacec25a-9e94-421d-b8e0-3737a529643f", "metadata": {}, "outputs": [], "source": ["def fe_highest_cpo():\n", "    sql = \"\"\"\n", "    with ru as (select (date(date_)) as dt, destination_outlet_id as fe, max(destination_name) as fe_name,\n", "    sum(date_cost) as cost from metrics.base_fleet_costs\n", "    where date(date_) = current_date - 1\n", "    and source_facility_id >0\n", "    and destination_outlet_id >0\n", "    and source_facility_id != 1212\n", "    group by 1,2),\n", "\n", "    bla as (select distinct a.facility_id, cloud_store_id as outlet_id from lake_po.physical_facility_outlet_mapping a\n", "        join lake_po.bulk_facility_outlet_mapping b on a.facility_id=b.facility_id\n", "        join lake_retail.warehouse_outlet_mapping \n", "        on warehouse_id=a.outlet_id\n", "        where a.active=1 and a.ars_active=1\n", "        group by 1,2),\n", "\n", "    chico as (SELECT dt, outlet_id ,SUM(billed_qty) AS outbound_quan\n", "        FROM (\n", "                SELECT DISTINCT p.grofers_order_id AS sto_id ,DATE(p.created_at) as dt,receiving_outlet_id as outlet_id, pid.invoice_id, pp.item_id, pid.quantity AS billed_qty\n", "                FROM lake_pos.pos_invoice_product_details pid\n", "                JOIN lake_rpc.product_product pp ON pp.variant_id = pid.variant_id\n", "                JOIN lake_pos.pos_invoice p ON p.id = pid.invoice_id\n", "                join bla on bla.outlet_id = p.outlet_id\n", "                join metrics.esto_details dm on p.grofers_order_id=dm.sto_id\n", "                WHERE pp.active = 1 AND pp.approved = 1\n", "                AND p.invoice_type_id IN (5,14,16)\n", "                AND DATE(p.created_at) = current_date - 1\n", "                and dm.receiving_outlet_id != bla.outlet_id\n", "            )\n", "        where item_id not in (select distinct item_id from lake_rpc.product_product where handling_type =8)\n", "        GROUP BY 1,2\n", "        )\n", "\n", "\n", "        select ru.*, outbound_quan, (cost/outbound_quan) as cost_per_outbound, (case when ru.fe in (select retail_outlet_id from dwh.e3_store_go_live_planning) then 'Live' else 'Closed' end) as Status from ru\n", "        join chico on ru.dt=chico.dt and ru.fe=chico.outlet_id\n", "        order by 2,1\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "fe = fe_highest_cpo()"]}, {"cell_type": "code", "execution_count": null, "id": "0f869913-575f-4f8e-bbb1-cd2042699cf6", "metadata": {}, "outputs": [], "source": ["fe"]}, {"cell_type": "code", "execution_count": null, "id": "d7a48912-2324-46a6-aa41-0c1a39548f07", "metadata": {}, "outputs": [], "source": ["fe[fe[\"status\"] == \"Closed\"]"]}, {"cell_type": "code", "execution_count": null, "id": "d3abd479-6098-4eb7-a7a5-8977d242b969", "metadata": {}, "outputs": [], "source": ["fe = fe.sort_values(by=\"cost_per_outbound\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f1395338-a15b-44e7-a9bc-69b1e783914b", "metadata": {}, "outputs": [], "source": ["x = fe.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "f149ac6c-bc53-4108-8ec5-c1a1af9c9e6e", "metadata": {}, "outputs": [], "source": ["x = x.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "3e6d7c98-699e-45f8-99e3-092a6e175fec", "metadata": {}, "outputs": [], "source": ["x.columns"]}, {"cell_type": "code", "execution_count": null, "id": "ee4d3e2f-faf0-493f-808a-4bd0bdb1f07f", "metadata": {}, "outputs": [], "source": ["x = x[[\"dt\", \"fe\", \"fe_name\", \"cost\", \"outbound_quan\", \"cost_per_outbound\", \"status\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "28570aa3-3a7f-4a65-b7b5-36c221d45b84", "metadata": {}, "outputs": [], "source": ["x"]}, {"cell_type": "code", "execution_count": null, "id": "1d92866c-07f0-455c-9583-71c03ce8a86c", "metadata": {}, "outputs": [], "source": ["x.to_csv(\"fe_highest_cpo.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "ac34c342-76eb-47df-aeb3-ce2f99d670c6", "metadata": {}, "outputs": [], "source": ["def city_highest_cpo():\n", "    sql = \"\"\"\n", "        with ru as (select (date(date_)) as dt, source_facility_id as be,source_name as be_name,\n", "    case when source_city = 'Noida' then 'NCR' else (case when source_city = 'Gurgaon' then 'NCR' else (case when source_city = 'Delhi' then 'NCR' else (case when source_city = 'Ghaziabad' then 'NCR' else (case when source_city = 'Jhajjar' then 'NCR' else (case when source_city = 'Mohali' then 'Lucknow' else source_city end) end) end) end ) end) end as source_city,\n", "    sum(date_cost) as cost from metrics.base_fleet_costs\n", "    where date(date_) = current_date -1\n", "    and source_facility_id != 1212\n", "    and source_facility_id >0\n", "    group by 1,2,3,4),\n", "\n", "    bla as (select distinct a.facility_id, cloud_store_id as outlet_id from lake_po.physical_facility_outlet_mapping a\n", "        join lake_po.bulk_facility_outlet_mapping b on a.facility_id=b.facility_id\n", "        join lake_retail.warehouse_outlet_mapping \n", "        on warehouse_id=a.outlet_id\n", "        where a.active=1 and a.ars_active=1\n", "        group by 1,2),\n", "\n", "    chico as (SELECT dt,facility_id ,SUM(billed_qty) AS outbound_quan\n", "        FROM (\n", "                SELECT DISTINCT p.grofers_order_id AS sto_id ,DATE(p.created_at) as dt,bla.facility_id, pid.invoice_id, pp.item_id, pid.quantity AS billed_qty\n", "                FROM lake_pos.pos_invoice_product_details pid\n", "                JOIN lake_rpc.product_product pp ON pp.variant_id = pid.variant_id\n", "                JOIN lake_pos.pos_invoice p ON p.id = pid.invoice_id\n", "                join bla on bla.outlet_id = p.outlet_id\n", "                join metrics.esto_details dm on p.grofers_order_id=dm.sto_id\n", "                WHERE pp.active = 1 AND pp.approved = 1\n", "                AND p.invoice_type_id IN (5,14,16)\n", "                AND DATE(p.created_at) = current_date - 1\n", "                and dm.receiving_outlet_id != bla.outlet_id\n", "            )\n", "        where item_id not in (select distinct item_id from lake_rpc.product_product where handling_type =8)\n", "        GROUP BY 1,2\n", "        )\n", "\n", "\n", "        select ru.dt, source_city as city, sum(cost) as cost, sum(outbound_quan) as outbound_quan, (sum(ru.cost)/sum(chico.outbound_quan)) as cost_per_outbound from ru\n", "        join chico on ru.dt=chico.dt and ru.be=chico.facility_id\n", "        group by 1,2\n", "        order by 2,1\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "city = city_highest_cpo()"]}, {"cell_type": "code", "execution_count": null, "id": "11fcd187-072b-4a5f-89d5-df64af249038", "metadata": {}, "outputs": [], "source": ["city = city.sort_values(by=\"cost_per_outbound\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6cc342f3-e54e-4b41-968e-9648c214a3f2", "metadata": {}, "outputs": [], "source": ["y = city.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "39147e1b-28ae-4faa-b409-5721e3e50f6b", "metadata": {}, "outputs": [], "source": ["y = y.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "f4b6689c-64ee-4de0-84de-71c3900b70ca", "metadata": {}, "outputs": [], "source": ["y.columns"]}, {"cell_type": "code", "execution_count": null, "id": "008c487d-83aa-4661-a557-624339101e7a", "metadata": {}, "outputs": [], "source": ["y = y[[\"dt\", \"city\", \"cost\", \"outbound_quan\", \"cost_per_outbound\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "bcfe3951-6f99-41d7-a893-20d9d5c8b8ee", "metadata": {}, "outputs": [], "source": ["y"]}, {"cell_type": "code", "execution_count": null, "id": "5ca53218-18bd-43cc-8bfa-586a40e7ca52", "metadata": {}, "outputs": [], "source": ["y.to_csv(\"city_highest_cpo.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "aa2889ff-dfa0-4af4-9fff-3083f84c7c26", "metadata": {}, "outputs": [], "source": ["def adhoc_numbers():\n", "    sql = \"\"\"\n", "    with bla as (select date(date_), source_facility_id, source_name, sum (((CAST ((\"date_cost\") as float))/ (CAST ((\"daily/trip cost\") as float)) )) as vehicle_count\n", "    from metrics.base_fleet_costs\n", "    where date(date_) = current_date -1\n", "    and source_facility_id >0\n", "    and source_facility_id != 1212\n", "    and \"fixed/adhoc\" = 'Adhoc'\n", "    and (CAST ((\"daily/trip cost\") as float)) > 0\n", "    group by 1,2,3\n", "    order by 4 desc)\n", "    select * from bla\n", "    where vehicle_count>0\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "adhoc = adhoc_numbers()"]}, {"cell_type": "code", "execution_count": null, "id": "d883e840-cddf-4f3d-a3f4-58145d5a4fbc", "metadata": {}, "outputs": [], "source": ["z = adhoc"]}, {"cell_type": "code", "execution_count": null, "id": "4a6964d7-2144-4bbe-bb99-4bb2cf056f60", "metadata": {}, "outputs": [], "source": ["z"]}, {"cell_type": "code", "execution_count": null, "id": "1c13363b-093e-479c-9365-10f8233c3473", "metadata": {}, "outputs": [], "source": ["z.to_csv(\"adhoc.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca32267a-ff27-411b-8b12-4f7346d88c82", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=3.0,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "fig, ax = render_mpl_table(w, header_columns=0)\n", "fig.savefig(\"be_highest_cpo.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "5b9c30b2-3e5e-40a7-b548-dfaeb142e486", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.0,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "fig, ax = render_mpl_table(x, header_columns=0)\n", "fig.savefig(\"fe_highest_cpo.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "b7250404-5dbf-44da-acf3-788f19cf7d8f", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=3.0,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "fig, ax = render_mpl_table(y, header_columns=0)\n", "fig.savefig(\"city_highest_cpo.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "f04b3e59-7c73-4a49-90a0-d8231dbe510f", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=4.0,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "fig, ax = render_mpl_table(z, header_columns=0)\n", "fig.savefig(\"adhoc.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "e5fcbe28-4f5e-49b7-8b5a-2fbfc8a85834", "metadata": {}, "outputs": [], "source": ["print(\"ready\")"]}, {"cell_type": "code", "execution_count": null, "id": "dd5f3d8d-b4e6-4490-9bef-23ba8fa862a3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d9ce346-7d5a-492b-a26e-e9ba261631ae", "metadata": {}, "outputs": [], "source": ["channel = \"pr_test_channel\"\n", "text_req = \"BE with high cost/outbound\"\n", "text_req\n", "pb.send_slack_message(channel=channel, text=text_req, files=[\"./be_highest_cpo.png\"])"]}, {"cell_type": "code", "execution_count": null, "id": "74c80277-dc50-4f0e-9bdd-ae1ee3df1cee", "metadata": {}, "outputs": [], "source": ["channel = \"bl-fleet-core-team\"\n", "text_req = \"BE with high cost/outbound\"\n", "text_req\n", "pb.send_slack_message(channel=channel, text=text_req, files=[\"./be_highest_cpo.png\"])"]}, {"cell_type": "code", "execution_count": null, "id": "93268ad9-9483-4d06-9805-3c1e3bff837d", "metadata": {}, "outputs": [], "source": ["channel = \"bl-fleet-core-team\"\n", "text_req = \"FE with high cost/outbound)\"\n", "text_req\n", "pb.send_slack_message(channel=channel, text=text_req, files=[\"./fe_highest_cpo.png\"])"]}, {"cell_type": "code", "execution_count": null, "id": "763b428f-8ca6-4f9e-bce7-563a44805861", "metadata": {}, "outputs": [], "source": ["channel = \"bl-fleet-core-team\"\n", "text_req = \"Cities with high cost/outbound\"\n", "text_req\n", "pb.send_slack_message(channel=channel, text=text_req, files=[\"./city_highest_cpo.png\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5191645f-9b43-4f86-a5a3-d1c025026bd1", "metadata": {}, "outputs": [], "source": ["channel = \"bl-fleet-core-team\"\n", "text_req = \"Number of adhoc vehicles placed yesterday\"\n", "text_req\n", "pb.send_slack_message(channel=channel, text=text_req, files=[\"./adhoc.png\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}