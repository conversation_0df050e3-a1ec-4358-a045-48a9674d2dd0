alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: daily_top_alerts
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters:
    end_date: ''
    start_date: ''
owner:
  email: <EMAIL>
  slack_id: U04JZTM4FC7
path: povms/fleet/etl/daily_top_alerts
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 5 * * *
  start_date: '2023-03-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
