{"cells": [{"cell_type": "code", "execution_count": null, "id": "e0c9cede-04d1-4e14-a12b-68292c0d577e", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "from pytz import timezone\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "80f6f9dd-9c54-4550-9163-b9521bb8e2b0", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "1d2df192-b0f3-453c-9eab-44a06be3e405", "metadata": {}, "outputs": [], "source": ["# mode = \"backfill\"\n", "mode = \"normal\""]}, {"cell_type": "code", "execution_count": null, "id": "0a2ccec3-4248-4b73-afd0-6d8f7305afa6", "metadata": {}, "outputs": [], "source": ["if mode == \"backfill\":\n", "    fall_back = 60\n", "else:\n", "    fall_back = 4"]}, {"cell_type": "code", "execution_count": null, "id": "954fdfd1-d34a-496e-8b38-219cd6d943b7", "metadata": {}, "outputs": [], "source": ["fall_back"]}, {"cell_type": "code", "execution_count": null, "id": "a6c8d99a-b917-4b43-8412-76f2414b2e8f", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "    with ars_job_runs as (\n", "    select run_id, \n", "        json_extract_scalar(simulation_params, '$.run') as run_type,\n", "        json_extract_scalar(simulation_params, '$.mode') as mode\n", "    from ars.job_run \n", "    where run_id not in ('20210607116002', '20210607116006', '20210607116005', '20210607116003')\n", "        and started_at > (current_date-interval'{fall_back}'day) \n", "        and completed_at is not null\n", "        and simulation_params like '%%ars%%'\n", "        and simulation_params not like '%%hyperpure_run%%'\n", "        and simulation_params not like '%%mode%%'\n", "        AND simulation_params not like '%%mode%%'\n", "        AND simulation_params not like '%%hyperpure_run%%'\n", "        AND simulation_params not like '%%test%%'\n", "        AND simulation_params not like '%%type%%'\n", "        AND simulation_params not like '%%manual%%'\n", "        AND simulation_params not like '%%any_day_po%%'    \n", "        AND simulation_params not like '%%spr_migration_simulation%%'\n", "\n", "),\n", "\n", "\n", "run_wise_resource_caps as (\n", "    select distinct b.backend_outlet_id, \n", "        b.frontend_outlet_id, \n", "        b.run_id, \n", "        b.picking_capacity_quantity,\n", "        b.picking_capacity_sku,\n", "        b.truck_load_capacity,\n", "        b.inward_capacity\n", "    from ars.bulk_facility_transfer_days b\n", "        where truck_load_capacity > 0\n", "),\n", "\n", "indent_report as (\n", "    select run_id, \n", "        backend_outlet_id, \n", "        frontend_outlet_id,\n", "        sto_date,\n", "        sum(total_item_weight_gm)/1000 as total_weight_kg,\n", "        sum(indent_qty) as indent_qty,\n", "        sum(inward_drop) as inward_drop,\n", "        sum(storage_drop) as storage_drop,\n", "        sum(truck_load_drop) as truck_load_drop,\n", "        sum(picking_capacity_quantity_drop) as picking_capacity_quantity_drop,\n", "        sum(picking_capacity_sku_drop) as picking_capacity_sku_drop\n", "    from (select run_id, \n", "            backend_outlet_id, \n", "            frontend_outlet_id,\n", "            sto_date,\n", "            indent.item_id,\n", "            sum(weight_in_gm) as item_weight,\n", "            sum(sto_quantity_post_truncation_in_case) as indent_qty,\n", "            sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.inward_drop') AS INTEGER)) AS inward_drop,\n", "            sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER)) AS storage_drop,\n", "            sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.truck_load_drop') AS INTEGER)) AS truck_load_drop,\n", "            sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_quantity_drop') AS INTEGER)) AS picking_capacity_quantity_drop,\n", "            sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_sku_drop') AS INTEGER)) AS picking_capacity_sku_drop,\n", "            (sum(weight_in_gm)*sum(sto_quantity_post_truncation_in_case)) as total_item_weight_gm\n", "        from ars.transfers_optimization_results_v2 as indent\n", "        INNER JOIN rpc.item_details id ON id.item_id = indent.item_id AND id.active = 1 AND id.approved = 1\n", "        where sto_date > (current_date-interval'{fall_back}'day) \n", "        and indent.insert_ds_ist > cast((current_date-interval'{fall_back}'day)  as varchar)\n", "        and weight_in_gm>0\n", "        group by 1,2,3,4,5)\n", "    group by 1,2,3,4\n", "),\n", "\n", "final_utilisation as (\n", "    select ir.sto_date,\n", "        ir.run_id,\n", "        ir.backend_outlet_id,\n", "        ir.frontend_outlet_id,\n", "        jr.run_type,\n", "        jr.mode,\n", "        sum(ir.total_weight_kg) as total_weight,\n", "        sum(ir.indent_qty) as indent_qty,\n", "        sum(ir.inward_drop) as inward_drop,\n", "        sum(ir.storage_drop) as storage_drop,\n", "        sum(ir.truck_load_drop) as truck_load_drop,\n", "        sum(ir.picking_capacity_quantity_drop) as picking_capacity_quantity_drop,\n", "        sum(ir.picking_capacity_sku_drop) as picking_capacity_sku_drop,\n", "        sum(rc.picking_capacity_quantity) as total_picking_caps_qty,\n", "        sum(rc.picking_capacity_sku) as total_picking_caps_sku,\n", "        sum(rc.truck_load_capacity) as total_load_caps,\n", "        sum(rc.inward_capacity) as total_inw_caps\n", "\n", "    from indent_report ir \n", "    inner join run_wise_resource_caps rc on ir.run_id = rc.run_id and ir.backend_outlet_id = rc.backend_outlet_id and ir.frontend_outlet_id = rc.frontend_outlet_id\n", "    inner join ars_job_runs jr on ir.run_id = jr.run_id\n", "    group by 1,2,3,4,5,6\n", "),\n", "\n", "active_assortment as (\n", "    select facility_id, \n", "        count(distinct item_id) as count_act_and_temp_act_items,\n", "        count(distinct case when master_assortment_substate_id = 1 then item_id end) as count_act_items,\n", "        count(distinct case when master_assortment_substate_id = 3 then item_id end) as count_temp_act_items\n", "    from rpc.product_facility_master_assortment\n", "    where master_assortment_substate_id in (1,3)\n", "    and active = 1\n", "    group by 1\n", "),\n", "\n", "\n", "\n", "availability_proxy as (\n", "    select tor.run_id, \n", "        sto_date,\n", "        backend_outlet_id,\n", "        frontend_outlet_id,\n", "        frontend_facility_id,\n", "        sum(case when sto_quantity > 0 then 1 else 0 end) as count_active_item_ids_in_run, \n", "        count(distinct case when oiu2.final_inventory = 0 and oiu.final_inventory > 0 and sto_quantity_post_truncation_in_case = 0 then tor.item_id end) as count_oos_item_ids_in_run,\n", "        sum(oiu.final_inventory) as total_backend_inv,\n", "        sum(oiu2.final_inventory) as total_frontend_inv,\n", "        sum(sto_quantity) as total_sto_quantity\n", "    from ars.transfers_optimization_results_v2 tor\n", "    inner join ars.outlet_item_universe oiu on tor.run_id = oiu.run_id and tor.backend_outlet_id = oiu.outlet_id and tor.item_id = oiu.item_id\n", "    inner join ars.outlet_item_universe oiu2 on tor.run_id = oiu2.run_id and tor.frontend_outlet_id = oiu2.outlet_id and tor.item_id = oiu2.item_id\n", "    inner join ars_job_runs jr on tor.run_id = jr.run_id\n", "    where sto_date > (current_date-interval'{fall_back}'day) \n", "    and tor.insert_ds_ist > cast((current_date-interval'{fall_back}'day)  as varchar)\n", "    and oiu.insert_ds_ist > cast((current_date-interval'{fall_back}'day)  as varchar)\n", "    and oiu2.insert_ds_ist > cast((current_date-interval'{fall_back}'day)  as varchar)\n", "    group by 1,2,3,4,5\n", "\n", ")\n", "\n", "\n", "\n", "\n", "\n", "select ap.sto_date,\n", "    ap.run_id,\n", "    ap.backend_outlet_id,\n", "    co1.name as backend_name,\n", "    ap.frontend_outlet_id,\n", "    ap.frontend_facility_id,\n", "    co.name as frontend_name,\n", "    ap.count_active_item_ids_in_run,\n", "    ap.count_oos_item_ids_in_run,\n", "    aa.count_act_items,\n", "    aa.count_temp_act_items,\n", "    aa.count_act_and_temp_act_items,\n", "    ap.total_backend_inv,\n", "    ap.total_frontend_inv,\n", "    ap.total_sto_quantity,\n", "    fu.run_type,\n", "    fu.mode,\n", "    fu.total_weight,\n", "    fu.indent_qty,\n", "    fu.inward_drop,\n", "    fu.storage_drop,\n", "    fu.truck_load_drop,\n", "    fu.picking_capacity_quantity_drop,\n", "    fu.picking_capacity_sku_drop,\n", "    fu.total_picking_caps_qty,\n", "    fu.total_picking_caps_sku,\n", "    fu.total_load_caps,\n", "    fu.total_inw_caps\n", "from availability_proxy ap\n", "left join final_utilisation fu on ap.run_id = fu.run_id and ap.backend_outlet_id = fu.backend_outlet_id and ap.frontend_outlet_id = fu.frontend_outlet_id  \n", "left join active_assortment aa on ap.frontend_facility_id = aa.facility_id\n", "left join retail.console_outlet co on ap.frontend_outlet_id = co.id\n", "left join retail.console_outlet co1 on ap.backend_outlet_id = co1.id\n", "\n", "    \n", "\n", "\n", "\n", "\"\"\"\n", "dataset_raw = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "85e0c81e-22eb-4fbd-84fe-22d954935d9c", "metadata": {}, "outputs": [], "source": ["dataset_raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "dde1e122-b461-4878-8b06-47879a7912e6", "metadata": {}, "outputs": [], "source": ["dataset_raw.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "78fab16b-a4c9-4ed1-9137-09bcc1dfe97f", "metadata": {}, "outputs": [], "source": ["dataset = dataset_raw.fillna(0)\n", "dataset[\"sto_date\"] = pd.to_datetime(dataset[\"sto_date\"])\n", "dataset[\"run_id\"] = dataset[\"run_id\"].astype(\"str\")\n", "dataset[\"backend_outlet_id\"] = dataset[\"backend_outlet_id\"].astype(\"int\")\n", "dataset[\"backend_name\"] = dataset[\"backend_name\"].astype(\"str\")\n", "dataset[\"frontend_outlet_id\"] = dataset[\"frontend_outlet_id\"].astype(\"int\")\n", "dataset[\"frontend_facility_id\"] = dataset[\"frontend_facility_id\"].astype(\"int\")\n", "dataset[\"frontend_name\"] = dataset[\"frontend_name\"].astype(\"str\")\n", "\n", "dataset[\"count_active_item_ids_in_run\"] = dataset[\n", "    \"count_active_item_ids_in_run\"\n", "].astype(\"float\")\n", "dataset[\"count_oos_item_ids_in_run\"] = dataset[\"count_oos_item_ids_in_run\"].astype(\n", "    \"float\"\n", ")\n", "dataset[\"count_act_items\"] = dataset[\"count_act_items\"].astype(\"float\")\n", "dataset[\"count_temp_act_items\"] = dataset[\"count_temp_act_items\"].astype(\"float\")\n", "dataset[\"total_backend_inv\"] = dataset[\"total_backend_inv\"].astype(\"float\")\n", "dataset[\"total_frontend_inv\"] = dataset[\"total_frontend_inv\"].astype(\"float\")\n", "dataset[\"total_sto_quantity\"] = dataset[\"total_sto_quantity\"].astype(\"float\")\n", "\n", "dataset[\"run_type\"] = dataset[\"run_type\"].astype(\"str\")\n", "dataset[\"mode\"] = dataset[\"mode\"].astype(\"str\")\n", "\n", "dataset[\"total_weight\"] = dataset[\"total_weight\"].astype(\"float\")\n", "dataset[\"indent_qty\"] = dataset[\"indent_qty\"].astype(\"float\")\n", "\n", "dataset[\"inward_drop\"] = dataset[\"inward_drop\"].astype(\"float\")\n", "dataset[\"storage_drop\"] = dataset[\"storage_drop\"].astype(\"float\")\n", "dataset[\"truck_load_drop\"] = dataset[\"truck_load_drop\"].astype(\"float\")\n", "dataset[\"picking_capacity_quantity_drop\"] = dataset[\n", "    \"picking_capacity_quantity_drop\"\n", "].astype(\"float\")\n", "dataset[\"picking_capacity_sku_drop\"] = dataset[\"picking_capacity_sku_drop\"].astype(\n", "    \"float\"\n", ")\n", "\n", "dataset[\"total_picking_caps_qty\"] = dataset[\"total_picking_caps_qty\"].astype(\"float\")\n", "dataset[\"total_picking_caps_sku\"] = dataset[\"total_picking_caps_sku\"].astype(\"float\")\n", "dataset[\"total_load_caps\"] = dataset[\"total_load_caps\"].astype(\"float\")\n", "dataset[\"total_inw_caps\"] = dataset[\"total_inw_caps\"].astype(\"float\")"]}, {"cell_type": "code", "execution_count": null, "id": "49ec20d4-9d3f-4e31-b1e4-3ae2ed88b2ce", "metadata": {}, "outputs": [], "source": ["dataset.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e83fcaf5-dce2-4db9-a3a3-64e24f2d236b", "metadata": {}, "outputs": [], "source": ["current_date_time = datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "id": "28d08a25-9a94-4c10-be22-a94f59ee3ab7", "metadata": {}, "outputs": [], "source": ["dataset[\"computed_at\"] = current_date_time"]}, {"cell_type": "code", "execution_count": null, "id": "58f5e1e4-3897-436b-b103-0391061dfc98", "metadata": {}, "outputs": [], "source": ["dataset.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "ac127fde-f7ab-480c-8c37-0a358ca58cb4", "metadata": {}, "outputs": [], "source": ["kwargs_output = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"ars_transfer_stockouts\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"sto_date\", \"type\": \"date\", \"description\": \"NA\"},\n", "        {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"backend_outlet_id\", \"type\": \"bigint\", \"description\": \"NA\"},\n", "        {\"name\": \"backend_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"frontend_outlet_id\", \"type\": \"bigint\", \"description\": \"NA\"},\n", "        {\"name\": \"frontend_facility_id\", \"type\": \"bigint\", \"description\": \"NA\"},\n", "        {\"name\": \"frontend_name\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"count_active_item_ids_in_run\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"count_oos_item_ids_in_run\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"count_act_items\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"count_temp_act_items\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"count_act_and_temp_act_items\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"total_backend_inv\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"total_frontend_inv\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"total_sto_quantity\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"run_type\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"mode\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"total_weight\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"indent_qty\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"inward_drop\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"storage_drop\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"truck_load_drop\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\n", "            \"name\": \"picking_capacity_quantity_drop\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"NA\",\n", "        },\n", "        {\"name\": \"picking_capacity_sku_drop\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"total_picking_caps_qty\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"total_picking_caps_sku\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"total_load_caps\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"total_inw_caps\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"computed_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"sto_date\",\n", "        \"run_id\",\n", "        \"backend_outlet_id\",\n", "        \"frontend_outlet_id\",\n", "        \"run_type\",\n", "    ],\n", "    \"incremental_key\": \"sto_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Table to track ars_transfer_stock_outs\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c50e6559-5ff5-41b8-8da7-1215b564d6a2", "metadata": {}, "outputs": [], "source": ["pb.to_trino(dataset, **kwargs_output)"]}, {"cell_type": "code", "execution_count": null, "id": "026ad8da-a2a9-4af9-9bdb-adf767b6e59f", "metadata": {}, "outputs": [], "source": ["print(\"Pushing Complete !! COOLs\")"]}, {"cell_type": "code", "execution_count": null, "id": "68200aff-ac81-45a7-b766-67f7c68ee3e9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}