{"cells": [{"cell_type": "code", "execution_count": null, "id": "ac31f858-b018-4654-8593-17029f6f4eae", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "908ebfc4-ec93-4176-9085-bf68f310a2d6", "metadata": {}, "outputs": [], "source": ["## New table\n", "redshift_schema_name = \"metrics\"\n", "redshift_table_name = \"fleet_costs_push_v1\"\n", "\n", "# Connecting with Redshift\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "a7c2ea71-f2d0-4e6b-97be-31a9a8f696f0", "metadata": {}, "outputs": [], "source": ["current_date = datetime.datetime.now(timezone(\"Asia/Kolkata\"))\n", "current_day = datetime.datetime.now(timezone(\"Asia/Kolkata\")).strftime(\"%A\")\n", "\n", "# current year and month\n", "current_year = current_date.year\n", "current_month = current_date.month\n", "\n", "# previous year and month\n", "prev_year = (current_date - pd.DateOffset(months=1)).year\n", "prev_month = (current_date - pd.DateOffset(months=1)).month"]}, {"cell_type": "code", "execution_count": null, "id": "9ef1cb9d-bf6e-4e77-b9b0-69a2de51c396", "metadata": {}, "outputs": [], "source": ["current_day"]}, {"cell_type": "code", "execution_count": null, "id": "7341c7c6-3876-4bbc-b724-4967420c9920", "metadata": {}, "outputs": [], "source": ["# # Add sheet id to month_dict\n", "# month_dict = {\n", "#     \"2023-02\": \"1nzr3_Ves0DQOhXihtDeySyFWV1moXIh4g5rcfVv3ss8\",\n", "#     \"2023-03\": \"1yYIYr0AofYxWL2cmjQcgO7jL1iVowS7jRqPumjLRb0o\",\n", "#     \"2023-04\": \"1v_Auvh7J31Cj8joqjsNN6Brw3MetYqbqO1JSneV6pEM\",\n", "#     \"2023-05\": \"1RjTJzkEVD6BwTFHjn308LLUpk2Q5o3Y2HlqyaMTkGpI\",\n", "# }"]}, {"cell_type": "code", "execution_count": null, "id": "aedd4b5b-e0d1-492c-acbb-37cb8f30cde3", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1qVU-fnKHb2B5Uaq_Hu1_CxIcxa8RUOEdPf_qAcPEHsg\"\n", "sheet_name = \"links\"\n", "links_df1 = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "e9e7aafc-9b85-48bd-bf38-f6c3c94949a2", "metadata": {}, "outputs": [], "source": ["links_df = links_df1.set_index(links_df1[\"year_month\"], drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "09b0d11d-2301-44d9-b864-1f604f8bd25e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3a71bd84-af0f-4346-a92c-dafada7a14cf", "metadata": {}, "outputs": [], "source": ["def main_funct(year, month, sheet_id):\n", "\n", "    df = pb.from_sheets(\n", "        sheetid=sheet_id, sheetname=\"Master Sheet PAN India Grocery + F&V\"\n", "    )\n", "    # df = pd.read_csv('april.csv')\n", "\n", "    df.columns = df.iloc[0]\n", "    df.drop(df.index[0], inplace=True)\n", "\n", "    if year == 2022 and month == 10:\n", "\n", "        df.insert(loc=8, column=\"Vehicle no.\", value=np.nan)\n", "        # df[] = np.nan\n", "\n", "    df.drop(\n", "        [\n", "            \"Zone\",\n", "            \"Updated By\",\n", "            \"ES SAP code\",\n", "            # \"Kms Slab (as per LOI)\",\n", "            \"Remarks (Reverse/IT/Infra/Setup/Closure)\",\n", "            # \"Fixed Rate (As per contract/LOI)\",\n", "            # \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "            # \"No entry & Others\",\n", "            # \"No Entry cost\",\n", "            \" \",\n", "            \"\",\n", "        ],\n", "        axis=1,\n", "        errors=\"ignore\",\n", "        inplace=True,\n", "    )\n", "    df.dropna(axis=1, how=\"all\")\n", "    df = df.loc[:, df.columns.notna()]\n", "\n", "    df[\"Function\"] = np.where(\n", "        df[\"Function\"] == \"Express Store\",\n", "        \"Grocery\",\n", "        np.where(\n", "            df[\"Function\"] == \"Express Store - FNV\",\n", "            \"FNV\",\n", "            np.where(\n", "                df[\"Function\"] == \"Express Store - Perishable\", \"Perishable\", \"Infra\"\n", "            ),\n", "        ),\n", "    )\n", "    df.rename(columns={\"Function\": \"category\"}, inplace=True)\n", "\n", "    pivot_cols = [\n", "        \"BE Facility ID\",\n", "        \"category\",\n", "        \"Source FC\",\n", "        \"DS/DP/FC Name\",\n", "        \"Outlet ID\",\n", "        \"Vendor Name\",\n", "        \"Vehicle no.\",\n", "        \"Fixed/Adhoc\",\n", "        \"Vehicle Size\",\n", "        \"Fixed Working Hours\",\n", "        \"No. of vehicles (MTD)\",\n", "        \"Daily/trip Cost \",\n", "        \"MTD Fixed cost\",\n", "        \"MTD adhoc cost\",\n", "        \"Total Cost ( Inc buffer)\",\n", "        \"Kms Slab (as per LOI)\",\n", "        \"Fixed Rate (As per contract/LOI)\",\n", "        \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "        \"No entry & Others\",\n", "    ]\n", "\n", "    columns = df.columns.to_list()\n", "\n", "    for i in pivot_cols:\n", "        columns.remove(i)\n", "    columns\n", "\n", "    check = pivot_cols + columns\n", "    df = df[check]\n", "\n", "    num_days = calendar.monthrange(year, month)[1]\n", "    days = [datetime.date(year, month, day) for day in range(1, num_days + 1)]\n", "    start = len(pivot_cols)\n", "\n", "    end = [i for i in range(14, len(df.columns))]\n", "    column_indices = [i for i in range(start, len(df.columns))]\n", "    new_names = days\n", "    old_names = df.columns[column_indices]\n", "\n", "    df.rename(columns=dict(zip(old_names, new_names)), inplace=True)\n", "\n", "    new_df = df.melt(\n", "        id_vars=pivot_cols, value_vars=days, var_name=\"date_\", value_name=\"date_cost\"\n", "    )\n", "\n", "    # replace epmty strings with nan\n", "    new_df.replace(r\"^\\s*$\", np.nan, regex=True, inplace=True)\n", "\n", "    new_df[\"date_cost\"] = new_df[\"date_cost\"].astype(float)\n", "    new_df[\"Daily/trip Cost \"] = new_df[\"Daily/trip Cost \"].astype(float)\n", "\n", "    new_df[\"date_cost\"] = new_df[\"Daily/trip Cost \"] * new_df[\"date_cost\"]\n", "\n", "    arr_cols = [\n", "        \"BE Facility ID\",\n", "        \"Source FC\",\n", "        \"Outlet ID\",\n", "        \"DS/DP/FC Name\",\n", "        \"category\",\n", "        \"date_\",\n", "        \"date_cost\",\n", "        \"Vendor Name\",\n", "        \"Vehicle no.\",\n", "        \"Fixed/Adhoc\",\n", "        \"Vehicle Size\",\n", "        \"Fixed Working Hours\",\n", "        \"No. of vehicles (MTD)\",\n", "        \"Daily/trip Cost \",\n", "        \"MTD Fixed cost\",\n", "        \"MTD adhoc cost\",\n", "        \"Total Cost ( Inc buffer)\",\n", "        \"Kms Slab (as per LOI)\",\n", "        \"Fixed Rate (As per contract/LOI)\",\n", "        \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "    ]\n", "\n", "    new_df = new_df[arr_cols]\n", "\n", "    new_df.rename(\n", "        columns={\n", "            \"BE Facility ID\": \"source_facility_id\",\n", "            \"Source FC\": \"source_name\",\n", "            \"Outlet ID\": \"destination_outlet_id\",\n", "            \"DS/DP/FC Name\": \"destination_name\",\n", "        },\n", "        inplace=True,\n", "    )\n", "\n", "    # new_df['date_cost'] = new_df['date_cost'].fillna(0)\n", "    new_df.dropna(subset=[\"date_cost\"], inplace=True)\n", "    # new_df['date_'] = pd.to_datetime(new_df['date_'], format='%Y-%m-%d')\n", "\n", "    new_df.columns = new_df.columns.str.strip()\n", "\n", "    wh_city_query = \"\"\" \n", "    select \n", "        pfom.facility_id, \n", "        case \n", "            when co.name ilike '%%budhpur%%' then 'Delhi'\n", "            else co.name\n", "            end as source_city\n", "\n", "    from \n", "        lake_po.physical_facility_outlet_mapping pfom\n", "    left join \n", "        lake_crates.facility cf \n", "        ON cf.id = pfom.facility_id\n", "    inner join \n", "        (   select distinct m.facility_id, c.name \n", "            from lake_retail.console_outlet m \n", "            inner join lake_retail.console_location c \n", "            on c.id = m.tax_location_id\n", "            where business_type_id in (1,12,19,20,21) \n", "        ) co\n", "        on pfom.facility_id = co.facility_id \n", "\n", "    where pfom.ars_active = 1 AND pfom.active = 1\n", "\n", "    group by 1,2\n", "    \"\"\"\n", "\n", "    wh_city_df = pd.read_sql_query(sql=wh_city_query, con=redshift_connection)\n", "    wh_city_df[\"facility_id\"] = wh_city_df[\"facility_id\"].astype(str)\n", "\n", "    ds_city_query = \"\"\" \n", "     SELECT \n", "            DISTINCT cl.name AS destination_city, \n", "            bfom.outlet_id\n", "        FROM lake_po.bulk_facility_outlet_mapping AS bfom\n", "    INNER JOIN \n", "        lake_retail.console_outlet AS co \n", "        ON bfom.outlet_id = co.id AND co.business_type_id = 7       -- business_type_id = 7  means DS\n", "    LEFT JOIN \n", "        lake_po.physical_facility_outlet_mapping AS pfom  \n", "        ON pfom.outlet_id = bfom.outlet_id\n", "    LEFT JOIN \n", "        lake_retail.console_location AS cl \n", "        ON cl.id = pfom.city_id\n", "    \"\"\"\n", "\n", "    ds_city_df = pd.read_sql_query(sql=ds_city_query, con=redshift_connection)\n", "    ds_city_df[\"outlet_id\"] = ds_city_df[\"outlet_id\"].astype(str)\n", "\n", "    new_df = pd.merge(\n", "        new_df,\n", "        wh_city_df,\n", "        how=\"left\",\n", "        left_on=\"source_facility_id\",\n", "        right_on=\"facility_id\",\n", "    )\n", "    new_df = pd.merge(\n", "        new_df,\n", "        ds_city_df,\n", "        how=\"left\",\n", "        left_on=\"destination_outlet_id\",\n", "        right_on=\"outlet_id\",\n", "    )\n", "\n", "    new_df.drop([\"facility_id\", \"outlet_id\"], axis=1, inplace=True)\n", "\n", "    return new_df"]}, {"cell_type": "code", "execution_count": null, "id": "986dfde8-68fb-48bb-a613-857320660410", "metadata": {}, "outputs": [], "source": ["current_year_month = current_date.strftime(\"%Y-%m\")\n", "today = datetime.date.today()\n", "first = today.replace(day=1)\n", "last_month = first - datetime.timedelta(days=1)\n", "previous_year_month = last_month.strftime(\"%Y-%m\")"]}, {"cell_type": "code", "execution_count": null, "id": "9145b99e-979b-496d-9259-519f56dcfcfb", "metadata": {}, "outputs": [], "source": ["sheet_id1 = links_df[\"sheet_id\"][current_year_month]\n", "sheet_id2 = links_df[\"sheet_id\"][previous_year_month]"]}, {"cell_type": "code", "execution_count": null, "id": "0ff38105-de62-4faa-9dc4-1ae1078a0b9c", "metadata": {}, "outputs": [], "source": ["current_df1 = main_funct(year=current_year, month=current_month, sheet_id=sheet_id1)\n", "if current_month == 1:\n", "    current_df2 = main_funct(year=prev_year, month=12, sheet_id=sheet_id2)\n", "else:\n", "    current_df2 = main_funct(\n", "        year=current_year, month=current_month - 1, sheet_id=sheet_id2\n", "    )\n", "\n", "current_df = current_df1.append(current_df2, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f703f6c1-2915-45f8-b8bd-3adcc892a6cd", "metadata": {}, "outputs": [], "source": ["current_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "84e54817-313c-4440-bcb6-26da79096917", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": redshift_schema_name,\n", "    \"table_name\": redshift_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"source_facility_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"source facility id\",\n", "        },\n", "        {\"name\": \"source_name\", \"type\": \"varchar\", \"description\": \"source name\"},\n", "        {\n", "            \"name\": \"destination_outlet_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination outlet id\",\n", "        },\n", "        {\n", "            \"name\": \"destination_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination name\",\n", "        },\n", "        {\"name\": \"category\", \"type\": \"varchar\", \"description\": \"category\"},\n", "        {\"name\": \"date_\", \"type\": \"varchar\", \"description\": \"Date\"},\n", "        {\"name\": \"date_cost\", \"type\": \"float\", \"description\": \"cost for the date\"},\n", "        {\"name\": \"Vendor Name\", \"type\": \"varchar\", \"description\": \"Vendor Name\"},\n", "        {\"name\": \"Vehicle no.\", \"type\": \"varchar\", \"description\": \"Vehicle no.\"},\n", "        {\"name\": \"Fixed/Adhoc\", \"type\": \"varchar\", \"description\": \"Fixed/Adhoc\"},\n", "        {\"name\": \"Vehicle Size\", \"type\": \"varchar\", \"description\": \"Vehicle Size\"},\n", "        {\n", "            \"name\": \"Fixed Working Hours\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Fixed Working Hours\",\n", "        },\n", "        {\n", "            \"name\": \"No. of vehicles (MTD)\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"No. of vehicles (MTD)\",\n", "        },\n", "        {\n", "            \"name\": \"Daily/trip Cost\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Daily/trip Cost\",\n", "        },\n", "        {\"name\": \"MTD Fixed cost\", \"type\": \"varchar\", \"description\": \"MTD Fixed cost\"},\n", "        {\"name\": \"MTD adhoc cost\", \"type\": \"varchar\", \"description\": \"MTD adhoc cost\"},\n", "        {\n", "            \"name\": \"Total Cost ( Inc buffer)\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total Cost ( Inc buffer)\",\n", "        },\n", "        {\n", "            \"name\": \"Kms Slab (as per LOI)\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Kms Slab (as per LOI)\",\n", "        },\n", "        {\n", "            \"name\": \"Fixed Rate (As per contract/LOI)\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Fixed Rate (As per contract/LOI)\",\n", "        },\n", "        {\n", "            \"name\": \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "        },\n", "        {\"name\": \"source_city\", \"type\": \"varchar\", \"description\": \"source city\"},\n", "        {\n", "            \"name\": \"destination_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination city\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"source_facility_id\",\n", "        \"destination_outlet_id\",\n", "        \"category\",\n", "        \"Fixed/Adhoc\",\n", "        \"date_\",\n", "    ],  # list\n", "    \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"date_\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"Base table for fleet costs\"\"\",\n", "}\n", "\n", "print(\"pushing to redshift\")\n", "\n", "pb.to_redshift(current_df, **kwargs)\n", "\n", "print(\"Complete!!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}