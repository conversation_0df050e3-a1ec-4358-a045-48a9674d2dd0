alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: fleet_cost_base_push_v1
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters:
    end_date: ''
    start_date: ''
owner:
  email: <EMAIL>
  slack_id: U04JZTM4FC7
path: povms/fleet/etl/fleet_cost_base_push_v1
paused: false
project_name: fleet
schedule:
  end_date: '2024-03-07T00:00:00'
  interval: 0 0,14 * * *
  start_date: '2023-07-21T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 5
pool: povms_pool
