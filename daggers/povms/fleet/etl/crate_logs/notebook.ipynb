{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import pytz\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "32c2a860-3691-4e12-8a0f-671993ae844a", "metadata": {}, "source": ["### Crate Logs"]}, {"cell_type": "code", "execution_count": null, "id": "2de66b18-8261-48cd-a41c-8013acde7ee5", "metadata": {"tags": []}, "outputs": [], "source": ["logs_query = f\"\"\"\n", "\n", "select \n", "    c.crate_id,\n", "    cl.created_at as used_at, \n", "--    cl.new_user as used_by, \n", "    cl.new_facility_id as used_in_facility, \n", "    cstat.name as status, \n", "    ennew.entity_id as use_ref_id, \n", "    ennew.entity_type as use_ref_type\n", "       \n", "from crates.crate c \n", "    join crates.crate_log cl on c.id = cl.crate_id\n", "    join crates.crate_entity ennew on ennew.id = cl.new_crate_entity_id\n", "    join crates.crate_status cstat on cstat.id = cl.new_status_id\n", "    \n", "where \n", "cl.created_at >= current_date - interval '13' Day\n", "and cl.created_at < current_timestamp\n", "and cl.is_success = 1\n", "\n", "\"\"\"\n", "\n", "logs_df = pd.read_sql_query(sql=logs_query, con=trino_connection)\n", "logs_df = logs_df.drop_duplicates()\n", "logs_df[\"used_at\"] = pd.to_datetime(logs_df[\"used_at\"])\n", "logs_df[\"used_at\"] = logs_df[\"used_at\"] + pd.Timedelta(hours=5, minutes=30)\n", "logs_df = logs_df.sort_values([\"crate_id\", \"used_at\"]).reset_index(drop=True)\n", "logs_df"]}, {"cell_type": "code", "execution_count": null, "id": "ec6a0bca-6f86-412f-bfcc-4dee3424a8c3", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"crate_logs\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"crate_id\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"-\",\n", "        },\n", "        {\"name\": \"used_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"-\"},\n", "        {\"name\": \"used_in_facility\", \"type\": \"INTEGER\", \"description\": \"-\"},\n", "        {\"name\": \"status\", \"type\": \"VARCHAR\", \"description\": \"-\"},\n", "        {\"name\": \"use_ref_id\", \"type\": \"VARCHAR\", \"description\": \"-\"},\n", "        {\"name\": \"use_ref_type\", \"type\": \"INTEGER\", \"description\": \"-\"},\n", "    ],\n", "    \"primary_key\": ([\"used_at\"]),\n", "    \"partition_key\": [\"used_at\"],\n", "    # \"incremental_key\": \"product_type_id\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"-\",\n", "}\n", "\n", "pb.to_trino(data_obj=logs_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "24eb91d1-90d1-4567-97cf-9a551c5dd1ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}