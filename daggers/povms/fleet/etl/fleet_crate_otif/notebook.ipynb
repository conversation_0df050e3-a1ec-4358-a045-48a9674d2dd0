{"cells": [{"cell_type": "code", "execution_count": null, "id": "635d0400-dcce-4bc2-908a-f0928eaea53d", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "9a5b60be-f59c-4fa8-a08f-fcd10f3529cb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "6e6f9c7c-1ac9-400d-a1f5-7d81056bea16", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b172f9f4-a943-487a-bb2b-842f044ed0af", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "695d908a-bfd7-444e-8ea2-4cdd2fa1f7e9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f8d8bead-654c-4c26-a365-49751fb88a1f", "metadata": {}, "outputs": [], "source": ["transit_query = f\"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   --type,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "   \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          --type,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           --n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "    GROUP BY 1,2,3,4,5,6,7,t.metadata\n", "    ),\n", "        \n", "docs AS\n", "    (\n", "    SELECT  t.id AS consignment_id,\n", "            count(DISTINCT d.external_id) AS num_invoices,\n", "            count(DISTINCT c.external_id) AS total_crates_dispatched\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "    JOIN transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND c.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "    GROUP BY 1\n", "    ),\n", "   \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    ),\n", "       \n", "\n", "final_base as\n", "    (\n", "    SELECT base.consignment_id,\n", "           tm.trip_id,\n", "           cast((tm.truck_handshake + interval '330' MINUTE) as date) AS consignment_date,\n", "           CASE\n", "               WHEN facility_id = 1743 THEN 264\n", "               WHEN facility_id = 4306 THEN 1983\n", "               ELSE facility_id\n", "           END AS facility_id,\n", "           --facility_name,\n", "           ds_outlet_id,\n", "           ds_outlet_name,\n", "           truck_number,\n", "           truck_type,\n", "           tm.trip_state,\n", "           current_state AS consignment_state,\n", "           num_invoices,\n", "           total_crates_dispatched,\n", "           (dispatch_time + interval '240' MINUTE) AS reporting_threshold,\n", "           (dispatch_time + interval '285' MINUTE) AS loading_start_threshold,\n", "           \n", "           (dispatch_time + interval '270' MINUTE) AS scheduled_truck_arrival,\n", "           (tm.truck_entry_wh + interval '330' MINUTE) AS truck_entry_wh,\n", "           (tm.truck_handshake + interval '330' MINUTE) AS truck_handshake,\n", "           (loading_start + interval '330' MINUTE) AS loading_start,\n", "           (ready_for_dispatch + interval '330' MINUTE) AS ready_for_dispatch,\n", "           (enroute + interval '330' MINUTE) AS enroute,\n", "           (dispatch_time + interval '330' MINUTE) AS scheduled_dispatch_time,\n", "           (dispatch_time + interval '345' MINUTE) AS exp_dispatch_time,\n", "           (ds_reached + interval '330' MINUTE) AS ds_reached\n", "           \n", "    FROM base\n", "    left join docs on docs.consignment_id = base.consignment_id\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    LEFT JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "    ),\n", "    \n", "time_gaps_base as\n", "    (\n", "    select  final_base.*,\n", "            co.id as wh_outlet_id,\n", "            co.name as wh_outlet_name,\n", "    date_diff('minute', loading_start, ready_for_dispatch) as loading_time_at_wh,\n", "    date_diff('minute', exp_dispatch_time, ready_for_dispatch) as dispatch_delay_min,\n", "    \n", "    --case when return_time_of_previous_trip > reporting_threshold then 'reporting_delay' else 'reporting_ontime' \n", "    --end as vehicle_report_status,\n", "    \n", "    case when loading_start > loading_start_threshold then 'load_start_delay' else 'load_start_ontime' \n", "    end as load_start_status\n", "    \n", "    from final_base\n", "    left join retail.console_outlet co on co.facility_id = final_base.facility_id and business_type_id in (1,12) and name not like '%%HOT%%' and name <> 'K3 T K4'\n", "    ),\n", "\n", "last_trip as\n", "    (\n", "    select  install_ts,\n", "            trip_id,\n", "            truck_number,\n", "            lag(trip_id,1) over (partition by truck_number order by install_ts) as last_trip_id\n", "    from transit_server.transit_projection_trip \n", "    where insert_ds_ist >= cast((CURRENT_DATE - interval '33' DAY) as varchar)\n", "    and state not in ('EXPIRED','CANCELLED')\n", "    )\n", "    \n", "select  base.*,\n", "        last_trip_id,\n", "        (tet.actual_ts + interval '330' minute) as complete_time_last_trip,\n", "        case when dispatch_delay_min <= 0 then 'dispatch_ontime' else 'dispatch_delay' \n", "        end as dispatch_delay_status,\n", "        case when trim(co.location)='Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "        \n", "        CAST(scheduled_dispatch_time AS time) as sch_dispatch_time,\n", "        CAST(scheduled_dispatch_time AS date) as sch_dispatch_date,\n", "        (CASE\n", "              WHEN hour(CAST(truck_handshake AS time)) < 12 THEN 'Morning'\n", "              ELSE 'Evening'\n", "        END) AS shift\n", "        --(case when vehicle_report_status = 'reporting_ontime' and load_start_status = 'load_start_ontime' and dispatch_delay_min <= 0 \n", "        --     then 'perfect_trip' end) as trip_success\n", "        \n", "\n", "from time_gaps_base base\n", "left join lake_retail.console_outlet co on co.id = base.ds_outlet_id\n", "left join last_trip lt on base.trip_id=lt.trip_id\n", "left join transit_server.transit_projection_trip_event_timeline tet on tet.trip_id = lt.last_trip_id and event_type = 'COMPLETED'\n", " and tet.lake_active_record and insert_ds_ist >= cast((current_date - interval '33' day) as varchar)\n", "\n", "where UPPER(wh_outlet_name) not like '%%PC%%'\n", "  and trip_state <> 'CANCELLED'\n", "  --and con_type = 'Grocery'\n", "  and truck_type not like '%%Reefer%%'\n", "  and scheduled_dispatch_time is not null\n", "         \n", "\"\"\"\n", "\n", "frwd_df = read_sql_query(transit_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "46117262-c0d3-48f1-a2f3-6d7f6aca0de1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7a6a23a4-25d3-413a-8699-c5012efd7991", "metadata": {}, "outputs": [], "source": ["frwd_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "42e17cbd-5f2b-43f7-9929-19d11ad87ad2", "metadata": {}, "outputs": [], "source": ["frwd_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f2b09fc1-dc8a-41e7-aa4a-c385dbc47d93", "metadata": {}, "outputs": [], "source": ["# x=transit_df.groupby(['wh_outlet_name','consignment_date'], as_index=False)['trip_id'].nunique()\n", "# x.head(60)"]}, {"cell_type": "code", "execution_count": null, "id": "2690fc15-8996-494c-a7f6-49b8b4d44606", "metadata": {}, "outputs": [], "source": ["# frwd_df[frwd_df[\"facility_id\"] == 2078].consignment_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "9b150365-41f3-447a-8d03-6906af066193", "metadata": {}, "outputs": [], "source": ["# x = frwd_df[frwd_df['wh_outlet_name']=='Bengaluru B3 - Feeder']\n", "# x.groupby(['consignment_date'], as_index=False).trip_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "4f420d9b-b344-4bf7-b5a6-c162733ec11a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7f6ae3b6-b8ae-4c80-8c0d-dd1d24cafbf1", "metadata": {}, "outputs": [], "source": ["rev_transit_query = \"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   --type,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND t.type = 'REVERSE_CONSIGNMENT' \n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "   \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          --type,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           co2.outlet_id as source_outlet_id,\n", "           n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "      \n", "      left join \n", "      (     select distinct logistic_node_id, outlet_id\n", "            from retail.console_outlet_logistic_mapping a\n", "            join retail.console_outlet b\n", "            on a.outlet_id = b.id\n", "            where b.active = 1\n", "            and a.active = TRUE\n", "            and a.id not in (611, 1538)\n", "      ) as co2 \n", "      ON co2.logistic_node_id = cast(t.source_store_id as int)  \n", "        \n", "        \n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "      AND t.type = 'REVERSE_CONSIGNMENT' \n", "    GROUP BY 1,2,3,4,5,6,7,8,t.metadata\n", "    )\n", "   \n", "\n", "SELECT distinct tm.trip_id as last_trip_id,\n", "        --base.consignment_id,\n", "        --facility_name,\n", "        --ds_outlet_name,\n", "    max(ds_reached + interval '330' MINUTE) AS backend_reached_at,\n", "    max(unloading_start + interval '330' MINUTE) AS unloading_start_wh,\n", "    max(unloading_completed + interval '330' MINUTE) AS unloading_completed_wh\n", "    \n", "FROM base\n", "left JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "left join pos on base.consignment_id=pos.csmt_id\n", "where tm.trip_id is not null\n", "group by 1\n", "\n", "\"\"\"\n", "\n", "\n", "rev_transit_df = read_sql_query(rev_transit_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "49dd4bf4-aea0-4ac8-ac82-a888a8059906", "metadata": {}, "outputs": [], "source": ["rev_transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5e63a702-85e5-461b-a58f-4c7846ef6627", "metadata": {}, "outputs": [], "source": ["rev_transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f9069b02-a8d9-4e4a-99b1-147a2cfd12fd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a1885278-cbff-485b-ae97-245dbaf225a6", "metadata": {}, "outputs": [], "source": ["transit = frwd_df.merge(rev_transit_df, how=\"left\", on=[\"last_trip_id\"])\n", "transit.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3fed1ca1-f22d-4e59-8d5a-94524c77044d", "metadata": {}, "outputs": [], "source": ["transit.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7612e6de-76c3-405c-ace9-94299ef44acf", "metadata": {}, "outputs": [], "source": ["transit.rename(\n", "    columns={\n", "        \"backend_reached_at\": \"last_trip_wh_checkin\",\n", "        \"unloading_start_wh\": \"last_unloading_start_wh\",\n", "        \"unloading_completed_wh\": \"last_unloading_completed_wh\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "461b1820-74e1-4f7c-8d00-2e11871663ca", "metadata": {}, "outputs": [], "source": ["transit_df = sqldf(\n", "    \"\"\"\n", "    \n", "select  *,\n", "        coalesce(last_trip_wh_checkin,complete_time_last_trip) as last_check_in,\n", "        \n", "        case when coalesce(last_trip_wh_checkin,complete_time_last_trip) > reporting_threshold then 'reporting_delay' else 'reporting_ontime' end as vehicle_report_status,\n", "        (strftime('%s', coalesce(last_trip_wh_checkin,complete_time_last_trip)) - strftime('%s', reporting_threshold)) / 60 as reporting_delay_min\n", "\n", "from transit\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "15089bd7-7133-4b12-b78c-eda1b218f39d", "metadata": {}, "outputs": [], "source": ["transit_df.drop(columns=[\"complete_time_last_trip\", \"last_trip_wh_checkin\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "51853d11-c90a-4068-bf74-7314b6a5e4a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1b49c665-eff8-4654-985e-b6cf0a0f9d29", "metadata": {}, "source": ["### Sortation Data"]}, {"cell_type": "code", "execution_count": null, "id": "21e48de0-617f-4226-9774-23a8d8beeaa1", "metadata": {}, "outputs": [], "source": ["## B3 Sortation Data\n", "\n", "sorted_crates_blr = f\"\"\"\n", "\n", "with bnglr_packaging_base_cte as\n", "    (\n", "    select distinct\n", "        date(e.created_at - interval '60' minute) as date,\n", "        case when hour(e.created_at) between 1 and 12 then 'Morning' else 'Evening' end as shift,\n", "        e.created_at,\n", "        e.outlet_id,\n", "        co.name as outlet_name,\n", "        json_extract_scalar(e.meta,'$.demand_id') as demand_id,\n", "        json_extract_scalar(e.meta,'$.picking_zone_identifier') as zone_id,\n", "        json_extract_scalar(json_extract(pl.meta,'$.outbound_demand_details'),'$.destination_outlet_id') as ds_outlet_id,\n", "        e.id as entity_id,\n", "        cast(e.external_entity_id as int) as pick_list_id,\n", "        a.state as pkg_state,\n", "        mc.state as sortation_state,\n", "        opec.container_id as container_id,\n", "        opc.container_id as dispatch_container_id,\n", "        a.entity_container_id,\n", "        date_parse(cast(json_extract(json_extract(pcm.meta,'$.drop_details'),'$.drop_ts') as varchar) ,'%%Y-%%m-%%dT%%H:%%i:%%s.%%fZ') as pdz_drop_ts,\n", "        cmt.type,\n", "        (opec.scheduled_time + interval '330' minute) as scheduled_dispatch_time,\n", "        (opec.scheduled_time + interval '270' minute) packaging_start_cut_off_time,\n", "        (opec.scheduled_time + interval '285' minute) packaging_cut_off_time,\n", "        (opec.scheduled_time + interval '300' minute) sortation_cut_off_time,\n", "        (opec.scheduled_time + interval '315' minute) sortation_cut_off_time_15_mins,\n", "        a.created_at as packaging_started_at,\n", "        a.updated_at as packaged_at,\n", "        mc.updated_at as sorted_at\n", "    \n", "    from wms.ob_pkg_entity e \n", "    join wms.ob_pkg_entity_container opec on opec.entity_id = e.id \n", "    join retail.console_outlet co on co.id=e.outlet_id and co.active=1 and co.business_type_id in (1,12) \n", "    join wms.pick_list_container_mapping pcm on pcm.pick_list_id = cast(e.external_entity_id as int) and pcm.container_id=opec.container_id \n", "    and pcm.created_at >= cast((current_date - interval '16' day) as timestamp)\n", "    join wms.pick_list pl on pl.id = pcm.pick_list_id and pl.insert_ds_ist  >= cast((current_date - interval '16' day) as varchar)\n", "    join wms.ob_pkg_activity a on a.entity_id=e.id AND a.entity_container_id = opec.id and a.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "    join wms.ob_pkg_outbound_container opc on opc.activity_id = a.id and opc.insert_ds_ist  >= cast((current_date - interval '16' day) as varchar)\n", "    left join wms.migration_container mc on json_extract_scalar(mc.meta,'$.demand_id') = json_extract_scalar(e.meta,'$.demand_id') and mc.container_id = opc.container_id\n", "    and mc.insert_ds_ist >= cast((current_date - interval '16' day) as varchar) and opec.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "    left join wms.container_migration_task cmt on cmt.id = mc.container_migration_task_id and cmt.insert_ds_ist > cast((current_date - interval '16' day) as varchar)\n", "    and cmt.type = 'DISPATCH_SORTATION'\n", "    where e.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "      and opec.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "      and e.created_at between (cast(current_date as timestamp) - interval '16' day + interval '60' minute) and (cast(current_date as timestamp)+interval '1' day + interval '60' minute)\n", "      and e.created_at  >= (cast((current_date - interval '16' day) as timestamp) + interval '60' minute)\n", "      and pcm.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "      --and e.outlet_id = 4170\n", "    )\n", "\n", "select  distinct date,\n", "        shift,\n", "        outlet_id,\n", "        outlet_name,\n", "        scheduled_dispatch_time,\n", "        max(sorted_at) as sorted_at,\n", "        sortation_cut_off_time,\n", "        cast(scheduled_dispatch_time as date) as dispatch_date,\n", "        cast(scheduled_dispatch_time as time) as dispatch_time,\n", "        cast(ds_outlet_id as int) as ds_outlet_id,\n", "        dispatch_container_id\n", "from bnglr_packaging_base_cte\n", "where pkg_state = 'COMPLETED' and sortation_state = 'DROPPED' and type = 'DISPATCH_SORTATION'\n", "group by 1,2,3,4,5,7,8,9,10,11\n", "\n", "\"\"\"\n", "\n", "sorted_blr_df = read_sql_query(sorted_crates_blr, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "eb4801a0-3107-4df8-ba30-9f728c1aaafe", "metadata": {}, "outputs": [], "source": ["sorted_blr_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "98ef59de-e703-48ad-b3be-0a6e03070e53", "metadata": {}, "outputs": [], "source": ["sorted_blr_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f6dc0c57-e20a-4e34-917d-2ab435cc844c", "metadata": {}, "outputs": [], "source": ["sorted_blr_df[\"scheduled_dispatch_time\"] = pd.to_datetime(sorted_blr_df[\"scheduled_dispatch_time\"])\n", "sorted_blr_df[\"sorted_at\"] = pd.to_datetime(sorted_blr_df[\"sorted_at\"])\n", "sorted_blr_df[\"sortation_cut_off_time\"] = pd.to_datetime(sorted_blr_df[\"sortation_cut_off_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "0952c238-a6ab-48bd-a5ca-46bfac0f4216", "metadata": {}, "outputs": [], "source": ["sorted_blr_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b41c0234-ff91-41e0-ad80-bc4547bbf929", "metadata": {}, "outputs": [], "source": ["sorted_df = sorted_blr_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e8ad1f69-8660-4422-9110-bbbe971232ce", "metadata": {}, "outputs": [], "source": ["# ## Rest Warehouses Sortation Data\n", "\n", "# sorted_crates_rest = f\"\"\"\n", "\n", "\n", "#     select  distinct date(sto.created_at) as date,\n", "#             case when hour(sto.created_at) between 1 and 12 then 'Morning' else 'Evening' end as shift,\n", "#             cmt.outlet_id,\n", "#             co.name as outlet_name,\n", "#             (oc.dispatch_time + interval '330' minute) as scheduled_dispatch_time,\n", "#             max(mc.updated_at) as sorted_at,\n", "#             (oc.dispatch_time + interval '300' minute) sortation_cut_off_time,\n", "#             cast((oc.dispatch_time + interval '330' minute) as date) as dispatch_date,\n", "#             cast((oc.dispatch_time + interval '330' minute) as time) as dispatch_time,\n", "#             destination_outlet_id as ds_outlet_id,\n", "#             mc.container_id as dispatch_container_id\n", "\n", "#     from wms.migration_container mc\n", "#     join wms.container_migration_task cmt on cmt.id=mc.container_migration_task_id and cmt.type = 'DISPATCH_SORTATION'\n", "#     join wms.outbound_container oc on oc.outbound_demand_id=json_extract_scalar(mc.meta,'$.demand_id')\n", "#      and oc.billing_entity_type in ('STO_BULK_PICK_LIST','BACKGROUND_BILLING_TASK')\n", "#      and oc.container_id = mc.container_id\n", "#      and oc.insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "#      and oc.state = 'DISPATCHED'\n", "#     join wms.pick_list pl on oc.outbound_demand_id = json_extract_scalar(json_extract(pl.meta,'$.outbound_demand_details'),'$.demand_id')\n", "#      and pl.zone_identifier not like '%%PACKAGING%%'\n", "#     join wms.pick_list_item pli on pli.pick_list_id = pl.id and pli.insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "#     join wms.pick_list_item_order_mapping plio on plio.pick_list_item_id = pli.id\n", "#     join po.sto sto on sto.id = cast(plio.order_id as int)\n", "#     left join retail.console_outlet co on co.id=cmt.outlet_id and co.active=1 and co.business_type_id in (1,12)\n", "#     where mc.insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "#     and mc.state='DROPPED'\n", "#     and cmt.type='DISPATCH_SORTATION'\n", "#     and cmt.insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "#     and pl.insert_ds_ist >= cast(current_date - interval '10' day as varchar)\n", "#     and sto.created_at >= cast(current_date - interval '10' day as timestamp)\n", "#     and plio.created_at >= cast(current_date - interval '10' day as timestamp)\n", "#     and cmt.outlet_id in (337,481,714,1104,1644,1725,2653,2904,4165,4181,4306,4325,4330,4375,4427,4432,4496,4516)\n", "#     group by 1,2,3,4,5,7,8,9,10,11\n", "\n", "# \"\"\"\n", "\n", "# sorted_rest_df = read_sql_query(sorted_crates_rest, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "f623b6d2-06c1-4a88-8326-b73b5aac4e41", "metadata": {}, "outputs": [], "source": ["# sorted_rest_df[sorted_rest_df[\"outlet_id\"] == 4432].date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "de4cd467-8423-485d-8a86-feafc6d034ba", "metadata": {}, "outputs": [], "source": ["# sorted_df = pd.read_csv('sorted_df.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "e7f67081-43cf-4197-b97e-d67565a082b1", "metadata": {}, "outputs": [], "source": ["# sorted_df['scheduled_dispatch_time'] = pd.to_datetime(sorted_df['scheduled_dispatch_time'])\n", "# sorted_df['sorted_at'] = pd.to_datetime(sorted_df['sorted_at'])\n", "# sorted_df['sortation_cut_off_time'] = pd.to_datetime(sorted_df['sortation_cut_off_time'])"]}, {"cell_type": "code", "execution_count": null, "id": "ab46f1e6-a58c-4cc3-8d64-b8ae76cccc7d", "metadata": {}, "outputs": [], "source": ["# sorted_rest_df[\"scheduled_dispatch_time\"] = pd.to_datetime(\n", "#     sorted_rest_df[\"scheduled_dispatch_time\"]\n", "# )\n", "# sorted_rest_df[\"sorted_at\"] = pd.to_datetime(sorted_rest_df[\"sorted_at\"])\n", "# sorted_rest_df[\"sortation_cut_off_time\"] = pd.to_datetime(\n", "#     sorted_rest_df[\"sortation_cut_off_time\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "41f8e8d3-65cf-4a57-a720-8617afebe84b", "metadata": {}, "outputs": [], "source": ["# sorted_rest_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4c3e9394-58af-49e3-95a0-906dbd4d4af7", "metadata": {}, "outputs": [], "source": ["# sorted_rest_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0a191464-d2cb-4075-abc8-34b97773f7fc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f6530f51-5489-4a25-b4a3-7badaad40cec", "metadata": {}, "outputs": [], "source": ["# sorted_df = pd.concat([sorted_blr_df, sorted_rest_df])\n", "# sorted_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "055930b9-949a-417e-886b-04b7121e256c", "metadata": {}, "outputs": [], "source": ["# sorted_df.to_csv('sorted_df.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f21c72ec-fc36-4c99-940f-f92c59539a8e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a6ad22b8-8d1c-4b32-bde7-ff7a51fc5695", "metadata": {}, "outputs": [], "source": ["# dd = sorted_df[(sorted_df['outlet_id']==714) & (sorted_df['date']=='2024-06-12')]"]}, {"cell_type": "code", "execution_count": null, "id": "b9255c85-b834-40b2-b8c8-3799754cf2bf", "metadata": {}, "outputs": [], "source": ["# dd.to_csv('crate_sort.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8c33283b-8693-48b9-9870-8fcd5444fc51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "30c3227f-34e5-4796-ba1a-d4520bb26d78", "metadata": {}, "source": ["### Loading Data"]}, {"cell_type": "code", "execution_count": null, "id": "2179c1b3-7564-4895-96bb-17e80ace0150", "metadata": {}, "outputs": [], "source": ["loaded_crates = f\"\"\"\n", "\n", "with load_crate_base as\n", "    (\n", "    select  consignment_id,\n", "            c.external_id as crate_id,\n", "            lm1.outlet_id as wh_outlet_id,\n", "            lm2.outlet_id as ds_outlet_id,\n", "            (c.install_ts + interval '330' minute) as crate_loading_time\n", "    from transit_server.transit_consignment_container c\n", "    left join transit_server.transit_consignment t on c.consignment_id = t.id\n", "    left join retail.console_outlet_logistic_mapping lm1 on lm1.logistic_node_id = cast(t.source_store_id as int) and lm1.active = True \n", "    left join retail.console_outlet_logistic_mapping lm2 on lm2.logistic_node_id = cast(t.destination_store_id as int) and lm2.active = True \n", "    where c.insert_ds_ist > cast(current_date - interval '16' day as varchar)\n", "      and t.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "      and c.state <> 'PENDING_AT_SOURCE'\n", "    ),\n", "    \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time + interval '330' minute) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    )\n", "\n", "select  lb.*,\n", "        dispatch_time as scheduled_dispatch_time,\n", "        cast(dispatch_time as date) as dispatch_date,\n", "        cast(dispatch_time as time) as dispatch_time\n", "from pos\n", "join load_crate_base lb on lb.consignment_id = pos.csmt_id\n", "where dispatch_time is not null\n", "         \n", "\"\"\"\n", "\n", "loaded_df = read_sql_query(loaded_crates, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "67fd9fd2-8660-46b8-89d8-24c9ce68bfcf", "metadata": {}, "outputs": [], "source": ["loaded_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "66288ee6-8203-41fa-93bb-16116725ddf2", "metadata": {}, "outputs": [], "source": ["loaded_df[\"crate_loading_time\"] = pd.to_datetime(loaded_df[\"crate_loading_time\"])\n", "loaded_df[\"scheduled_dispatch_time\"] = pd.to_datetime(loaded_df[\"scheduled_dispatch_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d5c2b957-f728-45a5-b394-94d3301dee8b", "metadata": {}, "outputs": [], "source": ["loaded_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "66de7ca8-bb2d-40ca-9478-5974bd7bdcc7", "metadata": {}, "outputs": [], "source": ["loaded_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "985ce063-d6ab-415f-b295-cd118079f40a", "metadata": {}, "outputs": [], "source": ["# loaded_df.to_csv('loaded_df.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "39483531-1480-4c5b-ad34-e98c064f607e", "metadata": {}, "outputs": [], "source": ["loaded_df[loaded_df[\"wh_outlet_id\"] == 4432].dispatch_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "24733c74-13d2-41a4-a9ad-77beca46a16a", "metadata": {}, "outputs": [], "source": ["## Getting Late Loaded Crates\n", "\n", "df1 = sqldf(\n", "    \"\"\"\n", "with base as\n", "    (\n", "    select  sd.date as date,\n", "            shift,\n", "            sd.outlet_id,\n", "            sd.outlet_name,\n", "            count(distinct sd.dispatch_container_id) as total_sorted_crates,\n", "            count(distinct case when crate_loading_time <= datetime(ld.scheduled_dispatch_time, '+15 minutes') then ld.crate_id end) as load_crates_before_sdt,\n", "            count(distinct case when crate_loading_time > datetime(ld.scheduled_dispatch_time, '+15 minutes')  then ld.crate_id end) as load_crates_after_sdt\n", "            --count(distinct case when crate_loading_time > ld.scheduled_dispatch_time and crate_loading_time <= datetime(ld.scheduled_dispatch_time, '+360 minutes') then ld.crate_id end) as load_crates_after_sdt_0_6\n", "\n", "    from sorted_df sd\n", "    left join loaded_df ld on sd.dispatch_container_id = ld.crate_id \n", "     and sd.dispatch_date = ld.dispatch_date\n", "     and sd.dispatch_time = ld.dispatch_time\n", "     and sd.outlet_id = ld.wh_outlet_id\n", "    group by 1,2,3,4\n", "    )\n", "    \n", "select  *,\n", "        load_crates_before_sdt + load_crates_after_sdt as total_loaded_crates,\n", "        --load_crates_before_sdt + load_crates_after_sdt_0_6 as total_loaded_crates_till_sdt_6,\n", "        --total_sorted_crates - (load_crates_before_sdt+load_crates_after_sdt_0_6) as actual_spillover_crates,\n", "        total_sorted_crates - (load_crates_before_sdt+load_crates_after_sdt) as total_spillover_crates\n", "from base\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d590075-7731-4c63-8e4f-d66249653ce5", "metadata": {}, "outputs": [], "source": ["df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "de363772-1055-46da-8f11-af68aa9c90e2", "metadata": {"tags": []}, "outputs": [], "source": ["df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bd09de0e-85aa-42d8-8eaa-a139cdea3848", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "709ee851-0388-4d3a-82f0-15fbd363d480", "metadata": {}, "outputs": [], "source": ["## Getting Late Loaded and Late Sorted Crates count\n", "\n", "df2 = sqldf(\n", "    \"\"\"\n", "\n", "with late_load_crate as\n", "    (\n", "        select  consignment_id,\n", "                crate_id,\n", "                wh_outlet_id,\n", "                crate_loading_time,\n", "                scheduled_dispatch_time,\n", "                dispatch_date,\n", "                dispatch_time\n", "        from loaded_df\n", "        where crate_loading_time > datetime(scheduled_dispatch_time, '+15 minutes') --and crate_loading_time < datetime(scheduled_dispatch_time, '+360 minutes')\n", "    )\n", "\n", "select  sd.date as date,\n", "        shift,\n", "        outlet_id,\n", "        count(distinct case when sorted_at > sortation_cut_off_time then sd.dispatch_container_id end) as late_load_late_sort_crates\n", "\n", "from late_load_crate ld\n", "join sorted_df sd on sd.dispatch_container_id = ld.crate_id \n", " and sd.dispatch_date = ld.dispatch_date\n", " and sd.dispatch_time = ld.dispatch_time\n", " and sd.outlet_id = ld.wh_outlet_id\n", "group by 1,2,3\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7ac6a6e1-29d1-49b3-a820-8aea97dc355a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "edee4cb5-29ee-42a1-8766-6fd6e31032f8", "metadata": {}, "outputs": [], "source": ["## Combining Not Late Sorted Crates to overall data\n", "\n", "df3 = df1.merge(df2, how=\"left\", on=[\"date\", \"shift\", \"outlet_id\"])\n", "df3[\"late_load_early_sort_crates\"] = (\n", "    df3[\"load_crates_after_sdt\"] - df3[\"late_load_late_sort_crates\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "61f9efa2-d94c-4277-867f-4e1876bb2ce0", "metadata": {}, "outputs": [], "source": ["df3.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "9f055db6-7c78-404c-88c5-172e57acec33", "metadata": {}, "outputs": [], "source": ["df3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b55340a5-56ac-467a-b89c-2384092b7b6f", "metadata": {"tags": []}, "outputs": [], "source": ["# df4 = sqldf(\n", "\n", "# \"\"\"\n", "\n", "# with late_load_crate as\n", "#     (\n", "#         select  consignment_id,\n", "#                 crate_id,\n", "#                 wh_outlet_id,\n", "#                 crate_loading_time,\n", "#                 scheduled_dispatch_time,\n", "#                 dispatch_date,\n", "#                 dispatch_time\n", "#         from loaded_df\n", "#         where crate_loading_time > scheduled_dispatch_time and crate_loading_time < datetime(scheduled_dispatch_time, '+360 minutes')\n", "#     ),\n", "\n", "# not_late_sort_but_late_load_crate as\n", "#     (\n", "#         select  sd.date as sto_date,\n", "#                 shift,\n", "#                 sd.outlet_id,\n", "#                 sd.scheduled_dispatch_time,\n", "#                 sd.dispatch_date,\n", "#                 sd.dispatch_time,\n", "#                 sd.dispatch_container_id\n", "\n", "#         from late_load_crate ld\n", "#         join sorted_df sd on sd.dispatch_container_id = ld.crate_id\n", "#          and sd.dispatch_date = ld.dispatch_date\n", "#          and sd.dispatch_time = ld.dispatch_time\n", "#          and sd.outlet_id = ld.wh_outlet_id\n", "#         where sorted_at < sortation_cut_off_time\n", "#     )\n", "\n", "\n", "# --Consignments containing crates that are Not Late Sorted But Late Load Started\n", "\n", "# select  distinct consignment_id\n", "# from not_late_sort_but_late_load_crate ls\n", "# join late_load_crate ll on ls.dispatch_container_id = ll.crate_id\n", "#  and ls.dispatch_date = ll.dispatch_date\n", "#  and ls.dispatch_time = ll.dispatch_time\n", "\n", "\n", "# \"\"\"\n", "\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "adaddda7-efdf-4164-9cf8-8906d81c4ddd", "metadata": {"tags": []}, "outputs": [], "source": ["# df5 = sqldf(\n", "\n", "# \"\"\"\n", "# select  consignment_id,\n", "#         trip_id,\n", "#         sch_dispatch_date,\n", "#         sch_dispatch_time,\n", "#         facility_id,\n", "#         wh_outlet_id,\n", "#         ds_outlet_id,\n", "#         total_crates_dispatched,\n", "#         return_time_of_previous_trip,\n", "#         reporting_threshold,\n", "#         truck_entry_wh,\n", "#         loading_start,\n", "#         loading_start_threshold,\n", "#         ready_for_dispatch,\n", "#         scheduled_dispatch_time,\n", "#         loading_time_at_wh,\n", "#         dispatch_delay_min,\n", "#         vehicle_report_status,\n", "#         load_start_status,\n", "#         shift\n", "# from transit_df\n", "# where consignment_id in (select consignment_id from df4)\n", "\n", "# \"\"\"\n", "\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "2be7f62a-b01e-43d2-954d-0c25b133cb20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cb9095fa-d97b-450a-b663-67eb9e19630d", "metadata": {}, "outputs": [], "source": ["## For crates that were late Loaded but Sorted early, the attribution to how many were Reported Delay or Load Start Delay\n", "\n", "df4 = sqldf(\n", "    \"\"\"\n", "\n", "with late_load_crate as\n", "    (\n", "        select  consignment_id,\n", "                crate_id,\n", "                wh_outlet_id,\n", "                crate_loading_time,\n", "                scheduled_dispatch_time,\n", "                dispatch_date,\n", "                dispatch_time\n", "        from loaded_df\n", "        where crate_loading_time > datetime(scheduled_dispatch_time, '+15 minutes') --and crate_loading_time < datetime(scheduled_dispatch_time, '+360 minutes')\n", "    )\n", "\n", "select  sto_date as date,\n", "        cte.shift,\n", "        cte.outlet_id,\n", "        count(distinct case when load_start_status = 'load_start_delay' and vehicle_report_status = 'reporting_delay' then dispatch_container_id end) as delay_report_and_load_start_delay_crates\n", "\n", "from\n", "    (\n", "    select  ld.consignment_id,\n", "            sd.date as sto_date,\n", "            sd.shift,\n", "            sd.outlet_id,\n", "            sd.scheduled_dispatch_time,\n", "            sd.dispatch_date,\n", "            sd.dispatch_time,\n", "            sd.dispatch_container_id\n", "\n", "    from late_load_crate ld\n", "    join sorted_df sd on sd.dispatch_container_id = ld.crate_id \n", "     and sd.dispatch_date = ld.dispatch_date\n", "     and sd.dispatch_time = ld.dispatch_time\n", "     and sd.outlet_id = ld.wh_outlet_id\n", "    where sorted_at < sortation_cut_off_time\n", "    ) cte\n", "left join transit_df td on td.consignment_id = cte.consignment_id\n", "group by 1,2,3\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b4859d1f-f941-43e2-a3c6-2a574ef7039d", "metadata": {}, "outputs": [], "source": ["df4.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6980f2d5-9f7b-4f67-8d6e-b98b7db1c1f6", "metadata": {}, "outputs": [], "source": ["df4.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "b0950936-19e2-4520-a4e7-2fbc2af83b2e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "77eb7e43-3e8f-4060-852e-cf4b2183f2a4", "metadata": {}, "outputs": [], "source": ["## Combining Not Late Sorted Crates to overall data\n", "\n", "df5 = df3.merge(df4, how=\"left\", on=[\"date\", \"shift\", \"outlet_id\"])\n", "df5[\"ontime_report_and_load_start_delay_crates\"] = (\n", "    df5[\"late_load_early_sort_crates\"] - df5[\"delay_report_and_load_start_delay_crates\"]\n", ")\n", "df5.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2748a518-5b57-4352-a2e9-8ebd29a0dad8", "metadata": {}, "outputs": [], "source": ["df5.shape"]}, {"cell_type": "code", "execution_count": null, "id": "88e6c3fd-5c3d-4127-9a11-2d1b006a0a9b", "metadata": {}, "outputs": [], "source": ["df5.to_csv(\"df5.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "969dbe22-92cc-487c-a094-d0b7428057b8", "metadata": {}, "outputs": [], "source": ["crate_funnel_raw = \"1lmYe9ySCArCiWz6sZTAAXCvsWr1nnjbn6UVTWQSbREk\""]}, {"cell_type": "code", "execution_count": null, "id": "3fc257aa-1b84-428e-8e7d-ec97686dd892", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df5, crate_funnel_raw, \"loading_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "266ace6f-08d7-491e-865f-9c57fbce49ba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "91cf734b-1d48-4f3b-af76-69f721f094ec", "metadata": {}, "source": ["### Trip level Funnel"]}, {"cell_type": "code", "execution_count": null, "id": "a157f250-68ca-4811-a65b-22ca8137d8dd", "metadata": {}, "outputs": [], "source": ["transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "03f61bf9-a7e5-4880-b129-e38d5bf725d3", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ba07ada1-ea76-48be-8044-4ec600a24158", "metadata": {}, "outputs": [], "source": ["## Getting the perfect trips\n", "\n", "df6 = sqldf(\n", "    \"\"\"\n", "\n", "with trip_base as\n", "    (\n", "    select  consignment_date as date,\n", "            shift,\n", "            wh_outlet_id as outlet_id,\n", "            count(distinct trip_id) as total_trips,\n", "            count(distinct case when vehicle_report_status = 'reporting_ontime' then trip_id end) as ontime_report_trips,\n", "            count(distinct case when vehicle_report_status = 'reporting_ontime' and load_start_status = 'load_start_ontime' then trip_id end) as ontime_report_ontime_load_trips,\n", "            count(distinct case when vehicle_report_status = 'reporting_ontime' and load_start_status = 'load_start_ontime' and dispatch_delay_status = 'dispatch_ontime' then trip_id end) as ontime_report_ontime_load_ontime_dispatch_trips,\n", "            avg(loading_time_at_wh) as avg_loading_time\n", "    from transit_df\n", "    group by 1,2,3\n", "    )\n", "\n", "select *\n", "from trip_base\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "adaf94b8-e2cd-4caf-9a27-cf900d468a90", "metadata": {}, "outputs": [], "source": ["df6.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8110e9d3-5cb3-428d-9d8b-940e6bf9759e", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "75e45c47-a9fb-4bea-b56a-c95038c3ee1a", "metadata": {}, "outputs": [], "source": ["# dfa = transit_df[(transit_df['wh_outlet_id']==4170) & (transit_df['consignment_date']=='2024-06-07')]\n", "# # dfa.to_csv('dfa.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f1d9eaaf-9987-4d91-a0b4-ff10dc168b99", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b48189a0-2133-466d-83b2-4d675047defa", "metadata": {}, "outputs": [], "source": ["transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "76e8fb01-5a3a-4a73-ada8-8bb88c16432e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6a4fc11d-c7d3-4fd6-9622-1b69f6434cb1", "metadata": {}, "outputs": [], "source": ["## Getting Loading Fill Rate of Perfect Trips\n", "\n", "df7 = sqldf(\n", "    \"\"\"\n", "\n", "    select  sd.dispatch_date as date,\n", "            case when cast(strftime('%H', sd.dispatch_time) as int) between 1 and 12 then 'Morning' else 'Evening' end as shift,\n", "            sd.outlet_id,\n", "            count(distinct sd.dispatch_container_id) as total_sorted_crates_perfect,\n", "            count(distinct case when crate_loading_time <= datetime(ld.scheduled_dispatch_time, '+15 minutes') then ld.crate_id end) as load_crates_before_sdt_perfect,\n", "            count(distinct case when crate_loading_time > datetime(ld.scheduled_dispatch_time, '+15 minutes')  then ld.crate_id end) as load_crates_after_sdt_perfect\n", "            --count(distinct case when crate_loading_time > ld.scheduled_dispatch_time and crate_loading_time <= datetime(ld.scheduled_dispatch_time, '+360 minutes') then ld.crate_id end) as load_crates_after_sdt_0_6_perfect\n", "    from sorted_df sd\n", "    left join loaded_df ld on sd.dispatch_container_id = ld.crate_id \n", "     and sd.dispatch_date = ld.dispatch_date\n", "     and sd.dispatch_time = ld.dispatch_time\n", "     and sd.outlet_id = ld.wh_outlet_id\n", "    where ld.consignment_id in (select distinct consignment_id from transit_df where vehicle_report_status = 'reporting_ontime' and load_start_status = 'load_start_ontime' and dispatch_delay_status = 'dispatch_ontime')\n", "    group by 1,2,3\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "62b2e908-845d-4848-83de-e453fd4490c8", "metadata": {}, "outputs": [], "source": ["df7.shape"]}, {"cell_type": "code", "execution_count": null, "id": "92265c0a-92f7-4f11-9a1f-728f028df814", "metadata": {}, "outputs": [], "source": ["df7.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "89e73622-c939-4218-a1df-ef0d2f6fcaf6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3800ba8e-6a79-46cc-aafd-2426e70c441b", "metadata": {}, "outputs": [], "source": ["df8 = df6.merge(df7, how=\"left\", on=[\"date\", \"shift\", \"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "4153bc88-be7d-419c-9fdf-4a7da04b300d", "metadata": {}, "outputs": [], "source": ["df8.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bde0c21e-036b-4c1a-a091-413789b0deb7", "metadata": {}, "outputs": [], "source": ["df8.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "de3e7305-b684-4f41-aaa2-8d5ed4597773", "metadata": {}, "outputs": [], "source": ["# df8.to_csv('df8.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "148e5a65-6dc4-4944-8e8f-f209d0c36e97", "metadata": {}, "outputs": [], "source": ["# z = df8[df8['outlet_id']==4170]\n", "# z.groupby([\"date\"], as_index=False)[\"total_trips\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d63e8cee-e3ff-43de-9669-a55148c9a0ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "82c8c56c-49c6-4c6a-bfa5-dec73c4ebf85", "metadata": {}, "outputs": [], "source": ["perfect_funnel_raw = \"1lmYe9ySCArCiWz6sZTAAXCvsWr1nnjbn6UVTWQSbREk\""]}, {"cell_type": "code", "execution_count": null, "id": "82665751-af1f-4bbb-8178-875b9a2e3605", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df8, perfect_funnel_raw, \"dispatch_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "15387fbd-6880-4907-a4fe-e3e25f7f0feb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f7ba205e-d8eb-4103-8618-af92c1c35c1f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c98a9637-308b-47f3-8a4d-b2f9fbcfb0c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4f164585-a72c-464c-8ffb-0a1b62b0c938", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c61b58e8-625d-4496-a232-518d41f94130", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cfd42044-2282-4af8-adef-af7ba3c1285a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e07ac472-c12e-4094-bda2-b714b1da0341", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}