{"cells": [{"cell_type": "code", "execution_count": null, "id": "6fafbda1-abad-4c8e-bf7c-8003bdce0e50", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "import warnings\n", "import math\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "361eda33-10b2-4827-b001-6e5ce44ce622", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "89cfeb81-f559-44a2-bb0e-f0ebcbd1e76b", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(0, max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "markdown", "id": "49125ad9-9b6c-4e1a-bb75-2801068c4173", "metadata": {}, "source": ["#### City Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "892af478-637b-4b88-a93b-018a01761bb8", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "WITH fe_details AS (\n", "    SELECT \n", "        DISTINCT cl.name AS city, \n", "        bfom.outlet_id, \n", "        pfom.facility_id, \n", "        pfom.outlet_name\n", "    FROM lake_po.bulk_facility_outlet_mapping AS bfom\n", "INNER JOIN lake_retail.console_outlet AS co ON bfom.outlet_id = co.id AND co.business_type_id = 7\n", "    LEFT JOIN lake_po.physical_facility_outlet_mapping AS pfom ON pfom.outlet_id = bfom.outlet_id\n", "LEFT JOIN lake_retail.console_location AS cl ON cl.id = pfom.city_id\n", "    WHERE pfom.active = 1 AND pfom.ars_active = 1 AND bfom.active = 1\n", ")\n", "select \n", "    city,\n", "    facility_id\n", "from fe_details\n", "    group by 1,2\n", "\n", "\"\"\"\n", "\n", "city_mapping = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "0bd5d1a9-34f5-4fb1-a895-89cac0a27444", "metadata": {}, "outputs": [], "source": ["city_mapping.head(2)"]}, {"cell_type": "markdown", "id": "075b878a-3ab1-4271-9f6e-17fe0cbb6485", "metadata": {}, "source": ["### Packaged Good Items"]}, {"cell_type": "code", "execution_count": null, "id": "696f9b28-796a-456e-93b3-48258f30b04a", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"with \n", "\n", "fe_details as\n", "    (select ma.facility_id as fe_facility_id, ma.item_id, category\n", "        from metrics.perishable_assortment ma\n", "        \n", "            join (select max(updated_at) as updated_at, item_id, facility_id from metrics.perishable_assortment\n", "                where date(updated_at) between current_date - 8 and current_date\n", "                    group by 2,3\n", "                    ) pa on pa.item_id = ma.item_id and pa.facility_id = ma.facility_id and pa.updated_at = ma.updated_at\n", "    )\n", "    \n", "select distinct rpc.item_id,\n", "        case when l0_id = 1487 then 'FnV'\n", "            when l2_id in (1185,1961,1367,63,1732,1733,1734,1369) then 'Perishable'\n", "            when perishable = 1 then 'Perishable'\n", "            when fd.item_id is not null then 'Perishable' else 'Packaged Goods' end as assortment_type\n", "\n", "                from lake_rpc.product_product rpc\n", "        \n", "                left join (select item_id, l0_id, l2_id from lake_rpc.item_category_details) cd on cd.item_id = rpc.item_id\n", "                left join (select item_id from fe_details) fd on fd.item_id = rpc.item_id\n", "                \n", "                left join (select distinct item_id from lake_rpc.item_tag_mapping where active = true\n", "                    and cast(tag_value as int) = 3 and tag_type_id = 3\n", "                        ) i on i.item_id = rpc.item_id\n", "                    \n", "                    where id in (select max(id) as id from lake_rpc.product_product pp where pp.active = 1 and pp.approved = 1 group by item_id)\n", "                        and assortment_type = 'Packaged Goods' and i.item_id is null\n", "\"\"\"\n", "\n", "item_ids = tuple(np.unique(read_sql_query(query, CON_REDSHIFT)[\"item_id\"]))"]}, {"cell_type": "markdown", "id": "145c8950-9daa-47bf-86ef-cf5e42be1f92", "metadata": {}, "source": ["### Facility Ids"]}, {"cell_type": "code", "execution_count": null, "id": "094a0afc-627b-450a-a4d4-fcbdfd7a6d26", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    select \n", "        distinct facility_id \n", "    from lake_retail.console_outlet where business_type_id in (7)\n", "     and active=1\"\"\"\n", "\n", "frontend_facility = tuple(np.unique(read_sql_query(query, CON_REDSHIFT)[\"facility_id\"]))\n", "\n", "\n", "query = f\"\"\"\n", "    select \n", "        distinct facility_id \n", "    from lake_retail.console_outlet where business_type_id in (1,12,19,20,21)\n", "     and active=1\"\"\"\n", "\n", "backend_facility = tuple(np.unique(read_sql_query(query, CON_REDSHIFT)[\"facility_id\"]))"]}, {"cell_type": "markdown", "id": "36b0f81b-84b7-4df0-8218-4dfe47d347df", "metadata": {}, "source": ["### Grocery Outbound "]}, {"cell_type": "code", "execution_count": null, "id": "4a96dcee-d3c8-456d-94a3-9bf757977e45", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "\n", "with \n", "\n", "fe_details as\n", "    (select ma.facility_id as fe_facility_id, ma.item_id, category\n", "        from metrics.perishable_assortment ma\n", "        \n", "            join (select max(updated_at) as updated_at, item_id, facility_id from metrics.perishable_assortment\n", "                where date(updated_at) between current_date - 8 and current_date\n", "                    group by 2,3\n", "                    ) pa on pa.item_id = ma.item_id and pa.facility_id = ma.facility_id and pa.updated_at = ma.updated_at\n", "    )\n", "    \n", ",temp2 as (select distinct rpc.item_id,\n", "        case when l0_id = 1487 then 'FnV'\n", "            when l2_id in (1185,1961,1367,63,1732,1733,1734,1369) then 'Perishable'\n", "            when perishable = 1 then 'Perishable'\n", "            when fd.item_id is not null then 'Perishable' else 'Packaged Goods' end as assortment_type\n", "\n", "                from lake_rpc.product_product rpc\n", "        \n", "                left join (select item_id, l0_id, l2_id from lake_rpc.item_category_details) cd on cd.item_id = rpc.item_id\n", "                left join (select item_id from fe_details) fd on fd.item_id = rpc.item_id\n", "                \n", "                left join (select distinct item_id from lake_rpc.item_tag_mapping where active = true\n", "                    and cast(tag_value as int) = 3 and tag_type_id = 3\n", "                        ) i on i.item_id = rpc.item_id\n", "                    \n", "                    where id in (select max(id) as id from lake_rpc.product_product pp where pp.active = 1 and pp.approved = 1 group by item_id)\n", "                        and assortment_type = 'Packaged Goods' and i.item_id is null)\n", ", base as\n", "\n", "(select \n", "    zone,\n", "    date(ars_started_at) as date,\n", "    backend_outlet_id,\n", "    backend_facility_name,\n", "    frontend_outlet_id,\n", "    frontend_facility_name,\n", "    backend_facility_id,\n", "    frontend_facility_id,\n", "    a.item_id,\n", "    v2_quantity*weight_in_gm as weight_in_KGs,\n", "    v2_quantity as grocery_outbound\n", "    from \n", "    metrics.ars_truncation_rca_l7_raw_data_v2 a\n", "    inner join lake_rpc.item_details b on a.item_id = b.item_id\n", "where (ars_started_at) between current_date-14 and current_date+1\n", "and a.item_id in (select distinct item_id from temp2))\n", "\n", ",temp as\n", "    (select \n", "    zone,\n", "    date,\n", "    backend_outlet_id,\n", "    backend_facility_name,\n", "    frontend_outlet_id,\n", "    frontend_facility_name,\n", "    backend_facility_id,\n", "    frontend_facility_id,\n", "    sum(weight_in_KGs) as weight_in_KGs,\n", "    sum(grocery_outbound) as grocery_outbound\n", "from base\n", "group by 1,2,3,4,5,6,7,8)\n", "        \n", "\n", ",base1 as (select \n", "    zone,\n", "    date,\n", "    backend_outlet_id,\n", "    backend_facility_name,\n", "    frontend_outlet_id,\n", "    frontend_facility_name,\n", "    backend_facility_id,\n", "    frontend_facility_id,\n", "    weight_in_KGs,\n", "    grocery_outbound,\n", "     avg(weight_in_KGs) over(partition by backend_facility_id,frontend_facility_id\n", "        order by date DESC\n", "        rows between current row and 6 Following) as avg_weight_in_KGs,\n", "    avg(grocery_outbound) over(partition by backend_facility_id,frontend_facility_id\n", "        order by date DESC\n", "        rows between current row and 6 Following) as avg_outbound\n", "    from temp\n", "    where\n", "    backend_facility_id in {backend_facility}\n", "and frontend_facility_id in {frontend_facility})\n", "\n", "select \n", "* from base1 where date between current_date-7 and current_date\n", "    \n", "    \n", "\"\"\"\n", "Grocery_outbound_mapping = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "f18f1454-b8dc-4bd6-99b1-e78bdc93b773", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping.rename(\n", "    columns={\n", "        \"backend_facility_id\": \"sender_facility_id\",\n", "        \"frontend_facility_id\": \"receiver_facility_id\",\n", "        \"frontend_facility_name\": \"receiver_facility_name\",\n", "        \"backend_facility_name\": \"sender_facility_name\",\n", "        \"frontend_outlet_id\": \"receiver_outlet_id\",\n", "        \"backend_outlet_id\": \"sender_outlet_id\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5800649f-95c6-4392-b5c6-15719ff3f4f0", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping.columns"]}, {"cell_type": "code", "execution_count": null, "id": "5e3e51ca-c9b3-4e17-be6e-e3a8829ddb26", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping = Grocery_outbound_mapping[\n", "    [\n", "        \"zone\",\n", "        \"date\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_facility_name\",\n", "        \"weight_in_kgs\",\n", "        \"grocery_outbound\",\n", "        \"avg_weight_in_kgs\",\n", "        \"avg_outbound\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "83bf8c9e-66fa-4bd7-881c-d97e7c442af1", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping.rename(\n", "    columns={\n", "        \"avg_weight_in_kgs\": \"avg_billed_weight\",\n", "        \"avg_outbound\": \"avg_billed_qty\",\n", "        \"weight_in_kgs\": \"total_billed_weight\",\n", "        \"grocery_outbound\": \"total_billed_qty\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1c238aa9-8868-4238-8f83-a347d0b991e6", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping[\"date\"] = pd.to_datetime(\n", "    Grocery_outbound_mapping[\"date\"]\n", ").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "d254841c-df43-4a63-9b0a-7bdd5fdaba3a", "metadata": {}, "outputs": [], "source": ["min_date = str(np.min(Grocery_outbound_mapping[\"date\"]))\n", "max_date = str(np.max(Grocery_outbound_mapping[\"date\"]))"]}, {"cell_type": "markdown", "id": "d02f83f2-f146-412a-aa45-b7dcd0c8b480", "metadata": {}, "source": ["### Projections"]}, {"cell_type": "code", "execution_count": null, "id": "e14221c0-7ef8-4c98-8b36-a2aebefc836c", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    select \n", "       date,\n", "       facility_id,\n", "       carts*grocery_ipo as projection\n", "    from metrics.monthly_cart_projection\n", "where date(date) between date('{min_date}') and date('{max_date}')\n", "and updated_at=(select max(updated_at) from metrics.monthly_cart_projection where date(date) between date('{min_date}') and date('{max_date}'))\"\"\"\n", "\n", "projections = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "61cff5b1-fef6-428e-add2-2f12966ce551", "metadata": {}, "outputs": [], "source": ["projections"]}, {"cell_type": "code", "execution_count": null, "id": "d675a70e-2873-4716-b7f9-f60ec8c41b46", "metadata": {}, "outputs": [], "source": ["np.max(projections[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "35e83636-3eed-49f4-ad2c-f05975d2dff6", "metadata": {}, "outputs": [], "source": ["projections.rename(\n", "    columns={\"outletid\": \"outlet_id\", \"facility_id\": \"receiver_facility_id\"},\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b0f77f3e-eaf1-4657-bf8d-07f191cada21", "metadata": {}, "outputs": [], "source": ["projections = projections[[\"receiver_facility_id\", \"date\", \"projection\"]]\n", "projections[\"date\"] = pd.to_datetime(projections[\"date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "538a139f-55b3-4a10-a293-a254741bb2d3", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping = pd.merge(\n", "    Grocery_outbound_mapping,\n", "    projections,\n", "    on=[\"date\", \"receiver_facility_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "50dd88be-f52c-4c18-9491-31f39308c92d", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping"]}, {"cell_type": "markdown", "id": "1ba5f389-084c-45e6-bdce-314fe0a9f516", "metadata": {}, "source": ["#### Sales from outlet"]}, {"cell_type": "code", "execution_count": null, "id": "59f27610-e125-4db4-a5e8-7a5649945da7", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with \n", "mer_outlet_mapping as (\n", "    select frontend_merchant_id, \n", "    frontend_merchant_name,\n", "    backend_merchant_id, \n", "    backend_merchant_name,\n", "    pos_outlet_id as outlet_id, \n", "    pos_outlet_name as store,\n", "facility_id\n", "    from dwh.dim_merchant_outlet_facility_mapping\n", "    where valid_to_utc >= current_date\n", "    and is_current = True\n", "    and is_mapping_enabled = True\n", ")\n", "SELECT \n", "    o_fe.facility_id,\n", "    date(o.install_ts + interval '5.5 Hours') as order_date,\n", "    sum(quantity-cancelled_quantity) as qty_sold,\n", "    sum((quantity-cancelled_quantity)*selling_price) as gmv    \n", "FROM lake_oms_bifrost.oms_order o\n", "INNER JOIN lake_oms_bifrost.oms_merchant M ON o.MERCHANT_ID = M.ID\n", "INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "LEFT JOIN lake_rpc.item_product_mapping ipm ON oi.product_id = ipm.product_id AND ipm.active=1\n", "INNER JOIN mer_outlet_mapping mom on m.external_id = mom.frontend_merchant_id\n", "left join (select distinct id as outlet_id,facility_id from lake_retail.console_outlet where active=1) o_fe using(outlet_id)\n", "WHERE date(o.install_ts + interval '5.5 Hours') between current_date - 7 and current_date\n", "AND o.current_Status <> 'CANCELLED'\n", "AND (\"type\" IS NULL OR \"type\" IN ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder'))\n", "AND o_fe.facility_id in {frontend_facility}\n", "and item_id in {item_ids}\n", "\n", "GROUP BY 1,2\n", "order by 1,2,3\n", "\"\"\"\n", "\n", "sales = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "09f7dee7-07a2-4cce-ba55-d8ccd6fc36be", "metadata": {}, "source": ["### Truncations"]}, {"cell_type": "code", "execution_count": null, "id": "53d1e4da-2b63-4cf4-9b17-2e127cc26842", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with live_facility_list as\n", "    (select facility_id, min(date(a.created_at)) outbound_start_date\n", "    from lake_ims.ims_order_details a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where \n", "    -- date(a.created_at) <= current_date-interval '14 days' and\n", "     a.status_id <> 5\n", "    and business_type not ilike '%%b2b%%'\n", "    -- and business_type_id in (7)\n", "    group by 1)\n", ",\n", "metrics as\n", "(\n", "    select \n", "        zone,\n", "        backend_facility_id,\n", "        frontend_facility_id,\n", "        frontend_facility_name,\n", "        backend_facility_name,\n", "        frontend_outlet_id,\n", "        backend_outlet_id,\n", "        run_id,\n", "        v1.item_id,\n", "        (v1_quantity*weight_in_gm)/1000 as v1_quantity,\n", "        (v2_quantity*weight_in_gm)/1000 as v2_quantity,\n", "        (quantity_drops*weight_in_gm)/1000 as quantity_drops,\n", "        indent,\n", "        ars_started_at,\n", "        (inward_drop*weight_in_gm)/1000 as inward_drop,\n", "        (storage_drop*weight_in_gm)/1000 as storage_drop,\n", "        (truck_load_drop*weight_in_gm)/1000 as truck_load_drop,\n", "        picking_capacity_sku_drop,\n", "        (picking_capacity_quantity_drop*weight_in_gm)/1000 as picking_capacity_quantity_drop,\n", "        v1.created_at,\n", "        v2_quantity_without_case\n", "    from metrics.ars_truncation_rca_l7_raw_data_v2 v1\n", "    inner join lake_rpc.item_details v2 on cast(v1.item_id as varchar)=cast(v2.item_id as varchar)\n", ")\n", ",\n", "fe_details as\n", "    (select ma.facility_id as fe_facility_id, ma.item_id, category\n", "        from metrics.perishable_assortment ma\n", "        \n", "            join (select max(updated_at) as updated_at, item_id, facility_id from metrics.perishable_assortment\n", "                where date(updated_at) between current_date - 8 and current_date\n", "                    group by 2,3\n", "                    ) pa on pa.item_id = ma.item_id and pa.facility_id = ma.facility_id and pa.updated_at = ma.updated_at\n", "    ),\n", "base1 as\n", "    (select distinct rpc.item_id,\n", "        case when l0_id = 1487 then 'FnV'\n", "            when l2_id in (1185,1961,1367,63,1732,1733,1734,1369) then 'Perishable'\n", "            when perishable = 1 then 'Perishable'\n", "            when fd.item_id is not null then 'Perishable' else 'Packaged Goods' end as assortment_type\n", "\n", "                from lake_rpc.product_product rpc\n", "        \n", "                left join (select item_id, l0_id, l2_id from lake_rpc.item_category_details) cd on cd.item_id = rpc.item_id\n", "                left join (select item_id from fe_details) fd on fd.item_id = rpc.item_id\n", "                \n", "                left join (select distinct item_id from lake_rpc.item_tag_mapping where active = true\n", "                    and cast(tag_value as int) = 3 and tag_type_id = 3\n", "                        ) i on i.item_id = rpc.item_id\n", "                    \n", "                    where id in (select max(id) as id from lake_rpc.product_product pp where pp.active = 1 and pp.approved = 1 group by item_id)\n", "                        and assortment_type = 'Packaged Goods' and i.item_id is null\n", "    )\n", "\n", "select backend_facility_id\n", ", frontend_facility_id\n", ", zone\n", ", backend_Facility_name\n", ", frontend_facility_name\n", ", date(ars_started_at) ars_date\n", "\n", ", count(distinct case when v1_quantity>0 then item_id end)  v1_skus\n", ", sum(coalesce(v1_quantity,0)) v1_quantity\n", "\n", ", count(distinct case when v2_quantity>0 then item_id end)  v2_skus\n", ", sum(coalesce(v2_quantity,0)) v2_quantity\n", "\n", ", count(distinct case when inward_drop>0 and quantity_drop_segment='inward_drop' then item_id end) inward_drop_skus\n", ", sum(case when quantity_drop_segment='inward_drop' then coalesce(inward_drop,0) end) inward_drop_quantity\n", "\n", "\n", ", count(distinct case when storage_drop>0 and quantity_drop_segment='storage_drop' then item_id end) storage_drop_skus\n", ", sum(case when quantity_drop_segment='storage_drop' then coalesce(storage_drop,0) end) storage_drop_quantity\n", "\n", "\n", ", count(distinct case when truck_load_drop>0 and quantity_drop_segment='truck_load_drop' then item_id end) truck_load_drop_skus\n", ", sum(case when quantity_drop_segment='truck_load_drop' then coalesce(truck_load_drop,0) end) truck_load_drop_quantity\n", "\n", "\n", ", count(distinct case when picking_capacity_sku_drop>0 and quantity_drop_segment='picking_capacity_sku_drop' then item_id end) picking_capacity_sku_drop_skus\n", ", sum(case when quantity_drop_segment='picking_capacity_sku_drop' then coalesce(picking_capacity_sku_drop,0) end) picking_capacity_sku_drop_quantity\n", "\n", ", count(distinct case when picking_capacity_quantity_drop>0 and quantity_drop_segment='picking_capacity_quantity_drop' then item_id end) picking_capacity_quantity_drop_skus\n", ", sum(case when quantity_drop_segment='picking_capacity_quantity_drop' then coalesce(picking_capacity_quantity_drop,0) end) picking_capacity_quantity_drop_quantity\n", "\n", ", outbound_start_date\n", "\n", "\n", "from\n", "(select *\n", "    , case when inward_drop>0 and  inward_drop>storage_drop and inward_drop>truck_load_drop and inward_drop>picking_capacity_sku_drop and inward_drop>picking_capacity_quantity_drop then 'inward_drop'\n", "\n", "            when storage_drop>0 and storage_drop>inward_drop and storage_drop>truck_load_drop and storage_drop>picking_capacity_sku_drop and storage_drop>picking_capacity_quantity_drop then 'storage_drop'\n", "\n", "            when truck_load_drop>0 and truck_load_drop>inward_drop and truck_load_drop>storage_drop and truck_load_drop>picking_capacity_sku_drop and truck_load_drop>picking_capacity_quantity_drop then 'truck_load_drop'\n", "\n", "            when picking_capacity_sku_drop>0 and picking_capacity_sku_drop>inward_drop and picking_capacity_sku_drop>truck_load_drop and picking_capacity_sku_drop>storage_drop and picking_capacity_sku_drop>picking_capacity_quantity_drop then 'picking_capacity_sku_drop'\n", "            \n", "            when picking_capacity_quantity_drop>0 and picking_capacity_quantity_drop>inward_drop and picking_capacity_quantity_drop>truck_load_drop and picking_capacity_quantity_drop>storage_drop and picking_capacity_quantity_drop>picking_capacity_sku_drop then 'picking_capacity_quantity_drop'            \n", "\n", "            when inward_drop>0 then 'inward_drop' \n", "            \n", "            when storage_drop>0 then 'storage_drop' \n", "            \n", "            when truck_load_drop>0 then 'truck_load_drop' \n", "            \n", "            when picking_capacity_sku_drop>0 then 'picking_capacity_sku_drop'             \n", "\n", "            when picking_capacity_quantity_drop>0 then 'picking_capacity_quantity_drop' \n", "\n", "            end as quantity_drop_segment\n", "\n", "\n", "    from metrics\n", "    left join live_facility_list x on metrics.frontend_facility_id=x.facility_id\n", "    where created_at>=current_date-7\n", "    and item_id in (select distinct item_id from base1 where assortment_type in ('Packaged Goods'))\n", "    and backend_facility_id in (select distinct facility_id from lake_retail.console_outlet where business_type_id in (1,12,19,20,21))\n", "    and frontend_facility_id in (select distinct facility_id from lake_retail.console_outlet where business_type_id in (7))\n", "    ) base\n", "    group by 1,2,3,4,5,6,21\n", "order by case when zone='North' then 0 when zone='South' then 1 when zone='East' then 2 else 3 end\n", ",frontend_facility_id asc,ars_date desc\"\"\"\n", "\n", "\n", "truncations = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "76f3b1af-1262-4172-9c60-f64d77d1a3c8", "metadata": {}, "source": ["### Truncations Qty"]}, {"cell_type": "code", "execution_count": null, "id": "b304d9f6-f9d8-4d65-bbab-2a751deef42f", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with live_facility_list as\n", "    (select facility_id, min(date(a.created_at)) outbound_start_date\n", "    from lake_ims.ims_order_details a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where \n", "    -- date(a.created_at) <= current_date-interval '14 days' and\n", "     a.status_id <> 5\n", "    and business_type not ilike '%%b2b%%'\n", "    -- and business_type_id in (7)\n", "    group by 1)\n", ",\n", "metrics as\n", "(\n", "    select \n", "        zone,\n", "        backend_facility_id,\n", "        frontend_facility_id,\n", "        frontend_facility_name,\n", "        backend_facility_name,\n", "        frontend_outlet_id,\n", "        backend_outlet_id,\n", "        run_id,\n", "        v1.item_id,\n", "        v1_quantity,\n", "        v2_quantity,\n", "        quantity_drops,\n", "        indent,\n", "        ars_started_at,\n", "        inward_drop,\n", "        storage_drop,\n", "        truck_load_drop,\n", "        picking_capacity_sku_drop,\n", "        picking_capacity_quantity_drop,\n", "        v1.created_at,\n", "        v2_quantity_without_case\n", "    from metrics.ars_truncation_rca_l7_raw_data_v2 v1\n", "    inner join lake_rpc.item_details v2 on cast(v1.item_id as varchar)=cast(v2.item_id as varchar)\n", ")\n", ",\n", "fe_details as\n", "    (select ma.facility_id as fe_facility_id, ma.item_id, category\n", "        from metrics.perishable_assortment ma\n", "        \n", "            join (select max(updated_at) as updated_at, item_id, facility_id from metrics.perishable_assortment\n", "                where date(updated_at) between current_date - 8 and current_date\n", "                    group by 2,3\n", "                    ) pa on pa.item_id = ma.item_id and pa.facility_id = ma.facility_id and pa.updated_at = ma.updated_at\n", "    ),\n", "base1 as\n", "    (select distinct rpc.item_id,\n", "        case when l0_id = 1487 then 'FnV'\n", "            when l2_id in (1185,1961,1367,63,1732,1733,1734,1369) then 'Perishable'\n", "            when perishable = 1 then 'Perishable'\n", "            when fd.item_id is not null then 'Perishable' else 'Packaged Goods' end as assortment_type\n", "\n", "                from lake_rpc.product_product rpc\n", "        \n", "                left join (select item_id, l0_id, l2_id from lake_rpc.item_category_details) cd on cd.item_id = rpc.item_id\n", "                left join (select item_id from fe_details) fd on fd.item_id = rpc.item_id\n", "                \n", "                left join (select distinct item_id from lake_rpc.item_tag_mapping where active = true\n", "                    and cast(tag_value as int) = 3 and tag_type_id = 3\n", "                        ) i on i.item_id = rpc.item_id\n", "                    \n", "                    where id in (select max(id) as id from lake_rpc.product_product pp where pp.active = 1 and pp.approved = 1 group by item_id)\n", "                        and assortment_type = 'Packaged Goods' and i.item_id is null\n", "    )\n", "\n", "select backend_facility_id\n", ", frontend_facility_id\n", ", zone\n", ", backend_Facility_name\n", ", frontend_facility_name\n", ", date(ars_started_at) ars_date\n", "\n", ", count(distinct case when v1_quantity>0 then item_id end)  v1_skus\n", ", sum(coalesce(v1_quantity,0)) v1_quantity_1\n", "\n", ", count(distinct case when v2_quantity>0 then item_id end)  v2_skus\n", ", sum(coalesce(v2_quantity,0)) v2_quantity_1\n", "\n", ", count(distinct case when inward_drop>0 and quantity_drop_segment='inward_drop' then item_id end) inward_drop_skus_1\n", ", sum(case when quantity_drop_segment='inward_drop' then coalesce(inward_drop,0) end) inward_drop_quantity_1\n", "\n", "\n", ", count(distinct case when storage_drop>0 and quantity_drop_segment='storage_drop' then item_id end) storage_drop_skus_1\n", ", sum(case when quantity_drop_segment='storage_drop' then coalesce(storage_drop,0) end) storage_drop_quantity_1\n", "\n", "\n", ", count(distinct case when truck_load_drop>0 and quantity_drop_segment='truck_load_drop' then item_id end) truck_load_drop_skus_1\n", ", sum(case when quantity_drop_segment='truck_load_drop' then coalesce(truck_load_drop,0) end) truck_load_drop_quantity_1\n", "\n", "\n", ", count(distinct case when picking_capacity_sku_drop>0 and quantity_drop_segment='picking_capacity_sku_drop' then item_id end) picking_capacity_sku_drop_skus_1\n", ", sum(case when quantity_drop_segment='picking_capacity_sku_drop' then coalesce(picking_capacity_sku_drop,0) end) picking_capacity_sku_drop_quantity_1\n", "\n", ", count(distinct case when picking_capacity_quantity_drop>0 and quantity_drop_segment='picking_capacity_quantity_drop' then item_id end) picking_capacity_quantity_drop_skus_1\n", ", sum(case when quantity_drop_segment='picking_capacity_quantity_drop' then coalesce(picking_capacity_quantity_drop,0) end) picking_capacity_quantity_drop_quantity_1\n", "\n", ", outbound_start_date\n", "\n", "\n", "from\n", "(select *\n", "    , case when inward_drop>0 and  inward_drop>storage_drop and inward_drop>truck_load_drop and inward_drop>picking_capacity_sku_drop and inward_drop>picking_capacity_quantity_drop then 'inward_drop'\n", "\n", "            when storage_drop>0 and storage_drop>inward_drop and storage_drop>truck_load_drop and storage_drop>picking_capacity_sku_drop and storage_drop>picking_capacity_quantity_drop then 'storage_drop'\n", "\n", "            when truck_load_drop>0 and truck_load_drop>inward_drop and truck_load_drop>storage_drop and truck_load_drop>picking_capacity_sku_drop and truck_load_drop>picking_capacity_quantity_drop then 'truck_load_drop'\n", "\n", "            when picking_capacity_sku_drop>0 and picking_capacity_sku_drop>inward_drop and picking_capacity_sku_drop>truck_load_drop and picking_capacity_sku_drop>storage_drop and picking_capacity_sku_drop>picking_capacity_quantity_drop then 'picking_capacity_sku_drop'\n", "            \n", "            when picking_capacity_quantity_drop>0 and picking_capacity_quantity_drop>inward_drop and picking_capacity_quantity_drop>truck_load_drop and picking_capacity_quantity_drop>storage_drop and picking_capacity_quantity_drop>picking_capacity_sku_drop then 'picking_capacity_quantity_drop'            \n", "\n", "            when inward_drop>0 then 'inward_drop' \n", "            \n", "            when storage_drop>0 then 'storage_drop' \n", "            \n", "            when truck_load_drop>0 then 'truck_load_drop' \n", "            \n", "            when picking_capacity_sku_drop>0 then 'picking_capacity_sku_drop'             \n", "\n", "            when picking_capacity_quantity_drop>0 then 'picking_capacity_quantity_drop' \n", "\n", "            end as quantity_drop_segment\n", "\n", "\n", "    from metrics\n", "    left join live_facility_list x on metrics.frontend_facility_id=x.facility_id\n", "    where created_at>=current_date-7\n", "    and item_id in (select distinct item_id from base1 where assortment_type in ('Packaged Goods'))\n", "    and backend_facility_id in (select distinct facility_id from lake_retail.console_outlet where business_type_id in (1,12,19,20,21))\n", "    and frontend_facility_id in (select distinct facility_id from lake_retail.console_outlet where business_type_id in (7))\n", "    ) base\n", "    group by 1,2,3,4,5,6,21\n", "order by case when zone='North' then 0 when zone='South' then 1 when zone='East' then 2 else 3 end\n", ",frontend_facility_id asc,ars_date desc\"\"\"\n", "\n", "\n", "truncations_qty = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "2d6ab580-7344-4a0a-894f-214b9cc3740f", "metadata": {}, "outputs": [], "source": ["truncations.head()"]}, {"cell_type": "markdown", "id": "971384b0-2009-432f-b9c9-936b01bc0e6a", "metadata": {}, "source": ["### Weighted Availability overall"]}, {"cell_type": "code", "execution_count": null, "id": "38a5def5-bbb5-4d92-8e71-bc5004cee3c0", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"select \n", "    date(date_) as date,\n", "    facility_id,\n", "    avg(weighted_availability) as weighted_availability\n", "    from\n", "metrics.store_hourly_weighted_avail_2\n", "    where date(date_) between current_date-7 and current_date\n", "and facility_id in (select distinct facility_id from lake_retail.console_outlet where business_type_id in (7))\n", "group by 1,2\"\"\"\n", "\n", "weight_avail = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "a254e723-3b23-479e-839c-317b52cb84aa", "metadata": {}, "source": ["### Weighted Availability Assortment Level"]}, {"cell_type": "code", "execution_count": null, "id": "b55926f9-8c09-45c9-b98d-43abbadcd625", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select \n", "    date(date_) as date,\n", "    facility_id,\n", "    avg(case when assortment_type='FnV' then weighted_availability end) as FNV_weighted_availability,\n", "    avg(case when assortment_type='Packaged Goods' then weighted_availability end ) as PC_weighted_availability,\n", "    avg(case when assortment_type='Perishable' then weighted_availability end) as Perishable_weighted_availability\n", "from\n", "    metrics.store_hourly_weighted_availability_logs\n", "where \n", "    date(date_) between current_date-7 and current_date-1\n", "and facility_id in (select distinct facility_id from lake_retail.console_outlet where business_type_id in (7))\n", "group by 1,2\n", "    union\n", "select \n", "    date(date_) as date,\n", "    facility_id,\n", "    avg(case when assortment_type='FnV' then weighted_availability end) as FNV_weighted_availability,\n", "    avg(case when assortment_type='Packaged Goods' then weighted_availability end ) as PC_weighted_availability,\n", "    avg(case when assortment_type='Perishable' then weighted_availability end) as Perishable_weighted_availability\n", "from\n", "    metrics.store_hourly_weighted_availability\n", "where \n", "    date(date_) in (current_date)\n", "and facility_id in (select distinct facility_id from lake_retail.console_outlet where business_type_id in (7))\n", "group by 1,2\n", "\"\"\"\n", "\n", "weight_avail_assortment = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "0264b5e3-a10d-4d48-9684-9c719cf0a99c", "metadata": {}, "outputs": [], "source": ["weight_avail_assortment.head(5)"]}, {"cell_type": "markdown", "id": "60e0f3ba-a784-49c1-9f38-a3286ae30a84", "metadata": {}, "source": ["### Projected Carts"]}, {"cell_type": "code", "execution_count": null, "id": "96bf68c9-5797-422d-8262-2416263c3a80", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "WITH fe_details AS (\n", "    SELECT DISTINCT cl.name AS city, bfom.outlet_id, pfom.facility_id as facility_id, pfom.outlet_name\n", "    FROM lake_po.bulk_facility_outlet_mapping AS bfom\n", "    INNER JOIN lake_retail.console_outlet AS co ON bfom.outlet_id = co.id AND co.business_type_id = 7\n", "    LEFT JOIN lake_po.physical_facility_outlet_mapping AS pfom ON pfom.outlet_id = bfom.outlet_id\n", "    LEFT JOIN lake_retail.console_location AS cl ON cl.id = pfom.city_id\n", "    WHERE pfom.active = 1 AND pfom.ars_active = 1 AND bfom.active = 1\n", ")\n", "\n", ",base as\n", "    (select\n", "    date,\n", "    facility_id,\n", "    sum(case when (new_carts = '') or (new_carts is null) or (new_carts = 'nan') then 0 else cast(new_carts as int) end) as projected_carts\n", "from \n", "    (select *, carts::float as new_carts\n", "    from metrics.cart_projections\n", "    where carts <> 'nan' and carts is not null and carts != ''\n", "    ) A\n", "inner join fe_details as B on A.outletid=B.outlet_id\n", "where updated_on = (SELECT max(updated_on) FROM metrics.cart_projections)\n", "and date between current_date-14 and current_date\n", "group by 1,2)\n", "\n", "select \n", "    date,\n", "    facility_id,\n", "    projected_carts,\n", "    avg(projected_carts) over(partition by facility_id order by date desc\n", "    rows between current row and 6 Following) as avg_projected_carts\n", "from base\n", "\n", "\"\"\"\n", "\n", "projected_carts = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "4cfcd844-4ce0-4a43-b14a-162fe8c2911a", "metadata": {}, "outputs": [], "source": ["projected_carts.head(5)"]}, {"cell_type": "markdown", "id": "a69f94f3-8095-409a-a9a5-d7d039c64d6d", "metadata": {}, "source": ["### Actual Carts"]}, {"cell_type": "code", "execution_count": null, "id": "df081bac-6df2-4fec-b07b-33bb446d0088", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"with base_1 as\n", "(\n", "SELECT date(od.created_at  + interval '330' minute) as date, outlet AS outlet_id, count(distinct od.ancestor) as actual_order\n", "FROM ims.ims_order_details od\n", "LEFT JOIN ims.ims_order_items oi on od.id = oi.order_details_id\n", "LEFT JOIN ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "WHERE status_id in (1,2)\n", "AND od.insert_ds_ist between cast(current_date-interval '15' day as varchar) and cast(current_date + interval '1' day as varchar)\n", "AND oi.insert_ds_ist between cast(current_date-interval '15' day as varchar) and cast(current_date + interval '1' day as varchar)\n", "AND oa.insert_ds_ist between cast(current_date-interval '15' day as varchar) and cast(current_date + interval '1' day as varchar)\n", "GROUP BY 1,2\n", "),\n", "base as\n", "(SELECT\n", "    b.date,\n", "    co.facility_id,\n", "    sum(actual_order) as actual_orders\n", "FROM base_1 b\n", "left join lake_retail.console_outlet co on co.id = b.outlet_id and co.active = 1\n", " group by 1,2)\n", "select\n", "    date,\n", "    facility_id,\n", "    actual_orders,\n", "    avg(actual_orders) over(partition by facility_id order by date desc\n", "    rows between current row and 6 Following) as avg_actual_orders\n", "from base\n", "\n", "\n", "\"\"\"\n", "\n", "actual_carts = read_sql_query(query, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "1f90e421-1534-41e9-9e40-1e7a49f444a7", "metadata": {}, "outputs": [], "source": ["actual_carts[\"date\"] = pd.to_datetime(actual_carts[\"date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "b0e85ce5-7eb3-4d20-aeb6-8b89c1b5e113", "metadata": {}, "outputs": [], "source": ["actual_carts"]}, {"cell_type": "markdown", "id": "648bcac1-0668-4591-9006-d33ed2e58439", "metadata": {}, "source": ["#### Vehicle Capacity Morning and Evening\n"]}, {"cell_type": "code", "execution_count": null, "id": "458d9fb1-c8dc-499f-8211-caf87a008f2b", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "with base as (\n", "select facility_id, run_id,started_at, \n", "        replace(replace(json_extract_path_text(extra, 'auto_sto_outlets'), '[',''),']','') comments \n", "from lake_ars.job_run \n", "where date(started_at + interval '5.5 Hours' + interval '1 day') between current_date-interval '6 day' and current_date + interval '1 day' -- -interval '1 day'\n", "and json_extract_path_text(simulation_params, 'mode', true) = '' and json_extract_path_text(simulation_params, 'run', true) = 'ars_lite'\n", "order by run_date\n", "),\n", "\n", "\n", "seq as (\n", "select row_number() over(order by TRUE)::integer as i\n", "from lake_ars.job_run\n", "limit 1000\n", "),\n", "\n", "run_ids_outlet as (\n", "select a.facility_id, a.run_id, a.started_at, trim(SPLIT_PART(comments,',', s.i)) as outlet_id\n", "            \n", "from base a\n", "inner join seq s on s.i <= REGEXP_COUNT(a.comments, ',') + 1\n", "where outlet_id !=''\n", ")\n", "\n", "\n", "select \n", "    date(started_at + interval '5.5 Hours') as date,\n", "    r.facility_id as backend_facility_id,\n", "    o.facility_id as frontend_facility_id,\n", "    sum(case when extract(hour from (started_at + interval '5.5 hours' )) between 5 and 14 then truck_load_capacity end) as \"morning_truck_load_capacity\",\n", "    sum(case when extract(hour from (started_at + interval '5.5 hours')) not between 5 and 14 then truck_load_capacity end) as \"evening_truck_load_capacity\"\n", "from run_ids_outlet r\n", "left join\n", "lake_ars.bulk_facility_transfer_days t on r.outlet_id=t.frontend_outlet_id and r.run_id=t.run_id\n", "left join lake_retail.console_outlet o on r.outlet_id=o.id\n", "where started_at between (current_date - 8 || ' 00:00:00')::timestamp  - interval '5.5 Hours'\n", "                    and (current_date + 1 || ' 23:59:59')::timestamp - interval '5.5 Hours'\n", "group by 1,2,3\n", "\n", "    \"\"\"\n", "\n", "vehicle_capacity = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "8d6b296d-bf5c-4711-a2b8-79d1788010af", "metadata": {}, "outputs": [], "source": ["vehicle_capacity.sort_values(by=[\"date\"], ascending=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "68823e00-1fbe-4823-9ddb-840afa7487f2", "metadata": {}, "outputs": [], "source": ["vehicle_capacity[\n", "    (vehicle_capacity[\"backend_facility_id\"] == 1320)\n", "    & (vehicle_capacity[\"frontend_facility_id\"] == 1764)\n", "]"]}, {"cell_type": "markdown", "id": "b8405be8-8c64-4296-958a-852724dd7924", "metadata": {}, "source": ["#### Vehicle Usage Morning Evening"]}, {"cell_type": "code", "execution_count": null, "id": "9fb4eb20-81e1-4ae7-b593-a073ecb8949a", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"with zone_tbl_treatment as\n", "(\n", "select\n", "facility_name,\n", "max(zone) as zone \n", "from metrics.outlet_zone_mapping\n", "group by facility_name\n", ")\n", ",\n", "base as(\n", "select \n", "    date(m.sto_created_at) as sto_created_date,\n", "    x.sender_facility_id,\n", "    x.receiving_facility_id,\n", "    (case when extract(hour from (sto_created_at)) between 5 and 14 then 'Indent 1' else 'Indent 2' end) as indent,\n", "    sum(m.reserved_quantity) as created_qty,\n", "    round(sum(m.weight_in_kg),0) as weight_in_kg\n", "from metrics.multirep_data m\n", "    join (select bulk_sto_id,c.facility_id as sender_facility_id,c1.facility_id as receiving_facility_id,count(distinct sto_id) as stos\n", "from metrics.esto_details y\n", "left join lake_retail.console_outlet c on c.id = y.sender_outlet_id\n", "left join lake_retail.console_outlet c1 on c1.id = y.receiving_outlet_id\n", "group by 1,2,3) x on x.bulk_sto_id = m.bulk_sto_id\n", "left join zone_tbl_treatment z on z.facility_name = m.sender_facility_name\n", "where date(sto_created_at) > current_date-8\n", "and ars_mode in ('normal')\n", "group by \n", "1,2,3,4\n", ")\n", "\n", "select \n", "    sto_created_date,\n", "    sender_facility_id,\n", "    receiving_facility_id,\n", "    sum(case when indent='Indent 1' then created_qty end) as qty_morning,\n", "    sum(case when indent='Indent 1' then weight_in_kg end) as weight_morning,\n", "    sum(case when indent='Indent 2' then created_qty end) as qty_evening,\n", "    sum(case when indent='Indent 2' then weight_in_kg end) as weight_evening\n", "from base\n", "group by 1,2,3\"\"\"\n", "\n", "qty_mapping = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "c5153f74-1fd6-45ad-8ee4-deaed942c9b8", "metadata": {}, "outputs": [], "source": ["qty_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e7797e4b-b8d1-4c87-a8be-69073966090a", "metadata": {}, "outputs": [], "source": ["qty_mapping[\"weight_morning\"]"]}, {"cell_type": "code", "execution_count": null, "id": "535325e8-617a-4744-ac63-fbcee124ba6b", "metadata": {}, "outputs": [], "source": ["qty_mapping[\n", "    (qty_mapping[\"sender_facility_id\"] == 1206)\n", "    & ((qty_mapping[\"receiving_facility_id\"] == 873))\n", "]"]}, {"cell_type": "markdown", "id": "528e99bf-25fe-4e9c-9102-aeaf3beb0be7", "metadata": {}, "source": ["### weighted availability"]}, {"cell_type": "code", "execution_count": null, "id": "3e2ce859-175d-48d5-9b8f-5e59bf342586", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "select facility_id, date_,\n", "avg(bx_avail) as bx_avail,\n", "avg(ba_avail) as ba_avail,\n", "avg(bb_avail) as bb_avail\n", "from(\n", "    select a.facility_id,\n", "    date(order_date) as date_, \n", "    extract(hour from order_date) as hour,\n", "    count(distinct case when bucket_x='Y' and new_substate=1 then a.item_id else null end) as active_bx,\n", "    count(distinct case when bucket_x='Y' and new_substate=1 and inv_flag > 0 then a.item_id else null end) as live_bx,\n", "    (case when active_bx > 0 then live_bx*1.00/active_bx else 0 end) as bx_avail,\n", "    count(distinct case when (bucket_x='Y' or buckets='A') and new_substate=1 then a.item_id else null end) as active_ba,\n", "    count(distinct case when (bucket_x='Y' or buckets='A') and new_substate=1 and inv_flag > 0 then a.item_id else null end) as live_ba,\n", "    (case when active_ba > 0 then live_ba*1.00/active_ba else 0 end) as ba_avail,\n", "    count(distinct case when bucket_x<>'Y' and buckets<>'A' and new_substate=1 then a.item_id else null end) as active_bb,\n", "    count(distinct case when bucket_x<>'Y' and buckets<>'A' and new_substate=1 and inv_flag > 0 then a.item_id else null end) as live_bb,\n", "    (case when active_bb > 0 then live_bb*1.00/active_bb else 0 end) as bb_avail\n", "    \n", "    from rpc_daily_availability a\n", "    inner join lake_retail.console_outlet co on a.facility_id=co.facility_id and business_type_id=7 and co.active=1\n", "    where order_date between current_date-7 and current_date+1\n", "    and hour between 6 and 23\n", "\n", "    group by 1,2,3\n", ")\n", "group by 1,2\n", "order by 1,2 desc\"\"\"\n", "\n", "bucket_weighted = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "ceb8f8f4-5d0f-4753-917e-b63dcbeac35d", "metadata": {}, "source": ["#### Final Table"]}, {"cell_type": "code", "execution_count": null, "id": "d8f316ba-c2b2-46f8-bfc4-ad4a7aa54a72", "metadata": {}, "outputs": [], "source": ["sales.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e4f04ccd-1e57-4a2f-8f39-676ebaf62b6c", "metadata": {}, "outputs": [], "source": ["truncations.columns"]}, {"cell_type": "code", "execution_count": null, "id": "f4f439e0-9b19-428a-8364-55f35ac9d677", "metadata": {}, "outputs": [], "source": ["Grocery_outbound_mapping.columns"]}, {"cell_type": "code", "execution_count": null, "id": "b1c1dc3f-7d8e-42bc-af83-0136958a0a4d", "metadata": {}, "outputs": [], "source": ["## merging for grocery_qty with sales\n", "final = pd.merge(\n", "    Grocery_outbound_mapping,\n", "    sales,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"receiver_facility_id\"],\n", "    right_on=[\"order_date\", \"facility_id\"],\n", ").drop([\"facility_id\", \"order_date\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "ecaab679-f90f-4ab9-8c63-6905400b60ba", "metadata": {}, "outputs": [], "source": ["final1 = pd.merge(\n", "    final,\n", "    truncations,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"sender_facility_id\", \"receiver_facility_id\"],\n", "    right_on=[\"ars_date\", \"backend_facility_id\", \"frontend_facility_id\"],\n", ").drop(\n", "    [\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"zone_y\",\n", "        \"backend_facility_name\",\n", "        \"frontend_facility_name\",\n", "        \"ars_date\",\n", "        \"picking_capacity_sku_drop_skus\",\n", "        \"picking_capacity_sku_drop_quantity\",\n", "        \"outbound_start_date\",\n", "    ],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7135bdac-b01d-4d86-b827-e057b8036906", "metadata": {}, "outputs": [], "source": ["final2 = pd.merge(\n", "    final1,\n", "    weight_avail,\n", "    how=\"left\",\n", "    left_on=[\"receiver_facility_id\", \"date\"],\n", "    right_on=[\"facility_id\", \"date\"],\n", ").drop([\"facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "40c0c0ec-2d4c-4f90-a4ff-89c4c1fec3ec", "metadata": {}, "outputs": [], "source": ["final3 = pd.merge(\n", "    final2,\n", "    weight_avail_assortment,\n", "    how=\"left\",\n", "    left_on=[\"receiver_facility_id\", \"date\"],\n", "    right_on=[\"facility_id\", \"date\"],\n", ").drop([\"facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "39c046a0-65d4-4fc6-870f-14e16c33963c", "metadata": {}, "outputs": [], "source": ["final4 = pd.merge(\n", "    final3,\n", "    projected_carts,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"receiver_facility_id\"],\n", "    right_on=[\"date\", \"facility_id\"],\n", ").drop([\"facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "32eb31d9-9807-4599-be38-ac4e7dac11fc", "metadata": {}, "outputs": [], "source": ["final5 = pd.merge(\n", "    final4,\n", "    actual_carts,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"receiver_facility_id\"],\n", "    right_on=[\"date\", \"facility_id\"],\n", ").drop([\"facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "252883a8-afa7-46b2-a36a-66a769ef8987", "metadata": {}, "outputs": [], "source": ["final6 = pd.merge(\n", "    final5,\n", "    city_mapping,\n", "    how=\"left\",\n", "    left_on=[\"receiver_facility_id\"],\n", "    right_on=[\"facility_id\"],\n", ").drop([\"facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "42c6cb54-d096-45f7-ae9f-58c69941e3cd", "metadata": {}, "outputs": [], "source": ["final7 = pd.merge(\n", "    final6,\n", "    vehicle_capacity,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"sender_facility_id\", \"receiver_facility_id\"],\n", "    right_on=[\"date\", \"backend_facility_id\", \"frontend_facility_id\"],\n", ").drop([\"backend_facility_id\", \"frontend_facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "177e885a-4c4a-4f5a-8f68-f9cb48dfe4f6", "metadata": {}, "outputs": [], "source": ["final8 = pd.merge(\n", "    final7,\n", "    qty_mapping,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"sender_facility_id\", \"receiver_facility_id\"],\n", "    right_on=[\"sto_created_date\", \"sender_facility_id\", \"receiving_facility_id\"],\n", ").drop([\"sto_created_date\", \"receiving_facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "68206a7c-d3eb-41bf-a168-ed52681e48a7", "metadata": {}, "outputs": [], "source": ["final9 = pd.merge(\n", "    final8,\n", "    bucket_weighted,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"receiver_facility_id\"],\n", "    right_on=[\"date_\", \"facility_id\"],\n", ").drop([\"date_\", \"facility_id\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "ae6392b8-6f3b-4af1-812e-169f1de54786", "metadata": {}, "outputs": [], "source": ["final9 = final9[\n", "    [\n", "        \"zone_x\",\n", "        \"date\",\n", "        \"sender_outlet_id\",\n", "        \"sender_facility_id\",\n", "        \"sender_facility_name\",\n", "        \"receiver_outlet_id\",\n", "        \"receiver_facility_id\",\n", "        \"receiver_facility_name\",\n", "        \"total_billed_weight\",\n", "        \"total_billed_qty\",\n", "        \"avg_billed_weight\",\n", "        \"avg_billed_qty\",\n", "        \"qty_sold\",\n", "        \"gmv\",\n", "        \"v1_skus\",\n", "        \"v1_quantity\",\n", "        \"v2_skus\",\n", "        \"v2_quantity\",\n", "        \"inward_drop_skus\",\n", "        \"inward_drop_quantity\",\n", "        \"storage_drop_skus\",\n", "        \"storage_drop_quantity\",\n", "        \"truck_load_drop_skus\",\n", "        \"truck_load_drop_quantity\",\n", "        \"picking_capacity_quantity_drop_skus\",\n", "        \"picking_capacity_quantity_drop_quantity\",\n", "        \"weighted_availability\",\n", "        \"fnv_weighted_availability\",\n", "        \"pc_weighted_availability\",\n", "        \"perishable_weighted_availability\",\n", "        \"projected_carts\",\n", "        \"avg_projected_carts\",\n", "        \"actual_orders\",\n", "        \"avg_actual_orders\",\n", "        \"city\",\n", "        \"morning_truck_load_capacity\",\n", "        \"evening_truck_load_capacity\",\n", "        \"qty_morning\",\n", "        \"weight_morning\",\n", "        \"qty_evening\",\n", "        \"weight_evening\",\n", "        \"bx_avail\",\n", "        \"ba_avail\",\n", "        \"bb_avail\",\n", "        \"projection\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9af1bb0e-8143-452b-8e8c-76516616fdd2", "metadata": {}, "outputs": [], "source": ["final10 = pd.merge(\n", "    final9,\n", "    truncations_qty,\n", "    how=\"left\",\n", "    left_on=[\"date\", \"sender_facility_id\", \"receiver_facility_id\"],\n", "    right_on=[\"ars_date\", \"backend_facility_id\", \"frontend_facility_id\"],\n", ").drop(\n", "    [\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"frontend_facility_name\",\n", "        \"ars_date\",\n", "        \"outbound_start_date\",\n", "    ],\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "855c3171-4baa-4642-90d5-99e6810b9b46", "metadata": {}, "outputs": [], "source": ["final10.sort_values(\n", "    by=[\"date\", \"sender_facility_id\", \"receiver_facility_id\"],\n", "    ascending=False,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1ec58011-825e-439b-b67a-3c4cb3ef9f22", "metadata": {}, "outputs": [], "source": ["final10.columns"]}, {"cell_type": "code", "execution_count": null, "id": "0af9cc02-bd19-4545-a0b9-0b35c6fbab69", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final9, \"1yX2y5OQYuMOubuVgy_7Ti9GqJKZUzYW9aVyAPA2L54o\", \"Sheet6\")"]}, {"cell_type": "code", "execution_count": null, "id": "7cb97787-6c0d-45bb-b196-f152ea4cd7a8", "metadata": {}, "outputs": [], "source": ["final10.columns"]}, {"cell_type": "markdown", "id": "17e76c05-2f36-40a1-83e9-df7bdf1a1375", "metadata": {}, "source": ["#### City Level View"]}, {"cell_type": "code", "execution_count": null, "id": "47879abe-cd8b-4683-a02d-0ea9dc9865bf", "metadata": {}, "outputs": [], "source": ["city = final10.groupby(by=[\"date\", \"zone_x\", \"city\"]).agg(\n", "    {\n", "        \"total_billed_weight\": \"sum\",\n", "        \"total_billed_qty\": \"sum\",\n", "        \"avg_billed_weight\": \"sum\",\n", "        \"avg_billed_qty\": \"sum\",\n", "        \"qty_sold\": \"sum\",\n", "        \"gmv\": \"sum\",\n", "        \"v1_skus_x\": \"sum\",\n", "        \"v1_quantity\": \"sum\",\n", "        \"v2_skus_x\": \"sum\",\n", "        \"v2_quantity\": \"sum\",\n", "        \"inward_drop_skus\": \"sum\",\n", "        \"inward_drop_quantity\": \"sum\",\n", "        \"storage_drop_skus\": \"sum\",\n", "        \"storage_drop_quantity\": \"sum\",\n", "        \"truck_load_drop_skus\": \"sum\",\n", "        \"truck_load_drop_quantity\": \"sum\",\n", "        \"picking_capacity_quantity_drop_skus\": \"sum\",\n", "        \"picking_capacity_quantity_drop_quantity\": \"sum\",\n", "        \"weighted_availability\": \"mean\",\n", "        \"fnv_weighted_availability\": \"mean\",\n", "        \"pc_weighted_availability\": \"mean\",\n", "        \"perishable_weighted_availability\": \"mean\",\n", "        \"projected_carts\": \"sum\",\n", "        \"avg_projected_carts\": \"sum\",\n", "        \"actual_orders\": \"sum\",\n", "        \"avg_actual_orders\": \"sum\",\n", "        \"morning_truck_load_capacity\": \"sum\",\n", "        \"evening_truck_load_capacity\": \"sum\",\n", "        \"qty_morning\": \"sum\",\n", "        \"weight_morning\": \"sum\",\n", "        \"qty_evening\": \"sum\",\n", "        \"weight_evening\": \"sum\",\n", "        \"bx_avail\": \"mean\",\n", "        \"ba_avail\": \"mean\",\n", "        \"bb_avail\": \"mean\",\n", "        \"v1_quantity_1\": \"sum\",\n", "        \"v2_skus_y\": \"sum\",\n", "        \"v2_quantity_1\": \"sum\",\n", "        \"inward_drop_quantity_1\": \"sum\",\n", "        \"storage_drop_skus_1\": \"sum\",\n", "        \"storage_drop_quantity_1\": \"sum\",\n", "        \"truck_load_drop_skus_1\": \"sum\",\n", "        \"truck_load_drop_quantity_1\": \"sum\",\n", "        \"picking_capacity_sku_drop_skus_1\": \"sum\",\n", "        \"picking_capacity_sku_drop_quantity_1\": \"sum\",\n", "        \"picking_capacity_quantity_drop_skus_1\": \"sum\",\n", "        \"picking_capacity_quantity_drop_quantity_1\": \"sum\",\n", "        \"projection\": \"sum\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "79f4378c-115a-4284-8d42-b6f3b5d097f0", "metadata": {}, "outputs": [], "source": ["city.reset_index(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "91c0c439-00ee-4609-bfa1-f3757c44341d", "metadata": {}, "outputs": [], "source": ["city[\"morning_utilization\"] = (\n", "    city[\"weight_morning\"] / city[\"morning_truck_load_capacity\"]\n", ")\n", "city[\"evening_utilization\"] = (\n", "    city[\"weight_evening\"] / city[\"evening_truck_load_capacity\"]\n", ")\n", "city[\"total_truncation\"] = city[\"v1_quantity\"] - city[\"v2_quantity\"]\n", "city[\"total_capacity\"] = (\n", "    city[\"morning_truck_load_capacity\"] + city[\"evening_truck_load_capacity\"]\n", ")\n", "city[\"avg_billed_weight\"] = city[\"avg_billed_weight\"] / 1000"]}, {"cell_type": "code", "execution_count": null, "id": "1348aed8-cfbe-4c8e-a046-f84d0cdd0117", "metadata": {}, "outputs": [], "source": ["city.columns"]}, {"cell_type": "code", "execution_count": null, "id": "efa785ff-806f-472f-afdb-6bbfef449338", "metadata": {}, "outputs": [], "source": ["city = city[\n", "    [\n", "        \"city\",\n", "        \"date\",\n", "        \"total_billed_qty\",\n", "        \"actual_orders\",\n", "        \"avg_billed_qty\",\n", "        \"avg_billed_weight\",\n", "        \"morning_truck_load_capacity\",\n", "        \"evening_truck_load_capacity\",\n", "        \"weight_morning\",\n", "        \"weight_evening\",\n", "        \"morning_utilization\",\n", "        \"evening_utilization\",\n", "        \"v1_quantity\",\n", "        \"v2_quantity\",\n", "        \"total_truncation\",\n", "        \"truck_load_drop_quantity\",\n", "        \"storage_drop_quantity\",\n", "        \"picking_capacity_quantity_drop_quantity\",\n", "        \"inward_drop_quantity\",\n", "        \"weighted_availability\",\n", "        \"pc_weighted_availability\",\n", "        \"fnv_weighted_availability\",\n", "        \"perishable_weighted_availability\",\n", "        \"bx_avail\",\n", "        \"ba_avail\",\n", "        \"bb_avail\",\n", "        \"v1_quantity_1\",\n", "        \"v2_skus_y\",\n", "        \"v2_quantity_1\",\n", "        \"inward_drop_quantity_1\",\n", "        \"storage_drop_skus_1\",\n", "        \"storage_drop_quantity_1\",\n", "        \"truck_load_drop_skus_1\",\n", "        \"truck_load_drop_quantity_1\",\n", "        \"picking_capacity_sku_drop_skus_1\",\n", "        \"picking_capacity_sku_drop_quantity_1\",\n", "        \"picking_capacity_quantity_drop_skus_1\",\n", "        \"picking_capacity_quantity_drop_quantity_1\",\n", "        \"morning_utilization\",\n", "        \"evening_utilization\",\n", "        \"total_truncation\",\n", "        \"qty_sold\",\n", "        \"gmv\",\n", "        \"projected_carts\",\n", "        \"avg_projected_carts\",\n", "        \"avg_actual_orders\",\n", "        \"total_capacity\",\n", "        \"qty_morning\",\n", "        \"qty_evening\",\n", "        \"projection\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "98eab74f-82a2-4b4f-b97c-e868780fc05f", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(city, \"1yX2y5OQYuMOubuVgy_7Ti9GqJKZUzYW9aVyAPA2L54o\", \"City Level RAW\")"]}, {"cell_type": "code", "execution_count": null, "id": "64ddb2fc-bd1b-487a-8c8f-156cec2b7628", "metadata": {}, "outputs": [], "source": ["city.columns"]}, {"cell_type": "code", "execution_count": null, "id": "2bc4e56d-443c-46a6-b92c-a723aa1c7a40", "metadata": {}, "outputs": [], "source": ["final10.columns"]}, {"cell_type": "markdown", "id": "fd0f5941-7b75-4d3d-961d-e22bf00be760", "metadata": {}, "source": ["### Backend Level View"]}, {"cell_type": "code", "execution_count": null, "id": "1beb6696-b210-4ed5-87f0-25bf73d084d8", "metadata": {}, "outputs": [], "source": ["backend = (\n", "    final10.groupby(\n", "        by=[\"date\", \"zone_x\", \"city\", \"sender_facility_id\", \"sender_facility_name\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"total_billed_weight\": \"sum\",\n", "            \"total_billed_qty\": \"sum\",\n", "            \"avg_billed_weight\": \"sum\",\n", "            \"avg_billed_qty\": \"sum\",\n", "            \"qty_sold\": \"sum\",\n", "            \"gmv\": \"sum\",\n", "            \"v1_skus_x\": \"sum\",\n", "            \"v1_quantity\": \"sum\",\n", "            \"v2_skus_x\": \"sum\",\n", "            \"v2_quantity\": \"sum\",\n", "            \"inward_drop_skus\": \"sum\",\n", "            \"inward_drop_quantity\": \"sum\",\n", "            \"storage_drop_skus\": \"sum\",\n", "            \"storage_drop_quantity\": \"sum\",\n", "            \"truck_load_drop_skus\": \"sum\",\n", "            \"truck_load_drop_quantity\": \"sum\",\n", "            \"picking_capacity_quantity_drop_skus\": \"sum\",\n", "            \"picking_capacity_quantity_drop_quantity\": \"sum\",\n", "            \"weighted_availability\": \"mean\",\n", "            \"fnv_weighted_availability\": \"mean\",\n", "            \"pc_weighted_availability\": \"mean\",\n", "            \"perishable_weighted_availability\": \"mean\",\n", "            \"projected_carts\": \"sum\",\n", "            \"avg_projected_carts\": \"sum\",\n", "            \"actual_orders\": \"sum\",\n", "            \"avg_actual_orders\": \"sum\",\n", "            \"morning_truck_load_capacity\": \"sum\",\n", "            \"evening_truck_load_capacity\": \"sum\",\n", "            \"qty_morning\": \"sum\",\n", "            \"weight_morning\": \"sum\",\n", "            \"qty_evening\": \"sum\",\n", "            \"weight_evening\": \"sum\",\n", "            \"bx_avail\": \"mean\",\n", "            \"ba_avail\": \"mean\",\n", "            \"bb_avail\": \"mean\",\n", "            \"v1_quantity_1\": \"sum\",\n", "            \"v2_skus_y\": \"sum\",\n", "            \"v2_quantity_1\": \"sum\",\n", "            \"inward_drop_quantity_1\": \"sum\",\n", "            \"storage_drop_skus_1\": \"sum\",\n", "            \"storage_drop_quantity_1\": \"sum\",\n", "            \"truck_load_drop_skus_1\": \"sum\",\n", "            \"truck_load_drop_quantity_1\": \"sum\",\n", "            \"picking_capacity_sku_drop_skus_1\": \"sum\",\n", "            \"picking_capacity_sku_drop_quantity_1\": \"sum\",\n", "            \"picking_capacity_quantity_drop_skus_1\": \"sum\",\n", "            \"picking_capacity_quantity_drop_quantity_1\": \"sum\",\n", "            \"projection\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "backend[\"morning_utilization\"] = (\n", "    backend[\"weight_morning\"] / backend[\"morning_truck_load_capacity\"]\n", ")\n", "backend[\"evening_utilization\"] = (\n", "    backend[\"weight_evening\"] / backend[\"evening_truck_load_capacity\"]\n", ")\n", "backend[\"total_truncation\"] = backend[\"v1_quantity\"] - backend[\"v2_quantity\"]\n", "backend[\"total_capacity\"] = (\n", "    backend[\"morning_truck_load_capacity\"] + backend[\"evening_truck_load_capacity\"]\n", ")\n", "backend[\"avg_billed_weight\"] = backend[\"avg_billed_weight\"] / 1000"]}, {"cell_type": "code", "execution_count": null, "id": "65102a6e-0d51-47c5-86d4-559194763fdc", "metadata": {}, "outputs": [], "source": ["backend = backend[\n", "    [\n", "        \"city\",\n", "        \"date\",\n", "        \"total_billed_qty\",\n", "        \"actual_orders\",\n", "        \"avg_billed_qty\",\n", "        \"avg_billed_weight\",\n", "        \"morning_truck_load_capacity\",\n", "        \"evening_truck_load_capacity\",\n", "        \"weight_morning\",\n", "        \"weight_evening\",\n", "        \"morning_utilization\",\n", "        \"evening_utilization\",\n", "        \"v1_quantity\",\n", "        \"v2_quantity\",\n", "        \"total_truncation\",\n", "        \"truck_load_drop_quantity\",\n", "        \"storage_drop_quantity\",\n", "        \"picking_capacity_quantity_drop_quantity\",\n", "        \"inward_drop_quantity\",\n", "        \"weighted_availability\",\n", "        \"pc_weighted_availability\",\n", "        \"fnv_weighted_availability\",\n", "        \"perishable_weighted_availability\",\n", "        \"bx_avail\",\n", "        \"ba_avail\",\n", "        \"bb_avail\",\n", "        \"v1_quantity_1\",\n", "        \"v2_skus_y\",\n", "        \"v2_quantity_1\",\n", "        \"inward_drop_quantity_1\",\n", "        \"storage_drop_skus_1\",\n", "        \"storage_drop_quantity_1\",\n", "        \"truck_load_drop_skus_1\",\n", "        \"truck_load_drop_quantity_1\",\n", "        \"picking_capacity_sku_drop_skus_1\",\n", "        \"picking_capacity_sku_drop_quantity_1\",\n", "        \"picking_capacity_quantity_drop_skus_1\",\n", "        \"picking_capacity_quantity_drop_quantity_1\",\n", "        \"morning_utilization\",\n", "        \"evening_utilization\",\n", "        \"total_truncation\",\n", "        \"qty_sold\",\n", "        \"gmv\",\n", "        \"projected_carts\",\n", "        \"avg_projected_carts\",\n", "        \"avg_actual_orders\",\n", "        \"total_capacity\",\n", "        \"qty_morning\",\n", "        \"qty_evening\",\n", "        \"sender_facility_id\",\n", "        \"sender_facility_name\",\n", "        \"projection\",\n", "    ]\n", "]\n", "pb.to_sheets(\n", "    backend, \"1yX2y5OQYuMOubuVgy_7Ti9GqJKZUzYW9aVyAPA2L54o\", \"Backend Level Raw\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "385d4017-e926-4d15-83e8-ed40211c086c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "855fa16d-72f6-41db-b3ce-40b95283c86a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "27583bc0-9c48-4212-923e-befaaedc0391", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98515269-fb6f-4702-b3d5-b40fe9f78c57", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4f60e0e9-20fa-4fa9-8050-f339d10f531e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b8dd842c-f57f-4c2f-af6f-ae624a94f251", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f51ca9a8-f399-4e60-8ad1-2938b5485230", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}