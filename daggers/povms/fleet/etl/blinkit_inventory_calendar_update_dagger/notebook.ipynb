{"cells": [{"cell_type": "code", "execution_count": null, "id": "b89f73c5-9727-4f1e-bcd2-dbf797bea04d", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "from pytz import timezone\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "14349fda-9597-4baa-b154-4bab6a855586", "metadata": {}, "outputs": [], "source": ["current_datetime = datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "id": "cb32d387-ccb9-46e4-8ed4-0dca73bcb738", "metadata": {}, "outputs": [], "source": ["current_datetime"]}, {"cell_type": "code", "execution_count": null, "id": "6dad3dd7-38e4-48a7-b111-3400862505a2", "metadata": {}, "outputs": [], "source": ["### Blinkit Inventory Calendar https://docs.google.com/spreadsheets/d/1Y18sKeM-wPnkN4X7NV9eOz1EMWb5R_imY0xgWrjwO-g/edit#gid=1267041663"]}, {"cell_type": "code", "execution_count": null, "id": "cfdd015b-bf35-4d4a-8e55-889692c22386", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Y18sKeM-wPnkN4X7NV9eOz1EMWb5R_imY0xgWrjwO-g\"\n", "sheet_name = \"special_days_raw\"\n", "special_days_raw = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "41b97fcb-0745-420c-982a-585ec9eaeedb", "metadata": {}, "outputs": [], "source": ["special_days_raw = special_days_raw.dropna(how=\"any\")"]}, {"cell_type": "code", "execution_count": null, "id": "a79729eb-2d31-422b-a1cc-4c5af5edbbaa", "metadata": {}, "outputs": [], "source": ["special_days_raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c8cb0b65-4e18-48d0-a3ce-d2ee783fd35b", "metadata": {}, "outputs": [], "source": ["special_days_raw.tail(2)"]}, {"cell_type": "code", "execution_count": null, "id": "8d4270d2-7dae-4c9b-a4a2-6f2b26d8b413", "metadata": {}, "outputs": [], "source": ["special_days = special_days_raw[\n", "    [\"date_\", \"year\", \"event_code\", \"event_name\", \"event_count\", \"region\"]\n", "]\n", "special_days = special_days[special_days[\"date_\"] != \"\"]\n", "special_days = special_days.reset_index(drop=True)\n", "special_days[\"date_\"] = pd.to_datetime(special_days[\"date_\"])\n", "special_days[\"year\"] = special_days[\"year\"].astype(\"int\")\n", "special_days[\"event_code\"] = special_days[\"event_code\"].astype(\"int\")\n", "special_days[\"event_name\"] = special_days[\"event_name\"].astype(\"str\")\n", "special_days[\"event_count\"] = special_days[\"event_count\"].astype(\"int\")\n", "special_days[\"region\"] = special_days[\"region\"].astype(\"str\")\n", "special_days[\"updated_at\"] = current_datetime"]}, {"cell_type": "code", "execution_count": null, "id": "3d8ec442-56d3-40bf-8f7c-d6b5712cd6a3", "metadata": {}, "outputs": [], "source": ["special_days.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f9b69b2c-eb5d-4fb4-9069-f9cae91366c5", "metadata": {}, "outputs": [], "source": ["special_days.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "c65e0f77-1545-4e81-a6c9-42a85d07b6c6", "metadata": {}, "outputs": [], "source": ["check_df = (\n", "    special_days[[\"year\", \"event_code\", \"event_name\", \"date_\"]]\n", "    .groupby(by=[\"year\", \"event_code\", \"event_name\"])\n", "    .count()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a90e7854-3c08-410c-af3c-11afabd21056", "metadata": {}, "outputs": [], "source": ["check_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5f94067c-baf7-42cf-9c8b-991eca4d8033", "metadata": {}, "outputs": [], "source": ["check_df[check_df[\"date_\"] == 2]"]}, {"cell_type": "code", "execution_count": null, "id": "d5eac73a-2e97-4986-9120-4b122bb01292", "metadata": {}, "outputs": [], "source": ["kwargs_output = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"blinkit_inventory_calendar\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"year\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"event_code\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"event_name\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"event_count\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"region\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"year\",\n", "        \"event_code\",\n", "        \"event_name\",\n", "    ],\n", "    # \"sortkey\": [\"at_date_ist\",\"traits__city_name\",\"keyword\"],\n", "    # \"incremental_key\": \"tag_name\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"blinkit_inventory_calendar\",\n", "}\n", "\n", "pb.to_trino(special_days, **kwargs_output)\n", "print(\"Data Push Complete !! Cools\")"]}, {"cell_type": "code", "execution_count": null, "id": "c94726ee-dd4f-4816-b858-8c35c3d3e699", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}