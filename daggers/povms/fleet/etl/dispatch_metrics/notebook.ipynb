{"cells": [{"cell_type": "code", "execution_count": null, "id": "5e7b483f-b8e1-49eb-aaaa-b198d8867e54", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "7b992b6e-a7cd-4e52-8e34-6e07f9f78f99", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e256a0c9-ed4d-4d5a-bfa2-b7fe9e9ab7a1", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "0a6f91f9-56ea-476d-a1af-6ed7a10c68a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e3656a0f-6b7f-4a20-a547-6db3b26ded88", "metadata": {"tags": []}, "outputs": [], "source": ["forward_query = f\"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   --type,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '17' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)\n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "   \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          --type,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           --n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '17' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "    GROUP BY 1,2,3,4,5,6,7,t.metadata\n", "    ),\n", "        \n", "docs AS\n", "    (\n", "    SELECT  t.id AS consignment_id,\n", "            count(DISTINCT d.external_id) AS num_invoices,\n", "            count(DISTINCT c.external_id) AS total_crates_dispatched\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "    JOIN transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '17' DAY) as varchar)\n", "      AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)\n", "      AND c.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)\n", "    GROUP BY 1\n", "    ),\n", "   \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    ),\n", "       \n", "ctype AS\n", "  (\n", "  SELECT consignment_id,\n", "         item_type AS con_type\n", "   FROM\n", "     (SELECT *,\n", "             RANK () OVER (PARTITION BY consignment_id ORDER BY type_count DESC) AS rnk\n", "      FROM\n", "        (SELECT DISTINCT c.id AS consignment_id,\n", "                         (CASE\n", "                              WHEN ids.perishable = 1 THEN 'Perishable'\n", "                              ELSE 'Grocery'\n", "                          END) AS item_type,\n", "                         count(CASE\n", "                                  WHEN ids.perishable = 1 THEN 'Perishable'\n", "                                  ELSE 'Grocery'\n", "                              END) AS type_count,\n", "                sum(pipd.quantity) as dispatched_qty,\n", "                sum(pipd.quantity*pp.weight_in_gm) as dispatched_qty_weight,\n", "                sum(pipd.quantity*ids.length_in_cm*ids.breadth_in_cm*ids.height_in_cm) as indent_volume\n", "         FROM transit_server.transit_consignment c\n", "         JOIN transit_server.transit_consignment_document d ON c.id = d.consignment_id\n", "         JOIN pos.pos_invoice pi ON pi.invoice_id = d.external_id\n", "         JOIN pos.pos_invoice_product_details pipd ON pipd.invoice_id = pi.id\n", "         JOIN rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "         JOIN rpc.item_details ids ON ids.item_id = pp.item_id\n", "         WHERE c.insert_ds_ist >= cast((CURRENT_DATE - interval '17' DAY) AS varchar)\n", "           AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) AS varchar)\n", "           AND pi.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) AS varchar)\n", "           and pipd.insert_ds_ist >= cast((CURRENT_DATE - interval '18' DAY) AS varchar)\n", "           AND d.document_type = 'INVOICE'\n", "         GROUP BY 1,2)\n", "                 )\n", "   WHERE rnk = 1 \n", "   ),\n", "\n", "final_base as\n", "    (\n", "    SELECT base.consignment_id,\n", "           --type,\n", "           con_type,\n", "           tm.trip_id,\n", "           cast((tm.truck_handshake + interval '330' MINUTE) as date) AS consignment_date,\n", "           CASE\n", "               WHEN facility_id = 1743 THEN 264\n", "               WHEN facility_id = 4306 THEN 1983\n", "               ELSE facility_id\n", "           END AS facility_id,\n", "           --facility_name,\n", "           ds_outlet_id,\n", "           ds_outlet_name,\n", "           truck_number,\n", "           truck_type,\n", "           tm.driver_id,\n", "           tm.driver_name,\n", "           tm.driver_mobile,\n", "           tm.trip_state,\n", "           current_state AS consignment_state,\n", "           num_invoices,\n", "           total_crates_dispatched,\n", "           (dispatch_time + interval '240' MINUTE) AS scheduled_wh_picking,\n", "           (dispatch_time + interval '270' MINUTE) AS scheduled_wh_billing,\n", "           (dispatch_time + interval '300' MINUTE) AS scheduled_wh_sortation,\n", "           (dispatch_time + interval '240' MINUTE) AS reporting_threshold,\n", "           (dispatch_time + interval '285' MINUTE) AS loading_start_threshold,\n", "           \n", "           (tav.install_ts + interval '330' minute) as checkin_time_of_previous_trip,\n", "           (dispatch_time + interval '270' MINUTE) AS scheduled_truck_arrival,\n", "           (tm.truck_entry_wh + interval '330' MINUTE) AS truck_entry_wh,\n", "           (tm.truck_handshake + interval '330' MINUTE) AS truck_handshake,\n", "           (loading_start + interval '330' MINUTE) AS loading_start,\n", "           (ready_for_dispatch + interval '330' MINUTE) AS ready_for_dispatch,\n", "           (enroute + interval '330' MINUTE) AS enroute,\n", "           (dispatch_time + interval '330' MINUTE) AS scheduled_dispatch_time,\n", "           dispatch_time + interval '345' minute as exp_dispatch_time,\n", "           (ds_reached + interval '330' MINUTE) AS ds_reached\n", "           \n", "    FROM base\n", "    left join docs on docs.consignment_id = base.consignment_id\n", "    LEFT JOIN ctype ON ctype.consignment_id = base.consignment_id\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    LEFT JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "    left join transit_server.transit_allocation_vehicle tav on tav.trip_id = tm.trip_id and activity_type = 'LOADING' \n", "    and insert_ds_ist >= cast((current_date - interval '18' day) as varchar)\n", "    ),\n", "    \n", "facility_outlet_mapping as\n", "(\n", "select 337 as outlet_id, 1 as facility_id \n", "union all\n", "select 1577 as outlet_id, 517 as facility_id \n", "union all\n", "select 1725 as outlet_id, 603 as facility_id \n", "union all\n", "select 2653 as outlet_id, 1206 as facility_id \n", "union all\n", "select 4165 as outlet_id, 1872 as facility_id \n", "union all\n", "select 4170 as outlet_id, 1873 as facility_id \n", "union all\n", "select 1104 as outlet_id, 264 as facility_id \n", "union all\n", "select 1644 as outlet_id, 555 as facility_id \n", "union all\n", "select 481 as outlet_id, 43 as facility_id \n", "union all\n", "select 2904 as outlet_id, 1320 as facility_id \n", "union all\n", "select 4181 as outlet_id, 1876 as facility_id \n", "union all\n", "select 4325 as outlet_id, 2006 as facility_id \n", "union all\n", "select 714 as outlet_id, 92 as facility_id \n", "union all\n", "select 4306 as outlet_id, 1983 as facility_id \n", "union all\n", "select 4330 as outlet_id, 2010 as facility_id \n", "union all\n", "select 4375 as outlet_id, 2015 as facility_id \n", "union all\n", "select 4427 as outlet_id, 2076 as facility_id \n", "union all\n", "select 4432 as outlet_id, 2078 as facility_id \n", "union all\n", "select 4496 as outlet_id, 2123 as facility_id \n", "union all\n", "select 4516 as outlet_id, 2141 as facility_id\n", "),\n", "    \n", "time_gaps_base as\n", "    (\n", "    select  final_base.*,\n", "            fom.outlet_id as wh_outlet_id,\n", "            name as wh_outlet_name,\n", "    date_diff('minute', loading_start, ready_for_dispatch) as loading_time_at_wh,\n", "    date_diff('minute', exp_dispatch_time, ready_for_dispatch) as dispatch_delay_min,\n", "    case when checkin_time_of_previous_trip > reporting_threshold then 'reporting_delay' else 'reporting_ontime' \n", "    end as vehicle_report_status,\n", "    case when loading_start > loading_start_threshold then 'load_start_delay' else 'load_start_ontime' \n", "    end as load_start_status\n", "    from final_base\n", "    left join facility_outlet_mapping fom on fom.facility_id = final_base.facility_id\n", "    left join retail.console_outlet co on co.id = fom.outlet_id\n", "    )\n", "    \n", "select  base.*,\n", "        case when trim(co.location)='Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "        \n", "        CAST(scheduled_dispatch_time AS time) as sch_dispatch_time,\n", "        CAST(scheduled_dispatch_time AS date) as sch_dispatch_date,\n", "        (CASE\n", "              WHEN hour(CAST(scheduled_dispatch_time AS time)) < 12 THEN 'Morning'\n", "              ELSE 'Evening'\n", "        END) AS shift\n", "        \n", "\n", "from time_gaps_base base\n", "left join lake_retail.console_outlet co on co.id = base.ds_outlet_id\n", "\n", "\n", "where UPPER(wh_outlet_name) not like '%%PC%%'\n", "  and trip_state <> 'CANCELLED'\n", "  and con_type = 'Grocery'\n", "  and truck_type not like '%%Reefer%%'\n", "  and scheduled_dispatch_time is not null\n", "         \n", "\"\"\"\n", "\n", "frwd_df = read_sql_query(forward_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "ae3c23c9-c61d-4c3b-9221-32144941d07e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ea9d6f53-f023-4d3a-836c-2d3ed63bb584", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5417fb83-bf30-463f-9613-73adb217525f", "metadata": {}, "outputs": [], "source": ["frwd_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "91a9cc59-daba-4bbd-94e8-e995abdea750", "metadata": {}, "outputs": [], "source": ["frwd_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "16114e2e-ce9f-4cf6-aba2-5aa16cb0c4cf", "metadata": {}, "outputs": [], "source": ["frwd_df.consignment_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "6757eba0-f8f2-4ab7-8aea-34b559a12942", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4b67abbe-37cc-4341-a331-cf3eb6139e8a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ead30c17-90ee-440c-bd4a-80fbd4b85d14", "metadata": {}, "outputs": [], "source": ["load_query = f\"\"\"\n", "\n", "with load_crate_base as\n", "    (\n", "    select\n", "        consignment_id,\n", "        external_id as crate_id,\n", "        (install_ts + interval '330' minute) as crate_loading_time\n", "    from transit_server.transit_consignment_container\n", "    where insert_ds_ist > cast(current_date - interval '16' day as varchar)\n", "    ),\n", "    \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time + interval '330' minute) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    )\n", "\n", "select  pos.csmt_id as consignment_id,\n", "        count(distinct crate_id) as crates_loaded_till_sdt\n", "from pos\n", "left join load_crate_base lb on lb.consignment_id = pos.csmt_id and lb.crate_loading_time <= pos.dispatch_time\n", "group by 1\n", "\n", "\"\"\"\n", "\n", "load_df = read_sql_query(load_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "3d2f3af7-1d48-499a-8d86-2a5f14bbb858", "metadata": {}, "outputs": [], "source": ["load_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0a1c9047-d5c5-4759-8699-e49ae3323cf0", "metadata": {}, "outputs": [], "source": ["load_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "62eee4d4-f98f-4ebe-8995-3bae04ee32ec", "metadata": {}, "outputs": [], "source": ["frwd_df1 = frwd_df.merge(load_df, how=\"left\", on=\"consignment_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "3a8807a0-e40c-4256-a38d-78f218dc4812", "metadata": {}, "outputs": [], "source": ["frwd_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2720ba04-9c74-4019-b26c-da27fd544625", "metadata": {}, "outputs": [], "source": ["frwd_df1.to_csv(\"frwd_df1.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "9047ef64-03dd-4605-8f5e-f64fcc66e9c3", "metadata": {}, "outputs": [], "source": ["sort_query = f\"\"\"\n", "\n", "with sort_base as\n", "    (\n", "        select  distinct date(sto.created_at) sto_date,\n", "                cmt.outlet_id,\n", "                destination_outlet_id,\n", "                mc.container_id,\n", "                (oc.dispatch_time + interval '330' minute) as dispatch_time,\n", "                (oc.dispatch_time - interval '30' minute) sortation_cut_off,\n", "                mc.updated_at as sorted_at\n", "        \n", "        from wms.migration_container mc\n", "        join wms.container_migration_task cmt on cmt.id=mc.container_migration_task_id and cmt.type = 'DISPATCH_SORTATION'\n", "        join wms.outbound_container oc on oc.outbound_demand_id=json_extract_scalar(mc.meta,'$.demand_id') and oc.billing_entity_type in ('STO_BULK_PICK_LIST','BACKGROUND_BILLING_TASK')\n", "        and oc.container_id = mc.container_id and oc.insert_ds_ist >= cast(current_date - interval '18' day as varchar) and oc.state = 'DISPATCHED'\n", "        join wms.pick_list pl on oc.outbound_demand_id = json_extract_scalar(json_extract(pl.meta,'$.outbound_demand_details'),'$.demand_id') and pl.zone_identifier not like '%%PACKAGING%%'\n", "        join wms.pick_list_item pli on pli.pick_list_id = pl.id and pli.insert_ds_ist >= cast(current_date - interval '18' day as varchar)\n", "        join wms.pick_list_item_order_mapping plio on plio.pick_list_item_id = pli.id \n", "        join po.sto sto on sto.id = cast(plio.order_id as int) \n", "        \n", "        where mc.insert_ds_ist >= cast(current_date - interval '18' day as varchar) \n", "        and mc.state='DROPPED' \n", "        and cmt.type='DISPATCH_SORTATION'\n", "        and cmt.insert_ds_ist >= cast(current_date - interval '18' day as varchar)\n", "        and pl.insert_ds_ist >= cast(current_date - interval '18' day as varchar)\n", "        and sto.created_at >= cast(current_date - interval '18' day as timestamp)\n", "        and plio.created_at >= cast(current_date - interval '18' day as timestamp)\n", "        and cmt.outlet_id in (337,481,714,1104,1644,1725,2653,2904,4165,4170,4181,4306,4325,4330,4375,4427,4432,4496,4516)\n", "        -- and consignment_id = 969960 --in (965352,969960)\n", "    )\n", "\n", "select  dispatch_time as scheduled_dispatch_time,\n", "        destination_outlet_id as ds_outlet_id,\n", "        count(distinct case when sorted_at <= sortation_cut_off then container_id end) as crates_sorted_30_mins_before_SDT,\n", "        count(distinct container_id) as total_crates\n", "from sort_base\n", "group by 1,2\n", "order by 2,1\n", "\n", "\"\"\"\n", "\n", "sort_df = read_sql_query(sort_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "15c4cec4-5cb8-4081-9396-4b73afbfb836", "metadata": {}, "outputs": [], "source": ["sort_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "99dce2cd-8d3c-4359-8040-736029cb2213", "metadata": {}, "outputs": [], "source": ["sort_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "306ea010-61dd-4334-abc7-218b40a215b0", "metadata": {}, "outputs": [], "source": ["frwd_df2 = frwd_df1.merge(\n", "    sort_df, how=\"left\", on=[\"scheduled_dispatch_time\", \"ds_outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35b3963b-2364-4000-ae18-caf6e1c71ef5", "metadata": {}, "outputs": [], "source": ["sort_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "0242190b-e0fb-4c4e-81ab-2b9e3d9cf096", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b14857fe-1df0-4dcd-b67e-a7a69fc30839", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "14ad72b2-f100-4815-8018-8e6f46667153", "metadata": {}, "outputs": [], "source": ["frwd_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1914d31d-9b1c-4dc8-942c-3c702759a7f8", "metadata": {}, "outputs": [], "source": ["frwd_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c621d9e0-d1a4-4cb2-8074-6bbcd20afe8b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a481b501-e489-483c-abcf-3e2bed90a3c7", "metadata": {}, "outputs": [], "source": ["frwd_df2[\"spillover_crates\"] = (\n", "    frwd_df2[\"total_crates_dispatched\"] - frwd_df2[\"total_crates\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c60b73a0-704c-42f8-a6e6-88fc11eb03d3", "metadata": {}, "outputs": [], "source": ["frwd_df2"]}, {"cell_type": "code", "execution_count": null, "id": "0ca746d6-570b-40d0-93ff-af74175ff6f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ad45afb6-ee82-4d92-a7db-d7d886cc0b1c", "metadata": {}, "outputs": [], "source": ["df1 = sqldf(\n", "    \"\"\"\n", "select  *,\n", "        sum(day_spillover_crates) over (partition by facility_id,ds_outlet_id order by consignment_date rows between 9 preceding and current row) as rolling_last_10_days_spillover\n", "from\n", "    (\n", "    select  consignment_date,\n", "            facility_id,\n", "            ds_outlet_id,\n", "            sum(spillover_crates) as day_spillover_crates\n", "    from frwd_df2\n", "    group by 1,2,3\n", "    )\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "81b75715-0c88-4291-ab58-bfc511198ab9", "metadata": {}, "outputs": [], "source": ["data = frwd_df2.merge(\n", "    df1, how=\"left\", on=[\"consignment_date\", \"facility_id\", \"ds_outlet_id\"]\n", ")\n", "data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3d94afaa-ec6b-45be-bab7-a08998749eec", "metadata": {}, "outputs": [], "source": ["data[\"actual_loading_min_per_crate\"] = (\n", "    data[\"loading_time_at_wh\"] / data[\"total_crates_dispatched\"]\n", ")\n", "data[\"ideal_loading_min_per_crate\"] = 60.0 / data[\"total_crates_dispatched\"]"]}, {"cell_type": "code", "execution_count": null, "id": "44a40ff6-75c8-481a-a5d5-a7a594c467c7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d72c51a-0094-4b80-b60c-251dbf879fa9", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "36c6f518-f899-44cd-ba21-f06bfc913d5e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3507a108-5360-45ba-9e44-14befaf8cade", "metadata": {}, "outputs": [], "source": ["# raw_data = data.copy()\n", "# raw_data.to_csv('raw_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d48c1e03-5051-40b9-97ed-9246370e4544", "metadata": {}, "outputs": [], "source": ["# raw_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "319a4069-9fd7-4a7b-b626-90c2bff02b3b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce46da8f-ef03-4cc3-8ad6-25429f8cb71f", "metadata": {}, "outputs": [], "source": ["data[\"consignment_date\"] = pd.to_datetime(data[\"consignment_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "f8ca0cc7-815a-4bb4-88b7-fa4a7333a280", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now()\n", "seven_days_ago = current_date - <PERSON><PERSON><PERSON>(days=7)\n", "\n", "data = data[data[\"consignment_date\"] >= seven_days_ago]"]}, {"cell_type": "code", "execution_count": null, "id": "71dccbb3-f79e-4e29-b24a-23bee8fb6231", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e15d2a36-c29f-4a44-a42c-f9115dde182f", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a82fb26c-db6f-498d-bb6b-26c74242caf1", "metadata": {}, "outputs": [], "source": ["# data.to_csv('raw_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d57a83d5-0a6c-4852-b99d-0676cf79189f", "metadata": {}, "outputs": [], "source": ["fleet_raw = \"1h09o3tSZ8cDaHvun9ROeplLy_JKR1HumUiakq1Y9H2Y\""]}, {"cell_type": "code", "execution_count": null, "id": "97cfd5b7-ecc8-476d-a24d-3b3417fa6b8b", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(data, fleet_raw, \"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "f44e94af-d7b2-434d-9c8b-806e4d643a04", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "49fa9b3f-316c-4111-993d-525024ae2865", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "20d05c6f-98be-4898-9e15-a614610c3c87", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e6a17eda-b0fc-48e5-92b4-83bea854ed9d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d9166958-fe26-47c4-aae4-9a23f5040371", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "22c9686e-47b7-43fa-85dc-81f6f97c7cd4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e8dc894f-0846-43c6-af92-bd531074c743", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "305e4663-63ba-4781-a893-7120e51adb57", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "504b1383-e34f-4ffa-af34-cad661df0cc2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "92ebf310-3d1b-4175-a4cb-a67fc72a6203", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "27a69b31-1e01-44fe-a3bc-14061da8bb8f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "85cd5b8b-650a-44ea-94b4-ec65cf850f6a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f6920058-2f69-44bd-93d1-d7a3f63a40f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7eaef9a0-c663-449f-a867-1fc12821b0d0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "92b4b13d-b25a-4265-8b60-2be0fceeff4c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "987de31f-89dd-4ba3-b802-6de4ec825c09", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f9b1adf7-c1cf-4f56-9990-040c7fa37057", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7df4ca78-59ef-44f1-8996-5eaa1e537821", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}