alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: btransit_trip_refresh
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
  retries: 3
owner:
  email: <EMAIL>
  slack_id: U057LU69XUZ
path: povms/fleet/etl/btransit_trip_refresh
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2024-11-29T00:00:00'
  interval: 0 * * * *
  start_date: '2024-07-29T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
