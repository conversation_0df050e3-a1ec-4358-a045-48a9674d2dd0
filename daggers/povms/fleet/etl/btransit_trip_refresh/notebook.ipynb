{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e3656a0f-6b7f-4a20-a547-6db3b26ded88", "metadata": {"tags": []}, "outputs": [], "source": ["transit_query = f\"\"\"\n", "\n", "WITH td AS\n", "  (SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "   FROM transit_server.transit_consignment t\n", "   JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "   JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "   \n", "   WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   and pc.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   and pt.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   \n", "   GROUP BY 1,2,3,4,5,6\n", "   ),\n", "   \n", "     trip1 AS\n", "  (SELECT trip_id,\n", "          min(truck_handshake) AS truck_handshake\n", "   FROM td\n", "   GROUP BY 1),\n", "   \n", "     trip2 AS\n", "  (SELECT trip_id,\n", "          min(truck_entry_wh) AS truck_entry_wh,\n", "          min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "   FROM\n", "     (SELECT DISTINCT trip_id,\n", "                      CASE\n", "                          WHEN event_type = 'WH_ARRIVAL'\n", "                               AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                          ELSE NULL\n", "                      END AS truck_entry_wh,\n", "                      CASE\n", "                          WHEN event_type = 'WH_ARRIVAL'\n", "                               AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                          ELSE NULL\n", "                      END AS truck_return_wh1,\n", "                      CASE\n", "                          WHEN event_type = 'COMPLETED'\n", "                               AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                          ELSE NULL\n", "                      END AS truck_return_wh2,\n", "                      CASE\n", "                          WHEN event_type = 'COMPLETED'\n", "                               AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                          ELSE NULL\n", "                      END AS truck_return_wh3\n", "      FROM transit_server.transit_projection_trip_event_timeline\n", "      WHERE trip_id IN\n", "          (SELECT trip_id\n", "           FROM trip1)\n", "      and insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)       \n", "         )\n", "         \n", "   GROUP BY 1\n", "   \n", "   ),\n", "     trip_main AS\n", "  (SELECT consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "   FROM td\n", "   LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "   LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "   ),\n", "     base AS\n", "  (SELECT t.id AS consignment_id,\n", "          t.state AS current_state,\n", "          coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "          n.external_name AS facility_name,\n", "          m.outlet_id AS ds_outlet_id,\n", "          co.name AS ds_outlet_name,\n", "          upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "          cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "          max(CASE\n", "                  WHEN (to_state='LOADING') THEN tl.install_ts\n", "              END) AS loading_start,\n", "          max(CASE\n", "                  WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts\n", "              END) AS ready_for_dispatch,\n", "          max(CASE\n", "                  WHEN (to_state='ENROUTE') THEN tl.install_ts\n", "              END) AS enroute,\n", "          max(CASE\n", "                  WHEN (to_state='REACHED') THEN tl.install_ts\n", "              END) AS ds_reached,\n", "          max(CASE\n", "                  WHEN (to_state='UNLOADING') THEN tl.install_ts\n", "              END) AS unloading_start,\n", "          max(CASE\n", "                  WHEN (to_state='COMPLETED') THEN tl.install_ts\n", "              END) AS unloading_completed\n", "   FROM transit_server.transit_consignment t\n", "   JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "   JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "   JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "   AND m.active = TRUE\n", "   AND m.id NOT IN (611,\n", "                    1538)\n", "   JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "   LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "                      facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,\n", "                                 12,\n", "                                 19,\n", "                                 20) ) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "   WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   and tl.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            t.metadata\n", "        ),\n", "        \n", "     docs AS\n", "  (SELECT t.id AS consignment_id,\n", "          count(DISTINCT d.external_id) AS num_invoices,\n", "          count(DISTINCT c.external_id) AS num_containers\n", "   FROM transit_server.transit_consignment t\n", "   JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "   JOIN transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "   WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   and d.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   and c.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "   GROUP BY 1\n", "   ),\n", "   \n", "     pos AS\n", "  (\n", "  SELECT DISTINCT td.consignment_id AS csmt_id,\n", "                   max(s.dispatch_time) AS dispatch_time\n", "   FROM pos.pos_invoice pi\n", "   JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "   JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "   JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "   AND co.business_type_id IN (1,\n", "                               12,\n", "                               20)\n", "   WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "   and td.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "     AND invoice_type_id IN (5,\n", "                             14,\n", "                             16)\n", "   GROUP BY 1\n", "   ),\n", "   \n", "     grn AS\n", "  (\n", "  SELECT cast(er.external_order_no as int) AS c_id,\n", "          min(a.created_date) AS grn_started_at,\n", "          max(a.created_date) AS grn_completed_at\n", "   FROM storeops.er er\n", "   JOIN storeops.activity a ON a.er_id = er.id\n", "   WHERE er.created_date > cast((CURRENT_DATE - interval '14' DAY) AS TIMESTAMP)\n", "     AND er.er_type = 'BULKIN_CONSIGNMENT'\n", "     AND er.external_order_no IN\n", "       (SELECT DISTINCT cast(consignment_id AS varchar)\n", "        FROM base\n", "        )\n", "   GROUP BY 1\n", "   \n", "   ),\n", "     grn1 AS\n", "  (\n", "  SELECT DISTINCT t.id AS c_id,\n", "                   min(il.pos_timestamp) AS grn_started_at,\n", "                   max(il.pos_timestamp) AS grn_completed_at\n", "   FROM transit_server.transit_consignment t\n", "   JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "   JOIN ims.ims_inventory_log il ON il.merchant_invoice_id = d.external_id\n", "   and il.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "   WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "   and d.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "     AND inventory_update_type_id IN (1,\n", "                                      28,\n", "                                      76,\n", "                                      90,\n", "                                      93)\n", "   GROUP BY 1\n", "   ),\n", "   \n", "    excpected_timelines AS (\n", "    \n", "         WITH base_ AS (\n", "         \n", "             select distinct \n", "                trip_id,\n", "                consignment_id,\n", "                event_type,\n", "                (last_value(scheduled_ts) over(PARTITION BY trip_id,consignment_id,event_type ORDER BY update_ts ROWS BETWEEN unbounded preceding AND unbounded following)) + interval '330' MINUTE AS scheduled_ts, \n", "                (last_value(expected_ts) over(PARTITION BY trip_id,consignment_id,event_type ORDER BY update_ts ROWS BETWEEN unbounded preceding AND unbounded following)) + interval '330' MINUTE AS expected_ts\n", "                                                                            \n", "            from (\n", "                    SELECT DISTINCT trip_id,\n", "                          cast(json_extract(event_meta, '$.consignment_id') as varchar) AS consignment_id,\n", "                          event_type,\n", "                          scheduled_ts,\n", "                          update_ts,\n", "                          expected_ts\n", "                          \n", "                    FROM transit_server.transit_projection_trip_event_timeline AS tp\n", "                    WHERE event_type IN ('DS_ARRIVAL',\n", "                                       'UNLOADING_END',\n", "                                       'COMPLETED')\n", "                    AND insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) AS varchar) \n", "                    )\n", "                \n", "            )\n", "            \n", "            ,wh_return AS\n", "         (SELECT trip_id,\n", "                 max(CASE\n", "                         WHEN event_type = 'COMPLETED' THEN expected_ts\n", "                         ELSE NULL\n", "                     END) AS exp_truck_return_wh,\n", "                 max(CASE\n", "                         WHEN event_type = 'COMPLETED' THEN scheduled_ts\n", "                         ELSE NULL\n", "                     END) AS sch_truck_return_wh\n", "          FROM base_\n", "          GROUP BY 1),\n", "            ds_details AS\n", "         (SELECT trip_id,\n", "                 consignment_id,\n", "                 max(CASE\n", "                         WHEN event_type = 'DS_ARRIVAL' THEN expected_ts\n", "                         ELSE NULL\n", "                     END) AS exp_ds_reach,\n", "                 max(CASE\n", "                         WHEN event_type = 'DS_ARRIVAL' THEN scheduled_ts\n", "                         ELSE NULL\n", "                     END) AS sch_ds_reach,\n", "                 max(CASE\n", "                         WHEN event_type = 'UNLOADING_END' THEN expected_ts\n", "                         ELSE NULL\n", "                     END) AS exp_unloading_complete,\n", "                 max(CASE\n", "                         WHEN event_type = 'UNLOADING_END' THEN scheduled_ts\n", "                         ELSE NULL\n", "                     END) AS sch_unloading_complete\n", "          FROM base_\n", "          WHERE consignment_id!=''\n", "          GROUP BY 1,\n", "                   2) SELECT DISTINCT wr.trip_id,\n", "                                      dd.consignment_id,\n", "                                      exp_ds_reach,\n", "                                      sch_ds_reach,\n", "                                      exp_unloading_complete,\n", "                                      sch_unloading_complete,\n", "                                      exp_truck_return_wh,\n", "                                      sch_truck_return_wh\n", "        FROM wh_return AS wr\n", "        LEFT JOIN ds_details AS dd ON wr.trip_id = dd.trip_id\n", "        ),\n", "        \n", "     ctype AS\n", "  (\n", "  SELECT consignment_id,\n", "          item_type AS con_type\n", "   FROM\n", "     (SELECT *,\n", "             RANK () OVER (PARTITION BY consignment_id\n", "                           ORDER BY type_count DESC) AS rnk\n", "      FROM\n", "        (SELECT DISTINCT c.id AS consignment_id,\n", "                         (CASE\n", "                              WHEN ids.perishable = 1 THEN 'Perishable'\n", "                              ELSE 'Grocery'\n", "                          END) AS item_type,\n", "                         count(CASE\n", "                                   WHEN ids.perishable = 1 THEN 'Perishable'\n", "                                   ELSE 'Grocery'\n", "                               END) AS type_count\n", "         FROM transit_server.transit_consignment c\n", "         JOIN transit_server.transit_consignment_document d ON c.id = d.consignment_id\n", "         JOIN pos.pos_invoice pi ON pi.invoice_id = d.external_id\n", "         JOIN pos.pos_invoice_product_details pipd ON pipd.invoice_id = pi.id\n", "         JOIN rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "         JOIN rpc.item_details ids ON ids.item_id = pp.item_id\n", "         WHERE c.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) AS varchar)\n", "           AND pi.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) AS varchar)\n", "           and pipd.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) AS varchar)\n", "           AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) AS varchar)\n", "           AND d.document_type = 'INVOICE'\n", "         GROUP BY 1,\n", "                  2)\n", "                 )\n", "   WHERE rnk = 1 \n", "   )\n", "   \n", "SELECT base.consignment_id,\n", "       ctype.con_type,\n", "       tm.trip_id,\n", "       cast((tm.truck_handshake + interval '330' MINUTE) as date) AS consignment_date,\n", "       CASE\n", "           WHEN facility_id = 1743 THEN 264\n", "           WHEN facility_id = 4306 THEN 1983\n", "           ELSE facility_id\n", "       END AS facility_id,\n", "       facility_name,\n", "       ds_outlet_id,\n", "       ds_outlet_name,\n", "       truck_number,\n", "       truck_type,\n", "       tm.driver_id,\n", "       tm.driver_name,\n", "       tm.driver_mobile,\n", "       tm.trip_state,\n", "       current_state AS consignment_state,\n", "       num_invoices,\n", "       num_containers,\n", "       null as total_trip_distance_km,\n", "       (dispatch_time + interval '255' MINUTE) AS scheduled_truck_arrival,\n", "       (tm.truck_entry_wh + interval '330' MINUTE) AS truck_entry_wh,\n", "       (tm.truck_handshake + interval '330' MINUTE) AS truck_handshake,\n", "       (loading_start + interval '330' MINUTE) AS loading_start,\n", "       (ready_for_dispatch + interval '330' MINUTE) AS ready_for_dispatch,\n", "       (enroute + interval '330' MINUTE) AS enroute,\n", "       (dispatch_time + interval '330' MINUTE) AS scheduled_dispatch_time,\n", "       (ds_reached + interval '330' MINUTE) AS ds_reached,\n", "       exp_ds_reach,\n", "       sch_ds_reach,\n", "       (unloading_start + interval '330' MINUTE) AS unloading_start,\n", "       (unloading_completed + interval '330' MINUTE) AS unloading_completed,\n", "       exp_unloading_complete,\n", "       sch_unloading_complete,\n", "       CASE \n", "            WHEN current_state = 'COMPLETED' \n", "                THEN (coalesce(grn.grn_started_at,grn1.grn_started_at) + interval '330' MINUTE)\n", "                ELSE NULL\n", "        END AS grn_started_at,\n", "        \n", "        CASE \n", "            WHEN current_state = 'COMPLETED' \n", "                THEN (coalesce(grn.grn_completed_at,grn1.grn_completed_at) + interval '330' MINUTE) \n", "                ELSE NULL\n", "        END AS grn_completed_at,\n", "        \n", "       (tm.truck_return_wh + interval '330' MINUTE) AS truck_return_wh,\n", "       exp_truck_return_wh,\n", "       sch_truck_return_wh,\n", "       \n", "       CASE\n", "           WHEN date_diff('minute', (dispatch_time + interval '270' MINUTE), truck_entry_wh) <= 0 THEN 0\n", "           ELSE date_diff('minute', (dispatch_time + interval '270' MINUTE), truck_entry_wh)\n", "       END AS truck_arrival_delay_min,\n", "       \n", "       date_diff('minute',truck_handshake,loading_start) AS handshake_to_loading_min,\n", "       date_diff('minute',loading_start,ready_for_dispatch) AS total_scan_min,\n", "       date_diff('minute',ready_for_dispatch,enroute) AS driver_delay_min,\n", "       date_diff('minute',loading_start,enroute) AS loading_time_min,\n", "       \n", "       CASE\n", "           WHEN date_diff('minute', (dispatch_time + interval '330' MINUTE), enroute) <= 0 THEN 0\n", "           ELSE date_diff('minute', (dispatch_time + interval '330' MINUTE), enroute)\n", "       END AS dispatch_delay_min,\n", "       \n", "       CASE\n", "           WHEN ds_reached > exp_ds_reach THEN date_diff('minute',exp_ds_reach,ds_reached)\n", "           ELSE 0\n", "       END AS ds_reach_delay_from_expected,\n", "       \n", "       CASE\n", "           WHEN ds_reached > sch_ds_reach THEN date_diff('minute',sch_ds_reach,ds_reached)\n", "           ELSE 0\n", "       END AS ds_reach_delay_from_scheduled,\n", "       \n", "       date_diff('minute',unloading_start,unloading_completed) AS unloading_time_min,\n", "       date_diff('minute',truck_entry_wh,truck_return_wh) AS truck_time_at_wh,\n", "       date_diff('minute',unloading_completed,truck_return_wh) AS return_transit_truck_time\n", "FROM base\n", "left join docs on docs.consignment_id = base.consignment_id\n", "LEFT JOIN ctype ON ctype.consignment_id = base.consignment_id\n", "JOIN pos ON base.consignment_id = pos.csmt_id\n", "LEFT JOIN grn ON grn.c_id = base.consignment_id\n", "LEFT JOIN grn1 ON grn1.c_id = base.consignment_id\n", "LEFT JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "LEFT JOIN excpected_timelines AS et ON tm.trip_id = et.trip_id\n", "AND cast(base.consignment_id as varchar) = et.consignment_id\n", "ORDER BY 4,\n", "         1 DESC\n", "         \n", "\"\"\"\n", "\n", "transit_df = pd.read_sql_query(sql=transit_query, con=trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "62aea4be-2c3c-4b65-9b02-88972febab15", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1A7QPwKVUc6Y_MdBr9D6y1KJqxRar2HFgFSTVEgqVKqM\"\n", "pb.to_sheets(transit_df, sheet_id, \"Transit Tracker FC (Last 14 days)\")"]}, {"cell_type": "code", "execution_count": null, "id": "2e86f866-8969-4a9d-93a6-b7f8f087666d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}