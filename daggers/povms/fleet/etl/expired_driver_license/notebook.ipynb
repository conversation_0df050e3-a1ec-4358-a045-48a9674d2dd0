{"cells": [{"cell_type": "code", "execution_count": null, "id": "2c1f0e3f-25f7-4528-9959-43e027d49813", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "3484ad0d-250e-4275-82b8-a1e188787eaa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "83b19126-50be-46d8-a289-ea6be43537d5", "metadata": {}, "outputs": [], "source": ["# Connecting with Trino\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "b0151ecc-3d73-4567-b93a-45891b354278", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=4.2,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "markdown", "id": "85332ce7-f6a4-40f3-a314-950b99e60a37", "metadata": {}, "source": ["### driver license data"]}, {"cell_type": "code", "execution_count": null, "id": "3dd24e7c-8476-4a9b-b333-1697b2df7776", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with active_users as (\n", "    SELECT \n", "        tu.phone, max(tt.shipment_label_value) as trip_id\n", "    FROM \n", "        transit_server.transit_user tu\n", "    JOIN \n", "        transit_server.transit_user_profile tup \n", "        ON tup.user_id = tu.id\n", "    JOIN \n", "        transit_server.transit_task tt \n", "        ON cast(tt.user_profile_id as int) = tup.id\n", "    \n", "    WHERE tt.shipment_label_type = 'TRIP_ID'\n", "    and tt.install_ts >= current_date - interval '2' day\n", "    \n", "    group by 1\n", "    having count(distinct tt.shipment_label_value) > 0\n", ")\n", "\n", ", license_info as (\n", "\n", "SELECT  \n", "    fmu.old_employee_id,\n", "    fmudoc.number as driver_license_number,\n", "    cast(replace( json_query(fmudoc.meta_data,'lax $.thru_date') ,'\"','') as date) as driver_license_expiry_date\n", "    \n", "FROM fleet_management.fleet_management_user fmu\n", "JOIN fleet_management.fleet_management_user_document fmudoc ON fmu.id = fmudoc.user_id\n", "where fmudoc.type = 'DRIVING_LICENSE'\n", ")\n", "\n", "\n", "SELECT  \n", "fmu.old_employee_id as driver_employee_id,\n", "fmu.name,\n", "fmu.phone,\n", "driver_license_number,\n", "driver_license_expiry_date,\n", "au.trip_id \n", "\n", "FROM \n", "    fleet_management.fleet_management_user fmu\n", "join \n", "    fleet_management.fleet_management_user_detail as fmud\n", "    on fmu.id = fmud.user_id \n", "    and fmud.employee_type = 'TRUCK_DRIVER'\n", "left JOIN \n", "    fleet_management.fleet_management_user_address fmua \n", "    ON fmua.user_id = fmu.id\n", "left join \n", "    license_info as li \n", "    on fmu.old_employee_id = li.old_employee_id\n", "join \n", "    active_users as au\n", "    on fmu.phone = au.phone\n", "    \n", "where driver_license_expiry_date <= current_date\n", "\n", "\"\"\"\n", "\n", "license_df = pd.read_sql_query(sql=query, con=trino_connection)\n", "license_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "051291c0-efe4-479e-8a24-44bba1d9243d", "metadata": {}, "outputs": [], "source": ["if not license_df.empty:\n", "\n", "    query = \"\"\"\n", "\n", "    create temporary table old_app_data as (\n", "\n", "    SELECT \n", "        td.id::varchar AS trip_id,\n", "        td.source_outlet_name\n", "\n", "    FROM lake_vendor_console.fleet_trip_details td\n", "    INNER JOIN lake_vendor_console.fleet_consignment fc ON td.id=fc.trip_id\n", "    inner join lake_vendor_console.fleet_consignment_invoice_mapping as im on fc.id = im.consignment_id\n", "    AND td.created_by_id NOT IN ('**********', '**********')\n", "    and date(CONVERT_TIMEZONE('Asia/Kolkata', td.created_at)) >= (CURRENT_DATE - 7) \n", "    and fc.started_at is not null\n", "    AND fc.id NOT IN (4)\n", "    group by 1,2\n", "\n", "    );\n", "\n", "    create temporary table new_app_data as (\n", "    WITH base AS\n", "      (SELECT t.id AS consignment_id,\n", "                 t.state AS current_state,\n", "                 trip.trip_id,\n", "                 t.source_store_id::int AS facility_id,\n", "                 n.external_name AS facility_name\n", "\n", "          FROM lake_transit_server.transit_consignment t\n", "          JOIN lake_transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "          JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "          JOIN lake_transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "          JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "          JOIN lake_retail.console_outlet_logistic_mapping m ON m.logistic_node_id = t.destination_store_id\n", "          JOIN lake_retail.console_outlet co ON co.id = m.outlet_id\n", "          JOIN\n", "            (SELECT DISTINCT t.id AS consignment_id,\n", "                             tsl.label_value AS trip_id\n", "             FROM lake_transit_server.transit_consignment t\n", "             JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "             JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "             AND tsl.label_type = 'TRIP_ID'\n", "             WHERE t.install_ts >= cast((CURRENT_DATE - 7) AS TIMESTAMP)) tc ON tc.consignment_id = t.id\n", "          JOIN\n", "            (SELECT tsl.label_value AS trip_id,\n", "                    min(q.entry_ts) AS truck_entry_wh,\n", "                    min(tts.install_ts) AS truck_return_wh,\n", "                    min(t.install_ts) AS truck_handshake\n", "             FROM lake_transit_server.transit_consignment t\n", "             JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "             JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "             AND tsl.label_type = 'TRIP_ID'\n", "             JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value\n", "             AND tt.shipment_label_type = 'TRIP_ID'\n", "             LEFT JOIN lake_transit_server.task_management_taskstatelog tts ON tts.task_id = tt.id\n", "             AND tts.to_state = 'COMPLETED'\n", "             JOIN lake_transit_server.transit_express_allocation_field_executive_queue q ON (q.user_profile_id || '_' || q.id) = tsl.label_value\n", "             WHERE t.install_ts >= cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "             GROUP BY 1) trip ON trip.trip_id = tc.trip_id\n", "          WHERE t.install_ts >= cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "          GROUP BY 1,\n", "                   2,\n", "                   3,\n", "                   4,\n", "                   5),\n", "         pos AS\n", "      (SELECT DISTINCT td.consignment_id AS csmt_id,\n", "                       max(s.dispatch_time) AS dispatch_time\n", "       FROM lake_pos.pos_invoice pi\n", "       JOIN lake_transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "       JOIN lake_po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "       JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id\n", "       AND co.business_type_id IN (1,\n", "                                   12)\n", "       WHERE pi.created_at >= cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "         AND invoice_type_id IN (5,\n", "                                 14,\n", "                                 16)\n", "       GROUP BY 1)\n", "\n", "    SELECT \n", "           trip_id,\n", "           facility_name\n", "\n", "    FROM base\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    group by 1,2\n", "    );\n", "\n", "    select * from new_app_data\n", "    union all\n", "    select * from old_app_data\n", "\n", "\n", "    \"\"\"\n", "\n", "    bt_df = pd.read_sql_query(sql=query, con=redshift_connection)\n", "    bt_df.columns = [\"trip_id\", \"last_trip_facility\"]\n", "\n", "    final_df = license_df.merge(bt_df, on=\"trip_id\", how=\"left\")\n", "    final_df = final_df[\n", "        [\n", "            \"driver_employee_id\",\n", "            \"name\",\n", "            \"phone\",\n", "            \"driver_license_number\",\n", "            \"driver_license_expiry_date\",\n", "            \"last_trip_facility\",\n", "        ]\n", "    ]\n", "    final_df.head()\n", "\n", "    fig, ax = render_mpl_table(final_df)\n", "    fig.savefig(\"summary.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "6139063c-6be9-4090-8d2c-da292bae0de5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c0385077-ecd0-4166-bd26-f32dc1577e15", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "baf3ccd3-80e8-40dc-a1ee-d356c714e8a5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}