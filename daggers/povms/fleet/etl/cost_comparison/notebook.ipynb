{"cells": [{"cell_type": "code", "execution_count": null, "id": "2c1f0e3f-25f7-4528-9959-43e027d49813", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "from datetime import timedelta\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "83b19126-50be-46d8-a289-ea6be43537d5", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "6b676327-5819-4d0d-bff4-59f024340c7f", "metadata": {"tags": []}, "outputs": [], "source": ["raw_query = \"\"\" \n", " with trip_base as (\n", "\n", "SELECT DISTINCT \n", "           pt.trip_id,\n", "           coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as trip_creation_time,\n", "           cast( coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as date) as trip_creation_date,\n", "           pt.source_node_id,\n", "           pc.consignment_id,\n", "           pc.destination_id,\n", "           co1.id as wh_outlet_id,\n", "           case \n", "            when coalesce(co1.facility_id,cast(t.source_store_id as int)) = 1743 then 264\n", "            else coalesce(co1.facility_id,cast(t.source_store_id as int))\n", "           end as facility_id,\n", "           n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "        --   UPPER(split_part(cast(json_extract(t.metadata,'$.truck_number') AS varchar),'/',1)) AS truck_number,\n", "           pt.truck_number,\n", "           case \n", "                when (cast(json_extract(t.metadata,'$.vehicle_type') as varchar)) = '' or (cast(json_extract(t.metadata,'$.vehicle_type') as varchar) is null)\n", "                then cast(json_extract(pt.truck_meta,'$.vehicle_type') as varchar)\n", "                else cast(json_extract(t.metadata,'$.vehicle_type') as varchar)\n", "           end AS truck_type\n", "                       \n", "                   \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    \n", "    LEFT JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "   JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "   LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "                      facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12,19,20) \n", "    ) AS co1 \n", "    \n", "    ON cast(co1.id as varchar) = t.source_store_id\n", "    \n", "    left join \n", "        (\n", "            select distinct trip_id, (actual_ts + interval '330' MINUTE) as trip_creation_time_1\n", "            from transit_server.transit_projection_trip_event_timeline\n", "            where insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "            and event_type = 'WH_DISPATCH'\n", "            and actual_ts is not null\n", "        ) as pte\n", "    on pt.trip_id = pte.trip_id\n", "    \n", "    WHERE pt.insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "    and t.insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "    and pc.insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "    -- cast((CURRENT_DATE - interval '14' DAY) as timestamp)\n", "    and pt.state in ('COMPLETED','CANCELLED','EXPIRED', 'WH_DISPATCH', 'DS_ARRIVAL', 'UNLOADING_START', 'UNLOADING_END', 'DS_DISPATCH')\n", "    and cast(json_extract(pt.source_node_meta,'$.store_type') AS varchar)!= 'HYPERPURE'\n", "    and (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "\n", ")\n", "\n", ", route_base as (\n", "\n", "    select distinct \n", "        truck_number,\n", "        cast(source_node_id as varchar) as source_node_id,\n", "        destination_node_id,\n", "        billable_destination_city,\n", "        truck_billing_type,\n", "        truck_type,\n", "        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "        vendor_id,\n", "        name as vendor_name,\n", "        cast(json_extract(tvm.metadata,'$.km_slab') AS int) as km_slab,\n", "        cast(json_extract(tvm.metadata,'$.hr_slab') AS int) as hr_slab,\n", "        expected_trips_per_day\n", "        \n", "        \n", "    from fleet_management.fleet_management_truck_vendor_mapping as tvm\n", "    join fleet_management.fleet_management_node_billable_city_mapping as bcm\n", "    on tvm.id = bcm.truck_vendor_mapping_id\n", "    left join \n", "        fleet_management.fleet_management_hiring_source as hs\n", "        on hs.id = tvm.vendor_id and active = True\n", "    where tvm.is_active= True\n", "    and bcm.is_active= True\n", "    and source_node_id is not null\n", ")\n", "\n", "\n", ", trip_vendor_base as (\n", "\n", "    select distinct\n", "        tb.trip_id,\n", "        cast(tb.trip_creation_time as date) as trip_creation_date,\n", "        tb.trip_creation_time,\n", "        rb.source_node_id,\n", "        tb.consignment_id,\n", "        rb.billable_destination_city,\n", "        tb.destination_id,\n", "        tb.truck_number,\n", "        rb.truck_type,\n", "        rb.vendor_id,\n", "        rb.vendor_name,\n", "        rb.truck_billing_type,\n", "        rb.km_slab,\n", "        rb.hr_slab,\n", "        rb.expected_trips_per_day,\n", "        tb.wh_outlet_id,\n", "        tb.facility_id,\n", "        tb.facility_name,\n", "        tb.ds_outlet_id,\n", "        tb.ds_outlet_name\n", "                       \n", "    from trip_base as tb\n", "    left join route_base as rb\n", "    \n", "    on tb.truck_number = rb.truck_number \n", "    -- and tb.source_node_id = rb.source_node_id\n", "    and tb.destination_id = rb.destination_node_id\n", "    and (tb.trip_creation_time >= rb.tenure_start and tb.trip_creation_time< rb.tenure_end)\n", "    \n", "    -- where rb.truck_number is not null\n", ")\n", "\n", ",vendor_rate_card as (\n", "\n", "    with base_charge as ( \n", "    \n", "        with km_base_charge as (\n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                p.value as km_slab,\n", "                p.rule_id,\n", "                output as base_cost\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and attribute_name = 'km'\n", "            and rule.rule_name = 'BASE_PAY'\n", "            \n", "        )\n", "        \n", "        , hr_base_charge as (\n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                p.value as hr_slab,\n", "                p.rule_id,\n", "                output as base_cost\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and attribute_name = 'hour'\n", "            and rule.rule_name = 'BASE_PAY'\n", "            \n", "        )\n", "        \n", "        select distinct kb.*, hb.hr_slab\n", "        from km_base_charge as kb\n", "        join hr_base_charge as hb\n", "        on kb.vendor_rate_card_config_id = hb.vendor_rate_card_config_id and kb.base_cost = hb.base_cost and kb.rule_id = hb.rule_id\n", "    )\n", "\n", "\n", "    , extra_charge as (\n", "\n", "        with km_extra_charge as (\n", "        \n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                output as extra_cost_per_km\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and rule.rule_name = 'ADDITIONAL_COST_PER_KM'\n", "            \n", "        )\n", "        \n", "        , hr_extra_charge as (\n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                output as extra_cost_per_hr\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and rule.rule_name = 'ADDITIONAL_COST_PER_HR'\n", "            \n", "        )\n", "        \n", "        select distinct ke.*, he.extra_cost_per_hr\n", "        from km_extra_charge as ke\n", "        join hr_extra_charge as he\n", "        on ke.vendor_rate_card_config_id = he.vendor_rate_card_config_id \n", "    )\n", "\n", "    select distinct \n", "        bc.source_node_id,\n", "        bc.source_name,\n", "        bc.destination_city,\n", "        bc.vendor_id,\n", "        bc.vendor_name,\n", "        bc.truck_type,\n", "        bc.truck_billing_type,\n", "        bc.base_cost,\n", "        bc.tenure_start,\n", "        bc.tenure_end,\n", "        bc.km_slab,\n", "        bc.hr_slab,\n", "        ec.extra_cost_per_hr,\n", "        ec.extra_cost_per_km\n", "        \n", "    from base_charge as bc\n", "    left join extra_charge as ec\n", "    on bc.vendor_rate_card_config_id = ec.vendor_rate_card_config_id\n", "\n", "\n", ")\n", "\n", ", extra_charges_base as (\n", "    select \n", "        trip_id,\n", "        sum(case when attribute_type = 'EXTRA_HOURS' then cast(value as decimal) end) as extra_hours,\n", "        sum(case when attribute_type = 'MISCELLANEOUS_CHARGES' then cast(value as decimal) end) as misc_charge\n", "       \n", "    from fleet_management.fleet_management_trip_adhoc_costs_attributes\n", "    group by 1\n", ")\n", "\n", ", tolls_base as (\n", "\n", "    select distinct\n", "        source_node_id, \n", "        destination_node_id,\n", "        truck_type,\n", "        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "        tolls_cost\n", "    \n", "    from fleet_management.fleet_management_tolls_cost\n", "    where is_active=true\n", ")\n", "\n", ", cancelled_enroute as (\n", "    \n", "    select distinct trip_id \n", "    from transit_server.transit_projection_trip_event_timeline \n", "    where actual_ts IS NULL \n", "    and event_type = 'WH_DISPATCH'\n", "    and insert_ds_ist between cast('2024-02-01' as varchar) and  cast(CURRENT_DATE as varchar)\n", ")\n", "\n", ", same_day_trip_count_base as (\n", "\n", "    SELECT  \n", "        cast(trip_creation_time as date) as trip_creation_date,\n", "        truck_number,\n", "        count(distinct trip_id) as trips\n", "    from trip_base\n", "    group by 1,2   \n", ")\n", "\n", ", discarded_trips as (\n", "\n", "    select distinct trip_id\n", "    from fleet_management.fleet_management_trips_cost\n", "    where status = 'DISCARDED'\n", "    and insert_ds_ist >= cast('2024-02-01' as varchar)\n", ")\n", "\n", ", pre_final as (\n", "\n", "select distinct\n", "    vb.trip_id,\n", "    vb.trip_creation_date,\n", "    vb.trip_creation_time,\n", "    \n", "    vb.facility_id,\n", "    -- vb.source_node_id,\n", "    vb.facility_name as wh_name,\n", "    \n", "    vb.consignment_id,\n", "    vb.billable_destination_city as destination_city,\n", "    \n", "    vb.ds_outlet_id as outlet_id,\n", "    vb.ds_outlet_name as destination_name,\n", "    \n", "    vb.truck_number,\n", "    vb.truck_type,\n", "    -- vb.vendor_id,\n", "    vb.vendor_name,\n", "    vb.truck_billing_type,\n", "    vb.km_slab,\n", "    vb.hr_slab,\n", "    vb.expected_trips_per_day,\n", "    \n", "    vr.base_cost,\n", "    vr.extra_cost_per_hr,\n", "    vr.extra_cost_per_km,\n", "    tb.tolls_cost/count(consignment_id) over(partition by vb.trip_id) as tolls_cost,\n", "    case when ce.trip_id is not null then 1 else 0 end as cancelled_before_enroute,\n", "    sd.trips as same_day_trips,\n", "    case \n", "        when vb.truck_billing_type = 'FIXED' then cast(base_cost as decimal)/day(last_day_of_month(vb.trip_creation_date))\n", "        else cast(base_cost as decimal)\n", "    end as per_day_cost,\n", "    \n", "    cast(extra_cost_per_hr as decimal)*ec.extra_hours as extra_hr_cost,\n", "    count(consignment_id) over(partition by vb.trip_id) as consign_num\n", "    \n", "from \n", "    trip_vendor_base as vb\n", "left join\n", "    vendor_rate_card as vr\n", "    on vb.vendor_id = vr.vendor_id and vb.source_node_id = cast(vr.source_node_id as varchar)\n", "    and vb.billable_destination_city = vr.destination_city and vb.truck_type = vr.truck_type \n", "    and (vb.trip_creation_time >= vr.tenure_start and vb.trip_creation_time< vr.tenure_end)\n", "    and cast(vb.km_slab as varchar) = vr.km_slab and cast(vb.hr_slab as varchar) = vr.hr_slab \n", "    and vb.truck_billing_type = vr.truck_billing_type\n", "    \n", "left join \n", "    tolls_base as tb\n", "    on vb.source_node_id = tb.source_node_id and vb.destination_id = tb.destination_node_id\n", "    and vb.truck_type = tb.truck_type \n", "    and (vb.trip_creation_time >= tb.tenure_start and vb.trip_creation_time< tb.tenure_end)\n", "    \n", "left join\n", "    cancelled_enroute as ce\n", "    on vb.trip_id = ce.trip_id\n", "\n", "left join \n", "    same_day_trip_count_base as sd\n", "    on vb.trip_creation_date = sd.trip_creation_date and vb.truck_number = sd.truck_number\n", "    \n", "left join \n", "    extra_charges_base as ec\n", "    on vb.trip_id = ec.trip_id\n", "\n", "left join discarded_trips as dt\n", "    on vb.trip_id = dt.trip_id\n", "\n", "where dt.trip_id is null\n", "    \n", ")\n", "\n", "\n", ", final as (\n", "\n", "select *,\n", "\n", "    case\n", "        when (expected_trips_per_day < 1 and expected_trips_per_day > 0) then per_day_cost/(expected_trips_per_day *consign_num)\n", "        when same_day_trips <= expected_trips_per_day then per_day_cost/(expected_trips_per_day *consign_num)\n", "        else per_day_cost/(same_day_trips*consign_num)\n", "    end as fixed_cost,\n", "    \n", "    case \n", "        when cancelled_before_enroute = 1 then extra_hr_cost\n", "        else extra_hr_cost + tolls_cost\n", "    end as misc_cost\n", "\n", "from pre_final \n", ")\n", "\n", "\n", "\n", "select distinct  *, \n", "    (fixed_cost + coalesce(tolls_cost,0)) as total_cost_tolls,\n", "    (fixed_cost + coalesce(tolls_cost,0) + coalesce(misc_cost,0)) as total_cost\n", "from final\n", "\n", "\"\"\"\n", "\n", "raw_df = pd.read_sql_query(sql=raw_query, con=trino_connection)\n", "raw_df[[\"fixed_cost\", \"misc_cost\", \"total_cost_tolls\", \"total_cost\"]] = raw_df[\n", "    [\"fixed_cost\", \"misc_cost\", \"total_cost_tolls\", \"total_cost\"]\n", "].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "de6dd306-e701-40dd-bc75-92efb86567bd", "metadata": {}, "outputs": [], "source": ["agg_df = (\n", "    raw_df.groupby(\n", "        [\n", "            \"trip_creation_date\",\n", "            \"facility_id\",\n", "            \"wh_name\",\n", "            \"outlet_id\",\n", "            \"destination_name\",\n", "        ]\n", "    )[[\"fixed_cost\", \"misc_cost\", \"total_cost_tolls\", \"total_cost\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "agg_df[[\"facility_id\", \"outlet_id\"]] = agg_df[[\"facility_id\", \"outlet_id\"]].astype(str)\n", "agg_df.insert(\n", "    0,\n", "    \"concat\",\n", "    agg_df[\"trip_creation_date\"]\n", "    + \"-\"\n", "    + agg_df[\"facility_id\"]\n", "    + \"-\"\n", "    + agg_df[\"outlet_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "22c91928-404b-4a2c-bd1b-eb38ee053b19", "metadata": {"tags": []}, "outputs": [], "source": ["pb.to_sheets(raw_df, \"19fMh7bY1AhHuB8bKoRjv2ZQr5Az1ehCNaV2HXhD4PUE\", \"new_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "faf751c8-1f81-4de4-98be-6ff762af0ca4", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    raw_df, \"1o1EI6mv7F3NIMmwPTQzxAbiQufYWDgA-O-r8K4q52t4\", \"Master Mapping backlog Raw\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7195231d-e142-43c1-8325-0f6f936f1b98", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(agg_df, \"1f38imk21OucC7d7V2_3JT8JP8I7DvAeMfbjMgmB9FMs\", \"new aggregate\")"]}, {"cell_type": "code", "execution_count": null, "id": "aede42b6-160e-4fb9-a9b1-da4e2cf0b8ec", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}