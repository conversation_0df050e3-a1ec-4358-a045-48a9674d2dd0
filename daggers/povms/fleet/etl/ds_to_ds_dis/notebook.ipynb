{"cells": [{"cell_type": "code", "execution_count": null, "id": "714e844c-a496-425a-a1a0-ad40d2779b84", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import date, timedelta\n", "import time\n", "import json\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "id": "a8d21195-caf3-4a56-b525-35a99ddbd313", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "51056226-2fdf-464d-a3c2-c77292549811", "metadata": {}, "outputs": [], "source": ["trino_schema_name = \"supply_etls\"\n", "trino_table_name = \"ds_to_ds_distance\""]}, {"cell_type": "markdown", "id": "c7413d28-3779-454e-a14a-874849ae4cf7", "metadata": {"tags": []}, "source": ["### Fetching DS coords"]}, {"cell_type": "code", "execution_count": null, "id": "cf3db4fe-221b-4f1c-a505-48b0062c057f", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct node.external_id as frontend_merchant_id,\n", "x.outlet_id,\n", "y.facility_id,\n", "y.name, \n", "latitude,\n", "longitude\n", "-- cast(latitude as varchar) as latitude, \n", "-- cast(longitude as varchar) as longitude\n", "\n", "from transit_server.transit_node node \n", "join transit_server.transit_node_address add on node.node_address_id = add.id\n", "\n", "join (\n", "    select distinct logistic_node_id, outlet_id, name\n", "    from retail.console_outlet_logistic_mapping\n", "    where active =true\n", ") as x\n", "\n", "ON cast(x.logistic_node_id as varchar) = node.external_id \n", "\n", " join (\n", "\n", "    select distinct \n", "        id, \n", "        facility_id,\n", "        name\n", "        \n", "    from retail.console_outlet \n", "    where active =1\n", "    and facility_id is not null\n", "    and id is not null\n", "    and id!= 1638\n", "    -- and facility_id!= 806\n", "    and business_type_id = 7\n", "    \n", ") as y\n", "\n", "on y.id = x.outlet_id\n", "\n", "where node.type not in ('CPC','FC','PC')\n", "and not (node.external_id= '32360' and outlet_id= 3702)\n", "and not (node.external_id= '32210' and outlet_id= 4252)\n", "and not (node.external_id= '31015' and outlet_id= 2805)\n", "-- and node.active = true\n", "\"\"\"\n", "\n", "ds_coord_df = pd.read_sql_query(sql=query, con=trino_connection)\n", "ds_coord_df.columns = [\n", "    \"ds_merchant_id\",\n", "    \"ds_outlet_id\",\n", "    \"ds_facility_id\",\n", "    \"ds_name\",\n", "    \"ds_lat\",\n", "    \"ds_lon\",\n", "]\n", "ds_coord_df[\"ds_lat\"] = ds_coord_df[\"ds_lat\"].astype(str)\n", "ds_coord_df[\"ds_lon\"] = ds_coord_df[\"ds_lon\"].astype(str)"]}, {"cell_type": "markdown", "id": "8998ebaa-df54-4b40-a2ed-8b8449951e7d", "metadata": {}, "source": ["### fetching old DS"]}, {"cell_type": "code", "execution_count": null, "id": "c78d959f-e250-40fc-b124-0adac2cd3bad", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct ds_outlet_id\n", "from \n", "(\n", "    select ds_outlet_id as ds_outlet_id\n", "    from supply_etls.wh_to_ds_distance\n", "    union\n", "    select source_ds_outlet_id as ds_outlet_id\n", "    from supply_etls.ds_to_ds_distance\n", ")\n", "\"\"\"\n", "old_ds = pd.read_sql_query(sql=query, con=trino_connection)"]}, {"cell_type": "markdown", "id": "41d5abe5-d3c6-46f6-b5ea-3e2ae896ff55", "metadata": {}, "source": ["### finding the new opened DS"]}, {"cell_type": "code", "execution_count": null, "id": "9e14db79-39fe-45ba-8ab5-fbaee3d6ff42", "metadata": {}, "outputs": [], "source": ["unique_ids = old_ds[\"ds_outlet_id\"].unique()\n", "df_coord_df_new = ds_coord_df[\n", "    ~ds_coord_df[\"ds_outlet_id\"].isin(unique_ids)\n", "].reset_index(drop=True)\n", "\n", "new_ds_ids = df_coord_df_new[\"ds_outlet_id\"].unique()\n", "\n", "mask = ds_coord_df[\"ds_outlet_id\"].isin(new_ds_ids)\n", "ds_coord_df_old = ds_coord_df[~mask]"]}, {"cell_type": "code", "execution_count": null, "id": "6d77de43-c02a-4d78-ae40-0f26b770300e", "metadata": {}, "outputs": [], "source": ["base_df = df_coord_df_new.merge(ds_coord_df_old, how=\"cross\")"]}, {"cell_type": "code", "execution_count": null, "id": "4afb1358-a7b7-43fa-8a5f-966ddef4dc22", "metadata": {}, "outputs": [], "source": ["base_df.columns = [\n", "    \"source_ds_merchant_id\",\n", "    \"source_ds_outlet_id\",\n", "    \"source_ds_facility_id\",\n", "    \"source_ds_name\",\n", "    \"source_ds_lat\",\n", "    \"source_ds_lon\",\n", "    \"dest_ds_merchant_id\",\n", "    \"dest_ds_outlet_id\",\n", "    \"dest_ds_facility_id\",\n", "    \"dest_ds_name\",\n", "    \"dest_ds_lat\",\n", "    \"dest_ds_lon\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "435d2907-72ef-4e85-b60a-7299768bee28", "metadata": {}, "outputs": [], "source": ["base_df[\"source_lat_lon\"] = (\n", "    \"[\" + base_df[\"source_ds_lat\"] + \",\" + base_df[\"source_ds_lon\"] + \"]\"\n", ")\n", "base_df[\"dest_lat_lon\"] = (\n", "    \"[\" + base_df[\"dest_ds_lat\"] + \",\" + base_df[\"dest_ds_lon\"] + \"]\"\n", ")\n", "base_df[\"combined\"] = (\n", "    \"[\" + base_df[\"source_lat_lon\"] + \",\" + base_df[\"dest_lat_lon\"] + \"]\"\n", ")\n", "base_df[\"combined\"] = base_df[\"combined\"].apply(lambda x: json.loads(x))\n", "base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a86226ad-185c-4867-99de-0d75f22d6cef", "metadata": {}, "outputs": [], "source": ["OSRM_URL = \"http://osrm-service-data.prod-sgp-k8s.grofer.io\"\n", "\n", "\n", "def osrm_table_url_formatter(coords, url, profile=\"car\"):\n", "    statement = (\n", "        url + \"/table/v1/\" + profile + \"/\" + coords + \"?annotations=distance,duration\"\n", "    )\n", "\n", "    try:\n", "        r = requests.get(statement)\n", "        # time.sleep(0.03)\n", "    except Exception:\n", "        raise Exception(\"Spin up the OSRM server on \", url)\n", "    return r.json()\n", "\n", "\n", "def get_inter_order_distance(locations):\n", "    # source is warehouse\n", "    source = locations[0]\n", "    # destination is darkstore\n", "    destination = locations[1]\n", "\n", "    coords = f\"{source[1]},{source[0]};{destination[1]},{destination[0]}\"\n", "    response = osrm_table_url_formatter(coords, OSRM_URL, \"car\")\n", "\n", "    try:\n", "        distance = response[\"distances\"][0][1]\n", "        duration = response[\"durations\"][0][1]\n", "    except:\n", "        return 0\n", "\n", "    return distance / 1000, duration / 60"]}, {"cell_type": "code", "execution_count": null, "id": "741adc7f-792a-4473-9507-ef27f051e3dc", "metadata": {}, "outputs": [], "source": ["base_df.sort_values([\"source_ds_merchant_id\", \"dest_ds_merchant_id\"], inplace=True)\n", "base_df = base_df.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d2aca826-f82a-46d3-b03d-a55cf6572cea", "metadata": {}, "outputs": [], "source": ["if not base_df.empty:\n", "    base_df[[\"distance_km\", \"time_min\"]] = (\n", "        base_df[\"combined\"].apply(get_inter_order_distance).apply(pd.Series)\n", "    )\n", "\n", "    base_df = base_df[\n", "        [\n", "            \"source_ds_merchant_id\",\n", "            \"source_ds_outlet_id\",\n", "            \"source_ds_facility_id\",\n", "            \"source_ds_name\",\n", "            \"source_ds_lat\",\n", "            \"source_ds_lon\",\n", "            \"dest_ds_merchant_id\",\n", "            \"dest_ds_outlet_id\",\n", "            \"dest_ds_facility_id\",\n", "            \"dest_ds_name\",\n", "            \"dest_ds_lat\",\n", "            \"dest_ds_lon\",\n", "            \"distance_km\",\n", "            \"time_min\",\n", "        ]\n", "    ]\n", "\n", "    base_df = base_df.astype(\n", "        {\n", "            \"source_ds_merchant_id\": \"int\",\n", "            \"source_ds_outlet_id\": \"int\",\n", "            \"source_ds_facility_id\": \"int\",\n", "            \"source_ds_lat\": \"float\",\n", "            \"source_ds_lon\": \"float\",\n", "            \"dest_ds_merchant_id\": \"int\",\n", "            \"dest_ds_outlet_id\": \"int\",\n", "            \"dest_ds_facility_id\": \"int\",\n", "            \"dest_ds_facility_id\": \"int\",\n", "            \"dest_ds_lat\": \"float\",\n", "            \"dest_ds_lon\": \"float\",\n", "            \"distance_km\": \"float\",\n", "            \"time_min\": \"float\",\n", "        }\n", "    )\n", "\n", "    kwargs = {\n", "        \"schema_name\": trino_schema_name,\n", "        \"table_name\": trino_table_name,\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"source_ds_merchant_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"source_ds_merchant_id\",\n", "            },\n", "            {\n", "                \"name\": \"source_ds_outlet_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"source_ds_outlet_id\",\n", "            },\n", "            {\n", "                \"name\": \"source_ds_facility_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"source_ds_facility_id\",\n", "            },\n", "            {\n", "                \"name\": \"source_ds_name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"source_ds_name\",\n", "            },\n", "            {\"name\": \"source_ds_lat\", \"type\": \"DOUBLE\", \"description\": \"source_ds_lat\"},\n", "            {\"name\": \"source_ds_lon\", \"type\": \"DOUBLE\", \"description\": \"source_ds_lon\"},\n", "            {\n", "                \"name\": \"dest_ds_merchant_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"dest_ds_merchant_id\",\n", "            },\n", "            {\n", "                \"name\": \"dest_ds_outlet_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"dest_ds_outlet_id\",\n", "            },\n", "            {\n", "                \"name\": \"dest_ds_facility_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"dest_ds_facility_id\",\n", "            },\n", "            {\"name\": \"dest_ds_name\", \"type\": \"varchar\", \"description\": \"dest_ds_name\"},\n", "            {\"name\": \"dest_ds_lat\", \"type\": \"DOUBLE\", \"description\": \"dest_ds_lat\"},\n", "            {\"name\": \"dest_ds_lon\", \"type\": \"DOUBLE\", \"description\": \"dest_ds_lon\"},\n", "            {\n", "                \"name\": \"distance_km\",\n", "                \"type\": \"DOUBLE\",\n", "                \"description\": \"distance between ds and ds in km\",\n", "            },\n", "            {\n", "                \"name\": \"time_min\",\n", "                \"type\": \"DOUBLE\",\n", "                \"description\": \"time from wds and ds in min\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\n", "            \"source_ds_facility_id\",\n", "            \"source_ds_lat\",\n", "            \"source_ds_lon\",\n", "            \"dest_ds_facility_id\",\n", "            \"dest_ds_lat\",\n", "            \"dest_ds_lon\",\n", "            \"distance_km\",\n", "        ],\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"\"\"This table contains distance & duration between wh and ds\"\"\",\n", "    }\n", "\n", "    pb.to_trino(base_df, **kwargs)\n", "\n", "    final_query = \"\"\"\n", "    select *\n", "    from supply_etls.ds_to_ds_distance\n", "    \"\"\"\n", "\n", "    final_df = pd.read_sql_query(sql=final_query, con=trino_connection)\n", "\n", "    final_df = final_df.sort_values(\n", "        [\n", "            \"source_ds_facility_id\",\n", "            \"source_ds_outlet_id\",\n", "            \"dest_ds_facility_id\",\n", "            \"dest_ds_outlet_id\",\n", "            \"distance_km\",\n", "        ],\n", "        ascending=[True, True, True, True, False],\n", "    ).reset_index(drop=True)\n", "\n", "    final_df = (\n", "        final_df.groupby(\n", "            [\n", "                \"source_ds_facility_id\",\n", "                \"source_ds_outlet_id\",\n", "                \"dest_ds_facility_id\",\n", "                \"dest_ds_outlet_id\",\n", "            ]\n", "        )\n", "        .first()\n", "        .reset_index()\n", "    )\n", "\n", "    final_df = final_df[\n", "        [\n", "            \"source_ds_merchant_id\",\n", "            \"source_ds_outlet_id\",\n", "            \"source_ds_facility_id\",\n", "            \"source_ds_name\",\n", "            \"source_ds_lat\",\n", "            \"source_ds_lon\",\n", "            \"dest_ds_merchant_id\",\n", "            \"dest_ds_outlet_id\",\n", "            \"dest_ds_facility_id\",\n", "            \"dest_ds_name\",\n", "            \"dest_ds_lat\",\n", "            \"dest_ds_lon\",\n", "            \"distance_km\",\n", "            \"time_min\",\n", "        ]\n", "    ].reset_index(drop=True)\n", "\n", "    kwargs = {\n", "        \"schema_name\": trino_schema_name,\n", "        \"table_name\": trino_table_name,\n", "        \"column_dtypes\": [\n", "            {\n", "                \"name\": \"source_ds_merchant_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"source_ds_merchant_id\",\n", "            },\n", "            {\n", "                \"name\": \"source_ds_outlet_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"source_ds_outlet_id\",\n", "            },\n", "            {\n", "                \"name\": \"source_ds_facility_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"source_ds_facility_id\",\n", "            },\n", "            {\n", "                \"name\": \"source_ds_name\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"source_ds_name\",\n", "            },\n", "            {\"name\": \"source_ds_lat\", \"type\": \"DOUBLE\", \"description\": \"source_ds_lat\"},\n", "            {\"name\": \"source_ds_lon\", \"type\": \"DOUBLE\", \"description\": \"source_ds_lon\"},\n", "            {\n", "                \"name\": \"dest_ds_merchant_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"dest_ds_merchant_id\",\n", "            },\n", "            {\n", "                \"name\": \"dest_ds_outlet_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"dest_ds_outlet_id\",\n", "            },\n", "            {\n", "                \"name\": \"dest_ds_facility_id\",\n", "                \"type\": \"INTEGER\",\n", "                \"description\": \"dest_ds_facility_id\",\n", "            },\n", "            {\"name\": \"dest_ds_name\", \"type\": \"varchar\", \"description\": \"dest_ds_name\"},\n", "            {\"name\": \"dest_ds_lat\", \"type\": \"DOUBLE\", \"description\": \"dest_ds_lat\"},\n", "            {\"name\": \"dest_ds_lon\", \"type\": \"DOUBLE\", \"description\": \"dest_ds_lon\"},\n", "            {\n", "                \"name\": \"distance_km\",\n", "                \"type\": \"DOUBLE\",\n", "                \"description\": \"distance between ds and ds in km\",\n", "            },\n", "            {\n", "                \"name\": \"time_min\",\n", "                \"type\": \"DOUBLE\",\n", "                \"description\": \"time from wds and ds in min\",\n", "            },\n", "        ],\n", "        \"primary_key\": [],  # list\n", "        \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"\"\"This table contains distance & duration between wh and ds\"\"\",\n", "    }\n", "\n", "    pb.to_trino(final_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "9fb20ca5-72a7-4892-9067-7d20c6db79b9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}