{"cells": [{"cell_type": "code", "execution_count": null, "id": "cb3369cb-9da1-4a7c-9da1-adbf03d9a3a4", "metadata": {}, "outputs": [], "source": ["# !pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "cc2f445d-a366-47b3-81c6-ef758383e1ab", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "# from pandasql import sqldf\n", "import datetime\n", "import calendar\n", "from pytz import timezone\n", "from datetime import datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "0a96cd1b-7c23-4901-a692-19596305f57f", "metadata": {}, "outputs": [], "source": ["trino_schema_name = \"supply_etls\"  ##supply_etls\n", "trino_table_name = \"fleet_cms_cost\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "633b1154-0c64-4832-b285-6f4b95fc0251", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "490e8a76-9cec-4acf-91f9-8c9696afcded", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7dfc5910-0792-4e69-a89f-e737c8cfb612", "metadata": {}, "outputs": [], "source": ["end_date = \"2025-02-05\""]}, {"cell_type": "code", "execution_count": null, "id": "d3663e81-456f-41b2-9a6b-d595783d004e", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "     with cost_base as\n", "    (\n", "    select  cte.*,\n", "            cast(trim('][' from (array_join(split(value, ','), ','))) as varchar) AS ds_node_id,\n", "            cast(json_extract(cost_breakdown,'$.extra_hr_cost') as int) AS extra_hr_cost,\n", "            cast(json_extract(cost_breakdown,'$.extra_km_cost') as int) AS extra_km_cost,\n", "            cast(json_extract(cost_breakdown,'$.extra_driver_cost_for_trip') as int) AS extra_driver_cost_for_trip\n", "    from fleet_management.fleet_management_trips_cost cte\n", "    cross join UNNEST(destination_node_ids) as t(value)\n", "    where insert_ds_ist  between cast('2024-11-28' as varchar) and '2025-02-10'\n", "    --insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "      and status <> 'DISCARDED'\n", "    ),\n", "    \n", "-- to join on trip_id and ds_outlet_id and get consignment_id\n", "con_data as\n", "    (\n", "    select  consignment_id,\n", "            trip_id,\n", "            consignment_type,\n", "            pc.source_id,\n", "            pc.destination_id,\n", "            state,\n", "            m.outlet_id as ds_outlet_id,\n", "            m.name\n", "    from transit_server.transit_projection_consignment pc\n", "    left join retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = pc.destination_id\n", "    where insert_ds_ist between cast('2024-11-28' as varchar) and '2025-02-10'\n", "    --insert_ds_ist >= cast(current_date - interval '65' day as varchar)\n", "      and consignment_type = 'FORWARD_CONSIGNMENT'\n", "    )\n", "\n", "select  max(cast(coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as date)) as trip_creation_date,\n", "        cd.consignment_id,\n", "        cte.*\n", "from\n", "    (   \n", "    select  tc.trip_id,\n", "            tc.source_node_id as wh_node_id,\n", "            m1.outlet_id as wh_outlet_id,\n", "            co1.facility_id as wh_facility_id,\n", "            m1.name as wh_outlet_name,\n", "            ds_node_id as ds_node_id,\n", "            m2.outlet_id as ds_outlet_id,\n", "            m2.name as ds_outlet_name,\n", "            case when trim(co2.location) = 'Bangalore' then 'Bengaluru' else trim(co2.location) end as destination_city,\n", "            tc.truck_number,\n", "            tc.truck_type,\n", "            billable_truck_type,\n", "            truck_billing_type,\n", "            km_slab as total_km_travelled,\n", "            hr_slab as total_hr_travelled,\n", "            cast(json_extract(vendor_details,'$.id') as varchar) as vendor_id,\n", "            cast(json_extract(vendor_details,'$.name') as varchar) as vendor_name,\n", "            fixed_cost,\n", "            misc_charges,\n", "            tolls_cost,\n", "            extra_hr_cost,\n", "            extra_km_cost,\n", "            extra_driver_cost_for_trip,\n", "            fixed_cost + misc_charges as total_trip_cost,\n", "            status,\n", "           (fixed_cost + misc_charges)/count(*) over (partition by tc.trip_id) as total_con_cost\n", "    from cost_base tc\n", "    left join retail.console_outlet_logistic_mapping m1 ON cast(m1.logistic_node_id as varchar) = tc.source_node_id\n", "    left join retail.console_outlet_logistic_mapping m2 ON cast(m2.logistic_node_id as varchar) = tc.ds_node_id\n", "    left join retail.console_outlet co1 ON co1.id = m1.outlet_id and co1.name not like '%%HOT%%' and co1.name <> 'K3 T K4' --and co.business_type_id in (1,12,19,20)\n", "    left join retail.console_outlet co2 ON co2.id = m2.outlet_id \n", "    where m1.lake_active_record\n", "      and m2.lake_active_record\n", "    ) cte\n", "left join\n", "        (\n", "            select distinct trip_id, (actual_ts + interval '330' MINUTE) as trip_creation_time_1\n", "            from transit_server.transit_projection_trip_event_timeline\n", "            where \n", "            insert_ds_ist  between cast('2024-11-28' as varchar) and '2025-02-10'\n", "            --insert_ds_ist >= cast((current_date - interval '65' day) as varchar)\n", "            and event_type = 'LOADING_START'\n", "            and actual_ts is not null\n", "            and cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT'\n", "            and lake_active_record\n", "        ) as pte on cte.trip_id = pte.trip_id\n", "left join transit_server.transit_projection_trip pt ON pt.trip_id = cte.trip_id\n", "and (pt.insert_ds_ist between '2024-11-28' and '2025-02-10')\n", "--cast('2024-07-28' as varchar) and cast(current_date as varchar))\n", "--and (pt.insert_ds_ist between cast(current_date - interval '65' day as varchar) and cast(current_date as varchar))\n", "left join con_data cd on cd.trip_id = cte.trip_id and cd.ds_outlet_id = cte.ds_outlet_id\n", "\n", "group by 2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f106c672-b321-49f1-ad4d-e8bb0310d957", "metadata": {}, "outputs": [], "source": ["cms_df = read_sql_query(query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "828683c8-cf7a-49c3-b5ef-082dbed1bdb6", "metadata": {}, "outputs": [], "source": ["cms_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4814243d-c8db-45d0-b50a-22e0df69ce67", "metadata": {}, "outputs": [], "source": ["cms_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "913bcede-381f-407d-99f1-24f781ef1d90", "metadata": {}, "outputs": [], "source": ["cms_df[\"trip_creation_date\"] = pd.to_datetime(cms_df[\"trip_creation_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "36d74cd2-9334-4ad8-8a8e-c99c28107e3f", "metadata": {}, "outputs": [], "source": ["cms_df[\"consignment_id\"] = cms_df[\"consignment_id\"].fillna(value=0)"]}, {"cell_type": "code", "execution_count": null, "id": "572108a7-9b02-47f8-9f03-5be2e8f6acb2", "metadata": {}, "outputs": [], "source": ["cms_df[\"consignment_id\"] = cms_df[\"consignment_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "667a6091-9f4e-4742-923f-386f86f227ad", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_node_id\"] = cms_df[\"wh_node_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "757f57f0-9ad8-456a-a769-b2931d2f22bf", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_outlet_id\"] = cms_df[\"wh_outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "da245c39-87eb-473e-860a-d426d6afcac8", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_facility_id\"] = cms_df[\"wh_facility_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "279d7537-7f28-4b96-913f-47b541cfab56", "metadata": {}, "outputs": [], "source": ["cms_df[\"ds_node_id\"] = cms_df[\"ds_node_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "843dad55-318a-4164-a72b-db8528e7480e", "metadata": {}, "outputs": [], "source": ["cms_df[\"vendor_id\"] = cms_df[\"vendor_id\"].fillna(value=0)\n", "\n", "cms_df[\"vendor_id\"] = cms_df[\"vendor_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "16f47c16-ddaa-406b-9997-3159348e4a85", "metadata": {}, "outputs": [], "source": ["cms_df[\"fixed_cost\"] = cms_df[\"fixed_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "f235292e-2d5f-4d58-9241-44de5c3d4005", "metadata": {}, "outputs": [], "source": ["cms_df[\"tolls_cost\"] = cms_df[\"tolls_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "e805c24b-9ccb-44d5-a05b-12db073a1514", "metadata": {}, "outputs": [], "source": ["cms_df[\"misc_charges\"] = cms_df[\"misc_charges\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "9f3dcc8c-7890-4c22-b012-67ce4461a210", "metadata": {}, "outputs": [], "source": ["cms_df[\"total_trip_cost\"] = cms_df[\"total_trip_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "e2239b86-4e24-4736-9127-616b36cc5820", "metadata": {}, "outputs": [], "source": ["cms_df[\"total_con_cost\"] = cms_df[\"total_con_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "aa491135-7ab2-428c-9a03-83b64577d672", "metadata": {}, "outputs": [], "source": ["cms_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "edf3c345-f356-4b98-ae90-c37ed5500403", "metadata": {}, "outputs": [], "source": ["cms_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ec71ca4d-fd9a-465c-bbbb-e6619abe6453", "metadata": {}, "outputs": [], "source": ["cms_df1 = cms_df.groupby([\"trip_id\", \"wh_outlet_id\", \"ds_outlet_id\"], as_index=False).agg(\n", "    {\n", "        \"trip_creation_date\": \"max\",\n", "        \"consignment_id\": \"max\",\n", "        \"wh_node_id\": \"max\",\n", "        \"wh_facility_id\": \"max\",\n", "        \"wh_outlet_name\": \"max\",\n", "        \"ds_node_id\": \"max\",\n", "        \"ds_outlet_name\": \"max\",\n", "        \"destination_city\": \"max\",\n", "        \"truck_number\": \"max\",\n", "        \"truck_type\": \"max\",\n", "        \"billable_truck_type\": \"max\",\n", "        \"truck_billing_type\": \"max\",\n", "        \"total_km_travelled\": \"max\",\n", "        \"total_hr_travelled\": \"max\",\n", "        \"vendor_id\": \"max\",\n", "        \"vendor_name\": \"max\",\n", "        \"fixed_cost\": \"max\",\n", "        \"misc_charges\": \"max\",\n", "        \"tolls_cost\": \"max\",\n", "        \"extra_hr_cost\": \"max\",\n", "        \"extra_km_cost\": \"max\",\n", "        \"extra_driver_cost_for_trip\": \"max\",\n", "        \"total_trip_cost\": \"max\",\n", "        \"status\": \"max\",\n", "        \"total_con_cost\": \"max\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "74b74b87-7490-49ed-a350-6e855c6b0578", "metadata": {}, "outputs": [], "source": ["cms_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "69b1f973-ffc6-42a6-90d5-6224bb20c095", "metadata": {}, "outputs": [], "source": ["cms_df1[cms_df1[\"trip_id\"] == \"21874_990372\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6e683fb9-ffb2-418f-bd7d-cc7835c264c8", "metadata": {}, "outputs": [], "source": ["cms_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "83081e74-bc22-431c-b6af-fcb98c5127bc", "metadata": {}, "outputs": [], "source": ["cms_df1[(cms_df1[\"trip_id\"] == \"13582_867047\") & (cms_df1[\"ds_outlet_id\"] == 4396)]"]}, {"cell_type": "code", "execution_count": null, "id": "5819e8ca-5c98-4ed8-86be-d320f41ec323", "metadata": {}, "outputs": [], "source": ["cms_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f73ece2c-571c-4b0f-bd32-39576968be14", "metadata": {}, "outputs": [], "source": ["cms_df1 = cms_df1[\n", "    [\n", "        \"trip_creation_date\",\n", "        \"consignment_id\",\n", "        \"trip_id\",\n", "        \"wh_node_id\",\n", "        \"wh_outlet_id\",\n", "        \"wh_facility_id\",\n", "        \"wh_outlet_name\",\n", "        \"ds_node_id\",\n", "        \"ds_outlet_id\",\n", "        \"ds_outlet_name\",\n", "        \"destination_city\",\n", "        \"truck_number\",\n", "        \"truck_type\",\n", "        \"billable_truck_type\",\n", "        \"truck_billing_type\",\n", "        \"total_km_travelled\",\n", "        \"total_hr_travelled\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"fixed_cost\",\n", "        \"misc_charges\",\n", "        \"tolls_cost\",\n", "        \"extra_hr_cost\",\n", "        \"extra_km_cost\",\n", "        \"extra_driver_cost_for_trip\",\n", "        \"total_trip_cost\",\n", "        \"status\",\n", "        \"total_con_cost\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4720af03-c6e6-4bcf-93d9-eeaa14d9b5cb", "metadata": {}, "outputs": [], "source": ["cms_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5a0b49a9-ffbb-49a6-981a-a2b0b20ec988", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "008b9ebd-697f-4c29-840e-7cd14f242e92", "metadata": {}, "outputs": [], "source": ["cms_df1 = cms_df1[cms_df1[\"trip_creation_date\"] >= pd.to_datetime(\"2024-12-01\")]"]}, {"cell_type": "code", "execution_count": null, "id": "22729706-b6fe-45b2-ba1f-9de9427a38aa", "metadata": {}, "outputs": [], "source": ["cms_df1.trip_creation_date.min()"]}, {"cell_type": "code", "execution_count": null, "id": "f5f45778-723f-4b97-b50e-fd9c892e5140", "metadata": {}, "outputs": [], "source": ["cms_df1.trip_creation_date.max()"]}, {"cell_type": "code", "execution_count": null, "id": "43dab679-1e79-49da-9c22-b2f54d8a3bfd", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"trip_creation_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Trip creation date\",\n", "        },\n", "        {\"name\": \"consignment_id\", \"type\": \"integer\", \"description\": \"Consignment ID\"},\n", "        {\"name\": \"trip_id\", \"type\": \"varchar\", \"description\": \"Trip Id\"},\n", "        {\"name\": \"wh_node_id\", \"type\": \"integer\", \"description\": \"WH NODE ID\"},\n", "        {\"name\": \"wh_outlet_id\", \"type\": \"integer\", \"description\": \"WH outlet ID\"},\n", "        {\"name\": \"wh_facility_id\", \"type\": \"integer\", \"description\": \"WH facility ID\"},\n", "        {\"name\": \"wh_outlet_name\", \"type\": \"varchar\", \"description\": \"WH Outlet Name\"},\n", "        {\"name\": \"ds_node_id\", \"type\": \"integer\", \"description\": \"DS NODE ID\"},\n", "        {\"name\": \"ds_outlet_id\", \"type\": \"integer\", \"description\": \"DS outlet ID\"},\n", "        {\"name\": \"ds_outlet_name\", \"type\": \"varchar\", \"description\": \"DS outlet name\"},\n", "        {\n", "            \"name\": \"destination_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Destination city\",\n", "        },\n", "        {\"name\": \"truck_number\", \"type\": \"varchar\", \"description\": \"Truck number\"},\n", "        {\"name\": \"truck_type\", \"type\": \"varchar\", \"description\": \"Truck Type\"},\n", "        {\n", "            \"name\": \"billable_truck_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Billable Truck Type\",\n", "        },\n", "        {\n", "            \"name\": \"truck_billing_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Truck Billable Type\",\n", "        },\n", "        {\n", "            \"name\": \"total_km_travelled\",\n", "            \"type\": \"real\",\n", "            \"description\": \"total_km_travelled\",\n", "        },\n", "        {\n", "            \"name\": \"total_hr_travelled\",\n", "            \"type\": \"real\",\n", "            \"description\": \"total_hr_travelled\",\n", "        },\n", "        {\"name\": \"vendor_id\", \"type\": \"integer\", \"description\": \"Vendor ID\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"Vendor name\"},\n", "        {\"name\": \"fixed_cost\", \"type\": \"real\", \"description\": \"Fixed Cost\"},\n", "        {\"name\": \"misc_charges\", \"type\": \"real\", \"description\": \"Fixed Cost\"},\n", "        {\"name\": \"tolls_cost\", \"type\": \"real\", \"description\": \"Tolls Cost\"},\n", "        {\"name\": \"extra_hr_cost\", \"type\": \"real\", \"description\": \"extra_hr_cost\"},\n", "        {\"name\": \"extra_km_cost\", \"type\": \"real\", \"description\": \"extra_km_cost\"},\n", "        {\n", "            \"name\": \"extra_driver_cost_for_trip\",\n", "            \"type\": \"real\",\n", "            \"description\": \"extra_driver_cost_for_trip\",\n", "        },\n", "        {\"name\": \"total_trip_cost\", \"type\": \"real\", \"description\": \"total_trip_cost\"},\n", "        {\"name\": \"status\", \"type\": \"varchar\", \"description\": \"status\"},\n", "        {\"name\": \"total_con_cost\", \"type\": \"real\", \"description\": \"total_con_cost\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"trip_id\",\n", "        \"wh_outlet_id\",\n", "        \"ds_outlet_id\",\n", "    ],  # list\n", "    \"partition_key\": [\"trip_creation_date\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"trip_id\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"CMS cost\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(cms_df1, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "2adc2b25-e9ec-450e-b3fc-8981f9e903dd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}