alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: fleet_cms_cost_backfill
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U046NS19TQV
path: povms/fleet/etl/fleet_cms_cost_backfill
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2025-04-18T00:00:00'
  interval: 5 4 1 * *
  start_date: '2024-04-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
