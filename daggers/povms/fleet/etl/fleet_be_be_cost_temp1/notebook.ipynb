{"cells": [{"cell_type": "code", "execution_count": null, "id": "d1423da1-6518-44c6-9960-e8cba459536f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "# from pandasql import sqldf\n", "import datetime\n", "import calendar\n", "from pytz import timezone\n", "from datetime import datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "a601cf52-27e6-4f2b-bdc9-a93fcc577da6", "metadata": {}, "outputs": [], "source": ["## New table\n", "trino_schema_name = \"supply_etls\"  # \"supply_etls\"  # \"povms\"\n", "trino_table_name = \"fleet_be_be_cost_temp1\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "15b07db6-9aa1-4c74-a9e2-fdabcaf7455b", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e266611a-3dce-4c29-8ca6-fd9dd462d701", "metadata": {}, "outputs": [], "source": ["# cms_df = pd.read_csv('cost.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "124d8861-7f6d-47f1-8575-0a96000a9a93", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1quUJbHmT_oAqVZoAGvC8_tLQl2_L4xt_6KFxoUURjpQ\"\n", "sheet_name = \"Final_cost_sheet\""]}, {"cell_type": "code", "execution_count": null, "id": "c2d28a99-8ea2-4abd-8da9-266a475fc581", "metadata": {}, "outputs": [], "source": ["cms_df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "5e8c1f9c-0f89-41a8-a992-e9f25c3cf065", "metadata": {}, "outputs": [], "source": ["cms_df[\"trip_creation_date\"] = pd.to_datetime(cms_df[\"trip_creation_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "97992e09-f597-4ab3-958e-46551468a75a", "metadata": {}, "outputs": [], "source": ["cms_df[\"consignment_id\"] = cms_df[\"consignment_id\"].fillna(value=0)"]}, {"cell_type": "code", "execution_count": null, "id": "01f8bc94-6bee-43ef-b235-e928f2c29353", "metadata": {}, "outputs": [], "source": ["cms_df[\"consignment_id\"] = cms_df[\"consignment_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "35def450-e584-4690-ae5f-edf2612c2112", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_node_id\"] = cms_df[\"wh_node_id\"].replace(\"\", np.nan)  # Replace empty strings with NaN\n", "cms_df[\"wh_node_id\"] = pd.to_numeric(cms_df[\"wh_node_id\"], errors=\"coerce\").fillna(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "024c0ff0-3d87-4117-adad-029083a43f66", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_outlet_id\"] = cms_df[\"wh_outlet_id\"].replace(\n", "    \"\", np.nan\n", ")  # Replace empty strings with NaN\n", "cms_df[\"wh_outlet_id\"] = (\n", "    cms_df[\"wh_outlet_id\"].fillna(0).astype(int)\n", ")  # Fill NaN with 0 and convert to int"]}, {"cell_type": "code", "execution_count": null, "id": "8a92725e-2733-4d0b-ab7a-585e566d1fa9", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_facility_id\"] = cms_df[\"wh_facility_id\"].replace(\n", "    \"\", np.nan\n", ")  # Convert empty strings to NaN\n", "cms_df[\"wh_facility_id\"] = (\n", "    cms_df[\"wh_facility_id\"].fillna(0).astype(int)\n", ")  # Fill NaN with 0 and convert to int"]}, {"cell_type": "code", "execution_count": null, "id": "53de76a5-6da7-4222-8238-8c760f96ddcc", "metadata": {}, "outputs": [], "source": ["cms_df[\"ds_node_id\"] = cms_df[\"ds_node_id\"].replace(\"\", np.nan)  # Convert empty strings to NaN\n", "cms_df[\"ds_node_id\"] = (\n", "    cms_df[\"ds_node_id\"].fillna(0).astype(int)\n", ")  # Fill NaN with 0 and convert to int"]}, {"cell_type": "code", "execution_count": null, "id": "c608f79d-150c-4b8a-bca7-19b664b539f0", "metadata": {}, "outputs": [], "source": ["cms_df[\"ds_outlet_id\"] = cms_df[\"ds_outlet_id\"].replace(\"\", np.nan)  # Convert empty strings to NaN\n", "cms_df[\"ds_outlet_id\"] = (\n", "    cms_df[\"ds_outlet_id\"].fillna(0).astype(int)\n", ")  # Fill NaN with 0 and convert to int"]}, {"cell_type": "code", "execution_count": null, "id": "03a0ad99-9014-497f-bc74-3cbb3a6681cd", "metadata": {}, "outputs": [], "source": ["cms_df[\"vendor_id\"] = cms_df[\"vendor_id\"].replace(\"\", np.nan)\n", "\n", "# Convert to numeric, forcing invalid values to NaN, then fill NaNs with 0 and convert to int\n", "cms_df[\"vendor_id\"] = pd.to_numeric(cms_df[\"vendor_id\"], errors=\"coerce\").fillna(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "a012e5c7-29e6-421d-a653-d9349f630ebb", "metadata": {}, "outputs": [], "source": ["cms_df[\"fixed_cost\"] = cms_df[\"fixed_cost\"].replace(\"\", np.nan)\n", "\n", "# Convert to numeric, forcing invalid values to NaN, then convert to float\n", "cms_df[\"fixed_cost\"] = pd.to_numeric(cms_df[\"fixed_cost\"], errors=\"coerce\").astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "4baae62b-b98e-47ac-8939-242a1e20aad3", "metadata": {}, "outputs": [], "source": ["cms_df[\"tolls_cost\"] = cms_df[\"tolls_cost\"].replace(\"\", np.nan)\n", "cms_df[\"tolls_cost\"] = pd.to_numeric(cms_df[\"tolls_cost\"], errors=\"coerce\").astype(float)\n", "\n", "cms_df[\"misc_charges\"] = cms_df[\"misc_charges\"].replace(\"\", np.nan)\n", "cms_df[\"misc_charges\"] = pd.to_numeric(cms_df[\"misc_charges\"], errors=\"coerce\").astype(float)\n", "\n", "cms_df[\"total_trip_cost\"] = cms_df[\"total_trip_cost\"].replace(\"\", np.nan)\n", "cms_df[\"total_trip_cost\"] = pd.to_numeric(cms_df[\"total_trip_cost\"], errors=\"coerce\").astype(float)\n", "\n", "cms_df[\"total_con_cost\"] = cms_df[\"total_con_cost\"].replace(\"\", np.nan)\n", "cms_df[\"total_con_cost\"] = pd.to_numeric(cms_df[\"total_con_cost\"], errors=\"coerce\").astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "78a0c632-0a3e-471d-810e-7abd63fb847c", "metadata": {}, "outputs": [], "source": ["cms_df[\"total_km_travelled\"] = cms_df[\"total_km_travelled\"].replace(\"\", np.nan)\n", "cms_df[\"total_km_travelled\"] = pd.to_numeric(cms_df[\"total_km_travelled\"], errors=\"coerce\").astype(\n", "    float\n", ")\n", "\n", "cms_df[\"total_hr_travelled\"] = cms_df[\"total_hr_travelled\"].replace(\"\", np.nan)\n", "cms_df[\"total_hr_travelled\"] = pd.to_numeric(cms_df[\"total_hr_travelled\"], errors=\"coerce\").astype(\n", "    float\n", ")\n", "\n", "cms_df[\"fixed_cost\"] = cms_df[\"fixed_cost\"].replace(\"\", np.nan)\n", "cms_df[\"fixed_cost\"] = pd.to_numeric(cms_df[\"fixed_cost\"], errors=\"coerce\").astype(float)\n", "\n", "cms_df[\"misc_charges\"] = cms_df[\"misc_charges\"].replace(\"\", np.nan)\n", "cms_df[\"misc_charges\"] = pd.to_numeric(cms_df[\"misc_charges\"], errors=\"coerce\").astype(float)\n", "\n", "cms_df[\"extra_hr_cost\"] = cms_df[\"extra_hr_cost\"].replace(\"\", np.nan)\n", "cms_df[\"extra_hr_cost\"] = pd.to_numeric(cms_df[\"extra_hr_cost\"], errors=\"coerce\").astype(float)\n", "\n", "cms_df[\"extra_km_cost\"] = cms_df[\"extra_km_cost\"].replace(\"\", np.nan)\n", "cms_df[\"extra_km_cost\"] = pd.to_numeric(cms_df[\"extra_km_cost\"], errors=\"coerce\").astype(float)\n", "\n", "cms_df[\"extra_driver_cost_for_trip\"] = cms_df[\"extra_driver_cost_for_trip\"].replace(\"\", np.nan)\n", "cms_df[\"extra_driver_cost_for_trip\"] = pd.to_numeric(\n", "    cms_df[\"extra_driver_cost_for_trip\"], errors=\"coerce\"\n", ").astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "83fc07f4-5032-49f0-ab6a-6ca7e52ecd75", "metadata": {}, "outputs": [], "source": ["# cms_df[\"total_trip_cost\"] = cms_df[\"total_trip_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "c1831815-fdac-4da0-9706-1227fff7c0c0", "metadata": {}, "outputs": [], "source": ["# cms_df[\"total_con_cost\"] = cms_df[\"total_con_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "f464bb04-b0c1-4d2b-8b40-5603ad355f07", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c54752a-9333-4164-a212-4cb1e3e19fb1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f42c0724-3287-420a-9d4b-bcf8fb23f66a", "metadata": {}, "outputs": [], "source": ["cms_df1 = cms_df[\n", "    [\n", "        \"trip_creation_date\",\n", "        \"consignment_id\",\n", "        \"trip_id\",\n", "        \"wh_node_id\",\n", "        \"wh_outlet_id\",\n", "        \"wh_facility_id\",\n", "        \"wh_outlet_name\",\n", "        \"ds_node_id\",\n", "        \"ds_outlet_id\",\n", "        \"ds_outlet_name\",\n", "        \"destination_city\",\n", "        \"truck_number\",\n", "        \"truck_type\",\n", "        \"billable_truck_type\",\n", "        \"truck_billing_type\",\n", "        \"total_km_travelled\",\n", "        \"total_hr_travelled\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"fixed_cost\",\n", "        \"misc_charges\",\n", "        \"tolls_cost\",\n", "        \"extra_hr_cost\",\n", "        \"extra_km_cost\",\n", "        \"extra_driver_cost_for_trip\",\n", "        \"total_trip_cost\",\n", "        \"status\",\n", "        \"total_con_cost\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e6daa8f5-24ca-4d17-ba28-64360093459b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "83a392ed-c32a-459e-8cbb-b42992932790", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"trip_creation_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Trip creation date\",\n", "        },\n", "        {\"name\": \"consignment_id\", \"type\": \"integer\", \"description\": \"Consignment ID\"},\n", "        {\"name\": \"trip_id\", \"type\": \"varchar\", \"description\": \"Trip Id\"},\n", "        {\"name\": \"wh_node_id\", \"type\": \"integer\", \"description\": \"WH NODE ID\"},\n", "        {\"name\": \"wh_outlet_id\", \"type\": \"integer\", \"description\": \"WH outlet ID\"},\n", "        {\"name\": \"wh_facility_id\", \"type\": \"integer\", \"description\": \"WH facility ID\"},\n", "        {\"name\": \"wh_outlet_name\", \"type\": \"varchar\", \"description\": \"WH Outlet Name\"},\n", "        {\"name\": \"ds_node_id\", \"type\": \"integer\", \"description\": \"DS NODE ID\"},\n", "        {\"name\": \"ds_outlet_id\", \"type\": \"integer\", \"description\": \"DS outlet ID\"},\n", "        {\"name\": \"ds_outlet_name\", \"type\": \"varchar\", \"description\": \"DS outlet name\"},\n", "        {\n", "            \"name\": \"destination_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Destination city\",\n", "        },\n", "        {\"name\": \"truck_number\", \"type\": \"varchar\", \"description\": \"Truck number\"},\n", "        {\"name\": \"truck_type\", \"type\": \"varchar\", \"description\": \"Truck Type\"},\n", "        {\n", "            \"name\": \"billable_truck_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Billable Truck Type\",\n", "        },\n", "        {\n", "            \"name\": \"truck_billing_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Truck Billable Type\",\n", "        },\n", "        {\n", "            \"name\": \"total_km_travelled\",\n", "            \"type\": \"real\",\n", "            \"description\": \"total_km_travelled\",\n", "        },\n", "        {\n", "            \"name\": \"total_hr_travelled\",\n", "            \"type\": \"real\",\n", "            \"description\": \"total_hr_travelled\",\n", "        },\n", "        {\"name\": \"vendor_id\", \"type\": \"integer\", \"description\": \"Vendor ID\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"Vendor name\"},\n", "        {\"name\": \"fixed_cost\", \"type\": \"real\", \"description\": \"Fixed Cost\"},\n", "        {\"name\": \"misc_charges\", \"type\": \"real\", \"description\": \"Fixed Cost\"},\n", "        {\"name\": \"tolls_cost\", \"type\": \"real\", \"description\": \"Tolls Cost\"},\n", "        {\"name\": \"extra_hr_cost\", \"type\": \"real\", \"description\": \"extra_hr_cost\"},\n", "        {\"name\": \"extra_km_cost\", \"type\": \"real\", \"description\": \"extra_km_cost\"},\n", "        {\n", "            \"name\": \"extra_driver_cost_for_trip\",\n", "            \"type\": \"real\",\n", "            \"description\": \"extra_driver_cost_for_trip\",\n", "        },\n", "        {\"name\": \"total_trip_cost\", \"type\": \"real\", \"description\": \"total_trip_cost\"},\n", "        {\"name\": \"status\", \"type\": \"varchar\", \"description\": \"status\"},\n", "        {\"name\": \"total_con_cost\", \"type\": \"real\", \"description\": \"total_con_cost\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"trip_id\",\n", "        \"wh_outlet_id\",\n", "        \"ds_outlet_id\",\n", "    ],  # list\n", "    \"partition_key\": [\"trip_creation_date\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"trip_id\",\n", "    \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"CMS cost\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(cms_df1, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "31c0ff3d-2407-4a46-b081-00e4f9f922d9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}