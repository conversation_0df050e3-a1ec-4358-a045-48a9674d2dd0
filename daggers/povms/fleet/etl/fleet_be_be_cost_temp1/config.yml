alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: fleet_be_be_cost_temp1
dag_type: etl
escalation_priority: low
execution_timeout: 100
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06M9JKD48G
path: povms/fleet/etl/fleet_be_be_cost_temp1
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2025-04-23T00:00:00'
  interval: 0 12 * * *
  start_date: '2025-03-22T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
