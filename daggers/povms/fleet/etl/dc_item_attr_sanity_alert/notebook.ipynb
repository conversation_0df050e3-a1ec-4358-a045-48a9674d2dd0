{"cells": [{"cell_type": "code", "execution_count": null, "id": "f785b609-1e16-42e2-97e3-69cc8278fc27", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "fa39c7fc-3cba-4041-987b-10b7266f4b06", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bb698297-00b4-489d-a925-662bd2962494", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "ee00d18d-9325-48a1-b194-2dc0aed013fc", "metadata": {}, "outputs": [], "source": ["# ## New table\n", "# trino_schema_name = \"supply_etls\"  # \"supply_etls\"  # \"povms\"\n", "# trino_table_name = \"fleet_trips\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "0122ffaf-59ab-44b1-9a7e-c9b8efa54400", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "27af1cd4-cd51-4cf2-a702-0d43160f5ee7", "metadata": {}, "outputs": [], "source": ["q1 = \"\"\"\n", "WITH base_consignments AS (\n", "    select distinct tc.id, tc.type, tc.source_store_id, tc.destination_store_id from transit_server.transit_consignment tc \n", "        join transit_server.transit_consignment_document tcd \n", "        on tcd.consignment_id=tc.id \n", "        where tc.insert_ds_ist = cast(current_date - interval '1' day as varchar) \n", "        and tcd.insert_ds_ist = cast(current_date - interval '1' day as varchar) \n", "        and tcd.document_type='INVOICE' and tcd.state is not NULL and tc.state='COMPLETED'\n", "        AND tc.type = 'FORWARD_CONSIGNMENT'\n", "        and tc.lake_active_record\n", "        and tcd.lake_active_record\n", "), container_scanned_qty AS (SELECT tcc.consignment_id AS consignment_id,\n", "          cast(json_extract(tcc.metadata, '$.billing_entity.id') AS varchar) AS billing_entity_id,\n", "          tci.variant_id,\n", "          sum(COALESCE(tci.added_quantity,0)) AS total_scanned_quantity\n", "   FROM transit_server.transit_consignment_container tcc\n", "   JOIN transit_server.transit_consignment_item tci ON tci.consignment_id = tcc.consignment_id AND tci.container_id = tcc.external_id\n", "   JOIN base_consignments ON tcc.consignment_id = base_consignments.id\n", "   AND tci.container_id = tcc.external_id\n", "   WHERE tcc.state IN ('LOADED_AT_SOURCE',\n", "                       'IN_TRANSIT',\n", "                       'MISSING_IN_TRANSIT',\n", "                       'UNLOADED_AT_DESTINATION')\n", "    AND NOT coalesce(cast(json_extract(tcc.metadata, '$.missed_source_scan') AS BOOLEAN), false)\n", "    AND tcc.insert_ds_ist >= cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "    AND tci.insert_ds_ist >= cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "    and tcc.lake_active_record\n", "    and tci.lake_active_record\n", "   GROUP BY 1,\n", "            2,\n", "            3\n", "), consignment_document AS (\n", "    SELECT tcd.consignment_id AS consignment_id,\n", "          cast(json_extract(tcd.metadata, '$.billing_entity.id') AS varchar) AS billing_entity_id,\n", "          pipd.variant_id,\n", "          sum(COALESCE(pipd.quantity,0)) pos_invoice_quantity,\n", "          sum(CASE\n", "                  WHEN tcd.state='FULLY_DISPATCHED' AND (tcd.billed_source IS NULL OR tcd.billed_source = base_consignments.source_store_id) THEN pipd.quantity\n", "                  ELSE 0\n", "              END) AS invoice_attr_qty\n", "   FROM transit_server.transit_consignment_document tcd\n", "   JOIN pos.pos_invoice pi ON tcd.external_id = pi.invoice_id AND tcd.document_type='INVOICE'\n", "   JOIN pos.pos_invoice_product_details pipd ON pipd.invoice_id=pi.id\n", "   JOIN base_consignments ON tcd.consignment_id = base_consignments.id\n", "     AND tcd.insert_ds_ist >= cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "     AND pi.insert_ds_ist >= cast(CURRENT_DATE - interval '30' DAY AS varchar)\n", "     AND pipd.insert_ds_ist >= cast(CURRENT_DATE - interval '30' DAY AS varchar)\n", "    and tcd.lake_active_record\n", "    and pi.lake_active_record\n", "    and pipd.lake_active_record\n", "    GROUP BY 1,\n", "    2,\n", "    3\n", "), delivery_challan AS(\n", "    SELECT tcd.consignment_id AS consignment_id,\n", "          cast(json_extract(tcd2.metadata, '$.billing_entity.id') AS varchar) AS billing_entity_id,\n", "          tdci.variant_id,\n", "          sum(COALESCE(tdci.quantity, 0)) dc_qty\n", "   FROM transit_server.transit_consignment_document tcd\n", "   JOIN transit_server.transit_delivery_challan tdc ON tdc.id = tcd.external_id\n", "   JOIN transit_server.transit_consignment_document tcd2 ON tcd2.consignment_id = tcd.consignment_id\n", "   AND tdc.reference_invoice_id = tcd2.external_id\n", "   JOIN transit_server.transit_delivery_challan_item tdci ON tdci.delivery_challan_id = tdc.id\n", "     AND tcd.document_type='DELIVERY_CHALLAN'\n", "     AND tdc.state ='CREATED'\n", "     AND tcd.insert_ds_ist >= cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "     AND tdc.insert_ds_ist >= cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "     AND tdci.insert_ds_ist >= cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "     AND tcd2.insert_ds_ist >= cast(CURRENT_DATE - interval '1' DAY AS varchar)\n", "   JOIN base_consignments ON base_consignments.id = tcd.consignment_id\n", "    and tcd.lake_active_record\n", "    and tdc.lake_active_record\n", "    and tcd2.lake_active_record\n", "    and tdci.lake_active_record\n", "    GROUP BY 1,\n", "            2,\n", "            3\n", ") SELECT cd.consignment_id,\n", "       cd.billing_entity_id,\n", "       cd.variant_id,\n", "       cd.pos_invoice_quantity,\n", "       COALESCE(ci.total_scanned_quantity,0) as container_scanned_quantity,\n", "       COALESCE(cd.invoice_attr_qty,0) + COALESCE(dc_qty,0) AS total_attr_qty,\n", "       COALESCE(cd.invoice_attr_qty,0) as invoice_attr_qty,\n", "       COALESCE(dc_qty,0) as dc_qty,\n", "       COALESCE(total_scanned_quantity,0) - (COALESCE(cd.invoice_attr_qty,0) + COALESCE(dc_qty,0)) AS diff\n", "FROM consignment_document cd\n", "LEFT JOIN container_scanned_qty ci ON ci.consignment_id = cd.consignment_id\n", "AND ci.billing_entity_id = cd.billing_entity_id\n", "AND ci.variant_id = cd.variant_id\n", "LEFT JOIN delivery_challan dc ON ci.consignment_id = dc.consignment_id\n", "AND ci.billing_entity_id = dc.billing_entity_id\n", "AND ci.variant_id = dc.variant_id\n", "\"\"\"\n", "\n", "df1 = read_sql_query(q1, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "cb5d39ed-e9a9-41cc-b5b4-04a4d335d2d3", "metadata": {}, "outputs": [], "source": ["df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ea2ecf39-1953-4c3f-a0b9-08f629acb23d", "metadata": {}, "outputs": [], "source": ["# . total_invoice_qty<=  total_scanned_qty or total_scanned_qty!=total_attr_qty"]}, {"cell_type": "code", "execution_count": null, "id": "d41072f3-864f-4b0e-9f7a-9e513ebde4f9", "metadata": {}, "outputs": [], "source": ["df2 = df1[\n", "    (df1[\"container_scanned_quantity\"] != df1[\"total_attr_qty\"])\n", "    | (df1[\"pos_invoice_quantity\"] < df1[\"container_scanned_quantity\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "8d3a55b6-2f00-4050-a697-5a0a331d0595", "metadata": {}, "outputs": [], "source": ["df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2f5975f4-2b39-4424-9325-d41cb3f875cd", "metadata": {}, "outputs": [], "source": ["df2.consignment_id.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "d6b62a60-bf98-4064-9bc1-a93761af2bc2", "metadata": {}, "outputs": [], "source": ["n = df2.consignment_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "d0decbdd-cd30-4620-809b-6a56620ecfd4", "metadata": {}, "outputs": [], "source": ["n"]}, {"cell_type": "code", "execution_count": null, "id": "8e63d5db-be97-461a-9aab-134a84ad37ec", "metadata": {}, "outputs": [], "source": ["df2[df2[\"diff\"] > 0].shape"]}, {"cell_type": "code", "execution_count": null, "id": "1e05d888-cde3-4ffe-bf01-3b00658ad5ed", "metadata": {}, "outputs": [], "source": ["# df2[df2['consignment_id']==2007792]"]}, {"cell_type": "code", "execution_count": null, "id": "5caf853b-6724-4ea8-b739-59f156777d87", "metadata": {}, "outputs": [], "source": ["# df2[df2['pos_invoice_quantity'] < df2['container_scanned_quantity']]"]}, {"cell_type": "code", "execution_count": null, "id": "f9cc6159-9ec6-49d9-a328-f2227028dfef", "metadata": {}, "outputs": [], "source": ["# sheet_id='1c-U6tzSFjuf_a_nJmhgfw0E6BrEyrd4M96FFgCPjkqo'"]}, {"cell_type": "code", "execution_count": null, "id": "880d2d07-c851-44e3-a476-0eef46f13fa5", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(df2,sheet_id,\"raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "aa7b0e2d-c0bf-4e16-b8f2-99d2a02e9ebd", "metadata": {}, "outputs": [], "source": ["df2.to_csv(\"df2.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c3797030-d480-4412-965c-657e7881c744", "metadata": {}, "outputs": [], "source": ["slack_channel = [\"bl-middle-mile-alerts\"]\n", "slack_channel = list(slack_channel)\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "eff2a016-4865-4a8a-a9f9-93721e5b16ef", "metadata": {}, "outputs": [], "source": ["for i in range(len(slack_channel)):\n", "    channel = slack_channel[i]\n", "\n", "    text_req = (\n", "        f\"Delivery Challan Product Quantity Sanity\"\n", "        + \"\\n\"\n", "        + f\"No of Consignments: {n}\"\n", "        + \"\\n\"\n", "        + datetime.now().strftime(\"%Y-%m-%d\")\n", "        + \"\\n\"\n", "        # + \"\\n\"\n", "    )\n", "    pb.send_slack_message(\n", "        channel=channel,\n", "        text=text_req,\n", "        files=[\"df2.csv\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "c45ce17e-0d94-4f35-8ccf-f9ff5d0434d4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "11b9e7c5-949e-455a-9203-9395cae61a26", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}