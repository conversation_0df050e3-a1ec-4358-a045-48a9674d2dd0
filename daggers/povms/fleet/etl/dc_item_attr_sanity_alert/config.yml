alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: dc_item_attr_sanity_alert
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06M9JKD48G
path: povms/fleet/etl/dc_item_attr_sanity_alert
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2025-07-25T00:00:00'
  interval: 30 4 * * *
  start_date: '2025-05-24T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
