{"cells": [{"cell_type": "code", "execution_count": null, "id": "2c1f0e3f-25f7-4528-9959-43e027d49813", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "from pytz import timezone\n", "from datetime import timedelta\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "83b19126-50be-46d8-a289-ea6be43537d5", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "55cd7f64-63dd-46af-80bd-b9edad9a8d58", "metadata": {"tags": []}, "outputs": [], "source": ["old_query = \"\"\" \n", "\n", "select *\n", "from supply_etls.blinkit_middle_mile_fleet_cost\n", "where date_ between cast('2024-01-01' as date) and current_date\n", "\n", "\"\"\"\n", "\n", "old_df = pd.read_sql_query(sql=old_query, con=trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "6b676327-5819-4d0d-bff4-59f024340c7f", "metadata": {"tags": []}, "outputs": [], "source": ["new_query = \"\"\" \n", " with trip_base as (\n", "\n", "SELECT DISTINCT \n", "           pt.trip_id,\n", "           coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as trip_creation_time,\n", "           cast( coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as date) as trip_creation_date,\n", "           pt.source_node_id,\n", "           pc.consignment_id,\n", "           pc.destination_id,\n", "           co1.id as wh_outlet_id,\n", "           case \n", "            when coalesce(co1.facility_id,cast(t.source_store_id as int)) = 1743 then 264\n", "            else coalesce(co1.facility_id,cast(t.source_store_id as int))\n", "           end as facility_id,\n", "           n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "        --   UPPER(split_part(cast(json_extract(t.metadata,'$.truck_number') AS varchar),'/',1)) AS truck_number,\n", "           pt.truck_number,\n", "           case \n", "                when (cast(json_extract(t.metadata,'$.vehicle_type') as varchar)) = '' or (cast(json_extract(t.metadata,'$.vehicle_type') as varchar) is null)\n", "                then cast(json_extract(pt.truck_meta,'$.vehicle_type') as varchar)\n", "                else cast(json_extract(t.metadata,'$.vehicle_type') as varchar)\n", "           end AS truck_type\n", "                       \n", "                   \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    \n", "    LEFT JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "   JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "   LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "                      facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12,19,20) \n", "    ) AS co1 \n", "    \n", "    ON cast(co1.id as varchar) = t.source_store_id\n", "    \n", "    left join \n", "        (\n", "            select distinct trip_id, (actual_ts + interval '330' MINUTE) as trip_creation_time_1\n", "            from transit_server.transit_projection_trip_event_timeline\n", "            where insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "            and event_type = 'WH_DISPATCH'\n", "            and actual_ts is not null\n", "        ) as pte\n", "    on pt.trip_id = pte.trip_id\n", "    \n", "    WHERE pt.insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "    and t.insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "    and pc.insert_ds_ist between cast('2024-02-01' as varchar) and cast(current_date as varchar)\n", "    -- cast((CURRENT_DATE - interval '14' DAY) as timestamp)\n", "    and pt.state in ('COMPLETED','CANCELLED','EXPIRED', 'WH_DISPATCH', 'DS_ARRIVAL', 'UNLOADING_START', 'UNLOADING_END', 'DS_DISPATCH')\n", "    and cast(json_extract(pt.source_node_meta,'$.store_type') AS varchar)!= 'HYPERPURE'\n", "    and (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "\n", ")\n", "\n", ", route_base as (\n", "\n", "    select distinct \n", "        truck_number,\n", "        cast(source_node_id as varchar) as source_node_id,\n", "        destination_node_id,\n", "        billable_destination_city,\n", "        truck_billing_type,\n", "        truck_type,\n", "        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "        vendor_id,\n", "        name as vendor_name,\n", "        cast(json_extract(tvm.metadata,'$.km_slab') AS int) as km_slab,\n", "        cast(json_extract(tvm.metadata,'$.hr_slab') AS int) as hr_slab,\n", "        expected_trips_per_day\n", "        \n", "        \n", "    from fleet_management.fleet_management_truck_vendor_mapping as tvm\n", "    join fleet_management.fleet_management_node_billable_city_mapping as bcm\n", "    on tvm.id = bcm.truck_vendor_mapping_id\n", "    left join \n", "        fleet_management.fleet_management_hiring_source as hs\n", "        on hs.id = tvm.vendor_id and active = True\n", "    where tvm.is_active= True\n", "    and bcm.is_active= True\n", "    and source_node_id is not null\n", ")\n", "\n", "\n", ", trip_vendor_base as (\n", "\n", "    select distinct\n", "        tb.trip_id,\n", "        cast(tb.trip_creation_time as date) as trip_creation_date,\n", "        tb.trip_creation_time,\n", "        rb.source_node_id,\n", "        tb.consignment_id,\n", "        rb.billable_destination_city,\n", "        tb.destination_id,\n", "        tb.truck_number,\n", "        rb.truck_type,\n", "        rb.vendor_id,\n", "        rb.vendor_name,\n", "        rb.truck_billing_type,\n", "        rb.km_slab,\n", "        rb.hr_slab,\n", "        rb.expected_trips_per_day,\n", "        tb.wh_outlet_id,\n", "        tb.facility_id,\n", "        tb.facility_name,\n", "        tb.ds_outlet_id,\n", "        tb.ds_outlet_name\n", "                       \n", "    from trip_base as tb\n", "    left join route_base as rb\n", "    \n", "    on tb.truck_number = rb.truck_number \n", "    -- and tb.source_node_id = rb.source_node_id\n", "    and tb.destination_id = rb.destination_node_id\n", "    and (tb.trip_creation_time >= rb.tenure_start and tb.trip_creation_time< rb.tenure_end)\n", "    \n", "    -- where rb.truck_number is not null\n", ")\n", "\n", ",vendor_rate_card as (\n", "\n", "    with base_charge as ( \n", "    \n", "        with km_base_charge as (\n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                p.value as km_slab,\n", "                p.rule_id,\n", "                output as base_cost\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and attribute_name = 'km'\n", "            and rule.rule_name = 'BASE_PAY'\n", "            \n", "        )\n", "        \n", "        , hr_base_charge as (\n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                p.value as hr_slab,\n", "                p.rule_id,\n", "                output as base_cost\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and attribute_name = 'hour'\n", "            and rule.rule_name = 'BASE_PAY'\n", "            \n", "        )\n", "        \n", "        select distinct kb.*, hb.hr_slab\n", "        from km_base_charge as kb\n", "        join hr_base_charge as hb\n", "        on kb.vendor_rate_card_config_id = hb.vendor_rate_card_config_id and kb.base_cost = hb.base_cost and kb.rule_id = hb.rule_id\n", "    )\n", "\n", "\n", "    , extra_charge as (\n", "\n", "        with km_extra_charge as (\n", "        \n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                output as extra_cost_per_km\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and rule.rule_name = 'ADDITIONAL_COST_PER_KM'\n", "            \n", "        )\n", "        \n", "        , hr_extra_charge as (\n", "            select distinct \n", "                vendor_rate_card_config_id,\n", "                vendor_id,\n", "                source_node_id,\n", "                external_name as source_name,\n", "                destination_city,\n", "                hs.name as vendor_name,\n", "                truck_type,\n", "                truck_billing_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                output as extra_cost_per_hr\n", "                \n", "            from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "            join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "            join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "            left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "            left join fleet_management.fleet_management_hiring_source as hs on hs.id = vrc.vendor_id\n", "            \n", "            where vrc.is_active and vrcm.is_active\n", "            and rule.rule_name = 'ADDITIONAL_COST_PER_HR'\n", "            \n", "        )\n", "        \n", "        select distinct ke.*, he.extra_cost_per_hr\n", "        from km_extra_charge as ke\n", "        join hr_extra_charge as he\n", "        on ke.vendor_rate_card_config_id = he.vendor_rate_card_config_id \n", "    )\n", "\n", "    select distinct \n", "        bc.source_node_id,\n", "        bc.source_name,\n", "        bc.destination_city,\n", "        bc.vendor_id,\n", "        bc.vendor_name,\n", "        bc.truck_type,\n", "        bc.truck_billing_type,\n", "        bc.base_cost,\n", "        bc.tenure_start,\n", "        bc.tenure_end,\n", "        bc.km_slab,\n", "        bc.hr_slab,\n", "        ec.extra_cost_per_hr,\n", "        ec.extra_cost_per_km\n", "        \n", "    from base_charge as bc\n", "    left join extra_charge as ec\n", "    on bc.vendor_rate_card_config_id = ec.vendor_rate_card_config_id\n", "\n", "\n", ")\n", "\n", ", extra_charges_base as (\n", "    select \n", "        trip_id,\n", "        sum(case when attribute_type = 'EXTRA_HOURS' then cast(value as decimal) end) as extra_hours,\n", "        sum(case when attribute_type = 'MISCELLANEOUS_CHARGES' then cast(value as decimal) end) as misc_charge\n", "       \n", "    from fleet_management.fleet_management_trip_adhoc_costs_attributes\n", "    where value <> 'None'\n", "    group by 1\n", ")\n", "\n", ", tolls_base as (\n", "\n", "    select distinct\n", "        source_node_id, \n", "        destination_node_id,\n", "        truck_type,\n", "        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "        tolls_cost\n", "    \n", "    from fleet_management.fleet_management_tolls_cost\n", "    where is_active=true\n", ")\n", "\n", ", cancelled_enroute as (\n", "    \n", "    select distinct trip_id \n", "    from transit_server.transit_projection_trip_event_timeline \n", "    where actual_ts IS NULL \n", "    and event_type = 'WH_DISPATCH'\n", "    and insert_ds_ist between cast('2024-02-01' as varchar) and  cast(CURRENT_DATE as varchar)\n", ")\n", "\n", ", same_day_trip_count_base as (\n", "\n", "    SELECT  \n", "        cast(trip_creation_time as date) as trip_creation_date,\n", "        truck_number,\n", "        count(distinct trip_id) as trips\n", "    from trip_base\n", "    group by 1,2   \n", ")\n", "\n", ", discarded_trips as (\n", "\n", "    select distinct trip_id\n", "    from fleet_management.fleet_management_trips_cost\n", "    where status = 'DISCARDED'\n", "    and insert_ds_ist >= cast('2024-02-01' as varchar)\n", ")\n", "\n", ", pre_final as (\n", "\n", "select distinct\n", "    vb.trip_id,\n", "    vb.trip_creation_date,\n", "    vb.trip_creation_time,\n", "    \n", "    vb.facility_id,\n", "    -- vb.source_node_id,\n", "    vb.facility_name as wh_name,\n", "    \n", "    vb.consignment_id,\n", "    vb.billable_destination_city as destination_city,\n", "    \n", "    vb.ds_outlet_id as outlet_id,\n", "    vb.ds_outlet_name as destination_name,\n", "    \n", "    vb.truck_number,\n", "    vb.truck_type,\n", "    -- vb.vendor_id,\n", "    vb.vendor_name,\n", "    vb.truck_billing_type,\n", "    vb.km_slab,\n", "    vb.hr_slab,\n", "    vb.expected_trips_per_day,\n", "    \n", "    vr.base_cost,\n", "    vr.extra_cost_per_hr,\n", "    vr.extra_cost_per_km,\n", "    tb.tolls_cost/count(consignment_id) over(partition by vb.trip_id) as tolls_cost,\n", "    case when ce.trip_id is not null then 1 else 0 end as cancelled_before_enroute,\n", "    sd.trips as same_day_trips,\n", "    case \n", "        when vb.truck_billing_type = 'FIXED' then cast(base_cost as decimal)/day(last_day_of_month(vb.trip_creation_date))\n", "        else cast(base_cost as decimal)\n", "    end as per_day_cost,\n", "    \n", "    cast(extra_cost_per_hr as decimal)*ec.extra_hours as extra_hr_cost,\n", "    count(consignment_id) over(partition by vb.trip_id) as consign_num\n", "    \n", "from \n", "    trip_vendor_base as vb\n", "left join\n", "    vendor_rate_card as vr\n", "    on vb.vendor_id = vr.vendor_id and vb.source_node_id = cast(vr.source_node_id as varchar)\n", "    and vb.billable_destination_city = vr.destination_city and vb.truck_type = vr.truck_type \n", "    and (vb.trip_creation_time >= vr.tenure_start and vb.trip_creation_time< vr.tenure_end)\n", "    and cast(vb.km_slab as varchar) = vr.km_slab and cast(vb.hr_slab as varchar) = vr.hr_slab \n", "    and vb.truck_billing_type = vr.truck_billing_type\n", "    \n", "left join \n", "    tolls_base as tb\n", "    on vb.source_node_id = tb.source_node_id and vb.destination_id = tb.destination_node_id\n", "    and vb.truck_type = tb.truck_type \n", "    and (vb.trip_creation_time >= tb.tenure_start and vb.trip_creation_time< tb.tenure_end)\n", "    \n", "left join\n", "    cancelled_enroute as ce\n", "    on vb.trip_id = ce.trip_id\n", "\n", "left join \n", "    same_day_trip_count_base as sd\n", "    on vb.trip_creation_date = sd.trip_creation_date and vb.truck_number = sd.truck_number\n", "    \n", "left join \n", "    extra_charges_base as ec\n", "    on vb.trip_id = ec.trip_id\n", "\n", "left join discarded_trips as dt\n", "    on vb.trip_id = ec.trip_id\n", "\n", "where dt.trip_id is null\n", "    \n", ")\n", "\n", "\n", ", final as (\n", "\n", "select *,\n", "\n", "    case\n", "        when (expected_trips_per_day < 1 and expected_trips_per_day > 0) then per_day_cost/(expected_trips_per_day *consign_num)\n", "        when same_day_trips <= expected_trips_per_day then per_day_cost/(expected_trips_per_day *consign_num)\n", "        else per_day_cost/(same_day_trips*consign_num)\n", "    end as fixed_cost,\n", "    \n", "    case \n", "        when cancelled_before_enroute = 1 then extra_hr_cost\n", "        else extra_hr_cost + tolls_cost\n", "    end as misc_cost\n", "\n", "from pre_final \n", ")\n", "\n", "\n", "\n", "select distinct  *, \n", "    (fixed_cost + coalesce(tolls_cost,0)) as total_cost_tolls,\n", "    (fixed_cost + coalesce(tolls_cost,0) + coalesce(misc_cost,0)) as total_cost\n", "from final\n", "\n", "\"\"\"\n", "\n", "new_df = pd.read_sql_query(sql=new_query, con=trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "f24ec241-bde2-4911-a04a-a7c6587105c2", "metadata": {}, "outputs": [], "source": ["old_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4e3a78fb-99a1-49f5-8907-dda381ee42af", "metadata": {}, "outputs": [], "source": ["old_df[\"kms_slab_as_per_loi\"] = old_df[\"kms_slab_as_per_loi\"].fillna(0).astype(int)\n", "new_df[\"km_slab\"] = new_df[\"km_slab\"].fillna(0).astype(\"int\")\n", "# old_df[\"fixed_working_hours\"] = old_df[\"fixed_working_hours\"].str.lower()\n", "\n", "old_df[\"fixed_working_hours\"] = pd.to_numeric(\n", "    old_df[\"fixed_working_hours\"], errors=\"coerce\"\n", ")\n", "#\n", "# old_df[\"fixed_working_hours\"] = (\n", "#     old_df[\"fixed_working_hours\"]\n", "#     .str.replace(\"hours\", \"\")\n", "#     .str.replace(\"hour\", \"\")\n", "#     .str.replace(\"nan\", \"0\")\n", "#     .astype(int)\n", "# )\n", "\n", "old_df[\"vehicle_size\"] = old_df[\"vehicle_size\"].str.lower()\n", "new_df[\"truck_type\"] = new_df[\"truck_type\"].str.lower()\n", "\n", "old_df[\"vehicle_number\"] = old_df[\"vehicle_number\"].str.lower()\n", "new_df[\"truck_number\"] = new_df[\"truck_number\"].str.lower()"]}, {"cell_type": "code", "execution_count": null, "id": "bd5ecce3-44e2-415f-8304-0b8fd12ee9a9", "metadata": {}, "outputs": [], "source": ["def assign_week(day):\n", "    if 1 <= day <= 7:\n", "        return \"week 1\"\n", "    elif 8 <= day <= 14:\n", "        return \"week 2\"\n", "    elif 15 <= day <= 21:\n", "        return \"week 3\"\n", "    elif 22 <= day <= 28:\n", "        return \"week 4\"\n", "    else:  # 29-31\n", "        return \"week 5\""]}, {"cell_type": "code", "execution_count": null, "id": "da667e05-e0fd-4d86-8756-5ef70caf4629", "metadata": {}, "outputs": [], "source": ["new_df[\"trip_creation_date\"] = pd.to_datetime(new_df[\"trip_creation_date\"])\n", "new_df[\"week\"] = new_df[\"trip_creation_date\"].dt.day.apply(assign_week)\n", "\n", "old_df[\"date_\"] = pd.to_datetime(old_df[\"date_\"])\n", "old_df[\"week\"] = old_df[\"date_\"].dt.day.apply(assign_week)\n", "\n", "new_df[\"year_month\"] = new_df[\"trip_creation_date\"].dt.strftime(\"%b %Y\")\n", "old_df[\"year_month\"] = old_df[\"date_\"].dt.strftime(\"%b %Y\")"]}, {"cell_type": "code", "execution_count": null, "id": "6b075801-8b4a-44bc-985a-0532854d6e67", "metadata": {}, "outputs": [], "source": ["old_df[\"truck_store_pair\"] = (\n", "    old_df[\"vehicle_number\"] + \"_\" + old_df[\"destination_outlet_id\"].astype(str)\n", ")\n", "new_df[\"truck_store_pair\"] = (\n", "    new_df[\"truck_number\"] + \"_\" + new_df[\"outlet_id\"].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0d28c36e-5944-428c-8757-db5ef2d4aacc", "metadata": {}, "outputs": [], "source": ["old_df[\"date_cost\"] = old_df[\"date_cost\"].astype(float)\n", "new_df[\"total_cost_tolls\"] = new_df[\"total_cost_tolls\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "6c8d3917-a757-461b-865c-0657fb036e06", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a2b4a74-099b-40c7-8bbf-744e53e7f69f", "metadata": {}, "outputs": [], "source": ["old_metrics_df = (\n", "    old_df.groupby([\"year_month\", \"week\", \"source_facility_id\"])\n", "    .agg(\n", "        {\n", "            \"date_cost\": \"sum\",\n", "            \"vehicle_number\": \"nunique\",\n", "            \"kms_slab_as_per_loi\": \"mean\",\n", "            \"fixed_working_hours\": \"mean\",\n", "            \"truck_store_pair\": \"nunique\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "old_metrics_df.columns = [\n", "    \"year_month\",\n", "    \"week\",\n", "    \"facility_id\",\n", "    \"date_cost\",\n", "    \"vehicle_number\",\n", "    \"kms_slab_as_per_loi\",\n", "    \"fixed_working_hours\",\n", "    \"truck_store_pair\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0986956c-07ef-4555-81c1-9e6693b281f7", "metadata": {}, "outputs": [], "source": ["old_metrics_df_ = (\n", "    old_df.groupby([\"year_month\", \"source_facility_id\"])\n", "    .agg(\n", "        {\n", "            \"date_cost\": \"sum\",\n", "            \"vehicle_number\": \"nunique\",\n", "            \"kms_slab_as_per_loi\": \"mean\",\n", "            \"fixed_working_hours\": \"mean\",\n", "            \"truck_store_pair\": \"nunique\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "old_metrics_df_[\"week\"] = \"all weeks\"\n", "old_metrics_df_.columns = [\n", "    \"year_month\",\n", "    \"facility_id\",\n", "    \"date_cost\",\n", "    \"vehicle_number\",\n", "    \"kms_slab_as_per_loi\",\n", "    \"fixed_working_hours\",\n", "    \"truck_store_pair\",\n", "    \"week\",\n", "]\n", "old_metrics_df_ = old_metrics_df_[\n", "    [\n", "        \"year_month\",\n", "        \"week\",\n", "        \"facility_id\",\n", "        \"date_cost\",\n", "        \"vehicle_number\",\n", "        \"kms_slab_as_per_loi\",\n", "        \"fixed_working_hours\",\n", "        \"truck_store_pair\",\n", "    ]\n", "]\n", "\n", "old_metrics_df = pd.concat([old_metrics_df, old_metrics_df_], ignore_index=True)\n", "old_metrics_df.sort_values(by=[\"year_month\", \"facility_id\", \"week\"], inplace=True)\n", "old_metrics_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "98ead34a-78e8-4c14-90c3-44d6b4a152ef", "metadata": {}, "outputs": [], "source": ["new_metrics_df = (\n", "    new_df.groupby([\"year_month\", \"week\", \"facility_id\"])\n", "    .agg(\n", "        {\n", "            \"total_cost_tolls\": \"sum\",\n", "            \"truck_number\": \"nunique\",\n", "            \"km_slab\": \"mean\",\n", "            \"hr_slab\": \"mean\",\n", "            \"truck_store_pair\": \"nunique\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7daf75f3-4099-40dd-8771-5c25310d54d7", "metadata": {}, "outputs": [], "source": ["new_metrics_df_ = (\n", "    new_df.groupby([\"year_month\", \"facility_id\"])\n", "    .agg(\n", "        {\n", "            \"total_cost_tolls\": \"sum\",\n", "            \"truck_number\": \"nunique\",\n", "            \"km_slab\": \"mean\",\n", "            \"hr_slab\": \"mean\",\n", "            \"truck_store_pair\": \"nunique\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "new_metrics_df_[\"week\"] = \"all weeks\"\n", "new_metrics_df_.columns = [\n", "    \"year_month\",\n", "    \"facility_id\",\n", "    \"total_cost_tolls\",\n", "    \"truck_number\",\n", "    \"km_slab\",\n", "    \"hr_slab\",\n", "    \"truck_store_pair\",\n", "    \"week\",\n", "]\n", "new_metrics_df_ = new_metrics_df_[\n", "    [\n", "        \"year_month\",\n", "        \"week\",\n", "        \"facility_id\",\n", "        \"total_cost_tolls\",\n", "        \"truck_number\",\n", "        \"km_slab\",\n", "        \"hr_slab\",\n", "        \"truck_store_pair\",\n", "    ]\n", "]\n", "\n", "new_metrics_df = pd.concat([new_metrics_df, new_metrics_df_], ignore_index=True)\n", "new_metrics_df.sort_values(by=[\"year_month\", \"facility_id\", \"week\"], inplace=True)\n", "new_metrics_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3c96d558-1f03-4a04-8994-81bf5f1e1046", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "badac0fb-b0e2-45bb-a9c8-dae013c2f3d8", "metadata": {}, "source": ["### truck numbers present in one but not in the other"]}, {"cell_type": "code", "execution_count": null, "id": "5ed88372-0265-4e91-81de-ac9de106efbc", "metadata": {"tags": []}, "outputs": [], "source": ["grouped_old_df = old_df.groupby([\"year_month\", \"week\", \"source_facility_id\"])[\n", "    \"vehicle_number\"\n", "]\n", "grouped_new_df = new_df.groupby([\"year_month\", \"week\", \"facility_id\"])[\"truck_number\"]\n", "\n", "# Initialize a dictionary to store the counts for each 'wh_id'\n", "old_unique_counts = {}\n", "\n", "# Iterate over each group in old_df\n", "for group_keys, truck_numbers_old_df in grouped_old_df:\n", "    # Get corresponding truck numbers from new_df for the same 'wh_id', if exists\n", "    truck_numbers_new_df = (\n", "        grouped_new_df.get_group(group_keys)\n", "        if group_keys in grouped_new_df.groups\n", "        else pd.Series([])\n", "    )\n", "\n", "    # Count unique truck numbers in old_df that are not in new_df for this 'wh_id'\n", "    unique_trucks = truck_numbers_old_df[\n", "        ~truck_numbers_old_df.isin(truck_numbers_new_df)\n", "    ].nunique()\n", "    old_unique_counts[group_keys] = unique_trucks\n", "\n", "\n", "old_unique_counts_df = pd.DataFrame(\n", "    list(old_unique_counts.items()), columns=[\"week_facility\", \"truck_num_not_in_new\"]\n", ")\n", "# Split the 'week_facility' column into 'week' and 'facility_id'\n", "old_unique_counts_df[[\"year_month\", \"week\", \"facility_id\"]] = pd.DataFrame(\n", "    old_unique_counts_df[\"week_facility\"].tolist(), index=old_unique_counts_df.index\n", ")\n", "# Drop the 'week_facility' column\n", "old_unique_counts_df.drop(\"week_facility\", axis=1, inplace=True)\n", "# Reorder the columns\n", "old_unique_counts_df = old_unique_counts_df[\n", "    [\"year_month\", \"week\", \"facility_id\", \"truck_num_not_in_new\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "257fa093-9359-4e02-8c0d-6c0e1dd153c8", "metadata": {}, "outputs": [], "source": ["grouped_old_df_ = old_df.groupby([\"year_month\", \"source_facility_id\"])[\"vehicle_number\"]\n", "grouped_new_df_ = new_df.groupby([\"year_month\", \"facility_id\"])[\"truck_number\"]\n", "\n", "# Initialize a dictionary to store the counts for each 'wh_id'\n", "old_unique_counts_ = {}\n", "\n", "# Iterate over each group in old_df\n", "for group_keys, truck_numbers_old_df in grouped_old_df_:\n", "    # Get corresponding truck numbers from new_df for the same 'wh_id', if exists\n", "    truck_numbers_new_df = (\n", "        grouped_new_df_.get_group(group_keys)\n", "        if group_keys in grouped_new_df_.groups\n", "        else pd.Series([])\n", "    )\n", "\n", "    # Count unique truck numbers in old_df that are not in new_df for this 'wh_id'\n", "    unique_trucks = truck_numbers_old_df[\n", "        ~truck_numbers_old_df.isin(truck_numbers_new_df)\n", "    ].nunique()\n", "    old_unique_counts_[group_keys] = unique_trucks\n", "\n", "\n", "old_unique_counts_df_ = pd.DataFrame(\n", "    list(old_unique_counts_.items()), columns=[\"week_facility\", \"truck_num_not_in_new\"]\n", ")\n", "# # Split the 'week_facility' column into 'week' and 'facility_id'\n", "old_unique_counts_df_[[\"year_month\", \"facility_id\"]] = pd.DataFrame(\n", "    old_unique_counts_df_[\"week_facility\"].tolist(), index=old_unique_counts_df_.index\n", ")\n", "# Drop the 'week_facility' column\n", "old_unique_counts_df_.drop(\"week_facility\", axis=1, inplace=True)\n", "# Reorder the columns\n", "old_unique_counts_df_ = old_unique_counts_df_[\n", "    [\"year_month\", \"facility_id\", \"truck_num_not_in_new\"]\n", "]\n", "\n", "old_unique_counts_df_.insert(1, \"week\", \"all weeks\")\n", "\n", "\n", "old_unique_counts_df = pd.concat(\n", "    [old_unique_counts_df, old_unique_counts_df_], ignore_index=True\n", ")\n", "old_unique_counts_df.sort_values(by=[\"year_month\", \"facility_id\", \"week\"], inplace=True)\n", "old_unique_counts_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a6096131-d0f5-42be-ab14-86fbdb6adcdf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ef5f771-2ba4-4503-9013-d7577dc542ce", "metadata": {}, "outputs": [], "source": ["# Initialize a dictionary to store the counts for each 'wh_id'\n", "new_unique_counts = {}\n", "\n", "# Iterate over each group in new_df\n", "for group_keys, truck_numbers_new_df in grouped_new_df:\n", "    # Get corresponding truck numbers from old_df for the same 'wh_id', if exists\n", "    truck_numbers_old_df = (\n", "        grouped_old_df.get_group(group_keys)\n", "        if group_keys in grouped_old_df.groups\n", "        else pd.Series([])\n", "    )\n", "\n", "    # Count unique truck numbers in new_df that are not in old_df for this 'wh_id'\n", "    unique_trucks = truck_numbers_new_df[\n", "        ~truck_numbers_new_df.isin(truck_numbers_old_df)\n", "    ].nunique()\n", "    new_unique_counts[group_keys] = unique_trucks\n", "\n", "\n", "new_unique_counts_df = pd.DataFrame(\n", "    list(new_unique_counts.items()), columns=[\"week_facility\", \"truck_num_not_in_old\"]\n", ")\n", "# Split the 'week_facility' column into 'week' and 'facility_id'\n", "new_unique_counts_df[[\"year_month\", \"week\", \"facility_id\"]] = pd.DataFrame(\n", "    new_unique_counts_df[\"week_facility\"].tolist(), index=new_unique_counts_df.index\n", ")\n", "# Drop the 'week_facility' column\n", "new_unique_counts_df.drop(\"week_facility\", axis=1, inplace=True)\n", "# Reorder the columns\n", "new_unique_counts_df = new_unique_counts_df[\n", "    [\"year_month\", \"week\", \"facility_id\", \"truck_num_not_in_old\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5e46d688-0fe4-4030-a1b4-d3641403db3a", "metadata": {}, "outputs": [], "source": ["# Initialize a dictionary to store the counts for each 'wh_id'\n", "new_unique_counts_ = {}\n", "\n", "# Iterate over each group in new_df\n", "for group_keys, truck_numbers_new_df in grouped_new_df_:\n", "    # Get corresponding truck numbers from old_df for the same 'wh_id', if exists\n", "    truck_numbers_old_df = (\n", "        grouped_old_df_.get_group(group_keys)\n", "        if group_keys in grouped_old_df_.groups\n", "        else pd.Series([])\n", "    )\n", "\n", "    # Count unique truck numbers in new_df that are not in old_df for this 'wh_id'\n", "    unique_trucks = truck_numbers_new_df[\n", "        ~truck_numbers_new_df.isin(truck_numbers_old_df)\n", "    ].nunique()\n", "    new_unique_counts_[group_keys] = unique_trucks\n", "\n", "\n", "new_unique_counts_df_ = pd.DataFrame(\n", "    list(new_unique_counts_.items()), columns=[\"week_facility\", \"truck_num_not_in_old\"]\n", ")\n", "# Split the 'week_facility' column into 'week' and 'facility_id'\n", "new_unique_counts_df_[[\"year_month\", \"facility_id\"]] = pd.DataFrame(\n", "    new_unique_counts_df_[\"week_facility\"].tolist(), index=new_unique_counts_df_.index\n", ")\n", "# Drop the 'week_facility' column\n", "new_unique_counts_df_.drop(\"week_facility\", axis=1, inplace=True)\n", "# Reorder the columns\n", "new_unique_counts_df_ = new_unique_counts_df_[\n", "    [\"year_month\", \"facility_id\", \"truck_num_not_in_old\"]\n", "]\n", "\n", "new_unique_counts_df_.insert(1, \"week\", \"all weeks\")\n", "\n", "new_unique_counts_df = pd.concat(\n", "    [new_unique_counts_df, new_unique_counts_df_], ignore_index=True\n", ")\n", "new_unique_counts_df.sort_values(by=[\"year_month\", \"facility_id\", \"week\"], inplace=True)\n", "new_unique_counts_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2ba62420-6ac3-42b7-917c-cf29406a51f9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5a31a93e-29e9-4730-9b2a-550d4b304bdb", "metadata": {}, "outputs": [], "source": ["old_df_modified = (\n", "    old_df[\n", "        [\n", "            \"source_facility_id\",\n", "            \"source_name\",\n", "            \"destination_outlet_id\",\n", "            \"date_\",\n", "            \"year_month\",\n", "            \"week\",\n", "            \"vehicle_number\",\n", "            \"kms_slab_as_per_loi\",\n", "            \"fixed_working_hours\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "new_df_modified = (\n", "    new_df[\n", "        [\n", "            \"facility_id\",\n", "            \"wh_name\",\n", "            \"outlet_id\",\n", "            \"trip_creation_date\",\n", "            \"year_month\",\n", "            \"week\",\n", "            \"truck_number\",\n", "            \"km_slab\",\n", "            \"hr_slab\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b6b1f163-4991-420a-93f7-d370bade4ac9", "metadata": {}, "outputs": [], "source": ["old_df_modified_ = (\n", "    old_df[\n", "        [\n", "            \"source_facility_id\",\n", "            \"source_name\",\n", "            \"destination_outlet_id\",\n", "            \"date_\",\n", "            \"year_month\",\n", "            \"vehicle_number\",\n", "            \"kms_slab_as_per_loi\",\n", "            \"fixed_working_hours\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "new_df_modified_ = (\n", "    new_df[\n", "        [\n", "            \"facility_id\",\n", "            \"wh_name\",\n", "            \"outlet_id\",\n", "            \"trip_creation_date\",\n", "            \"year_month\",\n", "            \"truck_number\",\n", "            \"km_slab\",\n", "            \"hr_slab\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "markdown", "id": "4a8472bf-b181-4987-b12d-fe2caebfeadc", "metadata": {}, "source": ["### km slab diff"]}, {"cell_type": "code", "execution_count": null, "id": "caea7012-8c01-44fd-a5ca-3a28c5f7d25e", "metadata": {}, "outputs": [], "source": ["# Merging the two dataframes on the specified columns\n", "merged_df = pd.merge(\n", "    old_df_modified,\n", "    new_df_modified,\n", "    left_on=[\n", "        \"source_facility_id\",\n", "        \"destination_outlet_id\",\n", "        \"date_\",\n", "        \"year_month\",\n", "        \"week\",\n", "        \"vehicle_number\",\n", "    ],\n", "    right_on=[\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"trip_creation_date\",\n", "        \"year_month\",\n", "        \"week\",\n", "        \"truck_number\",\n", "    ],\n", "    suffixes=(\"_old\", \"_new\"),\n", ")\n", "\n", "# Checking where km_slab is more in new_df\n", "more_km_slab_new = merged_df[merged_df[\"km_slab\"] > merged_df[\"kms_slab_as_per_loi\"]]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_km_slab_new = (\n", "    more_km_slab_new.groupby([\"year_month\", \"week\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_km_slab_new.columns = [\n", "    \"year_month\",\n", "    \"week\",\n", "    \"facility_id\",\n", "    \"km_slab_more_new\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4f4ca817-5902-4740-b80c-9973981283ca", "metadata": {}, "outputs": [], "source": ["# Merging the two dataframes on the specified columns\n", "merged_df_ = pd.merge(\n", "    old_df_modified_,\n", "    new_df_modified_,\n", "    left_on=[\n", "        \"source_facility_id\",\n", "        \"destination_outlet_id\",\n", "        \"date_\",\n", "        \"year_month\",\n", "        \"vehicle_number\",\n", "    ],\n", "    right_on=[\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"trip_creation_date\",\n", "        \"year_month\",\n", "        \"truck_number\",\n", "    ],\n", "    suffixes=(\"_old\", \"_new\"),\n", ")\n", "\n", "# Checking where km_slab is more in new_df\n", "more_km_slab_new_ = merged_df_[\n", "    merged_df_[\"km_slab\"] > merged_df_[\"kms_slab_as_per_loi\"]\n", "]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_km_slab_new_ = (\n", "    more_km_slab_new_.groupby([\"year_month\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_km_slab_new_.columns = [\"year_month\", \"facility_id\", \"km_slab_more_new\"]\n", "count_more_km_slab_new_.insert(1, \"week\", \"all weeks\")\n", "\n", "\n", "count_more_km_slab_new = pd.concat(\n", "    [count_more_km_slab_new, count_more_km_slab_new_], ignore_index=True\n", ")\n", "count_more_km_slab_new.sort_values(\n", "    by=[\"year_month\", \"facility_id\", \"week\"], inplace=True\n", ")\n", "count_more_km_slab_new.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e3cfcd32-54c7-41f8-b6b7-1960908356f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b40827e5-1f81-4814-afcb-2eaf0420217e", "metadata": {}, "outputs": [], "source": ["# Checking where km_slab is more in old_df\n", "more_km_slab_old = merged_df[merged_df[\"km_slab\"] < merged_df[\"kms_slab_as_per_loi\"]]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_km_slab_old = (\n", "    more_km_slab_old.groupby([\"year_month\", \"week\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_km_slab_old.columns = [\n", "    \"year_month\",\n", "    \"week\",\n", "    \"facility_id\",\n", "    \"km_slab_more_old\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ef13697-6258-4311-9287-e9674e94c255", "metadata": {}, "outputs": [], "source": ["# Checking where km_slab is more in old_df\n", "more_km_slab_old_ = merged_df_[\n", "    merged_df_[\"km_slab\"] < merged_df_[\"kms_slab_as_per_loi\"]\n", "]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_km_slab_old_ = (\n", "    more_km_slab_old_.groupby([\"year_month\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_km_slab_old_.columns = [\"year_month\", \"facility_id\", \"km_slab_more_old\"]\n", "\n", "count_more_km_slab_old_.insert(1, \"week\", \"all weeks\")\n", "\n", "\n", "count_more_km_slab_old = pd.concat(\n", "    [count_more_km_slab_old, count_more_km_slab_old_], ignore_index=True\n", ")\n", "count_more_km_slab_old.sort_values(\n", "    by=[\"year_month\", \"facility_id\", \"week\"], inplace=True\n", ")\n", "count_more_km_slab_old.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "20956f00-9c28-4c72-8139-11d7a980c543", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d7133f6c-dd71-4609-92ed-db9d846b1374", "metadata": {}, "source": ["### hr slab diff"]}, {"cell_type": "code", "execution_count": null, "id": "9208265b-e142-4765-ab24-b5aa6f989053", "metadata": {}, "outputs": [], "source": ["# Checking where km_slab is more in new_df\n", "more_hr_slab_new = merged_df[merged_df[\"hr_slab\"] > merged_df[\"fixed_working_hours\"]]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_hr_slab_new = (\n", "    more_hr_slab_new.groupby([\"year_month\", \"week\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_hr_slab_new.columns = [\n", "    \"year_month\",\n", "    \"week\",\n", "    \"facility_id\",\n", "    \"hr_slab_more_new\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "cbc09f07-f3e1-4d5f-a15a-8e7e3af5b780", "metadata": {}, "outputs": [], "source": ["# Checking where km_slab is more in new_df\n", "more_hr_slab_new_ = merged_df_[\n", "    merged_df_[\"hr_slab\"] > merged_df_[\"fixed_working_hours\"]\n", "]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_hr_slab_new_ = (\n", "    more_hr_slab_new_.groupby([\"year_month\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_hr_slab_new_.columns = [\"year_month\", \"facility_id\", \"hr_slab_more_new\"]\n", "\n", "count_more_hr_slab_new_.insert(1, \"week\", \"all weeks\")\n", "\n", "\n", "count_more_hr_slab_new = pd.concat(\n", "    [count_more_hr_slab_new, count_more_hr_slab_new_], ignore_index=True\n", ")\n", "count_more_hr_slab_new.sort_values(\n", "    by=[\"year_month\", \"facility_id\", \"week\"], inplace=True\n", ")\n", "count_more_hr_slab_new.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "dd89e938-f0bc-4768-85ae-aa280da1e7f9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e4ed7b1c-4ca7-4459-8c4c-fadf3b9b29ae", "metadata": {}, "outputs": [], "source": ["# Checking where km_slab is more in old_df\n", "more_hr_slab_old = merged_df[merged_df[\"hr_slab\"] < merged_df[\"fixed_working_hours\"]]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_hr_slab_old = (\n", "    more_hr_slab_old.groupby([\"year_month\", \"week\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_hr_slab_old.columns = [\n", "    \"year_month\",\n", "    \"week\",\n", "    \"facility_id\",\n", "    \"hr_slab_more_old\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "60458da4-a2f9-4c6a-8ace-85d7a6db72a1", "metadata": {}, "outputs": [], "source": ["# Checking where km_slab is more in old_df\n", "more_hr_slab_old_ = merged_df_[\n", "    merged_df_[\"hr_slab\"] < merged_df_[\"fixed_working_hours\"]\n", "]\n", "\n", "# Counting the cases grouped by 'week' and 'wh_id'\n", "count_more_hr_slab_old_ = (\n", "    more_hr_slab_old_.groupby([\"year_month\", \"facility_id\"]).size().reset_index()\n", ")\n", "\n", "count_more_hr_slab_old_.columns = [\"year_month\", \"facility_id\", \"hr_slab_more_old\"]\n", "\n", "count_more_hr_slab_old_.insert(1, \"week\", \"all weeks\")\n", "\n", "\n", "count_more_hr_slab_old = pd.concat(\n", "    [count_more_hr_slab_old, count_more_hr_slab_old_], ignore_index=True\n", ")\n", "count_more_hr_slab_old.sort_values(\n", "    by=[\"year_month\", \"facility_id\", \"week\"], inplace=True\n", ")\n", "count_more_hr_slab_old.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "95b08867-8161-45ad-936c-736583b9d9bf", "metadata": {"tags": []}, "outputs": [], "source": ["list_1 = [\"14 ft\", \"14 ft canter\", \"ref multi rep plan\", \"14 ft canter\", \"14ft canter\"]\n", "list_2 = [\"17 ft\", \"17ft\", \"17fT\", \"17 ft canter\", \"17 ft canter\", \"17ft canter\"]\n", "list_3 = [\"pickup\", \"bolero pickup\"]\n", "list_4 = [\"tata 407\"]\n", "list_5 = [\"tata ace\"]\n", "list_6 = [\"12 ft canter\"]\n", "list_7 = [\"20 ft canter\"]\n", "list_8 = [\"22 ft canter\", \"22ft canter\"]\n", "list_9 = [\"reefer bolero pickup\"]\n", "list_10 = [\"reffer vehicle \"]\n", "list_11 = [\"reefer 14 ft canter\"]\n", "\n", "for i in range(len(old_df)):\n", "    vehicle_type = old_df.iloc[i][\"vehicle_size\"]\n", "    if vehicle_type in list_1:\n", "        old_df.at[i, \"vehicle_size\"] = \"14 ft\"\n", "    elif vehicle_type in list_2:\n", "        old_df.at[i, \"vehicle_size\"] = \"17 ft\"\n", "    elif vehicle_type in list_3:\n", "        old_df.at[i, \"vehicle_size\"] = \"pickup\"\n", "    elif vehicle_type in list_4:\n", "        old_df.at[i, \"vehicle_size\"] = \"tata 407\"\n", "    elif vehicle_type in list_5:\n", "        old_df.at[i, \"vehicle_size\"] = \"tata ace\"\n", "    elif vehicle_type in list_6:\n", "        old_df.at[i, \"vehicle_size\"] = \"12 ft\"\n", "    elif vehicle_type in list_7:\n", "        old_df.at[i, \"vehicle_size\"] = \"20 ft\"\n", "    elif vehicle_type in list_8:\n", "        old_df.at[i, \"vehicle_size\"] = \"22 ft\"\n", "    elif vehicle_type in list_9:\n", "        old_df.at[i, \"vehicle_size\"] = \"reefer pickup\"\n", "    elif vehicle_type in list_10:\n", "        old_df.at[i, \"vehicle_size\"] = \"reefer vehicle\"\n", "    elif vehicle_type in list_11:\n", "        old_df.at[i, \"vehicle_size\"] = \"reefer 14 ft\""]}, {"cell_type": "code", "execution_count": null, "id": "110b4180-3804-4fab-a353-1aaa04035a92", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f4bd3a8b-657e-498c-a3c1-8a75aa23db9a", "metadata": {}, "outputs": [], "source": ["old_temp = (\n", "    old_df[[\"year_month\", \"week\", \"source_facility_id\", \"source_name\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "old_temp_ = (\n", "    old_df[[\"year_month\", \"source_facility_id\", \"source_name\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "old_temp_.insert(1, \"week\", \"all weeks\")\n", "\n", "old_temp = pd.concat([old_temp, old_temp_], ignore_index=True)\n", "old_temp.sort_values(by=[\"year_month\", \"source_facility_id\", \"week\"], inplace=True)\n", "old_temp.reset_index(drop=True, inplace=True)\n", "\n", "old_temp.columns = [\"year_month\", \"week\", \"facility_id\", \"wh_name\"]\n", "\n", "\n", "new_temp = (\n", "    new_df[[\"year_month\", \"week\", \"facility_id\", \"wh_name\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "new_temp_ = (\n", "    new_df[[\"year_month\", \"facility_id\", \"wh_name\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "new_temp_.insert(1, \"week\", \"all weeks\")\n", "\n", "new_temp = pd.concat([new_temp, new_temp_], ignore_index=True)\n", "new_temp.sort_values(by=[\"year_month\", \"facility_id\", \"week\"], inplace=True)\n", "new_temp.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "bf3a705e-f480-4566-a707-6a5d33a4b978", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b95207d1-b6ce-4dcd-81a6-49735d89b377", "metadata": {}, "outputs": [], "source": ["base_df = (\n", "    pd.concat([old_temp, new_temp], axis=0).drop_duplicates().reset_index(drop=True)\n", ")\n", "base_df = (\n", "    base_df.groupby([\"year_month\", \"week\", \"facility_id\"])[[\"wh_name\"]]\n", "    .max()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f368136a-609d-44cf-86be-05cb622d38d7", "metadata": {}, "outputs": [], "source": ["base_df[\"wh_name\"] = base_df[\"wh_name\"].str.replace(\n", "    \"Kundli- Warehouse\", \"Kundli - Feeder\"\n", ")"]}, {"cell_type": "markdown", "id": "a09b6bbb-24eb-448c-84fd-adf6b04fbd5d", "metadata": {}, "source": ["### trips without rate card"]}, {"cell_type": "code", "execution_count": null, "id": "c706a6f0-1e01-4121-9e0f-d783de54a4a3", "metadata": {}, "outputs": [], "source": ["no_rate_card = (\n", "    new_df[new_df[\"base_cost\"].isnull()]\n", "    .reset_index(drop=True)\n", "    .groupby([\"year_month\", \"week\", \"facility_id\", \"wh_name\"])[[\"trip_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "no_rate_card.columns = [\"year_month\", \"week\", \"facility_id\", \"wh_name\", \"no_rate_card\"]\n", "# no_rate_card"]}, {"cell_type": "code", "execution_count": null, "id": "0c4f6452-49ce-491c-b97c-913725a6e79e", "metadata": {}, "outputs": [], "source": ["no_rate_card_ = (\n", "    new_df[new_df[\"base_cost\"].isnull()]\n", "    .reset_index(drop=True)\n", "    .groupby([\"year_month\", \"facility_id\", \"wh_name\"])[[\"trip_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "no_rate_card_.columns = [\"year_month\", \"facility_id\", \"wh_name\", \"no_rate_card\"]\n", "no_rate_card_.insert(1, \"week\", \"all weeks\")\n", "\n", "no_rate_card = pd.concat([no_rate_card, no_rate_card_], ignore_index=True)\n", "no_rate_card.sort_values(by=[\"year_month\", \"facility_id\", \"week\"], inplace=True)\n", "no_rate_card.reset_index(drop=True, inplace=True)\n", "# no_rate_card"]}, {"cell_type": "markdown", "id": "cea4b902-b643-4034-b989-58f9454ad11a", "metadata": {}, "source": ["### trips without tolls"]}, {"cell_type": "code", "execution_count": null, "id": "f86bc218-30fb-4f3b-9c08-7f1f0774f4df", "metadata": {}, "outputs": [], "source": ["no_tolls = (\n", "    new_df[new_df[\"tolls_cost\"].isnull()]\n", "    .reset_index(drop=True)\n", "    .groupby([\"year_month\", \"week\", \"facility_id\", \"wh_name\"])[[\"trip_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "no_tolls.columns = [\"year_month\", \"week\", \"facility_id\", \"wh_name\", \"no_tolls\"]\n", "# no_tolls"]}, {"cell_type": "code", "execution_count": null, "id": "19021f19-e0cc-4c47-adb1-dbe915438dfb", "metadata": {}, "outputs": [], "source": ["no_tolls_ = (\n", "    new_df[new_df[\"tolls_cost\"].isnull()]\n", "    .reset_index(drop=True)\n", "    .groupby([\"year_month\", \"facility_id\", \"wh_name\"])[[\"trip_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "no_tolls_.columns = [\"year_month\", \"facility_id\", \"wh_name\", \"no_tolls\"]\n", "no_tolls_.insert(1, \"week\", \"all weeks\")\n", "\n", "no_tolls = pd.concat([no_tolls, no_tolls_], ignore_index=True)\n", "no_tolls.sort_values(by=[\"year_month\", \"facility_id\", \"week\"], inplace=True)\n", "no_tolls.reset_index(drop=True, inplace=True)\n", "# no_tolls"]}, {"cell_type": "code", "execution_count": null, "id": "4f6a6132-bcc5-4c27-88e7-3cb685636957", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd16f751-a8d7-4a4c-85f7-8cce60c849a7", "metadata": {"tags": []}, "outputs": [], "source": ["old_final = pd.merge(\n", "    base_df, old_metrics_df, how=\"left\", on=[\"year_month\", \"week\", \"facility_id\"]\n", ")\n", "old_final = pd.merge(\n", "    old_final,\n", "    new_unique_counts_df,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", ")\n", "old_final = pd.merge(\n", "    old_final,\n", "    count_more_km_slab_old,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", ")\n", "old_final = pd.merge(\n", "    old_final,\n", "    count_more_hr_slab_old,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", ")\n", "old_final.columns = [\n", "    \"year_month\",\n", "    \"week\",\n", "    \"facility_id\",\n", "    \"wh_name\",\n", "    \"old_overall_cost\",\n", "    \"old_unique_trucks\",\n", "    \"old_avg_km_slab\",\n", "    \"old_avg_hr_slab\",\n", "    \"old_truck_store_pair\",\n", "    \"truck_num_not_in_old\",\n", "    \"km_slab_more_old\",\n", "    \"hr_slab_more_old\",\n", "]\n", "old_final = old_final.fillna(0)\n", "old_final = old_final.round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2112721b-00a1-4cb1-a608-668654932b07", "metadata": {}, "outputs": [], "source": ["new_final = pd.merge(\n", "    base_df, new_metrics_df, how=\"left\", on=[\"year_month\", \"week\", \"facility_id\"]\n", ")\n", "new_final = pd.merge(\n", "    new_final,\n", "    old_unique_counts_df,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", ")\n", "new_final = pd.merge(\n", "    new_final,\n", "    count_more_km_slab_new,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", ")\n", "new_final = pd.merge(\n", "    new_final,\n", "    count_more_hr_slab_new,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", ")\n", "new_final = pd.merge(\n", "    new_final,\n", "    no_rate_card,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", "    suffixes=[None, \"_a\"],\n", ")\n", "new_final = pd.merge(\n", "    new_final,\n", "    no_tolls,\n", "    how=\"left\",\n", "    on=[\"year_month\", \"week\", \"facility_id\"],\n", "    suffixes=[None, \"_b\"],\n", ")\n", "new_final = new_final.drop([\"wh_name_a\", \"wh_name_b\"], axis=1)\n", "new_final.columns = [\n", "    \"year_month\",\n", "    \"week\",\n", "    \"facility_id\",\n", "    \"wh_name\",\n", "    \"new_overall_cost\",\n", "    \"new_unique_trucks\",\n", "    \"new_avg_km_slab\",\n", "    \"new_avg_hr_slab\",\n", "    \"new_truck_store_pair\",\n", "    \"truck_num_not_in_new\",\n", "    \"km_slab_more_new\",\n", "    \"hr_slab_more_new\",\n", "    \"no_rate_card\",\n", "    \"no_tolls\",\n", "]\n", "new_final = new_final.fillna(0)\n", "new_final = new_final.round(2)"]}, {"cell_type": "code", "execution_count": null, "id": "22c91928-404b-4a2c-bd1b-eb38ee053b19", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1XUN8coQ-nDrHgDKSHaEfdTRglnQhEBXKQjzbkbt6TxQ\"\n", "\n", "pb.to_sheets(old_final, sheet_id, \"cost sheet raw\")\n", "pb.to_sheets(new_final, sheet_id, \"transit raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "a1fad3b4-a85c-4cb5-8b22-d85b6523aad5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}