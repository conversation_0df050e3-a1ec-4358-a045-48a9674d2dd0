alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: cost_debug
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 4G
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 3
owner:
  email: <EMAIL>
  slack_id: U057LU69XUZ
path: povms/fleet/etl/cost_debug
paused: false
project_name: fleet
schedule:
  end_date: '2024-05-31T00:00:00'
  interval: 0 */6 * * *
  start_date: '2024-04-21T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 10
pool: povms_pool
