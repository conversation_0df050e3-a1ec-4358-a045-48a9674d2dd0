{"cells": [{"cell_type": "code", "execution_count": null, "id": "d2c09b1d-1a7a-4c56-a044-8c65b6b2525c", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "9092db31-d1a2-4309-a5c0-4f95f2f850fd", "metadata": {}, "outputs": [], "source": ["## New table\n", "trino_schema_name = \"supply_etls\"\n", "trino_table_name = \"blinkit_middle_mile_fleet_cost\"\n", "\n", "# Connecting with Redshift\n", "# redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "6436b2ae-0349-4f50-85d2-b8e4176f6ca6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1992cd72-7929-405c-9930-395962ccded5", "metadata": {}, "outputs": [], "source": ["current_date = datetime.datetime.now(timezone(\"Asia/Kolkata\"))\n", "current_day = datetime.datetime.now(timezone(\"Asia/Kolkata\")).strftime(\"%A\")\n", "\n", "# current year and month\n", "current_year = current_date.year\n", "previous_year = 2024\n", "current_month = current_date.month\n", "\n", "# previous year and month\n", "prev_year = (current_date - pd.DateOffset(months=1)).year\n", "prev_month = (current_date - pd.DateOffset(months=1)).month"]}, {"cell_type": "code", "execution_count": null, "id": "69eed69b-fb12-4460-9984-bbd10cbfdae1", "metadata": {}, "outputs": [], "source": ["current_day"]}, {"cell_type": "code", "execution_count": null, "id": "626dd4cc-7c33-4d55-92bf-a4177e18b448", "metadata": {}, "outputs": [], "source": ["current_month"]}, {"cell_type": "code", "execution_count": null, "id": "dc72399c-36cb-458e-886c-9257a3b4fc19", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1qVU-fnKHb2B5Uaq_Hu1_CxIcxa8RUOEdPf_qAcPEHsg\"\n", "sheet_name = \"links\"\n", "links_df1 = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "efe0be5d-8ebd-4212-aed9-d48090893db7", "metadata": {}, "outputs": [], "source": ["links_df = links_df1.set_index(links_df1[\"year_month\"], drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "debe066a-2b93-44f4-96f1-ccbf38b707b4", "metadata": {}, "outputs": [], "source": ["links_df"]}, {"cell_type": "code", "execution_count": null, "id": "ed7a9eb2-97d4-42f2-a2e7-f8e6bff84deb", "metadata": {}, "outputs": [], "source": ["def main_funct(year, month, sheet_id):\n", "    try:\n", "        df = pb.from_sheets(\n", "            sheetid=sheet_id, sheetname=\"Master Sheet PAN India Grocery + F&V+DS Infra\"\n", "        )\n", "    except:\n", "        df = pb.from_sheets(sheetid=sheet_id, sheetname=\"Master Sheet PAN India Grocery + F&V\")\n", "    # df = pd.read_csv('april.csv')\n", "\n", "    df.columns = df.iloc[0]\n", "    df.drop(df.index[0], inplace=True)\n", "\n", "    if year == 2022 and month == 10:\n", "\n", "        df.insert(loc=8, column=\"Vehicle no.\", value=np.nan)\n", "        # df[] = np.nan\n", "\n", "    df.drop(\n", "        [\n", "            \"Zone\",\n", "            \"Updated By\",\n", "            \"ES SAP code\",\n", "            # \"Kms Slab (as per LOI)\",\n", "            \"Remarks (Reverse/IT/Infra/Setup/Closure)\",\n", "            # \"Fixed Rate (As per contract/LOI)\",\n", "            # \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "            # \"No entry & Others\",\n", "            # \"No Entry cost\",\n", "            \" \",\n", "            \"\",\n", "        ],\n", "        axis=1,\n", "        errors=\"ignore\",\n", "        inplace=True,\n", "    )\n", "    df.dropna(axis=1, how=\"all\")\n", "    df = df.loc[:, df.columns.notna()]\n", "\n", "    df[\"Function\"] = np.where(\n", "        df[\"Function\"] == \"Express Store\",\n", "        \"Grocery\",\n", "        np.where(\n", "            df[\"Function\"] == \"Express Store - FNV\",\n", "            \"FNV\",\n", "            np.where(df[\"Function\"] == \"Express Store - Perishable\", \"Perishable\", \"Infra\"),\n", "        ),\n", "    )\n", "    df.rename(columns={\"Function\": \"category\"}, inplace=True)\n", "\n", "    pivot_cols = [\n", "        \"BE Facility ID\",\n", "        \"category\",\n", "        \"Source FC\",\n", "        \"DS/DP/FC Name\",\n", "        \"Outlet ID\",\n", "        \"Vendor Name\",\n", "        \"Vehicle no.\",\n", "        \"Fixed/Adhoc\",\n", "        \"Vehicle Size\",\n", "        \"Fixed Working Hours\",\n", "        \"No. of vehicles (MTD)\",\n", "        \"Daily/trip Cost \",\n", "        \"MTD Fixed cost\",\n", "        \"MTD adhoc cost\",\n", "        \"Total Cost ( Inc buffer)\",\n", "        \"Kms Slab (as per LOI)\",\n", "        \"Fixed Rate (As per contract/LOI)\",\n", "        \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "        # \"No entry & Others\",\n", "        \"Extra KM's Amount\",\n", "        \"Extra KM's as per GPS\",\n", "    ]\n", "\n", "    columns = df.columns.to_list()\n", "\n", "    for i in pivot_cols:\n", "        columns.remove(i)\n", "    columns\n", "\n", "    check = pivot_cols + columns\n", "    df = df[check]\n", "\n", "    num_days = calendar.monthrange(year, month)[1]\n", "    days = [datetime.date(year, month, day) for day in range(1, num_days + 1)]\n", "    start = len(pivot_cols)\n", "\n", "    end = [i for i in range(14, len(df.columns))]\n", "    column_indices = [i for i in range(start, len(df.columns))]\n", "    new_names = days\n", "    old_names = df.columns[column_indices]\n", "\n", "    df.rename(columns=dict(zip(old_names, new_names)), inplace=True)\n", "\n", "    new_df = df.melt(id_vars=pivot_cols, value_vars=days, var_name=\"date_\", value_name=\"date_cost\")\n", "\n", "    new_df = new_df[new_df[\"date_cost\"] != 0]\n", "\n", "    # replace epmty strings with nan\n", "    new_df.replace(r\"^\\s*$\", np.nan, regex=True, inplace=True)\n", "\n", "    new_df[\"date_cost\"] = new_df[\"date_cost\"].astype(float)\n", "    new_df[\"Daily/trip Cost \"] = new_df[\"Daily/trip Cost \"].astype(float)\n", "\n", "    new_df[\"date_cost\"] = new_df[\"Daily/trip Cost \"] * new_df[\"date_cost\"]\n", "\n", "    arr_cols = [\n", "        \"BE Facility ID\",\n", "        \"Source FC\",\n", "        \"Outlet ID\",\n", "        \"DS/DP/FC Name\",\n", "        \"category\",\n", "        \"date_\",\n", "        \"date_cost\",\n", "        \"Vendor Name\",\n", "        \"Vehicle no.\",\n", "        \"Fixed/Adhoc\",\n", "        \"Vehicle Size\",\n", "        \"Fixed Working Hours\",\n", "        \"No. of vehicles (MTD)\",\n", "        \"Daily/trip Cost \",\n", "        \"MTD Fixed cost\",\n", "        \"MTD adhoc cost\",\n", "        \"Total Cost ( Inc buffer)\",\n", "        \"Kms Slab (as per LOI)\",\n", "        \"Fixed Rate (As per contract/LOI)\",\n", "        \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "        \"Extra KM's Amount\",\n", "        \"Extra KM's as per GPS\",\n", "    ]\n", "\n", "    new_df = new_df[arr_cols]\n", "\n", "    new_df.rename(\n", "        columns={\n", "            \"BE Facility ID\": \"source_facility_id\",\n", "            \"Source FC\": \"source_name\",\n", "            \"Outlet ID\": \"destination_outlet_id\",\n", "            \"DS/DP/FC Name\": \"destination_name\",\n", "        },\n", "        inplace=True,\n", "    )\n", "\n", "    # new_df['date_cost'] = new_df['date_cost'].fillna(0)\n", "    new_df.dropna(subset=[\"date_cost\"], inplace=True)\n", "    # new_df['date_'] = pd.to_datetime(new_df['date_'], format='%Y-%m-%d')\n", "\n", "    new_df.columns = new_df.columns.str.strip()\n", "\n", "    wh_city_query = \"\"\" \n", "    select \n", "        pfom.facility_id, \n", "        case \n", "            when co.name like '%%budhpur%%' then 'Delhi'\n", "            else co.name\n", "            end as source_city\n", "\n", "    from \n", "        po.physical_facility_outlet_mapping pfom\n", "    left join \n", "        crates.facility cf \n", "        ON cf.id = pfom.facility_id\n", "    inner join \n", "        (   select distinct m.facility_id, c.name \n", "            from retail.console_outlet m \n", "            inner join retail.console_location c \n", "            on c.id = m.tax_location_id\n", "            where business_type_id in (1,12,19,20,21) \n", "        ) co\n", "        on pfom.facility_id = co.facility_id \n", "\n", "    where pfom.ars_active = 1 AND pfom.active = 1\n", "\n", "    group by 1,2\n", "    \"\"\"\n", "\n", "    wh_city_df = pd.read_sql_query(sql=wh_city_query, con=trino_connection)\n", "    wh_city_df[\"facility_id\"] = wh_city_df[\"facility_id\"].astype(str)\n", "\n", "    ds_city_query = \"\"\" \n", "     SELECT \n", "            DISTINCT cl.name AS destination_city, \n", "            bfom.outlet_id\n", "        FROM po.bulk_facility_outlet_mapping AS bfom\n", "    INNER JOIN \n", "        retail.console_outlet AS co \n", "        ON bfom.outlet_id = co.id AND co.business_type_id = 7       -- business_type_id = 7  means DS\n", "    LEFT JOIN \n", "        po.physical_facility_outlet_mapping AS pfom  \n", "        ON pfom.outlet_id = bfom.outlet_id\n", "    LEFT JOIN \n", "        retail.console_location AS cl \n", "        ON cl.id = pfom.city_id\n", "    \"\"\"\n", "\n", "    ds_city_df = pd.read_sql_query(sql=ds_city_query, con=trino_connection)\n", "    ds_city_df[\"outlet_id\"] = ds_city_df[\"outlet_id\"].astype(str)\n", "\n", "    new_df = pd.merge(\n", "        new_df,\n", "        wh_city_df,\n", "        how=\"left\",\n", "        left_on=\"source_facility_id\",\n", "        right_on=\"facility_id\",\n", "    )\n", "    new_df = pd.merge(\n", "        new_df,\n", "        ds_city_df,\n", "        how=\"left\",\n", "        left_on=\"destination_outlet_id\",\n", "        right_on=\"outlet_id\",\n", "    )\n", "\n", "    new_df.drop([\"facility_id\", \"outlet_id\"], axis=1, inplace=True)\n", "\n", "    return new_df"]}, {"cell_type": "code", "execution_count": null, "id": "43c12576-3f02-4185-a007-d99e8029e8b7", "metadata": {}, "outputs": [], "source": ["# # current_year_month = current_date.strftime(\"%Y-%m\")\n", "# # today = datetime.date.today()\n", "# # first = today.replace(day=1)\n", "# # last_month = first - datetime.timedelta(days=1)\n", "# # previous_year_month = last_month.strftime(\"%Y-%m\")\n", "\n", "\n", "# current_year_month = current_date.strftime(\"%Y-%m\")\n", "# today = datetime.date.today()\n", "# first = today.replace(day=1)\n", "# last_month = first - datetime.timedelta(days=1)\n", "# previous_year_month_1 = last_month.strftime(\"%Y-%m\")\n", "# year_month_2 = today.replace(month=prev_month - 1)\n", "# previous_year_month_2 = year_month_2.strftime(\"%Y-%m\")\n", "# year_month_3 = today.replace(month=prev_month - 2)\n", "# previous_year_month_3 = year_month_3.strftime(\"%Y-%m\")\n", "# year_month_4 = today.replace(month=prev_month - 3)\n", "# previous_year_month_4 = year_month_4.strftime(\"%Y-%m\")\n", "# year_month_5 = today.replace(month=prev_month - 4)\n", "# previous_year_month_5 = year_month_5.strftime(\"%Y-%m\")\n", "# year_month_6 = today.replace(month=prev_month - 5)\n", "# previous_year_month_6 = year_month_6.strftime(\"%Y-%m\")\n", "# year_month_7 = today.replace(month=prev_month - 6)\n", "# previous_year_month_7 = year_month_7.strftime(\"%Y-%m\")\n", "# year_month_8 = today.replace(month=prev_month - 7)\n", "# previous_year_month_8 = year_month_8.strftime(\"%Y-%m\")\n", "# year_month_9 = today.replace(month=prev_month - 8)\n", "# previous_year_month_9 = year_month_9.strftime(\"%Y-%m\")\n", "# year_month_10 = today.replace(month=prev_month - 9)\n", "# previous_year_month_10 = year_month_10.strftime(\"%Y-%m\")\n", "# year_month_11 = today.replace(month=prev_month - 10)\n", "# previous_year_month_11 = year_month_11.strftime(\"%Y-%m\")"]}, {"cell_type": "code", "execution_count": null, "id": "a8922971-b48e-4b06-8fec-c32108b0ac42", "metadata": {}, "outputs": [], "source": ["current_year_month = current_date.strftime(\"%Y-%m\")\n", "today = datetime.date.today()\n", "first = today.replace(day=1)\n", "\n", "last_month = first - datetime.timedelta(days=1)\n", "previous_year_month_1 = last_month.strftime(\"%Y-%m\")\n", "last_month1 = first - datetime.timedelta(days=32)\n", "previous_year_month_2 = last_month1.strftime(\"%Y-%m\")\n", "last_month2 = first - datetime.timedelta(days=63)\n", "previous_year_month_3 = last_month2.strftime(\"%Y-%m\")\n", "last_month3 = first - datetime.timedelta(days=95)\n", "previous_year_month_4 = last_month3.strftime(\"%Y-%m\")\n", "last_month4 = first - datetime.timedelta(days=127)\n", "previous_year_month_5 = last_month4.strftime(\"%Y-%m\")\n", "last_month5 = first - datetime.timedel<PERSON>(days=158)\n", "previous_year_month_6 = last_month5.strftime(\"%Y-%m\")\n", "last_month6 = first - datetime.timedelta(days=189)\n", "previous_year_month_7 = last_month6.strftime(\"%Y-%m\")\n", "last_month7 = first - datetime.timedelta(days=220)\n", "previous_year_month_8 = last_month7.strftime(\"%Y-%m\")\n", "last_month8 = first - datetime.timedelta(days=252)\n", "previous_year_month_9 = last_month8.strftime(\"%Y-%m\")\n", "last_month9 = first - datetime.timedelta(days=283)\n", "previous_year_month_10 = last_month9.strftime(\"%Y-%m\")\n", "last_month10 = first - datetime.timedelta(days=314)\n", "previous_year_month_11 = last_month10.strftime(\"%Y-%m\")\n", "last_month11 = first - datetime.timedelta(days=345)\n", "previous_year_month_12 = last_month11.strftime(\"%Y-%m\")"]}, {"cell_type": "code", "execution_count": null, "id": "35f53f59-3bc3-43ce-b455-edf0507a4868", "metadata": {}, "outputs": [], "source": ["# last_month = first - datetime.timedelta(days=32)\n", "# previous_year_month_1 = last_month.strftime(\"%Y-%m\")\n", "# previous_year_month_1"]}, {"cell_type": "code", "execution_count": null, "id": "8988c30a-3ab8-4e01-9381-67f45f3fe7a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "edfd77b6-7e91-42c6-aad7-ebd71c326e77", "metadata": {}, "outputs": [], "source": ["print(current_year_month)\n", "print(previous_year_month_1)\n", "print(previous_year_month_2)\n", "print(previous_year_month_3)\n", "print(previous_year_month_4)\n", "print(previous_year_month_5)\n", "print(previous_year_month_6)\n", "print(previous_year_month_7)\n", "print(previous_year_month_8)\n", "print(previous_year_month_9)\n", "print(previous_year_month_10)\n", "print(previous_year_month_11)\n", "print(previous_year_month_12)"]}, {"cell_type": "code", "execution_count": null, "id": "f27f171f-1756-4718-aeb7-3607a3bc8101", "metadata": {}, "outputs": [], "source": ["sheet_id1 = links_df[\"sheet_id\"][current_year_month]\n", "sheet_id2 = links_df[\"sheet_id\"][previous_year_month_1]\n", "sheet_id3 = links_df[\"sheet_id\"][previous_year_month_2]\n", "sheet_id4 = links_df[\"sheet_id\"][previous_year_month_3]\n", "sheet_id5 = links_df[\"sheet_id\"][previous_year_month_4]\n", "sheet_id6 = links_df[\"sheet_id\"][previous_year_month_5]\n", "sheet_id7 = links_df[\"sheet_id\"][previous_year_month_6]\n", "sheet_id8 = links_df[\"sheet_id\"][previous_year_month_7]\n", "sheet_id9 = links_df[\"sheet_id\"][previous_year_month_8]\n", "sheet_id10 = links_df[\"sheet_id\"][previous_year_month_9]\n", "sheet_id11 = links_df[\"sheet_id\"][previous_year_month_10]\n", "sheet_id12 = links_df[\"sheet_id\"][previous_year_month_11]\n", "sheet_id13 = links_df[\"sheet_id\"][previous_year_month_12]"]}, {"cell_type": "code", "execution_count": null, "id": "b8b953fd-35cf-4028-bcd2-a8862cd76408", "metadata": {}, "outputs": [], "source": ["previous_year_month_1"]}, {"cell_type": "code", "execution_count": null, "id": "ae300248-150d-4fa7-a891-81cef0c8d4c4", "metadata": {}, "outputs": [], "source": ["# # current_df1 = main_funct(year=current_year, month=current_month, sheet_id=sheet_id1)\n", "\n", "# current_df1 = main_funct(year=current_year, month=current_month, sheet_id=sheet_id1)\n", "# current_df2 = main_funct(year=previous_year, month=current_month - 1, sheet_id=sheet_id2)\n", "# current_df3 = main_funct(year=previous_year, month=current_month - 2, sheet_id=sheet_id3)\n", "# current_df4 = main_funct(year=previous_year, month=current_month - 3, sheet_id=sheet_id4)\n", "# current_df5 = main_funct(year=previous_year, month=current_month - 4, sheet_id=sheet_id5)\n", "# current_df6 = main_funct(year=previous_year, month=current_month - 5, sheet_id=sheet_id6)\n", "# current_df7 = main_funct(year=previous_year, month=current_month - 6, sheet_id=sheet_id7)\n", "# current_df8 = main_funct(year=previous_year, month=current_month - 7, sheet_id=sheet_id8)\n", "# current_df9 = main_funct(year=previous_year, month=current_month - 8, sheet_id=sheet_id9)\n", "# current_df10 = main_funct(year=previous_year, month=current_month - 9, sheet_id=sheet_id10)\n", "# current_df11 = main_funct(year=previous_year, month=current_month - 10, sheet_id=sheet_id11)\n", "# current_df12 = main_funct(year=previous_year, month=current_month - 11, sheet_id=sheet_id12)\n", "# current_df13 = main_funct(year=previous_year, month=current_month - 12, sheet_id=sheet_id13)"]}, {"cell_type": "code", "execution_count": null, "id": "85880997-0c3c-485b-80cf-2fae42498672", "metadata": {}, "outputs": [], "source": ["# # current_df1 = main_funct(year=current_year, month=current_month, sheet_id=sheet_id1)\n", "\n", "# current_df1 = main_funct(year=current_year, month=current_month, sheet_id=sheet_id1)\n", "# current_df2 = main_funct(year=previous_year, month=12, sheet_id=sheet_id2)\n", "# current_df3 = main_funct(year=previous_year, month=11, sheet_id=sheet_id3)\n", "# current_df4 = main_funct(year=previous_year, month=10, sheet_id=sheet_id4)\n", "# current_df5 = main_funct(year=previous_year, month=9, sheet_id=sheet_id5)\n", "# current_df6 = main_funct(year=previous_year, month=8, sheet_id=sheet_id6)\n", "# current_df7 = main_funct(year=previous_year, month=7, sheet_id=sheet_id7)\n", "# current_df8 = main_funct(year=previous_year, month=6, sheet_id=sheet_id8)\n", "# current_df9 = main_funct(year=previous_year, month=5, sheet_id=sheet_id9)\n", "# current_df10 = main_funct(year=previous_year, month=4, sheet_id=sheet_id10)\n", "# current_df11 = main_funct(year=previous_year, month=3, sheet_id=sheet_id11)\n", "# current_df12 = main_funct(year=previous_year, month=2, sheet_id=sheet_id12)\n", "# current_df13 = main_funct(year=previous_year, month=1, sheet_id=sheet_id13)"]}, {"cell_type": "code", "execution_count": null, "id": "c12e9894-087d-45bd-8f2d-16d435b7583a", "metadata": {}, "outputs": [], "source": ["current_df1 = main_funct(year=current_year, month=current_month, sheet_id=sheet_id1)\n", "current_df2 = main_funct(year=current_year, month=5, sheet_id=sheet_id2)\n", "current_df3 = main_funct(year=current_year, month=4, sheet_id=sheet_id3)\n", "current_df4 = main_funct(year=current_year, month=3, sheet_id=sheet_id4)\n", "current_df5 = main_funct(year=current_year, month=2, sheet_id=sheet_id5)\n", "\n", "current_df6 = main_funct(year=previous_year, month=1, sheet_id=sheet_id6)\n", "current_df7 = main_funct(year=previous_year, month=12, sheet_id=sheet_id7)\n", "current_df8 = main_funct(year=previous_year, month=11, sheet_id=sheet_id8)\n", "current_df9 = main_funct(year=previous_year, month=10, sheet_id=sheet_id9)\n", "current_df10 = main_funct(year=previous_year, month=9, sheet_id=sheet_id10)\n", "current_df11 = main_funct(year=previous_year, month=8, sheet_id=sheet_id11)\n", "current_df12 = main_funct(year=previous_year, month=7, sheet_id=sheet_id12)\n", "current_df13 = main_funct(year=previous_year, month=6, sheet_id=sheet_id13)"]}, {"cell_type": "code", "execution_count": null, "id": "5699e254-4a59-4490-9eeb-c032f544e5aa", "metadata": {}, "outputs": [], "source": ["current_month"]}, {"cell_type": "code", "execution_count": null, "id": "d166d195-b48f-43d5-861f-0a7ef76d0c99", "metadata": {}, "outputs": [], "source": ["# current_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5ca23e27-f207-496d-ae9c-6867611066c2", "metadata": {}, "outputs": [], "source": ["# current_df1.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "6e9441cd-554c-4489-a6cc-2fe3826137a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "109835f8-5724-42d6-9e0f-257914698664", "metadata": {}, "outputs": [], "source": ["# if current_month == 1:\n", "#     current_df2 = main_funct(year=prev_year, month=12, sheet_id=sheet_id2)\n", "# else:\n", "#     current_df2 = main_funct(\n", "#         year=current_year, month=current_month - 1, sheet_id=sheet_id2\n", "#     )\n", "\n", "# current_df = current_df1.append(current_df2, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "7f488ad9-fa10-4e14-a187-049a2f0e7410", "metadata": {}, "outputs": [], "source": ["# curr_df = current_df1.append(current_df2, ignore_index=True)\n", "# current_df = curr_df.append(current_df3, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "eca9dd39-9792-466f-bb77-362dcc7d0394", "metadata": {}, "outputs": [], "source": ["curr_df = current_df1.append(current_df2, ignore_index=True)\n", "curr_df1 = curr_df.append(current_df3, ignore_index=True)\n", "curr_df2 = curr_df1.append(current_df4, ignore_index=True)\n", "curr_df3 = curr_df2.append(current_df5, ignore_index=True)\n", "curr_df4 = curr_df3.append(current_df6, ignore_index=True)\n", "curr_df5 = curr_df4.append(current_df7, ignore_index=True)\n", "curr_df6 = curr_df5.append(current_df8, ignore_index=True)\n", "curr_df7 = curr_df6.append(current_df9, ignore_index=True)\n", "curr_df8 = curr_df7.append(current_df10, ignore_index=True)\n", "curr_df9 = curr_df8.append(current_df11, ignore_index=True)\n", "curr_df10 = curr_df9.append(current_df12, ignore_index=True)\n", "current_df = curr_df10.append(current_df13, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a55477c3-ab38-4c33-ae9f-d836849f55ca", "metadata": {}, "outputs": [], "source": ["# current_df2.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "7cae36ed-2b32-4704-a4c9-58f1af14fbfc", "metadata": {}, "outputs": [], "source": ["current_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "fac00e17-e271-4b7c-bd7e-254e5e16b78b", "metadata": {}, "outputs": [], "source": ["current_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ff32cabd-1c6d-459e-bb3a-0f5164e5a540", "metadata": {}, "outputs": [], "source": ["current_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e3787992-2559-452e-af7f-80716d104503", "metadata": {}, "outputs": [], "source": ["# current_df[\"date_\"] = pd.to_datetime(current_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "08a9884a-e897-438f-9d29-76d1e0eec76d", "metadata": {}, "outputs": [], "source": ["current_df[\"source_facility_id\"] = current_df[\"source_facility_id\"].fillna(value=-1)  # coded as -1\n", "current_df[\"source_facility_id\"] = current_df[\"source_facility_id\"].astype(\"int\")\n", "\n", "current_df[\"source_name\"] = current_df[\"source_name\"].astype(\"str\")\n", "\n", "current_df[\"destination_outlet_id\"] = current_df[\"destination_outlet_id\"].fillna(\n", "    value=-1\n", ")  # coded as -1\n", "current_df[\"destination_outlet_id\"] = current_df[\"destination_outlet_id\"].astype(\"int\")\n", "\n", "current_df[\"destination_name\"] = current_df[\"destination_name\"].astype(\"str\")\n", "\n", "current_df[\"category\"] = current_df[\"category\"].astype(\"str\")\n", "\n", "# current_df[\"date_\"] = pd.to_datetime(current_df[\"date_\"]) kept as varchar for now\n", "\n", "current_df[\"date_cost\"] = current_df[\"date_cost\"].astype(\"float\")\n", "\n", "current_df[\"Vendor Name\"] = current_df[\"Vendor Name\"].astype(\"str\")\n", "\n", "current_df[\"Vehicle no.\"] = current_df[\"Vehicle no.\"].astype(\"str\")\n", "\n", "current_df[\"Fixed/Adhoc\"] = current_df[\"Fixed/Adhoc\"].astype(\"str\")\n", "\n", "current_df[\"Vehicle Size\"] = current_df[\"Vehicle Size\"].astype(\"str\")\n", "\n", "current_df[\"Fixed Working Hours\"] = current_df[\"Fixed Working Hours\"].astype(\"str\")\n", "\n", "current_df[\"No. of vehicles (MTD)\"] = current_df[\"No. of vehicles (MTD)\"].fillna(\n", "    value=0\n", ")  # coded as 0\n", "current_df[\"No. of vehicles (MTD)\"] = current_df[\"No. of vehicles (MTD)\"].astype(\n", "    \"float\"\n", ")  # kept as varchar for now\n", "\n", "current_df[\"Daily/trip Cost\"] = current_df[\"Daily/trip Cost\"].astype(\"float\")\n", "\n", "current_df[\"MTD Fixed cost\"] = current_df[\"MTD Fixed cost\"].fillna(value=0)  # coded as 0\n", "current_df[\"MTD Fixed cost\"] = current_df[\"MTD Fixed cost\"].astype(\n", "    \"float\"\n", ")  # kept as varchar for now\n", "\n", "current_df[\"MTD adhoc cost\"] = current_df[\"MTD adhoc cost\"].fillna(value=0)  # coded as 0\n", "current_df[\"MTD adhoc cost\"] = current_df[\"MTD adhoc cost\"].astype(\n", "    \"float\"\n", ")  # kept as varchar for now\n", "\n", "current_df[\"Total Cost ( Inc buffer)\"] = current_df[\"Total Cost ( Inc buffer)\"].fillna(\n", "    value=0\n", ")  # coded as 0\n", "current_df[\"Total Cost ( Inc buffer)\"] = current_df[\"Total Cost ( Inc buffer)\"].astype(\n", "    \"float\"\n", ")  # kept as varchar for now\n", "\n", "current_df[\"Kms Slab (as per LOI)\"] = current_df[\"Kms Slab (as per LOI)\"].fillna(\n", "    value=0\n", ")  # coded as 0\n", "current_df[\"Kms Slab (as per LOI)\"] = current_df[\"Kms Slab (as per LOI)\"].astype(\"float\")\n", "\n", "current_df[\"Fixed Rate (As per contract/LOI)\"] = current_df[\n", "    \"Fixed Rate (As per contract/LOI)\"\n", "].fillna(\n", "    value=0\n", ")  # coded as 0\n", "current_df[\"Fixed Rate (As per contract/LOI)\"] = current_df[\n", "    \"Fixed Rate (As per contract/LOI)\"\n", "].astype(\"float\")\n", "\n", "current_df[\"Tolls/Parking/MCD/NGT (valid slips)\"] = current_df[\n", "    \"Tolls/Parking/MCD/NGT (valid slips)\"\n", "].fillna(\n", "    value=0\n", ")  # coded as 0\n", "current_df[\"Tolls/Parking/MCD/NGT (valid slips)\"] = current_df[\n", "    \"Tolls/Parking/MCD/NGT (valid slips)\"\n", "].astype(\"float\")\n", "\n", "current_df[\"Extra KM's Amount\"] = current_df[\"Extra KM's Amount\"].fillna(value=0)  # coded as 0\n", "current_df[\"Extra KM's Amount\"] = current_df[\"Extra KM's Amount\"].astype(\"float\")\n", "\n", "current_df[\"Extra KM's as per GPS\"] = current_df[\"Extra KM's as per GPS\"].fillna(\n", "    value=0\n", ")  # coded as 0\n", "current_df[\"Extra KM's as per GPS\"] = current_df[\"Extra KM's as per GPS\"].astype(\"float\")\n", "\n", "current_df[\"source_city\"] = current_df[\"source_city\"].astype(\"str\")\n", "\n", "current_df[\"destination_city\"] = current_df[\"destination_city\"].astype(\"str\")"]}, {"cell_type": "code", "execution_count": null, "id": "bc904ad0-6f40-498c-9641-9d52b594d33e", "metadata": {}, "outputs": [], "source": ["current_df = current_df.rename(\n", "    columns={\n", "        \"Vendor Name\": \"vendor_name\",\n", "        \"Vehicle no.\": \"vehicle_number\",\n", "        \"Fixed/Adhoc\": \"fleet_type\",\n", "        \"Vehicle Size\": \"vehicle_size\",\n", "        \"Fixed Working Hours\": \"fixed_working_hours\",\n", "        \"No. of vehicles (MTD)\": \"number_of_vehicles_mtd\",\n", "        \"Daily/trip Cost\": \"daily_trip_cost\",\n", "        \"MTD Fixed cost\": \"mtd_fixed_cost\",\n", "        \"MTD adhoc cost\": \"mtd_adhoc_cost\",\n", "        \"Total Cost ( Inc buffer)\": \"total_cost_inc_buffer\",\n", "        \"Kms Slab (as per LOI)\": \"kms_slab_as_per_loi\",\n", "        \"Fixed Rate (As per contract/LOI)\": \"fixed_rate_as_per_loi\",\n", "        \"Tolls/Parking/MCD/NGT (valid slips)\": \"tolls_parking_others\",\n", "        \"Extra KM's Amount\": \"amt_extra_kms\",\n", "        \"Extra KM's as per GPS\": \"gps_extra_kms\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7937cada-7953-4e37-bb1e-ff2296fa7538", "metadata": {}, "outputs": [], "source": ["current_df[\"row_differentiator\"] = (\n", "    current_df.sort_values([\"date_\"], ascending=[True])\n", "    .groupby([\"date_\", \"source_facility_id\", \"destination_outlet_id\", \"vehicle_number\"])\n", "    .cumcount()\n", "    + 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ad87d0ae-8dcd-4fe7-8235-0276469248c9", "metadata": {}, "outputs": [], "source": ["current_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "2005a3e0-ea0e-4c2f-bbbd-5811e5f08cd8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4f2f9e95-6766-4de3-96ec-7e3cf6879da4", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"source_facility_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"source facility id\",\n", "        },\n", "        {\"name\": \"source_name\", \"type\": \"varchar\", \"description\": \"source name\"},\n", "        {\n", "            \"name\": \"destination_outlet_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"destination outlet id\",\n", "        },\n", "        {\n", "            \"name\": \"destination_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination name\",\n", "        },\n", "        {\"name\": \"category\", \"type\": \"varchar\", \"description\": \"category\"},\n", "        {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Date\"},\n", "        {\"name\": \"date_cost\", \"type\": \"double\", \"description\": \"cost for the date\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"Vendor Name\"},\n", "        {\"name\": \"vehicle_number\", \"type\": \"varchar\", \"description\": \"Vehicle no.\"},\n", "        {\"name\": \"fleet_type\", \"type\": \"varchar\", \"description\": \"Fixed/Adhoc\"},\n", "        {\"name\": \"vehicle_size\", \"type\": \"varchar\", \"description\": \"Vehicle Size\"},\n", "        {\n", "            \"name\": \"fixed_working_hours\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Fixed Working Hours\",\n", "        },\n", "        {\n", "            \"name\": \"number_of_vehicles_mtd\",\n", "            \"type\": \"double\",\n", "            \"description\": \"No. of vehicles (MTD)\",\n", "        },\n", "        {\n", "            \"name\": \"daily_trip_cost\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Daily/trip Cost\",\n", "        },\n", "        {\"name\": \"mtd_fixed_cost\", \"type\": \"double\", \"description\": \"MTD Fixed cost\"},\n", "        {\"name\": \"mtd_adhoc_cost\", \"type\": \"double\", \"description\": \"MTD adhoc cost\"},\n", "        {\n", "            \"name\": \"total_cost_inc_buffer\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Total Cost ( Inc buffer)\",\n", "        },\n", "        {\n", "            \"name\": \"kms_slab_as_per_loi\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Kms Slab (as per LOI)\",\n", "        },\n", "        {\n", "            \"name\": \"fixed_rate_as_per_loi\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Fixed Rate (As per contract/LOI)\",\n", "        },\n", "        {\n", "            \"name\": \"tolls_parking_others\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "        },\n", "        {\n", "            \"name\": \"amt_extra_kms\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Extra KM's Amount\",\n", "        },\n", "        {\n", "            \"name\": \"gps_extra_kms\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Extra KM's as per GPS\",\n", "        },\n", "        {\"name\": \"source_city\", \"type\": \"varchar\", \"description\": \"source city\"},\n", "        {\n", "            \"name\": \"destination_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination city\",\n", "        },\n", "        {\n", "            \"name\": \"row_differentiator\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Key to differentiate rows\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"source_facility_id\",\n", "        \"destination_outlet_id\",\n", "        \"vehicle_number\",\n", "        \"row_differentiator\",\n", "    ],  # list\n", "    \"partition_key\": [\"date_\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"date_\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"Base table for fleet costs\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(current_df, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "b638fbec-5690-44be-b4ca-4b48dd062286", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c78e00c0-fe5f-459b-bee6-a71659a85f74", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2a32799c-13b1-4607-934e-807f24f01da6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "715d37e1-4162-423a-8b05-4a0dfcdaf87d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "78b0b11c-5c4d-4446-90bc-2904584de167", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cc203ecf-6851-47a1-95de-8f428a8b0fd5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "381f678a-66e6-4de6-bc7f-572a159ab654", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2e0e38ae-54d2-44fc-b05f-65681f34f267", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}