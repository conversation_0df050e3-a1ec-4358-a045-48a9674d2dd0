{"cells": [{"cell_type": "code", "execution_count": null, "id": "cb3369cb-9da1-4a7c-9da1-adbf03d9a3a4", "metadata": {}, "outputs": [], "source": ["# !pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "cc2f445d-a366-47b3-81c6-ef758383e1ab", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "\n", "# from pandasql import sqldf\n", "import datetime\n", "import calendar\n", "from pytz import timezone\n", "from datetime import datetime\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "0a96cd1b-7c23-4901-a692-19596305f57f", "metadata": {}, "outputs": [], "source": ["trino_schema_name = \"supply_etls\"  ##supply_etls\n", "trino_table_name = \"fleet_cms_cost\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "633b1154-0c64-4832-b285-6f4b95fc0251", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "44cc1a5a-3e72-452e-8e4e-149975de28ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d3663e81-456f-41b2-9a6b-d595783d004e", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "    with cost_base as\n", "    (WITH base AS (\n", "  SELECT\n", "    *\n", "  FROM\n", "    fleet_management.fleet_management_trips_cost\n", "    CROSS JOIN UNNEST(destination_node_ids) AS t (ds_node_id)\n", "  WHERE\n", "    --trip_id = '24049_1566274'AND \n", "    insert_ds_ist >= cast(current_date - interval '70' day as varchar)\n", "    AND status <> 'DISCARDED'\n", "    and lake_active_record\n", "),\n", "store_costs as(\n", "SELECT\n", "  trip_id,\n", "  node_id_key AS node_id,\n", "  status,\n", "  CAST(json_extract_scalar(node_value_json, '$.fixed_cost') AS double) AS fixed_cost,\n", "  CAST(json_extract_scalar(node_value_json, '$.tolls_cost') AS double) AS tolls_cost,\n", "  CAST(json_extract_scalar(node_value_json, '$.misc_charges') AS double) AS misc_charges,\n", "  cast(json_extract(cost_breakdown,'$.extra_hr_cost') as int) AS extra_hr_cost,\n", "  cast(json_extract(cost_breakdown,'$.extra_km_cost') as int) AS extra_km_cost,\n", "  cast(json_extract(cost_breakdown,'$.extra_driver_cost_for_trip') as int) AS extra_driver_cost_for_trip\n", "\n", "FROM fleet_management.fleet_management_trips_cost\n", "CROSS JOIN UNNEST(\n", "  map_entries(\n", "    CAST(json_extract(cost_breakdown, '$.store_wise_costs') AS map<varchar, json>)\n", "  )\n", ") AS t (node_id_key, node_value_json)\n", "WHERE --trip_id = '24049_1566274'AND \n", "    insert_ds_ist >=  cast(current_date - interval '70' day as varchar)\n", "  AND coalesce(trip_tags, '') != 'DEDICATED'\n", "    and lake_active_record\n", "UNION ALL\n", "\n", "-- Dedicated trips (trip-level cost)\n", "SELECT\n", "  trip_id,\n", "  ds_node_id as node_id,\n", "  status,\n", "  CAST(json_extract_scalar(cost_breakdown, '$.fixed_cost') AS double) AS fixed_cost,\n", "  CAST(json_extract_scalar(cost_breakdown, '$.tolls_cost') AS double) AS tolls_cost,\n", "  CAST(json_extract_scalar(cost_breakdown, '$.misc_charges') AS double) AS misc_charges,\n", "  cast(json_extract(cost_breakdown,'$.extra_hr_cost') as int) AS extra_hr_cost,\n", "  cast(json_extract(cost_breakdown,'$.extra_km_cost') as int) AS extra_km_cost,\n", "  cast(json_extract(cost_breakdown,'$.extra_driver_cost_for_trip') as int) AS extra_driver_cost_for_trip\n", "\n", "FROM fleet_management.fleet_management_trips_cost\n", "CROSS JOIN UNNEST(destination_node_ids) AS t (ds_node_id)\n", "WHERE --trip_id = '25769_1704484'AND\n", "    insert_ds_ist IS NOT NULL\n", "  AND coalesce(trip_tags, '') = 'DEDICATED' and lake_active_record\n", ")\n", "\n", "\n", "select\n", "  b.*, \n", "  \n", "  s.fixed_cost as con_fixed_cost,\n", "  s.tolls_cost con_tolls_cost,\n", "  s.misc_charges as con_misc_charges,\n", "  extra_hr_cost,\n", "  extra_km_cost,\n", "  extra_driver_cost_for_trip \n", "  \n", "from\n", "  base b\n", "  LEFT JOIN store_costs s ON b.trip_id = s.trip_id\n", "  AND b.ds_node_id = s.node_id\n", " --where b.trip_id='24049_1566274'\n", " ),\n", "    \n", "con_data as\n", "    (\n", "    select  consignment_id,\n", "            trip_id,\n", "            consignment_type,\n", "            pc.source_id,\n", "            pc.destination_id,\n", "            state,\n", "            m.outlet_id as ds_outlet_id,\n", "            m.name\n", "    from transit_server.transit_projection_consignment pc\n", "    left join retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = pc.destination_id\n", "    where --insert_ds_ist >= cast('2024-07-28' as varchar)\n", "    insert_ds_ist >= cast(current_date - interval '70' day as varchar)\n", "      and consignment_type = 'FORWARD_CONSIGNMENT'\n", "    )\n", "\n", "select  max(cast(coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as date)) as trip_creation_date,\n", "        cd.consignment_id,\n", "        cte.*\n", "from\n", "    (   \n", "    select  tc.trip_id,\n", "            tc.source_node_id as wh_node_id,\n", "            m1.outlet_id as wh_outlet_id,\n", "            co1.facility_id as wh_facility_id,\n", "            m1.name as wh_outlet_name,\n", "            ds_node_id as ds_node_id,\n", "            m2.outlet_id as ds_outlet_id,\n", "            m2.name as ds_outlet_name,\n", "            case when trim(co2.location) = 'Bangalore' then 'Bengaluru' else trim(co2.location) end as destination_city,\n", "            tc.truck_number,\n", "            tc.truck_type,\n", "            billable_truck_type,\n", "            truck_billing_type,\n", "            km_slab as total_km_travelled,\n", "            hr_slab as total_hr_travelled,\n", "            coalesce(cast(vendor_id as varchar),cast(json_extract(vendor_details,'$.id') as varchar)) as vendor_id,\n", "            coalesce(cast(json_extract(vendor_details,'$.name') as varchar),mv.name) as vendor_name,\n", "            sum(con_fixed_cost) over (partition by trip_id) as fixed_cost,\n", "            sum(con_misc_charges) over (partition by trip_id)  as misc_charges,\n", "            sum(con_tolls_cost) over (partition by trip_id) as tolls_cost,\n", "            extra_hr_cost,\n", "            extra_km_cost,\n", "            extra_driver_cost_for_trip,\n", "            sum(con_fixed_cost) over (partition by trip_id) + sum(con_fixed_cost) over (partition by trip_id) as total_trip_cost,\n", "            status,\n", "            con_fixed_cost + con_misc_charges as total_con_cost\n", "           --(fixed_cost + misc_charges)/count(*) over (partition by tc.trip_id) as total_con_cost\n", "           \n", "    from cost_base tc\n", "    left join retail.console_outlet_logistic_mapping m1 ON cast(m1.logistic_node_id as varchar) = tc.source_node_id\n", "    left join retail.console_outlet_logistic_mapping m2 ON cast(m2.logistic_node_id as varchar) = tc.ds_node_id\n", "    left join retail.console_outlet co1 ON co1.id = m1.outlet_id and co1.name not like '%%HOT%%' and co1.name <> 'K3 T K4' --and co.business_type_id in (1,12,19,20)\n", "    left join retail.console_outlet co2 ON co2.id = m2.outlet_id \n", "    left join fleet_management.fleet_management_vendor mv on mv.id = tc.vendor_id\n", "    where m1.lake_active_record\n", "      and m2.lake_active_record\n", "    ) cte\n", "left join\n", "        (\n", "            select distinct trip_id, (actual_ts + interval '330' MINUTE) as trip_creation_time_1\n", "            from transit_server.transit_projection_trip_event_timeline\n", "            where \n", "            --insert_ds_ist >= cast('2024-07-28' as varchar)\n", "            insert_ds_ist >= cast((current_date - interval '75' day) as varchar)\n", "            and event_type = 'LOADING_START'\n", "            and actual_ts is not null\n", "            and cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT'\n", "            and lake_active_record\n", "        ) as pte on cte.trip_id = pte.trip_id\n", "left join transit_server.transit_projection_trip pt ON pt.trip_id = cte.trip_id\n", "\n", "--and (pt.insert_ds_ist between cast('2024-07-28' as varchar) and cast(current_date as varchar))\n", "and (pt.insert_ds_ist between cast(current_date - interval '75' day as varchar) and cast(current_date as varchar))\n", "left join con_data cd on cd.trip_id = cte.trip_id and cd.ds_outlet_id = cte.ds_outlet_id\n", "--where --cte.trip_id='24049_1566274'\n", "group by 2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f106c672-b321-49f1-ad4d-e8bb0310d957", "metadata": {}, "outputs": [], "source": ["cms_df = read_sql_query(query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "828683c8-cf7a-49c3-b5ef-082dbed1bdb6", "metadata": {}, "outputs": [], "source": ["cms_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0e6d589a-a987-48db-a003-c1faac429c15", "metadata": {}, "outputs": [], "source": ["cms_df.consignment_id.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "c9796f9a-a9cd-4c22-abec-a393d9630e4a", "metadata": {}, "outputs": [], "source": ["cms_df.trip_id.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ac52a15e-877a-4322-84e3-025798c815cb", "metadata": {}, "outputs": [], "source": ["cms_df[cms_df[\"trip_id\"] == \"24049_1566274\"]"]}, {"cell_type": "code", "execution_count": null, "id": "257b7155-8a42-41b6-a670-82012b59ecc8", "metadata": {}, "outputs": [], "source": ["cms_df.vendor_name.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "50b994f7-06b5-4607-9cb4-5b96ad5440bd", "metadata": {}, "outputs": [], "source": ["# cms_df.to_csv(\"cms_df.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8a7e25ce-2546-459f-9b59-061fb1faea88", "metadata": {}, "outputs": [], "source": ["# cms_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4814243d-c8db-45d0-b50a-22e0df69ce67", "metadata": {}, "outputs": [], "source": ["cms_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "913bcede-381f-407d-99f1-24f781ef1d90", "metadata": {}, "outputs": [], "source": ["cms_df[\"trip_creation_date\"] = pd.to_datetime(cms_df[\"trip_creation_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "36d74cd2-9334-4ad8-8a8e-c99c28107e3f", "metadata": {}, "outputs": [], "source": ["cms_df[\"consignment_id\"] = cms_df[\"consignment_id\"].fillna(value=0)"]}, {"cell_type": "code", "execution_count": null, "id": "572108a7-9b02-47f8-9f03-5be2e8f6acb2", "metadata": {}, "outputs": [], "source": ["cms_df[\"consignment_id\"] = cms_df[\"consignment_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "667a6091-9f4e-4742-923f-386f86f227ad", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_node_id\"] = cms_df[\"wh_node_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "757f57f0-9ad8-456a-a769-b2931d2f22bf", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_outlet_id\"] = cms_df[\"wh_outlet_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "da245c39-87eb-473e-860a-d426d6afcac8", "metadata": {}, "outputs": [], "source": ["cms_df[\"wh_facility_id\"] = cms_df[\"wh_facility_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "279d7537-7f28-4b96-913f-47b541cfab56", "metadata": {}, "outputs": [], "source": ["cms_df[\"ds_node_id\"] = cms_df[\"ds_node_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "843dad55-318a-4164-a72b-db8528e7480e", "metadata": {}, "outputs": [], "source": ["cms_df[\"vendor_id\"] = cms_df[\"vendor_id\"].fillna(value=0)\n", "\n", "cms_df[\"vendor_id\"] = cms_df[\"vendor_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "16f47c16-ddaa-406b-9997-3159348e4a85", "metadata": {}, "outputs": [], "source": ["cms_df[\"fixed_cost\"] = cms_df[\"fixed_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "f235292e-2d5f-4d58-9241-44de5c3d4005", "metadata": {}, "outputs": [], "source": ["cms_df[\"tolls_cost\"] = cms_df[\"tolls_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "e805c24b-9ccb-44d5-a05b-12db073a1514", "metadata": {}, "outputs": [], "source": ["cms_df[\"misc_charges\"] = cms_df[\"misc_charges\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "9f3dcc8c-7890-4c22-b012-67ce4461a210", "metadata": {}, "outputs": [], "source": ["cms_df[\"total_trip_cost\"] = cms_df[\"total_trip_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "e2239b86-4e24-4736-9127-616b36cc5820", "metadata": {}, "outputs": [], "source": ["cms_df[\"total_con_cost\"] = cms_df[\"total_con_cost\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "aa491135-7ab2-428c-9a03-83b64577d672", "metadata": {}, "outputs": [], "source": ["cms_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "edf3c345-f356-4b98-ae90-c37ed5500403", "metadata": {}, "outputs": [], "source": ["cms_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "99b0a70a-b375-46ec-91d2-e7aedf8ec2bf", "metadata": {}, "outputs": [], "source": ["# df=cms_df.groupby(['trip_creation_date','trip_id','wh_outlet_id','ds_outlet_id']).nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "522690b7-305b-4895-bc62-49ac17dfa1b1", "metadata": {}, "outputs": [], "source": ["# df.to_csv('df.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "bf358b16-ad11-4222-9c67-bb058405e254", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d88a6f76-ec7c-4da6-89ca-62ad0a7cf7ca", "metadata": {}, "outputs": [], "source": ["# x = cms_df.groupby(['trip_creation_date','trip_id','wh_outlet_id','ds_outlet_id'], as_index=False).count()\n", "# x[x['consignment_id']>1]"]}, {"cell_type": "code", "execution_count": null, "id": "cf77c813-10b0-4783-90be-b340b0bc73d8", "metadata": {}, "outputs": [], "source": ["# cms_df[(cms_df[\"trip_id\"] == \"13582_867047\") & (cms_df[\"ds_outlet_id\"] == 4396)]"]}, {"cell_type": "code", "execution_count": null, "id": "2af49037-dd40-4ba6-9dbe-f9cf19cbe7f6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ec71ca4d-fd9a-465c-bbbb-e6619abe6453", "metadata": {}, "outputs": [], "source": ["cms_df1 = cms_df.groupby([\"trip_id\", \"wh_outlet_id\", \"ds_outlet_id\"], as_index=False).agg(\n", "    {\n", "        \"trip_creation_date\": \"max\",\n", "        \"consignment_id\": \"max\",\n", "        \"wh_node_id\": \"max\",\n", "        \"wh_facility_id\": \"max\",\n", "        \"wh_outlet_name\": \"max\",\n", "        \"ds_node_id\": \"max\",\n", "        \"ds_outlet_name\": \"max\",\n", "        \"destination_city\": \"max\",\n", "        \"truck_number\": \"max\",\n", "        \"truck_type\": \"max\",\n", "        \"billable_truck_type\": \"max\",\n", "        \"truck_billing_type\": \"max\",\n", "        \"total_km_travelled\": \"max\",\n", "        \"total_hr_travelled\": \"max\",\n", "        \"vendor_id\": \"max\",\n", "        \"vendor_name\": \"max\",\n", "        \"fixed_cost\": \"max\",\n", "        \"misc_charges\": \"max\",\n", "        \"tolls_cost\": \"max\",\n", "        \"extra_hr_cost\": \"max\",\n", "        \"extra_km_cost\": \"max\",\n", "        \"extra_driver_cost_for_trip\": \"max\",\n", "        \"total_trip_cost\": \"max\",\n", "        \"status\": \"max\",\n", "        \"total_con_cost\": \"max\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "74b74b87-7490-49ed-a350-6e855c6b0578", "metadata": {}, "outputs": [], "source": ["cms_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "dbacf874-175d-4971-803d-2a53839f6853", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "69b1f973-ffc6-42a6-90d5-6224bb20c095", "metadata": {}, "outputs": [], "source": ["cms_df1[cms_df1[\"trip_id\"] == \"21874_990372\"]"]}, {"cell_type": "code", "execution_count": null, "id": "76e55cbe-29cc-4b5a-aae5-6b794d01ee94", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "db31b7ce-a4e8-4e4a-ba42-f1c741ebde5a", "metadata": {}, "outputs": [], "source": ["# cms = cms_df1.merge(cms_df,how=\"left\",on=['trip_id','ds_outlet_id'])"]}, {"cell_type": "code", "execution_count": null, "id": "6e683fb9-ffb2-418f-bd7d-cc7835c264c8", "metadata": {}, "outputs": [], "source": ["cms_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "83081e74-bc22-431c-b6af-fcb98c5127bc", "metadata": {}, "outputs": [], "source": ["cms_df1[(cms_df1[\"trip_id\"] == \"13582_867047\") & (cms_df1[\"ds_outlet_id\"] == 4396)]"]}, {"cell_type": "code", "execution_count": null, "id": "5819e8ca-5c98-4ed8-86be-d320f41ec323", "metadata": {}, "outputs": [], "source": ["cms_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "ea4cf678-39ea-46d3-9c93-752a3f224f0d", "metadata": {}, "outputs": [], "source": ["date_60_days_ago = pd.to_datetime(\"today\").date() - <PERSON><PERSON><PERSON>(days=60)"]}, {"cell_type": "code", "execution_count": null, "id": "cd570108-951a-4c5e-8402-4f75a98078ad", "metadata": {}, "outputs": [], "source": ["cms_df1 = cms_df1[cms_df1[\"trip_creation_date\"] >= date_60_days_ago]"]}, {"cell_type": "code", "execution_count": null, "id": "f73ece2c-571c-4b0f-bd32-39576968be14", "metadata": {}, "outputs": [], "source": ["cms_df1 = cms_df1[\n", "    [\n", "        \"trip_creation_date\",\n", "        \"consignment_id\",\n", "        \"trip_id\",\n", "        \"wh_node_id\",\n", "        \"wh_outlet_id\",\n", "        \"wh_facility_id\",\n", "        \"wh_outlet_name\",\n", "        \"ds_node_id\",\n", "        \"ds_outlet_id\",\n", "        \"ds_outlet_name\",\n", "        \"destination_city\",\n", "        \"truck_number\",\n", "        \"truck_type\",\n", "        \"billable_truck_type\",\n", "        \"truck_billing_type\",\n", "        \"total_km_travelled\",\n", "        \"total_hr_travelled\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"fixed_cost\",\n", "        \"misc_charges\",\n", "        \"tolls_cost\",\n", "        \"extra_hr_cost\",\n", "        \"extra_km_cost\",\n", "        \"extra_driver_cost_for_trip\",\n", "        \"total_trip_cost\",\n", "        \"status\",\n", "        \"total_con_cost\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4720af03-c6e6-4bcf-93d9-eeaa14d9b5cb", "metadata": {}, "outputs": [], "source": ["cms_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "3e51a7e4-0f40-45a8-9cfa-edd05bcd50eb", "metadata": {}, "outputs": [], "source": ["# cms_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "43dab679-1e79-49da-9c22-b2f54d8a3bfd", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"trip_creation_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Trip creation date\",\n", "        },\n", "        {\"name\": \"consignment_id\", \"type\": \"integer\", \"description\": \"Consignment ID\"},\n", "        {\"name\": \"trip_id\", \"type\": \"varchar\", \"description\": \"Trip Id\"},\n", "        {\"name\": \"wh_node_id\", \"type\": \"integer\", \"description\": \"WH NODE ID\"},\n", "        {\"name\": \"wh_outlet_id\", \"type\": \"integer\", \"description\": \"WH outlet ID\"},\n", "        {\"name\": \"wh_facility_id\", \"type\": \"integer\", \"description\": \"WH facility ID\"},\n", "        {\"name\": \"wh_outlet_name\", \"type\": \"varchar\", \"description\": \"WH Outlet Name\"},\n", "        {\"name\": \"ds_node_id\", \"type\": \"integer\", \"description\": \"DS NODE ID\"},\n", "        {\"name\": \"ds_outlet_id\", \"type\": \"integer\", \"description\": \"DS outlet ID\"},\n", "        {\"name\": \"ds_outlet_name\", \"type\": \"varchar\", \"description\": \"DS outlet name\"},\n", "        {\n", "            \"name\": \"destination_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Destination city\",\n", "        },\n", "        {\"name\": \"truck_number\", \"type\": \"varchar\", \"description\": \"Truck number\"},\n", "        {\"name\": \"truck_type\", \"type\": \"varchar\", \"description\": \"Truck Type\"},\n", "        {\n", "            \"name\": \"billable_truck_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Billable Truck Type\",\n", "        },\n", "        {\n", "            \"name\": \"truck_billing_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Truck Billable Type\",\n", "        },\n", "        {\n", "            \"name\": \"total_km_travelled\",\n", "            \"type\": \"real\",\n", "            \"description\": \"total_km_travelled\",\n", "        },\n", "        {\n", "            \"name\": \"total_hr_travelled\",\n", "            \"type\": \"real\",\n", "            \"description\": \"total_hr_travelled\",\n", "        },\n", "        {\"name\": \"vendor_id\", \"type\": \"integer\", \"description\": \"Vendor ID\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"Vendor name\"},\n", "        {\"name\": \"fixed_cost\", \"type\": \"real\", \"description\": \"Fixed Cost\"},\n", "        {\"name\": \"misc_charges\", \"type\": \"real\", \"description\": \"Fixed Cost\"},\n", "        {\"name\": \"tolls_cost\", \"type\": \"real\", \"description\": \"Tolls Cost\"},\n", "        {\"name\": \"extra_hr_cost\", \"type\": \"real\", \"description\": \"extra_hr_cost\"},\n", "        {\"name\": \"extra_km_cost\", \"type\": \"real\", \"description\": \"extra_km_cost\"},\n", "        {\n", "            \"name\": \"extra_driver_cost_for_trip\",\n", "            \"type\": \"real\",\n", "            \"description\": \"extra_driver_cost_for_trip\",\n", "        },\n", "        {\"name\": \"total_trip_cost\", \"type\": \"real\", \"description\": \"total_trip_cost\"},\n", "        {\"name\": \"status\", \"type\": \"varchar\", \"description\": \"status\"},\n", "        {\"name\": \"total_con_cost\", \"type\": \"real\", \"description\": \"total_con_cost\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"trip_id\",\n", "        \"wh_outlet_id\",\n", "        \"ds_outlet_id\",\n", "    ],  # list\n", "    \"partition_key\": [\"trip_creation_date\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"trip_id\",\n", "    \"load_type\": \"partition_overwrite\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"CMS cost\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(cms_df1, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "2adc2b25-e9ec-450e-b3fc-8981f9e903dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f5840e11-a78e-4382-9415-1c64345fd26b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "afc4e87f-ca7f-4fd5-ba6b-8b788e7f74bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1c83118b-6b6e-4e5e-8764-e53ce1a421d3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b0d4704a-3d70-4bac-b3b2-d5fcb8ae9d62", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3bf2e425-1029-48bd-925c-cbd5d536b53b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}