alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: adherence_metrics
dag_type: etl
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 16G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters:
    end_date: ''
    start_date: ''
owner:
  email: <EMAIL>
  slack_id: U04JZTM4FC7
path: povms/fleet/etl/adherence_metrics
paused: false
project_name: fleet
schedule:
  end_date: '2023-09-07T00:00:00'
  interval: 30 6 * * *
  start_date: '2023-07-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
pool: povms_pool
