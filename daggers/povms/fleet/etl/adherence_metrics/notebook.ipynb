{"cells": [{"cell_type": "code", "execution_count": null, "id": "deaf0f8b-65d3-4e38-a96a-2d5b5d64a541", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "# import matplotlib.pyplot as plt\n", "from datetime import date\n", "from datetime import timedelta\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "911f5324-c475-49a9-9a7f-c41aadf13fb8", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "b12e5e89-277f-436b-9d93-ebf125c86e10", "metadata": {}, "outputs": [], "source": ["look_back_days = 7"]}, {"cell_type": "code", "execution_count": null, "id": "187754f7-0e65-4145-9582-cc156b02a5c1", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "    create temporary table base as\n", "      (SELECT *,\n", "              coalesce(vehicle_type2,vehicle_type1) AS truck_type\n", "       FROM\n", "         (SELECT t.id AS consignment_id,\n", "                 t.state AS current_state,\n", "                 trip.trip_id,\n", "                 coalesce(co1.facility_id,t.source_store_id::int) AS facility_id,\n", "                 n.external_name AS facility_name,\n", "                 m.outlet_id AS ds_outlet_id,\n", "                 co.name AS ds_outlet_name,\n", "                 UPPER(split_part(json_extract_path_text(t.metadata,'truck_number'),'/',1)) AS truck_number,\n", "                 tc.driver_id,\n", "                 tc.driver_name,\n", "                 tc.driver_mobile,\n", "                 split_part(json_extract_path_text(t.metadata,'truck_number'),'/',2) AS vehicle_type1,\n", "                 json_extract_path_text(t.metadata,'vehicle_type') AS vehicle_type2,\n", "                 count(DISTINCT d.external_id) AS num_invoices,\n", "                 count(DISTINCT c.external_id) AS num_containers,\n", "                 trip.truck_entry_wh,\n", "                 trip.truck_handshake,\n", "                 max(CASE\n", "                         WHEN (to_state='LOADING') THEN tl.install_ts\n", "                     END) AS loading_start,\n", "                 max(CASE\n", "                         WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts\n", "                     END) AS ready_for_dispatch,\n", "                 max(CASE\n", "                         WHEN (to_state='ENROUTE') THEN tl.install_ts\n", "                     END) AS enroute,\n", "                 max(CASE\n", "                         WHEN (to_state='REACHED') THEN tl.install_ts\n", "                     END) AS ds_reached,\n", "                 max(CASE\n", "                         WHEN (to_state='UNLOADING') THEN tl.install_ts\n", "                     END) AS unloading_start,\n", "                 max(CASE\n", "                         WHEN (to_state='COMPLETED') THEN tl.install_ts\n", "                     END) AS unloading_completed,\n", "                 trip.truck_return_wh\n", "          FROM lake_transit_server.transit_consignment t\n", "          JOIN lake_transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "          JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "          JOIN lake_transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "          JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "          JOIN lake_retail.console_outlet_logistic_mapping m ON m.logistic_node_id = t.destination_store_id\n", "          AND m.active = TRUE\n", "          AND m.id NOT IN (611,\n", "                           1538)\n", "          JOIN lake_retail.console_outlet co ON co.id = m.outlet_id\n", "          LEFT JOIN\n", "            (SELECT DISTINCT id,\n", "                             facility_id\n", "             FROM lake_retail.console_outlet\n", "             WHERE active = 1\n", "               AND id!= 1638\n", "               AND facility_id!= 806\n", "               AND business_type_id IN (1,\n", "                                        12,\n", "                                        19,\n", "                                        20) ) AS co1 ON co1.id = t.source_store_id\n", "          JOIN\n", "            (SELECT DISTINCT t.id AS consignment_id,\n", "                             tsl.label_value AS trip_id,\n", "                             up.employee_id AS driver_id,\n", "                             u.name AS driver_name,\n", "                             u.phone AS driver_mobile\n", "             FROM lake_transit_server.transit_consignment t\n", "             JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "             JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "             AND tsl.label_type = 'TRIP_ID'\n", "             JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value\n", "             AND tt.shipment_label_type = 'TRIP_ID'\n", "             JOIN lake_transit_server.transit_user_profile up ON up.id = tt.user_profile_id\n", "             JOIN lake_transit_server.transit_user u ON up.user_id = u.id\n", "             WHERE t.install_ts >= cast((CURRENT_DATE - 7) AS TIMESTAMP)) tc ON tc.consignment_id = t.id\n", "          JOIN\n", "            (SELECT tsl.label_value AS trip_id,\n", "                    min(q.entry_ts) AS truck_entry_wh,\n", "                    min(tts.install_ts) AS truck_return_wh,\n", "                    min(t.install_ts) AS truck_handshake\n", "             FROM lake_transit_server.transit_consignment t\n", "             JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "             JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "             AND tsl.label_type = 'TRIP_ID'\n", "             JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value\n", "             AND tt.shipment_label_type = 'TRIP_ID'\n", "             LEFT JOIN lake_transit_server.task_management_taskstatelog tts ON tts.task_id = tt.id\n", "             AND tts.to_state = 'COMPLETED'\n", "             JOIN lake_transit_server.transit_express_allocation_field_executive_queue q ON (q.user_profile_id || '_' || q.id) = tsl.label_value\n", "             WHERE t.install_ts >= cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "             GROUP BY 1) trip ON trip.trip_id = tc.trip_id\n", "          WHERE t.install_ts >= cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "          GROUP BY 1,\n", "                   2,\n", "                   3,\n", "                   4,\n", "                   5,\n", "                   6,\n", "                   7,\n", "                   9,\n", "                   10,\n", "                   11,\n", "                   16,\n", "                   17,\n", "                   24,\n", "                   t.metadata));\n", "create temporary table pos AS\n", "      (SELECT DISTINCT td.consignment_id AS csmt_id,\n", "                       max(s.dispatch_time) AS dispatch_time\n", "       FROM lake_pos.pos_invoice pi\n", "       JOIN lake_transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "       JOIN lake_po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "       JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id\n", "       AND co.business_type_id IN (1,\n", "                                   12,\n", "                                   20)\n", "       WHERE pi.created_at >= cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "         AND invoice_type_id IN (5,\n", "                                 14,\n", "                                 16)\n", "       GROUP BY 1);\n", "create temporary table grn AS\n", "      (SELECT er.external_order_no::int AS c_id,\n", "              min(a.created_date) AS grn_started_at,\n", "              max(a.created_date) AS grn_completed_at\n", "       FROM lake_storeops.er er\n", "       JOIN lake_storeops.activity a ON a.er_id = er.id\n", "       WHERE er.created_date > cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "         AND er.er_type = 'BULKIN_CONSIGNMENT'\n", "         AND er.external_order_no IN\n", "           (SELECT DISTINCT cast(consignment_id AS varchar)\n", "            FROM base)\n", "       GROUP BY 1); \n", "create temporary table grn1 AS\n", "      (SELECT DISTINCT t.id AS c_id,\n", "                      min(il.pos_timestamp) AS grn_started_at,\n", "                      max(il.pos_timestamp) AS grn_completed_at\n", "      FROM lake_transit_server.transit_consignment t\n", "      JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "      JOIN lake_ims.ims_inventory_log il ON il.merchant_invoice_id = d.external_id\n", "      WHERE t.install_ts >= cast((CURRENT_DATE - 7) AS TIMESTAMP)\n", "         AND inventory_update_type_id IN (1,\n", "                                          28,\n", "                                          76,\n", "                                          90,\n", "                                          93)\n", "      GROUP BY 1);\n", "create temporary table trip_state AS\n", "      (SELECT DISTINCT trip_id,\n", "                       state AS trip_state\n", "       FROM lake_transit_server.transit_projection_trip\n", "       WHERE install_ts >= CURRENT_DATE - 30 );\n", "create temporary table excpected_timelines AS\n", "      (WITH base_ AS\n", "         (SELECT DISTINCT trip_id,\n", "                          json_extract_path_text(event_meta, 'consignment_id') AS consignment_id,\n", "                          event_type, (last_value(scheduled_ts) over(PARTITION BY trip_id,consignment_id,event_type\n", "                                                                     ORDER BY update_ts ROWS BETWEEN unbounded preceding AND unbounded following)) + interval '5.5 hrs' AS scheduled_ts, (last_value(expected_ts) over(PARTITION BY trip_id,consignment_id,event_type\n", "                                                                                                                                                                                                                       ORDER BY update_ts ROWS BETWEEN unbounded preceding AND unbounded following)) + interval '5.5 hrs' AS expected_ts\n", "          FROM lake_transit_server.transit_projection_trip_event_timeline AS tp\n", "          WHERE event_type IN ('DS_ARRIVAL',\n", "                               'UNLOADING_END',\n", "                               'COMPLETED')\n", "            AND install_ts >= cast((CURRENT_DATE - 20) AS TIMESTAMP) ),\n", "            wh_return AS\n", "         (SELECT trip_id,\n", "                 max(CASE\n", "                         WHEN event_type = 'COMPLETED' THEN expected_ts\n", "                         ELSE NULL\n", "                     END) AS exp_truck_return_wh,\n", "                 max(CASE\n", "                         WHEN event_type = 'COMPLETED' THEN scheduled_ts\n", "                         ELSE NULL\n", "                     END) AS sch_truck_return_wh\n", "          FROM base_\n", "          GROUP BY 1),\n", "            ds_details AS\n", "         (SELECT trip_id,\n", "                 consignment_id,\n", "                 max(CASE\n", "                         WHEN event_type = 'DS_ARRIVAL' THEN expected_ts\n", "                         ELSE NULL\n", "                     END) AS exp_ds_reach,\n", "                 max(CASE\n", "                         WHEN event_type = 'DS_ARRIVAL' THEN scheduled_ts\n", "                         ELSE NULL\n", "                     END) AS sch_ds_reach,\n", "                 max(CASE\n", "                         WHEN event_type = 'UNLOADING_END' THEN expected_ts\n", "                         ELSE NULL\n", "                     END) AS exp_unloading_complete,\n", "                 max(CASE\n", "                         WHEN event_type = 'UNLOADING_END' THEN scheduled_ts\n", "                         ELSE NULL\n", "                     END) AS sch_unloading_complete\n", "          FROM base_\n", "          WHERE consignment_id!=''\n", "          GROUP BY 1,\n", "                   2) SELECT DISTINCT wr.trip_id,\n", "                                      dd.consignment_id,\n", "                                      exp_ds_reach,\n", "                                      sch_ds_reach,\n", "                                      exp_unloading_complete,\n", "                                      sch_unloading_complete,\n", "                                      exp_truck_return_wh,\n", "                                      sch_truck_return_wh\n", "       FROM wh_return AS wr\n", "       LEFT JOIN ds_details AS dd ON wr.trip_id = dd.trip_id);\n", "       \n", "       \n", "    SELECT base.consignment_id,\n", "           base.trip_id,\n", "           (truck_handshake + interval '5.5 hrs')::date AS consignment_date,\n", "           CASE\n", "               WHEN facility_id = 1743 THEN 264\n", "               WHEN facility_id = 4306 THEN 1983\n", "               ELSE facility_id\n", "           END AS facility_id,\n", "           facility_name,\n", "           ds_outlet_id,\n", "           ds_outlet_name,\n", "           truck_number,\n", "           truck_type,\n", "           driver_id,\n", "           driver_name,\n", "           driver_mobile,\n", "           trip_state,\n", "           current_state AS consignment_state,\n", "           num_invoices,\n", "           num_containers,\n", "           td.total_trip_distance_km,\n", "           (dispatch_time + interval '4.5 hr') AS scheduled_truck_arrival,\n", "           (truck_entry_wh + interval '5.5 hrs') AS truck_entry_wh,\n", "           (truck_handshake + interval '5.5 hrs') AS truck_handshake,\n", "           (loading_start + interval '5.5 hrs') AS loading_start,\n", "           (ready_for_dispatch + interval '5.5 hrs') AS ready_for_dispatch,\n", "           (enroute + interval '5.5 hrs') AS enroute,\n", "           (dispatch_time + interval '5.5 hrs') AS scheduled_dispatch_time,\n", "           (ds_reached + interval '5.5 hrs') AS ds_reached,\n", "           exp_ds_reach,\n", "           sch_ds_reach,\n", "           (unloading_start + interval '5.5 hrs') AS unloading_start,\n", "           (unloading_completed + interval '5.5 hrs') AS unloading_completed,\n", "           exp_unloading_complete,\n", "           sch_unloading_complete,\n", "           (coalesce(grn.grn_started_at,grn1.grn_started_at) + interval '5.5 hrs') AS grn_started_at,\n", "           (coalesce(grn.grn_completed_at,grn1.grn_completed_at) + interval '5.5 hrs') AS grn_completed_at,\n", "           (truck_return_wh + interval '5.5 hrs') AS truck_return_wh,\n", "           exp_truck_return_wh,\n", "           sch_truck_return_wh,\n", "           CASE\n", "               WHEN datediff(MINUTE,scheduled_truck_arrival,truck_entry_wh) <= 0 THEN 0\n", "               ELSE datediff(MINUTE,scheduled_truck_arrival,truck_entry_wh)\n", "           END AS truck_arrival_delay_min,\n", "           datediff(MINUTE,truck_handshake,loading_start) AS handshake_to_loading_min,\n", "           datediff(MINUTE,loading_start,ready_for_dispatch) AS total_scan_min,\n", "           datediff(MINUTE,ready_for_dispatch,enroute) AS driver_delay_min,\n", "           datediff(MINUTE,loading_start,enroute) AS loading_time_min,\n", "           CASE\n", "               WHEN datediff(MINUTE,scheduled_dispatch_time,enroute) <= 0 THEN 0\n", "               ELSE datediff(MINUTE,scheduled_dispatch_time,enroute)\n", "           END AS dispatch_delay_min,\n", "           CASE\n", "               WHEN ds_reached > exp_ds_reach THEN datediff(MINUTE,exp_ds_reach,ds_reached)\n", "               ELSE 0\n", "           END AS ds_reach_delay_from_expected,\n", "           CASE\n", "               WHEN ds_reached > sch_ds_reach THEN datediff(MINUTE,sch_ds_reach,ds_reached)\n", "               ELSE 0\n", "           END AS ds_reach_delay_from_scheduled,\n", "           datediff(MINUTE,unloading_start,unloading_completed) AS unloading_time_min,\n", "           datediff(MINU<PERSON>,truck_entry_wh,truck_return_wh) AS truck_time_at_wh,\n", "           datediff(MINUTE,unloading_completed,truck_return_wh) AS return_transit_truck_time\n", "    FROM base\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    LEFT JOIN grn ON grn.c_id = base.consignment_id\n", "    LEFT JOIN grn1 ON grn1.c_id = base.consignment_id\n", "    LEFT JOIN trip_state AS st ON base.trip_id = st.trip_id\n", "    LEFT JOIN excpected_timelines AS et ON base.trip_id = et.trip_id\n", "    AND base.consignment_id = et.consignment_id\n", "    LEFT JOIN metrics.fleet_bt_app_trip_dist AS td ON base.trip_id = td.trip_id\n", "    ORDER BY 4,\n", "             1 DESC\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8f6b072c-91a4-4797-95bc-8c6a4338de7f", "metadata": {}, "outputs": [], "source": ["bt_raw = read_sql_query(sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "478eb39b-293d-462a-b147-39c56e43934f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1c5548c8-5d46-4d8a-8d1d-007e5d167791", "metadata": {}, "outputs": [], "source": ["# bt_raw = import_btransit_app_data()"]}, {"cell_type": "code", "execution_count": null, "id": "bfe266cf-c6c3-4753-9ae4-2e1eb6421606", "metadata": {}, "outputs": [], "source": ["print(\"ready\")"]}, {"cell_type": "code", "execution_count": null, "id": "e19bd1a3-74c5-44d0-83ef-eafc199d7ce4", "metadata": {}, "outputs": [], "source": ["print(\"ready\")"]}, {"cell_type": "code", "execution_count": null, "id": "0892587d-7a8a-4f16-9790-39d02ea2ced1", "metadata": {}, "outputs": [], "source": ["bt_raw[\"truck_type\"] = bt_raw[\"truck_type\"].str.lower()\n", "bt_raw[\"truck_type\"] = bt_raw[\"truck_type\"].str.strip()\n", "bt_raw[\"truck_number\"] = bt_raw[\"truck_number\"].str.replace(\" \", \"\")\n", "bt_raw[\"truck_number\"] = bt_raw[\"truck_number\"].str.replace(\"-\", \"\")\n", "bt_raw[\"truck_number\"] = bt_raw[\"truck_number\"].str.replace(\"/\", \"\")\n", "bt_raw[\"truck_number\"] = bt_raw[\"truck_number\"].str.replace(\".\", \"\")"]}, {"cell_type": "code", "execution_count": null, "id": "4c8afdac-8b9b-40dc-a004-436cdc4bb842", "metadata": {}, "outputs": [], "source": ["bt = bt_raw[\n", "    [\n", "        \"consignment_date\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"ds_outlet_id\",\n", "        \"ds_outlet_name\",\n", "        \"truck_number\",\n", "    ]\n", "]\n", "bt_data = bt.groupby(\n", "    by=[\n", "        \"consignment_date\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"ds_outlet_id\",\n", "        \"ds_outlet_name\",\n", "        \"truck_number\",\n", "    ]\n", ").sum()\n", "bt_data = bt_data.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "c7ddb72c-15ef-437f-96ea-d31a40142203", "metadata": {}, "outputs": [], "source": ["bt_data = bt_data.rename(\n", "    columns={\n", "        \"consignment_date\": \"date\",\n", "        \"ds_outlet_id\": \"outlet_id\",\n", "        \"ds_outlet_name\": \"outlet_name\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "baf3dc63-9abb-4bb6-8897-ef7c1e880520", "metadata": {}, "outputs": [], "source": ["def import_cost_sheet_raw(look_back_days):\n", "    sql = \"\"\"\n", "        SELECT date(date_) AS trip_date,\n", "               (CASE\n", "                    WHEN (source_name LIKE ('%%M9%%')) THEN ('1983')\n", "                    ELSE source_facility_id\n", "                END) AS source_facility_id,\n", "               source_name AS source_name_csheet,\n", "               category, -- in ('Grocery','FNV','Perishable')\n", "        destination_outlet_id AS ds_outlet_id,\n", "                                                             destination_name AS destination_name_csheet,\n", "                                                             \"vehicle no.\" AS truck_number,\n", "                                                             \"vehicle size\" AS truck_type,\n", "                                                             \"fixed/adhoc\" AS vehicle_type\n", "        FROM metrics.middle_mile_fleet_cost_v2\n", "        WHERE date(date_) between (CURRENT_DATE - 8) AND (CURRENT_DATE - 1)\n", "          AND source_facility_id >0\n", "        GROUP BY 1,\n", "                 2,\n", "                 3,\n", "                 4,\n", "                 5,\n", "                 6,\n", "                 7,\n", "                 8,\n", "                 9\n", "    \n", "    \"\"\".format(\n", "        look_back_days_sql=look_back_days\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "2aeb2dca-79c8-492f-9965-20cf56314b32", "metadata": {}, "outputs": [], "source": ["cost_sheet_raw = import_cost_sheet_raw(look_back_days)"]}, {"cell_type": "code", "execution_count": null, "id": "11afae08-acfd-4e53-a9ec-7041a0d5977a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c56e6fe7-8e9f-45c7-8216-d67798c0508d", "metadata": {}, "outputs": [], "source": ["cost_sheet_raw.loc[\n", "    cost_sheet_raw[\"source_facility_id\"] == \"\", [\"source_facility_id\"]\n", "] = \"0\"\n", "cost_sheet_raw[\"source_facility_id\"] = cost_sheet_raw[\"source_facility_id\"].str.replace(\n", "    \" \", \"0\"\n", ")\n", "cost_sheet_raw[\"ds_outlet_id\"] = cost_sheet_raw[\"ds_outlet_id\"].str.replace(\" \", \"0\")\n", "cost_sheet_raw.loc[cost_sheet_raw[\"ds_outlet_id\"] == \"\", [\"ds_outlet_id\"]] = \"0\"\n", "cost_sheet_raw[\"source_facility_id\"] = cost_sheet_raw[\"source_facility_id\"].astype(\n", "    \"int64\"\n", ")\n", "cost_sheet_raw[\"ds_outlet_id\"] = cost_sheet_raw[\"ds_outlet_id\"].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "f79ff09a-9e1a-4f24-a878-1e53c9db11bd", "metadata": {}, "outputs": [], "source": ["### Truck Type\n", "cost_sheet_raw[\"truck_type\"] = cost_sheet_raw[\"truck_type\"].str.lower()\n", "cost_sheet_raw[\"truck_type\"] = cost_sheet_raw[\"truck_type\"].str.strip()\n", "### Vehicle Number\n", "cost_sheet_raw[\"truck_number\"] = cost_sheet_raw[\"truck_number\"].str.replace(\" \", \"\")\n", "cost_sheet_raw[\"truck_number\"] = cost_sheet_raw[\"truck_number\"].str.replace(\"-\", \"\")\n", "cost_sheet_raw[\"truck_number\"] = cost_sheet_raw[\"truck_number\"].str.replace(\"/\", \"\")\n", "cost_sheet_raw[\"truck_number\"] = cost_sheet_raw[\"truck_number\"].str.replace(\".\", \"\")"]}, {"cell_type": "code", "execution_count": null, "id": "7491b852-2c10-4c72-9ce3-8be38bf947a3", "metadata": {}, "outputs": [], "source": ["cs = cost_sheet_raw[\n", "    [\n", "        \"trip_date\",\n", "        \"source_facility_id\",\n", "        \"source_name_csheet\",\n", "        \"ds_outlet_id\",\n", "        \"destination_name_csheet\",\n", "        \"truck_number\",\n", "    ]\n", "]\n", "cs_data = cs.groupby(\n", "    by=[\n", "        \"trip_date\",\n", "        \"source_facility_id\",\n", "        \"source_name_csheet\",\n", "        \"ds_outlet_id\",\n", "        \"destination_name_csheet\",\n", "        \"truck_number\",\n", "    ]\n", ").sum()\n", "cs_data = cs_data.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "4995cdbd-67a9-4b8b-a913-ff79067fcb1e", "metadata": {}, "outputs": [], "source": ["cs_data\n", "cs_data = cs_data.rename(\n", "    columns={\n", "        \"trip_date\": \"date\",\n", "        \"source_facility_id\": \"facility_id\",\n", "        \"source_name_csheet\": \"facility_name\",\n", "        \"ds_outlet_id\": \"outlet_id\",\n", "        \"destination_name_csheet\": \"outlet_name\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bad4fe10-79aa-4c1d-81de-8b5b28b03970", "metadata": {}, "outputs": [], "source": ["# Btransit data\n", "bt_data.loc[bt_data[\"facility_id\"] == 1235, [\"facility_id\"]] = 1395\n", "# bt_data.loc[bt_data[\"facility_id\"] == 1397, [\"facility_id\"]] = 1395\n", "bt_data.loc[bt_data[\"facility_id\"] == 1424, [\"facility_id\"]] = 1211\n", "bt_data.loc[bt_data[\"facility_id\"] == 1426, [\"facility_id\"]] = 1209"]}, {"cell_type": "code", "execution_count": null, "id": "5e206d0d-a271-4594-89e2-931eef683b0d", "metadata": {}, "outputs": [], "source": ["bt_data[\"sum\"] = 1\n", "bt_cum = (\n", "    bt_data[[\"date\", \"facility_id\", \"sum\"]].groupby(by=[\"date\", \"facility_id\"]).sum()\n", ")\n", "bt_cum = bt_cum.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "d001c3ea-f332-4a87-899e-e1776c62c3da", "metadata": {}, "outputs": [], "source": ["# Entries in transit data but missing from cost Sheet\n", "temp1 = pd.concat(\n", "    [\n", "        bt_data[[\"date\", \"truck_number\", \"facility_id\", \"outlet_id\"]],\n", "        cs_data[[\"date\", \"truck_number\", \"facility_id\", \"outlet_id\"]],\n", "        cs_data[[\"date\", \"truck_number\", \"facility_id\", \"outlet_id\"]],\n", "    ]\n", ").drop_duplicates(keep=False)\n", "\n", "temp1 = temp1.reset_index(drop=True)\n", "temp1[\"check\"] = 1\n", "bt_base = (\n", "    temp1[[\"date\", \"facility_id\", \"check\"]].groupby(by=[\"date\", \"facility_id\"]).sum()\n", ")\n", "bt_base = bt_base.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "1639efe0-c964-402c-99df-5b1d435f6963", "metadata": {}, "outputs": [], "source": ["final_bt = bt_cum.merge(bt_base, on=[\"date\", \"facility_id\"], how=\"left\")\n", "final_bt[\"performance\"] = (final_bt[\"check\"] / final_bt[\"sum\"]) * 100"]}, {"cell_type": "code", "execution_count": null, "id": "9843f4cd-dd7e-46af-acdc-303a27c6261f", "metadata": {}, "outputs": [], "source": ["cs_data[\"sum\"] = 1\n", "cs_cum = (\n", "    cs_data[[\"date\", \"facility_id\", \"sum\"]].groupby(by=[\"date\", \"facility_id\"]).sum()\n", ")\n", "cs_cum = cs_cum.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "349f00ce-f4ba-493e-9897-76874c1deaab", "metadata": {}, "outputs": [], "source": ["# Entries missing from btransit app\n", "temp2 = pd.concat(\n", "    [\n", "        cs_data[[\"date\", \"truck_number\", \"facility_id\", \"outlet_id\"]],\n", "        bt_data[[\"date\", \"truck_number\", \"facility_id\", \"outlet_id\"]],\n", "        bt_data[[\"date\", \"truck_number\", \"facility_id\", \"outlet_id\"]],\n", "    ]\n", ").drop_duplicates(keep=False)\n", "\n", "temp2 = temp2.reset_index(drop=True)\n", "temp2[\"check\"] = 1\n", "cs_base = (\n", "    temp2[[\"date\", \"facility_id\", \"check\"]].groupby(by=[\"date\", \"facility_id\"]).sum()\n", ")\n", "cs_base = cs_base.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "505f90f7-2f38-40aa-8cda-dab938b54b14", "metadata": {}, "outputs": [], "source": ["final_cs = cs_cum.merge(cs_base, on=[\"date\", \"facility_id\"], how=\"left\")\n", "final_cs[\"performance\"] = (final_cs[\"check\"] / final_cs[\"sum\"]) * 100"]}, {"cell_type": "code", "execution_count": null, "id": "c26e3535-002b-4145-9554-5a674931ce5f", "metadata": {}, "outputs": [], "source": ["bt_cum[\"flag\"] = \"bt\"\n", "cs_cum[\"flag\"] = \"cs\""]}, {"cell_type": "code", "execution_count": null, "id": "468aff88-664e-48bc-8024-8bf5ea927c26", "metadata": {}, "outputs": [], "source": ["final = pd.concat(\n", "    [\n", "        bt_cum,\n", "        cs_cum,\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "71a20703-3f86-473b-9a62-1d65ed1f8971", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final, \"1sZQ3yE9Yk_GnNbrKqqjP0q1WWoNfkhVJaKksp0qeuV4\", \"quantitative_metrics\"\n", ")\n", "pb.to_sheets(temp2, \"1sZQ3yE9Yk_GnNbrKqqjP0q1WWoNfkhVJaKksp0qeuV4\", \"cs_raw\")\n", "pb.to_sheets(final_cs, \"1sZQ3yE9Yk_GnNbrKqqjP0q1WWoNfkhVJaKksp0qeuV4\", \"correct_cs_raw\")\n", "pb.to_sheets(temp1, \"1sZQ3yE9Yk_GnNbrKqqjP0q1WWoNfkhVJaKksp0qeuV4\", \"bt_raw\")\n", "pb.to_sheets(final_bt, \"1sZQ3yE9Yk_GnNbrKqqjP0q1WWoNfkhVJaKksp0qeuV4\", \"correct_bt_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "b53b229c-58c7-4369-b4d7-bce80f40ed3e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}