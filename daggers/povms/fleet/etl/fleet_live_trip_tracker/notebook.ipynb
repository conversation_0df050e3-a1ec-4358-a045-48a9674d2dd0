{"cells": [{"cell_type": "code", "execution_count": null, "id": "5e7b483f-b8e1-49eb-aaaa-b198d8867e54", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e256a0c9-ed4d-4d5a-bfa2-b7fe9e9ab7a1", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "0a6f91f9-56ea-476d-a1af-6ed7a10c68a0", "metadata": {}, "outputs": [], "source": ["live_trip_query = f\"\"\"\n", "\n", "with cte as(\n", "SELECT     \n", "           pc.trip_id,\n", "           pt.state AS trip_state,\n", "           cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "           cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "           cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "           source_id,\n", "           destination_id,\n", "           truck_meta\n", "           \n", "    FROM  transit_server.transit_projection_consignment pc \n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    \n", "    WHERE \n", "       pc.insert_ds_ist >= cast((CURRENT_DATE - interval '4' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '4' DAY) as varchar)\n", "     --GROUP BY 1,2,3,4,5,6,7,8\n", "),\n", "pos1 as \n", "    (\n", "    select  trip_id,\n", "            --activity_type,\n", "            dispatch_time\n", "    from transit_server.transit_projection_plan_vehicle \n", "    where insert_ds_ist >= cast((current_date - interval '12' day) as varchar)\n", "      and activity_type='LOADING'\n", ")\n", "\n", ",trip_base AS\n", "    (\n", "    SELECT  trip_id,\n", "            max(sch_wh_arrival + interval '330' minute) as sch_wh_arrival,\n", "            max(truck_entry_wh + interval '330' minute) AS truck_entry_wh,\n", "            max(loading_start_wh + interval '330' minute) AS loading_start_wh,\n", "            max(loading_end_wh + interval '330' minute) AS loading_end_wh,\n", "            max(enroute_wh + interval '330' minute) AS enroute_wh,\n", "            max(ds_reach + interval '330' minute) AS ds_reach,\n", "            max(unload_start_ds + interval '330' minute) AS unloading_start_ds,\n", "            max(unload_end_ds + interval '330' minute) AS unloading_end_ds,\n", "            max(loading_start_ds + interval '330' minute) AS loading_start_ds,\n", "            max(loading_end_ds + interval '330' minute) AS loading_end_ds,\n", "            max(truck_return_wh + interval '330' minute) AS truck_return_wh,\n", "            max(unload_start_wh + interval '330' minute) AS unloading_start_wh,\n", "            max(unload_end_wh + interval '330' minute) as unloading_end_wh,\n", "            min(coalesce(trip_comp_1 + interval '330' minute,trip_comp_2 + interval '330' minute)) as completed_ts\n", "            \n", "            \n", "    FROM\n", "        (\n", "        SELECT    trip_id,\n", "                  \n", "                  max(CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS truck_entry_wh,\n", "                  max(CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN scheduled_ts\n", "                      ELSE NULL\n", "                  END) as sch_wh_arrival,\n", "                  max(CASE\n", "                      WHEN event_type = 'LOADING_START' AND cast(json_extract(event_meta,'$.node_type') as varchar) = 'FC' \n", "                      AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS loading_start_wh,\n", "                  max(CASE\n", "                      WHEN event_type = 'LOADING_END' AND cast(json_extract(event_meta,'$.node_type') as varchar) = 'FC' \n", "                      AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS loading_end_wh,\n", "                  max(CASE\n", "                      WHEN event_type = 'WH_DISPATCH' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS enroute_wh,\n", "                  max(CASE\n", "                      WHEN event_type = 'DS_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS ds_reach,\n", "                  max(CASE\n", "                      WHEN event_type = 'UNLOADING_START' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS unload_start_ds,\n", "                  max(CASE\n", "                      WHEN event_type = 'UNLOADING_END' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS unload_end_ds,\n", "                  max(CASE\n", "                      WHEN event_type = 'LOADING_START' AND cast(json_extract(event_meta,'$.node_type') as varchar) = 'DARK_STORE' \n", "                      AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS loading_start_ds,\n", "                  max(CASE\n", "                      WHEN event_type = 'LOADING_END' AND cast(json_extract(event_meta,'$.node_type') as varchar) = 'DARK_STORE' \n", "                      AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS loading_end_ds,\n", "                  \n", "                   max(CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS truck_return_wh,\n", "                  \n", "                  max(CASE\n", "                      WHEN event_type = 'UNLOADING_START' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS unload_start_wh,\n", "                  max(CASE\n", "                      WHEN event_type = 'UNLOADING_END' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS unload_end_wh,\n", "                  \n", "                  max(CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS trip_comp_1,\n", "                  max(CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END) AS trip_comp_2\n", "                  \n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE insert_ds_ist >= cast((CURRENT_DATE - interval '2' DAY) as varchar)\n", "        and lake_active_record  \n", "        --and trip_id = '17057_1037488'\n", "        group by 1\n", "        )\n", "    GROUP BY 1\n", "    ),\n", "final as(\n", "select tb.*,\n", "        --cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS \n", "        driver_name,\n", "        --   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS \n", "        driver_mobile,\n", "        --   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS \n", "        driver_id,\n", "          cast(json_extract(truck_meta,'$.truck_number') as varchar) as truck_number,\n", "          cast(json_extract(truck_meta,'$.vehicle_type') as varchar) as vehicle_type,\n", "           source_id,\n", "           destination_id,\n", "           CASE\n", "           WHEN coalesce(co1.facility_id,cast(source_id as int)) = 1743 THEN 264\n", "           WHEN coalesce(co1.facility_id,cast(source_id as int)) = 4306 THEN 1983\n", "           ELSE coalesce(co1.facility_id,cast(source_id as int))\n", "           END AS wh_facility_id,\n", "           --n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           trip_state,\n", "           cast(loading_end_wh as timestamp) as ready_for_dispatch,\n", "           (pos1.dispatch_time + interval '330' MINUTE) as scheduled_dispatch_time,\n", "           row_number() over(partition by tb.trip_id order by m.outlet_id desc) as rank_\n", "from trip_base tb\n", "left join cte on cte.trip_id=tb.trip_id\n", "left join pos1 on pos1.trip_id=tb.trip_id\n", "JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = cte.destination_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12,19,20)) AS co1 ON cast(co1.id as varchar) = cte.source_id\n", "\n", ")\n", "select final.*, \n", "        cast(co.id as int) as wh_outlet_id,\n", "       co.name as wh_outlet_name\n", "       \n", "from final \n", "join retail.console_outlet co on co.facility_id = final.wh_facility_id and business_type_id in (1,12,19,20) and name not like '%%HOT%%' and name <> 'K3 T K4'\n", "where rank_=1\n", "and CAST(scheduled_dispatch_time AS date) >= current_date - INTERVAL '2' DAY\n", "\"\"\"\n", "live_df = read_sql_query(live_trip_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "2135a081-ffec-4682-95a9-f949d74985df", "metadata": {}, "outputs": [], "source": ["live_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1b73a5bd-3344-4e62-aca0-5992a69d9f30", "metadata": {}, "outputs": [], "source": ["live_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b226d206-951a-44dd-b9c9-0b68759c1ad3", "metadata": {}, "outputs": [], "source": ["# live_df[live_df['trip_id']=='16191_1067976']"]}, {"cell_type": "code", "execution_count": null, "id": "29803b6f-396c-4065-b003-bc9a6d627afc", "metadata": {}, "outputs": [], "source": ["# live_df[live_df['trip_id']=='20950_1069936']"]}, {"cell_type": "code", "execution_count": null, "id": "f3d66c08-533f-4be1-a8dd-de620c80a5ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af4252f4-4ade-4055-83f6-84987c64ab32", "metadata": {}, "outputs": [], "source": ["q1 = live_df"]}, {"cell_type": "code", "execution_count": null, "id": "f5c4f0e7-46ed-4df7-a44a-5a28fc2fae18", "metadata": {}, "outputs": [], "source": ["q1[\"loading_start_ds\"] = pd.to_datetime(q1[\"loading_start_ds\"])\n", "q1[\"loading_end_ds\"] = pd.to_datetime(q1[\"loading_end_ds\"])\n", "q1[\"enroute_wh\"] = pd.to_datetime(q1[\"enroute_wh\"])\n", "q1[\"unloading_start_wh\"] = pd.to_datetime(q1[\"unloading_start_wh\"])\n", "q1[\"unloading_end_wh\"] = pd.to_datetime(q1[\"unloading_end_wh\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5783998f-2d43-4b3a-8ada-5f98b96a7774", "metadata": {}, "outputs": [], "source": ["q1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "06398e9c-0013-454f-b93f-6cf20cf113e8", "metadata": {}, "outputs": [], "source": ["trips_df = q1"]}, {"cell_type": "code", "execution_count": null, "id": "4dec4938-12ea-4a7e-83bb-a52758a23b1c", "metadata": {}, "outputs": [], "source": ["trips_df.trip_state.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "0589dab9-6ca2-48ce-8b91-e760a8f95ea6", "metadata": {}, "outputs": [], "source": ["q1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e8400f44-d6d1-47bf-89f4-ef510a21e59d", "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"\n", "select  wh_outlet_id,\n", "            ds_outlet_id,\n", "            truck_type as vehicle_type,\n", "            approx_percentile(enroute_frwd_time_taken, 0.5) as med_frwd_enroute_time_taken,\n", "            approx_percentile(enroute_rev_time_taken, 0.5) as med_rev_enroute_time_taken\n", "    from\n", "        (\n", "        select *,\n", "                date_diff('minute',cast(ready_for_dispatch as timestamp) , cast(ds_reached as timestamp)) as enroute_frwd_time_taken,\n", "                date_diff('minute',cast(coalesce(loading_end_ds,unloading_end_ds) as timestamp),cast(truck_return_wh as timestamp)) as enroute_rev_time_taken\n", "        from supply_etls.fleet_trips\n", "        where trip_date between cast(current_date - interval '60' day as varchar) and cast(current_date - interval '1' day as varchar)\n", "        --and wh_outlet_id in (1644,1104,4330,714)\n", "        )\n", "    group by 1,2,3\n", "\"\"\"\n", "query_df = read_sql_query(query2, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "443865ca-48c4-4c54-bac8-d9508d13d346", "metadata": {}, "outputs": [], "source": ["trips_df1 = q1.merge(query_df, how=\"left\", on=[\"wh_outlet_id\", \"ds_outlet_id\", \"vehicle_type\"])"]}, {"cell_type": "code", "execution_count": null, "id": "e2a700ab-a059-4b3f-9e00-18f94a4269c8", "metadata": {}, "outputs": [], "source": ["trips_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b1397e94-53eb-4bd9-9350-2ff0124b9969", "metadata": {}, "outputs": [], "source": ["final1 = sqldf(\n", "    \"\"\"\n", "with base as\n", "    (\n", "    select  *,\n", "            --lead(truck_entry_wh,1) over (partition by truck_number order by scheduled_truck_arrival) as next_trip_entry,\n", "            --lead(truck_handshake,1) over (partition by truck_number order by scheduled_truck_arrival) as next_trip_handshake\n", "            row_number() over (partition by trip_id) as num\n", "    from trips_df1\n", "    )\n", "\n", "select  *,\n", "        case when max_nums = 1 then 'Non-Milk Run' else 'Milk Run' end as trip_type\n", "from \n", "    (\n", "    select  *,\n", "\n", "            -- case when completed_ts is null then (strftime('%s', truck_return_wh) - strftime('%s', truck_handshake)) / 60\n", "            --      else (strftime('%s', completed_ts) - strftime('%s', truck_handshake)) / 60\n", "            -- end as total_trip_time,\n", "\n", "\n", "            (strftime('%s', ready_for_dispatch) - strftime('%s', loading_start_wh)) / 60 as loading_wh,\n", "            (strftime('%s', loading_start_wh) - strftime('%s', truck_entry_wh)) / 60 as loading_wh_idle,\n", "\n", "            (strftime('%s', ds_reach) - strftime('%s', ready_for_dispatch)) / 60 as enroute_ds_time,\n", "            -- (strftime('%s', exp_ds_arrival) - strftime('%s', ds_reach)) / 60 as enroute_ds_time_idle,\n", "\n", "            (strftime('%s', unloading_end_ds) - strftime('%s', unloading_start_ds)) / 60 as unloading_ds,\n", "            (strftime('%s', unloading_start_ds) - strftime('%s', ds_reach)) / 60 as unloading_ds_idle,\n", "\n", "            (strftime('%s', loading_end_ds) - strftime('%s', loading_start_ds)) / 60 as loading_ds,\n", "            (strftime('%s', loading_start_ds) - strftime('%s', unloading_end_ds)) / 60 as loading_ds_idle,\n", "\n", "            (strftime('%s', truck_return_wh) - strftime('%s', loading_end_ds)) / 60 as enroute_wh_time,\n", "\n", "            (strftime('%s', unloading_end_wh) - strftime('%s', unloading_start_wh)) / 60 as unloading_wh,\n", "            (strftime('%s', unloading_start_wh) - strftime('%s', truck_return_wh)) / 60 as unloading_wh_idle,\n", "\n", "            --(strftime('%s', next_trip_handshake) - strftime('%s', unloading_completed_wh)) / 60 as wait_time_bw_trips,\n", "\n", "            max(num) over (partition by trip_id) as max_nums\n", "\n", "    from base\n", "    )\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "114fbc71-e80e-4140-8967-8a2c8a637b87", "metadata": {}, "outputs": [], "source": ["final1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "303b2c4b-b767-4f15-84c5-5dd84f8a8a44", "metadata": {}, "outputs": [], "source": ["final1.drop(columns=[\"num\", \"max_nums\"], inplace=True)\n", "final1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "82d84a86-abfb-41f6-9b6c-7d8608052353", "metadata": {}, "outputs": [], "source": ["# final1=trips_df"]}, {"cell_type": "code", "execution_count": null, "id": "7fca0ae5-042c-4b9b-b8da-e08b6aa9cfad", "metadata": {}, "outputs": [], "source": ["final1[\"truck_return_wh\"] = pd.to_datetime(final1[\"truck_return_wh\"])\n", "final1[\"loading_end_ds\"] = pd.to_datetime(final1[\"loading_end_ds\"])\n", "\n", "# Subtract\n", "final1[\"enroute_wh_time\"] = final1[\"truck_return_wh\"] - final1[\"loading_end_ds\"]"]}, {"cell_type": "code", "execution_count": null, "id": "7f3a5ad4-d044-4578-9f76-865889f886ef", "metadata": {}, "outputs": [], "source": ["final1[\"enroute_wh_time\"] = final1[\"enroute_wh_time\"].dt.total_seconds() / 60"]}, {"cell_type": "markdown", "id": "04b1cfb0-f274-4f3a-aed9-d2d8a9609a18", "metadata": {}, "source": ["### Median Values of time taken at each leg of Trip in last 15 days for each Truck Type"]}, {"cell_type": "code", "execution_count": null, "id": "b3676b4d-9f85-48be-bbb6-71e62c3db2bd", "metadata": {}, "outputs": [], "source": ["med_calculation = (\n", "    final1.groupby([\"wh_outlet_id\", \"ds_outlet_id\", \"vehicle_type\"], as_index=False)\n", "    .agg(\n", "        {\n", "            \"enroute_ds_time\": \"median\",\n", "            \"enroute_wh_time\": \"median\",\n", "        }\n", "    )\n", "    .rename(\n", "        columns={\"enroute_wh_time\": \"enroute_wh_time_med\", \"enroute_ds_time\": \"enroute_ds_time_med\"}\n", "    )\n", ")\n", "\n", "med_calculation"]}, {"cell_type": "code", "execution_count": null, "id": "6d23d493-8dd2-4192-b1c1-57f8c90ea6ed", "metadata": {}, "outputs": [], "source": ["dataframe = final1.merge(\n", "    med_calculation, how=\"left\", on=[\"wh_outlet_id\", \"ds_outlet_id\", \"vehicle_type\"]\n", ")\n", "\n", "dataframe.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3299884c-7669-46bb-806d-adcba0bb0ef1", "metadata": {}, "outputs": [], "source": ["# dataframe.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ad5391ea-e0e8-4400-a7cd-513823e684d4", "metadata": {}, "outputs": [], "source": ["transit_med = sqldf(\n", "    \"\"\"\n", "with cte as(\n", "select *,row_number() over (partition by truck_number order by loading_end_wh desc) as rank__\n", "from dataframe\n", ")\n", "select * from\n", "cte where rank__=1\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "17e3ec6f-f1b5-449b-9a7c-e0089e875bd2", "metadata": {}, "outputs": [], "source": ["transit_med.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4139da02-4ffe-48f3-8cdf-45e992a395f9", "metadata": {}, "outputs": [], "source": ["transit_med.drop(columns={\"rank__\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "75d07057-d676-4c04-8ff0-a2fcbe828b3c", "metadata": {}, "outputs": [], "source": ["transit_med.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "11b59860-c5ce-4460-83a6-5874ec52d4b2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6d5d8cb5-91c5-474e-8e3a-8ac92a22461d", "metadata": {"tags": []}, "outputs": [], "source": ["# final_data['trip_state'].value_counts()\n", "ended_trips = [\"COMPLETED\", \"CANCELLED\", \"EXPIRED\"]\n", "\n", "# Filter Non-Milk Run Trips, Ongoing trips, and trips that has started since yesterday\n", "test_model1 = transit_med[\n", "    # (transit_med[\"trip_type\"] == \"Non-Milk Run\")\n", "    # &\n", "    (~transit_med[\"trip_state\"].isin(ended_trips))\n", "    & (transit_med[\"truck_return_wh\"].isna() == True)\n", "    # & (transit_med[\"rnk\"]==1)\n", "]\n", "\n", "## Remove all trips from examination in which a truck is going for the 1st time from that FC to that DS\n", "test_model1 = test_model1[test_model1[\"enroute_wh_time_med\"].isna() == False]"]}, {"cell_type": "code", "execution_count": null, "id": "5886aa8a-1453-4557-bf5f-9181512db829", "metadata": {"tags": []}, "outputs": [], "source": ["# test_model1=trips_df"]}, {"cell_type": "code", "execution_count": null, "id": "37ce112a-a923-498b-b2ed-2e972b016ba5", "metadata": {}, "outputs": [], "source": ["test_model1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9631bce3-3911-4b3c-94aa-ea533551bcda", "metadata": {}, "outputs": [], "source": ["test_model1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f9b532e0-08db-414e-99b9-d6387831846d", "metadata": {}, "outputs": [], "source": ["test_model2 = sqldf(\n", "    \"\"\"\n", "SELECT * \n", "FROM transit_med\n", "WHERE trip_id NOT IN (SELECT DISTINCT trip_id FROM test_model1)\n", "AND trip_state = 'WH_ARRIVAL'\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d11e364e-6511-41b1-8e18-5a06fd5f7b28", "metadata": {}, "outputs": [], "source": ["test_model2.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "40353f38-0fc2-4aa6-8d9b-9f167b376962", "metadata": {}, "outputs": [], "source": ["test_model1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8392ccbf-d11a-4f56-a13a-1e191c1a31d0", "metadata": {}, "outputs": [], "source": ["test_model2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b270f242-435b-4eec-abc2-fd8cdcb828ea", "metadata": {}, "outputs": [], "source": ["test_model2 = test_model2[test_model2[\"enroute_wh_time_med\"].isna() == False]"]}, {"cell_type": "code", "execution_count": null, "id": "9f4a8478-57d9-45aa-9d4a-a7a180c3a5d8", "metadata": {}, "outputs": [], "source": ["test_model2 = test_model2[test_model2[\"enroute_ds_time_med\"].isna() == False]"]}, {"cell_type": "code", "execution_count": null, "id": "3da8b9e2-a4c5-452e-851e-b4b06c56f53c", "metadata": {"tags": []}, "outputs": [], "source": ["live_trips = test_model1[\"trip_id\"].to_list()\n", "len(live_trips)"]}, {"cell_type": "code", "execution_count": null, "id": "d6a77467-a429-44c4-b7b0-2c2a69cfe8ae", "metadata": {"tags": []}, "outputs": [], "source": ["non_live_trips = transit_med[~(transit_med[\"trip_id\"].isin(live_trips))]\n", "non_live_trips.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7485de0c-223d-48d7-99b0-e811229f60d5", "metadata": {"tags": []}, "outputs": [], "source": ["# Ideal and Median times for Fleet time legs\n", "\n", "ideal_entry_to_load_start = <PERSON><PERSON><PERSON>(minutes=15)\n", "# median_entry_to_load_start = timedelta(minutes=loading_wh_idle_med)\n", "\n", "ideal_load_start_to_dispatch_ready = timedelta(minutes=90)  # dispatch_ready+enroute = 60+30 = 90\n", "# median_load_start_to_dispatch_ready = timedelta(minutes=loading_wh_med)\n", "\n", "# median_dispatch_to_ds_reach = timedelta(minutes=enroute_ds_time_med)\n", "\n", "ideal_ds_reach_to_unload_start = <PERSON><PERSON><PERSON>(minutes=15)\n", "# median_ds_reach_to_unload_start = timedelta(minutes=unloading_ds_idle_med)\n", "\n", "ideal_ds_unload_start_to_end = <PERSON><PERSON>ta(minutes=60)\n", "# median_ds_unload_start_to_end = timedelta(minutes=unloading_ds_med)\n", "\n", "ideal_loadstart_to_loadend_ds = timedelta(minutes=30)\n", "# median_loadstart_to_loadend_ds = timedelta(minutes=loading_ds_med)\n", "\n", "# median_ds_to_wh_reach = timedelta(minutes=enroute_wh_time_med)\n", "\n", "ideal_wh_reach_to_unload_start = <PERSON><PERSON><PERSON>(minutes=15)\n", "# median_ws_reach_to_unload_start = timedelta(minutes=unloading_wh_idle_med)\n", "\n", "ideal_wh_unload_start_to_end = <PERSON><PERSON><PERSON>(minutes=60)\n", "# median_wh_unload_start_to_end = timedelta(minutes=unloading_wh_med)"]}, {"cell_type": "code", "execution_count": null, "id": "63319a37-ae70-4c93-b0df-9ab918d65c94", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "57a358c3-af3f-4dde-9c6a-98f1e6c2fc4e", "metadata": {}, "outputs": [], "source": ["test_model1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "680580ad-ce9b-4c02-bf63-9f156e3acfcd", "metadata": {"tags": []}, "outputs": [], "source": ["def ideal_wh_arrival(t):\n", "    if test_model[\"loading_end_ds\"].isna().values[0] == True:\n", "        if test_model[\"unloading_end_ds\"].isna().values[0] == True:\n", "            if test_model[\"unloading_start_ds\"].isna().values[0] == True:\n", "                if test_model[\"ds_reach\"].isna().values[0] == True:\n", "                    if test_model[\"ready_for_dispatch\"].isna().values[0] == True:\n", "                        if test_model[\"loading_start_wh\"].isna().values[0] == True:\n", "                            x = (\n", "                                pd.to_datetime(test_model[\"truck_entry_wh\"])\n", "                                + ideal_entry_to_load_start\n", "                                + ideal_load_start_to_dispatch_ready\n", "                                + timedelta(minutes=test_model[\"enroute_ds_time_med\"].values[0])\n", "                                + ideal_ds_reach_to_unload_start\n", "                                + ideal_ds_unload_start_to_end\n", "                                + ideal_loadstart_to_loadend_ds\n", "                                + timedelta(minutes=test_model[\"enroute_wh_time_med\"].values[0])\n", "                            )\n", "                        else:\n", "                            x = (\n", "                                pd.to_datetime(test_model[\"loading_start_wh\"])\n", "                                + ideal_load_start_to_dispatch_ready\n", "                                + timedelta(minutes=test_model[\"enroute_ds_time_med\"].values[0])\n", "                                + ideal_ds_reach_to_unload_start\n", "                                + ideal_ds_unload_start_to_end\n", "                                + ideal_loadstart_to_loadend_ds\n", "                                + timedelta(minutes=test_model[\"enroute_wh_time_med\"].values[0])\n", "                            )\n", "                    else:\n", "                        x = (\n", "                            pd.to_datetime(test_model[\"ready_for_dispatch\"])\n", "                            + timedelta(minutes=test_model[\"enroute_ds_time_med\"].values[0])\n", "                            + ideal_ds_reach_to_unload_start\n", "                            + ideal_ds_unload_start_to_end\n", "                            + ideal_loadstart_to_loadend_ds\n", "                            + timedelta(minutes=test_model[\"enroute_wh_time_med\"].values[0])\n", "                        )\n", "                else:\n", "                    x = (\n", "                        pd.to_datetime(test_model[\"ds_reach\"])\n", "                        + ideal_ds_reach_to_unload_start\n", "                        + ideal_ds_unload_start_to_end\n", "                        + ideal_loadstart_to_loadend_ds\n", "                        + timedelta(minutes=test_model[\"enroute_wh_time_med\"].values[0])\n", "                    )\n", "            else:\n", "                x = (\n", "                    pd.to_datetime(test_model[\"unloading_start_ds\"])\n", "                    + ideal_ds_unload_start_to_end\n", "                    + ideal_loadstart_to_loadend_ds\n", "                    + timedelta(minutes=test_model[\"enroute_wh_time_med\"].values[0])\n", "                )\n", "        else:\n", "            x = (\n", "                pd.to_datetime(test_model[\"unloading_end_ds\"])\n", "                + ideal_loadstart_to_loadend_ds\n", "                + timedelta(minutes=test_model[\"enroute_wh_time_med\"].values[0])\n", "            )\n", "    else:\n", "        x = pd.to_datetime(test_model[\"loading_end_ds\"]) + timed<PERSON>ta(\n", "            minutes=test_model[\"enroute_wh_time_med\"].values[0]\n", "        )\n", "    # print(x)\n", "    return x"]}, {"cell_type": "code", "execution_count": null, "id": "a855f0c5-bc13-400b-9901-34900b010cc9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7cffae1f-947c-4f51-97da-4b8729dc2785", "metadata": {"tags": []}, "outputs": [], "source": ["test_model1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a88a4c43-53c9-4260-b472-6044221cb10c", "metadata": {}, "outputs": [], "source": ["test_model1.truck_number.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "e4e0d066-e760-4f52-99a1-734feba0bac8", "metadata": {}, "outputs": [], "source": ["# test_model1[test_model1['truck_number']=='DL1LAG1235']"]}, {"cell_type": "code", "execution_count": null, "id": "53244243-6c35-4975-b269-e0295b8f69c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "08edd1b2-9cef-425e-a070-450501186020", "metadata": {}, "outputs": [], "source": ["duplicates = test_model1[test_model1.duplicated(\"truck_number\", keep=False)]"]}, {"cell_type": "code", "execution_count": null, "id": "c5c32fb8-d8c1-4a03-ae22-0ce20e60e350", "metadata": {}, "outputs": [], "source": ["duplicates"]}, {"cell_type": "code", "execution_count": null, "id": "8fb3978c-493c-49d3-bb03-9879498875f0", "metadata": {}, "outputs": [], "source": ["test_model1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fc7013ac-5114-45bd-a805-afa1e373b0c2", "metadata": {}, "outputs": [], "source": ["test_model1 = test_model1[test_model1[\"enroute_ds_time_med\"].isna() == False]"]}, {"cell_type": "code", "execution_count": null, "id": "eddcb29d-a1a4-469d-8cd5-38528695cf83", "metadata": {}, "outputs": [], "source": ["final_dataframe = []\n", "for i in range(len(test_model1)):\n", "    test_model = test_model1[test_model1[\"truck_number\"] == test_model1[\"truck_number\"].values[i]]\n", "    test_model[\"ideal_wh_arrival\"] = test_model[\"trip_id\"].apply(lambda x: ideal_wh_arrival(x))\n", "    # test_model[\"median_wh_arrival\"] = test_model[\"trip_id\"].apply(\n", "    #     lambda x: med_wh_arrival(x)\n", "    # )\n", "\n", "    final_dataframe.append(test_model)"]}, {"cell_type": "code", "execution_count": null, "id": "313ecd82-55e5-4492-8789-458c9c1bc5fe", "metadata": {"tags": []}, "outputs": [], "source": ["result_df = pd.concat(final_dataframe, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "844f622e-22a2-49dc-8982-efabc62833f4", "metadata": {}, "outputs": [], "source": ["result_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d90f5483-43de-4b0d-a57f-eb77c535ca15", "metadata": {}, "outputs": [], "source": ["result_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8373f5de-404e-440c-8f5d-0e2b87268512", "metadata": {}, "outputs": [], "source": ["test_model2[\"ideal_wh_arrival\"] = pd.to_datetime(test_model2[\"truck_return_wh\"])"]}, {"cell_type": "code", "execution_count": null, "id": "72685062-6950-4718-b74c-23983473d44c", "metadata": {}, "outputs": [], "source": ["test_model2.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9536875c-fd08-43b0-a5ab-cf5d89b37232", "metadata": {}, "outputs": [], "source": ["result_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "a5eb48b9-e66a-4f18-81a4-e833743dbbe8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a8b48545-1e28-48bd-a98e-a8a8859638e8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b924fff8-323d-49da-8afb-8641de7066b6", "metadata": {}, "outputs": [], "source": ["for column in result_df.columns:\n", "    test_model2[column] = test_model2[column].astype(result_df[column].dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "8db4bae2-3d52-42a9-841b-d875298364b9", "metadata": {}, "outputs": [], "source": ["test_model2.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "dea401a4-5f9e-4f51-a78c-d2e9815de2cb", "metadata": {}, "outputs": [], "source": ["result_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "ee7b8735-233d-4f63-97be-3850c2af1fa3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7584dd21-1cb2-42ba-89bb-f3717b032521", "metadata": {}, "outputs": [], "source": ["same_dtypes = result_df.dtypes.equals(test_model2.dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "8b4bc346-f3a2-46a7-9ae4-896bc9bc4642", "metadata": {}, "outputs": [], "source": ["same_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e1aac083-62d7-4b30-9af9-f1ba069808d1", "metadata": {}, "outputs": [], "source": ["result_df[\"enroute_ds_time_med\"] = result_df[\"enroute_ds_time_med\"].astype(float)\n", "result_df[\"enroute_wh_time_med\"] = result_df[\"enroute_wh_time_med\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "5895ba40-079b-4285-8b49-1e3bfa0bff99", "metadata": {"tags": []}, "outputs": [], "source": ["result_df1 = result_df.append(test_model2)"]}, {"cell_type": "code", "execution_count": null, "id": "5207a7c8-722a-4f4b-81b7-0f85eb11bfd4", "metadata": {}, "outputs": [], "source": ["result_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1fc72a56-6c5a-43f2-a5e1-9543006e880b", "metadata": {}, "outputs": [], "source": ["# result_df1.sch_wh_arrival.min()"]}, {"cell_type": "code", "execution_count": null, "id": "b9d8c5f1-558c-491c-835c-4fb5c0ae990a", "metadata": {}, "outputs": [], "source": ["result_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "31e23d16-5640-49da-9014-aabc4c0ebfd2", "metadata": {}, "outputs": [], "source": ["# result_df1[result_df1['trip_id']=='12636_973339']"]}, {"cell_type": "code", "execution_count": null, "id": "d63c577c-e363-4031-9d40-d2b13f38fb12", "metadata": {}, "outputs": [], "source": ["# result_df1[result_df1['truck_number']=='DL1LAN0121']"]}, {"cell_type": "code", "execution_count": null, "id": "6392f789-19d1-439f-8d7d-0897663b57a8", "metadata": {}, "outputs": [], "source": ["result_df1[\"enroute_ds_time_med\"] = result_df1[\"enroute_ds_time_med\"].fillna(value=0).astype(int)\n", "result_df1[\"enroute_wh_time_med\"] = result_df1[\"enroute_wh_time_med\"].fillna(value=0).astype(int)"]}, {"cell_type": "markdown", "id": "d7814c01-8b05-43ba-9cf6-fe7f79ed6e77", "metadata": {}, "source": ["#### Fleet Plan"]}, {"cell_type": "code", "execution_count": null, "id": "e6ed5632-3060-4b48-9382-c0e46aab261d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cc7c8ba5-44c1-44c3-a1a4-8460a3e46b85", "metadata": {}, "outputs": [], "source": ["fleet_plan_query = \"\"\"\n", "with fleet_plan_base as\n", "    (\n", "    select  source_node_id,\n", "            destination_node_id,\n", "            lm1.outlet_id as source_outlet_id,\n", "            -- lm1.name as source_outlet_name,\n", "            co.name as source_outlet_name,\n", "            lm2.outlet_id as  destination_outlet_id,\n", "            lm2.name as destination_outlet_name,\n", "            (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "            (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "            vehicle_number,\n", "            billing_type,\n", "            handling_type,\n", "            cast(from_unixtime(slot_time/pow(10,6), 'UTC') as time) as dispatch_slot,\n", "            cast(from_unixtime(destination_arrival_time/pow(10,6), 'UTC') as time) as destination_arrival_time\n", "            \n", "    from transit_server.transit_fleet_plan_slot_assigned_vehicle sav\n", "    left join transit_server.transit_fleet_plan_dispatch_slot ds on ds.id = sav.dispatch_slot_id\n", "    left join transit_server.transit_fleet_plan fp on fp.id = ds.fleet_plan_id\n", "    left join retail.console_outlet_logistic_mapping lm1 on lm1.logistic_node_id = cast(fp.source_node_id as int) and lm1.active\n", "    left join retail.console_outlet_logistic_mapping lm2 on lm2.logistic_node_id = cast(fp.destination_node_id as int) and lm2.active\n", "    left join retail.console_outlet co on co.id = lm1.outlet_id and business_type_id in (1,12,19,20,21)\n", "    where fp.is_active\n", "    --   and lm2.outlet_id = 3587\n", "    )\n", "    \n", "select  *\n", "        -- source_outlet_\n", "        -- name,\n", "        -- count(*)\n", "from fleet_plan_base\n", "where tenure_end>current_date\n", "-- group by 1\n", "order by source_outlet_name, destination_outlet_name, tenure_start\n", "\"\"\"\n", "fleet_plan_df = read_sql_query(fleet_plan_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "12e028f8-880a-4b45-8bf0-1fe40721d3cd", "metadata": {}, "outputs": [], "source": ["fleet_plan_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d9ab0e2a-6869-4793-b1f1-fc520e3e4cbb", "metadata": {}, "outputs": [], "source": ["fleet_plan_df[\"tenure_start\"] = pd.to_datetime(fleet_plan_df[\"tenure_start\"])"]}, {"cell_type": "code", "execution_count": null, "id": "18633e36-e264-46b7-85a7-68c1c179dbe5", "metadata": {}, "outputs": [], "source": ["start_date = (pd.Timestamp.now() - <PERSON><PERSON><PERSON>(days=2)).date()\n", "start_date"]}, {"cell_type": "code", "execution_count": null, "id": "b14b635d-a1d0-4b68-a5a6-d648aec3efee", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now().date()\n", "current_date"]}, {"cell_type": "code", "execution_count": null, "id": "1649dd0d-c28e-4ea5-9f9e-43e283c81df6", "metadata": {}, "outputs": [], "source": ["date_range = pd.date_range(start=current_date, end=current_date)\n", "date_range"]}, {"cell_type": "code", "execution_count": null, "id": "2ae67233-5614-42d1-9580-221e5dd1d637", "metadata": {}, "outputs": [], "source": ["fleet_plan_df[\"dispatch_slot\"] = fleet_plan_df[\"dispatch_slot\"].str[:8]"]}, {"cell_type": "code", "execution_count": null, "id": "21c4008e-8b94-4836-9f0e-ea7eb4404abc", "metadata": {}, "outputs": [], "source": ["fleet_plan_df[\"dispatch_slot\"] = pd.to_datetime(\n", "    fleet_plan_df[\"dispatch_slot\"], format=\"%H:%M:%S\"\n", ").dt.time"]}, {"cell_type": "code", "execution_count": null, "id": "9c7df70f-5a7f-482b-877d-70373795a28c", "metadata": {}, "outputs": [], "source": ["fleet_plan_df[\"tenure_start\"] = pd.to_datetime(fleet_plan_df[\"tenure_start\"])\n", "fleet_plan_df[\"tenure_end\"] = pd.to_datetime(fleet_plan_df[\"tenure_end\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2b711792-064f-4739-92fd-cce234f202a3", "metadata": {}, "outputs": [], "source": ["results = []\n", "\n", "# Iterate over each date in the range\n", "for single_date in date_range:\n", "    # Check for each warehouse and darkstore combination\n", "    for index, row in fleet_plan_df.iterrows():\n", "        # Combine the date with each dispatch slot\n", "        dispatch_datetime = datetime.combine(single_date, row[\"dispatch_slot\"])\n", "\n", "        # Check if the combination of date and dispatch slot is within the range\n", "        if row[\"tenure_start\"] <= dispatch_datetime <= row[\"tenure_end\"]:\n", "            results.append(\n", "                {\n", "                    \"date\": single_date,\n", "                    \"source_outlet_id\": row[\"source_outlet_id\"],\n", "                    \"source_outlet_name\": row[\"source_outlet_name\"],\n", "                    \"destination_outlet_id\": row[\"destination_outlet_id\"],\n", "                    \"destination_outlet_name\": row[\"destination_outlet_name\"],\n", "                    \"dispatch_slot\": row[\"dispatch_slot\"],\n", "                    \"vehicle_number\": row[\"vehicle_number\"],\n", "                    \"destination_arrival_time\": row[\"destination_arrival_time\"],\n", "                }\n", "            )\n", "\n", "# Convert results to DataFrame\n", "fleet_plan_df1 = pd.DataFrame(results)"]}, {"cell_type": "code", "execution_count": null, "id": "da72ccc9-4cf3-4ab7-98d3-481eed672882", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1[\"dispatch_slot\"] = pd.to_datetime(\n", "    fleet_plan_df1[\"dispatch_slot\"], format=\"%H:%M:%S\"\n", ").dt.time\n", "# final_df['sch_dispatch_time'] = pd.to_datetime(final_df['sch_dispatch_time'], format='%H:%M:%S').dt.time"]}, {"cell_type": "code", "execution_count": null, "id": "27c47fd2-f27e-4c0f-8def-f2bacbef8d3b", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7fce773d-0d66-4c22-929c-e053a4790cd4", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1.date.min()"]}, {"cell_type": "code", "execution_count": null, "id": "4228159a-2f49-476a-a517-759885ff7478", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1[fleet_plan_df1[\"vehicle_number\"] == \"DL1LAE3408\"]"]}, {"cell_type": "code", "execution_count": null, "id": "5c339301-9f49-4929-b23f-55bed0d2c352", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1[fleet_plan_df1[\"source_outlet_id\"] == 1104].vehicle_number.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "db7df060-ff0c-4037-8d1d-737dac040a56", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e70af48-f3ac-470a-8076-754e691eaced", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4324c7e0-136b-4097-a487-af4419f9edc8", "metadata": {}, "outputs": [], "source": ["result_df1[\"sch_wh_arrival\"] = pd.to_datetime(result_df1[\"sch_wh_arrival\"])"]}, {"cell_type": "code", "execution_count": null, "id": "dd1f635c-fc5a-491f-9f6e-a794b2966ba4", "metadata": {}, "outputs": [], "source": ["# fleet_plan_df1['dispatch_slot']=pd.to_datetime(fleet_plan_df1['dispatch_slot']).dt.time"]}, {"cell_type": "code", "execution_count": null, "id": "d4f38338-6d12-4c34-be76-f1834889706d", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1[\"date\"] = fleet_plan_df1[\"date\"].astype(str)\n", "fleet_plan_df1[\"dispatch_slot\"] = fleet_plan_df1[\"dispatch_slot\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "7ad1b974-e0d7-4d55-8148-419e450c554f", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1[\"dispatch_slot\"] = pd.to_datetime(\n", "    fleet_plan_df1[\"date\"] + \" \" + fleet_plan_df1[\"dispatch_slot\"]\n", ")\n", "# fleet_plan_df1['destination_arrival_time']=pd.to_datetime(fleet_plan_df1['date'] + ' ' + fleet_plan_df1['destination_arrival_time'])"]}, {"cell_type": "code", "execution_count": null, "id": "4a015216-68f4-4030-9a4c-9c08ceb9e5d6", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1"]}, {"cell_type": "code", "execution_count": null, "id": "7c834d4c-bb44-43c7-817f-70ca14ebab69", "metadata": {}, "outputs": [], "source": ["result_df1[\"formatted_arrival\"] = result_df1[\"sch_wh_arrival\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "7806899d-ce85-413c-8be5-4da5934bdae3", "metadata": {}, "outputs": [], "source": ["# result_df1['scheduled_dispatch_time'] = result_df1['scheduled_dispatch_time'].dt."]}, {"cell_type": "code", "execution_count": null, "id": "ee040fad-d805-4a0a-aed3-07be1dead45f", "metadata": {}, "outputs": [], "source": ["result_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "44901f28-ce4e-4844-8909-93cf07c25536", "metadata": {}, "outputs": [], "source": ["q3 = sqldf(\n", "    \"\"\"\n", "with cte as(\n", "SELECT date(fpd.date) as date,\n", "        fpd.source_outlet_id,\n", "        source_outlet_name,\n", "        vehicle_number, \n", "        fpd.dispatch_slot, \n", "        fpd.destination_arrival_time,\n", "        destination_outlet_id,\n", "        destination_outlet_name,\n", "        rd.*,\n", "        row_number() OVER (PARTITION BY vehicle_number ORDER BY dispatch_slot) AS rankk\n", "    \n", "from fleet_plan_df1 AS fpd\n", "left join result_df1 AS rd\n", "ON fpd.source_outlet_id = rd.wh_outlet_id\n", "   AND fpd.vehicle_number = rd.truck_number \n", "WHERE (fpd.dispatch_slot > rd.scheduled_dispatch_time or rd.scheduled_dispatch_time is null)\n", "and (fpd.dispatch_slot > strftime('%H:%M:%S', rd.sch_wh_arrival) or rd.sch_wh_arrival is null)\n", "and dispatch_slot > DATETIME(CURRENT_TIMESTAMP, '+330 minutes')\n", ")\n", "select *\n", "from cte \n", "where rankk=1\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e1a70dd4-3a40-402c-a58c-8c7e86684575", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e4e14cca-a507-4e9b-9e3a-d3bc7b3111a3", "metadata": {}, "outputs": [], "source": ["q3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "842ee7f0-f7da-4ac3-9357-13340f3d2811", "metadata": {}, "outputs": [], "source": ["q3.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c617f83d-9ac7-4807-900a-df66930e3f71", "metadata": {}, "outputs": [], "source": ["q3[q3[\"trip_id\"].isnull() == False]"]}, {"cell_type": "code", "execution_count": null, "id": "852b4ef6-14f9-4283-841e-f3cf9b4fc951", "metadata": {}, "outputs": [], "source": ["res = q3"]}, {"cell_type": "code", "execution_count": null, "id": "60f996d1-3fd9-4706-b697-0598dc2e0282", "metadata": {}, "outputs": [], "source": ["res.shape"]}, {"cell_type": "code", "execution_count": null, "id": "42e56f9c-5df3-455a-8b46-93f0246572d1", "metadata": {}, "outputs": [], "source": ["fleet_plan_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "7d6f2cf2-9ca9-4a1e-9b45-d5d1385d264e", "metadata": {}, "outputs": [], "source": ["res.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ea4376a3-59c7-4665-8fd6-905ed35d1f94", "metadata": {}, "outputs": [], "source": ["res.truck_number.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "0ccd973d-dd40-4a75-8275-86b303c4fb90", "metadata": {}, "outputs": [], "source": ["res[\"dispatch_slot1\"] = pd.to_datetime(res[\"dispatch_slot\"]).dt.time"]}, {"cell_type": "code", "execution_count": null, "id": "cc830b82-99da-4aa7-9941-f79a9bdcfbd0", "metadata": {}, "outputs": [], "source": ["res.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5317a188-a05b-406f-8be1-1a4bbcea22d5", "metadata": {}, "outputs": [], "source": ["# res[res['consignment_id'].isnull()==False].shape"]}, {"cell_type": "code", "execution_count": null, "id": "babfbfee-753a-4c3b-aae4-355136f074ac", "metadata": {}, "outputs": [], "source": ["res[\"dispatch_slot\"] = pd.to_datetime(res[\"dispatch_slot\"])"]}, {"cell_type": "code", "execution_count": null, "id": "82677ca6-1b0b-49ef-b0c4-6c0ee2f8d994", "metadata": {}, "outputs": [], "source": ["# res['dispatch_slot1'] = pd.to_datetime(res['dispatch_slot1'], format='%H:%M:%S')\n", "\n", "# Calculate 'next_trip_reporting_threshold'\n", "res[\"next_trip_reporting_threshold\"] = res[\"dispatch_slot\"] - <PERSON><PERSON><PERSON>(minutes=90)\n", "# res['next_trip_reporting_threshold']=pd.to_datetime(res['next_trip_reporting_threshold']).dt.time\n", "# res['dispatch_slot']=pd.to_datetime(res['dispatch_slot']).dt.time"]}, {"cell_type": "code", "execution_count": null, "id": "480cfc81-7e84-420d-a709-6de1814f0c9b", "metadata": {}, "outputs": [], "source": ["res.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2c44c755-bff7-4fc2-903d-cf59a2181a65", "metadata": {}, "outputs": [], "source": ["res[\"ideal_wh_arrival\"] = pd.to_datetime(res[\"ideal_wh_arrival\"])"]}, {"cell_type": "code", "execution_count": null, "id": "57cfdbf9-5007-4a1f-aa42-1cf36fe90108", "metadata": {}, "outputs": [], "source": ["# import pandas as pd\n", "\n", "\n", "def calculate_delay(row):\n", "    # Check for NaT (missing values)\n", "    if pd.isna(row[\"ideal_wh_arrival\"]) or pd.isna(row[\"next_trip_reporting_threshold\"]):\n", "        return None  # or pd.NaT or any default value you prefer\n", "\n", "    # Calculate the delay directly\n", "    delay = row[\"next_trip_reporting_threshold\"] - row[\"ideal_wh_arrival\"]\n", "\n", "    return delay.total_seconds() / 60  # Return delay in minutes\n", "\n", "\n", "# Apply the function to calculate delay\n", "res[\"delay\"] = res.apply(calculate_delay, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "e8d7332f-a565-463e-95dd-36081d7e865b", "metadata": {}, "outputs": [], "source": ["res.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c297da2b-b025-47b2-9d88-bca7bd8d0b36", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0b068a13-24fe-48af-a430-d238813dd45b", "metadata": {}, "outputs": [], "source": ["res.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c3c2d50e-66d0-40a6-b4f3-32b138608281", "metadata": {}, "outputs": [], "source": ["res.drop(columns={\"formatted_arrival\", \"dispatch_slot1\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "44aabdea-60e8-42d2-8dde-00a26fddecda", "metadata": {}, "outputs": [], "source": ["q4 = \"\"\"\n", "WITH disruptions AS (\n", "    SELECT \n", "        outlet_id, \n", "        percent, \n", "        start_date_time, \n", "        end_date_time,\n", "        date(start_date_time) AS start_date,\n", "        date(end_date_time) AS end_date,\n", "        date_format(start_date_time, '%%H:%%i:%%s')AS start_time,  \n", "        date_format(end_date_time, '%%H:%%i:%%s')AS end_time\n", "    FROM \n", "        ars.disruption_v2_log \n", "    WHERE \n", "        insert_ds_ist >= cast(CURRENT_DATE - INTERVAL '1' DAY as varchar)\n", "),\n", "\n", "rnk as (\n", "SELECT  \n", "    d.start_date,\n", "    d.outlet_id, \n", "    d.percent, \n", "    d.start_date_time, \n", "    d.end_date_time,\n", "    ROW_NUMBER() OVER (PARTITION BY outlet_id, start_date ORDER BY start_time desc ) AS rnk_\n", "FROM \n", "    disruptions d )\n", "    \n", "select \n", "    r.start_date,\n", "    r.outlet_id, \n", "    r.percent, \n", "    r.start_date_time, \n", "    r.end_date_time\n", "    from rnk r\n", "where r.rnk_ = 1\n", "\"\"\"\n", "q4_df = read_sql_query(q4, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "ac9fc218-4770-41e1-a957-cdb00b29ee29", "metadata": {}, "outputs": [], "source": ["q4_df"]}, {"cell_type": "code", "execution_count": null, "id": "16ee69f7-9088-47f5-8fcc-11447532f736", "metadata": {}, "outputs": [], "source": ["res[\"sch_dispatch_date\"] = pd.to_datetime(res[\"scheduled_dispatch_time\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "afb3fe00-628c-4f1c-9194-5be752008323", "metadata": {}, "outputs": [], "source": ["res1 = sqldf(\n", "    \"\"\"\n", "SELECT res.*,\n", "       CASE \n", "           WHEN (delay < -420 AND trip_state IN ('LOADING_START', 'LOADING_END')) THEN 'Ontime-Next'\n", "           WHEN delay > 30 THEN 'Ontime'\n", "           WHEN (delay > 0 AND delay < 30) THEN 'Low-risk'\n", "           when delay is null then 'No prev trip'\n", "           ELSE 'High Risk' \n", "       END AS Reporting_status,\n", "       CASE \n", "           WHEN percent IS NULL THEN '-' \n", "           ELSE CAST(percent AS TEXT) \n", "       END AS percent,\n", "       CASE \n", "           WHEN start_date_time IS NULL THEN '-' \n", "           ELSE start_date_time \n", "       END AS start_date_time,\n", "       CASE \n", "           WHEN end_date_time IS NULL THEN '-' \n", "           ELSE end_date_time \n", "       END AS end_date_time\n", "FROM res\n", "LEFT JOIN q4_df ON q4_df.outlet_id = res.ds_outlet_id AND q4_df.start_date = res.sch_dispatch_date\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "62c48821-6644-41ec-8676-4f5cc7e05e10", "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "# Function to convert minutes to hours and minutes\n", "def convert_minutes_to_hours_minutes(minutes):\n", "    # Check for NaN (missing values)\n", "    if pd.isna(minutes):\n", "        return None\n", "    hours = math.floor(minutes / 60)  # Get the integer part (hours)\n", "    remaining_minutes = abs(minutes % 60)  # Get the remaining minutes\n", "    return f\"{hours} hrs {int(remaining_minutes):02} mins\"\n", "\n", "\n", "# Apply the function to the 'delay' column\n", "res1[\"hours_minutes\"] = res1[\"delay\"].apply(convert_minutes_to_hours_minutes)\n", "\n", "\n", "# Display the updated DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "bddd008c-d1f8-44b4-bce1-87a84931e175", "metadata": {}, "outputs": [], "source": ["# res1[res1['Reporting_status']=='Delay']\n", "res1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e007dfb5-1f9c-4e26-8bf2-748d5f5a94c1", "metadata": {}, "outputs": [], "source": ["res1.drop(\n", "    columns={\n", "        \"rankk\",\n", "        \"loading_wh_idle\",\n", "        \"med_frwd_enroute_time_taken\",\n", "        \"med_rev_enroute_time_taken\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "389d8550-5c71-49d8-b5da-02c9f958d24c", "metadata": {}, "outputs": [], "source": ["res2 = res1[\n", "    [\n", "        \"date\",\n", "        \"source_outlet_id\",\n", "        \"source_outlet_name\",\n", "        \"vehicle_number\",\n", "        \"dispatch_slot\",\n", "        \"destination_arrival_time\",\n", "        \"destination_outlet_id\",\n", "        \"destination_outlet_name\",\n", "        \"trip_id\",\n", "        \"wh_facility_id\",\n", "        \"wh_outlet_id\",\n", "        \"wh_outlet_name\",\n", "        \"ds_outlet_id\",\n", "        \"ds_outlet_name\",\n", "        \"trip_type\",\n", "        \"trip_state\",\n", "        \"truck_number\",\n", "        \"vehicle_type\",\n", "        \"driver_id\",\n", "        \"driver_name\",\n", "        \"driver_mobile\",\n", "        \"sch_wh_arrival\",\n", "        \"truck_entry_wh\",\n", "        \"loading_start_wh\",\n", "        \"loading_end_wh\",\n", "        \"enroute_wh\",\n", "        \"ready_for_dispatch\",\n", "        \"scheduled_dispatch_time\",\n", "        \"ds_reach\",\n", "        \"unloading_start_ds\",\n", "        \"unloading_end_ds\",\n", "        \"loading_start_ds\",\n", "        \"loading_end_ds\",\n", "        \"truck_return_wh\",\n", "        \"unloading_start_wh\",\n", "        \"unloading_end_wh\",\n", "        \"completed_ts\",\n", "        \"enroute_wh_time\",\n", "        \"enroute_ds_time\",\n", "        \"enroute_wh_time_med\",\n", "        \"enroute_ds_time_med\",\n", "        \"ideal_wh_arrival\",\n", "        \"next_trip_reporting_threshold\",\n", "        \"delay\",\n", "        \"Reporting_status\",\n", "        \"percent\",\n", "        \"start_date_time\",\n", "        \"end_date_time\",\n", "        \"hours_minutes\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f6178a01-cffd-49f1-beb1-7b5068440ece", "metadata": {}, "outputs": [], "source": ["res2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ada8e782-52fa-4484-8495-96c4cf9367b5", "metadata": {}, "outputs": [], "source": ["res2 = res2[res2[\"trip_id\"].isnull() == False]"]}, {"cell_type": "code", "execution_count": null, "id": "50216a84-8615-49be-b570-d1d121bef70b", "metadata": {}, "outputs": [], "source": ["# res2.date.min()"]}, {"cell_type": "code", "execution_count": null, "id": "fe2bb38c-ae56-4deb-b6c5-e0e2ba93fafa", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1zhfudEJ79ZaG5PhWyq_ghV6lbZ29txIH6ClYMs5nLIM\""]}, {"cell_type": "code", "execution_count": null, "id": "6f59527a-9d21-469d-bc40-cfd1413292af", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(res2, sheet_id, \"vehicle status raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "06ab88ee-0b40-41de-affd-d732be4703f4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}