alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: b2b_master_data
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U0882AQN864
path: povms/fleet/etl/b2b_master_data
paused: false
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2025-09-17T00:00:00'
  interval: 0 */4 * * *
  start_date: '2025-06-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
