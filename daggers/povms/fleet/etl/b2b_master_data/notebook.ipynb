{"cells": [{"cell_type": "code", "execution_count": null, "id": "8af98ff7-8b51-4c80-a95b-1f033490ea5c", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "e2116e01-0507-4d32-8d6f-80ab8477841a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "e604f555-3a86-4b9b-9647-e8304ff0b808", "metadata": {}, "outputs": [], "source": ["# # New table\n", "trino_schema_name = \"supply_etls\"  # \"supply_etls\"  # \"povms\"\n", "trino_table_name = \"B2B_master_data\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "38a1ae3b-d715-4ae1-9790-e2fa9881b695", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "80078c6a-47ef-4003-bd54-ba08d4064ab3", "metadata": {}, "source": ["### Current Trip Data"]}, {"cell_type": "code", "execution_count": null, "id": "1efc715d-2f34-4a0e-a303-1329c934fe09", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with enroute_tat as\n", "    (\n", "    select con.consignment_id,\n", "           tl1.actual_ts as dispatch_ts,\n", "           tl1.actual_ts + (tl2.scheduled_ts - tl1.scheduled_ts) as arrival_eta_at_dispatch,\n", "           tl2.actual_ts as arrival_ts,\n", "           tl2.scheduled_ts + interval '330' minute as sch_ds_arrival\n", "    from transit_server.transit_projection_consignment con\n", "        join transit_server.transit_projection_trip_event_timeline tl1 on con.trip_id = tl1.trip_id and tl1.event_type = 'LOADING_END' and con.consignment_type = json_extract_scalar(tl1.event_meta, '$.consignment_type') and tl1.lake_active_record\n", "        join transit_server.transit_projection_trip_event_timeline tl2 on con.trip_id = tl2.trip_id and con.consignment_id = cast(json_extract_scalar(tl2.event_meta, '$.consignment_id') as int) and con.consignment_type = json_extract_scalar(tl2.event_meta, '$.consignment_type')\n", "        and tl2.event_type = 'DS_ARRIVAL' and tl2.lake_active_record\n", "    where tl1.insert_ds_ist >= cast(current_date - interval '30' day as varchar) and tl1.lake_active_record --and tl1.event_type = 'LOADING_END' \n", "        and tl2.insert_ds_ist >= cast(current_date - interval '30' day as varchar) and tl2.lake_active_record \n", "        and con.insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "        and con.consignment_type = 'FORWARD_CONSIGNMENT'\n", "    ),\n", "\n", "dock_assignment as\n", "    (\n", "    select  trip_id,\n", "            -- vehicle_type,\n", "            -- vehicle_number,\n", "            -- cast(json_extract(tav.metadata,'$.driver_name') as varchar) AS driver_name,\n", "            max(tavl2.label_value) as loading_dock_name,\n", "            max(tavl2.install_ts + interval '330' minute) as dock_assign_time_frwd\n", "    from transit_server.transit_allocation_vehicle tav\n", "    join transit_server.transit_allocation_vehicle_label tavl1 on tav.id = tavl1.allocation_vehicle_id and tavl1.label_type = 'EXPECTED_NODES'\n", "    join transit_server.transit_allocation_vehicle_label tavl2 on tav.id = tavl2.allocation_vehicle_id and tavl2.label_type = 'DOCK'\n", "    where activity_type = 'LOADING'\n", "      and tav.insert_ds_ist >= cast(current_date - interval '40' day as varchar)\n", "      and tavl1.insert_ds_ist >= cast(current_date - interval '40' day as varchar)\n", "      and tavl2.insert_ds_ist >= cast(current_date - interval '40' day as varchar)\n", "    group by 1\n", "    ),\n", "\n", "\n", "med_time as\n", "    (\n", "    with cte as(select  wh_outlet_id,\n", "            ds_outlet_id,\n", "            count(distinct trip_id) as trip_count,\n", "            approx_percentile(enroute_time_taken, 0.5) as med_enroute_time_taken\n", "    from\n", "        (\n", "        select *,\n", "                date_diff('minute',cast(ready_for_dispatch as timestamp) , cast(ds_reached as timestamp)) as enroute_time_taken \n", "        from supply_etls.fleet_trips\n", "        where trip_date between cast(current_date - interval '60' day as varchar) and cast(current_date - interval '1' day as varchar)\n", "        )\n", "    group by 1,2\n", "    )\n", "    \n", "select cte.*,distance_km,time_min\n", "from cte \n", "left join supply_etls.wh_to_ds_distance_v4 wd on wd.wh_outlet_id=cte.wh_outlet_id and wd.ds_outlet_id=cte.ds_outlet_id\n", "\n", "    ),\n", "truck_entry as (\n", "select id,entry_ts as truck_entry_wh \n", "from transit_server.transit_express_allocation_field_executive_queue \n", "where insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", " ),\n", "\n", "final as\n", "    (\n", "    select  distinct ft.consignment_id,\n", "            con_type,\n", "            ft.trip_id,\n", "            trip_date,\n", "            trip_type,\n", "            n1.external_id as source_node_id,\n", "            wh_facility_id as facility_id,\n", "            ft.wh_outlet_id,\n", "            wh_outlet_name,\n", "            n.external_id as ds_node_id,\n", "            ft.ds_outlet_id,\n", "            ds_outlet_name,\n", "            case when trim(ft.destination_city)='Bangalore' then 'Bengaluru'\n", "                 when ft.destination_city in ('Bhiwadi') then 'Bhiwadi - Not in use'\n", "                 when ft.destination_city in ('Ghaziabad') then 'UP-NCR'\n", "                 when ft.destination_city in ('Gurgaon','Jhajjar') then 'HR-NCR'\n", "                 else trim(ft.destination_city) end\n", "            as city,\n", "            vendor_id,\n", "            vendor_name,\n", "            truck_number,\n", "            truck_type,\n", "            CASE WHEN truck_type LIKE '%%Reefer%%' OR truck_type LIKE '%%Chiller%%' THEN 'Cold' ELSE 'Dry' END AS truck_cold_dry,\n", "            case when truck_type in ('<PERSON><PERSON> Ace', 'Chiller Tata Ace', 'Reefer <PERSON> Ace') then 750\n", "                 when truck_type in ('Pickup','Chiller Pickup', 'Reefer Pickup','EV Pickup','Tata Gold','Electric Van') then 1200\n", "                 when truck_type in ('8 FT','Reefer 8 FT','Chiller 8 FT') then 1200\n", "                 when truck_type in ('22 FT', 'Chiller 22 FT','Reefer 22 FT') then 6500\n", "                 when truck_type in ('Tata 407','Reefer Tata 407','Chiller Tata 407','Reefer 10 FT','Chiller 10 FT','10 FT')  then 2500\n", "                 when truck_type in ('14 FT','Reefer 14 FT','Chiller 14 FT') then 3000\n", "                 when truck_type in ('17 FT','Reefer 17 FT','Chiller 17 FT') then 4000\n", "                 when truck_type in ('20 FT','Reefer 20 FT','Chiller 20 FT') then 6000\n", "                 when truck_type in ('Tata Intra V50','Reefer Tata Intra V50','Chiller Tata Intra V50') then 2000\n", "                 when truck_type in ('24 FT','Reefer 24 FT','Chiller 24 FT') then 8500\n", "                 when truck_type in ('32 FT','Reefer 32 FT','Chiller 32 FT') then 9000\n", "                 when truck_type in ('32 FT' , '32 FT SXL' , 'Chiller 32 FT SXL 9MT' , 'Reefer 32 FT SXL 9MT') then 9000\n", "                 when truck_type in ('32 FT MXL' , '32 FT MXL 15MT' , 'Chiller 32 FT MXL 15MT' , 'Chiller 32 FT 15MT') then 15000\n", "                 when truck_type in ('32 FT SXL 9 MT') then 9000\n", "                 when truck_type in ('32 FT SXL 7 MT','24 FT') then 7000\n", "                 when truck_type in ('32 FT MXL 9 MT','Chiller 32 FT SXL','Reefer 32 FT SXL') then 9000\n", "                 when truck_type in ('32 FT MXL 18MT' ,  'Chiller 32 FT MXL 18MT' , 'Chiller 32 FT 18MT') then 18000\n", "                 when truck_type in ('19 FT') then 5500 \n", "                 when truck_type in ('Chiller 32 FT MXL') then 15000\n", "            end as truck_capacity,\n", "            -- case when truck_type = 'Tata Ace' then 6*3.5*3.5*30.5*30.5*30.5\n", "--                  when truck_type = 'Pickup' then 7*4*4*30.5*30.5*30.5  \n", "--                  when truck_type = 'Reefer Pickup' then 9*6*6*30.5*30.5*30.5  \n", "--                  when truck_type = '14 FT' then 14*6.5*7*30.5*30.5*30.5\n", "--                  when truck_type = '17 FT' then 17*6.5*7*30.5*30.5*30.5\n", "--                  when truck_type = '20 FT' then 20*7.5*7.5*30.5*30.5*30.5\n", "--                  when truck_type = '22 FT' then 22*8*8*30.5*30.5*30.5\n", "--                  when truck_type = 'Tata 407' then 10*6.5*7*30.5*30.5*30.5\n", "--             end as truck_volume,\n", "CASE\n", "    WHEN truck_type = '14 FT' THEN 14.0*6.0*7.0*30.48*30.48*30.48\n", "    WHEN truck_type = '17 FT' THEN 17.0*7.0*7.0*30.48*30.48*30.48\n", "    WHEN truck_type = '20 FT' THEN 20.0*7.0*7.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Bolero Pickup' THEN 8.0*5.0*6.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Chiller Pickup' THEN 8.5*4.9*4.9*30.48*30.48*30.48\n", "    WHEN truck_type = 'Intra Pickup' THEN 8.0*5.0*5.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Pickup' THEN 8.21*5.19*5.6*30.48*30.48*30.48\n", "    WHEN truck_type = 'Reefer Pickup' THEN 8.0*5.1*5.25*30.48*30.48*30.48\n", "    WHEN truck_type = 'Tata 407' THEN 10.0*6.0*7.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Tata Ace' THEN 7.24*5.01*6.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Tata Gold' THEN 7.2*4.9*4.9*30.48*30.48*30.48\n", "    WHEN truck_type = 'Tata Intra V50' THEN 9.7*5.74*5.0*30.48*30.48*30.48\n", "    WHEN truck_type = '10 FT' THEN 10.0*7.0*7.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Electric Van' THEN 9.5*4.8*4.8*30.48*30.48*30.48\n", "    WHEN truck_type = 'EV Pickup' THEN 7.5*5.0*4.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'EV Tata Ace' THEN 6.3*4.3*4.3*30.48*30.48*30.48\n", "    WHEN truck_type = '22 FT' THEN 22.0*7.6*8.0*30.48*30.48*30.48\n", "    WHEN truck_type = '19 FT' THEN 19.0*7.2*7.5*30.48*30.48*30.48\n", "    WHEN truck_type = 'Reefer Tata 407' THEN 9.5*5.3*5.3*30.48*30.48*30.48\n", "    WHEN truck_type = 'Chiller 17 FT' THEN 16.5*6.0*6.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Chiller 14 FT' THEN 13.5*5.5*5.5*30.48*30.48*30.48\n", "    WHEN truck_type = 'Reefer <PERSON>' THEN 7.0*4.5*4.5*30.48*30.48*30.48\n", "    WHEN truck_type = '8 FT' THEN 8.0*5.0*5.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Reefer 14 FT' THEN 13.5*5.5*5.5*30.48*30.48*30.48\n", "    WHEN truck_type = 'Chiller 22 FT' THEN 21.5*7.0*7.0*30.48*30.48*30.48\n", "    WHEN truck_type = 'Chiller Tata 407' THEN 9.0*6.0*5.0*30.48*30.48*30.48\n", "    WHEN truck_type = '24 FT' THEN 24.0*7.3*7.0*30.48*30.48*30.48\n", "END as truck_volume,\n", "\n", "            trip_state,\n", "            consignment_state,\n", "            num_invoices,\n", "            exp_crates_dispatched,\n", "            total_crates_dispatched,\n", "            dispatched_qty as indent,\n", "            (dispatched_qty_weight/1000) as indent_wt,\n", "            dispatched_volume as indent_volume,\n", "            (cast(scheduled_dispatch_time as timestamp) - interval '210' minute) as reporting_threshold,\n", "            (cast(scheduled_dispatch_time as timestamp) - interval '180' MINUTE) AS loading_start_threshold,\n", "            scheduled_truck_arrival,    -- 60mins before SDT\n", "            -- truck_entry_wh,\n", "            te.truck_entry_wh,\n", "            dock_assign_time_frwd,\n", "            loading_dock_name,\n", "            truck_handshake,\n", "            loading_start_wh as loading_start,\n", "            ready_for_dispatch,\n", "            enroute,\n", "            scheduled_dispatch_time,\n", "            (cast(cast(scheduled_dispatch_time as timestamp) as time)) as dispatch_time,\n", "            CAST(cast(scheduled_dispatch_time as timestamp) AS date) as dispatch_date,\n", "            exp_dispatch_time,\n", "            ds_reached,\n", "            arrival_eta_at_dispatch + interval '330' minute as exp_ds_reach_system,\n", "            --cast(coalesce(scheduled_dispatch_time,ready_for_dispatch) as timestamp) + interval '1' minute * med_enroute_time_taken as exp_ds_reach_manual,\n", "            --coalesce(CAST(CAST(ds_arrival_date AS VARCHAR) || ' ' || destination_arrival_time AS timestamp),\n", "            et.sch_ds_arrival as exp_ds_reach_manual,\n", "            --cast(coalesce(scheduled_dispatch_time,ready_for_dispatch) as timestamp) + interval '1' minute * (case when mt.trip_count>5 then med_enroute_time_taken else time_min end)) as exp_ds_reach_manual,\n", "            unloading_start_ds,\n", "            unloading_end_ds,\n", "            loading_start_ds,\n", "            loading_end_ds,\n", "            truck_return_wh,\n", "            completed_ts,\n", "            last_trip_id,\n", "\n", "            date_diff('minute', cast(loading_start_wh as timestamp), cast(ready_for_dispatch as timestamp)) as loading_time_at_wh,\n", "            case when cast(loading_start_wh as timestamp) > (cast(scheduled_dispatch_time as timestamp) - interval '180' MINUTE) \n", "                 then 'load_start_delay' else 'load_start_ontime' \n", "            end as load_start_status,  \n", "            date_diff('minute', cast(ready_for_dispatch as timestamp), cast(ds_reached as timestamp)) as frwd_transit_time,\n", "\n", "            date_diff('minute', cast(exp_dispatch_time as timestamp), cast(ready_for_dispatch as timestamp)) as dispatch_delay_min,\n", "            case when (date_diff('minute', cast(scheduled_dispatch_time as timestamp), cast(ready_for_dispatch as timestamp))) <= 60 \n", "                 then 'dispatch_ontime' else 'dispatch_delay' \n", "            end as dispatch_delay_status,\n", "            \n", "            date_diff('minute', cast(ds_reached as timestamp), cast(unloading_start_ds as timestamp)) as unload_start_delay_at_ds,\n", "            date_diff('minute', cast(unloading_start_ds as timestamp), cast(unloading_end_ds as timestamp)) as unloading_time_at_ds,\n", "            date_diff('minute', cast(unloading_end_ds as timestamp), cast(loading_start_ds as timestamp)) as unload_com_to_load_start_ds,\n", "            date_diff('minute', cast(loading_start_ds as timestamp), cast(loading_end_ds as timestamp)) as loading_time_at_ds,\n", "\n", "            (CASE\n", "                  WHEN hour(cast(CAST(truck_handshake AS timestamp) as time)) < 12 THEN 'Morning'\n", "                  ELSE 'Evening'\n", "            END) AS dispatch_slot,\n", "            (case when arrival_ts > (arrival_eta_at_dispatch + interval '30' minute) then 'breach' else 'normal' end) as frwd_enroute_breach_status\n", "          \n", "            \n", "\n", "            from supply_etls.fleet_trips ft\n", "            left join enroute_tat et on et.consignment_id = ft.consignment_id\n", "            left join dock_assignment da on da.trip_id = ft.trip_id\n", "            left join med_time mt on mt.wh_outlet_id=ft.wh_outlet_id and mt.ds_outlet_id=ft.ds_outlet_id\n", "\n", "            left join retail.console_outlet_logistic_mapping m ON m.outlet_id = ft.ds_outlet_id and m.lake_active_record\n", "            left join retail.console_outlet_logistic_mapping m1 ON m1.outlet_id = ft.wh_outlet_id\n", "            left join transit_server.transit_node n on cast(m.logistic_node_id as varchar) = n.external_id and n.lake_active_record\n", "            left join transit_server.transit_node n1 on cast(m1.logistic_node_id as varchar) = n1.external_id\n", "            left join supply_etls.fleet_plan fp on fp.dispatch_date = CAST(cast(scheduled_dispatch_time as timestamp) AS date)\n", "            and cast(fp.dispatch_slot as time) = cast(format_datetime(cast(scheduled_dispatch_time as timestamp), 'HH:mm:ss') as time)\n", "            and ft.ds_outlet_id = fp.destination_outlet_id and ft.wh_outlet_id = fp.source_outlet_id\n", "            and fp.dispatch_date >= current_date - interval '30' day\n", "            left join truck_entry te on split_part(ft.trip_id, '_', 2)=cast(te.id as varchar)\n", "    where (trip_date) >= cast(current_date - interval '30' day as varchar)\n", "      and trip_state <> 'CANCELLED'\n", "    \n", "    ),\n", "    \n", "    \n", "    planned_time as (\n", "    select * from(\n", "    SELECT \n", "    source_node_id,\n", "    destination_node_id,\n", "    system_distance_in_km,\n", "    team_recommended_distance_in_km,\n", "    row_number()over (partition by source_node_id,destination_node_id order by update_ts desc) as rn\n", "FROM fleet_management.fleet_management_distance_mapping\n", "WHERE lake_active_record\n", "and is_active)\n", "where rn=1\n", ")\n", "\n", "select  md.*, pt.system_distance_in_km,pt.system_distance_in_km/15.00 as system_transit_time_in_hrs,pt.team_recommended_distance_in_km,\n", "pt.team_recommended_distance_in_km/15.00 as team_recommended_transit_time_in_hrs,\n", "        ((indent_wt*truck_capacity)/NULLIF((sum(indent_wt) over (partition by trip_id)),0)) as truck_cap_proportion,\n", "        ((indent_volume*truck_volume)/NULLIF((sum(indent_volume) over (partition by trip_id)),0)) as truck_vol_proportion\n", "from final md\n", "join retail.console_outlet co on co.id = md.ds_outlet_id and co.business_type_id in (1,12)\n", "left join planned_time pt on cast(pt.source_node_id as varchar)=cast(md.source_node_id as varchar) and cast(pt.destination_node_id as varchar)=cast(md.ds_node_id as varchar)\n", "-- left join truck_entry te on split_part(md.trip_id, '_', 2)=cast(te.id as varchar)\n", "\"\"\"\n", "\n", "transit_df = read_sql_query(query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "746142a4-3c4a-4fa1-b902-8f0ca4b4442c", "metadata": {}, "outputs": [], "source": ["transit_df.tail(10)"]}, {"cell_type": "code", "execution_count": null, "id": "f7f432f1-c894-42af-944a-834c5c42c452", "metadata": {}, "outputs": [], "source": ["transit_df.groupby(\"trip_date\")[\"trip_id\"].count().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "8f197b7b-e0e4-41ea-8d37-5460921a9fe1", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c0e6c2a3-0b35-49d1-aad6-e5497b1f484a", "metadata": {}, "outputs": [], "source": ["transit_df[\"ds_reached\"] = pd.to_datetime(transit_df[\"ds_reached\"])\n", "transit_df[\"exp_ds_reach_system\"] = pd.to_datetime(transit_df[\"exp_ds_reach_system\"])\n", "transit_df[\"exp_ds_reach_manual\"] = pd.to_datetime(transit_df[\"exp_ds_reach_manual\"])\n", "\n", "transit_df[\"ds_reach_delay_system\"] = (\n", "    transit_df[\"ds_reached\"] - transit_df[\"exp_ds_reach_system\"]\n", ").dt.total_seconds() / 60\n", "transit_df[\"ds_reach_delay_manual\"] = (\n", "    transit_df[\"ds_reached\"] - transit_df[\"exp_ds_reach_manual\"]\n", ").dt.total_seconds() / 60"]}, {"cell_type": "markdown", "id": "dad054d4-c0b4-4894-8839-ad463a63ceeb", "metadata": {}, "source": ["#### Additions"]}, {"cell_type": "code", "execution_count": null, "id": "76284413-0379-403a-9989-a86217ab79cd", "metadata": {}, "outputs": [], "source": ["transit_df[\"month\"] = pd.to_datetime(transit_df[\"trip_date\"]).dt.strftime(\"%B\")"]}, {"cell_type": "markdown", "id": "9d47b04b-1ec9-414f-bdfe-b42bba3429d0", "metadata": {}, "source": ["### Last Trip data - Vehicle Timelines in its last trip"]}, {"cell_type": "code", "execution_count": null, "id": "690df0d4-0e3c-423c-a77b-3944959ed835", "metadata": {}, "outputs": [], "source": ["last_rev_transit_query = \"\"\"\n", "\n", "SELECT  trip_id as last_trip_id,\n", "        max(cast(scheduled_dispatch_time as timestamp)) as last_trip_scheduled_dispatch_time,\n", "        max(cast(ready_for_dispatch as timestamp)) as last_trip_ready_for_dispatch,  \n", "        max(cast(ds_reached as timestamp)) as last_trip_ds_reached,\n", "        max(cast(unloading_start_ds as timestamp)) as last_trip_unloading_start_ds,\n", "        max(cast(unloading_end_ds as timestamp)) as last_trip_unloading_end_ds,\n", "        max(cast(loading_start_ds as timestamp)) as last_trip_load_start_ds,\n", "        max(cast(loading_end_ds as timestamp)) as last_trip_load_complete_ds,\n", "        max(cast(coalesce(coalesce(truck_return_wh,completed_ts),truck_entry_wh) as timestamp)) as last_trip_wh_checkin,\n", "        max(cast(unloading_start_wh as timestamp)) AS last_trip_unload_start_wh,\n", "        max(cast(unloading_completed_wh as timestamp)) AS last_trip_unload_complete_wh\n", "    \n", "from supply_etls.fleet_trips ft\n", "join retail.console_outlet co on co.id = ft.ds_outlet_id and co.business_type_id in (1,12)\n", "where trip_date >= cast(current_date - interval '40' day as varchar)\n", "group by 1\n", "\n", "\"\"\"\n", "last_rev_transit_df = read_sql_query(last_rev_transit_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "0de0f430-ebd8-439d-82b0-8bf7d722a7cf", "metadata": {}, "outputs": [], "source": ["last_rev_transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "78dc11bb-f629-482d-a24e-c5ad7307da21", "metadata": {}, "outputs": [], "source": ["transit = transit_df.merge(last_rev_transit_df, how=\"left\", on=[\"last_trip_id\"])\n", "transit.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bff3773e-4329-463d-9c4e-f8a350a64fc5", "metadata": {}, "outputs": [], "source": ["transit.shape"]}, {"cell_type": "code", "execution_count": null, "id": "70ed4d01-cc66-47cb-8206-d663b5cdbfe9", "metadata": {}, "outputs": [], "source": ["## To define Vehicle's Dispatch status in last trip & Perfect Trip Status\n", "\n", "transit1 = sqldf(\n", "    \"\"\"\n", "with trips_base as\n", "    (\n", "    select  *,\n", "            coalesce((strftime('%s', last_trip_unload_start_wh) - strftime('%s', last_trip_wh_checkin)),0) / 60 as idle_time_checkin_unload_start,\n", "            coalesce((strftime('%s', loading_start) - strftime('%s', last_trip_unload_complete_wh)),0) / 60 as idle_time_unload_comp_load_start,\n", "            CASE \n", "                WHEN truck_entry_wh > reporting_threshold THEN 'reporting_delay' \n", "                ELSE 'reporting_ontime' \n", "            END AS vehicle_report_status,\n", "\n", "\n", "            --case when last_trip_wh_checkin > reporting_threshold then 'reporting_delay' else 'reporting_ontime' end as vehicle_report_status,\n", "            (strftime('%s', truck_entry_wh) - strftime('%s', reporting_threshold)) / 60 as reporting_delay_min,\n", "\n", "            (strftime('%s', last_trip_ready_for_dispatch) - strftime('%s', last_trip_scheduled_dispatch_time)) / 60 as dispatch_delay_last_trip,\n", "            case when (strftime('%s', last_trip_ready_for_dispatch) - strftime('%s', last_trip_scheduled_dispatch_time)) / 60 > 15 \n", "            then 'dispatch_delay' else 'normal' \n", "            end as dispatch_status_last_trip,\n", "\n", "            (strftime('%s', ready_for_dispatch) - strftime('%s', last_trip_wh_checkin)) / (60 * 60 * 24) as days_diff_bw_last_trip,\n", "            (strftime('%s', coalesce(last_trip_load_complete_ds,last_trip_unloading_end_ds)) - strftime('%s', last_trip_ds_reached)) / 60 as last_trip_ds_stay_time,\n", "            (strftime('%s', last_trip_wh_checkin) - strftime('%s', coalesce(last_trip_load_complete_ds,last_trip_unloading_end_ds))) / 60 as last_trip_enroute_wh_time\n", "\n", "    from transit\n", "    )\n", "\n", "select  *--,\n", "--        case when vehicle_report_status = 'reporting_ontime' \n", "--              and ds_reporting_status = 'On-time'\n", "--             and dispatch_delay_status = 'dispatch_ontime'\n", "--             then 'perfect_trip' \n", "--             else 'normal_trip'\n", "--        end as trip_success\n", "from trips_base\n", "\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aefee3cf-e022-4d0f-b276-86164457b310", "metadata": {}, "outputs": [], "source": ["transit1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9c152bb2-7b00-429b-9d03-8129aa2e21f8", "metadata": {}, "outputs": [], "source": ["transit1.dispatch_status_last_trip.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "0be9d881-bfcf-4664-a125-a08b5ee89b0b", "metadata": {}, "outputs": [], "source": ["transit1.shape"]}, {"cell_type": "markdown", "id": "655a708f-3980-4bb5-b9d5-c1dc9c0cedae", "metadata": {}, "source": ["### Dispatch Delay Attribution"]}, {"cell_type": "markdown", "id": "4a087490-47ac-4e12-aa9c-6d2eb9665146", "metadata": {}, "source": ["#### To get Docks Count at WH"]}, {"cell_type": "code", "execution_count": null, "id": "1c86e5f0-a4df-4d0e-bd2e-86a8f8436f87", "metadata": {}, "outputs": [], "source": ["# query2 = \"\"\"\n", "\n", "# with base as\n", "#     (\n", "#     SELECT\n", "#         wh_outlet_id,\n", "#         wh_outlet_name,\n", "#         loading_dock_name,\n", "#         COUNT(DISTINCT trip_id) AS trip_count\n", "#     FROM\n", "#         supply_etls.fleet_master_data\n", "#     where trip_date > current_date - interval '20' day\n", "#     GROUP BY 1,2,3\n", "#     ),\n", "\n", "# perc_35 as\n", "#     (\n", "#     select  *,\n", "#             approx_percentile(trip_count,0.35) over (partition by wh_outlet_id) as perc_35\n", "#     from base\n", "#     )\n", "\n", "# select  wh_outlet_id,\n", "#         wh_outlet_name,\n", "#         count(distinct loading_dock_name) as total_docks\n", "# from perc_35\n", "# where trip_count >= perc_35\n", "# group by 1,2\n", "\n", "# \"\"\"\n", "# dock_count_wh = read_sql_query(query2, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "9fc47a74-20fe-4e77-a495-3ef261a25286", "metadata": {}, "outputs": [], "source": ["# transit11 = transit1.merge(\n", "#     dock_count_wh[[\"wh_outlet_id\", \"total_docks\"]], how=\"left\", on=[\"wh_outlet_id\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "4ead3544-c8af-4ca4-a81b-7aaccab98752", "metadata": {}, "outputs": [], "source": ["transit11 = transit1\n", "transit11.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f2fbdd0f-9ecf-4136-93dd-2c539ff6e311", "metadata": {}, "outputs": [], "source": ["transit11[\"ready_for_dispatch\"] = pd.to_datetime(transit11[\"ready_for_dispatch\"])\n", "transit11[\"loading_start_threshold\"] = pd.to_datetime(transit11[\"loading_start_threshold\"])\n", "transit11[\"loading_start\"] = pd.to_datetime(transit11[\"loading_start\"])"]}, {"cell_type": "code", "execution_count": null, "id": "736621a6-a3aa-472f-bd8b-30dfeda20ebf", "metadata": {}, "outputs": [], "source": ["# matching_counts = []\n", "\n", "\n", "# for current_trip_index in range(len(transit11)):\n", "#     current_trip = transit11.iloc[current_trip_index]\n", "\n", "#     # add WH\n", "#     same_wh_condition = transit11[\"wh_outlet_id\"] == current_trip[\"wh_outlet_id\"]\n", "#     same_date_condition = transit11[\"trip_date\"] == current_trip[\"trip_date\"]\n", "#     same_slot_condition = transit11[\"dispatch_slot\"] == current_trip[\"dispatch_slot\"]\n", "#     ready_for_dispatch_condition = (\n", "#         transit11[\"ready_for_dispatch\"] > current_trip[\"loading_start_threshold\"]\n", "#     )\n", "#     loading_start_condition = transit11[\"loading_start\"] < current_trip[\"loading_start_threshold\"]\n", "\n", "#     count_matching_trips = transit11[\n", "#         same_wh_condition\n", "#         & same_date_condition\n", "#         & same_slot_condition\n", "#         & ready_for_dispatch_condition\n", "#         & loading_start_condition\n", "#     ].loading_dock_name.nunique()\n", "\n", "#     matching_counts.append(count_matching_trips)\n", "\n", "# transit11[\"docks_in_use\"] = matching_counts"]}, {"cell_type": "code", "execution_count": null, "id": "c76cd747-a1cb-4374-a716-6a6d572f58f9", "metadata": {}, "outputs": [], "source": ["# transit11[\"dock_util\"] = transit11[\"docks_in_use\"] * 100.00 / transit11[\"total_docks\"]"]}, {"cell_type": "code", "execution_count": null, "id": "55a50732-eb5f-432e-8e2b-76fd22dc9852", "metadata": {}, "outputs": [], "source": ["# transit11[\"dock_issue\"] = transit11[\"dock_util\"].apply(lambda x: \"Fleet\" if x >= 75.00 else \"FC\")"]}, {"cell_type": "code", "execution_count": null, "id": "db6b7342-2eca-4134-b925-4db3d9f5892f", "metadata": {}, "outputs": [], "source": ["transit11.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d46e91c4-9e36-4eb4-bad7-96683d7328cf", "metadata": {}, "outputs": [], "source": ["## Calculate Median WH Return Time to use for Reporting Delay attribution to <PERSON><PERSON><PERSON> in case of transit delay\n", "\n", "med_calculation = (\n", "    transit11.groupby([\"facility_id\", \"ds_outlet_id\", \"truck_type\"], as_index=False)\n", "    .agg({\"last_trip_enroute_wh_time\": \"median\"})\n", "    .rename(columns={\"last_trip_enroute_wh_time\": \"enroute_wh_time_med\"})\n", ")\n", "\n", "med_calculation.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e9e87f5e-d2ce-4127-b95a-d336c2bc95ae", "metadata": {}, "outputs": [], "source": ["transit2 = transit11.merge(\n", "    med_calculation, how=\"left\", on=[\"facility_id\", \"ds_outlet_id\", \"truck_type\"]\n", ")\n", "\n", "transit2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3098edb1-58a9-43d0-8e58-f738aafdf531", "metadata": {}, "outputs": [], "source": ["## Vehicle Report Delay attribution\n", "\n", "\n", "def report_delay_attribute(x):\n", "    if x[\"vehicle_report_status\"] == \"reporting_delay\":\n", "        if pd.isna(x[\"scheduled_dispatch_time\"]) == True:\n", "            return \"-\"\n", "        elif (pd.isna(x[\"last_trip_ds_reached\"]) == True) | (x[\"days_diff_bw_last_trip\"] >= 3):\n", "            return \"Vendor\"\n", "        else:\n", "            if x[\"last_trip_enroute_wh_time\"] > 1.3 * x[\"enroute_wh_time_med\"]:\n", "                return \"Vendor\"\n", "            elif (x[\"last_trip_enroute_wh_time\"] <= 1.3 * x[\"enroute_wh_time_med\"]) & (\n", "                x[\"last_trip_ds_stay_time\"] > 120\n", "            ):\n", "                return \"DS\"\n", "            elif x[\"dispatch_delay_last_trip\"] > 0:\n", "                return \"Last Dispatch Delay\"\n", "            else:\n", "                return \"Late Trip\"\n", "    else:\n", "        return \"-\""]}, {"cell_type": "code", "execution_count": null, "id": "2cca1078-426d-4c78-95b1-65a21edabc3b", "metadata": {}, "outputs": [], "source": ["transit2[\"report_delay_attribution\"] = transit2.apply(report_delay_attribute, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "50357896-ad39-4994-8eb3-6feae12e066e", "metadata": {}, "outputs": [], "source": ["transit2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "10d73bce-1e75-427e-a10d-5d642cbae389", "metadata": {}, "outputs": [], "source": ["transit2[\"scheduled_truck_arrival\"] = pd.to_datetime(transit2[\"scheduled_truck_arrival\"])\n", "transit2[\"last_trip_unload_complete_wh\"] = pd.to_datetime(transit2[\"last_trip_unload_complete_wh\"])\n", "transit2[\"loading_start_threshold\"] = pd.to_datetime(transit2[\"loading_start_threshold\"])\n", "transit2[\"dock_assign_time_frwd\"] = pd.to_datetime(transit2[\"dock_assign_time_frwd\"])"]}, {"cell_type": "code", "execution_count": null, "id": "64e7b4d1-78e7-4b45-99b1-5dea91557be1", "metadata": {}, "outputs": [], "source": ["# ## Define Ontime Vehicle Report but Late Load Start Attribution\n", "\n", "\n", "# def load_start_delay_attribute(x):\n", "#     if (x[\"vehicle_report_status\"] == \"reporting_ontime\") & (\n", "#         x[\"load_start_status\"] == \"load_start_delay\"\n", "#     ):\n", "#         if (pd.isna(x[\"scheduled_dispatch_time\"]) == True) | (\n", "#             pd.isna(x[\"dock_assign_time_frwd\"]) == True\n", "#         ):\n", "#             return \"Data NA\"\n", "#         elif (x[\"dock_assign_time_frwd\"] > x[\"loading_start_threshold\"]) & (\n", "#             x[\"dock_issue\"] == \"FC\"\n", "#         ):\n", "#             if x[\"last_trip_unload_complete_wh\"] > x[\"loading_start_threshold\"]:\n", "#                 return \"Dock NA - FC - Late Unloading\"\n", "#             else:\n", "#                 return \"Dock NA - FC - Late Load Start\"\n", "\n", "#         elif (x[\"dock_assign_time_frwd\"] > x[\"loading_start_threshold\"]) & (\n", "#             x[\"dock_issue\"] == \"Fleet\"\n", "#         ):\n", "#             return \"Dock NA - Fleet - Disrupt Last Slot\"\n", "\n", "#         elif x[\"dock_assign_time_frwd\"] <= x[\"loading_start_threshold\"]:\n", "#             return \"Dock Unutilised - Late Load Start\""]}, {"cell_type": "code", "execution_count": null, "id": "b29ab74a-4694-4011-8395-350d0e57d466", "metadata": {}, "outputs": [], "source": ["def load_start_delay_attribute(x):\n", "    if (x[\"vehicle_report_status\"] == \"reporting_ontime\") & (\n", "        x[\"load_start_status\"] == \"load_start_delay\"\n", "    ):\n", "        return \"FC\"\n", "    elif (x[\"vehicle_report_status\"] == \"reporting_ontime\") & (\n", "        x[\"load_start_status\"] == \"load_start_ontime\"\n", "    ):\n", "        return \"NA\"\n", "    else:\n", "        return \"Fleet_(vendor)\""]}, {"cell_type": "code", "execution_count": null, "id": "26a782c9-7bb6-472a-8b26-f671f4c69d25", "metadata": {}, "outputs": [], "source": ["transit2[\"load_start_delay_attribution\"] = transit2.apply(load_start_delay_attribute, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "ff04b07c-0b25-4615-9d89-9a26446e63dc", "metadata": {}, "outputs": [], "source": ["transit2.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "7a67ca6f-9a4a-49af-901c-2edec45b3d51", "metadata": {}, "outputs": [], "source": ["transit2[\n", "    (transit2[\"vehicle_report_status\"] == \"reporting_ontime\")\n", "    & (transit2[\"load_start_status\"] == \"load_start_delay\")\n", "].load_start_delay_attribution.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "4f4b2d9f-94f4-4a9a-954a-37866586b8aa", "metadata": {}, "outputs": [], "source": ["transit2.load_start_delay_attribution.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "32acdbc1-fbe0-4c0b-beec-8d0f639fa33b", "metadata": {}, "outputs": [], "source": ["transit2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c8e129c7-cb8e-4257-b5b5-51ebe5706acd", "metadata": {}, "outputs": [], "source": ["# transit2.drop(columns=[\"dock_issue\", \"docks_in_use\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2808f295-5c62-4b13-b1df-623aac7a7c6e", "metadata": {}, "outputs": [], "source": ["transit2[\"ready_for_dispatch\"] = pd.to_datetime(transit2[\"ready_for_dispatch\"])\n", "transit2[\"exp_dispatch_time\"] = pd.to_datetime(transit2[\"exp_dispatch_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "f40131d8-85a2-4cd4-ad46-bb69ceeb52df", "metadata": {}, "outputs": [], "source": ["## Define Ontime Vehicle Report, Ontime Load Start, but Late Dispatch Attribution\n", "\n", "\n", "def dispatch_delay_attribute(x):\n", "    if (x[\"load_start_status\"] == \"load_start_ontime\") & (\n", "        x[\"dispatch_delay_status\"] == \"dispatch_delay\"\n", "    ):\n", "        if (pd.isna(x[\"exp_dispatch_time\"]) == True) | (pd.isna(x[\"ready_for_dispatch\"]) == True):\n", "            return \"-\"\n", "        elif x[\"ready_for_dispatch\"] < x[\"exp_dispatch_time\"]:\n", "            return \"Delay in Dispatching\"\n", "        elif x[\"ready_for_dispatch\"] >= x[\"exp_dispatch_time\"]:\n", "            return \"Loading Delay\"\n", "    else:\n", "        return \"-\""]}, {"cell_type": "code", "execution_count": null, "id": "db9829d9-c0ed-4cf6-8c5c-fbe271ee68aa", "metadata": {}, "outputs": [], "source": ["transit2[\"dispatch_delay_attribution\"] = transit2.apply(dispatch_delay_attribute, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "400a1e4a-5d15-4b50-b54e-abeca3acca4c", "metadata": {}, "outputs": [], "source": ["transit2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8c21c0e4-2b48-41e9-bb55-3d7cf8d0dd50", "metadata": {}, "outputs": [], "source": ["transit2.shape"]}, {"cell_type": "markdown", "id": "85b63c79-57c3-419f-b681-341fc70e5b62", "metadata": {}, "source": ["### DS Reporting Delay Attribution"]}, {"cell_type": "code", "execution_count": null, "id": "97dec429-8b7d-416f-89d6-22b3092fbb6f", "metadata": {}, "outputs": [], "source": ["conditions = [\n", "    (transit2[\"ds_reach_delay_manual\"] < -60),\n", "    (transit2[\"ds_reach_delay_manual\"] > 60),\n", "    ((transit2[\"ds_reach_delay_manual\"] >= -60) & (transit2[\"ds_reach_delay_manual\"] <= 60)),\n", "    ((transit2[\"ds_reached\"].isnull())),  # Check for null values\n", "    # ((transit2['ds_reach_time'].notnull()) & (transit2['ds_planned_arrival'].isnull()))   # Check for null values\n", "]\n", "\n", "status_conditions = [\"Early\", \"Delay\", \"On-time\", \"Not_reached_DS\"]\n", "transit2[\"ds_reporting_status\"] = np.select(conditions, status_conditions, default=\"Other\")"]}, {"cell_type": "code", "execution_count": null, "id": "eb774fa2-b4c5-4233-8992-d47bc68f4e0b", "metadata": {}, "outputs": [], "source": ["transit2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c33214b5-a70e-442d-8f41-418a62994ff6", "metadata": {}, "outputs": [], "source": ["# transit2[(transit2['wh_outlet_id']==1104) & (transit2['trip_date']=='2024-12-02')].to_csv('frk.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "3247b968-bff0-4f59-8a69-883e99457b37", "metadata": {}, "outputs": [], "source": ["transit2[transit2[\"ds_reach_delay_manual\"] < 0].shape"]}, {"cell_type": "code", "execution_count": null, "id": "9ff46158-bf91-4a5d-9b3b-60aff4d4e9d4", "metadata": {"tags": []}, "outputs": [], "source": ["transit2[\"transit_delay\"] = transit2[\"ds_reach_delay_manual\"] - transit2[\"dispatch_delay_min\"]"]}, {"cell_type": "code", "execution_count": null, "id": "998dd88c-9537-4e6e-a098-612610e11329", "metadata": {}, "outputs": [], "source": ["conditions = [\n", "    (transit2[\"ds_reporting_status\"] == \"Early\"),\n", "    (transit2[\"ds_reporting_status\"] == \"On-time\"),\n", "    (transit2[\"ds_reporting_status\"] == \"Not_reached_DS\"),\n", "    (\n", "        (transit2[\"ds_reporting_status\"] == \"Delay\")\n", "        & (transit2[\"dispatch_delay_min\"] >= transit2[\"transit_delay\"])\n", "    ),\n", "    (\n", "        (transit2[\"ds_reporting_status\"] == \"Delay\")\n", "        & (transit2[\"dispatch_delay_min\"] < transit2[\"transit_delay\"])\n", "    ),\n", "]\n", "status_conditions = [\n", "    \"Early\",\n", "    \"Ontime\",\n", "    \"Not_reached_DS\",\n", "    \"Dispatch_delay\",\n", "    \"Transit_delay\",\n", "]\n", "transit2[\"late_ds_reached_attribution\"] = np.select(conditions, status_conditions, default=\"Other\")"]}, {"cell_type": "code", "execution_count": null, "id": "e9126477-0c2a-41cd-bb8b-23bf8e9e7df9", "metadata": {}, "outputs": [], "source": ["transit2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7e45c5a8-2d8d-4ab6-9e96-0c8e2abb4df3", "metadata": {}, "outputs": [], "source": ["# transit2['trip_type']=np.where(transit2['loading_start_ds'].isnull()==True,'Only forward','Both')"]}, {"cell_type": "code", "execution_count": null, "id": "86eb017a-83f5-4a9e-bda8-84c00bf0ff0d", "metadata": {}, "outputs": [], "source": ["transit2[\"unloading_start_ds\"] = pd.to_datetime(transit2[\"unloading_start_ds\"], errors=\"coerce\")\n", "transit2[\"ds_reached\"] = pd.to_datetime(transit2[\"ds_reached\"], errors=\"coerce\")\n", "transit2[\"unloading_end_ds\"] = pd.to_datetime(transit2[\"unloading_end_ds\"], errors=\"coerce\")\n", "transit2[\"loading_start_ds\"] = pd.to_datetime(transit2[\"loading_start_ds\"], errors=\"coerce\")\n", "transit2[\"loading_end_ds\"] = pd.to_datetime(transit2[\"loading_end_ds\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "b604a07a-a90a-4bbc-b95a-ecb4d8cac270", "metadata": {}, "outputs": [], "source": ["# Calculate ds_exit_time_taken\n", "transit2[\"ds_exit_time_taken\"] = np.where(\n", "    transit2[\"loading_end_ds\"].isnull(),\n", "    (transit2[\"unloading_end_ds\"] - transit2[\"ds_reached\"]),\n", "    (transit2[\"loading_end_ds\"] - transit2[\"ds_reached\"]),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9889c5d3-304e-41a6-90e8-e349e186d436", "metadata": {}, "outputs": [], "source": ["transit2[\"ds_exit_time_taken\"] = transit2[\"ds_exit_time_taken\"].dt.total_seconds() / 60"]}, {"cell_type": "code", "execution_count": null, "id": "8f5e1c07-04d7-46fd-98ee-0e3899497de1", "metadata": {}, "outputs": [], "source": ["def exit_status(df):\n", "    if (df[\"trip_type\"] == \"Milk Run\") & (df[\"ds_exit_time_taken\"] <= 210):\n", "        return \"ontime_exit\"\n", "    elif (df[\"trip_type\"] == \"Milk Run\") & (df[\"ds_exit_time_taken\"] > 210):\n", "        return \"delay_exit\"\n", "    elif (df[\"trip_type\"] == \"Non-Milk Run\") & (df[\"ds_exit_time_taken\"] <= 180):\n", "        return \"ontime_exit\"\n", "    elif (df[\"trip_type\"] == \"Non-Milk Run\") & (df[\"ds_exit_time_taken\"] > 180):\n", "        return \"delay_exit\"\n", "    else:\n", "        return \"NA\"\n", "\n", "\n", "transit2[\"exit_status\"] = transit2.apply(exit_status, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "7feb18e0-2188-475b-bb4f-0f9926e2172a", "metadata": {}, "outputs": [], "source": ["transit2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "884d1784-1b3d-4987-ba1a-f2c4fafdbfd8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1da20921-d74e-462b-9974-58bc06659c08", "metadata": {}, "outputs": [], "source": ["def unloading_status(df):\n", "    if df[\"unload_start_delay_at_ds\"] > 30.00:\n", "        return \"unload_start_delay\"\n", "    elif df[\"unloading_time_at_ds\"] > 180.00:\n", "        return \"unloading_delay\"\n", "    # elif df[\"unload_com_to_load_start_ds\"] > 10.00:\n", "    #     return \"rev_load_start_delay\"\n", "    # elif df[\"loading_time_at_ds\"] > 180.00:\n", "    #     return \"rev_loading_delay\"\n", "    else:\n", "        return \"-\""]}, {"cell_type": "code", "execution_count": null, "id": "8c3a2648-5512-4530-9078-a5e822dd838d", "metadata": {}, "outputs": [], "source": ["transit2[\"reporting_to_exit_attribution_ds\"] = transit2.apply(unloading_status, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "853eb1e9-d829-492a-a68d-fa6805069890", "metadata": {}, "outputs": [], "source": ["transit2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "81a78d9a-8262-4633-9c15-c1098044f7e6", "metadata": {}, "outputs": [], "source": ["transit2 = sqldf(\n", "    \"\"\"\n", "select *,\n", "        case when vehicle_report_status = 'reporting_ontime' \n", "              and ds_reporting_status = 'On-time'\n", "             and dispatch_delay_status = 'dispatch_ontime'\n", "             then 'perfect_trip' \n", "             else 'normal_trip'\n", "        end as trip_success\n", "from transit2\n", "\"\"\"\n", ")"]}, {"cell_type": "markdown", "id": "85da8918-fcce-41f7-8cc0-7b21425bd889", "metadata": {}, "source": ["### GMV Loss due to Truncation Drop"]}, {"cell_type": "markdown", "id": "6ab5b7ea-e3fd-4609-9d9a-3bce4e7c5f78", "metadata": {}, "source": ["#### Take Max of loss value across date and facility ID to report GMV Loss, instead of taking sum"]}, {"cell_type": "code", "execution_count": null, "id": "c62fc359-cee5-4579-b989-c3920a856342", "metadata": {}, "outputs": [], "source": ["# gmv_loss = f\"\"\"\n", "\n", "# with outlet_details as\n", "#     (\n", "#         select  hot_outlet_id as outlet_id,\n", "#                 facility_id,\n", "#                 facility_name\n", "#         from supply_etls.outlet_details\n", "#         group by 1,2,3\n", "#     )\n", "\n", "# select  date_ist as trip_date, od.facility_id,\n", "#         --od.facility_name,\n", "#         --rr.backend_outlet_id,\n", "#         -- od.outlet_id as ds_outlet_id,\n", "#         sum(case when truncation_flag = 'truck_load_drop' and truncation_loss > 0  then gmv_loss_qty end) as gmv_loss_qty_fleet,\n", "#         sum(case when truncation_flag = 'truck_load_drop' and truncation_loss > 0  then gmv_loss_value end) as gmv_loss_value_fleet,\n", "#         sum(gmv_loss_value) as gmv_loss_value\n", "# from supply_etls.store_item_unavailability_reasons rr\n", "# left join outlet_details od on od.outlet_id = rr.backend_outlet_id\n", "# where date_ist >= (current_date - interval '20' day)\n", "# group by 1,2\n", "# \"\"\"\n", "\n", "# gmv_loss_df = read_sql_query(gmv_loss, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "b86ef431-e024-437d-9273-e85fec18d168", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ea89fd2f-d9e2-4be5-b173-9d7e72ab0a96", "metadata": {}, "outputs": [], "source": ["# transit3 = transit2.merge(gmv_loss_df, how=\"left\", on=[\"trip_date\", \"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "fda65cbd-52b5-4ec9-bd75-fbb3cae41685", "metadata": {}, "outputs": [], "source": ["transit3 = transit2\n", "transit3.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a60221c4-9daf-4a72-a9e2-0f313571f492", "metadata": {}, "outputs": [], "source": ["# transit33 = sqldf(\n", "#     \"\"\"\n", "#     select  *,\n", "#             gmv_loss_qty_fleet/count(*) over (partition by trip_date,facility_id) as gmv_loss_qty_fleet_con,\n", "#             gmv_loss_value_fleet/count(*) over (partition by trip_date,facility_id) as gmv_loss_value_fleet_con,\n", "#             gmv_loss_value/count(*) over (partition by trip_date,facility_id) as gmv_loss_value_con\n", "#     from transit3\n", "\n", "# \"\"\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "16f7a3fc-8814-461f-8848-1e49696330ae", "metadata": {}, "outputs": [], "source": ["transit33 = transit3\n", "transit33.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6e9de404-010f-401a-9e41-10fb1f68e01d", "metadata": {}, "outputs": [], "source": ["# transit33.drop(\n", "#     columns=[\"gmv_loss_qty_fleet\", \"gmv_loss_value_fleet\", \"gmv_loss_value\"],\n", "#     inplace=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "6bf633f2-854a-4df5-aa8a-250092f845af", "metadata": {}, "outputs": [], "source": ["transit33.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b125c5fc-9525-4059-88e1-e9f147bfcc6d", "metadata": {}, "outputs": [], "source": ["# transit33.rename(\n", "#     columns={\n", "#         \"gmv_loss_qty_fleet_con\": \"gmv_loss_qty_fleet\",\n", "#         \"gmv_loss_value_fleet_con\": \"gmv_loss_value_fleet\",\n", "#         \"gmv_loss_value_con\": \"gmv_loss_value\",\n", "#     },\n", "#     inplace=True,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "91ba5a54-00af-488a-a5b7-9315c6d20389", "metadata": {}, "outputs": [], "source": ["transit33.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b6ba87e6-c264-4fe2-b626-507c2670f57e", "metadata": {}, "outputs": [], "source": ["# transit33.total_docks.unique()"]}, {"cell_type": "markdown", "id": "9ffa9923-3d3d-4696-99b2-41fa10bf252d", "metadata": {}, "source": ["### CMS - Data related to Cost of each Trip"]}, {"cell_type": "code", "execution_count": null, "id": "b928e4cc-3e2f-4674-b93f-83c90a7e4d35", "metadata": {}, "outputs": [], "source": ["cms = \"\"\"\n", "\n", "SELECT  consignment_id, \n", "        trip_id,\n", "        truck_billing_type,\n", "        total_km_travelled,\n", "        total_hr_travelled,\n", "        fixed_cost,\n", "        misc_charges,\n", "        tolls_cost,\n", "        extra_km_cost,\n", "        extra_hr_cost,\n", "        extra_driver_cost_for_trip,\n", "        total_con_cost\n", "        \n", "        \n", "FROM supply_etls.fleet_cms_cost\n", "where trip_creation_date >= current_date - interval '30' day\n", "  and status = 'APPROVED'\n", "\n", "\"\"\"\n", "cms_df = read_sql_query(cms, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "b7a43eba-8d48-4467-8648-bf318ef0e37c", "metadata": {}, "outputs": [], "source": ["cms_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "894dbf0e-a8c3-4c84-a820-79e86b126259", "metadata": {}, "outputs": [], "source": ["cms_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f34a5488-b1f5-4e3b-8561-6677ec69580e", "metadata": {}, "outputs": [], "source": ["cms_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d62c8841-2fbd-4696-9da9-c2ab223c2de6", "metadata": {}, "outputs": [], "source": ["transit33.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b3c43a70-8b82-497a-9e6a-a7bd4f8f9b8d", "metadata": {}, "outputs": [], "source": ["transit4 = transit33.merge(cms_df, how=\"left\", on=[\"consignment_id\", \"trip_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d570b4f4-b2bb-4cf8-8a39-6c9e02e548e7", "metadata": {}, "outputs": [], "source": ["transit4.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fb3eb6bf-8ab4-49f4-8bf1-cf5fa7f85276", "metadata": {}, "outputs": [], "source": ["transit4.head()"]}, {"cell_type": "markdown", "id": "a8144a0e-b143-4449-9557-20c317e30c2f", "metadata": {}, "source": ["### Vendor Ranking - Current and respective L1 Vendors Rate across each slab"]}, {"cell_type": "code", "execution_count": null, "id": "d6fcfe80-912a-4df3-a101-d845837032bb", "metadata": {}, "outputs": [], "source": ["# vendor_rate_card = f\"\"\"\n", "\n", "# with vendor_rate_card as\n", "#     (\n", "#         with base_charge as (\n", "#             with km_base_charge as (\n", "#                 select distinct\n", "#                     vendor_rate_card_config_id,\n", "#                     vendor_id,\n", "#                     source_node_id,\n", "#                     external_name as source_name,\n", "#                     destination_city,\n", "#                     hs.name as vendor_name,\n", "#                     truck_type,\n", "#                     truck_billing_type,\n", "#                     (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "#                     (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "#                     p.value as km_slab,\n", "#                     p.rule_id,\n", "#                     output as base_cost\n", "\n", "#                 from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "#                 join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "#                 join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "#                 join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "#                 left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "#                 left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "\n", "#                 where vrc.is_active and vrcm.is_active\n", "#                 and attribute_name = 'km'\n", "#                 and rule.rule_name = 'BASE_PAY'\n", "#             ),\n", "\n", "#             hr_base_charge as\n", "#             (\n", "#                 select distinct\n", "#                     vendor_rate_card_config_id,\n", "#                     vendor_id,\n", "#                     source_node_id,\n", "#                     external_name as source_name,\n", "#                     destination_city,\n", "#                     hs.name as vendor_name,\n", "#                     truck_type,\n", "#                     truck_billing_type,\n", "#                     (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "#                     (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "#                     p.value as hr_slab,\n", "#                     p.rule_id,\n", "#                     output as base_cost\n", "\n", "#                 from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "#                 join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "#                 join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "#                 join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "#                 left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "#                 left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "\n", "#                 where vrc.is_active and vrcm.is_active\n", "#                 and attribute_name = 'hour'\n", "#                 and rule.rule_name = 'BASE_PAY'\n", "\n", "#             )\n", "\n", "#             select distinct kb.*, hb.hr_slab\n", "#             from km_base_charge as kb\n", "#             join hr_base_charge as hb\n", "#             on kb.vendor_rate_card_config_id = hb.vendor_rate_card_config_id and kb.base_cost = hb.base_cost and kb.rule_id = hb.rule_id\n", "#         ),\n", "\n", "#         extra_charge as\n", "#         (\n", "#             with km_extra_charge as\n", "#                 (\n", "#                     select\n", "#                         distinct vendor_rate_card_config_id,\n", "#                         vendor_id,\n", "#                         source_node_id,\n", "#                         external_name as source_name,\n", "#                         destination_city,\n", "#                         hs.name as vendor_name,\n", "#                         truck_type,\n", "#                         truck_billing_type,\n", "#                         (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "#                         (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "#                         output as extra_cost_per_km\n", "\n", "#                     from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "#                     join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "#                     join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "#                     left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "#                     left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "\n", "#                     where vrc.is_active and vrcm.is_active\n", "#                     and rule.rule_name = 'ADDITIONAL_COST_PER_KM'\n", "#                 ),\n", "\n", "#             hr_extra_charge as\n", "#                 (\n", "#                     select\n", "#                         distinct vendor_rate_card_config_id,\n", "#                         vendor_id,\n", "#                         source_node_id,\n", "#                         external_name as source_name,\n", "#                         destination_city,\n", "#                         hs.name as vendor_name,\n", "#                         truck_type,\n", "#                         truck_billing_type,\n", "#                         (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "#                         (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "#                         output as extra_cost_per_hr\n", "\n", "#                     from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "#                     join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "#                     join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "#                     left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "#                     left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "\n", "#                     where vrc.is_active and vrcm.is_active\n", "#                     and rule.rule_name = 'ADDITIONAL_COST_PER_HR'\n", "#                 )\n", "\n", "#             select distinct ke.*, he.extra_cost_per_hr\n", "#             from km_extra_charge as ke\n", "#             join hr_extra_charge as he\n", "#             on ke.vendor_rate_card_config_id = he.vendor_rate_card_config_id\n", "#         )\n", "\n", "#         select distinct\n", "#             bc.source_node_id,\n", "#             bc.source_name,\n", "#             case when bc.destination_city in ('Goa','SouthGoa','NorthGoa') then 'Goa' else bc.destination_city end as destination_city,\n", "#             bc.vendor_id,\n", "#             bc.vendor_name,\n", "#             bc.truck_type,\n", "#             bc.truck_billing_type,\n", "#             cast(bc.base_cost as decimal) as base_cost,\n", "#             bc.tenure_start,\n", "#             bc.tenure_end,\n", "#             cast(bc.km_slab as int) as km_slab,\n", "#             cast(bc.hr_slab as int) as hr_slab,\n", "#             ec.extra_cost_per_hr,\n", "#             ec.extra_cost_per_km\n", "\n", "#         from base_charge as bc\n", "#         left join extra_charge as ec on bc.vendor_rate_card_config_id = ec.vendor_rate_card_config_id\n", "#     )\n", "\n", "# select  *\n", "# from vendor_rate_card\n", "# where tenure_start <= current_date + interval '30' day\n", "#   --and tenure_end >= current_date - interval '365' day\n", "\n", "# \"\"\"\n", "\n", "# vendor_rate_card_df = read_sql_query(vendor_rate_card, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "9e08aaff-8234-4242-b1de-d77b15e25f18", "metadata": {}, "outputs": [], "source": ["# vendor_rate_card_df.to_csv(\"vendor_rate_card_df.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4d41e8d0-ef76-4084-b870-fccd46967fe0", "metadata": {}, "outputs": [], "source": ["# vendor_rate_card_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "3ba298ad-d997-48b1-8b21-e1ce3087124e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c876ad44-c17c-4fb4-bf50-e3ac254d3899", "metadata": {}, "outputs": [], "source": ["# ## To get the km slab and hr slab used across any trip for given truck number\n", "\n", "# slab_query = \"\"\"\n", "\n", "# select  distinct truck_number,\n", "#         cast(source_node_id as varchar) as source_node_id,\n", "#         destination_node_id,\n", "#         -- billable_destination_city,\n", "#         truck_billing_type,\n", "#         truck_type,\n", "#         (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "#         (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "#         vendor_id,\n", "#         name as vendor_name,\n", "#         km_slab,\n", "#         hr_slab\n", "\n", "# from fleet_management.fleet_management_truck_vendor_mapping as tvm\n", "# join fleet_management.fleet_management_node_billable_city_mapping as bcm on tvm.id = bcm.truck_vendor_mapping_id\n", "# left join fleet_management.fleet_management_vendor as hs on hs.id = tvm.vendor_id and active = True\n", "# where tvm.is_active= True\n", "#   and bcm.is_active= True\n", "#   and source_node_id is not null\n", "\n", "# \"\"\"\n", "\n", "# slab_df = read_sql_query(slab_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "82b31ba2-aea2-4bcc-9e4d-442e56259cfa", "metadata": {}, "outputs": [], "source": ["# slab_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "26418c2e-1df0-43c6-8ca1-0487194e5345", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "15feb036-67ae-4df0-b8f2-1668a38<PERSON>ba", "metadata": {}, "outputs": [], "source": ["# transit4.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8ff1d4fc-051d-4538-86f3-b2aa030fac8a", "metadata": {}, "outputs": [], "source": ["# transit5 = sqldf(\n", "#     \"\"\"\n", "\n", "#     with base as\n", "#     (\n", "#     select  final.*,\n", "#             km_slab,\n", "#             hr_slab,\n", "#             row_number() over (partition by consignment_id,final.truck_number,final.source_node_id,destination_node_id order by cast(km_slab as int) desc) as num\n", "#     from transit4 final\n", "#     left join slab_df ds\n", "#     on  ds.truck_number=final.truck_number\n", "#     and (final.loading_start >= ds.tenure_start and final.loading_start < ds.tenure_end)\n", "#     and final.ds_node_id = ds.destination_node_id\n", "\n", "#     --------- Also join on source node id\n", "#     and ds.source_node_id = final.source_node_id\n", "#     )\n", "\n", "#     select *\n", "#     from base\n", "#     where num = 1\n", "\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "52cf3e9e-5bff-4de7-a73c-35752d6f9530", "metadata": {}, "outputs": [], "source": ["transit5 = transit4\n", "transit5.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0fe82bfd-96b9-4ad0-89dd-17433ca2e171", "metadata": {}, "outputs": [], "source": ["transit5.head()"]}, {"cell_type": "code", "execution_count": null, "id": "54ccbce4-d91b-4c0d-ac26-cbaa61eba3ee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "62b9a484-d185-4637-8217-3da8b4088a14", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7fb93fe7-a6a1-438b-b001-520fddba38fd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "aee43b4a-b436-44ae-9241-af7c50a6b667", "metadata": {}, "outputs": [], "source": ["# ## Defining the current Vendor Rank and getting base cost of L1 Vendor across same slabs for that trip\n", "\n", "# transit6 = sqldf(\n", "#     \"\"\"\n", "\n", "# with base as\n", "#     (\n", "#     select  final.trip_id,\n", "#             final.consignment_id,\n", "#             final.loading_start,\n", "#             vl.vendor_name,\n", "#             vl.tenure_start,\n", "#             vl.tenure_end,\n", "#             vl.base_cost,\n", "# dense_rank() over (partition by destination_city,final.source_node_id,final.truck_type,final.truck_billing_type,final.km_slab,final.hr_slab order by cast(base_cost as int)) as rnk\n", "#     from transit5 final\n", "#     join vendor_rate_card_df vl\n", "#     on vl.source_node_id=final.source_node_id\n", "#     and lower(vl.destination_city) = lower(final.city)\n", "#     and final.truck_type = vl.truck_type\n", "#     and final.truck_billing_type=vl.truck_billing_type\n", "#     and vl.km_slab = final.km_slab\n", "#     and vl.hr_slab=final.hr_slab\n", "#     and (final.loading_start >= vl.tenure_start and final.loading_start < vl.tenure_end)\n", "#     )\n", "\n", "# select  distinct tt.*,\n", "#         b1.base_cost as current_vendor_base_cost,\n", "#         b1.rnk as current_vendor_rank,\n", "#         b2.base_cost as l1_vendor_base_cost\n", "\n", "# from transit5 tt\n", "# left join base b1 on tt.trip_id = b1.trip_id and tt.consignment_id = b1.consignment_id and tt.vendor_name = b1.vendor_name\n", "# left join\n", "#     (\n", "#     select  distinct trip_id,\n", "#             max(base_cost) as base_cost\n", "#     from base\n", "#     where rnk = 1\n", "#     group by 1\n", "#     ) b2 on tt.trip_id = b2.trip_id\n", "\n", "# \"\"\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "344bbd4f-6f1b-42db-aa3e-18982e1e5880", "metadata": {}, "outputs": [], "source": ["transit6 = transit5\n", "transit6.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c46f2b50-3dcf-4442-aaff-c089281ad1e0", "metadata": {}, "outputs": [], "source": ["# transit6[transit6[\"trip_id\"] == \"25925_1150333\"]"]}, {"cell_type": "code", "execution_count": null, "id": "4bb3e60e-1733-4ab5-b986-f69a58408a7b", "metadata": {}, "outputs": [], "source": ["# transit6.to_csv('transit.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "91ed38f6-99c0-4b94-a937-3b5fd0cd2e5d", "metadata": {}, "outputs": [], "source": ["transit6.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d82f1bd0-6e89-4426-b2ee-c56041cb4cb3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "91ab3226-da24-4f1c-9afe-3f1ccfbf38ca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2954062b-b61c-48b2-808c-33f8b2d2dc24", "metadata": {}, "outputs": [], "source": ["transit6.drop(\n", "    columns=[\n", "        \"last_trip_scheduled_dispatch_time\",\n", "        \"last_trip_ready_for_dispatch\",\n", "        \"days_diff_bw_last_trip\",\n", "    ],\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "377f289f-d9bd-471a-b0e6-b6a6646e6b71", "metadata": {}, "outputs": [], "source": ["# last_trip_scheduled_dispatch_time\n", "# last_trip_ready_for_dispatch\n", "# days_diff_bw_last_trip"]}, {"cell_type": "code", "execution_count": null, "id": "adfc9973-7ec1-42a8-a540-e53a68312d4e", "metadata": {}, "outputs": [], "source": ["transit6.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d89f9c8f-1bef-4701-9d17-df13d2f0e473", "metadata": {}, "outputs": [], "source": ["transit6.head()"]}, {"cell_type": "code", "execution_count": null, "id": "506f376b-3754-45d4-9ca6-784714<PERSON><PERSON>ae", "metadata": {}, "outputs": [], "source": ["transit6[transit6[\"trip_id\"] == \"25925_1150333\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ab43b5ae-975d-4e74-b8bd-9fc8ef877271", "metadata": {}, "outputs": [], "source": ["transit6.columns"]}, {"cell_type": "code", "execution_count": null, "id": "9dfb1de3-0d03-419b-b089-39ef73df9145", "metadata": {}, "outputs": [], "source": ["# transit6[transit6[\"consignment_id\"] == 2714203]"]}, {"cell_type": "code", "execution_count": null, "id": "f1609516-40cd-4132-9c85-1d55b2ef6141", "metadata": {}, "outputs": [], "source": ["# transit6.to_csv(\"transit6.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "b7c6bcba-9288-4d3b-a27a-62690fd9bd59", "metadata": {}, "outputs": [], "source": ["transit6[\"source_node_id\"] = transit6[\"source_node_id\"].fillna(value=0).astype(int)\n", "transit6[\"facility_id\"] = transit6[\"facility_id\"].fillna(value=0).astype(int)\n", "transit6[\"wh_outlet_id\"] = transit6[\"wh_outlet_id\"].fillna(value=0).astype(int)\n", "transit6[\"ds_node_id\"] = transit6[\"ds_node_id\"].fillna(value=0).astype(int)\n", "transit6[\"ds_outlet_id\"] = transit6[\"ds_outlet_id\"].fillna(value=0).astype(int)\n", "transit6[\"vendor_id\"] = transit6[\"vendor_id\"].fillna(value=0).astype(int)\n", "transit6[\"truck_capacity\"] = transit6[\"truck_capacity\"].fillna(value=0).astype(float)\n", "transit6[\"truck_volume\"] = transit6[\"truck_volume\"].fillna(value=0).astype(float)\n", "transit6[\"num_invoices\"] = transit6[\"num_invoices\"].fillna(value=0).astype(int)\n", "transit6[\"exp_crates_dispatched\"] = transit6[\"exp_crates_dispatched\"].fillna(value=0).astype(int)\n", "transit6[\"total_crates_dispatched\"] = (\n", "    transit6[\"total_crates_dispatched\"].fillna(value=0).astype(int)\n", ")\n", "transit6[\"indent\"] = transit6[\"indent\"].fillna(value=0).astype(int)\n", "transit6[\"indent_wt\"] = transit6[\"indent_wt\"].fillna(value=0).astype(float)\n", "transit6[\"indent_volume\"] = transit6[\"indent_volume\"].fillna(value=0).astype(float)\n", "transit6[\"loading_time_at_wh\"] = transit6[\"loading_time_at_wh\"].fillna(value=0).astype(float)\n", "transit6[\"frwd_transit_time\"] = transit6[\"frwd_transit_time\"].fillna(value=0).astype(float)\n", "transit6[\"dispatch_delay_min\"] = transit6[\"dispatch_delay_min\"].fillna(value=0).astype(float)\n", "transit6[\"unload_start_delay_at_ds\"] = (\n", "    transit6[\"unload_start_delay_at_ds\"].fillna(value=0).astype(float)\n", ")\n", "transit6[\"unloading_time_at_ds\"] = transit6[\"unloading_time_at_ds\"].fillna(value=0).astype(float)\n", "transit6[\"unload_com_to_load_start_ds\"] = (\n", "    transit6[\"unload_com_to_load_start_ds\"].fillna(value=0).astype(float)\n", ")\n", "transit6[\"loading_time_at_ds\"] = transit6[\"loading_time_at_ds\"].fillna(value=0).astype(float)\n", "transit6[\"truck_cap_proportion\"] = transit6[\"truck_cap_proportion\"].fillna(value=0).astype(float)\n", "transit6[\"truck_vol_proportion\"] = transit6[\"truck_vol_proportion\"].fillna(value=0).astype(float)\n", "transit6[\"ds_reach_delay_system\"] = transit6[\"ds_reach_delay_system\"].fillna(value=0).astype(float)\n", "transit6[\"ds_reach_delay_manual\"] = transit6[\"ds_reach_delay_manual\"].fillna(value=0).astype(float)\n", "transit6[\"idle_time_checkin_unload_start\"] = (\n", "    transit6[\"idle_time_checkin_unload_start\"].fillna(value=0).astype(float)\n", ")\n", "transit6[\"reporting_delay_min\"] = transit6[\"reporting_delay_min\"].fillna(value=0).astype(float)\n", "transit6[\"dispatch_delay_last_trip\"] = (\n", "    transit6[\"dispatch_delay_last_trip\"].fillna(value=0).astype(float)\n", ")\n", "transit6[\"last_trip_ds_stay_time\"] = (\n", "    transit6[\"last_trip_ds_stay_time\"].fillna(value=0).astype(float)\n", ")\n", "transit6[\"last_trip_enroute_wh_time\"] = (\n", "    transit6[\"last_trip_enroute_wh_time\"].fillna(value=0).astype(float)\n", ")\n", "transit6[\"enroute_wh_time_med\"] = transit6[\"enroute_wh_time_med\"].fillna(value=0).astype(float)\n", "transit6[\"transit_delay\"] = transit6[\"transit_delay\"].fillna(value=0).astype(float)\n", "transit6[\"ds_exit_time_taken\"] = transit6[\"ds_exit_time_taken\"].fillna(value=0).astype(float)\n", "# transit6[\"gmv_loss_qty_fleet\"] = transit6[\"gmv_loss_qty_fleet\"].fillna(value=0).astype(float)\n", "# transit6[\"gmv_loss_value_fleet\"] = transit6[\"gmv_loss_value_fleet\"].fillna(value=0).astype(float)\n", "# transit6[\"gmv_loss_value\"] = transit6[\"gmv_loss_value\"].fillna(value=0).astype(float)\n", "transit6[\"total_km_travelled\"] = transit6[\"total_km_travelled\"].fillna(value=0).astype(float)\n", "transit6[\"total_hr_travelled\"] = transit6[\"total_hr_travelled\"].fillna(value=0).astype(float)\n", "transit6[\"fixed_cost\"] = transit6[\"fixed_cost\"].fillna(value=0).astype(float)\n", "transit6[\"misc_charges\"] = transit6[\"misc_charges\"].fillna(value=0).astype(float)\n", "transit6[\"tolls_cost\"] = transit6[\"tolls_cost\"].fillna(value=0).astype(float)\n", "transit6[\"extra_km_cost\"] = transit6[\"extra_km_cost\"].fillna(value=0).astype(float)\n", "transit6[\"extra_hr_cost\"] = transit6[\"extra_hr_cost\"].fillna(value=0).astype(float)\n", "transit6[\"extra_driver_cost_for_trip\"] = (\n", "    transit6[\"extra_driver_cost_for_trip\"].fillna(value=0).astype(float)\n", ")\n", "transit6[\"total_con_cost\"] = transit6[\"total_con_cost\"].fillna(value=0).astype(float)\n", "# transit6[\"km_slab\"] = transit6[\"km_slab\"].fillna(value=0).astype(int)\n", "# transit6[\"hr_slab\"] = transit6[\"hr_slab\"].fillna(value=0).astype(int)\n", "# transit6[\"current_vendor_base_cost\"] = (\n", "#     transit6[\"current_vendor_base_cost\"].fillna(value=0).astype(float)\n", "# )\n", "# transit6[\"current_vendor_rank\"] = transit6[\"current_vendor_rank\"].fillna(value=0).astype(int)\n", "# transit6[\"l1_vendor_base_cost\"] = transit6[\"l1_vendor_base_cost\"].fillna(value=0).astype(float)\n", "# transit6[\"total_docks\"] = transit6[\"total_docks\"].fillna(value=0).astype(int)\n", "# transit6[\"dock_util\"] = transit6[\"dock_util\"].fillna(value=0).astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "4ebf7a5c-a75f-420d-95fd-a36d18b22dc8", "metadata": {}, "outputs": [], "source": ["transit6[\"trip_date\"] = pd.to_datetime(transit6[\"trip_date\"]).dt.date\n", "transit6[\"reporting_threshold\"] = pd.to_datetime(transit6[\"reporting_threshold\"])\n", "transit6[\"loading_start_threshold\"] = pd.to_datetime(transit6[\"loading_start_threshold\"])\n", "transit6[\"scheduled_truck_arrival\"] = pd.to_datetime(transit6[\"scheduled_truck_arrival\"])\n", "transit6[\"truck_entry_wh\"] = pd.to_datetime(transit6[\"truck_entry_wh\"])\n", "transit6[\"dock_assign_time_frwd\"] = pd.to_datetime(transit6[\"dock_assign_time_frwd\"])\n", "transit6[\"truck_handshake\"] = pd.to_datetime(transit6[\"truck_handshake\"])\n", "transit6[\"loading_start\"] = pd.to_datetime(transit6[\"loading_start\"])\n", "transit6[\"ready_for_dispatch\"] = pd.to_datetime(transit6[\"ready_for_dispatch\"])\n", "transit6[\"enroute\"] = pd.to_datetime(transit6[\"enroute\"])\n", "transit6[\"scheduled_dispatch_time\"] = pd.to_datetime(transit6[\"scheduled_dispatch_time\"])\n", "transit6[\"dispatch_date\"] = pd.to_datetime(transit6[\"dispatch_date\"]).dt.date\n", "transit6[\"exp_dispatch_time\"] = pd.to_datetime(transit6[\"exp_dispatch_time\"])\n", "transit6[\"ds_reached\"] = pd.to_datetime(transit6[\"ds_reached\"])\n", "transit6[\"exp_ds_reach_system\"] = pd.to_datetime(transit6[\"exp_ds_reach_system\"])\n", "transit6[\"exp_ds_reach_manual\"] = pd.to_datetime(transit6[\"exp_ds_reach_manual\"])\n", "transit6[\"unloading_start_ds\"] = pd.to_datetime(transit6[\"unloading_start_ds\"])\n", "transit6[\"unloading_end_ds\"] = pd.to_datetime(transit6[\"unloading_end_ds\"])\n", "transit6[\"loading_start_ds\"] = pd.to_datetime(transit6[\"loading_start_ds\"])\n", "transit6[\"loading_end_ds\"] = pd.to_datetime(transit6[\"loading_end_ds\"])\n", "transit6[\"truck_return_wh\"] = pd.to_datetime(transit6[\"truck_return_wh\"])\n", "transit6[\"completed_ts\"] = pd.to_datetime(transit6[\"completed_ts\"])\n", "transit6[\"last_trip_ds_reached\"] = pd.to_datetime(transit6[\"last_trip_ds_reached\"])\n", "transit6[\"last_trip_unloading_start_ds\"] = pd.to_datetime(transit6[\"last_trip_unloading_start_ds\"])\n", "transit6[\"last_trip_unloading_end_ds\"] = pd.to_datetime(transit6[\"last_trip_unloading_end_ds\"])\n", "transit6[\"last_trip_load_start_ds\"] = pd.to_datetime(transit6[\"last_trip_load_start_ds\"])\n", "transit6[\"last_trip_load_complete_ds\"] = pd.to_datetime(transit6[\"last_trip_load_complete_ds\"])\n", "transit6[\"last_trip_wh_checkin\"] = pd.to_datetime(transit6[\"last_trip_wh_checkin\"])\n", "transit6[\"last_trip_unload_start_wh\"] = pd.to_datetime(transit6[\"last_trip_unload_start_wh\"])\n", "transit6[\"last_trip_unload_complete_wh\"] = pd.to_datetime(transit6[\"last_trip_unload_complete_wh\"])\n", "\n", "\n", "transit6[\"system_distance_in_km\"] = transit6[\"system_distance_in_km\"].astype(float)\n", "transit6[\"system_transit_time_in_hrs\"] = transit6[\"system_transit_time_in_hrs\"].astype(float)\n", "transit6[\"team_recommended_distance_in_km\"] = transit6[\"team_recommended_distance_in_km\"].astype(\n", "    float\n", ")\n", "transit6[\"team_recommended_transit_time_in_hrs\"] = transit6[\n", "    \"team_recommended_transit_time_in_hrs\"\n", "].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "466b288b-043f-4dd7-a146-c5ef64f4df7b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b9d18a2e-4297-4134-903d-36262e13b604", "metadata": {}, "outputs": [], "source": ["# transit6[transit6['trip_id']=='18496_1131785']"]}, {"cell_type": "code", "execution_count": null, "id": "92dedcd0-8121-45fa-be7c-31a259dd7a97", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"consignment_id\", \"type\": \"integer\", \"description\": \"Consignment ID\"},\n", "        {\"name\": \"con_type\", \"type\": \"varchar\", \"description\": \"Consignment type\"},\n", "        {\"name\": \"trip_id\", \"type\": \"varchar\", \"description\": \"Trip Id\"},\n", "        {\"name\": \"trip_date\", \"type\": \"date\", \"description\": \"Trip Date\"},\n", "        {\n", "            \"name\": \"trip_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Trip type milk/non milk run\",\n", "        },\n", "        {\"name\": \"source_node_id\", \"type\": \"integer\", \"description\": \"source_node_id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"WH facility ID\"},\n", "        {\"name\": \"wh_outlet_id\", \"type\": \"integer\", \"description\": \"WH outlet ID\"},\n", "        {\"name\": \"wh_outlet_name\", \"type\": \"varchar\", \"description\": \"WH Outlet Name\"},\n", "        {\"name\": \"ds_node_id\", \"type\": \"integer\", \"description\": \"ds_node_id\"},\n", "        {\"name\": \"ds_outlet_id\", \"type\": \"integer\", \"description\": \"DS outlet id\"},\n", "        {\"name\": \"ds_outlet_name\", \"type\": \"varchar\", \"description\": \"DS outlet name\"},\n", "        {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City\"},\n", "        {\"name\": \"vendor_id\", \"type\": \"integer\", \"description\": \"Vendor Id\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"Vendor name\"},\n", "        {\"name\": \"truck_number\", \"type\": \"varchar\", \"description\": \"truck number\"},\n", "        {\"name\": \"truck_type\", \"type\": \"varchar\", \"description\": \"Truck Type\"},\n", "        {\"name\": \"truck_cold_dry\", \"type\": \"varchar\", \"description\": \"Truck cold/dry\"},\n", "        {\"name\": \"truck_capacity\", \"type\": \"integer\", \"description\": \"Truck Capacity\"},\n", "        {\"name\": \"truck_volume\", \"type\": \"double\", \"description\": \"Truck Volume\"},\n", "        {\"name\": \"trip_state\", \"type\": \"varchar\", \"description\": \"Trip State\"},\n", "        {\n", "            \"name\": \"consignment_state\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Consignment State\",\n", "        },\n", "        {\n", "            \"name\": \"num_invoices\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Number of invoices\",\n", "        },\n", "        {\n", "            \"name\": \"exp_crates_dispatched\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Expected Crates Dispatched\",\n", "        },\n", "        {\n", "            \"name\": \"total_crates_dispatched\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Total Crates Dispatched\",\n", "        },\n", "        {\"name\": \"indent\", \"type\": \"integer\", \"description\": \"Quantity Dispatched\"},\n", "        {\n", "            \"name\": \"indent_wt\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Quantity Dispatched weight\",\n", "        },\n", "        {\"name\": \"indent_volume\", \"type\": \"double\", \"description\": \"Volume Dispatched\"},\n", "        {\n", "            \"name\": \"reporting_threshold\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Reporting Threshold\",\n", "        },\n", "        {\n", "            \"name\": \"loading_start_threshold\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"loading_start_threshold\",\n", "        },\n", "        {\n", "            \"name\": \"scheduled_truck_arrival\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"scheduled truck arrival\",\n", "        },\n", "        {\n", "            \"name\": \"truck_entry_wh\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"truck entry wh\",\n", "        },\n", "        {\n", "            \"name\": \"dock_assign_time_frwd\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"dock_assign_time_frwd\",\n", "        },\n", "        {\n", "            \"name\": \"loading_dock_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"loading_dock_name\",\n", "        },\n", "        {\n", "            \"name\": \"truck_handshake\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"truck handshake\",\n", "        },\n", "        {\n", "            \"name\": \"loading_start\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"loading start wh\",\n", "        },\n", "        {\n", "            \"name\": \"ready_for_dispatch\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"ready for dispatch\",\n", "        },\n", "        {\"name\": \"enroute\", \"type\": \"TIMESTAMP(6)\", \"description\": \"enroute\"},\n", "        {\n", "            \"name\": \"scheduled_dispatch_time\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"scheduled dispatch time\",\n", "        },\n", "        {\n", "            \"name\": \"dispatch_time\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"scheduled dispatch time\",\n", "        },\n", "        {\n", "            \"name\": \"dispatch_date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"scheduled dispatch time\",\n", "        },\n", "        {\n", "            \"name\": \"exp_dispatch_time\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"expected dispatch time\",\n", "        },\n", "        {\"name\": \"ds_reached\", \"type\": \"TIMESTAMP(6)\", \"description\": \"ds reached\"},\n", "        {\n", "            \"name\": \"exp_ds_reach_system\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"expected  ds arrival\",\n", "        },\n", "        {\n", "            \"name\": \"exp_ds_reach_manual\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"exp_ds_reach_manual\",\n", "        },\n", "        {\n", "            \"name\": \"unloading_start_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"unloading start ds\",\n", "        },\n", "        {\n", "            \"name\": \"unloading_end_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"unloading end ds\",\n", "        },\n", "        # {\n", "        #     \"name\": \"rev_consignment_id\",\n", "        #     \"type\": \"varchar\",\n", "        #     \"description\": \"Reverse Consignment ID\",\n", "        # },\n", "        {\n", "            \"name\": \"loading_start_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"loading start ds\",\n", "        },\n", "        {\n", "            \"name\": \"loading_end_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"loading end ds\",\n", "        },\n", "        {\n", "            \"name\": \"truck_return_wh\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"truck return wh\",\n", "        },\n", "        {\n", "            \"name\": \"completed_ts\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"completed ts\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Last Trip ID\",\n", "        },\n", "        {\n", "            \"name\": \"loading_time_at_wh\",\n", "            \"type\": \"double\",\n", "            \"description\": \"loading_time_at_wh\",\n", "        },\n", "        {\n", "            \"name\": \"load_start_status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"load_start_status\",\n", "        },\n", "        {\n", "            \"name\": \"frwd_transit_time\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"frwd_transit_time\",\n", "        },\n", "        {\n", "            \"name\": \"dispatch_delay_min\",\n", "            \"type\": \"double\",\n", "            \"description\": \"dispatch_delay_min\",\n", "        },\n", "        {\n", "            \"name\": \"dispatch_delay_status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"dispatch_delay_status\",\n", "        },\n", "        {\n", "            \"name\": \"unload_start_delay_at_ds\",\n", "            \"type\": \"double\",\n", "            \"description\": \"unload_start_delay_at_ds\",\n", "        },\n", "        {\n", "            \"name\": \"unloading_time_at_ds\",\n", "            \"type\": \"double\",\n", "            \"description\": \"unloading_time_at_ds\",\n", "        },\n", "        {\n", "            \"name\": \"unload_com_to_load_start_ds\",\n", "            \"type\": \"double\",\n", "            \"description\": \"unload_com_to_load_start_ds\",\n", "        },\n", "        {\n", "            \"name\": \"loading_time_at_ds\",\n", "            \"type\": \"double\",\n", "            \"description\": \"loading_time_at_ds\",\n", "        },\n", "        {\"name\": \"dispatch_slot\", \"type\": \"varchar\", \"description\": \"dispatch_slot\"},\n", "        {\n", "            \"name\": \"frwd_enroute_breach_status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"frwd_enroute_breach_status\",\n", "        },\n", "        {\n", "            \"name\": \"system_distance_in_km\",\n", "            \"type\": \"real\",\n", "            \"description\": \"system_distance_in_km\",\n", "        },\n", "        {\n", "            \"name\": \"system_transit_time_in_hrs\",\n", "            \"type\": \"real\",\n", "            \"description\": \"system_transit_time_in_hrs\",\n", "        },\n", "        {\n", "            \"name\": \"team_recommended_distance_in_km\",\n", "            \"type\": \"real\",\n", "            \"description\": \"team_recommended_distance_in_km\",\n", "        },\n", "        {\n", "            \"name\": \"team_recommended_transit_time_in_hrs\",\n", "            \"type\": \"real\",\n", "            \"description\": \"team_recommended_transit_time_in_hrs\",\n", "        },\n", "        {\n", "            \"name\": \"truck_cap_proportion\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"truck_cap_proportion\",\n", "        },\n", "        {\n", "            \"name\": \"truck_vol_proportion\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"truck_vol_proportion\",\n", "        },\n", "        {\n", "            \"name\": \"ds_reach_delay_system\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"ds_reach_delay_system\",\n", "        },\n", "        {\n", "            \"name\": \"ds_reach_delay_manual\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"ds_reach_delay_manual\",\n", "        },\n", "        {\"name\": \"month\", \"type\": \"varchar\", \"description\": \"month\"},\n", "        {\n", "            \"name\": \"last_trip_ds_reached\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_ds_reached\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_unloading_start_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_unloading_start_ds\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_unloading_end_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_unloading_end_ds\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_load_start_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_load_start_ds\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_load_complete_ds\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_load_complete_ds\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_wh_checkin\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_wh_checkin\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_unload_start_wh\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_unload_start_wh\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_unload_complete_wh\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"last_trip_unload_complete_wh\",\n", "        },\n", "        {\n", "            \"name\": \"idle_time_checkin_unload_start\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"idle_time_checkin_unload_start\",\n", "        },\n", "        {\n", "            \"name\": \"idle_time_unload_comp_load_start\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"idle_time_unload_comp_load_start\",\n", "        },\n", "        {\n", "            \"name\": \"vehicle_report_status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"vehicle_report_status\",\n", "        },\n", "        {\n", "            \"name\": \"reporting_delay_min\",\n", "            \"type\": \"double\",\n", "            \"description\": \"reporting_delay_min\",\n", "        },\n", "        {\n", "            \"name\": \"dispatch_delay_last_trip\",\n", "            \"type\": \"double\",\n", "            \"description\": \"dispatch_delay_last_trip\",\n", "        },\n", "        {\n", "            \"name\": \"dispatch_status_last_trip\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"dispatch_status_last_trip\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_ds_stay_time\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"last_trip_ds_stay_time\",\n", "        },\n", "        {\n", "            \"name\": \"last_trip_enroute_wh_time\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"last_trip_enroute_wh_time\",\n", "        },\n", "        {\"name\": \"trip_success\", \"type\": \"varchar\", \"description\": \"trip_success\"},\n", "        # {\n", "        #     \"name\": \"total_docks\",\n", "        #     \"type\": \"integer\",\n", "        #     \"description\": \"Total dock for outbound\",\n", "        # },\n", "        # {\"name\": \"dock_util\", \"type\": \"DOUBLE\", \"description\": \"Dock Utilization\"},\n", "        {\n", "            \"name\": \"enroute_wh_time_med\",\n", "            \"type\": \"double\",\n", "            \"description\": \"enroute_wh_time_med\",\n", "        },\n", "        {\n", "            \"name\": \"report_delay_attribution\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"report_delay_attribution\",\n", "        },\n", "        {\n", "            \"name\": \"load_start_delay_attribution\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"load_start_delay_attribution\",\n", "        },\n", "        {\n", "            \"name\": \"dispatch_delay_attribution\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"dispatch_delay_attribution\",\n", "        },\n", "        {\n", "            \"name\": \"ds_reporting_status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"ds_reporting_status\",\n", "        },\n", "        {\"name\": \"transit_delay\", \"type\": \"double\", \"description\": \"transit_delay\"},\n", "        {\n", "            \"name\": \"late_ds_reached_attribution\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"late_ds_reached_attribution\",\n", "        },\n", "        {\n", "            \"name\": \"ds_exit_time_taken\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"ds_exit_time_taken\",\n", "        },\n", "        {\"name\": \"exit_status\", \"type\": \"varchar\", \"description\": \"exit_status\"},\n", "        {\n", "            \"name\": \"reporting_to_exit_attribution_ds\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"reporting_to_exit_attribution_ds\",\n", "        },\n", "        # {\n", "        #     \"name\": \"gmv_loss_qty_fleet\",\n", "        #     \"type\": \"DOUBLE\",\n", "        #     \"description\": \"gmv_loss_qty_fleet\",\n", "        # },\n", "        # {\n", "        #     \"name\": \"gmv_loss_value_fleet\",\n", "        #     \"type\": \"DOUBLE\",\n", "        #     \"description\": \"gmv_loss_value_fleet\",\n", "        # },\n", "        # {\"name\": \"gmv_loss_value\", \"type\": \"DOUBLE\", \"description\": \"gmv_loss_value\"},\n", "        {\n", "            \"name\": \"truck_billing_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"truck_billing_type\",\n", "        },\n", "        {\n", "            \"name\": \"total_km_travelled\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_km_travelled\",\n", "        },\n", "        {\n", "            \"name\": \"total_hr_travelled\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_hr_travelled\",\n", "        },\n", "        {\"name\": \"fixed_cost\", \"type\": \"double\", \"description\": \"fixed_cost\"},\n", "        {\"name\": \"misc_charges\", \"type\": \"double\", \"description\": \"misc_charges\"},\n", "        {\"name\": \"tolls_cost\", \"type\": \"double\", \"description\": \"tolls_cost\"},\n", "        {\"name\": \"extra_km_cost\", \"type\": \"double\", \"description\": \"extra_km_cost\"},\n", "        {\"name\": \"extra_hr_cost\", \"type\": \"double\", \"description\": \"extra_hr_cost\"},\n", "        {\n", "            \"name\": \"extra_driver_cost_for_trip\",\n", "            \"type\": \"double\",\n", "            \"description\": \"extra_driver_cost_for_trip\",\n", "        },\n", "        {\"name\": \"total_con_cost\", \"type\": \"double\", \"description\": \"total_con_cost\"},\n", "        # {\"name\": \"km_slab\", \"type\": \"integer\", \"description\": \"km_slab\"},\n", "        # {\"name\": \"hr_slab\", \"type\": \"integer\", \"description\": \"hr_slab\"},\n", "        # {\n", "        #     \"name\": \"current_vendor_base_cost\",\n", "        #     \"type\": \"double\",\n", "        #     \"description\": \"current_vendor_base_cost\",\n", "        # },\n", "        # {\n", "        #     \"name\": \"current_vendor_rank\",\n", "        #     \"type\": \"integer\",\n", "        #     \"description\": \"current_vendor_rank\",\n", "        # },\n", "        # {\n", "        #     \"name\": \"l1_vendor_base_cost\",\n", "        #     \"type\": \"double\",\n", "        #     \"description\": \"l1_vendor_base_cost\",\n", "        # },\n", "    ],\n", "    \"primary_key\": [\"consignment_id\", \"trip_id\", \"trip_date\", \"ds_outlet_id\"],  # list\n", "    \"partition_key\": [\"trip_date\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"trip_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"Fleet Master table\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(transit6, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "230cbaf4-5cec-4d17-acf3-082bd35bd799", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "60e1d2a5-09c8-4bc7-8291-ec7912194ef4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af04c026-6906-497c-820a-a7ddf31d32f2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f9cf413c-0621-4f1b-9981-ea118dd26233", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "256988e9-6aeb-4be3-9b29-d56a01f1bb92", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af40d80d-3773-473a-9f15-87daf3ad0946", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}