{"cells": [{"cell_type": "code", "execution_count": null, "id": "714e844c-a496-425a-a1a0-ad40d2779b84", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import date, timedelta\n", "import requests\n", "import json\n", "from tqdm.notebook import tqdm\n", "from concurrent.futures import ThreadPoolExecutor\n", "import time\n", "\n", "tqdm.pandas()"]}, {"cell_type": "code", "execution_count": null, "id": "73af8a41-f651-432c-b4f8-82b19d39959e", "metadata": {}, "outputs": [], "source": ["redshift_schema_name = \"metrics\"\n", "redshift_table_name = \"fleet_ds_to_ds_distance\""]}, {"cell_type": "code", "execution_count": null, "id": "a8d21195-caf3-4a56-b525-35a99ddbd313", "metadata": {}, "outputs": [], "source": ["# Connecting with Redshift\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "id": "1eb2e8b8-8b61-4792-9727-e3de8bbec8bc", "metadata": {}, "source": ["### fetching old DS"]}, {"cell_type": "code", "execution_count": null, "id": "8ad9bdfc-18bf-4e5b-a9bb-05257d914533", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct ds_outlet_id\n", "from \n", "(\n", "    select source_ds_outlet_id as ds_outlet_id\n", "    from metrics.fleet_ds_to_ds_distance\n", "    union\n", "    select dest_ds_outlet_id as ds_outlet_id\n", "    from metrics.fleet_ds_to_ds_distance\n", ")\n", "\"\"\"\n", "old_ds = pd.read_sql_query(sql=query, con=redshift_connection)"]}, {"cell_type": "markdown", "id": "aa692c2a-ec80-4f90-a33b-0081808729f3", "metadata": {}, "source": ["### Fetching DS coords"]}, {"cell_type": "code", "execution_count": null, "id": "39758478-3adf-4f19-b405-3e11f4f1efe3", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct node.external_id as frontend_merchant_id,\n", "--node.type,\n", "x.outlet_id,\n", "y.facility_id,\n", "y.name, \n", "latitude::text, \n", "longitude::text\n", "\n", "from lake_transit_server.transit_node node \n", "join lake_transit_server.transit_node_address add on node.node_address_id = add.id\n", "\n", "join (\n", "    select distinct logistic_node_id, outlet_id, name\n", "    from lake_retail.console_outlet_logistic_mapping\n", "    where active =true\n", ") as x\n", "\n", "ON x.logistic_node_id = node.external_id \n", "\n", " join (\n", "\n", "    select distinct \n", "        id, \n", "        facility_id,\n", "        name\n", "        \n", "    from lake_retail.console_outlet \n", "    where active =1\n", "    and facility_id is not null\n", "    and id is not null\n", "    and id!= 1638\n", "    -- and facility_id!= 806\n", "    and business_type_id = 7\n", "    \n", ") as y\n", "\n", "on y.id = x.outlet_id\n", "\n", "where node.type not in ('CPC','FC','PC')\n", "and not (frontend_merchant_id= 32360 and outlet_id= 3702)\n", "and not (frontend_merchant_id= 32210 and outlet_id= 4252)\n", "and not (frontend_merchant_id= 31015 and outlet_id= 2805)\n", "-- and node.active = true\n", "\n", "\"\"\"\n", "\n", "ds_coord_df = pd.read_sql_query(sql=query, con=redshift_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "048f6f15-bacf-416b-ba19-550737650bca", "metadata": {}, "outputs": [], "source": ["ds_coord_df.head()"]}, {"cell_type": "markdown", "id": "6856c050-1751-4150-91ee-c1ac6087679c", "metadata": {}, "source": ["### finding the new opened DS"]}, {"cell_type": "code", "execution_count": null, "id": "e9e6517b-1b60-4e72-b0cb-40e65d3d6171", "metadata": {}, "outputs": [], "source": ["unique_ids = old_ds[\"ds_outlet_id\"].unique()\n", "df_coord_df_temp = ds_coord_df[~ds_coord_df[\"outlet_id\"].isin(unique_ids)]\n", "\n", "new_ds_ids = df_coord_df_temp[\"outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "cc58d4a6-8c21-4a02-b222-f8d4f7f1f256", "metadata": {"tags": []}, "outputs": [], "source": ["base_df = ds_coord_df.merge(ds_coord_df, how=\"cross\")\n", "base_df.columns = [\n", "    \"source_ds_merchant_id\",\n", "    \"source_ds_outlet_id\",\n", "    \"source_ds_facility_id\",\n", "    \"source_ds_name\",\n", "    \"source_ds_lat\",\n", "    \"source_ds_lon\",\n", "    \"dest_ds_merchant_id\",\n", "    \"dest_ds_outlet_id\",\n", "    \"dest_ds_facility_id\",\n", "    \"dest_ds_name\",\n", "    \"dest_ds_lat\",\n", "    \"dest_ds_lon\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "448e64b7-3177-413f-8be8-a1174b541c40", "metadata": {}, "outputs": [], "source": ["base_df[\"source_lat_lon\"] = (\n", "    \"[\" + base_df[\"source_ds_lat\"] + \",\" + base_df[\"source_ds_lon\"] + \"]\"\n", ")\n", "base_df[\"dest_lat_lon\"] = (\n", "    \"[\" + base_df[\"dest_ds_lat\"] + \",\" + base_df[\"dest_ds_lon\"] + \"]\"\n", ")\n", "base_df[\"combined\"] = (\n", "    \"[\" + base_df[\"source_lat_lon\"] + \",\" + base_df[\"dest_lat_lon\"] + \"]\"\n", ")\n", "base_df[\"combined\"] = base_df[\"combined\"].apply(lambda x: json.loads(x))\n", "base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0d72774a-a700-4369-a36b-c3d1b5faca05", "metadata": {"tags": []}, "outputs": [], "source": ["OSRM_URL = \"http://osrm-service-data.prod-sgp-k8s.grofer.io\"\n", "\n", "\n", "def osrm_table_url_formatter(coords, url, profile=\"car\"):\n", "    statement = (\n", "        url + \"/table/v1/\" + profile + \"/\" + coords + \"?annotations=distance,duration\"\n", "    )\n", "\n", "    try:\n", "        r = requests.get(statement)\n", "        # time.sleep(0.04)\n", "    except Exception:\n", "        raise Exception(\"Spin up the OSRM server on \", url)\n", "    return r.json()\n", "\n", "\n", "def get_inter_order_distance(locations):\n", "    # source is warehouse\n", "    source = locations[0]\n", "    # destination is darkstore\n", "    destination = locations[1]\n", "\n", "    coords = f\"{source[1]},{source[0]};{destination[1]},{destination[0]}\"\n", "    response = osrm_table_url_formatter(coords, OSRM_URL, \"car\")\n", "\n", "    try:\n", "        distance = response[\"distances\"][0][1]\n", "        duration = response[\"durations\"][0][1]\n", "    except:\n", "        return 0\n", "\n", "    return distance / 1000, duration / 60"]}, {"cell_type": "code", "execution_count": null, "id": "fd9fc751-468b-4397-8dd0-3aa431c02f1c", "metadata": {"tags": []}, "outputs": [], "source": ["base_df.sort_values([\"source_ds_merchant_id\", \"dest_ds_merchant_id\"], inplace=True)"]}, {"cell_type": "markdown", "id": "c5b29695-d6c3-4f65-85a0-d6d15715f5a0", "metadata": {}, "source": ["### removing data that is already present"]}, {"cell_type": "code", "execution_count": null, "id": "272fe126-4bc3-4c22-8b49-d043c1c5bc16", "metadata": {}, "outputs": [], "source": ["base_df = base_df[\n", "    base_df[\"source_ds_outlet_id\"].isin(new_ds_ids)\n", "    | base_df[\"dest_ds_outlet_id\"].isin(new_ds_ids)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "dbb2c4d6-3e23-4ad8-82ef-170eb8d53828", "metadata": {"tags": []}, "outputs": [], "source": ["base_df[[\"distance_km\", \"time_min\"]] = (\n", "    base_df[\"combined\"].progress_apply(get_inter_order_distance).apply(pd.Series)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "489affe6-cb2a-4974-8d68-fac562b1836f", "metadata": {}, "outputs": [], "source": ["base_df = base_df[\n", "    [\n", "        \"source_ds_merchant_id\",\n", "        \"source_ds_outlet_id\",\n", "        \"source_ds_facility_id\",\n", "        \"source_ds_name\",\n", "        \"source_ds_lat\",\n", "        \"source_ds_lon\",\n", "        \"dest_ds_merchant_id\",\n", "        \"dest_ds_outlet_id\",\n", "        \"dest_ds_facility_id\",\n", "        \"dest_ds_name\",\n", "        \"dest_ds_lat\",\n", "        \"dest_ds_lon\",\n", "        \"distance_km\",\n", "        \"time_min\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "70a74e23-1339-4f2e-a5ac-65d70d8b2315", "metadata": {}, "outputs": [], "source": ["base_df = base_df.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "11368c82-5dc1-4f5c-8124-54ea746c1c30", "metadata": {"tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": redshift_schema_name,\n", "    \"table_name\": redshift_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"source_ds_merchant_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"source_ds_merchant_id\",\n", "        },\n", "        {\n", "            \"name\": \"source_ds_outlet_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"source_ds_outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"source_ds_facility_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"source_ds_facility_id\",\n", "        },\n", "        {\"name\": \"source_ds_name\", \"type\": \"varchar\", \"description\": \"source_ds_name\"},\n", "        {\"name\": \"source_ds_lat\", \"type\": \"float\", \"description\": \"source_ds_lat\"},\n", "        {\"name\": \"source_ds_lon\", \"type\": \"float\", \"description\": \"source_ds_lon\"},\n", "        {\n", "            \"name\": \"dest_ds_merchant_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"dest_ds_merchant_id\",\n", "        },\n", "        {\n", "            \"name\": \"dest_ds_outlet_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"dest_ds_outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"dest_ds_facility_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"dest_ds_facility_id\",\n", "        },\n", "        {\"name\": \"dest_ds_name\", \"type\": \"varchar\", \"description\": \"dest_ds_name\"},\n", "        {\"name\": \"dest_ds_lat\", \"type\": \"float\", \"description\": \"dest_ds_lat\"},\n", "        {\"name\": \"dest_ds_lon\", \"type\": \"float\", \"description\": \"dest_ds_lon\"},\n", "        {\n", "            \"name\": \"distance_km\",\n", "            \"type\": \"float\",\n", "            \"description\": \"distance between ds and ds in km\",\n", "        },\n", "        {\n", "            \"name\": \"time_min\",\n", "            \"type\": \"float\",\n", "            \"description\": \"time from wds and ds in min\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"source_ds_outlet_id\", \"dest_ds_outlet_id\"],\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"This table contains distance & duration between ds and ds\"\"\",\n", "}\n", "\n", "print(\"pushing to redshift\")\n", "\n", "pb.to_redshift(base_df, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "f1b08baf-46b8-4d9b-b51c-9addb942cfb9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}