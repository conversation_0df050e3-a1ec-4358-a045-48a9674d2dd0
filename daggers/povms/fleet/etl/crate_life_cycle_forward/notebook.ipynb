{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import json\n", "import pytz\n", "import os\n", "import glob\n", "import re\n", "import warnings\n", "import ast\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e3656a0f-6b7f-4a20-a547-6db3b26ded88", "metadata": {}, "outputs": [], "source": ["logistic_mapping_query = f\"\"\"\n", "\n", "select distinct outlet_id, logistic_node_id\n", "from retail.console_outlet_logistic_mapping\n", "where lake_active_record = true\n", "\n", "\"\"\"\n", "\n", "logistic_mapping_df = pd.read_sql_query(sql=logistic_mapping_query, con=trino_connection)\n", "logistic_mapping_df = logistic_mapping_df.astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "12a3e792-b29a-42fd-8755-bbc649fb4e37", "metadata": {}, "outputs": [], "source": ["outlet_name_query = f\"\"\"\n", "select  cast(id as varchar) as id,max(name) as name,\n", "case \n", "    when (max(business_type_id) = 1 or max(business_type_id) = 12) then 'FC'\n", "    when max(business_type_id) = 20 then 'PC'\n", "    when max(business_type_id) = 19 then 'CPC'\n", "    when max(business_type_id) = 21 then 'HP'\n", "    else ''\n", "end as wh_type\n", "from retail.console_outlet\n", "group by 1\n", "\"\"\"\n", "\n", "outlet_name_df = pd.read_sql_query(sql=outlet_name_query, con=trino_connection)"]}, {"cell_type": "markdown", "id": "0e30d2c0-9705-42ec-a5ae-cd2cd0d63003", "metadata": {}, "source": ["### excess crates"]}, {"cell_type": "code", "execution_count": null, "id": "4b211f57-2ba3-4739-bef2-8c822bc6a2dc", "metadata": {"tags": []}, "outputs": [], "source": ["excess_query = f\"\"\"\n", "select\n", "    t.id as consignment_id,\n", "    t.destination_store_id,\n", "    json_extract(t.metadata,'$.excess_crate_ids') as excess_crates,\n", "    (max(c.install_ts) + interval '330' MINUTE) as wrong_unload_ts\n", "      \n", "\n", "FROM transit_server.transit_consignment_container c\n", "JOIN transit_server.transit_consignment t ON t.id = c.consignment_id\n", "\n", "WHERE c.state = 'UNLOADED_AT_DESTINATION'\n", "AND (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "and t.insert_ds_ist >= cast((current_date - interval '7' DAY) as varchar)\n", "and c.insert_ds_ist >= cast((current_date - interval '7' DAY) as varchar)\n", "and json_extract(t.metadata,'$.excess_crate_ids') is not null\n", "\n", "group by 1,2,3\n", "\"\"\"\n", "\n", "excess_df = pd.read_sql_query(sql=excess_query, con=trino_connection)\n", "excess_df[\"excess_crates\"] = excess_df[\"excess_crates\"].apply(ast.literal_eval)\n", "excess_df = excess_df.explode(\"excess_crates\").reset_index(drop=True)\n", "excess_df = excess_df.sort_values(\n", "    [\"consignment_id\", \"destination_store_id\", \"excess_crates\", \"wrong_unload_ts\"]\n", ").reset_index(drop=True)\n", "\n", "excess_df = excess_df.merge(\n", "    logistic_mapping_df,\n", "    how=\"left\",\n", "    left_on=\"destination_store_id\",\n", "    right_on=\"logistic_node_id\",\n", ")\n", "excess_df = excess_df.rename(columns={\"outlet_id\": \"ds_outlet_id\"})\n", "excess_df = excess_df.drop(\"logistic_node_id\", axis=1)"]}, {"cell_type": "markdown", "id": "32bdf011-a0cd-4ae8-b221-cbfd673aef98", "metadata": {}, "source": ["### B2B scan data"]}, {"cell_type": "code", "execution_count": null, "id": "543ed275-567c-4f01-aabf-7b264c92c8e3", "metadata": {}, "outputs": [], "source": ["b2b_query = f\"\"\"\n", "\n", "select distinct\n", "    (pic.pos_timestamp + interval '330' MINUTE) as b2b_scan_ts,\n", "    pic.crate_ids as crate_id,\n", "    pic.invoice_id,\n", "    pi.original_invoice_id,\n", "    tc.consignment_id,\n", "    pi.outlet_id as wh_outlet_id\n", "    \n", "from \n", "    pos.pos_invoice_crates_update_logs as pic\n", "join \n", "    pos.pos_invoice as pi\n", "    on pic.invoice_id = pi.id\n", "    and pi.insert_ds_ist >= cast((current_date - interval '14' day) as varchar)\n", "    \n", "left join \n", "    transit_server.transit_consignment_document as tc\n", "    on tc.external_id = pi.original_invoice_id \n", "    and tc.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "   \n", "\n", "where pic.insert_ds_ist >= cast((current_date - interval '7' day) as varchar)\n", "\n", "\"\"\"\n", "\n", "b2b_df = pd.read_sql_query(sql=b2b_query, con=trino_connection)\n", "b2b_df = b2b_df.sort_values(\n", "    [\"b2b_scan_ts\", \"crate_id\", \"original_invoice_id\", \"consignment_id\"]\n", ").reset_index(drop=True)\n", "\n", "\n", "def pick_row(group):\n", "    if len(group) > 1:\n", "        return group.iloc[1]\n", "    else:\n", "        return group.iloc[0]\n", "\n", "\n", "b2b_df = (\n", "    b2b_df.groupby([\"b2b_scan_ts\", \"crate_id\", \"invoice_id\", \"original_invoice_id\"])\n", "    .apply(pick_row)\n", "    .reset_index(drop=True)\n", ")\n", "b2b_df = (\n", "    b2b_df[[\"b2b_scan_ts\", \"crate_id\", \"consignment_id\", \"wh_outlet_id\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "b2b_df[\"wh_outlet_id\"] = b2b_df[\"wh_outlet_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "fad4dce1-feec-47d0-8d11-2828c3af0ed8", "metadata": {}, "outputs": [], "source": ["billed_dispatch_query = f\"\"\"\n", "\n", "select distinct\n", "    cast((ec.created_at + interval '330' MINUTE) as date) AS billing_date,\n", "    (ec.created_at + interval '330' MINUTE) as billed_ts,\n", "    (ec.updated_at + interval '330' MINUTE) as dispatch_ts,\n", "    ec.outlet_id as wh_outlet_id,\n", "    ec.state,\n", "    ec.container_id as crate_id,\n", "    s.frontend_outlet_id as ds_outlet_id,\n", "    ec.billing_entity_id,\n", "    ec.billing_entity_type\n", "    \n", "    \n", "from \n", "    wms.outbound_container as ec --billed\n", "join \n", "    wms.outbound_invoice as ei\n", "    on ec.billing_entity_id = ei.billing_entity_id and ec.billing_entity_type = ei.billing_entity_type and ec.outlet_id = ei.outlet_id\n", "join \n", "    pos.pos_invoice as pi\n", "on ei.invoice_id = pi.invoice_id \n", "and pi.invoice_type_id in (5,14,16)\n", "and pi.insert_ds_ist >= cast((current_date - interval '7' DAY) as varchar)\n", "and ei.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", "\n", "join \n", "    po.sto as s \n", "    on s.id = CAST(pi.grofers_order_id AS int)\n", "\n", "where ec.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", "and ec.state in ('BILLED', 'DISPATCHED','MISSING','SORTED')\n", "\"\"\"\n", "\n", "billed_dispatch_df = pd.read_sql_query(sql=billed_dispatch_query, con=trino_connection)\n", "billed_dispatch_df[[\"wh_outlet_id\", \"ds_outlet_id\"]] = billed_dispatch_df[\n", "    [\"wh_outlet_id\", \"ds_outlet_id\"]\n", "].astype(str)\n", "billed_dispatch_df = billed_dispatch_df[\n", "    ~billed_dispatch_df[\"ds_outlet_id\"].isin(\n", "        [\n", "            \"337\",\n", "            \"481\",\n", "            \"714\",\n", "            \"1104\",\n", "            \"1577\",\n", "            \"1644\",\n", "            \"1725\",\n", "            \"2653\",\n", "            \"2904\",\n", "            \"4165\",\n", "            \"4170\",\n", "            \"4283\",\n", "            \"4306\",\n", "            \"4325\",\n", "            \"4330\",\n", "            \"4375\",\n", "        ]\n", "    )\n", "].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e0e70db5-0d27-4347-a119-af1727828510", "metadata": {}, "outputs": [], "source": ["crate_to_consignment_query = f\"\"\"\n", "with base as (\n", "select distinct \n", "    c.state,\n", "    c.external_id AS crate_id,\n", "    c.consignment_id,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.id') as varchar) as billing_entity_id,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.type') as varchar) as billing_entity_type,\n", "    c.TYPE AS crate_type,\n", "    -- source_store_id, \n", "    -- co.id as wh_outlet_id,\n", "    -- co.name ,\n", "    -- t.destination_store_id,\n", "    -- co1.id as ds_outlet_id,\n", "    (c.install_ts + interval '330' MINUTE) as install_ts\n", "    \n", "\n", "FROM transit_server.transit_consignment_container c\n", "JOIN transit_server.transit_consignment t ON t.id = c.consignment_id\n", "\n", "WHERE (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "and t.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", "and c.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", ")\n", "\n", ",final as (\n", "select *, rank() over(partition by crate_id, billing_entity_id, billing_entity_type order by install_ts desc) as rn\n", "from base\n", ")\n", "\n", "select *\n", "from final\n", "where rn=1\n", "\n", "\"\"\"\n", "crate_to_consignment_df = pd.read_sql_query(sql=crate_to_consignment_query, con=trino_connection)\n", "crate_to_consignment_df = (\n", "    crate_to_consignment_df[\n", "        [\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c45c3c21-dd51-46c9-9790-8f55ab891727", "metadata": {}, "outputs": [], "source": ["billed_dispatch_df = billed_dispatch_df.merge(\n", "    crate_to_consignment_df,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "# billed_dispatch_df"]}, {"cell_type": "code", "execution_count": null, "id": "6c2c75c9-d3c5-4a3f-996f-57ae0e40436b", "metadata": {}, "outputs": [], "source": ["billed_df = (\n", "    billed_dispatch_df[billed_dispatch_df[\"state\"] == \"BILLED\"]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "# billed_df"]}, {"cell_type": "code", "execution_count": null, "id": "8bbe1648-108a-4ce6-95e7-6172f44c19dc", "metadata": {}, "outputs": [], "source": ["dispatch_df = (\n", "    billed_dispatch_df[billed_dispatch_df[\"state\"] == \"DISPATCHED\"]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "# dispatch_df"]}, {"cell_type": "code", "execution_count": null, "id": "d03fd4e8-1d7e-4096-b264-12a5360f1b4b", "metadata": {}, "outputs": [], "source": ["missing_df = (\n", "    billed_dispatch_df[billed_dispatch_df[\"state\"] == \"MISSING\"]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "# dispatch_df"]}, {"cell_type": "code", "execution_count": null, "id": "af37c070-5f45-4096-82bd-f4063836d25a", "metadata": {}, "outputs": [], "source": ["delivered_query = f\"\"\"\n", "    \n", "with base as (\n", "select distinct\n", "    c.external_id AS crate_id,\n", "    c.TYPE AS crate_type,\n", "    c.consignment_id,\n", "    t.source_store_id,\n", "    t.destination_store_id,\n", "    (c.install_ts + interval '330' MINUTE) as delivered_ts,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.id') as varchar) as billing_entity_id,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.type') as varchar) as billing_entity_type\n", "    \n", "      \n", "\n", "FROM transit_server.transit_consignment_container c\n", "JOIN transit_server.transit_consignment t ON t.id = c.consignment_id\n", "\n", "WHERE c.state = 'UNLOADED_AT_DESTINATION'\n", "AND (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "and t.insert_ds_ist >= cast((current_date - interval '7' DAY) as varchar)\n", "and c.insert_ds_ist >= cast((current_date - interval '7' DAY) as varchar)\n", ")\n", "\n", ",final as (\n", "select *, rank() over(partition by crate_id, billing_entity_id, billing_entity_type order by delivered_ts desc) as rn\n", "from base\n", ")\n", "\n", "select *\n", "from final\n", "where rn=1\n", "\n", "\"\"\"\n", "\n", "delivered_df = pd.read_sql_query(sql=delivered_query, con=trino_connection)\n", "delivered_df[\"destination_store_id\"] = delivered_df[\"destination_store_id\"].replace(\n", "    [\"32715\"], \"32750\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0472083f-6eb3-43e8-a842-1574c85402bc", "metadata": {}, "outputs": [], "source": ["delivered_df = delivered_df.merge(\n", "    logistic_mapping_df,\n", "    how=\"left\",\n", "    left_on=\"source_store_id\",\n", "    right_on=\"logistic_node_id\",\n", ")\n", "delivered_df = delivered_df.rename(columns={\"outlet_id\": \"wh_outlet_id\"})\n", "delivered_df = delivered_df.drop([\"rn\", \"logistic_node_id\"], axis=1)\n", "\n", "delivered_df = delivered_df.merge(\n", "    logistic_mapping_df,\n", "    how=\"left\",\n", "    left_on=\"destination_store_id\",\n", "    right_on=\"logistic_node_id\",\n", ")\n", "delivered_df = delivered_df.rename(columns={\"outlet_id\": \"ds_outlet_id\"})\n", "delivered_df = delivered_df.drop(\"logistic_node_id\", axis=1)"]}, {"cell_type": "markdown", "id": "bc23ac7d-fed2-4338-a897-4c3b62bdf865", "metadata": {}, "source": ["### Delivered from store ops table"]}, {"cell_type": "code", "execution_count": null, "id": "14afa037-7a36-4bb2-ba15-2a0eff3d4da3", "metadata": {"tags": []}, "outputs": [], "source": ["delivered_query_1 = f\"\"\"\n", "\n", "select distinct \n", "\n", "(a.last_modified_date + interval '330' MINUTE) as delivered_ts_store, \n", "a.container_id as crate_id, \n", "b.external_order_no as consignment_id\n", "\n", "from storeops.er_container a \n", "join storeops.er b \n", "on b.id = a.er_id\n", "where a.status in ('UNLOADED_AT_DESTINATION','MARKED_MISSING_AT_DESTINATION', 'PUT_AWAY_COMPLETED', 'PUT_AWAY_IN_PROGRESS')\n", "and b.er_type = 'FORWARD_CONSIGNMENT'\n", "and a.created_date >= current_date - interval '7' Day\n", "\"\"\"\n", "\n", "delivered_df_1 = pd.read_sql_query(sql=delivered_query_1, con=trino_connection)\n", "delivered_df_1[\"consignment_id\"] = delivered_df_1[\"consignment_id\"].astype(float)\n", "delivered_df_1 = delivered_df_1.merge(crate_to_consignment_df, on=[\"crate_id\", \"consignment_id\"])\n", "delivered_df_1 = delivered_df_1.merge(\n", "    delivered_df,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "delivered_df_1 = delivered_df_1[delivered_df_1[\"delivered_ts\"].isnull()].reset_index(drop=True)\n", "delivered_df_1[\"rn\"] = np.nan\n", "delivered_df_1 = (\n", "    delivered_df_1[\n", "        [\n", "            \"crate_id\",\n", "            \"crate_type\",\n", "            \"consignment_id\",\n", "            \"source_store_id\",\n", "            \"destination_store_id\",\n", "            \"delivered_ts_store\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"rn\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "delivered_df_1 = delivered_df_1.rename(columns={\"delivered_ts_store\": \"delivered_ts\"})\n", "\n", "delivered_df = pd.concat([delivered_df, delivered_df_1])\n", "delivered_df = delivered_df.drop_duplicates().reset_index(drop=True)"]}, {"cell_type": "markdown", "id": "bab683c5-ec24-4013-ae27-dab1925891fd", "metadata": {}, "source": ["### Delivered Crates (Dispatched and unloaded at correct store)"]}, {"cell_type": "code", "execution_count": null, "id": "a7de8a42-f418-48de-998c-77e3f5a8bf02", "metadata": {}, "outputs": [], "source": ["delivered_crates_final = pd.merge(\n", "    dispatch_df,\n", "    delivered_df,\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "markdown", "id": "31a53df6-df9c-463a-a46b-14dfdee03273", "metadata": {}, "source": ["### GRN data"]}, {"cell_type": "code", "execution_count": null, "id": "a8f9a048-47df-4488-b09c-952f6bfca226", "metadata": {"tags": []}, "outputs": [], "source": ["grn_base_query = f\"\"\"\n", "\n", "select distinct\n", "    container_id as crate_id,\n", "    (a.created_date + interval '330' MINUTE) as consig_create_ts,\n", "    (a.last_modified_date + interval '330' MINUTE) as last_modified_ts,\n", "    a.status,\n", "    container_type as crate_type,\n", "    b.external_order_no as consignment_id,\n", "    b.source_id as wh_outlet_id,\n", "    a.site_id as ds_outlet_id\n", "    \n", "from storeops.er_container a \n", "join storeops.er b \n", "on b.id = a.er_id\n", "where a.created_date >= current_date - interval '8' Day\n", "and b.er_type = 'FORWARD_CONSIGNMENT'\n", "and a.status in ('MISSING', '<PERSON>LOADED_AT_DESTINATION', 'MARKED_MISSING_AT_DESTINATION', 'PUT_AWAY_COMPLETED','PUT_AWAY_IN_PROGRESS')\n", "\"\"\"\n", "\n", "grn_base_df = pd.read_sql_query(sql=grn_base_query, con=trino_connection)\n", "grn_base_df[\"ds_outlet_id\"] = grn_base_df[\"ds_outlet_id\"].replace([\"32715\"], \"32750\")\n", "grn_base_df[\"consignment_id\"] = grn_base_df[\"consignment_id\"].astype(float)"]}, {"cell_type": "markdown", "id": "1b8819b0-3e03-44eb-816d-c622f982ff21", "metadata": {}, "source": ["### GR<PERSON> completed"]}, {"cell_type": "code", "execution_count": null, "id": "7dbdf5b3-46d6-4a0e-8286-677783c0cd27", "metadata": {}, "outputs": [], "source": ["grn_complete_df = grn_base_df[grn_base_df[\"status\"] == \"PUT_AWAY_COMPLETED\"].reset_index(drop=True)\n", "grn_complete_df = grn_complete_df.rename(columns={\"last_modified_ts\": \"grn_complete_ts\"})"]}, {"cell_type": "markdown", "id": "9dce30ae-a22d-4c48-877c-72ce3a2c955f", "metadata": {}, "source": ["### missing while GRN"]}, {"cell_type": "code", "execution_count": null, "id": "78ba67f7-e894-4be7-bbcc-9f1781bbd619", "metadata": {}, "outputs": [], "source": ["grn_missing_df = grn_base_df[grn_base_df[\"status\"] == \"MARKED_MISSING_AT_DESTINATION\"].reset_index(\n", "    drop=True\n", ")\n", "grn_missing_df = grn_missing_df.rename(columns={\"last_modified_ts\": \"grn_missing_ts\"})"]}, {"cell_type": "markdown", "id": "87011ed2-c2de-4085-b470-9a581681fbae", "metadata": {}, "source": ["### GRN in progress"]}, {"cell_type": "code", "execution_count": null, "id": "dae850b7-1de3-45e6-8175-3e2d88df3465", "metadata": {}, "outputs": [], "source": ["grn_progress_df = grn_base_df[grn_base_df[\"status\"] == \"PUT_AWAY_IN_PROGRESS\"].reset_index(\n", "    drop=True\n", ")\n", "grn_progress_df = grn_progress_df.rename(columns={\"last_modified_ts\": \"grn_progress_ts\"})"]}, {"cell_type": "markdown", "id": "e7c921e5-1ea4-4da4-b55f-64598fcabf87", "metadata": {}, "source": ["### unloaded and pending for GRN"]}, {"cell_type": "code", "execution_count": null, "id": "b5a31318-3eb0-407d-b335-912916223bb2", "metadata": {}, "outputs": [], "source": ["grn_pending_df = grn_base_df[grn_base_df[\"status\"] == \"UNLOADED_AT_DESTINATION\"].reset_index(\n", "    drop=True\n", ")\n", "grn_pending_df = grn_pending_df.rename(columns={\"last_modified_ts\": \"grn_pending_ts\"})"]}, {"cell_type": "markdown", "id": "24ae8b58-4495-462b-9280-7114cfc67c50", "metadata": {}, "source": ["### In Tansit Crates"]}, {"cell_type": "code", "execution_count": null, "id": "d01ad6e9-b8ef-4cd0-bfb5-96f380bbef70", "metadata": {}, "outputs": [], "source": ["in_transit_query = f\"\"\"\n", "\n", "with base as (\n", "select distinct\n", "    c.external_id AS crate_id,\n", "    c.TYPE AS crate_type,\n", "    c.consignment_id,\n", "    t.source_store_id,\n", "    t.destination_store_id,\n", "    (c.install_ts + interval '330' MINUTE) as in_transit_ts,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.id') as varchar) as billing_entity_id,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.type') as varchar) as billing_entity_type\n", "    \n", "      \n", "\n", "FROM transit_server.transit_consignment_container c\n", "JOIN transit_server.transit_consignment t ON t.id = c.consignment_id\n", "\n", "WHERE c.state = 'IN_TRANSIT'\n", "and t.state != 'CANCELLED'\n", "AND (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "and t.insert_ds_ist >= cast((current_date - interval '7' DAY) as varchar)\n", "and c.insert_ds_ist >= cast((current_date - interval '7' DAY) as varchar)\n", ")\n", "\n", ",final as (\n", "select *, rank() over(partition by crate_id, billing_entity_id, billing_entity_type order by in_transit_ts desc) as rn\n", "from base\n", ")\n", "\n", "select *\n", "from final\n", "where rn=1\n", "\n", "\"\"\"\n", "\n", "in_transit_df = pd.read_sql_query(sql=in_transit_query, con=trino_connection)\n", "in_transit_df[\"consignment_id\"] = in_transit_df[\"consignment_id\"].astype(float)"]}, {"cell_type": "markdown", "id": "54ec1dc8-3f8f-45df-b56a-64c6be7ae522", "metadata": {}, "source": ["### missing data"]}, {"cell_type": "code", "execution_count": null, "id": "9f19dcba-1ad4-42ca-8156-83a241a4184d", "metadata": {}, "outputs": [], "source": ["missing_query = f\"\"\"\n", "\n", "with base as (\n", "select distinct\n", "c.external_id AS crate_id,\n", "t.source_store_id,\n", "c.TYPE AS crate_type,\n", "c.state AS crate_state,\n", "c.consignment_id,\n", "t.destination_store_id,\n", "(c.install_ts + interval '330' MINUTE) as missing_ts,\n", "cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.id') as varchar) as billing_entity_id,\n", "cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.type') as varchar) as billing_entity_type\n", "\n", "\n", "FROM transit_server.transit_consignment_container c\n", "JOIN transit_server.transit_consignment t ON t.id = c.consignment_id\n", "\n", "WHERE c.state = 'MISSING_IN_TRANSIT'\n", "AND (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "and t.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", "and c.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", ")\n", "\n", ",final as (\n", "select *, rank() over(partition by crate_id, billing_entity_id, billing_entity_type order by missing_ts desc) as rn\n", "from base\n", ")\n", "\n", "select *\n", "from final\n", "where rn=1\n", "\n", "\"\"\"\n", "\n", "missing_df_ = pd.read_sql_query(sql=missing_query, con=trino_connection)\n", "missing_df_[\"destination_store_id\"] = missing_df_[\"destination_store_id\"].replace(\n", "    [\"32715\"], \"32750\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "29d1cdca-46e3-4903-afa6-81d0de67e3c9", "metadata": {}, "outputs": [], "source": ["missing_df_ = missing_df_.merge(\n", "    logistic_mapping_df,\n", "    how=\"left\",\n", "    left_on=\"source_store_id\",\n", "    right_on=\"logistic_node_id\",\n", ")\n", "missing_df_ = missing_df_.rename(columns={\"outlet_id\": \"wh_outlet_id\"})\n", "missing_df_ = missing_df_.drop([\"rn\", \"logistic_node_id\"], axis=1)\n", "\n", "missing_df_ = missing_df_.merge(\n", "    logistic_mapping_df,\n", "    how=\"left\",\n", "    left_on=\"destination_store_id\",\n", "    right_on=\"logistic_node_id\",\n", ")\n", "missing_df_ = missing_df_.rename(columns={\"outlet_id\": \"ds_outlet_id\"})\n", "missing_df_ = missing_df_.drop(\"logistic_node_id\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "ccb65580-ca62-4048-a45c-d084a366e7cc", "metadata": {}, "outputs": [], "source": ["missing_transit_df = missing_df_[missing_df_[\"crate_state\"] == \"MISSING_IN_TRANSIT\"].reset_index(\n", "    drop=True\n", ")\n", "missing_transit_df = missing_transit_df.rename(columns={\"missing_ts\": \"missing_transit_ts\"})"]}, {"cell_type": "code", "execution_count": null, "id": "d032f8f0-c8ae-4ae1-9813-2b266e4af1d5", "metadata": {}, "outputs": [], "source": ["missing_query_1 = f\"\"\"\n", "\n", "select distinct \n", "\n", "(a.last_modified_date + interval '330' MINUTE) as missing_ts_store, \n", "a.container_id as crate_id, \n", "b.external_order_no as consignment_id\n", "\n", "from storeops.er_container a \n", "join storeops.er b \n", "on b.id = a.er_id\n", "where a.status = 'MISSING'\n", "and b.er_type = 'FORWARD_CONSIGNMENT'\n", "and a.created_date >= current_date - interval '8' Day\n", "\"\"\"\n", "\n", "missing_transit_1 = pd.read_sql_query(sql=missing_query_1, con=trino_connection)\n", "missing_transit_1[\"consignment_id\"] = missing_transit_1[\"consignment_id\"].astype(float)\n", "\n", "missing_transit_1 = missing_transit_1.merge(\n", "    crate_to_consignment_df, on=[\"crate_id\", \"consignment_id\"]\n", ")\n", "missing_transit_1 = missing_transit_1.merge(\n", "    missing_transit_df,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "missing_transit_1 = missing_transit_1[missing_transit_1[\"missing_transit_ts\"].isnull()].reset_index(\n", "    drop=True\n", ")\n", "missing_transit_1 = (\n", "    missing_transit_1[\n", "        [\n", "            \"crate_id\",\n", "            \"crate_type\",\n", "            \"consignment_id\",\n", "            \"source_store_id\",\n", "            \"destination_store_id\",\n", "            \"missing_ts_store\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "missing_transit_1 = missing_transit_1.rename(columns={\"missing_ts_store\": \"missing_transit_ts\"})\n", "missing_transit_1\n", "missing_transit_df = pd.concat([missing_transit_df, missing_transit_1])\n", "missing_transit_df = missing_transit_df.drop_duplicates().reset_index(drop=True)"]}, {"cell_type": "markdown", "id": "cb72a69f-7755-4d35-8517-a127843f38bc", "metadata": {}, "source": ["### cancelled consignments"]}, {"cell_type": "code", "execution_count": null, "id": "e48d15c3-a7ba-48de-8d77-a554f4a408f5", "metadata": {}, "outputs": [], "source": ["cancelled_query = f\"\"\"\n", "\n", "with base as (\n", "select distinct\n", "    c.external_id AS crate_id,\n", "    c.TYPE AS crate_type,\n", "    c.consignment_id,\n", "    t.source_store_id,\n", "    t.destination_store_id,\n", "    (c.install_ts + interval '330' MINUTE) as cancelled_ts,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.id') as varchar) as billing_entity_id,\n", "    cast(json_extract(json_extract(c.metadata,'$.billing_entity'), '$.type') as varchar) as billing_entity_type\n", "    \n", "      \n", "\n", "FROM transit_server.transit_consignment_container c\n", "JOIN transit_server.transit_consignment t ON t.id = c.consignment_id\n", "\n", "WHERE c.state = 'IN_TRANSIT'\n", "and t.state = 'CANCELLED'\n", "AND (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "and t.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", "and c.insert_ds_ist >= cast((current_date - interval '8' DAY) as varchar)\n", ")\n", "\n", ",final as (\n", "select *, rank() over(partition by crate_id, billing_entity_id, billing_entity_type order by cancelled_ts desc) as rn\n", "from base\n", ")\n", "\n", "select *\n", "from final\n", "where rn=1\n", "\n", "\"\"\"\n", "\n", "cancelled_df = pd.read_sql_query(sql=cancelled_query, con=trino_connection)\n", "cancelled_df[\"consignment_id\"] = cancelled_df[\"consignment_id\"].astype(float)\n", "\n", "cancelled_df = cancelled_df.merge(\n", "    delivered_df[[\"crate_id\", \"consignment_id\", \"delivered_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True),\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", ")\n", "cancelled_df = cancelled_df[cancelled_df[\"delivered_ts\"].isnull()].reset_index(drop=True)\n", "cancelled_df = cancelled_df.drop(\"delivered_ts\", axis=1).drop_duplicates().reset_index(drop=True)\n", "\n", "cancelled_df = cancelled_df.merge(\n", "    missing_transit_df[[\"crate_id\", \"consignment_id\", \"missing_transit_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True),\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", ")\n", "cancelled_df = cancelled_df[cancelled_df[\"missing_transit_ts\"].isnull()].reset_index(drop=True)\n", "cancelled_df = (\n", "    cancelled_df.drop(\"missing_transit_ts\", axis=1).drop_duplicates().reset_index(drop=True)\n", ")"]}, {"cell_type": "markdown", "id": "28061980-b652-4a8c-ae34-64682836a6be", "metadata": {}, "source": ["### missing in transit"]}, {"cell_type": "code", "execution_count": null, "id": "097f9ce6-2ff6-41c6-b300-2d0350d2a844", "metadata": {}, "outputs": [], "source": ["missing_transit_final = dispatch_df.merge(\n", "    missing_transit_df,\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "markdown", "id": "f075e7d6-aa3f-4d86-8c99-3e8742b45535", "metadata": {}, "source": ["### missing in transit: Dispatched and unloaded at other store"]}, {"cell_type": "code", "execution_count": null, "id": "6ffd67b1-81e7-4793-83b9-32a3fea02acf", "metadata": {}, "outputs": [], "source": ["miss_transit_unloaded_wrong = missing_transit_final.merge(\n", "    excess_df, left_on=\"crate_id\", right_on=\"excess_crates\"\n", ")\n", "miss_transit_unloaded_wrong = miss_transit_unloaded_wrong[\n", "    miss_transit_unloaded_wrong[\"ds_outlet_id_x\"] != miss_transit_unloaded_wrong[\"ds_outlet_id_y\"]\n", "]\n", "miss_transit_unloaded_wrong = miss_transit_unloaded_wrong[\n", "    miss_transit_unloaded_wrong[\"billed_ts\"] <= miss_transit_unloaded_wrong[\"wrong_unload_ts\"]\n", "]\n", "miss_transit_unloaded_wrong[\"wrong_unload_ts\"] = pd.to_datetime(\n", "    miss_transit_unloaded_wrong[\"wrong_unload_ts\"]\n", ")\n", "miss_transit_unloaded_wrong[\"missing_transit_ts\"] = pd.to_datetime(\n", "    miss_transit_unloaded_wrong[\"missing_transit_ts\"]\n", ")\n", "\n", "miss_transit_unloaded_wrong[\"time_difference\"] = (\n", "    miss_transit_unloaded_wrong[\"wrong_unload_ts\"]\n", "    - miss_transit_unloaded_wrong[\"missing_transit_ts\"]\n", ").abs()\n", "miss_transit_unloaded_wrong[\"within_2_days\"] = miss_transit_unloaded_wrong[\n", "    \"time_difference\"\n", "] <= pd.<PERSON><PERSON><PERSON>(days=2)\n", "miss_transit_unloaded_wrong = miss_transit_unloaded_wrong[\n", "    miss_transit_unloaded_wrong[\"within_2_days\"] == True\n", "].reset_index(drop=True)\n", "miss_transit_unloaded_wrong = miss_transit_unloaded_wrong.sort_values(\n", "    [\"dispatch_ts\", \"crate_id\", \"wrong_unload_ts\"]\n", ").reset_index(drop=True)\n", "\n", "miss_transit_unloaded_wrong_temp = miss_transit_unloaded_wrong.copy()\n", "\n", "miss_transit_unloaded_wrong = (\n", "    miss_transit_unloaded_wrong[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id_x\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"crate_type\",\n", "            \"crate_state\",\n", "            \"missing_transit_ts\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_transit_unloaded_wrong = (\n", "    miss_transit_unloaded_wrong.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id_x\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"crate_type\",\n", "            \"crate_state\",\n", "            \"missing_transit_ts\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "947613f7-6938-45d9-adf5-05e0d8274092", "metadata": {}, "outputs": [], "source": ["miss_transit_unloaded_wrong_temp = (\n", "    miss_transit_unloaded_wrong_temp[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id_x\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"crate_type\",\n", "            \"crate_state\",\n", "            \"ds_outlet_id_y\",\n", "            \"missing_transit_ts\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_transit_unloaded_wrong_temp = (\n", "    miss_transit_unloaded_wrong_temp.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id_x\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"crate_type\",\n", "            \"crate_state\",\n", "            \"missing_transit_ts\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "miss_transit_unloaded_wrong_temp = miss_transit_unloaded_wrong_temp.rename(\n", "    columns={\"ds_outlet_id_y\": \"wrong_store_id\"}\n", ")"]}, {"cell_type": "markdown", "id": "82b4da73-8c2f-44c8-a8eb-d20c17a1c7ab", "metadata": {}, "source": ["### missing in transit: Dispatched and unloaded at same store wrong consig"]}, {"cell_type": "code", "execution_count": null, "id": "9422ef86-ca78-4e0e-af7d-49dc342c8f76", "metadata": {}, "outputs": [], "source": ["miss_transit_unloaded_corr_store_wrong_cons = missing_transit_final.merge(\n", "    excess_df, left_on=\"crate_id\", right_on=\"excess_crates\"\n", ")\n", "\n", "miss_transit_unloaded_corr_store_wrong_cons = miss_transit_unloaded_corr_store_wrong_cons[\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"ds_outlet_id_x\"]\n", "    == miss_transit_unloaded_corr_store_wrong_cons[\"ds_outlet_id_y\"]\n", "]\n", "miss_transit_unloaded_corr_store_wrong_cons = miss_transit_unloaded_corr_store_wrong_cons[\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"billed_ts\"]\n", "    <= miss_transit_unloaded_corr_store_wrong_cons[\"wrong_unload_ts\"]\n", "]\n", "miss_transit_unloaded_corr_store_wrong_cons = miss_transit_unloaded_corr_store_wrong_cons[\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"consignment_id_x\"]\n", "    != miss_transit_unloaded_corr_store_wrong_cons[\"consignment_id_y\"]\n", "]\n", "\n", "miss_transit_unloaded_corr_store_wrong_cons[\"wrong_unload_ts\"] = pd.to_datetime(\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"wrong_unload_ts\"]\n", ")\n", "miss_transit_unloaded_corr_store_wrong_cons[\"missing_transit_ts\"] = pd.to_datetime(\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"missing_transit_ts\"]\n", ")\n", "\n", "miss_transit_unloaded_corr_store_wrong_cons[\"time_difference\"] = (\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"wrong_unload_ts\"]\n", "    - miss_transit_unloaded_corr_store_wrong_cons[\"missing_transit_ts\"]\n", ").abs()\n", "miss_transit_unloaded_corr_store_wrong_cons[\"within_2_days\"] = (\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"time_difference\"] <= pd.Timedelta(days=2)\n", ")\n", "miss_transit_unloaded_corr_store_wrong_cons = miss_transit_unloaded_corr_store_wrong_cons[\n", "    miss_transit_unloaded_corr_store_wrong_cons[\"within_2_days\"] == True\n", "].reset_index(drop=True)\n", "miss_transit_unloaded_corr_store_wrong_cons = (\n", "    miss_transit_unloaded_corr_store_wrong_cons.sort_values(\n", "        [\"dispatch_ts\", \"crate_id\", \"wrong_unload_ts\"]\n", "    ).reset_index(drop=True)\n", ")\n", "\n", "miss_transit_unloaded_corr_store_wrong_cons = (\n", "    miss_transit_unloaded_corr_store_wrong_cons[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id_x\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"crate_type\",\n", "            \"crate_state\",\n", "            \"missing_transit_ts\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_transit_unloaded_corr_store_wrong_cons = (\n", "    miss_transit_unloaded_corr_store_wrong_cons.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id_x\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"crate_type\",\n", "            \"crate_state\",\n", "            \"missing_transit_ts\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "\n", "# miss_transit_unloaded_corr_store_wrong_cons"]}, {"cell_type": "markdown", "id": "73d43103-6273-4bfb-baee-12ee94d82071", "metadata": {}, "source": ["### missing in transit but grn tried (unloading scan missed and grn tried)"]}, {"cell_type": "code", "execution_count": null, "id": "92f76fa2-29da-4f4c-941b-f29dd78c855e", "metadata": {}, "outputs": [], "source": ["grn_tried_query = f\"\"\"\n", "\n", "SELECT distinct \n", "    cast((from_unixtime(eve.timestamp / 1000) AT TIME ZONE 'Asia/Kolkata') as timestamp) AS grn_try_timestamp,\n", "    eve.site_id as ds_outlet_id,\n", "    eve.scan_data as crate_id\n", "    \n", "from \n", "    zomato.blinkit_jumbo2.storeops_app_events eve\n", "\n", "where event_name = 'CRATE_SCAN_FAILED' \n", "and eve.error_msg = 'You cannot start putAway as this crate was marked MISSING'\n", "and eve.dt >= date_format((current_date - interval '7' day), '%%Y%%m%%d')\n", "--cast((current_date - interval '7' day) as var<PERSON>r)\n", "\n", "\"\"\"\n", "\n", "grn_tried_df = pd.read_sql_query(sql=grn_tried_query, con=trino_connection)\n", "grn_tried_df[\"ds_outlet_id\"] = grn_tried_df[\"ds_outlet_id\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "493bf5d2-da13-4f1e-823a-6eb6457e3cc7", "metadata": {}, "outputs": [], "source": ["missing_transit_df_temp = (\n", "    missing_transit_df[\n", "        [\n", "            \"crate_id\",\n", "            \"consignment_id\",\n", "            \"missing_transit_ts\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"ds_outlet_id\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_transit_grn_tried = missing_transit_df_temp.merge(grn_tried_df, on=[\"crate_id\"])\n", "miss_transit_grn_tried = miss_transit_grn_tried[\n", "    miss_transit_grn_tried[\"grn_try_timestamp\"] >= miss_transit_grn_tried[\"missing_transit_ts\"]\n", "]\n", "miss_transit_grn_tried = miss_transit_grn_tried.sort_values(\n", "    [\"crate_id\", \"missing_transit_ts\", \"grn_try_timestamp\"]\n", ").reset_index(drop=True)\n", "miss_transit_grn_tried = miss_transit_grn_tried[\n", "    (miss_transit_grn_tried[\"ds_outlet_id_x\"] == miss_transit_grn_tried[\"ds_outlet_id_y\"])\n", "    | (miss_transit_grn_tried[\"ds_outlet_id_x\"].isnull())\n", "].reset_index(drop=True)\n", "miss_transit_grn_tried = (\n", "    miss_transit_grn_tried.groupby(\n", "        [\n", "            \"crate_id\",\n", "            \"consignment_id\",\n", "            \"missing_transit_ts\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "miss_transit_grn_tried[\"grn_try_timestamp\"] = pd.to_datetime(\n", "    miss_transit_grn_tried[\"grn_try_timestamp\"]\n", ")\n", "miss_transit_grn_tried[\"missing_transit_ts\"] = pd.to_datetime(\n", "    miss_transit_grn_tried[\"missing_transit_ts\"]\n", ")\n", "\n", "### grn to be done with 2 days after missing in transit\n", "miss_transit_grn_tried[\"diff\"] = (\n", "    miss_transit_grn_tried[\"grn_try_timestamp\"] - miss_transit_grn_tried[\"missing_transit_ts\"]\n", ")\n", "miss_transit_grn_tried = miss_transit_grn_tried[\n", "    miss_transit_grn_tried[\"diff\"] <= pd.Timedel<PERSON>(days=2)\n", "].reset_index(drop=True)\n", "# miss_transit_grn_tried"]}, {"cell_type": "markdown", "id": "8683c5f5-2873-42d9-8307-893c18505cad", "metadata": {}, "source": ["### missing in transit but b2b done at WH"]}, {"cell_type": "code", "execution_count": null, "id": "0cadc926-db6d-42c6-8cf3-8b7f94ce5600", "metadata": {}, "outputs": [], "source": ["b2b_temp = b2b_df[~b2b_df[\"consignment_id\"].isnull()].drop_duplicates().reset_index(drop=True)\n", "\n", "miss_transit_b2b = missing_transit_df.merge(b2b_temp, on=[\"crate_id\", \"consignment_id\"])\n", "miss_transit_b2b = miss_transit_b2b.rename(columns={\"b2b_scan_ts\": \"b2b_scan_miss_transit_ts\"})\n", "miss_transit_b2b = miss_transit_b2b.sort_values([\"crate_id\", \"missing_transit_ts\"]).reset_index(\n", "    drop=True\n", ")\n", "miss_transit_b2b = (\n", "    miss_transit_b2b.groupby([\"crate_id\", \"missing_transit_ts\"]).first().reset_index()\n", ")\n", "# miss_transit_b2b"]}, {"cell_type": "markdown", "id": "9b015db2-2950-4af1-8f89-efd39fed9c9c", "metadata": {}, "source": ["### missing at source"]}, {"cell_type": "code", "execution_count": null, "id": "f348906d-2a5d-432c-9e21-0fc7f4be28f8", "metadata": {}, "outputs": [], "source": ["# missing_source_final = missing_df.merge(missing_source_df, on=['crate_id', 'consignment_id','billing_entity_id','billing_entity_type'])\n", "missing_source_final = missing_df.copy()\n", "missing_source_final = missing_source_final.rename(columns={\"dispatch_ts\": \"missing_source_ts\"})\n", "# missing_source_final"]}, {"cell_type": "markdown", "id": "427d27ac-72e6-4d24-b2bd-adb15bbf5988", "metadata": {}, "source": ["###  Missing at Source but unloaded at correct store"]}, {"cell_type": "code", "execution_count": null, "id": "25ca99ee-c314-4fe4-85d2-653fcfeb5f8b", "metadata": {"tags": []}, "outputs": [], "source": ["miss_at_source_unloaded_correct_store = missing_source_final.merge(\n", "    delivered_df,\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "# miss_at_source_unloaded_correct_store"]}, {"cell_type": "markdown", "id": "e2d28754-274a-4647-b648-980d602a5518", "metadata": {}, "source": ["###  Missing at Source but unloaded at wrong store"]}, {"cell_type": "code", "execution_count": null, "id": "f2702b7a-707e-4e2a-96d5-cc899ed2d3f4", "metadata": {"tags": []}, "outputs": [], "source": ["miss_at_source_unloaded_wrong = missing_source_final.merge(\n", "    excess_df, left_on=\"crate_id\", right_on=\"excess_crates\"\n", ")\n", "miss_at_source_unloaded_wrong = miss_at_source_unloaded_wrong[\n", "    miss_at_source_unloaded_wrong[\"ds_outlet_id_x\"]\n", "    != miss_at_source_unloaded_wrong[\"ds_outlet_id_y\"]\n", "]\n", "\n", "miss_at_source_unloaded_wrong = miss_at_source_unloaded_wrong[\n", "    miss_at_source_unloaded_wrong[\"billed_ts\"] <= miss_at_source_unloaded_wrong[\"wrong_unload_ts\"]\n", "]\n", "miss_at_source_unloaded_wrong[\"wrong_unload_ts\"] = pd.to_datetime(\n", "    miss_at_source_unloaded_wrong[\"wrong_unload_ts\"]\n", ")\n", "miss_at_source_unloaded_wrong[\"missing_source_ts\"] = pd.to_datetime(\n", "    miss_at_source_unloaded_wrong[\"missing_source_ts\"]\n", ")\n", "\n", "miss_at_source_unloaded_wrong[\"time_difference\"] = (\n", "    miss_at_source_unloaded_wrong[\"wrong_unload_ts\"]\n", "    - miss_at_source_unloaded_wrong[\"missing_source_ts\"]\n", ").abs()\n", "miss_at_source_unloaded_wrong[\"within_2_days\"] = miss_at_source_unloaded_wrong[\n", "    \"time_difference\"\n", "] <= pd.<PERSON><PERSON><PERSON>(days=2)\n", "miss_at_source_unloaded_wrong = miss_at_source_unloaded_wrong[\n", "    miss_at_source_unloaded_wrong[\"within_2_days\"] == True\n", "].reset_index(drop=True)\n", "miss_at_source_unloaded_wrong = miss_at_source_unloaded_wrong.sort_values(\n", "    [\"missing_source_ts\", \"crate_id\", \"wrong_unload_ts\"]\n", ").reset_index(drop=True)\n", "\n", "miss_at_source_unloaded_wrong_temp = miss_at_source_unloaded_wrong.copy()\n", "\n", "miss_at_source_unloaded_wrong = (\n", "    miss_at_source_unloaded_wrong[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"missing_source_ts\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_at_source_unloaded_wrong = (\n", "    miss_at_source_unloaded_wrong.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"missing_source_ts\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "# miss_at_source_unloaded_wrong"]}, {"cell_type": "code", "execution_count": null, "id": "23c37b0f-b640-400d-97df-32910ad89a14", "metadata": {}, "outputs": [], "source": ["miss_at_source_unloaded_wrong_temp = (\n", "    miss_at_source_unloaded_wrong_temp[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"ds_outlet_id_y\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_at_source_unloaded_wrong_temp = (\n", "    miss_at_source_unloaded_wrong_temp.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "miss_at_source_unloaded_wrong_temp = miss_at_source_unloaded_wrong_temp.rename(\n", "    columns={\"ds_outlet_id_y\": \"wrong_store_id\"}\n", ")"]}, {"cell_type": "markdown", "id": "96128571-c5e7-4c7e-9763-bcfe85c549ab", "metadata": {}, "source": ["### missing at source and B2B done"]}, {"cell_type": "code", "execution_count": null, "id": "17ee639f-ad52-454e-9e3d-d6b7a63b3f59", "metadata": {}, "outputs": [], "source": ["miss_source_b2b = missing_source_final.merge(b2b_temp, on=[\"crate_id\", \"consignment_id\"])\n", "miss_source_b2b = miss_source_b2b.rename(columns={\"b2b_scan_ts\": \"b2b_scan_miss_source_ts\"})\n", "miss_source_b2b = miss_source_b2b.sort_values([\"crate_id\", \"missing_source_ts\"]).reset_index(\n", "    drop=True\n", ")\n", "miss_source_b2b = miss_source_b2b.groupby([\"crate_id\", \"missing_source_ts\"]).first().reset_index()\n", "# miss_source_b2b"]}, {"cell_type": "markdown", "id": "121ab70e-dbae-48e5-a7de-c1063d83424c", "metadata": {}, "source": ["### Billed but not Dispatched"]}, {"cell_type": "code", "execution_count": null, "id": "cf30577d-961f-4b13-a301-bc44d4e956a7", "metadata": {}, "outputs": [], "source": ["billed_dispatch_df_temp = billed_dispatch_df[\n", "    billed_dispatch_df[\"state\"].isin([\"BILLED\", \"SORTED\", \"PENDING\"])\n", "].reset_index(drop=True)\n", "billed_dispatch_df_temp[\"billing_date\"] = pd.to_datetime(\n", "    billed_dispatch_df_temp[\"billing_date\"]\n", ").dt.date\n", "\n", "ist_timezone = pytz.timezone(\"Asia/Kolkata\")\n", "current_date_ist = datetime.now(ist_timezone).date()\n", "\n", "# Filter rows where 'billed_ts' is older than 3 days from the current date\n", "billed_not_dispatch_df = billed_dispatch_df_temp.copy()\n", "# [billed_dispatch_df_temp['billing_date'] <= current_date_ist - timedelta(days=3)].drop_duplicates().reset_index(drop=True)\n", "\n", "# billed_not_dispatch_df"]}, {"cell_type": "markdown", "id": "42f94c5b-551f-4742-b4ef-773c248e7f27", "metadata": {}, "source": ["###  Billed but not Dispatched and (unloaded at correct store)"]}, {"cell_type": "code", "execution_count": null, "id": "4fa8e439-8c36-4885-855e-0a03c772382c", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_unloaded_corrent_df = billed_not_dispatch_df.merge(\n", "    excess_df, left_on=\"crate_id\", right_on=\"excess_crates\"\n", ")\n", "billed_not_dispatch_unloaded_corrent_df = billed_not_dispatch_unloaded_corrent_df[\n", "    billed_not_dispatch_unloaded_corrent_df[\"billed_ts\"]\n", "    <= billed_not_dispatch_unloaded_corrent_df[\"wrong_unload_ts\"]\n", "]\n", "billed_not_dispatch_unloaded_corrent_df = billed_not_dispatch_unloaded_corrent_df[\n", "    billed_not_dispatch_unloaded_corrent_df[\"ds_outlet_id_x\"]\n", "    == billed_not_dispatch_unloaded_corrent_df[\"ds_outlet_id_y\"]\n", "]\n", "\n", "billed_not_dispatch_unloaded_corrent_df[\"wrong_unload_ts\"] = pd.to_datetime(\n", "    billed_not_dispatch_unloaded_corrent_df[\"wrong_unload_ts\"]\n", ")\n", "billed_not_dispatch_unloaded_corrent_df[\"billing_ts\"] = pd.to_datetime(\n", "    billed_not_dispatch_unloaded_corrent_df[\"billing_date\"]\n", ")\n", "\n", "billed_not_dispatch_unloaded_corrent_df[\"time_difference\"] = (\n", "    billed_not_dispatch_unloaded_corrent_df[\"wrong_unload_ts\"]\n", "    - billed_not_dispatch_unloaded_corrent_df[\"billing_ts\"]\n", ").abs()\n", "billed_not_dispatch_unloaded_corrent_df[\"within_2_days\"] = billed_not_dispatch_unloaded_corrent_df[\n", "    \"time_difference\"\n", "] <= pd.<PERSON><PERSON><PERSON>(days=2)\n", "billed_not_dispatch_unloaded_corrent_df = billed_not_dispatch_unloaded_corrent_df[\n", "    billed_not_dispatch_unloaded_corrent_df[\"within_2_days\"] == True\n", "].reset_index(drop=True)\n", "billed_not_dispatch_unloaded_corrent_df = billed_not_dispatch_unloaded_corrent_df.sort_values(\n", "    [\"billing_date\", \"crate_id\", \"wrong_unload_ts\"]\n", ").reset_index(drop=True)\n", "\n", "billed_not_dispatch_unloaded_corrent_df = (\n", "    billed_not_dispatch_unloaded_corrent_df[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "billed_not_dispatch_unloaded_corrent_df = (\n", "    billed_not_dispatch_unloaded_corrent_df.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "# billed_not_dispatch_unloaded_corrent_df"]}, {"cell_type": "code", "execution_count": null, "id": "8b2c3feb-c5f4-4fd5-b940-6723509aa49d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "439c4a34-49e7-42ea-97a0-517bbfef3002", "metadata": {}, "source": ["###  Billed but not Dispatched and (unloaded at wrong store)"]}, {"cell_type": "code", "execution_count": null, "id": "794762b2-92b1-4d70-b0a1-59cf22ea836d", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_unload_wrong = billed_not_dispatch_df.merge(\n", "    excess_df, left_on=\"crate_id\", right_on=\"excess_crates\"\n", ")\n", "billed_not_dispatch_unload_wrong = billed_not_dispatch_unload_wrong[\n", "    billed_not_dispatch_unload_wrong[\"billed_ts\"]\n", "    <= billed_not_dispatch_unload_wrong[\"wrong_unload_ts\"]\n", "]\n", "billed_not_dispatch_unload_wrong = billed_not_dispatch_unload_wrong[\n", "    billed_not_dispatch_unload_wrong[\"ds_outlet_id_x\"]\n", "    != billed_not_dispatch_unload_wrong[\"ds_outlet_id_y\"]\n", "]\n", "\n", "billed_not_dispatch_unload_wrong[\"wrong_unload_ts\"] = pd.to_datetime(\n", "    billed_not_dispatch_unload_wrong[\"wrong_unload_ts\"]\n", ")\n", "billed_not_dispatch_unload_wrong[\"billing_ts\"] = pd.to_datetime(\n", "    billed_not_dispatch_unload_wrong[\"billing_date\"]\n", ")\n", "\n", "billed_not_dispatch_unload_wrong[\"time_difference\"] = (\n", "    billed_not_dispatch_unload_wrong[\"wrong_unload_ts\"]\n", "    - billed_not_dispatch_unload_wrong[\"billing_ts\"]\n", ").abs()\n", "billed_not_dispatch_unload_wrong[\"within_2_days\"] = billed_not_dispatch_unload_wrong[\n", "    \"time_difference\"\n", "] <= pd.<PERSON><PERSON><PERSON>(days=2)\n", "billed_not_dispatch_unload_wrong = billed_not_dispatch_unload_wrong[\n", "    billed_not_dispatch_unload_wrong[\"within_2_days\"] == True\n", "].reset_index(drop=True)\n", "billed_not_dispatch_unload_wrong = billed_not_dispatch_unload_wrong.sort_values(\n", "    [\"billing_date\", \"crate_id\", \"wrong_unload_ts\"]\n", ").reset_index(drop=True)\n", "\n", "billed_not_dispatch_unload_wrong_temp = billed_not_dispatch_unload_wrong.copy()\n", "\n", "billed_not_dispatch_unload_wrong = (\n", "    billed_not_dispatch_unload_wrong[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "billed_not_dispatch_unload_wrong = (\n", "    billed_not_dispatch_unload_wrong.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "# billed_not_dispatch_unload_wrong"]}, {"cell_type": "code", "execution_count": null, "id": "59777300-4191-40d1-9b10-140f385fa515", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_unload_wrong_temp = (\n", "    billed_not_dispatch_unload_wrong_temp[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"ds_outlet_id_y\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "billed_not_dispatch_unload_wrong_temp = (\n", "    billed_not_dispatch_unload_wrong_temp.groupby(\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"state\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "billed_not_dispatch_unload_wrong_temp = billed_not_dispatch_unload_wrong_temp.rename(\n", "    columns={\"ds_outlet_id_y\": \"wrong_store_id\"}\n", ")"]}, {"cell_type": "markdown", "id": "e7aee8e0-115f-418e-beaf-7561eaace5d3", "metadata": {}, "source": ["### Billed but not dispatched and B2B done"]}, {"cell_type": "code", "execution_count": null, "id": "c62eac04-6210-4435-bfc1-9f4ef7e3d487", "metadata": {}, "outputs": [], "source": ["b2b_bill_not_dis = (\n", "    b2b_df[b2b_df[\"consignment_id\"].isnull()].drop_duplicates().reset_index(drop=True)\n", ")\n", "b2b_bill_not_dis = b2b_bill_not_dis.drop(\"consignment_id\", axis=1)\n", "b2b_bill_not_dis = b2b_bill_not_dis.rename(\n", "    columns={\"b2b_scan_ts\": \"b2b_scan_billed_not_dispatch_ts\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7e310955-b1d8-4fb4-ad3b-3b2c84f191b9", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_df_temp = (\n", "    billed_not_dispatch_df[\n", "        [\n", "            \"billed_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "billed_not_dispatch_b2b = billed_not_dispatch_df_temp.merge(\n", "    b2b_bill_not_dis, on=[\"crate_id\", \"wh_outlet_id\"]\n", ")\n", "billed_not_dispatch_b2b = billed_not_dispatch_b2b[\n", "    billed_not_dispatch_b2b[\"b2b_scan_billed_not_dispatch_ts\"]\n", "    >= billed_not_dispatch_b2b[\"billed_ts\"]\n", "].reset_index(drop=True)\n", "billed_not_dispatch_b2b = billed_not_dispatch_b2b.sort_values(\n", "    [\"billed_ts\", \"crate_id\", \"b2b_scan_billed_not_dispatch_ts\"]\n", ").reset_index(drop=True)\n", "billed_not_dispatch_b2b = (\n", "    billed_not_dispatch_b2b.groupby(\n", "        [\n", "            \"billed_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    )\n", "    .first()\n", "    .reset_index()\n", ")\n", "# billed_not_dispatch_b2b"]}, {"cell_type": "code", "execution_count": null, "id": "7c90b695-91a5-4c5e-b8a2-22ed4740881d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c506fdac-9e31-4ce2-b2e6-4df53d2dbecf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "4809e34a-f17c-439f-b290-2890c8916f98", "metadata": {}, "source": ["### final"]}, {"cell_type": "code", "execution_count": null, "id": "62c12b1a-6cd3-4438-b0cd-b7a132c47dd5", "metadata": {}, "outputs": [], "source": ["delivered_crates_final_join = (\n", "    delivered_crates_final[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"delivered_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "delivered_crates_final_join = delivered_crates_final_join.rename(\n", "    columns={\"delivered_ts\": \"delivered_correct_store_ts\"}\n", ")\n", "\n", "final_1 = billed_dispatch_df.merge(\n", "    delivered_crates_final_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", "    suffixes=(\"_main\", \"_delivered_correct\"),\n", ")\n", "# final_1"]}, {"cell_type": "code", "execution_count": null, "id": "a9139fd7-0110-44a3-b009-de2c18192185", "metadata": {}, "outputs": [], "source": ["grn_complete_df_join = (\n", "    grn_complete_df[[\"crate_id\", \"consignment_id\", \"grn_complete_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_2 = final_1.merge(grn_complete_df_join, how=\"left\", on=[\"crate_id\", \"consignment_id\"])\n", "# final_1"]}, {"cell_type": "code", "execution_count": null, "id": "c3af67ef-67bf-479e-b5db-4f45046600b1", "metadata": {}, "outputs": [], "source": ["grn_missing_df_join = (\n", "    grn_missing_df[[\"crate_id\", \"consignment_id\", \"grn_missing_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_3 = final_2.merge(grn_missing_df_join, how=\"left\", on=[\"crate_id\", \"consignment_id\"])\n", "# final_1"]}, {"cell_type": "code", "execution_count": null, "id": "e2f4a77c-c25f-494b-af38-a211672947c1", "metadata": {}, "outputs": [], "source": ["grn_progress_df_join = (\n", "    grn_progress_df[[\"crate_id\", \"consignment_id\", \"grn_progress_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_4 = final_3.merge(grn_progress_df_join, how=\"left\", on=[\"crate_id\", \"consignment_id\"])\n", "# final_1"]}, {"cell_type": "code", "execution_count": null, "id": "d3088bbe-15aa-4410-8cc6-a83b1dfff677", "metadata": {}, "outputs": [], "source": ["grn_pending_df_join = (\n", "    grn_pending_df[[\"crate_id\", \"consignment_id\", \"grn_pending_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_5 = final_4.merge(grn_pending_df_join, how=\"left\", on=[\"crate_id\", \"consignment_id\"])\n", "# final_1"]}, {"cell_type": "code", "execution_count": null, "id": "88e90e8c-9fc2-407a-afc9-a91b749c2ad7", "metadata": {}, "outputs": [], "source": ["missing_transit_final_join = (\n", "    missing_transit_final[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"missing_transit_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_6 = final_5.merge(\n", "    missing_transit_final_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", "    suffixes=(\"\", \"_missing_transit\"),\n", ")\n", "# final_2"]}, {"cell_type": "code", "execution_count": null, "id": "84718dbf-6400-4f98-ad99-0bf5e22a65e0", "metadata": {}, "outputs": [], "source": ["miss_transit_unloaded_wrong_join = (\n", "    miss_transit_unloaded_wrong[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_transit_unloaded_wrong_join = miss_transit_unloaded_wrong_join.rename(\n", "    columns={\n", "        \"consignment_id_x\": \"consignment_id\",\n", "        \"wrong_unload_ts\": \"miss_transit_wrong_unload_ts\",\n", "    }\n", ")\n", "\n", "final_7 = final_6.merge(\n", "    miss_transit_unloaded_wrong_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "# final_3"]}, {"cell_type": "code", "execution_count": null, "id": "448ed0a4-2cf1-461a-8348-c06126a7dccd", "metadata": {}, "outputs": [], "source": ["miss_transit_unloaded_corr_store_wrong_cons_join = (\n", "    miss_transit_unloaded_corr_store_wrong_cons[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_transit_unloaded_corr_store_wrong_cons_join = (\n", "    miss_transit_unloaded_corr_store_wrong_cons_join.rename(\n", "        columns={\n", "            \"consignment_id_x\": \"consignment_id\",\n", "            \"wrong_unload_ts\": \"miss_transit_unloaded_corr_store_wrong_cons_ts\",\n", "        }\n", "    )\n", ")\n", "\n", "final_8 = final_7.merge(\n", "    miss_transit_unloaded_corr_store_wrong_cons_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d3b1614f-fe95-4e94-9844-b815d8717d7b", "metadata": {"tags": []}, "outputs": [], "source": ["miss_transit_grn_tried_join = (\n", "    miss_transit_grn_tried[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"grn_try_timestamp\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_transit_grn_tried_join\n", "\n", "final_9 = final_8.merge(\n", "    miss_transit_grn_tried_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "29e8539b-a429-4a6c-909e-2c784f4aaac8", "metadata": {}, "outputs": [], "source": ["miss_transit_b2b_join = (\n", "    miss_transit_b2b[\n", "        [\n", "            \"crate_id\",\n", "            \"consignment_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"b2b_scan_miss_transit_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_10 = final_9.merge(\n", "    miss_transit_b2b_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7e8ada4a-8ffa-4a8f-b41b-6b13990a3ea6", "metadata": {}, "outputs": [], "source": ["missing_source_final_join = (\n", "    missing_source_final[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"missing_source_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_11 = final_10.merge(\n", "    missing_source_final_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "# final_4"]}, {"cell_type": "code", "execution_count": null, "id": "14276833-95ff-4fdd-8c63-8732061a4dcb", "metadata": {}, "outputs": [], "source": ["miss_at_source_unloaded_correct_store_join = (\n", "    miss_at_source_unloaded_correct_store[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"delivered_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_at_source_unloaded_correct_store_join = miss_at_source_unloaded_correct_store_join.rename(\n", "    columns={\"delivered_ts\": \"miss_source_unload_correct_ts\"}\n", ")\n", "\n", "final_12 = final_11.merge(\n", "    miss_at_source_unloaded_correct_store_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "# final_5"]}, {"cell_type": "code", "execution_count": null, "id": "934e755c-0bfd-406f-bda6-06cb52f8f145", "metadata": {}, "outputs": [], "source": ["miss_at_source_unloaded_wrong_join = (\n", "    miss_at_source_unloaded_wrong[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_at_source_unloaded_wrong_join = miss_at_source_unloaded_wrong_join.rename(\n", "    columns={\n", "        \"consignment_id_x\": \"consignment_id\",\n", "        \"wrong_unload_ts\": \"miss_source_wrong_unload_ts\",\n", "    }\n", ")\n", "\n", "final_13 = final_12.merge(\n", "    miss_at_source_unloaded_wrong_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "# final_6"]}, {"cell_type": "code", "execution_count": null, "id": "438b09b3-e658-4106-ba87-a4f1bbbb6c8c", "metadata": {"tags": []}, "outputs": [], "source": ["miss_source_b2b_join = (\n", "    miss_source_b2b[\n", "        [\n", "            \"crate_id\",\n", "            \"consignment_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"b2b_scan_miss_source_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_14 = final_13.merge(\n", "    miss_source_b2b_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "658771bc-6dd4-4dfd-b2e3-9bc65771b240", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_df_join = (\n", "    billed_not_dispatch_df[\n", "        [\n", "            \"billed_ts\",\n", "            \"crate_id\",\n", "            \"consignment_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_15 = final_14.merge(\n", "    billed_not_dispatch_df_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", "    suffixes=(\"\", \"_billed_not_dispatch\"),\n", ")\n", "# final_7"]}, {"cell_type": "code", "execution_count": null, "id": "ef01cd6c-8966-41ee-82de-f07759d7013b", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_unloaded_corrent_df_join = (\n", "    billed_not_dispatch_unloaded_corrent_df[\n", "        [\n", "            \"crate_id\",\n", "            \"consignment_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "billed_not_dispatch_unloaded_corrent_df_join = billed_not_dispatch_unloaded_corrent_df_join.rename(\n", "    columns={\n", "        \"consignment_id_x\": \"consignment_id\",\n", "        \"wrong_unload_ts\": \"billed_not_dispatch_unloaded_corrent_ts\",\n", "    }\n", ")\n", "\n", "final_16 = final_15.merge(\n", "    billed_not_dispatch_unloaded_corrent_df_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")\n", "# final_8"]}, {"cell_type": "code", "execution_count": null, "id": "ba1607f4-3559-4d92-b4a3-1f003c1657fe", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_unload_wrong_join = (\n", "    billed_not_dispatch_unload_wrong[\n", "        [\n", "            \"crate_id\",\n", "            \"consignment_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "billed_not_dispatch_unload_wrong_join = billed_not_dispatch_unload_wrong_join.rename(\n", "    columns={\n", "        \"consignment_id_x\": \"consignment_id\",\n", "        \"wrong_unload_ts\": \"billed_not_dispatch_wrong_unload_ts\",\n", "    }\n", ")\n", "\n", "final_17 = final_16.merge(\n", "    billed_not_dispatch_unload_wrong_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "db7a9e7d-750a-4903-86dd-9802cf855bc3", "metadata": {}, "outputs": [], "source": ["billed_not_dispatch_b2b_join = (\n", "    billed_not_dispatch_b2b[\n", "        [\n", "            \"crate_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"b2b_scan_billed_not_dispatch_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_18 = final_17.merge(\n", "    billed_not_dispatch_b2b_join,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2e947858-91da-416a-a7ac-ec3f43950daa", "metadata": {}, "outputs": [], "source": ["in_transit_df_join = (\n", "    in_transit_df[[\"crate_id\", \"consignment_id\", \"in_transit_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_19 = final_18.merge(in_transit_df_join, how=\"left\", on=[\"crate_id\", \"consignment_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c064228b-4d2b-4899-96ea-ced1c445f5c6", "metadata": {}, "outputs": [], "source": ["cancelled_df_join = (\n", "    cancelled_df[[\"crate_id\", \"consignment_id\", \"cancelled_ts\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "\n", "final_20 = final_19.merge(cancelled_df_join, how=\"left\", on=[\"crate_id\", \"consignment_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "e58a9ea5-8e06-48b8-9b80-4bc2bfa7c0cf", "metadata": {}, "outputs": [], "source": ["final = final_20.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "0a78f8ba-af31-4be2-b221-4825e8d025c7", "metadata": {}, "outputs": [], "source": ["class ConsignmentContainerType:\n", "    COLD = \"COLD\"\n", "    PERM = \"PERM\"\n", "    PR = \"PR\"\n", "    TEMP = \"TEMP\"\n", "    CASE = \"CASE\"\n", "\n", "\n", "def get_crate_type_from_crate_id(crate_id):\n", "    if ConsignmentContainerType.PERM in crate_id or ConsignmentContainerType.PR in crate_id:\n", "        return ConsignmentContainerType.PERM\n", "    elif ConsignmentContainerType.COLD in crate_id:\n", "        return ConsignmentContainerType.COLD\n", "    elif ConsignmentContainerType.CASE in crate_id:\n", "        return ConsignmentContainerType.CASE\n", "    else:\n", "        return ConsignmentContainerType.TEMP\n", "\n", "\n", "final[\"crate_type\"] = final[\"crate_id\"].apply(get_crate_type_from_crate_id)"]}, {"cell_type": "code", "execution_count": null, "id": "ba6528ed-4b41-4e15-9d72-d4ea42028270", "metadata": {"tags": []}, "outputs": [], "source": ["key_timestamp_columns = [\n", "    \"billed_ts\",\n", "    \"dispatch_ts\",\n", "    \"delivered_correct_store_ts\",\n", "    \"grn_complete_ts\",\n", "    \"grn_missing_ts\",\n", "    \"grn_progress_ts\",\n", "    \"grn_pending_ts\",\n", "    \"missing_transit_ts\",\n", "    \"miss_transit_wrong_unload_ts\",\n", "    \"miss_transit_unloaded_corr_store_wrong_cons_ts\",\n", "    \"grn_try_timestamp\",\n", "    \"b2b_scan_miss_transit_ts\",\n", "    \"missing_source_ts\",\n", "    \"miss_source_unload_correct_ts\",\n", "    \"miss_source_wrong_unload_ts\",\n", "    \"b2b_scan_miss_source_ts\",\n", "    \"billed_ts_billed_not_dispatch\",\n", "    \"billed_not_dispatch_unloaded_corrent_ts\",\n", "    \"billed_not_dispatch_wrong_unload_ts\",\n", "    \"b2b_scan_billed_not_dispatch_ts\",\n", "    \"in_transit_ts\",\n", "    \"cancelled_ts\",\n", "]\n", "\n", "for col in key_timestamp_columns:\n", "    final[col] = pd.to_datetime(final[col], errors=\"coerce\").dt.date\n", "\n", "\n", "def count_crates(df, date, wh_outlet_id, crate_type, end_date):\n", "    if wh_outlet_id == \"overall\" and crate_type != \"overall\":\n", "        filtered_df = df[(df[\"crate_type\"] == crate_type) & (df[\"billed_ts\"] == date)]\n", "    elif crate_type == \"overall\" and wh_outlet_id != \"overall\":\n", "        filtered_df = df[(df[\"wh_outlet_id\"] == wh_outlet_id) & (df[\"billed_ts\"] == date)]\n", "    elif crate_type == \"overall\" and wh_outlet_id == \"overall\":\n", "        filtered_df = df[df[\"billed_ts\"] == date]\n", "    else:\n", "        filtered_df = df[\n", "            (df[\"wh_outlet_id\"] == wh_outlet_id)\n", "            & (df[\"crate_type\"] == crate_type)\n", "            & (df[\"billed_ts\"] == date)\n", "        ]\n", "\n", "    def count_unique_crates(condition):\n", "        return (\n", "            filtered_df[condition]\n", "            .drop_duplicates(subset=[\"crate_id\", \"billing_entity_id\", \"billing_entity_type\"])\n", "            .shape[0]\n", "        )\n", "\n", "    counts = {\n", "        \"Billed Crates\": count_unique_crates(filtered_df[\"billed_ts\"].notna()),\n", "        \"Dispatched Crates\": count_unique_crates(\n", "            (filtered_df[\"dispatch_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"dispatch_ts\"] < end_date)\n", "            & filtered_df[\"dispatch_ts\"].notna()\n", "            & (filtered_df[\"state\"] == \"DISPATCHED\")\n", "        ),\n", "        \"Delivered Crates (Dispatched and unloaded at correct store)\": count_unique_crates(\n", "            (filtered_df[\"delivered_correct_store_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"delivered_correct_store_ts\"] < end_date)\n", "            & filtered_df[\"delivered_correct_store_ts\"].notna()\n", "        ),\n", "        \"GRN completed\": count_unique_crates(\n", "            (filtered_df[\"grn_complete_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"grn_complete_ts\"] < end_date)\n", "            & filtered_df[\"grn_complete_ts\"].notna()\n", "        ),\n", "        \"Missing while GRN\": count_unique_crates(\n", "            (filtered_df[\"grn_missing_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"grn_missing_ts\"] < end_date)\n", "            & filtered_df[\"grn_missing_ts\"].notna()\n", "        ),\n", "        \"GRN in progress\": count_unique_crates(\n", "            (filtered_df[\"grn_progress_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"grn_progress_ts\"] < end_date)\n", "            & filtered_df[\"grn_progress_ts\"].notna()\n", "        ),\n", "        \"GRN pending\": count_unique_crates(\n", "            (filtered_df[\"grn_pending_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"grn_pending_ts\"] < end_date)\n", "            & filtered_df[\"grn_pending_ts\"].notna()\n", "        ),\n", "        \"Missing in Transit\": count_unique_crates(\n", "            (filtered_df[\"missing_transit_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"missing_transit_ts\"] < end_date)\n", "            & filtered_df[\"missing_transit_ts\"].notna()\n", "        ),\n", "        \"Dispatched and unloaded at other store\": count_unique_crates(\n", "            (filtered_df[\"miss_transit_wrong_unload_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"miss_transit_wrong_unload_ts\"] < end_date)\n", "            & filtered_df[\"miss_transit_wrong_unload_ts\"].notna()\n", "        ),\n", "        \"Dispatched and unloaded at same store wrong consig\": count_unique_crates(\n", "            (\n", "                filtered_df[\"miss_transit_unloaded_corr_store_wrong_cons_ts\"]\n", "                >= filtered_df[\"billed_ts\"]\n", "            )\n", "            & (filtered_df[\"miss_transit_unloaded_corr_store_wrong_cons_ts\"] < end_date)\n", "            & filtered_df[\"miss_transit_unloaded_corr_store_wrong_cons_ts\"].notna()\n", "        ),\n", "        \"GRN scan Tried\": count_unique_crates(\n", "            (filtered_df[\"grn_try_timestamp\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"grn_try_timestamp\"] < end_date)\n", "            & filtered_df[\"grn_try_timestamp\"].notna()\n", "        ),\n", "        \"Missing in Transit but B2B done at source\": count_unique_crates(\n", "            (filtered_df[\"b2b_scan_miss_transit_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"b2b_scan_miss_transit_ts\"] < end_date)\n", "            & filtered_df[\"b2b_scan_miss_transit_ts\"].notna()\n", "        ),\n", "        \"Missing at Source\": count_unique_crates(\n", "            (filtered_df[\"missing_source_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"missing_source_ts\"] < end_date)\n", "            & filtered_df[\"missing_source_ts\"].notna()\n", "        ),\n", "        \"Missing at Source but unloaded at correct store\": count_unique_crates(\n", "            (filtered_df[\"miss_source_unload_correct_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"miss_source_unload_correct_ts\"] < end_date)\n", "            & filtered_df[\"miss_source_unload_correct_ts\"].notna()\n", "        ),\n", "        \"Missing at Source but unloaded at wrong store\": count_unique_crates(\n", "            (filtered_df[\"miss_source_wrong_unload_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"miss_source_wrong_unload_ts\"] < end_date)\n", "            & filtered_df[\"miss_source_wrong_unload_ts\"].notna()\n", "        ),\n", "        \"Missing at Source but B2B done at source\": count_unique_crates(\n", "            (filtered_df[\"b2b_scan_miss_source_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"b2b_scan_miss_source_ts\"] < end_date)\n", "            & filtered_df[\"b2b_scan_miss_source_ts\"].notna()\n", "        ),\n", "        \"Billed but not Dispatched\": count_unique_crates(\n", "            (filtered_df[\"billed_ts_billed_not_dispatch\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"billed_ts_billed_not_dispatch\"] < end_date)\n", "            & filtered_df[\"billed_ts_billed_not_dispatch\"].notna()\n", "        ),\n", "        \"Billed but not Dispatched and (unloaded at correct store)\": count_unique_crates(\n", "            (filtered_df[\"billed_not_dispatch_unloaded_corrent_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"billed_not_dispatch_unloaded_corrent_ts\"] < end_date)\n", "            & filtered_df[\"billed_not_dispatch_unloaded_corrent_ts\"].notna()\n", "        ),\n", "        \"Billed but not Dispatched and (unloaded at wrong store)\": count_unique_crates(\n", "            (filtered_df[\"billed_not_dispatch_wrong_unload_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"billed_not_dispatch_wrong_unload_ts\"] < end_date)\n", "            & filtered_df[\"billed_not_dispatch_wrong_unload_ts\"].notna()\n", "        ),\n", "        \"Billed but not Dispatched but B2B done at source\": count_unique_crates(\n", "            (filtered_df[\"b2b_scan_billed_not_dispatch_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"b2b_scan_billed_not_dispatch_ts\"] < end_date)\n", "            & filtered_df[\"b2b_scan_billed_not_dispatch_ts\"].notna()\n", "        ),\n", "        \"In Transit Crates\": count_unique_crates(\n", "            (filtered_df[\"in_transit_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"in_transit_ts\"] < end_date)\n", "            & filtered_df[\"in_transit_ts\"].notna()\n", "        ),\n", "        \"Cancelled Consignments\": count_unique_crates(\n", "            (filtered_df[\"cancelled_ts\"] >= filtered_df[\"billed_ts\"])\n", "            & (filtered_df[\"cancelled_ts\"] < end_date)\n", "            & filtered_df[\"cancelled_ts\"].notna()\n", "        ),\n", "    }\n", "    return counts\n", "\n", "\n", "billed_dispatch_df[\"billed_ts\"] = pd.to_datetime(billed_dispatch_df[\"billed_ts\"])\n", "\n", "start_date = billed_dispatch_df[\"billed_ts\"].min().date()\n", "end_date = current_date_ist\n", "date_range = [start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)]\n", "\n", "end_comparison_date = pd.to_datetime(current_date_ist + timedelta(days=1))\n", "\n", "\n", "wh_outlet_ids = billed_dispatch_df[\"wh_outlet_id\"].unique().tolist() + [\"overall\"]\n", "\n", "crate_types = final[\"crate_type\"].unique().tolist() + [\"overall\"]\n", "\n", "results = []\n", "for date in date_range:\n", "    for wh_outlet_id in wh_outlet_ids:\n", "        for crate_type in crate_types:\n", "            counts = count_crates(final, date, wh_outlet_id, crate_type, end_comparison_date)\n", "            results.append(\n", "                {\n", "                    \"Date\": date,\n", "                    \"WH Outlet ID\": wh_outlet_id,\n", "                    \"Crate Type\": crate_type,\n", "                    **counts,\n", "                }\n", "            )\n", "\n", "result_final = pd.DataFrame(results)\n", "\n", "result_final = result_final.merge(outlet_name_df, how=\"left\", left_on=\"WH Outlet ID\", right_on=\"id\")\n", "result_final = result_final.drop([\"id\", \"wh_type\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "7e6fa911-9f3e-470b-8331-a5ac933568ca", "metadata": {"tags": []}, "outputs": [], "source": ["cols = list(result_final.columns)\n", "new_order = cols[:-1]\n", "new_order.insert(2, cols[-1])\n", "\n", "result_final = result_final[new_order]\n", "result_final = result_final.rename(columns={\"name\": \"WH name\"})\n", "\n", "# cols = list(result_final.columns)\n", "# new_order = cols[:-1]\n", "# new_order.insert(2, cols[-1])\n", "\n", "# result_final = result_final[new_order]\n", "\n", "result_final[\"Dispatched and not unloaded at all\"] = (\n", "    result_final[\"Missing in Transit\"]\n", "    - result_final[\"Dispatched and unloaded at other store\"]\n", "    - result_final[\"Dispatched and unloaded at same store wrong consig\"]\n", "    - result_final[\"GRN scan Tried\"]\n", "    - result_final[\"Missing in Transit but B2B done at source\"]\n", ")\n", "result_final[\"Missing at Source and not unloaded anywhere\"] = (\n", "    result_final[\"Missing at Source\"]\n", "    - result_final[\"Missing at Source but unloaded at correct store\"]\n", "    - result_final[\"Missing at Source but unloaded at wrong store\"]\n", "    - result_final[\"Missing at Source but B2B done at source\"]\n", ")\n", "result_final[\"Billed but not Dispatched and (not unloaded anywhere)\"] = (\n", "    result_final[\"Billed but not Dispatched\"]\n", "    - result_final[\"Billed but not Dispatched and (unloaded at correct store)\"]\n", "    - result_final[\"Billed but not Dispatched and (unloaded at wrong store)\"]\n", "    - result_final[\"Billed but not Dispatched but B2B done at source\"]\n", ")\n", "\n", "result_final[\"WH name\"] = result_final[\"WH name\"].fillna(\"overall\")\n", "# result_final['wh_type'] = result_final['wh_type'].fillna('overall')"]}, {"cell_type": "code", "execution_count": null, "id": "929d36cb-a79a-409d-ab45-57c54a5f5779", "metadata": {}, "outputs": [], "source": ["# unpivoted_df = result_final.melt(id_vars=['Date', 'WH Outlet ID', 'WH name', 'wh_type', 'Crate Type'], var_name='metric', value_name='value')\n", "unpivoted_df = result_final.melt(\n", "    id_vars=[\"Date\", \"WH Outlet ID\", \"WH name\", \"Crate Type\"],\n", "    var_name=\"metric\",\n", "    value_name=\"value\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "20fcae36-db06-4334-ab33-110ce4f2b034", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1vUS5htsabn3QRH9UJdZCAilfckkllnBR6ifxv4tP8lA\"\n", "pb.to_sheets(unpivoted_df, sheet_id, \"raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "46ec63fe-b490-4cc8-b3e3-a24977755734", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(result_final, \"1R63WKF1MIOEZpMFDQlBZ-PtWTgBGBk7tPjkk4NFWweA\", \"forward_raw\")"]}, {"cell_type": "markdown", "id": "e2d74da5-363a-44b3-8955-28a987681292", "metadata": {}, "source": ["### RCA raw data push"]}, {"cell_type": "code", "execution_count": null, "id": "d4954783-30e0-42b7-8cb6-e46d618e1773", "metadata": {}, "outputs": [], "source": ["sheet_id_rca = \"1EzzpNPQNmzOQkPWx6yIoH3ZM-W8ezmlf9rA1O3uTy4g\""]}, {"cell_type": "code", "execution_count": null, "id": "50866d70-ece2-406a-9ada-cd8caffb5fe6", "metadata": {}, "outputs": [], "source": ["dispatch_rca = final_20[~final_20[\"in_transit_ts\"].isnull()]\n", "dispatch_rca = (\n", "    dispatch_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"in_transit_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(dispatch_rca, \"12u9m6YyYTMfKody4QbL7ZRj0i_RJJFmpU9xnRhoJpB4\", \"fwd_dispatch\")"]}, {"cell_type": "code", "execution_count": null, "id": "c3a47e4b-9536-416f-adff-331baa9026ba", "metadata": {}, "outputs": [], "source": ["# in_transit_rca"]}, {"cell_type": "code", "execution_count": null, "id": "6f42c6b5-b7c6-4d08-8822-78dfc5cb1634", "metadata": {}, "outputs": [], "source": ["in_transit_rca = final_20[~final_20[\"in_transit_ts\"].isnull()]\n", "in_transit_rca = (\n", "    in_transit_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"in_transit_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(in_transit_rca, sheet_id_rca, \"in_transit_rca\")"]}, {"cell_type": "code", "execution_count": null, "id": "e6d4e718-afe3-4470-881e-adf68af8875d", "metadata": {}, "outputs": [], "source": ["cancelled_consig_rca = final_20[~final_20[\"cancelled_ts\"].isnull()]\n", "cancelled_consig_rca = (\n", "    cancelled_consig_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"cancelled_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(cancelled_consig_rca, sheet_id_rca, \"cancelled_consig_rca\")"]}, {"cell_type": "code", "execution_count": null, "id": "ce68ad89-d5ea-4f7c-b19c-fa66f1fc2b61", "metadata": {}, "outputs": [], "source": ["miss_transit_rca = final_20[~final_20[\"missing_transit_ts\"].isnull()]\n", "miss_transit_rca = (\n", "    miss_transit_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"missing_transit_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "pb.to_sheets(miss_transit_rca, sheet_id_rca, \"missing_in_transit_rca\")"]}, {"cell_type": "code", "execution_count": null, "id": "d24251ea-a088-466d-9b05-40ccabb622a8", "metadata": {}, "outputs": [], "source": ["dispatched_unloaded_other_store_rca = (\n", "    miss_transit_unloaded_wrong_temp[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id_x\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_store_id\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id_x\", \"consignment_id_x\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "dispatched_unloaded_other_store_rca.columns = [\n", "    \"billing_date\",\n", "    \"billed_ts\",\n", "    \"dispatch_ts\",\n", "    \"wh_outlet_id\",\n", "    \"crate_id\",\n", "    \"ds_outlet_id\",\n", "    \"billing_entity_id\",\n", "    \"billing_entity_type\",\n", "    \"consignment_id\",\n", "    \"wrong_store_id\",\n", "    \"miss_transit_wrong_unload_ts\",\n", "]\n", "\n", "dispatched_unloaded_other_store_rca = dispatched_unloaded_other_store_rca.reset_index(drop=True)\n", "pb.to_sheets(\n", "    dispatched_unloaded_other_store_rca,\n", "    sheet_id_rca,\n", "    \"dispatched_unloaded_other_store_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7271715a-9ebe-46c9-8ab8-b95d07050cb1", "metadata": {}, "outputs": [], "source": ["dispatched_unloaded_same_store_wrong_consig_rca = final_20[\n", "    ~final_20[\"miss_transit_unloaded_corr_store_wrong_cons_ts\"].isnull()\n", "]\n", "dispatched_unloaded_same_store_wrong_consig_rca = (\n", "    dispatched_unloaded_same_store_wrong_consig_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"miss_transit_unloaded_corr_store_wrong_cons_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(\n", "    dispatched_unloaded_same_store_wrong_consig_rca,\n", "    sheet_id_rca,\n", "    \"dispatched_unloaded_same_store_wrong_consig_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "402bb8be-f23f-44d7-8674-46b23a862242", "metadata": {}, "outputs": [], "source": ["grn_scan_tried_rca = final_20[~final_20[\"grn_try_timestamp\"].isnull()]\n", "grn_scan_tried_rca = (\n", "    grn_scan_tried_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"grn_try_timestamp\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(grn_scan_tried_rca, sheet_id_rca, \"grn_scan_tried_rca\")"]}, {"cell_type": "code", "execution_count": null, "id": "f4bca985-8d04-490d-bcd4-806702d22b75", "metadata": {}, "outputs": [], "source": ["missing_transit_b2b_done_at_source_rca = final_20[~final_20[\"b2b_scan_miss_transit_ts\"].isnull()]\n", "missing_transit_b2b_done_at_source_rca = (\n", "    missing_transit_b2b_done_at_source_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"b2b_scan_miss_transit_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(\n", "    missing_transit_b2b_done_at_source_rca,\n", "    sheet_id_rca,\n", "    \"missing_transit_b2b_done_at_source_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aeb99bbf-6176-4a70-bfcf-1fcb9502bc52", "metadata": {}, "outputs": [], "source": ["dispatched_and_not_unloaded_at_all_rca = pd.merge(\n", "    miss_transit_rca,\n", "    dispatched_unloaded_other_store_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    # suffixes=[\"_x\", \"_a\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "da9d4f6b-6d5c-4e57-a22a-36343fb41a19", "metadata": {}, "outputs": [], "source": ["dispatched_and_not_unloaded_at_all_rca = miss_transit_rca.merge(\n", "    dispatched_unloaded_other_store_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    suffixes=[\"_x\", \"_a\"],\n", ")\n", "dispatched_and_not_unloaded_at_all_rca = dispatched_and_not_unloaded_at_all_rca[\n", "    dispatched_and_not_unloaded_at_all_rca[\"miss_transit_wrong_unload_ts\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "dispatched_and_not_unloaded_at_all_rca = dispatched_and_not_unloaded_at_all_rca.merge(\n", "    dispatched_unloaded_same_store_wrong_consig_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    suffixes=[\"_p\", \"_b\"],\n", ")\n", "dispatched_and_not_unloaded_at_all_rca = dispatched_and_not_unloaded_at_all_rca[\n", "    dispatched_and_not_unloaded_at_all_rca[\n", "        \"miss_transit_unloaded_corr_store_wrong_cons_ts\"\n", "    ].isnull()\n", "].reset_index(drop=True)\n", "\n", "dispatched_and_not_unloaded_at_all_rca = dispatched_and_not_unloaded_at_all_rca.merge(\n", "    grn_scan_tried_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    suffixes=[\"_q\", \"_c\"],\n", ")\n", "dispatched_and_not_unloaded_at_all_rca = dispatched_and_not_unloaded_at_all_rca[\n", "    dispatched_and_not_unloaded_at_all_rca[\"grn_try_timestamp\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "dispatched_and_not_unloaded_at_all_rca = dispatched_and_not_unloaded_at_all_rca.merge(\n", "    missing_transit_b2b_done_at_source_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    suffixes=[\"_r\", \"_d\"],\n", ")\n", "dispatched_and_not_unloaded_at_all_rca = dispatched_and_not_unloaded_at_all_rca[\n", "    dispatched_and_not_unloaded_at_all_rca[\"b2b_scan_miss_transit_ts\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "dispatched_and_not_unloaded_at_all_rca = (\n", "    dispatched_and_not_unloaded_at_all_rca[\n", "        [\n", "            \"billing_date_x\",\n", "            \"billed_ts_x\",\n", "            \"dispatch_ts_x\",\n", "            \"wh_outlet_id_x\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id_x\",\n", "            \"billing_entity_type_x\",\n", "            \"consignment_id\",\n", "            \"missing_transit_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "dispatched_and_not_unloaded_at_all_rca.columns = [\n", "    \"billing_date\",\n", "    \"billed_ts\",\n", "    \"dispatch_ts\",\n", "    \"wh_outlet_id\",\n", "    \"crate_id\",\n", "    \"ds_outlet_id\",\n", "    \"billing_entity_id\",\n", "    \"billing_entity_type\",\n", "    \"consignment_id\",\n", "    \"missing_transit_ts\",\n", "]\n", "\n", "pb.to_sheets(\n", "    dispatched_and_not_unloaded_at_all_rca,\n", "    sheet_id_rca,\n", "    \"dispatched_and_not_unloaded_at_all_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6124b65a-bf37-4693-848b-1bd33a61c41e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1718c076-7fe2-48c2-9510-d8bddfdbf3a5", "metadata": {}, "outputs": [], "source": ["miss_source_rca = final_20[~final_20[\"missing_source_ts\"].isnull()]\n", "miss_source_rca = (\n", "    miss_source_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"missing_source_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(miss_source_rca, sheet_id_rca, \"miss_source_rca\")"]}, {"cell_type": "code", "execution_count": null, "id": "05d956cb-ad30-46b7-8043-418a46655b6e", "metadata": {}, "outputs": [], "source": ["miss_source_unloaded_correct_store_rca = final_20[\n", "    ~final_20[\"miss_source_unload_correct_ts\"].isnull()\n", "]\n", "miss_source_unloaded_correct_store_rca = (\n", "    miss_source_unloaded_correct_store_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"miss_source_unload_correct_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(\n", "    miss_source_unloaded_correct_store_rca,\n", "    sheet_id_rca,\n", "    \"miss_source_unloaded_correct_store_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "499a9c16-aff4-4e42-b964-91426cf27d9c", "metadata": {}, "outputs": [], "source": ["miss_source_unloaded_wrong_store_rca = miss_at_source_unloaded_wrong_temp\n", "miss_source_unloaded_wrong_store_rca = (\n", "    miss_source_unloaded_wrong_store_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_store_id\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id_x\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "miss_source_unloaded_wrong_store_rca.columns = [\n", "    \"billing_date\",\n", "    \"billed_ts\",\n", "    \"wh_outlet_id\",\n", "    \"crate_id\",\n", "    \"ds_outlet_id\",\n", "    \"billing_entity_id\",\n", "    \"billing_entity_type\",\n", "    \"consignment_id\",\n", "    \"wrong_store_id\",\n", "    \"miss_source_wrong_unload_ts\",\n", "]\n", "\n", "pb.to_sheets(\n", "    miss_source_unloaded_wrong_store_rca,\n", "    sheet_id_rca,\n", "    \"miss_source_unloaded_wrong_store_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35dcde94-c4ac-432b-944f-09b8ad4efdc4", "metadata": {}, "outputs": [], "source": ["missing_source_but_b2b_done_at_source_rca = final_20[~final_20[\"b2b_scan_miss_source_ts\"].isnull()]\n", "missing_source_but_b2b_done_at_source_rca = (\n", "    missing_source_but_b2b_done_at_source_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"b2b_scan_miss_source_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(\n", "    missing_source_but_b2b_done_at_source_rca,\n", "    sheet_id_rca,\n", "    \"missing_source_but_b2b_done_at_source_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7992421c-3fd2-4d75-84f9-ff5c1e0cdd0a", "metadata": {}, "outputs": [], "source": ["miss_source_not_unloaded_anywhere_rca = miss_source_rca.merge(\n", "    miss_source_unloaded_correct_store_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    suffixes=[\"_x\", \"_a\"],\n", ")\n", "miss_source_not_unloaded_anywhere_rca = miss_source_not_unloaded_anywhere_rca[\n", "    miss_source_not_unloaded_anywhere_rca[\"miss_source_unload_correct_ts\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "miss_source_not_unloaded_anywhere_rca = miss_source_not_unloaded_anywhere_rca.merge(\n", "    miss_source_unloaded_wrong_store_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    suffixes=[\"_p\", \"_b\"],\n", ")\n", "miss_source_not_unloaded_anywhere_rca = miss_source_not_unloaded_anywhere_rca[\n", "    miss_source_not_unloaded_anywhere_rca[\"miss_source_wrong_unload_ts\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "miss_source_not_unloaded_anywhere_rca = miss_source_not_unloaded_anywhere_rca.merge(\n", "    missing_source_but_b2b_done_at_source_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"consignment_id\"],\n", "    suffixes=[\"_q\", \"_c\"],\n", ")\n", "miss_source_not_unloaded_anywhere_rca = miss_source_not_unloaded_anywhere_rca[\n", "    miss_source_not_unloaded_anywhere_rca[\"b2b_scan_miss_source_ts\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "miss_source_not_unloaded_anywhere_rca = (\n", "    miss_source_not_unloaded_anywhere_rca[\n", "        [\n", "            \"billing_date_x\",\n", "            \"billed_ts_x\",\n", "            \"dispatch_ts_x\",\n", "            \"wh_outlet_id_x\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id_x\",\n", "            \"billing_entity_type_x\",\n", "            \"consignment_id\",\n", "            \"missing_source_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "miss_source_not_unloaded_anywhere_rca.columns = [\n", "    \"billing_date\",\n", "    \"billed_ts\",\n", "    \"dispatch_ts\",\n", "    \"wh_outlet_id\",\n", "    \"crate_id\",\n", "    \"ds_outlet_id\",\n", "    \"billing_entity_id\",\n", "    \"billing_entity_type\",\n", "    \"consignment_id\",\n", "    \"missing_source_ts\",\n", "]\n", "\n", "pb.to_sheets(\n", "    miss_source_not_unloaded_anywhere_rca,\n", "    sheet_id_rca,\n", "    \"miss_source_not_unloaded_anywhere_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "94902816-5345-455c-a5e5-87c0181f17f6", "metadata": {}, "outputs": [], "source": ["billed_not_dispatched_rca = final_20[~final_20[\"billed_ts_billed_not_dispatch\"].isnull()]\n", "billed_not_dispatched_rca = (\n", "    billed_not_dispatched_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"billed_ts_billed_not_dispatch\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(billed_not_dispatched_rca, sheet_id_rca, \"billed_not_dispatched_rca\")"]}, {"cell_type": "code", "execution_count": null, "id": "ef5b8d48-7db7-4b3e-b88d-a19d64e7ec5c", "metadata": {}, "outputs": [], "source": ["billed_not_dispatched_unloaded_correct_store_rca = final_20[\n", "    ~final_20[\"billed_not_dispatch_unloaded_corrent_ts\"].isnull()\n", "]\n", "billed_not_dispatched_unloaded_correct_store_rca = (\n", "    billed_not_dispatched_unloaded_correct_store_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"billed_not_dispatch_unloaded_corrent_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(\n", "    billed_not_dispatched_unloaded_correct_store_rca,\n", "    sheet_id_rca,\n", "    \"billed_not_dispatched_unloaded_correct_store_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "579f95df-2411-4e57-995e-1753eefa04f1", "metadata": {}, "outputs": [], "source": ["billed_not_dispatched_unloaded_wrong_store_rca = billed_not_dispatch_unload_wrong_temp\n", "billed_not_dispatched_unloaded_wrong_store_rca = (\n", "    billed_not_dispatched_unloaded_wrong_store_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"wrong_store_id\",\n", "            \"wrong_unload_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id_x\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "billed_not_dispatched_unloaded_wrong_store_rca.columns = [\n", "    \"billing_date\",\n", "    \"billed_ts\",\n", "    \"dispatch_ts\",\n", "    \"wh_outlet_id\",\n", "    \"crate_id\",\n", "    \"ds_outlet_id\",\n", "    \"billing_entity_id\",\n", "    \"billing_entity_type\",\n", "    \"consignment_id\",\n", "    \"wrong_store_id\",\n", "    \"billed_not_dispatch_wrong_unload_ts\",\n", "]\n", "\n", "pb.to_sheets(\n", "    billed_not_dispatched_unloaded_wrong_store_rca,\n", "    sheet_id_rca,\n", "    \"billed_not_dispatched_unloaded_wrong_store_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b619b46c-d8f8-4d0a-876a-7905c871f23a", "metadata": {}, "outputs": [], "source": ["billed_not_dispatched_but_b2b_done_at_source_rca = final_20[\n", "    ~final_20[\"b2b_scan_billed_not_dispatch_ts\"].isnull()\n", "]\n", "billed_not_dispatched_but_b2b_done_at_source_rca = (\n", "    billed_not_dispatched_but_b2b_done_at_source_rca[\n", "        [\n", "            \"billing_date\",\n", "            \"billed_ts\",\n", "            \"dispatch_ts\",\n", "            \"wh_outlet_id\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id\",\n", "            \"b2b_scan_billed_not_dispatch_ts\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .sort_values([\"billed_ts\", \"wh_outlet_id\", \"consignment_id\", \"crate_id\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n", "pb.to_sheets(\n", "    billed_not_dispatched_but_b2b_done_at_source_rca,\n", "    sheet_id_rca,\n", "    \"billed_not_dispatched_but_b2b_done_at_source_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4cd3756e-0036-4680-84f0-d063c277ac5b", "metadata": {}, "outputs": [], "source": ["billed_not_dispatched_not_unloaded_anywhere_rca = billed_not_dispatched_rca.merge(\n", "    billed_not_dispatched_unloaded_correct_store_rca,\n", "    how=\"left\",\n", "    on=[\"crate_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", "    suffixes=[\"_x\", \"_a\"],\n", ")\n", "billed_not_dispatched_not_unloaded_anywhere_rca = billed_not_dispatched_not_unloaded_anywhere_rca[\n", "    billed_not_dispatched_not_unloaded_anywhere_rca[\n", "        \"billed_not_dispatch_unloaded_corrent_ts\"\n", "    ].isnull()\n", "].reset_index(drop=True)\n", "\n", "billed_not_dispatched_not_unloaded_anywhere_rca = (\n", "    billed_not_dispatched_not_unloaded_anywhere_rca.merge(\n", "        billed_not_dispatched_unloaded_wrong_store_rca,\n", "        how=\"left\",\n", "        on=[\"crate_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", "        suffixes=[\"_p\", \"_b\"],\n", "    )\n", ")\n", "billed_not_dispatched_not_unloaded_anywhere_rca = billed_not_dispatched_not_unloaded_anywhere_rca[\n", "    billed_not_dispatched_not_unloaded_anywhere_rca[\"billed_not_dispatch_wrong_unload_ts\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "billed_not_dispatched_not_unloaded_anywhere_rca = (\n", "    billed_not_dispatched_not_unloaded_anywhere_rca.merge(\n", "        billed_not_dispatched_but_b2b_done_at_source_rca,\n", "        how=\"left\",\n", "        on=[\"crate_id\", \"billing_entity_id\", \"billing_entity_type\"],\n", "        suffixes=[\"_q\", \"_c\"],\n", "    )\n", ")\n", "billed_not_dispatched_not_unloaded_anywhere_rca = billed_not_dispatched_not_unloaded_anywhere_rca[\n", "    billed_not_dispatched_not_unloaded_anywhere_rca[\"b2b_scan_billed_not_dispatch_ts\"].isnull()\n", "].reset_index(drop=True)\n", "\n", "billed_not_dispatched_not_unloaded_anywhere_rca = (\n", "    billed_not_dispatched_not_unloaded_anywhere_rca[\n", "        [\n", "            \"billing_date_x\",\n", "            \"billed_ts_x\",\n", "            \"dispatch_ts_x\",\n", "            \"wh_outlet_id_x\",\n", "            \"crate_id\",\n", "            \"ds_outlet_id_x\",\n", "            \"billing_entity_id\",\n", "            \"billing_entity_type\",\n", "            \"consignment_id_x\",\n", "            \"billed_ts_billed_not_dispatch\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "billed_not_dispatched_not_unloaded_anywhere_rca.columns = [\n", "    \"billing_date\",\n", "    \"billed_ts\",\n", "    \"dispatch_ts\",\n", "    \"wh_outlet_id\",\n", "    \"crate_id\",\n", "    \"ds_outlet_id\",\n", "    \"billing_entity_id\",\n", "    \"billing_entity_type\",\n", "    \"consignment_id\",\n", "    \"billed_ts_billed_not_dispatch\",\n", "]\n", "\n", "pb.to_sheets(\n", "    billed_not_dispatched_not_unloaded_anywhere_rca,\n", "    sheet_id_rca,\n", "    \"billed_not_dispatched_not_unloaded_anywhere_rca\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "77074c20-9c16-403d-91a0-e0e41a774971", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1481bac0-fef8-4c09-9ea4-bc145ff441be", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}