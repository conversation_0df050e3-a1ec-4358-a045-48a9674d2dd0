alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: WH_churn
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: ramnar<PERSON>.ch<PERSON><PERSON>@grofers.com
  slack_id: U077JCPBFH6
path: povms/fleet/etl/WH_churn
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2025-04-15T00:00:00'
  interval: 0 */12 * * *
  start_date: '2024-11-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
