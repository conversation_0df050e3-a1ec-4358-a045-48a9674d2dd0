{"cells": [{"cell_type": "code", "execution_count": null, "id": "d2b820bf-1789-406c-b7a7-04d28f5940e9", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import numpy as npq\n", "import itertools\n", "import time\n", "import math\n", "from datetime import datetime, timedelta, date\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4256908f-16af-46f3-ad11-9a8a62a7e771", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_rows\", None)\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "# Set float format to avoid scientific notation\n", "pd.options.display.float_format = \"{:.2f}\".format"]}, {"cell_type": "code", "execution_count": null, "id": "1551c477-03ed-41a3-bf83-d120d48730e5", "metadata": {}, "outputs": [], "source": ["## New table\n", "trino_schema_name = \"supply_etls\"  # \"supply_etls\"  # \"povms\"\n", "trino_table_name = \"wh_churn\"\n", "\n", "# trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "bd9fe181-6429-4259-ad86-30379cf34bfd", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, conn):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, conn)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5b40bb29-e18d-4e7f-8d6b-afcdc5442d3a", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-rsto-flow-update\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c56cf765-de3e-44ba-bc3c-40fa675492fa", "metadata": {}, "outputs": [], "source": ["wh_employees = \"\"\"\n", "with base as(\n", "select\n", "        -- co.id as outlet,\n", "        wu.state as emp_state,\n", "        wu.operational_facility_id as facility_id,\n", "        job_location,\n", "        CASE \n", "            WHEN job_location = 'FC-Kolkata K4' THEN 4375\n", "            WHEN job_location = 'FC- Surat' THEN 4992\n", "            WHEN job_location = 'FC- Chennai-Sennerikuppam C3' THEN 4181\n", "            WHEN job_location = 'FC - Sonipat' THEN NULL\n", "            WHEN job_location = 'FC- Lucknow Banthara L4' THEN 2653\n", "            WHEN job_location = 'FC- Visakhapatnam V1 -Feeder' THEN 5079\n", "            WHEN job_location = 'FC- Talegaon' THEN NULL\n", "            WHEN job_location = 'FC- Ranchi' THEN 5093\n", "            WHEN job_location = 'FC-Goa' THEN 4498\n", "            WHEN job_location = 'FC- Chennai-Chettipedu C4' THEN 4516\n", "            WHEN job_location = 'FC- Ludhiana L2' THEN 5365\n", "            WHEN job_location = 'FC- Tatayawas J2' THEN 1725\n", "            WHEN job_location = 'FC- Indore' THEN 4325\n", "            WHEN job_location = 'FC - Dehradun - Kuanwala' THEN 4427\n", "            WHEN job_location = 'FC-Rajpura Feeder' THEN 4432\n", "            WHEN job_location = 'FC - Ahmedabad New Changodar' THEN 4888\n", "            WHEN job_location = 'FC -Dasna 2.0' THEN 4886\n", "            WHEN job_location = 'FC- Ahmedabad Changodar' THEN 337\n", "            WHEN job_location = 'FC - Chennai-Chettipedu C4' THEN NULL\n", "            WHEN job_location = 'FC- Ghaziabad Dasna' THEN 714\n", "            WHEN job_location = 'FC-Kuanwala' THEN NULL\n", "            WHEN job_location = 'FC - Coimbatore' THEN 5091\n", "            WHEN job_location = 'FC- Bangalore - Hoskote BLR 4' THEN 4518\n", "            WHEN job_location = 'FC- M9 Bhiwandi' THEN 4306\n", "            WHEN job_location = 'FC-Mumbai' THEN 4496\n", "            WHEN job_location = 'FC- Ludhiana - Kohara Road' THEN 2904\n", "            WHEN job_location = 'FC - Nagpur' THEN 4884\n", "            WHEN job_location = 'FC- Varanasi' THEN 5363\n", "            WHEN job_location = 'FC- Gr Noida' THEN 5002\n", "            WHEN job_location = 'FC- Lucknow Memora L5' THEN 5004\n", "            WHEN job_location = 'FC- Bangalore - Iggalur BLR 3' THEN 4170\n", "            WHEN job_location = 'FC- Gurugram Horizon Industrial Park' THEN 1644\n", "            WHEN job_location = 'FC - Gurugram - Farrukh Nagar' THEN 1104\n", "            ELSE NULL\n", "        END AS onboarding_outlet_id,\n", "        cp.*\n", "\n", "from ops_management.candidate_profile cp \n", "        left join retail.warehouse_user wu on wu.emp_id = cp.employee_id\n", "        --left join retail.console_outlet co on co.facility_id = wu.operational_facility_id\n", "    where\n", "    cp.lake_active_record \n", "    --and wu.lake_active_record\n", "    -- and wu.state = 'ACTIVE'\n", "    -- and co.lake_active_record\n", "    AND job_location like '%%FC%%'\n", "    --and (install_ts >= date('2024-10-01') or wu.state = 'ACTIVE')\n", "    \n", "    -- and \n", ")\n", "\n", "select * from base \n", "-- where onboarding_outlet_id <> outlet and emp_state = 'ACTIVE'\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "deff8997-69c4-4ae6-a8ba-a54337c8fb09", "metadata": {}, "outputs": [], "source": ["wh_employees1 = read_sql_query(wh_employees, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "7d7c6e89-e3a2-4b22-89f6-135ace7253d2", "metadata": {}, "outputs": [], "source": ["wh_employees1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "19866171-1cb0-4606-a26f-489bc208875f", "metadata": {}, "outputs": [], "source": ["# name_count = wh_employees1.groupby('phone_number_primary').size().reset_index(name = 'count1')\n", "# name_count.head()\n", "# name_count[name_count.count1>1].shape"]}, {"cell_type": "code", "execution_count": null, "id": "bc0d99ef-6037-40b8-aba9-e7540d827bbb", "metadata": {}, "outputs": [], "source": ["wh_employees1[wh_employees1[\"employee_id\"] == \"GCEZ50564\"]"]}, {"cell_type": "code", "execution_count": null, "id": "663d1a13-1ff9-40e8-a5e5-471e7deea7b1", "metadata": {}, "outputs": [], "source": ["# wh_employees1[(wh_employees1['last_working_date'].isnull()) & (wh_employees1['emp_state']=='INACTIVE')].head()\n", "# wh_employees1[(wh_employees1['emp_state']=='INACTIVE')]['employee_id'].nunique()\n", "# wh_employees1[wh_employees1['emp_state']=='INACTIVE'].shape\n", "# wh_employees1[wh_employees1['emp_state']=='ACTIVE'].head()"]}, {"cell_type": "code", "execution_count": null, "id": "25b0ab91-8cb3-4b78-a88b-b9861cb48bd8", "metadata": {}, "outputs": [], "source": ["# wh_employees1[wh_employees1['employee_id']=='GCEZ21662']"]}, {"cell_type": "code", "execution_count": null, "id": "f7ea859d-6f5a-4ed5-a31e-ff11b61baad1", "metadata": {}, "outputs": [], "source": ["duplicates = wh_employees1[wh_employees1.duplicated(subset=[\"employee_id\"], keep=False)]\n", "duplicates"]}, {"cell_type": "code", "execution_count": null, "id": "a93b6b32-7ad1-4eb3-b1ce-960a90b36d4d", "metadata": {}, "outputs": [], "source": ["# Ensure 'dob' column is in datetime format\n", "wh_employees1[\"dob\"] = pd.to_datetime(wh_employees1[\"dob\"], errors=\"coerce\")\n", "\n", "# Get today's date\n", "current_date = pd.Timestamp.today().date()\n", "\n", "# Calculate the actual age in years\n", "wh_employees1[\"Actual_age\"] = wh_employees1[\"dob\"].apply(\n", "    lambda dob: current_date.year\n", "    - dob.year\n", "    - ((current_date.month, current_date.day) < (dob.month, dob.day))\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ec2d2518-5499-4d8c-96d7-04e54ee91921", "metadata": {}, "outputs": [], "source": ["wh_employees1[\"interview_date\"] = pd.to_datetime(wh_employees1[\"interview_date\"], errors=\"coerce\")\n", "wh_employees1[\"joining_date\"] = pd.to_datetime(wh_employees1[\"joining_date\"], errors=\"coerce\")\n", "\n", "wh_employees1[\"time_to_join\"] = wh_employees1[\"joining_date\"] - wh_employees1[\"interview_date\"]"]}, {"cell_type": "code", "execution_count": null, "id": "32a817aa-4ac8-42c9-9bc0-ae1fa22fd110", "metadata": {}, "outputs": [], "source": ["wh_employees1[\"age_cat\"] = np.where(\n", "    wh_employees1[\"Actual_age\"] <= 20,\n", "    \"<=20\",\n", "    np.where(\n", "        (wh_employees1[\"Actual_age\"] <= 23) & (wh_employees1[\"Actual_age\"] >= 21),\n", "        \"21-23\",\n", "        np.where(\n", "            (wh_employees1[\"Actual_age\"] <= 30) & (wh_employees1[\"Actual_age\"] >= 24),\n", "            \"24-30\",\n", "            np.where(\n", "                (wh_employees1[\"Actual_age\"] <= 35) & (wh_employees1[\"Actual_age\"] >= 31),\n", "                \"31-35\",\n", "                np.where(\n", "                    (wh_employees1[\"Actual_age\"] <= 40) & (wh_employees1[\"Actual_age\"] >= 36),\n", "                    \"36-40\",\n", "                    \">40\",\n", "                ),\n", "            ),\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "54fa270e-2fa9-470d-972b-364eb7a10ac8", "metadata": {}, "outputs": [], "source": ["# wh_employees1.groupby(['highest_education', 'emp_state']).size().reset_index()\n", "# wh_employees1.groupby(['candidate_type', 'emp_state']).size().reset_index()\n", "# wh_employees1.groupby(['designation', 'emp_state']).size().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "c98d697a-052e-4254-b222-86192d28188a", "metadata": {}, "outputs": [], "source": ["location = \"\"\"\n", "\n", "select * from retail.console_outlet where \n", "active=1 and business_type_id in (1,12)\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b0401abd-adb4-4ff0-80aa-886869d787fd", "metadata": {}, "outputs": [], "source": ["location1 = read_sql_query(location, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "506f680a-1565-4142-8f61-19758f7fa268", "metadata": {}, "outputs": [], "source": ["location1 = location1[[\"name\", \"location\", \"facility_id\"]]\n", "# location1.sort_values(by = 'facility_id', ascending = True)\n", "# location1 = location1[location1['facility_id']==264]"]}, {"cell_type": "code", "execution_count": null, "id": "fcf5b2c4-8cd6-4bb7-849c-98301afd1f79", "metadata": {}, "outputs": [], "source": ["# Remove duplicates based on `location` and `facility_id`\n", "df_deduplicated = location1.drop_duplicates(subset=[\"location\", \"facility_id\"])\n", "df_deduplicated.shape"]}, {"cell_type": "code", "execution_count": null, "id": "16add9fa-f5d0-4a6e-a505-970edc2e8757", "metadata": {}, "outputs": [], "source": ["df_deduplicated = df_deduplicated[df_deduplicated[\"facility_id\"].isin(wh_employees1[\"facility_id\"])]\n", "df_deduplicated.sort_values(by=\"facility_id\", ascending=False)\n", "df_deduplicated.shape"]}, {"cell_type": "code", "execution_count": null, "id": "cc2c0eaf-0523-44d3-bb01-71da2dfc95f6", "metadata": {}, "outputs": [], "source": ["# Load the city-to-state mapping dataset\n", "city_to_state_df = pd.DataFrame(\n", "    {\n", "        \"location\": [\n", "            \"Ahmedabad\",\n", "            \"Jaipur\",\n", "            \"Bengaluru\",\n", "            \"Chennai\",\n", "            \"Mumbai\",\n", "            \"Indore\",\n", "            \"Sonipat\",\n", "            \"Kolkata\",\n", "            \"Dehradun\",\n", "            \"Goa\",\n", "            \"Nagpur\",\n", "            \"Surat\",\n", "            \"Noida\",\n", "            \"Lucknow\",\n", "            \"Visakhapatnam\",\n", "            \"Coimbatore\",\n", "            \"Ranchi\",\n", "            \"<PERSON>aran<PERSON>\",\n", "            \"Patna\",\n", "            \"Soukya road\",\n", "            \"<PERSON><PERSON><PERSON><PERSON>\",\n", "            \"Ghaziabad\",\n", "            \"Ludhiana\",\n", "            \"Pune\",\n", "            \"Rajpura\",\n", "            \"Hapur\",\n", "        ],\n", "        \"state\": [\n", "            \"Gujarat\",\n", "            \"Rajasthan\",\n", "            \"Karnataka\",\n", "            \"Tamil Nadu\",\n", "            \"Maharashtra\",\n", "            \"Madhya Pradesh\",\n", "            \"Haryana\",\n", "            \"West Bengal\",\n", "            \"Uttarakhand\",\n", "            \"Goa\",\n", "            \"Maharashtra\",\n", "            \"Gujarat\",\n", "            \"Uttar Pradesh\",\n", "            \"Uttar Pradesh\",\n", "            \"Andhra Pradesh\",\n", "            \"Tamil Nadu\",\n", "            \"Jharkhand\",\n", "            \"Uttar Pradesh\",\n", "            \"Bihar\",\n", "            \"Karnataka\",\n", "            \"Haryana\",\n", "            \"Uttar Pradesh\",\n", "            \"Punjab\",\n", "            \"Maharashtra\",\n", "            \"Punjab\",\n", "            \"Uttar Pradesh\",\n", "        ],\n", "    }\n", ")\n", "\n", "# Create a dictionary for mapping\n", "location_to_state = city_to_state_df.set_index(\"location\")[\"state\"].to_dict()\n", "\n", "# Map the `location` column to the `state` column\n", "df_deduplicated[\"state\"] = df_deduplicated[\"location\"].map(location_to_state)\n", "# df_deduplicated\n", "# df_deduplicated.sort_values(by = 'facility_id', ascending = True)"]}, {"cell_type": "code", "execution_count": null, "id": "61624d9d-8866-4c6a-9630-c642c3158697", "metadata": {}, "outputs": [], "source": ["df_deduplicated[\"facility_id\"].nunique()\n", "wh_employees1[~wh_employees1[\"facility_id\"].isin(df_deduplicated[\"facility_id\"])][\n", "    \"facility_id\"\n", "].head()\n", "# wh_employees1[wh_employees1['facility_id']==(1102)]"]}, {"cell_type": "code", "execution_count": null, "id": "7c6bd2c6-240e-426e-b4b7-415e8d4e3e49", "metadata": {}, "outputs": [], "source": ["wh_employees1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "37125d71-a913-45ae-a94a-cf88b387251d", "metadata": {}, "outputs": [], "source": ["# Perform the left join\n", "wh_employees1 = wh_employees1.merge(\n", "    df_deduplicated[[\"facility_id\", \"location\", \"state\"]].rename(\n", "        columns={\"location\": \"fc_city\", \"state\": \"fc_state\"}\n", "    ),\n", "    on=\"facility_id\",\n", "    how=\"left\",\n", ")\n", "wh_employees1.shape\n", "# wh_employees1.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "d19e1b9c-5bb8-452a-9a5a-2e275fd8f8c2", "metadata": {}, "outputs": [], "source": ["# wh_employees1[wh_employees1['facility_id']==264].groupby(['facility_id', 'fc_city' ,'emp_state']).size().reset_index()\n", "# wh_employees1.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "6369bedb-e2c2-4dfe-bb6b-d2690e73c05f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Data for the neighbor states\n", "data = {\n", "    \"State\": [\n", "        \"west bengal\",\n", "        \"west bengal\",\n", "        \"west bengal\",\n", "        \"west bengal\",\n", "        \"west bengal\",\n", "        \"uttarakhand\",\n", "        \"uttarakhand\",\n", "        \"uttar pradesh\",\n", "        \"uttar pradesh\",\n", "        \"uttar pradesh\",\n", "        \"uttar pradesh\",\n", "        \"uttar pradesh\",\n", "        \"uttar pradesh\",\n", "        \"tripura\",\n", "        \"tripura\",\n", "        \"telangana\",\n", "        \"telangana\",\n", "        \"telangana\",\n", "        \"telangana\",\n", "        \"tamil nadu\",\n", "        \"tamil nadu\",\n", "        \"sikkim\",\n", "        \"sikkim\",\n", "        \"rajasthan\",\n", "        \"rajasthan\",\n", "        \"rajasthan\",\n", "        \"rajasthan\",\n", "        \"rajasthan\",\n", "        \"punjab\",\n", "        \"punjab\",\n", "        \"punjab\",\n", "        \"punjab\",\n", "        \"odisha\",\n", "        \"odisha\",\n", "        \"odisha\",\n", "        \"nagaland\",\n", "        \"nagaland\",\n", "        \"nagaland\",\n", "        \"mizoram\",\n", "        \"mizoram\",\n", "        \"meghalaya\",\n", "        \"meghalaya\",\n", "        \"manipur\",\n", "        \"manipur\",\n", "        \"manipur\",\n", "        \"maharashtra\",\n", "        \"maharashtra\",\n", "        \"maharashtra\",\n", "        \"maharashtra\",\n", "        \"maharashtra\",\n", "        \"maharashtra\",\n", "        \"madhya pradesh\",\n", "        \"madhya pradesh\",\n", "        \"madhya pradesh\",\n", "        \"madhya pradesh\",\n", "        \"kerala\",\n", "        \"kerala\",\n", "        \"karnataka\",\n", "        \"karnataka\",\n", "        \"karnataka\",\n", "        \"karnataka\",\n", "        \"jharkhand\",\n", "        \"jharkhand\",\n", "        \"jharkhand\",\n", "        \"jharkhand\",\n", "        \"himachal pradesh\",\n", "        \"himachal pradesh\",\n", "        \"himachal pradesh\",\n", "        \"haryana\",\n", "        \"haryana\",\n", "        \"haryana\",\n", "        \"haryana\",\n", "        \"gujarat\",\n", "        \"gujarat\",\n", "        \"gujarat\",\n", "        \"goa\",\n", "        \"goa\",\n", "        \"chhattisgarh\",\n", "        \"chhattisgarh\",\n", "        \"chhattisgarh\",\n", "        \"chhattisgarh\",\n", "        \"chhattisgarh\",\n", "        \"bihar\",\n", "        \"bihar\",\n", "        \"bihar\",\n", "        \"assam\",\n", "        \"assam\",\n", "        \"assam\",\n", "        \"assam\",\n", "        \"assam\",\n", "        \"assam\",\n", "        \"assam\",\n", "        \"arunachal pradesh\",\n", "        \"arunachal pradesh\",\n", "        \"andhra pradesh\",\n", "        \"andhra pradesh\",\n", "        \"andhra pradesh\",\n", "        \"andhra pradesh\",\n", "    ],\n", "    \"Neighbour State\": [\n", "        \"odisha\",\n", "        \" jharkhand\",\n", "        \" bihar\",\n", "        \" sikkim\",\n", "        \" assam\",\n", "        \"himachal pradesh\",\n", "        \" uttar pradesh\",\n", "        \"bihar\",\n", "        \" jharkhand\",\n", "        \" madhya pradesh\",\n", "        \" rajasthan\",\n", "        \" haryana\",\n", "        \" uttarakhand\",\n", "        \"assam\",\n", "        \" mizoram\",\n", "        \"andhra pradesh\",\n", "        \" chhattisgarh\",\n", "        \" maharashtra\",\n", "        \" karnataka\",\n", "        \"andhra pradesh\",\n", "        \" karnataka\",\n", "        \"west bengal\",\n", "        \" assam\",\n", "        \"punjab\",\n", "        \" haryana\",\n", "        \" madhya pradesh\",\n", "        \" gujarat\",\n", "        \" uttar pradesh\",\n", "        \"jammu & kashmir\",\n", "        \" haryana\",\n", "        \" rajasthan\",\n", "        \" himachal pradesh\",\n", "        \"west bengal\",\n", "        \" chhattisgarh\",\n", "        \" andhra pradesh\",\n", "        \"assam\",\n", "        \" manipur\",\n", "        \" arunachal pradesh\",\n", "        \"assam\",\n", "        \" manipur\",\n", "        \"assam\",\n", "        \" tripura\",\n", "        \"nagaland\",\n", "        \" mizoram\",\n", "        \" assam\",\n", "        \"gujarat\",\n", "        \" madhya pradesh\",\n", "        \" chhattisgarh\",\n", "        \" telangana\",\n", "        \" karnataka\",\n", "        \" goa\",\n", "        \"uttar pradesh\",\n", "        \" rajasthan\",\n", "        \" chhattisgarh\",\n", "        \" maharashtra\",\n", "        \"karnataka\",\n", "        \" tamil nadu\",\n", "        \"goa\",\n", "        \" maharashtra\",\n", "        \" andhra pradesh\",\n", "        \" tamil nadu\",\n", "        \"west bengal\",\n", "        \" odisha\",\n", "        \" chhattisgarh\",\n", "        \" bihar\",\n", "        \"punjab\",\n", "        \" jammu & kashmir\",\n", "        \" uttarakhand\",\n", "        \"punjab\",\n", "        \" rajasthan\",\n", "        \" himachal pradesh\",\n", "        \" uttar pradesh\",\n", "        \"rajasthan\",\n", "        \" madhya pradesh\",\n", "        \" maharashtra\",\n", "        \"maharashtra\",\n", "        \" karnataka\",\n", "        \"uttar pradesh\",\n", "        \" jharkhand\",\n", "        \" odisha\",\n", "        \" madhya pradesh\",\n", "        \" maharashtra\",\n", "        \"jharkhand\",\n", "        \" west bengal\",\n", "        \" uttar pradesh\",\n", "        \"west bengal\",\n", "        \" meghalaya\",\n", "        \" arunachal pradesh\",\n", "        \" nagaland\",\n", "        \" manipur\",\n", "        \" mizoram\",\n", "        \" tripura\",\n", "        \"assam\",\n", "        \" nagaland\",\n", "        \"telangana\",\n", "        \" odisha\",\n", "        \" tamil nadu\",\n", "        \" karnataka\",\n", "    ],\n", "}\n", "\n", "# Verify lengths of lists\n", "len_state = len(data[\"State\"])\n", "len_neighbour_state = len(data[\"Neighbour State\"])\n", "\n", "print(f\"Length of 'State': {len_state}\")\n", "print(f\"Length of 'Neighbour State': {len_neighbour_state}\")\n", "\n", "\n", "# # Create DataFrame\n", "neighbour_state = pd.DataFrame(data)\n", "\n", "# # Display the DataFrame\n", "# neighbour_state.shape"]}, {"cell_type": "code", "execution_count": null, "id": "dc3fa5ec-d84e-45b3-9b88-7a5e9a02f89c", "metadata": {}, "outputs": [], "source": ["neighbour_state.shape\n", "neighbour_state[\"check\"] = \"YES\""]}, {"cell_type": "code", "execution_count": null, "id": "8f326874-9cd2-4698-a0bf-0d869446f395", "metadata": {}, "outputs": [], "source": ["neighbour_state.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a3cf4be9-8292-442a-88d5-e53a1a86b188", "metadata": {}, "outputs": [], "source": ["# df1_sorted = neighbour_state1.sort_values(by=neighbour_state1.columns.tolist()).reset_index(drop=True)\n", "# df2_sorted = neighbour_state.sort_values(by=neighbour_state.columns.tolist()).reset_index(drop=True)\n", "\n", "# if df1_sorted.equals(df2_sorted):\n", "#     print(\"The DataFrames are identical after sorting.\")\n", "# else:\n", "#     print(\"The DataFrames are not identical, even after sorting.\")"]}, {"cell_type": "code", "execution_count": null, "id": "822b7207-ff19-4a04-91a4-2fa257f87a4f", "metadata": {}, "outputs": [], "source": ["# neighbour_state = pd.read_csv(\"/home/<USER>/Shared/Parth Jaiswal/WH-employee-churn/Neigbour State - Sheet4.csv\")\n", "# neighbour_state['check'] = \"YES\"\n", "# neighbour_state.shape"]}, {"cell_type": "code", "execution_count": null, "id": "379f6d4a-91a1-4f82-93c9-618650f6120d", "metadata": {}, "outputs": [], "source": ["test = wh_employees1\n", "# [['employee_id','fc_city','fc_state','city','state']]\n", "test[\"fc_city\"] = test[\"fc_city\"].str.lower()\n", "test[\"fc_state\"] = test[\"fc_state\"].str.lower()\n", "test[\"city\"] = test[\"city\"].str.lower()\n", "test[\"state\"] = test[\"state\"].str.lower()\n", "test.shape"]}, {"cell_type": "code", "execution_count": null, "id": "482e2d77-dbe0-485a-9269-97cd865181b8", "metadata": {}, "outputs": [], "source": ["test = pd.merge(\n", "    test,\n", "    neighbour_state,\n", "    left_on=[\"fc_state\", \"state\"],\n", "    right_on=[\"State\", \"Neighbour State\"],\n", "    how=\"left\",\n", ")\n", "# test[test['check']].head(20)\n", "test.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5c3c21c9-31bc-43bf-95f0-a7ef16a774c2", "metadata": {}, "outputs": [], "source": ["# test[(test['fc_city']!=test['city']) & (test['fc_state']==test['state'])].shape\n", "# test[(test['fc_city']==test['city'])].shape\n", "# test[(test['state'] == test['Neighbour State'])].shape"]}, {"cell_type": "code", "execution_count": null, "id": "73dd5ca8-3dba-4a9e-8ef6-e2416c41e5a6", "metadata": {}, "outputs": [], "source": ["test[\"cat_dist\"] = np.where(\n", "    test[\"fc_city\"] == test[\"city\"],\n", "    \"local\",\n", "    np.where(\n", "        (test[\"fc_city\"] != test[\"city\"]) & (test[\"fc_state\"] == test[\"state\"]),\n", "        \"local_migrant\",\n", "        np.where((test[\"state\"] == test[\"Neighbour State\"]), \"neighbouring migrant\", \"migrant\"),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "faea21bf-eac0-4864-93cf-bc7bf3ded7a7", "metadata": {}, "outputs": [], "source": ["# test[test.cat_dist==\"migrant\"][['fc_city','fc_state','city','state','State','Neighbour State','check','cat_dist']].head(100)\n", "# test.groupby(['cat_dist', 'emp_state']).size().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "504736a1-e207-46ea-95c4-41cb289de364", "metadata": {}, "outputs": [], "source": ["# Assuming 'job_location' is duplicated and you want to drop the second occurrence\n", "test = test.loc[:, ~test.columns.duplicated()]"]}, {"cell_type": "code", "execution_count": null, "id": "86bc4a81-275b-448e-b852-f378cb1e2251", "metadata": {}, "outputs": [], "source": ["test[test[\"job_location\"] == \"FC - Gurugram - Farrukh Nagar\"].groupby(\n", "    [\"facility_id\", \"fc_city\", \"emp_state\"]\n", ").size().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "48251050-27ae-40a5-b141-b66af2b608d9", "metadata": {}, "outputs": [], "source": ["# test.groupby(['job_location','fc_city','fc_state','zone']).size().reset_index().sort_values(by='zone', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "eadf03a8-7deb-4347-8a12-d00263b9ea14", "metadata": {}, "outputs": [], "source": ["# Define the state-to-zone mapping\n", "state_to_zone = {\n", "    \"punjab\": \"North\",\n", "    \"haryana\": \"North\",\n", "    \"uttarakhand\": \"North\",\n", "    \"uttar pradesh\": \"North\",\n", "    \"rajasthan\": \"North\",\n", "    \"gujarat\": \"West\",\n", "    \"maharashtra\": \"West\",\n", "    \"madhya pradesh\": \"West\",\n", "    \"west bengal\": \"East\",\n", "    \"jharkhand\": \"East\",\n", "    \"bihar\": \"East\",\n", "    \"andhra pradesh\": \"South\",\n", "    \"tamil nadu\": \"South\",\n", "    \"karnataka\": \"South\",\n", "    \"kerala\": \"South\",\n", "    \"goa\": \"West\",\n", "    \"chhattisgarh\": \"East\",\n", "    \"odisha\": \"East\",\n", "    \"assam\": \"East\",\n", "    \"nagaland\": \"East\",\n", "    \"manipur\": \"East\",\n", "    \"tripura\": \"East\",\n", "    \"arunachal pradesh\": \"East\",\n", "    \"meghalaya\": \"East\",\n", "    \"mizoram\": \"East\",\n", "    \"sikkim\": \"East\",\n", "    \"telangana\": \"South\",\n", "    \"delhi\": \"North\",\n", "    \"jammu and kashmir\": \"North\",\n", "    \"ladakh\": \"North\",\n", "    \"himachal pradesh\": \"North\",\n", "}\n", "\n", "# Add a new column 'zone' based on the mapping\n", "test[\"zone\"] = test[\"fc_state\"].str.lower().map(state_to_zone)\n", "\n", "# Display the result\n", "test[[\"job_location\", \"fc_city\", \"fc_state\", \"zone\"]].head()"]}, {"cell_type": "code", "execution_count": null, "id": "a1fe5c9b-cc9f-48dc-889f-4a1bd94a334a", "metadata": {}, "outputs": [], "source": ["# test[['install_ts','employee_id','emp_state','current_status','facility_id','onboarding_outlet_id','gender','Actual_age','time_to_join','age_cat','is_married','cat_dist','highest_education','candidate_type','designation','joining_date','last_working_date','exit_reason','exit_type','vendor_name','job_location','job_location_allotted','fc_city','fc_state','city','state','zone']].head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "535bddcf-8225-44d5-9d05-389e35aa29d5", "metadata": {}, "outputs": [], "source": ["test[test[\"employee_id\"] == \"GCEZ52870\"][\n", "    [\"employee_id\", \"joining_date\", \"last_working_date\", \"install_ts\", \"emp_state\"]\n", "].head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "82bfb211-e3ad-4395-bf80-758367cf6232", "metadata": {}, "outputs": [], "source": ["# Convert dates to datetime\n", "test[\"joining_date\"] = pd.to_datetime(test[\"joining_date\"])\n", "test[\"last_working_date\"] = pd.to_datetime(test[\"last_working_date\"])\n", "test[\"install_ts\"] = pd.to_datetime(test[\"install_ts\"])\n", "\n", "# Max install_ts for active\n", "max_install_ts = test[\"install_ts\"].max()\n", "\n", "# List to store dummy rows\n", "dummy_rows = []\n", "\n", "# Generate dummy rows\n", "for _, row in test.iterrows():\n", "    if row[\"emp_state\"] == \"ACTIVE\":\n", "        # Generate dates from joining_date to max(install_ts)\n", "        date_range = pd.date_range(start=row[\"joining_date\"], end=max_install_ts, freq=\"D\")\n", "        for date in date_range:\n", "            dummy_rows.append({\"employee_id\": row[\"employee_id\"], \"date\": date, \"status\": \"ACTIVE\"})\n", "    else:\n", "        # Generate dates from joining_date to last_working_date (or just joining_date if missing)\n", "        end_date = (\n", "            row[\"last_working_date\"] if pd.notna(row[\"last_working_date\"]) else row[\"joining_date\"]\n", "        )\n", "        date_range = pd.date_range(start=row[\"joining_date\"], end=end_date, freq=\"D\")\n", "        for date in date_range:\n", "            dummy_rows.append(\n", "                {\"employee_id\": row[\"employee_id\"], \"date\": date, \"status\": \"INACTIVE\"}\n", "            )\n", "\n", "# Create DataFrame from dummy rows\n", "dummy_df = pd.DataFrame(dummy_rows)\n", "\n", "dummy_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2386fc3d-7c3c-4d09-8c27-d8f647614fc0", "metadata": {}, "outputs": [], "source": ["# dummy_df[dummy_df['status']=='INACTIVE'].head()"]}, {"cell_type": "code", "execution_count": null, "id": "dc9de106-5bc4-4121-a9c2-cd9cb9372916", "metadata": {}, "outputs": [], "source": ["# dummy_df[dummy_df['employee_id']=='GCEZ52870']"]}, {"cell_type": "code", "execution_count": null, "id": "4b64768e-016f-45d2-be02-9f75974b1d1d", "metadata": {}, "outputs": [], "source": ["dummy_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1a122c5f-754a-4886-8d5d-248b25040bad", "metadata": {}, "outputs": [], "source": ["employee_data = test[\n", "    [\n", "        \"install_ts\",\n", "        \"employee_id\",\n", "        \"emp_state\",\n", "        \"facility_id\",\n", "        \"onboarding_outlet_id\",\n", "        \"job_location\",\n", "        \"zone\",\n", "        \"gender\",\n", "        \"Actual_age\",\n", "        \"age_cat\",\n", "        \"time_to_join\",\n", "        \"is_married\",\n", "        \"cat_dist\",\n", "        \"highest_education\",\n", "        \"candidate_type\",\n", "        \"designation\",\n", "        \"joining_date\",\n", "        \"last_working_date\",\n", "        \"exit_reason\",\n", "        \"exit_type\",\n", "        \"vendor_name\",\n", "        \"fc_city\",\n", "        \"fc_state\",\n", "        \"city\",\n", "        \"state\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "98a22cbf-e363-460f-ba76-daabddbd1587", "metadata": {}, "outputs": [], "source": ["employee_data[employee_data[\"employee_id\"] == \"GCEZ52870\"].head()"]}, {"cell_type": "code", "execution_count": null, "id": "5052ee17-4aed-4821-b61a-248d0ef2d268", "metadata": {}, "outputs": [], "source": ["# Perform a left join\n", "result_df = pd.merge(dummy_df, employee_data, on=\"employee_id\", how=\"left\")\n", "result_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7ca7b046-aecf-443f-8472-6ac968f01dac", "metadata": {}, "outputs": [], "source": ["# result_df.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "92774933-8981-4a99-89e0-6367ac7ed580", "metadata": {}, "outputs": [], "source": ["result_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "348de1f7-b389-4504-a044-97542bbdfc60", "metadata": {}, "outputs": [], "source": ["duplicates = result_df[\n", "    result_df.duplicated(\n", "        subset=[\n", "            \"date\",\n", "            \"employee_id\",\n", "        ],\n", "        keep=False,\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "81a4e54d-a1fd-48b9-97e1-f37e230febca", "metadata": {}, "outputs": [], "source": ["duplicates"]}, {"cell_type": "code", "execution_count": null, "id": "0e259b0f-10e2-4b31-bd01-52fe4b6e72dc", "metadata": {}, "outputs": [], "source": ["result_df[\"time_to_join\"] = result_df[\"time_to_join\"].astype(str)\n", "result_df[\"is_married\"] = result_df[\"is_married\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "7b0cbde8-ba4b-4f0c-8f08-e0e1f6ec74c7", "metadata": {}, "outputs": [], "source": ["result_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "95ebc8fe-3b17-46cd-9b10-ea03e0a90fc3", "metadata": {}, "outputs": [], "source": ["# t = result_df[result_df.job_location=='FC - Gurugram - Farrukh Nagar']"]}, {"cell_type": "code", "execution_count": null, "id": "67e86134-5d56-4d72-a9d6-47cd394a9b27", "metadata": {}, "outputs": [], "source": ["# attendance = \"\"\"\n", "\n", "# select * from eveprod.userattendance where insert_ds_ist > '2022-09-10'\n", "\n", "# \"\"\"\n", "\n", "# attendance1 = read_sql_query(attendance, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "614748d0-3f9a-4ab5-9ac2-fb5082acf6ea", "metadata": {}, "outputs": [], "source": ["# attendance1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dc2a3742-0d6f-4128-ab53-1dbcabb9a950", "metadata": {}, "outputs": [], "source": ["# t2 = attendance1[attendance1.userid.isin(t['employee_id'])]"]}, {"cell_type": "code", "execution_count": null, "id": "5c840438-5245-4147-a2a1-8b208d96c0e0", "metadata": {}, "outputs": [], "source": ["# Convert 'attdatetime' to datetime and then extract only the date\n", "# t2['attdatetime'] = pd.to_datetime(t2['attdatetime']).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "14ab7271-5940-4ee6-87b0-e5aaa92990c0", "metadata": {}, "outputs": [], "source": ["# t2.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4e82bf07-506d-4875-9200-8ce1be266aa0", "metadata": {}, "outputs": [], "source": ["# t2.sort_values(by = 'attdatetime', ascending = False).head()"]}, {"cell_type": "code", "execution_count": null, "id": "a722a571-bf01-446a-9b41-8831613cd4ac", "metadata": {}, "outputs": [], "source": ["# t2['attdatetime'] = pd.to_datetime(t2['attdatetime'])\n", "# t2[t2['attdatetime'].dt.date == pd.to_datetime('2024-11-18').date()].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "185abdfb-f1d7-489d-89bf-c566d3d9dd58", "metadata": {}, "outputs": [], "source": ["# t2.groupby('')"]}, {"cell_type": "code", "execution_count": null, "id": "1f92ec47-0e0a-4926-886d-7a90654e7b83", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c7f335e3-4f1f-421b-a45d-b3e0a11953b0", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"employee_id\", \"type\": \"varchar\", \"description\": \"employee_id\"},\n", "        {\"name\": \"date\", \"type\": \"DATE\", \"description\": \"date\"},\n", "        {\n", "            \"name\": \"status\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"status\",\n", "        },\n", "        {\"name\": \"install_ts\", \"type\": \"TIMESTAMP(6)\", \"description\": \"install_ts\"},\n", "        {\n", "            \"name\": \"emp_state\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"emp_state\",\n", "        },\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"facility_id\",\n", "        },\n", "        {\n", "            \"name\": \"onboarding_outlet_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"onboarding_outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"job_location\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"job_location\",\n", "        },\n", "        {\"name\": \"zone\", \"type\": \"varchar\", \"description\": \"zone\"},\n", "        {\n", "            \"name\": \"gender\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"gender\",\n", "        },\n", "        {\"name\": \"Actual_age\", \"type\": \"integer\", \"description\": \"Actual_age\"},\n", "        {\"name\": \"age_cat\", \"type\": \"varchar\", \"description\": \"age_cat\"},\n", "        {\"name\": \"time_to_join\", \"type\": \"varchar\", \"description\": \"time_to_join\"},\n", "        {\"name\": \"is_married\", \"type\": \"varchar\", \"description\": \"is_married\"},\n", "        {\"name\": \"cat_dist\", \"type\": \"varchar\", \"description\": \"cat_dist\"},\n", "        {\n", "            \"name\": \"highest_education\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"highest_education\",\n", "        },\n", "        {\"name\": \"candidate_type\", \"type\": \"varchar\", \"description\": \"candidate_type\"},\n", "        {\"name\": \"designation\", \"type\": \"varchar\", \"description\": \"designation\"},\n", "        {\n", "            \"name\": \"joining_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"joining_date\",\n", "        },\n", "        {\n", "            \"name\": \"last_working_date\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"last_working_date\",\n", "        },\n", "        {\"name\": \"exit_reason\", \"type\": \"varchar\", \"description\": \"exit_reason\"},\n", "        {\n", "            \"name\": \"exit_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"exit_type\",\n", "        },\n", "        {\n", "            \"name\": \"vendor_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"vendor_name\",\n", "        },\n", "        {\"name\": \"fc_city\", \"type\": \"varchar\", \"description\": \"fc_city\"},\n", "        {\n", "            \"name\": \"fc_state\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"fc_state\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\n", "            \"name\": \"state\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"state\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"date\",\n", "        \"employee_id\",\n", "    ],  # list\n", "    \"partition_key\": [\"date\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"date\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"Base table for WH_Churn\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(result_df, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "e8889200-09a1-4a2d-821d-fa5000cfcf34", "metadata": {}, "outputs": [], "source": ["# result_df[result_df['employee_id']=='GCEZ52870'].head()"]}, {"cell_type": "code", "execution_count": null, "id": "4ebee1f2-1542-4460-94bc-10c669fc43b4", "metadata": {}, "outputs": [], "source": ["# result_df[result_df['status']=='INACTIVE'].head()"]}, {"cell_type": "code", "execution_count": null, "id": "a7154a9b-71ad-4536-85f4-dee66fb36bae", "metadata": {}, "outputs": [], "source": ["# result_df[result_df['date']>='2024-10-01'].shape"]}, {"cell_type": "code", "execution_count": null, "id": "49855d47-5274-4cba-a325-dc2b7a4a00cb", "metadata": {}, "outputs": [], "source": ["# result_df.to_csv(\"/home/<USER>/Shared/Parth <PERSON>wal/WH-employee-churn/final_employee_churn_data.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "d07e3dd1-9da1-4213-b02b-152cf89ea218", "metadata": {}, "outputs": [], "source": ["# result_df[result_df['status']=='ACTIVE'].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "b035037d-8f34-4b21-9bd2-22694e0c8255", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}