{"cells": [{"cell_type": "code", "execution_count": null, "id": "f9c12728-7ce7-4ca0-8185-97f03b65f0d8", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "from pytz import timezone\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "d45a1747-5b1b-45dc-8426-e8b209e371e4", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "09f36019-389f-450e-a627-b65342c70230", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "        create temporary table ars_job_runs as (\n", "        select run_id, \n", "            json_extract_path_text(simulation_params,'run') as run_type,\n", "            json_extract_path_text(simulation_params,'mode') as mode \n", "        from lake_ars.job_run \n", "        where run_id not in ('20210607116002', '20210607116006', '20210607116005', '20210607116003')\n", "            and started_at > (current_date-4) \n", "            and completed_at is not null\n", "            and simulation_params ilike '%%ars%%'\n", "            and simulation_params not like '%%hyperpure_run%%'\n", "            and simulation_params not like '%%mode%%'\n", "\n", "    );\n", "\n", "\n", "    create temporary table run_wise_resource_caps as (\n", "        select distinct b.backend_outlet_id, \n", "            b.frontend_outlet_id, \n", "            b.run_id, \n", "            b.picking_capacity_quantity,\n", "            b.picking_capacity_sku,\n", "            b.truck_load_capacity,\n", "            b.inward_capacity\n", "        from lake_ars.bulk_facility_transfer_days b\n", "            where truck_load_capacity > 0\n", "    );\n", "\n", "    create temporary table indent_report as (\n", "        select run_id, \n", "            backend_outlet_id, \n", "            frontend_outlet_id,\n", "            sto_date,\n", "            sum(total_item_weight_gm)/1000 as total_weight_kg\n", "        from (select run_id, \n", "                backend_outlet_id, \n", "                frontend_outlet_id,\n", "                sto_date,\n", "                indent.item_id,\n", "                sum(weight_in_gm) as item_weight,\n", "                sum(sto_quantity_post_truncation_in_case) as indent_qty,\n", "                (item_weight*indent_qty) as total_item_weight_gm\n", "            from lake_ars.transfers_optimization_results_v2 as indent\n", "            INNER JOIN lake_rpc.item_details id ON id.item_id = indent.item_id AND id.active = 1 AND id.approved = 1\n", "            where sto_date > (current_date- 4) \n", "            and weight_in_gm>0\n", "            group by 1,2,3,4,5)\n", "        group by 1,2,3,4\n", "    );\n", "\n", "    create temporary table final_utilisation as (\n", "        select ir.sto_date,\n", "            ir.run_id,\n", "            ir.backend_outlet_id,\n", "            ir.frontend_outlet_id,\n", "            jr.run_type,\n", "            jr.mode,\n", "            sum(ir.total_weight_kg) as total_weight,\n", "            sum(rc.picking_capacity_quantity) as total_picking_caps_qty,\n", "            sum(rc.picking_capacity_sku) as total_picking_caps_sku,\n", "            sum(rc.truck_load_capacity) as total_load_caps,\n", "            sum(rc.inward_capacity) as total_inw_caps\n", "\n", "        from indent_report ir \n", "        inner join run_wise_resource_caps rc on ir.run_id = rc.run_id and ir.backend_outlet_id = rc.backend_outlet_id and ir.frontend_outlet_id = rc.frontend_outlet_id\n", "        inner join ars_job_runs jr on ir.run_id = jr.run_id\n", "        group by 1,2,3,4,5,6\n", "    );\n", "\n", "    create temporary table active_assortment as (\n", "        select facility_id, \n", "            count(distinct item_id) as count_act_and_temp_act_items,\n", "            count(distinct case when master_assortment_substate_id = 1 then item_id end) as count_act_items,\n", "            count(distinct case when master_assortment_substate_id = 3 then item_id end) as count_temp_act_items\n", "        from lake_rpc.product_facility_master_assortment\n", "        where master_assortment_substate_id in (1,3)\n", "        and active = 1\n", "        group by 1\n", "    );\n", "\n", "\n", "\n", "    create temporary table availability_proxy as (\n", "        select tor.run_id, \n", "            sto_date,\n", "            backend_outlet_id,\n", "            frontend_outlet_id,\n", "            frontend_facility_id,\n", "            sum(case when sto_quantity > 0 then 1 else 0 end) as count_active_item_ids_in_run, \n", "            count(distinct case when oiu2.final_inventory = 0 and oiu.final_inventory > 0 and sto_quantity_post_truncation_in_case = 0 then tor.item_id end) as count_oos_item_ids_in_run,\n", "            sum(oiu.final_inventory) as total_backend_inv,\n", "            sum(oiu2.final_inventory) as total_frontend_inv,\n", "            sum(sto_quantity) as total_sto_quantity\n", "        from lake_ars.transfers_optimization_results_v2 tor\n", "        inner join lake_ars.outlet_item_universe oiu on tor.run_id = oiu.run_id and tor.backend_outlet_id = oiu.outlet_id and tor.item_id = oiu.item_id\n", "        inner join lake_ars.outlet_item_universe oiu2 on tor.run_id = oiu2.run_id and tor.frontend_outlet_id = oiu2.outlet_id and tor.item_id = oiu2.item_id\n", "        inner join ars_job_runs jr on tor.run_id = jr.run_id\n", "        where sto_date > (current_date - 4) \n", "        group by 1,2,3,4,5\n", "\n", "    );\n", "\n", "\n", "\n", "\n", "\n", "    select ap.sto_date,\n", "        ap.run_id,\n", "        ap.backend_outlet_id,\n", "        co1.name as backend_name,\n", "        ap.frontend_outlet_id,\n", "        ap.frontend_facility_id,\n", "        co.name as frontend_name,\n", "        ap.count_active_item_ids_in_run,\n", "        ap.count_oos_item_ids_in_run,\n", "        aa.count_act_items,\n", "        aa.count_temp_act_items,\n", "        aa.count_act_and_temp_act_items,\n", "        ap.total_backend_inv,\n", "        ap.total_frontend_inv,\n", "        ap.total_sto_quantity,\n", "        fu.run_type,\n", "        fu.mode,\n", "        fu.total_weight,\n", "        fu.total_picking_caps_qty,\n", "        fu.total_picking_caps_sku,\n", "        fu.total_load_caps,\n", "        fu.total_inw_caps\n", "    from availability_proxy ap\n", "    left join final_utilisation fu on ap.run_id = fu.run_id and ap.backend_outlet_id = fu.backend_outlet_id and ap.frontend_outlet_id = fu.frontend_outlet_id  \n", "    left join active_assortment aa on ap.frontend_facility_id = aa.facility_id\n", "    left join lake_retail.console_outlet co on ap.frontend_outlet_id = co.id\n", "    left join lake_retail.console_outlet co1 on ap.backend_outlet_id = co1.id\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "    \n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "12108ba0-b629-4a72-ba58-6a751583fbe6", "metadata": {}, "outputs": [], "source": ["dataset = read_sql_query(sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "b87bcd12-6a3b-4912-925f-8b3e85ea2335", "metadata": {}, "outputs": [], "source": ["dataset.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "a0a4c1a3-0c84-45b2-a8cf-a3db1f190990", "metadata": {}, "outputs": [], "source": ["current_date_time = datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "id": "e04b2e62-dc78-45bf-bea1-f5a0e1ea8a64", "metadata": {}, "outputs": [], "source": ["dataset[\"computed_at\"] = current_date_time"]}, {"cell_type": "code", "execution_count": null, "id": "0cb35c3f-4759-4c1b-9c60-3ccad819a77f", "metadata": {}, "outputs": [], "source": ["dataset.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e5c4a3cb-113a-419c-b849-f78f5075a9e3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1bb1d8d1-d38e-454e-a7aa-7008705b7412", "metadata": {}, "outputs": [], "source": ["dataset[\"sto_date\"] = pd.to_datetime(dataset[\"sto_date\"])\n", "\n", "dataset[\"count_active_item_ids_in_run\"] = dataset[\n", "    \"count_active_item_ids_in_run\"\n", "].astype(\"float\")\n", "dataset[\"count_oos_item_ids_in_run\"] = dataset[\"count_oos_item_ids_in_run\"].astype(\n", "    \"float\"\n", ")\n", "dataset[\"count_act_items\"] = dataset[\"count_act_items\"].astype(\"float\")\n", "dataset[\"count_temp_act_items\"] = dataset[\"count_temp_act_items\"].astype(\"float\")\n", "dataset[\"count_act_and_temp_act_items\"] = dataset[\n", "    \"count_act_and_temp_act_items\"\n", "].astype(\"float\")\n", "\n", "dataset[\"total_backend_inv\"] = dataset[\"total_backend_inv\"].astype(\"float\")\n", "dataset[\"total_frontend_inv\"] = dataset[\"total_frontend_inv\"].astype(\"float\")\n", "dataset[\"total_sto_quantity\"] = dataset[\"total_sto_quantity\"].astype(\"float\")\n", "\n", "\n", "dataset[\"total_picking_caps_qty\"] = dataset[\"total_picking_caps_qty\"].astype(\"float\")\n", "dataset[\"total_picking_caps_sku\"] = dataset[\"total_picking_caps_sku\"].astype(\"float\")\n", "dataset[\"total_inw_caps\"] = dataset[\"total_inw_caps\"].astype(\"float\")"]}, {"cell_type": "code", "execution_count": null, "id": "771a5b4e-4c9b-4232-9bb3-806159456e9d", "metadata": {}, "outputs": [], "source": ["dataset.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "c54b90dc-d2d3-4397-b7e2-ff545ca61465", "metadata": {}, "outputs": [], "source": ["kwargs_output = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"ars_transfer_stock_outs\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"sto_date\", \"type\": \"date\", \"description\": \"NA\"},\n", "        {\"name\": \"run_id\", \"type\": \"varchar\", \"description\": \"NA\"},\n", "        {\"name\": \"backend_outlet_id\", \"type\": \"bigint\", \"description\": \"NA\"},\n", "        {\"name\": \"backend_name\", \"type\": \"text\", \"description\": \"NA\"},\n", "        {\"name\": \"frontend_outlet_id\", \"type\": \"bigint\", \"description\": \"NA\"},\n", "        {\"name\": \"frontend_facility_id\", \"type\": \"bigint\", \"description\": \"NA\"},\n", "        {\"name\": \"frontend_name\", \"type\": \"text\", \"description\": \"NA\"},\n", "        {\"name\": \"count_active_item_ids_in_run\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"count_oos_item_ids_in_run\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"count_act_items\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"count_temp_act_items\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"count_act_and_temp_act_items\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"total_backend_inv\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"total_frontend_inv\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"total_sto_quantity\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"run_type\", \"type\": \"text\", \"description\": \"NA\"},\n", "        {\"name\": \"mode\", \"type\": \"text\", \"description\": \"NA\"},\n", "        {\"name\": \"total_weight\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"total_picking_caps_qty\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"total_picking_caps_sku\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"total_load_caps\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"total_inw_caps\", \"type\": \"float\", \"description\": \"NA\"},\n", "        {\"name\": \"computed_at\", \"type\": \"datetime\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\"sto_date\", \"backend_outlet_id\", \"frontend_outlet_id\", \"run_type\"],\n", "    # \"sortkey\": [\"at_date_ist\",\"traits__city_name\",\"keyword\"],\n", "    \"incremental_key\": \"sto_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Table to track ars_transfer_stock_outs\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "45ca9aa4-39ce-4c18-b0da-7fcaaa10f353", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(dataset, **kwargs_output)"]}, {"cell_type": "code", "execution_count": null, "id": "4440a807-b716-4b4b-a6e4-f759950bde47", "metadata": {}, "outputs": [], "source": ["print(\"Pushing Complete !! COOLs\")"]}, {"cell_type": "code", "execution_count": null, "id": "7cf10103-00e6-45ee-972c-23e7e12b8376", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}