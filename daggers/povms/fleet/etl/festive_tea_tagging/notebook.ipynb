{"cells": [{"cell_type": "code", "execution_count": null, "id": "5928e90c-2dcc-4cad-bfc5-cf72bf4ff141", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "3c8b3e31-d5e3-41c8-9858-aefed1ebf626", "metadata": {}, "outputs": [], "source": ["## New table\n", "trino_schema_name = \"supply_etls\"\n", "trino_table_name = \"festival_tea_tagging\"\n", "\n", "# Connecting with Trino\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "de1ec573-f085-429a-83aa-bccfe07be05c", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1VqHMvk8c0axfz_5EVEhM08YBohWwu3oQkNyi0jpt1xQ\"\n", "sheet_name = \"raw\"\n", "festival_tea_tagging_raw = pb.from_sheets(sheet_id, sheet_name)\n", "# festival_tea_tagging_raw = pd.read_csv(\n", "#     \"Festival wise Proposed - TEA TAGGING - raw (1).csv\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "0ec4e182-98c6-475c-9ad1-362ab478a178", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging = festival_tea_tagging_raw.fillna(0)\n", "festival_tea_tagging = festival_tea_tagging_raw.astype(\n", "    {\"outlet_id\": \"int\", \"proposed_be_facility_id\": \"int\", \"active\": \"int\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6a5d7fb7-b67d-4732-8c6a-0fb16f2e321a", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging = festival_tea_tagging.drop_duplicates(\n", "    subset=[\"outlet_id\", \"festival_name\", \"proposed_be_facility_id\"], keep=\"first\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "03769852-48f2-412d-8269-0b4967289901", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8ea65115-df34-49fb-a6bf-1230537af93e", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "91e97a3f-04a5-4525-9a9e-84cfa71cff8a", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging[\"festival_name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "cd0a3bee-299c-4e04-a023-e06116fdd62a", "metadata": {}, "outputs": [], "source": ["kwargs_output = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"festival_name\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"proposed_be_facility_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"active\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"festival_name\",\n", "        \"proposed_be_facility_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"festival_name\",\n", "    ],\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"festival tea tagging\",\n", "}\n", "\n", "pb.to_trino(festival_tea_tagging, **kwargs_output)\n", "print(\"Data Push Complete !! Cools\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}