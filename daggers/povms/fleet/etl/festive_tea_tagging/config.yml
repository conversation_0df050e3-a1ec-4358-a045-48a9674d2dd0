alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: festive_tea_tagging
dag_type: etl
escalation_priority: low
execution_timeout: 10
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U04JZTM4FC7
path: povms/fleet/etl/festive_tea_tagging
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2024-12-08T00:00:00'
  interval: 0 5,12 * * *
  start_date: '2024-09-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
