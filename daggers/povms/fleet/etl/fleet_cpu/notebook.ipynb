{"cells": [{"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_schema_name = \"supply_etls\"  # supply_etls playground\n", "trino_table_name = \"fleet_cpu\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e256a0c9-ed4d-4d5a-bfa2-b7fe9e9ab7a1", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c184dfb9-b500-4a62-9a0a-96faaa212d7b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "91fce0a6-8c63-47b4-99fe-5f41b0b600a1", "metadata": {}, "outputs": [], "source": ["## Facilities where <PERSON><PERSON> is live\n", "facility = [\n", "    1,\n", "    1320,\n", "    2006,\n", "    2076,\n", "    2078,\n", "    1873,\n", "    2412,\n", "    2124,\n", "    2470,\n", "    1206,\n", "    43,\n", "    1872,\n", "    2468,\n", "    2681,\n", "    2569,\n", "    2141,\n", "    2015,\n", "    2682,\n", "    2123,\n", "    1983,\n", "    1872,\n", "    2670,\n", "    2576,\n", "    2947,\n", "    2142,\n", "    603,\n", "]\n", "facilities = tuple(facility)\n", "facilities"]}, {"cell_type": "code", "execution_count": null, "id": "934cdd33-e135-486b-b5ec-e00b15927c65", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3cd733b5-6f27-4c7c-9f1d-e31d528b0eb7", "metadata": {}, "outputs": [], "source": ["cpu_query1 = \"\"\"\n", "\n", "\n", "with valid_ars_job_runs_extract as\n", "    (\n", "        select  distinct run_id, \n", "                cast(json_extract(simulation_params,'$.run') as varchar) as run_type,\n", "                cast(json_extract(simulation_params,'$.mode') as varchar) as mode \n", "        from ars.job_run \n", "        where run_id not in ('20210607116002', '20210607116006', '20210607116005', '20210607116003')\n", "            and started_at > cast((current_date-interval '180' day) as timestamp) \n", "            and completed_at is not null\n", "    ),\n", "\n", "valid_ars_job_runs as \n", "    (\n", "        select  run_id,\n", "                run_type,\n", "                mode\n", "        from valid_ars_job_runs_extract\n", "        where run_type in ('ars_lite')\n", "            and (mode in ('','frozen','milk','fnv','perishable') or mode is null)\n", "            -- and (mode in ('') or mode is null)\n", "            -- removing things like stocking level runs, ordering runs, reverse flow runs\n", "    ),\n", "                \n", "truck_caps as \n", "    (\n", "        select  distinct backend_outlet_id, \n", "                frontend_outlet_id, \n", "                run_id, \n", "                truck_load_capacity \n", "        from ars.bulk_facility_transfer_days\n", "        where truck_load_capacity > 0\n", "    ),\n", "    \n", "runs AS\n", "    (   \n", "    SELECT run_id\n", "    FROM ars.job_run\n", "    where date(started_at+interval'5'hour+interval'30'minute) >= current_date-interval'30'day\n", "    AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "    AND simulation_params not like '%%mode%%'\n", "    AND simulation_params not like '%%hyperpure_run%%'\n", "    AND simulation_params not like '%%any_day_po%%'    \n", "    AND simulation_params not like '%%spr_migration_simulation%%' \n", "    AND cast(json_extract(simulation_params,'$.manual') AS varchar) is null\n", "    order by 1\n", "    ),\n", "    \n", "ars_indent as \n", "    (\n", "        select  run_id, \n", "                backend_outlet_id, \n", "                frontend_outlet_id,\n", "                sto_date,\n", "                sum(weight_in_gm*sto_quantity_post_truncation_in_case) as total_weight,\n", "                sum(sto_quantity_post_truncation_in_case) as total_indent,\n", "                avg(item_factor) as avg_item_factor\n", "        from ars.transfers_optimization_results_v2 as indent\n", "        inner join rpc.item_details id ON id.item_id = indent.item_id AND id.active = 1 AND id.approved = 1\n", "        left join supply_etls.item_factor fac on fac.item_id = indent.item_id\n", "        where indent.insert_ds_ist >= cast((CURRENT_DATE - interval '30' DAY) as varchar)\n", "          and run_id in (select run_id from runs) \n", "        group by 1,2,3,4\n", "    ),\n", "\n", "base as \n", "    (\n", "        select  awu.sto_date,\n", "                awu.run_id,\n", "                awu.backend_outlet_id, \n", "                awu.frontend_outlet_id,\n", "                jr.run_type,\n", "                jr.mode,\n", "                truck_load_capacity,\n", "                cast(total_weight as decimal)/1000 as total_weight_kg,\n", "                total_indent,\n", "                avg_item_factor\n", "        from ars_indent as awu\n", "        inner join valid_ars_job_runs jr on jr.run_id = awu.run_id\n", "        inner join truck_caps tc on awu.run_id = tc.run_id and awu.backend_outlet_id = tc.backend_outlet_id and awu.frontend_outlet_id = tc.frontend_outlet_id\n", "    ),\n", "    \n", "cost_sheet AS\n", "    (\n", "        with base as \n", "            (\n", "            select  *,\n", "                    case \n", "                        when fleet_type like '%%ixe%%' then (vehicle_count * cast((kms_slab/days_in_month) as decimal))\n", "                        else (vehicle_count * cast(kms_slab as decimal))\n", "                    end as planned_kms_slab\n", "            from \n", "                (\n", "                select  date_,\n", "                        case \n", "                            when extract(month from date(date_)) in  (1,3,5,7,8,10,12) then 31\n", "                            when extract(month from date(date_)) in  (4,6,9,11) then 30\n", "                            else 28\n", "                        end as days_in_month,\n", "                        case when source_facility_id = 4181 then 1876\n", "                             when source_facility_id = 4325 then 2006\n", "                             else source_facility_id\n", "                        end as source_facility_id_,\n", "                        source_name,\n", "                        destination_outlet_id,\n", "                        destination_name,\n", "                        fleet_type,\n", "                        vehicle_number as truck_num,\n", "                        date_cost,\n", "                        cast(date_cost AS decimal)*1.00 / cast(daily_trip_cost AS decimal) as vehicle_count,\n", "                        kms_slab_as_per_loi as kms_slab\n", "                from supply_etls.blinkit_middle_mile_fleet_cost\n", "                where date_ >= CAST((CURRENT_DATE - interval '30' DAY) AS DATE)\n", "                  and daily_trip_cost > 0\n", "                  --and category = 'Grocery'\n", "                ) cte\n", "            )\n", "            \n", "        select  date_,\n", "                source_facility_id_,\n", "                destination_outlet_id,\n", "                -- destination_name,\n", "                --truck_num,\n", "                -- sum(planned_kms_slab) as planned_kms,\n", "                sum(date_cost) as total_cost\n", "        from base\n", "        where (source_facility_id_ not in {cms_live_fc} and \n", "                (\n", "                (source_facility_id_ in (2469, 264, 555, 92, 2010) and date_ < date('2024-12-16')) or\n", "                (source_facility_id_ not in (2469, 264, 555, 92, 2010))\n", "                )\n", "             )\n", "        group by 1,2,3\n", "    ),\n", "    \n", "cms_cost as\n", "    (\n", "    select  trip_creation_date as date_,\n", "            wh_facility_id as source_facility_id_,\n", "            ds_outlet_id as destination_outlet_id,\n", "            sum(total_con_cost) as total_cost\n", "    from supply_etls.fleet_cms_cost\n", "    where (wh_facility_id in {cms_live_fc} or (wh_facility_id  in (2469,264,555,92,2010) and trip_creation_date >= date('2024-12-16')))\n", "      and trip_creation_date >= cast(current_date - interval '30' day as date)\n", "    group by 1,2,3\n", "    ),\n", "    \n", "cost_base as\n", "    (\n", "    select * from cost_sheet\n", "    union all\n", "    select * from cms_cost\n", "    )\n", "\n", "select  cte.date_,\n", "        sender_outlet_id,\n", "        sender_outlet_name,\n", "        sender_facility_id,\n", "        receiver_outlet_id,\n", "        receiver_outlet_name,\n", "        receiver_facility_id,\n", "        city,\n", "        cs.total_cost,\n", "        -- mode,\n", "        mode_new,\n", "        truck_load,\n", "        indent_weight,\n", "        indent,\n", "        case when cte.date_ >= (current_date - INTERVAL '7' DAY) then 'L7D' \n", "             when cte.date_ >= (current_date - INTERVAL '14' DAY) and cte.date_ < (current_date - INTERVAL '7' DAY) then 'L14D-L7D' \n", "             else 'L21D-L14D' \n", "        end as days_bucket,\n", "        total_trips\n", "from\n", "    (\n", "    select  date(sto_date) as date_,\n", "            backend_outlet_id as sender_outlet_id,\n", "            co1.name as sender_outlet_name,\n", "            co1.facility_id as sender_facility_id,\n", "            frontend_outlet_id as receiver_outlet_id,\n", "            co2.name as receiver_outlet_name,\n", "            co2.facility_id as receiver_facility_id,\n", "            case when trim(co2.location) = 'Bangalore' then 'Bengaluru' else trim(co2.location) end as city,\n", "            case when mode is null then 'Grocery'\n", "                 when mode in ('fnv','milk','perishable') then 'Perishable/FnV'\n", "                 when mode = 'frozen' then 'Cold/Frozen'\n", "            end as mode_new,\n", "            sum(truck_load_capacity) as truck_load,\n", "            sum(total_weight_kg) as indent_weight,\n", "            sum(total_indent) as indent,\n", "            avg(avg_item_factor) as avg_item_factor\n", "    from base b\n", "    join lake_retail.console_outlet co1 on b.backend_outlet_id = co1.id and business_type_id in (1,12)\n", "    join lake_retail.console_outlet co2 on b.frontend_outlet_id = co2.id\n", "    group by 1,2,3,4,5,6,7,8,9\n", "    having sum(truck_load_capacity) > 0\n", "    ) cte\n", "left join \n", "        (\n", "        select  trip_date,\n", "                wh_facility_id,\n", "                ds_outlet_id,\n", "                count(distinct trip_id) as total_trips\n", "        from supply_etls.fleet_trips\n", "        where trip_date >= cast((current_date - interval '30' day) as varchar)\n", "        group by 1,2,3\n", "        ) trip on trip.trip_date = cast(cte.date_ as varchar) and trip.wh_facility_id = cte.sender_facility_id and cte.receiver_outlet_id = trip.ds_outlet_id  \n", "left join cost_base cs on cs.date_ = cte.date_ and cs.source_facility_id_ = cte.sender_facility_id and cs.destination_outlet_id = cte.receiver_outlet_id \n", "where indent_weight <> 0\n", "  and cte.date_ >= cast((current_date-interval '30' day) as date) and cte.date_ < cast(current_date as date)\n", "order by date_, sender_facility_id, receiver_outlet_id\n", "\n", "\n", "\n", "--left join cost_base cs on cs.date_ = cte.date_ and cs.source_facility_id_ = cte.sender_facility_id and cs.destination_outlet_id = cte.receiver_outlet_id \n", "--where indent_weight <> 0\n", "--  and cte.date_ >= cast((current_date-interval '30' day) as date) and cte.date_ < cast(current_date as date)\n", "--order by date_, sender_facility_id, receiver_outlet_id\n", "\n", "\n", "\"\"\".format(\n", "    cms_live_fc=facilities\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b8f08b7d-bbc3-412e-a53f-f134e7a89e1e", "metadata": {}, "outputs": [], "source": ["cpu_data1 = pd.read_sql_query(sql=cpu_query1, con=trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "e94ab10f-fb89-4f41-a836-1bff1bf3b488", "metadata": {}, "outputs": [], "source": ["cpu_data1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "41c1ac29-826c-4f81-82cd-45fda6e9eea0", "metadata": {}, "outputs": [], "source": ["cpu_data1.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "d5dac7e8-e8ac-4b8e-8198-39363af36ecd", "metadata": {}, "outputs": [], "source": ["cpu_data1.columns"]}, {"cell_type": "code", "execution_count": null, "id": "8b970132-25e6-454d-a976-45ca6ae2ba30", "metadata": {}, "outputs": [], "source": ["cpu_data1.sender_outlet_name.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "a0e48e5c-b8ea-4a48-ae88-ec81c150381c", "metadata": {}, "outputs": [], "source": ["# cpu_data1[(cpu_data1['city']=='Agra') & (cpu_data1['total_cost'].isnull()==True) & (cpu_data1['sender_facility_id']==2469)].sort_values(by='date_',ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6c3bb2f8-b028-4185-bf0e-6afe36bb7711", "metadata": {}, "outputs": [], "source": ["# cpu_data1[(cpu_data1['city']=='Gurgaon') & (cpu_data1['total_cost'].isnull()==True) & (cpu_data1['sender_facility_id']==555) & (cpu_data1['receiver_outlet_id']==2824)].sort_values(by='date_',ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "41d8358a-18aa-4138-9b0d-18bf835327f2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e2daf01f-1b0c-4b0a-a357-4824a5c22473", "metadata": {}, "outputs": [], "source": ["# cpu_data1[cpu_data1['receiver_outlet_name']=='SS Delhi Mohan Garden ES250'].sort_values(by='date_',ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "640d7c2d-4598-442f-aa45-14506ab2a18b", "metadata": {}, "outputs": [], "source": ["# cpu_data1[(cpu_data1['city']=='Gurgaon') & (cpu_data1['total_cost'].isnull()==True)].sort_values(by='date_',ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "565ac463-92e4-4165-9b05-67e2e4768ce7", "metadata": {}, "outputs": [], "source": ["# cpu_data1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d3ea3573-2289-4754-865c-0894176d214b", "metadata": {}, "outputs": [], "source": ["# cpu_data1[(cpu_data1[\"city\"] == \"Gurgaon\")]"]}, {"cell_type": "code", "execution_count": null, "id": "caf7e4d1-d1aa-4df6-8543-cde0a98147c6", "metadata": {}, "outputs": [], "source": ["# cpu_data1.to_csv('cpu_data1.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "0f614827-0d09-4dbe-b49c-0b2c1f484ae9", "metadata": {}, "outputs": [], "source": ["cpu_data1[\"indent_weight\"] = cpu_data1[\"indent_weight\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "2dce6cc7-5d0a-447e-beaa-93817c95f615", "metadata": {}, "outputs": [], "source": ["cpu_data1[\"date_\"] = pd.to_datetime(cpu_data1[\"date_\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "f4d16788-19b0-43f9-bc00-ea93d2d92782", "metadata": {}, "outputs": [], "source": ["cpu_data1[\"total_trips\"] = cpu_data1[\"total_trips\"].fillna(value=0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1fe1c693-cc0d-4ed9-aeea-819412f79309", "metadata": {}, "outputs": [], "source": ["# cpu_data1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5f39ecb8-fbfb-464f-b9c5-820be3defaef", "metadata": {}, "outputs": [], "source": ["# duplicates = cpu_data1.duplicated(subset=['date_', 'sender_outlet_id', 'receiver_outlet_id'])\n", "\n", "# # If you want to include the first occurrence as well, set keep=False\n", "# duplicates_all = cpu_data1.duplicated(subset=['date_', 'sender_outlet_id', 'receiver_outlet_id'], keep=False)"]}, {"cell_type": "code", "execution_count": null, "id": "316cead9-91f6-4f64-b658-d432c241a902", "metadata": {}, "outputs": [], "source": ["# duplicates.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f5554919-c27c-44ca-a9f6-d829f02218b3", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date_\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Date\",\n", "        },\n", "        {\n", "            \"name\": \"sender_outlet_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Sender outlet ID\",\n", "        },\n", "        {\n", "            \"name\": \"sender_outlet_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Sender Outlet Name\",\n", "        },\n", "        {\n", "            \"name\": \"sender_facility_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Sender facility ID\",\n", "        },\n", "        {\n", "            \"name\": \"receiver_outlet_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Receiver Outlet ID\",\n", "        },\n", "        {\n", "            \"name\": \"receiver_outlet_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Receiver Outlet name\",\n", "        },\n", "        {\n", "            \"name\": \"receiver_facility_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"receiver facility ID\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\"name\": \"total_cost\", \"type\": \"real\", \"description\": \"Total Cost\"},\n", "        {\"name\": \"mode_new\", \"type\": \"varchar\", \"description\": \"Mode\"},\n", "        {\"name\": \"truck_load\", \"type\": \"real\", \"description\": \"Truck load\"},\n", "        {\"name\": \"indent_weight\", \"type\": \"real\", \"description\": \"Indent weight\"},\n", "        {\"name\": \"indent\", \"type\": \"real\", \"description\": \"Indent\"},\n", "        {\"name\": \"days_bucket\", \"type\": \"varchar\", \"description\": \"Days bucket\"},\n", "        {\"name\": \"total_trips\", \"type\": \"integer\", \"description\": \"Total Trip count\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"sender_outlet_id\",\n", "        \"receiver_outlet_id\",\n", "    ],  # list\n", "    \"partition_key\": [\"date_\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"date_\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"CPU\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(cpu_data1, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "01786052-9158-459c-a22e-1b758faaa00e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "26007fa7-9cf8-49a0-8ed9-45b7a2b05475", "metadata": {}, "outputs": [], "source": ["# cpu_data1.to_csv('cpu_data1.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "b241d4d3-025b-47e0-9485-c4cebdb328d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3e417812-2b18-4022-8c87-ca0dce55e1f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6be65c86-7621-4521-bfd9-daa7031e1dec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "659a9d38-fefb-444d-a922-c1d5321f0b48", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "aa85ef53-2fbc-4f73-a20d-baa499d74090", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}