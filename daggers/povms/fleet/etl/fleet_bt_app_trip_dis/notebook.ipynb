{"cells": [{"cell_type": "code", "execution_count": null, "id": "09235f41-0ac6-4b29-9094-7d15b25037d2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "import json\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "83b19126-50be-46d8-a289-ea6be43537d5", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b4ee6266-b3fc-4d1a-923d-89c0c5c8a266", "metadata": {}, "outputs": [], "source": ["redshift_schema_name = \"metrics\"\n", "redshift_table_name = \"fleet_bt_app_trip_dist\""]}, {"cell_type": "code", "execution_count": null, "id": "55cd7f64-63dd-46af-80bd-b9edad9a8d58", "metadata": {"tags": []}, "outputs": [], "source": ["query = f\"\"\" \n", "select distinct \n", "    transit_trip_id as trip_id,\n", "    cast(json_extract(extras,'$.total_trip_time') as real)/3600 as total_trip_time_hr,\n", "    cast(json_extract(extras,'$.total_trip_distance') as real)/1000 as total_trip_distance_km\n", "\n", "from\n", "    transit_server.transit_location as tl\n", "join \n", "    locations_geo_tracker.trips_trip as t\n", "    on tl.location_tracker_trip_id = t.id\n", "    \n", "where \n", "tl.install_ts >= (CURRENT_DATE - interval '20' DAY)\n", "and json_extract(extras,'$.total_trip_time') is not null\n", "\n", "\"\"\"\n", "\n", "df = pd.read_sql_query(sql=query, con=trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "7621e7aa-ab90-4031-b55a-d8f02a285a15", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "120a97bd-7106-4fa7-b51c-01603212b5a6", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": redshift_schema_name,\n", "    \"table_name\": redshift_table_name,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"trip_id\", \"type\": \"varchar\", \"description\": \"trip id\"},\n", "        {\n", "            \"name\": \"total_trip_time_hr\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total_trip_time_hr\",\n", "        },\n", "        {\n", "            \"name\": \"total_trip_distance_km\",\n", "            \"type\": \"float\",\n", "            \"description\": \"total_trip_distance_km\",\n", "        },\n", "    ],\n", "    \"primary_key\": \"trip_id\",  # list\n", "    \"sortkey\": \"\",  # list\n", "    # \"incremental_key\" : \"date_\",\n", "    # \"force_upsert_without_increment_check\": True,\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"Base table for total trip distance and time from BT app\"\"\",\n", "}\n", "\n", "print(\"pushing to redshift\")\n", "\n", "pb.to_redshift(df, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "275a97bb-6720-4c11-a193-4a21481f237b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}