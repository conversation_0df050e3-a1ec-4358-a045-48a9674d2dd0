{"cells": [{"cell_type": "code", "execution_count": null, "id": "8b5eed7e-4907-4eb2-9308-55e54b98904a", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": null, "id": "b8e2e847-bee4-4d7c-abda-7a2c4f5c04f9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import time\n", "import numpy as np\n", "from datetime import timedelta, datetime, date\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "b00b0b5d-f243-492d-b63d-ed29d1e9b8f0", "metadata": {}, "outputs": [], "source": ["# Connecting with Redshift\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "0df9efbe-9f19-4042-ae6f-6128ecc2274d", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(0, max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9123b81f-6d89-41b0-9870-04d003c2d190", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=4.8,\n", "    row_height=0.625,\n", "    font_size=14,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "54344929-1153-4bbf-8b3a-1b3aa7dd45fa", "metadata": {}, "outputs": [], "source": ["query_1 = f\"\"\"\n", "create temporary table new_app_data as (\n", "    \n", "    WITH base AS\n", "  (SELECT *,\n", "          coalesce(vehicle_type2,vehicle_type1) AS truck_type\n", "   FROM\n", "     (SELECT t.id AS consignment_id,\n", "             t.state AS current_state,\n", "             trip.trip_id,\n", "             t.source_store_id::int AS facility_id,\n", "             n.external_name AS facility_name,\n", "             m.outlet_id AS ds_outlet_id,\n", "             co.name AS ds_outlet_name,\n", "             UPPER(split_part(json_extract_path_text(t.metadata,'truck_number'),'/',1)) AS truck_number,\n", "             split_part(json_extract_path_text(t.metadata,'truck_number'),'/',2) AS vehicle_type1,\n", "             json_extract_path_text(t.metadata,'vehicle_type') AS vehicle_type2,\n", "             count(DISTINCT d.external_id) AS num_invoices,\n", "             count(DISTINCT c.external_id) AS num_containers,\n", "             trip.truck_entry_wh,\n", "             trip.truck_handshake,\n", "             max(CASE\n", "                     WHEN (to_state='LOADING') THEN tl.install_ts\n", "                 END) AS loading_start,\n", "             max(CASE\n", "                     WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts\n", "                 END) AS ready_for_dispatch,\n", "             max(CASE\n", "                     WHEN (to_state='ENROUTE') THEN tl.install_ts\n", "                 END) AS enroute,\n", "             max(CASE\n", "                     WHEN (to_state='REACHED') THEN tl.install_ts\n", "                 END) AS ds_reached,\n", "             max(CASE\n", "                     WHEN (to_state='UNLOADING') THEN tl.install_ts\n", "                 END) AS unloading_start,\n", "             max(CASE\n", "                     WHEN (to_state='COMPLETED') THEN tl.install_ts\n", "                 END) AS unloading_completed,\n", "             trip.truck_return_wh\n", "      FROM lake_transit_server.transit_consignment t\n", "      JOIN lake_transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "      JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "      JOIN lake_transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "      JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "      JOIN lake_retail.console_outlet_logistic_mapping m ON m.logistic_node_id = t.destination_store_id\n", "      JOIN lake_retail.console_outlet co ON co.id = m.outlet_id\n", "      JOIN\n", "        (SELECT DISTINCT t.id AS consignment_id,\n", "                         tsl.label_value AS trip_id\n", "         FROM lake_transit_server.transit_consignment t\n", "         JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "         JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "         AND tsl.label_type = 'TRIP_ID'\n", "         WHERE t.install_ts >= cast((current_date - 7) AS TIMESTAMP)) tc \n", "         ON tc.consignment_id = t.id\n", "      JOIN\n", "        (SELECT tsl.label_value AS trip_id,\n", "                min(q.entry_ts) AS truck_entry_wh,\n", "                min(tts.install_ts) AS truck_return_wh,\n", "                min(t.install_ts) AS truck_handshake\n", "         FROM lake_transit_server.transit_consignment t\n", "         JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "         JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "         AND tsl.label_type = 'TRIP_ID'\n", "         JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value\n", "         AND tt.shipment_label_type = 'TRIP_ID'\n", "         LEFT JOIN lake_transit_server.task_management_taskstatelog tts ON tts.task_id = tt.id\n", "         AND tts.to_state = 'COMPLETED'\n", "         JOIN lake_transit_server.transit_express_allocation_field_executive_queue q ON (q.user_profile_id || '_' || q.id) = tsl.label_value\n", "         WHERE t.install_ts >= cast((current_date - 7) AS TIMESTAMP)\n", "         GROUP BY 1) trip ON trip.trip_id = tc.trip_id\n", "      WHERE t.install_ts >= cast((current_date - 7) AS TIMESTAMP) \n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               13,\n", "               14,\n", "               21,\n", "               t.metadata)),\n", "     pos AS\n", "  (SELECT DISTINCT td.consignment_id AS csmt_id,\n", "                   max(s.dispatch_time) AS dispatch_time\n", "   FROM lake_pos.pos_invoice pi\n", "   JOIN lake_transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "   JOIN lake_po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "   JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id\n", "   AND co.business_type_id IN (1,\n", "                               12,20)\n", "   WHERE pi.created_at >= cast((current_date - 7) AS TIMESTAMP)\n", "     AND invoice_type_id IN (5,\n", "                             14,\n", "                             16)\n", "   GROUP BY 1)\n", "       \n", "    SELECT\n", "        (truck_handshake + interval '5.5 hrs')::date AS trip_date,\n", "        trip_id,\n", "        consignment_id,\n", "        truck_number as vehicle_number,\n", "        facility_name as source_outlet_name,\n", "        case \n", "            when facility_id = 1743 then 264 \n", "            when facility_id = 4306 then 1983\n", "            else facility_id\n", "        end as source_facility_id,\n", "        ds_outlet_id as destination_outlet_id,\n", "        ds_outlet_name as destination_outlet_name,\n", "        (truck_entry_wh + interval '5.5 hrs') AS truck_entry_wh_,\n", "        (loading_start + interval '5.5 hrs') AS loading_start_,\n", "        (ready_for_dispatch + interval '5.5 hrs') AS ready_for_dispatch_,\n", "         (enroute + interval '5.5 hrs') AS enroute_,\n", "        -- truck_return_wh as return_to_backend,\n", "        (dispatch_time + interval '5.5 hrs') AS scheduled_dispatch_time,\n", "        \n", "        case \n", "            when source_facility_id in (1983,4306, 264, 43, 517, 1206, 513) then (dispatch_time + interval '4.25 hr') \n", "            when source_facility_id = 555 then (dispatch_time + interval '4 hr') \n", "            else (dispatch_time + interval '4.5 hr') \n", "        end AS scheduled_truck_arrival,\n", "        \n", "        case \n", "            when ready_for_dispatch_ > scheduled_dispatch_time then 1\n", "            else 0\n", "        end as scheduled_dispatch_ts_breached_ready_dispatch,\n", "        \n", "        case \n", "            when truck_entry_wh_ > scheduled_truck_arrival then 1\n", "            else 0\n", "        end as scheduled_checkin_time_breached,\n", "        \n", "        datediff(MINUTE,loading_start_,ready_for_dispatch_) AS loading_time_min,\n", "        datediff(MINUTE,scheduled_dispatch_time,ready_for_dispatch_) AS dispatch_delay_ready_dispatch_min\n", "\n", "        \n", "           \n", "    FROM base\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    where (truck_handshake + interval '5.5 hrs')::date between (current_date - 7) and (current_date - 1) \n", "  \n", ");\n", "\n", "\n", "select \n", "source_outlet_name,\n", "count(distinct trip_id) as total_trips,\n", "count(distinct \n", "        case when (ready_for_dispatch_ > scheduled_dispatch_time) \n", "        and (truck_entry_wh_ < scheduled_truck_arrival) then trip_id \n", "        end\n", "    ) as \"dispatch delay trips due to high loading time\",\n", " \n", "\"dispatch delay trips due to high loading time\"*100.0/total_trips as \"%% dispatch delay trips\",\n", "\n", "sum(case when (ready_for_dispatch_ > scheduled_dispatch_time ) and (truck_entry_wh_ < scheduled_truck_arrival) and loading_time_min is not null then loading_time_min end)*1.0/\n", "count(distinct case when (ready_for_dispatch_ > scheduled_dispatch_time) and (truck_entry_wh_ < scheduled_truck_arrival) then trip_id end) as \"avg load time of delayed trips\",\n", "\n", "sum(case when (ready_for_dispatch_ < scheduled_dispatch_time) or (scheduled_dispatch_time is null)  then loading_time_min  end)*1.0/\n", "count(distinct case when (ready_for_dispatch_ < scheduled_dispatch_time) or (scheduled_dispatch_time is null) then trip_id end) as \"avg load time of non delay trips\",\n", "\n", "sum(case when ((ready_for_dispatch_ > scheduled_dispatch_time) or (scheduled_dispatch_time is null)) and (truck_entry_wh_ < scheduled_truck_arrival) then dispatch_delay_ready_dispatch_min end)*1.0/\n", "count(distinct case when (ready_for_dispatch_ > scheduled_dispatch_time) and (truck_entry_wh_ < scheduled_truck_arrival) then trip_id end) as \"avg delay time of dispatch\"\n", "\n", "\n", "from new_app_data\n", "where trip_date = current_date - 1\n", "\n", "group by 1\n", "\n", "\"\"\"\n", "\n", "df = read_sql_query(query_1, redshift_connection)\n", "df.columns = [\n", "    \"Backend Name\",\n", "    \"Total Trips\",\n", "    \"dispatch delay trips due to high loading time\",\n", "    \"% dispatch delay trips\",\n", "    \"avg load time of delayed trips\",\n", "    \"avg load time of non delay trips\",\n", "    \"avg delay time of dispatch\",\n", "]\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4153df27-e709-499a-bbd1-2df85fcaa26b", "metadata": {}, "outputs": [], "source": ["df = df.round(\n", "    {\n", "        \"% dispatch delay trips\": 1,\n", "        \"avg load time of delayed trips\": 0,\n", "        \"avg load time of non delay trips\": 0,\n", "        \"avg delay time of dispatch\": 0,\n", "    }\n", ")\n", "df.replace(np.nan, \"-\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4ec0cf9f-be37-43bc-a575-fc337af38259", "metadata": {}, "outputs": [], "source": ["fig, ax = render_mpl_table(df)\n", "fig.savefig(\"summary.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "adda4014-60e0-402e-a7d3-73cdcaed8f78", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"bl-fc-all\",\n", "    text=\"Dispatch delay due to high loading time (T-1)\",\n", "    files=[\"./summary.png\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3c7c4523-87ba-4472-ad65-f9d3b5b6af4a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}