{"cells": [{"cell_type": "code", "execution_count": null, "id": "bb803ce8-08cf-432a-a4c4-2a63d0abdd8b", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "2618df77-c3c9-4a75-85a4-c113816cdbe5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "dec4b3ef-99cf-40a3-9f33-14c6d782feed", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e256a0c9-ed4d-4d5a-bfa2-b7fe9e9ab7a1", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c184dfb9-b500-4a62-9a0a-96faaa212d7b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e3656a0f-6b7f-4a20-a547-6db3b26ded88", "metadata": {"tags": []}, "outputs": [], "source": ["transit_query = f\"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "   \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "    GROUP BY 1,2,3,4,5,6,7,t.metadata\n", "    ),\n", "        \n", "docs AS\n", "    (\n", "    SELECT  t.id AS consignment_id,\n", "            count(DISTINCT d.external_id) AS num_invoices,\n", "            count(DISTINCT c.external_id) AS num_containers\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "    JOIN transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "      AND c.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "    GROUP BY 1\n", "    ),\n", "   \n", "pos AS\n", "    (\n", "    select  csmt_id,\n", "            dispatch_time\n", "    from\n", "        (\n", "        SELECT  td.consignment_id AS csmt_id,\n", "                s.dispatch_time AS dispatch_time,\n", "                count(distinct s.id) as total_stos,\n", "                row_number() over (partition by consignment_id order by count(distinct s.id) desc) as num\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        group by 1,2\n", "        )\n", "    where num = 1\n", "    ),\n", "   \n", "grn AS\n", "    (\n", "    SELECT cast(er.external_order_no as int) AS c_id,\n", "      min(a.created_date) AS grn_started_at,\n", "      max(a.created_date) AS grn_completed_at\n", "    FROM storeops.er er\n", "    JOIN storeops.activity a ON a.er_id = er.id\n", "    WHERE er.created_date > cast((CURRENT_DATE - interval '14' DAY) AS TIMESTAMP)\n", "    AND er.er_type = 'BULKIN_CONSIGNMENT'\n", "    AND er.external_order_no IN (SELECT DISTINCT cast(consignment_id AS varchar) FROM base)\n", "    GROUP BY 1\n", "    ),\n", "    \n", "grn1 AS\n", "    (\n", "    SELECT DISTINCT t.id AS c_id,\n", "               min(il.pos_timestamp) AS grn_started_at,\n", "               max(il.pos_timestamp) AS grn_completed_at\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "    JOIN ims.ims_inventory_log il ON il.merchant_invoice_id = d.external_id\n", "    and il.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "    AND inventory_update_type_id IN (1,28,76,90,93)\n", "    GROUP BY 1\n", "    ),\n", "   \n", "excpected_timelines AS \n", "    (\n", "    WITH base_ AS \n", "        (\n", "        select  distinct trip_id,\n", "                consignment_id,\n", "                event_type,\n", "                (last_value(scheduled_ts) over(PARTITION BY trip_id,consignment_id,event_type ORDER BY update_ts ROWS BETWEEN unbounded preceding AND unbounded following)) + interval '330' MINUTE AS scheduled_ts, \n", "                (last_value(expected_ts) over(PARTITION BY trip_id,consignment_id,event_type ORDER BY update_ts ROWS BETWEEN unbounded preceding AND unbounded following)) + interval '330' MINUTE AS expected_ts\n", "                                                                        \n", "        from (\n", "                SELECT DISTINCT trip_id,\n", "                      cast(json_extract(event_meta, '$.consignment_id') as varchar) AS consignment_id,\n", "                      event_type,\n", "                      scheduled_ts,\n", "                      update_ts,\n", "                      expected_ts\n", "                FROM transit_server.transit_projection_trip_event_timeline AS tp\n", "                WHERE event_type IN ('DS_ARRIVAL',\n", "                                   'UNLOADING_END',\n", "                                   'COMPLETED')\n", "                AND insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) AS varchar) \n", "                )\n", "        ),\n", "    wh_return AS\n", "        \n", "     (SELECT trip_id,\n", "             max(CASE\n", "                     WHEN event_type = 'COMPLETED' THEN expected_ts\n", "                     ELSE NULL\n", "                 END) AS exp_truck_return_wh,\n", "             max(CASE\n", "                     WHEN event_type = 'COMPLETED' THEN scheduled_ts\n", "                     ELSE NULL\n", "                 END) AS sch_truck_return_wh\n", "      FROM base_\n", "      GROUP BY 1),\n", "    ds_details AS\n", "     (SELECT trip_id,\n", "             consignment_id,\n", "             max(CASE\n", "                     WHEN event_type = 'DS_ARRIVAL' THEN expected_ts\n", "                     ELSE NULL\n", "                 END) AS exp_ds_reach,\n", "             max(CASE\n", "                     WHEN event_type = 'DS_ARRIVAL' THEN scheduled_ts\n", "                     ELSE NULL\n", "                 END) AS sch_ds_reach,\n", "             max(CASE\n", "                     WHEN event_type = 'UNLOADING_END' THEN expected_ts\n", "                     ELSE NULL\n", "                 END) AS exp_unloading_complete,\n", "             max(CASE\n", "                     WHEN event_type = 'UNLOADING_END' THEN scheduled_ts\n", "                     ELSE NULL\n", "                 END) AS sch_unloading_complete\n", "      FROM base_\n", "      WHERE consignment_id!=''\n", "      GROUP BY 1,\n", "               2) \n", "    SELECT    DISTINCT wr.trip_id,\n", "              dd.consignment_id,\n", "              exp_ds_reach,\n", "              sch_ds_reach,\n", "              exp_unloading_complete,\n", "              sch_unloading_complete,\n", "              exp_truck_return_wh,\n", "              sch_truck_return_wh\n", "    FROM wh_return AS wr\n", "    LEFT JOIN ds_details AS dd ON wr.trip_id = dd.trip_id\n", "    ),\n", "       \n", "       \n", "ctype AS\n", "  (\n", "  SELECT consignment_id,\n", "         item_type AS con_type\n", "   FROM\n", "     (SELECT *,\n", "             RANK () OVER (PARTITION BY consignment_id ORDER BY type_count DESC) AS rnk\n", "      FROM\n", "        (SELECT DISTINCT c.id AS consignment_id,\n", "                         (CASE\n", "                              WHEN ids.perishable = 1 THEN 'Perishable'\n", "                              ELSE 'Grocery'\n", "                          END) AS item_type,\n", "                         count(CASE\n", "                                  WHEN ids.perishable = 1 THEN 'Perishable'\n", "                                  ELSE 'Grocery'\n", "                              END) AS type_count,\n", "                sum(pipd.quantity) as dispatched_qty,\n", "                sum(pipd.quantity*pp.weight_in_gm) as dispatched_qty_weight,\n", "                sum(pipd.quantity*ids.length_in_cm*ids.breadth_in_cm*ids.height_in_cm) as indent_volume\n", "         FROM transit_server.transit_consignment c\n", "         JOIN transit_server.transit_consignment_document d ON c.id = d.consignment_id\n", "         JOIN pos.pos_invoice pi ON pi.invoice_id = d.external_id\n", "         JOIN pos.pos_invoice_product_details pipd ON pipd.invoice_id = pi.id\n", "         JOIN rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "         JOIN rpc.item_details ids ON ids.item_id = pp.item_id\n", "         WHERE c.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) AS varchar)\n", "           AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) AS varchar)\n", "           AND pi.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) AS varchar)\n", "           and pipd.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) AS varchar)\n", "           AND d.document_type = 'INVOICE'\n", "         GROUP BY 1,2)\n", "                 )\n", "   WHERE rnk = 1 \n", "   ),\n", "\n", "final_base as\n", "    (\n", "    SELECT base.consignment_id,\n", "           ctype.con_type,\n", "           tm.trip_id,\n", "           cast((tm.truck_handshake + interval '330' MINUTE) as date) AS consignment_date,\n", "           CASE\n", "               WHEN facility_id = 1743 THEN 264\n", "               WHEN facility_id = 4306 THEN 1983\n", "               ELSE facility_id\n", "           END AS facility_id,\n", "           facility_name,\n", "           ds_outlet_id,\n", "           ds_outlet_name,\n", "           truck_number,\n", "           truck_type,\n", "           tm.driver_id,\n", "           tm.driver_name,\n", "           tm.driver_mobile,\n", "           tm.trip_state,\n", "           current_state AS consignment_state,\n", "           num_invoices,\n", "           num_containers,\n", "           (dispatch_time + interval '270' MINUTE) AS scheduled_truck_arrival,\n", "           (tm.truck_entry_wh + interval '330' MINUTE) AS truck_entry_wh,\n", "           (tm.truck_handshake + interval '330' MINUTE) AS truck_handshake,\n", "           (loading_start + interval '330' MINUTE) AS loading_start,\n", "           (ready_for_dispatch + interval '330' MINUTE) AS ready_for_dispatch,\n", "           (enroute + interval '330' MINUTE) AS enroute,\n", "           (dispatch_time + interval '330' MINUTE) AS scheduled_dispatch_time,\n", "           (ds_reached + interval '330' MINUTE) AS ds_reached,\n", "           exp_ds_reach,\n", "           sch_ds_reach,\n", "           (unloading_start + interval '330' MINUTE) AS unloading_start,\n", "           (unloading_completed + interval '330' MINUTE) AS unloading_completed,\n", "           exp_unloading_complete,\n", "           sch_unloading_complete,\n", "           CASE \n", "                WHEN current_state = 'COMPLETED' \n", "                    THEN (coalesce(grn.grn_started_at,grn1.grn_started_at) + interval '330' MINUTE)\n", "                    ELSE NULL\n", "            END AS grn_started_at,\n", "            \n", "            CASE \n", "                WHEN current_state = 'COMPLETED' \n", "                    THEN (coalesce(grn.grn_completed_at,grn1.grn_completed_at) + interval '330' MINUTE) \n", "                    ELSE NULL\n", "            END AS grn_completed_at,\n", "\n", "           (tm.truck_return_wh + interval '330' MINUTE) AS truck_return_wh,\n", "           exp_truck_return_wh,\n", "           sch_truck_return_wh\n", "           \n", "    FROM base\n", "    left join docs on docs.consignment_id = base.consignment_id\n", "    LEFT JOIN ctype ON ctype.consignment_id = base.consignment_id\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    LEFT JOIN grn ON grn.c_id = base.consignment_id\n", "    LEFT JOIN grn1 ON grn1.c_id = base.consignment_id\n", "    LEFT JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "    LEFT JOIN excpected_timelines AS et ON tm.trip_id = et.trip_id AND cast(base.consignment_id as varchar) = et.consignment_id\n", "    ),\n", "    \n", "time_gaps_base as\n", "    (\n", "    select  *,\n", "    -- WH Time Metrics:\n", "    date_diff('minute', truck_entry_wh, truck_return_wh) as truck_time_at_wh,\n", "    date_diff('minute', scheduled_truck_arrival, truck_entry_wh) as truck_arrival_delay_min,\n", "    date_diff('minute', truck_handshake, loading_start) as handshake_to_loading_min,\n", "    date_diff('minute', loading_start, ready_for_dispatch) as loading_time_at_wh,\n", "    date_diff('minute', scheduled_dispatch_time, enroute) as sch_dispatch_to_enroute,\n", "    date_diff('minute', ready_for_dispatch, enroute) as driver_delay_min,\n", "    date_diff('minute', scheduled_dispatch_time, ready_for_dispatch) as dispatch_delay_min,\n", "    \n", "            -- To be reviewed as sch_truck_return_wh might not be correct\n", "    date_diff('minute', sch_truck_return_wh, truck_return_wh) as sch_to_actual_truck_return_wh,\n", "    \n", "    -- Store Time Metrics:\n", "    --datediff(MINUTE,exp_ds_reach,ds_reached) AS ds_reach_delay_from_expected,  -- manual calc from sheet\n", "    \n", "    date_diff('minute', ds_reached, unloading_start) as ds_reach_to_unload_start,\n", "    date_diff('minute', unloading_start, unloading_completed) as unloading_time_min,\n", "    date_diff('minute', (unloading_start + interval '60' minute), unloading_completed) as sch_to_actual_unload_complete,\n", "    date_diff('minute', unloading_completed, truck_return_wh) as return_transit_truck_time\n", "    from final_base\n", "    )\n", "    \n", "select  base.*,\n", "        case when dispatch_delay_min between -60 and 0 then '(-60 - 0)'\n", "             when dispatch_delay_min between -120 and -60 then '(-120 - -60)'\n", "             when dispatch_delay_min between 0 and 30 then '(0 - 30)'\n", "             when dispatch_delay_min between 30 and 60 then '(30 - 60)'\n", "             when dispatch_delay_min between 60 and 120 then '(60 - 120)'\n", "             when dispatch_delay_min > 120 then '(>120)'\n", "             when dispatch_delay_min < -120 then '(<120)'\n", "             else 'NA'\n", "        end as dispatch_delay_bucket,\n", "        \n", "        case when dispatch_delay_min between -15 and 15 then 'On-time'\n", "             when dispatch_delay_min < -15 then 'Early'\n", "             when dispatch_delay_min > 15 then 'Delay'\n", "             else 'NA'\n", "        end as dispatch_delay_status,\n", "        \n", "        case when truck_arrival_delay_min between -10 and 10 then 'On-time'\n", "             when truck_arrival_delay_min < -10 then 'Early'\n", "             when truck_arrival_delay_min > 10 then 'Delay'\n", "             else 'NA'\n", "        end as truck_arrival_delay_status,\n", "        \n", "        case when handshake_to_loading_min between 0 and 5 then 'On-time'\n", "             when handshake_to_loading_min < 0 then 'Early'\n", "             when handshake_to_loading_min > 5 then 'Delay'\n", "             else 'NA'\n", "        end as handshake_to_loading_delay_status,\n", "        \n", "        case when loading_time_at_wh between 60 and 75 then 'On-time'\n", "             when loading_time_at_wh < 60 then 'Early'\n", "             when loading_time_at_wh > 75 then 'Delay'\n", "             else 'NA'\n", "        end as loading_time_delay_status,\n", "        \n", "        case when driver_delay_min between 0 and 30 then 'On-time'\n", "             when driver_delay_min < 0 then 'Early'\n", "             when driver_delay_min > 30 then 'Delay'\n", "             else 'NA'\n", "        end as driver_delay_status,\n", "        \n", "        case when ds_reach_to_unload_start between 0 and 15 then 'On-time'\n", "             when ds_reach_to_unload_start < 0 then 'Early'\n", "             when ds_reach_to_unload_start > 15 then 'Delay'\n", "             else 'NA'\n", "        end as ds_reach_to_unload_start_delay_status,\n", "        \n", "        case when sch_to_actual_unload_complete between 0 and 15 then 'On-time'\n", "             when sch_to_actual_unload_complete < 0 then 'Early'\n", "             when sch_to_actual_unload_complete > 15 then 'Delay'\n", "             else 'NA'\n", "        end as sch_to_actual_unload_complete_delay_status,\n", "        \n", "        \n", "        -- To be reviewed as sch_truck_return_wh might not be correct\n", "        case when sch_to_actual_truck_return_wh between -30 and 30 then 'On-time'\n", "             when sch_to_actual_truck_return_wh < -30 then 'Early'\n", "             when sch_to_actual_truck_return_wh > 30 then 'Delay'\n", "             else 'NA'\n", "        end as sch_to_actual_truck_return_wh_delay_status,\n", "        case when trim(co.location)='Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "        case when consignment_date > (current_date - INTERVAL '7' DAY) then 'L7D' \n", "             when consignment_date > (current_date - INTERVAL '14' DAY) and consignment_date <= (current_date - INTERVAL '7' DAY) then 'L14D-L7D' \n", "             else 'L21D-L14D' \n", "        end as days_bucket,\n", "        \n", "        CAST(scheduled_dispatch_time AS time) as sch_dispatch_time,\n", "        CAST(ds_reached AS time) as ds_reach_time,\n", "\n", "        case when scheduled_dispatch_time is null then 'NA'\n", "             when CAST(scheduled_dispatch_time AS time) < cast('12:00:00' as time) then 'Day'\n", "             when CAST(scheduled_dispatch_time AS time) >= cast('12:00:00' as time) then 'Night'\n", "             end as dispatch_slot\n", "        \n", "from time_gaps_base base\n", "left join lake_retail.console_outlet co on co.id = base.ds_outlet_id\n", "where UPPER(facility_name) not like '%%PC%%'\n", "  and trip_state <> 'CANCELLED'\n", "  and ds_reached is not null\n", "  and con_type = 'Grocery'\n", "  and truck_type not like '%%Reefer%%'\n", "  and base.facility_id = 1873\n", "         \n", "\"\"\"\n", "\n", "transit_df = read_sql_query(transit_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "5417fb83-bf30-463f-9613-73adb217525f", "metadata": {}, "outputs": [], "source": ["transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "91a9cc59-daba-4bbd-94e8-e995abdea750", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d03fdf15-ac6e-4d6c-9e9f-29fa1ea1330a", "metadata": {}, "outputs": [], "source": ["transit_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "16114e2e-ce9f-4cf6-aba2-5aa16cb0c4cf", "metadata": {}, "outputs": [], "source": ["transit_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "95204295-0a31-4b04-839a-a7dc69fe857b", "metadata": {}, "outputs": [], "source": ["transit_df.consignment_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "9d5ab5d4-8fa2-42ea-888e-7ed3e57c986f", "metadata": {}, "outputs": [], "source": ["transit_df[\"month\"] = pd.to_datetime(transit_df[\"consignment_date\"]).dt.strftime(\"%B\")"]}, {"cell_type": "code", "execution_count": null, "id": "f15b3959-15cb-4947-8dae-a7c93fb4b829", "metadata": {}, "outputs": [], "source": ["transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7630f550-6e81-4394-86fa-10046bbc7b97", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2936b04c-ad71-443d-8f63-f2caf8f36a1f", "metadata": {}, "outputs": [], "source": ["transit_df[\"day\"] = pd.to_datetime(transit_df[\"consignment_date\"]).dt.day"]}, {"cell_type": "code", "execution_count": null, "id": "84dac8ff-bbdc-434e-8b0f-1c93e9237f50", "metadata": {}, "outputs": [], "source": ["transit_df[\"date\"] = pd.to_datetime(transit_df[\"consignment_date\"]).dt.date\n", "transit_df[\"next_month\"] = transit_df[\"date\"] + relativedelta(months=1)\n", "transit_df[\"next_month\"] = pd.to_datetime(transit_df[\"next_month\"]).dt.strftime(\"%B\")"]}, {"cell_type": "code", "execution_count": null, "id": "56262a89-8726-4736-acdf-c3f0fd3a167d", "metadata": {}, "outputs": [], "source": ["transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b108d23a-0440-4a37-ba03-143e30ddddd5", "metadata": {"tags": []}, "outputs": [], "source": ["# transit_df[\"otd_month\"] = np.where(\n", "#     ((transit_df[\"day\"] >= 26) & (transit_df[\"day\"] <= 31)),\n", "#     transit_df[\"next_month\"],\n", "#     transit_df[\"month\"],\n", "# )\n", "\n", "transit_df[\"otd_month\"] = transit_df[\"month\"]"]}, {"cell_type": "code", "execution_count": null, "id": "eb0e7957-d3d2-4cfa-a4b8-ddbd476a2d4f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e2d013d5-681c-47bc-89da-1108bf02d3a9", "metadata": {}, "outputs": [], "source": ["transit_df.drop(columns=[\"month\", \"day\", \"date\", \"next_month\"], inplace=True)\n", "\n", "transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e418ccba-7b72-4684-8a4a-f15ff9023f7a", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a97eb578-ac44-42cc-8677-b5bec9063e07", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1HFADY6wROhf2rZUKa8aSz6KZc_v0wxtDrJgAczpPixI\"\n", "# sheet_name1 = \"February\"\n", "sheet_name2 = \"March\"\n", "# feb_planning = pb.from_sheets(sheet_id, sheet_name1)\n", "mar_planning = pb.from_sheets(sheet_id, sheet_name2)\n", "\n", "# feb_planning = pd.read_csv('planning_data_feb.csv')\n", "# mar_planning = pd.read_csv('planning_data_mar.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "0e9c6071-398c-4156-831a-1f2b8c302073", "metadata": {}, "outputs": [], "source": ["# feb_planning[\"otd_month\"] = \"February\"\n", "# mar_planning[\"otd_month\"] = \"March\""]}, {"cell_type": "code", "execution_count": null, "id": "086e6007-f798-40ed-9ae6-9e3334917a71", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "243cb2d2-2b8f-44d7-bb6b-a8d8af30ed3d", "metadata": {}, "outputs": [], "source": ["# planning_data = pd.concat([feb_planning, mar_planning])\n", "planning_data = mar_planning.copy()\n", "planning_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "88885996-1126-45ee-8545-17e499bef917", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e7eecb1f-8a3c-42d7-9ac9-2602d538be83", "metadata": {}, "outputs": [], "source": ["planning_data.rename(\n", "    columns={\n", "        \"sender_facility_id\": \"facility_id\",\n", "        \"receiving_outlet_id\": \"ds_outlet_id\",\n", "        \"DS Arrival time at Day\": \"Day\",\n", "        \"DS Arrival time at Night\": \"Night\",\n", "    },\n", "    inplace=True,\n", ")\n", "# planning_data = planning_data[\n", "#     [\"facility_id\", \"ds_outlet_id\", \"Day\", \"Night\", \"otd_month\"]\n", "# ]\n", "\n", "planning_data = planning_data[[\"facility_id\", \"ds_outlet_id\", \"Day\", \"Night\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "bcd010d4-de71-4338-ade6-3b7a78827cf0", "metadata": {}, "outputs": [], "source": ["planning_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2ddfc1eb-370c-4e35-85bf-815e8c8535e0", "metadata": {}, "outputs": [], "source": ["planning_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f4e65e0b-ebeb-4c05-b2cc-e287cfa707dc", "metadata": {}, "outputs": [], "source": ["# end_date = datetime.now()\n", "# date_strings = [(end_date - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(14, -1, -1)]\n", "\n", "# date_series = pd.to_datetime(date_strings, format='%Y-%m-%d')\n", "\n", "# df = pd.DataFrame({'dt': date_series})\n", "# df"]}, {"cell_type": "code", "execution_count": null, "id": "d04cc79b-96cf-47b6-87c8-52f5587d4a34", "metadata": {}, "outputs": [], "source": ["# final = pd.merge(df,planning_data,how='cross')"]}, {"cell_type": "code", "execution_count": null, "id": "4a75c639-5f49-4d44-893e-a5b1f5d40d7b", "metadata": {}, "outputs": [], "source": ["planning_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "07a9a83f-3016-4c01-b92e-9f9330524110", "metadata": {}, "outputs": [], "source": ["# planning_df = planning_data.melt(\n", "#     id_vars=[\"facility_id\", \"ds_outlet_id\", \"otd_month\"],\n", "#     value_vars=[\"Day\", \"Night\"],\n", "#     var_name=\"dispatch_slot\",\n", "#     value_name=\"planned_ds_arrival\",\n", "# )\n", "\n", "planning_df = planning_data.melt(\n", "    id_vars=[\"facility_id\", \"ds_outlet_id\"],\n", "    value_vars=[\"Day\", \"Night\"],\n", "    var_name=\"dispatch_slot\",\n", "    value_name=\"planned_ds_arrival\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "41b0965c-80a1-4032-aaa2-39479048548c", "metadata": {}, "outputs": [], "source": ["planning_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ea54f4c1-85e4-4550-a2da-abf7394fc35e", "metadata": {}, "outputs": [], "source": ["planning_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "598a349b-7c96-43b7-ab6c-fbb57fe01136", "metadata": {}, "outputs": [], "source": ["planning_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "2914a295-cf6f-4bc2-b162-406b42090192", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "91255fa8-1412-4ce3-b464-8041bb198400", "metadata": {}, "outputs": [], "source": ["planning_df[\"facility_id\"] = planning_df[\"facility_id\"].astype(int)\n", "planning_df[\"ds_outlet_id\"] = planning_df[\"ds_outlet_id\"].astype(int)\n", "planning_df[\"dispatch_slot\"] = planning_df[\"dispatch_slot\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "3de68ef9-7c94-4b3b-a92e-caacbefef68c", "metadata": {}, "outputs": [], "source": ["# final_df = transit_df.merge(\n", "#     planning_df,\n", "#     how=\"left\",\n", "#     on=[\"facility_id\", \"ds_outlet_id\", \"dispatch_slot\", \"otd_month\"],\n", "# )\n", "\n", "final_df = transit_df.merge(\n", "    planning_df,\n", "    how=\"left\",\n", "    on=[\"facility_id\", \"ds_outlet_id\", \"dispatch_slot\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4e7ad45c-21f7-428e-a584-5b1be4d99980", "metadata": {}, "outputs": [], "source": ["final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c914c3fa-c945-452c-8f9b-2c3cdb5a012c", "metadata": {}, "outputs": [], "source": ["# final_df.to_csv(\"df.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f46e8f8d-5964-4ef5-9a09-aff8d73607b8", "metadata": {}, "outputs": [], "source": ["final_df[\"ds_reach_delay\"] = (\n", "    pd.to_datetime(final_df[\"ds_reach_time\"])\n", "    - pd.to_datetime(final_df[\"planned_ds_arrival\"])\n", ").dt.total_seconds() / 60\n", "final_df[\"ds_reach_delay\"] = final_df[\"ds_reach_delay\"].fillna(value=\"NA\")"]}, {"cell_type": "code", "execution_count": null, "id": "c445a989-0654-47cd-89de-90d908ae388d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f2ee74f1-6e81-42ce-90b7-d4fdbd46d847", "metadata": {}, "outputs": [], "source": ["final_df_na = final_df[final_df[\"ds_reach_delay\"] == \"NA\"]\n", "final_df_int = final_df[final_df[\"ds_reach_delay\"] != \"NA\"]"]}, {"cell_type": "code", "execution_count": null, "id": "5fe5ba8a-5460-4a55-9216-1c81d713a9a6", "metadata": {}, "outputs": [], "source": ["print(final_df_na.shape)\n", "print(final_df_int.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "f1933c85-cf9e-41ca-b95c-a2529d05810a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c05a5dbc-d319-4300-9050-57baf0e8a238", "metadata": {}, "outputs": [], "source": ["final_df_int[\"ds_reach_delay\"] = final_df_int[\"ds_reach_delay\"].astype(int)\n", "final_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "10759819-4eaa-494f-93a2-f2042df09bb3", "metadata": {}, "outputs": [], "source": ["final_df_int.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "4fde17cd-7581-4660-8049-8600a44ccc33", "metadata": {}, "outputs": [], "source": ["def ds_delay_bucket(x):\n", "    if x < 0:\n", "        if x <= -60:\n", "            if x <= -120:\n", "                result = \"(<120)\"\n", "            else:\n", "                result = \"(-120 - -60)\"\n", "        else:\n", "            result = \"(-60 - 0)\"\n", "\n", "    elif x >= 0:\n", "        if x >= 30:\n", "            if x >= 60:\n", "                if x >= 120:\n", "                    result = \"(>120)\"\n", "                else:\n", "                    result = \"(60 - 120)\"\n", "            else:\n", "                result = \"(30 - 60)\"\n", "        else:\n", "            result = \"(0 - 30)\"\n", "\n", "    else:\n", "        result = \"NA\"\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "545aec1e-434d-4174-8b16-745e9eddec1b", "metadata": {}, "outputs": [], "source": ["def ds_delay_status(x):\n", "    if x >= 0:\n", "        if x > 60:\n", "            result = \"Delay\"\n", "        else:\n", "            result = \"On-Time\"\n", "\n", "    elif x < 0:\n", "        if x < -30:\n", "            result = \"Early\"\n", "        else:\n", "            result = \"On-Time\"\n", "\n", "    else:\n", "        result = \"Delay\"\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "b649cc46-2ce0-4a62-88a6-8c2afcfc920f", "metadata": {}, "outputs": [], "source": ["final_df_int[\"ds_reach_delay_bucket\"] = final_df_int[\"ds_reach_delay\"].apply(\n", "    lambda x: ds_delay_bucket(x)\n", ")\n", "final_df_int[\"ds_reach_delay_status\"] = final_df_int[\"ds_reach_delay\"].apply(\n", "    ds_delay_status\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5afc8e3b-4d74-4303-96f1-cb7702e9e933", "metadata": {"tags": []}, "outputs": [], "source": ["final_df_int.tail(3)"]}, {"cell_type": "code", "execution_count": null, "id": "31196a17-c77f-48fb-8139-16b2d62c392b", "metadata": {}, "outputs": [], "source": ["final_df_int.ds_reach_delay_bucket.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "35b398b4-8c5e-48ef-89f8-5de91112b0db", "metadata": {}, "outputs": [], "source": ["final_df_na[\"ds_reach_delay_bucket\"] = \"NA\"\n", "final_df_na[\"ds_reach_delay_status\"] = \"Delay\"\n", "final_df_na.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "67f0ab54-638c-4b1d-8681-e2f40f468702", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98cc9821-e5fd-4634-9e68-9598fd84d55a", "metadata": {}, "outputs": [], "source": ["final = pd.concat([final_df_int, final_df_na])"]}, {"cell_type": "code", "execution_count": null, "id": "1b919ad6-13e8-4ba7-8e07-59c2a2dac0d7", "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8ce85999-1895-4d0d-ad96-5bd007e3367b", "metadata": {}, "outputs": [], "source": ["final.sort_values(by=[\"consignment_date\", \"facility_id\", \"ds_outlet_id\"], inplace=True)\n", "# final.drop(columns=[\"otd_month\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d15d2caa-d7cd-4c52-a6bd-901c35819513", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7485de0c-223d-48d7-99b0-e811229f60d5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dbf88e3c-2885-4d30-b35a-c7dfc686d77f", "metadata": {}, "outputs": [], "source": ["final.consignment_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "7c7b2352-6b86-463b-8164-5688645167e8", "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f90d2009-a34e-4162-9abb-21998f87e211", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "11a6751d-847b-49af-b085-439891da5da4", "metadata": {}, "outputs": [], "source": ["mp_query = f\"\"\"\n", "\n", "with active_time as\n", "    (\n", "    select  order_date,\n", "            outlet_id,\n", "            hour_,\n", "            picking_time/60 as picking_time,\n", "            billing_time/60 as billing_time,\n", "            picker_active_time,\n", "            putter_active_time,\n", "            total_active_time,\n", "            COALESCE(((picking_time+billing_time)/60)*1.00/NULLIF(total_active_time,0),0) as pick_util, \n", "            COALESCE(picker_active_time*1.00/NULLIF(total_active_time,0),0) as picker_flow,\n", "            COALESCE(putter_active_time*1.00/NULLIF(total_active_time,0),0) as putter_flow\n", "    from storeops_etls.instore_metrics_hourly\n", "    where order_date >= cast((current_date - interval '14' day) as varchar)\n", "    ),\n", "    \n", "cnt_employees as\n", "    (\n", "    with base as\n", "        (\n", "        select *\n", "        from dwh.agg_hourly_outlet_role_instore_employee_login\n", "        where snapshot_date_ist >= cast((current_date - interval '14' day) as date) \n", "        )\n", "    \n", "    select  outlet_id,\n", "            snapshot_date_ist,\n", "            snapshot_hour_ist,\n", "            count(distinct employee_id) as total_manpower,\n", "            count(distinct case when role = 'PICKER' then employee_id end) as mp_in_picker_flow,\n", "            count(distinct case when role = 'PUTTER' then employee_id end) as mp_in_putter_flow,\n", "            count(distinct case when role = 'AUDITOR' then employee_id end) as mp_in_auditor_flow\n", "    from base\n", "    group by 1,2,3\n", "    ),\n", "    \n", "surge_data as\n", "    (\n", "    with base as\n", "        (\n", "        select  \n", "                insert_ds_ist as date_ist,\n", "                merchant_id,\n", "                city_name,\n", "                hr,\n", "                count(distinct case when surge_reason = 'mec_picker_surge' then run_ts_ist end) as minutes_in_picker_surge,\n", "                count(distinct case when surge_reason = 'mec_fe_surge' then run_ts_ist end) as minutes_in_rider_surge,\n", "                count(distinct case when surge_reason = 'mec_fe_surge_and_mec_picker_surge' then run_ts_ist end) as minutes_in_both_surge,\n", "                count(distinct case when surge_reason = 'rain_surge' then run_ts_ist end) as minutes_in_rain_surge\n", "        from  \n", "            (select  \n", "                    (run_ts + interval '330' minute) as run_ts_ist,\n", "                    date((run_ts + interval '330' minute)) as dt,\n", "                    extract(hour from (run_ts + interval '330' minute)) as hr,\n", "                    merchant_id,\n", "                    city_name,\n", "                    surge_reason,\n", "                    surge_charge,\n", "                    insert_ds_ist\n", "                    from serviceability_controltower.sct_store_state\n", "                    where insert_ds_ist >= cast((current_date - interval '14' day) as varchar)\n", "                      and surge_charge > 0 \n", "            )\n", "        group by 1,2,3,4\n", "        ),\n", "        \n", "    mapping as \n", "        (\n", "        select distinct outlet_id, merchant_id from\n", "             (select *, row_number() over(partition by outlet_id order by orders desc) as rnk\n", "             from\n", "             (select outlet_id,frontend_merchant_id as merchant_id,count(distinct order_id) as orders\n", "             from dwh.fact_sales_order_item_details\n", "             where order_create_dt_ist >= (current_date-interval '14' day)\n", "             group by 1,2))\n", "        where rnk=1\n", "        )\n", "        \n", "    select  outlet_id,\n", "            date_ist,\n", "            hr,\n", "            minutes_in_picker_surge,\n", "            minutes_in_rider_surge,\n", "            minutes_in_both_surge\n", "    from base\n", "    left join mapping map on map.merchant_id = base.merchant_id\n", "    )\n", "    \n", "select  act.*,\n", "        total_manpower,\n", "        mp_in_picker_flow,\n", "        mp_in_putter_flow,\n", "        mp_in_auditor_flow,\n", "        minutes_in_picker_surge,\n", "        minutes_in_rider_surge,\n", "        minutes_in_both_surge\n", "from active_time act\n", "left join cnt_employees emp on act.outlet_id = emp.outlet_id and act.order_date = cast(emp.snapshot_date_ist as varchar) and act.hour_ = emp.snapshot_hour_ist\n", "left join surge_data surge on act.outlet_id = surge.outlet_id and act.order_date = surge.date_ist and act.hour_ = surge.hr\n", "         \n", "\"\"\"\n", "\n", "manpower_df = read_sql_query(mp_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "5483b7a5-ef0d-4a18-bc6e-d4c8ac9e63bf", "metadata": {}, "outputs": [], "source": ["manpower_df.rename(\n", "    columns={\"order_date\": \"consignment_date\", \"outlet_id\": \"ds_outlet_id\"},\n", "    inplace=True,\n", ")\n", "manpower_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3d9ad787-d037-48bb-89f8-b6212120377b", "metadata": {}, "outputs": [], "source": ["manpower_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "01698d22-267b-4189-a53a-b0a493001074", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed056c45-c1ce-43db-ab91-3341d9cb9f7d", "metadata": {}, "outputs": [], "source": ["# manpower_df[(manpower_df['ds_outlet_id']==1889) & (manpower_df['consignment_date']=='2024-03-24')]"]}, {"cell_type": "code", "execution_count": null, "id": "0bf9f141-b50a-4253-89c8-e98e4b628ec8", "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2b898661-38eb-423a-8c0d-26923b5fc51f", "metadata": {}, "outputs": [], "source": ["final[\"ds_reach_hr\"] = pd.to_datetime(final[\"ds_reached\"]).dt.hour\n", "final[\"unload_start_hr\"] = pd.to_datetime(final[\"unloading_start\"]).dt.hour"]}, {"cell_type": "code", "execution_count": null, "id": "08d8f9da-f534-4174-a3fb-80a2c61ec735", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d9c03c65-fd23-4755-826b-beddd32f3557", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a36f4a0d-6896-473e-ba37-c3b1cbabb623", "metadata": {}, "outputs": [], "source": ["final1 = sqldf(\n", "    \"\"\"\n", "with base as\n", "    (\n", "    select *\n", "    from final fin\n", "    left join manpower_df mp \n", "           on fin.ds_outlet_id = mp.ds_outlet_id \n", "          and fin.consignment_date = mp.consignment_date\n", "          and hour_ >= ds_reach_hr\n", "          and hour_ <= unload_start_hr\n", "    )\n", "    \n", "    \n", "select  consignment_id,\n", "        (sum(picking_time)+sum(billing_time))*100.0/sum(total_active_time) as pick_util,\n", "        sum(picker_active_time)*100.0/sum(total_active_time) as picker_flow,\n", "        sum(putter_active_time)*100.0/sum(total_active_time) as putter_flow,\n", "        avg(total_manpower) as total_avg_manpower,\n", "        avg(mp_in_picker_flow) as avg_mp_in_picker_flow,\n", "        avg(mp_in_putter_flow) as avg_mp_in_putter_flow,\n", "        avg(mp_in_auditor_flow) as avg_mp_in_auditor_flow,\n", "        sum(minutes_in_picker_surge) as minutes_in_picker_surge,\n", "        sum(minutes_in_rider_surge) as minutes_in_rider_surge,\n", "        sum(minutes_in_both_surge) as minutes_in_both_surge\n", "from base\n", "group by 1\n", "\n", "\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "745efed4-1f4e-490c-b3f5-80ef72a20534", "metadata": {}, "outputs": [], "source": ["final1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b949bac9-4ec8-4fc2-b47a-0438ee795e8d", "metadata": {}, "outputs": [], "source": ["final1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "90cdc3bf-4ef7-4a9f-ae79-fe8767331407", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce54854d-3b2e-40b0-af0d-9ce9c93d8341", "metadata": {}, "outputs": [], "source": ["data = final.merge(final1, how=\"left\", on=[\"consignment_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "3b9a72e0-e2a9-4a80-8785-790bdaaa99b0", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3ac256b2-8f17-4a52-88d2-1e74cb15eada", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dd494c99-c44a-4b77-bfa4-5e0f4ff3fc45", "metadata": {}, "outputs": [], "source": ["# data.to_csv(\"data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a38978d4-cdab-4718-9553-d07c81b75650", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6f6f349a-0777-4f02-99f1-e1c1c4546474", "metadata": {}, "outputs": [], "source": ["final1 = sqldf(\n", "    \"\"\"\n", "\n", "select  *,\n", "        --lag(grn_started_at,1) over(partition by ds_outlet_id order by ds_reached) as last_grn_start,\n", "        --lag(grn_completed_at,1) over(partition by ds_outlet_id order by ds_reached) as last_grn_end,\n", "        lag(consignment_id,1) over(partition by ds_outlet_id order by ds_reached) as last_con_id\n", "from data\n", "        \n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ddbf8c19-3510-4e4f-9290-aba9a3bca4d8", "metadata": {}, "outputs": [], "source": ["final1[\"last_con_id\"].fillna(0, inplace=True)\n", "final1[\"last_con_id\"] = final1[\"last_con_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1f2deaef-6649-490e-84e5-f0dc826818c8", "metadata": {}, "outputs": [], "source": ["final1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c58126a8-0a7f-4723-9932-c586ee186602", "metadata": {}, "outputs": [], "source": ["consignments = final1.consignment_id.to_list()\n", "consignments = tuple(consignments)\n", "len(consignments)"]}, {"cell_type": "code", "execution_count": null, "id": "1395fc00-517d-4c71-a3d0-796965dba3fb", "metadata": {}, "outputs": [], "source": ["putaway_query = \"\"\"\n", "\n", "select  c.id AS consignment_id,\n", "        er.expected_count as consignment_qty,\n", "        d.external_id as invoice_id,\n", "        e.id,\n", "        e.expected_count as invoice_qty,\n", "        ul.item_id,\n", "        (ul.last_modified_date + interval '330' minute) as last_modified_ts,\n", "        delta_qty\n", "FROM transit_server.transit_consignment c\n", "JOIN transit_server.transit_consignment_document d ON c.id = d.consignment_id\n", "LEFT JOIN storeops.er er on er.external_order_no = cast(c.id as varchar) and er.er_type = 'BULKIN_CONSIGNMENT'\n", "LEFT JOIN storeops.er e on e.reference_entity_id = d.external_id\n", "LEFT JOIN storeops.inventory_update_log ul on e.id = cast(ul.er_id as bigint)\n", "WHERE c.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) AS varchar)\n", "AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) AS varchar)\n", "AND ul.insert_ds_ist >= cast((current_date - interval '15' day) as varchar)\n", "AND c.id in {consignment_list}\n", "AND d.document_type = 'INVOICE'\n", "         \n", "\"\"\".format(\n", "    consignment_list=consignments\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "af071f9f-1c9e-411f-97d4-8c7fe02e4eab", "metadata": {}, "outputs": [], "source": ["putaway_df = read_sql_query(putaway_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "60a25f2b-41b1-4fee-a0e9-d53748162692", "metadata": {}, "outputs": [], "source": ["putaway_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3cb9c060-765b-4470-b972-5d1815612a3c", "metadata": {}, "outputs": [], "source": ["putaway_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7c55f719-664a-4e55-b6f6-fb1118a39430", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "76bcd713-f2fd-4ef8-b127-baa9118437b9", "metadata": {}, "outputs": [], "source": ["combine = sqldf(\n", "    \"\"\"\n", "with base1 as\n", "(\n", "select  f1.consignment_id,\n", "        count(distinct pd1.item_id) as pending_grn_items,\n", "        sum(pd1.delta_qty) as pending_grn_qty\n", "from final1 f1\n", "left join putaway_df pd1 on f1.last_con_id = pd1.consignment_id and pd1.last_modified_ts > f1.ds_reached\n", "group by 1\n", "),\n", "\n", "base2 as\n", "(\n", "select  f1.consignment_id,\n", "        count(distinct pd2.item_id) as item_grned_before_unload,\n", "        sum(pd2.delta_qty) as qty_grned_before_unload\n", "from final1 f1\n", "left join putaway_df pd2 on f1.last_con_id = pd2.consignment_id and pd2.last_modified_ts > f1.ds_reached and pd2.last_modified_ts < f1.unloading_start\n", "group by 1\n", ")\n", "\n", "select  b1.*,\n", "        b2.item_grned_before_unload,\n", "        b2.qty_grned_before_unload\n", "from base1 b1\n", "left join base2 b2 on b1.consignment_id = b2.consignment_id\n", "\n", "\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "090a9737-a3f8-413c-9e3c-26a0236126d6", "metadata": {}, "outputs": [], "source": ["combine"]}, {"cell_type": "code", "execution_count": null, "id": "beaecbdf-e417-45ca-9677-58e03038abd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "19810445-f70a-49c4-8435-af85fb7040d2", "metadata": {}, "outputs": [], "source": ["data = final1.merge(combine, how=\"left\", on=[\"consignment_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "ed70ef7b-9169-4074-b1a4-0814908c1927", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2eec7443-44fa-4ed4-94ed-7f4ab52c8e45", "metadata": {}, "outputs": [], "source": ["# data[data['consignment_id']==780422]"]}, {"cell_type": "code", "execution_count": null, "id": "40fc0963-c20e-4d38-ba09-b6c273de4414", "metadata": {}, "outputs": [], "source": ["data[\"late_unload_start\"] = np.where(\n", "    data[\"ds_reach_to_unload_start\"] > 15, \"Delay\", \"On-Time\"\n", ")\n", "data[\"delta_pick_flow_util\"] = (data[\"picker_flow\"] - data[\"pick_util\"]) / data[\n", "    \"picker_flow\"\n", "]\n", "data[\"percent_grn_bw_reach_unload_start\"] = (\n", "    data[\"qty_grned_before_unload\"] / data[\"pending_grn_qty\"]\n", ")\n", "data[\"late_unload_start_issue\"] = np.where(\n", "    (\n", "        (data[\"late_unload_start\"] == \"Delay\")\n", "        & (data[\"delta_pick_flow_util\"] > 0.4)\n", "        & (data[\"percent_grn_bw_reach_unload_start\"] < 0.5)\n", "    ),\n", "    \"Intent\",\n", "    \"-\",\n", ")\n", "data[\"unloading_time_delays\"] = np.where(data[\"unloading_time_min\"] > 60, \"Delay\", \"-\")"]}, {"cell_type": "code", "execution_count": null, "id": "5b54fb31-d966-434a-8c25-00a92a62c338", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4fc5eae-2334-40c4-bb41-16d0f2bed9cb", "metadata": {}, "outputs": [], "source": ["total_indent = sqldf(\n", "    \"\"\"\n", "\n", "select  consignment_id as last_con_id,\n", "        count(distinct item_id) as total_items,\n", "        max(consignment_qty) as total_indent\n", "from putaway_df\n", "group by 1\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "170e0cdc-ea79-4257-a9c7-5241c374434a", "metadata": {}, "outputs": [], "source": ["data1 = data.merge(total_indent, how=\"left\", on=[\"last_con_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "491594ff-0c4b-4ac1-9222-f2644bbe67c9", "metadata": {}, "outputs": [], "source": ["data1.drop(columns=[\"last_con_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0543e500-c679-4c3c-b218-aa6138ad09a1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8936e465-4be0-4ee2-ae7e-f349c8440486", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b2cc29d4-40ea-452f-8771-bd920ac8ccea", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(data1, \"1dZh0VZc0irc0UrJV-lRo12kF-fjWJxLMgVKm1g6tTAU\", \"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "181b4059-b17e-4966-aa11-ac29de75fd2f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6e11bb12-310a-4a3b-9c38-d6ed3dba6f2d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c4953673-828e-43f1-b3a3-1d4a0d50fc1c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8c73453d-40f7-483b-a676-e8c1ad762d1e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}