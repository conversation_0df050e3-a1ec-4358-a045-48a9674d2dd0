{"cells": [{"cell_type": "code", "execution_count": null, "id": "e38fd822-ce44-4fa5-9e24-31b343293e71", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5c241b93-5f3b-434a-9855-32604c880ef8", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "9705eae0-e0df-4e79-8215-7761f3a52572", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "                \n", "    select distinct order_date,\n", "        item_id,\n", "        facility_id,\n", "        orders,\n", "        order_quantity,\n", "        available_hours,\n", "        potential_order_quantity,\n", "        availability_factor\n", "    from ars.daily_orders_and_availability\n", "    where order_date = current_date-interval'2'day\n", "    and facility_id in (1100,1216,1453,1232,1317,1238,1176,1390,1632,1002,1316,1000,1995,1240,1313,791,790,1239,995,792,1612,1246,996,1629,681\n", "                ,1387,1480,795,1336,1615,1098,1102,1307,1017,1355,1354,1616,1356,228,993,1340,1018,452,793,1634,1243,1244,1237,1483,1096,1231,1626,1707)\n", "\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "30828bca-1938-40d7-ab4d-ba1bf920b930", "metadata": {}, "outputs": [], "source": ["old_logic_aps = read_sql_query(sql, CON_TRINO)"]}, {"cell_type": "code", "execution_count": null, "id": "e1fb2fd4-d705-466d-af6a-80ff379838b2", "metadata": {}, "outputs": [], "source": ["old_logic_aps.shape"]}, {"cell_type": "code", "execution_count": null, "id": "29c3382e-4955-4598-a250-be7d0ebc406f", "metadata": {}, "outputs": [], "source": ["old_logic_aps.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f0a5d04b-7c34-4d26-819a-07030bfb20db", "metadata": {}, "outputs": [], "source": ["old_logic_aps.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "64e4dc64-59c9-40fb-9277-d41c8fb8ba0d", "metadata": {}, "outputs": [], "source": ["old_logic_aps[\"order_date\"] = pd.to_datetime(old_logic_aps[\"order_date\"])\n", "old_logic_aps[\"available_hours\"] = old_logic_aps[\"available_hours\"].astype(\"float\")\n", "old_logic_aps[\"potential_order_quantity\"] = old_logic_aps[\n", "    \"potential_order_quantity\"\n", "].astype(\"float\")\n", "old_logic_aps[\"availability_factor\"] = old_logic_aps[\"availability_factor\"].astype(\n", "    \"float\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d6c7cd3a-5ac0-4168-80a4-394c2c4ce159", "metadata": {}, "outputs": [], "source": ["non_negative_values = old_logic_aps[old_logic_aps[\"potential_order_quantity\"] != -1]\n", "# non_negative_values = non_negative_values.reset_index()\n", "negative_values = old_logic_aps[old_logic_aps[\"potential_order_quantity\"] == -1]\n", "# negative_values = negative_values.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "75d925d4-f09e-44dd-8f03-4aaadadde248", "metadata": {}, "outputs": [], "source": ["non_negative_values[\"old_potential_order_qty\"] = (\n", "    non_negative_values[\"potential_order_quantity\"]\n", "    - non_negative_values[\"order_quantity\"]\n", ")\n", "negative_values[\"old_potential_order_qty\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "bfead7b5-6e02-43ae-a546-6c8310fd8ddd", "metadata": {}, "outputs": [], "source": ["old_dataset = pd.concat([non_negative_values, negative_values], ignore_index=True)\n", "old_dataset = old_dataset.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "afa4d7da-91a6-49c3-b8e6-8b60b42fb218", "metadata": {}, "outputs": [], "source": ["old_dataset.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "8b952ea3-6c8f-4a7e-ac03-301316184ca5", "metadata": {}, "outputs": [], "source": ["old_dataset.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6114259f-a8b3-496c-8b5f-0f5bac022205", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "    select distinct \"date\" as date_,\n", "        item_id,\n", "        name,\n", "        facility_id,\n", "        outlet_id,\n", "        outlet_name,\n", "        merchant_id,\n", "        frontend_merchant_city_id,\n", "        city_name,\n", "        potential_search_atc_sess,\n", "        potential_search_atc_cust,\n", "        potential_search_atc\n", "    from metrics.potential_search_atc_simulation\n", "    where date_ = current_date-2\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1fa2c618-d589-496a-bbc6-939c7c107f5e", "metadata": {}, "outputs": [], "source": ["new_logic_aps = read_sql_query(sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "a033dd0d-2810-4578-b720-fce8262f1588", "metadata": {}, "outputs": [], "source": ["new_logic_aps.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a900ce8a-c6f4-400c-b61e-b7403fee387c", "metadata": {}, "outputs": [], "source": ["new_logic_aps.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "706ca2dc-9d40-40a2-b7d8-e61ce89d4727", "metadata": {}, "outputs": [], "source": ["new_logic_aps.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "d67fe07f-9b1c-4306-bbd5-d4e13ea60426", "metadata": {}, "outputs": [], "source": ["new_logic_aps[\"date_\"] = pd.to_datetime(new_logic_aps[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2afb34ed-aa8a-44f4-8039-0d94286f1429", "metadata": {}, "outputs": [], "source": ["dataset = new_logic_aps.merge(old_dataset, on=[\"facility_id\", \"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "b16a3918-57a2-47fb-9591-03767d0ab8b2", "metadata": {}, "outputs": [], "source": ["dataset.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "318b591e-7a30-4c48-affe-54e2d3c07e8b", "metadata": {}, "outputs": [], "source": ["dataset.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "dea96a2f-7e80-4377-99f0-fbfd3312d354", "metadata": {}, "outputs": [], "source": ["dataset[\"orders\"] = dataset[\"orders\"].fillna(0)\n", "dataset[\"order_quantity\"] = dataset[\"order_quantity\"].fillna(0)\n", "dataset[\"available_hours\"] = dataset[\"available_hours\"].fillna(0)\n", "dataset[\"potential_order_quantity\"] = dataset[\"potential_order_quantity\"].fillna(0)\n", "dataset[\"availability_factor\"] = dataset[\"availability_factor\"].fillna(0)\n", "dataset[\"old_potential_order_qty\"] = dataset[\"old_potential_order_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "83eb61a8-45c6-465f-a58a-6a4deb5ba569", "metadata": {}, "outputs": [], "source": ["dataset.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "727c3538-86f4-4e1c-8b62-2adc29f4da82", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "    create temporary table channel_wise_atc_base as (          \n", "        select distinct \n", "            properties__page_name as page_name,\n", "            coalesce(traits__session_uuid,session_uuid) as session_id,\n", "            device_uuid as device_id,\n", "            coalesce(properties__product_id,properties__child_widget_id, properties__widget_id) as product_id, \n", "            max(properties__quantity) as quantity_added\n", "        from spectrum.mobile_event_data            \n", "        where at_date_ist = current_date-2\n", "        and name = 'Product Added'\n", "        group by 1,2,3,4\n", "    );\n", "\n", "    create temporary table channel_wise_atc as (\n", "        select product_id,\n", "            page_name,\n", "            case \n", "                when page_name in ('Search Page','search','Search List') then 'search_atc'\n", "                else 'non_search_atc'\n", "            end as atc_type,\n", "            count(distinct device_id) as distinct_devices,\n", "            count(distinct session_id) as distinct_sessions,\n", "            sum(quantity_added) as quantity_added\n", "        from channel_wise_atc_base\n", "        group by 1,2\n", "    );\n", "\n", "\n", "    select ipom.item_id,\n", "        page_name,\n", "        atc_type,\n", "        sum(distinct_devices) as channel_wise_distinct_devices,\n", "        sum(distinct_sessions) as channel_wise_distinct_sessions,\n", "        sum(quantity_added) as channel_wise_quantity_added\n", "    from channel_wise_atc cw\n", "    left join dwh.dim_item_product_offer_mapping ipom on cw.product_id = ipom.product_id\n", "    group by 1,2,3\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5886edad-be4d-4ffb-97fc-e8c4f48d0a29", "metadata": {}, "outputs": [], "source": ["channel_wise_atc_events = read_sql_query(sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "33f6c9e5-d2cf-4c5f-a2dd-ce81000383b3", "metadata": {}, "outputs": [], "source": ["channel_wise_atc_events.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "128b3ea5-ff0b-42bf-8d36-1afc6bc651db", "metadata": {}, "outputs": [], "source": ["channel_wise_atc_events.shape"]}, {"cell_type": "code", "execution_count": null, "id": "cb1e8f72-2570-4757-a6b1-d8c750af6cb5", "metadata": {}, "outputs": [], "source": ["channel_wise_atc = (\n", "    channel_wise_atc_events[\n", "        [\n", "            \"item_id\",\n", "            \"atc_type\",\n", "            \"channel_wise_distinct_devices\",\n", "            \"channel_wise_distinct_sessions\",\n", "            \"channel_wise_quantity_added\",\n", "        ]\n", "    ]\n", "    .groupby(by=[\"item_id\", \"atc_type\"])\n", "    .sum()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4db2a098-7ae7-470b-bf3d-02b15126f02f", "metadata": {}, "outputs": [], "source": ["channel_wise_atc = channel_wise_atc.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "cc369839-da48-46bb-9e02-65a202097ba3", "metadata": {}, "outputs": [], "source": ["channel_wise_atc.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "46c9911c-4901-4ad1-b6a3-c52c1c784849", "metadata": {}, "outputs": [], "source": ["channel_wise_atc.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "36eebf2e-c493-4c61-9157-514ff7be5efb", "metadata": {}, "outputs": [], "source": ["channel_wise_atc.shape"]}, {"cell_type": "code", "execution_count": null, "id": "39636f5c-9a06-4724-b778-ff5299eeaff6", "metadata": {}, "outputs": [], "source": ["channel_wise_atc[\"item_id\"] = channel_wise_atc[\"item_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "e50178ec-d2b7-4bc2-983a-22a389e8715e", "metadata": {}, "outputs": [], "source": ["channel_wise_atc = channel_wise_atc.pivot(index=[\"item_id\"], columns=[\"atc_type\"])"]}, {"cell_type": "code", "execution_count": null, "id": "7755af1d-7cab-4a0b-8f6b-f98bb068a7d8", "metadata": {}, "outputs": [], "source": ["channel_wise_atc = channel_wise_atc.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "b9c66a6b-9732-412f-a6ef-e402b6615cbf", "metadata": {}, "outputs": [], "source": ["dataset_base = channel_wise_atc[\"channel_wise_quantity_added\"].reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "87b7226d-670c-4256-aa1b-808188662ef7", "metadata": {}, "outputs": [], "source": ["dataset_trim = dataset_base[dataset_base[\"search_atc\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "a974d2cf-24ba-467f-90f9-1312db7a0dc2", "metadata": {}, "outputs": [], "source": ["dataset_trim = dataset_trim.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0a3f2f72-1fa0-4475-b602-cc0c136ce696", "metadata": {}, "outputs": [], "source": ["dataset_trim[\"factor\"] = (\n", "    dataset_trim[\"non_search_atc\"] + dataset_trim[\"search_atc\"]\n", ") / dataset_trim[\"search_atc\"]"]}, {"cell_type": "code", "execution_count": null, "id": "558e7d11-7880-4b05-894c-9de6728b81e5", "metadata": {}, "outputs": [], "source": ["dataset_trim[\"search_conversion\"] = dataset_trim[\"search_atc\"] / (\n", "    dataset_trim[\"non_search_atc\"] + dataset_trim[\"search_atc\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1621d345-1a44-429b-906f-d6e6cc3552b9", "metadata": {}, "outputs": [], "source": ["dataset_trim[\"non_search_conversion\"] = dataset_trim[\"non_search_atc\"] / (\n", "    dataset_trim[\"non_search_atc\"] + dataset_trim[\"search_atc\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "00879fc5-10ad-41de-ab50-9b18eab1b869", "metadata": {}, "outputs": [], "source": ["dataset_trim"]}, {"cell_type": "code", "execution_count": null, "id": "e0da1865-a880-4742-a663-60c1b0c27c6e", "metadata": {}, "outputs": [], "source": ["final = dataset.merge(\n", "    dataset_trim[[\"item_id\", \"factor\", \"search_conversion\", \"non_search_conversion\"]],\n", "    on=[\"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c66b06ba-c3b7-4aa2-ae87-4442c4d65c10", "metadata": {}, "outputs": [], "source": ["dataset.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "6268b05d-0141-4d51-97ca-087d4a65a39f", "metadata": {}, "outputs": [], "source": ["final.head(4)"]}, {"cell_type": "code", "execution_count": null, "id": "a1911b3f-4ab0-409e-a054-af426c4db155", "metadata": {}, "outputs": [], "source": ["final[\"potential_atc_sess\"] = final[\"potential_search_atc_sess\"] * final[\"factor\"]\n", "final[\"potential_atc_cust\"] = final[\"potential_search_atc_cust\"] * final[\"factor\"]\n", "final[\"potential_atc\"] = final[\"potential_search_atc\"] * final[\"factor\"]\n", "# final[\"potential_atc_sess\"] = final[\"potential_search_atc_sess\"] * final[\"factor\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b9644b4b-97c2-49bb-85ea-cf8f91c65063", "metadata": {}, "outputs": [], "source": ["final[\"potential_search_atc\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "79832f50-3eb2-4942-a27c-99fbb4b0dd9d", "metadata": {}, "outputs": [], "source": ["final[\"potential_atc\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "f1977cbb-5c67-4859-b1d5-06729ddf3358", "metadata": {}, "outputs": [], "source": ["final[\"old_potential_order_qty\"].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "01b2c741-3799-459a-a0ee-90180a086d06", "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "549459c2-5c41-46af-94a7-b99b66d55835", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab4970f9-da40-4b4b-9e78-7ea49253864a", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "    create temporary table pre_atc as (\n", "        select distinct trim(lower(traits__city_name)) as city_name,\n", "            trim(lower(properties__search_actual_keyword))::varchar as keyword,\n", "            coalesce(traits__session_uuid,session_uuid) as session_id,\n", "            device_uuid as device_id,\n", "            coalesce(properties__child_widget_id, properties__widget_id, properties__product_id) as product_id, \n", "            count(at_ist) as quantity_added\n", "        from spectrum.mobile_event_data\n", "            where at_date_ist = current_date - 2\n", "            and name = 'Product Added'\n", "            and traits__user_id is not NULL\n", "            and traits__merchant_id is not NULL\n", "            -- and len(keyword) between 2 and 70\n", "            and cast(coalesce(properties__child_widget_id, properties__widget_id, properties__product_id) as int) is not null\n", "        group by 1,2,3,4,5\n", "    );\n", "\n", "    create temporary table product_atc as (\n", "        select city_name,\n", "            product_id, \n", "            count(session_id) as distinct_atc_sessions,\n", "            count(distinct device_id) as distinct_atc_customers, \n", "            count(device_id) as atcs,\n", "            sum(quantity_added) as quantity_added\n", "        from pre_atc pa\n", "        group by 1,2\n", "    );\n", "\n", "\n", "    create temporary table product_sales as (\n", "        select trim(lower(city_name)) as city_name,\n", "            product_id,\n", "            sum(product_quantity) as quantity_bought\n", "        from dwh.fact_sales_order_item_details\n", "        where cart_checkout_ts_ist >= (current_date-2)\n", "        and cart_checkout_ts_ist < (current_date-1)\n", "        and order_current_status = 'DELIVERED'\n", "        group by 1,2\n", "    );\n", "\n", "\n", "    select ps.city_name as city_name,\n", "        ipom.item_id as item_id,\n", "        sum(multiplier * quantity_bought) as item_quantity_bought,\n", "        sum(multiplier * quantity_added) as item_quantity_added\n", "    from product_sales ps\n", "    left join product_atc pa on ps.product_id = pa.product_id and ps.city_name = pa.city_name\n", "    left join dwh.dim_item_product_offer_mapping ipom on ps.product_id = ipom.product_id\n", "    group by 1,2\n", "    \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "5c3f0c3f-dc56-47fe-a4bb-02d14f492cfb", "metadata": {}, "outputs": [], "source": ["conversion_atc_to_bought = read_sql_query(sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "20a3e63c-84d6-4613-b58b-dc1f565b9405", "metadata": {}, "outputs": [], "source": ["conversion_atc_to_bought\n", "conversion_atc_to_bought = conversion_atc_to_bought.dropna(how=\"any\")\n", "conversion_atc_to_bought = conversion_atc_to_bought.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d9b10520-52b9-43e5-99e5-9a8c04088464", "metadata": {}, "outputs": [], "source": ["conversion_atc_to_bought.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "54267442-9176-48d1-a956-1932de140e5e", "metadata": {}, "outputs": [], "source": ["conversion_atc_to_bought[\"atc_to_brought_conversion\"] = (\n", "    conversion_atc_to_bought[\"item_quantity_bought\"]\n", "    / conversion_atc_to_bought[\"item_quantity_added\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f30ccab5-fd34-4a7d-b41e-ec6bce5aa85c", "metadata": {}, "outputs": [], "source": ["conversion_atc_to_bought[conversion_atc_to_bought[\"atc_to_brought_conversion\"] > 1]"]}, {"cell_type": "code", "execution_count": null, "id": "b0ad4044-0d72-4bcd-b16c-2532af18222f", "metadata": {}, "outputs": [], "source": ["conversion_atc_to_bought[\"atc_to_brought_conversion\"] = np.where(\n", "    conversion_atc_to_bought[\"atc_to_brought_conversion\"] < 1,\n", "    conversion_atc_to_bought[\"atc_to_brought_conversion\"],\n", "    1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a12c3326-ec8d-4bc0-ba7e-0b141764d6b1", "metadata": {}, "outputs": [], "source": ["final.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f46b1906-8368-4ed5-b5b8-5f24ecb8baae", "metadata": {}, "outputs": [], "source": ["final_database = final.merge(\n", "    conversion_atc_to_bought[[\"city_name\", \"item_id\", \"atc_to_brought_conversion\"]],\n", "    on=[\"city_name\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "48750221-ab5d-477b-9372-1b0f232d9181", "metadata": {}, "outputs": [], "source": ["final_database.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c0a48cc1-7a2e-4051-9468-7b2f16326d13", "metadata": {}, "outputs": [], "source": ["final_database.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "4de3e360-ad7d-4b1d-895e-41e40f42237f", "metadata": {}, "outputs": [], "source": ["final_database.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "70264c4b-f985-4629-aada-010d0e2a8467", "metadata": {}, "outputs": [], "source": ["(final_database[\"atc_to_brought_conversion\"] * final_database[\"potential_atc\"]).sum()"]}, {"cell_type": "code", "execution_count": null, "id": "1357e22d-e8d6-4cc9-99a2-0ab00f59397a", "metadata": {}, "outputs": [], "source": ["final_database.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "355e48a4-fd23-4755-ba6e-1b004a864d41", "metadata": {}, "outputs": [], "source": ["kwargs_output = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"potential_sales_simulation\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"item_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"name\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"facility_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"merchant_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"frontend_merchant_city_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"city_name\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_search_atc_sess\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_search_atc_cust\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_search_atc\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"order_date\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"orders\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"order_quantity\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"available_hours\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_order_quantity\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"availability_factor\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"old_potential_order_qty\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"factor\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"search_conversion\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"non_search_conversion\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_atc_sess\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_atc_cust\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"potential_atc\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "        {\"name\": \"atc_to_brought_conversion\", \"type\": \"FLOAT\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\"date_\", \"item_id\", \"facility_id\", \"outlet_id\"],\n", "    # \"sortkey\": [\"at_date_ist\",\"traits__city_name\",\"keyword\"],\n", "    \"incremental_key\": \"date_\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"New APS values \",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "3213e48c-46af-4401-abdb-46cddba2e6a7", "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final_database, **kwargs_output)"]}, {"cell_type": "code", "execution_count": null, "id": "1b1a14d5-d403-4529-bd98-bd3681b721aa", "metadata": {}, "outputs": [], "source": ["print(\"Good Work !! :)\")"]}, {"cell_type": "code", "execution_count": null, "id": "82d9a234-c18f-48ee-9a73-57f2cbfba4ce", "metadata": {}, "outputs": [], "source": ["print(dataset[\"date_\"][0])"]}, {"cell_type": "code", "execution_count": null, "id": "73e3aeac-e127-4f71-ba04-cd9b048170fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b031344c-c1b8-4f4c-83ae-ae68f53a2aff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}