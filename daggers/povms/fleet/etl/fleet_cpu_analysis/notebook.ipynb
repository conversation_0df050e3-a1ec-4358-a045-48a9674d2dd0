{"cells": [{"cell_type": "code", "execution_count": null, "id": "396e1616-37b6-426c-b5a3-b8457c5878d6", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "5006deb3-4543-4e6e-ae8d-af7878ee3c9e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from pandasql import sqldf\n", "from dateutil.relativedelta import relativedelta\n", "import calendar\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "8e89090e-5f6e-4f1b-9c52-94f1eade2c2e", "metadata": {}, "outputs": [], "source": ["trino_schema_name = \"supply_etls\"  # supply_etls playground\n", "trino_table_name = \"cpu_analysis\"\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "6cf7e607-244b-475e-b18c-000465b4964c", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "f3e523de-4360-4006-a238-5b424fef374c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd88eb8a-40ec-433e-81d5-1b3145c6ed90", "metadata": {}, "outputs": [], "source": ["util_query = f\"\"\"\n", "with trip_base as \n", "    (\n", "    SELECT DISTINCT \n", "              pt.trip_id,\n", "              pt.state AS trip_state,\n", "              pt.source_node_id,\n", "              cast(pc.consignment_id as int) as consignment_id,\n", "              pc.destination_id,\n", "              co1.id as wh_outlet_id,\n", "              case \n", "                when coalesce(co1.facility_id,cast(t.source_store_id as int)) = 1743 then 264\n", "                else coalesce(co1.facility_id,cast(t.source_store_id as int))\n", "              end as facility_id,\n", "              n.external_name AS facility_name,\n", "              m.outlet_id AS ds_outlet_id,\n", "              co.name AS ds_outlet_name,\n", "             case \n", "                when trim(co.location)='Bangalore' then 'Bengaluru' \n", "                when co.location in ('Bhiwadi') then 'Bhiwadi - Not in use'\n", "                when co.location in ('Ghaziabad') then 'UP-NCR'\n", "                when co.location in ('Gurgaon','Jhajjar') then 'HR-NCR'\n", "                else trim(co.location) \n", "              end as billable_destination_city,  --billable city names random in Vendor Rate Card, hence doing hardcode correction here \n", "              pt.truck_number,\n", "              case \n", "                    when (cast(json_extract(t.metadata,'$.vehicle_type') as varchar)) = '' or (cast(json_extract(t.metadata,'$.vehicle_type') as varchar) is null)\n", "                    then cast(json_extract(pt.truck_meta,'$.vehicle_type') as varchar)\n", "                    else cast(json_extract(t.metadata,'$.vehicle_type') as varchar)\n", "              end AS truck_type,\n", "              expected_containers,\n", "              cast(json_extract(t.metadata,'$.dispatched_containers') as int) AS dispatched_containers,\n", "              max(coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE))) as trip_creation_time,\n", "              max(cast(coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as date)) as trip_creation_date\n", "                       \n", "        FROM transit_server.transit_consignment t\n", "        JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "        JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "        \n", "        LEFT JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "        JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id AND m.active = TRUE AND m.id NOT IN (611,1538)\n", "        JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "        LEFT JOIN\n", "                 (SELECT DISTINCT id,\n", "                                  facility_id\n", "                  FROM retail.console_outlet\n", "                  WHERE active = 1\n", "                    AND id!= 1638\n", "                    AND facility_id!= 806\n", "                    AND business_type_id IN (1,12,19,20) \n", "                ) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "        left join \n", "                (\n", "                    select distinct trip_id, (actual_ts + interval '330' MINUTE) as trip_creation_time_1\n", "                    from transit_server.transit_projection_trip_event_timeline\n", "                    where insert_ds_ist between cast(current_date - interval '45' day as varchar) and cast(current_date as varchar)\n", "                    and event_type = 'LOADING_START'\n", "                    and actual_ts is not null\n", "                    and cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT'\n", "                    and lake_active_record\n", "                ) as pte\n", "        on pt.trip_id = pte.trip_id\n", "        \n", "        WHERE pt.insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "        and t.insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "        and pc.insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "        and cast(json_extract(pt.source_node_meta,'$.store_type') AS varchar)!= 'HYPERPURE'\n", "        and (t.type in ('FORWARD_CONSIGNMENT', '') OR t.type is null)\n", "        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n", "    ),\n", "    \n", "ctype AS\n", "    (\n", "    with base as( select  distinct c.id as consignment_id,\n", "                e.external_id as container_id,\n", "                ids.item_id,\n", "                CASE WHEN ids.perishable = 1 THEN 'Perishable' ELSE 'Grocery' END AS item_type,\n", "                obi.quantity,\n", "                pp.weight_in_gm,\n", "                item_factor,\n", "                (obi.quantity*pp.weight_in_gm) as total_wt,\n", "                (obi.quantity*ids.length_in_cm*ids.breadth_in_cm*ids.height_in_cm) as total_vol\n", "        from transit_server.transit_consignment c\n", "        left join transit_server.transit_consignment_container e on e.consignment_id=c.id\n", "        left join wms.outbound_billing_item obi on obi.container_key = cast(json_extract(e.metadata,'$.container_key') as varchar) \n", "        and (obi.insert_ds_ist >= cast(current_date - interval '50' day as varchar))\n", "        left join rpc.product_product pp on pp.variant_id = obi.variant_id\n", "        left join rpc.item_details ids on ids.item_id = pp.item_id\n", "        left join supply_etls.item_factor fac on fac.item_id = pp.item_id\n", "        where c.insert_ds_ist >= cast(current_date - interval '50' day as varchar)\n", "          and e.insert_ds_ist >= cast(current_date - interval '50' day as varchar)\n", "          and e.state not in ('DISCARDED_AT_SOURCE','PENDING_AT_SOURCE','MISSING_AT_SOURCE')\n", "          and pp.handling_type != '8'\n", "        ),\n", "        consignment_type as\n", "        (\n", "        select  consignment_id,\n", "                max(case when rnk = 1 then item_type end) as con_type\n", "        from\n", "            (\n", "            select  consignment_id,\n", "                    item_type,\n", "                    RANK() OVER (PARTITION BY consignment_id ORDER BY type_count DESC) AS rnk\n", "            from\n", "                (\n", "                select  consignment_id,\n", "                        item_type,\n", "                        count(distinct item_id) AS type_count\n", "                from base \n", "                group by 1,2\n", "                )\n", "            )\n", "        group by 1\n", "        )\n", "\n", "    select  base.consignment_id,\n", "            con_type,\n", "            count(distinct case when container_id like '%%CASE%%' then container_id end) as count_cases,\n", "            count(distinct case when container_id like '%%TEMP%%' then container_id end) as count_temp_crates,\n", "            count(distinct case when container_id like '%%PERM%%' then container_id end) as count_perm_crates,\n", "            sum(case when container_id like '%%PERM%%' then quantity end) as perm_disp_qty,\n", "            sum(quantity) as dispatched_qty,\n", "            sum(total_wt) as dispatched_qty_weight,\n", "            sum(total_vol) as indent_volume,\n", "            avg(item_factor) as avg_item_factor\n", "    from base\n", "    left join consignment_type ct on base.consignment_id = ct.consignment_id\n", "    group by 1,2 \n", "    ),\n", "\n", "route_base as \n", "    (\n", "        select  distinct truck_number,\n", "                cast(source_node_id as varchar) as source_node_id,\n", "                destination_node_id,\n", "                -- billable_destination_city,\n", "                truck_billing_type,\n", "                truck_type,\n", "                (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                vendor_id,\n", "                name as vendor_name,\n", "                km_slab,\n", "                hr_slab,\n", "                expected_trips_per_day\n", "            \n", "        from fleet_management.fleet_management_truck_vendor_mapping as tvm\n", "        join fleet_management.fleet_management_node_billable_city_mapping as bcm on tvm.id = bcm.truck_vendor_mapping_id\n", "        left join fleet_management.fleet_management_vendor as hs on hs.id = tvm.vendor_id and active = True\n", "        where tvm.is_active= True\n", "          and bcm.is_active= True\n", "          and source_node_id is not null\n", "    ),\n", "\n", "trip_vendor_base as \n", "    (\n", "        select \n", "            distinct tb.trip_id,\n", "            trip_state,\n", "            cast(tb.trip_creation_time as date) as trip_creation_date,\n", "            tb.trip_creation_time,\n", "            rb.source_node_id,\n", "            tb.consignment_id,\n", "            tb.billable_destination_city,\n", "            tb.destination_id,\n", "            tb.truck_number,\n", "            rb.truck_type,\n", "            expected_containers,dispatched_containers,\n", "            rb.vendor_id,\n", "            rb.vendor_name,\n", "            rb.truck_billing_type,\n", "            rb.km_slab,\n", "            rb.hr_slab,\n", "            rb.expected_trips_per_day,\n", "            tb.wh_outlet_id,\n", "            tb.facility_id,\n", "            tb.facility_name,\n", "            tb.ds_outlet_id,\n", "            tb.ds_outlet_name\n", "                           \n", "        from trip_base as tb\n", "        left join route_base as rb on tb.truck_number = rb.truck_number and tb.destination_id = rb.destination_node_id \n", "               and (tb.trip_creation_time >= rb.tenure_start and tb.trip_creation_time < rb.tenure_end)\n", "    ),\n", "\n", "\n", "vendor_rate_card as \n", "    (\n", "        with base_charge as ( \n", "            with km_base_charge as (\n", "                select distinct \n", "                    vendor_rate_card_config_id,\n", "                    vendor_id,\n", "                    source_node_id,\n", "                    external_name as source_name,\n", "                    destination_city,\n", "                    hs.name as vendor_name,\n", "                    truck_type,\n", "                    truck_billing_type,\n", "                    (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                    (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                    p.value as km_slab,\n", "                    p.rule_id,\n", "                    output as base_cost\n", "                    \n", "                from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "                left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                \n", "                where vrc.is_active and vrcm.is_active\n", "                and attribute_name = 'km'\n", "                and rule.rule_name = 'BASE_PAY'\n", "            ),\n", "            \n", "            hr_base_charge as \n", "            (\n", "                select distinct \n", "                    vendor_rate_card_config_id,\n", "                    vendor_id,\n", "                    source_node_id,\n", "                    external_name as source_name,\n", "                    destination_city,\n", "                    hs.name as vendor_name,\n", "                    truck_type,\n", "                    truck_billing_type,\n", "                    (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                    (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                    p.value as hr_slab,\n", "                    p.rule_id,\n", "                    output as base_cost\n", "                    \n", "                from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "                left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                \n", "                where vrc.is_active and vrcm.is_active\n", "                and attribute_name = 'hour'\n", "                and rule.rule_name = 'BASE_PAY'\n", "                \n", "            )\n", "            \n", "            select distinct kb.*, hb.hr_slab\n", "            from km_base_charge as kb\n", "            join hr_base_charge as hb\n", "            on kb.vendor_rate_card_config_id = hb.vendor_rate_card_config_id and kb.base_cost = hb.base_cost and kb.rule_id = hb.rule_id\n", "        ),\n", "    \n", "        extra_charge as \n", "        (\n", "            with km_extra_charge as \n", "                (\n", "                    select \n", "                        distinct vendor_rate_card_config_id,\n", "                        vendor_id,\n", "                        source_node_id,\n", "                        external_name as source_name,\n", "                        destination_city,\n", "                        hs.name as vendor_name,\n", "                        truck_type,\n", "                        truck_billing_type,\n", "                        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                        output as extra_cost_per_km\n", "                        \n", "                    from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                    join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                    join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                    left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                    left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                    \n", "                    where vrc.is_active and vrcm.is_active\n", "                    and rule.rule_name = 'ADDITIONAL_COST_PER_KM'\n", "                ),\n", "            \n", "            hr_extra_charge as \n", "                (\n", "                    select \n", "                        distinct vendor_rate_card_config_id,\n", "                        vendor_id,\n", "                        source_node_id,\n", "                        external_name as source_name,\n", "                        destination_city,\n", "                        hs.name as vendor_name,\n", "                        truck_type,\n", "                        truck_billing_type,\n", "                        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                        output as extra_cost_per_hr\n", "                        \n", "                    from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                    join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                    join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                    left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                    left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                    \n", "                    where vrc.is_active and vrcm.is_active\n", "                    and rule.rule_name = 'ADDITIONAL_COST_PER_HR'\n", "                )\n", "            \n", "            select distinct ke.*, he.extra_cost_per_hr\n", "            from km_extra_charge as ke\n", "            join hr_extra_charge as he\n", "            on ke.vendor_rate_card_config_id = he.vendor_rate_card_config_id \n", "        )\n", "    \n", "        select distinct \n", "            bc.source_node_id,\n", "            bc.source_name,\n", "            case when bc.destination_city in ('Goa','SouthGoa','NorthGoa') then 'Goa' else bc.destination_city end as destination_city,\n", "            bc.vendor_id,\n", "            bc.vendor_name,\n", "            bc.truck_type,\n", "            bc.truck_billing_type,\n", "            cast(bc.base_cost as DOUBLE) as base_cost,\n", "            bc.tenure_start,\n", "            bc.tenure_end,\n", "            cast(bc.km_slab as int) as km_slab,\n", "            cast(bc.hr_slab as int) as hr_slab,\n", "            ec.extra_cost_per_hr,\n", "            ec.extra_cost_per_km\n", "            \n", "        from base_charge as bc\n", "        left join extra_charge as ec on bc.vendor_rate_card_config_id = ec.vendor_rate_card_config_id\n", "    ),\n", "    \n", "rate_card_base as \n", "    (\n", "    select  *,\n", "            case when rnk = 1 then 'L1'\n", "                 when rnk = 2 then 'L2'\n", "                 when rnk = 3 then 'L3'\n", "                 when rnk = 4 then 'L4'\n", "                 when rnk = 5 then 'L5'\n", "                 when rnk = 6 then 'L6'\n", "                 when rnk = 7 then 'L7'\n", "                 when rnk = 8 then 'L8'\n", "                 when rnk = 9 then 'L9'\n", "                 when rnk = 10 then 'L10'\n", "                 when rnk = 11 then 'L11'\n", "                 when rnk = 12 then 'L12'\n", "                 when rnk = 13 then 'L13'\n", "                 when rnk = 14 then 'L14'\n", "                 when rnk = 15 then 'L15'\n", "            end as current_vendor_rank\n", "    from \n", "        (\n", "        select  *,\n", "                dense_rank() over (partition by destination_city,source_name,truck_type,km_slab,hr_slab order by base_cost) as rnk\n", "        from vendor_rate_card\n", "        --where tenure_start <= cast(current_date - interval '45' day as varchar)\n", "        where  tenure_start <= cast(current_date - interval '45' day as date)\n", "          and tenure_end >= cast(current_date - interval '45' day as date)\n", "          --and tenure_end >= cast((current_date) as timestamp)\n", "          and vendor_id not in (1097,1009,1018,988,1099,943,997,999,991,1027,941,1062,1029,1023,1101,1142,1017,1169,995)\n", "        )\n", "    ),\n", "\n", "extra_charges_base as \n", "    (\n", "        select \n", "            trip_id,\n", "            sum(case when attribute_type = 'EXTRA_HOURS' then cast(value as DOUBLE) end) as extra_hours,\n", "            sum(case when attribute_type = 'EXTRA_KM' then cast(value as DOUBLE) end) as extra_kms,\n", "            sum(case when attribute_type = 'MISCELLANEOUS_CHARGES' then cast(value as DOUBLE) end) as misc_charge\n", "          \n", "        from fleet_management.fleet_management_trip_adhoc_costs_attributes\n", "        where value <> 'None'\n", "        group by 1\n", "    ),\n", "    \n", "cms_cost as \n", "    (\n", "        with trips as\n", "    (\n", "    select  date(trip_date) as trip_date,\n", "            --facility_id as wh_facility_id,\n", "            wh_facility_id as wh_facility_id,\n", "            wh_outlet_name,\n", "            ds_outlet_id,\n", "            ds_outlet_name,\n", "            --sum(indent)  as total_indent\n", "            sum(dispatched_qty)  as total_indent\n", "    from supply_etls.fleet_trips\n", "    where trip_date >= cast(current_date - interval '45' day as varchar)\n", "    group by 1,2,3,4,5\n", "    ),\n", "\n", "cost_sheet as\n", "    (\n", "    select  date_ as trip_creation_date,\n", "            source_facility_id,\n", "            source_name,\n", "            destination_outlet_id,\n", "            --destination_name,\n", "            sum(date_cost) as total_cost\n", "    from supply_etls.blinkit_middle_mile_fleet_cost\n", "    where date_ >= cast(current_date - interval '45' day as date)\n", "    group by 1,2,3,4--,5\n", "    ),\n", "\n", "cms_trip_cost as \n", "    (\n", "    with cost_base as\n", "        (\n", "        select  cte.*,\n", "                cast(trim('][' from (array_join(split(value, ','), ','))) as varchar) AS ds_node_id,\n", "                cast(json_extract(cost_breakdown,'$.extra_hr_cost') as int) AS extra_hr_cost,\n", "                cast(json_extract(cost_breakdown,'$.extra_km_cost') as int) AS extra_km_cost,\n", "                cast(json_extract(cost_breakdown,'$.extra_driver_cost_for_trip') as int) AS extra_driver_cost_for_trip\n", "        from fleet_management.fleet_management_trips_cost cte\n", "        cross join UNNEST(destination_node_ids) as t(value)\n", "        where insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "          and status <> 'DISCARDED'\n", "        )\n", "\n", "\n", "    select  max(cast(coalesce(trip_creation_time_1, (pt.install_ts + interval '330' MINUTE)) as date)) as trip_creation_date,\n", "            cte.*\n", "    from\n", "        (   \n", "        select  tc.trip_id,\n", "                m1.outlet_id as wh_outlet_id,\n", "                co1.facility_id as wh_facility_id,\n", "                m1.name as wh_outlet_name,\n", "                m2.outlet_id as ds_outlet_id,\n", "                m2.name as ds_outlet_name,\n", "                case when trim(co2.location) = 'Bangalore' then 'Bengaluru' else trim(co2.location) end as destination_city,\n", "                tc.truck_number,\n", "                tc.truck_type,\n", "                billable_truck_type,\n", "                truck_billing_type,\n", "                km_slab as total_km_travelled,\n", "                hr_slab as total_hr_travelled,\n", "                cast(json_extract(vendor_details,'$.id') as varchar) as vendor_id,\n", "                cast(json_extract(vendor_details,'$.name') as varchar) as vendor_name,\n", "                fixed_cost + misc_charges as total_trip_cost,\n", "                status,\n", "                (fixed_cost + misc_charges)/count(*) over (partition by tc.trip_id) as total_con_cost\n", "        from cost_base tc\n", "        left join retail.console_outlet_logistic_mapping m1 ON cast(m1.logistic_node_id as varchar) = tc.source_node_id\n", "        left join retail.console_outlet_logistic_mapping m2 ON cast(m2.logistic_node_id as varchar) = tc.ds_node_id\n", "        left join retail.console_outlet co1 ON co1.id = m1.outlet_id and co1.name not like '%%HOT%%' and co1.name <> 'K3 T K4' --and co.business_type_id in (1,12,19,20)\n", "        left join retail.console_outlet co2 ON co2.id = m2.outlet_id \n", "        where m1.lake_active_record\n", "          and m2.lake_active_record\n", "        ) cte\n", "    left join\n", "            (\n", "                select distinct trip_id, (actual_ts + interval '330' MINUTE) as trip_creation_time_1\n", "                from transit_server.transit_projection_trip_event_timeline\n", "                where insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "                and event_type = 'LOADING_START'\n", "                and actual_ts is not null\n", "                and cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT'\n", "                and lake_active_record\n", "            ) as pte on cte.trip_id = pte.trip_id\n", "    left join transit_server.transit_projection_trip pt ON pt.trip_id = cte.trip_id \n", "    and (pt.insert_ds_ist between cast(current_date - interval '45' day as varchar) and cast(current_date as varchar))\n", "    where status = 'APPROVED'\n", "    group by 2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19\n", "    ),\n", "    \n", "cms_day_cost as\n", "    (\n", "    select  trip_creation_date,\n", "            wh_facility_id,\n", "            ds_outlet_id,\n", "            sum(total_con_cost) as total_cost\n", "    from cms_trip_cost\n", "    group by 1,2,3\n", "    ),\n", "\n", "flagging as\n", "    (\n", "    select  trip_date, \n", "            t.wh_facility_id,\n", "            t.wh_outlet_name, \n", "            t.ds_outlet_id, \n", "            t.ds_outlet_name,\n", "            t.total_indent,\n", "            cs.total_cost as cs_cost,\n", "            cms.total_cost as cms_cost,\n", "            CASE WHEN cs.total_cost IS NULL OR cs.total_cost = 0 THEN 0 ELSE 1 END AS cost_sheet_flag\n", "            --CASE WHEN cs.total_cost IS NULL OR cs.total_cost = 0 THEN cms.total_cost ELSE cs.total_cost END AS total_cost\n", "    from trips t\n", "    left join cost_sheet cs on date(trip_date) = date(cs.trip_creation_date) and cast(cs.source_facility_id as int) = cast(t.wh_facility_id as int)\n", "        and cast(t.ds_outlet_id as int) = cast(cs.destination_outlet_id as int)\n", "    left join cms_day_cost cms on cast(cms.trip_creation_date as date)= cast(t.trip_date as date) and  cast(t.wh_facility_id as int) = cms.wh_facility_id \n", "        and cast(t.ds_outlet_id as int) = cast(cms.ds_outlet_id as int)\n", "    ),\n", "\n", "trip_data as\n", "    (\n", "    select  date(trip_date) as trip_date,\n", "            trip_id,\n", "            wh_facility_id as facility_id,\n", "            wh_outlet_name,\n", "            ds_outlet_id,\n", "            ds_outlet_name, \n", "            sum(dispatched_qty) as indent, \n", "            sum(dispatched_qty_weight) as indent_wt, \n", "            sum(dispatched_volume) as indent_volume\n", "    from supply_etls.fleet_trips\n", "    where trip_date >= cast(current_date - interval '45' day as varchar)\n", "    group by 1,2,3,4,5,6\n", "    ),\n", "\n", "indent_weight as\n", "    (\n", "    select t.trip_date,\n", "           trip_id,\n", "           t.facility_id, \n", "           t.wh_outlet_name,\n", "           t.ds_outlet_id,\n", "           t.indent,\n", "           f.total_indent,\n", "           ROUND(t.indent * 1.00 / NULLIF(f.total_indent, 0), 2) AS indent_proportion,\n", "           cost_sheet_flag,\n", "           f.cs_cost,\n", "           f.cms_cost\n", "    from trip_data t\n", "    left join flagging f on f.wh_facility_id=t.facility_id and f.ds_outlet_id=t.ds_outlet_id and f.trip_date = t.trip_date\n", "    ),\n", "    \n", "\n", "final as\n", "    (\n", "    select  t.*, \n", "            case when cost_sheet_flag = 1 then cs_cost*indent_proportion else cms.total_trip_cost end as trip_cost\n", "    from indent_weight t\n", "    --left join cost_sheet cs on cs.source_facility_id = t.facility_id and cs.destination_outlet_id = t.ds_outlet_id and cs.trip_creation_date = t.trip_date\n", "    left join cms_trip_cost cms on cms.trip_id= t.trip_id\n", "    )\n", "\n", "select  NULL as consignment_id,\n", "        trip_id,\n", "        0 as fixed_cost,\n", "        0 as tolls_cost,\n", "        0 as misc_charges,\n", "        0 as extra_km_cost,\n", "        0 as extra_hr_cost,\n", "        0 as extra_driver_cost_for_trip,\n", "        trip_cost as total_trip_cost,\n", "         NULL as status,\n", "        trip_cost as total_con_cost\n", "        \n", "from final\n", "        \n", "--where trip_cost is null\n", "    ),\n", "\n", "\n", "pre_final as \n", "    (\n", "    select distinct\n", "        vb.trip_id,\n", "        trip_state,\n", "        vb.trip_creation_date,\n", "        vb.trip_creation_time,\n", "        vb.facility_id,\n", "        vb.facility_name as wh_name,\n", "        vb.consignment_id,\n", "        vb.billable_destination_city as destination_city,\n", "        vb.ds_outlet_id as outlet_id,\n", "        vb.ds_outlet_name as destination_name,\n", "        vb.truck_number,\n", "        vb.truck_type,\n", "        expected_containers,\n", "        dispatched_containers,\n", "        vb.vendor_id,\n", "        vb.vendor_name,\n", "        vb.truck_billing_type,\n", "        round(2*wh.distance_km,0) as wh_to_ds_distance,\n", "        case when location_ping_source = 'transit-android' or travelled_distance_kms is null then cms.trip_kms else kms.travelled_distance_kms end as actual_kms,\n", "        \n", "        vb.km_slab,\n", "        vb.hr_slab,\n", "        current_vendor_rank,\n", "        vr.base_cost,\n", "        vr.extra_cost_per_hr,\n", "        vr.extra_cost_per_km,\n", "        -- tb.tolls_cost/count(consignment_id) over(partition by vb.trip_id) as tolls_cost,\n", "        -- case when ce.trip_id is not null then 1 else 0 end as cancelled_before_enroute,\n", "        -- sd.trips as same_day_trips,\n", "        case \n", "            when vb.truck_billing_type = 'FIXED' then cast(base_cost as DOUBLE)/day(last_day_of_month(vb.trip_creation_date))\n", "            else cast(base_cost as DOUBLE)\n", "        end as per_day_cost,\n", "        ec.extra_hours,\n", "        ec.extra_kms,\n", "        -- cast(extra_cost_per_hr as DOUBLE)*ec.extra_hours as extra_hr_cost,\n", "        -- (cast(extra_cost_per_km as DOUBLE)*ec.extra_kms) as extra_km_cost_of_trip,\n", "        -- count(consignment_id) over (partition by vb.trip_id) as num_of_consign,\n", "        row_number() over (partition by vb.trip_id order by trip_creation_time) as num\n", "        \n", "    from trip_vendor_base as vb\n", "    left join rate_card_base as vr on vb.vendor_id = vr.vendor_id and vb.source_node_id = cast(vr.source_node_id as varchar)\n", "        and vb.billable_destination_city = vr.destination_city and vb.truck_type = vr.truck_type \n", "        and (vb.trip_creation_time >= vr.tenure_start and vb.trip_creation_time < vr.tenure_end)\n", "        and vb.km_slab = vr.km_slab  \n", "        and vb.hr_slab = vr.hr_slab \n", "        and vb.truck_billing_type = vr.truck_billing_type\n", "        \n", "    left join extra_charges_base as ec on vb.trip_id = ec.trip_id\n", "    left join supply_etls.wh_to_ds_distance wh on wh.wh_facility_id = vb.facility_id and wh.ds_outlet_id = vb.ds_outlet_id\n", "    left join \n", "            (\n", "                select  trip_id,\n", "                        km_slab as trip_kms,\n", "                        hr_slab\n", "                from fleet_management.fleet_management_trips_cost\n", "                where insert_ds_ist >= cast(current_date - interval '50' day as varchar)\n", "            ) cms on vb.trip_id = cms.trip_id\n", "    left join \n", "            (\n", "            select  seg.trip_id,\n", "                    pt.location_ping_source,\n", "                    sum(travelled_distance_metres/1000) as travelled_distance_kms\n", "            from transit_server.transit_travel_segment seg\n", "            left join transit_server.transit_projection_trip pt on pt.trip_id = seg.trip_id\n", "            where seg.insert_ds_ist >= cast(current_date - interval '50' day as varchar)\n", "              and pt.insert_ds_ist >= cast(current_date - interval '50' day as varchar)\n", "            group by 1,2\n", "            ) kms on kms.trip_id = vb.trip_id\n", "    ),\n", "\n", "\n", "final as \n", "    (\n", "    select  pf.*,\n", "            sum(coalesce(actual_kms,wh_to_ds_distance)) over (partition by truck_number,trip_creation_date) as total_distance_covered,  --Total distance by a truck on any given day\n", "            -- per_day_cost/(same_day_trips * num_of_consign) as fixed_cost,\n", "            -- case \n", "            --     when cancelled_before_enroute = 1 then extra_hr_cost\n", "            --     else extra_hr_cost + tolls_cost\n", "            -- end as misc_cost,\n", "            max(num) over (partition by pf.trip_id) as max_nums,\n", "            fixed_cost,\n", "            tolls_cost,\n", "            misc_charges,\n", "            extra_km_cost,\n", "            extra_hr_cost,\n", "            extra_driver_cost_for_trip,\n", "            total_trip_cost,\n", "            status,\n", "            total_con_cost\n", "    from pre_final pf\n", "    left join cms_cost cms on cms.trip_id = pf.trip_id\n", "    ),\n", "\n", "final_base as \n", "    (\n", "    select  distinct final.*, \n", "            --((coalesce(actual_kms,wh_to_ds_distance)/total_distance_covered)*per_day_cost) as cost_per_trip,\n", "            total_trip_cost as cost_per_trip,\n", "            total_trip_cost/(coalesce(actual_kms,wh_to_ds_distance)) as cost_per_trip_per_km,\n", "            \n", "            -- per_day_cost/total_distance_covered as cost_per_trip_per_km,\n", "            -- (fixed_cost + coalesce(tolls_cost,0)) as fixed_cost_plus_tolls,\n", "            -- (fixed_cost + coalesce(tolls_cost,0) + coalesce(extra_hr_cost,0)) as total_cost,\n", "            case when max_nums = 1 then 'Non-Milk Run' else 'Milk Run' end as trip_type,\n", "            date_diff('minute', cast(loading_start_wh as timestamp), cast(coalesce(unloading_completed_wh,truck_return_wh) as timestamp)) as total_trip_time,\n", "            date_diff('minute', cast(ready_for_dispatch as timestamp), cast(ds_reached as timestamp)) + date_diff('minute', cast(coalesce(loading_end_ds,unloading_end_ds) as timestamp), cast(coalesce(truck_return_wh,completed_ts) as timestamp)) as transit_time\n", "    from final\n", "    left join supply_etls.fleet_trips fmd on fmd.consignment_id = final.consignment_id\n", "    where trip_date >= cast(current_date - interval '50' day as varchar)\n", "    )\n", "\n", "    \n", "select  final_base.trip_id,\n", "        final_base.consignment_id,\n", "        trip_type,\n", "        trip_state,\n", "        trip_creation_date,\n", "        trip_creation_time,\n", "        final_base.facility_id,\n", "        wh_name,\n", "        destination_city,\n", "        final_base.outlet_id,\n", "        destination_name,\n", "        truck_number,\n", "        truck_type,\n", "        case when truck_type in ('<PERSON><PERSON> Ace', 'Chiller Tata Ace', 'Reefer <PERSON> Ace') then 750\n", "             when truck_type in ('Pickup','Chiller Pickup', 'Reefer Pickup') then 1200\n", "             when truck_type in ('8 FT','10 FT','Reefer 8 FT','Chiller 8 FT','Reefer 10 FT','Chiller 10 FT') then 1200\n", "             when truck_type in ('22 FT', 'Chiller 22 FT','Reefer 22 FT') then 6000\n", "             when truck_type in ('Tata 407','Reefer Tata 407','Chiller Tata 407')  then 2500\n", "             when truck_type in ('14 FT','Reefer 14 FT','Chiller 14 FT') then 3000\n", "             when truck_type in ('17 FT','Reefer 17 FT','Chiller 17 FT') then 4000\n", "             when truck_type in ('20 FT','Reefer 20 FT','Chiller 20 FT') then 5000\n", "             when truck_type in ('Tata 407','Reefer Tata 407','Chiller Tata 407') then 2500\n", "             when truck_type in ('Tata Intra V50','Reefer Tata Intra V50','Chiller Tata Intra V50') then 2000\n", "             when truck_type in ('24 FT','Reefer 24 FT','Chiller 24 FT') then 8500\n", "             when truck_type in ('32 FT','Reefer 32 FT','Chiller 32 FT') then 10000\n", "        end as truck_capacity,\n", "        \n", "        expected_containers,dispatched_containers,\n", "        count_cases,\n", "        count_temp_crates,\n", "        count_perm_crates,\n", "        perm_disp_qty,\n", "        dispatched_qty,\n", "        dispatched_qty_weight/1000 as dispatched_qty_weight,\n", "        vendor_id,\n", "        vendor_name,\n", "        truck_billing_type,\n", "        final_base.km_slab,\n", "        final_base.hr_slab,\n", "        base_cost,\n", "        current_vendor_rank,\n", "        extra_cost_per_hr as extra_rate_per_hr,\n", "        extra_cost_per_km as extra_rate_per_km,\n", "        \n", "        extra_hours as extra_hrs_booked,\n", "        extra_kms as extra_kms_booked,\n", "        \n", "        --num_of_consign,\n", "        --same_day_trips as trip_count_per_vehicle,\n", "        per_day_cost,\n", "        \n", "        coalesce(actual_kms,wh_to_ds_distance) as trip_distance,\n", "        total_distance_covered as total_distance_covered_by_truck_in_day,\n", "        cost_per_trip_per_km,\n", "        \n", "        fixed_cost,\n", "        tolls_cost,\n", "        misc_charges,\n", "        extra_km_cost as extra_kms_cost_booked,\n", "        extra_hr_cost as extra_hrs_cost_booked,\n", "        extra_driver_cost_for_trip as extra_driver_cost_booked,\n", "        total_trip_cost,\n", "        status,\n", "        total_con_cost,\n", "        total_trip_time,\n", "        -- sum(total_trip_time) over (partition by truck_number,trip_creation_date)\n", "        transit_time,\n", "        total_trip_time-transit_time as idle_time\n", "\n", "from final_base\n", "left join ctype on ctype.consignment_id = final_base.consignment_id\n", "join retail.console_outlet co on co.id = final_base.outlet_id and co.business_type_id in (7)\n", "\n", "where trip_creation_date >= cast(current_date - interval '45' day as date)\n", "  and trip_type = 'Non-Milk Run'\n", "  and trip_state <> 'CANCELLED'\n", "order by trip_creation_date desc, trip_id \n", "\n", "\n", "\"\"\"\n", "\n", "util_df = read_sql_query(util_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "3de9cba1-e4d5-47b1-9a9d-adaf70071624", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c6d42f9-5692-4617-a0e2-400fabacb26a", "metadata": {}, "outputs": [], "source": ["util_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c7680463-c23c-47c8-85cd-101d783b1ff2", "metadata": {}, "outputs": [], "source": ["util_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "927d1660-25ca-455c-8571-385bf6739cf9", "metadata": {}, "outputs": [], "source": ["# util_df[util_df['trip_id']=='1000_528753']"]}, {"cell_type": "code", "execution_count": null, "id": "a9012127-11cc-4b87-9f0c-fbf9ffae4604", "metadata": {}, "outputs": [], "source": ["# util_df.groupby(['wh_name'], as_index=False)['dispatched_qty'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "ed149764-5f93-40ad-9769-04a782cdfedc", "metadata": {}, "outputs": [], "source": ["util_df.consignment_id.count()"]}, {"cell_type": "code", "execution_count": null, "id": "cc16e9ad-cd1e-4a17-bac8-6a1357435057", "metadata": {}, "outputs": [], "source": ["vendor_l1 = f\"\"\"\n", "with vendor_rate_card as \n", "    (\n", "        with base_charge as ( \n", "            with km_base_charge as (\n", "                select distinct \n", "                    vendor_rate_card_config_id,\n", "                    vendor_id,\n", "                    source_node_id,\n", "                    external_name as source_name,\n", "                    destination_city,\n", "                    hs.name as vendor_name,\n", "                    truck_type,\n", "                    truck_billing_type,\n", "                    (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                    (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                    p.value as km_slab,\n", "                    p.rule_id,\n", "                    output as base_cost\n", "                    \n", "                from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "                left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                \n", "                where vrc.is_active and vrcm.is_active\n", "                and attribute_name = 'km'\n", "                and rule.rule_name = 'BASE_PAY'\n", "            ),\n", "            \n", "            hr_base_charge as \n", "            (\n", "                select distinct \n", "                    vendor_rate_card_config_id,\n", "                    vendor_id,\n", "                    source_node_id,\n", "                    external_name as source_name,\n", "                    destination_city,\n", "                    hs.name as vendor_name,\n", "                    truck_type,\n", "                    truck_billing_type,\n", "                    (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                    (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                    p.value as hr_slab,\n", "                    p.rule_id,\n", "                    output as base_cost\n", "                    \n", "                from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                join fleet_management.fleet_management_predicate p on p.rule_id = rule.id\n", "                left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                \n", "                where vrc.is_active and vrcm.is_active\n", "                and attribute_name = 'hour'\n", "                and rule.rule_name = 'BASE_PAY'\n", "                \n", "            )\n", "            \n", "            select distinct kb.*, hb.hr_slab\n", "            from km_base_charge as kb\n", "            join hr_base_charge as hb\n", "            on kb.vendor_rate_card_config_id = hb.vendor_rate_card_config_id and kb.base_cost = hb.base_cost and kb.rule_id = hb.rule_id\n", "        ),\n", "    \n", "        extra_charge as \n", "        (\n", "            with km_extra_charge as \n", "                (\n", "                    select \n", "                        distinct vendor_rate_card_config_id,\n", "                        vendor_id,\n", "                        source_node_id,\n", "                        external_name as source_name,\n", "                        destination_city,\n", "                        hs.name as vendor_name,\n", "                        truck_type,\n", "                        truck_billing_type,\n", "                        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                        output as extra_cost_per_km\n", "                        \n", "                    from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                    join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                    join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                    left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                    left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                    \n", "                    where vrc.is_active and vrcm.is_active\n", "                    and rule.rule_name = 'ADDITIONAL_COST_PER_KM'\n", "                ),\n", "            \n", "            hr_extra_charge as \n", "                (\n", "                    select \n", "                        distinct vendor_rate_card_config_id,\n", "                        vendor_id,\n", "                        source_node_id,\n", "                        external_name as source_name,\n", "                        destination_city,\n", "                        hs.name as vendor_name,\n", "                        truck_type,\n", "                        truck_billing_type,\n", "                        (CAST(regexp_extract(tenure, '^\\[\"(.*?[^\"])\"', 1) AS timestamp) + interval '330' MINUTE) as tenure_start,\n", "                        (CAST(regexp_extract(tenure, ',\"(.*?[^\"])\"[\\)]$', 1) AS timestamp) + interval '330' MINUTE) as tenure_end,\n", "                        output as extra_cost_per_hr\n", "                        \n", "                    from fleet_management.fleet_management_vendor_rate_card_config vrc\n", "                    join fleet_management.fleet_management_vendor_rate_card_config_rule_mapping vrcm on vrc.id = vrcm.vendor_rate_card_config_id\n", "                    join fleet_management.fleet_management_rule rule on rule.id = vrcm.rule_id\n", "                    left join lake_transit_server.transit_node as tn on vrc.source_node_id = cast(tn.external_id as int)\n", "                    left join fleet_management.fleet_management_vendor as hs on hs.id = vrc.vendor_id\n", "                    \n", "                    where vrc.is_active and vrcm.is_active\n", "                    and rule.rule_name = 'ADDITIONAL_COST_PER_HR'\n", "                )\n", "            \n", "            select distinct ke.*, he.extra_cost_per_hr\n", "            from km_extra_charge as ke\n", "            join hr_extra_charge as he\n", "            on ke.vendor_rate_card_config_id = he.vendor_rate_card_config_id \n", "        )\n", "    \n", "        select distinct \n", "            bc.source_node_id,\n", "            bc.source_name,\n", "            case when bc.destination_city in ('Goa','SouthGoa','NorthGoa') then 'Goa' else bc.destination_city end as destination_city,\n", "            bc.vendor_id,\n", "            bc.vendor_name,\n", "            bc.truck_type,\n", "            bc.truck_billing_type,\n", "            cast(bc.base_cost as DOUBLE) as base_cost,\n", "            bc.tenure_start,\n", "            bc.tenure_end,\n", "            cast(bc.km_slab as int) as km_slab,\n", "            cast(bc.hr_slab as int) as hr_slab,\n", "            ec.extra_cost_per_hr,\n", "            ec.extra_cost_per_km\n", "            \n", "        from base_charge as bc\n", "        left join extra_charge as ec on bc.vendor_rate_card_config_id = ec.vendor_rate_card_config_id\n", "    ),\n", "    \n", "rate_card_base as \n", "    (\n", "    select  *,\n", "            case when rnk = 1 then 'L1'\n", "                 when rnk = 2 then 'L2'\n", "                 when rnk = 3 then 'L3'\n", "                 when rnk = 4 then 'L4'\n", "                 when rnk = 5 then 'L5'\n", "                 when rnk = 6 then 'L6'\n", "                 when rnk = 7 then 'L7'\n", "                 when rnk = 8 then 'L8'\n", "                 when rnk = 9 then 'L9'\n", "                 when rnk = 10 then 'L10'\n", "                 when rnk = 11 then 'L11'\n", "                 when rnk = 12 then 'L12'\n", "                 when rnk = 13 then 'L13'\n", "                 when rnk = 14 then 'L14'\n", "                 when rnk = 15 then 'L15'\n", "            end as card_vendor_rank\n", "    from \n", "        (\n", "        select  *,\n", "                row_number() over (partition by destination_city,source_name,truck_type,km_slab,hr_slab order by base_cost) as rnk\n", "        from vendor_rate_card\n", "        where  date(tenure_start) <= cast(current_date - interval '45' day as date)\n", "           and date(tenure_end) >= cast(current_date - interval '45' day as date)\n", "        --where tenure_start <= cast((current_date + interval '45' day) as timestamp)\n", "        and vendor_id not in (1097,1009,1018,988,1099,943,997,999,991,1027,941,1062,1029,1023,1101,1142,1017,1169,995)\n", "        )\n", "    )\n", "    \n", "select  *\n", "from rate_card_base\n", "where card_vendor_rank = 'L1'\n", "\"\"\"\n", "\n", "vendor_l1 = read_sql_query(vendor_l1, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "c8187709-3d80-4da7-a789-fb874ee9d82b", "metadata": {}, "outputs": [], "source": ["vendor_l1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c29f2e79-0a5f-4dde-900b-069927f86bc9", "metadata": {}, "outputs": [], "source": ["vendor_l1.tail()\n", "\n", "# vendor_l1[(vendor_l1['source_name']=='Bengaluru B3 - Feeder Warehouse')&(vendor_l1['km_slab']==3000) &\n", "#           (vendor_l1['destination_city']=='Bengaluru')&(vendor_l1['truck_type']=='Tata 407')&(vendor_l1['hr_slab']==24)]"]}, {"cell_type": "code", "execution_count": null, "id": "8f4ba65a-2543-4f44-98d3-95961b068569", "metadata": {}, "outputs": [], "source": ["vendor_l1.destination_city.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "c348135a-d52a-4770-b17d-f68cdb5219ad", "metadata": {}, "outputs": [], "source": ["# vendor_l1[vendor_l1['destination_city']=='jaipur']"]}, {"cell_type": "code", "execution_count": null, "id": "abe8d536-a76f-4a0c-a14f-8a5ad38d7309", "metadata": {}, "outputs": [], "source": ["util_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5ed32371-896b-4380-8926-8cc92cb22a09", "metadata": {}, "outputs": [], "source": ["# util_df[util_df['destination_city']=='jaipur']\n", "util_df.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "be4abc2b-d231-4aa6-ac10-ed7ea8142e58", "metadata": {}, "outputs": [], "source": ["util_df[\"destination_city\"] = np.where(\n", "    util_df[\"destination_city\"] == \"jaipur\", \"Jaipur\", util_df[\"destination_city\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7df7e011-beb0-46e8-8480-de49ec3b9180", "metadata": {}, "outputs": [], "source": ["# Getting the L1 Vendor Rates for all trips\n", "\n", "vendor_data = sqldf(\n", "    \"\"\"\n", "select  vb.*,\n", "        coalesce(vr.card_vendor_rank,vb.current_vendor_rank) as l1_vendor,\n", "        coalesce(vr.base_cost,vb.base_cost) as l1_base_cost,\n", "        coalesce(vr.extra_cost_per_hr,vb.extra_rate_per_hr) as l1_extra_rate_per_hr,\n", "        coalesce(vr.extra_cost_per_km,vb.extra_rate_per_km) as l1_extra_rate_per_km\n", "from util_df vb\n", "left join vendor_l1 vr on vb.wh_name = vr.source_name --vb.source_node_id = cast(vr.source_node_id as varchar)\n", "        and cast(vb.destination_city as varchar) = cast(vr.destination_city as varchar)\n", "        and vb.truck_type = vr.truck_type \n", "        and (vb.trip_creation_time >= vr.tenure_start and vb.trip_creation_time < vr.tenure_end)\n", "        and vb.km_slab = vr.km_slab and vb.hr_slab = vr.hr_slab \n", "        and vb.truck_billing_type = vr.truck_billing_type\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4c640a5c-6e0c-4b67-a4bb-a29eaa5ab45d", "metadata": {}, "outputs": [], "source": ["vendor_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b8b8a0b6-a103-4d3c-90ff-b1191d19a908", "metadata": {}, "outputs": [], "source": ["# vendor_data[(vendor_data['wh_name']=='Bengaluru B3 - Feeder Warehouse')&(vendor_data['km_slab']==3000)&\n", "#           (vendor_data['destination_city']=='Bengaluru')&(vendor_data['truck_type']=='Tata 407')&(vendor_data['hr_slab']==24)\n", "#            &(vendor_data['outlet_id']==2141)]"]}, {"cell_type": "code", "execution_count": null, "id": "7d2449ff-01ae-47d3-a193-c8557504bc69", "metadata": {}, "outputs": [], "source": ["# util_df[(util_df['wh_name']=='Bengaluru B3 - Feeder Warehouse')&(util_df['km_slab']==3000)&\n", "#           (util_df['destination_city']=='Bengaluru')&(util_df['truck_type']=='Tata 407')&(util_df['hr_slab']==24)]"]}, {"cell_type": "code", "execution_count": null, "id": "feb6edb7-298d-409e-a389-ba4ae3d1f9bd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "30ba57ad-5f61-4af7-b970-4996126ee81f", "metadata": {}, "outputs": [], "source": ["vendor_data.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "d36ac494-8e4c-4632-a2ed-f05182ea823e", "metadata": {}, "outputs": [], "source": ["vendor_data[vendor_data[\"trip_id\"] == \"13965_1168996\"]"]}, {"cell_type": "code", "execution_count": null, "id": "90e4ec6c-89ee-49f3-969d-8e4699cda870", "metadata": {}, "outputs": [], "source": ["cost_data = sqldf(\n", "    \"\"\"\n", "    \n", "with daily_cost as\n", "    (\n", "        select  *,\n", "                case \n", "                    when truck_billing_type = 'FIXED' then cast(l1_base_cost as DOUBLE)/31\n", "                    else cast(l1_base_cost as DOUBLE)\n", "                end as l1_per_day_cost,\n", "                cast(l1_extra_rate_per_hr as DOUBLE)*extra_hrs_booked as l1_extra_hr_cost,\n", "                cast(l1_extra_rate_per_km as DOUBLE)*extra_kms_booked as l1_extra_km_cost,\n", "                count(consignment_id) over (partition by trip_id) as num_of_consign\n", "\n", "        from vendor_data\n", "    ),\n", "    \n", "same_day_trip_count_base as \n", "    (\n", "        SELECT  \n", "            trip_creation_date,\n", "            truck_number,\n", "            count(distinct trip_id) as same_day_trips\n", "        from vendor_data\n", "        group by 1,2   \n", "    )\n", "    \n", "select  *,\n", "        coalesce(((trip_distance/total_distance_covered_by_truck_in_day)*l1_per_day_cost),total_trip_cost) as l1_cost_per_trip,\n", "        l1_per_day_cost/total_distance_covered_by_truck_in_day as l1_cost_per_trip_per_km,\n", "        l1_fixed_cost,\n", "        l1_misc_cost,\n", "        coalesce((l1_fixed_cost + l1_misc_cost),total_trip_cost) as l1_total_trip_cost,\n", "        coalesce((l1_fixed_cost + l1_misc_cost),total_con_cost)/count(*) over (partition by trip_id) as l1_total_con_cost\n", "        --dispatched_qty_weight/(0.9*truck_capacity) as weight_util\n", "from\n", "    (\n", "    select  dc.*,\n", "            l1_per_day_cost/(same_day_trips * num_of_consign) as l1_fixed_cost,\n", "            (coalesce(l1_extra_hr_cost,0) + coalesce(l1_extra_km_cost,0) + coalesce(tolls_cost,0)) as l1_misc_cost\n", "    from daily_cost dc\n", "    left join same_day_trip_count_base as sd on dc.trip_creation_date = sd.trip_creation_date and dc.truck_number = sd.truck_number\n", "    )\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fc9843df-78ec-4297-ade0-73cc4bd9c215", "metadata": {}, "outputs": [], "source": ["cost_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7cd46651-5107-40b1-acf9-aae1b15dc710", "metadata": {}, "outputs": [], "source": ["cost_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f4b29b32-d8e4-4165-885a-c49a2f9c36ef", "metadata": {}, "outputs": [], "source": ["# cost_data.trip_creation_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "76510ab7-ddfa-438f-9345-691d3cf57e0c", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now()\n", "last_day = current_date - <PERSON><PERSON><PERSON>(days=2)\n", "last_day"]}, {"cell_type": "code", "execution_count": null, "id": "90bd3e0a-69b1-4595-82a2-62b9ee742613", "metadata": {}, "outputs": [], "source": ["cost_data[\"trip_creation_date\"] = pd.to_datetime(cost_data[\"trip_creation_date\"])\n", "\n", "cost_data1 = cost_data[cost_data[\"trip_creation_date\"] < last_day]"]}, {"cell_type": "code", "execution_count": null, "id": "ebabc609-c395-4b77-9990-452ea67aa1c1", "metadata": {}, "outputs": [], "source": ["cost_data1.trip_creation_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "742c79d2-aed8-41d6-95f3-bd64416a8976", "metadata": {}, "outputs": [], "source": ["cost_data1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0482b2df-1ad6-4f9c-a96f-d56083e95595", "metadata": {}, "outputs": [], "source": ["cost_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c2eb64a1-b8b2-4731-84f2-6165c67f1759", "metadata": {}, "outputs": [], "source": ["cost_data1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "34658f09-89e8-4e25-a421-e59b058c0c77", "metadata": {}, "outputs": [], "source": ["# cost_data1['trip_creation_date']=pd.to_datetime(cost_data1['trip_creation_date']).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "a3b1fca4-01c4-4ed0-a4db-355f82055f5e", "metadata": {}, "outputs": [], "source": ["cost_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "55def088-0685-4879-8662-8254879d96ab", "metadata": {}, "outputs": [], "source": ["# cost_data_df1 = sqldf (\"\"\"\n", "# with dist_travelled_daily as(\n", "# select trip_creation_date,\n", "#         truck_number,\n", "#         sum(distance_travelled) as total_dist_travelled\n", "# from cost_data1\n", "\n", "# )\n", "# select c1.*, total_dist_travelled\n", "#        from cost_data1 c1\n", "#        left join dist_travelled_daily d on d.trip_creation_date=c1.trip_creation_date and d.truck_number=c1.truck_number\n", "\n", "# \"\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "5930b05d-8e63-4728-8620-5e5a6e8f4531", "metadata": {}, "outputs": [], "source": ["# cost_data_df = sqldf (\"\"\"\n", "\n", "# select *,\n", "#         sum(distance_travelled) over (partition by truck_number,trip_creation_date) as total_dis_travelled\n", "# from cost_data1\n", "\n", "\n", "# \"\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "cca69425-df90-4ff7-9319-3955a6650d38", "metadata": {}, "outputs": [], "source": ["# cost_data_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c42a2bbe-163d-4168-9466-05a102307439", "metadata": {}, "outputs": [], "source": ["# cost_data_df[cost_data_df['truck_number']=='TS08UL0371']"]}, {"cell_type": "code", "execution_count": null, "id": "71206f49-a1ac-4bef-9330-61ffb94f3e02", "metadata": {}, "outputs": [], "source": ["cost_data_df = cost_data1.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "30a5263a-6946-421c-852d-9be9fc5f5809", "metadata": {}, "outputs": [], "source": ["# cost_data1.to_csv('cost_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "562ddf32-5129-41b7-a4b4-ceb13a37698f", "metadata": {}, "outputs": [], "source": ["cost_data_df[\"trip_creation_date\"] = pd.to_datetime(cost_data_df[\"trip_creation_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "0a622af2-9368-44a2-ab74-d032d0c8e4d1", "metadata": {}, "outputs": [], "source": ["def days_in_month(year, month):\n", "    return calendar.monthrange(year, month)[1]"]}, {"cell_type": "code", "execution_count": null, "id": "66cf795b-b987-4bc6-9c8a-8701bd040034", "metadata": {}, "outputs": [], "source": ["def divide_by_days(row):\n", "    year = row[\"trip_creation_date\"].year\n", "    month = row[\"trip_creation_date\"].month\n", "    days = days_in_month(year, month)\n", "    return row[\"km_slab\"] / days\n", "\n", "\n", "# Apply the function to each row in the dataframe\n", "cost_data_df[\"km_per_day\"] = cost_data_df.apply(divide_by_days, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "fb2d7755-d31a-4369-928f-9a6580f5ff4d", "metadata": {}, "outputs": [], "source": ["cost_data_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4dd92290-ac01-44a9-9317-0503bd394c84", "metadata": {}, "outputs": [], "source": ["cost_data_df[cost_data_df[\"destination_city\"] == \"Hyderabad\"].wh_name.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "2d8980aa-0011-4671-85a4-12ee5b24ab41", "metadata": {}, "outputs": [], "source": ["cost_data_df1 = sqldf(\n", "    \"\"\"\n", "  \n", "select  *,\n", "        case when destination_city in ('Agra','Ajmer','Amritsar','Bareilly','Bathinda','Bhopal','Dehradun','Durgapur','Gwalior','Haridwar','Jalandhar','Jamshedpur','Jodhpur','Kochi',\n", "        '<PERSON>','Mysore','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','Raj<PERSON>','Roorkee','Udaipur','Varanasi','Vijayawada','Visakhapatnam') then \"Satellite\"\n", "             when destination_city = 'Hyderabad' and wh_name not in ('Super Store Hyderabad H2 - Warehouse') then \"InterCity\"\n", "             when destination_city = 'Chennai' and wh_name not in ('Chennai C4 - Feeder') then \"InterCity\"\n", "             when destination_city = 'Mumbai' and wh_name not in ('Mumbai M10 - Feeder','Mumbai M9 Ecom - Feeder') then \"InterCity\"\n", "             else \"SameCity\"\n", "        end as city_groups\n", "from cost_data_df\n", "\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "532ff55c-94ac-4b69-a3ef-5e11c0636125", "metadata": {}, "outputs": [], "source": ["cost_data_df1.destination_name.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "02c037d0-75a2-4f4d-9d26-7fd0c8722211", "metadata": {}, "outputs": [], "source": ["# cost_data_df1[(cost_data_df1['facility_id']==2141) & (cost_data_df1['city_groups']=='SameCity')]"]}, {"cell_type": "code", "execution_count": null, "id": "e00c515d-f0ac-4e36-a4d1-b4345fe09040", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "18afb20b-0ac9-411f-825e-2fba886c133e", "metadata": {}, "outputs": [], "source": ["cost_data_df1.city_groups.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "94ad8fd5-b0a5-4072-b494-1a2adf21d956", "metadata": {}, "outputs": [], "source": ["cost_data_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "34283482-bfb8-4e80-a005-fe83d78d03eb", "metadata": {}, "outputs": [], "source": ["cost_data_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3e178cf3-8d21-4a70-94f6-5daec0d2b01f", "metadata": {}, "outputs": [], "source": ["cost_data_df1.columns"]}, {"cell_type": "code", "execution_count": null, "id": "79e7a729-0ffa-4a63-b515-3c7e674b61f4", "metadata": {}, "outputs": [], "source": ["# cost_data_df1.drop(columns=['expected_containers','dispatched_containers','extra_rate_per_hr','extra_rate_per_km', 'extra_hrs_booked', 'extra_hrs_cost_booked'], inplace=True)\n", "cost_data_df1.drop(columns=[\"num_of_consign\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3aabd88a-0d57-4a19-9d49-042647b400cb", "metadata": {}, "outputs": [], "source": ["cost_data_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "40477462-ba0d-45d0-9228-42f57dcd09a2", "metadata": {}, "outputs": [], "source": ["cost_data_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8450788c-12ce-4a52-8c72-ec63b38fc974", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2777ccb4-6eef-4a82-904d-05c61812d266", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "aee3df7a-b275-48b9-9f3b-344efb907e00", "metadata": {}, "outputs": [], "source": ["truck_age = f\"\"\"\n", "\n", "with last_trip as\n", "    (\n", "    select  truck_number,\n", "            max(case when num = 1 then trip_ts end) as last_trip_ts,\n", "            max(case when num1 = 1 then trip_ts end) as first_trip_ts\n", "    from\n", "        (\n", "        select  truck_number,\n", "                (install_ts+interval '330' minute) as trip_ts,\n", "                row_number() over (partition by truck_number order by install_ts desc) as num,\n", "                row_number() over (partition by truck_number order by install_ts asc) as num1\n", "        from transit_server.transit_projection_trip \n", "        where insert_ds_ist >= cast((CURRENT_DATE - interval '720' DAY) as varchar)\n", "        and state not in ('EXPIRED','CANCELLED')\n", "        -- and truck_number = 'DL1LY3538'\n", "        )\n", "    where num = 1 or num1 = 1\n", "    group by 1\n", "    )\n", "\n", "select  \n", "        -- (veh.install_ts + interval '330' minute) as onboarding_time,\n", "        registration_number as truck_number,\n", "        vehicle_type,\n", "        ven2.name as vendor_name,\n", "        ven1.name as gps_provider_name,\n", "        status,\n", "        first_trip_ts,\n", "        last_trip_ts,\n", "        cast(json_extract(veh.meta,'$.external_vehicle_details.result.rcExpiryDate') as varchar) AS rcExpiryDate,\n", "        cast(json_extract(veh.meta,'$.external_vehicle_details.result.permitValidUpto') as varchar) AS permitValidUpto,\n", "        cast(json_extract(veh.meta,'$.external_vehicle_details.result.vehicleInsuranceUpto') as varchar) AS vehicleInsuranceUpto,\n", "        cast(json_extract(veh.meta,'$.external_vehicle_details.result.vehicleManufacturingMonthYear') as varchar) AS vehicleManufacturingMonthYear\n", "from fleet_management.fleet_management_vehicle veh\n", "left join fleet_management.fleet_management_vendor ven1 on ven1.id = veh.gps_provider_id\n", "left join fleet_management.fleet_management_vendor ven2 on ven2.id = veh.vendor_id\n", "left join last_trip lt on lt.truck_number = veh.registration_number\n", "where status = 'ACTIVE'\n", "\n", "\n", "\"\"\"\n", "\n", "truck_age_df = read_sql_query(truck_age, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "bd562066-4df7-4fb6-a39e-6e6af1c1a122", "metadata": {}, "outputs": [], "source": ["truck_age_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d409d7cc-9c03-4fab-a46d-ccc69cf2b908", "metadata": {}, "outputs": [], "source": ["truck_age_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b73fb729-c607-4e54-bb4a-65358d11f3d1", "metadata": {}, "outputs": [], "source": ["truck_age_df1 = truck_age_df[\n", "    [\n", "        \"truck_number\",\n", "        \"first_trip_ts\",\n", "        \"last_trip_ts\",\n", "        \"rcExpiryDate\",\n", "        \"vehicleManufacturingMonthYear\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b5332f1a-6410-456e-9d35-1e04dcf6aca9", "metadata": {}, "outputs": [], "source": ["cost_data_df2 = cost_data_df1.merge(truck_age_df1, on=\"truck_number\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "f26b59fc-e5e7-465f-aa01-f670c170aad4", "metadata": {}, "outputs": [], "source": ["cost_data_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dcf8e643-5b2f-4070-9969-592b2fcf37d0", "metadata": {}, "outputs": [], "source": ["cost_data_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "200d1d69-0911-4d20-a1eb-95dcfce4d2be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "191c6acc-de11-42d4-ae22-8854592217a6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "46fa1786-3e04-4203-be80-b081b4c75cb1", "metadata": {}, "outputs": [], "source": ["# Function to clean and handle invalid dates like '00/2011'\n", "def clean_invalid_dates(date_str):\n", "    # Ensure that we only process strings, not datetime objects (NaT, Timestamps, etc.)\n", "    if isinstance(date_str, str):\n", "        # Check if the date string contains an invalid month (e.g., '00')\n", "        try:\n", "            month, year = date_str.split(\"/\")\n", "            if int(month) == 0:  # Invalid month\n", "                return None  # Return None for invalid dates\n", "            return date_str  # Return the original date string if valid\n", "        except ValueError:\n", "            return None  # Return None if there's any error in splitting (e.g., malformed format)\n", "    return None  # If the value is not a string, return None\n", "\n", "\n", "# Clean date1 column to handle invalid '00/2011' and other invalid formats\n", "cost_data_df2[\"vehicleManufacturingMonthYear\"] = cost_data_df2[\n", "    \"vehicleManufacturingMonthYear\"\n", "].apply(clean_invalid_dates)\n", "\n", "\n", "# Convert 'date1' to datetime (using the format for month/year)\n", "cost_data_df2[\"vehicleManufacturingMonthYear\"] = pd.to_datetime(\n", "    cost_data_df2[\"vehicleManufacturingMonthYear\"], format=\"%m/%Y\", errors=\"coerce\"\n", ")\n", "# Convert 'date2' to datetime, coerce errors to NaT (if invalid or None)\n", "cost_data_df2[\"trip_creation_date\"] = pd.to_datetime(\n", "    cost_data_df2[\"trip_creation_date\"], format=\"%Y-%m-%d\", errors=\"coerce\"\n", ")\n", "\n", "\n", "def calculate_month_diff(row):\n", "\n", "    # Safe check for NaT or missing values\n", "    if pd.isna(row.get(\"vehicleManufacturingMonthYear\", None)) or pd.isna(\n", "        row.get(\"trip_creation_date\", None)\n", "    ):\n", "        return None  # Return None if any date is missing\n", "    else:\n", "        # Calculate the difference in months using relativedelta\n", "        diff = relativedelta(row[\"trip_creation_date\"], row[\"vehicleManufacturingMonthYear\"])\n", "        return diff.years * 12 + diff.months\n", "\n", "\n", "# Apply the function to calculate month difference\n", "cost_data_df2[\"vehicle_age_in_month\"] = cost_data_df2.apply(calculate_month_diff, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "c7f1a6fb-91a0-4fd5-b0d3-7d9430ff83d1", "metadata": {}, "outputs": [], "source": ["cost_data_df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a4e38350-c410-4c84-8906-cc3c537cb138", "metadata": {}, "outputs": [], "source": ["cost_data_df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d8488fb8-7c84-4473-8e72-61aeba83256f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f200daeb-4037-4583-b0d9-6771eacc77ae", "metadata": {}, "outputs": [], "source": ["store_age_query = \"\"\"select cast(outlet_id as int) as outlet_id, cast(min(order_create_dt_ist) as date) as min_date\n", "from dwh.fact_sales_order_details\n", "where order_create_dt_ist >= (current_date - interval '1' year)\n", "group by 1\n", "\"\"\"\n", "store_age_df = read_sql_query(store_age_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "91a677a7-1126-4d86-b791-95833a6c90e9", "metadata": {}, "outputs": [], "source": ["store_age_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "182fcfc2-6f94-4e3e-a782-9cc59f535360", "metadata": {}, "outputs": [], "source": ["store_age_df[store_age_df[\"outlet_id\"] == 4936]"]}, {"cell_type": "code", "execution_count": null, "id": "e6b94d48-c06d-40cc-8c46-4471a412b0dd", "metadata": {}, "outputs": [], "source": ["cost_data_df3 = cost_data_df2.merge(store_age_df, how=\"left\", on=\"outlet_id\")"]}, {"cell_type": "code", "execution_count": null, "id": "2d2092c0-823a-49af-b347-55b0fe27c5b8", "metadata": {}, "outputs": [], "source": ["cost_data_df3[\"min_date\"] = pd.to_datetime(cost_data_df3[\"min_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "2df8a2e4-0c8a-41f0-9ff1-fcbd74c9f800", "metadata": {}, "outputs": [], "source": ["cost_data_df3[\"trip_creation_date\"] = pd.to_datetime(cost_data_df3[\"trip_creation_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "e01acf54-9ee9-4bd2-9ed5-3b490f21b0bf", "metadata": {}, "outputs": [], "source": ["cost_data_df3[\"outlet_age\"] = (\n", "    cost_data_df3[\"trip_creation_date\"] - cost_data_df3[\"min_date\"]\n", ").dt.days"]}, {"cell_type": "code", "execution_count": null, "id": "19744437-833c-42cf-8a19-67e738d78ed0", "metadata": {}, "outputs": [], "source": ["cost_data_df3.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8e4cd647-292d-41de-9ddd-fa6664161a14", "metadata": {}, "outputs": [], "source": ["carts_on_store = \"\"\"\n", "with\n", "outlet_details as\n", "    (select * from supply_etls.outlet_details),\n", "\n", "item_details as\n", "    (select * from supply_etls.item_details),\n", "\n", "raw_data as\n", "    (select * from supply_etls.date_hourly_sales_details),\n", "\n", "final_raw_data as\n", "    (\n", "    select * from raw_data\n", "    where\n", "    insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "    ),\n", "\n", "adding_item_details as\n", "    (\n", "    select\n", "        date(frd.insert_ds_ist) as date_,\n", "        cart_id,\n", "        frd.outlet_id,\n", "        frd.item_id,\n", "        id.assortment_type,\n", "        frd.sales_quantity,\n", "        frd.sales_value,\n", "        frd.sales_weighted_landing_price,\n", "        frd.sales_mrp\n", "\n", "    from final_raw_data frd\n", "    join\n", "    item_details id on id.item_id = frd.item_id\n", "    ),\n", "\n", "cart_details as\n", "    (\n", "    select\n", "        date_,\n", "        outlet_id,\n", "        count(distinct cart_id) as carts,\n", "        sum(sales_quantity) as sales_quantity,\n", "        sum(sales_value) as sales_value\n", "\n", "    from adding_item_details\n", "    group by 1,2\n", "\n", "    )\n", "\n", "    select\n", "        cd.date_,\n", "        city_name,\n", "        cd.outlet_id,\n", "        cd.carts,\n", "        cd.sales_quantity,\n", "        -- cd.sales_value,\n", "        sum(case when assortment_type in ('Packaged Goods') then aid.sales_quantity end) as grocery_qty\n", "        from adding_item_details aid\n", "        join\n", "        cart_details cd on cd.date_ = aid.date_ and cd.outlet_id = aid.outlet_id\n", "        join outlet_details od on od.hot_outlet_id = cd.outlet_id\n", "        -- join \n", "    \n", "        group by 1,2,3,4,5\n", "\n", "\"\"\"\n", "carts_on_store_df = read_sql_query(carts_on_store, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "8ce2105b-8b10-4e99-83a0-c876e0029444", "metadata": {}, "outputs": [], "source": ["carts_on_store_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a7e7efbd-1b5d-45c2-bb40-4ea32118084b", "metadata": {}, "outputs": [], "source": ["carts_on_store_df[carts_on_store_df[\"outlet_id\"] == 4500].sort_values(by=\"date_\")"]}, {"cell_type": "code", "execution_count": null, "id": "c2ce220e-1419-4631-ad0b-925b098b4637", "metadata": {}, "outputs": [], "source": ["# carts_on_store_df[carts_on_store_df['outlet_id']==4936]"]}, {"cell_type": "code", "execution_count": null, "id": "15d64d53-0c3e-4af6-a851-87aeefe05dd9", "metadata": {}, "outputs": [], "source": ["# weekly_carts_df = sqldf(\n", "# '''\n", "# with cte as(\n", "# select strftime('%W', date_) as week_num,\n", "#         outlet_id,\n", "#         SUM(carts) as carts,\n", "#         sum(sales_quantity) as sales_quantity,\n", "#         sum(grocery_qty) as grocery_qty\n", "# from carts_on_store_df\n", "# group by 1,2\n", "# )\n", "\n", "# select *, lag(carts) over(partition by week_num order by week_num) as last_week_carts,\n", "#             lag(sales_quantity) over(partition by week_num order by week_num) as last_week_sales_quantity,\n", "#             lag(grocery_qty) over(partition by week_num order by week_num) as last_week_grocery_qty\n", "# from cte\n", "\n", "\n", "# '''\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "cdfb8a82-7dd1-43a7-b998-5cd12c3e9872", "metadata": {}, "outputs": [], "source": ["# weekly_carts_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7007fce1-c56d-42ca-8a9a-1d9c1d5fd436", "metadata": {}, "outputs": [], "source": ["carts_on_store_df1 = sqldf(\n", "    \"\"\"\n", "SELECT \n", "    df.date_,\n", "    df.city_name,\n", "    df.outlet_id,\n", "    df.carts,\n", "    df.sales_quantity,\n", "    df.grocery_qty,\n", "    SUM(CASE \n", "            WHEN df1.date_ >= DATE(df.date_, '-7 days') THEN df1.carts \n", "            ELSE 0 \n", "        END) AS rolling_7_day_carts\n", "FROM \n", "    carts_on_store_df df\n", "LEFT JOIN \n", "    carts_on_store_df df1 \n", "ON \n", "    df1.date_ BETWEEN DATE(df.date_, '-7 days') AND df.date_ \n", "    and df1.city_name = df.city_name and df1.outlet_id=df.outlet_id\n", "GROUP BY \n", "    1,2,3,4,5,6\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "507e5456-f889-4345-a506-eeecb4e786ff", "metadata": {}, "outputs": [], "source": ["carts_on_store_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c6f820c2-d3f0-4b70-80c8-34bde40ca5a2", "metadata": {}, "outputs": [], "source": ["carts_on_store_df1[carts_on_store_df1[\"outlet_id\"] == 4500].sort_values(by=\"date_\")"]}, {"cell_type": "code", "execution_count": null, "id": "a5b60c9f-319d-4b08-80bd-b43b2e072db1", "metadata": {}, "outputs": [], "source": ["# carts_on_store_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "fbbb4f1b-e315-4a4b-9027-c39953330c63", "metadata": {}, "outputs": [], "source": ["carts_on_store_df1[\"date_\"] = pd.to_datetime(carts_on_store_df1[\"date_\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "0e6dfd7b-5ca7-49dc-8c9a-d882b7c90763", "metadata": {}, "outputs": [], "source": ["cost_data_df4 = sqldf(\n", "    \"\"\"\n", "select df.*,carts,sales_quantity,grocery_qty\n", "from\n", "cost_data_df3 df\n", "\n", "left join carts_on_store_df cs on trip_creation_date = date_ and df.outlet_id = cs.outlet_id\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d9b8e4df-86d8-4109-8e4d-e46bfd874d44", "metadata": {}, "outputs": [], "source": ["# carts_on_store_df1[carts_on_store_df1['outlet_id']==4500].sort_values(by='date_')"]}, {"cell_type": "code", "execution_count": null, "id": "468ea35f-eaa6-44ce-91f9-c5caade0095d", "metadata": {}, "outputs": [], "source": ["cost_data_df4 = cost_data_df3.merge(\n", "    carts_on_store_df1,\n", "    how=\"left\",\n", "    left_on=[\"trip_creation_date\", \"outlet_id\"],\n", "    right_on=[\"date_\", \"outlet_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d00c310f-45b0-4345-aec7-ec62e3c95a35", "metadata": {}, "outputs": [], "source": ["cost_data_df4.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9378df3a-97b0-487b-b2bc-94872c3851a4", "metadata": {}, "outputs": [], "source": ["cost_data_df4[\"trip_creation_date\"] = pd.to_datetime(cost_data_df4[\"trip_creation_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "87b657d2-c2db-412c-8e1c-ca6a4e6aade5", "metadata": {}, "outputs": [], "source": ["cost_data_df4.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3daa60cb-0eeb-43f4-9270-e9ec3a0d4ff3", "metadata": {}, "outputs": [], "source": ["# cost_data_df4.drop(columns={\n", "#     'min_date'\n", "# },inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "7d6c2668-8638-4f2e-90c5-b825a763764d", "metadata": {}, "outputs": [], "source": ["cost_data_df4.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b47d629a-3a90-4f13-b2e4-5463aa236ef2", "metadata": {}, "outputs": [], "source": ["# cost_data_df4.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c1091633-ec01-45bd-b537-4c83a74de258", "metadata": {}, "outputs": [], "source": ["ars_query = \"\"\"\n", "with valid_ars_job_runs_extract as\n", "    (\n", "        select  distinct run_id, \n", "                cast(json_extract(simulation_params,'$.run') as varchar) as run_type,\n", "                cast(json_extract(simulation_params,'$.mode') as varchar) as mode \n", "        from ars.job_run \n", "        where run_id not in ('20210607116002', '20210607116006', '20210607116005', '20210607116003')\n", "            and started_at > cast(current_date - interval '50' day as timestamp) \n", "            and completed_at is not null\n", "    ),\n", "\n", "valid_ars_job_runs as \n", "    (\n", "        select  run_id,\n", "                run_type,\n", "                mode\n", "        from valid_ars_job_runs_extract\n", "        where run_type in ('ars_lite')\n", "            and (mode in ('','frozen','milk','fnv','perishable') or mode is null)\n", "            -- and (mode in ('') or mode is null)\n", "            -- removing things like stocking level runs, ordering runs, reverse flow runs\n", "    ),\n", "                \n", "runs AS\n", "    (   \n", "    SELECT run_id\n", "    FROM ars.job_run\n", "    where date(started_at+interval'5'hour+interval'30'minute) >= cast(current_date - interval '45' day as date)\n", "    AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "    AND simulation_params not like '%%mode%%'\n", "    AND simulation_params not like '%%hyperpure_run%%'\n", "    AND simulation_params not like '%%any_day_po%%'    \n", "    AND simulation_params not like '%%spr_migration_simulation%%' \n", "    AND cast(json_extract(simulation_params,'$.manual') AS varchar) is null\n", "    order by 1\n", "    ),\n", "    \n", "ars_indent as \n", "    (\n", "        select  run_id, \n", "                backend_outlet_id, \n", "                frontend_outlet_id,\n", "                sto_date,\n", "                min(extract(hour from indent.created_at+interval'5'hour+interval'30'minute)) as sto_created_hr,\n", "                sum(weight_in_gm*sto_quantity_post_truncation_in_case) as total_weight,\n", "                sum(sto_quantity_post_truncation_in_case) as total_indent,\n", "                avg(item_factor) as avg_item_factor\n", "        from ars.transfers_optimization_results_v2 as indent\n", "        inner join rpc.item_details id ON id.item_id = indent.item_id AND id.active = 1 AND id.approved = 1\n", "        left join supply_etls.item_factor fac on fac.item_id = indent.item_id\n", "        where indent.insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "          and run_id in (select run_id from runs) \n", "        group by 1,2,3,4\n", "    )\n", "\n", "        select  awu.sto_date,\n", "                awu.backend_outlet_id, \n", "                co.facility_id,\n", "                awu.frontend_outlet_id,\n", "                sum(cast(total_weight as DOUBLE)/1000) as total_weight_kg,\n", "                sum(total_indent) as total_indent\n", "        from ars_indent as awu\n", "        inner join valid_ars_job_runs jr on jr.run_id = awu.run_id\n", "        join retail.console_outlet co on co.id=awu.backend_outlet_id and lake_active_record and active=1 \n", "        group by 1,2,3,4\n", "\"\"\"\n", "ars_df = read_sql_query(ars_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "1203f9d8-ec3c-455d-92a4-136754afb870", "metadata": {}, "outputs": [], "source": ["ars_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3855ff99-7a83-44b4-9bab-59825568e473", "metadata": {}, "outputs": [], "source": ["ars_df[\"sto_date\"] = pd.to_datetime(ars_df[\"sto_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "09465b43-470a-4aef-9ec4-0cccd464a72b", "metadata": {}, "outputs": [], "source": ["cost_data_df5 = cost_data_df4.merge(\n", "    ars_df,\n", "    how=\"left\",\n", "    left_on=[\"trip_creation_date\", \"facility_id\", \"outlet_id\"],\n", "    right_on=[\"sto_date\", \"facility_id\", \"frontend_outlet_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2726215b-a79a-423a-9744-b6b68fc949d3", "metadata": {}, "outputs": [], "source": ["cost_data_df5.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9ec7abf6-f772-406c-972f-e88ac828f907", "metadata": {}, "outputs": [], "source": ["cost_data_df5.drop(\n", "    columns={\n", "        \"sto_date\",\n", "        \"backend_outlet_id\",\n", "        \"frontend_outlet_id\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fae326b9-63b9-41d8-8c47-d11baa0e2a10", "metadata": {}, "outputs": [], "source": ["# cost_data_df5.to_csv('cost_data_df5.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "b82899ec-661d-4abf-bed6-cfb45b3eee39", "metadata": {}, "outputs": [], "source": ["cost_data_df5.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c25aa9b7-3d04-4cb1-9b84-ab6e34d3992f", "metadata": {}, "outputs": [], "source": ["cost_data_df5.trip_creation_date.min()"]}, {"cell_type": "code", "execution_count": null, "id": "3e48f87a-4689-4f78-a583-0b9041f55663", "metadata": {}, "outputs": [], "source": ["# cost_data_df5.to_csv('cost_data_df5.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "955d1b17-e981-4500-b3a7-9e43d5f24534", "metadata": {}, "outputs": [], "source": ["### WOW aggregate data"]}, {"cell_type": "code", "execution_count": null, "id": "c1d6a6bd-a4ba-4dc6-9088-0bfcd3992aa7", "metadata": {}, "outputs": [], "source": ["cost_data_df5.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3b0bac89-f548-499a-9313-c40e122b858d", "metadata": {}, "outputs": [], "source": ["cost_data_df5.shape"]}, {"cell_type": "code", "execution_count": null, "id": "72819666-241d-4f70-94dc-6e5e7e5d84c1", "metadata": {}, "outputs": [], "source": ["cost_data_df5.consignment_id.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "06751ac5-1178-43d8-b426-8c5e94ceb1f7", "metadata": {}, "outputs": [], "source": ["cost_data_df5[cost_data_df5[\"consignment_id\"] == 1898818]"]}, {"cell_type": "code", "execution_count": null, "id": "533f82a6-04da-4eb2-926b-33fcbf850903", "metadata": {}, "outputs": [], "source": ["cost_data_df5[cost_data_df5[\"consignment_id\"] == 1895551]"]}, {"cell_type": "code", "execution_count": null, "id": "e12372b6-a1b9-4756-a9be-b3d04cfb1f8e", "metadata": {}, "outputs": [], "source": ["cost_data_df5.destination_name.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "2d6f6b37-393d-4c34-9a4a-538ad265fcc9", "metadata": {}, "outputs": [], "source": ["cost_data_df5.drop(columns={\"date_\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "38295955-acfb-4a0d-9165-e46c23656270", "metadata": {}, "outputs": [], "source": ["cost_data_df5[\"trip_creation_date\"] = pd.to_datetime(\n", "    cost_data_df5[\"trip_creation_date\"], errors=\"coerce\"\n", ")\n", "\n", "# Convert to string format if necessary\n", "cost_data_df5[\"trip_creation_date\"] = cost_data_df5[\"trip_creation_date\"].dt.strftime(\n", "    \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0ca1313e-be64-4f16-b77b-a6c401deed81", "metadata": {}, "outputs": [], "source": ["cost_data_df5.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e5a5ebaf-4d89-4fcd-8bcc-fba0f1c80a08", "metadata": {}, "outputs": [], "source": ["cost_data_df5[\"consignment_id\"] = cost_data_df5[\"consignment_id\"].fillna(0).fillna(0).astype(int)\n", "cost_data_df5[\"trip_creation_date\"] = cost_data_df5[\"trip_creation_date\"].astype(str)\n", "cost_data_df5[\"facility_id\"] = cost_data_df5[\"facility_id\"].fillna(0).astype(int)\n", "cost_data_df5[\"outlet_id\"] = cost_data_df5[\"outlet_id\"].fillna(0).astype(int)\n", "cost_data_df5[\"truck_capacity\"] = cost_data_df5[\"truck_capacity\"].astype(float)\n", "cost_data_df5[\"expected_containers\"] = cost_data_df5[\"expected_containers\"].fillna(0).astype(int)\n", "cost_data_df5[\"dispatched_containers\"] = (\n", "    cost_data_df5[\"dispatched_containers\"].fillna(0).astype(int)\n", ")\n", "cost_data_df5[\"count_cases\"] = cost_data_df5[\"count_cases\"].fillna(0).astype(int)\n", "cost_data_df5[\"count_temp_crates\"] = cost_data_df5[\"count_temp_crates\"].fillna(0).astype(int)\n", "cost_data_df5[\"count_perm_crates\"] = cost_data_df5[\"count_perm_crates\"].fillna(0).astype(int)\n", "cost_data_df5[\"perm_disp_qty\"] = cost_data_df5[\"perm_disp_qty\"].fillna(0).astype(int)\n", "cost_data_df5[\"dispatched_qty\"] = cost_data_df5[\"dispatched_qty\"].fillna(0).astype(int)\n", "cost_data_df5[\"vendor_id\"] = cost_data_df5[\"vendor_id\"].fillna(0).astype(int)\n", "cost_data_df5[\"vendor_id\"] = cost_data_df5[\"vendor_id\"].fillna(0).astype(int)\n", "\n", "cost_data_df5[\"km_slab\"] = cost_data_df5[\"km_slab\"].fillna(0).astype(int)\n", "cost_data_df5[\"hr_slab\"] = cost_data_df5[\"hr_slab\"].fillna(0).astype(int)\n", "cost_data_df5[\"base_cost\"] = cost_data_df5[\"base_cost\"].astype(float)\n", "cost_data_df5[\"extra_rate_per_hr\"] = cost_data_df5[\"extra_rate_per_hr\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"extra_hrs_booked\"] = cost_data_df5[\"extra_hrs_booked\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"per_day_cost\"] = cost_data_df5[\"per_day_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"trip_distance\"] = cost_data_df5[\"trip_distance\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"total_distance_covered_by_truck_in_day\"] = (\n", "    cost_data_df5[\"total_distance_covered_by_truck_in_day\"].fillna(0.0).astype(float)\n", ")\n", "cost_data_df5[\"cost_per_trip_per_km\"] = (\n", "    cost_data_df5[\"cost_per_trip_per_km\"].fillna(0.0).astype(float)\n", ")\n", "cost_data_df5[\"fixed_cost\"] = cost_data_df5[\"fixed_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"tolls_cost\"] = cost_data_df5[\"tolls_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"misc_charges\"] = cost_data_df5[\"misc_charges\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"extra_kms_cost_booked\"] = (\n", "    cost_data_df5[\"extra_kms_cost_booked\"].fillna(0.0).astype(float)\n", ")\n", "cost_data_df5[\"extra_hrs_cost_booked\"] = (\n", "    cost_data_df5[\"extra_hrs_cost_booked\"].fillna(0.0).astype(float)\n", ")\n", "\n", "cost_data_df5[\"extra_driver_cost_booked\"] = (\n", "    cost_data_df5[\"extra_driver_cost_booked\"].fillna(0.0).astype(float)\n", ")\n", "cost_data_df5[\"total_trip_cost\"] = cost_data_df5[\"total_trip_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"total_con_cost\"] = cost_data_df5[\"total_con_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"total_trip_time\"] = cost_data_df5[\"total_trip_time\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"transit_time\"] = cost_data_df5[\"transit_time\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"idle_time\"] = cost_data_df5[\"idle_time\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"l1_base_cost\"] = cost_data_df5[\"l1_base_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"transit_time\"] = cost_data_df5[\"transit_time\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"l1_extra_rate_per_hr\"] = (\n", "    cost_data_df5[\"l1_extra_rate_per_hr\"].fillna(0.0).astype(float)\n", ")\n", "cost_data_df5[\"l1_extra_rate_per_km\"] = (\n", "    cost_data_df5[\"l1_extra_rate_per_km\"].fillna(0.0).astype(float)\n", ")\n", "\n", "\n", "cost_data_df5[\"l1_per_day_cost\"] = cost_data_df5[\"l1_per_day_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"l1_extra_hr_cost\"] = cost_data_df5[\"l1_extra_hr_cost\"].fillna(0.0).astype(float)\n", "\n", "cost_data_df5[\"l1_extra_km_cost\"] = cost_data_df5[\"l1_extra_km_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"l1_fixed_cost\"] = cost_data_df5[\"l1_fixed_cost\"].fillna(0.0).astype(float)\n", "\n", "cost_data_df5[\"l1_misc_cost\"] = cost_data_df5[\"l1_misc_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"l1_cost_per_trip\"] = cost_data_df5[\"l1_cost_per_trip\"].fillna(0.0).astype(float)\n", "\n", "cost_data_df5[\"l1_total_trip_cost\"] = cost_data_df5[\"l1_total_trip_cost\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"l1_total_con_cost\"] = cost_data_df5[\"l1_total_con_cost\"].fillna(0.0).astype(float)\n", "\n", "\n", "cost_data_df5[\"km_per_day\"] = cost_data_df5[\"km_per_day\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"l1_total_con_cost\"] = cost_data_df5[\"l1_total_con_cost\"].fillna(0.0).astype(float)\n", "\n", "\n", "cost_data_df5[\"first_trip_ts\"] = cost_data_df5[\"first_trip_ts\"].astype(str)\n", "cost_data_df5[\"last_trip_ts\"] = cost_data_df5[\"last_trip_ts\"].astype(str)\n", "\n", "# cost_data_df5['vehicleManufacturingMonthYear']=cost_data_df5['vehicleManufacturingMonthYear'].astype(str)\n", "\n", "cost_data_df5[\"min_date\"] = cost_data_df5[\"min_date\"].astype(str)\n", "\n", "\n", "cost_data_df5[\"carts\"] = cost_data_df5[\"carts\"].fillna(0).astype(int)\n", "cost_data_df5[\"sales_quantity\"] = cost_data_df5[\"sales_quantity\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"grocery_qty\"] = cost_data_df5[\"grocery_qty\"].fillna(0.0).astype(float)\n", "\n", "cost_data_df5[\"rolling_7_day_carts\"] = cost_data_df5[\"rolling_7_day_carts\"].fillna(0).astype(int)\n", "\n", "cost_data_df5[\"total_weight_kg\"] = cost_data_df5[\"total_weight_kg\"].fillna(0.0).astype(float)\n", "cost_data_df5[\"total_indent\"] = cost_data_df5[\"total_indent\"].fillna(0.0).astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "cd832982-0744-4d4b-a1c2-2c8100412250", "metadata": {}, "outputs": [], "source": ["cost_data_df5[\"vehicleManufacturingMonthYear\"] = pd.to_datetime(\n", "    cost_data_df5[\"vehicleManufacturingMonthYear\"], errors=\"coerce\"\n", ")\n", "\n", "# Now you can safely use .dt to format the datetime values\n", "cost_data_df5[\"vehicleManufacturingMonthYear\"] = cost_data_df5[\n", "    \"vehicleManufacturingMonthYear\"\n", "].dt.strftime(\n", "    \"%Y-%m-%d\"\n", ")  # Or another format you prefer"]}, {"cell_type": "code", "execution_count": null, "id": "70d3ff16-f2ea-4bb8-87e7-37b16b9c60c0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9dfe4c7d-fc72-4e73-ad57-7013bc6dd3dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ab7d35e-1fe5-4e1d-be2b-735393827a53", "metadata": {}, "outputs": [], "source": ["def kwarg(df):\n", "    result = []\n", "    for col in df.columns:\n", "        dtype = str(df[col].dtype)\n", "\n", "        # Handle different dtype cases\n", "        if dtype == \"int64\":\n", "            dtype = \"integer\"\n", "        elif dtype == \"float64\":\n", "            dtype = \"REAL\"\n", "        elif dtype == \"object\":\n", "            dtype = \"varchar\"\n", "        elif dtype == \"datetime64[ns]\":\n", "            dtype = \"varchar\"  # Default to varchar for datetime64[ns] type\n", "        elif col == \"trip_creation_date\":  # Explicit check for the 'trip_creation_date' column\n", "            dtype = \"TIMESTAMP(6)\"  # Override with TIMESTAMP(6) for this specific column\n", "\n", "        # Create the metadata dictionary for the column\n", "        s = {\"name\": col, \"type\": dtype, \"description\": \"nothing\"}\n", "\n", "        # Append the column metadata to the result\n", "        result.append(s)\n", "\n", "    return result\n", "\n", "\n", "metadata = kwarg(cost_data_df5)"]}, {"cell_type": "code", "execution_count": null, "id": "42ca2d6c-0283-457c-9efd-7a37cdb2da1d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d87e6b0f-146d-4cd4-abf3-96d79eb33c20", "metadata": {}, "outputs": [], "source": ["print(metadata)"]}, {"cell_type": "code", "execution_count": null, "id": "4d55ee92-e67f-4094-9b73-aa4e04bc6a6b", "metadata": {}, "outputs": [], "source": ["cost_data_df5.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c774172b-f0e3-44b9-b8e8-d96c1a04133c", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"trip_id\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"consignment_id\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"trip_type\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"trip_state\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"trip_creation_date\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"trip_creation_time\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"wh_name\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"destination_city\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"destination_name\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"truck_number\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"truck_type\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"truck_capacity\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"expected_containers\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"dispatched_containers\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"count_cases\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"count_temp_crates\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"count_perm_crates\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"perm_disp_qty\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"dispatched_qty\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"dispatched_qty_weight\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"vendor_id\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"truck_billing_type\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"km_slab\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"hr_slab\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"base_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"current_vendor_rank\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"extra_rate_per_hr\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"extra_rate_per_km\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"extra_hrs_booked\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"extra_kms_booked\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"per_day_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"trip_distance\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\n", "            \"name\": \"total_distance_covered_by_truck_in_day\",\n", "            \"type\": \"REAL\",\n", "            \"description\": \"nothing\",\n", "        },\n", "        {\"name\": \"cost_per_trip_per_km\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"fixed_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"tolls_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"misc_charges\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"extra_kms_cost_booked\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"extra_hrs_cost_booked\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"extra_driver_cost_booked\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"total_trip_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"status\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"total_con_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"total_trip_time\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"transit_time\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"idle_time\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_vendor\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_base_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_extra_rate_per_hr\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_extra_rate_per_km\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_per_day_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_extra_hr_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_extra_km_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_fixed_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_misc_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_cost_per_trip\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_cost_per_trip_per_km\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_total_trip_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"l1_total_con_cost\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"km_per_day\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"city_groups\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"first_trip_ts\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"last_trip_ts\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"rcexpirydate\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"vehicleManufacturingMonthYear\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"vehicle_age_in_month\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"min_date\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"outlet_age\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"nothing\"},\n", "        {\"name\": \"carts\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"sales_quantity\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"grocery_qty\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"rolling_7_day_carts\", \"type\": \"integer\", \"description\": \"nothing\"},\n", "        {\"name\": \"total_weight_kg\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "        {\"name\": \"total_indent\", \"type\": \"REAL\", \"description\": \"nothing\"},\n", "    ],\n", "    \"primary_key\": [\"consignment_id\", \"trip_id\"],  # list\n", "    \"partition_key\": [\"trip_creation_date\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"trip_creation_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"CPU Analysis\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(cost_data_df5, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "945e6cd8-017e-49ae-9736-72ad4d2b7c88", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ec13653a-3ca0-4b07-814d-eadb7018c6fc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "969c780f-09a2-4290-b690-46ef06c879e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "02df401e-8c1b-45a6-b8ac-288a48c4729b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c28efd80-2e42-4823-aa5a-f8fded3ead20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "66860d95-105b-4c2b-8455-dce5c8bc750e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "72eb6c9d-f866-42eb-b01d-d8df45f9b77a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1683d5a4-ba95-4a68-ac0a-d0e27bdd27a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "37deaa75-4c71-4efc-b504-d0d71943590c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e4ae41b5-18e8-49fa-a3f1-b11137342ae6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c65d5419-a778-4f91-bfc2-a542e7b46336", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d869a9b-d76e-4d1f-ba7a-581bf3e573aa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d0180043-6325-41e4-899a-64cd32d7c73b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "03d22643-6bb7-4abe-b2bc-7a35d185ed76", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}