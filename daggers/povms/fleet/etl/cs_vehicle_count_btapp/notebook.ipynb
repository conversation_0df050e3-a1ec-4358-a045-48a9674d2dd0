{"cells": [{"cell_type": "code", "execution_count": null, "id": "f5668667-ed1c-4cb5-9417-88edaa0acab8", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "# import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "4bd8457a-5af8-4636-9a8b-8c8e3aff4b38", "metadata": {}, "outputs": [], "source": ["def import_btransit_app_data():\n", "    sql = \"\"\"\n", "        select distinct consignment_date as trip_date, trip_id, source_facility_id, facility_name, ds_outlet_id, ds_outlet_name, truck_number, truck_type, trip_state from \n", "    (WITH base AS\n", "      (SELECT *,\n", "              coalesce(vehicle_type2,vehicle_type1) AS truck_type\n", "       FROM\n", "         (SELECT t.id AS consignment_id,\n", "                 t.state AS current_state,\n", "                 trip.trip_id,\n", "                 t.source_store_id::int AS facility_id,\n", "                 n.external_name AS facility_name,\n", "                 m.outlet_id AS ds_outlet_id,\n", "                 co.name AS ds_outlet_name,\n", "                 UPPER(split_part(json_extract_path_text(t.metadata,'truck_number'),'/',1)) AS truck_number,\n", "                 split_part(json_extract_path_text(t.metadata,'truck_number'),'/',2) AS vehicle_type1,\n", "                 json_extract_path_text(t.metadata,'vehicle_type') AS vehicle_type2,\n", "                 count(DISTINCT d.external_id) AS num_invoices,\n", "                 count(DISTINCT c.external_id) AS num_containers,\n", "                 trip.truck_entry_wh,\n", "                 trip.truck_handshake,\n", "                 max(CASE\n", "                         WHEN (to_state='LOADING') THEN tl.install_ts\n", "                     END) AS loading_start,\n", "                 max(CASE\n", "                         WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts\n", "                     END) AS ready_for_dispatch,\n", "                 max(CASE\n", "                         WHEN (to_state='ENROUTE') THEN tl.install_ts\n", "                     END) AS enroute,\n", "                 max(CASE\n", "                         WHEN (to_state='REACHED') THEN tl.install_ts\n", "                     END) AS ds_reached,\n", "                 max(CASE\n", "                         WHEN (to_state='UNLOADING') THEN tl.install_ts\n", "                     END) AS unloading_start,\n", "                 max(CASE\n", "                         WHEN (to_state='COMPLETED') THEN tl.install_ts\n", "                     END) AS unloading_completed,\n", "                 trip.truck_return_wh\n", "          FROM lake_transit_server.transit_consignment t\n", "          JOIN lake_transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "          JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "          JOIN lake_transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "          JOIN lake_transit_server.transit_node n ON n.external_id = t.source_store_id\n", "          JOIN lake_retail.console_outlet_logistic_mapping m ON m.logistic_node_id = t.destination_store_id\n", "          JOIN lake_retail.console_outlet co ON co.id = m.outlet_id\n", "          JOIN\n", "            (SELECT DISTINCT t.id AS consignment_id,\n", "                             tsl.label_value AS trip_id\n", "             FROM lake_transit_server.transit_consignment t\n", "             JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "             JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "             AND tsl.label_type = 'TRIP_ID'\n", "             WHERE t.install_ts >= cast((CURRENT_DATE - 14) AS TIMESTAMP)) tc ON tc.consignment_id = t.id\n", "          JOIN\n", "            (SELECT tsl.label_value AS trip_id,\n", "                    min(q.entry_ts) AS truck_entry_wh,\n", "                    min(tts.install_ts) AS truck_return_wh,\n", "                    min(t.install_ts) AS truck_handshake\n", "             FROM lake_transit_server.transit_consignment t\n", "             JOIN lake_transit_server.transit_shipment ts ON ts.external_id = t.id\n", "             JOIN lake_transit_server.transit_shipment_label tsl ON tsl.shipment_id = ts.id\n", "             AND tsl.label_type = 'TRIP_ID'\n", "             JOIN lake_transit_server.transit_task tt ON tt.shipment_label_value = tsl.label_value\n", "             AND tt.shipment_label_type = 'TRIP_ID'\n", "             LEFT JOIN lake_transit_server.task_management_taskstatelog tts ON tts.task_id = tt.id\n", "             AND tts.to_state = 'COMPLETED'\n", "             JOIN lake_transit_server.transit_express_allocation_field_executive_queue q ON (q.user_profile_id || '_' || q.id) = tsl.label_value\n", "             WHERE t.install_ts >= cast((CURRENT_DATE - 14) AS TIMESTAMP)\n", "             GROUP BY 1) trip ON trip.trip_id = tc.trip_id\n", "          WHERE t.install_ts >= cast((CURRENT_DATE - 14) AS TIMESTAMP)\n", "          GROUP BY 1,\n", "                   2,\n", "                   3,\n", "                   4,\n", "                   5,\n", "                   6,\n", "                   7,\n", "                   13,\n", "                   14,\n", "                   21,\n", "                   t.metadata)),\n", "         pos AS\n", "      (SELECT DISTINCT td.consignment_id AS csmt_id,\n", "                       max(s.dispatch_time) AS dispatch_time\n", "       FROM lake_pos.pos_invoice pi\n", "       JOIN lake_transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "       JOIN lake_po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "       JOIN lake_retail.console_outlet co ON co.id = pi.outlet_id\n", "       AND co.business_type_id IN (1,\n", "                                   12,20)\n", "       WHERE pi.created_at >= cast((CURRENT_DATE - 14) AS TIMESTAMP)\n", "         AND invoice_type_id IN (5,\n", "                                 14,\n", "                                 16)\n", "       GROUP BY 1),\n", "         grn AS\n", "      (SELECT DISTINCT t.id AS c_id,\n", "                       min(il.pos_timestamp) AS grn_started_at,\n", "                       max(il.pos_timestamp) AS grn_completed_at\n", "       FROM lake_transit_server.transit_consignment t\n", "       JOIN lake_transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "       JOIN lake_ims.ims_inventory_log il ON il.merchant_invoice_id = d.external_id\n", "       WHERE t.install_ts >= cast((CURRENT_DATE - 14) AS TIMESTAMP)\n", "         AND inventory_update_type_id IN (1,\n", "                                          28,\n", "                                          76,\n", "                                          90,\n", "                                          93)\n", "       GROUP BY 1)\n", "       \n", "    , trip_state as (\n", "    select distinct trip_id, state as trip_state\n", "    from lake_transit_server.transit_projection_trip\n", "    where install_ts >= current_date - 30\n", "    )\n", "    \n", "    , excpected_timelines as (\n", "    \n", "        with base_ as (\n", "            select \n", "                trip_id,\n", "                event_type,\n", "                (last_value(scheduled_ts) \n", "                    over(partition by trip_id,event_type \n", "                         order by update_ts rows between unbounded preceding and unbounded following) ) + interval '5.5 hrs'\n", "                     as scheduled_ts,\n", "                (last_value(expected_ts) \n", "                    over(partition by trip_id,event_type \n", "                         order by update_ts rows between unbounded preceding and unbounded following) ) + interval '5.5 hrs'\n", "                     as expected_ts\n", "            from \n", "                lake_transit_server.transit_projection_trip_event_timeline as tp\n", "            where \n", "            event_type in ('DS_ARRIVAL', 'UNLOADING_END', 'COMPLETED')\n", "            and date(date_trunc('week',date((install_ts + interval '5.5 hrs')))) >= date(date_trunc('week',current_date-90))\n", "        )\n", "        \n", "        select\n", "            trip_id, \n", "            max(CASE WHEN event_type = 'DS_ARRIVAL' THEN expected_ts ELSE NULL END ) AS exp_ds_reach, \n", "            max(CASE WHEN event_type = 'DS_ARRIVAL' THEN scheduled_ts ELSE NULL END ) AS sch_ds_reach, \n", "            max(CASE WHEN event_type = 'UNLOADING_END' THEN expected_ts ELSE NULL END) AS exp_unloading_complete, \n", "            max(CASE WHEN event_type = 'UNLOADING_END' THEN scheduled_ts ELSE NULL END) AS sch_unloading_complete, \n", "            max(CASE WHEN event_type = 'COMPLETED' THEN expected_ts ELSE NULL END) AS exp_truck_return_wh,\n", "            max(CASE WHEN event_type = 'COMPLETED' THEN scheduled_ts ELSE NULL END) AS sch_truck_return_wh\n", "            \n", "        FROM base_ \n", "        group by 1\n", "        \n", "    )\n", "    \n", "    SELECT consignment_id,\n", "           base.trip_id,\n", "           (truck_handshake + interval '5.5 hrs')::date AS consignment_date,\n", "           case \n", "                when facility_id = 1743 then 264 \n", "                when facility_id = 4306 then 1983\n", "                when facility_id = 4283 then 1964\n", "                else facility_id\n", "            end as source_facility_id,\n", "           facility_name,\n", "           ds_outlet_id,\n", "           ds_outlet_name,\n", "           truck_number,\n", "           truck_type,\n", "           trip_state,\n", "           current_state as consignment_state,\n", "           num_invoices,\n", "           num_containers,\n", "           (dispatch_time + interval '4.5 hr') AS scheduled_truck_arrival,\n", "           (truck_entry_wh + interval '5.5 hrs') AS truck_entry_wh,\n", "           (truck_handshake + interval '5.5 hrs') AS truck_handshake,\n", "           (loading_start + interval '5.5 hrs') AS loading_start,\n", "           (ready_for_dispatch + interval '5.5 hrs') AS ready_for_dispatch,\n", "           (enroute + interval '5.5 hrs') AS enroute,\n", "           (dispatch_time + interval '5.5 hrs') AS scheduled_dispatch_time,\n", "           (ds_reached + interval '5.5 hrs') AS ds_reached,\n", "           exp_ds_reach,\n", "           sch_ds_reach,\n", "           (unloading_start + interval '5.5 hrs') AS unloading_start,\n", "           (unloading_completed + interval '5.5 hrs') AS unloading_completed,\n", "           exp_unloading_complete,\n", "           sch_unloading_complete,\n", "           (grn_started_at + interval '5.5 hrs') AS grn_started_at,\n", "           (grn_completed_at + interval '5.5 hrs') AS grn_completed_at,\n", "           (truck_return_wh + interval '5.5 hrs') AS truck_return_wh,\n", "           exp_truck_return_wh,\n", "           sch_truck_return_wh,\n", "           CASE\n", "               WHEN datediff(MINUTE,scheduled_truck_arrival,truck_entry_wh) <= 0 THEN 0\n", "               ELSE datediff(MINUTE,scheduled_truck_arrival,truck_entry_wh)\n", "           END AS truck_arrival_delay_min,\n", "           datediff(MINUTE,truck_handshake,loading_start) AS handshake_to_loading_min,\n", "           datediff(MINUTE,loading_start,ready_for_dispatch) AS total_scan_min,\n", "           datediff(MINUTE,ready_for_dispatch,enroute) AS driver_delay_min,\n", "           datediff(MINUTE,loading_start,enroute) AS loading_time_min,\n", "           CASE\n", "               WHEN datediff(MINUTE,scheduled_dispatch_time,enroute) <= 0 THEN 0\n", "               ELSE datediff(MINUTE,scheduled_dispatch_time,enroute)\n", "           END AS dispatch_delay_min,\n", "           \n", "           CASE \n", "                WHEN ds_reached > exp_ds_reach then datediff(MINUTE,exp_ds_reach,ds_reached) \n", "                else 0\n", "            end as ds_reach_delay_from_expected,\n", "            \n", "            CASE \n", "                WHEN ds_reached > sch_ds_reach then datediff(MINUTE,sch_ds_reach,ds_reached)\n", "                else 0\n", "            end as ds_reach_delay_from_scheduled,\n", "           \n", "           datediff(MINUTE,unloading_start,unloading_completed) AS unloading_time_min,\n", "           datediff(MINU<PERSON>,truck_entry_wh,truck_return_wh) AS truck_time_at_wh,\n", "           datediff(MINUTE,unloading_completed,truck_return_wh) AS return_transit_truck_time\n", "           \n", "           \n", "    FROM base\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    LEFT JOIN grn ON grn.c_id = base.consignment_id\n", "    left join trip_state as st on base.trip_id = st.trip_id\n", "    left join excpected_timelines as et on base.trip_id = et.trip_id\n", "    where consignment_date <= (current_date-1)\n", "    and consignment_date >= (current_date-12)\n", "    ORDER BY 4,\n", "             1)\n", "\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    # b_transit_df = pd.read_sql_query(sql=sql, con=con)\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "b_transit_df = import_btransit_app_data()\n", "b_transit_df[\"truck_type\"] = b_transit_df[\"truck_type\"].str.lower()\n", "b_transit_df[\"truck_type\"] = b_transit_df[\"truck_type\"].str.strip()"]}, {"cell_type": "code", "execution_count": null, "id": "cdbafffa-bbc4-4415-8d53-8b36f88ef8c1", "metadata": {}, "outputs": [], "source": ["b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\" \", \"\")\n", "b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\"-\", \"\")\n", "b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\"/\", \"\")\n", "b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\"/\", \"\")"]}, {"cell_type": "code", "execution_count": null, "id": "bd37709a-7ff6-4b9d-83c4-39b2b9fe2599", "metadata": {}, "outputs": [], "source": ["b_transit_df"]}, {"cell_type": "code", "execution_count": null, "id": "e9e15956-5a28-441e-a821-19bf1bb560fe", "metadata": {}, "outputs": [], "source": ["bt_df = b_transit_df\n", "bt_df = bt_df[\n", "    [\"trip_date\", \"truck_number\", \"truck_type\", \"source_facility_id\", \"facility_name\"]\n", "]\n", "temp = bt_df.groupby(\n", "    by=[\n", "        \"trip_date\",\n", "        \"truck_number\",\n", "        \"truck_type\",\n", "        \"source_facility_id\",\n", "        \"facility_name\",\n", "    ]\n", ").sum()"]}, {"cell_type": "code", "execution_count": null, "id": "45cdcc48-1c94-4817-92e0-20db36d4c747", "metadata": {}, "outputs": [], "source": ["bt_df = temp.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "d8cb86dc-42ec-4a07-a8e4-4fb72243e007", "metadata": {}, "outputs": [], "source": ["bt_df[\"count\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "fc2b04fa-3666-4cdc-a43a-d6ceca176985", "metadata": {}, "outputs": [], "source": ["bt_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fc12de09-771a-471f-9675-ffe93b5e3ace", "metadata": {}, "outputs": [], "source": ["def cost_sheet():\n", "    sql = \"\"\"\n", "    with be as (select distinct a.facility_id, cloud_store_id as outlet_id from lake_po.physical_facility_outlet_mapping a\n", "     join lake_po.bulk_facility_outlet_mapping b on a.facility_id=b.facility_id\n", "     join lake_retail.warehouse_outlet_mapping \n", "     on warehouse_id=a.outlet_id\n", "     where a.active=1 and a.ars_active=1 and b.active =1\n", "     group by 1,2),\n", "\n", "    ex as \n", "        (select date(date_) as trip_date ,\n", "         (case \n", "         when (source_name like ('%%M9%%')) then ('1983') \n", "         else source_facility_id \n", "         end) as source_facility_id,\n", "         source_name as source_name_csheet,\n", "         category,\n", "         destination_outlet_id as ds_outlet_id,\n", "         destination_name as destination_name_csheet, \n", "         \"vehicle no.\" as truck_number,\n", "         \"vehicle size\" as truck_type,\n", "         \"fixed/adhoc\" as vehicle_type\n", "         from metrics.base_fleet_costs\n", "         where date(date_)>=(current_date - 12)\n", "         and date(date_)<=(current_date - 1)\n", "         and source_facility_id >0\n", "         --date(date_) between (current_date - 7) and (current_date - 1) \n", "         group by 1,2,3,4,5,6,7,8,9)\n", "\n", "         select * from ex\n", "         where source_facility_id in (select facility_id from be)    \n", "         \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "csheet_raw = cost_sheet()"]}, {"cell_type": "code", "execution_count": null, "id": "8fe7f43f-43f1-4a43-9504-5b9d75a5634a", "metadata": {}, "outputs": [], "source": ["csheet = csheet_raw"]}, {"cell_type": "code", "execution_count": null, "id": "a149c28a-f460-45af-8de0-fa54d6967bba", "metadata": {}, "outputs": [], "source": ["csheet"]}, {"cell_type": "code", "execution_count": null, "id": "8d177334-e6d6-44e4-adb9-fa43a10457ed", "metadata": {}, "outputs": [], "source": ["# csheet[\"source_facility_id\"] = csheet[\"source_facility_id\"].str.replace(\"\", \"0\")\n", "csheet[\"source_facility_id\"] = csheet[\"source_facility_id\"].astype(int)\n", "csheet[\"ds_outlet_id\"] = csheet[\"ds_outlet_id\"].str.replace(\"\", \"0\")\n", "csheet[\"ds_outlet_id\"] = csheet[\"ds_outlet_id\"].astype(int)\n", "csheet[\"truck_number\"] = csheet[\"truck_number\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "61713a46-479f-46dc-a862-2178f20336c2", "metadata": {}, "outputs": [], "source": ["### Truck Type\n", "csheet[\"truck_type\"] = csheet[\"truck_type\"].str.lower()\n", "csheet[\"truck_type\"] = csheet[\"truck_type\"].str.strip()\n", "### Vehicle Number\n", "csheet[\"truck_number\"] = csheet[\"truck_number\"].str.replace(\" \", \"\")\n", "csheet[\"truck_number\"] = csheet[\"truck_number\"].str.replace(\"-\", \"\")\n", "csheet[\"truck_number\"] = csheet[\"truck_number\"].str.replace(\"/\", \"\")\n", "csheet[\"truck_number\"] = csheet[\"truck_number\"].str.replace(\"/\", \"\")"]}, {"cell_type": "code", "execution_count": null, "id": "273b11bb-9b74-4522-9798-737cdb931f05", "metadata": {}, "outputs": [], "source": ["cgsheet = pd.DataFrame()\n", "cgsheet = csheet.loc[csheet[\"category\"] != \"FNV\"]\n", "cgsheet = cgsheet.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "784a806e-221b-42a4-a3a6-608c33ae1313", "metadata": {}, "outputs": [], "source": ["cgsheet = cgsheet[\n", "    [\n", "        \"trip_date\",\n", "        \"truck_number\",\n", "        \"truck_type\",\n", "        \"source_facility_id\",\n", "        \"source_name_csheet\",\n", "    ]\n", "]\n", "temp = cgsheet.groupby(\n", "    by=[\n", "        \"trip_date\",\n", "        \"truck_number\",\n", "        \"truck_type\",\n", "        \"source_facility_id\",\n", "        \"source_name_csheet\",\n", "    ]\n", ").sum()\n", "cgsheet = temp.reset_index()\n", "cgsheet[\"count\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "f8d56571-5045-4025-828e-77f688acd2fe", "metadata": {}, "outputs": [], "source": ["cgsheet"]}, {"cell_type": "code", "execution_count": null, "id": "aa87ba42-6db6-40dc-a732-3728f39c16a0", "metadata": {}, "outputs": [], "source": ["def import_BE_mapping():\n", "    sql = \"\"\"\n", "        select distinct a.facility_id, a.outlet_name as outlet_name from lake_po.physical_facility_outlet_mapping a\n", "         join lake_po.bulk_facility_outlet_mapping b on a.facility_id=b.facility_id\n", "         join lake_retail.warehouse_outlet_mapping \n", "         on warehouse_id=a.outlet_id\n", "         where a.active=1 and a.ars_active=1 and b.active =1\n", "         group by 1,2\n", "\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "be_mapping = import_BE_mapping()"]}, {"cell_type": "code", "execution_count": null, "id": "8228cef1-1656-41a2-892d-ef568531ebec", "metadata": {}, "outputs": [], "source": ["vn_wrng = []\n", "b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\n", "    \" \", \"\", regex=True\n", ")\n", "b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\n", "    \".\", \"\", regex=True\n", ")\n", "b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\n", "    \"-\", \"\", regex=True\n", ")\n", "b_transit_df[\"truck_number\"] = b_transit_df[\"truck_number\"].str.replace(\n", "    \"/\", \"\", regex=True\n", ")\n", "\n", "for ind in b_transit_df[\"truck_number\"].index:\n", "    if len(b_transit_df[\"truck_number\"][ind]) < 8:\n", "        vn_wrng.append(ind)\n", "# vn_wrng\n", "# b_transit_df.iloc[vn_wrng].to_csv(\"Vehicle_Number_Check_Btransit_app.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "ce6b2a64-ad35-4579-9c28-3b6fba6dda19", "metadata": {}, "outputs": [], "source": ["# vehicle count from btransit app data\n", "vc_bt_df = bt_df.groupby(\n", "    by=[\n", "        \"trip_date\",\n", "        \"truck_type\",\n", "        \"source_facility_id\",\n", "        \"facility_name\",\n", "    ]\n", ").sum()\n", "\n", "vc_bt_df = vc_bt_df.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "e7135f3d-a039-4763-8ea3-ccbe61270e75", "metadata": {}, "outputs": [], "source": ["vc_bt_df"]}, {"cell_type": "code", "execution_count": null, "id": "34cd03f0-0973-42e8-9722-855549dc9391", "metadata": {}, "outputs": [], "source": ["vc_bt_df[\"flag\"] = \"BT App\""]}, {"cell_type": "code", "execution_count": null, "id": "e1783291-2da2-4725-95cf-ca805ff2136c", "metadata": {}, "outputs": [], "source": ["vc_bt_df"]}, {"cell_type": "code", "execution_count": null, "id": "6fb471b0-32da-4431-b831-6107692cb594", "metadata": {}, "outputs": [], "source": ["# cgsheet['flag'] = 'Cost Sheet'\n", "cgsheet = cgsheet.replace(\"14 ft canter\", \"14 ft\")\n", "cgsheet = cgsheet.replace(\"17 ft canter\", \"17 ft\")\n", "cgsheet = cgsheet.replace(\"20 ft canter\", \"20 ft\")\n", "cgsheet = cgsheet.replace(\"bolero pickup\", \"pickup\")"]}, {"cell_type": "code", "execution_count": null, "id": "7e05abc6-885c-4143-9840-2239f6fab9a4", "metadata": {}, "outputs": [], "source": ["# cgsheet"]}, {"cell_type": "code", "execution_count": null, "id": "4265eeff-1b1f-44cd-a5ac-85b298ff7f7b", "metadata": {}, "outputs": [], "source": ["cgsheet[\"count\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "486535f4-5b17-485f-981f-6f9fb9b090bd", "metadata": {}, "outputs": [], "source": ["vc_cgsheet = cgsheet.groupby(\n", "    by=[\n", "        \"trip_date\",\n", "        \"truck_type\",\n", "        \"source_facility_id\",\n", "        \"source_name_csheet\",\n", "    ]\n", ").sum()\n", "\n", "vc_cgsheet = vc_cgsheet.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "00fcaf79-8b3b-4a60-82ff-aafd316436a0", "metadata": {}, "outputs": [], "source": ["vc_cgsheet[\"flag\"] = \"Cost Sheet\""]}, {"cell_type": "code", "execution_count": null, "id": "374ce252-3e83-45d2-b574-e748ebbe9917", "metadata": {}, "outputs": [], "source": ["vc_cgsheet"]}, {"cell_type": "code", "execution_count": null, "id": "70325af0-b9bb-42f6-8627-f7cd3246818b", "metadata": {}, "outputs": [], "source": ["temp = pd.concat(\n", "    [\n", "        vc_bt_df[[\"trip_date\", \"source_facility_id\", \"truck_type\", \"count\", \"flag\"]],\n", "        vc_cgsheet[[\"trip_date\", \"source_facility_id\", \"truck_type\", \"count\", \"flag\"]],\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e3e99c1e-5796-4319-85db-3d4565034aa9", "metadata": {}, "outputs": [], "source": ["temp"]}, {"cell_type": "code", "execution_count": null, "id": "54bbb33d-f4bc-4aba-857d-0265225804a5", "metadata": {}, "outputs": [], "source": ["final_df = temp.merge(\n", "    be_mapping, how=\"left\", left_on=[\"source_facility_id\"], right_on=[\"facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "54a5131b-2e33-4ed5-80f3-126487309b71", "metadata": {}, "outputs": [], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "751db0ed-9046-4932-bd70-0b2101c0ec3c", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_df, \"10GcIFYEDIsVMkti7BISSEGivfv_xeUyngZ1Wt5uUnuo\", \"Sheet1\")"]}, {"cell_type": "code", "execution_count": null, "id": "c04c2282-7bb4-43fd-b4c7-74c01289da60", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}