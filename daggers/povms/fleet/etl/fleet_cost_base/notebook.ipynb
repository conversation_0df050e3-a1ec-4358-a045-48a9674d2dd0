{"cells": [{"cell_type": "code", "execution_count": null, "id": "2c1f0e3f-25f7-4528-9959-43e027d49813", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "83b19126-50be-46d8-a289-ea6be43537d5", "metadata": {}, "outputs": [], "source": ["redshift_schema_name = \"metrics\"\n", "redshift_table_name = \"base_fleet_costs\"\n", "\n", "# Connecting with Redshift\n", "redshift_connection = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "109bb2d7-ac75-4336-bb1e-981cc14c39e6", "metadata": {}, "outputs": [], "source": ["current_date = datetime.datetime.now(timezone(\"Asia/Kolkata\"))\n", "current_day = datetime.datetime.now(timezone(\"Asia/Kolkata\")).strftime(\"%A\")\n", "\n", "# current year and month\n", "current_year = current_date.year\n", "current_month = current_date.month\n", "\n", "# previous year and month\n", "prev_year = (current_date - pd.DateOffset(months=1)).year\n", "prev_month = (current_date - pd.DateOffset(months=1)).month"]}, {"cell_type": "code", "execution_count": null, "id": "3771ae50-b1d5-4355-b6cc-b4808e85ce77", "metadata": {}, "outputs": [], "source": ["current_day"]}, {"cell_type": "code", "execution_count": null, "id": "e68e9d84-1026-43ea-99cd-4d19e7ce803f", "metadata": {}, "outputs": [], "source": ["# Add sheet id to month_dict\n", "month_dict = {\n", "    \"2023-02\": \"1nzr3_Ves0DQOhXihtDeySyFWV1moXIh4g5rcfVv3ss8\",\n", "    \"2023-03\": \"1yYIYr0AofYxWL2cmjQcgO7jL1iVowS7jRqPumjLRb0o\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "55cd7f64-63dd-46af-80bd-b9edad9a8d58", "metadata": {}, "outputs": [], "source": ["def main_funct(year, month, sheet_id):\n", "\n", "    df = pb.from_sheets(\n", "        sheetid=sheet_id, sheetname=\"Master Sheet PAN India Grocery + F&V\"\n", "    )\n", "\n", "    df.columns = df.iloc[0]\n", "    df.drop(df.index[0], inplace=True)\n", "\n", "    if year == 2022 and month == 10:\n", "\n", "        df.insert(loc=8, column=\"Vehicle no.\", value=np.nan)\n", "        # df[] = np.nan\n", "\n", "    df.drop(\n", "        [\n", "            \"Zone\",\n", "            \"Updated By\",\n", "            \"ES SAP code\",\n", "            \"Kms Slab (as per LOI)\",\n", "            \"Remarks (Reverse/IT/Infra/Setup/Closure)\",\n", "            \"Fixed Rate (As per contract/LOI)\",\n", "            \"Tolls/Parking/MCD/NGT (valid slips)\",\n", "            \"No entry & Others\",\n", "            \"No Entry cost\",\n", "            \" \",\n", "            \"\",\n", "        ],\n", "        axis=1,\n", "        errors=\"ignore\",\n", "        inplace=True,\n", "    )\n", "\n", "    df[\"Function\"] = np.where(\n", "        df[\"Function\"] == \"Express Store\",\n", "        \"Grocery\",\n", "        np.where(\n", "            df[\"Function\"] == \"Express Store - FNV\",\n", "            \"FNV\",\n", "            np.where(\n", "                df[\"Function\"] == \"Express Store - Perishable\", \"Perishable\", \"Infra\"\n", "            ),\n", "        ),\n", "    )\n", "    df.rename(columns={\"Function\": \"category\"}, inplace=True)\n", "\n", "    pivot_cols = [\n", "        \"BE Facility ID\",\n", "        \"category\",\n", "        \"Source FC\",\n", "        \"DS/DP/FC Name\",\n", "        \"Outlet ID\",\n", "        \"Vendor Name\",\n", "        \"Vehicle no.\",\n", "        \"Fixed/Adhoc\",\n", "        \"Vehicle Size\",\n", "        \"Fixed Working Hours\",\n", "        \"No. of vehicles (MTD)\",\n", "        \"Daily/trip Cost \",\n", "        \"MTD Fixed cost\",\n", "        \"MTD adhoc cost\",\n", "        \"Total Cost ( Inc buffer)\",\n", "    ]\n", "\n", "    num_days = calendar.monthrange(year, month)[1]\n", "    days = [datetime.date(year, month, day) for day in range(1, num_days + 1)]\n", "\n", "    start = len(pivot_cols)\n", "    end = [i for i in range(14, len(df.columns))]\n", "\n", "    column_indices = [i for i in range(start, len(df.columns))]\n", "    new_names = days\n", "    old_names = df.columns[column_indices]\n", "    df.rename(columns=dict(zip(old_names, new_names)), inplace=True)\n", "\n", "    new_df = df.melt(\n", "        id_vars=pivot_cols, value_vars=days, var_name=\"date_\", value_name=\"date_cost\"\n", "    )\n", "\n", "    # replace epmty strings with nan\n", "    new_df.replace(r\"^\\s*$\", np.nan, regex=True, inplace=True)\n", "\n", "    new_df[\"date_cost\"] = new_df[\"date_cost\"].astype(float)\n", "    new_df[\"Daily/trip Cost \"] = new_df[\"Daily/trip Cost \"].astype(float)\n", "\n", "    new_df[\"date_cost\"] = new_df[\"Daily/trip Cost \"] * new_df[\"date_cost\"]\n", "\n", "    arr_cols = [\n", "        \"BE Facility ID\",\n", "        \"Source FC\",\n", "        \"Outlet ID\",\n", "        \"DS/DP/FC Name\",\n", "        \"category\",\n", "        \"date_\",\n", "        \"date_cost\",\n", "        \"Vendor Name\",\n", "        \"Vehicle no.\",\n", "        \"Fixed/Adhoc\",\n", "        \"Vehicle Size\",\n", "        \"Fixed Working Hours\",\n", "        \"No. of vehicles (MTD)\",\n", "        \"Daily/trip Cost \",\n", "        \"MTD Fixed cost\",\n", "        \"MTD adhoc cost\",\n", "        \"Total Cost ( Inc buffer)\",\n", "    ]\n", "\n", "    new_df = new_df[arr_cols]\n", "\n", "    new_df.rename(\n", "        columns={\n", "            \"BE Facility ID\": \"source_facility_id\",\n", "            \"Source FC\": \"source_name\",\n", "            \"Outlet ID\": \"destination_outlet_id\",\n", "            \"DS/DP/FC Name\": \"destination_name\",\n", "        },\n", "        inplace=True,\n", "    )\n", "\n", "    # new_df['date_cost'] = new_df['date_cost'].fillna(0)\n", "    new_df.dropna(subset=[\"date_cost\"], inplace=True)\n", "    # new_df['date_'] = pd.to_datetime(new_df['date_'], format='%Y-%m-%d')\n", "\n", "    new_df.columns = new_df.columns.str.strip()\n", "\n", "    wh_city_query = \"\"\" \n", "    select \n", "        pfom.facility_id, \n", "        case \n", "            when co.name ilike '%%budhpur%%' then 'Delhi'\n", "            else co.name\n", "            end as source_city\n", "\n", "    from \n", "        lake_po.physical_facility_outlet_mapping pfom\n", "    left join \n", "        lake_crates.facility cf \n", "        ON cf.id = pfom.facility_id\n", "    inner join \n", "        (   select distinct m.facility_id, c.name \n", "            from lake_retail.console_outlet m \n", "            inner join lake_retail.console_location c \n", "            on c.id = m.tax_location_id\n", "            where business_type_id in (1,12,19,20,21) \n", "        ) co\n", "        on pfom.facility_id = co.facility_id \n", "\n", "    where pfom.ars_active = 1 AND pfom.active = 1\n", "\n", "    group by 1,2\n", "    \"\"\"\n", "\n", "    wh_city_df = pd.read_sql_query(sql=wh_city_query, con=redshift_connection)\n", "    wh_city_df[\"facility_id\"] = wh_city_df[\"facility_id\"].astype(str)\n", "\n", "    ds_city_query = \"\"\" \n", "     SELECT \n", "            DISTINCT cl.name AS destination_city, \n", "            bfom.outlet_id\n", "        FROM lake_po.bulk_facility_outlet_mapping AS bfom\n", "    INNER JOIN \n", "        lake_retail.console_outlet AS co \n", "        ON bfom.outlet_id = co.id AND co.business_type_id = 7       -- business_type_id = 7  means DS\n", "    LEFT JOIN \n", "        lake_po.physical_facility_outlet_mapping AS pfom  \n", "        ON pfom.outlet_id = bfom.outlet_id\n", "    LEFT JOIN \n", "        lake_retail.console_location AS cl \n", "        ON cl.id = pfom.city_id\n", "    \"\"\"\n", "\n", "    ds_city_df = pd.read_sql_query(sql=ds_city_query, con=redshift_connection)\n", "    ds_city_df[\"outlet_id\"] = ds_city_df[\"outlet_id\"].astype(str)\n", "\n", "    new_df = pd.merge(\n", "        new_df,\n", "        wh_city_df,\n", "        how=\"left\",\n", "        left_on=\"source_facility_id\",\n", "        right_on=\"facility_id\",\n", "    )\n", "    new_df = pd.merge(\n", "        new_df,\n", "        ds_city_df,\n", "        how=\"left\",\n", "        left_on=\"destination_outlet_id\",\n", "        right_on=\"outlet_id\",\n", "    )\n", "\n", "    new_df.drop([\"facility_id\", \"outlet_id\"], axis=1, inplace=True)\n", "\n", "    return new_df"]}, {"cell_type": "code", "execution_count": null, "id": "229acb2a-2fa7-4008-9f6c-b25d61d446bc", "metadata": {}, "outputs": [], "source": ["current_year_month = current_date.strftime(\"%Y-%m\")\n", "\n", "current_df = main_funct(\n", "    year=current_year, month=current_month, sheet_id=month_dict[current_year_month]\n", ")\n", "current_df = main_funct(\n", "    year=current_year, month=current_month - 1, sheet_id=month_dict[current_year_month]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b1bdeae0-03b1-4c73-88a1-7f1d949a4ca7", "metadata": {}, "outputs": [], "source": ["current_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f5dd0a8d-6b8f-4d6e-bab0-9b0ae5005677", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": redshift_schema_name,\n", "    \"table_name\": redshift_table_name,\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"source_facility_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"source facility id\",\n", "        },\n", "        {\"name\": \"source_name\", \"type\": \"varchar\", \"description\": \"source name\"},\n", "        {\n", "            \"name\": \"destination_outlet_id\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination outlet id\",\n", "        },\n", "        {\n", "            \"name\": \"destination_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination name\",\n", "        },\n", "        {\"name\": \"category\", \"type\": \"varchar\", \"description\": \"category\"},\n", "        {\"name\": \"date_\", \"type\": \"varchar\", \"description\": \"Date\"},\n", "        {\"name\": \"date_cost\", \"type\": \"float\", \"description\": \"cost for the date\"},\n", "        {\"name\": \"Vendor Name\", \"type\": \"varchar\", \"description\": \"Vendor Name\"},\n", "        {\"name\": \"Vehicle no.\", \"type\": \"varchar\", \"description\": \"Vehicle no.\"},\n", "        {\"name\": \"Fixed/Adhoc\", \"type\": \"varchar\", \"description\": \"Fixed/Adhoc\"},\n", "        {\"name\": \"Vehicle Size\", \"type\": \"varchar\", \"description\": \"Vehicle Size\"},\n", "        {\n", "            \"name\": \"Fixed Working Hours\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Fixed Working Hours\",\n", "        },\n", "        {\n", "            \"name\": \"No. of vehicles (MTD)\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"No. of vehicles (MTD)\",\n", "        },\n", "        {\n", "            \"name\": \"Daily/trip Cost\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Daily/trip Cost\",\n", "        },\n", "        {\"name\": \"MTD Fixed cost\", \"type\": \"varchar\", \"description\": \"MTD Fixed cost\"},\n", "        {\"name\": \"MTD adhoc cost\", \"type\": \"varchar\", \"description\": \"MTD adhoc cost\"},\n", "        {\n", "            \"name\": \"Total Cost ( Inc buffer)\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Total Cost ( Inc buffer)\",\n", "        },\n", "        {\"name\": \"source_city\", \"type\": \"varchar\", \"description\": \"source city\"},\n", "        {\n", "            \"name\": \"destination_city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"destination city\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"source_facility_id\",\n", "        \"destination_outlet_id\",\n", "        \"category\",\n", "        \"Fixed/Adhoc\",\n", "        \"date_\",\n", "    ],  # list\n", "    \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"date_\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"Base table for fleet costs\"\"\",\n", "}\n", "\n", "print(\"pushing to redshift\")\n", "\n", "pb.to_redshift(current_df, **kwargs)\n", "\n", "print(\"Complete!!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}