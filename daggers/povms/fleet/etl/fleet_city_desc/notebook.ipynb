{"cells": [{"cell_type": "code", "execution_count": null, "id": "81a56f02-896c-484c-9ac8-b0697d1862a1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "75df6418-a5ef-4177-835e-7cdf6c0a1952", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5593bcd5-978a-4f0a-a34c-199a792d6d85", "metadata": {}, "outputs": [], "source": ["trino_schema_name = \"supply_etls\"  # playground supply_etls\n", "trino_table_name = \"fleet_city_desc\""]}, {"cell_type": "code", "execution_count": null, "id": "8d5d6cd5-7187-415b-a0f2-380021c18cf1", "metadata": {}, "outputs": [], "source": ["# store_bucket = pd.read_csv('30D_store_bucket.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "ee8fe27c-0fbf-408d-bcf4-1e99d009bcd6", "metadata": {}, "outputs": [], "source": ["# store_bucket = pb.from_sheets(sheet_id=\"\", sheet_name=\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "b15f0e14-19a1-4875-96f9-335151f2f8c6", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ed23d8ce7TbGqEd3xMQE0PPxEvfYNyAhzec2pggjU5o\"\n", "sheet_name = \"raw_data\"\n", "store_bucket = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "d3b9ce36-869b-4d5d-85a3-84d997e3db10", "metadata": {}, "outputs": [], "source": ["store_bucket.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0ff37151-6fc7-40bc-a4ea-6222837d6198", "metadata": {}, "outputs": [], "source": ["store_bucket.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "41f3cc14-5bac-435c-9cc4-6dbd02d7fc59", "metadata": {}, "outputs": [], "source": ["store_bucket[\"trip_creation_date\"] = pd.to_datetime(store_bucket[\"trip_creation_date\"]).dt.date\n", "store_bucket[\"trip_id\"] = store_bucket[\"trip_id\"].astype(str)\n", "store_bucket[\"wh_outlet_id\"] = store_bucket[\"wh_outlet_id\"].astype(int)\n", "store_bucket[\"wh_outlet_name\"] = store_bucket[\"wh_outlet_name\"].astype(str)\n", "store_bucket[\"ds_outlet_id\"] = store_bucket[\"ds_outlet_id\"].astype(int)\n", "store_bucket[\"ds_outlet_name\"] = store_bucket[\"ds_outlet_name\"].astype(str)\n", "store_bucket[\"source_city\"] = store_bucket[\"source_city\"].astype(str)\n", "store_bucket[\"destination_city\"] = store_bucket[\"destination_city\"].astype(str)\n", "store_bucket[\"city_bucket\"] = store_bucket[\"city_bucket\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "cf4dbc59-ae44-4fcd-a3a4-8ba9953e85c7", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"trip_id\", \"type\": \"varchar\", \"description\": \"trip_id\"},\n", "        {\"name\": \"trip_creation_date\", \"type\": \"Date\", \"description\": \"date\"},\n", "        {\"name\": \"wh_outlet_id\", \"type\": \"integer\", \"description\": \"wh_outlet_id\"},\n", "        {\"name\": \"wh_outlet_name\", \"type\": \"varchar\", \"description\": \"outlet name\"},\n", "        {\"name\": \"ds_outlet_id\", \"type\": \"integer\", \"description\": \"Ds outlet id\"},\n", "        {\"name\": \"ds_outlet_name\", \"type\": \"varchar\", \"description\": \"Ds outlet name\"},\n", "        {\"name\": \"source_city\", \"type\": \"varchar\", \"description\": \"source city\"},\n", "        {\"name\": \"destination_city\", \"type\": \"varchar\", \"description\": \"destination city\"},\n", "        {\"name\": \"city_bucket\", \"type\": \"varchar\", \"description\": \"City Bucket\"},\n", "    ],\n", "    \"primary_key\": [\"trip_id\", \"ds_outlet_id\"],  # list\n", "    \"partition_key\": [\"trip_creation_date\"],\n", "    # \"sortkey\": [\"date_\"],  # list\n", "    \"incremental_key\": \"trip_creation_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"\"\"fleet_city_desc\"\"\",\n", "    \"force_upsert_without_increment_check\": False,\n", "}\n", "\n", "print(\"pushing to trino\")\n", "\n", "pb.to_trino(store_bucket, **kwargs)\n", "\n", "print(\"Complete!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "c7f161ca-5e0e-48b7-a2da-15cda3549834", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}