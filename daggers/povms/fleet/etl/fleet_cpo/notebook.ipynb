{"cells": [{"cell_type": "code", "execution_count": null, "id": "de49ef3d-df79-41ec-81dc-564419bac1cf", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "4c5cb065-6b48-4f0c-b764-f3e3a5804e36", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "import time\n", "from datetime import datetime, timedelta, date\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "75ce2cfc-f19d-4270-89a5-04f4d0bc235b", "metadata": {}, "outputs": [], "source": ["# # New table\n", "# trino_schema_name = \"supply_etls\"  # \"supply_etls\"  # \"povms\"\n", "# trino_table_name = \"fleet_master_data\"\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "6899f60d-a083-4383-a77a-a1e50e62b7ff", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6ce2acf3-87bf-4760-86a9-b15bca81cd38", "metadata": {}, "outputs": [], "source": ["cost_query = \"\"\"\n", "WITH base_cost AS (\n", "    SELECT\n", "        cms.trip_creation_date AS trip_date,\n", "        cms.consignment_id,\n", "        cms.trip_id,\n", "        cms.wh_facility_id,\n", "        cms.wh_outlet_id,\n", "        cms.wh_outlet_name,\n", "        cms.ds_outlet_id,\n", "        cms.ds_outlet_name,\n", "        cms.truck_number,\n", "        cms.truck_billing_type,\n", "        cms.destination_city,\n", "        ob_start_date,\n", "        md.trip_state,\n", "        travelled_dist,\n", "        (cms.total_km_travelled) / COUNT(*) OVER (PARTITION BY cms.trip_id) AS con_total_km,\n", "        dispatched_qty,\n", "        dispatched_qty_weight,\n", "        truck_cap_proportion,\n", "        cms.total_con_cost AS total_cms_cost,\n", "        cost_sheet,\n", "        COUNT(md.consignment_id) OVER(\n", "            PARTITION BY cms.trip_creation_date, cms.wh_facility_id, cms.ds_outlet_id\n", "        ) AS total_con,\n", "        cost_sheet / COUNT(md.consignment_id) OVER(\n", "            PARTITION BY cms.trip_creation_date, cms.wh_facility_id, cms.ds_outlet_id\n", "        ) AS total_cs_cost\n", "    FROM supply_etls.fleet_cms_cost cms\n", "    LEFT JOIN supply_etls.fleet_trips md\n", "           ON cms.consignment_id = md.consignment_id\n", "          AND md.trip_date >= CAST(date_trunc('week', current_date) - INTERVAL '35' DAY AS varchar)\n", "    LEFT JOIN supply_etls.fleet_master_data mas\n", "           ON cms.consignment_id = mas.consignment_id \n", "          AND mas.trip_date >= CAST(date_trunc('week', current_date) - INTERVAL '35' DAY AS date)\n", "    LEFT JOIN (\n", "        SELECT\n", "            trip_id,\n", "            SUM(travelled_distance_metres * 1.00 / 1000) AS travelled_dist\n", "        FROM transit_server.transit_travel_segment\n", "        WHERE insert_ds_ist >= CAST(current_date - INTERVAL '35' DAY AS varchar)\n", "        GROUP BY 1\n", "    ) tts\n", "           ON tts.trip_id = cms.trip_id\n", "    LEFT JOIN (\n", "        SELECT\n", "            date_,\n", "            source_facility_id,\n", "            destination_outlet_id,\n", "            SUM(date_cost) AS cost_sheet\n", "        FROM supply_etls.blinkit_middle_mile_fleet_cost\n", "        WHERE date_ >= date_trunc('week', current_date) - INTERVAL '35' DAY\n", "        GROUP BY 1,2,3\n", "    ) cs\n", "           ON cs.date_ = CAST(md.trip_date AS date)\n", "          AND cs.source_facility_id = md.wh_facility_id\n", "          AND cs.destination_outlet_id = md.ds_outlet_id\n", "    LEFT JOIN (\n", "        SELECT\n", "            outlet_id,\n", "            CAST(MIN(order_create_dt_ist) AS date) AS ob_start_date\n", "        FROM dwh.fact_sales_order_details\n", "        WHERE order_create_dt_ist >= (current_date - INTERVAL '35' DAY)\n", "        GROUP BY 1\n", "    ) cte\n", "           ON cte.outlet_id = md.ds_outlet_id\n", "    JOIN retail.console_outlet co\n", "           ON co.id = cms.ds_outlet_id\n", "          AND business_type_id = 7  -- to remove WH-WH transfers\n", "    WHERE trip_creation_date >= date_trunc('week', current_date) - INTERVAL '35' DAY\n", "      AND cms.wh_outlet_id NOT IN (3167, 4283)\n", "      AND cms.billable_truck_type NOT LIKE '%%Reefer%%'\n", "      AND cms.billable_truck_type NOT LIKE '%%Chiller%%'\n", "),\n", "\n", "billed_qty AS (\n", "    WITH p_type AS (\n", "        SELECT\n", "            item_id,\n", "            CASE\n", "                WHEN ids.outlet_type = 1 THEN 'FNV'\n", "                WHEN ids.perishable = 1 THEN 'Perishable'\n", "                ELSE 'Grocery'\n", "            END AS ptype\n", "        FROM rpc.item_details ids\n", "        GROUP BY 1,2\n", "    )\n", "    SELECT\n", "        frontend_outlet_id,\n", "        s.\"date\" AS dt,\n", "        EXTRACT(WEEK FROM \"date\") AS wk,\n", "        SUM(s.reserved_qty) AS created_qty,\n", "        SUM(p.billed_qty) AS billed_qty,\n", "        SUM(p.inward_qty) AS inward_qty\n", "    FROM (\n", "        SELECT\n", "            j.id AS sto_id,\n", "            j.outlet_id,\n", "            CAST(j.created_at AS date) AS \"date\",\n", "            j.created_by,\n", "            j.dispatch_time,\n", "            j.bulk_sto_id,\n", "            pt.ptype,\n", "            frontend_outlet_id,\n", "            SUM(reserved_quantity) AS reserved_qty,\n", "            COUNT(DISTINCT ii.item_id) AS line_item\n", "        FROM po.sto j\n", "        JOIN po.sto_items ii ON ii.sto_id = j.id\n", "        JOIN p_type pt ON pt.item_id = ii.item_id\n", "        JOIN vms.vms_vendor_city_mapping v\n", "             ON v.vendor_id = j.source_entity_vendor_id\n", "            AND v.active = 1\n", "        JOIN vms.vms_vendor_city_tax_info vt\n", "             ON vt.vendor_city_id = v.id\n", "            AND vt.active = 1\n", "            AND UPPER(vt.legal_name) NOT LIKE '%%BLINK%%'\n", "        WHERE j.created_at >= date_trunc('week', current_date) - INTERVAL '35' DAY\n", "        GROUP BY 1,2,3,4,5,6,7,8\n", "    ) s\n", "    JOIN retail.console_outlet c\n", "           ON c.id = s.outlet_id\n", "          AND c.active = 1\n", "          AND c.device_id <> 47\n", "          AND c.business_type_id IN (1, 12, 19, 20)\n", "    LEFT JOIN (\n", "        SELECT\n", "            pi.grofers_order_id,\n", "            pt.ptype,\n", "            SUM(pipd.quantity) AS billed_qty,\n", "            SUM(inw.inward_qty) AS inward_qty,\n", "            SUM(quantity * selling_price) AS sales\n", "        FROM pos.pos_invoice pi\n", "        JOIN pos.pos_invoice_product_details pipd ON pipd.invoice_id = pi.id\n", "        LEFT JOIN (\n", "            SELECT\n", "                merchant_invoice_id,\n", "                variant_id,\n", "                SUM(l1.\"delta\") AS inward_qty\n", "            FROM ims.ims_inventory_log l1\n", "            WHERE inventory_update_type_id IN (1, 28, 76, 90, 93)\n", "              AND created_at >= date_trunc('week', current_date) - INTERVAL '35' DAY\n", "              AND l1.insert_ds_ist >= CAST(date_trunc('week', current_date) - INTERVAL '35' DAY AS varchar)\n", "            GROUP BY 1,2\n", "        ) inw\n", "               ON inw.merchant_invoice_id = pi.invoice_id\n", "              AND inw.variant_id = pipd.variant_id\n", "        JOIN (\n", "            SELECT item_id, variant_id\n", "            FROM rpc.product_product\n", "            WHERE active = 1\n", "            GROUP BY 1,2\n", "        ) rpc\n", "               ON rpc.variant_id = pipd.variant_id\n", "        JOIN p_type pt\n", "               ON pt.item_id = rpc.item_id\n", "        WHERE pi.insert_ds_ist >= CAST(date_trunc('week', current_date) - INTERVAL '35' DAY AS varchar)\n", "          AND pipd.insert_ds_ist >= CAST(date_trunc('week', current_date) - INTERVAL '35' DAY AS varchar)\n", "          AND invoice_type_id IN (5, 14, 16)\n", "          AND grofers_order_id IS NOT NULL\n", "          AND grofers_order_id <> ''\n", "        GROUP BY 1,2\n", "    ) p\n", "           ON CAST(p.grofers_order_id AS varchar) = CAST(s.sto_id AS varchar)\n", "          AND s.ptype = p.ptype\n", "    JOIN crates.facility f ON f.id = c.facility_id\n", "    WHERE s.\"date\" >= date_trunc('week', current_date) - INTERVAL '35' DAY\n", "      AND s.ptype = 'Grocery'\n", "    GROUP BY 1,2,3\n", "),\n", "\n", "final_base AS (\n", "    SELECT\n", "        WEEK(trip_date) AS week_num,\n", "        trip_date,\n", "        trip_id,\n", "        consignment_id,\n", "        trip_state,\n", "        ds_outlet_id,\n", "        ds_outlet_name,\n", "        wh_outlet_id,\n", "        wh_outlet_name,\n", "        truck_number,\n", "        truck_billing_type,\n", "        ob_start_date,\n", "        total_cms_cost,\n", "        total_cs_cost,\n", "        COUNT(DISTINCT trip_id) AS total_trips,\n", "        SUM(dispatched_qty) AS total_indent,\n", "        SUM(total_cost) AS total_cost,\n", "        SUM(dispatched_qty_weight) AS total_indent_wt,\n", "        SUM(truck_cap_proportion *0.90) AS total_truck_cap,\n", "        SUM(travelled_dist) AS travelled_dist,\n", "        destination_city,\n", "        SUM(con_total_km) AS con_total_km\n", "    FROM (\n", "        SELECT\n", "            *,\n", "            CASE\n", "                WHEN wh_facility_id IN (2010, 555, 2469, 92, 2078, 264, 603)\n", "                     AND trip_date <= CAST('2024-12-15' AS date)\n", "                THEN COALESCE(total_cs_cost, total_cms_cost)\n", "\n", "                WHEN wh_facility_id IN (2946)\n", "                     AND trip_date <= CAST('2024-12-31' AS date)\n", "                THEN COALESCE(total_cs_cost, total_cms_cost)\n", "\n", "                WHEN trip_date >= CAST('2025-01-01' AS date)\n", "                THEN COALESCE(total_cms_cost, 0) + COALESCE(total_cs_cost, 0)\n", "                \n", "                ELSE COALESCE(total_cms_cost, total_cs_cost)\n", "            END AS total_cost\n", "        FROM base_cost\n", "    ) bc\n", "    GROUP BY\n", "        WEEK(trip_date),\n", "        trip_date,\n", "        trip_id,\n", "        consignment_id,\n", "        trip_state,\n", "        ds_outlet_id,\n", "        ds_outlet_name,\n", "        wh_outlet_id,\n", "        wh_outlet_name,\n", "        truck_number,\n", "        truck_billing_type,\n", "        ob_start_date,\n", "        total_cms_cost,\n", "        total_cs_cost,\n", "        destination_city\n", ")\n", "\n", "SELECT distinct\n", "    fb.week_num,\n", "    fb.trip_date,\n", "    fb.trip_id,\n", "    fb.consignment_id,\n", "    fb.trip_state,\n", "    fb.truck_billing_type,\n", "    fb.ds_outlet_id,\n", "    fb.ds_outlet_name,\n", "    fb.ob_start_date,\n", "    fb.total_cms_cost,\n", "    fb.total_cs_cost,\n", "    fb.total_trips,\n", "    fb.total_indent,\n", "    fb.total_cost,\n", "    fb.total_indent_wt,\n", "    fb.total_truck_cap,\n", "    fb.con_total_km,\n", "    bq.created_qty,\n", "    bq.billed_qty,\n", "    CASE\n", "        WHEN fb.total_cost = fb.total_cs_cost THEN 'CS'\n", "        WHEN fb.total_cost = fb.total_cms_cost THEN 'CMS'\n", "        WHEN fb.total_cost = fb.total_cs_cost + fb.total_cms_cost THEN 'CMS'\n", "        ELSE NULL\n", "    END AS cost_flag,\n", "    fb.wh_outlet_id,\n", "    fb.wh_outlet_name,\n", "    fb.truck_number,\n", "    fb.travelled_dist,\n", "    fb.destination_city,\n", "    city_bucket\n", "FROM final_base fb\n", "LEFT JOIN billed_qty bq\n", "ON bq.dt = fb.trip_date\n", "      AND bq.frontend_outlet_id = fb.ds_outlet_id\n", "LEFT JOIN supply_etls.fleet_city_desc cd on cd.trip_id = fb.trip_id and cd.ds_outlet_id = fb.ds_outlet_id and trip_creation_date >= current_date - interval '35' day\n", "       \n", "-- WHERE fb.wh_outlet_name IN (\n", "--     'Nagpur N1 - Feeder',\n", "--     'MODI <PERSON> - Feeder',\n", "--     'Hyderabad H3 - Feeder',\n", "--     '<PERSON><PERSON><PERSON><PERSON> - Feeder',\n", "--     'Super Store Hyderabad H2 (MODI)',\n", "--     'Ranchi R1 - Feeder',\n", "--     'Lucknow L5 - Feeder',\n", "--     'Super Store Jaipur J2 (SSC)',\n", "--     'Mumbai M9 Ecom - Feeder',\n", "--     '<PERSON><PERSON> - Feeder',\n", "--     '<PERSON><PERSON><PERSON> - Feeder',\n", "--     'MODI Bengaluru B3 - Feeder',\n", "--     'Bhubaneswar B1 - Feeder',\n", "--     '<PERSON><PERSON> D3 - Fe<PERSON><PERSON>',\n", "--     '<PERSON><PERSON> P1 - Feeder',\n", "--     'SSC <PERSON>agar - SR Feeder',\n", "--     'Goa - Feeder',\n", "--     'Super Store Dasna 2 (SSC)',\n", "--     'MODI Pune P2 - Feeder',\n", "--     --'SS Jaipur PC (SSC)',\n", "--     'Ahmedabad A2 - Feeder',\n", "--     'Visakhapatnam V1 - Feeder',\n", "--     '<PERSON><PERSON><PERSON> V1 - Feeder',\n", "--     'Super Store Gurgaon G7 Ecom (SSC)',\n", "--     'Bengaluru B4 - Feeder',\n", "--     'Surat S1 - Feeder',\n", "--     'Farukhnagar F2 - Feeder',\n", "--     'Noida N1 - Feeder',\n", "--     '<PERSON><PERSON><PERSON> L2 - Feeder',\n", "--     'Coimbatore C1 - Feeder',\n", "--     'Rajpura - Feeder',\n", "--     'Kolkata K4 - Feeder',\n", "--     'Mumbai M10 - Feeder',\n", "--     'Chennai C4 - Feeder',\n", "--     'Super Store Lucknow L4 (SSC)'\n", "-- )\n", "\n", "\n", "\n", "-- select * from supply_etls.fleet_cms_cost where trip_creation_date>=cast(current_date - interval '2' day as date)\n", "\n", " \"\"\"\n", "cost_df = read_sql_query(cost_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "389e3ba5-d84d-4dfa-9e9f-0da781628463", "metadata": {}, "outputs": [], "source": ["cost_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9de6cad5-c715-4088-a636-3ad5fabb176b", "metadata": {}, "outputs": [], "source": ["cost_df.consignment_id.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ed535630-2f1a-4e2e-affe-64adc3f3a460", "metadata": {}, "outputs": [], "source": ["cost_df[cost_df[\"consignment_id\"] == 2385095]"]}, {"cell_type": "code", "execution_count": null, "id": "bf108741-aead-486d-851e-f75b7d9f8ade", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4e54f904-4b7f-4a47-9097-cb66279a7bdf", "metadata": {}, "outputs": [], "source": ["carts_query = \"\"\"\n", "with\n", "outlet_details as\n", "    (select * from supply_etls.outlet_details),\n", "\n", "item_details as\n", "    (select * from supply_etls.item_details),\n", "\n", "raw_data as\n", "    (select * from supply_etls.date_hourly_sales_details),\n", "\n", "final_raw_data as\n", "    (\n", "    select * from raw_data\n", "    where\n", "    insert_ds_ist >= cast(current_date - interval '45' day as varchar)\n", "    ),\n", "\n", "adding_item_details as\n", "    (\n", "    select\n", "        date(frd.insert_ds_ist) as date_,\n", "        cart_id,\n", "        frd.outlet_id,\n", "        frd.item_id,\n", "        id.assortment_type,\n", "        frd.sales_quantity,\n", "        frd.sales_value,\n", "        frd.sales_weighted_landing_price,\n", "        frd.sales_mrp\n", "\n", "    from final_raw_data frd\n", "    join\n", "    item_details id on id.item_id = frd.item_id\n", "    ),\n", "\n", "cart_details as\n", "    (\n", "    select\n", "        date_,\n", "        outlet_id,\n", "        count(distinct cart_id) as carts,\n", "        sum(sales_quantity) as sales_quantity,\n", "        sum(sales_value) as sales_value\n", "\n", "    from adding_item_details\n", "    group by 1,2\n", "\n", "    )\n", "\n", "    select distinct\n", "        cd.date_,\n", "        city_name,\n", "        cd.outlet_id,\n", "        od.hot_outlet_name as outlet_name,\n", "        cd.carts,\n", "        rank() over (partition by cd.outlet_id order by cd.date_) as rnk,\n", "        min(cd.date_)  over (partition by cd.outlet_id order by cd.date_) as rnk1\n", "        from adding_item_details aid\n", "        join\n", "        cart_details cd on cd.date_ = aid.date_ and cd.outlet_id = aid.outlet_id\n", "        join outlet_details od on od.hot_outlet_id = cd.outlet_id\n", "\n", "        ORDER BY 1 ASC\n", "\n", "\"\"\"\n", "\n", "carts_df = read_sql_query(carts_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "5b50e9b2-feb1-486d-a4b5-34b4fc3e4519", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "321038ae-f8d0-411d-b581-ecff761e4770", "metadata": {}, "outputs": [], "source": ["carts_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f277f8ca-dccc-4c0a-83e0-4045c911ac9f", "metadata": {}, "outputs": [], "source": ["carts_df[carts_df[\"rnk\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "72fc0c4d-41de-4009-98cb-7bc80ac69258", "metadata": {}, "outputs": [], "source": ["# carts_df[carts_df['outlet_id']==2987]"]}, {"cell_type": "code", "execution_count": null, "id": "ba4cf847-6a08-415a-8534-646d76812209", "metadata": {}, "outputs": [], "source": ["cost_n_carts = cost_df.merge(\n", "    carts_df, how=\"left\", left_on=[\"trip_date\", \"ds_outlet_id\"], right_on=[\"date_\", \"outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "af331b8a-1e4f-4901-90d8-b8e2349a5221", "metadata": {}, "outputs": [], "source": ["cost_n_carts.shape"]}, {"cell_type": "code", "execution_count": null, "id": "34e5d353-5cce-44fe-b208-c8648b332f89", "metadata": {}, "outputs": [], "source": ["cost_n_carts.drop(columns={\"outlet_name\", \"city_name\", \"outlet_id\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f1cdca6e-cba3-4711-a3cf-9624640f0a54", "metadata": {}, "outputs": [], "source": ["cost_n_carts.head()"]}, {"cell_type": "code", "execution_count": null, "id": "15e67e8c-0aa5-4ed4-9b4c-692eb509ff6b", "metadata": {}, "outputs": [], "source": ["cost_n_carts[\"trip_date\"] = pd.to_datetime(cost_n_carts[\"trip_date\"])\n", "cost_n_carts[\"rnk1\"] = pd.to_datetime(cost_n_carts[\"rnk1\"])"]}, {"cell_type": "code", "execution_count": null, "id": "36aff166-e06e-4d46-8e15-0370d7bef6c9", "metadata": {}, "outputs": [], "source": ["cost_n_carts = sqldf(\n", "    \"\"\"SELECT *,\n", "       (carts * total_indent_wt) / SUM(total_indent_wt) OVER (PARTITION BY trip_date, ds_outlet_id) AS carts1,\n", "       CASE\n", "           WHEN (julianday(trip_date) - julianday(rnk1)) > 7 THEN \"Old Store\"\n", "           ELSE \"New Store\"\n", "       END AS store_type\n", "       \n", "       \n", "FROM cost_n_carts\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d30ce12-f7fb-4879-9aa7-9cff3108f3bf", "metadata": {}, "outputs": [], "source": ["cost_n_carts.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3d196e3c-a87b-472c-81c9-8b3dbc3e29ed", "metadata": {}, "outputs": [], "source": ["cost_n_carts[cost_n_carts[\"trip_id\"] == \"18198_1402218\"]"]}, {"cell_type": "code", "execution_count": null, "id": "fe60ae91-3535-46c3-842c-69bc9feb1a30", "metadata": {}, "outputs": [], "source": ["cost_n_carts[\n", "    (cost_n_carts[\"ds_outlet_id\"] == 4908)\n", "    & (cost_n_carts[\"trip_date\"] == pd.to_datetime(\"2025-02-22\").date())\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "eeaac047-1466-4942-b24f-b18553ed841e", "metadata": {}, "outputs": [], "source": ["cost_n_carts.store_type.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "479547d2-94a9-4177-88f1-9c78b43b95f0", "metadata": {}, "outputs": [], "source": ["cost_n_carts[\"trip_date\"] = pd.to_datetime(cost_n_carts[\"trip_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "3b0326d9-4dc9-4fe5-90da-cff214b0f4b4", "metadata": {}, "outputs": [], "source": ["cost_n_carts.head()"]}, {"cell_type": "code", "execution_count": null, "id": "826a7bbb-34d8-48ba-898c-a479b6dfbe24", "metadata": {}, "outputs": [], "source": ["cost_n_carts1 = sqldf(\n", "    \"\"\"\n", "SELECT *, \n", "       COUNT(consignment_id) OVER (PARTITION BY trip_date, ds_outlet_id) AS store_days, \n", "       trip_date || '-' || ds_outlet_id AS store_data,\n", "       case when count(*) over (partition by trip_id) > 1 then \"Milk Run\" else \"Non-Milk Run\" end as trip_type\n", "FROM cost_n_carts\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "365c0b52-8d45-480f-bce8-f4790e7d4126", "metadata": {}, "outputs": [], "source": ["cost_n_carts1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e8a68c47-a446-4c5f-8fae-ab4a3db3612e", "metadata": {}, "outputs": [], "source": ["cost_n_carts1[cost_n_carts1[\"trip_id\"] == \"18198_1402218\"]"]}, {"cell_type": "code", "execution_count": null, "id": "05a0b5b9-de8a-4d34-baa3-874a0897a774", "metadata": {}, "outputs": [], "source": ["dist_q = \"\"\"\n", "select  --*\n", "        install_ts,\n", "        update_ts,\n", "        lm1.outlet_id as source_outlet_id,\n", "        lm1.name as source_name,\n", "        lm2.outlet_id as destination_outlet_id,\n", "        lm2.name as destination_name,\n", "        system_distance_in_km,\n", "        team_recommended_distance_in_km,\n", "        tenure,\n", "        is_active,\n", "        status,\n", "        dm.updated_by,\n", "        approved_by\n", "from fleet_management.fleet_management_distance_mapping dm\n", "left join retail.console_outlet_logistic_mapping lm1 on lm1.logistic_node_id = cast(dm.source_node_id as int)\n", "left join retail.console_outlet_logistic_mapping lm2 on lm2.logistic_node_id = cast(dm.destination_node_id as int)\n", "where install_ts >= cast((current_date - interval '365' day) as date)\n", "\n", "\n", "\"\"\"\n", "dist_df = read_sql_query(dist_q, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "c1985d58-a331-4678-9fb6-2829957329e9", "metadata": {}, "outputs": [], "source": ["dist_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e0b7e688-df0e-4760-90be-a3841cec4435", "metadata": {}, "outputs": [], "source": ["cost_n_carts2 = sqldf(\n", "    \"\"\"\n", "with cte as(\n", "select c1.* ,team_recommended_distance_in_km\n", "from\n", "cost_n_carts1 c1\n", "left join dist_df dd on dd.source_outlet_id = c1.wh_outlet_id and dd.destination_outlet_id = ds_outlet_id\n", "),\n", "cte2 as(\n", "SELECT *, \n", "    CASE \n", "        WHEN con_total_km IS NOT NULL THEN con_total_km \n", "        WHEN trip_type = 'Milk Run' THEN \n", "            CASE \n", "                WHEN travelled_dist IS NOT NULL THEN \n", "                    travelled_dist * team_recommended_distance_in_km / \n", "                    SUM(team_recommended_distance_in_km) OVER (PARTITION BY trip_id) \n", "                ELSE \n", "                    team_recommended_distance_in_km * 2 * team_recommended_distance_in_km / \n", "                    SUM(team_recommended_distance_in_km) OVER (PARTITION BY trip_id)  \n", "            END \n", "        WHEN travelled_dist IS NOT NULL THEN travelled_dist \n", "        ELSE team_recommended_distance_in_km \n", "    END AS distance_final\n", "FROM cte\n", ")\n", "select\n", "*, distance_final * sum(total_cost) over (partition by trip_date,truck_number) / sum(distance_final) over (partition by trip_date,truck_number) as cost_per_dist\n", "from cte2\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6530fb04-5670-46ce-addc-374070e04e13", "metadata": {}, "outputs": [], "source": ["cost_n_carts2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dc9bae4f-d239-419b-83dd-61251585bba6", "metadata": {}, "outputs": [], "source": ["cost_n_carts2[cost_n_carts2[\"trip_id\"] == \"25055_1368633\"]"]}, {"cell_type": "code", "execution_count": null, "id": "91f3c542-8902-46d8-a615-1b25a72c1739", "metadata": {}, "outputs": [], "source": ["cost_n_carts2.drop(columns={\"rnk\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4a61a591-647b-4ba6-a5e0-8feb48b4fb69", "metadata": {}, "outputs": [], "source": ["cost_n_carts2.rename(columns={\"rnk1\": \"cart_date\", \"store_data\": \"store_date\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "463e2b7f-0792-45ff-81e4-0ead02a5b80b", "metadata": {}, "outputs": [], "source": ["cost_n_carts2[\"cart_date\"] = pd.to_datetime(cost_n_carts2[\"cart_date\"]).dt.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "b214aeaa-0204-4c6c-9d96-e093bb9979d2", "metadata": {}, "outputs": [], "source": ["# Convert to appropriate types while handling NA values\n", "cost_n_carts2[\"week_num\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"week_num\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"consignment_id\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"consignment_id\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"ds_outlet_id\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"ds_outlet_id\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"total_cms_cost\"] = pd.to_numeric(cost_n_carts2[\"total_cms_cost\"], errors=\"coerce\")\n", "cost_n_carts2[\"total_cs_cost\"] = pd.to_numeric(cost_n_carts2[\"total_cs_cost\"], errors=\"coerce\")\n", "\n", "cost_n_carts2[\"total_trips\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"total_trips\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"total_indent\"] = pd.to_numeric(cost_n_carts2[\"total_indent\"], errors=\"coerce\")\n", "cost_n_carts2[\"total_cost\"] = pd.to_numeric(cost_n_carts2[\"total_cost\"], errors=\"coerce\")\n", "cost_n_carts2[\"total_indent_wt\"] = pd.to_numeric(cost_n_carts2[\"total_indent_wt\"], errors=\"coerce\")\n", "cost_n_carts2[\"total_truck_cap\"] = pd.to_numeric(cost_n_carts2[\"total_truck_cap\"], errors=\"coerce\")\n", "cost_n_carts2[\"con_total_km\"] = pd.to_numeric(cost_n_carts2[\"con_total_km\"], errors=\"coerce\")\n", "cost_n_carts2[\"created_qty\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"created_qty\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"billed_qty\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"billed_qty\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"wh_outlet_id\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"wh_outlet_id\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"travelled_dist\"] = pd.to_numeric(cost_n_carts2[\"travelled_dist\"], errors=\"coerce\")\n", "cost_n_carts2[\"carts\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"carts\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"carts1\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"carts1\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"store_date\"] = (\n", "    pd.to_numeric(cost_n_carts2[\"store_days\"], errors=\"coerce\").fillna(0).astype(int)\n", ")\n", "cost_n_carts2[\"team_recommended_distance_in_km\"] = pd.to_numeric(\n", "    cost_n_carts2[\"team_recommended_distance_in_km\"], errors=\"coerce\"\n", ")\n", "cost_n_carts2[\"distance_final\"] = pd.to_numeric(cost_n_carts2[\"distance_final\"], errors=\"coerce\")\n", "\n", "cost_n_carts2[\"cost_per_dist\"] = pd.to_numeric(cost_n_carts2[\"cost_per_dist\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "ff0542a4-b11f-4b2a-ba55-d0ab695806fd", "metadata": {}, "outputs": [], "source": ["def kwarg(df):\n", "    result = []\n", "    for col in df.columns:\n", "        dtype = str(df[col].dtype)\n", "\n", "        # Handle different dtype cases\n", "        if dtype == \"int64\":\n", "            dtype = \"integer\"\n", "        elif dtype == \"float64\":\n", "            dtype = \"REAL\"\n", "        elif dtype == \"object\":\n", "            dtype = \"varchar\"\n", "        elif dtype == \"datetime64[ns]\":\n", "            dtype = \"varchar\"  # Default to varchar for datetime64[ns] type\n", "        elif col == \"trip_creation_date\":  # Explicit check for the 'trip_creation_date' column\n", "            dtype = \"TIMESTAMP(6)\"  # Override with TIMESTAMP(6) for this specific column\n", "\n", "        # Create the metadata dictionary for the column\n", "        s = {\"name\": col, \"type\": dtype, \"description\": \"nothing\"}\n", "\n", "        # Append the column metadata to the result\n", "        result.append(s)\n", "\n", "    return result\n", "\n", "\n", "metadata = kwarg(cost_n_carts2)"]}, {"cell_type": "code", "execution_count": null, "id": "de055805-cf04-4bb3-8f93-5cc030e5b1b1", "metadata": {}, "outputs": [], "source": ["metadata"]}, {"cell_type": "code", "execution_count": null, "id": "aae774af-75c2-4de5-9ee1-747661482f8e", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"fleet_cpo\",\n", "    \"column_dtypes\": metadata,\n", "    \"primary_key\": [\"consignment_id\", \"trip_id\"],\n", "    \"partition_key\": [\"trip_date\"],\n", "    \"load_type\": \"partition_overwrite\",  # options: append, rebuild, truncate, or upsert\n", "    \"table_description\": \"fleet_cpo\",\n", "}\n", "\n", "\n", "# INSERTING DATA INTO THE DATABASE\n", "print(\"pushing to trino\")\n", "pb.to_trino(data_obj=cost_n_carts2, **kwargs)\n", "print(\"Complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "bd2ff8c3-4e17-4cd1-8135-91d35bfa76a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8f4e3bc2-480a-45c6-97cd-1da7f0f4f64b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0d4a4f7c-f515-435c-a78f-c89900f880b6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fd703e31-852b-4636-b875-f828c394d537", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b0ca0217-5fff-4fbc-ad91-f8eaf9ca963b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ae4cf68d-4d53-45f7-b9f1-30871c0fc264", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c17aa347-c3e1-4b91-b8d3-906527ca0b99", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "00ccdd8f-4efb-41a1-b88c-1773a57c9344", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "128d9383-55ce-42bd-86bf-71e176a93744", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}