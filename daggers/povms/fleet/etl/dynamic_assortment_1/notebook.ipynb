{"cells": [{"cell_type": "code", "execution_count": null, "id": "a30e7df4-24df-48dd-984f-5ecd1744f51d", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# import matplotlib.pyplot as plt\n", "\n", "# from pencilbox.io.sheets import gspread_client\n", "from datetime import datetime\n", "from pytz import timezone\n", "import boto3\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "cb36751e-c0a0-4716-a009-096720bca64e", "metadata": {}, "outputs": [], "source": ["!pip install pymysql"]}, {"cell_type": "code", "execution_count": null, "id": "d803c713-4bee-4557-a813-ae9b5d0192b0", "metadata": {}, "outputs": [], "source": ["import pymysql"]}, {"cell_type": "code", "execution_count": null, "id": "92da68d9-e417-45a7-a27b-042b624bd28f", "metadata": {}, "outputs": [], "source": ["!pip install time-python"]}, {"cell_type": "code", "execution_count": null, "id": "0a89247b-050e-402f-9be7-73a3ca7033d4", "metadata": {}, "outputs": [], "source": ["import time"]}, {"cell_type": "code", "execution_count": null, "id": "9c381984-96c6-4486-b89e-329180b09dcc", "metadata": {}, "outputs": [], "source": ["!pip install git+https://github.com/grofers/funk-svd"]}, {"cell_type": "code", "execution_count": null, "id": "3b3e0c3d-1c9c-41b2-b8d5-d3885ac16f7d", "metadata": {}, "outputs": [], "source": ["from funk_svd import SVD\n", "from sklearn.metrics import mean_absolute_error"]}, {"cell_type": "code", "execution_count": null, "id": "53a477ac-357b-4dba-877a-88137d70ebfe", "metadata": {}, "outputs": [], "source": ["def fetch_city_assortment(city_id):\n", "    sql = \"\"\"\n", "    select item_id, master_assortment_substate_id as state from rpc.product_city_assortment_suggestion where active = true and \n", "    master_assortment_substate_id in (1,3) and city_id in {x}\n", "    group by 1,2\n", "    \"\"\".format(\n", "        x=city_id\n", "    )\n", "\n", "    rpc_secret = pb.get_secret(\"retail/noto-reports/mysql/rpc-rds-read\")\n", "    host = rpc_secret.get(\"host\")\n", "    username = rpc_secret.get(\"username\")\n", "    password = rpc_secret.get(\"password\")\n", "    CON_RPC = pymysql.connect(host=host, user=username, password=password)\n", "    return pd.read_sql_query(sql=sql, con=CON_RPC)\n", "\n", "\n", "def fetch_temp_inact_with_low_inv(city_id):\n", "    sql = \"\"\"\n", "    WITH fo_be AS\n", "  (SELECT facility_id,\n", "          outlet_id,\n", "          cloud_store_id,outlet_name,\n", "          city_id\n", "   FROM lake_po.physical_facility_outlet_mapping x\n", "   INNER JOIN lake_retail.warehouse_outlet_mapping y ON x.outlet_id = y.warehouse_id\n", "   AND x.active\n", "   AND y.active\n", "   AND ars_active=1\n", "   AND city_id in {x}),\n", "   \n", "     fo_fe AS\n", "  (SELECT facility_id,\n", "          outlet_id,\n", "          city_id\n", "   FROM lake_po.physical_facility_outlet_mapping\n", "   WHERE active=1\n", "     AND ars_active=1\n", "     AND facility_id IN\n", "       (SELECT facility_id\n", "        FROM lake_retail.console_outlet\n", "        WHERE business_type_id = 7)\n", "     AND facility_id NOT IN (1028,\n", "                             1182)\n", "     AND facility_id IN\n", "       (SELECT facility_id\n", "        FROM dwh.dim_merchant_outlet_facility_mapping\n", "        WHERE is_current\n", "          AND is_mapping_enabled)\n", "     AND city_id in {x}),\n", "     inv AS\n", "  (SELECT item_id,\n", "          sum(quantity) AS be_samaan\n", "   FROM lake_ims.ims_item_inventory\n", "   WHERE active\n", "     AND outlet_id IN\n", "       (SELECT cloud_store_id\n", "        FROM fo_be)\n", "     AND item_id IN\n", "       (SELECT item_id\n", "        FROM lake_rpc.product_city_assortment_suggestion\n", "        WHERE city_id in {x}\n", "          AND active\n", "          AND master_assortment_substate_id = 3)\n", "   GROUP BY 1) \n", "SELECT item_id\n", "     FROM\n", "       (SELECT item_id,\n", "               be_samaan,\n", "               1.0*be_samaan/\n", "          (SELECT count(DISTINCT facility_id)\n", "           FROM fo_fe) AS x\n", "        FROM inv)\n", "     WHERE x < 2\n", "     and item_id not in (select distinct item_id from lake_rpc.item_category_details where l0_id = 1487)\n", "     and item_id not in (select distinct item_id from lake_rpc.product_product where perishable = 1 and active = 1\n", "        and item_id in (select distinct item_id from lake_rpc.item_category_details where l2_id in (31,198,1097,1956,949,950,1389,1395,1778,1425,138,1091,1093,1094,1185)))\n", "    \"\"\".format(\n", "        x=city_id\n", "    )\n", "\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "# def fetch_city_sales(city_id, lookback_days, sku):\n", "#     sql = \"\"\"\n", "#     DROP TABLE IF EXISTS fo;\n", "#      create temporary table fo as (select facility_id, outlet_id, city_id from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "#         and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "#         and outlet_id in (select distinct outlet_id from dwh.fact_sales_order_details where cart_checkout_ts_ist > current_date-15)\n", "#         and city_id in ({c})\n", "#     );\n", "\n", "#     DROP TABLE IF EXISTS rpc;\n", "#     create temporary table rpc as (\n", "#         select facility_id, item_id, order_date, actual_quantity from consumer.rpc_daily_availability\n", "#         where order_date between (current_date - {n_days} || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp\n", "#         and item_id in {sku} and facility_id in (select facility_id from fo)\n", "#     );\n", "\n", "#     DROP TABLE IF EXISTS rpc_2;\n", "#     create temporary table rpc_2 as (\n", "#         select facility_id, item_id, date(order_date) as dt, count(order_date)as n_order_date from rpc\n", "#         where actual_quantity > 0 and item_id in {sku} and facility_id in (select facility_id from fo)\n", "#         group by 1,2,3\n", "#     );\n", "\n", "#     with temp_non_perish as (select dt, facility_id, item_id from rpc_2 where n_order_date > 6),\n", "\n", "#     fnv_rpc as (\n", "#             select facility_id, item_id, date(updated_at) as dt, count(updated_at) as n_order_date from metrics.fnv_hourly_details\n", "#             where current_inv > 0 and (updated_at between (current_date - {n_days}-60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "#                     and item_id in {sku}\n", "#                     and facility_id in (select facility_id from fo)\n", "#             group by 1,2,3\n", "#     ),\n", "\n", "#     temp_fnv as (select dt, facility_id, item_id from fnv_rpc where n_order_date > 6),\n", "\n", "#     perish_rpc as (\n", "#             select facility_id, item_id, date(updated_at) as dt, count(updated_at)as n_order_date from metrics.perishable_hourly_details_v2\n", "#             where current_inv > 0 and is_perishable = 1 and (updated_at between (current_date - {n_days} || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "#                     and item_id in {sku}\n", "#                     and facility_id in (select facility_id from fo)\n", "#             group by 1,2,3\n", "#     ),\n", "\n", "#     temp_perish as (select dt, facility_id, item_id from perish_rpc where n_order_date > 6),\n", "\n", "#     avail_days_count as (\n", "#         select *, 'non_perish' as category from temp_non_perish\n", "#         where item_id not in (select distinct item_id from temp_fnv) and item_id not in (select distinct item_id from temp_perish)\n", "#         union\n", "#         select *, 'perish' as category from temp_perish\n", "#         union\n", "#         select *, 'fnv' as category from temp_fnv\n", "#         ),\n", "\n", "#     availability as (\n", "#     select avail_days_count.*, outlet_id, city_id from avail_days_count inner join fo on avail_days_count.facility_id = fo.facility_id\n", "#     ),\n", "\n", "#     cat_i as (select x.item_id, l0, l1, l2, storage_type, weight_in_gm as weight, shelf_life, greatest(1,shelf_life::int-outward_guidelines::int) as saleable_shelf_life\n", "#     from lake_rpc.product_product x\n", "#     inner join (select item_id as i, max(id) as id from lake_rpc.product_product where active=1\n", "#                     and weight_in_gm*shelf_life*outward_guidelines is not null group by 1) y on x.item_id = y.i and x.id=y.id and active = 1\n", "#     inner join lake_rpc.item_category_details z on x.item_id = z.item_id ),\n", "\n", "#     cat_p as (select item_id, y.product_id, multiplier, avg_selling_price_ratio, min(case when is_combo = False then product_name end) as item_name,\n", "#                 max(case when is_current = 1 and l0_category != 'Specials' and is_combo = False then product_type end) as product_type from dwh.dim_product x\n", "#         inner join dwh.dim_item_product_offer_mapping y on x.product_id = y.product_id\n", "#         group by 1,2,3,4),\n", "\n", "#     cat as (select distinct cat_i.item_id, product_id, multiplier, avg_selling_price_ratio, item_name, product_type, l0, l1, l2, storage_type, weight,\n", "#         shelf_life, saleable_shelf_life from cat_i\n", "#         inner join cat_p on cat_i.item_id = cat_p.item_id),\n", "\n", "#     avail_cat as (select dt,facility_id, outlet_id, city_id, category, cat.* from cat inner join availability on cat.item_id = availability.item_id),\n", "\n", "#     order_sales as (\n", "#         select date(cart_checkout_ts_ist) as dt, product_id, outlet_id, sum(product_quantity) as quantity,\n", "#         sum(total_selling_price) as gmv, sum(total_retained_margin) as margin\n", "#         from dwh.fact_sales_order_item_details\n", "#         where cart_checkout_ts_ist between (current_date - {n_days} || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp\n", "#         and order_current_status = 'DELIVERED' and outlet_id in (select outlet_id from fo)\n", "#         group by 1,2,3\n", "#     ),\n", "#     temp as (\n", "#     select x.dt, x.city_id, facility_id, item_id, x.product_id, multiplier, avg_selling_price_ratio, quantity, gmv, margin, product_type, item_name, l0, l1, l2, category, storage_type, weight,\n", "#             shelf_life, saleable_shelf_life from avail_cat x left join order_sales y on x.dt = y.dt and x.outlet_id = y.outlet_id and x.product_id = y.product_id\n", "#             ),\n", "#     temp_ts as(\n", "#     select dt, city_id, facility_id, item_id, row_number() over(partition by facility_id, item_id order by dt) as rn, sum(quantity*multiplier) as quantity,\n", "#             sum(gmv*avg_selling_price_ratio) as gmv, sum(margin*avg_selling_price_ratio) as margin,\n", "#             min(case when item_name is not null then item_name end) as item_name,\n", "#             max(case when product_type is not null then product_type end) as product_type, max(l0) as l0, max(l1) as l1,\n", "#             max(l2) as l2, max(category) as category, max(storage_type) as storage_type, max(weight) as weight,\n", "#             min(shelf_life) as shelf_life, min(saleable_shelf_life) as saleable_shelf_life\n", "#             from temp\n", "#             group by 1,2,3,4)\n", "#     select case when city_id = 70 then 6 else city_id end as city_id, facility_id, item_id,\n", "#             1.00*sum((rn+200)*(case when quantity is NULL then 0 else quantity end)) / sum(rn+200) as quantity,\n", "#             1.00*sum((rn+200)*(case when gmv is NULL then 0 else gmv end)) / sum(rn+200) as gmv,\n", "#             1.00*sum((rn+200)*(case when margin is NULL then 0 else margin end)) / sum(rn+200) as margin,\n", "#             stddev(case when quantity is NULL then 0 else 1.00*quantity end) as quant_std, count(dt) as available_days_count,\n", "#             min(item_name) as item_name, max(product_type) as product_type, max(l0) as l0, max(l1) as l1,\n", "#             max(l2) as l2, max(category) as category, max(storage_type) as storage_type, max(weight) as weight,\n", "#             min(shelf_life) as shelf_life, min(saleable_shelf_life) as saleable_shelf_life\n", "#             from temp_ts\n", "#             group by 1,2,3\n", "\n", "#     \"\"\".format(c = city_id, n_days = lookback_days, sku=sku)\n", "#     con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "#     return(pd.read_sql_query(sql=sql, con=con))\n", "\n", "\n", "def fetch_city_sales(city_id, lookback_days, sku):\n", "    sql = \"\"\" \n", "    DROP TABLE IF EXISTS fo;\n", "     create temporary table fo as (select facility_id, outlet_id, city_id from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "        and outlet_id in (select distinct outlet_id from dwh.fact_sales_order_details where cart_checkout_ts_ist > current_date-15)\n", "        and city_id in ({c})\n", "    );\n", "    \n", "    DROP TABLE IF EXISTS rpc;\n", "    create temporary table rpc as (\n", "        select facility_id, item_id, order_date, actual_quantity from consumer.rpc_daily_availability\n", "        where order_date between (current_date - {n_days} || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp\n", "        and item_id in {sku} and facility_id in (select facility_id from fo)\n", "    );\n", "    \n", "    DROP TABLE IF EXISTS rpc_2;\n", "    create temporary table rpc_2 as (\n", "        select facility_id, item_id, date(order_date) as dt, count(order_date)as n_order_date from rpc\n", "        where actual_quantity > 0 and item_id in {sku} and facility_id in (select facility_id from fo)\n", "        group by 1,2,3\n", "    );\n", "    \n", "    with temp_non_perish as (select dt, facility_id, item_id from rpc_2 where n_order_date > 6),\n", "    \n", "    cat_ as (select item_id, case when item_id in (select item_id from lake_rpc.item_category_details where l0_id = 1487)\n", "    then 'fnv' else case when item_id in (select item_id from lake_rpc.product_product where active =1 and perishable = 1)\n", "    then 'perish' else 'non_perish' end end as category from lake_rpc.item_category_details group by 1,2),\n", "        \n", "    avail_days_count as (\n", "        select temp_non_perish.*, category from temp_non_perish inner join cat_ on temp_non_perish.item_id = cat_.item_id\n", "        ),\n", "\n", "    availability as (\n", "    select avail_days_count.*, outlet_id, city_id from avail_days_count inner join fo on avail_days_count.facility_id = fo.facility_id\n", "    ),\n", "\n", "    cat_i as (select x.item_id, l0, l1, l2, storage_type, weight_in_gm as weight, shelf_life, greatest(1,shelf_life::int-outward_guidelines::int) as saleable_shelf_life \n", "    from lake_rpc.product_product x\n", "    inner join (select item_id as i, max(id) as id from lake_rpc.product_product where active=1 \n", "                    and weight_in_gm*shelf_life*outward_guidelines is not null group by 1) y on x.item_id = y.i and x.id=y.id and active = 1\n", "    inner join lake_rpc.item_category_details z on x.item_id = z.item_id ),\n", "\n", "    cat_p as (select item_id, y.product_id, multiplier, avg_selling_price_ratio, min(case when is_combo = False then product_name end) as item_name, \n", "                max(case when is_current = 1 and l0_category != 'Specials' and is_combo = False then product_type end) as product_type from dwh.dim_product x\n", "        inner join dwh.dim_item_product_offer_mapping y on x.product_id = y.product_id\n", "        group by 1,2,3,4),\n", "\n", "    cat as (select distinct cat_i.item_id, product_id, multiplier, avg_selling_price_ratio, item_name, product_type, l0, l1, l2, storage_type, weight,\n", "        shelf_life, saleable_shelf_life from cat_i \n", "        inner join cat_p on cat_i.item_id = cat_p.item_id),\n", "\n", "    avail_cat as (select dt,facility_id, outlet_id, city_id, category, cat.* from cat inner join availability on cat.item_id = availability.item_id),\n", "\n", "    order_sales as (\n", "        select date(cart_checkout_ts_ist) as dt, product_id, outlet_id, sum(product_quantity) as quantity, \n", "        sum(total_selling_price) as gmv, sum(total_retained_margin) as margin\n", "        from dwh.fact_sales_order_item_details \n", "        where cart_checkout_ts_ist between (current_date - {n_days} || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp\n", "        and order_current_status = 'DELIVERED' and outlet_id in (select outlet_id from fo)\n", "        group by 1,2,3\n", "    ),\n", "    temp as (\n", "    select x.dt, x.city_id, facility_id, item_id, x.product_id, multiplier, avg_selling_price_ratio, quantity, gmv, margin, product_type, item_name, l0, l1, l2, category, storage_type, weight, \n", "            shelf_life, saleable_shelf_life from avail_cat x left join order_sales y on x.dt = y.dt and x.outlet_id = y.outlet_id and x.product_id = y.product_id\n", "            ),\n", "    temp_ts as(\n", "    select dt, city_id, facility_id, item_id, row_number() over(partition by facility_id, item_id order by dt) as rn, sum(quantity*multiplier) as quantity,\n", "            sum(gmv*avg_selling_price_ratio) as gmv, sum(margin*avg_selling_price_ratio) as margin,\n", "            min(case when item_name is not null then item_name end) as item_name, \n", "            max(case when product_type is not null then product_type end) as product_type, max(l0) as l0, max(l1) as l1,\n", "            max(l2) as l2, max(category) as category, max(storage_type) as storage_type, max(weight) as weight,\n", "            min(shelf_life) as shelf_life, min(saleable_shelf_life) as saleable_shelf_life\n", "            from temp\n", "            group by 1,2,3,4)\n", "    select case when city_id = 70 then 6 else city_id end as city_id, facility_id, item_id, \n", "            1.00*sum((rn+200)*(case when quantity is NULL then 0 else quantity end)) / sum(rn+200) as quantity, \n", "            1.00*sum((rn+200)*(case when gmv is NULL then 0 else gmv end)) / sum(rn+200) as gmv, \n", "            1.00*sum((rn+200)*(case when margin is NULL then 0 else margin end)) / sum(rn+200) as margin,\n", "            stddev(case when quantity is NULL then 0 else 1.00*quantity end) as quant_std, count(dt) as available_days_count,\n", "            min(item_name) as item_name, max(product_type) as product_type, max(l0) as l0, max(l1) as l1,\n", "            max(l2) as l2, max(category) as category, max(storage_type) as storage_type, max(weight) as weight, \n", "            min(shelf_life) as shelf_life, min(saleable_shelf_life) as saleable_shelf_life\n", "            from temp_ts\n", "            group by 1,2,3\n", "\n", "    \"\"\".format(\n", "        c=city_id, n_days=lookback_days, sku=sku\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def fetch_cluster_sales(cluster, lookback_days):\n", "    sql = \"\"\" \n", "    with fo as\n", "        (select z.cluster_id, cluster_name, x.city_id, facility_id, outlet_id, outlet_name from lake_po.physical_facility_outlet_mapping x\n", "        inner join lake_rpc.ams_city_cluster_mapping y on x.city_id = y.city_id\n", "        inner join lake_rpc.ams_cluster z on z.cluster_id = y.cluster_id and y.active and z.cluster_id != 50\n", "        where x.active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "        and z.cluster_name = '{cluster}'\n", "        ),\n", "\n", "    sku as (select item_id from lake_rpc.product_facility_master_assortment a\n", "    inner join fo on fo.facility_id = a.facility_id and master_assortment_substate_id in (1,3) and a.active\n", "    group by 1),\n", "\n", "    temp as (select item_id, outlet_id, \"delta\", actual_quantity, blocked_quantity, created_at + interval '330 minute' as created_at from lake_ims.ims_item_inventory_log\n", "    where (actual_quantity = 0 or actual_quantity = \"delta\") and blocked_quantity = 0 and\n", "    created_at between current_date-{n_days}- interval '330 minute' and current_date-1 - interval '330 minute' and outlet_id in (select outlet_id from fo)\n", "    and item_id in (select item_id from sku)\n", "    order by created_at),\n", "\n", "    t2 as (select temp.*, lag(created_at,1) over(partition by outlet_id, item_id order by created_at desc) as lag_dt_ from temp),\n", "\n", "\n", "    t3 as (select *, case when date(created_at) = date(lag_dt_) then \n", "    datediff(minute,created_at,lag_dt_) else datediff(minute,created_at,date(created_at)+1) end as diff\n", "    from t2),\n", "\n", "\n", "    date_ as (select item_id, outlet_id, dt \n", "    from (select date(cart_checkout_ts_ist) dt from dwh.fact_sales_order_details where cart_checkout_ts_ist between current_date - {n_days} and current_date-1 group by 1)\n", "    cross join (select item_id, outlet_id from temp group by 1,2)\n", "    group by 1,2,3\n", "    ),\n", "\n", "    ff as (select date_.*, coalesce(actual_quantity,\n", "    last_value(actual_quantity-\"delta\" ignore nulls) over (partition by date_.outlet_id, date_.item_id order by dt desc, created_at desc rows unbounded preceding),\n", "    last_value(actual_quantity ignore nulls) over (partition by date_.outlet_id, date_.item_id order by dt, created_at rows unbounded preceding)\n", "    ) flag, case when flag = 0 then coalesce(1.0*diff/60,24) else 0 end as diff_new\n", "    from date_ left join t3 on date_.outlet_id = t3.outlet_id and date_.item_id = t3.item_id\n", "    and t3.created_at between date_.dt and date_.dt+1\n", "    order by dt, created_at),\n", "\n", "    avail as (select dt, item_id, outlet_id, sum(diff_new) as unavail_hours from ff group by 1,2,3 having unavail_hours < 10),\n", "\n", "    order_sales as (\n", "        select date(cart_checkout_ts_ist+ interval '330 minute') as dt, item_id, outlet_id, sum(multiplier*product_quantity) as quantity, \n", "        sum(avg_selling_price_ratio*total_selling_price) as gmv, sum(avg_selling_price_ratio*total_retained_margin) as margin\n", "        from dwh.fact_sales_order_item_details x\n", "        inner join dwh.dim_item_product_offer_mapping y on x.product_id = y.product_id\n", "        where cart_checkout_ts_ist between current_date - {n_days}- interval '330 minute' and current_date-1- interval '330 minute'\n", "        and order_current_status = 'DELIVERED' and outlet_id in (select outlet_id from fo) and item_id in (select item_id from sku)\n", "        group by 1,2,3\n", "    ),\n", "\n", "    sale_avail as (\n", "    select avail.*, quantity, gmv, margin, row_number() over(partition by avail.outlet_id, avail.item_id order by avail.dt) as rn from avail\n", "    left join order_sales on order_sales.item_id = avail.item_id and order_sales.outlet_id = avail.outlet_id\n", "    and order_sales.dt = avail.dt\n", "    ),\n", "\n", "    ultimate_data as (select item_id, outlet_id, sum(1.0*(rn+100)*quantity)/sum(rn+100) as quantity,\n", "    stddev(case when quantity is NULL then 0 else 1.00*sale_avail.quantity end) as quant_std,\n", "    sum(1.0*(rn+100)*gmv)/sum(rn+100) as gmv,sum(1.0*(rn+100)*margin)/sum(rn+100) as margin, count(distinct dt) as available_days_count\n", "    from sale_avail \n", "    group by 1,2),\n", "    \n", "    cat_i as (select x.item_id, l0, l1, l2, storage_type, weight_in_gm as weight, shelf_life, greatest(1,shelf_life::int-outward_guidelines::int) as saleable_shelf_life \n", "    from lake_rpc.product_product x\n", "    inner join (select item_id as i, max(id) as id from lake_rpc.product_product where active=1 \n", "                    and weight_in_gm*shelf_life*outward_guidelines is not null group by 1) y on x.item_id = y.i and x.id=y.id and active = 1\n", "    inner join lake_rpc.item_category_details z on x.item_id = z.item_id ),\n", "    \n", "    cat_ as (select item_id, name as item_name, case when item_id in (select item_id from lake_rpc.item_category_details where l0_id = 1487)\n", "    then 'fnv' else case when item_id in (select item_id from lake_rpc.product_product where active =1 and perishable = 1)\n", "    then 'perish' else 'non_perish' end end as category from lake_rpc.item_category_details group by 1,2,3),\n", "\n", "\n", "    ptype as (select item_id, product_type from dwh.dim_product x\n", "    inner join dwh.dim_item_product_offer_mapping y on x.product_id = y.product_id\n", "    and is_current and is_product_enabled and is_offer = false and is_combo = false\n", "    group by 1,2),\n", "\n", "    cat_data as (select cat_i.*,product_type, category, item_name  from cat_i inner join ptype on ptype.item_id = cat_i.item_id\n", "    inner join cat_ on cat_.item_id = cat_i.item_id)\n", "    \n", "    select cat_data.*, facility_id, city_id, quantity, quant_std, gmv, margin, available_days_count from ultimate_data inner join cat_data on cat_data.item_id = ultimate_data.item_id \n", "    inner join (select facility_id, outlet_id, city_id from fo group by 1,2,3) f on f.outlet_id = ultimate_data.outlet_id\n", "\n", "    \"\"\".format(\n", "        cluster=cluster, n_days=lookback_days\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def fetch_item_cat():\n", "    sql = \"\"\"\n", "    with cat_i as (select x.item_id, l0, l1, l2, storage_type, weight_in_gm as weight, shelf_life, greatest(1,shelf_life::int-outward_guidelines::int) as saleable_shelf_life \n", "    from lake_rpc.product_product x\n", "    inner join (select item_id as i, max(id) as id from lake_rpc.product_product where active=1 \n", "                    and weight_in_gm*shelf_life*outward_guidelines is not null group by 1) y on x.item_id = y.i and x.id=y.id and active = 1\n", "    inner join lake_rpc.item_category_details z on x.item_id = z.item_id ),\n", "\n", "\n", "    ptype as (select item_id, product_type from dwh.dim_product x\n", "    inner join dwh.dim_item_product_offer_mapping y on x.product_id = y.product_id\n", "    and is_current and is_product_enabled and is_offer = false and is_combo = false\n", "    group by 1,2)\n", "\n", "    select cat_i.*,product_type  from cat_i inner join ptype on ptype.item_id = cat_i.item_id\n", "        \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def fetch_atc_view_conv_cluster():\n", "    sql = \"\"\"\n", "    with fo as (select x.facility_id, x.outlet_id, ST_GeometryFromText(polygon) as polygons\n", "    from lake_po.physical_facility_outlet_mapping x\n", "    inner join dwh.dim_merchant_outlet_facility_mapping m on m.pos_outlet_id = x.outlet_id and is_pos_outlet_active=1 and is_backend_merchant_active=true and is_frontend_merchant_active=true and is_current and is_mapping_enabled=true\n", "    and x.active=1 and ars_active=1 and x.facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "    inner join (select cast(merchant_id as int) merchant_id, polygon from lake_serviceability.ser_store_polygons where type = 'STORE_POLYGON' and is_active = true) poly on poly.merchant_id = m.frontend_merchant_id\n", "    ),\n", "    \n", "\n", "    dekha as (\n", "        select distinct e.at_date_ist, coalesce(e.properties__child_widget_id, e.properties__product_id, e.properties__widget_id) as pid, \n", "        e.traits__latitude, e.traits__longitude, e.device_uuid\n", "        from \n", "            lake_events.mobile_impression_data e\n", "        Where\n", "            name = 'Product Shown'\n", "            and at_date_ist > current_date - interval '5' day\n", "        ),\n", "        \n", "    k<PERSON><PERSON> as (\n", "        select distinct e.at_date_ist, e.properties__product_id as pid, \n", "        e.traits__latitude, e.traits__longitude, e.device_uuid\n", "        from \n", "            lake_events.mobile_impression_data e\n", "        Where\n", "            name = 'Product Added'\n", "            and at_date_ist > current_date - interval '5' day\n", "        ),\n", "        \n", "    data as (\n", "        select facility_id, item_id, count(dekha.device_uuid) views, count(khareeda.device_uuid) atc from dekha\n", "        left join khareeda on khareeda.pid = dekha.pid\n", "        inner join fo on ST_WITHIN(ST_POINT(dekha.traits__longitude, dekha.traits__latitude), polygons)\n", "                    and ST_WITHIN(ST_POINT(khareeda.traits__longitude, khareeda.traits__latitude), polygons)\n", "        inner join dwh.dim_item_product_offer_mapping p on p.product_id = cast(dekha.pid as int)\n", "        group by 1,2\n", "        )\n", "        \n", "    select *, 1.0*atc/views as conversion from data \n", "        \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def fetch_city_sos_assortment(city_id):\n", "    sql = \"\"\"\n", "    select item_id from metrics.perishable_item_city where city in (select name from lake_retail.console_location where id = {id}) and flag = 'sos'\n", "    \"\"\".format(\n", "        id=city_id\n", "    )\n", "    con = pb.get_connection(\"redpen\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    df[\"sos\"] = 1\n", "    return df\n", "\n", "\n", "def fetch_xpress_ptype():\n", "    sheet_id = \"1kuqz4iaj05ZfLkR1V6Z6OauJw0StqDPrUhRom6YX5iI\"\n", "    sheet_name = \"express\"\n", "    df_express = pb.from_sheets(sheet_id, sheet_name)\n", "    return df_express[\"product_type\"].unique()\n", "\n", "\n", "def fetch_must_ptype():\n", "    sheet_id = \"1kuqz4iaj05ZfLkR1V6Z6OauJw0StqDPrUhRom6YX5iI\"\n", "    sheet_name = \"must\"\n", "    df_express = pb.from_sheets(sheet_id, sheet_name)\n", "    return df_express[\"product_type\"].unique()\n", "\n", "\n", "def add_ptype_depth_classified_by_xpress(df):\n", "    data = df.groupby(\"product_type\").agg(\n", "        {\"quantity\": \"sum\", \"gmv\": \"sum\", \"xpress_ptype\": \"min\"}\n", "    )\n", "    data.sort_values(by=[\"xpress_ptype\", \"gmv\"], ascending=[True, False], inplace=True)\n", "    data[\"ptype_depth_gmv\"] = data[\"gmv\"] / data.groupby(data[\"xpress_ptype\"])[\n", "        \"gmv\"\n", "    ].transform(\"sum\")\n", "    data[\"ptype_depth_gmv\"] = data.groupby(\"xpress_ptype\")[\"ptype_depth_gmv\"].cumsum()\n", "\n", "    data.sort_values(\n", "        by=[\"xpress_ptype\", \"quantity\"], ascending=[True, False], inplace=True\n", "    )\n", "    data[\"ptype_depth_quant\"] = data[\"gmv\"] / data.groupby(data[\"xpress_ptype\"])[\n", "        \"quantity\"\n", "    ].transform(\"sum\")\n", "    data[\"ptype_depth_quant\"] = data.groupby(\"xpress_ptype\")[\n", "        \"ptype_depth_quant\"\n", "    ].cumsum()\n", "    data = data.drop([\"gmv\", \"quantity\", \"xpress_ptype\"], axis=1)\n", "    df = pd.merge(df, data, on=\"product_type\")\n", "    return df\n", "\n", "\n", "def add_item_depth(data):\n", "    data.sort_values(by=[\"product_type\", \"gmv\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_gmv\"] = data[\"gmv\"] / data.groupby(data[\"product_type\"])[\n", "        \"gmv\"\n", "    ].transform(\"sum\")\n", "    data[\"item_depth_gmv\"] = data.groupby(\"product_type\")[\"item_depth_gmv\"].cumsum()\n", "\n", "    data.sort_values(\n", "        by=[\"product_type\", \"quantity\"], ascending=[True, False], inplace=True\n", "    )\n", "    data[\"item_depth_quant\"] = data[\"quantity\"] / data.groupby(data[\"product_type\"])[\n", "        \"quantity\"\n", "    ].transform(\"sum\")\n", "    data[\"item_depth_quant\"] = data.groupby(\"product_type\")[\"item_depth_quant\"].cumsum()\n", "\n", "    data[\"ptype_avg_weight\"] = data.groupby(data[\"product_type\"])[\"weight\"].transform(\n", "        \"mean\"\n", "    )\n", "    data[\"item_count_in_ptype\"] = data.groupby(data[\"product_type\"])[\n", "        \"item_id\"\n", "    ].transform(\"count\")\n", "    data[\"item_count_in_l0\"] = data.groupby(data[\"l0\"])[\"item_id\"].transform(\"count\")\n", "    data[\"ptype_std\"] = data.groupby(\"product_type\")[\"quantity\"].transform(\"std\")\n", "    data[\"ptype_mean\"] = data.groupby(\"product_type\")[\"quantity\"].transform(\"mean\")\n", "\n", "\n", "def add_item_depth_classified_by_weight(data):\n", "    data[\"weight_type\"] = 3\n", "    data[\"weight_type\"][data[\"weight\"] < 1001] = 2\n", "    data[\"weight_type\"][data[\"weight\"] < 251] = 1\n", "    data.sort_values(\n", "        by=[\"weight_type\", \"quantity\"], ascending=[True, False], inplace=True\n", "    )\n", "    data[\"item_depth_classified_by_weight\"] = data[\"quantity\"] / data.groupby(\n", "        data[\"weight_type\"]\n", "    )[\"quantity\"].transform(\"sum\")\n", "    data[\"item_depth_classified_by_weight\"] = data.groupby(\"weight_type\")[\n", "        \"item_depth_classified_by_weight\"\n", "    ].cumsum()\n", "\n", "\n", "def add_item_depth_l0(data):\n", "    data.sort_values(by=[\"l0\", \"gmv\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_gmv_l0\"] = data[\"gmv\"] / data.groupby(data[\"l0\"])[\"gmv\"].transform(\n", "        \"sum\"\n", "    )\n", "    data[\"item_depth_gmv_l0\"] = data.groupby(\"l0\")[\"item_depth_gmv_l0\"].cumsum()\n", "\n", "    data.sort_values(by=[\"l0\", \"quantity\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_quant_l0\"] = data[\"quantity\"] / data.groupby(data[\"l0\"])[\n", "        \"quantity\"\n", "    ].transform(\"sum\")\n", "    data[\"item_depth_quant_l0\"] = data.groupby(\"l0\")[\"item_depth_quant_l0\"].cumsum()\n", "\n", "\n", "# changed here below: add a new function: repair_shelf_life\n", "def repair_shelf_life(data):\n", "    data[\"slife_l2\"] = data.groupby(\"l2\")[\"shelf_life\"].transform(\"median\")\n", "    data[\"slife_l0\"] = data.groupby(\"l0\")[\"shelf_life\"].transform(\"median\")\n", "    data[\"slife_l2\"][data[\"slife_l2\"] > 500] = data[\"slife_l0\"]\n", "    data[\"shelf_life\"][data[\"shelf_life\"] > 500] = data[\"slife_l2\"]\n", "    del data[\"slife_l2\"], data[\"slife_l0\"]\n", "    return data\n", "\n", "\n", "def must_keep_perishable(actual_data):\n", "    data = actual_data.copy()\n", "    data[\"sales_in_slife\"] = data[\"quantity\"] * data[\"shelf_life\"]\n", "    temp_item = data[data[\"sales_in_slife\"] >= 1][\"item_id\"].value_counts()\n", "    must_keep_items = temp_item[\n", "        temp_item > 0.8 * len(data[\"facility_id\"].unique())\n", "    ].index\n", "    data[\"keep\"] = 1\n", "    return data[data[\"item_id\"].isin(must_keep_items)][\n", "        [\"item_id\", \"product_type\", \"keep\"]\n", "    ].drop_duplicates()\n", "\n", "\n", "def once_generate(final_ass):\n", "    final_ass = add_ptype_depth_classified_by_xpress(final_ass)\n", "    add_item_depth(final_ass)\n", "    add_item_depth_classified_by_weight(final_ass)\n", "    add_item_depth_l0(final_ass)\n", "    final_ass[\"item_count_rank\"] = final_ass.groupby(\"facility_id\")[\"quantity\"].rank(\n", "        ascending=False, method=\"min\"\n", "    )\n", "    final_ass[\"item_gmv_rank\"] = final_ass.groupby(\"facility_id\")[\"quantity\"].rank(\n", "        ascending=False, method=\"min\"\n", "    )\n", "    final_ass[\"item_count_rank_ptype\"] = final_ass.groupby(\n", "        [\"facility_id\", \"product_type\"]\n", "    )[\"quantity\"].rank(ascending=False, method=\"min\")\n", "    final_ass[\"item_count_rank_l0\"] = final_ass.groupby([\"facility_id\", \"l0\"])[\n", "        \"quantity\"\n", "    ].rank(ascending=False, method=\"min\")\n", "    final_ass[\"item_gmv_rank_ptype\"] = final_ass.groupby(\n", "        [\"facility_id\", \"product_type\"]\n", "    )[\"gmv\"].rank(ascending=False, method=\"min\")\n", "    final_ass[\"item_weight_rank_ptype\"] = final_ass.groupby(\n", "        [\"facility_id\", \"product_type\"]\n", "    )[\"weight\"].rank(ascending=True, method=\"max\")\n", "    final_ass[\"sales_in_slife\"] = final_ass[\"quantity\"] * final_ass[\"shelf_life\"]\n", "    final_ass[\"sales_in_sslife\"] = (\n", "        final_ass[\"quantity\"] * final_ass[\"saleable_shelf_life\"]\n", "    )\n", "    return final_ass"]}, {"cell_type": "code", "execution_count": null, "id": "e4b2764c-8646-43de-a1f8-537d0a64509d", "metadata": {}, "outputs": [], "source": ["# Accomodated availablity factor\n", "def train_recommender_against(x, city_assortment_sales, svd_assortment):\n", "    for variable in x:\n", "        print(\"Training for\", variable)\n", "        train_assortment_combo = pd.DataFrame(columns=[\"u_id\", \"i_id\", \"rating\"])\n", "        train_assortment_combo[\"u_id\"] = city_assortment_sales[\"facility_id\"]\n", "        train_assortment_combo[\"i_id\"] = city_assortment_sales[\"item_id\"]\n", "        train_assortment_combo[\"rating\"] = city_assortment_sales[variable]\n", "        train_assortment_combo = train_assortment_combo.dropna()\n", "        train_assortment = train_assortment_combo.sample(frac=0.8, random_state=7)\n", "        val_assortment = train_assortment_combo.drop(train_assortment.index.tolist())\n", "        svd_assortment[variable] = SVD(\n", "            lr=5.0\n", "            * (0.001 + city_assortment_sales[variable].min())\n", "            / city_assortment_sales[variable].max(),\n", "            reg=0.5,\n", "            n_epochs=3000,\n", "            n_factors=10,\n", "            early_stopping=True,\n", "            shuffle=False,\n", "            min_rating=city_assortment_sales[variable].min(),\n", "            max_rating=city_assortment_sales[variable].max(),\n", "        )\n", "\n", "        svd_assortment[variable].fit(X=train_assortment, X_val=val_assortment)\n", "        print(\"Training done for\", variable)"]}, {"cell_type": "code", "execution_count": null, "id": "9949dc4a-36c7-4584-abd4-70c9ab2bb13a", "metadata": {}, "outputs": [], "source": ["def store_wise_sales(facility_id, city_assortment_sales):\n", "    cols = [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"quantity\",\n", "        \"gmv\",\n", "        \"views\",\n", "        \"atc\",\n", "        \"conversion\",\n", "        \"active_days\",\n", "    ]\n", "    return city_assortment_sales[city_assortment_sales[\"facility_id\"] == facility_id][\n", "        cols\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "f272d964-22e8-4e0a-bd92-733168f7266f", "metadata": {}, "outputs": [], "source": ["def make_file(predicted_assortment_sales_df, city_id):\n", "    format = \"%Y-%m-%d_%H-%M-%S\"\n", "    now_utc = datetime.now(timezone(\"UTC\"))\n", "    now_asia = now_utc.astimezone(timezone(\"Asia/Kolkata\"))\n", "    csv_file_path = \"SVD_Assortment_Sales_{today}.csv\".format(\n", "        today=now_asia.strftime(format)\n", "    )\n", "\n", "    local_file_path = \"{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "    processed_file = predicted_assortment_sales_df.to_csv(\n", "        \"{filepath}\".format(filepath=local_file_path), index=False, header=True\n", "    )\n", "    file_path = \"assortment_recommendation/sales/{city_id}/{csv_file_path}\".format(\n", "        city_id=city_id, csv_file_path=local_file_path\n", "    )\n", "\n", "    secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "    bucket_name = \"retail-bulk-upload\"\n", "    aws_key = secrets.get(\"aws_key\")\n", "    aws_secret = secrets.get(\"aws_secret\")\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "    return (bucket_obj, local_file_path, file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "4489a7d7-12e6-4e8f-907d-73218617fdfa", "metadata": {}, "outputs": [], "source": ["def push_to_redash_table(df):\n", "    df = df[\n", "        [\n", "            \"city_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"quantity\",\n", "            \"gmv\",\n", "            \"margin\",\n", "            \"quant_std\",\n", "            \"available_days_count\",\n", "            \"item_name\",\n", "            \"product_type\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"category\",\n", "            \"weight\",\n", "            \"shelf_life\",\n", "            \"saleable_shelf_life\",\n", "            \"xpress_ptype\",\n", "            \"predicted\",\n", "            \"conversion\",\n", "            \"sales_in_slife\",\n", "            \"sales_in_sslife\",\n", "        ]\n", "    ]\n", "\n", "    column_dtypes = [\n", "        {\n", "            \"name\": \"city_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"city_id\",\n", "        },\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"facility_id\",\n", "        },\n", "        {\n", "            \"name\": \"item_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"item_id\",\n", "        },\n", "        {\n", "            \"name\": \"quantity\",\n", "            \"type\": \"float\",\n", "            \"description\": \"quantity\",\n", "        },\n", "        {\n", "            \"name\": \"gmv\",\n", "            \"type\": \"float\",\n", "            \"description\": \"gmv\",\n", "        },\n", "        {\n", "            \"name\": \"margin\",\n", "            \"type\": \"float\",\n", "            \"description\": \"margin\",\n", "        },\n", "        {\n", "            \"name\": \"quant_std\",\n", "            \"type\": \"float\",\n", "            \"description\": \"quant_std\",\n", "        },\n", "        {\n", "            \"name\": \"available_days_count\",\n", "            \"type\": \"float\",\n", "            \"description\": \"available_days_count\",\n", "        },\n", "        {\n", "            \"name\": \"item_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"item_name\",\n", "        },\n", "        {\n", "            \"name\": \"product_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"product_type\",\n", "        },\n", "        {\n", "            \"name\": \"l0\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l0\",\n", "        },\n", "        {\n", "            \"name\": \"l1\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l1\",\n", "        },\n", "        {\n", "            \"name\": \"l2\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"l2\",\n", "        },\n", "        {\n", "            \"name\": \"category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"category\",\n", "        },\n", "        {\n", "            \"name\": \"weight\",\n", "            \"type\": \"float\",\n", "            \"description\": \"weight\",\n", "        },\n", "        {\n", "            \"name\": \"shelf_life\",\n", "            \"type\": \"int\",\n", "            \"description\": \"shelf_life\",\n", "        },\n", "        {\n", "            \"name\": \"saleable_shelf_life\",\n", "            \"type\": \"int\",\n", "            \"description\": \"saleable_shelf_life\",\n", "        },\n", "        {\n", "            \"name\": \"xpress_ptype\",\n", "            \"type\": \"float\",\n", "            \"description\": \"xpress_ptype\",\n", "        },\n", "        {\n", "            \"name\": \"must_ptype\",\n", "            \"type\": \"float\",\n", "            \"description\": \"must_ptype\",\n", "        },\n", "        {\n", "            \"name\": \"predicted\",\n", "            \"type\": \"boolean\",\n", "            \"description\": \"predicted\",\n", "        },\n", "        {\n", "            \"name\": \"conversion\",\n", "            \"type\": \"float\",\n", "            \"description\": \"conversion\",\n", "        },\n", "        {\n", "            \"name\": \"sales_in_slife\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sales_in_slife\",\n", "        },\n", "        {\n", "            \"name\": \"sales_in_sslife\",\n", "            \"type\": \"float\",\n", "            \"description\": \"sales_in_sslife\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"assortment_input_v2\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"city_id\"],\n", "        \"sortkey\": [\n", "            \"city_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"product_type\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "        ],\n", "        \"incremental_key\": \"city_id\",\n", "        \"load_type\": \"upsert\",  # , # append, rebuild, truncate or upsert\n", "        \"table_description\": \"input data for dynamic assortment\",  # Description of the table being sent to redshift\n", "    }\n", "    if df.shape[0] > 0:\n", "        start_r = time.time()\n", "        pb.to_redshift(df.drop_duplicates(), **kwargs)\n", "        end_r = time.time()\n", "        print(round((end_r - start_r) / 60, 2))\n", "    else:\n", "        print(\"Not updated\")"]}, {"cell_type": "code", "execution_count": null, "id": "0cbd8a92-038b-4d36-9a39-f0f42398f640", "metadata": {}, "outputs": [], "source": ["def fetch_clusters():\n", "    sql = \"\"\"\n", "        select * from lake_rpc.ams_cluster where cluster_id in\n", "    (select cluster_id from\n", "    (\n", "    select city_id, x.cluster_id, c, row_number() over(partition by city_id order by c desc) as rn from lake_rpc.ams_city_cluster_mapping x\n", "    inner join (select cluster_id, count(distinct city_id) c from lake_rpc.ams_city_cluster_mapping where active group by 1) y\n", "    on x.cluster_id = y.cluster_id and x.active and x.cluster_id != 50\n", "    ) where rn = 1)\n", "        \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7c5ae9c8-6990-42ad-97e1-77d3b2fd8b0f", "metadata": {}, "outputs": [], "source": ["cluster_list = fetch_clusters()"]}, {"cell_type": "code", "execution_count": null, "id": "0b4ed919-06d7-453a-930b-a2931a08b5e0", "metadata": {}, "outputs": [], "source": ["# city_list = [1,7,37,10,27,9,204,142,8,19]\n", "# city_list = [9,7,37,10,142,8,1]\n", "# city_list = [(14,40)]\n", "# city_list = [(15,-15)]\n", "# city_list = [(6,-6)]\n", "# city_list = [(3,-3)]\n", "# city_list = [(5,-5)]\n", "# city_list = [(4,-4)]\n", "# city_list = [(54,66,110,125,55,167)]\n", "# city_list = [(12,-12), (14,40), (54,66,110,125,55,167)]\n", "# city_list = [(6,-6),(5,-5),(4,-4),(3,-3),(12,-12), (14,40), (54,66,110,125,55,167)]\n", "# city_list = [(7,37,10,27,9,204,142,8,19)]\n", "# city_list = [(6,-6)]\n", "city_list = [\n", "    (7, 37, 10, 27, 9, 204, 142, 8, 19),\n", "    (1, -1),\n", "    (5, -5),\n", "    (4, -4),\n", "    (15, -15),\n", "    (3, -3),\n", "    (54, 66, 110, 125, 55, 167, 87, 230),\n", "    (14, 40),\n", "    (12, -12),\n", "    (2, -2),\n", "    (6, -6),\n", "]\n", "# city_list = [(1,-1), (5, -5), (4, -4), (15, -15), (3, -3), (54,66,110,125,55,167,87,230),\n", "#             (14,40), (12,-12), (2,-2), (6, -6)]\n", "city_done_list = []\n", "red_data = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "014cb81c-6cec-440a-a136-14f2d2c3eac1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6f074d8b-929f-4df9-aded-ed6cb6a14461", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e4282a47-057d-4f17-be66-952cbcfcc7bf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b7b7cf2d-cd97-4f9e-85c9-5de2734e60a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3f79f0a8-9ec1-4ac2-819b-e875751ec0c8", "metadata": {"tags": []}, "outputs": [], "source": ["# %%time\n", "# for city_id in city_list:\n", "#     print('building blinkit for city_cluster ', city_id)\n", "#     city_assortment = fetch_city_assortment(city_id)\n", "#     remove_skus = fetch_temp_inact_with_low_inv(city_id)['item_id'].unique()\n", "#     city_assortment_list = city_assortment[~city_assortment['item_id'].isin(remove_skus)]['item_id'].unique()\n", "#     # city_assortment_list = city_assortment['item_id'].unique()\n", "#     print(len(city_assortment_list))\n", "#     print('fetched city assortment')\n", "\n", "#     city_sales_df = pd.DataFrame()\n", "#     for j in city_id:\n", "#         if j > 0:\n", "#             city_sales_df = pd.concat([city_sales_df, fetch_city_sales(j, 120, tuple(city_assortment_list))], ignore_index=True)\n", "#         print(\"Fetched sales data for city_id = \", j)\n", "#     print('fetched sales data for cluster')\n", "\n", "#     city_sales_df = city_sales_df[city_sales_df['item_id'].isin(city_assortment_list)]\n", "\n", "#     atc_view_conv = fetch_atc_view_conv(city_id)\n", "#     print(\"Fetched item facility conversion\")\n", "#     city_sales_df = city_sales_df.merge(atc_view_conv, on = ['item_id', 'facility_id'], how = 'left')\n", "\n", "#     #repairing shelf life bugs\n", "#     city_sales_df = repair_shelf_life(city_sales_df)\n", "#     city_sales_df = city_sales_df.fillna(0)\n", "#     city_sales_df['margin'][city_sales_df['margin'] < 0] = 0\n", "\n", "#     #removed all heavy SKUs\n", "#     # city_sales_df = city_sales_df[city_sales_df['storage_type'] != 5].drop_duplicates()\n", "\n", "#     svd_assortment = dict()\n", "#     variables_to_predict = ['quantity', 'gmv', 'margin', 'quant_std']\n", "#     train_recommender_against(variables_to_predict, city_sales_df, svd_assortment)\n", "#     print(\"Model training done\")\n", "\n", "\n", "#     city_sales_df['xpress_ptype'] = 0\n", "#     city_sales_df['xpress_ptype'][city_sales_df['product_type'].isin(fetch_xpress_ptype())] = 1\n", "\n", "#     city_sales_df['must_ptype'] = 0\n", "#     city_sales_df['must_ptype'][city_sales_df['product_type'].isin(fetch_must_ptype())] = 1\n", "\n", "\n", "#     facility_list = city_sales_df['facility_id'].unique()\n", "#     sku_cat = city_sales_df[['item_id',\n", "#                              'item_name',\n", "#                              'product_type',\n", "#                              'l0',\n", "#                              'l1',\n", "#                              'l2',\n", "#                              'category',\n", "#                              'storage_type',\n", "#                              'weight',\n", "#                              'xpress_ptype',\n", "#                              'shelf_life',\n", "#                              'saleable_shelf_life']].drop_duplicates()\n", "#     whole_sale_data = pd.DataFrame()\n", "#     for facility_id in facility_list:\n", "#         facility_data_sold = city_sales_df[city_sales_df['facility_id'] == facility_id].drop_duplicates()\n", "#         facility_data_sold['predicted'] = False\n", "\n", "#         facility_data_never_sold = sku_cat[~sku_cat['item_id'].isin(facility_data_sold['item_id'].unique())].drop_duplicates()\n", "#         facility_data_never_sold['facility_id'] = facility_id\n", "#         facility_data_never_sold['city_id'] = facility_data_sold[facility_data_sold['facility_id'] == facility_id]['city_id'].unique()[0]\n", "#         facility_data_never_sold['predicted'] = True\n", "#         facility_data_never_sold['u_id'] = facility_data_never_sold['facility_id']\n", "#         facility_data_never_sold['i_id'] = facility_data_never_sold['item_id']\n", "\n", "\n", "#         for variable in variables_to_predict:\n", "#             facility_data_never_sold[variable] = svd_assortment[variable].predict(facility_data_never_sold)\n", "\n", "#         del facility_data_never_sold['u_id'], facility_data_never_sold['i_id']\n", "#         facility_data_never_sold = facility_data_never_sold.fillna(0)\n", "#         facility_data = pd.concat([facility_data_sold, facility_data_never_sold], axis = 0)\n", "#         whole_sale_data = pd.concat([whole_sale_data, facility_data])\n", "#         print('done for facility=', facility_id)\n", "\n", "#     whole_sale_data = whole_sale_data.drop_duplicates()\n", "\n", "#     whole_sale_data = once_generate(whole_sale_data)\n", "#     whole_sale_data = whole_sale_data.fillna(0)\n", "#     red_data = pd.concat([red_data, whole_sale_data[[\"city_id\", \"facility_id\", \"item_id\", \"quantity\", \"gmv\", \"margin\", \"quant_std\",\n", "#                                                  \"available_days_count\", \"item_name\", \"product_type\", \"l0\",\n", "#                                                 \"l1\", \"l2\", \"category\", \"weight\", \"shelf_life\",\n", "#                                                  \"saleable_shelf_life\",\"xpress_ptype\", \"predicted\", \"conversion\",\n", "#                                                  \"sales_in_slife\", \"sales_in_sslife\"]]])\n", "#     print(len(whole_sale_data))\n", "#     if len(whole_sale_data) > 0:\n", "#         bucket_obj, local_file_path, file_path = make_file(whole_sale_data, city_id)\n", "#         bucket_obj.upload_file(local_file_path, file_path)\n", "#         os.remove(local_file_path)\n", "#     city_done_list.append(city_id)\n", "\n", "#     red_data = whole_sale_data[[\"city_id\", \"facility_id\", \"item_id\", \"quantity\", \"gmv\", \"margin\", \"quant_std\",\n", "#                                              \"available_days_count\", \"item_name\", \"product_type\", \"l0\",\n", "#                                             \"l1\", \"l2\", \"category\", \"weight\", \"shelf_life\",\n", "#                                              \"saleable_shelf_life\",\"xpress_ptype\", \"predicted\", \"conversion\",\n", "#                                              \"sales_in_slife\", \"sales_in_sslife\"]].drop_duplicates()\n", "\n", "#     red_data[['city_id', 'facility_id', 'item_id', 'shelf_life', 'saleable_shelf_life']] = red_data[['city_id', 'facility_id', 'item_id', 'shelf_life', 'saleable_shelf_life']].astype(int)\n", "\n", "#     # push_to_redash_table(red_data)"]}, {"cell_type": "code", "execution_count": null, "id": "c69c8ade-5dc0-4997-ad11-af043f261fe7", "metadata": {}, "outputs": [], "source": ["# temp_df = fetch_cluster_sales('NCR', 120)"]}, {"cell_type": "code", "execution_count": null, "id": "264791a9-168e-4e03-ac9a-3f47b520c1cd", "metadata": {}, "outputs": [], "source": ["cluster_list[\"cluster_name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "22a9558d-690b-4c7d-affd-1b78ec0bac7e", "metadata": {}, "outputs": [], "source": ["cluster_list_set = [\n", "    \"Bhopal_Indore\",\n", "    \"Mumbai\",\n", "    \"Pune\",\n", "    \"NCR\",\n", "    \"Bengaluru\",\n", "    \"Lucknow_Kanpur\",\n", "    \"Hyderabad\",\n", "    \"Kolkata\",\n", "    \"Ahmedabad_vadodara\",\n", "    \"Chennai\",\n", "    \"Jaipur\",\n", "    \"Punjab\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ff98aafe-3bfe-4d1c-b252-9d208cc20404", "metadata": {"tags": []}, "outputs": [], "source": ["atc_view_conv = fetch_atc_view_conv_cluster()\n", "for cluster in cluster_list_set:\n", "    print(cluster)\n", "    city_sales_df = pd.DataFrame()\n", "\n", "    city_sales_df = fetch_cluster_sales(cluster, 120)\n", "    print(\"fetched sales data for cluster\")\n", "\n", "    # atc_view_conv = fetch_atc_view_conv_cluster(cluster)\n", "    print(\"Fetched item facility conversion\")\n", "    city_sales_df = city_sales_df.merge(\n", "        atc_view_conv, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", "    )\n", "\n", "    # repairing shelf life bugs\n", "    city_sales_df = repair_shelf_life(city_sales_df)\n", "    city_sales_df = city_sales_df.fillna(0)\n", "    city_sales_df[\"margin\"][city_sales_df[\"margin\"] < 0] = 0\n", "\n", "    # removed all heavy SKUs\n", "    # city_sales_df = city_sales_df[city_sales_df['storage_type'] != 5].drop_duplicates()\n", "\n", "    svd_assortment = dict()\n", "    variables_to_predict = [\"quantity\", \"gmv\", \"margin\", \"quant_std\"]\n", "    train_recommender_against(variables_to_predict, city_sales_df, svd_assortment)\n", "    print(\"Model training done\")\n", "\n", "    city_sales_df[\"xpress_ptype\"] = 0\n", "    city_sales_df[\"xpress_ptype\"][\n", "        city_sales_df[\"product_type\"].isin(fetch_xpress_ptype())\n", "    ] = 1\n", "\n", "    city_sales_df[\"must_ptype\"] = 0\n", "    city_sales_df[\"must_ptype\"][\n", "        city_sales_df[\"product_type\"].isin(fetch_must_ptype())\n", "    ] = 1\n", "\n", "    facility_list = city_sales_df[\"facility_id\"].unique()\n", "    sku_cat = city_sales_df[\n", "        [\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"product_type\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"category\",\n", "            \"storage_type\",\n", "            \"weight\",\n", "            \"xpress_ptype\",\n", "            \"shelf_life\",\n", "            \"saleable_shelf_life\",\n", "        ]\n", "    ].drop_duplicates()\n", "    whole_sale_data = pd.DataFrame()\n", "    for facility_id in facility_list:\n", "        facility_data_sold = city_sales_df[\n", "            city_sales_df[\"facility_id\"] == facility_id\n", "        ].drop_duplicates()\n", "        facility_data_sold[\"predicted\"] = False\n", "\n", "        facility_data_never_sold = sku_cat[\n", "            ~sku_cat[\"item_id\"].isin(facility_data_sold[\"item_id\"].unique())\n", "        ].drop_duplicates()\n", "        facility_data_never_sold[\"facility_id\"] = facility_id\n", "        facility_data_never_sold[\"city_id\"] = facility_data_sold[\n", "            facility_data_sold[\"facility_id\"] == facility_id\n", "        ][\"city_id\"].unique()[0]\n", "        facility_data_never_sold[\"predicted\"] = True\n", "        facility_data_never_sold[\"u_id\"] = facility_data_never_sold[\"facility_id\"]\n", "        facility_data_never_sold[\"i_id\"] = facility_data_never_sold[\"item_id\"]\n", "\n", "        for variable in variables_to_predict:\n", "            facility_data_never_sold[variable] = svd_assortment[variable].predict(\n", "                facility_data_never_sold\n", "            )\n", "\n", "        del facility_data_never_sold[\"u_id\"], facility_data_never_sold[\"i_id\"]\n", "        facility_data_never_sold = facility_data_never_sold.fillna(0)\n", "        facility_data = pd.concat(\n", "            [facility_data_sold, facility_data_never_sold], axis=0\n", "        )\n", "        whole_sale_data = pd.concat([whole_sale_data, facility_data])\n", "        print(\"done for facility=\", facility_id)\n", "\n", "    whole_sale_data = whole_sale_data.drop_duplicates()\n", "\n", "    whole_sale_data = once_generate(whole_sale_data)\n", "    whole_sale_data = whole_sale_data.fillna(0)\n", "    red_data = pd.concat(\n", "        [\n", "            red_data,\n", "            whole_sale_data[\n", "                [\n", "                    \"city_id\",\n", "                    \"facility_id\",\n", "                    \"item_id\",\n", "                    \"quantity\",\n", "                    \"gmv\",\n", "                    \"margin\",\n", "                    \"quant_std\",\n", "                    \"available_days_count\",\n", "                    \"item_name\",\n", "                    \"product_type\",\n", "                    \"l0\",\n", "                    \"l1\",\n", "                    \"l2\",\n", "                    \"category\",\n", "                    \"weight\",\n", "                    \"shelf_life\",\n", "                    \"saleable_shelf_life\",\n", "                    \"xpress_ptype\",\n", "                    \"predicted\",\n", "                    \"conversion\",\n", "                    \"sales_in_slife\",\n", "                    \"sales_in_sslife\",\n", "                ]\n", "            ],\n", "        ]\n", "    )\n", "    print(len(whole_sale_data))\n", "    if len(whole_sale_data) > 0:\n", "        bucket_obj, local_file_path, file_path = make_file(whole_sale_data, cluster)\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        os.remove(local_file_path)\n", "    city_done_list.append(cluster)\n", "\n", "    red_data = whole_sale_data[\n", "        [\n", "            \"city_id\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"quantity\",\n", "            \"gmv\",\n", "            \"margin\",\n", "            \"quant_std\",\n", "            \"available_days_count\",\n", "            \"item_name\",\n", "            \"product_type\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"category\",\n", "            \"weight\",\n", "            \"shelf_life\",\n", "            \"saleable_shelf_life\",\n", "            \"xpress_ptype\",\n", "            \"predicted\",\n", "            \"conversion\",\n", "            \"sales_in_slife\",\n", "            \"sales_in_sslife\",\n", "        ]\n", "    ].drop_duplicates()\n", "\n", "    red_data[\n", "        [\"city_id\", \"facility_id\", \"item_id\", \"shelf_life\", \"saleable_shelf_life\"]\n", "    ] = red_data[\n", "        [\"city_id\", \"facility_id\", \"item_id\", \"shelf_life\", \"saleable_shelf_life\"]\n", "    ].astype(\n", "        int\n", "    )\n", "\n", "    # push_to_redash_table(red_data)"]}, {"cell_type": "code", "execution_count": null, "id": "c651d78d-addc-44df-89d0-e545c007b06a", "metadata": {}, "outputs": [], "source": ["city_done_list"]}, {"cell_type": "code", "execution_count": null, "id": "1e9b6e37-a1ef-46bf-8cc5-32692f57dcf2", "metadata": {}, "outputs": [], "source": ["cluster_list_set"]}, {"cell_type": "code", "execution_count": null, "id": "4cea751c-4a36-4220-9b8d-4c7208358c31", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2831e821-dd62-4244-b6d4-7804d658bb04", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "60e9801d-4ec1-4113-bdec-f32c9381ddfe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fcadeed3-9034-4d78-9ba5-2711cc28b66c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c01deb43-8ea8-4056-8186-2b7a6704206a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "373eeb3a-3dcd-49e7-bb8f-805d1791b799", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}