{"cells": [{"cell_type": "code", "execution_count": null, "id": "8af98ff7-8b51-4c80-a95b-1f033490ea5c", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "e2116e01-0507-4d32-8d6f-80ab8477841a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "e604f555-3a86-4b9b-9647-e8304ff0b808", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "38a1ae3b-d715-4ae1-9790-e2fa9881b695", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "fc3e18e3-5c0c-487e-b380-0928c81a8869", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1efc715d-2f34-4a0e-a303-1329c934fe09", "metadata": {}, "outputs": [], "source": ["transit_query = f\"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   --type,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "   \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          --type,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "    GROUP BY 1,2,3,4,5,6,7,t.metadata\n", "    ),\n", "        \n", "docs AS\n", "    (\n", "    SELECT  t.id AS consignment_id,\n", "            count(DISTINCT d.external_id) AS num_invoices,\n", "            count(DISTINCT c.external_id) AS total_crates_dispatched\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "    JOIN transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND c.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "    GROUP BY 1\n", "    ),\n", "   \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    ),\n", "    \n", "ctype AS\n", "  (\n", "  SELECT consignment_id,\n", "         item_type AS con_type\n", "   FROM\n", "     (SELECT *,\n", "             RANK () OVER (PARTITION BY consignment_id ORDER BY type_count DESC) AS rnk\n", "      FROM\n", "        (SELECT DISTINCT c.id AS consignment_id,\n", "                         (CASE\n", "                              WHEN ids.perishable = 1 THEN 'Perishable'\n", "                              ELSE 'Grocery'\n", "                          END) AS item_type,\n", "                         count(CASE\n", "                                  WHEN ids.perishable = 1 THEN 'Perishable'\n", "                                  ELSE 'Grocery'\n", "                              END) AS type_count,\n", "                sum(pipd.quantity) as dispatched_qty,\n", "                sum(pipd.quantity*pp.weight_in_gm) as dispatched_qty_weight,\n", "                sum(pipd.quantity*ids.length_in_cm*ids.breadth_in_cm*ids.height_in_cm) as indent_volume\n", "         FROM transit_server.transit_consignment c\n", "         JOIN transit_server.transit_consignment_document d ON c.id = d.consignment_id\n", "         JOIN pos.pos_invoice pi ON pi.invoice_id = d.external_id\n", "         JOIN pos.pos_invoice_product_details pipd ON pipd.invoice_id = pi.id\n", "         JOIN rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "         JOIN rpc.item_details ids ON ids.item_id = pp.item_id\n", "         WHERE c.insert_ds_ist >= cast((CURRENT_DATE - interval '21' DAY) AS varchar)\n", "           AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '23' DAY) AS varchar)\n", "           AND pi.insert_ds_ist >= cast((CURRENT_DATE - interval '23' DAY) AS varchar)\n", "           and pipd.insert_ds_ist >= cast((CURRENT_DATE - interval '23' DAY) AS varchar)\n", "           AND d.document_type = 'INVOICE'\n", "         GROUP BY 1,2)\n", "                 )\n", "   WHERE rnk = 1 \n", "   ),\n", "       \n", "\n", "final_base as\n", "    (\n", "    SELECT base.consignment_id,\n", "           con_type,\n", "           tm.trip_id,\n", "           --cast((dispatch_time + interval '330' MINUTE) as date) AS consignment_date,\n", "           cast((tm.truck_handshake + interval '330' MINUTE) as date) AS consignment_date,\n", "           CASE\n", "               WHEN facility_id = 1743 THEN 264\n", "               WHEN facility_id = 4306 THEN 1983\n", "               ELSE facility_id\n", "           END AS facility_id,\n", "           --facility_name,\n", "           ds_outlet_id,\n", "           ds_outlet_name,\n", "           truck_number,\n", "           truck_type,\n", "           tm.trip_state,\n", "           current_state AS consignment_state,\n", "           num_invoices,\n", "           total_crates_dispatched,\n", "           (dispatch_time + interval '240' MINUTE) AS reporting_threshold,\n", "           (dispatch_time + interval '285' MINUTE) AS loading_start_threshold,\n", "           \n", "           (tav.install_ts + interval '330' minute) as return_time_of_previous_trip,\n", "           (dispatch_time + interval '270' MINUTE) AS scheduled_truck_arrival,\n", "           (tm.truck_entry_wh + interval '330' MINUTE) AS truck_entry_wh,\n", "           (tm.truck_handshake + interval '330' MINUTE) AS truck_handshake,\n", "           (loading_start + interval '330' MINUTE) AS loading_start,\n", "           (ready_for_dispatch + interval '330' MINUTE) AS ready_for_dispatch,\n", "           (enroute + interval '330' MINUTE) AS enroute,\n", "           (dispatch_time + interval '330' MINUTE) AS scheduled_dispatch_time,\n", "           (dispatch_time + interval '345' MINUTE) AS exp_dispatch_time,\n", "           (ds_reached + interval '330' MINUTE) AS ds_reached\n", "           \n", "    FROM base\n", "    left join docs on docs.consignment_id = base.consignment_id\n", "    LEFT JOIN ctype ON ctype.consignment_id = base.consignment_id\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    LEFT JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "    left join transit_server.transit_allocation_vehicle tav on tav.trip_id = tm.trip_id and activity_type = 'LOADING' \n", "    and insert_ds_ist >= cast((current_date - interval '14' day) as varchar)\n", "    ),\n", "    \n", "facility_outlet_mapping as\n", "(\n", "select 337 as outlet_id, 1 as facility_id \n", "union all\n", "select 1577 as outlet_id, 517 as facility_id \n", "union all\n", "select 1725 as outlet_id, 603 as facility_id \n", "union all\n", "select 2653 as outlet_id, 1206 as facility_id \n", "union all\n", "select 4165 as outlet_id, 1872 as facility_id \n", "union all\n", "select 4170 as outlet_id, 1873 as facility_id \n", "union all\n", "select 1104 as outlet_id, 264 as facility_id \n", "union all\n", "select 1644 as outlet_id, 555 as facility_id \n", "union all\n", "select 481 as outlet_id, 43 as facility_id \n", "union all\n", "select 2904 as outlet_id, 1320 as facility_id \n", "union all\n", "select 4181 as outlet_id, 1876 as facility_id \n", "union all\n", "select 4325 as outlet_id, 2006 as facility_id \n", "union all\n", "select 714 as outlet_id, 92 as facility_id \n", "union all\n", "select 4306 as outlet_id, 1983 as facility_id \n", "union all\n", "select 4330 as outlet_id, 2010 as facility_id \n", "union all\n", "select 4375 as outlet_id, 2015 as facility_id \n", "union all\n", "select 4427 as outlet_id, 2076 as facility_id \n", "union all\n", "select 4432 as outlet_id, 2078 as facility_id \n", "union all\n", "select 4496 as outlet_id, 2123 as facility_id \n", "union all\n", "select 4516 as outlet_id, 2141 as facility_id\n", "),\n", "    \n", "time_gaps_base as\n", "    (\n", "    select  final_base.*,\n", "            fom.outlet_id as wh_outlet_id,\n", "            name as wh_outlet_name,\n", "            date_diff('minute', loading_start, ready_for_dispatch) as loading_time_at_wh,\n", "            date_diff('minute', exp_dispatch_time, ready_for_dispatch) as dispatch_delay_min,\n", "            case when return_time_of_previous_trip > reporting_threshold then 'reporting_delay' else 'reporting_ontime' \n", "            end as vehicle_report_status,\n", "            case when loading_start > loading_start_threshold then 'load_start_delay' else 'load_start_ontime' \n", "            end as load_start_status,  \n", "            date_diff('minute', ready_for_dispatch, ds_reached) as frwd_transit_time\n", "    from final_base\n", "    left join facility_outlet_mapping fom on fom.facility_id = final_base.facility_id\n", "    left join retail.console_outlet co on co.id = fom.outlet_id\n", "    ),\n", "\n", "last_trip as(\n", "    select  install_ts,\n", "        trip_id,\n", "        truck_number,\n", "        lag(trip_id,1) over (partition by truck_number order by install_ts) as last_trip_id\n", "from transit_server.transit_projection_trip \n", "where insert_ds_ist >= cast((CURRENT_DATE - interval '17' DAY) as varchar)\n", "    ),\n", "    \n", "enroute_tat as\n", "    (\n", "    select con.consignment_id,\n", "           tl1.actual_ts as dispatch_ts,\n", "           tl1.actual_ts + (tl2.scheduled_ts - tl1.scheduled_ts) as arrival_eta_at_dispatch,\n", "           tl2.actual_ts as arrival_ts\n", "    from transit_server.transit_projection_consignment con\n", "        join transit_server.transit_projection_trip_event_timeline tl1 on con.trip_id = tl1.trip_id and tl1.event_type = 'LOADING_END' and con.consignment_type = json_extract_scalar(tl1.event_meta, '$.consignment_type')\n", "        join transit_server.transit_projection_trip_event_timeline tl2 on con.trip_id = tl2.trip_id and con.consignment_id = cast(json_extract_scalar(tl2.event_meta, '$.consignment_id') as int) and con.consignment_type = json_extract_scalar(tl2.event_meta, '$.consignment_type')\n", "        and tl2.event_type = 'DS_ARRIVAL'\n", "    where tl1.insert_ds_ist >= cast((current_date - interval '15' day) as varchar) and tl1.lake_active_record --and tl1.event_type = 'LOADING_END' \n", "        and tl2.insert_ds_ist >= cast((current_date - interval '15' day) as varchar) and tl2.lake_active_record \n", "        and con.insert_ds_ist >= cast((current_date - interval '15' day) as varchar)\n", "        and con.consignment_type = 'FORWARD_CONSIGNMENT'\n", "    )\n", "\n", "    \n", " select  base.*,last_trip_id,\n", "        case when dispatch_delay_min <= 0 then 'dispatch_ontime' else 'dispatch_delay' \n", "        end as dispatch_delay_status,\n", "        case when trim(co.location)='Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "        \n", "        CAST(scheduled_dispatch_time AS time) as sch_dispatch_time,\n", "        CAST(scheduled_dispatch_time AS date) as sch_dispatch_date,\n", "        (CASE\n", "              WHEN hour(CAST(scheduled_dispatch_time AS time)) < 12 THEN 'Day'\n", "              ELSE 'Night'\n", "        END) AS dispatch_slot,\n", "        CAST(ds_reached AS time) as ds_reach_time,\n", "        (case when arrival_ts > (arrival_eta_at_dispatch + interval '30' minute) then 'breach' else 'normal' end) as enroute_tat_breach_trip\n", "        \n", "\n", "from time_gaps_base base\n", "left join lake_retail.console_outlet co on co.id = base.ds_outlet_id\n", "left join last_trip lt on base.trip_id=lt.trip_id\n", "left join enroute_tat et on et.consignment_id = base.consignment_id\n", "\n", "where UPPER(wh_outlet_name) not like '%%PC%%'\n", "  and trip_state <> 'CANCELLED'\n", "  and con_type = 'Grocery'\n", "  and truck_type not like '%%Reefer%%'\n", "  --and scheduled_dispatch_time is not null\n", "         \n", "\"\"\"\n", "\n", "frwd_df = read_sql_query(transit_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "2f09b261-071b-4e23-a52d-da3ef5e731f7", "metadata": {}, "outputs": [], "source": ["frwd_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e9d56dfa-2e2c-43d7-ba55-dbd90b05e66b", "metadata": {}, "outputs": [], "source": ["# x=transit_df.groupby(['wh_outlet_name','consignment_date'], as_index=False)['trip_id'].nunique()\n", "# x.head(60)"]}, {"cell_type": "code", "execution_count": null, "id": "1fb98b9f-83c6-4f17-8e81-95d57a13ea27", "metadata": {}, "outputs": [], "source": ["frwd_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f1954a28-8448-48e7-9534-a394a9756ca6", "metadata": {}, "outputs": [], "source": ["# frwd_df.to_csv('frwd_df.csv',index='false')"]}, {"cell_type": "code", "execution_count": null, "id": "7923a86b-bf01-4776-bee1-6ec760a3472d", "metadata": {}, "outputs": [], "source": ["# frwd_df.consignment_id.value_counts()\n", "# frwd_df[frwd_df['last_trip_id'].isna()==True]"]}, {"cell_type": "code", "execution_count": null, "id": "ebee5f49-8872-4a0b-a42a-0f4e12e3ebbb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f64efdd2-fcb2-4f6c-a97d-1a4b9da133b6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "65c99754-78f9-433b-bc82-0519a3cd45d7", "metadata": {}, "outputs": [], "source": ["rev_transit_query = \"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   --type,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND t.type = 'REVERSE_CONSIGNMENT' \n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "   \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          --type,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           co2.outlet_id as source_outlet_id,\n", "           n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "      \n", "      left join \n", "      (     select distinct logistic_node_id, outlet_id\n", "            from retail.console_outlet_logistic_mapping a\n", "            join retail.console_outlet b\n", "            on a.outlet_id = b.id\n", "            where b.active = 1\n", "            and a.active = TRUE\n", "            and a.id not in (611, 1538)\n", "      ) as co2 \n", "      ON co2.logistic_node_id = cast(t.source_store_id as int)  \n", "        \n", "        \n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "      AND t.type = 'REVERSE_CONSIGNMENT' \n", "    GROUP BY 1,2,3,4,5,6,7,8,t.metadata\n", "    )\n", "   \n", "\n", "SELECT distinct tm.trip_id as last_trip_id,\n", "        --base.consignment_id,\n", "        --facility_name,\n", "        --ds_outlet_name,\n", "    max(ds_reached + interval '330' MINUTE) AS backend_reached_at,\n", "    max(unloading_start + interval '330' MINUTE) AS unloading_start_wh,\n", "    max(unloading_completed + interval '330' MINUTE) AS unloading_completed_wh\n", "    \n", "FROM base\n", "left JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "left join pos on base.consignment_id=pos.csmt_id\n", "where tm.trip_id is not null\n", "group by 1\n", "\"\"\"\n", "\n", "\n", "rev_transit_df = read_sql_query(rev_transit_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "6fb4b909-3fac-477e-8f2b-41f50ab5b160", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c94c04e1-7611-444b-bca2-26c0f107d291", "metadata": {}, "outputs": [], "source": ["rev_transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "27924172-b4c4-475b-880f-51146d6f3bfb", "metadata": {}, "outputs": [], "source": ["# rev_transit_df[rev_transit_df['last_trip_id']=='336_643356']"]}, {"cell_type": "code", "execution_count": null, "id": "73d8bb20-61fb-434a-9c55-dc71c69c226a", "metadata": {}, "outputs": [], "source": ["# rev_transit_df[rev_transit_df['last_trip_id'].isna()==True]"]}, {"cell_type": "code", "execution_count": null, "id": "70336c25-b258-4632-b436-d00e31ac85f6", "metadata": {}, "outputs": [], "source": ["transit_df = frwd_df.merge(rev_transit_df, how=\"left\", on=[\"last_trip_id\"])\n", "transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "49b3940f-a69d-46ff-861b-57a53319217d", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "9629f215-f2e7-4b56-90e3-1d0be863b613", "metadata": {}, "outputs": [], "source": ["# transit_df[transit_df['trip_id']=='336_645526']"]}, {"cell_type": "code", "execution_count": null, "id": "62583590-a5a0-4ba5-a6e7-97d5325ee9ca", "metadata": {}, "outputs": [], "source": ["transit_df.rename(\n", "    columns={\n", "        \"backend_reached_at\": \"last_check_in\",\n", "        \"unloading_start_wh\": \"last_unloading_start_wh\",\n", "        \"unloading_completed_wh\": \"last_unloading_completed_wh\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2f2c1d8c-52c2-479b-a33a-ca47bea9d1c8", "metadata": {}, "outputs": [], "source": ["transit_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b09142be-5db2-4789-87b8-73d8c6d52aaa", "metadata": {}, "outputs": [], "source": ["# transit_df.to_csv('frwd_rev_combine.csv',index='false')"]}, {"cell_type": "code", "execution_count": null, "id": "14341d43-3664-404c-84a3-b205b03ee27d", "metadata": {}, "outputs": [], "source": ["# transit_df[transit_df['last_trip_id'].isna()==True]"]}, {"cell_type": "code", "execution_count": null, "id": "ef4b7a8f-5955-4e9c-825e-bee21d001704", "metadata": {}, "outputs": [], "source": ["# transit_df[transit_df['trip_id']=='15591_660456']"]}, {"cell_type": "code", "execution_count": null, "id": "cc5244fb-d5fb-436e-b037-69e86c00a2c3", "metadata": {}, "outputs": [], "source": ["transit_df[\"loading_start\"] = pd.to_datetime(transit_df[\"loading_start\"])\n", "transit_df[\"last_unloading_completed_wh\"] = pd.to_datetime(\n", "    transit_df[\"last_unloading_completed_wh\"]\n", ")\n", "\n", "transit_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "853236ae-6ba3-4a21-8c53-e82461087a1d", "metadata": {}, "outputs": [], "source": ["# idle_time_unload_comp_load_start\n", "transit_df[\"idle_time_unload_comp_load_start\"] = (\n", "    transit_df[\"loading_start\"] - transit_df[\"last_unloading_completed_wh\"]\n", ")\n", "\n", "\n", "transit_df[\"idle_time_unload_comp_load_start\"] = transit_df[\n", "    \"idle_time_unload_comp_load_start\"\n", "].apply(lambda x: x.total_seconds() / 60 if pd.notnull(x) else np.nan)\n", "\n", "# Replace idle time with None where last_trip_id is null\n", "# transit_df['idle_time_unload_comp_load_start'] = np.where(transit_df['last_trip_id'].isnull(), np.nan, transit_df['idle_time_unload_comp_load_start'])\n", "\n", "# Convert the idle_time to string, handling NaN values appropriately\n", "# transit_df['idle_time_unload_comp_load_start'] = transit_df['idle_time_unload_comp_load_start'].apply(lambda x: str(x) if pd.notnull(x) else 'None')"]}, {"cell_type": "code", "execution_count": null, "id": "8ef0baf9-dc26-4be2-a3b6-e046c3ff4815", "metadata": {}, "outputs": [], "source": ["transit_df[\"last_unloading_start_wh\"] = pd.to_datetime(transit_df[\"last_unloading_start_wh\"])\n", "transit_df[\"last_check_in\"] = pd.to_datetime(transit_df[\"last_check_in\"])\n", "transit_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "3ecd3d6e-1d5d-4937-b454-9a1b3ca6f602", "metadata": {}, "outputs": [], "source": ["# idle_time_checkin_unload_start#\n", "transit_df[\"idle_time_checkin_unload_start\"] = (\n", "    transit_df[\"last_unloading_start_wh\"] - transit_df[\"last_check_in\"]\n", ")\n", "\n", "\n", "transit_df[\"idle_time_checkin_unload_start\"] = transit_df[\"idle_time_checkin_unload_start\"].apply(\n", "    lambda x: x.total_seconds() / 60 if pd.notnull(x) else np.nan\n", ")\n", "\n", "# # Replace idle time with None where last_trip_id is null\n", "# transit_df['idle_time_checkin_unload_start'] = np.where(transit_df['last_trip_id'].isnull(), np.nan, transit_df['idle_time_checkin_unload_start'])\n", "\n", "# # Convert the idle_time to string, handling NaN values appropriately\n", "# transit_df['idle_time_checkin_unload_start'] = transit_df['idle_time_checkin_unload_start'].apply(lambda x: str(x) if pd.notnull(x) else 'None')"]}, {"cell_type": "code", "execution_count": null, "id": "4feb97b8-30be-48be-8972-694b6e4ba9a0", "metadata": {}, "outputs": [], "source": ["# transit_df['idle_time']=(transit_df['loading_start']-transit_df['last_unloading_completed_wh']).dt.total_seconds() / 60\n", "transit_df"]}, {"cell_type": "code", "execution_count": null, "id": "d00bd3f6-cc3a-4897-8634-10c7e7fd2b9a", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4b177683-54cc-4fa4-a175-808f6add1742", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9369b7c0-ef12-47f5-8fcf-6812a7765c79", "metadata": {}, "outputs": [], "source": ["# x=transit_df[(transit_df['facility_id']==603) & (transit_df['consignment_date']=='2024-06-04')]\n", "# x.to_csv('x.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "2a485cfb-2a87-4265-8973-f83441e4a997", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5646d037-f931-47f6-bab2-86710631a2cf", "metadata": {}, "outputs": [], "source": ["# transit_df[transit_df['trip_id']=='336_645526']"]}, {"cell_type": "code", "execution_count": null, "id": "2d210e72-b1eb-497d-b1fa-6cac3c85bdbf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3683b61b-5da2-4c7f-af76-8d440023ba55", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "markdown", "id": "26055b3d-3913-4e10-b233-26fecbe8893d", "metadata": {}, "source": ["### OTD Metrics\n"]}, {"cell_type": "code", "execution_count": null, "id": "1b82ff85-276e-4076-9a24-4d7d008630a8", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1HFADY6wROhf2rZUKa8aSz6KZc_v0wxtDrJgAczpPixI\"\n", "sheet_name2 = \"March\"\n", "\n", "mar_planning = pb.from_sheets(sheet_id, sheet_name2)\n", "\n", "# mar_planning = pd.read_csv('planning_data_mar.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "1abf331c-4023-4e9e-ba9b-3a3804750f1d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "081fd772-ab7e-4f5f-8c56-6908728da4cb", "metadata": {}, "outputs": [], "source": ["# planning_data = pd.concat([feb_planning, mar_planning])\n", "planning_data = mar_planning.copy()\n", "planning_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ac71c645-24fa-44f1-921a-061dea440a41", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f3383269-884c-4b94-bef4-fc6a21a5aab8", "metadata": {}, "outputs": [], "source": ["planning_data.rename(\n", "    columns={\n", "        \"sender_facility_id\": \"facility_id\",\n", "        \"receiving_outlet_id\": \"ds_outlet_id\",\n", "        \"DS Arrival time at Day\": \"Day\",\n", "        \"DS Arrival time at Night\": \"Night\",\n", "    },\n", "    inplace=True,\n", ")\n", "# planning_data = planning_data[\n", "#     [\"facility_id\", \"ds_outlet_id\", \"Day\", \"Night\", \"otd_month\"]\n", "# ]\n", "\n", "planning_data = planning_data[[\"facility_id\", \"ds_outlet_id\", \"Day\", \"Night\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "a8ad805a-48b2-4482-843a-f57eff98ed96", "metadata": {}, "outputs": [], "source": ["planning_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bf3252da-c83b-4e24-ba0a-91638dd37be6", "metadata": {}, "outputs": [], "source": ["planning_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "66766d9c-f8b0-4eec-8efd-d7756e732b64", "metadata": {}, "outputs": [], "source": ["planning_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1804c3ab-174d-4512-ba8c-485e957eee42", "metadata": {}, "outputs": [], "source": ["planning_df = planning_data.melt(\n", "    id_vars=[\"facility_id\", \"ds_outlet_id\"],\n", "    value_vars=[\"Day\", \"Night\"],\n", "    var_name=\"dispatch_slot\",\n", "    value_name=\"planned_ds_arrival\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c14526be-bc02-4342-8a1d-6c8bf2403d05", "metadata": {}, "outputs": [], "source": ["planning_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "11d7802f-48b6-44e8-97fd-2de5639815ac", "metadata": {}, "outputs": [], "source": ["planning_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d22cd3e5-f752-4470-86ce-67092991b552", "metadata": {}, "outputs": [], "source": ["planning_df = planning_df[planning_df[\"planned_ds_arrival\"].isna() == False]\n", "planning_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "40f38ce3-1c4b-4e02-9568-ecb6433808f0", "metadata": {}, "outputs": [], "source": ["# planning_df[planning_df['planned_ds_arrival'].isna()==True]"]}, {"cell_type": "code", "execution_count": null, "id": "385a98e4-a13e-4065-ba03-94814aabde2b", "metadata": {}, "outputs": [], "source": ["planning_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fbc6defd-426e-4422-b1c6-08d42b46cbe4", "metadata": {}, "outputs": [], "source": ["# planning_df['planned_ds_arrival']=pd.to_datetime(planning_df['planned_ds_arrival']).dt.time\n", "# # df['time'] = pd.to_datetime(df['time_str']).dt.time\n", "# planning_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5fc5e845-7aa2-4590-9cf5-18afbd96737d", "metadata": {}, "outputs": [], "source": ["max_planned_ds_arrival = sqldf(\n", "    \"\"\"\n", "select  facility_id,\n", "        ds_outlet_id,\n", "        dispatch_slot,\n", "        max(planned_ds_arrival) as planned_ds_arrival\n", "from planning_df \n", "group by 1,2,3\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "17173fc4-49e3-4039-9976-5fc559f42631", "metadata": {}, "outputs": [], "source": ["# con.shape"]}, {"cell_type": "code", "execution_count": null, "id": "69343178-4823-4596-a395-bd5055115d59", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "14252d26-124b-465d-b693-dff6f796a2fd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "783f1586-f0a0-4d4d-9486-02dc781ee9f6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2c47c09f-01e8-4eb0-bdce-89f87316ccdb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7967443a-b1a3-415c-8641-c9b03748dbd2", "metadata": {}, "outputs": [], "source": ["# planning_df['planned_ds_arrival'] = pd.to_datetime(planning_df['planned_ds_arrival'], format='%H:%M:%S', errors='coerce').dt.time\n", "\n", "# planning_df['planned_ds_arrival'] = planning_df['planned_ds_arrival'].apply(lambda x: str(x))\n", "# max_planned_ds_arrival = planning_df.groupby(['facility_id', 'ds_outlet_id', 'dispatch_slot'])['planned_ds_arrival'].max().reset_index()\n", "# max_planned_ds_arrival"]}, {"cell_type": "code", "execution_count": null, "id": "34336f59-580e-4b61-9415-f881a5678d94", "metadata": {}, "outputs": [], "source": ["max_planned_ds_arrival[\"facility_id\"] = max_planned_ds_arrival[\"facility_id\"].astype(int)\n", "max_planned_ds_arrival[\"ds_outlet_id\"] = max_planned_ds_arrival[\"ds_outlet_id\"].astype(int)\n", "max_planned_ds_arrival[\"dispatch_slot\"] = max_planned_ds_arrival[\"dispatch_slot\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "daadea82-cb6c-4215-a880-137d2229ec60", "metadata": {}, "outputs": [], "source": ["# final_df = transit_df.merge(\n", "#     planning_df,\n", "#     how=\"left\",\n", "#     on=[\"facility_id\", \"ds_outlet_id\", \"dispatch_slot\", \"otd_month\"],\n", "# )\n", "\n", "final_df = transit_df.merge(\n", "    max_planned_ds_arrival,\n", "    how=\"left\",\n", "    on=[\"facility_id\", \"ds_outlet_id\", \"dispatch_slot\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "74398364-7e45-4553-bbb1-e2c275d9972e", "metadata": {}, "outputs": [], "source": ["final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "88ea5e3c-8057-4b29-960e-05afaa83d9fb", "metadata": {}, "outputs": [], "source": ["transit_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "dc76a440-7a54-4c3b-bf3e-e6421cacf0a8", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b107fb84-f39b-4427-8493-84f84e223205", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cce2ed86-6a30-4b66-ba20-548800fb90c9", "metadata": {}, "outputs": [], "source": ["final_df.consignment_id.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "7f26141b-999b-4176-b6f7-4b975cff8091", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f78805c-11ca-49eb-b2d1-2c76067caf3d", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7959b1f0-05d7-4a23-9aa4-126410f47268", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c7c2ba3-0214-4962-9a1d-4fe3e15a8e8c", "metadata": {}, "outputs": [], "source": ["final_df[\"ds_reach_delay\"] = (\n", "    pd.to_datetime(final_df[\"ds_reach_time\"]) - pd.to_datetime(final_df[\"planned_ds_arrival\"])\n", ").dt.total_seconds() / 60\n", "final_df[\"ds_reach_delay\"] = final_df[\"ds_reach_delay\"].fillna(value=\"NA\")"]}, {"cell_type": "code", "execution_count": null, "id": "adf9289d-5c1d-4c8c-a778-1279e0e687de", "metadata": {}, "outputs": [], "source": ["final_df_na = final_df[final_df[\"ds_reach_delay\"] == \"NA\"]\n", "final_df_int = final_df[final_df[\"ds_reach_delay\"] != \"NA\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c5728ffb-8145-47af-99e9-3ea22d55f012", "metadata": {}, "outputs": [], "source": ["print(final_df_na.shape)\n", "print(final_df_int.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "5d99c127-87f3-4abc-b560-fe7d679f09e0", "metadata": {}, "outputs": [], "source": ["final_df_int[\"ds_reach_delay\"] = final_df_int[\"ds_reach_delay\"].astype(int)\n", "final_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "baf8030c-d0fd-44b0-90d3-84590b048081", "metadata": {}, "outputs": [], "source": ["final_df_int.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "f9a30e6f-5e52-46be-a0b9-c877410da35c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "affd0ede-23f3-4b83-bffb-bf57835e0412", "metadata": {}, "outputs": [], "source": ["def ds_delay_bucket(x):\n", "    if x < 0:\n", "        if x <= -60:\n", "            if x <= -120:\n", "                result = \"(<120)\"\n", "            else:\n", "                result = \"(-120 - -60)\"\n", "        else:\n", "            result = \"(-60 - 0)\"\n", "\n", "    elif x >= 0:\n", "        if x >= 30:\n", "            if x >= 60:\n", "                if x >= 120:\n", "                    result = \"(>120)\"\n", "                else:\n", "                    result = \"(60 - 120)\"\n", "            else:\n", "                result = \"(30 - 60)\"\n", "        else:\n", "            result = \"(0 - 30)\"\n", "\n", "    else:\n", "        result = \"NA\"\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "4c3f9543-bc54-4b86-a218-ba217e14788a", "metadata": {}, "outputs": [], "source": ["def ds_delay_status(x):\n", "    if x >= 0:\n", "        if x > 60:\n", "            result = \"Delay\"\n", "        else:\n", "            result = \"On-Time\"\n", "\n", "    elif x < 0:\n", "        if x < -30:\n", "            result = \"Early\"\n", "        else:\n", "            result = \"On-Time\"\n", "\n", "    else:\n", "        result = \"Delay\"\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "e3578148-7997-4f39-8159-7db734e14280", "metadata": {}, "outputs": [], "source": ["final_df_int[\"ds_reach_delay_bucket\"] = final_df_int[\"ds_reach_delay\"].apply(\n", "    lambda x: ds_delay_bucket(x)\n", ")\n", "final_df_int[\"ds_reach_delay_status\"] = final_df_int[\"ds_reach_delay\"].apply(ds_delay_status)"]}, {"cell_type": "code", "execution_count": null, "id": "591dcd16-3211-4a34-a3f7-dc3b53611a3d", "metadata": {}, "outputs": [], "source": ["final_df_int.shape"]}, {"cell_type": "code", "execution_count": null, "id": "747c6c33-74dc-4dff-9b27-765b23104585", "metadata": {}, "outputs": [], "source": ["final_df_na[\"ds_reach_delay_bucket\"] = \"NA\"\n", "final_df_na[\"ds_reach_delay_status\"] = \"Delay\"\n", "final_df_na.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "5d5bd2e0-a479-4045-9161-a9f51b0e41f9", "metadata": {}, "outputs": [], "source": ["final = pd.concat([final_df_int, final_df_na])"]}, {"cell_type": "code", "execution_count": null, "id": "011e7575-7f67-4425-a576-2d910f4e150a", "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ba1c805b-c5ca-4730-be1a-70d5deee6ee0", "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3214fac6-abcd-475e-9732-8aa7914223f3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "897f3c65-fbfe-4727-ab03-a83e00e9d13c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce914dd3-3e9d-489b-8d4c-9491d9ce70d3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "70e5125b-bbb5-473f-8bef-49705064857e", "metadata": {}, "outputs": [], "source": ["## Warehouse Sortation Data\n", "\n", "sorted_crates = f\"\"\"\n", "\n", "\n", "select  distinct date(oc.dispatch_time + interval '330' minute) date,\n", "    -- (case when hour(sto.created_at) < 12 then 'Morning' else 'Evening' end)  as shift,\n", "    cmt.outlet_id,\n", "    co.name as outlet_name,\n", "    -- outbound_demand_id,\n", "    -- destination_outlet_id,\n", "    mc.container_id,\n", "    (oc.dispatch_time +interval '330' minute) as scheduled_dispatch_time,\n", "    cast((oc.dispatch_time +interval '330' minute) as date) as dispatch_date,\n", "    cast((oc.dispatch_time +interval '330' minute) as time) as dispatch_time\n", "    -- (mc.updated_at+interval '330' minute) as sorted_at,\n", "    -- oc.updated_at as dispatched_at\n", "    \n", "from wms.migration_container mc\n", "join wms.container_migration_task cmt on cmt.id=mc.container_migration_task_id and cmt.type = 'DISPATCH_SORTATION'\n", "join wms.outbound_container oc on oc.outbound_demand_id=json_extract_scalar(mc.meta,'$.demand_id') and oc.billing_entity_type in ('STO_BULK_PICK_LIST','BACKGROUND_BILLING_TASK')\n", " and oc.container_id = mc.container_id\n", " and oc.insert_ds_ist >= cast(current_date - interval '16' day as varchar)\n", " and oc.state = 'DISPATCHED'\n", "join wms.pick_list pl on json_extract_scalar(mc.meta,'$.demand_id') = json_extract_scalar(json_extract(pl.meta,'$.outbound_demand_details'),'$.demand_id')\n", " and pl.insert_ds_ist >= cast(current_date - interval '16' day as varchar)\n", "join wms.pick_list_item pli on pl.id = pli.pick_list_id and pli.insert_ds_ist >= cast(current_date - interval '16' day as varchar)\n", "join wms.pick_list_item_order_mapping plio on plio.pick_list_item_id = pli.id\n", "join po.sto on sto.id = cast(plio.order_id as int)\n", "left join retail.console_outlet co on co.id=cmt.outlet_id and co.active=1 and co.business_type_id in (1,12) \n", "where mc.insert_ds_ist >= cast(current_date - interval '16' day as varchar) \n", "  and mc.state='DROPPED' \n", "  and cmt.type='DISPATCH_SORTATION'\n", "  and cmt.insert_ds_ist >= cast(current_date - interval '16' day as varchar)\n", "  and sto.created_at between cast(current_date - interval '16' day as timestamp) and current_date\n", "  and cmt.outlet_id in (337,1577,1725,2653,4165,4170,1104,1644,481,2904,4181,4325,714,4306,4330,4375,4427,4432,4496,4516,4498)\n", "\n", "         \n", "\"\"\"\n", "\n", "sorted_df = read_sql_query(sorted_crates, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "a9f6ce14-d3c4-4235-83aa-15af1702d087", "metadata": {}, "outputs": [], "source": ["# sorted_df.to_csv('sorted_df.csv', index=False)\n", "# sorted_df = pd.read_csv('sorted_df.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "c9875e79-e5af-4d90-a538-b50792324869", "metadata": {}, "outputs": [], "source": ["sorted_df[\"scheduled_dispatch_time\"] = pd.to_datetime(sorted_df[\"scheduled_dispatch_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "09e1947d-1472-4e9f-9bec-a1f242996eaf", "metadata": {}, "outputs": [], "source": ["sorted_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c82f84c3-f844-455c-b979-2b42ed9629b6", "metadata": {}, "outputs": [], "source": ["sorted_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e6a0dbdb-be28-4784-9f1e-6e8199cd0e87", "metadata": {}, "outputs": [], "source": ["sorted_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a0647df3-813f-4b25-a4fa-07a3e0616298", "metadata": {}, "outputs": [], "source": ["# sorted_df.to_csv(\"sorted_df.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "eee5d07e-bee5-4bd5-802e-7c60ff43fb71", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e17c4924-80e1-4816-ab55-1a8e452a20fc", "metadata": {}, "outputs": [], "source": ["## Warehouse Billed Data\n", "\n", "billed_crates = f\"\"\"\n", "\n", "select  distinct date(p.scheduled_time+interval '330' minute) as date,\n", "        p.outlet_id,\n", "        s.outlet_name,\n", "        plcm.container_id,\n", "        -- json_extract_scalar(p.meta,'$.merchant_name') as merchant,\n", "        (p.scheduled_time+interval '330' minute) as scheduled_dispatch_time,\n", "        cast((p.scheduled_time +interval '330' minute) as date) as dispatch_date,\n", "        cast((p.scheduled_time +interval '330' minute) as time) as dispatch_time\n", "        -- p.id,\n", "        -- coalesce((e.updated_at+interval '330' minute),(p.billed_at+interval '330' minute)) as picklist_billed_ts,\n", "        -- coalesce(extract(hour from (e.updated_at+interval '330' minute)),extract(hour from (p.billed_at+interval '330' minute))) as hour,\n", "        -- count(distinct plcm.container_id) as crates\n", "   FROM wms.pick_list p\n", "   JOIN wms.pick_list_item pi ON pi.pick_list_id = p.id \n", "   JOIN wms.pick_list_item_order_mapping w ON w.pick_list_item_id = pi.id\n", "   JOIN po.sto s ON s.id = cast(w.order_id as int) \n", "     and s.source_entity_vendor_id not in \n", "                (select distinct entity_vendor_id \n", "                from retail.outlet_entity_vendor_mapping o\n", "                join vms.vms_vendor v on v.id = o.entity_vendor_id \n", "                and v.vendor_name like '%%BCPL%%')\n", "    join wms.pick_list_container_mapping plcm on p.id=plcm.pick_list_id\n", "    left join wms.ob_pkg_entity e on p.id=cast(e.external_entity_id as integer)\n", "    and e.state='QC_COMPLETED'\n", "    and e.insert_ds_ist between cast(current_date - interval '16' day as varchar) and cast(current_date + interval '1' day as varchar)\n", "WHERE p.insert_ds_ist between cast(current_date - interval '16' day as varchar) and cast(current_date + interval '1' day as varchar)\n", "    AND pi.insert_ds_ist between cast(current_date - interval '16' day as varchar) and cast(cast(current_date as date) + interval '1' day as varchar)\n", "    AND s.created_at>= (cast(current_date as timestamp) - interval '16' day)\n", "    AND s.created_at< cast(current_date as timestamp) \n", "    AND plcm.insert_ds_ist between cast(current_date - interval '16' day as varchar) and cast(cast(current_date as date) + interval '1' day as varchar)\n", "    AND p.assigned_to <> ''\n", "         \n", "\"\"\"\n", "\n", "billed_df = read_sql_query(billed_crates, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "e98327dd-9746-429d-a1bd-7a4fb92cb92c", "metadata": {}, "outputs": [], "source": ["billed_df[\"scheduled_dispatch_time\"] = pd.to_datetime(billed_df[\"scheduled_dispatch_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "530ef018-7ad4-40ac-b09c-705913b6e43d", "metadata": {}, "outputs": [], "source": ["billed_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "21776345-c4af-45e1-bcb2-6bd03d0bb7c0", "metadata": {}, "outputs": [], "source": ["billed_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "3bf6f494-1442-45c7-8535-2e4a6667f6c6", "metadata": {}, "outputs": [], "source": ["billed_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "68855776-3205-4142-b43c-37e2c085e509", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a215d4a9-3660-490a-a66e-9f57da679c3f", "metadata": {}, "outputs": [], "source": ["loaded_crates = f\"\"\"\n", "\n", "with load_crate_base as\n", "    (\n", "    select  consignment_id,\n", "            c.external_id as crate_id,\n", "            lm1.outlet_id as wh_outlet_id,\n", "            lm2.outlet_id as ds_outlet_id,\n", "            (c.install_ts + interval '330' minute) as crate_loading_time\n", "    from transit_server.transit_consignment_container c\n", "    left join transit_server.transit_consignment t on c.consignment_id = t.id\n", "    left join retail.console_outlet_logistic_mapping lm1 on lm1.logistic_node_id = cast(t.source_store_id as int) and lm1.active = True \n", "    left join retail.console_outlet_logistic_mapping lm2 on lm2.logistic_node_id = cast(t.destination_store_id as int) and lm2.active = True \n", "    where c.insert_ds_ist > cast(current_date - interval '16' day as varchar)\n", "      and t.insert_ds_ist >= cast((current_date - interval '16' day) as varchar)\n", "      and c.state <> 'PENDING_AT_SOURCE'   --To remove cases of Partial crates\n", "    ),\n", "    \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time + interval '330' minute) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '16' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    )\n", "\n", "select  lb.*,\n", "        dispatch_time as scheduled_dispatch_time,\n", "        cast(dispatch_time as date) as dispatch_date,\n", "        cast(dispatch_time as time) as dispatch_time\n", "from pos\n", "join load_crate_base lb on lb.consignment_id = pos.csmt_id\n", "where dispatch_time is not null\n", "         \n", "\"\"\"\n", "\n", "loaded_df = read_sql_query(loaded_crates, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "04a52dd9-fdfa-4b4b-b479-4df918e77835", "metadata": {}, "outputs": [], "source": ["loaded_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "32cf64d8-c3cb-4452-9cc3-ab91627c5aca", "metadata": {}, "outputs": [], "source": ["loaded_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "93838536-9ae1-4473-8050-4cfcb85cb446", "metadata": {}, "outputs": [], "source": ["loaded_df[\"crate_loading_time\"] = pd.to_datetime(loaded_df[\"crate_loading_time\"])\n", "loaded_df[\"scheduled_dispatch_time\"] = pd.to_datetime(loaded_df[\"scheduled_dispatch_time\"])"]}, {"cell_type": "code", "execution_count": null, "id": "23eecd56-a614-45f6-89b7-002ce2f63522", "metadata": {}, "outputs": [], "source": ["loaded_df.consignment_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "6736f9c0-5dec-4412-a8e0-9dbcd60dd7d6", "metadata": {}, "outputs": [], "source": ["loaded_df.scheduled_dispatch_time.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "c8d5ac96-48c4-42b3-bfd9-2d868f853846", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98bb9c70-da2f-481d-89c8-4155b1d4c3d4", "metadata": {}, "outputs": [], "source": ["## To calculate the Loading Fill Rate\n", "\n", "df1 = sqldf(\n", "    \"\"\"\n", "with base as\n", "    (\n", "    select  --sd.date,\n", "            consignment_id,\n", "            sd.outlet_id,\n", "            sd.outlet_name,\n", "            count(distinct sd.container_id) as total_crates,\n", "            count(distinct case when crate_loading_time <= ld.scheduled_dispatch_time then ld.crate_id end) as load_crates_before_sdt,\n", "            count(distinct case when crate_loading_time > ld.scheduled_dispatch_time  then ld.crate_id end) as load_crates_after_sdt,\n", "            count(distinct case when crate_loading_time > ld.scheduled_dispatch_time and crate_loading_time <= datetime(ld.scheduled_dispatch_time, '+360 minutes') then ld.crate_id end) as load_crates_after_sdt_0_6\n", "\n", "    from sorted_df sd\n", "    left join loaded_df ld on sd.container_id = ld.crate_id \n", "     and sd.dispatch_date = ld.dispatch_date\n", "     and sd.dispatch_time = ld.dispatch_time\n", "     and sd.outlet_id = ld.wh_outlet_id\n", "    group by 1,2,3\n", "    )\n", "    \n", "select  *,\n", "        load_crates_before_sdt + load_crates_after_sdt_0_6 as total_loaded_crates_till_sdt_6,\n", "        total_crates - (load_crates_before_sdt+load_crates_after_sdt_0_6) as actual_spillover_crates,\n", "        total_crates - (load_crates_before_sdt+load_crates_after_sdt) as total_spillover_crates\n", "from base\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "edbfda6e-d41a-44a5-a729-c7bc104f4b81", "metadata": {}, "outputs": [], "source": ["df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a74b7d72-2ec7-4b5c-a634-b1eae6a37636", "metadata": {}, "outputs": [], "source": ["df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "397cae94-8b78-4744-b91a-e0f174e85979", "metadata": {}, "outputs": [], "source": ["df1.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "47c3e474-2c1a-4611-b919-d0c64e3e5bf5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab3bdaac-4deb-40ea-94b8-a16a36d6f585", "metadata": {}, "outputs": [], "source": ["x = df1.groupby([\"consignment_id\"], as_index=False).agg(\n", "    {\"total_crates\": \"sum\", \"total_loaded_crates_till_sdt_6\": \"sum\"}\n", ")\n", "x[x[\"total_loaded_crates_till_sdt_6\"] == 0].shape[0] * 100 / transit_df.consignment_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "7b271958-59f9-4b53-bd3b-896c399fa77b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6d41121a-0375-4ee3-9da8-4b09a5552c95", "metadata": {}, "outputs": [], "source": ["final_data1 = final.merge(df1, how=\"left\", on=[\"consignment_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5dc5b5bd-80d5-4ae2-9540-db219bab22e4", "metadata": {}, "outputs": [], "source": ["final_data1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5e040e37-ffdc-41d0-834e-24a685ca3d1e", "metadata": {}, "outputs": [], "source": ["# x=final_data1.groupby(['wh_outlet_name','consignment_date'], as_index=False)['trip_id'].nunique()\n", "# x.head(60)"]}, {"cell_type": "code", "execution_count": null, "id": "45a7852a-1733-4ac6-9284-ee9887f38e82", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "599fcb62-9acf-4c08-95da-d126ce09a1b6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "13153261-b7f9-434d-9b22-68de92c3122b", "metadata": {}, "outputs": [], "source": ["final_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "73114e37-fbf8-4e43-9580-a6148fd1be37", "metadata": {}, "outputs": [], "source": ["final_data1[final_data1[\"total_crates\"].isna() == True].wh_outlet_name.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "12896b7f-e5ab-4c75-a743-a8a9d125bcee", "metadata": {}, "outputs": [], "source": ["test = final_data1[\n", "    (final_data1[\"wh_outlet_name\"] == \"Bengaluru B3 - Feeder\") & final_data1[\"total_crates\"].isna()\n", "    == True\n", "]\n", "test.consignment_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ff03e9ac-4f76-4d0c-a3c9-c491fbc918cf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "89e6cb6a-45ea-4abe-a9b2-b315cf86f18f", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a1e79475-cefa-4eb0-9888-652e73bc33db", "metadata": {}, "source": ["### To get Consignments of WHs where Sortation flow is not live - Get Billed Crates for them"]}, {"cell_type": "code", "execution_count": null, "id": "27d25c3b-f397-473e-917a-0d83928d5d1d", "metadata": {}, "outputs": [], "source": ["## To get Consignments of WHs where Sortation flow is not live\n", "\n", "con = sqldf(\n", "    \"\"\"\n", "select distinct consignment_id\n", "from final_data1 \n", "where total_crates is null\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6f4043c2-9fe6-41ee-8231-f1827187caf4", "metadata": {}, "outputs": [], "source": ["con.shape"]}, {"cell_type": "code", "execution_count": null, "id": "963d3607-014d-4b87-a0f1-73e3a60bb98f", "metadata": {}, "outputs": [], "source": ["billed_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "046914a0-eca3-43d8-b6aa-c351dab61c47", "metadata": {}, "outputs": [], "source": ["## To calculate the Loading Fill Rate for WHs where Sortation flow is not Live\n", "\n", "df2 = sqldf(\n", "    \"\"\"\n", "with base as\n", "    (\n", "    select  --sd.date,\n", "            consignment_id,\n", "            sd.outlet_id,\n", "            sd.outlet_name,\n", "            count(distinct sd.container_id) as total_crates,\n", "            count(distinct case when crate_loading_time <= ld.scheduled_dispatch_time then ld.crate_id end) as load_crates_before_sdt,\n", "            count(distinct case when crate_loading_time > ld.scheduled_dispatch_time  then ld.crate_id end) as load_crates_after_sdt,\n", "            count(distinct case when crate_loading_time > ld.scheduled_dispatch_time and crate_loading_time <= datetime(ld.scheduled_dispatch_time, '+360 minutes') then ld.crate_id end) as load_crates_after_sdt_0_6\n", "\n", "    from billed_df sd\n", "    left join loaded_df ld on sd.container_id = ld.crate_id \n", "     and sd.dispatch_date = ld.dispatch_date\n", "     and sd.dispatch_time = ld.dispatch_time\n", "     and sd.outlet_id = ld.wh_outlet_id\n", "    where consignment_id in (select distinct consignment_id from con)\n", "    group by 1,2,3\n", "    )\n", "    \n", "select  *,\n", "        load_crates_before_sdt + load_crates_after_sdt_0_6 as total_loaded_crates_till_sdt_6,\n", "        total_crates - (load_crates_before_sdt+load_crates_after_sdt_0_6) as actual_spillover_crates,\n", "        total_crates - (load_crates_before_sdt+load_crates_after_sdt) as total_spillover_crates\n", "from base\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "324b0a83-f3a1-4512-99c4-99d865900516", "metadata": {}, "outputs": [], "source": ["df2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b3e66dcc-59c0-4b0a-b0ab-5e0018a8f483", "metadata": {}, "outputs": [], "source": ["df2.consignment_id.nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "6f2e4d13-2c39-4b2a-94d2-62c9c4b40090", "metadata": {}, "outputs": [], "source": ["df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9ace921d-b035-46ec-8c02-a3357891e06a", "metadata": {}, "outputs": [], "source": ["df2.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b5edcfa0-75a1-49d1-b7c5-01182b574c75", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f3351c11-ff39-4d41-aa02-6ac80c74f16d", "metadata": {}, "outputs": [], "source": ["final_data2 = final.merge(df2, how=\"left\", on=[\"consignment_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "51e7ba20-7a08-4fe3-942c-f155af1db16b", "metadata": {}, "outputs": [], "source": ["final_data2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c7403407-5816-4fcc-a713-131b671daf31", "metadata": {}, "outputs": [], "source": ["final_data1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "874cb4f8-3fde-4034-9f17-5fa9e3231589", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "de50913a-506d-4065-a73a-543f0186a950", "metadata": {}, "outputs": [], "source": ["# x=final_data2.groupby(['wh_outlet_name','consignment_date'], as_index=False)['trip_id'].nunique()\n", "# x.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "6458c4c7-f8c5-47ec-9ec5-885a83d53e11", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "6125004c-90f1-4a34-bc2b-bf5d2ee90d65", "metadata": {}, "source": ["### Separating consignments having Sorted data and ones having only Billed data"]}, {"cell_type": "code", "execution_count": null, "id": "b422c8de-b88b-473c-ada5-9165d546f566", "metadata": {}, "outputs": [], "source": ["billed_con = final_data2[final_data2[\"total_crates\"].isna() == False].consignment_id.tolist()\n", "len(billed_con)"]}, {"cell_type": "code", "execution_count": null, "id": "90010ff1-dc27-409b-bc87-5e318081f7a7", "metadata": {}, "outputs": [], "source": ["final_data1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "609e47ef-5250-4fab-81ef-e630d85ad31e", "metadata": {}, "outputs": [], "source": ["final_data3 = final_data1[~(final_data1[\"consignment_id\"].isin(billed_con))]\n", "final_data3.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d274c529-bc78-4387-89ea-70639b8a6f99", "metadata": {}, "outputs": [], "source": ["final_data4 = final_data2[(final_data2[\"consignment_id\"].isin(billed_con))]\n", "final_data4.shape"]}, {"cell_type": "code", "execution_count": null, "id": "835ec4cd-92d5-42ec-b97d-794c0409c547", "metadata": {}, "outputs": [], "source": ["final_data = pd.concat([final_data3, final_data4])\n", "final_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "67a14835-63ba-449a-bb6b-42d9f671f250", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fd1b57cd-2c60-4e16-ad5a-30c20191d55f", "metadata": {}, "outputs": [], "source": ["final_data[final_data[\"total_crates\"].isna() == True].wh_outlet_name.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "f010d020-5e12-4ac5-ad71-c73c33f61d89", "metadata": {}, "outputs": [], "source": ["final_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ed08c42a-a0a3-4f58-b372-185f3db<PERSON><PERSON>e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "79bd045f-3f45-492e-9923-247c96142a8f", "metadata": {}, "outputs": [], "source": ["# agg_data[(agg_data['consignment_date']=='2024-05-30') & (agg_data['wh_outlet_name']=='Bengaluru B3 - Feeder')]"]}, {"cell_type": "code", "execution_count": null, "id": "67c104cd-0a0f-46be-8466-0543cad80438", "metadata": {}, "outputs": [], "source": ["# x=final_data.groupby(['wh_outlet_name','consignment_date'], as_index=False)['total_spillover_crates'].sum()\n", "# x.total_spillover_crates.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "fd5cb879-3d5e-483a-a02b-7ddef98f80fa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b7fb9079-b82e-4cb9-bfa2-0b171da0ce3c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "67ce4ee6-56e7-4086-ba0d-35fd0932c24b", "metadata": {}, "outputs": [], "source": ["## Getting the aggregated View of Trips\n", "\n", "agg_data = sqldf(\n", "    \"\"\"\n", "\n", "with base as\n", "    (\n", "    select  *,\n", "            case when vehicle_report_status = 'reporting_ontime'\n", "                  and load_start_status = 'load_start_ontime'\n", "                  and dispatch_delay_status = 'dispatch_ontime'\n", "                  --and load_crates_before_sdt = total_crates\n", "                 then 'perfect_trip' else 'normal_trip'\n", "            end as trip_success\n", "    from final_data\n", "    --where total_crates is not null\n", "    )\n", "    \n", "select  consignment_date,\n", "        facility_id,\n", "        wh_outlet_id,\n", "        wh_outlet_name,\n", "        ds_outlet_id,\n", "        ds_outlet_name,\n", "        \n", "        count(distinct trip_id) as total_trips,\n", "        sum(total_crates_dispatched) as total_crates_dispatched,\n", "        sum(loading_time_at_wh) as total_loading_time_at_wh,\n", "        avg(loading_time_at_wh) as avg_loading_time_at_wh,\n", "        avg(case when idle_time_unload_comp_load_start is not null  then idle_time_unload_comp_load_start end) as idle_time_unload_comp_load_start,\n", "        avg(case when idle_time_checkin_unload_start is not null  then idle_time_checkin_unload_start end) as idle_time_checkin_unload_start,\n", "\n", "        \n", "        count(distinct case when vehicle_report_status = 'reporting_ontime' then trip_id end) as ontime_reported_trips,\n", "        count(distinct case when load_start_status = 'load_start_ontime' then trip_id end) as ontime_loaded_trips,\n", "        count(distinct case when dispatch_delay_status = 'dispatch_ontime' then trip_id end) as ontime_dispatched_trips,\n", "        count(distinct case when trip_success = 'perfect_trip' then trip_id end) as perfect_trips,\n", "        count(distinct case when enroute_tat_breach_trip = 'breach' then trip_id end) as enroute_tat_breach_trips,\n", "        count(distinct case when ds_reach_delay_status = 'Delay' then trip_id end) as ds_otd_breach_trips,\n", "        \n", "        sum(total_crates) as total_crates_created_for_sdt,\n", "        sum(load_crates_before_sdt) as total_loaded_crates_before_sdt,\n", "        sum(total_loaded_crates_till_sdt_6) as total_loaded_crates_till_sdt_6,\n", "        sum(actual_spillover_crates) as total_actual_spillover_crates,\n", "        sum(total_spillover_crates) as total_spillover_crates,\n", "        count(distinct case when load_crates_before_sdt = total_crates then trip_id end) as loaded_before_sdt_trips\n", "        \n", "from base\n", "group by 1,2,3,4,5,6\n", "\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1d60aa6b-6457-4d70-ad88-2b130e5d9aa9", "metadata": {}, "outputs": [], "source": ["agg_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "13e5b45f-0631-4c66-bbc3-6b3255add4ac", "metadata": {}, "outputs": [], "source": ["agg_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "269bc970-5d15-456a-a6d1-bb53280c253f", "metadata": {}, "outputs": [], "source": ["agg_data[agg_data[\"total_actual_spillover_crates\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "407867a1-8472-4ce0-be7a-ce5287b15910", "metadata": {}, "outputs": [], "source": ["agg_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "70c9d363-2192-4de7-b146-cc23869459f9", "metadata": {}, "outputs": [], "source": ["# agg_data.to_csv(\"agg_data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a27f83c0-2f19-4b1f-8ea7-98fd123599a4", "metadata": {}, "outputs": [], "source": ["# agg_data = pd.read_csv(\"agg_data.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "36e13609-4410-4981-ace3-1fb0227ecf93", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0df89d21-2f6e-42fc-801e-aae244a06640", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b5f60cd3-c963-421a-a51c-c6964be89a5f", "metadata": {}, "source": ["## GMV Loss due to Truncation Drop"]}, {"cell_type": "code", "execution_count": null, "id": "aac7d4b1-c87f-4345-b117-561586be6772", "metadata": {}, "outputs": [], "source": ["gmv_loss = f\"\"\"\n", "with outlet_details as\n", "    (\n", "        select  hot_outlet_id as outlet_id,\n", "                facility_id,\n", "                facility_name\n", "        from supply_etls.outlet_details\n", "        group by 1,2,3\n", "    )\n", "\n", "select  date_ist as consignment_date, od.facility_id,--od.facility_name,--rr.backend_outlet_id,\n", "        rr.outlet_id as ds_outlet_id,\n", "        --od.outlet_id,\n", "        sum(case when truncation_flag = 'truck_load_drop' and truncation_loss > 0  then gmv_loss_qty end) as gmv_loss_qty_fleet,\n", "        sum(case when truncation_flag = 'truck_load_drop' and truncation_loss > 0  then gmv_loss_value end) as gmv_loss_value_fleet,\n", "        sum(gmv_loss_value) as gmv_loss_value\n", "from supply_etls.store_item_unavailability_reasons rr\n", "left join outlet_details od on od.outlet_id = rr.backend_outlet_id\n", "where date_ist >= current_date - interval '14' day\n", "and date_ist >= cast((current_date - interval '14' day) as date) and od.facility_id is not null\n", "group by 1,2,3 \n", "\"\"\"\n", "\n", "gmv_loss_df = read_sql_query(gmv_loss, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "e44a7107-41de-40b4-a60e-75225542ee2c", "metadata": {}, "outputs": [], "source": ["gmv_loss_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "95c67e00-c826-4dc1-a704-78159f565860", "metadata": {}, "outputs": [], "source": ["gmv_loss_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ab9525e6-a814-4683-86d5-0cc31c2fd9b5", "metadata": {}, "outputs": [], "source": ["gmv_loss_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5b92f97a-1c35-4d47-919b-a0c743b3b141", "metadata": {}, "outputs": [], "source": ["gmv_loss_df[gmv_loss_df[\"facility_id\"].isna() == True]"]}, {"cell_type": "code", "execution_count": null, "id": "0f6232d2-9df1-4d82-b284-b4e2471171b0", "metadata": {}, "outputs": [], "source": ["gmv_loss_df[\"ds_outlet_id\"] = gmv_loss_df[\"ds_outlet_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "0636bb00-e6db-4b73-b985-fa0ec10714f9", "metadata": {}, "outputs": [], "source": ["gmv_loss_df[\"facility_id\"] = gmv_loss_df[\"facility_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "9e7f8314-16d9-4e13-8639-d8cca5aa23d6", "metadata": {}, "outputs": [], "source": ["agg_data[\"consignment_date\"] = pd.to_datetime(agg_data[\"consignment_date\"])\n", "gmv_loss_df[\"consignment_date\"] = pd.to_datetime(gmv_loss_df[\"consignment_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "9a016249-f27c-4e9c-8866-f2baaa213479", "metadata": {}, "outputs": [], "source": ["agg_gmv_data = agg_data.merge(\n", "    gmv_loss_df, how=\"left\", on=[\"consignment_date\", \"facility_id\", \"ds_outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a7092f62-059a-4966-b383-28a1d2569896", "metadata": {}, "outputs": [], "source": ["agg_gmv_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "58baebf2-e8a7-4a2b-938f-9db8b65c1261", "metadata": {}, "outputs": [], "source": ["agg_gmv_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5759d232-6b09-43ac-8176-a8979ebe34e9", "metadata": {}, "outputs": [], "source": ["agg_gmv_b3 = agg_gmv_data[(agg_gmv_data[\"wh_outlet_name\"] == \"Bengaluru B3 - Feeder\")]\n", "agg_gmv_b3.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b1e612a4-b05f-4a77-825f-8d96137d0e20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9deec824-60b5-473d-a70e-656ad7316681", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07476db8-2a97-47e9-ae5f-ab20cd293756", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e9f05be2-43b0-4658-a011-14d70234d325", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25f82f63-8d56-4a05-b19e-357fdbf7f328", "metadata": {}, "outputs": [], "source": ["agg_gmv_data.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "66fe983a-4a92-492d-9688-da2d0749c0a6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "7b36fe2b-4e7c-4383-881f-4e103bd6135e", "metadata": {}, "source": ["### CPU"]}, {"cell_type": "code", "execution_count": null, "id": "cd0f5c5b-9cbb-4058-844e-0940bc8663c5", "metadata": {}, "outputs": [], "source": ["cpu_query = f\"\"\"\n", "\n", "with valid_ars_job_runs_extract as\n", "    (\n", "        select  distinct run_id, \n", "                cast(json_extract(simulation_params,'$.run') as varchar) as run_type,\n", "                cast(json_extract(simulation_params,'$.mode') as varchar) as mode \n", "        from ars.job_run \n", "        where run_id not in ('20210607116002', '20210607116006', '20210607116005', '20210607116003')\n", "            and started_at > cast((current_date-interval '180' day) as timestamp) \n", "            and completed_at is not null\n", "    ),\n", "\n", "valid_ars_job_runs as \n", "    (\n", "        select  run_id,\n", "                run_type,\n", "                mode\n", "        from valid_ars_job_runs_extract\n", "        where run_type in ('ars_lite')\n", "            --and (mode in ('','frozen','milk','fnv','perishable') or mode is null)\n", "            and (mode in ('') or mode is null)\n", "            -- removing things like stocking level runs, ordering runs, reverse flow runs\n", "    ),\n", "                \n", "truck_caps as \n", "    (\n", "        select  distinct backend_outlet_id, \n", "                frontend_outlet_id, \n", "                run_id, \n", "                truck_load_capacity \n", "        from ars.bulk_facility_transfer_days\n", "        where truck_load_capacity > 0\n", "    ),\n", "    \n", "runs AS\n", "(   SELECT run_id\n", "    FROM ars.job_run\n", "    where date(started_at+interval'5'hour+interval'30'minute) >= current_date-interval'22'day\n", "    AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "    AND simulation_params not like '%%mode%%'\n", "    AND simulation_params not like '%%hyperpure_run%%'\n", "    AND simulation_params not like '%%any_day_po%%'    \n", "    AND simulation_params not like '%%spr_migration_simulation%%' \n", "    order by 1),\n", "    \n", "ars_indent as \n", "    (\n", "        select  run_id, \n", "                backend_outlet_id, \n", "                frontend_outlet_id,\n", "                sto_date,\n", "                sum(weight_in_gm*sto_quantity_post_truncation_in_case) as total_weight,\n", "                sum(sto_quantity_post_truncation_in_case) as total_indent,\n", "                avg(item_factor) as avg_item_factor\n", "        from ars.transfers_optimization_results_v2 as indent\n", "        inner join rpc.item_details id ON id.item_id = indent.item_id AND id.active = 1 AND id.approved = 1\n", "        left join supply_etls.item_factor fac on fac.item_id = indent.item_id\n", "        where indent.insert_ds_ist >= cast((CURRENT_DATE - interval '22' DAY) as varchar)\n", "          and run_id in (select run_id from runs) \n", "        group by 1,2,3,4\n", "    ),\n", "\n", "base as \n", "    (\n", "        select  awu.sto_date,\n", "                awu.run_id,\n", "                awu.backend_outlet_id, \n", "                awu.frontend_outlet_id,\n", "                jr.run_type,\n", "                jr.mode,\n", "                truck_load_capacity,\n", "                cast(total_weight as decimal)/1000 as total_weight_kg,\n", "                total_indent,\n", "                avg_item_factor\n", "        from ars_indent as awu\n", "        inner join valid_ars_job_runs jr on jr.run_id = awu.run_id\n", "        inner join truck_caps tc on awu.run_id = tc.run_id and awu.backend_outlet_id = tc.backend_outlet_id and awu.frontend_outlet_id = tc.frontend_outlet_id\n", "    ),\n", "    \n", "cost_base AS\n", "    (\n", "        with base as \n", "            (\n", "            select  *,\n", "                    case \n", "                        when fleet_type like '%%ixe%%' then (vehicle_count * cast((kms_slab/days_in_month) as decimal))\n", "                        else (vehicle_count * cast(kms_slab as decimal))\n", "                    end as planned_kms_slab\n", "            from \n", "                (\n", "                select  date_,\n", "                        case \n", "                            when extract(month from date(date_)) in  (1,3,5,7,8,10,12) then 31\n", "                            when extract(month from date(date_)) in  (4,6,9,11) then 30\n", "                            else 28\n", "                        end as days_in_month,\n", "                        case when source_facility_id = 4181 then 1876\n", "                             when source_facility_id = 4325 then 2006\n", "                             else source_facility_id\n", "                        end as source_facility_id_,\n", "                        source_name,\n", "                        destination_outlet_id,\n", "                        destination_name,\n", "                        fleet_type,\n", "                        vehicle_number as truck_num,\n", "                        date_cost,\n", "                        cast(date_cost AS decimal)*1.00 / cast(daily_trip_cost AS decimal) as vehicle_count,\n", "                        kms_slab_as_per_loi as kms_slab\n", "                from supply_etls.blinkit_middle_mile_fleet_cost\n", "                where date_ >= CAST((CURRENT_DATE - interval '22' DAY) AS DATE)\n", "                  and daily_trip_cost > 0\n", "                  and category = 'Grocery'\n", "                ) cte\n", "            )\n", "            \n", "        select  date_,\n", "                source_facility_id_,\n", "                --destination_outlet_id,\n", "                --destination_name,\n", "                --truck_num,\n", "                -- sum(planned_kms_slab) as planned_kms,\n", "                sum(date_cost) as daily_date_cost\n", "        from base\n", "        group by 1,2\n", "    ),\n", "    \n", "final_base as\n", "    (\n", "    select  cte.date_,\n", "            sender_outlet_id,\n", "            sender_outlet_name,\n", "            sender_facility_id,\n", "            receiver_outlet_id,\n", "            -- receiver_outlet_name,\n", "            -- receiver_facility_id,\n", "            --city,\n", "            cs.daily_date_cost,\n", "            -- mode,\n", "            mode_new,\n", "            truck_load,\n", "            indent_weight,\n", "            indent\n", "    from\n", "        (\n", "        select  date(sto_date) as date_,\n", "                backend_outlet_id as sender_outlet_id,\n", "                co1.name as sender_outlet_name,\n", "                co1.facility_id as sender_facility_id,\n", "                frontend_outlet_id as receiver_outlet_id,\n", "                --co2.name as receiver_outlet_name,\n", "                --co2.facility_id as receiver_facility_id,\n", "                --case when trim(co2.location) = 'Bangalore' then 'Bengaluru' else trim(co2.location) end as city,\n", "                case when mode is null then 'Grocery'\n", "                     when mode in ('fnv','milk','perishable') then 'Perishable/FnV'\n", "                     when mode = 'frozen' then 'Cold/Frozen'\n", "                end as mode_new,\n", "                sum(truck_load_capacity) as truck_load,\n", "                sum(total_weight_kg) as indent_weight,\n", "                sum(total_indent) as indent,\n", "                avg(avg_item_factor) as avg_item_factor\n", "        from base b\n", "        join lake_retail.console_outlet co1 on b.backend_outlet_id = co1.id and business_type_id in (1,12)\n", "        join lake_retail.console_outlet co2 on b.frontend_outlet_id = co2.id\n", "        group by 1,2,3,4,5,6\n", "        having sum(truck_load_capacity) > 0\n", "        ) cte\n", "    left join cost_base cs on cs.date_ = cte.date_ and cs.source_facility_id_ = cte.sender_facility_id --and cs.destination_outlet_id = cte.receiver_outlet_id \n", "    where indent_weight <> 0\n", "      and cte.date_ >= cast((current_date-interval '21' day) as date) and cte.date_ < cast(current_date as date)\n", "    order by date_, sender_facility_id , receiver_outlet_id\n", "    )\n", "    \n", "select  date_ as consignment_date,\n", "        sender_facility_id as facility_id,\n", "        receiver_outlet_id as ds_outlet_id,\n", "        sum(daily_date_cost) as daily_date_cost,\n", "        sum(indent) as indent\n", "from final_base\n", "group by 1,2,3\n", "\n", "         \n", "\"\"\"\n", "\n", "cpu_df = read_sql_query(cpu_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "58b58f7b-45c6-4adb-beef-ab329cbf0058", "metadata": {}, "outputs": [], "source": ["cpu_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b76b91b7-7749-432f-b437-29eac78717a9", "metadata": {}, "outputs": [], "source": ["cpu_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d114c0c3-de45-4e79-a142-2ddf858a6bb1", "metadata": {}, "outputs": [], "source": ["cpu_df[\"consignment_date\"] = pd.to_datetime(cpu_df[\"consignment_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "0b4723c8-0404-4258-b47a-9d40b2ebe680", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3d991fca-60e6-4d5f-bf54-330df2adb9ec", "metadata": {}, "outputs": [], "source": ["etl_data1 = agg_gmv_data.merge(\n", "    cpu_df, how=\"left\", on=[\"consignment_date\", \"facility_id\", \"ds_outlet_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6a780e34-85a4-4f10-a94c-4b878233d0a8", "metadata": {}, "outputs": [], "source": ["etl_data1[\"cpu\"] = etl_data1[\"daily_date_cost\"] / etl_data1[\"indent\"]"]}, {"cell_type": "code", "execution_count": null, "id": "deb00c41-6da6-4fa6-9037-56bf9a052db7", "metadata": {}, "outputs": [], "source": ["etl_data1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ebc95b2f-e25e-4ec8-8c2a-ab6022bbdb7f", "metadata": {}, "outputs": [], "source": ["etl_data1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bc1fd9b3-a709-4e7b-ac0e-7c624a89cd82", "metadata": {}, "outputs": [], "source": ["# etl_data.to_csv(\"etl_data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a0192c75-ce6a-42b4-b103-99415e359d37", "metadata": {}, "outputs": [], "source": ["etl_data1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ccecad22-8e57-40ac-bb7e-4c6ce2a45233", "metadata": {}, "outputs": [], "source": ["etl_data1[etl_data1[\"total_actual_spillover_crates\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "e1aa54a5-3e1c-4bc1-8f30-fb0965ae3ca4", "metadata": {}, "outputs": [], "source": ["etl_data = sqldf(\n", "    \"\"\"\n", "with base as\n", "    (\n", "    select *,\n", "            lag(total_actual_spillover_crates,1) over (partition by wh_outlet_id,ds_outlet_id order by consignment_date) as rolling_crate\n", "    from etl_data1\n", "    )    \n", "select  *,\n", "        (rolling_crate+total_crates_created_for_sdt-total_crates_dispatched) as spillover\n", "from base \n", "    \n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b3447002-e2f6-491b-927f-ca31a6dbc21e", "metadata": {}, "outputs": [], "source": ["etl_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4fce6e8d-3a12-409f-9db6-3d73e87e234d", "metadata": {}, "outputs": [], "source": ["etl_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bec568f6-b667-4a23-97b5-acc16195bb27", "metadata": {}, "outputs": [], "source": ["# etl_data.to_csv('etl_data.csv',index='false')"]}, {"cell_type": "code", "execution_count": null, "id": "91ab3226-da24-4f1c-9afe-3f1ccfbf38ca", "metadata": {}, "outputs": [], "source": ["etl_data[\"gmv_loss_value\"] = etl_data[\"gmv_loss_value\"].fillna(value=0)\n", "etl_data[\"gmv_loss_value_fleet\"] = etl_data[\"gmv_loss_value_fleet\"].fillna(value=0)\n", "etl_data[\"gmv_loss_qty_fleet\"] = etl_data[\"gmv_loss_qty_fleet\"].fillna(value=0)\n", "etl_data[\"total_crates_dispatched\"] = etl_data[\"total_crates_dispatched\"].fillna(value=0)\n", "etl_data[\"ds_otd_breach_trips\"] = etl_data[\"ds_otd_breach_trips\"].fillna(value=0)\n", "etl_data[\"enroute_tat_breach_trips\"] = etl_data[\"enroute_tat_breach_trips\"].fillna(value=0)\n", "etl_data[\"daily_date_cost\"] = etl_data[\"daily_date_cost\"].fillna(value=0)\n", "etl_data[\"indent\"] = etl_data[\"indent\"].fillna(value=0)\n", "etl_data[\"total_loading_time_at_wh\"] = etl_data[\"total_loading_time_at_wh\"].fillna(value=0)\n", "etl_data[\"avg_loading_time_at_wh\"] = etl_data[\"avg_loading_time_at_wh\"].fillna(value=0)\n", "etl_data[\"total_crates_created_for_sdt\"] = etl_data[\"total_crates_created_for_sdt\"].fillna(value=0)\n", "etl_data[\"total_loaded_crates_before_sdt\"] = etl_data[\"total_loaded_crates_before_sdt\"].fillna(\n", "    value=0\n", ")\n", "etl_data[\"total_loaded_crates_till_sdt_6\"] = etl_data[\"total_loaded_crates_till_sdt_6\"].fillna(\n", "    value=0\n", ")\n", "etl_data[\"total_actual_spillover_crates\"] = etl_data[\"total_actual_spillover_crates\"].fillna(\n", "    value=0\n", ")\n", "etl_data[\"total_spillover_crates\"] = etl_data[\"total_spillover_crates\"].fillna(value=0)\n", "etl_data[\"idle_time_unload_comp_load_start\"] = etl_data[\"idle_time_unload_comp_load_start\"].fillna(\n", "    value=0\n", ")\n", "etl_data[\"idle_time_checkin_unload_start\"] = etl_data[\"idle_time_checkin_unload_start\"].fillna(\n", "    value=0\n", ")\n", "\n", "etl_data[\"spillover\"] = etl_data[\"spillover\"].fillna(value=0)\n", "\n", "etl_data[\"gmv_loss_qty_fleet\"] = etl_data[\"gmv_loss_qty_fleet\"].astype(\"int\")\n", "etl_data[\"total_loading_time_at_wh\"] = etl_data[\"total_loading_time_at_wh\"].astype(\"int\")\n", "etl_data[\"total_crates_created_for_sdt\"] = etl_data[\"total_crates_created_for_sdt\"].astype(\"int\")\n", "etl_data[\"total_loaded_crates_before_sdt\"] = etl_data[\"total_loaded_crates_before_sdt\"].astype(\n", "    \"int\"\n", ")\n", "etl_data[\"total_loaded_crates_till_sdt_6\"] = etl_data[\"total_loaded_crates_till_sdt_6\"].astype(\n", "    \"int\"\n", ")\n", "etl_data[\"total_actual_spillover_crates\"] = etl_data[\"total_actual_spillover_crates\"].astype(\"int\")\n", "etl_data[\"total_spillover_crates\"] = etl_data[\"total_spillover_crates\"].astype(\"int\")\n", "etl_data[\"indent\"] = etl_data[\"indent\"].astype(\"int\")\n", "etl_data[\"avg_loading_time_at_wh\"] = etl_data[\"avg_loading_time_at_wh\"].astype(\"int\")\n", "etl_data[\"spillover\"] = etl_data[\"spillover\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a34f280-2c4c-46bd-8b7c-adfb3ed5c86c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f50a1ece-2a13-4f21-b03d-84271bb0b059", "metadata": {}, "outputs": [], "source": ["etl_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "16122d09-99a7-4e50-a1db-fad88b21a2e6", "metadata": {}, "outputs": [], "source": ["etl_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3384bb40-51cf-482b-92fe-2873f28df6c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5e90448e-2448-4995-8209-275e4f5e4bec", "metadata": {}, "outputs": [], "source": ["raw_data = \"1pxXz1gFudJduJHQYH9xQpry2-cFLHRwQqOQonfAAEck\""]}, {"cell_type": "code", "execution_count": null, "id": "ddb8b88d-2c9c-46e6-843a-aad03c66c73a", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(etl_data, raw_data, \"ds_raw_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "46bb4fc3-c15a-4925-b06c-ef3bbbe48cb0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a8e204fa-b3d9-4ca8-949e-3090ddf5e75d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "be498947-8187-4bb9-a203-bc8be9aaa312", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9ff01e52-883e-4245-927f-c0284231328d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fddd3edb-032c-4e20-a43a-800d090c0f24", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6b10b680-c9d0-49ca-80ff-fedffbf2e84f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c6fef17a-d9cc-4ce1-9803-d10960be5a4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0af8a6b8-f540-449e-9cd6-4a0cd06a88ac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d71cf8aa-ff18-4b52-a1a7-24f6d8fa0ac2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c167a7b1-bec7-4813-b2f5-924e31084b12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23dec6ec-596c-40a6-9297-fbd839174fad", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6cffc623-0e61-4c6e-9090-9bb9811b0e42", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "68acc626-096e-4f9c-9c2e-4718e93edba9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b6449e37-ef64-40aa-84fa-0781eb19c6b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "746e077c-611c-4e30-9fc6-412542fcb031", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c133ca6-0269-49ab-afda-e43a7a278177", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}