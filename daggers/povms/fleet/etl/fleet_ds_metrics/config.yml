alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: fleet_ds_metrics
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06M9JKD48G
path: povms/fleet/etl/fleet_ds_metrics
paused: true
pool: povms_pool
project_name: fleet
schedule:
  end_date: '2024-11-24T00:00:00'
  interval: 0 */6 * * *
  start_date: '2024-05-01T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
