{"cells": [{"cell_type": "code", "execution_count": null, "id": "8af98ff7-8b51-4c80-a95b-1f033490ea5c", "metadata": {}, "outputs": [], "source": ["!pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "id": "e2116e01-0507-4d32-8d6f-80ab8477841a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import datetime, timedelta, date\n", "from dateutil.relativedelta import relativedelta\n", "from pandasql import sqldf\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "345fbd8d-3b83-49fa-a1f7-8e23ee7e2b49", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "38a1ae3b-d715-4ae1-9790-e2fa9881b695", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "fc3e18e3-5c0c-487e-b380-0928c81a8869", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1efc715d-2f34-4a0e-a303-1329c934fe09", "metadata": {}, "outputs": [], "source": ["frwd_query = f\"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   --type,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "   \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          --type,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           --n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "    GROUP BY 1,2,3,4,5,6,7,t.metadata\n", "    ),\n", "        \n", "docs AS\n", "    (\n", "    SELECT  t.id AS consignment_id,\n", "            count(DISTINCT d.external_id) AS num_invoices,\n", "            count(DISTINCT c.external_id) AS total_crates_dispatched\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_document d ON d.consignment_id = t.id\n", "    JOIN transit_server.transit_consignment_container c ON c.consignment_id = t.id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "      AND c.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "    GROUP BY 1\n", "    ),\n", "   \n", "pos AS\n", "    (\n", "        SELECT  DISTINCT td.consignment_id AS csmt_id,\n", "                max(s.dispatch_time) AS dispatch_time\n", "        FROM pos.pos_invoice pi\n", "        JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "        JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "        JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "        AND co.business_type_id IN (1,12)\n", "        WHERE pi.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "          AND td.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) as varchar)\n", "        AND invoice_type_id IN (5,14,16)\n", "        AND s.created_by = 3511\n", "        group by 1\n", "    ),\n", "    \n", "ctype AS\n", "  (\n", "  SELECT consignment_id,\n", "         item_type AS con_type\n", "   FROM\n", "     (SELECT *,\n", "             RANK () OVER (PARTITION BY consignment_id ORDER BY type_count DESC) AS rnk\n", "      FROM\n", "        (SELECT DISTINCT c.id AS consignment_id,\n", "                         (CASE\n", "                              WHEN ids.perishable = 1 THEN 'Perishable'\n", "                              ELSE 'Grocery'\n", "                          END) AS item_type,\n", "                         count(CASE\n", "                                  WHEN ids.perishable = 1 THEN 'Perishable'\n", "                                  ELSE 'Grocery'\n", "                              END) AS type_count,\n", "                sum(pipd.quantity) as dispatched_qty,\n", "                sum(pipd.quantity*pp.weight_in_gm) as dispatched_qty_weight,\n", "                sum(pipd.quantity*ids.length_in_cm*ids.breadth_in_cm*ids.height_in_cm) as indent_volume\n", "         FROM transit_server.transit_consignment c\n", "         JOIN transit_server.transit_consignment_document d ON c.id = d.consignment_id\n", "         JOIN pos.pos_invoice pi ON pi.invoice_id = d.external_id\n", "         JOIN pos.pos_invoice_product_details pipd ON pipd.invoice_id = pi.id\n", "         JOIN rpc.product_product pp ON pp.variant_id = pipd.variant_id\n", "         JOIN rpc.item_details ids ON ids.item_id = pp.item_id\n", "         WHERE c.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) AS varchar)\n", "           AND d.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) AS varchar)\n", "           AND pi.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) AS varchar)\n", "           and pipd.insert_ds_ist >= cast((CURRENT_DATE - interval '14' DAY) AS varchar)\n", "           AND d.document_type = 'INVOICE'\n", "         GROUP BY 1,2)\n", "                 )\n", "   WHERE rnk = 1 \n", "   ),\n", "       \n", "\n", "final_base as\n", "    (\n", "    SELECT base.consignment_id,\n", "           con_type,\n", "           tm.trip_id,\n", "           --cast((dispatch_time + interval '330' MINUTE) as date) AS consignment_date,\n", "           cast((tm.truck_handshake + interval '330' MINUTE) as date) AS consignment_date,\n", "           CASE\n", "               WHEN facility_id = 1743 THEN 264\n", "               WHEN facility_id = 4306 THEN 1983\n", "               ELSE facility_id\n", "           END AS facility_id,\n", "           --facility_name,\n", "           ds_outlet_id,\n", "           ds_outlet_name,\n", "           truck_number,\n", "           truck_type,\n", "           tm.trip_state,\n", "           current_state AS consignment_state,\n", "           num_invoices,\n", "           total_crates_dispatched,\n", "           (dispatch_time + interval '240' MINUTE) AS reporting_threshold,\n", "           (dispatch_time + interval '285' MINUTE) AS loading_start_threshold,\n", "           \n", "           (dispatch_time + interval '270' MINUTE) AS scheduled_truck_arrival,\n", "           (tm.truck_entry_wh + interval '330' MINUTE) AS truck_entry_wh,\n", "           (tm.truck_handshake + interval '330' MINUTE) AS truck_handshake,\n", "           (loading_start + interval '330' MINUTE) AS loading_start,\n", "           (ready_for_dispatch + interval '330' MINUTE) AS ready_for_dispatch,\n", "           (enroute + interval '330' MINUTE) AS enroute,\n", "           (dispatch_time + interval '330' MINUTE) AS scheduled_dispatch_time,\n", "           (dispatch_time + interval '345' MINUTE) AS exp_dispatch_time,\n", "           (ds_reached + interval '330' MINUTE) AS ds_reached,\n", "           (unloading_start + interval '330' MINUTE) AS unload_start_ds,\n", "           (unloading_completed + interval '330' MINUTE) AS unload_complete_ds\n", "           --(tav.install_ts + interval '330' minute) as complete_time_last_trip\n", "           \n", "           \n", "    FROM base\n", "    left join docs on docs.consignment_id = base.consignment_id\n", "    LEFT JOIN ctype ON ctype.consignment_id = base.consignment_id\n", "    JOIN pos ON base.consignment_id = pos.csmt_id\n", "    LEFT JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "    --left join transit_server.transit_allocation_vehicle tav on tav.trip_id = tm.trip_id and activity_type = 'LOADING' \n", "    --and insert_ds_ist >= cast((current_date - interval '14' day) as varchar)\n", "\n", "    \n", "    ),\n", "    \n", "time_gaps_base as\n", "    (\n", "    select  final_base.*,\n", "            co.id as wh_outlet_id,\n", "            co.name as wh_outlet_name,\n", "            date_diff('minute', loading_start, ready_for_dispatch) as loading_time_at_wh,\n", "            date_diff('minute', exp_dispatch_time, ready_for_dispatch) as dispatch_delay_min,\n", "            \n", "            --case when return_time_of_previous_trip > reporting_threshold then 'reporting_delay' else 'reporting_ontime' \n", "            --end as vehicle_report_status,\n", "            --date_diff('minute', reporting_threshold, return_time_of_previous_trip) as reporting_delay_min,\n", "            \n", "            case when loading_start > loading_start_threshold then 'load_start_delay' else 'load_start_ontime' \n", "            end as load_start_status,  \n", "            date_diff('minute', ready_for_dispatch, ds_reached) as frwd_transit_time\n", "    from final_base\n", "    left join retail.console_outlet co on co.facility_id = final_base.facility_id and business_type_id in (1,12) and name not like '%%HOT%%' and name <> 'K3 T K4'\n", "    ),\n", "\n", "last_trip as\n", "    (\n", "        select  install_ts,\n", "            trip_id,\n", "            truck_number,\n", "            lag(trip_id,1) over (partition by truck_number order by install_ts) as last_trip_id\n", "    from transit_server.transit_projection_trip \n", "    where insert_ds_ist >= cast((CURRENT_DATE - interval '20' DAY) as varchar)\n", "      and state not in ('EXPIRED','CANCELLED')\n", "    ),\n", "    \n", "dock_assignment as\n", "    (\n", "    select  trip_id,\n", "            -- vehicle_type,\n", "            -- vehicle_number,\n", "            -- cast(json_extract(tav.metadata,'$.driver_name') as varchar) AS driver_name,\n", "            max(tavl2.label_value) as loading_dock_name,\n", "            max(tavl2.install_ts + interval '330' minute) as dock_assign_time_frwd\n", "    from transit_server.transit_allocation_vehicle tav\n", "    join transit_server.transit_allocation_vehicle_label tavl1 on tav.id = tavl1.allocation_vehicle_id and tavl1.label_type = 'EXPECTED_NODES'\n", "    join transit_server.transit_allocation_vehicle_label tavl2 on tav.id = tavl2.allocation_vehicle_id and tavl2.label_type = 'DOCK'\n", "    where activity_type = 'LOADING'\n", "      and tav.insert_ds_ist >= cast((current_date - interval '15' day) as varchar)\n", "      and tavl1.insert_ds_ist >= cast((current_date - interval '15' day) as varchar)\n", "      and tavl2.insert_ds_ist >= cast((current_date - interval '15' day) as varchar)\n", "    group by 1\n", "    ),\n", "    enroute_tat as\n", "    (\n", "    select con.consignment_id,\n", "           tl1.actual_ts as dispatch_ts,\n", "           tl1.actual_ts + (tl2.scheduled_ts - tl1.scheduled_ts) as arrival_eta_at_dispatch,\n", "           tl2.actual_ts as arrival_ts\n", "    from transit_server.transit_projection_consignment con\n", "        join transit_server.transit_projection_trip_event_timeline tl1 on con.trip_id = tl1.trip_id and tl1.event_type = 'LOADING_END' and con.consignment_type = json_extract_scalar(tl1.event_meta, '$.consignment_type')\n", "        join transit_server.transit_projection_trip_event_timeline tl2 on con.trip_id = tl2.trip_id and con.consignment_id = cast(json_extract_scalar(tl2.event_meta, '$.consignment_id') as int) and con.consignment_type = json_extract_scalar(tl2.event_meta, '$.consignment_type')\n", "        and tl2.event_type = 'DS_ARRIVAL'\n", "    where tl1.insert_ds_ist >= cast((current_date - interval '15' day) as varchar) and tl1.lake_active_record --and tl1.event_type = 'LOADING_END' \n", "        and tl2.insert_ds_ist >= cast((current_date - interval '15' day) as varchar) and tl2.lake_active_record \n", "        and con.insert_ds_ist >= cast((current_date - interval '15' day) as varchar)\n", "        and con.consignment_type = 'FORWARD_CONSIGNMENT'\n", "    )\n", "\n", "    \n", " select  \n", "        base.*,\n", "        dock_assign_time_frwd,\n", "        loading_dock_name,\n", "        last_trip_id,\n", "        (tet.actual_ts + interval '330' minute) as complete_time_last_trip,\n", "        case when dispatch_delay_min <= 0 then 'dispatch_ontime' else 'dispatch_delay' \n", "        end as dispatch_delay_status,\n", "        case when trim(co.location)='Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "        \n", "        CAST(scheduled_dispatch_time AS time) as sch_dispatch_time,\n", "        CAST(scheduled_dispatch_time AS date) as sch_dispatch_date,\n", "        (CASE\n", "              WHEN hour(CAST(scheduled_dispatch_time AS time)) < 12 THEN 'Day'\n", "              ELSE 'Night'\n", "        END) AS dispatch_slot,\n", "        cast(ds_reached as time) as ds_reach_time,\n", "        (case when arrival_ts > (arrival_eta_at_dispatch + interval '30' minute) then 'breach' else 'normal' end) as enroute_tat_breach_trip,\n", "        base.dispatch_delay_min as dispatch_delay_mins,\n", "        arrival_ts + interval '330' minute as arrival_ts,\n", "        arrival_eta_at_dispatch + interval '330' minute as arrival_eta_at_dispatch\n", "        \n", "        \n", "\n", "        \n", "        \n", "        \n", "\n", "from time_gaps_base base\n", "left join lake_retail.console_outlet co on co.id = base.ds_outlet_id\n", "left join last_trip lt on base.trip_id=lt.trip_id\n", "left join dock_assignment da on da.trip_id = base.trip_id\n", "left join transit_server.transit_projection_trip_event_timeline tet on tet.trip_id = lt.last_trip_id and event_type = 'COMPLETED'\n", " and tet.lake_active_record and insert_ds_ist >= cast((current_date - interval '15' day) as varchar)\n", "left join enroute_tat et on et.consignment_id = base.consignment_id\n", "where UPPER(wh_outlet_name) not like '%%PC%%'\n", "  and trip_state <> 'CANCELLED'\n", "  and con_type = 'Grocery'\n", "  and truck_type not like '%%Reefer%%'\n", "  \n", "\"\"\"\n", "\n", "frwd_df = read_sql_query(frwd_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "e4619c2f-2513-4787-bb7d-f02025c53ea3", "metadata": {}, "outputs": [], "source": ["frwd_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1fb98b9f-83c6-4f17-8e81-95d57a13ea27", "metadata": {}, "outputs": [], "source": ["frwd_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d83039f3-30a5-4482-966e-b0c0232ba122", "metadata": {}, "outputs": [], "source": ["# enroute diff"]}, {"cell_type": "code", "execution_count": null, "id": "0675a70c-ec77-4b1a-9467-29c301166540", "metadata": {}, "outputs": [], "source": ["frwd_df[\"arrival_ts\"] = pd.to_datetime(frwd_df[\"arrival_ts\"])\n", "frwd_df[\"arrival_eta_at_dispatch\"] = pd.to_datetime(frwd_df[\"arrival_eta_at_dispatch\"])\n", "\n", "\n", "frwd_df[\"enroute_diff\"] = (\n", "    frwd_df[\"arrival_ts\"] - frwd_df[\"arrival_eta_at_dispatch\"]\n", ").dt.total_seconds() / 60\n", "frwd_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "729b9474-5411-4c28-8df4-da8bb1e2b237", "metadata": {}, "outputs": [], "source": ["frwd_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "986eb211-5d87-458a-8f4a-4cb597e8a3ac", "metadata": {}, "outputs": [], "source": ["frwd_df[\"dispatch_delay_min\"].isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "75a33341-12f5-44ec-932f-6615e410334d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d63349b2-4a35-42bc-827a-a94c660f8e98", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "94dc9b3f-5277-45e1-ad4a-59b4e881f29c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "84f17883-d55d-4f37-895b-01f2bb364cd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "435c6818-367d-4ae5-805f-6d2ac971228a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "49b75fef-5878-4017-9ff1-7bb841ef7f32", "metadata": {}, "outputs": [], "source": ["frwd_df[\"dispatch_delay_mins\"] = frwd_df[\"dispatch_delay_mins\"].astype(\"float\")\n", "frwd_df[\"enroute_diff\"] = frwd_df[\"enroute_diff\"].astype(\"float\")"]}, {"cell_type": "code", "execution_count": null, "id": "c8b240d2-15d3-4275-8ed0-ef90a5d5df03", "metadata": {}, "outputs": [], "source": ["frwd_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "6fff4726-3eed-4955-8fe8-1c4471aa26cd", "metadata": {}, "outputs": [], "source": ["# cimport numpy as np\n", "\n", "# Assuming frwd_df is already defined and contains the necessary columns\n", "\n", "conditions = [\n", "    (frwd_df[\"dispatch_delay_mins\"].fillna(0) >= 0) & (frwd_df[\"enroute_diff\"].fillna(0) <= 30),\n", "    ((frwd_df[\"dispatch_delay_mins\"].fillna(0) <= frwd_df[\"enroute_diff\"].fillna(0))),\n", "]\n", "choices = [\"normal\", \"enroute_breach\"]\n", "frwd_df[\"overall_delay_reason\"] = np.select(conditions, choices, default=\"dispatch_breach\")\n", "frwd_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "44130d2d-d322-46cc-b3dc-2bd9cd7cb519", "metadata": {}, "outputs": [], "source": ["frwd_df.overall_delay_reason.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "4c46e698-0b6d-43b4-9892-79b7595fdf9a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a62518c6-5bf1-438c-a6c4-1cb047ab5818", "metadata": {}, "outputs": [], "source": ["frwd_df.overall_delay_reason.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "4d567c18-42cd-4056-84e8-8b05a9a25a3d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8bcff18d-63a4-4520-af59-cfa970d5dd3b", "metadata": {}, "source": ["### OTD METRICS"]}, {"cell_type": "code", "execution_count": null, "id": "36787420-0055-4ce0-b413-7d08fcb30cd9", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1HFADY6wROhf2rZUKa8aSz6KZc_v0wxtDrJgAczpPixI\"\n", "sheet_name2 = \"March\"\n", "\n", "mar_planning = pb.from_sheets(sheet_id, sheet_name2)\n", "\n", "# mar_planning = pd.read_csv(\"planning_data_mar.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "4e0b4ec9-3e36-4696-b65d-2bab6eacca15", "metadata": {}, "outputs": [], "source": ["# planning_data = pd.concat([feb_planning, mar_planning])\n", "planning_data = mar_planning.copy()\n", "planning_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e86482a0-3ddc-4e29-9191-161c95740e69", "metadata": {}, "outputs": [], "source": ["planning_data.rename(\n", "    columns={\n", "        \"sender_facility_id\": \"facility_id\",\n", "        \"receiving_outlet_id\": \"ds_outlet_id\",\n", "        \"DS Arrival time at Day\": \"Day\",\n", "        \"DS Arrival time at Night\": \"Night\",\n", "    },\n", "    inplace=True,\n", ")\n", "# planning_data = planning_data[\n", "#     [\"facility_id\", \"ds_outlet_id\", \"Day\", \"Night\", \"otd_month\"]\n", "# ]\n", "\n", "planning_data = planning_data[[\"facility_id\", \"ds_outlet_id\", \"Day\", \"Night\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "42a14d9f-edce-43ef-86f9-1f0422b6ac5e", "metadata": {}, "outputs": [], "source": ["planning_df = planning_data.melt(\n", "    id_vars=[\"facility_id\", \"ds_outlet_id\"],\n", "    value_vars=[\"Day\", \"Night\"],\n", "    var_name=\"dispatch_slot\",\n", "    value_name=\"planned_ds_arrival\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "14d0f475-b2ab-4ea4-8af5-2152686e5e13", "metadata": {}, "outputs": [], "source": ["planning_df = planning_df[planning_df[\"planned_ds_arrival\"].isna() == False]\n", "planning_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5d3f3c1e-c041-4013-98d1-c82f1d53de68", "metadata": {}, "outputs": [], "source": ["max_planned_ds_arrival = sqldf(\n", "    \"\"\"\n", "select  facility_id,\n", "        ds_outlet_id,\n", "        dispatch_slot,\n", "        max(planned_ds_arrival) as planned_ds_arrival\n", "from planning_df \n", "group by 1,2,3\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "efc5233b-abef-4198-9630-6fa4d93f9a17", "metadata": {}, "outputs": [], "source": ["max_planned_ds_arrival[\"facility_id\"] = max_planned_ds_arrival[\"facility_id\"].astype(int)\n", "max_planned_ds_arrival[\"ds_outlet_id\"] = max_planned_ds_arrival[\"ds_outlet_id\"].astype(int)\n", "max_planned_ds_arrival[\"dispatch_slot\"] = max_planned_ds_arrival[\"dispatch_slot\"].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "0e069a9d-6824-422d-9ac1-01858b58878e", "metadata": {}, "outputs": [], "source": ["# final_df = transit_df.merge(\n", "#     planning_df,\n", "#     how=\"left\",\n", "#     on=[\"facility_id\", \"ds_outlet_id\", \"dispatch_slot\", \"otd_month\"],\n", "# )\n", "\n", "final_df = frwd_df.merge(\n", "    max_planned_ds_arrival,\n", "    how=\"left\",\n", "    on=[\"facility_id\", \"ds_outlet_id\", \"dispatch_slot\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "17cc7c05-0739-4cc2-bc0f-9b42cbbf60d4", "metadata": {}, "outputs": [], "source": ["final_df[frwd_df[\"trip_id\"] == \"1921_670562\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0a86e3ca-8e36-49e6-aafc-070bf042030e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "65c99754-78f9-433b-bc82-0519a3cd45d7", "metadata": {}, "outputs": [], "source": ["reverse_query = f\"\"\"\n", "\n", "WITH td AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "                   pc.trip_id,\n", "                   pt.state AS trip_state,\n", "                   --type,\n", "                   cast(json_extract(pt.user_profile_meta,'$.name') as varchar) AS driver_name,\n", "                   cast(json_extract(pt.user_profile_meta,'$.phone') as varchar) AS driver_mobile,\n", "                   cast(json_extract(pt.user_profile_meta,'$.employee_id') as varchar) AS driver_id,\n", "                   min(t.install_ts) AS truck_handshake\n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_projection_consignment pc ON pc.consignment_id = t.id\n", "    JOIN transit_server.transit_projection_trip pt ON pt.trip_id = pc.trip_id\n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND pc.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND pt.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND t.type = 'REVERSE_CONSIGNMENT' \n", "    GROUP BY 1,2,3,4,5,6\n", "    ),\n", "   \n", "trip1 AS\n", "    (\n", "    SELECT  trip_id,\n", "            min(truck_handshake) AS truck_handshake\n", "    FROM td\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip2 AS\n", "    (SELECT trip_id,\n", "            min(truck_entry_wh) AS truck_entry_wh,\n", "            min(coalesce(truck_return_wh1,truck_return_wh2,truck_return_wh3)) AS truck_return_wh\n", "    FROM\n", "        (\n", "        SELECT    DISTINCT trip_id,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_entry_wh,\n", "                  CASE\n", "                      WHEN event_type = 'WH_ARRIVAL' AND cast(json_extract(event_meta,'$.consignment_type') as varchar)= 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh1,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'REVERSE_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh2,\n", "                  CASE\n", "                      WHEN event_type = 'COMPLETED' AND cast(json_extract(event_meta,'$.consignment_type') as varchar) = 'FORWARD_CONSIGNMENT' THEN actual_ts\n", "                      ELSE NULL\n", "                  END AS truck_return_wh3\n", "        FROM transit_server.transit_projection_trip_event_timeline\n", "        WHERE trip_id IN\n", "          (SELECT trip_id FROM trip1)\n", "        and insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)       \n", "         )\n", "    GROUP BY 1\n", "    ),\n", "    \n", "trip_main AS\n", "    (\n", "    SELECT \n", "          consignment_id,\n", "          td.trip_id,\n", "          trip_state,\n", "          --type,\n", "          driver_name,\n", "          driver_mobile,\n", "          driver_id,\n", "          trip1.truck_handshake,\n", "          --truck_entry_wh,\n", "          truck_return_wh\n", "    FROM td\n", "    LEFT JOIN trip1 ON trip1.trip_id = td.trip_id\n", "    LEFT JOIN trip2 ON trip2.trip_id = td.trip_id\n", "    ),\n", "    \n", "base AS\n", "    (\n", "    SELECT t.id AS consignment_id,\n", "           t.state AS current_state,\n", "           coalesce(co1.facility_id,cast(t.source_store_id as int)) AS facility_id,\n", "           co2.outlet_id as source_outlet_id,\n", "           n.external_name AS facility_name,\n", "           m.outlet_id AS ds_outlet_id,\n", "           co.name AS ds_outlet_name,\n", "           case when trim(co.location) = 'Bangalore' then 'Bengaluru' else trim(co.location) end as city,\n", "           upper(cast(json_extract(t.metadata,'$.truck_number') as varchar)) AS truck_number,\n", "           cast(json_extract(t.metadata,'$.vehicle_type') as varchar) AS truck_type,\n", "           max(CASE WHEN (to_state='LOADING') THEN tl.install_ts END) AS loading_start,\n", "           max(CASE WHEN (to_state='READY_FOR_DISPATCH') THEN tl.update_ts END) AS ready_for_dispatch,\n", "           max(CASE WHEN (to_state='ENROUTE') THEN tl.install_ts END) AS enroute,\n", "           max(CASE WHEN (to_state='REACHED') THEN tl.install_ts END) AS ds_reached,\n", "           max(CASE WHEN (to_state='UNLOADING') THEN tl.install_ts END) AS unloading_start,\n", "           max(CASE WHEN (to_state='COMPLETED') THEN tl.install_ts END) AS unloading_completed\n", "           \n", "    FROM transit_server.transit_consignment t\n", "    JOIN transit_server.transit_consignment_state_logs tl ON t.id = tl.consignment_id\n", "    JOIN transit_server.transit_node n ON n.external_id = t.source_store_id\n", "    JOIN retail.console_outlet_logistic_mapping m ON cast(m.logistic_node_id as varchar) = t.destination_store_id\n", "    AND m.active = TRUE\n", "    AND m.id NOT IN (611,1538)\n", "    JOIN retail.console_outlet co ON co.id = m.outlet_id\n", "    LEFT JOIN\n", "     (SELECT DISTINCT id,\n", "             facility_id\n", "      FROM retail.console_outlet\n", "      WHERE active = 1\n", "        AND id!= 1638\n", "        AND facility_id!= 806\n", "        AND business_type_id IN (1,12)) AS co1 ON cast(co1.id as varchar) = t.source_store_id\n", "      \n", "      left join \n", "      (     select distinct logistic_node_id, outlet_id\n", "            from retail.console_outlet_logistic_mapping a\n", "            join retail.console_outlet b\n", "            on a.outlet_id = b.id\n", "            where b.active = 1\n", "            and a.active = TRUE\n", "            and a.id not in (611, 1538)\n", "      ) as co2 \n", "      ON co2.logistic_node_id = cast(t.source_store_id as int)  \n", "        \n", "        \n", "    WHERE t.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND tl.insert_ds_ist >= cast((CURRENT_DATE - interval '15' DAY) as varchar)\n", "      AND n.external_name NOT LIKE '%%PC%%'\n", "      AND t.type = 'REVERSE_CONSIGNMENT' \n", "    GROUP BY 1,2,3,4,5,6,7,8,t.metadata\n", "    )\n", "\n", "\n", "SELECT distinct tm.trip_id,\n", "    --base.consignment_id,\n", "    --trip_state,\n", "    --source_outlet_id,\n", "    --facility_name as sender_outlet_name,\n", "    --ds_outlet_id as receiver_outlet_id,\n", "    --ds_outlet_name as receiver_name,\n", "\n", "    max(loading_start + interval '330' MINUTE) AS loading_start_ds,\n", "    max(ready_for_dispatch + interval '330' MINUTE) AS loading_end_ds,\n", "    max(ds_reached + interval '330' MINUTE) AS backend_reached_at,\n", "    max(unloading_start + interval '330' MINUTE) AS unloading_start_,\n", "    max(unloading_completed + interval '330' MINUTE) AS unloading_completed_\n", "    --max(truck_return_wh + interval '330' MINUTE) as completed_ts\n", "\n", "    \n", "FROM base\n", "left JOIN trip_main AS tm ON base.consignment_id = tm.consignment_id\n", "where tm.trip_id is not null\n", "group by 1\n", "\n", "\"\"\"\n", "\n", "rev_df = read_sql_query(reverse_query, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "3e66df78-73e8-4078-a6b8-35b3c58bfda0", "metadata": {}, "outputs": [], "source": ["rev_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6fb4b909-3fac-477e-8f2b-41f50ab5b160", "metadata": {}, "outputs": [], "source": ["rev_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a9096ffb-74cb-40ba-9187-ece088bdccd2", "metadata": {}, "outputs": [], "source": ["rev_df.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "70336c25-b258-4632-b436-d00e31ac85f6", "metadata": {}, "outputs": [], "source": ["transit_df1 = final_df.merge(rev_df, how=\"left\", on=[\"trip_id\"])\n", "transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "49b3940f-a69d-46ff-861b-57a53319217d", "metadata": {}, "outputs": [], "source": ["transit_df1.shape"]}, {"cell_type": "code", "execution_count": null, "id": "edae9c26-b5b4-4e52-9c9d-0cf2b02b478c", "metadata": {}, "outputs": [], "source": ["# DS REACH DELAY"]}, {"cell_type": "code", "execution_count": null, "id": "494a27db-95f3-498a-8d41-7eb9c966431f", "metadata": {}, "outputs": [], "source": ["transit_df1[\"ds_reach_delay\"] = (\n", "    pd.to_datetime(transit_df1[\"ds_reach_time\"]) - pd.to_datetime(transit_df1[\"planned_ds_arrival\"])\n", ").dt.total_seconds() / 60\n", "# transit_df1[\"ds_reach_delay\"] = transit_df1[\"ds_reach_delay\"].fillna(value=\"NA\")\n", "transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "82a1b870-4d64-4eae-80b7-c89fbd08bc6e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6532f6b0-2371-479e-aaa9-716340c2f2bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "df061174-a2e5-47fd-9f10-eab7a4bc4580", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "504876af-b2fe-4f0b-8a44-096699c3d6b5", "metadata": {}, "source": ["### DS reporting status\n"]}, {"cell_type": "code", "execution_count": null, "id": "b56f2761-dd47-467c-a86c-276689a037f1", "metadata": {}, "outputs": [], "source": ["transit_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "742097a3-5fb6-4063-920a-c1615492b6d3", "metadata": {}, "outputs": [], "source": ["transit_df1[\"ds_reach_delay\"] = transit_df1[\"ds_reach_delay\"].astype(\"float\")"]}, {"cell_type": "code", "execution_count": null, "id": "c9b9e788-8757-4e68-85ad-adc52e2d6aba", "metadata": {}, "outputs": [], "source": ["conditions = [\n", "    (transit_df1[\"ds_reach_delay\"] < -30),\n", "    (transit_df1[\"ds_reach_delay\"] > 60),\n", "    ((transit_df1[\"ds_reach_delay\"] >= -30) & (transit_df1[\"ds_reach_delay\"] <= 60)),\n", "    (\n", "        (transit_df1[\"ds_reach_time\"].isnull()) & (transit_df1[\"planned_ds_arrival\"].notnull())\n", "    ),  # Check for null values\n", "    (\n", "        (transit_df1[\"ds_reach_time\"].notnull()) & (transit_df1[\"planned_ds_arrival\"].isnull())\n", "    ),  # Check for null values\n", "]\n", "\n", "status_conditions = [\n", "    \"early\",\n", "    \"delay\",\n", "    \"ontime\",\n", "    \"not_reached_ds\",\n", "    \"Planning_data_error\",\n", "]\n", "transit_df1[\"ds_reporting_status\"] = np.select(conditions, status_conditions, default=\"Both_error\")"]}, {"cell_type": "code", "execution_count": null, "id": "0934db90-af51-4f56-9297-63e07f35a95a", "metadata": {}, "outputs": [], "source": ["transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3f089f9a-c7d7-4e80-b914-d45069d05279", "metadata": {}, "outputs": [], "source": ["transit_df1[\"trip_type\"] = np.where(\n", "    transit_df1[\"loading_start_ds\"].isnull() == True, \"Only forward\", \"Both\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dadc19de-5489-4ad8-a0a4-559db7e36f17", "metadata": {}, "outputs": [], "source": ["transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "59826800-9247-47a2-bdd7-b904f4104bdd", "metadata": {}, "outputs": [], "source": ["transit_df1[\"loading_end_ds\"] = pd.to_datetime(transit_df1[\"loading_end_ds\"], errors=\"coerce\")\n", "transit_df1[\"unload_complete_ds\"] = pd.to_datetime(\n", "    transit_df1[\"unload_complete_ds\"], errors=\"coerce\"\n", ")\n", "transit_df1[\"ds_reached\"] = pd.to_datetime(transit_df1[\"ds_reached\"], errors=\"coerce\")\n", "\n", "# Calculate ds_exit_time_taken\n", "transit_df1[\"ds_exit_time_taken\"] = np.where(\n", "    transit_df1[\"loading_end_ds\"].isnull(),\n", "    (transit_df1[\"unload_complete_ds\"] - transit_df1[\"ds_reached\"]),\n", "    (transit_df1[\"loading_end_ds\"] - transit_df1[\"ds_reached\"]),\n", ")\n", "\n", "# Convert the timedelta to a desired format (e.g., minutes)\n", "transit_df1[\"ds_exit_time_taken\"] = transit_df1[\"ds_exit_time_taken\"].dt.total_seconds() / 60"]}, {"cell_type": "code", "execution_count": null, "id": "37738c06-e485-4923-b229-01723c831681", "metadata": {}, "outputs": [], "source": ["transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "152736b2-4b72-4a84-97c6-82a09369e050", "metadata": {}, "outputs": [], "source": ["transit_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "5bf797bc-9f34-479e-a0f4-78886b5fcb21", "metadata": {}, "outputs": [], "source": ["def exit_status(df):\n", "    if (df[\"trip_type\"] == \"Both\") & (df[\"ds_exit_time_taken\"] <= 150):\n", "        return \"ontime_exit\"\n", "    elif (df[\"trip_type\"] == \"Both\") & (df[\"ds_exit_time_taken\"] > 150):\n", "        return \"delay_exit\"\n", "    elif (df[\"trip_type\"] == \"Only forward\") & (df[\"ds_exit_time_taken\"] <= 90):\n", "        return \"ontime_exit\"\n", "    elif (df[\"trip_type\"] == \"Only forward\") & (df[\"ds_exit_time_taken\"] > 90):\n", "        return \"delay_exit\"\n", "    else:\n", "        return \"error\"\n", "\n", "\n", "transit_df1[\"exit_status\"] = transit_df1.apply(exit_status, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "c020ba4d-9a4f-4fb8-9d03-c0805df5fe7a", "metadata": {}, "outputs": [], "source": ["transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b8ab9d12-0af2-471f-aa49-e171a503947d", "metadata": {}, "outputs": [], "source": ["transit_df1.exit_status.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "97706d82-1f5f-4ef9-90ae-71e84488b68e", "metadata": {}, "outputs": [], "source": ["transit_df1[transit_df1[\"exit_status\"] == \"error\"].consignment_date.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "e5842e28-2901-471f-94a2-93e8c91c3010", "metadata": {}, "outputs": [], "source": ["transit_df1[\n", "    (transit_df1[\"consignment_date\"] == \"2024-06-16\") & (transit_df1[\"exit_status\"] == \"error\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e1fcd22d-0c6e-4b0e-89a4-7ef8a08a5775", "metadata": {}, "outputs": [], "source": ["transit_df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b132b23b-2a05-4a63-96dd-a75f6630dcd4", "metadata": {}, "outputs": [], "source": ["transit_df1[\"unload_start_ds\"] = pd.to_datetime(transit_df1[\"unload_start_ds\"], errors=\"coerce\")\n", "transit_df1[\"ds_reached\"] = pd.to_datetime(transit_df1[\"ds_reached\"], errors=\"coerce\")\n", "transit_df1[\"unload_complete_ds\"] = pd.to_datetime(\n", "    transit_df1[\"unload_complete_ds\"], errors=\"coerce\"\n", ")\n", "transit_df1[\"loading_start_ds\"] = pd.to_datetime(transit_df1[\"loading_start_ds\"], errors=\"coerce\")\n", "transit_df1[\"loading_end_ds\"] = pd.to_datetime(transit_df1[\"loading_end_ds\"], errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "0b09cc05-8a02-4bc2-b2cc-d64c186c9c07", "metadata": {}, "outputs": [], "source": ["transit_df1[\"reach_ds_to_unloading_start_time\"] = (\n", "    transit_df1[\"unload_start_ds\"] - transit_df1[\"ds_reached\"]\n", ").dt.total_seconds() / 60"]}, {"cell_type": "code", "execution_count": null, "id": "7053cc88-e452-45b2-9356-dfe0b0c92d68", "metadata": {}, "outputs": [], "source": ["transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d523d4c6-5288-4b4f-a7e0-cfe938232b80", "metadata": {}, "outputs": [], "source": ["transit_df1[\"unloading_start_to_unloading_end_time\"] = (\n", "    transit_df1[\"unload_complete_ds\"] - transit_df1[\"unload_start_ds\"]\n", ").dt.total_seconds() / 60"]}, {"cell_type": "code", "execution_count": null, "id": "06925481-4a9b-47a2-9335-5cbab27663f3", "metadata": {}, "outputs": [], "source": ["transit_df1.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "55bd15ac-b0c8-48d3-b9b2-cac9447a84e7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f4dd9aee-9947-421a-a6fb-33680539fece", "metadata": {}, "outputs": [], "source": ["transit_df1[\"unloading_comp_to_loading_start_time\"] = (\n", "    transit_df1[\"loading_start_ds\"] - transit_df1[\"unload_complete_ds\"]\n", ").dt.total_seconds() / 60"]}, {"cell_type": "code", "execution_count": null, "id": "8db72347-c73f-4ba5-a1df-1dec79d8c4f9", "metadata": {}, "outputs": [], "source": ["transit_df1.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "8ab9c160-c6ea-4875-bc54-16065b7ec623", "metadata": {}, "outputs": [], "source": ["transit_df1[\"loading_start_to_loading_comp_time\"] = (\n", "    transit_df1[\"loading_end_ds\"] - transit_df1[\"loading_start_ds\"]\n", ").dt.total_seconds() / 60"]}, {"cell_type": "code", "execution_count": null, "id": "2da81678-ee77-4c27-b149-b1769de035e6", "metadata": {}, "outputs": [], "source": ["transit_df1.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "9ca7c311-e8f6-464f-93fc-7cd9563a8287", "metadata": {}, "outputs": [], "source": ["def unloading_status(df):\n", "    if df[\"reach_ds_to_unloading_start_time\"] > 15.00:\n", "        return \"unloading start delay\"\n", "    elif df[\"unloading_start_to_unloading_end_time\"] > 30.00:\n", "        return \"unloading complete delay\"\n", "    elif df[\"unloading_comp_to_loading_start_time\"] > 5.00:\n", "        return \"rev loading start delay\"\n", "    elif df[\"loading_start_to_loading_comp_time\"] > 60.00:\n", "        return \"rev loading complete delay\"\n", "    else:\n", "        return \"-\""]}, {"cell_type": "code", "execution_count": null, "id": "ce67ed9c-5443-42fc-90b3-b41c96e0abc8", "metadata": {}, "outputs": [], "source": ["transit_df1[\"reporting_to_exit_attribution\"] = transit_df1.apply(unloading_status, axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "25bc6758-9040-46bd-999c-e239ade60e1b", "metadata": {}, "outputs": [], "source": ["transit_df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8b85df15-8ce3-4fde-9b50-ad617bfd0a9c", "metadata": {}, "outputs": [], "source": ["transit_df1.reporting_to_exit_attribution.value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "06743c0f-3352-4c6d-b553-49eb5ab103fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8f72a77a-0196-4f5b-a025-22133d539b8e", "metadata": {}, "outputs": [], "source": ["# transit_df1.to_csv('ds_funnel_data.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8110d397-6f8a-40bb-8e88-a39c2404ebde", "metadata": {}, "outputs": [], "source": ["sheet = \"1CG_DUmZbTmlKh7CpA6X4rqKOJusCEfImaht_2n2gvVs\""]}, {"cell_type": "code", "execution_count": null, "id": "025a5c17-e456-4151-bac9-771415879ea5", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(transit_df1, sheet, \"raw_data\")"]}, {"cell_type": "code", "execution_count": null, "id": "eefb1e9d-9302-437c-b372-ed3a8eb6f564", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2f5f7da-6770-4fd7-a997-00a1150f4f89", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b6449e37-ef64-40aa-84fa-0781eb19c6b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "746e077c-611c-4e30-9fc6-412542fcb031", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c133ca6-0269-49ab-afda-e43a7a278177", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a59c31f-f08c-4e38-9ad9-dcc718d5b84c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2e7f4dff-e615-45f3-82ca-58cf7171793b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}