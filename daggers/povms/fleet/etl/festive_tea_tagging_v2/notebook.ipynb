{"cells": [{"cell_type": "code", "execution_count": null, "id": "f78f0b67-d74e-40c1-adb8-b0fb503ab6f2", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime\n", "import calendar\n", "from pytz import timezone"]}, {"cell_type": "code", "execution_count": null, "id": "2496ce9b-2038-46e1-8091-3c136b7d7d7c", "metadata": {}, "outputs": [], "source": ["## New table\n", "trino_schema_name = \"supply_etls\"\n", "trino_table_name = \"festival_tea_tagging_v2\"\n", "\n", "# Connecting with Trino\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "d9457fae-8b30-4382-a358-9cf5a45d89a1", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1VqHMvk8c0axfz_5EVEhM08YBohWwu3oQkNyi0jpt1xQ\"\n", "sheet_name = \"raw\"\n", "festival_tea_tagging_raw = pb.from_sheets(sheet_id, sheet_name)\n", "# festival_tea_tagging_raw = pd.read_csv(\n", "#     \"Festival wise Proposed - TEA TAGGING - raw (3).csv\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "d970dc30-bf42-4d76-9314-cd55a7c61365", "metadata": {}, "outputs": [], "source": ["# festival_tea_tagging_raw = pd.read_csv('raw.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "1ab02452-ce5f-4bc1-87b7-4c3f5d8355d4", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging = festival_tea_tagging_raw.fillna(0)\n", "festival_tea_tagging = festival_tea_tagging_raw.astype(\n", "    {\"outlet_id\": \"int\", \"proposed_be_facility_id\": \"int\", \"active\": \"int\"}\n", ")\n", "festival_tea_tagging[\"festival_start_date\"] = pd.to_datetime(\n", "    festival_tea_tagging[\"festival_start_date\"]\n", ").dt.date\n", "festival_tea_tagging[\"festival_end_date\"] = pd.to_datetime(\n", "    festival_tea_tagging[\"festival_end_date\"]\n", ").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "d09c5f39-9722-4800-8fa8-1748925e6df2", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging = festival_tea_tagging.drop_duplicates(\n", "    subset=[\"outlet_id\", \"festival_name\", \"active\"], keep=\"first\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0482c7fb-a988-4199-b865-0305c72c81a6", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1aac42db-ac81-49d6-a9e7-de656d596a4b", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "1f8fb41e-90f0-462b-9206-40d5eedb55f2", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging[\"festival_name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "5c9fa72f-68f6-4425-ac99-e958d349ff78", "metadata": {}, "outputs": [], "source": ["festival_tea_tagging.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9e3efe2c-5f42-403c-9f0b-ca2a67b78ad2", "metadata": {}, "outputs": [], "source": ["kwargs_output = {\n", "    \"schema_name\": trino_schema_name,\n", "    \"table_name\": trino_table_name,\n", "    \"column_dtypes\": [\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"festival_name\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"proposed_be_facility_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"active\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"festival_start_date\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"festival_end_date\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"city_name\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\"outlet_id\", \"festival_name\", \"active\"],\n", "    \"partition_key\": [\n", "        \"festival_name\",\n", "    ],\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"festival tea tagging\",\n", "}\n", "\n", "pb.to_trino(festival_tea_tagging, **kwargs_output)"]}, {"cell_type": "code", "execution_count": null, "id": "2bf2e6a3-1b9f-4046-bbc9-6a3476005480", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}