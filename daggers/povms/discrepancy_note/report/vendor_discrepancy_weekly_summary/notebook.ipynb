{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "import boto3\n", "import io\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_RETAIL = pb.get_connection(\"retail\")\n", "\n", "\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "bucket_name = \"grofers-retail-test\"\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "s3c = boto3.client(\"s3\", aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "from tabulate import tabulate\n", "\n", "\n", "def check_level(df, cols, identifier):\n", "    if df.shape[0] != df[cols].drop_duplicates().shape[0]:\n", "        query = \" and \".join([x + \" > 1\" for x in list(df.columns) if x not in cols])\n", "        temp_df = df.groupby(cols).count().reset_index()\n", "        sample = tabulate(temp_df.query(query), tablefmt=\"pipe\", headers=\"keys\")\n", "        error = f\"\"\":red_circle: *Alert*: Duplication found while extracting `{identifier}` for Run ID: `{Tag}`\\n\n", "        ```Count on grouped by {\", \".join(cols)}:\\n\\n{sample}```\"\"\"\n", "        print(error)\n", "        # raise Exception(\"Sorry, level does not match\", identifier)\n", "    else:\n", "        print(\"No errors\")\n", "        log_file_name = f\"{logs}/{Tag}_{identifier}_extracted_data.csv\"\n", "        df.to_csv(log_file_name, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Tag = \"datapulls\"\n", "cwd = os.getcwd()\n", "\n", "# directories\n", "GLOBAL_BASE_DIR = cwd\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")\n", "\n", "for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date = date.today()\n", "start_date = date.today() - <PERSON><PERSON><PERSON>(days=6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ImeLlSKsHlLwox7RWU9uGpy7rYuFUoZCkVtx1Y_-WTg\"\n", "mailers_sent = pb.from_sheets(sheet_id, \"Mailers_Sent\")\n", "mailers_sent.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mailers_sent[\"mail_sent_on\"] = pd.to_datetime(mailers_sent[\"mail_sent_on\"])\n", "mailers_sent = mailers_sent[\n", "    (pd.to_datetime(mailers_sent[\"mail_sent_on\"]) >= start_date)\n", "    & (pd.to_datetime(mailers_sent[\"mail_sent_on\"]) <= end_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["name = \"final_discrepancy_weekly_summary.csv\"\n", "mailers_sent.to_csv(name, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [\"<EMAIL>\"]\n", "cc = [\"<EMAIL>\"]\n", "\n", "subject = \"Hands On Trades Discrepancy Note Weekly Summary: \" + str(date.today())\n", "\n", "html_content = f\"\"\"\n", "Hi,<br><br>\n", "\n", "Please find attached the summary of vendors who have received an email on discrepancy in the past week - between {str(start_date)} and {str(end_date)}.<br><br>\n", "\n", "The link to the Discrepancy Note Response Tracker can be found \n", "\n", "<a href='https://docs.google.com/spreadsheets/d/1ImeLlSKsHlLwox7RWU9uGpy7rYuFUoZCkVtx1Y_-WTg/edit#gid=435210261'>\n", "here\n", "</a><br><br>\n", "\n", "<PERSON><PERSON>,<br>\n", "Data Bangalore\n", "\"\"\"\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    cc=cc,\n", "    files=[name],\n", ")  # cc=cc #     , files=['raw_data.csv'],)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}