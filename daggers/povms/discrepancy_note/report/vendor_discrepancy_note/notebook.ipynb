{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "import boto3\n", "import io\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "# CON_RETAIL = pb.get_connection(\"retail\")\n", "CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "bucket_name = \"grofers-retail-test\"\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "s3c = boto3.client(\"s3\", aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "from tabulate import tabulate\n", "\n", "\n", "def check_level(df, cols, identifier):\n", "    if df.shape[0] != df[cols].drop_duplicates().shape[0]:\n", "        query = \" and \".join([x + \" > 1\" for x in list(df.columns) if x not in cols])\n", "        temp_df = df.groupby(cols).count().reset_index()\n", "        sample = tabulate(temp_df.query(query), tablefmt=\"pipe\", headers=\"keys\")\n", "        error = f\"\"\":red_circle: *Alert*: Duplication found while extracting `{identifier}` for Run ID: `{Tag}`\\n\n", "        ```Count on grouped by {\", \".join(cols)}:\\n\\n{sample}```\"\"\"\n", "        print(error)\n", "        # raise Exception(\"Sorry, level does not match\", identifier)\n", "    else:\n", "        print(\"No errors\")\n", "        log_file_name = f\"{logs}/{Tag}_{identifier}_extracted_data.csv\"\n", "        df.to_csv(log_file_name, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Tag = \"datapulls\"\n", "cwd = os.getcwd()\n", "\n", "# directories\n", "GLOBAL_BASE_DIR = cwd\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")\n", "\n", "for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "end_date = date.today()  # - <PERSON><PERSON><PERSON>(days = 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get GRN Data for T-1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_query = f\"\"\"\n", "SELECT (i.\"delta\"-i3.return_quantity)::float AS Grn_quantity, r.item_id AS item_id, \n", "        ro.id AS outlet_id, ro.name AS outlet_name, f.id AS facility_id, \n", "        f.name AS facility_name,\n", "        i3.purchase_order_id AS po_received,\n", "                CASE\n", "                    WHEN iii.source_type=2 THEN 'ESTO Stock-In'\n", "                    WHEN iii.source_type=1\n", "                         AND iii.valid_po=1 THEN 'Normal Stock-In'\n", "                    WHEN iii.source_type = 3 THEN 'Customer Returns Without Invoice'\n", "                    ELSE 'In-Valid Po'\n", "                END AS stock_in_type,\n", "                rcm.name AS merchant,\n", "                pi.invoice_type_id,\n", "                date(convert_timezone('Asia/Kolkata',i.pos_timestamp)) AS stock_in_date, i.merchant_invoice_id as invoice_id\n", "         from  lake_ims.ims_inventory_log  i\n", "         INNER join lake_rpc.product_product r ON r.variant_id= i.variant_id\n", "         LEFT join lake_rpc.product_brand br ON br.id=r.brand_id\n", "         LEFT join lake_rpc.product_manufacturer man ON man.id=br.manufacturer_id\n", "         INNER join lake_retail.console_outlet ro ON ro.id = i.outlet_id\n", "         --left JOIN consumer.retail_console_location lo ON ro.tax_location_id=lo.id\n", "         LEFT join lake_crates.facility f ON ro.facility_id = f.id\n", "         INNER join lake_ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "         INNER join lake_ims.ims_inventory_stock_details i3 ON i3.inventory_update_id::varchar = i.inventory_update_id::varchar\n", "         LEFT JOIN lake_ims.ims_inventory_update_type iut on iut.id::varchar =i3.inventory_update_id::varchar\n", "         INNER JOIN lake_ims.ims_inward_invoice iii ON iii.grn_id=i3.grn_id\n", "         INNER join lake_retail.console_merchant rcm ON rcm.id = i.merchant_id\n", "         LEFT JOIN lake_po.purchase_order pur_ord ON pur_ord.po_number= i3.purchase_order_id\n", "         LEFT join lake_pos.view_pos_invoice pi ON pi.invoice_id=i.merchant_invoice_id\n", "         WHERE i.inventory_update_type_id IN (1, 28, 76, 90, 93)\n", "           AND (date(convert_timezone('Asia/Kolkata',i.created_at)) >= date('{start_date}'))-- and (date(convert_timezone('Asia/Kolkata',i.created_at)) < date('{end_date}'))\n", "           AND i.\"delta\" > 0\n", "           AND rcm.name NOT LIKE '%%HOT%%'\n", "           AND rcm.name NOT IN ('FNV Dummy GRN Pan India', 'Customer Returns Without Invoice', 'Store Infra Merchant',\n", "                                'HOT Warehouse Bulk FNV', 'F&V Dump stock - Jaipur', 'Customer Return without Invoice',\n", "                                'SSC Warehouse Bulk FNV')\n", "\"\"\"\n", "grn = read_sql_query(sql=grn_query, con=CON_REDSHIFT)\n", "\n", "grn = grn[\n", "    [\n", "        \"stock_in_date\",\n", "        \"stock_in_type\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"po_received\",\n", "        \"grn_quantity\",\n", "        \"invoice_type_id\",\n", "        \"merchant\",\n", "        \"invoice_id\",\n", "    ]\n", "]\n", "\n", "grn[\"invoice_id\"] = grn[\"invoice_id\"].fillna(0)\n", "\n", "\n", "grn[\"po_id\"] = grn[\"po_received\"].copy()\n", "grn[\"invoice_type_id\"] = grn[\"invoice_type_id\"].fillna(\"0\")\n", "grn = (\n", "    grn.groupby(\n", "        [\n", "            \"stock_in_date\",\n", "            \"stock_in_type\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"item_id\",\n", "            \"po_received\",\n", "            \"invoice_type_id\",\n", "            \"merchant\",\n", "            \"po_id\",\n", "            \"invoice_id\",\n", "        ]\n", "    )\n", "    .agg({\"grn_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "print(grn.shape[0])\n", "check_level(\n", "    grn,\n", "    [\"stock_in_date\", \"outlet_id\", \"po_received\", \"invoice_id\", \"item_id\", \"merchant\"],\n", "    \"grn\",\n", ")\n", "\n", "grn.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Function to create HTML Table for Mail"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_to_html(df):\n", "    html_table_summary = \"\"\"\n", "    <div class=\"mce-toc\">\n", "      <table style=\"border-collapse: collapse; width: 1000px; \" border=\"1\">\n", "        <tbody>\n", "          <tr style=\"height: 57px;\">\n", "    \"\"\"\n", "\n", "    for x in df.columns:\n", "        html_table_summary += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "        html_table_summary += x\n", "        html_table_summary += \"\"\"</strong></span></td>\"\"\"\n", "\n", "    html_table_summary += \"\"\"</tr>\"\"\"\n", "\n", "    for i, r in df.iterrows():\n", "        html_table_summary += \"\"\"<tr style=\"height: 87px;\">\"\"\"\n", "        for x in df.columns:\n", "            if x == \"Destination Outlet\" or x == \"Catering Facility\":\n", "                html_table_summary += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "                html_table_summary += str(r[x])\n", "                html_table_summary += \"\"\"</strong></span></td>\"\"\"\n", "            else:\n", "                html_table_summary += (\n", "                    \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\">\"\"\"\n", "                )\n", "                html_table_summary += str(r[x])\n", "                html_table_summary += \"\"\"</td>\"\"\"\n", "        html_table_summary += \"\"\"</tr>\"\"\"\n", "\n", "    html_table_summary += \"\"\"\n", "        </tbody>\n", "      </table>\n", "    </div>\n", "    \"\"\"\n", "    return html_table_summary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Create Mail Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(start_date)\n", "d_note_all_query = f\"\"\"\n", "SELECT created_at, date(created_at) as date_created_at,\n", "        d_note.id as \"Buyers_Detail_Id\", \n", "        d_note.outlet_id,\n", "        d_note.outlet_name,\n", "        d_note.outlet_address,\n", "        d_note.vendor_id,\n", "        d_note.vendor_name,\n", "        d_note.vendor_address,\n", "        d_note.vendor_invoice_id,\n", "        d_note.po_number,\n", "        d_note_detail.upc_id as \"discrepancy_upc_id\",\n", "        d_note_detail.name as \"discrepancy_upc_name\",\n", "        dr.name as \"discrepancy_reason\",\n", "        \n", "        vendor_gst_tin, outlet_cin, outlet_pan, outlet_gst_tin,\n", "        \n", "        sum(d_note_detail.quantity ) as \"discrepancy_quantity\",\n", "        sum(d_note_detail.cost_price ) as \"cost_price\", -- cost_price\n", "        sum(d_note_detail.cgst_per ) as \"CGST\",\n", "        sum(d_note_detail.sgst_per ) as \"SGST\",\n", "        sum(d_note_detail.igst_per ) as \"IGST\",\n", "\n", "        sum(d_note_detail.landing_price) as \"unit_landing_price\"\n", "    FROM\n", "        lake_view_pos.discrepancy_note d_note\n", "    INNER JOIN\n", "        lake_view_pos.discrepancy_note_product_detail d_note_detail ON d_note.id=d_note_detail.dn_id_id\n", "    INNER JOIN\n", "            lake_view_pos.discrepancy_reason dr on dr.id=d_note_detail.reason_code\n", "    WHERE (d_note.created_at >= date('{start_date}')) and (d_note.created_at < (date('{start_date}') + INTERVAL '1' DAY)) \n", "    AND d_note.outlet_id IN (SELECT cloud_store_id FROM lake_view_retail.warehouse_outlet_mapping)  \n", "    GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18\n", "\"\"\"\n", "d_note_all = read_sql_query(sql=d_note_all_query, con=CON_PRESTO)\n", "# check_level(d_note, ['po_number','created_at','discrepancy_reason','discrepancy_upc_id','Buyers_Detail_Id'], \"d_note\")\n", "\n", "d_note_all[\"created_at\"] = pd.to_datetime(d_note_all[\"created_at\"])\n", "\n", "d_note_all[\"CGST\"] = d_note_all[\"CGST\"].astype(float)\n", "d_note_all[\"SGST\"] = d_note_all[\"SGST\"].astype(float)\n", "d_note_all[\"IGST\"] = d_note_all[\"IGST\"].astype(float)\n", "# d_note_all[\"CGST\"] = d_note_all[\"CGST\"].astype(float)\n", "\n", "d_note_all[\"GST\"] = np.where(\n", "    (d_note_all[\"CGST\"].isna() & d_note_all[\"SGST\"].isna()),\n", "    d_note_all[\"IGST\"],\n", "    d_note_all[\"CGST\"] + d_note_all[\"SGST\"],\n", ")  ## fillna Add\n", "d_note_all[\"GST\"] = d_note_all[\"GST\"].fillna(0)\n", "d_note_all[\"subtotal_sum\"] = d_note_all[\"discrepancy_quantity\"] * d_note_all[\"cost_price\"]\n", "d_note_all[\"nettotal\"] = (d_note_all[\"subtotal_sum\"] * (d_note_all[\"GST\"] / 100)) + d_note_all[\n", "    \"subtotal_sum\"\n", "]\n", "\n", "d_note_all[\"vendor_address\"] = d_note_all[\"vendor_address\"].str.replace(\"\\n\", \" \")\n", "d_note_all[\"outlet_address\"] = d_note_all[\"outlet_address\"].str.replace(\"\\n\", \" \")\n", "\n", "\n", "d_note_all.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# d_note_all[d_note_all['CGST'].isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(\n", "    d_note_all,\n", "    [\n", "        \"po_number\",\n", "        \"created_at\",\n", "        \"discrepancy_reason\",\n", "        \"discrepancy_upc_id\",\n", "        \"discrepancy_upc_name\",\n", "        \"Buyers_Detail_Id\",\n", "        \"vendor_invoice_id\",\n", "    ],\n", "    \"d_note_all\",\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["#### Getting all Gurgaon outlets"]}, {"cell_type": "raw", "metadata": {}, "source": ["city_mapping_query = f\"\"\"\n", "select o.id as outlet_id, o.tax_location_id AS city_id, l.name AS city_name\n", "from lake_retail.console_outlet o\n", "left join lake_retail.console_location l ON o.tax_location_id = l.id\n", "where active = 1 and city_id = 9\n", "\"\"\"\n", "city_mapping = read_sql_query(sql=city_mapping_query, con=CON_REDSHIFT)\n", "\n", "city_mapping.head(2)\n", "\n", "city_mapping_outlets = city_mapping[[\"outlet_id\"]].drop_duplicates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Getting Vendor email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_contact_query = f\"\"\"with \n", "\n", "vendor_contact_map as (select contact_id, vendor_id, contact_type_id\n", "from lake_vms.contact_vendor),\n", "\n", "details as (\n", "    select distinct base.id as vendor_id, base.vendor_name, base.company_name, base.company_head_name,\n", "    po.vendor_marketing_email,\n", "    gst.gst_tin,\n", "    prod.brand,\n", "    lower(man.manufacturer_name) as manufacturer_name,\n", "    rank() over (partition by base.id order by gst.id desc) as rnk\n", "\n", "    from lake_vms.vms_vendor base\n", "\n", "    inner join (select distinct vendor_id, vendor_marketing_email \n", "        from (select vendor_id, vendor_marketing_email, rank() over (partition by vendor_id order by updated_at desc)\n", "        from lake_po.purchase_order po\n", "        inner join lake_po.po_grn grn on po.id = grn.po_id \n", "        where po.issue_date between current_date-interval '6 months' and current_date)\n", "        where rank = 1) po\n", "    on base.id = po.vendor_id\n", "\n", "    \n", "    left join (select distinct vendor_id, item_id from lake_vms.vms_vendor_alignment \n", "            where active=1 and is_aligned=1 and date(end_date) >= date(current_date)) itm on base.id = itm.vendor_id \n", "    \n", "    left join (select distinct item_id, brand from lake_rpc.product_product) prod on itm.item_id = prod.item_id\n", "    \n", "    left join (select distinct a.vendor_id, manufacturer_id from lake_vms.vendor_manufacturer_physical_facility_attributes a\n", "                inner join (select vendor_id, max(updated_at) as mua from lake_vms.vendor_manufacturer_physical_facility_attributes group by 1) b\n", "                on a.vendor_id = b.vendor_id and a.updated_at = b.mua) vm\n", "    on base.id = vm.vendor_id\n", "    \n", "    left join (select distinct a.manufacturer_id, manufacturer_name from lake_vms.vms_manufacturer_contact_details a\n", "                inner join (select manufacturer_id, max(updated_at) as mua from lake_vms.vms_manufacturer_contact_details group by 1) b\n", "                on a.manufacturer_id = b.manufacturer_id and a.updated_at = b.mua) man\n", "    on vm.manufacturer_id = man.manufacturer_id\n", "    \n", "    left join lake_vms.vms_vendor_city_mapping ven on base.id = ven.vendor_id\n", "    left join lake_vms.vms_vendor_city_tax_info gst on ven.id = gst.vendor_city_id\n", "    \n", "    \n", "    where base.active = 1   \n", "    and gst.legal_name is not null)\n", "\n", "\n", "select distinct a.vendor_id, vendor_name, vendor_marketing_email, manufacturer_name, b.brands, gst_tin\n", "from details a\n", "left join\n", "(select vendor_id, listagg(distinct lower(brand), ', ') within group (order by vendor_id) as brands    \n", "from details    \n", "group by vendor_id) b\n", "on a.vendor_id = b.vendor_id\n", "\n", "where (vendor_name not ilike '%%test%%'\n", "and vendor_name not ilike '%%cancel%%')\n", "\n", "and rnk = 1\n", "order by vendor_id\n", "\n", "\"\"\"\n", "vendor_contact = read_sql_query(sql=vendor_contact_query, con=CON_REDSHIFT)\n", "\n", "\n", "sheet_id = \"1ybpF_I3JHj2fULj4zcrlBuX5bcgYYpyr9KpARAZcNTA\"\n", "vendor_contact_alternate = pb.from_sheets(sheet_id, \"Grocery Email ID\")\n", "\n", "vendor_contact_alternate = vendor_contact_alternate[\n", "    [\"Supplier Name as per GPOS Vendor Master\", \"Email Id\"]\n", "].drop_duplicates()\n", "vendor_contact_alternate.columns = [\"vendor_name\", \"vendor_marketing_email_alt\"]\n", "vendor_contact_alternate[\"vendor_marketing_email_alt\"] = vendor_contact_alternate[\n", "    \"vendor_marketing_email_alt\"\n", "].str.replace(\";\", \",\")\n", "\n", "vendor_contact_fnv = pb.from_sheets(sheet_id, \"FNV EMAIL ID\")\n", "vendor_contact_fnv = vendor_contact_fnv[[\"Supplier Name\", \"Email Id\"]].drop_duplicates()\n", "vendor_contact_fnv.columns = [\"vendor_name\", \"vendor_marketing_email_alt\"]\n", "\n", "\n", "vendor_contact_alt2 = pb.from_sheets(sheet_id, \"Sheet2\")\n", "vendor_contact_alt2 = vendor_contact_alt2[[\"Supplier Name\", \"Email Id\"]].drop_duplicates()\n", "vendor_contact_alt2.columns = [\"vendor_name\", \"vendor_marketing_email_alt\"]\n", "\n", "vendor_contact_alternate = pd.concat(\n", "    [vendor_contact_alternate, vendor_contact_fnv, vendor_contact_alt2]\n", ")\n", "vendor_contact_alternate = vendor_contact_alternate.drop_duplicates()\n", "\n", "vendor_email = vendor_contact[[\"vendor_name\", \"vendor_marketing_email\"]].drop_duplicates()\n", "\n", "vendor_email[\"vendor_marketing_email\"] = vendor_email[\"vendor_marketing_email\"].str.replace(\n", "    \";\", \",\"\n", ")\n", "vendor_email.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# d_note_all.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Filter for required outlets, checks to remove discrepant or duplicate item entries "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(d_note_all.shape[0], d_note_all.drop_duplicates().shape[0])\n", "\n", "# d_note_all = pd.merge(d_note_all, city_mapping_outlets, on=[\"outlet_id\"], how=\"inner\")\n", "\n", "print(d_note_all.shape[0], d_note_all.drop_duplicates().shape[0])\n", "\n", "grn = grn.rename(columns={\"invoice_id\": \"vendor_invoice_id\"})\n", "grn_invoice = grn[grn.stock_in_type == \"Normal Stock-In\"][[\"vendor_invoice_id\"]].drop_duplicates()\n", "\n", "d_pre = d_note_all.copy()\n", "\n", "d_note_all = pd.merge(d_note_all, grn_invoice, on=[\"vendor_invoice_id\"], how=\"inner\")\n", "\n", "print(d_note_all.shape[0], d_pre.shape[0])\n", "\n", "d_note_all[\"dense_rank\"] = d_note_all.groupby(\n", "    [\"po_number\", \"vendor_invoice_id\", \"discrepancy_upc_id\"]\n", ")[\"created_at\"].rank(method=\"dense\", ascending=False)\n", "d_note_all = d_note_all[d_note_all.dense_rank == 1]\n", "\n", "d_note_all.groupby([\"po_number\", \"discrepancy_upc_id\"]).agg(\n", "    {\"vendor_invoice_id\": \"count\"}\n", ").reset_index().sort_values(by=\"vendor_invoice_id\", ascending=False).head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d_note_all[[\"vendor_invoice_id\", \"po_number\"]].drop_duplicates().groupby([\"vendor_invoice_id\"]).agg(\n", "    {\"po_number\": \"count\"}\n", ").reset_index().sort_values(by=\"po_number\", ascending=False).head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get all outlets with discrepancy tagged"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_outlets = d_note_all[[\"outlet_id\"]].drop_duplicates().reset_index().drop(columns={\"index\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Add Vendor Emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d_note_all = pd.merge(d_note_all, vendor_email, on=[\"vendor_name\"], how=\"left\")\n", "d_note_all = pd.merge(d_note_all, vendor_contact_alternate, on=[\"vendor_name\"], how=\"left\")\n", "\n", "d_note_all[\"vendor_marketing_email\"] = np.where(\n", "    (d_note_all[\"vendor_marketing_email\"].isna())\n", "    | (d_note_all[\"vendor_marketing_email\"] == \"Not Available\"),\n", "    d_note_all[\"vendor_marketing_email_alt\"],\n", "    d_note_all[\"vendor_marketing_email\"],\n", ")\n", "d_note_all = d_note_all.drop(columns={\"vendor_marketing_email_alt\"})\n", "d_note_all[[\"subtotal_sum\", \"nettotal\"]] = d_note_all[[\"subtotal_sum\", \"nettotal\"]].round(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (\n", "    d_note_all[\n", "        (d_note_all[\"vendor_marketing_email\"].isna())\n", "        | (d_note_all[\"vendor_marketing_email\"] == \"Not Available\")\n", "    ][[\"vendor_name\"]]\n", "    .drop_duplicates()\n", "    .shape[0]\n", "    > 0\n", "):\n", "    if (\n", "        d_note_all[\n", "            (d_note_all[\"vendor_marketing_email\"].isna())\n", "            | (d_note_all[\"vendor_marketing_email\"] == \"Not Available\")\n", "        ][[\"vendor_name\"]]\n", "        .drop_duplicates()\n", "        .shape[0]\n", "        == 1\n", "    ):\n", "        vendors = (\n", "            d_note_all[\n", "                (d_note_all[\"vendor_marketing_email\"].isna())\n", "                | (d_note_all[\"vendor_marketing_email\"] == \"Not Available\")\n", "            ][[\"vendor_name\"]]\n", "            .drop_duplicates()\n", "            .reset_index()\n", "            .drop(columns={\"index\"})\n", "            .iloc[:, 0][0]\n", "        )\n", "        vendor_email_po_query = f\"\"\"\n", "        select po_original.vendor_name, po_original.vendor_marketing_email as email\n", "        from lake_view_po.purchase_order po_original\n", "        inner join (select vendor_name, max(created_at) as created_at\n", "        from lake_view_po.purchase_order\n", "        where vendor_name in ('{vendors}')\n", "        group by 1) as po_max on po_max.vendor_name = po_original.vendor_name and po_max.created_at = po_original.created_at\n", "        where po_original.vendor_name in ('{vendors}')\n", "        group by 1,2\n", "        \"\"\"\n", "        vendor_email_po = read_sql_query(sql=vendor_email_po_query, con=CON_PRESTO)\n", "        d_note_all = pd.merge(d_note_all, vendor_email_po, on=[\"vendor_name\"], how=\"left\")\n", "        d_note_all[\"vendor_marketing_email\"] = np.where(\n", "            d_note_all[\"vendor_marketing_email\"].isna(),\n", "            d_note_all[\"email\"],\n", "            d_note_all[\"vendor_marketing_email\"],\n", "        )\n", "        d_note_all = d_note_all.drop(columns={\"email\"})\n", "    else:\n", "        vendors = tuple(\n", "            d_note_all[\n", "                (d_note_all[\"vendor_marketing_email\"].isna())\n", "                | (d_note_all[\"vendor_marketing_email\"] == \"Not Available\")\n", "            ].vendor_name.unique()\n", "        )\n", "        vendor_email_po_query = f\"\"\"\n", "        select po_original.vendor_name, po_original.vendor_marketing_email as email\n", "        from lake_view_po.purchase_order po_original\n", "        inner join (select vendor_name, max(created_at) as created_at\n", "        from lake_view_po.purchase_order\n", "        where vendor_name in {vendors}\n", "        group by 1) as po_max on po_max.vendor_name = po_original.vendor_name and po_max.created_at = po_original.created_at\n", "        where po_original.vendor_name in {vendors}\n", "        group by 1,2\n", "        \"\"\"\n", "        vendor_email_po = read_sql_query(sql=vendor_email_po_query, con=CON_PRESTO)\n", "        d_note_all = pd.merge(d_note_all, vendor_email_po, on=[\"vendor_name\"], how=\"left\")\n", "        d_note_all[\"vendor_marketing_email\"] = np.where(\n", "            d_note_all[\"vendor_marketing_email\"].isna(),\n", "            d_note_all[\"email\"],\n", "            d_note_all[\"vendor_marketing_email\"],\n", "        )\n", "        d_note_all = d_note_all.drop(columns={\"email\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d_note_all = d_note_all.drop(columns={\"dense_rank\"})\n", "d_note_all = d_note_all.drop_duplicates()\n", "d_note_all.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d_note_all[\n", "    (d_note_all[\"vendor_marketing_email\"] == \"Not Available\")\n", "    | (d_note_all[\"vendor_marketing_email\"].isna())\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Logging Data for vendors who will get discrepancy mail for t-1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_sent_summary_log = (\n", "    d_note_all.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"vendor_name\",\n", "            \"vendor_invoice_id\",\n", "            \"po_number\",\n", "            \"vendor_marketing_email\",\n", "        ]\n", "    )\n", "    .agg({\"discrepancy_quantity\": \"sum\", \"nettotal\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "print(start_date + <PERSON><PERSON>ta(days=1))\n", "mail_sent_summary_log[\"mail_sent_on\"] = start_date + timedelta(days=1)\n", "\n", "mail_sent_summary_log = mail_sent_summary_log[\n", "    [\n", "        \"mail_sent_on\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"po_number\",\n", "        \"vendor_name\",\n", "        \"vendor_invoice_id\",\n", "        \"vendor_marketing_email\",\n", "        \"discrepancy_quantity\",\n", "        \"nettotal\",\n", "    ]\n", "]\n", "mail_sent_summary_log.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ImeLlSKsHlLwox7RWU9uGpy7rYuFUoZCkVtx1Y_-WTg\"\n", "output_sheet = \"Mailers_Sent\"\n", "backup = pb.from_sheets(sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backup = pd.concat([backup, mail_sent_summary_log])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ImeLlSKsHlLwox7RWU9uGpy7rYuFUoZCkVtx1Y_-WTg\"\n", "output_sheet = \"Mailers_Sent\"\n", "# pb.to_sheets(backup, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(all_outlets)):\n", "\n", "    outlet = all_outlets.iloc[:, 0][i]\n", "    all_vendor = (\n", "        d_note_all[d_note_all.outlet_id == outlet][[\"vendor_name\"]]\n", "        .drop_duplicates()\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    for j in range(len(all_vendor)):\n", "        vendor = all_vendor.iloc[:, 0][j]\n", "\n", "        filter_data = d_note_all[\n", "            (d_note_all.outlet_id == outlet) & (d_note_all.vendor_name == vendor)\n", "        ].drop_duplicates()\n", "\n", "        d_mail_summary = (\n", "            filter_data.groupby(\n", "                [\n", "                    \"Buyers_Detail_Id\",\n", "                    \"outlet_id\",\n", "                    \"outlet_name\",\n", "                    \"vendor_name\",\n", "                    \"vendor_invoice_id\",\n", "                    \"po_number\",\n", "                ]\n", "            )\n", "            .agg(\n", "                {\n", "                    \"discrepancy_upc_id\": \"count\",\n", "                    \"discrepancy_quantity\": \"sum\",\n", "                    \"nettotal\": \"sum\",\n", "                }\n", "            )\n", "            .reset_index()\n", "        )\n", "        d_mail_summary.columns = [\n", "            \"Buyers_Detail_Id\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"vendor_name\",\n", "            \"vendor_invoice_id\",\n", "            \"purchase_order\",\n", "            \"item_count\",\n", "            \"Quantity\",\n", "            \"Amount\",\n", "        ]\n", "\n", "        d_mail_file = filter_data[\n", "            [\n", "                \"Buyers_Detail_Id\",\n", "                \"outlet_name\",\n", "                \"vendor_name\",\n", "                \"vendor_invoice_id\",\n", "                \"po_number\",\n", "                \"discrepancy_upc_id\",\n", "                \"discrepancy_upc_name\",\n", "                \"discrepancy_reason\",\n", "                \"discrepancy_quantity\",\n", "                \"cost_price\",\n", "                \"CGST\",\n", "                \"SGST\",\n", "                \"unit_landing_price\",\n", "                \"GST\",\n", "                \"subtotal_sum\",\n", "                \"nettotal\",\n", "            ]\n", "        ]\n", "\n", "        mail_output = d_mail_file.copy()\n", "\n", "        mail_summary = d_mail_summary.copy()\n", "        finalmail_summary = mail_summary[\n", "            [\n", "                \"outlet_name\",\n", "                \"vendor_invoice_id\",\n", "                \"purchase_order\",\n", "                \"item_count\",\n", "                \"Quantity\",\n", "                \"Amount\",\n", "            ]\n", "        ]\n", "\n", "        finalmail_summary = df_to_html(finalmail_summary)\n", "\n", "        total_amount = (\n", "            mail_summary.agg({\"Amount\": \"sum\"}).reset_index().drop(columns={\"index\"}).iloc[:, 0][0]\n", "        )\n", "\n", "        mail_items = filter_data.copy().reset_index()\n", "        mail_items = mail_items.drop(columns={\"index\"})\n", "        vendor_name = mail_items.iloc[:, 7][0]\n", "        outlet_name = mail_items.iloc[:, 4][0]\n", "        outlet_name = outlet_name.replace(\"&\", \"%26\")\n", "\n", "        all_invoice = \", \".join(map(str, list(mail_summary.vendor_invoice_id.unique()))).replace(\n", "            \", nan\", \"\"\n", "        )\n", "        all_invoice = all_invoice.replace(\"&\", \"%26\")\n", "        from_email = \"<EMAIL>\"\n", "        to_email = [\n", "            \", \".join(map(str, list(filter_data.vendor_marketing_email.unique())))\n", "            .replace(\", nan\", \"\")\n", "            .replace(\"\\xa0\", \"\")\n", "        ]\n", "        cc = [\n", "            \"<EMAIL>\",\n", "            \"<EMAIL>\",\n", "        ]\n", "\n", "        subject = (\n", "            \"Hands On Trades Discrepancy Note Generated DATED: \"\n", "            + str(date.today())\n", "            + \" :- Location of Supply: \"\n", "            + mail_items.iloc[:, 4][0]\n", "            + \" - Vendor Name: \"\n", "            + mail_items.iloc[:, 7][0]\n", "        )\n", "\n", "        name = \"final_discrepancy_file_outlet_\" + str(outlet) + \".csv\"\n", "\n", "        mail_output.to_csv(name, index=False)\n", "\n", "        html_content = f\"\"\"\n", "        Hi,<br><br>\n", "\n", "        Please find attached the discrepancy note for damaged or short materials supplied by you.<br><br>\n", "\n", "        You are requested to acknowledge the same through the link below and share the credit note copy on the below email id, so that both parties’ sale and purchase data can be matched with each other and there would not be any discrepancy at the time of filing the GST return. \n", "        <br>Please respond to this in the next 3 days. A faliure to responding to this will be deemed as the discrepancy to be accepted by you. <br><br>\n", "\n", "        The link to the acknowledgement form can be found \n", "\n", "        <a href='https://docs.google.com/forms/d/e/1FAIpQLSehu6SnYKYHcEaNERmh4v3pE1ZkwxLQyDu9J2jG6LF11qcKkw/viewform?usp=pp_url&entry.1394356670={vendor_name}&entry.946648033={outlet_name}&entry.1335420865={all_invoice}&entry.1675221750=INR {total_amount}'>\n", "        here\n", "        </a><br><br>\n", "        \n", "\n", "        Please find the discrepancy note summary below :\n", "        <br><br>\n", "        {finalmail_summary}<br>\n", "        <br>\n", "        <br>\n", "        <PERSON><PERSON>,<br>\n", "        Data Bangalore\n", "        \"\"\"\n", "        pb.send_email(\n", "            from_email,\n", "            to_email,\n", "            subject,\n", "            html_content,\n", "            cc=cc,\n", "            files=[name],\n", "        )  # cc=cc #     , files=['raw_data.csv'],)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}