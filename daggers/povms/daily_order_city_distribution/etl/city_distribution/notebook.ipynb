{"cells": [{"cell_type": "code", "execution_count": null, "id": "aab0102f-46b3-422e-8169-3a945223b4aa", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timezone, timedelta\n", "\n", "trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4a8c2076-3377-4821-ad1b-e0fd79897869", "metadata": {}, "outputs": [], "source": ["sql = f\"\"\"\n", "    with filtered_data as (\n", "  Select\n", "    distinct om.city_id,\n", "    om.facility_id,\n", "    om.outlet_id,\n", "    om.outlet_name,\n", "    ac.cluster_id,\n", "    ac.cluster_name\n", "  from\n", "    po.physical_facility_outlet_mapping om\n", "    inner join rpc.ams_city_cluster_mapping ccm on om.city_id = ccm.city_id\n", "    and ccm.active --added\n", "    and om.ars_active = 1\n", "    inner join rpc.ams_cluster ac on ccm.cluster_id = ac.cluster_id\n", "    and ac.lake_active_record\n", "  where\n", "    ccm.lake_active_record\n", "    and ccm.active\n", "    and ac.lake_active_record\n", "    and om.city_id != ac.cluster_id\n", "    and om.facility_id IN (\n", "      Select\n", "        facility_id\n", "      from\n", "        lake_retail.console_outlet\n", "      where\n", "        business_type_id = 7\n", "    )\n", "),\n", "fc AS (\n", "  SELECT\n", "    distinct x.outlet_id,\n", "    date(cart_checkout_ts_ist) as dt,\n", "    count(distinct cart_id) AS orders\n", "  FROM\n", "    dwh.fact_sales_order_details x\n", "  WHERE\n", "    cart_checkout_ts_ist > CURRENT_DATE - INTERVAL '30' DAY\n", "    AND order_create_dt_ist > CURRENT_DATE - INTERVAL '30' DAY\n", "    AND order_current_status = 'DELIVERED'\n", "  GROUP BY\n", "    1,2\n", "),\n", "final as(\n", "  select\n", "    y.*,\n", "sum(fc.orders) * 1.00 / 30 daily_orders\n", "  from\n", "    filtered_data as y\n", "    inner join fc on y.outlet_id = fc.outlet_id\n", "  group by\n", "    1,\n", "    2,\n", "    3,\n", "    4,\n", "    5,\n", "    6\n", ")\n", "select\n", "  city_id,\n", "  cl.name as city_name,\n", "  cluster_id,\n", "  cluster_name,\n", "  sum(daily_orders) as daily_orders,\n", "  case\n", "    when sum(daily_orders) > 40000 then 'Core'\n", "    else 'Emerging'\n", "  end as \"city_type\"\n", "from\n", "  final f\n", "  left join retail.console_location cl on cl.id = f.city_id\n", "group by\n", "  1,\n", "  2,\n", "  3,\n", "  4 \"\"\"\n", "data = pd.read_sql(con=trino_connection_3, sql=sql)"]}, {"cell_type": "code", "execution_count": null, "id": "30cedb9b-3655-4b24-9fe7-1e350cf2e3bd", "metadata": {}, "outputs": [], "source": ["utc_now = datetime.now(timezone.utc)\n", "ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "data[\"insert_ds_ist\"] = formatted_date\n", "data[\"updated_at\"] = pd.to_datetime(utc_now)"]}, {"cell_type": "code", "execution_count": null, "id": "b843881c-40b7-41c7-bb75-f4efa11abb28", "metadata": {}, "outputs": [], "source": ["data[\"active_flag\"] = np.where(data[\"cluster_name\"].str.endswith((\"_Core\", \"_Emerging\")), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "3665f96f-a6c2-4dd9-8de0-48e8e522e1b3", "metadata": {}, "outputs": [], "source": ["data[\"daily_orders\"] = data[\"daily_orders\"].astype(float)\n", "data[\"cluster_id\"] = data[\"cluster_id\"].astype(int)\n", "data[\"city_id\"] = data[\"city_id\"].astype(int)\n", "data[\"active_flag\"] = data[\"active_flag\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "ef79923e-3fbb-4b8c-be90-3d36686361cd", "metadata": {}, "outputs": [], "source": ["data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "28b86561-d9cb-49bd-a3ed-7948c3a015b2", "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bac02f0a-92a6-4e8f-8909-c0ff0954d6a2", "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7e806c7d-ae6a-415e-987a-a268811228a4", "metadata": {}, "outputs": [], "source": ["filtered_data = data[data[\"active_flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "960cac2b-fa12-4e13-9080-2fc2f8c96333", "metadata": {}, "outputs": [], "source": ["filtered_data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1a093045-c43b-4183-9cf1-640ad43c1f76", "metadata": {}, "outputs": [], "source": ["filtered_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d57a7fca-d9d0-46a8-a7e2-46e23e6ecdf2", "metadata": {}, "outputs": [], "source": ["filtered_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "cdb7d6da-bb82-45ac-b748-272f9b5467f9", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"city_distirubution_on_daily_orders\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"city_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Id of cluster\",\n", "        },\n", "        {\n", "            \"name\": \"city_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Name of the specfic cluster\",\n", "        },\n", "        {\n", "            \"name\": \"cluster_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Respective zone id\",\n", "        },\n", "        {\n", "            \"name\": \"cluster_name\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Allocated zone name\",\n", "        },\n", "        {\n", "            \"name\": \"daily_orders\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Name of the state\",\n", "        },\n", "        {\n", "            \"name\": \"city_type\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Name of the state\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Time of updation\",\n", "        },\n", "        {\n", "            \"name\": \"insert_ds_ist\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Day of table creation\",\n", "        },\n", "        {\n", "            \"name\": \"active_flag\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Day of table creation\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"city_id\", \"cluster_id\"],\n", "    # \"partition_key\": [\"festival_name\"],  # ** Note below points while selecting this\n", "    # \"incremental_key\": \"update_ts\",\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"City division based on daily orders\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c2a50c27-a83e-4e74-8637-8e52c8356cca", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=filtered_data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "680583d3-dcba-4aba-bdd7-e35e3730fed3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "data = {\"city_id\": [], \"cluster_id\": [], \"active\": []}\n", "\n", "city_cluster_mapping = pd.DataFrame(data)\n", "print(city_cluster_mapping)"]}, {"cell_type": "code", "execution_count": null, "id": "4e2bfe46-df31-4bd3-996c-c57b9f64de36", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "c0b5cc47-f10b-4528-b196-f6cc096032ca", "metadata": {}, "outputs": [], "source": ["def post_dataframes_to_api(ams_city_cluster_mapping_df: pd.DataFrame):\n", "    # ams_cluster_payload = {\"data\": ams_cluster_df.to_dict(orient='records')}\n", "\n", "    ams_city_cluster_mapping_payload = {\n", "        \"data\": ams_city_cluster_mapping_df.to_dict(orient=\"records\")\n", "    }\n", "\n", "    # print(ams_cluster_payload)\n", "    print(ams_city_cluster_mapping_payload)\n", "\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "\n", "    # ams_cluster_api_url = \"https://retail-internal.grofer.io/rpc/v1/ams-cluster-mappings/\"\n", "\n", "    ams_city_cluster_mapping_api_url = (\n", "        \"https://retail-internal.grofer.io/rpc/v1/ams-city-cluster-mappings/\"\n", "    )\n", "\n", "    # response1 = requests.post(ams_cluster_api_url, headers=headers, data=json.dumps(ams_cluster_payload))\n", "\n", "    response2 = requests.post(\n", "        ams_city_cluster_mapping_api_url,\n", "        headers=headers,\n", "        data=json.dumps(ams_city_cluster_mapping_payload),\n", "    )\n", "\n", "    return \"Success\""]}, {"cell_type": "code", "execution_count": null, "id": "61e2c98e-9c56-4e83-8734-8da250cce5fb", "metadata": {}, "outputs": [], "source": ["post_dataframes_to_api(city_cluster_mapping)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}