alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: city_distribution
dag_type: etl
escalation_priority: low
execution_timeout: 2460
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U08KMDSJUPM
path: povms/daily_order_city_distribution/etl/city_distribution
paused: false
pool: povms_pool
project_name: daily_order_city_distribution
schedule:
  end_date: '2025-07-10T00:00:00'
  interval: 30 3 * * 1
  start_date: '2025-04-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
