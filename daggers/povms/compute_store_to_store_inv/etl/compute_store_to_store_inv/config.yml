alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: compute_store_to_store_inv
dag_type: etl
escalation_priority: low
execution_timeout: 240
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07JD7JA0LR
path: povms/compute_store_to_store_inv/etl/compute_store_to_store_inv
paused: false
pool: povms_pool
project_name: compute_store_to_store_inv
schedule:
  end_date: '2025-06-15T00:00:00'
  interval: 59 11 31 12 *
  start_date: '2025-04-27T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
