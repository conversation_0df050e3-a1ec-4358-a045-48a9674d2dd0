{"cells": [{"cell_type": "code", "execution_count": null, "id": "fcbf21c2-f961-4a54-b3ec-3b0a548c895e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import io\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "import os\n", "import logging\n", "import random\n", "import requests\n", "from pytz import timezone\n", "import boto3\n", "from time import sleep"]}, {"cell_type": "code", "execution_count": null, "id": "81bec4d8-25d3-4df4-9088-8b19ac6919fd", "metadata": {}, "outputs": [], "source": ["env = \"prod\"\n", "sheet_link = \"1l0lPpGhwvstb99BZsFCTYWTG7yhgrC9Wr8RNyf0M5D4\""]}, {"cell_type": "code", "execution_count": null, "id": "b071994e-a905-4d48-8c04-7b641b76bf2b", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "ENABLE_LOGGING = 0\n", "\n", "\n", "def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "def print_log(message):\n", "    if ENABLE_LOGGING:\n", "        if isinstance(message, pd.DataFrame):\n", "            display(message)\n", "        else:\n", "            print(message)\n", "\n", "\n", "delivery_date = pd.to_datetime(\n", "    datetime.today() + timedelta(hours=5.5) + timedelta(hours=int(25))\n", ").strftime(\"%Y-%m-%d %H:%M:%S\")\n", "dag_run_time = pd.to_datetime(datetime.today() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "9e2e690b-69df-4ee1-b38a-c0e9afc1c1dd", "metadata": {}, "outputs": [], "source": ["suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            if not df.empty:\n", "                # Print the size of the DataFrame\n", "                size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "                print(\"DataFrame size: \", size)\n", "                return df\n", "            else:\n", "                print(\"DataFrame is empty, retrying...\")\n", "        except Exception as e:\n", "            print(e)\n", "\n", "        time.sleep(5)\n", "\n", "    print(\"Max retries reached, returning an empty DataFrame\")\n", "    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "2a8ebaa1-7379-4e5d-9d8b-f9286003e4a3", "metadata": {}, "outputs": [], "source": ["if env == \"dev\":\n", "    from IPython.display import display, clear_output\n", "    import ipywidgets as widgets\n", "\n", "    def upload_and_process_csv(\n", "        expected_columns=[],\n", "        save_path=\"~/Shared/<PERSON><PERSON><PERSON>/hooks/util_functions/upload_garbage/\",\n", "    ):\n", "        save_path = os.path.expanduser(save_path)\n", "        os.makedirs(save_path, exist_ok=True)\n", "\n", "        uploader = widgets.FileUpload(accept=\".csv\", multiple=False)\n", "        display(uploader)\n", "\n", "        output = widgets.Output()\n", "        display(output)\n", "\n", "        result = {\"df\": None}\n", "\n", "        def on_upload_change(change):\n", "            with output:\n", "                clear_output(wait=True)\n", "\n", "                if not uploader.value:\n", "                    print(\"❌ No file uploaded.\")\n", "                    return\n", "\n", "                uploaded_file = next(iter(uploader.value.values()))\n", "                file_content = uploaded_file[\"content\"]\n", "\n", "                df = pd.read_csv(pd.io.common.BytesIO(file_content))\n", "                if len(expected_columns) > 0 and not set(expected_columns).issubset(df.columns):\n", "                    print(\"❌ Error: Missing expected columns in the uploaded file.\")\n", "                    return\n", "\n", "                print(\"✅ Columns validated!\")\n", "\n", "                original_file_name = uploaded_file[\"metadata\"][\"name\"]\n", "                timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "                file_name, file_ext = os.path.splitext(original_file_name)\n", "                new_file_name = f\"{file_name}_{timestamp}{file_ext}\"\n", "                full_path = os.path.join(save_path, new_file_name)\n", "\n", "                with open(full_path, \"wb\") as f:\n", "                    f.write(file_content)\n", "\n", "                print(f\"✅ File saved at: {full_path}\")\n", "\n", "                print(\"✅ File successfully processed!\")\n", "                display(df.head())\n", "                uploader.close()\n", "                result[\"df\"] = df\n", "\n", "        uploader.observe(on_upload_change, names=\"value\")\n", "        return result\n", "\n", "    upload_container_input = upload_and_process_csv()"]}, {"cell_type": "code", "execution_count": null, "id": "b7961afc-1a1f-44d7-af56-bbc3853f7287", "metadata": {}, "outputs": [], "source": ["if env == \"dev\":\n", "    input_config = upload_container_input[\"df\"]\n", "else:\n", "    input_config = pb.from_sheets(sheet_link, \"s2s_base\")"]}, {"cell_type": "code", "execution_count": null, "id": "5385c4b0-2d37-4c2c-80f0-4e86acd1c90f", "metadata": {}, "outputs": [], "source": ["cols_to_convert = [\n", "    \"parent_outlet_id\",\n", "    \"child_outlet_ids\",\n", "    \"child_util_threshold\",\n", "    \"sto_lookback_days\",\n", "    \"transfer_tat\",\n", "]\n", "input_config[cols_to_convert] = input_config[cols_to_convert].apply(pd.to_numeric, errors=\"coerce\")\n", "\n", "transfer_tat = int(input_config.transfer_tat.unique()[0])\n", "sto_lookback_days = int(input_config.sto_lookback_days.unique()[0])\n", "parent_outlet = input_config[\"parent_outlet_id\"].dropna().unique()\n", "children_outlets = input_config[\"child_outlet_ids\"].dropna().unique()\n", "\n", "cold_handling_types = [\"2\", \"6\"]"]}, {"cell_type": "code", "execution_count": null, "id": "82bae3a9-e000-4962-a6f3-e3cd58e52360", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"C08EDHXBU7K\",\n", "    text=f\"Sheet Input Read! Proceeding to store to store movement computation from parent {parent_outlet} to {children_outlets}...\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bac0a5c0-815f-4f0e-a07f-7e3a03ef3a84", "metadata": {}, "outputs": [], "source": ["# this cell fetches inventory substate reason at both child and parent outlets\n", "df_substate_status = read_sql_query(\n", "    sql=f\"\"\"\n", "    with icd as (-- icd_store_closure\n", "select\n", "        distinct id.item_id,\n", "        icd.name as item_name,\n", "        icd.product_type as p_type,\n", "        icd.product_type_id,\n", "        icd.l0 as l0_category,\n", "        icd.l1 as l1_category,\n", "        icd.l2 as l2_category,\n", "        id.storage_type,\n", "        id.handling_type,\n", "        id.perishable\n", "from rpc.item_category_details icd \n", "left join rpc.item_details id on id.item_id = icd.item_id\n", "where l1_id not in (2039, 183)\n", "        and l0 not like '%%specials%%' \n", "        and l0_id not in (1487, 343)\n", "        and perishable = 0 \n", "        and handling_type not in ('8', '9') \n", "        and storage_type not in ('3', '6', '7', '9')\n", "),\n", "\n", "    doac as (\n", "        select \n", "            doac.item_id, \n", "            doac.facility_id, \n", "            sum(case when doac.available_hours > 9 then 1 else 0 end) as available_days\n", "        from ars.daily_orders_and_availability doac\n", "        where doac.insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "        group by 1,2)\n", "\n", "    select \n", "        pfma.item_id, \n", "        icd.item_name,\n", "        icd.p_type,\n", "        icd.product_type_id,\n", "        icd.l0_category,\n", "        icd.l1_category,\n", "        icd.l2_category,\n", "        pfom.outlet_id, \n", "        pfom.outlet_name,\n", "        pfom.facility_id,\n", "        master_assortment_substate_id,\n", "        assortment_type,\n", "        available_days,\n", "        coalesce(itf.item_factor, 1) as item_factor,\n", "        q.qmax_final as qmax\n", "    from rpc.product_facility_master_assortment pfma\n", "    left join (select facility_id, outlet_id, outlet_name from po.physical_facility_outlet_mapping) as pfom on pfom.facility_id = pfma.facility_id\n", "    left join supply_etls.item_factor itf on itf.item_id = pfma.item_id\n", "    left join supply_etls.ars_qmax q on q.item_id = pfma.item_id and q.outlet_id = pfom.outlet_id and q.updated_at is not null\n", "    inner join icd on icd.item_id = pfma.item_id\n", "    left join doac on doac.item_id = pfma.item_id and pfma.facility_id = doac.facility_id\n", "    where pfma.active = 1 and pfma.lake_active_record\n", "        and pfom.outlet_id in  {tuple(list(set(parent_outlet).union(set(children_outlets))))}\"\"\",\n", "    con=CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dcf642c4-3011-46f2-9a9a-f8d17b07d447", "metadata": {}, "outputs": [], "source": ["# assort base is the base assortment df for all active assortment at the children outlets\n", "assort_base = df_substate_status.query(\n", "    f\"outlet_id != {parent_outlet[0]} and master_assortment_substate_id in (1,3)\"\n", ").merge(\n", "    df_substate_status.query(f\"outlet_id == {parent_outlet[0]}\")[[\"item_id\"]],\n", "    on=\"item_id\",\n", "    how=\"inner\",\n", ")\n", "assort_base"]}, {"cell_type": "code", "execution_count": null, "id": "6c371e4d-5f32-46d1-81ed-8eba3a0f778f", "metadata": {}, "outputs": [], "source": ["# this fetches inventory at both source and destination outlets\n", "df_inv = read_sql_query(\n", "    sql=f\"\"\"        \n", "    with ii as (-- is_inv.sql texting\n", "        select try_cast(ii.item_id as int) as item_id, \n", "               try_cast(ii.outlet_id as int) as outlet_id, \n", "               sum(quantity - coalesce(blocked_inv, 0)) as inv\n", "        from dynamodb.blinkit_store_inventory_service_oi_rt_view as ii\n", "        left join (\n", "            select item_id, outlet_id, sum(quantity) blocked_inv \n", "            from dynamodb.blinkit_store_inventory_service_blk_rt_view\n", "            where status = 'BLOCKED' \n", "              and reference_type not in ('DL_VALIDATION_CRON', 'PNA') \n", "            group by 1, 2\n", "        ) ib on ib.item_id = ii.item_id and ib.outlet_id = ii.outlet_id\n", "        where ii.state = 'GOOD'\n", "        group by 1, 2\n", "    ),\n", "    \n", "     pfsc as (\n", "     select \n", "        pfsc.facility_id, \n", "        pfom.outlet_id,\n", "        storage_type,\n", "        base_storage_type,\n", "        storage_capacity,\n", "        threshold,\n", "        1 as flag\n", "    from ars.physical_facility_storage_capacity pfsc\n", "    left join po.physical_facility_outlet_mapping pfom on pfom.facility_id = pfsc.facility_id and pfom.active = 1 \n", "        and pfom.lake_active_record\n", "    where pfsc.active\n", "        and pfsc.lake_active_record\n", "        and pfsc.storage_capacity > 0\n", "        ),\n", "        \n", "        ictd as (\n", "            with base_1 as (\n", "                select\n", "                    distinct id.item_id,\n", "                    case \n", "                        when (id.storage_type) in ('1','8') then 'REGULAR'\n", "                        when (id.storage_type) in ('4','5') then 'HEAVY'\n", "                        when (id.storage_type) in ('2','6') then 'COLD'\n", "                        when (id.storage_type) in ('3','7') then 'FROZEN'\n", "                        else 'REGULAR'\n", "                    END AS storage_type,\n", "                    case \n", "                        when itm.tag_value = '1' then 'BEAUTY'\n", "                        when itm.tag_value = '2' then 'BOUQUET'\n", "                        when itm.tag_value = '3' then 'PREMIUM'\n", "                        when itm.tag_value = '4' then 'BOOKS'\n", "                        when itm.tag_value = '5' then 'NON_VEG'\n", "                        when itm.tag_value = '6' then 'ICE_CREAM'\n", "                        when itm.tag_value = '7' then 'TOYS'\n", "                        when itm.tag_value = '8' then 'ELECTRONICS'\n", "                        -- when itm.tag_value = '9' then 'FESTIVE'\n", "                        when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "                        when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "                        when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "                        when itm.tag_value = '13' then 'LARGE'\n", "                        when itm.tag_value = '14' then 'APPAREL'\n", "                        when itm.tag_value = '15' then 'SPORTS'\n", "                        when itm.tag_value = '16' then 'PET_CARE'\n", "                        when itm.tag_value = '17' then 'HOME_DECOR'\n", "                        when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "                        else 'NORMAL_GROCERY'\n", "                    end as IBT,\n", "                    itm.tag_value\n", "                from rpc.item_details id \n", "                left join rpc.item_tag_mapping itm on itm.item_id = id.item_id and itm.tag_type_id = 7 and itm.active = TRUE and itm.lake_active_record\n", "                ),\n", "\n", "                base_2 as (\n", "                    select \n", "                        base_1.*,\n", "                        case when ibt = 'NORMAL_GROCERY' then base_1.storage_type else concat(IBT,'_',base_1.storage_type) end as sub_storage_type\n", "                    from base_1\n", "                )\n", "\n", "                select * from base_2\n", "            )\n", "\n", "            select \n", "                ii.*,\n", "                case when pfsc.flag is null then ictd.storage_type else ictd.sub_storage_type end as sub_storage_type\n", "            from ii\n", "            left join ictd on ictd.item_id = ii.item_id\n", "            left join pfsc on pfsc.outlet_id = ii.outlet_id and pfsc.storage_type = ictd.sub_storage_type\n", "         where ii.outlet_id in {tuple(list(set(parent_outlet).union(set(children_outlets))))} and ii.item_id in {tuple(assort_base.item_id.unique())}\"\"\",\n", "    con=CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "759b89b2-31d0-44bc-b3ce-3c4401ca2867", "metadata": {}, "outputs": [], "source": ["import ast\n", "\n", "spillover_types = read_sql_query(\n", "    f\"\"\" select value from ars.ars_config where name = 'spillover_enabled_storage_types' \"\"\",\n", "    con=CON_TRINO,\n", ")\n", "spillover_types = ast.literal_eval(spillover_types.iloc[0].value)\n", "spillover_types"]}, {"cell_type": "code", "execution_count": null, "id": "5eeb03ae-6ca0-4312-8ff6-f0ab798b1cb0", "metadata": {}, "outputs": [], "source": ["df_inv[\"base_storage_type\"] = np.where(\n", "    df_inv[\"sub_storage_type\"].str.contains(\"_\"),\n", "    df_inv[\"sub_storage_type\"].str.rsplit(\"_\", n=1).str[-1],\n", "    df_inv[\"sub_storage_type\"],\n", ")\n", "\n", "df_inv[\"check_storage_type\"] = np.where(\n", "    df_inv[\"sub_storage_type\"].isin(spillover_types),\n", "    df_inv[\"base_storage_type\"],\n", "    df_inv[\"sub_storage_type\"],\n", ")\n", "\n", "df_inv"]}, {"cell_type": "code", "execution_count": null, "id": "9429721b-9044-4ad1-a219-03b9c347e6c5", "metadata": {}, "outputs": [], "source": ["df_item_storage = df_inv.sort_values(\"inv\", ascending=False)[\n", "    [\"item_id\", \"check_storage_type\"]\n", "].drop_duplicates()\n", "df_item_storage"]}, {"cell_type": "code", "execution_count": null, "id": "78f6e577-20a1-4a1e-8864-2a01110ce988", "metadata": {}, "outputs": [], "source": ["# case when sto_state = 1 then 'created'\n", "# when sto_state = 2 then 'billed' when sto_state = 3 then 'expired'\n", "# when sto_state = 4 then 'inward' when sto_state = 5 then 'partial-billed'\n", "# else null end as sto_state\n", "\n", "children_open_stos = pd.read_sql_query(\n", "    sql=f\"\"\" \n", "    with base as (\n", "        select \n", "            isd.merchant_outlet_id as outlet_id,\n", "            isi.item_id,\n", "            isi.billed_quantity,\n", "            isd.created_at\n", "        from (\n", "            select \n", "                sto_id,\n", "                merchant_outlet_id,\n", "                created_at\n", "            from ims.ims_sto_details \n", "            where created_at >= current_date - interval '{sto_lookback_days}' day \n", "                and lake_active_record \n", "                and sto_state in (1,2,5)\n", "            ) isd\n", "        inner join (\n", "            select \n", "                sto_id,\n", "                item_id, \n", "                reserved_quantity,\n", "                billed_quantity\n", "            from ims.ims_sto_item \n", "            where created_at >= current_date - interval '{sto_lookback_days}' day \n", "                and lake_active_record\n", "            ) isi on isd.sto_id = isi.sto_id\n", "        )\n", "    select * from base where outlet_id in {tuple(children_outlets) if len(children_outlets) > 1 else f'({children_outlets[0]})'}\n", "    \"\"\",\n", "    con=CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b3d1d9cc-6532-4a9a-97a0-65870feeb3e0", "metadata": {}, "outputs": [], "source": ["# fetch billed (open po qty) qty against items at children outlet(s)\n", "open_sto_inv = (\n", "    children_open_stos.groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"billed_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "open_sto_inv"]}, {"cell_type": "code", "execution_count": null, "id": "271544f6-ce9c-4f46-ac6a-58d2c105efa0", "metadata": {}, "outputs": [], "source": ["df_base = (\n", "    assort_base.merge(\n", "        df_inv[[\"item_id\", \"outlet_id\", \"inv\"]],\n", "        on=[\"item_id\", \"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "    .merge(open_sto_inv, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "    .merge(df_item_storage, on=\"item_id\", how=\"left\")\n", ")\n", "\n", "df_base"]}, {"cell_type": "code", "execution_count": null, "id": "48779472-2511-4fb3-a6bc-566adee66f16", "metadata": {}, "outputs": [], "source": ["item_list = assort_base.item_id.unique()\n", "\n", "children_cpd = read_sql_query(\n", "    sql=f\"\"\"\n", "WITH cpd_data AS (\n", "    SELECT \n", "        item_id, \n", "        outlet_id, \n", "        insert_ds_ist AS insert_date, \n", "        aps_adjusted as cpd,\n", "        potential_sale\n", "    FROM ars.outlet_item_aps_derived_cpd\n", "    WHERE insert_ds_ist = CAST(current_date - interval '1' day AS varchar)\n", "    AND item_id IN {tuple(item_list)}\n", "    AND outlet_id IN {tuple(children_outlets) if len(children_outlets) > 1 else f'({children_outlets[0]})'}\n", ")\n", "\n", "SELECT \n", "    item_id, \n", "    outlet_id,\n", "    cpd\n", "from cpd_data \"\"\",\n", "    con=CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "18b4b821-554a-448c-bfb1-6d82abd623dd", "metadata": {}, "outputs": [], "source": ["df_base_2 = df_base.merge(children_cpd, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "\n", "df_base_2[\"inv_with_open_sto\"] = df_base_2[\"inv\"].fillna(0) + df_base_2[\"billed_quantity\"].fillna(0)\n", "\n", "df_base_2[\"inv_with_open_sto_depl\"] = np.maximum(\n", "    df_base_2[\"inv_with_open_sto\"].fillna(0) - (df_base_2[\"cpd\"].fillna(0) * transfer_tat), 0\n", ")\n", "\n", "df_base_2[\"inv_intake_cap\"] = np.maximum(\n", "    df_base_2[\"qmax\"].fillna(np.round(df_base_2[\"cpd\"] * 2))\n", "    - df_base_2[\"inv_with_open_sto_depl\"].fillna(0),\n", "    0,\n", ")\n", "\n", "df_base_2"]}, {"cell_type": "code", "execution_count": null, "id": "d3e50ce5-122c-4518-bce1-b1d9b0e62ad8", "metadata": {}, "outputs": [], "source": ["df_base_2.groupby([\"outlet_id\", \"outlet_name\", \"check_storage_type\"]).agg(\n", "    {\n", "        \"inv\": \"sum\",\n", "        \"billed_quantity\": \"sum\",\n", "        \"qmax\": \"sum\",\n", "        \"cpd\": \"sum\",\n", "        \"inv_intake_cap\": \"sum\",\n", "        \"inv_with_open_sto\": \"sum\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "21acaeca-b7fd-443e-83e8-b9da6b4fc007", "metadata": {}, "outputs": [], "source": ["# find storage caps for different storage types\n", "\n", "df_storage_caps = read_sql_query(\n", "    sql=f\"\"\"\n", "    select \n", "        distinct\n", "        pfom.outlet_id,\n", "        pfsc.facility_id,\n", "        pfsc.facility_name,\n", "        pfsc.storage_type as sub_storage_type,\n", "        pfsc.base_storage_type,\n", "        pfsc.threshold,\n", "        pfsc.storage_capacity as storage_cap\n", "    from lake_ars.physical_facility_storage_capacity pfsc\n", "    left join po.physical_facility_outlet_mapping pfom on pfom.facility_id = pfsc.facility_id\n", "    inner join\n", "        (\n", "            select \n", "                distinct\n", "                facility_id,\n", "                storage_type,\n", "                base_storage_type,\n", "                max(updated_at) as updated_at_\n", "            from \n", "                lake_ars.physical_facility_storage_capacity \n", "            group by 1,2,3\n", "        ) sb2 on sb2.facility_id = pfsc.facility_id\n", "                and sb2.storage_type = pfsc.storage_type\n", "                and sb2.base_storage_type = pfsc.base_storage_type\n", "                and sb2.updated_at_ = pfsc.updated_at\n", "    where pfsc.active \n", "    and pfsc.lake_active_record and\n", "    pfom.active = 1 and pfom.lake_active_record = true\n", "    and pfom.outlet_id in {tuple(children_outlets) if len(children_outlets) > 1 else f'({children_outlets[0]})'}\n", "\"\"\",\n", "    con=CON_TRINO,\n", ")\n", "\n", "df_storage_caps"]}, {"cell_type": "code", "execution_count": null, "id": "8cc0ced7-3f99-4b27-8714-150203276fbf", "metadata": {}, "outputs": [], "source": ["df_storage_caps[\"base_storage_type\"] = np.where(\n", "    df_storage_caps[\"sub_storage_type\"].str.contains(\"_\"),\n", "    df_storage_caps[\"sub_storage_type\"].str.rsplit(\"_\", n=1).str[-1],\n", "    df_storage_caps[\"sub_storage_type\"],\n", ")\n", "\n", "df_storage_caps[\"check_storage_type\"] = np.where(\n", "    df_storage_caps[\"sub_storage_type\"].isin(spillover_types),\n", "    df_storage_caps[\"base_storage_type\"],\n", "    df_storage_caps[\"sub_storage_type\"],\n", ")\n", "\n", "df_storage_caps"]}, {"cell_type": "code", "execution_count": null, "id": "40871913-2a16-43d9-84a2-fb2fe23497dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f8fb865a-7f97-4362-99d4-72d1f9bddd15", "metadata": {}, "outputs": [], "source": ["df_store_prior = (\n", "    df_storage_caps.groupby([\"outlet_id\", \"facility_name\", \"check_storage_type\"])\n", "    .agg({\"storage_cap\": \"sum\"})\n", "    .reset_index()\n", "    .merge(\n", "        input_config[[\"child_outlet_ids\", \"child_util_threshold\"]].rename(\n", "            columns={\"child_outlet_ids\": \"outlet_id\"}\n", "        ),\n", "        on=\"outlet_id\",\n", "        how=\"left\",\n", "    )\n", "    .merge(\n", "        df_base_2.assign(scaled_open_inv=lambda x: x[\"inv_with_open_sto\"] * x[\"item_factor\"])\n", "        .groupby([\"outlet_id\", \"check_storage_type\"])\n", "        .agg({\"scaled_open_inv\": \"sum\"})\n", "        .reset_index(),\n", "        on=[\"outlet_id\", \"check_storage_type\"],\n", "        how=\"left\",\n", "    )\n", "    .fillna(0)\n", ")\n", "\n", "df_store_prior[\"utilisation\"] = df_store_prior[\"scaled_open_inv\"] / df_store_prior[\"storage_cap\"]\n", "df_store_prior[\"util_scaled_open_inv_left\"] = np.maximum(\n", "    (df_store_prior[\"storage_cap\"] * df_store_prior[\"child_util_threshold\"])\n", "    - df_store_prior[\"scaled_open_inv\"],\n", "    0,\n", ")\n", "\n", "df_store_prior"]}, {"cell_type": "code", "execution_count": null, "id": "4b65cd0a-474a-4b90-8f8b-44487255bd8f", "metadata": {}, "outputs": [], "source": ["store_storage_cap = (\n", "    df_storage_caps.query(\"sub_storage_type == 'REGULAR' \")\n", "    .sort_values(\"storage_cap\", ascending=False)\n", "    .reset_index(drop=True)\n", ")\n", "store_storage_cap[\"priority_rank\"] = store_storage_cap.index + 1\n", "store_storage_cap"]}, {"cell_type": "code", "execution_count": null, "id": "347ba037-42f1-441d-b3df-b17b90c96510", "metadata": {}, "outputs": [], "source": ["df_2 = (\n", "    df_base_2.merge(\n", "        store_storage_cap[[\"facility_id\", \"priority_rank\"]], on=\"facility_id\", how=\"left\"\n", "    )\n", "    .sort_values(by=[\"item_id\", \"priority_rank\"])\n", "    .merge(\n", "        df_store_prior[\n", "            [\n", "                \"outlet_id\",\n", "                \"check_storage_type\",\n", "                \"scaled_open_inv\",\n", "                \"util_scaled_open_inv_left\",\n", "            ]\n", "        ],\n", "        on=[\"outlet_id\", \"check_storage_type\"],\n", "        how=\"left\",\n", "    )\n", ")\n", "df_2[\"inv_intake_ceil\"] = np.ceil(df_2[\"inv_intake_cap\"])\n", "\n", "print(len(df_2.item_id.unique()))\n", "df_2"]}, {"cell_type": "code", "execution_count": null, "id": "e3fe567a-06fd-4d86-87d0-cf8bae15fa2a", "metadata": {}, "outputs": [], "source": ["parent_inv = (\n", "    df_inv.query(f\"outlet_id == {parent_outlet[0]}\")[\n", "        [\"item_id\", \"outlet_id\", \"check_storage_type\", \"inv\"]\n", "    ]\n", "    .merge(assort_base.groupby(\"item_id\").agg({\"item_factor\": \"max\"}), on=\"item_id\", how=\"inner\")\n", "    .fillna(1)\n", ")\n", "\n", "display(\n", "    parent_inv.query(\"inv > 0\")\n", "    .groupby([\"check_storage_type\"])\n", "    .agg({\"inv\": \"sum\", \"item_id\": \"count\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b75b056f-9d95-47ce-8354-fc07fd72742b", "metadata": {}, "outputs": [], "source": ["parent_inv[\"transfer_details\"] = [{} for _ in range(len(parent_inv))]\n", "parent_inv[\"transfer_qty\"] = 0\n", "\n", "df_2[\"transfer_intake\"] = 0\n", "df_2[\"scaled_transfer_intake\"] = df_2[\"util_scaled_open_inv_left\"]\n", "\n", "print_log(\"Starting transfer allocation process...\")\n", "\n", "item_cpd_priority = (\n", "    children_cpd.sort_values(\"cpd\", ascending=False)\n", "    .drop_duplicates(subset=\"item_id\")\n", "    .set_index(\"item_id\")[\"cpd\"]\n", ")\n", "\n", "ordered_item_ids = sorted(parent_inv.item_id.unique(), key=lambda x: -item_cpd_priority.get(x, 0))\n", "\n", "for item_id in ordered_item_ids:\n", "    # for item_id in parent_inv.item_id.unique():\n", "    required_inv = parent_inv.loc[parent_inv[\"item_id\"] == item_id, \"inv\"].iloc[0]\n", "    required_item_factor = parent_inv.loc[parent_inv[\"item_id\"] == item_id, \"item_factor\"].iloc[0]\n", "    temp = df_2[df_2[\"item_id\"] == item_id].sort_values(\"priority_rank\")\n", "\n", "    transfer_dict = {}\n", "    total_transferred = 0\n", "\n", "    print_log(\n", "        f\"\\nProcessing Item ID: {item_id}, Required Inventory: {required_inv}, Required Storage: {np.round(required_inv * required_item_factor, 4)}\"\n", "    )\n", "\n", "    for idx, row in temp.iterrows():\n", "        if row[\"inv_intake_ceil\"] >= required_inv and row[\"scaled_transfer_intake\"] > 0:\n", "            transfer_qty = required_inv\n", "            df_2.loc[idx, \"transfer_intake\"] = transfer_qty\n", "            df_2.loc[\n", "                (df_2[\"outlet_id\"] == row[\"outlet_id\"])\n", "                & (df_2[\"check_storage_type\"] == row[\"check_storage_type\"]),\n", "                \"scaled_transfer_intake\",\n", "            ] -= np.round(required_item_factor * transfer_qty, 4)\n", "            transfer_dict[row[\"outlet_id\"]] = transfer_qty\n", "            total_transferred = required_inv\n", "            print_log(\n", "                f\"\\tFull transfer fulfilled by Outlet {row['outlet_id']}, storage_left in {row['check_storage_type']} = {row['scaled_transfer_intake']}\"\n", "            )\n", "            break\n", "\n", "    if not transfer_dict:\n", "        for idx, row in temp.iterrows():\n", "            if total_transferred < required_inv and row[\"scaled_transfer_intake\"] > 0:\n", "                transfer_qty = min(row[\"inv_intake_ceil\"], required_inv - total_transferred)\n", "                df_2.loc[idx, \"transfer_intake\"] = transfer_qty  # Properly update df_2\n", "                df_2.loc[\n", "                    (df_2[\"outlet_id\"] == row[\"outlet_id\"])\n", "                    & (df_2[\"check_storage_type\"] == row[\"check_storage_type\"]),\n", "                    \"scaled_transfer_intake\",\n", "                ] -= np.round(required_item_factor * transfer_qty, 4)\n", "                transfer_dict[row[\"outlet_id\"]] = transfer_qty\n", "                total_transferred += transfer_qty\n", "                print_log(\n", "                    f\"\\tPartial transfer: {transfer_qty} to Outlet {row['outlet_id']}, storage_left in {row['check_storage_type']} = {row['scaled_transfer_intake']}\"\n", "                )\n", "            if total_transferred >= required_inv:\n", "                break\n", "\n", "    parent_inv.loc[parent_inv[\"item_id\"] == item_id, \"transfer_details\"] = [\n", "        transfer_dict if transfer_dict else {}\n", "    ]\n", "    parent_inv.loc[parent_inv[\"item_id\"] == item_id, \"transfer_qty\"] = total_transferred\n", "\n", "    print_log(f\"Completed Item ID: {item_id}, Total Transferred: {total_transferred}\")\n", "\n", "print_log(\"Transfer allocation process completed.\")"]}, {"cell_type": "code", "execution_count": null, "id": "6cc9c61c-580d-4584-baee-d41eef3577b1", "metadata": {}, "outputs": [], "source": ["df_2.query(\"transfer_intake > 0\")[\n", "    df_2.query(\"transfer_intake > 0\").duplicated(subset=[\"item_id\", \"outlet_id\"])\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e82bceda-512f-47bb-80df-6112ecd23d0e", "metadata": {}, "outputs": [], "source": ["df_2"]}, {"cell_type": "code", "execution_count": null, "id": "08aa6b4d-da74-490d-a8be-270748d4aef9", "metadata": {}, "outputs": [], "source": ["df_2[\"scaled_transfer\"] = df_2[\"transfer_intake\"] * df_2[\"item_factor\"]\n", "df_2.query(\"transfer_intake > 0\").groupby(\"check_storage_type\").agg(\n", "    {\n", "        \"transfer_intake\": \"sum\",\n", "        \"util_scaled_open_inv_left\": \"unique\",\n", "        \"scaled_transfer\": \"sum\",\n", "        \"scaled_transfer_intake\": \"unique\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aa5e0665-3f67-4cec-98cd-5f61f7e41b27", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    df_2.query(\"transfer_intake > 0\").sort_values(by=[\"scaled_transfer_intake\", \"outlet_id\"]),\n", "    \"1l0lPpGhwvstb99BZsFCTYWTG7yhgrC9Wr8RNyf0M5D4\",\n", "    \"detailed_inv_report\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "809503d7-e9d2-4a78-923f-7f506ef9c253", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(parent_inv, \"1l0lPpGhwvstb99BZsFCTYWTG7yhgrC9Wr8RNyf0M5D4\", \"parent_inv_output\")"]}, {"cell_type": "code", "execution_count": null, "id": "9e8d8307-4235-46d0-afbd-033b960afcb5", "metadata": {}, "outputs": [], "source": ["pb.send_slack_message(\n", "    channel=\"C08EDHXBU7K\",\n", "    text=f\"Store to Store Output published in sheet\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}