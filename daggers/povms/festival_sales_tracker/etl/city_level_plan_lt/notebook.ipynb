{"cells": [{"cell_type": "code", "execution_count": null, "id": "15ad58f8-360e-4621-b781-392d268cacbc", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "!pip install tqdm\n", "from tqdm import tqdm\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "# redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)"]}, {"cell_type": "code", "execution_count": null, "id": "d083bbad-78f1-499e-ab77-3736a0b98ffd", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "1301414c-eb56-41fe-b7a4-81710a2e1f07", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = f\"\"\"\n", "    \n", "    select item_id,case when storage_type in ('<PERSON><PERSON>','Fridge') then 'Cold' else 'Regular' end storage \n", "        from supply_etls.item_details\n", "        group by 1,2\n", "\n", "    \n", "    \"\"\"\n", "    return read_sql_query(item_details, trino)\n", "\n", "\n", "item_details = item_details()"]}, {"cell_type": "code", "execution_count": null, "id": "2a73d50c-3707-465e-86a4-5259edc10b6e", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2f8ed689-d78e-42c3-b8b6-c15d8ad<PERSON>cbe", "metadata": {}, "outputs": [], "source": ["# df = pd.read_csv(\"city_level splits (AS) - LT.csv\")\n", "# df2 = pd.read_csv(\"festival_dag__city level - base.csv\")\n", "\n", "df = pb.from_sheets(\n", "    \"1qXRkbGG7H_ox0jLTZcFPDtipPAmt79VTqUDjGON7xhI\",\n", "    \"LT\",\n", "    clear_cache=True,\n", ")\n", "df2 = pb.from_sheets(\"1_43qdWddHCBrV84iRYbKmnBCmi7K24bOF_PyFasrPPs\", \"base\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9a4ff4fc-12ec-40c6-ae92-e11f30438cda", "metadata": {}, "outputs": [], "source": ["city_raw_data = df[[\"city\", \"storage\", \"facility_id\", \"split_perc\", \"assortment_type::filter\"]]\n", "\n", "city_raw_data = city_raw_data[\n", "    ~(\n", "        city_raw_data[\"city\"].isnull()\n", "        | city_raw_data[\"storage\"].isnull()\n", "        | city_raw_data[\"facility_id\"].isnull()\n", "        | city_raw_data[\"split_perc\"].isnull()\n", "        | city_raw_data[\"assortment_type::filter\"].isnull()\n", "    )\n", "]\n", "\n", "\n", "city_raw_data[\"updated_at_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "\n", "city_raw_data[\"facility_id\"] = city_raw_data[\"facility_id\"].apply(pd.to_numeric, errors=\"coerce\")\n", "city_raw_data[[\"storage\", \"city\", \"assortment_type::filter\"]] = city_raw_data[\n", "    [\"storage\", \"city\", \"assortment_type::filter\"]\n", "].astype(str)\n", "city_raw_data[\"split_perc\"] = city_raw_data[\"split_perc\"].astype(float)"]}, {"cell_type": "code", "execution_count": null, "id": "f29ba03f-0c26-4545-8c99-cfc6b202fe76", "metadata": {}, "outputs": [], "source": ["df2"]}, {"cell_type": "code", "execution_count": null, "id": "e1bf7a51-18d7-4919-b1f3-458bdf065c04", "metadata": {}, "outputs": [], "source": ["df2_copy = df2.copy()\n", "\n", "# Selecting columns\n", "city_demand_data = df2_copy[\n", "    [\n", "        \"city\",\n", "        \"item_id\",\n", "        \"catg_segment\",\n", "        \"festival\",\n", "        \"festival_bau\",\n", "        \"planned_qty\",\n", "        \"sale_start\",\n", "        \"sale_end\",\n", "        \"cutt_off\",\n", "        \"Assortment (Express / Hybrid)\",\n", "    ]\n", "]\n", "\n", "# Filtering out rows with null values\n", "city_demand_data = city_demand_data[\n", "    ~(\n", "        city_demand_data[\"city\"].isnull()\n", "        | city_demand_data[\"item_id\"].isnull()\n", "        | city_demand_data[\"festival\"].isnull()\n", "        | city_demand_data[\"sale_start\"].isnull()\n", "        | city_demand_data[\"sale_end\"].isnull()\n", "        | city_demand_data[\"cutt_off\"].isnull()\n", "        | city_demand_data[\"Assortment (Express / Hybrid)\"].isnull()\n", "    )\n", "]\n", "\n", "# Handling missing values in \"planned_qty\", \"catg_segment\", \"festival_bau\", and \"group\"\n", "city_demand_data[\"planned_qty\"].fillna(0, inplace=True)\n", "# city_demand_data[[\"catg_segment\", \"festival_bau\", \"group\"]] = city_demand_data[[\"catg_segment\", \"festival_bau\", \"group\"]].fillna(pd.NA)\n", "city_demand_data[[\"catg_segment\", \"festival_bau\"]] = city_demand_data[\n", "    [\"catg_segment\", \"festival_bau\"]\n", "].fillna(pd.NA)\n", "\n", "\n", "# Adding a new column with the current datetime\n", "city_demand_data[\"updated_at_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "\n", "# Converting \"item_id\" to integer after removing commas\n", "city_demand_data[\"item_id\"] = (\n", "    city_demand_data[\"item_id\"].astype(str).str.replace(\",\", \"\").astype(float).astype(int)\n", ")\n", "\n", "# Converting selected columns to string\n", "# city_demand_data[['city', 'catg_segment', 'festival', 'festival_bau', 'group']] = city_demand_data[['city', 'catg_segment', 'festival', 'festival_bau', 'group']].astype(str)\n", "city_demand_data[\n", "    [\"city\", \"catg_segment\", \"festival\", \"festival_bau\", \"Assortment (Express / Hybrid)\"]\n", "] = city_demand_data[\n", "    [\"city\", \"catg_segment\", \"festival\", \"festival_bau\", \"Assortment (Express / Hybrid)\"]\n", "].astype(\n", "    str\n", ")\n", "\n", "# Converting \"planned_qty\" to Int64 type\n", "city_demand_data[\"planned_qty\"] = (\n", "    pd.to_numeric(city_demand_data[\"planned_qty\"], errors=\"coerce\").round(0).astype(\"Int64\")\n", ")\n", "\n", "# Converting date columns to datetime\n", "city_demand_data[\"sale_start\"] = pd.to_datetime(city_demand_data[\"sale_start\"])\n", "city_demand_data[\"sale_end\"] = pd.to_datetime(city_demand_data[\"sale_end\"])\n", "city_demand_data[\"cutt_off\"] = pd.to_datetime(city_demand_data[\"cutt_off\"])\n", "city_demand_data[\"Assortment (Express / Hybrid)\"] = city_demand_data[\n", "    \"Assortment (Express / Hybrid)\"\n", "].str.upper()"]}, {"cell_type": "code", "execution_count": null, "id": "abee7598-dd16-4b77-8e38-94d4ffa2478c", "metadata": {}, "outputs": [], "source": ["city_raw_data.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "1592ab0b-9fe5-417c-9448-fc0856306449", "metadata": {}, "outputs": [], "source": ["city_demand_data.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "2d9582bc-2437-4321-a47e-596666939d80", "metadata": {}, "outputs": [], "source": ["city_storage = pd.merge(\n", "    city_demand_data,\n", "    item_details,\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "city_storage.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "653b0e18-081e-45c4-a54e-91b39b0e42ce", "metadata": {}, "outputs": [], "source": ["final_view = pd.merge(\n", "    city_storage,\n", "    city_raw_data,\n", "    left_on=[\"city\", \"storage\", \"Assortment (Express / Hybrid)\"],\n", "    right_on=[\"city\", \"storage\", \"assortment_type::filter\"],\n", "    how=\"left\",\n", ")\n", "final_view.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "d0a1ffe0-0947-4bb4-bdfe-775d0567b877", "metadata": {}, "outputs": [], "source": ["final_view = final_view[\n", "    [\n", "        \"facility_id\",\n", "        \"city\",\n", "        \"item_id\",\n", "        \"storage\",\n", "        \"catg_segment\",\n", "        \"festival\",\n", "        \"festival_bau\",\n", "        \"planned_qty\",\n", "        \"split_perc\",\n", "        \"sale_start\",\n", "        \"sale_end\",\n", "        \"cutt_off\",\n", "        \"Assortment (Express / Hybrid)\",\n", "    ]\n", "]\n", "final_view"]}, {"cell_type": "code", "execution_count": null, "id": "8b60c778-327f-4c16-98d1-02c35c70ee1c", "metadata": {}, "outputs": [], "source": ["final_view.to_csv(\"final_view.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c0240427-d5b3-4e57-96d8-68032cbc802e", "metadata": {}, "outputs": [], "source": ["result_data = (\n", "    final_view.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"storage\",\n", "            \"catg_segment\",\n", "            \"festival\",\n", "            \"festival_bau\",\n", "            \"sale_start\",\n", "            \"sale_end\",\n", "            \"cutt_off\",\n", "            \"Assortment (Express / Hybrid)\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"planned_qty\": lambda x: (\n", "                x * final_view[\"split_perc\"]\n", "            ).sum(),  # Round to the nearest integer\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "61008358-8079-46a1-9f9e-b313d2ac2d32", "metadata": {}, "outputs": [], "source": ["result_data[\"planned_qty\"] = result_data[\"planned_qty\"].round(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "667959f4-7f7b-47ca-ae39-c6fc20984747", "metadata": {}, "outputs": [], "source": ["result_data"]}, {"cell_type": "code", "execution_count": null, "id": "460e0c19-0e2c-4488-ba3c-77bdf1938d0e", "metadata": {}, "outputs": [], "source": ["result_data[\"updated_at_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "d3800904-a47a-40e5-b5e8-0a4471e75771", "metadata": {}, "outputs": [], "source": ["result_data.rename(columns={\"Assortment (Express / Hybrid)\": \"assortment_type\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "45afa49a-82b1-4eff-a47f-5794a2abe03c", "metadata": {}, "outputs": [], "source": ["result_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "eae13a40-cceb-43d5-a24b-8a1ecc5aedaf", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"storage\", \"type\": \"varchar\", \"description\": \"storage_type\"},\n", "    {\n", "        \"name\": \"catg_segment\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"category segment of product\",\n", "    },\n", "    {\"name\": \"festival\", \"type\": \"varchar\", \"description\": \"name of the festival\"},\n", "    {\n", "        \"name\": \"festival_bau\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"article is wether festival or bau\",\n", "    },\n", "    {\"name\": \"sale_start\", \"type\": \"Date\", \"description\": \"sale start date\"},\n", "    {\"name\": \"sale_end\", \"type\": \"Date\", \"description\": \"sale end date\"},\n", "    {\n", "        \"name\": \"cutt_off\",\n", "        \"type\": \"Date\",\n", "        \"description\": \"cut off date to get the item to wh\",\n", "    },\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"assortment_type\"},\n", "    {\"name\": \"planned_qty\", \"type\": \"integer\", \"description\": \"final planned quantity\"},\n", "    {\"name\": \"updated_at_ist\", \"type\": \"TIMESTAMP(6)\", \"description\": \"updated_time\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "24ebadac-4629-4834-a8c9-516b47b4ee48", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"final_festival_planning_LT\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"get the data from catg team in city level and getting planned qty to facility\",\n", "}\n", "\n", "pb.to_trino(result_data, **kwargs)\n", "\n", "print(\"final base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "7ce75ac5-37b1-4e34-93da-9756d51c47ac", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}