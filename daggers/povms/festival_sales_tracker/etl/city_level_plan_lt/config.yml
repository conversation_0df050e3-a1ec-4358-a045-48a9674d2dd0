alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: city_level_plan_lt
dag_type: etl
escalation_priority: low
execution_timeout: 240
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07V5F4G8CV
path: povms/festival_sales_tracker/etl/city_level_plan_lt
paused: false
pool: povms_pool
project_name: festival_sales_tracker
schedule:
  end_date: '2025-09-10T00:00:00'
  interval: 0 */2 * * *
  start_date: '2025-06-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
