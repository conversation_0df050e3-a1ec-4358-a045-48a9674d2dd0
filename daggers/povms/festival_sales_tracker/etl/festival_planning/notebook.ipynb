{"cells": [{"cell_type": "code", "execution_count": null, "id": "8c0d9457-f9d6-495c-b91b-96566368b8bd", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "bb1b7eeb-cd6e-4940-abfb-94e06e272be2", "metadata": {}, "outputs": [], "source": ["# def read_sql_query(sql, con):\n", "#     max_tries = 1\n", "#     for attempt in range(max_tries):\n", "#         print(f\"Read attempt: {attempt}...\")\n", "#         try:\n", "#             start = time.time()\n", "#             df = pd.read_sql_query(sql, con)\n", "#             end = time.time()\n", "#             if (end - start) > 60:\n", "#                 print(\"Time: \", (end - start) / 60, \"min\")\n", "#             else:\n", "#                 print(\"Time: \", end - start, \"s\")\n", "#             return df\n", "#             break\n", "#         except BaseException as e:\n", "#             print(e)\n", "#             time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b179a94b-d9c6-4f4b-94a1-dabac7c30cc4", "metadata": {}, "outputs": [], "source": ["updated_at_ist = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "updated_at_ist"]}, {"cell_type": "code", "execution_count": null, "id": "6aeff8e7-e295-4299-a8a6-53dd228252ee", "metadata": {}, "outputs": [], "source": ["# base_data  = pd.read_csv('festival_dag.csv')\n", "\n", "\n", "base_data = pb.from_sheets(\"1wjpi_uAmLHxa-nIgoC9bQF4DZa5X5rVtHF5yaGQ55MQ\", \"base\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "828d3c9c-aebd-4a6b-b889-d74469cdb5c2", "metadata": {}, "outputs": [], "source": ["base_data"]}, {"cell_type": "code", "execution_count": null, "id": "e53846f8-95fb-4567-ab30-e5b0b88f64e0", "metadata": {}, "outputs": [], "source": ["base_data = base_data[\n", "    ~(\n", "        (base_data[\"facility_id\"] == \"\")\n", "        | (base_data[\"item_id\"] == \"\")\n", "        | (base_data[\"catg_segment\"] == \"\")\n", "        | (base_data[\"ptype\"] == \"\")\n", "        | (base_data[\"festival\"] == \"\")\n", "        | (base_data[\"group_type\"] == \"\")\n", "        | (base_data[\"festival_bau\"] == \"\")\n", "        | (base_data[\"final_qty\"] == \"\")\n", "        | (base_data[\"sale_start\"] == \"\")\n", "        | (base_data[\"sale_end\"] == \"\")\n", "        | (base_data[\"cutt_off\"] == \"\")\n", "        | (base_data[\"initial_qty\"] == \"\")\n", "    )\n", "]\n", "\n", "base_data[\"facility_id\"] = base_data[\"facility_id\"].astype(str).str.replace(\",\", \"\").astype(int)\n", "base_data[\"item_id\"] = base_data[\"item_id\"].astype(str).str.replace(\",\", \"\").astype(int)\n", "base_data[\"final_qty\"] = (\n", "    pd.to_numeric(base_data[\"final_qty\"], errors=\"coerce\").round(0).astype(\"Int64\")\n", ")\n", "base_data[\"initial_qty\"] = (\n", "    pd.to_numeric(base_data[\"initial_qty\"], errors=\"coerce\").round(0).astype(\"Int64\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "46d2fc37-7ddc-4ca0-9ccf-583621c6dc07", "metadata": {}, "outputs": [], "source": ["base_data"]}, {"cell_type": "code", "execution_count": null, "id": "1297e0bd-b4f4-487b-b477-9dea23476f5a", "metadata": {}, "outputs": [], "source": ["base_data[[\"catg_segment\", \"ptype\", \"festival\", \"group_type\", \"festival_bau\"]] = base_data[\n", "    [\"catg_segment\", \"ptype\", \"festival\", \"group_type\", \"festival_bau\"]\n", "].astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "31332568-fc75-49b0-92a4-edf07efa2da5", "metadata": {}, "outputs": [], "source": ["base_data[\"sale_start\"] = pd.to_datetime(base_data[\"sale_start\"])\n", "base_data[\"sale_end\"] = pd.to_datetime(base_data[\"sale_end\"])\n", "base_data[\"cutt_off\"] = pd.to_datetime(base_data[\"cutt_off\"])"]}, {"cell_type": "code", "execution_count": null, "id": "de68b9ba-7fc2-4409-9ef9-c1b46a14c75e", "metadata": {}, "outputs": [], "source": ["base_data[\"updated_at_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "999e009f-8e1a-4cdd-890a-8a8b4361945a", "metadata": {}, "outputs": [], "source": ["base_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "33153cad-0df8-4069-aaaa-c12dd3ba1b2b", "metadata": {}, "outputs": [], "source": ["base_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "15880315-101a-4ef8-8b7a-2d9964d2d8c4", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"facility_id\"},\n", "    {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"item_id\"},\n", "    {\"name\": \"catg_segment\", \"type\": \"VARCHAR\", \"description\": \"segment of category\"},\n", "    {\"name\": \"ptype\", \"type\": \"VARCHAR\", \"description\": \"product_type\"},\n", "    {\"name\": \"festival\", \"type\": \"VARCHAR\", \"description\": \"festival name\"},\n", "    {\"name\": \"group_type\", \"type\": \"VARCHAR\", \"description\": \"category group\"},\n", "    {\n", "        \"name\": \"festival_bau\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"festival or bau article\",\n", "    },\n", "    {\"name\": \"final_qty\", \"type\": \"INTEGER\", \"description\": \"Planned qty for festival\"},\n", "    {\"name\": \"sale_start\", \"type\": \"DATE\", \"description\": \"sale start date\"},\n", "    {\"name\": \"sale_end\", \"type\": \"DATE\", \"description\": \"sale end date\"},\n", "    {\n", "        \"name\": \"cutt_off\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"Cut-off date for festival planning\",\n", "    },\n", "    {\n", "        \"name\": \"initial_qty\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"Initital planning give by catg\",\n", "    },\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"Updated at time\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "94471e40-b840-4c89-9ace-2b91990446bb", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_planning\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"updated_at_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"Used for festivals tracker and track all the data\",\n", "}\n", "\n", "pb.to_trino(base_data, **kwargs)\n", "\n", "print(\"final base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "1f981cd5-5712-4c5e-87bd-9a55bfe60dad", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "30dc869d-848b-4fb8-b3f9-706d9a7616b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2fd663e8-c511-4989-b748-b9827afdc2d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af0a5c83-dcc8-408b-ae18-0321651632b1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5cce8554-7dcd-4ca4-81b1-a411414b3a20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "daf33683-a32d-426e-80ff-4491daf36a65", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}