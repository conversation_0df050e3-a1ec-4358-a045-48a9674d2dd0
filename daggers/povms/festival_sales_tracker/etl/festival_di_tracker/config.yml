alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: festival_di_tracker
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 2
owner:
  email: <EMAIL>
  slack_id: U056F5X9Q5U
path: povms/festival_sales_tracker/etl/festival_di_tracker
paused: false
project_name: festival_sales_tracker
schedule:
  end_date: '2023-12-21T00:00:00'
  interval: 0 1 * * *
  start_date: '2023-09-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
