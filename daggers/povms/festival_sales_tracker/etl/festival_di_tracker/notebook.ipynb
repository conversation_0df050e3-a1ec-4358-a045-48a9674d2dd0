{"cells": [{"cell_type": "code", "execution_count": null, "id": "b2dd1089-4ad5-4516-bad6-0a18429de2e1", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "2e5081f3-188e-4419-97c5-1223160faaf2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1CB02qQeR1HbDsJPYPpn2pKKX6wuh0h9Fe18YumyrplQ\"\n", "sheet_name = \"Master\"\n", "df = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "# Reading the CSV file into a DataFrame\n", "# df = pd.read_csv('HFS-item_list-code.csv')\n", "\n", "# Filtering out rows with empty or NaN values in certain columns\n", "df = df[\n", "    ~(\n", "        (df[\"item_id\"] == \"\")\n", "        | (df[\"item_name\"] == \"\")\n", "        | (df[\"brand_name\"] == \"\")\n", "        | (df[\"manufacturer_name\"] == \"\")\n", "        | (df[\"l0\"] == \"\")\n", "        | (df[\"l1\"] == \"\")\n", "        | (df[\"p_type\"] == \"\")\n", "        | (df[\"item_type\"] == \"\")\n", "        | (df[\"storage_type_name\"] == \"\")\n", "        | (df[\"handling_type\"] == \"\")\n", "    )\n", "]\n", "\n", "# Selecting only the desired columns\n", "final_df_columns = [\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"brand_name\",\n", "    \"manufacturer_name\",\n", "    \"l0\",\n", "    \"l1\",\n", "    \"p_type\",\n", "    \"item_type\",\n", "    \"storage_type_name\",\n", "    \"handling_type\",\n", "]\n", "df = df[final_df_columns]\n", "\n", "# Converting specific columns to the object data type\n", "df[\n", "    [\n", "        \"item_name\",\n", "        \"brand_name\",\n", "        \"manufacturer_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"p_type\",\n", "        \"storage_type_name\",\n", "        \"handling_type\",\n", "    ]\n", "] = df[\n", "    [\n", "        \"item_name\",\n", "        \"brand_name\",\n", "        \"manufacturer_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"p_type\",\n", "        \"storage_type_name\",\n", "        \"handling_type\",\n", "    ]\n", "].astype(\n", "    object\n", ")\n", "\n", "# Filling NaN values in \"item_id\" with 0 and converting the column to integer data type\n", "df[[\"item_id\"]] = df[[\"item_id\"]].fillna(0).astype(int)\n", "\n", "# limiting the character for the column\n", "# df['item_name'] = df['item_name'].str.slice(0, 35)\n", "\n", "# Filtering rows where \"item_id\" is greater than or equal to 0\n", "df = df[df[\"item_id\"] >= 0]\n", "\n", "# Displaying the first few rows of the DataFrame\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": null, "id": "e7f6a145-6c43-43a2-a2b5-1288246c279f", "metadata": {}, "outputs": [], "source": ["# adding updated at column\n", "df[\"updated_at_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "73e65753-da25-4cc2-918e-aa78389808e3", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "58f351dc-64d4-4772-9a49-af0f5eafbfcf", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"unique_item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar\", \"description\": \"brand_name\"},\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"manufacturer_name\",\n", "    },\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"product_category_1\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"product_category_2\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"product_type\"},\n", "    {\"name\": \"item_type\", \"type\": \"varchar\", \"description\": \"item_type\"},\n", "    {\"name\": \"storage_type_name\", \"type\": \"varchar\", \"description\": \"storage_type\"},\n", "    {\"name\": \"handling_type\", \"type\": \"varchar\", \"description\": \"handling_type\"},\n", "    {\"name\": \"updated_at_ist\", \"type\": \"datetime\", \"description\": \"update_timestamp\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9d3d913f-195d-448b-b38f-af8e768bbd03", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"item_base_file_festival\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\"],\n", "    \"sort_key\": [\"item_id\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"base_file_for_festival_item_tracker\",\n", "}\n", "\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "977b46c5-959e-422b-b4bf-23fc56a4c07d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "13daae46-ec78-4d67-a381-3a633d94caa3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "689b82d3-c870-407b-a53f-c80828bed9c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2c60c850-332f-48f5-a1dc-484200c797cb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f9f9e587-9c57-41f6-a35e-37620dea82ba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "17c28736-1de9-4643-a90c-a8f1c4737b50", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "06b2a057-39be-4100-856c-be4743716407", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "12b74880-5d79-490e-885c-7e64ff570d5b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1921d0ec-3880-4c38-ba26-782b6671a1cc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}