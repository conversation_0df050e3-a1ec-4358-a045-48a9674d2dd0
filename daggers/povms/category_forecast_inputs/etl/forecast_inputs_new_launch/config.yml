alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: forecast_inputs_new_launch
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U01JWCA6AJG
path: povms/category_forecast_inputs/etl/forecast_inputs_new_launch
paused: true
pool: povms_pool
project_name: category_forecast_inputs
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 42 0 * * *
  start_date: '2022-01-05T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
