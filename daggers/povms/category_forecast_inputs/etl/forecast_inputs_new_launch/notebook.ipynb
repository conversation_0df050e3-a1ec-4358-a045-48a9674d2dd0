{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "\n", "from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "current_time_ts = datetime.now(IST).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_input_df = pb.from_sheets(\n", "    sheetid=\"1knWEAkIoMeE5vmIGjVizCAoyQVAb-nPTxl_JM1qQd3I\", sheetname=\"new launch\"\n", ")\n", "new_input_df.rename(\n", "    columns={\n", "        \"City name\": \"city_name\",\n", "        \"Item ID\": \"item_id\",\n", "        \"Month(YYYY-MM)\": \"year_month\",\n", "        \"Monthly Quantity\": \"qty\",\n", "        \"Category Manager\": \"category_manager\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_input_df.sort_values(by=[\"qty\"], ascending=False, inplace=True)\n", "new_input_df.drop_duplicates(subset=[\"city_name\", \"item_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = new_input_df[[\"category_manager\", \"city_name\", \"item_id\", \"qty\", \"year_month\"]]\n", "d[\"updated_at\"] = current_time_ts\n", "d = d.astype({\"item_id\": int, \"qty\": float})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"category_manager\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"NAme of category manager who provided the input\",\n", "    },\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"Name of city\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"Item ID\"},\n", "    {\"name\": \"qty\", \"type\": \"float\", \"description\": \"Quantity in put from category\"},\n", "    {\n", "        \"name\": \"year_month\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"year and month for which input is provided\",\n", "    },\n", "    {\n", "        \"name\": \"updated_at\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"timestamp of database updated_at\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"forecast_category_inputs_new_articles\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"city_name\", \"item_id\", \"year_month\"],\n", "    \"sortkey\": [\"year_month\"],\n", "    \"incremental_key\": \"item_id\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Category inputs of forecast for new launched articles\",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(d, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}