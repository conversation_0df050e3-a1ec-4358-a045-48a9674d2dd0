alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: frozen_disruption_outlets_without_tickets
dag_type: alert
escalation_priority: low
execution_timeout: 30
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U072YSLPD4N
path: povms/disruption_jhanvi/alert/frozen_disruption_outlets_without_tickets
paused: false
project_name: disruption_jhanvi
schedule:
  end_date: '2024-08-01T00:00:00'
  interval: 0 * * * *
  start_date: '2024-07-30T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
