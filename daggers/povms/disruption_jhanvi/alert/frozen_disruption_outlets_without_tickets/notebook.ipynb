{"cells": [{"cell_type": "code", "execution_count": null, "id": "f3e23fd2-154a-40dc-a776-9a0ca3bb0a79", "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import pytz\n", "import pencilbox as pb\n", "import time\n", "import math\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "eb3a3e41-b827-4bd1-b494-0aecfe0cbf46", "metadata": {}, "outputs": [], "source": ["def outlets_having_disruption():\n", "    outlets_having_disruption = f\"\"\"\n", "               WITH fo AS (\n", "                        SELECT \n", "                            x.facility_id, \n", "                            x.outlet_id, \n", "                            x.outlet_name, \n", "                            frontend_merchant_id AS merchant_id,\n", "                            frontend_merchant_city_name AS city, \n", "                            CASE \n", "                                WHEN eld.ownership_type IN ('Real Estate', 'Surrogate Store') THEN 'Own' \n", "                                WHEN eld.ownership_type IN ('Partner Store', 'New Partner Store') THEN 'Partner'\n", "                                WHEN sd.mode_of_ops='OWN' then 'Own'\n", "                                when sd.mode_of_ops='MERCHANT' then 'Partner'\n", "                            END AS ownership_type,\n", "                            CASE\n", "                                WHEN eld.cold_room_status = 'Completed' THEN 1\n", "                                WHEN eld.cold_room_status = 'Pending' THEN 0\n", "                            END AS cold_room_flag\n", "                        FROM lake_po.physical_facility_outlet_mapping x\n", "                        INNER JOIN dwh.dim_merchant_outlet_facility_mapping m \n", "                            ON m.pos_outlet_id = x.outlet_id \n", "                            AND is_pos_outlet_active = 1 \n", "                            AND is_backend_merchant_active = true \n", "                            AND is_frontend_merchant_active = true \n", "                            AND is_current \n", "                            AND is_mapping_enabled = true\n", "                        AND x.facility_id IN (\n", "                            SELECT facility_id \n", "                            FROM lake_retail.console_outlet \n", "                            WHERE business_type_id = 7\n", "                        )\n", "                        LEFT JOIN supply_etls.e3_live_darkstores eld \n", "                            ON x.facility_id = eld.facility_id\n", "                        left join storeops_etls.store_details sd on sd.outlet_id=x.outlet_id \n", "\n", "                    ),\n", "                    base AS (\n", "                        SELECT \n", "                            fo.city AS city,\n", "                            node_id AS merchant_id,\n", "                            fo.facility_id,\n", "                            fo.outlet_id,\n", "                            fo.outlet_name,\n", "                            ownership_type,\n", "                            cold_room_flag,\n", "                            CASE \n", "                                WHEN sds.active = true THEN 'IN DISRUPTION' \n", "                                ELSE 'DISRUPTION ENDED' \n", "                            END AS disruption_status,\n", "                            start_ts + INTERVAL '330' minute AS disruption_start_ist,\n", "                            update_ts + INTERVAL '330' minute AS disruption_end_ist\n", "                        FROM serviceability.ser_disruption_schedule sds\n", "                        LEFT JOIN fo \n", "                            ON CAST(fo.merchant_id AS varchar) = sds.node_id\n", "                        WHERE sds.insert_ds_ist > CAST(current_date - INTERVAL '30' day AS varchar)\n", "                        AND reason_code IN ('COLD_ROOM_DOWNTIME')\n", "                        AND NOT (active = false AND start_ts > update_ts)\n", "                        AND outlet_id <> 4146 \n", "                        ORDER BY 1, 2, 10 DESC\n", "                    ),\n", "                    SortedDisruptions AS (\n", "                        SELECT\n", "                            city,\n", "                            merchant_id,\n", "                            facility_id,\n", "                            outlet_id,\n", "                            outlet_name,\n", "                            ownership_type,\n", "                            cold_room_flag,\n", "                            disruption_start_ist,\n", "                            disruption_end_ist,\n", "                            disruption_status,\n", "                            ROW_NUMBER() OVER (PARTITION BY outlet_name ORDER BY disruption_start_ist) AS rn\n", "                        FROM base\n", "                    ),\n", "                    GroupedDisruptions AS (\n", "                        SELECT\n", "                            city,\n", "                            merchant_id,\n", "                            facility_id,\n", "                            outlet_id,\n", "                            outlet_name,\n", "                            ownership_type,\n", "                            cold_room_flag,\n", "                            disruption_start_ist,\n", "                            disruption_end_ist,\n", "                            disruption_status,\n", "                            rn,\n", "                            CASE \n", "                                WHEN disruption_start_ist <= MAX(disruption_end_ist) OVER (\n", "                                    PARTITION BY outlet_name \n", "                                    ORDER BY disruption_start_ist \n", "                                    ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING\n", "                                ) THEN 0\n", "                                ELSE 1\n", "                            END AS new_group\n", "                        FROM SortedDisruptions\n", "                    ),\n", "                    CumulativeGroups AS (\n", "                        SELECT\n", "                            city,\n", "                            merchant_id,\n", "                            facility_id,\n", "                            outlet_id,\n", "                            outlet_name,\n", "                            ownership_type,\n", "                            cold_room_flag,\n", "                            disruption_start_ist,\n", "                            disruption_end_ist,\n", "                            disruption_status,\n", "                            rn,\n", "                            SUM(new_group) OVER (PARTITION BY outlet_name ORDER BY rn) AS group_id\n", "                        FROM GroupedDisruptions\n", "                    ),\n", "                    MergedDisruptions AS (\n", "                        SELECT\n", "                            city,\n", "                            merchant_id,\n", "                            facility_id,\n", "                            outlet_id,\n", "                            outlet_name,\n", "                            ownership_type,\n", "                            cold_room_flag,\n", "                            MAX(disruption_status) AS disruption_status,\n", "                            MIN(disruption_start_ist) AS disruption_start_ist,\n", "                            CASE \n", "                                WHEN MAX(disruption_status) = 'IN DISRUPTION' THEN NULL \n", "                                ELSE MAX(disruption_end_ist) \n", "                            END AS disruption_end_ist\n", "                        FROM CumulativeGroups\n", "                        GROUP BY 1, 2, 3, 4, 5, 6, 7, group_id\n", "                    ),\n", "                    final_base as(\n", "                                SELECT\n", "                                    city,\n", "                                    merchant_id,\n", "                                    facility_id,\n", "                                    outlet_id,\n", "                                    outlet_name,\n", "                                    ownership_type,\n", "                                    cold_room_flag,\n", "                                    disruption_status,\n", "                                    DATE(disruption_start_ist) AS disruption_start_date,\n", "                                    CAST(disruption_start_ist AS varchar) AS disruption_start_ist,\n", "                                    CASE \n", "                                        WHEN disruption_end_ist IS NULL THEN 'N/A' \n", "                                        ELSE CAST(disruption_end_ist AS varchar) \n", "                                    END AS disruption_end_ist,\n", "                                    CASE \n", "                                        WHEN disruption_end_ist IS NULL THEN 'N/A' \n", "                                        ELSE CAST((DATE_DIFF('second', disruption_start_ist, disruption_end_ist) * (1.00) / 3600) AS varchar) \n", "                                    END AS disruption_duration,\n", "                                    COUNT(*) OVER (PARTITION BY outlet_id) AS disruption_count_30_days\n", "                                FROM MergedDisruptions\n", "                                ORDER BY 1, 2, 10 DESC\n", "                    )\n", "                    select outlet_id, outlet_name, disruption_status, disruption_start_ist, disruption_end_ist\n", "                    from final_base\n", "                    where disruption_end_ist ='N/A'\n", "\n", "\n", "            \"\"\"\n", "    return pd.read_sql_query(sql=outlets_having_disruption, con=CON_TRINO)\n", "\n", "\n", "outlets_having_disruption = outlets_having_disruption()\n", "\n", "max_dates = outlets_having_disruption.groupby(\"outlet_id\")[\n", "    \"disruption_start_ist\"\n", "].transform(\"max\")\n", "outlets_having_disruption = outlets_having_disruption[\n", "    outlets_having_disruption[\"disruption_start_ist\"] == max_dates\n", "]\n", "outlets_having_disruption = outlets_having_disruption.drop_duplicates(\n", "    subset=\"outlet_id\"\n", ").reset_index(drop=True)\n", "\n", "outlets_having_disruption"]}, {"cell_type": "code", "execution_count": null, "id": "f02c9b13-33c5-47ab-859a-6b9c0e7e5aa4", "metadata": {}, "outputs": [], "source": ["outlets_having_disruption[\"disruption_start_ist\"] = pd.to_datetime(\n", "    outlets_having_disruption[\"disruption_start_ist\"]\n", ")\n", "\n", "# Find the minimum datetime value\n", "min_disruption_start = outlets_having_disruption[\n", "    \"disruption_start_ist\"\n", "].min() - <PERSON><PERSON><PERSON>(hours=8)\n", "start_date = min_disruption_start.strftime(\"%Y-%m-%dT%H:%M:%S.%fZ\")\n", "start_date"]}, {"cell_type": "code", "execution_count": null, "id": "fd47baab-7740-4d8b-aba7-fd1c95c3ebe1", "metadata": {}, "outputs": [], "source": ["# if sub_event is Walk- In Frozen Room (Ideal Temprature -12°C To -18 °C), then adds it's outlet_id\n", "\n", "\n", "def process_tickets_data(contents):\n", "    asset_code = []\n", "    sub_event = []\n", "    status = []\n", "    created_at_date = []\n", "    history_date = []\n", "\n", "    for i in contents:\n", "\n", "        # convert to lower case(complete string of subevent) if req\n", "        if (\n", "            i.get(\"subevent\")\n", "            == \"Walk- In Frozen Room (Ideal Temprature -12°C To -18 °C)\"\n", "        ):\n", "\n", "            asset_code.append(\n", "                i[\"locationId\"].get(\"assetCode\", None) if \"locationId\" in i else None\n", "            )\n", "            status.append(i.get(\"status\", None))\n", "            sub_event.append(i.get(\"subEvent\", None))\n", "            created_at_date.append(i.get(\"createdAt\", None))\n", "            history_date.append(i.get(\"history_date\", None))\n", "\n", "    d = {\n", "        \"outlet_id\": asset_code,\n", "        \"sub_event\": sub_event,\n", "        \"status\": status,\n", "        \"created_at_date\": created_at_date,\n", "        \"history_date\": history_date,\n", "    }\n", "\n", "    # Convert to DataFrame\n", "    df = pd.DataFrame(d)\n", "    return df\n", "\n", "\n", "# HANDLE THE CASE WHEN ALREADY CLOSED TICKET AND OUTLET COMING IN DISRUPTION"]}, {"cell_type": "code", "execution_count": null, "id": "b40d189e-3134-4fb8-9536-fd1c628428c0", "metadata": {}, "outputs": [], "source": ["current_time = datetime.now().strftime(\"%Y-%m-%dT%H:%M:%S.%fZ\")\n", "\n", "# FIRST CALL OF API TO GET NUMBER OF PAGES !!!!!!!!!!!!!!!!!!!!!!!!!!\n", "\n", "page = 1\n", "limit = 1\n", "\n", "base_url = \"https://blinkitapp.iot.vuelogix.ai/helpdesk/get-ticket-updates/\"\n", "url_params = {\n", "    \"startDate\": start_date,\n", "    \"endDate\": current_time,\n", "    \"dateFilterField\": \"updatedAt\",\n", "    \"page\": page,\n", "    \"limit\": limit,\n", "}\n", "url = base_url + \"?\" + \"&\".join([f\"{key}={value}\" for key, value in url_params.items()])\n", "# response = requests.get(url)\n", "\n", "payload = {}\n", "headers = {\n", "    \"X-APP-ID\": \"a85e850b-c563-431e-ac88-57dbbdc37732\",\n", "    \"X-API-Key\": \"zBRKyiDp2hz5ovmlCMvE6nrP7KyoF0xK0v0Wz9HrRUFDr4Cgjs\",\n", "}\n", "\n", "try:\n", "    response = requests.request(\"GET\", url, headers=headers, data=payload)\n", "    response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)\n", "\n", "    # Check if response contains data\n", "    if response.status_code == 200 and response.json():\n", "        # Process the response data\n", "        data = response.json()\n", "        # print(response.text)\n", "        # print(\"API returned data successfully:\")\n", "\n", "    else:\n", "        print(\"API response was empty or did not contain data\")\n", "\n", "except requests.exceptions.HTTPError as http_err:\n", "    # Handle HTTP errors, including specific status codes\n", "    print(f\"HTTP error occurred: {http_err}\")\n", "\n", "    # Print more details from the response if available\n", "    if response.text:\n", "        print(\"Response details:\")\n", "        print(response.text)\n", "\n", "except requests.exceptions.RequestException as req_err:\n", "    # Handle other request exceptions\n", "    print(f\"An error occurred: {req_err}\")\n", "\n", "except ValueError as val_err:\n", "    # Handle value error if JSON decoding fails\n", "    print(f\"Error decoding JSON: {val_err}\")\n", "\n", "except Exception as ex:\n", "    # Handle other unexpected exceptions\n", "    print(f\"An unexpected error occurred: {ex}\")\n", "\n", "# response = requests.request(\"GET\", url, headers=headers, data=payload)\n", "\n", "print(response.text)\n", "\n", "# data = response.json()"]}, {"cell_type": "code", "execution_count": null, "id": "e59f4c9d-2d2f-447a-bbef-874309b0b0e1", "metadata": {}, "outputs": [], "source": ["# SECOND CALL OF API FOR ALL PAGES !!!!!!!!!!!!!!!!!!!\n", "\n", "limit = 100\n", "num_of_tickets = data[\"totalCount\"]\n", "\n", "num_pages = num_of_tickets / limit\n", "num_pages = math.ceil(num_pages)"]}, {"cell_type": "code", "execution_count": null, "id": "4b3bbc3f-8331-48ee-9119-8874f4b5259c", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame()\n", "\n", "\n", "def fetch_all_tickets(start_date, end_date, limit, num_pages, df):\n", "    # all_data = []\n", "\n", "    for page in range(1, num_pages + 1):\n", "        print(f\"Doing Page Number {page}\")\n", "        url_params = {\n", "            \"startDate\": start_date,\n", "            \"endDate\": end_date,\n", "            \"dateFilterField\": \"updatedAt\",\n", "            \"page\": page,\n", "            \"limit\": limit,\n", "        }\n", "\n", "        url = (\n", "            base_url\n", "            + \"?\"\n", "            + \"&\".join([f\"{key}={value}\" for key, value in url_params.items()])\n", "        )\n", "\n", "        try:\n", "            response = requests.get(url, headers=headers)\n", "            response.raise_for_status()\n", "            # print(response.text)\n", "\n", "            # Check if response contains data\n", "            if response.status_code == 200 and response.json():\n", "                # Append fetched data to all_data\n", "                data = response.json()\n", "                new_df = process_tickets_data(data[\"contents\"])\n", "                df = df.append(new_df, ignore_index=True)\n", "            else:\n", "                print(f\"API response for page {page} was empty or did not contain data\")\n", "\n", "        except requests.exceptions.HTTPError as http_err:\n", "            print(f\"HTTP error occurred for page {page}: {http_err}\")\n", "            if response.text:\n", "                print(\"Response details:\")\n", "                print(response.text)\n", "\n", "        except requests.exceptions.RequestException as req_err:\n", "            print(f\"Request error occurred for page {page}: {req_err}\")\n", "\n", "        except ValueError as val_err:\n", "            print(f\"Error decoding JSON for page {page}: {val_err}\")\n", "\n", "        except Exception as ex:\n", "            print(f\"An unexpected error occurred for page {page}: {ex}\")\n", "\n", "    return df\n", "\n", "\n", "df = fetch_all_tickets(start_date, current_time, limit, num_pages, df)\n", "\n", "# this df has data of req tickets from api\n", "df\n", "\n", "# now want to get data from vuelogix table"]}, {"cell_type": "code", "execution_count": null, "id": "ac92f9e2-8120-4da8-be6c-9c74039e6ae3", "metadata": {}, "outputs": [], "source": ["date_columns = [\"created_at_date\", \"history_date\"]\n", "df[date_columns] = df[date_columns].apply(pd.to_datetime)\n", "df[date_columns] = df[date_columns].apply(lambda x: x + <PERSON><PERSON>ta(hours=5.5))\n", "df[date_columns] = df[date_columns].apply(lambda x: x.dt.strftime(\"%Y-%m-%d %H:%M:%S\"))\n", "current_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "date_columns = [\"created_at_date\", \"history_date\"]\n", "\n", "df[date_columns] = df[date_columns].apply(pd.to_datetime, errors=\"coerce\")"]}, {"cell_type": "code", "execution_count": null, "id": "5bbcadfb-b9ef-4479-9265-2c37fa14220c", "metadata": {}, "outputs": [], "source": ["updated_data_till_now = df"]}, {"cell_type": "code", "execution_count": null, "id": "478123a9-315b-4370-94a7-a52736e6a7ea", "metadata": {}, "outputs": [], "source": ["# Selecting latest status of all tickets\n", "max_dates = updated_data_till_now.groupby(\"outlet_id\")[\"history_date\"].transform(\"max\")\n", "\n", "# Select rows where 'history_date' is equal to the maximum 'history_date' for that 'outlet_id'\n", "latest_df = updated_data_till_now[updated_data_till_now[\"history_date\"] == max_dates]\n", "\n", "# Drop duplicates if there are multiple rows with the same maximum date\n", "latest_df = latest_df.drop_duplicates(subset=\"outlet_id\").reset_index(drop=True)\n", "latest_df[\"outlet_id\"] = latest_df[\"outlet_id\"].astype(int)\n", "latest_df"]}, {"cell_type": "code", "execution_count": null, "id": "493a1fad-d2a5-4669-823d-3323a07912f8", "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(outlets_having_disruption, latest_df, on=\"outlet_id\", how=\"left\")\n", "final_df"]}, {"cell_type": "code", "execution_count": null, "id": "e4f1bcca-5620-4331-b7a8-7a51ca1db22c", "metadata": {}, "outputs": [], "source": ["final_df[\"to_raise\"] = 1\n", "final_df.loc[\n", "    ~final_df[\"status\"].isin([\"CLOSED\", \"RESOLVED\"]) & final_df[\"status\"].notna(),\n", "    \"to_raise\",\n", "] = 0\n", "\n", "final_df[\"to_raise\"] = pd.to_numeric(final_df[\"to_raise\"], errors=\"coerce\")\n", "\n", "final_df[\"to_raise\"] = final_df.apply(\n", "    lambda row: 0\n", "    if pd.notna(row[\"history_date\"])\n", "    and pd.notna(row[\"disruption_start_ist\"])\n", "    and row[\"history_date\"] > row[\"disruption_start_ist\"]\n", "    else row[\"to_raise\"],\n", "    axis=1,\n", ")\n", "final_df = final_df[final_df[\"to_raise\"] == 1]\n", "final_df"]}, {"cell_type": "code", "execution_count": null, "id": "39674b53-b235-4f97-bdd3-63ce5db3aa03", "metadata": {}, "outputs": [], "source": ["index_dict = {}\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    index_dict,\n", "    col_width=5.0,\n", "    row_height=1.325,\n", "    font_size=30,\n", "    header_color=\"#E96125\",\n", "    # other_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width * 2, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        cellLoc=\"center\",\n", "        colLabels=data.columns,\n", "        **kwargs\n", "    )\n", "    # mpl_table.auto_set_column_width(col=list(range(len(df_v1.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "    print(type(mpl_table))\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        # print(k[0:2][1])\n", "        # print(k[0]) # Gets column k[1]\n", "\n", "        # print(mpl_table._cells)\n", "\n", "        if k[0] == 0 or k[0] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\", ma=\"center\")\n", "            cell.set_facecolor(header_color)\n", "            if (k[1] in list(index_dict.keys())) & (k[0] >= 1):\n", "                if k[0] in (index_dict)[k[1]]:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"red\", ma=\"center\")\n", "                else:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"green\", ma=\"center\")\n", "        elif k[0] <= 0:\n", "            cell.set_text_props(weight=\"bold\", ma=\"center\", color=\"black\")\n", "            cell.set_facecolor(\"#eafff5\")\n", "            if (k[1] in list(index_dict.keys())) & (k[0] >= 1):\n", "                if k[0] in (index_dict)[k[1]]:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"red\", ma=\"center\")\n", "                else:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"green\", ma=\"center\")\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "            if (k[1] in list(index_dict.keys())) & (k[0] >= 1):\n", "                if k[0] in (index_dict)[k[1]]:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"red\", ma=\"center\")\n", "                else:\n", "                    cell.set_text_props(weight=\"extra bold\", color=\"green\", ma=\"center\")\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "c641d329-0f25-48df-b990-44b2834b2195", "metadata": {}, "outputs": [], "source": ["if final_df.empty:\n", "    pb.send_slack_message(\n", "        channel=\"test_channel\",\n", "        text=\"No stores on disruption without raised tickets\",\n", "    )\n", "\n", "else:\n", "    disruption_report_image = \"final_df.png\"\n", "    fig1, ax1 = render_mpl_table(final_df, index_dict)\n", "    fig1.savefig(disruption_report_image)\n", "    pb.send_slack_message(\n", "        channel=\"test_channel\",\n", "        text=\"<!channel> Stores on disruption (whose tickets are not raised)\",\n", "        files=[\"final_df.png\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "8453e9a3-1198-4148-abc2-4244beb30ebb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}