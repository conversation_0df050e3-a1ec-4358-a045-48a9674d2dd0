alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: monthly_performance_summary
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07KUHSCH8W
path: povms/email_alert/report/monthly_performance_summary
paused: false
pool: povms_pool
project_name: email_alert
schedule:
  end_date: '2025-08-14T00:00:00'
  interval: 0 7 1 * *
  start_date: '2025-05-21T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
