{"cells": [{"cell_type": "code", "execution_count": null, "id": "87018800-ed22-40d9-a3b4-5b12e83d6216", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "import requests\n", "from requests.exceptions import HTTPError\n", "from tabulate import tabulate\n", "\n", "# start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "66cb5438-a8a0-4e5a-b8ef-ff2f35f15bcf", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e14c41d4-dd3a-49be-a2bb-0d4a86feb1c2", "metadata": {"tags": []}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "            except:\n", "                df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "1e376308-a2bd-4d16-a330-e67a138170a5", "metadata": {"tags": []}, "outputs": [], "source": ["def to_sheets(df, sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            try:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "            except:\n", "                pb.to_sheets(df, sheet_id, sheet_name)\n", "\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed to sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed to sheet in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "2045dca7-46d4-4673-9605-9dd8b45c6f5c", "metadata": {"tags": []}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-data-inventory-pvt\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "a1cd824d-0e83-4001-bdca-95ad829daf9c", "metadata": {}, "outputs": [], "source": ["def tabulate_print(df):\n", "    print(tabulate(df, headers=\"keys\", tablefmt=\"psql\"))"]}, {"cell_type": "code", "execution_count": null, "id": "b8eb1837-7f6c-4edf-a128-f9b501f711aa", "metadata": {"tags": []}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5aa44f4c-ebaf-40e3-9873-62ffa67b3929", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "68053463-914c-4570-8137-cd69d9a44f26", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now()\n", "\n", "previous_month = current_date.replace(day=1) - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "first_date = previous_month.replace(day=1)\n", "last_date = previous_month\n", "\n", "start_date = first_date.strftime(\"%Y-%m-%d\")\n", "end_date = last_date.strftime(\"%Y-%m-%d\")\n", "\n", "# start_date = '2024-08-01'\n", "# end_date = '2024-08-31'\n", "# first_date = datetime(2024, 8, 1, 9, 21, 27, 434676)\n", "# first_date"]}, {"cell_type": "markdown", "id": "0200eb6d-d15a-4aba-ac32-a5fc6cce32be", "metadata": {}, "source": ["### PO Summary and Penalty Summary"]}, {"cell_type": "code", "execution_count": null, "id": "56ba8685-ccde-4354-85a9-83e277d33d23", "metadata": {}, "outputs": [], "source": ["vendor_mails_df = from_sheets(\"1QkyWkeZ6pfHDjrKyTIKPZ0D7VkDV7x4QyqnzQdjYi9c\", \"Vendor email IDs\")\n", "# vendor_mails_df = pd.read_csv(\"Vendor Details - Vendor email IDs.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "33b628e3-1a13-4e9a-a349-3f123c8ed23e", "metadata": {}, "outputs": [], "source": ["vendor_id_all = vendor_mails_df[\"vendor_id\"].tolist()\n", "vendor_id_all_str = \", \".join([str(v_id) for v_id in vendor_id_all])\n", "vendor_id_all_str"]}, {"cell_type": "code", "execution_count": null, "id": "d705d551-6a9a-4360-9c5e-d429fbfd0380", "metadata": {}, "outputs": [], "source": ["raw_data = read_sql_query(\n", "    f\"\"\"\n", "          with\n", "outlet_details as\n", "    (select\n", "        hot_outlet_id,\n", "        facility_id,\n", "        facility_name\n", "        \n", "            from supply_etls.outlet_details\n", "            \n", "                where\n", "                    ars_check = 1\n", "                    and\n", "                        taggings = 'be'\n", "                    and\n", "                        store_type = 'Packaged Goods'\n", "    ),\n", "\n", "item_details as\n", "    (select\n", "        item_id\n", "        \n", "            from supply_etls.item_details\n", "            \n", "                where\n", "                    handling_type = 'Non Packaging Material'\n", "                    and\n", "                        assortment_type = 'Packaged Goods'\n", "    ),\n", "\n", "ars_indent as\n", "    (select\n", "        run_id,\n", "        item_id,\n", "        outlet_id,\n", "\n", "        case\n", "            when load_type = 2 then ((load_size * 1.0000000000)/1000)\n", "            else load_size\n", "        end as load_size,\n", "\n", "        load_type,\n", "        landing_price,\n", "        ((weight_in_gm * 1.0000000000)/1000) as uom,\n", "        \n", "        case_sensitivity_type,\n", "        \n", "        case\n", "            when case_sensitivity_type = 2 then outer_case_size\n", "            when case_sensitivity_type = 1 then inner_case_size\n", "            else 1\n", "        end as case_size\n", "        \n", "            from ars.final_indent\n", "            \n", "                where\n", "                    lake_active_record\n", "                    and\n", "                        run_id like '2025%%'\n", "    ),\n", "\n", "po_id_details as\n", "    (select\n", "        date(coalesce(mis.max_po_grn_at_ist,mis.po_expiry_at_ist)) as grn_date,\n", "        mis.run_id,\n", "        mis.po_id,\n", "        mis.po_number,\n", "        date(mis.po_created_at_ist) as po_issue_date,\n", "        date(mis.po_expiry_at_ist) as po_expiry_date,\n", "        date(mis.po_schedule_at_ist) as po_schedule_date,\n", "        date(mis.max_po_grn_at_ist) as last_grn_date,\n", "        mis.outlet_id,\n", "        mis.vendor_id,\n", "        mis.load_group_id,\n", "        mis.item_id,\n", "        mis.po_quantity,\n", "        mis.grn_quantity,\n", "        mis.remaining_po_quantity,\n", "        mis.total_po_quantity,\n", "        mis.total_grn_quantity,\n", "        mis.total_remaining_quantity,\n", "        mis.po_fill_rate,\n", "        \n", "        case\n", "            when mis.vendor_id in (20598,20539,20861,20851,20599,25404) then 0.05 \n", "            else 0.2 \n", "        end as penalty_price,\n", "        \n", "        mis.is_multiple_grn,\n", "\n", "        ai.load_size,\n", "        \n", "        ai.load_type,\n", "        ai.landing_price,\n", "        ai.uom,\n", "        ai.case_sensitivity_type,\n", "        ai.case_size,\n", "        \n", "        case\n", "            when load_type = 1 then landing_price\n", "            when load_type = 2 then uom\n", "            when load_type = 3 then case_size\n", "            else 1\n", "        end as vendor_type_value,\n", "        \n", "        sum(po_landing_price * po_quantity) over (partition by po_number) as po_value,\n", "        sum(po_landing_price * grn_quantity) over (partition by po_number) as grn_value,\n", "        sum(po_landing_price * remaining_po_quantity) over (partition by po_number) as remaining_po_value,\n", "        \n", "        case\n", "            when ai.case_size is null then 0 \n", "            when (((mis.po_quantity * 1.0000)/nullif(ai.case_size,0)) - ceil((mis.po_quantity * 1.0000)/nullif(ai.case_size,0))) < 0 then 0\n", "            else 1\n", "        end as case_size_flag\n", "\n", "            from supply_etls.inventory_metrics_purchase_mis mis\n", "            \n", "                left join\n", "                    ars_indent ai on ai.item_id = mis.item_id\n", "                    and\n", "                        ai.run_id = mis.run_id\n", "            \n", "                    where \n", "                        insert_ds_ist between date'{start_date}' - interval '60' day and date'{end_date}' + interval '60' day\n", "                        and\n", "                            po_to_be_consider = 1\n", "                        and\n", "                            cast(vendor_id as int) in ({vendor_id_all_str})\n", "    ),\n", "\n", "po_load_size as\n", "    (select\n", "        *,\n", "        (po_quantity * vendor_type_value) as po_value_type,\n", "        sum(po_quantity * vendor_type_value) over (partition by outlet_id, vendor_id, load_group_id, po_issue_date) as po_load_value,\n", "        ((sum(po_quantity * vendor_type_value) over (partition by outlet_id, vendor_id, load_group_id, po_issue_date) * 1.0000)/nullif(load_size,0)) as po_load_ratio\n", "        \n", "            from po_id_details\n", "            \n", "                where\n", "                    po_expiry_date < current_date\n", "    ),\n", "\n", "po_load_check as\n", "    (select\n", "        *,\n", "        case\n", "            when po_load_ratio >= 1 then 'load'\n", "            else 'under_load'\n", "        end as load_flag\n", "        \n", "            from po_load_size\n", "    ),\n", "\n", "extension_details as\n", "    (select\n", "        po_id,\n", "        insert_ds_ist,\n", "        created_at,\n", "        row_number() over (partition by po_id order by created_at) as min_rnk,\n", "        row_number() over (partition by po_id order by created_at desc) as max_rnk\n", "\n", "            from supply_etls.po_extension_logs\n", "\n", "                where \n", "                    insert_ds_ist between date'{start_date}' - interval '60' day and date'{end_date}' + interval '60' day\n", "    ),\n", "\n", "final_extension_details as\n", "    (select\n", "        po_id,\n", "        min(insert_ds_ist) as min_expiry_at,\n", "        max(insert_ds_ist) as max_expiry_at\n", "        \n", "            from extension_details\n", "            \n", "                where\n", "                    (min_rnk = 1 or max_rnk = 1)\n", "                    \n", "                    group by 1\n", "    ),\n", "\n", "final_extension as\n", "    (select\n", "        po_id,\n", "        min_expiry_at,\n", "        max_expiry_at,\n", "        date_diff('day',min_expiry_at, max_expiry_at) as extension_days\n", "        \n", "            from final_extension_details\n", "    ),\n", "\n", "unloading_details as\n", "    (select \n", "        date(max(created_at + interval '330' minute)) as unloading_date,\n", "        -- outlet_id as inv_outlet_id,\n", "        reference_number as po_number\n", "        -- vendor_id\n", "\n", "            from wms.unloading_task\n", "\n", "                where \n", "                    insert_ds_ist between cast(date'{start_date}' - interval '60' day as varchar) and cast(date'{start_date}' + interval '60' day as varchar)\n", "                    and \n", "                        type in ('PO')\n", "                    and \n", "                        lake_active_record\n", "\n", "                    group by 2\n", "    ),\n", "\n", "vendor_name as\n", "    (select\n", "        id,\n", "        vendor_name\n", "        \n", "            from vms.vms_vendor\n", "            \n", "                where\n", "                    active = 1\n", "                    and\n", "                        lake_active_record\n", "    ),\n", "\n", "adding_all as\n", "    (select\n", "        plc.grn_date,\n", "        plc.run_id,\n", "        plc.po_id,\n", "        plc.po_number,\n", "        plc.po_issue_date,\n", "        plc.po_expiry_date,\n", "        plc.po_schedule_date,\n", "        plc.last_grn_date,\n", "        plc.outlet_id,\n", "        od.facility_id,\n", "        od.facility_name,\n", "        plc.vendor_id,\n", "        vn.vendor_name,\n", "        plc.load_group_id,\n", "        count(plc.item_id) as skus_count,\n", "        sum(1 - plc.case_size_flag) as non_case_count,\n", "        -- plc.po_quantity,\n", "        -- plc.grn_quantity,\n", "        -- plc.remaining_po_quantity,\n", "        plc.total_po_quantity,\n", "        plc.total_grn_quantity,\n", "        plc.total_remaining_quantity,\n", "        plc.po_fill_rate,\n", "        plc.penalty_price,\n", "        plc.is_multiple_grn,\n", "        plc.load_size,\n", "        plc.load_type,\n", "        -- plc.landing_price,\n", "        -- plc.uom,\n", "        -- plc.case_sensitivity_type,\n", "        -- plc.case_size,\n", "        plc.po_value,\n", "        plc.grn_value,\n", "        plc.remaining_po_value,\n", "        -- plc.vendor_type_value,\n", "        -- plc.po_value_type,\n", "        plc.po_load_value,\n", "        plc.po_load_ratio,\n", "        plc.load_flag,\n", "        ud.unloading_date,\n", "        fe.min_expiry_at,\n", "        fe.max_expiry_at,\n", "        fe.extension_days,\n", "        \n", "        case \n", "            when plc.load_flag = 'under_load' then 0\n", "            when plc.is_multiple_grn = 1 then 0\n", "            when fe.extension_days > 0 then (fe.extension_days * 5000)\n", "            when (plc.po_schedule_date = plc.last_grn_date) then 0\n", "            when (plc.po_schedule_date = ud.unloading_date) then 0\n", "            when plc.po_schedule_date is null then 0\n", "            else (plc.total_po_quantity * 0.1)\n", "        end as extension_scheduling_penalty_value,\n", "        \n", "        case\n", "            when plc.load_flag = 'under_load' then 0\n", "            when plc.is_multiple_grn = 1 then 0\n", "            when plc.po_fill_rate < 0.95 then ((plc.po_value - plc.grn_value) * plc.penalty_price)\n", "            else 0\n", "        end as fill_rate_penalty_value,\n", "\n", "        case \n", "            when plc.load_flag = 'under_load' then null\n", "            when plc.is_multiple_grn = 1 then null\n", "            when fe.extension_days > 0 then 'extension_penalty'\n", "            when (plc.po_schedule_date = plc.last_grn_date) then null\n", "            when (plc.po_schedule_date = ud.unloading_date) then null\n", "            when plc.po_schedule_date is null then null\n", "            else 'scheduling_penalty'\n", "        end as extension_scheduling_penalty_reason,\n", "\n", "        case\n", "            when plc.load_flag = 'under_load' then null\n", "            when plc.is_multiple_grn = 1 then null\n", "            when plc.po_fill_rate < 0.95 then 'fill_rate_penalty'\n", "        end as fill_rate_penalty_reason,\n", "        \n", "        case\n", "            when plc.is_multiple_grn = 1 then 'multi_grn'\n", "            when plc.run_id like '202%%' and load_flag = 'load' then 'po_to_be_considered'\n", "            else 'adhoc'\n", "        end as po_raised_check,\n", "        \n", "        case \n", "            when plc.load_flag = 'under_load' then null\n", "            when plc.is_multiple_grn = 1 then null\n", "            when plc.po_fill_rate < 0.95 then 'fill_rate_penalty'\n", "            when fe.extension_days > 0 then 'extension_penalty'\n", "            when (plc.po_schedule_date = plc.last_grn_date) then null\n", "            when (plc.po_schedule_date = ud.unloading_date) then null\n", "            when plc.po_schedule_date is null then null\n", "            else 'scheduling_penalty'\n", "        end as total_reason\n", "        \n", "            from po_load_check plc\n", "                \n", "                join\n", "                    outlet_details od on od.hot_outlet_id = plc.outlet_id\n", "                    \n", "                left join\n", "                    final_extension fe on fe.po_id = plc.po_id\n", "\n", "                left join\n", "                    unloading_details ud on ud.po_number = plc.po_number\n", "                \n", "                left join\n", "                    vendor_name vn on vn.id = plc.vendor_id\n", "                    \n", "                    where\n", "                        plc.grn_date between date'{start_date}' and date'{end_date}'\n", "                    \n", "                        group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34\n", "    ),\n", "\n", "    final_view as\n", "    (\n", "        select\n", "            grn_date,\n", "            run_id,\n", "            po_id,\n", "            po_number,\n", "            is_multiple_grn,\n", "            po_issue_date,\n", "            po_expiry_date,\n", "            po_schedule_date,\n", "            last_grn_date,\n", "            unloading_date,\n", "            min_expiry_at,\n", "            max_expiry_at,\n", "            extension_days,\n", "            outlet_id,\n", "            facility_id,\n", "            facility_name,\n", "            vendor_id,\n", "            vendor_name,\n", "            load_group_id,\n", "            load_size,\n", "            load_type,\n", "            skus_count,\n", "            non_case_count,\n", "            total_po_quantity,\n", "            total_grn_quantity,\n", "            total_remaining_quantity,\n", "            penalty_price,\n", "            extension_scheduling_penalty_value as penalty_value,\n", "            extension_scheduling_penalty_reason as penalty_reason,\n", "            -- fill_rate_penalty_value,\n", "            load_flag,\n", "            po_raised_check\n", "            -- fill_rate_penalty_reason\n", "            \n", "                from adding_all\n", "                \n", "                    where\n", "                        (extension_scheduling_penalty_reason = 'extension_penalty')\n", "                        \n", "    union\n", "        \n", "        select\n", "            grn_date,\n", "            run_id,\n", "            po_id,\n", "            po_number,\n", "            is_multiple_grn,\n", "            po_issue_date,\n", "            po_expiry_date,\n", "            po_schedule_date,\n", "            last_grn_date,\n", "            unloading_date,\n", "            min_expiry_at,\n", "            max_expiry_at,\n", "            extension_days,\n", "            outlet_id,\n", "            facility_id,\n", "            facility_name,\n", "            vendor_id,\n", "            vendor_name,\n", "            load_group_id,\n", "            load_size,\n", "            load_type,\n", "            skus_count,\n", "            non_case_count,\n", "            total_po_quantity,\n", "            total_grn_quantity,\n", "            total_remaining_quantity,\n", "            penalty_price,\n", "            extension_scheduling_penalty_value as penalty_value,\n", "            extension_scheduling_penalty_reason as penalty_reason,\n", "            -- fill_rate_penalty_value,\n", "            load_flag,\n", "            po_raised_check\n", "            -- fill_rate_penalty_reason\n", "            \n", "                from adding_all\n", "                \n", "                    where\n", "                        (extension_scheduling_penalty_reason = 'scheduling_penalty')\n", "                        \n", "    union\n", "        \n", "        select\n", "            grn_date,\n", "            run_id,\n", "            po_id,\n", "            po_number,\n", "            is_multiple_grn,\n", "            po_issue_date,\n", "            po_expiry_date,\n", "            po_schedule_date,\n", "            last_grn_date,\n", "            unloading_date,\n", "            min_expiry_at,\n", "            max_expiry_at,\n", "            extension_days,\n", "            outlet_id,\n", "            facility_id,\n", "            facility_name,\n", "            vendor_id,\n", "            vendor_name,\n", "            load_group_id,\n", "            load_size,\n", "            load_type,\n", "            skus_count,\n", "            non_case_count,\n", "            total_po_quantity,\n", "            total_grn_quantity,\n", "            total_remaining_quantity,\n", "            penalty_price,\n", "            fill_rate_penalty_value as penalty_value,\n", "            fill_rate_penalty_reason as penalty_reason,\n", "            load_flag,\n", "            po_raised_check\n", "\n", "                from adding_all\n", "                \n", "                    where\n", "                        (fill_rate_penalty_reason = 'fill_rate_penalty')\n", "\n", "    union\n", "        \n", "        select\n", "            grn_date,\n", "            run_id,\n", "            po_id,\n", "            po_number,\n", "            is_multiple_grn,\n", "            po_issue_date,\n", "            po_expiry_date,\n", "            po_schedule_date,\n", "            last_grn_date,\n", "            unloading_date,\n", "            min_expiry_at,\n", "            max_expiry_at,\n", "            extension_days,\n", "            outlet_id,\n", "            facility_id,\n", "            facility_name,\n", "            vendor_id,\n", "            vendor_name,\n", "            load_group_id,\n", "            load_size,\n", "            load_type,\n", "            skus_count,\n", "            non_case_count,\n", "            total_po_quantity,\n", "            total_grn_quantity,\n", "            total_remaining_quantity,\n", "            penalty_price,\n", "            fill_rate_penalty_value as penalty_value,\n", "            fill_rate_penalty_reason as penalty_reason,\n", "            load_flag,\n", "            po_raised_check\n", "\n", "                from adding_all\n", "                \n", "                    where\n", "                        (total_reason is null)\n", "    ),\n", "\n", "    final_view_x as\n", "    (\n", "        select\n", "            fv.grn_date,\n", "            fv.run_id,\n", "            fv.po_id,\n", "            fv.po_number,\n", "            fv.is_multiple_grn,\n", "            fv.po_issue_date,\n", "            fv.po_expiry_date,\n", "            fv.po_schedule_date,\n", "            fv.last_grn_date,\n", "            fv.unloading_date,\n", "            fv.min_expiry_at,\n", "            fv.max_expiry_at,\n", "            fv.extension_days,\n", "            fv.outlet_id,\n", "            fv.facility_id,\n", "            fv.facility_name,\n", "            fv.vendor_id,\n", "            fv.vendor_name,\n", "            fv.load_group_id,\n", "            fv.load_size,\n", "            fv.load_type,\n", "            fv.skus_count,\n", "            fv.non_case_count,\n", "            fv.total_po_quantity,\n", "            fv.total_grn_quantity,\n", "            fv.total_remaining_quantity,\n", "            aa.po_value,\n", "            aa.grn_value,\n", "            aa.remaining_po_value,\n", "            aa.po_load_value,\n", "            aa.po_load_ratio,\n", "            aa.po_fill_rate,\n", "            fv.penalty_price,\n", "            fv.penalty_value,\n", "            fv.penalty_reason,\n", "            fv.load_flag,\n", "            fv.po_raised_check\n", "            \n", "                from final_view fv\n", "                \n", "                    join\n", "                        adding_all aa on aa.po_number = fv.po_number\n", "    )\n", "    \n", "    select * from final_view_x\n", "    \"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "717109c5-f29d-4c93-ac32-520001ed7f8a", "metadata": {}, "outputs": [], "source": ["raw_data.head()"]}, {"cell_type": "markdown", "id": "133a3c63-bdb6-4cb5-9255-095aa074c358", "metadata": {"tags": []}, "source": ["### vendor and mails inputs"]}, {"cell_type": "code", "execution_count": null, "id": "8a51de27-ceee-4f0f-9f68-e2acb6f32b9a", "metadata": {}, "outputs": [], "source": ["for vendor_id in vendor_id_all:\n", "    # for vendor_id in [24969]:\n", "    try:\n", "        vendor_name = vendor_mails_df.loc[\n", "            vendor_mails_df[\"vendor_id\"] == vendor_id, \"Vendor Name\"\n", "        ].values[0]\n", "        vendor_data = raw_data[raw_data[\"vendor_id\"] == vendor_id].copy()\n", "        vendor_data.to_csv(\n", "            f\"{first_date.strftime('%B %Y')} raw data {vendor_name}.csv\", index=False\n", "        )\n", "\n", "        po_raised = {\n", "            # 'vendor_id': vendor_id,\n", "            \"data\": \"#PO Raised\",\n", "            \"number_of_po\": vendor_data[\"po_number\"].count(),\n", "            \"value_of_po\": vendor_data[\"po_value\"].sum(),\n", "        }\n", "\n", "        po_served_data = vendor_data[vendor_data[\"total_grn_quantity\"] != 0]\n", "        po_served = {\n", "            # 'vendor_id': vendor_id,\n", "            \"data\": \"#PO Served\",\n", "            \"number_of_po\": po_served_data[\"total_grn_quantity\"].count(),\n", "            \"value_of_po\": po_served_data[\"grn_value\"].sum(),\n", "        }\n", "\n", "        po_summary = pd.DataFrame([po_raised, po_served])\n", "        po_summary[\"value_of_po\"] = po_summary[\"value_of_po\"].astype(int)\n", "\n", "        if po_summary.shape[0] > 0:\n", "            # po_summary.to_csv(f\"{first_date.strftime('%B %Y')} po summary {vendor_name}_{vendor_id}.csv\", index=False)\n", "            po_summary_dict = po_summary.to_dict(orient=\"records\")\n", "\n", "        else:\n", "            print(f\"{vendor_name} ka data hi nhi hai humare paas for po_summary\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing data for {vendor_id}: {e} for po_summary\")\n", "\n", "    try:\n", "        penalty_causes = [\n", "            \"#PO with Fill Rate Penalty\",\n", "            \"#PO with Extension Penalty\",\n", "            \"#PO with Schedule Miss Penalty\",\n", "            \"#PO with No Penalty\",\n", "        ]\n", "        vendor_name = vendor_mails_df.loc[\n", "            vendor_mails_df[\"vendor_id\"] == vendor_id, \"Vendor Name\"\n", "        ].values[0]\n", "        vendor_data = raw_data[raw_data[\"vendor_id\"] == vendor_id].copy()\n", "        vendor_data[\"penalty_cause\"] = vendor_data[\"penalty_reason\"].apply(\n", "            lambda x: (\n", "                \"#PO with Fill Rate Penalty\"\n", "                if x == \"fill_rate_penalty\"\n", "                else (\n", "                    \"#PO with Extension Penalty\"\n", "                    if x == \"extension_penalty\"\n", "                    else (\n", "                        \"#PO with Schedule Miss Penalty\"\n", "                        if x == \"scheduling_penalty\"\n", "                        else \"#PO with No Penalty\"\n", "                    )\n", "                )\n", "            )\n", "        )\n", "\n", "        penalty_summary = (\n", "            vendor_data.groupby([\"penalty_cause\"])\n", "            .agg(number_of_po=(\"po_number\", \"count\"), penalty_value=(\"penalty_value\", \"sum\"))\n", "            .reset_index()\n", "        )\n", "\n", "        penalty_summary = (\n", "            penalty_summary.set_index(\"penalty_cause\")\n", "            .reindex(penalty_causes, fill_value=0)\n", "            .reset_index()\n", "        )\n", "\n", "        total_penalty_value = penalty_summary[\n", "            penalty_summary[\"penalty_cause\"] != \"#PO with No Penalty\"\n", "        ][\"penalty_value\"].sum()\n", "        if total_penalty_value <= 0:\n", "            print(f\"{vendor_name} has no penalties\")\n", "        else:\n", "            penalty_summary = penalty_summary.sort_values(by=\"penalty_value\", ascending=False)\n", "            penalty_summary[\"penalty_value\"] = penalty_summary[\"penalty_value\"].astype(int)\n", "            penalty_summary_dict = penalty_summary.to_dict(orient=\"records\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing data for {vendor_id}: {e} for penalty summary\")\n", "\n", "    try:\n", "        vendor_name = vendor_mails_df.loc[\n", "            vendor_mails_df[\"vendor_id\"] == vendor_id, \"Vendor Name\"\n", "        ].values[0]\n", "        vendor_email = vendor_mails_df.loc[\n", "            vendor_mails_df[\"vendor_id\"] == vendor_id, \"email_id\"\n", "        ].values[0]\n", "\n", "        # internal_mail_list = [\n", "        #     \"<EMAIL>\",\n", "        #     \"<EMAIL>\",\n", "        #     \"<EMAIL>\",\n", "        #     \"<EMAIL>\",\n", "        # ]\n", "        internal_mail_list = [\"<EMAIL>\"]\n", "        internal_mail_list = json.dumps(internal_mail_list)\n", "\n", "        # external_mail_list = [vendor_email]\n", "        external_mail_list = [\"<EMAIL>\"]\n", "        external_mail_list = json.dumps(external_mail_list)\n", "\n", "        template_data = {\"penalty_summary\": penalty_summary_dict, \"po_summary\": po_summary_dict}\n", "        template_data_json = json.dumps(template_data)\n", "\n", "        if po_summary.shape[0] > 0 and penalty_summary.shape[0] > 0:\n", "\n", "            file_name = f\"{first_date.strftime('%B %Y')} raw data {vendor_name}.csv\"\n", "            file_path = f\"./{file_name}\"\n", "\n", "            url = \"http://vendor-console-private-canary-retail.prod-sgp-k8s.grofer.io:80/internal/v1/send-email\"\n", "\n", "            payload = {\n", "                \"template_name\": \"po_fill_rate_penalty_email_template\",\n", "                \"to_email_addresses\": str(internal_mail_list),\n", "                \"cc_email_addresses\": str(external_mail_list),\n", "                \"template_data\": template_data_json,\n", "                \"subject\": f\"{vendor_name} ({pd.to_datetime(first_date).strftime('%B %Y')} Performace Summary)\",\n", "                \"has_attachment\": \"true\",\n", "            }\n", "\n", "            files = [(\"file\", (file_name, open(file_path, \"rb\"), \"text/csv\"))]\n", "\n", "            headers = {\n", "                \"x-email-key\": \"I1mXzQXXgsqu4GWY515LlQUtSJKTU2kXpTXhqWocwtpmEeYODeUzd1MFlI1SQ0oy\"\n", "            }\n", "            response = requests.request(\"POST\", url, headers=headers, data=payload, files=files)\n", "            print(response.text)\n", "            # print(payload['subject'])\n", "        else:\n", "            print(\"data hi nhi hai humare paas\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing data for {vendor_id}: {e} for mailing\")"]}, {"cell_type": "code", "execution_count": null, "id": "5657ce31-8852-45b0-a309-b0b6abce763f", "metadata": {}, "outputs": [], "source": ["summary_rows = []\n", "\n", "for vendor_id in vendor_id_all:\n", "    summary = {\n", "        \"Vendor Name\": \"\",\n", "        \"<PERSON><PERSON>\": \"No\",\n", "        \"Has Penalties\": \"No\",\n", "        \"Attachment Sent\": \"No\",\n", "        \"Notes\": \"\",\n", "    }\n", "    try:\n", "        vendor_name = vendor_mails_df.loc[\n", "            vendor_mails_df[\"vendor_id\"] == vendor_id, \"Vendor Name\"\n", "        ].values[0]\n", "        summary[\"Vendor Name\"] = vendor_name\n", "\n", "        vendor_data = raw_data[raw_data[\"vendor_id\"] == vendor_id].copy()\n", "        file_name = f\"{first_date.strftime('%B %Y')} raw data {vendor_name}.csv\"\n", "        file_path = f\"./{file_name}\"\n", "\n", "        if not vendor_data.empty:\n", "            vendor_data.to_csv(file_path, index=False)\n", "            summary[\"Attachment Sent\"] = \"Yes\"\n", "        else:\n", "            summary[\"Notes\"] = \"No vendor data\"\n", "            continue\n", "\n", "        # Penalty summary logic\n", "        po_served_data = vendor_data[vendor_data[\"total_grn_quantity\"] != 0]\n", "        total_penalty_value = vendor_data[\"penalty_value\"].sum()\n", "        if total_penalty_value > 0:\n", "            summary[\"Has Penalties\"] = \"Yes\"\n", "\n", "        # Email sending logic (simulate success for demo)\n", "        email_sent = True  # Replace with actual email sending result\n", "        if email_sent:\n", "            summary[\"<PERSON><PERSON>\"] = \"Yes\"\n", "\n", "    except Exception as e:\n", "        summary[\"Notes\"] = str(e)\n", "\n", "    summary_rows.append(summary)"]}, {"cell_type": "code", "execution_count": null, "id": "b83000e8-8bcc-41fb-862c-28e9a9ca29df", "metadata": {}, "outputs": [], "source": ["summary_df = pd.DataFrame(summary_rows)\n", "summary_df"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}