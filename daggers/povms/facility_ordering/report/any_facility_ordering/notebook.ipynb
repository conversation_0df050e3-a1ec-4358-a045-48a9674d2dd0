{"cells": [{"cell_type": "code", "execution_count": null, "id": "c55404a3-c2b3-45ca-8fcc-7d892f60691c", "metadata": {}, "outputs": [], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": null, "id": "4f934abf-f5ef-4cab-aa96-62f98b28cf3f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "from datetime import datetime\n", "import pencilbox as pb\n", "import itertools\n", "import math\n", "\n", "pd.set_option(\"max_columns\", None)\n", "pd.set_option(\"max_rows\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "20c053d6-a2b0-4b77-8ca6-1eba341fb88d", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")"]}, {"cell_type": "code", "execution_count": null, "id": "e2895d70-0d04-42a0-a4cd-dffc07e8469f", "metadata": {}, "outputs": [], "source": ["def fetchDataFromDB(query):\n", "    import pencilbox as pb\n", "    import pandas as pd\n", "    import time\n", "\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pd.read_sql_query(sql=query, con=con)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "ef0c4cc7-60d4-4e91-b0d6-815076c382d4", "metadata": {}, "outputs": [], "source": ["def read_from_sheet(spreadSheetId, sheetName):\n", "    import pencilbox as pb\n", "    import time\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pb.from_sheets(spreadSheetId, sheetName)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                raise Exception(\n", "                    \"Failed to pushToTrinoMoqLogs after {} attempts\".format(max_retries)\n", "                ) from e"]}, {"cell_type": "code", "execution_count": null, "id": "f229bf49-52a4-4826-a989-786c09abc478", "metadata": {}, "outputs": [], "source": ["def read_facility_ids_from_sheet(spreadSheetId, sheetName):\n", "    import time\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            df = pb.from_sheets(spreadSheetId, sheetName)\n", "            return df\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error fetching data (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to fetch data after {max_retries} attempts: {e}\")\n", "                raise Exception(\n", "                    \"Failed to pushToTrinoMoqLogs after {} attempts\".format(max_retries)\n", "                ) from e"]}, {"cell_type": "code", "execution_count": null, "id": "f8c4f667-b234-4174-96f1-d53f726d6723", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ffd72628-c159-4d5e-b4c3-c5affd2ed7f6", "metadata": {}, "outputs": [], "source": ["# Read the facility sheet\n", "facility_id_df = read_facility_ids_from_sheet(\"1T9hMGkBnREX5XMYB79fYQ-nAPeqoTsrSH37XCU5nGoI\", \"raw\")\n", "# facility_id_df= pd.read_csv('input.csv')\n", "\n", "\n", "# Convert relevant columns to numeric safely\n", "numeric_columns = [\n", "    \"facility_id\",\n", "    \"cpd\",\n", "    \"min qty per outlet\",\n", "    \"po freq multiple for fr less than 75\",\n", "    \"fill rate factor\",  # new column\n", "    \"cpd growth\",\n", "    \"min_qmax\",  # new column\n", "]\n", "\n", "for col in numeric_columns:\n", "    facility_id_df[col] = pd.to_numeric(facility_id_df[col], errors=\"coerce\")\n", "\n", "# Extract unique facility_ids\n", "facility_ids = facility_id_df[\"facility_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "018b314e-8f3d-4ea9-884c-5923437dcbcd", "metadata": {}, "outputs": [], "source": ["facility_ids"]}, {"cell_type": "code", "execution_count": null, "id": "d654dea7-dccf-4bd0-81c7-8ba66f641cca", "metadata": {}, "outputs": [], "source": ["facility_id_df"]}, {"cell_type": "code", "execution_count": null, "id": "25f0bcf4-42f4-4cd0-9b84-a28a59331520", "metadata": {}, "outputs": [], "source": ["def get_run_data(facility_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query5 = \"\"\"\n", "    SELECT *\n", "    FROM ars.job_run\n", "    WHERE success = 1\n", "      AND facility_id ={facility_id}\n", "      AND started_at >= CURRENT_DATE- interval '2' day\n", "      AND CAST(json_extract(simulation_params, '$.run') AS VARCHAR(100)) = 'multi_business'\n", "    \"\"\"\n", "    run_data = fetchDataFromDB(query5.format(facility_id=facility_id))\n", "    # run_data = pd.read_sql(con=Trino_connection_3, sql=query5.format(facility_id=facility_id))\n", "    selected_run_id = run_data[\"run_id\"].iloc[0]\n", "    print(selected_run_id)\n", "    return selected_run_id"]}, {"cell_type": "code", "execution_count": null, "id": "ab7eb626-4edb-40c6-83df-a4328ee54ace", "metadata": {}, "outputs": [], "source": ["def get_assortment_cut_list(tag_value, weighted_avail_cut):\n", "    empty_list = list()\n", "    if weighted_avail_cut == 2:\n", "        return empty_list\n", "\n", "    assortment_query = \"\"\"\n", "    WITH fo AS (\n", "      SELECT\n", "        om.facility_id,\n", "        om.outlet_id AS outlet_id,\n", "        mo_map.frontend_merchant_id AS merchant_id,\n", "        om.outlet_name AS outlet_name,\n", "        rcl.id AS city_id,\n", "        rcl.name AS city_name,\n", "        rcs.name AS state\n", "      FROM\n", "        po.physical_facility_outlet_mapping om\n", "        INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "        AND rco.business_type_id IN (7)\n", "        AND rco.lake_active_record\n", "        INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "        AND rcl.lake_active_record\n", "        INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "        AND rco.business_type_id = 7\n", "        AND is_current\n", "        AND is_current_mapping_active\n", "        AND is_backend_merchant_active\n", "        AND is_frontend_merchant_active\n", "        INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "        AND rcs.lake_active_record\n", "      WHERE\n", "        om.active = 1\n", "        AND om.ars_active = 1\n", "        AND om.lake_active_record\n", "        AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "        AND om.facility_id != 273\n", "    ),\n", "    catalog_ AS (\n", "      SELECT\n", "        *\n", "      FROM\n", "        (\n", "          SELECT\n", "            icd.item_id,\n", "            icd.name,\n", "            icd.l0,\n", "            icd.l1,\n", "            icd.l2,\n", "            icd.product_type,\n", "            CASE\n", "              WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "              ELSE 'PACKAGED'\n", "            END AS item_type,\n", "            id.storage_type AS storage_type_raw,\n", "            CASE\n", "              WHEN id.storage_type IN (\n", "                '1',\n", "                '8',\n", "                '11'\n", "              ) THEN 'REGULAR'\n", "              WHEN id.storage_type IN ('4', '5') THEN 'HEAVY'\n", "              WHEN id.storage_type IN ('2', '6') THEN 'COLD'\n", "              WHEN id.storage_type IN ('3', '7') THEN 'FROZEN'\n", "              ELSE 'REGULAR'\n", "            END AS storage_type,\n", "            CASE\n", "              WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "              WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "              ELSE 'REGULAR'\n", "            END AS handling_type,\n", "            id.variant_mrp AS mrp,\n", "            id.variant_description,\n", "            id.weight_in_gm,\n", "            id.length_in_cm,\n", "            id.height_in_cm,\n", "            id.breadth_in_cm,\n", "            id.shelf_life,\n", "            coalesce(itf.item_factor, 0.01) AS item_factor,\n", "            DATE_DIFF('day', date(icd.created_at), CURRENT_DATE) AS item_catalog_age,\n", "            rank() OVER (\n", "              PARTITION BY id.item_id\n", "              ORDER BY\n", "                id.id DESC\n", "            ) AS variant_rank,\n", "            itm.tag_value AS custom_storage_type_raw,\n", "            CASE\n", "              WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "              WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "              WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "              WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "              WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "              WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "              WHEN itm.tag_value = '7' THEN 'TOYS'\n", "              WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "              WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "              WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "              WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "              WHEN itm.tag_value = '13' THEN 'LARGE'\n", "              WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "              WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "              WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "              WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "              WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "              WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "              WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "              WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "              ELSE 'UNKNOWN_CONFIG'\n", "            END AS custom_storage_type,\n", "            pb.manufacturer_id,\n", "            id.manufacturer AS manufacturer_name,\n", "            pb.name AS brand_name,\n", "            id.outer_case_size,\n", "            id.inner_case_size,\n", "            CASE\n", "              WHEN itm2.tag_value = '1' THEN TRUE\n", "              ELSE FALSE\n", "            END AS is_high_value,\n", "            itm2.tag_value AS high_value_tag_raw\n", "          FROM\n", "            rpc.item_category_details icd\n", "            INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "            AND id.active = 1\n", "            AND id.approved = 1\n", "            AND id.lake_active_record\n", "            LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "            LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "            AND itm.active = TRUE\n", "            AND itm.lake_active_record\n", "            AND itm.item_id = icd.item_id\n", "            LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "            AND pb.lake_active_record\n", "            AND pb.active = 1\n", "            LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "            AND itm2.active\n", "            AND itm2.tag_type_id = 3\n", "            AND itm2.lake_active_record\n", "          WHERE\n", "            icd.lake_active_record\n", "            AND perishable != 1\n", "            AND id.handling_type != '8'\n", "            AND id.storage_type NOT IN ('3', '7')\n", "            AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "            AND icd.l0 NOT IN (\n", "              'wholesale store',\n", "              'Trial new tree',\n", "              'Specials'\n", "            ) -- removing test and flyer/freebie l0s\n", "        ) AS x\n", "      WHERE\n", "        variant_rank = 1\n", "    ),\n", "    cpd_base AS (\n", "      SELECT\n", "        *\n", "      FROM\n", "        (\n", "          SELECT\n", "            cpd.*,\n", "            rank() OVER (\n", "              PARTITION BY cpd.item_id,\n", "              cpd.outlet_id\n", "              ORDER BY\n", "                cpd.updated_at DESC\n", "            ) AS cpd_rank\n", "          FROM\n", "            ars.outlet_item_aps_derived_cpd cpd\n", "            INNER JOIN fo ON fo.outlet_id = cpd.outlet_id\n", "          WHERE\n", "            created_at >= CURRENT_DATE - interval '2' DAY\n", "            AND insert_ds_ist >= cast(CURRENT_DATE - interval '2' DAY AS varchar)\n", "            AND aps_adjusted > 0\n", "            AND lake_active_record\n", "        ) AS x\n", "      WHERE\n", "        cpd_rank = 1\n", "    ),\n", "    base AS (\n", "      SELECT\n", "        coalesce(try_cast(iotm.tag_value AS int), 0) AS be_outlet_id,\n", "        pfma.item_id,\n", "        c.item_factor,\n", "        c.l0,\n", "        c.l1,\n", "        c.l2,\n", "        c.product_type,\n", "        pfma.assortment_type,\n", "        sum(coalesce(cpd.aps_adjusted, 0)) AS aps_cpd\n", "      FROM\n", "        rpc.product_facility_master_assortment pfma\n", "        INNER JOIN catalog_ c ON c.item_id = pfma.item_id\n", "        INNER JOIN rpc.product_master_assortment_substate_reasons sr ON sr.id = pfma.substate_reason_id\n", "        AND sr.reason_type != 'SAMPLING'\n", "        AND sr.lake_active_record\n", "        INNER JOIN fo ON fo.facility_id = pfma.facility_id\n", "        INNER JOIN rpc.item_outlet_tag_mapping iotm ON iotm.tag_type_id = 8\n", "        AND iotm.active = 1\n", "        AND iotm.lake_active_record\n", "        AND iotm.outlet_id = fo.outlet_id\n", "        AND iotm.item_id = pfma.item_id\n", "        LEFT JOIN cpd_base cpd ON cpd.item_id = pfma.item_id\n", "        AND cpd.outlet_id = fo.outlet_id\n", "      WHERE\n", "        pfma.lake_active_record\n", "        AND pfma.active = 1\n", "        AND pfma.master_assortment_substate_id IN (1, 3)\n", "      GROUP BY\n", "        1,\n", "        2,\n", "        3,\n", "        4,\n", "        5,\n", "        6,\n", "        7,\n", "        8\n", "    ),\n", "    base_1 AS (\n", "      SELECT\n", "        be_outlet_id,\n", "        assortment_type,\n", "        l0,\n", "        l1,\n", "        l2,\n", "        item_factor,\n", "        item_id,\n", "        aps_cpd,\n", "        aps_cpd / (\n", "          sum(aps_cpd) OVER (PARTITION BY be_outlet_id, assortment_type)\n", "        ) AS normalized_cpd,\n", "        aps_cpd / (\n", "          sum(aps_cpd) OVER (\n", "            PARTITION BY be_outlet_id,\n", "            assortment_type,\n", "            l0,\n", "            l1,\n", "            l2\n", "          )\n", "        ) AS normalized_cpd_l2\n", "      FROM\n", "        base\n", "      WHERE\n", "        be_outlet_id != 0\n", "    ),\n", "    final_base as (\n", "      SELECT\n", "        be_outlet_id,\n", "        assortment_type,\n", "        l0,\n", "        l1,\n", "        l2,\n", "        item_factor,\n", "        item_id,\n", "        aps_cpd,\n", "        normalized_cpd,\n", "        normalized_cpd_l2,\n", "        SUM(\n", "          case\n", "            when is_nan(normalized_cpd) then 1\n", "            else normalized_cpd\n", "          end\n", "        ) OVER (\n", "          PARTITION BY be_outlet_id,\n", "          assortment_type\n", "          ORDER BY\n", "            aps_cpd desc,\n", "            item_factor asc\n", "        ) AS cumulative_cpd_weight,\n", "        SUM(\n", "          case\n", "            when is_nan(normalized_cpd_l2) then 1\n", "            else normalized_cpd_l2\n", "          end\n", "        ) OVER (\n", "          PARTITION BY be_outlet_id,\n", "          assortment_type,\n", "          l0,\n", "          l1,\n", "          l2\n", "          ORDER BY\n", "            aps_cpd desc,\n", "            item_factor asc\n", "        ) AS cumulative_cpd_weight_l2\n", "      FROM\n", "        base_1\n", "      ORDER BY\n", "        11 asc\n", "    )\n", "    select\n", "      be_outlet_id,\n", "      item_id,\n", "      min(cumulative_cpd_weight) as cumulative_cpd_weight,\n", "      least(\n", "        min(cumulative_cpd_weight_l2),\n", "        min(cumulative_cpd_weight)\n", "      ) as cumulative_cpd_weight_l2\n", "    from\n", "      final_base\n", "    where\n", "      item_id not in (\n", "        SELECT\n", "          DISTINCT item_id\n", "        FROM\n", "          ars.bulk_process_event_planner\n", "        WHERE\n", "          active = 1\n", "          and planned_qty > 0\n", "          AND partition_field IS NOT NULL\n", "          AND sale_end_date + interval '5' DAY >= CURRENT_DATE\n", "          AND sale_start_date - interval '20' DAY <= CURRENT_DATE\n", "      )\n", "      and item_id not in (\n", "        select\n", "          distinct(item_id)\n", "        from\n", "          seller.seller_product_mappings\n", "      )\n", "      and item_id not in (\n", "        select\n", "          item_id\n", "        from\n", "          rpc.item_tag_mapping\n", "        where\n", "          tag_type_id = 7\n", "          and tag_value = '21'\n", "          and active\n", "          and lake_active_record\n", "      )\n", "      and item_id not in (\n", "        select\n", "          distinct(\n", "            cast(\n", "              trim(\n", "                ']['\n", "                from\n", "                  (array_join(split(val, ','), ','))\n", "              ) as int\n", "            )\n", "          ) as item_id\n", "        from\n", "          ars.ars_config\n", "          cross join UNNEST(split(value, ',')) as t(val)\n", "        where\n", "          name = 'no_truncation_items'\n", "          and lake_active_record\n", "      )\n", "      and item_id not in (\n", "        select\n", "          distinct item_id\n", "        from\n", "          rpc.item_category_details\n", "        where\n", "          l1_id in (6366, 1349, 1015, 1342, 661)\n", "          and lake_active_record\n", "      )\n", "      and be_outlet_id = {be_outlet_id}\n", "    group by\n", "      1,\n", "      2\n", "\n", "\n", "    \"\"\".format(\n", "        be_outlet_id=tag_value\n", "    )\n", "\n", "    not_ordering_assortment = fetchDataFromDB(assortment_query)\n", "    not_ordering_assortment = not_ordering_assortment[\n", "        not_ordering_assortment[\"cumulative_cpd_weight\"] > weighted_avail_cut\n", "    ]\n", "    not_ordering_item_list = list(not_ordering_assortment[\"item_id\"].unique())\n", "\n", "    return not_ordering_item_list"]}, {"cell_type": "code", "execution_count": null, "id": "71b049e9-c086-44af-8649-fcb87830a8b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bdc8baeb-fd89-46b3-ae15-eef59553b30d", "metadata": {}, "outputs": [], "source": ["def get_indent_data(selected_run_id, tag_value):\n", "    print(\"i am here1\")\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query5 = f\"\"\"\n", "    SELECT * FROM ars.final_indent \n", "    WHERE run_id='{selected_run_id}'\n", "    \"\"\"\n", "    print(\"i am here2\")\n", "    indent_data = fetchDataFromDB(query5)\n", "\n", "    assortment_input = read_from_sheet(\n", "        \"1IefbfrS_9NypI07Ffo-EgDchAbfDZMnbxbYARBflXRk\", \"assortment_cut_weighted_avail\"\n", "    )\n", "    # assortment_input = pd.read_csv('input1.csv')\n", "    assortment_input[\"be_outlet_id\"] = assortment_input[\"be_outlet_id\"].astype(int)\n", "    assortment_input[\"weighted_avail_cut\"] = assortment_input[\"weighted_avail_cut\"].astype(float)\n", "\n", "    weighted_avail_cut = assortment_input.set_index(\"be_outlet_id\")[\"weighted_avail_cut\"].get(\n", "        tag_value, 2\n", "    )\n", "\n", "    not_ordering_item_list = get_assortment_cut_list(tag_value, weighted_avail_cut)\n", "    print(\"Not ordering list length - \", len(not_ordering_item_list))\n", "\n", "    print(indent_data.shape)\n", "    indent_data = indent_data[~indent_data[\"item_id\"].isin(not_ordering_item_list)]\n", "\n", "    if indent_data.empty:\n", "        raise ValueError(f\"No data found for Run ID {selected_run_id}\")\n", "\n", "    print(indent_data.shape)\n", "    return indent_data"]}, {"cell_type": "code", "execution_count": null, "id": "ae4ff6da-0e4f-46f2-91a0-7ab3156a846a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "476a97cb-34e7-4b91-8483-a789a5892090", "metadata": {}, "outputs": [], "source": ["def get_outlet_cpd_data(selected_run_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query5 = f\"\"\"\n", "    SELECT * \n", "    FROM ars.outlet_item_cpd\n", "    WHERE CAST(insert_ds_ist AS DATE) >= CURRENT_DATE - INTERVAL '1' DAY\n", "    and run_id='{selected_run_id}' \n", "    \"\"\"\n", "    outlet_cpd_data = fetchDataFromDB(query5)\n", "    # outlet_cpd_data = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "    return outlet_cpd_data"]}, {"cell_type": "code", "execution_count": null, "id": "cd5cab14-8dc5-442c-9543-3c079ed7ef1c", "metadata": {}, "outputs": [], "source": ["def get_facility_details(facility_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query5 = \"\"\"\n", "    select distinct co.facility_id, \n", "            co.name as be_name, \n", "            pfom.outlet_id as tag_value,\n", "            case \n", "                when wom.cloud_store_id is null then pfom.outlet_id \n", "                else wom.cloud_store_id \n", "            end as be_outlet_id\n", "        from po.physical_facility_outlet_mapping pfom -- Transfers and Inventory Outlet ids are same,since this table has ars_flag, the outlet_id in this table is for transfers and used by ARS\n", "        left join retail.warehouse_outlet_mapping wom on pfom.outlet_id = wom.warehouse_id\n", "        left join retail.console_outlet co on co.id = pfom.outlet_id and co.active = 1\n", "        where pfom.active = 1 \n", "        and pfom.ars_active = 1\n", "        and pfom.lake_active_record\n", "        and wom.lake_active_record\n", "        and co.facility_id= {facility_id}\n", "    \"\"\"\n", "    # Pass parameters using the params argument in read_sql\n", "    # B = pd.read_sql(con=Trino_connection_3, sql=query5.format(facility_id=facility_id))\n", "    B = fetchDataFromDB(query5.format(facility_id=facility_id))\n", "    print(B.head())\n", "    return B"]}, {"cell_type": "code", "execution_count": null, "id": "8000a75b-ad4a-482d-a411-655e0972c9f7", "metadata": {}, "outputs": [], "source": ["def get_outlet_data(facility_id, selected_run_id, end_date, item_list):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query5 = \"\"\"\n", "    SELECT DISTINCT to_outlet_id \n", "    FROM ars.frontend_cycle_sto_quantity \n", "    WHERE run_id='{selected_run_id}'\n", "    and partition_field='{end_date}'\n", "    and item_id in {item_list}\n", "    \"\"\"\n", "    outlet_data = fetchDataFromDB(\n", "        query5.format(selected_run_id=selected_run_id, end_date=end_date, item_list=item_list)\n", "    )\n", "    # outlet_data = pd.read_sql(con=Trino_connection_3, sql=query5.format(selected_run_id =selected_run_id,end_date=end_date,item_list=item_list))\n", "    print(outlet_data.head())\n", "    return outlet_data"]}, {"cell_type": "code", "execution_count": null, "id": "d364e83c-8859-4560-b6a2-983f36555461", "metadata": {}, "outputs": [], "source": ["def get_ds_connected(selected_run_id, end_date, item_list):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query5 = \"\"\"\n", "    SELECT item_id,count(DISTINCT to_outlet_id) as number_of_ds_connected_1\n", "    FROM ars.frontend_cycle_sto_quantity \n", "    WHERE run_id='{selected_run_id}'\n", "    and partition_field='{end_date}'\n", "    and item_id in {item_list}\n", "    --and item_id= 10164428\n", "    group by 1\n", "    \"\"\"\n", "    item_active_ds_count = fetchDataFromDB(\n", "        query5.format(selected_run_id=selected_run_id, end_date=end_date, item_list=item_list)\n", "    )\n", "    # item_active_ds_count = pd.read_sql(con=Trino_connection_3, sql=query5.format(selected_run_id =selected_run_id,end_date=end_date,item_list=item_list))\n", "    item_active_ds_count.fillna(0, inplace=True)\n", "    print(item_active_ds_count.head())\n", "    return item_active_ds_count"]}, {"cell_type": "code", "execution_count": null, "id": "8b6bf702-b222-4cf7-b6b1-9b8ceab296a4", "metadata": {}, "outputs": [], "source": ["def get_high_value_tag():\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query5 = \"\"\"\n", "    SELECT *\n", "    FROM rpc.item_tag_mapping\n", "    WHERE CAST(tag_type_id AS integer) = 3\n", "      AND CAST(tag_value AS integer) = 1\n", "    \"\"\"\n", "    high_value_tag = fetchDataFromDB(query5)\n", "    # high_value_tag = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "    return high_value_tag"]}, {"cell_type": "code", "execution_count": null, "id": "f43a41cf-b09f-4bed-9d12-bf4ea1cbe24d", "metadata": {}, "outputs": [], "source": ["Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "query5 = \"\"\"\n", "--select * from rpc.item_tag_mapping  where tag_type_id = 3\n", "\n", "SELECT *\n", "FROM rpc.item_tag_mapping\n", "WHERE CAST(tag_type_id AS integer) = 3\n", "  AND CAST(tag_value AS integer) = 1\n", "\"\"\"\n", "high_value_tag = fetchDataFromDB(query5)\n", "# high_value_tag = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "high_value_tag.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2d0470ed-8c22-4625-abb2-c0fae4645743", "metadata": {}, "outputs": [], "source": ["def get_active_ds_count(item_list, tag_value):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Convert the list of item IDs to a string suitable for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    query5 = f\"\"\"\n", "    with first as (SELECT pfom.facility_id,\n", "           pfom.outlet_id,\n", "           pfom.outlet_name,\n", "           pfma.item_id,\n", "           assortment_type,\n", "           pp.name,\n", "           CASE\n", "               WHEN pfma.master_assortment_substate_id = 1 THEN 'Active'\n", "               WHEN pfma.master_assortment_substate_id = 2 THEN 'Inactive'\n", "               WHEN pfma.master_assortment_substate_id = 3 THEN 'Temp-Inactive'\n", "               ELSE 'Others'\n", "           END AS assortment_status,\n", "           CASE\n", "               WHEN iotm.active = 1 THEN 'TEA Tagged'\n", "               ELSE 'Not TEA Tagged'\n", "           END AS tea_status,\n", "           pfom_be.outlet_id AS be_outlet_id,\n", "           pfom_be.outlet_name AS be_name,\n", "           pfma.updated_at + interval '330' minute as assortment_update_ts,\n", "           iotm.updated_at + interval '330' minute as tea_updated_ts\n", "    FROM rpc.product_facility_master_assortment pfma\n", "    INNER JOIN po.physical_facility_outlet_mapping pfom ON pfom.facility_id = pfma.facility_id\n", "    LEFT JOIN rpc.item_outlet_tag_mapping iotm ON iotm.item_id = pfma.item_id\n", "    AND pfom.outlet_id = iotm.outlet_id\n", "    AND tag_type_id = 8\n", "    INNER JOIN po.physical_facility_outlet_mapping pfom_be ON pfom_be.outlet_id = cast(iotm.tag_value as int)\n", "    INNER JOIN rpc.item_details pp ON pp.item_id = pfma.item_id\n", "    WHERE pfom_be.outlet_id={tag_value}\n", "      AND pfma.item_id in {item_list}\n", "    AND pfma.master_assortment_substate_id NOT IN (2, 3)\n", "      ),\n", "\n", "      second as (SELECT \n", "        outlet_id,\n", "        COUNT(DISTINCT CASE WHEN date(cart_checkout_ts_ist) BETWEEN CURRENT_DATE - INTERVAL '7' DAY AND CURRENT_DATE THEN cart_id END) AS delivered_orders\n", "    FROM dwh.fact_sales_order_item_details \n", "    WHERE \n", "        order_current_status='DELIVERED' AND\n", "        order_type IN ('RetailForwardOrder', 'DropShippingForwardOrder') AND\n", "        outlet_id > 1\n", "        and date(order_create_dt_ist) between CURRENT_DATE - INTERVAL '7' DAY AND CURRENT_DATE \n", "\n", "    GROUP BY 1),\n", "\n", "     third as ( select x.*,y.delivered_orders from first x\n", "      left join second y on x.outlet_id=y.outlet_id\n", "      where y.delivered_orders>0)\n", "\n", "     select item_id,count(distinct outlet_id) as distinct_outlets  from third \n", "      group by 1\n", "    \"\"\"\n", "    item_active_ds_count_1 = fetchDataFromDB(query5)\n", "    # item_active_ds_count_1 = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "    item_active_ds_count_1.fillna(0, inplace=True)\n", "    print(item_active_ds_count_1.head())\n", "    return item_active_ds_count_1"]}, {"cell_type": "code", "execution_count": null, "id": "72690f66-a6a9-45d0-bf73-57b2ed9080ab", "metadata": {}, "outputs": [], "source": ["# def get_forecast_cpd(tag_value):\n", "#     Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "#     # Define the SQL query with the placeholder for tag_value\n", "#     query5 = \"\"\"\n", "\n", "#     SELECT\n", "#     outlet_id,\n", "#     item_id,\n", "#     avg_cpd\n", "#     FROM ars.case_incorporation_result\n", "#     WHERE insert_ds_ist = CAST(current_date - INTERVAL '1' DAY AS VARCHAR)\n", "#     AND outlet_id = {tag_value}\n", "\n", "\n", "#     --SELECT outlet_id, item_id, avg_cpd FROM ars.case_incorporation_result\n", "#     --WHERE insert_ds_ist >= CAST(CURRENT_DATE AS VARCHAR) AND outlet_id={tag_value}\n", "#     \"\"\"\n", "#     # Execute the query and fetch the data\n", "#     forecast_cpd_1 = pd.read_sql(con=Trino_connection_3, sql=query5.format(tag_value=tag_value))\n", "#     # Rename the column 'avg_cpd' to 'Avg_procured_qty'\n", "#     forecast_cpd_1.rename(columns={'avg_cpd': 'Avg_procured_qty'}, inplace=True)\n", "#     # Multiply the 'Avg_procured_qty' column by 1 (placeholder for other possible operations)\n", "#     forecast_cpd_1['Avg_procured_qty'] = forecast_cpd_1['Avg_procured_qty'] * 1\n", "#     print(forecast_cpd_1.head())\n", "#     return forecast_cpd_1"]}, {"cell_type": "code", "execution_count": null, "id": "14002430-7425-434a-a603-ee31c34efae5", "metadata": {}, "outputs": [], "source": ["# Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "# # Define the SQL query with the placeholder for tag_value\n", "# query5 = \"\"\"\n", "\n", "# SELECT\n", "# outlet_id,\n", "# item_id,\n", "# avg_cpd\n", "# FROM ars.case_incorporation_result\n", "# WHERE insert_ds_ist = CAST(current_date AS VARCHAR)\n", "# AND outlet_id = 4885\n", "\n", "# \"\"\"\n", "# # Execute the query and fetch the data\n", "# forecast_cpd_1 = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "# # Rename the column 'avg_cpd' to 'Avg_procured_qty'\n", "# forecast_cpd_1.rename(columns={'avg_cpd': 'Avg_procured_qty'}, inplace=True)\n", "# # Multiply the 'Avg_procured_qty' column by 1 (placeholder for other possible operations)\n", "# forecast_cpd_1['Avg_procured_qty'] = forecast_cpd_1['Avg_procured_qty'] * 1\n", "# forecast_cpd_1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4407053c-e47f-4d48-ad96-ee7d98e10861", "metadata": {}, "outputs": [], "source": ["# forecast_cpd_1.info()"]}, {"cell_type": "code", "execution_count": null, "id": "ecb4efa6-d208-4eef-84cb-68032c674894", "metadata": {}, "outputs": [], "source": ["def get_new_forecast_cpd(facility_id):\n", "    # Establish the connection (adjust connection details as needed)\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    # Define the SQL query, safely formatting the facility_id\n", "    query5 = f\"\"\"\n", "    SELECT * \n", "    FROM     supply_etls.packaged_good_item_backend_forecast\n", "    --supply_etls.packaged_good_item_backend_forecast \n", "    WHERE run_date = current_date - interval '1' day  AND be_facility_id = {facility_id}\n", "    \"\"\"\n", "\n", "    # Execute the query and fetch the data\n", "    forecast_cpd_new = fetchDataFromDB(query5)\n", "\n", "    # forecast_cpd_new = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "\n", "    # Rename the 'forecast_date' column to 'date_'\n", "    forecast_cpd_new.rename(columns={\"forecast_date\": \"date_\"}, inplace=True)\n", "\n", "    # Return the DataFrame\n", "    return forecast_cpd_new"]}, {"cell_type": "code", "execution_count": null, "id": "6ecf5dcb-c493-4adc-8062-e829a414b829", "metadata": {}, "outputs": [], "source": ["# Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "# # Define the SQL query, safely formatting the facility_id\n", "# query5 = f\"\"\"\n", "# SELECT *\n", "# FROM supply_etls.packaged_good_item_backend_forecast\n", "# WHERE run_date >= current_date AND be_facility_id = 2469\n", "# \"\"\"\n", "\n", "# # Execute the query and fetch the data\n", "# forecast_cpd_new = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "\n", "# # Rename the 'forecast_date' column to 'date_'\n", "# forecast_cpd_new.rename(columns={'forecast_date': 'date_'}, inplace=True)\n", "# forecast_cpd_new.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f4ef6032-dc40-4353-bb91-6f64225512cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd651c65-fb66-4793-bd7a-e752de2fa61d", "metadata": {}, "outputs": [], "source": ["def get_forecast_cpd(tag_value):\n", "    \"\"\"\n", "    Fetch forecast data for a given be_outlet_id (tag_value), compute an average\n", "    of the row-wise max between 'forecast_cpd1' and 'category_proposed_cpd'\n", "    within the first 14 days from the earliest date in each group,\n", "    and return both the aggregated DataFrame and the raw data.\n", "    \"\"\"\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    query5 = \"\"\"\n", "    \n", "\n", "    WITH latest_run AS (\n", "        SELECT MAX(run_date) AS run_date\n", "        FROM supply_etls.packaged_good_item_outlet_forecast\n", "        WHERE be_outlet_id = {be_outlet_id}\n", "        and run_date is not null\n", "    ),\n", "\n", "    additional_outlets_by_date AS (\n", "        SELECT\n", "            forecast_date,\n", "            outlet_id\n", "        FROM supply_etls.fc_rampup_input\n", "        WHERE insert_ds_ist IS NOT NULL\n", "          AND be_outlet_id    = {be_outlet_id}\n", "    ),\n", "\n", "    filtered_forecast AS (\n", "        SELECT\n", "            f.run_date,\n", "            f.forecast_date,\n", "            f.item_id,\n", "            f.outlet_id,\n", "            f.be_outlet_id,\n", "            f.final_forecast,\n", "            f.forecast_cpd2\n", "        FROM supply_etls.packaged_good_item_outlet_forecast f\n", "        JOIN latest_run lr            ON f.run_date = lr.run_date      -- ← uses the latest date\n", "        WHERE f.item_outlet_state = 1 and f.run_date is not null\n", "          AND (\n", "                f.be_outlet_id = {be_outlet_id}\n", "             OR EXISTS (\n", "                    SELECT 1\n", "                    FROM additional_outlets_by_date a\n", "                    WHERE a.forecast_date = f.forecast_date\n", "                      AND a.outlet_id     = f.outlet_id\n", "                )\n", "              )\n", "          AND f.forecast_date\n", "                BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '44' DAY\n", "    )\n", "\n", "\n", "    SELECT\n", "        run_date,\n", "        {be_outlet_id} AS be_outlet_id,\n", "        item_id,\n", "        forecast_date,\n", "        SUM(final_forecast) AS new_forecast,\n", "        SUM(forecast_cpd2)  AS forecast_cpd2\n", "    FROM filtered_forecast\n", "\n", "    GROUP BY 1,2,3,4\n", "    ORDER BY 1,2\n", "    \n", "    \"\"\".format(\n", "        be_outlet_id=tag_value\n", "    )\n", "\n", "    forecast_cpd_new = fetchDataFromDB(query5)\n", "\n", "    print(forecast_cpd_new.head(1))\n", "\n", "    # Rename 'forecast_date' to 'date_' and convert to datetime\n", "    forecast_cpd_new.rename(columns={\"forecast_date\": \"date_\"}, inplace=True)\n", "    forecast_cpd_new[\"date_\"] = pd.to_datetime(forecast_cpd_new[\"date_\"], errors=\"coerce\")\n", "\n", "    # forecast_cpd_new = forecast_cpd_new.drop(\"new_forecast\", axis=1)\n", "    # forecast_cpd_new[\"new_forecast\"] = forecast_cpd_new[[\"forecast_cpd1\", \"category_proposed_cpd\"]].max(axis=1)\n", "\n", "    def avg_new_forecast_first_7_df(df):\n", "        \"\"\"\n", "        1) Find the earliest 'date_' in the sub-DataFrame.\n", "        2) Filter rows within the first 14 days of that earliest date.\n", "        3) For each filtered row, compute the row-wise max of 'forecast_cpd1' and 'category_proposed_cpd'.\n", "        4) Return the mean of that row-wise max.\n", "        \"\"\"\n", "        start_date = df[\"date_\"].min()\n", "        mask = df[\"date_\"] < start_date + pd.Timedelta(days=14)\n", "        df_filtered = df.loc[mask]\n", "\n", "        # Take the row-wise max between 'forecast_cpd1' and 'category_proposed_cpd'\n", "        # row_max = df_filtered[['forecast_cpd1', 'category_proposed_cpd']].max(axis=1)\n", "        row_max = df_filtered[[\"forecast_cpd2\", \"forecast_cpd2\"]].max(axis=1)\n", "\n", "        # Return the mean of that row-wise maximum\n", "        return row_max.mean()\n", "\n", "    # Group by (item_id, be_outlet_id), apply aggregator, rename and reset index\n", "    forecast_cpd_1 = (\n", "        forecast_cpd_new.groupby([\"item_id\", \"be_outlet_id\"])\n", "        .apply(avg_new_forecast_first_7_df)\n", "        .rename(\"avg_new_forecast\")  # Rename the resulting Series\n", "        .reset_index()  # Convert the group keys into columns\n", "    )\n", "\n", "    # Convert item_id to int64\n", "    forecast_cpd_1[\"item_id\"] = forecast_cpd_1[\"item_id\"].astype(\"int64\")\n", "\n", "    # Rename to final column name\n", "    forecast_cpd_1.rename(columns={\"avg_new_forecast\": \"avg_cpd\"}, inplace=True)\n", "    forecast_cpd_1.rename(columns={\"avg_cpd\": \"Avg_procured_qty\"}, inplace=True)\n", "\n", "    # Replace NaN or 0 with 1 in 'Avg_procured_qty'\n", "    forecast_cpd_1[\"Avg_procured_qty\"] = forecast_cpd_1[\"Avg_procured_qty\"].fillna(1).replace(0, 1)\n", "\n", "    # Return both the aggregated DataFrame and the raw forecast data\n", "    return forecast_cpd_1, forecast_cpd_new"]}, {"cell_type": "code", "execution_count": null, "id": "bae62a01-f35a-4f68-994c-1a466b8158aa", "metadata": {}, "outputs": [], "source": ["# result_df.to_csv('result_df.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "d8327850-c988-4a9c-b681-f347c7da42e8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c8a3f212-bc8a-4e00-90a3-b978b692867a", "metadata": {}, "outputs": [], "source": ["def handle_cpd_hikes(facility_id, forecast_cpd_1):\n", "    if facility_id in facility_id_df[\"facility_id\"].values:\n", "        row = facility_id_df.loc[facility_id_df[\"facility_id\"] == facility_id]\n", "        cpd_value = row[\"cpd\"].iloc[0]\n", "        po_freq_multiple = row[\"po freq multiple for fr less than 75\"].iloc[0]\n", "        cpd_growth = row[\"cpd growth\"].iloc[0]\n", "\n", "        # Handle NaN for cpd_value\n", "        if pd.isna(cpd_value):\n", "            # Return forecast_cpd_1 unmodified, plus a default n\n", "            return forecast_cpd_1, 1\n", "\n", "        total_avg_procured_qty = forecast_cpd_1[\"Avg_procured_qty\"].sum()\n", "\n", "        if total_avg_procured_qty > 0:\n", "            n = cpd_value / total_avg_procured_qty\n", "        else:\n", "            n = 1\n", "\n", "        # If cpd_growth != 0, don't allow n < 1\n", "        if cpd_growth != 0:\n", "            n = max(n, 1)\n", "\n", "        # Multiply unconditionally\n", "        forecast_cpd_1[\"Avg_procured_qty\"] *= n\n", "\n", "        return forecast_cpd_1, n\n", "\n", "    # If facility_id not found, return original DataFrame and a default n\n", "    return forecast_cpd_1, 1\n", "\n", "\n", "# Then call:\n", "# forecast_cpd_1, n = handle_cpd_hikes(facility_id, forecast_cpd_1)"]}, {"cell_type": "code", "execution_count": null, "id": "e93ac063-bad2-4384-b2c0-026891392bab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "31b69fa7-2598-4ca0-9a3b-1fc99ec40b04", "metadata": {}, "outputs": [], "source": ["def get_sales_data(start_date, end_date, item_list, outlet_list):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Format the item_list and outlet_list for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    outlet_list_str = \"(\" + \", \".join(map(str, outlet_list)) + \")\"\n", "    query4 = f\"\"\"\n", "            select date(cart_checkout_ts_ist) as date_,\n", "                    fsoid.outlet_id,icd.item_id,\n", "                    sum(product_quantity*multiplier) as procured_quantity\n", "            from dwh.fact_sales_order_item_details fsoid \n", "            inner join dwh.dim_item_product_offer_mapping om on fsoid.product_id = om.product_id\n", "            inner join rpc.item_category_details icd on om.item_id = icd.item_id\n", "           -- where date(cart_checkout_ts_ist) between date('{start_date}') and date('{end_date}')\n", "            where date(order_create_dt_ist) between date('{start_date}') and date('{end_date}')\n", "                and order_current_status = 'DELIVERED'\n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "                and icd.item_id in {item_list}\n", "                and fsoid.outlet_id in {outlet_list}\n", "                and om.is_current = true\n", "            group by 1,2,3\n", "    \"\"\"\n", "    sale_data = fetchDataFromDB(query4)\n", "\n", "    print(sale_data.head())\n", "    return sale_data"]}, {"cell_type": "code", "execution_count": null, "id": "869b5db5-1bc7-4064-9fd7-cd1086e5bb2b", "metadata": {}, "outputs": [], "source": ["def get_po_data(item_list, outlet_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Format the item_list for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    query5 = f\"\"\"\n", "    SELECT DISTINCT p.id AS po_id,\n", "                    p.po_number AS po_number,\n", "                    o.facility_id,\n", "                    issue_date + interval '330' MINUTE AS issue_date,\n", "                                                          expiry_date + interval '330' MINUTE AS expiry_date,\n", "                                                                                                 date(ps.schedule_date_time + interval '330' MINUTE) AS scheduled_date,\n", "                                                                                                 poi.item_id,\n", "                                                                                                 poi.units_ordered AS expected_po_qty,\n", "                                                                                                 posta.name,\n", "                                                                                                 grn.created_at + interval '330' MINUTE AS grn_ts,\n", "                                                                                                                                           grn.quantity AS grn_qty\n", "    FROM po.purchase_order p\n", "    LEFT JOIN po.po_schedule ps ON p.id=ps.po_id_id\n", "    INNER JOIN po.purchase_order_items poi ON p.id=poi.po_id\n", "    INNER JOIN retail.console_outlet o ON o.id=p.outlet_id\n", "    INNER JOIN po.purchase_order_status posa ON posa.po_id = p.id\n", "    INNER JOIN po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "    LEFT JOIN po.po_grn grn ON grn.item_id = poi.item_id\n", "    AND grn.po_id = p.id and insert_ds_ist >= cast(CURRENT_DATE - interval '60' DAY AS varchar)\n", "    WHERE \n", "      facility_id IN\n", "        (SELECT DISTINCT facility_id\n", "         FROM retail.console_outlet\n", "         WHERE id IN\n", "             (SELECT cast(tag_value as int)\n", "              FROM rpc.item_outlet_tag_mapping\n", "              WHERE item_id in {item_list}\n", "                AND outlet_id ={outlet_id}\n", "                AND tag_type_id = 8\n", "                AND active = 1 ))\n", "    and poi.item_id in {item_list}\n", "    and p.po_type_id not in (11,9)\n", "    ORDER BY po_id DESC\n", "    \"\"\"\n", "    po_data = fetchDataFromDB(query5)\n", "    # po_data = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "    print(po_data.head())\n", "    return po_data"]}, {"cell_type": "code", "execution_count": null, "id": "78b74c7e-1eb3-4fad-ac37-af41e71f8a33", "metadata": {}, "outputs": [], "source": ["def get_holding_quantity(item_list, outlet_list):\n", "    # Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    # Convert the item_list and outlet_list into strings for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    outlet_list_str = \"(\" + \", \".join(map(str, outlet_list)) + \")\"\n", "\n", "    # query4 = f\"\"\"\n", "    # select item_id, outlet_id,sum(quantity) as holding_quantity from ims.ims_item_inventory\n", "    # where item_id in {item_list}\n", "    # and outlet_id in {outlet_list}\n", "    # group by 1,2\n", "    # \"\"\"\n", "\n", "    query4 = f\"\"\"\n", "    \n", "    select try_cast(ii.item_id as int) as item_id,\n", "       try_cast(ii.outlet_id as int) as outlet_id,\n", "       case when (sum(quantity - coalesce(blocked_inv, 0))) < 0 then 0 else (sum(quantity - coalesce(blocked_inv, 0))) end as holding_quantity\n", "    from dynamodb.blinkit_store_inventory_service_oi_rt_view as ii\n", "    left join (\n", "        select item_id, outlet_id, sum(quantity) blocked_inv\n", "        from dynamodb.blinkit_store_inventory_service_blk_rt_view\n", "        where status = 'BLOCKED'\n", "          and reference_type not in ('DL_VALIDATION_CRON', 'PNA')\n", "        group by 1, 2\n", "    ) ib on ib.item_id = ii.item_id and ib.outlet_id = ii.outlet_id\n", "    where ii.state = 'GOOD'\n", "    and try_cast(ii.outlet_id as int) in {outlet_list}\n", "    and try_cast(ii.item_id as int ) in {item_list}\n", "    group by 1, 2\n", "    \n", "    \"\"\"\n", "\n", "    # holding_quantity_1 = pd.read_sql(con=Trino_connection_3, sql=query4)\n", "    holding_quantity_1 = fetchDataFromDB(query4)\n", "    print(holding_quantity_1.head())\n", "    return holding_quantity_1"]}, {"cell_type": "code", "execution_count": null, "id": "d46b3491-6b50-49ba-83cd-e88101895e9e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f76a454f-f43e-40ce-a9f9-85813ee5fcc2", "metadata": {}, "outputs": [], "source": ["def get_open_po_data(item_list, end_date, facility_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Convert the item_list into a string suitable for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    query5 = f\"\"\"\n", "    select date(po_created_at_ist) as issue_date,date(po_schedule_at_ist) as po_schedule_date,date(po_expiry_at_ist) as po_expiry_date,\n", "    item_id, sum(remaining_po_quantity) as landing_po_quantity\n", "    from supply_etls.inventory_metrics_open_po\n", "    where item_id in {item_list}\n", "    and facility_id ={facility_id}\n", "    and date(po_created_at_ist) != date('{end_date}')\n", "    group by 1,2,3,4\n", "    \"\"\"\n", "    # open_po = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "    open_po = fetchDataFromDB(query5)\n", "    print(open_po.head())\n", "    return open_po"]}, {"cell_type": "code", "execution_count": null, "id": "78bc3a1d-c4be-4fd8-81d7-5821c671d032", "metadata": {}, "outputs": [], "source": ["def get_holding_quantity_1(item_list, outlet_list):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Convert the item_list and outlet_list into strings for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    outlet_list_str = \"(\" + \", \".join(map(str, outlet_list)) + \")\"\n", "    query4 = f\"\"\"\n", "    select item_id, sum(quantity) as holding_quantity from ims.ims_item_inventory\n", "    where item_id in {item_list}\n", "    and outlet_id in {outlet_list}\n", "    group by 1\n", "    \"\"\"\n", "    holding_quantity_1 = fetchDataFromDB(query4)\n", "    # holding_quantity_1 = pd.read_sql(con=Trino_connection_3, sql=query4)\n", "    print(holding_quantity_1.head())\n", "    return holding_quantity_1"]}, {"cell_type": "code", "execution_count": null, "id": "a9ec0e84-6f2f-4f85-b9ac-f0c8bdff4b8a", "metadata": {}, "outputs": [], "source": ["def get_available_inventory(item_list, wh_outlet_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Convert item_list into a string format suitable for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    query4 = f\"\"\"\n", "            with iii as (\n", "                select outlet_id,\n", "                    item_id,\n", "                    sum(quantity) as inventory\n", "                from ims.ims_item_inventory\n", "                where item_id IN {item_list}\n", "                    and outlet_id ={wh_outlet_id}\n", "                    and active = 1 \n", "                    and lake_active_record\n", "                group by 1,2\n", "            ),\n", "\n", "            iib as (\n", "                select outlet_id, \n", "                    iib.item_id, \n", "                    sum(iib.quantity) as blocked_inventory\n", "                from ims.ims_item_blocked_inventory iib\n", "                where item_id IN {item_list}\n", "                    and outlet_id ={wh_outlet_id}\n", "                    and lake_active_record\n", "                group by 1,2\n", "            )\n", "            select iii.outlet_id,\n", "                iii.item_id,\n", "                inventory,\n", "                blocked_inventory,\n", "                case when (inventory - blocked_inventory) < 0 then 0 else (iii.inventory - COALESCE(iib.blocked_inventory, 0))\n", "                --(inventory - blocked_inventory) \n", "                end as actual_quantity\n", "            from iii\n", "            left join iib on iii.outlet_id = iib.outlet_id and iii.item_id = iib.item_id\n", "    \"\"\"\n", "    # item_inventory_df = pd.read_sql(con=Trino_connection_3, sql=query4)\n", "    item_inventory_df = fetchDataFromDB(query4)\n", "    print(item_inventory_df.head())\n", "    return item_inventory_df"]}, {"cell_type": "code", "execution_count": null, "id": "10348d59-e0c1-4904-bcdf-fb9312c5ac9b", "metadata": {}, "outputs": [], "source": ["# def get_available_inventory(item_list, wh_outlet_id):\n", "#     Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "#     # Convert item_list into a string format suitable for SQL IN clause\n", "#     item_list_str = '(' + ', '.join(map(str, item_list)) + ')'\n", "#     query4 = f\"\"\"\n", "#             with iii as (\n", "#                 select outlet_id,\n", "#                     item_id,\n", "#                     sum(quantity) as inventory\n", "#                 from ims.ims_item_inventory\n", "#                 where item_id IN {item_list}\n", "#                     and outlet_id =5634\n", "#                     and active = 1\n", "#                     and lake_active_record\n", "#                 group by 1,2\n", "#             ),\n", "\n", "#             iib as (\n", "#                 select outlet_id,\n", "#                     iib.item_id,\n", "#                     sum(iib.quantity) as blocked_inventory\n", "#                 from ims.ims_item_blocked_inventory iib\n", "#                 where item_id IN {item_list}\n", "#                     and outlet_id =5634\n", "#                     and lake_active_record\n", "#                 group by 1,2\n", "#             )\n", "#             select iii.outlet_id,\n", "#                 iii.item_id,\n", "#                 inventory,\n", "#                 blocked_inventory,\n", "#                 case when (inventory - blocked_inventory) < 0 then 0 else (iii.inventory - COALESCE(iib.blocked_inventory, 0))\n", "#                 --(inventory - blocked_inventory)\n", "#                 end as actual_quantity\n", "#             from iii\n", "#             left join iib on iii.outlet_id = iib.outlet_id and iii.item_id = iib.item_id\n", "#     \"\"\"\n", "#     item_inventory_df = pd.read_sql(con=Trino_connection_3, sql=query4)\n", "#     print(item_inventory_df.head())\n", "#     return item_inventory_df"]}, {"cell_type": "code", "execution_count": null, "id": "4cbd2f63-0272-4a2d-b958-49192bb0bfbd", "metadata": {}, "outputs": [], "source": ["# def get_pending_putaway(wh_outlet_id):\n", "#     Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "#     query4 = f\"\"\"\n", "#     SELECT y.item_id,sum(x.quantity) as quantity\n", "#     FROM ims.ims_good_inventory x\n", "#     INNER JOIN rpc.product_product y\n", "#     ON x.variant_id = y.variant_id\n", "#     WHERE x.outlet_id = {wh_outlet_id}\n", "#     AND x.inventory_update_type_id IN (28, 76)\n", "#     AND CAST(x.updated_at AS DATE) >=DATE '2025-02-17'\n", "#     group by 1\n", "#     \"\"\"\n", "#     pending_put = fetchDataFromDB(query4)\n", "#     # pending_put = pd.read_sql(con=Trino_connection_3, sql=query4)\n", "#     print(pending_put.head())\n", "#     return pending_put"]}, {"cell_type": "code", "execution_count": null, "id": "552acbbc-194e-43e9-b506-c9bb28c762ec", "metadata": {}, "outputs": [], "source": ["def get_pending_putaway(wh_outlet_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    query4 = f\"\"\"\n", "    SELECT \n", "        y.item_id,\n", "        SUM(x.quantity) AS quantity\n", "    FROM ims.ims_good_inventory x\n", "    INNER JOIN rpc.product_product y \n", "        ON x.variant_id = y.variant_id\n", "    WHERE x.outlet_id = {wh_outlet_id}\n", "      AND x.inventory_update_type_id IN (28, 76)\n", "      AND CAST(x.updated_at AS DATE) >= CURRENT_DATE - INTERVAL '7' DAY\n", "    GROUP BY y.item_id\n", "    \"\"\"\n", "    pending_put = fetchDataFromDB(query4)\n", "    # pending_put = pd.read_sql(con=Trino_connection_3, sql=query4)\n", "    print(pending_put.head())\n", "    return pending_put"]}, {"cell_type": "code", "execution_count": null, "id": "0265e68a-2287-4f35-aa5d-0e901a57e64b", "metadata": {}, "outputs": [], "source": ["def get_open_sto_quantity(item_list, wh_outlet_id):\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "    # Convert item_list into a string format suitable for SQL IN clause\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    query5 = f\"\"\"\n", "    with sto_items as (\n", "        select * from ims.ims_sto_item\n", "        where created_at > CURRENT_DATE - interval '7' day\n", "        AND item_id IN {item_list}\n", "       ),\n", "\n", "     sto_details as (\n", "        select * from ims.ims_sto_details\n", "        where created_at > CURRENT_DATE - interval '7' day\n", "        and sto_state IN (1,2,5)\n", "        and outlet_id= {wh_outlet_id}\n", "       ),   \n", "    open_sto_base AS\n", "      ( SELECT s.outlet_id, isi.item_id,\n", "               sum(reserved_quantity-inward_quantity-released_billed_quantity-released_reserved_quantity) AS total_left_quantity\n", "       FROM sto_items isi\n", "       INNER JOIN sto_details s ON isi.sto_id=s.sto_id\n", "       GROUP BY 1,2)\n", "     select outlet_id,item_id, total_left_quantity as open_sto_quantity from open_sto_base \n", "    \"\"\"\n", "    # open_sto_quantity = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "    open_sto_quantity = fetchDataFromDB(query5)\n", "    print(open_sto_quantity.head())\n", "    return open_sto_quantity"]}, {"cell_type": "code", "execution_count": null, "id": "ed116f15-38cf-43da-9c5c-6445e401bcdd", "metadata": {}, "outputs": [], "source": ["def get_min_qmax(item_list, outlet_list, facility_id):\n", "    \"\"\"\n", "    Fetch min-qty, moq and qmax for the given items & outlets.\n", "\n", "    Parameters\n", "    ----------\n", "    item_list : list[int] | list[str]\n", "        One or more `item_id` values.\n", "    outlet_list : list[int] | list[str]\n", "        One or more `outlet_id` values.\n", "    facility_id : int | None\n", "        Kept for API compatibility; not used in current query.\n", "    \"\"\"\n", "    Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "    # --- build comma-separated IN-clause strings ----------------------------\n", "    item_list_str = \"(\" + \", \".join(map(str, item_list)) + \")\"\n", "    outlet_list_str = \"(\" + \", \".join(map(str, outlet_list)) + \")\"\n", "\n", "    # query5 = f\"\"\"\n", "    # SELECT\n", "    #     outlet_id,\n", "    #     item_id,\n", "    #     cpd_org                                                         AS aps_cpd,\n", "    #     ROUND(GREATEST(2.0000 / run_count * cpd_org, final_min_qty))    AS min_qty,\n", "    #     GREATEST(\n", "    #         CASE\n", "    #             WHEN n > 0 THEN ROUND(GREATEST(n * cpd_org, 1))\n", "    #             ELSE ROUND(GREATEST(3 * cpd_org, 1))\n", "    #         END,\n", "    #         moq\n", "    #     )                                                               AS moq,\n", "    #     GREATEST(\n", "    #         CASE\n", "    #             WHEN n > 0 THEN ROUND(GREATEST(n * cpd_org, 1))\n", "    #                           + ROUND(GREATEST(2.0000 / run_count * cpd_org, final_min_qty))\n", "    #             ELSE ROUND(GREATEST(3 * cpd_org, 1))\n", "    #                           + ROUND(GREATEST(2.0000 / run_count * cpd_org, final_min_qty))\n", "    #         END,\n", "    #         qmax\n", "    #     )                                                               AS qmax\n", "    # FROM supply_etls.store_item_qmax_moq_quantity\n", "    # WHERE insert_ds_ist = cast( current_date - interval'1'day as varchar )\n", "    #   AND outlet_id IN {outlet_list}\n", "    #   AND item_id   IN {item_list}\n", "    # \"\"\"\n", "\n", "    query5 = f\"\"\"\n", "            with base as (\n", "          select\n", "            *,\n", "            rank() over (\n", "              partition by outlet_id\n", "              order by\n", "                updated_at desc\n", "            ) as rank_\n", "          from\n", "            supply_etls.store_item_qmax_moq_quantity\n", "          where\n", "            insert_ds_ist >= cast(current_date - interval '4' day as varchar)\n", "            and outlet_id in {outlet_list}\n", "            and item_id in {item_list}\n", "        )\n", "        select\n", "          outlet_id,\n", "          item_id,\n", "          cpd_org as aps_cpd,\n", "          greatest(\n", "            floor(2.0000 / greatest(run_count, 1) * cpd_org),\n", "            final_min_qty\n", "          ) as min_qty,\n", "          greatest(\n", "            case\n", "              when n > 0 then greatest(floor(n * cpd_org), 1)\n", "              else greatest(floor(3 * cpd_org), 1)\n", "            end,\n", "            moq\n", "          ) as moq,\n", "          greatest(\n", "            case\n", "              when n > 0 then greatest(floor(n * cpd_org), 1)\n", "              else greatest(floor(3 * cpd_org), 1)\n", "            end,\n", "            moq\n", "          ) + greatest(\n", "            floor(2.0000 / greatest(run_count, 1) * cpd_org),\n", "            final_min_qty\n", "          ) as qmax --   ,\n", "          --   n,\n", "          --   final_min_qty,\n", "          --   moq,\n", "          --   qmax\n", "        from\n", "          base\n", "        where\n", "          rank_ = 1\n", "    \"\"\"\n", "    min_qmax = pd.read_sql(con=Trino_connection_3, sql=query5)\n", "    # min_qmax['facility_id']=facility_id\n", "    return min_qmax"]}, {"cell_type": "code", "execution_count": null, "id": "cf0c25fa-f871-4529-8481-3106bca5d17b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a89bdf6a-0661-4857-bd14-2e2e5e3fd4d0", "metadata": {}, "outputs": [], "source": ["def remove_unusual_sales(sale_data, indent_data):\n", "    sale_data_1 = sale_data[[\"date_\", \"outlet_id\", \"item_id\", \"procured_quantity\"]]\n", "    sale_data_1.rename(columns={\"procured_quantity\": \"procured_quantity_1\"}, inplace=True)\n", "    sale_data_1[\"procured_quantity_1\"] = pd.to_numeric(\n", "        sale_data_1[\"procured_quantity_1\"], errors=\"coerce\"\n", "    )\n", "    sale_data_1.head()\n", "    sale_data = pd.merge(\n", "        sale_data,\n", "        indent_data[[\"aligned_vendor_id\", \"group_id\", \"item_id\"]],\n", "        how=\"left\",\n", "        on=\"item_id\",\n", "    )\n", "    sale_data_1 = pd.merge(\n", "        sale_data_1,\n", "        indent_data[[\"aligned_vendor_id\", \"group_id\", \"item_id\"]],\n", "        how=\"left\",\n", "        on=\"item_id\",\n", "    )\n", "    grouped_df = (\n", "        sale_data_1.groupby([\"date_\", \"item_id\"])[\"procured_quantity_1\"].sum().reset_index()\n", "    )\n", "    lower_percentile = grouped_df.groupby(\"item_id\")[\"procured_quantity_1\"].quantile(0.30)\n", "    upper_percentile = grouped_df.groupby(\"item_id\")[\"procured_quantity_1\"].quantile(0.95)\n", "    # Map these thresholds back to the original DataFrame\n", "    grouped_df[\"lower_threshold\"] = grouped_df[\"item_id\"].map(lower_percentile)\n", "    grouped_df[\"upper_threshold\"] = grouped_df[\"item_id\"].map(upper_percentile)\n", "    # Filter outliers based on percentiles\n", "    unusual_df = grouped_df[\n", "        (grouped_df[\"procured_quantity_1\"] < grouped_df[\"lower_threshold\"])\n", "        | (grouped_df[\"procured_quantity_1\"] > grouped_df[\"upper_threshold\"])\n", "    ].reset_index(drop=True)\n", "    sale_data_1[\"date_\"] = pd.to_datetime(sale_data_1[\"date_\"])\n", "    unusual_df[\"date_\"] = pd.to_datetime(unusual_df[\"date_\"])\n", "    # Perform a left merge to identify rows to exclude\n", "    merged_df = sale_data_1.merge(\n", "        unusual_df[[\"date_\", \"item_id\"]], on=[\"date_\", \"item_id\"], how=\"left\", indicator=True\n", "    )\n", "    # Filter rows where there is no match in unusual_df\n", "    filtered_sale_data_1 = merged_df[merged_df[\"_merge\"] == \"left_only\"]\n", "    # Drop the merge indicator column as it's no longer needed\n", "    filtered_sale_data_1.drop(\"_merge\", axis=1, inplace=True)\n", "    sale_data_1 = filtered_sale_data_1\n", "    grouped_df_1 = (\n", "        sale_data_1.groupby([\"date_\", \"item_id\"])[\"procured_quantity_1\"].sum().reset_index()\n", "    )\n", "    return grouped_df_1, sale_data_1"]}, {"cell_type": "code", "execution_count": null, "id": "c49767df-f640-43d3-b4b6-556821454a4d", "metadata": {}, "outputs": [], "source": ["def find_next_po_day(row):\n", "    days = row[\"po_days\"].split(\",\")\n", "    current_day = row[\"day\"]\n", "    # Define the order of days\n", "    day_order = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"]\n", "    # Find the index of the current day\n", "    current_day_index = day_order.index(current_day)\n", "    # Find the next available day in po_days\n", "    for i in range(1, len(day_order)):\n", "        next_day_index = (current_day_index + i) % len(day_order)\n", "        next_day = day_order[next_day_index]\n", "        if next_day in days:\n", "            return next_day\n", "    return current_day"]}, {"cell_type": "code", "execution_count": null, "id": "03564677-51ac-454f-bcc3-848141247424", "metadata": {}, "outputs": [], "source": ["def calculate_sum_product(group):\n", "    load_type = group[\"load_type\"].iloc[\n", "        0\n", "    ]  # Using iloc[0] since load_type is consistent within each group\n", "    if load_type == 3:\n", "        return (group[\"case_per_quantity\"] * group[\"Avg_procured_qty\"]).sum()\n", "    elif load_type == 2:\n", "        return (group[\"weight_in_gm\"] * group[\"Avg_procured_qty\"]).sum()\n", "    elif load_type == 1:\n", "        return (group[\"landing_price\"] * group[\"Avg_procured_qty\"]).sum()"]}, {"cell_type": "code", "execution_count": null, "id": "fb81194a-d0c4-4789-b428-387f4c169384", "metadata": {}, "outputs": [], "source": ["def calculate_day_difference(row):\n", "    day_order = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"]\n", "    day_index = day_order.index(row[\"day\"])\n", "    next_po_day_index = day_order.index(row[\"next_po_day\"])\n", "    difference = (next_po_day_index - day_index) % 7\n", "    if difference == 0:\n", "        return 7  # Return 7 to indicate a full week difference if same day\n", "    # Calculate the circular difference\n", "    return difference"]}, {"cell_type": "code", "execution_count": null, "id": "f235b82d-471c-49c6-ba8f-f675e4ba1b80", "metadata": {}, "outputs": [], "source": ["# def calculate_lead_time(row):\n", "#     if pd.notna(row['grn_ts']) and pd.notna(row['issue_date']):\n", "#         return (row['grn_ts'].date() - row['issue_date'].date()).days\n", "#     else:\n", "#         return 0"]}, {"cell_type": "code", "execution_count": null, "id": "31ba9971-1509-4533-a77b-7738f29750b9", "metadata": {}, "outputs": [], "source": ["def expand_dates_cpd(row):\n", "    date_range = pd.date_range(start=row[\"date_\"], periods=row[\"mean_tat\"] + 1)\n", "    repeat_rows = pd.DataFrame(\n", "        {\n", "            \"date_\": date_range,\n", "            \"item_id\": row[\"item_id\"],\n", "            \"Avg_procured_qty\": row[\"Avg_procured_qty\"],\n", "            \"std_deviation_procured_quantity\": row[\"std_deviation_procured_quantity\"],\n", "            \"mean_tat\": row[\"mean_tat\"],\n", "        }\n", "    )\n", "    return repeat_rows"]}, {"cell_type": "code", "execution_count": null, "id": "c73aa1f3-03c4-4d2f-8857-fed0a1f42263", "metadata": {}, "outputs": [], "source": ["def expand_dates_po(row):\n", "    date_range = pd.date_range(start=row[\"date_\"], periods=row[\"mean_tat\"] + 1)\n", "    repeat_rows = pd.DataFrame(\n", "        {\n", "            \"date_\": date_range,\n", "            \"item_id\": row[\"item_id\"],\n", "            \"holding_quantity\": row[\"holding_quantity\"],\n", "            \"holding_quantity_consumable\": row[\"holding_quantity_consumable\"],\n", "            \"open_sto_quantity\": row[\"open_sto_quantity\"],\n", "            \"current_inventory\": row[\"current_inventory\"],\n", "            \"mean_tat\": row[\"mean_tat\"],\n", "            \"tat_days\": row[\"tat_days\"],\n", "        }\n", "    )\n", "    return repeat_rows"]}, {"cell_type": "code", "execution_count": null, "id": "b628a7f4-7ddf-4cb2-a732-6e0d7e78af36", "metadata": {}, "outputs": [], "source": ["def process_group(group):\n", "    group[\"consumption\"] = 0.0\n", "    # Initialize the consumption and update the total_inventory for each day\n", "    for i in range(len(group)):\n", "        index = group.index[i]  # Get the actual index of the row in the group\n", "        if i == 0:\n", "            group.at[index, \"consumption\"] = min(\n", "                group.at[index, \"total_inventory\"], group.at[index, \"new_forecast\"]\n", "            )\n", "        else:\n", "            previous_index = group.index[i - 1]\n", "            new_inventory = (\n", "                group.at[previous_index, \"total_inventory\"]\n", "                - group.at[previous_index, \"consumption\"]\n", "                + group.at[index, \"landing_po_quantity\"]\n", "            )\n", "            group.at[index, \"total_inventory\"] = max(new_inventory, 0)\n", "            group.at[index, \"consumption\"] = min(\n", "                group.at[index, \"total_inventory\"], group.at[index, \"new_forecast\"]\n", "            )\n", "\n", "    return group"]}, {"cell_type": "code", "execution_count": null, "id": "9e498768-f2e8-4f56-a54c-d42c9028e150", "metadata": {}, "outputs": [], "source": ["# def find_max_gap(days_string):\n", "#     weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']\n", "#     # Clean and split the string days to their index numbers\n", "#     days = days_string.replace(\"'\", \"\").split(',')\n", "#     day_numbers = [weekdays.index(day.strip()) for day in days]\n", "#     if len(day_numbers) < 2:\n", "#         return 7  # No gap possible if there's only one or no days\n", "#     max_gap = max((day_numbers[i + 1] - day_numbers[i]) for i in range(len(day_numbers) - 1))\n", "#     # Add the wrap-around gap (from the last day back to the first)\n", "#     wrap_around_gap = (7 - day_numbers[-1] + day_numbers[0]) % 7\n", "#     # Consider the maximum gap\n", "#     max_gap = max(max_gap, wrap_around_gap)\n", "#     return max_gap"]}, {"cell_type": "code", "execution_count": null, "id": "7dfb6644-bce5-489d-ac25-7c2796b85974", "metadata": {}, "outputs": [], "source": ["def find_max_gap(days_string):\n", "    weekdays = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"]\n", "    days = days_string.replace(\"'\", \"\").split(\",\")\n", "    day_numbers = sorted([weekdays.index(day.strip()) for day in days])\n", "    if len(day_numbers) < 2:\n", "        return 7  # If only one day or none, gap is 7 by default\n", "    # To account for wrap-around from Sunday to Monday, append the first day plus 7 to the list\n", "    day_numbers.append(day_numbers[0] + 7)\n", "    # Compute gaps between consecutive days\n", "    max_gap = max(day_numbers[i + 1] - day_numbers[i] - 1 for i in range(len(day_numbers) - 1))\n", "    return max_gap"]}, {"cell_type": "code", "execution_count": null, "id": "e9ee90c6-4ced-4a02-9271-f22108550e3e", "metadata": {}, "outputs": [], "source": ["# def rationalize_po_days(indent_data):\n", "#     dfb = indent_data\n", "#     dfb.reset_index(drop=True, inplace=True)\n", "#     dfb['first_element_list'] = dfb['po_days'].apply(lambda x: [day.strip().capitalize() for day in x.split(',')])\n", "#     dfb['po_days'] = dfb['first_element_list'].apply(lambda x: \", \".join([f\"'{day}'\" for day in x]))\n", "#     po_days = dfb['po_days'][0].replace(\"'\", \"\").split(', ')\n", "#     po_days = [day.strip() for day in po_days]\n", "#     dfb['po_days'] = dfb['po_days'].str.replace(\"'\", \"\")\n", "#     return dfb"]}, {"cell_type": "code", "execution_count": null, "id": "637d3a83-1839-4425-aba5-fb4a7d160758", "metadata": {}, "outputs": [], "source": ["def rationalize_po_days(indent_data):\n", "    dfb = indent_data  # or indent_data.copy(), either is fine if you do .loc below\n", "    dfb.reset_index(drop=True, inplace=True)\n", "    dfb.loc[:, \"first_element_list\"] = dfb[\"po_days\"].apply(\n", "        lambda x: [day.strip().capitalize() for day in x.split(\",\")]\n", "    )\n", "    dfb.loc[:, \"po_days\"] = dfb[\"first_element_list\"].apply(\n", "        lambda x: \", \".join([f\"'{day}'\" for day in x])\n", "    )\n", "    dfb.loc[:, \"po_days\"] = dfb[\"po_days\"].str.replace(\"'\", \"\")\n", "    return dfb"]}, {"cell_type": "code", "execution_count": null, "id": "3cb4aa88-755f-48c1-9e83-1de229995bf9", "metadata": {}, "outputs": [], "source": ["def correct_po_days(indent_data):\n", "    indent_data[\"po_days\"] = indent_data[\"po_days\"].str.replace(\n", "        \"sunday\", \"\", case=False, regex=True\n", "    )\n", "    indent_data[\"po_days\"] = indent_data[\"po_days\"].str.replace(\"\\s*,\\s*\", \",\", regex=True)\n", "    indent_data[\"po_days\"] = indent_data[\"po_days\"].str.replace(\n", "        \",{2,}\", \",\", regex=True\n", "    )  # Replace multiple commas with a single comma\n", "    indent_data[\"po_days\"] = indent_data[\"po_days\"].str.strip(\n", "        \",\"\n", "    )  # Remove any leading or trailing commas\n", "    indent_data[\"po_days\"] = indent_data[\"po_days\"].str.replace(\",\", \", \", regex=True)\n", "    indent_data[\"po_days\"] = indent_data[\n", "        \"po_days\"\n", "    ].str.strip()  # Remove leading/trailing whitespace\n", "    indent_data = indent_data[indent_data[\"po_days\"] != \"\"]\n", "    return indent_data"]}, {"cell_type": "code", "execution_count": null, "id": "ce512bcc-6273-4142-87be-8f22b285905e", "metadata": {}, "outputs": [], "source": ["def arrange_po_days(indent_data):\n", "    # Define the order of days for sorting\n", "    days_order = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"]\n", "\n", "    # Clean, capitalize, and sort 'po_days'\n", "    indent_data[\"po_days\"] = indent_data[\"po_days\"].apply(\n", "        lambda x: \", \".join(\n", "            sorted(\n", "                [day.strip().capitalize() for day in x.split(\",\")],\n", "                key=lambda day: days_order.index(day),\n", "            )\n", "        )\n", "    )\n", "    # Convert 'date_' to datetime and extract day names\n", "    indent_data[\"date_\"] = pd.to_datetime(indent_data[\"date_\"])\n", "    indent_data[\"day\"] = indent_data[\"date_\"].dt.strftime(\"%A\").str.title()\n", "    # Reorder columns to place 'date_' and 'day' first\n", "    indent_data = indent_data[\n", "        [\"date_\", \"day\"] + [col for col in indent_data.columns if col not in [\"date_\", \"day\"]]\n", "    ]\n", "    return indent_data"]}, {"cell_type": "code", "execution_count": null, "id": "f5b33672-7fd6-49f2-9683-f13aee7fcf29", "metadata": {}, "outputs": [], "source": ["def outlet_item_universe(outlet_data, dfb):\n", "    dfb_ = outlet_data\n", "    distinct_item_ids = dfb[\"item_id\"].unique()\n", "    item_list = \",\".join(map(str, distinct_item_ids))\n", "    string_values = item_list.split(\",\")\n", "    # Convert the strings to integers using list comprehension\n", "    int_values = [int(value) for value in string_values]\n", "    # Convert the list to a tuple\n", "    item_list = tuple(int_values)\n", "    distinct_outlet_id = dfb_[\"outlet_id\"].unique()\n", "    outlet_list = \",\".join(map(str, distinct_outlet_id))\n", "    string_values = outlet_list.split(\",\")\n", "    # Convert the strings to integers using list comprehension\n", "    int_values = [int(value) for value in string_values]\n", "    # Convert the list to a tuple\n", "    outlet_list = tuple(int_values)\n", "    outlet_id = outlet_list[0]\n", "    return outlet_list, item_list, outlet_id"]}, {"cell_type": "code", "execution_count": null, "id": "0a43c21c-183e-4e92-8862-599228ae2eee", "metadata": {}, "outputs": [], "source": ["def build_base(sale_data_1, start_date, end_date, outlet_list, item_list):\n", "    dfa1_ = sale_data_1\n", "    end_date = pd.to_datetime(end_date)\n", "    new_end_date = end_date + pd.offsets.Day(0)\n", "    dates = pd.date_range(start_date, new_end_date)\n", "    # pd.date_range('2023-06-12', '2024-01-08')\n", "    # Create a list of outlet_ids\n", "    outlet_ids = outlet_list\n", "    # Create a list of item_ids\n", "    item_ids = item_list\n", "    # Create a list of combinations of outlet_ids and item_ids\n", "    combinations = list(itertools.product(dates, outlet_ids, item_ids))\n", "    # Create the DataFrame\n", "    df = pd.DataFrame.from_records(combinations, columns=[\"date_\", \"outlet_id\", \"item_id\"])\n", "    df[\"procured_quantity\"] = 0\n", "    dfaaa1 = df\n", "    dfaaa1[\"date_\"] = pd.to_datetime(dfaaa1[\"date_\"])\n", "    dfa1_[\"date_\"] = pd.to_datetime(dfa1_[\"date_\"])\n", "    merged_df = pd.merge(\n", "        dfaaa1,\n", "        dfa1_[[\"date_\", \"outlet_id\", \"item_id\", \"procured_quantity_1\"]],\n", "        on=[\"date_\", \"outlet_id\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "    merged_df.drop(columns=[\"procured_quantity\"], inplace=True)\n", "    merged_df.rename(columns={\"procured_quantity_1\": \"procured_quantity\"}, inplace=True)\n", "    merged_df[\"procured_quantity\"].fillna(0, inplace=True)\n", "    dfaaa1 = merged_df\n", "    return merged_df"]}, {"cell_type": "code", "execution_count": null, "id": "a2729209-157f-4da9-957e-0e2dbe6317a8", "metadata": {}, "outputs": [], "source": ["def process_rolling_window_statistics(dataframe):\n", "    # dataframe.to_csv('dataframe_1.csv')\n", "    # Aggregate by date and item ID to sum the procured quantities\n", "    aggregated_data = (\n", "        dataframe.groupby([\"date_\", \"item_id\"]).agg({\"procured_quantity\": \"sum\"}).reset_index()\n", "    )\n", "    # Convert 'date_' column to datetime, just in case it's not in datetime format\n", "    aggregated_data[\"date_\"] = pd.to_datetime(aggregated_data[\"date_\"], errors=\"coerce\")\n", "    # Drop any rows with NA dates if any\n", "    aggregated_data = aggregated_data.dropna(subset=[\"date_\"])\n", "    # Sort the DataFrame by 'date_' column\n", "    aggregated_data = aggregated_data.sort_values(by=\"date_\")\n", "    # Define the rolling window size (30 days)\n", "    window_size = pd.DateOffset(days=30)\n", "    # Create a new DataFrame to store the results\n", "    result_df = pd.DataFrame()\n", "    # Iterate through each unique date in the DataFrame\n", "    for date in aggregated_data[\"date_\"].unique():\n", "        start_date_ = pd.to_datetime(date) - window_size\n", "        end_date_ = pd.to_datetime(date)\n", "        # Select data for the rolling window of 30 days\n", "        window_data = aggregated_data[\n", "            (aggregated_data[\"date_\"] >= start_date_) & (aggregated_data[\"date_\"] < end_date_)\n", "        ]\n", "        # Calculate average 'procured_quantity' and standard deviation 'procured_quantity' for each 'item_id'\n", "        summary_data = (\n", "            window_data.groupby(\"item_id\")[\"procured_quantity\"].agg([\"mean\", \"std\"]).reset_index()\n", "        )\n", "        # Add the 'date_' column for which the calculations are performed\n", "        summary_data[\"date_\"] = date\n", "        # Append the results to the result DataFrame\n", "        result_df = pd.concat([result_df, summary_data], ignore_index=True)\n", "    # Sort the result DataFrame by 'date_' and 'item_id'\n", "    result_df = result_df.sort_values(by=[\"date_\", \"item_id\"])\n", "    # Reset the index of the result DataFrame\n", "    result_df.reset_index(drop=True, inplace=True)\n", "    # result_df.to_csv('dataframe_2.csv.csv')\n", "    return result_df"]}, {"cell_type": "code", "execution_count": null, "id": "5c800721-3b15-480e-8837-fcbb4f4bd475", "metadata": {}, "outputs": [], "source": ["def calculate_fill_rate(po_data, start_date, end_date):\n", "    def calculate_lead_time(row):\n", "        if pd.notna(row[\"grn_ts\"]) and pd.notna(row[\"issue_date\"]):\n", "            return (row[\"grn_ts\"].date() - row[\"issue_date\"].date()).days\n", "        else:\n", "            return 0\n", "\n", "    dfc = po_data\n", "    print(\"inside Shape of dfc:\", dfc.shape)\n", "\n", "    start_date_str = start_date  # Start date with timestamp at midnight\n", "    end_date_str = end_date\n", "    start_date_ = pd.to_datetime(start_date_str)\n", "    end_date_ = pd.to_datetime(end_date_str)\n", "    dfc[\"expiry_date\"] = pd.to_datetime(dfc[\"expiry_date\"])\n", "    dfc1 = dfc[(dfc[\"expiry_date\"] >= start_date_) & (dfc[\"expiry_date\"] < end_date_)]\n", "    print(\"Shape of dfc1:\", dfc1.shape)\n", "    dfc1[\"scheduled_date\"] = pd.to_datetime(dfc1[\"scheduled_date\"])\n", "    dfc[\"expiry_date\"] = dfc[\"expiry_date\"].astype(\"object\")\n", "    dfc1[\"issue_date\"] = pd.to_datetime(dfc1[\"issue_date\"])\n", "    dfc1[\"grn_ts\"] = pd.to_datetime(dfc1[\"grn_ts\"])\n", "    print(\"Shape of dfc1 again:\", dfc1.shape)\n", "\n", "    if not dfc1.empty:\n", "        dfc1[\"lead_time\"] = dfc1.apply(calculate_lead_time, axis=1)\n", "    else:\n", "        print(\"dfc1 is empty. Skipping lead_time calculation.\")\n", "        dfc1[\"lead_time\"] = 0  # Optional: Add an empty column if needed\n", "\n", "    # dfc1['lead_time'] = dfc1.apply(calculate_lead_time, axis=1)\n", "\n", "    print(\"Shape of dfc1 with lead time:\", dfc1.shape)\n", "    dflt = pd.DataFrame()\n", "    dflt = dfc1\n", "    dflt = dflt[dflt[\"grn_ts\"].notna()]\n", "    print(\"Shape of dflt:\", dflt.shape)\n", "\n", "    B1_sorted = dfc1.sort_values(by=\"expiry_date\", ascending=False)\n", "    print(\"Shape of B1_sorted:\", B1_sorted.shape)\n", "    B2 = B1_sorted.groupby([\"facility_id\", \"item_id\"]).head(5)\n", "    fr = (\n", "        B2.groupby([\"po_id\", \"item_id\"])\n", "        .agg({\"expected_po_qty\": \"first\", \"grn_qty\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "    print(\"Shape of fr:\", fr.shape)\n", "    fr1 = fr.groupby([\"item_id\"]).agg({\"expected_po_qty\": \"sum\", \"grn_qty\": \"sum\"}).reset_index()\n", "    print(\"Shape of fr1:\", fr1.shape)\n", "    fr1[\"fill_rate_percentage\"] = (fr1[\"grn_qty\"] / fr1[\"expected_po_qty\"]) * 100\n", "    fr1[\"fill_rate_percentage\"] = fr1[\"fill_rate_percentage\"].fillna(70)\n", "    fr1[\"fill_rate_percentage\"] = fr1[\"fill_rate_percentage\"].replace(0, 50)\n", "    dfc1[\"issue_date\"] = pd.to_datetime(dfc1[\"issue_date\"])\n", "    dfc1 = dfc1.sort_values(by=\"issue_date\")\n", "    # dflt.to_csv('dflt.csv')\n", "    dfc1.to_csv(\"dfc1.csv\")\n", "    return fr1, dfc1, dflt"]}, {"cell_type": "code", "execution_count": null, "id": "9c93be83-e22c-4e96-90bb-1b2e75729b40", "metadata": {}, "outputs": [], "source": ["def populate_lead_time(dfc1, dfaa2):\n", "    if dfc1.empty:\n", "        print(\n", "            \"dfc1 is empty. Assigning default values for average_lead_time and std_deviation_lead_time.\"\n", "        )\n", "        # Create a result DataFrame with default values\n", "        merged_df = dfaa2.copy()\n", "        merged_df[\"average_lead_time\"] = 0\n", "        merged_df[\"std_deviation_lead_time\"] = 0\n", "        return merged_df\n", "    # Set the rolling window size\n", "    window_size = pd.DateOffset(days=60)\n", "    # Find the latest issue_date\n", "    latest_date = dfc1[\"issue_date\"].max()\n", "    # Check if latest_date is valid\n", "    if pd.isna(latest_date):\n", "        print(\"latest_date is NaT. No valid issue dates in dfc1.\")\n", "        # Create a result DataFrame with default values\n", "        merged_df = dfaa2.copy()\n", "        merged_df[\"average_lead_time\"] = 0\n", "        merged_df[\"std_deviation_lead_time\"] = 0\n", "        return merged_df\n", "    print(\"I am here_3\")\n", "    # Set the rolling window size\n", "    window_size = pd.DateOffset(days=60)\n", "    # Find the latest issue_date\n", "    latest_date = dfc1[\"issue_date\"].max()\n", "    # Calculate the start and end date for the window\n", "    start_date_ = latest_date - window_size\n", "    end_date_ = latest_date\n", "    dflt = dfc1\n", "    dflt = dflt[dflt[\"grn_ts\"].notna()]\n", "    # Group by item_id and calculate the mean lead time\n", "    grouped = dflt.groupby(\"item_id\")\n", "    mean_lead_time = grouped[\"lead_time\"].mean().reset_index(name=\"mean\")\n", "    # Join the mean back to the original DataFrame to filter\n", "    dflt_with_mean = dfc1.merge(mean_lead_time, on=\"item_id\")\n", "    # Replace lead_time values less than mean with the mean itself\n", "    dflt_with_mean[\"adjusted_lead_time\"] = dflt_with_mean.apply(\n", "        lambda x: x[\"mean\"] if x[\"lead_time\"] < x[\"mean\"] else x[\"lead_time\"], axis=1\n", "    )\n", "    # Calculate standard deviation on the adjusted lead time\n", "    std_dev_data = (\n", "        dflt_with_mean.groupby(\"item_id\")[\"adjusted_lead_time\"].std().reset_index(name=\"std\")\n", "    )\n", "    # Merge the mean and standard deviation data\n", "    summary_data = pd.merge(mean_lead_time, std_dev_data, on=\"item_id\", how=\"left\")\n", "    summary_data[\"date_\"] = latest_date\n", "    print(\"I am here_4\")\n", "    # Prepare the final DataFrame with proper column names\n", "    result_df = summary_data.rename(\n", "        columns={\"mean\": \"average_lead_time\", \"std\": \"std_deviation_lead_time\"}\n", "    )\n", "    result_df = result_df[[\"date_\", \"item_id\", \"average_lead_time\", \"std_deviation_lead_time\"]]\n", "    result_df[\"date_\"] = pd.to_datetime(result_df[\"date_\"])\n", "    result_df = result_df.sort_values(by=[\"date_\", \"item_id\"]).reset_index(drop=True)\n", "    # Initialize an empty list to store the merged DataFrames\n", "    merged_dfs = []\n", "    # Iterate through each row in 'dfaa2'\n", "    print(\"I am here_5\")\n", "    # dfaa2.to_csv('dfaa2.csv')\n", "    # dfc1.to_csv('dfc1.csv')\n", "    # dfaa2.to_csv('dfaa2.csv')\n", "    for _, row in dfaa2.iterrows():\n", "        item_id = row[\"item_id\"]\n", "        date_ = row[\"date_\"]\n", "        # Filter 'result_df' for the same 'item_id' and dates less than or equal to 'date_'\n", "        filtered_dfcc2 = result_df[\n", "            (result_df[\"item_id\"] == item_id) & (result_df[\"date_\"] <= date_)\n", "        ]\n", "        # Sort the filtered DataFrame by 'date_' in descending order\n", "        sorted_dfcc2 = filtered_dfcc2.sort_values(by=\"date_\", ascending=False)\n", "        if not sorted_dfcc2.empty:\n", "            # Take the first row (closest preceding date) from the sorted DataFrame\n", "            closest_preceding_row = sorted_dfcc2.iloc[0]\n", "            # Create a DataFrame with all columns from 'dfaa2' and selected columns from 'result_df'\n", "            merged_row_df = pd.DataFrame([row])\n", "            merged_row_df[\"average_lead_time\"] = (\n", "                closest_preceding_row[\"average_lead_time\"]\n", "                if pd.notna(closest_preceding_row[\"average_lead_time\"])\n", "                else 0\n", "            )\n", "            merged_row_df[\"std_deviation_lead_time\"] = (\n", "                closest_preceding_row[\"std_deviation_lead_time\"]\n", "                if pd.notna(closest_preceding_row[\"std_deviation_lead_time\"])\n", "                else 0\n", "            )\n", "            merged_dfs.append(merged_row_df)\n", "        else:\n", "            # Handle the case where filtered_dfcc2 is empty by assigning default values\n", "            merged_row_df = pd.DataFrame([row])\n", "            merged_row_df[\"average_lead_time\"] = 0  # Default value\n", "            merged_row_df[\"std_deviation_lead_time\"] = 0  # Default value\n", "            merged_dfs.append(merged_row_df)\n", "    print(\"I am here_6\")\n", "    # Concatenate all the merged row DataFrames into a final merged DataFrame\n", "    merged_df = pd.concat(merged_dfs, ignore_index=True)\n", "    # merged_df.to_csv('merged_df1.csv')\n", "    return merged_df"]}, {"cell_type": "code", "execution_count": null, "id": "1d15fd95-8004-48a9-8472-38fbac974007", "metadata": {}, "outputs": [], "source": ["def base_fluctuations(dfc1, dfaa2, forecast_cpd_1, grouped_stats, end_date):\n", "    dfaa2 = dfaa2.rename(\n", "        columns={\"mean\": \"Avg_procured_qty\", \"std\": \"std_deviation_procured_quantity\"}\n", "    )\n", "    dfaa2 = dfaa2.drop([\"Avg_procured_qty\"], axis=1)\n", "    dfaa2 = dfaa2.merge(forecast_cpd_1[[\"item_id\", \"Avg_procured_qty\"]], on=\"item_id\", how=\"left\")\n", "    # intentional line added.\n", "    dfaa2 = dfaa2[dfaa2[\"date_\"] == end_date]\n", "    # Replace zero values in std_deviation_procured_quantity with NaN\n", "    dfaa2[\"std_deviation_procured_quantity\"] = dfaa2[\"std_deviation_procured_quantity\"].replace(\n", "        0, np.nan\n", "    )\n", "    # Use forward fill (ffill) to fill NaNs with the latest non-zero value for each item_id\n", "    dfaa2[\"std_deviation_procured_quantity\"] = dfaa2.groupby(\"item_id\")[\n", "        \"std_deviation_procured_quantity\"\n", "    ].ffill()\n", "    # If there are any remaining NaNs after forward filling, you can replace them with zero\n", "    dfaa2[\"std_deviation_procured_quantity\"] = dfaa2[\"std_deviation_procured_quantity\"].fillna(0)\n", "\n", "    # dfc1.to_csv('dfc1.csv')\n", "    # dfaa2.to_csv('dfaa2.csv')\n", "    # fr1,dfc1,dflt=calculate_fill_rate(po_data, start_date, end_date)\n", "    dfcc3 = populate_lead_time(dfc1, dfaa2)\n", "    # dfcc3.to_csv('dfcc3.csv')\n", "    column_order = [\n", "        \"date_\",\n", "        \"item_id\",\n", "        \"Avg_procured_qty\",\n", "        \"std_deviation_procured_quantity\",\n", "        \"average_lead_time\",\n", "        \"std_deviation_lead_time\",\n", "    ]\n", "    dfcc3 = dfcc3[column_order]\n", "    dfcc3.drop(\"std_deviation_procured_quantity\", axis=1, inplace=True)\n", "    dfcc3 = pd.merge(dfcc3, grouped_stats[[\"item_id\", \"std\"]], on=\"item_id\", how=\"left\")\n", "    dfcc3.rename(columns={\"std\": \"std_deviation_procured_quantity\"}, inplace=True)\n", "    dfcc3[\"std_deviation_procured_quantity\"] = dfcc3[\"std_deviation_procured_quantity\"].fillna(0)\n", "    return dfcc3"]}, {"cell_type": "code", "execution_count": null, "id": "7795c17a-4d4c-4d71-86f4-9c3dea6e3ee9", "metadata": {}, "outputs": [], "source": ["def consumption_simulator(dfcc6, open_po, end_date, forecast_cpd_new):\n", "    dfcc6_ = dfcc6[dfcc6[\"date_\"] == end_date]\n", "    dfcc6_.loc[dfcc6_[\"mean_tat\"] > dfcc6_[\"tat_days\"], \"mean_tat\"] = dfcc6_.loc[\n", "        dfcc6_[\"mean_tat\"] > dfcc6_[\"tat_days\"], \"tat_days\"\n", "    ]\n", "    dfc1_ = open_po\n", "    open_po_agg = pd.DataFrame()\n", "    open_po_agg = open_po.groupby(\"item_id\")[\"landing_po_quantity\"].sum().reset_index()\n", "    dfc1_[\"issue_date\"] = pd.to_datetime(dfc1_[\"issue_date\"])\n", "    dfc1_[\"po_schedule_date\"] = pd.to_datetime(dfc1_[\"po_schedule_date\"])\n", "    dfc1_[\"po_expiry_date\"] = pd.to_datetime(dfc1_[\"po_expiry_date\"])\n", "    dfc1_ = dfc1_.merge(dfcc6_[[\"item_id\", \"mean_tat\"]], on=\"item_id\", how=\"left\")\n", "    dfc1_[\"po_schedule_date\"] = pd.to_datetime(dfc1_[\"po_schedule_date\"], errors=\"coerce\")\n", "    # If 'po_schedule_date' exists, use it; otherwise, calculate 'date_'\n", "    dfc1_[\"date_\"] = np.where(\n", "        dfc1_[\"po_schedule_date\"].notna(),\n", "        dfc1_[\"po_schedule_date\"],\n", "        dfc1_[\"issue_date\"] + pd.to_<PERSON><PERSON>ta(dfc1_[\"mean_tat\"], unit=\"D\"),\n", "    )\n", "    dfc1_[\"date_\"] = pd.to_datetime(dfc1_[\"date_\"])\n", "    cpd_details = dfcc6_[\n", "        [\"date_\", \"item_id\", \"mean_tat\", \"Avg_procured_qty\", \"std_deviation_procured_quantity\"]\n", "    ]\n", "    cpd_details_1 = pd.concat(\n", "        [expand_dates_cpd(row) for index, row in cpd_details.iterrows()], ignore_index=True\n", "    )\n", "    cpd_details_1[\"ordering_cpd\"] = cpd_details_1[\"Avg_procured_qty\"]\n", "    con_1 = pd.concat([expand_dates_po(row) for index, row in dfcc6_.iterrows()], ignore_index=True)\n", "    con_2 = pd.merge(\n", "        con_1,\n", "        dfc1_[[\"item_id\", \"date_\", \"landing_po_quantity\"]],\n", "        on=[\"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    con_2[\"landing_po_quantity\"] = con_2[\"landing_po_quantity\"].fillna(0)\n", "    con_2[\"current_inventory\"] = con_2[\"current_inventory\"].fillna(0)\n", "    con_2[\"holding_quantity\"] = con_2[\"holding_quantity\"].fillna(0)\n", "    con_2[\"holding_quantity\"] = con_2[\"holding_quantity\"].fillna(0)\n", "    con_2[\"holding_quantity_consumable\"] = con_2[\"holding_quantity_consumable\"].fillna(0)\n", "    con_2[\"open_sto_quantity\"] = con_2[\"open_sto_quantity\"].fillna(0)\n", "    con_2[\"system_inventory\"] = (\n", "        con_2[\"holding_quantity_consumable\"]\n", "        + con_2[\"current_inventory\"]\n", "        + con_2[\"open_sto_quantity\"]\n", "    )\n", "    con_2 = pd.merge(\n", "        con_2,\n", "        cpd_details_1[[\"item_id\", \"date_\", \"ordering_cpd\"]],\n", "        on=[\"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "    con_2 = (\n", "        con_2.groupby(\n", "            [\n", "                \"date_\",\n", "                \"item_id\",\n", "                \"holding_quantity\",\n", "                \"open_sto_quantity\",\n", "                \"current_inventory\",\n", "                \"mean_tat\",\n", "                \"tat_days\",\n", "                \"system_inventory\",\n", "                \"ordering_cpd\",\n", "            ]\n", "        )\n", "        .agg({\"landing_po_quantity\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "\n", "    # con_2.to_csv('con_2.csv')\n", "\n", "    con_2[\"date_\"] = pd.to_datetime(con_2[\"date_\"], errors=\"coerce\")\n", "    forecast_cpd_new[\"date_\"] = pd.to_datetime(forecast_cpd_new[\"date_\"], errors=\"coerce\")\n", "    # print(\"con_2['date_']: \", con_2['date_'].dtype)\n", "    # print(\"forecast_cpd_new['date_']: \", forecast_cpd_new['date_'].dtype)\n", "\n", "    con_2 = pd.merge(\n", "        con_2,\n", "        forecast_cpd_new[[\"item_id\", \"date_\", \"new_forecast\"]],\n", "        on=[\"item_id\", \"date_\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    # 3. <PERSON><PERSON> in 'new_forecast' with the average at the item_id level.\n", "    #    transform() calculates the mean within each group, and fillna() uses that mean.\n", "    con_2[\"new_forecast\"] = con_2.groupby(\"item_id\")[\"new_forecast\"].transform(\n", "        lambda x: x.fillna(x.mean())\n", "    )\n", "    con_2[\"new_forecast\"] = con_2[\"new_forecast\"].fillna(1).replace(0, 1)\n", "\n", "    # Optionally, output con_2 to CSV for debugging purposes\n", "    # con_2.to_csv('con_2.csv', index=False)\n", "\n", "    con_2[\"total_inventory\"] = con_2[\"system_inventory\"] + con_2[\"landing_po_quantity\"]\n", "    con_2.sort_values(by=[\"item_id\", \"date_\"], inplace=True)\n", "\n", "    con_2 = con_2.groupby(\"item_id\").apply(process_group).reset_index(drop=True)\n", "\n", "    con_3 = (\n", "        con_2.groupby(\"item_id\")\n", "        .agg({\"consumption\": \"sum\", \"landing_po_quantity\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "\n", "    # con_3.to_csv('con_3.csv')\n", "\n", "    con_3 = con_3.drop(columns=[\"landing_po_quantity\"])\n", "    con_3 = pd.merge(\n", "        con_3, open_po_agg[[\"item_id\", \"landing_po_quantity\"]], on=\"item_id\", how=\"left\"\n", "    )\n", "    con_3[\"landing_po_quantity\"].fillna(0, inplace=True)\n", "    con_3.rename(columns={\"landing_po_quantity\": \"open_po_quantity\"}, inplace=True)\n", "\n", "    # dfc2_ = dfc1_[['date_', 'po_expiry_date','item_id', 'landing_po_quantity']].copy()\n", "    dfcc6 = pd.merge(dfcc6, con_3[[\"item_id\", \"open_po_quantity\"]], on=\"item_id\", how=\"left\")\n", "    con_3\n", "    dfcc6 = pd.merge(dfcc6, con_3[[\"item_id\", \"consumption\"]], on=\"item_id\", how=\"left\")\n", "    return dfcc6"]}, {"cell_type": "code", "execution_count": null, "id": "364ee221-717f-4161-8194-06ea4e00d920", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "683960f1-3c75-4713-bd07-337da6477d3b", "metadata": {}, "outputs": [], "source": ["def calculate_safety_stock(\n", "    dfcc6, fr1, item_active_ds_count, forecast_cpd_1, forecast_cpd_new, facility_id, min_qmax\n", "):\n", "    dfcc7 = pd.merge(dfcc6, fr1[[\"item_id\", \"fill_rate_percentage\"]], on=\"item_id\", how=\"left\")\n", "    dfcc7[\"fill_rate_percentage\"] = dfcc7[\"fill_rate_percentage\"].fillna(70)\n", "    dfcc7[\"fill_rate_percentage\"] = dfcc7[\"fill_rate_percentage\"].replace(0, 50)\n", "    dfcc7[\"fill_rate_percentage\"] = dfcc7[\"fill_rate_percentage\"].apply(\n", "        lambda x: 50 if x < 50 else x\n", "    )\n", "    dfcc7[\"open_po_quantity\"] = dfcc7[\"open_po_quantity\"].fillna(0)\n", "    dfcc7[\"open_po_quantity_2\"] = (dfcc7[\"open_po_quantity\"] * dfcc7[\"fill_rate_percentage\"]) / 100\n", "    dfcc7[\"holding_quantity\"] = dfcc7[\"holding_quantity\"].fillna(0)\n", "    dfcc7[\"current_inventory\"] = dfcc7[\"current_inventory\"].fillna(0)\n", "    dfcc7[\"lead_time\"] = dfcc7.groupby(\"date_\")[\"average_lead_time\"].transform(\"mean\") + 1\n", "    dfcc7[\"consumption\"] = dfcc7[\"consumption\"].fillna(0)\n", "    # n=handle_cpd_hikes(facility_id, forecast_cpd_1)\n", "    # dfcc7['hike_factor']=n\n", "\n", "    # dfcc7['demand'] = (dfcc7['Avg_procured_qty']*1) * np.maximum(dfcc7['days_b/w_successive_po'], dfcc7['expected_po_frequency'])\n", "\n", "    # -------------------------------------------------\n", "    # 1) Ensure forecast_cpd_new has datetime columns\n", "    # -------------------------------------------------\n", "    forecast_cpd_new[\"run_date\"] = pd.to_datetime(forecast_cpd_new[\"run_date\"], errors=\"coerce\")\n", "    forecast_cpd_new[\"date_\"] = pd.to_datetime(forecast_cpd_new[\"date_\"], errors=\"coerce\")\n", "\n", "    # ---------------------------------------------------------\n", "    # 2) For each item_id, pick the latest run_date (if needed)\n", "    #    This yields a dict: { item_id -> latest run_date }\n", "    # ---------------------------------------------------------\n", "    latest_run_date = forecast_cpd_new.groupby(\"item_id\")[\"run_date\"].max().to_dict()\n", "\n", "    # ---------------------------------------------------------\n", "    # 3) Function to get the average new_forecast for a row\n", "    #    in dfcc7 based on:\n", "    #      start_date = run_date + ceil(mean_tat)\n", "    #      end_date   = start_date + ceil(expected_po_frequency)\n", "    # ---------------------------------------------------------\n", "    def calc_new_forecast_average(row):\n", "        \"\"\"\n", "        row: a row from dfcc7 with columns:\n", "             - item_id\n", "             - mean_tat\n", "             - expected_po_frequency\n", "        We'll use the item_id to:\n", "          1) find its run_date from 'latest_run_date'\n", "          2) compute start_date, end_date\n", "          3) filter forecast_cpd_new for date_ in [start_date, end_date]\n", "          4) return the average new_forecast for that range\n", "        \"\"\"\n", "        item_id = row[\"item_id\"]\n", "\n", "        # If no run_date in forecast_cpd_new for this item, return NaN\n", "        if item_id not in latest_run_date:\n", "            return np.nan\n", "\n", "        run_dt = latest_run_date[item_id]\n", "\n", "        # Round up mean_tat and expected_po_frequency\n", "        tat_days = math.ceil(row[\"mean_tat\"] if pd.notna(row[\"mean_tat\"]) else 0)\n", "\n", "        # freq_days = math.ceil(row['expected_po_frequency'] if pd.notna(row['expected_po_frequency']) else 0)\n", "\n", "        freq_days = math.ceil(\n", "            max(\n", "                row[\"expected_po_frequency\"] if pd.notna(row[\"expected_po_frequency\"]) else 0,\n", "                row[\"days_b/w_successive_po\"] if pd.notna(row[\"days_b/w_successive_po\"]) else 0,\n", "            )\n", "        )\n", "\n", "        start_date = run_dt + pd.Timedelta(days=tat_days)\n", "        end_date = start_date + pd.Timedelta(days=freq_days)\n", "\n", "        # Filter forecast_cpd_new rows matching:\n", "        #   same item_id, date_ between start_date and end_date\n", "        mask = (\n", "            (forecast_cpd_new[\"item_id\"] == item_id)\n", "            & (forecast_cpd_new[\"date_\"] >= start_date)\n", "            & (forecast_cpd_new[\"date_\"] <= end_date)\n", "        )\n", "        subset = forecast_cpd_new.loc[mask, \"new_forecast\"]\n", "\n", "        # Return the mean if not empty\n", "        if subset.empty:\n", "            return np.nan\n", "        else:\n", "            return subset.mean()\n", "\n", "    # ---------------------------------------------------------\n", "    # 4) Build a new DataFrame with columns: [item_id, new_forecast_average]\n", "    #    NOTE: If dfcc7 has multiple rows per item, we do a row-wise apply.\n", "    #          If each item appears exactly once, you can deduplicate first.\n", "    # ---------------------------------------------------------\n", "    # We'll do it row-wise. If dfcc7 is large, consider optimization.\n", "    temp_df = dfcc7[\n", "        [\"item_id\", \"mean_tat\", \"expected_po_frequency\", \"days_b/w_successive_po\"]\n", "    ].copy()\n", "    # Ensure no duplicates if you only want 1 row per item:\n", "    #   temp_df = temp_df.drop_duplicates(subset=['item_id'], keep='last')\n", "\n", "    temp_df[\"new_forecast_average\"] = temp_df.apply(calc_new_forecast_average, axis=1)\n", "    temp_df[\"new_forecast_average\"] = temp_df[\"new_forecast_average\"].fillna(1).replace(0, 1)\n", "\n", "    # We only keep columns needed to join back\n", "    temp_df = temp_df[[\"item_id\", \"new_forecast_average\"]]\n", "\n", "    # ---------------------------------------------------------\n", "    # 5) Merge new_forecast_average into dfcc7\n", "    #    Keep all rows from dfcc7, bringing in new_forecast_average\n", "    # ---------------------------------------------------------\n", "    dfcc7 = dfcc7.merge(temp_df, on=\"item_id\", how=\"left\")\n", "\n", "    # ---------------------------------------------------------\n", "    # 6) Finally, compute 'demand' using new_forecast_average\n", "    # ---------------------------------------------------------\n", "    dfcc7[\"demand\"] = dfcc7[\"new_forecast_average\"] * np.maximum(\n", "        dfcc7[\"days_b/w_successive_po\"], dfcc7[\"expected_po_frequency\"]\n", "    )\n", "\n", "    dfcc7 = pd.merge(\n", "        dfcc7, item_active_ds_count[[\"item_id\", \"number_of_ds_connected\"]], on=\"item_id\", how=\"left\"\n", "    )\n", "    high_value_tag = get_high_value_tag()\n", "    high_value_items = set(high_value_tag[\"item_id\"].unique())\n", "    dfcc7[\"is_high_value\"] = dfcc7[\"item_id\"].isin(high_value_items).astype(int)\n", "\n", "    ###################\n", "\n", "    dfcc7 = dfcc7.merge(min_qmax[[\"item_id\", \"fe_safety_stock_new\"]], on=[\"item_id\"], how=\"left\")\n", "\n", "    # dfcc7['fe_safety_stock_new'] = np.where(\n", "    #     dfcc7['is_high_value'] == 1,\n", "    #     np.maximum(dfcc7['fe_safety_stock_new'], 1 * dfcc7['number_of_ds_connected']),\n", "    #     np.maximum(dfcc7['fe_safety_stock_new'], 2 * dfcc7['number_of_ds_connected']))\n", "\n", "    # 4) Conditionally assign fe_safety_stock:\n", "    dfcc7[\"fe_safety_stock_old\"] = dfcc7[\"Avg_procured_qty\"] * 3\n", "\n", "    # np.where(\n", "    #     dfcc7[\"is_high_value\"] == 1,\n", "    #     np.maximum(dfcc7[\"Avg_procured_qty\"] * 4, 1 * dfcc7[\"number_of_ds_connected\"]),\n", "    #     np.maximum(dfcc7[\"Avg_procured_qty\"] * 4, 2 * dfcc7[\"number_of_ds_connected\"]),\n", "    # )\n", "\n", "    ############\n", "\n", "    use_new_safety_stock = facility_id_df[facility_id_df[\"facility_id\"] == facility_id][\n", "        \"min_qmax\"\n", "    ].values[0]\n", "\n", "    dfcc7[\"fe_safety_stock_new\"] = dfcc7[\"fe_safety_stock_new\"].fillna(dfcc7[\"fe_safety_stock_old\"])\n", "\n", "    if use_new_safety_stock:\n", "        dfcc7[\"fe_safety_stock\"] = np.minimum(\n", "            dfcc7[\"fe_safety_stock_old\"], dfcc7[\"fe_safety_stock_new\"]\n", "        )\n", "    else:\n", "        dfcc7[\"fe_safety_stock\"] = dfcc7[\"fe_safety_stock_old\"]\n", "\n", "    dfcc7[\"fe_safety_stock\"] = dfcc7[\"fe_safety_stock\"] + dfcc7[\"Avg_procured_qty\"]\n", "\n", "    dfcc7[\"fe_safety_stock\"] = np.where(\n", "        dfcc7[\"is_high_value\"] == 1,\n", "        np.maximum(dfcc7[\"fe_safety_stock\"], 1 * dfcc7[\"number_of_ds_connected\"]),\n", "        np.maximum(dfcc7[\"fe_safety_stock\"], 1 * dfcc7[\"number_of_ds_connected\"]),\n", "    )\n", "\n", "    # dfcc7['fe_safety_stock'] = np.maximum(dfcc7['Avg_procured_qty'] * 4, 2*dfcc7['number_of_ds_connected'])\n", "\n", "    dfcc7[\"open_sto_quantity\"].fillna(0, inplace=True)\n", "    weekdays = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"]\n", "    # Apply the function to the 'po_days' column\n", "    dfcc7[\"max_po_day_gap\"] = dfcc7[\"po_days\"].apply(find_max_gap)\n", "    # dfcc7 = dfcc7.drop(columns=['safety_stock'])\n", "    dfcc7.loc[dfcc7[\"std_deviation_lead_time\"] == 0, \"std_deviation_lead_time\"] = 1\n", "    # Update 'average_lead_time' to the value from 'tat_days' where 'average_lead_time' is 0\n", "    dfcc7.loc[dfcc7[\"average_lead_time\"] == 0, \"average_lead_time\"] = dfcc7[\"tat_days\"]\n", "    dfcc7.loc[\n", "        (dfcc7[\"std_deviation_lead_time\"] + dfcc7[\"mean_tat\"]) > (dfcc7[\"tat_days\"] + 1),\n", "        \"std_deviation_lead_time\",\n", "    ] = 1\n", "    Z = 1.96\n", "    dfcc7[\"safety_stock\"] = (\n", "        Z\n", "        * (\n", "            dfcc7[\"std_deviation_lead_time\"] ** 2 * dfcc7[\"Avg_procured_qty\"] ** 2\n", "            + dfcc7[\"average_lead_time\"] * dfcc7[\"std_deviation_procured_quantity\"] ** 2\n", "        )\n", "        ** 0.5\n", "    )\n", "\n", "    dfcc7[\"be_safety_stock\"] = dfcc7.apply(\n", "        lambda row: row[\"safety_stock\"]\n", "        + row[\"Avg_procured_qty\"]\n", "        * min(max(row[\"max_po_day_gap\"], row[\"expected_po_frequency\"]), 7),\n", "        axis=1,\n", "    )\n", "\n", "    # dfcc7['be_safety_stock'] = dfcc7.apply(\n", "    # lambda row: row['safety_stock'] + row['Avg_procured_qty'] * max(row['max_po_day_gap'], row['expected_po_frequency']),\n", "    # axis=1)\n", "\n", "    # dfcc7['selected_safety_stock'] = np.where(dfcc7['fill_rate_percentage'] < 75, dfcc7['be_safety_stock'], dfcc7['safety_stock'])\n", "\n", "    row = facility_id_df.loc[facility_id_df[\"facility_id\"] == facility_id]\n", "    if not row.empty:\n", "        fill_rate_factor = row[\"fill rate factor\"].iloc[0]\n", "    else:\n", "        # If not found, default to 1 or any fallback\n", "        fill_rate_factor = 1\n", "    # 2) Decide threshold based on fill_rate_factor\n", "    if fill_rate_factor == 0:\n", "        threshold = 50\n", "    else:\n", "        threshold = 75\n", "    # 3) Final logic for selected_safety_stock\n", "    dfcc7[\"selected_safety_stock\"] = np.where(\n", "        dfcc7[\"fill_rate_percentage\"] < threshold, dfcc7[\"be_safety_stock\"], dfcc7[\"safety_stock\"]\n", "    )\n", "    return dfcc7"]}, {"cell_type": "code", "execution_count": null, "id": "e907607f-b76b-40f3-bc2d-b38f311f655e", "metadata": {}, "outputs": [], "source": ["# def calculate_safety_stock(dfcc6,fr1,item_active_ds_count,forecast_cpd_1,facility_id):\n", "#     dfcc7 = pd.merge(dfcc6, fr1[['item_id', 'fill_rate_percentage']], on='item_id', how='left')\n", "#     dfcc7['fill_rate_percentage'] = dfcc7['fill_rate_percentage'].fillna(70)\n", "#     dfcc7['fill_rate_percentage'] = dfcc7['fill_rate_percentage'].replace(0, 50)\n", "#     dfcc7['fill_rate_percentage'] = dfcc7['fill_rate_percentage'].apply(lambda x: 50 if x < 50 else x)\n", "#     dfcc7['open_po_quantity'] = dfcc7['open_po_quantity'].fillna(0)\n", "#     dfcc7['open_po_quantity_2'] = (dfcc7['open_po_quantity'] * dfcc7['fill_rate_percentage'])/100\n", "#     dfcc7['holding_quantity'] = dfcc7['holding_quantity'].fillna(0)\n", "#     dfcc7['current_inventory'] = dfcc7['current_inventory'].fillna(0)\n", "#     dfcc7['lead_time'] = dfcc7.groupby('date_')['average_lead_time'].transform('mean') + 1\n", "#     dfcc7['consumption'] = dfcc7['consumption'].fillna(0)\n", "#     # n=handle_cpd_hikes(facility_id, forecast_cpd_1)\n", "#     # dfcc7['hike_factor']=n\n", "\n", "#     dfcc7['demand'] = (dfcc7['Avg_procured_qty']*1) * np.maximum(dfcc7['days_b/w_successive_po'], dfcc7['expected_po_frequency'])\n", "\n", "\n", "#     dfcc7 = pd.merge(dfcc7,\n", "#                      item_active_ds_count[['item_id', 'number_of_ds_connected']],\n", "#                      on='item_id',\n", "#                      how='left')\n", "#     high_value_tag = get_high_value_tag()\n", "#     high_value_items = set(high_value_tag['item_id'].unique())\n", "#     dfcc7['is_high_value'] = dfcc7['item_id'].isin(high_value_items).astype(int)\n", "#     # 4) Conditionally assign fe_safety_stock:\n", "#     dfcc7['fe_safety_stock'] = np.where(\n", "#         dfcc7['is_high_value'] == 1,\n", "#         np.maximum(dfcc7['Avg_procured_qty'] * 4, 1 * dfcc7['number_of_ds_connected']),\n", "#         np.maximum(dfcc7['Avg_procured_qty'] * 4, 2 * dfcc7['number_of_ds_connected'])\n", "#     )\n", "\n", "#     # dfcc7['fe_safety_stock'] = np.maximum(dfcc7['Avg_procured_qty'] * 4, 2*dfcc7['number_of_ds_connected'])\n", "\n", "#     dfcc7['open_sto_quantity'].fillna(0, inplace=True)\n", "#     weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']\n", "#     # Apply the function to the 'po_days' column\n", "#     dfcc7['max_po_day_gap'] = dfcc7['po_days'].apply(find_max_gap)\n", "#     # dfcc7 = dfcc7.drop(columns=['safety_stock'])\n", "#     dfcc7.loc[dfcc7['std_deviation_lead_time'] == 0, 'std_deviation_lead_time'] = 1\n", "#     # Update 'average_lead_time' to the value from 'tat_days' where 'average_lead_time' is 0\n", "#     dfcc7.loc[dfcc7['average_lead_time'] == 0, 'average_lead_time'] = dfcc7['tat_days']\n", "#     dfcc7.loc[(dfcc7['std_deviation_lead_time'] + dfcc7['mean_tat']) > (dfcc7['tat_days'] + 1), 'std_deviation_lead_time'] = 1\n", "#     Z=1.96\n", "#     dfcc7['safety_stock'] = Z * (dfcc7['std_deviation_lead_time']**2 * dfcc7['Avg_procured_qty']**2 +\n", "#                               dfcc7['average_lead_time'] * dfcc7['std_deviation_procured_quantity']**2)**0.5\n", "#     dfcc7['be_safety_stock'] = dfcc7.apply(\n", "#     lambda row: row['safety_stock'] + row['Avg_procured_qty'] * max(row['max_po_day_gap'], row['expected_po_frequency']),\n", "#     axis=1)\n", "#     dfcc7['selected_safety_stock'] = np.where(dfcc7['fill_rate_percentage'] < 75, dfcc7['be_safety_stock'], dfcc7['safety_stock'])\n", "#     return dfcc7"]}, {"cell_type": "code", "execution_count": null, "id": "11817a2d-c18f-46a8-93ac-92b3253761e3", "metadata": {}, "outputs": [], "source": ["def initial_indent(dfcc7, indent_data_1, facility_id):\n", "    dfcc7[\"base_quantity\"] = (\n", "        dfcc7[\"demand\"]\n", "        + dfcc7[\"consumption\"]\n", "        + dfcc7[\"selected_safety_stock\"]\n", "        + dfcc7[\"fe_safety_stock\"]\n", "    ) - (\n", "        dfcc7[\"holding_quantity\"]\n", "        + dfcc7[\"open_po_quantity\"]\n", "        + dfcc7[\"current_inventory\"]\n", "        + dfcc7[\"open_sto_quantity\"]\n", "        + dfcc7[\"quantity\"]\n", "    )\n", "    columns_to_round = [\n", "        \"safety_stock\",\n", "        \"fe_safety_stock\",\n", "        \"holding_quantity\",\n", "        \"open_po_quantity\",\n", "        \"current_inventory\",\n", "        \"consumption\",\n", "        \"demand\",\n", "        \"base_quantity\",\n", "    ]\n", "    dfcc7[columns_to_round] = dfcc7[columns_to_round].round(0)\n", "    indent_data_1.rename(columns={\"final_indent_quantity\": \"system_po_quantity\"}, inplace=True)\n", "    indent_data_1.rename(columns={\"current_inventory\": \"system_current_inventory\"}, inplace=True)\n", "    indent_data_1.rename(columns={\"open_po_quantity\": \"system_open_po_quantity\"}, inplace=True)\n", "    dfcc7 = pd.merge(\n", "        dfcc7,\n", "        indent_data_1[\n", "            [\n", "                \"item_id\",\n", "                \"system_current_inventory\",\n", "                \"system_po_quantity\",\n", "                \"system_open_po_quantity\",\n", "                \"error_details\",\n", "                \"is_critical\",\n", "                \"po_cycle\",\n", "                \"trigger_doi\",\n", "            ]\n", "        ],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    dfcc7[\"extra_days_of_demand\"] = dfcc7[\"trigger_doi\"] - dfcc7[\"po_cycle\"] - 3\n", "    dfcc7[\"extra_days_of_demand\"] = np.where(\n", "        dfcc7[\"extra_days_of_demand\"] < 0, 0, dfcc7[\"extra_days_of_demand\"]\n", "    )\n", "    # dfcc7['final_indent_quantity'] = (dfcc7['base_quantity']) // 1\n", "\n", "    dfcc7[\"final_indent_quantity\"] = (\n", "        dfcc7[\"base_quantity\"] + dfcc7[\"extra_days_of_demand\"] * dfcc7[\"Avg_procured_qty\"]\n", "    ) // 1\n", "\n", "    dfcc7[\"final_indent_quantity\"] = dfcc7[\"final_indent_quantity\"].apply(lambda x: max(0, x))\n", "    dfcc7[\"final_indent_quantity_adjusting_case\"] = (\n", "        np.ceil(dfcc7[\"final_indent_quantity\"] / dfcc7[\"case_size\"]) * dfcc7[\"case_size\"]\n", "    )\n", "\n", "    dfcc7.loc[\n", "        (dfcc7[\"case_size\"] / dfcc7[\"Avg_procured_qty\"] > 100) & (dfcc7[\"case_size\"] > 200),\n", "        \"final_indent_quantity_adjusting_case\",\n", "    ] = 0\n", "\n", "    dfcc7[\"case_check\"] = 1  # Default to 1\n", "    dfcc7.loc[\n", "        (dfcc7[\"case_size\"] / dfcc7[\"Avg_procured_qty\"] > 100) & (dfcc7[\"case_size\"] > 200),\n", "        \"case_check\",\n", "    ] = 0\n", "\n", "    dfcc7[\"final_number_of_cases\"] = np.ceil(\n", "        dfcc7[\"final_indent_quantity_adjusting_case\"] / dfcc7[\"case_size\"]\n", "    )\n", "    dfcc7[\"final_weight\"] = np.ceil(\n", "        dfcc7[\"final_indent_quantity_adjusting_case\"] * dfcc7[\"weight_in_gm\"]\n", "    )\n", "    dfcc7[\"final_indent_value\"] = (\n", "        dfcc7[\"final_indent_quantity_adjusting_case\"] * dfcc7[\"landing_price\"]\n", "    )\n", "    dfcc7[\"facility_id\"] = facility_id\n", "    dfcc7_ = dfcc7[dfcc7[\"date_\"] == end_date]\n", "    return dfcc7_"]}, {"cell_type": "code", "execution_count": null, "id": "826a52bc-2e14-42df-9cce-a955eb9cc6cc", "metadata": {}, "outputs": [], "source": ["def process_vendor_data(\n", "    start_date,\n", "    end_date,\n", "    facility_id,\n", "    indent_data,\n", "    outlet_data,\n", "    sale_data,\n", "    sale_data_1,\n", "    po_data,\n", "    open_po,\n", "    open_sto_quantity,\n", "    current_inventory,\n", "    holding_quantity,\n", "    indent_data_1,\n", "    forecast_cpd_1,\n", "    forecast_cpd_new,\n", "    ds_quantity_effective,\n", "    item_active_ds_count,\n", "    grouped_stats,\n", "    aggregate_demand,\n", "    holding_quantity_2,\n", "    pending_put,\n", "    number_of_unique_outlets,\n", "    min_qmax,\n", "):\n", "\n", "    dfb = rationalize_po_days(indent_data)\n", "    print(\"After rationalize_po_days:\", dfb.shape)\n", "    outlet_list, item_list, outlet_id = outlet_item_universe(outlet_data, dfb)\n", "    dfaaa1 = build_base(sale_data_1, start_date, end_date, outlet_list, item_list)\n", "    print(\"After build_base:\", dfaaa1.shape)\n", "    dfaa2 = process_rolling_window_statistics(dfaaa1)\n", "    print(\"After rolling_window:\", dfaa2.shape)\n", "    fr1, dfc1, dflt = calculate_fill_rate(po_data, start_date, end_date)\n", "    print(\"Shape of fr1:\", fr1.shape)\n", "    print(\"Shape of dfc1:\", dfc1.shape)\n", "    print(\"Shape of dflt:\", dflt.shape)\n", "\n", "    dfcc4 = base_fluctuations(dfc1, dfaa2, forecast_cpd_1, grouped_stats, end_date)\n", "    print(\"After base_fluctuations:\", dfcc4.shape)\n", "    dfcc4.fillna(0, inplace=True)\n", "    dfcc6 = pd.merge(\n", "        dfcc4,\n", "        dfb[\n", "            [\n", "                \"item_id\",\n", "                \"landing_price\",\n", "                \"case_size\",\n", "                \"load_type\",\n", "                \"open_po_quantity\",\n", "                \"current_inventory\",\n", "                \"item_name\",\n", "                \"weight_in_gm\",\n", "                \"po_days\",\n", "                \"tat_days\",\n", "            ]\n", "        ],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    dfcc6[\"weight_in_kg\"] = dfcc6[\"weight_in_gm\"] / 1000\n", "    dfcc6 = dfcc6[\n", "        [\n", "            \"date_\",\n", "            \"po_days\",\n", "            \"tat_days\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"landing_price\",\n", "            \"case_size\",\n", "            \"load_type\",\n", "            \"weight_in_gm\",\n", "            \"weight_in_kg\",\n", "            \"Avg_procured_qty\",\n", "            \"std_deviation_procured_quantity\",\n", "            \"average_lead_time\",\n", "            \"std_deviation_lead_time\",\n", "            \"open_po_quantity\",\n", "            \"current_inventory\",\n", "        ]\n", "    ]\n", "    print(\"After this:\", dfcc6.shape)\n", "    dfcc6.fillna(0, inplace=True)\n", "    dfcc6_modified = dfcc6.copy()\n", "    date_to_keep = end_date  # to be thought the relevance of it.\n", "    dfcc6_modified.loc[\n", "        dfcc6_modified[\"date_\"] != date_to_keep, [\"open_po_quantity\", \"current_inventory\"]\n", "    ] = 0\n", "    dfcc6_modified.head()\n", "    # excel_filename = 'check_1.xlsx'\n", "    # dfcc6_modified.to_excel(excel_filename, index=False)\n", "    dfcc6 = dfcc6_modified\n", "    dfcc6 = dfcc6.sort_values(by=\"date_\")\n", "    # dfcc6_=dfcc6[dfcc6['date_']=='2024-02-21']\n", "    start_date_ = pd.to_datetime(start_date)\n", "    end_date_ = pd.to_datetime(end_date)\n", "    # Filter the DataFrame to include only rows within the specified date range\n", "    filtered_dfcc6 = dfcc6[(dfcc6[\"date_\"] >= start_date_) & (dfcc6[\"date_\"] <= end_date)]\n", "    dfcc6 = filtered_dfcc6\n", "    dfcc6[\"date_\"] = pd.to_datetime(dfcc6[\"date_\"])\n", "    # Extract the day of the week and store it in a new 'day' column\n", "    dfcc6[\"day\"] = dfcc6[\"date_\"].dt.strftime(\"%A\").str.upper()\n", "    dfcc6[\"day\"] = dfcc6[\"day\"].str.title()\n", "    # Reorder the columns to place 'day' next to 'date_'\n", "    dfcc6 = dfcc6[[\"date_\", \"day\"] + [col for col in dfcc6.columns if col not in [\"date_\", \"day\"]]]\n", "    days_order = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"]\n", "    dfcc6[\"po_days\"] = dfcc6[\"po_days\"].apply(\n", "        lambda x: \",\".join(\n", "            sorted([day.strip() for day in x.split(\",\")], key=lambda day: days_order.index(day))\n", "        )\n", "    )\n", "\n", "    # Apply the function to create the next_po_day column\n", "    dfcc6[\"next_po_day\"] = dfcc6.apply(find_next_po_day, axis=1)\n", "\n", "    dfcc6[\"mean_tat\"] = np.ceil(dfcc6[\"average_lead_time\"]).astype(int)\n", "    dfcc6[\"mean_tat\"] = dfcc6[\"mean_tat\"].replace([0, np.nan], pd.NA)\n", "    dfcc6[\"mean_tat\"] = dfcc6[\"mean_tat\"].fillna(dfcc6[\"tat_days\"])\n", "    # Optionally ensure 'mean_tat' is integer type; this can handle cases where 'tat_days' also had non-integer types\n", "    dfcc6[\"mean_tat\"] = dfcc6[\"mean_tat\"].astype(int)\n", "    # dfcc6['mean_tat'] = dfcc6['average_lead_time'].round(0)\n", "    # Reorder the columns to place next_po_day next to day\n", "    dfcc6 = dfcc6[\n", "        [\n", "            \"date_\",\n", "            \"day\",\n", "            \"next_po_day\",\n", "            \"tat_days\",\n", "            \"po_days\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"landing_price\",\n", "            \"case_size\",\n", "            \"load_type\",\n", "            \"weight_in_gm\",\n", "            \"weight_in_kg\",\n", "            \"landing_price\",\n", "            \"case_size\",\n", "            \"Avg_procured_qty\",\n", "            \"std_deviation_procured_quantity\",\n", "            \"average_lead_time\",\n", "            \"std_deviation_lead_time\",\n", "            \"mean_tat\",\n", "            \"open_po_quantity\",\n", "            \"current_inventory\",\n", "        ]\n", "    ]\n", "\n", "    # Apply the function to create the day_difference column\n", "    dfcc6[\"days_b/w_successive_po\"] = dfcc6.apply(calculate_day_difference, axis=1)\n", "    dfcc6_ = dfcc6[dfcc6[\"date_\"] == end_date]\n", "    dfcc6 = dfcc6.loc[:, ~dfcc6.columns.duplicated()]\n", "    dfch = holding_quantity_2\n", "    dfcc6 = pd.merge(\n", "        dfcc6,\n", "        dfch[[\"item_id\", \"holding_quantity\", \"holding_quantity_consumable\"]],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    dfcc6[\"holding_quantity_consumable\"] = dfcc6[\"holding_quantity_consumable\"].fillna(0)\n", "    dfcc6 = pd.merge(\n", "        dfcc6,\n", "        ds_quantity_effective[[\"item_id\", \"effective_holding_quantity\"]],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    dfcc6[\"effective_holding_quantity\"] = dfcc6[\"effective_holding_quantity\"].fillna(0)\n", "    dfcc6 = dfcc6.merge(pending_put[[\"item_id\", \"quantity\"]], on=\"item_id\", how=\"left\")\n", "    dfcc6[\"quantity\"] = dfcc6[\"quantity\"].fillna(0)\n", "    dfcc61 = current_inventory\n", "    dfcc61.rename(columns={\"actual_quantity\": \"current_inventory\"}, inplace=True)\n", "    dfcc6 = dfcc6.drop([\"current_inventory\", \"open_po_quantity\"], axis=1)\n", "    dfcc6 = pd.merge(dfcc6, dfcc61[[\"item_id\", \"current_inventory\"]], on=\"item_id\", how=\"left\")\n", "    dfcc6 = dfcc6.drop([\"Avg_procured_qty\"], axis=1)\n", "    df_sto = open_sto_quantity\n", "    dfcc6 = dfcc6.merge(df_sto[[\"item_id\", \"open_sto_quantity\"]], on=\"item_id\", how=\"left\")\n", "    # dfcc6 = dfcc6.merge(df_sto[['item_id', 'open_sto_quantity']], on='item_id', how='left')\n", "    dfcc6 = dfcc6.merge(forecast_cpd_1[[\"item_id\", \"Avg_procured_qty\"]], on=\"item_id\", how=\"left\")\n", "\n", "    new_forecast = get_new_forecast_cpd(facility_id)\n", "\n", "    # new_forecast.to_csv('new_forecast.csv')\n", "\n", "    dfcc6 = consumption_simulator(dfcc6, open_po, end_date, forecast_cpd_new)\n", "    dfcc6 = pd.merge(\n", "        dfcc6,\n", "        indent_data_1[[\"item_id\", \"load_size\", \"aligned_vendor_id\", \"group_id\"]],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    dfcc6[\"case_per_quantity\"] = 1 / dfcc6[\"case_size\"]\n", "    latest_date = end_date\n", "    filtered_data = dfcc6[dfcc6[\"date_\"] == end_date]\n", "    # Define the function to calculate sum_product for each group based on load_type\n", "    grouped = filtered_data.groupby([\"aligned_vendor_id\", \"group_id\"])\n", "    sum_product = grouped.apply(calculate_sum_product).reset_index(name=\"sum_product\")\n", "    dfcc6 = pd.merge(dfcc6, sum_product, on=[\"aligned_vendor_id\", \"group_id\"], how=\"left\")\n", "    dfcc6[\"expected_po_frequency\"] = np.minimum(31, dfcc6[\"load_size\"] / dfcc6[\"sum_product\"])\n", "    dfcc7 = calculate_safety_stock(\n", "        dfcc6, fr1, item_active_ds_count, forecast_cpd_1, forecast_cpd_new, facility_id, min_qmax\n", "    )\n", "    dfcc7_ = initial_indent(dfcc7, indent_data_1, facility_id)\n", "    return dfcc7_"]}, {"cell_type": "code", "execution_count": null, "id": "0643ec7c-5bb7-40a2-b46a-785c3c109001", "metadata": {}, "outputs": [], "source": ["def aggregate_data(df):\n", "    df.loc[df[\"load_type\"] == 1, \"final_indent_value\"] = (\n", "        df[\"final_indent_quantity_adjusting_case\"] * df[\"landing_price\"]\n", "    )\n", "\n", "    df.loc[df[\"load_type\"] == 2, \"final_weight\"] = (\n", "        df[\"final_indent_quantity_adjusting_case\"] * df[\"weight_in_gm\"]\n", "    )\n", "    df.loc[df[\"load_type\"] == 3, \"final_number_of_cases\"] = (\n", "        df[\"final_indent_quantity_adjusting_case\"] * df[\"case_per_quantity\"]\n", "    )\n", "    # Aggregate the data based on the 'load_type'\n", "    df_grouped = df.groupby([\"aligned_vendor_id\", \"group_id\", \"load_type\"])\n", "    # Using dictionary to specify aggregation for each column based on load_type\n", "    aggregation_funcs = {\n", "        1: (\"final_indent_value\", sum),\n", "        2: (\"final_weight\", sum),\n", "        3: (\"final_number_of_cases\", sum),\n", "    }\n", "    # Initialize a DataFrame to store results\n", "    results = pd.DataFrame()\n", "    for load_type, (column, func) in aggregation_funcs.items():\n", "        # Filter and aggregate data\n", "        temp_df = df[df[\"load_type\"] == load_type]\n", "        aggregated_data = (\n", "            temp_df.groupby([\"aligned_vendor_id\", \"group_id\"])[column].agg(func).reset_index()\n", "        )\n", "        aggregated_data.rename(columns={column: \"Summed Value\"}, inplace=True)\n", "        # Add load_type info\n", "        aggregated_data[\"load_type\"] = load_type\n", "        # Append results\n", "        results = pd.concat([results, aggregated_data], ignore_index=True)\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "id": "2576044d-7a7b-4792-a92c-cdbbc1353829", "metadata": {}, "outputs": [], "source": ["def assign_percentile(group):\n", "    # Calculate the 40th percentile threshold within the group\n", "    percentile_40 = group[\"doi_post_landing\"].quantile(1)\n", "    # percentile_40 = group['doi_post_landing'].quantile(0.4)\n", "    # Assign 1 if doi_post_landing is below or equal to the 40th percentile, else 0\n", "    group[\"less_than_40_percentile\"] = (group[\"doi_post_landing\"] <= percentile_40).astype(int)\n", "    return group"]}, {"cell_type": "code", "execution_count": null, "id": "35810ab6-dbd3-48da-9ae2-af317aa61d1b", "metadata": {}, "outputs": [], "source": ["def check_po_day(row):\n", "    po_days_list = row[\"po_days\"].split(\",\")  # Split the po_days string into a list of days\n", "    return 1 if row[\"day\"] in po_days_list else 0  # Check if 'day' is in this list"]}, {"cell_type": "code", "execution_count": null, "id": "1b8c3220-5e4b-4cd9-8554-6632823eb472", "metadata": {}, "outputs": [], "source": ["def check_indent_load(merged_df):\n", "    A1 = merged_df\n", "    filtered_df = A1\n", "    filtered_df[\"expected_po\"] = np.select(\n", "        [\n", "            filtered_df[\"load_type\"] == 3,\n", "            filtered_df[\"load_type\"] == 2,\n", "            filtered_df[\"load_type\"] == 1,\n", "        ],\n", "        [\n", "            filtered_df[\"final_number_of_cases\"],\n", "            filtered_df[\"final_weight\"],\n", "            filtered_df[\"final_indent_value\"],\n", "        ],\n", "        default=0,\n", "    )\n", "    # Group by 'aligned_vendor_id' and 'group_id', summing 'expected_po' and keeping 'load_size'\n", "    grouped_df = (\n", "        filtered_df.groupby([\"aligned_vendor_id\", \"group_id\"])\n", "        .agg(\n", "            {\n", "                \"expected_po\": \"sum\",\n", "                \"final_indent_quantity_adjusting_case\": \"sum\",\n", "                \"system_po_quantity\": \"sum\",\n", "                \"item_id\": \"count\",\n", "                \"load_size\": \"first\",  # assuming load_size is the same within each group\n", "            }\n", "        )\n", "        .reset_index()\n", "    )\n", "\n", "    grouped_df[\"percent_load_met\"] = (grouped_df[\"expected_po\"] / grouped_df[\"load_size\"]) * 100\n", "    grouped_df = grouped_df.sort_values(by=\"percent_load_met\", ascending=False).reset_index(\n", "        drop=True\n", "    )\n", "    filtered_df = grouped_df[\n", "        (grouped_df[\"percent_load_met\"] > 65) & (grouped_df[\"percent_load_met\"] < 100)\n", "    ]\n", "    result = filtered_df[[\"aligned_vendor_id\", \"group_id\"]]\n", "    A1 = pd.merge(A1, result, on=[\"aligned_vendor_id\", \"group_id\"], how=\"left\", indicator=True)\n", "    # Creating the Match column based on the merge indicator\n", "    A1[\"Match\"] = A1[\"_merge\"].apply(lambda x: 1 if x == \"both\" else 0)\n", "    # Dropping the indicator column used for matching\n", "    A1.drop(columns=[\"_merge\"], inplace=True)\n", "    # Displaying the first few rows of the updated dataframe\n", "    A1[\"inventory_at_tat\"] = (\n", "        A1[\"current_inventory\"]\n", "        + A1[\"holding_quantity\"]\n", "        + A1[\"open_po_quantity\"]\n", "        + A1[\"quantity\"]\n", "        - A1[\"consumption\"]\n", "    )\n", "    A1[\"doi_at_landing\"] = (A1[\"inventory_at_tat\"] / A1[\"Avg_procured_qty\"]).round(2)\n", "    A1[\"inventory_post_landing\"] = A1[\"inventory_at_tat\"] + np.where(\n", "        (A1[\"Raise PO\"] == \"Yes\") & (A1[\"is_po_day\"] == 1),\n", "        A1[\"final_indent_quantity_adjusting_case\"],\n", "        0,\n", "    )\n", "    A1[\"doi_post_landing\"] = (A1[\"inventory_post_landing\"] / A1[\"Avg_procured_qty\"]).round(2)\n", "    A1[\"average_doi_post_landing\"] = (\n", "        A1.groupby([\"aligned_vendor_id\", \"group_id\"])[\"doi_post_landing\"].transform(\"mean\").round(2)\n", "    )\n", "    return A1"]}, {"cell_type": "code", "execution_count": null, "id": "b52ec08c-acaf-49cb-bbb6-774279e82e0d", "metadata": {}, "outputs": [], "source": ["# def trigger_load_meet(A1):\n", "#     A1 = A1.groupby(['aligned_vendor_id', 'group_id']).apply(assign_percentile)\n", "#     groups = A1.groupby(['aligned_vendor_id', 'group_id'])\n", "#     for name, group in groups:\n", "#         avg_load_size = group['load_size'].mean()\n", "#         load_value = 0\n", "#         # Calculate initial load_value for the whole group\n", "#         for idx, row in group.iterrows():\n", "#             if row['load_type'] == 1:\n", "#                 load_value += row['final_indent_quantity_adjusting_case'] * row['landing_price']\n", "#             elif row['load_type'] == 2:\n", "#                 load_value += row['final_indent_quantity_adjusting_case'] * row['weight_in_gm']\n", "#             elif row['load_type'] == 3:\n", "#                 load_value += row['final_indent_quantity_adjusting_case'] * row['case_per_quantity']\n", "\n", "#         # relevant_rows = group[(group['Match'] == 1) & (group['less_than_40_percentile'] == 1)].sort_values('doi_post_landing')\n", "\n", "#         relevant_rows = group[\n", "#             (group['Match'] == 1) &\n", "#             (group['less_than_40_percentile'] == 1) &\n", "#             (group['case_check'] == 1)\n", "#         ].sort_values('doi_post_landing')\n", "#         # Loop for multiple cycles\n", "#         cycle_count = 0\n", "#         while load_value <= avg_load_size and cycle_count < 500:\n", "#             for idx, row in relevant_rows.iterrows():\n", "#                 additional_case = row['case_size']\n", "#                 A1.loc[idx, 'final_indent_quantity_adjusting_case'] += additional_case\n", "\n", "#                 if row['load_type'] == 1:\n", "#                     load_value += additional_case * row['landing_price']\n", "#                 elif row['load_type'] == 2:\n", "#                     load_value += additional_case * row['weight_in_gm']\n", "#                 elif row['load_type'] == 3:\n", "#                     load_value += additional_case * row['case_per_quantity']\n", "#             cycle_count += 1\n", "#             if load_value > avg_load_size:\n", "#                 break  # Exit after condition is met\n", "#     return A1"]}, {"cell_type": "code", "execution_count": null, "id": "5444fd8a-e070-4232-a939-0169dd586729", "metadata": {}, "outputs": [], "source": ["def trigger_load_meet(A1):\n", "    A1 = A1.groupby([\"aligned_vendor_id\", \"group_id\"]).apply(assign_percentile)\n", "    groups = A1.groupby([\"aligned_vendor_id\", \"group_id\"])\n", "\n", "    for (vendor_id, grp_id), group_index in groups.groups.items():\n", "        # Subset for this vendor + group\n", "        group = A1.loc[group_index]\n", "\n", "        # Compute the average load_size for this group\n", "        avg_load_size = group[\"load_size\"].mean()\n", "\n", "        # Calculate the initial load_value for the group\n", "        load_value = 0.0\n", "        for idx, row in group.iterrows():\n", "            if row[\"load_type\"] == 1:\n", "                load_value += row[\"final_indent_quantity_adjusting_case\"] * row[\"landing_price\"]\n", "            elif row[\"load_type\"] == 2:\n", "                load_value += row[\"final_indent_quantity_adjusting_case\"] * row[\"weight_in_gm\"]\n", "            elif row[\"load_type\"] == 3:\n", "                load_value += row[\"final_indent_quantity_adjusting_case\"] * row[\"case_per_quantity\"]\n", "\n", "        # We'll do this in a loop until we exceed avg_load_size or reach a max iteration\n", "        cycle_count = 0\n", "        max_cycles = 500\n", "\n", "        while load_value <= avg_load_size and cycle_count < max_cycles:\n", "            cycle_count += 1\n", "\n", "            # STEP A: Recompute doi_post_landing for all rows in this group\n", "            for idx, row in group.iterrows():\n", "                if row[\"Avg_procured_qty\"] > 0:  # Avoid division by zero\n", "                    A1.loc[idx, \"doi_post_landing\"] = (\n", "                        A1.loc[idx, \"final_indent_quantity_adjusting_case\"]\n", "                        + A1.loc[idx, \"inventory_at_tat\"]\n", "                    ) / A1.loc[idx, \"Avg_procured_qty\"]\n", "                #     A1.loc[idx, 'doi_post_landing'] = (\n", "                #         A1.loc[idx, 'final_indent_quantity_adjusting_case'] / A1.loc[idx, 'Avg_procured_qty']\n", "                #     )\n", "                else:\n", "                    A1.loc[idx, \"doi_post_landing\"] = None\n", "\n", "            # Pull the latest version of the group from A1\n", "            group = A1.loc[group_index]\n", "\n", "            # STEP B: Identify relevant rows that we could increment\n", "            # (Match == 1, less_than_40_percentile == 1, case_check == 1)\n", "            relevant_rows = group[\n", "                (group[\"Match\"] == 1)\n", "                & (group[\"less_than_40_percentile\"] == 1)\n", "                & (group[\"case_check\"] == 1)\n", "            ].copy()\n", "\n", "            # If no relevant rows, we can't increment further\n", "            if relevant_rows.empty:\n", "                break\n", "\n", "            # STEP C: Sort them by the newly updated doi_post_landing\n", "            relevant_rows.sort_values(by=\"doi_post_landing\", ascending=True, inplace=True)\n", "\n", "            # The row with the smallest doi_post_landing is at the top\n", "            # We'll increment the first row only (one-at-a-time approach).\n", "            top_idx = relevant_rows.index[0]\n", "            top_row = relevant_rows.iloc[0]\n", "\n", "            # Double-check if we're already above avg_load_size\n", "            if load_value > avg_load_size:\n", "                break\n", "\n", "            # STEP D: Add one \"case_size\" increment to the chosen row\n", "            additional_case = top_row[\"case_size\"]\n", "            A1.loc[top_idx, \"final_indent_quantity_adjusting_case\"] += additional_case\n", "\n", "            # STEP E: Update load_value based on the row's load_type\n", "            if top_row[\"load_type\"] == 1:\n", "                load_value += additional_case * top_row[\"landing_price\"]\n", "            elif top_row[\"load_type\"] == 2:\n", "                load_value += additional_case * top_row[\"weight_in_gm\"]\n", "            elif top_row[\"load_type\"] == 3:\n", "                load_value += additional_case * top_row[\"case_per_quantity\"]\n", "\n", "        # End of while loop for this group\n", "\n", "    return A1"]}, {"cell_type": "code", "execution_count": null, "id": "549c304a-305d-4233-b3d2-d9684a846221", "metadata": {}, "outputs": [], "source": ["def post_po_calculation(aggregated_results, df):\n", "    comparison_df = aggregated_results.merge(\n", "        df[[\"aligned_vendor_id\", \"group_id\", \"load_size\"]].drop_duplicates(),\n", "        on=[\"aligned_vendor_id\", \"group_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    # Determine whether to raise PO\n", "    comparison_df[\"Raise PO\"] = np.where(\n", "        comparison_df[\"Summed Value\"] > comparison_df[\"load_size\"], \"Yes\", \"No\"\n", "    )\n", "    comparison_df_sorted = comparison_df.sort_values(by=\"Raise PO\", ascending=False)\n", "    comparison_df_sorted\n", "    merged_df = df.merge(\n", "        comparison_df[[\"aligned_vendor_id\", \"group_id\", \"Raise PO\"]],\n", "        on=[\"aligned_vendor_id\", \"group_id\"],\n", "        how=\"inner\",\n", "    )\n", "    merged_df[\"is_po_day\"] = merged_df.apply(check_po_day, axis=1)\n", "    A1 = check_indent_load(merged_df)\n", "    df1 = trigger_load_meet(A1)\n", "\n", "    aggregated_results = aggregate_data(df1)\n", "    # Merge with original DataFrame to get load_size for comparison\n", "\n", "    comparison_df = aggregated_results.merge(\n", "        df1[[\"aligned_vendor_id\", \"group_id\", \"load_size\"]].drop_duplicates(),\n", "        on=[\"aligned_vendor_id\", \"group_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    # Determine whether to raise PO\n", "    comparison_df[\"Final Raise PO\"] = np.where(\n", "        comparison_df[\"Summed Value\"] > comparison_df[\"load_size\"], \"Yes\", \"No\"\n", "    )\n", "    comparison_df_sorted = comparison_df.sort_values(by=\"Final Raise PO\", ascending=False)\n", "    comparison_df_sorted\n", "    merged_df = df1.merge(\n", "        comparison_df[[\"aligned_vendor_id\", \"group_id\", \"Final Raise PO\"]],\n", "        on=[\"aligned_vendor_id\", \"group_id\"],\n", "        how=\"inner\",\n", "    )\n", "    merged_df[\"inventory_post_landing_final\"] = merged_df[\"inventory_at_tat\"] + np.where(\n", "        merged_df[\"Final Raise PO\"] == \"Yes\", merged_df[\"final_indent_quantity_adjusting_case\"], 0\n", "    )\n", "    merged_df[\"doi_post_landing\"] = (\n", "        merged_df[\"inventory_post_landing_final\"] / merged_df[\"Avg_procured_qty\"]\n", "    ).round(2)\n", "    merged_df[\"average_doi_post_landing\"] = (\n", "        merged_df.groupby([\"aligned_vendor_id\", \"group_id\"])[\"doi_post_landing\"]\n", "        .transform(\"mean\")\n", "        .round(2)\n", "    )\n", "    conditions = [\n", "        merged_df[\"load_type\"] == 1,\n", "        merged_df[\"load_type\"] == 2,\n", "        merged_df[\"load_type\"] == 3,\n", "    ]\n", "\n", "    # Define the choices corresponding to each condition\n", "    choices = [\n", "        merged_df[\"final_indent_value\"],  # When load_type is 1\n", "        merged_df[\"final_weight\"],  # When load_type is 2\n", "        merged_df[\"final_number_of_cases\"],  # When load_type is 3\n", "    ]\n", "\n", "    # Create the 'load_value' column using np.select\n", "    merged_df[\"load_value\"] = np.select(conditions, choices, default=np.nan)\n", "    merged_df[\"summed_load_value\"] = merged_df.groupby([\"aligned_vendor_id\", \"group_id\"])[\n", "        \"load_value\"\n", "    ].transform(\"sum\")\n", "    # Create the new column 'days_till_next_po' based on conditions\n", "    merged_df[\"days_till_next_po\"] = np.where(\n", "        merged_df[\"Final Raise PO\"] == \"No\",\n", "        ((merged_df[\"load_size\"] - merged_df[\"summed_load_value\"]) / merged_df[\"load_size\"])\n", "        * merged_df[\"expected_po_frequency\"],\n", "        merged_df[\"expected_po_frequency\"],\n", "    )\n", "    merged_df[\"days_till_next_po\"] = np.ceil(merged_df[\"days_till_next_po\"]).astype(int)\n", "    fac_id = merged_df[\"facility_id\"].iloc[0]\n", "\n", "    # Create a filename that includes the facility_id value\n", "    filename = f\"today_facility_id_{fac_id}.csv\"\n", "\n", "    # Save the DataFrame to the file\n", "    merged_df.to_csv(filename, index=False)\n", "\n", "    print(f\"Saved file: {filename}\")\n", "    # merged_df.to_csv('merged_df.csv')\n", "    return merged_df"]}, {"cell_type": "code", "execution_count": null, "id": "2865cff6-21c6-46d3-a941-b364b63d6db3", "metadata": {"tags": []}, "outputs": [], "source": ["current_date = datetime.now(IST)\n", "print(current_date)\n", "end_date = (current_date - timedelta(days=0)).strftime(\"%Y-%m-%d\")  # Current date minus 1 day\n", "start_date = (current_date - timed<PERSON>ta(days=60)).strftime(\"%Y-%m-%d\")  # Current date minus 6 days\n", "Trino_connection_3 = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def process_facility(facility_id):\n", "    import pandas as pd\n", "    import numpy as np\n", "    from datetime import datetime, timedelta\n", "    from datetime import datetime\n", "    import pencilbox as pb\n", "    import itertools\n", "\n", "    current_date = datetime.now(IST)\n", "    end_date = (current_date - timedelta(days=0)).strftime(\"%Y-%m-%d\")\n", "    start_date = (current_date - timed<PERSON>ta(days=60)).strftime(\"%Y-%m-%d\")\n", "    selected_run_id = get_run_data(facility_id)\n", "    outlet_cpd_data = get_outlet_cpd_data(selected_run_id)\n", "    outlet_cpd_data[\"cpd\"] = pd.to_numeric(outlet_cpd_data[\"cpd\"], errors=\"coerce\")\n", "    B = get_facility_details(facility_id)\n", "    tag_value = B[\"tag_value\"].iloc[0]\n", "    wh_outlet_id = B[\"be_outlet_id\"].iloc[0]\n", "    indent_data = get_indent_data(selected_run_id, tag_value)\n", "    indent_data = correct_po_days(indent_data)\n", "    indent_data_1 = indent_data\n", "    distinct_item_ids = indent_data[\"item_id\"].unique()\n", "    item_list = \",\".join(map(str, distinct_item_ids))\n", "    string_values = item_list.split(\",\")\n", "    # Convert the strings to integers using list comprehension\n", "    int_values = [int(value) for value in string_values]\n", "    # Convert the list to a tuple\n", "    item_list = tuple(int_values)\n", "    df = pd.DataFrame(item_list, columns=[\"item_id\"])\n", "    outlet_data = get_outlet_data(facility_id, selected_run_id, end_date, item_list)\n", "    outlet_data.rename(columns={\"to_outlet_id\": \"outlet_id\"}, inplace=True)\n", "    number_of_unique_outlets = outlet_data.shape[0]\n", "\n", "    item_active_ds_count = get_ds_connected(selected_run_id, end_date, item_list)\n", "\n", "    item_active_ds_count_1 = get_active_ds_count(item_list, tag_value)\n", "\n", "    item_active_ds_count = pd.merge(\n", "        item_active_ds_count,\n", "        item_active_ds_count_1[[\"item_id\", \"distinct_outlets\"]],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "\n", "    # item_active_ds_count['number_of_ds_connected'] = np.maximum(item_active_ds_count['number_of_ds_connected_1'], item_active_ds_count['distinct_outlets'])\n", "\n", "    item_active_ds_count[\"number_of_ds_connected\"] = np.maximum(\n", "        item_active_ds_count[\"number_of_ds_connected_1\"],\n", "        item_active_ds_count[\"number_of_ds_connected_1\"],\n", "    )\n", "\n", "    distinct_outlet_id = outlet_data[\"outlet_id\"].unique()\n", "    outlet_list = \",\".join(map(str, distinct_outlet_id))\n", "    string_values = outlet_list.split(\",\")\n", "\n", "    # Convert the strings to integers using list comprehension\n", "    int_values = [int(value) for value in string_values]\n", "\n", "    # Convert the list to a tuple\n", "    outlet_list = tuple(int_values)\n", "\n", "    forecast_cpd_1, forecast_cpd_new = get_forecast_cpd(tag_value)\n", "\n", "    forecast_cpd_1, n = handle_cpd_hikes(facility_id, forecast_cpd_1)\n", "    forecast_cpd_new[\"new_forecast\"] = n * forecast_cpd_new[\"new_forecast\"]\n", "    indent_data = pd.merge(\n", "        indent_data, forecast_cpd_1[[\"item_id\", \"Avg_procured_qty\"]], on=\"item_id\", how=\"left\"\n", "    )\n", "    indent_data[\"case_per_quantity\"] = 1 / indent_data[\"case_size\"]\n", "    grouped = indent_data.groupby([\"aligned_vendor_id\", \"group_id\"])\n", "    sum_product = grouped.apply(calculate_sum_product).reset_index(name=\"sum_product\")\n", "    dfcc6 = pd.merge(indent_data, sum_product, on=[\"aligned_vendor_id\", \"group_id\"], how=\"left\")\n", "    dfcc6[\"hike_factor\"] = n\n", "    indent_data[\"expected_po_frequency\"] = np.minimum(31, dfcc6[\"load_size\"] / dfcc6[\"sum_product\"])\n", "    current_date = datetime.now(IST).strftime(\n", "        \"%Y-%m-%d\"\n", "    )  # Formatting the date as a string in the format YYYY-MM-DD\n", "    indent_data[\"date_\"] = current_date\n", "\n", "    indent_data = arrange_po_days(indent_data)\n", "\n", "    # Apply the function to create the next_po_day column\n", "    indent_data[\"next_po_day\"] = indent_data.apply(find_next_po_day, axis=1)\n", "    # Apply the function to create the day_difference column\n", "    indent_data[\"days_b/w_successive_po\"] = indent_data.apply(calculate_day_difference, axis=1)\n", "    sale_data = get_sales_data(start_date, end_date, item_list, outlet_list)\n", "    grouped_df_1, sale_data_1 = remove_unusual_sales(sale_data, indent_data)\n", "    std = grouped_df_1\n", "    grouped_stats = std.groupby(\"item_id\")[\"procured_quantity_1\"].agg([\"mean\", \"std\"]).reset_index()\n", "    outlet_id = outlet_list[0]\n", "    po_data = get_po_data(item_list, outlet_id)\n", "\n", "    po_data = po_data[po_data[\"facility_id\"] == facility_id]\n", "\n", "    po_data = po_data[po_data[\"name\"] != \"Rejected\"]\n", "    po_data = po_data[po_data[\"name\"] != \"Cancelled post Creation\"]\n", "\n", "    po_data = pd.merge(\n", "        po_data, indent_data[[\"aligned_vendor_id\", \"group_id\", \"item_id\"]], how=\"left\", on=\"item_id\"\n", "    )\n", "    open_po = get_open_po_data(item_list, end_date, facility_id)\n", "    open_po = pd.merge(\n", "        open_po, indent_data[[\"aligned_vendor_id\", \"group_id\", \"item_id\"]], how=\"left\", on=\"item_id\"\n", "    )\n", "\n", "    grouped_df = open_po.groupby(\"item_id\")[\"landing_po_quantity\"].sum().reset_index()\n", "\n", "    sale_data_1[\"date_\"] = pd.to_datetime(sale_data_1[\"date_\"])\n", "\n", "    # Define the end date as the most recent date in the data and the start date as 30 days before\n", "    end_date = sale_data_1[\"date_\"].max()\n", "    start_date = end_date - pd.Timedelta(days=30)\n", "\n", "    # Filter the data for the last 30 days\n", "    filtered_data = sale_data_1[\n", "        (sale_data_1[\"date_\"] >= start_date) & (sale_data_1[\"date_\"] <= end_date)\n", "    ]\n", "\n", "    # Group by 'outlet_id' and 'item_id', and calculate the average 'procured_quantity_1'\n", "    grouped_data = (\n", "        filtered_data.groupby([\"outlet_id\", \"item_id\"])[\"procured_quantity_1\"].mean().reset_index()\n", "    )\n", "\n", "    # Rename the column for clarity\n", "    grouped_data.rename(\n", "        columns={\"procured_quantity_1\": \"average_procured_quantity_1_last_30_days\"}, inplace=True\n", "    )\n", "\n", "    holding_quantity_1 = get_holding_quantity(item_list, outlet_list)\n", "\n", "    ##########\n", "\n", "    df_min_qmax = get_min_qmax(\n", "        item_list=item_list, outlet_list=outlet_list, facility_id=facility_id\n", "    )\n", "\n", "    # compute the derived column\n", "    df_min_qmax[\"fe_safety_stock_new\"] = (df_min_qmax[\"min_qty\"] + df_min_qmax[\"qmax\"]) / 2\n", "    df_min_qmax[\"facility_id\"] = facility_id\n", "\n", "    print(df_min_qmax.head(1))\n", "\n", "    min_qmax = df_min_qmax.groupby([\"facility_id\", \"item_id\"], as_index=False)[\n", "        \"fe_safety_stock_new\"\n", "    ].sum()\n", "\n", "    print(min_qmax.head(1))\n", "\n", "    ##########\n", "\n", "    holding_quantity_2 = pd.merge(\n", "        holding_quantity_1,\n", "        indent_data[\n", "            [\n", "                \"item_id\",\n", "                \"expected_po_frequency\",\n", "                \"days_b/w_successive_po\",\n", "                \"tat_days\",\n", "                \"trigger_doi\",\n", "                \"po_cycle\",\n", "            ]\n", "        ],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "\n", "    holding_quantity_2 = pd.merge(\n", "        holding_quantity_2,\n", "        outlet_cpd_data[[\"item_id\", \"outlet_id\", \"cpd\"]],\n", "        on=[\"item_id\", \"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "    holding_quantity_2[\"cpd\"] = holding_quantity_2[\"cpd\"].fillna(0)\n", "    holding_quantity_2[\"net_po_frequency\"] = np.maximum(\n", "        holding_quantity_2[\"expected_po_frequency\"], holding_quantity_2[\"days_b/w_successive_po\"]\n", "    )\n", "\n", "    holding_quantity_2[\"extra_days_of_demand\"] = (\n", "        holding_quantity_2[\"trigger_doi\"] - holding_quantity_2[\"po_cycle\"] - 3\n", "    )\n", "    holding_quantity_2[\"extra_days_of_demand\"] = np.where(\n", "        holding_quantity_2[\"extra_days_of_demand\"] < 0,\n", "        0,\n", "        holding_quantity_2[\"extra_days_of_demand\"],\n", "    )\n", "\n", "    holding_quantity_2[\"holding_quantity_net\"] = np.minimum(\n", "        (\n", "            holding_quantity_2[\"net_po_frequency\"]\n", "            + 4\n", "            + holding_quantity_2[\"tat_days\"]\n", "            + holding_quantity_2[\"extra_days_of_demand\"]\n", "        )\n", "        * holding_quantity_2[\"cpd\"],\n", "        holding_quantity_2[\"holding_quantity\"],\n", "    )\n", "\n", "    holding_quantity_2[\"holding_quantity_consumable\"] = np.minimum(\n", "        (holding_quantity_2[\"tat_days\"] + 1) * holding_quantity_2[\"cpd\"],\n", "        holding_quantity_2[\"holding_quantity\"],\n", "    )\n", "\n", "    holding_quantity_2 = (\n", "        holding_quantity_2.groupby(\"item_id\")[\n", "            [\"holding_quantity_net\", \"holding_quantity_consumable\"]\n", "        ]\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "    holding_quantity_2[\"holding_quantity_consumable\"] = holding_quantity_2[\n", "        \"holding_quantity_consumable\"\n", "    ].fillna(0)\n", "\n", "    holding_quantity_2[\"holding_quantity_net\"] = (\n", "        holding_quantity_2[\"holding_quantity_net\"].round().astype(int)\n", "    )\n", "    holding_quantity_2[\"holding_quantity_consumable\"] = (\n", "        holding_quantity_2[\"holding_quantity_consumable\"].round().astype(int)\n", "    )\n", "    holding_quantity_2.rename(columns={\"holding_quantity_net\": \"holding_quantity\"}, inplace=True)\n", "    outlet_demand = pd.merge(\n", "        holding_quantity_1,\n", "        outlet_cpd_data[[\"item_id\", \"outlet_id\", \"cpd\"]],\n", "        on=[\"item_id\", \"outlet_id\"],\n", "        how=\"right\",\n", "    )\n", "    outlet_demand[\"holding_quantity\"] = outlet_demand[\"holding_quantity\"].fillna(0)\n", "    outlet_demand = pd.merge(\n", "        outlet_demand,\n", "        indent_data[[\"item_id\", \"expected_po_frequency\", \"days_b/w_successive_po\"]],\n", "        on=\"item_id\",\n", "        how=\"left\",\n", "    )\n", "    outlet_demand[\"days_to_order\"] = np.maximum(\n", "        outlet_demand[\"expected_po_frequency\"], outlet_demand[\"days_b/w_successive_po\"]\n", "    )\n", "    outlet_demand[\"fe_safety_stock\"] = np.maximum(outlet_demand[\"cpd\"] * 3, 1)\n", "    outlet_demand[\"net_demand\"] = np.maximum(\n", "        (outlet_demand[\"cpd\"] * outlet_demand[\"days_to_order\"] + outlet_demand[\"fe_safety_stock\"])\n", "        - outlet_demand[\"holding_quantity\"],\n", "        0,\n", "    )\n", "    outlet_demand = outlet_demand[outlet_demand[\"cpd\"].notna()]\n", "    aggregate_demand = outlet_demand.groupby(\"item_id\")[\"net_demand\"].sum().reset_index()\n", "    aggregate_demand.rename(columns={\"net_demand\": \"total_net_demand\"}, inplace=True)\n", "    holding_quantity_1 = pd.merge(\n", "        holding_quantity_1,\n", "        grouped_data[[\"outlet_id\", \"item_id\", \"average_procured_quantity_1_last_30_days\"]],\n", "        on=[\"item_id\", \"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "    holding_quantity_1[\"average_procured_quantity_1_last_30_days\"] = holding_quantity_1[\n", "        \"average_procured_quantity_1_last_30_days\"\n", "    ].fillna(0.05)\n", "\n", "    # holding_quantity_1['holding_quantity'] = holding_quantity_1.apply(\n", "    #     lambda row: min(row['holding_quantity'], 3 * row['average_procured_quantity_1_last_30_days'])\n", "    #     if (row['holding_quantity'] > 3 * row['average_procured_quantity_1_last_30_days'] and row['average_procured_quantity_1_last_30_days'] > 1)\n", "    #     else row['holding_quantity'],\n", "    #     axis=1\n", "    # )\n", "\n", "    ds_quantity_effective = (\n", "        holding_quantity_1.groupby(\"item_id\")[\"holding_quantity\"].sum().reset_index()\n", "    )\n", "    ds_quantity_effective = ds_quantity_effective.rename(\n", "        columns={\"holding_quantity\": \"effective_holding_quantity\"}\n", "    )\n", "\n", "    holding_quantity = get_holding_quantity_1(item_list, outlet_list)\n", "\n", "    holding_quantity = pd.merge(\n", "        holding_quantity,\n", "        indent_data[[\"aligned_vendor_id\", \"group_id\", \"item_id\"]],\n", "        how=\"left\",\n", "        on=\"item_id\",\n", "    )\n", "    holding_quantity[\"holding_quantity\"] = holding_quantity[\"holding_quantity\"].fillna(0)\n", "\n", "    item_inventory_df = get_available_inventory(item_list, wh_outlet_id)\n", "\n", "    item_inventory_df.drop(columns=[\"outlet_id\", \"inventory\", \"blocked_inventory\"], inplace=True)\n", "\n", "    # Renaming the 'inv' column to 'actual_quantity'\n", "    item_inventory_df.rename(columns={\"inv\": \"actual_quantity\"}, inplace=True)\n", "    item_inventory_df = pd.merge(\n", "        item_inventory_df,\n", "        indent_data[[\"aligned_vendor_id\", \"group_id\", \"item_id\"]],\n", "        how=\"left\",\n", "        on=\"item_id\",\n", "    )\n", "    item_inventory_df[\"actual_quantity\"] = item_inventory_df[\"actual_quantity\"].fillna(0)\n", "    current_inventory = pd.DataFrame()\n", "    current_inventory = item_inventory_df\n", "    pending_put = get_pending_putaway(wh_outlet_id)\n", "    open_sto_quantity = get_open_sto_quantity(item_list, wh_outlet_id)\n", "\n", "    open_sto_quantity = pd.merge(\n", "        open_sto_quantity,\n", "        indent_data[[\"aligned_vendor_id\", \"group_id\", \"item_id\"]],\n", "        how=\"left\",\n", "        on=\"item_id\",\n", "    )\n", "    print(\"I am here1\")\n", "    aggregate_demand.rename(columns={\"total_net_demand\": \"demand\"}, inplace=True)\n", "    indent_data_1 = indent_data\n", "    # indent_data_1.to_csv('indent_data_1.csv')\n", "\n", "    current_date = datetime.now(IST)\n", "    end_date = current_date.strftime(\"%Y-%m-%d\")\n", "    start_date = (current_date - timed<PERSON>ta(days=60)).strftime(\"%Y-%m-%d\")\n", "    print(\"going ahead1\")\n", "    dfcc7_output = process_vendor_data(\n", "        start_date,\n", "        end_date,\n", "        facility_id,\n", "        indent_data,\n", "        outlet_data,\n", "        sale_data,\n", "        sale_data_1,\n", "        po_data,\n", "        open_po,\n", "        open_sto_quantity,\n", "        current_inventory,\n", "        holding_quantity,\n", "        indent_data_1,\n", "        forecast_cpd_1,\n", "        forecast_cpd_new,\n", "        ds_quantity_effective,\n", "        item_active_ds_count,\n", "        grouped_stats,\n", "        aggregate_demand,\n", "        holding_quantity_2,\n", "        pending_put,\n", "        number_of_unique_outlets,\n", "        min_qmax,\n", "    )\n", "\n", "    result_df = pd.DataFrame()\n", "    result_df = result_df.append(dfcc7_output, ignore_index=True)\n", "    result_df.drop_duplicates(subset=\"item_id\", keep=\"first\", inplace=True)\n", "    result_df = result_df.drop_duplicates()\n", "    df = result_df\n", "    aggregated_results = aggregate_data(df)\n", "    merged_df = post_po_calculation(aggregated_results, df)\n", "    return merged_df"]}, {"cell_type": "code", "execution_count": null, "id": "0a83abe6-c1c3-4d83-b121-96a130b99a31", "metadata": {"tags": []}, "outputs": [], "source": ["# # facility_ids = [1983, 603, 2078,2469,2470,2076,1320,2006,2076,2576,2124,555,1872,264,2015, 43, 2123, 1873, 1206, 2010, 92, 2141, 2142]\n", "# facility_ids =[2469]\n", "# # facility_ids =[3213,3201,2123,2576,2469]\n", "# # 2469,555\n", "# # facility_ids=[2469,555,2576,2468,2670,2681,2569,2682,2946,2960,3127,3213,3201]\n", "# # facility_ids=[2141,1206,264,2010,1872,2015,43]\n", "\n", "\n", "# # facility_ids=[264,2076,1,2469,2010,2470,2960,2015,2682,3126,2141,2124,1206,3201,2947,3213,2670,2468,2569,3127,1873,555,1872,3200,2078,2576,2946,2142,2577,2681,603,2006,92,2123,43,1983]\n", "# # facility_ids=[2946,2960,2469,2576,555,2468,2670,2681,2569,2682,3127]\n", "# # facility_ids=[1873,2469,2010,2947,2142,1872,555,43,2078,2015,2470,264,92,2124,603,2123,1206,3127,2576,2960,1983,1,2141,2006,2681,2076,2682,2569,2670,3126,2468,2946]\n", "\n", "# # facility_ids=[2576,2468,2670,2681,2569,2682,2946,2960,3127,555]\n", "\n", "# # facility_ids=[264, 2010, 2123, 2015, 1873, 2142, 1872,3126]\n", "# # facility_ids = [264,1983,603,2078,2469,2470,2076,1320,2006,2076,2576,2124,555,1872,1320,2569,2670,2682,2947,2015,43,2123,1873,1206,2010,92,2141,2142]\n", "# current_date = datetime.now(IST)\n", "# end_date = current_date.strftime('%Y-%m-%d')\n", "# start_date = (current_date - timed<PERSON>ta(days=60)).strftime('%Y-%m-%d')\n", "# df_list = []\n", "# for fid in facility_ids:\n", "#     try:\n", "#         df = process_facility(fid)\n", "#         df_list.append(df)\n", "#     except Exception as e:\n", "#         print(f\"Error processing facility ID {fid}: {e}\")\n", "# final_df = pd.concat(df_list, ignore_index=True) if df_list else pd.DataFrame()\n", "# final_df.to_csv('Facilities_PO.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "55decb3f-69e8-4734-8e81-72de8f29d875", "metadata": {}, "outputs": [], "source": ["######\n", "### computation begin here\n", "#######"]}, {"cell_type": "code", "execution_count": null, "id": "4a046ec0-9a6b-42b0-87d2-f4ad101acc19", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f08ae619-f7f8-4f09-a573-bd32f4c6ae8b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b524187-588f-4f46-83c5-0dd5cf05089a", "metadata": {"tags": []}, "outputs": [], "source": ["current_date = datetime.now(IST)\n", "end_date = current_date.strftime(\"%Y-%m-%d\")\n", "start_date = (current_date - timed<PERSON>ta(days=60)).strftime(\"%Y-%m-%d\")\n", "df_list = []\n", "for fid in facility_ids:\n", "    try:\n", "        df = process_facility(fid)\n", "        df_list.append(df)\n", "    except Exception as e:\n", "        print(f\"Error processing facility ID {fid}: {e}\")\n", "final_df = pd.concat(df_list, ignore_index=True) if df_list else pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "59c6b379-6dc1-45a4-8ff6-cf7372a96d34", "metadata": {}, "outputs": [], "source": ["def clean_column_names(df):\n", "    \"\"\"Cleans column names to ensure SQL compatibility.\"\"\"\n", "    df.columns = (\n", "        df.columns.str.replace(\n", "            r\"[^\\w]\", \"_\", regex=True\n", "        )  # Replace special characters with underscores\n", "        .str.replace(\n", "            r\"__+\", \"_\", regex=True\n", "        )  # Replace multiple underscores with a single underscore\n", "        .str.strip(\"_\")  # Remove leading/trailing underscores\n", "        .str.lower()  # Convert to lowercase\n", "    )\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "0c51db05-80d1-4b5f-840c-216ae933fd91", "metadata": {}, "outputs": [], "source": ["def adjust_column_types(df):\n", "    \"\"\"Adjusts column types to ensure SQL compatibility.\"\"\"\n", "    for col in df.columns:\n", "        if df[col].dtype == \"object\":\n", "            try:\n", "                df[col] = pd.to_numeric(df[col], errors=\"coerce\").fillna(df[col])\n", "            except ValueError:\n", "                df[col] = df[col].astype(str)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "76633242-63a1-4541-bbc8-81ae18b9e12d", "metadata": {}, "outputs": [], "source": ["final_df = clean_column_names(final_df)\n", "final_df = adjust_column_types(final_df)"]}, {"cell_type": "code", "execution_count": null, "id": "64f150e2-cc9f-46a1-b9b7-a6b4ded14249", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0342a999-e622-433c-9273-c3c9c4813a33", "metadata": {}, "outputs": [], "source": ["final_df[\"run_date\"] = final_df[\"date\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ef08b248-925f-4544-8ad7-7235be5d1742", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4d991dd-72c0-46b7-bb66-74ba9b1fbd8e", "metadata": {}, "outputs": [], "source": ["def pushD<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    df,\n", "    tableName,\n", "    description=None,\n", "    primaryKeys=[\"insert_ds_ist\"],\n", "    load_type=\"upsert\",\n", "    environ=\"playground\",\n", "    partition_key=[],\n", "):\n", "    from datetime import datetime, timezone, timedelta\n", "    import time\n", "    import pandas as pd\n", "\n", "    utc_now = datetime.now(timezone.utc)\n", "    ist_now = utc_now + <PERSON><PERSON><PERSON>(hours=5, minutes=30)\n", "    formatted_date = ist_now.strftime(\"%Y-%m-%d\")\n", "    df[\"insert_ds_ist\"] = formatted_date\n", "    df[\"updated_at\"] = pd.to_datetime(utc_now)\n", "    column_dtypes = [\n", "        {\n", "            \"name\": col,\n", "            \"type\": str(df[col].dtype)\n", "            .replace(\"int64\", \"integer\")\n", "            .replace(\"float64\", \"real\")\n", "            .replace(\"str\", \"varchar\")\n", "            .replace(\"datetime64[ns]\", \"timestamp(6)\")\n", "            .replace(\"object\", \"varchar\")\n", "            .replace(\"bool\", \"boolean\")\n", "            .replace(\"datetime64[ns, UTC]\", \"timestamp(6)\"),\n", "            \"description\": col,\n", "        }\n", "        for col in df.columns\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": environ,\n", "        \"table_name\": tableN<PERSON>,\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": primary<PERSON><PERSON><PERSON>,\n", "        \"load_type\": load_type,\n", "        \"table_description\": (description if description else tableName),\n", "    }\n", "\n", "    if len(partition_key) > 0:\n", "        kwargs[\"partition_key\"] = partition_key\n", "\n", "    import pencilbox as pb\n", "\n", "    max_retries = 3  # adjust the number of retries as needed\n", "    retry_delay = 10  # seconds\n", "\n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            pb.to_trino(data_obj=df, **kwargs)\n", "            return\n", "        except Exception as e:\n", "            if attempt < max_retries:\n", "                print(f\"Error pushDfToTrino (attempt {attempt+1}/{max_retries}): {e}\")\n", "                time.sleep(retry_delay * (attempt + 1))\n", "            else:\n", "                print(f\"Failed to pushDfToTrino after {max_retries} attempts: {e}\")\n", "                return"]}, {"cell_type": "code", "execution_count": null, "id": "fa039a39-8dc9-4327-bd23-8a63af3e6295", "metadata": {}, "outputs": [], "source": ["def safe_stringify(val):\n", "    \"\"\"\n", "    Try converting 'val' to a string.\n", "    If it fails or is NaN, return 'ERROR_VALUE' as a placeholder.\n", "    \"\"\"\n", "    try:\n", "        if pd.isna(val):\n", "            return \"ERROR_VALUE\"\n", "        return str(val)\n", "    except Exception:\n", "        return \"ERROR_VALUE\"\n", "\n", "\n", "# 1) Apply safe_stringify row-by-row\n", "final_df[\"item_name\"] = final_df[\"item_name\"].apply(safe_stringify)\n", "\n", "# 2) Now every row in item_name is guaranteed to be a string or \"ERROR_VALUE\"\n", "#    so pushing to <PERSON><PERSON> won't fail with \"Expected bytes, got float\" error."]}, {"cell_type": "code", "execution_count": null, "id": "7941b1d8-e5d4-41ff-b7d0-8b54b385098e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5fec4d4a-693a-4ca0-a778-4e0491711dd9", "metadata": {}, "outputs": [], "source": ["pushDfToTrino(\n", "    final_df,\n", "    \"packaged_indent\",\n", "    description=\"indent report for packaged ordering\",\n", "    primaryKeys=[\"item_id\", \"facility_id\", \"run_date\"],\n", "    load_type=\"upsert\",\n", "    environ=\"supply_etls\",\n", "    partition_key=[\"run_date\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "08bb44f3-5713-420b-88b0-6f580bd268ad", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}