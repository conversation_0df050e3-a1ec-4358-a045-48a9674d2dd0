alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: any_facility_ordering
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: very-high-mem
    node_type: od
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06DDLK8PLJ
path: povms/facility_ordering/report/any_facility_ordering
paused: false
pool: priority_pool
project_name: facility_ordering
schedule:
  end_date: '2025-06-20T00:00:00'
  interval: 00 19 * * *
  start_date: '2025-06-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 22
