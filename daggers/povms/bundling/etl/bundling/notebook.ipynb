{"cells": [{"cell_type": "code", "execution_count": null, "id": "4310d6e0-3907-4818-b66e-78c34e996d22", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "0d1901a7-b0b0-446c-93bf-da44e943fea3", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "35a74789-ca3d-4b7c-98d0-d4e73d2d2cce", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4442ee0a-88a6-4bbb-80c1-9597b058c1e2", "metadata": {}, "outputs": [], "source": ["bundle_id = pb.from_sheets(\"1HuDU73b4OsSu8qUbYOxo3XI2KmwlkbsyD2pE2KxTLnk\", \"readme\")"]}, {"cell_type": "code", "execution_count": null, "id": "2b181cfe-056f-4055-af3e-5acc291e72d5", "metadata": {}, "outputs": [], "source": ["bundle_id[[\"bundle_id\", \"item_id\", \"active\"]] = (\n", "    bundle_id[[\"bundle_id\", \"item_id\", \"active\"]].fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "aeb929e9-e8e0-4c2f-9d21-4118d5b7e143", "metadata": {}, "outputs": [], "source": ["def upload_to_trino_table_wh_df(bundle_id):\n", "    data = bundle_id.copy()\n", "    from datetime import datetime, timedelta\n", "\n", "    kwargs_log = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"serialized_inventory_bundle_item_ids\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"bundle_id\", \"type\": \"integer\", \"description\": \"bundle_id\"},\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "            {\"name\": \"active\", \"type\": \"integer\", \"description\": \"active flag\"},\n", "        ],\n", "        \"primary_key\": [\"bundle_id\", \"item_id\"],\n", "        \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"bundle id associated with every item id\",\n", "    }\n", "\n", "    pb.to_trino(data, **kwargs_log)"]}, {"cell_type": "code", "execution_count": null, "id": "cb456b19-88bb-461d-9db7-16ffbbfe9f6c", "metadata": {}, "outputs": [], "source": ["upload_to_trino_table_wh_df(bundle_id)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}