alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: bundling
dag_type: etl
escalation_priority: low
execution_timeout: 240
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07DBKX9KA9
path: povms/bundling/etl/bundling
paused: false
pool: povms_pool
project_name: bundling
schedule:
  end_date: '2025-07-08T00:00:00'
  interval: 5 17 * * *
  start_date: '2025-04-09T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
