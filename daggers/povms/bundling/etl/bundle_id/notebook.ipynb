{"cells": [{"cell_type": "code", "execution_count": null, "id": "58578221-5d7c-42c2-9671-8406b37b9239", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "7c184c3e-ca7a-4370-8aba-cd204e80b35a", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a66f7f0b-a318-4d9d-ae13-0e92fe195d34", "metadata": {}, "outputs": [], "source": ["# bundle_id = pd.read_csv('bundle_id.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "46e0ccc4-faad-4960-acb7-ca2eafd2bc2e", "metadata": {}, "outputs": [], "source": ["bundle_id = pb.from_sheets(\"1HuDU73b4OsSu8qUbYOxo3XI2KmwlkbsyD2pE2KxTLnk\", \"readme\")"]}, {"cell_type": "code", "execution_count": null, "id": "0a26f7f9-f09f-4206-9701-75b7921dbb1a", "metadata": {}, "outputs": [], "source": ["bundle_id[[\"bundle_id\", \"item_id\", \"active\"]] = (\n", "    bundle_id[[\"bundle_id\", \"item_id\", \"active\"]].fillna(0).astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5df8c727-9257-4def-963c-05f7c2bee7e8", "metadata": {}, "outputs": [], "source": ["def upload_to_trino_table_wh_df(bundle_id):\n", "    data = bundle_id.copy()\n", "    from datetime import datetime, timedelta\n", "\n", "    kwargs_log = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"serialized_inv_bundle_ids\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"bundle_id\", \"type\": \"integer\", \"description\": \"bundle_id\"},\n", "            {\"name\": \"bundle_name\", \"type\": \"varchar\", \"description\": \"bundle_name\"},\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "            {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "            {\"name\": \"active\", \"type\": \"integer\", \"description\": \"active flag\"},\n", "        ],\n", "        \"primary_key\": [\"bundle_id\", \"item_id\"],\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"bundle id associated with every item id\",\n", "    }\n", "\n", "    pb.to_trino(data, **kwargs_log)"]}, {"cell_type": "code", "execution_count": null, "id": "9ec37c90-f3e0-4d30-ad92-59ff39c23271", "metadata": {}, "outputs": [], "source": ["upload_to_trino_table_wh_df(bundle_id)"]}, {"cell_type": "code", "execution_count": null, "id": "f6694249-44d4-4e5c-bcf3-674a02474b3c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}