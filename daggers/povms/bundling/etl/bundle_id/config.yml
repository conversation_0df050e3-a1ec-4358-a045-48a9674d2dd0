alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: bundle_id
dag_type: etl
escalation_priority: low
execution_timeout: 600
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07DBKX9KA9
path: povms/bundling/etl/bundle_id
paused: true
pool: povms_pool
project_name: bundling
schedule:
  end_date: '2025-06-04T00:00:00'
  interval: 5 4 * * *
  start_date: '2025-03-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
