{"cells": [{"cell_type": "code", "execution_count": null, "id": "1d3f6d99-a704-4ed3-8c89-7f33ccc9799a", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "import io\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "start_time = time.time()\n", "\n", "!pip install pytz\n", "import pytz\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "d0c108f1-d7f8-479a-bbae-3d41657b20be", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ChQPlQ8AQTQ0FHzTHUpeM96yQqtWaXrBFw5qCWW7p0g\"\n", "sheet_details = \"IB/OB Plan\"\n", "\n", "df = pb.from_sheets(sheet_id, sheet_details)\n", "df = df[\n", "    [\n", "        \"City\",\n", "        \"Outlet Id\",\n", "        \"Outlet Name\",\n", "        \"Store Type for Marketing/Assortment/Other Initiatives\",\n", "        \"Full Address\",\n", "        \"OB Tentative\",\n", "        \"OB Final Date\",\n", "        \"Replacement/Sister Store Facillity ID\",\n", "        \"Replacement/Sister Store Name\",\n", "        \"Mode of ops\",\n", "    ]\n", "]\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")\n", "current_date_ist = datetime.now(ist).date()\n", "\n", "\n", "def extract_date(date_str):\n", "    try:\n", "        return pd.to_datetime(date_str, errors=\"coerce\")\n", "    except:\n", "        return None\n", "\n", "\n", "# Apply conditions to create the 'final_ob_date' column\n", "df[\"final_ob_date\"] = df[\"OB Final Date\"].apply(\n", "    lambda x: extract_date(x) if extract_date(x) is not pd.NaT else np.nan\n", ")\n", "\n", "df[\"final_ob_date\"].fillna(\n", "    df[\"OB Tentative\"].apply(\n", "        lambda x: extract_date(x) if extract_date(x) is not pd.NaT else np.nan\n", "    ),\n", "    inplace=True,\n", ")\n", "df[\"final_ob_date\"] = pd.to_datetime(df[\"final_ob_date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "4af16e26-4cb7-4a04-8973-36571900511f", "metadata": {}, "outputs": [], "source": ["# Print the resulting DataFrame\n", "comparison_date = pd.to_datetime(\"2024-01-01\")\n", "filtered_df = df[df[\"final_ob_date\"] > comparison_date]\n", "filtered_df = filtered_df.sort_values(by=\"final_ob_date\", ascending=True)\n", "filtered_df = filtered_df.rename(\n", "    columns={\n", "        \"City\": \"zone\",\n", "        \"Outlet Id\": \"outlet_id\",\n", "        \"Outlet Name\": \"facility_name\",\n", "        \"Full Address\": \"address\",\n", "        \"Store Type for Marketing/Assortment/Other Initiatives\": \"store_type\",\n", "        \"Replacement/Sister Store Facillity ID\": \"sister_store_facility_id\",\n", "        \"Replacement/Sister Store Name\": \"sister_store_name\",\n", "        \"Mode of ops\": \"ownership_type\",\n", "    }\n", ")\n", "filtered_df[\"outlet_id\"] = filtered_df[\"outlet_id\"].astype(int)\n", "outlet_list = tuple(filtered_df[\"outlet_id\"].astype(int))\n", "\n", "\n", "def facility_details(outlet_list):\n", "    sql_ds = f\"\"\"\n", "            select id as outlet_id, facility_id from lake_retail.console_outlet\n", "            where id in {outlet_list}\n", "            \"\"\"\n", "    return pd.read_sql_query(sql=sql_ds, con=CON_TRINO)\n", "\n", "\n", "facility_details = facility_details(outlet_list)\n", "\n", "filtered_df = pd.merge(filtered_df, facility_details, how=\"left\", on=\"outlet_id\")\n", "\n", "\n", "filtered_df[\"updated_at\"] = datetime.today() + timedelta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "id": "4889978f-4ea5-4e24-b75a-a1efdc382399", "metadata": {}, "outputs": [], "source": ["filtered_df = filtered_df[\n", "    [\n", "        \"zone\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"store_type\",\n", "        \"address\",\n", "        \"final_ob_date\",\n", "        \"updated_at\",\n", "        \"sister_store_facility_id\",\n", "        \"sister_store_name\",\n", "        \"ownership_type\",\n", "    ]\n", "]\n", "\n", "filtered_df[\"sister_store_facility_id\"] = np.where(\n", "    (filtered_df[\"sister_store_facility_id\"] == \"\")\n", "    | (filtered_df[\"sister_store_facility_id\"].isna()),\n", "    0,\n", "    filtered_df[\"sister_store_facility_id\"],\n", ")\n", "\n", "\n", "filtered_df[\"sister_store_facility_id\"] = filtered_df[\"sister_store_facility_id\"].astype(int)\n", "\n", "filtered_df[\"sister_store_name\"] = filtered_df[\"sister_store_name\"].astype(str)\n", "filtered_df[\"ownership_type\"] = filtered_df[\"ownership_type\"].astype(str)\n", "\n", "filtered_df[\"store_status\"] = \"a\"\n", "filtered_df[\"new_city_flag\"] = True\n", "\n", "filtered_df[\"store_status\"] = filtered_df[\"store_status\"].astype(str)\n", "filtered_df[\"new_city_flag\"] = filtered_df[\"new_city_flag\"].astype(bool)"]}, {"cell_type": "code", "execution_count": null, "id": "9ff53700-35fc-4227-8361-7156ae1634a3", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    {\"name\": \"zone\", \"type\": \"VARCHAR\", \"description\": \"Zone\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Outlet ID\"},\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "    {\"name\": \"facility_name\", \"type\": \"VARCHAR\", \"description\": \"Facility Name\"},\n", "    {\"name\": \"store_type\", \"type\": \"VARCHAR\", \"description\": \"Store Type\"},\n", "    {\"name\": \"address\", \"type\": \"VARCHAR\", \"description\": \"Address\"},\n", "    {\n", "        \"name\": \"final_ob_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"Final Outbound Date\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Updated at\"},\n", "    {\n", "        \"name\": \"sister_store_facility_id\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"Sister Store Facility ID\",\n", "    },\n", "    {\n", "        \"name\": \"sister_store_name\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"Sister Store Name\",\n", "    },\n", "    {\n", "        \"name\": \"ownership_type\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"Ownership of the store\",\n", "    },\n", "    {\n", "        \"name\": \"store_status\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"Ownership of the store\",\n", "    },\n", "    {\n", "        \"name\": \"new_city_flag\",\n", "        \"type\": \"BOOLEAN\",\n", "        \"description\": \"Ownership of the store\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"e3_new_darkstores\",\n", "    \"column_dtypes\": columns,\n", "    \"primary_key\": [\"outlet_id\", \"facility_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"Details of Live Stores as per E3 Go Live Tracker\",  # Description of the table being sent to redshift\n", "}\n", "pb.to_trino(filtered_df, **kwargs)\n", "\n", "import datetime\n", "\n", "current_date_str = datetime.datetime.now().strftime(\"%Y-%m-%d\")\n", "# Alert\n", "channel = \"table-updates-tanishq\"\n", "text_req = \"\\n <@U05CCTXLBU1> \\n New Store OB Date Table Updated for Today \" + current_date_str\n", "pb.send_slack_message(\n", "    channel=channel,\n", "    text=text_req,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "119cb9fc-4141-4d46-9780-e2822466c655", "metadata": {}, "outputs": [], "source": ["# Backfiling the data\n", "\n", "# sheet_id = \"1JL98SjNb3YN0VxKkgwJpDYDdyYUK8-tEz2rIIW7FAgM\"\n", "# sheet_details = \"Sheet1\"\n", "\n", "# df = pb.from_sheets(sheet_id, sheet_details)\n", "\n", "# ist = pytz.timezone(\"Asia/Kolkata\")\n", "# current_date_ist = datetime.now(ist).date()\n", "\n", "\n", "# def extract_date(date_str):\n", "#     try:\n", "#         return pd.to_datetime(date_str, errors=\"coerce\")\n", "#     except:\n", "#         return None\n", "\n", "\n", "# df[\"open_date\"] = df[\"open_date\"].apply(\n", "#     lambda x: extract_date(x) if extract_date(x) is not pd.NaT else np.nan\n", "# )\n", "\n", "# df[\"open_date\"] = pd.to_datetime(df[\"open_date\"]).dt.date\n", "\n", "\n", "# df = df.rename(columns={\"open_date\": \"final_ob_date\"})\n", "\n", "# filtered_df = df.copy()\n", "\n", "\n", "# filtered_df = filtered_df[\n", "#     [\n", "#         \"zone\",\n", "#         \"outlet_id\",\n", "#         \"facility_id\",\n", "#         \"facility_name\",\n", "#         \"store_type\",\n", "#         \"address\",\n", "#         \"final_ob_date\",\n", "#     ]\n", "# ]\n", "# filtered_df[\"updated_at\"] = datetime.today() + timedelta(hours=5.5)\n", "\n", "# filtered_df[\"final_ob_date\"] = pd.to_datetime(filtered_df[\"final_ob_date\"]).dt.date\n", "\n", "# filtered_df[\"outlet_id\"] = filtered_df[\"outlet_id\"].astype(int)\n", "\n", "# filtered_df[\"facility_id\"] = filtered_df[\"facility_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "e8d9e480-82ce-4dbc-9bb8-6594bdc2b64e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}