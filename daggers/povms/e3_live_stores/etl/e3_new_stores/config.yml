alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: e3_new_stores
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U05CCTXLBU1
path: povms/e3_live_stores/etl/e3_new_stores
paused: false
pool: povms_pool
project_name: e3_live_stores
schedule:
  end_date: '2025-06-30T00:00:00'
  interval: 30 0,4,10,14 * * *
  start_date: '2025-04-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 17
