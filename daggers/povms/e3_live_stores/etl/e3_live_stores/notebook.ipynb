{"cells": [{"cell_type": "code", "execution_count": null, "id": "b8ba7bf0-0f60-4806-b8f8-08ce6ddf73a0", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "import io\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "653e696b-8a3f-4288-bc41-ac965111cfa4", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ChQPlQ8AQTQ0FHzTHUpeM96yQqtWaXrBFw5qCWW7p0g\"\n", "sheet_name = \"LIVE_Stores_Master\"\n", "e3_data = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "ffc63855-e636-48b2-b37f-93dc4061efed", "metadata": {}, "outputs": [], "source": ["list(e3_data)"]}, {"cell_type": "code", "execution_count": null, "id": "ba62279d-e42b-47b7-8083-f7b8d437cbe5", "metadata": {}, "outputs": [], "source": ["e3_data = e3_data[\n", "    [\n", "        \"City\",\n", "        \"Retail Outlet Id\",\n", "        \"Facility ID\",\n", "        \"Express SS Name (Outlet Name)\",\n", "        \"Live Date\",\n", "        \"Mode of Ops\",\n", "        \"Address\",\n", "        \"Lat/Long\",\n", "        \"Cold Room\",\n", "    ]\n", "].sort_values(by=[\"City\"])"]}, {"cell_type": "code", "execution_count": null, "id": "07f29109-d558-4cab-a588-ad6be52ea1cc", "metadata": {}, "outputs": [], "source": ["e3_data = e3_data.rename(\n", "    columns={\n", "        \"City\": \"zone\",\n", "        \"Retail Outlet Id\": \"outlet_id\",\n", "        \"Facility ID\": \"facility_id\",\n", "        \"Express SS Name (Outlet Name)\": \"facility_name\",\n", "        \"Live Date\": \"go_live_date\",\n", "        \"Mode of Ops\": \"ownership_type\",\n", "        \"Address\": \"address\",\n", "        \"Lat/Long\": \"ds_coordinates\",\n", "        \"Cold Room\": \"cold_room_status\",\n", "    }\n", ")\n", "e3_data[\"outlet_id\"] = e3_data[\"outlet_id\"].astype(int)\n", "e3_data[\"facility_id\"] = e3_data[\"facility_id\"].astype(int)\n", "e3_data[\"cold_room_status\"] = e3_data[\"cold_room_status\"].fillna(\"-\")\n", "e3_data"]}, {"cell_type": "code", "execution_count": null, "id": "a784fe7f-8fbf-4c86-83b9-9cefa803d46b", "metadata": {}, "outputs": [], "source": ["outlet_list = tuple(e3_data[\"outlet_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "id": "ce3ea179-ee70-4a0e-9754-4c0043d8b3ba", "metadata": {}, "outputs": [], "source": ["def active_check(outlet_list):\n", "    sql = f\"\"\"\n", "        with fo as (select facility_id, pos_outlet_id as outlet_id, frontend_merchant_id, pos_outlet_city_name as city from dwh.dim_merchant_outlet_facility_mapping\n", "        where facility_id in (select facility_id from po.physical_facility_outlet_mapping where active=1 and ars_active=1)\n", "            and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "            and is_current = true\n", "            )\n", "        select fo.outlet_id,count(distinct order_id) as orders from dwh.fact_sales_order_details a\n", "        join fo on  a.frontend_merchant_id = fo.frontend_merchant_id\n", "        where order_current_status = 'DELIVERED'\n", "        and cart_checkout_ts_ist > current_date- interval '5' day\n", "        and order_create_dt_ist > current_date- interval '5' day \n", "        and fo.outlet_id in {outlet_list}\n", "        group by 1\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=CON_TRINO)\n", "\n", "\n", "df_active_check = active_check(outlet_list)\n", "df_active_check"]}, {"cell_type": "code", "execution_count": null, "id": "742b1b8e-9879-46e5-b544-ef16a58352cb", "metadata": {}, "outputs": [], "source": ["df_active_check[df_active_check[\"outlet_id\"] == 2340]"]}, {"cell_type": "code", "execution_count": null, "id": "44ac81bb-eb9d-4c34-905f-5a81d27e5d9e", "metadata": {}, "outputs": [], "source": ["df_active_check[\"active\"] = np.where(df_active_check[\"orders\"] > 0, 1, 0)\n", "df_active_check = df_active_check[[\"outlet_id\", \"active\"]]\n", "df_active_check[\"active\"] = df_active_check[\"active\"].astype(int)\n", "df_active_check"]}, {"cell_type": "code", "execution_count": null, "id": "c0dc83c4-e3b4-41dc-bc03-7cd6bc945916", "metadata": {}, "outputs": [], "source": ["df_e3 = pd.merge(e3_data, df_active_check, how=\"left\", on=[\"outlet_id\"])\n", "df_e3[\"go_live_date\"] = pd.to_datetime(df_e3[\"go_live_date\"])\n", "df_e3"]}, {"cell_type": "code", "execution_count": null, "id": "8a4fb4d1-8b2c-45c6-9d2b-0dfc1f9f40bf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "48e07634-3281-41b7-9300-aaec11f2b9d3", "metadata": {}, "outputs": [], "source": ["df_e3[\"updated_at\"] = datetime.today() + timedelta(hours=5.5)\n", "df_e3[\"active\"] = df_e3[\"active\"].fillna(0)\n", "df_e3[\"active\"] = df_e3[\"active\"].astype(int)\n", "live_stores_data = df_e3.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "4777e3b2-774b-44a9-b1e5-17d13c2989f1", "metadata": {}, "outputs": [], "source": ["live_stores_data"]}, {"cell_type": "markdown", "id": "baf45615-dca7-467d-abde-1b5314254481", "metadata": {}, "source": ["Upload Data to Trino"]}, {"cell_type": "code", "execution_count": null, "id": "9e59be8f-25d9-4a0b-9a3b-f745a6f89923", "metadata": {}, "outputs": [], "source": ["live_stores_data_trino = live_stores_data[\n", "    [\n", "        \"zone\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"go_live_date\",\n", "        \"ownership_type\",\n", "        \"address\",\n", "        \"ds_coordinates\",\n", "        \"active\",\n", "        \"updated_at\",\n", "        \"cold_room_status\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "deb805db-1712-428c-9360-d0831101ef17", "metadata": {}, "outputs": [], "source": ["# Creating Table for logs\n", "try:\n", "    columns = [\n", "        {\"name\": \"zone\", \"type\": \"VARCHAR\", \"description\": \"City Name\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Outlet ID\"},\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "        {\"name\": \"facility_name\", \"type\": \"VARCHAR\", \"description\": \"Facility Name\"},\n", "        {\n", "            \"name\": \"go_live_date\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"Store Live Date\",\n", "        },\n", "        {\"name\": \"ownership_type\", \"type\": \"VARCHAR\", \"description\": \"Ownership\"},\n", "        {\"name\": \"address\", \"type\": \"VARCHAR\", \"description\": \"Address\"},\n", "        {\n", "            \"name\": \"ds_coordinates\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Store Live Date\",\n", "        },\n", "        {\n", "            \"name\": \"active\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Stores having sales in last 2 days\",\n", "        },\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Address\"},\n", "        {\n", "            \"name\": \"cold_room_status\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Cold Room Status\",\n", "        },\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"e3_live_darkstores\",\n", "        \"column_dtypes\": columns,\n", "        \"primary_key\": [\"outlet_id\", \"facility_id\"],\n", "        \"sortkey\": [\"outlet_id\", \"facility_id\", \"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"Details of Live Stores as per E3 Go Live Tracker\",  # Description of the table being sent to redshift\n", "    }\n", "    pb.to_trino(live_stores_data_trino, **kwargs)\n", "except:\n", "    print(\"trino not updated\")"]}, {"cell_type": "markdown", "id": "59f77fb5-af1f-4823-b1e4-f946f1feef55", "metadata": {}, "source": ["*** New Stores***\n"]}, {"cell_type": "code", "execution_count": null, "id": "dae32979-a53e-46f3-b458-fd171c33daab", "metadata": {}, "outputs": [], "source": ["!pip install pytz\n", "import pytz\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "e7fd8aea-7168-485a-b185-4f7980d19be2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ChQPlQ8AQTQ0FHzTHUpeM96yQqtWaXrBFw5qCWW7p0g\"\n", "sheet_details = \"IB/OB Plan\"\n", "\n", "df = pb.from_sheets(sheet_id, sheet_details)\n", "df = df[\n", "    [\n", "        \"City\",\n", "        \"Outlet Id\",\n", "        \"Outlet Name\",\n", "        \"Full Address\",\n", "        \"OB Tentative\",\n", "        \"OB Final Date\",\n", "    ]\n", "]\n", "\n", "ist = pytz.timezone(\"Asia/Kolkata\")\n", "current_date_ist = datetime.now(ist).date()\n", "\n", "\n", "def extract_date(date_str):\n", "    try:\n", "        return pd.to_datetime(date_str, errors=\"coerce\")\n", "    except:\n", "        return None\n", "\n", "\n", "# Apply conditions to create the 'final_ob_date' column\n", "df[\"final_ob_date\"] = df[\"OB Final Date\"].apply(\n", "    lambda x: extract_date(x) if extract_date(x) is not pd.NaT else np.nan\n", ")\n", "df[\"final_ob_date\"].fillna(\n", "    df[\"OB Tentative\"].apply(\n", "        lambda x: extract_date(x) if extract_date(x) is not pd.NaT else np.nan\n", "    ),\n", "    inplace=True,\n", ")\n", "df[\"final_ob_date\"] = pd.to_datetime(df[\"final_ob_date\"]).dt.date\n", "# Print the resulting DataFrame\n", "filtered_df = df[df[\"final_ob_date\"] > current_date_ist]\n", "filtered_df = filtered_df.sort_values(by=\"final_ob_date\", ascending=True)\n", "filtered_df = filtered_df.rename(\n", "    columns={\n", "        \"City\": \"zone\",\n", "        \"Outlet Id\": \"outlet_id\",\n", "        \"Outlet Name\": \"facility_name\",\n", "        \"Full Address\": \"address\",\n", "    }\n", ")\n", "filtered_df[\"outlet_id\"] = filtered_df[\"outlet_id\"].astype(int)\n", "outlet_list = tuple(filtered_df[\"outlet_id\"].astype(int))\n", "\n", "\n", "def facility_details(outlet_list):\n", "    sql_ds = f\"\"\"\n", "            select id as outlet_id, facility_id from lake_retail.console_outlet\n", "            where id in {outlet_list}\n", "            \"\"\"\n", "    return pd.read_sql_query(sql=sql_ds, con=CON_TRINO)\n", "\n", "\n", "facility_details = facility_details(outlet_list)\n", "\n", "filtered_df = pd.merge(filtered_df, facility_details, how=\"left\", on=\"outlet_id\")\n", "\n", "filtered_df = filtered_df[\n", "    [\"zone\", \"outlet_id\", \"facility_id\", \"facility_name\", \"address\", \"final_ob_date\"]\n", "]\n", "filtered_df[\"updated_at\"] = datetime.today() + timedelta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "id": "fd589f85-2f5e-445d-8e9e-5d51a02c4db8", "metadata": {}, "outputs": [], "source": ["columns = [\n", "    {\"name\": \"zone\", \"type\": \"VARCHAR\", \"description\": \"Zone\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Outlet ID\"},\n", "    {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "    {\"name\": \"facility_name\", \"type\": \"VARCHAR\", \"description\": \"Facility Name\"},\n", "    {\"name\": \"address\", \"type\": \"VARCHAR\", \"description\": \"Address\"},\n", "    {\n", "        \"name\": \"final_ob_date\",\n", "        \"type\": \"DATE\",\n", "        \"description\": \"Final Outbound Date\",\n", "    },\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Updated at\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"e3_upcoming_darkstores\",\n", "    \"column_dtypes\": columns,\n", "    \"primary_key\": [\"outlet_id\", \"facility_id\"],\n", "    \"sortkey\": [\"outlet_id\", \"facility_id\", \"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"table_description\": \"Details of Live Stores as per E3 Go Live Tracker\",  # Description of the table being sent to redshift\n", "}\n", "pb.to_trino(filtered_df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}