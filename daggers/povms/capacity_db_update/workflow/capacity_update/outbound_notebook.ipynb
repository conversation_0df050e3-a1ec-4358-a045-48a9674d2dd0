{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import datetime\n", "import numpy as np\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TODAY = datetime.datetime.today() + datetime.timedelta(hours=5.5)\n", "current_year = TODAY.year\n", "current_month = TODAY.month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime, calendar\n", "\n", "year = current_year\n", "month = current_month\n", "num_days = calendar.monthrange(year, month)[1]\n", "\n", "date_list = []\n", "for i in range(num_days + 1):\n", "    if i != 0:\n", "        x = datetime.date(year, month, i)\n", "        x = x.strftime(\"%m-%d-%y\")\n", "        date_list.append(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# date_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1unpM2R3gWWm9JNvOFoC1JF55x11wR-BaApJlyMeFc8o\"\n", "sheet_name = \"Outbound Capacity\"\n", "frame1 = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "frame1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["header_row = 0\n", "\n", "frame1.columns = frame1.iloc[header_row]\n", "frame1 = frame1.drop(header_row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame1[\"Facility\"].replace({\"\": np.nan}, inplace=True)\n", "frame1.head(20)\n", "frame1[\"Facility\"].ffill(axis=0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame2 = frame1[~frame1[\"Facility\"].isin([\"Facility\", \"Total\", \"\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame3 = frame2.melt(\n", "    id_vars=[\"Facility\", \"Facility Name\", \"Capacity Tagging\"],\n", "    var_name=\"Date\",\n", "    value_name=\"Value\",\n", ")\n", "frame3 = frame3[frame3[\"Date\"] != \"Facility\"]\n", "frame3 = frame3[frame3[\"Date\"] != \"\"]\n", "frame3 = frame3[frame3[\"Date\"] != \"Total Capacity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame3[\"facility_id\"] = frame3[\"Facility\"]\n", "# frame3[\"Facility Name\"].str.split(\"-\", 1).str[0]\n", "frame3[\"facility_name\"] = frame3[\"Facility Name\"]\n", "# frame3[\"Facility Name\"].str.split(\"-\", 1).str[1]\n", "frame3.rename(\n", "    columns={\n", "        \"Value\": \"transfer_capacity\",\n", "        \"Capacity Tagging\": \"store_type\",\n", "        \"Date\": \"date\",\n", "    },\n", "    inplace=True,\n", ")\n", "frame4 = frame3[[\"facility_id\", \"facility_name\", \"store_type\", \"date\", \"transfer_capacity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame4.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = frame4[[\"facility_id\", \"store_type\", \"date\", \"transfer_capacity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "\n", "final[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"transfer_capacity\"] = np.where(\n", "    (final[\"transfer_capacity\"].isna()) | (final[\"transfer_capacity\"] == \"\"),\n", "    0,\n", "    final[\"transfer_capacity\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[[\"facility_id\", \"transfer_capacity\"]] = final[[\"facility_id\", \"transfer_capacity\"]].astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"date\"] = final[\"date\"].apply(\n", "    lambda x: x + \"-\" + str((datetime.today() + timedelta(hours=5.5)).year)\n", ")\n", "final[\"date\"] = pd.to_datetime(final[\"date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"date\"] = pd.to_datetime(final[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# redshift=pb.get_connection(\"redshift\")\n", "\n", "# query=\"\"\"SELECT *\n", "# FROM metrics.backend_transfer_capacity\"\"\"\n", "# cap=pd.read_sql(query,redshift)\n", "# cap.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Flat table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# redshift_schema(cap)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"Facility id\"},\n", "    {\"name\": \"store_type\", \"type\": \"varchar\", \"description\": \"store type\"},\n", "    {\"name\": \"date\", \"type\": \"timestamp\", \"description\": \"Date\"},\n", "    {\"name\": \"transfer_capacity\", \"type\": \"int\", \"description\": \"Transfer Capacity\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"Table Updated at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def redshift_schema(df):\n", "#     metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "#     ref_dict = {\n", "#         \"int64\": \"int\",\n", "#         \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "#         \"datetime64[ns]\": \"timestamp\",\n", "#         \"object\": \"varchar(1000)\",\n", "#         \"float64\": \"float\",\n", "#     }\n", "#     return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"backend_transfer_capacity_v2\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"store_type\", \"date\"],\n", "    \"sortkey\": [\"date\", \"facility_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Facility Transfer capacity\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}