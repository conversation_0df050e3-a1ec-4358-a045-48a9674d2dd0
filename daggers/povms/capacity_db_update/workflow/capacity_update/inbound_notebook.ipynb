{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame1 = pb.from_sheets(\n", "    \"1unpM2R3gWWm9JNvOFoC1JF55x11wR-BaApJlyMeFc8o\", \"Inbound Capacity\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(frame1, \"167eZAE8HatOBMwKcYAzqgSSnI457Sxt58Pf7fwPszD4\", sheetname=\"Sheet2\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["##new addition\n", "\n", "frame1.iloc[0] = np.where(frame1.iloc[0] == \"\", frame1.columns, frame1.iloc[0])\n", "\n", "frame1.columns = frame1.iloc[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame2 = frame1[~frame1[\"Facility\"].isin([\"Facility\", \"Total\", \"\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame3 = frame2.melt(\n", "    id_vars=[\"Facility\", \"Facility Name\", \"STO/PO\"], var_name=\"Date\", value_name=\"Value\"\n", ")\n", "frame3 = frame3[frame3[\"Date\"] != \"Facility\"]\n", "frame3 = frame3[frame3[\"Date\"] != \"\"]\n", "frame3 = frame3[frame3[\"Date\"] != \"Total Capacity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame3.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame3[\"facility_id\"] = frame3[\"Facility\"]\n", "# frame3[\"Facility Name\"].str.split(\"-\", 1).str[0]\n", "frame3[\"facility_name\"] = frame3[\"Facility Name\"]\n", "# frame3[\"Facility Name\"].str.split(\"-\", 1).str[1]\n", "frame3.rename(\n", "    columns={\n", "        \"Value\": \"capacity\",\n", "        \"STO/PO\": \"transfer_type\",\n", "        \"Date\": \"date\",\n", "    },\n", "    inplace=True,\n", ")\n", "frame4 = frame3[[\"facility_id\", \"facility_name\", \"transfer_type\", \"date\", \"capacity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(\n", "#     frame4, \"167eZAE8HatOBMwKcYAzqgSSnI457Sxt58Pf7fwPszD4\", sheetname=\"ToDatabase\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame4.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = frame4[[\"facility_id\", \"transfer_type\", \"date\", \"capacity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[[\"facility_id\", \"capacity\"]] = final[[\"facility_id\", \"capacity\"]].astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"date\"] = final[\"date\"].apply(\n", "    lambda x: x + \"-\" + str((datetime.today() + timedelta(hours=5.5)).year)\n", ")\n", "final[\"date\"] = pd.to_datetime(final[\"date\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"date\"] = pd.to_datetime(final[\"date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Flat table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"Facility id\"},\n", "    {\"name\": \"transfer_type\", \"type\": \"varchar\", \"description\": \"PO/STO\"},\n", "    {\n", "        \"name\": \"date\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date for which capacity is uploaded\",\n", "    },\n", "    {\"name\": \"capacity\", \"type\": \"int\", \"description\": \"Inbound Capacity\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"Table Updated at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def redshift_schema(df):\n", "#     metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "#     ref_dict = {\n", "#         \"int64\": \"int\",\n", "#         \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "#         \"datetime64[ns]\": \"timestamp\",\n", "#         \"object\": \"varchar(1000)\",\n", "#         \"float64\": \"float\",\n", "#     }\n", "#     return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"facility_inbound_capacity\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"transfer_type\", \"date\"],\n", "    \"sortkey\": [\"facility_id\", \"transfer_type\", \"date\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Facility Inbound capacity\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "raw", "metadata": {}, "source": ["kwargs1 = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"facility_inbound_capacity_log\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"transfer_type\", \"date\", \"updated_at\"],\n", "    \"sortkey\": [\"facility_id\", \"transfer_type\", \"date\", \"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"Facility Inbound capacity\",\n", "}"]}, {"cell_type": "raw", "metadata": {}, "source": ["pb.to_redshift(final, **kwargs1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}