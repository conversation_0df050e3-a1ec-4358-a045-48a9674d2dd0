dag_name: capacity_update
dag_type: workflow
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebooks:
- alias: inbound_gs_update
  executor_config:
    load_type: tiny
    node_type: spot
  name: inbound_notebook
  parameters: null
  tag: parallel
- alias: outbound_redshift_update
  executor_config:
    load_type: tiny
    node_type: spot
  name: outbound_notebook
  parameters: null
  tag: parallel
- alias: inbound_sku_redshift_update
  executor_config:
    load_type: tiny
    node_type: spot
  name: inbound_sku_notebook
  parameters: null
  tag: parallel
owner:
  email: <EMAIL>
  slack_id: U03RR39TSTZ
path: povms/capacity_db_update/workflow/capacity_update
paused: true
pool: povms_pool
project_name: capacity_db_update
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 15 2 * * *
  start_date: '2023-02-01T06:42:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files: []
tags: []
template_name: multi_notebook
version: 11
concurrency: 3
