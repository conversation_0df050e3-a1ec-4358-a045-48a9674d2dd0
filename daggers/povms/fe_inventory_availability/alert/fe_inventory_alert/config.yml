alert_configs:
  slack:
  - channel: bl-invt-alerts
dag_name: fe_inventory_alert
dag_type: alert
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S74DEH35
path: povms/fe_inventory_availability/alert/fe_inventory_alert
paused: true
pool: povms_pool
project_name: fe_inventory_availability
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 3 * * *
  start_date: '2022-11-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
