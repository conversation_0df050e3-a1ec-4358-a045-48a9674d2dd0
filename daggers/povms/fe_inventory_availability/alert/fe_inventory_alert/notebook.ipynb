{"cells": [{"cell_type": "code", "execution_count": null, "id": "e4be25d5-8d7f-4087-98a9-5e1d9193cf53", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date\n", "from datetime import timedelta\n", "\n", "!pip install matplotlib\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "a107157d-7a6a-450f-a24b-f94313eeb5f4", "metadata": {}, "source": ["Query to fetch FE, BE availability and truncation details for STO indent "]}, {"cell_type": "code", "execution_count": null, "id": "82ece3f6-36cc-4923-a3d9-e7f4935f3c2f", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"with be as (select receiving_outlet_id,sender_outlet_id,co.facility_id as backend_facility_id from metrics.esto_details ed\n", "left join lake_retail.console_outlet co on co.id = ed.sender_outlet_id and co.active = 1\n", "where date(ed.sto_created_at) = current_date-1 and ed.ars_mode = 'normal' \n", "group by 1,2,3),\n", "\n", "truncation_data as (\n", "SELECT date(ars_started_at) as dt,\n", "       ZONE,\n", "       backend_facility_id,\n", "       frontend_facility_id,\n", "       frontend_outlet_id,\n", "       sum(v1_quantity) as v1_quantity, sum(v2_quantity) AS v2_quantity,\n", "       sum(v2_quantity_without_case) as v2_quantity_without_case,\n", "       sum(inward_drop)/sum(v1_quantity) AS inward_drop,\n", "       sum(storage_drop)/sum(v1_quantity) AS storage_drop,\n", "       sum(truck_load_drop)/sum(v1_quantity) AS truck_load_drop,\n", "       sum(picking_capacity_quantity_drop)/sum(v1_quantity) AS picking_capacity_quantity_drop\n", "       \n", "from metrics.ars_truncation_rca_l7_raw_data_v2\n", "where dt=current_date-1\n", "group by 1,2,3,4,5\n", "order by 6\n", "),\n", "\n", "past_truncation as (\n", "SELECT date(ars_started_at) as dt,\n", "       ZONE,\n", "       backend_facility_id,\n", "       frontend_facility_id,\n", "       frontend_outlet_id,\n", "       sum(v1_quantity) as v1_quantity, sum(v2_quantity) AS v2_quantity,\n", "       sum(v2_quantity_without_case) as v2_quantity_without_case,\n", "       sum(inward_drop)/sum(v1_quantity) AS inward_drop,\n", "       sum(storage_drop)/sum(v1_quantity) AS storage_drop,\n", "       sum(truck_load_drop)/sum(v1_quantity) AS truck_load_drop,\n", "       sum(picking_capacity_quantity_drop)/sum(v1_quantity) AS picking_capacity_quantity_drop\n", "       \n", "from metrics.ars_truncation_rca_l7_raw_data_v2\n", "where dt between current_date-3 and current_date-1\n", "group by 1,2,3,4,5\n", "order by 6\n", "),\n", "\n", "base as (\n", "select receiving_outlet_id,sender_outlet_id,be.backend_facility_id, bhw.backend_facility_name, bhw.cum_weighted_availability as weighted_availability,\n", "t.v1_quantity,t.inward_drop,t.storage_drop,\n", "t.truck_load_drop,t.picking_capacity_quantity_drop,avg(p.inward_drop) as past_inward,avg(p.storage_drop) as past_storage,avg(p.truck_load_drop) as past_truck,\n", "avg(p.picking_capacity_quantity_drop) as past_picking\n", "--(case when t.inward_drop<=0.01 and t.storage_drop <= 0.01 and  t.truck_load_drop <= 0.01 and t.picking_capacity_quantity_drop<0.01 then max() ) \n", "from be\n", "left join metrics.backend_hourly_weighted_availability_logs bhw on bhw.backend_facility_id = be.backend_facility_id\n", "left join truncation_data t on t.backend_facility_id = bhw.backend_facility_id and t.frontend_outlet_id = be.receiving_outlet_id\n", "left join past_truncation p on p.backend_facility_id = bhw.backend_facility_id and p.frontend_outlet_id = be.receiving_outlet_id\n", "where bhw.date_ = current_date-1 and bhw.hour in (20,21,22,23) and bhw.assortment_type = 'Packaged Goods'\n", "group by 1,2,3,4,5,6,7,8,9,10\n", "),\n", "\n", "final as (select shw.facility_id,receiving_outlet_id,shw.facility_name,--sender_outlet_id,\n", "base.backend_facility_id,backend_facility_name,shw.assortment_type,\n", "avg(shw.weighted_availability) as fe_availability, avg(base.weighted_availability) as be_availability,v1_quantity,inward_drop,\n", "storage_drop,truck_load_drop,picking_capacity_quantity_drop, past_inward,past_storage,past_truck,past_picking from metrics.store_hourly_weighted_availability shw\n", "left join lake_po.physical_facility_outlet_mapping pfom on pfom.facility_id = shw.facility_id\n", "left join base on base.receiving_outlet_id = pfom.outlet_id\n", "where shw.assortment_type = 'Packaged Goods' and shw.date_ = current_date and shw.hour in (6,7,8,9,10)\n", "group by 1,2,3,4,5,6,9,10,11,12,13,14,15,16,17\n", "order by 4,7,1)\n", "\n", "select * from final \n", "where fe_availability < 0.9\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "av_data = pd.read_sql_query(sql=sql, con=con)\n", "av_data.head()"]}, {"cell_type": "markdown", "id": "e145c07d-4b09-4515-a7da-56ecae6449bb", "metadata": {}, "source": ["Query to fetch average picking truncation at BE level"]}, {"cell_type": "code", "execution_count": null, "id": "48fc672d-2753-424b-b571-78e19b37f220", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"with be as (select receiving_outlet_id,sender_outlet_id,co.facility_id as backend_facility_id,ars_run_id from metrics.esto_details ed\n", "left join lake_retail.console_outlet co on co.id = ed.sender_outlet_id and co.active = 1\n", "where date(ed.sto_created_at) = current_date-1 and ed.ars_mode = 'normal' \n", "group by 1,2,3,4),\n", "\n", "truncation_data as (\n", "SELECT date(ars_started_at) as dt,\n", "       run_id,\n", "       backend_facility_id,\n", "       frontend_facility_id,\n", "       frontend_outlet_id,\n", "       sum(v1_quantity) as v1_quantity, sum(v2_quantity) AS v2_quantity,\n", "       sum(v2_quantity_without_case) as v2_quantity_without_case,\n", "       sum(inward_drop)/sum(v1_quantity) AS inward_drop,\n", "       sum(storage_drop)/sum(v1_quantity) AS storage_drop,\n", "       sum(truck_load_drop)/sum(v1_quantity) AS truck_load_drop,\n", "       sum(picking_capacity_quantity_drop)/sum(v1_quantity) AS picking_capacity_quantity_drop\n", "       \n", "from metrics.ars_truncation_rca_l7_raw_data_v2\n", "where dt=current_date-1\n", "group by 1,2,3,4,5\n", "order by 6\n", ")\n", "\n", "select be.backend_facility_id,sum(v1_quantity) as v1_quantity,avg(picking_capacity_quantity_drop) as picking_capacity_quantity_drop  from be\n", "left join truncation_data t on t.backend_facility_id = be.backend_facility_id and t.frontend_outlet_id = be.receiving_outlet_id and t.run_id = be.ars_run_id\n", "group by 1\n", "order by 1\n", "\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "picking_data = pd.read_sql_query(sql=sql, con=con)\n", "picking_data.head()"]}, {"cell_type": "markdown", "id": "3c4f39ed-305c-48f6-ae34-ca335e1623ca", "metadata": {}, "source": ["Query to fetch inward, storage and truck load truncation for the past 7 days for FEs"]}, {"cell_type": "code", "execution_count": null, "id": "110e2f42-2a82-46aa-a53c-bbde2b21a13c", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"-- Getting STO details for identifying FC mapping\n", "with be as (select receiving_outlet_id,sender_outlet_id,co.facility_id as backend_facility_id from metrics.esto_details ed\n", "left join lake_retail.console_outlet co on co.id = ed.sender_outlet_id and co.active = 1\n", "where date(ed.sto_created_at) = current_date-1 and ed.ars_mode = 'normal' \n", "group by 1,2,3),\n", "\n", "--Getting truncation data \n", "truncation_data as (\n", "SELECT date(ars_started_at) as dt,\n", "       ZONE,\n", "       backend_facility_id,\n", "       frontend_facility_id,\n", "       frontend_outlet_id,\n", "       sum(v1_quantity) as v1_quantity, sum(v2_quantity) AS v2_quantity,\n", "       sum(v2_quantity_without_case) as v2_quantity_without_case,\n", "       sum(inward_drop)/sum(v1_quantity) AS inward_drop,\n", "       sum(storage_drop)/sum(v1_quantity) AS storage_drop,\n", "       sum(truck_load_drop)/sum(v1_quantity) AS truck_load_drop,\n", "       sum(picking_capacity_quantity_drop)/sum(v1_quantity) AS picking_capacity_quantity_drop\n", "       \n", "from metrics.ars_truncation_rca_l7_raw_data_v2\n", "where dt between current_date-7 and current_date-1\n", "group by 1,2,3,4,5\n", "order by 6\n", "),\n", "\n", "--Making a base table for above two and BE availability\n", "base as (\n", "select t.dt,receiving_outlet_id,sender_outlet_id,be.backend_facility_id, v1_quantity,inward_drop,storage_drop,\n", "truck_load_drop,picking_capacity_quantity_drop from be\n", "left join metrics.backend_hourly_weighted_availability_logs bhw on bhw.backend_facility_id = be.backend_facility_id\n", "left join truncation_data t on t.backend_facility_id = be.backend_facility_id and t.frontend_outlet_id = be.receiving_outlet_id\n", "),\n", "\n", "final as (select shw.facility_id,receiving_outlet_id,shw.facility_name,\n", "avg(shw.weighted_availability) as fe_availability,avg(v1_quantity) as v1_quantity,avg(inward_drop) as inward_drop,\n", "avg(storage_drop) as storage_drop,avg(truck_load_drop) as truck_load_drop,avg(picking_capacity_quantity_drop) as picking_capacity_quantity_drop \n", "from metrics.store_hourly_weighted_availability shw\n", "left join lake_po.physical_facility_outlet_mapping pfom on pfom.facility_id = shw.facility_id\n", "left join base on base.receiving_outlet_id = pfom.outlet_id\n", "where shw.assortment_type = 'Packaged Goods' and shw.date_ = current_date and shw.hour in (6,7,8,9,10)\n", "group by 1,2,3\n", "order by 1)\n", "\n", "select * from final \n", "where fe_availability < 0.9\n", "\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "truncation_data = pd.read_sql_query(sql=sql, con=con)\n", "truncation_data.head()"]}, {"cell_type": "markdown", "id": "e8f8e3b5-80a2-4b27-975a-a193ee8861d0", "metadata": {}, "source": ["Starting analysis to find reason for low availability "]}, {"cell_type": "code", "execution_count": null, "id": "dad967f0-9ae8-4b87-94a7-41f10b6ff85c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "51d5e987-8a6c-4272-a0fb-a2a01ebe6571", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1c6ed6a0-b63c-4bd9-be6d-765ea4813c59", "metadata": {}, "source": ["Dealing with cases of STO TAT breach"]}, {"cell_type": "code", "execution_count": null, "id": "679cbb97-df8e-4361-b4aa-e3e86c4080c3", "metadata": {}, "outputs": [], "source": ["# Calculating nth percentile TAT value on backend level\n", "sql = \"\"\"\n", "with base as (\n", "    select ars_run_id, date(sto_created_at) as dt, co.facility_id as backend_facility_id, pfom.facility_id as frontend_facility_id, receiving_outlet_id, receiver_outlet_name, \n", "    min(case when picking_started_at_ist = '' or picking_started_at_ist is null then (getdate()+40) else cast(left(picking_started_at_ist,19) as timestamp) end) as picking_started_at_ist,\n", "    max(case when picking_completed_at_ist = '' or picking_completed_at_ist is null then (getdate()-40) else cast(left(picking_completed_at_ist,19) as timestamp) end) as picking_completed_at_ist,\n", "    min(case when trip_created_at is null then (getdate()+40) else cast(left(trip_created_at,19) as timestamp) end) as trip_created_at,\n", "    min(case when trip_started_at is null then (getdate()+40) else cast(left(trip_started_at,19) as timestamp) end) as trip_started_at,\n", "    max(case when consignment_received_at is null then (getdate()-40) else cast(left(consignment_received_at,19) as timestamp) end) as consignment_received_at,\n", "    min(case when grn_started_at is null then (getdate()+40) else cast(left(grn_started_at,19) as timestamp) end) as grn_start,\n", "    max(case when grn_started_at is null then (getdate()-40) else cast(left(grn_started_at,19) as timestamp) end) as grn_end\n", "    from metrics.esto_details ed\n", "    join lake_po.physical_facility_outlet_mapping pfom on pfom.outlet_id = ed.receiving_outlet_id\n", "    left join lake_retail.console_outlet co on co.id = ed.sender_outlet_id and co.active = 1\n", "    where sto_created_at between getdate() - interval '40 hours' and getdate() - interval '10 hours'\n", "    and ars_mode = 'normal'\n", "    group by 1,2,3,4,5,6\n", "    order by 3,2,1\n", ")\n", "\n", "select backend_facility_id,ars_run_id, receiving_outlet_id,frontend_facility_id,(datediff(seconds,picking_started_at_ist,picking_completed_at_ist)/3600.00) as picking_time,\n", "(datediff(seconds,trip_started_at,consignment_received_at)/3600.00) as transit_time,\n", "(datediff(seconds,grn_start,grn_end)/3600.00) as grn_time\n", "from base\n", "--where picking_time between 0 and 48 and transit_time between 0 and 72 and grn_time between 0 and 72\n", "group by 1,2,3,4,5,6,7\n", "order by 1\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "past_tat = pd.read_sql_query(sql=sql, con=con)\n", "past_tat"]}, {"cell_type": "code", "execution_count": null, "id": "14b3f1a3-3481-49b5-bd18-2b856d133da5", "metadata": {}, "outputs": [], "source": ["past_tat.loc[past_tat[\"picking_time\"] < 0, \"picking_time\"] = None\n", "past_tat.loc[past_tat[\"transit_time\"] < 0, \"transit_time\"] = None\n", "past_tat.loc[past_tat[\"grn_time\"] < 0, \"grn_time\"] = None"]}, {"cell_type": "code", "execution_count": null, "id": "daef1451-4835-4696-89f3-11a3767d6729", "metadata": {"tags": []}, "outputs": [], "source": ["# Function to return nth quantile for deciding upper limit\n", "def upper_limit(x):\n", "    return x.quantile(0.85)"]}, {"cell_type": "code", "execution_count": null, "id": "099ad080-9b93-4c0c-a4b3-8b637b3a2d49", "metadata": {}, "outputs": [], "source": ["f = {\"number\": [upper_limit]}\n", "past_tat = past_tat.groupby([\"backend_facility_id\"], as_index=False).agg(\n", "    {\n", "        \"picking_time\": [upper_limit],\n", "        \"transit_time\": [upper_limit],\n", "        \"grn_time\": [upper_limit],\n", "    }\n", ")\n", "past_tat.columns = [\"backend_facility_id\", \"picking_time\", \"transit_time\", \"grn_time\"]\n", "past_tat"]}, {"cell_type": "code", "execution_count": null, "id": "eb6bdb6a-1bd4-46a2-97bf-bc6b5e34b4e4", "metadata": {}, "outputs": [], "source": ["# Introducing hard limits for upper cap of TAT values\n", "past_tat.loc[past_tat[\"picking_time\"] > 10, \"picking_time\"] = 10\n", "past_tat.loc[past_tat[\"transit_time\"] > 8, \"transit_time\"] = 8\n", "past_tat.loc[past_tat[\"grn_time\"] < 8, \"grn_time\"] = 8"]}, {"cell_type": "code", "execution_count": null, "id": "8edee357-5320-4fab-b799-2226ff37a4c6", "metadata": {}, "outputs": [], "source": ["# Taking the last two ARS runs for each facility where run_time_start < current_time-10 hours\n", "sql = \"\"\"\n", "with base as (\n", "    select ars_run_id, date(sto_created_at) as dt, co.facility_id as backend_facility_id, pfom.facility_id as frontend_facility_id, receiving_outlet_id, receiver_outlet_name, \n", "    min(case when picking_started_at_ist = '' or picking_started_at_ist is null then (getdate()+40) else cast(left(picking_started_at_ist,19) as timestamp) end) as picking_started_at_ist,\n", "    max(case when picking_completed_at_ist = '' or picking_completed_at_ist is null then (getdate()-40) else cast(left(picking_completed_at_ist,19) as timestamp) end) as picking_completed_at_ist,\n", "    min(case when trip_created_at is null then (getdate()+40) else cast(left(trip_created_at,19) as timestamp) end) as trip_created_at,\n", "    min(case when trip_started_at is null then (getdate()+40) else cast(left(trip_started_at,19) as timestamp) end) as trip_started_at,\n", "    max(case when consignment_received_at is null then (getdate()-40) else cast(left(consignment_received_at,19) as timestamp) end) as consignment_received_at,\n", "    min(case when grn_started_at is null then (getdate()+40) else cast(left(grn_started_at,19) as timestamp) end) as grn_start,\n", "    max(case when grn_started_at is null then (getdate()-40) else cast(left(grn_started_at,19) as timestamp) end) as grn_end\n", "    from metrics.esto_details ed\n", "    join lake_po.physical_facility_outlet_mapping pfom on pfom.outlet_id = ed.receiving_outlet_id\n", "    left join lake_retail.console_outlet co on co.id = ed.sender_outlet_id and co.active = 1\n", "    where sto_created_at between getdate() - interval '40 hours' and getdate() - interval '10 hours'\n", "    and ars_mode = 'normal'\n", "    group by 1,2,3,4,5,6\n", "    order by 4,1\n", "),\n", "\n", "t as (select backend_facility_id,ars_run_id, receiving_outlet_id,frontend_facility_id,row_number() over (partition by frontend_facility_id order by picking_completed_at_ist desc),\n", "picking_completed_at_ist,(datediff(seconds,picking_started_at_ist,picking_completed_at_ist)/3600.00) as picking_time,\n", "(datediff(seconds,trip_started_at,consignment_received_at)/3600.00) as transit_time,\n", "(datediff(seconds,grn_start,grn_end)/3600.00) as grn_time\n", "from base\n", "group by 1,2,3,4,6,7,8,9\n", "order by 3,6 desc)\n", "\n", "select * from t where row_number in(1,2)\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "tat = pd.read_sql_query(sql=sql, con=con)\n", "tat"]}, {"cell_type": "code", "execution_count": null, "id": "a53d9b53-e398-4d92-ae4d-61ce06dc5a0d", "metadata": {}, "outputs": [], "source": ["tat.loc[tat[\"picking_time\"] < 0, \"picking_time\"] = None\n", "tat.loc[tat[\"transit_time\"] < 0, \"transit_time\"] = None\n", "tat.loc[tat[\"grn_time\"] < 0, \"grn_time\"] = None"]}, {"cell_type": "code", "execution_count": null, "id": "1f06eef1-4528-40b0-89a2-5f71a5727e9d", "metadata": {}, "outputs": [], "source": ["# ind = np.where(mapping['facility_id'] == str(av_data['facility_id'][i]))[0]\n", "n = np.where(past_tat[\"backend_facility_id\"] == 555)[0]\n", "past_tat[\"picking_time\"][n[0]]"]}, {"cell_type": "code", "execution_count": null, "id": "2c04b8ee-5314-445e-b20d-ce612b94d776", "metadata": {}, "outputs": [], "source": ["# function to return flag value\n", "def get_flag(index, val, be_id):\n", "    if val < 0:\n", "        return 0\n", "    n = np.where(past_tat[\"backend_facility_id\"] == be_id)[0]\n", "    if len(n) == 0:\n", "        return 0\n", "    elif index == 1 and past_tat[\"picking_time\"][n[0]] < val:\n", "        return 1\n", "    elif index == 2 and past_tat[\"transit_time\"][n[0]] < val:\n", "        return 1\n", "    elif index == 1 and past_tat[\"grn_time\"][n[0]] < val:\n", "        return 1\n", "    return 0"]}, {"cell_type": "code", "execution_count": null, "id": "b8d43c12-af75-40c3-a591-699c66b97757", "metadata": {}, "outputs": [], "source": ["# calculate the count of ars runs which breached the limit record flag value accordingly\n", "tat[\"picking_flag\"] = 0\n", "tat[\"transit_flag\"] = 0\n", "tat[\"grn_flag\"] = 0\n", "for i in range(len(tat)):\n", "    # picking time check\n", "    flag = get_flag(1, tat[\"picking_time\"][i], tat[\"backend_facility_id\"][i])\n", "    tat[\"picking_flag\"][i] = flag\n", "    # transit time check\n", "    flag = get_flag(2, tat[\"transit_time\"][i], tat[\"backend_facility_id\"][i])\n", "    tat[\"transit_flag\"][i] = flag\n", "    # grn time check\n", "    flag = get_flag(3, tat[\"grn_time\"][i], tat[\"backend_facility_id\"][i])\n", "    tat[\"grn_flag\"][i] = flag"]}, {"cell_type": "code", "execution_count": null, "id": "a9085c73-fd74-4d3f-869c-6c4d4e09630f", "metadata": {}, "outputs": [], "source": ["tat = tat.groupby([\"receiving_outlet_id\", \"frontend_facility_id\"], as_index=False).agg(\n", "    {\"picking_flag\": \"sum\", \"transit_flag\": \"sum\", \"grn_flag\": \"sum\"}\n", ")\n", "tat.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "69f289bc-a631-4e07-9b34-c8536a203dc0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "2a939b25-599e-48fc-a064-7d2a5d78b303", "metadata": {}, "source": ["Functions for truncation statements"]}, {"cell_type": "code", "execution_count": null, "id": "f4068984-750e-402d-916f-19e8f7c106fb", "metadata": {}, "outputs": [], "source": ["def picking_drop(t, be_id, picking_data, state):  # state = 0 t = t, state = 1, t = t_p\n", "    if state == 1:\n", "        return f\"High picking capacity truncation on t-2 & t-3({round(t*100,2)}%)\"\n", "    n = np.where(picking_data[\"backend_facility_id\"] == be_id)[0][0]\n", "    val = picking_data[\"picking_capacity_quantity_drop\"][n]\n", "    r = f\"Picking capacity truncation high for this DS({round(t*100,2)}%)\"\n", "    if (\n", "        val < 10\n", "    ):  # Threshold decider: considering average truncation above 10% as alerting otherwise do RCA\n", "        return r\n", "    else:\n", "        r = f\"Picking capacity truncation high for all DS({round(val*100,2)}%), align more manpower at FC\"\n", "    return r  # Missing picking ratio check and alignment"]}, {"cell_type": "code", "execution_count": null, "id": "7a06e040-1d01-4de9-914e-b303d43cf5b5", "metadata": {}, "outputs": [], "source": ["def inward_drop(t, fe_id, truncation_data, state):\n", "    if state == 1:\n", "        return f\"High inward truncation on t-2 & t-3({round(t*100,2)}%)\"\n", "    n = np.where(truncation_data[\"facility_id\"] == fe_id)[0][0]\n", "    val = truncation_data[\"inward_drop\"][n]\n", "    r = f\"Inward truncation high for this DS({round(t*100,2)}%)\"\n", "    if val < 0.6 * t:\n", "        return r\n", "    else:\n", "        r = f\"High inward truncation({round(val*100,2)}%) for the past week, align more manpower at DS\"\n", "        return r"]}, {"cell_type": "code", "execution_count": null, "id": "2e7ee7ec-2388-4c42-83da-05430b49d275", "metadata": {}, "outputs": [], "source": ["def storage_drop(t, fe_id, truncation_data, state):\n", "    if state == 1:\n", "        return f\"High storage truncation on t-2 & t-3({round(t*100,2)}%)\"\n", "    n = np.where(truncation_data[\"facility_id\"] == fe_id)[0][0]\n", "    val = truncation_data[\"storage_drop\"][n]\n", "    r = f\"Storage truncation high for this DS({round(t*100,2)}%)\"\n", "    if val < 0.6 * t:\n", "        return r\n", "    else:\n", "        r = f\"High storage truncation({round(val*100,2)}%) for the past week, increase replenishments/ reduce DOI\"  # Check if can be done basis on DOI value\n", "        return r"]}, {"cell_type": "code", "execution_count": null, "id": "5fde19aa-412d-45e9-a666-4a36adc7e63d", "metadata": {}, "outputs": [], "source": ["def truck_drop(t, fe_id, truncation_data, state):\n", "    if state == 1:\n", "        return f\"High truck truncation on t-2 & t-3({round(t*100,2)}%)\"\n", "    n = np.where(truncation_data[\"facility_id\"] == fe_id)[0][0]\n", "    val = truncation_data[\"truck_load_drop\"][n]\n", "    r = f\"Truck truncation high for this DS({round(t*100,2)}%)\"\n", "    if val < 0.6 * t:\n", "        return r\n", "    else:\n", "        r = f\"High truck load truncation({round(val*100,2)}%) for the past week, upgrade truck\"\n", "        return r"]}, {"cell_type": "code", "execution_count": null, "id": "a402373a-f45e-4705-a1fe-96b667e4bb3d", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1OT6q7Gv7PjdKCdAzA4jGlxp3eozE7NdzGmP8FlFJPNI\"\n", "sheet_name = \"30 minutes stores\"\n", "\n", "ds_list = pb.from_sheets(sheet_id, sheet_name, service_account=\"service_account\")\n", "ds_list"]}, {"cell_type": "code", "execution_count": null, "id": "c1645af1-0888-4468-8052-a26d205046e1", "metadata": {}, "outputs": [], "source": ["for i in range(len(ds_list)):\n", "    av_data.drop(\n", "        av_data[av_data[\"facility_id\"] == int(ds_list[\"Facility ID\"][i])].index,\n", "        inplace=True,\n", "    )\n", "av_data = av_data.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6c21a795-d90d-4c22-895b-2f26cb53cff2", "metadata": {}, "outputs": [], "source": ["av_data = av_data.fillna(-1)\n", "reason = \"Do RCA\"\n", "av_data[\"Reason\"] = reason\n", "av_data[\"Reason Flag\"] = 0\n", "av_data.head()"]}, {"cell_type": "markdown", "id": "5d609477-0dd3-4885-af43-b3d5728f9600", "metadata": {}, "source": ["Getting default mapping for DS"]}, {"cell_type": "code", "execution_count": null, "id": "27c5c21c-09d2-41a2-8a1e-f7894be93525", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1OT6q7Gv7PjdKCdAzA4jGlxp3eozE7NdzGmP8FlFJPNI\"\n", "sheet_name = \"default_mapping\"\n", "\n", "mapping = pb.from_sheets(sheet_id, sheet_name, service_account=\"service_account\")\n", "mapping"]}, {"cell_type": "markdown", "id": "79fc5358-b984-4e97-a5f0-344f3f416f10", "metadata": {}, "source": ["Facility wise check to find reason for low availability"]}, {"cell_type": "code", "execution_count": null, "id": "094b98d2-ef21-45ad-84cb-13f0949b7d76", "metadata": {}, "outputs": [], "source": ["for i in range(len(av_data)):\n", "    reason = \"Do RCA\"  # default reason\n", "    flag = 0\n", "\n", "    # No STO for the FE in the search range\n", "    if av_data[\"backend_facility_id\"][i] == -1:\n", "        reason = \"No STO quantity for the past day\"\n", "        av_data[\"Reason\"][i] = reason\n", "        av_data[\"Reason Flag\"][i] = 1\n", "        ind = np.where(mapping[\"facility_id\"] == str(av_data[\"facility_id\"][i]))[0]\n", "        if len(ind) > 0:\n", "            ind = ind[0]\n", "            av_data[\"backend_facility_id\"][i] = mapping[\"backend_facility_id\"][ind]\n", "            av_data[\"backend_facility_name\"][i] = mapping[\"backend_facility_name\"][ind]\n", "        continue\n", "\n", "    # Checking for TAT breach\n", "    t_i = np.where(tat[\"frontend_facility_id\"] == av_data[\"facility_id\"][i])[0]\n", "    if len(t_i) > 0 and (\n", "        tat[\"picking_flag\"][t_i[0]] > 0\n", "        or tat[\"transit_flag\"][t_i[0]] > 0\n", "        or tat[\"grn_flag\"][t_i[0]] > 0\n", "    ):\n", "        reason = f'Breach in STO TAT: [Picking Time:{tat[\"picking_flag\"][t_i[0]]}, Transit Time:{tat[\"transit_flag\"][t_i[0]]}, GRN Time:{tat[\"grn_flag\"][t_i[0]]}]'\n", "        av_data[\"Reason\"][i] = reason\n", "        flag = pd.Series(tat.iloc[t_i[0], 2:], dtype=\"float64\")\n", "        flag = flag.idxmax()\n", "        if flag == \"picking_flag\":\n", "            av_data[\"Reason Flag\"][i] = 2\n", "        if flag == \"transit_flag\":\n", "            av_data[\"Reason Flag\"][i] = 3\n", "        if flag == \"grn_flag\":\n", "            av_data[\"Reason Flag\"][i] = 4\n", "        continue\n", "\n", "    # Obtaining truncation data\n", "    # for t-1\n", "    t = av_data.iloc[i, 9:13].max()\n", "    temp = pd.Series(av_data.iloc[i, 9:13], dtype=\"float64\")\n", "    index = temp.idxmax()\n", "    # for t-2 and t-3\n", "    t_p = av_data.iloc[i, 13:17].max()\n", "    temp_p = pd.Series(av_data.iloc[i, 13:17], dtype=\"float64\")\n", "    index_p = temp_p.idxmax()\n", "\n", "    # Checking backend availability\n", "    if av_data[\"be_availability\"][i] < 0.85:\n", "        reason, flag = \"Low backend availability\", 5\n", "    elif av_data[\"be_availability\"][i] > 0.85 and av_data[\"be_availability\"][i] < 0.9 and t < 0.05:\n", "        reason, flag = \"Low backend availability\", 5\n", "    # Checking truncation values\n", "    elif t > 0.01 or t_p > 0.01:\n", "        if t > 0.01:\n", "            if index == \"picking_capacity_quantity_drop\":\n", "                reason, flag = (\n", "                    picking_drop(t, av_data[\"backend_facility_id\"][i], picking_data, 0),\n", "                    6,\n", "                )\n", "            elif index == \"inward_drop\":\n", "                reason, flag = (\n", "                    inward_drop(t, av_data[\"facility_id\"][i], truncation_data, 0),\n", "                    9,\n", "                )\n", "            elif index == \"storage_drop\":\n", "                reason, flag = (\n", "                    storage_drop(t, av_data[\"facility_id\"][i], truncation_data, 0),\n", "                    8,\n", "                )\n", "            elif index == \"truck_load_drop\":\n", "                reason, flag = (\n", "                    truck_drop(t, av_data[\"facility_id\"][i], truncation_data, 0),\n", "                    7,\n", "                )\n", "        elif t_p > 0.01:\n", "            if index_p == \"past_picking\":\n", "                reason, flag = (\n", "                    picking_drop(t_p, av_data[\"backend_facility_id\"][i], picking_data, 1),\n", "                    6,\n", "                )\n", "            elif index_p == \"past_inward\":\n", "                reason, flag = (\n", "                    inward_drop(t_p, av_data[\"facility_id\"][i], truncation_data, 1),\n", "                    9,\n", "                )\n", "            elif index_p == \"past_storage\":\n", "                reason, flag = (\n", "                    storage_drop(t_p, av_data[\"facility_id\"][i], truncation_data, 1),\n", "                    8,\n", "                )\n", "            elif index_p == \"past_truck\":\n", "                reason, flag = (\n", "                    truck_drop(t_p, av_data[\"facility_id\"][i], truncation_data, 1),\n", "                    7,\n", "                )\n", "    av_data[\"Reason\"][i], av_data[\"Reason Flag\"][i] = reason, flag"]}, {"cell_type": "code", "execution_count": null, "id": "e53db6f5-7166-43c2-9ca7-f01ab57e4918", "metadata": {}, "outputs": [], "source": ["av_data = av_data.sort_values(by=[\"backend_facility_id\", \"fe_availability\"]).reset_index(drop=True)\n", "av_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e65a1f0c-9220-4177-86f7-31fe90e47576", "metadata": {}, "outputs": [], "source": ["output = pd.DataFrame(\n", "    columns=[\n", "        \"FE ID\",\n", "        \"FE Name\",\n", "        \"BE ID\",\n", "        \"BE Name\",\n", "        \"FE Availability%\",\n", "        \"BE Availability%\",\n", "        \"Reason\",\n", "    ]\n", ")\n", "output[\"FE ID\"] = av_data[\"facility_id\"]\n", "output[\"FE Name\"] = av_data[\"facility_name\"]\n", "output[\"BE ID\"] = av_data[\"backend_facility_id\"]\n", "output[\"BE Name\"] = av_data[\"backend_facility_name\"]\n", "output[\"BE Availability%\"] = av_data[\"be_availability\"] * 100\n", "output[\"FE Availability%\"] = av_data[\"fe_availability\"] * 100\n", "output[\"Reason\"] = av_data[\"Reason\"]"]}, {"cell_type": "code", "execution_count": null, "id": "d8c102d0-2d0b-4f4f-985f-65d17a7d72f2", "metadata": {}, "outputs": [], "source": ["output[\"BE Availability%\"] = output[\"BE Availability%\"].round(decimals=2)\n", "output[\"FE Availability%\"] = output[\"FE Availability%\"].round(decimals=2)\n", "output[\"BE ID\"] = output[\"BE ID\"].astype(\"int32\")\n", "output[\"BE ID\"] = output[\"BE ID\"].replace(-1, \"-\")\n", "output[\"BE Name\"] = output[\"BE Name\"].replace(-1, \"-\")\n", "output[\"BE Availability%\"] = output[\"BE Availability%\"].replace(-100, \"-\")\n", "output.head()"]}, {"cell_type": "markdown", "id": "0326a5f0-8f78-42ef-bdc3-b8d2d3ed2efd", "metadata": {}, "source": ["Updating in overview sheet "]}, {"cell_type": "code", "execution_count": null, "id": "64c79a9b-acfd-47ae-a936-d0ecf6a24416", "metadata": {}, "outputs": [], "source": ["summary = output.copy()\n", "summary[\"date\"] = date.today()\n", "summary[\"Reason flag\"] = av_data[\"Reason Flag\"]\n", "summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2eb49d2e-7061-4838-a781-26d8d144cba2", "metadata": {}, "outputs": [], "source": ["output.head()"]}, {"cell_type": "code", "execution_count": null, "id": "16439e7c-e868-441d-9eb5-de3fd5904ed8", "metadata": {}, "outputs": [], "source": ["# take past 7 days data from sheet, remove the data from oldest day, append new data and rewrite\n", "today = date.today()\n", "today"]}, {"cell_type": "code", "execution_count": null, "id": "ff0c83b6-7062-409a-8cfa-853c88fcd5d2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1OT6q7Gv7PjdKCdAzA4jGlxp3eozE7NdzGmP8FlFJPNI\"\n", "sheet_name = \"raw_data\"\n", "\n", "past_data = pb.from_sheets(sheet_id, sheet_name, service_account=\"service_account\")\n", "past_data"]}, {"cell_type": "code", "execution_count": null, "id": "a87fd688-5a4f-4d36-b9fe-093614868f04", "metadata": {}, "outputs": [], "source": ["# Finding the last day to delete data\n", "def subtract_days_from_date(date, days):\n", "    \"\"\"Subtract days from a date and return the date.\n", "\n", "    Args:\n", "        date (string): Date string in YYYY-MM-DD format.\n", "        days (int): Number of days to subtract from date\n", "\n", "    Returns:\n", "        date (date): Date in YYYY-MM-DD with X days subtracted.\n", "    \"\"\"\n", "\n", "    subtracted_date = pd.to_datetime(date) - timedelta(days=days)\n", "    subtracted_date = subtracted_date.strftime(\"%Y-%m-%d\")\n", "\n", "    return subtracted_date\n", "\n", "\n", "last_date = subtract_days_from_date(today, 90)\n", "last_date"]}, {"cell_type": "code", "execution_count": null, "id": "5235e62b-2551-4605-802a-8dbc5cc1de13", "metadata": {}, "outputs": [], "source": ["past_data.drop(past_data[past_data[\"date\"] <= last_date].index, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "eefd6ed4-c946-4c96-b969-6300a19bf615", "metadata": {}, "outputs": [], "source": ["summary = summary.append(past_data, ignore_index=True)\n", "try:\n", "    summary[\"BE ID\"] = summary[\"BE ID\"].astype(\"int32\")\n", "except ValueError as e:\n", "    print(e)\n", "try:\n", "    summary[\"FE ID\"] = summary[\"FE ID\"].astype(\"int32\")\n", "except ValueError as e:\n", "    print(e)\n", "summary = summary.sort_values(by=[\"BE ID\", \"FE ID\", \"date\"]).reset_index(drop=True)\n", "summary"]}, {"cell_type": "code", "execution_count": null, "id": "c68bdd4b-6834-4032-9d4f-1b4360a6e572", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(summary, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "fa2cd46a-958a-4bf1-96e1-d91004664b42", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ed37aae-c58e-4ad3-b221-3f8e3d16daf2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "10ac94eb-a438-4ea2-bfd3-0f414d986176", "metadata": {}, "outputs": [], "source": ["def render_mpl_table(\n", "    data,\n", "    col_width=8.0,\n", "    row_height=0.9125,\n", "    font_size=24,\n", "    header_color=\"#204B57\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, cellLoc=\"center\", **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    mpl_table.auto_set_font_size(False)\n", "    # plt.show()\n", "    return ax.get_figure(), ax"]}, {"cell_type": "code", "execution_count": null, "id": "278f6332-5136-46d6-8d5d-e42308f1ba28", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cef517be-3084-48b6-afe9-e8c9edd2d701", "metadata": {}, "source": ["Slack message output "]}, {"cell_type": "code", "execution_count": null, "id": "3e578c66-50ee-42d2-a5a8-4fcb0b653ef8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1fb33b18-a6d0-4973-92c1-88a24e13e5a7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a83114c7-812f-499e-a2c2-2c0a323f9d52", "metadata": {}, "outputs": [], "source": ["output = output.sort_values(by=[\"FE Availability%\"]).reset_index(drop=True)\n", "l = min(15, len(output))\n", "temp = output[:l]\n", "fig, ax = render_mpl_table(temp)\n", "fig.savefig(f\"Top15worststores.png\")\n", "filepath = f\"./Top15worststores.png\"\n", "pb.send_slack_message(\n", "    channel=\"C03SZHNSVPA\",\n", "    text=f\"Top 15 stores with worst availability, refer to <https://docs.google.com/spreadsheets/d/1OT6q7Gv7PjdKCdAzA4jGlxp3eozE7NdzGmP8FlFJPNI/edit?usp=sharing| this sheet > for detailed view\",\n", "    files=[filepath],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "588e96f0-b3f4-4453-a27f-7494078d8462", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5ac762c7-eaa1-4e0a-b597-3f18a9d577e9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "77debe4a-aa16-4e49-ac42-7cac3d65c860", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a21796b6-f5ab-47a4-8475-d8e8f6fc411d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2bfa6637-b428-4027-9b47-d8f86d4a0f46", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}