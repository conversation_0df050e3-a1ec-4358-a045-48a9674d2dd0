{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pandas as pd\n", "import pencilbox as pb\n", "import boto3\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_query = \"\"\"\n", "select max(run_id) as run from ars.job_run where is_simulation=0 and run_date=CURDATE() group by facility_id having facility_id in (select distinct facility_id from po.physical_facility_outlet_mapping);\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_data = pd.read_sql_query(run_id_query, con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_ids = run_id_data.loc[:, \"run\"].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_plan_data = pd.DataFrame()\n", "for run_id in run_ids:\n", "    sales_plan_query = \"\"\"\n", "        select * from ars.outlet_item_date_wise_cpd where run_id = %(run_id)s;\n", "    \"\"\"\n", "    query_params = {\"run_id\": run_id}\n", "    sales_plan_int_data = pd.read_sql_query(sales_plan_query, con, params=query_params)\n", "    sales_plan_data = sales_plan_data.append(sales_plan_int_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_plan_data.sort_values(by=[\"outlet_id\", \"item_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_plan_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_plan_data = pd.pivot_table(\n", "    sales_plan_data, index=[\"item_id\", \"outlet_id\"], columns=\"for_date\", values=\"cpd\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_plan_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["csv_file_path = \"date_wise_cpd_ars-{today}.csv\".format(today=datetime.now())\n", "local_file_path = \"/tmp/{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "sales_plan = sales_plan_data.to_csv(\"{filepath}\".format(filepath=local_file_path))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secrets = pb.get_secret(\"retail/jhub_keys\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_path = \"reports/{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "bucket_name = \"grofers-retail-test\"\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "bucket_obj.upload_file(local_file_path, file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s3_client = session.client(\"s3\")\n", "presigned_url = s3_client.generate_presigned_url(\n", "    \"get_object\",\n", "    Params={\"Bucket\": bucket_name, \"Key\": file_path},\n", "    HttpMethod=\"GET\",\n", "    ExpiresIn=10800,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = json.loads(secrets.get(\"date_wise_cpd_to_emails\"))\n", "subject = \"ARS Date Wise CPD Report\"\n", "html_content = \"<p>Hi, You can download the file from <a href='{url}'>here.</a></p><p><b>Note</b>: The link will be active for only three hours</p>\".format(\n", "    url=presigned_url\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 4}