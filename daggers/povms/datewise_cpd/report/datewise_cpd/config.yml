dag_name: datewise_cpd
dag_type: report
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters:
    foo: bar
owner:
  name: chaitra
  slack_id: UAFM5NR0D
path: povms/datewise_cpd/report/datewise_cpd
paused: true
pool: povms_pool
project_name: datewise_cpd
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 3 * * *
  start_date: '2019-07-10T03:30:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
