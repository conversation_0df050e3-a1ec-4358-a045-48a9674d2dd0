{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import pandas\n", "import numpy as np\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT tat_days.*,\n", "current_date  as todays,\n", "PUT_AWAY_BUFFER_DOI,trigger_doi\n", "FROM\n", "  (SELECT a.aligned_vendor_id,\n", "          a.item_id,\n", "          a.outlet_id,\n", "          a.put_away_buffer_doi,a.trigger_doi\n", "   FROM consumer.ars_case_incorporation_result AS a\n", "   INNER JOIN (select aligned_vendor_id,\n", "               item_id,\n", "               outlet_id,\n", "               max(created_at) AS created_at\n", "               FROM consumer.ars_case_incorporation_result\n", "               where PUT_AWAY_BUFFER_DOI is not null and trigger_doi is not null\n", "               GROUP BY 1,\n", "                        2,\n", "                        3) AS b ON a.aligned_vendor_id=b.aligned_vendor_id\n", "   AND a.item_id=b.item_id\n", "   AND a.outlet_id=b.outlet_id\n", "   AND a.created_at=b.created_at) AS buffer_day\n", " full outer JOIN\n", "  (SELECT a.item_id,\n", "          a.outlet_id,\n", "          a.vendor_id,\n", "          a.tat_days\n", "   FROM consumer.ars_outlet_vendor_item_tat_days AS a\n", "   INNER JOIN\n", "     (SELECT item_id,\n", "             outlet_id,\n", "             vendor_id,\n", "             max(created_at) AS created_at\n", "      FROM consumer.ars_outlet_vendor_item_tat_days\n", "      GROUP BY 1,\n", "               2,\n", "               3) AS b ON a.item_id=b.item_id\n", "   AND a.outlet_id=b.outlet_id\n", "   AND a.vendor_id=b.vendor_id\n", "   AND a.created_at=b.created_at) tat_days ON buffer_day.item_id=tat_days.item_id\n", "AND buffer_day.outlet_id=tat_days.outlet_id\n", "AND buffer_day.aligned_vendor_id=tat_days.vendor_id\n", "where tat_days.outlet_id in (413,419)\n", "AND buffer_day.outlet_id in (413,419)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.sample(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.tat_days.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def date_by_adding_business_days(from_date, add_days):\n", "    business_days_to_add = add_days\n", "    current_date = from_date\n", "    while business_days_to_add > 0:\n", "        current_date += <PERSON><PERSON><PERSON>(days=1)\n", "        weekday = current_date.weekday()\n", "        if weekday == 6:  # sunday = 6\n", "            continue\n", "        business_days_to_add -= 1\n", "    return current_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check[\"max_tat_Date\"] = cpd_check.apply(\n", "    lambda x: date_by_adding_business_days(x[\"todays\"], x[\"tat_days\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.sample(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check[\"day\"] = cpd_check.max_tat_Date.apply(lambda x: x.weekday())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check[\"put_away_buffer_doi\"] = np.where(\n", "    cpd_check.put_away_buffer_doi.isna(), 1, cpd_check.put_away_buffer_doi\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check[\"final_Date_modify\"] = np.where(\n", "    cpd_check.day == 6, cpd_check.max_tat_Date + timedelta(days=1), cpd_check.max_tat_Date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check[\"final_tat_Date\"] = cpd_check.apply(\n", "    lambda x: date_by_adding_business_days(x[\"final_Date_modify\"], x[\"put_away_buffer_doi\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check[\"final_tat_Date\"] = pandas.to_datetime(cpd_check.final_tat_Date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check[\"last_check_date\"] = cpd_check.apply(\n", "    lambda x: x.final_tat_Date + timedelta(days=x.trigger_doi), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_check.tat_days.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date = cpd_check.last_check_date.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date = pandas.to_datetime(cpd_date).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_cpd = \"\"\"\n", "\n", "with\n", " cpd_data as\n", " (\n", "SELECT DISTINCT date as missing_date,'{cpd_date}' as max_date\n", "FROM po_date_wise_consumption_per_day AS a\n", " WHERE date BETWEEN CURRENT_DATE AND '{cpd_date}'),\n", "    \n", "     summary as (\n", "     SELECT a.*,\n", "     item_id,\n", "     outlet_id,\n", "      consumption_per_day\n", "      from cpd_data as a\n", "      left join po_date_wise_consumption_per_day cpd ON a.missing_date=cpd.date\n", "\n", "WHERE a.missing_date between current_date and '{cpd_date}'\n", "and cpd.outlet_id in (413,419)\n", ")\n", "select * from summary\n", "\n", "              \"\"\".format(\n", "    cpd_date=cpd_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_value = pandas.read_sql_query(sql=query_cpd, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_value.shape\n", "# (95123, 5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge = cpd_check.merge(cpd_value, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_not_present = (\n", "    cpd_merge.groupby([\"item_id\", \"outlet_id\", \"vendor_id\"])[\n", "        [\"last_check_date\", \"consumption_per_day\"]\n", "    ]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_not_present.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_not_present = data_not_present[\n", "    (data_not_present.last_check_date == 1) & (data_not_present.consumption_per_day == 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data_not_present[(final_data_not_present.item_id==10000656)&(final_data_not_present.outlet_id==413)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_not_present.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_not_present.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_outlet_no_data_raw = final_data_not_present[[\"item_id\", \"outlet_id\", \"vendor_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_outlet_no_data_raw.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present = (\n", "    final_data_not_present.groupby([\"outlet_id\"])[[\"item_id\"]].nunique().reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,name as outlet_name from pos_console_outlet \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_mapping = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name = item_outlet_no_data_raw.merge(\n", "    outlet_mapping, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name = no_cpd_data_present_outlet_name[\n", "    [\"outlet_id\", \"outlet_name\", \"item_id\", \"vendor_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# items = no_cpd_data_present_outlet_name.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# no_cpd_data_present=no_cpd_data_present.rename(columns={'item_id':'count of item id'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_data_items = item_outlet_no_data_raw.merge(outlet_mapping, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_data_items.sample(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_data_items = no_data_items[[\"item_id\", \"outlet_id\", \"vendor_id\", \"outlet_name\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1 = cpd_check.merge(cpd_value, on=[\"item_id\", \"outlet_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1[cpd_merge_1.consumption_per_day.isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1.missing_date = pandas.to_datetime(cpd_merge_1.missing_date).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1[\"missing_date\"] = pandas.to_datetime(cpd_merge_1.missing_date)\n", "cpd_merge_1[\"last_check_date\"] = pandas.to_datetime(cpd_merge_1.last_check_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1[\"date_flag\"] = cpd_merge_1.missing_date <= cpd_merge_1.last_check_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date_check = cpd_merge_1[cpd_merge_1.date_flag == True]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date_check.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date_check.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final = cpd_date_check[cpd_date_check.consumption_per_day.isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final['days']=cpd_final.final_Date-cpd_final.todays"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final.days = cpd_final.days.astype('int64')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final_l30 = cpd_final[cpd_final.days <= 30].groupby('outlet_id')['item_id'].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final_g30 = cpd_final[cpd_final.days > 30].groupby('outlet_id')['item_id'].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final = pandas.merge(cpd_final_g30,cpd_final_l30,on='outlet_id',how='outer')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # cpd_date_check_final=cpd_date_check_final.rename(columns={'item_id_x':'count of item(>30 days)'})\n", "# cpd_date_check_final=cpd_date_check_final.rename(columns={'item_id_y':'count of item (<=30 days)'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final[['outlet_id','count of item (<=30 days)','count of item(>30 days)']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final[['item_id','outlet_id','vendor_id']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_of_run = (datetime.now()).strftime(\"%d-%m-%Y\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_of_run"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final = cpd_final[\n", "    [\n", "        \"item_id\",\n", "        \"outlet_id\",\n", "        \"vendor_id\",\n", "        \"tat_days\",\n", "        \"put_away_buffer_doi\",\n", "        \"todays\",\n", "        \"last_check_date\",\n", "        \"final_tat_Date\",\n", "        \"missing_date\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final = cpd_final.rename(\n", "    columns={\n", "        \"last_check_date\": \"trigger_doi_date(expected_live_date+trigger_doi)\",\n", "        \"todays\": \"current_date\",\n", "        \"final_tat_Date\": \"expected_live_date\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["missing_values_filepath = pb.to_csv(\n", "    cpd_final, \"Missing CPD values for items-{}.csv\".format(date_of_run)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_data_present_filepath = pb.to_csv(\n", "    no_cpd_data_present_outlet_name, \"No CPD Data present -{}.csv\".format(date_of_run)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "# to_email = [\"<EMAIL>\", \"<EMAIL>\",\"<EMAIL>\", \"<EMAIL>\"]\n", "subject = \"Date wise CPD missing Report - \" + date_of_run\n", "html_content = \"\"\"\n", "\n", "Hi all,<br/>\n", "\n", "Please find date wise cpd missing data report.<br/>\n", "<br/>\n", "Attachments consists of the following data:<br/>\n", "-'No CPD Data present' file consists of item-vendor-outlet whose CPD values were not present at all<br/>\n", "-'Missing CPD values for items' file consists of item-vendor-outlet whose CPD values were missing for particular dates from today till their expected live date <br/>\n", "<br/>\n", "\n", "Kindly update their values<br/>\n", "<br/>\n", "\n", "Note:Only B'lore outlets are reported for now<br/>\n", "<br/>\n", "Best Regards,<br/>\n", "Data Bangalore <br/>\n", "\n", "\n", "\"\"\"\n", "\n", "print(\"Sending message\")\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    files=[missing_values_filepath, no_data_present_filepath],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# email_ids=[\"<EMAIL>\"]\n", "\n", "# from gr_mailer import send_email\n", "# import jinja2\n", "\n", "# # In[19]:\n", "\n", "\n", "# sender = \"<EMAIL>\"\n", "# to = ['<EMAIL>','<EMAIL>']\n", "# cc=[\"<EMAIL>\"]\n", "# subject = \"Missing CPD values for items:\"+ date_of_run\n", "\n", "\n", "# send_email(sender, to, subject, html_content = rendered,files=['data/output/Missing CPD values for items-{}.csv'.format(date_of_run),'data/output/No CPD Data present -{}.csv'.format(date_of_run)],\n", "#                    dryrun=False, cc=cc, bcc=None,\n", "#                     mime_subtype='mixed', mime_charset='utf-8')\n", "# print('line6')\n", "# print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 2}