<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}
</style>
</head>
<p>Hi All,</p>

<p>Please find report for date wise cpd missing data.<br/>
Summary statistics for Item-outlet-vendors whose CPD values were not present at all are as follows:<br/>
</p>
<!-- <table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Outlet id</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Outlet name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Count of items </span></th>
          
        </tr>
        {% for product in products %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.outlet_id}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.outlet_name}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.count of item_id}}</b>
            </td>
           
        </tr>
        {% endfor %}

    </tbody>
</table> -->
<p>
Attachments consists of the following data:<br/>
-Item-outlet-aligned vendor whose CPD values were not present at all<br/>
-Item-outlets-date whose CPD values were missing for particular dates between current date and expected live date <br/>
Kindly update their values<br/>
</p>

<p>Best Regards,<br />
Data Bangalore
</p>
