{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import pandas\n", "import numpy as np\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT tat_days.*,\n", "current_date  as todays,\n", "PUT_AWAY_BUFFER_DOI,trigger_doi\n", "FROM\n", "  (SELECT a.aligned_vendor_id,\n", "          a.item_id,\n", "          a.outlet_id,\n", "          a.put_away_buffer_doi,a.trigger_doi\n", "   FROM consumer.ars_case_incorporation_result AS a\n", "   INNER JOIN (select aligned_vendor_id,\n", "               item_id,\n", "               outlet_id,\n", "               max(created_at) AS created_at\n", "               FROM consumer.ars_case_incorporation_result\n", "               where PUT_AWAY_BUFFER_DOI is not null and trigger_doi is not null\n", "               GROUP BY 1,\n", "                        2,\n", "                        3) AS b ON a.aligned_vendor_id=b.aligned_vendor_id\n", "   AND a.item_id=b.item_id\n", "   AND a.outlet_id=b.outlet_id\n", "   AND a.created_at=b.created_at) AS buffer_day\n", "left outer JOIN\n", "  (SELECT a.item_id,\n", "          a.outlet_id,\n", "          a.vendor_id,\n", "          a.tat_days\n", "   FROM consumer.ars_outlet_vendor_item_tat_days AS a\n", "   INNER JOIN\n", "     (SELECT item_id,\n", "             outlet_id,\n", "             vendor_id,\n", "             max(created_at) AS created_at\n", "      FROM consumer.ars_outlet_vendor_item_tat_days\n", "      GROUP BY 1,\n", "               2,\n", "               3) AS b ON a.item_id=b.item_id\n", "   AND a.outlet_id=b.outlet_id\n", "   AND a.vendor_id=b.vendor_id\n", "   AND a.created_at=b.created_at) tat_days ON buffer_day.item_id=tat_days.item_id\n", "AND buffer_day.outlet_id=tat_days.outlet_id\n", "AND buffer_day.aligned_vendor_id=tat_days.vendor_id\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.sample(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_outlets_query = \"\"\"\n", "SELECT outlet_id\n", "FROM ars_outlet ao\n", "INNER JOIN\n", "  (SELECT facility_id,\n", "          max(run_id) AS run_id\n", "   FROM ars_job_run\n", "   WHERE date(started_at)=CURRENT_DATE-1\n", "   GROUP BY 1) jr ON ao.physical_facility_id=jr.facility_id\n", "AND ao.run_id=jr.run_id\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_outlets = pandas.read_sql_query(sql=ars_outlets_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_outlets.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ars_outlets[ars_outlets.outlet_id==116]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_outlets = cpd_check.merge(ars_outlets, on=[\"outlet_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check = cpd_check_outlets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.outlet_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check.tat_days.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na = cpd_check.dropna()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "\n", "def date_by_adding_business_days(from_date, add_days):\n", "    business_days_to_add = add_days\n", "    current_date = from_date\n", "    while business_days_to_add > 0:\n", "        current_date += datetime.timedelta(days=1)\n", "        weekday = current_date.weekday()\n", "        if weekday == 6:  # sunday = 6\n", "            continue\n", "        business_days_to_add -= 1\n", "    return current_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na[\"max_tat_Date\"] = cpd_check_without_na.apply(\n", "    lambda x: date_by_adding_business_days(x[\"todays\"], x[\"tat_days\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na.sample(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na[\"day\"] = cpd_check_without_na.max_tat_Date.apply(lambda x: x.weekday())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na[\"put_away_buffer_doi\"] = np.where(\n", "    cpd_check_without_na.put_away_buffer_doi.isna(), 1, cpd_check_without_na.put_away_buffer_doi\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na[\"final_Date_modify\"] = np.where(\n", "    cpd_check_without_na.day == 6,\n", "    cpd_check_without_na.max_tat_Date + timedelta(days=1),\n", "    cpd_check_without_na.max_tat_Date,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na[\"final_tat_Date\"] = cpd_check_without_na.apply(\n", "    lambda x: date_by_adding_business_days(x[\"final_Date_modify\"], x[\"put_away_buffer_doi\"]), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na[\"final_tat_Date\"] = pandas.to_datetime(cpd_check_without_na.final_tat_Date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na[\"last_check_date\"] = cpd_check_without_na.apply(\n", "    lambda x: x.final_tat_Date + timedelta(days=x.trigger_doi), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_check_without_na.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_check.tat_days.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date = cpd_check_without_na.last_check_date.max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date = pandas.to_datetime(cpd_date).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_cpd = \"\"\"\n", "\n", "with\n", " cpd_data as\n", " (\n", "SELECT DISTINCT date as missing_date,'{cpd_date}' as max_date\n", "FROM snorlax_date_wise_consumption AS a\n", " WHERE date BETWEEN CURRENT_DATE AND '{cpd_date}'),\n", "    \n", "     summary as (\n", "     SELECT a.*,\n", "     item_id,\n", "     outlet_id,\n", "      consumption\n", "      from cpd_data as a\n", "      left join snorlax_date_wise_consumption cpd ON a.missing_date=cpd.date\n", "\n", "WHERE a.missing_date between current_date and '{cpd_date}'\n", "\n", ")\n", "select * from summary\n", "\n", "              \"\"\".format(\n", "    cpd_date=cpd_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_value = pandas.read_sql_query(sql=query_cpd, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_value.shape\n", "# (95123, 5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge = cpd_check_without_na.merge(cpd_value, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_not_present = (\n", "    cpd_merge.groupby([\"item_id\", \"outlet_id\", \"vendor_id\"])[[\"last_check_date\", \"consumption\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_not_present.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_not_present = data_not_present[\n", "    (data_not_present.last_check_date == 1) & (data_not_present.consumption == 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_data_not_present[(final_data_not_present.item_id==10000656)&(final_data_not_present.outlet_id==413)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_not_present.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_not_present.sample(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_outlet_no_data_raw = final_data_not_present[[\"item_id\", \"outlet_id\", \"vendor_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_outlet_no_data_raw.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present = (\n", "    final_data_not_present.groupby([\"outlet_id\"])[[\"item_id\"]].nunique().reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,name as outlet_name from pos_console_outlet \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_mapping = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name = item_outlet_no_data_raw.merge(\n", "    outlet_mapping, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name = no_cpd_data_present_outlet_name[\n", "    [\"outlet_id\", \"outlet_name\", \"item_id\", \"vendor_id\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# items = no_cpd_data_present_outlet_name.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# no_cpd_data_present=no_cpd_data_present.rename(columns={'item_id':'count of item id'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_data_items = item_outlet_no_data_raw.merge(outlet_mapping, on=[\"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_data_items.sample(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_data_items = no_data_items[[\"item_id\", \"outlet_id\", \"vendor_id\", \"outlet_name\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1 = cpd_check_without_na.merge(cpd_value, on=[\"item_id\", \"outlet_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1[cpd_merge_1.consumption.isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1.missing_date = pandas.to_datetime(cpd_merge_1.missing_date).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1[\"missing_date\"] = pandas.to_datetime(cpd_merge_1.missing_date)\n", "cpd_merge_1[\"last_check_date\"] = pandas.to_datetime(cpd_merge_1.last_check_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1[\"date_flag\"] = cpd_merge_1.missing_date <= cpd_merge_1.last_check_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_merge_1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date_check = cpd_merge_1[cpd_merge_1.date_flag == True]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date_check.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date_check.outlet_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_date_check.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final = cpd_date_check[cpd_date_check.consumption.isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final['days']=cpd_final.final_Date-cpd_final.todays"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final.days = cpd_final.days.astype('int64')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final_l30 = cpd_final[cpd_final.days <= 30].groupby('outlet_id')['item_id'].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final_g30 = cpd_final[cpd_final.days > 30].groupby('outlet_id')['item_id'].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final = pandas.merge(cpd_final_g30,cpd_final_l30,on='outlet_id',how='outer')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # cpd_date_check_final=cpd_date_check_final.rename(columns={'item_id_x':'count of item(>30 days)'})\n", "# cpd_date_check_final=cpd_date_check_final.rename(columns={'item_id_y':'count of item (<=30 days)'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_date_check_final[['outlet_id','count of item (<=30 days)','count of item(>30 days)']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final[['item_id','outlet_id','vendor_id']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_of_run = (datetime.now()).strftime(\"%d-%m-%Y\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_of_run"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final.to_csv(\n", "    \"/home/<USER>/Chaitra/daily_cpd_alert/data/output/Missing CPD values for items-{}.csv\".format(\n", "        date_of_run\n", "    ),\n", "    encoding=\"utf8\",\n", "    index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final = cpd_final.rename(columns={\"todays\": \"current_date\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final = cpd_final[[\"item_id\", \"outlet_id\", \"vendor_id\", \"current_date\", \"missing_date\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_final=cpd_final.rename(columns={'last_check_date':'trigger_doi_date(expected_live_date+trigger_doi)','todays':'current_date','final_tat_Date':'expected_live_date'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name.to_csv(\n", "    \"/home/<USER>/Chaitra/daily_cpd_alert/data/output/No CPD Data present -{}.csv\".format(\n", "        date_of_run\n", "    ),\n", "    encoding=\"utf8\",\n", "    index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_cpd_data_present_outlet_name.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility = pandas.read_sql_query(\n", "    sql=\"\"\"select pco.id as outlet_id,facility_id,cf.name as facility_name from pos_console_outlet pco\n", "left join crates_facility cf\n", "on pco.facility_id=cf.id\n", "\"\"\",\n", "    con=redshift,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_no_cpd_facility = no_cpd_data_present_outlet_name.merge(\n", "    facility, on=[\"outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_no_cpd_facility.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_no_cpd = (\n", "    items_no_cpd_facility[[\"facility_id\", \"facility_name\", \"item_id\"]]\n", "    .drop_duplicates()\n", "    .groupby([\"facility_id\", \"facility_name\"])[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_no_cpd = items_no_cpd.sort_values([\"item_id\"], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_no_cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = items_no_cpd.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "\n", "sender = \"<EMAIL>\"\n", "to = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "# to=[\"<EMAIL>\", \"<EMAIL>\",\"<EMAIL>\", \"<EMAIL>\"]\n", "\n", "# to=[\"<EMAIL>\"]\n", "\n", "email_template = \"email_template.html\"\n", "\n", "subject = \"Date wise CPD missing Report - \" + date_of_run\n", "\n", "\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/povms/datewise_cpd/report/missing_cpd\"\n", ")\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(products=items, cpd_date=cpd_date)\n", "\n", "# send_email(sender, to, subject, html_content = rendered,\n", "#                     dryrun=False, cc=cc, bcc=None,\n", "#                     mime_subtype='mixed', mime_charset='utf-8')\n", "\n", "\n", "print(\"Sending message\")\n", "\n", "# send_email(sender, to, subject, message_text, files=['/home/<USER>/Chaitra/daily_cpd_alert/data/output/Missing CPD values for items-{}.csv'.format(date_of_run),'/home/<USER>/Chaitra/daily_cpd_alert/data/output/No CPD Data present -{}.csv'.format(date_of_run)],\n", "#                     dryrun=False, cc=None,\n", "#                     mime_subtype='mixed', mime_charset='utf-8')\n", "\n", "pb.send_email(\n", "    sender,\n", "    to,\n", "    subject,\n", "    html_content=rendered,\n", "    files=[\n", "        \"/home/<USER>/Chaitra/daily_cpd_alert/data/output/Missing CPD values for items-{}.csv\".format(\n", "            date_of_run\n", "        ),\n", "        \"/home/<USER>/Chaitra/daily_cpd_alert/data/output/No CPD Data present -{}.csv\".format(\n", "            date_of_run\n", "        ),\n", "    ],\n", ")\n", "\n", "print(\"Sent email\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 2}