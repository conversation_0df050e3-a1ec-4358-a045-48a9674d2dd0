dag_name: missing_cpd
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: chaitra
  slack_id: UAFM5NR0D
path: povms/datewise_cpd/report/missing_cpd
paused: true
pool: povms_pool
project_name: datewise_cpd
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 4 * * *
  start_date: '2019-07-12T04:30:00'
schedule_type: flexi
sla: 120 minutes
support_files:
- email_template.html
tags: []
template_name: notebook
version: 1
