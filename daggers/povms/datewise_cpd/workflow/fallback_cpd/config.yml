dag_name: fallback_cpd
dag_type: workflow
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters:
    end_date: '{{ ds }}'
    start_date: '{{ macros.ds_add(ds, -31) }}'
owner:
  name: chaitra
  slack_id: UAFM5NR0D
path: povms/datewise_cpd/workflow/fallback_cpd
paused: true
pool: povms_pool
project_name: datewise_cpd
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 4 * * *
  start_date: '2019-10-01T04:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
