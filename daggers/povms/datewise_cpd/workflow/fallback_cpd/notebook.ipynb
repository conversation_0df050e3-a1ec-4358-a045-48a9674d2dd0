{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import re\n", "\n", "import math\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["start_date = (datetime.now() - timed<PERSON>ta(days=31)).strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "end_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%dT%H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH order_details AS\n", "  (SELECT id,\n", "          product_id,\n", "          order_date,\n", "          backend_merchant_id,\n", "          external_id,\n", "          city,\n", "          facility_id,\n", "          outlet_id,\n", "          sum(quantity) AS prod_quantity\n", "   FROM\n", "     (SELECT os.id,\n", "             ooi.product_id,\n", "             date(os.install_ts) AS order_date,\n", "             os.backend_merchant_id,\n", "             om.external_id,\n", "             cl.name AS city,\n", "             co.facility_id,\n", "             oss.quantity,\n", "             cocs.outlet_id\n", "      FROM\n", "        (SELECT id,\n", "                install_ts,\n", "                TYPE ,\n", "                backend_merchant_id\n", "         FROM oms_suborder\n", "         WHERE date(install_ts) BETWEEN '{start_date}' AND '{end_date}'\n", "           AND TYPE='RetailSuborder'\n", "         GROUP BY id,\n", "                  install_ts,\n", "                  TYPE,\n", "                  backend_merchant_id)os\n", "      LEFT JOIN oms_suborder_item oss ON os.id=oss.suborder_id\n", "      INNER JOIN oms_order_item ooi ON oss.order_item_id=ooi.id\n", "      LEFT JOIN oms_merchant om--\n", " ON os.backend_merchant_id=om.id\n", "      LEFT JOIN rt_console_outlet_cms_store cocs ON om.external_id=cocs.cms_store\n", "      INNER JOIN pos_console_outlet co ON cocs.outlet_id = co.id\n", "      INNER JOIN pos_console_location cl ON co.tax_location_id = cl.id\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,\n", "               9)a\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8) \n", "            ,\n", "  \n", "     item_product_mapping AS --to get item_id and product id mapping\n", "\n", "  (SELECT product_id,\n", "          item_id,\n", "       \n", "          multiplier ,\n", "          updated_on\n", "  FROM it_pd_log), products_updated_dates AS\n", "  (SELECT a.product_id,\n", "          order_date,\n", "          max(updated_on) AS updated_list\n", "  FROM\n", "     (SELECT product_id,\n", "             ordeR_date\n", "      FROM order_details\n", "      GROUP BY product_id,\n", "              order_date)A\n", "  LEFT JOIN it_pd_log ip ON (a.product_id)=(ip.product_id)\n", "  WHERE date(order_date)-date(updated_on)>=0---to get latest item,product mapping of given the order date\n", "\n", "  GROUP BY a.product_id,\n", "            order_date),\n", "                    order_item_mapping AS ---using product item mapping and mapping it to orders\n", "\n", "  (SELECT od.order_date,\n", "          city,\n", "          od.facility_id,\n", "          od.product_id,\n", "          prod_quantity,\n", "          updated_list ,\n", "          outlet_id,\n", "          item_id,\n", "          (prod_quantity*coalesce(multiplier,1)) AS item_quantity\n", "  FROM\n", "     (SELECT product_id,\n", "             order_date,\n", "             city,\n", "             facility_id,\n", "             outlet_id,\n", "             sum(prod_quantity) AS prod_quantity\n", "      FROM order_details\n", "      GROUP BY 1,\n", "              2,\n", "              3,\n", "              4,5) od\n", "  INNER JOIN products_updated_dates prod ON od.product_id=prod.product_id\n", "  AND date(od.order_date)=date(prod.order_date)\n", "  INNER JOIN item_product_mapping ipm ON prod.product_id=ipm.product_id\n", "  AND date(prod.updated_list)=date(ipm.updated_on)\n", "  GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,9), cpd AS\n", "  (SELECT city,\n", "          item_id,\n", "          facility_id,\n", "          outlet_id,\n", "          order_date,\n", "          sum(item_quantity) AS total_item_quantity,\n", "          avg(item_quantity) as avg_item_quantity\n", "  FROM order_item_mapping oi\n", "  GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5\n", "             --calculating total items ordered in a city at daily level\n", ") ,\n", "                live_Days AS\n", "  (SELECT item_id,\n", "          outlet_id,\n", "         \n", "          count(*) AS live_Days\n", "  FROM reports_reports_item_inventory_snapshot riis\n", "  LEFT JOIN pos_console_outlet pco ON riis.outlet_id=pco.id\n", "  WHERE date(snapshot_datetime) BETWEEN '{start_date}' AND '{end_date}'\n", "  and actual_quantity-blocked_quantity>0\n", "  GROUP BY 1,\n", "            2\n", "          )\n", "SELECT cpd.*,\n", "      live_Days,\n", "     \n", "      (total_item_quantity* live_Days/30) AS avg_cpd\n", "FROM cpd\n", "LEFT JOIN live_Days ld ON cpd.item_id=ld.item_id\n", "AND cpd.outlet_id=ld.outlet_id\n", "left join pos_console_outlet pco \n", "on cpd.outlet_id=pco.id\n", "WHERE upper(pco.name) like '%%(SSC)%%' \n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_filtered = cpd_values[cpd_values.avg_cpd.notna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_filtered.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg = cpd_values_filtered.groupby([\"item_id\", \"outlet_id\"]).agg(\n", "    {\n", "        \"total_item_quantity\": [\"sum\", \"min\", \"max\", \"mean\", \"std\", \"count\"],\n", "        \"live_days\": [\"mean\"],\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg.columns = [\"_\".join(x) for x in cpd_values_avg.columns.ravel()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg = cpd_values_avg.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg[\"total_item_quantity_std\"] = (cpd_values_avg.total_item_quantity_std).fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg[\"total_item_quantity_min\"] = np.ceil(cpd_values_avg.total_item_quantity_min).astype(\n", "    int\n", ")\n", "cpd_values_avg[\"total_item_quantity_max\"] = np.ceil(cpd_values_avg.total_item_quantity_max).astype(\n", "    int\n", ")\n", "cpd_values_avg[\"total_item_quantity_mean\"] = np.ceil(\n", "    cpd_values_avg.total_item_quantity_mean\n", ").astype(int)\n", "cpd_values_avg[\"total_item_quantity_std\"] = np.ceil(cpd_values_avg.total_item_quantity_std).astype(\n", "    int\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_values_avg[cpd_values_avg.total_item_quantity_std==0].sample(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Sending in data through API"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_values_avg.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#  # cpd fields\n", "#     item_id = models.IntegerField(blank=False, null=False)\n", "#     outlet_id = models.IntegerField(blank=False, null=False)\n", "#     evaluated_cpd = models.DecimalField(blank=False, null=False, max_digits=10, decimal_places=2)\n", "#     sales_deviation = models.IntegerField(blank=True, null=True)\n", "#     min_daily_sales = models.DecimalField(blank=True, null=True, max_digits=10, decimal_places=2)\n", "#     max_daily_sales = models.DecimalField(blank=True, null=True, max_digits=10, decimal_places=2)\n", "\n", "#     # Tracking Fields\n", "#     active = models.BooleanField(blank=False, null=False, default=True)\n", "#     created_at = models.DateTimeField(blank=False, null=False, auto_now=False, auto_now_add=True)\n", "#     created_by = models.IntegerField(blank=False, null=False)\n", "#     updated_at = models.DateTimeField(blank=False, null=False, auto_now=True, auto_now_add=False)\n", "#     updated_by = models.IntegerField(blank=False, null=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actions = []\n", "payload = {\"op\": \"create_or_update_cpd_stats\", \"actions\": actions}\n", "for row in cpd_values_avg.to_dict(orient=\"records\"):\n", "    actions.append(\n", "        {\n", "            \"item_id\": int(row[\"item_id\"]),\n", "            \"outlet_id\": int(row[\"outlet_id\"]),\n", "            \"sales_mean\": int(row[\"total_item_quantity_mean\"]),\n", "            \"sales_deviation\": int(row[\"total_item_quantity_std\"]),\n", "            \"min_daily_sales\": int(row[\"total_item_quantity_min\"]),\n", "            \"max_daily_sales\": int(row[\"total_item_quantity_max\"]),\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(actions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n = 500\n", "results = []\n", "import logging as log\n", "\n", "url = \"https://retail-internal.grofer.io/po/v1/cpd-stats/_bulk/\"\n", "for chunk in [actions[i : i + n] for i in range(0, len(actions), n)]:\n", "    response = requests.post(\n", "        url,\n", "        json={\"op\": \"create_or_update_cpd_stats\", \"actions\": chunk},\n", "        headers={\"user-id\": \"1593\"},\n", "    )\n", "    #     log.info(response.content)\n", "    results.extend(response.json()[\"data\"][\"results\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 2}