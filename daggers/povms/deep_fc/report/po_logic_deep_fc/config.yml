alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: po_logic_deep_fc
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U05F1R698RL
path: povms/deep_fc/report/po_logic_deep_fc
paused: true
pool: povms_pool
project_name: deep_fc
schedule:
  end_date: '2024-10-24T00:00:00'
  interval: 0 2 * * *
  start_date: '2024-07-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
