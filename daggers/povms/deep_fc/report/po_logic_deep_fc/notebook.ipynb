{"cells": [{"cell_type": "code", "execution_count": null, "id": "e1ac6116-aaa6-4420-ad35-f6acad400718", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import time\n", "import datetime\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "ace789f7-2512-49c8-baa8-05f17f82b0e2", "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def read_sql_query(sql):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, trino)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "920bdc9d-6fe4-4fdc-9ac6-c517f412815a", "metadata": {"tags": []}, "source": ["## Define item_ids, Outlet_ids etc"]}, {"cell_type": "markdown", "id": "91cacf8d-31e2-4118-873b-5663e61cb269", "metadata": {}, "source": ["#### B2B Control Sheet\n", "\n", "https://docs.google.com/spreadsheets/d/1rGO-1fQlkESCLOwt4xWybwrgqKpIkgbXEcOSogazU6Q/edit?gid=0#gid=0"]}, {"cell_type": "code", "execution_count": null, "id": "3b816930-0818-4033-8385-1197ef0f6e31", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1rGO-1fQlkESCLOwt4xWybwrgqKpIkgbXEcOSogazU6Q\"\n", "\n", "zone_df = pb.from_sheets(sheet_id, \"open_po_input\")\n", "zone_df[\"mother_wh_facility_id\"] = zone_df[\"mother_wh_facility_id\"].astype(int)\n", "zone_df[\"child_wh_facility_id\"] = zone_df[\"child_wh_facility_id\"].astype(int)\n", "zone_df[\"item_id\"] = zone_df[\"item_id\"].astype(int)\n", "zone_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "381a9cf6-8350-424d-ac11-3050de9ec751", "metadata": {}, "outputs": [], "source": ["zone_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "707ba072-d930-491b-b6d1-b4bb6067471a", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(list(zone_df[\"item_id\"].unique()))\n", "print(type(item_id_list))\n", "item_id_list"]}, {"cell_type": "code", "execution_count": null, "id": "2b2e4551-1770-4786-b36c-53759f4b0b10", "metadata": {}, "outputs": [], "source": ["sender_facility_id = tuple(list(zone_df[\"mother_wh_facility_id\"].unique()))\n", "print(type(sender_facility_id))\n", "sender_facility_id"]}, {"cell_type": "code", "execution_count": null, "id": "faf9d3c9-a7e9-4587-a40d-f3c5dc2e77f2", "metadata": {}, "outputs": [], "source": ["child_facility_id_list = tuple(list(zone_df[\"child_wh_facility_id\"].unique()))\n", "print(type(child_facility_id_list))\n", "child_facility_id_list"]}, {"cell_type": "markdown", "id": "2999544b-b59a-496b-8651-51c280bd848f", "metadata": {}, "source": ["## Current inv -- use \"fe\" or \"be\" for inputs"]}, {"cell_type": "code", "execution_count": null, "id": "1ab43b6f-5a8c-4eec-bb92-d23919600734", "metadata": {}, "outputs": [], "source": ["def current_inv(outlet_type):\n", "\n", "    current_inv = read_sql_query(\n", "        f\"\"\"\n", "\n", "    with iii as (\n", "        select outlet_id, item_id, quantity as actual_quantity\n", "        from ims.ims_item_inventory\n", "        where lake_active_record and active = 1 and item_id in {item_id_list}\n", "    ),\n", "\n", "    iibi as (\n", "        select outlet_id, item_id, sum(quantity) as blocked_quantity\n", "        from ims.ims_item_blocked_inventory\n", "        where lake_active_record and active = 1 and blocked_type in (1,2,5) and item_id in {item_id_list}\n", "        group by 1,2\n", "    ),\n", "\n", "    id as (select item_id from supply_etls.item_details where item_id in {item_id_list} )\n", "\n", "\n", "    select iii.outlet_id, od.facility_id, od.hot_outlet_name, iii.item_id, \n", "        case when actual_quantity - coalesce(blocked_quantity, 0) <0 then 0 else actual_quantity - coalesce(blocked_quantity, 0) end as current_inventory\n", "    from iii\n", "    left join iibi on iibi.item_id = iii.item_id and iibi.outlet_id = iii.outlet_id\n", "    inner join id on id.item_id = iii.item_id\n", "    inner join supply_etls.outlet_details od on od.inv_outlet_id = iii.outlet_id and ars_check =1 and taggings = '{outlet_type}'\n", "    \n", "    \"\"\"\n", "    )\n", "    return current_inv\n", "    del current_inv"]}, {"cell_type": "code", "execution_count": null, "id": "6d0de421-5894-4b0a-b194-98994fea3d11", "metadata": {}, "outputs": [], "source": ["current_inv_be = current_inv(\"be\")"]}, {"cell_type": "code", "execution_count": null, "id": "a8bdd676-ddb7-4394-9b28-f5b32ddb5dfb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "5bf13462-0632-4918-ae82-455e32b737e7", "metadata": {}, "source": ["## Active & inactive items DOI & CPD"]}, {"cell_type": "code", "execution_count": null, "id": "9e604719-8491-4458-8728-efb371c9433e", "metadata": {"tags": []}, "outputs": [], "source": ["doi_cpd = read_sql_query(\n", "    f\"\"\"\n", "\n", "with\n", "outlet_details as\n", "    (select\n", "        store_type,\n", "        hot_outlet_id,\n", "        hot_outlet_name,\n", "        facility_id,\n", "        facility_name,\n", "        taggings\n", "\n", "            from supply_etls.outlet_details\n", "            ),\n", "\n", "item_details as\n", "    (select\n", "        item_id,\n", "        item_name,\n", "        brand_id,\n", "        brand_name,\n", "        l0_id,\n", "        l0_category,\n", "        l1_id,\n", "        l1_category,\n", "        l2_id,\n", "        l2_category,\n", "        p_type,\n", "        storage_type_id,\n", "        storage_type,\n", "        handling_type_id,\n", "        handling_type\n", "\n", "            from supply_etls.item_details\n", "        where item_id in {item_id_list}\n", "        --(10154887, 10154886, 10155192, 10155193, 10154889, 10155212, 10155209, 10155203, 10154891, 10154952, 10154953)\n", "        \n", "        ),\n", "        \n", "packaged_goods_inventory_and_availability as (\n", "\n", "select * from supply_etls.packaged_goods_inventory_and_availability where item_id in {item_id_list}\n", "    union\n", "select * from supply_etls.packaged_goods_inventory_and_availability_ti where item_id in {item_id_list}\n", "\n", "\n", "),\n", "\n", "\n", "raw_data as\n", "    (select\n", "        aa.be_hot_outlet_id,\n", "        aa.be_facility_id,\n", "        od.facility_name as be_facility_name,\n", "        aa.item_id,\n", "        id.item_name,\n", "        aa.be_transfer_cpd,\n", "        aa.be_net_inventory,\n", "        aa.backend_demand,\n", "        min(case when aa.buckets = 'Bucket x' then 1\n", "             when aa.buckets = 'Bucket a' then 2\n", "             when aa.buckets = 'Bucket b' then 3\n", "             else 4\n", "        end) as bucket,\n", "        sum(aa.net_inventory) as fe_net_inventory\n", "        \n", "        \n", "            from packaged_goods_inventory_and_availability aa\n", "            \n", "                join\n", "                    outlet_details od on od.hot_outlet_id = aa.be_hot_outlet_id\n", "                \n", "                join\n", "                    item_details id on id.item_id = aa.item_id\n", "                    \n", "                    group by 1,2,3,4,5,6,7,8\n", "    ),\n", "\n", "final_view as\n", "    (select\n", "        be_facility_id,\n", "        be_facility_name,\n", "        item_id,\n", "        item_name,\n", "        bucket,\n", "        be_transfer_cpd as be_cpd,\n", "        case when (backend_demand - be_net_inventory) <0 then 0 else (backend_demand - be_net_inventory) end as backend_demand,  -- YAHAN BE NET INVENTORY MINUS KAR RAHA HU DEMAND SE\n", "        be_net_inventory,\n", "        fe_net_inventory,\n", "        ((be_net_inventory * 1.00)/nullif(be_transfer_cpd,0)) as be_doi,\n", "        ((fe_net_inventory * 1.00)/nullif(be_transfer_cpd,0)) as fe_doi,\n", "        ((be_net_inventory + fe_net_inventory * 1.00)/nullif(be_transfer_cpd,0)) as total_doi\n", "        \n", "            from raw_data\n", "    )\n", "    \n", "        select * from final_view\n", "        where total_doi is not null\n", "        order by 1,2,3,4\n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a4e32800-883a-4b5b-92d1-343c3ae91501", "metadata": {}, "outputs": [], "source": ["doi_cpd"]}, {"cell_type": "code", "execution_count": null, "id": "ee1155a1-738a-4ad4-8304-85fc8e6d2cf8", "metadata": {}, "outputs": [], "source": ["# Calculating backend_demand, be_cpd, total_doi, all_cpd, all_doi\n", "\n", "\n", "def calculate_facility_metrics(facility_list):\n", "    facility_metrics = doi_cpd[doi_cpd[\"be_facility_id\"].isin(facility_list)]\n", "    facility_metrics = (\n", "        facility_metrics.groupby(\n", "            [\n", "                \"be_facility_id\",\n", "                \"be_facility_name\",\n", "                \"item_id\",\n", "                \"item_name\",\n", "                \"backend_demand\",\n", "            ]\n", "        )\n", "        .aggregate({\"be_cpd\": \"sum\", \"total_doi\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "\n", "    return facility_metrics"]}, {"cell_type": "markdown", "id": "9ea2f7c5-7958-4bb4-9264-9aa0c2be4201", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "390c5718-07ff-4a5e-b7b6-87ef5930e66d", "metadata": {}, "outputs": [], "source": ["vms_query = read_sql_query(\n", "    f\"\"\"\n", "\n", "with outlets as (\n", "select \n", "    facility_id,\n", "    hot_outlet_id\n", "\n", "from supply_etls.outlet_details \n", "    where 1 = 1\n", "    and ars_active = 1\n", "and taggings = 'be'\n", "),\n", "\n", "base as (\n", "select \n", "a.item_id,\n", "a.facility_id,\n", "a.vendor_id,\n", "tat_day,\n", "a.group_id,\n", "b.load_size,\n", "b.po_days,\n", "a.case_sensitivity_type,\n", "b.load_type,\n", "idd.inner_case_size,\n", "idd.outer_case_size,\n", "idd.weight_in_kg,\n", "case \n", "    when tot.on_invoice_margin_value >0 then (idd.variant_mrp - (idd.variant_mrp * (tot.on_invoice_margin_value/100)) )\n", "    when tot.landing_price >0 then tot.landing_price\n", "    else idd.variant_mrp * 0.80 end as landing_price,\n", "case\n", "    when a.case_sensitivity_type = 1 then idd.inner_case_size\n", "    when a.case_sensitivity_type = 2 then idd.outer_case_size\n", "    else 1 end as case_size\n", "\n", "\n", "from vms.vms_vendor_facility_alignment a\n", "left join vms.vms_vendor_new_pi_logic b on a.vendor_id = b.vendor_id and a.facility_id= b.facility_id and a.group_id= b.group_id and b.active = True and b.lake_active_record = True\n", "join outlets o on o.facility_id = a.facility_id\n", "join supply_etls.item_details idd on idd.item_id = a.item_id\n", "left join rpc.tot_margin tot on tot.item_id = a.item_id and tot.facility_id = a.facility_id and tot.vendor_id = a.vendor_id and tot.active = 1 and tot.lake_active_record\n", "\n", "where 1=1\n", "and a.active = 1\n", "and a.lake_active_record = True\n", "and a.facility_id in {sender_facility_id}\n", "and a.item_id in {item_id_list}\n", "\n", ")\n", "\n", "select \n", "    item_id,\n", "    facility_id as be_facility_id,\n", "    vendor_id,\n", "    tat_day,\n", "    group_id,\n", "    load_size,\n", "    load_type,\n", "    case_size,\n", "    po_days,\n", "    case\n", "        when load_type = 1 then landing_price\n", "        when load_type = 2 then weight_in_kg\n", "        when load_type = 3 then case_size\n", "        end as load_factor\n", "from base \n", "\n", "\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b63149e9-6711-4e03-a2f5-3c7cd5194e6a", "metadata": {}, "outputs": [], "source": ["vms_query"]}, {"cell_type": "code", "execution_count": null, "id": "76b346c2-3c07-4921-8252-68760da18a85", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "3aa56cd1-9ec1-47dc-b533-364542740c9c", "metadata": {"tags": []}, "source": ["## Building TEA"]}, {"cell_type": "code", "execution_count": null, "id": "bf126ec3-216e-421c-ab59-8ee5867bb764", "metadata": {}, "outputs": [], "source": ["def tea_build(sender_facility_id, child_facility_id_list):\n", "    sender = (\n", "        calculate_facility_metrics(sender_facility_id)\n", "        .drop(columns={\"backend_demand\", \"total_doi\"})\n", "        .rename(columns={\"be_cpd\": \"sender_cpd\"})\n", "    )\n", "    receievers = (\n", "        calculate_facility_metrics(child_facility_id_list + sender_facility_id)\n", "        .drop(columns={\"item_name\"})\n", "        .rename(\n", "            columns={\n", "                \"be_facility_id\": \"facility_id\",\n", "                \"be_facility_name\": \"facility_name\",\n", "                \"backend_demand\": \"connected_demand\",\n", "                \"be_cpd\": \"connected_cpd\",\n", "                \"total_doi\": \"connected_doi\",\n", "            }\n", "        )\n", "    )\n", "    sender_zone = pd.merge(\n", "        zone_df,\n", "        sender,\n", "        left_on=[\"mother_wh_facility_id\", \"item_id\"],\n", "        right_on=[\"be_facility_id\", \"item_id\"],\n", "        how=\"inner\",\n", "    )\n", "    tea = pd.merge(\n", "        sender_zone,\n", "        receievers,\n", "        left_on=[\"child_wh_facility_id\", \"item_id\"],\n", "        right_on=[\"facility_id\", \"item_id\"],\n", "        how=\"inner\",\n", "    )\n", "    tea = pd.merge(tea, vms_query, on=[\"item_id\", \"be_facility_id\"], how=\"left\")\n", "\n", "    del sender, receievers, sender_zone\n", "\n", "    return tea"]}, {"cell_type": "code", "execution_count": null, "id": "97ad3a65-9815-4591-b8c0-b804d9ffba7d", "metadata": {}, "outputs": [], "source": ["teatea = tea_build(sender_facility_id, child_facility_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "bd14ace9-6205-4fd9-ae93-3e3460675e38", "metadata": {"tags": []}, "outputs": [], "source": ["teatea"]}, {"cell_type": "markdown", "id": "b5fc527a-58a9-422d-9316-2a46c15b36be", "metadata": {}, "source": ["## Load Factor & PO Cycle calc"]}, {"cell_type": "code", "execution_count": null, "id": "db862b20-13d6-4d84-a395-d41e0a8a664e", "metadata": {}, "outputs": [], "source": ["def po_cycle(sender_facility_id):\n", "    teatea = tea_build(sender_facility_id, child_facility_id_list)\n", "    teateatea = (\n", "        teatea.groupby(\n", "            [\n", "                \"zone\",\n", "                \"be_facility_id\",\n", "                \"be_facility_name\",\n", "                \"item_id\",\n", "                \"item_name\",\n", "                \"vendor_id\",\n", "                \"tat_day\",\n", "                \"group_id\",\n", "                \"load_size\",\n", "                \"load_type\",\n", "                \"po_days\",\n", "                \"case_size\",\n", "                \"load_factor\",\n", "            ]\n", "        )\n", "        .aggregate(\n", "            {\n", "                \"connected_demand\": sum,\n", "                \"connected_cpd\": sum,\n", "            }\n", "        )\n", "        .reset_index()\n", "        .rename(columns={\"connected_demand\": \"sum_demand\", \"connected_cpd\": \"sum_cpd\"})\n", "    )\n", "\n", "    teateatea[\"monthly_requirement\"] = np.where(\n", "        teateatea[\"load_type\"] == 3,\n", "        (teateatea[\"sum_cpd\"] * 30) / teateatea[\"load_factor\"],\n", "        teateatea[\"sum_cpd\"] * 30 * teateatea[\"load_factor\"],\n", "    )\n", "    teateatea[\"new_po_cycle\"] = np.ceil(\n", "        (teateatea[\"monthly_requirement\"] / teateatea[\"load_size\"]) / 30\n", "    )\n", "    teateatea[\"po_days_count\"] = 7 - np.where(\n", "        teateatea[\"po_days\"].apply(lambda x: x.count(\",\") + 1) == 1,\n", "        0,\n", "        teateatea[\"po_days\"].apply(lambda x: x.count(\",\") + 1),\n", "    )\n", "    teateatea[\"final_po_cycle\"] = (\n", "        teateatea[[\"new_po_cycle\", \"po_days_count\"]].max(axis=1).fillna(1).astype(int)\n", "    )\n", "    teateatea[\"pre_final_demand\"] = np.ceil(\n", "        (teateatea[\"final_po_cycle\"] * teateatea[\"sum_cpd\"]) + teateatea[\"sum_demand\"]\n", "    )\n", "    teateatea = pd.merge(\n", "        teateate<PERSON>,\n", "        current_inv_be[[\"facility_id\", \"item_id\", \"current_inventory\"]],\n", "        left_on=[\"be_facility_id\", \"item_id\"],\n", "        right_on=[\"facility_id\", \"item_id\"],\n", "        how=\"left\",\n", "    ).drop(columns=\"facility_id\")\n", "    teateatea[\"final_demand\"] = np.where(\n", "        teateatea[\"current_inventory\"] > teateatea[\"pre_final_demand\"],\n", "        0,\n", "        teateatea[\"pre_final_demand\"],\n", "    )\n", "    teateatea[\"pre_final_demand_in_case\"] = abs(\n", "        np.ceil(teateatea[\"final_demand\"] / teateatea[\"case_size\"])\n", "        - (teateatea[\"final_demand\"] / teateatea[\"case_size\"])\n", "    )\n", "    teateatea[\"final_demand_in_case\"] = np.where(\n", "        teateatea[\"pre_final_demand_in_case\"] < 0.3,\n", "        np.ceil(teateatea[\"final_demand\"] / teateatea[\"case_size\"]),\n", "        np.floor(teateatea[\"final_demand\"] / teateatea[\"case_size\"]),\n", "    )\n", "    # teateatea['load_meeet'] = np.where(teateatea['load_type'] == 3, teateatea['final_demand_in_case'] / teateatea['load_factor'], teateatea['final_demand_in_case'] * teateatea['load_factor'])\n", "\n", "    teateatea[\"final_indent_qty_in_case\"] = (\n", "        teateatea[\"final_demand_in_case\"] * teateatea[\"case_size\"]\n", "    )\n", "    teateatea[\"vendor_indent_factor\"] = np.where(\n", "        teateatea[\"load_type\"] == 3,\n", "        teateatea[\"final_indent_qty_in_case\"] / teateatea[\"load_factor\"],\n", "        teateatea[\"final_indent_qty_in_case\"] * teateatea[\"load_factor\"],\n", "    )\n", "    teateatea[\"total_vendor_value\"] = teateatea.groupby(\n", "        [\"be_facility_id\", \"vendor_id\", \"group_id\"]\n", "    )[\"vendor_indent_factor\"].transform(np.sum)\n", "    teateatea[\"vendor_load_meet_perc\"] = teateatea[\"total_vendor_value\"] / teateatea[\"load_size\"]\n", "    teateatea[\"vendor_raise_flag\"] = np.where(\n", "        ((teateatea[\"vendor_load_meet_perc\"] >= 1) & (teateatea[\"final_indent_qty_in_case\"] > 0)),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    # assumed case fulfilment percentage 70-- agar 70% fulfill hogaya toh 30 extra karlo else round down\n", "\n", "    del teatea\n", "\n", "    return teateatea"]}, {"cell_type": "code", "execution_count": null, "id": "bb25124b-ee65-4fd9-a883-8ab1039a0231", "metadata": {}, "outputs": [], "source": ["output = po_cycle(sender_facility_id)\n", "output[\"updated_timestamp\"] = pd.Timestamp.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "output"]}, {"cell_type": "code", "execution_count": null, "id": "a80ac41c-9d84-4ebd-94e8-9f5f7e4da673", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(output, sheet_id, \"b2b_open_po_output\")"]}, {"cell_type": "code", "execution_count": null, "id": "957ebca2-423d-4737-97e2-442da82c05a3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}