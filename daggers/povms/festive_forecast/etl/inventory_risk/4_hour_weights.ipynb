{"cells": [{"cell_type": "code", "execution_count": null, "id": "d49c7649-3970-47e7-b858-5a3ce64ab5cc", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "c70c2839-8552-48ac-a036-87b7d6ad33ae", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "63c66f37-7519-490c-97df-54a1d568b946", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)\n", "\n", "# df = pd.read_csv(\"./Inventory Risk _ Festive Date Inputs - date_input (1).csv\")\n", "# df\n", "\n", "festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "007826fd-ac8d-4985-a5e6-76635b9972aa", "metadata": {}, "outputs": [], "source": ["# bau_start_date = '2023-08-23'\n", "# bau_end_date = '2023-09-06'\n", "# festival_start_date = '2023-09-06'\n", "# festival_end_date = '2023-09-20'\n", "# sale_end_date = '2024-09-14'\n", "# festival = 'GC'\n", "# gap_days = '354'"]}, {"cell_type": "code", "execution_count": null, "id": "cc202679-4bd3-44a3-b54d-02b0708a5d56", "metadata": {}, "outputs": [], "source": ["cpd_date = festive_date_data[\"cpd_date\"].iloc[-1]\n", "bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "festival = festive_date_data[\"festival\"].iloc[-1]\n", "gap_days = festive_date_data[\"gap_days\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "3a092b77-8362-4db6-8187-cb597be4a228", "metadata": {}, "outputs": [], "source": ["hour_weight = f\"\"\" \n", "\n", "with tea_tagging as (\n", "    select  \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "            inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "    from supply_etls.outlet_details\n", "    where ars_check=1\n", "),\n", "\n", "festive_details as (\n", "    \n", "    select be_facility_id as facility_id, \n", "        item_id, \n", "        festival_name as festival,\n", "        --sale_start_date, \n", "        --sale_end_date, \n", "        --cut_off_date, \n", "        --substitute_ptype, \n", "        --assortment_reason_type,\n", "        --rsto, \n", "        --rtv,\n", "        sum(planned_quantity) as planned_qty\n", "    from ars_etls.final_festival_planning\n", "    where festival_name is not null\n", "    and festival_name = '{festival}' \n", "    group by 1,2,3\n", "\n", "    --select \n", "    --    facility_id, item_id, \n", "    --        --storage, catg_segment,\n", "    --        --festival, festival_bau, sale_start, sale_end, cutt_off, \n", "    --        sum(planned_qty) as planned_qty\n", "    --from supply_etls.final_festival_planning\n", "    --where festival='{festival}'\n", "    --group by 1,2\n", "),\n", "\n", "sales as (\n", "    select od.order_create_dt_ist date_,  \n", "           extract(hour from order_create_ts_ist) hr,\n", "           tt.city_id, tt.city_name, icd.product_type as p_type, \n", "           sum(procured_quantity * multiplier) as qty_sold, \n", "           sum(total_procurement_price) as gmv\n", "    from dwh.fact_sales_order_item_details od\n", "    inner join\n", "     (select distinct ipr.product_id,\n", "                      case\n", "                          when ipr.item_id IS NULL THEN ipom_0.item_id\n", "                          else ipr.item_id\n", "                      end AS item_id,\n", "                      case \n", "                          when ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "                          else COALESCE(ipom_0.multiplier,1)\n", "                      end AS multiplier\n", "      from lake_rpc.item_product_mapping ipr\n", "      left join dwh.dim_item_product_offer_mapping ipom ON ipr.product_id = ipom.product_id\n", "      and ipr.item_id = ipom.item_id\n", "      left join dwh.dim_item_product_offer_mapping ipom_0 ON ipr.product_id = ipom_0.product_id) ipm ON od.product_id = ipm.product_id\n", "      inner join rpc.item_category_details icd ON icd.item_id = ipm.item_id\n", "      left join outlet_details tt on od.outlet_id = tt.inv_outlet_id \n", "     \n", "   where od.order_current_status = 'DELIVERED'\n", "     and od.order_type NOT IN ('InternalForwardOrder',\n", "                               'InternalReverseOrder',\n", "                               'DropShippingInternalReverseOrder')\n", "     and ((od.order_create_dt_ist between date('{festival_start_date}') and date('{festival_end_date}')))\n", "     and od.total_selling_price > 0\n", "     and icd.lake_active_record\n", "     group by 1,2,3,4,5\n", "),\n", "\n", "ptype as (\n", "    select distinct product_type \n", "    from rpc.item_category_details \n", "    where lake_active_record = true\n", "    and product_type not in (select distinct p_type from sales)\n", "),\n", "\n", "old_ptypes as (\n", "    select date_ + interval'{gap_days}' day as date_, \n", "    city_id, city_name, p_type as product_type, hr as hour_, \n", "    coalesce(try(hr_ptype_sales*1.00/total_ptype_sales),0) as hr_weight\n", "    from\n", "        (select date_ , city_id, city_name, p_type, hr, \n", "            qty_sold as hr_ptype_sales,\n", "            sum(qty_sold) over (partition by date_ , city_id, p_type) as total_ptype_sales\n", "        from sales)\n", "),\n", "\n", "new_ptypes as (\n", "    select date_, city_id, city_name, hr as hour_, \n", "    coalesce(try(hr_ptype_sales*1.00/total_ptype_sales),0) as hr_weight\n", "    from \n", "        (select date_ + interval'{gap_days}' day as date_, \n", "        city_id, city_name, hr, total_ptype_sales, \n", "        sum(hr_ptype_sales) hr_ptype_sales\n", "        from\n", "            (select date_ , city_id, city_name, p_type, hr, \n", "            qty_sold as hr_ptype_sales,\n", "            sum(qty_sold) over (partition by date_ , city_id) as total_ptype_sales\n", "            from \n", "            sales) \n", "        group by 1,2,3,4,5)\n", "),\n", "\n", "new as (\n", "    select n.date_, n.city_id, n.city_name, p.product_type, n.hour_, n.hr_weight\n", "    from new_ptypes n\n", "    cross join ptype p\n", ")\n", "\n", "select *,'{festival}' as festival_name\n", "from old_ptypes\n", "union\n", "select *,'{festival}' as festival_name\n", "from new\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3b29540b-ece1-4131-8864-7f6f38bceb6f", "metadata": {}, "outputs": [], "source": ["# event_data = []\n", "# for i in range(0, df.shape[0]):\n", "#     cpd_date = festive_date_data[\"cpd_date\"].iloc[i]\n", "#     bau_start_date = festive_date_data[\"bau_start_date\"].iloc[i]\n", "#     bau_end_date = festive_date_data[\"bau_end_date\"].iloc[i]\n", "#     festival_start_date = festive_date_data[\"festival_start_date\"].iloc[i]\n", "#     festival_end_date = festive_date_data[\"festival_end_date\"].iloc[i]\n", "#     sale_start_date = festive_date_data[\"sale_start_date\"].iloc[i]\n", "#     sale_end_date = festive_date_data[\"sale_end_date\"].iloc[i]\n", "#     festival = festive_date_data[\"festival\"].iloc[i]\n", "#     gap_days = festive_date_data[\"gap_days\"].iloc[i]\n", "#     prepared_query = hour_weight.format(\n", "#         cpd_date=cpd_date,\n", "#         bau_start_date=bau_start_date,\n", "#         bau_end_date=bau_end_date,\n", "#         festival_start_date=festival_start_date,\n", "#         festival_end_date=festival_end_date,\n", "#         sale_start_date=sale_start_date,\n", "#         sale_end_date=sale_end_date,\n", "#         festival=festival,\n", "#         gap_days=gap_days,\n", "#     )\n", "#     _data = read_sql_query(prepared_query, CON_TRINO)\n", "#     event_data.append(_data)\n", "# hour_weight = pd.concat(event_data, axis=0).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ab038ae5-4f3e-48e8-af91-61528fe3f217", "metadata": {}, "outputs": [], "source": ["# df_hour_weight = read_sql_query(hour_weight, CON_TRINO)\n", "# df_hour_weight"]}, {"cell_type": "code", "execution_count": null, "id": "4eca2490-58c6-4d8c-abd9-69955441bf6c", "metadata": {}, "outputs": [], "source": ["# df_hour_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b703fe43-242e-4a29-8a8c-4fdbd037d701", "metadata": {}, "outputs": [], "source": ["# df_hour_weight.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "65eb0d3d-4f72-4270-8cde-eda1fb41c4c3", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_hour_weight[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_hour_weight.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "17b16a26-bce1-4116-8d28-b7539fd64749", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"City ID\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Hour\"},\n", "    {\"name\": \"hr_weight\", \"type\": \"real\", \"description\": \"Hour Weight\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1e097ba1-c7d1-4c2c-ad88-79b61ee28b9a", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_forecast_hour_weight\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"product_type\",\n", "        \"hour_\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive hour weight forecast\",\n", "}\n", "\n", "to_trino(hour_weight, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}