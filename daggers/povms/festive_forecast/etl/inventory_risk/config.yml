alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-inventory-risk-dashboard
concurrency: 3
dag_name: inventory_risk
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: 1_be_ptype_forecast
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_store_distribution
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 3_item_weights
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 4_hour_weights
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 5_sale_inv_grn
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 6_store_item_level_forecast
  parameters: null
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: 7_store_item_risk
  parameters: null
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: 8_store_ptype_risk
  parameters: null
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: 9_city_item_risk
  parameters: null
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: 10_city_ptype_risk
  parameters: null
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: 11_be_item_risk
  parameters: null
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: 12_be_ptype_risk
  parameters: null
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: 13_pan_india_item_risk
  parameters: null
  tag: fourth
- executor_config:
    load_type: tiny
    node_type: spot
  name: 14_pan_india_ptype_risk
  parameters: null
  tag: fourth
owner:
  email: <EMAIL>
  slack_id: U06EEDWNCJF
path: povms/festive_forecast/etl/inventory_risk
paused: false
pool: povms_pool
project_name: festive_forecast
schedule:
  end_date: '2025-03-28T00:00:00'
  interval: 35 * * * *
  start_date: '2025-03-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 44
