{"cells": [{"cell_type": "code", "execution_count": null, "id": "d68efcb7-349f-47dd-88fd-6104a2fa0b93", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "77a647bc-f311-4e53-9a40-5dfd1919b944", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "1c15da05-5105-4e7c-bda5-4bf2ec48647c", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)\n", "\n", "# df = pd.read_csv(\"./Inventory Risk _ Festive Date Inputs - date_input (1).csv\")\n", "# df"]}, {"cell_type": "code", "execution_count": null, "id": "80da47c1-4b02-4bab-924e-e0fa5c5f320b", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "af92a676-817d-495b-ac77-e7c09eda3b63", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-08-31' #festive_date_data['cpd_date'].iloc[-1]\n", "# bau_start_date = '2023-08-23'#festive_date_data['bau_start_date'].iloc[-1]\n", "# bau_end_date = '2023-09-06'#festive_date_data['bau_end_date'].iloc[-1]\n", "# festival_start_date = '2023-09-06' #festive_date_data['festival_start_date'].iloc[-1]\n", "# festival_end_date = '2023-09-20' #festive_date_data['festival_end_date'].iloc[-1]\n", "# sale_start_date = '2024-09-01' #festive_date_data['sale_start_date'].iloc[-1]\n", "# sale_end_date = '2024-09-14' #festive_date_data['sale_end_date'].iloc[-1]\n", "# festival = 'GC' #festive_date_data['festival'].iloc[-1]\n", "# gap_days = '354' #festive_date_data['gap_days'].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "e06c5822-0e1c-4554-a74d-c7327afd96eb", "metadata": {}, "outputs": [], "source": ["cpd_date = festive_date_data[\"cpd_date\"].iloc[-1]\n", "bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "festival = festive_date_data[\"festival\"].iloc[-1]\n", "gap_days = festive_date_data[\"gap_days\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "07679c12-3e88-40b8-9b05-1a9fbf180b32", "metadata": {}, "outputs": [], "source": ["store_dist = f\"\"\"\n", "\n", "with ptype_distribution_raw as (\n", "    select *\n", "    from supply_etls.keyword_ptype_distribution\n", "    where date_ between (date('{festival_start_date}') - interval'7'day) and (date('{festival_end_date}') + interval'7'day)\n", "),\n", "\n", "ptype_distribution0 as (\n", "    select base.date_,\n", "        base.keyword,\n", "        base.product_type,\n", "        coalesce(devices,0) as devices,\n", "        coalesce(total_devices,0) as total_devices,\n", "        coalesce(sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following),0) as nearby_devices,\n", "        coalesce(sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following),0) as nearby_total_devices,\n", "        coalesce(try((1.00 * sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following)) / (1.00 * sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following))),0) as percentage_attri\n", "        \n", "    from (\n", "            (select distinct date_ from ptype_distribution_raw) \n", "                cross join \n", "            (select distinct keyword, product_type from ptype_distribution_raw)\n", "        ) base\n", "    left join ptype_distribution_raw pdr on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "), \n", "\n", "ptype_distribution1 as (\n", "    select date_,\n", "        keyword,\n", "        product_type,\n", "        devices,\n", "        total_devices,\n", "        nearby_devices,\n", "        nearby_total_devices,\n", "        percentage_attri,\n", "        rank() over (partition by date_, keyword order by percentage_attri desc) as key_rank\n", "    from ptype_distribution0\n", "    \n", "),\n", "\n", "ptype_distribution as (\n", "    select date_,\n", "        keyword,\n", "        product_type,\n", "        devices,\n", "        total_devices,\n", "        nearby_devices,\n", "        nearby_total_devices,\n", "        percentage_attri,\n", "        key_rank\n", "    from ptype_distribution1\n", "    where (key_rank<=1 or percentage_attri > 0.1)\n", "),\n", "\n", "\n", "\n", "keys_raw as (\n", "    select distinct date_,\n", "        keyword,\n", "        outlet_id,\n", "        search_device_count\n", "    from supply_etls.merchant_dod_keyword_searches_v2\n", "    where date_ between date('{festival_start_date}') and date('{festival_end_date}')\n", "),\n", "\n", "atc_split as (\n", "    select date_,\n", "        product_type,\n", "        1.000000 * sum(search_atc_device_count) as search_atcs,\n", "        1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "        try((1.000000 * sum(search_atc_device_count))/((1.000000 * sum(search_atc_device_count)) + (1.000000 * sum(non_search_atc_device_count)))) as split\n", "    from supply_etls.ptype_store_atc_channel_split_v2\n", "    where (date_ between date('{festival_start_date}') and date('{festival_end_date}'))\n", "    group by 1,2\n", "),\n", "\n", "day_on_day_searches as (\n", "    select keys_raw.date_,\n", "        outlet_id,\n", "        coalesce(ptype_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "        split as search_vs_non_search_atc_split,\n", "        sum(keys_raw.search_device_count) AS search_device_count,\n", "        sum(keys_raw.search_device_count * percentage_attri) AS prob_adjusted_search_device_count\n", "    from keys_raw\n", "    left join ptype_distribution on keys_raw.date_ = ptype_distribution.date_ and keys_raw.keyword = ptype_distribution.keyword\n", "    left join atc_split on keys_raw.date_ = atc_split.date_ and ptype_distribution.product_type = atc_split.product_type\n", "    group by 1,2,3,4\n", "),\n", "\n", "searches_final as (\n", "    select outlet_id,\n", "        product_type,\n", "        avg(search_device_count) as avg_searches_old,\n", "        avg(prob_adjusted_search_device_count) as avg_searches_adj_old\n", "    from day_on_day_searches\n", "    group by 1,2\n", "),\n", "\n", "carts_old as (\n", "    select date(cart_checkout_ts_ist) as date_,\n", "        fsoid.outlet_id, \n", "        count(distinct order_id) as orders_old\n", "    from dwh.fact_sales_order_item_details fsoid\n", "    where order_current_status = 'DELIVERED'\n", "        and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "        and cart_checkout_ts_ist between date('{bau_start_date}') and date('{bau_end_date}')\n", "        and order_create_dt_ist between date('{bau_start_date}') and date('{bau_end_date}')\n", "    group by 1,2\n", "),\n", "\n", "carts_final_old as (\n", "    select outlet_id, \n", "        avg(orders_old) as avg_orders_old \n", "    from carts_old \n", "    group by 1 \n", "    having  \n", "    avg(orders_old) > 500 \n", "),\n", "\n", "carts_new as (\n", "    select date(cart_checkout_ts_ist) as date_,\n", "        fsoid.outlet_id, \n", "        count(distinct order_id) as orders\n", "    from dwh.fact_sales_order_item_details fsoid\n", "    where order_current_status = 'DELIVERED'\n", "        and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "        and date(cart_checkout_ts_ist) between (date('{cpd_date}') - interval '14' day) and date('{cpd_date}')\n", "        and order_create_dt_ist between (date('{cpd_date}') - interval '14' day) and date('{cpd_date}')\n", "    group by 1,2\n", ")\n", "\n", ",\n", "\n", "carts_final_new as (\n", "    select outlet_id,\n", "        avg(orders) as avg_orders\n", "    from carts_new\n", "    group by 1\n", "),\n", "\n", "\n", "cross_table as (\n", "    select distinct cfn.outlet_id, t1.product_type from carts_final_new cfn\n", "    cross join (select distinct product_type from searches_final) t1\n", "),\n", "    \n", "final_base as (   \n", "    select ct.outlet_id,\n", "        co.name as outlet_name,\n", "        ct.product_type,\n", "        'PAN' as nation_name,\n", "        cl.id as city_id,\n", "        cl.name as city_name,\n", "        cfo.avg_orders_old,\n", "        cfn.avg_orders,\n", "        sf.avg_searches_old,\n", "        try(sf.avg_searches_adj_old / cfo.avg_orders_old) as avg_searches_pen_old,\n", "        avg(cfo.avg_orders_old) over (partition by cl.name, ct.product_type) as avg_city_orders_old,\n", "        avg(sf.avg_searches_adj_old) over (partition by cl.name, ct.product_type) as avg_city_searches_old,\n", "        avg(coalesce(try(sf.avg_searches_adj_old / cfo.avg_orders_old),0)) over (partition by cl.name, ct.product_type) as avg_city_searches_pen_old,\n", "\n", "        avg(cfo.avg_orders_old) over (partition by ct.product_type) as avg_nation_orders_old,\n", "        avg(sf.avg_searches_adj_old) over (partition by ct.product_type) as avg_nation_searches_old,\n", "        avg(coalesce(try(sf.avg_searches_adj_old/cfo.avg_orders_old),0)) over (partition by ct.product_type) as avg_nation_searches_pen_old\n", "\n", "        \n", "    from cross_table ct\n", "    left join carts_final_new cfn on ct.outlet_id = cfn.outlet_id\n", "    left join carts_final_old cfo on ct.outlet_id = cfo.outlet_id\n", "    left join searches_final sf on ct.outlet_id = sf.outlet_id and ct.product_type = sf.product_type\n", "    \n", "    left join retail.console_outlet co on co.id = ct.outlet_id\n", "    left join po.physical_facility_outlet_mapping pfom on pfom.outlet_id  = ct.outlet_id\n", "    left join retail.console_location cl on cl.id = pfom.city_id\n", "),\n", "\n", "final_to_final_base as (\n", "    select \n", "    outlet_id,\n", "    outlet_name,\n", "    product_type,\n", "    city_name,\n", "    avg_orders,\n", "    avg_orders_old,\n", "    avg_searches_old,\n", "    cast(avg_searches_pen_old as real) as avg_searches_pen_old,\n", "    avg_city_orders_old,\n", "    avg_city_searches_old,\n", "    avg_city_searches_pen_old,\n", "    avg_nation_orders_old,\n", "    avg_nation_searches_old,\n", "    avg_nation_searches_pen_old,\n", "    case\n", "        when ((avg_searches_pen_old is not null)) then (avg_searches_pen_old) \n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is not null)) then (avg_city_searches_pen_old) -- new store handling but existing city\n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is null) and (avg_nation_searches_pen_old is not null)) then (avg_nation_searches_pen_old) -- new store handling but non - existing city\n", "        else (avg_nation_searches_pen_old)\n", "    end as derived_search_pen\n", "    \n", "\n", "from final_base fb \n", "where fb.avg_orders is not null\n", "    and fb.avg_orders > 0\n", "\n", "),\n", "\n", "percentile_values as (\n", "    select product_type,\n", "        city_name,\n", "        avg(derived_search_pen) as avg_search_pen,\n", "        stddev_pop(derived_search_pen) as std_dev_search_pen\n", "    from final_to_final_base\n", "    group by 1,2\n", "),\n", "\n", "item_product_mapping as (\n", "    select item_id, product_id, multiplier\n", "    from dwh.dim_item_product_offer_mapping\n", "    where is_current\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "            inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "    from supply_etls.outlet_details\n", "    where ars_check=1\n", "),\n", "\n", "item_details as (\n", "    select \n", "        item_id, item_name, l0_id, l0_category, \n", "                l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "    from supply_etls.item_details\n", "    where assortment_type='Packaged Goods'\n", "),\n", "\n", "festive_details as (\n", "  \n", "    select be_facility_id as facility_id, \n", "        item_id, \n", "        festival_name as festival,\n", "        --sale_start_date, \n", "        --sale_end_date, \n", "        --cut_off_date, \n", "        --substitute_ptype, \n", "        --assortment_reason_type,\n", "        --rsto, \n", "        --rtv,\n", "        sum(planned_quantity) as planned_qty\n", "    from ars_etls.final_festival_planning\n", "    where festival_name is not null\n", "    and festival_name = '{festival}' \n", "    group by 1,2,3\n", "  \n", "  --select \n", "  --      facility_id, item_id, storage, catg_segment,\n", "  --          festival, festival_bau, sale_start, sale_end, cutt_off, planned_qty \n", "  --  from supply_etls.final_festival_planning\n", "  --  where festival='{festival}'\n", "),\n", "\n", "festive_core as (\n", "    select distinct id.p_type\n", "    from festive_details fd\n", "    inner join item_details id on id.item_id=fd.item_id\n", "),\n", "\n", "festival_ptype_plan_qty as (\n", "    select facility_id, id.p_type, festival, sum(planned_qty) as planned_qty\n", "    from festive_details fd\n", "    inner join item_details id on id.item_id=fd.item_id\n", "    group by 1,2,3\n", "    \n", "),\n", "\n", "festive_ptype_weights as\n", "(\n", "    select \n", "    p_type, festival,\n", "    try(1.00000000*planned_qty/sum(planned_qty) over (order by p_type)) as ptype_weight\n", "    from\n", "    (select \n", "        p_type,festival,\n", "        sum(planned_qty) as planned_qty\n", "    from festival_ptype_plan_qty \n", "    group by 1,2)\n", "),\n", "\n", "tea_tagging as (\n", "    select \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "sales as (\n", "    select date(cart_checkout_ts_ist) as date_, fs.outlet_id, ip.item_id, id.p_type, tea.be_hot_outlet_id,\n", "            sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price) as gmv\n", "    from dwh.fact_sales_order_item_details fs\n", "    inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "    inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "    inner join item_details id on id.item_id=ip.item_id\n", "    inner join tea_tagging tea on tea.item_id=ip.item_id and tea.fe_facility_id=od.facility_id\n", "    where fs.order_create_dt_ist between date('{sale_start_date}') and date('{sale_end_date}')\n", "    and fs.order_current_status = 'DELIVERED'\n", "    group by 1,2,3,4,5\n", "),\n", "\n", "ptype_sales as (\n", "    select date_, be_hot_outlet_id, p_type, sum(qty_sold) as qty_sold, sum(gmv) as gmv\n", "    from sales\n", "    group by 1,2,3\n", "),\n", "\n", "final1 as (\n", "    select outlet_id,\n", "         outlet_name,\n", "        ff.product_type,\n", "        ff.city_name,\n", "        avg_orders,\n", "        case \n", "            when derived_search_pen < avg_search_pen - 1.96 * std_dev_search_pen then avg_orders * (avg_search_pen - 1.96 * std_dev_search_pen)\n", "            when derived_search_pen > avg_search_pen + 1.96 * std_dev_search_pen then avg_orders * (avg_search_pen + 1.96 * std_dev_search_pen)\n", "        else avg_orders * derived_search_pen\n", "        end as val\n", "        \n", "    from final_to_final_base ff\n", "    left join percentile_values pv on ff.city_name = pv.city_name and ff.product_type = pv.product_type\n", "    where  ff.product_type in (select * from festive_core)\n", "),\n", "\n", "\n", "store_dist as (\n", "    select outlet_id,\n", "        co.tax_location_id,\n", "        outlet_name,\n", "        city_name,\n", "        fpw.festival as festival_name,\n", "        avg(avg_orders) as avg_orders,\n", "        avg(round(val, 4)) as avg_val,\n", "        sum(round(val, 4)) as sum_val,\n", "        array_join(array_agg(product_type || ' : ' || cast(fpw.ptype_weight as varchar) || ' : ' || cast(round(val, 4) as varchar) order by product_type), ', ') as concatenated_items,\n", "        try(sum(fpw.ptype_weight*round(val, 4))/sum(fpw.ptype_weight)) as weighted_val\n", "    from final1\n", "    left join retail.console_outlet co on co.id = final1.outlet_id\n", "    left join festive_ptype_weights fpw on fpw.p_type=final1.product_type\n", "    group by 1,2,3,4,5\n", ")\n", "\n", "select\n", "od.facility_id as fe_facility_id, od.facility_name as fe_facility_name, be_facility_id, od_be.facility_name as be_facility_name, festival_name,\n", "weighted_val/sum(weighted_val) over (partition by be_facility_id) as store_ratio\n", "from store_dist sd\n", "    inner join outlet_details od on od.hot_outlet_id = sd.outlet_id\n", "    inner join (select distinct fe_outlet_id, fe_facility_id, be_hot_outlet_id, be_inv_outlet_id, be_facility_id from tea_tagging) tea on tea.fe_facility_id=od.facility_id\n", "    inner join outlet_details od_be on od_be.facility_id = tea.be_facility_id\n", "where od_be.facility_name not like '%%CPC%%' and od_be.facility_name not like '%%Perishable%%' \n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "dce6251a-c6aa-4a47-8d7d-13ca553a61c6", "metadata": {}, "outputs": [], "source": ["# event_data = []\n", "# for i in range(0, df.shape[0]):\n", "#     cpd_date = festive_date_data[\"cpd_date\"].iloc[i]\n", "#     bau_start_date = festive_date_data[\"bau_start_date\"].iloc[i]\n", "#     bau_end_date = festive_date_data[\"bau_end_date\"].iloc[i]\n", "#     festival_start_date = festive_date_data[\"festival_start_date\"].iloc[i]\n", "#     festival_end_date = festive_date_data[\"festival_end_date\"].iloc[i]\n", "#     sale_start_date = festive_date_data[\"sale_start_date\"].iloc[i]\n", "#     sale_end_date = festive_date_data[\"sale_end_date\"].iloc[i]\n", "#     festival = festive_date_data[\"festival\"].iloc[i]\n", "#     gap_days = festive_date_data[\"gap_days\"].iloc[i]\n", "#     prepared_query = store_dist.format(\n", "#         cpd_date=cpd_date,\n", "#         bau_start_date=bau_start_date,\n", "#         bau_end_date=bau_end_date,\n", "#         festival_start_date=festival_start_date,\n", "#         festival_end_date=festival_end_date,\n", "#         sale_start_date=sale_start_date,\n", "#         sale_end_date=sale_end_date,\n", "#         festival=festival,\n", "#         gap_days=gap_days,\n", "#     )\n", "#     _data = read_sql_query(prepared_query, CON_TRINO)\n", "#     event_data.append(_data)\n", "# store_dist = pd.concat(event_data, axis=0).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8eade977-8d45-48a7-81b6-dc5238d71616", "metadata": {}, "outputs": [], "source": ["# df_store_dist = read_sql_query(store_dist, CON_TRINO)\n", "# df_store_dist"]}, {"cell_type": "code", "execution_count": null, "id": "483e1d6f-185f-4ba2-a787-9a73328da376", "metadata": {}, "outputs": [], "source": ["# df_store_dist.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eae03880-cb7b-4bb3-a445-3e3b59cf82a1", "metadata": {}, "outputs": [], "source": ["# df_store_dist.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "320f2640-aa57-4efa-b7e8-622d94d4b370", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_store_dist[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_store_dist.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "0814978c-f4c8-4829-8bad-fbf27925137b", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"Dark Store ID\"},\n", "    {\"name\": \"fe_facility_name\", \"type\": \"varchar\", \"description\": \"Dark Store Name\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Warehouse ID\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"Warehouse Name\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"store_ratio\", \"type\": \"real\", \"description\": \"Store Ratios\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5a5db7cf-4ae3-467a-8976-3b193cbba1b2", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_forecast_store_dist\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"fe_facility_id\",\n", "        \"fe_facility_name\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}\n", "\n", "to_trino(store_dist, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}