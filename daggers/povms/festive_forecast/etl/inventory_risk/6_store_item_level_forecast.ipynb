{"cells": [{"cell_type": "code", "execution_count": null, "id": "14d182fe-6c91-49a8-aa53-755a02d0ab58", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8a2f3d9e-b11e-4c6d-8f3d-5ca1128ac6ea", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "9460999d-6d3b-4c91-a62e-58934633f694", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d89b4703-92e6-481e-8418-996b30fa10c5", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b4cd797a-cade-4ad0-9571-3792b59dd616", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "c8176b0f-a803-4c7e-ae33-48009b024728", "metadata": {}, "outputs": [], "source": ["cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "40fb2e1a-2fa7-4972-a96b-1d28248b74ce", "metadata": {}, "outputs": [], "source": ["bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "festival = festive_date_data[\"festival\"].iloc[-1]\n", "gap_days = festive_date_data[\"gap_days\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "b652be96-481d-456a-ae55-d1e8b5e5c5e3", "metadata": {}, "outputs": [], "source": ["store_item_forcast = f\"\"\" \n", "\n", "with festive_forecast_be_ptype as (\n", "    select \n", "        date_,\n", "        festival_name,\n", "        be_facility_id,\n", "        be_name,\n", "        product_type,\n", "        be_ptype_forecast_cpd,\n", "        be_ptype_forecast_planned,\n", "        be_ptype_forecast\n", "    from supply_etls.festive_forecast_be_ptype\n", "    where festival_name = '{festival}'\n", "),\n", "\n", "store_dist as (\n", "    select\n", "        fe_facility_id, \n", "        festival_name,\n", "        fe_facility_name, \n", "        be_facility_id, \n", "        be_facility_name,\n", "        store_ratio\n", "    from supply_etls.festive_forecast_store_dist\n", "    where festival_name = '{festival}'\n", "),\n", "\n", "item_weights as (\n", "    select \n", "        be_facility_id,\n", "        festival_name,\n", "        product_type,\n", "        item_id,\n", "        item_name,\n", "        item_weight\n", "    from supply_etls.festive_forecast_item_weight\n", "    where festival_name = '{festival}'\n", "),\n", "\n", "hour_weights as (\n", "    select \n", "        date_,\n", "        festival_name,\n", "        city_id,\n", "        city_name,\n", "        product_type,\n", "        hour_,\n", "        hr_weight\n", "    from supply_etls.festive_forecast_hour_weight\n", "    where festival_name = '{festival}'\n", "),\n", "\n", "\n", "sale_inv_grn as (\n", "    select\n", "        date_,\n", "        hour_,\n", "        festival_name,\n", "       -- city_id,\n", "       -- city_name,\n", "       -- fe_outlet_id,\n", "        fe_facility_id, \n", "        item_id,\n", "        current_inventory,\n", "        grn_qty,\n", "        gmv,\n", "        qty_sold,\n", "        potential_sold_qty\n", "    from supply_etls.festive_forecast_sale_inv_grn\n", "    where festival_name = '{festival}'\n", "),\n", "\n", "festive_details as (\n", "    \n", "    select be_facility_id as facility_id, \n", "        item_id, \n", "        festival_name as festival,\n", "        --sale_start_date, \n", "        --sale_end_date, \n", "        --cut_off_date, \n", "        --substitute_ptype, \n", "        --assortment_reason_type,\n", "        --rsto, \n", "        --rtv,\n", "        sum(planned_quantity) as planned_qty\n", "    from ars_etls.final_festival_planning\n", "    where festival_name is not null\n", "    and festival_name = '{festival}' \n", "    group by 1,2,3\n", "\n", "    --select \n", "    --    facility_id, item_id, \n", "    --        --storage, catg_segment,\n", "    --        --festival, festival_bau, sale_start, sale_end, cutt_off, \n", "    --        sum(planned_qty) as planned_qty\n", "    --from supply_etls.final_festival_planning\n", "    --where festival='{festival}'\n", "    --group by 1,2\n", "),\n", "\n", "tea_tagging as (\n", "    select  \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "    and item_id in (select distinct item_id from festive_details)\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "            inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "    from supply_etls.outlet_details\n", "    where ars_check=1\n", ")\n", "\n", "select\n", "a.date_, \n", "a.festival_name,\n", "a.be_facility_id,\n", "a.be_name,\n", "f.city_id, \n", "f.city_name,\n", "b.fe_facility_id,\n", "f.inv_outlet_id as fe_outlet_id,\n", "b.fe_facility_name,\n", "a.product_type,\n", "c.item_id,\n", "c.item_name,\n", "d.hour_,\n", "d.hr_weight,\n", "c.item_weight,\n", "b.store_ratio,\n", "e.current_inventory,\n", "e.grn_qty,\n", "e.gmv,\n", "e.qty_sold,\n", "e.potential_sold_qty as potential_sold_qty_daily,\n", "(a.be_ptype_forecast_cpd*store_ratio*item_weight*hr_weight*1.0000) as store_item_hour_forecast_cpd,\n", "(a.be_ptype_forecast_planned*store_ratio*item_weight*hr_weight*1.0000) as store_item_hour_forecast_planned,\n", "(a.be_ptype_forecast*store_ratio*item_weight*hr_weight*1.0000) as store_item_hour_forecast\n", "from \n", "festive_forecast_be_ptype a\n", "join store_dist b on a.be_facility_id=b.be_facility_id and a.festival_name=b.festival_name\n", "join outlet_details f on f.facility_id= b.fe_facility_id\n", "join item_weights c on c.be_facility_id = a.be_facility_id and a.product_type = c.product_type and a.festival_name=c.festival_name\n", "join hour_weights d on a.date_ = d.date_ and d.city_id = f.city_id and a.product_type = d.product_type and a.festival_name=d.festival_name\n", "join tea_tagging tea on tea.item_id=c.item_id and tea.be_facility_id=a.be_facility_id and tea.fe_facility_id=b.fe_facility_id\n", "left join sale_inv_grn e on b.fe_facility_id = e.fe_facility_id and a.date_ = e.date_ and c.item_id = e.item_id and d.hour_= e.hour_ and a.festival_name=e.festival_name\n", "where a.date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "807f8b65-eb95-4f69-b924-1b8155c3a687", "metadata": {}, "outputs": [], "source": ["# event_data = []\n", "# for i in range(0, df.shape[0]):\n", "#     cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[i])\n", "#     bau_start_date = festive_date_data[\"bau_start_date\"].iloc[i]\n", "#     bau_end_date = festive_date_data[\"bau_end_date\"].iloc[i]\n", "#     festival_start_date = festive_date_data[\"festival_start_date\"].iloc[i]\n", "#     festival_end_date = festive_date_data[\"festival_end_date\"].iloc[i]\n", "#     sale_start_date = festive_date_data[\"sale_start_date\"].iloc[i]\n", "#     sale_end_date = festive_date_data[\"sale_end_date\"].iloc[i]\n", "#     festival = festive_date_data[\"festival\"].iloc[i]\n", "#     gap_days = festive_date_data[\"gap_days\"].iloc[i]\n", "#     prepared_query = store_item_forcast.format(\n", "#         cpd_date=cpd_date,\n", "#         bau_start_date=bau_start_date,\n", "#         bau_end_date=bau_end_date,\n", "#         festival_start_date=festival_start_date,\n", "#         festival_end_date=festival_end_date,\n", "#         sale_start_date=sale_start_date,\n", "#         sale_end_date=sale_end_date,\n", "#         festival=festival,\n", "#         gap_days=gap_days,\n", "#     )\n", "#     _data = read_sql_query(prepared_query, CON_TRINO)\n", "#     event_data.append(_data)\n", "#     store_item_forcast = pd.concat(event_data, axis=0).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f578a8c8-6d8a-4035-9928-5b4d7dee7234", "metadata": {}, "outputs": [], "source": ["# df_store_item_forcast = read_sql_query(store_item_forcast, CON_TRINO)\n", "# df_store_item_forcast"]}, {"cell_type": "code", "execution_count": null, "id": "dc012563-1936-4038-ba37-d38054b7c989", "metadata": {}, "outputs": [], "source": ["# df_store_item_forcast.head()"]}, {"cell_type": "code", "execution_count": null, "id": "05a0c696-4974-4def-a546-6df602b76e86", "metadata": {}, "outputs": [], "source": ["# df_store_item_forcast.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "1d6cccd5-a5fa-46df-924d-7f96249a8235", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_store_item_forcast[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_store_item_forcast.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "467a28c1-04e5-4ce7-8c50-c0efd44c7055", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\n", "        \"name\": \"be_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Warehouse Facility ID\",\n", "    },\n", "    {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"Warehouse Name\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"City ID\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\n", "        \"name\": \"fe_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store Facility ID\",\n", "    },\n", "    {\"name\": \"fe_outlet_id\", \"type\": \"integer\", \"description\": \"Dark Store Outlet ID\"},\n", "    {\"name\": \"fe_facility_name\", \"type\": \"varchar\", \"description\": \"Dark Store Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"Item Name\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Hour\"},\n", "    {\"name\": \"hr_weight\", \"type\": \"real\", \"description\": \"Hour Weight\"},\n", "    {\"name\": \"item_weight\", \"type\": \"real\", \"description\": \"Item Weight\"},\n", "    {\"name\": \"store_ratio\", \"type\": \"real\", \"description\": \"Store Ratio\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"real\", \"description\": \"Hourly Inventory\"},\n", "    {\"name\": \"grn_qty\", \"type\": \"real\", \"description\": \"Hourly GRN Inventory\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"GMV\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"Hourly Sales\"},\n", "    {\n", "        \"name\": \"potential_sold_qty_daily\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Day Potential Sales\",\n", "    },\n", "    {\n", "        \"name\": \"store_item_hour_forecast_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis CPD\",\n", "    },\n", "    {\n", "        \"name\": \"store_item_hour_forecast_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis Planned QTY\",\n", "    },\n", "    {\n", "        \"name\": \"store_item_hour_forecast\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d1d1116d-46e6-4efd-a5ad-f39a4422056f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_forecast_store_item\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"be_facility_id\",\n", "        \"be_name\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"fe_facility_id\",\n", "        \"fe_outlet_id\",\n", "        \"fe_facility_name\",\n", "        \"product_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\", \"date_\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}\n", "\n", "to_trino(store_item_forcast, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}