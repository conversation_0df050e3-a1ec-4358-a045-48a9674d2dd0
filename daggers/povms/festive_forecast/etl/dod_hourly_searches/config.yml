alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: dod_hourly_searches
dag_type: etl
escalation_priority: low
execution_timeout: 1000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: low
    node_type: spot
  name: 1_city_keyword_ptype_distribution
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: 1_merchant_dod_hourly_keyword_searches
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 1_store_dod_hourly_metrics
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: first
- executor_config:
    load_type: low
    node_type: spot
  name: 2_city_keyword_ptype_distribution_normalised
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: second
- executor_config:
    load_type: low
    node_type: spot
  name: 2_ptype_hourly_store_atc_channel_split
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_merchant_dod_hourly_keyword_searches_ptype
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: second
- executor_config:
    load_type: low
    node_type: spot
  name: 3_outlet_hourly_ptype_search_and_sales
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: third
- executor_config:
    load_type: low
    node_type: spot
  name: 3_outlet_hourly_ptype_search_and_sales_v1
  parameters: null
  retries: 3
  retry_delay_in_seconds: 30
  tag: third
owner:
  email: <EMAIL>
  slack_id: U0721GE89QV
path: povms/festive_forecast/etl/dod_hourly_searches
paused: false
pool: povms_pool
project_name: festive_forecast
schedule:
  end_date: '2025-09-21T00:00:00'
  interval: 35 21 * * *
  start_date: '2025-05-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 6
