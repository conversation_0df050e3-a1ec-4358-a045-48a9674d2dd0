{"cells": [{"cell_type": "code", "execution_count": null, "id": "05014746-67a1-4e6d-868a-5109c78edd1a", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "# import matplotlib.pyplot as plt\n", "from itertools import product\n", "from pytz import timezone\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "51149822-67f6-417b-b13f-efb63fd71233", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "71ee3c5e-4de4-4a8f-8c27-55bdb104df31", "metadata": {}, "outputs": [], "source": ["default_mode = \"run\"\n", "mode = default_mode"]}, {"cell_type": "code", "execution_count": null, "id": "05b9ad13-249d-4ecf-a2f3-aedd5796d218", "metadata": {}, "outputs": [], "source": ["current_date_time = datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "id": "1da08620-a8d7-41e7-931b-c55649e9dd9d", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"ptype_hourly_store_atc_channel_split\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"merchant_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"product_type\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"product_type_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"search_atc_session_count\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"search_atc_device_count\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"search_quantity_added\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"non_search_atc_session_count\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"non_search_atc_device_count\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"non_search_quantity_added\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"hour\",\n", "        \"merchant_id\",\n", "        \"product_type_id\",\n", "    ],\n", "    # \"sortkey\": [\"at_date_ist\",\"traits__city_name\",\"keyword\"],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"tag_name\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"ptype_hourly_store_atc_channel_split\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ef804d90-c71c-4607-8d5a-4a31ce6982ca", "metadata": {}, "outputs": [], "source": ["# query_info_df= pd.read_csv(\"main_demand_estimation.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "e310a632-67b0-4f6f-bae7-725519f84f32", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1BKRWpHpv6CPbgq3v094T_yAi2qrhFCNzVoxrmJKMgIc\"\n", "sheet_name = \"main_demand_estimation\"\n", "query_info_df = pb.from_sheets(sheet_id, sheet_name)\n", "query_info_df[\"start_date\"] = query_info_df[\"start_date\"].astype(\"str\")\n", "query_info_df[\"end_date\"] = query_info_df[\"end_date\"].astype(\"str\")\n", "query_info_df[\"mode\"] = query_info_df[\"mode\"].astype(\"str\")\n", "query_info_df[\"flag_atc_channel_contri\"] = query_info_df[\"flag_atc_channel_contri\"].astype(\"int\")\n", "df = query_info_df[query_info_df[\"flag_atc_channel_contri\"] == 1]\n", "df = df.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "301862ed-7c78-4845-8967-905ee30330ba", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e88d11a5-2d5a-4e7b-9be0-2f85939ae09a", "metadata": {}, "outputs": [], "source": ["if not df.empty:  # More readable check for non-empty DataFrame\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        start_date_backfill = row[\"start_date\"]\n", "        end_date_backfill = row[\"end_date\"]\n", "        mode = row[\"mode\"]\n", "        # Print extracted values\n", "        print(\"start_date:\", str(start_date_backfill))\n", "        print(\"end_date:\", str(end_date_backfill))\n", "        print(\"mode:\", mode)\n", "        print(\"load_type:\", kwargs[\"load_type\"])\n", "\n", "        backfill_sql = f\"\"\"\n", "        with product_item_mapping as \n", "        (\n", "            select product_id,\n", "                count(distinct item_id) as sku_count\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current = true\n", "            group by 1\n", "            having count(distinct item_id) = 1\n", "        ),\n", "\n", "        product_id_to_unique_ptype_mapping as (\n", "            select distinct dp.product_id,\n", "                product_type,\n", "                product_type_id\n", "            from dwh.dim_product dp\n", "            inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "            where is_current = true\n", "            and product_type not in ('combo','Combo')\n", "        ),\n", "\n", "        merchant_to_outlet_mapping_base as \n", "        (\n", "        select \n", "            order_create_dt_ist as date_, \n", "            frontend_merchant_id as merchant_id,\n", "            outlet_id\n", "        from dwh.fact_sales_order_details\n", "        where order_create_dt_ist between (date({start_date_backfill}) - interval '8' day) and (date({end_date_backfill}) + interval '8' day)\n", "        group by 1,2,3\n", "        )\n", "        ,\n", "        merchant_to_outlet_mapping as \n", "        (\n", "        select \n", "            a.date_,\n", "            a.merchant_id,\n", "            coalesce(coalesce(b.outlet_id, \n", "            last_value(b.outlet_id) ignore nulls over (partition by a.merchant_id order by a.date_ asc rows between unbounded preceding and current row)), \n", "            first_value(b.outlet_id) ignore nulls over (partition by a.merchant_id order by a.date_ asc rows between current row and unbounded following)) as outlet_id\n", "        from \n", "        ((select distinct date_ from merchant_to_outlet_mapping_base)\n", "        cross join \n", "        (select distinct merchant_id from merchant_to_outlet_mapping_base)\n", "        ) a\n", "        left join merchant_to_outlet_mapping_base b on a.date_=b.date_ and a.merchant_id=b.merchant_id\n", "        ),\n", "\n", "        search_atc as (\n", "            select \n", "                e.at_date_ist as date_,\n", "                extract(hour from e.at_ist) as hour,\n", "                e.platform,\n", "                trim(lower(e.traits__city_name)) as city_name,\n", "                e.traits__merchant_id as merchant_id,\n", "                e.traits__user_id as user_id,\n", "                ptype_mapping.product_type,\n", "                ptype_mapping.product_type_id,\n", "                case \n", "                    when e.properties__search_keyword_type = 'auto_suggest' then cast( trim(lower(coalesce(e.properties__search_previous_keyword,e.properties__search_input_keyword))) as varchar )\n", "                    else cast(trim(lower(e.properties__search_input_keyword)) as varchar) \n", "                end input_keyword,\n", "                cast(trim(lower(e.properties__search_actual_keyword)) as varchar) as actual_keyword,\n", "                case\n", "                    when e.properties__search_keyword_type in ('auto_suggest','did_you_mean') then 'auto_suggest/did_you_mean'\n", "                    else 'others' \n", "                end properties__search_keyword_type,\n", "                coalesce(e.device_uuid, e.traits__device_uuid) as device_id,\n", "                coalesce(e.session_uuid, e.traits__session_uuid) as session_id,\n", "                sum(properties__quantity) as quantity_added\n", "            from lake_events.mobile_event_data e\n", "            inner join product_id_to_unique_ptype_mapping ptype_mapping on ptype_mapping.product_id = coalesce(try_cast(properties__product_id as int),try_cast(properties__child_widget_id as int), try_cast(properties__widget_id as int) , 0 )\n", "            where e.at_date_ist between date({start_date_backfill}) and date({end_date_backfill})\n", "\n", "                and e.properties__search_keyword_parent in ('type-to-search', 'type_to_search')\n", "                and e.properties__search_keyword_type in ('type-to-search', 'type_to_search','auto_suggest','history')\n", "                and e.name = 'Product Added'                 \n", "                and e.properties__page_name IN ('Search Page', 'Search List', 'search')\n", "                and coalesce(e.properties__page_type,'0') in ('0','Global Search')\n", "                and e.traits__user_id is not NULL\n", "                and e.traits__user_id not in ('-1','0')\n", "                -- and e.traits__user_id not in ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')\n", "                and e.traits__merchant_id is not NULL\n", "                and device_uuid IS NOT NULL\n", "                and (\n", "                    CASE WHEN properties__search_keyword_type = 'auto_suggest' then COALESCE( CAST(TRIM(LOWER(properties__search_previous_keyword)) AS VARCHAR), CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) )\n", "                    ELSE CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) END\n", "                ) NOT IN ('',' ','#-na')\n", "                and LENGTH(\n", "                    CASE WHEN properties__search_keyword_type = 'auto_suggest' then coalesce( cast(trim(lower(properties__search_previous_keyword)) AS VARCHAR), CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) )\n", "                    ELSE CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) END\n", "                ) BETWEEN 3 AND 50\n", "            group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "        )\n", "        ,\n", "        search as (\n", "            select sar.date_,\n", "                hour,\n", "                sar.merchant_id as merchant_id,\n", "                coalesce(spm.outlet_id,c.pos_outlet_id) as outlet_id,\n", "                product_type,\n", "                product_type_id,\n", "                count(distinct session_id) as search_atc_session_count,\n", "                count(distinct device_id) as search_atc_device_count,\n", "                sum(quantity_added) search_quantity_added\n", "            from search_atc sar\n", "            left join merchant_to_outlet_mapping spm on sar.date_=spm.date_ and sar.merchant_id=spm.merchant_id\n", "            left join dwh.dim_merchant_outlet_facility_mapping c on c.frontend_merchant_id=sar.merchant_id and c.is_current and c.is_mapping_enabled and c.is_current_mapping_active \n", "            left join supply_etls.outlet_details b on coalesce(spm.outlet_id,c.pos_outlet_id)=b.inv_outlet_id\n", "            where coalesce(spm.outlet_id,c.pos_outlet_id) is not null\n", "            group by 1,2,3,4,5,6\n", "        )\n", "        ,\n", "        non_search_atc as (\n", "            select \n", "                e.at_date_ist as date_,\n", "                extract(hour from e.at_ist) as hour,\n", "                e.platform,\n", "                trim(lower(e.traits__city_name)) as city_name,\n", "                e.traits__merchant_id as merchant_id,\n", "                properties__search_keyword_parent,\n", "                properties__search_keyword_type,\n", "                e.traits__user_id as user_id,\n", "                properties__page_name,\n", "                ptype_mapping.product_type,\n", "                ptype_mapping.product_type_id,\n", "                case \n", "                    when e.properties__search_keyword_type = 'auto_suggest' then cast( trim(lower(coalesce(e.properties__search_previous_keyword,e.properties__search_input_keyword))) as varchar )\n", "                    else cast(trim(lower(e.properties__search_input_keyword)) as varchar) \n", "                end input_keyword,\n", "                cast(trim(lower(e.properties__search_actual_keyword)) as varchar) as actual_keyword,\n", "                case\n", "                    when e.properties__search_keyword_type in ('auto_suggest','did_you_mean') then 'auto_suggest/did_you_mean'\n", "                    else 'others' \n", "                end properties__search_keyword_type,\n", "                coalesce(e.device_uuid, e.traits__device_uuid) as device_id,\n", "                coalesce(e.session_uuid, e.traits__session_uuid) as session_id,\n", "                sum(properties__quantity) as quantity_added\n", "            from lake_events.mobile_event_data e\n", "            inner join product_id_to_unique_ptype_mapping ptype_mapping on ptype_mapping.product_id = coalesce(try_cast(properties__product_id as int),try_cast(properties__child_widget_id as int), try_cast(properties__widget_id as int) , 0 )\n", "            where e.at_date_ist between date({start_date_backfill}) and date({end_date_backfill})\n", "                and e.name = 'Product Added'                 \n", "                and e.properties__page_name not IN ('Search Page', 'Search List', 'search')\n", "                and coalesce(e.properties__page_type,'0') in ('0','Global Search')\n", "                and e.traits__user_id is not NULL\n", "                and e.traits__user_id not in ('-1','0')\n", "                -- and e.traits__user_id not in ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')\n", "                and e.traits__merchant_id is not NULL\n", "                and device_uuid IS NOT NULL\n", "            group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16\n", "        )\n", "        ,\n", "        non_search as (\n", "            select sar.date_,\n", "                hour,\n", "                sar.merchant_id,\n", "                coalesce(spm.outlet_id,c.pos_outlet_id) as outlet_id,\n", "                product_type,\n", "                product_type_id,\n", "                count(distinct session_id) as non_search_atc_session_count,\n", "                count(distinct device_id) as non_search_atc_device_count,\n", "                sum(quantity_added) non_search_quantity_added\n", "            from non_search_atc sar\n", "            left join merchant_to_outlet_mapping spm on sar.date_=spm.date_ and sar.merchant_id=spm.merchant_id\n", "            left join dwh.dim_merchant_outlet_facility_mapping c on c.frontend_merchant_id=sar.merchant_id and c.is_current and c.is_mapping_enabled and c.is_current_mapping_active \n", "            where coalesce(spm.outlet_id,c.pos_outlet_id) is not null\n", "            group by 1,2,3,4,5,6\n", "        )\n", "\n", "        select \n", "            coalesce(search.date_,non_search.date_) as date_,\n", "            coalesce(search.hour,non_search.hour) as hour,\n", "            coalesce(search.merchant_id,non_search.merchant_id) as merchant_id,\n", "            coalesce(search.outlet_id,non_search.outlet_id) as outlet_id,\n", "            coalesce(search.product_type,non_search.product_type) as product_type,\n", "            coalesce(search.product_type_id,non_search.product_type_id) as product_type_id,\n", "            search.search_atc_session_count,\n", "            search.search_atc_device_count,\n", "            search.search_quantity_added,\n", "            non_search.non_search_atc_session_count,\n", "            non_search.non_search_atc_device_count,\n", "            non_search.non_search_quantity_added,\n", "            cast(current_timestamp as timestamp) as updated_at\n", "        from search \n", "        full join non_search on search.date_ = non_search.date_\n", "                    and search.hour = non_search.hour\n", "                    and search.merchant_id = non_search.merchant_id\n", "                    and search.product_type_id = non_search.product_type_id\n", "\n", "\n", "            \"\"\"\n", "        to_trino(backfill_sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "        kwargs[\"load_type\"] = \"upsert\""]}, {"cell_type": "code", "execution_count": null, "id": "42c1047b-938a-4695-b256-8e75b33b26b0", "metadata": {}, "outputs": [], "source": ["print(\"mode:\", default_mode)\n", "print(\"load_type:\", kwargs[\"load_type\"])\n", "\n", "sql = \"\"\"\n", "        with product_item_mapping as \n", "        (\n", "            select product_id,\n", "                count(distinct item_id) as sku_count\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current = true\n", "            group by 1\n", "            having count(distinct item_id) = 1\n", "        ),\n", "\n", "        product_id_to_unique_ptype_mapping as (\n", "            select distinct dp.product_id,\n", "                product_type,\n", "                product_type_id\n", "            from dwh.dim_product dp\n", "            inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "            where is_current = true\n", "            and product_type not in ('combo','Combo')\n", "        ),\n", "\n", "        merchant_to_outlet_mapping_base as \n", "        (\n", "        select \n", "            order_create_dt_ist as date_, \n", "            frontend_merchant_id as merchant_id,\n", "            outlet_id\n", "        from dwh.fact_sales_order_details\n", "        where order_create_dt_ist  >= (current_date - interval '10' day)\n", "        group by 1,2,3\n", "        )\n", "        ,\n", "        merchant_to_outlet_mapping as \n", "        (\n", "        select \n", "            a.date_,\n", "            a.merchant_id,\n", "            coalesce(coalesce(b.outlet_id, \n", "            last_value(b.outlet_id) ignore nulls over (partition by a.merchant_id order by a.date_ asc rows between unbounded preceding and current row)), \n", "            first_value(b.outlet_id) ignore nulls over (partition by a.merchant_id order by a.date_ asc rows between current row and unbounded following)) as outlet_id\n", "        from \n", "        ((select distinct date_ from merchant_to_outlet_mapping_base)\n", "        cross join \n", "        (select distinct merchant_id from merchant_to_outlet_mapping_base)\n", "        ) a\n", "        left join merchant_to_outlet_mapping_base b on a.date_=b.date_ and a.merchant_id=b.merchant_id\n", "        ),\n", "\n", "        search_atc as (\n", "            select \n", "                e.at_date_ist as date_,\n", "                extract(hour from e.at_ist) as hour,\n", "                e.platform,\n", "                trim(lower(e.traits__city_name)) as city_name,\n", "                e.traits__merchant_id as merchant_id,\n", "                e.traits__user_id as user_id,\n", "                ptype_mapping.product_type,\n", "                ptype_mapping.product_type_id,\n", "                case \n", "                    when e.properties__search_keyword_type = 'auto_suggest' then cast( trim(lower(coalesce(e.properties__search_previous_keyword,e.properties__search_input_keyword))) as varchar )\n", "                    else cast(trim(lower(e.properties__search_input_keyword)) as varchar) \n", "                end input_keyword,\n", "                cast(trim(lower(e.properties__search_actual_keyword)) as varchar) as actual_keyword,\n", "                case\n", "                    when e.properties__search_keyword_type in ('auto_suggest','did_you_mean') then 'auto_suggest/did_you_mean'\n", "                    else 'others' \n", "                end properties__search_keyword_type,\n", "                coalesce(e.device_uuid, e.traits__device_uuid) as device_id,\n", "                coalesce(e.session_uuid, e.traits__session_uuid) as session_id,\n", "                sum(properties__quantity) as quantity_added\n", "            from lake_events.mobile_event_data e\n", "            inner join product_id_to_unique_ptype_mapping ptype_mapping on ptype_mapping.product_id = coalesce(try_cast(properties__product_id as int),try_cast(properties__child_widget_id as int), try_cast(properties__widget_id as int) , 0 )\n", "            where e.at_date_ist >= current_date - interval '8' day\n", "\n", "                and e.properties__search_keyword_parent in ('type-to-search', 'type_to_search')\n", "                and e.properties__search_keyword_type in ('type-to-search', 'type_to_search','auto_suggest','history')\n", "                and e.name = 'Product Added'                 \n", "                and e.properties__page_name IN ('Search Page', 'Search List', 'search')\n", "                and coalesce(e.properties__page_type,'0') in ('0','Global Search')\n", "                and e.traits__user_id is not NULL\n", "                and e.traits__user_id not in ('-1','0')\n", "                -- and e.traits__user_id not in ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')\n", "                and e.traits__merchant_id is not NULL\n", "                and device_uuid IS NOT NULL\n", "                and (\n", "                    CASE WHEN properties__search_keyword_type = 'auto_suggest' then COALESCE( CAST(TRIM(LOWER(properties__search_previous_keyword)) AS VARCHAR), CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) )\n", "                    ELSE CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) END\n", "                ) NOT IN ('',' ','#-na')\n", "                and LENGTH(\n", "                    CASE WHEN properties__search_keyword_type = 'auto_suggest' then coalesce( cast(trim(lower(properties__search_previous_keyword)) AS VARCHAR), CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) )\n", "                    ELSE CAST(TRIM(LOWER(properties__search_input_keyword)) AS VARCHAR) END\n", "                ) BETWEEN 3 AND 50\n", "            group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "        )\n", "        ,\n", "        search as (\n", "            select sar.date_,\n", "                hour,\n", "                sar.merchant_id as merchant_id,\n", "                coalesce(spm.outlet_id,c.pos_outlet_id) as outlet_id,\n", "                product_type,\n", "                product_type_id,\n", "                count(distinct session_id) as search_atc_session_count,\n", "                count(distinct device_id) as search_atc_device_count,\n", "                sum(quantity_added) search_quantity_added\n", "            from search_atc sar\n", "            left join merchant_to_outlet_mapping spm on sar.date_=spm.date_ and sar.merchant_id=spm.merchant_id\n", "            left join dwh.dim_merchant_outlet_facility_mapping c on c.frontend_merchant_id=sar.merchant_id and c.is_current and c.is_mapping_enabled and c.is_current_mapping_active \n", "            left join supply_etls.outlet_details b on coalesce(spm.outlet_id,c.pos_outlet_id)=b.inv_outlet_id\n", "            where coalesce(spm.outlet_id,c.pos_outlet_id) is not null\n", "            group by 1,2,3,4,5,6\n", "        )\n", "        ,\n", "        non_search_atc as (\n", "            select \n", "                e.at_date_ist as date_,\n", "                extract(hour from e.at_ist) as hour,\n", "                e.platform,\n", "                trim(lower(e.traits__city_name)) as city_name,\n", "                e.traits__merchant_id as merchant_id,\n", "                properties__search_keyword_parent,\n", "                properties__search_keyword_type,\n", "                e.traits__user_id as user_id,\n", "                properties__page_name,\n", "                ptype_mapping.product_type,\n", "                ptype_mapping.product_type_id,\n", "                case \n", "                    when e.properties__search_keyword_type = 'auto_suggest' then cast( trim(lower(coalesce(e.properties__search_previous_keyword,e.properties__search_input_keyword))) as varchar )\n", "                    else cast(trim(lower(e.properties__search_input_keyword)) as varchar) \n", "                end input_keyword,\n", "                cast(trim(lower(e.properties__search_actual_keyword)) as varchar) as actual_keyword,\n", "                case\n", "                    when e.properties__search_keyword_type in ('auto_suggest','did_you_mean') then 'auto_suggest/did_you_mean'\n", "                    else 'others' \n", "                end properties__search_keyword_type,\n", "                coalesce(e.device_uuid, e.traits__device_uuid) as device_id,\n", "                coalesce(e.session_uuid, e.traits__session_uuid) as session_id,\n", "                sum(properties__quantity) as quantity_added\n", "            from lake_events.mobile_event_data e\n", "            inner join product_id_to_unique_ptype_mapping ptype_mapping on ptype_mapping.product_id = coalesce(try_cast(properties__product_id as int),try_cast(properties__child_widget_id as int), try_cast(properties__widget_id as int) , 0 )\n", "            where e.at_date_ist >= current_date - interval '8' day\n", "                and e.name = 'Product Added'                 \n", "                and e.properties__page_name not IN ('Search Page', 'Search List', 'search')\n", "                and coalesce(e.properties__page_type,'0') in ('0','Global Search')\n", "                and e.traits__user_id is not NULL\n", "                and e.traits__user_id not in ('-1','0')\n", "                -- and e.traits__user_id not in ('14647274', '9961423','9709403','13957980','13605597','3927621','14740725','4144617','10045662')\n", "                and e.traits__merchant_id is not NULL\n", "                and device_uuid IS NOT NULL\n", "            group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16\n", "        )\n", "        ,\n", "        non_search as (\n", "            select sar.date_,\n", "                hour,\n", "                sar.merchant_id,\n", "                coalesce(spm.outlet_id,c.pos_outlet_id) as outlet_id,\n", "                product_type,\n", "                product_type_id,\n", "                count(distinct session_id) as non_search_atc_session_count,\n", "                count(distinct device_id) as non_search_atc_device_count,\n", "                sum(quantity_added) non_search_quantity_added\n", "            from non_search_atc sar\n", "            left join merchant_to_outlet_mapping spm on sar.date_=spm.date_ and sar.merchant_id=spm.merchant_id\n", "            left join dwh.dim_merchant_outlet_facility_mapping c on c.frontend_merchant_id=sar.merchant_id and c.is_current and c.is_mapping_enabled and c.is_current_mapping_active \n", "            where coalesce(spm.outlet_id,c.pos_outlet_id) is not null\n", "            group by 1,2,3,4,5,6\n", "        )\n", "\n", "        select \n", "            coalesce(search.date_,non_search.date_) as date_,\n", "            coalesce(search.hour,non_search.hour) as hour,\n", "            coalesce(search.merchant_id,non_search.merchant_id) as merchant_id,\n", "            coalesce(search.outlet_id,non_search.outlet_id) as outlet_id,\n", "            coalesce(search.product_type,non_search.product_type) as product_type,\n", "            coalesce(search.product_type_id,non_search.product_type_id) as product_type_id,\n", "            search.search_atc_session_count,\n", "            search.search_atc_device_count,\n", "            search.search_quantity_added,\n", "            non_search.non_search_atc_session_count,\n", "            non_search.non_search_atc_device_count,\n", "            non_search.non_search_quantity_added,\n", "            cast(current_timestamp as timestamp) as updated_at\n", "        from search \n", "        full join non_search on search.date_ = non_search.date_\n", "                    and search.hour = non_search.hour\n", "                    and search.merchant_id = non_search.merchant_id\n", "                    and search.product_type_id = non_search.product_type_id\n", "\n", "\n", "        \"\"\"\n", "\n", "to_trino(sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "493dd2b5-cdc7-488a-9b3b-e399fe37c3da", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}