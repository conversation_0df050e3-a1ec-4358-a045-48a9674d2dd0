{"cells": [{"cell_type": "code", "execution_count": null, "id": "05014746-67a1-4e6d-868a-5109c78edd1a", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "# import matplotlib.pyplot as plt\n", "from itertools import product\n", "from pytz import timezone\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "51149822-67f6-417b-b13f-efb63fd71233", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "71ee3c5e-4de4-4a8f-8c27-55bdb104df31", "metadata": {}, "outputs": [], "source": ["default_mode = \"run\"\n", "mode = default_mode"]}, {"cell_type": "code", "execution_count": null, "id": "05b9ad13-249d-4ecf-a2f3-aedd5796d218", "metadata": {}, "outputs": [], "source": ["current_date_time = datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "id": "1da08620-a8d7-41e7-931b-c55649e9dd9d", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"city_keyword_ptype_distribution_final\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"city_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"keyword\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"product_type\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"product_type_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"percentage_attribution_d_day\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"percentage_attribution_2d_window\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"percentage_attribution_6d_window\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"percentage_attribution_14d_window\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city_id\",\n", "        \"product_type_id\",\n", "        \"keyword\",\n", "    ],\n", "    # \"sortkey\": [\"at_date_ist\",\"traits__city_name\",\"keyword\"],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"tag_name\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"city_keyword_ptype_distribution_final\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ef804d90-c71c-4607-8d5a-4a31ce6982ca", "metadata": {}, "outputs": [], "source": ["# query_info_df= pd.read_csv(\"main_demand_estimation.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "e310a632-67b0-4f6f-bae7-725519f84f32", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1BKRWpHpv6CPbgq3v094T_yAi2qrhFCNzVoxrmJKMgIc\"\n", "sheet_name = \"main_demand_estimation\"\n", "query_info_df = pb.from_sheets(sheet_id, sheet_name)\n", "query_info_df[\"start_date\"] = query_info_df[\"start_date\"].astype(\"str\")\n", "query_info_df[\"end_date\"] = query_info_df[\"end_date\"].astype(\"str\")\n", "query_info_df[\"mode\"] = query_info_df[\"mode\"].astype(\"str\")\n", "query_info_df[\"flag_ptype_atc\"] = query_info_df[\"flag_ptype_atc\"].astype(\"int\")\n", "df = query_info_df[query_info_df[\"flag_ptype_atc\"] == 1]\n", "df = df.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "301862ed-7c78-4845-8967-905ee30330ba", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "42c1047b-938a-4695-b256-8e75b33b26b0", "metadata": {}, "outputs": [], "source": ["if not df.empty:  # More readable check for non-empty DataFrame\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        start_date_backfill = row[\"start_date\"]\n", "        end_date_backfill = row[\"end_date\"]\n", "        mode = row[\"mode\"]\n", "        # Print extracted values\n", "        print(\"start_date:\", str(start_date_backfill))\n", "        print(\"end_date:\", str(end_date_backfill))\n", "        print(\"mode:\", mode)\n", "        print(\"load_type:\", kwargs[\"load_type\"])\n", "\n", "        backfill_sql = f\"\"\"\n", "\n", "        with ptype_distribution_raw as \n", "        (\n", "        select * \n", "        from interim.city_keyword_ptype_distribution\n", "        where date_ between (date({start_date_backfill}) - interval '7' day) and (date({end_date_backfill}) + interval '7' day)\n", "        ),\n", "        \n", "        ptype_atc_distribution_initial as (\n", "            select distinct base.date_,\n", "                base.city_id,\n", "                base.product_type,\n", "                base.product_type_id,\n", "                base.keyword,\n", "                try(pdr.devices*1.00/pdr.total_devices) as percentage_attribution_d_day,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ ROWS BETWEEN 1 preceding and 1 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ rows between 1 preceding and 1 following))) as percentage_attribution_2d_window,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ ROWS BETWEEN 3 preceding and 3 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ rows between 3 preceding and 3 following))) as percentage_attribution_6d_window,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ ROWS BETWEEN 7 preceding and 7 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ rows between 7 preceding and 7 following))) as percentage_attribution_14d_window\n", "            from (\n", "                    (select distinct date_ from ptype_distribution_raw) \n", "                        cross join \n", "                    (select distinct city_id,keyword, product_type, product_type_id from ptype_distribution_raw)\n", "                ) base\n", "            left join ptype_distribution_raw pdr \n", "            on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type_id = base.product_type_id and base.city_id=pdr.city_id\n", "        ), \n", "\n", "        ptype_atc_distribution as (\n", "            select date_,\n", "                city_id,\n", "                keyword,\n", "                product_type,\n", "                product_type_id,\n", "                percentage_attribution_d_day,\n", "                percentage_attribution_2d_window,\n", "                percentage_attribution_6d_window,\n", "                percentage_attribution_14d_window,\n", "                key_rank\n", "            from (select date_,\n", "                    city_id,\n", "                    keyword,\n", "                    product_type,\n", "                    product_type_id,\n", "                    percentage_attribution_d_day,\n", "                    percentage_attribution_2d_window,\n", "                    percentage_attribution_6d_window,\n", "                    percentage_attribution_14d_window,\n", "                    rank() over (partition by city_id, date_, keyword order by percentage_attribution_14d_window desc) as key_rank\n", "                from ptype_atc_distribution_initial\n", "                where date_ between date({start_date_backfill}) and date({end_date_backfill})\n", "                )\n", "            where (key_rank<=1 or percentage_attribution_14d_window > 0.1)\n", "        )\n", "\n", "        select date_,\n", "        city_id,\n", "        keyword, \n", "        product_type,\n", "        product_type_id,\n", "        percentage_attribution_d_day*1.00/nullif(sum(percentage_attribution_d_day) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_d_day,\n", "        percentage_attribution_2d_window*1.00/nullif(sum(percentage_attribution_2d_window) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_2d_window,\n", "        percentage_attribution_6d_window*1.00/nullif(sum(percentage_attribution_6d_window) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_6d_window,\n", "        percentage_attribution_14d_window*1.00/nullif(sum(percentage_attribution_14d_window) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_14d_window,\n", "        cast(current_timestamp as timestamp) as updated_at\n", "        from ptype_atc_distribution\n", "\n", "            \"\"\"\n", "        to_trino(backfill_sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "        kwargs[\"load_type\"] = \"upsert\""]}, {"cell_type": "code", "execution_count": null, "id": "2f78ecda-3dc5-4b47-8a7a-8abe6465e0ad", "metadata": {}, "outputs": [], "source": ["print(\"mode:\", default_mode)\n", "print(\"load_type:\", kwargs[\"load_type\"])\n", "\n", "sql = \"\"\"\n", "        with ptype_distribution_raw as\n", "        (\n", "        select * \n", "        from interim.city_keyword_ptype_distribution\n", "        where date_ >= (current_date - interval '15' day)\n", "        )\n", "        ,\n", "        ptype_atc_distribution_initial as (\n", "            select distinct base.date_,\n", "                base.city_id,\n", "                base.product_type,\n", "                base.product_type_id,\n", "                base.keyword,\n", "                try(pdr.devices*1.00/pdr.total_devices) as percentage_attribution_d_day,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ ROWS BETWEEN 1 preceding and 1 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ rows between 1 preceding and 1 following))) as percentage_attribution_2d_window,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ ROWS BETWEEN 3 preceding and 3 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ rows between 3 preceding and 3 following))) as percentage_attribution_6d_window,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ ROWS BETWEEN 7 preceding and 7 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.city_id, base.product_type_id, base.keyword order by base.date_ rows between 7 preceding and 7 following))) as percentage_attribution_14d_window\n", "            from (\n", "                    (select distinct date_ from ptype_distribution_raw) \n", "                        cross join \n", "                    (select distinct city_id,keyword, product_type, product_type_id from ptype_distribution_raw)\n", "                ) base\n", "            left join ptype_distribution_raw pdr \n", "            on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type_id = base.product_type_id and base.city_id=pdr.city_id\n", "        ), \n", "\n", "        ptype_atc_distribution as (\n", "            select date_,\n", "                city_id,\n", "                keyword,\n", "                product_type,\n", "                product_type_id,\n", "                percentage_attribution_d_day,\n", "                percentage_attribution_2d_window,\n", "                percentage_attribution_6d_window,\n", "                percentage_attribution_14d_window,\n", "                key_rank\n", "            from (select date_,\n", "                    city_id,\n", "                    keyword,\n", "                    product_type,\n", "                    product_type_id,\n", "                    percentage_attribution_d_day,\n", "                    percentage_attribution_2d_window,\n", "                    percentage_attribution_6d_window,\n", "                    percentage_attribution_14d_window,\n", "                    rank() over (partition by city_id, date_, keyword order by percentage_attribution_14d_window desc) as key_rank\n", "                from ptype_atc_distribution_initial\n", "                where date_ >= (current_date - interval '8' day)\n", "                )\n", "            where (key_rank<=1 or percentage_attribution_14d_window > 0.1)\n", "        )\n", "\n", "        select date_,\n", "        city_id,\n", "        keyword, \n", "        product_type,\n", "        product_type_id,\n", "        percentage_attribution_d_day*1.00/nullif(sum(percentage_attribution_d_day) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_d_day,\n", "        percentage_attribution_2d_window*1.00/nullif(sum(percentage_attribution_2d_window) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_2d_window,\n", "        percentage_attribution_6d_window*1.00/nullif(sum(percentage_attribution_6d_window) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_6d_window,\n", "        percentage_attribution_14d_window*1.00/nullif(sum(percentage_attribution_14d_window) over (partition by date_, city_id, keyword) , 0) as percentage_attribution_14d_window,\n", "        cast(current_timestamp as timestamp) as updated_at\n", "        from ptype_atc_distribution\n", "\n", "        \"\"\"\n", "\n", "to_trino(sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "12c6371f-6aa0-4651-8621-dc523e094c42", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}