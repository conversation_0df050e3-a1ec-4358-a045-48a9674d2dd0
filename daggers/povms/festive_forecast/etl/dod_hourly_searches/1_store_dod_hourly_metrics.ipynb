{"cells": [{"cell_type": "code", "execution_count": null, "id": "05014746-67a1-4e6d-868a-5109c78edd1a", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "# import matplotlib.pyplot as plt\n", "from itertools import product\n", "from pytz import timezone\n", "\n", "# CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "51149822-67f6-417b-b13f-efb63fd71233", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "71ee3c5e-4de4-4a8f-8c27-55bdb104df31", "metadata": {}, "outputs": [], "source": ["default_mode = \"run\"\n", "mode = default_mode"]}, {"cell_type": "code", "execution_count": null, "id": "05b9ad13-249d-4ecf-a2f3-aedd5796d218", "metadata": {}, "outputs": [], "source": ["current_date_time = datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "id": "1da08620-a8d7-41e7-931b-c55649e9dd9d", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"store_dod_hourly_metrics\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"NA\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"merchant_id\", \"type\": \"BIGINT\", \"description\": \"NA\"},\n", "        {\"name\": \"product_type\", \"type\": \"VARCHAR\", \"description\": \"NA\"},\n", "        {\"name\": \"product_type_id\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"total_carts\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"total_quantity\", \"type\": \"INTEGER\", \"description\": \"NA\"},\n", "        {\"name\": \"avail_perc\", \"type\": \"DOUBLE\", \"description\": \"NA\"},\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"NA\"},\n", "    ],\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"hour\",\n", "        \"product_type_id\",\n", "        \"merchant_id\",\n", "    ],\n", "    # \"sortkey\": [\"at_date_ist\",\"traits__city_name\",\"keyword\"],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"tag_name\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"store_dod_hourly_metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ef804d90-c71c-4607-8d5a-4a31ce6982ca", "metadata": {}, "outputs": [], "source": ["# query_info_df= pd.read_csv(\"main_demand_estimation.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "e310a632-67b0-4f6f-bae7-725519f84f32", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1BKRWpHpv6CPbgq3v094T_yAi2qrhFCNzVoxrmJKMgIc\"\n", "sheet_name = \"main_demand_estimation\"\n", "query_info_df = pb.from_sheets(sheet_id, sheet_name)\n", "query_info_df[\"start_date\"] = query_info_df[\"start_date\"].astype(\"str\")\n", "query_info_df[\"end_date\"] = query_info_df[\"end_date\"].astype(\"str\")\n", "query_info_df[\"mode\"] = query_info_df[\"mode\"].astype(\"str\")\n", "query_info_df[\"flag_store_carts\"] = query_info_df[\"flag_store_carts\"].astype(\"int\")\n", "df = query_info_df[query_info_df[\"flag_store_carts\"] == 1]\n", "df = df.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "301862ed-7c78-4845-8967-905ee30330ba", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "42c1047b-938a-4695-b256-8e75b33b26b0", "metadata": {}, "outputs": [], "source": ["if not df.empty:  # More readable check for non-empty DataFrame\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        start_date_backfill = row[\"start_date\"]\n", "        end_date_backfill = row[\"end_date\"]\n", "        mode = row[\"mode\"]\n", "        # Print extracted values\n", "        print(\"start_date:\", str(start_date_backfill))\n", "        print(\"end_date:\", str(end_date_backfill))\n", "        print(\"mode:\", mode)\n", "        print(\"load_type:\", kwargs[\"load_type\"])\n", "\n", "        backfill_sql = f\"\"\"\n", "            with product_item_mapping as (\n", "                select product_id,\n", "                    count(distinct item_id) as sku_count\n", "                from dwh.dim_item_product_offer_mapping\n", "                where is_current = true\n", "                group by 1\n", "                having count(distinct item_id) = 1\n", "            ),\n", "\n", "            pid_to_ptype as \n", "            (\n", "            select \n", "                distinct dp.product_id,\n", "                dp.product_type_id,\n", "                product_type\n", "            from dwh.dim_product dp\n", "            inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "            where is_current = true\n", "            and product_type not in ('combo','Combo')\n", "            )\n", "\n", "            ,carts_ptype as \n", "            (\n", "                select \n", "                order_create_dt_ist as date_,\n", "                extract(hour from order_create_ts_ist) as hour,\n", "                dp.product_type,\n", "                dp.product_type_id,\n", "                x.outlet_id,\n", "                x.frontend_merchant_id as merchant_id,\n", "                count(distinct cart_id) as carts,\n", "                sum(x.procured_quantity*om.multiplier) as quantity\n", "                from dwh.fact_sales_order_item_details x \n", "                inner join pid_to_ptype dp on x.product_id=dp.product_id\n", "                left join dwh.dim_item_product_offer_mapping om on x.product_id=om.product_id and om.is_current=true\n", "                left join supply_etls.outlet_details od on x.outlet_id=od.inv_outlet_id\n", "                where order_create_dt_ist between date({start_date_backfill}) and date({end_date_backfill})\n", "                and order_current_status = 'DELIVERED' \n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "                group by 1,2,3,4,5,6\n", "            )\n", "\n", "            ,carts_ptype_item as \n", "            (\n", "                select \n", "                order_create_dt_ist as date_,\n", "                dp.product_type,\n", "                dp.product_type_id,\n", "                x.outlet_id,\n", "                x.frontend_merchant_id as merchant_id,\n", "                om.item_id,\n", "                sum(x.procured_quantity*om.multiplier) as quantity\n", "                from dwh.fact_sales_order_item_details x \n", "                inner join pid_to_ptype dp on x.product_id=dp.product_id\n", "                left join dwh.dim_item_product_offer_mapping om on x.product_id=om.product_id and om.is_current=true\n", "                left join supply_etls.outlet_details od on x.outlet_id=od.inv_outlet_id\n", "                where order_create_dt_ist between date({start_date_backfill}) and date({end_date_backfill})\n", "                and order_current_status = 'DELIVERED' \n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "                group by 1,2,3,4,5,6\n", "            )\n", "            ,\n", "\n", "            ranked_items as \n", "            (\n", "            select \n", "                date_,\n", "                outlet_id,\n", "                merchant_id,\n", "                product_type,\n", "                product_type_id,\n", "                item_id,\n", "                quantity*1.00/nullif(sum(quantity) over (partition by date_, outlet_id, product_type_id) ,0) as perc,\n", "                sum(quantity) over (partition by date_, outlet_id, product_type_id order by quantity desc rows between unbounded preceding and current row)*1.00/\n", "                nullif(sum(quantity) over (partition by date_, outlet_id, product_type_id) ,0) as cumu_perc\n", "            from carts_ptype_item\n", "            )\n", "            ,\n", "            top_items as \n", "            (\n", "            select \n", "                date_,\n", "                outlet_id,\n", "                merchant_id,\n", "                product_type,\n", "                product_type_id,\n", "                item_id\n", "                from ranked_items\n", "                where (cumu_perc<=0.8 or perc>=0.30)\n", "            )\n", "            ,\n", "\n", "            agg_hourly_outlet_item_inventory AS (\n", "                select \n", "                    snapshot_date_ist as date_,\n", "                    case when minute(updated_till) = 30 then hour(updated_till) else hour(updated_till) - 1 end as hour,\n", "                    his.outlet_id,\n", "                    ti.product_type,\n", "                    ti.product_type_id,\n", "                    his.item_id,\n", "                    max(current_inventory) as current_inv,\n", "                    case when max(current_inventory) > 0 then 1 else 0 end as avail_flag\n", "                from dwh.agg_hourly_outlet_item_inventory his\n", "                inner join top_items ti on his.outlet_id=ti.outlet_id and his.item_id=ti.item_id and ti.date_=his.snapshot_date_ist\n", "                where snapshot_date_ist>date('2024-09-20') and snapshot_date_ist between date({start_date_backfill}) and date({end_date_backfill})\n", "                group by 1,2,3,4,5,6\n", "\n", "                union all\n", "                select \n", "                    his.date_ist as date_,\n", "                    hour(his.updated_at_ist) as hour,\n", "                    his.outlet_id,\n", "                    ti.product_type,\n", "                    ti.product_type_id,\n", "                    his.item_id,\n", "                    current_inventory as current_inv,\n", "                    case when current_inventory > 0 then 1 else 0 end as avail_flag\n", "                from supply_etls.hourly_inventory_snapshots his\n", "                inner join top_items ti on his.outlet_id=ti.outlet_id and his.item_id=ti.item_id and ti.date_=his.date_ist\n", "                where date_ist<=date('2024-09-20') and date_ist between date({start_date_backfill}) and date({end_date_backfill})\n", "            )\n", "            ,\n", "\n", "            availability as\n", "            (\n", "            select date_, hour, outlet_id, product_type, product_type_id,\n", "            sum(avail_flag)*1.00/nullif(count(avail_flag),0) as avail_perc\n", "            from agg_hourly_outlet_item_inventory\n", "            group by 1,2,3,4,5\n", "            )\n", "\n", "            select \n", "            a.date_,\n", "            a.hour,\n", "            a.outlet_id,\n", "            a.merchant_id,\n", "            a.product_type,\n", "            a.product_type_id,\n", "            a.carts as total_carts,\n", "            a.quantity as total_quantity,\n", "            b.avail_perc,\n", "            cast(current_timestamp as timestamp) as updated_at\n", "            from carts_ptype a\n", "            left join availability b on a.date_=b.date_ and a.hour=b.hour and a.outlet_id=b.outlet_id and a.product_type_id=b.product_type_id\n", "\n", "\n", "            \"\"\"\n", "        to_trino(backfill_sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "        kwargs[\"load_type\"] = \"upsert\""]}, {"cell_type": "code", "execution_count": null, "id": "2f78ecda-3dc5-4b47-8a7a-8abe6465e0ad", "metadata": {}, "outputs": [], "source": ["print(\"mode:\", default_mode)\n", "print(\"load_type:\", kwargs[\"load_type\"])\n", "\n", "sql = \"\"\"\n", "        with product_item_mapping as (\n", "            select product_id,\n", "                count(distinct item_id) as sku_count\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current = true\n", "            group by 1\n", "            having count(distinct item_id) = 1\n", "        ),\n", "\n", "        pid_to_ptype as \n", "        (\n", "        select \n", "            distinct dp.product_id,\n", "            dp.product_type_id,\n", "            product_type\n", "        from dwh.dim_product dp\n", "        inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "        where is_current = true\n", "        and product_type not in ('combo','Combo')\n", "        )\n", "\n", "        ,carts_ptype as \n", "        (\n", "            select \n", "            order_create_dt_ist as date_,\n", "            extract(hour from order_create_ts_ist) as hour,\n", "            dp.product_type,\n", "            dp.product_type_id,\n", "            x.outlet_id,\n", "            x.frontend_merchant_id as merchant_id,\n", "            count(distinct cart_id) as carts,\n", "            sum(x.procured_quantity*om.multiplier) as quantity\n", "            from dwh.fact_sales_order_item_details x \n", "            inner join pid_to_ptype dp on x.product_id=dp.product_id\n", "            left join dwh.dim_item_product_offer_mapping om on x.product_id=om.product_id and om.is_current=true\n", "            left join supply_etls.outlet_details od on x.outlet_id=od.inv_outlet_id\n", "            where order_create_dt_ist >= current_date - interval '8' day\n", "            and order_current_status = 'DELIVERED' \n", "            and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "            group by 1,2,3,4,5,6\n", "        )\n", "\n", "        ,carts_ptype_item as \n", "        (\n", "            select \n", "            order_create_dt_ist as date_,\n", "            dp.product_type,\n", "            dp.product_type_id,\n", "            x.outlet_id,\n", "            x.frontend_merchant_id as merchant_id,\n", "            om.item_id,\n", "            sum(x.procured_quantity*om.multiplier) as quantity\n", "            from dwh.fact_sales_order_item_details x \n", "            inner join pid_to_ptype dp on x.product_id=dp.product_id\n", "            left join dwh.dim_item_product_offer_mapping om on x.product_id=om.product_id and om.is_current=true\n", "            left join supply_etls.outlet_details od on x.outlet_id=od.inv_outlet_id\n", "            where order_create_dt_ist >= current_date - interval '8' day\n", "            and order_current_status = 'DELIVERED' \n", "            and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "            group by 1,2,3,4,5,6\n", "        )\n", "        ,\n", "\n", "        ranked_items as \n", "        (\n", "        select \n", "            date_,\n", "            outlet_id,\n", "            merchant_id,\n", "            product_type,\n", "            product_type_id,\n", "            item_id,\n", "            quantity*1.00/nullif(sum(quantity) over (partition by date_, outlet_id, product_type_id) ,0) as perc,\n", "            sum(quantity) over (partition by date_, outlet_id, product_type_id order by quantity desc rows between unbounded preceding and current row)*1.00/\n", "            nullif(sum(quantity) over (partition by date_, outlet_id, product_type_id) ,0) as cumu_perc\n", "        from carts_ptype_item\n", "        )\n", "        ,\n", "        top_items as \n", "        (\n", "        select \n", "            date_,\n", "            outlet_id,\n", "            merchant_id,\n", "            product_type,\n", "            product_type_id,\n", "            item_id\n", "            from ranked_items\n", "            where (cumu_perc<=0.8 or perc>=0.30)\n", "        )\n", "        ,\n", "\n", "        agg_hourly_outlet_item_inventory AS (\n", "            select \n", "                snapshot_date_ist as date_,\n", "                case when minute(updated_till) = 30 then hour(updated_till) else hour(updated_till) - 1 end as hour,\n", "                his.outlet_id,\n", "                ti.product_type,\n", "                ti.product_type_id,\n", "                his.item_id,\n", "                max(current_inventory) as current_inv,\n", "                case when max(current_inventory) > 0 then 1 else 0 end as avail_flag\n", "            from dwh.agg_hourly_outlet_item_inventory his\n", "            inner join top_items ti on his.outlet_id=ti.outlet_id and his.item_id=ti.item_id and ti.date_=his.snapshot_date_ist\n", "            where snapshot_date_ist >= current_date - interval '8' day \n", "            group by 1,2,3,4,5,6\n", "        )\n", "        ,\n", "\n", "        availability as\n", "        (\n", "        select date_, hour, outlet_id, product_type, product_type_id,\n", "        sum(avail_flag)*1.00/nullif(count(avail_flag),0) as avail_perc\n", "        from agg_hourly_outlet_item_inventory\n", "        group by 1,2,3,4,5\n", "        )\n", "\n", "        select \n", "        a.date_,\n", "        a.hour,\n", "        a.outlet_id,\n", "        a.merchant_id,\n", "        a.product_type,\n", "        a.product_type_id,\n", "        a.carts as total_carts,\n", "        a.quantity as total_quantity,\n", "        b.avail_perc,\n", "        cast(current_timestamp as timestamp) as updated_at\n", "        from carts_ptype a\n", "        left join availability b on a.date_=b.date_ and a.hour=b.hour and a.outlet_id=b.outlet_id and a.product_type_id=b.product_type_id\n", "\n", "\n", "        \"\"\"\n", "\n", "to_trino(sql, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "493dd2b5-cdc7-488a-9b3b-e399fe37c3da", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}