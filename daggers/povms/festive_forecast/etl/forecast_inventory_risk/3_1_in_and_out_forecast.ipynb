{"cells": [{"cell_type": "code", "execution_count": null, "id": "a023cb36-c723-40c4-8cd1-132de478edd8", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import math\n", "import time\n", "from pytz import timezone\n", "from datetime import date, datetime, timedelta\n", "from datetime import timedelta, date\n", "import math\n", "from itertools import product\n", "import gc\n", "import random\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "import collections\n", "\n", "\n", "pd.set_option(\"display.max_rows\", 500)\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.float_format\", \"{:.2f}\".format)"]}, {"cell_type": "code", "execution_count": null, "id": "a3bbc0e2-806a-4e21-a4c0-57f59520a264", "metadata": {}, "outputs": [], "source": ["today_date = pd.to_datetime(date.today()).strftime(\"%Y-%m-%d\")\n", "today_date\n", "today = pd.to_datetime(date.today())"]}, {"cell_type": "markdown", "id": "47eb49ce-a023-4654-814c-b29964bbedbd", "metadata": {}, "source": ["### DataFrames -\n", "<br />\n", "1. <b> outlet_city_mapping </b> - gets active ```outlet x city mapping```\n", "\n", "2. <b>item_ptype_sold_qty </b> - gets ```item, ptype and ptype_sold (multiplier * qty)```\n", "\n", "3. <b>cat_ptype_item_mapping </b> - based on ptype, we get ``` category x ptype x item``` mapping (when not grouped, ptype only is category)\n", "\n", "4. <b>batches </b> - grouping of items, based on group size (determined by no. of items in the category). This is done using greedy knapsack in the function - ```creating_batches_using_bin_packing_algorithm```\n", "\n", "5. <b> def fetch_item_level_availability </b> - Fetches input data ```inv_and_sales_logs_raw (date x hr x outlet x item x ptype x city)``` and returns ```inv_and_sales_logs_with_cat```, which is same as input, just with added category column.\n", "    1. inv_and_sales_logs_with_cat - (date x hr x outlet x item x ptype x city x CATEGORY)\n", "\n", "6. <b> def make_assortment_please </b> - Makes Assortment\n", "    1. <b> original_inv_and_sales_logs </b> - Copy of inv_and_sales_logs_with_cat\n", "    2. <b> inv_and_sales_logs_avail  == original_inv_and_sales_logs_avail </b> - Wherever avail is > 0 in inv_and_sales_logs_with_cat\n", "    3. <b> city_sales </b> - It gives sales contribution of item_id, for the [city x category] i.e. how much item_1 contributes to mumbai x atta sale.\n", "    4. <b> city_sales_top_assortment </b> - Removes noise (items) that contribute less than 7.5% sale for [city x category]\n", "    5. <b> city_sales_top_assortment_size </b> - Contains [city x category x num_items_city_cat]. [Counts no. of unique items for city x category] based on city_sales_top_assortment.\n", "    6. <b> assortment_sales_data </b> - Inner Joins (inv_and_sales_logs_avail, city_sales_top_assortment) to contain data on (date x hr x outlet x item x ptype x city x CATEGORY) level along with sale contribution of item in [city x category] for all Top assortments (Noiseless data).\n", "    7. <b> num_assortments_hr_fe_cat </b> - Contains no. of item ids present at (date x hr x outlet_id, city x category). Lets call num_items_fe_hr\n", "    8. <b> top_sales2 </b> - Same as num_assortments_hr_fe_cat,  but also contains num_items_city_cat in addition to num_items_cat_fe_hr.\n", "    ```It has a flag that indicates 1 if no. of items at that hr in that fe for category x = no. of items totally for that city x category.```\n", "    9. <b> top_sales3 </b> - Joins assortment_sales_data with top_sales2 to introduce flag\n", "    10. <b> full_city_cat_assortment_at_fe_hr </b> - All date x hr x outlet x category x city x item where flag = 1\n", "    11. <b> hero_assortment </b> - Getting all [city x item x cat] that comprise 80%+ cum sales qty. (i.e. all hero_sku)\n", "    12. <b> inv_and_sales_hero_sku_flag </b> - Merges original_inv_and_sales_logs with hero_assortment so that, we introduce a hero_sku_flag - whether item_id is a hero sku or not.\n", "    13. <b> inv_and_sales_hero_skus_only </b> - Contains all hero_sku_flag = 1 at (date x hr x outlet x category x city) level.\n", "    \n", "    \n", "    \n", "7. <b> hourly_orders </b> - Fetches (hour x date x outlet x total_orders). ```total_orders = orders at that hr, date & outlet```\n", "8. <b> cat_hourly_availability </b> - from inv_and_sales_hero_skus_only, we groupby [date x hr x outlet x cat] to find num_hero_sku_hr_fe_cat (no. of hero skus i). I.e. At a particular hour, on date xyz, at outlet xyz, we have 10 hero skus of Atta category.\n", "9. <b> inv_sales_hourly_orders </b> - At (date x hr x outlet x category), we merge the sales & inventory date of all items with (total_orders x date x outlet x hr) of hourly_orders.\n", "10. <b> inv_sales_avail_hourly_orders </b> - Now, at (date x hr x outlet x category x item) level, we have sales, cur_inv, num_hero_sku_hr_fe_cat. ```cart_pen is added to inv_sales_avail_hourly_orders as cart_pen = sales_qty/total_orders. i.e. sale_qty of item at (date x hr x outlet)/(# orders in outlet at hr x date)```\n", "11. <b> hero_assortment_size </b> - We group hero_assortment by [city x cat], and find num_unique_items & num_hero_sku_city_cat in [city x cat]\n", "12. <b> hourly_orders4 </b> - Merge inv_sales_avail_hourly_orders with hero_assortment_size, so now, we have at (date x hr x outlet x category x item) the sales, cur_inv, num_hero_sku_hr_fe_cat, num_hero_sku_city_cat (here num_hero_sku_hr_fe_cat <= num_hero_sku_city_cat should be True)\n", "13. <b> hourly_orders4['avail'] </b> - It is 1 if 70%+ of the hero_skus for [cat x city] are present at that (hr, date, outlet)\n", "14. <b> def evaluate_hourly_store_level_proposed_quantity </b> - Calculates the proposed sales\n", "    1. "]}, {"cell_type": "markdown", "id": "2a999732-dcba-485e-bfee-3b028f474b00", "metadata": {}, "source": ["# OUTPUT TABLES - \n", "1. ```blinkit_staging.ds_etls.demand_forecasting_ptype_festive_unavailability_main``` - Full proposed sales table, city/PAN India sales, orders, etc.\n", "2.```blinkit_staging.ds_etls.demand_forecasting_ptype_festive_unavailability_cat_sales_details_ov``` \n", "3.```blinkit_staging.ds_etls.demand_forecasting_ptype_festive_unavailability_cat_sales_ov``` \n", "4.```blinkit_staging.ds_etls.demand_forecasting_ptype_festive_unavailability_cat_assortment_ov_main``` \n", "5.```blinkit_staging.ds_etls.demand_forecasting_ptype_festive_unavailability_cat_assortment_ov```"]}, {"cell_type": "markdown", "id": "997c4a8e-9eac-4b80-86ab-9f25a6289173", "metadata": {}, "source": ["# Schema"]}, {"cell_type": "code", "execution_count": null, "id": "8c98c99e-8295-4386-ba87-d7a2a82fd870", "metadata": {}, "outputs": [], "source": ["unavailability_main_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_outlet_ptype_in_out_dod_hr\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date\",\n", "        },\n", "        {\n", "            \"name\": \"hour\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Hour of the day\",\n", "        },\n", "        {\n", "            \"name\": \"outlet_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\n", "            \"name\": \"cluster_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"cluster_name\",\n", "        },\n", "        {\n", "            \"name\": \"category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"category or ptype\",\n", "        },\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"festival\",\n", "        },\n", "        {\n", "            \"name\": \"proposed_sales\",\n", "            \"type\": \"double\",\n", "            \"description\": \"proposed_sales\",\n", "        },\n", "        {\n", "            \"name\": \"proposed_cart_pen\",\n", "            \"type\": \"double\",\n", "            \"description\": \"proposed_cart_pen\",\n", "        },\n", "        {\n", "            \"name\": \"final_cart_pen\",\n", "            \"type\": \"double\",\n", "            \"description\": \"final_cart_pen\",\n", "        },\n", "        {\n", "            \"name\": \"num_hero_sku_hr_fe_cat\",\n", "            \"type\": \"double\",\n", "            \"description\": \"num_hero_sku_hr_fe_cat\",\n", "        },\n", "        {\n", "            \"name\": \"num_hero_skus_city_cat\",\n", "            \"type\": \"double\",\n", "            \"description\": \"num_hero_skus_city_cat\",\n", "        },\n", "        {\n", "            \"name\": \"avail\",\n", "            \"type\": \"double\",\n", "            \"description\": \"avail\",\n", "        },\n", "        {\n", "            \"name\": \"city_sales\",\n", "            \"type\": \"double\",\n", "            \"description\": \"city_sales\",\n", "        },\n", "        {\n", "            \"name\": \"city_orders\",\n", "            \"type\": \"double\",\n", "            \"description\": \"city_orders\",\n", "        },\n", "        {\n", "            \"name\": \"cart_pen_city\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cart_pen_city\",\n", "        },\n", "        {\n", "            \"name\": \"cluster_sales\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cluster_sales\",\n", "        },\n", "        {\n", "            \"name\": \"cluster_orders\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cluster_orders\",\n", "        },\n", "        {\n", "            \"name\": \"cart_pen_cluster\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cart_pen_cluster\",\n", "        },\n", "        {\n", "            \"name\": \"pan_india_sales\",\n", "            \"type\": \"double\",\n", "            \"description\": \"pan_india_sales\",\n", "        },\n", "        {\n", "            \"name\": \"pan_india_orders\",\n", "            \"type\": \"double\",\n", "            \"description\": \"pan_india_orders\",\n", "        },\n", "        {\n", "            \"name\": \"cart_pen_pan\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cart_pen_pan\",\n", "        },\n", "        {\n", "            \"name\": \"cart_pen_fe_hr\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cart_pen_fe_hr\",\n", "        },\n", "        {\n", "            \"name\": \"total_orders_fe_hr\",\n", "            \"type\": \"double\",\n", "            \"description\": \"total_orders_fe_hr\",\n", "        },\n", "        {\n", "            \"name\": \"cur_inv_fe_hr\",\n", "            \"type\": \"double\",\n", "            \"description\": \"cur_inv_fe_hr\",\n", "        },\n", "        {\n", "            \"name\": \"sale_qty_fe_hr\",\n", "            \"type\": \"double\",\n", "            \"description\": \"sale_qty_fe_hr\",\n", "        },\n", "        {\n", "            \"name\": \"fallback_taken\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Indicates the fallback taken.\",\n", "        },\n", "        {\n", "            \"name\": \"snapshot_dt_ist\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date of creation of record of the DB\",\n", "        },\n", "        {\n", "            \"name\": \"run_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"run_id\",\n", "        },\n", "        {\n", "            \"name\": \"meta_data\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"meta_data\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"date\", \"hour\", \"outlet_id\", \"category\", \"festival\", \"run_id\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"upsert\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Captures the Proposed Sales in case of unavailability for any festival.\",\n", "    \"partition_key\": [\"festival\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "e58581b9-969e-4782-bd34-cc76be87380e", "metadata": {}, "outputs": [], "source": ["unavailability_cat_sales_details_ov_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_pan_india_ptype_in_out_dod\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date\",\n", "        },\n", "        {\n", "            \"name\": \"category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"category or ptype\",\n", "        },\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"festival\",\n", "        },\n", "        {\n", "            \"name\": \"proposed_sales\",\n", "            \"type\": \"double\",\n", "            \"description\": \"proposed_sales\",\n", "        },\n", "        {\n", "            \"name\": \"sale_qty_fe_hr\",\n", "            \"type\": \"double\",\n", "            \"description\": \"sale_qty_fe_hr\",\n", "        },\n", "        {\n", "            \"name\": \"snapshot_dt_ist\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date of creation of record of the DB\",\n", "        },\n", "        {\n", "            \"name\": \"run_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"run_id\",\n", "        },\n", "        {\n", "            \"name\": \"meta_data\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"meta_data\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"date\", \"category\", \"festival\", \"run_id\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Captures the category x date wise Proposed Sales, Sale Qty in case of unavailability for any festival.\",\n", "    \"partition_key\": [\"festival\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "dc574bd3-3337-4ae6-85c6-ddea97ba720d", "metadata": {}, "outputs": [], "source": ["unavailability_cat_sales_ov_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_pan_india_ptype_in_out\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"category or ptype\",\n", "        },\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"festival\",\n", "        },\n", "        {\n", "            \"name\": \"proposed_sales\",\n", "            \"type\": \"double\",\n", "            \"description\": \"proposed_sales\",\n", "        },\n", "        {\n", "            \"name\": \"sale_qty_fe_hr\",\n", "            \"type\": \"double\",\n", "            \"description\": \"sale_qty_fe_hr\",\n", "        },\n", "        {\n", "            \"name\": \"snapshot_dt_ist\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date of creation of record of the DB\",\n", "        },\n", "        {\n", "            \"name\": \"run_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"run_id\",\n", "        },\n", "        {\n", "            \"name\": \"meta_data\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"meta_data\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"category\", \"festival\", \"run_id\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Captures the category wise Proposed Sales, Sale Qty \",\n", "    \"partition_key\": [\"festival\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "bbc22a00-4e37-46bd-8c9b-71c6abfc6bf2", "metadata": {}, "outputs": [], "source": ["unavailability_cat_city_sales_ov_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_ptype_in_out\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"category\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"category or ptype\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"festival\",\n", "        },\n", "        {\n", "            \"name\": \"proposed_sales\",\n", "            \"type\": \"double\",\n", "            \"description\": \"proposed_sales\",\n", "        },\n", "        {\n", "            \"name\": \"sale_qty_fe_hr\",\n", "            \"type\": \"double\",\n", "            \"description\": \"sale_qty_fe_hr\",\n", "        },\n", "        {\n", "            \"name\": \"snapshot_dt_ist\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date of creation of record of the DB\",\n", "        },\n", "        {\n", "            \"name\": \"run_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"run_id\",\n", "        },\n", "        {\n", "            \"name\": \"meta_data\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"meta_data\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"category\", \"city\", \"festival\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Captures the category x city wise Proposed Sales, Sale Qty \",\n", "    \"partition_key\": [\"festival\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2d6fe213-3e85-4644-8a06-c9c3473b208e", "metadata": {}, "outputs": [], "source": ["group_ptype_mapping_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_group_ptype_mapping\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"festival\",\n", "        },\n", "        {\n", "            \"name\": \"group_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"group_name\",\n", "        },\n", "        {\n", "            \"name\": \"ptype\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"ptype\",\n", "        },\n", "        {\n", "            \"name\": \"item_id\",\n", "            \"type\": \"double\",\n", "            \"description\": \"item_id\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"festival\", \"group_name\", \"ptype\", \"item_id\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Captures the group x ptype mapping for festival inputs in - in & out forecast\",\n", "    \"partition_key\": [\"festival\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2baa5a37-d2c5-482a-81eb-56431e854428", "metadata": {}, "outputs": [], "source": ["def group_mapping_to_trino(df_group_mapping, schema_group_mapping):\n", "\n", "    # Creating interim table for Unavailability\n", "    try:\n", "\n", "        msg = f\"Pushing Festival x Group x Ptype x Item Mapping to Trino.\"\n", "        print(msg)\n", "\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        _trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "        pb.to_trino(df_group_mapping, **schema_group_mapping)\n", "\n", "        del _trino_con, _trino_conn\n", "        print(f\"Done pushing mapping to Trino..\")\n", "        print()\n", "\n", "    except Exception as e:\n", "        msg = f\"Failed to push group mapping data to interim DB, got error = {e}\"\n", "        print(msg)\n", "        raise Exception(msg)\n", "\n", "    return"]}, {"cell_type": "markdown", "id": "0baff751-57ec-45dd-b645-67864fb46864", "metadata": {}, "source": ["# Queries"]}, {"cell_type": "code", "execution_count": null, "id": "121fff67-8e1e-4e1f-8a23-3b43b0c5254c", "metadata": {}, "outputs": [], "source": ["def escape_single_quotes(value):\n", "    return value.replace(\"'\", \"''\")"]}, {"cell_type": "code", "execution_count": null, "id": "6f90f9a5-fad3-4cb2-a65d-64d5ccff3e67", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "da3a0d49-1852-4e96-a489-60b3731c53a3", "metadata": {}, "outputs": [], "source": ["def fetch_item_ptype_sold_qty(ptype_tuple, start_date, end_date):\n", "\n", "    escaped_tuple = tuple(escape_single_quotes(item) for item in ptype_tuple)\n", "    ptype_tuple_str = \", \".join([f\"'{item}'\" for item in escaped_tuple])\n", "\n", "    sql = f\"\"\"\n", "            select ipom.item_id,\n", "                icd.product_type,\n", "                sum(product_quantity * multiplier) as prod_qty\n", "            from dwh.fact_sales_order_item_details fsoid\n", "            inner join dwh.dim_item_product_offer_mapping ipom on ipom.product_id = fsoid.product_id\n", "            inner join rpc.item_category_details icd on icd.item_id = ipom.item_id\n", "            where order_create_dt_ist between date('{start_date}') and date('{end_date}')\n", "                and order_current_status = 'DELIVERED'\n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "                and ipom.is_current = true\n", "                and icd.product_type in ({ptype_tuple_str})\n", "            group by 1,2    \n", "    \"\"\"\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    item_ptype_sold_qty = read_sql_query(sql, CON_TRINO)\n", "    return item_ptype_sold_qty\n", "\n", "\n", "def fetch_hourly_availability_and_sales(start_date, end_date, item_id_list, product_type_sql):\n", "    sql = f\"\"\"\n", "        with seller_items as (\n", "            select \n", "                distinct spm.item_id as item_id\n", "            from seller.seller_product_mappings spm \n", "        ),\n", "        \n", "        hourly_sales_snap as (\n", "            select date(b.cart_checkout_ts_ist) as date_, \n", "                extract(hour from b.cart_checkout_ts_ist) as hour_,\n", "                b.outlet_id, \n", "                item_id, \n", "                SUM(product_quantity * multiplier) as product_quantity\n", "            from dwh.fact_sales_order_item_details b \n", "            left join dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = b.product_id\n", "            where b.order_create_dt_ist between date('{start_date}') and date('{end_date}')  \n", "            and order_current_status = 'DELIVERED'\n", "            and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "            and ipom.item_id in {item_id_list}\n", "            and ipom.is_current\n", "            group by 1,2,3,4\n", "        ),\n", "\n", "\n", "        active_outlets as (\n", "            select\n", "                outlet_id,\n", "                count(distinct order_id) as count_orders\n", "            from dwh.fact_sales_order_item_details\n", "            where order_create_dt_ist between date('{start_date}') and date('{end_date}')  \n", "            group by 1\n", "            having count(distinct order_id) >= 100\n", "        ),\n", "        \n", "\n", "        hourly_inventory_snap as (\n", "            select distinct date_ist as date_,\n", "                extract(hour from updated_at_ist) as hour_,\n", "                item_id,\n", "                outlet_id,\n", "                current_inventory\n", "            from supply_etls.hourly_inventory_snapshots his\n", "            where date_ist between date('{start_date}') and date('{end_date}')\n", "                and outlet_id in (select distinct outlet_id from active_outlets)\n", "                and his.item_id in {item_id_list}\n", "                and his.item_id not in (select item_id from seller_items)\n", "        )\n", "\n", "        select distinct hourly_inventory_snap.date_,\n", "            cast(hourly_inventory_snap.hour_ as int) as hour_,\n", "            cast(hourly_inventory_snap.outlet_id as int) as outlet_id,\n", "            cast(co.tax_location_id as int) as city_id,\n", "            -- cl.name as city_name,\n", "            hourly_inventory_snap.item_id,\n", "            icd.product_type_id,\n", "            -- icd.product_type,\n", "            cast(current_inventory as int) as cur_inv,\n", "            cast(product_quantity as int) as sale_qty\n", "        from hourly_inventory_snap\n", "        left join hourly_sales_snap \n", "            on hourly_inventory_snap.item_id = hourly_sales_snap.item_id\n", "            and hourly_inventory_snap.hour_ = hourly_sales_snap.hour_\n", "            and hourly_inventory_snap.date_ = hourly_sales_snap.date_\n", "            and hourly_inventory_snap.outlet_id = hourly_sales_snap.outlet_id\n", "        left join rpc.item_category_details icd on icd.item_id = hourly_inventory_snap.item_id\n", "        left join retail.console_outlet co on co.id = hourly_inventory_snap.outlet_id\n", "        left join retail.console_location cl on cl.id = co.tax_location_id\n", "    \n", "    \"\"\"\n", "\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    hourly_availability_and_sales = read_sql_query(sql, CON_TRINO)\n", "    return hourly_availability_and_sales\n", "\n", "\n", "def fetch_outlet_city_mapping():\n", "    sql = \"\"\" \n", "    select \n", "        distinct outlet_id, \n", "        -- cl.name as city_name,\n", "        cl.id as city_id\n", "    from \n", "        po.physical_facility_outlet_mapping pfom\n", "        \n", "    inner join retail.console_location cl \n", "        on pfom.city_id = cl.id    \n", "\n", "    \"\"\"\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    city_store_mapping = read_sql_query(sql, CON_TRINO)\n", "    return city_store_mapping\n", "\n", "\n", "def fetch_item_ptype_mapping(product_type_sql):\n", "\n", "    escaped_tuple = tuple(escape_single_quotes(item) for item in product_type_sql)\n", "    product_type_sql_str = \", \".join([f\"'{item}'\" for item in escaped_tuple])\n", "\n", "    my_sql = f\"\"\"\n", "            select distinct item_id, \n", "                product_type \n", "            from rpc.item_category_details \n", "            where product_type  in ({product_type_sql_str})\n", "        \"\"\"\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    item_ptype_mapping = read_sql_query(my_sql, CON_TRINO)\n", "    return item_ptype_mapping\n", "\n", "\n", "def fetch_hourly_dod_orders(start_date_sql, end_date_sql):\n", "    dod_orders_sql = f\"\"\"\n", "        select date(cart_checkout_ts_ist) as date_,\n", "            extract (hour from cart_checkout_ts_ist) as hour_,\n", "            fsoid.outlet_id,\n", "            count(distinct order_id) as total_orders\n", "        from dwh.fact_sales_order_item_details fsoid \n", "        where cart_checkout_ts_ist between date('{start_date_sql}') and date('{end_date_sql}')\n", "            and order_create_ts_ist between date('{start_date_sql}') and date('{end_date_sql}')\n", "            and order_create_dt_ist between date('{start_date_sql}') and date('{end_date_sql}')\n", "            and order_current_status = 'DELIVERED'\n", "            and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "        group by 1,2,3\n", "    \"\"\"\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    dod_orders_mapping = read_sql_query(dod_orders_sql, CON_TRINO)\n", "    return dod_orders_mapping\n", "\n", "\n", "def fetch_cluster_mapping():\n", "    sql_city_clusters = \"\"\"\n", "        with cluster as (\n", "            select \n", "                cluster_id, \n", "                cluster_name\n", "            from rpc.ams_cluster \n", "            where cluster_id > 15000 and lake_active_record\n", "        ), \n", "\n", "        cluster_city as (\n", "            select \n", "                cluster_name, \n", "                cluster.cluster_id, \n", "                city_id\n", "            from rpc.ams_city_cluster_mapping accm\n", "            inner join cluster on cluster.cluster_id = accm.cluster_id and lake_active_record and active\n", "        ),\n", "\n", "        city_id_name_mapping as (\n", "        select\n", "            cc.cluster_name,\n", "            cc.cluster_id,\n", "            cc.city_id,\n", "            cl.name as city_name\n", "        from cluster_city cc\n", "        left join retail.console_location cl\n", "        on cl.id = cc.city_id\n", "        )\n", "        select \n", "            distinct cluster_name, \n", "            cluster_id, \n", "            city_id,\n", "            city_name\n", "        from city_id_name_mapping\n", "    \"\"\"\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    df_city_clusters = read_sql_query(sql_city_clusters, CON_TRINO)\n", "    return df_city_clusters"]}, {"cell_type": "markdown", "id": "45744937-e051-4764-b411-63a1f29a3621", "metadata": {}, "source": ["# Inputs"]}, {"cell_type": "code", "execution_count": null, "id": "5831e571-c8de-40a4-8889-50177aef7209", "metadata": {}, "outputs": [], "source": ["# input_df = pd.DataFrame()\n", "# input_df['start_date'] = ['2025-03-05']\n", "# input_df['end_date'] = ['2025-03-15']\n", "# input_df['Festival'] = ['Holi_2025']\n", "# input_df['hero_sku_avail_cutoff'] =[0.6]\n", "# input_df['cum_per_contri_cutoff'] = [0.8]\n", "# input_df['usage_in_forecasting'] = [1]\n", "# input_df['overwrite_flag'] = [1]\n", "\n", "\n", "# input_df['top_assortment_cutoff'] = [0.075]\n", "# input_df['city_order_cutoff'] = [75]\n", "# input_df['key_rank_intent_cutoff'] = [1]\n", "# input_df['intent_percent_attri_cutoff'] = [0.05]\n", "\n", "df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)\n", "\n", "\n", "df[\"start_date\"] = df[\"festival_start_date\"]\n", "df[\"end_date\"] = df[\"festival_end_date\"]\n", "df[\"Festival\"] = df[\"festival\"]\n", "df[\"hero_sku_avail_cutoff\"] = df[\"hero_sku_availability\"].astype(float)\n", "df[\"cum_per_contri_cutoff\"] = df[\"hero_sku_sales_contribution\"].astype(float)\n", "df[\"usage_in_forecasting\"] = df[\"flag\"].astype(int)\n", "df[\"overwrite_flag\"] = df[\"overwrite_flag\"].astype(int)\n", "\n", "df[\"top_assortment_cutoff\"] = 0.075\n", "df[\"city_order_cutoff\"] = 75\n", "df[\"key_rank_intent_cutoff\"] = 1\n", "df[\"intent_percent_attri_cutoff\"] = 0.05\n", "\n", "\n", "input_df = df[\n", "    [\n", "        \"start_date\",\n", "        \"end_date\",\n", "        \"Festival\",\n", "        \"hero_sku_avail_cutoff\",\n", "        \"cum_per_contri_cutoff\",\n", "        \"top_assortment_cutoff\",\n", "        \"city_order_cutoff\",\n", "        \"key_rank_intent_cutoff\",\n", "        \"intent_percent_attri_cutoff\",\n", "        \"overwrite_flag\",\n", "        \"usage_in_forecasting\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "10c6e2a1-292e-484b-a61f-cc2b43ad9dfc", "metadata": {}, "outputs": [], "source": ["input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9ad6492f-c6c0-41a9-b5f8-5a33e1853a17", "metadata": {}, "outputs": [], "source": ["get_usage_fests = input_df[input_df[\"usage_in_forecasting\"] == 1][\"Festival\"].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "56880762-2123-4714-add5-63d321222c58", "metadata": {}, "outputs": [], "source": ["# Ensure the list is not empty\n", "\n", "if get_usage_fests:\n", "\n", "    if len(get_usage_fests) == 1:\n", "        festival_tuple = f\"'{get_usage_fests[0]}'\"  # Single festival, just a string\n", "    else:\n", "        festival_tuple = \", \".join(\n", "            f\"'{festival}'\" for festival in get_usage_fests\n", "        )  # Multiple festivals, formatted as a string of values\n", "\n", "    # Construct SQL query with the festival tuple\n", "    sql_to_read_ptypes = f\"\"\"\n", "    SELECT \n", "        distinct festival_name, product_type\n", "    FROM supply_etls.festival_forecast_city_ptype_flag\n", "    WHERE \n", "        festival_name IN ({festival_tuple}) \n", "        AND avg_spike >= 1.5\n", "    \"\"\"\n", "\n", "    # Execute the query\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql(sql_to_read_ptypes, con=con)\n", "\n", "    # If no festival is not present in supply_etls.festival_forecast_city_ptype_flag\n", "    if df.shape[0] == 0:\n", "        ptypes = {fest: [] for fest in get_usage_fests}\n", "        groups = {}\n", "\n", "    else:\n", "        df_grouped = df.groupby(\"festival_name\")\n", "\n", "        ###### Fetch Ptypes ######\n", "        ptypes = {}\n", "\n", "        ptypes = df.groupby(\"festival_name\")[\"product_type\"].apply(list).to_dict()\n", "        del (\n", "            df_grouped,\n", "            df,\n", "        )\n", "\n", "        # If a particular festival is not present in supply_etls.festival_forecast_city_ptype_flag\n", "        for fest in get_usage_fests:\n", "            if fest not in ptypes.keys():\n", "                ptypes[fest] = []\n", "        ##############################\n", "\n", "        ###### Fetch Groups ######\n", "        df_group = pb.from_sheets(\n", "            \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"grouped_ptypes\", clear_cache=True\n", "        )\n", "        df_group[\"flag\"] = df_group[\"flag\"].astype(int)\n", "\n", "        df_group_mapping = df_group.copy()\n", "\n", "        df_group_mapping[\"item_id\"] = pd.to_numeric(df_group_mapping[\"item_id\"], errors=\"coerce\")\n", "        df_group_mapping[\"item_id\"] = df_group_mapping[\"item_id\"].fillna(-999)\n", "        df_group_mapping = df_group_mapping.rename(columns={\"group\": \"group_name\"})\n", "\n", "        df_group_mapping[\"item_id\"] = df_group_mapping[\"item_id\"].astype(int)\n", "        df_group_mapping = df_group_mapping[[\"festival\", \"group_name\", \"ptype\", \"item_id\"]]\n", "\n", "        print(\"Pushing the group x ptype x item mapping to trino..\")\n", "        group_mapping_to_trino(df_group_mapping, group_ptype_mapping_schema)\n", "\n", "        df_group = df_group[df_group[\"flag\"] == 1]\n", "\n", "        result = {}\n", "        festival_group = pd.DataFrame()\n", "\n", "        ### Group by festival, then group by group within each festival\n", "        for festival, festival_group in df_group.groupby(\"festival\"):\n", "\n", "            festival_dict = {}\n", "\n", "            for group, group_group in festival_group.groupby(\"group\"):\n", "                elements = tuple(group_group[\"ptype\"])\n", "                festival_dict[group] = elements\n", "\n", "            result[festival] = festival_dict\n", "\n", "        groups = result.copy()\n", "\n", "        del (df_group, festival_group, result)\n", "        ##############################\n", "\n", "else:\n", "    # Handle the case where there are no festivals to query\n", "    print(\"No festivals to query.\")\n", "    ptypes = {}\n", "    groups = {}"]}, {"cell_type": "code", "execution_count": null, "id": "d7dd5baa-9382-489d-9c92-dfa5c571de31", "metadata": {}, "outputs": [], "source": ["print(\"Ptypes are --\")\n", "print(ptypes)\n", "print()\n", "\n", "print(\"Groups are --\")\n", "print(groups)"]}, {"cell_type": "markdown", "id": "9c096b3b-7075-45bf-8088-c150ce972cdf", "metadata": {}, "source": ["## Input Processing"]}, {"cell_type": "code", "execution_count": null, "id": "611755f9-278d-4015-8edd-f4876bbaf34d", "metadata": {}, "outputs": [], "source": ["def group_ptypes_category_items(ptypes, groups):\n", "    \"\"\"\n", "    Function for fetching item, ptype & category mapping.\n", "\n", "    Returns:\n", "        Returns category, ptype, item_id in a DataFrame\n", "        Format: pd.DataFrame(['Mugs_overall', 'Mug Set', 123], ['Mugs_overall', 'Mug Set', 146], ['Mugs_overall', 'Mug', 144])\n", "\n", "    Args:\n", "        ptypes (tuple): Tuple of independent ptypes\n", "        groups (dict): Group to ptype mapping.\n", "    \"\"\"\n", "\n", "    # Get tuple of ptypes\n", "\n", "    group_ptypes = []\n", "    for group_name, ptype_lst in groups.items():\n", "        group_ptypes.extend(list(ptype_lst))\n", "\n", "    final_ptype_tuple = tuple(ptypes + group_ptypes)\n", "\n", "    # Obtain item, ptype information\n", "    item_ptype_sold_qty = fetch_item_ptype_sold_qty(final_ptype_tuple, start_date, end_date)\n", "    item_ptype_mapping = item_ptype_sold_qty[[\"item_id\", \"product_type\"]]\n", "\n", "    # Create category to ptype mapping for groups of product_types\n", "    df1 = pd.DataFrame(\n", "        [(key, value) for key, values in groups.items() for value in values],\n", "        columns=[\"category\", \"product_type\"],\n", "    )\n", "\n", "    # Create category to ptype mapping for ptypes\n", "    df2 = pd.DataFrame(ptypes, columns=[\"product_type\"])\n", "    df2[\"category\"] = df2[\"product_type\"]\n", "    df2 = df2[[\"category\", \"product_type\"]]\n", "    cat_ptype_mapping = pd.concat([df1, df2], axis=0, ignore_index=True)\n", "    cat_ptype_item_mapping = cat_ptype_mapping.merge(\n", "        item_ptype_mapping, on=[\"product_type\"], how=\"left\"\n", "    )\n", "\n", "    cat_ptype_item_mapping = cat_ptype_item_mapping.drop_duplicates().reset_index(drop=True)\n", "\n", "    del (df1, df2, item_ptype_sold_qty, item_ptype_mapping, final_ptype_tuple, group_ptypes)\n", "\n", "    return cat_ptype_item_mapping"]}, {"cell_type": "code", "execution_count": null, "id": "1ca5d584-ec47-4578-a084-ab7bc82e1b94", "metadata": {}, "outputs": [], "source": ["# def greedy_selection(lst):\n", "#     result = []\n", "#     left = 0\n", "#     right = len(lst) - 1\n", "\n", "#     while left <= right:\n", "#         if left == right:\n", "#             result.append(lst[left][0])  # Only one element left, append it\n", "#         else:\n", "#             result.append(lst[left][0])  # First element\n", "#             result.append(lst[right][0])  # Last element\n", "\n", "#         left += 1\n", "#         right -= 1\n", "\n", "#     return result\n", "\n", "\n", "def group_by_max_tags(tags_vals, max_value=100):\n", "\n", "    sorted_tags_vals = sorted(tags_vals, key=lambda x: x[1])\n", "\n", "    groups = []\n", "    groups_items = []\n", "    current_group = []\n", "    current_sum = 0\n", "\n", "    for tag, val in sorted_tags_vals:\n", "        if current_sum + val <= max_value:\n", "            # Add to the current group if it doesn't exceed the max value\n", "            current_group.append(tag)\n", "            current_sum += val\n", "        else:\n", "            # Start a new group if adding the tag exceeds the max value\n", "            groups.append(current_group)\n", "            current_group = [tag]\n", "            current_sum = val\n", "\n", "    # Append the last group if it has any remaining tags\n", "    if current_group:\n", "        groups.append(current_group)\n", "\n", "    return groups\n", "\n", "\n", "def category_size_batch(unbatched_df):\n", "    \"\"\"\n", "    Creates Batches for processing based on size of group. Group is the items in a category.\n", "    This is achieved by greedy knapsack - where groups with less items are clustered together.\n", "    This would support categories as a list, and chunking of 4 categories for processing.\n", "\n", "    Returns:\n", "        Returns the batches containing item ids for each group to be processed in one go.\n", "        Format: []\n", "\n", "    Args:\n", "        batch_size (int): Size of group allowed\n", "        unbatched_df (pd.DataFrame): Containing category, ptype, item mapping (cat_ptype_item_mapping)\n", "    \"\"\"\n", "\n", "    # Create batches of batch_size items with minimum batches\n", "    batches = []\n", "    remaining_categories = []\n", "    # Group items by category\n", "    grouped = unbatched_df[[\"item_id\", \"category\"]].groupby(\"category\")\n", "\n", "    # Collect all groups as (category, group_items, group_size)\n", "    for category, group in grouped:\n", "        group_items = group.to_dict(\"records\")\n", "        group_size = len(group_items)\n", "\n", "        remaining_categories.append([category, group_size])\n", "\n", "    # Sort categories by size (descending) to fill larger items first\n", "    remaining_categories.sort(key=lambda x: x[1], reverse=True)\n", "\n", "    # Select (first, second, last, second_last) as one batch of ptypes.\n", "    # category_ordered = greedy_selection(remaining_categories)\n", "    category_ordered = group_by_max_tags(remaining_categories)\n", "\n", "    del (remaining_categories, grouped)\n", "\n", "    return category_ordered"]}, {"cell_type": "markdown", "id": "21a43359-7677-4fe5-8594-6130fa5c9c53", "metadata": {}, "source": ["## Find (Festivals, Category) that need to be Pushed."]}, {"cell_type": "code", "execution_count": null, "id": "cdbcb4ef-fd7e-4119-999b-a216cd1c9624", "metadata": {}, "outputs": [], "source": ["def festivals_to_push(df_input, ptype_dict, group_dict):\n", "\n", "    dict_festival_ptypes = {}\n", "\n", "    input_combinations = []\n", "\n", "    dict_festival_run_id = {}\n", "\n", "    table_present = pd.DataFrame()\n", "\n", "    for i in range(len(df_input)):\n", "        usage = df_input[\"usage_in_forecasting\"][i]\n", "\n", "        if usage == 0:\n", "            continue\n", "\n", "        year_date = df_input[\"start_date\"][i][:4]\n", "        festival = df_input[\"Festival\"][i]\n", "        start_date = df_input[\"start_date\"][i]\n", "        end_date = df_input[\"end_date\"][i]\n", "        hero_sku_avail_cutoff = df_input[\"hero_sku_avail_cutoff\"][i]\n", "        city_order_cutoff = df_input[\"city_order_cutoff\"][i]\n", "        top_assortment_cutoff = df_input[\"top_assortment_cutoff\"][i]\n", "        cum_per_contri_cutoff = df_input[\"cum_per_contri_cutoff\"][i]\n", "        meta_data = f\"hero_sku_avail_cutoff : {hero_sku_avail_cutoff}, city_order_cutoff : {city_order_cutoff}, top_assortment_cutoff : {top_assortment_cutoff}, cum_per_contri_cutoff : {cum_per_contri_cutoff}, start_date: {start_date}, end_date: {end_date}\"\n", "\n", "        if festival in group_dict.keys():\n", "            input_ptypes = ptype_dict[festival] + list(group_dict[festival].keys())\n", "        else:\n", "            input_ptypes = ptype_dict[festival]\n", "\n", "        input_ptypes = list(set(input_ptypes))\n", "\n", "        # SQL Query to Get Festivals Already in Table\n", "        sql_festivals_in_trino = f\"\"\"\n", "        select \n", "            distinct category, meta_data, run_id\n", "        from \n", "            supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "        where \n", "            festival = '{festival}'\n", "        \"\"\"\n", "\n", "        sql_run_id = f\"\"\"\n", "        select \n", "            max(run_id) as \"max_run_id\"\n", "        from \n", "            supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "        where \n", "            festival = '{festival}'\n", "        \"\"\"\n", "\n", "        try:\n", "\n", "            CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "            table_present = pd.read_sql(sql_festivals_in_trino, con=CON_TRINO)\n", "\n", "            if len(table_present) != 0:\n", "                combinations_present = table_present.apply(\n", "                    lambda row: (row[\"category\"], row[\"meta_data\"], row[\"run_id\"]), axis=1\n", "                ).tolist()\n", "                run_id_df = pd.read_sql(sql_run_id, con=CON_TRINO)\n", "                max_run_id = int(run_id_df[\"max_run_id\"][0])\n", "\n", "            else:\n", "                table_present = pd.DataFrame()\n", "                combinations_present = []\n", "                max_run_id = 0\n", "\n", "        except Exception as e:\n", "            print(f\"Error while computing run id for {festival}, error msg = {e}\")\n", "            max_run_id = 0\n", "            combinations_present = []\n", "\n", "        # Overwrite full run for that festival.\n", "        if df_input[\"overwrite_flag\"][i] == 1:\n", "\n", "            dict_festival_ptypes[festival] = {\n", "                \"meta_data\": meta_data,\n", "                \"category_list\": input_ptypes,\n", "            }\n", "\n", "            dict_festival_run_id[festival] = max_run_id + 1\n", "            continue\n", "\n", "        if combinations_present == []:\n", "            dict_festival_ptypes[festival] = {\"meta_data\": meta_data, \"category_list\": input_ptypes}\n", "            dict_festival_run_id[festival] = max_run_id + 1\n", "\n", "        else:\n", "\n", "            combinations_input = []\n", "\n", "            # i.e. same meta data is present...\n", "            if meta_data in table_present[\"meta_data\"].unique().tolist():\n", "                max_run_id = table_present[table_present[\"meta_data\"] == meta_data][\"run_id\"].max()\n", "            else:\n", "                max_run_id = max_run_id + 1\n", "\n", "            dict_festival_run_id[festival] = max_run_id\n", "\n", "            for ptype in input_ptypes:\n", "                combinations_input.append((ptype, meta_data, max_run_id))\n", "\n", "            combinations_to_push = list(set(combinations_input) - set(combinations_present))\n", "            list_ptypes_to_push = [combo[0] for combo in combinations_to_push]\n", "            list_meta_data = [combo[1] for combo in combinations_to_push]\n", "            dict_festival_ptypes[festival] = {\n", "                \"meta_data\": meta_data,\n", "                \"category_list\": list_ptypes_to_push,\n", "            }\n", "\n", "    del table_present\n", "\n", "    return dict_festival_ptypes, dict_festival_run_id"]}, {"cell_type": "code", "execution_count": null, "id": "bfb766ce-ea0b-4dca-87e6-4a1f99fc86ae", "metadata": {}, "outputs": [], "source": ["dict_festival_ptypes, dict_festival_run_id = festivals_to_push(input_df, ptypes, groups)\n", "dict_festival_ptypes"]}, {"cell_type": "code", "execution_count": null, "id": "cfa8d6e7-d8f3-4ec2-9b6f-e1d6d9368913", "metadata": {}, "outputs": [], "source": ["dict_festival_run_id"]}, {"cell_type": "markdown", "id": "78d6500f-54bb-48a7-9da5-b5afe346a269", "metadata": {}, "source": ["# Helper Functions"]}, {"cell_type": "code", "execution_count": null, "id": "3d5e6a56-3f01-4579-bc66-1cfb28d6a0f1", "metadata": {}, "outputs": [], "source": ["# Chunking Ptypes\n", "def chunk_ptype_list(lst, chunk_size):\n", "\n", "    # Using itertools.islice to break the list into chunks\n", "    return (lst[i : i + chunk_size] for i in range(0, len(lst), chunk_size))"]}, {"cell_type": "code", "execution_count": null, "id": "59d74658-d687-4c24-90eb-903b6d34ead7", "metadata": {}, "outputs": [], "source": ["def push_to_trino_table(dictionary_dfs, festival, dictionary_schemas):\n", "\n", "    for df_name, dataframe in dictionary_dfs.items():\n", "\n", "        dataframe_final = dataframe.copy()\n", "        schema = dictionary_schemas[df_name]\n", "\n", "        # Creating interim table for Unavailability\n", "        try:\n", "            # msg = f\"Creating {df_name}, having dimensions - {dataframe_final.shape} data for festival = {festival}, festival_year = {festival_year}\"\n", "            msg = f\"Populating {schema['table_name']} with {df_name}, having dimensions - {dataframe_final.shape} data for festival = {festival}\"\n", "            print(msg)\n", "\n", "            _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "            _trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "            pb.to_trino(dataframe_final, **schema)\n", "\n", "            del _trino_con, _trino_conn\n", "            print(f\"Done pushing {df_name} to Trino..\")\n", "            print()\n", "\n", "        except Exception as e:\n", "            msg = f\"Failed to push {df_name} data to interim DB for = {festival}, got error = {e}\"\n", "            print(msg)\n", "            raise Exception(msg)\n", "            break\n", "\n", "    return"]}, {"cell_type": "code", "execution_count": null, "id": "f8482cae-e50c-4afd-928d-350a701995ab", "metadata": {}, "outputs": [], "source": ["# def case_logic_for_availability(row, hero_sku_avail_cutoff):\n", "#     if row[\"num_hero_skus_city_cat\"] == 0:\n", "#         return 0\n", "\n", "#     if (row[\"num_hero_skus_city_cat\"] >= 1) & (\n", "#         (1.00 * row[\"num_hero_sku_hr_fe_cat\"]) / row[\"num_hero_skus_city_cat\"]\n", "#         > hero_sku_avail_cutoff\n", "#     ):\n", "#         return 1\n", "#     else:\n", "#         return 0"]}, {"cell_type": "code", "execution_count": null, "id": "6b4b7faa-f16e-4b8d-b121-62d29da155c0", "metadata": {}, "outputs": [], "source": ["def festival_details(df_festival, row_idx=0):\n", "\n", "    snapshot_dt_ist = today.strftime(\"%Y-%m-%d\")\n", "\n", "    start_date = df_festival[\"start_date\"][row_idx]\n", "    end_date = df_festival[\"end_date\"][row_idx]\n", "\n", "    festival = df_festival[\"Festival\"][row_idx]\n", "    # festival_year = df_festival['Festival_Year'][row_idx]\n", "\n", "    # Fetching Hyper-Parameters..\n", "    hero_sku_avail_cutoff = df_festival[\"hero_sku_avail_cutoff\"][row_idx]\n", "    city_order_cutoff = df_festival[\"city_order_cutoff\"][row_idx]\n", "    top_assortment_cutoff = df_festival[\"top_assortment_cutoff\"][row_idx]\n", "    cum_per_contri_cutoff = df_festival[\"cum_per_contri_cutoff\"][row_idx]\n", "\n", "    usage_in_forecasting = df_festival[\"usage_in_forecasting\"][row_idx]\n", "\n", "    return (\n", "        start_date,\n", "        end_date,\n", "        festival,\n", "        hero_sku_avail_cutoff,\n", "        city_order_cutoff,\n", "        top_assortment_cutoff,\n", "        cum_per_contri_cutoff,\n", "        usage_in_forecasting,\n", "        snapshot_dt_ist,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "ab1d96a9-9815-44c6-bf20-3b26f1874649", "metadata": {}, "outputs": [], "source": ["def fetch_item_level_availability(\n", "    start_date, end_date, cat_ptype_item_mapping_subset, product_type_sql, item_id_list, ans=\"new\"\n", "):\n", "    \"\"\"\n", "    Fetches input data inv_and_sales_logs_raw at (date x hr x outlet x item x ptype x city) level.\n", "    Calculates avail based on curr_inv.\n", "    Add category to inv_and_sales_logs_raw, by merging. New DF - inv_and_sales_logs_with_cat\n", "\n", "\n", "    Returns:\n", "        Returns inv_and_sales_logs_with_cat\n", "        Format: (date x hr x outlet x item x ptype x city X CATEGORY) level\n", "\n", "    Args:\n", "        ans (str): Whether to use old/new query\n", "        start_date (str): start_date of event\n", "        end_date (str): end_date of event\n", "        cat_ptype_item_mapping (pd.DataFrame): category x ptype x item mapping.\n", "        item_id_list (list): all item_ids\n", "    \"\"\"\n", "\n", "    # Evaluate\n", "    # input('Which logs tables to use, old/new')\n", "\n", "    print(\"Executing Query for Input Data\")\n", "\n", "    ## If number of items to process are greater than 100, in that case fetch the date from query in chunks\n", "    if len(item_id_list) > 100:\n", "\n", "        item_id_list_new = chunk_ptype_list(item_id_list, 100)\n", "        inv_and_sales_logs_with_cat = pd.DataFrame()\n", "\n", "        for chunk_no, item_id_list in enumerate(item_id_list_new):\n", "            print(\n", "                f\"Fetching data from for chunk {chunk_no+1}, length of items = {len(item_id_list)}\"\n", "            )\n", "\n", "            if len(item_id_list) == 1:\n", "                item_id_list = item_id_list + (-999,)\n", "\n", "            inv_and_sales_logs_raw_new_chunk = fetch_hourly_availability_and_sales(\n", "                start_date, end_date, item_id_list, product_type_sql\n", "            )\n", "\n", "            print(f\"Shape of {chunk_no+1} chunk = {inv_and_sales_logs_raw_new_chunk.shape}\")\n", "            inv_and_sales_logs_with_cat = pd.concat(\n", "                [inv_and_sales_logs_with_cat, inv_and_sales_logs_raw_new_chunk], ignore_index=True\n", "            )\n", "\n", "            del inv_and_sales_logs_raw_new_chunk\n", "\n", "        print(\"All chunks processed..\")\n", "        print(\n", "            f\"Shape of the Inventory + Sales dataframe fetched = {inv_and_sales_logs_with_cat.shape}\"\n", "        )\n", "\n", "    ## If number of items are less, in that case fetch all items together.\n", "    else:\n", "        print(\"Chunking process for the Inventory + Sales query not required.\")\n", "        inv_and_sales_logs_with_cat = fetch_hourly_availability_and_sales(\n", "            start_date, end_date, item_id_list, product_type_sql\n", "        )\n", "\n", "    inv_and_sales_logs_with_cat = inv_and_sales_logs_with_cat.fillna(0)\n", "\n", "    # Adjust for inventory reporting inconsistency\n", "    inv_and_sales_logs_with_cat[\"cur_inv\"] = np.where(\n", "        inv_and_sales_logs_with_cat[\"cur_inv\"] < inv_and_sales_logs_with_cat[\"sale_qty\"],\n", "        inv_and_sales_logs_with_cat[\"sale_qty\"],\n", "        inv_and_sales_logs_with_cat[\"cur_inv\"],\n", "    )\n", "\n", "    inv_and_sales_logs_with_cat[\"avail\"] = np.where(\n", "        inv_and_sales_logs_with_cat[\"cur_inv\"] > 0, 1, 0\n", "    )\n", "\n", "    # Map category to ptypes\n", "    inv_and_sales_logs_with_cat = inv_and_sales_logs_with_cat.merge(\n", "        cat_ptype_item_mapping_subset[[\"category\", \"item_id\"]], on=[\"item_id\"], how=\"left\"\n", "    )\n", "\n", "    # del (inv_and_sales_logs_raw_new, inv_and_sales_logs)\n", "    gc.collect()\n", "\n", "    return inv_and_sales_logs_with_cat"]}, {"cell_type": "code", "execution_count": null, "id": "9bff8c76-d235-415e-a92f-489d35c3b3e3", "metadata": {}, "outputs": [], "source": ["def compute_top_assortment(group, top_assortment_cutoff):\n", "\n", "    if (group[\"per_contri\"] > top_assortment_cutoff).any():\n", "        return group[group[\"per_contri\"] > top_assortment_cutoff]\n", "    else:\n", "        if group[\"sale_qty\"].max() == 0:\n", "            return pd.DataFrame(columns=group.columns)\n", "        else:\n", "            return group.loc[group[\"per_contri\"].idxmax()]\n", "\n", "\n", "def city_sales_prep(inv_and_sales_logs_avail, groupby_cols, top_assortment_cutoff):\n", "    \"\"\"\n", "    Filters Top Assortments of a city.\n", "\n", "    Returns:\n", "        1. city_sales_top_assortment - Removes noise (items) that contribute less than 7.5% sale for [city x category]\n", "        2. city_sales_top_assortment_size - Contains [city x category x num_items_city_cat].\n", "        [Counts no. of unique items for city x category] based on city_sales_top_assortment.\n", "\n", "\n", "    Args:\n", "        inv_and_sales_logs_with_cat (pd.DataFrame)): (date x hr x outlet x item x ptype x city X CATEGORY) level input dataframe.\n", "    \"\"\"\n", "\n", "    city_sales = (\n", "        inv_and_sales_logs_avail[groupby_cols + [\"sale_qty\"]]\n", "        .groupby(by=groupby_cols)\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "\n", "    if \"sale_qty\" not in city_sales.columns:\n", "        city_sales[\"sale_qty\"] = []\n", "\n", "    # Finding Cumulative Sales\n", "    city_sales[\"cum_sale_qty\"] = city_sales.groupby([\"city_id\", \"category\"])[\"sale_qty\"].transform(\n", "        \"sum\"\n", "    )\n", "    city_sales = city_sales.sort_values(by=[\"city_id\", \"category\"], ascending=True).reset_index(\n", "        drop=True\n", "    )\n", "\n", "    # Find Contribution of city, ptype sales.\n", "    city_sales[\"per_contri\"] = city_sales[\"sale_qty\"] / city_sales[\"cum_sale_qty\"]\n", "    city_sales = city_sales.sort_values(\n", "        by=[\"city_id\", \"category\", \"per_contri\"], ascending=[True, True, False]\n", "    ).reset_index(drop=True)\n", "\n", "    city_sales[\"cum_per_contri\"] = city_sales.groupby([\"city_id\", \"category\"])[\n", "        \"per_contri\"\n", "    ].cumsum()\n", "\n", "    city_sales_top_assortment = (\n", "        city_sales.groupby([\"city_id\", \"category\"])\n", "        .apply(compute_top_assortment, top_assortment_cutoff=top_assortment_cutoff)\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    # Evaluating city category important assortment\n", "    city_sales_top_assortment_size = (\n", "        city_sales_top_assortment[[\"city_id\", \"category\", \"item_id\"]]\n", "        .groupby(by=[\"city_id\", \"category\"])\n", "        .nunique()\n", "        .reset_index()\n", "    )\n", "    city_sales_top_assortment_size = city_sales_top_assortment_size.rename(\n", "        columns={\"item_id\": \"num_items_city_cat\"}\n", "    )\n", "\n", "    del city_sales\n", "    return city_sales_top_assortment, city_sales_top_assortment_size\n", "\n", "\n", "def find_hero_skus(\n", "    full_city_cat_assortment_at_fe_hr, inv_and_sales_logs_avail, cum_per_contri_cutoff\n", "):\n", "\n", "    # EVALUAITNG TOP ASSORTMENT CONTRIBUTING ITEMS\n", "    df0 = (\n", "        full_city_cat_assortment_at_fe_hr[[\"city_id\", \"item_id\", \"category\", \"sale_qty\"]]\n", "        .groupby(by=[\"city_id\", \"item_id\", \"category\"])\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "    df0[\"cum_sale_qty\"] = df0.groupby([\"city_id\", \"category\"])[\"sale_qty\"].transform(\"sum\")\n", "    df0 = df0.sort_values(by=[\"city_id\", \"category\"], ascending=True).reset_index(drop=True)\n", "    df0[\"per_contri\"] = df0[\"sale_qty\"] / df0[\"cum_sale_qty\"]\n", "    df0 = df0.sort_values(\n", "        by=[\"city_id\", \"category\", \"per_contri\"], ascending=[True, True, False]\n", "    ).reset_index(drop=True)\n", "    df0[\"cum_per_contri\"] = df0.groupby([\"city_id\", \"category\"])[\"per_contri\"].cumsum()\n", "    # filtered_df4_assortment = df0[df0['cum_per_contri'] < cum_per_contri_cutoff]\n", "\n", "    filtered_df4_assortment = df0[\n", "        (df0[\"cum_per_contri\"] < cum_per_contri_cutoff)\n", "        | (df0[\"per_contri\"] >= cum_per_contri_cutoff)\n", "    ]\n", "\n", "    del df0\n", "    # EVALUAITNG TOP ASSORTMENT CONTRIBUTING ITEMS\n", "    df0 = (\n", "        inv_and_sales_logs_avail[[\"city_id\", \"item_id\", \"category\", \"sale_qty\"]]\n", "        .groupby(by=[\"city_id\", \"item_id\", \"category\"])\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "    df0[\"cum_sale_qty\"] = df0.groupby([\"city_id\", \"category\"])[\"sale_qty\"].transform(\"sum\")\n", "    df0 = df0.sort_values(by=[\"city_id\", \"category\"], ascending=True).reset_index(drop=True)\n", "    df0[\"per_contri\"] = df0[\"sale_qty\"] / df0[\"cum_sale_qty\"]\n", "    df0 = df0.sort_values(\n", "        by=[\"city_id\", \"category\", \"per_contri\"], ascending=[True, True, False]\n", "    ).reset_index(drop=True)\n", "    df0[\"cum_per_contri\"] = df0.groupby([\"city_id\", \"category\"])[\"per_contri\"].cumsum()\n", "    # filtered_df5_assortment = df1[df1['cum_per_contri'] < cum_per_contri_cutoff]\n", "\n", "    filtered_df5_assortment = df0[\n", "        (df0[\"cum_per_contri\"] < cum_per_contri_cutoff)\n", "        | (df0[\"per_contri\"] >= cum_per_contri_cutoff)\n", "    ]\n", "\n", "    del df0\n", "\n", "    filtered_df4_assortment = filtered_df4_assortment[[\"city_id\", \"item_id\", \"category\"]]\n", "    filtered_df5_assortment = filtered_df5_assortment[[\"city_id\", \"item_id\", \"category\"]]\n", "    # a.merge(b, how=\"left\", indicator=True, on=[\"city_id\", \"item_id\", \"category\"])\n", "    hero_assortment = (\n", "        pd.concat([filtered_df4_assortment, filtered_df5_assortment])\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "    hero_assortment[\"hero_sku_flag\"] = 1\n", "\n", "    hero_assortment_size = (\n", "        hero_assortment.groupby(by=[\"city_id\", \"category\"])\n", "        .agg({\"item_id\": \"nunique\", \"hero_sku_flag\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "    hero_assortment_size = hero_assortment_size.rename(\n", "        columns={\"hero_sku_flag\": \"num_hero_skus_city_cat\"}\n", "    )\n", "\n", "    del (filtered_df4_assortment, filtered_df5_assortment)\n", "\n", "    return hero_assortment, hero_assortment_size\n", "\n", "\n", "def make_assortment_please(inv_and_sales_logs_with_cat, top_assortment_cutoff=0.075):\n", "    \"\"\"\n", "    Makes various assortment Dataframes, where hero_sku_flags are introduced, city sales items, and various other flags/contri are computed.\n", "\n", "\n", "    Returns:\n", "        1. original_inv_and_sales_logs - Contains all original data of (date x hr x outlet x item x ptype x city x CATEGORY)\n", "        2. original_inv_and_sales_logs_avail - Contains data of (date x hr x outlet x item x ptype x city x CATEGORY) where availability > 0\n", "        3. assortment_sales_data - Contains data of assortment (item contri>7.5%) sales for [city x category] on (date x hr x outlet x item x ptype x city x CATEGORY)\n", "        4. full_city_cat_assortment_at_fe_hr -  All (date x hr x outlet x category x city x item) where num_items_fe_hr_cat = num_items_cat_city\n", "        5. filtered_df4_assortment - Contains 80%+ contributing items on [city x category] level based on full_city_cat_assortment_at_fe_hr (ie. top city assortment, where no. items at fe x cat x hr = no. of items in city x cat)\n", "        6. filtered_df5_assortment - Contains 80%+ contributing items on [city x category] level based on original_inv_and_sales_logs_avail.\n", "        7. hero_assortment - Contains (city x item x cat, hero_sku_flag = 1) of all items that contribute 80%+ sales on [city x cat]\n", "        8. inv_and_sales_hero_sku_flag - Contains data about (date x hr x outlet x cat x item x city) level, for all items (hero & non-hero)\n", "        9. inv_and_sales_hero_skus_only - Contains data about (date x hr x outlet x cat x item x city) level, for all hero_skus (hero_sku_flag = 1)\n", "        10. hero_assortment_size - Contains num_hero_skus_city_cat i.e. no. of items that contribute 80%+ sales on [city x cat]\n", "\n", "    Args:\n", "        inv_and_sales_logs_with_cat (pd.DataFrame)): (date x hr x outlet x item x ptype x city X CATEGORY) level input dataframe.\n", "        top_assortment_cutoff (int) - Default: 0.075, it is the threshold that item's sale contribution > 7.5% in a city x category.\n", "\n", "    \"\"\"\n", "\n", "    # original_inv_and_sales_logs = inv_and_sales_logs_with_cat.copy()\n", "\n", "    # Available lines\n", "    inv_and_sales_logs_avail = inv_and_sales_logs_with_cat[\n", "        inv_and_sales_logs_with_cat[\"cur_inv\"] > 0\n", "    ]\n", "    # original_inv_and_sales_logs_avail = inv_and_sales_logs_with_cat[\n", "    #     inv_and_sales_logs_with_cat[\"cur_inv\"] > 0\n", "    # ]\n", "\n", "    # Finding City Sales - Top Assortments\n", "    groupby_cols = [\"city_id\", \"item_id\", \"category\"]\n", "    city_sales_top_assortment, city_sales_top_assortment_size = city_sales_prep(\n", "        inv_and_sales_logs_avail, groupby_cols, top_assortment_cutoff\n", "    )\n", "\n", "    assortment_sales_data = inv_and_sales_logs_avail.merge(\n", "        city_sales_top_assortment[[\"city_id\", \"item_id\", \"per_contri\", \"cum_per_contri\"]],\n", "        on=[\"city_id\", \"item_id\"],\n", "        how=\"inner\",\n", "    )\n", "\n", "    num_assortments_hr_fe_cat = (\n", "        assortment_sales_data[[\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\", \"item_id\"]]\n", "        .groupby(by=[\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\"])\n", "        .nunique()\n", "        .reset_index()\n", "    )\n", "    num_assortments_hr_fe_cat = num_assortments_hr_fe_cat.rename(\n", "        columns={\"item_id\": \"num_items_hr_fe_cat\"}\n", "    )\n", "\n", "    num_assortments_hr_fe_cat = num_assortments_hr_fe_cat.merge(\n", "        city_sales_top_assortment_size, how=\"left\", on=[\"city_id\", \"category\"]\n", "    )\n", "\n", "    # Evaluating lines where data is present for all hours\n", "    num_assortments_hr_fe_cat[\"flag\"] = np.where(\n", "        num_assortments_hr_fe_cat[\"num_items_hr_fe_cat\"]\n", "        == num_assortments_hr_fe_cat[\"num_items_city_cat\"],\n", "        1,\n", "        0,\n", "    )\n", "\n", "    assortment_sales_data = assortment_sales_data.merge(\n", "        num_assortments_hr_fe_cat[[\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\", \"flag\"]],\n", "        on=[\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    full_city_cat_assortment_at_fe_hr = assortment_sales_data[assortment_sales_data[\"flag\"] == 1]\n", "\n", "    hero_assortment, hero_assortment_size = find_hero_skus(\n", "        full_city_cat_assortment_at_fe_hr, inv_and_sales_logs_avail, cum_per_contri_cutoff\n", "    )\n", "\n", "    inv_and_sales_hero_sku_flag = inv_and_sales_logs_with_cat.merge(\n", "        hero_assortment[[\"city_id\", \"item_id\", \"hero_sku_flag\"]],\n", "        on=[\"city_id\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    inv_and_sales_hero_skus_available = inv_and_sales_hero_sku_flag[\n", "        inv_and_sales_hero_sku_flag[\"hero_sku_flag\"] == 1\n", "    ]\n", "\n", "    # inv_and_sales_hero_skus_available = inv_and_sales_hero_skus_only.copy()\n", "\n", "    inv_and_sales_hero_skus_available[\"hero_sku_avail_hr_fe\"] = np.where(\n", "        inv_and_sales_hero_skus_available[\"cur_inv\"] > 0, 1, 0\n", "    )\n", "\n", "    inv_and_sales_hero_sku_flag = inv_and_sales_hero_sku_flag.fillna(0)\n", "\n", "    del (\n", "        city_sales_top_assortment,\n", "        city_sales_top_assortment_size,\n", "        num_assortments_hr_fe_cat,\n", "    )\n", "\n", "    return (\n", "        inv_and_sales_logs_with_cat,\n", "        inv_and_sales_logs_avail,\n", "        assortment_sales_data,\n", "        full_city_cat_assortment_at_fe_hr,\n", "        hero_assortment,\n", "        hero_assortment_size,\n", "        inv_and_sales_hero_sku_flag,\n", "        inv_and_sales_hero_skus_available,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "2a0d6d13-2dab-4b62-b8a4-72c09fb46ba7", "metadata": {}, "outputs": [], "source": ["def evaluate_hourly_store_level_proposed_quantity(hourly_orders4):\n", "    \"\"\"\n", "    Calculates the proposes sales, proposed cart pen using fall back methods on City/PAN India x category levels.\n", "    It calculates fall back on city/Pan India bases, when avail = 1. Subsequently, cart pen is calculated for city/Pan India x category.\n", "    We fall back on city x category if proposed cart pen data is available, if not, then we fall back on (Pan India x category) level .\n", "\n", "    We calculate total proposed sales as proposed_cart_pen * total_orders\n", "\n", "    Returns:\n", "        1. hourly_orders5 (pd.DataFrame): (date x hr x outlet x item x ptype x city X CATEGORY) level, with proposed sales.\n", "\n", "    Args:\n", "        hourly_orders4 (pd.DataFrame)): (date x hr x outlet x item x ptype x city X CATEGORY) level input dataframe.\n", "    \"\"\"\n", "\n", "    flag = 1\n", "    if flag == 0:\n", "        ### Using all Hours data for Fall Back\n", "        fall_back = l2_hourly_sales.dropna()\n", "\n", "        fall_back_city = (\n", "            fall_back[[\"date_\", \"hour_\", \"city_name\", \"total_sales\"]]\n", "            .groupby(by=[\"date_\", \"hour_\", \"city_name\"])\n", "            .mean()\n", "            .reset_index()\n", "        )\n", "        fall_back_city = fall_back_city.rename(columns={\"total_sales\": \"city_avg_sales\"})\n", "\n", "        fall_back_pan_india = (\n", "            fall_back[[\"date_\", \"hour_\", \"total_sales\"]]\n", "            .groupby(\n", "                by=[\n", "                    \"date_\",\n", "                    \"hour_\",\n", "                ]\n", "            )\n", "            .mean()\n", "            .reset_index()\n", "        )\n", "        fall_back_pan_india = fall_back_pan_india.rename(\n", "            columns={\"total_sales\": \"pan_india_avg_sales\"}\n", "        )\n", "\n", "        l2_hourly_sales = l2_hourly_sales.merge(\n", "            fall_back_city, on=[\"date_\", \"hour_\", \"city_name\"], how=\"left\"\n", "        )\n", "        l2_hourly_sales = l2_hourly_sales.merge(\n", "            fall_back_pan_india, on=[\"date_\", \"hour_\"], how=\"left\"\n", "        )\n", "\n", "    else:\n", "        ### Using Available Hours data for Fall Back\n", "        hourly_orders4_all_avail = hourly_orders4[hourly_orders4[\"avail\"] == 1]\n", "        # fall_back2 = fall_back2[fall_back2['avail_count'] > availability_threshold * fall_back2['max_avail_count']]\n", "\n", "        ## CITY FALL BACK\n", "        print(\"Computing city fallbacks..\")\n", "        fall_back_city = (\n", "            hourly_orders4_all_avail[\n", "                [\n", "                    \"date_\",\n", "                    \"hour_\",\n", "                    \"city_id\",\n", "                    \"cluster_name\",\n", "                    \"sale_qty\",\n", "                    \"category\",\n", "                    \"total_orders\",\n", "                ]\n", "            ]\n", "            .groupby(by=[\"date_\", \"hour_\", \"city_id\", \"cluster_name\", \"category\"])\n", "            .agg({\"sale_qty\": \"sum\", \"total_orders\": \"sum\"})\n", "            .reset_index()\n", "        )\n", "        fall_back_city = fall_back_city.rename(columns={\"sale_qty\": \"city_sales\"})\n", "        fall_back_city = fall_back_city.rename(columns={\"total_orders\": \"city_orders\"})\n", "\n", "        ## CLUSTER FALL BACK\n", "        print(\"Computing cluster fallbacks..\")\n", "        fall_back_cluster = (\n", "            hourly_orders4_all_avail[\n", "                [\n", "                    \"date_\",\n", "                    \"hour_\",\n", "                    \"city_id\",\n", "                    \"cluster_name\",\n", "                    \"sale_qty\",\n", "                    \"category\",\n", "                    \"total_orders\",\n", "                ]\n", "            ]\n", "            .groupby(by=[\"date_\", \"hour_\", \"cluster_name\", \"category\"])\n", "            .agg({\"sale_qty\": \"sum\", \"total_orders\": \"sum\"})\n", "            .reset_index()\n", "        )\n", "        fall_back_cluster = fall_back_cluster.rename(columns={\"sale_qty\": \"cluster_sales\"})\n", "        fall_back_cluster = fall_back_cluster.rename(columns={\"total_orders\": \"cluster_orders\"})\n", "\n", "        ## PAN INDIA FALL BACK\n", "        print(\"Computing Pan India fallbacks..\")\n", "        fall_back_pan_india = (\n", "            hourly_orders4_all_avail[\n", "                [\n", "                    \"date_\",\n", "                    \"hour_\",\n", "                    \"city_id\",\n", "                    \"cluster_name\",\n", "                    \"sale_qty\",\n", "                    \"category\",\n", "                    \"total_orders\",\n", "                ]\n", "            ]\n", "            .groupby(by=[\"date_\", \"hour_\", \"category\"])\n", "            .agg({\"sale_qty\": \"sum\", \"total_orders\": \"sum\"})\n", "            .reset_index()\n", "        )\n", "        fall_back_pan_india = fall_back_pan_india.rename(columns={\"sale_qty\": \"pan_india_sales\"})\n", "        fall_back_pan_india = fall_back_pan_india.rename(\n", "            columns={\"total_orders\": \"pan_india_orders\"}\n", "        )\n", "\n", "        hourly_orders5 = hourly_orders4.merge(\n", "            fall_back_city, on=[\"date_\", \"hour_\", \"city_id\", \"cluster_name\", \"category\"], how=\"left\"\n", "        )\n", "\n", "        hourly_orders5 = hourly_orders5.merge(\n", "            fall_back_cluster, on=[\"date_\", \"hour_\", \"cluster_name\", \"category\"], how=\"left\"\n", "        )\n", "\n", "        hourly_orders5 = hourly_orders5.merge(\n", "            fall_back_pan_india, on=[\"date_\", \"hour_\", \"category\"], how=\"left\"\n", "        )\n", "\n", "    hourly_orders5[\"cart_pen_city\"] = hourly_orders5[\"city_sales\"] / hourly_orders5[\"city_orders\"]\n", "\n", "    hourly_orders5[\"cart_pen_cluster\"] = (\n", "        hourly_orders5[\"cluster_sales\"] / hourly_orders5[\"cluster_orders\"]\n", "    )\n", "\n", "    hourly_orders5[\"cart_pen_pan\"] = (\n", "        hourly_orders5[\"pan_india_sales\"] / hourly_orders5[\"pan_india_orders\"]\n", "    )\n", "\n", "    city_orders_threshold_for_cart_pen = 100\n", "    cluster_orders_threshold_for_cart_pen = 150\n", "    pan_india_orders_threshold_for_cart_pen = 1000\n", "\n", "    hourly_orders5[\"fallback_taken\"] = \"No Fallbacks\"\n", "\n", "    # City imputation\n", "    hourly_orders5[\"final_cart_pen\"] = np.where(\n", "        (hourly_orders5[\"avail\"] == 0)\n", "        & (hourly_orders5[\"cart_pen_city\"].notna())\n", "        & (hourly_orders5[\"city_orders\"] >= city_orders_threshold_for_cart_pen),\n", "        hourly_orders5[\"cart_pen_city\"],\n", "        hourly_orders5[\"cart_pen\"],\n", "    )\n", "    hourly_orders5[\"fallback_taken\"] = np.where(\n", "        (hourly_orders5[\"avail\"] == 0)\n", "        & (hourly_orders5[\"cart_pen_city\"].notna())\n", "        & (hourly_orders5[\"city_orders\"] >= city_orders_threshold_for_cart_pen),\n", "        \"City\",\n", "        hourly_orders5[\"fallback_taken\"],\n", "    )\n", "\n", "    # Cluster imputation\n", "    hourly_orders5[\"final_cart_pen\"] = np.where(\n", "        (hourly_orders5[\"avail\"] == 0)\n", "        & (hourly_orders5[\"cart_pen_city\"].isna())\n", "        & (hourly_orders5[\"cart_pen_cluster\"].notna())\n", "        & (hourly_orders5[\"cluster_orders\"] >= cluster_orders_threshold_for_cart_pen),\n", "        hourly_orders5[\"cart_pen_cluster\"],\n", "        hourly_orders5[\"final_cart_pen\"],\n", "    )\n", "\n", "    hourly_orders5[\"fallback_taken\"] = np.where(\n", "        (hourly_orders5[\"avail\"] == 0)\n", "        & (hourly_orders5[\"cart_pen_city\"].isna())\n", "        & (hourly_orders5[\"cart_pen_cluster\"].notna())\n", "        & (hourly_orders5[\"cluster_orders\"] >= cluster_orders_threshold_for_cart_pen),\n", "        \"Cluster\",\n", "        hourly_orders5[\"fallback_taken\"],\n", "    )\n", "\n", "    # Pan India imputation\n", "    hourly_orders5[\"final_cart_pen\"] = np.where(\n", "        (hourly_orders5[\"cart_pen_city\"].isna())\n", "        & (hourly_orders5[\"cart_pen_cluster\"].isna())\n", "        & (hourly_orders5[\"avail\"] == 0)\n", "        & (hourly_orders5[\"cart_pen_pan\"].notna())\n", "        & (hourly_orders5[\"pan_india_orders\"] >= pan_india_orders_threshold_for_cart_pen),\n", "        hourly_orders5[\"cart_pen_pan\"],\n", "        hourly_orders5[\"final_cart_pen\"],\n", "    )\n", "\n", "    hourly_orders5[\"fallback_taken\"] = np.where(\n", "        (hourly_orders5[\"cart_pen_city\"].isna())\n", "        & (hourly_orders5[\"cart_pen_cluster\"].isna())\n", "        & (hourly_orders5[\"avail\"] == 0)\n", "        & (hourly_orders5[\"cart_pen_pan\"].notna())\n", "        & (hourly_orders5[\"pan_india_orders\"] >= pan_india_orders_threshold_for_cart_pen),\n", "        \"Pan India\",\n", "        hourly_orders5[\"fallback_taken\"],\n", "    )\n", "\n", "    print(\"Finished city x cluster x Pan India imputations of cart pen.\")\n", "\n", "    hourly_orders5[\"proposed_cart_pen\"] = hourly_orders5[[\"final_cart_pen\", \"cart_pen\"]].max(axis=1)\n", "\n", "    hourly_orders5[\"fallback_taken\"] = np.where(\n", "        (hourly_orders5[\"proposed_cart_pen\"] == hourly_orders5[\"cart_pen\"])\n", "        & (hourly_orders5[\"avail\"] == 0),\n", "        \"None - Higher cart_pen than fallback\",\n", "        hourly_orders5[\"fallback_taken\"],\n", "    )\n", "\n", "    hourly_orders5[\"proposed_sales\"] = (\n", "        hourly_orders5[\"proposed_cart_pen\"] * hourly_orders5[\"total_orders\"]\n", "    )\n", "\n", "    del (fall_back_city, fall_back_pan_india, hourly_orders4_all_avail, hourly_orders4)\n", "\n", "    return hourly_orders5"]}, {"cell_type": "code", "execution_count": null, "id": "80e4fbce-28ec-4956-9448-b24134c8526e", "metadata": {}, "outputs": [], "source": ["def fallback_nan_pan_india(df_empty, df_full):\n", "\n", "    # Initialize an empty list to store the results for each row in df1\n", "\n", "    average_orders_lst = []\n", "    average_sales_lst = []\n", "\n", "    filtered_df = pd.DataFrame()\n", "    sales_series = pd.Series()\n", "    order_series = pd.Series()\n", "\n", "    # Iterate through each row of df1\n", "\n", "    # print(df_empty.shape)\n", "    for idx, row in df_empty.iterrows():\n", "\n", "        if row[\"hour_\"] == 0:\n", "            one_hr_before = 23\n", "            one_dt_before = (pd.to_datetime(row[\"date_\"]) - pd.DateOffset(days=1)).strftime(\n", "                \"%Y-%m-%d\"\n", "            )\n", "            one_hr_after = 1\n", "            one_dt_after = row[\"date_\"]\n", "            # Condition 1: colb in df2 is 1 + colb in df1\n", "            condition1 = df_full[\"hour_\"] == one_hr_after\n", "\n", "            condition2 = df_full[\"hour_\"] == one_hr_before\n", "\n", "            filtered_df = df_full[\n", "                ((df_full[\"date_\"] == one_dt_before) | (df_full[\"date_\"] == one_dt_after))\n", "                & (df_full[\"category\"] == row[\"category\"])\n", "                & (condition1 | condition2)\n", "            ]\n", "        elif row[\"hour_\"] == 23:\n", "            one_hr_before = 22\n", "            one_dt_before = row[\"date_\"]\n", "            one_hr_after = 0\n", "            one_dt_after = (pd.to_datetime(row[\"date_\"]) + pd.DateOffset(days=1)).strftime(\n", "                \"%Y-%m-%d\"\n", "            )\n", "            # Condition 1: colb in df2 is 1 + colb in df1\n", "            condition1 = df_full[\"hour_\"] == one_hr_after\n", "\n", "            # Condition 2: colb in df2 is colb in df1 - 1\n", "\n", "            condition2 = df_full[\"hour_\"] == one_hr_before\n", "\n", "            filtered_df = df_full[\n", "                ((df_full[\"date_\"] == one_dt_before) | (df_full[\"date_\"] == one_dt_after))\n", "                & (df_full[\"category\"] == row[\"category\"])\n", "                & (condition1 | condition2)\n", "            ]\n", "\n", "        else:\n", "            one_hr_before = row[\"hour_\"] - 1\n", "            same_dt = row[\"date_\"]\n", "            one_hr_after = row[\"hour_\"] + 1\n", "\n", "            condition1 = df_full[\"hour_\"] == one_hr_after\n", "\n", "            condition2 = df_full[\"hour_\"] == one_hr_before\n", "\n", "            filtered_df = df_full[\n", "                ((df_full[\"date_\"] == same_dt))\n", "                & (df_full[\"category\"] == row[\"category\"])\n", "                & (condition1 | condition2)\n", "            ]\n", "\n", "        # Calculate the average of column 'd' in the filtered df2\n", "        sales_series = filtered_df[filtered_df[\"pan_india_sales\"].notna()][\"pan_india_sales\"]\n", "        order_series = filtered_df[filtered_df[\"pan_india_orders\"].notna()][\"pan_india_orders\"]\n", "\n", "        if len(sales_series) == 0 and len(order_series) == 0:\n", "            average_sales = 0\n", "            average_orders = 0\n", "        else:\n", "            average_sales = sales_series.mean()\n", "            average_orders = order_series.mean()\n", "\n", "        # Append the result to the averages list\n", "        average_sales_lst.append(average_sales)\n", "        average_orders_lst.append(average_orders)\n", "\n", "        # Add the calculated averages as a new column in df1\n", "    df_empty[\"pan_india_sales\"] = average_sales_lst\n", "    df_empty[\"pan_india_orders\"] = average_orders_lst\n", "\n", "    del (filtered_df, sales_series, order_series)\n", "\n", "    return df_empty\n", "\n", "\n", "def impute_pan_india_sales(hourly_orders5):\n", "\n", "    df_empty = (\n", "        hourly_orders5[hourly_orders5[\"pan_india_sales\"].isna()][[\"date_\", \"hour_\", \"category\"]]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    df_full = (\n", "        hourly_orders5[hourly_orders5[\"pan_india_sales\"].notna()][\n", "            [\"date_\", \"hour_\", \"category\", \"pan_india_sales\", \"pan_india_orders\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .reset_index(drop=True)\n", "    )\n", "    df_filled = fallback_nan_pan_india(df_empty, df_full)\n", "\n", "    # # Merge df2 with df1 on columns 'a' and 'b' to get the corresponding 'c' values from df1\n", "    df2 = pd.merge(\n", "        hourly_orders5,\n", "        df_filled,\n", "        on=[\"date_\", \"hour_\", \"category\"],\n", "        how=\"left\",\n", "        suffixes=(\"\", \"_df1\"),\n", "    )\n", "\n", "    hourly_orders5[\"pan_india_sales\"] = df2[\"pan_india_sales\"].combine_first(\n", "        df2[\"pan_india_sales_df1\"]\n", "    )\n", "    hourly_orders5[\"pan_india_orders\"] = df2[\"pan_india_orders\"].combine_first(\n", "        df2[\"pan_india_orders_df1\"]\n", "    )\n", "\n", "    del (df_empty, df_full, df2)\n", "\n", "    return hourly_orders5"]}, {"cell_type": "code", "execution_count": null, "id": "02f54b08-c58c-4543-b96e-b85e91d0d4a5", "metadata": {}, "outputs": [], "source": ["def hourly_dod_orders(\n", "    start_date,\n", "    end_date,\n", "    # inv_and_sales_hero_skus_only,\n", "    inv_and_sales_hero_skus_available,\n", "    inv_and_sales_logs_with_cat,\n", "    hero_assortment_size,\n", "    hero_sku_avail_cutoff,\n", "    batch_categories_list,\n", "):\n", "    \"\"\"\n", "    Merges hourly order data at date x hr x outlet x total_orders at outlet with inventory_sales data.\n", "    Returns data at (date x hr x outlet x item level) and marks availability based on substitutability with hero sku.\n", "\n", "    We calculate total proposed sales as proposed_cart_pen * total_orders\n", "\n", "    Returns:\n", "        1. proposed_sales_fe_hr_item (pd.DataFrame): (date x hr x outlet x item x ptype x city X CATEGORY) level, with proposed sales.\n", "\n", "    Args:\n", "        start_date (str): start_date\n", "        end_date (str): end_date\n", "        inv_and_sales_hero_skus_only (pd.DataFrame): Contains data about (date x hr x outlet x cat x item x city) level, for all hero_skus (hero_sku_flag = 1)\n", "        original_inv_and_sales_logs (pd.DataFrame): Contains all original data of (date x hr x outlet x item x ptype x city x CATEGORY)\n", "        hero_assortment_size (pd.DataFrame): Contains num_hero_skus_city_cat i.e. no. of items that contribute 80%+ sales on [city x cat]\n", "        hero_sku_avail_cutoff (float): Availability cutoff for substitutability of hero_sku at cat x fe x hr with city x cat.\n", "        (i.e. we mark availability as 1 if the outlet has 70% of hero sku's of city x cat at hr x fe x cat)\n", "\n", "    \"\"\"\n", "    end_date = (pd.to_datetime(end_date) + pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "\n", "    hourly_orders_raw = fetch_hourly_dod_orders(start_date, end_date)\n", "    hourly_orders = hourly_orders_raw.fillna(0)\n", "\n", "    df = pd.DataFrame(\n", "        list(\n", "            product(\n", "                hourly_orders[\"outlet_id\"].unique(),\n", "                hourly_orders[\"date_\"].unique(),\n", "                hourly_orders[\"hour_\"].unique(),\n", "                batch_categories_list,\n", "            )\n", "        ),\n", "        columns=[\"outlet_id\", \"date_\", \"hour_\", \"category\"],\n", "    )\n", "\n", "    hourly_orders1 = df.merge(hourly_orders, how=\"left\", on=[\"date_\", \"hour_\", \"outlet_id\"])\n", "\n", "    # Finding hero sku availability/count in 'date_','hour_','outlet_id','category'\n", "    # cat_hourly_avail = inv_and_sales_hero_skus_only[\n", "    #     [\"date_\", \"hour_\", \"outlet_id\", \"category\", \"hero_sku_flag\"]\n", "    # ].drop_duplicates()\n", "\n", "    # cat_hourly_availability = inv_and_sales_hero_skus_only[['date_','hour_','outlet_id','category','hero_sku_flag']].groupby(['date_','hour_','outlet_id','category']).sum().reset_index()\n", "    cat_hourly_availability = (\n", "        inv_and_sales_hero_skus_available[\n", "            [\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\", \"hero_sku_avail_hr_fe\"]\n", "        ]\n", "        .groupby([\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\"])\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "\n", "    cat_hourly_availability = cat_hourly_availability.rename(\n", "        columns={\"hero_sku_avail_hr_fe\": \"num_hero_sku_hr_fe_cat\"}\n", "    )\n", "\n", "    # Adding Sales Quantity\n", "    cat_hourly_sales = (\n", "        inv_and_sales_logs_with_cat[\n", "            [\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\", \"cur_inv\", \"sale_qty\"]\n", "        ]\n", "        .groupby(by=[\"date_\", \"hour_\", \"outlet_id\", \"city_id\", \"category\"])\n", "        .sum()\n", "        .reset_index()\n", "    )\n", "\n", "    hourly_orders1 = hourly_orders1.merge(\n", "        cat_hourly_sales, how=\"left\", on=[\"date_\", \"hour_\", \"outlet_id\", \"category\"]\n", "    )\n", "\n", "    hourly_orders1 = hourly_orders1.merge(\n", "        cat_hourly_availability, how=\"left\", on=[\"date_\", \"hour_\", \"outlet_id\", \"category\"]\n", "    )\n", "\n", "    hourly_orders1 = hourly_orders1.fillna(0)\n", "\n", "    # Adding Category Availability Flag\n", "    # This will be item x outlet x hr x cat wise.. sales of item at fe, hr divided by total orders at that fe, hr.\n", "    hourly_orders1[\"cart_pen\"] = hourly_orders1[\"sale_qty\"] / hourly_orders1[\"total_orders\"]\n", "\n", "    city_mapping = fetch_outlet_city_mapping()\n", "\n", "    # inv_sales_avail_hourly_orders = inv_sales_avail_hourly_orders.merge(city_mapping, on = ['outlet_id'], how = 'inner')\n", "    hourly_orders1 = hourly_orders1.merge(city_mapping, on=[\"outlet_id\"], how=\"left\")\n", "\n", "    hourly_orders1 = hourly_orders1.fillna(0)\n", "\n", "    hero_assortment_size = (\n", "        hero_assortment.groupby(by=[\"city_id\", \"category\"])\n", "        .agg({\"item_id\": \"nunique\", \"hero_sku_flag\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "    hero_assortment_size = hero_assortment_size.rename(\n", "        columns={\"hero_sku_flag\": \"num_hero_skus_city_cat\"}\n", "    )\n", "\n", "    hourly_orders4 = hourly_orders1.merge(\n", "        hero_assortment_size[[\"city_id\", \"category\", \"num_hero_skus_city_cat\"]],\n", "        how=\"left\",\n", "        on=[\"city_id\", \"category\"],\n", "    )\n", "    # As there is no sale in places where city x category have no sale, hence the num_hero_skus_city_cat for that city cat for that period would be 0.\n", "\n", "    # hourly_orders4[\"avail\"] = 1  # Initialize with default value\n", "\n", "    # hourly_orders4[\"avail\"] = hourly_orders4.apply(\n", "    #     case_logic_for_availability, hero_sku_avail_cutoff=hero_sku_avail_cutoff, axis=1\n", "    # )\n", "\n", "    print(\"Calculating the availability based on hero skus using vectorized method.\")\n", "    hourly_orders4[\"avail\"] = np.where(\n", "        (\n", "            (hourly_orders4[\"num_hero_skus_city_cat\"] >= 1)\n", "            & (\n", "                (1.00 * hourly_orders4[\"num_hero_sku_hr_fe_cat\"])\n", "                / hourly_orders4[\"num_hero_skus_city_cat\"]\n", "                > hero_sku_avail_cutoff\n", "            )\n", "        ),\n", "        1,\n", "        0,\n", "    )\n", "\n", "    hourly_orders4 = hourly_orders4.fillna(0)\n", "\n", "    df_city_clusters = fetch_cluster_mapping()\n", "    hourly_orders4 = hourly_orders4.merge(\n", "        df_city_clusters[[\"cluster_name\", \"city_id\"]], on=\"city_id\", how=\"left\"\n", "    )\n", "\n", "    # Creating final proposed quantity dataframe\n", "    proposed_sales_fe_hr_item = evaluate_hourly_store_level_proposed_quantity(hourly_orders4)\n", "\n", "    proposed_sales_fe_hr_item = impute_pan_india_sales(proposed_sales_fe_hr_item)\n", "\n", "    del (\n", "        hourly_orders_raw,\n", "        hourly_orders,\n", "        hourly_orders1,\n", "        df,\n", "        cat_hourly_availability,\n", "        cat_hourly_sales,\n", "        hourly_orders4,\n", "        hero_assortment_size,\n", "    )\n", "\n", "    return proposed_sales_fe_hr_item  # , inv_sales_avail_hourly_orders, inv_sales_hourly_orders"]}, {"cell_type": "code", "execution_count": null, "id": "35bcc986-5a5a-43a3-8290-d76dc25a88f5", "metadata": {}, "outputs": [], "source": ["def check_db_existence(groups_to_check, festival, run_id):\n", "\n", "    sql_categories_in_trino = f\"\"\"\n", "    select \n", "        distinct category\n", "    from \n", "        supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "    where \n", "        festival = '{festival}' \n", "        and run_id = {run_id}\n", "    \"\"\"\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    category_df = pd.read_sql(sql_categories_in_trino, con=CON_TRINO)\n", "    if len(category_df) > 0:\n", "        categories_present = set(category_df[\"category\"].unique())\n", "    else:\n", "        categories_present = set()\n", "    item_groups_to_process = list(set(groups_to_check) - set(categories_present))\n", "    return item_groups_to_process"]}, {"cell_type": "markdown", "id": "f874c76e-9bb6-4fd9-9647-853d6ee7a080", "metadata": {}, "source": ["## Reading Groups based on Item Ids"]}, {"cell_type": "code", "execution_count": null, "id": "be6a543f-5f9c-48a7-963b-435deef08f97", "metadata": {}, "outputs": [], "source": ["df_group_items = pb.from_sheets(\n", "    \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"grouped_ptypes\", clear_cache=True\n", ")\n", "\n", "\n", "df_group_items[\"flag\"] = df_group_items[\"flag\"].astype(int)\n", "\n", "df_group_items[\"flag_items\"] = df_group_items[\"flag_items\"].fillna(0)\n", "df_group_items[\"flag_items\"] = df_group_items[\"flag_items\"].astype(int)\n", "\n", "df_group_items[\"ptype\"] = df_group_items[\"ptype\"].fillna(\"dummy\")\n", "df_group_items = df_group_items[(df_group_items[\"flag\"] == 1) & (df_group_items[\"flag_items\"] == 1)]\n", "df_group_items[\"item_id\"] = df_group_items[\"item_id\"].astype(int)"]}, {"cell_type": "markdown", "id": "37173e24-90ff-46cd-8d8d-ce87f78e9b92", "metadata": {}, "source": ["# LOOP"]}, {"cell_type": "code", "execution_count": null, "id": "57a5512c-6922-4c93-9c91-d29f03f8459c", "metadata": {"tags": []}, "outputs": [], "source": ["chunk_size = 2\n", "\n", "\n", "for festival, festival_dict in dict_festival_ptypes.items():\n", "\n", "    festival_name = festival\n", "    run_id = dict_festival_run_id[festival]\n", "    print(f\"Running for {festival}\")\n", "\n", "    ##### Grouping Based on Items is Handled in this block ######\n", "    df_group_items_festival = df_group_items[(df_group_items[\"festival\"] == festival)]\n", "\n", "    ## Check if group is already existing in database..corresponding to the festival, and run_id\n", "    if len(df_group_items_festival) > 0:\n", "        print(\"There are item groups present for the festival\")\n", "        groups_to_check = list(df_group_items_festival[\"group\"].unique())\n", "        item_groups_to_process = check_db_existence(groups_to_check, festival_name, run_id)\n", "        df_group_items_festival = df_group_items_festival[\n", "            df_group_items_festival[\"group\"].isin(item_groups_to_process)\n", "        ].reset_index(drop=True)\n", "    else:\n", "        print(\"There are no item groups present for the festival\")\n", "        item_groups_to_process = []\n", "\n", "    if festival_dict[\"category_list\"] == [] and len(item_groups_to_process) == 0:\n", "        print(f\"Categories already present for {festival} in Trino.\")\n", "        continue\n", "\n", "    df_festival_needed = input_df[input_df[\"Festival\"] == festival_name].reset_index(drop=True)\n", "\n", "    (\n", "        start_date,\n", "        end_date,\n", "        festival,\n", "        hero_sku_avail_cutoff,\n", "        city_order_cutoff,\n", "        top_assortment_cutoff,\n", "        cum_per_contri_cutoff,\n", "        usage_in_forecasting,\n", "        snapshot_dt_ist,\n", "    ) = festival_details(df_festival_needed)\n", "\n", "    # run_id = dict_festival_run_id[festival]\n", "\n", "    meta_data = f\"hero_sku_avail_cutoff : {hero_sku_avail_cutoff}, city_order_cutoff : {city_order_cutoff}, top_assortment_cutoff : {top_assortment_cutoff}, cum_per_contri_cutoff : {cum_per_contri_cutoff}, start_date: {start_date}, end_date: {end_date}\"\n", "\n", "    if usage_in_forecasting != 1:\n", "        continue\n", "\n", "    # Get the mapped ptypes\n", "    ptypes_festival = ptypes[festival_name]\n", "\n", "    if festival_name in groups:\n", "        groups_festival = groups[festival_name]\n", "    else:\n", "        groups_festival = {}\n", "\n", "    #     ##### Grouping Based on Items is Handled in this block ######\n", "    #     df_group_items_festival = df_group_items[(df_group_items[\"festival\"] == festival)]\n", "\n", "    #     ## Check if group is already existing in database..corresponding to the festival, and run_id\n", "    #     if len(df_group_items_festival)>0:\n", "    #         groups_to_check = list(df_group_items_festival['group'].unique())\n", "    #         allowed_groups = check_db_existence(groups_to_check, festival_name,  run_id)\n", "    #         df_group_items_festival = df_group_items_festival[df_group_items_festival['group'].isin(allowed_groups)].reset_index(drop = True)\n", "\n", "    # df_group_items_festival has any items to process\n", "    if len(df_group_items_festival) > 0:\n", "        df_group_items_festival = (\n", "            df_group_items_festival[[\"group\", \"ptype\", \"item_id\"]]\n", "            .drop_duplicates()\n", "            .reset_index(drop=True)\n", "        )\n", "        df_group_items_festival = df_group_items_festival.rename(\n", "            columns={\"ptype\": \"product_type\", \"group\": \"category\"}\n", "        )\n", "        categories_group_item = (\n", "            df_group_items_festival[\"category\"].unique().tolist()\n", "            if df_group_items_festival.shape[0] > 0\n", "            else []\n", "        )\n", "\n", "        groups_festival = {\n", "            key: value for key, value in groups_festival.items() if key not in categories_group_item\n", "        }\n", "\n", "    else:\n", "        df_group_items_festival = pd.DataFrame()\n", "\n", "    # Create category, ptype, item mapping.\n", "    if len(ptypes_festival) == 0 and len(groups_festival) == 0:\n", "        cat_ptype_item_mapping = pd.DataFrame()\n", "    else:\n", "        cat_ptype_item_mapping = group_ptypes_category_items(ptypes_festival, groups_festival)\n", "\n", "        # Filter df to contains only those categories that have not been processed yet.\n", "        cat_ptype_item_mapping = cat_ptype_item_mapping[\n", "            cat_ptype_item_mapping[\"category\"].isin(festival_dict[\"category_list\"])\n", "        ].reset_index(drop=True)\n", "\n", "    cat_ptype_item_mapping = pd.concat(\n", "        [cat_ptype_item_mapping, df_group_items_festival], ignore_index=True\n", "    )\n", "    #######################################################\n", "\n", "    print(\n", "        \"Cat_Ptype_Item_Mapping: \",\n", "        cat_ptype_item_mapping[[\"category\", \"item_id\"]]\n", "        .groupby(\"category\")\n", "        .agg({\"item_id\": \"nunique\"})\n", "        .reset_index(),\n", "    )\n", "    print()\n", "\n", "    # Get list of categories - [CatA, CatZ, CatB, CatY...] (top, last, 2nd, 2nd last...) by size..\n", "    category_ordered = category_size_batch(cat_ptype_item_mapping)\n", "\n", "    #     print(\"category_ordered: \", category_ordered)\n", "    #     print()\n", "\n", "    #     no_chunks = math.ceil((len(category_ordered) / chunk_size))\n", "\n", "    #     total_data = []\n", "\n", "    no_chunks = len(category_ordered)\n", "\n", "    # for chunk_number, list_ptypes in enumerate(chunk_ptype_list(category_ordered, chunk_size)):\n", "    for chunk_number, list_ptypes in enumerate(category_ordered):\n", "\n", "        start_time_block_1 = time.time()\n", "\n", "        print(\"===============================================\")\n", "        print(f\"Computing for {chunk_number+1} of {no_chunks} chunks..\")\n", "\n", "        ## Remove any categories where no items found in the festive period (i.e. removing NaNs)\n", "        cat_ptype_item_mapping_subset = cat_ptype_item_mapping[\n", "            cat_ptype_item_mapping[\"category\"].isin(list_ptypes)\n", "        ].reset_index(drop=True)\n", "\n", "        cat_ptype_item_mapping_subset = cat_ptype_item_mapping_subset[\n", "            cat_ptype_item_mapping_subset[\"item_id\"].notna()\n", "        ]\n", "\n", "        cat_ptype_item_mapping_subset = cat_ptype_item_mapping_subset.drop_duplicates().reset_index(\n", "            drop=True\n", "        )\n", "\n", "        batch_item_tuple = tuple(cat_ptype_item_mapping_subset[\"item_id\"].unique())\n", "\n", "        if len(batch_item_tuple) == 0:\n", "            print(\"No items for this ptype present, hence skipping it..\")\n", "            continue\n", "\n", "        # Single Item present - fix to avoid DB Errors\n", "        elif len(batch_item_tuple) == 1:\n", "            fix_tuple = tuple([-999])\n", "            batch_item_tuple = batch_item_tuple + fix_tuple\n", "\n", "        ## Skip batches where no. of items > 350.\n", "        # elif len(batch_item_tuple) > 350:\n", "        #     print(\n", "        #         f\"Skipping this batch of categories = {list_ptypes} as number of items exceed 350!!!\"\n", "        #     )\n", "        #     continue\n", "\n", "        product_type_sql = tuple((set(list_ptypes)))\n", "\n", "        batch_categories = list_ptypes\n", "\n", "        # FETCH ITEM LEVEL AVAILABILITY from the tables\n", "\n", "        print(\n", "            f\"Fetching query for no.ptypes = {len(product_type_sql)}, start_date = {start_date}, end_date = {end_date}, no.items = {len(batch_item_tuple)}, categories = {list_ptypes}\"\n", "        )\n", "\n", "        inv_and_sales_logs_with_cat = fetch_item_level_availability(\n", "            start_date, end_date, cat_ptype_item_mapping_subset, product_type_sql, batch_item_tuple\n", "        )\n", "        gc.collect()\n", "\n", "        print(\"Query successfully executed\")\n", "\n", "        if inv_and_sales_logs_with_cat.shape[0] == 0:\n", "            print(\"No data present.. moving to next iteration..\")\n", "            continue\n", "\n", "        (\n", "            inv_and_sales_logs_with_cat,\n", "            inv_and_sales_logs_avail,\n", "            assortment_sales_data,\n", "            full_city_cat_assortment_at_fe_hr,\n", "            hero_assortment,\n", "            hero_assortment_size,\n", "            inv_and_sales_hero_sku_flag,\n", "            inv_and_sales_hero_skus_available,\n", "        ) = make_assortment_please(inv_and_sales_logs_with_cat)\n", "        gc.collect()\n", "\n", "        print(\"City Assortment data created\")\n", "\n", "        # TABLE 1\n", "\n", "        proposed_sales_fe_hr_item = hourly_dod_orders(\n", "            start_date,\n", "            end_date,\n", "            inv_and_sales_hero_skus_available,\n", "            inv_and_sales_logs_with_cat,\n", "            hero_assortment_size,\n", "            hero_sku_avail_cutoff,\n", "            batch_categories,\n", "        )\n", "        gc.collect()\n", "\n", "        df_city_clusters = fetch_cluster_mapping()\n", "        proposed_sales_fe_hr_item = proposed_sales_fe_hr_item.merge(\n", "            df_city_clusters[[\"city_name\", \"city_id\"]], on=\"city_id\", how=\"left\"\n", "        )\n", "\n", "        start_time_block_2 = time.time()\n", "\n", "        proposed_sales_fe_hr_item = proposed_sales_fe_hr_item.rename(\n", "            columns={\n", "                \"date_\": \"date\",\n", "                \"hour_\": \"hour\",\n", "                \"city_name\": \"city\",\n", "                \"sale_qty\": \"sale_qty_fe_hr\",\n", "                \"cur_inv\": \"cur_inv_fe_hr\",\n", "                \"total_orders\": \"total_orders_fe_hr\",\n", "                \"cart_pen\": \"cart_pen_fe_hr\",\n", "            }\n", "        )\n", "\n", "        proposed_sales_fe_hr_item[\"festival\"] = festival\n", "\n", "        proposed_sales_fe_hr_item[\"snapshot_dt_ist\"] = snapshot_dt_ist\n", "        proposed_sales_fe_hr_item[\"run_id\"] = run_id\n", "        proposed_sales_fe_hr_item[\"meta_data\"] = meta_data\n", "\n", "        column_order = [\n", "            \"date\",\n", "            \"hour\",\n", "            \"outlet_id\",\n", "            \"city\",\n", "            \"cluster_name\",\n", "            \"category\",\n", "            \"festival\",\n", "            \"proposed_sales\",\n", "            \"proposed_cart_pen\",\n", "            \"final_cart_pen\",\n", "            \"num_hero_sku_hr_fe_cat\",\n", "            \"num_hero_skus_city_cat\",\n", "            \"avail\",\n", "            \"city_sales\",\n", "            \"city_orders\",\n", "            \"cart_pen_city\",\n", "            \"pan_india_sales\",\n", "            \"pan_india_orders\",\n", "            \"cart_pen_pan\",\n", "            \"cart_pen_fe_hr\",\n", "            \"cart_pen_cluster\",\n", "            \"cluster_sales\",\n", "            \"cluster_orders\",\n", "            \"total_orders_fe_hr\",\n", "            \"cur_inv_fe_hr\",\n", "            \"sale_qty_fe_hr\",\n", "            \"fallback_taken\",\n", "            \"snapshot_dt_ist\",\n", "            \"run_id\",\n", "            \"meta_data\",\n", "        ]\n", "\n", "        proposed_sales_fe_hr_item = proposed_sales_fe_hr_item[column_order]\n", "\n", "        print(\"==================\")\n", "        print(\"Finished computing all 5 tables.\")\n", "        print(\"==================\")\n", "        print()\n", "\n", "        ### Convert all date strings into date time format ###\n", "        proposed_sales_fe_hr_item[\"date\"] = pd.to_datetime(\n", "            proposed_sales_fe_hr_item[\"date\"]\n", "        ).dt.date\n", "        proposed_sales_fe_hr_item[\"snapshot_dt_ist\"] = pd.to_datetime(\n", "            proposed_sales_fe_hr_item[\"snapshot_dt_ist\"]\n", "        ).dt.date\n", "\n", "        ## PUSHING TO TRINO...\n", "        start_time_block_3 = time.time()\n", "        try:\n", "\n", "            dict_df = {\"proposed_sales_fe_hr_item\": proposed_sales_fe_hr_item}\n", "\n", "            dictionary_schemas = {\"proposed_sales_fe_hr_item\": unavailability_main_schema}\n", "\n", "            push_to_trino_table(dict_df, festival, dictionary_schemas)\n", "            print(\"Successfully pushed all dataframes to table.\")\n", "\n", "            print(\n", "                f\"Pushed to Trino Table for {festival} and chunk_number of ptype list = {chunk_number+1}\"\n", "            )\n", "\n", "        except Exception as E:\n", "            msg = f\"Error in pushing data - {E}\"\n", "            print(msg)\n", "\n", "        end_time_block_3 = time.time()\n", "        duration_block_3_minutes = (\n", "            end_time_block_3 - start_time_block_3\n", "        ) / 60  # Convert seconds to minutes\n", "        print(\n", "            f\"---- Time taken to push data to Trino: {duration_block_3_minutes:.2f} minutes -----\"\n", "        )\n", "\n", "        del (\n", "            cat_ptype_item_mapping_subset,\n", "            inv_and_sales_logs_with_cat,\n", "            assortment_sales_data,\n", "            full_city_cat_assortment_at_fe_hr,\n", "            hero_assortment,\n", "            hero_assortment_size,\n", "            inv_and_sales_hero_sku_flag,\n", "            inv_and_sales_hero_skus_available,\n", "        )\n", "\n", "        gc.collect()\n", "\n", "        end_time_block_1 = time.time()\n", "        duration_block_1_minutes = (\n", "            end_time_block_1 - start_time_block_1\n", "        ) / 60  # Convert seconds to minutes\n", "        print(\n", "            f\"----- Time taken to process chunk no. {chunk_number+1}: {duration_block_1_minutes:.2f} minutes -----\"\n", "        )\n", "\n", "        print(\"===============================================\")"]}, {"cell_type": "markdown", "id": "ed92c858-c600-4e73-9a52-15328b46beb6", "metadata": {}, "source": ["# Aggregated Table Creation"]}, {"cell_type": "code", "execution_count": null, "id": "f3ffa844-b22e-4213-9bb3-e85922fb7086", "metadata": {}, "outputs": [], "source": ["sql_agg_category_date = \"\"\"\n", "with base_table as (\n", "  select\n", "    *\n", "  from\n", "    supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "  where\n", "    festival = '{festival_name}'\n", "    and meta_data = '{meta_data}'\n", "    and run_id = cast('{run_id}' as double)\n", ")\n", "\n", "\n", "select\n", "  date,\n", "  category,\n", "  festival,\n", "  sum(proposed_sales) as proposed_sales,\n", "  sum(sale_qty_fe_hr) as sale_qty_fe_hr,\n", "  snapshot_dt_ist,\n", "  run_id,\n", "  meta_data\n", "from\n", "  base_table\n", "group by\n", "  1,2,3,6,7,8\n", "\"\"\"\n", "\n", "sql_agg_category = \"\"\"\n", "with base_table as (\n", "  select\n", "    *\n", "  from\n", "    supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "  where\n", "    festival = '{festival_name}'\n", "    and meta_data = '{meta_data}'\n", "    and run_id = cast('{run_id}' as double)\n", ")\n", "\n", "\n", "select\n", "  category,\n", "  festival,\n", "  sum(sale_qty_fe_hr) as sale_qty_fe_hr,\n", "  sum(proposed_sales) as proposed_sales,\n", "  snapshot_dt_ist,\n", "  run_id,\n", "  meta_data\n", "from\n", "  base_table\n", "group by\n", "  1,2,5,6,7\n", "\"\"\"\n", "\n", "\n", "sql_agg_category_city = \"\"\"\n", "with base_table as (\n", "  select\n", "    *\n", "  from\n", "    supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "  where\n", "    festival = '{festival_name}'\n", "    and meta_data = '{meta_data}'\n", "    and run_id = cast('{run_id}' as double)\n", ")\n", "\n", "\n", "select\n", "  category,\n", "  city,\n", "  festival,\n", "  sum(sale_qty_fe_hr) as sale_qty_fe_hr,\n", "  sum(proposed_sales) as proposed_sales,\n", "  snapshot_dt_ist,\n", "  run_id,\n", "  meta_data\n", "from\n", "  base_table\n", "group by\n", "  1,2,3,6,7,8\n", "\"\"\"\n", "\n", "sql_run_id = \"\"\"\n", "select \n", "    max(run_id) as \"max_run_id\"\n", "from \n", "    supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "where \n", "    festival = '{festival_name}' and meta_data = '{meta_data}'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e664f998-d740-4541-9150-1dc0bb265e28", "metadata": {}, "outputs": [], "source": ["def push_to_aggregated_table(queries_details, festival_name, meta_data, run_id):\n", "\n", "    for query, schema in queries_details:\n", "        try:\n", "\n", "            msg = f\"Populating the table - {schema['table_name']},  for festival = {festival_name}\"\n", "            print(msg)\n", "\n", "            _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "            _trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "            pb.to_trino(\n", "                query.format(festival_name=festival_name, meta_data=meta_data, run_id=run_id),\n", "                **schema,\n", "            )\n", "\n", "            del _trino_con, _trino_conn\n", "            print(f\"Done pushing aggregated tables to Trino..\")\n", "            print()\n", "\n", "        except Exception as e:\n", "            msg = f\"Failed to push data to DB for = {festival_name}, got error = {e}\"\n", "            print(msg)\n", "            raise Exception(msg)\n", "            break\n", "\n", "    return"]}, {"cell_type": "code", "execution_count": null, "id": "52865a72-f336-4339-96bf-729bbc03b413", "metadata": {}, "outputs": [], "source": ["queries_details = [\n", "    (sql_agg_category_date, unavailability_cat_sales_details_ov_schema),\n", "    (sql_agg_category, unavailability_cat_sales_ov_schema),\n", "    (sql_agg_category_city, unavailability_cat_city_sales_ov_schema),\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4ab792f6-4189-4bef-8efc-6a5a80295a23", "metadata": {}, "outputs": [], "source": ["for idx in range(len(input_df)):\n", "\n", "    festival_name = input_df[\"Festival\"][idx]\n", "    usage_in_forecasting = input_df[\"usage_in_forecasting\"][idx]\n", "\n", "    if usage_in_forecasting != 1:\n", "        continue\n", "\n", "    start_date = input_df[\"start_date\"][idx]\n", "    end_date = input_df[\"end_date\"][idx]\n", "    hero_sku_avail_cutoff = input_df[\"hero_sku_avail_cutoff\"][idx]\n", "    city_order_cutoff = input_df[\"city_order_cutoff\"][idx]\n", "    top_assortment_cutoff = input_df[\"top_assortment_cutoff\"][idx]\n", "    cum_per_contri_cutoff = input_df[\"cum_per_contri_cutoff\"][idx]\n", "    meta_data = f\"hero_sku_avail_cutoff : {hero_sku_avail_cutoff}, city_order_cutoff : {city_order_cutoff}, top_assortment_cutoff : {top_assortment_cutoff}, cum_per_contri_cutoff : {cum_per_contri_cutoff}, start_date: {start_date}, end_date: {end_date}\"\n", "\n", "    print(f\"========= Aggregating for {festival_name} =========\")\n", "    print()\n", "\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    run_id_df = pd.read_sql(\n", "        sql_run_id.format(festival_name=festival_name, meta_data=meta_data), con=CON_TRINO\n", "    )\n", "    run_id = run_id_df[\"max_run_id\"].max()\n", "\n", "    push_to_aggregated_table(queries_details, festival_name, meta_data, run_id)\n", "    print(\"==================================== \")\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}