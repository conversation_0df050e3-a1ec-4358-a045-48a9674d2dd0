{"cells": [{"cell_type": "code", "execution_count": null, "id": "d68efcb7-349f-47dd-88fd-6104a2fa0b93", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "77a647bc-f311-4e53-9a40-5dfd1919b944", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "0814978c-f4c8-4829-8bad-fbf27925137b", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"Dark Store HOT Outlet ID\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"Dark Store Name\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"avg_orders\", \"type\": \"real\", \"description\": \"Avg Current Orders\"},\n", "    {\"name\": \"val\", \"type\": \"real\", \"description\": \"Store Ratio Value\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "5a5db7cf-4ae3-467a-8976-3b193cbba1b2", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_store_dist_fallback\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"product_type\",\n", "        \"city_name\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "570bb328-1492-44b0-a5c3-6db484f0948a", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'"]}, {"cell_type": "code", "execution_count": null, "id": "1f2db038-6127-421b-8b0d-eb54fb0ab4c2", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "68f1c508-aa37-457c-8786-480c60127fb9", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7cb83505-9c6e-4d70-ba98-5aad79695c48", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "606c8b38-a3db-4010-9b6f-f1dd6a8e0472", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e2a6b971-8608-439c-8983-7d31cb2970c8", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "d4aca4af-54e9-4d1b-8f26-8a1b9c5008a2", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[festive_date_data[\"flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "da835c5d-5717-4d5f-8f51-a1f4d8e83358", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9a79fac3-90f7-4614-a88b-ba90ec47abdc", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "7da89cc7-e2b1-4c4b-a82c-32ea433bde54", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "07679c12-3e88-40b8-9b05-1a9fbf180b32", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        bau_start_date = row[\"bau_start_date\"]\n", "        bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "\n", "        store_dist = f\"\"\"\n", "\n", "        with ptype_distribution_raw as (\n", "            select *\n", "            from supply_etls.keyword_ptype_distribution\n", "            where date_ between (date('{festival_start_date}') - interval'7'day) and (date('{festival_end_date}') + interval'7'day)\n", "        ),\n", "\n", "        ptype_distribution0 as (\n", "            select base.date_,\n", "                base.keyword,\n", "                base.product_type,\n", "                coalesce(devices,0) as devices,\n", "                coalesce(total_devices,0) as total_devices,\n", "                coalesce(sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following),0) as nearby_devices,\n", "                coalesce(sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following),0) as nearby_total_devices,\n", "                coalesce((1.00 * sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following)) / nullif((1.00 * sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following)),0),0) as percentage_attri\n", "\n", "            from (\n", "                    (select distinct date_ from ptype_distribution_raw) \n", "                        cross join \n", "                    (select distinct keyword, product_type from ptype_distribution_raw)\n", "                ) base\n", "            left join ptype_distribution_raw pdr on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "        ), \n", "\n", "        ptype_distribution1 as (\n", "            select date_,\n", "                keyword,\n", "                product_type,\n", "                devices,\n", "                total_devices,\n", "                nearby_devices,\n", "                nearby_total_devices,\n", "                percentage_attri,\n", "                rank() over (partition by date_, keyword order by percentage_attri desc) as key_rank\n", "            from ptype_distribution0\n", "\n", "        ),\n", "\n", "        ptype_distribution as (\n", "            select date_,\n", "                keyword,\n", "                product_type,\n", "                devices,\n", "                total_devices,\n", "                nearby_devices,\n", "                nearby_total_devices,\n", "                percentage_attri,\n", "                try(percentage_attri/nullif((sum(percentage_attri) over(partition by date_, keyword)),0)) as normalized_percent_attri,\n", "                key_rank\n", "            from ptype_distribution1\n", "            where (key_rank<=1 or percentage_attri > 0.05)\n", "        ),\n", "\n", "        keys_base as (\n", "            select distinct date_,\n", "                keyword,\n", "                outlet_id,\n", "                search_device_count\n", "            from supply_etls.merchant_dod_keyword_searches_v2\n", "            where date_ between date('{festival_start_date}') and date('{festival_end_date}')\n", "        ),\n", "        \n", "        keys_search as (\n", "            select \n", "                date_,\n", "                outlet_id,\n", "                sum(search_device_count) as search_device_count\n", "            from keys_base\n", "            group by 1,2\n", "        ),\n", "        \n", "        keys_search_min as (\n", "            select \n", "                outlet_id,\n", "                min(search_device_count) as search_device_count\n", "            from keys_search\n", "            group by 1\n", "            having min(search_device_count)<1000\n", "        ),\n", "\n", "        keys_raw as (\n", "            select \n", "                date_,\n", "                keyword,\n", "                outlet_id,\n", "                search_device_count\n", "            from keys_base\n", "            where outlet_id not in (select outlet_id from keys_search_min)\n", "        ),\n", "\n", "        atc_split as (\n", "            select date_,\n", "                product_type,\n", "                1.000000 * sum(search_atc_device_count) as search_atcs,\n", "                1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "                try((1.000000 * sum(search_atc_device_count))/((1.000000 * sum(search_atc_device_count)) + (1.000000 * sum(non_search_atc_device_count)))) as search_contri\n", "\n", "            from supply_etls.ptype_store_atc_channel_split_v2\n", "            where (date_ between date('{festival_start_date}') and date('{festival_end_date}'))\n", "            group by 1,2\n", "        ),\n", "\n", "        day_on_day_searches as (\n", "            select keys_raw.date_,\n", "                outlet_id,\n", "                coalesce(ptype_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "                search_contri as search_contri_atc,\n", "                sum(keys_raw.search_device_count) AS search_device_count,\n", "                sum(keys_raw.search_device_count * normalized_percent_attri) AS prob_adjusted_search_device_count\n", "            from keys_raw\n", "            left join ptype_distribution on keys_raw.date_ = ptype_distribution.date_ and keys_raw.keyword = ptype_distribution.keyword\n", "            left join atc_split on keys_raw.date_ = atc_split.date_ and ptype_distribution.product_type = atc_split.product_type\n", "            group by 1,2,3,4\n", "        ),\n", "\n", "        searches_final as (\n", "            select outlet_id,\n", "                product_type,\n", "                try(avg(1.00 * search_device_count/ (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_searches_old,\n", "                try(avg(1.00 * prob_adjusted_search_device_count/ (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_searches_adj_old\n", "            from day_on_day_searches\n", "            group by 1,2\n", "        ),\n", "\n", "        carts_old as (\n", "            select date(cart_checkout_ts_ist) as date_,\n", "                fsoid.outlet_id, \n", "                count(distinct order_id) as orders_old\n", "            from dwh.fact_sales_order_item_details fsoid\n", "            where order_current_status = 'DELIVERED'\n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "                and cart_checkout_ts_ist between (date('{festival_start_date}') - interval '15' day) and (date('{festival_start_date}') - interval '1' day)\n", "                and order_create_dt_ist between (date('{festival_start_date}') - interval '15' day) and (date('{festival_start_date}') - interval '1' day)\n", "            group by 1,2\n", "        ),\n", "\n", "        carts_final_old as (\n", "            select outlet_id, \n", "                avg(orders_old) as avg_orders_old \n", "            from carts_old \n", "            group by 1 \n", "            having  \n", "            avg(orders_old) > 500 \n", "        ),\n", "\n", "        --carts_new as (\n", "        --    select date(cart_checkout_ts_ist) as date_,\n", "        --        fsoid.outlet_id, \n", "        --        count(distinct order_id) as orders\n", "        --    from dwh.fact_sales_order_item_details fsoid\n", "        --    where order_current_status = 'DELIVERED'\n", "        --        and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "        --        and date(cart_checkout_ts_ist) between (date('{cpd_date}') - interval '14' day) and date('{cpd_date}')\n", "        --        and order_create_dt_ist between (date('{cpd_date}') - interval '14' day) and date('{cpd_date}')\n", "        --    group by 1,2\n", "        --)\n", "        --\n", "        --,\n", "        --\n", "        --carts_final_new as (\n", "        --    select outlet_id,\n", "        --        avg(orders) as avg_orders\n", "        --    from carts_new\n", "        --    group by 1\n", "        --),\n", "\n", "        max_available_date AS (\n", "            SELECT \n", "                MAX(\"date\") AS max_date\n", "            FROM logistics_data_etls.cart_projections\n", "        ),\n", "\n", "        fallback_date AS (\n", "            SELECT \n", "                CASE \n", "                    WHEN date('{sale_start_date}') > (SELECT max_date FROM max_available_date) \n", "                    THEN (SELECT max_date FROM max_available_date) \n", "                    ELSE date('{sale_start_date}')\n", "                END AS effective_date\n", "        ),\n", "\n", "        carts_new AS (\n", "            SELECT \n", "                \"date\" AS date_,\n", "                outletid AS outlet_id,\n", "                SUM(aov) AS aov,\n", "                SUM(carts) AS carts\n", "            FROM logistics_data_etls.cart_projections cp\n", "            INNER JOIN \n", "                (SELECT MAX(updated_on) AS ts\n", "                FROM logistics_data_etls.cart_projections\n", "                WHERE \"date\" BETWEEN \n", "                    (SELECT effective_date FROM fallback_date) - INTERVAL '15' DAY \n", "                    AND (SELECT effective_date FROM fallback_date) - INTERVAL '1' DAY\n", "                ) tst \n", "            ON cp.updated_on = tst.ts\n", "            WHERE \"date\" BETWEEN \n", "                (SELECT effective_date FROM fallback_date) - INTERVAL '15' DAY \n", "                AND (SELECT effective_date FROM fallback_date) - INTERVAL '1' DAY\n", "            GROUP BY 1, 2\n", "        ),\n", "\n", "        --carts_new as (\n", "        --select \"date\" as date_,\n", "        --            outletid as outlet_id,\n", "        --            sum(aov) as aov,\n", "        --            sum(carts) as carts\n", "        --        from logistics_data_etls.cart_projections cp\n", "        --        inner join \n", "        --            (select max(updated_on) as ts\n", "        --            from logistics_data_etls.cart_projections) tst on cp.updated_on = tst.ts\n", "        --        where \"date\" between (date('{sale_start_date}') - interval '15' day) and (date('{sale_start_date}') - interval '1' day)\n", "        --        group by 1,2\n", "        --)\n", "        --,\n", "        \n", "        carts_final_new as (\n", "            select outlet_id,\n", "                avg(carts) as avg_orders\n", "            from carts_new\n", "            group by 1\n", "        ),\n", "\n", "        cross_table as (\n", "            select distinct cfn.outlet_id, t1.product_type from carts_final_new cfn\n", "            cross join (select distinct product_type from searches_final) t1\n", "        ),\n", "\n", "        cluster as (\n", "            select \n", "                cluster_id, \n", "                cluster_name\n", "            from rpc.ams_cluster \n", "            where cluster_id > 15000 and lake_active_record\n", "        ), \n", "\n", "        cluster_city as (\n", "            select \n", "                cluster_name, \n", "                cluster.cluster_id, \n", "                city_id\n", "            from rpc.ams_city_cluster_mapping accm\n", "            inner join cluster on cluster.cluster_id = accm.cluster_id and lake_active_record and active\n", "        ),\n", "\n", "        final_base as (   \n", "            select \n", "                ct.outlet_id,\n", "                co.name as outlet_name,\n", "                ct.product_type,\n", "                'PAN' as nation_name,\n", "                cc.cluster_id,\n", "                cc.cluster_name,\n", "                cl.id as city_id,\n", "                cl.name as city_name,\n", "\n", "                cfn.avg_orders,\n", "\n", "                cfo.avg_orders_old,\n", "                sf.avg_searches_old,\n", "                try((sf.avg_searches_adj_old / nullif(cfo.avg_orders_old,0))) as avg_searches_pen_old,\n", "\n", "                avg(cfo.avg_orders_old) over (partition by cl.name, ct.product_type) as avg_city_orders_old,\n", "                avg(sf.avg_searches_adj_old) over (partition by cl.name, ct.product_type) as avg_city_searches_old,\n", "                try(avg(sf.avg_searches_adj_old / nullif(cfo.avg_orders_old,0)) over (partition by cl.name, ct.product_type)) as avg_city_searches_pen_old,\n", "\n", "                avg(cfo.avg_orders_old) over (partition by cc.cluster_id, ct.product_type) as avg_cluster_orders_old,\n", "                avg(sf.avg_searches_adj_old) over (partition by cc.cluster_id, ct.product_type) as avg_cluster_searches_old,\n", "                try(avg(sf.avg_searches_adj_old / nullif(cfo.avg_orders_old,0)) over (partition by cc.cluster_id, ct.product_type)) as avg_cluster_searches_pen_old,\n", "\n", "                avg(cfo.avg_orders_old) over (partition by ct.product_type) as avg_nation_orders_old,\n", "                avg(sf.avg_searches_adj_old) over (partition by ct.product_type) as avg_nation_searches_old,\n", "                try(avg(sf.avg_searches_adj_old / nullif(cfo.avg_orders_old,0)) over (partition by ct.product_type)) as avg_nation_searches_pen_old\n", "\n", "\n", "            from cross_table ct\n", "            left join carts_final_new cfn on ct.outlet_id = cfn.outlet_id\n", "            left join carts_final_old cfo on ct.outlet_id = cfo.outlet_id\n", "            left join searches_final sf on ct.outlet_id = sf.outlet_id and ct.product_type = sf.product_type\n", "\n", "            left join retail.console_outlet co on co.id = ct.outlet_id\n", "            left join po.physical_facility_outlet_mapping pfom on pfom.outlet_id  = ct.outlet_id\n", "            left join retail.console_location cl on cl.id = pfom.city_id\n", "            left join cluster_city cc on cl.id = cc.city_id\n", "        ),\n", "\n", "        final_to_final_base as (\n", "            select \n", "            outlet_id,\n", "            outlet_name,\n", "            product_type,\n", "            city_name,\n", "            avg_orders,\n", "            avg_orders_old,\n", "            avg_searches_old,\n", "            cast(avg_searches_pen_old as real) as avg_searches_pen_old,\n", "            avg_city_orders_old,\n", "            avg_city_searches_old,\n", "            avg_city_searches_pen_old,\n", "            avg_cluster_orders_old,\n", "            avg_cluster_searches_old,\n", "            avg_cluster_searches_pen_old,\n", "            avg_nation_orders_old,\n", "            avg_nation_searches_old,\n", "            avg_nation_searches_pen_old,\n", "            case\n", "                when ((avg_searches_pen_old is not null)) then (avg_searches_pen_old) -- old store\n", "                -- New store Handling\n", "                when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is not null)) then (avg_city_searches_pen_old) -- new store handling but existing city\n", "                when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is null) and (avg_cluster_searches_pen_old is not null)) then (avg_cluster_searches_pen_old) -- new city \n", "                else (avg_nation_searches_pen_old) -- new cluster\n", "            end as derived_search_pen\n", "\n", "        from final_base fb \n", "        where fb.avg_orders is not null\n", "            and fb.avg_orders > 0\n", "\n", "        ),\n", "\n", "        percentile_values as (\n", "            select product_type,\n", "                city_name,\n", "                avg(derived_search_pen) as avg_search_pen,\n", "                stddev_pop(derived_search_pen) as std_dev_search_pen\n", "            from final_to_final_base\n", "            group by 1,2\n", "        )\n", "\n", "\n", "        select outlet_id,\n", "            outlet_name,\n", "            '{festival}' as festival_name,\n", "            ff.product_type,\n", "            ff.city_name,\n", "            avg_orders,\n", "            case \n", "                when derived_search_pen < avg_search_pen - 1.96 * std_dev_search_pen then avg_orders * (avg_search_pen - 1.96 * std_dev_search_pen)\n", "                when derived_search_pen > avg_search_pen + 1.96 * std_dev_search_pen then avg_orders * (avg_search_pen + 1.96 * std_dev_search_pen)\n", "            else avg_orders * derived_search_pen\n", "            end as val\n", "\n", "        from final_to_final_base ff\n", "        left join percentile_values pv on ff.city_name = pv.city_name and ff.product_type = pv.product_type\n", "\n", "        \"\"\"\n", "\n", "        to_trino(store_dist, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "b1993fa0-c18c-4dc5-8d5e-bdc1f688f792", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "8eade977-8d45-48a7-81b6-dc5238d71616", "metadata": {}, "outputs": [], "source": ["# df_store_dist = read_sql_query(store_dist, CON_TRINO)\n", "# df_store_dist"]}, {"cell_type": "code", "execution_count": null, "id": "483e1d6f-185f-4ba2-a787-9a73328da376", "metadata": {}, "outputs": [], "source": ["# df_store_dist.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eae03880-cb7b-4bb3-a445-3e3b59cf82a1", "metadata": {}, "outputs": [], "source": ["# df_store_dist.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "320f2640-aa57-4efa-b7e8-622d94d4b370", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_store_dist[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_store_dist.columns\n", "# ]\n", "\n", "# column_dtypes"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}