{"cells": [{"cell_type": "code", "execution_count": null, "id": "c444a6d5-18f4-4d0b-b89c-2d75bc77104d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4775dcdf-3b7a-40ec-a513-cd3d8b53e153", "metadata": {"tags": []}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b7962273-b8ba-4e38-b2ef-1cc1bcd174ad", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"category_planned_flag\", \"type\": \"real\", \"description\": \"Category Planned Flag\"},\n", "    {\"name\": \"festival_tech_flag\", \"type\": \"integer\", \"description\": \"Festival Tech Flag\"},\n", "    {\n", "        \"name\": \"festival_tech_flag_cpd_contribution\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Festival Tech Flag CPD Contribution\",\n", "    },\n", "    {\n", "        \"name\": \"festival_tech_flag_spike\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Festival Tech Flag Avg Spike\",\n", "    },\n", "    {\n", "        \"name\": \"festival_tech_flag_day_spike\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Festival Tech Flag Day Spike\",\n", "    },\n", "    {\n", "        \"name\": \"be_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Warehouse Facility ID\",\n", "    },\n", "    {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"Warehouse Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"cpd\", \"type\": \"real\", \"description\": \"Current APS\"},\n", "    {\n", "        \"name\": \"be_ptype_forecast_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis CPD\",\n", "    },\n", "    {\"name\": \"cpd_contribution\", \"type\": \"real\", \"description\": \"CPD Contribution\"},\n", "    {\"name\": \"max_spike\", \"type\": \"real\", \"description\": \"Max Spike\"},\n", "    {\"name\": \"avg_spike\", \"type\": \"real\", \"description\": \"Avg Spike\"},\n", "    {\"name\": \"spike\", \"type\": \"real\", \"description\": \"Spike\"},\n", "    {\n", "        \"name\": \"be_ptype_forecast_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis Planned QTY\",\n", "    },\n", "    {\"name\": \"be_ptype_forecast\", \"type\": \"real\", \"description\": \"Forecast Value\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ec78ac0-465d-4cfe-996f-1bc60d2d3970", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_be_ptype_flag\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"be_facility_id\",\n", "        \"be_name\",\n", "        \"product_type\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "8435f906-e62e-4a6f-b15e-588791cbd13f", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'"]}, {"cell_type": "code", "execution_count": null, "id": "e48e07b9-3628-43eb-a430-afa040b7fb46", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "cf02aa85-9a9c-4543-92d6-413721be99db", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "91065fce-5f90-4161-bd11-3a1344a951dc", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "09c74acd-46fb-40ea-af8c-069e947a3a9d", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d5ce9b6d-954e-42e9-ae00-cab83fdf2d3e", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "c025fccd-6e6c-4c91-a8ae-0e3d79dcd7d2", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[festive_date_data[\"flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "f86af62a-dd9f-4098-9e0c-1d2e0c57b660", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "19f57f28-ee54-49c7-a108-d8abf1bd7b91", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "1347758c-af2e-4ab4-9230-67effdfc992b", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "c26ced28-e03d-4302-b810-dd6a5e66b2cd", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        bau_start_date = row[\"bau_start_date\"]\n", "        bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "\n", "        be_ptype = f\"\"\" \n", "\n", "        with final_base as (\n", "            select \n", "                date_,\n", "                festival_name,\n", "                category_planned_flag,\n", "                be_facility_id,\n", "                be_name,\n", "                product_type,\n", "                cpd,\n", "                be_ptype_forecast_cpd,\n", "                be_ptype_forecast_planned,\n", "                be_ptype_forecast\n", "            from supply_etls.festival_forecast_be_ptype\n", "            where date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "            and festival_name = '{festival}'\n", "        ),\n", "\n", "\n", "        flag_festive_cart_pen as ( --0.02perc cart pen\n", "            SELECT\n", "                distinct product_type, try(1.0000*a.be_ptype_forecast_cpd/b.total_cpd) as cpd_contribution\n", "            FROM\n", "                (SELECT \n", "                    product_type, sum(cpd) as cpd,\n", "                    sum(be_ptype_forecast_cpd) AS be_ptype_forecast_cpd\n", "                FROM final_base\n", "                group by 1) a\n", "\n", "                CROSS JOIN\n", "\n", "                (SELECT \n", "                    sum(be_ptype_forecast_cpd) AS total_cpd\n", "                FROM final_base) b \n", "\n", "                WHERE try(1.0000*a.be_ptype_forecast_cpd/b.total_cpd)>0.0002 \n", "        ),\n", "\n", "        flag_festive_spike_day as ( --1.25x spike on any day\n", "            select \n", "                product_type, max(try(1.0000*be_ptype_forecast_cpd/cpd)) as max_spike\n", "            FROM final_base\n", "            where be_ptype_forecast_cpd>1.25*cpd  \n", "            group by 1\n", "        ),\n", "\n", "        flag_festive_spike as ( --1.25x spike across festival\n", "            select \n", "                distinct product_type, try(1.0000*be_ptype_forecast_cpd/cpd) as spike\n", "            from \n", "                (SELECT product_type, sum(cpd) as cpd,\n", "                        sum(be_ptype_forecast_cpd) AS be_ptype_forecast_cpd\n", "                    FROM final_base\n", "                    group by 1\n", "                    having sum(be_ptype_forecast_cpd)>1.25*sum(cpd)\n", "                )\n", "        )\n", "\n", "\n", "        SELECT \n", "\n", "            date_,\n", "            festival_name,\n", "            category_planned_flag,\n", "            (case when ffcp.product_type is not null and ffs.product_type is not null and ffsd.product_type is not null then 1 else 0 end) as festival_tech_flag,\n", "            (case when ffcp.product_type is not null then 1 else 0 end) as festival_tech_flag_cpd_contribution,\n", "            (case when ffs.product_type is not null then 1 else 0 end) as festival_tech_flag_spike,\n", "            (case when ffsd.product_type is not null then 1 else 0 end) as festival_tech_flag_day_spike,\n", "            be_facility_id,\n", "            be_name,\n", "            fb.product_type,\n", "            cpd,\n", "            be_ptype_forecast_cpd,\n", "            ffcp.cpd_contribution,\n", "            ffsd.max_spike,\n", "            ffs.spike as avg_spike,\n", "            try(1.0000*be_ptype_forecast_cpd/cpd) as spike,\n", "            be_ptype_forecast_planned,\n", "            be_ptype_forecast\n", "\n", "        from final_base fb\n", "        left join flag_festive_cart_pen ffcp on ffcp.product_type=fb.product_type\n", "        left join flag_festive_spike ffs on ffs.product_type=fb.product_type\n", "        left join flag_festive_spike_day ffsd on ffsd.product_type=fb.product_type\n", "\n", "\n", "        \"\"\"\n", "\n", "        to_trino(be_ptype, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "a29df938-2c97-490a-86f3-b6f08a92651d", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b66d084f-319e-4477-ac0f-29eaac5f33de", "metadata": {}, "outputs": [], "source": ["# df_be_ptype = read_sql_query(be_ptype, CON_TRINO)\n", "# df_be_ptype"]}, {"cell_type": "code", "execution_count": null, "id": "de630dd3-e554-47d6-86e9-e351925a6993", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d3cbe65b-4fcc-4f4b-bfd6-c8384cdf4c31", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9de672d0-fe2f-4623-8b6c-608a5a9009d1", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_be_ptype[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_be_ptype.columns\n", "# ]\n", "\n", "# column_dtypes"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}