{"cells": [{"cell_type": "code", "execution_count": null, "id": "579395d0-190e-4eef-8f7a-c6db59356a8f", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import copy\n", "import time\n", "import warnings\n", "import datetime\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date\n", "\n", "\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "pd.set_option(\"display.float_format\", \"{:.2f}\".format)"]}, {"cell_type": "markdown", "id": "65aa6b5d-7c76-40cd-b587-82553508b995", "metadata": {}, "source": ["# Tables -\n", "1. ```supply_etls.festival_forecast_city_date_ptype_intent``` - \"date\", \"city\", \"product_type\", \"orig_fest_name\", \"festival\", \"festival_year\"\n", "2. ```supply_etls.festival_forecast_city_year_intent``` (City wise intent numbers) - \"city\", \"festival\", \"festival_year\", \"orig_fest_name\"\n", "3. ```supply_etls.festive_forecast_city_wise_intent_growth``` - \"city\", \"orig_fest_name\""]}, {"cell_type": "markdown", "id": "2e0690ab-b4fb-46aa-bc67-ab98c4b2a239", "metadata": {}, "source": ["# Schema"]}, {"cell_type": "code", "execution_count": null, "id": "fc45aea3-44db-469e-abd4-f5af2d417229", "metadata": {}, "outputs": [], "source": ["ptype_festival_intent_growth_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_date_ptype_intent\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"Date\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\n", "            \"name\": \"product_type\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"product_type\",\n", "        },\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Name of the festival\",\n", "        },\n", "        {\n", "            \"name\": \"orig_fest_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"orig_fest_name\",\n", "        },\n", "        {\n", "            \"name\": \"festival_year\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Year of the festival\",\n", "        },\n", "        {\n", "            \"name\": \"search_device_count\",\n", "            \"type\": \"double\",\n", "            \"description\": \"search_device_count\",\n", "        },\n", "        {\n", "            \"name\": \"demand_device_count\",\n", "            \"type\": \"double\",\n", "            \"description\": \"demand_device_count\",\n", "        },\n", "        {\n", "            \"name\": \"dau_city_date\",\n", "            \"type\": \"double\",\n", "            \"description\": \"dau_city_date\",\n", "        },\n", "        {\n", "            \"name\": \"search_intent\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Total of (search_device_count/dau) grouped by ptype, city, date\",\n", "        },\n", "        {\n", "            \"name\": \"demand_intent\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Total of demand_device_count/dau, grouped by ptype, date, city\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"date\", \"city\", \"product_type\", \"orig_fest_name\", \"festival\", \"festival_year\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Captures the demand/search count, dau, search/demand intent for Ptypes x city x date for festivals\",\n", "    \"partition_key\": [\"orig_fest_name\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c56767e6-c7b9-43c4-88b3-6c1a46917015", "metadata": {}, "outputs": [], "source": ["city_year_fest_base = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_year_intent\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"Name of the festival\",\n", "        },\n", "        {\n", "            \"name\": \"orig_fest_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"orig_fest_name\",\n", "        },\n", "        {\n", "            \"name\": \"festival_year\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Year of the festival\",\n", "        },\n", "        {\n", "            \"name\": \"search_intent\",\n", "            \"type\": \"double\",\n", "            \"description\": \"search_intent\",\n", "        },\n", "        {\n", "            \"name\": \"demand_intent\",\n", "            \"type\": \"double\",\n", "            \"description\": \"demand_intent\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"city\", \"festival\", \"festival_year\", \"orig_fest_name\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Captures the city wise intent\",\n", "    \"partition_key\": [\"orig_fest_name\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f5096d83-7cfb-4ae7-9485-dbf4a03596f9", "metadata": {}, "outputs": [], "source": ["city_intent_growth_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_wise_intent_growth\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\n", "            \"name\": \"festival\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"festival base name\",\n", "        },\n", "        {\n", "            \"name\": \"orig_fest_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"orig_fest_name\",\n", "        },\n", "        {\n", "            \"name\": \"search_intent_growth\",\n", "            \"type\": \"double\",\n", "            \"description\": \"search_intent_growth\",\n", "        },\n", "        {\n", "            \"name\": \"demand_intent_growth\",\n", "            \"type\": \"double\",\n", "            \"description\": \"demand_intent_growth\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"city\", \"orig_fest_name\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Captures city-wise intent growth\",\n", "    \"partition_key\": [\"orig_fest_name\"],\n", "}"]}, {"cell_type": "markdown", "id": "7f53ce65-44dc-4e63-9fa9-260ce7534c52", "metadata": {}, "source": ["# Query"]}, {"cell_type": "code", "execution_count": null, "id": "71012157-1db8-4f6d-981f-72a354da7e6e", "metadata": {}, "outputs": [], "source": ["Sql_query_parent = \"\"\"\n", "with ptype_distribution_raw as (\n", "    select *\n", "    from supply_etls.keyword_ptype_distribution\n", "    where ((date_ between (date('{start_date}') - interval'2'day) and (date('{end_date}') + interval'2'day))\n", "        or (date_ between (date('{last_year_start}') - interval'2'day) and (date('{last_year_end}') + interval'2'day)))\n", "),\n", "\n", "ptype_distribution0 as (\n", "    select base.date_,\n", "        base.keyword,\n", "        base.product_type,\n", "        coalesce(devices,0) as devices,\n", "        coalesce(total_devices,0) as total_devices,\n", "        coalesce(sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 2 preceding and 2 following),0) as nearby_devices,\n", "        coalesce(sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 2 preceding and 2 following),0) as nearby_total_devices,\n", "        coalesce( try((1.00 * sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 2 preceding and 2 following)) / (1.00 * sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 2 preceding and 2 following))),0) as percentage_attri\n", "        \n", "    from (\n", "            (select distinct date_ from ptype_distribution_raw) \n", "                cross join \n", "            (select distinct keyword, product_type from ptype_distribution_raw)\n", "        ) base\n", "    left join ptype_distribution_raw pdr on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "), \n", "\n", "ptype_distribution1 as (\n", "    select date_,\n", "        keyword,\n", "        product_type,\n", "        devices,\n", "        total_devices,\n", "        nearby_devices,\n", "        nearby_total_devices,\n", "        percentage_attri,\n", "        rank() over (partition by date_, keyword order by percentage_attri desc) as key_rank\n", "    from ptype_distribution0\n", "    \n", "),\n", "\n", "ptype_distribution as (\n", "    select date_,\n", "        keyword,\n", "        product_type,\n", "        devices,\n", "        total_devices,\n", "        nearby_devices,\n", "        nearby_total_devices,\n", "        percentage_attri,\n", "        try(percentage_attri/(sum(percentage_attri) over(partition by date_, keyword))) as normalized_percent_attri,\n", "        key_rank\n", "    from ptype_distribution1\n", "    where (key_rank<=1 or percentage_attri > 0.075)\n", "),\n", "        \n", "keys as (\n", "    select distinct date_,\n", "        keyword,\n", "        outlet_id,\n", "        search_device_count\n", "  from supply_etls.merchant_dod_keyword_searches_v2\n", "  where ((date_ between (date('{start_date}') - interval'2'day) and (date('{end_date}') + interval'2'day))\n", "        or (date_ between (date('{last_year_start}') - interval'2'day) and (date('{last_year_end}') + interval'2'day)))\n", " \n", "),\n", "\n", "keys_dau as (\n", "    select date_,\n", "        outlet_id,\n", "        sum(search_device_count) as search_dau\n", "    from keys\n", "    group by 1,2 \n", "),\n", "\n", "atc_split as (\n", "    select \n", "        date_,\n", "        outlet_id,\n", "        product_type,\n", "        1.000000 * sum(search_atc_device_count) as search_atcs,\n", "        1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "        try((1.000000 * sum(search_atc_device_count))/((1.000000 * coalesce(sum(search_atc_device_count),0)) + (1.000000 * coalesce(sum(non_search_atc_device_count),0)))) as split\n", "    from supply_etls.ptype_store_atc_channel_split_v2\n", "    where ((date_ between (date('{start_date}') - interval'2'day) and (date('{end_date}') + interval'2'day))\n", "        or (date_ between (date('{last_year_start}') - interval'2'day) and (date('{last_year_end}') + interval'2'day)))\n", "    group by 1,2,3\n", "),\n", "\n", "\n", "final_outlet as (\n", "    select keys.date_,\n", "        coalesce(keys.outlet_id, atc_split.outlet_id) as outlet_id,\n", "        coalesce(ptype_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "        avg(split) as avg_split,\n", "\n", "        sum(keys.search_device_count * normalized_percent_attri) as search_device_count_normalized,\n", "        try((sum(keys.search_device_count * normalized_percent_attri))/avg(split)) as demand_device_count_normalized,\n", "\n", "        avg(search_dau) as search_dau\n", "    from keys\n", "    left join retail.console_outlet co on co.id = keys.outlet_id\n", "    left join retail.console_location cl on co.tax_location_id = cl.id\n", "    left join ptype_distribution on keys.date_ = ptype_distribution.date_ and keys.keyword = ptype_distribution.keyword\n", "    left join atc_split on keys.date_ = atc_split.date_ and ptype_distribution.product_type = atc_split.product_type and atc_split.outlet_id = keys.outlet_id\n", "    left join keys_dau on keys_dau.date_ = keys.date_ and keys_dau.outlet_id = keys.outlet_id\n", "    group by 1,2,3\n", "),\n", "\n", "outlet_city_mapping as (\n", "select \n", "    distinct outlet_id, cl.name as city\n", "from \n", "    po.physical_facility_outlet_mapping pfom\n", "    \n", "inner join retail.console_location cl \n", "    on pfom.city_id = cl.id\n", "),\n", "\n", "final_city_outlet as (\n", "select\n", "    a.date_,\n", "    a.outlet_id,\n", "    b.city,\n", "    a.product_type,\n", "    a.search_device_count_normalized,\n", "    a.demand_device_count_normalized,\n", "    a.search_dau\n", "from final_outlet a\n", "left join outlet_city_mapping b \n", "on a.outlet_id = b.outlet_id\n", "),\n", "\n", "city_rollup as (\n", "select \n", "    date_,\n", "    city,\n", "    product_type,\n", "    sum(search_device_count_normalized) as search_device_count,\n", "    sum(demand_device_count_normalized)  as demand_device_count\n", "from final_city_outlet\n", "where ((date_ between (date('{last_year_start}')) and (date('{last_year_end}')))\n", "    or (date_ between (date('{start_date}')) and (date('{end_date}'))))\n", "group by 1,2,3\n", "),\n", "\n", "\n", "city_date_dau as (\n", "select \n", "    snapshot_date_ist as date,\n", "    (case \n", "        when city in ('HR-NCR') then 'Gurgaon'\n", "        when city in ('UP-NCR') then 'Ghaziabad'\n", "        else city\n", "    end) as city,\n", "    sum(daily_active_users) as dau_city_date\n", "from dwh.agg_daily_consumer_conversion_details dau\n", "where \n", "    (((snapshot_date_ist between date('{start_date}') and date('{end_date}'))) or\n", "(snapshot_date_ist between date('{last_year_start}') and date('{last_year_end}')))\n", "    and snapshot_hour_ist = 24\n", "    and city is not null\n", "    and dau.merchant_id = 'Overall'\n", "group by 1,2\n", "),\n", "\n", "\n", "festive_ptypes as (\n", "SELECT \n", "    distinct product_type\n", "from supply_etls.festival_forecast_city_ptype_flag\n", "where \n", "    festival_name = '{orig_fest_name}' and avg_spike >= 3\n", "),\n", "\n", "Base_Table_final as (\n", "select \n", "    a.date_ as date,\n", "    a.city,\n", "    a.product_type,\n", "    '{festival}' as festival,\n", "    '{orig_fest_name}' as orig_fest_name,\n", "    cast(year(a.date_) as double) as festival_year,\n", "    \n", "    a.search_device_count,\n", "    a.demand_device_count,\n", "    b.dau_city_date,\n", "    coalesce(((a.search_device_count)/(b.dau_city_date)),0) as search_intent,\n", "    coalesce(((a.demand_device_count)/(b.dau_city_date)),0) as demand_intent\n", "from city_rollup a\n", "inner join festive_ptypes fp on fp.product_type = a.product_type\n", "left join city_date_dau b\n", "on b.city = a.city and a.date_ = b.date\n", ")\n", "\n", "select *\n", "from Base_Table_final\n", "where ((date between (date('{last_year_start}')) and (date('{last_year_end}')))\n", "    or (date between (date('{start_date}')) and (date('{end_date}'))))\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "0668d5e0-3f30-4233-b530-f6abba02c036", "metadata": {}, "outputs": [], "source": ["sql_city_aggregation_01 = \"\"\"\n", "with calc_day_city as (\n", "select\n", "    date,\n", "    city,\n", "    dau_city_date,\n", "    sum(search_device_count) as search_device_count,\n", "    sum(demand_device_count) as demand_device_count\n", "from supply_etls.festival_forecast_city_date_ptype_intent\n", "where orig_fest_name = '{orig_fest_name}'\n", "    and ((date between (date('{last_year_start}')) and (date('{last_year_end}')))\n", "    or (date between (date('{start_date}')) and (date('{end_date}'))))\n", "group by 1,2,3\n", "),\n", "\n", "calc_city as (\n", "select \n", "    a.city,\n", "    '{festival}' as festival,\n", "    '{orig_fest_name}' as orig_fest_name,\n", "    cast(year(a.date) as double) as festival_year,\n", "    \n", "    sum(a.search_device_count) as search_device_count,\n", "    sum(a.demand_device_count) as demand_device_count,\n", "    \n", "    coalesce((sum(a.search_device_count)/sum(a.dau_city_date)), 0) as search_intent,\n", "    coalesce((sum(a.demand_device_count)/sum(a.dau_city_date)),0) as demand_intent\n", "\n", "    \n", "from calc_day_city a\n", "group by 1,2,3,4\n", ")\n", "\n", "select * \n", "from calc_city\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1eb3e71b-de33-40ce-b01f-9305213562d4", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df\n", "\n", "\n", "def fetch_festival_df(festival_lst):\n", "\n", "    sql_intent = f\"\"\"\n", "        select \n", "        *\n", "        from supply_etls.festival_forecast_city_year_intent\n", "        where festival = '{festival_lst}'\n", "        and city <> 'Not in service area'\n", "    \"\"\"\n", "    CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "    festival_intent_data = read_sql_query(sql_intent, CON_TRINO)\n", "    return festival_intent_data"]}, {"cell_type": "markdown", "id": "200aef35-254a-4544-9394-7608ee17796f", "metadata": {}, "source": ["# Helper Function"]}, {"cell_type": "code", "execution_count": null, "id": "446fb7bd-e3c6-4c81-ac4d-c1a96a2a08aa", "metadata": {}, "outputs": [], "source": ["def push_to_table(\n", "    Sql_query_parent,\n", "    sql_city_aggregation_01,\n", "    start_date,\n", "    end_date,\n", "    last_year_start,\n", "    last_year_end,\n", "    festival,\n", "    key_rank,\n", "    precent_attri_threshold,\n", "    orig_fest_name,\n", "):\n", "\n", "    # Creating interim table for FE FUNNEL/SEARCHERS devices\n", "    try:\n", "        msg = f\"Creating table for Festival & Intent data between {str(start_date)} and {str(end_date)}, for festival = {festival}\"\n", "        print(msg)\n", "\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        _trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "        print(\"Pushing the base table at city x date x ptype level\")\n", "        pb.to_trino(\n", "            Sql_query_parent.format(\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                last_year_start=last_year_start,\n", "                last_year_end=last_year_end,\n", "                snapshot_dt_ist=snapshot_dt_ist,\n", "                festival=festival,\n", "                key_rank=key_rank,\n", "                precent_attri_threshold=precent_attri_threshold,\n", "                orig_fest_name=orig_fest_name,\n", "            ),\n", "            **ptype_festival_intent_growth_schema,\n", "        )\n", "        print(\"Pushed the base Table...\")\n", "        time.sleep(60)\n", "        print()\n", "\n", "        print(\"Pushing the aggregation of city\")\n", "        pb.to_trino(\n", "            sql_city_aggregation_01.format(\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                last_year_start=last_year_start,\n", "                last_year_end=last_year_end,\n", "                festival=festival,\n", "                orig_fest_name=orig_fest_name,\n", "            ),\n", "            **city_year_fest_base,\n", "        )\n", "\n", "        print(\"Pushed the base & aggregated tables..\")\n", "\n", "        del _trino_con, _trino_conn\n", "        print(\"Done\")\n", "\n", "    except Exception as e:\n", "        msg = f\"Failed to push Festival & Intent data to interim DB between {str(start_date)} and {str(end_date)}, got error = {e}\"\n", "        print(msg)\n", "        raise Exception(msg)"]}, {"cell_type": "code", "execution_count": null, "id": "150d8291-56b2-492b-b47d-573823e11466", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "de9d536b-08fd-4346-b803-23a4a5b910f1", "metadata": {}, "source": ["# Input"]}, {"cell_type": "code", "execution_count": null, "id": "f986be33-91ac-41bd-a205-8aa79b9324df", "metadata": {}, "outputs": [], "source": ["# df = pd.DataFrame()\n", "# df['start_date'] = ['2025-02-05']\n", "# df['end_date'] = ['2025-02-15']\n", "\n", "# df['last_year_start'] = ['2024-02-05']\n", "# df['last_year_end'] = ['2024-02-15']\n", "\n", "\n", "# df['orig_fest_name'] = ['VALENTINES_DAY_2025-02-14']\n", "# df['hero_sku_avail_cutoff'] = [0.7]\n", "# df['city_order_cutoff'] = [75]\n", "# df['top_assortment_cutoff'] = [0.075]\n", "# df['cum_per_contri_cutoff'] = [0.8]\n", "# df['key_rank_intent_cutoff'] = [1]\n", "# df['intent_percent_attri_cutoff'] = [ 0.05]\n", "# df[\"overwrite_flag\"] = [1]\n", "\n", "\n", "df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)\n", "\n", "\n", "df[\"start_date\"] = df[\"sale_start_date\"]\n", "df[\"end_date\"] = df[\"sale_end_date\"]\n", "\n", "df[\"last_year_start\"] = df[\"festival_start_date\"]\n", "df[\"last_year_end\"] = df[\"festival_end_date\"]\n", "\n", "df[\"orig_fest_name\"] = df[\"festival\"]\n", "df[\"hero_sku_avail_cutoff\"] = df[\"hero_sku_availability\"].astype(float)\n", "df[\"cum_per_contri_cutoff\"] = df[\"hero_sku_sales_contribution\"].astype(float)\n", "df[\"usage_in_forecasting\"] = df[\"flag\"].astype(int)\n", "df[\"overwrite_flag\"] = df[\"overwrite_flag\"].astype(int)\n", "\n", "\n", "df[\"top_assortment_cutoff\"] = 0.075\n", "df[\"city_order_cutoff\"] = 75\n", "df[\"key_rank_intent_cutoff\"] = 1\n", "df[\"intent_percent_attri_cutoff\"] = 0.05\n", "\n", "df[\"festival_base\"] = df[\"orig_fest_name\"].str.replace(r\"[\\d_-]\", \"\", regex=True).str.lower()\n", "\n", "\n", "other_columns = [\n", "    \"orig_fest_name\",\n", "    \"festival_base\",\n", "    \"overwrite_flag\",\n", "    \"hero_sku_avail_cutoff\",\n", "    \"cum_per_contri_cutoff\",\n", "    \"top_assortment_cutoff\",\n", "    \"city_order_cutoff\",\n", "    \"key_rank_intent_cutoff\",\n", "    \"intent_percent_attri_cutoff\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4f57a222-4484-4478-aa20-8f1bb7f74f38", "metadata": {}, "outputs": [], "source": ["input_df = df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "d33654f1-a232-4bcb-8043-3023fc94eeb5", "metadata": {}, "outputs": [], "source": ["input_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "db36f233-9825-4951-aca5-0e12ce639c76", "metadata": {}, "outputs": [], "source": ["# input_df[\"festival_year\"] = pd.to_datetime(input_df[\"end_date\"]).dt.year"]}, {"cell_type": "code", "execution_count": null, "id": "4201766b-1327-4ae3-9e0d-125a1cb7f876", "metadata": {}, "outputs": [], "source": ["today_date = pd.to_datetime(date.today()).strftime(\"%Y-%m-%d\")\n", "today_date"]}, {"cell_type": "code", "execution_count": null, "id": "27380bc7-9e99-4d9f-8028-2b2567a43a6a", "metadata": {}, "outputs": [], "source": ["input_df.head()"]}, {"cell_type": "markdown", "id": "cb149436-0c8a-4195-954c-e88a191accfe", "metadata": {}, "source": ["# Finding festivals in tables to be pushed"]}, {"cell_type": "code", "execution_count": null, "id": "ccc4a72c-e71d-42f3-bfee-9601395155b2", "metadata": {}, "outputs": [], "source": ["sql_festivals_in_trino = f\"\"\"\n", "SELECT \n", "    distinct orig_fest_name\n", "FROM \n", "    supply_etls.festival_forecast_city_date_ptype_intent\n", "WHERE \n", "    date <= date('{today_date}')\n", "    and orig_fest_name is not null\n", "    and festival_year is not null\n", "    and festival is not null\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "90f3aa8a-7bc9-4380-b3b6-8345e8d8cc0d", "metadata": {}, "outputs": [], "source": ["try:\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    table_present_festivals = pd.read_sql(sql_festivals_in_trino, con=con)\n", "\n", "    if len(table_present_festivals) == 0:\n", "        table_present_festivals = pd.DataFrame()\n", "        table_present_festivals[\"orig_fest_name\"] = []\n", "        table_present_festivals[\"festival\"] = []\n", "        table_present_festivals[\"festival_year\"] = []\n", "\n", "\n", "except Exception as e:\n", "    # print(e)\n", "    # max_run_id = 0\n", "    table_present_festivals = pd.DataFrame()\n", "    table_present_festivals[\"orig_fest_name\"] = []\n", "    table_present_festivals[\"festival\"] = []\n", "    table_present_festivals[\"festival_year\"] = []"]}, {"cell_type": "code", "execution_count": null, "id": "98c769bf-2be7-4124-b803-8b8bb21f86b3", "metadata": {}, "outputs": [], "source": ["input_df[\"end_date\"] = pd.to_datetime(input_df[\"end_date\"])\n", "today = pd.to_datetime(date.today())"]}, {"cell_type": "code", "execution_count": null, "id": "75b0ffe3-235f-455f-a2fa-29e434ec672c", "metadata": {}, "outputs": [], "source": ["# All festivals that are already passed in our dataframe\n", "all_festivals_passed = input_df[input_df[\"end_date\"] < today]\n", "\n", "\n", "festival_year_passed_unique = all_festivals_passed[[\"orig_fest_name\"]].drop_duplicates()\n", "\n", "\n", "# Festivals Present in our Trino Table\n", "if table_present_festivals.shape[0] == 0:\n", "    df_festivals_to_be_pushed_1 = all_festivals_passed\n", "\n", "else:\n", "    festivals_in_trino = table_present_festivals[[\"orig_fest_name\"]].drop_duplicates()\n", "\n", "    # Finding festivals that are done, yet to be pushed to Trino.\n", "    combinations_to_push = (\n", "        pd.merge(festival_year_passed_unique, festivals_in_trino, how=\"left\", indicator=True)\n", "        .query('_merge == \"left_only\"')\n", "        .drop(\"_merge\", axis=1)\n", "    )\n", "\n", "    df_festivals_to_be_pushed_1 = pd.merge(\n", "        all_festivals_passed,\n", "        combinations_to_push,\n", "        on=[\"orig_fest_name\"],\n", "        how=\"inner\",\n", "    ).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4437bbc8-c24b-4d56-a4e2-557ccae39202", "metadata": {}, "outputs": [], "source": ["df_festivals_to_be_pushed_1 = df_festivals_to_be_pushed_1.reset_index(drop=True)\n", "df_festivals_to_be_pushed_1"]}, {"cell_type": "markdown", "id": "3a794388-0b4a-4506-8799-a0d5910d5b55", "metadata": {}, "source": ["## Also adding the festivals where overwrite_flag = 1 to df_festivals_to_be_pushed\n"]}, {"cell_type": "code", "execution_count": null, "id": "653a113a-40cf-4f00-9058-d2c29b50c44c", "metadata": {}, "outputs": [], "source": ["festivals_to_overwrite = input_df[\n", "    (input_df[\"overwrite_flag\"] == 1) & (input_df[\"end_date\"] < today)\n", "].reset_index(drop=True)\n", "\n", "df_festivals_to_be_pushed = pd.concat(\n", "    [df_festivals_to_be_pushed_1, festivals_to_overwrite], axis=0, ignore_index=True\n", ")\n", "df_festivals_to_be_pushed = df_festivals_to_be_pushed.drop_duplicates().reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9aced3a4-bda7-4bf1-ad50-c28c574dbfa7", "metadata": {}, "outputs": [], "source": ["df_festivals_to_be_pushed"]}, {"cell_type": "markdown", "id": "b8c81b1f-3db3-4619-8934-373e8a092324", "metadata": {}, "source": ["# Push Festivals to Trino"]}, {"cell_type": "markdown", "id": "3236f481-34a2-43ba-9885-422fa8e1ba67", "metadata": {}, "source": ["### Push to Trino all the festivals in our sheet that have passed, but not in Trino."]}, {"cell_type": "code", "execution_count": null, "id": "af3d5cb2-d69f-4a5a-83e8-a37bb5d5f23d", "metadata": {}, "outputs": [], "source": ["lst = []\n", "\n", "for row in range(len(df_festivals_to_be_pushed)):\n", "\n", "    start_date = str(df_festivals_to_be_pushed[\"start_date\"][row])\n", "    end_date = str(df_festivals_to_be_pushed[\"end_date\"][row].strftime(\"%Y-%m-%d\"))\n", "    last_year_start = str(df_festivals_to_be_pushed[\"last_year_start\"][row])\n", "    last_year_end = str(df_festivals_to_be_pushed[\"last_year_end\"][row])\n", "\n", "    festival = str(df_festivals_to_be_pushed[\"festival_base\"][row])\n", "    orig_fest_name = str(df_festivals_to_be_pushed[\"orig_fest_name\"][row])\n", "    # festival_year = str(df_festivals_to_be_pushed[\"festival_year\"][row])\n", "\n", "    print(f\"========= Now Pushing for {orig_fest_name}  =========\")\n", "\n", "    key_rank = str(df_festivals_to_be_pushed[\"key_rank_intent_cutoff\"][row])\n", "    precent_attri_threshold = str(df_festivals_to_be_pushed[\"intent_percent_attri_cutoff\"][row])\n", "    snapshot_dt_ist = today.strftime(\"%Y-%m-%d\")\n", "\n", "    snap_split = snapshot_dt_ist.split(\"-\")\n", "    # run_id = max_run_id + 1\n", "\n", "    try:\n", "        msg = f\"Pushing to Table for festival = {orig_fest_name}, start_date = {start_date}, end_date = {end_date}, last_year_start = {last_year_start}, last_year_end = {last_year_end}\"\n", "        print(msg)\n", "\n", "        push_to_table(\n", "            Sql_query_parent,\n", "            sql_city_aggregation_01,\n", "            start_date,\n", "            end_date,\n", "            last_year_start,\n", "            last_year_end,\n", "            festival,\n", "            key_rank,\n", "            precent_attri_threshold,\n", "            orig_fest_name,\n", "        )\n", "\n", "        print(f\"Pushed All base table for {orig_fest_name}\")\n", "\n", "    except Exception as E:\n", "        msg = f\"Couldn't push to Trino Table, exception has occured with error message = {E}\"\n", "        print(msg)\n", "\n", "    print(f\"=============================================\")\n", "    print()\n", "\n", "# df_full = pd.concat(lst, axis = 0)"]}, {"cell_type": "code", "execution_count": null, "id": "9e69fb46-22f6-4562-99cc-a568eb5926e7", "metadata": {}, "outputs": [], "source": ["print(\"Now Trino Table is up to date\")"]}, {"cell_type": "markdown", "id": "7437c092-92d7-4439-819b-781302dec77b", "metadata": {}, "source": ["# Aggregated Growth Factor"]}, {"cell_type": "code", "execution_count": null, "id": "a30c13ca-0027-4323-b42c-8213a0358f4e", "metadata": {}, "outputs": [], "source": ["sql_city_growth = \"\"\"\n", "with min_max_years as (\n", "select \n", "    max(festival_year) as max_year,\n", "    min(festival_year) as min_year\n", "from supply_etls.festival_forecast_city_year_intent\n", "where orig_fest_name = '{orig_fest_name}'\n", "),\n", "\n", "max_year_festival_info as \n", "(\n", "select *\n", "from supply_etls.festival_forecast_city_year_intent\n", "where orig_fest_name = '{orig_fest_name}' \n", "    and festival_year = (select max_year from min_max_years)\n", "),\n", "\n", "\n", "min_year_festival_info as \n", "(\n", "select *\n", "from supply_etls.festival_forecast_city_year_intent\n", "where orig_fest_name = '{orig_fest_name}' \n", "    and festival_year = (select min_year from min_max_years)\n", "),\n", "\n", "\n", "common_cities as (\n", "select\n", "    distinct a.city\n", "from max_year_festival_info a\n", "inner join min_year_festival_info b\n", "on a.city = b.city\n", "),\n", "\n", "\n", "city_weight as (\n", "    select \n", "        city,\n", "        cast(weight as double) as weight\n", "    from supply_etls.city_weights\n", "    where updated_at=(select max(updated_at) from supply_etls.city_weights)\n", "    and city in (select city from common_cities)\n", "),\n", "\n", "\n", "\n", "pan_india_fallback_base as (\n", "select\n", "    a.city,\n", "    a.demand_intent as demand_intent_max_yr,\n", "    a.search_intent as search_intent_max_yr,\n", "    \n", "    b.demand_intent as demand_intent_min_yr,\n", "    b.search_intent as search_intent_min_yr,\n", "    \n", "    1.0000*((1.0000*(a.demand_intent))/(1.0000*(b.demand_intent))) as demand_intent_growth,\n", "    1.0000*((1.0000*(a.search_intent))/(1.0000*(b.search_intent))) as search_intent_growth\n", "\n", "from max_year_festival_info a\n", "inner join min_year_festival_info b\n", "on a.city = b.city\n", "),\n", "\n", "\n", "\n", "\n", "weighted_intent AS (\n", "    SELECT t.city,\n", "          (t.demand_intent_growth * w.weight) AS weighted_DI,\n", "          (t.search_intent_growth * w.weight) AS weighted_SI,\n", "          w.weight\n", "    FROM pan_india_fallback_base t\n", "    INNER JOIN city_weight w ON t.city = w.city\n", "    where t.search_intent_min_yr <> 0 and t.demand_intent_min_yr <> 0 \n", "),\n", "\n", "total_weighted_vals AS (\n", "SELECT SUM(weighted_DI) AS total_weighted_DI,\n", "      SUM(weighted_SI) AS total_weighted_SI,\n", "      SUM(weight) AS total_weight\n", "FROM weighted_intent\n", "),\n", "\n", "\n", "pan_india_fallback as (\n", "SELECT \n", "    (1.0000*total_weighted_DI) / total_weight AS fallback_demand_intent,\n", "    (1.0000*total_weighted_SI) / total_weight AS fallback_search_intent\n", "FROM total_weighted_vals\n", "),\n", "\n", "growth_cte AS (\n", "    SELECT\n", "        maxi.city,\n", "        maxi.demand_intent as demand_intent_max_year,\n", "        maxi.search_intent as search_intent_max_year,\n", "        \n", "        mini.demand_intent as demand_intent_min_year,\n", "        mini.search_intent as search_intent_max_year,\n", "        f.fallback_demand_intent,\n", "        f.fallback_search_intent,\n", "        \n", "        \n", "        CASE \n", "            WHEN mini.demand_intent = 0 OR mini.demand_intent IS NULL THEN f.fallback_demand_intent\n", "            ELSE 1.0000*((1.0000*(maxi.demand_intent))/(1.0000*(mini.demand_intent)))\n", "        END AS demand_intent_growth, \n", "        \n", "        \n", "        CASE \n", "            WHEN mini.search_intent = 0 OR mini.search_intent IS NULL THEN f.fallback_search_intent\n", "            ELSE 1.0000*((1.0000*(maxi.search_intent))/(1.0000*(mini.search_intent)))\n", "        END AS search_intent_growth\n", "        \n", "        \n", "        \n", "    FROM max_year_festival_info maxi\n", "    LEFT JOIN min_year_festival_info mini\n", "        ON maxi.city = mini.city\n", "    CROSS JOIN pan_india_fallback f\n", "),\n", "\n", "\n", "FINAL_INTENT_GROWTH AS (\n", "SELECT\n", "    city,\n", "    '{festival_base}' as festival,\n", "    '{orig_fest_name}' as orig_fest_name,\n", "    demand_intent_growth,\n", "    search_intent_growth\n", "FROM growth_cte\n", "\n", "UNION ALL\n", "\n", "SELECT\n", "    'PAN INDIA' as city,\n", "    '{festival_base}' as festival,\n", "    '{orig_fest_name}' as orig_fest_name,\n", "    f.fallback_demand_intent,\n", "    f.fallback_search_intent\n", "FROM pan_india_fallback f\n", "\n", ")\n", "\n", "SELECT *\n", "FROM FINAL_INTENT_GROWTH\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "03b1df6e-e994-45cf-81ed-532c2f729853", "metadata": {}, "outputs": [], "source": ["def push_to_growth_table(sql_city_growth, festival_base, orig_fest_name):\n", "\n", "    # Creating interim table for FE FUNNEL/SEARCHERS devices\n", "    try:\n", "        msg = f\"Pushing growth data\"\n", "        print(msg)\n", "\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        _trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "        pb.to_trino(\n", "            sql_city_growth.format(festival_base=festival_base, orig_fest_name=orig_fest_name),\n", "            **city_intent_growth_schema,\n", "        )\n", "\n", "        del _trino_con, _trino_conn\n", "        print(\"Done\")\n", "\n", "    except Exception as e:\n", "        msg = f\"Failed to push intent growth to  DB, got error = {e}\"\n", "        print(msg)\n", "        raise Exception(msg)"]}, {"cell_type": "code", "execution_count": null, "id": "aad4113a-563d-4fd8-8ee9-ab765c0e382d", "metadata": {}, "outputs": [], "source": ["festival_uniques = (\n", "    df_festivals_to_be_pushed[[\"orig_fest_name\", \"festival_base\"]]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5f253e69-7332-484f-8267-c321b6d04789", "metadata": {}, "outputs": [], "source": ["for idx in range(len(festival_uniques)):\n", "\n", "    festival_base = festival_uniques[\"festival_base\"][idx]\n", "    orig_fest_name = festival_uniques[\"orig_fest_name\"][idx]\n", "\n", "    print(\n", "        f\"======= Aggregating Intent growth  for {festival_base}, which is the base of {orig_fest_name} =======\"\n", "    )\n", "\n", "    push_to_growth_table(sql_city_growth, festival_base, orig_fest_name)\n", "\n", "    print(\"==========================================\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "id": "da486a75-7a9e-47a9-9fc5-95faabbd8e30", "metadata": {}, "outputs": [], "source": ["print(\"Finished\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}