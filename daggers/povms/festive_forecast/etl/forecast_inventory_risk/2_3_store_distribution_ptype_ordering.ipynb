{"cells": [{"cell_type": "code", "execution_count": null, "id": "d68efcb7-349f-47dd-88fd-6104a2fa0b93", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "77a647bc-f311-4e53-9a40-5dfd1919b944", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "e34d5b5c-d742-4c25-99e7-d1ad00b27ead", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"Dark Store HOT Outlet ID\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"Dark Store Name\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"store_ratio\", \"type\": \"real\", \"description\": \"Store Ratios\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "8c56e0f5-5e78-4d87-93ac-f18bd6a2e348", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_store_dist_ptype_ordering\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"city_name\",\n", "        \"festival_name\",\n", "        \"product_type\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ec088ea0-7c47-4327-86cc-206148fabe7e", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'"]}, {"cell_type": "code", "execution_count": null, "id": "392fafa8-437e-48ae-960f-1aa0b3694fee", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1d233ba2-fd75-41de-b90f-fbb97524c7b6", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1cb1ee25-d066-4191-9d91-3c54312db2d4", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "37a38524-ef7e-4a9b-beb6-6fb1bba01967", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9887bc96-0142-4ebf-abcf-97161cb5ff7a", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "a5f5e830-b946-4d64-a75c-a3131c792b4b", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[festive_date_data[\"flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "bcb79a16-314b-46d8-98db-64597705c7fb", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7b50947d-5ae8-48a1-aed4-9ce7502f1c64", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "afd496d7-5328-4827-bdc4-2382bceb2079", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "07679c12-3e88-40b8-9b05-1a9fbf180b32", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        bau_start_date = row[\"bau_start_date\"]\n", "        bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "\n", "        store_dist = f\"\"\"\n", "\n", "        with outlet_details as (\n", "            select \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                    inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "            from supply_etls.outlet_details\n", "            where ars_check=1\n", "                and grocery_active_count >= 1\n", "                and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "\n", "        item_details as (\n", "            select  \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "                and handling_type = 'Non Packaging Material'\n", "        ),\n", "\n", "        be_ptype_outcome as (\n", "            select \n", "                date_,\n", "                festival_name,\n", "                be_facility_id,\n", "                be_name,\n", "                product_type,\n", "                be_ptype_forecast_cpd,\n", "                be_ptype_forecast_planned,\n", "                be_ptype_forecast\n", "            from supply_etls.festival_forecast_be_ptype\n", "            where date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "            and festival_name = '{festival}' \n", "        ),\n", "\n", "        ptype_plan_qty as (\n", "            select \n", "            product_type, festival_name, sum(be_ptype_forecast_cpd) as planned_qty\n", "                from be_ptype_outcome\n", "            group by 1,2\n", "        ),\n", "\n", "        ptype_weights as (\n", "            select \n", "            product_type, festival_name,\n", "            1.00000000*planned_qty/sum(nullif(planned_qty,0)) over () as ptype_weight\n", "            from ptype_plan_qty \n", "        ),\n", "\n", "        festive_details as (\n", "\n", "            select be_facility_id as facility_id, \n", "                item_id, \n", "                festival_name as festival,\n", "                --sale_start_date, \n", "                --sale_end_date, \n", "                --cut_off_date, \n", "                --substitute_ptype, \n", "                --assortment_reason_type,\n", "                --rsto, \n", "                --rtv,\n", "                sum(planned_quantity) as planned_qty\n", "            from ars_etls.final_festival_planning\n", "            where festival_name is not null\n", "            and festival_name = '{festival}' \n", "            group by 1,2,3\n", "\n", "          --select \n", "          --      facility_id, item_id, storage, catg_segment,\n", "          --          festival, festival_bau, sale_start, sale_end, cutt_off, planned_qty \n", "          --  from supply_etls.final_festival_planning\n", "          --  where festival='{festival}'\n", "        ),\n", "\n", "        festive_core as (\n", "            select distinct id.p_type\n", "            from festive_details fd\n", "            inner join item_details id on id.item_id=fd.item_id\n", "        ),\n", "\n", "        festival_ptype_plan_qty as (\n", "            select  \n", "            id.p_type, festival, sum(planned_qty) as planned_qty\n", "            from festive_details fd\n", "            inner join item_details id on id.item_id=fd.item_id\n", "            group by 1,2\n", "\n", "        ),\n", "\n", "        festive_ptype_weights\n", "        as\n", "        (\n", "            select \n", "            p_type, festival,\n", "            1.00000000*planned_qty/sum(nullif(planned_qty,0)) over () as ptype_weight\n", "            from festival_ptype_plan_qty \n", "        ),\n", "\n", "        tea_tagging as (\n", "            select \n", "                fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "                    be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "            from supply_etls.inventory_metrics_tea_tagging\n", "            where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "        ),\n", "\n", "        final1 as (\n", "            select \n", "                outlet_id,\n", "                outlet_name,\n", "                product_type,\n", "                city_name,\n", "                avg_orders,\n", "                val\n", "            from supply_etls.festival_forecast_store_dist_fallback\n", "            where festival_name = '{festival}' \n", "            and product_type in (select distinct product_type from be_ptype_outcome)\n", "\n", "        ),\n", "\n", "\n", "        store_dist as (\n", "            select \n", "                outlet_id, outlet_name, city_name,\n", "                '{festival}' as festival_name,\n", "                avg(avg_orders) as avg_orders,\n", "                avg(round(val, 4)) as avg_val,\n", "                sum(round(val, 4)) as sum_val,\n", "                array_join(array_agg(final1.product_type || ' : ' || cast(coalesce(fpw.ptype_weight,bpw.ptype_weight) as varchar) || ' : ' || cast(round(val, 4) as varchar) order by final1.product_type), ', ') as concatenated_items,\n", "                try(sum(coalesce(fpw.ptype_weight,bpw.ptype_weight)*round(val, 4))/sum(coalesce(fpw.ptype_weight,bpw.ptype_weight))) as weighted_val\n", "            from final1\n", "            left join festive_ptype_weights fpw on fpw.p_type=final1.product_type\n", "            left join ptype_weights bpw on bpw.product_type=final1.product_type\n", "            group by 1,2,3,4\n", "        ),\n", "\n", "        store_dist_fallback as (\n", "            select\n", "                outlet_id, outlet_name, city_name,\n", "                festival_name,\n", "                weighted_val,\n", "                try(weighted_val/sum(weighted_val) over ()) as store_ratio\n", "            from store_dist sd\n", "        ),\n", "\n", "        store_dist_ptype as (\n", "            select\n", "                outlet_id, outlet_name, city_name,\n", "                '{festival}' as festival_name,\n", "                final1.product_type,\n", "                val,\n", "                try(val/sum(val) over (partition by final1.product_type)) as store_ratio\n", "            from final1\n", "            left join festive_ptype_weights fpw on fpw.p_type=final1.product_type\n", "            left join ptype_weights bpw on bpw.product_type=final1.product_type\n", "        ),\n", "\n", "        ptype as (\n", "            select distinct product_type \n", "            from rpc.item_category_details \n", "            where lake_active_record = true\n", "        ),\n", "\n", "        map_fallback as (\n", "            select \n", "                *\n", "            from store_dist_fallback sdf\n", "            cross join ptype\n", "        ),\n", "\n", "        map_actual_ptype as (\n", "            select \n", "                mf.outlet_id, mf.outlet_name, mf.city_name,\n", "                mf.festival_name,\n", "                mf.product_type,\n", "                (case when sdp.product_type is null then mf.store_ratio else sdp.store_ratio end) as store_ratio\n", "            from map_fallback mf\n", "            left join store_dist_ptype sdp on \n", "                mf.outlet_id=sdp.outlet_id\n", "                and mf.festival_name=sdp.festival_name\n", "                and mf.product_type=sdp.product_type\n", "        )\n", "\n", "        select\n", "\n", "        outlet_id, outlet_name, city_name,\n", "        festival_name, product_type,\n", "\n", "        try(store_ratio/sum(store_ratio) over (partition by product_type)) as store_ratio\n", "\n", "        from map_actual_ptype\n", "\n", "\n", "        \"\"\"\n", "\n", "        to_trino(store_dist, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "0b427ff3-db3d-4f39-b713-308d807fc690", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "8eade977-8d45-48a7-81b6-dc5238d71616", "metadata": {}, "outputs": [], "source": ["# df_store_dist = read_sql_query(store_dist, CON_TRINO)\n", "# df_store_dist"]}, {"cell_type": "code", "execution_count": null, "id": "483e1d6f-185f-4ba2-a787-9a73328da376", "metadata": {}, "outputs": [], "source": ["# df_store_dist.head()"]}, {"cell_type": "code", "execution_count": null, "id": "eae03880-cb7b-4bb3-a445-3e3b59cf82a1", "metadata": {}, "outputs": [], "source": ["# df_store_dist.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "320f2640-aa57-4efa-b7e8-622d94d4b370", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_store_dist[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_store_dist.columns\n", "# ]\n", "\n", "# column_dtypes"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}