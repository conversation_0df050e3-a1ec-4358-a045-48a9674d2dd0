{"cells": [{"cell_type": "code", "execution_count": null, "id": "29f4d1ec-3fd8-498a-8e66-3da29488494f", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "73a08478-2597-467f-b92a-348875e0f8f0", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "bed33efc-6032-407c-be05-4ad8595efbc4", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "27dd5197-76bf-4c06-b527-2fded527db98", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d1279fa3-7afe-4040-9acd-968550411c74", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "89f6c33c-53ab-46d7-b1af-1f7de106f921", "metadata": {}, "outputs": [], "source": ["cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "42eba244-760e-4d41-b0b5-12ab1532d7a7", "metadata": {}, "outputs": [], "source": ["bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "festival = festive_date_data[\"festival\"].iloc[-1]\n", "gap_days = festive_date_data[\"gap_days\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "ce9a8aed-809d-412a-82ad-b5d9aa9f1105", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'"]}, {"cell_type": "code", "execution_count": null, "id": "20c1af22-cffd-4542-93db-b4db2901c04c", "metadata": {}, "outputs": [], "source": ["sale_inv_grn = f\"\"\"\n", "\n", "with outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "            inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "    from supply_etls.outlet_details\n", "    where ars_check=1\n", "        and grocery_active_count >= 1\n", "        and (store_type in ('Packaged Goods', 'Dark Store'))\n", "),\n", "\n", "item_details as (\n", "    select \n", "        item_id, item_name, l0_id, l0_category, \n", "                l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "    from supply_etls.item_details\n", "    where assortment_type='Packaged Goods'\n", "        and handling_type = 'Non Packaging Material'\n", "),\n", "\n", "tea_tagging as (\n", "    select  \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "festive_details as (\n", "\n", "    select be_facility_id as facility_id, \n", "        item_id, \n", "        festival_name as festival,\n", "        --sale_start_date, \n", "        --sale_end_date, \n", "        --cut_off_date, \n", "        --substitute_ptype, \n", "        --assortment_reason_type,\n", "        --rsto, \n", "        --rtv,\n", "        sum(planned_quantity) as planned_qty\n", "    from ars_etls.final_festival_planning\n", "    where festival_name is not null\n", "    and festival_name = '{festival}' \n", "    group by 1,2,3\n", "\n", "    --select \n", "    --    facility_id, p.item_id, storage, catg_segment,\n", "    --        festival, festival_bau, sale_start, sale_end, cutt_off, planned_qty \n", "    --from supply_etls.final_festival_planning p\n", "    --where festival='{festival}'\n", "),\n", "\n", "potential_sales as (\n", "    select insert_ds_ist , date(order_date) order_date_,\n", "    od.city_id, od.city_name,\n", "    doa.facility_id, inv_outlet_id, doa.item_id, \n", "    sum(doa.order_quantity) sold_qty,\n", "    sum(doa.potential_order_quantity) potential_sold_qty\n", "    from \n", "    ars.daily_orders_and_availability doa \n", "    left join outlet_details od on doa.facility_id = od.facility_id \n", "    where insert_ds_ist between '{sale_start_date}' and '{sale_end_date}'\n", "    and doa.lake_active_record = true\n", "    and doa.item_id in (select distinct item_id from festive_details)\n", "    group by 1,2,3,4,5,6,7\n", "),\n", "\n", "grn as (\n", "    select \n", "    date(isd.created_at + interval '330' minute) created_date, \n", "    extract(hour from (isd.created_at+ interval '330' minute)) created_hr,\n", "    od.city_id, od.city_name,\n", "    isd.outlet_id, \n", "    rpc.item_id, \n", "    sum(isd.delta) grn_qty\n", "    from ims.ims_inventory_stock_details isd\n", "    left join (select distinct upc, item_id from rpc.product_product where lake_active_record = true) rpc on isd.upc_id = rpc.upc\n", "    left join outlet_details od on isd.outlet_id = od.inv_outlet_id\n", "    where isd.lake_active_record = true\n", "    and insert_ds_ist between ('{sale_start_date}') and ('{sale_end_date}')\n", "    and date(isd.created_at) between date('{sale_start_date}') and date('{sale_end_date}')\n", "    and rpc.item_id in (select distinct item_id from festive_details)\n", "    group by 1,2,3,4,5,6\n", "),\n", "\n", "sales as (\n", "    select od.order_create_dt_ist date_,  extract(hour from order_create_ts_ist) hr,\n", "           od.outlet_id, ipm.item_id, \n", "           sum(procured_quantity * multiplier) as qty_sold, \n", "           sum(total_procurement_price) as gmv\n", "from dwh.fact_sales_order_item_details od\n", "inner join\n", "     (select distinct ipr.product_id,\n", "                      case\n", "                          when ipr.item_id IS NULL THEN ipom_0.item_id\n", "                          else ipr.item_id\n", "                      end AS item_id,\n", "                      case \n", "                          when ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "                          else COALESCE(ipom_0.multiplier,1)\n", "                      end AS multiplier\n", "      from lake_rpc.item_product_mapping ipr\n", "      left join dwh.dim_item_product_offer_mapping ipom ON ipr.product_id = ipom.product_id\n", "      AND ipr.item_id = ipom.item_id\n", "      left join dwh.dim_item_product_offer_mapping ipom_0 ON ipr.product_id = ipom_0.product_id) ipm ON od.product_id = ipm.product_id\n", "      inner join rpc.item_category_details icd ON icd.item_id = ipm.item_id\n", "    where od.order_current_status = 'DELIVERED'\n", "     and od.ORDER_TYPE NOT IN ('InternalForwardOrder',\n", "                               'InternalReverseOrder',\n", "                               'DropShippingInternalReverseOrder')\n", "     and od.order_create_dt_ist between date('{sale_start_date}') and date('{sale_end_date}')\n", "     and od.total_selling_price > 0\n", "     and icd.lake_active_record\n", "     group by 1,2,3,4\n", "),\n", "\n", "base_inventory as (\n", "    select item_id, outlet_id, sum(case when actual_quantity - blocked_quantity > 0 then actual_quantity - blocked_quantity else 0 end) as net_inv\n", "    from (\n", "        select iii.item_id, iii.outlet_id as outlet_id, iii.quantity as actual_quantity, coalesce(sum(iibi.quantity),0) as blocked_quantity\n", "        from ims.ims_item_inventory iii\n", "        left join ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id and iibi.quantity > 0  and iibi.active = 1 and iibi.lake_active_record\n", "        where iii.active = 1 and iii.lake_active_record \n", "        group by 1,2,3\n", "    )\n", "    group by 1,2\n", "),\n", "\n", "inventory as (\n", "    select \n", "    date(his.date_ist) date_, hour(his.updated_at_ist) hr , \n", "    his.outlet_id, his.facility_id, his.item_id, max(his.current_inventory) as current_inventory\n", "    from \n", "    supply_etls.hourly_inventory_snapshots his\n", "    where date_ist between date('{sale_start_date}') and date('{sale_end_date}')\n", "    and his.item_id in (select distinct item_id from festive_details)\n", "    and substate_id in (1,3)\n", "    group by 1,2,3,4,5\n", "),\n", "\n", "base_outlet_item as (\n", "    select * from \n", "    ((select distinct outlet_id, item_id from base_inventory) cross join (select distinct date_, hr from sales))\n", "    where item_id in (select distinct item_id from festive_details)\n", ")\n", "\n", "select boi.date_, boi.hr as hour_, \n", "    od.city_id, \n", "    od.city_name,\n", "    '{festival}' as festival_name,\n", "    boi.outlet_id as fe_outlet_id, \n", "    od.facility_id as fe_facility_id,\n", "    boi.item_id, \n", "    'NA' as product_type,\n", "    i.current_inventory, \n", "    coalesce(s.qty_sold,0) qty_sold , \n", "    coalesce(s.gmv,0) gmv,\n", "    coalesce(g.grn_qty,0) grn_qty,\n", "    coalesce(ps.potential_sold_qty,0) potential_sold_qty\n", "from \n", "base_outlet_item boi \n", "left join inventory i on boi.date_ = i.date_ and boi.hr = i.hr and boi.outlet_id = i.outlet_id and boi.item_id = i.item_id\n", "left join sales s on boi.date_ = s.date_ and boi.hr = s.hr and boi.outlet_id = s.outlet_id and boi.item_id = s.item_id\n", "left join grn g on boi.date_ = g.created_date and boi.hr = g.created_hr and boi.outlet_id = g.outlet_id and boi.item_id = g.item_id\n", "left join potential_sales ps on boi.date_ = ps.order_date_ and boi.outlet_id = ps.inv_outlet_id and boi.item_id = ps.item_id\n", "left join outlet_details od on boi.outlet_id = od.inv_outlet_id\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "91c1fb3a-a0ff-4b4c-9341-0a8643adb972", "metadata": {}, "outputs": [], "source": ["# df_sale_inv_grn = read_sql_query(sale_inv_grn, CON_TRINO)\n", "# df_sale_inv_grn"]}, {"cell_type": "code", "execution_count": null, "id": "5b7bb27a-4b55-459d-91dd-1e8b560a62a8", "metadata": {}, "outputs": [], "source": ["# df_sale_inv_grn.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bfc09fa2-b143-401a-b89a-ca694bae95b1", "metadata": {}, "outputs": [], "source": ["# df_sale_inv_grn.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "aa556500-19e4-4716-aeae-33128bb6a996", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_sale_inv_grn[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_sale_inv_grn.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "38f4d322-d7d2-4423-adf1-81e870e2ff47", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"Sale Hour\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"City ID\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"fe_outlet_id\", \"type\": \"integer\", \"description\": \"Dark Store Outlet ID\"},\n", "    {\n", "        \"name\": \"fe_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store Facility ID\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"real\", \"description\": \"Current Inventory\"},\n", "    {\"name\": \"grn_qty\", \"type\": \"real\", \"description\": \"GRN Qty\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"GMV\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"QTY sold\"},\n", "    {\n", "        \"name\": \"potential_sold_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Day Potential QTY Sold\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f9146889-4485-494e-b088-900ec4a22ec8", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_sale_inv_grn\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"hour_\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"fe_outlet_id\",\n", "        \"fe_facility_id\",\n", "        \"item_id\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\", \"date_\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}\n", "\n", "to_trino(sale_inv_grn, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}