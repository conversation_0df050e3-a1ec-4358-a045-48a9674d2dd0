alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: forecast_inventory_risk
dag_type: etl
escalation_priority: low
execution_timeout: 4000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: 1_be_ptype_forecast-loop
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 1_city_ptype_forecast-loop-current_year
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 1_city_ptype_forecast-loop
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_4_day_ptype_weights
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_1_store_distribution_fallback
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 1_1_be_ptype_forecast_flag
  parameters: null
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: 1_1_city_ptype_forecast_flag
  parameters: null
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: 3_0_price_bucketing
  parameters: null
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: 3_2_cart_growth
  parameters: null
  tag: third
- executor_config:
    load_type: ultra-high-mem
    node_type: spot
  name: 3_1_in_and_out_forecast
  parameters: null
  tag: third
- executor_config:
    load_type: low
    node_type: spot
  name: 3_3_intent_growth
  parameters: null
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_store_distribution
  parameters: null
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: 3_item_weights
  parameters: null
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: 4_hour_weights
  parameters: null
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_2_store_distribution_ptype
  parameters: null
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_3_store_distribution_ptype_ordering
  parameters: null
  tag: third
- executor_config:
    load_type: tiny
    node_type: spot
  name: 5_1_final_city_ptype_forecast
  parameters: null
  tag: fourth
owner:
  email: <EMAIL>
  slack_id: U078HMPLR7H
path: povms/festive_forecast/etl/forecast_inventory_risk
paused: false
pool: povms_pool
project_name: festive_forecast
schedule:
  end_date: '2025-07-05T00:00:00'
  interval: 35 2-16/6 * * *
  start_date: '2025-06-06T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 146
