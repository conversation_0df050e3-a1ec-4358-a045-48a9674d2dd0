{"cells": [{"cell_type": "code", "execution_count": null, "id": "6fe4ab0f-5a71-49e0-9cab-7c94aa5114d4", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import math\n", "import time\n", "from pytz import timezone\n", "from datetime import date, datetime, timedelta\n", "\n", "from itertools import product\n", "\n", "import random\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "\n", "# pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc5fd-52a5-462b-b94f-a2bbe5f0cbde", "metadata": {}, "outputs": [], "source": ["# from Schema import ptype_festival_cart_growth_schema"]}, {"cell_type": "code", "execution_count": null, "id": "fbf37e20-a91b-41c2-9c19-0a1fbd7b148c", "metadata": {}, "outputs": [], "source": ["ptype_festival_cart_growth_schema = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_cart_growth\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"date\",\n", "            \"type\": \"date\",\n", "            \"description\": \"date\",\n", "        },\n", "        {\n", "            \"name\": \"city\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"city\",\n", "        },\n", "        {\n", "            \"name\": \"orders\",\n", "            \"type\": \"double\",\n", "            \"description\": \"No. of Orders\",\n", "        },\n", "        {\n", "            \"name\": \"rolling_orders\",\n", "            \"type\": \"double\",\n", "            \"description\": \"Rolling orders (Preceding 7 days)\",\n", "        },\n", "        {\n", "            \"name\": \"snapshot_dt_ist\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"date of creation of record of the DB\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\"date\", \"city\"],\n", "    \"sortkey\": [],\n", "    \"load_type\": \"upsert\",  # append, truncate, upsert, overwrite\n", "    \"table_description\": \"Cart Growth - Previous & Projected\",\n", "    \"partition_key\": [\"city\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "cb317870-7cc0-41ae-bfd2-ddbda27ee016", "metadata": {}, "outputs": [], "source": ["today_date = pd.to_datetime(date.today()).strftime(\"%Y-%m-%d\")\n", "today_date\n", "\n", "snapshot_dt_ist = today_date\n", "snapshot_dt_ist"]}, {"cell_type": "code", "execution_count": null, "id": "a05375af-1428-4588-9f2c-183f8b32bfb7", "metadata": {}, "outputs": [], "source": ["def push_to_table(query, snapshot_dt_ist):\n", "\n", "    # Creating interim table for FE FUNNEL/SEARCHERS devices\n", "    try:\n", "        msg = f\"Creating Cart Growth Table in Trino\"\n", "        print(msg)\n", "\n", "        _trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "        _trino_conn = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "        print(\"Executing Query\")\n", "        pb.to_trino(\n", "            query.format(snapshot_dt_ist=snapshot_dt_ist),\n", "            **ptype_festival_cart_growth_schema,\n", "        )\n", "\n", "        del _trino_con, _trino_conn\n", "        print(\"Done\")\n", "\n", "    except Exception as e:\n", "        msg = f\"Failed to push Cart Growth data to interim DB due to : {e}\"\n", "        print(msg)\n", "        raise Exception(msg)"]}, {"cell_type": "code", "execution_count": null, "id": "94059ea3-f2df-48da-a500-890ebe149667", "metadata": {}, "outputs": [], "source": ["end_date = (pd.to_datetime(today_date) - <PERSON><PERSON><PERSON>(days=2)).strftime(\"%Y-%m-%d\")\n", "start_date = (pd.to_datetime(end_date) - timed<PERSON><PERSON>(days=15)).strftime(\"%Y-%m-%d\")\n", "start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "id": "edaa4303-2c29-4aeb-ab80-c3c3e38a10ed", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8a14dee8-88a4-46d8-b3c9-868121b356be", "metadata": {}, "outputs": [], "source": ["sql_query_cart_growth = f\"\"\"\n", "with base as (\n", "\n", "select date_ as date,\n", "    city,\n", "    orders,\n", "    avg(orders) over (partition by city order by date_ rows between 7 preceding and current row) as rolling_orders,\n", "    '{snapshot_dt_ist}' as snapshot_dt_ist\n", "    \n", "from (\n", "    select \n", "        date(cart_checkout_ts_ist) date_, \n", "        city_name as city,\n", "        count(distinct order_id) as orders\n", "\n", "    from dwh.fact_sales_order_details\n", "    where \n", "        order_create_dt_ist >=  current_date-interval'40'day\n", "        and order_create_dt_ist < current_date\n", "        \n", "        and order_create_ts_ist >= current_date-interval'40'day\n", "        and order_create_ts_ist < current_date\n", "        \n", "        and cart_checkout_ts_ist >= current_date-interval'40'day\n", "        and cart_checkout_ts_ist < current_date\n", "        \n", "        and order_current_status = 'DELIVERED'\n", "        and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "    group by 1, 2\n", "    \n", "    union all\n", "    \n", "     select \n", "         \"date\" as date_,\n", "         (case when co_map.city_name in ('Gurgaon') then 'HR-NCR'\n", "         when co_map.city_name in ('Ghaziabad') then 'UP-NCR'\n", "         else co_map.city_name end) as city,\n", "         sum(carts) as orders\n", "     from logistics_data_etls.cart_projections cp\n", "     inner join \n", "         (select max(updated_on) as ts\n", "         from logistics_data_etls.cart_projections) tst on cp.updated_on = tst.ts\n", "    inner join \n", "        (select distinct outlet_id, cl.name as city_name\n", "        from po.physical_facility_outlet_mapping pfom\n", "        inner join retail.console_location cl on pfom.city_id = cl.id) co_map on co_map.outlet_id = cp.outletid\n", "     where \"date\" between (current_date) and (current_date + interval'180'day)\n", "     group by 1, 2 \n", " )\n", ")\n", "\n", "select * from base where date >= current_date-interval'30'day\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3074d97a-39ce-48cc-8d5c-4c7b41143d1a", "metadata": {}, "outputs": [], "source": ["sql_query_cart_growth_pan_india = f\"\"\"\n", "with base as (\n", "\n", "select date_ as date,\n", "    'PAN INDIA' as city,\n", "    orders,\n", "    avg(orders) over (order by date_ rows between 7 preceding and current row) as rolling_orders,\n", "    '{snapshot_dt_ist}' as snapshot_dt_ist\n", "    \n", "from (\n", "    select \n", "        date(cart_checkout_ts_ist) date_, \n", "        count(distinct order_id) as orders\n", "\n", "    from dwh.fact_sales_order_details\n", "    where \n", "        order_create_dt_ist >=  current_date-interval'40'day\n", "        and order_create_dt_ist < current_date\n", "        \n", "        and order_create_ts_ist >= current_date-interval'40'day\n", "        and order_create_ts_ist < current_date\n", "        \n", "        and cart_checkout_ts_ist >= current_date-interval'40'day\n", "        and cart_checkout_ts_ist < current_date\n", "        \n", "        and order_current_status = 'DELIVERED'\n", "        and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "    group by 1\n", "    \n", "    union all\n", "    \n", "     select \n", "         \"date\" as date_,\n", "         sum(carts) as orders\n", "     from logistics_data_etls.cart_projections cp\n", "     inner join \n", "         (select max(updated_on) as ts\n", "         from logistics_data_etls.cart_projections) tst on cp.updated_on = tst.ts\n", "     where \"date\" between (current_date) and (current_date + interval'180'day)\n", "     group by 1\n", " )\n", ")\n", "\n", "select * from base where date >= current_date-interval'30'day\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "10faf97b-4cda-4f74-bb7c-a2f196a3b2cc", "metadata": {}, "outputs": [], "source": ["# print(sql_query_cart_growth)"]}, {"cell_type": "code", "execution_count": null, "id": "bf0a4a7d-423a-4cac-90aa-ca55ee14340b", "metadata": {}, "outputs": [], "source": ["try:\n", "    msg = f\"Pushing to Table\"\n", "    print(msg)\n", "\n", "    push_to_table(sql_query_cart_growth, snapshot_dt_ist)\n", "\n", "    print(f\"Pushed Cart Growth to Table\")\n", "\n", "    push_to_table(sql_query_cart_growth_pan_india, snapshot_dt_ist)\n", "\n", "    print(f\"Pushed PAN INDIA Cart Growth to Table\")\n", "\n", "except Exception as E:\n", "    msg = f\"Couldn't push to Trino Table, exception has occured with error message = {E}\"\n", "    print(msg)"]}, {"cell_type": "code", "execution_count": null, "id": "ad6ffcc3-5df3-4e48-a6f3-9658532385b7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}