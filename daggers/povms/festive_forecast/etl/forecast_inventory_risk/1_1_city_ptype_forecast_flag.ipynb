{"cells": [{"cell_type": "code", "execution_count": null, "id": "c444a6d5-18f4-4d0b-b89c-2d75bc77104d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4775dcdf-3b7a-40ec-a513-cd3d8b53e153", "metadata": {"tags": []}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b7962273-b8ba-4e38-b2ef-1cc1bcd174ad", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"festival_tech_flag\", \"type\": \"integer\", \"description\": \"Festival Tech Flag\"},\n", "    {\n", "        \"name\": \"festival_tech_flag_cpd_contribution\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Festival Tech Flag CPD Contribution\",\n", "    },\n", "    {\n", "        \"name\": \"festival_tech_flag_spike\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Festival Tech Flag Avg Spike\",\n", "    },\n", "    {\n", "        \"name\": \"festival_tech_flag_day_spike\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Festival Tech Flag Day Spike\",\n", "    },\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"cpd\", \"type\": \"real\", \"description\": \"Current APS\"},\n", "    {\"name\": \"potential_sales\", \"type\": \"real\", \"description\": \"Current Potential Sales\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"QTY sold in last festival\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"GMV in last festival\"},\n", "    {\"name\": \"asp\", \"type\": \"real\", \"description\": \"ASP in last festival\"},\n", "    {\n", "        \"name\": \"city_ptype_forecast_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis CPD\",\n", "    },\n", "    {\n", "        \"name\": \"city_ptype_forecast_aps\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis Potential Sales\",\n", "    },\n", "    {\"name\": \"cpd_contribution\", \"type\": \"real\", \"description\": \"CPD Contribution\"},\n", "    {\"name\": \"max_spike\", \"type\": \"real\", \"description\": \"Max Spike\"},\n", "    {\"name\": \"avg_spike\", \"type\": \"real\", \"description\": \"Avg Spike\"},\n", "    {\"name\": \"spike\", \"type\": \"real\", \"description\": \"Spike\"},\n", "    {\"name\": \"actual_potential_sales\", \"type\": \"real\", \"description\": \"Current Potential Sales\"},\n", "    {\"name\": \"actual_cpd\", \"type\": \"real\", \"description\": \"Actual current APS\"},\n", "    {\n", "        \"name\": \"search_device_count_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Searches (Festival)\",\n", "    },\n", "    {\"name\": \"total_demand_festival\", \"type\": \"real\", \"description\": \"Total Demand (Festival)\"},\n", "    {\"name\": \"search_device_count_bau\", \"type\": \"real\", \"description\": \"Total Searches (BAU)\"},\n", "    {\"name\": \"total_demand_bau\", \"type\": \"real\", \"description\": \"Total Demand (BAU)\"},\n", "    {\n", "        \"name\": \"search_contri_atc_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution in ATC (Festival)\",\n", "    },\n", "    {\"name\": \"search_atcs_festival\", \"type\": \"real\", \"description\": \"Search ATC (Festival)\"},\n", "    {\n", "        \"name\": \"non_search_atcs_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Non-Search ATC (Festival)\",\n", "    },\n", "    {\"name\": \"search_atcs_bau\", \"type\": \"real\", \"description\": \"Search ATC (BAU)\"},\n", "    {\"name\": \"non_search_atcs_bau\", \"type\": \"real\", \"description\": \"Non-Search ATC (BAU)\"},\n", "    {\"name\": \"total_atc_festival\", \"type\": \"real\", \"description\": \"Total ATC (Festival)\"},\n", "    {\n", "        \"name\": \"search_contri_atc_bau\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution in ATC (BAU)\",\n", "    },\n", "    {\"name\": \"total_atc_bau\", \"type\": \"real\", \"description\": \"Total ATC (BAU)\"},\n", "    {\"name\": \"search_spike\", \"type\": \"real\", \"description\": \"Search Spike\"},\n", "    {\"name\": \"demand_spike\", \"type\": \"real\", \"description\": \"Demand Spike\"},\n", "    {\n", "        \"name\": \"search_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Conversion (festival)\",\n", "    },\n", "    {\n", "        \"name\": \"demand_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Demand Conversion (festival)\",\n", "    },\n", "    {\"name\": \"search_conversion_bau\", \"type\": \"real\", \"description\": \"Search Conversion (BAU)\"},\n", "    {\"name\": \"demand_conversion_bau\", \"type\": \"real\", \"description\": \"Demand Conversion (BAU)\"},\n", "    {\"name\": \"ptype_carts\", \"type\": \"real\", \"description\": \"Carts of that Ptype\"},\n", "    {\"name\": \"overall_carts\", \"type\": \"real\", \"description\": \"All carts\"},\n", "    {\n", "        \"name\": \"ipc_ptype_carts\",\n", "        \"type\": \"real\",\n", "        \"description\": \"IPC considering carts of that ptype\",\n", "    },\n", "    {\"name\": \"ipc_overall_carts\", \"type\": \"real\", \"description\": \"IPC considering all carts\"},\n", "    {\"name\": \"cart_pen\", \"type\": \"real\", \"description\": \"Cart Pen\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ec78ac0-465d-4cfe-996f-1bc60d2d3970", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_ptype_flag\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city\",\n", "        \"product_type\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5b69eeae-b52f-48e2-9c26-fb19765d65b2", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'\n", "# # commented"]}, {"cell_type": "code", "execution_count": null, "id": "3c4fdf56-10da-4ee5-a5bc-ca86eaf45bf3", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b0c02292-6c1b-46e4-9295-3fdf4f8a0a2d", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "18cb652a-f2a1-47e1-bfd6-9a6e9272a5ca", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "70167d71-cf6f-4b78-a828-1063938fe408", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2adf1ae0-b52f-4647-9c67-b7352e2bc9b6", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "3834e532-7cd5-42e2-8900-299066cfae17", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[festive_date_data[\"flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "ea129943-d247-4fd1-9c45-bd91ad10400f", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "807ea161-f66f-4ede-bed9-c5631590f8b3", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "1cac9338-7bff-477b-b479-c01e40faaa3e", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "c26ced28-e03d-4302-b810-dd6a5e66b2cd", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        bau_start_date = row[\"bau_start_date\"]\n", "        bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "\n", "        be_ptype = f\"\"\" \n", "\n", "        with final_base as (\n", "            select \n", "                date_,\n", "                festival_name,\n", "                city,\n", "                product_type,\n", "                cpd,\n", "                potential_sales,\n", "                city_ptype_forecast_cpd,\n", "                city_ptype_forecast_aps,\n", "                actual_potential_sales,\n", "                actual_cpd,\n", "                search_device_count_festival,\n", "                total_demand_festival,\n", "                search_device_count_bau,\n", "                total_demand_bau,\n", "                search_contri_atc_festival,\n", "                search_atcs_festival,\n", "                non_search_atcs_festival,\n", "                search_atcs_bau,\n", "                non_search_atcs_bau,\n", "                total_atc_festival,\n", "                search_contri_atc_bau,\n", "                total_atc_bau,\n", "                search_spike,\n", "                demand_spike,\n", "                search_conversion_festival,\n", "                demand_conversion_festival,\n", "                search_conversion_bau,\n", "                demand_conversion_bau\n", "            from supply_etls.festival_forecast_city_ptype\n", "            where date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "            and festival_name = '{festival}'\n", "        ),\n", "\n", "        outlet_details as (\n", "            select \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                    inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "            from supply_etls.outlet_details\n", "            where ars_check=1\n", "            and grocery_active_count >= 1\n", "            and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "\n", "        item_details as (\n", "            select \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where handling_type = 'Non Packaging Material'\n", "            --and assortment_type='Packaged Goods'\n", "        ),\n", "\n", "        item_product_mapping as (\n", "            select item_id, product_id, multiplier\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current\n", "        ),\n", "\n", "        sales_overall as(\n", "            select\n", "                order_create_dt_ist as date_,\n", "                od.city_name,\n", "                count(distinct cart_id) as overall_carts\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            where fs.order_create_dt_ist between date('{festival_start_date}') and date('{festival_end_date}')\n", "                and fs.order_current_status = 'DELIVERED'\n", "            group by 1,2\n", "        ),\n", "\n", "        sales as (\n", "            select \n", "                    order_create_dt_ist + interval '{gap_days}' day as date_, \n", "                    od.city_name, id.p_type,\n", "                    sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price) as gmv,\n", "                    count(distinct cart_id) as ptype_carts,\n", "                    max(overall_carts) as overall_carts,\n", "                    cast(sum(procured_quantity * multiplier) as decimal (10,4)) / count(distinct cart_id) as ipc_ptype_carts,\n", "                    cast(sum(procured_quantity * multiplier) as decimal(10,4)) / max(overall_carts) as ipc_overall_carts,\n", "                    count(distinct cart_id) / max(overall_carts) as cart_pen\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            inner join item_details id on id.item_id=ip.item_id\n", "            left join sales_overall so on so.date_ = fs.order_create_dt_ist\n", "                and so.city_name = od.city_name\n", "            where fs.order_create_dt_ist between date('{festival_start_date}') and date('{festival_end_date}')\n", "            and fs.order_current_status = 'DELIVERED'\n", "            group by 1,2,3\n", "        ),\n", "\n", "        flag_festive_cart_pen as ( --0.02perc cart pen\n", "            SELECT\n", "                distinct product_type, try(1.0000*a.city_ptype_forecast_cpd/b.total_cpd) as cpd_contribution\n", "            FROM\n", "                (SELECT \n", "                    product_type, sum(cpd) as cpd,\n", "                    sum(city_ptype_forecast_cpd) AS city_ptype_forecast_cpd\n", "                FROM final_base\n", "                group by 1) a\n", "\n", "                CROSS JOIN\n", "\n", "                (SELECT \n", "                    sum(city_ptype_forecast_cpd) AS total_cpd\n", "                FROM final_base) b \n", "\n", "                WHERE try(1.0000*a.city_ptype_forecast_cpd/b.total_cpd)>0.0002 \n", "        ),\n", "\n", "        flag_festive_spike_day as ( --1.25x spike on any day\n", "            select \n", "                product_type, max(try(1.0000*city_ptype_forecast_cpd/cpd)) as max_spike\n", "            FROM final_base\n", "            where city_ptype_forecast_cpd>1.25*cpd  \n", "            group by 1\n", "        ),\n", "\n", "        flag_festive_spike as ( --1.25x spike across festival\n", "            select \n", "                distinct product_type, try(1.0000*city_ptype_forecast_cpd/cpd) as spike\n", "            from \n", "                (SELECT product_type, sum(cpd) as cpd,\n", "                        sum(city_ptype_forecast_cpd) AS city_ptype_forecast_cpd\n", "                    FROM final_base\n", "                    group by 1\n", "                    having sum(city_ptype_forecast_cpd)>1.25*sum(cpd)\n", "                )\n", "        )\n", "\n", "\n", "        SELECT \n", "\n", "            fb.date_,\n", "            festival_name,\n", "            (case when ffcp.product_type is not null and ffs.product_type is not null and ffsd.product_type is not null then 1 else 0 end) as festival_tech_flag,\n", "            (case when ffcp.product_type is not null then 1 else 0 end) as festival_tech_flag_cpd_contribution,\n", "            (case when ffs.product_type is not null then 1 else 0 end) as festival_tech_flag_spike,\n", "            (case when ffsd.product_type is not null then 1 else 0 end) as festival_tech_flag_day_spike,\n", "            city,\n", "            fb.product_type,\n", "            cpd,\n", "            potential_sales,\n", "            qty_sold,\n", "            gmv,\n", "            ptype_carts,\n", "            overall_carts,\n", "            ipc_ptype_carts,\n", "            ipc_overall_carts,\n", "            cart_pen,\n", "            try(1.0000*gmv/qty_sold) as asp,\n", "            city_ptype_forecast_cpd,\n", "            city_ptype_forecast_aps,\n", "            ffcp.cpd_contribution,\n", "            ffsd.max_spike,\n", "            ffs.spike as avg_spike,\n", "            try(1.0000*city_ptype_forecast_cpd/cpd) as spike,\n", "            fb.actual_potential_sales,\n", "            fb.actual_cpd,\n", "            fb.search_device_count_festival,\n", "            fb.total_demand_festival,\n", "            fb.search_device_count_bau,\n", "            fb.total_demand_bau,\n", "            fb.search_contri_atc_festival,\n", "            fb.search_atcs_festival,\n", "            fb.non_search_atcs_festival,\n", "            fb.search_atcs_bau,\n", "            fb.non_search_atcs_bau,\n", "            fb.total_atc_festival,\n", "            fb.search_contri_atc_bau,\n", "            fb.total_atc_bau,\n", "            fb.search_spike,\n", "            fb.demand_spike,\n", "            fb.search_conversion_festival,\n", "            fb.demand_conversion_festival,\n", "            fb.search_conversion_bau,\n", "            fb.demand_conversion_bau\n", "\n", "        from final_base fb\n", "        left join sales s on s.date_=fb.date_ and s.city_name=fb.city and s.p_type=fb.product_type\n", "        left join flag_festive_cart_pen ffcp on ffcp.product_type=fb.product_type\n", "        left join flag_festive_spike ffs on ffs.product_type=fb.product_type\n", "        left join flag_festive_spike_day ffsd on ffsd.product_type=fb.product_type\n", "\n", "\n", "        \"\"\"\n", "\n", "        to_trino(be_ptype, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "6a67af73-4f39-4e6c-8be4-ac9b7be65f01", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b66d084f-319e-4477-ac0f-29eaac5f33de", "metadata": {}, "outputs": [], "source": ["# df_be_ptype = read_sql_query(be_ptype, CON_TRINO)\n", "# df_be_ptype\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "de630dd3-e554-47d6-86e9-e351925a6993", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.head()\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "d3cbe65b-4fcc-4f4b-bfd6-c8384cdf4c31", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.dtypes\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "9de672d0-fe2f-4623-8b6c-608a5a9009d1", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_be_ptype[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_be_ptype.columns\n", "# ]\n", "\n", "# column_dtypes"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}