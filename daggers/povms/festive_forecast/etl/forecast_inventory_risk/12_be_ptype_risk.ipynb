{"cells": [{"cell_type": "code", "execution_count": null, "id": "7ec760a0-8f48-4b77-b439-1981b71e5be9", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "21a3d196-aa79-4132-a0ed-eb66cd084d86", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "613505e1-f6b5-466f-b93a-352e096bbbef", "metadata": {}, "outputs": [], "source": ["# df = pb.from_sheets(\n", "#     \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "55759308-40be-4584-af18-393c9253d128", "metadata": {}, "outputs": [], "source": ["# festive_date_data = df[\n", "#     [\n", "#         \"festival\",\n", "#         \"cpd_date\",\n", "#         \"bau_start_date\",\n", "#         \"bau_end_date\",\n", "#         \"festival_start_date\",\n", "#         \"festival_end_date\",\n", "#         \"sale_start_date\",\n", "#         \"sale_end_date\",\n", "#         \"gap_days\",\n", "#     ]\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "id": "2a749a25-d05b-4973-9bfb-bdf8089e56df", "metadata": {}, "outputs": [], "source": ["# cpd_date = festive_date_data[\"cpd_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "f8196b7a-f5d3-47e5-9b51-c6281ead9725", "metadata": {}, "outputs": [], "source": ["risk = f\"\"\" \n", "with final as (\n", "    select \n", "        date_,\n", "        festival_name,\n", "        product_type,\n", "        be_facility_id,\n", "        be_name,\n", "        sum(start_inv) as start_inv,\n", "        sum(left_over_inv) as left_over_inv,\n", "        sum(day_grn_qty) as day_grn_qty,\n", "        sum(day_qty_sold) as day_qty_sold,\n", "        sum(day_forecast_cpd) as day_forecast_cpd,\n", "        sum(day_forecast_planned) as day_forecast_planned,\n", "        sum(day_risk_cpd) as day_risk_cpd,\n", "        sum(day_risk_planned) as day_risk_planned,\n", "        sum(day_gmv_loss_cpd) as day_gmv_loss_cpd,\n", "        sum(day_gmv_loss_planned) as day_gmv_loss_planned,\n", "        sum(total_grn_post_start) as total_grn_post_start,\n", "        sum(total_sales_post_start) as total_sales_post_start,\n", "        sum(total_forecast_cpd) as total_forecast_cpd,\n", "        sum(total_forecast_planned) as total_forecast_planned,\n", "        sum(total_gmv_loss_cpd) as total_gmv_loss_cpd,\n", "        sum(total_gmv_loss_planned) as total_gmv_loss_planned,\n", "        sum(total_upcoming_forecast_cpd) as total_upcoming_forecast_cpd,\n", "        sum(total_upcoming_forecast_planned) as total_upcoming_forecast_planned,\n", "        sum(total_expected_upcoming_sales_cpd) as total_expected_upcoming_sales_cpd,\n", "        sum(total_expected_upcoming_sales_planned) as total_expected_upcoming_sales_planned\n", "    from supply_etls.festival_forecast_inventory_risk_store_item\n", "    where festival_name is not null\n", "    group by 1,2,3,4,5\n", "),\n", "\n", "gmv as (\n", "    select\n", "    be_facility_id, product_type, festival_name,\n", "    sum(avg_item_asp*qty_sold)/sum(qty_sold) as avg_item_asp\n", "    from\n", "    (select \n", "        be_facility_id, product_type, item_id, festival_name, sum(coalesce(qty_sold,0)) as qty_sold,\n", "        coalesce((sum(coalesce(gmv,0))*1.00/sum(coalesce(qty_sold,0))),0) as avg_item_asp\n", "    from supply_etls.festival_forecast_store_item g\n", "    where qty_sold > 0 \n", "    and festival_name is not null\n", "    group by 1,2,3,4)\n", "    group by 1,2,3\n", "),\n", "\n", "risk_final as (\n", "    select \n", "        *, \n", "        \n", "        least((total_forecast_cpd -  total_sales_post_start),left_over_inv) as absolute_risk_cpd,\n", "        least((total_forecast_planned -  total_sales_post_start),left_over_inv) as absolute_risk_planned,\n", "        \n", "        left_over_inv - coalesce(total_expected_upcoming_sales_cpd,0) as adjusted_risk_cpd,\n", "        left_over_inv - coalesce(total_expected_upcoming_sales_planned,0) as adjusted_risk_planned,\n", "        \n", "        left_over_inv - coalesce(total_upcoming_forecast_cpd,0) as min_risk_cpd,\n", "        left_over_inv - coalesce(total_upcoming_forecast_planned,0) as min_risk_planned\n", "        \n", "    from \n", "    (select\n", "    m.*,\n", "    x.avg_item_asp\n", "    from\n", "    final m\n", "    left join gmv x on m.be_facility_id = x.be_facility_id and m.product_type = x.product_type  and m.festival_name=x.festival_name)\n", "),\n", "\n", "score_base as (\n", "    select \n", "        date_,\n", "        \n", "        sum(case when adjusted_risk_cpd > 0 then adjusted_risk_cpd else 0 end) as  total_adjusted_risk_cpd,\n", "        sum(case when adjusted_risk_planned > 0 then adjusted_risk_planned else 0 end) as total_adjusted_risk_planned,\n", "        sum(case when min_risk_cpd > 0 then min_risk_cpd else 0 end) as  total_min_risk_cpd,\n", "        sum(case when min_risk_planned > 0 then min_risk_planned else 0 end) as total_min_risk_planned,\n", "        \n", "        sum(case when adjusted_risk_cpd > 0 then avg_item_asp else 0 end) as total_adjusted_asp_cpd,\n", "        sum(case when adjusted_risk_planned > 0 then avg_item_asp else 0 end) as total_adjusted_asp_planned,\n", "        sum(case when min_risk_cpd > 0 then avg_item_asp else 0 end) as  total_min_asp_cpd,\n", "        sum(case when min_risk_planned > 0 then avg_item_asp else 0 end) as total_min_asp_planned\n", "        \n", "    from risk_final\n", "    group by 1\n", ")\n", "\n", "\n", "select \n", "    *,\n", "    rank() over (partition by date_ order by risk_inv_contri_planned desc) as risk_inv_planned_rnk,\n", "    rank() over (partition by date_ order by risk_inv_contri_cpd desc) as risk_inv_cpd_rnk,\n", "    rank() over (partition by date_ order by final_risk_score_planned desc) as final_risk_planned_rnk,\n", "    rank() over (partition by date_ order by final_risk_score_cpd desc) as final_risk_cpd_rnk\n", "from \n", "    (\n", "        select\n", "            rf.*, \n", "            (case when rf.adjusted_risk_cpd > 0 then (rf.adjusted_risk_cpd*1.00/sb.total_adjusted_risk_cpd) else 0 end) as risk_inv_contri_cpd,\n", "            (case when rf.adjusted_risk_planned > 0 then (rf.adjusted_risk_planned*1.00/sb.total_adjusted_risk_planned) else 0 end) as risk_inv_contri_planned,\n", "            (case when rf.min_risk_cpd > 0 then (rf.min_risk_cpd*1.00/sb.total_min_risk_cpd) else 0 end) as ideal_risk_inv_contri_cpd,\n", "            (case when rf.min_risk_planned > 0 then (rf.min_risk_planned*1.00/sb.total_min_risk_planned) else 0 end) as ideal_risk_inv_contri_planned,\n", "            \n", "            (case when adjusted_risk_cpd > 0 then (avg_item_asp*1.00/total_adjusted_asp_cpd) else 0 end)*0.4 + (case when rf.adjusted_risk_cpd > 0 then (rf.adjusted_risk_cpd*1.00/sb.total_adjusted_risk_cpd) else 0 end)*0.6 as final_risk_score_cpd,\n", "            (case when adjusted_risk_planned > 0 then (avg_item_asp*1.00/total_adjusted_asp_planned) else 0 end)*0.4 + (case when rf.adjusted_risk_planned > 0 then (rf.adjusted_risk_planned*1.00/sb.total_adjusted_risk_planned) else 0 end)*0.6 as final_risk_score_planned\n", "            \n", "        from risk_final rf\n", "        left join score_base sb on rf.date_ = sb.date_ \n", "    )\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8aa1841b-e2c0-4f83-a4d3-61a748fa37c5", "metadata": {}, "outputs": [], "source": ["# df_risk = read_sql_query(risk, CON_TRINO)\n", "# df_risk"]}, {"cell_type": "code", "execution_count": null, "id": "2da723de-a634-48b5-8726-70227b61b1f8", "metadata": {}, "outputs": [], "source": ["# df_risk.head()"]}, {"cell_type": "code", "execution_count": null, "id": "834d987c-c2b2-41e4-a4d0-2b722fc4d1fe", "metadata": {}, "outputs": [], "source": ["# df_risk.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "8bec9bec-9ddc-47f3-b8b8-44186f4e6e91", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_risk[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_risk.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9e86fb23-8768-4107-8314-2118f7420252", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"Backend Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"product_type\"},\n", "    {\"name\": \"avg_item_asp\", \"type\": \"real\", \"description\": \"City Avg Selling Price \"},\n", "    {\"name\": \"start_inv\", \"type\": \"integer\", \"description\": \"Inventory on Day Start\"},\n", "    {\"name\": \"left_over_inv\", \"type\": \"real\", \"description\": \"Inventory on Day End\"},\n", "    {\"name\": \"day_grn_qty\", \"type\": \"real\", \"description\": \"Day Grn Qty\"},\n", "    {\"name\": \"day_qty_sold\", \"type\": \"integer\", \"description\": \"Day Qty Sold\"},\n", "    {\"name\": \"day_forecast_cpd\", \"type\": \"real\", \"description\": \"Day Forecasted Sales cpd\"},\n", "    {\"name\": \"day_forecast_planned\", \"type\": \"real\", \"description\": \"Day Forecasted Sales planned\"},\n", "    {\"name\": \"day_risk_cpd\", \"type\": \"real\", \"description\": \"Day Risk CPD\"},\n", "    {\"name\": \"day_risk_planned\", \"type\": \"real\", \"description\": \"Day Risk Planned\"},\n", "    {\"name\": \"day_gmv_loss_cpd\", \"type\": \"varchar\", \"description\": \"Day GMV loss cpd\"},\n", "    {\"name\": \"day_gmv_loss_planned\", \"type\": \"varchar\", \"description\": \"Day GMV loss Planned\"},\n", "    {\"name\": \"total_grn_post_start\", \"type\": \"real\", \"description\": \"total grn till date\"},\n", "    {\"name\": \"total_sales_post_start\", \"type\": \"integer\", \"description\": \"total sales till date\"},\n", "    {\"name\": \"total_forecast_cpd\", \"type\": \"real\", \"description\": \"total cpd forecast till date\"},\n", "    {\n", "        \"name\": \"total_forecast_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"total planned forecast till date\",\n", "    },\n", "    {\"name\": \"total_gmv_loss_cpd\", \"type\": \"real\", \"description\": \"total GMV loss cpd till date\"},\n", "    {\n", "        \"name\": \"total_gmv_loss_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"total GMV loss planned till date\",\n", "    },\n", "    {\n", "        \"name\": \"total_upcoming_forecast_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"total cpd forecast left till sale end\",\n", "    },\n", "    {\n", "        \"name\": \"total_upcoming_forecast_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"total planned forecast left till sale end\",\n", "    },\n", "    {\n", "        \"name\": \"total_expected_upcoming_sales_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"total cpd forecast left by current run-rate\",\n", "    },\n", "    {\n", "        \"name\": \"total_expected_upcoming_sales_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"total planned forecast left by current run-rate\",\n", "    },\n", "    {\"name\": \"absolute_risk_cpd\", \"type\": \"real\", \"description\": \"day risk cumulative cpd\"},\n", "    {\"name\": \"absolute_risk_planned\", \"type\": \"real\", \"description\": \"day risk cumulative planned\"},\n", "    {\n", "        \"name\": \"adjusted_risk_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"day risk cumulative cpd - upcoming expected\",\n", "    },\n", "    {\n", "        \"name\": \"adjusted_risk_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"day risk cumulative planned - upcoming expected\",\n", "    },\n", "    {\n", "        \"name\": \"min_risk_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"day risk cumulative cpd - upcoming expected\",\n", "    },\n", "    {\n", "        \"name\": \"min_risk_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"day risk cumulative planned - upcoming\",\n", "    },\n", "    {\"name\": \"risk_inv_contri_cpd\", \"type\": \"real\", \"description\": \"qty risk cpd contribution\"},\n", "    {\n", "        \"name\": \"risk_inv_contri_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"qty risk planned contribution\",\n", "    },\n", "    {\n", "        \"name\": \"ideal_risk_inv_contri_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"min qty risk cpd contribution\",\n", "    },\n", "    {\n", "        \"name\": \"ideal_risk_inv_contri_planned\",\n", "        \"type\": \"real\",\n", "        \"description\": \"min qty risk planned contribution\",\n", "    },\n", "    {\"name\": \"final_risk_score_cpd\", \"type\": \"real\", \"description\": \"risk score cpd\"},\n", "    {\"name\": \"final_risk_score_planned\", \"type\": \"real\", \"description\": \"risk score planned\"},\n", "    {\"name\": \"risk_inv_planned_rnk\", \"type\": \"integer\", \"description\": \"planned qty risk rank\"},\n", "    {\"name\": \"risk_inv_cpd_rnk\", \"type\": \"integer\", \"description\": \"cpd qty risk rank\"},\n", "    {\"name\": \"final_risk_planned_rnk\", \"type\": \"integer\", \"description\": \"Final Risk Rank Planned\"},\n", "    {\"name\": \"final_risk_cpd_rnk\", \"type\": \"integer\", \"description\": \"Final Risk Rank cpd\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "176c52e5-fc2d-4aa2-a7c9-14cb0b881674", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_inventory_risk_be_ptype\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"festival_name\",\n", "        \"be_facility_id\",\n", "        \"be_name\",\n", "        \"product_type\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}\n", "\n", "to_trino(risk, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}