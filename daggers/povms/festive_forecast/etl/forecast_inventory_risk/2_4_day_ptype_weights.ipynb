{"cells": [{"cell_type": "code", "execution_count": null, "id": "c444a6d5-18f4-4d0b-b89c-2d75bc77104d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4775dcdf-3b7a-40ec-a513-cd3d8b53e153", "metadata": {"tags": []}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b7962273-b8ba-4e38-b2ef-1cc1bcd174ad", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"l0_id\", \"type\": \"integer\", \"description\": \"L0 ID\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"L0 Category\"},\n", "    {\"name\": \"l1_id\", \"type\": \"integer\", \"description\": \"L1 ID\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"L1 Category\"},\n", "    {\"name\": \"l2_id\", \"type\": \"integer\", \"description\": \"L2 ID\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"L2 Category\"},\n", "    {\n", "        \"name\": \"day_weight\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Day weight of the sale for the Product Type\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ec78ac0-465d-4cfe-996f-1bc60d2d3970", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"ptype_festival_weights_dod\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"p_type\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "5b69eeae-b52f-48e2-9c26-fb19765d65b2", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'"]}, {"cell_type": "code", "execution_count": null, "id": "3c4fdf56-10da-4ee5-a5bc-ca86eaf45bf3", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b0c02292-6c1b-46e4-9295-3fdf4f8a0a2d", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "18cb652a-f2a1-47e1-bfd6-9a6e9272a5ca", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "70167d71-cf6f-4b78-a828-1063938fe408", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2adf1ae0-b52f-4647-9c67-b7352e2bc9b6", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "3834e532-7cd5-42e2-8900-299066cfae17", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[festive_date_data[\"flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "ea129943-d247-4fd1-9c45-bd91ad10400f", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "807ea161-f66f-4ede-bed9-c5631590f8b3", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "1cac9338-7bff-477b-b479-c01e40faaa3e", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "c26ced28-e03d-4302-b810-dd6a5e66b2cd", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        bau_start_date = row[\"bau_start_date\"]\n", "        bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "\n", "        be_ptype = f\"\"\" \n", "\n", "        with outlet_details as (\n", "            select --current_date as updated_at, \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "            from supply_etls.outlet_details\n", "            where ars_check=1\n", "                and grocery_active_count >= 1\n", "                and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "\n", "        item_product_mapping as (\n", "            select item_id, product_id, multiplier\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current\n", "        ),\n", "\n", "        sales as (\n", "            select \n", "                ip.item_id,\n", "                sum(procured_quantity * multiplier) as qty_sold\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            where fs.order_create_dt_ist between date('{festival_start_date}') and date('{festival_end_date}')\n", "                and cart_checkout_ts_ist >= date('{festival_start_date}')\n", "                and cart_checkout_ts_ist < date('{festival_end_date}') + interval '1' day\n", "                and fs.order_current_status = 'DELIVERED'\n", "            group by 1\n", "        ),\n", "\n", "        item_details_base as (\n", "            select --current_date as updated_at, \n", "                p_type, l0_id, l0_category, \n", "                l1_id, l1_category, l2_id, l2_category,\n", "                sum(qty_sold),\n", "                row_number() over(partition by p_type order by sum(qty_sold) desc) as rn\n", "            from supply_etls.item_details id\n", "            left join sales s on s.item_id = id.item_id\n", "            where handling_type = 'Non Packaging Material'\n", "            group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "        item_details as(\n", "            select p_type, l0_id, l0_category, \n", "                l1_id, l1_category, l2_id, l2_category\n", "            from item_details_base\n", "            where rn = 1\n", "        ),\n", "\n", "        forecast_dates as(\n", "            select (date('{sale_start_date}') - interval '1' day) + (n * interval '1' day) as date_\n", "            from unnest(sequence(1,date_diff('day', date('{sale_start_date}'), date('{sale_end_date}') + interval '1' day))) as t(n)\n", "        ),\n", "\n", "        date_city_ptype_universe as (\n", "            select\n", "                fd.date_,\n", "                id.p_type,\n", "                id.l0_id,\n", "                id.l0_category,\n", "                id.l1_id,\n", "                id.l1_category,\n", "                id.l2_id, \n", "                id.l2_category\n", "            from item_details id\n", "            cross join forecast_dates fd\n", "        ),\n", "\n", "        keyword_search_traffic as (\n", "            select distinct mdks.date_,\n", "                mdks.keyword,\n", "                od.city_name,\n", "                sum(mdks.search_device_count) as search_device_count\n", "           from supply_etls.merchant_dod_keyword_searches_v2 mdks\n", "           left join supply_etls.outlet_details od on od.hot_outlet_id = mdks.outlet_id\n", "           where date_ between date('{festival_start_date}') and date('{festival_end_date}')\n", "           group by 1,2,3\n", "        ),\n", "\n", "        ptype_distribution_raw as (\n", "            select *\n", "            from supply_etls.keyword_ptype_distribution\n", "            where date_ between (date('{festival_start_date}') - interval'7'day) and (date('{festival_end_date}') + interval'7'day)\n", "        ),\n", "\n", "        ptype_atc_distribution_initial as (\n", "            select distinct base.date_,\n", "                base.product_type,\n", "                base.keyword,\n", "                pdr.devices,\n", "                pdr.total_devices,\n", "                try(pdr.devices/nullif(pdr.total_devices,0)) as percentage_attribution_d_day,\n", "                try(sum(pdr.devices) over \n", "                (partition by base.product_type, base.keyword order by base.date_  rows between 7 preceding and 7 following)) as devices_14d,\n", "                try(sum(pdr.total_devices) over \n", "                (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following)) as total_devices_14d,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.product_type, base.keyword order by base.date_ ROWS BETWEEN 7 preceding and 7 following)) / \n", "                nullif((1.00 * sum(pdr.total_devices) \n", "                over (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following)),0)) as percentage_attribution_14d_window\n", "            from (\n", "                    (select distinct date_ from ptype_distribution_raw) \n", "                        cross join \n", "                    (select distinct keyword, product_type from ptype_distribution_raw)\n", "                ) base\n", "            left join ptype_distribution_raw pdr \n", "            on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "        ), \n", "\n", "        ptype_atc_distribution as (\n", "            select date_,\n", "                keyword,\n", "                product_type,\n", "                devices,\n", "                total_devices,\n", "                percentage_attribution_d_day,\n", "                devices_14d,\n", "                total_devices_14d,\n", "                percentage_attribution_14d_window,\n", "                key_rank\n", "            from (select date_,\n", "                    keyword,\n", "                    product_type,\n", "                    devices,\n", "                    total_devices,\n", "                    percentage_attribution_d_day,\n", "                    devices_14d,\n", "                    total_devices_14d,\n", "                    percentage_attribution_14d_window,\n", "                    rank() over (partition by date_, keyword order by percentage_attribution_14d_window desc) as key_rank\n", "                from ptype_atc_distribution_initial)\n", "            where (key_rank<=1 or percentage_attribution_14d_window > 0.1)\n", "        ),\n", "\n", "        atc_split as (\n", "            select date_,\n", "                product_type,\n", "                1.000000 * sum(search_atc_device_count) as search_atcs,\n", "                1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "                try((1.000000 * sum(search_atc_device_count))/nullif(((1.000000 * sum(search_atc_device_count)) + (1.000000 * sum(non_search_atc_device_count))),0)) as search_contri\n", "            from supply_etls.ptype_store_atc_channel_split_v2\n", "            where date_ between date('{festival_start_date}') and date('{festival_end_date}')\n", "            group by 1,2\n", "        ),\n", "\n", "        dod_searches_curve_raw as (\n", "            select keys.date_ as date_,\n", "                keys.date_ + interval '{gap_days}' day as curr_date_,\n", "                coalesce(ptype_atc_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "                search_contri as search_contri_atc,\n", "                sum(keys.search_device_count) as search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_d_day) as ptype_d_day_search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_14d_window) as ptype_14d_window_search_device_count\n", "            from keyword_search_traffic keys\n", "            left join ptype_atc_distribution on keys.date_ = ptype_atc_distribution.date_ and keys.keyword = ptype_atc_distribution.keyword\n", "            left join atc_split on keys.date_ = atc_split.date_ and ptype_atc_distribution.product_type = atc_split.product_type\n", "            group by 1,2,3,4\n", "        ),\n", "\n", "        dod_searches_curve as (\n", "             select date_,\n", "                curr_date_,\n", "                product_type,\n", "                -- city_name,\n", "                search_device_count,\n", "                ptype_d_day_search_device_count,\n", "                ptype_14d_window_search_device_count,\n", "                search_contri_atc,\n", "                0.95 as availability_cut,\n", "                1.10 as buffer,\n", "                try(1.00 * search_device_count / (case when search_contri_atc = 0 then 1 when search_contri_atc is null then 1 else search_contri_atc end)) as total_demand,\n", "                try(1.00 * ptype_d_day_search_device_count / (case when search_contri_atc = 0 then 1 when search_contri_atc is null then 1 else search_contri_atc end)) as total_demand_d_day_ptype,\n", "                try(1.00 * ptype_14d_window_search_device_count) / (case when search_contri_atc = 0 then 1 when search_contri_atc is null then 1 else search_contri_atc end) as total_demand_14_day_ptype\n", "            from dod_searches_curve_raw\n", "            where curr_date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "        ),\n", "\n", "        final_base as(\n", "            select \n", "                dcpu.date_,\n", "                dcpu.p_type,\n", "                dcpu.l0_id,\n", "                dcpu.l0_category, \n", "                dcpu.l1_id,\n", "                dcpu.l1_category,\n", "                dcpu.l2_id, \n", "                dcpu.l2_category,\n", "                dsc.total_demand_14_day_ptype,\n", "                try((sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.date_, dcpu.p_type))/(sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.p_type))) as p_type_weight,\n", "                try((sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.date_, dcpu.l2_id))/(sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.l2_id))) as l2_weight,\n", "                try((sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.date_, dcpu.l1_id))/(sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.l1_id))) as l1_weight,\n", "                try((sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.date_, dcpu.l0_id))/(sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.l0_id))) as l0_weight,\n", "                try((sum(dsc.total_demand_14_day_ptype) over(partition by dcpu.date_))/(sum(dsc.total_demand_14_day_ptype) over())) as overall_weight\n", "            from date_city_ptype_universe dcpu\n", "            left join dod_searches_curve dsc on dcpu.date_ = cast(dsc.curr_date_ as date) and dsc.product_type = dcpu.p_type\n", "        ),\n", "\n", "        final as(\n", "            select\n", "                date_,\n", "                p_type,\n", "                l0_id,\n", "                l0_category,\n", "                l1_id,\n", "                l1_category,\n", "                l2_id,\n", "                l2_category,\n", "                p_type_weight,\n", "                l2_weight,\n", "                l1_weight,\n", "                l0_weight,\n", "                overall_weight,\n", "                try((count(p_type_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) as p_type_weight_count_ratio,\n", "                try((count(l2_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) as l2_weight_count_ratio,\n", "                try((count(l1_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) as l1_weight_count_ratio,\n", "                try((count(l0_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) as l0_weight_count_ratio,\n", "                case when try((count(p_type_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) > 0.66\n", "                     then p_type_weight\n", "                     when try((count(l2_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) > 0.66\n", "                     then l2_weight\n", "                     when try((count(l1_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) > 0.66\n", "                     then l1_weight\n", "                     when try((count(l0_weight) over(partition by p_type)) * 1.0000/(count(date_) over(partition by p_type))) > 0.66\n", "                     then l0_weight\n", "                     else overall_weight\n", "                end as day_weight\n", "            from final_base\n", "        )\n", "\n", "        select\n", "            date_,\n", "            '{festival}' as festival_name,\n", "            p_type,\n", "            l0_id,\n", "            l0_category,\n", "            l1_id,\n", "            l1_category,\n", "            l2_id,\n", "            l2_category,\n", "            coalesce(day_weight,0) as day_weight \n", "        from final \n", "\n", "        \"\"\"\n", "\n", "        to_trino(be_ptype, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "6a67af73-4f39-4e6c-8be4-ac9b7be65f01", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b66d084f-319e-4477-ac0f-29eaac5f33de", "metadata": {}, "outputs": [], "source": ["# df_be_ptype = read_sql_query(be_ptype, CON_TRINO)\n", "# df_be_ptype"]}, {"cell_type": "code", "execution_count": null, "id": "de630dd3-e554-47d6-86e9-e351925a6993", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d3cbe65b-4fcc-4f4b-bfd6-c8384cdf4c31", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9de672d0-fe2f-4623-8b6c-608a5a9009d1", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_be_ptype[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_be_ptype.columns\n", "# ]\n", "\n", "# column_dtypes"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}