{"cells": [{"cell_type": "code", "execution_count": null, "id": "c444a6d5-18f4-4d0b-b89c-2d75bc77104d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4775dcdf-3b7a-40ec-a513-cd3d8b53e153", "metadata": {"tags": []}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b7962273-b8ba-4e38-b2ef-1cc1bcd174ad", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"actual_potential_sales\", \"type\": \"real\", \"description\": \"Current Potential Sales\"},\n", "    {\"name\": \"actual_cpd\", \"type\": \"real\", \"description\": \"Actual current APS\"},\n", "    {\n", "        \"name\": \"search_device_count_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Searches (Festival)\",\n", "    },\n", "    {\"name\": \"total_demand_festival\", \"type\": \"real\", \"description\": \"Total Demand (Festival)\"},\n", "    {\"name\": \"search_device_count_bau\", \"type\": \"real\", \"description\": \"Total Searches (BAU)\"},\n", "    {\"name\": \"total_demand_bau\", \"type\": \"real\", \"description\": \"Total Demand (BAU)\"},\n", "    {\n", "        \"name\": \"search_contri_atc_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution in ATC (Festival)\",\n", "    },\n", "    {\"name\": \"search_atcs_festival\", \"type\": \"real\", \"description\": \"Search ATC (Festival)\"},\n", "    {\n", "        \"name\": \"non_search_atcs_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Non-Search ATC (Festival)\",\n", "    },\n", "    {\"name\": \"search_atcs_bau\", \"type\": \"real\", \"description\": \"Search ATC (BAU)\"},\n", "    {\"name\": \"non_search_atcs_bau\", \"type\": \"real\", \"description\": \"Non-Search ATC (BAU)\"},\n", "    {\"name\": \"total_atc_festival\", \"type\": \"real\", \"description\": \"Total ATC (Festival)\"},\n", "    {\n", "        \"name\": \"search_contri_atc_bau\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution in ATC (BAU)\",\n", "    },\n", "    {\"name\": \"total_atc_bau\", \"type\": \"real\", \"description\": \"Total ATC (BAU)\"},\n", "    {\"name\": \"search_spike\", \"type\": \"real\", \"description\": \"Search Spike\"},\n", "    {\"name\": \"demand_spike\", \"type\": \"real\", \"description\": \"Demand Spike\"},\n", "    {\n", "        \"name\": \"search_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Conversion (festival)\",\n", "    },\n", "    {\n", "        \"name\": \"demand_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Demand Conversion (festival)\",\n", "    },\n", "    {\"name\": \"search_conversion_bau\", \"type\": \"real\", \"description\": \"Search Conversion (BAU)\"},\n", "    {\"name\": \"demand_conversion_bau\", \"type\": \"real\", \"description\": \"Demand Conversion (BAU)\"},\n", "    {\"name\": \"ptype_carts\", \"type\": \"real\", \"description\": \"Carts of that Ptype\"},\n", "    {\"name\": \"overall_carts\", \"type\": \"real\", \"description\": \"All carts\"},\n", "    {\n", "        \"name\": \"ipc_ptype_carts\",\n", "        \"type\": \"real\",\n", "        \"description\": \"IPC considering carts of that ptype\",\n", "    },\n", "    {\"name\": \"ipc_overall_carts\", \"type\": \"real\", \"description\": \"IPC considering all carts\"},\n", "    {\"name\": \"cart_pen\", \"type\": \"real\", \"description\": \"Cart Pen\"},\n", "    {\"name\": \"actual_sales\", \"type\": \"real\", \"description\": \"Actual qty sold\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ec78ac0-465d-4cfe-996f-1bc60d2d3970", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_ptype_actuals\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city\",\n", "        \"product_type\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "def7eaea-914b-4ad2-b02f-81fce6b4236e", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'\n", "# sale_bau_start_date = '2024-09-02'\n", "# sale_bau_end_date = '2024-09-15'\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "0ae9bb4d-e983-4a93-ba15-abe8d12a233e", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5a80c1e7-b4c3-479f-a41e-104f7a34e2b3", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "        \"sale_bau_start_date\",\n", "        \"sale_bau_end_date\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a7ca20a2-8e1a-4f8d-b70a-f94b9c84af68", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "da73be4d-482b-4632-8663-7cda52d8092d", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8007f82a-9864-460c-ad48-97b6916656a8", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "3fb4aae7-7c65-4c02-86fd-af665650a489", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[festive_date_data[\"flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "6058ddee-bd10-4a2d-90e6-b2c9b2c7b4ac", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5c4d67fb-4cf6-482c-bdd0-0b5e1cca8d1f", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3b6f7c8-40c0-4536-a288-c0de31ae5c9d", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "420fa915-7af1-4d3b-ac50-2fadbc607662", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        bau_start_date = row[\"bau_start_date\"]\n", "        bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "        sale_bau_start_date = row[\"sale_bau_start_date\"]\n", "        sale_bau_end_date = row[\"sale_bau_end_date\"]\n", "\n", "        be_ptype = f\"\"\" \n", "\n", "        with seller_items as (\n", "            select \n", "                distinct spm.item_id as item_id\n", "            from seller.seller_product_mappings spm \n", "        ),\n", "        \n", "        store_carts as (\n", "            select outlet_id, \n", "                try(count(distinct order_id)*1.00/count(distinct date(cart_checkout_ts_ist)))  as carts \n", "            from dwh.fact_sales_order_details x \n", "            where cart_checkout_ts_ist >= date('{sale_start_date}') - interval '8' day \n", "                and cart_checkout_ts_ist < date('{sale_start_date}')\n", "                and order_create_dt_ist between date('{sale_start_date}') - interval '8' day and date('{sale_start_date}') - interval '1' day\n", "                and order_current_status = 'DELIVERED' \n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "            group by 1\n", "\n", "        ),\n", "\n", "        pfma as (\n", "            select distinct item_id,\n", "                facility_id,\n", "                master_assortment_substate_id\n", "            from rpc.product_facility_master_assortment pfma \n", "            where active = 1\n", "        --    and master_assortment_substate_id in (1,3)\n", "            and lake_active_record\n", "        ),\n", "\n", "        aps as (\n", "            select distinct outlet_id,\n", "                item_id,\n", "                cpd as bumped_cpd,\n", "                aps_adjusted as cpd\n", "            from ars.outlet_item_aps_derived_cpd\n", "            where for_date = date('{sale_start_date}') - interval '1' day\n", "            and insert_ds_ist = cast(date('{sale_start_date}') - interval '1' day as varchar)\n", "        ),\n", "\n", "\n", "        iotm as (\n", "            select distinct item_id,\n", "                outlet_id,\n", "                cast(tag_value as int) as po_outlet_id\n", "            from rpc.item_outlet_tag_mapping \n", "            where tag_type_id = 8\n", "                and active = 1\n", "                and lake_active_record\n", "                and item_id not in (select item_id from seller_items)\n", "        ),\n", "\n", "        daily_orders as (\n", "            select item_id,\n", "                facility_id,\n", "                1.000000*sum(order_quantity)/15 as sales,\n", "                1.000000*sum(potential_order_quantity)/15 as potential_sales\n", "            from ars.daily_orders_and_availability\n", "            where lake_active_record \n", "                and insert_ds_ist between cast(date('{sale_start_date}') - interval '15' day as varchar) and cast(date('{sale_start_date}') - interval '1' day as varchar)\n", "            group by 1,2\n", "        ),\n", "\n", "        outlet_details as (\n", "            select --current_date as updated_at, \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                    inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "            from supply_etls.outlet_details\n", "            where ars_check=1\n", "            and grocery_active_count >= 1\n", "            and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "\n", "        final_sales as (\n", "            select --location,\n", "                od.city_name as city,\n", "                product_type,\n", "                sum(bumped_cpd) as bumped_cpd,\n", "                sum(cpd) as actual_cpd,\n", "                sum(sales) as sales,\n", "                sum(potential_sales) as actual_potential_sales\n", "            from iotm\n", "            inner join store_carts on store_carts.outlet_id = iotm.outlet_id and carts>100\n", "            inner join rpc.item_category_details icd on icd.item_id = iotm.item_id\n", "            inner join retail.console_outlet co on co.id = iotm.outlet_id\n", "            inner join pfma on iotm.item_id = pfma.item_id and co.facility_id = pfma.facility_id\n", "            left join aps on aps.item_id = iotm.item_id and aps.outlet_id = iotm.outlet_id\n", "            left join daily_orders on daily_orders.item_id = iotm.item_id and daily_orders.facility_id = co.facility_id\n", "            left join supply_etls.outlet_details od on od.hot_outlet_id = iotm.outlet_id\n", "            group by 1,2\n", "        ),\n", "\n", "        ptype_distribution_raw as (\n", "            select *\n", "            from supply_etls.keyword_ptype_distribution\n", "            where date_ between (date('{sale_bau_start_date}') - interval'7'day) and (date('{sale_end_date}') + interval'7'day)\n", "        ),\n", "\n", "        ptype_atc_distribution_initial as (\n", "            select distinct base.date_,\n", "                base.product_type,\n", "                base.keyword,\n", "                pdr.devices,\n", "                pdr.total_devices,\n", "                try(pdr.devices/pdr.total_devices) as percentage_attribution_d_day,\n", "                sum(pdr.devices) over \n", "                (partition by base.product_type, base.keyword order by base.date_  rows between 7 preceding and 7 following) as devices_14d,\n", "                sum(pdr.total_devices) over \n", "                (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following) as total_devices_14d,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.product_type, base.keyword order by base.date_ ROWS BETWEEN 7 preceding and 7 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following))) as percentage_attribution_14d_window\n", "            from (\n", "                    (select distinct date_ from ptype_distribution_raw) \n", "                        cross join \n", "                    (select distinct keyword, product_type from ptype_distribution_raw)\n", "                ) base\n", "            left join ptype_distribution_raw pdr \n", "            on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "        ), \n", "\n", "        ptype_atc_distribution as (\n", "            select date_,\n", "                keyword,\n", "                product_type,\n", "                devices,\n", "                total_devices,\n", "                percentage_attribution_d_day,\n", "                devices_14d,\n", "                total_devices_14d,\n", "                percentage_attribution_14d_window,\n", "                key_rank\n", "            from (select date_,\n", "                    keyword,\n", "                    product_type,\n", "                    devices,\n", "                    total_devices,\n", "                    percentage_attribution_d_day,\n", "                    devices_14d,\n", "                    total_devices_14d,\n", "                    percentage_attribution_14d_window,\n", "                    rank() over (partition by date_, keyword order by percentage_attribution_14d_window desc) as key_rank\n", "                from ptype_atc_distribution_initial)\n", "            where (key_rank<=1 or percentage_attribution_14d_window > 0.1)\n", "        ),\n", "\n", "        keyword_search_traffic_city as (\n", "            select distinct mdks.date_,\n", "                mdks.keyword,\n", "                od.city_name,\n", "                --cc.cluster_name,\n", "                sum(mdks.search_device_count) as search_device_count\n", "           from supply_etls.merchant_dod_keyword_searches_v2 mdks\n", "           left join supply_etls.outlet_details od on od.hot_outlet_id = mdks.outlet_id\n", "           --left join cluster_city cc on cc.city_id = od.city_id\n", "           where date_ between date('{sale_bau_start_date}') and date('{sale_end_date}')\n", "           group by 1,2,3--,4\n", "        ),\n", "        \n", "        atc_split_city as (\n", "            select date_,\n", "                product_type,\n", "                od.city_name,\n", "                1.000000 * sum(search_atc_device_count) as search_atcs,\n", "                1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "                try((1.000000 * sum(search_atc_device_count))/((1.000000 * sum(search_atc_device_count)) + (1.000000 * sum(non_search_atc_device_count)))) as search_contri\n", "            from supply_etls.ptype_store_atc_channel_split_v2 atc\n", "            left join supply_etls.outlet_details od on od.hot_outlet_id = atc.outlet_id\n", "            where date_ between date('{sale_bau_start_date}') and date('{sale_end_date}')\n", "            group by 1,2,3\n", "        ),\n", "        \n", "        dod_searches_curve_raw_city as (\n", "            select keys.date_ as date_, keys.city_name,\n", "                keys.date_ as curr_date_,\n", "                coalesce(ptype_atc_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "                search_contri as search_contri_atc,\n", "                search_atcs,\n", "                non_search_atcs,\n", "                sum(keys.search_device_count) as search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_d_day) as ptype_d_day_search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_14d_window) as ptype_14d_window_search_device_count\n", "            from keyword_search_traffic_city keys\n", "            left join ptype_atc_distribution on keys.date_ = ptype_atc_distribution.date_ and keys.keyword = ptype_atc_distribution.keyword\n", "            left join atc_split_city atc_split on keys.date_ = atc_split.date_ and ptype_atc_distribution.product_type = atc_split.product_type and atc_split.city_name = keys.city_name\n", "            group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "\n", "        dod_searches_curve_14_days_city as (\n", "            select product_type, city_name,\n", "                min(curr_date_) as min_curr_date_,\n", "                max(curr_date_) as max_curr_date_,\n", "                avg(search_device_count) as avg_search_device_count,\n", "                avg(search_atcs + non_search_atcs) as avg_total_atc,\n", "                avg(search_atcs) as avg_search_atcs,\n", "                avg(non_search_atcs) as avg_non_search_atcs,\n", "                avg(ptype_d_day_search_device_count) as avg_ptype_d_day_search_device_count,\n", "                avg(ptype_14d_window_search_device_count) as avg_ptype_14d_window_search_device_count,\n", "                try((1.000000 * sum(search_atcs))/((1.000000 * sum(search_atcs)) + (1.000000 * sum(non_search_atcs)))) as avg_search_contri_atc,\n", "                try(avg(1.00 * search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand,\n", "                try(avg(1.00 * ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_d_day_ptype,\n", "                try(avg(1.00 * ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_14_day_ptype\n", "            from dod_searches_curve_raw_city\n", "            where date_ between date('{sale_bau_start_date}') and date('{sale_bau_end_date}')\n", "            group by 1,2\n", "        ),\n", "\n", "        dod_searches_curve_city as (\n", "             select date_,\n", "                curr_date_,\n", "                product_type,\n", "                city_name,\n", "                search_device_count,\n", "                search_atcs + non_search_atcs as total_atc,\n", "                search_atcs,\n", "                non_search_atcs,\n", "                ptype_d_day_search_device_count,\n", "                ptype_14d_window_search_device_count,\n", "                search_contri_atc,\n", "                0.95 as availability_cut,\n", "                1.10 as buffer,\n", "                (1.00 * try(search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand,\n", "                (1.00 * try(ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_d_day_ptype,\n", "                (1.00 * try(ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_14_day_ptype\n", "            from dod_searches_curve_raw_city\n", "            where curr_date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "        ),\n", "        \n", "        item_details as (\n", "            select \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where handling_type = 'Non Packaging Material'\n", "            --and assortment_type='Packaged Goods'\n", "        ),\n", "        \n", "        item_product_mapping as (\n", "            select item_id, product_id, multiplier\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current\n", "        ),\n", "        \n", "        sales_overall as(\n", "            select\n", "                order_create_dt_ist as date_,\n", "                od.city_name,\n", "                count(distinct cart_id) as overall_carts\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            where fs.order_create_dt_ist between date('{sale_start_date}') and date('{sale_end_date}')\n", "                and fs.order_current_status = 'DELIVERED'\n", "            group by 1,2\n", "        ),\n", "        \n", "        sales as (\n", "            select \n", "                order_create_dt_ist as date_, \n", "                od.city_name, id.p_type,\n", "                sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price) as gmv,\n", "                count(distinct cart_id) as ptype_carts,\n", "                max(overall_carts) as overall_carts,\n", "                cast(sum(procured_quantity * multiplier) as decimal (10,4)) / count(distinct cart_id) as ipc_ptype_carts,\n", "                cast(sum(procured_quantity * multiplier) as decimal(10,4)) / max(overall_carts) as ipc_overall_carts,\n", "                count(distinct cart_id) / max(overall_carts) as cart_pen\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            inner join item_details id on id.item_id=ip.item_id\n", "            left join sales_overall so on so.date_ = fs.order_create_dt_ist\n", "                and so.city_name = od.city_name\n", "            where fs.order_create_dt_ist between date('{sale_start_date}') and date('{sale_end_date}')\n", "            and fs.order_current_status = 'DELIVERED'\n", "            group by 1,2,3\n", "        ),\n", "\n", "\n", "        final as (\n", "        select \n", "            dod_city.curr_date_,\n", "            '{festival}' as festival,\n", "            dod_city.city_name as city,\n", "            dod_city.product_type,\n", "            \n", "            dod_city_base.avg_search_atcs as city_avg_search_atcs,\n", "            dod_city_base.avg_non_search_atcs as city_avg_non_search_atcs,\n", "            dod_city_base.avg_search_contri_atc as city_avg_search_contri_atc,\n", "            dod_city_base.avg_total_atc as city_avg_total_atc,\n", "            dod_city_base.avg_search_device_count as city_avg_search_device_count,\n", "            dod_city_base.avg_total_demand as city_avg_total_demand,\n", "            \n", "            dod_city.search_contri_atc as city_search_contri_atc,\n", "            dod_city.total_atc as city_total_atc,\n", "            dod_city.search_atcs as city_search_atcs,\n", "            dod_city.non_search_atcs as city_non_search_atcs,\n", "            dod_city.search_device_count as city_search_device_count,\n", "            dod_city.total_demand as city_total_demand,\n", "            \n", "            final_sales.actual_cpd,\n", "            final_sales.actual_potential_sales,\n", "            \n", "            s.qty_sold as actual_sales,\n", "            s.ptype_carts,\n", "            s.overall_carts,\n", "            s.ipc_ptype_carts,\n", "            s.ipc_overall_carts,\n", "            s.cart_pen\n", "    \n", "        from dod_searches_curve_city dod_city\n", "        \n", "        left join dod_searches_curve_14_days_city dod_city_base \n", "            on dod_city.city_name = dod_city_base.city_name and dod_city.product_type = dod_city_base.product_type\n", "            \n", "        inner join final_sales \n", "            on final_sales.product_type = dod_city.product_type and final_sales.city = dod_city.city_name\n", "        \n", "        left join sales s \n", "            on s.date_ = dod_city.curr_date_\n", "            and s.city_name = dod_city.city_name\n", "            and s.p_type = dod_city.product_type\n", "        )\n", "        \n", "        select \n", "            curr_date_ as date_,\n", "            festival as festival_name,\n", "            city,\n", "            product_type,\n", "            actual_cpd,\n", "            actual_potential_sales,\n", "            city_search_device_count as search_device_count_festival,\n", "            city_total_demand as total_demand_festival ,\n", "            city_avg_search_device_count as search_device_count_bau,\n", "            city_avg_total_demand as total_demand_bau,\n", "            city_search_contri_atc as search_contri_atc_festival,\n", "            city_search_atcs as search_atcs_festival,\n", "            city_non_search_atcs as non_search_atcs_festival,\n", "            city_avg_search_atcs as search_atcs_bau,\n", "            city_avg_non_search_atcs as non_search_atcs_bau,\n", "            city_total_atc as total_atc_festival,\n", "            city_avg_search_contri_atc as search_contri_atc_bau,\n", "            city_avg_total_atc as total_atc_bau,\n", "            try(city_search_device_count / city_avg_search_device_count) as search_spike,\n", "            try(city_total_demand / city_avg_total_demand) as demand_spike,\n", "            try(city_search_atcs / city_search_device_count) as search_conversion_festival,\n", "            try(city_total_atc / city_total_demand) as demand_conversion_festival,\n", "            try(city_avg_search_atcs / city_avg_search_device_count) as search_conversion_bau,\n", "            try(city_avg_total_atc / city_avg_total_demand) as demand_conversion_bau,\n", "            ptype_carts,\n", "            overall_carts,\n", "            ipc_ptype_carts,\n", "            ipc_overall_carts,\n", "            cart_pen,\n", "            actual_sales\n", "            \n", "        from final\n", "     \n", "\n", "\n", "        \"\"\"\n", "\n", "        to_trino(be_ptype, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "76839e3d-526d-4292-9250-ac47a4efaec9", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b66d084f-319e-4477-ac0f-29eaac5f33de", "metadata": {}, "outputs": [], "source": ["# df_be_ptype = read_sql_query(be_ptype, CON_TRINO)\n", "# df_be_ptype\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "de630dd3-e554-47d6-86e9-e351925a6993", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.head()\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "d3cbe65b-4fcc-4f4b-bfd6-c8384cdf4c31", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.dtypes\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "9de672d0-fe2f-4623-8b6c-608a5a9009d1", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_be_ptype[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_be_ptype.columns\n", "# ]\n", "\n", "# column_dtypes\n", "# commented"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}