{"cells": [{"cell_type": "code", "execution_count": null, "id": "3b7d991f-85b6-4f01-81bd-8e70a968acc7", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "54f53e90-e200-4c0c-905e-2762daf14df8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "1a650ae2-4cf7-44aa-a5c3-35fb27587537", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "50a3bb34-fd16-4fc6-b456-7fc641ce1e90", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "8117a762-10cd-4fc1-b248-85855b78ee0a", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "61647c02-9e57-43f3-8074-de7798bbb661", "metadata": {}, "outputs": [], "source": ["cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "c44a32ee-f200-434c-89c3-e89b7fc42d77", "metadata": {}, "outputs": [], "source": ["bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "festival = festive_date_data[\"festival\"].iloc[-1]\n", "gap_days = festive_date_data[\"gap_days\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "d90f81c3-5e24-478d-93d2-6fc41b92a9dc", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'"]}, {"cell_type": "code", "execution_count": null, "id": "2a16081a-7849-4f15-8469-81f94100bd95", "metadata": {}, "outputs": [], "source": ["item_weight = f\"\"\" \n", "with backend_item_weights as (\n", "    select backend_facility_id, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.00000000000 as bi_weights\n", "    from supply_etls.backend_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "item_details as (\n", "    select  \n", "        item_id, item_name, l0_id, l0_category, \n", "                l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "    from supply_etls.item_details\n", "    where assortment_type='Packaged Goods'\n", "        and handling_type = 'Non Packaging Material'\n", "),\n", "\n", "be_ptype_outcome as (\n", "    select \n", "        date_,\n", "        festival_name,\n", "        be_facility_id,\n", "        be_name,\n", "        product_type,\n", "        be_ptype_forecast_cpd,\n", "        be_ptype_forecast_planned,\n", "        be_ptype_forecast\n", "    from supply_etls.festival_forecast_be_ptype\n", "    where date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "    and festival_name is not null\n", "),\n", "\n", "be_ptype_plan_qty as (\n", "    select \n", "        be_facility_id, product_type, festival_name, \n", "        sum(be_ptype_forecast_cpd) as planned_qty\n", "    from be_ptype_outcome\n", "    group by 1,2,3\n", "),\n", "\n", "be_ptype_weights as (\n", "    select \n", "        be_facility_id, product_type, festival_name,\n", "        1.00000000*planned_qty/sum(planned_qty) over (partition by be_facility_id) as ptype_weight\n", "    from be_ptype_plan_qty \n", "),\n", "\n", "\n", "be_item_plan as (\n", "    select\n", "        be_facility_id, product_type, item_id, item_name, festival_name, \n", "        ptype_weight/count(item_id) over (partition by product_type,be_facility_id) as item_weight\n", "    from be_ptype_weights bpw\n", "    join item_details id on bpw.product_type=id.p_type\n", "),\n", "\n", "festive_details as (\n", "    \n", "    select be_facility_id as facility_id, \n", "        item_id, \n", "        festival_name as festival,\n", "        --sale_start_date, \n", "        --sale_end_date, \n", "        --cut_off_date, \n", "        --substitute_ptype, \n", "        --assortment_reason_type,\n", "        --rsto, \n", "        --rtv,\n", "        sum(planned_quantity) as planned_qty\n", "    from ars_etls.final_festival_planning\n", "    where festival_name is not null\n", "    and festival_name = '{festival}' \n", "    group by 1,2,3\n", "\n", "    --select \n", "    --    facility_id, item_id, \n", "    --        --storage, catg_segment,\n", "    --        --festival, festival_bau, sale_start, sale_end, cutt_off, \n", "    --        sum(planned_qty) as planned_qty\n", "    --from supply_etls.final_festival_planning\n", "    --where festival='{festival}'\n", "    --group by 1,2\n", "),\n", "\n", "\n", "festive_plan as (\n", "    select \n", "        fd.facility_id as be_facility_id,\n", "        fd.festival as festival_name,\n", "        id.p_type as product_type,\n", "        id.item_id,\n", "        id.item_name,\n", "        coalesce(try(1.00000000*planned_qty/sum(planned_qty) over (partition by facility_id, p_type)),0) as item_weight\n", "    from festive_details fd\n", "    join item_details id on id.item_id=fd.item_id\n", ")\n", "\n", "select \n", "\n", "    coalesce(fp.be_facility_id, bip.be_facility_id) as be_facility_id,\n", "    coalesce(fp.festival_name, bip.festival_name) as festival_name,\n", "    coalesce(fp.product_type, bip.product_type) as product_type,\n", "    coalesce(fp.item_id, bip.item_id) as item_id,\n", "    coalesce(fp.item_name, bip.item_name) as item_name,\n", "    coalesce(fp.item_weight, bip.item_weight) as item_weight\n", "    \n", "from be_item_plan bip\n", "left join festive_plan fp \n", "    on bip.be_facility_id = fp.be_facility_id\n", "    and bip.festival_name = fp.festival_name\n", "    and bip.item_id = fp.item_id\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c80e9393-5c66-4a9f-a7d8-481967961230", "metadata": {}, "outputs": [], "source": ["# df_item_weight = read_sql_query(item_weight, CON_TRINO)\n", "# df_item_weight"]}, {"cell_type": "code", "execution_count": null, "id": "2ac0b6a0-be60-4a37-b042-97ae946c0b00", "metadata": {}, "outputs": [], "source": ["# df_item_weight.head()"]}, {"cell_type": "code", "execution_count": null, "id": "51f3f1a6-6754-4b23-a2ea-f839db6a2253", "metadata": {}, "outputs": [], "source": ["# df_item_weight.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b7c46731-6964-4cc1-8bb9-b095ec358b33", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_item_weight[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_item_weight.columns\n", "# ]\n", "# a\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "dd664686-3e25-409b-b226-b69dfe1dbbed", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"be_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Warehouse Facility ID\",\n", "    },\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"Item Name\"},\n", "    {\"name\": \"item_weight\", \"type\": \"real\", \"description\": \"Item Weight\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d1691224-d67f-4480-960b-999ac7cdec9e", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_item_weight\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"be_facility_id\",\n", "        \"product_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}\n", "\n", "to_trino(item_weight, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}