{"cells": [{"cell_type": "code", "execution_count": null, "id": "c444a6d5-18f4-4d0b-b89c-2d75bc77104d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4775dcdf-3b7a-40ec-a513-cd3d8b53e153", "metadata": {"tags": []}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b7962273-b8ba-4e38-b2ef-1cc1bcd174ad", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\n", "        \"name\": \"festival_tech_flag\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Indicates whether the festival is considered for tech-based forecasting\",\n", "    },\n", "    {\n", "        \"name\": \"bau_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecasted CPD value based on cart growth and user input cpd_date\",\n", "    },\n", "    {\"name\": \"qty_sold_last_year\", \"type\": \"real\", \"description\": \"Quantity sold in the last year\"},\n", "    {\n", "        \"name\": \"avg_spike_pan_india_ptype\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Average spike in sales at the pan-India level for the product type\",\n", "    },\n", "    {\n", "        \"name\": \"avg_spike_city_ptype\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Average spike in sales at the city level for the product type\",\n", "    },\n", "    {\n", "        \"name\": \"city_ptype_forecast_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecasted CPD value for the city and product type\",\n", "    },\n", "    {\n", "        \"name\": \"city_ptype_forecast_aps\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecasted APS value for the city and product type\",\n", "    },\n", "    {\n", "        \"name\": \"last_year_actual_sales_from_model\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Actual sales from last year's model\",\n", "    },\n", "    {\n", "        \"name\": \"last_year_potential_sales_from_model\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Potential sales estimated from last year's model\",\n", "    },\n", "    {\n", "        \"name\": \"cart_growth_pan_india\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Growth of carts at the pan-India level\",\n", "    },\n", "    {\n", "        \"name\": \"cart_growth_city\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Growth of carts at the city level\",\n", "    },\n", "    {\"name\": \"intent\", \"type\": \"real\", \"description\": \"User purchase intent\"},\n", "    {\n", "        \"name\": \"in_out_forecast_sale\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecasted in-and-out sale value\",\n", "    },\n", "    {\n", "        \"name\": \"final_forecast\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Final adjusted forecasted value considering various conditions\",\n", "    },\n", "    {\n", "        \"name\": \"final_forecast_aps\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Final adjusted forecasted value considering various conditions using APS\",\n", "    },\n", "    {\n", "        \"name\": \"category_bulk_upload_planned_qty\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Planned quantity uploaded in bulk for the category\",\n", "    },\n", "    {\"name\": \"actual_sales\", \"type\": \"real\", \"description\": \"Actual sales observed\"},\n", "    {\n", "        \"name\": \"actual_availability\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Actual product availability in stock\",\n", "    },\n", "    {\"name\": \"actual_intent\", \"type\": \"real\", \"description\": \"Actual User purchase intent\"},\n", "    {\"name\": \"actual_cart_growth\", \"type\": \"real\", \"description\": \"Actual Cart Growth\"},\n", "    {\n", "        \"name\": \"pred_90_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Cart growth predicted at T-90\",\n", "    },\n", "    {\n", "        \"name\": \"pred_60_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Cart growth predicted at T-60\",\n", "    },\n", "    {\n", "        \"name\": \"pred_30_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Cart growth predicted at T-30\",\n", "    },\n", "    {\n", "        \"name\": \"pred_15_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Cart growth predicted at T-15\",\n", "    },\n", "    {\n", "        \"name\": \"pred_10_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Cart growth predicted at T-10\",\n", "    },\n", "    {\n", "        \"name\": \"pred_5_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Cart growth predicted at T-5\",\n", "    },\n", "    {\n", "        \"name\": \"pred_1_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Cart growth predicted at T-1\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_actual_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Actual Cart Growth\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_90_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Cart growth predicted at T-90\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_60_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Cart growth predicted at T-60\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_30_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Cart growth predicted at T-30\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_15_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Cart growth predicted at T-15\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_10_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Cart growth predicted at T-10\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_5_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Cart growth predicted at T-5\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_1_day_forecast_cart_growth\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Pan India Cart growth predicted at T-1\",\n", "    },\n", "    {\n", "        \"name\": \"cy_actual_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Current Year Actual CPD at Sale Start - 1 day\",\n", "    },\n", "    {\n", "        \"name\": \"cy_actual_potential_sales\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Current Year Actual Potential Sales at Sale Start - 1 day\",\n", "    },\n", "    {\n", "        \"name\": \"cy_search_device_count_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Searches (Festival)\",\n", "    },\n", "    {\"name\": \"cy_total_demand_festival\", \"type\": \"real\", \"description\": \"Total Demand (Festival)\"},\n", "    {\"name\": \"search_device_count_bau\", \"type\": \"real\", \"description\": \"null\"},\n", "    {\"name\": \"total_demand_bau\", \"type\": \"real\", \"description\": \"null\"},\n", "    {\"name\": \"cy_search_device_count_bau\", \"type\": \"real\", \"description\": \"Total Searches (BAU)\"},\n", "    {\"name\": \"cy_total_demand_bau\", \"type\": \"real\", \"description\": \"Total Demand (BAU)\"},\n", "    {\n", "        \"name\": \"cy_search_contri_atc_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution to ATC (Festival)\",\n", "    },\n", "    {\n", "        \"name\": \"cy_search_atcs_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Search ATC (Festival)\",\n", "    },\n", "    {\n", "        \"name\": \"cy_non_search_atcs_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Non Search ATC (Festival)\",\n", "    },\n", "    {\"name\": \"cy_search_atcs_bau\", \"type\": \"real\", \"description\": \"Total Search ATC (BAU)\"},\n", "    {\"name\": \"cy_non_search_atcs_bau\", \"type\": \"real\", \"description\": \"Total Non-Search ATC (BAU)\"},\n", "    {\"name\": \"cy_total_atc_festival\", \"type\": \"real\", \"description\": \"Total ATC (Festival)\"},\n", "    {\n", "        \"name\": \"cy_search_contri_atc_bau\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution to ATC (BAU)\",\n", "    },\n", "    {\"name\": \"cy_total_atc_bau\", \"type\": \"real\", \"description\": \"Total ATC (BAU)\"},\n", "    {\"name\": \"cy_search_spike\", \"type\": \"real\", \"description\": \"Search Spike\"},\n", "    {\"name\": \"cy_demand_spike\", \"type\": \"real\", \"description\": \"Demand Spike\"},\n", "    {\n", "        \"name\": \"cy_search_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Conversion (Festival)\",\n", "    },\n", "    {\n", "        \"name\": \"cy_demand_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Demand Conversion (Festival)\",\n", "    },\n", "    {\"name\": \"cy_search_conversion_bau\", \"type\": \"real\", \"description\": \"Search Conversion (BAU)\"},\n", "    {\"name\": \"cy_demand_conversion_bau\", \"type\": \"real\", \"description\": \"Demand Conversion (BAU)\"},\n", "    {\n", "        \"name\": \"py_actual_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Actual CPD based on user input cpd_date\",\n", "    },\n", "    {\n", "        \"name\": \"py_actual_potential_sales\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Actual Potential Sales based on user input cpd_date\",\n", "    },\n", "    {\n", "        \"name\": \"py_search_device_count_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Searches (Festival)\",\n", "    },\n", "    {\"name\": \"py_total_demand_festival\", \"type\": \"real\", \"description\": \"Total Demand (Festival)\"},\n", "    {\"name\": \"py_search_device_count_bau\", \"type\": \"real\", \"description\": \"Total Searches (BAU)\"},\n", "    {\"name\": \"py_total_demand_bau\", \"type\": \"real\", \"description\": \"Total Demand (BAU)\"},\n", "    {\n", "        \"name\": \"py_search_contri_atc_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution to ATC (Festival)\",\n", "    },\n", "    {\n", "        \"name\": \"py_search_atcs_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Search ATC (Festival)\",\n", "    },\n", "    {\n", "        \"name\": \"py_non_search_atcs_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Non Search ATC (Festival)\",\n", "    },\n", "    {\"name\": \"py_search_atcs_bau\", \"type\": \"real\", \"description\": \"Total Search ATC (BAU)\"},\n", "    {\"name\": \"py_non_search_atcs_bau\", \"type\": \"real\", \"description\": \"Total Non-Search ATC (BAU)\"},\n", "    {\"name\": \"py_total_atc_festival\", \"type\": \"real\", \"description\": \"Total ATC (Festival)\"},\n", "    {\n", "        \"name\": \"py_search_contri_atc_bau\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution to ATC (BAU)\",\n", "    },\n", "    {\"name\": \"py_total_atc_bau\", \"type\": \"real\", \"description\": \"Total ATC (BAU)\"},\n", "    {\"name\": \"py_search_spike\", \"type\": \"real\", \"description\": \"Search Spike\"},\n", "    {\"name\": \"py_demand_spike\", \"type\": \"real\", \"description\": \"Demand Spike\"},\n", "    {\n", "        \"name\": \"py_search_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Conversion (Festival)\",\n", "    },\n", "    {\n", "        \"name\": \"py_demand_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Demand Conversion (Festival)\",\n", "    },\n", "    {\"name\": \"py_search_conversion_bau\", \"type\": \"real\", \"description\": \"Search Conversion (BAU)\"},\n", "    {\"name\": \"py_demand_conversion_bau\", \"type\": \"real\", \"description\": \"Demand Conversion (BAU)\"},\n", "    {\"name\": \"cy_ptype_carts\", \"type\": \"real\", \"description\": \"Carts of that Ptype\"},\n", "    {\"name\": \"cy_overall_carts\", \"type\": \"real\", \"description\": \"All carts\"},\n", "    {\n", "        \"name\": \"cy_ipc_ptype_carts\",\n", "        \"type\": \"real\",\n", "        \"description\": \"IPC considering carts of that ptype\",\n", "    },\n", "    {\"name\": \"cy_ipc_overall_carts\", \"type\": \"real\", \"description\": \"IPC considering all carts\"},\n", "    {\"name\": \"cy_cart_pen\", \"type\": \"real\", \"description\": \"Cart Pen\"},\n", "    {\"name\": \"py_ptype_carts\", \"type\": \"real\", \"description\": \"Carts of that Ptype\"},\n", "    {\"name\": \"py_overall_carts\", \"type\": \"real\", \"description\": \"All carts\"},\n", "    {\n", "        \"name\": \"py_ipc_ptype_carts\",\n", "        \"type\": \"real\",\n", "        \"description\": \"IPC considering carts of that ptype\",\n", "    },\n", "    {\"name\": \"py_ipc_overall_carts\", \"type\": \"real\", \"description\": \"IPC considering all carts\"},\n", "    {\"name\": \"py_cart_pen\", \"type\": \"real\", \"description\": \"Cart Pen\"},\n", "    {\"name\": \"cy_actual_sales\", \"type\": \"real\", \"description\": \"Actual Qty Sold\"},\n", "    {\n", "        \"name\": \"cart_growth_max_date\",\n", "        \"type\": \"date\",\n", "        \"description\": \"Max available date of Cart Projections\",\n", "    },\n", "    {\n", "        \"name\": \"bau_potential_sales\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecasted Potential Sales based on cart growth and user input cpd_date\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ec78ac0-465d-4cfe-996f-1bc60d2d3970", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_final_date_city_ptype\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city_name\",\n", "        \"product_type\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "cf6b8ad1-4cb8-4372-9ede-7a4dee4540af", "metadata": {}, "outputs": [], "source": ["# def escape_single_quotes(value):\n", "#     return value.replace(\"'\", \"''\")"]}, {"cell_type": "code", "execution_count": null, "id": "d64b05c8-aa93-4e55-be06-89f0fc8afa73", "metadata": {}, "outputs": [], "source": ["# df_group = pb.from_sheets(\n", "#     \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"grouped_ptypes\", clear_cache=True\n", "# )\n", "\n", "# df_group[\"group\"] = df_group[\"group\"].apply(escape_single_quotes)\n", "# df_group[\"ptype\"] = df_group[\"ptype\"].apply(escape_single_quotes)\n", "\n", "# result = {}\n", "\n", "# ### Group by festival, then group by group within each festival\n", "# for festival, festival_group in df_group.groupby(\"festival\"):\n", "\n", "#     festival_dict = {}\n", "\n", "#     for group, group_group in festival_group.groupby(\"group\"):\n", "#         elements = tuple(group_group[\"ptype\"])\n", "#         festival_dict[group] = elements\n", "\n", "#     result[festival] = festival_dict\n", "\n", "# groups = result.copy()\n", "\n", "# del (df_group, festival_group, result)"]}, {"cell_type": "code", "execution_count": null, "id": "def7eaea-914b-4ad2-b02f-81fce6b4236e", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# festival_to_consider_for_intent = 'Shradh'\n", "# festival_name_in_bulk_upload = 'Shradh'\n", "# gap_days = '353'\n", "# #commented"]}, {"cell_type": "code", "execution_count": null, "id": "0ae9bb4d-e983-4a93-ba15-abe8d12a233e", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5a80c1e7-b4c3-479f-a41e-104f7a34e2b3", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "        \"hero_sku_availability\",\n", "        \"hero_sku_sales_contribution\",\n", "        \"overwrite_flag\",\n", "        \"festival_to_consider_for_intent\",\n", "        \"festival_name_in_bulk_upload\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a7ca20a2-8e1a-4f8d-b70a-f94b9c84af68", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "da73be4d-482b-4632-8663-7cda52d8092d", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8007f82a-9864-460c-ad48-97b6916656a8", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "7a6ee357-cdb9-4418-bfee-ccca0746647b", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"overwrite_flag\"]] = festive_date_data[[\"overwrite_flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "3fb4aae7-7c65-4c02-86fd-af665650a489", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[\n", "    (festive_date_data[\"flag\"] == 1) | (festive_date_data[\"overwrite_flag\"] == 1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6058ddee-bd10-4a2d-90e6-b2c9b2c7b4ac", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5c4d67fb-4cf6-482c-bdd0-0b5e1cca8d1f", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3b6f7c8-40c0-4536-a288-c0de31ae5c9d", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "b5bf5c0c-b6a7-4739-9e39-91d5d5d340cc", "metadata": {}, "outputs": [], "source": ["# festive_date_data = pd.DataFrame()\n", "# festive_date_data['festival'] = ['KANJAK_APRIL_2025']\n", "# festive_date_data['festival_start_date'] = ['2024-10-08']\n", "# festive_date_data['sale_end_date'] = ['2025-04-07']\n", "# festive_date_data['sale_start_date'] = ['2025-04-04']\n", "# festive_date_data['gap_days'] = [178]\n", "# festive_date_data['festival_to_consider_for_intent'] = [\"'VALENTINES_DAY_2025-02-14'\"]\n", "# festive_date_data['festival_name_in_bulk_upload'] = ['KANJAK_APRIL_2025']"]}, {"cell_type": "code", "execution_count": null, "id": "d13fc819-b7f7-4671-a00e-18646524fe10", "metadata": {}, "outputs": [], "source": ["# df_group = pd.DataFrame()\n", "\n", "# df_group['festival'] = ['KANJAK_APRIL_2025','KANJAK_APRIL_2025']\n", "# df_group['group'] = ['Frooti Single', 'Frooti Pack of 10']\n", "# df_group['ptype'] = ['Mango Drink','Mango Drink']\n", "\n", "# df_group[\"group\"] = df_group[\"group\"].apply(escape_single_quotes)\n", "# df_group[\"ptype\"] = df_group[\"ptype\"].apply(escape_single_quotes)\n", "\n", "# result = {}\n", "\n", "# ### Group by festival, then group by group within each festival\n", "# for festival, festival_group in df_group.groupby(\"festival\"):\n", "\n", "#     festival_dict = {}\n", "\n", "#     for group, group_group in festival_group.groupby(\"group\"):\n", "#         elements = tuple(group_group[\"ptype\"])\n", "#         festival_dict[group] = elements\n", "\n", "#     result[festival] = festival_dict\n", "\n", "# groups = result.copy()\n", "\n", "# del (df_group, festival_group, result)"]}, {"cell_type": "code", "execution_count": null, "id": "420fa915-7af1-4d3b-ac50-2fadbc607662", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        # cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        # bau_start_date = row[\"bau_start_date\"]\n", "        # bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        # festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "        festival_to_consider_for_intent = row[\"festival_to_consider_for_intent\"]\n", "        festival_name_in_bulk_upload = row[\"festival_name_in_bulk_upload\"]\n", "\n", "        #         if festival not in groups:\n", "        #             sql_condition = \"FALSE\"\n", "        #             cte_query = \"\"\"group_ptypes AS (\n", "        #                 SELECT\n", "        #                     NULL AS festival_name,\n", "        #                     NULL AS product_type,\n", "        #                     CAST(0 AS DOUBLE) AS festival_tech_flag,\n", "        #                     CAST(NULL AS DECIMAL(38,6)) AS cpd,\n", "        #                     CAST(NULL AS DECIMAL(38,6)) AS bau_sale,\n", "        #                     CAST(NULL AS DECIMAL(38,6)) AS qty_sold_last_year,\n", "        #                     CAST(NULL AS DECIMAL(38,6)) AS city_ptype_forecast_cpd,\n", "        #                     CAST(NULL AS DECIMAL(38,6)) AS avg_spike,\n", "        #                     CAST(NULL AS DECIMAL(38,6)) AS city_ptype_forecast_aps\n", "        #             ),\n", "        #             \"\"\"\n", "        #             cte_query_group = \"\"\"ptype_groups AS (\n", "        #                 SELECT\n", "        #                     NULL AS festival_name,\n", "        #                     NULL AS group_name,\n", "        #                     NULL AS product_type\n", "        #             ),\n", "        #             \"\"\"\n", "\n", "        #         else:\n", "        #             # Filter conditions for this festival\n", "        #             festival_conditions = []\n", "        #             grp_lst = list(groups[festival].keys())\n", "        #             # for group, ptypes in groups[festival].items():\n", "        #             #     grp_lst += list(ptypes)\n", "        #             #     # ptype_list = \"', '\".join(ptypes)  # Convert tuple to comma-separated string\n", "        #             #     # festival_conditions.append(f\"(product_type = '{group}' OR product_type IN ('{ptype_list}'))\")\n", "\n", "        #             grp_str = \"', '\".join(grp_lst)\n", "        #             sql_condition = f\"product_type in ('{grp_str}')\"\n", "        #             # sql_condition = \" OR \".join(festival_conditions)\n", "\n", "        #             # Generate SQL CTE\n", "        #             cte_query = \"group_ptypes AS (\\n\"\n", "        #             cte_rows = []\n", "\n", "        #             cte_query_group = \"ptype_groups AS (\\n\"\n", "        #             cte_rows_group = []\n", "\n", "        #             for group, ptypes in groups[festival].items():\n", "        #                 cte_rows.append(\n", "        #                     f\"    SELECT '{festival}' AS festival_name, \"\n", "        #                     f\"'{group}' AS product_type, \"\n", "        #                     f\"CAST(1 AS DOUBLE) AS festival_tech_flag, \"\n", "        #                     f\"CAST(NULL AS DECIMAL(38,6)) AS cpd, \"\n", "        #                     f\"CAST(NULL AS DECIMAL(38,6)) AS bau_sale, \"\n", "        #                     f\"CAST(NULL AS DECIMAL(38,6)) AS qty_sold_last_year, \"\n", "        #                     f\"CAST(NULL AS DECIMAL(38,6)) AS city_ptype_forecast_cpd, \"\n", "        #                     f\"CAST(NULL AS DECIMAL(38,6)) AS avg_spike, \"\n", "        #                     f\"CAST(NULL AS DECIMAL(38,6)) AS city_ptype_forecast_aps\"\n", "        #                 )\n", "        #                 for ptype in ptypes:\n", "        #                     cte_rows_group.append(\n", "        #                         f\"    SELECT '{festival}' AS festival_name, '{group}' AS group_name, '{ptype}' AS product_type\"\n", "        #                     )\n", "\n", "        #             cte_rows = list(set(cte_rows))\n", "        #             cte_rows_group = list(set(cte_rows_group))\n", "        #             cte_query += \" UNION ALL\\n\".join(cte_rows) + \"\\n),\\n\"\n", "        #             cte_query_group += \" UNION ALL\\n\".join(cte_rows_group) + \"\\n),\\n\"\n", "\n", "        #         print(sql_condition)\n", "        #         print(cte_query)\n", "        #         print(cte_query_group)\n", "\n", "        be_ptype = f\"\"\" \n", "\n", "        WITH \n", "        city_breakdown_base as (\n", "            select \n", "\n", "                city_name,\n", "                product_type,\n", "                sum(store_ratio) as city_weight\n", "\n", "            from supply_etls.festival_forecast_store_dist_ptype_ordering\n", "\n", "            where festival_name='{festival}'\n", "                and city_name is not null\n", "                and product_type is not null\n", "\n", "            group by 1,2\n", "        ),\n", "\n", "        dod_breakdown_base as (\n", "            select \n", "\n", "            date_,\n", "            p_type as product_type,\n", "            day_weight\n", "\n", "            from supply_etls.ptype_festival_weights_dod\n", "\n", "            where festival_name='{festival}'\n", "                and date_ is not null\n", "                and p_type is not null\n", "        ),\n", "\n", "\n", "        ptype_groups as (\n", "        SELECT \n", "            distinct festival as festival_name,\n", "            group_name,\n", "            ptype as product_type\n", "        FROM supply_etls.festival_forecast_group_ptype_mapping\n", "        where festival = '{festival}' \n", "        ),\n", "\n", "        city_breakdown_groups as (\n", "            select\n", "\n", "                cb.city_name,\n", "                pg.group_name as product_type,\n", "                1.000000*avg(city_weight) as city_weight\n", "\n", "            from city_breakdown_base cb\n", "            join (select * from ptype_groups \n", "                    where group_name not in (select distinct product_type from city_breakdown_base)) pg \n", "                on cb.product_type=pg.product_type\n", "\n", "            group by 1,2\n", "        ),\n", "\n", "        dod_breakdown_groups as (\n", "            select\n", "\n", "                db.date_,\n", "                pg.group_name as product_type,\n", "                1.000000*avg(day_weight) as day_weight\n", "\n", "            from dod_breakdown_base db\n", "            join (select * from ptype_groups \n", "                    where group_name not in (select distinct product_type from dod_breakdown_base)) pg \n", "                on db.product_type=pg.product_type\n", "\n", "            group by 1,2\n", "        ),\n", "\n", "        city_breakdown as (\n", "            select \n", "                * \n", "            from (select * from city_breakdown_base\n", "                        union\n", "                    select * from city_breakdown_groups) \n", "            where product_type is not null\n", "        ),\n", "\n", "        dod_breakdown as (\n", "            select \n", "                * \n", "            from (select * from dod_breakdown_base\n", "                        union\n", "                    select * from dod_breakdown_groups) \n", "            where product_type is not null\n", "        ),\n", "\n", "        group_ptypes as (\n", "        SELECT distinct festival as festival_name,\n", "                group_name AS product_type,\n", "                CAST(1 AS DOUBLE) AS festival_tech_flag,\n", "                CAST(NULL AS DECIMAL(38,6)) AS cpd,\n", "                CAST(NULL AS DECIMAL(38,6)) AS bau_sale,\n", "                CAST(NULL AS DECIMAL(38,6)) AS qty_sold_last_year,\n", "                CAST(NULL AS DECIMAL(38,6)) AS city_ptype_forecast_cpd,\n", "                CAST(NULL AS DECIMAL(38,6)) AS avg_spike,\n", "                CAST(NULL AS DECIMAL(38,6)) AS city_ptype_forecast_aps\n", "        FROM supply_etls.festival_forecast_group_ptype_mapping\n", "        where festival = '{festival}'\n", "\n", "        ),\n", "\n", "        bau_forecast_base as (\n", "            SELECT \n", "\n", "                festival_name, \n", "                product_type, \n", "                festival_tech_flag, \n", "                sum(cpd)/count(distinct date_) as cpd, \n", "                sum(cpd) as bau_sale, \n", "                sum(qty_sold) as qty_sold_last_year,\n", "                sum(city_ptype_forecast_cpd) as city_ptype_forecast_cpd, \n", "                sum(city_ptype_forecast_cpd)/sum(cpd) as avg_spike,\n", "                sum(city_ptype_forecast_aps) as city_ptype_forecast_aps\n", "\n", "            FROM supply_etls.festival_forecast_city_ptype_flag\n", "\n", "            WHERE festival_name='{festival}'\n", "                and product_type is not null\n", "\n", "            group by 1,2,3\n", "        ),\n", "\n", "        bau_forecast as (\n", "            SELECT \n", "                *\n", "            from (select * from bau_forecast_base \n", "                            union \n", "                        (select * from group_ptypes where product_type not in (select distinct product_type from bau_forecast_base)))\n", "            where product_type is not null  \n", "        ),\n", "\n", "        bau_forecast_city as (\n", "            SELECT \n", "\n", "                festival_name, \n", "                date_,\n", "                city,\n", "                product_type, \n", "                festival_tech_flag, \n", "                cpd, \n", "                qty_sold as qty_sold_last_year,\n", "                city_ptype_forecast_cpd, \n", "                city_ptype_forecast_cpd/cpd as avg_spike,\n", "                city_ptype_forecast_aps,\n", "                ptype_carts,\n", "                overall_carts,\n", "                ipc_ptype_carts,\n", "                ipc_overall_carts,\n", "                cart_pen,\n", "                actual_potential_sales,\n", "                actual_cpd,\n", "                search_device_count_festival,\n", "                total_demand_festival,\n", "                search_device_count_bau,\n", "                total_demand_bau,\n", "                search_contri_atc_festival,\n", "                search_atcs_festival,\n", "                non_search_atcs_festival,\n", "                search_atcs_bau,\n", "                non_search_atcs_bau,\n", "                total_atc_festival,\n", "                search_contri_atc_bau,\n", "                total_atc_bau,\n", "                search_spike,\n", "                demand_spike,\n", "                search_conversion_festival,\n", "                demand_conversion_festival,\n", "                search_conversion_bau,\n", "                demand_conversion_bau,\n", "                potential_sales\n", "                \n", "\n", "            FROM supply_etls.festival_forecast_city_ptype_flag\n", "\n", "            WHERE festival_name='{festival}'\n", "        ),\n", "\n", "        in_out_forecast as (\n", "            select\n", "\n", "                festival,\n", "                category as product_type, \n", "                sum(sale_qty_fe_hr) as last_year_actual_sales,\n", "                sum(proposed_sales) as last_year_potential_sales\n", "\n", "            from supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "            --supply_etls.festival_forecast_pan_india_ptype_in_out\n", "            where festival='{festival}' \n", "            and run_id in (select max(run_id) from supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr where festival='{festival}')\n", "            group by 1,2\n", "        ),\n", "\n", "        in_out_forecast_city as (\n", "            select\n", "\n", "                festival,\n", "                \"date\" + interval '{gap_days}' day as date_,\n", "                city,\n", "                category as product_type, \n", "                sum(sale_qty_fe_hr) as last_year_actual_sales,\n", "                sum(proposed_sales) as last_year_potential_sales\n", "\n", "            from supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr\n", "            --supply_etls.festival_forecast_pan_india_ptype_in_out\n", "            where festival='{festival}' \n", "            and run_id in (select max(run_id) from supply_etls.festival_forecast_outlet_ptype_in_out_dod_hr where festival='{festival}')\n", "            group by 1,2,3,4\n", "        ),\n", "\n", "        city_weight as (\n", "            select \n", "                city,\n", "                cast(weight as double) as weight\n", "            from supply_etls.city_weights\n", "            where updated_at=(select max(updated_at) from supply_etls.city_weights)\n", "        ),\n", "\n", "        -- demand_intent_growth as (\n", "        --     with base as (\n", "        --         select \n", "        --             -- * \n", "        --             orig_fest_name as festival_name,\n", "        --             cig.city,\n", "        --             demand_intent_growth as demand_intent_growth,\n", "        --             weight\n", "        --         from supply_etls.festival_forecast_city_wise_intent_growth cig\n", "        --         join city_weight cw on cw.city=cig.city\n", "        --         where orig_fest_name in ({festival_to_consider_for_intent})\n", "        --         -- and demand_intent_growth < 10\n", "        --          ),\n", "\n", "        --         final as (\n", "\n", "        --         select\n", "        --             festival_name,\n", "        --             sum(1.000000*demand_intent_growth*weight)/sum(weight) as weighted_intent\n", "        --         from base\n", "        --         group by 1\n", "        --         )\n", "\n", "        --         select \n", "        --             avg(weighted_intent) as intent \n", "        --         from final\n", "        -- ),\n", "\n", "\n", "        demand_intent_growth as (\n", "            select \n", "                avg(case when orig_fest_name in ({festival_to_consider_for_intent}) then demand_intent_growth end) as intent,\n", "                avg(case when orig_fest_name = '{festival}' then demand_intent_growth end) as actual_intent\n", "            from supply_etls.festival_forecast_city_wise_intent_growth cig\n", "            where orig_fest_name in ({festival_to_consider_for_intent}, '{festival}')\n", "            and city='PAN INDIA'\n", "        ),\n", "\n", "        max_available_date AS (\n", "            SELECT \n", "                MAX(\"date\") AS max_date\n", "            FROM supply_etls.festival_forecast_cart_growth\n", "                where city='PAN INDIA'\n", "        ),\n", "\n", "        fallback_date AS (\n", "            SELECT \n", "                CASE \n", "                    WHEN date('{sale_start_date}') > (SELECT max_date FROM max_available_date) \n", "                    THEN (SELECT max_date FROM max_available_date) \n", "                    ELSE date('{sale_start_date}')\n", "                END AS effective_date\n", "        ),\n", "\n", "        cart_growth as (\n", "            select \n", "                sum(case when date = (SELECT effective_date FROM fallback_date) - interval '1' day then rolling_orders else null end) / \n", "                sum(case when date = date('{festival_start_date}') - interval '1' day then rolling_orders else null end) as cart_growth\n", "            from supply_etls.festival_forecast_cart_growth\n", "            where city='PAN INDIA'\n", "                and (date = date('{festival_start_date}') - interval '1' day or date = (SELECT effective_date FROM fallback_date) - interval '1' day)\n", "        ),\n", "\n", "        cart_growth_city as (\n", "            select \n", "                (case when city in ('HR-NCR') then 'Gurgaon'\n", "                    when city in ('UP-NCR') then 'Ghaziabad'\n", "                    else city end) as city,\n", "                sum(case when date = (SELECT effective_date FROM fallback_date) - interval '1' day then rolling_orders else null end) as current_carts,\n", "                sum(case when date = date('{festival_start_date}') - interval '1' day then rolling_orders else null end) as last_year_carts,\n", "                sum(case when date = (SELECT effective_date FROM fallback_date) - interval '1' day then rolling_orders else null end) / \n", "                sum(case when date = date('{festival_start_date}') - interval '1' day then rolling_orders else null end) as cart_growth\n", "            from supply_etls.festival_forecast_cart_growth\n", "            where city<>'PAN INDIA'\n", "                and (date = date('{festival_start_date}') - interval '1' day or date = (SELECT effective_date FROM fallback_date) - interval '1' day)\n", "            group by 1\n", "        ),\n", "\n", "        outlet_details as (\n", "            select \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "            from supply_etls.outlet_details\n", "            -- where ars_check=1\n", "            -- and grocery_active_count >= 1\n", "            -- and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "\n", "        item_details as (\n", "            select --current_date as updated_at, \n", "                item_id, item_name, l0_id, l0_category, \n", "                l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            -- where assortment_type='Packaged Goods'\n", "            -- and handling_type = 'Non Packaging Material'\n", "        ),\n", "\n", "        item_product_mapping as (\n", "            select item_id, product_id, multiplier\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current\n", "        ),\n", "\n", "        -- sales as (\n", "\n", "        --     select \n", "        --         --date(cart_checkout_ts_ist) as date_, fs.outlet_id, ip.item_id,\n", "        --         id.p_type as product_type,\n", "        --         sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price) as gmv\n", "        --     from dwh.fact_sales_order_item_details fs\n", "        --     inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "        --     inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "        --     inner join item_details id on id.item_id=ip.item_id\n", "        --     where fs.order_create_dt_ist between  date('{sale_start_date}') and date('{sale_end_date}')\n", "        --     and fs.order_current_status = 'DELIVERED'\n", "        --     group by 1--,2,3\n", "        -- ),\n", "\n", "        sales_city as (\n", "\n", "            select \n", "                date(cart_checkout_ts_ist) as date_,\n", "                od.city_name,\n", "                id.p_type as product_type,\n", "                sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price) as gmv\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            inner join item_details id on id.item_id=ip.item_id\n", "            where fs.order_create_dt_ist between  date('{sale_start_date}') and date('{sale_end_date}')\n", "            and fs.order_current_status = 'DELIVERED'\n", "            group by 1,2,3\n", "        ),\n", "\n", "        -- avail as (\n", "        --     with base as (\n", "        --         select\n", "        --             (case when city in ('HR-NCR') then 'Gurgaon'\n", "        --                 when city in ('UP-NCR') then 'Ghaziabad'\n", "        --                 else city end) as city,\n", "        --             product_type,\n", "        --             sum(ptype_avg_availability)/cast(DATE_DIFF('day', DATE('{sale_start_date}'), DATE('{sale_end_date}')) as int) as ptype_avg_availability\n", "        --         from ds_etls.demand_forecasting_ptype_city_daily_availability\n", "        --         where date >= ('{sale_start_date}') and date <= ('{sale_end_date}')\n", "        --         group by 1,2\n", "        --     )\n", "\n", "        --     select\n", "        --         product_type,\n", "        --         sum(ptype_avg_availability*weight)/sum(weight) as ptype_avg_availability\n", "        --     from base\n", "        --     join city_weight cw on base.city=cw.city\n", "        --     group by 1\n", "        -- ),\n", "\n", "        avail_city as (\n", "            select\n", "                \"date\",\n", "                (case when city in ('HR-NCR') then 'Gurgaon'\n", "                    when city in ('UP-NCR') then 'Ghaziabad'\n", "                    else city end) as city,\n", "                product_type,\n", "                ptype_avg_availability\n", "            from ds_etls.demand_forecasting_ptype_city_daily_availability\n", "            where date >= ('{sale_start_date}') and date <= ('{sale_end_date}')\n", "        ),\n", "\n", "        event_name as (\n", "            SELECT sei.id AS id,\n", "                CONCAT(name, '_', cast(start_date as varchar)) AS event_name\n", "            FROM rpc.supply_event_info sei\n", "            JOIN rpc.supply_event se ON se.id = sei.supply_event_id\n", "            WHERE sei.active = TRUE\n", "              AND se.active = TRUE\n", "            ORDER BY sei.id DESC\n", "        ),\n", "\n", "        store_level_data as (\n", "            select \n", "                event_id, \n", "                outlet_id,\n", "                item_id, \n", "                quantity\n", "            from ars.event_outlet_item_distribution where partition_field is not null and active = 1 \n", "        ),\n", "\n", "        planned_qty_city as (\n", "            select\n", "                event_name as festival_name,\n", "                city_name,\n", "                p_type as product_type,\n", "                sum(quantity) as planned_qty\n", "            from store_level_data sld\n", "            join outlet_details od on od.hot_outlet_id=sld.outlet_id\n", "            join event_name en on en.id=sld.event_id\n", "            join item_details id on id.item_id=sld.item_id\n", "            where event_name='{festival_name_in_bulk_upload}'\n", "            group by 1,2,3\n", "        ),\n", "\n", "        -- planned_qty_pan_india as (\n", "        --     select \n", "        --         event_name as festival_name,\n", "        --         p_type as product_type,\n", "        --         sum(planned_qty) as planned_qty\n", "        --     from ars.bulk_process_event_planner bpep\n", "        --     join event_name en on bpep.event_id=en.id\n", "        --     join item_details id on id.item_id=bpep.item_id\n", "        --     where partition_field is not null and active = 1\n", "        --     and event_name='{festival_name_in_bulk_upload}'\n", "        --     group by 1,2\n", "        -- )\n", "\n", "        max_updated_proj_carts as(\n", "            select \n", "                \"date\",\n", "                outletid,\n", "                date(updated_on) as updated_date,\n", "                max(updated_on) as updated_on\n", "            from logistics_data_etls.cart_projections cp\n", "            group by 1,2,3\n", "        ),\n", "\n", "        proj_carts_base as(\n", "            select\n", "                cp.\"date\",\n", "                (case when co_map.city_name in ('Gurgaon') then 'HR-NCR'\n", "                     when co_map.city_name in ('Ghaziabad') then 'UP-NCR'\n", "                     else co_map.city_name end) as city,\n", "                cp.<PERSON><PERSON>,\n", "                cp.carts,\n", "                avg(cp.carts) over(partition by cp.city, cp.updated_on order by cp.\"date\" rows between 7 preceding and current row) as rolling_orders,\n", "                mupc.updated_date\n", "            from logistics_data_etls.cart_projections cp\n", "            inner join \n", "                (select distinct outlet_id, cl.name as city_name\n", "                from po.physical_facility_outlet_mapping pfom\n", "                inner join retail.console_location cl on pfom.city_id = cl.id) co_map \n", "                on co_map.outlet_id = cp.outletid\n", "            inner join max_updated_proj_carts mupc on mupc.\"date\" = cp.\"date\"\n", "                and mupc.outletid = cp.outletid\n", "                and mupc.updated_on = cp.updated_on\n", "            where cp.\"date\" between date('{sale_start_date}') - interval '8' day and date('{sale_start_date}') - interval '1' day\n", "                and date(cp.updated_on) in (date('{sale_start_date}') - interval '91' day,\n", "                                         date('{sale_start_date}') - interval '61' day,\n", "                                         date('{sale_start_date}') - interval '31' day,\n", "                                         date('{sale_start_date}') - interval '16' day,\n", "                                         date('{sale_start_date}') - interval '11' day,\n", "                                         date('{sale_start_date}') - interval '6' day,\n", "                                         date('{sale_start_date}') - interval '2' day)\n", "\n", "        ),\n", "\n", "        proj_carts as(\n", "            select \n", "                \"date\" as date_,\n", "                city,\n", "                case when updated_date = date('{sale_start_date}') - interval '91' day then '90_day_forecast'\n", "                     when updated_date = date('{sale_start_date}') - interval '61' day then '60_day_forecast'\n", "                     when updated_date = date('{sale_start_date}') - interval '31' day then '30_day_forecast'\n", "                     when updated_date = date('{sale_start_date}') - interval '16' day then '15_day_forecast'\n", "                     when updated_date = date('{sale_start_date}') - interval '11' day then '10_day_forecast'\n", "                     when updated_date = date('{sale_start_date}') - interval '6' day then '5_day_forecast'\n", "                     when updated_date = date('{sale_start_date}') - interval '2' day then '1_day_forecast'\n", "                end as forecast_time,\n", "                sum(carts) as proj_carts,\n", "                sum(rolling_orders) as rolling_proj_carts\n", "            from proj_carts_base cp\n", "            where date = date('{sale_start_date}') - interval '1' day\n", "            group by 1,2,3\n", "        ),\n", "\n", "        actual_carts_base as(\n", "            select date_,\n", "            city,\n", "            orders,\n", "            avg(orders) over (partition by city order by date_ rows between 7 preceding and current row) as rolling_orders\n", "\n", "        from (\n", "            select \n", "                date(cart_checkout_ts_ist) date_, \n", "                city_name as city,\n", "                count(distinct order_id) as orders\n", "\n", "            from dwh.fact_sales_order_details\n", "            where \n", "                order_current_status = 'DELIVERED'\n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "                and (order_create_dt_ist between date('{festival_start_date}') - interval '10' day and date('{festival_start_date}') \n", "                    or order_create_dt_ist between date('{sale_start_date}') - interval '10' day and date('{sale_start_date}'))\n", "            group by 1, 2\n", "            )\n", "        ),\n", "\n", "        actual_carts as(\n", "            select\n", "                city,\n", "                case when date_ = date('{festival_start_date}') - interval '1' day\n", "                        then 'festival_start'\n", "                     when date_ = date('{sale_start_date}') - interval '1' day\n", "                        then 'sale_start'\n", "                    end as cart_date,\n", "                sum(orders) as total_carts,\n", "                sum(rolling_orders) as rolling_total_carts\n", "            from actual_carts_base\n", "            where date_ = date('{festival_start_date}') - interval '1' day -- fest start\n", "                or date_ = date('{sale_start_date}') - interval '1' day -- sale start\n", "            group by 1,2\n", "        ),\n", "\n", "        actual_cart_growth as(\n", "            select \n", "                ac.city,\n", "\n", "                max(1.0000 * ac2.rolling_total_carts / ac.rolling_total_carts) as actual_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '90_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as \"90_day_forecast_cart_growth\",\n", "\n", "                max(case when pc.forecast_time = '60_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as \"60_day_forecast_cart_growth\",\n", "\n", "                max(case when pc.forecast_time = '30_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as \"30_day_forecast_cart_growth\",\n", "\n", "                max(case when pc.forecast_time = '15_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as \"15_day_forecast_cart_growth\",\n", "\n", "                max(case when pc.forecast_time = '10_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as \"10_day_forecast_cart_growth\",\n", "\n", "                max(case when pc.forecast_time = '5_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as \"5_day_forecast_cart_growth\",\n", "\n", "                max(case when pc.forecast_time = '1_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as \"1_day_forecast_cart_growth\"\n", "\n", "            from (select * from actual_carts where cart_date = 'festival_start')  ac\n", "            left join (select * from actual_carts where cart_date = 'sale_start') ac2 on ac.city = ac2.city \n", "            left join proj_carts pc on pc.city = ac.city\n", "            group by 1\n", "        ),\n", "        \n", "        actual_cart_growth_pan_india as(\n", "            select\n", "\n", "                max(1.0000 * ac2.rolling_total_carts / ac.rolling_total_carts) as pan_india_actual_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '90_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as pan_india_90_day_forecast_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '60_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as pan_india_60_day_forecast_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '30_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as pan_india_30_day_forecast_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '15_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as pan_india_15_day_forecast_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '10_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as pan_india_10_day_forecast_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '5_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as pan_india_5_day_forecast_cart_growth,\n", "\n", "                max(case when pc.forecast_time = '1_day_forecast'\n", "                    then 1.0000 * pc.rolling_proj_carts / ac.rolling_total_carts \n", "                end) as pan_india_1_day_forecast_cart_growth\n", "\n", "            from (select sum(total_carts) as total_carts,\n", "                       sum(rolling_total_carts) as rolling_total_carts \n", "                    from actual_carts where cart_date = 'festival_start')  ac\n", "            cross join (select sum(total_carts) as total_carts,\n", "                       sum(rolling_total_carts) as rolling_total_carts \n", "                    from actual_carts where cart_date = 'sale_start')  ac2\n", "            cross join (select forecast_time, \n", "                            sum(proj_carts) as proj_carts, \n", "                            sum(rolling_proj_carts) as rolling_proj_carts\n", "                        from proj_carts\n", "                        group by 1) pc\n", "\n", "        ),\n", "\n", "        final as (\n", "            select \n", "\n", "                bf.festival_name,\n", "                cb.city_name,\n", "                db.date_,\n", "                bf.product_type,\n", "                bf.festival_tech_flag,\n", "                bfc.cpd as bau_cpd,\n", "                bfc.potential_sales as bau_potential_sales,\n", "                bfc.qty_sold_last_year,\n", "                bf.avg_spike as avg_spike_pan_india_ptype,\n", "                bfc.avg_spike as avg_spike_city_ptype,\n", "                (1.000000*coalesce(bfc.city_ptype_forecast_cpd,bf.city_ptype_forecast_cpd*city_weight*day_weight)) as city_ptype_forecast_cpd,\n", "                (1.000000*coalesce(bfc.city_ptype_forecast_aps,bf.city_ptype_forecast_aps*city_weight*day_weight)) as city_ptype_forecast_aps,\n", "                (1.000000*iofc.last_year_actual_sales) as last_year_actual_sales_from_model,\n", "                (1.000000*iofc.last_year_potential_sales) as last_year_potential_sales_from_model,\n", "                cg.cart_growth as cart_growth_pan_india,\n", "                cgc.cart_growth as cart_growth_city,\n", "                fd.effective_date as cart_growth_max_date,\n", "                \n", "                intent,\n", "                actual_intent,\n", "                (1.000000*coalesce(iofc.last_year_potential_sales*cgc.cart_growth*intent,iof.last_year_potential_sales*cg.cart_growth*intent*city_weight*day_weight)) as in_out_forecast_sale,\n", "                (1.000000*pqpi.planned_qty*day_weight) as category_bulk_upload_planned_qty,\n", "                qty_sold as actual_sales,\n", "                ptype_avg_availability as actual_availability,\n", "                actual_cart_growth,\n", "                \"90_day_forecast_cart_growth\",\n", "                \"60_day_forecast_cart_growth\",\n", "                \"30_day_forecast_cart_growth\",\n", "                \"15_day_forecast_cart_growth\",\n", "                \"10_day_forecast_cart_growth\",\n", "                \"5_day_forecast_cart_growth\",\n", "                \"1_day_forecast_cart_growth\",\n", "                \n", "                cgpi.pan_india_actual_cart_growth,\n", "                cgpi.pan_india_90_day_forecast_cart_growth,\n", "                cgpi.pan_india_60_day_forecast_cart_growth,\n", "                cgpi.pan_india_30_day_forecast_cart_growth,\n", "                cgpi.pan_india_15_day_forecast_cart_growth,\n", "                cgpi.pan_india_10_day_forecast_cart_growth,\n", "                cgpi.pan_india_5_day_forecast_cart_growth,\n", "                cgpi.pan_india_1_day_forecast_cart_growth,\n", "\n", "                bfc.ptype_carts as py_ptype_carts,\n", "                bfc.overall_carts as py_overall_carts,\n", "                bfc.ipc_ptype_carts as py_ipc_ptype_carts,\n", "                bfc.ipc_overall_carts as py_ipc_overall_carts,\n", "                bfc.cart_pen as py_cart_pen,\n", "                bfc.actual_potential_sales as py_actual_potential_sales,\n", "                bfc.actual_cpd as py_actual_cpd,\n", "                bfc.search_device_count_festival as py_search_device_count_festival,\n", "                bfc.total_demand_festival as py_total_demand_festival,\n", "                bfc.search_device_count_bau as py_search_device_count_bau,\n", "                bfc.total_demand_bau as py_total_demand_bau,\n", "                bfc.search_contri_atc_festival as py_search_contri_atc_festival,\n", "                bfc.search_atcs_festival as py_search_atcs_festival,\n", "                bfc.non_search_atcs_festival as py_non_search_atcs_festival,\n", "                bfc.search_atcs_bau as py_search_atcs_bau,\n", "                bfc.non_search_atcs_bau as py_non_search_atcs_bau,\n", "                bfc.total_atc_festival as py_total_atc_festival,\n", "                bfc.search_contri_atc_bau as py_search_contri_atc_bau,\n", "                bfc.total_atc_bau as py_total_atc_bau,\n", "                bfc.search_spike as py_search_spike,\n", "                bfc.demand_spike as py_demand_spike,\n", "                bfc.search_conversion_festival as py_search_conversion_festival,\n", "                bfc.demand_conversion_festival as py_demand_conversion_festival,\n", "                bfc.search_conversion_bau as py_search_conversion_bau,\n", "                bfc.demand_conversion_bau as py_demand_conversion_bau,\n", "\n", "                cy.actual_cpd as cy_actual_cpd,\n", "                cy.actual_potential_sales as cy_actual_potential_sales,\n", "                cy.search_device_count_festival as cy_search_device_count_festival,\n", "                cy.total_demand_festival as cy_total_demand_festival,\n", "                cy.search_device_count_bau as cy_search_device_count_bau,\n", "                cy.total_demand_bau as cy_total_demand_bau,\n", "                cy.search_contri_atc_festival as cy_search_contri_atc_festival,\n", "                cy.search_atcs_festival as cy_search_atcs_festival,\n", "                cy.non_search_atcs_festival as cy_non_search_atcs_festival,\n", "                cy.search_atcs_bau as cy_search_atcs_bau,\n", "                cy.non_search_atcs_bau as cy_non_search_atcs_bau,\n", "                cy.total_atc_festival as cy_total_atc_festival,\n", "                cy.search_contri_atc_bau as cy_search_contri_atc_bau,\n", "                cy.total_atc_bau as cy_total_atc_bau,\n", "                cy.search_spike as cy_search_spike,\n", "                cy.demand_spike as cy_demand_spike,\n", "                cy.search_conversion_festival as cy_search_conversion_festival,\n", "                cy.demand_conversion_festival as cy_demand_conversion_festival,\n", "                cy.search_conversion_bau as cy_search_conversion_bau,\n", "                cy.demand_conversion_bau as cy_demand_conversion_bau,\n", "                cy.ptype_carts as cy_ptype_carts,\n", "                cy.overall_carts as cy_overall_carts,\n", "                cy.ipc_ptype_carts as cy_ipc_ptype_carts,\n", "                cy.ipc_overall_carts as cy_ipc_overall_carts,\n", "                cy.cart_pen as cy_cart_pen,\n", "                cy.actual_sales as cy_actual_sales\n", "\n", "            from bau_forecast bf\n", "            left join city_breakdown cb on cb.product_type=bf.product_type\n", "            left join dod_breakdown db on db.product_type=bf.product_type\n", "            left join in_out_forecast iof on bf.product_type=iof.product_type\n", "            left join sales_city s on s.product_type=bf.product_type and s.city_name=cb.city_name and s.date_=db.date_\n", "            left join avail_city a on a.product_type=bf.product_type and a.city=cb.city_name and cast(a.\"date\" as date)=db.date_\n", "            left join planned_qty_city pqpi on pqpi.product_type=bf.product_type and pqpi.city_name=cb.city_name\n", "            left join bau_forecast_city bfc on bf.product_type=bfc.product_type and bfc.city=cb.city_name and bfc.date_=db.date_\n", "            left join in_out_forecast_city iofc on iofc.product_type=bf.product_type and iofc.city=cb.city_name and iofc.date_=db.date_\n", "            left join cart_growth_city cgc on cgc.city=cb.city_name\n", "            left join actual_cart_growth acg on acg.city = cb.city_name\n", "            left join supply_etls.festival_forecast_city_ptype_actuals cy on cy.date_ = db.date_\n", "                and cy.city = cb.city_name\n", "                and cy.product_type = bf.product_type\n", "                and cy.festival_name = '{festival}'\n", "            cross join cart_growth cg\n", "            cross join demand_intent_growth dig\n", "            cross join fallback_date fd\n", "            cross join actual_cart_growth_pan_india cgpi\n", "        )\n", "\n", "        select \n", "            festival_name,\n", "            date_,\n", "            city_name,\n", "            product_type,\n", "            (case when festival_tech_flag=1 or last_year_potential_sales_from_model>0 then 1 else 0 end) as festival_tech_flag,\n", "            bau_cpd,\n", "            bau_potential_sales,\n", "            qty_sold_last_year,\n", "            avg_spike_pan_india_ptype,\n", "            avg_spike_city_ptype,\n", "            city_ptype_forecast_cpd,\n", "            city_ptype_forecast_aps,\n", "            last_year_actual_sales_from_model,\n", "            last_year_potential_sales_from_model,\n", "            cart_growth_pan_india,\n", "            cart_growth_city,\n", "            cart_growth_max_date,\n", "            intent,\n", "            in_out_forecast_sale,\n", "            \n", "            case \n", "                when product_type in (select distinct group_name \n", "                                    FROM supply_etls.festival_forecast_group_ptype_mapping\n", "                                    where festival = '{festival}') then in_out_forecast_sale\n", "                when (avg_spike_pan_india_ptype = 0 or avg_spike_pan_india_ptype is null or avg_spike_pan_india_ptype > 3 or bau_cpd < 1) \n", "                    and in_out_forecast_sale>0 then in_out_forecast_sale\n", "                else city_ptype_forecast_cpd end as final_forecast,\n", "                \n", "            --CASE\n", "            --    -- 1. If product_type is part of the special mapping for the festival, use in_out_forecast_sale\n", "            --    WHEN product_type IN (\n", "            --        SELECT DISTINCT group_name\n", "            --        FROM supply_etls.festival_forecast_group_ptype_mapping\n", "            --        WHERE festival = '{festival}'\n", "            --    ) THEN in_out_forecast_sale\n", "\n", "            --    -- 2. If spike > 3 and final forecast would be < 1.2x BAU, move to APS\n", "            --    WHEN avg_spike_pan_india_ptype > 3 \n", "            --         AND in_out_forecast_sale < 1.2 * bau_cpd THEN city_ptype_forecast_aps\n", "\n", "            --    -- 3. If APS < BAU, move to CPD\n", "            --    WHEN city_ptype_forecast_aps < bau_cpd THEN city_ptype_forecast_cpd\n", "\n", "            --    -- 4. If spike between 1.5 and 3, and potential_sale > 0.8x CPD forecast, use in_out_forecast_sale\n", "            --    WHEN avg_spike_pan_india_ptype BETWEEN 1.5 AND 3\n", "            --         AND last_year_potential_sales_from_model > 0.8 * city_ptype_forecast_cpd THEN in_out_forecast_sale\n", "\n", "            --    -- 5. If spike between 1.5 and 3, and BAU > 0.8x in_out_forecast_sale, use APS\n", "            --    WHEN avg_spike_pan_india_ptype BETWEEN 1.5 AND 3\n", "            --         AND bau_cpd > 0.8 * in_out_forecast_sale THEN city_ptype_forecast_aps\n", "\n", "            --    -- 6. Fallback: if spike is 0/null/>3 or BAU < 1, and in_out is positive, use in_out\n", "            --    WHEN (avg_spike_pan_india_ptype = 0 OR avg_spike_pan_india_ptype IS NULL OR avg_spike_pan_india_ptype > 3 OR bau_cpd < 1)\n", "            --         AND in_out_forecast_sale > 0 THEN in_out_forecast_sale\n", "\n", "            --    -- 7. <PERSON><PERSON><PERSON> to CPD forecast\n", "            --    ELSE city_ptype_forecast_cpd\n", "            --END AS final_forecast,\n", "\n", "                \n", "                \n", "            case \n", "                when product_type in (select distinct group_name \n", "                                    FROM supply_etls.festival_forecast_group_ptype_mapping\n", "                                    where festival = '{festival}') then in_out_forecast_sale\n", "                when (avg_spike_pan_india_ptype = 0 or avg_spike_pan_india_ptype is null or avg_spike_pan_india_ptype > 3 or bau_cpd < 1) \n", "                    and in_out_forecast_sale>0 then in_out_forecast_sale\n", "                else city_ptype_forecast_aps end as final_forecast_aps,\n", "            category_bulk_upload_planned_qty,\n", "            actual_sales,\n", "            actual_availability,\n", "            actual_intent,\n", "            actual_cart_growth,\n", "            \"90_day_forecast_cart_growth\" as pred_90_day_forecast_cart_growth,\n", "            \"60_day_forecast_cart_growth\" as pred_60_day_forecast_cart_growth,\n", "            \"30_day_forecast_cart_growth\" as pred_30_day_forecast_cart_growth,\n", "            \"15_day_forecast_cart_growth\" as pred_15_day_forecast_cart_growth,\n", "            \"10_day_forecast_cart_growth\" as pred_10_day_forecast_cart_growth,\n", "            \"5_day_forecast_cart_growth\" as pred_5_day_forecast_cart_growth,\n", "            \"1_day_forecast_cart_growth\" as pred_1_day_forecast_cart_growth,\n", "            \n", "            pan_india_actual_cart_growth,\n", "            pan_india_90_day_forecast_cart_growth,\n", "            pan_india_60_day_forecast_cart_growth,\n", "            pan_india_30_day_forecast_cart_growth,\n", "            pan_india_15_day_forecast_cart_growth,\n", "            pan_india_10_day_forecast_cart_growth,\n", "            pan_india_5_day_forecast_cart_growth,\n", "            pan_india_1_day_forecast_cart_growth,\n", "\n", "            cy_actual_cpd,\n", "            cy_actual_potential_sales,\n", "            cy_search_device_count_festival,\n", "            cy_total_demand_festival,\n", "            cy_search_device_count_bau,\n", "            cy_total_demand_bau,\n", "            cy_search_contri_atc_festival,\n", "            cy_search_atcs_festival,\n", "            cy_non_search_atcs_festival,\n", "            cy_search_atcs_bau,\n", "            cy_non_search_atcs_bau,\n", "            cy_total_atc_festival,\n", "            cy_search_contri_atc_bau,\n", "            cy_total_atc_bau,\n", "            cy_search_spike,\n", "            cy_demand_spike,\n", "            cy_search_conversion_festival,\n", "            cy_demand_conversion_festival,\n", "            cy_search_conversion_bau,\n", "            cy_demand_conversion_bau,\n", "            cy_ptype_carts,\n", "            cy_overall_carts,\n", "            cy_ipc_ptype_carts,\n", "            cy_ipc_overall_carts,\n", "            cy_cart_pen,\n", "            cy_actual_sales,\n", "\n", "            py_actual_potential_sales,\n", "            py_actual_cpd,\n", "            py_search_device_count_festival,\n", "            py_total_demand_festival,\n", "            py_search_device_count_bau,\n", "            py_total_demand_bau,\n", "            py_search_contri_atc_festival,\n", "            py_search_atcs_festival,\n", "            py_non_search_atcs_festival,\n", "            py_search_atcs_bau,\n", "            py_non_search_atcs_bau,\n", "            py_total_atc_festival,\n", "            py_search_contri_atc_bau,\n", "            py_total_atc_bau,\n", "            py_search_spike,\n", "            py_demand_spike,\n", "            py_search_conversion_festival,\n", "            py_demand_conversion_festival,\n", "            py_search_conversion_bau,\n", "            py_demand_conversion_bau,\n", "            py_ptype_carts,\n", "            py_overall_carts,\n", "            py_ipc_ptype_carts,\n", "            py_ipc_overall_carts,\n", "            py_cart_pen,\n", "            null as search_device_count_bau,\n", "            null as total_demand_bau\n", "\n", "        from final\n", "\n", "        \"\"\"\n", "\n", "        to_trino(be_ptype, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "76839e3d-526d-4292-9250-ac47a4efaec9", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b66d084f-319e-4477-ac0f-29eaac5f33de", "metadata": {}, "outputs": [], "source": ["# df_be_ptype = read_sql_query(be_ptype, CON_TRINO)\n", "# df_be_ptype"]}, {"cell_type": "code", "execution_count": null, "id": "de630dd3-e554-47d6-86e9-e351925a6993", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d3cbe65b-4fcc-4f4b-bfd6-c8384cdf4c31", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9de672d0-fe2f-4623-8b6c-608a5a9009d1", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_be_ptype[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_be_ptype.columns\n", "# ]\n", "\n", "# column_dtypes"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}