{"cells": [{"cell_type": "code", "execution_count": null, "id": "c444a6d5-18f4-4d0b-b89c-2d75bc77104d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4775dcdf-3b7a-40ec-a513-cd3d8b53e153", "metadata": {"tags": []}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "b7962273-b8ba-4e38-b2ef-1cc1bcd174ad", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"potential_sales\", \"type\": \"real\", \"description\": \"Current Potential Sales\"},\n", "    {\"name\": \"actual_potential_sales\", \"type\": \"real\", \"description\": \"Current Potential Sales\"},\n", "    {\"name\": \"cpd\", \"type\": \"real\", \"description\": \"Current APS\"},\n", "    {\"name\": \"actual_cpd\", \"type\": \"real\", \"description\": \"Actual current APS\"},\n", "    {\n", "        \"name\": \"city_ptype_forecast_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis CPD\",\n", "    },\n", "    {\n", "        \"name\": \"city_ptype_forecast_aps\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis APS\",\n", "    },\n", "    {\n", "        \"name\": \"search_device_count_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Total Searches (Festival)\",\n", "    },\n", "    {\"name\": \"total_demand_festival\", \"type\": \"real\", \"description\": \"Total Demand (Festival)\"},\n", "    {\"name\": \"search_device_count_bau\", \"type\": \"real\", \"description\": \"Total Searches (BAU)\"},\n", "    {\"name\": \"total_demand_bau\", \"type\": \"real\", \"description\": \"Total Demand (BAU)\"},\n", "    {\n", "        \"name\": \"search_contri_atc_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution in ATC (Festival)\",\n", "    },\n", "    {\"name\": \"search_atcs_festival\", \"type\": \"real\", \"description\": \"Search ATC (Festival)\"},\n", "    {\n", "        \"name\": \"non_search_atcs_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Non-Search ATC (Festival)\",\n", "    },\n", "    {\"name\": \"search_atcs_bau\", \"type\": \"real\", \"description\": \"Search ATC (BAU)\"},\n", "    {\"name\": \"non_search_atcs_bau\", \"type\": \"real\", \"description\": \"Non-Search ATC (BAU)\"},\n", "    {\"name\": \"total_atc_festival\", \"type\": \"real\", \"description\": \"Total ATC (Festival)\"},\n", "    {\n", "        \"name\": \"search_contri_atc_bau\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Contribution in ATC (BAU)\",\n", "    },\n", "    {\"name\": \"total_atc_bau\", \"type\": \"real\", \"description\": \"Total ATC (BAU)\"},\n", "    {\"name\": \"search_spike\", \"type\": \"real\", \"description\": \"Search Spike\"},\n", "    {\"name\": \"demand_spike\", \"type\": \"real\", \"description\": \"Demand Spike\"},\n", "    {\n", "        \"name\": \"search_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Search Conversion (festival)\",\n", "    },\n", "    {\n", "        \"name\": \"demand_conversion_festival\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Demand Conversion (festival)\",\n", "    },\n", "    {\"name\": \"search_conversion_bau\", \"type\": \"real\", \"description\": \"Search Conversion (BAU)\"},\n", "    {\"name\": \"demand_conversion_bau\", \"type\": \"real\", \"description\": \"Demand Conversion (BAU)\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ec78ac0-465d-4cfe-996f-1bc60d2d3970", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_ptype\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city\",\n", "        \"product_type\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "def7eaea-914b-4ad2-b02f-81fce6b4236e", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'\n", "# # commented"]}, {"cell_type": "code", "execution_count": null, "id": "0ae9bb4d-e983-4a93-ba15-abe8d12a233e", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5a80c1e7-b4c3-479f-a41e-104f7a34e2b3", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "        \"flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a7ca20a2-8e1a-4f8d-b70a-f94b9c84af68", "metadata": {}, "outputs": [], "source": ["festive_date_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "da73be4d-482b-4632-8663-7cda52d8092d", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8007f82a-9864-460c-ad48-97b6916656a8", "metadata": {}, "outputs": [], "source": ["festive_date_data[[\"flag\"]] = festive_date_data[[\"flag\"]].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "3fb4aae7-7c65-4c02-86fd-af665650a489", "metadata": {}, "outputs": [], "source": ["festive_date_data = festive_date_data[festive_date_data[\"flag\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "6058ddee-bd10-4a2d-90e6-b2c9b2c7b4ac", "metadata": {}, "outputs": [], "source": ["festive_date_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5c4d67fb-4cf6-482c-bdd0-0b5e1cca8d1f", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3b6f7c8-40c0-4536-a288-c0de31ae5c9d", "metadata": {}, "outputs": [], "source": ["# cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# # sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "# sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]\n", "# bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "# bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "# festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "# festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "# sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "# festival = festive_date_data[\"festival\"].iloc[-1]\n", "# gap_days = festive_date_data[\"gap_days\"].iloc[-1]\n", "# flag = festive_date_data[\"flag\"].iloc[-1]\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "420fa915-7af1-4d3b-ac50-2fadbc607662", "metadata": {}, "outputs": [], "source": ["def process_and_insert(df, kwargs):\n", "    for _, row in df.iterrows():\n", "        # Extract values for the current row\n", "        festival = row[\"festival\"]\n", "        cpd_date = min(current_date_str, row[\"cpd_date\"])\n", "        bau_start_date = row[\"bau_start_date\"]\n", "        bau_end_date = row[\"bau_end_date\"]\n", "        festival_start_date = row[\"festival_start_date\"]\n", "        festival_end_date = row[\"festival_end_date\"]\n", "        sale_start_date = row[\"sale_start_date\"]\n", "        sale_end_date = row[\"sale_end_date\"]\n", "        gap_days = row[\"gap_days\"]\n", "\n", "        be_ptype = f\"\"\" \n", "\n", "        with seller_items as (\n", "            select \n", "                distinct spm.item_id as item_id\n", "            from seller.seller_product_mappings spm \n", "        ),\n", "        \n", "        \n", "        store_carts as (\n", "            select outlet_id, \n", "                try(count(distinct order_id)*1.00/count(distinct date(cart_checkout_ts_ist)))  as carts \n", "            from dwh.fact_sales_order_details x \n", "            where cart_checkout_ts_ist >= date('{cpd_date}') - interval '7' day \n", "                and cart_checkout_ts_ist < date('{cpd_date}') + interval '1' day\n", "                and order_create_dt_ist between date('{cpd_date}') - interval '7' day and date('{cpd_date}')\n", "                and order_current_status = 'DELIVERED' \n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "            group by 1\n", "\n", "        )\n", "        ,\n", "\n", "        pfma as (\n", "            select distinct item_id,\n", "                facility_id,\n", "                master_assortment_substate_id\n", "            from rpc.product_facility_master_assortment pfma \n", "            where active = 1\n", "        --    and master_assortment_substate_id in (1,3)\n", "            and lake_active_record\n", "        ),\n", "\n", "        aps as (\n", "            select distinct outlet_id,\n", "                item_id,\n", "                cpd as bumped_cpd,\n", "                aps_adjusted as cpd\n", "            from ars.outlet_item_aps_derived_cpd\n", "            where for_date = date('{cpd_date}')\n", "            and insert_ds_ist = cast(date('{cpd_date}') as varchar)\n", "        ),\n", "\n", "\n", "        iotm as (\n", "            select distinct item_id,\n", "                outlet_id,\n", "                cast(tag_value as int) as po_outlet_id\n", "            from rpc.item_outlet_tag_mapping \n", "            where tag_type_id = 8\n", "                and active = 1\n", "                and lake_active_record\n", "                and item_id not in (select item_id from seller_items)\n", "        ),\n", "\n", "        daily_orders as (\n", "            select item_id,\n", "                facility_id,\n", "                1.000000*sum(order_quantity)/15 as sales,\n", "                1.000000*sum(potential_order_quantity)/15 as potential_sales\n", "            from ars.daily_orders_and_availability\n", "            where lake_active_record \n", "                and insert_ds_ist between cast(date('{cpd_date}') - interval '14' day as varchar) and cast(date('{cpd_date}') as varchar)\n", "            group by 1,2\n", "        ),\n", "\n", "        outlet_details as (\n", "            select --current_date as updated_at, \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                    inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "            from supply_etls.outlet_details\n", "            where ars_check=1\n", "            and grocery_active_count >= 1\n", "            and (store_type in ('Packaged Goods', 'Dark Store'))\n", "        ),\n", "\n", "        final_sales_base as (\n", "            select --location,\n", "                iotm.outlet_id,\n", "                product_type,\n", "                sum(bumped_cpd) as bumped_cpd,\n", "                sum(cpd) as cpd,\n", "                sum(sales) as sales,\n", "                sum(potential_sales) as potential_sales\n", "            from iotm\n", "            inner join store_carts on store_carts.outlet_id = iotm.outlet_id and carts>100\n", "            inner join rpc.item_category_details icd on icd.item_id = iotm.item_id\n", "            inner join retail.console_outlet co on co.id = iotm.outlet_id\n", "            inner join pfma on iotm.item_id = pfma.item_id and co.facility_id = pfma.facility_id\n", "            left join aps on aps.item_id = iotm.item_id and aps.outlet_id = iotm.outlet_id\n", "            left join daily_orders on daily_orders.item_id = iotm.item_id and daily_orders.facility_id = co.facility_id\n", "            group by 1,2\n", "        ),\n", "\n", "\n", "        max_available_date AS (\n", "            SELECT \n", "                MAX(\"date\") AS max_date\n", "            FROM logistics_data_etls.cart_projections\n", "        ),\n", "        \n", "        fallback_date AS (\n", "            SELECT \n", "                CASE \n", "                    WHEN date('{sale_start_date}') > (SELECT max_date FROM max_available_date) \n", "                    THEN (SELECT max_date FROM max_available_date) \n", "                    ELSE date('{sale_start_date}')\n", "                END AS effective_date\n", "        ),\n", "        \n", "        cart_proj AS (\n", "            SELECT\n", "                outletid AS outlet_id,\n", "                SUM(carts) / COUNT(DISTINCT \"date\") AS orders\n", "            FROM logistics_data_etls.cart_projections cp\n", "            INNER JOIN (\n", "                        SELECT \n", "                            MAX(updated_on) AS ts\n", "                        FROM logistics_data_etls.cart_projections\n", "                        WHERE \"date\" BETWEEN \n", "                            (SELECT effective_date FROM fallback_date) - INTERVAL '7' DAY \n", "                            AND (SELECT effective_date FROM fallback_date) - INTERVAL '1' DAY\n", "                    ) tst \n", "            ON cp.updated_on = tst.ts\n", "            WHERE \"date\" BETWEEN \n", "                (SELECT effective_date FROM fallback_date) - INTERVAL '7' DAY \n", "                AND (SELECT effective_date FROM fallback_date) - INTERVAL '1' DAY\n", "            GROUP BY 1\n", "        ),\n", "\n", "\n", "        --cart_proj as (\n", "        --    select\n", "        --    -- \"date\" as date_,\n", "        --        outletid as outlet_id,\n", "        --        -- sum(aov) as aov,\n", "        --        sum(carts)/count(distinct \"date\") as orders\n", "        --    from logistics_data_etls.cart_projections cp\n", "        --    inner join\n", "        --        (select max(updated_on) as ts\n", "        --        from logistics_data_etls.cart_projections where \"date\" between date('{sale_start_date}') - interval '7' day and date('{sale_start_date}') - interval '1' day) tst on cp.updated_on = tst.ts\n", "        --    where \"date\" between date('{sale_start_date}') - interval '7' day and date('{sale_start_date}') - interval '1' day\n", "        --    group by 1\n", "        --),\n", "\n", "        sister_store_mapping as (\n", "            select \n", "                * \n", "            from \n", "            supply_etls.e3_new_darkstores nd \n", "            where nd.outlet_id not in (select distinct outlet_id from final_sales_base)\n", "        ),\n", "        \n", "        \n", "        --cluster as (\n", "        --    select\n", "        --        cluster_id,\n", "        --        cluster_name\n", "        --    from rpc.ams_cluster\n", "        --    where cluster_id > 15000 and lake_active_record\n", "        --),\n", "\n", "        --cluster_city as (\n", "        --    select\n", "        --        cluster_name,\n", "        --        cluster.cluster_id,\n", "        --        city_id\n", "        --    from rpc.ams_city_cluster_mapping accm\n", "        --    inner join cluster on cluster.cluster_id = accm.cluster_id and lake_active_record\n", "        --),\n", "\n", "        final_sales as (\n", "            select \n", "                od2.city_name as city,\n", "                -- outlet_id,\n", "                --cc.cluster_name,\n", "                coalesce(fs.product_type, fs2.product_type) as product_type,\n", "                sum(case \n", "                    when fs.product_type is not null then (fs.cpd / nullif(sc.carts,0)) * (cj.orders) \n", "                    else (fs2.cpd / nullif(sc2.carts,0)) * (cj.orders)\n", "                end) as cpd,\n", "                sum(fs.cpd) as actual_cpd,\n", "                sum(case \n", "                    when fs.product_type is not null then (fs.bumped_cpd / nullif(sc.carts,0)) * (cj.orders) \n", "                    else (fs2.bumped_cpd / nullif(sc2.carts,0)) * (cj.orders)\n", "                end) as bumped_cpd,\n", "                sum(case \n", "                    when fs.product_type is not null then (fs.potential_sales / nullif(sc.carts,0)) * (cj.orders) \n", "                    else (fs2.potential_sales / nullif(sc2.carts,0)) * (cj.orders)\n", "                end) as potential_sales,\n", "                sum(fs.potential_sales) as actual_potential_sales,\n", "                sum(fs.sales) as sales\n", "            from cart_proj cj \n", "            left join sister_store_mapping nd on nd.outlet_id = cj.outlet_id\n", "            left join supply_etls.outlet_details od on od.facility_id = nd.sister_store_facility_id\n", "            left join supply_etls.outlet_details od2 on od2.hot_outlet_id = cj.outlet_id\n", "            --left join cluster_city cc on cc.city_id = od2.city_id\n", "            left join store_carts sc on sc.outlet_id = cj.outlet_id\n", "            left join store_carts sc2 on sc2.outlet_id = od.hot_outlet_id\n", "            left join final_sales_base fs on fs.outlet_id = cj.outlet_id\n", "            left join final_sales_base fs2 on fs2.outlet_id = od.hot_outlet_id   \n", "            group by 1,2--,3\n", "        ),\n", "\n", "        ptype_distribution_raw as (\n", "            select *\n", "            from supply_etls.keyword_ptype_distribution\n", "            where date_ between (date('{bau_start_date}') - interval'7'day) and (date('{festival_end_date}') + interval'7'day)\n", "        ),\n", "\n", "        ptype_atc_distribution_initial as (\n", "            select distinct base.date_,\n", "                base.product_type,\n", "                base.keyword,\n", "                pdr.devices,\n", "                pdr.total_devices,\n", "                try(pdr.devices/pdr.total_devices) as percentage_attribution_d_day,\n", "                sum(pdr.devices) over \n", "                (partition by base.product_type, base.keyword order by base.date_  rows between 7 preceding and 7 following) as devices_14d,\n", "                sum(pdr.total_devices) over \n", "                (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following) as total_devices_14d,\n", "                try((1.00 * sum(pdr.devices) \n", "                over (partition by base.product_type, base.keyword order by base.date_ ROWS BETWEEN 7 preceding and 7 following)) / \n", "                (1.00 * sum(pdr.total_devices) \n", "                over (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following))) as percentage_attribution_14d_window\n", "            from (\n", "                    (select distinct date_ from ptype_distribution_raw) \n", "                        cross join \n", "                    (select distinct keyword, product_type from ptype_distribution_raw)\n", "                ) base\n", "            left join ptype_distribution_raw pdr \n", "            on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "        ), \n", "\n", "        ptype_atc_distribution as (\n", "            select date_,\n", "                keyword,\n", "                product_type,\n", "                devices,\n", "                total_devices,\n", "                percentage_attribution_d_day,\n", "                devices_14d,\n", "                total_devices_14d,\n", "                percentage_attribution_14d_window,\n", "                key_rank\n", "            from (select date_,\n", "                    keyword,\n", "                    product_type,\n", "                    devices,\n", "                    total_devices,\n", "                    percentage_attribution_d_day,\n", "                    devices_14d,\n", "                    total_devices_14d,\n", "                    percentage_attribution_14d_window,\n", "                    rank() over (partition by date_, keyword order by percentage_attribution_14d_window desc) as key_rank\n", "                from ptype_atc_distribution_initial)\n", "            where (key_rank<=1 or percentage_attribution_14d_window > 0.1)\n", "        ),\n", "\n", "        keyword_search_traffic_base as (\n", "            select \n", "                mdks.date_,\n", "                mdks.keyword,\n", "                od.city_name,\n", "                --cc.cluster_name,\n", "                sum(mdks.search_device_count) as search_device_count\n", "           from supply_etls.merchant_dod_keyword_searches_v2 mdks\n", "           left join supply_etls.outlet_details od on od.hot_outlet_id = mdks.outlet_id\n", "           --left join cluster_city cc on cc.city_id = od.city_id\n", "           where date_ between date('{bau_start_date}') and date('{festival_end_date}')\n", "           group by 1,2,3--,4\n", "        ),\n", "        \n", "        keyword_search_traffic as (\n", "            select \n", "                date_,\n", "                keyword,\n", "                sum(search_device_count) as search_device_count\n", "           from keyword_search_traffic_base\n", "           group by 1,2\n", "        ),\n", "        \n", "        search_traffic_city as (\n", "            select \n", "                date_,\n", "                city_name,\n", "                sum(search_device_count) as search_device_count\n", "            from keyword_search_traffic_base\n", "            group by 1,2\n", "        ),\n", "        \n", "        search_traffic_city_min as (\n", "            select \n", "                city_name, \n", "                min(search_device_count) as search_device_count\n", "            from search_traffic_city\n", "            group by 1\n", "            having min(search_device_count)<1000\n", "        ),\n", "        \n", "        keyword_search_traffic_city as (\n", "            select \n", "                date_,\n", "                keyword,\n", "                city_name,\n", "                search_device_count\n", "           from keyword_search_traffic_base\n", "           where city_name not in (select city_name from search_traffic_city_min)\n", "        ),\n", "        \n", "        --keyword_search_traffic_cluster as (\n", "        --    select distinct date_,\n", "        --        keyword,\n", "        --        cluster_name,\n", "        --        sum(search_device_count) as search_device_count\n", "        --   from keyword_search_traffic_base\n", "        --   group by 1,2,3\n", "        --),\n", "        \n", "        atc_split_city as (\n", "            select date_,\n", "                product_type,\n", "                od.city_name,\n", "                1.000000 * sum(search_atc_device_count) as search_atcs,\n", "                1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "                try((1.000000 * sum(search_atc_device_count))/((1.000000 * sum(search_atc_device_count)) + (1.000000 * sum(non_search_atc_device_count)))) as search_contri\n", "            from supply_etls.ptype_store_atc_channel_split_v2 atc\n", "            left join supply_etls.outlet_details od on od.hot_outlet_id = atc.outlet_id\n", "            where date_ between date('{bau_start_date}') and date('{festival_end_date}')\n", "            group by 1,2,3\n", "        ),\n", "        \n", "        atc_split as (\n", "            select date_,\n", "                product_type,\n", "                1.000000 * sum(search_atcs) as search_atcs,\n", "                1.000000 * sum(non_search_atcs) as non_search_atcs,\n", "                try((1.000000 * sum(search_atcs))/((1.000000 * sum(search_atcs)) + (1.000000 * sum(non_search_atcs)))) as search_contri\n", "            from atc_split_city\n", "            where date_ between date('{bau_start_date}') and date('{festival_end_date}')\n", "            group by 1,2\n", "        ),\n", "\n", "        dod_searches_curve_raw as (\n", "            select keys.date_ as date_,\n", "                keys.date_ + interval '{gap_days}' day as curr_date_,\n", "                coalesce(ptype_atc_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "                search_contri as search_contri_atc,\n", "                search_atcs,\n", "                non_search_atcs,\n", "                sum(keys.search_device_count) as search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_d_day) as ptype_d_day_search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_14d_window) as ptype_14d_window_search_device_count\n", "            from keyword_search_traffic keys\n", "            left join ptype_atc_distribution on keys.date_ = ptype_atc_distribution.date_ and keys.keyword = ptype_atc_distribution.keyword\n", "            left join atc_split on keys.date_ = atc_split.date_ and ptype_atc_distribution.product_type = atc_split.product_type\n", "            group by 1,2,3,4,5,6\n", "        ),\n", "\n", "\n", "        dod_searches_curve_14_days as (\n", "            select product_type,\n", "                min(curr_date_) as min_curr_date_,\n", "                max(curr_date_) as max_curr_date_,\n", "                avg(search_device_count) as avg_search_device_count,\n", "                avg(search_atcs + non_search_atcs) as avg_total_atc,\n", "                avg(search_atcs) as avg_search_atcs,\n", "                avg(non_search_atcs) as avg_non_search_atcs,\n", "                try((1.000000 * sum(search_atcs))/((1.000000 * sum(search_atcs)) + (1.000000 * sum(non_search_atcs)))) as avg_search_contri_atc,\n", "                avg(ptype_d_day_search_device_count) as avg_ptype_d_day_search_device_count,\n", "                avg(ptype_14d_window_search_device_count) as avg_ptype_14d_window_search_device_count,\n", "                try(avg(1.00 * search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand,\n", "                try(avg(1.00 * ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_d_day_ptype,\n", "                try(avg(1.00 * ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_14_day_ptype\n", "            from dod_searches_curve_raw\n", "            where date_ between date('{bau_start_date}') and date('{bau_end_date}')\n", "            group by 1\n", "        ),\n", "\n", "        dod_searches_curve as (\n", "             select date_,\n", "                curr_date_,\n", "                product_type,\n", "                search_device_count,\n", "                search_atcs + non_search_atcs as total_atc,\n", "                search_atcs,\n", "                non_search_atcs,\n", "                ptype_d_day_search_device_count,\n", "                ptype_14d_window_search_device_count,\n", "                search_contri_atc,\n", "                0.95 as availability_cut,\n", "                1.10 as buffer,\n", "                (1.00 * try(search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand,\n", "                (1.00 * try(ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_d_day_ptype,\n", "                (1.00 * try(ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_14_day_ptype\n", "            from dod_searches_curve_raw\n", "            where curr_date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "        ),\n", "        \n", "        dod_searches_curve_raw_city as (\n", "            select keys.date_ as date_, keys.city_name,\n", "                keys.date_ + interval '{gap_days}' day as curr_date_,\n", "                coalesce(ptype_atc_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "                search_contri as search_contri_atc,\n", "                search_atcs,\n", "                non_search_atcs,\n", "                sum(keys.search_device_count) as search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_d_day) as ptype_d_day_search_device_count,\n", "                sum(keys.search_device_count * percentage_attribution_14d_window) as ptype_14d_window_search_device_count\n", "            from keyword_search_traffic_city keys\n", "            left join ptype_atc_distribution on keys.date_ = ptype_atc_distribution.date_ and keys.keyword = ptype_atc_distribution.keyword\n", "            left join atc_split_city atc_split on keys.date_ = atc_split.date_ and ptype_atc_distribution.product_type = atc_split.product_type and atc_split.city_name = keys.city_name\n", "            group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "\n", "        dod_searches_curve_14_days_city as (\n", "            select product_type, city_name,\n", "                min(curr_date_) as min_curr_date_,\n", "                max(curr_date_) as max_curr_date_,\n", "                avg(search_device_count) as avg_search_device_count,\n", "                avg(search_atcs + non_search_atcs) as avg_total_atc,\n", "                avg(search_atcs) as avg_search_atcs,\n", "                avg(non_search_atcs) as avg_non_search_atcs,\n", "                avg(ptype_d_day_search_device_count) as avg_ptype_d_day_search_device_count,\n", "                avg(ptype_14d_window_search_device_count) as avg_ptype_14d_window_search_device_count,\n", "                try((1.000000 * sum(search_atcs))/((1.000000 * sum(search_atcs)) + (1.000000 * sum(non_search_atcs)))) as avg_search_contri_atc,\n", "                try(avg(1.00 * search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand,\n", "                try(avg(1.00 * ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_d_day_ptype,\n", "                try(avg(1.00 * ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_14_day_ptype\n", "            from dod_searches_curve_raw_city\n", "            where date_ between date('{bau_start_date}') and date('{bau_end_date}')\n", "            group by 1,2\n", "        ),\n", "\n", "        dod_searches_curve_city as (\n", "             select date_,\n", "                curr_date_,\n", "                product_type,\n", "                city_name,\n", "                search_device_count,\n", "                search_atcs + non_search_atcs as total_atc,\n", "                search_atcs,\n", "                non_search_atcs,\n", "                ptype_d_day_search_device_count,\n", "                ptype_14d_window_search_device_count,\n", "                search_contri_atc,\n", "                0.95 as availability_cut,\n", "                1.10 as buffer,\n", "                (1.00 * try(search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand,\n", "                (1.00 * try(ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_d_day_ptype,\n", "                (1.00 * try(ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_14_day_ptype\n", "            from dod_searches_curve_raw_city\n", "            where curr_date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "        ),\n", "\n", "        --dod_searches_curve_raw_cluster as (\n", "        --    select keys.date_ as date_, keys.cluster_name,\n", "        --        keys.date_ + interval '{gap_days}' day as curr_date_,\n", "        --        coalesce(ptype_atc_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "        --        search_contri as search_contri_atc,\n", "        --        sum(keys.search_device_count) as search_device_count,\n", "        --        sum(keys.search_device_count * percentage_attribution_d_day) as ptype_d_day_search_device_count,\n", "        --        sum(keys.search_device_count * percentage_attribution_14d_window) as ptype_14d_window_search_device_count\n", "        --    from keyword_search_traffic_cluster keys\n", "        --    left join ptype_atc_distribution on keys.date_ = ptype_atc_distribution.date_ and keys.keyword = ptype_atc_distribution.keyword\n", "        --    left join atc_split on keys.date_ = atc_split.date_ and ptype_atc_distribution.product_type = atc_split.product_type\n", "        --    group by 1,2,3,4,5\n", "        --),\n", "\n", "\n", "        --dod_searches_curve_14_days_cluster as (\n", "        --    select product_type, cluster_name,\n", "        --        min(curr_date_) as min_curr_date_,\n", "        --        max(curr_date_) as max_curr_date_,\n", "        --        avg(search_device_count) as avg_search_device_count,\n", "        --        avg(ptype_d_day_search_device_count) as avg_ptype_d_day_search_device_count,\n", "        --        avg(ptype_14d_window_search_device_count) as avg_ptype_14d_window_search_device_count,\n", "        --        try(avg(1.00 * search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand,\n", "        --        try(avg(1.00 * ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_d_day_ptype,\n", "        --        try(avg(1.00 * ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_14_day_ptype\n", "        --    from dod_searches_curve_raw_cluster\n", "        --    where date_ between date('{bau_start_date}') and date('{bau_end_date}')\n", "        --    group by 1,2\n", "        --),\n", "\n", "        --dod_searches_curve_cluster as (\n", "        --     select date_,\n", "        --        curr_date_,\n", "        --        product_type,\n", "        --        cluster_name,\n", "        --        search_device_count,\n", "        --        ptype_d_day_search_device_count,\n", "        --        ptype_14d_window_search_device_count,\n", "        --        search_contri_atc,\n", "        --        0.95 as availability_cut,\n", "        --        1.10 as buffer,\n", "        --        (1.00 * try(search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand,\n", "        --        (1.00 * try(ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_d_day_ptype,\n", "        --        (1.00 * try(ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_14_day_ptype\n", "        --    from dod_searches_curve_raw_cluster\n", "        --    where curr_date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "        --),\n", "\n", "        final as (\n", "        select \n", "            dod_searches_curve.curr_date_,\n", "            '{festival}' as festival,\n", "            final_sales.city,\n", "            --final_sales.cluster_name,\n", "            dod_searches_curve.product_type,\n", "            dod_searches_curve.search_device_count,\n", "            dod_searches_curve.ptype_d_day_search_device_count,\n", "            dod_searches_curve.ptype_14d_window_search_device_count,\n", "            dod_searches_curve.search_contri_atc,\n", "            dod_searches_curve.total_demand,\n", "            dod_searches_curve.total_demand_d_day_ptype,\n", "            dod_searches_curve.total_demand_14_day_ptype,\n", "            dod_searches_curve.total_atc,\n", "            dod_searches_curve.search_atcs,\n", "            dod_searches_curve.non_search_atcs,\n", "            \n", "            dod_searches_curve_14_days.avg_search_atcs,\n", "            dod_searches_curve_14_days.avg_non_search_atcs,\n", "            dod_searches_curve_14_days.avg_search_contri_atc,\n", "            dod_searches_curve_14_days.avg_total_atc,\n", "            dod_searches_curve_14_days.avg_search_device_count,\n", "            dod_searches_curve_14_days.avg_ptype_d_day_search_device_count,\n", "            dod_searches_curve_14_days.avg_ptype_14d_window_search_device_count,\n", "            dod_searches_curve_14_days.avg_total_demand,\n", "            dod_searches_curve_14_days.avg_total_demand_d_day_ptype,\n", "            dod_searches_curve_14_days.avg_total_demand_14_day_ptype,\n", "            \n", "            dod_city_base.avg_search_atcs as city_avg_search_atcs,\n", "            dod_city_base.avg_non_search_atcs as city_avg_non_search_atcs,\n", "            dod_city_base.avg_search_contri_atc as city_avg_search_contri_atc,\n", "            dod_city_base.avg_total_atc as city_avg_total_atc,\n", "            dod_city_base.avg_search_device_count as city_avg_search_device_count,\n", "            dod_city_base.avg_total_demand as city_avg_total_demand,\n", "            \n", "            dod_city.search_contri_atc as city_search_contri_atc,\n", "            dod_city.total_atc as city_total_atc,\n", "            dod_city.search_atcs as city_search_atcs,\n", "            dod_city.non_search_atcs as city_non_search_atcs,\n", "            dod_city.search_device_count as city_search_device_count,\n", "            dod_city.total_demand as city_total_demand,\n", "            \n", "            coalesce(cpd,0.05) as cpd,\n", "            coalesce(bumped_cpd,0.05) as bumped_cpd,\n", "            sales,\n", "            final_sales.actual_cpd,\n", "            final_sales.actual_potential_sales,\n", "            coalesce(potential_sales,0.05) as potential_sales,\n", "            dod_searches_curve.availability_cut,\n", "            dod_searches_curve.buffer,\n", "            try(((coalesce(cpd,0.05) * dod_searches_curve.buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand) / nullif(dod_searches_curve_14_days.avg_total_demand,0)) as final_cpd_keyword,\n", "            try(((coalesce(cpd,0.05) * dod_searches_curve.buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_d_day_ptype) / nullif(dod_searches_curve_14_days.avg_total_demand_d_day_ptype,0)) as final_cpd_ptype_d_day,\n", "            \n", "            \n", "            try(((coalesce(cpd,0.05) * dod_city.buffer * dod_city.availability_cut) * dod_city.total_demand_14_day_ptype) / nullif(dod_city_base.avg_total_demand_14_day_ptype,0)) as final_cpd_ptype_14d_city,\n", "            try(((coalesce(potential_sales,0.05) * dod_city.buffer * dod_city.availability_cut) * dod_city.total_demand_14_day_ptype) / nullif(dod_city_base.avg_total_demand_14_day_ptype,0)) as final_potential_ptype_14d_city,\n", "\n", "            --try(((coalesce(cpd,0.05) * dod_cluster.buffer * dod_cluster.availability_cut) * dod_cluster.total_demand_14_day_ptype) / nullif(dod_cluster_base.avg_total_demand_14_day_ptype,0)) as final_cpd_ptype_14d_cluster,\n", "            --try(((coalesce(potential_sales,0.05) * dod_cluster.buffer * dod_cluster.availability_cut) * dod_cluster.total_demand_14_day_ptype) / nullif(dod_cluster_base.avg_total_demand_14_day_ptype,0)) as final_potential_ptype_14d_cluster,\n", "            \n", "            try(((coalesce(cpd,0.05) * dod_searches_curve.buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_14_day_ptype) / nullif(dod_searches_curve_14_days.avg_total_demand_14_day_ptype,0)) as final_cpd_ptype_14d,\n", "            try(((coalesce(potential_sales,0.05) * dod_searches_curve.buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_14_day_ptype) / nullif(dod_searches_curve_14_days.avg_total_demand_14_day_ptype,0)) as final_potential_ptype_14d\n", "\n", "        from dod_searches_curve\n", "        \n", "        inner join dod_searches_curve_14_days \n", "            on dod_searches_curve.product_type = dod_searches_curve_14_days.product_type\n", "        \n", "        inner join final_sales \n", "            on final_sales.product_type  = dod_searches_curve.product_type\n", "        \n", "        --left join dod_searches_curve_cluster dod_cluster \n", "        --    on final_sales.product_type  = dod_cluster.product_type and final_sales.cluster_name = dod_cluster.cluster_name and dod_cluster.curr_date_=dod_searches_curve.curr_date_\n", "        \n", "        --left join dod_searches_curve_14_days_cluster dod_cluster_base \n", "        --    on dod_cluster.cluster_name = dod_cluster_base.cluster_name and dod_cluster.product_type = dod_cluster_base.product_type\n", "        \n", "        left join dod_searches_curve_city dod_city \n", "            on final_sales.product_type  = dod_city.product_type and final_sales.city=dod_city.city_name and dod_city.curr_date_=dod_searches_curve.curr_date_\n", "        \n", "        left join dod_searches_curve_14_days_city dod_city_base \n", "            on dod_city.city_name = dod_city_base.city_name and dod_city.product_type = dod_city_base.product_type\n", "        )\n", "\n", "        select \n", "            -- *    \n", "            curr_date_ as date_,\n", "            festival as festival_name,\n", "            city,\n", "            product_type,\n", "            cpd,\n", "            actual_cpd,\n", "            potential_sales,\n", "            actual_potential_sales,\n", "            coalesce(final_cpd_ptype_14d_city, \n", "            --final_cpd_ptype_14d_cluster, \n", "                final_cpd_ptype_14d,0) as city_ptype_forecast_cpd,\n", "            coalesce(final_potential_ptype_14d_city, \n", "            --final_potential_ptype_14d_cluster, \n", "                final_potential_ptype_14d,0) as city_ptype_forecast_aps,\n", "            city_search_device_count as search_device_count_festival,\n", "            city_total_demand as total_demand_festival ,\n", "            city_avg_search_device_count as search_device_count_bau,\n", "            city_avg_total_demand as total_demand_bau,\n", "            city_search_contri_atc as search_contri_atc_festival,\n", "            city_search_atcs as search_atcs_festival,\n", "            city_non_search_atcs as non_search_atcs_festival,\n", "            city_avg_search_atcs as search_atcs_bau,\n", "            city_avg_non_search_atcs as non_search_atcs_bau,\n", "            city_total_atc as total_atc_festival,\n", "            city_avg_search_contri_atc as search_contri_atc_bau,\n", "            city_avg_total_atc as total_atc_bau,\n", "            coalesce(try((city_search_device_count / city_avg_search_device_count)), try((search_device_count / avg_search_device_count))) as search_spike,\n", "            coalesce(try((city_total_demand / city_avg_total_demand)), try((total_demand / avg_total_demand))) as demand_spike,\n", "            coalesce(try((city_search_atcs / city_search_device_count)), try((search_atcs / search_device_count))) as search_conversion_festival,\n", "            coalesce(try((city_total_atc / city_total_demand)), try((total_atc / total_demand))) as demand_conversion_festival,\n", "            coalesce(try((city_avg_search_atcs / city_avg_search_device_count)), try((avg_search_atcs / avg_search_device_count))) as search_conversion_bau,\n", "            coalesce(try((city_avg_total_atc / city_avg_total_demand)), try((avg_total_atc / avg_total_demand))) as demand_conversion_bau\n", "            \n", "        from final\n", "     \n", "        \"\"\"\n", "\n", "        to_trino(be_ptype, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Data for festival {festival} inserted successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "76839e3d-526d-4292-9250-ac47a4efaec9", "metadata": {}, "outputs": [], "source": ["process_and_insert(festive_date_data, kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b66d084f-319e-4477-ac0f-29eaac5f33de", "metadata": {}, "outputs": [], "source": ["# df_be_ptype = read_sql_query(be_ptype, CON_TRINO)\n", "# df_be_ptype\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "de630dd3-e554-47d6-86e9-e351925a6993", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.head()\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "d3cbe65b-4fcc-4f4b-bfd6-c8384cdf4c31", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.dtypes\n", "# commented"]}, {"cell_type": "code", "execution_count": null, "id": "9de672d0-fe2f-4623-8b6c-608a5a9009d1", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_be_ptype[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_be_ptype.columns\n", "# ]\n", "\n", "# column_dtypes\n", "# commented"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}