{"cells": [{"cell_type": "code", "execution_count": null, "id": "c444a6d5-18f4-4d0b-b89c-2d75bc77104d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "4775dcdf-3b7a-40ec-a513-cd3d8b53e153", "metadata": {"tags": []}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "0ae9bb4d-e983-4a93-ba15-abe8d12a233e", "metadata": {"tags": []}, "outputs": [], "source": ["df = pb.from_sheets(\"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5a80c1e7-b4c3-479f-a41e-104f7a34e2b3", "metadata": {}, "outputs": [], "source": ["festive_date_data = df[\n", "    [\n", "        \"festival\",\n", "        \"cpd_date\",\n", "        \"bau_start_date\",\n", "        \"bau_end_date\",\n", "        \"festival_start_date\",\n", "        \"festival_end_date\",\n", "        \"sale_start_date\",\n", "        \"sale_end_date\",\n", "        \"gap_days\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2a344ead-ba66-48a1-95a1-3221b5a95b4c", "metadata": {}, "outputs": [], "source": ["current_date = date.today()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "936dee8c-5070-43f2-8b30-d06284bdd75b", "metadata": {}, "outputs": [], "source": ["cpd_date = min(current_date_str, festive_date_data[\"cpd_date\"].iloc[-1])\n", "# sale_start_date = max(yesterday_date_str, festive_date_data[\"sale_start_date\"].iloc[-1])\n", "sale_start_date = festive_date_data[\"sale_start_date\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "06cf9d31-a480-4329-b569-3fd0939a8425", "metadata": {}, "outputs": [], "source": ["bau_start_date = festive_date_data[\"bau_start_date\"].iloc[-1]\n", "bau_end_date = festive_date_data[\"bau_end_date\"].iloc[-1]\n", "festival_start_date = festive_date_data[\"festival_start_date\"].iloc[-1]\n", "festival_end_date = festive_date_data[\"festival_end_date\"].iloc[-1]\n", "sale_end_date = festive_date_data[\"sale_end_date\"].iloc[-1]\n", "festival = festive_date_data[\"festival\"].iloc[-1]\n", "gap_days = festive_date_data[\"gap_days\"].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "id": "e4b6bdb1-6dff-427a-88b7-5caed06040df", "metadata": {}, "outputs": [], "source": ["# cpd_date = '2024-09-15'\n", "# bau_start_date = '2023-09-15'\n", "# bau_end_date = '2023-09-28'\n", "# festival_start_date = '2023-09-29'\n", "# festival_end_date = '2023-10-14'\n", "# sale_start_date = '2024-09-16'\n", "# sale_end_date = '2024-10-02'\n", "# festival = 'Shradh'\n", "# gap_days = '353'"]}, {"cell_type": "code", "execution_count": null, "id": "c26ced28-e03d-4302-b810-dd6a5e66b2cd", "metadata": {}, "outputs": [], "source": ["be_ptype = f\"\"\" \n", "\n", "with store_carts as (\n", "    select outlet_id, \n", "        try(count(distinct order_id)*1.00/count(distinct date(cart_checkout_ts_ist)))  as carts \n", "    from dwh.fact_sales_order_details x \n", "    where cart_checkout_ts_ist >= date('{cpd_date}') - interval '7' day \n", "        and cart_checkout_ts_ist < date('{cpd_date}') + interval '1' day\n", "        and order_create_dt_ist between date('{cpd_date}') - interval '7' day and date('{cpd_date}')\n", "        and order_current_status = 'DELIVERED' \n", "        and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder') \n", "    group by 1\n", "\n", ")\n", ",\n", "\n", "pfma as (\n", "    select distinct item_id,\n", "        facility_id,\n", "        master_assortment_substate_id\n", "    from rpc.product_facility_master_assortment pfma \n", "    where active = 1\n", "    and master_assortment_substate_id in (1,3)\n", "    and lake_active_record\n", "),\n", "\n", "aps as (\n", "    select distinct outlet_id,\n", "        item_id,\n", "        cpd as bumped_cpd,\n", "        aps_adjusted as cpd\n", "    from ars.outlet_item_aps_derived_cpd\n", "    where for_date = date('{cpd_date}')\n", "    and insert_ds_ist = cast(date('{cpd_date}') as varchar)\n", "),\n", "\n", "\n", "iotm as (\n", "    select distinct item_id,\n", "        outlet_id,\n", "        cast(tag_value as int) as po_outlet_id\n", "    from rpc.item_outlet_tag_mapping \n", "    where tag_type_id = 8\n", "        and active = 1\n", "        and lake_active_record\n", "),\n", "\n", "daily_orders as (\n", "    select item_id,\n", "        facility_id,\n", "        avg(order_quantity) as sales,\n", "        avg(potential_order_quantity) as potential_sales\n", "    from ars.daily_orders_and_availability\n", "    where lake_active_record \n", "        and insert_ds_ist between cast(date('{cpd_date}') - interval '14' day as varchar) and cast(date('{cpd_date}') as varchar)\n", "    group by 1,2\n", "),\n", "\n", "outlet_details as (\n", "    select --current_date as updated_at, \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "            inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "    from supply_etls.outlet_details\n", "    where ars_check=1\n", "    and grocery_active_count >= 1\n", "    and (store_type in ('Packaged Goods', 'Dark Store'))\n", "),\n", "\n", "final_sales_base as (\n", "    select --location,\n", "        iotm.outlet_id,\n", "        product_type,\n", "        sum(bumped_cpd) as bumped_cpd,\n", "        sum(cpd) as cpd,\n", "        sum(sales) as sales,\n", "        sum(potential_sales) as potential_sales\n", "    from iotm\n", "    inner join rpc.item_category_details icd on icd.item_id = iotm.item_id\n", "    inner join retail.console_outlet co on co.id = iotm.outlet_id\n", "    inner join pfma on iotm.item_id = pfma.item_id and co.facility_id = pfma.facility_id\n", "    left join aps on aps.item_id = iotm.item_id and aps.outlet_id = iotm.outlet_id\n", "    left join daily_orders on daily_orders.item_id = iotm.item_id and daily_orders.facility_id = co.facility_id\n", "    group by 1,2\n", "),\n", "\n", "cart_proj as (\n", "    select\n", "    -- \"date\" as date_,\n", "        outletid as outlet_id,\n", "        -- sum(aov) as aov,\n", "        sum(carts)/count(distinct \"date\") as orders\n", "    from logistics_data_etls.cart_projections cp\n", "    inner join\n", "        (select max(updated_on) as ts\n", "        from logistics_data_etls.cart_projections where \"date\" between date('{sale_start_date}') - interval '7' day and date('{sale_start_date}') - interval '1' day) tst on cp.updated_on = tst.ts\n", "    where \"date\" between date('{sale_start_date}') - interval '7' day and date('{sale_start_date}') - interval '1' day\n", "    group by 1\n", "),\n", "\n", "sister_store_mapping as (\n", "    select \n", "        * \n", "    from \n", "    supply_etls.e3_new_darkstores nd \n", "    where nd.outlet_id not in (select distinct outlet_id from final_sales_base)\n", "),\n", "\n", "final_sales as (\n", "    select \n", "        od2.city_name as city,\n", "        -- outlet_id,\n", "        coalesce(fs.product_type, fs2.product_type) as product_type,\n", "        sum(case \n", "            when fs.product_type is not null then (fs.cpd / nullif(sc.carts,0)) * (cj.orders) \n", "            else (fs2.cpd / nullif(sc2.carts,0)) * (cj.orders)\n", "        end) as cpd,\n", "        sum(case \n", "            when fs.product_type is not null then (fs.bumped_cpd / nullif(sc.carts,0)) * (cj.orders) \n", "            else (fs2.bumped_cpd / nullif(sc2.carts,0)) * (cj.orders)\n", "        end) as bumped_cpd,\n", "        sum(case \n", "            when fs.product_type is not null then (fs.potential_sales / nullif(sc.carts,0)) * (cj.orders) \n", "            else (fs2.potential_sales / nullif(sc2.carts,0)) * (cj.orders)\n", "        end) as potential_sales,\n", "        sum(fs.sales) as sales\n", "    from cart_proj cj \n", "    left join sister_store_mapping nd on nd.outlet_id = cj.outlet_id\n", "    left join supply_etls.outlet_details od on od.facility_id = nd.sister_store_facility_id\n", "    left join supply_etls.outlet_details od2 on od2.hot_outlet_id = cj.outlet_id\n", "    left join store_carts sc on sc.outlet_id = cj.outlet_id\n", "    left join store_carts sc2 on sc2.outlet_id = od.hot_outlet_id\n", "    left join final_sales_base fs on fs.outlet_id = cj.outlet_id\n", "    left join final_sales_base fs2 on fs2.outlet_id = od.hot_outlet_id   \n", "    group by 1,2\n", "),\n", "\n", "ptype_distribution_raw as (\n", "    select *\n", "    from supply_etls.keyword_ptype_distribution\n", "    where date_ between (date('{bau_start_date}') - interval'7'day) and (date('{festival_end_date}') + interval'7'day)\n", "),\n", "\n", "ptype_atc_distribution_initial as (\n", "    select distinct base.date_,\n", "        base.product_type,\n", "        base.keyword,\n", "        pdr.devices,\n", "        pdr.total_devices,\n", "        try(pdr.devices/pdr.total_devices) as percentage_attribution_d_day,\n", "        sum(pdr.devices) over \n", "        (partition by base.product_type, base.keyword order by base.date_  rows between 7 preceding and 7 following) as devices_14d,\n", "        sum(pdr.total_devices) over \n", "        (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following) as total_devices_14d,\n", "        try((1.00 * sum(pdr.devices) \n", "        over (partition by base.product_type, base.keyword order by base.date_ ROWS BETWEEN 7 preceding and 7 following)) / \n", "        (1.00 * sum(pdr.total_devices) \n", "        over (partition by base.product_type, base.keyword order by base.date_ rows between 7 preceding and 7 following))) as percentage_attribution_14d_window\n", "    from (\n", "            (select distinct date_ from ptype_distribution_raw) \n", "                cross join \n", "            (select distinct keyword, product_type from ptype_distribution_raw)\n", "        ) base\n", "    left join ptype_distribution_raw pdr \n", "    on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "), \n", "\n", "ptype_atc_distribution as (\n", "    select date_,\n", "        keyword,\n", "        product_type,\n", "        devices,\n", "        total_devices,\n", "        percentage_attribution_d_day,\n", "        devices_14d,\n", "        total_devices_14d,\n", "        percentage_attribution_14d_window,\n", "        key_rank\n", "    from (select date_,\n", "            keyword,\n", "            product_type,\n", "            devices,\n", "            total_devices,\n", "            percentage_attribution_d_day,\n", "            devices_14d,\n", "            total_devices_14d,\n", "            percentage_attribution_14d_window,\n", "            rank() over (partition by date_, keyword order by percentage_attribution_14d_window desc) as key_rank\n", "        from ptype_atc_distribution_initial)\n", "    where (key_rank<=1 or percentage_attribution_14d_window > 0.1)\n", "),\n", "\n", "\n", "keyword_search_traffic as (\n", "    select distinct date_,\n", "        keyword,\n", "        sum(search_device_count) as search_device_count\n", "   from supply_etls.merchant_dod_keyword_searches_v2\n", "   where date_ between date('{bau_start_date}') and date('{festival_end_date}')\n", "   group by 1,2\n", "),\n", "\n", "atc_split as (\n", "    select date_,\n", "        product_type,\n", "        1.000000 * sum(search_atc_device_count) as search_atcs,\n", "        1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "        try((1.000000 * sum(search_atc_device_count))/((1.000000 * sum(search_atc_device_count)) + (1.000000 * sum(non_search_atc_device_count)))) as search_contri\n", "    from supply_etls.ptype_store_atc_channel_split_v2\n", "    where date_ between date('{bau_start_date}') and date('{festival_end_date}')\n", "    group by 1,2\n", "),\n", "\n", "dod_searches_curve_raw as (\n", "    select keys.date_ as date_,\n", "        keys.date_ + interval '{gap_days}' day as curr_date_,\n", "        coalesce(ptype_atc_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "        search_contri as search_contri_atc,\n", "        sum(keys.search_device_count) as search_device_count,\n", "        sum(keys.search_device_count * percentage_attribution_d_day) as ptype_d_day_search_device_count,\n", "        sum(keys.search_device_count * percentage_attribution_14d_window) as ptype_14d_window_search_device_count\n", "    from keyword_search_traffic keys\n", "    left join ptype_atc_distribution on keys.date_ = ptype_atc_distribution.date_ and keys.keyword = ptype_atc_distribution.keyword\n", "    left join atc_split on keys.date_ = atc_split.date_ and ptype_atc_distribution.product_type = atc_split.product_type\n", "    group by 1,2,3,4\n", "),\n", "\n", "\n", "dod_searches_curve_14_days as (\n", "    select product_type,\n", "        min(curr_date_) as min_curr_date_,\n", "        max(curr_date_) as max_curr_date_,\n", "        avg(search_device_count) as avg_search_device_count,\n", "        avg(ptype_d_day_search_device_count) as avg_ptype_d_day_search_device_count,\n", "        avg(ptype_14d_window_search_device_count) as avg_ptype_14d_window_search_device_count,\n", "        try(avg(1.00 * search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand,\n", "        try(avg(1.00 * ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_d_day_ptype,\n", "        try(avg(1.00 * ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as avg_total_demand_14_day_ptype\n", "    from dod_searches_curve_raw\n", "    where date_ between date('{bau_start_date}') and date('{bau_end_date}')\n", "    group by 1\n", "),\n", "\n", "dod_searches_curve as (\n", "     select date_,\n", "        curr_date_,\n", "        product_type,\n", "        search_device_count,\n", "        ptype_d_day_search_device_count,\n", "        ptype_14d_window_search_device_count,\n", "        search_contri_atc,\n", "        0.95 as availability_cut,\n", "        1.10 as buffer,\n", "        (1.00 * try(search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand,\n", "        (1.00 * try(ptype_d_day_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_d_day_ptype,\n", "        (1.00 * try(ptype_14d_window_search_device_count / (case when search_contri_atc = 0 or search_contri_atc is null then 1 else search_contri_atc end))) as total_demand_14_day_ptype\n", "    from dod_searches_curve_raw\n", "    where curr_date_ between date('{sale_start_date}') and date('{sale_end_date}')\n", "),\n", "\n", "item_product_mapping as (\n", "    select item_id, product_id, multiplier\n", "    from dwh.dim_item_product_offer_mapping\n", "    where is_current\n", "),\n", "\n", "item_details as (\n", "    select  \n", "        item_id, item_name, l0_id, l0_category, \n", "                l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "    from supply_etls.item_details\n", "    where assortment_type='Packaged Goods'\n", "        and handling_type = 'Non Packaging Material'\n", "),\n", "\n", "festive_details as (\n", "\n", "    select be_facility_id as facility_id, \n", "        item_id, \n", "        festival_name as festival,\n", "        --sale_start_date, \n", "        --sale_end_date, \n", "        --cut_off_date, \n", "        --substitute_ptype, \n", "        --assortment_reason_type,\n", "        --rsto, \n", "        --rtv,\n", "        sum(planned_quantity) as planned_qty\n", "    from ars_etls.final_festival_planning\n", "    where festival_name is not null\n", "    and festival_name = '{festival}'\n", "    group by 1,2,3\n", "\n", "\n", "    --select \n", "    --    facility_id, item_id, storage, catg_segment,\n", "    --        festival, festival_bau, sale_start, sale_end, cutt_off, planned_qty \n", "    --from supply_etls.final_festival_planning\n", "    --where festival='{festival}'\n", "),\n", "\n", "\n", "\n", "festival_ptype_plan_qty as (\n", "    select facility_id, id.p_type, festival, sum(planned_qty) as planned_qty\n", "    from festive_details fd\n", "    inner join item_details id on id.item_id=fd.item_id\n", "    group by 1,2,3\n", "    \n", "),\n", "\n", "tea_tagging as (\n", "    select --current_date as updated_at, \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, \n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", ")\n", ",\n", "\n", "\n", "final as (\n", "select \n", "    dod_searches_curve.curr_date_,\n", "    '{festival}' as festival,\n", "    final_sales.city,\n", "    --final_sales.po_outlet_id,\n", "    --od.facility_id as be_facility_id,\n", "    --co.name,\n", "    dod_searches_curve.product_type,\n", "    dod_searches_curve.search_device_count,\n", "    dod_searches_curve.ptype_d_day_search_device_count,\n", "    dod_searches_curve.ptype_14d_window_search_device_count,\n", "    dod_searches_curve.search_contri_atc,\n", "    dod_searches_curve.total_demand,\n", "    dod_searches_curve.total_demand_d_day_ptype,\n", "    dod_searches_curve.total_demand_14_day_ptype,\n", "    dod_searches_curve_14_days.avg_search_device_count,\n", "    dod_searches_curve_14_days.avg_ptype_d_day_search_device_count,\n", "    dod_searches_curve_14_days.avg_ptype_14d_window_search_device_count,\n", "    dod_searches_curve_14_days.avg_total_demand,\n", "    dod_searches_curve_14_days.avg_total_demand_d_day_ptype,\n", "    dod_searches_curve_14_days.avg_total_demand_14_day_ptype,\n", "    coalesce(cpd,0.05) as cpd,\n", "    coalesce(bumped_cpd,0.05) as bumped_cpd,\n", "    sales,\n", "    coalesce(potential_sales,0.05) as potential_sales,\n", "    availability_cut,\n", "    buffer,\n", "    try(((coalesce(cpd,0.05) * buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand) / dod_searches_curve_14_days.avg_total_demand) as final_cpd_keyword,\n", "    try(((coalesce(cpd,0.05) * buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_d_day_ptype) / dod_searches_curve_14_days.avg_total_demand_d_day_ptype) as final_cpd_ptype_d_day,\n", "    try(((coalesce(cpd,0.05) * buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_14_day_ptype) / dod_searches_curve_14_days.avg_total_demand_14_day_ptype) as final_cpd_ptype_14d,\n", "    try(((coalesce(potential_sales,0.05) * buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_14_day_ptype) / (1.10*dod_searches_curve_14_days.avg_total_demand_14_day_ptype)) as final_potential_ptype_14d--,\n", "    /*try(((coalesce(planned_qty,0) * buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_14_day_ptype) / (sum(dod_searches_curve.total_demand_14_day_ptype) over (partition by dod_searches_curve.product_type, final_sales.po_outlet_id))) as final_planned_ptype_14d,\n", "\n", "    greatest(\n", "    try(((coalesce(cpd,0.05) * buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_14_day_ptype) \n", "    / dod_searches_curve_14_days.avg_total_demand_14_day_ptype),\n", "    try(((coalesce(planned_qty,0) * buffer * dod_searches_curve.availability_cut) * dod_searches_curve.total_demand_14_day_ptype) \n", "    / (sum(dod_searches_curve.total_demand_14_day_ptype) over (partition by dod_searches_curve.product_type, final_sales.po_outlet_id)))\n", "    ) as max_forecast*/\n", "    \n", "from dod_searches_curve\n", "inner join dod_searches_curve_14_days on dod_searches_curve.product_type = dod_searches_curve_14_days.product_type\n", "inner join final_sales on final_sales.product_type  = dod_searches_curve.product_type\n", "--inner join retail.console_outlet co on co.id = final_sales.po_outlet_id\n", "--inner join outlet_details od on final_sales.po_outlet_id=od.hot_outlet_id\n", "--left join festival_ptype_plan_qty fpq on fpq.facility_id=co.facility_id and fpq.p_type=dod_searches_curve.product_type\n", ")\n", "\n", "select \n", "    curr_date_ as date_,\n", "    festival as festival_name,\n", "    city,\n", "    --be_facility_id,\n", "    --name as be_name,\n", "    product_type,\n", "    cpd,\n", "    coalesce(final_cpd_ptype_14d,0) as city_ptype_forecast_cpd,\n", "    coalesce(final_potential_ptype_14d,0) as city_ptype_forecast_aps--,\n", "    --coalesce(final_planned_ptype_14d,0) as be_ptype_forecast_planned,\n", "    --greatest(coalesce(final_cpd_ptype_14d,0),coalesce(final_planned_ptype_14d,0)) as be_ptype_forecast\n", "from final\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b66d084f-319e-4477-ac0f-29eaac5f33de", "metadata": {}, "outputs": [], "source": ["# df_be_ptype = read_sql_query(be_ptype, CON_TRINO)\n", "# df_be_ptype"]}, {"cell_type": "code", "execution_count": null, "id": "de630dd3-e554-47d6-86e9-e351925a6993", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d3cbe65b-4fcc-4f4b-bfd6-c8384cdf4c31", "metadata": {}, "outputs": [], "source": ["# df_be_ptype.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9de672d0-fe2f-4623-8b6c-608a5a9009d1", "metadata": {}, "outputs": [], "source": ["# column_dtypes = [\n", "#     {\n", "#         \"name\": col,\n", "#         \"type\": str(df_be_ptype[col].dtype).replace(\"int64\", \"integer\").replace(\"float64\", \"real\").replace(\"str\",\"varchar\").replace(\"datetime64[ns]\",\"date\").replace(\"object\",\"varchar\"),\n", "#         \"description\": \"sample description\"\n", "#     } for col in df_be_ptype.columns\n", "# ]\n", "\n", "# column_dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b7962273-b8ba-4e38-b2ef-1cc1bcd174ad", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Sale Date\"},\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival Name\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"City Name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"cpd\", \"type\": \"real\", \"description\": \"Current APS\"},\n", "    {\n", "        \"name\": \"city_ptype_forecast_cpd\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis CPD\",\n", "    },\n", "    {\n", "        \"name\": \"city_ptype_forecast_aps\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Forecast Value basis APS\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7ec78ac0-465d-4cfe-996f-1bc60d2d3970", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_forecast_city_ptype\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"city\",\n", "        \"product_type\",\n", "        \"festival_name\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive inventory forecast\",\n", "}\n", "\n", "to_trino(be_ptype, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}