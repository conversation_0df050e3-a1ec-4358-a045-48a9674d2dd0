{"cells": [{"cell_type": "code", "execution_count": null, "id": "8a212405-4cfe-4ad6-81a6-4df73b5b6df7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "id": "7773eb4d-1848-4388-9e43-cdbc0a710877", "metadata": {}, "outputs": [], "source": ["print(\"Reading data from price_groups & date_input G-sheet\")"]}, {"cell_type": "code", "execution_count": null, "id": "60f02fe4-3fcf-4d2a-9701-9628f3fd7487", "metadata": {}, "outputs": [], "source": ["input_df = pb.from_sheets(\n", "    \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"price_groups\", clear_cache=True\n", ")\n", "\n", "input_df[\"item_id\"] = pd.to_numeric(input_df[\"item_id\"], errors=\"coerce\")\n", "input_df[\"item_id\"] = input_df[\"item_id\"].fillna(-999)\n", "input_df[\"item_id\"] = input_df[\"item_id\"].astype(int)\n", "\n", "input_df[\"flag\"] = input_df[\"flag\"].astype(int)\n", "input_df[\"flag_items\"] = input_df[\"flag_items\"].astype(int)\n", "\n", "\n", "print(f\"Shape of input sheet - price_groups = {input_df.shape}\")\n", "\n", "\n", "df_date_input = pb.from_sheets(\n", "    \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"date_input\", clear_cache=True\n", ")\n", "\n", "\n", "df_date_input[\"start_date\"] = df_date_input[\"festival_start_date\"]\n", "df_date_input[\"end_date\"] = df_date_input[\"festival_end_date\"]\n", "df_date_input[\"festival\"] = df_date_input[\"festival\"]"]}, {"cell_type": "code", "execution_count": null, "id": "27042c21-5724-4dd5-b332-d7d3c44684d4", "metadata": {}, "outputs": [], "source": ["input_df = input_df.merge(\n", "    df_date_input[[\"festival\", \"start_date\", \"end_date\"]], on=\"festival\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "77823860-c31b-4abf-a20c-20abb9adcf08", "metadata": {}, "outputs": [], "source": ["# input_df = pd.DataFrame()\n", "\n", "# input_df['festival'] = ['MOTHERS_DAY_2025-05-11', 'MOTHERS_DAY_2025-05-11', 'MOTHERS_DAY_2025-05-11']\n", "\n", "# input_df['flag'] = [1, 1, 1]\n", "# input_df['flag_items'] = [0, 0, 0]\n", "# input_df['group'] = ['Cake Muffin & Cupcake Group', 'Cake Muffin & Cupcake Group', 'Photo Frame Group']\n", "\n", "# input_df['ptype'] = ['Cupcake', 'Muffin', 'Photo Frame']\n", "\n", "# input_df['item_id'] = [-999, -999, -999]"]}, {"cell_type": "code", "execution_count": null, "id": "b302ec3a-a3ca-4f1b-9dd4-afc77a6459f4", "metadata": {}, "outputs": [], "source": ["# df_date_input = pd.DataFrame()\n", "# df_date_input['festival'] = ['HOLI_2025-03-14','MOTHERS_DAY_2025-05-11']\n", "# df_date_input['start_date'] = ['2024-03-24', '2024-05-10']\n", "# df_date_input['end_date'] = ['2024-03-26', '2024-05-12']"]}, {"cell_type": "code", "execution_count": null, "id": "4bfa4f67-8cd1-48a1-ac0c-e046518839d3", "metadata": {}, "outputs": [], "source": ["# input_df = input_df.merge(df_date_input[['festival', 'start_date', 'end_date']], on = 'festival', how = 'left')\n", "# input_df"]}, {"cell_type": "code", "execution_count": null, "id": "081e7858-93e7-4178-aa82-4d3a25305b9e", "metadata": {}, "outputs": [], "source": ["def escape_single_quotes(value):\n", "    return value.replace(\"'\", \"''\")"]}, {"cell_type": "code", "execution_count": null, "id": "87b245ef-f096-4877-b5c8-ac8f319aaf2e", "metadata": {}, "outputs": [], "source": ["def ptype_based_buckets(df_input_ptypes):\n", "\n", "    if len(df_input_ptypes) == 0:\n", "        print(\"No ptypes to process for bucketing..\")\n", "        return pd.DataFrame()\n", "\n", "    lst_final = []\n", "\n", "    for festival, df in df_input_ptypes.groupby(\"festival\"):\n", "\n", "        print(f\"Computing for {festival}\")\n", "        print()\n", "        start_date = df[\"start_date\"].unique()[0]\n", "        end_date = df[\"end_date\"].unique()[0]\n", "\n", "        df = df.drop(\"item_id\", axis=1).reset_index(drop=True)\n", "        ptypes_lst = df[\"ptype\"].unique().tolist()\n", "        escaped_tuple = tuple(escape_single_quotes(item) for item in ptypes_lst)\n", "        ptype_tuple_str = \", \".join([f\"'{item}'\" for item in escaped_tuple])\n", "\n", "        sql = f\"\"\"\n", "        with \n", "\n", "        item_details as (\n", "            select \n", "                item_id, brand_name, item_name, p_type\n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "        ),\n", "\n", "        item_product_mapping as (\n", "            select item_id, product_id, multiplier, avg_selling_price_ratio, avg_mrp_ratio\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current\n", "        ),\n", "\n", "        order_item as (\n", "            select \n", "            f.item_id,\n", "            f.item_name,\n", "            a.unit_selling_price,\n", "            (a.unit_mrp*d.avg_mrp_ratio)/(d.multiplier*1.0000) as item_unit_mrp,\n", "            (a.unit_selling_price*d.avg_selling_price_ratio)/(d.multiplier*1.0000) as item_unit_sp\n", "            FROM dwh.fact_sales_order_item_details a\n", "            INNER JOIN item_product_mapping d ON a.product_id = d.product_id\n", "            INNER JOIN item_details f ON f.item_id = d.item_id\n", "            WHERE \n", "                order_create_dt_ist >= date('{start_date}') \n", "                and order_create_dt_ist <= date('{end_date}') \n", "\n", "        )\n", "\n", "        select \n", "            distinct o.item_id as item_id,\n", "            id.p_type as ptype,\n", "            avg(o.item_unit_sp) as price\n", "        from order_item o\n", "        left join item_details id\n", "        on o.item_id = id.item_id\n", "        where id.p_type in ({ptype_tuple_str})\n", "        group by 1,2\n", "        \"\"\"\n", "        con = pb.get_connection(\"[Warehouse] Trino\")\n", "        ptype_item_price = pd.read_sql_query(sql=sql, con=con)\n", "\n", "        ptype_item_price = ptype_item_price.merge(df, on=\"ptype\", how=\"left\")\n", "\n", "        low_df_overall = []\n", "        med_df_overall = []\n", "        high_df_overall = []\n", "\n", "        print(f\"Proceeding to the bucketing part for {festival}\")\n", "        print()\n", "        for group, df2 in ptype_item_price.groupby(\"group\"):\n", "            lst_low = []\n", "            lst_medium = []\n", "            lst_high = []\n", "\n", "            median = df2[\"price\"].quantile(0.5)\n", "            Q1 = df2[\"price\"].quantile(0.25)\n", "            Q3 = df2[\"price\"].quantile(0.75)\n", "            IQR = Q3 - Q1\n", "\n", "            b1 = median - 0.5 * IQR\n", "            b2 = median + 0.5 * IQR\n", "\n", "            low = df2[(df2[\"price\"] < b1)]\n", "            med = df2[(df2[\"price\"] > b1) & (df2[\"price\"] <= b2)]\n", "            high = df2[(df2[\"price\"] > b2)]\n", "\n", "            if low.shape[0] > 0:\n", "                lst_low.append(low)\n", "\n", "            if med.shape[0] > 0:\n", "                lst_medium.append(med)\n", "\n", "            if high.shape[0] > 0:\n", "                lst_high.append(high)\n", "\n", "            cols = [\"festival\", \"group\", \"ptype\", \"flag\", \"item_id\", \"flag_items\", \"price\"]\n", "\n", "            if lst_low == []:\n", "                low_df = pd.DataFrame(columns=cols)\n", "                low_df[\"group\"] = low_df[\"group\"] + \"_Low\"\n", "            else:\n", "                low_df = pd.concat(lst_low, axis=0)\n", "                min_low = int(low_df[\"price\"].min())\n", "                max_low = int(low_df[\"price\"].max())\n", "                low_df[\"group\"] = low_df[\"group\"] + \"_Low_\" + str(min_low) + \"_\" + str(max_low)\n", "\n", "            if lst_medium == []:\n", "                med_df = pd.DataFrame(columns=cols)\n", "                med_df[\"group\"] = med_df[\"group\"] + \"_Medium\"\n", "            else:\n", "                med_df = pd.concat(lst_medium, axis=0)\n", "                min_med = int(med_df[\"price\"].min())\n", "                max_med = int(med_df[\"price\"].max())\n", "                med_df[\"group\"] = med_df[\"group\"] + \"_Medium_\" + str(min_med) + \"_\" + str(max_med)\n", "\n", "            if lst_high == []:\n", "                high_df = pd.DataFrame(columns=cols)\n", "                high_df[\"group\"] = high_df[\"group\"] + \"_High\"\n", "            else:\n", "                high_df = pd.concat(lst_high, axis=0)\n", "                min_high = int(high_df[\"price\"].min())\n", "                max_high = int(high_df[\"price\"].max())\n", "                high_df[\"group\"] = high_df[\"group\"] + \"_High_\" + str(min_high) + \"_\" + str(max_high)\n", "\n", "            low_df_overall.append(low_df)\n", "            med_df_overall.append(med_df)\n", "            high_df_overall.append(high_df)\n", "\n", "        final_list = low_df_overall + med_df_overall + high_df_overall\n", "        print(\"Bucketing completed !!!\")\n", "        print()\n", "        df_final = pd.concat(final_list, axis=0)\n", "        df_final = df_final[\n", "            [\"festival\", \"group\", \"ptype\", \"flag\", \"item_id\", \"flag_items\", \"price\"]\n", "        ].reset_index(drop=True)\n", "\n", "        print(f\"Shape of ptype bucket list for {festival} = {df_final.shape}\")\n", "        lst_final.append(df_final)\n", "\n", "    df_final_ptypes_bucket = pd.concat(lst_final, axis=0)\n", "    df_final_ptypes_bucket[\"flag\"] = 1\n", "    df_final_ptypes_bucket[\"flag_items\"] = 1\n", "\n", "    print(f\"Shape of Final ptype bucket list = {df_final_ptypes_bucket.shape}\")\n", "    print(\"Completed all ptype based bucketing !!!\")\n", "    print()\n", "    return df_final_ptypes_bucket"]}, {"cell_type": "code", "execution_count": null, "id": "e0b77377-2962-406c-89c9-6edb86ba5525", "metadata": {}, "outputs": [], "source": ["def item_based_buckets(df_input_items):\n", "\n", "    if len(df_input_items) == 0:\n", "        print(\"No items to process for bucketing..\")\n", "        return pd.DataFrame()\n", "\n", "    lst_final = []\n", "\n", "    for festival, df in df_input_items.groupby(\"festival\"):\n", "\n", "        print(f\"Computing for {festival}\")\n", "        print()\n", "\n", "        start_date = df[\"start_date\"].unique()[0]\n", "        end_date = df[\"end_date\"].unique()[0]\n", "\n", "        df[\"item_id\"] = df[\"item_id\"].astype(int)\n", "        item_list = df[\"item_id\"].unique().tolist()\n", "\n", "        sql = f\"\"\"\n", "        with \n", "\n", "        item_details as (\n", "            select \n", "                item_id, brand_name, item_name, p_type\n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "        ),\n", "\n", "        item_product_mapping as (\n", "            select item_id, product_id, multiplier, avg_selling_price_ratio, avg_mrp_ratio\n", "            from dwh.dim_item_product_offer_mapping\n", "            where is_current\n", "        ),\n", "\n", "        order_item as (\n", "            select \n", "            f.item_id,\n", "            f.item_name,\n", "            a.unit_selling_price,\n", "            (a.unit_mrp*d.avg_mrp_ratio)/(d.multiplier*1.0000) as item_unit_mrp,\n", "            (a.unit_selling_price*d.avg_selling_price_ratio)/(d.multiplier*1.0000) as item_unit_sp\n", "            FROM dwh.fact_sales_order_item_details a\n", "            INNER JOIN item_product_mapping d ON a.product_id = d.product_id\n", "            INNER JOIN item_details f ON f.item_id = d.item_id\n", "            WHERE \n", "                order_create_dt_ist >= date('{start_date}') \n", "                and order_create_dt_ist <= date('{end_date}') \n", "                and f.item_id in {tuple(item_list)}\n", "\n", "        )\n", "\n", "        select \n", "            distinct o.item_id as item_id,\n", "            id.p_type as ptype,\n", "            avg(o.item_unit_sp) as price\n", "        from order_item o\n", "        left join item_details id\n", "        on o.item_id = id.item_id\n", "        group by 1,2\n", "        \"\"\"\n", "        con = pb.get_connection(\"[Warehouse] Trino\")\n", "        ptype_item_price = pd.read_sql_query(sql=sql, con=con)\n", "        ptype_item_price = ptype_item_price[[\"item_id\", \"price\"]].merge(\n", "            df, on=\"item_id\", how=\"inner\"\n", "        )\n", "\n", "        low_df_overall = []\n", "        med_df_overall = []\n", "        high_df_overall = []\n", "\n", "        print(f\"Proceeding to bucketing for {festival}\")\n", "        print()\n", "\n", "        for group, df2 in ptype_item_price.groupby(\"group\"):\n", "            lst_low = []\n", "            lst_medium = []\n", "            lst_high = []\n", "\n", "            median = df2[\"price\"].quantile(0.5)\n", "            Q1 = df2[\"price\"].quantile(0.25)\n", "            Q3 = df2[\"price\"].quantile(0.75)\n", "            IQR = Q3 - Q1\n", "\n", "            b1 = median - 0.5 * IQR\n", "            b2 = median + 0.5 * IQR\n", "\n", "            low = df2[(df2[\"price\"] < b1)]\n", "            med = df2[(df2[\"price\"] >= b1) & (df2[\"price\"] <= b2)]\n", "            high = df2[(df2[\"price\"] > b2)]\n", "\n", "            if low.shape[0] > 0:\n", "                lst_low.append(low)\n", "\n", "            if med.shape[0] > 0:\n", "                lst_medium.append(med)\n", "\n", "            if high.shape[0] > 0:\n", "                lst_high.append(high)\n", "\n", "            cols = [\"festival\", \"group\", \"ptype\", \"flag\", \"item_id\", \"flag_items\", \"price\"]\n", "\n", "            if lst_low == []:\n", "                low_df = pd.DataFrame(columns=cols)\n", "                low_df[\"group\"] = low_df[\"group\"] + \"_Item_Low\"\n", "            else:\n", "                low_df = pd.concat(lst_low, axis=0)\n", "                min_low = int(low_df[\"price\"].min())\n", "                max_low = int(low_df[\"price\"].max())\n", "                low_df[\"group\"] = low_df[\"group\"] + \"_Item_Low_\" + str(min_low) + \"_\" + str(max_low)\n", "\n", "            if lst_medium == []:\n", "                med_df = pd.DataFrame(columns=cols)\n", "                med_df[\"group\"] = med_df[\"group\"] + \"_Item_Medium\"\n", "            else:\n", "                med_df = pd.concat(lst_medium, axis=0)\n", "                min_med = int(med_df[\"price\"].min())\n", "                max_med = int(med_df[\"price\"].max())\n", "                med_df[\"group\"] = (\n", "                    med_df[\"group\"] + \"_Item_Medium_\" + str(min_med) + \"_\" + str(max_med)\n", "                )\n", "\n", "            if lst_high == []:\n", "                high_df = pd.DataFrame(columns=cols)\n", "                high_df[\"group\"] = high_df[\"group\"] + \"_Item_High\"\n", "            else:\n", "                high_df = pd.concat(lst_high, axis=0)\n", "                min_high = int(high_df[\"price\"].min())\n", "                max_high = int(high_df[\"price\"].max())\n", "                high_df[\"group\"] = (\n", "                    high_df[\"group\"] + \"_Item_High_\" + str(min_high) + \"_\" + str(max_high)\n", "                )\n", "\n", "            low_df_overall.append(low_df)\n", "            med_df_overall.append(med_df)\n", "            high_df_overall.append(high_df)\n", "\n", "        final_list = low_df_overall + med_df_overall + high_df_overall\n", "\n", "        df_final = pd.concat(final_list, axis=0)\n", "\n", "        df_final = df_final[\n", "            [\"festival\", \"group\", \"ptype\", \"flag\", \"item_id\", \"flag_items\", \"price\"]\n", "        ]\n", "\n", "        print(f\"Shape of item level bucket list for {festival} = {df_final.shape}\")\n", "        lst_final.append(df_final)\n", "\n", "    df_final_items_bucket = pd.concat(lst_final, axis=0)\n", "    df_final_items_bucket[\"flag\"] = 1\n", "    df_final_items_bucket[\"flag_items\"] = 1\n", "\n", "    print(f\"Shape of Final Item Level bucket list = {df_final_items_bucket.shape}\")\n", "    print(\"Completed all item based bucketing !!!\")\n", "    print()\n", "    return df_final_items_bucket"]}, {"cell_type": "code", "execution_count": null, "id": "8594f29b-c8f1-44d9-b751-59233f21426f", "metadata": {}, "outputs": [], "source": ["df_ptypes_only = input_df[(input_df[\"flag_items\"] != 1) & (input_df[\"flag\"] == 1)]\n", "df_items_only = input_df[(input_df[\"flag_items\"] == 1) & (input_df[\"flag\"] == 1)]"]}, {"cell_type": "code", "execution_count": null, "id": "37bd9306-8a48-420d-92b4-239e42f4c33d", "metadata": {}, "outputs": [], "source": ["ptypes_only_answer = ptype_based_buckets(df_ptypes_only)"]}, {"cell_type": "code", "execution_count": null, "id": "bb259eab-2ffd-4aec-9e56-d7f67b91b10e", "metadata": {}, "outputs": [], "source": ["items_only_answer = item_based_buckets(df_items_only)"]}, {"cell_type": "code", "execution_count": null, "id": "1bd22866-4798-4a17-8a96-b231a212e16a", "metadata": {}, "outputs": [], "source": ["if items_only_answer.shape[0] != 0 or ptypes_only_answer.shape[0] != 0:\n", "\n", "    df_final = pd.concat([ptypes_only_answer, items_only_answer], axis=0)\n", "\n", "    df_final = df_final[[\"festival\", \"group\", \"ptype\", \"flag\", \"item_id\", \"flag_items\"]]\n", "    df_final = df_final.drop_duplicates().reset_index(drop=True)\n", "\n", "    print(f\"Shape of concatenated items + ptype level bucket = {df_final.shape}\")\n", "    print(\"Pushing to G-Sheet sheet_name = grouped_ptypes\")\n", "\n", "    group_ptypes_sheet = pb.from_sheets(\n", "        \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\", \"grouped_ptypes\", clear_cache=True\n", "    )\n", "\n", "    print(f\"Original shape of grouped_ptypes sheet = {group_ptypes_sheet.shape}\")\n", "\n", "    group_ptypes_sheet[\"item_id\"] = pd.to_numeric(group_ptypes_sheet[\"item_id\"], errors=\"coerce\")\n", "    group_ptypes_sheet[\"item_id\"] = group_ptypes_sheet[\"item_id\"].fillna(-999)\n", "    group_ptypes_sheet[\"item_id\"] = group_ptypes_sheet[\"item_id\"].astype(int)\n", "    group_ptypes_sheet = group_ptypes_sheet.drop_duplicates().reset_index(drop=True)\n", "\n", "    print(f\"Shape of grouped_ptypes sheet after dropping duplicates = {group_ptypes_sheet.shape}\")\n", "\n", "    print(group_ptypes_sheet.dtypes)\n", "    print(df_final.dtypes)\n", "\n", "    df_final[\"festival\"] = df_final[\"festival\"].astype(str)\n", "    df_final[\"group\"] = df_final[\"group\"].astype(str)\n", "    df_final[\"ptype\"] = df_final[\"ptype\"].astype(str)\n", "    df_final[\"flag\"] = df_final[\"flag\"].astype(int)\n", "    df_final[\"item_id\"] = df_final[\"item_id\"].astype(int)\n", "    df_final[\"flag_items\"] = df_final[\"flag_items\"].astype(int)\n", "    print(\"Dtypes of df_final after type casting\")\n", "    print(df_final.dtypes)\n", "    print()\n", "\n", "    group_ptypes_sheet[\"festival\"] = group_ptypes_sheet[\"festival\"].astype(str)\n", "    group_ptypes_sheet[\"group\"] = group_ptypes_sheet[\"group\"].astype(str)\n", "    group_ptypes_sheet[\"ptype\"] = group_ptypes_sheet[\"ptype\"].astype(str)\n", "    group_ptypes_sheet[\"flag\"] = group_ptypes_sheet[\"flag\"].astype(int)\n", "    group_ptypes_sheet[\"item_id\"] = group_ptypes_sheet[\"item_id\"].astype(int)\n", "    group_ptypes_sheet[\"flag_items\"] = group_ptypes_sheet[\"flag_items\"].astype(int)\n", "    print(\"Dtypes of group_ptypes_sheet after type casting\")\n", "    print(group_ptypes_sheet.dtypes)\n", "\n", "    input_df_new = group_ptypes_sheet.append(df_final).reset_index(drop=True)\n", "    # input_df_new = input_df_new.drop_duplicates().reset_index(drop=True)\n", "\n", "    print(f\"Shape after appending = {input_df_new.shape}\")\n", "\n", "    input_df_new = (\n", "        input_df_new.groupby([\"festival\", \"group\", \"ptype\", \"item_id\"])\n", "        .agg({\"flag\": \"max\", \"flag_items\": \"max\"})\n", "        .reset_index()\n", "    )\n", "\n", "    print(f\"Shape after dropping duplicates & keeping max flag value = {input_df_new.shape}\")\n", "    print(\n", "        f\"Final shape of grouped_ptypes sheet after appending the buckets is = {input_df_new.shape}\"\n", "    )\n", "\n", "    sheet_id = \"1dL1grhTKlu5RcGH4T0It1Rg5y3MnKxweCKu8ssWixC8\"\n", "    sheet_name = \"grouped_ptypes\"\n", "    pb.to_sheets(input_df_new, sheet_id, sheet_name)\n", "    print(\"Completed the bucketing procedure!!\")\n", "\n", "\n", "else:\n", "    print(\"Nothing to push..\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}