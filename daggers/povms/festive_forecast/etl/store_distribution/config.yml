alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: store_distribution
dag_type: etl
escalation_priority: low
execution_timeout: 60
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U06EEDWNCJF
path: povms/festive_forecast/etl/store_distribution
paused: true
pool: povms_pool
project_name: festive_forecast
schedule:
  end_date: '2025-04-08T00:00:00'
  interval: 30 */2 * * *
  start_date: '2025-02-03T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
