{"cells": [{"cell_type": "code", "execution_count": null, "id": "b26299a7-adbe-41de-b5c8-0cdc7a4a1da4", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "\n", "\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "478045e0-1e86-47f6-9e0f-e75348e73d13", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(\n", "                channel=\"bl-inventory-risk-dashboard\", text=f\"{name} - upload failed\"\n", "            )\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "# Send to Slack\n", "def send_to_slack(channel_id, text_to_send=None, file_paths=None):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            if file_paths is None:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send)\n", "                print(\n", "                    f\"Message: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            else:\n", "                pb.send_slack_message(channel=channel_id, text=text_to_send, files=file_paths)\n", "                print(\n", "                    f\"Message with attached file: {text_to_send}\\nSent to slack successfully in {attempt + 1} attempt\"\n", "                )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d0ee50ce-8dbf-47d0-8fb6-8b63f71a5a67", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\n", "    \"1lapqPvg-5w8Z6jiRiBWce3GyxoZcR7bBZMKFYnIAvUg\", \"input_parameters\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a029ad4f-4da8-4819-a96b-4a92eb97c155", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "a3d2c8e1-85f0-4dbe-8539-8cc1911ea074", "metadata": {}, "outputs": [], "source": ["df[\"flag\"] = df[\"flag\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "id": "f26fbbb1-d4d5-48ae-9109-ca11f9e7f146", "metadata": {}, "outputs": [], "source": ["required_columns = [\n", "    \"festival_name\",\n", "    \"previous_year_start_date\",\n", "    \"previous_year_end_date\",\n", "    \"current_sale_start_date\",\n", "    \"current_sale_end_date\",\n", "]\n", "\n", "if not all(col in df.columns for col in required_columns):\n", "    raise ValueError(\"One or more required columns are missing from the Google Sheet.\")"]}, {"cell_type": "code", "execution_count": null, "id": "3de01460-f79f-4bdd-90ec-2b02402e21b6", "metadata": {}, "outputs": [], "source": ["df1 = df.query(f\"flag==1\")[required_columns].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "68d1111f-5b4b-4c7d-8e92-0579e0fd6328", "metadata": {}, "outputs": [], "source": ["df1"]}, {"cell_type": "code", "execution_count": null, "id": "b705c8a5-7667-4fa9-8ff3-81083b4e5ac8", "metadata": {}, "outputs": [], "source": ["unique_pairs = df.drop(columns=[\"product_type\"]).drop_duplicates()\n", "unique_pairs"]}, {"cell_type": "code", "execution_count": null, "id": "b93b9862-67a6-47ef-b913-3a5dfd5d90ec", "metadata": {}, "outputs": [], "source": ["makar = \"\"\" with ptype_distribution_raw as (\n", "    select *\n", "    from supply_etls.keyword_ptype_distribution\n", "    where date_ between (date('{previous_year_start_date}') - interval'7'day) and (date('{previous_year_end_date}') + interval'7'day)\n", "),\n", "\n", "ptype_distribution0 as (\n", "    select base.date_,\n", "        base.keyword,\n", "        base.product_type,\n", "        coalesce(devices,0) as devices,\n", "        coalesce(total_devices,0) as total_devices,\n", "        coalesce(sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following),0) as nearby_devices,\n", "        coalesce(sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following),0) as nearby_total_devices,\n", "        try(coalesce((1.00 * sum(devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following)) / (1.00 * sum(total_devices) over (partition by base.product_type, base.keyword order by base.date_ rows between 3 preceding and 3 following)),0)) as percentage_attri\n", "        \n", "    from (\n", "            (select distinct date_ from ptype_distribution_raw) \n", "                cross join \n", "            (select distinct keyword, product_type from ptype_distribution_raw)\n", "        ) base\n", "    left join ptype_distribution_raw pdr on pdr.date_ = base.date_ and pdr.keyword = base.keyword and pdr.product_type = base.product_type\n", "), \n", "\n", "ptype_distribution1 as (\n", "    select date_,\n", "        keyword,\n", "        product_type,\n", "        devices,\n", "        total_devices,\n", "        nearby_devices,\n", "        nearby_total_devices,\n", "        percentage_attri,\n", "        rank() over (partition by date_, keyword order by percentage_attri desc) as key_rank\n", "    from ptype_distribution0\n", "    \n", "),\n", "\n", "ptype_distribution as (\n", "    select date_,\n", "        keyword,\n", "        product_type,\n", "        devices,\n", "        total_devices,\n", "        nearby_devices,\n", "        nearby_total_devices,\n", "        percentage_attri,\n", "        try(percentage_attri/(sum(percentage_attri) over(partition by date_, keyword))) as normalized_percent_attri,\n", "        key_rank\n", "    from ptype_distribution1\n", "    where (key_rank<=1 or percentage_attri > 0.05)\n", "),\n", "\n", "\n", "\n", "        \n", "keys_raw as (\n", "    select distinct date_,\n", "        keyword,\n", "        outlet_id,\n", "        search_device_count\n", "    from supply_etls.merchant_dod_keyword_searches_v2\n", "    where date_ between date('{previous_year_start_date}') and date('{previous_year_end_date}')\n", "),\n", "\n", "atc_split as (\n", "    select date_,\n", "        product_type,\n", "        1.000000 * sum(search_atc_device_count) as search_atcs,\n", "        1.000000 * sum(non_search_atc_device_count) as non_search_atcs,\n", "        try((1.000000 * sum(search_atc_device_count))/((1.000000 * sum(search_atc_device_count)) + (1.000000 * sum(non_search_atc_device_count)))) as split\n", "    from supply_etls.ptype_store_atc_channel_split_v2\n", "    where date_ between date('{previous_year_start_date}') and date('{previous_year_end_date}')\n", "    group by 1,2\n", "),\n", "\n", "day_on_day_searches as (\n", "    select keys_raw.date_,\n", "        outlet_id,\n", "        coalesce(ptype_distribution.product_type, atc_split.product_type, 'no_ptype_assigned') as product_type,\n", "        split as search_vs_non_search_atc_split,\n", "        sum(keys_raw.search_device_count) AS search_device_count,\n", "        sum(keys_raw.search_device_count * normalized_percent_attri) AS prob_adjusted_search_device_count\n", "    from keys_raw\n", "    left join ptype_distribution on keys_raw.date_ = ptype_distribution.date_ and keys_raw.keyword = ptype_distribution.keyword\n", "    left join atc_split on keys_raw.date_ = atc_split.date_ and ptype_distribution.product_type = atc_split.product_type\n", "    -- where percentage_attri>0.025\n", "    group by 1,2,3,4\n", "),\n", "\n", "searches_final as (\n", "    select outlet_id,\n", "        product_type,\n", "        avg(search_device_count) as avg_searches_old,\n", "        avg(prob_adjusted_search_device_count) as avg_searches_adj_old\n", "    from day_on_day_searches\n", "    group by 1,2\n", "),\n", "\n", "carts_old as (\n", "    select date(cart_checkout_ts_ist) as date_,\n", "        fsoid.outlet_id, \n", "        count(distinct order_id) as orders_old\n", "    from dwh.fact_sales_order_item_details fsoid\n", "    where order_current_status = 'DELIVERED'\n", "        and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "        and cart_checkout_ts_ist between date('{previous_year_start_date}') and date('{previous_year_end_date}')\n", "        and order_create_dt_ist between date('{previous_year_start_date}') and date('{previous_year_end_date}')\n", "    group by 1,2\n", "),\n", "\n", "carts_final_old as (\n", "    select outlet_id, \n", "        avg(orders_old) as avg_orders_old \n", "    from carts_old \n", "    group by 1 \n", "    having  -- avg_orders_old > 0 -- only stores where we have orders last time only\n", "    avg(orders_old) > 500 -- only stores where we have orders last time only\n", "),\n", "\n", "carts_new as (\n", "select \"date\" as date_,\n", "            outletid as outlet_id,\n", "            sum(aov) as aov,\n", "            sum(carts) as carts\n", "        from logistics_data_etls.cart_projections cp\n", "        inner join \n", "            (select max(updated_on) as ts\n", "            from logistics_data_etls.cart_projections) tst on cp.updated_on = tst.ts\n", "        where \"date\" between date('{current_sale_start_date}')  and date('{current_sale_end_date}')\n", "        group by 1,2\n", ")\n", ",\n", "carts_final_new as (\n", "    select outlet_id,\n", "        avg(carts) as avg_orders\n", "    from carts_new\n", "    group by 1\n", "),\n", "\n", "cross_table as (\n", "    select distinct cfn.outlet_id, t1.product_type from carts_final_new cfn\n", "    cross join (select distinct product_type from searches_final) t1\n", "),\n", "\n", "cluster_city as (\n", "select \n", "    distinct \n", "    cluster_name,\n", "    cm.cluster_id,     \n", "    om.city_id\n", "from \n", "    po.physical_facility_outlet_mapping om\n", "inner join \n", "    retail.console_location cl on cl.id = om.city_id and cl.lake_active_record\n", "    and om.ars_active = 1 and om.active = 1\n", "left join \n", "    rpc.ams_city_cluster_mapping cm on cm.city_id = om.city_id\n", "    and cm.active and cm.lake_active_record\n", "left join \n", "    rpc.ams_cluster c on c.cluster_id = cm.cluster_id and c.lake_active_record\n", "LEFT JOIN retail.console_state cs on cs.id = cl.state_id and cs.lake_active_record\n", "where facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "-- and outlet_name not like '%%Draft%%'\n", "and outlet_name not like '%%Test Store%%'\n", "and outlet_name not like '%%Dummy%%'\n", "and cm.cluster_id > 15000\n", ")\n", "\n", ",\n", "\n", "\n", "final_base as (   \n", "    select \n", "       '{festival_name}' as festival_name,\n", "       '{previous_year_start_date}' as previous_year_start_date,\n", "       '{previous_year_end_date}' as previous_year_end_date,\n", "       '{current_sale_start_date}' as current_sale_start_date,\n", "       '{current_sale_end_date}' as current_sale_end_date,\n", "        ct.outlet_id,\n", "        co.name as outlet_name,\n", "        ct.product_type,\n", "        'PAN' as nation_name,\n", "        cc.cluster_id,\n", "        cc.cluster_name,\n", "        cl.id as city_id,\n", "        cl.name as city_name,\n", "        cfo.avg_orders_old,\n", "        cfn.avg_orders,\n", "        sf.avg_searches_adj_old,\n", "        try(sf.avg_searches_adj_old / cfo.avg_orders_old) as avg_searches_pen_old,\n", "        avg(cfo.avg_orders_old) over (partition by cl.name, ct.product_type) as avg_city_orders_old,\n", "        avg(sf.avg_searches_adj_old) over (partition by cl.name, ct.product_type) as avg_city_searches_old,\n", "        try(avg(sf.avg_searches_adj_old / cfo.avg_orders_old) over (partition by cl.name, ct.product_type)) as avg_city_searches_pen_old,\n", "\n", "        avg(cfo.avg_orders_old) over (partition by cc.cluster_id, ct.product_type) as avg_cluster_orders_old,\n", "        avg(sf.avg_searches_adj_old) over (partition by cc.cluster_id, ct.product_type) as avg_cluster_searches_old,\n", "       try(avg(sf.avg_searches_adj_old / cfo.avg_orders_old) over (partition by cc.cluster_id, ct.product_type)) as avg_cluster_searches_pen_old,\n", "\n", "        avg(cfo.avg_orders_old) over (partition by ct.product_type) as avg_nation_orders_old,\n", "        avg(sf.avg_searches_adj_old) over (partition by ct.product_type) as avg_nation_searches_old,\n", "        try(avg(sf.avg_searches_adj_old/cfo.avg_orders_old) over (partition by ct.product_type)) as avg_nation_searches_pen_old            \n", "        \n", "    from cross_table ct\n", "    left join carts_final_new cfn on ct.outlet_id = cfn.outlet_id\n", "    left join carts_final_old cfo on ct.outlet_id = cfo.outlet_id\n", "    left join searches_final sf on ct.outlet_id = sf.outlet_id and ct.product_type = sf.product_type\n", "    left join retail.console_outlet co on co.id = ct.outlet_id\n", "    left join po.physical_facility_outlet_mapping pfom on pfom.outlet_id  = ct.outlet_id\n", "    left join retail.console_location cl on cl.id = pfom.city_id\n", "    left join cluster_city cc on cl.id = cc.city_id\n", "),\n", "\n", "final_to_final_base as (\n", "    select \n", "    festival_name,\n", "    previous_year_start_date,\n", "    previous_year_end_date,\n", "    current_sale_start_date,\n", "    current_sale_end_date,\n", "    outlet_id,\n", "    outlet_name,\n", "    REGEXP_REPLACE(lower(outlet_name), 'ss|super store|mumbai|kolkata|delhi|gurgaon|hyderabad|chennai|japur|bhopal|indore|ahmedabad|pune|lucknow|kanpur|', '') as short_outlet_name,\n", "    product_type,\n", "    -- nation_name,\n", "    -- city_id,\n", "    city_name,\n", "    avg_orders,\n", "    avg_orders_old,\n", "    avg_searches_adj_old,\n", "    cast(avg_searches_pen_old as real) as avg_searches_pen_old,\n", "    -- tot_searches,\n", "    avg_city_orders_old,\n", "    avg_city_searches_old,\n", "    avg_city_searches_pen_old,\n", "    avg_cluster_orders_old,\n", "    avg_cluster_searches_old,\n", "    avg_cluster_searches_pen_old,\n", "    -- tot_city_searches,\n", "    avg_nation_orders_old,\n", "    avg_nation_searches_old,\n", "    avg_nation_searches_pen_old,\n", "    -- tot_nation_searches,\n", "\n", "    --- FALL BACKS TYPE 1\n", "    case\n", "        when ((avg_orders_old is not null)) then try(avg_orders /  avg_orders_old) *  avg_searches_adj_old -- old store\n", "        -- New store Handling\n", "        when ((avg_orders_old is null) and (avg_city_orders_old is not null)) then try(avg_orders /  avg_city_orders_old) *  avg_city_searches_old -- new store handling but existing city\n", "        when ((avg_orders_old is null) and (avg_city_orders_old is null) and (avg_nation_orders_old is null)) then try(avg_orders /  avg_nation_orders_old) *  avg_nation_searches_old -- new store handling but non - existing city\n", "        else avg_nation_searches_old * try(avg_orders / avg_nation_orders_old)\n", "    end as rel_values_searhes_fall_back,\n", "    \n", "    --- FALL BACKS TYPE 2\n", "    case\n", "        when ((avg_searches_pen_old is not null)) then (avg_orders *  avg_searches_pen_old) -- old store\n", "        -- New store Handling\n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is not null)) then (avg_orders * avg_city_searches_pen_old) -- new store handling but existing city\n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is null) and (avg_nation_searches_pen_old is null)) then (avg_orders  *  avg_nation_searches_pen_old) -- new store handling but non - existing city\n", "        else (avg_orders  * avg_nation_searches_pen_old)\n", "    end as rel_values_searhes_pen_fall_back,\n", "    \n", "    \n", "    --- FALL BACKS TYPE 1 DETAILS\n", "    case\n", "        when ((avg_orders_old is not null)) then 1 -- old store\n", "        -- New store Handling\n", "        when ((avg_orders_old is null) and (avg_city_orders_old is not null)) then 2 -- new store handling but existing city\n", "        when ((avg_orders_old is null) and (avg_city_orders_old is null) and (avg_nation_orders_old is null)) then 3 -- new store handling but non - existing city\n", "        else 4 -- else\n", "    end as value_type,\n", "    \n", "    --- FALL BACKS TYPE 2 DETAILS\n", "    case\n", "        when ((avg_searches_pen_old is not null)) then 1 -- old store\n", "        -- New store Handling\n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is not null)) then 2 -- new store handling but existing city\n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is null) and (avg_nation_searches_pen_old is null)) then 3 -- new store handling but non - existing city\n", "        else 4\n", "    end as value_type2,\n", "    \n", "    case\n", "        when ((avg_searches_pen_old is not null)) then (avg_searches_pen_old) -- old store\n", "        -- New store Handling\n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is not null)) then (avg_city_searches_pen_old) -- new store handling but existing city\n", "        when ((avg_searches_pen_old is null) and (avg_city_searches_pen_old is null) and (avg_cluster_searches_pen_old is not null) and (avg_nation_searches_pen_old is not null)) then avg_cluster_searches_pen_old -- new city \n", "        else (avg_nation_searches_pen_old)\n", "    end as derived_search_pen\n", "    \n", "\n", "from final_base fb \n", "where fb.avg_orders is not null\n", "    and fb.avg_orders > 0\n", "\n", "\n", "),\n", "\n", "percentile_values as (\n", "    select product_type,\n", "        city_name,\n", "        approx_percentile(derived_search_pen,0.10) as th_per_10,\n", "        approx_percentile(derived_search_pen,0.20) as th_per_20,\n", "        approx_percentile(derived_search_pen,0.30) as th_per_30,\n", "        approx_percentile(derived_search_pen,0.70) as th_per_70,\n", "        approx_percentile(derived_search_pen,0.80) as th_per_80,\n", "        approx_percentile(derived_search_pen,0.90) as th_per_90,\n", "        approx_percentile(derived_search_pen,0.90) as th_per_95,\n", "        avg(derived_search_pen) as avg_search_pen,\n", "        stddev_pop(derived_search_pen) as std_dev_search_pen\n", "    from final_to_final_base\n", "    group by 1,2\n", ")\n", "\n", "select   \n", "    festival_name,\n", "    previous_year_start_date,\n", "    previous_year_end_date,\n", "    current_sale_start_date,\n", "    current_sale_end_date,\n", "    outlet_id,\n", "    outlet_name,\n", "    short_outlet_name,\n", "    ff.product_type, \n", "    ff.city_name, \n", "    avg_orders,    \n", "    case \n", "        when derived_search_pen < avg_search_pen - 1.96 * std_dev_search_pen then avg_orders * (avg_search_pen - 1.96 * std_dev_search_pen)\n", "        when derived_search_pen > avg_search_pen + 1.96 * std_dev_search_pen then avg_orders * (avg_search_pen + 1.96 * std_dev_search_pen)\n", "    else avg_orders * derived_search_pen\n", "    end as val\n", "from final_to_final_base ff\n", "left join percentile_values pv on ff.city_name = pv.city_name and ff.product_type = pv.product_type\n", "where ff.product_type in {ptypes}\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b992ff4c-f324-4c61-8852-3d8d3d837ffc", "metadata": {}, "outputs": [], "source": ["event_data = []\n", "for i in range(0, df1.shape[0]):\n", "    prepared_query = makar.format(\n", "        festival_name=df1[\"festival_name\"].iloc[i],\n", "        previous_year_start_date=df1[\"previous_year_start_date\"].iloc[i],\n", "        previous_year_end_date=df1[\"previous_year_end_date\"].iloc[i],\n", "        current_sale_start_date=df1[\"current_sale_start_date\"].iloc[i],\n", "        current_sale_end_date=df1[\"current_sale_end_date\"].iloc[i],\n", "        ptypes=tuple(\n", "            df.query(\n", "                f\"festival_name=='{df1['festival_name'].iloc[i]}' and previous_year_start_date=='{df1['previous_year_start_date'].iloc[i]}'  and previous_year_end_date=='{df1['previous_year_end_date'].iloc[0]}' and current_sale_start_date=='{df1['current_sale_start_date'].iloc[i]}'  and current_sale_end_date=='{df1['current_sale_end_date'].iloc[i]}' \"\n", "            )[\"product_type\"].unique()\n", "        ),\n", "    )\n", "    _data = read_sql_query(prepared_query, CON_TRINO)\n", "    event_data.append(_data)\n", "\n", "\n", "df_makar = pd.concat(event_data, axis=0).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b4a678e4-aaa8-4489-9e52-71f47a3cfc53", "metadata": {}, "outputs": [], "source": ["df_makar"]}, {"cell_type": "code", "execution_count": null, "id": "e63e90a7-6245-400f-b96a-1c2da8baab01", "metadata": {}, "outputs": [], "source": ["df_makar[\"previous_year_start_date\"] = pd.to_datetime(\n", "    df_makar[\"previous_year_start_date\"], errors=\"coerce\"\n", ")  # date\n", "df_makar[\"previous_year_end_date\"] = pd.to_datetime(\n", "    df_makar[\"previous_year_end_date\"], errors=\"coerce\"\n", ")  # date\n", "df_makar[\"current_sale_start_date\"] = pd.to_datetime(\n", "    df_makar[\"current_sale_start_date\"], errors=\"coerce\"\n", ")  # date\n", "df_makar[\"current_sale_end_date\"] = pd.to_datetime(\n", "    df_makar[\"current_sale_end_date\"], errors=\"coerce\"\n", ")  # date"]}, {"cell_type": "code", "execution_count": null, "id": "18b83f51-468c-4e77-8468-d452498881c1", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"festival_name\"},\n", "    {\"name\": \"previous_year_start_date\", \"type\": \"date\", \"description\": \"previous_year_start_date\"},\n", "    {\"name\": \"previous_year_end_date\", \"type\": \"date\", \"description\": \"previous_year_end_date\"},\n", "    {\"name\": \"current_sale_start_date\", \"type\": \"date\", \"description\": \"current_sale_start_date\"},\n", "    {\"name\": \"current_sale_end_date\", \"type\": \"date\", \"description\": \"current_sale_end_date\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "    {\"name\": \"short_outlet_name\", \"type\": \"varchar\", \"description\": \"short_outlet_name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product Type\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"city_name\"},\n", "    {\"name\": \"avg_orders\", \"type\": \"real\", \"description\": \"avg_orders\"},\n", "    {\"name\": \"val\", \"type\": \"real\", \"description\": \"val\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2ed11bc9-091f-438d-aa6e-bdf4d6081ae1", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festival_ptype_store_distribution\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"festival_name\",\n", "        \"outlet_id\",\n", "        \"product_type\",\n", "    ],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"incremental_key\": \"festival_name\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains festive outlet ptype store distribution\",\n", "}\n", "\n", "to_trino(df_makar, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}