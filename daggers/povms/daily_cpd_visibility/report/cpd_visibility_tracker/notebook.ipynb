{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install pymysql"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import json\n", "import pymysql\n", "\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "pos_connection = pb.get_connection(\"[Replica] RDS POS\")\n", "\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "snorlax_read_conn = json.loads(secrets.get(\"snorlax_rds_read_user\"))\n", "host_name = snorlax_read_conn[0]\n", "user_name = snorlax_read_conn[1]\n", "password = snorlax_read_conn[2]\n", "connection = pymysql.connect(\n", "    host=host_name, user=user_name, password=password, autocommit=True, local_infile=1\n", ")\n", "\n", "cp = os.getcwd()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Importing All Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_mapping_df = pb.from_sheets(\n", "    sheetid=\"10e6U4ez0JOMytyg_Iok_he9FsHlLJ4uZCI7hUKv4lWQ\", sheetname=\"city_mapping\"\n", ")\n", "city_mapping_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### List of ARS Active Facilities\n", "facility_id_list_query = \"\"\"\n", "    SELECT DISTINCT fe_outlet_id outlet_id, fe_outlet_name, fe_facility_id facility_id\n", "    FROM ( \n", "        SELECT bom.fe_outlet_id, bom.fe_outlet_name, bom.fe_facility_id, pf.internal_facility_identifier AS fe_facility_name, \n", "        bom.fe_business_type, tea_skus_count, pom.be_outlet_id, pom.be_outlet_name, bom.be_facility_id, \n", "        pf2.internal_facility_identifier AS be_facility_name, bt.name AS be_business_type\n", "        FROM ( \n", "            SELECT bom.facility_id AS be_facility_id, outlet_id AS fe_outlet_id, outlet_name AS fe_outlet_name,\n", "            rco.facility_id AS fe_facility_id, bt.name AS fe_business_type\n", "            FROM metrics.bulk_facility_outlet_mapping bom\n", "            LEFT JOIN (\n", "                SELECT id, name, facility_id, \n", "                CASE \n", "                    WHEN id = 581 THEN 12 \n", "                    ELSE business_type_id \n", "                END AS business_type_id\n", "                FROM lake_retail.console_outlet\n", "                ) rco ON rco.id = bom.outlet_id\n", "            LEFT JOIN lake_retail.console_business_type bt ON bt.id = rco.business_type_id\n", "            LEFT JOIN lake_retail.auth_user au ON au.id = bom.updated_by\n", "            WHERE bom.active = 1\n", "        ) bom\n", "        LEFT JOIN (\n", "            SELECT facility_id AS be_facility_id, outlet_id AS be_outlet_id, outlet_name AS be_outlet_name\n", "            FROM lake_po.physical_facility_outlet_mapping\n", "            WHERE active = 1 AND ars_active = 1 AND is_primary = 1\n", "        ) pom ON pom.be_facility_id = bom.be_facility_id\n", "        LEFT JOIN lake_po.physical_facility pf ON pf.facility_id = bom.fe_facility_id\n", "        LEFT JOIN lake_po.physical_facility pf2 ON pf2.facility_id = bom.be_facility_id\n", "        LEFT JOIN (\n", "            SELECT id,\n", "            CASE \n", "                WHEN id = 581 THEN 12 \n", "                ELSE business_type_id \n", "            END AS business_type_id\n", "            FROM lake_retail.console_outlet\n", "        ) rco ON rco.id = pom.be_outlet_id\n", "        LEFT JOIN lake_retail.console_business_type bt ON bt.id = rco.business_type_id\n", "        LEFT JOIN (\n", "            SELECT COUNT(item_id) AS tea_skus_count, outlet_id, tag_value \n", "            FROM lake_rpc.item_outlet_tag_mapping \n", "            WHERE tag_type_id = 8 AND active = 1\n", "            GROUP BY 2,3\n", "        ) tm ON tm.outlet_id = bom.fe_outlet_id AND tm.tag_value = pom.be_outlet_id\n", "        WHERE pom.be_outlet_id IS NOT NULL AND tea_skus_count IS NOT NULL\n", "        AND pf2.internal_facility_identifier NOT LIKE '%%Perishable%%'\n", "        AND pf2.internal_facility_identifier NOT LIKE '%%PC%%'\n", "        AND pf2.internal_facility_identifier NOT LIKE '%%CPC%%'\n", "        AND pf2.internal_facility_identifier NOT LIKE '%%cpc%%'\n", "        AND bom.fe_business_type IN ('Dark Store - Grofers')\n", "        ORDER BY pf2.internal_facility_identifier, pf.internal_facility_identifier\n", "    ) final\n", "    \"\"\"\n", "facility_id_df = pd.read_sql_query(facility_id_list_query, redshift)\n", "facility_id_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["physical_facility_query = \"\"\"\n", "    SELECT outlet_id, facility_id\n", "    FROM lake_po.physical_facility_outlet_mapping\n", "    WHERE ars_active = 1 AND active = 1\n", "    \"\"\"\n", "out_df = pd.read_sql_query(physical_facility_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Facilities considered for Forecast Adjustment\n", "facility_df = pd.merge(facility_id_df, out_df, on=[\"facility_id\", \"outlet_id\"])\n", "facility_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(set(facility_df[\"facility_id\"].to_list()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment_query = \"\"\"\n", "    SELECT ma.item_id, ma.facility_id, b.name AS city\n", "    FROM lake_rpc.product_facility_master_assortment ma\n", "    JOIN (\n", "        SELECT item_id\n", "        FROM lake_rpc.product_product\n", "        WHERE id IN ( \n", "            SELECT MAX(id) AS id FROM lake_rpc.product_product\n", "            WHERE active = 1 AND approved = 1 GROUP BY item_id\n", "        ) AND outlet_type NOT IN (1) AND perishable NOT IN (1)\n", "    ) rpc ON rpc.item_id = ma.item_id\n", "    JOIN (\n", "        SELECT item_id FROM lake_rpc.item_category_details \n", "        WHERE l0_id NOT IN (1487) AND l2_id NOT IN (116,1961,63,1127)\n", "    ) cd ON cd.item_id = ma.item_id\n", "    LEFT JOIN lake_retail.console_location b ON ma.city_id = b.id\n", "    WHERE ma.active = 1 AND ma.master_assortment_substate_id IN (1)\n", "    AND ma.facility_id IN %(facility_id_list)s\n", "    \"\"\"\n", "\n", "active_assortment_df = pd.read_sql_query(\n", "    active_assortment_query, redshift, params={\"facility_id_list\": facility_id_list}\n", ")\n", "active_assortment_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### City Mapping (Cluster) for Active Assortment\n", "active_assortment_city_df = pd.merge(\n", "    active_assortment_df, city_mapping_df, on=[\"city\"], how=\"left\"\n", ")\n", "active_assortment_city_df = active_assortment_city_df.drop(columns={\"city\"}).rename(\n", "    columns={\"cluster_name\": \"city\"}\n", ")\n", "\n", "active_assortment_city_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Default CPD from Snorlax Table\n", "system_default_cpd = \"\"\"\n", "    SELECT outlet_id, item_id, cpd \n", "    FROM snorlax.default_cpd \n", "    WHERE outlet_id IN %(outlet_id_list)s\n", "    \"\"\"\n", "default_cpd_df = pd.read_sql_query(\n", "    system_default_cpd,\n", "    connection,\n", "    params={\"outlet_id_list\": tuple(set(facility_id_df[\"outlet_id\"].to_list()))},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Active Assortment Item Facility City Level\n", "assortment_1 = pd.merge(\n", "    facility_id_df, active_assortment_city_df, on=[\"facility_id\"], how=\"left\"\n", ")\n", "assortment_1 = assortment_1[~assortment_1.city.isna()]\n", "\n", "#### Default CPD Item Facility City Level\n", "assortment_2 = pd.merge(\n", "    assortment_1, default_cpd_df, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "assortment_2.fillna({\"cpd\": 0.045}, inplace=True)\n", "\n", "#### Final Assortment, Item City Level\n", "assortment_df = (\n", "    assortment_2.groupby([\"city\", \"item_id\"]).agg({\"cpd\": \"sum\"}).reset_index()\n", ")\n", "assortment_df[\"item_id\"] = assortment_df[\"item_id\"].astype(int)\n", "assortment_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_item_asp_query = \"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM lake_rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, \n", "        oid.total_doorstep_return_quantity, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "        (oid.unit_selling_price * 1.000 / im.multiplier) * sales_quantity AS GMV\n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id \n", "        WHERE (oid.cart_checkout_ts_ist BETWEEN (DATE(current_date - 30) || ' 00:00:00')::timestamp AND (DATE(current_date - 1) || ' 23:59:59')::timestamp) \n", "        AND oid.is_internal_order = false \n", "        AND (oid.order_type NOT ILIKE '%%internal%%' OR oid.order_type IS NULL) \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "    ),\n", "\n", "    pre_summary AS (\n", "        SELECT DATE(order_date) AS date_, item_id, cl.name AS city, outlet_id, SUM(sales_quantity) AS sales, SUM(GMV) AS gmv\n", "        FROM sales a\n", "        JOIN lake_retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "        LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "        GROUP BY 1,2,3,4\n", "    )\n", "\n", "    SELECT city, item_id, (SUM(gmv) / (0.00001 + SUM(sales))) AS final_asp\n", "    FROM pre_summary\n", "    GROUP BY 1,2\n", "    \"\"\"\n", "city_item_asp = pd.read_sql_query(city_item_asp_query, redshift)\n", "\n", "item_asp_query = \"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM lake_rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, oid.outlet_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, \n", "        oid.total_doorstep_return_quantity, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity,\n", "        (oid.unit_selling_price * 1.000 / im.multiplier) * sales_quantity AS GMV\n", "        FROM dwh.fact_sales_order_item_details oid\n", "        JOIN item_mapping im on im.product_id = oid.product_id \n", "        WHERE (oid.cart_checkout_ts_ist BETWEEN (DATE(current_date - 30) || ' 00:00:00')::timestamp AND (DATE(current_date - 1) || ' 23:59:59')::timestamp) \n", "        AND oid.is_internal_order = false \n", "        AND (oid.order_type NOT ILIKE '%%internal%%' OR oid.order_type IS NULL) \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "    ),\n", "\n", "    pre_summary AS (\n", "        SELECT DATE(order_date) AS date_, item_id, outlet_id, SUM(sales_quantity) AS sales, SUM(GMV) AS gmv\n", "        FROM sales a\n", "        JOIN lake_retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "        GROUP BY 1,2,3\n", "    )\n", "\n", "    SELECT item_id, (SUM(gmv) / (0.00001 + SUM(sales))) AS final_asp\n", "    FROM pre_summary\n", "    GROUP BY 1\n", "    \"\"\"\n", "item_asp = pd.read_sql_query(item_asp_query, redshift)\n", "\n", "### Variant MRP\n", "variant_mrp_query = \"\"\"\n", "    SELECT a.item_id, variant_mrp * 0.85 AS variant_mrp\n", "    FROM lake_rpc.product_product a\n", "    INNER JOIN (SELECT item_id, MAX(id) AS id FROM lake_rpc.product_product GROUP BY 1) b\n", "    ON a.item_id = b.item_id AND a.id = b.id\n", "    \"\"\"\n", "variant_mrp = pd.read_sql_query(variant_mrp_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### City Mapping (Cluster)\n", "city_item_asp_mapped = pd.merge(city_item_asp, city_mapping_df, on=[\"city\"], how=\"left\")\n", "city_item_asp_mapped = city_item_asp_mapped.drop(columns={\"city\"}).rename(\n", "    columns={\"cluster_name\": \"city\"}\n", ")\n", "\n", "city_item_asp_mapped.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### ASP Calculation\n", "city_item_asp_1 = (\n", "    city_item_asp_mapped.groupby([\"city\", \"item_id\"])\n", "    .agg({\"final_asp\": \"mean\"})\n", "    .reset_index()\n", ")\n", "\n", "assortment_asp_1 = pd.merge(\n", "    assortment_df, city_item_asp_1, on=[\"city\", \"item_id\"], how=\"left\"\n", ").rename(columns={\"final_asp\": \"city_item_asp\"})\n", "\n", "assortment_asp_2 = pd.merge(\n", "    assortment_asp_1, item_asp, on=[\"item_id\"], how=\"left\"\n", ").rename(columns={\"final_asp\": \"item_asp\"})\n", "\n", "assortment_asp_3 = pd.merge(assortment_asp_2, variant_mrp, on=[\"item_id\"], how=\"left\")\n", "\n", "assortment_asp_3[\"asp\"] = np.where(\n", "    assortment_asp_3[\"city_item_asp\"].notnull(),\n", "    assortment_asp_3[\"city_item_asp\"],\n", "    np.where(\n", "        assortment_asp_3[\"item_asp\"].notnull(),\n", "        assortment_asp_3[\"item_asp\"],\n", "        np.where(\n", "            assortment_asp_3[\"variant_mrp\"].notnull(),\n", "            assortment_asp_3[\"variant_mrp\"],\n", "            80,\n", "        ),\n", "    ),\n", ")\n", "assortment_asp_3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_final = assortment_asp_3.drop(\n", "    columns={\"city_item_asp\", \"item_asp\", \"variant_mrp\"}\n", ")\n", "assortment_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_details_query = \"\"\"\n", "    SELECT id.item_id, id.name AS item_name, pb.name AS brand,\n", "    id.manufacturer,\n", "    cd.l0,cd.l1, cd.l2, pt.name AS p_type\n", "    FROM lake_rpc.product_product id\n", "    LEFT JOIN (SELECT item_id, l0, l1, l2 FROM lake_rpc.item_category_details) cd ON cd.item_id = id.item_id\n", "    LEFT JOIN (SELECT id, name FROM lake_rpc.product_brand) pb ON pb.id = id.brand_id\n", "    LEFT JOIN (SELECT item_id, product_id FROM lake_rpc.item_product_mapping) ipm ON ipm.item_id = id.item_id\n", "    LEFT JOIN (SELECT id, type_id FROM lake_cms.gr_product) gp ON gp.id = ipm.product_id\n", "    LEFT JOIN (SELECT id, name FROM lake_cms.gr_product_type) pt ON pt.id = gp.type_id\n", "    WHERE id.id IN (SELECT MAX(id) AS id FROM lake_rpc.product_product pp WHERE pp.active = 1 AND pp.approved = 1 GROUP BY item_id)\n", "    \"\"\"\n", "item_details_df = pd.read_sql_query(item_details_query, redshift)\n", "item_details_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compile All Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_frame = pd.merge(assortment_final, item_details_df, on=[\"item_id\"], how=\"left\")\n", "final_frame[\"gmv\"] = final_frame[\"cpd\"] * final_frame[\"asp\"]\n", "final_frame[\"cityitem\"] = final_frame[\"city\"] + final_frame[\"item_id\"].astype(str)\n", "final_frame.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_frame = final_frame[\n", "    [\n", "        \"city\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"cityitem\",\n", "        \"cpd\",\n", "        \"asp\",\n", "        \"gmv\",\n", "        \"brand\",\n", "        \"p_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"manufacturer\",\n", "    ]\n", "]\n", "pb.to_sheets(\n", "    final_frame,\n", "    sheetid=\"1MUBwaJu0GvjMJbgEGorwxvRKSc5idxTcikTWJYQ0TCE\",\n", "    sheetname=\"Summary\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}