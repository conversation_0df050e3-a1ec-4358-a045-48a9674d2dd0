dag_name: ars_threhold_doi
dag_type: workflow
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
nobuild: true
owner:
  email: <EMAIL>
  slack_id: S03SJ4WQJF8
path: povms/dark_stores/workflow/ars_threhold_doi
paused: true
project_name: darkstore
schedule:
  end_date: '2023-06-06T00:00:00'
  interval: '*/30 * * * *'
  start_date: '2023-06-02T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: dummy
version: 5
pool: povms_pool
