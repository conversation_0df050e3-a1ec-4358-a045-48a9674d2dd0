{"cells": [{"cell_type": "code", "execution_count": null, "id": "4e4c46be-c601-4bd1-b640-9b006dee14ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6c72752b-f600-4bfc-a506-733ce046c1bd", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import requests\n", "from datetime import datetime, timedelta\n", "import time\n", "import pytz"]}, {"cell_type": "code", "execution_count": null, "id": "dd3fd111-5d95-44f3-a5d0-86b130f5b089", "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "85b43210-e9d3-4fe5-b24f-60cd8c6809bb", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "b3c79c5b-f3b6-4b38-bd94-5200699a3d5a", "metadata": {}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            df = pb.from_sheets(sheet_id, sheet_name)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "218d3481-8a0f-46f1-99c4-53bd1aeb2e0e", "metadata": {}, "outputs": [], "source": ["ignore_warehouse = from_sheets(\n", "    \"1J_ggH6U6TBwy0GyC-UVTTSSFuc7IDa1o9E2KqrOjwJc\", \"ignore warehouses\"\n", ")\n", "ignore_warehouse_list = tuple(\n", "    list(ignore_warehouse[\"be_facility_id\"].unique().astype(int))\n", ")\n", "\n", "ignore_store = from_sheets(\n", "    \"1J_ggH6U6TBwy0GyC-UVTTSSFuc7IDa1o9E2KqrOjwJc\", \"ignore stores\"\n", ")\n", "ignore_store_list = tuple(list(ignore_store[\"outlet_id\"].unique().astype(int)))\n", "\n", "toggle = from_sheets(\"1J_ggH6U6TBwy0GyC-UVTTSSFuc7IDa1o9E2KqrOjwJc\", \"toggle\")\n", "toggle = toggle[\"toggle\"][0]\n", "\n", "ignore_warehouse_list, ignore_store_list, toggle"]}, {"cell_type": "code", "execution_count": null, "id": "7f636e3d-f15d-42a6-ae0e-cb4649e493b9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8a8967ec-2a18-40ce-8f40-3b5bf216b60f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9e496fbf-afc9-417b-ab96-cbc7b40df340", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with be_fo as (SELECT pfom.facility_id AS fc_id,\n", "          pf.internal_facility_identifier AS fc_name,\n", "          pfom.outlet_id AS tag_value,\n", "          wom.cloud_store_id AS outlet_id\n", "  FROM lake_po.physical_facility_outlet_mapping pfom\n", "  INNER JOIN lake_retail.warehouse_outlet_mapping wom ON wom.warehouse_id = pfom.outlet_id\n", "  INNER JOIN lake_po.physical_facility pf ON pf.facility_id = pfom.facility_id\n", "  WHERE pfom.active = 1 and pfom.ars_active = 1\n", "  and pf.facility_id in (1,43,92,264,555,603,1206,1320,1546,1574,1575,1872,1873,1876,1964,1983,2006,2010,2015,2076,2078,2082,2123,2141)\n", "  and pf.facility_id not in {ignore_warehouse_list}\n", "),\n", "  \n", "fo as (\n", "    select x.facility_id, x.outlet_id, co.name as outlet_name, cl.name as city,\n", "            REGEXP_REPLACE(lower(co.name), 'ss|upncr|noida|ghaziabad|bengaluru|super store|mumbai|kolkata|delhi|gurgaon|hyderabad|chennai|japur|bhopal|indore|ahmedabad|pune|lucknow|kanpur|upncr|ghaziabad|noida|faridabad|bengaluru|', '') as ds_name\n", "    from po.physical_facility_outlet_mapping x\n", "    inner join retail.console_outlet co on co.id = x.outlet_id and co.active = 1\n", "    inner join retail.console_location cl on cl.id = co.tax_location_id\n", "    where x.lake_active_record and co.lake_active_record and cl.lake_active_record\n", "    and x.ars_active = 1 and x.active = 1 \n", "    group by 1,2,3,4\n", "),\n", "\n", "idd as (\n", "    select item_id, assortment_type from  supply_etls.item_details id\n", "    where assortment_type in ('Packaged Goods') and handling_type_id not in (8)\n", "),\n", "\n", "sd as (\n", "    select * from ims.ims_sto_details\n", "    where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "),\n", "\n", "isi as (\n", "    select * from ims.ims_sto_item\n", "    where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "),\n", "\n", "psi as (\n", "    select * from po.sto_items\n", "    where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "),\n", "\n", "ps as (\n", "    select * from po.sto\n", "    where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "),\n", "\n", "au as (\n", "    select id as user_id, email as user_mail\n", "    from retail.auth_user\n", "    where lake_active_record\n", "),\n", "\n", "core_sto as (\n", "    select isi.created_at + interval '330' minute as sto_creation_time,\n", "            sd.delivery_date + interval '330' minute as sto_expiry_time,\n", "            isi.sto_id, \n", "            case when sd.sto_state=1 then 'CREATED' when sd.sto_state=2 then 'BILLED' when sd.sto_state=3 then 'EXPIRED' when sd.sto_state=4 then 'INWARD' when sd.sto_state=5 then 'FORCE_BILLED' end as sto_state,\n", "            sd.outlet_id as sender_outlet_id, fc_name,\n", "            sd.merchant_outlet_id as receiver_outlet_id, ds_name,\n", "            isi.item_id, idd.assortment_type,\n", "            cast(JSON_EXTRACT(JSON_EXTRACT(ps.meta,'$.destination_entity_vendor'),'$.name') as varchar) as destnation_entity,\n", "            psi.created_by, au.user_mail,\n", "            sum(isi.reserved_quantity) as sto_quantity,\n", "            sum(isi.billed_quantity) as billed_quanity,\n", "            sum(isi.inward_quantity) as inward\n", "    from sd\n", "    left join isi on isi.sto_id = sd.sto_id\n", "    left join psi on psi.sto_id = isi.sto_id and psi.item_id = isi.item_id\n", "    left join ps on ps.id = isi.sto_id\n", "    left join fo on fo.outlet_id = sd.merchant_outlet_id\n", "    inner join idd on idd.item_id = isi.item_id\n", "    inner join au on au.user_id = psi.created_by\n", "    inner join be_fo on be_fo.outlet_id = sd.outlet_id\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "),\n", "\n", "-- select * from core_sto\n", "\n", "pi as (\n", "    SELECT *\n", "   FROM pos.pos_invoice\n", "   WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "     AND invoice_type_id IN (5,14,16)\n", "     AND grofers_order_id != ''\n", "     AND grofers_order_id IS NOT NULL\n", "),\n", "\n", "pid as (\n", "    SELECT *\n", "    FROM pos.pos_invoice_product_details\n", "    WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "),\n", "\n", "pp as (\n", "    select variant_id, item_id from rpc.product_product where lake_active_record and active = 1\n", "),\n", "\n", "\n", "iii as (\n", "    SELECT *\n", "    FROM ims.ims_inward_invoice\n", "    WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "    AND source_type = 2\n", "),\n", "\n", "\n", "isd as (\n", "    SELECT *\n", "    FROM ims.ims_inventory_stock_details\n", "    WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "),\n", "\n", "grn_invoice as (\n", "    select iii.grn_id, vendor_invoice_id, item_id, max(isd.created_at) as isd_created_at,\n", "            sum(coalesce(isd.delta,0)) AS grn1, sum(coalesce(isd.delta,0) * isd.landing_price) AS value1\n", "    from iii\n", "    inner join isd on iii.grn_id = isd.grn_id\n", "    inner join pp on pp.variant_id = isd.variant_id\n", "    group by 1,2,3\n", "),\n", "\n", "gi_core as (\n", "    select vendor_invoice_id as invoice_id, item_id, max(isd_created_at + interval '330' minute) as grn_started_at,\n", "            sum(grn1) AS inwarded_quantity, sum(value1) AS inward_value\n", "    from grn_invoice\n", "    group by 1,2\n", "),\n", "\n", "core_grn as (\n", "    select cast(pi.grofers_order_id as int) as sto_id, pi.invoice_id,\n", "            pi.pos_timestamp + interval '330' minute as sto_invoice_created_at,\n", "            case when cast(transfer_state as integer) = 1 then 'Raised'\n", "                when cast(transfer_state as integer) = 2 then 'GRN Complete'\n", "                when cast(transfer_state as integer) = 3 then 'GRN Partial'\n", "                when cast(transfer_state as integer) = 4 then 'GRN Force Complete'\n", "                when cast(transfer_state as integer) = 5 then 'DISCREPANCY_NOTE_GENERATED'\n", "                else 'NA'\n", "            end as invoice_state,\n", "            pp.item_id, \n", "            \n", "            sum(coalesce(pid.quantity,0)) AS billed_quantity,\n", "            sum(pid.selling_price*coalesce(pid.quantity,0)) AS invoice_value,\n", "            grn_started_at,\n", "            inwarded_quantity,\n", "            inward_value\n", "            \n", "    from pi\n", "    inner join pid on pid.invoice_id = pi.id\n", "    inner join pp on pp.variant_id = pid.variant_id\n", "    left join gi_core gc on gc.invoice_id = pi.invoice_id AND gc.item_id = pp.item_id\n", "    inner join idd on idd.item_id = pp.item_id\n", "    group by 1,2,3,4,5,8,9,10\n", "),\n", "\n", "disruption as (\n", "    select \n", "            cs.sender_outlet_id, fc_name, cs.receiver_outlet_id, ds_name,\n", "            sum(cs.sto_quantity) as sto_quantity, \n", "            sum(cs.billed_quanity) as billed_quanity, sum(cg.billed_quantity) as core_billed_qty, \n", "            sum(cs.inward) as inwarded_quantity, sum(cg.inwarded_quantity) as core_inwarded_quantity,\n", "            sum(cg.billed_quantity) - sum(cg.inwarded_quantity) as delta\n", "            \n", "    from core_sto cs\n", "    left join core_grn cg on cg.sto_id = cs.sto_id and cg.item_id = cs.item_id\n", "    where cg.sto_invoice_created_at <= current_timestamp - interval '36' hour\n", "    and cg.sto_invoice_created_at >= current_timestamp - interval '90' hour\n", "    group by 1,2,3,4\n", "),\n", "\n", "avg_trend as (\n", "    select date(sto_creation_time) as sto_created_date, cs.sender_outlet_id, cs.receiver_outlet_id, sum(sto_quantity) as trend_sto\n", "    from core_sto cs\n", "    where cs.sto_creation_time <= current_timestamp - interval '48' hour\n", "    group by 1,2,3\n", "),\n", "\n", "trend as (\n", "    select sender_outlet_id, receiver_outlet_id, avg(trend_sto) as avg_sto\n", "    from avg_trend\n", "    group by 1,2\n", "),\n", "\n", "workdesk_dis as (\n", "    select outlet_id, mode, percent as wdk_percent, start_date_time, end_date_time, reason_id, created_at, updated_at\n", "    from ars.disruption_v2\n", "    where end_date_time between current_timestamp and current_timestamp + interval '1' day\n", "    and insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "    and mode = 'normal'\n", "),\n", "\n", "wd as (\n", "    select a.outlet_id, a.wdk_percent\n", "    from workdesk_dis a\n", "    inner join (select outlet_id, min(end_date_time) as end_date_time from workdesk_dis group by 1) b on b.outlet_id = a.outlet_id and b.end_date_time = a.end_date_time\n", "),\n", "\n", "\n", "logic_dis as (\n", "    select at.sender_outlet_id, at.receiver_outlet_id,  \n", "            be_fo.fc_name as \"fc::filter\", fo.ds_name, \n", "            avg_sto, delta, \n", "            (case when delta > avg_sto then 100.000 else delta * 100.000 / avg_sto end) as logic_percent\n", "    from trend at\n", "    left join disruption d on d.sender_outlet_id = at.sender_outlet_id and d.receiver_outlet_id = at.receiver_outlet_id\n", "    left join fo on fo.outlet_id = at.receiver_outlet_id\n", "    left join be_fo on be_fo.outlet_id = at.sender_outlet_id\n", "    where d.delta is not null\n", "    and at.receiver_outlet_id not in {ignore_store_list}\n", "),\n", "\n", "dd as (\n", "    select ld.receiver_outlet_id as outlet_id, ds_name, ld.avg_sto, delta, logic_percent, wdk_percent, greatest(coalesce(wdk_percent, 0), coalesce(logic_percent, 0)) as disruption_perc\n", "    from logic_dis ld\n", "    inner join (select receiver_outlet_id, max(avg_sto) as avg_sto from logic_dis group by 1) ll on ll.avg_sto = ld.avg_sto and ll.receiver_outlet_id = ld.receiver_outlet_id\n", "    left join wd on wd.outlet_id = ld.receiver_outlet_id\n", "),\n", "\n", "all_sources as (\n", "    select outlet_id, round(disruption_perc, 0) as disruption_perc\n", "    from dd\n", "    where disruption_perc >= 30\n", "    \n", "    union \n", "    \n", "    select outlet_id, wdk_percent as disruption_perc\n", "    from wd\n", ")\n", "\n", "select outlet_id, max(disruption_perc) as disruption_perc\n", "from all_sources\n", "group by 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8d66d5ee-8cdd-4c86-aea2-92249de874e6", "metadata": {}, "outputs": [], "source": ["df = read_sql_query(query, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "7d3e6f97-808c-4fab-904b-f261eeff6600", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b9dce3c9-dc58-4353-ba5e-308cc88be8dc", "metadata": {}, "outputs": [], "source": ["identified_ts = datetime.now(pytz.timezone(\"Asia/Kolkata\")) + <PERSON><PERSON><PERSON>(\n", "    minutes=10, weeks=0\n", ")\n", "start_time = identified_ts.strftime(\"%Y-%m-%dT%H:%M:%S.000Z\")\n", "end_time = identified_ts.replace(hour=23, minute=59, second=59).strftime(\n", "    \"%Y-%m-%d %H:%M:%S.000Z\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "afafe1bf-d261-4fed-a030-ee613d324ef6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f6f54917-b6f2-49a3-ad65-0727fdace658", "metadata": {}, "outputs": [], "source": ["if toggle == \"Yes\":\n", "\n", "    response_dict = {}\n", "    for index, data in df.iterrows():\n", "        percent = data[\"disruption_perc\"]\n", "        payload = {\n", "            \"outlet_id\": int(data[\"outlet_id\"]),\n", "            \"reason\": 65,\n", "            \"mode\": \"normal\",\n", "            \"percent\": percent,\n", "            \"start_date_time\": start_time,\n", "            \"end_date_time\": end_time,\n", "            \"updated_by\": \"14\",\n", "        }\n", "\n", "        response = requests.post(\n", "            \"https://ars-retail.prod-sgp-k8s.grofer.io/v2/disruption/\",\n", "            headers={\"userid\": \"14\", \"roleids\": \"10\"},\n", "            json=payload,\n", "        )\n", "\n", "        if response.ok:\n", "            response_dict[\n", "                int(data[\"outlet_id\"])\n", "            ] = f\"Success - Disruption percent {percent} applied\"\n", "        else:\n", "            try:\n", "                response_dict[int(data[\"outlet_id\"])] = str(response.json()[\"message\"])\n", "            except:\n", "                response_dict[int(data[\"outlet_id\"])] = response.text\n", "            response_dict[int(data[\"outlet_id\"])] = (\n", "                \"Failure - \" + response_dict[int(data[\"outlet_id\"])]\n", "            )\n", "else:\n", "    print(\"skipped\")"]}, {"cell_type": "code", "execution_count": null, "id": "8d2378f3-5296-4d50-8514-0afcff5da155", "metadata": {}, "outputs": [], "source": ["# response_dict = {}\n", "# for index, data in df.iterrows():\n", "#     payload = {\n", "#         \"outlet_id\": int(data[\"outlet_id\"]),\n", "#         \"reason\": 65,\n", "#         \"mode\": \"normal\",\n", "#         \"percent\": data[\"disruption_perc\"],\n", "#         \"start_date_time\": start_time,\n", "#         \"end_date_time\": end_time,\n", "#         \"updated_by\":\"14\"\n", "#     }\n", "\n", "#     response = requests.post(\"https://ars-retail.prod-sgp-k8s.grofer.io/v2/disruption/\", headers={\n", "#         \"userid\": \"14\",\n", "#         \"roleids\": \"10\"\n", "#     }, json=payload)\n", "\n", "#     print(payload, response.json())\n", "#     response_dict[int(data[\"outlet_id\"])] = response.json()"]}, {"cell_type": "code", "execution_count": null, "id": "f7927847-8378-453c-be24-605c94375bd6", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "pb.send_slack_message(\n", "    \"#bl-temp-disruption-automation-log\", json.dumps(response_dict, indent=2)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1c0dee07", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}