alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-temp-disruption-automation-log
dag_name: ars_disruption_upload
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 8G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/dark_stores/workflow/ars_disruption_upload
paused: true
project_name: dark_stores
schedule:
  end_date: '2024-07-21T00:00:00'
  interval: 30 22 * * *
  start_date: '2024-04-22T00:00:00'
schedule_type: fixed
sla: 20 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
