{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime\n", "import math\n", "import json\n", "import uuid\n", "import logging\n", "from copy import deepcopy\n", "import warnings\n", "import numpy as np\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "import os\n", "import boto3\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import time\n", "logger=logging.getLogger() \n", "logger.setLevel(logging.DEBUG) \n", "\n", "pd.set_option(\"display.max_colwidth\", 40)\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "CON_SQL = pb.get_connection(\"retail\")\n", "CON_PT = pb.get_connection(\"promise_time\")\n", "start = time.time()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_timestamp_in_ist(datetime_utc):\n", "    return datetime_utc + datetime.timedelta(hours=5, minutes=30)\n", "\n", "def get_timestamp_in_utc(datetime_ist):\n", "    return datetime_ist - datetime.timedelta(hours=5, minutes=30)\n", "\n", "today = datetime.datetime.combine(get_timestamp_in_ist(datetime.datetime.utcnow()), datetime.time.min) \n", "until_t = get_timestamp_in_utc(today + datetime.timedelta(days=1))\n", "until_t_plus_one = get_timestamp_in_utc(today + datetime.timedelta(days=2))\n", "until_t_plus_two = get_timestamp_in_utc(today + datetime.timedelta(days=3))\n", "until_t_plus_three = get_timestamp_in_utc(today + datetime.timedelta(days=4))\n", "until_t_plus_four = get_timestamp_in_utc(today + datetime.timedelta(days=5))\n", "until_t_plus_five = get_timestamp_in_utc(today + datetime.timedelta(days=6))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>item_name</th>\n", "      <th>backend_outlet</th>\n", "      <th>tag_value</th>\n", "      <th>frontend_facility</th>\n", "      <th>frontend_outlet</th>\n", "      <th>frontend_outlet_name</th>\n", "      <th>master_assortment_substate_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10000005</td>\n", "      <td>Nivea Men Oil Control 10x Whitening ...</td>\n", "      <td>340</td>\n", "      <td>436</td>\n", "      <td>135</td>\n", "      <td>845</td>\n", "      <td>Super Store Gurgaon 32 Milestone ES3...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10000005</td>\n", "      <td>Nivea Men Oil Control 10x Whitening ...</td>\n", "      <td>714</td>\n", "      <td>713</td>\n", "      <td>161</td>\n", "      <td>926</td>\n", "      <td>Super Store Ghaziabad Nehru Nagar ES...</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id                                item_name  backend_outlet  \\\n", "0  10000005  Nivea Men Oil Control 10x Whitening ...             340   \n", "1  10000005  Nivea Men Oil Control 10x Whitening ...             714   \n", "\n", "  tag_value  frontend_facility  frontend_outlet  \\\n", "0       436                135              845   \n", "1       713                161              926   \n", "\n", "                      frontend_outlet_name  master_assortment_substate_id  \n", "0  Super Store Gurgaon 32 Milestone ES3...                              1  \n", "1  Super Store Ghaziabad Nehru Nagar ES...                              1  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["assortment_query = f\"\"\"SELECT a.item_id,\n", "       f.name AS item_name,\n", "       FLOOR(wom.cloud_store_id) as backend_outlet,\n", "       c.tag_value,\n", "       b.facility_id AS frontend_facility,\n", "       b.id AS frontend_outlet,\n", "       b.name AS frontend_outlet_name,\n", "       a.master_assortment_substate_id \n", "FROM rpc.product_facility_master_assortment a\n", "LEFT JOIN retail.console_outlet b ON a.facility_id = b.facility_id\n", "AND b.active = 1\n", "AND a.active = 1\n", "INNER JOIN rpc.item_outlet_tag_mapping c ON c.tag_type_id = 8\n", "AND c.active = 1\n", "AND c.outlet_id = b.id\n", "AND c.item_id = a.item_id\n", "INNER JOIN\n", "  (SELECT DISTINCT r.item_id,\n", "                   r.name\n", "   FROM rpc.product_product r\n", "   INNER JOIN\n", "     (SELECT item_id,\n", "             max(created_at) AS dated\n", "      FROM rpc.product_product\n", "      GROUP BY 1) a ON a.item_id=r.item_id\n", "   WHERE r.created_at=a.dated\n", "     AND r.outlet_type!=1 ) f ON a.item_id = f.item_id\n", "left join retail.warehouse_outlet_mapping wom\n", "on c.tag_value = wom.warehouse_id\n", "WHERE b.name LIKE '%%ES%%' and b.name NOT LIKE '%%DarkS%%'\n", "  AND a.active = 1\n", "  AND a.master_assortment_substate_id = 1\"\"\"\n", "assortment = pd.read_sql_query(sql=assortment_query, con=CON_SQL)\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>outlet_id_hot</th>\n", "      <th>inventory_holding_outlet</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>435</td>\n", "      <td>337</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>413</td>\n", "      <td>116</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   outlet_id_hot  inventory_holding_outlet\n", "0            435                       337\n", "1            413                       116"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Query to extract inventory holding outlets\n", "# HOT for dual entity\n", "# SSC for single entity\n", "inventory_holding_query = \"\"\"\n", "SELECT\n", "    pfom.outlet_id as outlet_id_hot,\n", "    case\n", "        when wom.cloud_store_id is NULL then pfom.outlet_id\n", "        else wom.cloud_store_id\n", "    end as inventory_holding_outlet\n", "FROM\n", "\tpo.physical_facility_outlet_mapping pfom\n", "    LEFT JOIN retail.warehouse_outlet_mapping wom\n", "    ON pfom.outlet_id = wom.warehouse_id\n", "WHERE\n", "    pfom.active = 1 AND\n", "    pfom.ars_active = 1;\n", "\"\"\"\n", "inventory_holding = pd.read_sql_query(sql=inventory_holding_query, con=CON_SQL)\n", "inventory_holding.head(2)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Joining HOT/Inventory holding outlet with assortment\n", "assortment = pd.merge(assortment, \n", "             inventory_holding, \n", "             left_on=['frontend_outlet'],\n", "             right_on=['outlet_id_hot'],\n", "             how='left')\n", "assortment = assortment.drop(['outlet_id_hot'], axis = 1) \n", "assortment = assortment.rename(columns={'inventory_holding_outlet':'inventory_holding_frontend_outlet'})\n", "\n", "assortment = pd.merge(assortment, \n", "             inventory_holding, \n", "             left_on=['backend_outlet'],\n", "             right_on=['inventory_holding_outlet'],\n", "             how='left')\n", "assortment = assortment.drop(['inventory_holding_outlet'], axis = 1) \n", "assortment = assortment.rename(columns={'outlet_id_hot':'backend_hot_outlet'})"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Stringified list of outlets and items\n", "all_outlets = \", \".join(map(str,list(assortment.inventory_holding_frontend_outlet.unique())+list(assortment.backend_outlet.unique()))).replace(', nan','')\n", "\n", "all_items = \", \".join(map(str,list(assortment.item_id.unique()))).replace(', nan','')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>outlet_id</th>\n", "      <th>available_quantity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10000005</td>\n", "      <td>100</td>\n", "      <td>106.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10000005</td>\n", "      <td>340</td>\n", "      <td>144.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id  outlet_id  available_quantity\n", "0  10000005        100               106.0\n", "1  10000005        340               144.0"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extracting Overall Inventory\n", "query = f\"\"\" SELECT DISTINCT a.item_id, \n", "                a.outlet_id,\n", "                CASE\n", "                    WHEN sum(quantity) > 0 THEN sum(quantity)\n", "                    ELSE 0\n", "                END AS available_quantity\n", "FROM ims.ims_item_inventory AS a\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "          max(updated_at)AS update_at\n", "   FROM ims.ims_item_inventory\n", "   GROUP BY 1,\n", "            2) AS b ON a.item_id=b.item_id\n", "AND a.outlet_id=b.outlet_id\n", "AND a.updated_at=b.update_at\n", "where a.outlet_id in ({all_outlets})\n", "\n", "  AND a.item_id in ({all_items})\n", "GROUP BY 1,\n", "         2\"\"\"\n", "total_inventory = pd.read_sql_query(query, CON_SQL)\n", "total_inventory.head(2)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>outlet_id</th>\n", "      <th>blocked_quantity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10000005</td>\n", "      <td>100</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10000005</td>\n", "      <td>340</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id  outlet_id  blocked_quantity\n", "0  10000005        100               6.0\n", "1  10000005        340               2.0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extracting blocked inventory\n", "\n", "blocked = f\"\"\"select item_id ,\n", "     i.outlet_id ,\n", "     case when sum(quantity) > 0 then sum(quantity) else 0 end as blocked_quantity\n", "FROM ims.ims_item_blocked_inventory i\n", "where i.outlet_id in ({all_outlets})\n", "and item_id in ({all_items})\n", "and blocked_type in (1,2,4,5) and i.active = 1\n", "group by 1,2\n", "\"\"\"\n", "\n", "block_quantity = pd.read_sql_query(blocked, CON_SQL)\n", "block_quantity.head(2)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>outlet_id</th>\n", "      <th>available_quantity</th>\n", "      <th>blocked_quantity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10000005</td>\n", "      <td>100</td>\n", "      <td>106.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10000005</td>\n", "      <td>340</td>\n", "      <td>144.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id  outlet_id  available_quantity  blocked_quantity\n", "0  10000005        100               106.0               6.0\n", "1  10000005        340               144.0               2.0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Joining blocked and unblocked inventory\n", "inv_data = total_inventory.merge(block_quantity, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "inv_data = inv_data.drop_duplicates().fillna(0)\n", "inv_data.head(2)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>item_name</th>\n", "      <th>backend_outlet</th>\n", "      <th>tag_value</th>\n", "      <th>frontend_facility</th>\n", "      <th>frontend_outlet</th>\n", "      <th>frontend_outlet_name</th>\n", "      <th>master_assortment_substate_id</th>\n", "      <th>inventory_holding_frontend_outlet</th>\n", "      <th>backend_hot_outlet</th>\n", "      <th>available_quantity_frontend</th>\n", "      <th>blocked_quantity_frontend</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10000005</td>\n", "      <td>Nivea Men Oil Control 10x Whitening ...</td>\n", "      <td>340</td>\n", "      <td>436</td>\n", "      <td>135</td>\n", "      <td>845</td>\n", "      <td>Super Store Gurgaon 32 Milestone ES3...</td>\n", "      <td>1</td>\n", "      <td>846.0</td>\n", "      <td>436</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10000005</td>\n", "      <td>Nivea Men Oil Control 10x Whitening ...</td>\n", "      <td>714</td>\n", "      <td>713</td>\n", "      <td>161</td>\n", "      <td>926</td>\n", "      <td>Super Store Ghaziabad Nehru Nagar ES...</td>\n", "      <td>1</td>\n", "      <td>926.0</td>\n", "      <td>713</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id                                item_name  backend_outlet  \\\n", "0  10000005  Nivea Men Oil Control 10x Whitening ...             340   \n", "1  10000005  Nivea Men Oil Control 10x Whitening ...             714   \n", "\n", "  tag_value  frontend_facility  frontend_outlet  \\\n", "0       436                135              845   \n", "1       713                161              926   \n", "\n", "                      frontend_outlet_name  master_assortment_substate_id  \\\n", "0  Super Store Gurgaon 32 Milestone ES3...                              1   \n", "1  Super Store Ghaziabad Nehru Nagar ES...                              1   \n", "\n", "   inventory_holding_frontend_outlet  backend_hot_outlet  \\\n", "0                              846.0                 436   \n", "1                              926.0                 713   \n", "\n", "   available_quantity_frontend  blocked_quantity_frontend  \n", "0                          4.0                        0.0  \n", "1                          3.0                        0.0  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Joining inventory with assortment on frontend outlet\n", "assortment_frontend_qtys = pd.merge(assortment,\n", "         inv_data,\n", "         left_on=['item_id', 'inventory_holding_frontend_outlet'], \n", "         right_on=['item_id','outlet_id'],\n", "         how='left')\n", "\n", "assortment_frontend_qtys = assortment_frontend_qtys.\\\n", "                                rename(columns={\"available_quantity\":\"available_quantity_frontend\",\n", "                                                \"blocked_quantity\":\"blocked_quantity_frontend\"})\n", "\n", "assortment_frontend_qtys = assortment_frontend_qtys.drop(['outlet_id'], axis = 1) \n", "\n", "assortment_frontend_qtys.fillna(0, inplace=True)\n", "\n", "assortment_frontend_qtys.head(2)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Total quantity frontend = Overall - blocked (0 if -ve)\n", "assortment_frontend_qtys['total_quantity_frontend'] = np.where(assortment_frontend_qtys['blocked_quantity_frontend'] > assortment_frontend_qtys['available_quantity_frontend'],\n", "                                                              0,assortment_frontend_qtys['available_quantity_frontend']-assortment_frontend_qtys['blocked_quantity_frontend'])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>item_name</th>\n", "      <th>backend_outlet</th>\n", "      <th>tag_value</th>\n", "      <th>frontend_facility</th>\n", "      <th>frontend_outlet</th>\n", "      <th>frontend_outlet_name</th>\n", "      <th>master_assortment_substate_id</th>\n", "      <th>inventory_holding_frontend_outlet</th>\n", "      <th>backend_hot_outlet</th>\n", "      <th>available_quantity_frontend</th>\n", "      <th>blocked_quantity_frontend</th>\n", "      <th>total_quantity_frontend</th>\n", "      <th>available_quantity_backend</th>\n", "      <th>blocked_quantity_backend</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10000005</td>\n", "      <td>Nivea Men Oil Control 10x Whitening ...</td>\n", "      <td>340</td>\n", "      <td>436</td>\n", "      <td>135</td>\n", "      <td>845</td>\n", "      <td>Super Store Gurgaon 32 Milestone ES3...</td>\n", "      <td>1</td>\n", "      <td>846.0</td>\n", "      <td>436</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>144.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10000005</td>\n", "      <td>Nivea Men Oil Control 10x Whitening ...</td>\n", "      <td>714</td>\n", "      <td>713</td>\n", "      <td>161</td>\n", "      <td>926</td>\n", "      <td>Super Store Ghaziabad Nehru Nagar ES...</td>\n", "      <td>1</td>\n", "      <td>926.0</td>\n", "      <td>713</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>190.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id                                item_name  backend_outlet  \\\n", "0  10000005  Nivea Men Oil Control 10x Whitening ...             340   \n", "1  10000005  Nivea Men Oil Control 10x Whitening ...             714   \n", "\n", "  tag_value  frontend_facility  frontend_outlet  \\\n", "0       436                135              845   \n", "1       713                161              926   \n", "\n", "                      frontend_outlet_name  master_assortment_substate_id  \\\n", "0  Super Store Gurgaon 32 Milestone ES3...                              1   \n", "1  Super Store Ghaziabad Nehru Nagar ES...                              1   \n", "\n", "   inventory_holding_frontend_outlet  backend_hot_outlet  \\\n", "0                              846.0                 436   \n", "1                              926.0                 713   \n", "\n", "   available_quantity_frontend  blocked_quantity_frontend  \\\n", "0                          4.0                        0.0   \n", "1                          3.0                        0.0   \n", "\n", "   total_quantity_frontend  available_quantity_backend  \\\n", "0                      4.0                       144.0   \n", "1                      3.0                       190.0   \n", "\n", "   blocked_quantity_backend  \n", "0                       2.0  \n", "1                       0.0  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Joining inventory with assortment on backend outlet\n", "assortment_backend_qtys = pd.merge(assortment_frontend_qtys,\n", "         inv_data,\n", "         left_on=['item_id', 'backend_outlet'], \n", "         right_on=['item_id','outlet_id'],\n", "         how='left')\n", "\n", "assortment_backend_qtys = assortment_backend_qtys.\\\n", "                                rename(columns={\"available_quantity\":\"available_quantity_backend\",\n", "                                                \"blocked_quantity\":\"blocked_quantity_backend\"})\n", "\n", "assortment_backend_qtys = assortment_backend_qtys.drop(['outlet_id'], axis = 1) \n", "\n", "assortment_backend_qtys.fillna(0, inplace=True)\n", "\n", "assortment_backend_qtys.head(2)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Total quantity backend = Overall - blocked (0 if -ve)\n", "assortment_backend_qtys['total_quantity_backend'] = np.where(assortment_backend_qtys['blocked_quantity_backend'] > assortment_backend_qtys['available_quantity_backend'],\n", "                                                              0,assortment_backend_qtys['available_quantity_backend']-assortment_backend_qtys['blocked_quantity_backend'])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>outlet_id</th>\n", "      <th>item_id</th>\n", "      <th>intransit_quantity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100</td>\n", "      <td>10000005</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>100</td>\n", "      <td>10000006</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   outlet_id   item_id  intransit_quantity\n", "0        100  10000005                 0.0\n", "1        100  10000006                 0.0"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extracting open STOs\n", "sto_query = f\"\"\"\n", "SELECT merchant_outlet_id AS outlet_id,\n", "       item_id,\n", "       sum(quantity) AS intransit_quantity\n", "FROM ims.ims_sto_mapping_inventory\n", "WHERE sto_type IN (1,2)\n", "  AND inventory_type IN (1,2)\n", "  AND merchant_outlet_id in ({all_outlets})\n", "  AND item_id in ({all_items})\n", "GROUP BY 1,\n", "         2\"\"\"\n", "stos_grouped = pd.read_sql_query(sql=sto_query,con=CON_SQL)\n", "stos_grouped.head(2)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# Join <PERSON> with assortment \n", "if len(stos_grouped) > 0:\n", "    assortment_sto = pd.merge(assortment_backend_qtys, \n", "                     stos_grouped, \n", "                     left_on = ['item_id','inventory_holding_frontend_outlet'], \n", "                     right_on=['item_id','outlet_id'], \n", "                     how='left')\n", "\n", "    assortment_sto = assortment_sto.drop(['outlet_id'], axis = 1) \n", "\n", "    assortment_sto.fillna(0, inplace=True)\n", "\n", "    assortment_sto.head(2)\n", "else:\n", "    assortment_sto = assortment_backend_qtys\n", "    assortment_sto['intransit_quantity'] = 0"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>backend_outlet</th>\n", "      <th>frontend_outlet</th>\n", "      <th>item_id</th>\n", "      <th>feeder_enabled</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>340</td>\n", "      <td>1010</td>\n", "      <td>10000005</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>340</td>\n", "      <td>1010</td>\n", "      <td>10000006</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   backend_outlet  frontend_outlet   item_id  feeder_enabled\n", "0             340             1010  10000005               1\n", "1             340             1010  10000006               1"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Feeder - item - outlet live combination \n", "feeder_live_query = \"\"\"SELECT feeder_id AS backend_outlet,\n", "       outlet_id as frontend_outlet,\n", "       item_id,\n", "       1 as feeder_enabled \n", "FROM ims.feeder_outlet_item_mapping\n", "WHERE enabled = 1\n", "  AND (feeder_id,\n", "       outlet_id) IN\n", "    (SELECT feeder_id,\n", "            outlet_id\n", "     FROM ims.feeder_outlet_mapping\n", "     WHERE active = 1)\"\"\"\n", "feeder_live = pd.read_sql_query(sql=feeder_live_query,con=CON_SQL)\n", "feeder_live.head(2)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["assortment_feeder_live = pd.merge(assortment_sto,\n", "                             feeder_live,\n", "                             on=['backend_outlet',\n", "                                 'frontend_outlet',\n", "                                 'item_id'],\n", "                             how='left')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>inventory_holding_frontend_outlet</th>\n", "      <th>until_t</th>\n", "      <th>until_t_plus_one</th>\n", "      <th>until_t_plus_two</th>\n", "      <th>until_t_plus_three</th>\n", "      <th>until_t_plus_four</th>\n", "      <th>until_t_plus_five</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10000005</td>\n", "      <td>100</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10000005</td>\n", "      <td>280</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id  inventory_holding_frontend_outlet  until_t  until_t_plus_one  \\\n", "0  10000005                                100      0.0               3.0   \n", "1  10000005                                280      0.0               6.0   \n", "\n", "   until_t_plus_two  until_t_plus_three  until_t_plus_four  until_t_plus_five  \n", "0               0.0                 0.0                0.0                0.0  \n", "1               0.0                 0.0                0.0                0.0  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# All orders at frontend outlets\n", "orders_frontend_query = f\"\"\"\n", "SELECT i.item_id,\n", "       o.outlet AS inventory_holding_frontend_outlet,\n", "       sum(CASE\n", "               WHEN scheduled_at<='{str(until_t)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t,\n", "       sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t)}' AND '{str(until_t_plus_one)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_one,\n", "       sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_one)}' AND '{str(until_t_plus_two)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_two,\n", "        sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_two)}' AND '{str(until_t_plus_three)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_three,\n", "        sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_three)}' AND '{str(until_t_plus_four)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_four,\n", "        sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_four)}' AND '{str(until_t_plus_five)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_five\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "WHERE scheduled_at BETWEEN '{str(today)}' AND '{str(until_t_plus_one)}'\n", "  AND o.status_id IN (1,\n", "                      3)\n", "  AND i.billed_at_pos = 0\n", "GROUP BY 1,\n", "         2\n", "\"\"\"\n", "orders_frontend = pd.read_sql_query(sql=orders_frontend_query,con=CON_SQL)\n", "orders_frontend.head(2)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["assortment_orders = pd.merge(assortment_feeder_live,\n", "                     orders_frontend,\n", "                     on=['inventory_holding_frontend_outlet',\n", "                         'item_id'],\n", "                     how='left')"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["#TODO\n", "buffer_quantity_query = \"\"\"\n", "SELECT DISTINCT feeder_id as backend_outlet,\n", "                   item_id,\n", "                   buffer_quantity\n", "   FROM ims.feeder_item_buffer \n", "   WHERE active = 1\n", "\"\"\"\n", "buffer_quantity = pd.read_sql_query(sql=buffer_quantity_query,con=CON_SQL)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["assortment_buffer = pd.merge(assortment_orders, \n", "                         buffer_quantity, \n", "                         on=['backend_outlet','item_id'],\n", "                         how='left')\n", "assortment_buffer.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>inventory_holding_frontend_outlet</th>\n", "      <th>app_live</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10002965</td>\n", "      <td>475</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10007596</td>\n", "      <td>1159</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10005732</td>\n", "      <td>714</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10002880</td>\n", "      <td>907</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10067925</td>\n", "      <td>907</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id  inventory_holding_frontend_outlet  app_live\n", "0  10002965                                475         1\n", "1  10007596                               1159         1\n", "2  10005732                                714         1\n", "3  10002880                                907         1\n", "4  10067925                                907         1"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["app_live_query = \"\"\"\n", "WITH item_merchant AS\n", "  ( SELECT DISTINCT mpm.product_id,\n", "                    mpm.merchant_id\n", "   FROM lake_cms.gr_merchant_product_mapping AS mpm\n", "   WHERE mpm.inventory_limit>0\n", "     AND mpm.enabled_flag=1\n", "     AND IS_AVAILABLE = 1),\n", "     merchant_mapping AS\n", "  ( SELECT frontend.id frontend_id,\n", "           frontend.name frontend_name,\n", "           backend.id backend_id,\n", "           backend.name backend_name\n", "   FROM lake_cms.gr_merchant frontend\n", "   JOIN lake_cms.gr_virtual_to_real_merchant_mapping vmm ON frontend.id=vmm.virtual_merchant_id\n", "   JOIN lake_cms.gr_merchant backend ON backend.id=vmm.real_merchant_id\n", "   WHERE frontend.enabled_flag =1\n", "     AND vmm.enabled_flag=1\n", "     AND backend.name NOT ILIKE '%%Smart Bachat Club%%'\n", "     AND backend.name NOT ILIKE '%%Donation%%'\n", "     AND backend.name NOT ILIKE '%%Collection%%'\n", "     AND backend.name NOT ILIKE '%%Collect Return%%'\n", "     AND backend.name NOT ILIKE '%%Dummy%%'\n", "     AND backend.name NOT ILIKE '%%test%%' ),\n", "     final_data AS\n", "  ( SELECT DISTINCT a.*\n", "   FROM item_merchant a\n", "   INNER JOIN merchant_mapping m ON m.backend_id=a.merchant_id),\n", "     item_outlet AS\n", "  ( SELECT DISTINCT CURRENT_DATE AS order_date,\n", "                                    mpm.product_id,\n", "                                    rt.outlet_id\n", "   FROM final_data mpm\n", "   INNER JOIN lake_retail.console_outlet_cms_store rt ON mpm.merchant_id=rt.cms_store\n", "   WHERE rt.Active=1\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     product_offer_mapping AS\n", "  ( SELECT item_id,\n", "           a.product_id,\n", "           outlet_id,\n", "           order_date,\n", "           offer_id\n", "   FROM item_outlet AS a\n", "   LEFT JOIN lake_rpc.item_product_mapping AS b ON a.product_id=b.product_id\n", "   WHERE b.active=1 ),\n", "     item_not_null AS\n", "  ( SELECT item_id,\n", "           order_date,\n", "           outlet_id\n", "   FROM product_offer_mapping\n", "   WHERE item_id IS NOT NULL ),\n", "     item_null AS\n", "  ( SELECT *\n", "   FROM product_offer_mapping\n", "   WHERE item_id IS NULL ),\n", "     offer_details AS\n", "  (SELECT DISTINCT offer_reference_id :: varchar,\n", "                   cast(field_val AS int) AS item_id\n", "   FROM lake_offer_master.offer\n", "   LEFT JOIN lake_offer_master.offer_field o_field ON o_field.offer_id=offer.id\n", "   LEFT JOIN lake_offer_master.offer_type_field otf ON o_field.offer_type_field_id = otf.id\n", "   WHERE otf.field_name LIKE '%%product%%'),\n", "     item_offer_joining AS\n", "  ( SELECT b.item_id,\n", "           product_id,\n", "           outlet_id,\n", "           order_date,\n", "           offer_id\n", "   FROM item_null AS a\n", "   LEFT JOIN offer_details AS b ON a.offer_id=b.offer_reference_id),\n", "     item_facility_final AS\n", "  ( SELECT item_id,\n", "           order_date,\n", "           outlet_id\n", "   FROM item_offer_joining\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     union_table AS\n", "  (SELECT a.*\n", "   FROM\n", "     ((SELECT *\n", "      FROM item_not_null) \n", "   UNION\n", "     (SELECT *\n", "      FROM item_facility_final)) a )\n", "SELECT item_id,\n", "outlet_id as inventory_holding_frontend_outlet,\n", "1 as app_live\n", "FROM union_table\n", "WHERE item_id IS NOT NULL\n", "\"\"\"\n", "app_live = pd.read_sql_query(sql=app_live_query, con=CON_REDSHIFT)\n", "app_live.head()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["assortment_applive = pd.merge(assortment_buffer, \n", "                         app_live, \n", "                         on=['inventory_holding_frontend_outlet','item_id'],\n", "                         how='left')\n", "assortment_applive.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>inventory_holding_frontend_outlet</th>\n", "      <th>item_id</th>\n", "      <th>landing_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>948</td>\n", "      <td>10042663</td>\n", "      <td>52.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>948</td>\n", "      <td>10045762</td>\n", "      <td>7.817</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  inventory_holding_frontend_outlet   item_id  landing_price\n", "0                               948  10042663         52.000\n", "1                               948  10045762          7.817"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["landing_price_query = \"\"\"SELECT lp.outlet_id as inventory_holding_frontend_outlet,\n", "lp.item_id,lp.landing_price\n", "FROM consumer.weighted_landing_price lp\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "           max(dt_ist) AS dt_ist\n", "   FROM consumer.weighted_landing_price group by 1,2) lpp ON lp.dt_ist = lpp.dt_ist\n", "AND lp.item_id = lpp.item_id\n", "AND lp.outlet_id = lpp.outlet_id\n", "\"\"\"\n", "landing_price = pd.read_sql_query(sql=landing_price_query, con=CON_REDSHIFT)\n", "landing_price.head(2)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id</th>\n", "      <th>weighted_landing_price_avg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10041099</td>\n", "      <td>52.550926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10003090</td>\n", "      <td>111.638586</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    item_id  weighted_landing_price_avg\n", "0  10041099                   52.550926\n", "1  10003090                  111.638586"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["default_landing_price_query = \"\"\"SELECT lp.item_id, avg(landing_price) as weighted_landing_price_avg\n", "FROM consumer.weighted_landing_price lp\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "           max(dt_ist) AS dt_ist\n", "   FROM consumer.weighted_landing_price group by 1,2) lpp ON lp.dt_ist = lpp.dt_ist\n", "AND lp.item_id = lpp.item_id\n", "AND lp.outlet_id = lpp.outlet_id\n", "group by 1\"\"\"\n", "\n", "default_landing_price = pd.read_sql_query(sql=default_landing_price_query, con=CON_REDSHIFT)\n", "default_landing_price.head(2)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["landing_price = landing_price.astype({\"inventory_holding_frontend_outlet\":int,\n", "                      \"item_id\":int})\n", "default_landing_price = default_landing_price.astype({\"item_id\":int})"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["assortment_lp = pd.merge(assortment_applive,\n", "         landing_price,\n", "         on=[\"item_id\",\n", "             \"inventory_holding_frontend_outlet\"]\n", "         ,how='left')"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["assortment_lp_avg = pd.merge(assortment_lp,\n", "         default_landing_price,\n", "         on=[\"item_id\"]\n", "         ,how='left')"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["merchant_mapping_query = f\"\"\"\n", "SELECT DISTINCT\n", "        pco.id AS inventory_holding_frontend_outlet,\n", "        bom.external_id AS backend_merchant_id\n", "    FROM  lake_oms_bifrost.oms_merchant AS bom\n", "    INNER JOIN lake_retail.console_outlet_cms_store AS cms\n", "        ON bom.external_id = cms.cms_store \n", "        AND cms.active = 1 \n", "        AND cms.cms_update_active = 1\n", "        AND virtual_merchant_type <> 'superstore_merchant'\n", "    INNER JOIN lake_retail.console_outlet AS pco\n", "        ON cms.outlet_id = pco.id\n", "    INNER JOIN lake_crates.facility AS cf \n", "        ON cf.id = pco.facility_id  \t\t\n", "     \"\"\"\n", "merchant_mapping = pd.read_sql_query(sql=merchant_mapping_query,con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["assortment_merc = pd.merge(assortment_lp_avg,\n", "         merchant_mapping,\n", "         on=[\"inventory_holding_frontend_outlet\"]\n", "         ,how='left')"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["mrp_query = \"\"\"select\n", "item_id,\n", "variant_mrp\n", "from\n", "lake_rpc.product_product\"\"\"\n", "mrp = pd.read_sql_query(sql=mrp_query,con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["assortment_merc = assortment_merc.query('inventory_holding_frontend_outlet > 0')"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["assortment_mrp = pd.merge(assortment_merc,mrp,on=['item_id'],how='left')"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["pt_query = \"\"\"\n", "SELECT backend_merchant_id,\n", "       sum(CASE\n", "               WHEN user_type = 'SBC'\n", "                    AND order_type = 'heavy' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_sbc_heavy,\n", "       sum(CASE\n", "               WHEN user_type = 'Non-SBC'\n", "                    AND order_type = 'normal' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_non_sbc_normal,\n", "       sum(CASE\n", "               WHEN user_type = 'SBC'\n", "                    AND order_type = 'normal' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_sbc_normal,\n", "       sum(CASE\n", "               WHEN user_type = 'Non-SBC'\n", "                    AND order_type = 'heavy' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_non_sbc_heavy\n", "FROM earliest_slot_details\n", "GROUP BY 1\n", "\"\"\"\n", "pt = pd.read_sql_query(sql=pt_query,con=CON_PT)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>backend_merchant_id</th>\n", "      <th>pt_hours_sbc_heavy</th>\n", "      <th>pt_hours_non_sbc_normal</th>\n", "      <th>pt_hours_sbc_normal</th>\n", "      <th>pt_hours_non_sbc_heavy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>29107</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    backend_merchant_id  pt_hours_sbc_heavy  pt_hours_non_sbc_normal  \\\n", "57                29107                 3.0                      3.0   \n", "\n", "    pt_hours_sbc_normal  pt_hours_non_sbc_heavy  \n", "57                  3.0                     3.0  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["pt.query(\"backend_merchant_id == 29107\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["assortment_pt = pd.merge(assortment_mrp, pt, on=['backend_merchant_id'], how='left')"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["feeder_orders_query = \"\"\"\n", "SELECT backend_outlet,\n", "       frontend_outlet,\n", "       item_id,\n", "       sum(order_allocated) AS feeder_orders\n", "FROM metrics.feeder_excess_orders\n", "WHERE run_id not ILIKE '%%manual%%'\n", "GROUP BY 1,2,3\n", "\n", "\"\"\"\n", "feeder_orders = pd.read_sql_query(sql=feeder_orders_query,con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["assortment_pt = pd.merge(assortment_pt,\n", "         feeder_orders,\n", "         on=['backend_outlet','frontend_outlet','item_id'],\n", "         how='left')"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["assortment_pt['etl_ts_utc'] = datetime.datetime.now()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["schema = [{'name': 'item_id', 'type': 'int'},\n", " {'name': 'item_name', 'type': 'varchar(1000)'},\n", " {'name': 'backend_outlet', 'type': 'int'},\n", " {'name': 'tag_value', 'type': 'int'},\n", " {'name': 'frontend_facility', 'type': 'int'},\n", " {'name': 'frontend_outlet', 'type': 'int'},\n", " {'name': 'frontend_outlet_name', 'type': 'varchar(1000)'},\n", " {'name': 'master_assortment_substate_id', 'type': 'int'},\n", " {'name': 'inventory_holding_frontend_outlet', 'type': 'float'},\n", " {'name': 'backend_hot_outlet', 'type': 'int'},\n", " {'name': 'available_quantity_frontend', 'type': 'float'},\n", " {'name': 'blocked_quantity_frontend', 'type': 'float'},\n", " {'name': 'total_quantity_frontend', 'type': 'float'},\n", " {'name': 'available_quantity_backend', 'type': 'float'},\n", " {'name': 'blocked_quantity_backend', 'type': 'float'},\n", " {'name': 'total_quantity_backend', 'type': 'float'},\n", " {'name': 'intransit_quantity', 'type': 'float'},\n", " {'name': 'feeder_enabled', 'type': 'float'},\n", " {'name': 'until_t', 'type': 'float'},\n", " {'name': 'until_t_plus_one', 'type': 'float'},\n", " {'name': 'until_t_plus_two', 'type': 'float'},\n", " {'name': 'until_t_plus_three', 'type': 'float'},\n", " {'name': 'until_t_plus_four', 'type': 'float'},\n", " {'name': 'until_t_plus_five', 'type': 'float'},\n", " {'name': 'buffer_quantity', 'type': 'float'},\n", " {'name': 'app_live', 'type': 'int'},\n", " {'name': 'landing_price', 'type': 'float'},\n", " {'name': 'weighted_landing_price_avg', 'type': 'float'},\n", " {'name': 'backend_merchant_id', 'type': 'int'},\n", " {'name': 'variant_mrp', 'type': 'float'},\n", " {'name': 'pt_hours_sbc_heavy', 'type': 'float'},\n", " {'name': 'pt_hours_non_sbc_normal', 'type': 'float'},\n", " {'name': 'pt_hours_sbc_normal', 'type': 'float'},\n", " {'name': 'pt_hours_non_sbc_heavy', 'type': 'float'},\n", " {'name': 'feeder_orders', 'type': 'float'},\n", " {'name': 'etl_ts_utc', 'type': 'timestamp'}]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"ename": "Exception", "evalue": "\nuserid : 142 \nslice : 31 \ntbl : 41359086 \nstarttime : 2021-01-15 12:08:38.680578 \nsession : 113635 \nquery : 129761543 \nfilename : s3://prod-dse-redshift-adhoc/decore/tmp/to_redshift/metrics/feeder_metrics/tmp6ds443t3.csv \nline_number : 2 \ncolname : app_live \ntype : int4 \ncol_length : 0 \nposition : 242 \nraw_line : \"10000005\",\"Nivea Men Oil Control 10x Whitening Face Wash\",\"340\",\"436\",\"135\",\"845\",\"Super Store Gurgaon 32 Milestone ES3 (HOT)\",\"1\",\"846.0\",\"436\",\"4.0\",\"0.0\",\"4.0\",\"144.0\",\"2.0\",\"142.0\",\"0.0\",\"1.0\",\"0.0\",\"0.0\",\"0.0\",\"0.0\",\"0.0\",\"0.0\",\"3.0\",\"1.0\",\"165.165\",\"162.145106407767\",\"29107.0\",\"175.0\",\"3.0\",\"3.0\",\"3.0\",\"3.0\",\"\",\"2021-01-15 12:08:02.757345\" \nraw_field_value : 1.0 \nerr_code : 1207 \nerr_reason : Invalid digit, Value '.', Pos 1, Type: Integer \n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mInternalError_\u001b[0m                            Traceback (most recent call last)", "\u001b[0;32m/opt/conda/lib/python3.7/site-packages/decore/io/redshift.py\u001b[0m in \u001b[0;36mexecute\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    105\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mreconcile_schemas\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcolumn_dtypes\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpg_hook\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 106\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcopy_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcolumn_dtypes\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpg_hook\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    107\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/conda/lib/python3.7/site-packages/decore/io/redshift.py\u001b[0m in \u001b[0;36mcopy_data\u001b[0;34m(self, schema, pg_hook)\u001b[0m\n\u001b[1;32m    336\u001b[0m             \u001b[0mrebuild_sql\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mdrop_sql\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mcreate_if_not_exists_sql\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mload_sql\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 337\u001b[0;31m             \u001b[0mpg_hook\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mrun\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mrebuild_sql\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    338\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/conda/lib/python3.7/site-packages/decore/hooks.py\u001b[0m in \u001b[0;36mrun\u001b[0;34m(self, sql, autocommit, parameters)\u001b[0m\n\u001b[1;32m    114\u001b[0m                     \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 115\u001b[0;31m                         \u001b[0mcur\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexecute\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0ms\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    116\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mInternalError_\u001b[0m: Load into table 'feeder_metrics' failed.  Check 'stl_load_errors' system table for details.\n", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mException\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m<ipython-input-41-f23ae3a9fd0c>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      9\u001b[0m }\n\u001b[1;32m     10\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 11\u001b[0;31m \u001b[0mpb\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mto_redshift\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0massortment_pt\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/opt/conda/lib/python3.7/site-packages/pencilbox/redshift.py\u001b[0m in \u001b[0;36mto_redshift\u001b[0;34m(data_obj, schema_name, table_name, *args, **kwargs)\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      8\u001b[0m     \u001b[0;31m# Use decore's to_redshift function to upload data to redshift\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 9\u001b[0;31m     \u001b[0mdecore_to_redshift\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mschema_name\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtable_name\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     10\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     11\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0;34m\"table_description\"\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;34m\"column_dtypes\"\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/conda/lib/python3.7/site-packages/decore/io/redshift.py\u001b[0m in \u001b[0;36mto_redshift\u001b[0;34m(data_obj, schema_name, table_name, copy_params, schema_location, column_dtypes, primary_key, sortkey, incremental_key, s3_access_iam_role, s3_copy_bucket, load_type, distkey, *args, **kwargs)\u001b[0m\n\u001b[1;32m    536\u001b[0m         \u001b[0mloader\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexecute\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    537\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 538\u001b[0;31m         \u001b[0;32mraise\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    539\u001b[0m     \u001b[0;32mfinally\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    540\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mis_dataframe\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata_obj\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/conda/lib/python3.7/site-packages/decore/io/redshift.py\u001b[0m in \u001b[0;36mto_redshift\u001b[0;34m(data_obj, schema_name, table_name, copy_params, schema_location, column_dtypes, primary_key, sortkey, incremental_key, s3_access_iam_role, s3_copy_bucket, load_type, distkey, *args, **kwargs)\u001b[0m\n\u001b[1;32m    534\u001b[0m             \u001b[0mdistkey\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdistkey\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    535\u001b[0m         )\n\u001b[0;32m--> 536\u001b[0;31m         \u001b[0mloader\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexecute\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    537\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    538\u001b[0m         \u001b[0;32mraise\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/conda/lib/python3.7/site-packages/decore/io/redshift.py\u001b[0m in \u001b[0;36mexecute\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    107\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    108\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0;34m\"stl_load_errors\"\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0me\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 109\u001b[0;31m                 \u001b[0;32mraise\u001b[0m \u001b[0mException\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_stl_load_error\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpg_hook\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    110\u001b[0m             \u001b[0;32mraise\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    111\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mException\u001b[0m: \nuserid : 142 \nslice : 31 \ntbl : 41359086 \nstarttime : 2021-01-15 12:08:38.680578 \nsession : 113635 \nquery : 129761543 \nfilename : s3://prod-dse-redshift-adhoc/decore/tmp/to_redshift/metrics/feeder_metrics/tmp6ds443t3.csv \nline_number : 2 \ncolname : app_live \ntype : int4 \ncol_length : 0 \nposition : 242 \nraw_line : \"10000005\",\"Nivea Men Oil Control 10x Whitening Face Wash\",\"340\",\"436\",\"135\",\"845\",\"Super Store Gurgaon 32 Milestone ES3 (HOT)\",\"1\",\"846.0\",\"436\",\"4.0\",\"0.0\",\"4.0\",\"144.0\",\"2.0\",\"142.0\",\"0.0\",\"1.0\",\"0.0\",\"0.0\",\"0.0\",\"0.0\",\"0.0\",\"0.0\",\"3.0\",\"1.0\",\"165.165\",\"162.145106407767\",\"29107.0\",\"175.0\",\"3.0\",\"3.0\",\"3.0\",\"3.0\",\"\",\"2021-01-15 12:08:02.757345\" \nraw_field_value : 1.0 \nerr_code : 1207 \nerr_reason : Invalid digit, Value '.', Pos 1, Type: Integer \n"]}], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"feeder_metrics\",\n", "    \"column_dtypes\":schema,\n", "    \"primary_key\": ['etl_ts_utc','item_id','backend_outlet','frontend_outlet'],\n", "    \"sortkey\": ['etl_ts_utc','item_id','backend_outlet','frontend_outlet'],\n", "    \"incremental_key\": 'etl_ts_utc',\n", "    \"load_type\": 'rebuild' # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(assortment_pt, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}