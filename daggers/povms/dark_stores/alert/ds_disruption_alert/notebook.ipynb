{"cells": [{"cell_type": "code", "execution_count": null, "id": "a559fde9-55e8-4035-811c-509da042b31f", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "from tqdm.notebook import tqdm\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "b0a84822-f645-4d42-80c2-d12fe2f0afad", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "f404206a-9380-4116-9e8e-ab46e28dcaaa", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            send_slack_alert(\n", "                f\"`supply_etls.ds_disruption_index` Upload failed due to some error \\n{e}\"\n", "            )\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "eab3e7d9-2f8c-48b3-a8cc-885e82855b60", "metadata": {}, "outputs": [], "source": ["def send_slack_alert(error_message):\n", "\n", "    slack_channel = pb.from_sheets(\n", "        \"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\"\n", "    )\n", "    slack_channel = (\n", "        slack_channel[slack_channel[\"alert\"] == \"alerting_anuvrat\"]\n", "        .reset_index()\n", "        .iloc[:, 2][0]\n", "    )\n", "\n", "    error_message = error_message + \"\\ncc <@U03RJ5FRXFC>\"\n", "\n", "    pb.send_slack_message(channel=slack_channel, text=error_message)"]}, {"cell_type": "code", "execution_count": null, "id": "693c34b7-fcf9-4d0f-9e1f-dd0395ad264b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bb86f9eb-a202-403b-91c3-abbb2e45f039", "metadata": {"tags": []}, "outputs": [], "source": ["ds_disruption = read_sql_query(\n", "    f\"\"\"\n", "                    with be_fo as (SELECT pfom.facility_id AS fc_id,\n", "                              pf.internal_facility_identifier AS fc_name,\n", "                              pfom.outlet_id AS tag_value,\n", "                              wom.cloud_store_id AS outlet_id\n", "                      FROM lake_po.physical_facility_outlet_mapping pfom\n", "                      INNER JOIN lake_retail.warehouse_outlet_mapping wom ON wom.warehouse_id = pfom.outlet_id\n", "                      INNER JOIN lake_po.physical_facility pf ON pf.facility_id = pfom.facility_id\n", "                      WHERE pfom.active = 1 and pfom.ars_active = 1\n", "                      and pf.facility_id in (1,43,92,264,555,603,1206,1320,1546,1574,1575,1872,1873,1876,1964,1983,2006,2010,2015,2076,2078,2082,2123,2141)\n", "                    ),\n", "\n", "                    fo as (\n", "                        select x.facility_id, x.outlet_id, co.name as outlet_name, cl.name as city,\n", "                                REGEXP_REPLACE(lower(co.name), 'ss|upncr|noida|ghaziabad|bengaluru|super store|mumbai|kolkata|delhi|gurgaon|hyderabad|chennai|japur|bhopal|indore|ahmedabad|pune|lucknow|kanpur|upncr|ghaziabad|noida|faridabad|bengaluru|', '') as ds_name\n", "                        from po.physical_facility_outlet_mapping x\n", "                        inner join retail.console_outlet co on co.id = x.outlet_id and co.active = 1\n", "                        inner join retail.console_location cl on cl.id = co.tax_location_id\n", "                        where x.lake_active_record and co.lake_active_record and cl.lake_active_record\n", "                        and x.ars_active = 1 and x.active = 1 \n", "                        group by 1,2,3,4\n", "                    ),\n", "\n", "                    id as (\n", "                        select * from  supply_etls.item_details id\n", "                        where assortment_type in ('Packaged Goods') and handling_type_id not in (8)\n", "                    ),\n", "\n", "                    sd as (\n", "                        select * from ims.ims_sto_details\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    isi as (\n", "                        select * from ims.ims_sto_item\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    psi as (\n", "                        select * from po.sto_items\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    ps as (\n", "                        select * from po.sto\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    au as (\n", "                        select id as user_id, email as user_mail\n", "                        from retail.auth_user\n", "                        where lake_active_record\n", "                    ),\n", "\n", "                    core_sto as (\n", "                        select isi.created_at + interval '330' minute as sto_creation_time,\n", "                                sd.delivery_date + interval '330' minute as sto_expiry_time,\n", "                                isi.sto_id, \n", "                                case when sd.sto_state=1 then 'CREATED' when sd.sto_state=2 then 'BILLED' when sd.sto_state=3 then 'EXPIRED' when sd.sto_state=4 then 'INWARD' when sd.sto_state=5 then 'FORCE_BILLED' end as sto_state,\n", "                                sd.outlet_id as sender_outlet_id, fc_name,\n", "                                sd.merchant_outlet_id as receiver_outlet_id, ds_name,\n", "                                isi.item_id, id.assortment_type,\n", "                                cast(JSON_EXTRACT(JSON_EXTRACT(ps.meta,'$.destination_entity_vendor'),'$.name') as varchar) as destnation_entity,\n", "                                psi.created_by, au.user_mail,\n", "                                sum(isi.reserved_quantity) as sto_quantity,\n", "                                sum(isi.billed_quantity) as billed_quanity,\n", "                                sum(isi.inward_quantity) as inward\n", "                        from sd\n", "                        left join isi on isi.sto_id = sd.sto_id\n", "                        left join psi on psi.sto_id = isi.sto_id and psi.item_id = isi.item_id\n", "                        left join ps on ps.id = isi.sto_id\n", "                        left join fo on fo.outlet_id = sd.merchant_outlet_id\n", "                        inner join id on id.item_id = isi.item_id\n", "                        inner join au on au.user_id = psi.created_by\n", "                        inner join be_fo on be_fo.outlet_id = sd.outlet_id\n", "                        group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "                    ),\n", "\n", "\n", "                    pi as (\n", "                        SELECT *\n", "                       FROM pos.pos_invoice\n", "                       WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                         AND invoice_type_id IN (5,14,16)\n", "                         AND grofers_order_id != ''\n", "                         AND grofers_order_id IS NOT NULL\n", "                    ),\n", "\n", "                    pid as (\n", "                        SELECT *\n", "                        FROM pos.pos_invoice_product_details\n", "                        WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                    ),\n", "\n", "                    pp as (\n", "                        select variant_id, item_id from rpc.product_product where lake_active_record and active = 1\n", "                    ),\n", "\n", "\n", "                    iii as (\n", "                        SELECT *\n", "                        FROM ims.ims_inward_invoice\n", "                        WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                        AND source_type = 2\n", "                    ),\n", "\n", "\n", "                    isd as (\n", "                        SELECT *\n", "                        FROM ims.ims_inventory_stock_details\n", "                        WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                    ),\n", "\n", "                    grn_invoice as (\n", "                        select iii.grn_id, vendor_invoice_id, item_id, max(isd.created_at) as isd_created_at,\n", "                                sum(coalesce(isd.delta,0)) AS grn1, sum(coalesce(isd.delta,0) * isd.landing_price) AS value1\n", "                        from iii\n", "                        inner join isd on iii.grn_id = isd.grn_id\n", "                        inner join pp on pp.variant_id = isd.variant_id\n", "                        group by 1,2,3\n", "                    ),\n", "\n", "                    gi_core as (\n", "                        select vendor_invoice_id as invoice_id, item_id, max(isd_created_at + interval '330' minute) as grn_started_at,\n", "                                sum(grn1) AS inwarded_quantity, sum(value1) AS inward_value\n", "                        from grn_invoice\n", "                        group by 1,2\n", "                    ),\n", "\n", "                    core_grn as (\n", "                        select cast(pi.grofers_order_id as int) as sto_id, pi.invoice_id,\n", "                                pi.pos_timestamp + interval '330' minute as sto_invoice_created_at,\n", "                                case when cast(transfer_state as integer) = 1 then 'Raised'\n", "                                    when cast(transfer_state as integer) = 2 then 'GRN Complete'\n", "                                    when cast(transfer_state as integer) = 3 then 'GRN Partial'\n", "                                    when cast(transfer_state as integer) = 4 then 'GRN Force Complete'\n", "                                    when cast(transfer_state as integer) = 5 then 'DISCREPANCY_NOTE_GENERATED'\n", "                                    else 'NA'\n", "                                end as invoice_state,\n", "                                pp.item_id, \n", "\n", "                                sum(coalesce(pid.quantity,0)) AS billed_quantity,\n", "                                sum(pid.selling_price*coalesce(pid.quantity,0)) AS invoice_value,\n", "                                grn_started_at,\n", "                                inwarded_quantity,\n", "                                inward_value\n", "\n", "                        from pi\n", "                        inner join pid on pid.invoice_id = pi.id\n", "                        inner join pp on pp.variant_id = pid.variant_id\n", "                        left join gi_core gc on gc.invoice_id = pi.invoice_id AND gc.item_id = pp.item_id\n", "                        inner join id on id.item_id = pp.item_id\n", "                        group by 1,2,3,4,5,8,9,10\n", "                    ),\n", "\n", "                    disruption as (\n", "                        select \n", "                                cs.sender_outlet_id, fc_name, cs.receiver_outlet_id, ds_name,\n", "                                sum(cs.sto_quantity) as sto_quantity, \n", "                                sum(cs.billed_quanity) as billed_quanity, sum(cg.billed_quantity) as core_billed_qty, \n", "                                sum(cs.inward) as inwarded_quantity, sum(cg.inwarded_quantity) as core_inwarded_quantity,\n", "                                sum(cg.billed_quantity) - sum(cg.inwarded_quantity) as delta\n", "\n", "                        from core_sto cs\n", "                        left join core_grn cg on cg.sto_id = cs.sto_id and cg.item_id = cs.item_id\n", "                        where cg.sto_invoice_created_at <= current_timestamp - interval '36' hour\n", "                        and cg.sto_invoice_created_at >= current_timestamp - interval '90' hour\n", "                        group by 1,2,3,4\n", "                    ),\n", "\n", "                    avg_trend as (\n", "                        select date(sto_creation_time) as sto_created_date, cs.sender_outlet_id, cs.receiver_outlet_id, sum(sto_quantity) as trend_sto\n", "                        from core_sto cs\n", "                        where cs.sto_creation_time <= current_timestamp - interval '48' hour\n", "                        group by 1,2,3\n", "                    ),\n", "\n", "                    trend as (\n", "                        select sender_outlet_id, receiver_outlet_id, avg(trend_sto) as avg_sto\n", "                        from avg_trend\n", "                        group by 1,2\n", "                    )\n", "\n", "\n", "                    select at.sender_outlet_id, at.receiver_outlet_id,  \n", "                            avg_sto, delta, core_billed_qty as billed_qty, core_inwarded_quantity as inwarded_quantity,\n", "                            (case when delta > avg_sto then 100.000 else delta * 100.000 / avg_sto end) as disruption_perc\n", "                    from trend at\n", "                    left join disruption d on d.sender_outlet_id = at.sender_outlet_id and d.receiver_outlet_id = at.receiver_outlet_id\n", "                    where d.delta is not null\n", "                \"\"\",\n", "    CON_TRINO,\n", ")\n", "\n", "\n", "ds_disruption"]}, {"cell_type": "code", "execution_count": null, "id": "a12d4963-6bab-426f-b745-bb456b5c5de5", "metadata": {}, "outputs": [], "source": ["ds_details = read_sql_query(\n", "    f\"\"\" select id as outlet_id, name as outlet_name from retail.console_outlet where active = 1 and lake_active_record \"\"\",\n", "    CON_TRINO,\n", ")\n", "ds_details"]}, {"cell_type": "code", "execution_count": null, "id": "e8e944f5-c5e0-40cc-8c01-484b52353db4", "metadata": {}, "outputs": [], "source": ["str(date.today() + <PERSON><PERSON><PERSON>(days=1))"]}, {"cell_type": "code", "execution_count": null, "id": "9c28813e-2d0d-4ad1-a038-b7cc5c0b1a51", "metadata": {}, "outputs": [], "source": ["cart_projection = pb.from_sheets(\n", "    \"1XPePLZn_rfA3cIXy6d3GijMZDpZhd_r8q7Hou-ungR8\",\n", "    \"Order Projection\",\n", "    service_account=\"service_account\",\n", ")\n", "cart_projection = pd.DataFrame(\n", "    cart_projection.values[5:], columns=cart_projection.iloc[1, :]\n", ")\n", "\n", "cart_projection = cart_projection.melt(id_vars=[\"City\", \"Outlet ID\", \"Store Name\"])\n", "cart_projection.columns = [\"city\", \"outlet_id\", \"outlet_name\", \"date_\", \"cart_proj\"]\n", "cart_projection[\"date_\"] = pd.to_datetime(cart_projection[\"date_\"])\n", "cart_projection[\"cart_proj\"] = (\n", "    cart_projection[\"cart_proj\"].replace(\"\", np.nan).replace(np.nan, 0)\n", ")\n", "cart_projection[\"cart_proj\"] = cart_projection[\"cart_proj\"].astype(int)\n", "cart_projection[\"outlet_id\"] = cart_projection[\"outlet_id\"].astype(int)\n", "cart_projection = cart_projection[\n", "    cart_projection[\"date_\"] == str(date.today() + timedelta(days=1))\n", "]\n", "cart_projection"]}, {"cell_type": "code", "execution_count": null, "id": "3defe5bf-0cb4-48b5-b929-adda1d295dd9", "metadata": {}, "outputs": [], "source": ["cart_projection[cart_projection[\"outlet_id\"] == 4113]"]}, {"cell_type": "code", "execution_count": null, "id": "02bfe1a4-3bca-4edf-b9ea-f0396405b44e", "metadata": {}, "outputs": [], "source": ["ds_disruption_new = ds_disruption[ds_disruption[\"disruption_perc\"] > 30].reset_index(\n", "    drop=True\n", ")\n", "ds_disruption_new[\"disruption_perc\"] = np.where(\n", "    ds_disruption_new[\"disruption_perc\"] > 50, 100, ds_disruption_new[\"disruption_perc\"]\n", ")\n", "ds_disruption_new[\"disruption_perc\"] = np.round(ds_disruption_new[\"disruption_perc\"], 0)\n", "ds_disruption_new = ds_disruption_new.sort_values(\n", "    by=[\"disruption_perc\"], ascending=False\n", ").reset_index(drop=True)\n", "\n", "ds_disruption_new = pd.merge(\n", "    ds_disruption_new.rename(columns={\"receiver_outlet_id\": \"outlet_id\"}),\n", "    ds_details,\n", "    how=\"left\",\n", ")\n", "ds_disruption_new = pd.merge(\n", "    ds_disruption_new, cart_projection[[\"outlet_id\", \"cart_proj\"]], how=\"left\"\n", ")\n", "ds_disruption_new[\"disruption_perc\"] = (\n", "    np.round(ds_disruption_new[\"disruption_perc\"] / 10) * 10\n", ")\n", "ds_disruption_new[\"lost_proj\"] = (\n", "    ds_disruption_new[\"cart_proj\"] * ds_disruption_new[\"disruption_perc\"] * 0.01\n", ").astype(int)\n", "ds_disruption_new[\"disruption_perc\"] = (\n", "    ds_disruption_new[\"disruption_perc\"].astype(str) + \"%\"\n", ")\n", "ds_disruption_new = ds_disruption_new[\n", "    [\"outlet_id\", \"outlet_name\", \"disruption_perc\", \"lost_proj\"]\n", "]\n", "ds_disruption_new.columns = [\"Outlet ID\", \"Outlet Name\", \"Disruption %\", \"Lost Carts\"]\n", "ds_disruption_new"]}, {"cell_type": "code", "execution_count": null, "id": "5b484c2e-43cb-4ff2-af24-c50b7f46f51f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "11adabca-156b-4a71-be76-a6c39c00839a", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5.0,\n", "    row_height=0.625,\n", "    font_size=20,\n", "    header_color=\"#E96125\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    **kwargs\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "            [col_width, row_height]\n", "        )\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs\n", "    )\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "            cell.set_facecolor(header_color)\n", "        #         elif k[0] == data.shape[0]:\n", "        #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "        # cell.set_facecolor('#f1f1f2')\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "fig, ax = render_mpl_table(ds_disruption_new, header_columns=0)\n", "fig.savefig(\"ds_disruption.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "d0db13db-cc77-419e-a392-410db0692b67", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6b450288-7ee2-4f8c-bf4f-e8f7057282a1", "metadata": {}, "outputs": [], "source": ["text_req = \"<!channel> For tomorrow following store's packaged indent will be tentatively reduced by the given disrupted % if situation doesn't improve by EOD\"\n", "pb.send_slack_message(\n", "    channel=\"blinkit-replenishment-team\", text=text_req, files=[\"./ds_disruption.png\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8912e787-1853-40db-847d-6e72dcf07db6", "metadata": {}, "outputs": [], "source": ["# blinkit-replenishment-team"]}, {"cell_type": "code", "execution_count": null, "id": "89a1db8f-709c-47df-8e21-95c19fba2f76", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}