{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON> Tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "\n", "final_date = \"2021-01-26\"\n", "current_date = (datetime.now() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d\")\n", "current_date, final_date\n", "\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "aur = pb.get_connection(\"aurora\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from gspread.exceptions import APIError\n", "\n", "_retry_for = [429, 500, 503]\n", "\n", "\n", "def from_sheets(sheet_id, sheet_name, service_account, max_tries=10):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(20)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account, max_tries=10):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(20)\n", "\n", "\n", "pb.from_sheets_patch = from_sheets\n", "pb.to_sheets_patch = to_sheets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as facility_id,name as facility_name from crates.facility\"\"\"\n", "facility = pd.read_sql_query(query, retail)\n", "facility.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inaam items from category"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# https://docs.google.com/spreadsheets/d/1KGvh-lYG2R7J7MibiiZ86DeVF0lIAGLuGDlBxcwOQ_I/edit?ts=5fffdb1f"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1KGvh-lYG2R7J7MibiiZ86DeVF0lIAGLuGDlBxcwOQ_I\"\n", "sheet_name = \"Offer id-from content\"\n", "\n", "inaams_new = pb.from_sheets_patch(sheet_id, sheet_name, service_account=\"service_account\")\n", "inaams_new_list = inaams_new[[\"item id\"]].rename(columns={\"item id\": \"item_id\"})\n", "inaams_new_list[\"key\"] = 1\n", "inaams_new_list[\"item_id\"] = inaams_new_list[\"item_id\"].astype(\"int\")\n", "inaams_new_list.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaams_new_list.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inaam items inclusion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_inclusion = \"1YYBvyql8PeIfzGuekQtfbIhEfWEZasu0RR15o7Zgzqo\"\n", "inclusion = pb.from_sheets_patch(\n", "    sheet_inclusion, \"items_inclusion\", service_account=\"service_account\"\n", ")\n", "inclusion[\"item_id\"] = inclusion[\"item_id\"].astype(\"int\")\n", "\n", "inclusion[\"key\"] = 1\n", "inclusion.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaams_new_list = pd.concat([inaams_new_list, inclusion])\n", "inaams_new_list.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaams_new_list.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inaam exclusions items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# https://docs.google.com/spreadsheets/d/1YYBvyql8PeIfzGuekQtfbIhEfWEZasu0RR15o7Zgzqo/edit#gid=**********"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_exclusion = \"1YYBvyql8PeIfzGuekQtfbIhEfWEZasu0RR15o7Zgzqo\"\n", "exclusion = pb.from_sheets_patch(\n", "    sheet_exclusion, \"item_exclusion\", service_account=\"service_account\"\n", ")\n", "exclusion[\"item_id\"] = exclusion[\"item_id\"].astype(\"int\")\n", "exclusion_list = list(exclusion[\"item_id\"])\n", "exclusion_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_list_inaam = inaams_new_list[~inaams_new_list[\"item_id\"].isin(exclusion_list)]\n", "base_list_inaam.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_list_inaam.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inaam facility list and item qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaam_required_qty = pb.from_sheets_patch(\n", "    sheet_exclusion, \"facility_wise_inaam_qty\", service_account=\"service_account\"\n", ")\n", "inaam_required_qty = inaam_required_qty[[\"facility_name\", \"item_id\", \"qty\"]].drop_duplicates()\n", "inaam_required_qty = inaam_required_qty[inaam_required_qty[\"facility_name\"] != \"\"]\n", "inaam_required_qty.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Quantity multiplier"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"   with target_data as (\n", "select '2021-01-15 00:00:00' as date_, *********::int as gmv_target union\n", "select '2021-01-16 00:00:00' as date_, *********::int as gmv_target union\n", "select '2021-01-17 00:00:00' as date_, *********::int as gmv_target union\n", "select '2021-01-18 00:00:00' as date_, *********::int as gmv_target union\n", "select '2021-01-19 00:00:00' as date_, *********::int as gmv_target union\n", "select '2021-01-20 00:00:00' as date_, *********::int as gmv_target union\n", "select '2021-01-21 00:00:00' as date_, *********::int as gmv_target union\n", "select '2021-01-22 00:00:00' as date_, 119057024::int as gmv_target union\n", "select '2021-01-23 00:00:00' as date_, 180895974::int as gmv_target union\n", "select '2021-01-24 00:00:00' as date_, 173785582::int as gmv_target union\n", "select '2021-01-25 00:00:00' as date_, 116300749::int as gmv_target union\n", "select '2021-01-26 00:00:00' as date_, 224254939::int as gmv_target \n", "\n", "\n", "),\n", "\n", "\n", "cumu_targets as (\n", "select\n", "t1.date_::date as date_,\n", "t1.gmv_target as gmv_target,\n", "sum(t2.gmv_target) as cum_gmv_target\n", "from target_data t1\n", "left join target_data t2 on date(t2.date_) <= date(t1.date_)\n", "group by 1,2\n", "),\n", "\n", "daily_gmv as (\n", "select\n", "date_ist(o.install_ts) as cart_checkout_date,\n", "sum(total_cost) as actual_gmv\n", "from bifrost.oms_order o\n", "left join bifrost.oms_merchant om on o.merchant_id = om.id\n", "where o.install_ts >= '2020-01-01 00:00:00'\n", "-- '2019-12-30 00:00:00'\n", "and date_ist(o.install_ts) between '2021-01-15' and '2021-01-26'\n", "and om.city_name not in ('Not in service area','Hapur')\n", "and om.city_name not ilike '%%B2B%%'\n", "and (o.\"type\" is null or o.\"type\" in ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder'))\n", "group by 1\n", "),\n", "\n", "\n", "cumu_actual_gmv as (\n", "with base_ as (\n", "select\n", "t1.cart_checkout_date,\n", "t1.actual_gmv,\n", "sum(t2.actual_gmv) as cum_actual_gmv\n", "from daily_gmv t1\n", "left join daily_gmv t2 on t2.cart_checkout_date <= t1.cart_checkout_date-1\n", "group by 1,2\n", ")\n", "select\n", "cart_checkout_date as date_,\n", "(actual_gmv + coalesce(cum_actual_gmv,0)) as cum_actual_gmv\n", "from base_\n", ")\n", "\n", "\n", "select\n", "t.date_,\n", "round(t.cum_gmv_target::numeric / (10^7)::numeric,2) as cum_gmv_target,\n", "round(s.cum_actual_gmv::numeric / (10^7)::numeric,2) as cum_actual_gmv\n", "from cumu_targets t\n", "left join cumu_actual_gmv s on t.date_ = s.date_\n", "order by 1\"\"\"\n", "\n", "ratio = pd.read_sql(query, aur)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ratio[\"date_\"] = ratio[\"date_\"].astype(\"str\")\n", "ratio.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = ratio[ratio[\"date_\"] == current_date].iloc[0, 1]\n", "y = ratio[ratio[\"date_\"] == final_date].iloc[0, 1]\n", "\n", "multiplier = (y - x) / y\n", "multiplier"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaam_required_qty[\"multiplier\"] = multiplier\n", "inaam_required_qty[\"multiplier\"] = inaam_required_qty[\"multiplier\"].astype(\"float\")\n", "inaam_required_qty[\"qty\"] = inaam_required_qty[\"qty\"].astype(\"int\")\n", "inaam_required_qty[\"Required_qty\"] = np.ceil(\n", "    inaam_required_qty[\"qty\"] * inaam_required_qty[\"multiplier\"]\n", ")\n", "inaam_required_qty.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaam_required_qty.groupby([\"facility_name\"])[[\"item_id\"]].nunique().reset_index().head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_unique = inaam_required_qty[[\"facility_name\"]].drop_duplicates()\n", "facility_unique = facility_unique.merge(facility, on=[\"facility_name\"], how=\"left\")\n", "facility_unique[\"key\"] = 1\n", "facility_unique.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inaam facility items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list = facility_unique.merge(base_list_inaam, on=[\"key\"], how=\"left\")\n", "items_list = items_list[[\"facility_id\", \"facility_name\", \"item_id\"]].drop_duplicates()\n", "inaam_required_qty[\"item_id\"] = inaam_required_qty[\"item_id\"].astype(\"int\")\n", "items_list = items_list.merge(inaam_required_qty, on=[\"facility_name\", \"item_id\"], how=\"left\")\n", "items_list[\"qty\"] = items_list[\"qty\"].fillna(0)\n", "items_list[\"Required_qty\"] = items_list[\"Required_qty\"].fillna(0)\n", "items_list[\"multiplier\"] = items_list[\"multiplier\"].fillna(items_list[\"multiplier\"].median())\n", "items_list.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list.groupby([\"facility_name\"])[[\"item_id\"]].nunique().reset_index().head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c2_items = [********, ********, ********]\n", "chennai_inclusion = pb.from_sheets_patch(\n", "    sheet_exclusion, \"chennai_inaam\", service_account=\"service_account\"\n", ")\n", "chennai_inclusion_list = list(chennai_inclusion[\"item_id\"].astype(\"int\"))\n", "c2_items_final = c2_items + chennai_inclusion_list\n", "c2_items_final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def c2_inaam(x):\n", "    if x[\"facility_name\"] == \"Super Store Chennai C2 - Warehouse\":\n", "        if x[\"item_id\"] in (c2_items_final):\n", "            return 1\n", "        else:\n", "            return 0\n", "    else:\n", "        return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list[\"flag\"] = items_list.apply(c2_inaam, axis=1)\n", "items_list = items_list[items_list[\"flag\"] == 1]\n", "items_list.drop([\"flag\"], axis=1, inplace=True)\n", "items_list.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list.groupby([\"facility_name\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list[\"item_id\"] = items_list[\"item_id\"].astype(\"str\")\n", "item_id_list = set(filter(None, items_list.item_id.values))\n", "item_id_list = \", \".join(item_id_list)\n", "\n", "items_list.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Inaam items Franchise stores"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Facility serivng FS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT case when ro.facility_id in (202,194,232,233,242) then 15\n", "                else r.facility_id end as facility_id,\n", "                case when ro.facility_id in (202,194,232,233,242) then 'Super Store Jaipur - Warehouse'\n", "                else fc.name end as facility_name,\n", "                ro.facility_id AS fs_facility,\n", "                f.name AS fs_facility_name\n", "FROM rpc.item_outlet_tag_mapping AS t\n", "INNER JOIN retail.console_outlet ro ON ro.id=t.outlet_id\n", "INNER JOIN retail.console_outlet r ON r.id=t.tag_value\n", "INNER JOIN crates.facility f ON f.id=ro.facility_id\n", "INNER JOIN crates.facility fc ON fc.id=r.facility_id\n", "WHERE ( f.name like '%%dark%%' and f.name not like '%%es%%' )\n", "and fc.name not like '%%Bulk Lucknow 3SC - Warehouse%%'\n", "and fc.name not like '%%SSC Bulk Mumbai M2 - Warehouse%%'\n", "and ro.name not like \"%%old%%\"\n", "and t.tag_type_id=8\n", "and t.active=1\n", "order by 1\"\"\"\n", "\n", "mapping_fs = pd.read_sql_query(\n", "    query,\n", "    retail,\n", ")\n", "\n", "mapping_fs.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["franchise_inaam_items = mapping_fs.merge(items_list, on=[\"facility_name\"], how=\"left\")\n", "franchise_inaam_items = franchise_inaam_items[\n", "    [\"fs_facility\", \"fs_facility_name\", \"item_id\", \"qty\", \"multiplier\", \"Required_qty\"]\n", "].drop_duplicates()\n", "franchise_inaam_items.rename(\n", "    columns={\"fs_facility\": \"facility_id\", \"fs_facility_name\": \"facility_name\"},\n", "    inplace=True,\n", ")\n", "\n", "\n", "fs_list = [\n", "    15,\n", "    232,\n", "    178,\n", "    136,\n", "    137,\n", "    202,\n", "    263,\n", "    272,\n", "    259,\n", "    262,\n", "    242,\n", "    281,\n", "    225,\n", "    152,\n", "    177,\n", "    184,\n", "    275,\n", "    214,\n", "    241,\n", "    179,\n", "    240,\n", "]\n", "\n", "franchise_inaam_items = franchise_inaam_items[franchise_inaam_items[\"facility_id\"].isin(fs_list)]\n", "franchise_inaam_items.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Inaam items from list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaams = items_list.copy()\n", "inaams = inaams[\n", "    [\"facility_id\", \"facility_name\", \"item_id\", \"qty\", \"multiplier\", \"Required_qty\"]\n", "].drop_duplicates()\n", "final_inaam_list = pd.concat([franchise_inaam_items, inaams])\n", "final_inaam_list = final_inaam_list.drop_duplicates()\n", "final_inaam_list.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### G4 inaam items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT case when ro.facility_id in (202,194,232,233,242) then 15\n", "                else r.facility_id end as facility_id,\n", "                case when ro.facility_id in (202,194,232,233,242) then 'Super Store Jaipur - Warehouse'\n", "                else fc.name end as facility_name,\n", "                ro.facility_id AS ds_facility,\n", "                f.name AS ds_facility_name\n", "FROM rpc.item_outlet_tag_mapping AS t\n", "INNER JOIN retail.console_outlet ro ON ro.id=t.outlet_id\n", "INNER JOIN retail.console_outlet r ON r.id=t.tag_value\n", "INNER JOIN crates.facility f ON f.id=ro.facility_id\n", "INNER JOIN crates.facility fc ON fc.id=r.facility_id\n", "WHERE r.facility_id=29\n", "and ro.name not like \"%%old%%\"\n", "and ro.facility_id!=228\n", "and f.name not like '%%Super Store Gurgaon Sector 38 ES5%%'\n", "and f.name not like '%%Super Store Delhi Bamnoli EC - Warehouse%%'\n", "and f.name not like '%%Super Store Delhi Mundka EC - Warehouse%%'\n", "and f.name not like '%%Super Store Gurgaon G2 - Warehouse%%'\n", "\n", "and t.tag_type_id=8\n", "and t.active=1\n", "order by 1\"\"\"\n", "\n", "mapping_g4 = pd.read_sql_query(\n", "    query,\n", "    retail,\n", ")\n", "\n", "mapping_g4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g4_inaam = mapping_g4.merge(\n", "    items_list,\n", "    left_on=[\"ds_facility\", \"ds_facility_name\"],\n", "    right_on=[\"facility_id\", \"facility_name\"],\n", "    how=\"left\",\n", ")\n", "g4_inaam[\"qty\"] = g4_inaam[\"qty\"].fillna(100000000)\n", "g4_inaam[\"qty\"] = g4_inaam[\"qty\"].astype(\"int\")\n", "g4_inaam = (\n", "    g4_inaam.groupby([\"facility_id_x\", \"facility_name_x\", \"item_id\", \"multiplier\"])[\n", "        [\"qty\", \"Required_qty\"]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(columns={\"facility_id_x\": \"facility_id\", \"facility_name_x\": \"facility_name\"})\n", ")\n", "g4_inaam.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_inaam_list = pd.concat([final_inaam_list, g4_inaam])\n", "final_inaam_list = final_inaam_list.drop_duplicates()\n", "final_inaam_list[\"qty\"] = final_inaam_list[\"qty\"].fillna(1000000000)\n", "final_inaam_list.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_inaam__new = final_inaam_list.copy()\n", "final_inaam__new = final_inaam__new[\n", "    [\"facility_id\", \"facility_name\", \"item_id\", \"qty\", \"multiplier\", \"Required_qty\"]\n", "].drop_duplicates()\n", "final_inaam__new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_inaam__new.groupby([\"facility_id\", \"facility_name\"])[\n", "    [\"item_id\"]\n", "].nunique().reset_index().head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_inaam__new[\"item_id\"] = final_inaam__new[\"item_id\"].astype(\"str\")\n", "item_id_list = set(filter(None, final_inaam__new.item_id.values))\n", "item_id_list = \", \".join(item_id_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item product mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "item_product_mapping = pd.read_sql(item_attributes_query, redshift)\n", "item_product_mapping.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["innam_items_facility = final_inaam__new.copy()\n", "innam_items_facility = innam_items_facility[innam_items_facility[\"facility_id\"] != \"\"]\n", "innam_items_facility = innam_items_facility.dropna()\n", "innam_items_facility[\"facility_id\"] = innam_items_facility[\"facility_id\"].astype(\"int\")\n", "innam_items_facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["innam_items_facility.groupby([\"facility_name\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Actual Items inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT DISTINCT a.item_id,\n", "                a.outlet_id,\n", "                r.name,\n", "                r.facility_id,\n", "                quantity AS actual_quantity\n", "FROM ims.ims_item_inventory AS a\n", "INNER JOIN (select distinct facility_id,id ,name\n", "        from retail.console_outlet\n", "        where active=1\n", "        and ((name like '%%SSC%%' or name like '%%MODI%%'))\n", "        and name not like '%%liquidation%%'\n", "        and name not like '%%hogwarts%%'\n", "        and name not like '%%training%%'\n", "        and name not like '%%donate%%'\n", "        and name not like '%%orange%%'\n", "        and name not like '%%not in service area%%'\n", "        or (name like '%% ES%%' and (name like '%%SSC%%' or name like '%%CC%%' or name like '%%NM%%' or name like '%%FNV%%'))\n", "        or (name like '%% dark%%' and name not like '%%infra%%')\n", ") r \n", "ON a.outlet_id = r.id\n", "where item_id in %(item)s\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4\n", "         order by 4,1,2\n", "   \"\"\"\n", "actual_inventory = pd.read_sql_query(\n", "    query, retail, params={\"item\": tuple(innam_items_facility[\"item_id\"])}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Blocked inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["block_quantity = \"\"\"\n", "SELECT item_id,\n", "       outlet_id,\n", "       max(CASE\n", "               WHEN blocked_type = 1 THEN quantity\n", "               ELSE 0\n", "           END) AS order_blocked,\n", "       max(CASE\n", "               WHEN blocked_type = 2 THEN quantity\n", "               ELSE 0\n", "           END)AS sto_blocked,\n", "       max(CASE\n", "               WHEN blocked_type = 4 THEN quantity\n", "               ELSE 0\n", "           END)AS pro_blocked,\n", "       max(CASE\n", "               WHEN blocked_type = 5 THEN quantity\n", "               ELSE 0\n", "           END)AS sso_blocked\n", "FROM ims.ims_item_blocked_inventory\n", "where item_id in %(item)s\n", "GROUP BY 1,\n", "         2\n", "                     \"\"\"\n", "\n", "block_quantity_data = pd.read_sql_query(\n", "    sql=block_quantity,\n", "    con=retail,\n", "    params={\"item\": tuple(innam_items_facility[\"item_id\"])},\n", ")\n", "\n", "block_quantity_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = actual_inventory.merge(block_quantity_data, on=[\"item_id\", \"outlet_id\"], how=\"left\")\n", "\n", "inv_data = inv_data.drop_duplicates([\"item_id\", \"outlet_id\"])\n", "\n", "inv_data[\"blocked_quantity\"] = (\n", "    inv_data.order_blocked + inv_data.sto_blocked + inv_data.pro_blocked + inv_data.sso_blocked\n", ")\n", "\n", "inv_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_inventory = (\n", "    inv_data.groupby([\"item_id\", \"facility_id\"])[[\"actual_quantity\", \"blocked_quantity\"]]\n", "    .max()\n", "    .reset_index()\n", ")\n", "\n", "\n", "Total_inventory[\"actual_quantity\"] = np.where(\n", "    ((Total_inventory.actual_quantity.isna()) | (Total_inventory.actual_quantity < 0)),\n", "    0,\n", "    Total_inventory.actual_quantity,\n", ")\n", "\n", "\n", "Total_inventory[\"blocked_quantity\"] = np.where(\n", "    ((Total_inventory.blocked_quantity.isna()) | (Total_inventory.blocked_quantity < 0)),\n", "    0,\n", "    Total_inventory.blocked_quantity,\n", ")\n", "\n", "Total_inventory.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["innam_items_facility.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Net inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["innam_items_facility = innam_items_facility[innam_items_facility[\"item_id\"] != \"\"]\n", "innam_items_facility[[\"facility_id\", \"item_id\"]] = innam_items_facility[\n", "    [\"facility_id\", \"item_id\"]\n", "].astype(\"int\")\n", "\n", "Total_inventory_all = innam_items_facility.merge(\n", "    Total_inventory, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "net_inventory = Total_inventory_all[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"qty\",\n", "        \"multiplier\",\n", "        \"Required_qty\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "    ]\n", "].drop_duplicates()\n", "net_inventory[\"unblocked_quantity\"] = np.where(\n", "    net_inventory[\"actual_quantity\"] - net_inventory[\"blocked_quantity\"] > 0,\n", "    net_inventory[\"actual_quantity\"] - net_inventory[\"blocked_quantity\"],\n", "    0,\n", ")\n", "\n", "net_inventory = net_inventory.fillna(0)\n", "net_inventory.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item cpd requirement"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "SELECT DISTINCT wom.cloud_store_id AS outlet_id,\n", "                dwc.item_id,\n", "                avg(case when dated_range='cpd before GOBD' then consumption end ) as 'average cpd (till 14th jan)' ,\n", "                avg(case when dated_range='cpd during GOBD' then consumption end ) as 'average cpd (14-26 jan)' ,\n", "                avg(case when dated_range='cpd after GOBD' then consumption end ) as 'average cpd (27-31 jan)' \n", "FROM\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          date AS dated,\n", "          CASE\n", "              WHEN date<='2021-01-14' THEN 'cpd before GOBD'\n", "              WHEN date>='2021-01-15'\n", "                   AND date<='2021-01-26' THEN 'cpd during GOBD'\n", "              ELSE 'cpd after GOBD'\n", "          END AS dated_range,\n", "          consumption\n", "   FROM snorlax.date_wise_consumption\n", "   WHERE date BETWEEN CURRENT_DATE AND '2021-01-31'\n", "   and item_id in ({item_id})) dwc\n", "JOIN retail.warehouse_outlet_mapping wom ON dwc.outlet_id = wom.warehouse_id\n", "LEFT JOIN retail.console_outlet ro ON ro.id=dwc.outlet_id\n", "GROUP BY 1,\n", "         2\n", "\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "cpd1 = pd.read_sql_query(cpd_query, retail)\n", "cpd1.head(2)\n", "\n", "\n", "cpd_query = \"\"\"\n", "SELECT outlet_id,\n", "       item_id,\n", "       avg(case when dated_range='cpd before GOBD' then consumption end ) as 'average cpd (till 14th jan)',\n", "       avg(case when dated_range='cpd during GOBD' then consumption end ) as 'average cpd (14-26 jan)' ,\n", "       avg(case when dated_range='cpd after GOBD' then consumption end ) as 'average cpd (27-31 jan)' \n", "FROM\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          date AS dated,\n", "          CASE\n", "              WHEN date<='2021-01-14' THEN 'cpd before GOBD'\n", "              WHEN date>='2021-01-15'\n", "                   AND date<='2021-01-27' THEN 'cpd during GOBD'\n", "              ELSE 'cpd after GOBD'\n", "          END AS dated_range,\n", "          consumption\n", "   FROM snorlax.date_wise_consumption\n", "   WHERE date BETWEEN CURRENT_DATE AND '2021-01-31'\n", "   and item_id in ({item_id})\n", "      ) dwc\n", "GROUP BY 1,\n", "         2\n", "\n", "\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "cpd2 = pd.read_sql_query(cpd_query, retail)\n", "\n", "cpd = pd.concat([cpd1, cpd2])\n", "cpd = cpd.drop_duplicates()\n", "cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet facility mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id  as outlet_id,\n", "name as outlet_name,\n", "facility_id \n", "from retail.console_outlet\"\"\"\n", "facility = pd.read_sql_query(query, retail)\n", "facility = facility.dropna()\n", "facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_facility = cpd.merge(facility, on=[\"outlet_id\"], how=\"left\")\n", "cpd_facility = (\n", "    cpd_facility.groupby([\"facility_id\", \"item_id\"])[\n", "        [\n", "            \"average cpd (till 14th jan)\",\n", "            \"average cpd (14-26 jan)\",\n", "            \"average cpd (27-31 jan)\",\n", "        ]\n", "    ]\n", "    .max()\n", "    .reset_index()\n", ")\n", "cpd_facility.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merging cpd on inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_inventory_cpd = net_inventory.merge(cpd_facility, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "net_inventory_cpd = net_inventory_cpd.fillna(0)\n", "net_inventory_cpd[\n", "    [\n", "        \"average cpd (till 14th jan)\",\n", "        \"average cpd (14-26 jan)\",\n", "        \"average cpd (27-31 jan)\",\n", "    ]\n", "] = round(\n", "    net_inventory_cpd[\n", "        [\n", "            \"average cpd (till 14th jan)\",\n", "            \"average cpd (14-26 jan)\",\n", "            \"average cpd (27-31 jan)\",\n", "        ]\n", "    ],\n", "    2,\n", ")\n", "net_inventory_cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Active items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT facility_id as facility_id,\n", "                      item_id,\n", "                      state.name as item_state\n", "      FROM rpc.product_facility_master_assortment a\n", "      inner join rpc.product_master_assortment_substate state on state.id=a.master_assortment_substate_id\n", "      INNER JOIN crates.facility f ON f.id=a.facility_id\n", "      WHERE a.active = 1\n", "        and item_id IN ({item_id})\n", "                      \n", "      ORDER BY 1\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "\n", "active_items = pd.read_sql_query(query, retail)\n", "active_items.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item tag mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT ro.facility_id,\n", "                      tag_value AS backend_outlet,\n", "                      r.name AS backend_outlet_name,\n", "                      item_id\n", "      FROM rpc.item_outlet_tag_mapping tag\n", "      INNER JOIN retail.console_outlet ro ON ro.id=tag.outlet_id\n", "      INNER JOIN retail.console_outlet r ON r.id=tag.tag_value\n", "      WHERE tag_type_id=8\n", "        AND tag.active=1\n", "        and ro.name not like \"%%old%%\"\n", "        and item_id IN ({item_id})\n", "    \n", "      ORDER BY 1,\n", "               2,\n", "               3,\n", "               4\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "\n", "item_tag = pd.read_sql_query(query, retail)\n", "item_tag.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merging item status and tag on the net inventory cpd table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_inventory_status = net_inventory_cpd.merge(\n", "    active_items, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "net_inventory_status = net_inventory_status.fillna(\"Inactive\")\n", "net_inventory_status.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_inventory_tag = net_inventory_status.merge(item_tag, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "net_inventory_tag[[\"backend_outlet_name\"]] = net_inventory_tag[[\"backend_outlet_name\"]].fillna(\n", "    \"Direct ordering\"\n", ")\n", "net_inventory_tag = net_inventory_tag.drop_duplicates()\n", "net_inventory_tag = net_inventory_tag.fillna(100000)\n", "net_inventory_tag.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_inventory_tag.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select poi.item_id,\n", "o.facility_id,\n", "p.po_number,\n", "p.vendor_name,\n", "date(issue_date) AS issue_date,\n", "date(expiry_date) AS expiry_date,\n", "date(ps.schedule_date_time) AS schedule_date,\n", "poi.units_ordered AS po_qty\n", "FROM po.purchase_order p\n", "LEFT JOIN po.po_schedule ps ON p.id=ps.po_id_id\n", "INNER JOIN po.purchase_order_items poi ON p.id=poi.po_id\n", "INNER JOIN retail.console_outlet o ON o.id=p.outlet_id\n", "INNER JOIN retail.console_location cl ON cl.id=o.tax_location_id\n", "INNER JOIN retail.auth_user a ON a.id=p.created_by\n", "INNER JOIN po.purchase_order_status posa ON posa.po_id = p.id\n", "INNER JOIN po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "WHERE posta.id not  IN (4,5,8,9,10)\n", "  AND date(convert_tz(p.issue_date,'+00:00','+05:30'))\n", "  AND poi.item_id IN ({item_id})\n", "                      \"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "\n", "po = pd.read_sql_query(query, retail)\n", "\n", "po.sort_values(by=[\"facility_id\"], inplace=True)\n", "po.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_inventory_po = net_inventory_tag.merge(po, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "net_inventory_po[\"po_number\"] = net_inventory_po[\"po_number\"].fillna(100000)\n", "net_inventory_po[\"issue_date\"] = net_inventory_po[\"issue_date\"].fillna(\"\")\n", "net_inventory_po[\"expiry_date\"] = net_inventory_po[\"expiry_date\"].fillna(\"\")\n", "net_inventory_po[\"schedule_date\"] = net_inventory_po[\"schedule_date\"].fillna(\"\")\n", "net_inventory_po[\"vendor_name\"] = net_inventory_po[\"vendor_name\"].fillna(\"\")\n", "net_inventory_po[\"po_qty\"] = net_inventory_po[\"po_qty\"].fillna(100000)\n", "net_inventory_po.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_po = (\n", "    net_inventory_po.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"item_id\",\n", "            \"qty\",\n", "            \"multiplier\",\n", "            \"Required_qty\",\n", "            \"actual_quantity\",\n", "            \"blocked_quantity\",\n", "            \"unblocked_quantity\",\n", "            \"average cpd (till 14th jan)\",\n", "            \"average cpd (14-26 jan)\",\n", "            \"average cpd (27-31 jan)\",\n", "            \"item_state\",\n", "            \"backend_outlet\",\n", "            \"backend_outlet_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"po_number\": list,\n", "            \"vendor_name\": list,\n", "            \"issue_date\": list,\n", "            \"expiry_date\": list,\n", "            \"schedule_date\": list,\n", "            \"po_qty\": list,\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "def list_remove(x):\n", "    converted_list = [str(element) for element in x]\n", "    return \",\".join(converted_list)\n", "\n", "\n", "def int_conversion(x):\n", "    converted_list = [int(element) for element in x]\n", "    return converted_list\n", "\n", "\n", "def list_duplicates(x):\n", "    new_list = []\n", "    for i in x:\n", "        if i not in new_list:\n", "            new_list.append(i)\n", "    return new_list\n", "\n", "\n", "raw_po[\"po_number\"] = raw_po[\"po_number\"].apply(int_conversion)\n", "raw_po[\"po_qty\"] = raw_po[\"po_qty\"].apply(int_conversion)\n", "raw_po[\"issue_date\"] = raw_po[\"issue_date\"].apply(list_remove)\n", "raw_po[\"schedule_date\"] = raw_po[\"schedule_date\"].apply(list_remove)\n", "raw_po[\"vendor_name\"] = raw_po[\"vendor_name\"].apply(list_duplicates)\n", "raw_po[\"vendor_name\"] = raw_po[\"vendor_name\"].apply(list_remove)\n", "raw_po[\"expiry_date\"] = raw_po[\"expiry_date\"].apply(list_remove)\n", "raw_po.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# raw_po.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query=\"\"\"SELECT merchant_outlet_id AS receiving_outlet_id,\n", "#        o.name as receiving_outlet,\n", "#        outlet_id as sender_outlet_id,\n", "#        o1.name as sender_outlet,\n", "#        item_id,\n", "#        sum(quantity) AS intransit_quantity\n", "# FROM ims.ims_sto_mapping_inventory\n", "# INNER JOIN retail.console_outlet o\n", "# on ims_sto_mapping_inventory.merchant_outlet_id = o.id\n", "# INNER JOIN retail.console_outlet o1\n", "# on ims_sto_mapping_inventory.outlet_id = o1.id\n", "# WHERE inventory_type IN (1,2)\n", "# GROUP BY 1,\n", "#          2,\n", "#          3,\n", "#          4,\n", "#          5\n", "# HAVING intransit_quantity>0\n", "# ORDER BY 2,5\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sto details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"Select facility_id,T1.sto_id,date(T1.sto_created_at) as created_date,T1.item_id,T1.ordered_quantity-coalesce(T1.grn_quantity,0) as intransit_quantity\n", "\n", "FROM \n", "\n", "(Select facility_id,\n", "receiver_outlet_name,\n", "sender_outlet_name,\n", "item_id,\n", "sto_id,\n", "concat(sto_id,item_id) as sto_id_item_id,\n", "sto_type,\n", "sto_state,\n", "invoice_id,\n", "invoice_state,\n", "sto_created_at,\n", "sto_invoice_created_at as sto_billed_at,\n", "grn_created_at,\n", "reserved_quantity as ordered_quantity, \n", "billed_quantity,\n", "inwarded_quantity as grn_quantity\n", "\n", "from metrics.esto e\n", "inner join lake_retail.console_outlet o on o.id=e.receiving_outlet_id\n", "where date(created_date) between date(current_date - interval '30 days') and current_date \n", "and item_id in ({item_id})\n", "and grn_quantity is null and invoice_state not in ('GRN Complete', 'GRN Force Complete') and \n", "sto_state not in ('Manual Expiry') \n", " order by item_id\n", "\n", ") T1 \n", "\n", "left join \n", "\n", "(Select receiver_outlet_name,\n", "sender_outlet_name,\n", "item_id,\n", "sto_id,\n", "concat(sto_id,item_id) as sto_id_item_id\n", "from metrics.esto\n", "where  date(created_date) between date(current_date - interval '30 days') and current_date \n", "AND item_id in ({item_id})\n", "and inwarded_quantity is null and invoice_state not in ('GRN Complete', 'GRN Force Complete')  \n", "and sto_state = 'Expired' and sto_billed_at is null\n", ") T2 \n", "on T1.sto_id_item_id = T2.sto_id_item_id \n", "where T2.sto_id_item_id is null\n", "\n", "order by item_id\n", "\"\"\".format(\n", "    item_id=item_id_list\n", ")\n", "\n", "sto_new = pd.read_sql(query, redshift)\n", "\n", "sto_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto = sto_new.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"SELECT distinct o1.facility_id as facility_id,\n", "#           sto_id,\n", "#           date(delivery_date) as delivery_date ,\n", "#           date(created_date) as created_date,\n", "#           a.item_id,\n", "#           sum(case when a.invoice_state = 'NA' then a.reserved_quantity else 0 end) +\n", "#           sum(case when a.invoice_state = 'Raised' then a.billed_quantity else 0 end) as intransit_quantity\n", "#    FROM metrics.esto a\n", "#    inner join lake_retail.console_outlet o1 on o1.id=a.receiving_outlet_id\n", "#    inner join lake_crates.facility f1 on f1.id=o1.facility_id\n", "#    WHERE  a.invoice_state IN ('NA','Raised')\n", "#    AND a.sto_state != 'Expired'\n", "#    AND a.item_id in ({item_id})\n", "#    AND a.etl_timestamp_utc = (select max(etl_timestamp_utc) from metrics.esto)\n", "#    GROUP BY 1,2,3,4,5\n", "# \"\"\".format(\n", "#     item_id=item_id_list\n", "# )\n", "# sto = pd.read_sql(query, redshift)\n", "\n", "# sto.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_inventory_sto = net_inventory_tag.merge(sto, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "net_inventory_sto[\"sto_id\"] = net_inventory_sto[\"sto_id\"].fillna(100000)\n", "net_inventory_sto[\"created_date\"] = net_inventory_sto[\"created_date\"].fillna(\"\")\n", "net_inventory_sto[\"intransit_quantity\"] = net_inventory_sto[\"intransit_quantity\"].fillna(100000)\n", "net_inventory_sto.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = (\n", "    net_inventory_sto.groupby([\"facility_id\", \"item_id\"])\n", "    .agg(\n", "        {\n", "            \"sto_id\": list,\n", "            \"created_date\": list,\n", "            \"intransit_quantity\": list,\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "final[\"sto_id\"] = final[\"sto_id\"].apply(int_conversion)\n", "final[\"created_date\"] = final[\"created_date\"].apply(list_remove)\n", "final[\"intransit_quantity\"] = final[\"intransit_quantity\"].apply(int_conversion)\n", "final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new = raw_po.merge(final, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "final_new[[\"po_number\", \"po_qty\", \"sto_id\", \"intransit_quantity\", \"vendor_name\"]] = final_new[\n", "    [\"po_number\", \"po_qty\", \"sto_id\", \"intransit_quantity\", \"vendor_name\"]\n", "].astype(\"str\")\n", "\n", "final_new[\"backend_outlet\"] = final_new[\"backend_outlet\"].astype(\"str\")\n", "\n", "final_new.replace(\"[100000]\", \"\", inplace=True)\n", "final_new[\"backend_outlet_name\"].replace(\"Direct ordering\", \"\", inplace=True)\n", "final_new.columns = final_new.columns.str.replace(\"_\", \" \")\n", "final_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def order_type(x):\n", "    if x[\"backend outlet name\"] == \"\":\n", "        return \"Direct ordering\"\n", "    else:\n", "        return \"STO Transfer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new[\"order type\"] = final_new.apply(order_type, axis=1)\n", "final_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding backend inventory in the data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id  as 'backend outlet',\n", "facility_id as back_facility\n", "from retail.console_outlet\"\"\"\n", "facility = pd.read_sql_query(query, retail)\n", "facility = facility.dropna()\n", "\n", "final_new[\"backend outlet\"] = final_new[\"backend outlet\"].astype(\"int\")\n", "final_new_backend = final_new.merge(facility, on=[\"backend outlet\"], how=\"left\")\n", "Total_inventory[\"unblocked_quantity\"] = np.where(\n", "    (Total_inventory[\"actual_quantity\"] - Total_inventory[\"blocked_quantity\"]) < 0,\n", "    0,\n", "    Total_inventory[\"actual_quantity\"] - Total_inventory[\"blocked_quantity\"],\n", ")\n", "backend_inventory = (\n", "    Total_inventory[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"actual_quantity\",\n", "            \"blocked_quantity\",\n", "            \"unblocked_quantity\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(\n", "        columns={\n", "            \"actual_quantity\": \"backend_actual_quantity\",\n", "            \"blocked_quantity\": \"backend_blocked_quantity\",\n", "            \"unblocked_quantity\": \"backend_unblocked_quantity\",\n", "        }\n", "    )\n", ")\n", "backend_inventory.rename(\n", "    columns={\"facility_id\": \"back_facility\", \"item_id\": \"item id\"}, inplace=True\n", ")\n", "final_new_backend2 = final_new_backend.merge(\n", "    backend_inventory, on=[\"back_facility\", \"item id\"], how=\"left\"\n", ")\n", "final_new_backend2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_po = po.copy()\n", "backend_po[\"schedule_date\"] = backend_po[\"schedule_date\"].astype(str)\n", "backend_po[\"schedule_date\"].replace(\"None\", np.nan, inplace=True)\n", "backend_po[\"schedule_date\"] = backend_po[\"schedule_date\"].fillna(\"\")\n", "\n", "backend_po = (\n", "    backend_po.groupby([\"facility_id\", \"item_id\"])\n", "    .agg(\n", "        {\n", "            \"po_number\": list,\n", "            \"vendor_name\": list,\n", "            \"issue_date\": list,\n", "            \"expiry_date\": list,\n", "            \"schedule_date\": list,\n", "            \"po_qty\": list,\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "backend_po[\"po_number\"] = backend_po[\"po_number\"].apply(int_conversion)\n", "backend_po[\"issue_date\"] = backend_po[\"issue_date\"].apply(list_remove)\n", "backend_po[\"expiry_date\"] = backend_po[\"expiry_date\"].apply(list_remove)\n", "backend_po[\"vendor_name\"] = backend_po[\"vendor_name\"].apply(list_duplicates)\n", "backend_po[\"vendor_name\"] = backend_po[\"vendor_name\"].apply(list_remove)\n", "backend_po[\"schedule_date\"] = backend_po[\"schedule_date\"].apply(list_remove)\n", "backend_po[\"po_qty\"] = backend_po[\"po_qty\"].apply(int_conversion)\n", "\n", "\n", "backend_po.rename(\n", "    columns={\n", "        \"item_id\": \"item id\",\n", "        \"facility_id\": \"back_facility\",\n", "        \"po_number\": \"backend_po_number\",\n", "        \"vendor_name\": \"backend_vendor_name\",\n", "        \"issue_date\": \"backend_issue_date\",\n", "        \"expiry_date\": \"backend_expiry_date\",\n", "        \"schedule_date\": \"backend_schedule_date\",\n", "        \"po_qty\": \"backend_po_qty\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "\n", "backend_po.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new_backend3 = final_new_backend2.merge(\n", "    backend_po, on=[\"back_facility\", \"item id\"], how=\"left\"\n", ")\n", "final_new_backend3 = final_new_backend3.merge(\n", "    item_product_mapping[[\"item_id\", \"l0\"]],\n", "    left_on=[\"item id\"],\n", "    right_on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "final_new_backend3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def facility_identification(x):\n", "    if \"Super Store Gurgaon G4 - Warehouse\" in x[\"facility name\"]:\n", "        return \"Dark_store\"\n", "    elif \"es\" in x[\"facility name\"].lower():\n", "        return \"Dark_store\"\n", "    elif \"dark\" in x[\"facility name\"].lower():\n", "        return \"Franchise\"\n", "    else:\n", "        return \"NDD\"\n", "\n", "\n", "grocery = [\n", "    \"Personal Care\",\n", "    \"Household Items\",\n", "    \"Biscuits, Snacks & Chocolates\",\n", "    \"Breakfast & Dairy\",\n", "    \"Fresh & Frozen Food\",\n", "    \"Noodles, Sauces & Instant Food\",\n", "    \"Pet Care\",\n", "    \"Beverages\",\n", "    \"Baby Care\",\n", "    \"Grocery & Staples\",\n", "]\n", "\n", "\n", "def item_type(x):\n", "    if x[\"l0\"] in (grocery):\n", "        return \"Grocery/FMCG\"\n", "    else:\n", "        return \"GM\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new_backend3[\"facility type\"] = final_new_backend3.apply(facility_identification, axis=1)\n", "final_new_backend3[\"item type\"] = final_new_backend3.apply(item_type, axis=1)\n", "final_new_backend3.rename(columns={\"qty\": \"Inaam qty\"}, inplace=True)\n", "final_new_backend3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select item_id as 'item id',max(name) as 'item name' from rpc.product_product\n", "group by 1\"\"\"\n", "item_name = pd.read_sql_query(query, retail)\n", "\n", "final_new_backend3 = final_new_backend3.merge(item_name, on=[\"item id\"], how=\"left\")\n", "final_new_backend3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inaam items sales till now"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inaam_pids = inaams_new[[\"New pid/offer_id\"]].drop_duplicates()\n", "pid_list = tuple(inaam_pids[\"New pid/offer_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select outlet_id,product_id,sum(quantity) as order_qty from\n", "(SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "                date_ist(a.install_ts) AS checkout_date,\n", "                b.name AS front_end,\n", "                a.id AS ancestor,\n", "                a.current_status,\n", "                so.id AS sub_order_id,\n", "                case when b.name='Super Store - Delhi Jhilmil ES1'  then 1024\n", "                when b.name='Super Store - Indirapuram'  then 976\n", "                else o.outlet end AS outlet_id,\n", "                oa.product_id,\n", "                oi.quantity\n", "FROM bifrost.oms_order a\n", "INNER JOIN bifrost.oms_merchant b ON a.merchant_id = b.id\n", "INNER JOIN bifrost.oms_suborder so ON so.order_id=a.id\n", "INNER JOIN bifrost.oms_suborder_item oi ON so.id = oi.suborder_id\n", "INNER JOIN bifrost.oms_order_item oa ON oa.id=oi.order_item_id\n", "INNER JOIN ims.ims_order_details o ON o.order_id::int=so.id\n", "WHERE date_ist(a.install_ts) >='2021-01-15'\n", "  AND oa.product_id IN %(product)s\n", "  AND a.direction ='FORWARD'\n", "  AND a.current_status != 'CANCELLED'\n", "  AND so.current_status IN ('AT_DELIVERY_CENTER',\n", "                            'BILLED',\n", "                            'APPROVED')\n", "  AND a.type IN ('RetailForwardOrder',\n", "                 'InternalForwardOrder')\n", "ORDER BY 1,\n", "         2,\n", "         3,\n", "         4) x1\n", "         group by 1,2\"\"\"\n", "\n", "oms_items = pd.read_sql(query, aur, params={\"product\": pid_list})\n", "oms_items.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet facility mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,facility_id from retail.console_outlet\"\"\"\n", "outlet_facility = pd.read_sql_query(query, retail)\n", "outlet_facility = outlet_facility.dropna()\n", "outlet_facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_items_facility = oms_items.merge(outlet_facility, on=[\"outlet_id\"], how=\"left\")\n", "total_orders = (\n", "    oms_items_facility.groupby([\"facility_id\", \"product_id\"])[[\"order_qty\"]].sum().reset_index()\n", ")\n", "total_orders.rename(columns={\"order_qty\": \"Orders_till_date\"}, inplace=True)\n", "total_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product = (\n", "    inaams_new[[\"item id\", \"New pid/offer_id\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"item id\": \"item id\", \"New pid/offer_id\": \"product_id\"})\n", ")\n", "item_product[\"product_id\"] = item_product[\"product_id\"].astype(\"int\")\n", "total_orders_new = total_orders.merge(item_product, on=[\"product_id\"], how=\"left\")\n", "total_orders_new = (\n", "    total_orders_new[[\"facility_id\", \"item id\", \"Orders_till_date\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"facility_id\": \"facility id\"})\n", ")\n", "total_orders_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_orders_new[[\"facility id\", \"item id\"]] = total_orders_new[[\"facility id\", \"item id\"]].astype(\n", "    \"int\"\n", ")\n", "\n", "final_new_backend4 = final_new_backend3.merge(\n", "    total_orders_new, on=[\"facility id\", \"item id\"], how=\"left\"\n", ")\n", "final_new_backend4[\"Orders_till_date\"] = final_new_backend4[\"Orders_till_date\"].fillna(0)\n", "final_new_backend4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new_backend2 = final_new_backend4[\n", "    [\n", "        \"facility id\",\n", "        \"facility name\",\n", "        \"facility type\",\n", "        \"item id\",\n", "        \"item name\",\n", "        \"l0\",\n", "        \"item type\",\n", "        \"item state\",\n", "        \"Inaam qty\",\n", "        \"multiplier\",\n", "        \"Required qty\",\n", "        \"Orders_till_date\",\n", "        \"actual quantity\",\n", "        \"blocked quantity\",\n", "        \"unblocked quantity\",\n", "        \"average cpd (till 14th jan)\",\n", "        \"average cpd (14-26 jan)\",\n", "        \"average cpd (27-31 jan)\",\n", "        \"order type\",\n", "        \"po number\",\n", "        \"vendor name\",\n", "        \"issue date\",\n", "        \"expiry date\",\n", "        \"schedule date\",\n", "        \"po qty\",\n", "        \"sto id\",\n", "        \"created date\",\n", "        \"intransit quantity\",\n", "        \"backend outlet\",\n", "        \"backend outlet name\",\n", "        \"backend_actual_quantity\",\n", "        \"backend_blocked_quantity\",\n", "        \"backend_unblocked_quantity\",\n", "        \"backend_po_number\",\n", "        \"backend_vendor_name\",\n", "        \"backend_issue_date\",\n", "        \"backend_expiry_date\",\n", "        \"backend_schedule_date\",\n", "        \"backend_po_qty\",\n", "    ]\n", "].rename(columns={\"Inaam qty\": \"Total Inaam qty\", \"Required qty\": \"Required Inaam qty\"})\n", "\n", "final_new_backend2.columns = final_new_backend2.columns.str.replace(\"_\", \" \")\n", "final_new_backend2[\"backend outlet\"] = final_new_backend2[\"backend outlet\"].astype(\"str\")\n", "final_new_backend2[\"backend outlet\"].replace(\"100000\", \"\", inplace=True)\n", "final_new_backend2[\n", "    [\n", "        \"backend actual quantity\",\n", "        \"backend blocked quantity\",\n", "        \"backend unblocked quantity\",\n", "    ]\n", "] = final_new_backend2[\n", "    [\n", "        \"backend actual quantity\",\n", "        \"backend blocked quantity\",\n", "        \"backend unblocked quantity\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "final_new_backend2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new_backend2[\"backend actual quantity\"] = np.where(\n", "    final_new_backend2[\"backend outlet name\"] == \"\",\n", "    \"\",\n", "    final_new_backend2[\"backend actual quantity\"],\n", ")\n", "final_new_backend2[\"backend blocked quantity\"] = np.where(\n", "    final_new_backend2[\"backend outlet name\"] == \"\",\n", "    \"\",\n", "    final_new_backend2[\"backend blocked quantity\"],\n", ")\n", "final_new_backend2[\"backend unblocked quantity\"] = np.where(\n", "    final_new_backend2[\"backend outlet name\"] == \"\",\n", "    \"\",\n", "    final_new_backend2[\"backend unblocked quantity\"],\n", ")\n", "\n", "# final_new_backend2[\"Required Inaam qty (GOBD)\"].replace(1000000000, \"\", inplace=True)\n", "final_new_backend2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new_backend2.rename(\n", "    columns={\"Orders till date\": \"Order qty(15th jan to till date)\"}, inplace=True\n", ")\n", "final_new_backend2[\"updated_at\"] = datetime.now() + timed<PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets_patch(final_new_backend2, sheet_exclusion, \"raw1\", service_account=\"service_account\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Items to disable"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_new = \"1KGvh-lYG2R7J7MibiiZ86DeVF0lIAGLuGDlBxcwOQ_I\"\n", "sheet_name_new = \"Offer id-from content\"\n", "\n", "inaams_new = pb.from_sheets_patch(sheet_new, sheet_name_new, service_account=\"service_account\")\n", "disable_pid = (\n", "    inaams_new[[\"item id\", \"Base pid\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"item id\": \"item_id\", \"Base pid\": \"product_id\"})\n", ")\n", "disable_pid.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1YYBvyql8PeIfzGuekQtfbIhEfWEZasu0RR15o7Zgzqo\"\n", "sheet_name = \"enable_pid\"\n", "disable_ids = pb.from_sheets_patch(sheet_id, sheet_name, service_account=\"service_account\")\n", "disable_ids_base = disable_ids.merge(disable_pid, on=[\"item_id\"], how=\"left\")\n", "disable_ids_base = disable_ids_base[[\"item_id\", \"product_id\"]].drop_duplicates()\n", "disable_ids_base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_qty_available = final_new_backend2[\n", "    [\"facility id\", \"facility name\", \"item id\", \"unblocked quantity\"]\n", "].drop_duplicates()\n", "items_qty_available.columns = items_qty_available.columns.str.replace(\" \", \"_\")\n", "items_qty_available.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Product id mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disable_ids_base[\"item_id\"] = disable_pid[\"item_id\"].astype(\"int\")\n", "items_qty_available[\"item_id\"] = items_qty_available[\"item_id\"].astype(\"int\")\n", "items_qty_available_product = disable_ids_base.merge(\n", "    items_qty_available, on=[\"item_id\"], how=\"left\"\n", ")\n", "items_qty_available_product = items_qty_available_product.merge(\n", "    inaam_required_qty, on=[\"facility_name\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "items_qty_available_product = items_qty_available_product.fillna(\n", "    items_qty_available_product[\"qty\"].median()\n", ")\n", "items_qty_available_product[\"qty\"] = items_qty_available_product[\"qty\"].astype(\"int\")\n", "items_qty_available_product = items_qty_available_product[\n", "    items_qty_available_product[\"unblocked_quantity\"] > 0\n", "]\n", "items_qty_available_product.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def items_enable_flag(x):\n", "    if x[\"unblocked_quantity\"] <= (x[\"qty\"] / 12) * 3:\n", "        return 0\n", "    else:\n", "        return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = items_qty_available_product.copy()\n", "final[\"enable_flag\"] = final.apply(items_enable_flag, axis=1)\n", "final.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Backend outlet mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH MERCHANT AS\n", "  (SELECT DISTINCT M.id,\n", "                   m.name,\n", "                   m.enabled_flag,\n", "                   L.NAME AS city\n", "   FROM lake_cms.gr_merchant M\n", "   INNER JOIN lake_cms.gr_merchant_additional_info MAI ON MAI.MERCHANT_ID = M.ID\n", "   AND (MAI.MERCHANT_STORE_TYPE = 'grofers')\n", "   INNER JOIN lake_cms.gr_locality L2 ON M.LOCALITY_ID = L2.ID\n", "   AND (M.NAME NOT ILIKE '%%GROCERY%%MART%%'\n", "        AND M.NAME NOT ILIKE '%%FRESHBURY%%'\n", "        AND M.NAME NOT ILIKE '%%test%%'\n", "        AND M.NAME NOT ILIKE '%%donation%%')\n", "   INNER JOIN lake_cms.gr_locality L1 ON L2.PARENT_LOCALITY_ID = L1.ID\n", "   INNER JOIN lake_cms.gr_locality L ON L1.PARENT_LOCALITY_ID = L.ID\n", "   AND L.ID <> 2051)\n", "SELECT DISTINCT vmp.real_merchant_id::int AS backend_merchant,\n", "                f.id AS facility_id\n", "FROM lake_cms.gr_virtual_to_real_merchant_mapping vmp\n", "INNER JOIN merchant m ON m.id=vmp.real_merchant_id\n", "INNER JOIN merchant m1 ON m1.id=vmp.virtual_merchant_id\n", "INNER JOIN lake_retail.console_outlet_cms_store cms ON cms.cms_store=vmp.real_merchant_id\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=cms.outlet_id\n", "INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "AND m1.enabled_flag=TRUE\n", "AND vmp.enabled_flag=TRUE\n", "AND cms.active = 1\n", "and f.id in %(facility)s\n", "AND cms.cms_update_active = 1\n", "ORDER BY f.id\"\"\"\n", "\n", "merchant_outlet = pd.read_sql(query, redshift, params={\"facility\": tuple(final[\"facility_id\"])})\n", "merchant_outlet = merchant_outlet.dropna()\n", "merchant_outlet.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Active backend pids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT merchant_id as backend_merchant,\n", "                product_id\n", "FROM grofers_db.gr_merchant_product_mapping\n", "WHERE merchant_id in %(merchant)s\n", "and inventory_limit > 0 \n", "and enabled_flag= True\n", "order by 1\n", "\n", "\"\"\"\n", "\n", "mapped_items = pd.read_sql(\n", "    query, aur, params={\"merchant\": tuple(list(merchant_outlet[\"backend_merchant\"]))}\n", ")\n", "\n", "mapped_items_facility = mapped_items.merge(merchant_outlet, on=[\"backend_merchant\"], how=\"left\")\n", "mapped_items_facility = mapped_items_facility[\n", "    [\"facility_id\", \"backend_merchant\", \"product_id\"]\n", "].drop_duplicates()\n", "mapped_items_facility[\"current_state\"] = 1\n", "mapped_items_facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapped_items_facility[[\"facility_id\", \"product_id\"]] = mapped_items_facility[\n", "    [\"facility_id\", \"product_id\"]\n", "].astype(\"int\")\n", "final[[\"facility_id\", \"product_id\"]] = final[[\"facility_id\", \"product_id\"]].astype(\"int\")\n", "if len(mapped_items_facility) > 0:\n", "    final_cs = final.merge(mapped_items_facility, on=[\"facility_id\", \"product_id\"], how=\"inner\")\n", "    final_cs[\"current_state\"] = final_cs[\"current_state\"].fillna(0)\n", "else:\n", "    final_cs = final.copy()\n", "    final_cs[\"current_state\"] = final_cs[\"current_state\"].fillna(0)\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "final_cs[\"updated_at\"] = now\n", "final_cs.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cs.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]\n", "\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"inaam_items_disable_gobd\",\n", "    \"column_dtypes\": redshift_schema(final_cs),\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"product_id\"],\n", "    \"sortkey\": [\"facility_id\", \"item_id\"],\n", "    \"incremental_key\": [\"updated_at\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(final_cs, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delta_items = final_cs[final_cs[\"enable_flag\"] != final_cs[\"current_state\"]]\n", "item_disable = delta_items[delta_items[\"enable_flag\"] == 0]\n", "item_disable.drop_duplicates()\n", "item_disable = item_disable[[\"backend_merchant\", \"product_id\"]].drop_duplicates()\n", "item_disable.to_csv(\"/tmp/pid_disable.csv\", index=False)\n", "item_disable.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_disable.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delta_items = final_cs[final_cs[\"enable_flag\"] != final_cs[\"current_state\"]]\n", "item_enable = delta_items[delta_items[\"enable_flag\"] == 1]\n", "item_enable.drop_duplicates()\n", "item_enable = item_enable[[\"backend_merchant\", \"product_id\"]].drop_duplicates()\n", "item_enable.to_csv(\"/tmp/pid_enable.csv\", index=False)\n", "item_enable"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_enable.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email = pb.from_sheets_patch(sheet_id, \"email\", service_account=\"service_account\")\n", "email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = \"\"\"\n", "    Hi,<br><br>\n", "    Please find attached list of pids that needs to be enable/disable at the given facility.<br><br>\n", "    <PERSON><PERSON>,<br>\n", "    <PERSON><PERSON><PERSON>\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "import pencilbox as pb\n", "from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "subject = \"Inaam pids to be enabled/disabled\"\n", "\n", "to_email = email[\"email_id\"].tolist()\n", "# to_email=[\"<EMAIL>\"]\n", "\n", "if len(item_enable) > 0 and len(item_disable) > 0:\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[\"/tmp/pid_disable.csv\", \"/tmp/pid_enable.csv\"],\n", "    )\n", "\n", "if len(item_enable) > 0 and len(item_disable) == 0:\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[\"/tmp/pid_enable.csv\"],\n", "    )\n", "\n", "\n", "if len(item_enable) == 0 and len(item_disable) > 0:\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[\"/tmp/pid_disable.csv\"],\n", "    )\n", "\n", "\n", "print(\"mail sent\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}