{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime\n", "import math\n", "import json\n", "import uuid\n", "import logging\n", "from copy import deepcopy\n", "import warnings\n", "import numpy as np\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "\n", "pd.set_option(\"display.max_colwidth\", 40)\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "CON_SQL = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pip\n", "\n", "\n", "def import_or_install(package):\n", "    try:\n", "        __import__(package)\n", "    except ImportError:\n", "        pip.main([\"install\", package])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import_or_install(\"openpyxl\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excel_sheets = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Lb5meGTKr4cu0NS75BMrKuPep3itx0zjvU9qLA17J-g\"\n", "sheet_name = \"for_assortment\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = pb.from_sheets(\n", "    sheet_id, sheet_name, service_account=\"service_account\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = assortment_sheet.astype(\n", "    {\n", "        \"outlet_id\": int,\n", "        \"item_id\": int,\n", "        \"name\": str,\n", "        \"type_flag\": str,\n", "        \"doi\": int,\n", "        \"moq\": int,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_list = list(assortment_sheet.outlet_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets = \",\".join(map(str, outlet_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "SELECT -- date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) AS dated,\n", " r.id AS outlet_id,\n", " i.item_id,\n", " sum(i.quantity) as cpd\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) BETWEEN DATE_ADD(CURRENT_DATE, INTERVAL -8 DAY) AND CURRENT_DATE\n", "  AND r.id IN ({outlets})\n", "  AND status_id NOT IN (3,\n", "                        5)\n", "GROUP BY 1,\n", "         2\n", "ORDER BY 1,\n", "         2\n", "\"\"\".format(\n", "    outlets=outlets\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["cpd_query = \"\"\"\n", "WITH v1 AS\n", "  (SELECT *,\n", "          date(l.created_at) AS dt,\n", "          actual_quantity-blocked_quantity AS net_qty\n", "   FROM consumer.ims_item_inventory_log l\n", "   WHERE l.outlet_id in (846,926) -- hard coded outlet\n", "     AND l.item_id IN\n", "       (SELECT distinct(item_id)\n", "        FROM lake_rpc.product_product\n", "        WHERE outlet_type = 1)\n", "     AND l.order_log_id IS NOT NULL\n", "     AND l.\"delta\" >= 0\n", "     AND date(l.created_at) >= CURRENT_DATE - 30\n", "   ORDER BY l.created_At DESC),\n", "     v2 AS\n", "  (SELECT item_id,\n", "          outlet_id,\n", "          dt,\n", "          (max(net_qty) - min(net_qty)) AS sold,\n", "          (EXTRACT(epoch\n", "                   FROM (max(created_at) - min(created_at)))/3600) AS ts\n", "   FROM v1\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     v3 AS\n", "  (SELECT *,\n", "          sold*12/ts AS cpd,\n", "          date_part(dow, dt) AS dow\n", "   FROM v2\n", "   WHERE ts >= 1\n", "   ORDER BY outlet_id,\n", "            item_id,\n", "            dt)\n", "SELECT outlet_id,\n", "       item_id,\n", "       avg(CASE\n", "               WHEN dow IN (6,0) THEN cpd\n", "           END) AS weekend_cpd,\n", "       avg(CASE\n", "               WHEN dow IN (1,2,3,4,5) THEN cpd\n", "           END) AS weekday_cpd\n", "FROM v3\n", "GROUP BY 1,\n", "         2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd = pd.read_sql_query(sql=cpd_query, con=CON_SQL)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd[\"cpd\"] = cpd[\"cpd\"] / 7"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TODAY = datetime.datetime.today()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = assortment_sheet[assortment_sheet.type_flag == \"FnV\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excel_sheets[\"current_fnv_assortment\"] = assortment_sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = pd.merge(assortment_sheet, cpd, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = assortment_sheet.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet[\"cpd\"] = assortment_sheet[\"cpd\"].apply(math.ceil)"]}, {"cell_type": "raw", "metadata": {}, "source": ["def count_weekday_weekends(i):\n", "    li = [x.weekday() for x in pd.date_range(start=TODAY + datetime.timedelta(1), periods=i).to_list()]\n", "    weekends = 0\n", "    weekdays = 0\n", "    for j in li:\n", "        if j in [5,6]:\n", "            weekends += 1\n", "        else:\n", "            weekdays += 1\n", "    return weekends,weekdays"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = assortment_sheet.fillna(0)"]}, {"cell_type": "raw", "metadata": {}, "source": ["assortment_sheet['weekends'],assortment_sheet['weekdays'] = zip(*assortment_sheet['doi'].map(count_weekday_weekends))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet[\"total_demand\"] = assortment_sheet[\"cpd\"] * assortment_sheet[\"doi\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet[\"total_demand_moq\"] = np.where(\n", "    assortment_sheet[\"total_demand\"] < assortment_sheet[\"moq\"],\n", "    assortment_sheet[\"moq\"],\n", "    assortment_sheet[\"total_demand\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_query = \"\"\"\n", "SELECT good_inv.outlet_id AS outlet_id,\n", "       product.item_id,\n", "       sum(CASE\n", "               WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity ELSE 0\n", "           END) AS quantity,\n", "           0 as blocked_quantity\n", "FROM ims.ims_good_inventory good_inv\n", "INNER JOIN rpc.product_product product ON product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1\n", "  AND good_inv.outlet_id IN ({{outlets}})\n", "GROUP BY 1,\n", "         2\n", "         \n", "         \n", "UNION\n", "\n", "SELECT a.outlet_id, \n", "       a.item_id, \n", "       a.quantity, \n", "       b.quantity AS blocked_quantity \n", "FROM   (SELECT outlet_id, \n", "               item_id, \n", "               quantity \n", "        FROM   ims.ims_item_inventory \n", "        WHERE  outlet_id IN ( {{outlets}} ) \n", "               AND active = 1) a \n", "       LEFT JOIN (SELECT item_id, \n", "                         outlet_id, \n", "                         sum(quantity) AS quantity\n", "                  FROM   ims.ims_item_blocked_inventory \n", "                  WHERE  outlet_id IN ( {{outlets}}  ) \n", "                         AND active = 1 \n", "                         AND blocked_type in (1,2,5)  -- blocked for sto and orders\n", "                 GROUP BY 1,2) b \n", "              ON a.item_id = b.item_id \n", "                 AND a.outlet_id = b.outlet_id \n", "\"\"\".replace(\n", "    \"{{\", \"{\"\n", ").replace(\n", "    \"}}\", \"}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = pd.read_sql_query(sql=inventory_query.format(outlets=outlets), con=CON_SQL)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = (\n", "    inventory.groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\", \"blocked_quantity\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = pd.merge(assortment_sheet, inventory, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet[\"net_quantity\"] = (\n", "    assortment_sheet[\"quantity\"] - assortment_sheet[\"blocked_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet[\"final_indent\"] = (\n", "    assortment_sheet[\"total_demand_moq\"] - assortment_sheet[\"net_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = assortment_sheet.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet[\"final_indent_po_release\"] = assortment_sheet[\"final_indent\"].apply(math.ceil)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet[\"vendor_forecast\"] = (\n", "    assortment_sheet[\"total_demand\"] - assortment_sheet[\"cpd\"] - assortment_sheet[\"net_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheet = assortment_sheet.round(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excel_sheets[\"final_indent\"] = assortment_sheet[assortment_sheet.final_indent > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excel_sheets[\"quantity_not_required\"] = assortment_sheet[assortment_sheet.final_indent <= 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uid = str(uuid.uuid1())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now_india = str(datetime.datetime.now(timezone(\"Asia/Kolkata\"))).split(\"+\")[0].split(\".\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excel_file = \"/tmp/{today}_fnv_indent_{uid}.xlsx\".format(today=now_india, uid=uid).replace(\" \", \"_\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with pd.ExcelWriter(excel_file) as writer:\n", "    for k, v in excel_sheets.items():\n", "        v.to_excel(writer, sheet_name=k, index=False, engine=\"xlsxwriter\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL> \",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "subject = \"Dark Stores F&V Replenishment | \" + str(now_india)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = \"\"\"\n", "Hi,<br><br>\n", "Please find attached indents for F&V.<br><br>\n", "<PERSON><PERSON>,<br>\n", "<PERSON><PERSON><PERSON>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    files=[excel_file],\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}