{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import difflib\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Refresh flag"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if now.hour >= 16 and now.hour <= 17:\n", "    refresh_flag = \"yes\"\n", "else:\n", "    refresh_flag = \"no\"\n", "\n", "refresh_flag"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating base assortment of all given facilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "cater_ds_mapping = pb.from_sheets(sheet_id, \"Mapping\", clear_cache=True)\n", "cater_ds_mapping = cater_ds_mapping[[\"front_end\", \"dark_store\"]].drop_duplicates()\n", "cater_ds_mapping.rename(columns={\"front_end\": \"outlet_id\", \"dark_store\": \"ds_outlet\"}, inplace=True)\n", "cater_ds_mapping.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Classification of store into DS/FS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["is_ds = pb.from_sheets(sheet_id, \"Mapping\")\n", "is_ds = is_ds[[\"dark_store\", \"is_darkstore\"]].drop_duplicates()\n", "is_ds.rename(columns={\"dark_store\": \"ds_outlet\"}, inplace=True)\n", "is_ds[[\"ds_outlet\", \"is_darkstore\"]] = is_ds[[\"ds_outlet\", \"is_darkstore\"]].astype(\"int\")\n", "is_ds.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Changing Datatypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cater_ds_mapping[[\"outlet_id\", \"ds_outlet\"]] = cater_ds_mapping[[\"outlet_id\", \"ds_outlet\"]].astype(\n", "    \"int\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON>- Facility Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,\n", "facility_id\n", "from retail.console_outlet\n", "where active=1\"\"\"\n", "facility = pd.read_sql_query(query, retail)\n", "\n", "cater_facility_outlet = cater_ds_mapping.merge(facility, on=[\"outlet_id\"], how=\"left\")\n", "cater_facility_outlet[\"facility_id\"] = cater_facility_outlet[\"facility_id\"].astype(\"int\")\n", "cater_facility_outlet.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cater_facility_outlet[\"facility_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_outlets = tuple(list(cater_facility_outlet[\"ds_outlet\"].unique()))\n", "catering_outlet = tuple(list(cater_facility_outlet[\"outlet_id\"].unique()))\n", "catering_facility = tuple(list(cater_facility_outlet[\"facility_id\"].unique()))\n", "run_id = datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "TODAY = date.today()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item-product Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "item_product_mapping = pd.read_sql(item_attributes_query, redshift)\n", "item_product_mapping.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining Base assortment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Total items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT facility_id,\n", "                item_id,\n", "                master_assortment_substate_id\n", "FROM rpc.product_master_assortment r\n", "INNER JOIN retail.console_outlet o ON r.city_id=o.tax_location_id\n", "WHERE o.id IN %(cater_outlets)s\n", "\"\"\"\n", "\n", "Total_items = pd.read_sql_query(query, retail, params={\"cater_outlets\": catering_outlet})\n", "Total_items = Total_items.drop_duplicates()\n", "Total_items.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Active items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items = Total_items[Total_items[\"master_assortment_substate_id\"] == 1]\n", "active_items.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Temp Inactive Items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inactive_items = Total_items[~Total_items[\"master_assortment_substate_id\"].isin([1])]\n", "inactive_items.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding inventory of temp inactive and inactive items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct \n", "          a.item_id ,\n", "   a.outlet_id,\n", "          quantity  AS actual_quantity\n", "\n", "   FROM ims.ims_item_inventory as a\n", "   inner join\n", "   ( select item_id,outlet_id,max(updated_at)as update_at from ims.ims_item_inventory group by 1,2 ) as b\n", "   on a.item_id=b.item_id\n", "   and a.outlet_id=b.outlet_id\n", "   and a.updated_at=b.update_at\n", "   where  a.outlet_id in  %(cater_outlets)s\n", "   group by 1,2,3\n", "   \"\"\"\n", "Total_inventory = pd.read_sql_query(query, retail, params={\"cater_outlets\": catering_outlet})\n", "\n", "blocked = \"\"\"\n", "    select item_id ,\n", "     outlet_id ,\n", "     max(CASE\n", "         WHEN blocked_type = 1 THEN quantity\n", "         ELSE 0\n", "     END) AS order_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 2 THEN quantity\n", "         ELSE 0\n", "     END )AS sto_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 4 THEN quantity\n", "         ELSE 0\n", "     END )AS pro_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 5 THEN quantity\n", "         ELSE 0\n", "     END )AS sso_blocked\n", "FROM ims.ims_item_blocked_inventory\n", "where outlet_id in %(cater_outlets)s\n", "group by 1,2\n", "             \"\"\"\n", "block_quantity = pd.read_sql_query(blocked, retail, params={\"cater_outlets\": catering_outlet})\n", "\n", "inv_data = Total_inventory.merge(block_quantity, on=[\"outlet_id\", \"item_id\"], how=\"outer\")\n", "inv_data[\"blocked_quantity\"] = (\n", "    inv_data.order_blocked + inv_data.sto_blocked + inv_data.pro_blocked + inv_data.sso_blocked\n", ")\n", "\n", "inv_data[\"available\"] = inv_data[\"actual_quantity\"] - inv_data[\"blocked_quantity\"]\n", "inv_data = inv_data[[\"outlet_id\", \"item_id\", \"available\"]].drop_duplicates()\n", "inv_data = inv_data[inv_data[\"available\"] > 0]\n", "inv_data = inv_data.drop_duplicates()\n", "\n", "Total_inventory = inv_data.merge(facility, on=[\"outlet_id\"], how=\"left\")\n", "\n", "Total_inventory = Total_inventory.drop_duplicates()\n", "\n", "Total_inventory_inactive = Total_inventory.merge(\n", "    inactive_items, on=[\"facility_id\", \"item_id\"], how=\"inner\"\n", ")\n", "\n", "\n", "inactive_assortment = Total_inventory_inactive[\n", "    [\"facility_id\", \"item_id\", \"master_assortment_substate_id\"]\n", "].drop_duplicates()\n", "\n", "inactive_assortment.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Base assortment( Union of active and temp inactive)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_assortment = pd.concat([active_items, inactive_assortment])\n", "base_assortment[\"facility_id\"] = base_assortment[\"facility_id\"].astype(\"int\")\n", "base_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_assortment[\"facility_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Old assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"SELECT s.facility_id,\n", "# s.item_id,\n", "# s.master_assortment_substate_id,\n", "# s.rank,\n", "# s.is_darkstore,\n", "# s.base_updated_at\n", "# FROM metrics.dark_store_base_assortment s\n", "# INNER JOIN\n", "#   (SELECT facility_id,\n", "#   is_darkstore,\n", "#           max(base_updated_at) AS updated_at,\n", "#           max(refresh_at) as refresh_at\n", "#    FROM metrics.dark_store_base_assortment\n", "#    GROUP BY 1,2)a ON s.facility_id=a.facility_id and s.is_darkstore=a.is_darkstore\n", "# AND s.base_updated_at=a.updated_at and s.refresh_at=a.refresh_at \"\"\"\n", "\n", "# old_assortment = pd.read_sql(query, redshift)\n", "# old_assortment[\"facility_id\"] = old_assortment[\"facility_id\"].astype(\"int\")\n", "# old_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# old_assortment[\"facility_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding new facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# old_facility = old_assortment[[\"facility_id\"]].drop_duplicates()\n", "# old_facility[\"old_facility\"] = 1\n", "\n", "# new_facility = base_assortment[[\"facility_id\"]].drop_duplicates()\n", "# new_facility[\"new_facility\"] = 1\n", "\n", "# facility_new_added = new_facility.merge(old_facility, on=[\"facility_id\"], how=\"left\")\n", "# facility_new_added = facility_new_added[facility_new_added[\"old_facility\"].isna()]\n", "# facility_new_added = facility_new_added[\"facility_id\"].to_list()\n", "# facility_new_added_tuple = tuple(facility_new_added)\n", "# facility_new_added"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ranking of new facility assortment on the basis of catering facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if len(facility_new_added) > 0:\n", "\n", "#     base_assortment_new_facility = base_assortment[\n", "#         base_assortment[\"facility_id\"].isin(facility_new_added)\n", "#     ]\n", "\n", "#     query = \"\"\"\n", "#     select distinct ro.facility_id,\n", "#     dwc.item_id,\n", "#     sum(dwc.consumption) as final_cpd\n", "#     from\n", "#     (select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where\n", "#     date between current_date + interval 1 day\n", "#     and current_date + interval 1 week) dwc\n", "#     join\n", "#     retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "#     left join retail.console_outlet ro on ro.id=dwc.outlet_id\n", "#     where ro.facility_id in %(facility)s\n", "#     group by 1,2\n", "#     \"\"\"\n", "#     cpd_new_facility = pd.read_sql_query(\n", "#         query, retail, params={\"facility\": facility_new_added_tuple}\n", "#     )\n", "\n", "#     base_assortment_new_facility_cpd = base_assortment_new_facility.merge(\n", "#         cpd_new_facility, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", "#     )\n", "#     base_assortment_new_facility_cpd.sort_values(\n", "#         by=[\"facility_id\", \"final_cpd\"], ascending=False, inplace=True\n", "#     )\n", "#     base_assortment_new_facility_cpd[\"rank\"] = (\n", "#         base_assortment_new_facility_cpd.groupby([\"facility_id\"]).cumcount() + 1\n", "#     )\n", "#     base_assortment_new_facility_cpd.head()\n", "#     base_assortment_new_facility_cpd = base_assortment_new_facility_cpd.merge(\n", "#         cater_facility_outlet[[\"facility_id\", \"ds_outlet\"]],\n", "#         on=[\"facility_id\"],\n", "#         how=\"left\",\n", "#     )\n", "\n", "#     base_assortment_new_facility_cpd = base_assortment_new_facility_cpd.merge(\n", "#         is_ds, on=[\"ds_outlet\"], how=\"left\"\n", "#     )\n", "#     base_assortment_new_facility_cpd = base_assortment_new_facility_cpd[\n", "#         [\n", "#             \"facility_id\",\n", "#             \"item_id\",\n", "#             \"master_assortment_substate_id\",\n", "#             \"rank\",\n", "#             \"is_darkstore\",\n", "#         ]\n", "#     ].drop_duplicates()\n", "\n", "# else:\n", "#     base_assortment_new_facility_cpd = pd.DataFrame()\n", "\n", "# base_assortment_new_facility_cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ranking of the base assortment "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select distinct wom.cloud_store_id as ds_outlet,\n", "dwc.date as delivery_date,\n", "dwc.item_id, \n", "dwc.consumption as final_cpd \n", "from \n", "(select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "date between current_date + interval 1 day\n", "and current_date + interval 1 week) dwc\n", "join \n", "retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join retail.console_outlet ro on ro.id=dwc.outlet_id\n", "where wom.cloud_store_id in %(outlet)s\n", "\"\"\"\n", "cpd1 = pd.read_sql_query(cpd_query, retail, params={\"outlet\": ds_outlets})\n", "cpd1.head(2)\n", "\n", "\n", "cpd_query = \"\"\"\n", "select outlet_id as ds_outlet,\n", "item_id,\n", "date as delivery_date,\n", "consumption as final_cpd\n", "from snorlax.date_wise_consumption where \n", "date between current_date + interval 1 day\n", "and current_date + interval 1 week\n", "and outlet_id in %(outlet)s\n", "\"\"\"\n", "cpd2 = pd.read_sql_query(cpd_query, retail, params={\"outlet\": ds_outlets})\n", "\n", "Total_cpd = pd.concat([cpd1, cpd2])\n", "\n", "Total_cpd = Total_cpd.groupby([\"ds_outlet\", \"item_id\"])[[\"final_cpd\"]].sum().reset_index()\n", "\n", "Total_cpd_outlet = Total_cpd.merge(\n", "    cater_facility_outlet[[\"facility_id\", \"ds_outlet\"]], on=[\"ds_outlet\"], how=\"left\"\n", ")\n", "Total_cpd_outlet.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_cpd_outlet[\"ds_outlet\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_cpd_outlet[Total_cpd_outlet[\"ds_outlet\"] == 1139]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_cpd_outlet_ds = Total_cpd_outlet.merge(is_ds, on=[\"ds_outlet\"], how=\"left\")\n", "Total_cpd_outlet_ds.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_cpd_outlet_ds = (\n", "    Total_cpd_outlet_ds.groupby([\"facility_id\", \"is_darkstore\", \"item_id\"])[[\"final_cpd\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "Total_cpd_outlet_ds.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_cpd_outlet_ds[\"facility_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_assortment_rank = base_assortment.merge(\n", "    Total_cpd_outlet_ds, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "base_assortment_rank = base_assortment_rank.dropna()\n", "base_assortment_rank.sort_values(\n", "    by=[\"facility_id\", \"final_cpd\", \"is_darkstore\"], ascending=False, inplace=True\n", ")\n", "base_assortment_rank = base_assortment_rank.drop_duplicates()\n", "\n", "base_assortment_rank[\"rank\"] = (\n", "    base_assortment_rank.groupby([\"facility_id\", \"is_darkstore\"]).cumcount() + 1\n", ")\n", "\n", "final_base_assortment = base_assortment_rank[\n", "    [\"facility_id\", \"item_id\", \"master_assortment_substate_id\", \"rank\", \"is_darkstore\"]\n", "].drop_duplicates()\n", "\n", "final_base_assortment = final_base_assortment.drop_duplicates()\n", "final_base_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_base_assortment.groupby([\"facility_id\", \"is_darkstore\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### OLD Assortment facility wise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT s.facility_id,\n", "s.item_id,\n", "s.master_assortment_substate_id,\n", "s.rank,\n", "s.is_darkstore,\n", "s.base_updated_at\n", "FROM metrics.dark_store_base_assortment s\n", "INNER JOIN\n", "  (SELECT facility_id,\n", "  is_darkstore,\n", "          max(base_updated_at) AS updated_at,\n", "          max(refresh_at) as refresh_at\n", "   FROM metrics.dark_store_base_assortment\n", "   GROUP BY 1,2)a ON s.facility_id=a.facility_id and s.is_darkstore=a.is_darkstore\n", "AND s.base_updated_at=a.updated_at and s.refresh_at=a.refresh_at \n", "\"\"\"\n", "\n", "old_assortment = pd.read_sql(query, redshift)\n", "old_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_assortment.groupby([\"facility_id\", \"is_darkstore\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_assortment[\"facility_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Refresh Facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_facility = old_assortment[[\"facility_id\"]].drop_duplicates()\n", "\n", "if TODAY.weekday() == 4 and refresh_flag == \"yes\":\n", "    old_facility[\"is_refresh\"] = True\n", "else:\n", "    old_facility[\"is_refresh\"] = False\n", "\n", "new_facility = final_base_assortment[[\"facility_id\"]].drop_duplicates()\n", "\n", "refresh_facility = new_facility.merge(old_facility, on=[\"facility_id\"], how=\"outer\")\n", "refresh_facility[\"is_refresh\"] = refresh_facility[\"is_refresh\"].fillna(True)\n", "refresh_facility = refresh_facility[refresh_facility[\"is_refresh\"] == True]\n", "\n", "facility_refresh = list(refresh_facility[\"facility_id\"].unique())\n", "facility_refresh"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### New facility Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(facility_refresh) > 0:\n", "    new_facility_assortment = final_base_assortment[\n", "        final_base_assortment[\"facility_id\"].isin(facility_refresh)\n", "    ]\n", "\n", "    new_facility_assortment[\"base_updated_at\"] = run_id\n", "\n", "    new_facility_assortment.head()\n", "\n", "else:\n", "    new_facility_assortment = pd.DataFrame()\n", "\n", "new_facility_assortment.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment to Refresh"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if TODAY.weekday() == 4 and refresh_flag == \"yes\":\n", "    refresh_assortment = new_facility_assortment.copy()\n", "\n", "else:\n", "    refresh_assortment = pd.concat([old_assortment, new_facility_assortment])\n", "\n", "refresh_assortment = refresh_assortment.drop_duplicates()\n", "refresh_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["refresh_assortment.groupby([\"facility_id\", \"is_darkstore\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Base assortment flat table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "\n", "run_id = datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "\n", "if TODAY.weekday() == 4 and refresh_flag == \"yes\":\n", "    refresh_assortment[\"base_updated_at\"] = datetime.today() + timedelta(hours=5.5)\n", "else:\n", "    pass\n", "\n", "refresh_assortment[\"refresh_at\"] = run_id\n", "refresh_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_store_base_assortment\",\n", "    \"column_dtypes\": redshift_schema(refresh_assortment),\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"facility_id\"],\n", "    \"incremental_key\": [\"refresh_at\"],\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(refresh_assortment, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Frontend-Backend mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cater_facility = list(cater_facility_outlet[\"facility_id\"].unique())\n", "\n", "cater_facility_outlet[\"facility_id\"] = cater_facility_outlet[\"facility_id\"].astype(\"int\")\n", "\n", "cater_facility_outlet_ds = cater_facility_outlet.merge(is_ds, on=[\"ds_outlet\"], how=\"left\")\n", "cater_facility_outlet_ds[[\"facility_id\", \"is_darkstore\"]] = cater_facility_outlet_ds[\n", "    [\"facility_id\", \"is_darkstore\"]\n", "].astype(\"int\")\n", "\n", "refresh_assortment[[\"facility_id\", \"is_darkstore\"]] = refresh_assortment[\n", "    [\"facility_id\", \"is_darkstore\"]\n", "].astype(\"int\")\n", "refresh_assortment_ds = refresh_assortment.merge(\n", "    cater_facility_outlet_ds[[\"facility_id\", \"ds_outlet\", \"is_darkstore\"]],\n", "    on=[\"facility_id\", \"is_darkstore\"],\n", "    how=\"left\",\n", ")\n", "\n", "refresh_assortment_ds = refresh_assortment_ds.drop_duplicates()\n", "\n", "refresh_assortment_ds = refresh_assortment_ds[\n", "    refresh_assortment_ds[\"facility_id\"].isin(cater_facility)\n", "]\n", "\n", "refresh_assortment_ds.drop([\"refresh_at\"], axis=1, inplace=True)\n", "refresh_assortment_ds.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment count of frontend (manual input in the google sheet)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_count = pb.from_sheets(sheet_id, \"Mapping\", clear_cache=True)\n", "assortment_count = assortment_count[[\"dark_store\", \"assortment_size\"]].drop_duplicates()\n", "assortment_count.rename(columns={\"dark_store\": \"ds_outlet\"}, inplace=True)\n", "assortment_count[[\"ds_outlet\", \"assortment_size\"]] = assortment_count[\n", "    [\"ds_outlet\", \"assortment_size\"]\n", "].astype(\"int\")\n", "assortment_count.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FnV assortment (taken from DS input google sheet)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_fnv = \"1Lb5meGTKr4cu0NS75BMrKuPep3itx0zjvU9qLA17J-g\"\n", "items_fnv = pb.from_sheets(sheet_fnv, \"for_assortment\")\n", "items_fnv = items_fnv[items_fnv[\"manual_bump\"] != \"0\"]\n", "items_fnv = items_fnv[items_fnv[\"type_flag\"] == \"FnV\"]\n", "items_fnv = items_fnv[[\"outlet_id\", \"item_id\", \"type_flag\"]].drop_duplicates()\n", "items_fnv.rename(columns={\"outlet_id\": \"ds_outlet\"}, inplace=True)\n", "items_fnv[\"replenishment_flag\"] = 0\n", "\n", "items_fnv[\"ds_outlet\"] = items_fnv[\"ds_outlet\"].astype(\"int\")\n", "items_fnv.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Perishables assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_perishables = \"1aw-EVLAjTwGUfIlIf96_csul5oDdY6YnD0WQoB79jfQ\"\n", "items_perishables = pb.from_sheets(sheet_perishables, \"Sheet1\")\n", "items_perishables = items_perishables[[\"outlet_id\", \"Item ID\"]].drop_duplicates()\n", "items_perishables.rename(columns={\"Item ID\": \"item_id\"}, inplace=True)\n", "items_perishables[\"type_flag\"] = \"Perishable\"\n", "items_perishables.rename(columns={\"outlet_id\": \"ds_outlet\"}, inplace=True)\n", "items_perishables[\"replenishment_flag\"] = 0\n", "items_perishables = items_perishables[items_perishables[\"ds_outlet\"] != \"\"]\n", "items_perishables[\"ds_outlet\"] = items_perishables[\"ds_outlet\"].astype(\"int\")\n", "\n", "items = pd.concat([items_fnv, items_perishables])\n", "items = items.drop_duplicates()\n", "\n", "items_facility = items.merge(\n", "    cater_facility_outlet[[\"facility_id\", \"ds_outlet\"]],\n", "    on=[\"ds_outlet\"],\n", "    how=\"inner\",\n", ")\n", "items_facility = items_facility.drop_duplicates()\n", "items_facility.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Changing the datatypes "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_facility[[\"facility_id\", \"ds_outlet\", \"item_id\"]] = items_facility[\n", "    [\"facility_id\", \"ds_outlet\", \"item_id\"]\n", "].astype(\"int\")\n", "refresh_assortment_ds = refresh_assortment_ds.dropna()\n", "refresh_assortment_ds[[\"facility_id\", \"ds_outlet\", \"item_id\"]] = refresh_assortment_ds[\n", "    [\"facility_id\", \"ds_outlet\", \"item_id\"]\n", "].astype(\"int\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Items included in the assortment (from input sheet)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["extra_items = pb.from_sheets(sheet_id, \"assortment_inclusions\")\n", "extra_items_ds = extra_items[[\"is_ds\", \"item_id\"]].drop_duplicates()\n", "\n", "extra_items_ds = extra_items_ds.replace(r\"^\\s*$\", np.nan, regex=True)\n", "extra_items_ds = extra_items_ds.dropna()\n", "\n", "extra_items_ds[\"is_ds\"] = extra_items_ds[\"is_ds\"].astype(\"int\")\n", "\n", "extra_items_dsv2 = extra_items_ds.merge(\n", "    is_ds, left_on=[\"is_ds\"], right_on=[\"is_darkstore\"], how=\"left\"\n", ")\n", "extra_items_dsv2.drop([\"is_darkstore\", \"is_ds\"], axis=1, inplace=True)\n", "extra_items_dsv2 = extra_items_dsv2.dropna()\n", "extra_items_dsv2[[\"ds_outlet\"]] = extra_items_dsv2[[\"ds_outlet\"]].astype(\"int\")\n", "\n", "extra_items_outlet = extra_items[[\"outlet_id\", \"item_outlet\"]].drop_duplicates()\n", "extra_items_outlet.rename(\n", "    columns={\"outlet_id\": \"ds_outlet\", \"item_outlet\": \"item_id\"}, inplace=True\n", ")\n", "extra_items_outlet[[\"ds_outlet\"]] = extra_items_outlet[[\"ds_outlet\"]].astype(\"int\")\n", "\n", "extra_items_included = pd.concat([extra_items_dsv2, extra_items_outlet])\n", "extra_items_included = extra_items_included.drop_duplicates()\n", "\n", "extra_items_ds_facility = extra_items_included.merge(\n", "    cater_facility_outlet[[\"facility_id\", \"ds_outlet\"]],\n", "    on=[\"ds_outlet\"],\n", "    how=\"inner\",\n", ")\n", "\n", "extra_items_ds_facility[\"replenishment_flag\"] = 1\n", "extra_items_ds_facility[\"type_flag\"] = \"Grocery\"\n", "extra_items_ds_facility = extra_items_ds_facility.drop_duplicates()\n", "extra_items_ds_facility[\"extra_flag\"] = 1\n", "extra_items_ds_facility.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment-Category mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1 = refresh_assortment_ds.merge(item_product_mapping, on=[\"item_id\"], how=\"left\")\n", "assortmentv1 = assortmentv1.drop_duplicates()\n", "assortmentv1 = (\n", "    assortmentv1.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"master_assortment_substate_id\",\n", "            \"rank\",\n", "            \"is_darkstore\",\n", "            \"ds_outlet\",\n", "        ]\n", "    )\n", "    .max()\n", "    .reset_index()\n", ")\n", "assortmentv1.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Identify Dark and franchise stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["is_ds[\"ds_outlet\"] = is_ds[\"ds_outlet\"].astype(\"int\")\n", "assortmentv1 = assortmentv1.merge(is_ds, on=[\"ds_outlet\", \"is_darkstore\"], how=\"left\")\n", "assortmentv1 = assortmentv1.drop_duplicates()\n", "assortmentv1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1.groupby([\"ds_outlet\", \"is_darkstore\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1[\"l0\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### L0 Cat exclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l0_cat = pb.from_sheets(sheet_id, \"assortment_exclusions\")\n", "l0_cat = l0_cat[[\"ds_l0\", \"l0\"]].drop_duplicates()\n", "l0_cat = l0_cat.replace(r\"\", np.NaN)\n", "l0_cat = l0_cat.dropna()\n", "l0_cat[\"ds_l0\"] = l0_cat[\"ds_l0\"].astype(\"int\")\n", "l0_cat[\"l0\"] = l0_cat[\"l0\"].astype(\"str\")\n", "l0_cat[\"l0_ex\"] = 1\n", "l0_cat.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1[\"is_darkstore\"] = assortmentv1[\"is_darkstore\"].astype(\"int\")\n", "assortmentv1_l0_ex = assortmentv1.merge(\n", "    l0_cat, left_on=[\"is_darkstore\", \"l0\"], right_on=[\"ds_l0\", \"l0\"], how=\"left\"\n", ")\n", "assortmentv1_l0_ex[\"l0_ex\"] = assortmentv1_l0_ex[\"l0_ex\"].fillna(0)\n", "assortmentv1_l0_ex = assortmentv1_l0_ex[assortmentv1_l0_ex[\"l0_ex\"] == 0]\n", "assortmentv1_l0_ex.drop([\"ds_l0\", \"l0_ex\"], axis=1, inplace=True)\n", "assortmentv1_l0_ex.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_ex.groupby([\"ds_outlet\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_ex[\"l0\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### L1 Cat exclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l1_cat = pb.from_sheets(sheet_id, \"assortment_exclusions\")\n", "l1_cat = l1_cat[[\"ds_l1\", \"l1\"]].drop_duplicates()\n", "l1_cat = l1_cat.replace(r\"\", np.NaN)\n", "l1_cat = l1_cat.dropna()\n", "l1_cat[\"ds_l1\"] = l1_cat[\"ds_l1\"].astype(\"int\")\n", "l1_cat[\"l1\"] = l1_cat[\"l1\"].astype(\"str\")\n", "l1_cat[\"l1_ex\"] = 1\n", "l1_cat"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_ex[\"l1\"] = assortmentv1_l0_ex[\"l1\"].astype(\"str\")\n", "l1_cat[\"l1\"] = l1_cat[\"l1\"].astype(\"str\")\n", "assortmentv1_l0_l1 = assortmentv1_l0_ex.merge(\n", "    l1_cat, left_on=[\"is_darkstore\", \"l1\"], right_on=[\"ds_l1\", \"l1\"], how=\"left\"\n", ")\n", "assortmentv1_l0_l1[\"l1_ex\"] = assortmentv1_l0_l1[\"l1_ex\"].fillna(0)\n", "assortmentv1_l0_l1 = assortmentv1_l0_l1[assortmentv1_l0_l1[\"l1_ex\"] == 0]\n", "assortmentv1_l0_l1.drop([\"ds_l1\", \"l1_ex\"], axis=1, inplace=True)\n", "assortmentv1_l0_l1.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_l1.groupby([\"ds_outlet\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### L2 Cat exclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l2_cat = pb.from_sheets(sheet_id, \"assortment_exclusions\")\n", "l2_cat = l2_cat[[\"ds_l2\", \"l2\"]].drop_duplicates()\n", "l2_cat = l2_cat.replace(r\"\", np.NaN)\n", "l2_cat = l2_cat.dropna()\n", "l2_cat[\"ds_l2\"] = l2_cat[\"ds_l2\"].astype(\"int\")\n", "l2_cat[\"l2\"] = l2_cat[\"l2\"].astype(\"str\")\n", "l2_cat[\"l2_ex\"] = 1\n", "l2_cat.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_l1_l2 = assortmentv1_l0_l1.merge(\n", "    l2_cat, left_on=[\"is_darkstore\", \"l2\"], right_on=[\"ds_l2\", \"l2\"], how=\"left\"\n", ")\n", "assortmentv1_l0_l1_l2[\"l2_ex\"] = assortmentv1_l0_l1_l2[\"l2_ex\"].fillna(0)\n", "assortmentv1_l0_l1_l2 = assortmentv1_l0_l1_l2[assortmentv1_l0_l1_l2[\"l2_ex\"] == 0]\n", "assortmentv1_l0_l1_l2.drop([\"ds_l2\", \"l2_ex\"], axis=1, inplace=True)\n", "assortmentv1_l0_l1_l2.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_l1_l2.groupby([\"ds_outlet\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Ptypes exclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ptypes = pb.from_sheets(sheet_id, \"assortment_exclusions\")\n", "ptypes = ptypes[[\"ds_ptype\", \"product_type\"]].drop_duplicates()\n", "ptypes = ptypes.replace(r\"\", np.NaN)\n", "ptypes = ptypes.dropna()\n", "ptypes[\"ds_ptype\"] = ptypes[\"ds_ptype\"].astype(\"int\")\n", "ptypes[\"product_type\"] = ptypes[\"product_type\"].astype(\"str\")\n", "ptypes[\"ds_ptype_ex\"] = 1\n", "ptypes.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_l1_l2_p = assortmentv1_l0_l1_l2.merge(\n", "    ptypes,\n", "    left_on=[\"is_darkstore\", \"product_type\"],\n", "    right_on=[\"ds_ptype\", \"product_type\"],\n", "    how=\"left\",\n", ")\n", "assortmentv1_l0_l1_l2_p[\"ds_ptype_ex\"] = assortmentv1_l0_l1_l2_p[\"ds_ptype_ex\"].fillna(0)\n", "assortmentv1_l0_l1_l2_p = assortmentv1_l0_l1_l2_p[assortmentv1_l0_l1_l2_p[\"ds_ptype_ex\"] == 0]\n", "assortmentv1_l0_l1_l2_p.drop([\"ds_ptype\", \"ds_ptype_ex\"], axis=1, inplace=True)\n", "assortmentv1_l0_l1_l2_p.drop(\n", "    assortmentv1_l0_l1_l2_p[\n", "        (assortmentv1_l0_l1_l2_p[\"ds_outlet\"] == 846)\n", "        & (assortmentv1_l0_l1_l2_p[\"item_id\"] == 10005088)\n", "    ].index,\n", "    inplace=True,\n", ")\n", "assortmentv1_l0_l1_l2_p.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_l1_l2_p.groupby([\"ds_outlet\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Items exclusions (on outlet level)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_ex = pb.from_sheets(sheet_id, \"assortment_exclusions\")\n", "items_ex = items_ex[[\"ds_outlet\", \"item_id\"]].drop_duplicates()\n", "items_ex = items_ex.replace(r\"\", np.NaN)\n", "items_ex = items_ex.dropna()\n", "items_ex[[\"ds_outlet\", \"item_id\"]] = items_ex[[\"ds_outlet\", \"item_id\"]].astype(\"int\")\n", "items_ex[\"is_exclude\"] = 1\n", "\n", "assortmentv1_l0_l1_l2_p_item = assortmentv1_l0_l1_l2_p.merge(\n", "    items_ex, on=[\"ds_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "assortmentv1_l0_l1_l2_p_item[\"is_exclude\"] = assortmentv1_l0_l1_l2_p_item[\"is_exclude\"].fillna(0)\n", "assortmentv1_l0_l1_l2_p_item = assortmentv1_l0_l1_l2_p_item[\n", "    assortmentv1_l0_l1_l2_p_item[\"is_exclude\"] == 0\n", "]\n", "assortmentv1_l0_l1_l2_p_item.drop([\"is_exclude\"], axis=1, inplace=True)\n", "assortmentv1_l0_l1_l2_p_item.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv1_l0_l1_l2_p_item.groupby([\"ds_outlet\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Removing the left over perishables and FnV from assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_facility[\"direct_order\"] = 1\n", "assortment_peri = assortmentv1_l0_l1_l2_p_item.merge(\n", "    items_facility[[\"ds_outlet\", \"item_id\", \"direct_order\"]],\n", "    on=[\"ds_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "assortment_peri[\"direct_order\"] = assortment_peri[\"direct_order\"].fillna(0)\n", "assortment_peri = assortment_peri[assortment_peri[\"direct_order\"] == 0]\n", "assortment_peri = assortment_peri[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"ds_outlet\",\n", "        \"rank\",\n", "        \"base_updated_at\",\n", "        \"is_darkstore\",\n", "    ]\n", "].drop_duplicates()\n", "assortment_peri.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_peri.groupby([\"ds_outlet\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Removing extra items from assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["extra_items_ds_facility[[\"ds_outlet\", \"item_id\"]] = extra_items_ds_facility[\n", "    [\"ds_outlet\", \"item_id\"]\n", "].astype(\"int\")\n", "assortment_periv2 = assortment_peri.merge(\n", "    extra_items_ds_facility[[\"ds_outlet\", \"item_id\", \"extra_flag\"]],\n", "    on=[\"ds_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "assortment_periv2[\"extra_flag\"] = assortment_periv2[\"extra_flag\"].fillna(0)\n", "assortment_periv2 = assortment_periv2[assortment_periv2[\"extra_flag\"] == 0]\n", "assortment_periv2 = assortment_periv2.drop_duplicates()\n", "assortment_periv2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_periv2.groupby([\"ds_outlet\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ranking the skus"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv2 = assortment_periv2.copy()\n", "assortmentv2 = assortmentv2[\n", "    [\n", "        \"facility_id\",\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "        \"base_updated_at\",\n", "        \"is_darkstore\",\n", "    ]\n", "].drop_duplicates()\n", "assortmentv2.sort_values(by=[\"ds_outlet\", \"rank\"], inplace=True)\n", "\n", "\n", "assortmentv2[\"final_rank\"] = assortmentv2.groupby(\"ds_outlet\").cumcount() + 1\n", "assortmentv2 = assortmentv2.drop([\"rank\"], axis=1)\n", "assortmentv2 = assortmentv2.drop_duplicates()\n", "\n", "assortmentv2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Selecting the best assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment = pd.DataFrame()\n", "for outlets in ds_outlets:\n", "    df = assortmentv2[assortmentv2[\"ds_outlet\"] == int(outlets)]\n", "    df1 = assortment_count[assortment_count[\"ds_outlet\"] == int(outlets)]\n", "    sku_count = df1.iloc[0, 1]\n", "    df = df[df[\"final_rank\"] <= sku_count]\n", "    final_assortment = pd.concat([final_assortment, df])\n", "\n", "final_assortment[\"replenishment_flag\"] = 1\n", "final_assortment = final_assortment[\n", "    [\n", "        \"facility_id\",\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"replenishment_flag\",\n", "        \"final_rank\",\n", "    ]\n", "].drop_duplicates()\n", "final_assortment[\"type_flag\"] = \"Grocery\"\n", "final_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment.groupby([\"ds_outlet\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding extra items in the assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["extra_items_ds_facility[[\"facility_id\", \"ds_outlet\", \"item_id\"]] = extra_items_ds_facility[\n", "    [\"facility_id\", \"ds_outlet\", \"item_id\"]\n", "].astype(\"int\")\n", "\n", "extra_items_ds_facility_score = extra_items_ds_facility.merge(\n", "    refresh_assortment_ds,\n", "    on=[\"facility_id\", \"ds_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "extra_items_ds_facility_score = extra_items_ds_facility_score.drop_duplicates()\n", "extra_items_ds_facility_score.head()\n", "\n", "\n", "extra_items_ds_facility_score[\"master_assortment_substate_id\"] = extra_items_ds_facility_score[\n", "    \"master_assortment_substate_id\"\n", "].<PERSON>na(2)\n", "\n", "\n", "final_assortmentv3 = pd.concat([final_assortment, extra_items_ds_facility_score])\n", "final_assortmentv3 = final_assortmentv3.drop([\"extra_flag\"], axis=1)\n", "final_assortmentv3 = final_assortmentv3.drop_duplicates()\n", "final_assortmentv3.sort_values(by=[\"ds_outlet\", \"rank\"], inplace=True)\n", "final_assortmentv3[\"rank\"] = final_assortmentv3.groupby(\"ds_outlet\").cumcount() + 1\n", "final_assortmentv3 = final_assortmentv3.drop([\"final_rank\"], axis=1)\n", "final_assortmentv3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentv3.groupby([\"ds_outlet\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CPC Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"city_warehouse_input_sheet\"\n", "cpc_outlets = pb.from_sheets(sheet_id, sheet_name)\n", "cpc_outlets = cpc_outlets[[\"ds_outlet\"]].drop_duplicates()\n", "cpc_outlets = cpc_outlets[cpc_outlets[\"ds_outlet\"] != \"\"]\n", "cpc_outlets[\"ds_outlet\"] = cpc_outlets[\"ds_outlet\"].astype(\"int\")\n", "cpc_outlets_list = list(cpc_outlets[\"ds_outlet\"].unique())\n", "\n", "cpc_outlets_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentv3 = final_assortmentv3[~final_assortmentv3[\"ds_outlet\"].isin(cpc_outlets_list)]\n", "final_assortmentv3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "sheet_name = \"jhilmil_store_assortment\"\n", "jhilmil_assortment = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "jhilmil_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jhilmil_assortment[\"item_id\"] = jhilmil_assortment[\"item_id\"].astype(\"int\")\n", "jhilmil_assortment[\"facility_id\"] = jhilmil_assortment[\"facility_id\"].astype(\"int\")\n", "jhilmil_assortment_active = jhilmil_assortment.merge(\n", "    Total_items, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "jhilmil_assortment_active = jhilmil_assortment_active.fillna(0)\n", "jhilmil_assortment_active.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jhilmil_assortment_active[[\"facility_id\", \"ds_outlet\", \"item_id\"]] = jhilmil_assortment_active[\n", "    [\"facility_id\", \"ds_outlet\", \"item_id\"]\n", "].astype(\"int\")\n", "jhilmil_assortment_active_cpd = jhilmil_assortment_active.merge(\n", "    Total_cpd_outlet, on=[\"facility_id\", \"ds_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "jhilmil_assortment_active_cpd.sort_values(\n", "    by=[\"ds_outlet\", \"final_cpd\"], ascending=False, inplace=True\n", ")\n", "jhilmil_assortment_active_cpd = jhilmil_assortment_active_cpd.drop_duplicates()\n", "jhilmil_assortment_active_cpd[\"rank\"] = (\n", "    jhilmil_assortment_active_cpd.groupby(\"ds_outlet\").cumcount() + 1\n", ")\n", "jhilmil_assortment_active_cpd.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jhilmil_assortment_active_cpd.groupby([\"ds_outlet\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jhilmil_assortment_active_rank = jhilmil_assortment_active_cpd.copy()\n", "jhilmil_assortment_active_rank = jhilmil_assortment_active_rank[\n", "    [\n", "        \"ds_outlet\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"replenishment_flag\",\n", "        \"type_flag\",\n", "        \"is_darkstore\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "    ]\n", "].drop_duplicates()\n", "jhilmil_assortment_active_rank[\n", "    [\n", "        \"ds_outlet\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"replenishment_flag\",\n", "        \"is_darkstore\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "    ]\n", "] = jhilmil_assortment_active_rank[\n", "    [\n", "        \"ds_outlet\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"replenishment_flag\",\n", "        \"is_darkstore\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "    ]\n", "].astype(\n", "    \"int\"\n", ")\n", "\n", "jhilmil_assortment_active_rank[\"base_updated_at\"] = final_assortmentv3[[\"base_updated_at\"]].iloc[\n", "    0, 0\n", "]\n", "\n", "jhilmil_assortment_active_rank.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentv4 = pd.concat([final_assortmentv3, jhilmil_assortment_active_rank])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentv4.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Final Latest assortment including FNV and Perishables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentv5 = pd.concat([final_assortmentv4, items_facility])\n", "final_assortmentv5[\"master_assortment_substate_id\"] = final_assortmentv5[\n", "    \"master_assortment_substate_id\"\n", "].fillna(1)\n", "\n", "\n", "final_assortment_new = final_assortmentv5.merge(item_product_mapping, on=[\"item_id\"], how=\"left\")\n", "\n", "final_assortment_new = final_assortment_new.drop_duplicates()\n", "final_assortment_new = final_assortment_new[\n", "    [\n", "        \"ds_outlet\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"type_flag\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "        \"replenishment_flag\",\n", "    ]\n", "].drop_duplicates()\n", "final_assortment_new[\"rank\"] = final_assortment_new[\"rank\"].fillna(0)\n", "final_assortment_new = final_assortment_new.drop_duplicates()\n", "final_assortment_new = (\n", "    final_assortment_new.groupby([\"ds_outlet\", \"facility_id\", \"item_id\"]).max().reset_index()\n", ")\n", "final_assortment_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new.groupby([\"ds_outlet\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Outlet-Merchant mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_merchant = \"\"\"\n", "SELECT cms_store AS real_merchant_id,\n", "       outlet_id\n", "FROM retail.console_outlet_cms_store\n", "WHERE active=1\n", "\n", "\"\"\"\n", "outlet_merchant = pd.read_sql_query(outlet_merchant, retail)\n", "outlet_merchant.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Incorporating Notify me"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH VIEW AS\n", "  (SELECT at_date_ist,\n", "          user_id,\n", "          device_uuid,\n", "          properties__product_id,\n", "          traits__city_name,\n", "          traits__merchant_name,\n", "          traits__merchant_id\n", "   FROM spectrum.mobile_event_data\n", "   WHERE at_date_ist>=CURRENT_DATE-30\n", "     AND name='Notify Me Clicked'\n", "     AND app__version IN ('5.4.20',\n", "                          '5.4.21',\n", "                          '5.4.22',\n", "                          '5.4.23',\n", "                          '5.4.24',\n", "                          '5.4.25') ), mer_map AS\n", "  (SELECT virtual_merchant_id,\n", "          real_merchant_id\n", "   FROM lake_cms.gr_virtual_to_real_merchant_mapping\n", "   WHERE enabled_flag = 'true'),\n", "                                       p AS\n", "  (SELECT mp.real_merchant_id,\n", "          ipm.item_id,\n", "          count(DISTINCT device_uuid) AS dev,\n", "          row_number() OVER (PARTITION BY real_merchant_id\n", "                             ORDER BY count(DISTINCT device_uuid) DESC) AS rank_top_clicked\n", "   FROM VIEW\n", "   INNER JOIN lake_cms.gr_product gp ON view.properties__product_id=gp.id\n", "   INNER JOIN lake_rpc.item_product_mapping ipm ON ipm.product_id = properties__product_id\n", "   INNER JOIN mer_map mp ON mp.virtual_merchant_id = traits__merchant_id\n", "   GROUP BY 1,\n", "            2\n", "   ORDER BY dev DESC)\n", "SELECT *\n", "FROM p\n", "WHERE rank_top_clicked <= 30\n", "\"\"\"\n", "notify_me = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_v1 = pd.merge(\n", "    notify_me[[\"real_merchant_id\", \"item_id\"]],\n", "    outlet_merchant,\n", "    how=\"right\",\n", "    left_on=[\"real_merchant_id\"],\n", "    right_on=[\"real_merchant_id\"],\n", ")\n", "notify_me_v2 = notify_me_v1[notify_me_v1[\"outlet_id\"].isin(ds_outlets)]\n", "notify_me_v2 = notify_me_v2.dropna()\n", "notify_me_v2.rename(columns={\"outlet_id\": \"ds_outlet\"}, inplace=True)\n", "notify_me_v2[\"notify_me_flag\"] = 1\n", "notify_me_v2 = notify_me_v2[[\"ds_outlet\", \"item_id\", \"notify_me_flag\"]].drop_duplicates()\n", "notify_me_v2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new2 = final_assortment_new.merge(\n", "    notify_me_v2, on=[\"ds_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "final_assortment_new2[\"notify_me_flag\"] = final_assortment_new2[\"notify_me_flag\"].fillna(0)\n", "final_assortment_new2.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Incorporating searches in assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches = \"\"\"\n", "WITH SEARCH AS\n", "  (SELECT loc.at_date_ist,\n", "          lower(loc.properties__query)::varchar AS keyword,\n", "          loc.device_uuid AS user_id,\n", "          loc.traits__merchant_name AS merchant,\n", "          loc.traits__merchant_id --count(distinct loc.custom__user_id) as users\n", "\n", "   FROM spectrum.mobile_event_data loc\n", "   WHERE loc.at_date_ist >= CURRENT_DATE-1\n", "     AND loc.name = 'Search Results Viewed'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5), \n", "            atc AS\n", "  (SELECT loc.at_date_ist,\n", "          lower(loc.properties__search_input_keyword)::varchar AS keyword1,\n", "          lower(loc.properties__search_actual_keyword)::varchar AS keyword2,\n", "          loc.device_uuid AS user_id,\n", "          loc.traits__merchant_id -- count(distinct loc.custom__user_id) as users\n", "\n", "   FROM spectrum.mobile_event_data loc\n", "   WHERE loc.at_date_ist >= CURRENT_DATE-1\n", "     AND loc.name = 'Product Added'\n", "     AND loc.properties__page_name = 'Search List' --and loc.traits__merchant_id in ('29008','29108')\n", "\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5),\n", "                mer_map AS\n", "  (SELECT virtual_merchant_id,\n", "          real_merchant_id\n", "   FROM lake_cms.gr_virtual_to_real_merchant_mapping\n", "   WHERE enabled_flag = 'true')\n", "SELECT *,\n", "       (atc::float/SEARCH)*100 AS search_conv\n", "FROM\n", "  (SELECT s.at_date_ist,\n", "          merchant,\n", "          m.real_merchant_id,\n", "          s.keyword,\n", "          count(DISTINCT s.user_id) AS SEARCH,\n", "          count(DISTINCT a.user_id) AS atc, --atc * 100.0/search as search_conv,\n", " row_number() OVER (PARTITION BY merchant,\n", "                                 s.at_date_ist\n", "                    ORDER BY SEARCH DESC) AS rank_top_searched\n", "   FROM SEARCH s\n", "   LEFT JOIN atc a ON s.user_id=a.user_id\n", "   AND s.at_date_ist = a.at_date_ist\n", "   AND (s.keyword = a.keyword1\n", "        OR s.keyword = a.keyword2)\n", "   INNER JOIN mer_map m ON m.virtual_merchant_id = s.traits__merchant_id\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "WHERE rank_top_searched <=30\n", "  AND SEARCH>0\n", "\"\"\"\n", "searches_df = pd.read_sql_query(searches, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v1 = pd.merge(\n", "    searches_df[[\"real_merchant_id\", \"keyword\"]],\n", "    outlet_merchant,\n", "    how=\"left\",\n", "    left_on=[\"real_merchant_id\"],\n", "    right_on=[\"real_merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v1 = searches_df_v1.dropna()\n", "searches_df_v3 = searches_df_v1[searches_df_v1[\"outlet_id\"].isin(ds_outlets)]\n", "searches_df_v3[\"outlet_id\"] = searches_df_v3[\"outlet_id\"].astype(\"int\")\n", "searches_df_v3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwrds = list(searches_df_v3.keyword.unique())\n", "kwrds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = []\n", "df2 = []\n", "for keyword in kwrds:\n", "    li = []\n", "    for index, row in item_product_mapping.iterrows():\n", "        pot_matches = [\n", "            row[\"name\"],\n", "            row[\"l0\"],\n", "            row[\"l1\"],\n", "            row[\"l2\"],\n", "            row[\"brand\"],\n", "            row[\"manf\"],\n", "            row[\"product_type\"],\n", "        ]\n", "        pot_matches = [x for x in pot_matches if x is not None]\n", "        its = difflib.get_close_matches(keyword, pot_matches, cutoff=0.75)\n", "        if its is not None:\n", "            if len(its) > 0:\n", "                li.append(row[\"item_id\"])\n", "    if len(li) > 0:\n", "        df.append({\"keyword\": keyword, \"items\": li})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3 = pd.DataFrame.from_dict(df)\n", "df3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = item_product_mapping.copy()\n", "x = []\n", "for i in df3[\"items\"]:\n", "    for a in i:\n", "        x.append(a)\n", "searches = pd.DataFrame(x, columns=[\"item_id\"])\n", "searches = searches.drop_duplicates()\n", "searches.head()\n", "search_ptypes = searches.merge(\n", "    item_product_mapping[[\"item_id\", \"product_type\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "search_ptypes.drop([\"item_id\"], axis=1, inplace=True)\n", "search_ptypes = search_ptypes.drop_duplicates()\n", "search_ptypes = search_ptypes.dropna()\n", "search_ptypes[\"search_flag\"] = 1\n", "search_ptypes.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select distinct wom.cloud_store_id as ds_outlet,\n", "dwc.date as delivery_date,\n", "dwc.item_id, \n", "dwc.consumption as final_cpd \n", "from \n", "(select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "date between current_date + interval 1 day\n", "and current_date + interval 1 week) dwc\n", "join \n", "retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join retail.console_outlet ro on ro.id=dwc.outlet_id\n", "where wom.cloud_store_id in %(outlet)s\n", "\"\"\"\n", "cpd1 = pd.read_sql_query(cpd_query, retail, params={\"outlet\": ds_outlets})\n", "cpd1.head(2)\n", "\n", "\n", "cpd_query = \"\"\"\n", "select outlet_id as ds_outlet,\n", "item_id,\n", "date as delivery_date,\n", "consumption as final_cpd\n", "from snorlax.date_wise_consumption where \n", "date between current_date + interval 1 day\n", "and current_date + interval 1 week\n", "and outlet_id in %(outlet)s\n", "\"\"\"\n", "cpd2 = pd.read_sql_query(cpd_query, retail, params={\"outlet\": ds_outlets})\n", "\n", "final_cpd = pd.concat([cpd1, cpd2])\n", "final_cpd.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpd_search = final_cpd.groupby([\"ds_outlet\", \"item_id\"])[[\"final_cpd\"]].sum().reset_index()\n", "final_cpd_search_ptype = final_cpd_search.merge(\n", "    item_product_mapping[[\"item_id\", \"product_type\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "final_cpd_search_ptype = (\n", "    final_cpd_search_ptype.groupby([\"ds_outlet\", \"product_type\"])[[\"final_cpd\"]].sum().reset_index()\n", ")\n", "\n", "search_ptypes_top = search_ptypes.merge(final_cpd_search_ptype, on=[\"product_type\"], how=\"left\")\n", "search_ptypes_top_total = (\n", "    search_ptypes_top.groupby([\"ds_outlet\"])[[\"final_cpd\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(columns={\"final_cpd\": \"total_cpd\"})\n", ")\n", "search_ptypes_top = search_ptypes_top.merge(search_ptypes_top_total, on=[\"ds_outlet\"], how=\"left\")\n", "\n", "search_ptypes_top.sort_values(by=[\"ds_outlet\", \"final_cpd\"], ascending=False, inplace=True)\n", "search_ptypes_top[\"per_contri\"] = search_ptypes_top[\"final_cpd\"] / search_ptypes_top[\"total_cpd\"]\n", "search_ptypes_top[\"cumsum\"] = search_ptypes_top.groupby([\"ds_outlet\"])[[\"per_contri\"]].cumsum()\n", "search_ptypes_top = search_ptypes_top[search_ptypes_top[\"cumsum\"] <= 0.90]\n", "search_ptypes_top[\"is_top\"] = 1\n", "\n", "search_ptypes_top = search_ptypes_top[[\"ds_outlet\", \"product_type\", \"is_top\"]].drop_duplicates()\n", "search_ptypes_top.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding top items in search"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["search_items = search_ptypes_top.merge(\n", "    final_assortment_new2[[\"ds_outlet\", \"product_type\", \"item_id\"]],\n", "    on=[\"ds_outlet\", \"product_type\"],\n", "    how=\"left\",\n", ")\n", "search_items = search_items.merge(final_cpd_search, on=[\"ds_outlet\", \"item_id\"], how=\"left\")\n", "search_items = search_items.dropna()\n", "search_items_total = (\n", "    search_items.groupby([\"ds_outlet\", \"product_type\"])[[\"final_cpd\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(columns={\"final_cpd\": \"total_cpd\"})\n", ")\n", "search_items = search_items.merge(search_items_total, on=[\"ds_outlet\", \"product_type\"], how=\"left\")\n", "search_items.sort_values(\n", "    by=[\"ds_outlet\", \"product_type\", \"final_cpd\"], ascending=False, inplace=True\n", ")\n", "search_items[\"per_contri\"] = search_items[\"final_cpd\"] / search_items[\"total_cpd\"]\n", "search_items[\"cumsum\"] = search_items.groupby([\"ds_outlet\", \"product_type\"])[\n", "    [\"per_contri\"]\n", "].cumsum()\n", "search_items = search_items[search_items[\"cumsum\"] <= 0.80]\n", "search_items = search_items[[\"ds_outlet\", \"item_id\"]].drop_duplicates()\n", "search_items[\"search_flag\"] = 1\n", "search_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new3 = final_assortment_new2.merge(\n", "    search_items, on=[\"ds_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "final_assortment_new3[\"search_flag\"] = final_assortment_new3[\"search_flag\"].fillna(0)\n", "final_assortment_new3.rename(\n", "    columns={\"facility_id\": \"catering_facility\", \"outlet_id\": \"catering_outlet\"},\n", "    inplace=True,\n", ")\n", "final_assortment_new3 = final_assortment_new3.fillna(0)\n", "final_assortment_new3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding <PERSON><PERSON> Outlet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" select a.item_id ,\n", "          a.outlet_id as catering_outlet,\n", "          ro.facility_id as catering_facility,\n", "          a.quantity\n", "\n", "   FROM ims.ims_item_inventory as a\n", "   inner join retail.console_outlet ro on ro.id=a.outlet_id\n", "   where  a.outlet_id in %(outlet)s\n", "   group by 1,2,3\n", "   \"\"\"\n", "Total_inventory = pd.read_sql_query(query, retail, params={\"outlet\": catering_outlet})\n", "Total_inventory.sort_values(by=[\"catering_facility\", \"item_id\"], inplace=True)\n", "Total_inventory_max = (\n", "    Total_inventory.groupby([\"catering_facility\", \"item_id\"])[[\"quantity\"]].max().reset_index()\n", ")\n", "Total_inventory = Total_inventory.merge(\n", "    Total_inventory_max, on=[\"catering_facility\", \"item_id\", \"quantity\"], how=\"inner\"\n", ")\n", "Total_inventory = Total_inventory.dropna()\n", "\n", "\n", "Total_inventory = Total_inventory.groupby([\"catering_facility\", \"item_id\"]).max().reset_index()\n", "Total_inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new4 = final_assortment_new3.merge(\n", "    Total_inventory, on=[\"catering_facility\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "final_assortment_new4[[\"quantity\", \"catering_outlet\"]] = final_assortment_new4[\n", "    [\"quantity\", \"catering_outlet\"]\n", "].fillna(0)\n", "\n", "final_assortment_new4 = final_assortment_new4.drop([\"quantity\"], axis=1)\n", "final_assortment_new4.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orderable and unorderable items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT facility_id as catering_facility,\n", "       item_id,\n", "       unreliable,\n", "       unorderable\n", "FROM\n", "  (SELECT facility_id,\n", "          item_id,\n", "          max(unreliable) AS unreliable,\n", "          max(unorderable) AS unorderable\n", "   FROM\n", "     (SELECT DISTINCT pos.facility_id,\n", "                      a.item_id,\n", "                      CASE\n", "                          WHEN tag_type_id=9 THEN tag_value\n", "                      END AS unreliable,\n", "                      CASE\n", "                          WHEN tag_type_id=10 THEN tag_value\n", "                      END AS unorderable\n", "      FROM rpc.item_outlet_tag_mapping AS a\n", "      INNER JOIN retail.console_outlet AS pos ON a.outlet_id=pos.id\n", "      WHERE tag_type_id IN (9,\n", "                            10)\n", "        AND a.active=1\n", "        AND pos.active=1 )AS a\n", "   GROUP BY 1,\n", "            2\n", "   ORDER BY 1,\n", "            2) AS a\"\"\"\n", "\n", "items_unorderable = pd.read_sql_query(query, retail)\n", "\n", "items_unorderable.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new42 = final_assortment_new4.merge(\n", "    items_unorderable, on=[\"catering_facility\", \"item_id\"], how=\"left\"\n", ")\n", "final_assortment_new42 = final_assortment_new42.fillna(0)\n", "final_assortment_new42.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating Flat Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new42[\n", "    [\n", "        \"catering_facility\",\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "        \"replenishment_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"catering_outlet\",\n", "    ]\n", "] = final_assortment_new42[\n", "    [\n", "        \"catering_facility\",\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "        \"replenishment_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"catering_outlet\",\n", "    ]\n", "].astype(\n", "    \"int\"\n", ")\n", "\n", "final_assortment_new42 = final_assortment_new42[\n", "    [\n", "        \"ds_outlet\",\n", "        \"catering_facility\",\n", "        \"catering_outlet\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"type_flag\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "        \"replenishment_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"unreliable\",\n", "        \"unorderable\",\n", "    ]\n", "]\n", "final_assortment_new42.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_assortment_updated = refresh_assortment_ds[\n", "    [\"facility_id\", \"base_updated_at\"]\n", "].drop_duplicates()\n", "base_assortment_updated.rename(columns={\"facility_id\": \"catering_facility\"}, inplace=True)\n", "base_assortment_updated.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "final_assortment_new42[\"updated_at\"] = run_id\n", "final_assortment_new42 = final_assortment_new42.fillna(0)\n", "final_assortment_new42 = final_assortment_new42.drop_duplicates()\n", "final_assortment_new5 = final_assortment_new42.merge(\n", "    base_assortment_updated, on=[\"catering_facility\"], how=\"left\"\n", ")\n", "final_assortment_new5 = final_assortment_new5.groupby([\"ds_outlet\", \"item_id\"]).max().reset_index()\n", "final_assortment_new5.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new5.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new5 = final_assortment_new5[\n", "    [\n", "        \"ds_outlet\",\n", "        \"catering_facility\",\n", "        \"catering_outlet\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"type_flag\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "        \"replenishment_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"unreliable\",\n", "        \"unorderable\",\n", "        \"updated_at\",\n", "        \"base_updated_at\",\n", "    ]\n", "].drop_duplicates()\n", "final_assortment_new5.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new5.groupby([\"ds_outlet\", \"catering_facility\"])[\n", "    [\"item_id\"]\n", "].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding storage type to assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT item_id,\n", "          CASE\n", "              WHEN storage_type_id IN (1,\n", "                                       8) THEN 'Regular'\n", "              WHEN storage_type_id IN (2,\n", "                                       6) THEN 'Cold'\n", "              WHEN storage_type_id IN (4,\n", "                                       5) THEN 'Heavy'\n", "              WHEN storage_type_id IN (3,\n", "                                       7) THEN 'Frozen'\n", "          END AS storage_type\n", "   FROM\n", "     (SELECT item_id,\n", "             max(storage_type) AS storage_type_id\n", "      FROM lake_rpc.product_product\n", "      GROUP BY 1)x2\n", "\"\"\"\n", "\n", "storage = pd.read_sql(query, redshift)\n", "storage.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new6 = final_assortment_new5.merge(storage, on=[\"item_id\"], how=\"left\")\n", "final_assortment_new6.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding outlet name in assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT wom.warehouse_id as hot_outlet,\n", "       o.name AS hot_outlet_name,\n", "       wom.cloud_store_id as catering_outlet,\n", "       ro.name as catering_outlet_name\n", "FROM retail.warehouse_outlet_mapping wom\n", "INNER JOIN retail.console_outlet o ON o.id=wom.warehouse_id\n", "INNER JOIN retail.console_outlet ro ON ro.id=wom.cloud_store_id\n", "\"\"\"\n", "\n", "hot_outlet = pd.read_sql_query(query, retail)\n", "hot_outlet = hot_outlet.dropna()\n", "hot_outlet.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new7 = final_assortment_new6.merge(hot_outlet, on=[\"catering_outlet\"], how=\"left\")\n", "final_assortment_new7 = final_assortment_new7.fillna(0)\n", "final_assortment_new7[\"hot_outlet\"] = final_assortment_new7[\"hot_outlet\"].astype(\"int\")\n", "final_assortment_new7.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new7.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new7 = final_assortment_new7[\n", "    [\n", "        \"ds_outlet\",\n", "        \"catering_facility\",\n", "        \"catering_outlet\",\n", "        \"catering_outlet_name\",\n", "        \"hot_outlet\",\n", "        \"hot_outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"type_flag\",\n", "        \"storage_type\",\n", "        \"master_assortment_substate_id\",\n", "        \"rank\",\n", "        \"replenishment_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"unreliable\",\n", "        \"unorderable\",\n", "        \"updated_at\",\n", "        \"base_updated_at\",\n", "    ]\n", "].drop_duplicates()\n", "final_assortment_new7.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment_new7.groupby([\"ds_outlet\", \"catering_facility\"])[[\"item_id\"]].count().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_store_assortment_static\",\n", "    \"column_dtypes\": redshift_schema(final_assortment_new7),\n", "    \"primary_key\": [\n", "        \"catering_facility\",\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"product_type\",\n", "    ],\n", "    \"sortkey\": [\"ds_outlet\", \"rank\"],\n", "    \"incremental_key\": [\"updated_at\"],\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final_assortment_new7, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}