dag_name: static_assortment
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: himanshu
  slack_id: UMB7GB90T
path: povms/dark_stores/etl/static_assortment
paused: true
pool: povms_pool
project_name: dark_stores
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 0,5,6,7,9,10,11,12,13,14,15,16,17,18 * * *
  start_date: '2020-12-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 32
