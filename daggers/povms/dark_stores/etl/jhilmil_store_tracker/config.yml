dag_name: j<PERSON><PERSON>l_store_tracker
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: himanshu
  slack_id: UMB7GB90T
path: povms/dark_stores/etl/jhilmil_store_tracker
paused: true
pool: povms_pool
project_name: dark_stores
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: '*/15 * * * *'
  start_date: '2021-02-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 17
