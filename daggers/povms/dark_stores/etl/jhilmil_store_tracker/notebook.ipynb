{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "now = str(now)\n", "now"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "aur = pb.get_connection(\"aurora\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Input the parameters for tracker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"input_sheet\"\n", "input_parameters = pb.from_sheets(sheet_id, sheet_name)\n", "input_parameters[\n", "    [\"frontend_id\", \"backend_outlet\", \"backend_merchant\", \"front_end_outlet\"]\n", "] = input_parameters[\n", "    [\"frontend_id\", \"backend_outlet\", \"backend_merchant\", \"front_end_outlet\"]\n", "].astype(\n", "    \"int\"\n", ")\n", "input_parameters.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["front_end_id = list(input_parameters[\"frontend_id\"])\n", "backend_outlet = list(input_parameters[\"backend_outlet\"])\n", "combined_outlet_list = list(input_parameters[\"backend_outlet\"]) + list(\n", "    input_parameters[\"front_end_outlet\"]\n", ")\n", "front_end_id, backend_outlet, combined_outlet_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### IPO/SPO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH ancestor_orders AS\n", "  (SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "                  date(convert_timezone('Asia/Kolkata',a.install_ts)) AS checkout_date,\n", "                  b.name AS front_end,\n", "                  a.id AS ancestor,\n", "                --   a.merchant_id,\n", "                  a.current_status\n", "  FROM lake_oms_bifrost.oms_order a\n", "  INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "  WHERE DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') >=current_date-9\n", "     AND a.direction ='FORWARD'\n", "     AND a.current_status = 'DELIVERED'\n", "     AND a.type IN ('RetailForwardOrder',\n", "                    'InternalForwardOrder')\n", "     AND b.name LIKE '%%Jhilmil%%' ),\n", "     \n", "     quantity AS\n", "  (SELECT o.delivery_date,\n", "          o.checkout_date,\n", "          o.front_end,\n", "        iod.outlet,\n", "          o.ancestor,\n", "          so.id AS sub_order_id,\n", "          oa.product_id,\n", "          o.current_status,\n", "          so.current_status,\n", "          sum(oi.quantity) AS order_qty,\n", "         sum(oa.selling_price*oi.quantity) selling_price\n", "  FROM ancestor_orders o\n", "  INNER JOIN lake_oms_bifrost.oms_suborder so ON so.order_id=o.ancestor\n", "  INNER JOIN lake_oms_bifrost.oms_suborder_item oi ON so.id = oi.suborder_id\n", "  INNER JOIN lake_oms_bifrost.oms_order_item  oa ON oa.id=oi.order_item_id\n", "  inner join lake_ims.ims_order_details iod on iod.order_id=so.id\n", "  where so.current_status='AT_DELIVERY_CENTER'\n", "  GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,7,8,9)\n", "        \n", "SELECT \n", "delivery_date,count(distinct ancestor) ancestor_cnt,sum(selling_price) as gmv_total, sum( order_qty)/ count(distinct ancestor)*1.0 IPO_total,1.0*count( product_id)/ count(distinct ancestor) SPO_total,\n", "count(distinct case when outlet=1024 then ( sub_order_id) end ) as jhilmil_ord_cnt,\n", "sum(case when outlet=1024 then (selling_price) end ) as jhilmil_gmv,\n", "sum(case when outlet=1024 then  order_qty end)/ count(distinct case when outlet=1024 then  sub_order_id end) jhimlil_ipo,\n", "count(case when outlet=1024 then  product_id end )/ count(distinct case when outlet=1024 then  sub_order_id end) jhimlil_spo,\n", "count(distinct case when outlet=340 then ( sub_order_id) end ) as G4_ord_cnt,\n", "sum(case when outlet=340 then (selling_price) end ) as g4_gmv,\n", "case when count(distinct case when outlet=340 then  sub_order_id end)>0 then (sum(case when outlet=340 then  order_qty end)*1.00/ count(distinct case when outlet=340 then sub_order_id end))\n", "else 0 end G4_ipo,\n", "case when count(distinct case when outlet=340 then  sub_order_id end) >0 then (count(case when outlet=340 then  product_id end)*1.00/ count(distinct case when outlet=340 then  sub_order_id end))\n", "else 0 end  g4_spo\n", "FROM quantity \n", "group by 1\n", "order by 1 desc\n", "\n", "\n", "\n", "\"\"\"\n", "ipo = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"IPO/SPO\"\n", "pb.to_sheets(ipo, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item product mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\"\"\"\n", "\n", "item_product_mapping = pd.read_sql(query, redshift)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Total ancestor-suborders orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH ancestor_orders AS\n", "  (SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "                  date_ist(a.install_ts) AS checkout_date,\n", "                  b.name AS front_end,\n", "                  a.id AS ancestor,\n", "                  a.current_status\n", "  FROM bifrost.oms_order a\n", "  INNER JOIN bifrost.oms_merchant b ON a.merchant_id = b.id\n", "  WHERE DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') >=current_date-interval '9 days'\n", "     AND a.direction ='FORWARD'\n", "     AND a.current_status != 'CANCELLED'\n", "     AND a.type IN ('RetailForwardOrder',\n", "                    'InternalForwardOrder')\n", "     AND cast(a.shipment_id as integer) in %(front_end)s ),\n", "     \n", "     quantity AS\n", "  (SELECT o.delivery_date,\n", "          o.checkout_date,\n", "          o.front_end,\n", "          o.ancestor,\n", "          so.id AS sub_order_id,\n", "          oa.product_id,\n", "          o.current_status,\n", "          sum(oi.quantity) AS order_qty\n", "  FROM ancestor_orders o\n", "  INNER JOIN bifrost.oms_suborder so ON so.order_id=o.ancestor\n", "  INNER JOIN bifrost.oms_suborder_item oi ON so.id = oi.suborder_id\n", "  INNER JOIN bifrost.oms_order_item  oa ON oa.id=oi.order_item_id\n", "  GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,7)\n", "        \n", "SELECT *\n", "FROM quantity\n", "order by 3,2\"\"\"\n", "\n", "oms_items = pd.read_sql(query, aur, params={\"front_end\": tuple(front_end_id)})\n", "oms_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_items.groupby([\"checkout_date\", \"front_end\"])[[\"ancestor\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ancestor sub order mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" SELECT distinct \n", "                   o.outlet AS sub_order_outlet,\n", "                   o.order_id AS sub_order_id\n", "            FROM ims.ims_order_details o\n", "            INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "            WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\"))  >=DATE_ADD(CURRENT_DATE, INTERVAL -9 DAY)\n", "              AND status_id NOT IN (3,4,\n", "              \n", "                                    5,\n", "                                    6,\n", "                                    7,\n", "                                    8)\n", "                                    and o.outlet in %(outlet)s                \n", "     \"\"\"\n", "\n", "orders = pd.read_sql_query(query, retail, params={\"outlet\": tuple(combined_outlet_list)})\n", "orders[\"sub_order_id\"] = orders[\"sub_order_id\"].astype(\"int\")\n", "orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_items[\"sub_order_id\"] = oms_items[\"sub_order_id\"].astype(\"int\")\n", "orders[\"sub_order_id\"] = orders[\"sub_order_id\"].astype(\"int\")\n", "\n", "oms_suborders = oms_items.merge(orders, on=[\"sub_order_id\"], how=\"left\")\n", "oms_suborders = oms_suborders.dropna()\n", "oms_suborders.sort_values(by=[\"delivery_date\"], inplace=True)\n", "oms_suborders.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_orders = (\n", "    oms_suborders.groupby([\"checkout_date\", \"front_end\"])[[\"ancestor\"]]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"ancestor\": \"total_orders\"})\n", ")\n", "total_orders.tail(6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kundli_picked_orders_checkout = oms_suborders.copy()\n", "kundli_picked_orders_checkout = (\n", "    kundli_picked_orders_checkout.groupby([\"checkout_date\", \"front_end\", \"sub_order_outlet\"])[\n", "        [\"ancestor\"]\n", "    ]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"ancestor\": \"backend_picked_orders\"})\n", ")\n", "\n", "kundli_picked_orders_checkout = kundli_picked_orders_checkout[\n", "    kundli_picked_orders_checkout[\"sub_order_outlet\"].isin(backend_outlet)\n", "]\n", "kundli_picked_orders_checkout.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select o.id as sub_order_outlet,f.name\n", "from retail.console_outlet o\n", "inner join crates.facility f on o.facility_id=f.id\"\"\"\n", "\n", "facility = pd.read_sql_query(query, retail)\n", "facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_orders_summary = total_orders.merge(\n", "    kundli_picked_orders_checkout, on=[\"checkout_date\", \"front_end\"], how=\"left\"\n", ")\n", "# total_orders_summary = total_orders_summary.dropna()\n", "total_orders_summary[\"timestamp\"] = now\n", "total_orders_summary = total_orders_summary.merge(facility, on=[\"sub_order_outlet\"], how=\"left\")\n", "total_orders_summary.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Express orders sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"checkout_orders\"\n", "pb.to_sheets(total_orders_summary, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sub orders count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_suborders.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub_orders = (\n", "    oms_suborders.groupby([\"checkout_date\", \"front_end\", \"sub_order_outlet\"])[[\"sub_order_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"ancestor\": \"total_orders\"})\n", ")\n", "sub_orders.tail(6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sub_orders = sub_orders.merge(facility, on=[\"sub_order_outlet\"], how=\"left\")\n", "sub_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"sub_orders_details\"\n", "pb.to_sheets(sub_orders, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Order items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_suborders_items = oms_suborders.copy()\n", "oms_suborders_items = oms_suborders_items.dropna()\n", "oms_suborders_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_orders = (\n", "    oms_suborders_items.groupby([\"checkout_date\", \"front_end\", \"product_id\"])\n", "    .agg({\"ancestor\": \"nunique\", \"order_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "item_orders.sort_values(by=[\"ancestor\"], ascending=False, inplace=True)\n", "item_orders.rename(columns={\"ancestor\": \"total_orders\", \"order_qty\": \"total_qty\"}, inplace=True)\n", "item_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_items_kundli = oms_suborders_items[oms_suborders_items[\"sub_order_outlet\"].isin(backend_outlet)]\n", "item_orders_kundli = (\n", "    oms_items_kundli.groupby([\"checkout_date\", \"front_end\", \"product_id\"])\n", "    .agg({\"ancestor\": \"nunique\", \"order_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "item_orders_kundli.sort_values(by=[\"ancestor\"], ascending=False, inplace=True)\n", "item_orders_kundli.rename(\n", "    columns={\"ancestor\": \"kundli_orders\", \"order_qty\": \"kundli_qty\"}, inplace=True\n", ")\n", "item_orders_kundli.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_data = item_orders.merge(\n", "    item_orders_kundli, on=[\"checkout_date\", \"front_end\", \"product_id\"], how=\"inner\"\n", ")\n", "item_data.sort_values(by=[\"checkout_date\", \"total_orders\"], inplace=True)\n", "item_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_data = item_data.merge(\n", "    item_product_mapping[[\"product_id\", \"item_id\", \"name\"]],\n", "    on=[\"product_id\"],\n", "    how=\"left\",\n", ")\n", "item_data.sort_values(by=[\"checkout_date\", \"total_orders\"], ascending=[True, False], inplace=True)\n", "item_data = item_data[\n", "    [\n", "        \"checkout_date\",\n", "        \"front_end\",\n", "        \"product_id\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"total_orders\",\n", "        \"kundli_orders\",\n", "        \"total_qty\",\n", "        \"kundli_qty\",\n", "    ]\n", "].drop_duplicates()\n", "item_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_data.rename(\n", "    columns={\n", "        \"checkout_date\": \"checkout_date\",\n", "        \"total_orders\": \"total_item_orders\",\n", "        \"kundli_orders\": \"Backend_fulfilled_order\",\n", "        \"total_qty\": \"total_order_qty\",\n", "        \"kundli_qty\": \"Backend_fulfilled_qty\",\n", "    },\n", "    inplace=True,\n", ")\n", "item_data.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT s.ds_outlet,\n", "s.item_id   \n", "FROM metrics.dark_store_assortment_static s\n", "INNER JOIN\n", "  (SELECT ds_outlet,\n", "          max(updated_at) AS updated_at\n", "   FROM metrics.dark_store_assortment_static\n", "   GROUP BY 1)a ON a.ds_outlet=s.ds_outlet\n", "AND a.updated_at=s.updated_at\n", "and s.ds_outlet in %(outlet)s\n", "and s.replenishment_flag=1\n", "\"\"\"\n", "assortment = pd.read_sql(\n", "    query,\n", "    redshift,\n", "    params={\"outlet\": tuple(list(input_parameters[\"front_end_outlet\"]))},\n", ")\n", "assortment[\"assortment_flag\"] = 1\n", "assortment.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment.merge(\n", "    input_parameters[[\"front_end_outlet\", \"backend_outlet\"]],\n", "    left_on=[\"ds_outlet\"],\n", "    right_on=[\"front_end_outlet\"],\n", "    how=\"left\",\n", ")\n", "assortment = assortment.drop([\"front_end_outlet\"], axis=1)\n", "assortment.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding bundle pids in the assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offer_items = \"\"\"SELECT so.store_id AS backend_outlet,\n", "       so.offer_id,\n", "       o.field_val AS item_id,\n", "       offer.offer_reference_id,\n", "       offer.description,\n", "       product_id\n", "FROM offer_master.store_offer so\n", "INNER JOIN offer_master.offer_field o ON o.offer_id=so.offer_id\n", "INNER JOIN offer_master.offer_type_field of ON of.id=o.offer_type_field_id\n", "inner join offer_master.offer offer on offer.id=so.offer_id\n", "inner join rpc.item_product_mapping r on r.offer_id=offer.offer_reference_id\n", "WHERE so.store_id in %(outlet)s\n", "  AND so.is_offer_active=1\n", "  AND of.field_type LIKE '%%item_id%%'\"\"\"\n", "\n", "\n", "offer_items = pd.read_sql_query(offer_items, retail, params={\"outlet\": tuple(backend_outlet)})\n", "offer_items[\"is_offer\"] = 1\n", "offer_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment[\"item_id\"] = assortment[\"item_id\"].astype(\"int\")\n", "offer_items[\"item_id\"] = offer_items[\"item_id\"].astype(\"int\")\n", "\n", "assortment_offer = assortment.merge(offer_items, on=[\"backend_outlet\", \"item_id\"], how=\"outer\")\n", "assortment_offer = assortment_offer.fillna(0)\n", "assortment_offer.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offer_selected = (\n", "    assortment_offer.groupby([\"ds_outlet\", \"backend_outlet\", \"offer_id\"])\n", "    .agg({\"assortment_flag\": \"sum\", \"is_offer\": \"sum\"})\n", "    .reset_index()\n", ")\n", "offer_selected = offer_selected[offer_selected[\"assortment_flag\"] == offer_selected[\"is_offer\"]]\n", "offer_selected = offer_selected[[\"ds_outlet\", \"backend_outlet\", \"offer_id\"]].drop_duplicates()\n", "offer_selected[\"is_final\"] = 1\n", "offer_selected.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offer_selected2 = offer_selected.merge(offer_items, on=[\"backend_outlet\", \"offer_id\"], how=\"left\")\n", "offer_selected2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bundles = offer_selected2[\n", "    [\"ds_outlet\", \"backend_outlet\", \"product_id\", \"item_id\", \"description\"]\n", "].drop_duplicates()\n", "bundles.sort_values(by=[\"ds_outlet\", \"product_id\"], inplace=True)\n", "bundles.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"bundle/offer pid assortment\"\n", "pb.to_sheets(bundles, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offer_pids = offer_selected2[[\"ds_outlet\", \"backend_outlet\", \"product_id\"]].drop_duplicates()\n", "offer_pids[\"assortment_flag\"] = 1\n", "offer_pids.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offer_pids.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment.merge(\n", "    item_product_mapping[[\"item_id\", \"product_id\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "assortment = assortment[\n", "    [\"ds_outlet\", \"backend_outlet\", \"product_id\", \"assortment_flag\"]\n", "].drop_duplicates()\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment = pd.concat([assortment, offer_pids])\n", "final_assortment = final_assortment.drop_duplicates()\n", "final_assortment = final_assortment.merge(\n", "    input_parameters[[\"front_end_outlet\", \"front_end_name\"]],\n", "    left_on=[\"ds_outlet\"],\n", "    right_on=[\"front_end_outlet\"],\n", "    how=\"left\",\n", ")\n", "final_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_data = item_data.merge(\n", "    final_assortment[[\"front_end_name\", \"product_id\", \"assortment_flag\"]],\n", "    left_on=[\"front_end\", \"product_id\"],\n", "    right_on=[\"front_end_name\", \"product_id\"],\n", "    how=\"left\",\n", ")\n", "item_data[\"assortment_flag\"] = item_data[\"assortment_flag\"].fillna(0)\n", "item_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offer_name = offer_items[[\"product_id\", \"description\"]].drop_duplicates()\n", "offer_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_data2 = item_data.merge(offer_name, on=[\"product_id\"], how=\"left\")\n", "item_data2[\"name\"] = item_data2[\"name\"].fillna(0)\n", "item_data2[\"name\"] = np.where(\n", "    item_data2[\"name\"] == 0, item_data2[\"description\"], item_data2[\"name\"]\n", ")\n", "item_data2.drop([\"description\", \"front_end_name\"], axis=1, inplace=True)\n", "item_data2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"pid fulfilled from backend\"\n", "pb.to_sheets(item_data2, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orders effectd by pids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortment.rename(columns={\"front_end_name\": \"front_end\"}, inplace=True)\n", "orders_effected = oms_suborders.merge(\n", "    final_assortment[[\"front_end\", \"product_id\", \"assortment_flag\"]],\n", "    on=[\"front_end\", \"product_id\"],\n", "    how=\"left\",\n", ")\n", "orders_effected[\"assortment_flag\"] = orders_effected[\"assortment_flag\"].fillna(0)\n", "orders_effected.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_effected2 = orders_effected[\n", "    (orders_effected[\"assortment_flag\"] == 1)\n", "    & (orders_effected[\"sub_order_outlet\"].isin(backend_outlet))\n", "]\n", "orders_effected2 = orders_effected2[\n", "    [\"checkout_date\", \"sub_order_outlet\", \"product_id\", \"ancestor\"]\n", "].drop_duplicates()\n", "orders_effected2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_effected3 = (\n", "    orders_effected2.groupby([\"checkout_date\", \"sub_order_outlet\", \"ancestor\"])[[\"product_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"product_id\": \"count\"})\n", ")\n", "orders_effected3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"orders_effected\"\n", "pb.to_sheets(orders_effected3, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### default pids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["default = item_data2[item_data2[\"assortment_flag\"] == 1]\n", "default = default[\n", "    [\"checkout_date\", \"front_end\", \"product_id\", \"name\", \"Backend_fulfilled_order\"]\n", "].drop_duplicates()\n", "max_checkout_date = default[\"checkout_date\"].max()\n", "default = default[default[\"checkout_date\"] == max_checkout_date]\n", "default"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"default\"\n", "pb.to_sheets(default, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### common items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_picked_from_both = item_data2[\n", "    item_data2[\"total_item_orders\"] != item_data2[\"Backend_fulfilled_order\"]\n", "]\n", "item_picked_from_both.sort_values(by=[\"checkout_date\", \"total_item_orders\"], inplace=True)\n", "item_picked_from_both.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"common pid(picked from both frontend and backend)\"\n", "pb.to_sheets(item_picked_from_both, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mapped Pids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT merchant_id as backend_merchant,\n", "                product_id,\n", "                enabled_flag\n", "FROM grofers_db.gr_merchant_product_mapping\n", "WHERE merchant_id in %(merchant)s\n", "and inventory_limit>0 and enabled_flag=True\n", "order by 1\n", "\"\"\"\n", "\n", "kundli_mapped = pd.read_sql(\n", "    query, aur, params={\"merchant\": tuple(list(input_parameters[\"backend_merchant\"]))}\n", ")\n", "\n", "kundli_mapped.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"WITH mapping AS\n", "#   (WITH MERCHANT AS\n", "#      (SELECT DISTINCT M.id,\n", "#                       m.name,\n", "#                       m.gr_id,\n", "#                       m.enabled_flag,\n", "#                       L.NAME AS city\n", "#       FROM grofers_db.gr_merchant M\n", "#       INNER JOIN grofers_db.gr_merchant_additional_info MAI ON MAI.MERCHANT_ID = M.ID\n", "#       AND (MAI.MERCHANT_STORE_TYPE = 'grofers')\n", "#       INNER JOIN grofers_db.gr_locality L2 ON M.LOCALITY_ID = L2.ID\n", "#       AND (<PERSON><PERSON>NAME NOT ILIKE '%%GROCERY%%MART%%'\n", "#            AND <PERSON>.NAME NOT ILIKE '%%FRESHBURY%%'\n", "#            AND M.NAME NOT ILIKE '%%test%%'\n", "#            AND M.NAME NOT ILIKE '%%donation%%')\n", "#       INNER JOIN grofers_db.gr_locality L1 ON L2.PARENT_LOCALITY_ID = L1.ID\n", "#       INNER JOIN grofers_db.gr_locality L ON L1.PARENT_LOCALITY_ID = L.ID\n", "#       AND L.ID <> 2051 ) SELECT DISTINCT vmp.virtual_merchant_id::int AS frontend_id,\n", "#                                          m1.gr_id AS frontend_gr_id,\n", "#                                          m1.name AS frontend_name,\n", "#                                          m1.city AS frontend_city,\n", "#                                          vmp.real_merchant_id::int AS backend_id,\n", "#                                          m.gr_id AS backend_gr_id,\n", "#                                          m.name AS backend_name,\n", "#                                          m.city AS backend_city\n", "#    FROM grofers_db.gr_virtual_to_real_merchant_mapping vmp\n", "#    INNER JOIN merchant m ON m.id=vmp.real_merchant_id\n", "#    INNER JOIN merchant m1 ON m1.id=vmp.virtual_merchant_id\n", "#    AND m1.enabled_flag=TRUE\n", "#    AND vmp.enabled_flag=TRUE\n", "#    AND m.name NOT ILIKE '%%SBC%%'\n", "#    AND m.name NOT ILIKE '%%Haridwar%%'\n", "#    AND m.name NOT ILIKE '%%Service%%'\n", "#    ORDER BY m1.city)\n", "# SELECT DISTINCT mapping.backend_id as backend_merchant,\n", "#  mpm.product_id,\n", "#  p.name,\n", "#  p.unit,\n", "#  mpm.is_available\n", "# FROM grofers_db.gr_merchant_product_mapping mpm\n", "# INNER JOIN mapping ON mapping.backend_id=mpm.merchant_id\n", "# AND mpm.enabled_flag=TRUE\n", "# INNER JOIN grofers_db.gr_product p ON mpm.product_id=p.id\n", "# INNER JOIN grofers_db.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "# INNER JOIN grofers_db.gr_category CAT2 ON PCM.CATEGORY_ID = CAT2.ID\n", "# AND PCM.IS_PRIMARY=TRUE\n", "# INNER JOIN grofers_db.gr_category CAT1 ON CAT2.PARENT_CATEGORY_ID = CAT1.ID\n", "# INNER JOIN grofers_db.gr_category CAT ON CAT1.PARENT_CATEGORY_ID = CAT.ID\n", "# WHERE p.enabled_flag=TRUE\n", "#   AND mapping.backend_id IN %(merchant)s\n", "# ORDER BY 1 DESC\"\"\"\n", "\n", "# kundli_mapped = pd.read_sql(\n", "#     query, aur, params={\"merchant\": tuple(list(input_parameters[\"backend_merchant\"]))}\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kundli_mapped.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kundli_mapped = kundli_mapped.merge(\n", "    input_parameters[[\"backend_merchant\", \"front_end_outlet\"]],\n", "    on=[\"backend_merchant\"],\n", "    how=\"left\",\n", ")\n", "\n", "kundli_mapped.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kundli_mapped = kundli_mapped.merge(\n", "    final_assortment[[\"front_end_outlet\", \"product_id\", \"assortment_flag\"]],\n", "    on=[\"front_end_outlet\", \"product_id\"],\n", "    how=\"left\",\n", ")\n", "kundli_mapped[\"assortment_flag\"] = kundli_mapped[\"assortment_flag\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"active backend mapped pids\"\n", "pb.to_sheets(kundli_mapped, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> picked"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_suborders.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inventory available at jhilmil"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT a.outlet_id as ds_outlet, \n", "       a.item_id, \n", "       a.quantity as ds_quantity, \n", "       b.quantity AS ds_blocked_quantity \n", "FROM   (SELECT i.outlet_id,\n", "        o.tax_location_id as city_id, \n", "               item_id, \n", "               quantity \n", "        FROM   ims.ims_item_inventory i\n", "        left join retail.console_outlet o on o.id=i.outlet_id\n", "        WHERE  outlet_id IN %(outlet)s \n", "           \n", "               AND i.active = 1) a \n", "       LEFT JOIN (SELECT item_id, \n", "                         outlet_id, \n", "                         sum(quantity) AS quantity\n", "                  FROM   ims.ims_item_blocked_inventory \n", "                  WHERE  outlet_id IN %(outlet)s \n", "                   \n", "                         AND active = 1 \n", "                         AND blocked_type in (1)  -- blocked for sto and orders\n", "                 GROUP BY 1,2) b \n", "              ON a.item_id = b.item_id \n", "                 AND a.outlet_id = b.outlet_id \"\"\"\n", "\n", "\n", "DS_available = pd.read_sql_query(\n", "    query, retail, params={\"outlet\": tuple(list(input_parameters[\"front_end_outlet\"]))}\n", ")\n", "\n", "DS_available[\"ds_available_qty\"] = DS_available[\"ds_quantity\"] - DS_available[\"ds_blocked_quantity\"]\n", "DS_available[\"ds_available_qty\"] = np.where(\n", "    DS_available[\"ds_available_qty\"] < 0,\n", "    0,\n", "    DS_available[\"ds_available_qty\"],\n", ")\n", "DS_available.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT CASE\n", "           WHEN s.ds_outlet=846 THEN 845\n", "           ELSE s.ds_outlet\n", "       END AS ds_outlet,\n", "       s.item_id,\n", "       s.name,\n", "       s.l0,\n", "       s.l1,\n", "       s.l2,\n", "       s.brand,\n", "       s.manf,\n", "       s.product_type,\n", "       s.storage_type,\n", "       s.rank,\n", "       s.replenishment_flag\n", " \n", "FROM metrics.dark_store_assortment_static s\n", "INNER JOIN\n", "  (SELECT ds_outlet,\n", "          max(updated_at) AS updated_at\n", "   FROM metrics.dark_store_assortment_static\n", "   GROUP BY 1)a ON a.ds_outlet=s.ds_outlet\n", "AND a.updated_at=s.updated_at\n", "and s.ds_outlet IN %(outlet)s\n", "\"\"\"\n", "assortment = pd.read_sql(\n", "    query,\n", "    redshift,\n", "    params={\"outlet\": tuple(list(input_parameters[\"front_end_outlet\"]))},\n", ")\n", "assortment[\"assortment_flag\"] = 1\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment.merge(\n", "    item_product_mapping[[\"item_id\", \"product_id\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment[\n", "    [\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"product_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"storage_type\",\n", "        \"rank\",\n", "        \"replenishment_flag\",\n", "        \"assortment_flag\",\n", "    ]\n", "].drop_duplicates()\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"frontend assortment\"\n", "pb.to_sheets(assortment, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Jhilmil inventory summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = DS_available.merge(item_product_mapping, on=[\"item_id\"], how=\"left\")\n", "inventory = inventory.fillna(0)\n", "inventory.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = inventory[\n", "    [\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"product_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"ds_quantity\",\n", "        \"ds_blocked_quantity\",\n", "        \"ds_available_qty\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "inventory = inventory.merge(assortment[[\"item_id\", \"assortment_flag\"]], on=[\"item_id\"], how=\"left\")\n", "inventory[\"assortment_flag\"] = inventory[\"assortment_flag\"].fillna(0)\n", "inventory = inventory.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"Frontend store inventory\"\n", "pb.to_sheets(inventory, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Raw data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_suborders_items.head(2)\n", "oms_suborders_items2 = oms_suborders_items.merge(\n", "    item_product_mapping[[\"product_id\", \"item_id\", \"name\"]],\n", "    on=[\"product_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_items = oms_suborders_items2[\n", "    [\n", "        \"checkout_date\",\n", "        \"delivery_date\",\n", "        \"front_end\",\n", "        \"ancestor\",\n", "        \"sub_order_outlet\",\n", "        \"sub_order_id\",\n", "        \"product_id\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"order_qty\",\n", "    ]\n", "]\n", "\n", "oms_items.sort_values(\n", "    by=[\"checkout_date\", \"delivery_date\", \"ancestor\", \"sub_order_id\"], inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"raw\"\n", "pb.to_sheets(oms_items, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### View on created_at_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oms_suborders_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data3 = (\n", "    oms_suborders_items.groupby([\"checkout_date\", \"front_end\"])\n", "    .agg({\"ancestor\": \"nunique\", \"product_id\": \"nunique\", \"order_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "data3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"data3\"\n", "sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "pb.to_sheets(data3, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data4 = (\n", "    oms_suborders.groupby([\"checkout_date\", \"front_end\", \"sub_order_outlet\"])\n", "    .agg({\"product_id\": \"nunique\", \"order_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "data4.tail(12)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"data4\"\n", "sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "pb.to_sheets(data4, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PIDS mapped at kundli backend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"SELECT DISTINCT merchant_id,\n", "#                 product_id\n", "# FROM lake_cms.gr_merchant_product_mapping\n", "# WHERE merchant_id in %(mercahnt)s\n", "#   AND enabled_flag='true'\n", "#   AND is_available='true'\"\"\"\n", "\n", "# pids_backend = pd.read_sql(query, redshift,params={\"mercahnt\":tuple(list(input_parameters[\"backend_merchant\"]))})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pids_backend_name = pids_backend.merge(\n", "#     item_product_mapping[[\"product_id\", \"name\"]], on=[\"product_id\"], how=\"left\"\n", "# )\n", "# pids_backend_name2 = pids_backend_name.merge(\n", "#     offer_items[[\"product_id\", \"description\"]], on=[\"product_id\"], how=\"left\"\n", "# )\n", "# pids_backend_name2[\"name\"] = pids_backend_name2[\"name\"].fillna(0)\n", "# pids_backend_name2[\"name\"] = np.where(\n", "#     pids_backend_name2[\"name\"] == 0,\n", "#     pids_backend_name2[\"description\"],\n", "#     pids_backend_name2[\"name\"],\n", "# )\n", "\n", "# pids_backend_name2 = pids_backend_name2[\n", "#     [\"merchant_id\", \"product_id\", \"name\"]\n", "# ].drop_duplicates()\n", "# pids_backend_name3 = pids_backend_name2.merge(\n", "#     final_assortment, on=[\"product_id\"], how=\"left\"\n", "# )\n", "# pids_backend_name3[\"assortment_flag\"] = pids_backend_name3[\"assortment_flag\"].fillna(0)\n", "# pids_backend_name3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_name = \"kundli_backend_mapped_pids\"\n", "\n", "# pb.to_sheets(pids_backend_name3, sheet_id, sheet_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Order bifurcation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query = \"\"\"WITH ancestor_orders AS\n", "#   (SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "#                    date_ist(a.install_ts) AS checkout_date,\n", "#                    b.name AS front_end,\n", "#                    a.id AS ancestor,\n", "#                    so.id AS sub_order_id\n", "#    FROM bifrost.oms_order a\n", "#    INNER JOIN bifrost.oms_merchant b ON a.merchant_id = b.id\n", "#    INNER JOIN bifrost.oms_suborder so ON so.order_id=a.id\n", "#    WHERE DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') >= CURRENT_DATE- interval '4 day'\n", "#      AND a.direction ='FORWARD'\n", "#      AND a.current_status != 'CANCELLED'\n", "#      AND a.type IN ('RetailForwardOrder',\n", "#                     'InternalForwardOrder')\n", "#      AND b.name LIKE '%%Jhilmil%%' ),\n", "#      sub_order_outlet AS\n", "#   (SELECT DISTINCT wpom.order_id,\n", "#                    p.pick_list_id,\n", "#                    p.outlet_id\n", "#    FROM whl.warehouse_pick_list p\n", "#    INNER JOIN whl.warehouse_pick_list_order_mapping wpom ON wpom.pick_list_id=p.id\n", "#    WHERE date_ist(max_scheduled_time) >= CURRENT_DATE - interval '6 day'\n", "#      AND p.outlet_id IN (1024,\n", "#                          340) )\n", "# SELECT delivery_date,\n", "#        checkout_date,\n", "#        front_end,\n", "#        ancestor,\n", "#        a.sub_order_id,\n", "#        x1.sub_order_outlet,\n", "#        pick_list_id\n", "# FROM ancestor_orders a\n", "# INNER JOIN\n", "#   (SELECT DISTINCT o.outlet AS sub_order_outlet,\n", "#                    o.order_id AS sub_order_id\n", "#    FROM ims.ims_order_details o\n", "#    INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "#    WHERE date_ist(scheduled_at) >= CURRENT_DATE - interval '6 day'\n", "#      AND status_id NOT IN (3,\n", "#                            4,\n", "#                            5,\n", "#                            6,\n", "#                            7)\n", "#      AND o.outlet IN (340,\n", "#                       1024) ) x1 ON x1.sub_order_id::varchar=a.sub_order_id::varchar\n", "# LEFT JOIN sub_order_outlet so ON so.order_id::varchar=a.sub_order_id::varchar\n", "# ORDER BY 1,\n", "#          2,\n", "#          3,\n", "#          4,\n", "#          5\"\"\"\n", "\n", "# order_bifurcation = pd.read_sql(query, aur)\n", "# order_bifurcation.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id = \"1G3CKm7BQsoWbiF8y9B12i0L4_3FUAsfIUwbX-JYT3Mw\"\n", "# sheet_name = \"raw_data\"\n", "# pb.to_sheets(order_bifurcation, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}