{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import math\n", "import time\n", "import random\n", "import logging\n", "import warnings\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "from sklearn.externals import joblib\n", "from sklearn.metrics import mean_squared_error\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.model_selection import train_test_split\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims = pb.get_connection(\"ims\")\n", "retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = (datetime.today() + <PERSON><PERSON>ta(hours=5, minutes=30)).date()\n", "\n", "sdate = today + <PERSON><PERSON><PERSON>(1)\n", "edate = today + <PERSON><PERSON><PERSON>(8)\n", "\n", "days = pd.date_range(sdate, edate - timedelta(days=1), freq=\"d\")\n", "date = days.to_frame(index=False)\n", "date.columns = [\"delivery_date\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input = pb.from_sheets(\"1aw-EVLAjTwGUfIlIf96_csul5oDdY6YnD0WQoB79jfQ\", \"input\")\n", "sheet1 = pb.from_sheets(\"1aw-EVLAjTwGUfIlIf96_csul5oDdY6YnD0WQoB79jfQ\", \"Sheet1\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capping = list(filter(None, input.capping.values))\n", "capping = float(capping[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_id_list = set(filter(None, sheet1[\"Item ID\"].values))\n", "outlet_id_list = set(filter(None, input.outlet_id.values))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_id_list = \", \".join(item_id_list)\n", "outlet_id_list = \", \".join(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet1[\"Outward (Days)\"] = np.where(sheet1[\"Outward (Days)\"] == \"\", 0, sheet1[\"Outward (Days)\"])\n", "sheet1[\"Item ID\"] = np.where(sheet1[\"Item ID\"] == \"\", 0, sheet1[\"Item ID\"])\n", "sheet1[\"Outward (Days)\"] = sheet1[\"Outward (Days)\"].astype(int)\n", "sheet1[\"po_cycle\"] = np.where(\n", "    sheet1[\"PO Cycle\"] == \"Alternate days\",\n", "    3,\n", "    np.where(\n", "        sheet1[\"PO Cycle\"] == \"Biweekly\",\n", "        5,\n", "        np.where(sheet1[\"PO Cycle\"] == \"Weekly\", 7, 3),\n", "    ),\n", ")\n", "\n", "outward_days = list(set(filter(lambda x: x.item(), sheet1[\"Outward (Days)\"].values)))\n", "buckets = [(x + 1).item() for x in outward_days]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_outward_days = sheet1[[\"Outward (Days)\", \"Item ID\"]]\n", "item_outward_days.columns = [\"outward_days\", \"item_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets.sort()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle = sheet1[[\"Item ID\", \"po_cycle\"]]\n", "po_cycle.columns = [\"item_id\", \"po_cycle\"]\n", "po_cycle.item_id = po_cycle.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle = po_cycle.set_index(\"item_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlet_details_query = \"\"\"select c.item_id, c.outlet_id, c.outlet_name, c.facility_id, max(c.item_name) as item_name, max(c.inner_case_size) as inner_case_size, max(c.outer_case_size) as outer_case_size, max(c.weight_in_gm) as weight_in_gm, avg(lp.landing_price) as landing_price, 1 as tmp from\n", "(select a.*, b.outlet_id, outlet_name, facility_id\n", "from \n", "(\n", "(SELECT item_id, variant_id, name as item_name, inner_case_size, outer_case_size, weight_in_gm\n", "FROM rpc.product_product pp2\n", "WHERE item_id in ({item_id})) a\n", "\n", "CROSS JOIN\n", "\n", "(select id as outlet_id, name as outlet_name, facility_id\n", "from retail.console_outlet\n", "where id in ({outlet_id})) b\n", "\n", ")) c\n", "LEFT JOIN ims.ims_inventory_landing_price lp on c.variant_id = lp.variant_id and c.outlet_id = lp.outlet_id\n", "\n", "group by 1,2,3,4\n", "\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "itm_outlet_details = pd.read_sql_query(itm_outlet_details_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date[\"tmp\"] = 1\n", "\n", "itm_outlets_date = pd.merge(itm_outlet_details, date, on=\"tmp\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlets_date[\"item_code\"] = itm_outlets_date[\"item_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = po_cycle.to_dict()\n", "d1 = d[\"po_cycle\"]\n", "print(d1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlets_date.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_outlets_date = (\n", "    itm_outlets_date.groupby(\"item_id\")\n", "    .apply(lambda x: (x.nlargest(d1[x.name], \"item_id\")))\n", "    .reset_index(drop=True)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class LiquidationAgingInventory(object):\n", "    def __init__(self, day, buckets):\n", "        try:\n", "            self.date = day\n", "            self.buckets = buckets\n", "            self.csv_rows = []\n", "            self.fetch_outlets_and_compute()\n", "\n", "        except Exception as e:\n", "            print(e)\n", "\n", "    def fetch_outlets_and_compute(self):\n", "        outlets = self.get_outlets()\n", "        for row in outlets.itertuples():\n", "            outlet_id = int(row.outlet_id)\n", "            self.compute_aging(outlet_id)\n", "\n", "    @staticmethod\n", "    def daterange(start_date, end_date):\n", "        for n in range(int((end_date - start_date).days)):\n", "            yield start_date + timedelta(n)\n", "\n", "    def get_outlets(self):\n", "        query = \"\"\"select distinct outlet_id from retail.console_outlet_cms_store cocs \n", "        inner join retail.console_outlet co on co.id=cocs.outlet_id \n", "        where cocs.active=1 and co.active=1 and co.device_id <> 47 and co.id in ({outlet_id})\n", "        union select id from retail.console_outlet where id in (895,580,467);\"\"\".format(\n", "            outlet_id=outlet_id_list\n", "        )\n", "        rows = pd.read_sql_query(sql=query, con=retail)\n", "\n", "        return rows\n", "\n", "    def get_item_level_bucket_isd_quantities(\n", "        self, variant_datas, bucket_days, item_blocked_quantity\n", "    ):\n", "\n", "        bucket_isd_quantities = [0 for item in bucket_days]\n", "        for variant_data in variant_datas:\n", "            variant_bucket_isd_quantities = variant_data[\"bucket_isd_quantities\"]\n", "            for i in range(0, len(bucket_days)):\n", "                bucket_isd_quantities[i] += variant_bucket_isd_quantities[i]\n", "        #         if item_blocked_quantity > 0:\n", "        #             i = len(bucket_isd_quantities) - 1\n", "        #             while i > -1 and item_blocked_quantity > 0:\n", "        #                 bucket_inventory = bucket_isd_quantities[i]\n", "        #                 inventory = min(bucket_inventory, item_blocked_quantity)\n", "        #                 item_blocked_quantity -= inventory\n", "        #                 bucket_isd_quantities[i] -= inventory\n", "        #                 i -= 1\n", "        return bucket_isd_quantities\n", "\n", "    def get_item_variant_mrp(self, item_variants_data):\n", "\n", "        max_variant_quantity = 0\n", "        max_quantity_item_variant_mrp = float(\"-inf\")\n", "\n", "        item_variant_mrp_mapping = {}\n", "        for item_variant in item_variants_data:\n", "            if sum(item_variant.get(\"bucket_isd_quantities\")) > max_variant_quantity:\n", "                max_quantity_item_variant_mrp = item_variant.get(\"variant_mrp\")\n", "                max_variant_quantity = sum(item_variant.get(\"bucket_isd_quantities\"))\n", "        return max_quantity_item_variant_mrp\n", "\n", "    def generate_item_level_aggregate(self, outlet_id, bucket_days, blocked_item_inventory_map):\n", "\n", "        for item_id in self.outlet_item_inventory_map:\n", "            item_variants_data = self.outlet_item_inventory_map[item_id]\n", "            item_variant_mrp = self.get_item_variant_mrp(item_variants_data)\n", "            item_bucket_isd_quantities = self.get_item_level_bucket_isd_quantities(\n", "                item_variants_data,\n", "                bucket_days,\n", "                blocked_item_inventory_map.get(item_id, 0),\n", "            )\n", "            csv_row = [str(item_id), str(outlet_id), str(item_variant_mrp), self.date]\n", "            csv_row.extend(item_bucket_isd_quantities)\n", "            self.csv_rows.append(csv_row)\n", "        df = pd.DataFrame.from_records(self.csv_rows)\n", "        return df\n", "\n", "    def get_outlet_item_blocked_inventories_map(self, outlet_id, item_ids):\n", "\n", "        item_ids = \",\".join(item_ids)\n", "        query = \"\"\"select sum(quantity) quantity, item_id from ims.ims_item_blocked_inventory where outlet_id = {outlet_id} and item_id in ({item_ids}) and quantity>0 and blocked_type!=3 group by item_id;\"\"\"\n", "        query = query.format(outlet_id=outlet_id, item_ids=item_ids)\n", "        datas = pd.read_sql_query(sql=query, con=retail)\n", "        blocked_item_inventory_map = {}\n", "        for item_data in datas.itertuples():\n", "            blocked_item_inventory_map[item_data.item_id] = int(item_data.quantity)\n", "        return blocked_item_inventory_map\n", "\n", "    def add_variant_in_item_inventory_map(self, item_id, variant_data):\n", "        if item_id not in self.outlet_item_inventory_map:\n", "            self.outlet_item_inventory_map[item_id] = []\n", "        self.outlet_item_inventory_map[item_id].append(variant_data)\n", "\n", "    def get_isd_quantity_for_variant_with_buckets(\n", "        self,\n", "        variant_id,\n", "        variant_quantity,\n", "        bucket_days,\n", "        pending_putaway_inventory,\n", "        bucket_days_variant_quantity_map,\n", "    ):\n", "\n", "        try:\n", "            bucket_isd_quantities = [0 for item in bucket_days]\n", "            remaining_variant_quantity = variant_quantity\n", "\n", "            index = 0\n", "            while index < len(bucket_days):\n", "                if variant_id in bucket_days_variant_quantity_map[index]:\n", "                    bucket_quantity = bucket_days_variant_quantity_map[index][variant_id]\n", "                else:\n", "                    bucket_quantity = 0\n", "                if remaining_variant_quantity > bucket_quantity:\n", "                    bucket_isd_quantities[index] = bucket_quantity\n", "                else:\n", "                    bucket_isd_quantities[index] = remaining_variant_quantity\n", "                remaining_variant_quantity -= bucket_quantity\n", "                if remaining_variant_quantity <= 0:\n", "                    break\n", "\n", "                index += 1\n", "\n", "            if remaining_variant_quantity < 0:\n", "                remaining_variant_quantity = 0\n", "\n", "            bucket_isd_quantities.append(remaining_variant_quantity)\n", "            if pending_putaway_inventory > 0:\n", "                i = 0\n", "                while i < len(bucket_isd_quantities) and pending_putaway_inventory > 0:\n", "                    bucket_inventory = bucket_isd_quantities[i]\n", "                    inventory = min(bucket_inventory, pending_putaway_inventory)\n", "                    pending_putaway_inventory -= inventory\n", "                    bucket_isd_quantities[i] -= inventory\n", "                    i += 1\n", "            return bucket_isd_quantities\n", "        except Exception as e:\n", "            raise e\n", "\n", "    def get_in_clause_string_query(self, str_list):\n", "        query_value = \"\"\n", "        if str_list:\n", "            query_value = \"'\" + \"','\".join(str_list) + \"'\"\n", "        return query_value\n", "\n", "    def fetch_isd_quantity_for_variants(\n", "        self, outlet_id, variant_ids, start_date_time, end_date_time\n", "    ):\n", "        query = \"\"\"select COALESCE(sum(delta),0) as quantity, variant_id from ims.ims_inventory_stock_details isd inner join ims.ims_inward_invoice iii on isd.grn_id = iii.grn_id \n", "        where isd.outlet_id = {outlet_id} and isd.variant_id in ({variant_ids}) and iii.source_type in (1,2) and isd.created_at between \"{start_date_time}\" and \"{end_date_time}\" group by variant_id;\"\"\"\n", "        query = query.format(\n", "            outlet_id=outlet_id,\n", "            variant_ids=self.get_in_clause_string_query(variant_ids),\n", "            start_date_time=start_date_time,\n", "            end_date_time=end_date_time,\n", "        )\n", "        rows = pd.read_sql_query(sql=query, con=retail)\n", "        return rows\n", "\n", "    def get_isd_bucket_detail_for_variant_quantity_map(\n", "        self, outlet_id, variant_ids, start_date, end_date\n", "    ):\n", "        isd_bucket_variants = self.fetch_isd_quantity_for_variants(\n", "            outlet_id, variant_ids, start_date, end_date\n", "        )\n", "        variant_quantity_map = {}\n", "        for isd_bucket_variant in isd_bucket_variants.itertuples():\n", "            variant_quantity_map[isd_bucket_variant.variant_id] = int(isd_bucket_variant.quantity)\n", "        return variant_quantity_map\n", "\n", "    def get_isd_bucket_details_for_variants(self, outlet_id, variant_ids, bucket_days):\n", "        index = 0\n", "        bucket_days_variant_quantity_map = []\n", "\n", "        # As the inventory calculation have to made on date basis we changed the time stamp to \"YYYY-MM-DD 18:29:59\" .Previously it was on timestamp basis.\n", "\n", "        ref_date_time = datetime.strptime(\n", "            self.date.strftime(\"%Y-%m-%d 18:29:59\"),\n", "            \"%Y-%m-%d %H:%M:%S\",\n", "        )\n", "\n", "        end_date = ref_date_time\n", "        while index < len(bucket_days):\n", "            start_date = ref_date_time + timedelta(days=-bucket_days[index])\n", "            bucket_days_variant_quantity_map.append(\n", "                self.get_isd_bucket_detail_for_variant_quantity_map(\n", "                    outlet_id, variant_ids, start_date, end_date\n", "                )\n", "            )\n", "            end_date = start_date\n", "            index += 1\n", "        return bucket_days_variant_quantity_map\n", "\n", "    def fetch_outlet_pending_putaway_variants(self, outlet_id):\n", "\n", "        query = \"\"\"select variant_id, outlet_id, quantity from ims.ims_good_inventory where outlet_id = {outlet_id} and inventory_update_type_id = 28 and quantity > 0;\"\"\"\n", "        query = query.format(outlet_id=outlet_id)\n", "        active_variants = pd.read_sql_query(sql=query, con=retail)\n", "        pending_putaway_inventory_map = {}\n", "        for active_variant in active_variants.itertuples():\n", "            pending_putaway_inventory_map[active_variant.variant_id] = int(active_variant.quantity)\n", "        return pending_putaway_inventory_map\n", "\n", "    def fetch_outlet_variants(self, outlet_id):\n", "        query = \"\"\"select pp.item_id,pp.variant_mrp, ii.variant_id, ii.outlet_id, ii.quantity from \n", "        (select variant_id, outlet_id, sum(quantity) quantity from \n", "        (select variant_id, outlet_id, quantity from ims.ims_inventory where outlet_id = {outlet_id} and quantity > 0 and active = 1 \n", "        union all select variant_id, outlet_id, quantity from ims.ims_good_inventory where outlet_id = {outlet_id} and quantity > 0 and active = 1) inventory group by variant_id, outlet_id) \n", "        ii inner join rpc.product_product pp on pp.variant_id = ii.variant_id\n", "        where item_id in ({item_id});\"\"\"\n", "        query = query.format(outlet_id=outlet_id, item_id=item_id_list)\n", "        rows = pd.read_sql_query(sql=query, con=retail)\n", "        return rows\n", "\n", "    def compute_aging(self, outlet_id):\n", "        try:\n", "            self.outlet_item_inventory_map = {}\n", "            item_ids = set()\n", "            active_variants = self.fetch_outlet_variants(outlet_id)\n", "            if len(active_variants) == 0:\n", "                return\n", "            pending_putaway_inventory_map = self.fetch_outlet_pending_putaway_variants(outlet_id)\n", "            #             self.curr_date_time = day\n", "            bucket_days = self.buckets\n", "            variant_ids = [row.variant_id for row in active_variants.itertuples()]\n", "            bucket_days_variant_quantity_map = self.get_isd_bucket_details_for_variants(\n", "                outlet_id, variant_ids, bucket_days\n", "            )\n", "            for active_variant in active_variants.itertuples():\n", "                variant_id = active_variant.variant_id\n", "                variant_mrp = active_variant.variant_mrp\n", "                variant_quantity = int(active_variant.quantity)\n", "                pending_putaway_inventory = pending_putaway_inventory_map.get(variant_id, 0)\n", "\n", "                (bucket_isd_quantities) = self.get_isd_quantity_for_variant_with_buckets(\n", "                    variant_id,\n", "                    variant_quantity,\n", "                    bucket_days,\n", "                    pending_putaway_inventory,\n", "                    bucket_days_variant_quantity_map,\n", "                )\n", "                variant_data = {\n", "                    \"variant_id\": variant_id,\n", "                    \"variant_mrp\": variant_mrp,\n", "                    \"bucket_isd_quantities\": bucket_isd_quantities,\n", "                }\n", "                item_ids.add(str(active_variant.item_id))\n", "                self.add_variant_in_item_inventory_map(active_variant.item_id, variant_data)\n", "            blocked_item_inventory_map = self.get_outlet_item_blocked_inventories_map(\n", "                outlet_id, item_ids\n", "            )\n", "            a = self.generate_item_level_aggregate(\n", "                outlet_id, bucket_days, blocked_item_inventory_map\n", "            )\n", "            return a\n", "        except Exception as e:\n", "            print(e)\n", "\n", "\n", "# LiquidationAgingInventory()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x1 = LiquidationAgingInventory(date.delivery_date[0], buckets)\n", "x2 = LiquidationAgingInventory(date.delivery_date[1], buckets)\n", "x3 = LiquidationAgingInventory(date.delivery_date[2], buckets)\n", "x4 = LiquidationAgingInventory(date.delivery_date[3], buckets)\n", "x5 = LiquidationAgingInventory(date.delivery_date[4], buckets)\n", "x6 = LiquidationAgingInventory(date.delivery_date[5], buckets)\n", "x7 = LiquidationAgingInventory(date.delivery_date[6], buckets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data_1 = pd.DataFrame(x1.csv_rows)\n", "inv_data_2 = pd.DataFrame(x2.csv_rows)\n", "inv_data_3 = pd.DataFrame(x3.csv_rows)\n", "inv_data_4 = pd.DataFrame(x4.csv_rows)\n", "inv_data_5 = pd.DataFrame(x5.csv_rows)\n", "inv_data_6 = pd.DataFrame(x6.csv_rows)\n", "inv_data_7 = pd.DataFrame(x7.csv_rows)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_df(data):\n", "    data.columns = [\"item_id\", \"outlet_id\", \"mrp\", \"timestamp\"] + buckets\n", "    data[\"delivery_date\"] = data[\"timestamp\"]\n", "    data[\"current_inventory\"] = 0\n", "\n", "    data = data.merge(item_outward_days, on=[\"item_id\"], how=\"left\")\n", "\n", "    for i in range(0, len(data)):\n", "        for col in buckets:\n", "            if col <= data.loc[i, \"outward_days\"] + 1:\n", "                data.loc[i, \"current_inventory\"] = (\n", "                    data.loc[i, \"current_inventory\"] + data.loc[i, col]\n", "                )\n", "\n", "    data.drop(\n", "        [\n", "            \"mrp\",\n", "            \"timestamp\",\n", "            \"outward_days\",\n", "        ]\n", "        + buckets,\n", "        inplace=True,\n", "        axis=1,\n", "    )\n", "\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data_1 = format_df(inv_data_1)\n", "inv_data_2 = format_df(inv_data_2)\n", "inv_data_3 = format_df(inv_data_3)\n", "inv_data_4 = format_df(inv_data_4)\n", "inv_data_5 = format_df(inv_data_5)\n", "inv_data_6 = format_df(inv_data_6)\n", "inv_data_7 = format_df(inv_data_7)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data_6.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = inv_data_1.append(\n", "    [inv_data_2, inv_data_3, inv_data_4, inv_data_5, inv_data_6, inv_data_7]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.outlet_id = inv_data.outlet_id.astype(int)\n", "inv_data.item_id = inv_data.item_id.astype(int)\n", "\n", "inv_data = itm_outlets_date.merge(\n", "    inv_data, on=[\"item_id\", \"outlet_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.current_inventory = np.where(\n", "    inv_data.current_inventory.isna(), 0, inv_data.current_inventory\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = LiquidationAgingInventory(datetime.today().date(), buckets)\n", "inv_today = pd.DataFrame(x.csv_rows)\n", "inv_today.columns = [\"item_id\", \"outlet_id\", \"mrp\", \"timestamp\"] + buckets\n", "inv_today[\"delivery_date\"] = inv_today[\"timestamp\"]\n", "inv_today[\"cold_inv\"] = 0\n", "\n", "inv_today = inv_today.merge(item_outward_days, on=[\"item_id\"], how=\"left\")\n", "\n", "for i in range(0, len(inv_today)):\n", "    for col in buckets:\n", "        if col <= inv_today.loc[i, \"outward_days\"]:\n", "            inv_today.loc[i, \"cold_inv\"] = inv_today.loc[i, \"cold_inv\"] + inv_today.loc[i, col]\n", "\n", "inv_today.drop(\n", "    [\n", "        \"mrp\",\n", "        \"timestamp\",\n", "        \"outward_days\",\n", "        \"delivery_date\",\n", "    ]\n", "    + buckets,\n", "    inplace=True,\n", "    axis=1,\n", ")\n", "\n", "print(\"today_inv\", inv_today.cold_inv.sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_today.item_id = inv_today.item_id.astype(int)\n", "inv_today.outlet_id = inv_today.outlet_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = inv_data.merge(inv_today, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.cold_inv.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_query = \"\"\"\n", "select item_id, outlet_id, delivery_date, sum(case when status_id <> 5 then quantity else 0 end) as qty_ordered, count(distinct order_id) as carts from(\n", "    select o.outlet as outlet_id, date(o.scheduled_at) as delivery_date, o.order_id, oi.item_id, oi.quantity, o.status_id, os.name as status\n", "    from ims.ims_order_details o\n", "    left join ims.ims_order_items oi on o.id = oi.order_details_id\n", "    left join ims.ims_order_status os on o.status_id = os.id\n", "    where date(scheduled_at) between current_date + interval 1 day and current_date + interval 4 day\n", "    and oi.item_id in ({item_id})\n", "    and o.outlet in ({outlet_id})\n", ") a\n", "group by 1,2,3\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "orders_placed = pd.read_sql_query(orders_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_placed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_query = \"\"\"\n", "select item_id, outlet_id, delivery_date, sum(picked_quantity) as billed_qty from(\n", "    SELECT p.outlet_id,\n", "       date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "       p.pick_list_id,\n", "       PLO.order_id,\n", "       PLI.item_id,\n", "       sum(PLI.picked_quantity) AS picked_quantity\n", "       \n", "    FROM warehouse_location.warehouse_pick_list p\n", "    INNER JOIN warehouse_location.warehouse_pick_list_item PLI ON PLI.pick_list_id=p.id AND PLI.state IN (2,4)\n", "    LEFT JOIN warehouse_location.warehouse_pick_list_order_mapping PLO ON PLO.pick_list_id=p.id \n", "    LEFT JOIN warehouse_location.warehouse_wave_pick_list_mapping m ON p.id = m.pick_list_id\n", "    LEFT JOIN warehouse_location.warehouse_wave w ON w.id = m.wave_id\n", "    \n", "    WHERE p.id>44404456 and\n", "    date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) = date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")),interval 1 DAY)\n", "    and p.outlet_id in ({outlet_id})\n", "    and PLI.item_id in ({item_id})\n", "    AND p.state IN(10)\n", "    \n", "    GROUP BY 1,2,3,4,5) a\n", "group by 1,2,3\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "billed_list = pd.read_sql_query(billed_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_list.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_list.delivery_date = pd.to_datetime(billed_list.delivery_date)\n", "orders_placed.delivery_date = pd.to_datetime(orders_placed.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_placed = orders_placed.merge(\n", "    billed_list, on=[\"item_id\", \"outlet_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# orders_placed[orders_placed.item_id == 10005008]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data.delivery_date = pd.to_datetime(inv_data.delivery_date)\n", "orders_placed.delivery_date = pd.to_datetime(orders_placed.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_data.merge(orders_placed, on=[\"item_id\", \"outlet_id\", \"delivery_date\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_orders.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_orders[inv_orders.outlet_id != 100]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_cpd_query = \"\"\"\n", "select item_id, outlet_id, avg(consumption) as avg_f_cpd from( \n", "    select item_id, o.cloud_store_id as outlet_id, consumption, date\n", "    from lake_snorlax.date_wise_consumption a\n", "    left join lake_retail.warehouse_outlet_mapping o on a.outlet_id = o.warehouse_id\n", "    where date(date) between current_date - interval '6 days' and current_date\n", "    and item_id in ({item_id})\n", ")\n", "where outlet_id in ({outlet_id})\n", "group by 1,2\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "f_cpd = pd.read_sql_query(sql=f_cpd_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f_cpd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_orders.merge(f_cpd, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist_cpd_query = \"\"\"\n", "with checkout_details as\n", "(\n", "select \n", "oo.order_id,\n", "os.grofer_order_id,\n", "oo.order_date\n", "from\n", "(\n", "select \n", "order_id,\n", "grofer_order_id\n", "from\n", "(\n", "select \n", "order_id,\n", "id as grofer_order_id,\n", "dense_rank() over(partition by a.order_id order by a.update_ts::timestamp desc) as rnk\n", "from\n", "  (\n", "  select distinct order_id,\n", "  id, \n", "  to_timestamp(os.update_ts, 'YYYY-MM-DD HH24:MI') as update_ts \n", "  from lake_oms_bifrost.oms_suborder as os\n", "  where os.type='RetailSuborder' and \n", "  date(os.install_ts+ interval '330 minute') between current_date- interval '30 days' and current_date-interval '1 days'\n", "  and current_status not in ('REPROCURED','CANCELLED')\n", "     ) as a\n", "     )as b\n", "    --  where rnk=1\n", "     group by 1,2\n", "     )os\n", "     inner join\n", "     (\n", "     select id as order_id,\n", "     (oo.install_ts+ interval '330 minute') as order_date\n", "     from lake_oms_bifrost.oms_order as oo\n", "     where\n", "      date(oo.install_ts+ interval '330 minute') between current_date- interval '30 days' and current_date-interval '1 days'\n", "      and oo.type='RetailForwardOrder'\n", "      and current_status not in ('REPROCURED','CANCELLED')\n", "      ) oo\n", "      on os.order_id=oo.order_id\n", "      )\n", "      \n", "     ,\n", " order_details AS\n", " (SELECT DISTINCT c.order_id ,\n", "                  c.grofer_order_id,\n", "                  date(c.order_date) AS order_date,\n", "                  cast(outlet AS int) AS outlet ,\n", "                  facility_id,\n", "                  item_id ::int,\n", "                  product_id ,\n", "                  quantity,\n", "                  offer_id ,\n", "                  mrp,\n", "                  price\n", "  FROM lake_ims.ims_order_details AS a\n", "  inner join checkout_details as c on a.order_id=c.grofer_order_id\n", "  LEFT JOIN lake_ims.ims_order_actuals AS b ON a.id=b.order_details_id\n", " inner JOIN lake_retail.console_outlet AS pco ON a.outlet=pco.id\n", "inner join lake_retail.console_company_type rcc on pco.company_type_id=rcc.id\n", "where upper(rcc.entity_type) like '%%B2B_INDEPENDENT%%'\n", "and pco.active=1\n", "and pco.device_id!=47\n", "and item_id is not null\n", "        ) \n", "        \n", "select *, overall_qpc*1.000/live_days as avg_overall_qpc from(    \n", "select a.item_id, a.outlet as outlet_id, avg(selling_price) as avg_selling_price, avg(a.qty) as avg_cpd, avg(a.carts) as avg_carts, avg(qpc) as avg_qpc, count(distinct a.order_date) as live_days, sum(a.qty/b.total_carts) as overall_qpc from(\n", "    (select order_date, outlet, item_id, sum(price)/sum(quantity) as selling_price, ROUND(sum(quantity),4) as qty, ROUND(count(distinct order_id),4) as carts, sum(quantity)/count(distinct order_id) as qpc from\n", "    order_details\n", "    where item_id in ({item_id})\n", "    and outlet in ({outlet_id})\n", "    group by 1,2,3\n", "    ) a\n", "    \n", "    left join\n", "    \n", "    (select order_date, outlet, count(distinct order_id)::float as total_carts from\n", "    order_details\n", "    group by 1,2) b\n", "    \n", "    on a.order_date = b.order_date and a.outlet = b.outlet\n", ")\n", "group by 1,2)\n", "\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "hist_cpd = pd.read_sql_query(sql=hist_cpd_query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist_cpd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders = inv_orders.merge(hist_cpd, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reading slot wise capacity details from capacity system and manipulation\n", "query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "date(delivery_date) as delivery_date,\n", "delivery_slot_type,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "    warehouse_external_id AS backend_merchant_id,\n", "    warehouse_name AS backend_merchant_name,\n", "    DATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "    TO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "    TO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "    slot_type,\n", "    CASE WHEN EXTRACT(HOUR FROM (slot_start AT TIME ZONE 'ASIA/KOLKATA'))< 16 THEN 'SLOT A' ELSE  'SLOT B' END AS delivery_slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "    warehouse_planned AS given_capacity,\n", "    warehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between date(current_date) and date(current_date + interval '8 days')\n", "and given_capacity > 0\n", "group by 1,2,3,4\n", "order by 2,3;\n", "\"\"\"\n", "\n", "capacity_details = pd.read_sql_query(query, capacity_system)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store, o.facility_id\n", "from\n", "lake_oms_bifrost.oms_merchant m\n", "join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where\n", "cms.active = 1 and cms.cms_update_active = 1;\n", "\"\"\"\n", "# cms_outlet_details = fetch_query(\n", "#     \"cms_outlet_details\", query, redshift, params={\"outlet_id\": OUTLET_ID})\n", "\n", "# cms_outlet_details = pd.read_sql_query(query,redshift,params={\"outlet_id\": OUTLET_ID})\n", "\n", "cms_outlet_details = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"delivery_date\",\n", "            \"delivery_slot_type\",\n", "            \"planned_capacity\",\n", "            \"backend_merchant_name\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "\n", "# warehouse_capacities = warehouse_capacities[\n", "#     warehouse_capacities.outlet_id == int(OUTLET_ID)\n", "# ]\n", "\n", "# if OUTLET_ID ==3334:\n", "#     warehouse_capacities=warehouse_capacities.groupby([\"delivery_date\",\"outlet_id\",\"outlet\",\"delivery_slot_type\"]).agg(\n", "#     {\"Capacity\":\"max\"}).reset_index()\n", "# else:\n", "\n", "warehouse_capacities = (\n", "    warehouse_capacities.groupby([\"delivery_date\", \"outlet_id\", \"outlet\", \"delivery_slot_type\"])\n", "    .agg({\"Capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "warehouse_capacities[\"delivery_date\"] = pd.to_datetime(warehouse_capacities[\"delivery_date\"])\n", "# warehouse_capacities[\"Capacity\"]=1990\n", "warehouse_capacities[\"delivery_date_slot\"] = (\n", "    warehouse_capacities[\"delivery_date\"].astype(str) + warehouse_capacities[\"delivery_slot_type\"]\n", ")\n", "# warehouse_capacities.head()\n", "warehouse_capacities = (\n", "    warehouse_capacities.groupby(\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"delivery_date_slot\",\n", "            \"delivery_date\",\n", "            \"delivery_slot_type\",\n", "        ]\n", "    )\n", "    .agg({\"Capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "warehouse_capacities_date = (\n", "    warehouse_capacities.groupby([\"outlet_id\", \"outlet\", \"delivery_date\"])\n", "    .agg({\"Capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "warehouse_capacities = warehouse_capacities.merge(\n", "    warehouse_capacities_date[[\"outlet_id\", \"delivery_date\", \"Capacity\"]],\n", "    how=\"left\",\n", "    on=[\"outlet_id\", \"delivery_date\"],\n", ").rename(columns={\"Capacity_x\": \"Capacity\", \"Capacity_y\": \"Total_Capacity\"})\n", "warehouse_capacities[\"slot_capacity_ratio\"] = (\n", "    warehouse_capacities[\"Capacity\"] / warehouse_capacities[\"Total_Capacity\"]\n", ")\n", "warehouse_capacities = warehouse_capacities[\n", "    [\"outlet_id\", \"delivery_date\", \"Total_Capacity\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities.delivery_date = pd.to_datetime(warehouse_capacities.delivery_date)\n", "inv_orders.delivery_date = pd.to_datetime(inv_orders.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_capacity = inv_orders.merge(\n", "    warehouse_capacities, how=\"left\", on=[\"outlet_id\", \"delivery_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_capacity.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg_picking_query = \"\"\"\n", "select item_id, outlet_id, avg(qty_picked) as avg_qty_picked from(\n", "\n", "select item_id, outlet_id, delivery_date, sum(picked_quantity) as qty_picked from(\n", "    SELECT p.outlet_id,\n", "       date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "       p.pick_list_id,\n", "       PLO.order_id,\n", "       PLI.item_id,\n", "       sum(PLI.picked_quantity) AS picked_quantity\n", "       \n", "    FROM warehouse_location.warehouse_pick_list p\n", "    INNER JOIN warehouse_location.warehouse_pick_list_item PLI ON PLI.pick_list_id=p.id AND PLI.state IN (2,4)\n", "    LEFT JOIN warehouse_location.warehouse_pick_list_order_mapping PLO ON PLO.pick_list_id=p.id \n", "    LEFT JOIN warehouse_location.warehouse_wave_pick_list_mapping m ON p.id = m.pick_list_id\n", "    LEFT JOIN warehouse_location.warehouse_wave w ON w.id = m.wave_id\n", "    \n", "    WHERE p.id>44404456 and\n", "    date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) between date_sub(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval 7 DAY)\n", "    and date_sub(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")), interval 1 DAY)\n", "    and p.outlet_id in ({outlet_id})\n", "    and PLI.item_id in ({item_id})\n", "    AND p.state IN(9,\n", "                 10,\n", "                 11,\n", "                 12,\n", "                 13,\n", "                 14,\n", "                 8,\n", "                 3,\n", "                 4)\n", "    \n", "    GROUP BY 1,2,3,4,5) a\n", "group by 1,2,3) b\n", "group by 1,2\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "avg_qty_picked = pd.read_sql_query(avg_picking_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_capacity = order_capacity.merge(avg_qty_picked, how=\"left\", on=[\"item_id\", \"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select item_id, outlet_id, delivery_date, sum(picked_quantity) as qty_picked from(\n", "    SELECT p.outlet_id,\n", "       date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "       p.pick_list_id,\n", "       PLO.order_id,\n", "       PLI.item_id,\n", "       sum(PLI.picked_quantity) AS picked_quantity\n", "       \n", "    FROM warehouse_location.warehouse_pick_list p\n", "    INNER JOIN warehouse_location.warehouse_pick_list_item PLI ON PLI.pick_list_id=p.id AND PLI.state IN (2,4)\n", "    LEFT JOIN warehouse_location.warehouse_pick_list_order_mapping PLO ON PLO.pick_list_id=p.id \n", "    LEFT JOIN warehouse_location.warehouse_wave_pick_list_mapping m ON p.id = m.pick_list_id\n", "    LEFT JOIN warehouse_location.warehouse_wave w ON w.id = m.wave_id\n", "    \n", "    WHERE p.id>44404456 and\n", "    date(convert_tz(p.max_scheduled_time,\"+00:00\",\"+05:30\")) = date_add(date(convert_tz(current_date(),\"+00:00\",\"+05:30\")),interval 1 DAY)\n", "    and p.outlet_id in ({outlet_id})\n", "    and PLI.item_id in ({item_id})\n", "    AND p.state IN(9,\n", "                 10,\n", "                 11,\n", "                 12,\n", "                 13,\n", "                 14,\n", "                 8,\n", "                 3,\n", "                 4)\n", "    \n", "    GROUP BY 1,2,3,4,5) a\n", "group by 1,2,3\n", "\"\"\".format(\n", "    item_id=item_id_list, outlet_id=outlet_id_list\n", ")\n", "\n", "pick_list = pd.read_sql_query(sql, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pick_list.delivery_date = pd.to_datetime(pick_list.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked = order_capacity.merge(\n", "    pick_list, how=\"left\", on=[\"item_id\", \"outlet_id\", \"delivery_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po_query = \"\"\"\n", "select distinct\n", "o.facility_id,\n", "date(issue_date) as issue_date,\n", "date(expiry_date) as expiry_date,\n", "(case when date_add(date(ps.schedule_date_time), interval 1 day) is null then \n", "date_add(date(expiry_date), interval 1 day) else date_add(date(ps.schedule_date_time), interval 1 day) end) AS delivery_date,\n", "poi.item_id,\n", "poi.units_ordered as expected_po_qty\n", "from po.purchase_order p\n", "left join\n", "po.po_schedule ps on p.id=ps.po_id_id\n", "inner join\n", "po.purchase_order_items poi on p.id=poi.po_id\n", "inner join\n", "retail.console_outlet o on o.id=p.outlet_id\n", "inner join\n", "po.purchase_order_status posa on posa.po_id = p.id\n", "inner join\n", "po.purchase_order_state posta on posta.id = posa.po_state_id\n", "where \n", "posta.name in ('Created','Scheduled','Rescheduled')\n", "and date(convert_tz(p.issue_date,'+00:00','+05:30'))\n", "and poi.item_id in ({item_id})\n", "and o.facility_id in ({facility})\n", "\"\"\".format(\n", "    item_id=item_id_list,\n", "    facility=\", \".join(set(filter(None, order_cap_picked.facility_id.astype(str).values))),\n", ")\n", "\n", "open_po = pd.read_sql_query(sql=open_po_query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_po.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked.delivery_date = pd.to_datetime(order_cap_picked.delivery_date)\n", "open_po.delivery_date = pd.to_datetime(open_po.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked_po = order_cap_picked.merge(\n", "    open_po, on=[\"item_id\", \"facility_id\", \"delivery_date\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# order_cap_picked_po[order_cap_picked_po.item_id == 10001718]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_recvd_query = \"\"\"select o.outlet as outlet_id, date(o.scheduled_at) as delivery_date, count(distinct o.order_id) as orders_recvd\n", "from ims.ims_order_details o\n", "where date(scheduled_at) between current_date + interval 1 day and current_date + interval 4 day\n", "and outlet in ({outlet_id})\n", "and o.status_id <> 5\n", "group by 1,2\n", "\"\"\".format(\n", "    outlet_id=outlet_id_list\n", ")\n", "\n", "total_orders = pd.read_sql_query(sql=orders_recvd_query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_cap_picked_po.delivery_date = pd.to_datetime(order_cap_picked_po.delivery_date)\n", "total_orders.delivery_date = pd.to_datetime(total_orders.delivery_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = order_cap_picked_po.merge(total_orders, on=[\"outlet_id\", \"delivery_date\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Using method 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cols = [\n", "#     \"item_id\",\n", "#     \"item_name\",\n", "#     \"outlet_id\",\n", "#     \"outlet_name\",\n", "#     \"current_inventory\",\n", "#     \"delivery_date\",\n", "#     \"Total_Capacity\",\n", "#     \"orders_recvd\",\n", "#     \"carts\",\n", "#     \"avg_carts\",\n", "#     \"qty_ordered\",\n", "#     \"avg_qpc\",\n", "#     \"qty_picked\",\n", "#     \"expected_po_qty\",\n", "# ]\n", "# fin_m_1 = fin[cols].copy()\n", "\n", "# fin_m_1[\"calculated_carts\"] = (\n", "#     fin_m_1[\"carts\"] * fin[\"Total_Capacity\"] / fin_m_1[\"orders_recvd\"]\n", "# )\n", "# fin_m_1[\"calculated_carts\"] = np.where(\n", "#     fin_m_1[\"calculated_carts\"].isna(),\n", "#     fin_m_1[\"avg_carts\"],\n", "#     fin_m_1[\"calculated_carts\"],\n", "# )\n", "\n", "# fin_m_1[\"calculated_qty\"] = fin_m_1[\"calculated_carts\"] * fin_m_1[\"avg_qpc\"]\n", "# fin_m_1[\"qty_picked\"] = np.where(fin_m_1[\"qty_picked\"].isna(), 0, fin_m_1[\"qty_picked\"])\n", "# fin_m_1[\"expected_po_qty\"] = np.where(\n", "#     fin_m_1[\"expected_po_qty\"].isna(), 0, fin_m_1[\"expected_po_qty\"]\n", "# )\n", "# fin_m_1[\"calculated_qty\"] = np.ceil(fin_m_1[\"calculated_qty\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Using method 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cols_2 = [\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"facility_id\",\n", "    \"current_inventory\",\n", "    \"cold_inv\",\n", "    \"delivery_date\",\n", "    \"Total_Capacity\",\n", "    \"orders_recvd\",\n", "    \"carts\",\n", "    \"avg_carts\",\n", "    \"qty_ordered\",\n", "    \"avg_overall_qpc\",\n", "    \"avg_cpd\",\n", "    \"avg_f_cpd\",\n", "    \"qty_picked\",\n", "    \"billed_qty\",\n", "    \"expected_po_qty\",\n", "    \"inner_case_size\",\n", "    \"outer_case_size\",\n", "    \"weight_in_gm\",\n", "    \"landing_price\",\n", "    \"avg_selling_price\",\n", "    \"avg_qty_picked\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2 = fin[cols_2].copy()\n", "\n", "fin_m_2[\"carts\"].fillna(0, inplace=True)\n", "fin_m_2[\"qty_ordered\"].fillna(0, inplace=True)\n", "fin_m_2[\"billed_qty\"].fillna(0, inplace=True)\n", "fin_m_2[\"avg_overall_qpc\"].fillna(0, inplace=True)\n", "fin_m_2[\"avg_qty_picked\"].fillna(0, inplace=True)\n", "\n", "for i in range(0, len(fin_m_2)):\n", "    if pd.isnull(fin_m_2.loc[i, \"Total_Capacity\"]):\n", "        fin_m_2.loc[i, \"Total_Capacity\"] = fin_m_2.loc[i - 1, \"Total_Capacity\"]\n", "\n", "\n", "fin_m_2[\"orders_recvd\"] = np.where(fin_m_2[\"orders_recvd\"].isna(), 0, fin_m_2[\"orders_recvd\"])\n", "fin_m_2[\"remaining_carts\"] = fin_m_2[\"Total_Capacity\"] - fin_m_2[\"orders_recvd\"]\n", "fin_m_2[\"remaining_carts\"] = np.where(fin_m_2[\"remaining_carts\"] < 0, 0, fin_m_2[\"remaining_carts\"])\n", "fin_m_2[\"remaining_carts\"].fillna(0, inplace=True)\n", "\n", "fin_m_2[\"calculated_qty\"] = fin_m_2[\"qty_ordered\"] + (\n", "    fin_m_2[\"remaining_carts\"] * fin_m_2[\"avg_overall_qpc\"]\n", ")\n", "fin_m_2[\"extrapolated_qty\"] = np.ceil(fin_m_2[\"remaining_carts\"] * fin_m_2[\"avg_overall_qpc\"])\n", "\n", "fin_m_2[\"qty_picked\"].fillna(0, inplace=True)\n", "fin_m_2[\"billed_qty\"].fillna(0, inplace=True)\n", "fin_m_2[\"expected_po_qty\"].fillna(0, inplace=True)\n", "fin_m_2[\"calculated_qty\"] = np.ceil(fin_m_2[\"calculated_qty\"])\n", "\n", "fin_m_2[\"landing_price\"].fillna(0, inplace=True)\n", "fin_m_2[\"avg_selling_price\"].fillna(0, inplace=True)\n", "fin_m_2[\"avg_cpd\"].fillna(0, inplace=True)\n", "\n", "fin_m_2[\"gmv\"] = fin_m_2[\"avg_cpd\"] * fin_m_2[\"avg_selling_price\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fin_m_2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin_m_2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin1.sort_values(by=[\"item_id\", \"outlet_id\", \"delivery_date\"]).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.drop([\"index\"], inplace=True, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fin1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs = pb.from_sheets(\"1aw-EVLAjTwGUfIlIf96_csul5oDdY6YnD0WQoB79jfQ\", \"Sheet1\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cols = [\n", "    \"Item ID\",\n", "    \"Brand\",\n", "    \"Vendor Code\",\n", "    \"Vendor Name\",\n", "    \"Manufacturer\",\n", "    \"<PERSON>ad <PERSON> (Qty/Value)\",\n", "    \"Load size\",\n", "    \"Shelf life\\n (days)\",\n", "    \"Case Size\",\n", "    \"Outward (Days)\",\n", "    \"Storage Type\\n(Cold/Frozen/Ambient)\",\n", "    \"PO Day\",\n", "]\n", "inputs = inputs[cols]\n", "inputs.columns = [\n", "    \"item_id\",\n", "    \"brand\",\n", "    \"vendor_id\",\n", "    \"vendor\",\n", "    \"manufacturer\",\n", "    \"load_type\",\n", "    \"load_size\",\n", "    \"shelf_life\",\n", "    \"case_size\",\n", "    \"outward_days\",\n", "    \"storage_type\",\n", "    \"po_day\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs = inputs[inputs.item_id != \"\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs.item_id = inputs.item_id.astype(int)\n", "\n", "inputs.vendor_id = np.where(inputs.vendor_id == \"\", 0, inputs.vendor_id)\n", "inputs.vendor_id = inputs.vendor_id.astype(int)\n", "\n", "inputs.outward_days = np.where(inputs.outward_days == \"\", 0, inputs.outward_days)\n", "inputs.outward_days = inputs.outward_days.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "for i in range(0, len(inputs)):\n", "    inputs.loc[i, \"load_size\"] = re.search(\"\\d+\", inputs.loc[i, \"load_size\"])[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_query = \"\"\"\n", "select distinct d.item_id, d.facility_id, d.vendor_id, d.tat_days from \n", "\n", "    (select a.*, b.facility_id from po.item_outlet_vendor_tat a\n", "    left join\n", "    (select id, facility_id from retail.console_outlet) b\n", "    on a.outlet_id = b.id) d\n", "    \n", "    right join\n", "    \n", "    (select item_id, facility_id, vendor_id, max(updated_at) as updated_at\n", "    from\n", "    (select a.*, b.facility_id from po.item_outlet_vendor_tat a\n", "    left join\n", "    (select id, facility_id from retail.console_outlet) b\n", "    on a.outlet_id = b.id) c\n", "    group by 1,2,3) e\n", "    \n", "on d.item_id = e.item_id and d.vendor_id = e.vendor_id and d.facility_id = e.facility_id and d.updated_at = e.updated_at\n", "\n", "where d.facility_id in ({facility})\n", "and d.vendor_id in ({vendor})\n", "\"\"\".format(\n", "    facility=\", \".join(set(filter(None, fin1.facility_id.astype(str).values))),\n", "    vendor=\", \".join(set(filter(None, inputs[\"vendor_id\"].astype(str).values))),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat = pd.read_sql_query(sql=tat_query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin1.merge(inputs, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1 = fin1.merge(tat, on=[\"item_id\", \"facility_id\", \"vendor_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1[\"next_day_inv\"] = 0\n", "\n", "fin1.loc[0, \"actual_inv\"] = (\n", "    fin1.loc[0, \"current_inventory\"]\n", "    + fin1.loc[0, \"expected_po_qty\"]\n", "    - fin1.loc[0, \"qty_picked\"]\n", "    + fin1.loc[0, \"billed_qty\"]\n", ")\n", "\n", "if fin1.loc[0, \"actual_inv\"] < 0:\n", "    fin1.loc[0, \"actual_inv\"] = 0\n", "\n", "fin1.loc[0, \"qty_required\"] = (\n", "    fin1.loc[0, \"calculated_qty\"] - fin1.loc[0, \"qty_picked\"] - fin1.loc[0, \"actual_inv\"]\n", ")\n", "if fin1.loc[0, \"qty_required\"] < 0:\n", "    fin1.loc[0, \"qty_required\"] = 0\n", "\n", "fin1.loc[0, \"next_day_inv\"] = (\n", "    fin1.loc[0, \"current_inventory\"]\n", "    + fin1.loc[0, \"expected_po_qty\"]\n", "    - fin1.loc[0, \"qty_picked\"]\n", "    + fin1.loc[0, \"billed_qty\"]\n", ") - (fin1.loc[0, \"calculated_qty\"] - fin1.loc[0, \"qty_picked\"])\n", "if fin1.loc[0, \"next_day_inv\"] < 0:\n", "    fin1.loc[0, \"next_day_inv\"] = 0\n", "\n", "if (\n", "    fin1.loc[0, \"qty_required\"] > capping * fin1.loc[0, \"avg_qty_picked\"]\n", "    and fin1.loc[0, \"avg_qty_picked\"] != 0\n", "):\n", "    fin1.loc[0, \"qty_capped\"] = capping * fin1.loc[0, \"avg_qty_picked\"]\n", "else:\n", "    fin1.loc[0, \"qty_capped\"] = fin1.loc[0, \"qty_required\"]\n", "\n", "for i in range(1, len(fin1)):\n", "\n", "    if fin1.loc[i, \"delivery_date\"] == date.iloc[0][\"delivery_date\"]:\n", "\n", "        fin1.loc[i, \"actual_inv\"] = (\n", "            fin1.loc[i, \"current_inventory\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        )\n", "        if fin1.loc[i, \"actual_inv\"] < 0:\n", "            fin1.loc[i, \"actual_inv\"] = 0\n", "\n", "        fin1.loc[i, \"qty_required\"] = (\n", "            fin1.loc[i, \"calculated_qty\"] - fin1.loc[i, \"qty_picked\"] - fin1.loc[i, \"actual_inv\"]\n", "        )\n", "        if fin1.loc[i, \"qty_required\"] < 0:\n", "            fin1.loc[i, \"qty_required\"] = 0\n", "\n", "        fin1.loc[i, \"next_day_inv\"] = (\n", "            fin1.loc[i, \"current_inventory\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        ) - (fin1.loc[i, \"calculated_qty\"] - fin1.loc[i, \"qty_picked\"])\n", "\n", "        if fin1.loc[i, \"next_day_inv\"] < 0:\n", "            fin1.loc[i, \"next_day_inv\"] = 0\n", "\n", "        if (\n", "            fin1.loc[i, \"qty_required\"] > capping * fin1.loc[i, \"avg_qty_picked\"]\n", "            and fin1.loc[i, \"avg_qty_picked\"] != 0\n", "        ):\n", "            fin1.loc[i, \"qty_capped\"] = capping * fin1.loc[i, \"avg_qty_picked\"]\n", "        else:\n", "            fin1.loc[i, \"qty_capped\"] = fin1.loc[i, \"qty_required\"]\n", "\n", "    else:\n", "        if fin1.loc[i - 1, \"next_day_inv\"] > fin1.loc[i, \"current_inventory\"]:\n", "            if (\n", "                fin1.loc[i, \"delivery_date\"] == date.iloc[1][\"delivery_date\"]\n", "                and fin1.loc[i - 1, \"expected_po_qty\"] == 0\n", "            ):\n", "                fin1.loc[i - 1, \"next_day_inv\"] = fin1.loc[i, \"current_inventory\"]\n", "\n", "            elif fin1.loc[i, \"delivery_date\"] == date.iloc[2][\"delivery_date\"] and (\n", "                fin1.loc[i - 1, \"expected_po_qty\"] == 0 and fin1.loc[i - 2, \"expected_po_qty\"] == 0\n", "            ):\n", "                fin1.loc[i - 1, \"next_day_inv\"] = fin1.loc[i, \"current_inventory\"]\n", "\n", "        fin1.loc[i, \"actual_inv\"] = (\n", "            fin1.loc[i - 1, \"next_day_inv\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        )\n", "\n", "        if fin1.loc[i, \"actual_inv\"] < 0:\n", "            fin1.loc[i, \"actual_inv\"] = 0\n", "\n", "        fin1.loc[i, \"qty_required\"] = (\n", "            fin1.loc[i, \"calculated_qty\"] - fin1.loc[i, \"qty_picked\"] - fin1.loc[i, \"actual_inv\"]\n", "        )\n", "        if fin1.loc[i, \"qty_required\"] < 0:\n", "            fin1.loc[i, \"qty_required\"] = 0\n", "\n", "        fin1.loc[i, \"next_day_inv\"] = (\n", "            fin1.loc[i - 1, \"next_day_inv\"]\n", "            + fin1.loc[i, \"expected_po_qty\"]\n", "            - fin1.loc[i, \"qty_picked\"]\n", "            + fin1.loc[i, \"billed_qty\"]\n", "        ) - (fin1.loc[i, \"calculated_qty\"] - fin1.loc[i, \"qty_picked\"])\n", "        if fin1.loc[i, \"next_day_inv\"] < 0:\n", "            fin1.loc[i, \"next_day_inv\"] = 0\n", "\n", "        if (\n", "            fin1.loc[i, \"delivery_date\"]\n", "            == date.iloc[0][\"delivery_date\"] + timedelta(days=int(fin1.loc[i, \"outward_days\"]) - 1)\n", "        ) and (fin1.loc[i, \"next_day_inv\"] > fin1.loc[i, \"expected_po_qty\"]):\n", "            fin1.loc[i, \"next_day_inv\"] = fin1.loc[i, \"expected_po_qty\"]\n", "\n", "        if (\n", "            fin1.loc[i, \"qty_required\"] > capping * fin1.loc[i, \"avg_qty_picked\"]\n", "            and fin1.loc[i, \"avg_qty_picked\"] != 0\n", "        ):\n", "            fin1.loc[i, \"qty_capped\"] = capping * fin1.loc[i, \"avg_qty_picked\"]\n", "        else:\n", "            fin1.loc[i, \"qty_capped\"] = fin1.loc[i, \"qty_required\"]\n", "\n", "fin1[\"qty_capped\"] = np.ceil(fin1[\"qty_capped\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fin1[fin1.vendor == 'PARV AGENCY']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1[\"cpd_norm\"] = (fin1[\"avg_f_cpd\"] - fin1[\"avg_f_cpd\"].min()) / (\n", "    fin1[\"avg_f_cpd\"].max() - fin1[\"avg_f_cpd\"].min()\n", ")\n", "fin1[\"item_gmv_norm\"] = (fin1[\"gmv\"] - fin1[\"gmv\"].min()) / (fin1[\"gmv\"].max() - fin1[\"gmv\"].min())\n", "fin1[\"net_score\"] = fin1[\"cpd_norm\"] + fin1[\"item_gmv_norm\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1[\"updated_at\"] = datetime.strftime(\n", "    datetime.today() + timed<PERSON>ta(hours=5, minutes=30), \"%Y-%m-%d %H:%M:%S\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_cols = [\n", "    \"updated_at\",\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"facility_id\",\n", "    \"current_inventory\",\n", "    \"cold_inv\",\n", "    \"delivery_date\",\n", "    \"Total_Capacity\",\n", "    \"orders_recvd\",\n", "    \"remaining_carts\",\n", "    \"avg_overall_qpc\",\n", "    \"extrapolated_qty\",\n", "    \"avg_f_cpd\",\n", "    \"gmv\",\n", "    \"carts\",\n", "    \"qty_ordered\",\n", "    \"calculated_qty\",\n", "    \"qty_picked\",\n", "    \"billed_qty\",\n", "    \"expected_po_qty\",\n", "    \"actual_inv\",\n", "    \"next_day_inv\",\n", "    \"weight_in_gm\",\n", "    \"landing_price\",\n", "    \"avg_qty_picked\",\n", "    \"qty_required\",\n", "    \"qty_capped\",\n", "    \"brand\",\n", "    \"vendor\",\n", "    \"manufacturer\",\n", "    \"load_type\",\n", "    \"load_size\",\n", "    \"shelf_life\",\n", "    \"case_size\",\n", "    \"outward_days\",\n", "    \"net_score\",\n", "    \"tat_days\",\n", "    \"storage_type\",\n", "    \"po_day\",\n", "]\n", "fin1 = fin1[fin_cols]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(fin1, \"1EoQLuCjzj1rNvA4Rt-u8RX6c_XKjKP61Hoqm2kA4EOw\", \"Detailed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1[\"cap\"] = capping\n", "fin1 = fin1.rename(columns={\"Total_Capacity\": \"total_capacity\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.current_inventory = fin1.current_inventory.astype(int)\n", "fin1.cold_inv = fin1.cold_inv.astype(int)\n", "fin1.total_capacity = fin1.total_capacity.astype(int)\n", "fin1.orders_recvd = fin1.orders_recvd.astype(int)\n", "fin1.remaining_carts = fin1.remaining_carts.astype(int)\n", "fin1.extrapolated_qty = fin1.extrapolated_qty.astype(int)\n", "fin1.avg_f_cpd.fillna(0, inplace=True)\n", "fin1.avg_f_cpd = fin1.avg_f_cpd.astype(float)\n", "\n", "fin1.carts = fin1.carts.astype(int)\n", "fin1.qty_ordered = fin1.qty_ordered.astype(int)\n", "fin1.calculated_qty = fin1.calculated_qty.astype(int)\n", "fin1.qty_picked = fin1.qty_picked.astype(int)\n", "fin1.billed_qty = fin1.billed_qty.astype(int)\n", "fin1.expected_po_qty = fin1.expected_po_qty.astype(int)\n", "fin1.actual_inv = fin1.actual_inv.astype(int)\n", "fin1.next_day_inv = fin1.next_day_inv.astype(int)\n", "\n", "fin1.avg_overall_qpc = fin1.avg_overall_qpc.astype(str)\n", "fin1.avg_overall_qpc = fin1.avg_overall_qpc.str.slice(0, 7)\n", "\n", "fin1.load_size = fin1.load_size.astype(int)\n", "fin1.shelf_life = fin1.shelf_life.astype(int)\n", "fin1.case_size = fin1.case_size.astype(int)\n", "fin1.outward_days = fin1.outward_days.astype(int)\n", "fin1.net_score = fin1.net_score.astype(float)\n", "\n", "fin1.tat_days.fillna(2, inplace=True)\n", "fin1.tat_days = fin1.tat_days.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.outlet_name = fin1.outlet_name.str.replace(\"Super Store\", \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin1.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes_1 = [\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar(60)\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar(40)\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"integer\"},\n", "    {\"name\": \"cold_inv\", \"type\": \"integer\"},\n", "    {\"name\": \"delivery_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"total_capacity\", \"type\": \"integer\"},\n", "    {\"name\": \"orders_recvd\", \"type\": \"integer\"},\n", "    {\"name\": \"remaining_carts\", \"type\": \"integer\"},\n", "    {\"name\": \"avg_overall_qpc\", \"type\": \"varchar(7)\"},\n", "    {\"name\": \"extrapolated_qty\", \"type\": \"integer\"},\n", "    {\"name\": \"avg_f_cpd\", \"type\": \"float\"},\n", "    {\"name\": \"gmv\", \"type\": \"float\"},\n", "    {\"name\": \"carts\", \"type\": \"integer\"},\n", "    {\"name\": \"qty_ordered\", \"type\": \"integer\"},\n", "    {\"name\": \"calculated_qty\", \"type\": \"integer\"},\n", "    {\"name\": \"qty_picked\", \"type\": \"integer\"},\n", "    {\"name\": \"billed_qty\", \"type\": \"integer\"},\n", "    {\"name\": \"expected_po_qty\", \"type\": \"integer\"},\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\"},\n", "    {\"name\": \"next_day_inv\", \"type\": \"integer\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"integer\"},\n", "    {\"name\": \"landing_price\", \"type\": \"float\"},\n", "    {\"name\": \"avg_qty_picked\", \"type\": \"float\"},\n", "    {\"name\": \"qty_required\", \"type\": \"float\"},\n", "    {\"name\": \"qty_capped\", \"type\": \"float\"},\n", "    {\"name\": \"brand\", \"type\": \"varchar(20)\"},\n", "    {\"name\": \"vendor\", \"type\": \"varchar(40)\"},\n", "    {\"name\": \"manufacturer\", \"type\": \"varchar(50)\"},\n", "    {\"name\": \"load_type\", \"type\": \"varchar(10)\"},\n", "    {\"name\": \"load_size\", \"type\": \"integer\"},\n", "    {\"name\": \"shelf_life\", \"type\": \"integer\"},\n", "    {\"name\": \"case_size\", \"type\": \"integer\"},\n", "    {\"name\": \"outward_days\", \"type\": \"integer\"},\n", "    {\"name\": \"net_score\", \"type\": \"float\"},\n", "    {\"name\": \"tat_days\", \"type\": \"integer\"},\n", "    {\"name\": \"storage_type\", \"type\": \"varchar(40)\"},\n", "    {\"name\": \"po_day\", \"type\": \"varchar(20)\"},\n", "    {\"name\": \"cap\", \"type\": \"float\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"perishable_indenting_details\",\n", "    \"column_dtypes\": column_dtypes_1,\n", "    \"primary_key\": [\"item_id\", \"outlet_id\", \"updated_at\"],\n", "    \"sortkey\": [\"updated_at\", \"item_id\", \"outlet_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(fin1, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cols = [\n", "    \"updated_at\",\n", "    \"delivery_date\",\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"outlet_id\",\n", "    \"outlet_name\",\n", "    \"facility_id\",\n", "    \"brand\",\n", "    \"vendor\",\n", "    \"manufacturer\",\n", "    \"po_day\",\n", "    \"tat_days\",\n", "    \"cold_inv\",\n", "    \"weight_in_gm\",\n", "    \"landing_price\",\n", "    \"storage_type\",\n", "    \"load_type\",\n", "    \"load_size\",\n", "    \"shelf_life\",\n", "    \"case_size\",\n", "    \"avg_f_cpd\",\n", "    \"gmv\",\n", "    \"net_score\",\n", "    \"qty_required\",\n", "    \"avg_qty_picked\",\n", "    \"qty_capped\",\n", "]\n", "fin2 = fin1[cols]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin2.tat_days.fillna(0, inplace=True)\n", "fin2.cold_inv.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = (\n", "    fin2[fin2.delivery_date != date.iloc[0][\"delivery_date\"]]\n", "    .groupby(\n", "        [\n", "            \"updated_at\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"facility_id\",\n", "            \"brand\",\n", "            \"vendor\",\n", "            \"manufacturer\",\n", "            \"po_day\",\n", "            \"tat_days\",\n", "            \"cold_inv\",\n", "            \"case_size\",\n", "            \"weight_in_gm\",\n", "            \"landing_price\",\n", "            \"avg_f_cpd\",\n", "            \"gmv\",\n", "            \"net_score\",\n", "            \"storage_type\",\n", "            \"load_type\",\n", "            \"load_size\",\n", "            \"shelf_life\",\n", "        ]\n", "    )\n", "    .agg({\"qty_required\": \"sum\", \"qty_capped\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.case_size = summary.case_size.astype(int)\n", "summary[\"qty_case_adjusted\"] = np.where(\n", "    (\n", "        (summary[\"qty_capped\"] / summary[\"case_size\"])\n", "        - np.floor(summary[\"qty_capped\"] / summary[\"case_size\"])\n", "    )\n", "    >= 0.3,\n", "    np.ceil(summary[\"qty_capped\"] / summary[\"case_size\"]) * summary[\"case_size\"],\n", "    np.floor(summary[\"qty_capped\"] / summary[\"case_size\"]) * summary[\"case_size\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# summary[['qty_capped', 'case_size','qty_case_adjusted']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.shelf_life = summary.shelf_life.astype(int)\n", "summary.case_size = summary.case_size.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.sort_values(\n", "    by=[\"outlet_id\", \"vendor\", \"shelf_life\", \"case_size\"],\n", "    ascending=[True, True, False, True],\n", "    axis=0,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(0, len(summary)):\n", "\n", "    if summary.loc[i, \"load_type\"] == \"Case\":\n", "        summary.loc[i, \"curr_load\"] = (\n", "            summary.loc[i, \"qty_case_adjusted\"] / summary.loc[i, \"case_size\"]\n", "        )\n", "        summary.loc[i, \"curr_load\"] = np.where(\n", "            summary.loc[i, \"curr_load\"] == 0, 0, np.ceil(summary.loc[i, \"curr_load\"])\n", "        )\n", "        summary.loc[i, \"qty_case_adjusted\"] = (\n", "            summary.loc[i, \"curr_load\"] * summary.loc[i, \"case_size\"]\n", "        )\n", "\n", "    elif summary.loc[i, \"load_type\"] == \"Value\":\n", "        summary.loc[i, \"curr_load\"] = (\n", "            summary.loc[i, \"qty_case_adjusted\"] * summary.loc[i, \"landing_price\"]\n", "        )\n", "\n", "    elif summary.loc[i, \"load_type\"] == \"Weight\":\n", "        summary.loc[i, \"curr_load\"] = (\n", "            summary.loc[i, \"qty_case_adjusted\"] * summary.loc[i, \"weight_in_gm\"]\n", "        ) / 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_load = (\n", "    summary.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"])\n", "    .agg(\n", "        {\n", "            \"curr_load\": \"sum\",\n", "            \"shelf_life\": \"max\",\n", "            \"case_size\": \"min\",\n", "            \"item_id\": \"nunique\",\n", "            \"net_score\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "curr_load.columns = [\n", "    \"outlet_id\",\n", "    \"vendor\",\n", "    \"manufacturer\",\n", "    \"load_size1\",\n", "    \"curr_load_on_vendor\",\n", "    \"max_shelf_life\",\n", "    \"min_case_size\",\n", "    \"item_count\",\n", "    \"vendor_net_score\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_load.load_size1 = curr_load.load_size1.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_check = curr_load[\n", "    (curr_load.curr_load_on_vendor < curr_load.load_size1) & (curr_load.curr_load_on_vendor != 0)\n", "].reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary1 = summary.merge(curr_load, on=[\"outlet_id\", \"vendor\", \"manufacturer\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = summary1.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin[fin.vendor == \"PARV AGENCY\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.curr_load_on_vendor = fin.curr_load_on_vendor.astype(int)\n", "fin.load_size = fin.load_size.astype(int)\n", "\n", "fin[\"fin_load\"] = fin[\"curr_load\"]\n", "fin[\"fin_qty\"] = fin[\"qty_case_adjusted\"]\n", "\n", "i = 0\n", "\n", "while i < len(fin):\n", "\n", "    for j in range(0, len(load_check)):\n", "\n", "        if (\n", "            (fin.loc[i, \"vendor\"] == load_check.loc[j, \"vendor\"])\n", "            & (fin.loc[i, \"manufacturer\"] == load_check.loc[j, \"manufacturer\"])\n", "            & (fin.loc[i, \"outlet_id\"] == load_check.loc[j, \"outlet_id\"])\n", "        ):\n", "\n", "            #             if (fin.loc[i, \"qty_capped\"] > 0) & (fin.loc[i, \"shelf_life\"] == load_check.loc[j, \"max_shelf_life\"]):\n", "            load_diff = fin.loc[i, \"load_size\"] - fin.loc[i, \"curr_load_on_vendor\"]\n", "            k = i\n", "\n", "            while load_diff > 0:\n", "                if (fin.loc[k, \"vendor\"] == fin.loc[i, \"vendor\"]) & (\n", "                    fin.loc[k, \"manufacturer\"] == fin.loc[i, \"manufacturer\"]\n", "                ):\n", "\n", "                    if (fin.loc[k, \"load_type\"] == \"Case\") & (fin.loc[k, \"qty_case_adjusted\"] > 0):\n", "                        fin.loc[k, \"fin_load\"] = fin.loc[k, \"curr_load\"] + 1\n", "                        fin.loc[k, \"curr_load\"] = fin.loc[k, \"fin_load\"]\n", "                        fin.loc[k, \"fin_qty\"] = fin.loc[k, \"curr_load\"] * fin.loc[k, \"case_size\"]\n", "                        load_diff -= 1\n", "\n", "                    elif (fin.loc[k, \"load_type\"] == \"Value\") & (\n", "                        fin.loc[k, \"qty_case_adjusted\"] > 0\n", "                    ):\n", "                        fin.loc[k, \"fin_load\"] = (\n", "                            fin.loc[k, \"curr_load\"]\n", "                            + fin.loc[k, \"case_size\"] * fin.loc[k, \"landing_price\"]\n", "                        )\n", "                        fin.loc[k, \"curr_load\"] = fin.loc[k, \"fin_load\"]\n", "                        fin.loc[k, \"fin_qty\"] = (\n", "                            fin.loc[k, \"curr_load\"] / fin.loc[k, \"landing_price\"]\n", "                        )\n", "                        load_diff = load_diff - (\n", "                            fin.loc[k, \"case_size\"] * fin.loc[k, \"landing_price\"]\n", "                        )\n", "\n", "                    elif (fin.loc[k, \"load_type\"] == \"Weight\") & (\n", "                        fin.loc[k, \"qty_case_adjusted\"] > 0\n", "                    ):\n", "                        fin.loc[k, \"fin_load\"] = (\n", "                            fin.loc[k, \"curr_load\"]\n", "                            + fin.loc[k, \"case_size\"] * fin.loc[k, \"weight_in_gm\"] / 1000\n", "                        )\n", "                        fin.loc[k, \"curr_load\"] = fin.loc[k, \"fin_load\"]\n", "                        fin.loc[k, \"fin_qty\"] = fin.loc[k, \"curr_load\"] / (\n", "                            fin.loc[k, \"weight_in_gm\"] / 1000\n", "                        )\n", "                        load_diff = load_diff - (\n", "                            fin.loc[k, \"case_size\"] * fin.loc[k, \"weight_in_gm\"] / 1000\n", "                        )\n", "\n", "                    k += 1\n", "\n", "                else:\n", "                    k = i\n", "\n", "            i = i + (fin.loc[i, \"item_count\"] - 1)\n", "\n", "        else:\n", "            fin.loc[i, \"fin_load\"] = fin.loc[i, \"curr_load\"]\n", "            fin.loc[i, \"fin_qty\"] = fin.loc[i, \"qty_case_adjusted\"]\n", "\n", "    i += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"]).agg(\n", "    {\"fin_load\": \"sum\", \"fin_qty\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Inventory at GRN"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po = open_po.groupby([\"facility_id\", \"item_id\"]).agg({\"expected_po_qty\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if po.empty:\n", "    fin[\"expected_po_qty\"] = 0\n", "else:\n", "    fin = fin.merge(po, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "    fin.expected_po_qty.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin[\"inv_at_grn\"] = (fin[\"cold_inv\"] + fin[\"expected_po_qty\"]) - (\n", "    fin[\"tat_days\"] * fin[\"avg_f_cpd\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin[\"inv_at_grn\"] = np.where(fin[\"inv_at_grn\"] < 0, 0, fin[\"inv_at_grn\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage = pb.from_sheets(\"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\", \"Mapping\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage = storage[[\"dark_store\", \"Cold storage (units)\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage.columns = [\"outlet_id\", \"cold_storage\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage.outlet_id = storage.outlet_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = fin.merge(storage, on=\"outlet_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin[\"fin_load\"] = fin[\"fin_load\"].fillna(0)\n", "fin[\"fin_qty\"] = fin[\"fin_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_diff = (\n", "    fin.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"])\n", "    .agg({\"fin_load\": \"sum\", \"fin_qty\": \"sum\", \"expected_po_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "vendor_load_diff.rename(\n", "    columns={\n", "        \"fin_load\": \"vendor_fin_load\",\n", "        \"fin_qty\": \"vendor_fin_qty\",\n", "        \"load_size\": \"vendor_load_size\",\n", "        \"expected_po_qty\": \"vendor_expected_po_qty\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_diff[\"vendor_load_diff\"] = (\n", "    vendor_load_diff[\"vendor_fin_load\"] - vendor_load_diff[\"vendor_load_size\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = fin.merge(vendor_load_diff, on=[\"outlet_id\", \"vendor\", \"manufacturer\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(fin, \"1EoQLuCjzj1rNvA4Rt-u8RX6c_XKjKP61Hoqm2kA4EOw\", \"v1\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_fin = fin.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_fin = storage_fin[storage_fin.storage_type != \"Ambient\"].reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ambient_fin = storage_fin[storage_fin.storage_type == \"Ambient\"].reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_summary = (\n", "    cold_fin.groupby([\"outlet_id\", \"cold_storage\"])\n", "    .agg({\"inv_at_grn\": \"sum\", \"fin_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "storage_summary = storage_summary.rename(columns={\"inv_at_grn\": \"cold_inv\"})\n", "storage_summary.cold_inv = np.ceil(storage_summary.cold_inv)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# taking a buffer of 10 units\n", "storage_summary.cold_storage = storage_summary.cold_storage.astype(int)\n", "storage_summary.cold_storage = storage_summary.cold_storage + 20"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_summary[\"cal_cold_inv\"] = storage_summary[\"cold_inv\"] + storage_summary[\"fin_qty\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_summary.cold_storage = np.where(\n", "    storage_summary.cold_storage == \"\", 0, storage_summary.cold_storage\n", ")\n", "storage_summary.cold_storage = storage_summary.cold_storage.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_exceeds = storage_summary[(storage_summary.cal_cold_inv > storage_summary.cold_storage)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_exceeds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def remove_excess(fin_m_1):\n", "\n", "    fin_m_1[\"fin_qty_1\"] = fin_m_1[\"fin_qty\"]\n", "    fin_m_1[\"fin_qty_2\"] = fin_m_1[\"fin_qty\"]\n", "    fin_m_1[\"fin_load_1\"] = fin_m_1[\"fin_load\"]\n", "    fin_m_1[\"fin_load_2\"] = fin_m_1[\"fin_load\"]\n", "    timeout = time.time() + 10\n", "\n", "    for j in range(0, len(storage_exceeds)):\n", "\n", "        if storage_exceeds.loc[j, \"cold_inv\"] > storage_exceeds.loc[j, \"cold_storage\"]:\n", "            i = 0\n", "            while i < len(fin_m_1):\n", "\n", "                if fin_m_1.loc[i, \"outlet_id\"] == storage_exceeds.loc[j, \"outlet_id\"]:\n", "\n", "                    fin_m_1.loc[i, \"fin_qty_1\"] = 0\n", "                    fin_m_1.loc[i, \"fin_qty_2\"] = 0\n", "                    fin_m_1.loc[i, \"fin_load_1\"] = 0\n", "                    fin_m_1.loc[i, \"fin_load_2\"] = 0\n", "                i = i + 1\n", "\n", "        else:\n", "            i = 0\n", "\n", "            while i < len(fin_m_1):\n", "\n", "                if fin_m_1.loc[i, \"outlet_id\"] == storage_exceeds.loc[j, \"outlet_id\"]:\n", "\n", "                    storage_diff = (\n", "                        storage_exceeds.loc[j, \"cal_cold_inv\"]\n", "                        - storage_exceeds.loc[j, \"cold_storage\"]\n", "                    )\n", "                    l = i\n", "\n", "                    while storage_diff > 0:\n", "                        if time.time() > timeout:\n", "                            break\n", "\n", "                        if l >= len(fin_m_1):\n", "                            break\n", "                        if fin_m_1.loc[l, \"outlet_id\"] != fin_m_1.loc[i, \"outlet_id\"]:\n", "                            l = i\n", "\n", "                        k = l\n", "                        load_diff = np.where(\n", "                            fin_m_1.loc[k, \"vendor_load_diff\"] >= 0,\n", "                            fin_m_1.loc[k, \"vendor_load_diff\"],\n", "                            fin_m_1.loc[k, \"vendor_fin_load\"],\n", "                        )\n", "\n", "                        if (fin_m_1.loc[k, \"vendor_load_diff\"] == fin_m_1.loc[k, \"load_size\"]) | (\n", "                            fin_m_1.loc[k, \"vendor_load_diff\"] < 0\n", "                        ):\n", "                            fin_m_1.loc[k, \"vendor_load_diff\"] = 0\n", "                        else:\n", "                            fin_m_1.loc[k, \"vendor_load_diff\"] = fin_m_1.loc[k, \"load_size\"]\n", "\n", "                        while load_diff > 0:\n", "\n", "                            if time.time() > timeout:\n", "                                break\n", "\n", "                            if (load_diff > 0) & (k >= len(fin_m_1)):\n", "                                k = l\n", "\n", "                            if (\n", "                                (fin_m_1.loc[k, \"outlet_id\"] == fin_m_1.loc[l, \"outlet_id\"])\n", "                                & (fin_m_1.loc[k, \"vendor\"] == fin_m_1.loc[l, \"vendor\"])\n", "                                & (fin_m_1.loc[k, \"manufacturer\"] == fin_m_1.loc[l, \"manufacturer\"])\n", "                            ):\n", "\n", "                                if (fin_m_1.loc[k, \"load_type\"] == \"Case\") & (\n", "                                    fin_m_1.loc[k, \"fin_qty_2\"] > 0\n", "                                ):\n", "                                    if (load_diff - 1) < 0:\n", "                                        break\n", "                                    fin_m_1.loc[k, \"fin_qty_2\"] = (\n", "                                        fin_m_1.loc[k, \"fin_qty_1\"] - fin_m_1.loc[k, \"case_size\"]\n", "                                    )\n", "                                    fin_m_1.loc[k, \"fin_qty_1\"] = fin_m_1.loc[k, \"fin_qty_2\"]\n", "                                    fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"] - 1\n", "                                    fin_m_1.loc[k, \"fin_load_1\"] = fin_m_1.loc[k, \"fin_load_2\"]\n", "                                    load_diff -= 1\n", "                                    storage_diff -= fin_m_1.loc[k, \"case_size\"]\n", "                                    if storage_diff < 0:\n", "                                        break\n", "\n", "                                elif (fin_m_1.loc[k, \"load_type\"] == \"Value\") & (\n", "                                    fin_m_1.loc[k, \"fin_qty_2\"] > 0\n", "                                ):\n", "\n", "                                    if (\n", "                                        load_diff\n", "                                        - (\n", "                                            fin_m_1.loc[k, \"case_size\"]\n", "                                            * fin_m_1.loc[k, \"landing_price\"]\n", "                                        )\n", "                                    ) < 0:\n", "                                        break\n", "                                    fin_m_1.loc[k, \"fin_qty_2\"] = (\n", "                                        fin_m_1.loc[k, \"fin_qty_1\"] - fin_m_1.loc[k, \"case_size\"]\n", "                                    )\n", "                                    fin_m_1.loc[k, \"fin_qty_1\"] = fin_m_1.loc[k, \"fin_qty_2\"]\n", "                                    fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"] - (\n", "                                        fin_m_1.loc[k, \"case_size\"]\n", "                                        * fin_m_1.loc[k, \"landing_price\"]\n", "                                    )\n", "                                    fin_m_1.loc[k, \"fin_load_1\"] = fin_m_1.loc[k, \"fin_load_2\"]\n", "                                    load_diff = load_diff - (\n", "                                        fin_m_1.loc[k, \"case_size\"]\n", "                                        * fin_m_1.loc[k, \"landing_price\"]\n", "                                    )\n", "                                    storage_diff -= fin_m_1.loc[k, \"case_size\"]\n", "                                    if storage_diff < 0:\n", "                                        break\n", "\n", "                                elif (fin_m_1.loc[k, \"load_type\"] == \"Weight\") & (\n", "                                    fin_m_1.loc[k, \"fin_qty_2\"] > 0\n", "                                ):\n", "                                    if (\n", "                                        load_diff\n", "                                        - (\n", "                                            (fin_m_1.loc[k, \"weight_in_gm\"] / 1000)\n", "                                            * fin_m_1.loc[k, \"case_size\"]\n", "                                        )\n", "                                    ) < 0:\n", "                                        break\n", "                                    fin_m_1.loc[k, \"fin_qty_2\"] = (\n", "                                        fin_m_1.loc[k, \"fin_qty_1\"] - fin_m_1.loc[k, \"case_size\"]\n", "                                    )\n", "                                    fin_m_1.loc[k, \"fin_qty_1\"] = fin_m_1.loc[k, \"fin_qty_2\"]\n", "                                    fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"] - (\n", "                                        (fin_m_1.loc[k, \"weight_in_gm\"] / 1000)\n", "                                        * fin_m_1.loc[k, \"case_size\"]\n", "                                    )\n", "                                    fin_m_1.loc[k, \"fin_load_1\"] = fin_m_1.loc[k, \"fin_load_2\"]\n", "                                    load_diff = load_diff - (\n", "                                        (fin_m_1.loc[k, \"weight_in_gm\"] / 1000)\n", "                                        * fin_m_1.loc[k, \"case_size\"]\n", "                                    )\n", "                                    storage_diff -= fin_m_1.loc[k, \"case_size\"]\n", "                                    if storage_diff < 0:\n", "                                        break\n", "\n", "                                else:\n", "                                    fin_m_1.loc[k, \"fin_qty_2\"] = fin_m_1.loc[k, \"fin_qty_1\"]\n", "                                    fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"]\n", "\n", "                                k += 1\n", "\n", "                            else:\n", "                                k = l\n", "\n", "                        l = l + (fin_m_1.loc[l, \"item_count\"])\n", "\n", "                    i = i + len(fin_m_1)\n", "\n", "                else:\n", "\n", "                    i = i + 1\n", "\n", "    return fin_m_1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Method 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_fin.drop(\"index\", axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_fin_1 = cold_fin.sort_values(\n", "    by=[\n", "        \"outlet_id\",\n", "        \"vendor_load_diff\",\n", "        \"vendor_expected_po_qty\",\n", "        \"vendor_net_score\",\n", "        \"net_score\",\n", "    ],\n", "    ascending=[True, False, False, True, True],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_1 = cold_fin_1.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# remove excess in method 1\n", "fin_m_1 = remove_excess(fin_m_1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_1.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"]).agg(\n", "    {\"fin_load\": \"sum\", \"fin_qty\": \"sum\", \"fin_load_2\": \"sum\", \"fin_qty_2\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_qty = fin_m_1.groupby([\"outlet_id\"]).agg({\"fin_qty_2\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_exceeds_2 = storage_exceeds.copy()\n", "\n", "for i in range(0, len(new_qty)):\n", "    #     print(new_qty.loc[i, \"outlet_id\"])\n", "    storage_exceeds_2.fin_qty = np.where(\n", "        storage_exceeds_2.outlet_id == new_qty.loc[i, \"outlet_id\"],\n", "        new_qty.loc[i, \"fin_qty_2\"],\n", "        storage_exceeds_2.fin_qty,\n", "    )\n", "\n", "storage_exceeds_2.cal_cold_inv = storage_exceeds_2.cold_inv + storage_exceeds_2.fin_qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_exceeds_2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_fin_load = (\n", "    fin_m_1.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"])\n", "    .agg({\"fin_load_2\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(0, len(vendor_fin_load)):\n", "    fin_m_1.vendor_load_diff = np.where(\n", "        (fin_m_1.outlet_id == vendor_fin_load.loc[i, \"outlet_id\"])\n", "        & (fin_m_1.vendor == vendor_fin_load.loc[i, \"vendor\"])\n", "        & (fin_m_1.manufacturer == vendor_fin_load.loc[i, \"manufacturer\"]),\n", "        vendor_fin_load.loc[i, \"fin_load_2\"],\n", "        fin_m_1.vendor_load_diff,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_1 = fin_m_1.sort_values(\n", "    by=[\"outlet_id\", \"vendor_expected_po_qty\", \"vendor_net_score\", \"net_score\"],\n", "    ascending=[True, False, True, True],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["timeout = time.time() + 10\n", "\n", "for j in range(0, len(storage_exceeds_2)):\n", "\n", "    if storage_exceeds_2.loc[j, \"cold_inv\"] > storage_exceeds_2.loc[j, \"cold_storage\"]:\n", "        i = 0\n", "        while i < len(fin_m_1):\n", "\n", "            if fin_m_1.loc[i, \"outlet_id\"] == storage_exceeds_2.loc[j, \"outlet_id\"]:\n", "\n", "                fin_m_1.loc[i, \"fin_qty_1\"] = 0\n", "                fin_m_1.loc[i, \"fin_qty_2\"] = 0\n", "                fin_m_1.loc[i, \"fin_load_1\"] = 0\n", "                fin_m_1.loc[i, \"fin_load_2\"] = 0\n", "            i = i + 1\n", "\n", "    else:\n", "        i = 0\n", "\n", "        while i < len(fin_m_1):\n", "\n", "            if fin_m_1.loc[i, \"outlet_id\"] == storage_exceeds_2.loc[j, \"outlet_id\"]:\n", "\n", "                storage_diff = (\n", "                    storage_exceeds_2.loc[j, \"cal_cold_inv\"]\n", "                    - storage_exceeds_2.loc[j, \"cold_storage\"]\n", "                )\n", "                l = i\n", "                while storage_diff > 0:\n", "                    if time.time() > timeout:\n", "                        break\n", "\n", "                    if l >= len(fin_m_1):\n", "                        l = i\n", "                    if fin_m_1.loc[l, \"outlet_id\"] != fin_m_1.loc[i, \"outlet_id\"]:\n", "                        l = i\n", "\n", "                    k = l\n", "                    load_diff = np.where(\n", "                        fin_m_1.loc[k, \"vendor_load_diff\"] >= 0,\n", "                        fin_m_1.loc[k, \"vendor_load_diff\"],\n", "                        fin_m_1.loc[k, \"vendor_fin_load\"],\n", "                    )\n", "\n", "                    if (fin_m_1.loc[k, \"vendor_load_diff\"] == fin_m_1.loc[k, \"load_size\"]) | (\n", "                        fin_m_1.loc[k, \"vendor_load_diff\"] <= 0\n", "                    ):\n", "                        fin_m_1.loc[k, \"vendor_load_diff\"] = 0\n", "                    else:\n", "                        fin_m_1.loc[k, \"vendor_load_diff\"] = fin_m_1.loc[k, \"load_size\"]\n", "\n", "                    while load_diff > 0:\n", "\n", "                        if time.time() > timeout:\n", "                            break\n", "\n", "                        if (load_diff > 0) & (k >= len(fin_m_1)):\n", "                            k = l\n", "\n", "                        if (\n", "                            (fin_m_1.loc[k, \"outlet_id\"] == fin_m_1.loc[l, \"outlet_id\"])\n", "                            & (fin_m_1.loc[k, \"vendor\"] == fin_m_1.loc[l, \"vendor\"])\n", "                            & (fin_m_1.loc[k, \"manufacturer\"] == fin_m_1.loc[l, \"manufacturer\"])\n", "                        ):\n", "\n", "                            if (fin_m_1.loc[k, \"load_type\"] == \"Case\") & (\n", "                                fin_m_1.loc[k, \"fin_qty_2\"] > 0\n", "                            ):\n", "                                if (load_diff - 1) < 0:\n", "                                    break\n", "                                fin_m_1.loc[k, \"fin_qty_2\"] = (\n", "                                    fin_m_1.loc[k, \"fin_qty_1\"] - fin_m_1.loc[k, \"case_size\"]\n", "                                )\n", "                                fin_m_1.loc[k, \"fin_qty_1\"] = fin_m_1.loc[k, \"fin_qty_2\"]\n", "                                fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"] - 1\n", "                                fin_m_1.loc[k, \"fin_load_1\"] = fin_m_1.loc[k, \"fin_load_2\"]\n", "                                load_diff -= 1\n", "                                storage_diff -= fin_m_1.loc[k, \"case_size\"]\n", "                                if storage_diff < 0:\n", "                                    break\n", "\n", "                            elif (fin_m_1.loc[k, \"load_type\"] == \"Value\") & (\n", "                                fin_m_1.loc[k, \"fin_qty_2\"] > 0\n", "                            ):\n", "\n", "                                if (\n", "                                    load_diff\n", "                                    - (\n", "                                        fin_m_1.loc[k, \"case_size\"]\n", "                                        * fin_m_1.loc[k, \"landing_price\"]\n", "                                    )\n", "                                ) < 0:\n", "                                    break\n", "                                fin_m_1.loc[k, \"fin_qty_2\"] = (\n", "                                    fin_m_1.loc[k, \"fin_qty_1\"] - fin_m_1.loc[k, \"case_size\"]\n", "                                )\n", "                                fin_m_1.loc[k, \"fin_qty_1\"] = fin_m_1.loc[k, \"fin_qty_2\"]\n", "                                fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"] - (\n", "                                    fin_m_1.loc[k, \"case_size\"] * fin_m_1.loc[k, \"landing_price\"]\n", "                                )\n", "                                fin_m_1.loc[k, \"fin_load_1\"] = fin_m_1.loc[k, \"fin_load_2\"]\n", "                                load_diff = load_diff - (\n", "                                    fin_m_1.loc[k, \"case_size\"] * fin_m_1.loc[k, \"landing_price\"]\n", "                                )\n", "                                storage_diff -= fin_m_1.loc[k, \"case_size\"]\n", "                                if storage_diff < 0:\n", "                                    break\n", "\n", "                            elif (fin_m_1.loc[k, \"load_type\"] == \"Weight\") & (\n", "                                fin_m_1.loc[k, \"fin_qty_2\"] > 0\n", "                            ):\n", "                                if (\n", "                                    load_diff\n", "                                    - (\n", "                                        (fin_m_1.loc[k, \"weight_in_gm\"] / 1000)\n", "                                        * fin_m_1.loc[k, \"case_size\"]\n", "                                    )\n", "                                ) < 0:\n", "                                    break\n", "                                fin_m_1.loc[k, \"fin_qty_2\"] = (\n", "                                    fin_m_1.loc[k, \"fin_qty_1\"] - fin_m_1.loc[k, \"case_size\"]\n", "                                )\n", "                                fin_m_1.loc[k, \"fin_qty_1\"] = fin_m_1.loc[k, \"fin_qty_2\"]\n", "                                fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"] - (\n", "                                    (fin_m_1.loc[k, \"weight_in_gm\"] / 1000)\n", "                                    * fin_m_1.loc[k, \"case_size\"]\n", "                                )\n", "                                fin_m_1.loc[k, \"fin_load_1\"] = fin_m_1.loc[k, \"fin_load_2\"]\n", "                                load_diff = load_diff - (\n", "                                    (fin_m_1.loc[k, \"weight_in_gm\"] / 1000)\n", "                                    * fin_m_1.loc[k, \"case_size\"]\n", "                                )\n", "                                storage_diff -= fin_m_1.loc[k, \"case_size\"]\n", "                                if storage_diff < 0:\n", "                                    break\n", "\n", "                            else:\n", "                                fin_m_1.loc[k, \"fin_qty_2\"] = fin_m_1.loc[k, \"fin_qty_1\"]\n", "                                fin_m_1.loc[k, \"fin_load_2\"] = fin_m_1.loc[k, \"fin_load_1\"]\n", "\n", "                            k += 1\n", "\n", "                        else:\n", "                            k = l\n", "\n", "                    l = l + (fin_m_1.loc[l, \"item_count\"])\n", "\n", "                i = i + len(fin_m_1)\n", "\n", "            else:\n", "\n", "                i = i + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_check = (\n", "    fin_m_1.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"])\n", "    .agg({\"fin_load\": \"sum\", \"fin_qty\": \"sum\", \"fin_load_2\": \"sum\", \"fin_qty_2\": \"sum\"})\n", "    .reset_index()\n", ")\n", "load_check = load_check[\n", "    (load_check.fin_load_2 < load_check.load_size) & (load_check.fin_load_2 != 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_check = load_check.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for j in range(0, len(load_check)):\n", "    for i in range(0, len(fin_m_1)):\n", "        if (\n", "            (fin_m_1.loc[i, \"outlet_id\"] == load_check.loc[j, \"outlet_id\"])\n", "            & (fin_m_1.loc[i, \"vendor\"] == load_check.loc[j, \"vendor\"])\n", "            & (fin_m_1.loc[i, \"manufacturer\"] == load_check.loc[j, \"manufacturer\"])\n", "        ):\n", "            fin_m_1.loc[i, \"fin_qty_2\"] = 0\n", "            fin_m_1.loc[i, \"fin_load_2\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_1.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"]).agg(\n", "    {\"fin_load\": \"sum\", \"fin_qty\": \"sum\", \"fin_load_2\": \"sum\", \"fin_qty_2\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Method 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_fin_2 = cold_fin.sort_values(\n", "    by=[\n", "        \"outlet_id\",\n", "        \"vendor_load_diff\",\n", "        \"vendor_expected_po_qty\",\n", "        \"vendor_net_score\",\n", "        \"net_score\",\n", "    ],\n", "    ascending=[True, False, False, True, True],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2 = cold_fin_2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_exceeds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# remove excess in method 1\n", "fin_m_2 = remove_excess(fin_m_2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"]).agg(\n", "    {\"fin_load\": \"sum\", \"fin_qty\": \"sum\", \"fin_load_2\": \"sum\", \"fin_qty_2\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_qty = fin_m_2.groupby([\"outlet_id\"]).agg({\"fin_qty_2\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_exceeds_2 = storage_exceeds.copy()\n", "\n", "for i in range(0, len(new_qty)):\n", "    #     print(new_qty.loc[i, \"outlet_id\"])\n", "    storage_exceeds_2.fin_qty = np.where(\n", "        storage_exceeds_2.outlet_id == new_qty.loc[i, \"outlet_id\"],\n", "        new_qty.loc[i, \"fin_qty_2\"],\n", "        storage_exceeds_2.fin_qty,\n", "    )\n", "\n", "storage_exceeds_2.cal_cold_inv = storage_exceeds_2.cold_inv + storage_exceeds_2.fin_qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_exceeds_2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_fin_load = (\n", "    fin_m_2.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"])\n", "    .agg({\"fin_qty_2\": \"sum\", \"fin_load_2\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_fin_load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(0, len(vendor_fin_load)):\n", "    fin_m_2.vendor_fin_qty = np.where(\n", "        (fin_m_2.outlet_id == vendor_fin_load.loc[i, \"outlet_id\"])\n", "        & (fin_m_2.vendor == vendor_fin_load.loc[i, \"vendor\"])\n", "        & (fin_m_2.manufacturer == vendor_fin_load.loc[i, \"manufacturer\"]),\n", "        vendor_fin_load.loc[i, \"fin_qty_2\"],\n", "        fin_m_2.vendor_fin_qty,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2 = fin_m_2.sort_values(\n", "    by=[\n", "        \"outlet_id\",\n", "        \"vendor_expected_po_qty\",\n", "        \"vendor_fin_qty\",\n", "        \"vendor_net_score\",\n", "        \"net_score\",\n", "    ],\n", "    ascending=[True, False, False, True, True],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["timeout = time.time() + 10\n", "\n", "for j in range(0, len(storage_exceeds_2)):\n", "\n", "    if storage_exceeds_2.loc[j, \"cold_inv\"] > storage_exceeds_2.loc[j, \"cold_storage\"]:\n", "        i = 0\n", "        while i < len(fin_m_2):\n", "\n", "            if fin_m_2.loc[i, \"outlet_id\"] == storage_exceeds_2.loc[j, \"outlet_id\"]:\n", "\n", "                fin_m_2.loc[i, \"fin_qty_1\"] = 0\n", "                fin_m_2.loc[i, \"fin_qty_2\"] = 0\n", "                fin_m_2.loc[i, \"fin_load_1\"] = 0\n", "                fin_m_2.loc[i, \"fin_load_2\"] = 0\n", "            i = i + 1\n", "    else:\n", "        i = 0\n", "        while i < len(fin_m_2):\n", "\n", "            if fin_m_2.loc[i, \"outlet_id\"] == storage_exceeds_2.loc[j, \"outlet_id\"]:\n", "\n", "                storage_diff = (\n", "                    storage_exceeds_2.loc[j, \"cal_cold_inv\"]\n", "                    - storage_exceeds_2.loc[j, \"cold_storage\"]\n", "                )\n", "                l = i\n", "\n", "                while storage_diff > 0:\n", "\n", "                    if time.time() > timeout:\n", "                        break\n", "\n", "                    k = l\n", "\n", "                    qty = fin_m_2.loc[k, \"vendor_fin_qty\"]\n", "\n", "                    while qty > 0:\n", "\n", "                        if time.time() > timeout:\n", "                            break\n", "\n", "                        if (\n", "                            (fin_m_2.loc[k, \"outlet_id\"] == fin_m_2.loc[l, \"outlet_id\"])\n", "                            & (fin_m_2.loc[k, \"vendor\"] == fin_m_2.loc[l, \"vendor\"])\n", "                            & (fin_m_2.loc[k, \"manufacturer\"] == fin_m_2.loc[l, \"manufacturer\"])\n", "                        ):\n", "\n", "                            if (fin_m_2.loc[k, \"load_type\"] == \"Case\") & (\n", "                                fin_m_2.loc[k, \"fin_qty_2\"] > 0\n", "                            ):\n", "                                qty = np.floor(qty - fin_m_2.loc[k, \"fin_qty_2\"])\n", "                                storage_diff -= fin_m_2.loc[k, \"fin_qty_2\"]\n", "\n", "                                fin_m_2.loc[k, \"fin_qty_2\"] = 0\n", "                                fin_m_2.loc[k, \"fin_qty_1\"] = 0\n", "                                fin_m_2.loc[k, \"fin_load_1\"] = 0\n", "                                fin_m_2.loc[k, \"fin_load_2\"] = 0\n", "                                if storage_diff < 0:\n", "                                    break\n", "\n", "                            elif (fin_m_2.loc[k, \"load_type\"] == \"Value\") & (\n", "                                fin_m_2.loc[k, \"fin_qty_2\"] > 0\n", "                            ):\n", "                                qty = np.floor(qty - fin_m_2.loc[k, \"fin_qty_2\"])\n", "                                storage_diff -= fin_m_2.loc[k, \"fin_qty_2\"]\n", "\n", "                                fin_m_2.loc[k, \"fin_qty_2\"] = 0\n", "                                fin_m_2.loc[k, \"fin_qty_1\"] = 0\n", "                                fin_m_2.loc[k, \"fin_load_1\"] = 0\n", "                                fin_m_2.loc[k, \"fin_load_2\"] = 0\n", "                                if storage_diff < 0:\n", "                                    break\n", "\n", "                            elif (fin_m_2.loc[k, \"load_type\"] == \"Weight\") & (\n", "                                fin_m_2.loc[k, \"fin_qty_2\"] > 0\n", "                            ):\n", "                                qty = np.floor(qty - fin_m_2.loc[k, \"fin_qty_2\"])\n", "                                storage_diff -= fin_m_2.loc[k, \"fin_qty_2\"]\n", "\n", "                                fin_m_2.loc[k, \"fin_qty_2\"] = 0\n", "                                fin_m_2.loc[k, \"fin_qty_1\"] = 0\n", "                                fin_m_2.loc[k, \"fin_load_1\"] = 0\n", "                                fin_m_2.loc[k, \"fin_load_2\"] = 0\n", "                                if storage_diff < 0:\n", "                                    break\n", "                            else:\n", "                                fin_m_2.loc[k, \"fin_qty_2\"] = fin_m_2.loc[k, \"fin_qty_1\"]\n", "                                fin_m_2.loc[k, \"fin_load_2\"] = fin_m_2.loc[k, \"fin_load\"]\n", "\n", "                            k += 1\n", "\n", "                        else:\n", "                            k = l\n", "\n", "                    l = l + (fin_m_2.loc[l, \"item_count\"])\n", "\n", "                i = i + len(fin_m_2)\n", "\n", "            else:\n", "\n", "                i = i + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_check = (\n", "    fin_m_2.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"])\n", "    .agg({\"fin_load\": \"sum\", \"fin_qty\": \"sum\", \"fin_load_2\": \"sum\", \"fin_qty_2\": \"sum\"})\n", "    .reset_index()\n", ")\n", "load_check = load_check[\n", "    (load_check.fin_load_2 < load_check.load_size) & (load_check.fin_load_2 != 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_check = load_check.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for j in range(0, len(load_check)):\n", "    for i in range(0, len(fin_m_2)):\n", "        if (\n", "            (fin_m_2.loc[i, \"outlet_id\"] == load_check.loc[j, \"outlet_id\"])\n", "            & (fin_m_2.loc[i, \"vendor\"] == load_check.loc[j, \"vendor\"])\n", "            & (fin_m_2.loc[i, \"manufacturer\"] == load_check.loc[j, \"manufacturer\"])\n", "        ):\n", "            fin_m_2.loc[i, \"fin_qty_2\"] = 0\n", "            fin_m_2.loc[i, \"fin_load_2\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2.groupby([\"outlet_id\", \"vendor\", \"manufacturer\", \"load_size\"]).agg(\n", "    {\"fin_load\": \"sum\", \"fin_qty\": \"sum\", \"fin_load_2\": \"sum\", \"fin_qty_2\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_1.fin_qty_2.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_m_2.fin_qty_2.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import calendar\n", "\n", "day = calendar.day_name[today.weekday()]\n", "weekday = day[0:3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["weekday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ambient_fin_1 = pd.DataFrame()\n", "ambient_fin[\"fin_qty_2\"] = ambient_fin[\"fin_qty\"]\n", "ambient_fin[\"fin_load_2\"] = ambient_fin[\"fin_load\"]\n", "\n", "for i in range(0, len(ambient_fin)):\n", "    if bool(re.search(weekday, ambient_fin.loc[i, \"po_day\"])):\n", "        ambient_fin_1 = ambient_fin_1.append(ambient_fin.loc[i])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if fin_m_1.fin_qty_2.sum() != 0:\n", "    fin_m_1 = fin_m_1.append(ambient_fin_1)\n", "    pb.to_sheets(fin_m_1, \"1EoQLuCjzj1rNvA4Rt-u8RX6c_XKjKP61Hoqm2kA4EOw\", \"v2\")\n", "    final = fin_m_1[fin_m_1.fin_qty_2 > 0][\n", "        [\"item_id\", \"item_name\", \"outlet_id\", \"vendor\", \"manufacturer\", \"fin_qty_2\"]\n", "    ].rename(columns={\"fin_qty_2\": \"qty\"})\n", "    fin = fin_m_1.copy()\n", "else:\n", "    fin_m_2 = fin_m_2.append(ambient_fin_1)\n", "    pb.to_sheets(fin_m_2, \"1EoQLuCjzj1rNvA4Rt-u8RX6c_XKjKP61Hoqm2kA4EOw\", \"v2\")\n", "    final = fin_m_2[fin_m_2.fin_qty_2 > 0][\n", "        [\"item_id\", \"item_name\", \"outlet_id\", \"vendor\", \"manufacturer\", \"fin_qty_2\"]\n", "    ].rename(columns={\"fin_qty_2\": \"qty\"})\n", "    fin = fin_m_2.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.drop(\n", "    [\n", "        \"index\",\n", "        \"fin_qty_1\",\n", "        \"fin_load_1\",\n", "        \"vendor_load_diff\",\n", "        \"vendor_fin_qty\",\n", "        \"vendor_fin_load\",\n", "        \"load_size1\",\n", "        \"curr_load\",\n", "        \"curr_load_on_vendor\",\n", "        \"max_shelf_life\",\n", "        \"min_case_size\",\n", "        \"item_count\",\n", "        \"vendor_load_size\",\n", "    ],\n", "    inplace=True,\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.item_id = fin.item_id.astype(int)\n", "fin.outlet_id = fin.outlet_id.astype(int)\n", "fin.facility_id = fin.facility_id.astype(int)\n", "fin.weight_in_gm = fin.weight_in_gm.astype(int)\n", "fin.cold_inv = fin.cold_inv.astype(int)\n", "fin.avg_f_cpd.fillna(0, inplace=True)\n", "fin.avg_f_cpd = fin.avg_f_cpd.astype(float)\n", "\n", "fin.load_size = fin.load_size.astype(int)\n", "fin.shelf_life = fin.shelf_life.astype(int)\n", "fin.case_size = fin.case_size.astype(int)\n", "fin.net_score = fin.net_score.astype(float)\n", "fin.vendor_net_score = fin.vendor_net_score.astype(float)\n", "\n", "fin.tat_days.fillna(2, inplace=True)\n", "fin.tat_days = fin.tat_days.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin = fin[\n", "    [\n", "        \"updated_at\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"facility_id\",\n", "        \"brand\",\n", "        \"vendor\",\n", "        \"manufacturer\",\n", "        \"po_day\",\n", "        \"tat_days\",\n", "        \"cold_inv\",\n", "        \"case_size\",\n", "        \"weight_in_gm\",\n", "        \"landing_price\",\n", "        \"avg_f_cpd\",\n", "        \"gmv\",\n", "        \"net_score\",\n", "        \"storage_type\",\n", "        \"load_type\",\n", "        \"load_size\",\n", "        \"shelf_life\",\n", "        \"qty_required\",\n", "        \"qty_capped\",\n", "        \"qty_case_adjusted\",\n", "        \"vendor_net_score\",\n", "        \"fin_load\",\n", "        \"fin_qty\",\n", "        \"expected_po_qty\",\n", "        \"inv_at_grn\",\n", "        \"cold_storage\",\n", "        \"fin_qty_2\",\n", "        \"fin_load_2\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar(60)\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar(40)\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "    {\"name\": \"brand\", \"type\": \"varchar(20)\"},\n", "    {\"name\": \"vendor\", \"type\": \"varchar(40)\"},\n", "    {\"name\": \"manufacturer\", \"type\": \"varchar(50)\"},\n", "    {\"name\": \"po_day\", \"type\": \"varchar(20)\"},\n", "    {\"name\": \"tat_days\", \"type\": \"integer\"},\n", "    {\"name\": \"cold_inv\", \"type\": \"integer\"},\n", "    {\"name\": \"case_size\", \"type\": \"integer\"},\n", "    {\"name\": \"weight_in_gm\", \"type\": \"integer\"},\n", "    {\"name\": \"landing_price\", \"type\": \"float\"},\n", "    {\"name\": \"avg_f_cpd\", \"type\": \"float\"},\n", "    {\"name\": \"gmv\", \"type\": \"float\"},\n", "    {\"name\": \"net_score\", \"type\": \"float\"},\n", "    {\"name\": \"storage_type\", \"type\": \"varchar(40)\"},\n", "    {\"name\": \"load_type\", \"type\": \"varchar(10)\"},\n", "    {\"name\": \"load_size\", \"type\": \"integer\"},\n", "    {\"name\": \"shelf_life\", \"type\": \"integer\"},\n", "    {\"name\": \"qty_required\", \"type\": \"float\"},\n", "    {\"name\": \"qty_capped\", \"type\": \"float\"},\n", "    {\"name\": \"qty_case_adjusted\", \"type\": \"float\"},\n", "    {\"name\": \"vendor_net_score\", \"type\": \"float\"},\n", "    {\"name\": \"fin_load\", \"type\": \"float\"},\n", "    {\"name\": \"fin_qty\", \"type\": \"float\"},\n", "    {\"name\": \"expected_po_qty\", \"type\": \"float\"},\n", "    {\"name\": \"inv_at_grn\", \"type\": \"float\"},\n", "    {\"name\": \"cold_storage\", \"type\": \"float\"},\n", "    {\"name\": \"fin_qty_2\", \"type\": \"float\"},\n", "    {\"name\": \"fin_load_2\", \"type\": \"float\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"perishables_indenting_summary\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"outlet_id\", \"updated_at\"],\n", "    \"sortkey\": [\"updated_at\", \"item_id\", \"outlet_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(fin, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.groupby([\"outlet_id\", \"vendor\", \"manufacturer\"]).agg({\"qty\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_items = fin2.groupby(\"outlet_id\").agg({\"item_id\": \"nunique\"}).reset_index()\n", "total_items = total_items.rename(columns={\"item_id\": \"total items\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_with_indent = final.groupby(\"outlet_id\").agg({\"item_id\": \"nunique\"}).reset_index()\n", "items_with_indent = items_with_indent.rename(columns={\"item_id\": \"items with indent\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent = total_items.merge(items_with_indent, on=[\"outlet_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent.outlet_id = np.where(indent.outlet_id == 846, 845, indent.outlet_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent = indent.to_html(index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.outlet_id = np.where(final.outlet_id == 846, 845, final.outlet_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final.to_csv(\"Perishables Indent \" + today.strftime(\"%Y-%m-%d\") + \".csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email = pb.from_sheets(\"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\", \"Emails\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email = email.append(\n", "    pd.DataFrame({\"emails\": [\"<EMAIL>\"]}), ignore_index=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sending mailers\n", "import jinja2\n", "from datetime import date\n", "from jinja2 import Template\n", "\n", "from_email = \"<EMAIL>\"\n", "subject = \"Perishable Indent for %s\" % date.today()\n", "to_email = [x.strip() for x in list(email[\"emails\"]) if len(x.strip()) > 0]\n", "# to_email = [\"<EMAIL>\", ]\n", "\n", "if final.qty.empty:\n", "    html_content = \"\"\"\n", "    <head></head><p>Hi <PERSON>,</p> <p>No indent to be raised for today as any new order would breach storage capacity of the store.\n", "    <p> <p><PERSON><PERSON>,<br />Data Bangalore</p>\"\"\"\n", "    files = []\n", "else:\n", "    html_content = (\n", "        \"\"\"\n", "    <head></head><p>Hi <PERSON>,</p> <p>Please find attached perishables indent for today.  <br><br> \"\"\"\n", "        + indent\n", "        + \"\"\"<br><br>\n", "    <p> <p><PERSON><PERSON>,<br />Data Bangalore</p>\"\"\"\n", "    )\n", "    files = [\"Perishables Indent \" + today.strftime(\"%Y-%m-%d\") + \".csv\"]\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    files,\n", "    #     bcc = [\"<EMAIL>\",],\n", ")\n", "\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}