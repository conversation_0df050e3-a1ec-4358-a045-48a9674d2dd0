{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import difflib\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "\n", "now1 = datetime.now()\n", "\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet2 = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "cater_ds_mapping = pb.from_sheets(sheet2, \"Mapping\")\n", "cater_ds_mapping = cater_ds_mapping[[\"front_end\", \"dark_store\"]].drop_duplicates()\n", "cater_ds_mapping.rename(columns={\"front_end\": \"outlet_id\", \"dark_store\": \"ds_outlet\"}, inplace=True)\n", "cater_ds_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "next_Date = datetime.date.today() + datetime.timedelta(days=1)\n", "OUTLET_ID = cater_ds_mapping[\"ds_outlet\"].to_list()\n", "OUTLET_ID = list(dict.fromkeys(OUTLET_ID))\n", "catering_outlet = cater_ds_mapping[\"outlet_id\"].to_list()\n", "catering_outlet = list(dict.fromkeys(catering_outlet))\n", "cater_outlets = tuple(catering_outlet)\n", "ds_outlets = tuple(OUTLET_ID)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CPD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select distinct ro.facility_id,\n", "dwc.date as delivery_date,\n", "dwc.item_id, \n", "dwc.consumption as cpd \n", "from \n", "(select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "date between current_date + interval 1 day\n", "and current_date + interval 1 week) dwc\n", "join \n", "retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join retail.console_outlet ro on ro.id=dwc.outlet_id\n", "where wom.cloud_store_id in %(outlet)s\n", "\"\"\"\n", "cpd = pd.read_sql_query(cpd_query, retail, params={\"outlet\": cater_outlets})\n", "cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Static_assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT catering_facility as facility_id,\n", "       ds_outlet,\n", "       item_id\n", "FROM metrics.dark_store_assortment_static\n", "\"\"\"\n", "assortment = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.groupby([\"ds_outlet\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orders capacity catering facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reading slot wise capacity details from capacity system and manipulation\n", "query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "delivery_slot_type,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "\twarehouse_external_id AS backend_merchant_id,\n", "\twarehouse_name AS backend_merchant_name,\n", "\tDATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "\tTO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "\tTO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "\tslot_type,\n", "    CASE WHEN EXTRACT(HOUR FROM (slot_start AT TIME ZONE 'ASIA/KOLKATA'))< 16 THEN 'SLOT A' ELSE  'SLOT B' END AS delivery_slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "\twarehouse_planned AS given_capacity,\n", "\twarehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between date(current_date + interval '1 days') and date(current_date + interval '15 days')\n", "\n", "and given_capacity > 0\n", "group by 1,2,3,4\n", "order by 2,3;\n", "\"\"\"\n", "capacity_details = pd.read_sql_query(query, capacity_system)\n", "\n", "query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store\n", "from\n", "consumer.oms_merchant m\n", "join consumer.rt_console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join consumer.pos_console_outlet o on cms.outlet_id= o.id\n", "where cms.outlet_id in %(outlet)s \n", "and cms.active = 1 \n", "and cms.cms_update_active = 1;\n", "\"\"\"\n", "cms_outlet_details = pd.read_sql_query(query, redshift, params={\"outlet\": cater_outlets})\n", "\n", "warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"delivery_date\",\n", "            \"delivery_slot_type\",\n", "            \"planned_capacity\",\n", "            \"backend_merchant_name\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "warehouse_capacities = (\n", "    warehouse_capacities.groupby([\"delivery_date\", \"outlet_id\", \"outlet\"])\n", "    .agg({\"Capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "warehouse_capacities[\"delivery_date\"] = pd.to_datetime(warehouse_capacities[\"delivery_date\"])\n", "warehouse_capacities.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities[\"outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,\n", "facility_id\n", "from\n", "retail.console_outlet\n", "where active=1\"\"\"\n", "facility = pd.read_sql_query(query, retail)\n", "facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities_back = warehouse_capacities.merge(facility, on=[\"outlet_id\"], how=\"left\")\n", "warehouse_capacities_back.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Order capacity dark stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reading slot wise capacity details from capacity system and manipulation\n", "query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "delivery_slot_type,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "\twarehouse_external_id AS backend_merchant_id,\n", "\twarehouse_name AS backend_merchant_name,\n", "\tDATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "\tTO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "\tTO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "\tslot_type,\n", "    CASE WHEN EXTRACT(HOUR FROM (slot_start AT TIME ZONE 'ASIA/KOLKATA'))< 16 THEN 'SLOT A' ELSE  'SLOT B' END AS delivery_slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "\twarehouse_planned AS given_capacity,\n", "\twarehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between  date(current_date + interval '1 days') and date(current_date + interval '20 days')\n", "\n", "and given_capacity > 0\n", "group by 1,2,3,4\n", "order by 2,3;\n", "\"\"\"\n", "capacity_details_ds = pd.read_sql_query(query, capacity_system)\n", "\n", "query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store\n", "from\n", "consumer.oms_merchant m\n", "join consumer.rt_console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join consumer.pos_console_outlet o on cms.outlet_id= o.id\n", "where cms.outlet_id in %(outlet)s \n", "and cms.active = 1 \n", "and cms.cms_update_active = 1;\n", "\"\"\"\n", "cms_outlet_details_ds = pd.read_sql_query(query, redshift, params={\"outlet\": ds_outlets})\n", "\n", "warehouse_capacities_ds = (\n", "    capacity_details_ds.merge(\n", "        cms_outlet_details_ds,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"delivery_date\",\n", "            \"delivery_slot_type\",\n", "            \"planned_capacity\",\n", "            \"backend_merchant_name\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "warehouse_capacities_ds = (\n", "    warehouse_capacities_ds.groupby([\"delivery_date\", \"outlet_id\"])\n", "    .agg({\"Capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "warehouse_capacities_ds[\"delivery_date\"] = pd.to_datetime(warehouse_capacities_ds[\"delivery_date\"])\n", "warehouse_capacities_ds.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities_ds.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity = pb.from_sheets(sheet2, \"capacity\")\n", "capacity = capacity[[\"delivery_date\", \"outlet_id\", \"Capacity\"]].drop_duplicates()\n", "capacity[\"outlet_id\"] = capacity[\"outlet_id\"].astype(\"int\")\n", "capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["available_ds_outlet = list(capacity[\"outlet_id\"].unique())\n", "available_ds_outlet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities_ds = warehouse_capacities_ds[\n", "    ~warehouse_capacities_ds[\"outlet_id\"].isin(available_ds_outlet)\n", "]\n", "warehouse_capacities_dsv2 = pd.concat([warehouse_capacities_ds, capacity]).reset_index()\n", "warehouse_capacities_dsv2.drop([\"index\"], axis=1, inplace=True)\n", "warehouse_capacities_dsv2[\"delivery_date\"] = pd.to_datetime(\n", "    warehouse_capacities_dsv2[\"delivery_date\"]\n", ")\n", "warehouse_capacities_dsv2[\"outlet_id\"] = warehouse_capacities_dsv2[\"outlet_id\"].astype(\"int\")\n", "warehouse_capacities_dsv2 = warehouse_capacities_dsv2.drop_duplicates()\n", "warehouse_capacities_dsv2.sort_values(by=[\"delivery_date\"], inplace=True)\n", "warehouse_capacities_dsv2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities_dsv2[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Backend facility cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd[\"delivery_date\"] = pd.to_datetime(cpd[\"delivery_date\"])\n", "assortment_cap = assortment.merge(warehouse_capacities_back, on=[\"facility_id\"], how=\"left\")\n", "assortment_cap\n", "b_cpd = assortment_cap.merge(cpd, on=[\"delivery_date\", \"facility_id\", \"item_id\"], how=\"left\")\n", "default = b_cpd[\"cpd\"].median()\n", "b_cpd[\"cpd\"] = b_cpd[\"cpd\"].fillna(default)\n", "b_cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mapiing ds facility with backend cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cater_ds_mapping[\"outlet_id\"] = cater_ds_mapping[\"outlet_id\"].astype(\"int\")\n", "warehouse_capacities_dsv2[\"outlet_id\"] = warehouse_capacities_dsv2[\"outlet_id\"].astype(\"int\")\n", "cpd1 = b_cpd.copy()\n", "cpd1[\"ds_outlet\"] = cpd1[\"ds_outlet\"].astype(\"int\")\n", "cpd1 = (\n", "    cpd1.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"ds_outlet\",\n", "            \"item_id\",\n", "            \"delivery_date\",\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"Capacity\",\n", "        ]\n", "    )[[\"cpd\"]]\n", "    .max()\n", "    .reset_index()\n", ")\n", "cpd1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_cpd = cpd1.merge(\n", "    warehouse_capacities_dsv2,\n", "    left_on=[\"delivery_date\", \"ds_outlet\"],\n", "    right_on=[\"delivery_date\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "ds_cpd.rename(\n", "    columns={\n", "        \"outlet_id_x\": \"cater_outlet\",\n", "        \"outlet_x\": \"cater_outlet_name\",\n", "        \"Capacity_x\": \"cater_outlet_capacity\",\n", "        \"outlet_y\": \"ds_outlet_name\",\n", "        \"Capacity_y\": \"ds_capacity\",\n", "    },\n", "    inplace=True,\n", ")\n", "final_cpd = ds_cpd.drop([\"outlet_id_y\"], axis=1)\n", "final_cpd[\"cpd\"] = final_cpd[\"cpd\"].astype(\"float\")\n", "\n", "final_cpd = final_cpd.fillna(0)\n", "final_cpd[\"ds_capacity\"] = final_cpd[\"ds_capacity\"].astype(\"int\")\n", "\n", "final_cpd[\"ds_cpd\"] = (final_cpd[\"cpd\"] * final_cpd[\"ds_capacity\"]) / final_cpd[\n", "    \"cater_outlet_capacity\"\n", "]\n", "final_cpd.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpd = final_cpd.drop_duplicates()\n", "final_cpd.groupby([\"delivery_date\", \"ds_outlet\"])[[\"item_id\"]].count().reset_index().head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding IPO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) AS delivery_date,\n", "       r.id AS ds_outlet,\n", "       count(DISTINCT o.order_id) as total_orders,\n", "       sum(i.quantity) AS quantity,\n", "       sum(i.quantity)/count(DISTINCT o.order_id) as ipo\n", "       \n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) between date_add(current_date,interval -10 day) and current_date\n", "  AND r.id IN %(outlet)s \n", "  AND status_id NOT IN (3,\n", "                        5)\n", "GROUP BY 1,\n", "         2\n", "         \n", "\"\"\"\n", "orders = pd.read_sql_query(query, retail, params={\"outlet\": ds_outlets})\n", "orders.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo = orders.groupby([\"ds_outlet\"])[[\"ipo\"]].median().reset_index()\n", "ipo[\"ipo\"] = np.where(ipo[\"ipo\"] < 14, 14, ipo[\"ipo\"])\n", "ipo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["default_ipo = ipo[\"ipo\"].median()\n", "default_ipo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpd_ipo = final_cpd.merge(ipo, on=[\"ds_outlet\"], how=\"left\")\n", "final_cpd_ipo[\"ipo\"] = final_cpd_ipo[\"ipo\"].fillna(default_ipo)\n", "final_cpd_ipo[\"daily_requirement\"] = final_cpd_ipo[\"ds_capacity\"] * final_cpd_ipo[\"ipo\"]\n", "\n", "final_cpd_ipo.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_ratio = (\n", "    final_cpd_ipo.groupby([\"delivery_date\", \"ds_outlet\", \"daily_requirement\"])\n", "    .agg({\"ds_cpd\": \"sum\"})\n", "    .reset_index()\n", ")\n", "cpd_ratio[\"cpd_ratio\"] = cpd_ratio[\"daily_requirement\"] / cpd_ratio[\"ds_cpd\"]\n", "cpd_ratio.drop([\"daily_requirement\", \"ds_cpd\"], inplace=True, axis=1)\n", "cpd_ratio.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpd_ds = final_cpd_ipo.merge(cpd_ratio, on=[\"delivery_date\", \"ds_outlet\"], how=\"left\")\n", "final_cpd_ds[\"final_cpd\"] = final_cpd_ds[\"ds_cpd\"] * final_cpd_ds[\"cpd_ratio\"]\n", "final_cpd_ds = final_cpd_ds[\n", "    [\"delivery_date\", \"ds_outlet\", \"item_id\", \"final_cpd\"]\n", "].drop_duplicates()\n", "final_cpd_ds.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as ds_outlet,\n", "name as ds_outlet_name\n", "from retail.console_outlet\n", "where active=1\"\"\"\n", "\n", "outlet = pd.read_sql_query(query, retail)\n", "outlet.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final_cpd_ds.merge(outlet, on=[\"ds_outlet\"], how=\"left\")\n", "final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = (\n", "    final.groupby([\"delivery_date\", \"ds_outlet\", \"ds_outlet_name\", \"item_id\"])[[\"final_cpd\"]]\n", "    .max()\n", "    .reset_index()\n", ")\n", "final.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Flat Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "\n", "run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "final[\"run_id\"] = run_id\n", "final[[\"ds_outlet\", \"item_id\"]] = final[[\"ds_outlet\", \"item_id\"]].astype(\"int\")\n", "final[\"final_cpd\"] = final[\"final_cpd\"].astype(\"float\")\n", "\n", "final = final[\n", "    [\"run_id\", \"delivery_date\", \"ds_outlet\", \"ds_outlet_name\", \"item_id\", \"final_cpd\"]\n", "].drop_duplicates()\n", "final = final.dropna()\n", "# final = final[final[\"final_cpd\"] > 0]\n", "final = final.drop_duplicates()\n", "final.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"run_id\", \"type\": \"varchar\"},\n", "    {\"name\": \"delivery_date\", \"type\": \"datetime\"},\n", "    {\"name\": \"ds_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"ds_outlet_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"final_cpd\", \"type\": \"float\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_store_cpd\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"delivery_date\", \"ds_outlet\", \"item_id\"],\n", "    \"sortkey\": [\"delivery_date\", \"ds_outlet\"],\n", "    \"incremental_key\": [\"run_id\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}