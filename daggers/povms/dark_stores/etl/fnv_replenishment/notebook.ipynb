{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime\n", "import math\n", "import json\n", "import uuid\n", "import logging\n", "from copy import deepcopy\n", "import warnings\n", "import numpy as np\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "TODAY = datetime.datetime.today() + datetime.timedelta(hours=5.5)\n", "current_hour = TODAY.hour\n", "current_hour"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Lb5meGTKr4cu0NS75BMrKuPep3itx0zjvU9qLA17J-g\"\n", "assortment = pb.from_sheets(sheet_id, \"for_assortment\")\n", "assortment = assortment[assortment[\"type_flag\"] == \"FnV\"]\n", "assortment.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Final indent harcoded (manually)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_quantity_hard = assortment[\n", "    [\"outlet_id\", \"item_id\", \"manual_indent_qty\"]\n", "].drop_duplicates()\n", "final_quantity_hard = final_quantity_hard[\n", "    final_quantity_hard[\"manual_indent_qty\"] != \"\"\n", "]\n", "final_quantity_hard"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Changing Datatypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment.replace(r\"^\\s*$\", np.nan, regex=True)\n", "assortment[\"max_qty\"] = assortment[\"max_qty\"].fillna(5000)\n", "assortment = assortment.fillna(0)\n", "\n", "assortment = assortment.astype(\n", "    {\n", "        \"outlet_id\": int,\n", "        \"item_id\": int,\n", "        \"name\": str,\n", "        \"type_flag\": str,\n", "        \"doi\": float,\n", "        \"moq\": int,\n", "        \"Head\": int,\n", "        \"manual_bump\": \"float\",\n", "        \"max_qty\": \"float\",\n", "        \"manual_indent_qty\": int,\n", "        \"dump_flag\": int,\n", "    }\n", ")\n", "outlet_list = assortment[\"outlet_id\"].to_list()\n", "outlets = tuple(outlet_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON> and <PERSON>. quantity of items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = assortment[\n", "    [\"outlet_id\", \"item_id\", \"min_qty\", \"max_qty\", \"doi\"]\n", "].drop_duplicates()\n", "quantity.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### User defined buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_buckets = assortment[[\"outlet_id\", \"item_id\", \"Bucket\"]].drop_duplicates()\n", "user_buckets.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Finding system Buckets of the items on the basis of gmv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) AS delivery_date,\n", "date(convert_tz(o.created_at, \"+00:00\", \"+05:30\")) AS order_date,\n", "(convert_tz(o.created_at, \"+00:00\", \"+05:30\")) AS order_time,\n", " r.id AS outlet_id,\n", "o.id as order_id,\n", " i.item_id,\n", " sum(i.quantity) as quantity\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) BETWEEN DATE_ADD(CURRENT_DATE, INTERVAL -7 DAY) AND DATE_ADD(CURRENT_DATE, INTERVAL -0 DAY)\n", "  AND r.id IN %(outlet)s\n", "  AND status_id NOT IN (3,\n", "                        5)\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5,\n", "         6\n", "ORDER BY 1,\n", "         2\n", "\"\"\"\n", "\n", "order = pd.read_sql_query(query, retail, params={\"outlet\": outlets})\n", "order.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cart penetration of items and units sold"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sales = assortment.merge(order, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "assortment_sales[\"order_hour\"] = assortment_sales[\"order_time\"].dt.hour\n", "order_count = (\n", "    assortment_sales.groupby([\"outlet_id\"])\n", "    .agg({\"order_id\": \"nunique\", \"quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"order_id\": \"total_orders\", \"quantity\": \"total_qty\"})\n", ")\n", "item_order = (\n", "    assortment_sales.groupby([\"outlet_id\", \"item_id\", \"name\"])\n", "    .agg({\"order_id\": \"nunique\", \"quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "item_order.sort_values(by=[\"outlet_id\", \"order_id\"], ascending=False).head(2)\n", "item_orderv2 = item_order.merge(order_count, on=[\"outlet_id\"], how=\"left\")\n", "item_orderv2.sort_values(by=[\"outlet_id\", \"order_id\"], ascending=False, inplace=True)\n", "item_orderv2[\"per_order\"] = item_orderv2[\"order_id\"] / item_orderv2[\"total_orders\"]\n", "item_orderv2[\"per_qty\"] = item_orderv2[\"quantity\"] / item_orderv2[\"total_qty\"]\n", "item_orderv2.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Max-Min normalization for scoring"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = pd.DataFrame()\n", "for outlet in outlet_list:\n", "    df = item_orderv2[item_orderv2[\"outlet_id\"] == outlet]\n", "    df[\"order_norm\"] = (df[\"order_id\"] - df[\"order_id\"].min()) / (\n", "        df[\"order_id\"].max() - df[\"order_id\"].min()\n", "    )\n", "    df[\"qty_norm\"] = (df[\"quantity\"] - df[\"quantity\"].min()) / (\n", "        df[\"quantity\"].max() - df[\"quantity\"].min()\n", "    )\n", "    df[\"net_score\"] = df[\"order_norm\"] + df[\"qty_norm\"]\n", "    df = df[\n", "        [\"outlet_id\", \"item_id\", \"name\", \"quantity\", \"total_qty\", \"net_score\"]\n", "    ].drop_duplicates()\n", "    df1 = pd.concat([df1, df])\n", "\n", "df1 = df1.drop_duplicates()\n", "df1.sort_values(by=[\"outlet_id\", \"net_score\"], ascending=False, inplace=True)\n", "df1[\"rank\"] = df1.groupby([\"outlet_id\"]).cumcount() + 1\n", "\n", "df1[\"total_items\"] = df1[\"item_id\"].nunique()\n", "df1[\"cumsum\"] = df1.groupby([\"outlet_id\"])[\"quantity\"].apply(lambda x: x.cumsum())\n", "df1[\"cumsum_per\"] = df1[\"cumsum\"] / df1[\"total_qty\"]\n", "df1.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining Buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def bucket(x):\n", "    if x[\"cumsum_per\"] <= 0.8:\n", "        return \"A\"\n", "\n", "    if x[\"cumsum_per\"] > 0.8 and x[\"cumsum_per\"] <= 0.95:\n", "\n", "        return \"B\"\n", "    else:\n", "        return \"C\"\n", "\n", "\n", "df1[\"bucket\"] = df1.apply(bucket, axis=1)\n", "buckets = df1[[\"outlet_id\", \"item_id\", \"name\", \"bucket\"]]\n", "buckets.sort_values(by=[\"outlet_id\", \"bucket\"], inplace=True)\n", "buckets = buckets.drop_duplicates()\n", "item_buckets = buckets.merge(user_buckets, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "item_buckets[\"bucket\"] = np.where(\n", "    item_buckets[\"Bucket\"] == 0, item_buckets[\"bucket\"], item_buckets[\"Bucket\"]\n", ")\n", "item_buckets = item_buckets[[\"outlet_id\", \"item_id\", \"name\", \"bucket\"]]\n", "item_buckets.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Storing buckets in the input sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"fnv_buckets\"\n", "pb.to_sheets(\n", "    item_buckets,\n", "    sheet_id,\n", "    sheet_name,\n", "    service_account=\"service_account\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Finding the CPD of the items"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding QPO of the items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT distinct r.id AS outlet_id,\n", "       extract(hour from  convert_tz(o.created_at,\"+00:00\",\"+05:30\")) AS hour,\n", "      date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) AS order_date,\n", "       date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) AS delivery_date,          \n", "    o.order_id \n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) between DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY) and DATE_ADD(CURRENT_DATE, INTERVAL -0 DAY)\n", "  AND r.id in %(outlet_id)s\n", "  and o.status_id NOT IN (3,5)\n", "\n", "        \n", "\"\"\"\n", "\n", "orders_time = pd.read_sql_query(query, retail, params={\"outlet_id\": outlets})\n", "orders_time.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders_time[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Warehouse order capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reading slot wise capacity details from capacity system and manipulation\n", "query = \"\"\"select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "delivery_slot_type,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "\twarehouse_external_id AS backend_merchant_id,\n", "\twarehouse_name AS backend_merchant_name,\n", "\tDATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "\tTO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "\tTO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "\tslot_type,\n", "    CASE WHEN EXTRACT(HOUR FROM (slot_start AT TIME ZONE 'ASIA/KOLKATA'))< 16 THEN 'SLOT A' ELSE  'SLOT B' END AS delivery_slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "\twarehouse_planned AS given_capacity,\n", "\twarehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(current_date- interval '15 days')\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between  date(current_date- interval '15 days') and date(current_date + interval '15 days')\n", "\n", "and given_capacity > 0\n", "group by 1,2,3,4\n", "order by 2,3;\n", "\"\"\"\n", "capacity_details = pd.read_sql_query(query, capacity_system)\n", "\n", "query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store\n", "from\n", "lake_oms_bifrost.oms_merchant m\n", "join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where\n", "cms.active = 1 and cms.cms_update_active = 1;\n", "\"\"\"\n", "cms_outlet_details = pd.read_sql(query, redshift)\n", "\n", "\n", "warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[\n", "        [\n", "            \"outlet_id\",\n", "            \"outlet\",\n", "            \"delivery_date\",\n", "            \"planned_capacity\",\n", "            \"backend_merchant_name\",\n", "        ]\n", "    ]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "warehouse_capacities = warehouse_capacities[\n", "    warehouse_capacities.outlet_id.isin(outlet_list)\n", "]\n", "\n", "warehouse_capacities = (\n", "    warehouse_capacities.groupby([\"delivery_date\", \"outlet_id\", \"outlet\"])\n", "    .agg({\"Capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "warehouse_capacities.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Total orders in the DS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT r.id AS outlet_id,\n", "       date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) AS delivery_date,          \n", "       count(DISTINCT CASE WHEN o.status_id NOT IN (3,5) THEN o.order_id END) AS total_orders\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE convert_tz(scheduled_at,\"+00:00\",\"+05:30\") between DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY) and DATE_ADD(CURRENT_DATE, INTERVAL -0 DAY)\n", "  AND r.id in %(outlet_id)s\n", "GROUP BY 1,\n", "         2\n", "        \n", "ORDER BY 1,\n", "         2\n", "\"\"\"\n", "\n", "orders = pd.read_sql_query(query, retail, params={\"outlet_id\": outlets})\n", "orders.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def week(x):\n", "    if x[\"day\"] in (5, 6):\n", "        return \"weekend\"\n", "    else:\n", "        return \"weekday\"\n", "\n", "\n", "def order_time(x):\n", "    if x[\"order_date\"] < x[\"delivery_date\"]:\n", "        return 0\n", "    else:\n", "        return x[\"hour\"]\n", "\n", "\n", "def count_orders(x):\n", "    if x[\"max_hour\"] >= x[\"order_hour\"]:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding Capacity utilization of weeday/weekend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cap_utilization = warehouse_capacities.merge(\n", "    orders, on=[\"delivery_date\", \"outlet_id\"], how=\"left\"\n", ")\n", "cap_utilization[\"day\"] = pd.to_datetime(cap_utilization[\"delivery_date\"]).dt.dayofweek\n", "cap_utilization[\"utilization\"] = round(\n", "    cap_utilization[\"total_orders\"] / cap_utilization[\"Capacity\"] * 100, 2\n", ")\n", "cap_utilization[\"weekday\"] = cap_utilization.apply(week, axis=1)\n", "cap_utilization = cap_utilization.dropna()\n", "cap_utilization.sort_values(\n", "    by=[\"weekday\", \"delivery_date\"], ascending=[True, False], inplace=True\n", ")\n", "cap_utilization[\"row\"] = (\n", "    cap_utilization.groupby([\"outlet_id\", \"weekday\"]).cumcount() + 1\n", ")\n", "cap_utilization = cap_utilization[cap_utilization[\"row\"] <= 4]\n", "cap_utilization_mean = (\n", "    cap_utilization.groupby([\"outlet_id\", \"weekday\"])[[\"utilization\"]]\n", "    .mean()\n", "    .reset_index()\n", ")\n", "cap_utilization_mean"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment Stock out time "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select date,\n", "outlet_id,\n", "\n", "item_id,\n", "max(hour) as max_hour\n", "\n", "from\n", "\n", "(SELECT DISTINCT date(etl_timestamp_ist) AS date,\n", "dark_store as outlet_id,\n", "                extract(hour\n", "                        FROM etl_timestamp_ist) AS hour,\n", "                item_id,\n", "                product_id,\n", "                name,\n", "                product_type,\n", "                net_quantity\n", "FROM metrics.dark_stores_inventory_log\n", "WHERE type_flag='FnV'\n", "  AND dark_store in  %(outlet_id)s\n", "  AND date(etl_timestamp_ist)  between current_date- interval '15 days' and current_date + interval '0 days'\n", "  and net_quantity>0\n", "ORDER BY date,item_id,\n", "         hour )x1\n", "         \n", "         group by 1,2,3\n", "         \n", "         \"\"\"\n", "\n", "stock_out_time = pd.read_sql(query, redshift, params={\"outlet_id\": outlets})\n", "stock_out_time = stock_out_time.drop_duplicates()\n", "\n", "assortment_time = assortment.merge(\n", "    stock_out_time, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "assortment_time = assortment_time.dropna()\n", "assortment_time.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_time[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orders till stock out of the assortment items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT distinct r.id AS outlet_id,\n", "       extract(hour from  convert_tz(o.created_at,\"+00:00\",\"+05:30\")) AS hour,\n", "       date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) AS order_date,\n", "       date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) AS delivery_date,          \n", "    o.order_id \n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\"))  between DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY) and DATE_ADD(CURRENT_DATE, INTERVAL -0 DAY)\n", "  AND r.id in %(outlet_id)s\n", "  and o.status_id NOT IN (3,5)\n", "\n", "        \n", "\"\"\"\n", "\n", "orders_time = pd.read_sql_query(query, retail, params={\"outlet_id\": outlets})\n", "\n", "orders_time[\"order_hour\"] = orders_time.apply(order_time, axis=1)\n", "\n", "orders_time = orders_time[\n", "    [\"delivery_date\", \"outlet_id\", \"order_hour\", \"order_id\"]\n", "].drop_duplicates()\n", "\n", "item_order_time = assortment_time.merge(\n", "    orders_time,\n", "    left_on=[\"date\", \"outlet_id\"],\n", "    right_on=[\"delivery_date\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "item_order_time[\"count_order\"] = item_order_time.apply(count_orders, axis=1)\n", "item_order_time = item_order_time.dropna()\n", "item_order_count = (\n", "    item_order_time.groupby([\"outlet_id\", \"delivery_date\", \"item_id\"])[[\"count_order\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "item_order_count.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_order_count[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Quantity ordered for assortment items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT distinct r.id AS outlet_id,\n", "       date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) AS delivery_date,          \n", "    o.order_id,\n", "    i.item_id,\n", "    i.quantity\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) between DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY) and DATE_ADD(CURRENT_DATE, INTERVAL -0 DAY)\n", "  AND r.id in %(outlet_id)s\n", "  and o.status_id NOT IN (3,5)\n", "\n", "        \n", "\"\"\"\n", "\n", "order_quantity = pd.read_sql_query(query, retail, params={\"outlet_id\": outlets})\n", "order_quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_quantity[\"outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_quantity = (\n", "    order_quantity.groupby([\"delivery_date\", \"outlet_id\", \"item_id\"])[[\"quantity\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "order_quantity.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding QPO of the assortment items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qpo = item_order_count.merge(\n", "    order_quantity, on=[\"delivery_date\", \"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "qpo[\"qpo\"] = qpo[\"quantity\"] / qpo[\"count_order\"]\n", "qpo[\"day\"] = pd.to_datetime(qpo[\"delivery_date\"]).dt.dayofweek\n", "qpo[\"weekday\"] = qpo.apply(week, axis=1)\n", "qpo.sort_values(\n", "    by=[\"outlet_id\", \"item_id\", \"delivery_date\", \"weekday\"],\n", "    ascending=[False, True, False, True],\n", "    inplace=True,\n", ")\n", "qpo[\"row\"] = qpo.groupby([\"outlet_id\", \"weekday\", \"item_id\"]).cumcount() + 1\n", "qpo = qpo.fillna(0)\n", "\n", "qpo = qpo[qpo[\"row\"] <= 3]\n", "qpo_mean = (\n", "    qpo.groupby([\"outlet_id\", \"item_id\", \"weekday\"])[[\"qpo\"]].mean().reset_index()\n", ")\n", "qpo_mean.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qpo_mean[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Items cpd as per doi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = assortment[[\"outlet_id\", \"item_id\", \"doi\"]].drop_duplicates()\n", "items[[\"doi\", \"item_id\", \"outlet_id\"]] = items[[\"doi\", \"item_id\", \"outlet_id\"]].astype(\n", "    \"int\"\n", ")\n", "items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortmentv2 = pd.DataFrame()\n", "x = []\n", "for outlets_id in outlet_list:\n", "    if outlets_id not in x:\n", "        x.append(outlets_id)\n", "\n", "for outlet in x:\n", "    df = items[items[\"outlet_id\"] == outlet]\n", "    for index, row in df.iterrows():\n", "        doi = row[\"doi\"]\n", "        doi = int(doi)\n", "        for day in range(doi):\n", "            day = int(day)\n", "            date = (\n", "                datetime.datetime.today() + datetime.timedelta(days=day + 1)\n", "            ).strftime(\"%Y-%m-%d\")\n", "            df.set_value(index, \"delivery_date\", date)\n", "            assortmentv2 = pd.concat([assortmentv2, df])\n", "\n", "\n", "assortmentv2 = assortmentv2.drop_duplicates()\n", "assortmentv2 = assortmentv2.dropna()\n", "assortmentv2[\"day\"] = pd.to_datetime(assortmentv2[\"delivery_date\"]).dt.dayofweek\n", "assortmentv2[\"weekday\"] = assortmentv2.apply(week, axis=1)\n", "\n", "assortmentv2[\"delivery_date\"] = pd.to_datetime(assortmentv2[\"delivery_date\"])\n", "warehouse_capacities[\"delivery_date\"] = pd.to_datetime(\n", "    warehouse_capacities[\"delivery_date\"]\n", ")\n", "assortment_cap = assortmentv2.merge(\n", "    warehouse_capacities, on=[\"delivery_date\", \"outlet_id\"], how=\"left\"\n", ")\n", "assortment_cap = assortment_cap[\n", "    [\"delivery_date\", \"outlet_id\", \"item_id\", \"doi\", \"weekday\", \"Capacity\"]\n", "].drop_duplicates()\n", "assortment_cap.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_cap[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining CPD logic for buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cpd(x):\n", "    if x[\"bucket\"] == \"X\":\n", "        return x[\"qpo\"] * x[\"Capacity\"]\n", "\n", "    if x[\"bucket\"] == \"A\":\n", "        return x[\"qpo\"] * x[\"Capacity\"]\n", "\n", "    if x[\"bucket\"] == \"B\":\n", "        return x[\"qpo\"] * x[\"Capacity\"] * x[\"utilization\"] / 100\n", "\n", "    if x[\"bucket\"] == \"C\":\n", "        return x[\"qpo\"] * x[\"Capacity\"] * x[\"utilization\"] / 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_cap_cpd = assortment_cap.merge(\n", "    qpo_mean, on=[\"outlet_id\", \"item_id\", \"weekday\"], how=\"left\"\n", ")\n", "\n", "assortment_cap_cpd_buckets = assortment_cap_cpd.merge(\n", "    item_buckets, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "assortment_cap_cpd_buckets = assortment_cap_cpd_buckets.fillna(0)\n", "assortment_cap_cpd_buckets = assortment_cap_cpd_buckets.merge(\n", "    cap_utilization_mean, on=[\"outlet_id\", \"weekday\"], how=\"left\"\n", ")\n", "assortment_cap_cpd_buckets[\"cpd\"] = assortment_cap_cpd_buckets.apply(cpd, axis=1)\n", "assortment_cap_cpd_buckets = assortment_cap_cpd_buckets.drop_duplicates()\n", "assortment_cap_cpd_buckets = assortment_cap_cpd_buckets.merge(\n", "    quantity, on=[\"outlet_id\", \"item_id\", \"doi\"], how=\"left\"\n", ")\n", "assortment_cap_cpd_buckets[[\"cpd\", \"min_qty\", \"doi\"]] = assortment_cap_cpd_buckets[\n", "    [\"cpd\", \"min_qty\", \"doi\"]\n", "].astype(\"float\")\n", "\n", "assortment_cap_cpd_buckets[\"utilization\"] = assortment_cap_cpd_buckets[\n", "    \"utilization\"\n", "].<PERSON><PERSON>(100)\n", "assortment_cap_cpd_buckets[\"cpd\"] = assortment_cap_cpd_buckets[\"cpd\"].fillna(0)\n", "assortment_cap_cpd_buckets[\"cpd\"] = np.where(\n", "    assortment_cap_cpd_buckets[\"cpd\"] == 0,\n", "    assortment_cap_cpd_buckets[\"min_qty\"] / assortment_cap_cpd_buckets[\"doi\"],\n", "    assortment_cap_cpd_buckets[\"cpd\"],\n", ")\n", "\n", "final_cpd = (\n", "    assortment_cap_cpd_buckets.groupby([\"outlet_id\", \"item_id\", \"bucket\"])[[\"cpd\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "final_cpd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpdv2 = final_cpd[[\"outlet_id\", \"item_id\", \"bucket\", \"cpd\"]].drop_duplicates()\n", "final_cpdv2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merging item details with the cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itemsv2 = assortment[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"doi\",\n", "        \"moq\",\n", "        \"dump_flag\",\n", "        \"manual_bump\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "    ]\n", "].drop_duplicates()\n", "final_cpdv3 = final_cpdv2.merge(itemsv2, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "final_cpdv3 = final_cpdv3[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"bucket\",\n", "        \"name\",\n", "        \"doi\",\n", "        \"moq\",\n", "        \"dump_flag\",\n", "        \"manual_bump\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"cpd\",\n", "    ]\n", "]\n", "final_cpdv3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Available inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_query = \"\"\"\n", "SELECT good_inv.outlet_id AS outlet_id,\n", "       product.item_id,\n", "       sum(CASE\n", "               WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity ELSE 0\n", "           END) AS quantity,\n", "           0 as blocked_quantity\n", "FROM ims.ims_good_inventory good_inv\n", "INNER JOIN rpc.product_product product ON product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1\n", "  AND good_inv.outlet_id IN %(outlets)s\n", "GROUP BY 1,\n", "         2\n", "         \n", "         \n", "UNION\n", "\n", "SELECT a.outlet_id, \n", "       a.item_id, \n", "       a.quantity, \n", "       b.quantity AS blocked_quantity \n", "FROM   (SELECT outlet_id, \n", "               item_id, \n", "               quantity \n", "        FROM   ims.ims_item_inventory \n", "        WHERE  outlet_id IN %(outlets)s \n", "               AND active = 1) a \n", "       LEFT JOIN (SELECT item_id, \n", "                         outlet_id, \n", "                         sum(quantity) AS quantity\n", "                  FROM   ims.ims_item_blocked_inventory \n", "                  WHERE  outlet_id IN %(outlets)s \n", "                         AND active = 1 \n", "                         AND blocked_type in (1,2,5)  -- blocked for sto and orders\n", "                 GROUP BY 1,2) b \n", "              ON a.item_id = b.item_id \n", "                 AND a.outlet_id = b.outlet_id \n", "\"\"\"\n", "inventory = pd.read_sql_query(\n", "    sql=inventory_query, con=retail, params={\"outlets\": outlets}\n", ")\n", "\n", "\n", "inventory.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def available_qty(x):\n", "    return max(0, x[\"quantity\"] - x[\"blocked_quantity\"])\n", "\n", "\n", "def final_cpd(x):\n", "    return x[\"cpd\"] * x[\"manual_bump\"]\n", "\n", "\n", "def indent_estimated_qty(x):\n", "    if x[\"dump_flag\"] == 1:\n", "        return x[\"final_cpd\"]\n", "    else:\n", "        return max(0, x[\"final_cpd\"] - x[\"available_qty\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpdv3[[\"cpd\", \"manual_bump\"]] = final_cpdv3[[\"cpd\", \"manual_bump\"]].astype(\n", "    \"float\"\n", ")\n", "final_cpdv3[\"final_cpd\"] = final_cpdv3.apply(final_cpd, axis=1)\n", "\n", "inventory = inventory.groupby([\"outlet_id\", \"item_id\"]).max().reset_index()\n", "\n", "final_cpdv4 = final_cpdv3.merge(inventory, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "final_cpdv4[\"available_qty\"] = final_cpdv4.apply(available_qty, axis=1)\n", "final_cpdv4[\"final_cpd\"] = final_cpdv4.apply(final_cpd, axis=1)\n", "final_cpdv4[\"dump_flag\"] = final_cpdv4[\"dump_flag\"].astype(\"int\")\n", "final_cpdv4[\"final_cpd\"] = final_cpdv4[\"final_cpd\"].astype(\"int\")\n", "final_cpdv4[\"available_qty\"] = final_cpdv4[\"available_qty\"].astype(\"int\")\n", "\n", "final_cpdv4[\"indent_estimated_qty\"] = final_cpdv4.apply(indent_estimated_qty, axis=1)\n", "\n", "final_cpdv4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpdv4.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dump data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT date_ AS dated,\n", "       outlet_id,\n", "       item AS item_id,\n", "       sum(quantity) AS dump_quantity\n", "FROM\n", "  (SELECT date(convert_tz(i.pos_timestamp,'+00:00','+05:30')) AS date_,\n", "          i.outlet_id,\n", "          r.item_id AS item,\n", "          r.name AS Product,\n", "          r.variant_uom_text AS grammage,\n", "          i2.name AS Update_type,\n", "          r.variant_mrp AS Mrp,\n", "          i.weighted_lp AS weighted_lp,\n", "          SUM(i.delta) AS quantity\n", "   FROM ims.ims_inventory_log i\n", "   INNER JOIN rpc.product_product r ON r.variant_id = i.variant_id\n", "   INNER JOIN retail.console_outlet ro ON ro.id = i.outlet_id\n", "   INNER JOIN ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "   LEFT JOIN ims.ims_bad_inventory_update_log ibil ON i.inventory_update_id=ibil.inventory_update_id\n", "   LEFT JOIN ims.ims_bad_update_reason ibur ON ibil.reason_id=ibur.id\n", "   WHERE i.inventory_update_type_id IN (11,\n", "                                        12,\n", "                                        13,\n", "                                        64,\n", "                                        87,\n", "                                        88,\n", "                                        89)\n", "     AND date(convert_tz(i.pos_timestamp,'+00:00','+05:30')) BETWEEN DATE_ADD(CURRENT_DATE, INTERVAL -3 DAY) AND DATE_ADD(CURRENT_DATE, INTERVAL -1 DAY)\n", "     AND r.outlet_type=1\n", "     AND i.outlet_id IN %(outlets)s\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8) table1\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "ORDER BY 1,2,3\n", "\n", " \"\"\"\n", "\n", "dump = pd.read_sql_query(query, retail, params={\"outlets\": outlets})\n", "dump.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Available qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT date as dated\n", ", outlet_id,\n", "             item_id,\n", "             max(net_quantity) AS qty\n", "FROM\n", "  (SELECT DISTINCT date(etl_timestamp_ist) AS date,\n", "                   dark_store AS outlet_id,\n", "                   extract(hour\n", "                           FROM etl_timestamp_ist) AS hour,\n", "                   item_id,\n", "                   product_id,\n", "                   name,\n", "                   product_type,\n", "                   net_quantity\n", "   FROM metrics.dark_stores_inventory_log\n", "   WHERE type_flag='FnV'\n", "     AND dark_store IN %(outlet)s\n", "     AND date(etl_timestamp_ist) BETWEEN current_date- interval '3 days' and current_date - interval '1 days'\n", "     AND net_quantity>0\n", "   ORDER BY date,item_id,\n", "                 hour) x1\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "         \n", "         order by 2,1\n", "         \n", "         \"\"\"\n", "\n", "daily_available = pd.read_sql(query, redshift, params={\"outlet\": outlets})\n", "daily_available = daily_available.drop_duplicates()\n", "daily_available.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dump ratio"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump_ratio = daily_available.merge(\n", "    dump, on=[\"dated\", \"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "dump_ratio = dump_ratio.fillna(0)\n", "\n", "dump_ratio[\"dump_ratio\"] = round(dump_ratio[\"dump_quantity\"] / dump_ratio[\"qty\"], 2)\n", "dump_ratio = (\n", "    dump_ratio.groupby([\"outlet_id\", \"item_id\"])[[\"dump_ratio\"]].mean().reset_index()\n", ")\n", "dump_ratio[\"dump_ratio\"] = np.where(\n", "    dump_ratio[\"dump_ratio\"] > 0.4, 0.4, dump_ratio[\"dump_ratio\"]\n", ")\n", "\n", "dump_ratio.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def indent_qty_dump(x):\n", "    return x[\"indent_estimated_qty\"] - x[\"indent_estimated_qty\"] * x[\"dump_ratio\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpdv5 = final_cpdv4.merge(dump_ratio, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "final_cpdv5 = final_cpdv5.fillna(0)\n", "\n", "final_cpdv5[\"indent_qty_dump\"] = (\n", "    final_cpdv5[\"indent_estimated_qty\"]\n", "    - final_cpdv5[\"indent_estimated_qty\"] * final_cpdv5[\"dump_ratio\"]\n", ")\n", "\n", "final_cpdv5.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as outlet_id,\n", "name as outlet_name\n", "from retail.console_outlet\n", "where active=1\"\"\"\n", "outlet = pd.read_sql_query(query, retail)\n", "outlet.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merging outlet name with assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheetv1 = final_cpdv5.merge(outlet, on=[\"outlet_id\"], how=\"left\")\n", "assortment_sheetv1 = assortment_sheetv1.fillna(0)\n", "assortment_sheetv1[\"final_indent_po_release\"] = (\n", "    np.ceil(assortment_sheetv1[\"indent_qty_dump\"] / assortment_sheetv1[\"moq\"])\n", "    * assortment_sheetv1[\"moq\"]\n", ")\n", "assortment_sheetv1[\"final_indent_po_release\"] = np.where(\n", "    assortment_sheetv1[\"final_indent_po_release\"] > assortment_sheetv1[\"max_qty\"],\n", "    assortment_sheetv1[\"max_qty\"],\n", "    assortment_sheetv1[\"final_indent_po_release\"],\n", ")\n", "\n", "final_quantity_hard[[\"outlet_id\", \"item_id\"]] = final_quantity_hard[\n", "    [\"outlet_id\", \"item_id\"]\n", "].astype(\"int\")\n", "\n", "assortment_sheetv1 = assortment_sheetv1.merge(\n", "    final_quantity_hard, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "assortment_sheetv1[\"manual_indent_qty\"] = assortment_sheetv1[\n", "    \"manual_indent_qty\"\n", "].fillna(0)\n", "assortment_sheetv1[\n", "    [\"manual_indent_qty\", \"final_indent_po_release\"]\n", "] = assortment_sheetv1[[\"manual_indent_qty\", \"final_indent_po_release\"]].astype(\"int\")\n", "\n", "assortment_sheetv1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheetv1 = assortment_sheetv1[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"bucket\",\n", "        \"doi\",\n", "        \"moq\",\n", "        \"dump_flag\",\n", "        \"manual_bump\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"manual_indent_qty\",\n", "        \"cpd\",\n", "        \"final_cpd\",\n", "        \"quantity\",\n", "        \"blocked_quantity\",\n", "        \"available_qty\",\n", "        \"indent_estimated_qty\",\n", "        \"dump_ratio\",\n", "        \"indent_qty_dump\",\n", "        \"final_indent_po_release\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if current_hour == 19:\n", "\n", "    sheet_name = \"Tentative indent\"\n", "\n", "    pb.to_sheets(\n", "        assortment_sheetv1,\n", "        sheet_id,\n", "        sheet_name,\n", "        service_account=\"service_account\",\n", "        clear_cache=True,\n", "    )\n", "\n", "else:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheetv1[\"final_indent_po_release\"] = np.where(\n", "    assortment_sheetv1[\"manual_indent_qty\"] > 0,\n", "    assortment_sheetv1[\"manual_indent_qty\"],\n", "    assortment_sheetv1[\"final_indent_po_release\"],\n", ")\n", "assortment_sheetv1.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add item description"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT r.item_id,\n", "                r.variant_description\n", "FROM rpc.product_product r\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          max(created_at) AS dated\n", "   FROM rpc.product_product\n", "   GROUP BY 1) a ON a.item_id=r.item_id\n", "WHERE r.created_at=a.dated\"\"\"\n", "\n", "description = pd.read_sql_query(query, retail)\n", "description.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheetv2 = assortment_sheetv1.merge(description, on=[\"item_id\"], how=\"left\")\n", "assortment_sheetv2.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining vendor forcast"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def vendor_forcast(x):\n", "    if x[\"dump_flag\"] == 1:\n", "        return x[\"final_cpd\"]\n", "    else:\n", "        return x[\"final_cpd\"] - (\n", "            x[\"final_indent_po_release\"]\n", "            + x[\"available_qty\"]\n", "            - (x[\"final_cpd\"] / x[\"doi\"])\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sheetv2[\"vendor_forcasting\"] = assortment_sheetv2.apply(\n", "    vendor_forcast, axis=1\n", ")\n", "assortment_sheetv2[\"vendor_forcasting\"] = np.where(\n", "    assortment_sheetv2[\"vendor_forcasting\"] < 0,\n", "    0,\n", "    assortment_sheetv2[\"vendor_forcasting\"],\n", ")\n", "assortment_sheetv2[\"vendor_forcast\"] = (\n", "    np.ceil(assortment_sheetv2[\"vendor_forcasting\"] / assortment_sheetv2[\"moq\"])\n", "    * assortment_sheetv2[\"moq\"]\n", ")\n", "\n", "assortment_sheetv2.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = assortment_sheetv2[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"variant_description\",\n", "        \"final_indent_po_release\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "summary = summary[summary[\"final_indent_po_release\"] > 0]\n", "summary.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Copying Indent to Google Sheets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1MUI58LnQ-HSiz71Yc9e8h3MlAq-5s9NvwC8b6FL7eDE\"\n", "sheet_name = \"po_himanshu\"\n", "if current_hour == 20:\n", "    pb.to_sheets(summary, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding weight in vendor forcast"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT r.item_id,\n", "                r.weight_in_gm\n", "FROM rpc.product_product r\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          max(created_at) AS dated\n", "   FROM rpc.product_product\n", "   GROUP BY 1) a ON a.item_id=r.item_id\n", "WHERE r.created_at=a.dated\"\"\"\n", "\n", "weight = pd.read_sql_query(query, retail)\n", "weight.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def contain_string(x):\n", "    if \"kg\" in x[\"variant_description\"]:\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "def weight_item(x):\n", "    return max(x[\"weight_in_gm\"], x[\"wt2\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_forcast = assortment_sheetv2[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"variant_description\",\n", "        \"vendor_forcast\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "vendor_forcast = vendor_forcast[vendor_forcast[\"vendor_forcast\"] > 0]\n", "vendor_forcast_wt = vendor_forcast.merge(weight, on=[\"item_id\"], how=\"left\")\n", "vendor_forcast_wt[\"vendor_forcast\"] = np.ceil(vendor_forcast_wt[\"vendor_forcast\"])\n", "vendor_forcast_wt[\"variant_description\"] = vendor_forcast_wt[\n", "    \"variant_description\"\n", "].astype(\"str\")\n", "vendor_forcast_wt[\"wt\"] = vendor_forcast_wt[\"variant_description\"].str[-8:]\n", "vendor_forcast_wt[\"wt2\"] = vendor_forcast_wt[\"wt\"].str.replace(r\"\\D+\", \"\")\n", "vendor_forcast_wt[\"wt2\"] = vendor_forcast_wt[\"wt2\"].astype(\"int\")\n", "vendor_forcast_wt[\"wt2\"] = vendor_forcast_wt[\"wt2\"].astype(\"int\")\n", "\n", "vendor_forcast_wt[\"weight\"] = vendor_forcast_wt.apply(weight_item, axis=1)\n", "vendor_forcast_wt[\"Total_weight_kg\"] = round(\n", "    vendor_forcast_wt[\"vendor_forcast\"] * vendor_forcast_wt[\"weight\"] / 1000, 2\n", ")\n", "vendor_forcast_wt = vendor_forcast_wt[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"variant_description\",\n", "        \"vendor_forcast\",\n", "        \"Total_weight_kg\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "vendor_forcast_wt.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Saving data in excel file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pip\n", "\n", "\n", "def import_or_install(package):\n", "    try:\n", "        __import__(package)\n", "    except ImportError:\n", "        pip.main([\"install\", package])\n", "\n", "\n", "import_or_install(\"openpyxl\")\n", "excel_sheets = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = []\n", "for outlet in summary[\"outlet_id\"]:\n", "    if outlet not in x:\n", "        x.append(outlet)\n", "x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sending Mailers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "next_Date = datetime.date.today() + datetime.timedelta(days=1)\n", "next_Date\n", "\n", "t2_date = datetime.date.today() + datetime.timedelta(days=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### E-mail Id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Lb5meGTKr4cu0NS75BMrKuPep3itx0zjvU9qLA17J-g\"\n", "email_id = pb.from_sheets(sheet_id, \"FnV-mail_list\", clear_cache=True)\n", "email_id_summary = email_id[[\"file_type\", \"email\"]].drop_duplicates()\n", "email_id_summary = email_id_summary.replace(r\"^\\s*$\", np.nan, regex=True)\n", "email_id_summary = email_id_summary.dropna()\n", "email_id_summary[\"file_type\"] = email_id_summary[\"file_type\"].astype(\"str\")\n", "\n", "vendor_email = email_id[[\"vendor_name\", \"email_id\"]].drop_duplicates()\n", "vendor_email = vendor_email[vendor_email[\"vendor_name\"] != \"\"]\n", "vendor_email.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_to_html(df):\n", "    html_table = \"\"\"\n", "    <div class=\"mce-toc\">\n", "      <table style=\"border-collapse: collapse; width: 500px; \" border=\"1\">\n", "        <tbody>\n", "          <tr style=\"height: 57px;\">\n", "    \"\"\"\n", "    for x in df.columns:\n", "        html_table += \"\"\"<td style=\"height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "        html_table += x\n", "        html_table += \"\"\"</strong></span></td>\"\"\"\n", "    html_table += \"\"\"</tr>\"\"\"\n", "    for i, r in df.iterrows():\n", "        html_table += \"\"\"<tr style=\"height: 15px;\">\"\"\"\n", "        for x in df.columns:\n", "            html_table += (\n", "                \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\">\"\"\"\n", "            )\n", "            html_table += str(r[x])\n", "            html_table += \"\"\"</td>\"\"\"\n", "        html_table += \"\"\"</tr>\"\"\"\n", "    html_table += \"\"\"\n", "        </tbody>\n", "      </table>\n", "    </div>\n", "    \"\"\"\n", "    return html_table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### summary file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if current_hour == 20:\n", "\n", "    indent_summary = (\n", "        summary.groupby([\"outlet_id\", \"outlet_name\"])\n", "        .agg({\"item_id\": \"nunique\", \"final_indent_po_release\": \"sum\"})\n", "        .reset_index()\n", "        .rename(columns={\"item_id\": \"skus_in_indent\"})\n", "    )\n", "    indent_summary[\n", "        [\"outlet_id\", \"final_indent_po_release\", \"skus_in_indent\"]\n", "    ] = indent_summary[\n", "        [\"outlet_id\", \"final_indent_po_release\", \"skus_in_indent\"]\n", "    ].astype(\n", "        \"int\"\n", "    )\n", "    summary.to_csv(\"/tmp/summary.csv\", index=False)\n", "\n", "    from_email = \"<EMAIL>\"\n", "\n", "    subject = \"Dark Store FnV Replenishment Report | \" + str(next_Date)\n", "\n", "    to_email = email_id_summary[(email_id_summary[\"file_type\"] == \"summary\")][\n", "        \"email\"\n", "    ].tolist()\n", "\n", "    message_text = f\"\"\"Hi <PERSON>, <br><br> \n", "    Please find the attached FnV indent <br><br> {df_to_html(indent_summary)} \n", "\n", "    <br><br> <PERSON><PERSON>,<br><PERSON><PERSON><PERSON>\"\"\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content=message_text,\n", "        files=[\"/tmp/summary.csv\"],\n", "    )\n", "\n", "    print(\"mail sent\")\n", "\n", "else:\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Raw file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if current_hour == 20:\n", "    excel_sheets[\"summary\"] = summary\n", "    excel_sheets[\"current_fnv_assortment\"] = assortment_sheetv2\n", "    excel_sheets[\"final_indent\"] = assortment_sheetv2[\n", "        (assortment_sheetv2.final_indent_po_release > 0)\n", "    ]\n", "    excel_sheets[\"items_not_required\"] = assortment_sheetv2[\n", "        (assortment_sheetv2.final_indent_po_release <= 0)\n", "    ]\n", "\n", "    html_content = \"\"\"\n", "    Hi,<br><br>\n", "    Please find attached Raw sheet for F&V.<br><br>\n", "    <PERSON><PERSON>,<br>\n", "    <PERSON><PERSON><PERSON>\n", "    \"\"\"\n", "\n", "    excel_file = \"/tmp/raw_data_\" + \"_fnv_indent.xlsx\".format()\n", "    with pd.ExcelWriter(excel_file) as writer:\n", "        for k, v in excel_sheets.items():\n", "            v.to_excel(writer, sheet_name=k, index=False, engine=\"xlsxwriter\")\n", "\n", "    import jinja2\n", "    import pencilbox as pb\n", "    from jinja2 import Template\n", "\n", "    from_email = \"<EMAIL>\"\n", "    subject = \"Dark Store FnV Replenishment Raw file |\" + str(next_Date)\n", "\n", "    to_email = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "\n", "    pb.send_email(from_email, to_email, subject, html_content, files=[excel_file])\n", "\n", "    print(\"mail sent\")\n", "\n", "else:\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vendor forcast (T+2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Vendor name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_name = pb.from_sheets(sheet_id, \"Vendor Mapping FNV\")\n", "vendor_name.drop([\"PID\", \"Item Name\", \"New UPC\", \"Content\"], axis=1, inplace=True)\n", "vendor_name.rename(columns={\"Item Code\": \"item_id\"}, inplace=True)\n", "vendor_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = []\n", "for outlet in x:\n", "    temp_data = vendor_name[[\"item_id\", str(outlet)]]\n", "    temp_data.rename(columns={str(outlet): \"vendor_name\"}, inplace=True)\n", "    temp_data[\"outlet_id\"] = outlet\n", "    temp_data = temp_data[temp_data[\"vendor_name\"] != \"\"]\n", "    y.append(temp_data)\n", "\n", "vendors = pd.concat(y)\n", "vendors.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendors = vendors[vendors[\"item_id\"] != \"\"]\n", "vendors = vendors[vendors[\"outlet_id\"] != \"\"]\n", "vendors[[\"item_id\", \"outlet_id\"]] = vendors[[\"item_id\", \"outlet_id\"]].astype(\"int\")\n", "vendor_forcast_wt_name = vendor_forcast_wt.merge(\n", "    vendors, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "vendor_forcast_wt_name[\"vendor_name\"] = vendor_forcast_wt_name[\"vendor_name\"].astype(\n", "    \"str\"\n", ")\n", "vendor_forcast_wt_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_name = list(vendor_forcast_wt_name[\"vendor_name\"].unique())\n", "vendor_name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if current_hour == 20:\n", "\n", "    for name in vendor_name:\n", "        df = vendor_forcast_wt_name[vendor_forcast_wt_name[\"vendor_name\"] == name]\n", "        df.to_csv(\"/tmp/vendor_forcast_\" + str(name) + \".csv\", index=False)\n", "\n", "        import jinja2\n", "        import pencilbox as pb\n", "        from jinja2 import Template\n", "\n", "        from_email = \"<EMAIL>\"\n", "        subject = \"Dark Store FnV Vendor Forcast Report \" + \" |\" + str(t2_date)\n", "\n", "        to_email = vendor_email[(vendor_email[\"vendor_name\"] == name)][\n", "            \"email_id\"\n", "        ].tolist()\n", "\n", "        pb.send_email(\n", "            from_email,\n", "            to_email,\n", "            subject,\n", "            html_content,\n", "            files=[\"/tmp/vendor_forcast_\" + str(name) + \".csv\"],\n", "        )\n", "\n", "        print(\"mail sent\")\n", "\n", "else:\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Flat table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = assortment_sheetv2.copy()\n", "final = final[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"variant_description\",\n", "        \"bucket\",\n", "        \"doi\",\n", "        \"moq\",\n", "        \"dump_flag\",\n", "        \"manual_bump\",\n", "        \"min_qty\",\n", "        \"max_qty\",\n", "        \"cpd\",\n", "        \"final_cpd\",\n", "        \"quantity\",\n", "        \"blocked_quantity\",\n", "        \"available_qty\",\n", "        \"indent_estimated_qty\",\n", "        \"indent_qty_dump\",\n", "        \"manual_indent_qty\",\n", "        \"final_indent_po_release\",\n", "        \"vendor_forcast\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "\n", "run_id = datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "final[\"updated_at\"] = run_id\n", "final = final.<PERSON><PERSON>(0)\n", "final.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### old query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT r.*\n", "\n", "FROM metrics.dark_store_fnv_replenishment r\n", "INNER JOIN\n", "  (SELECT outlet_id,\n", "  date(updated_at),\n", "          max(updated_at) AS updated_at\n", "   FROM metrics.dark_store_fnv_replenishment\n", "   GROUP BY 1,2) x1 ON x1.outlet_id=r.outlet_id\n", "AND x1.updated_at=r.updated_at\"\"\"\n", "\n", "old = pd.read_sql(query, redshift)\n", "old.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new = pd.concat([old, final])\n", "final_new = final_new.fillna(0)\n", "final_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new = final_new[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"variant_description\",\n", "        \"bucket\",\n", "        \"doi\",\n", "        \"moq\",\n", "        \"dump_flag\",\n", "        \"manual_bump\",\n", "        \"max_qty\",\n", "        \"min_qty\",\n", "        \"manual_indent_qty\",\n", "        \"weekday_cpd\",\n", "        \"weekend_cpd\",\n", "        \"weekdays\",\n", "        \"weekends\",\n", "        \"cpd\",\n", "        \"final_cpd\",\n", "        \"quantity\",\n", "        \"blocked_quantity\",\n", "        \"available_qty\",\n", "        \"indent_estimated_qty\",\n", "        \"vendor_fillrate\",\n", "        \"indent_estimated_qty_avf\",\n", "        \"dump_ratio\",\n", "        \"indent_qty_dump\",\n", "        \"availability_ratio\",\n", "        \"final_indent_qty\",\n", "        \"final_indent_po_release\",\n", "        \"vendor_forcast\",\n", "        \"updated_at\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "final_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_store_fnv_replenishment\",\n", "    \"column_dtypes\": redshift_schema(final_new),\n", "    \"primary_key\": [\"outlet_id\", \"item_id\"],\n", "    \"sortkey\": [\"outlet_id\", \"bucket\"],\n", "    \"incremental_key\": [\"updated_at\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if current_hour == 20:\n", "    pb.to_redshift(final_new, **kwargs)\n", "else:\n", "    pass"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}