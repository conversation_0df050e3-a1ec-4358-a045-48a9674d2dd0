{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as facility_id,name as facility_name from crates.facility\"\"\"\n", "facility_name = pd.read_sql_query(query, retail)\n", "facility_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from gspread.exceptions import APIError\n", "\n", "_retry_for = [429, 500, 503]\n", "\n", "\n", "def from_sheets(sheet_id, sheet_name, service_account, max_tries=10):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to read attempt: {attempt}...\")\n", "        try:\n", "            return pb.from_sheets(sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(20)\n", "\n", "\n", "def to_sheets(df, sheet_id, sheet_name, service_account, max_tries=10):\n", "    for attempt in range(max_tries):\n", "        print(f\"Trying to write attempt: {attempt}...\")\n", "        try:\n", "            return pb.to_sheets(df, sheet_id, sheet_name, service_account)\n", "            break\n", "        except APIError as e:\n", "            exception_code = e.response.json()[\"error\"][\"code\"]\n", "            print(e)\n", "            if exception_code in _retry_for:\n", "                time.sleep(20)\n", "\n", "\n", "pb.from_sheets_patch = from_sheets\n", "pb.to_sheets_patch = to_sheets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1YYBvyql8PeIfzGuekQtfbIhEfWEZasu0RR15o7Zgzqo\"\n", "\n", "facility = pb.from_sheets_patch(sheet_id, \"facility_list\", service_account=\"povms_account\")\n", "# pb.from_sheets(sheet_id, \"facility_list\", clear_cache=True)\n", "\n", "facility = facility[[\"facility_name\"]].drop_duplicates()\n", "facility = facility.merge(facility_name, on=[\"facility_name\"], how=\"left\")\n", "facility = facility[~facility[\"facility_name\"].str.contains(\"ES\")]\n", "facility.sort_values(by=[\"facility_id\"], inplace=True)\n", "\n", "facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_facility = list(facility[\"facility_id\"].unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Defining parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility[\"facility_id\"] = facility[\"facility_id\"].astype(\"str\")\n", "\n", "facility_id_list = set(filter(None, facility.facility_id.values))\n", "facility_id_list = \", \".join(facility_id_list)\n", "facility_id_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT o.facility_id,\n", "                final.item_id,\n", "                final.Final_bucket\n", "FROM\n", "  (SELECT DISTINCT a.item_id,\n", "                  b.cloud_store_id AS outlet_id,\n", "                  (CASE\n", "                        WHEN a.tag_value='A'\n", "                             AND a1.tag_value ='Y' THEN 'X'\n", "                        WHEN a.tag_value='A'\n", "                             AND a1.tag_value IS NULL THEN 'A'\n", "                        ELSE 'B'\n", "                    END) AS Final_bucket\n", "  FROM rpc.item_outlet_tag_mapping a\n", "  INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id=b.warehouse_id\n", "  LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id=a.item_id\n", "  AND a1.outlet_id=a.outlet_id\n", "  AND a1.tag_type_id=6\n", "  AND a1.tag_value='Y'\n", "  WHERE a.tag_type_id IN (1)\n", "     AND a.active=1\n", "  ORDER BY 2,\n", "            3) final\n", "LEFT JOIN retail.console_outlet o ON final.outlet_id=o.id\n", "ORDER BY 1,\n", "         2\"\"\"\n", "\n", "buckets = pd.read_sql_query(query, retail)\n", "buckets.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item product mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "item_product_mapping = pd.read_sql(item_attributes_query, redshift)\n", "item_product_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_query = \"\"\"\n", "SELECT a.*,\n", "case when is_pl=1 then 'gb'\n", "else 'non-gb' end as item_type\n", "FROM\n", "  (SELECT a.item_id,\n", "          name,\n", "          is_pl,\n", "          variant_description,\n", "          weight_in_gm,\n", "          row_number()over(PARTITION BY a.item_id\n", "                           ORDER BY VARIANT_DESCRIPTION) AS rank_1\n", "   FROM lake_rpc.product_product AS a\n", "   INNER JOIN\n", "     (SELECT item_id,\n", "             max(updated_at) AS updated_at\n", "      FROM lake_rpc.product_product\n", "      GROUP BY 1)b ON a.item_id=b.item_id\n", "   AND a.updated_at=b.updated_at\n", "   AND a.active=1\n", "   AND a.approved=1) AS a\n", "WHERE rank_1=1 \n", "\"\"\"\n", "\n", "item_pl_detail = pd.read_sql_query(sql=item_query, con=redshift)\n", "\n", "item_pl_detail.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### items inventory (backend+Frontend)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT facility_id,\n", "       item_id,\n", "       COALESCE(actual_quantity,0) as actual_quantity,\n", "       COALESCE(blocked_quantity,0) as blocked_quantity,\n", "       CASE\n", "           WHEN actual_quantity-blocked_quantity<0 THEN 0\n", "           ELSE COALESCE((actual_quantity-blocked_quantity),0)\n", "       END AS unblocked_quantity\n", "FROM rpc_daily_availability AS a\n", "INNER JOIN\n", "  (SELECT max(order_date) AS maxi\n", "   FROM rpc_daily_availability\n", "   WHERE date(order_date) = CURRENT_DATE ) AS b ON a.order_date = b.maxi\n", "WHERE new_substate=1\n", "  AND assortment_date <= order_date\n", "  AND (unreliable IS NULL\n", "       OR unreliable = 0\n", "       OR unreliable = 'NA')\n", "  AND (unorderable IS NULL\n", "       OR unorderable = 0\n", "       OR unorderable = 'NA')\n", "  AND (date_of_activation < '2021-01-01'\n", "       OR a.facility_id IN (214,\n", "                            259,\n", "                            262,\n", "                            263,\n", "                            268,\n", "                            271,\n", "                            272,\n", "                            275,\n", "                            281,\n", "                            285,\n", "                            299,\n", "                            241,\n", "                            269))\n", "ORDER BY 1,\n", "         2,\n", "         3,\n", "         4\n", "   \"\"\"\n", "\n", "\n", "Total_inventory = pd.read_sql(query, redshift)\n", "Total_inventory.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory = Total_inventory.copy()\n", "active_items_inventory = active_items_inventory.fillna(0)\n", "active_items_inventory.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item forecasted quantity (till 26 jan)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["middle_sheet_id = \"1q1ZXJbPC1ZYdlX2P20koQne4Ec2uT0mrIB3X1YyYPrI\"\n", "sheet_name_middle = \"consideration_date\"\n", "time_period = pb.from_sheets_patch(\n", "    middle_sheet_id, sheet_name_middle, service_account=\"povms_account\"\n", ")\n", "time_period = time_period[[\"timedelta\"]]\n", "delta = time_period.iloc[0, 0]\n", "delta = int(delta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["middle_date = (datetime.now() + timedelta(hours=5.5) + timedelta(days=delta)).strftime(\"%Y-%m-%d\")\n", "middle_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "\n", "SELECT ro.facility_id,\n", "       item_id,\n", "       sum(CASE\n", "               WHEN dated>=CURRENT_DATE\n", "                    AND dated<=%(middle_date)s  THEN consumption\n", "           END) AS cpd,\n", "       sum(CASE\n", "               WHEN dated>=CURRENT_DATE\n", "                    AND dated<='2021-01-26'THEN consumption\n", "           END) AS final_cpd\n", "FROM\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          date AS dated,\n", "          consumption\n", "   FROM snorlax.date_wise_consumption\n", "   WHERE date BETWEEN CURRENT_DATE AND '2021-01-26'\n", "   GROUP BY 1,\n", "            2,\n", "            3) dwc\n", "JOIN retail.warehouse_outlet_mapping wom ON dwc.outlet_id = wom.warehouse_id\n", "LEFT JOIN retail.console_outlet ro ON ro.id=dwc.outlet_id\n", "WHERE ro.facility_id IN ({facility_id})\n", "GROUP BY 1,\n", "         2\n", "\"\"\".format(\n", "    facility_id=facility_id_list\n", ")\n", "final_cpd = pd.read_sql_query(cpd_query, retail, params={\"middle_date\": middle_date})\n", "final_cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merging inventory with demand"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd = active_items_inventory.merge(\n", "    final_cpd, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "active_items_inventory_cpd = active_items_inventory_cpd.fillna(0)\n", "active_items_inventory_cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Finding CPD at G4 (sum of all mapped ds)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT case when ro.facility_id in (202,194,232,233,242) then 15\n", "                else r.facility_id end as facility_id,\n", "                case when ro.facility_id in (202,194,232,233,242) then 'Super Store Jaipur - Warehouse'\n", "                else fc.name end as facility_name,\n", "                ro.facility_id AS ds_facility,\n", "                f.name AS ds_facility_name\n", "FROM rpc.item_outlet_tag_mapping AS t\n", "INNER JOIN retail.console_outlet ro ON ro.id=t.outlet_id\n", "INNER JOIN retail.console_outlet r ON r.id=t.tag_value\n", "INNER JOIN crates.facility f ON f.id=ro.facility_id\n", "INNER JOIN crates.facility fc ON fc.id=r.facility_id\n", "WHERE r.facility_id=29\n", "and ro.name not like \"%%old%%\"\n", "and ro.facility_id!=228\n", "and f.name not like '%%Super Store Gurgaon Sector 38 ES5%%'\n", "and f.name not like '%%Super Store Delhi Bamnoli EC - Warehouse%%'\n", "and f.name not like '%%Super Store Delhi Mundka EC - Warehouse%%'\n", "and f.name not like '%%Super Store Gurgaon G2 - Warehouse%%'\n", "\n", "and t.tag_type_id=8\n", "and t.active=1\n", "order by 1\"\"\"\n", "\n", "mapping_g4 = pd.read_sql_query(\n", "    query,\n", "    retail,\n", ")\n", "\n", "\n", "mapping_g4[\"ds_facility\"] = mapping_g4[\"ds_facility\"].astype(\"str\")\n", "\n", "facility_id_list_ds = set(filter(None, mapping_g4.ds_facility.values))\n", "facility_id_list_ds = \", \".join(facility_id_list_ds)\n", "\n", "query = \"\"\"\n", "SELECT item_id,\n", "       sum(CASE\n", "               WHEN dated>=CURRENT_DATE\n", "                    AND dated<=%(middle_date)s THEN consumption\n", "           END) AS ds_cpd,\n", "       sum(CASE\n", "               WHEN dated>=CURRENT_DATE\n", "                    AND dated<='2021-01-26' THEN consumption\n", "           END) AS ds_final_cpd\n", "FROM\n", "  (SELECT item_id,\n", "          date AS dated,\n", "          sum(consumption) AS consumption\n", "   FROM snorlax.date_wise_consumption c\n", "   INNER JOIN retail.console_outlet o ON c.outlet_id=o.id\n", "   WHERE date BETWEEN CURRENT_DATE AND '2021-01-26'\n", "     AND o.facility_id IN ({facility_id})\n", "   GROUP BY 1,\n", "            2)x1\n", "GROUP BY 1\n", "\n", "\"\"\".format(\n", "    facility_id=facility_id_list_ds\n", ")\n", "final_cpd_ds = pd.read_sql_query(query, retail, params={\"middle_date\": middle_date})\n", "final_cpd_ds[\"facility_id\"] = 29\n", "final_cpd_ds.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merging with base data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd_ds = active_items_inventory_cpd.merge(\n", "    final_cpd_ds, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "active_items_inventory_cpd_ds[\"final_cpd\"] = np.where(\n", "    active_items_inventory_cpd_ds[\"facility_id\"] == 29,\n", "    active_items_inventory_cpd_ds[\"ds_final_cpd\"],\n", "    active_items_inventory_cpd_ds[\"final_cpd\"],\n", ")\n", "\n", "active_items_inventory_cpd_ds[\"cpd\"] = np.where(\n", "    active_items_inventory_cpd_ds[\"facility_id\"] == 29,\n", "    active_items_inventory_cpd_ds[\"ds_cpd\"],\n", "    active_items_inventory_cpd_ds[\"cpd\"],\n", ")\n", "\n", "active_items_inventory_cpd_ds.drop([\"ds_final_cpd\"], axis=1, inplace=True)\n", "active_items_inventory_cpd_ds.drop([\"ds_cpd\"], axis=1, inplace=True)\n", "active_items_inventory_cpd_ds.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd = active_items_inventory_cpd_ds.copy()\n", "active_items_inventory_cpd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Open PO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select facility_id,\n", "item_id,\n", "sum(po_qty) as po_qty \n", "from\n", "(select poi.item_id,\n", "o.facility_id,\n", "p.po_number,\n", "p.vendor_name,\n", "date(issue_date) AS issue_date,\n", "date(expiry_date) AS expiry_date,\n", "date(ps.schedule_date_time) AS schedule_date,\n", "poi.units_ordered AS po_qty\n", "FROM po.purchase_order p\n", "LEFT JOIN po.po_schedule ps ON p.id=ps.po_id_id\n", "INNER JOIN po.purchase_order_items poi ON p.id=poi.po_id\n", "INNER JOIN retail.console_outlet o ON o.id=p.outlet_id\n", "INNER JOIN retail.console_location cl ON cl.id=o.tax_location_id\n", "INNER JOIN retail.auth_user a ON a.id=p.created_by\n", "INNER JOIN po.purchase_order_status posa ON posa.po_id = p.id\n", "INNER JOIN po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "WHERE posta.id not  IN (4,5,8,9,10)\n", "  AND date(convert_tz(p.issue_date,'+00:00','+05:30')) )x1\n", "  group by 1,\n", "           2\n", "                      \"\"\"\n", "\n", "po = pd.read_sql_query(query, retail)\n", "\n", "po.sort_values(by=[\"facility_id\"], inplace=True)\n", "po.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd_po = active_items_inventory_cpd.merge(\n", "    po, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "active_items_inventory_cpd_po = active_items_inventory_cpd_po.fillna(0)\n", "active_items_inventory_cpd_po.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd_po.groupby([\"facility_id\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Open sto"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT o.facility_id,\n", "       item_id,\n", "       sum(quantity) AS sto_intransit_quantity\n", "FROM ims.ims_sto_mapping_inventory\n", "INNER JOIN retail.console_outlet o ON ims_sto_mapping_inventory.merchant_outlet_id = o.id\n", "INNER JOIN retail.console_outlet o1 ON ims_sto_mapping_inventory.outlet_id = o1.id\n", "WHERE inventory_type IN (1,\n", "                         2)\n", "and o.facility_id in ({facility_id})                         \n", "GROUP BY 1,\n", "         2\n", "HAVING sto_intransit_quantity>0\n", "ORDER BY 2\"\"\".format(\n", "    facility_id=facility_id_list\n", ")\n", "\n", "sto = pd.read_sql_query(query, retail)\n", "\n", "sto.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd_po_sto = active_items_inventory_cpd_po.merge(\n", "    sto, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "active_items_inventory_cpd_po_sto = active_items_inventory_cpd_po_sto.fillna(0)\n", "active_items_inventory_cpd_po_sto.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def shortfallv1(x):\n", "    return max(\n", "        0,\n", "        x[\"final_cpd\"] - (x[\"unblocked_quantity\"] + x[\"po_qty\"] + x[\"sto_intransit_quantity\"]),\n", "    )\n", "\n", "\n", "def shortfallv2(x):\n", "    return max(0, x[\"shortfall_v1\"] - (x[\"qty_distribution\"]))\n", "\n", "\n", "def shortfallv1_middle(x):\n", "    return max(\n", "        0,\n", "        x[\"cpd\"] - (x[\"unblocked_quantity\"] + x[\"po_qty\"] + x[\"sto_intransit_quantity\"]),\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd_po_sto[\"shortfall_v1\"] = active_items_inventory_cpd_po_sto.apply(\n", "    shortfallv1, axis=1\n", ")\n", "active_items_inventory_cpd_po_sto[\"shortfall\"] = active_items_inventory_cpd_po_sto.apply(\n", "    shortfallv1_middle, axis=1\n", ")\n", "active_items_inventory_cpd_po_sto.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Middle shortfall"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["middle_shortfall = active_items_inventory_cpd_po_sto.copy()\n", "middle_shortfall.drop([\"shortfall_v1\"], axis=1, inplace=True)\n", "middle_shortfall_l0 = middle_shortfall.merge(\n", "    item_product_mapping[[\"item_id\", \"name\", \"l0\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "middle_shortfall_bucket = middle_shortfall_l0.merge(\n", "    buckets, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "middle_shortfall_bucket_gb = middle_shortfall_bucket.merge(\n", "    item_pl_detail[[\"item_id\", \"item_type\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "middle_shortfall_bucket_gb[\"updated_at\"] = now\n", "facility[\"facility_id\"] = facility[\"facility_id\"].astype(\"int\")\n", "final_middle = middle_shortfall_bucket_gb.merge(facility, on=[\"facility_id\"], how=\"left\")\n", "final_middle = final_middle[final_middle[\"facility_id\"].isin(base_facility)]\n", "\n", "final_middle = final_middle[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"item_type\",\n", "        \"l0\",\n", "        \"Final_bucket\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"unblocked_quantity\",\n", "        \"cpd\",\n", "        \"final_cpd\",\n", "        \"po_qty\",\n", "        \"sto_intransit_quantity\",\n", "        \"shortfall\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "middle_sheet_id = \"1q1ZXJbPC1ZYdlX2P20koQne4Ec2uT0mrIB3X1YyYPrI\"\n", "sheet_name_middle = \"raw\"\n", "final_middle[\"Final_bucket\"] = final_middle[\"Final_bucket\"].fillna(\"B\")\n", "final_middle[\"item_type\"] = final_middle[\"item_type\"].fillna(\"non-gb\")\n", "\n", "pb.to_sheets_patch(\n", "    final_middle, middle_sheet_id, sheet_name_middle, service_account=\"povms_account\"\n", ")\n", "final_middle.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_inventory_cpd_po_sto.drop([\"shortfall\", \"cpd\"], axis=1, inplace=True)\n", "active_items_inventory_cpd_po_sto.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item tag mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT ro.facility_id,\n", "                      tag_value AS backend_outlet,\n", "                      r.name AS backend_outlet_name,\n", "                      item_id\n", "      FROM rpc.item_outlet_tag_mapping tag\n", "      INNER JOIN retail.console_outlet ro ON ro.id=tag.outlet_id\n", "      INNER JOIN retail.console_outlet r ON r.id=tag.tag_value\n", "      WHERE tag_type_id=8\n", "        AND tag.active=1\n", "        and ro.name not like \"%%old%%\"\n", "        and ro.facility_id IN ({facility_id})\n", "    \n", "      ORDER BY 1,\n", "               2,\n", "               3,\n", "               4\"\"\".format(\n", "    facility_id=facility_id_list\n", ")\n", "\n", "item_tag = pd.read_sql_query(query, retail)\n", "item_tag.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_tag_inventory = active_items_inventory_cpd_po_sto.merge(\n", "    item_tag, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "item_tag_inventory[\"backend_outlet\"] = item_tag_inventory[\"backend_outlet\"].fillna(1000000)\n", "item_tag_inventory.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Checking backend inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select ro.id  as 'backend_outlet',\n", "facility_id as backend_facility_id,\n", "f.name as backend_facility_name\n", "from retail.console_outlet ro\n", "inner join crates.facility f on f.id=ro.facility_id\"\"\"\n", "facility = pd.read_sql_query(query, retail)\n", "facility = facility.dropna()\n", "\n", "item_tag_inventory[\"backend_outlet\"] = item_tag_inventory[\"backend_outlet\"].astype(\"int\")\n", "item_tag_inventory_backend = item_tag_inventory.merge(facility, on=[\"backend_outlet\"], how=\"left\")\n", "item_tag_inventory_backend.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_inventory = (\n", "    Total_inventory[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"actual_quantity\",\n", "            \"blocked_quantity\",\n", "            \"unblocked_quantity\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(\n", "        columns={\n", "            \"actual_quantity\": \"backend_actual_quantity\",\n", "            \"blocked_quantity\": \"backend_blocked_quantity\",\n", "            \"unblocked_quantity\": \"backend_unblocked_quantity\",\n", "            \"facility_id\": \"backend_facility_id\",\n", "        }\n", "    )\n", ")\n", "backend_inventory.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Backend open po"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_po = po.copy()\n", "backend_po.rename(\n", "    columns={\"facility_id\": \"backend_facility_id\", \"po_qty\": \"backend_po_qty\"},\n", "    inplace=True,\n", ")\n", "backend_inventory_po = backend_inventory.merge(\n", "    backend_po, on=[\"backend_facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "backend_inventory_po = backend_inventory_po.fillna(0)\n", "backend_inventory_po[\"net_backend_available\"] = (\n", "    backend_inventory_po[\"backend_unblocked_quantity\"] + backend_inventory_po[\"backend_po_qty\"]\n", ")\n", "backend_inventory_po.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_new_backend2 = item_tag_inventory_backend.merge(\n", "    backend_inventory_po, on=[\"backend_facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "final_new_backend2.drop([\"backend_outlet\", \"backend_outlet_name\"], axis=1, inplace=True)\n", "final_new_backend2 = final_new_backend2.drop_duplicates()\n", "final_new_backend2[\n", "    [\n", "        \"backend_actual_quantity\",\n", "        \"backend_blocked_quantity\",\n", "        \"backend_unblocked_quantity\",\n", "        \"net_backend_available\",\n", "        \"backend_po_qty\",\n", "    ]\n", "] = final_new_backend2[\n", "    [\n", "        \"backend_actual_quantity\",\n", "        \"backend_blocked_quantity\",\n", "        \"backend_unblocked_quantity\",\n", "        \"net_backend_available\",\n", "        \"backend_po_qty\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "final_new_backend2.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dividing backend inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_division = final_new_backend2.dropna()\n", "backend_division.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_division = backend_division[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"item_id\",\n", "        \"net_backend_available\",\n", "        \"facility_id\",\n", "        \"shortfall_v1\",\n", "    ]\n", "].drop_duplicates()\n", "backend_division.sort_values(by=[\"backend_facility_id\", \"item_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_net_sf = (\n", "    backend_division.groupby([\"backend_facility_id\", \"item_id\"])[[\"shortfall_v1\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(columns={\"shortfall_v1\": \"total_sf\"})\n", ")\n", "item_net_sf.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_division_ratio = backend_division.merge(\n", "    item_net_sf, on=[\"backend_facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "backend_division_ratio[\"ratio\"] = (\n", "    backend_division_ratio[\"shortfall_v1\"] / backend_division_ratio[\"total_sf\"]\n", ")\n", "backend_division_ratio[\"qty_distribution\"] = (\n", "    backend_division_ratio[\"ratio\"] * backend_division_ratio[\"net_backend_available\"]\n", ")\n", "backend_division_ratio = backend_division_ratio.fillna(0)\n", "backend_division_ratio.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_backend_qty = backend_division_ratio[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"total_sf\",\n", "        \"ratio\",\n", "        \"qty_distribution\",\n", "    ]\n", "].drop_duplicates()\n", "final_backend_qty.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = final_new_backend2.merge(\n", "    final_backend_qty, on=[\"facility_id\", \"backend_facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "final_data[[\"total_sf\", \"ratio\", \"qty_distribution\"]] = final_data[\n", "    [\"total_sf\", \"ratio\", \"qty_distribution\"]\n", "].fillna(0)\n", "final_data[\"shortfallv2\"] = final_data.apply(shortfallv2, axis=1)\n", "final_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping = final_data.merge(\n", "    item_product_mapping[[\"item_id\", \"name\", \"l0\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "final_data_mapping_name = final_data_mapping.merge(facility_name, on=[\"facility_id\"], how=\"left\")\n", "final_data_mapping_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name = final_data_mapping_name.merge(\n", "    item_pl_detail[[\"item_id\", \"item_type\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "final_data_mapping_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name = final_data_mapping_name.merge(\n", "    buckets, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "final_data_mapping_name[\"Final_bucket\"] = final_data_mapping_name[\"Final_bucket\"].fillna(\"B\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name = (\n", "    final_data_mapping_name[\n", "        [\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"item_id\",\n", "            \"name\",\n", "            \"item_type\",\n", "            \"l0\",\n", "            \"Final_bucket\",\n", "            \"unblocked_quantity\",\n", "            \"final_cpd\",\n", "            \"po_qty\",\n", "            \"sto_intransit_quantity\",\n", "            \"shortfall_v1\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "            \"backend_unblocked_quantity\",\n", "            \"backend_po_qty\",\n", "            \"net_backend_available\",\n", "            \"total_sf\",\n", "            \"ratio\",\n", "            \"qty_distribution\",\n", "            \"shortfallv2\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(columns={\"final_cpd\": \"item_forecasted_demand(till 26th jan)\"})\n", ")\n", "final_data_mapping_name[\"item_type\"] = final_data_mapping_name[\"item_type\"].fillna(\"non-gb\")\n", "final_data_mapping_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name.columns = final_data_mapping_name.columns.str.replace(\"_\", \" \")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name = final_data_mapping_name[\n", "    final_data_mapping_name[\"facility id\"].isin(base_facility)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name[\"facility name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "final_data_mapping_name[\"updated_at\"] = now"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_mapping_name = final_data_mapping_name.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1H1ppbkdUscMv9DkcKuOk0MgbRUboBXVffCqxnIK27xY\"\n", "sheet_name = \"raw\"\n", "pb.to_sheets_patch(final_data_mapping_name, sheet_id, sheet_name, service_account=\"povms_account\")\n", "# pb.to_sheets(final_data_mapping_name, sheet_id, sheet_name, clear_cache=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}