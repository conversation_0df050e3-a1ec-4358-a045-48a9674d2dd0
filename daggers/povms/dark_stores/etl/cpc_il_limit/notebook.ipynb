{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import requests\n", "\n", "import requests\n", "import json\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "capacity_system = pb.get_connection(\"supply_orchestrator\")\n", "aur = pb.get_connection(\"aurora\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extracting data from input sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"12liYuPlisl9N6rivHvyQv9Y5rMHdm2V5LVesQ0ZU5gU\"\n", "sheet_name = \"input_sheet\"\n", "input_data = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "input_data[\n", "    [\"front_end_outlet\", \"backend_outlet\", \"backend_merchant\", \"frontend_id\"]\n", "] = input_data[\n", "    [\"front_end_outlet\", \"backend_outlet\", \"backend_merchant\", \"frontend_id\"]\n", "].astype(\n", "    \"int\"\n", ")\n", "input_data[\"threshold_capacity_utilization\"] = input_data[\n", "    \"threshold_capacity_utilization\"\n", "].astype(\"float\")\n", "input_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["front_end_id = list(input_data[\"frontend_id\"])\n", "backend_outlet = list(input_data[\"backend_outlet\"])\n", "combined_outlet_list = list(input_data[\"backend_outlet\"]) + list(\n", "    input_data[\"front_end_outlet\"]\n", ")\n", "front_end_id, backend_outlet, combined_outlet_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### item product mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\"\"\"\n", "\n", "item_product_mapping = pd.read_sql(query, redshift)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Backend capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct backend_merchant_id as backend_merchant,\n", "backend_merchant_name,\n", "delivery_date,\n", "sum(given_capacity) as planned_capacity\n", "from\n", "(SELECT \n", "\twarehouse_external_id AS backend_merchant_id,\n", "\twarehouse_name AS backend_merchant_name,\n", "\tDATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "\tTO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "\tTO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "\tslot_type,\n", "    CASE WHEN EXTRACT(HOUR FROM (slot_start AT TIME ZONE 'ASIA/KOLKATA'))< 16 THEN 'SLOT A' ELSE  'SLOT B' END AS delivery_slot_type,\n", "    warehouse_asked AS asked_capcity,\n", "\twarehouse_planned AS given_capacity,\n", "\twarehouse_actual AS capacity_utilised,\n", "    warehouse_available AS capacity_under_utilised,\n", "    min(update_ts) AS update_ts\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between date(current_date) and date(current_date + interval '15 days')\n", "\n", "and given_capacity > 0\n", "group by 1,2,3\n", "order by 2,3;\n", "\"\"\"\n", "capacity_details = pd.read_sql_query(query, capacity_system)\n", "capacity_details = capacity_details[\n", "    capacity_details[\"backend_merchant\"].isin(\n", "        list(input_data[\"backend_merchant\"].unique())\n", "    )\n", "]\n", "\n", "capacity_details.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orders at backend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH ancestor_orders AS\n", "  (SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "                  b.name AS front_end,\n", "                  a.id AS ancestor,\n", "                  a.current_status\n", "  FROM bifrost.oms_order a\n", "  INNER JOIN bifrost.oms_merchant b ON a.merchant_id = b.id\n", "  WHERE DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') >=current_date-interval '1 days'\n", "     AND a.direction ='FORWARD'\n", "     AND a.current_status != 'CANCELLED'\n", "     AND a.type IN ('RetailForwardOrder',\n", "                    'InternalForwardOrder')\n", "     AND cast(a.shipment_id as integer) in %(front_end)s ),\n", "     \n", "     quantity AS\n", "  (SELECT o.delivery_date,\n", "          o.front_end,\n", "          o.ancestor,\n", "          so.id AS sub_order_id\n", "  FROM ancestor_orders o\n", "  INNER JOIN bifrost.oms_suborder so ON so.order_id=o.ancestor\n", "  INNER JOIN bifrost.oms_suborder_item oi ON so.id = oi.suborder_id\n", "  INNER JOIN bifrost.oms_order_item  oa ON oa.id=oi.order_item_id\n", "  GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "        \n", "SELECT *\n", "FROM quantity\n", "order by 3,2\"\"\"\n", "\n", "oms_items = pd.read_sql(query, aur, params={\"front_end\": tuple(front_end_id)})\n", "\n", "query = \"\"\" SELECT distinct \n", "                   o.outlet AS sub_order_outlet,\n", "                   o.order_id AS sub_order_id\n", "            FROM ims.ims_order_details o\n", "            INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "            WHERE date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\"))  >=DATE_ADD(CURRENT_DATE, INTERVAL -1 DAY)\n", "              AND status_id NOT IN (3,\n", "                                    4,\n", "                                    5,\n", "                                    6,\n", "                                    7,\n", "                                    8)\n", "                                    and o.outlet in %(outlet)s                \n", "     \"\"\"\n", "\n", "orders = pd.read_sql_query(\n", "    query, retail, params={\"outlet\": tuple(combined_outlet_list)}\n", ")\n", "orders[\"sub_order_id\"] = orders[\"sub_order_id\"].astype(\"int\")\n", "\n", "oms_items[\"sub_order_id\"] = oms_items[\"sub_order_id\"].astype(\"int\")\n", "orders[\"sub_order_id\"] = orders[\"sub_order_id\"].astype(\"int\")\n", "\n", "oms_suborders = oms_items.merge(orders, on=[\"sub_order_id\"], how=\"left\")\n", "oms_suborders = oms_suborders.dropna()\n", "oms_suborders.sort_values(by=[\"delivery_date\"], inplace=True)\n", "\n", "backend_picked_orders_checkout = oms_suborders.copy()\n", "backend_picked_orders_checkout = (\n", "    backend_picked_orders_checkout.groupby(\n", "        [\"delivery_date\", \"front_end\", \"sub_order_outlet\"]\n", "    )[[\"ancestor\"]]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"ancestor\": \"backend_picked_orders\",\n", "            \"sub_order_outlet\": \"backend_outlet\",\n", "        }\n", "    )\n", ")\n", "\n", "backend_picked_orders_checkout = backend_picked_orders_checkout[\n", "    backend_picked_orders_checkout[\"backend_outlet\"].isin(backend_outlet)\n", "]\n", "backend_picked_orders_checkout = backend_picked_orders_checkout.merge(\n", "    input_data[[\"backend_merchant\", \"backend_outlet\"]],\n", "    on=[\"backend_outlet\"],\n", "    how=\"left\",\n", ")\n", "backend_picked_orders_checkout = backend_picked_orders_checkout[\n", "    [\"delivery_date\", \"backend_merchant\", \"backend_picked_orders\"]\n", "].drop_duplicates()\n", "backend_picked_orders_checkout.sort_values(\n", "    by=[\"backend_merchant\", \"delivery_date\"], inplace=True\n", ")\n", "\n", "capacity_details[\"backend_merchant\"] = capacity_details[\"backend_merchant\"].astype(\n", "    \"int\"\n", ")\n", "backend_picked_orders_checkout[\"backend_merchant\"] = backend_picked_orders_checkout[\n", "    \"backend_merchant\"\n", "].astype(\"int\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Capacity utilization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_utilization = capacity_details.merge(\n", "    backend_picked_orders_checkout, on=[\"delivery_date\", \"backend_merchant\"], how=\"left\"\n", ")\n", "capacity_utilization[\"backend_merchant\"] = capacity_utilization[\n", "    \"backend_merchant\"\n", "].astype(\"int\")\n", "capacity_utilization = capacity_utilization.fillna(0)\n", "capacity_utilization[\"capacity_utilization\"] = round(\n", "    capacity_utilization[\"backend_picked_orders\"]\n", "    / capacity_utilization[\"planned_capacity\"]\n", "    * 100,\n", "    2,\n", ")\n", "capacity_utilization[\"delivery_date\"] = pd.to_datetime(\n", "    capacity_utilization[\"delivery_date\"]\n", ")\n", "cap_utilization = pd.DataFrame()\n", "for merchant in input_data[\"backend_merchant\"].unique():\n", "\n", "    capacity_utilization_back = capacity_utilization[\n", "        capacity_utilization[\"backend_merchant\"] == merchant\n", "    ]\n", "    delta_days = int(input_data[input_data[\"backend_merchant\"] == merchant].iloc[0, 7])\n", "\n", "    disable_date = (\n", "        datetime.now() + timedelta(hours=5.5) + timedelta(days=delta_days)\n", "    ).strftime(\"%Y-%m-%d\")\n", "    capacity_utilization_back_new = capacity_utilization_back[\n", "        capacity_utilization_back[\"delivery_date\"] == disable_date\n", "    ]\n", "    cap_utilization = pd.concat([cap_utilization, capacity_utilization_back_new])\n", "\n", "cap_utilization = cap_utilization.merge(\n", "    input_data[\n", "        [\n", "            \"front_end_outlet\",\n", "            \"backend_merchant\",\n", "            \"threshold_capacity_utilization\",\n", "            \"backend_enable\",\n", "            \"capacity_constraint\",\n", "        ]\n", "    ],\n", "    on=[\"backend_merchant\"],\n", "    how=\"left\",\n", ")\n", "cap_utilization[\"is_threshold_reached\"] = np.where(\n", "    cap_utilization[\"capacity_utilization\"]\n", "    >= cap_utilization[\"threshold_capacity_utilization\"],\n", "    \"yes\",\n", "    \"no\",\n", ")\n", "\n", "cap_utilization = cap_utilization[\n", "    [\n", "        \"backend_merchant\",\n", "        \"is_threshold_reached\",\n", "        \"backend_enable\",\n", "        \"capacity_constraint\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "cap_utilization[\"is_threshold_reached\"]\n", "cap_utilization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT s.ds_outlet as front_end_outlet,\n", "                  s.item_id\n", "FROM metrics.dark_store_assortment_static s\n", "INNER JOIN\n", "  (SELECT ds_outlet,\n", "          max(updated_at) AS updated_at\n", "   FROM metrics.dark_store_assortment_static\n", "   GROUP BY 1)a ON a.ds_outlet=s.ds_outlet\n", "AND a.updated_at=s.updated_at\n", "and s.ds_outlet IN %(outlet)s\n", "and s.replenishment_flag=1\n", "\"\"\"\n", "assortment = pd.read_sql(\n", "    query, redshift, params={\"outlet\": tuple(list(input_data[\"front_end_outlet\"]))}\n", ")\n", "\n", "assortment = assortment.merge(\n", "    input_data[[\"front_end_outlet\", \"backend_outlet\"]],\n", "    on=[\"front_end_outlet\"],\n", "    how=\"left\",\n", ")\n", "assortment[\"assortment_flag\"] = 1\n", "\n", "assortment_product = assortment.merge(\n", "    item_product_mapping[[\"item_id\", \"product_id\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "assortment_product.drop([\"item_id\"], axis=1, inplace=True)\n", "assortment_product.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_product.groupby([\"front_end_outlet\"])[[\"product_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bundles pids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offer_items = \"\"\"SELECT so.store_id AS backend_outlet,\n", "       so.offer_id,\n", "       o.field_val AS item_id,\n", "       offer.offer_reference_id,\n", "       offer.description,\n", "       product_id\n", "FROM offer_master.store_offer so\n", "INNER JOIN offer_master.offer_field o ON o.offer_id=so.offer_id\n", "INNER JOIN offer_master.offer_type_field of ON of.id=o.offer_type_field_id\n", "inner join offer_master.offer offer on offer.id=so.offer_id\n", "inner join rpc.item_product_mapping r on r.offer_id=offer.offer_reference_id\n", "WHERE so.store_id in %(outlet)s\n", "  AND so.is_offer_active=1\n", "  AND of.field_type LIKE '%%item_id%%'\"\"\"\n", "\n", "\n", "offer_items = pd.read_sql_query(\n", "    offer_items, retail, params={\"outlet\": tuple(list(input_data[\"backend_outlet\"]))}\n", ")\n", "offer_items[\"is_offer\"] = 1\n", "offer_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment[\"item_id\"] = assortment[\"item_id\"].astype(\"int\")\n", "offer_items[\"item_id\"] = offer_items[\"item_id\"].astype(\"int\")\n", "\n", "assortment_offer = assortment.merge(\n", "    offer_items, on=[\"backend_outlet\", \"item_id\"], how=\"outer\"\n", ")\n", "assortment_offer = assortment_offer.fillna(0)\n", "\n", "offer_selected = (\n", "    assortment_offer.groupby([\"front_end_outlet\", \"backend_outlet\", \"offer_id\"])\n", "    .agg({\"assortment_flag\": \"sum\", \"is_offer\": \"sum\"})\n", "    .reset_index()\n", ")\n", "offer_selected = offer_selected[\n", "    offer_selected[\"assortment_flag\"] == offer_selected[\"is_offer\"]\n", "]\n", "offer_selected = offer_selected[\n", "    [\"front_end_outlet\", \"backend_outlet\", \"offer_id\"]\n", "].drop_duplicates()\n", "offer_selected[\"is_final\"] = 1\n", "\n", "offer_selected2 = offer_selected.merge(\n", "    offer_items, on=[\"backend_outlet\", \"offer_id\"], how=\"left\"\n", ")\n", "\n", "bundles = offer_selected2[\n", "    [\"front_end_outlet\", \"backend_outlet\", \"product_id\", \"item_id\", \"description\"]\n", "].drop_duplicates()\n", "bundles.sort_values(by=[\"front_end_outlet\", \"product_id\"], inplace=True)\n", "bundles[[\"front_end_outlet\", \"backend_outlet\", \"product_id\", \"item_id\"]] = bundles[\n", "    [\"front_end_outlet\", \"backend_outlet\", \"product_id\", \"item_id\"]\n", "].astype(\"int\")\n", "offer_pids = bundles[\n", "    [\"front_end_outlet\", \"backend_outlet\", \"product_id\"]\n", "].drop_duplicates()\n", "\n", "assortment_new = assortment_product[\n", "    [\"front_end_outlet\", \"backend_outlet\", \"product_id\"]\n", "].drop_duplicates()\n", "final_assortment = pd.concat([assortment_new, offer_pids])\n", "final_assortment = final_assortment.drop_duplicates()\n", "final_assortment.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Items to disable (Assortment+ offer id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentmerchant = final_assortment.merge(\n", "    input_data[[\"front_end_outlet\", \"backend_outlet\", \"backend_merchant\"]],\n", "    on=[\"front_end_outlet\", \"backend_outlet\"],\n", "    how=\"left\",\n", ")\n", "items_to_disable = final_assortmentmerchant[\n", "    [\"backend_merchant\", \"product_id\"]\n", "].drop_duplicates()\n", "items_to_disable[\"assortment_flag\"] = 1\n", "items_to_disable.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_to_disable.groupby([\"backend_merchant\"])[[\"product_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Backend Enabled items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT merchant_id as backend_merchant,\n", "                product_id,\n", "                master_inventory_limit,\n", "                master_b2b_inventory_limit,\n", "                copy_product_master_il,\n", "                copy_product_master_b2b_il,\n", "                inventory_limit,\n", "                enabled_flag\n", "FROM grofers_db.gr_merchant_product_mapping\n", "WHERE merchant_id in %(merchant)s\n", "\n", "\"\"\"\n", "\n", "mapped_items = pd.read_sql(\n", "    query, aur, params={\"merchant\": tuple(list(input_data[\"backend_merchant\"]))}\n", ")\n", "\n", "mapped_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapped_items.groupby([\"backend_merchant\"])[[\"product_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Enabled items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["enable_items = mapped_items[\n", "    (mapped_items[\"inventory_limit\"] > 0) & (mapped_items[\"enabled_flag\"] == True)\n", "]\n", "\n", "enable_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["enable_items.groupby([\"backend_merchant\"])[[\"product_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Checking assortment items available on the backend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_disable_items = enable_items.merge(\n", "    items_to_disable, on=[\"backend_merchant\", \"product_id\"], how=\"inner\"\n", ")\n", "assortment_disable_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_disable_items.groupby([\"backend_merchant\"])[\n", "    [\"product_id\"]\n", "].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_disable_items_list = pd.DataFrame()\n", "final_enable_items_list = pd.DataFrame()\n", "\n", "for merchant in cap_utilization[\"backend_merchant\"].unique():\n", "    threshold_reached = str(\n", "        cap_utilization[cap_utilization[\"backend_merchant\"] == merchant].iloc[0, 1]\n", "    )\n", "    backend_enable = str(\n", "        cap_utilization[cap_utilization[\"backend_merchant\"] == merchant].iloc[0, 2]\n", "    )\n", "    capacity_constraint = str(\n", "        cap_utilization[cap_utilization[\"backend_merchant\"] == merchant].iloc[0, 3]\n", "    )\n", "\n", "    disable_assortment_items = assortment_disable_items[\n", "        assortment_disable_items[\"backend_merchant\"] == merchant\n", "    ][[\"backend_merchant\", \"product_id\", \"assortment_flag\"]]\n", "    backend_enable_items = enable_items[enable_items[\"backend_merchant\"] == merchant][\n", "        [\"backend_merchant\", \"product_id\"]\n", "    ]\n", "    total_mapped_items = mapped_items[mapped_items[\"backend_merchant\"] == merchant][\n", "        [\"backend_merchant\", \"product_id\"]\n", "    ]\n", "    total_assortment_items = items_to_disable[\n", "        items_to_disable[\"backend_merchant\"] == merchant\n", "    ][[\"backend_merchant\", \"product_id\", \"assortment_flag\"]]\n", "\n", "    if backend_enable == \"no\":\n", "        disable_items = backend_enable_items.copy()\n", "        items_to_enable = pd.DataFrame()\n", "\n", "    elif backend_enable == \"yes\" and capacity_constraint == \"on\":\n", "        if threshold_reached == \"yes\":\n", "            disable_items = backend_enable_items.copy()\n", "            items_to_enable = pd.DataFrame()\n", "        elif threshold_reached == \"no\":\n", "            disable_items = disable_assortment_items.copy()\n", "            items_to_enable = pd.DataFrame()\n", "\n", "    elif (\n", "        backend_enable == \"yes\"\n", "        and capacity_constraint == \"off\"\n", "        and len(backend_enable_items) < 1000\n", "    ):\n", "        disable_items = pd.DataFrame()\n", "        items_to_enable = total_mapped_items.merge(\n", "            total_assortment_items, on=[\"backend_merchant\", \"product_id\"], how=\"left\"\n", "        )\n", "        items_to_enable = items_to_enable.fillna(0)\n", "        items_to_enable = items_to_enable[items_to_enable[\"assortment_flag\"] == 0]\n", "        items_to_enable.head(2)\n", "    else:\n", "        disable_items = pd.DataFrame()\n", "        items_to_enable = pd.DataFrame()\n", "\n", "    final_disable_items_list = pd.concat([final_disable_items_list, disable_items])\n", "    final_enable_items_list = pd.concat([final_enable_items_list, items_to_enable])\n", "\n", "final_disable_items_list = final_disable_items_list.drop_duplicates()\n", "final_enable_items_list = final_enable_items_list.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_disable_items_list.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_enable_items_list.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Items upload"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# headers = {\n", "#     \"Connection\": \"keep-alive\",\n", "#     \"Accept\": \"*/*\",\n", "#     \"Accept-Encoding\": \"gzip, deflate\",\n", "#     \"Authorization\": \"Basic c2VhcmNoX2FwaTohIzhFZmM1eUdLJll3dDYr\",\n", "#     \"User-Agent\": \"python-requests/2.6.0 CPython/2.7.16 Linux/4.9.0-9-amd64\",\n", "#     \"Content-Type\": \"application/json\",\n", "# }\n", "\n", "\n", "# if len(final_disable_items_list) > 0:\n", "\n", "#     payload = {\n", "#         \"master_inventory_limit\": 0,\n", "#         \"master_b2b_inventory_limit\": 0,\n", "#         \"copy_product_master_il\": 0,\n", "#         \"copy_product_master_b2b_il\": 0,\n", "#         \"inventory_limit\": 0,\n", "#     }\n", "\n", "#     payload = json.dumps(payload)\n", "\n", "#     for merchant in final_disable_items_list[\"backend_merchant\"].unique():\n", "#         df = final_disable_items_list[\n", "#             final_disable_items_list[\"backend_merchant\"] == merchant\n", "#         ]\n", "#         for product in df[\"product_id\"].unique():\n", "#             baseUrl = f\"http://cmsapi.grofers.com/api/stores/{str(merchant)}/products/{str(product)}\"\n", "#             response = requests.request(\"PATCH\", baseUrl, headers=headers, data=payload)\n", "\n", "\n", "# elif len(final_enable_items_list) > 0:\n", "\n", "#     payload = {\n", "#         \"master_inventory_limit\": 0,\n", "#         \"master_b2b_inventory_limit\": 0,\n", "#         \"copy_product_master_il\": 1,\n", "#         \"copy_product_master_b2b_il\": 1,\n", "#         \"inventory_limit\": 1,\n", "#     }\n", "\n", "#     payload = json.dumps(payload)\n", "\n", "#     for merchant in final_enable_items_list[\"backend_merchant\"].unique():\n", "#         df = final_enable_items_list[\n", "#             final_enable_items_list[\"backend_merchant\"] == merchant\n", "#         ]\n", "#         for product in df[\"product_id\"].unique():\n", "#             baseUrl = f\"http://cmsapi.grofers.com/api/stores/{str(merchant)}/products/{str(product)}\"\n", "#             response = requests.request(\"PATCH\", baseUrl, headers=headers, data=payload)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## TEA Mapping Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Active items Tag mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT facility_id as frontend_facility,\n", "                      f.name AS facility_name,\n", "                      item_id,\n", "                      master_assortment_substate_id\n", "      FROM rpc.product_facility_master_assortment a\n", "      INNER JOIN crates.facility f ON f.id=a.facility_id\n", "      WHERE master_assortment_substate_id=1\n", "        AND a.active = 1\n", "        and facility_id not in (21,39,47,51,68,69,118,120,31)\n", "      ORDER BY 1\"\"\"\n", "\n", "active_items = pd.read_sql_query(query, retail)\n", "active_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items.groupby([\"frontend_facility\"])[[\"item_id\"]].nunique().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item tag mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT ro.facility_id AS frontend_facility,\n", "                      tag.outlet_id as frontend_outlet,\n", "                      ro.name as frontend_outlet_name,\n", "                      tag_value AS backend_outlet,\n", "                      r.name AS backend_outlet_name,\n", "                      item_id\n", "      FROM rpc.item_outlet_tag_mapping tag\n", "      INNER JOIN retail.console_outlet ro ON ro.id=tag.outlet_id\n", "      INNER JOIN retail.console_outlet r ON r.id=tag.tag_value\n", "      WHERE tag_type_id=8\n", "        AND tag.active=1\n", "        and ro.name not like \"%%old%%\"\n", "    \n", "      ORDER BY 1,\n", "               2,\n", "               3,\n", "               4\"\"\"\n", "\n", "item_tag = pd.read_sql_query(query, retail)\n", "item_tag.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_tag = active_items.merge(\n", "    item_tag, on=[\"frontend_facility\", \"item_id\"], how=\"left\"\n", ")\n", "active_items_tag = active_items_tag.fillna(\"Direct ordering\")\n", "active_items_tag.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items = active_items_tag.copy()\n", "active_items.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bucket mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT o.facility_id,\n", "                final.item_id,\n", "                final.Final_bucket\n", "FROM\n", "  (SELECT DISTINCT a.item_id,\n", "                  b.cloud_store_id AS outlet_id,\n", "                  (CASE\n", "                        WHEN a.tag_value='A'\n", "                             AND a1.tag_value ='Y' THEN 'X'\n", "                        WHEN a.tag_value='A'\n", "                             AND a1.tag_value IS NULL THEN 'A'\n", "                        ELSE 'B'\n", "                    END) AS Final_bucket\n", "  FROM rpc.item_outlet_tag_mapping a\n", "  INNER JOIN retail.warehouse_outlet_mapping b ON a.outlet_id=b.warehouse_id\n", "  LEFT JOIN rpc.item_outlet_tag_mapping a1 ON a1.item_id=a.item_id\n", "  AND a1.outlet_id=a.outlet_id\n", "  AND a1.tag_type_id=6\n", "  AND a1.tag_value='Y'\n", "  WHERE a.tag_type_id IN (1)\n", "     AND a.active=1\n", "  ORDER BY 2,\n", "            3) final\n", "LEFT JOIN retail.console_outlet o ON final.outlet_id=o.id\n", "ORDER BY 1,\n", "         2\"\"\"\n", "\n", "buckets = pd.read_sql_query(query, retail)\n", "buckets.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets[[\"facility_id\", \"item_id\"]] = buckets[[\"facility_id\", \"item_id\"]].astype(\"int\")\n", "active_items[[\"frontend_facility\", \"item_id\"]] = active_items[\n", "    [\"frontend_facility\", \"item_id\"]\n", "].astype(\"int\")\n", "\n", "active_items_buckets = active_items.merge(\n", "    buckets,\n", "    left_on=[\"frontend_facility\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "active_items_buckets.drop([\"facility_id\"], axis=1, inplace=True)\n", "active_items_buckets[\"Final_bucket\"] = active_items_buckets[\"Final_bucket\"].fillna(\"B\")\n", "active_items_buckets.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet facility mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as backend_outlet,\n", "         facility_id as backend_facility\n", "from retail.console_outlet \n", "\"\"\"\n", "\n", "facility = pd.read_sql_query(query, retail)\n", "facility = facility.dropna()\n", "facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_buckets.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility[\"backend_outlet\"] = facility[\"backend_outlet\"].astype(\"str\")\n", "active_items_buckets[\"backend_outlet\"] = active_items_buckets[\"backend_outlet\"].astype(\n", "    \"str\"\n", ")\n", "\n", "active_items_buckets_new = active_items_buckets.merge(\n", "    facility, on=[\"backend_outlet\"], how=\"left\"\n", ")\n", "active_items_buckets_new[\"backend_facility\"] = active_items_buckets_new[\n", "    \"backend_facility\"\n", "].fillna(\"0\")\n", "\n", "buckets[[\"facility_id\", \"item_id\"]] = buckets[[\"facility_id\", \"item_id\"]].astype(\"int\")\n", "active_items_buckets_new[[\"backend_facility\", \"item_id\"]] = active_items_buckets_new[\n", "    [\"backend_facility\", \"item_id\"]\n", "].astype(\"int\")\n", "buckets = buckets.rename(columns={\"Final_bucket\": \"Final_bucket_new\"})\n", "\n", "active_items_buckets_new = active_items_buckets_new.merge(\n", "    buckets,\n", "    left_on=[\"backend_facility\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "active_items_buckets_new[\"Final_bucket_new\"] = active_items_buckets_new[\n", "    \"Final_bucket_new\"\n", "].fillna(\"B\")\n", "active_items_buckets_new.drop([\"facility_id\"], axis=1, inplace=True)\n", "active_items_buckets_new[\"Final_bucket\"] = active_items_buckets_new[\n", "    \"Final_bucket\"\n", "].astype(\"str\")\n", "active_items_buckets_new.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_buckets_new.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def buckets_identification(x):\n", "    if \"es\" in x[\"facility_name\"].lower():\n", "        return x[\"Final_bucket_new\"]\n", "    elif \"dark\" in x[\"facility_name\"].lower():\n", "        return x[\"Final_bucket_new\"]\n", "    else:\n", "        return x[\"Final_bucket\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items_buckets_new[\"item_bucket\"] = active_items_buckets_new.apply(\n", "    buckets_identification, axis=1\n", ")\n", "active_items_buckets_new = active_items_buckets_new.drop_duplicates()\n", "active_items_buckets_new.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facitity_list = list(active_items_buckets_new[\"frontend_facility\"].unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CPD of items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select distinct wom.cloud_store_id as outlet_id,\n", "dwc.item_id, \n", "sum(dwc.consumption) as final_cpd \n", "from \n", "(select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "date between current_date and current_date +interval 7 day) dwc\n", "join \n", "retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join retail.console_outlet ro on ro.id=dwc.outlet_id\n", "group by 1,2\n", "\"\"\"\n", "cpd1 = pd.read_sql_query(cpd_query, retail)\n", "cpd1.head(2)\n", "\n", "\n", "cpd_query = \"\"\"\n", "select outlet_id as outlet_id,\n", "item_id,\n", "sum(consumption) as final_cpd \n", "from snorlax.date_wise_consumption \n", "where date between current_date and  current_date +interval 7 day\n", "group by 1,2\n", "\n", "\"\"\"\n", "cpd2 = pd.read_sql_query(cpd_query, retail)\n", "\n", "cpd = pd.concat([cpd1, cpd2])\n", "cpd.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility.rename(columns={\"backend_facility\": \"frontend_facility\"}, inplace=True)\n", "facility[\"backend_outlet\"] = facility[\"backend_outlet\"].astype(\"int\")\n", "cpd_facility = cpd.merge(\n", "    facility, left_on=[\"outlet_id\"], right_on=[\"backend_outlet\"], how=\"left\"\n", ")\n", "cpd_facility.drop([\"backend_outlet\"], axis=1, inplace=True)\n", "cpd_facility = (\n", "    cpd_facility.groupby([\"frontend_facility\", \"item_id\"])[[\"final_cpd\"]]\n", "    .max()\n", "    .reset_index()\n", ")\n", "cpd_facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data = active_items_buckets_new.merge(\n", "    cpd_facility, on=[\"frontend_facility\", \"item_id\"], how=\"left\"\n", ")\n", "final_data[\"final_cpd\"] = final_data[\"final_cpd\"].fillna(0)\n", "final_data = final_data[\n", "    [\n", "        \"frontend_facility\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"master_assortment_substate_id\",\n", "        \"frontend_outlet\",\n", "        \"frontend_outlet_name\",\n", "        \"backend_outlet\",\n", "        \"backend_outlet_name\",\n", "        \"item_bucket\",\n", "        \"final_cpd\",\n", "    ]\n", "]\n", "final_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_summary = (\n", "    final_data.groupby(\n", "        [\n", "            \"frontend_facility\",\n", "            \"facility_name\",\n", "            \"frontend_outlet_name\",\n", "            \"backend_outlet_name\",\n", "            \"item_bucket\",\n", "        ]\n", "    )\n", "    .agg({\"item_id\": \"nunique\", \"final_cpd\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "final_summary.sort_values(\n", "    by=[\"frontend_facility\", \"backend_outlet_name\", \"item_bucket\"], inplace=True\n", ")\n", "final_summary.rename(columns={\"item_id\": \"item_count\"}, inplace=True)\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "final_summary[\"updated_at\"] = now\n", "final_summary.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1xuKfbwH9IIaahJ2JtjIHxOsFbMXbbLKZ2dUiF7LAFKQ\"\n", "sheet_name = \"summary_raw\"\n", "pb.to_sheets(final_summary, sheet_id, sheet_name, clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item product mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "item_product_mapping = pd.read_sql(item_attributes_query, redshift)\n", "item_product_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### adding threshold/default doi"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON><PERSON><PERSON> doi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT o.facility_id as frontend_facility,\n", "                   dd.item_id,\n", "                   default_doi\n", "   FROM po.outlet_item_default_doi dd\n", "   INNER JOIN retail.console_outlet o ON o.id=dd.outlet_id\n", "  WHERE dd.active=1\n", "  and o.name not like '%%cold%%'\n", "  and o.name not like '%%large%%'\n", "  and o.name not like '%%bulk%%' \"\"\"\n", "\n", "default_doi = pd.read_sql_query(query, retail)\n", "default_doi.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON><PERSON><PERSON><PERSON> doi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT o.facility_id as frontend_facility,\n", "                td.item_id,\n", "                threshold_doi\n", "FROM po.outlet_item_threshold_doi td\n", "INNER JOIN retail.console_outlet o ON o.id=td.outlet_id\n", "WHERE td.active=1\n", "and o.name not like '%%cold%%'\n", "and o.name not like '%%large%%'\n", "and o.name not like '%%bulk%%'\"\"\"\n", "\n", "threshold_doi = pd.read_sql_query(query, retail)\n", "threshold_doi.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_default_doi = final_data.merge(\n", "    default_doi, on=[\"frontend_facility\", \"item_id\"], how=\"left\"\n", ")\n", "final_data_doi = final_data_default_doi.merge(\n", "    threshold_doi, on=[\"frontend_facility\", \"item_id\"], how=\"left\"\n", ")\n", "final_data_doi = final_data_doi.fillna(0)\n", "final_data_doi[\"wt_default_doi\"] = (\n", "    final_data_doi[\"final_cpd\"] * final_data_doi[\"default_doi\"]\n", ")\n", "final_data_doi[\"wt_threshold_doi\"] = (\n", "    final_data_doi[\"final_cpd\"] * final_data_doi[\"threshold_doi\"]\n", ")\n", "\n", "default_doi_not_available = final_data_doi[(final_data_doi[\"default_doi\"] == 0)]\n", "default_doi_not_available[\"default_doi_flag\"] = 1\n", "default_doi_not_available = default_doi_not_available[\n", "    [\"frontend_facility\", \"item_id\", \"default_doi_flag\"]\n", "].drop_duplicates()\n", "threshold_doi_not_available = final_data_doi[(final_data_doi[\"threshold_doi\"] == 0)]\n", "threshold_doi_not_available[\"threshold_doi_flag\"] = 1\n", "threshold_doi_not_available = threshold_doi_not_available[\n", "    [\"frontend_facility\", \"item_id\", \"threshold_doi_flag\"]\n", "].drop_duplicates()\n", "\n", "final_data_doi = final_data_doi.merge(\n", "    default_doi_not_available, on=[\"frontend_facility\", \"item_id\"], how=\"left\"\n", ")\n", "final_data_doi = final_data_doi.merge(\n", "    threshold_doi_not_available, on=[\"frontend_facility\", \"item_id\"], how=\"left\"\n", ")\n", "final_data_doi = final_data_doi.fillna(0)\n", "final_data_doi.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_doi = (\n", "    final_data_doi.groupby(\n", "        [\n", "            \"frontend_facility\",\n", "            \"facility_name\",\n", "            \"frontend_outlet_name\",\n", "            \"backend_outlet_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"default_doi_flag\": \"sum\",\n", "            \"threshold_doi_flag\": \"sum\",\n", "            \"final_cpd\": \"sum\",\n", "            \"wt_default_doi\": \"sum\",\n", "            \"wt_threshold_doi\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "summary_doi[\"default_doi\"] = summary_doi[\"wt_default_doi\"] / summary_doi[\"final_cpd\"]\n", "summary_doi[\"threshold_doi\"] = (\n", "    summary_doi[\"wt_threshold_doi\"] / summary_doi[\"final_cpd\"]\n", ")\n", "summary_doi.rename(\n", "    columns={\n", "        \"item_id\": \"item_count\",\n", "        \"default_doi_flag\": \"default_doi_not_available\",\n", "        \"threshold_doi_flag\": \"threshold_doi_not_available\",\n", "    },\n", "    inplace=True,\n", ")\n", "summary_doi.drop([\"wt_default_doi\", \"wt_threshold_doi\"], axis=1, inplace=True)\n", "summary_doi.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1xuKfbwH9IIaahJ2JtjIHxOsFbMXbbLKZ2dUiF7LAFKQ\"\n", "sheet_name = \"summary_doi_raw\"\n", "pb.to_sheets(summary_doi, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_data_doi.drop(\n", "    [\"wt_default_doi\", \"wt_threshold_doi\", \"default_doi_flag\", \"threshold_doi_flag\"],\n", "    axis=1,\n", "    inplace=True,\n", ")\n", "raw = final_data_doi.merge(item_product_mapping, on=[\"item_id\"], how=\"left\")\n", "\n", "raw[\"updated_at\"] = now\n", "raw.sort_values(\n", "    by=[\n", "        \"frontend_facility\",\n", "        \"backend_outlet_name\",\n", "    ],\n", "    inplace=True,\n", ")\n", "raw = raw.drop_duplicates()\n", "\n", "\n", "def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [\n", "        {\"name\": key, \"type\": ref_dict[value], \"description\": key}\n", "        for key, value in metadata.items()\n", "    ]\n", "\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"item_tea_tag_mapping\",\n", "    \"column_dtypes\": redshift_schema(raw),\n", "    \"primary_key\": [\"frontend_facility\", \"item_id\"],\n", "    \"sortkey\": [\"frontend_facility\", \"backend_outlet\"],\n", "    \"incremental_key\": [\"updated_at\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"TEA mapping details\",\n", "}\n", "\n", "pb.to_redshift(raw, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}