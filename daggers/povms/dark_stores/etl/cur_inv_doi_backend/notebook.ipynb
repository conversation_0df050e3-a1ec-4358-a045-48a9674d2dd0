{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import math"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"180GgdsvSBzYFxwMHG6wZQrSsP2Grczq5jbHmI23oO78\"\n", "Mapping = pb.from_sheets(sheet_id, \"Mapping\")\n", "Mapping.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_outlet_id = Mapping[\"Backend_facility\"]\n", "# ds_doi = Mapping['doi']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_outlet_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlet_threshold_doi = {'ds_outlet_id' : ds_outlet_id\n", "#               , 'ds_doi':ds_doi\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlet_threshold_doi_df =  pd.DataFrame.from_dict(outlet_threshold_doi)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# column_dtypes = [{\"name\":\"ds_outlet_id\", \"type\":\"int\"},\n", "#             {\"name\":\"ds_doi\", \"type\":\"int\"}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# kwargs = {\n", "#     \"schema_name\": \"metrics\",\n", "#     \"table_name\": \"Dark_store_doi_details\",\n", "#     \"column_dtypes\": column_dtypes,\n", "#     \"primary_key\": [\"ds_outlet_id\"],\n", "#     \"sortkey\": [\"ds_outlet_id\"],\n", "#     \"incremental_key\": [\"ds_outlet_id\"],\n", "#     \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "# }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pb.to_redshift(outlet_threshold_doi_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outlet_threshold_doi_df[['ds_outlet_id','ds_doi']] = outlet_threshold_doi_df[['ds_outlet_id','ds_doi']].astype('int')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Current Inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv_q = \"\"\"\n", " \n", "     SELECT co.facility_id,\n", "     iii.outlet_id,\n", "     iii.item_id,\n", "     product.name as item_name,\n", "     iii.created_at , \n", "     (iii.quantity - COALESCE(sum(case when iibi.blocked_type in (1,2,4,5) then iibi.quantity else 0 end),0))  as 'Current Inventory'\n", "     FROM\n", "ims.ims_item_inventory iii\n", "     LEFT JOIN\n", "ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "     INNER JOIN\n", "retail.warehouse_outlet_mapping wom on iii.outlet_id = wom.cloud_store_id\n", "     INNER JOIN\n", "retail.console_outlet co ON co.id=iii.outlet_id AND co.facility_id in %(facility_id)s \n", "     INNER JOIN\n", "retail.console_location cl ON cl.id = co.tax_location_id\n", "     INNER JOIN\n", "(SELECT item_id, name FROM rpc.product_product pp2 \n", "WHERE pp2.id in (\n", "SELECT max(id) as id FROM rpc.product_product pp \n", "WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", ") product ON product.item_id = iii.item_id\n", "WHERE iii.active = 1 \n", "GROUP BY 1,2,3,4\n", "\n", "\n", "\"\"\"\n", "item_inv = pd.read_sql_query(\n", "    sql=item_inv_q,\n", "    con=retail,\n", "    params={\"facility_id\": tuple(Mapping[\"Backend_facility\"])},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv.outlet_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv[(item_inv.facility_id == 92) & (item_inv.item_id == 10063554)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CPD Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g = item_inv.groupby([\"facility_id\", \"item_id\"])\n", "item_inv[\"RN\"] = g[\"created_at\"].rank(method=\"min\", ascending=False, na_option=\"bottom\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv = item_inv[item_inv[\"RN\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_estimated_q = \"\"\"select\n", "\n", "# l.dark_store_outlet,\n", "# l.item_id as item_id_estimated,\n", "# coalesce(l.avg_cpd_at_darkstore,0) as avg_cpd_at_darkstore\n", "\n", "# from metrics.dark_stores_indents_log l\n", "\n", "# inner join\n", "\n", "# (select dark_store_outlet, item_id , max(created_at_ist) as max_created_at\n", "# from metrics.dark_stores_indents_log\n", "# where dark_store_outlet = 846\n", "# group by 1,2\n", "# )T\n", "\n", "# on created_at_ist = max_created_at and T.item_id = l.item_id and T.dark_store_outlet = l.dark_store_outlet\n", "# where  l.dark_store_outlet = 846\n", "# order by l.item_id \"\"\"\n", "\n", "# cpd_estimated = pd.read_sql_query(sql=cpd_estimated_q, con = redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_estimated.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_snorlax_q = \"\"\"SELECT co.facility_id,\n", "wom.cloud_store_id AS outlet_id,\n", "       dwc.item_id ,\n", "       case when round(avg(dwc.consumption),0) = 0 then 1 else round(avg(dwc.consumption),0) end  as cpd\n", "FROM\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          date,\n", "          consumption\n", "   FROM snorlax.date_wise_consumption\n", "   WHERE date BETWEEN CURRENT_DATE AND CURRENT_DATE + interval 5 DAY) dwc\n", "INNER JOIN retail.warehouse_outlet_mapping wom ON dwc.outlet_id = wom.warehouse_id\n", "INNER JOIN retail.console_outlet co ON co.id=wom.cloud_store_id  AND co.facility_id in %(facility_id)s \n", "-- WHERE wom.cloud_store_id IN  %(facility_id)s\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "\"\"\"\n", "\n", "cpd_snorlax = pd.read_sql_query(\n", "    sql=cpd_snorlax_q,\n", "    con=retail,\n", "    params={\"facility_id\": tuple(Mapping[\"Backend_facility\"])},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_snorlax.head(50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T = item_inv.merge(\n", "    cpd_snorlax,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"outlet_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"outlet_id\", \"item_id\"],\n", ")[[\"facility_id\", \"outlet_id\", \"item_id\", \"item_name\", \"Current Inventory\", \"cpd\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T[\"Current DOI\"] = (T[\"Current Inventory\"] / T[\"cpd\"]).round()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Grofers operational facility\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"Grofers operational outlet\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"Grofers item\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"name of the item\"},\n", "    {\n", "        \"name\": \"Current Inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"current inventory of the item\",\n", "    },\n", "    {\"name\": \"cpd\", \"type\": \"float\", \"description\": \"consumption per day of the item\"},\n", "    {\n", "        \"name\": \"Current DOI\",\n", "        \"type\": \"float\",\n", "        \"description\": \"current days of inventory if the item\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"current_inv_doi_backend_end\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"outlet_id\", \"item_id\"],\n", "    \"sortkey\": [\"facility_id\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": [\"outlet_id\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"captures the current inventory and doi of backend merchant\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(T, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}