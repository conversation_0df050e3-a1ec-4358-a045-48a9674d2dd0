template_name: notebook
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
path: povms/dark_stores/etl/cur_inv_doi_backend
namespace: povms
project_name: dark_stores
dag_name: cur_inv_doi_backend
version: 3
owner:
  email: <EMAIL>
  slack_id: UABB9AK9V
schedule:
  start_date: '2021-03-15T15:30:00'
  interval: '*/30 * * * *'
notebook:
  # https://airflow.apache.org/macros.html
  # you can define your own parameters
  parameters:
dag_type: etl
escalation_priority: low
schedule_type: fixed
sla: 120 minutes
tags: []
support_files: []
paused: true
executor:
  type: kubernetes
  config:
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    node_selectors:
      nodetype: spot
    tolerations:
    - key: service
      operator: Equal
      value: airflow
      effect: NoSchedule
    cpu:
      request: 0.5
      limit: 1
    memory:
      request: 500M
      limit: 4G
    volume_mounts:
    - name: airflow-dags
      subPath: airflow/plugins
      mountPath: /usr/local/airflow/plugins
