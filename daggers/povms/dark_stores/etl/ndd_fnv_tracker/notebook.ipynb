{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime\n", "import math\n", "import json\n", "import uuid\n", "import logging\n", "from copy import deepcopy\n", "import warnings\n", "import numpy as np\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "import os\n", "import boto3\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import time\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.DEBUG)\n", "\n", "pd.set_option(\"display.max_colwidth\", 40)\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "CON_SQL = pb.get_connection(\"retail\")\n", "start = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1YhEaM7HYJLgXGMSCcc_kuhIsnyJp2zQz7qAMeR_Rlp0\"\n", "sheet_name = \"Inputs\"\n", "assortment = pb.from_sheets(\n", "    sheet_id, sheet_name, service_account=\"service_account\", clear_cache=True\n", ").query(\"active=='1'\")\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment.astype({\"item_id\": int, \"outlet_id\": int})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_ids = tuple([int(x) for x in list(assortment.outlet_id.unique())])\n", "item_ids = tuple([int(x) for x in list(assortment.item_id.unique())])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting Overall Inventory\n", "query = f\"\"\" SELECT DISTINCT a.item_id, \n", "                a.outlet_id,\n", "                CASE\n", "                    WHEN sum(quantity) > 0 THEN sum(quantity)\n", "                    ELSE 0\n", "                END AS available_quantity,\n", "                fnv_upper_limit\n", "FROM ims.ims_item_inventory AS a\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "          max(updated_at)AS update_at\n", "   FROM ims.ims_item_inventory\n", "   GROUP BY 1,\n", "            2) AS b ON a.item_id=b.item_id\n", "AND a.outlet_id=b.outlet_id\n", "AND a.updated_at=b.update_at\n", "where a.outlet_id in {outlet_ids}\n", "  AND a.item_id in {item_ids}\n", "GROUP BY 1,\n", "         2\"\"\"\n", "total_inventory = pd.read_sql_query(query, CON_SQL)\n", "total_inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting blocked inventory\n", "blocked = f\"\"\"select item_id ,\n", "     i.outlet_id ,\n", "     case when sum(quantity) > 0 then sum(quantity) else 0 end as blocked_quantity\n", "FROM ims.ims_item_blocked_inventory i\n", "where i.outlet_id in {outlet_ids}\n", "and item_id in {item_ids}\n", "and blocked_type in (1,2,4,5) and i.active = 1\n", "group by 1,2\n", "\"\"\"\n", "\n", "block_quantity = pd.read_sql_query(blocked, CON_SQL)\n", "block_quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Joining blocked and unblocked inventory\n", "inv_data = total_inventory.merge(block_quantity, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "inv_data = inv_data.drop_duplicates().fillna(0)\n", "inv_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data = pd.merge(assortment, inv_data, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data[\"unblocked_quantity\"] = (\n", "    overall_data[\"available_quantity\"] - overall_data[\"blocked_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["app_live_query = \"\"\"\n", "WITH item_merchant AS\n", "  ( SELECT DISTINCT mpm.product_id,\n", "                    mpm.merchant_id\n", "   FROM lake_cms.gr_merchant_product_mapping AS mpm\n", "   WHERE mpm.inventory_limit>0\n", "     AND mpm.enabled_flag=1\n", "     AND IS_AVAILABLE = 1),\n", "     merchant_mapping AS\n", "  ( SELECT frontend.id frontend_id,\n", "           frontend.name frontend_name,\n", "           backend.id backend_id,\n", "           backend.name backend_name\n", "   FROM lake_cms.view_gr_merchant frontend\n", "   JOIN lake_cms.gr_virtual_to_real_merchant_mapping vmm ON frontend.id=vmm.virtual_merchant_id\n", "   JOIN lake_cms.view_gr_merchant backend ON backend.id=vmm.real_merchant_id\n", "   WHERE frontend.enabled_flag =1\n", "     AND vmm.enabled_flag=1\n", "     AND backend.name NOT ILIKE '%%Smart Bachat Club%%'\n", "     AND backend.name NOT ILIKE '%%Donation%%'\n", "     AND backend.name NOT ILIKE '%%Collection%%'\n", "     AND backend.name NOT ILIKE '%%Collect Return%%'\n", "     AND backend.name NOT ILIKE '%%Dummy%%'\n", "     AND backend.name NOT ILIKE '%%test%%' ),\n", "     final_data AS\n", "  ( SELECT DISTINCT a.*\n", "   FROM item_merchant a\n", "   INNER JOIN merchant_mapping m ON m.backend_id=a.merchant_id),\n", "     item_outlet AS\n", "  ( SELECT DISTINCT CURRENT_DATE AS order_date,\n", "                                    mpm.product_id,\n", "                                    rt.outlet_id\n", "   FROM final_data mpm\n", "   INNER JOIN lake_retail.console_outlet_cms_store rt ON mpm.merchant_id=rt.cms_store\n", "   WHERE rt.Active=1\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     product_offer_mapping AS\n", "  ( SELECT item_id,\n", "           a.product_id,\n", "           outlet_id,\n", "           order_date,\n", "           offer_id\n", "   FROM item_outlet AS a\n", "   LEFT JOIN lake_rpc.item_product_mapping AS b ON a.product_id=b.product_id\n", "   WHERE b.active=1 ),\n", "     item_not_null AS\n", "  ( SELECT item_id,\n", "           order_date,\n", "           outlet_id\n", "   FROM product_offer_mapping\n", "   WHERE item_id IS NOT NULL ),\n", "     item_null AS\n", "  ( SELECT *\n", "   FROM product_offer_mapping\n", "   WHERE item_id IS NULL ),\n", "     offer_details AS\n", "  (SELECT DISTINCT offer_reference_id :: varchar,\n", "                   cast(field_val AS int) AS item_id\n", "   FROM lake_offer_master.offer\n", "   LEFT JOIN lake_offer_master.offer_field o_field ON o_field.offer_id=offer.id\n", "   LEFT JOIN lake_offer_master.offer_type_field otf ON o_field.offer_type_field_id = otf.id\n", "   WHERE otf.field_name LIKE '%%product%%'),\n", "     item_offer_joining AS\n", "  ( SELECT b.item_id,\n", "           product_id,\n", "           outlet_id,\n", "           order_date,\n", "           offer_id\n", "   FROM item_null AS a\n", "   LEFT JOIN offer_details AS b ON a.offer_id=b.offer_reference_id),\n", "     item_facility_final AS\n", "  ( SELECT item_id,\n", "           order_date,\n", "           outlet_id\n", "   FROM item_offer_joining\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     union_table AS\n", "  (SELECT a.*\n", "   FROM\n", "     ((SELECT *\n", "      FROM item_not_null) \n", "   UNION\n", "     (SELECT *\n", "      FROM item_facility_final)) a )\n", "SELECT item_id,\n", "outlet_id ,\n", "1 as app_live\n", "FROM union_table\n", "WHERE item_id IS NOT NULL\n", "\"\"\"\n", "app_live = pd.read_sql_query(sql=app_live_query, con=CON_REDSHIFT)\n", "app_live.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data = pd.merge(\n", "    overall_data,\n", "    app_live,\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "overall_data.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data[\"live\"] = np.where(\n", "    (overall_data[\"unblocked_quantity\"] > 0) | (overall_data[\"app_live\"] == 1), 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_timestamp_in_ist(datetime_utc):\n", "    return datetime_utc + datetime.timedelta(hours=5, minutes=30)\n", "\n", "\n", "def get_timestamp_in_utc(datetime_ist):\n", "    return datetime_ist - datetime.timedelta(hours=5, minutes=30)\n", "\n", "\n", "today = datetime.datetime.combine(\n", "    get_timestamp_in_ist(datetime.datetime.utcnow()), datetime.time.min\n", ")\n", "until_t = today + datetime.timedel<PERSON>(days=1)\n", "today = get_timestamp_in_utc(today)\n", "until_t = get_timestamp_in_utc(until_t)\n", "print(str(today), str(until_t))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "                SELECT poi.item_id,\n", "                       wom.cloud_store_id as outlet_id,\n", "                       poi.units_ordered AS po_qty\n", "                FROM po.purchase_order p\n", "                LEFT JOIN po.po_schedule ps ON p.id=ps.po_id_id\n", "                INNER JOIN po.purchase_order_items poi ON p.id=poi.po_id\n", "                INNER JOIN retail.console_outlet o ON o.id=p.outlet_id\n", "                INNER JOIN retail.warehouse_outlet_mapping wom ON p.outlet_id=wom.warehouse_id\n", "                INNER JOIN retail.console_location cl ON cl.id=o.tax_location_id\n", "                INNER JOIN retail.auth_user a ON a.id=p.created_by\n", "                INNER JOIN po.purchase_order_status posa ON posa.po_id = p.id\n", "                INNER JOIN po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "                WHERE posta.id NOT IN (4,\n", "                                       5,\n", "                                       8,\n", "                                       9,\n", "                                       10)\n", "                  AND ps.schedule_date_time between '{str(today)}' and '{str(until_t)}' \n", "                  AND poi.item_id IN {item_ids}\n", "                  and wom.cloud_store_id in {outlet_ids}\n", "                \"\"\"\n", "po_quantity = pd.read_sql_query(query, CON_SQL)\n", "po_quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data_po = pd.merge(\n", "    overall_data, po_quantity, on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data_po[\"expected_qty_after_po\"] = (\n", "    overall_data_po[\"unblocked_quantity\"] + overall_data_po[\"po_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data_po[\"expected_live\"] = np.where(\n", "    (overall_data_po[\"expected_qty_after_po\"] > 0) | (overall_data_po[\"live\"] == 1),\n", "    1,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data_po_agg = (\n", "    overall_data_po.groupby([\"outlet_name\", \"outlet_id\"])\n", "    .agg({\"item_id\": \"count\", \"live\": \"sum\", \"expected_live\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data_po_agg[\"availability\"] = overall_data_po_agg[\"live\"] / overall_data_po_agg[\"item_id\"]\n", "overall_data_po_agg[\"expected_availability\"] = (\n", "    overall_data_po_agg[\"expected_live\"] / overall_data_po_agg[\"item_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data_po_agg = overall_data_po_agg.rename(columns={\"item_id\": \"#_active_items\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def float_to_per(x):\n", "    return f\"{str(round((x*100), 2))}%\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_data_po_agg[\"availability\"] = overall_data_po_agg[\"availability\"].apply(float_to_per)\n", "overall_data_po_agg[\"expected_availability\"] = overall_data_po_agg[\"expected_availability\"].apply(\n", "    float_to_per\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["IST = timezone(\"Asia/Kolkata\")\n", "overall_data_po_agg[\"updated_at\"] = datetime.datetime.now(IST).strftime(\"%Y-%m-%d %H:%M:%S\")\n", "overall_data_po[\"updated_at\"] = datetime.datetime.now(IST).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(overall_data_po_agg, sheet_id, \"Report\", service_account=\"service_account\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(overall_data_po, sheet_id, \"Raw Data\", service_account=\"service_account\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}