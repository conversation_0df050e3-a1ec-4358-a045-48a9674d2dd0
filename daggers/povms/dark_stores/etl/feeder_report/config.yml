dag_name: feeder_report
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: ankit
  slack_id: U03RURE15HB
path: povms/dark_stores/etl/feeder_report
paused: true
pool: povms_pool
project_name: dark_stores
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: '*/30 * * * *'
  start_date: '2021-03-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: gobd-data-alerts
support_files: []
tags: []
template_name: notebook
version: 7
