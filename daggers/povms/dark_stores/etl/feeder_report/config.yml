dag_name: feeder_report
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep:stable
namespace: povms
notebook:
  parameters: null
owner:
  name: ankit
  slack_id: U03RURE15HB
path: povms/dark_stores/etl/feeder_report
paused: true
project_name: dark_stores
schedule:
  interval: '*/30 * * * *'
  start_date: '2021-03-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: gobd-data-alerts
support_files: []
tags: []
template_name: notebook
version: 7
