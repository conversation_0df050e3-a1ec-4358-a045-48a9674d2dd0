{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime\n", "import math\n", "import json\n", "import uuid\n", "import logging\n", "from copy import deepcopy\n", "import warnings\n", "import numpy as np\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "import os\n", "import boto3\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import time\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.DEBUG)\n", "\n", "pd.set_option(\"display.max_colwidth\", 40)\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "CON_SQL = pb.get_connection(\"retail\")\n", "CON_PT = pb.get_connection(\"promise_time\")\n", "start = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_timestamp_in_ist(datetime_utc):\n", "    return datetime_utc + datetime.timedelta(hours=5, minutes=30)\n", "\n", "\n", "def get_timestamp_in_utc(datetime_ist):\n", "    return datetime_ist - datetime.timedelta(hours=5, minutes=30)\n", "\n", "\n", "today = datetime.datetime.combine(\n", "    get_timestamp_in_ist(datetime.datetime.utcnow()), datetime.time.min\n", ")\n", "until_t = get_timestamp_in_utc(today + datetime.timedelta(days=1))\n", "until_t_plus_one = get_timestamp_in_utc(today + datetime.timedelta(days=2))\n", "until_t_plus_two = get_timestamp_in_utc(today + datetime.timedelta(days=3))\n", "until_t_plus_three = get_timestamp_in_utc(today + datetime.timedelta(days=4))\n", "until_t_plus_four = get_timestamp_in_utc(today + datetime.timedelta(days=5))\n", "until_t_plus_five = get_timestamp_in_utc(today + datetime.timedelta(days=6))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    counter = 0\n", "    while counter < 5:\n", "        try:\n", "            start = time.time()\n", "            print(\"Counter \", counter)\n", "            df = pd.read_sql_query(sql=sql, con=con)\n", "            end = time.time()\n", "            print((end - start), \"secs\")\n", "            return df\n", "            break\n", "        except:\n", "            counter += 1\n", "            if counter == 5:\n", "                start = time.time()\n", "                print(\"Counter \", counter)\n", "                df = pd.read_sql_query(sql=sql, con=con)\n", "                end = time.time()\n", "                print((end - start), \"secs\")\n", "                return df\n", "                break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_query = f\"\"\"SELECT a.item_id,\n", "       f.name AS item_name,\n", "       FLOOR(wom.cloud_store_id) as backend_outlet,\n", "       c.tag_value,\n", "       b.facility_id AS frontend_facility,\n", "       b.id AS frontend_outlet,\n", "       b.name AS frontend_outlet_name,\n", "       a.master_assortment_substate_id \n", "FROM rpc.product_facility_master_assortment a\n", "LEFT JOIN retail.console_outlet b ON a.facility_id = b.facility_id\n", "AND b.active = 1\n", "AND a.active = 1\n", "INNER JOIN rpc.item_outlet_tag_mapping c ON c.tag_type_id = 8\n", "AND c.active = 1\n", "AND c.outlet_id = b.id\n", "AND c.item_id = a.item_id\n", "INNER JOIN\n", "  (SELECT DISTINCT r.item_id,\n", "                   r.name\n", "   FROM rpc.product_product r\n", "   INNER JOIN\n", "     (SELECT item_id,\n", "             max(created_at) AS dated\n", "      FROM rpc.product_product\n", "      GROUP BY 1) a ON a.item_id=r.item_id\n", "   WHERE r.created_at=a.dated\n", "     AND r.outlet_type!=1 ) f ON a.item_id = f.item_id\n", "left join retail.warehouse_outlet_mapping wom\n", "on c.tag_value = wom.warehouse_id\n", "WHERE b.name LIKE '%%ES%%' and b.name NOT LIKE '%%DarkS%%'\n", "  AND a.active = 1\n", "  AND a.master_assortment_substate_id = 1\"\"\"\n", "assortment = read_sql_query(sql=assortment_query, con=CON_SQL)\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Query to extract inventory holding outlets\n", "# HOT for dual entity\n", "# SSC for single entity\n", "inventory_holding_query = \"\"\"\n", "SELECT\n", "    pfom.outlet_id as outlet_id_hot,\n", "    case\n", "        when wom.cloud_store_id is NULL then pfom.outlet_id\n", "        else wom.cloud_store_id\n", "    end as inventory_holding_outlet\n", "FROM\n", "\tpo.physical_facility_outlet_mapping pfom\n", "    LEFT JOIN retail.warehouse_outlet_mapping wom\n", "    ON pfom.outlet_id = wom.warehouse_id\n", "WHERE\n", "    pfom.active = 1 AND\n", "    pfom.ars_active = 1;\n", "\"\"\"\n", "inventory_holding = read_sql_query(sql=inventory_holding_query, con=CON_SQL)\n", "inventory_holding.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Joining HOT/Inventory holding outlet with assortment\n", "assortment = pd.merge(\n", "    assortment,\n", "    inventory_holding,\n", "    left_on=[\"frontend_outlet\"],\n", "    right_on=[\"outlet_id_hot\"],\n", "    how=\"left\",\n", ")\n", "assortment = assortment.drop([\"outlet_id_hot\"], axis=1)\n", "assortment = assortment.rename(\n", "    columns={\"inventory_holding_outlet\": \"inventory_holding_frontend_outlet\"}\n", ")\n", "\n", "assortment = pd.merge(\n", "    assortment,\n", "    inventory_holding,\n", "    left_on=[\"backend_outlet\"],\n", "    right_on=[\"inventory_holding_outlet\"],\n", "    how=\"left\",\n", ")\n", "assortment = assortment.drop([\"inventory_holding_outlet\"], axis=1)\n", "assortment = assortment.rename(columns={\"outlet_id_hot\": \"backend_hot_outlet\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Stringified list of outlets and items\n", "all_outlets = \", \".join(\n", "    map(\n", "        str,\n", "        list(assortment.inventory_holding_frontend_outlet.unique())\n", "        + list(assortment.backend_outlet.unique()),\n", "    )\n", ").replace(\", nan\", \"\")\n", "\n", "all_items = \", \".join(map(str, list(assortment.item_id.unique()))).replace(\", nan\", \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting Overall Inventory\n", "query = f\"\"\" SELECT DISTINCT a.item_id, \n", "                a.outlet_id,\n", "                CASE\n", "                    WHEN sum(quantity) > 0 THEN sum(quantity)\n", "                    ELSE 0\n", "                END AS available_quantity\n", "FROM ims.ims_item_inventory AS a\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "          max(updated_at)AS update_at\n", "   FROM ims.ims_item_inventory\n", "   GROUP BY 1,\n", "            2) AS b ON a.item_id=b.item_id\n", "AND a.outlet_id=b.outlet_id\n", "AND a.updated_at=b.update_at\n", "where a.outlet_id in ({all_outlets})\n", "\n", "  AND a.item_id in ({all_items})\n", "GROUP BY 1,\n", "         2\"\"\"\n", "total_inventory = read_sql_query(query, CON_SQL)\n", "total_inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting blocked inventory\n", "\n", "blocked = f\"\"\"select item_id ,\n", "     i.outlet_id ,\n", "     case when sum(quantity) > 0 then sum(quantity) else 0 end as blocked_quantity\n", "FROM ims.ims_item_blocked_inventory i\n", "where i.outlet_id in ({all_outlets})\n", "and item_id in ({all_items})\n", "and blocked_type in (1,2,4,5) and i.active = 1\n", "group by 1,2\n", "\"\"\"\n", "\n", "block_quantity = read_sql_query(blocked, CON_SQL)\n", "block_quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Joining blocked and unblocked inventory\n", "inv_data = total_inventory.merge(block_quantity, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "inv_data = inv_data.drop_duplicates().fillna(0)\n", "inv_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Joining inventory with assortment on frontend outlet\n", "assortment_frontend_qtys = pd.merge(\n", "    assortment,\n", "    inv_data,\n", "    left_on=[\"item_id\", \"inventory_holding_frontend_outlet\"],\n", "    right_on=[\"item_id\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_frontend_qtys = assortment_frontend_qtys.rename(\n", "    columns={\n", "        \"available_quantity\": \"available_quantity_frontend\",\n", "        \"blocked_quantity\": \"blocked_quantity_frontend\",\n", "    }\n", ")\n", "\n", "assortment_frontend_qtys = assortment_frontend_qtys.drop([\"outlet_id\"], axis=1)\n", "\n", "assortment_frontend_qtys.fillna(0, inplace=True)\n", "\n", "assortment_frontend_qtys.head(2)"]}, {"cell_type": "raw", "metadata": {}, "source": ["# Total quantity frontend = Overall - blocked (0 if -ve)\n", "assortment_frontend_qtys['total_quantity_frontend'] = np.where(assortment_frontend_qtys['blocked_quantity_frontend'] > assortment_frontend_qtys['available_quantity_frontend'],\n", "                                                              0,assortment_frontend_qtys['available_quantity_frontend']-assortment_frontend_qtys['blocked_quantity_frontend'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_frontend_qtys[\"total_quantity_frontend\"] = (\n", "    assortment_frontend_qtys[\"available_quantity_frontend\"]\n", "    - assortment_frontend_qtys[\"blocked_quantity_frontend\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Joining inventory with assortment on backend outlet\n", "assortment_backend_qtys = pd.merge(\n", "    assortment_frontend_qtys,\n", "    inv_data,\n", "    left_on=[\"item_id\", \"backend_outlet\"],\n", "    right_on=[\"item_id\", \"outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "assortment_backend_qtys = assortment_backend_qtys.rename(\n", "    columns={\n", "        \"available_quantity\": \"available_quantity_backend\",\n", "        \"blocked_quantity\": \"blocked_quantity_backend\",\n", "    }\n", ")\n", "\n", "assortment_backend_qtys = assortment_backend_qtys.drop([\"outlet_id\"], axis=1)\n", "\n", "assortment_backend_qtys.fillna(0, inplace=True)\n", "\n", "assortment_backend_qtys.head(2)"]}, {"cell_type": "raw", "metadata": {}, "source": ["# Total quantity backend = Overall - blocked (0 if -ve)\n", "assortment_backend_qtys['total_quantity_backend'] = np.where(assortment_backend_qtys['blocked_quantity_backend'] > assortment_backend_qtys['available_quantity_backend'],\n", "                                                              0,assortment_backend_qtys['available_quantity_backend']-assortment_backend_qtys['blocked_quantity_backend'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_backend_qtys[\"total_quantity_backend\"] = (\n", "    assortment_backend_qtys[\"available_quantity_backend\"]\n", "    - assortment_backend_qtys[\"blocked_quantity_backend\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting open STOs\n", "sto_query = f\"\"\"\n", "SELECT merchant_outlet_id AS outlet_id,\n", "       item_id,\n", "       sum(quantity) AS intransit_quantity\n", "FROM ims.ims_sto_mapping_inventory\n", "WHERE sto_type IN (1,2)\n", "  AND inventory_type IN (1,2)\n", "  AND merchant_outlet_id in ({all_outlets})\n", "  AND item_id in ({all_items})\n", "GROUP BY 1,\n", "         2\"\"\"\n", "stos_grouped = read_sql_query(sql=sto_query, con=CON_SQL)\n", "stos_grouped.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Join <PERSON> with assortment\n", "if len(stos_grouped) > 0:\n", "    assortment_sto = pd.merge(\n", "        assortment_backend_qtys,\n", "        stos_grouped,\n", "        left_on=[\"item_id\", \"inventory_holding_frontend_outlet\"],\n", "        right_on=[\"item_id\", \"outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    assortment_sto = assortment_sto.drop([\"outlet_id\"], axis=1)\n", "\n", "    assortment_sto.fillna(0, inplace=True)\n", "\n", "    assortment_sto.head(2)\n", "else:\n", "    assortment_sto = assortment_backend_qtys\n", "    assortment_sto[\"intransit_quantity\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feeder - item - outlet live combination\n", "feeder_live_query = \"\"\"SELECT feeder_id AS backend_outlet,\n", "       outlet_id as frontend_outlet,\n", "       item_id,\n", "       1 as feeder_enabled \n", "FROM ims.feeder_outlet_item_mapping\n", "WHERE enabled = 1\n", "  AND (feeder_id,\n", "       outlet_id) IN\n", "    (SELECT feeder_id,\n", "            outlet_id\n", "     FROM ims.feeder_outlet_mapping\n", "     WHERE active = 1)\"\"\"\n", "feeder_live = read_sql_query(sql=feeder_live_query, con=CON_SQL)\n", "feeder_live.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_feeder_live = pd.merge(\n", "    assortment_sto,\n", "    feeder_live,\n", "    on=[\"backend_outlet\", \"frontend_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# All orders at frontend outlets\n", "orders_frontend_query = f\"\"\"\n", "SELECT i.item_id,\n", "       o.outlet AS inventory_holding_frontend_outlet,\n", "       sum(CASE\n", "               WHEN scheduled_at<='{str(until_t)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t,\n", "       sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t)}' AND '{str(until_t_plus_one)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_one,\n", "       sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_one)}' AND '{str(until_t_plus_two)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_two,\n", "        sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_two)}' AND '{str(until_t_plus_three)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_three,\n", "        sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_three)}' AND '{str(until_t_plus_four)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_four,\n", "        sum(CASE\n", "               WHEN scheduled_at BETWEEN '{str(until_t_plus_four)}' AND '{str(until_t_plus_five)}' THEN i.quantity\n", "               ELSE 0\n", "           END) AS until_t_plus_five\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "WHERE scheduled_at BETWEEN '{str(today)}' AND '{str(until_t_plus_five)}'\n", "  AND o.status_id IN (1,\n", "                      3)\n", "  AND i.billed_at_pos = 0\n", "GROUP BY 1,\n", "         2\n", "\"\"\"\n", "orders_frontend = read_sql_query(sql=orders_frontend_query, con=CON_SQL)\n", "orders_frontend.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_orders = pd.merge(\n", "    assortment_feeder_live,\n", "    orders_frontend,\n", "    on=[\"inventory_holding_frontend_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO\n", "buffer_quantity_query = \"\"\"\n", "SELECT DISTINCT feeder_id as backend_outlet,\n", "                   item_id,\n", "                   buffer_quantity\n", "   FROM ims.feeder_item_buffer \n", "   WHERE active = 1\n", "\"\"\"\n", "buffer_quantity = read_sql_query(sql=buffer_quantity_query, con=CON_SQL)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_buffer = pd.merge(\n", "    assortment_orders, buffer_quantity, on=[\"backend_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "assortment_buffer.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["app_live_query = \"\"\"\n", "WITH item_merchant AS\n", "  ( SELECT DISTINCT mpm.product_id,\n", "                    mpm.merchant_id\n", "   FROM lake_cms.gr_merchant_product_mapping AS mpm\n", "   WHERE mpm.inventory_limit>0\n", "     AND mpm.enabled_flag=1\n", "     AND IS_AVAILABLE = 1),\n", "     merchant_mapping AS\n", "  ( SELECT frontend.id frontend_id,\n", "           frontend.name frontend_name,\n", "           backend.id backend_id,\n", "           backend.name backend_name\n", "   FROM lake_cms.gr_merchant frontend\n", "   JOIN lake_cms.gr_virtual_to_real_merchant_mapping vmm ON frontend.id=vmm.virtual_merchant_id\n", "   JOIN lake_cms.gr_merchant backend ON backend.id=vmm.real_merchant_id\n", "   WHERE frontend.enabled_flag =1\n", "     AND vmm.enabled_flag=1\n", "     AND backend.name NOT ILIKE '%%Smart Bachat Club%%'\n", "     AND backend.name NOT ILIKE '%%Donation%%'\n", "     AND backend.name NOT ILIKE '%%Collection%%'\n", "     AND backend.name NOT ILIKE '%%Collect Return%%'\n", "     AND backend.name NOT ILIKE '%%Dummy%%'\n", "     AND backend.name NOT ILIKE '%%test%%' ),\n", "     final_data AS\n", "  ( SELECT DISTINCT a.*\n", "   FROM item_merchant a\n", "   INNER JOIN merchant_mapping m ON m.backend_id=a.merchant_id),\n", "     item_outlet AS\n", "  ( SELECT DISTINCT CURRENT_DATE AS order_date,\n", "                                    mpm.product_id,\n", "                                    rt.outlet_id\n", "   FROM final_data mpm\n", "   INNER JOIN lake_retail.console_outlet_cms_store rt ON mpm.merchant_id=rt.cms_store\n", "   WHERE rt.Active=1\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     product_offer_mapping AS\n", "  ( SELECT item_id,\n", "           a.product_id,\n", "           outlet_id,\n", "           order_date,\n", "           offer_id\n", "   FROM item_outlet AS a\n", "   LEFT JOIN lake_rpc.item_product_mapping AS b ON a.product_id=b.product_id\n", "   WHERE b.active=1 ),\n", "     item_not_null AS\n", "  ( SELECT item_id,\n", "           order_date,\n", "           outlet_id\n", "   FROM product_offer_mapping\n", "   WHERE item_id IS NOT NULL ),\n", "     item_null AS\n", "  ( SELECT *\n", "   FROM product_offer_mapping\n", "   WHERE item_id IS NULL ),\n", "     offer_details AS\n", "  (SELECT DISTINCT offer_reference_id :: varchar,\n", "                   cast(field_val AS int) AS item_id\n", "   FROM lake_offer_master.offer\n", "   LEFT JOIN lake_offer_master.offer_field o_field ON o_field.offer_id=offer.id\n", "   LEFT JOIN lake_offer_master.offer_type_field otf ON o_field.offer_type_field_id = otf.id\n", "   WHERE otf.field_name LIKE '%%product%%'),\n", "     item_offer_joining AS\n", "  ( SELECT b.item_id,\n", "           product_id,\n", "           outlet_id,\n", "           order_date,\n", "           offer_id\n", "   FROM item_null AS a\n", "   LEFT JOIN offer_details AS b ON a.offer_id=b.offer_reference_id),\n", "     item_facility_final AS\n", "  ( SELECT item_id,\n", "           order_date,\n", "           outlet_id\n", "   FROM item_offer_joining\n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "     union_table AS\n", "  (SELECT a.*\n", "   FROM\n", "     ((SELECT *\n", "      FROM item_not_null) \n", "   UNION\n", "     (SELECT *\n", "      FROM item_facility_final)) a )\n", "SELECT item_id,\n", "outlet_id as inventory_holding_frontend_outlet,\n", "1 as app_live\n", "FROM union_table\n", "WHERE item_id IS NOT NULL\n", "\"\"\"\n", "app_live = read_sql_query(sql=app_live_query, con=CON_REDSHIFT)\n", "app_live.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_applive = pd.merge(\n", "    assortment_buffer,\n", "    app_live,\n", "    on=[\"inventory_holding_frontend_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "assortment_applive.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["landing_price_query = \"\"\"SELECT lp.outlet_id as inventory_holding_frontend_outlet,\n", "lp.item_id,lp.landing_price\n", "FROM consumer.weighted_landing_price lp\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "           max(dt_ist) AS dt_ist\n", "   FROM consumer.weighted_landing_price group by 1,2) lpp ON lp.dt_ist = lpp.dt_ist\n", "AND lp.item_id = lpp.item_id\n", "AND lp.outlet_id = lpp.outlet_id\n", "\"\"\"\n", "landing_price = read_sql_query(sql=landing_price_query, con=CON_REDSHIFT)\n", "landing_price.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["default_landing_price_query = \"\"\"SELECT lp.item_id, avg(landing_price) as weighted_landing_price_avg\n", "FROM consumer.weighted_landing_price lp\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "           max(dt_ist) AS dt_ist\n", "   FROM consumer.weighted_landing_price group by 1,2) lpp ON lp.dt_ist = lpp.dt_ist\n", "AND lp.item_id = lpp.item_id\n", "AND lp.outlet_id = lpp.outlet_id\n", "group by 1\"\"\"\n", "\n", "default_landing_price = read_sql_query(sql=default_landing_price_query, con=CON_REDSHIFT)\n", "default_landing_price.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["landing_price = landing_price.astype({\"inventory_holding_frontend_outlet\": int, \"item_id\": int})\n", "default_landing_price = default_landing_price.astype({\"item_id\": int})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_lp = pd.merge(\n", "    assortment_applive,\n", "    landing_price,\n", "    on=[\"item_id\", \"inventory_holding_frontend_outlet\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_lp_avg = pd.merge(assortment_lp, default_landing_price, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_mapping_query = f\"\"\"\n", "SELECT DISTINCT\n", "        pco.id AS inventory_holding_frontend_outlet,\n", "        bom.external_id AS backend_merchant_id\n", "    FROM  lake_oms_bifrost.oms_merchant AS bom\n", "    INNER JOIN lake_retail.console_outlet_cms_store AS cms\n", "        ON bom.external_id = cms.cms_store \n", "        AND cms.active = 1 \n", "        AND cms.cms_update_active = 1\n", "        AND virtual_merchant_type <> 'superstore_merchant'\n", "    INNER JOIN lake_retail.console_outlet AS pco\n", "        ON cms.outlet_id = pco.id\n", "    INNER JOIN lake_crates.facility AS cf \n", "        ON cf.id = pco.facility_id  \t\t\n", "     \"\"\"\n", "merchant_mapping = read_sql_query(sql=merchant_mapping_query, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_merc = pd.merge(\n", "    assortment_lp_avg,\n", "    merchant_mapping,\n", "    on=[\"inventory_holding_frontend_outlet\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mrp_query = \"\"\"select\n", "item_id,\n", "max(variant_mrp) as variant_mrp\n", "from\n", "lake_rpc.product_product group by 1\"\"\"\n", "mrp = pd.read_sql_query(sql=mrp_query, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_merc = assortment_merc.query(\"inventory_holding_frontend_outlet > 0\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_mrp = pd.merge(assortment_merc, mrp, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pt_query = \"\"\"\n", "SELECT backend_merchant_id,\n", "       sum(CASE\n", "               WHEN user_type = 'SBC'\n", "                    AND order_type = 'heavy' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_sbc_heavy,\n", "       sum(CASE\n", "               WHEN user_type = 'Non-SBC'\n", "                    AND order_type = 'normal' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_non_sbc_normal,\n", "       sum(CASE\n", "               WHEN user_type = 'SBC'\n", "                    AND order_type = 'normal' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_sbc_normal,\n", "       sum(CASE\n", "               WHEN user_type = 'Non-SBC'\n", "                    AND order_type = 'heavy' THEN promise_time_hours\n", "               ELSE 0\n", "           END) AS pt_hours_non_sbc_heavy\n", "FROM earliest_slot_details\n", "GROUP BY 1\n", "\"\"\"\n", "pt = read_sql_query(sql=pt_query, con=CON_PT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_pt = pd.merge(assortment_mrp, pt, on=[\"backend_merchant_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feeder_orders_query = \"\"\"\n", "SELECT backend_outlet,\n", "       frontend_outlet,\n", "       item_id,\n", "       sum(order_allocated) AS feeder_orders\n", "FROM metrics.feeder_excess_orders\n", "WHERE run_id not ILIKE '%%manual%%'\n", "GROUP BY 1,2,3\n", "\n", "\"\"\"\n", "feeder_orders = read_sql_query(sql=feeder_orders_query, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_pt = pd.merge(\n", "    assortment_pt,\n", "    feeder_orders,\n", "    on=[\"backend_outlet\", \"frontend_outlet\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_pt[\"etl_ts_utc\"] = datetime.datetime.now()\n", "assortment_pt = assortment_pt.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]\n", "\n", "\n", "redshift_schema(assortment_pt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"feeder_metrics\",\n", "    \"column_dtypes\": redshift_schema(assortment_pt),\n", "    \"primary_key\": [\"etl_ts_utc\", \"item_id\", \"backend_outlet\", \"frontend_outlet\"],\n", "    \"sortkey\": [\"etl_ts_utc\", \"item_id\", \"backend_outlet\", \"frontend_outlet\"],\n", "    \"incremental_key\": \"etl_ts_utc\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(assortment_pt, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}