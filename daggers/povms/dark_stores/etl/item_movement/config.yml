dag_name: item_movement
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 22
  tag: level1
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 24
  tag: level2
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 32
  tag: level3
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 34
  tag: level4
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 42
  tag: level5
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 43
  tag: level6
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 92
  tag: level7
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 264
  tag: level8
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 268
  tag: level9
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 398
  tag: level10
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 517
  tag: level11
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 513
  tag: level12
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 555
  tag: level13
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 555
  tag: level14
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 1206
  tag: level15
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 15
  tag: level16
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 3
  tag: level17
- executor_config:
    load_type: low
    node_type: spot
  name: notebook
  parameters:
    facility_id: 1
  tag: level18
owner:
  email: <EMAIL>
  slack_id: UMB7GB90T
path: povms/dark_stores/etl/item_movement
paused: true
pool: povms_pool
project_name: dark_stores
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: '*/120 * * * *'
  start_date: '2021-11-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 2
concurrency: 3
