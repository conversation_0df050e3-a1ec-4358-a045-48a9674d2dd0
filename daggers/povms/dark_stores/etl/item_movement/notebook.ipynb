{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import re\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()\n", "current_day = current_date.weekday()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# BACKEND_FACILITY"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BACKEND_FACILITY = int(facility_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT tag.outlet_id,\n", "                  tag.item_id\n", "           \n", "      FROM rpc.item_outlet_tag_mapping tag\n", "      INNER JOIN retail.console_outlet ro ON ro.id=tag.outlet_id\n", "      inner join retail.console_location l on l.id=ro.tax_location_id\n", "      INNER JOIN retail.console_outlet r ON r.id=tag.tag_value\n", "      WHERE tag_type_id=8\n", "      AND r.facility_id= %(facility)s\n", "        AND tag.active=1\n", "        and ro.name not like \"%%old%%\"\n", "        and ro.business_type_id=7\n", "      ORDER BY 1,\n", "               2\"\"\"\n", "\n", "item_tag = pd.read_sql_query(query, retail, params={\"facility\": BACKEND_FACILITY})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTLET = list(set(item_tag[\"outlet_id\"]))\n", "max_shelf_capacity = 50\n", "shelf_threshold = 10"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Locations available "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT distinct wom.outlet_id,\n", "       o.name AS outlet_name,\n", "       wa.aisle_id,\n", "       wr.rack_id,\n", "       ws.shelf_id,\n", "       wl.location_name\n", "FROM warehouse_location.warehouse w\n", "INNER JOIN warehouse_location.warehouse_storage_location wl ON w.id= wl.warehouse_id\n", "INNER JOIN warehouse_location.warehouse_shelf ws ON wl.shelf_id = ws.id\n", "INNER JOIN warehouse_location.warehouse_rack wr ON ws.rack_id = wr.id\n", "INNER JOIN warehouse_location.warehouse_rack_type wrt ON wrt.id = wr.rack_type_id\n", "INNER JOIN warehouse_location.warehouse_aisle wa ON wa.id = wr.aisle_id\n", "INNER JOIN warehouse_location.warehouse_zone wz ON wz.id= wa.zone_id\n", "inner join warehouse_location.warehouse_floor fl ON wz.floor_id= fl.id\n", "INNER JOIN warehouse_location.warehouse_outlet_mapping wom ON wom.warehouse_id=w.id\n", "INNER JOIN retail.console_outlet o ON o.id=wom.outlet_id\n", "WHERE wl.is_active = 1\n", "  AND wom.outlet_id in %(outlet)s\n", "  and wrt.rack_type=\"Rack\"\n", "ORDER BY 1,2,3,4,5,6\"\"\"\n", "\n", "\n", "location_data = pd.read_sql_query(query, retail, params={\"outlet\": tuple(OUTLET)})\n", "location_data = location_data.drop_duplicates()\n", "location_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location_data[\"outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shelf_count = (\n", "    location_data.groupby([\"outlet_id\", \"aisle_id\", \"rack_id\"])[[\"shelf_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "shelf_count[\"rack_threshold\"] = shelf_count[\"shelf_id\"] * shelf_threshold\n", "shelf_count = shelf_count[[\"outlet_id\", \"aisle_id\", \"rack_id\", \"rack_threshold\"]].drop_duplicates()\n", "shelf_count.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shelf_count[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Quantity present at locations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT ro.facility_id ,\n", "       m.outlet_id,\n", "       ws.location_name,\n", "       item_id,\n", "       sum(l.quantity) as quantity\n", "FROM warehouse_location.warehouse_item_location l\n", "LEFT JOIN warehouse_location.warehouse_storage_location ws ON ws.id=l.location_id\n", "AND l.warehouse_id=ws.warehouse_id\n", "INNER JOIN warehouse_location.warehouse_outlet_mapping m ON l.warehouse_id = m.warehouse_id\n", "inner join retail.console_outlet ro on ro.id=m.outlet_id\n", "WHERE m.outlet_id in %(outlet)s\n", "  AND l.active = 1\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4\"\"\"\n", "\n", "quantity_present_location = pd.read_sql_query(query, retail, params={\"outlet\": tuple(OUTLET)})\n", "quantity_present_location.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = location_data.merge(quantity_present_location, on=[\"outlet_id\", \"location_name\"], how=\"left\")\n", "df1 = (\n", "    df.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"outlet_id\",\n", "            \"outlet_name\",\n", "            \"aisle_id\",\n", "            \"rack_id\",\n", "            \"shelf_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\"quantity\"]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "df1[[\"facility_id\", \"outlet_id\", \"item_id\", \"quantity\"]] = df1[\n", "    [\"facility_id\", \"outlet_id\", \"item_id\", \"quantity\"]\n", "].astype(\"int\")\n", "df1.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Reserved locations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = [[926, \"A1\", \"001\", \"01\"], [926, \"A1\", \"004\", \"01\"], [1010, \"A1\", \"001\", \"01\"]]\n", "\n", "df_assigned_locations = pd.DataFrame(data, columns=[\"outlet_id\", \"aisle_id\", \"rack_id\", \"shelf_id\"])\n", "df_assigned_locations[\"is_reserved\"] = 1\n", "df_assigned_locations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assigned_locations_items = df_assigned_locations.merge(\n", "    df1, on=[\"outlet_id\", \"aisle_id\", \"rack_id\", \"shelf_id\"], how=\"inner\"\n", ")\n", "assigned_locations_items[[\"facility_id\", \"item_id\"]] = assigned_locations_items[\n", "    [\"facility_id\", \"item_id\"]\n", "].astype(\"int\")\n", "assigned_locations_items_df = (\n", "    assigned_locations_items.groupby([\"facility_id\", \"outlet_id\", \"item_id\"])[[\"quantity\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "assigned_locations_items_df = assigned_locations_items_df[\n", "    assigned_locations_items_df[\"quantity\"] > 0\n", "]\n", "assigned_locations_items_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Removing FnV items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select item_id from rpc.product_product\n", "where outlet_type!=1\"\"\"\n", "\n", "grocery_items = pd.read_sql_query(query, retail)\n", "grocery_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assigned_locations_items_df1 = assigned_locations_items_df.merge(\n", "    grocery_items, on=[\"item_id\"], how=\"inner\"\n", ")\n", "item_to_move = (\n", "    assigned_locations_items_df1[[\"facility_id\", \"outlet_id\", \"item_id\", \"quantity\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"quantity\": \"qty_move\"})\n", ")\n", "item_to_move = item_to_move.astype(\"int\")\n", "item_to_move"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Finding empty shelves"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["empty_shelves = (\n", "    df1.groupby([\"outlet_id\", \"outlet_name\", \"aisle_id\", \"rack_id\", \"shelf_id\"])[[\"quantity\"]]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "empty_shelves_df1 = empty_shelves.merge(\n", "    df_assigned_locations,\n", "    on=[\"outlet_id\", \"aisle_id\", \"rack_id\", \"shelf_id\"],\n", "    how=\"left\",\n", ")\n", "empty_shelves_df2 = empty_shelves_df1[empty_shelves_df1[\"is_reserved\"] != 1]\n", "empty_shelves_df2.drop([\"is_reserved\"], axis=1, inplace=True)\n", "empty_shelves_df2 = empty_shelves_df2[empty_shelves_df2[\"quantity\"] < max_shelf_capacity]\n", "empty_shelves_df2[\"capacity_total\"] = max_shelf_capacity - empty_shelves_df2[\"quantity\"]\n", "empty_shelves_df2.sort_values(by=[\"outlet_id\", \"capacity_total\"], ascending=False, inplace=True)\n", "empty_shelves_df2 = empty_shelves_df2.reset_index(drop=True)\n", "empty_shelves_df2[\"capacity_left\"] = empty_shelves_df2[\"capacity_total\"]\n", "empty_shelves_df2[\"capacity_used\"] = 0\n", "empty_shelves_df2[\"item_assigned\"] = \"\"\n", "empty_shelves_df2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_lst = set(item_to_move[\"outlet_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["empty_shelves_df2[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item assignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = pd.DataFrame()\n", "\n", "\n", "for outlet in list(outlets_lst):\n", "    df = item_to_move[item_to_move[\"outlet_id\"] == outlet]\n", "    df = df.reset_index(drop=True)\n", "    shelf_temp = empty_shelves_df2[empty_shelves_df2[\"outlet_id\"] == outlet]\n", "\n", "    for item in list(df[\"item_id\"].unique()):\n", "\n", "        df1 = df[df[\"item_id\"] == item]\n", "        df1 = df1.reset_index(drop=True)\n", "        space_required = df1.iloc[0, 3]\n", "\n", "        shelf_temp = shelf_temp[shelf_temp[\"capacity_left\"] > 0]\n", "\n", "        for i in range(len(empty_shelves_df2)):\n", "\n", "            if space_required > 0:\n", "\n", "                if space_required >= shelf_temp.iloc[i, 7]:\n", "\n", "                    space_required = space_required - shelf_temp.iloc[i, 7]\n", "                    shelf_temp.iloc[i, 8] = shelf_temp.iloc[i, 7]\n", "                    shelf_temp.iloc[i, 7] = 0\n", "                    shelf_temp.iloc[i, 9] = item\n", "\n", "                else:\n", "\n", "                    shelf_temp.iloc[i, 8] = space_required\n", "                    shelf_temp.iloc[i, 7] = shelf_temp.iloc[i, 7] - space_required\n", "                    space_required = 0\n", "                    shelf_temp.iloc[i, 9] = item\n", "\n", "        temp = shelf_temp[shelf_temp[\"item_assigned\"] == item]\n", "\n", "        final_df = pd.concat([final_df, temp])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_location = final_df[\n", "    [\"outlet_id\", \"outlet_name\", \"item_assigned\", \"aisle_id\", \"rack_id\", \"shelf_id\"]\n", "].drop_duplicates()\n", "item_location.rename(columns={\"item_assigned\": \"item_id\"}, inplace=True)\n", "item_location[\"to_location\"] = (\n", "    item_location[\"aisle_id\"] + \"-\" + item_location[\"rack_id\"] + \"-\" + item_location[\"shelf_id\"]\n", ")\n", "item_location = item_location[\n", "    [\"outlet_id\", \"outlet_name\", \"item_id\", \"to_location\"]\n", "].drop_duplicates()\n", "item_location.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Final output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_location = assigned_locations_items[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"aisle_id\",\n", "        \"rack_id\",\n", "        \"shelf_id\",\n", "        \"quantity\",\n", "    ]\n", "].drop_duplicates()\n", "old_location = old_location[old_location[\"quantity\"] > 0]\n", "old_location[\"from_location\"] = (\n", "    old_location[\"aisle_id\"] + \"-\" + old_location[\"rack_id\"] + \"-\" + old_location[\"shelf_id\"]\n", ")\n", "old_location = old_location[\n", "    [\"outlet_id\", \"outlet_name\", \"item_id\", \"from_location\", \"quantity\"]\n", "].drop_duplicates()\n", "old_location.rename(columns={\"quantity\": \"qty_to_move\"}, inplace=True)\n", "old_location"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select item_id,max(name) as item_name from rpc.product_product\n", "group by 1\"\"\"\n", "item_name = pd.read_sql_query(query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = old_location.merge(item_location, on=[\"outlet_id\", \"outlet_name\", \"item_id\"], how=\"inner\")\n", "final = final[\n", "    [\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"from_location\",\n", "        \"to_location\",\n", "        \"qty_to_move\",\n", "    ]\n", "].drop_duplicates()\n", "final[\"updated_at\"] = now\n", "final[\"updated_by\"] = 14\n", "final"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sheet_id=\"1UOm3api8OOxc7cUElJOJ3PYusLV-tpDc6xJsT94TpLU\"\n", "# sheet_name=\"backend-\"+str(BACKEND_FACILITY)\n", "# pb.to_sheets(final2,sheet_id,sheet_name,clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Flat table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# changes for min_max qty\n", "MIN_MAX_SCHEMA_NAME = \"consumer\"\n", "MIN_MAX_TABLE_NAME = \"outlet_item_location_movement\"\n", "\n", "\n", "def save_min_max_qty_to_redshift(df):\n", "    \"\"\"\n", "    Upload a dataframe to facility_item_min_max_quantity table in redshift\n", "    \"\"\"\n", "    kwargs = {\n", "        \"schema_name\": MIN_MAX_SCHEMA_NAME,\n", "        \"table_name\": MIN_MAX_TABLE_NAME,\n", "        \"column_dtypes\": [\n", "            {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"OUTLET ID\"},\n", "            {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"OUTLET NAME\"},\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item id\"},\n", "            {\n", "                \"name\": \"from_location\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"location from where item to be removed\",\n", "            },\n", "            {\n", "                \"name\": \"to_location\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"location from where item to be moved\",\n", "            },\n", "            {\n", "                \"name\": \"qty_to_move\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"qty of item to be moved\",\n", "            },\n", "            {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"Updated at ts\"},\n", "            {\"name\": \"updated_by\", \"type\": \"integer\", \"description\": \"Updated by\"},\n", "        ],\n", "        \"primary_key\": [\"outlet_id\", \"item_id\"],\n", "        \"sortkey\": [\"outlet_id\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"append\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"This table contains the min and max quantify for transfers against each facility-item.\",\n", "    }\n", "\n", "    pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "start = time.time()\n", "save_min_max_qty_to_redshift(final)\n", "print(\"Took {}\".format(time.time() - start))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}