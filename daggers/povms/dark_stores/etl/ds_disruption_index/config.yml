alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ds_disruption_index
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/dark_stores/etl/ds_disruption_index
paused: false
pool: povms_pool
project_name: dark_stores
schedule:
  end_date: '2025-09-06T00:00:00'
  interval: 35 0,4,8,12,16 * * *
  start_date: '2025-06-09T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
