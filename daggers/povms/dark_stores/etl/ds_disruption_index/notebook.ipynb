{"cells": [{"cell_type": "code", "execution_count": null, "id": "157d7247-3184-46bd-8d28-c4a5087f4c35", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "from tqdm.notebook import tqdm\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "f0e2665e-9783-4556-ab47-ab90446120fa", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "227c44b5-171f-4e49-b3fd-701f20ee9378", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            send_slack_alert(\n", "                f\"`supply_etls.ds_disruption_index` Upload failed due to some error \\n{e}\"\n", "            )\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "d1f1ae99-0d25-487f-af18-10b95f1ec8c8", "metadata": {}, "outputs": [], "source": ["def send_slack_alert(error_message):\n", "\n", "    slack_channel = pb.from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "    slack_channel = (\n", "        slack_channel[slack_channel[\"alert\"] == \"alerting_anuvrat\"].reset_index().iloc[:, 2][0]\n", "    )\n", "\n", "    error_message = error_message + \"\\ncc <@U03RJ5FRXFC>\"\n", "\n", "    pb.send_slack_message(channel=slack_channel, text=error_message)"]}, {"cell_type": "code", "execution_count": null, "id": "706e61c7-7155-4502-87fc-39575b5c7db0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fe5bda2c-b451-4c0f-89c4-a5933311e24f", "metadata": {}, "outputs": [], "source": ["ds_disruption = read_sql_query(\n", "    f\"\"\"\n", "                    with be_fo as (SELECT pfom.facility_id AS fc_id,\n", "                              pf.internal_facility_identifier AS fc_name,\n", "                              pfom.outlet_id AS tag_value,\n", "                              wom.cloud_store_id AS outlet_id\n", "                      FROM lake_po.physical_facility_outlet_mapping pfom\n", "                      INNER JOIN lake_retail.warehouse_outlet_mapping wom ON wom.warehouse_id = pfom.outlet_id\n", "                      INNER JOIN lake_po.physical_facility pf ON pf.facility_id = pfom.facility_id\n", "                      WHERE pfom.active = 1 and pfom.ars_active = 1\n", "                      and pf.facility_id in (1,43,92,264,555,603,1206,1320,1546,1574,1575,1872,1873,1876,1964,1983,2006,2010,2015,2076,2078,2082,2123,2141)\n", "                    ),\n", "\n", "                    fo as (\n", "                        select x.facility_id, x.outlet_id, co.name as outlet_name, cl.name as city,\n", "                                REGEXP_REPLACE(lower(co.name), 'ss|upncr|noida|ghaziabad|bengaluru|super store|mumbai|kolkata|delhi|gurgaon|hyderabad|chennai|japur|bhopal|indore|ahmedabad|pune|lucknow|kanpur|upncr|ghaziabad|noida|faridabad|bengaluru|', '') as ds_name\n", "                        from po.physical_facility_outlet_mapping x\n", "                        inner join retail.console_outlet co on co.id = x.outlet_id and co.active = 1\n", "                        inner join retail.console_location cl on cl.id = co.tax_location_id\n", "                        where x.lake_active_record and co.lake_active_record and cl.lake_active_record\n", "                        and x.ars_active = 1 and x.active = 1 \n", "                        group by 1,2,3,4\n", "                    ),\n", "\n", "                    id as (\n", "                        select * from  supply_etls.item_details id\n", "                        where assortment_type in ('Packaged Goods') and handling_type_id not in (8)\n", "                    ),\n", "\n", "                    sd as (\n", "                        select * from ims.ims_sto_details\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    isi as (\n", "                        select * from ims.ims_sto_item\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    psi as (\n", "                        select * from po.sto_items\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    ps as (\n", "                        select * from po.sto\n", "                        where lake_active_record and created_at >= cast(date(current_date) - interval '15' day as date) and created_at <= cast(date(current_date) + interval '1' day as date)\n", "                    ),\n", "\n", "                    au as (\n", "                        select id as user_id, email as user_mail\n", "                        from retail.auth_user\n", "                        where lake_active_record\n", "                    ),\n", "\n", "                    core_sto as (\n", "                        select isi.created_at + interval '330' minute as sto_creation_time,\n", "                                sd.delivery_date + interval '330' minute as sto_expiry_time,\n", "                                isi.sto_id, \n", "                                case when sd.sto_state=1 then 'CREATED' when sd.sto_state=2 then 'BILLED' when sd.sto_state=3 then 'EXPIRED' when sd.sto_state=4 then 'INWARD' when sd.sto_state=5 then 'FORCE_BILLED' end as sto_state,\n", "                                sd.outlet_id as sender_outlet_id, fc_name,\n", "                                sd.merchant_outlet_id as receiver_outlet_id, ds_name,\n", "                                isi.item_id, id.assortment_type,\n", "                                cast(JSON_EXTRACT(JSON_EXTRACT(ps.meta,'$.destination_entity_vendor'),'$.name') as varchar) as destnation_entity,\n", "                                psi.created_by, au.user_mail,\n", "                                sum(isi.reserved_quantity) as sto_quantity,\n", "                                sum(isi.billed_quantity) as billed_quanity,\n", "                                sum(isi.inward_quantity) as inward\n", "                        from sd\n", "                        left join isi on isi.sto_id = sd.sto_id\n", "                        left join psi on psi.sto_id = isi.sto_id and psi.item_id = isi.item_id\n", "                        left join ps on ps.id = isi.sto_id\n", "                        left join fo on fo.outlet_id = sd.merchant_outlet_id\n", "                        inner join id on id.item_id = isi.item_id\n", "                        inner join au on au.user_id = psi.created_by\n", "                        inner join be_fo on be_fo.outlet_id = sd.outlet_id\n", "                        group by 1,2,3,4,5,6,7,8,9,10,11,12,13\n", "                    ),\n", "\n", "\n", "                    pi as (\n", "                        SELECT *\n", "                       FROM pos.pos_invoice\n", "                       WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                         AND invoice_type_id IN (5,14,16)\n", "                         AND grofers_order_id != ''\n", "                         AND grofers_order_id IS NOT NULL\n", "                    ),\n", "\n", "                    pid as (\n", "                        SELECT *\n", "                        FROM pos.pos_invoice_product_details\n", "                        WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                    ),\n", "\n", "                    pp as (\n", "                        select variant_id, item_id from rpc.product_product where lake_active_record and active = 1\n", "                    ),\n", "\n", "\n", "                    iii as (\n", "                        SELECT *\n", "                        FROM ims.ims_inward_invoice\n", "                        WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                        AND source_type = 2\n", "                    ),\n", "\n", "\n", "                    isd as (\n", "                        SELECT *\n", "                        FROM ims.ims_inventory_stock_details\n", "                        WHERE lake_active_record and insert_ds_ist >= cast(date(current_date) - interval '15' day as varchar) and insert_ds_ist <= cast(date(current_date) + interval '5' day as varchar)\n", "                    ),\n", "\n", "                    grn_invoice as (\n", "                        select iii.grn_id, vendor_invoice_id, item_id, max(isd.created_at) as isd_created_at,\n", "                                sum(coalesce(isd.delta,0)) AS grn1, sum(coalesce(isd.delta,0) * isd.landing_price) AS value1\n", "                        from iii\n", "                        inner join isd on iii.grn_id = isd.grn_id\n", "                        inner join pp on pp.variant_id = isd.variant_id\n", "                        group by 1,2,3\n", "                    ),\n", "\n", "                    gi_core as (\n", "                        select vendor_invoice_id as invoice_id, item_id, max(isd_created_at + interval '330' minute) as grn_started_at,\n", "                                sum(grn1) AS inwarded_quantity, sum(value1) AS inward_value\n", "                        from grn_invoice\n", "                        group by 1,2\n", "                    ),\n", "\n", "                    core_grn as (\n", "                        select cast(pi.grofers_order_id as int) as sto_id, pi.invoice_id,\n", "                                pi.pos_timestamp + interval '330' minute as sto_invoice_created_at,\n", "                                case when cast(transfer_state as integer) = 1 then 'Raised'\n", "                                    when cast(transfer_state as integer) = 2 then 'GRN Complete'\n", "                                    when cast(transfer_state as integer) = 3 then 'GRN Partial'\n", "                                    when cast(transfer_state as integer) = 4 then 'GRN Force Complete'\n", "                                    when cast(transfer_state as integer) = 5 then 'DISCREPANCY_NOTE_GENERATED'\n", "                                    else 'NA'\n", "                                end as invoice_state,\n", "                                pp.item_id, \n", "\n", "                                sum(coalesce(pid.quantity,0)) AS billed_quantity,\n", "                                sum(pid.selling_price*coalesce(pid.quantity,0)) AS invoice_value,\n", "                                grn_started_at,\n", "                                inwarded_quantity,\n", "                                inward_value\n", "\n", "                        from pi\n", "                        inner join pid on pid.invoice_id = pi.id\n", "                        inner join pp on pp.variant_id = pid.variant_id\n", "                        left join gi_core gc on gc.invoice_id = pi.invoice_id AND gc.item_id = pp.item_id\n", "                        inner join id on id.item_id = pp.item_id\n", "                        group by 1,2,3,4,5,8,9,10\n", "                    ),\n", "\n", "                    disruption as (\n", "                        select \n", "                                cs.sender_outlet_id, fc_name, cs.receiver_outlet_id, ds_name,\n", "                                sum(cs.sto_quantity) as sto_quantity, \n", "                                sum(cs.billed_quanity) as billed_quanity, sum(cg.billed_quantity) as core_billed_qty, \n", "                                sum(cs.inward) as inwarded_quantity, sum(cg.inwarded_quantity) as core_inwarded_quantity,\n", "                                sum(cg.billed_quantity) - sum(cg.inwarded_quantity) as delta\n", "\n", "                        from core_sto cs\n", "                        left join core_grn cg on cg.sto_id = cs.sto_id and cg.item_id = cs.item_id\n", "                        where cg.sto_invoice_created_at <= current_timestamp - interval '36' hour\n", "                        and cg.sto_invoice_created_at >= current_timestamp - interval '90' hour\n", "                        group by 1,2,3,4\n", "                    ),\n", "\n", "                    avg_trend as (\n", "                        select date(sto_creation_time) as sto_created_date, cs.sender_outlet_id, cs.receiver_outlet_id, sum(sto_quantity) as trend_sto\n", "                        from core_sto cs\n", "                        where cs.sto_creation_time <= current_timestamp - interval '48' hour\n", "                        group by 1,2,3\n", "                    ),\n", "\n", "                    trend as (\n", "                        select sender_outlet_id, receiver_outlet_id, avg(trend_sto) as avg_sto\n", "                        from avg_trend\n", "                        group by 1,2\n", "                    )\n", "\n", "\n", "                    select at.sender_outlet_id, at.receiver_outlet_id,  \n", "                            avg_sto, delta, core_billed_qty as billed_qty, core_inwarded_quantity as inwarded_quantity,\n", "                            (case when delta > avg_sto then 100.000 else delta * 100.000 / avg_sto end) as disruption_perc\n", "                    from trend at\n", "                    left join disruption d on d.sender_outlet_id = at.sender_outlet_id and d.receiver_outlet_id = at.receiver_outlet_id\n", "                    where d.delta is not null\n", "                \"\"\",\n", "    CON_TRINO,\n", ")\n", "\n", "\n", "ds_disruption"]}, {"cell_type": "code", "execution_count": null, "id": "ce80b99f-d0fd-4552-ba74-6c5e45301866", "metadata": {}, "outputs": [], "source": ["# ds_disruption.isna().sum()\n", "ds_disruption.head()\n", "updated_at_ist = pd.to_datetime(pd.to_datetime(datetime.now() + timedelta(hours=5.5)))\n", "# .strftime(\"%Y-%m-%d %H\"))\n", "date_ist = (datetime.now() + timedelta(hours=5.5)).date()\n", "ds_disruption[\"date_ist\"] = date_ist\n", "ds_disruption[\"updated_at_ist\"] = updated_at_ist\n", "ds_disruption.head()"]}, {"cell_type": "code", "execution_count": null, "id": "78c078e7-8d30-4f8d-8c7d-1f7b5990838b", "metadata": {}, "outputs": [], "source": ["ds_disruption.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "c4fc5947-3bdf-4171-8efa-3a7ef3d425e4", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"ds_disruption_index\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"sender_outlet_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"sender_outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"receiver_outlet_id\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"receiver_outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"avg_sto\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"avg_sto\",\n", "        },\n", "        {\n", "            \"name\": \"delta\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"delta\",\n", "        },\n", "        {\"name\": \"billed_qty\", \"type\": \"DOUBLE\", \"description\": \"billed_qty\"},\n", "        {\n", "            \"name\": \"inwarded_quantity\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"inwarded_quantity\",\n", "        },\n", "        {\n", "            \"name\": \"disruption_perc\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"disruption_perc\",\n", "        },\n", "        {\n", "            \"name\": \"date_ist\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"date_ist\",\n", "        },\n", "        {\n", "            \"name\": \"updated_at_ist\",\n", "            \"type\": \"TIMESTAMP(6)\",\n", "            \"description\": \"updated_at_ist\",\n", "        },\n", "    ],\n", "    \"primary_key\": [\n", "        \"updated_at_ist\",\n", "        \"sender_outlet_id\",\n", "        \"receiver_outlet_id\",\n", "    ],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains store disruption index\",\n", "}\n", "\n", "to_trino(ds_disruption, **kwargs)\n", "print(\"upload\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a7aa6b4-a947-4d75-a97c-ce1c7c383a84", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}