select distinct wom.cloud_store_id as outlet_id, dwc.date as date,dwc.item_id, dwc.consumption as cpd 
    from 
    (select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where 
    date between current_date - interval 1 week 
    and current_date + interval 2 week) dwc
    join 
    retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id
    where wom.cloud_store_id in ({outlets})
     and dwc.item_id in ({items})