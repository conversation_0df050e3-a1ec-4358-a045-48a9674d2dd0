SELECT i.item_id,
         r.id AS outlet_id,
            date(convert_tz(scheduled_at,"+00:00","+05:30")) AS delivery_date,
           -- count(DISTINCT CASE WHEN o.status_id NOT IN (3,5) THEN o.order_id END) AS total_orders,
           count(DISTINCT o.order_id) AS total_orders,
           count(DISTINCT CASE WHEN o.status_id IN (2,6) THEN o.order_id END) AS billed_orders,
           sum(i.quantity) as quantity
    FROM ims.ims_order_details o
    INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id
    INNER JOIN retail.console_outlet r ON o.outlet = r.id
    where r.id in ({dark_stores})
    GROUP BY 1,
             2,3
    ORDER BY 1,
             2,3 