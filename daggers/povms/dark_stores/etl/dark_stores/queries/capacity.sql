select distinct backend_merchant_id,
backend_merchant_name,
delivery_date,
sum(given_capacity) as planned_capacity,
sum(capacity_utilised) as actual_capacity
from
(SELECT 
	warehouse_external_id AS backend_merchant_id,
	warehouse_name AS backend_merchant_name,
	
	DATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,
	TO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || 
	TO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,
	slot_type,
	
	warehouse_asked AS asked_capcity,
	warehouse_planned AS given_capacity,
	warehouse_actual AS capacity_utilised,
        warehouse_available AS capacity_under_utilised,

        min(update_ts) AS update_ts
	
FROM sco_path_capacity
WHERE date(slot_start) >= date(now())
and warehouse_name not like '%%Cold%%'
and warehouse_planned < 9999
GROUP BY 1,2,3,4,5,6,7,8,9
ORDER BY 2,3,4,5) a
where
delivery_date between date(current_date) and date(current_date + interval '15 days')
and given_capacity > 0
group by 1,2,3
order by 2,3;