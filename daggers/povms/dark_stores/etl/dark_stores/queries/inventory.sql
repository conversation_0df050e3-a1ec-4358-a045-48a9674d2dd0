SELECT a.outlet_id, 
       a.item_id, 
       a.quantity, 
       b.quantity AS blocked_quantity 
FROM   (SELECT outlet_id, 
               item_id, 
               quantity 
        FROM   ims.ims_item_inventory 
        WHERE  outlet_id IN ( {outlets} ) 
               AND item_id IN ( {items} ) 
               AND active = 1) a 
       LEFT JOIN (SELECT item_id, 
                         outlet_id, 
                         sum(quantity) AS quantity
                  FROM   ims.ims_item_blocked_inventory 
                  WHERE  outlet_id IN ( {outlets} ) 
                         AND item_id IN ( {items} ) 
                         AND active = 1 
                         AND blocked_type in (1)  -- blocked for sto and orders
                 GROUP BY 1,2) b 
              ON a.item_id = b.item_id 
                 AND a.outlet_id = b.outlet_id 