{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime\n", "import math\n", "import json\n", "import uuid\n", "import logging\n", "from copy import deepcopy\n", "import warnings\n", "import numpy as np\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "\n", "pd.set_option(\"display.max_colwidth\", 40)\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pip\n", "\n", "\n", "def import_or_install(package):\n", "    try:\n", "        __import__(package)\n", "    except ImportError:\n", "        pip.main([\"install\", package])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import_or_install(\"openpyxl\")"]}, {"cell_type": "raw", "metadata": {}, "source": ["outer_case_size_limit = 50\n", "min_weight_per = 0.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_schema = {\n", "    \"item_id\": \"int64\",\n", "    \"facility_id\": \"int64\",\n", "    \"date\": \"datetime64[ns]\",\n", "    \"system_cpd\": \"float\",\n", "}\n", "\n", "doi_matrix_schema = {\"dark_store\": \"float\", \"item_id\": \"float\", \"doi\": \"float\"}\n", "\n", "doi_reserve_schema = {\n", "    \"front_end\": \"float\",\n", "    \"doi_to_reserve\": \"float\",\n", "    \"item_id\": \"float\",\n", "}\n", "\n", "mapping_schema = {\n", "    \"front_end\": int,\n", "    \"dark_store\": int,\n", "    \"picking_capacity_morning\": int,\n", "    \"picking_capacity_evening\": int,\n", "    \"heavy_storage\": int,\n", "    \"regular_storage\": int,\n", "    \"priority\": int,\n", "    \"truck_load_size_morning\": int,\n", "    \"truck_load_size_evening\": int,\n", "    \"doi_to_reserve\": int,\n", "    \"doi\": int,\n", "    \"moq\": int,\n", "    \"turn_around_time\": int,\n", "    \"distinct_skus\": int,\n", "    \"is_darkstore\": int,\n", "}\n", "\n", "truck_cap_schema = {\"truck_capacity\": int}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cms_outlet_details_query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store\n", "from\n", "lake_oms_bifrost.oms_merchant m\n", "join consumer.rt_console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where\n", "cms.active = 1 and cms.cms_update_active = 1\n", "\"\"\"\n", "\n", "capacity_query = \"\"\"\n", "select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "\twarehouse_external_id AS backend_merchant_id,\n", "\twarehouse_name AS backend_merchant_name,\n", "\t\n", "\tDATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "\tTO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "\tTO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "\tslot_type,\n", "\t\n", "\twarehouse_asked AS asked_capcity,\n", "\twarehouse_planned AS given_capacity,\n", "\twarehouse_actual AS capacity_utilised,\n", "        warehouse_available AS capacity_under_utilised,\n", "\n", "        min(update_ts) AS update_ts\n", "\t\n", "FROM lake_supply_orchestrator.sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between date(current_date) and date(current_date + interval '15 days')\n", "and given_capacity > 0\n", "group by 1,2,3\n", "order by 2,3;\n", "\"\"\""]}, {"cell_type": "raw", "metadata": {}, "source": ["assortment = \"\"\"\n", "SELECT m.ds_outlet as dark_store,\n", "m.cater_outlet as front_end,\n", "m.run_id, m.item_id, \n", "rank,\n", "case when m.net_available_qty > 0 then 1 else 0 end as transferable_flag,\n", "m.assortment_flag,\n", "m.final_replenish_flag,\n", "m.gmv_flag,\n", "m.ds_available_flag,\n", "difference\n", "FROM metrics.dark_store_assortment_details m\n", "INNER JOIN\n", "  (SELECT max(run_id) AS run_id\n", "   FROM metrics.dark_store_assortment_details) a ON m.run_id=a.run_id\n", "where type_flag = 'Grocery'\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": [",\n", "\n", "po_cycle AS\n", "  (SELECT ds_outlet,\n", "          po.item_id,\n", "          CASE\n", "              WHEN ds_outlet = 971\n", "                   AND po.item_id NOT IN\n", "                     (SELECT item_id\n", "                      FROM metrics.dark_stores_stock_out\n", "                      WHERE dominant_reason IN ('stock_unavailable_at_catering_facility',\n", "                                                'stock_available_but_not_transferable')\n", "                        AND dark_store_outlet = 971\n", "                        AND created_at_ist =\n", "                          (SELECT max(created_at_ist)\n", "                           FROM metrics.dark_stores_stock_out)) THEN dow\n", "              ELSE '0,1,2,3,4,5,6'\n", "          END AS dow1,\n", "          CASE\n", "              WHEN ds_outlet = 971 THEN case_sensitive\n", "              ELSE 0\n", "          END AS case_sensitive\n", "   FROM metrics.dark_store_item_po_cycle po\n", "   INNER JOIN\n", "     (SELECT DISTINCT item_id\n", "      FROM consumer.pos_product_product\n", "      WHERE outlet_type=2) p ON p.item_id=po.item_id\n", "   WHERE dow1 LIKE '%%' || cast(date_part(dow, CURRENT_DATE) AS char) || '%%')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = \"\"\"WITH assortment AS\n", "  (SELECT catering_outlet as front_end_outlet,\n", "          -- catering_facility AS front_end_fac,\n", "          ds_outlet AS dark_store,\n", "          item_id,\n", "          name,\n", "          product_type,\n", "          updated_at AS run_id,\n", "          search_flag,\n", "          notify_me_flag,\n", "          rank AS pre_rank,\n", "          RANK() OVER(PARTITION BY ds_outlet\n", "                      ORDER BY search_flag DESC, notify_me_flag DESC, rank) AS rank\n", "   FROM metrics.dark_store_assortment_static\n", "   WHERE replenishment_flag = 1\n", "     AND updated_at =\n", "       (SELECT max(updated_at)\n", "        FROM metrics.dark_store_assortment_static))\n", "\n", "SELECT a.*,\n", "0 as case_sensitive\n", "FROM assortment a\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fnv_query = \"\"\"\n", "SELECT DISTINCT r.item_id,\n", "1 as FnV\n", "FROM lake_rpc.product_product r\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          max(created_at) AS dated\n", "   FROM lake_rpc.product_product\n", "   GROUP BY 1) a ON a.item_id=r.item_id\n", "WHERE r.created_at=a.dated\n", "  AND r.outlet_type=1\n", "  AND r.item_id in ({items})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shelf_life = \"\"\"\n", "WITH item_det AS\n", "  (SELECT *,\n", "          CASE\n", "              WHEN storage_type IN (1,\n", "                                    8) THEN 'Regular'\n", "              WHEN storage_type IN (2,\n", "                                    6) THEN 'Cold'\n", "              WHEN storage_type IN (4,\n", "                                    5) THEN 'Heavy'\n", "              WHEN storage_type IN (3,\n", "                                    7) THEN 'Frozen'\n", "          END AS storage_type_name\n", "   FROM lake_rpc.product_product),\n", "     item_list AS\n", "  (SELECT name,\n", "          upc,\n", "          brand,\n", "          variant_mrp,\n", "          variant_uom_value,\n", "          variant_Description,\n", "          variant_id,\n", "          outlet_type,\n", "          item_id,\n", "          variant_uom_text,\n", "          perishable,\n", "          shelf_life,\n", "          handling_type,\n", "          skip_expiry,\n", "          storage_type,\n", "          storage_type_name\n", "   FROM item_det -- where storage_type in (2,3,6,7)\n", "),\n", "inventory_snap AS\n", "  (SELECT ris.id,\n", "          ris.variant_id,\n", "          ris.upc_id,\n", "          ris.quantity,\n", "          ris.outlet_id,\n", "          ris.net_worth,\n", "          date(convert_timezone('Asia/Kolkata',ris.snapshot_datetime)) closing_Date,\n", "          date(convert_timezone('Asia/Kolkata',ris.snapshot_datetime)+1) opening_Date,\n", "          prod.item_id AS item_id,\n", "          prod.upc,\n", "          prod.name AS item_name,\n", "          prod.weight_in_gm,\n", "          prod.variant_uom_text,\n", "          CASE\n", "              WHEN prod.outlet_type = 1 THEN 'F&V type'\n", "              WHEN prod.outlet_type = 2 THEN 'Grocery type'\n", "              ELSE 'Not Assigned'\n", "          END AS Item_type,\n", "          prod.storage_type AS storage_type_id,\n", "          CASE\n", "              WHEN storage_type IN (1,\n", "                                    8) THEN 'Regular'\n", "              WHEN storage_type IN (2,\n", "                                    6) THEN 'Cold'\n", "              WHEN storage_type IN (4,\n", "                                    5) THEN 'Heavy'\n", "              WHEN storage_type IN (3,\n", "                                    7) THEN 'Frozen'\n", "          END AS storage_type_name,\n", "          rco.id AS outlet_id1,\n", "          rco.name AS outlet_name,\n", "          fac.id AS facility_id,\n", "          fac.name AS facility_name,\n", "          rcl.name AS city_name\n", "   FROM lake_reports.reports_inventory_snapshot ris\n", "   INNER JOIN lake_retail.console_outlet rco ON rco.id = ris.outlet_id\n", "   INNER JOIN lake_rpc.product_product prod ON prod.variant_id::varchar= ris.variant_id::varchar\n", "   INNER JOIN consumer.retail_console_location rcl ON rco.tax_location_id=rcl.id\n", "   LEFT JOIN lake_crates.facility fac ON fac.id=rco.facility_id\n", "   WHERE date(convert_timezone('Asia/Kolkata',ris.snapshot_datetime)) =CURRENT_DATE-1 -- and  rcl.name like '%Bengaluru%'\n", "AND ris.quantity>0\n", "     AND outlet_name NOT LIKE ('%Liquidation%')\n", "     AND outlet_name NOT LIKE ('%Infra%')\n", "     AND outlet_name NOT LIKE ('%DS%')-- and prod.storage_type in (2,3,6,7)\n", "),\n", "                        inventory_cold AS\n", "  (SELECT invs.*,\n", "          il.perishable,\n", "          il.shelf_life,\n", "          il.handling_type,\n", "          il.skip_expiry,\n", "          il.storage_type\n", "   FROM inventory_snap invs\n", "   LEFT JOIN item_list il ON invs.variant_id=il.variant_id),\n", "                        expiry_data AS\n", "  (SELECT outlet_id,\n", "          variant_id,\n", "          upc_id,\n", "          max(manufacture_date) manufactured_at -- ,expiry_date\n", "FROM lake_ims.ims_inventory_stock_details\n", "   WHERE manufacture_date IS NOT NULL\n", "     AND date(manufacture_date)>CURRENT_DATE-365\n", "     AND date(manufacture_date)<CURRENT_DATE-3\n", "   GROUP BY 1,\n", "            2,\n", "            3\n", "),\n", "                        PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id ),\n", "   category_data AS (\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL),\n", "                        FINAL AS\n", "  (SELECT ic.*,\n", "          date(ed.manufactured_at) manufactured,\n", "          date(ed.manufactured_at)+ic.shelf_life AS expiry_date,\n", "          CURRENT_DATE-date(ed.manufactured_at) AS days_gone,\n", "                       ic.shelf_life-(CURRENT_DATE-date(ed.manufactured_at)) AS days_left,\n", "                       cd.product_id,\n", "                       cd.manf manufacturer,\n", "                       cd.product_type,\n", "                       cd.l0,\n", "                       cd.l1,\n", "                       cd.l2\n", "   FROM inventory_cold ic\n", "   LEFT JOIN expiry_data ed ON ic.variant_id=ed.variant_id\n", "   AND ic.outlet_id=ed.outlet_id\n", "   LEFT JOIN category_data cd ON cd.item_id=ic.item_id\n", "   WHERE facility_id NOT IN (27,\n", "                             83,\n", "                             115) )\n", "SELECT b.facility_id as front_end_fac,\n", "       F1.item_id,\n", "       max(shelf_life) as shelf_life,\n", "       max(F1.expiry_date) as expiry_date,\n", "       max(days_left) as days_left,\n", "       max(days_left)::float/max(shelf_life)::float as percent_days_left\n", "FROM FINAL F1 join lake_retail.console_outlet b on F1.outlet_id = b.id\n", "WHERE b.facility_id in ({facilities})\n", "  AND F1.item_id in ({items})\n", "  AND skip_expiry=0\n", "  and F1.expiry_date is not null-- limit 4000\n", "GROUP BY 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_last_x_days = \"\"\"\n", "SELECT -- date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) AS dated,\n", " r.id AS outlet_id,\n", " i.item_id,\n", " sum(i.quantity) as quantity\n", "FROM ims.ims_order_details o\n", "INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "WHERE date(convert_tz(scheduled_at, \"+00:00\", \"+05:30\")) BETWEEN DATE_ADD(CURRENT_DATE, INTERVAL -{x} DAY) AND CURRENT_DATE\n", "  AND r.id IN ({outlets})\n", "  AND status_id NOT IN (3,\n", "                        5)\n", "GROUP BY 1,\n", "         2\n", "ORDER BY 1,\n", "         2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cms_outlet_details_query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store\n", "from\n", "lake_oms_bifrost.oms_merchant m\n", "join consumer.rt_console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where\n", "cms.active = 1 and cms.cms_update_active = 1\n", "\"\"\"\n", "cpd_query = \"\"\"\n", "SELECT b.facility_id,\n", "       item_id,\n", "       date,\n", "       consumption as system_cpd\n", "FROM snorlax.date_wise_consumption a\n", "LEFT JOIN retail.console_outlet b ON b.id = a.outlet_id\n", "WHERE date BETWEEN CURRENT_DATE - interval 1 week AND CURRENT_DATE + interval 2 week\n", "and b.active = 1\n", "and b.facility_id in ({{facilities}})\n", "and item_id in ({{items}})\n", "\"\"\".replace(\n", "    \"{{\", \"{\"\n", ").replace(\n", "    \"}}\", \"}\"\n", ")\n", "\n", "facility_outlet_mapping_query = \"\"\"\n", "SELECT pco.id as outlet_id,\n", "facility_id\n", "FROM lake_retail.console_outlet pco\n", "INNER JOIN consumer.retail_console_company_type rcc ON pco.company_type_id = rcc.id\n", "-- where facility_id in ({facilities})\n", "GROUP BY 1,2\n", "\"\"\"\n", "\n", "inventory_query = \"\"\"\n", "SELECT good_inv.outlet_id AS outlet_id,\n", "       product.item_id,\n", "       sum(CASE\n", "               WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity ELSE 0\n", "           END) AS quantity,\n", "           0 as blocked_quantity\n", "FROM ims.ims_good_inventory good_inv\n", "INNER JOIN rpc.product_product product ON product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1\n", " -- AND good_inv.outlet_id IN ({{outlets}})\n", "GROUP BY 1,\n", "         2\n", "         \n", "         \n", "UNION\n", "\n", "SELECT a.outlet_id, \n", "       a.item_id, \n", "       a.quantity, \n", "       b.quantity AS blocked_quantity \n", "FROM   (SELECT outlet_id, \n", "               item_id, \n", "               quantity \n", "        FROM   ims.ims_item_inventory \n", "        WHERE  outlet_id IN ( {{outlets}} ) \n", "               AND active = 1) a \n", "       LEFT JOIN (SELECT item_id, \n", "                         outlet_id, \n", "                         sum(quantity) AS quantity\n", "                  FROM   ims.ims_item_blocked_inventory \n", "                  WHERE  -- outlet_id IN ( {{outlets}}  ) AND \n", "                  active = 1 \n", "                         AND blocked_type in (1,2,5)  -- blocked for sto and orders\n", "                 GROUP BY 1,2) b \n", "              ON a.item_id = b.item_id \n", "                 AND a.outlet_id = b.outlet_id \n", "\"\"\"\n", "\n", "sto_query = \"\"\"\n", "WITH sto_max AS\n", "  (SELECT a.receiving_outlet_id as outlet_id,\n", "          a.item_id,\n", "          sum(case when a.invoice_state = 'NA' then a.reserved_quantity else 0 end) +\n", "          sum(case when a.invoice_state = 'Raised' then a.billed_quantity else 0 end) as recieving_quantity\n", "   FROM metrics.esto a\n", "   WHERE a.receiving_outlet_id IN ({outlets}) \n", "   AND a.item_id IN ({items})\n", "   AND a.invoice_state IN ('NA','Raised')\n", "   AND a.sto_state != 'Expired'\n", "   GROUP BY \n", "   a.receiving_outlet_id,\n", "   a.item_id)\n", "select * from sto_max\n", "\"\"\"\n", "\n", "item_case_size_query = \"\"\"\n", "select distinct item_id,max(case when outer_case_size is null \n", "or outer_case_size = 0 then 1 else outer_case_size end) \n", "as outer_case_size from lake_rpc.product_product \n", "where item_id in ({items}) and active = 1 group by 1\n", "\"\"\"\n", "\n", "item_product_mapping_query = \"\"\"\n", "select distinct item_id,product_id from lake_rpc.item_product_mapping where active = 1\n", "\"\"\"\n", "\n", "replenishment_orders_query = \"\"\"\n", "SELECT i.item_id,\n", "         r.id AS outlet_id,\n", "            date(convert_tz(scheduled_at,\"+00:00\",\"+05:30\")) AS delivery_date,\n", "           -- count(DISTINCT CASE WHEN o.status_id NOT IN (3,5) THEN o.order_id END) AS total_orders,\n", "           count(DISTINCT o.order_id) AS total_orders,\n", "           count(DISTINCT CASE WHEN o.status_id IN (2,6) THEN o.order_id END) AS billed_orders,\n", "           sum(i.quantity) as quantity\n", "    FROM ims.ims_order_details o\n", "    INNER JOIN ims.ims_order_items i ON o.id = i.order_details_id\n", "    INNER JOIN retail.console_outlet r ON o.outlet = r.id\n", "    where r.id in ({dark_stores})\n", "    GROUP BY 1,\n", "             2,3\n", "    ORDER BY 1,\n", "             2,3 \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attri = \"\"\"\n", "\n", "\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "   AND rpc.item_id IN ({items})\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["handler = logging.FileHandler(\"/tmp/files.log\")\n", "formatter = logging.Formatter(\"%(asctime)s - %(name)s - %(levelname)s - %(message)s\")\n", "handler.set<PERSON><PERSON><PERSON><PERSON>(formatter)\n", "log = logging.getLogger(\"INFO\")\n", "log.add<PERSON><PERSON><PERSON>(handler)\n", "log.setLevel(logging.INFO)\n", "log.info(\"\\n\\nExecution started...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TODAY = datetime.date.today()\n", "NOW = datetime.datetime.today()\n", "try:\n", "    CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "    CON_SQL = pb.get_connection(\"retail\")\n", "    CON_AURORA = pb.get_connection(\"aurora\")\n", "    CON_SUPPLY = pb.get_connection(\"supply_orchestrator\")\n", "except:\n", "    CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "    CON_SQL = pb.get_connection(\"retail\")\n", "    CON_AURORA = pb.get_connection(\"aurora\")\n", "    CON_SUPPLY = pb.get_connection(\"supply_orchestrator\")\n", "log.info(\"DB connections established successfully\")\n", "MAIL_LOGS = []\n", "REQ_METRICS = {}\n", "CASE_ERROR = []\n", "stock_out_reasons = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Input Sheet Details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "master_assortment = \"1yYvwsApPY3IvWxEw4UWFPd_wBE19qrMBcg6orhKU7YI\"\n", "sheet_names = [\n", "    \"Mapping\",\n", "    \"DOI Matrix\",\n", "    \"DoI Reserve\",\n", "    \"Gurgaon(HR)\",\n", "    \"noida/ghaziabad\",\n", "    \"Emails2\",\n", "    \"Truck Capacity\",\n", "    \"check_items\",\n", "    \"outer_case_size_min\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    mapping_sheet = pb.from_sheets(\n", "        sheet_id, sheet_names[0], service_account=\"service_account\", clear_cache=True\n", "    )\n", "except:\n", "    sleep(100)\n", "    mapping_sheet = pb.from_sheets(\n", "        sheet_id, sheet_names[0], service_account=\"service_account\", clear_cache=True\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet = mapping_sheet.astype(mapping_schema)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check if today is picking day"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V4_5_logs = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "def picking_day_flag(picking_days):\n", "    dow_today = int(TODAY.weekday())\n", "    picking_days = list(map(int, picking_days.split(\",\")))\n", "    if dow_today in picking_days:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet[\"if_picking_day\"] = mapping_sheet[\"picking_days\"].apply(picking_day_flag)  # v4.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets_excluded = mapping_sheet[\n", "    (mapping_sheet.is_darkstore != 1) & (mapping_sheet.if_picking_day == 0)\n", "]  # v4.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V4_5_logs[\"outlets_excluded\"] = \" ,\".join(\n", "    map(str, list(outlets_excluded.dark_store.unique()))\n", ")  # v4.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet = mapping_sheet[\n", "    (mapping_sheet.is_darkstore == 1) | (mapping_sheet.if_picking_day == 1)\n", "]  # v4.5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Days till next dispatch day"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def days_till_next_dispatch_day(dispatch_days):\n", "    dow_today = int(TODAY.weekday())\n", "    dispatch_days = list(map(int, dispatch_days.split(\",\")))\n", "    expelled_dispatch_days = dispatch_days + list(np.array(dispatch_days) + 7)\n", "    for day in expelled_dispatch_days:\n", "        if day >= dow_today:\n", "            return day - dow_today\n", "        else:\n", "            continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet[\"days_till_next_tf_day\"] = mapping_sheet[\"dispatch_days\"].apply(\n", "    days_till_next_dispatch_day\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet[\"doi\"] += mapping_sheet[\"days_till_next_tf_day\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Morning and Evening"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.datetime.now()\n", "today10 = now.replace(hour=10, minute=0, second=0, microsecond=0)\n", "if now < today10:\n", "    mapping_sheet[\"truck_load_size\"] = mapping_sheet[\"truck_load_size_morning\"]\n", "    mapping_sheet[\"picking_capacity\"] = mapping_sheet[\"picking_capacity_morning\"]\n", "    time = \"Morning\"\n", "else:\n", "    mapping_sheet[\"truck_load_size\"] = mapping_sheet[\"truck_load_size_evening\"]\n", "    mapping_sheet[\"picking_capacity\"] = mapping_sheet[\"picking_capacity_evening\"]\n", "    time = \"Evening\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email = pb.from_sheets(\n", "    sheet_id, sheet_names[5], service_account=\"service_account\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email = list(\n", "    set(\n", "        [\n", "            x.strip()\n", "            for x in list(email[\"hot\"].to_list() + email[\"grofers\"].to_list())\n", "            if len(x.strip()) > 0\n", "        ]\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email.sort()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = pd.read_sql_query(sql=assortment, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.item_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment.groupby([\"dark_store\", \"item_id\"]).max().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_level(df, cols, df_name):\n", "    if df.shape[0] != df[cols].drop_duplicates().shape[0]:\n", "        raise Exception(\"Sorry, level does not match\", df_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(assortment, [\"dark_store\", \"item_id\"], \"assortment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ass_846 = assortment[assortment.dark_store == 846]  # adhoc"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Total items in the assortment - Outlet wise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "total_items = (\n", "    assortment.groupby(\"dark_store\")[[\"item_id\"]]\n", "    .count()\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"total_items\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "def picking_days_len(picking_days):\n", "    picking_days = list(map(int, picking_days.split(\",\")))\n", "    return len(picking_days)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "picking_days_len_df = mapping_sheet[[\"dark_store\", \"picking_days\"]].drop_duplicates()\n", "picking_days_len_df[\"picking_days_len\"] = picking_days_len_df[\"picking_days\"].apply(\n", "    picking_days_len\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "items_today = pd.merge(picking_days_len_df, total_items, on=[\"dark_store\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_today = items_today.dropna()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "items_today[\"number_of_items\"] = items_today[\"total_items\"] / items_today[\"picking_days_len\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_today"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "items_today[\"number_of_items\"] = items_today[\"number_of_items\"].apply(math.floor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "items_today = items_today[[\"dark_store\", \"number_of_items\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "mapping_sheet = pd.merge(mapping_sheet, items_today, on=[\"dark_store\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "mapping_sheet[\"distinct_skus\"] = np.where(\n", "    (mapping_sheet.distinct_skus > mapping_sheet.number_of_items)\n", "    & (mapping_sheet.is_darkstore == 0),\n", "    mapping_sheet.number_of_items,\n", "    mapping_sheet.distinct_skus,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Frontend facility, item to outlet mapping"]}, {"cell_type": "raw", "metadata": {}, "source": ["front_end_outlet_mapping = assortment[[\"front_end_outlet\", \"front_end_fac\", \"item_id\"]]"]}, {"cell_type": "raw", "metadata": {}, "source": ["front_end_outlet_mapping = front_end_outlet_mapping[front_end_outlet_mapping.front_end_outlet != 0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Facility outlet mapping from system"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_outlet_mapping = pd.read_sql_query(sql=facility_outlet_mapping_query, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(facility_outlet_mapping, [\"outlet_id\", \"facility_id\"], \"assortment\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Joining mapping sheet and facility outlet mapping from system"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet_v1 = pd.merge(\n", "    mapping_sheet[\n", "        [\"front_end\", \"dark_store\", \"doi_to_reserve\", \"doi\", \"moq\", \"is_darkstore\"]\n", "    ],  # v4.5\n", "    facility_outlet_mapping.rename(\n", "        columns={\"outlet_id\": \"front_end\", \"facility_id\": \"front_end_fac\"}\n", "    ),\n", "    on=[\"front_end\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Names of dark stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["names = mapping_sheet[[\"dark_store\", \"name\"]].drop_duplicates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Mapping sheet joined with assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = pd.merge(\n", "    assortment,\n", "    mapping_sheet_v1[\n", "        [\n", "            \"front_end_fac\",\n", "            \"dark_store\",\n", "            \"doi_to_reserve\",\n", "            \"doi\",\n", "            \"moq\",\n", "            \"is_darkstore\",\n", "        ]  # v4.5\n", "    ].drop_duplicates(),\n", "    how=\"inner\",\n", "    on=[\"dark_store\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(\n", "    assortment,\n", "    [\"front_end_fac\", \"dark_store\", \"item_id\"],\n", "    \"assortment\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_last_updated = str(assortment.run_id.to_list()[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Reference notes\n", "- This code is a part of the picking days feature\n", "- In this snippet we're trying to remove the the items from base assortment which were sent in the previous indent\n", "- For the same, we've taken a list for items that got missed in the last updated indent for stock out table\n", "- Items only where missing flag is False or if the items belongs to the dark store were picked"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# List of entities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "items_sent_in_last_indent = \"\"\"\n", "WITH items AS\n", "  (SELECT item_id,\n", "          date(sto_created_at) AS sto_created_at,\n", "          receiving_outlet_id\n", "   FROM metrics.esto\n", "   WHERE receiving_outlet_id IN ({outlets})),\n", "     latest_date AS\n", "  (SELECT receiving_outlet_id,\n", "          max(date(sto_created_at)) AS sto_created_at\n", "   FROM metrics.esto\n", "   WHERE receiving_outlet_id IN ({outlets})\n", "   GROUP BY 1)\n", "SELECT distinct a.receiving_outlet_id AS dark_store,\n", "       a.item_id,\n", "       1 AS sent_before\n", "FROM items a\n", "INNER JOIN latest_date b ON a.receiving_outlet_id = b.receiving_outlet_id\n", "AND a.sto_created_at = b.sto_created_at\n", "\"\"\".format(\n", "    outlets=\", \".join(map(str, list(assortment.dark_store.unique())))\n", ")\n", "items_sent_in_last_indent = pd.read_sql_query(sql=items_sent_in_last_indent, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "check_level(items_sent_in_last_indent, [\"dark_store\", \"item_id\"], \"items_sent_in_last_indent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "assortment = pd.merge(\n", "    assortment, items_sent_in_last_indent, on=[\"item_id\", \"dark_store\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "stock_out_reasons[\"items_sent_in_last_indent\"] = assortment[\n", "    (assortment.sent_before == 1) & (assortment.is_darkstore != 1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "assortment = assortment[(assortment.sent_before != 1) | (assortment.is_darkstore == 1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "assortment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Merged input : subset of assortment df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_inputs = assortment[\n", "    [\n", "        \"dark_store\",\n", "        \"front_end_fac\",\n", "        \"doi\",\n", "        \"doi_to_reserve\",\n", "        \"run_id\",\n", "        \"item_id\",\n", "        \"rank\",\n", "        \"moq\",\n", "        \"case_sensitive\",\n", "        \"is_darkstore\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_inputs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stock out report : start"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_report = (\n", "    merged_inputs.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"total_assortment\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catering_outlets = \", \".join(list(map(str, list(mapping_sheet_v1.front_end.unique()))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores_list = list(merged_inputs.dark_store.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores = \", \".join([str(int(x)) for x in dark_stores_list])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list = list(merged_inputs.item_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = \", \".join([str(int(x)) for x in items_list])"]}, {"cell_type": "raw", "metadata": {}, "source": ["outlets_list = list(merged_inputs.front_end.unique())"]}, {"cell_type": "raw", "metadata": {}, "source": ["outlets = \", \".join([str(int(x)) for x in outlets_list])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_outlets = dark_stores  # + \", \" + outlets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catering_facilities_list = list(merged_inputs.front_end_fac.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_inputs[merged_inputs.front_end_fac.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catering_facilities = \", \".join([str(int(x)) for x in catering_facilities_list])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reading CPD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd = pd.read_sql_query(\n", "    sql=cpd_query.format(items=items, facilities=catering_facilities), con=CON_SQL\n", ")\n", "cpd = cpd.astype(cpd_schema)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CPD group by max"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd = cpd.groupby([\"facility_id\", \"item_id\", \"date\"]).max().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(cpd, [\"facility_id\", \"item_id\", \"date\"], \"cpd\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd[\"cpd\"] = cpd[\"system_cpd\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Item attributes DF"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attri_df = pd.read_sql_query(sql=item_attri.format(items=items), con=CON_REDSHIFT)\n", "item_attri_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attri_df = item_attri_df.groupby([\"item_id\"]).max().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(item_attri_df.drop_duplicates(), [\"item_id\"], \"cpd\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# F and V flag"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fnv_flag = pd.read_sql_query(sql=fnv_query.format(items=items), con=CON_REDSHIFT)\n", "fnv_flag.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Current quantity for dark stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = pd.read_sql_query(\n", "    sql=inventory_query.replace(\"{{\", \"{\")\n", "    .replace(\"}}\", \"}\")\n", "    .format(outlets=all_outlets, items=items),\n", "    con=CON_SQL,\n", ")\n", "quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disp_qty = deepcopy(quantity)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Group by sum "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity.groupby([\"outlet_id\", \"item_id\"]).agg(\n", "    {\"quantity\": \"sum\", \"blocked_quantity\": \"sum\"}\n", ")\n", "quantity = quantity.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(quantity, [\"outlet_id\", \"item_id\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = deepcopy(quantity)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reverse STO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_reverse_sto = deepcopy(quantity)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_last_x_days = pd.read_sql_query(\n", "    sql=sales_last_x_days.format(outlets=dark_stores, x=\"20\"),\n", "    con=CON_SQL,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_last_x_days.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(sales_last_x_days, [\"outlet_id\", \"item_id\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reverse_sto_v1 = pd.merge(\n", "    quantity_reverse_sto,\n", "    sales_last_x_days,\n", "    on=[\"item_id\", \"outlet_id\"],\n", "    how=\"left\",\n", "    suffixes=[\"_existing\", \"_sold_in_last_21_days\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["live_items_past_x_days = \"\"\"\n", "SELECT outlet_id,\n", "       item_id,\n", "       count(inventory_log_id) AS inventory_log_id_count\n", "FROM lake_ims.ims_item_inventory_log\n", "WHERE outlet_id IN ({outlets})\n", "  AND date(created_at) = CURRENT_DATE - 21\n", "GROUP BY 1,\n", "         2\n", "having inventory_log_id_count > 0\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["live_items_past_x_days = pd.read_sql_query(\n", "    sql=live_items_past_x_days.format(outlets=dark_stores),\n", "    con=CON_REDSHIFT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["live_items_past_x_days.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(live_items_past_x_days, [\"outlet_id\", \"item_id\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reverse_sto_v1 = reverse_sto_v1[reverse_sto_v1.outlet_id.isin(dark_stores_list)].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reverse_sto_v1 = pd.merge(\n", "    reverse_sto_v1, live_items_past_x_days, on=[\"item_id\", \"outlet_id\"], how=\"inner\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reverse_sto_v1[\"quantity_net\"] = (\n", "    reverse_sto_v1[\"quantity_existing\"] - reverse_sto_v1[\"blocked_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reverse_sto_v1[\"sold_by_net\"] = (\n", "    reverse_sto_v1[\"quantity_sold_in_last_21_days\"] / reverse_sto_v1[\"quantity_net\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reverse_sto_v1 = reverse_sto_v1[\n", "    (reverse_sto_v1.sold_by_net < 0.2) & (reverse_sto_v1.sold_by_net >= 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catering_facilities"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### !Reverse STO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" SELECT DISTINCT a.item_id,\n", "               a.outlet_id,\n", "                r.facility_id,\n", "                CASE\n", "                    WHEN sum(quantity) > 0 THEN sum(quantity)\n", "                    ELSE 0\n", "                END AS available_quantity\n", "FROM ims.ims_item_inventory AS a\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "          max(updated_at)AS update_at\n", "   FROM ims.ims_item_inventory\n", "   GROUP BY 1,\n", "            2) AS b ON a.item_id=b.item_id\n", "AND a.outlet_id=b.outlet_id\n", "AND a.updated_at=b.update_at\n", "INNER JOIN retail.console_outlet r ON a.outlet_id = r.id\n", "AND r.facility_id in ({facilities}) \n", "WHERE r.active = 1\n", "AND a.outlet_id in ({outlets})\n", "  AND r.device_id <> 47\n", "  AND quantity > 0\n", "GROUP BY 1,\n", "         2\n", "   \"\"\".format(\n", "    facilities=catering_facilities, outlets=catering_outlets\n", ")\n", "Total_inventory_v1 = pd.read_sql_query(query, CON_SQL)\n", "\n", "Total_inventory_v2 = deepcopy(Total_inventory_v1)\n", "\n", "Total_inventory_v1 = (\n", "    Total_inventory_v1[[\"item_id\", \"facility_id\", \"available_quantity\"]]\n", "    .groupby([\"item_id\", \"facility_id\"])\n", "    .max()\n", "    .reset_index()\n", ")\n", "\n", "Total_inventory_v1.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_inventory_v2 = Total_inventory_v2.drop_duplicates()\n", "\n", "Total_inventory_v2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_inventory_v3 = pd.merge(\n", "    Total_inventory_v1,\n", "    Total_inventory_v2,\n", "    on=[\"item_id\", \"facility_id\", \"available_quantity\"],\n", "    how=\"left\",\n", ").drop_duplicates()\n", "\n", "Total_inventory_v3.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_inventory_v3 = Total_inventory_v3.groupby([\"item_id\", \"facility_id\"]).max().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_inventory_v3.outlet_id.unique()\n", "# catering_outlets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Current quantities for catering facilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\" SELECT DISTINCT a.item_id, \n", "                r.facility_id,\n", "                CASE\n", "                    WHEN sum(quantity) > 0 THEN sum(quantity)\n", "                    ELSE 0\n", "                END AS available_quantity\n", "FROM ims.ims_item_inventory AS a\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "          max(updated_at)AS update_at\n", "   FROM ims.ims_item_inventory\n", "   GROUP BY 1,\n", "            2) AS b ON a.item_id=b.item_id\n", "AND a.outlet_id=b.outlet_id\n", "AND a.updated_at=b.update_at\n", "INNER JOIN retail.console_outlet r ON a.outlet_id = r.id\n", "AND r.facility_id in ({facilities}) \n", "WHERE r.active = 1\n", "and a.outlet_id in ({outlets})\n", "  AND r.device_id <> 47\n", "  AND quantity > 0\n", "GROUP BY 1,\n", "         2\n", "   \"\"\".format(\n", "    facilities=catering_facilities, outlets=catering_outlets\n", ")\n", "Total_inventory = pd.read_sql_query(query, CON_SQL)\n", "Total_inventory.head()\n", "blocked = \"\"\"select item_id ,\n", "     facility_id ,\n", "     case when sum(quantity) > 0 then sum(quantity) else 0 end as blocked_quantity\n", "FROM ims.ims_item_blocked_inventory i\n", "inner join retail.console_outlet ro on ro.id=i.outlet_id\n", "where facility_id in ({facilities})\n", "and i.outlet_id in ({outlets})\n", "and blocked_type in (1,2,5) and i.active = 1\n", "group by 1,2\n", "      \"\"\".format(\n", "    facilities=catering_facilities, outlets=catering_outlets\n", ")\n", "block_quantity = pd.read_sql_query(blocked, CON_SQL)\n", "inv_data = Total_inventory.merge(block_quantity, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "inv_data = inv_data.drop_duplicates().fillna(0)\n", "inv_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(inv_data, [\"item_id\", \"facility_id\"], \"inv_data\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g4 = inv_data[inv_data.facility_id == 29]  # adhoc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = deepcopy(inv_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity.rename(columns={\"facility_id\": \"front_end_fac\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_inputs.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"assortment\"] = (\n", "    merged_inputs.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"Count of SKUs to be replenished today (in Units)\"})\n", ")\n", "summary[\"assortment\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = pd.merge(quantity, merged_inputs, how=\"right\", on=[\"front_end_fac\", \"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_inputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"stock_unavailable_at_catering_facility\"] = quantity[\n", "    (quantity.available_quantity - quantity.blocked_quantity) <= 0\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity[(quantity.available_quantity - quantity.blocked_quantity) > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd = cpd.rename(columns={\"facility_id\": \"front_end_fac\", \"cpd\": \"cpd_list\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = pd.merge(quantity, cpd, on=[\"item_id\", \"front_end_fac\"], how=\"left\").dropna()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def delta(doi):\n", "    return TODAY + datetime.timedelta(days=doi)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity[\"delta_date_reserve\"] = quantity[\"doi_to_reserve\"].apply(delta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity[\"delta_date_reserve\"] = quantity[\"delta_date_reserve\"].astype(\"datetime64[ns]\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity[\n", "    (quantity[\"date\"] <= quantity[\"delta_date_reserve\"]) & (quantity[\"date\"] > TODAY)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = (\n", "    quantity.groupby(\n", "        [\n", "            \"item_id\",\n", "            \"front_end_fac\",\n", "            \"available_quantity\",\n", "            \"blocked_quantity\",\n", "            \"case_sensitive\",\n", "            \"dark_store\",\n", "            \"doi\",\n", "            \"doi_to_reserve\",\n", "            \"run_id\",\n", "            \"rank\",\n", "            \"moq\",\n", "        ]\n", "    )[\"cpd_list\"]\n", "    .apply(list)\n", "    .reset_index()\n", ")\n", "\n", "quantity.head()"]}, {"cell_type": "raw", "metadata": {}, "source": ["def cpd_list(row):\n", "    cpd_list = []\n", "    facility_id = row[\"front_end_fac\"]\n", "    item_id = row[\"item_id\"]\n", "    doi_to_reserve = row[\"doi_to_reserve\"]\n", "    cpd_item = cpd[\n", "        (cpd[\"facility_id\"] == facility_id)\n", "        & (cpd[\"item_id\"] == item_id)\n", "        & (cpd[\"date\"] > TODAY)\n", "        & (cpd[\"date\"] <= TODAY + datetime.timedelta(days=math.ceil(doi_to_reserve)))\n", "    ]\n", "    remainder = doi_to_reserve - math.floor(doi_to_reserve)\n", "    cpd_list = cpd_item.cpd.to_list()\n", "    if len(cpd_list) > 0:\n", "        if remainder > 0:\n", "            cpd_list[-1] = cpd_list[-1] * remainder\n", "        return cpd_list\n", "    else:\n", "        return []"]}, {"cell_type": "raw", "metadata": {}, "source": ["quantity[\"cpd_list\"] = quantity.apply(cpd_list, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity[\"total_quantity_preserved\"] = quantity[\"cpd_list\"].apply(sum).apply(math.ceil)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(merged_inputs, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_inputs[merged_inputs.item_id == 10000048]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity[[\"item_id\", \"front_end_fac\", \"dark_store\"]][\n", "    quantity[[\"item_id\", \"front_end_fac\", \"dark_store\"]].duplicated()\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transferable Qty"]}, {"cell_type": "raw", "metadata": {}, "source": ["quantity[\"transferable_qty\"] = (\n", "    quantity[\"available_quantity\"]\n", "    - quantity[\"blocked_quantity\"]\n", "    - quantity[\"total_quantity_preserved\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity[\"net_quantity\"] = quantity[\"available_quantity\"] - quantity[\"blocked_quantity\"]"]}, {"cell_type": "raw", "metadata": {}, "source": ["stock_out_reasons[\"stock_available_but_not_transferable\"] = quantity[\n", "    quantity[\"transferable_qty\"] <= 0\n", "]"]}, {"cell_type": "raw", "metadata": {}, "source": ["quantity = quantity[quantity[\"transferable_qty\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"cat_fac_available\"] = (\n", "    quantity.groupby([\"dark_store\", \"front_end_fac\"])\n", "    .agg({\"item_id\": \"count\", \"net_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of available SKUs at catering facility (in Units)\",\n", "            \"net_quantity\": \"Total quantity available at catering facility (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"cat_fac_available\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shelf_life_df = pd.read_sql_query(\n", "    sql=shelf_life.format(facilities=catering_facilities, items=items).replace(\"%\", \"%%\"),\n", "    con=CON_REDSHIFT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(shelf_life_df, [\"item_id\", \"front_end_fac\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = pd.merge(\n", "    quantity,\n", "    shelf_life_df,\n", "    how=\"left\",\n", "    left_on=[\"front_end_fac\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(quantity, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"near_to_expiry\"] = quantity[quantity[\"percent_days_left\"] < 0.33]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity[\n", "    (quantity[\"percent_days_left\"] >= 0.33) | (quantity[\"percent_days_left\"].isna())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"cat_fac_available_after_exp\"] = (\n", "    quantity.groupby(\"dark_store\")\n", "    .agg({\"item_id\": \"count\", \"net_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of available SKUs at catering facility after removing expired items (in Units)\",\n", "            \"net_quantity\": \"Total quantity available at catering facility after removing expired items (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"cat_fac_available_after_exp\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ! <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_size_df = pd.read_sql(\n", "    sql=item_case_size_query.format(items=items),\n", "    con=CON_REDSHIFT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity.drop(\"doi\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = quantity_full.rename(\n", "    columns={\"outlet_id\": \"dark_store\", \"quantity\": \"available_quantity\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(quantity_full, [\"item_id\", \"dark_store\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = pd.merge(\n", "    quantity_full,\n", "    merged_inputs,\n", "    how=\"right\",\n", "    left_on=[\"dark_store\", \"item_id\"],\n", "    right_on=[\"dark_store\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_query = \"\"\"WITH sto_max AS\n", "  (SELECT \n", "  date(sto_created_at) as date_of_creation,\n", "  a.receiving_outlet_id as outlet_id,\n", "          a.item_id,\n", "          sum(case when a.invoice_state = 'NA' then a.reserved_quantity else 0 end) +\n", "          sum(case when a.invoice_state = 'Raised' then a.billed_quantity else 0 end) as recieving_quantity\n", "   FROM metrics.esto a\n", "   WHERE a.receiving_outlet_id IN ({outlets}) \n", "   AND a.item_id IN ({items})\n", "   AND a.invoice_state IN ('NA','Raised')\n", "   AND a.sto_state != 'Expired'\n", "   GROUP BY \n", "   a.receiving_outlet_id,\n", "   a.item_id,\n", "   date(sto_created_at))\n", "select * from sto_max\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos = pd.read_sql_query(\n", "    sql=sto_query.format(outlets=dark_stores, items=items),\n", "    con=CON_REDSHIFT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos_display = (\n", "    stos.groupby([\"date_of_creation\", \"outlet_id\"])\n", "    .agg({\"item_id\": \"count\", \"recieving_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"item_id\": \"SKUs\", \"outlet_id\": \"Outlet ID\"})\n", ")\n", "# .pivot(index=\"outlet_id\", columns=\"dominant_reason\", values=\"item_id\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos_display = stos_display.applymap(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos_display[\"SKUs (Quantity)\"] = (\n", "    stos_display[\"SKUs\"] + \" (\" + stos_display[\"recieving_quantity\"] + \")\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos_display = (\n", "    stos_display.pivot(index=\"Outlet ID\", columns=\"date_of_creation\", values=\"SKUs (Quantity)\")\n", "    .fillna(\"\")\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos_display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_to_html(df):\n", "    html_table = \"\"\"<div class=\"mce-toc\">\n", "      <table style=\"border-collapse: collapse; width: 1000px; \" border=\"1\">\n", "        <tbody>\n", "          <tr style=\"height: 57px;\">\n", "    \"\"\"\n", "\n", "    for x in df.columns:\n", "        html_table += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "        html_table += x\n", "        html_table += \"\"\"</strong></span></td>\"\"\"\n", "\n", "    html_table += \"\"\"</tr>\"\"\"\n", "\n", "    for i, r in df.iterrows():\n", "        html_table += \"\"\"<tr style=\"height: 15px;\">\"\"\"\n", "        for x in df.columns:\n", "            if x == \"Outlet ID\":\n", "                html_table += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "                html_table += str(r[x])\n", "                html_table += \"</strong></span></td>\"\n", "            else:\n", "                html_table += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\">\"\"\"\n", "                html_table += str(r[x])\n", "                html_table += \"</td>\"\n", "        html_table += \"</tr>\"\n", "    html_table += \"</tbody> </table> </div>\"\n", "    return html_table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos_display_html = df_to_html(stos_display)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(stos, [\"date_of_creation\", \"outlet_id\", \"item_id\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos = stos.rename(columns={\"outlet_id\": \"dark_store\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stos = stos.groupby([\"dark_store\", \"item_id\"]).agg({\"recieving_quantity\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"sto\"] = (\n", "    stos.groupby(\"dark_store\")\n", "    .agg({\"item_id\": \"count\", \"recieving_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of SKUs in transit (in Units)\",\n", "            \"recieving_quantity\": \"Total quantity in transit (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = pd.merge(\n", "    quantity_full,\n", "    stos,\n", "    how=\"left\",\n", "    left_on=[\"dark_store\", \"item_id\"],\n", "    right_on=[\"dark_store\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_ds = \"\"\"\n", "SELECT date(delivery_date) as date,\n", "       ds_outlet as dark_store,\n", "       item_id,\n", "       final_cpd as cpd\n", "FROM metrics.dark_store_cpd\n", "WHERE delivery_date BETWEEN CURRENT_DATE+ interval '1 day' AND CURRENT_DATE+ interval '10 day'\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_outlet = \"(\" + all_outlets + \")\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full.dark_store.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_ds1 = \"\"\"\n", "select distinct wom.cloud_store_id as dark_store,\n", "dwc.date as date ,\n", "dwc.item_id, \n", "dwc.consumption as cpd \n", "from \n", "(select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "date between current_date + interval 1 day\n", "and current_date + interval 2 week) dwc\n", "join \n", "retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "left join retail.console_outlet ro on ro.id=dwc.outlet_id\n", "where wom.cloud_store_id in ({all_outlets})\n", "\"\"\".format(\n", "    all_outlets=all_outlets\n", ")\n", "cpd1 = pd.read_sql_query(cpd_ds1, CON_SQL, params={\"outlet\": ds_outlet})\n", "cpd1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select outlet_id as dark_store,\n", "item_id,\n", "date,\n", "consumption as cpd\n", "from snorlax.date_wise_consumption where \n", "date between current_date + interval 1 day\n", "and current_date + interval 2 week\n", "and outlet_id in ({all_outlets})\n", "\"\"\".format(\n", "    all_outlets=all_outlets\n", ")\n", "cpd2 = pd.read_sql_query(cpd_query, CON_SQL)\n", "cpd2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_ds = pd.concat([cpd1, cpd2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# quantity_full.dark_store.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = pd.merge(quantity_full, cpd_ds, on=[\"item_id\", \"dark_store\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def delta(doi):\n", "    return TODAY + datetime.timedelta(days=doi)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full[\"delta_date\"] = quantity_full[\"doi\"].apply(delta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = quantity_full[quantity_full[\"date\"] <= quantity_full[\"delta_date\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# quantity_full[quantity_full.is_darkstore == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = (\n", "    quantity_full.groupby(\n", "        [\n", "            \"dark_store\",\n", "            \"item_id\",\n", "            \"available_quantity\",\n", "            \"blocked_quantity\",\n", "            \"front_end_fac\",\n", "            \"doi\",\n", "            \"doi_to_reserve\",\n", "            \"run_id\",\n", "            \"rank\",\n", "            \"moq\",\n", "            \"recieving_quantity\",\n", "            \"is_darkstore\",\n", "        ]\n", "    )[\"cpd\"]\n", "    .apply(list)\n", "    .reset_index()\n", "    .rename(columns={\"cpd\": \"cpd_list\"})\n", ")\n", "quantity_full.head()"]}, {"cell_type": "raw", "metadata": {}, "source": ["def cpd_list(row):\n", "    cpd_item = cpd_ds[\n", "        (cpd_ds[\"outlet_id\"] == row[\"dark_store\"])\n", "        & (cpd_ds[\"item_id\"] == row[\"item_id\"])\n", "        & (cpd_ds[\"date\"] > TODAY)\n", "        & (cpd_ds[\"date\"] <= TODAY + datetime.timedelta(days=row[\"doi\"]))\n", "    ]\n", "    #remainder = doi_to_reserve - math.floor(doi_to_reserve)\n", "    return cpd_item.cpd.to_list()\n", "    #return cpd_list\n", "# if remainder > 0:\n", "#            cpd_list[-1] = cpd_list[-1] * remainder"]}, {"cell_type": "raw", "metadata": {}, "source": ["quantity_full[\"cpd_list\"] = quantity_full.apply(cpd_list, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full[\"threshold_qty_before_diff\"] = quantity_full[\"cpd_list\"].apply(sum).apply(math.ceil)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full[\"threshold_qty\"] = quantity_full[\n", "    \"threshold_qty_before_diff\"\n", "]  # + quantity_full[\"difference\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = quantity_full.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(quantity_full, [\"dark_store\", \"item_id\", \"front_end_fac\"], \"quantity\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Required Quantity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full[\"required_qty\"] = (\n", "    quantity_full[\"threshold_qty\"]\n", "    - (quantity_full[\"available_quantity\"] - quantity_full[\"blocked_quantity\"])\n", "    - quantity_full[\"recieving_quantity\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"quantity_not_required_at_darkstore\"] = quantity_full[\n", "    quantity_full[\"required_qty\"] <= 0\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = quantity_full[quantity_full[\"required_qty\"] > 0]\n", "quantity_full.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full[\"avg_cpd_at_dark_store\"] = quantity_full.cpd_list.apply(np.mean)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full[\"existing_doi_at_darkstore\"] = (\n", "    quantity_full[\"available_quantity\"] - quantity_full[\"blocked_quantity\"]\n", ") / quantity_full[\"avg_cpd_at_dark_store\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets_query = \"\"\"SELECT DISTINCT a.item_id, \n", "                b3.facility_id AS front_end_fac, \n", "                ( CASE \n", "                    WHEN a.tag_value = 'A' \n", "                         AND a1.tag_value = 'Y' THEN 'X' \n", "                    WHEN a.tag_value = 'A' \n", "                         AND a1.tag_value IS NULL THEN 'A' \n", "                    ELSE 'B' \n", "                  END )        AS final_bucket \n", "FROM   rpc.item_outlet_tag_mapping a \n", "       LEFT JOIN rpc.item_outlet_tag_mapping a1 \n", "              ON a1.item_id = a.item_id \n", "                 AND a1.outlet_id = a.outlet_id \n", "                 AND a1.tag_type_id = 6 \n", "                 AND a1.tag_value = 'Y' \n", "       LEFT JOIN po.physical_facility_outlet_mapping b3 \n", "              ON b3.outlet_id = a.outlet_id \n", "WHERE  a.tag_type_id IN ( 1 ) \n", "       AND a.active = 1 \n", "       AND b3.facility_id IN ( {catering_facilities} ) \"\"\".format(\n", "    catering_facilities=catering_facilities\n", ")  # v4.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "buckets_df = pd.read_sql_query(buckets_query, CON_SQL)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def assign_rank(bucket):\n", "    dict_ = {\"X\": 1, \"A\": 2, \"B\": 3}\n", "    return dict_[bucket]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets_df[\"rank_bucket\"] = buckets_df[\"final_bucket\"].apply(assign_rank)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets_df = (\n", "    buckets_df.groupby([\"item_id\", \"front_end_fac\"]).agg({\"rank_bucket\": min}).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def assign_buckets(rank):\n", "    dict_ = {1: \"X\", 2: \"A\", 3: \"B\"}\n", "    return dict_[rank]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buckets_df[\"final_bucket\"] = buckets_df[\"rank_bucket\"].apply(assign_buckets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "quantity_full = pd.merge(quantity_full, buckets_df, on=[\"item_id\", \"front_end_fac\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "quantity_full[\"final_bucket\"] = quantity_full[\"final_bucket\"].fillna(\"B\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "def keep_flag(row):\n", "    final_bucket = row[\"final_bucket\"]\n", "    if final_bucket == \"X\" and row[\"existing_doi_at_darkstore\"] <= 2:\n", "        return 1\n", "    elif final_bucket == \"A\" and row[\"existing_doi_at_darkstore\"] <= 1:\n", "        return 1\n", "    elif final_bucket == \"B\" and row[\"existing_doi_at_darkstore\"] <= 0.5:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "quantity_full[\"keep_flag\"] = quantity_full.apply(keep_flag, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "stock_out_reasons[\"more_than_trigger_doi\"] = quantity_full[\n", "    (quantity_full.keep_flag == 0) & (quantity_full.is_darkstore == 1)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v4.5\n", "quantity_full = quantity_full[(quantity_full.keep_flag == 1) | (quantity_full.is_darkstore != 1)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity[\"avg_cpd_at_frontend\"] = quantity.cpd_list.apply(np.mean)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep = pd.merge(\n", "    quantity[\n", "        [\n", "            \"dark_store\",\n", "            \"front_end_fac\",\n", "            \"item_id\",\n", "            \"net_quantity\",\n", "            \"avg_cpd_at_frontend\",\n", "            \"moq\",\n", "        ]\n", "    ],\n", "    quantity_full[\n", "        [\n", "            \"dark_store\",\n", "            \"front_end_fac\",\n", "            \"item_id\",\n", "            \"required_qty\",\n", "            \"avg_cpd_at_dark_store\",\n", "            \"existing_doi_at_darkstore\",\n", "        ]\n", "    ],\n", "    how=\"inner\",\n", "    left_on=[\"front_end_fac\", \"dark_store\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"dark_store\", \"item_id\"],\n", ")  # v4.8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(quantity, [\"dark_store\", \"item_id\", \"front_end_fac\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(quantity_full, [\"dark_store\", \"item_id\", \"front_end_fac\"], \"quantity\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full[\n", "    (quantity_full.dark_store == 849)\n", "    & (quantity_full.item_id == 10000234)\n", "    & (quantity_full.front_end_fac == 15)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = quantity_full[[\"dark_store\", \"item_id\", \"front_end_fac\"]]\n", "x[x.duplicated()].to_csv(\"multiple_buckets.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"rep\"] = (\n", "    final_rep.groupby(\"dark_store\")\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\"item_id\": \"Count of common SKUs which are transferable and required (in Units)\"}\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep, [\"dark_store\", \"front_end_fac\", \"item_id\"], \"final_rep\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator = (\n", "    final_rep.groupby([\"front_end_fac\", \"item_id\", \"net_quantity\", \"avg_cpd_at_frontend\"])\n", "    .agg({\"required_qty\": \"sum\", \"avg_cpd_at_dark_store\": \"sum\"})\n", "    .reset_index()\n", ")  # ...."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator[\"transferable_qty\"] = iterator[\"net_quantity\"] * (\n", "    iterator[\"avg_cpd_at_dark_store\"]\n", "    / (iterator[\"avg_cpd_at_frontend\"] + iterator[\"avg_cpd_at_dark_store\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator = iterator.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator[\"transferable_qty\"] = iterator[\"transferable_qty\"].apply(math.floor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_ideal = iterator[iterator.transferable_qty >= iterator.required_qty]\n", "iterator_ideal[\"logic_bucket\"] = \"Ideal\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal = iterator[iterator.transferable_qty < iterator.required_qty]\n", "iterator_nonideal[\"logic_bucket\"] = \"Not Ideal\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal = pd.merge(\n", "    iterator_nonideal[[\"front_end_fac\", \"item_id\", \"transferable_qty\"]],\n", "    final_rep,\n", "    how=\"left\",\n", "    left_on=[\"front_end_fac\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_ideal = pd.merge(\n", "    iterator_ideal[[\"front_end_fac\", \"item_id\", \"transferable_qty\"]],\n", "    final_rep,\n", "    how=\"left\",\n", "    left_on=[\"front_end_fac\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_ideal[\"indent\"] = iterator_ideal[\"required_qty\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal_total = (\n", "    iterator_nonideal.groupby([\"front_end_fac\", \"item_id\"])\n", "    .agg({\"required_qty\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"required_qty\": \"total_required_qty\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal = pd.merge(\n", "    iterator_nonideal,\n", "    iterator_nonideal_total,\n", "    on=[\"front_end_fac\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal[\"indent_ratio\"] = (\n", "    iterator_nonideal[\"required_qty\"] / iterator_nonideal[\"total_required_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal[\"indent\"] = (\n", "    iterator_nonideal[\"indent_ratio\"] * iterator_nonideal[\"transferable_qty\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ref = {}\n", "\n", "for fac in list(iterator_nonideal[\"front_end_fac\"].unique()):\n", "    ref[int(fac)] = {}\n", "    items_in_fac_i = list(\n", "        iterator_nonideal[iterator_nonideal.front_end_fac == fac][\"item_id\"].unique()\n", "    )\n", "    items_in_fac_i = map(int, items_in_fac_i)\n", "    for item in items_in_fac_i:\n", "        _filter = iterator_nonideal[\n", "            (iterator_nonideal.item_id == item) & (iterator_nonideal.front_end_fac == fac)\n", "        ]\n", "        transferable = int(_filter[\"transferable_qty\"].max())\n", "        details = (\n", "            _filter[[\"dark_store\", \"required_qty\", \"existing_doi_at_darkstore\"]]\n", "            .sort_values(\n", "                by=[\"existing_doi_at_darkstore\", \"required_qty\"],\n", "                ascending=[True, False],\n", "            )\n", "            .to_dict(\"records\")\n", "        )\n", "        ref[int(fac)][int(item)] = {\"transferable\": transferable, \"details\": details}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ref2 = []\n", "for fac, v in ref.items():\n", "    for item, v1 in v.items():\n", "        tf = v1[\"transferable\"]\n", "        df = v1[\"details\"]\n", "        for i, dc in enumerate(df):\n", "            req = dc[\"required_qty\"]\n", "            tf1 = tf\n", "            if tf <= req and tf >= 0:\n", "                indent = tf\n", "                tf = 0\n", "            elif tf > req:\n", "                indent = req\n", "                tf -= req\n", "            else:\n", "                indent = 0\n", "                tf = 0\n", "            ref[fac][item][\"details\"][i][\"indent\"] = indent\n", "            ref2.append({\"item_id\": item, \"dark_store\": dc[\"dark_store\"], \"indent\": indent})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dev_logs = {}\n", "dev_logs[\"non_ideal_iterator_allocation_logs\"] = ref"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal = pd.merge(\n", "    iterator_nonideal.drop([\"indent\"], axis=1),\n", "    pd.<PERSON><PERSON><PERSON><PERSON>(ref2),\n", "    on=[\"dark_store\", \"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["rank_list = list(iterator_nonideal_hl['rank'].unique())#[(iterator_nonideal_hl['front_end_fac']==29)&(iterator_nonideal_hl['item_id']==10003853)]"]}, {"cell_type": "raw", "metadata": {}, "source": ["rank_list.sort()\n", "rank_list"]}, {"cell_type": "raw", "metadata": {}, "source": ["for i in rank_list:\n", "    iterator_nonideal_hl['indent_new'] = np.where((iterator_nonideal_hl['rank']==i)&(iterator_nonideal_hl['required_qty']<=iterator_nonideal_hl['transferable_qty']),\n", "                                                  iterator_nonideal_hl['required_qty'],\n", "                                                  iterator_nonideal_hl['transferable_qty'])\n", "    iterator_nonideal_hl['transferable_qty'] = "]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal[\"indent\"] = iterator_nonideal[\"indent\"].apply(math.floor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"stock_available_but_not_transferable\"] = iterator_nonideal[\n", "    iterator_nonideal.indent < 1\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iterator_nonideal = iterator_nonideal[iterator_nonideal.indent >= 1]"]}, {"cell_type": "raw", "metadata": {}, "source": ["mapping_sheet_priority = mapping_sheet[[\"dark_store\", \"priority\"]].drop_duplicates()"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal = pd.merge(\n", "    iterator_nonideal,\n", "    mapping_sheet_priority,\n", "    how=\"left\",\n", "    left_on=[\"dark_store\"],\n", "    right_on=[\"dark_store\"],\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["unique_priorities = list(iterator_nonideal.priority.unique())"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v1 = iterator_nonideal_hl[\n", "    [\"front_end_fac\", \"item_id\", \"dark_store\", \"required_qty\", \"rank\"]\n", "]"]}, {"cell_type": "raw", "metadata": {}, "source": ["transferable = iterator_nonideal_hl[[\"front_end_fac\", \"item_id\", \"transferable_qty\"]]"]}, {"cell_type": "raw", "metadata": {}, "source": ["post_dist_transferable = deepcopy(transferable)"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v1[\"indent\"] = 0"]}, {"cell_type": "raw", "metadata": {}, "source": ["logic = (\n", "    iterator_nonideal_v1.groupby([\"front_end_fac\", \"rank\", \"dark_store\"])\n", "    .min()\n", "    .reset_index()[[\"front_end_fac\", \"rank\", \"dark_store\"]]\n", "    .groupby([\"front_end_fac\", \"rank\"])\n", "    .count()\n", "    .reset_index()\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["logic"]}, {"cell_type": "raw", "metadata": {}, "source": ["logicdf = logic[logic.dark_store > 1].rename(columns={\"dark_store\": \"occ\"})"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11 = pd.merge(\n", "    iterator_nonideal_v1, logicdf, on=[\"front_end_fac\", \"rank\"], how=\"left\"\n", ") "]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_nonrepeating = iterator_nonideal_v11[\n", "    iterator_nonideal_v11.occ.isna()\n", "]"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_repeating = iterator_nonideal_v11[\n", "    ~iterator_nonideal_v11.occ.isna()\n", "]"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_repeating = pd.merge(\n", "    iterator_nonideal_v11_repeating,\n", "    post_dist_transferable.drop_duplicates(),\n", "    how=\"left\",\n", "    left_on=[\"front_end_fac\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"item_id\"],\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_repeating[\"indent\"] = (\n", "    iterator_nonideal_v11_repeating[\"transferable_qty\"]\n", "    / iterator_nonideal_v11_repeating[\"occ\"]\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_repeating[\"indent\"] = iterator_nonideal_v11_repeating[\n", "    \"indent\"\n", "].apply(math.floor)"]}, {"cell_type": "raw", "metadata": {}, "source": ["def priority_allocation(iterator_nonideal_v1, transferable):\n", "    for i in rank_list:\n", "        transferable = transferable.groupby([\"front_end_fac\", \"item_id\"]).agg(\n", "            {\"transferable_qty\": min}\n", "        )\n", "        iterator_nonideal_v1 = pd.merge(\n", "            iterator_nonideal_v1,\n", "            transferable,\n", "            how=\"left\",\n", "            left_on=[\"front_end_fac\", \"item_id\"],\n", "            right_on=[\"front_end_fac\", \"item_id\"],\n", "        )\n", "        iterator_nonideal_v1[\"indent\"] = np.where(\n", "            (iterator_nonideal_v1.rank == i)\n", "            & (iterator_nonideal_v1.transferable_qty > 0)\n", "            & (\n", "                iterator_nonideal_v1.transferable_qty\n", "                >= iterator_nonideal_v1.required_qty\n", "            ),\n", "            iterator_nonideal_v1[\"required_qty\"],\n", "            iterator_nonideal_v1[\"indent\"],\n", "        )\n", "        iterator_nonideal_v1[\"indent\"] = np.where(\n", "            (iterator_nonideal_v1.rank == i)\n", "            & (iterator_nonideal_v1.transferable_qty > 0)\n", "            & (\n", "                iterator_nonideal_v1.transferable_qty\n", "                < iterator_nonideal_v1.required_qty\n", "            ),\n", "            iterator_nonideal_v1[\"transferable_qty\"],\n", "            iterator_nonideal_v1[\"indent\"],\n", "        )\n", "        iterator_nonideal_v1[\"transferable_qty\"] = (\n", "            iterator_nonideal_v1[\"transferable_qty\"] - iterator_nonideal_v1[\"indent\"]\n", "        )\n", "        transferable = iterator_nonideal_v1[iterator_nonideal_v1.rank == i][\n", "            [\"front_end_fac\", \"item_id\", \"transferable_qty\"]\n", "        ]\n", "        iterator_nonideal_v1 = iterator_nonideal_v1.drop(\"transferable_qty\", axis=1)\n", "    return iterator_nonideal_v1"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_nonrepeating = priority_allocation(\n", "    iterator_nonideal_v11_nonrepeating, transferable\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["required_cols = [\n", "    \"front_end_fac\",\n", "    \"item_id\",\n", "    \"dark_store\",\n", "    \"required_qty\",\n", "    \"priority\",\n", "    \"indent\",\n", "]"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_repeating = iterator_nonideal_v11_repeating[required_cols]"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v11_nonrepeating = iterator_nonideal_v11_nonrepeating[required_cols]"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v1 = pd.concat(\n", "    [iterator_nonideal_v11_repeating, iterator_nonideal_v11_nonrepeating]\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal_v1 = pd.merge(\n", "    iterator_nonideal_v1,\n", "    post_dist_transferable.drop_duplicates(),\n", "    how=\"left\",\n", "    left_on=[\"front_end_fac\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"item_id\"],\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["if len(iterator_nonideal_v1) > 0:\n", "    stock_out_reasons[\"indent_zero_due_to_low_priority\"] = iterator_nonideal_v1[\n", "        iterator_nonideal_v1.indent <= 0\n", "    ]"]}, {"cell_type": "raw", "metadata": {}, "source": ["iterator_nonideal = iterator_nonideal_v1.drop(\"priority\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity = quantity.rename(\n", "    columns={\n", "        \"available_quantity\": \"available_quantity_at_frontend\",\n", "        \"blocked_quantity\": \"blocked_quantity_at_frontend\",\n", "        \"doi_to_reserve\": \"doi_to_reserve_at_frontend\",\n", "        \"cpd_list\": \"cpd_list_at_frontend\",\n", "        \"total_quantity_preserved\": \"total_quantity_preserved_at_frontend\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_full = quantity_full.rename(\n", "    columns={\n", "        \"available_quantity\": \"available_quantity_at_darkstore\",\n", "        \"blocked_quantity\": \"blocked_quantity_at_darkstore\",\n", "        \"doi\": \"threshold_doi\",\n", "        \"recieving_quantity\": \"intransit_quantity\",\n", "        \"cpd_list\": \"cpd_list_at_dark_store\",\n", "        \"threshold_qty\": \"threshold_qty_at_darkstore\",\n", "        \"required_qty\": \"required_qty_at_darkstore\",\n", "    }\n", ")\n", "\n", "quantity_full.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"required\"] = (\n", "    quantity_full.groupby(\"dark_store\")\n", "    .agg({\"item_id\": \"count\", \"required_qty_at_darkstore\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of SKUs required (in Units)\",\n", "            \"required_qty_at_darkstore\": \"Required quantity (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep = pd.merge(\n", "    quantity,\n", "    quantity_full,\n", "    how=\"inner\",\n", "    left_on=[\"front_end_fac\", \"dark_store\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"dark_store\", \"item_id\"],\n", "    suffixes=[\"_frontend\", \"_darkstore\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep, [\"dark_store\", \"front_end_fac\", \"item_id\"], \"final_rep\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v1 = pd.concat([iterator_ideal, iterator_nonideal])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transferable_csv = deepcopy(\n", "    final_rep_v1[\n", "        [\n", "            \"dark_store\",\n", "            \"front_end_fac\",\n", "            \"item_id\",\n", "            \"avg_cpd_at_dark_store\",\n", "            \"avg_cpd_at_frontend\",\n", "            \"transferable_qty\",\n", "        ]\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"transferable\"] = (\n", "    final_rep_v1.groupby(\"dark_store\")\n", "    .agg({\"item_id\": \"count\", \"transferable_qty\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of transferable SKUs at catering facility after allocation logic (in Units)\",\n", "            \"transferable_qty\": \"Total transferable quantity at catering facility after allocation logic (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"transferable\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# marked\n", "final_rep_v2 = pd.merge(\n", "    final_rep,\n", "    final_rep_v1[[\"dark_store\", \"front_end_fac\", \"item_id\", \"transferable_qty\", \"indent\"]],\n", "    how=\"inner\",\n", "    left_on=[\"front_end_fac\", \"dark_store\", \"item_id\"],\n", "    right_on=[\"front_end_fac\", \"dark_store\", \"item_id\"],\n", "    suffixes=[\"_frontend\", \"_darkstore\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2,\n", "    case_size_df,\n", "    how=\"left\",\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"case_ratio\"] = final_rep_v2[\"indent\"] / final_rep_v2[\"outer_case_size\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"case_error\"] = np.where(\n", "    (final_rep_v2[\"case_ratio\"] > 0.6) | (final_rep_v2[\"case_sensitive\"] == 1), 0, 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"rounded_case_ratio\"] = final_rep_v2[\"case_ratio\"].apply(math.ceil)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_rep_v2[['front_end_fac','item_id','transferable_qty','indent','final_indent']]"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2[\"boost_case_size\"] = np.where(\n", "    (final_rep_v2[\"outer_case_size\"] <= final_rep_v2[\"outer_case_size_min_frontend\"])\n", "    & (final_rep_v2[\"rounded_case_ratio\"] < 1)\n", "    & (final_rep_v2[\"outer_case_size\"] <= final_rep_v2[\"transferable_qty\"]),\n", "    1,\n", "    0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"final_indent\"] = np.where(\n", "    (final_rep_v2[\"case_error\"] == 1),\n", "    final_rep_v2[\"indent\"],\n", "    final_rep_v2[\"rounded_case_ratio\"] * final_rep_v2[\"outer_case_size\"],\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2[\"final_indent\"] = np.where(\n", "    (final_rep_v2[\"boost_case_size\"] == 1),\n", "    final_rep_v2[\"outer_case_size\"],\n", "    final_rep_v2[\"final_indent\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stop here"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"after_case\"] = (\n", "    quantity_full.groupby(\"dark_store\")\n", "    .agg({\"required_qty_at_darkstore\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\"required_qty_at_darkstore\": \"Required quantity after case calculation (in Units)\"}\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"after_case\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# MOQ"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2[\"final_indent\"] = np.where(\n", "    (final_rep_v2[\"transferable_qty\"] >= final_rep_v2[\"moq_frontend\"])\n", "    & (final_rep_v2[\"final_indent\"] < final_rep_v2[\"moq_frontend\"]),\n", "    final_rep_v2[\"moq_frontend\"],\n", "    final_rep_v2[\"final_indent\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"net_available_qty_ds\"] = (\n", "    final_rep_v2[\"available_quantity_at_darkstore\"] - final_rep_v2[\"blocked_quantity_at_darkstore\"]\n", ")\n", "final_rep_v2[\"net_available_qty_ds\"] = np.where(\n", "    final_rep_v2[\"net_available_qty_ds\"] < 0, 0, final_rep_v2[\"net_available_qty_ds\"]\n", ")\n", "final_rep_v2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.columns  # here11"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ref_dict = {}\n", "for fac in final_rep_v2.front_end_fac.unique():\n", "    ref_dict[fac] = {}\n", "    items_i = final_rep_v2[final_rep_v2.front_end_fac == fac][\"item_id\"].unique()\n", "    for item in items_i:\n", "        ref_dict[fac][item] = {}\n", "        details = final_rep_v2[(final_rep_v2.front_end_fac == fac) & (final_rep_v2.item_id == item)]\n", "        ref_dict[fac][item][\"transferable_qty\"] = (\n", "            details[[\"item_id\", \"front_end_fac\", \"transferable_qty\"]]\n", "            .drop_duplicates()\n", "            .transferable_qty.max()\n", "        )\n", "        ref_dict[fac][item][\"details\"] = (\n", "            details[\n", "                [\n", "                    \"dark_store\",\n", "                    \"net_available_qty_ds\",\n", "                    \"final_indent\",\n", "                    \"moq_frontend\",\n", "                    \"is_darkstore\",\n", "                    \"existing_doi_at_darkstore\",\n", "                    \"avg_cpd_at_dark_store\",\n", "                ]\n", "            ]\n", "            .sort_values(\n", "                by=[\"existing_doi_at_darkstore\", \"avg_cpd_at_dark_store\"],\n", "                ascending=[True, False],\n", "            )\n", "            .to_dict(\"records\")\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["records_l = []\n", "for facility, v in ref_dict.items():\n", "    for item_i, v_i in v.items():\n", "        tfbl = v_i[\"transferable_qty\"]\n", "        details = v_i[\"details\"]\n", "        for i, outlet_i in enumerate(details):\n", "            # This block is for DS\n", "            if outlet_i[\"is_darkstore\"] == 1:\n", "                if outlet_i[\"final_indent\"] < outlet_i[\"moq_frontend\"]:\n", "                    if outlet_i[\"net_available_qty_ds\"] > 0:\n", "                        indent = 0\n", "                    else:\n", "                        if tfbl >= outlet_i[\"moq_frontend\"]:\n", "                            indent = outlet_i[\"moq_frontend\"]\n", "                            tfbl -= outlet_i[\"moq_frontend\"]\n", "                        else:\n", "                            indent = 0\n", "                            tfbl = 0\n", "                else:\n", "                    if tfbl >= outlet_i[\"final_indent\"]:\n", "                        indent = outlet_i[\"final_indent\"]\n", "                        tfbl -= outlet_i[\"final_indent\"]\n", "                    else:\n", "                        indent = 0\n", "                        tfbl = 0\n", "            # This block is for FS\n", "            else:\n", "                if outlet_i[\"final_indent\"] < outlet_i[\"moq_frontend\"]:\n", "                    if tfbl >= outlet_i[\"moq_frontend\"]:\n", "                        indent = outlet_i[\"moq_frontend\"]\n", "                        tfbl -= outlet_i[\"moq_frontend\"]\n", "                    else:\n", "                        indent = 0\n", "                        tfbl = 0\n", "                else:\n", "                    if tfbl >= outlet_i[\"final_indent\"]:\n", "                        indent = outlet_i[\"final_indent\"]\n", "                        tfbl -= outlet_i[\"final_indent\"]\n", "                    else:\n", "                        indent = 0\n", "                        tfbl = 0\n", "            ref_dict[facility][item_i][\"details\"][i][\"new_indent\"] = indent\n", "            records_l.append(\n", "                {\n", "                    \"dark_store\": outlet_i[\"dark_store\"],\n", "                    \"item_id\": item_i,\n", "                    \"final_indent\": indent,\n", "                }\n", "            )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dev_logs[\"moq_allocation_log\"] = ref_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2.drop([\"final_indent\"], axis=1),\n", "    pd.DataFrame(records_l),\n", "    how=\"left\",\n", "    on=[\"dark_store\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[(final_rep_v2.front_end_fac == 48) & (final_rep_v2.item_id.isin([10000388]))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"less_than_moq\"] = final_rep_v2[final_rep_v2.final_indent < 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2[final_rep_v2.final_indent >= 1]"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2[\"final_indent\"] = np.where(\n", "    (final_rep_v2[\"transferable_qty\"] < final_rep_v2[\"moq_frontend\"])\n", "    & (final_rep_v2[\"final_indent\"] < final_rep_v2[\"moq_frontend\"]),\n", "    0,\n", "    final_rep_v2[\"final_indent\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def case_flag(row):\n", "    if (\n", "        int(row[\"final_indent\"] / row[\"outer_case_size\"])\n", "        == row[\"final_indent\"] / row[\"outer_case_size\"]\n", "    ):\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"case_flag\"] = final_rep_v2.apply(case_flag, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"avg_cpd_at_darkstore\"] = final_rep_v2[\"cpd_list_at_dark_store\"].apply(np.mean)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"available_doi_at_darkstore\"] = (\n", "    final_rep_v2[\"available_quantity_at_darkstore\"] - final_rep_v2[\"blocked_quantity_at_darkstore\"]\n", ") / final_rep_v2[\"avg_cpd_at_darkstore\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v4 = deepcopy(final_rep_v2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"moq\"] = (\n", "    final_rep_v2.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\", \"final_indent\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of SKUs after MOQ logic (in Units)\",\n", "            \"final_indent\": \"Total indent after MOQ logic (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"moq\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_wts = \"\"\"\n", "with h as (select distinct r.item_id,\n", "    r.weight_in_gm as weight,\n", "    case when r.storage_type = 5 then 1 else 0 end as heavy_storage\n", "    from rpc_product_product r\n", "    inner join (select item_id,max(created_at) as dated from rpc_product_product\n", "    group by 1) a on a.item_id=r.item_id\n", "    where r.created_at=a.dated\n", "and active = 1 and r.item_id in ({items}) )\n", "\n", "select * from h \n", "\"\"\".format(\n", "    items=items\n", ")\n", "item_wts = pd.read_sql_query(sql=item_wts, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(item_wts, [\"item_id\"], \"final_rep\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2, item_wts, how=\"left\", left_on=[\"item_id\"], right_on=[\"item_id\"]\n", ")\n", "final_rep_v2[\"weight_unit\"] = final_rep_v2[\"weight\"].fillna(final_rep_v2[\"weight\"].mean())\n", "final_rep_v2[\"weight\"] = final_rep_v2[\"weight_unit\"] * final_rep_v2[\"final_indent\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["truck_load_size = mapping_sheet[[\"dark_store\", \"truck_load_size\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["truck_load_size[\"truck_load_size\"] = truck_load_size[\"truck_load_size\"] * 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_capacities = mapping_sheet[\n", "    [\n", "        \"dark_store\",\n", "        \"picking_capacity\",\n", "        \"distinct_skus\",\n", "        \"heavy_storage\",\n", "        \"regular_storage\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2,\n", "    truck_load_size,\n", "    on=[\"dark_store\"],\n", "    how=\"inner\",\n", "    suffixes=[\"\", \"_capacity\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### \\<Control overweight>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"final_indent\"] = final_rep_v2[\"final_indent\"].apply(math.ceil)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"weight\"] = final_rep_v2[\"weight_unit\"] * final_rep_v2[\"final_indent\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"weight_of_available_quantity_at_darkstore\"] = (\n", "    final_rep_v2[\"weight_unit\"] * final_rep_v2[\"available_quantity_at_darkstore\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep_v2, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"final_rep\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### \\<Control overweight>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2,\n", "    storage_capacities,\n", "    on=[\"dark_store\"],\n", "    how=\"inner\",\n", "    suffixes=[\"\", \"_capacity\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"indent_doi\"] = final_rep_v2[\"final_indent\"] / final_rep_v2[\"avg_cpd_at_darkstore\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_ext = \"\"\"\n", "WITH inventory as\n", "  (SELECT outlet_id,item_id,sum(quantity) AS quantity\n", "   FROM\n", "     (SELECT good_inv.outlet_id AS outlet_id, product.item_id, sum(CASE\n", "                                                                       WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity\n", "                                                                       ELSE 0\n", "                                                                   END) AS quantity, 0 AS blocked_quantity\n", "      FROM lake_ims.ims_good_inventory good_inv\n", "      INNER JOIN lake_rpc.product_product product ON product.variant_id = good_inv.variant_id\n", "      WHERE good_inv.active = 1\n", "        AND good_inv.outlet_id IN ({{outlets}})\n", "      GROUP BY 1, 2\n", "      UNION SELECT a.outlet_id, a.item_id, a.quantity, b.quantity AS blocked_quantity\n", "      FROM\n", "        (SELECT outlet_id, item_id, quantity\n", "         FROM lake_ims.ims_item_inventory\n", "         WHERE outlet_id IN ({{outlets}})\n", "           AND active = 1) a\n", "      LEFT JOIN\n", "        (SELECT item_id, outlet_id, sum(quantity) AS quantity\n", "         FROM lake_ims.ims_item_blocked_inventory\n", "         WHERE outlet_id IN ({{outlets}})\n", "           AND active = 1\n", "           AND blocked_type IN (1,2,5)-- blocked for sto and orders\n", "\n", "         GROUP BY 1,2) b ON a.item_id = b.item_id\n", "      AND a.outlet_id = b.outlet_id)\n", "   GROUP BY 1,2),\n", "               \n", "               \n", "wts AS\n", "  (SELECT DISTINCT r.item_id,\n", "                   r.weight_in_gm AS weight,\n", "                   CASE\n", "                       WHEN r.storage_type = 5 THEN 1\n", "                       ELSE 0\n", "                   END AS heavy_storage\n", "   FROM rpc_product_product r\n", "   INNER JOIN\n", "     (SELECT item_id,\n", "             max(created_at) AS dated\n", "      FROM rpc_product_product\n", "      GROUP BY 1) a ON a.item_id=r.item_id\n", "   WHERE r.created_at=a.dated\n", "     AND active = 1 ),\n", "     \n", "     \n", "FINAL AS (\n", "SELECT i.*,w.weight,w.heavy_storage,i.quantity*w.weight as total_wt\n", "FROM inventory i\n", "JOIN wts w ON i.item_id = w.item_id)\n", "\n", "select outlet_id as dark_store,\n", "sum(case when heavy_storage = 1 then quantity end) as heavy_existing_qty,\n", "sum(case when heavy_storage = 0 then quantity end) as regular_existing_qty,\n", "sum(case when heavy_storage = 0 then total_wt end) as regular_existing_wt,\n", "sum(case when heavy_storage = 1 then total_wt end) as heavy_existing_wt\n", "from FINAL group by 1\n", "\n", "\n", "\"\"\".replace(\n", "    \"{{\", \"{\"\n", ").replace(\n", "    \"}}\", \"}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantity_ext = pd.read_sql_query(\n", "    sql=quantity_ext.format(outlets=dark_stores),\n", "    con=CON_REDSHIFT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2,\n", "    quantity_ext,\n", "    on=[\"dark_store\"],\n", "    how=\"left\",\n", "    suffixes=[\"\", \"_capacity\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2.fillna(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["ggg = final_rep_v2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep_v2, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"final_rep\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["final_rep_v2 = ggg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.cpd_list_at_frontend = final_rep_v2.cpd_list_at_frontend.apply(json.dumps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.cpd_list_at_dark_store = final_rep_v2.cpd_list_at_dark_store.apply(json.dumps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.distinct_skus.dtype"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lis = []\n", "picking_capacity_issue = []\n", "for store in dark_stores_list:\n", "    temp_df = final_rep_v2[final_rep_v2.dark_store == store]\n", "    temp_df = temp_df.sort_values(\n", "        [\"rank_darkstore\", \"final_indent\", \"available_doi_at_darkstore\", \"indent_doi\"],\n", "        ascending=True,\n", "    ).reset_index(drop=True)\n", "    temp_df[\"cum_indent\"] = 1\n", "    temp_df[\"cum_indent\"] = temp_df[\"cum_indent\"].cumsum()\n", "    temp_df_issue = temp_df[temp_df.cum_indent > temp_df.distinct_skus]\n", "    temp_df = temp_df[temp_df.cum_indent <= temp_df.distinct_skus]\n", "    min_cum_indent = float(temp_df_issue.cum_indent.min())\n", "    max_cum_indent = float(temp_df.cum_indent.max())\n", "    if min_cum_indent is not np.nan and max_cum_indent is not np.nan:\n", "        min_cum_indent_util = temp_df_issue[temp_df_issue.cum_indent == min_cum_indent]\n", "        min_cum_indent_util[\"final_indent\"] = min_cum_indent_util[\"distinct_skus\"] - max_cum_indent\n", "        temp_df = pd.concat([temp_df, min_cum_indent_util])\n", "        # print(min_cum_indent_util)\n", "    picking_capacity_issue.append(temp_df_issue)\n", "    lis.append(temp_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"limited_distinct_skus__picking_capacity_issue\"] = pd.concat(\n", "    picking_capacity_issue\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.concat(lis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"picking1\"] = (\n", "    final_rep_v2.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\", \"final_indent\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of SKUs after limiting distinct SKUs (in Units)\",\n", "            \"final_indent\": \"Total indent after limiting distinct SKUs (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lis = []\n", "picking_capacity_issue = []\n", "for store in dark_stores_list:\n", "    temp_df = final_rep_v2[final_rep_v2.dark_store == store]\n", "    temp_df = temp_df.sort_values(\n", "        [\"rank_darkstore\", \"final_indent\", \"available_doi_at_darkstore\", \"indent_doi\"],\n", "        ascending=True,\n", "    ).reset_index(drop=True)\n", "    temp_df[\"cum_indent\"] = temp_df[\"final_indent\"].cumsum()\n", "    temp_df_issue = temp_df[temp_df.cum_indent > temp_df.picking_capacity]\n", "    temp_df = temp_df[temp_df.cum_indent <= temp_df.picking_capacity]\n", "    min_cum_indent = float(temp_df_issue.cum_indent.min())\n", "    max_cum_indent = float(temp_df.cum_indent.max())\n", "    if min_cum_indent is not np.nan and max_cum_indent is not np.nan:\n", "        min_cum_indent_util = temp_df_issue[temp_df_issue.cum_indent == min_cum_indent]\n", "        min_cum_indent_util[\"final_indent\"] = (\n", "            min_cum_indent_util[\"picking_capacity\"] - max_cum_indent\n", "        )\n", "        temp_df = pd.concat([temp_df, min_cum_indent_util])\n", "        # print(min_cum_indent_util)\n", "    picking_capacity_issue.append(temp_df_issue)\n", "    lis.append(temp_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"picking_capacity_issue\"] = pd.concat(picking_capacity_issue)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.concat(lis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"picking2\"] = (\n", "    final_rep_v2.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\", \"final_indent\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of SKUs after limiting picking quantity (in Units)\",\n", "            \"final_indent\": \"Total indent after limiting picking quantity (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lis = []\n", "truck_load_size_issue = []\n", "for store in dark_stores_list:\n", "    temp_df = final_rep_v2[final_rep_v2.dark_store == store]\n", "    temp_df = temp_df.sort_values(\n", "        [\"rank_darkstore\", \"weight\", \"available_doi_at_darkstore\", \"indent_doi\"],\n", "        ascending=True,\n", "    ).reset_index(drop=True)\n", "    temp_df[\"cum_weight\"] = temp_df[\"weight\"].cumsum()\n", "    temp_df_issue = temp_df[temp_df.cum_weight > temp_df.truck_load_size]\n", "    temp_df = temp_df[temp_df.cum_weight <= temp_df.truck_load_size]\n", "    min_cum_weight = float(temp_df_issue.cum_weight.min())\n", "    max_cum_weight = float(temp_df.cum_weight.max())\n", "    # if min_cum_weight is not np.nan and max_cum_weight is not np.nan:\n", "\n", "    truck_load_size_issue.append(temp_df_issue)\n", "    lis.append(temp_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"truck_load_size_issue\"] = pd.concat(truck_load_size_issue)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.concat(lis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"truck\"] = (\n", "    final_rep_v2.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\", \"final_indent\": \"sum\", \"weight\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of SKUs after trimming for truck (in Units)\",\n", "            \"final_indent\": \"Total indent after trimming for truck (in Units)\",\n", "            \"weight\": \"Total weight after trimming for truck (in Grams)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2_heavy = final_rep_v2[final_rep_v2.heavy_storage == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2_regular = final_rep_v2[final_rep_v2.heavy_storage != 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2_heavy[\"heavy_storage_capacity\"] = final_rep_v2_heavy[\"heavy_storage_capacity\"] * 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lis = []\n", "storage_issue_list = []\n", "for store in dark_stores_list:\n", "    temp_df = final_rep_v2_heavy[final_rep_v2_heavy.dark_store == store]\n", "    temp_df = temp_df.sort_values(\n", "        [\"rank_darkstore\", \"weight\", \"available_doi_at_darkstore\", \"indent_doi\"],\n", "        ascending=True,\n", "    ).reset_index(drop=True)\n", "    temp_df[\"cum_indent\"] = temp_df[\"final_indent\"]\n", "    temp_df[\"cum_indent\"] = temp_df[\"cum_indent\"].cumsum()\n", "    temp_df[\"cum_weight\"] = temp_df[\"weight\"]\n", "    temp_df[\"cum_weight\"] = temp_df[\"cum_weight\"].cumsum()\n", "    temp_df[\"cum_weight\"] = temp_df[\"cum_weight\"] + temp_df[\"heavy_existing_wt\"]\n", "    temp_df_issue = temp_df[temp_df.cum_weight > temp_df.heavy_storage_capacity]\n", "    temp_df = temp_df[temp_df.cum_weight <= temp_df.heavy_storage_capacity]\n", "    min_cum_weight = float(temp_df_issue.cum_weight.min())\n", "    storage_issue_list.append(temp_df_issue)\n", "    lis.append(temp_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"heavy_storage_issue\"] = pd.concat(storage_issue_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2_heavy = pd.concat(lis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"heavy_storage_issue\"] = (\n", "    final_rep_v2_heavy.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\", \"final_indent\": \"sum\", \"weight\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of heavy SKUs after trimming for heavy storage (in Units)\",\n", "            \"final_indent\": \"Total heavy items indent after trimming for heavy storage (in Units)\",\n", "            \"weight\": \"Total weight of heavy items after trimming for heavy storage (in Grams)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lis = []\n", "storage_issue_list = []\n", "for store in dark_stores_list:\n", "    temp_df = final_rep_v2_regular[final_rep_v2_regular.dark_store == store]\n", "    temp_df = temp_df.sort_values(\n", "        [\"rank_darkstore\", \"weight\", \"available_doi_at_darkstore\", \"indent_doi\"],\n", "        ascending=True,\n", "    ).reset_index(drop=True)\n", "    temp_df[\"cum_indent\"] = temp_df[\"final_indent\"].cumsum()\n", "    temp_df[\"cum_indent\"] = temp_df[\"cum_indent\"] + temp_df[\"regular_existing_qty\"]\n", "    temp_df_issue = temp_df[temp_df.cum_indent > temp_df.regular_storage]\n", "    temp_df = temp_df[temp_df.cum_indent <= temp_df.regular_storage]\n", "    min_cum_indent = float(temp_df_issue.cum_indent.min())\n", "    max_cum_indent = float(temp_df.cum_indent.max())\n", "    if min_cum_indent is not np.nan and max_cum_indent is not np.nan:\n", "        min_cum_indent_util = temp_df_issue[temp_df_issue.cum_indent == min_cum_indent]\n", "        min_cum_indent_util[\"final_indent\"] = (\n", "            min_cum_indent_util[\"regular_storage\"] - max_cum_indent\n", "        )\n", "        temp_df = pd.concat([temp_df, min_cum_indent_util])\n", "    storage_issue_list.append(temp_df_issue)\n", "    lis.append(temp_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"regular_storage_issue\"] = pd.concat(storage_issue_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2_regular = pd.concat(lis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"regular_storage_issue\"] = (\n", "    final_rep_v2_regular.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\", \"final_indent\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of regular SKUs after trimming for regular storage (in Units)\",\n", "            \"final_indent\": \"Total regular items indent after trimming for regular storage (in Units)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.concat([final_rep_v2_heavy, final_rep_v2_regular])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_rep_v2_regular[['item_id','final_indent']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"storage_issue\"] = (\n", "    final_rep_v2.groupby([\"dark_store\"])\n", "    .agg({\"item_id\": \"count\", \"final_indent\": \"sum\", \"weight\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of SKUs after trimming for storage (in Units)\",\n", "            \"final_indent\": \"Total indent after trimming for storage (in Units)\",\n", "            \"weight\": \"Total weight after trimming for storage (in Grams)\",\n", "        }\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary[\"storage_issue\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2[\"indent_doi\"] = final_rep_v2[\"final_indent\"] / final_rep_v2[\"avg_cpd_at_darkstore\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2[\n", "    [\n", "        \"front_end_fac\",\n", "        \"item_id\",\n", "        \"dark_store\",\n", "        \"rank_darkstore\",\n", "        \"indent_doi\",\n", "        \"final_indent\",\n", "        \"weight\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v4.cpd_list_at_frontend = final_rep_v4.cpd_list_at_frontend.apply(json.dumps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v4.cpd_list_at_dark_store = final_rep_v4.cpd_list_at_dark_store.apply(json.dumps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v4 = final_rep_v4.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v4,\n", "    final_rep_v2,\n", "    on=[\"dark_store\", \"front_end_fac\", \"item_id\"],\n", "    how=\"left\",\n", "    suffixes=[\"_V1\", \"_V2\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.cpd_list_at_frontend = final_rep_v2.cpd_list_at_frontend.apply(json.dumps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.cpd_list_at_dark_store = final_rep_v2.cpd_list_at_dark_store.apply(json.dumps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2.round(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep_v2, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"final_rep\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = final_rep_v2.groupby([\"dark_store\"]).agg({\"final_indent_V2\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = detailed_ds.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_avg = final_rep_v2[[\"item_id\", \"front_end_fac\", \"transferable_qty\", \"avg_cpd_at_dark_store\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_avg = (\n", "    doi_avg.groupby([\"item_id\", \"front_end_fac\"])\n", "    .agg({\"transferable_qty\": \"max\", \"avg_cpd_at_dark_store\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_avg[\"Median DoI Catering Facility\"] = np.where(\n", "    doi_avg[\"transferable_qty\"] / doi_avg[\"avg_cpd_at_dark_store\"] == np.inf,\n", "    0,\n", "    doi_avg[\"transferable_qty\"] / doi_avg[\"avg_cpd_at_dark_store\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_avg = (\n", "    doi_avg.groupby([\"front_end_fac\"]).agg({\"Median DoI Catering Facility\": \"median\"}).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x in summary.values():\n", "    detailed_ds = pd.merge(detailed_ds, x, on=[\"dark_store\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = pd.merge(detailed_ds, doi_avg, on=[\"front_end_fac\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = detailed_ds.apply(round)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = detailed_ds.rename(\n", "    columns={\n", "        \"dark_store\": \"Dark Store Outlet ID\",\n", "        \"final_indent_V2\": \"Final Indent (in Units)\",\n", "        \"front_end_fac\": \"Catering Facility ID\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["names = \"\"\"select id ,\n", "name from lake_retail.console_outlet\n", "where id in ({dark_store}) or facility_id in ({cat})\"\"\".format(\n", "    dark_store=dark_stores, cat=catering_facilities\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["names = pd.read_sql_query(names, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["names = names.rename(columns={\"id\": \"front_end\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet_bef = mapping_sheet[[\"front_end\", \"dark_store\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet_bef = pd.merge(mapping_sheet_bef, names, on=[\"front_end\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet_bef = mapping_sheet_bef.groupby([\"dark_store\"])[\"name\"].apply(list).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet_bef[\"name\"] = mapping_sheet_bef[\"name\"].map(lambda x: x[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_sheet_bef"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mapping_sheet_bef.name = mapping_sheet_bef.name.apply(\", \".join)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet_bef = mapping_sheet_bef.rename(\n", "    columns={\"name\": \"Name of Catering Outlets\", \"dark_store\": \"Dark Store Outlet ID\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["names = names.rename(columns={\"front_end\": \"Dark Store Outlet ID\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = pd.merge(\n", "    detailed_ds,\n", "    names[[\"Dark Store Outlet ID\", \"name\"]],\n", "    on=[\"Dark Store Outlet ID\"],\n", "    how=\"left\",\n", ").rename(columns={\"name\": \"Name of Dark Store Outlets\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = pd.merge(detailed_ds, mapping_sheet_bef, on=[\"Dark Store Outlet ID\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = detailed_ds.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds = detailed_ds.applymap(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds[\"Destination Outlet\"] = (\n", "    detailed_ds[\"Name of Dark Store Outlets\"] + \" (\" + detailed_ds[\"Dark Store Outlet ID\"] + \")\"\n", ")\n", "detailed_ds[\"Catering Facility\"] = (\n", "    detailed_ds[\"Name of Catering Outlets\"] + \" (\" + detailed_ds[\"Catering Facility ID\"] + \")\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds[\"Catering Facility\"] = (\n", "    detailed_ds[\"Catering Facility\"]\n", "    .str.replace(\"Super Store\", \"\")\n", "    .str.replace(\"\\(SSC\\)\", \"\")\n", "    .str.replace(\"\\(MODI\\) \", \"\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds[\"Destination Outlet\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds[\"Destination Outlet\"] = (\n", "    detailed_ds[\"Destination Outlet\"]\n", "    .str.replace(\" - DarkS \", \"\")\n", "    .str.replace(\"Super Store \", \"\")\n", "    .str.replace(\" - <PERSON><PERSON> \", \"\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["map_names = detailed_ds[[\"Catering Facility\", \"Destination Outlet\", \"Dark Store Outlet ID\"]].rename(\n", "    columns={\n", "        \"Catering Facility\": \"Catering Facility Name\",\n", "        \"Destination Outlet\": \"Destination Outlet Name\",\n", "        \"Dark Store Outlet ID\": \"Destination Outlet\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds1 = detailed_ds[\n", "    [\n", "        \"Destination Outlet\",\n", "        \"Catering Facility\",\n", "        \"Final Indent (in Units)\",\n", "        \"Count of SKUs to be replenished today (in Units)\",\n", "        \"Count of available SKUs at catering facility (in Units)\",\n", "        \"Total quantity available at catering facility (in Units)\",\n", "        \"Count of available SKUs at catering facility after removing expired items (in Units)\",\n", "        \"Total quantity available at catering facility after removing expired items (in Units)\",\n", "        \"Count of SKUs in transit (in Units)\",\n", "        \"Total quantity in transit (in Units)\",\n", "        \"Count of SKUs required (in Units)\",\n", "        \"Required quantity (in Units)\",\n", "        \"Required quantity after case calculation (in Units)\",\n", "        \"Count of common SKUs which are transferable and required (in Units)\",\n", "        \"Count of transferable SKUs at catering facility after allocation logic (in Units)\",\n", "        \"Total transferable quantity at catering facility after allocation logic (in Units)\",\n", "        \"Count of SKUs after MOQ logic (in Units)\",\n", "        \"Total indent after MOQ logic (in Units)\",\n", "        \"Count of SKUs after limiting distinct SKUs (in Units)\",\n", "        \"Total indent after limiting distinct SKUs (in Units)\",\n", "        \"Count of SKUs after limiting picking quantity (in Units)\",\n", "        \"Total indent after limiting picking quantity (in Units)\",\n", "        \"Count of SKUs after trimming for truck (in Units)\",\n", "        \"Total indent after trimming for truck (in Units)\",\n", "        \"Total weight after trimming for truck (in Grams)\",\n", "        \"Count of heavy SKUs after trimming for heavy storage (in Units)\",\n", "        \"Total heavy items indent after trimming for heavy storage (in Units)\",\n", "        \"Total weight of heavy items after trimming for heavy storage (in Grams)\",\n", "        \"Count of regular SKUs after trimming for regular storage (in Units)\",\n", "        \"Total regular items indent after trimming for regular storage (in Units)\",\n", "        \"Count of SKUs after trimming for storage (in Units)\",\n", "        \"Total indent after trimming for storage (in Units)\",\n", "        \"Total weight after trimming for storage (in Grams)\",\n", "        \"Median DoI Catering Facility\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds1 = detailed_ds1.rename(\n", "    columns={\n", "        \"Final Indent (in Units)\": \"Final Indent Qty\",\n", "        \"Count of SKUs to be replenished today (in Units)\": \"# SKUs Assortment\",\n", "        \"Count of available SKUs at catering facility (in Units)\": \"# SKUs Available Catering Facility\",\n", "        \"Count of SKUs in transit (in Units)\": \"# SKUs Open STO\",\n", "        \"Count of SKUs required (in Units)\": \"# SKUs Req\",\n", "        \"Count of common SKUs which are transferable and required (in Units)\": \"# SKUs Required\",\n", "        \"Count of transferable SKUs at catering facility after allocation logic (in Units)\": \"# SKUs After Allocation\",\n", "        \"Count of SKUs after MOQ logic (in Units)\": \"# SKUs Indent > MOQ\",\n", "        \"Count of SKUs after limiting distinct SKUs (in Units)\": \"# SKUs After SKU Limit\",\n", "        \"Count of SKUs after limiting picking quantity (in Units)\": \"# SKUs After Picking Cap Limit\",\n", "        \"Count of SKUs after trimming for truck (in Units)\": \"# SKUs After Truck Load Limit\",\n", "        \"Count of SKUs after trimming for storage (in Units)\": \"# SKUs After Storage Limit\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds1.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_ds1 = detailed_ds1[\n", "    [\n", "        \"Destination Outlet\",\n", "        \"Catering Facility\",\n", "        \"Median DoI Catering Facility\",\n", "        \"Final Indent Qty\",\n", "        \"# SKUs Assortment\",\n", "        \"# SKUs Available Catering Facility\",\n", "        \"# SKUs Open STO\",\n", "        \"# SKUs Required\",\n", "        \"# SKUs After Allocation\",\n", "        \"# SKUs Indent > MOQ\",\n", "        \"# SKUs After SKU Limit\",\n", "        \"# SKUs After Picking Cap Limit\",\n", "        \"# SKUs After Truck Load Limit\",\n", "        \"# SKUs After Storage Limit\",\n", "    ]\n", "]  # .T.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def row_to_index(df):\n", "    df.columns = df.iloc[0]\n", "    return df[1:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_table_summary = \"\"\"\n", "<div class=\"mce-toc\">\n", "  <table style=\"border-collapse: collapse; width: 1000px; \" border=\"1\">\n", "    <tbody>\n", "      <tr style=\"height: 57px;\">\n", "\"\"\"\n", "\n", "for x in detailed_ds1.columns:\n", "    html_table_summary += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "    html_table_summary += x\n", "    html_table_summary += \"\"\"</strong></span></td>\"\"\"\n", "\n", "html_table_summary += \"\"\"</tr>\"\"\"\n", "\n", "for i, r in detailed_ds1.iterrows():\n", "    html_table_summary += \"\"\"<tr style=\"height: 87px;\">\"\"\"\n", "    for x in detailed_ds1.columns:\n", "        if x == \"Destination Outlet\" or x == \"Catering Facility\":\n", "            html_table_summary += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "            html_table_summary += str(r[x])\n", "            html_table_summary += \"\"\"</strong></span></td>\"\"\"\n", "        else:\n", "            html_table_summary += \"\"\"<td style=\"width: 100px; height: 15px; text-align: center;\">\"\"\"\n", "            html_table_summary += str(r[x])\n", "            html_table_summary += \"\"\"</td>\"\"\"\n", "    html_table_summary += \"\"\"</tr>\"\"\"\n", "\n", "html_table_summary += \"\"\"\n", "    </tbody>\n", "  </table>\n", "</div>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uid = str(uuid.uuid1())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class NpEncoder(json.J<PERSON>ncoder):\n", "    def default(self, obj):\n", "        if isinstance(obj, np.integer):\n", "            return int(obj)\n", "        elif isinstance(obj, np.floating):\n", "            return float(obj)\n", "        elif isinstance(obj, np.n<PERSON>ray):\n", "            return obj.tolist()\n", "        else:\n", "            return super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).default(obj)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stringify_keys(d):\n", "    \"\"\"Convert a dict's keys to strings if they are not.\"\"\"\n", "    for key in d.keys():\n", "\n", "        # check inner dict\n", "        if isinstance(d[key], dict):\n", "            value = stringify_keys(d[key])\n", "        else:\n", "            value = d[key]\n", "\n", "        # convert nonstring to string if needed\n", "        if not isinstance(key, str):\n", "            try:\n", "                d[str(key)] = value\n", "            except Exception:\n", "                try:\n", "                    d[repr(key)] = value\n", "                except Exception:\n", "                    raise\n", "\n", "            # delete old key\n", "            del d[key]\n", "    return d\n", "\n", "\n", "dev_logs = stringify_keys(dev_logs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["excel_file = \"/tmp/{today}_missing_items_logs_{uid}.xlsx\".format(today=TODAY, uid=uid)\n", "csv_file = \"/tmp/{today}_{uid}.xlsx\".format(today=TODAY, uid=uid)\n", "excess_csv = \"/tmp/{today}_reverse_sto_{uid}.csv\".format(today=TODAY, uid=uid)\n", "detailed_sum = \"/tmp/{today}_detailed_sum_{uid}.csv\".format(today=TODAY, uid=uid)\n", "transferable = \"/tmp/{today}_transferable_{uid}.csv\".format(today=TODAY, uid=uid)\n", "dev_logs_json = \"/tmp/{today}_dev_logs_{uid}.json\".format(today=TODAY, uid=uid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    j = json.dumps(dev_logs, indent=4, cls=NpEncoder)\n", "except:\n", "    j = json.dumps(stringify_keys(dev_logs), indent=4, cls=NpEncoder)\n", "f = open(dev_logs_json, \"w\")\n", "f.write(j)\n", "f.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2,\n", "    item_attri_df,\n", "    how=\"left\",\n", "    on=[\"item_id\"],\n", "    suffixes=[\"\", \"_attribute\"],\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2['final_indent_v2_cumsum'] = final_rep_v2['final_indent_V2'].cumsum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2_list = []\n", "case_ref = {1: \"_Case_\", 0: \"_Loose_\"}\n", "for i in [1, 0]:\n", "    for store in dark_stores_list:\n", "        temp_case = final_rep_v2[(final_rep_v2.case_flag == i) & (final_rep_v2.dark_store == store)]\n", "        temp_case[\"final_indent_v2_cumsum\"] = temp_case[\"final_indent_V2\"].cumsum()\n", "        temp_case[\"chunk_flag\"] = temp_case[\"final_indent_v2_cumsum\"] / 500\n", "        temp_case[\"chunk_flag\"] = temp_case[\"chunk_flag\"].apply(math.ceil).astype(str)\n", "        temp_case[\"chunk_flag\"] = temp_case[\"chunk_flag\"].map(str) + case_ref[i] + str(store)\n", "        final_rep_v2_list.append(temp_case)\n", "final_rep_v2 = pd.concat(final_rep_v2_list)"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2['chunk_flag'] = final_rep_v2['final_indent_v2_cumsum'] / 500\n", "final_rep_v2['chunk_flag'] = final_rep_v2['chunk_flag'].apply(math.ceil)"]}, {"cell_type": "raw", "metadata": {}, "source": ["max_lim = math.ceil(final_rep_v2['final_indent_v2_cumsum_by_500'].max())"]}, {"cell_type": "raw", "metadata": {}, "source": ["_chunks = []\n", "for i in range(1,max_lim + 1):\n", "    temp_df = final_rep_v2[(final_rep_v2.final_indent_v2_cumsum_by_500 <= i) & (final_rep_v2.final_indent_v2_cumsum_by_500 > i-1)]\n", "    temp_df['chunk_flag'] = i\n", "    _chunks.append(temp_df)\n", "    "]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2 = pd.concat(_chunks)"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2, front_end_outlet_mapping, on=[\"front_end_fac\", \"item_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep_v2, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"final_rep\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = final_rep_v2.groupby([\"item_id\", \"front_end_fac\", \"dark_store\"]).max().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep_v2, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"final_rep\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Total_inventory_v3 = Total_inventory_v3[[\"item_id\", \"outlet_id\", \"facility_id\"]].rename(\n", "    columns={\"facility_id\": \"front_end_fac\", \"outlet_id\": \"front_end_outlet\"}\n", ")  # here0000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v2 = pd.merge(\n", "    final_rep_v2, Total_inventory_v3, on=[\"item_id\", \"front_end_fac\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(final_rep_v2, [\"item_id\", \"front_end_fac\", \"dark_store\"], \"final_rep\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v5 = final_rep_v2[\n", "    [\n", "        \"front_end_fac\",\n", "        \"front_end_outlet\",\n", "        \"dark_store\",\n", "        \"item_id\",\n", "        \"product_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"product_type\",\n", "        \"net_quantity\",\n", "        \"transferable_qty\",\n", "        \"avg_cpd_at_frontend\",\n", "        \"moq_frontend\",\n", "        \"net_available_qty_ds\",\n", "        \"intransit_quantity\",\n", "        \"threshold_doi\",\n", "        \"threshold_qty_at_darkstore\",\n", "        \"cpd_list_at_dark_store\",\n", "        \"avg_cpd_at_dark_store\",\n", "        \"required_qty_at_darkstore\",\n", "        \"existing_doi_at_darkstore\",\n", "        \"final_bucket\",\n", "        \"outer_case_size\",\n", "        \"case_error\",\n", "        \"final_indent_V1\",\n", "        \"weight\",\n", "        \"final_indent_V2\",\n", "        \"chunk_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v5 = final_rep_v5.rename(\n", "    columns={\n", "        \"front_end_fac\": \"Catering Facility\",\n", "        \"front_end_outlet\": \"Catering Outlet\",\n", "        \"dark_store\": \"Destination Outlet\",\n", "        \"item_id\": \"Item ID\",\n", "        \"product_id\": \"Product ID\",\n", "        \"name\": \"Product Name\",\n", "        \"l0\": \"L0\",\n", "        \"product_type\": \"Product Type\",\n", "        \"net_quantity\": \"Available Quantity Catering Facility\",\n", "        \"transferable_qty\": \"Transferable Quantity Catering Facility\",\n", "        \"avg_cpd_at_frontend\": \"Avg. CPD Catering Facility\",\n", "        \"moq_frontend\": \"MOQ\",\n", "        \"net_available_qty_ds\": \"Available Quantity Destination Outlet\",\n", "        \"intransit_quantity\": \"Intransit Quantity\",\n", "        \"threshold_doi\": \"Threshold DOI Destination Outlet\",\n", "        \"threshold_qty_at_darkstore\": \"Threshold Quantity Destination Outlet\",\n", "        \"cpd_list_at_dark_store\": \"CPD List Destination Outlet\",\n", "        \"avg_cpd_at_dark_store\": \"Avg. CPD Destination Outlet\",\n", "        \"required_qty_at_darkstore\": \"Required Quantity Destination Outlet\",\n", "        \"existing_doi_at_darkstore\": \"Existing DOI Destination Outlet\",\n", "        \"final_bucket\": \"Bucket\",\n", "        \"outer_case_size\": \"Outer Case Size\",\n", "        \"case_error\": \"Case Error\",\n", "        \"weight\": \"Weight V2\",\n", "    }\n", ")\n", "# final_rep_v2.dtypes.apply(lambda x: x.name).to_dict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["map_names = map_names.astype({\"Destination Outlet\": int})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# names.rename(columns={\"id\": \"front_end\"})\n", "# herefinal_rep_v5.merge()\n", "final_rep_v5 = final_rep_v5.merge(map_names, on=[\"Destination Outlet\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v5 = final_rep_v5[\n", "    [\n", "        \"Catering Facility\",\n", "        \"Catering Outlet\",\n", "        \"Catering Facility Name\",\n", "        \"Destination Outlet\",\n", "        \"Destination Outlet Name\",\n", "        \"Item ID\",\n", "        \"Product ID\",\n", "        \"Product Name\",\n", "        \"L0\",\n", "        \"Product Type\",\n", "        \"Available Quantity Catering Facility\",\n", "        \"Transferable Quantity Catering Facility\",\n", "        \"Avg. CPD Catering Facility\",\n", "        \"MOQ\",\n", "        \"Available Quantity Destination Outlet\",\n", "        \"Intransit Quantity\",\n", "        \"Threshold DOI Destination Outlet\",\n", "        \"Threshold Quantity Destination Outlet\",\n", "        \"CPD List Destination Outlet\",\n", "        \"Avg. CPD Destination Outlet\",\n", "        \"Required Quantity Destination Outlet\",\n", "        \"Existing DOI Destination Outlet\",\n", "        \"Bucket\",\n", "        \"Outer Case Size\",\n", "        \"Case Error\",\n", "        \"final_indent_V1\",\n", "        \"Weight V2\",\n", "        \"final_indent_V2\",\n", "        \"chunk_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v5.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_rep_v5[\n", "    (final_rep_v5[\"Destination Outlet\"] == 891) & (final_rep_v5[\"Item ID\"].isin([10000388]))\n", "].max()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["k = (\n", "    final_rep_v5.groupby(\n", "        [\n", "            \"Destination Outlet\",\n", "            \"Item ID\",\n", "            \"Catering Facility\",\n", "            \"Transferable Quantity Catering Facility\",\n", "        ]\n", "    )\n", "    .agg({\"final_indent_V2\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["k[k[\"Transferable Quantity Catering Facility\"] < k[\"final_indent_V2\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(k[k[\"Transferable Quantity Catering Facility\"] < k[\"final_indent_V2\"]]) != 0:\n", "    raise Exception(\"Tfbl<indent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_dict = pd.DataFrame(\n", "    [\n", "        {\n", "            \"Column\": \"Available Quantity Catering Facility\",\n", "            \"Description\": \"Available Quantity at the Catering Facility\",\n", "            \"Source\": \"System\",\n", "        },\n", "        {\n", "            \"Column\": \"Transferable Quantity Catering Facility\",\n", "            \"Description\": \"Transferable Quantity at the Catering Facility - Subset of Available Quantity \",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"Avg. CPD Catering Facility\",\n", "            \"Description\": \"Average CPD at the Catering Facility\",\n", "            \"Source\": \"System\",\n", "        },\n", "        {\n", "            \"Column\": \"MOQ\",\n", "            \"Description\": \"Minimum Order Quantity\",\n", "            \"Source\": \"User Defined\",\n", "        },\n", "        {\n", "            \"Column\": \"Available Quantity Destination Outlet\",\n", "            \"Description\": \"Available Quantity at the Destination Outlet\",\n", "            \"Source\": \"System\",\n", "        },\n", "        {\n", "            \"Column\": \"Intransit Quantity\",\n", "            \"Description\": \"Quantity in open STO\",\n", "            \"Source\": \"ESTO Flat Table\",\n", "        },\n", "        {\n", "            \"Column\": \"Threshold DOI Destination Outlet\",\n", "            \"Description\": \"DOI to be maintained at the Destination Outlet\",\n", "            \"Source\": \"User Defined\",\n", "        },\n", "        {\n", "            \"Column\": \"Threshold Quantity Destination Outlet\",\n", "            \"Description\": \"Quantity calculated based on threshold DOI\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"CPD List Destination Outlet\",\n", "            \"Description\": \"CPD list at the destination outlet\",\n", "            \"Source\": \"System\",\n", "        },\n", "        {\n", "            \"Column\": \"Avg. CPD Destination Outlet\",\n", "            \"Description\": \"Avg. of CPDs in the above list\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"Required Quantity Destination Outlet\",\n", "            \"Description\": \"Threshold Quantity - Intransit Quantity\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"Existing DOI Destination Outlet\",\n", "            \"Description\": \"Available DOI at the Destination Outlet\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\"Column\": \"Bucket\", \"Description\": \"Bucket X,A,B\", \"Source\": \"System\"},\n", "        {\n", "            \"Column\": \"Outer Case Size\",\n", "            \"Description\": \"Outer case size of the item\",\n", "            \"Source\": \"System\",\n", "        },\n", "        {\n", "            \"Column\": \"Case Error\",\n", "            \"Description\": \"0 - In case, 1-Not in case\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"final_indent_V1\",\n", "            \"Description\": \"Indent before optimizations\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"Weight V2\",\n", "            \"Description\": \"Total weight in version 2 indent \",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"final_indent_V2\",\n", "            \"Description\": \"Indent after optimizations\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "        {\n", "            \"Column\": \"chunk_flag\",\n", "            \"Description\": \"Chunks indicating multiple files\",\n", "            \"Source\": \"Calculated\",\n", "        },\n", "    ]\n", ")\n", "data_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with pd.ExcelWriter(csv_file) as writer_i:\n", "    data_dict.to_excel(writer_i, sheet_name=\"Data Dictionary\", index=False, engine=\"xlsxwriter\")\n", "    final_rep_v5.to_excel(writer_i, sheet_name=\"Indent Data\", index=False, engine=\"xlsxwriter\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_rep_v5.to_csv(csv_file, index=False, encoding=\"utf8\")\n", "reverse_sto_v1.to_csv(excess_csv, index=False, encoding=\"utf8\")\n", "detailed_ds1.to_csv(detailed_sum, index=False, encoding=\"utf8\")\n", "transferable_csv.to_csv(transferable, index=False, encoding=\"utf8\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_flat_table = final_rep_v2[\n", "    [\n", "        \"front_end_fac\",\n", "        \"dark_store\",\n", "        \"item_id\",\n", "        \"outer_case_size\",\n", "        \"available_quantity_at_frontend\",\n", "        \"blocked_quantity_at_frontend\",\n", "        \"doi_to_reserve_at_frontend\",\n", "        \"total_quantity_preserved_at_frontend\",\n", "        \"transferable_qty\",\n", "        \"available_quantity_at_darkstore\",\n", "        \"blocked_quantity_at_darkstore\",\n", "        \"threshold_doi\",\n", "        \"intransit_quantity\",\n", "        \"threshold_qty_at_darkstore\",\n", "        \"required_qty_at_darkstore\",\n", "        \"final_indent_V1\",\n", "        \"final_indent_V2\",\n", "        \"indent_doi\",\n", "        \"weight\",\n", "        \"avg_cpd_at_darkstore\",\n", "    ]\n", "]\n", "indent_flat_table.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_flat_table = indent_flat_table.rename(\n", "    columns={\n", "        \"front_end_fac\": \"catering_facility\",\n", "        \"dark_store\": \"dark_store_outlet\",\n", "        \"item_id\": \"item_id\",\n", "        \"outer_case_size\": \"outer_case_size\",\n", "        \"available_quantity_at_frontend\": \"available_quantity_at_catering_outlet\",\n", "        \"blocked_quantity_at_frontend\": \"blocked_quantity_at_catering_outlet\",\n", "        \"doi_to_reserve_at_frontend\": \"doi_to_reserve_at_catering_outlet\",\n", "        \"total_quantity_preserved_at_frontend\": \"total_quantity_preserved_at_catering_outlet\",\n", "        \"transferable_qty\": \"transferable_qty_at_catering_outlet\",\n", "        \"available_quantity_at_darkstore\": \"available_quantity_at_darkstore\",\n", "        \"blocked_quantity_at_darkstore\": \"blocked_quantity_at_darkstore\",\n", "        \"threshold_doi\": \"threshold_doi_at_darkstore\",\n", "        \"intransit_quantity\": \"intransit_quantity\",\n", "        \"threshold_qty_at_darkstore\": \"threshold_qty_at_darkstore\",\n", "        \"required_qty_at_darkstore\": \"required_qty_at_darkstore\",\n", "        \"final_indent_V1\": \"indent_before_optimization\",\n", "        \"indent_doi\": \"indent_doi_after_optimization\",\n", "        \"final_indent_V2\": \"indent_after_optimization\",\n", "        \"weight\": \"weight_of_indent\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_flat_table[\"created_at_ist\"] = datetime.datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indent_flat_table[\"run_uid\"] = uid"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k, v in stock_out_reasons.items():\n", "    new = v.groupby([\"dark_store\"]).agg({\"item_id\": \"count\"}).rename(columns={\"item_id\": k})\n", "    stock_out_report = pd.merge(stock_out_report, new, on=[\"dark_store\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k, v in stock_out_reasons.items():\n", "    v[\"reason\"] = k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"combined\"] = pd.concat(\n", "    [x[[\"front_end_fac\", \"dark_store\", \"item_id\", \"reason\"]] for x in stock_out_reasons.values()]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"combined\"] = stock_out_reasons[\"combined\"][\n", "    [\"front_end_fac\", \"dark_store\", \"item_id\", \"reason\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disp_qty = disp_qty.rename(columns={\"outlet_id\": \"dark_store\", \"quantity\": \"actual_quantity\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["disp_qty[\"net_quantity\"] = disp_qty[\"actual_quantity\"] - disp_qty[\"blocked_quantity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"combined\"] = pd.merge(\n", "    stock_out_reasons[\"combined\"], disp_qty, on=[\"dark_store\", \"item_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"combined\"] = (\n", "    stock_out_reasons[\"combined\"]\n", "    .groupby([\"front_end_fac\", \"dark_store\", \"item_id\"])[\"reason\"]\n", "    .apply(list)\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def dominant_reason(x):\n", "    return x[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"combined\"][\"dominant_reason\"] = stock_out_reasons[\"combined\"][\"reason\"].apply(\n", "    dominant_reason\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"combined\"][\"reason\"] = stock_out_reasons[\"combined\"][\"reason\"].apply(\",\".join)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_table = stock_out_reasons[\"combined\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3_stock_out = stock_out_table[stock_out_table.dark_store == 846]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_so = (\n", "    stock_out_table.groupby([\"dark_store\", \"dominant_reason\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_so = (\n", "    test_so.pivot(index=\"dark_store\", columns=\"dominant_reason\", values=\"item_id\")\n", "    .fillna(0)\n", "    .to_html()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["imp_items = \"\"\"\n", "SELECT distinct item_id,name\n", "FROM metrics.dark_store_assortment_details\n", "WHERE gmv_flag = 1\n", "  AND search_flag = 1\n", "  AND rank < 3\n", "  AND ds_outlet = 846\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["imp_items = pd.read_sql_query(\n", "    sql=imp_items,\n", "    con=CON_REDSHIFT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3_stock_out = pd.merge(d3_stock_out, imp_items, on=[\"item_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_searched = imp_items.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unav_searched_oos = d3_stock_out[\n", "    (d3_stock_out.dominant_reason == \"da__unavailable_at_catering_facility\")\n", "    | (d3_stock_out.dominant_reason == \"stock_available_but_not_transferable\")\n", "    | (d3_stock_out.dominant_reason == \"stock_unavailable_at_catering_facility\")\n", "].shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3_stock_out = d3_stock_out[[\"item_id\", \"name\", \"dominant_reason\"]].to_html(index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["alert = \"\"\"<font color=\"red\" ><b>Alert:</b></font>\n", "            <br>\n", "            <br>\n", "            Out of <b>{total_searched}</b> items corresponding to top 40 searched keywords,\n", "            <b>{unav_searched_oos}</b> were found unavailable at G1.\"\"\".format(\n", "    total_searched=total_searched, unav_searched_oos=unav_searched_oos\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_table[\"created_at_ist\"] = datetime.datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_table[\"run_uid\"] = uid"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now_india = str(datetime.datetime.now(timezone(\"Asia/Kolkata\"))).split(\"+\")[0].split(\".\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_table = stock_out_table.astype(\n", "    {\n", "        \"front_end_fac\": \"int64\",\n", "        \"dark_store\": \"int64\",\n", "        \"item_id\": \"int64\",\n", "        \"reason\": \"object\",\n", "        \"dominant_reason\": \"object\",\n", "        \"created_at_ist\": \"datetime64[ns, Asia/Kolkata]\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_table = stock_out_table.rename(\n", "    columns={\n", "        \"front_end_fac\": \"catering_facility\",\n", "        \"dark_store\": \"dark_store_outlet\",\n", "        \"item_id\": \"item_id\",\n", "        \"reason\": \"reasons\",\n", "        \"dominant_reason\": \"dominant_reason\",\n", "        \"created_at_ist\": \"created_at_ist\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_table.dark_store_outlet.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_stores_stock_out\",\n", "    \"column_dtypes\": redshift_schema(stock_out_table),\n", "    \"primary_key\": [\"created_at_ist\"],\n", "    \"sortkey\": [\"created_at_ist\", \"catering_facility\", \"dark_store_outlet\", \"item_id\"],\n", "    \"incremental_key\": \"created_at_ist\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(stock_out_table, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["schema = [\n", "    {\"name\": \"catering_facility\", \"type\": \"int\"},\n", "    {\"name\": \"dark_store_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"outer_case_size\", \"type\": \"int\"},\n", "    {\"name\": \"available_quantity_at_catering_outlet\", \"type\": \"float\"},\n", "    {\"name\": \"blocked_quantity_at_catering_outlet\", \"type\": \"float\"},\n", "    {\"name\": \"doi_to_reserve_at_catering_outlet\", \"type\": \"float\"},\n", "    {\"name\": \"total_quantity_preserved_at_catering_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"transferable_qty_at_catering_outlet\", \"type\": \"float\"},\n", "    {\"name\": \"available_quantity_at_darkstore\", \"type\": \"float\"},\n", "    {\"name\": \"blocked_quantity_at_darkstore\", \"type\": \"float\"},\n", "    {\"name\": \"threshold_doi_at_darkstore\", \"type\": \"float\"},\n", "    {\"name\": \"intransit_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"threshold_qty_at_darkstore\", \"type\": \"int\"},\n", "    {\"name\": \"required_qty_at_darkstore\", \"type\": \"float\"},\n", "    {\"name\": \"indent_before_optimization\", \"type\": \"float\"},\n", "    {\"name\": \"indent_after_optimization\", \"type\": \"float\"},\n", "    {\"name\": \"indent_doi_after_optimization\", \"type\": \"float\"},\n", "    {\"name\": \"weight_of_indent\", \"type\": \"float\"},\n", "    {\"name\": \"avg_cpd_at_darkstore\", \"type\": \"float\"},\n", "    {\"name\": \"created_at_ist\", \"type\": \"timestamp\"},\n", "    {\"name\": \"run_uid\", \"type\": \"varchar(1000)\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_stores_indents_log\",\n", "    \"column_dtypes\": schema,\n", "    \"primary_key\": [\"created_at_ist\"],\n", "    \"sortkey\": [\"created_at_ist\", \"catering_facility\", \"dark_store_outlet\", \"item_id\"],\n", "    \"incremental_key\": \"created_at_ist\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(indent_flat_table, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_out_reasons[\"summary\"] = stock_out_report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["map_names = map_names.rename(columns={\"Destination Outlet\": \"dark_store\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with pd.ExcelWriter(excel_file) as writer:\n", "    for k, v in stock_out_reasons.items():\n", "        if k != \"summary\":\n", "            v = pd.merge(\n", "                v,\n", "                item_attri_df,\n", "                how=\"left\",\n", "                on=[\"item_id\"],\n", "                suffixes=[\"\", \"_attribute\"],\n", "            )\n", "            v = v.round(2)\n", "            v = v.merge(map_names, on=[\"dark_store\"], how=\"left\")\n", "        v.to_excel(writer, sheet_name=k, index=False, engine=\"xlsxwriter\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["truck_load_size = truck_load_size[truck_load_size.truck_load_size > 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_indents = detailed_ds.to_html(index=False)\n", "truck_load_size = truck_load_size.to_html(index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def tos3(file):\n", "    secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "    file_path = \"reports/{csv_file_path}\".format(csv_file_path=file)\n", "    bucket_name = \"grofers-retail-test\"\n", "    aws_key = secrets.get(\"aws_key\")\n", "    aws_secret = secrets.get(\"aws_secret\")\n", "    import boto3\n", "\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "    bucket_obj.upload_file(file, file_path)\n", "    s3_client = session.client(\"s3\")\n", "    presigned_url = s3_client.generate_presigned_url(\n", "        \"get_object\",\n", "        Params={\"Bucket\": bucket_name, \"Key\": file_path},\n", "        HttpMethod=\"GET\",\n", "        ExpiresIn=86400,\n", "    )\n", "    return presigned_url"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dev_logs_url = tos3(dev_logs_json)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["presigned_url = tos3(excel_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["detailed_sum_url = tos3(detailed_sum)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "# email = ['<EMAIL>']\n", "# to_email = [x.strip() for x in list(email[\"emails\"]) if len(x.strip()) > 0]\n", "subject = \"Dark Stores Replenishment V4.8 | \" + time + \" Indent | \" + str(now_india)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc = \"https://docs.google.com/document/d/1ewB2DoiLEsRfVgjKFXq50XRJKYHckUZ0hWqTDUcTOEM/edit?usp=sharing\"\n", "email_form = \"https://forms.gle/N4Zwez2VvNDjVu2b9\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = (\n", "    \"\"\"\n", "Hi,<br><br>\n", "\n", "Please find the attached indents.<br><br>\n", "\n", "Click <a href='{presigned_url}'>here</a> to download missing items report. (This link will expire in 24 hrs)<br><br>\n", "\n", "<a href='{doc}'>Download process documentation</a> <br><br>\n", "\n", "Email list can be updated <a href='{email_form}'>here</a>. <br><br>\n", "\n", "<b>Summary:</b><br><br>\n", "\n", "\"\"\".format(\n", "        presigned_url=presigned_url, doc=doc, email_form=email_form\n", "    )\n", "    + html_table_summary\n", "    + \"\"\"<br><br>\n", "    <a href={detailed_sum}>Download above table in CSV format for analysis.</a><br>\n", "\"\"\".format(\n", "        detailed_sum=detailed_sum_url\n", "    )\n", ")\n", "html_content += \"<br><br><strong>Open STOs: (SKUs(Quantity))</strong><br><br>\"\n", "html_content += stos_display_html\n", "# html_content += \"<br><br><b>Assortment last updated at: </b>\" + assortment_last_updated\n", "html_content += \"<br><br><b>Unique ID: </b>\" + uid\n", "html_content += f\"<br><br><a href='{dev_logs_url}'>Download Stock Allocation Logs</a></b>\"\n", "\n", "if len(V4_5_logs[\"outlets_excluded\"]) > 0:\n", "    html_content += (\n", "        \"<br><br>Today is not a picking day for following outlets: \" + V4_5_logs[\"outlets_excluded\"]\n", "    )\n", "\n", "html_content += \"<br><br><PERSON><PERSON>,<br><PERSON><PERSON><PERSON>g\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email,\n", "    email,\n", "    subject,\n", "    html_content,\n", "    files=[csv_file, excess_csv, transferable],\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}