dag_name: dark_stores
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: sushant
  slack_id: USKH64U85
path: povms/dark_stores/etl/dark_stores
paused: true
pool: povms_pool
project_name: dark_stores
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 1,13 * * *
  start_date: '2020-11-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 84
