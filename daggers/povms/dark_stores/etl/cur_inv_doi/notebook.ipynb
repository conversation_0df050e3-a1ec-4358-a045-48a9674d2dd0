{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import math"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "Mapping = pb.from_sheets(sheet_id, \"Mapping\")\n", "Mapping.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_outlet_id = Mapping[\"dark_store\"]\n", "ds_doi = Mapping[\"doi\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_outlet_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_threshold_doi = {\"ds_outlet_id\": ds_outlet_id, \"ds_doi\": ds_doi}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_threshold_doi_df = pd.DataFrame.from_dict(outlet_threshold_doi)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"ds_outlet_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Grofers operational outlet\",\n", "    },\n", "    {\"name\": \"ds_doi\", \"type\": \"int\", \"description\": \"days of inventory of the outlet\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"Dark_store_doi_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"ds_outlet_id\"],\n", "    \"sortkey\": [\"ds_outlet_id\"],\n", "    \"incremental_key\": [\"ds_outlet_id\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"captures the doi details of the dark stores\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(outlet_threshold_doi_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_threshold_doi_df[[\"ds_outlet_id\", \"ds_doi\"]] = outlet_threshold_doi_df[\n", "    [\"ds_outlet_id\", \"ds_doi\"]\n", "].astype(\"int\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Current Inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# item_inv_q = \"\"\"SELECT\n", "#      iii.outlet_id,\n", "#      iii.item_id,\n", "#      product.name as item_name,\n", "#      (iii.quantity - COALESCE(sum(case when iibi.blocked_type in (1,2,4,5) then iibi.quantity else 0 end),0))  as 'Current Inventory'\n", "#      FROM\n", "# ims.ims_item_inventory iii\n", "#      LEFT JOIN\n", "# ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "#      INNER JOIN\n", "# retail.warehouse_outlet_mapping wom on iii.outlet_id = wom.cloud_store_id\n", "#      INNER JOIN\n", "# retail.console_outlet co ON co.id=iii.outlet_id\n", "#      INNER JOIN\n", "# retail.console_location cl ON cl.id = co.tax_location_id\n", "#      INNER JOIN\n", "# (SELECT item_id, name FROM rpc.product_product pp2\n", "# WHERE pp2.id in (\n", "# SELECT max(id) as id FROM rpc.product_product pp\n", "# WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", "# ) product ON product.item_id = iii.item_id\n", "# WHERE iii.outlet_id in %(outlet_id)s AND iii.active = 1\n", "# GROUP BY 1,2,3\n", "# \"\"\"\n", "# item_inv = pd.read_sql_query(\n", "#     sql=item_inv_q,\n", "#     con=retail,\n", "#     params={\"outlet_id\": tuple(outlet_threshold_doi_df[\"ds_outlet_id\"])},\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# item_inv.outlet_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# item_inv.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv_q = \"\"\" SELECT inv_details.outlet_id, inv_details.item_id, name as item_name, Current_Inventory as 'Current Inventory'\n", "FROM\n", "(SELECT outlet_id, item_id,  \n", "                max(quantity) as quantity , \n", "                max(blocked_quantity) as blocked_quantity,\n", "                max(quantity) - max(blocked_quantity) as Current_Inventory\n", "FROM \n", "(SELECT good_inv.outlet_id AS outlet_id,\n", "       product.item_id,\n", "       sum(CASE\n", "               WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity ELSE 0\n", "           END) AS quantity,\n", "           0 as blocked_quantity\n", "FROM ims.ims_good_inventory good_inv\n", "INNER JOIN rpc.product_product product ON product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1\n", " AND good_inv.outlet_id IN %(outlet_id)s\n", "GROUP BY 1,\n", "         2\n", "UNION\n", "SELECT a.outlet_id, \n", "       a.item_id, \n", "       a.quantity, \n", "       b.quantity AS blocked_quantity \n", "FROM   (SELECT outlet_id, \n", "               item_id, \n", "               quantity \n", "        FROM   ims.ims_item_inventory \n", "        WHERE  outlet_id IN %(outlet_id)s\n", "               AND active = 1) a \n", "       LEFT JOIN (SELECT item_id, \n", "                         outlet_id, \n", "                         sum(quantity) AS quantity\n", "                  FROM   ims.ims_item_blocked_inventory \n", "                  WHERE  outlet_id IN %(outlet_id)s AND \n", "                  active = 1 \n", "                         AND blocked_type in (1,2,4,5)  -- blocked for sto and orders\n", "                 GROUP BY 1,2) b \n", "              ON a.item_id = b.item_id \n", "                 AND a.outlet_id = b.outlet_id)  T \n", "                 group by 1, 2) inv_details\n", " INNER JOIN\n", "(SELECT item_id, name FROM rpc.product_product pp2 \n", "WHERE pp2.id in (\n", "SELECT max(id) as id FROM rpc.product_product pp \n", "WHERE pp.active=1 and pp.approved=1 GROUP BY item_id)\n", ") product ON inv_details.item_id = product.item_id\n", "\"\"\"\n", "\n", "item_inv = pd.read_sql_query(\n", "    sql=item_inv_q,\n", "    con=retail,\n", "    params={\"outlet_id\": tuple(outlet_threshold_doi_df[\"ds_outlet_id\"])},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv[item_inv.outlet_id == 971].head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_inv.outlet_id.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CPD Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_estimated_q = \"\"\"select\n", "\n", "# l.dark_store_outlet,\n", "# l.item_id as item_id_estimated,\n", "# coalesce(l.avg_cpd_at_darkstore,0) as avg_cpd_at_darkstore\n", "\n", "# from metrics.dark_stores_indents_log l\n", "\n", "# inner join\n", "\n", "# (select dark_store_outlet, item_id , max(created_at_ist) as max_created_at\n", "# from metrics.dark_stores_indents_log\n", "# where dark_store_outlet = 846\n", "# group by 1,2\n", "# )T\n", "\n", "# on created_at_ist = max_created_at and T.item_id = l.item_id and T.dark_store_outlet = l.dark_store_outlet\n", "# where  l.dark_store_outlet = 846\n", "# order by l.item_id \"\"\"\n", "\n", "# cpd_estimated = pd.read_sql_query(sql=cpd_estimated_q, con = redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_estimated.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_snorlax_q = \"\"\" SELECT cpd_details.outlet_id, cpd_details.item_id, max(cpd) as cpd\n", "FROM\n", "\n", "\n", "\n", "(SELECT wom.cloud_store_id AS outlet_id,\n", "       dwc.item_id ,\n", "      case when round(avg(dwc.consumption),0) = 0 then 1 else round(avg(dwc.consumption),0) end  as cpd\n", "FROM\n", "  (SELECT outlet_id,\n", "          item_id,\n", "          date,\n", "          consumption\n", "   FROM snorlax.date_wise_consumption\n", "   WHERE date BETWEEN CURRENT_DATE AND CURRENT_DATE + interval 5 DAY) dwc\n", "JOIN retail.warehouse_outlet_mapping wom ON dwc.outlet_id = wom.warehouse_id\n", "WHERE wom.cloud_store_id IN %(outlet_id)s\n", "GROUP BY 1,\n", "         2\n", "         \n", "         UNION \n", "         \n", "        SELECT dwc1.outlet_id,\n", "       dwc1.item_id ,\n", "      case when round(avg(dwc1.consumption),0) = 0 then 1 else round(avg(dwc1.consumption),0) end  as cpd\n", "FROM\n", "         (select outlet_id,\n", "item_id,\n", "date,\n", "consumption \n", "from snorlax.date_wise_consumption \n", "where date  BETWEEN CURRENT_DATE AND CURRENT_DATE + interval 5 DAY\n", "and outlet_id in %(outlet_id)s\n", ") dwc1\n", "GROUP BY 1,\n", "         2\n", "         ) cpd_details\n", "         group by 1,2\n", "\"\"\"\n", "\n", "cpd_snorlax = pd.read_sql_query(\n", "    sql=cpd_snorlax_q,\n", "    con=retail,\n", "    params={\"outlet_id\": tuple(outlet_threshold_doi_df[\"ds_outlet_id\"])},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_snorlax[cpd_snorlax.outlet_id == 971].head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_snorlax.head(50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# cpd_snorlax.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x = cpd_snorlax.merge(cpd_estimated, how = 'outer', right_on =[\"dark_store_outlet\",\"item_id_estimated\"], left_on = [\"snorlax_outlet_id\",\"snorlax_item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x[pd.isnull(x['dark_store_outlet']) == False].head(50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x['final_item_id'] = x.apply(lambda x: max(x['snorlax_item_id'], x['item_id_estimated']),axis = 1)\n", "# x['final_outlet_id'] = x.apply(lambda x: max(x['dark_store_outlet'], x['snorlax_outlet_id']),axis = 1)\n", "# x['final_cpd'] = x.apply(lambda x: max(x['cpd'],x['avg_cpd_at_darkstore']), axis = 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x['final_outlet_id'] = x.apply(lambda x: max(x['dark_store_outlet'], x['snorlax_outlet_id']),axis = 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x[pd.isnull(x['final_outlet_id']) == True]\n", "\n", "# x.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# y = x[['snorlax_outlet_id','snorlax_item_id','final_cpd']].drop_duplicates()\n", "item_inv.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T = item_inv.merge(\n", "    cpd_snorlax,\n", "    how=\"left\",\n", "    left_on=[\"outlet_id\", \"item_id\"],\n", "    right_on=[\"outlet_id\", \"item_id\"],\n", ")[[\"outlet_id\", \"item_id\", \"item_name\", \"Current Inventory\", \"cpd\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T[\"Current DOI\"] = (T[\"Current Inventory\"] / T[\"cpd\"]).round()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"int\", \"description\": \"Grofers operational outlet\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"Grofers item\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item name\"},\n", "    {\n", "        \"name\": \"Current Inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"actual physical inventory of the item\",\n", "    },\n", "    {\"name\": \"cpd\", \"type\": \"float\", \"description\": \"consumption per day of the item\"},\n", "    {\n", "        \"name\": \"Current DOI\",\n", "        \"type\": \"float\",\n", "        \"description\": \"current days of inventory of the item\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"current_inv_doi\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"outlet_id\", \"item_id\"],\n", "    \"sortkey\": [\"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": [\"ds_outlet_id\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"captures the current inventory and doi of the dark stores\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(T, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}