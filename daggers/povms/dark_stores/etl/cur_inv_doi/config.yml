dag_name: cur_inv_doi
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: UABB9AK9V
path: povms/dark_stores/etl/cur_inv_doi
paused: true
pool: povms_pool
project_name: dark_stores
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: '*/30 * * * *'
  start_date: '2021-03-15T17:50:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
