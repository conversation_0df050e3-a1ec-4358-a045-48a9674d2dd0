{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import boto3\n", "import requests\n", "import re\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = now.date()\n", "current_day = current_date.weekday()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "presto = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cold Drink items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1gA-VALGwSo3OeV35lQj49-ZnGBblGzB6BAIeRmf5rSM\"\n", "sheet_name = \"items_list\"\n", "\n", "items = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "items[\"item_id\"] = items[\"item_id\"].astype(\"int\")\n", "items.drop([\"name\"], axis=1, inplace=True)\n", "items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_facility = [\n", "    1,\n", "    3,\n", "    12,\n", "    15,\n", "    22,\n", "    24,\n", "    32,\n", "    34,\n", "    42,\n", "    43,\n", "    92,\n", "    264,\n", "    268,\n", "    398,\n", "    517,\n", "    554,\n", "    555,\n", "    513,\n", "    1206,\n", "    1320,\n", "    1111,\n", "]\n", "# [3, 32, 92, 264, 268, 554, 555, 513]\n", "# backend_facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT ro.facility_id,\n", "                  tag.outlet_id as frontend_outlet,\n", "                  ro.name as frontend_outlet_name,\n", "                  r.facility_id as backend_facility,\n", "                  tag.item_id\n", "           \n", "      FROM lake_view_rpc.item_outlet_tag_mapping tag\n", "      INNER JOIN lake_view_retail.console_outlet ro ON ro.id=tag.outlet_id\n", "      inner join lake_view_retail.console_location l on l.id=ro.tax_location_id\n", "      INNER JOIN lake_view_retail.console_outlet r ON r.id=cast(tag.tag_value as int)\n", "      WHERE tag_type_id=8\n", "        AND tag.active=1\n", "        and ro.name not like '%%old%%'\n", "        and ro.business_type_id=7\n", "        and r.facility_id in %(facility)s\n", "        ORDER BY 1,\n", "               2,\n", "               3,\n", "               4\"\"\"\n", "\n", "item_tag = pd.read_sql_query(query, presto, params={\"facility\": tuple(backend_facility)})\n", "item_tag.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_tag.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_tag[\"facility_id\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_drinks_facilities = item_tag.merge(items, on=[\"item_id\"], how=\"inner\")\n", "cold_drinks_facilities.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_drinks_facilities.groupby([\"frontend_outlet_name\"])[[\"item_id\"]].nunique().reset_index().head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_drinks_facilities.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_drinks_facilities = cold_drinks_facilities[\n", "    [\"facility_id\", \"frontend_outlet\", \"frontend_outlet_name\", \"item_id\"]\n", "].drop_duplicates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Min/max qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct facility_id,\n", "                item_id,\n", "                min_quantity,\n", "                max_quantity \n", "                from lake_ars.item_min_max_quantity\"\"\"\n", "\n", "\n", "min_max = pd.read_sql_query(query, presto)\n", "min_max.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### item weight\n", "\n", "product_query = \"\"\"\n", "SELECT item_id,\n", "          weight_in_gm\n", "   FROM\n", "     (SELECT item_id,\n", "             max(name) as item_name,\n", "             max(storage_type) AS storage_type_id,\n", "             max(inner_case_size) as inner_case_size,\n", "             max(outer_case_size) AS outer_case_size,\n", "             max(weight_in_gm) as weight_in_gm\n", "      FROM lake_view_rpc.product_product\n", "      GROUP BY 1)x2\n", "\"\"\"\n", "\n", "item_weight = pd.read_sql_query(product_query, presto)\n", "item_weight.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cold_stor(x):\n", "    if x[\"storage_type\"] == \"Cold\":\n", "        if x[\"weight_in_gm\"] == 0 and x[\"weight_in_gm\"] is not None:\n", "            return 0.2\n", "        val = x[\"weight_in_gm\"] // 50\n", "        if x[\"weight_in_gm\"] % 50 > 0:\n", "            return (val + 1) * 0.2\n", "        elif x[\"weight_in_gm\"] > 1000:\n", "            return 5\n", "        return val * 0.2\n", "\n", "    elif x[\"storage_type\"] == \"Heavy\":\n", "        return x[\"weight_in_gm\"] / 1000\n", "    return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cold_drinks_max = cold_drinks_facilities.merge(min_max, on=[\"facility_id\", \"item_id\"], how=\"inner\")\n", "cold_drinks_max[\"storage_type\"] = \"Cold\"\n", "cold_drinks_wt = cold_drinks_max.merge(item_weight, on=[\"item_id\"], how=\"inner\")\n", "cold_drinks_wt[\"storage_factor\"] = cold_drinks_wt.apply(cold_stor, axis=1)\n", "cold_drinks_wt[\"storage_requirement\"] = (\n", "    cold_drinks_wt[\"max_quantity\"] * cold_drinks_wt[\"storage_factor\"]\n", ")\n", "cold_drinks_wt.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frontend_lst = set(cold_drinks_wt[\"facility_id\"])\n", "item_lst = set(cold_drinks_wt[\"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["storage_available = 1000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = (\n", "    cold_drinks_wt.groupby([\"facility_id\", \"frontend_outlet\"])[[\"storage_requirement\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(columns={\"storage_requirement\": \"total_storage_requirement\"})\n", ")\n", "df[\"ratio\"] = storage_available / df[\"total_storage_requirement\"]\n", "df = df[[\"facility_id\", \"frontend_outlet\", \"ratio\"]]\n", "df[\"ratio\"] = np.where(df[\"ratio\"] > 1, 1, df[\"ratio\"])\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = cold_drinks_wt.merge(df, on=[\"facility_id\", \"frontend_outlet\"], how=\"left\")\n", "df1[\"allocated_apace\"] = df1[\"ratio\"] * df1[\"storage_requirement\"]\n", "df1[\"qty_allocated\"] = np.ceil(df1[\"allocated_apace\"] / df1[\"storage_factor\"])\n", "df1[\"qty_allocated\"] = np.where(df1[\"qty_allocated\"] < 2, 2, df1[\"qty_allocated\"])\n", "df1 = (\n", "    df1[\n", "        [\n", "            \"facility_id\",\n", "            \"frontend_outlet\",\n", "            \"item_id\",\n", "            \"min_quantity\",\n", "            \"max_quantity\",\n", "            \"qty_allocated\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .rename(columns={\"frontend_outlet\": \"outlet_id\"})\n", ")\n", "df1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_outlets = set(df1[\"outlet_id\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item available inventory (on shelf + ims)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT m.outlet_id,\n", "       l.item_id,\n", "       sum(CASE\n", "               WHEN ws.storage_type_id IN (3,4,5,7) THEN l.quantity\n", "           END) AS quantity_in_fridge,\n", "       sum(l.quantity) AS total_qty\n", "FROM lake_view_warehouse_location.warehouse_item_location l\n", "inner join\n", "(select warehouse_id, item_id, max(updated_at) as max_updated_at\n", "from lake_view_warehouse_location.warehouse_item_location\n", "group by 1,2) as a on l.item_id = a.item_id and \n", "l.updated_at = max_updated_at and a.warehouse_id = l.warehouse_id\n", "LEFT JOIN lake_view_warehouse_location.warehouse_storage_location ws ON ws.id=l.location_id\n", "AND l.warehouse_id=ws.warehouse_id\n", "INNER JOIN lake_view_warehouse_location.warehouse_outlet_mapping m ON l.warehouse_id = m.warehouse_id\n", "WHERE m.outlet_id IN %(outlet)s\n", "and l.item_id in %(item)s\n", "  AND l.active = 1 and (date(l.updated_at)>=current_date-interval '60' day)\n", "GROUP BY 1,\n", "         2\"\"\"\n", "\n", "\n", "total_available_qty = pd.read_sql_query(\n", "    query, presto, params={\"outlet\": tuple(all_outlets), \"item\": tuple(item_lst)}\n", ")\n", "total_available_qty = total_available_qty.fillna(0)\n", "total_available_qty.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def migrated_qty(x):\n", "\n", "    if x[\"quantity_in_fridge\"] >= x[\"qty_allocated\"]:\n", "        return 0\n", "    else:\n", "        return min(x[\"qty_allocated\"] - x[\"quantity_in_fridge\"], x[\"quantity_on_racks\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = df1.merge(total_available_qty, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "final_df = final_df.fillna(0)\n", "\n", "final_df[\"quantity_on_racks\"] = final_df[\"total_qty\"] - final_df[\"quantity_in_fridge\"]\n", "final_df[\"quantity_on_racks\"] = np.where(\n", "    final_df[\"quantity_on_racks\"] < 0, 0, final_df[\"quantity_on_racks\"]\n", ")\n", "final_df[\"quantity_to_migrate\"] = final_df.apply(migrated_qty, axis=1)\n", "final_df = final_df[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"qty_allocated\",\n", "        \"quantity_on_racks\",\n", "        \"quantity_in_fridge\",\n", "        \"quantity_to_migrate\",\n", "    ]\n", "].drop_duplicates()\n", "final_df = final_df.astype(\"int\")\n", "final_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select facility_id,f.name as facility_name,l.name as city_name\n", "from lake_view_retail.console_outlet ro\n", "left join lake_view_crates.facility f on f.id=ro.facility_id\n", "inner join lake_view_retail.console_location l on l.id=ro.tax_location_id\n", "where ro.id in %(outlet)s\n", "\"\"\"\n", "\n", "\n", "facility_attributes = pd.read_sql_query(query, presto, params={\"outlet\": tuple(all_outlets)})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select item_id,max(name) as item_name from lake_view_rpc.product_product\n", "group by 1\"\"\"\n", "item_name = pd.read_sql_query(query, presto)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df1 = final_df.merge(facility_attributes, on=[\"facility_id\"], how=\"left\")\n", "final_df2 = final_df1.merge(item_name, on=[\"item_id\"], how=\"left\")\n", "final_df2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df2 = final_df2[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"city_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"qty_allocated\",\n", "        \"quantity_on_racks\",\n", "        \"quantity_in_fridge\",\n", "        \"quantity_to_migrate\",\n", "    ]\n", "].drop_duplicates()\n", "final_df2.sort_values(\n", "    by=[\"city_name\", \"facility_name\", \"max_quantity\"],\n", "    ascending=[True, True, False],\n", "    inplace=True,\n", ")\n", "final_df2[\"last_updated_at\"] = now\n", "final_df2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1gA-VALGwSo3OeV35lQj49-ZnGBblGzB6BAIeRmf5rSM\"\n", "sheet_name = \"raw\"\n", "pb.to_sheets(final_df2, sheet_id, sheet_name, clear_cache=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}