{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import datetime\n", "import numpy as np\n", "\n", "CON_REDSHIFT = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Lb5meGTKr4cu0NS75BMrKuPep3itx0zjvU9qLA17J-g\"\n", "sheet_names = \"for_assortment\"\n", "mapping_sheet_rest = pb.from_sheets(\n", "    sheet_id, sheet_names, service_account=\"service_account\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_names = \"1094\"\n", "mapping_sheet = pb.from_sheets(\n", "    sheet_id, sheet_names, service_account=\"service_account\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet = (\n", "    mapping_sheet[[\"Item ID\"]].replace(\"\", np.nan).dropna().rename(columns={\"Item ID\": \"item_id\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet[\"outlet_id\"] = \"1094\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet_rest = (\n", "    mapping_sheet_rest[[\"outlet_id\", \"item_id\", \"manual_bump\"]]\n", "    .replace(\"\", np.nan)\n", "    .dropna()\n", "    .query(\"manual_bump == '1'\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_sheet = pd.concat([mapping_sheet_rest[[\"outlet_id\", \"item_id\"]], mapping_sheet])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlets = \", \".join(mapping_sheet.outlet_id.to_list())\n", "items = \", \".join(mapping_sheet.item_id.to_list())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT DISTINCT i.updated_at,\n", "                i.outlet_id,\n", "                i.item_id,\n", "                pp.name, pp.variant_description \n", "FROM ims.ims_item_inventory i\n", "LEFT JOIN ims.ims_item_blocked_inventory b ON i.item_id = b.item_id\n", "AND i.outlet_id = b.outlet_id\n", "AND b.active = 1\n", "AND b.blocked_type IN (1,\n", "                       2,\n", "                       5)\n", "INNER JOIN rpc.product_product pp ON i.item_id = pp.item_id\n", "AND pp.active=1\n", "AND outlet_type = 1\n", "WHERE i.quantity - b.quantity <= 0\n", "  AND i.active=1\n", "  AND i.outlet_id IN ({outlets})\n", "  AND i.item_id IN ({items})\n", "  AND (fnv_upper_limit = 0\n", "       OR fnv_upper_limit IS NULL)\n", "  AND date(i.updated_at) between current_date-1 and current_date\n", "\"\"\"\n", "items = pd.read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "uid = uuid.uuid1()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_names = \"emails\"\n", "mail_config = pb.from_sheets(\n", "    sheet_id, sheet_names, service_account=\"service_account\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [x.strip() for x in mail_config[\"e_three_fnv\"].to_list()]\n", "subject = \"FnV - Stockout Notification\"\n", "html_content = f\"\"\"Hi,<br><br>\n", "Please find the list below of stocked out items.<br><br>\n", "{items.query(\"outlet_id != 1094\").to_html(index=False)}\n", "<PERSON><PERSON>,<br>\n", "Data Team\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = [x.strip() for x in mail_config[\"1094\"].to_list()]\n", "subject = \"FnV - Stockout Notification\"\n", "html_content = f\"\"\"Hi,<br><br>\n", "Please find the list below of stocked out items.<br><br>\n", "{items.query(\"outlet_id == 1094\").to_html(index=False)}\n", "<PERSON><PERSON>,<br>\n", "Data Team\"\"\"\n", "pb.send_email(from_email, to_email, subject, html_content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}