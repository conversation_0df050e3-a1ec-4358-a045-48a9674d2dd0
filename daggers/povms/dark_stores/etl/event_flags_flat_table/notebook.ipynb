{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import difflib\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "\n", "now1 = datetime.now()\n", "now1\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Merchant-outlet mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_merchant = \"\"\"\n", "SELECT cms_store AS real_merchant_id,\n", "       outlet_id\n", "FROM retail.console_outlet_cms_store\n", "WHERE active=1\n", "\n", "\"\"\"\n", "outlet_merchant = pd.read_sql_query(outlet_merchant, retail)\n", "outlet_merchant.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Notify me items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH VIEW AS\n", "  (SELECT at_date_ist,\n", "          user_id,\n", "          device_uuid,\n", "          properties__product_id,\n", "          traits__city_name,\n", "          traits__merchant_name,\n", "          traits__merchant_id\n", "   FROM spectrum.mobile_event_data\n", "   WHERE at_date_ist>=CURRENT_DATE-30\n", "     AND name='Notify Me Clicked'\n", "     AND app__version IN ('5.4.20',\n", "                          '5.4.21',\n", "                          '5.4.22',\n", "                          '5.4.23',\n", "                          '5.4.24',\n", "                          '5.4.25') ), mer_map AS\n", "  (SELECT virtual_merchant_id,\n", "          real_merchant_id\n", "   FROM lake_cms.gr_virtual_to_real_merchant_mapping\n", "   WHERE enabled_flag = 'true'),\n", "                                       p AS\n", "  (SELECT mp.real_merchant_id,\n", "          ipm.item_id,\n", "          count(DISTINCT device_uuid) AS dev,\n", "          row_number() OVER (PARTITION BY real_merchant_id\n", "                             ORDER BY count(DISTINCT device_uuid) DESC) AS rank_top_clicked\n", "   FROM VIEW\n", "   INNER JOIN lake_cms.gr_product gp ON view.properties__product_id=gp.id\n", "   INNER JOIN consumer.rpc_item_product_mapping ipm ON ipm.product_id = properties__product_id\n", "   INNER JOIN mer_map mp ON mp.virtual_merchant_id = traits__merchant_id\n", "   GROUP BY 1,\n", "            2\n", "   ORDER BY dev DESC)\n", "SELECT *\n", "FROM p\n", "WHERE rank_top_clicked <= 30\n", "\"\"\"\n", "notify_me = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_v1 = pd.merge(\n", "    notify_me[[\"real_merchant_id\", \"item_id\"]],\n", "    outlet_merchant,\n", "    how=\"right\",\n", "    on=[\"real_merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_v1 = notify_me_v1.dropna()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Searches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches = \"\"\"\n", "WITH SEARCH AS\n", "  (SELECT loc.at_date_ist,\n", "          lower(loc.properties__query)::varchar AS keyword,\n", "          loc.device_uuid AS user_id,\n", "          loc.traits__merchant_name AS merchant,\n", "          loc.traits__merchant_id --count(distinct loc.custom__user_id) as users\n", "\n", "   FROM spectrum.mobile_event_data loc\n", "   WHERE loc.at_date_ist >= CURRENT_DATE\n", "     AND loc.name = 'Search Results Viewed'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5), atc AS\n", "  (SELECT loc.at_date_ist,\n", "          lower(loc.properties__search_input_keyword)::varchar AS keyword1,\n", "          lower(loc.properties__search_actual_keyword)::varchar AS keyword2,\n", "          loc.device_uuid AS user_id,\n", "          loc.traits__merchant_id -- count(distinct loc.custom__user_id) as users\n", "\n", "   FROM spectrum.mobile_event_data loc\n", "   WHERE loc.at_date_ist >= CURRENT_DATE-30\n", "     AND loc.name = 'Product Added'\n", "     AND loc.properties__page_name = 'Search List' --and loc.traits__merchant_id in ('29008','29108')\n", "\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5),\n", "                mer_map AS\n", "  (SELECT virtual_merchant_id,\n", "          real_merchant_id\n", "   FROM lake_cms.gr_virtual_to_real_merchant_mapping\n", "   WHERE enabled_flag = 'true')\n", "SELECT *,\n", "       (atc::float/SEARCH)*100 AS search_conv\n", "FROM\n", "  (SELECT s.at_date_ist,\n", "          merchant,\n", "          m.real_merchant_id,\n", "          s.keyword,\n", "          count(DISTINCT s.user_id) AS SEARCH,\n", "          count(DISTINCT a.user_id) AS atc, --atc * 100.0/search as search_conv,\n", " row_number() OVER (PARTITION BY merchant,\n", "                                 s.at_date_ist\n", "                    ORDER BY SEARCH DESC) AS rank_top_searched\n", "   FROM SEARCH s\n", "   LEFT JOIN atc a ON s.user_id=a.user_id\n", "   AND s.at_date_ist = a.at_date_ist\n", "   AND (s.keyword = a.keyword1\n", "        OR s.keyword = a.keyword2)\n", "   INNER JOIN mer_map m ON m.virtual_merchant_id = s.traits__merchant_id\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "WHERE rank_top_searched <=30\n", "  AND SEARCH>0\n", "\"\"\"\n", "searches_df = pd.read_sql_query(searches, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v1 = pd.merge(\n", "    searches_df[[\"real_merchant_id\", \"keyword\"]],\n", "    outlet_merchant,\n", "    how=\"left\",\n", "    left_on=[\"real_merchant_id\"],\n", "    right_on=[\"real_merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v1 = searches_df_v1.dropna()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### item_attributes_query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM consumer.rpc_item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "item_product_mapping = pd.read_sql(item_attributes_query, redshift)\n", "\n", "item_product_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v1[\"key\"] = 0\n", "item_product_mapping[\"key\"] = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_cross_join = searches_df_v1.merge(item_product_mapping, how=\"left\", on=[\"key\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def match_keyword(row):\n", "    pot_matches = [\n", "        row[\"name\"],\n", "        row[\"l0\"],\n", "        row[\"l1\"],\n", "        row[\"l2\"],\n", "        row[\"brand\"],\n", "        row[\"manf\"],\n", "        row[\"product_type\"],\n", "    ]\n", "    pot_matches = [x for x in pot_matches if x is not None]\n", "    keyword = row[\"keyword\"]\n", "    # print(keyword)\n", "    its = difflib.get_close_matches(keyword, pot_matches, cutoff=0.75)\n", "    if its is not None:\n", "        if len(its) > 0:\n", "            return 1\n", "        else:\n", "            return 0\n", "    else:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_cross_join[\"search_flag\"] = test_cross_join.apply(match_keyword, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_cross_join = test_cross_join[\n", "    test_cross_join.search_flag == 1\n", "]  # .reset_index(index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_cross_join = test_cross_join[[\"outlet_id\", \"item_id\", \"search_flag\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_v1[\"notify_me_flag\"] = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_v1 = notify_me_v1[[\"item_id\", \"outlet_id\", \"notify_me_flag\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_table = pd.merge(\n", "    notify_me_v1, test_cross_join, on=[\"outlet_id\", \"item_id\"], how=\"outer\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_table = final_table.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv = \"\"\"\n", "SELECT item_id,\n", "       outlet as outlet_id,\n", "       sum(quantity) * avg(selling_price) AS gmv\n", "FROM\n", "  (SELECT date(convert_timezone('Asia/Kolkata',od.scheduled_at)) AS delivery_date,\n", "          oi.id,\n", "          ip.item_id,\n", "          od.outlet,\n", "          ro.facility_id,\n", "          od.order_id AS sub_order_id,\n", "          oi.order_id AS ancestor_order_id,\n", "          (product_name || ' ' || p.unit) AS product_name,\n", "          quantity,\n", "          oi.price AS selling_price,\n", "          landing_price,\n", "          city\n", "   FROM consumer.order_item_data oi\n", "   LEFT JOIN lake_cms.gr_product p ON oi.product_id = p.id\n", "   LEFT JOIN lake_ims.ims_order_details od ON od.ancestor=oi.order_id\n", "   INNER JOIN consumer.pos_console_outlet ro ON ro.id=od.outlet\n", "   inner join consumer.rpc_item_product_mapping ip on oi.product_id = ip.product_id\n", "   WHERE date(convert_timezone('Asia/Kolkata',od.scheduled_at))BETWEEN CURRENT_DATE-interval'30 day' AND CURRENT_DATE-interval'1 day')\n", "GROUP BY 1,\n", "         2\n", "\"\"\"\n", "gmv_df = pd.read_sql_query(gmv, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_table = pd.merge(final_table, gmv_df, on=[\"item_id\", \"outlet_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_table = final_table.astype(\n", "    {\n", "        \"item_id\": int,\n", "        \"outlet_id\": int,\n", "        \"notify_me_flag\": int,\n", "        \"search_flag\": int,\n", "        \"gmv\": float,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_table[\"created_at\"] = now1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"item_outlet_event_flags\",\n", "    \"column_dtypes\": redshift_schema(final_table),\n", "    \"primary_key\": [\"created_at\"],\n", "    \"sortkey\": [\"created_at\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"created_at\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(final_table, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}