{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Importing libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import difflib\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "\n", "now1 = datetime.now()\n", "now1\n", "\n", "pd.set_option(\"display.max_columns\", 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Getting connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")\n", "redshift = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet2 = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "cater_ds_mapping = pb.from_sheets(sheet2, \"Mapping\")\n", "cater_ds_mapping = cater_ds_mapping[[\"front_end\", \"dark_store\"]].drop_duplicates()\n", "cater_ds_mapping.rename(columns={\"front_end\": \"outlet_id\", \"dark_store\": \"ds_outlet\"}, inplace=True)\n", "cater_ds_mapping"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Defining variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TODAY = date.today()\n", "OUTLET_ID = cater_ds_mapping[\"ds_outlet\"].to_list()\n", "catering_outlet = cater_ds_mapping[\"outlet_id\"].to_list()\n", "cater_outlets = tuple(catering_outlet)\n", "ds_outlets = tuple(OUTLET_ID)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Lb5meGTKr4cu0NS75BMrKuPep3itx0zjvU9qLA17J-g\"\n", "fnv_perishable_assortment = pb.from_sheets(sheet_id, \"for_assortment\")\n", "fnv_perishable_assortment.rename(columns={\"outlet_id\": \"ds_outlet\"}, inplace=True)\n", "fnv_perishable_assortment.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Merchant-outlet mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_merchant = \"\"\"\n", "SELECT cms_store AS real_merchant_id,\n", "       outlet_id\n", "FROM retail.console_outlet_cms_store\n", "WHERE active=1\n", "\n", "\"\"\"\n", "outlet_merchant = pd.read_sql_query(outlet_merchant, retail)\n", "outlet_merchant.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Item-Product Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM GR_PRODUCT P\n", "   INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM rpc_item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "\"\"\"\n", "item_product_mapping = pd.read_sql(item_attributes_query, redshift)\n", "\n", "item_product_mapping.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Notify me items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH VIEW AS\n", "  (SELECT at_date_ist,\n", "          user_id,\n", "          device_uuid,\n", "          properties__product_id,\n", "          traits__city_name,\n", "          traits__merchant_name,\n", "          traits__merchant_id\n", "   FROM spectrum.mobile_event_data\n", "   WHERE at_date_ist>=CURRENT_DATE-30\n", "     AND name='Notify Me Clicked'\n", "     AND app__version IN ('5.4.20',\n", "                          '5.4.21',\n", "                          '5.4.22',\n", "                          '5.4.23',\n", "                          '5.4.24',\n", "                          '5.4.25') ), mer_map AS\n", "  (SELECT virtual_merchant_id,\n", "          real_merchant_id\n", "   FROM consumer.gr_virtual_to_real_merchant_mapping\n", "   WHERE enabled_flag = 'true'),\n", "                                       p AS\n", "  (SELECT mp.real_merchant_id,\n", "          ipm.item_id,\n", "          count(DISTINCT device_uuid) AS dev,\n", "          row_number() OVER (PARTITION BY real_merchant_id\n", "                             ORDER BY count(DISTINCT device_uuid) DESC) AS rank_top_clicked\n", "   FROM VIEW\n", "   INNER JOIN gr_product gp ON view.properties__product_id=gp.id\n", "   INNER JOIN consumer.rpc_item_product_mapping ipm ON ipm.product_id = properties__product_id\n", "   INNER JOIN mer_map mp ON mp.virtual_merchant_id = traits__merchant_id\n", "   GROUP BY 1,\n", "            2\n", "   ORDER BY dev DESC)\n", "SELECT *\n", "FROM p\n", "WHERE rank_top_clicked <= 30\n", "\"\"\"\n", "notify_me = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_v1 = pd.merge(\n", "    notify_me[[\"real_merchant_id\", \"item_id\"]],\n", "    outlet_merchant,\n", "    how=\"right\",\n", "    left_on=[\"real_merchant_id\"],\n", "    right_on=[\"real_merchant_id\"],\n", ")\n", "notify_me_v2 = notify_me_v1[notify_me_v1[\"outlet_id\"].isin(OUTLET_ID)]\n", "notify_me_v2 = notify_me_v2.dropna()\n", "notify_me_v2.rename(columns={\"outlet_id\": \"ds_outlet\"}, inplace=True)\n", "notify_me_v2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Notify-me ptype mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_v3 = notify_me_v2.merge(\n", "    item_product_mapping[[\"item_id\", \"name\", \"product_type\"]],\n", "    on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "notify_me_v3 = notify_me_v3.dropna()\n", "notify_me_v3.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Removing FnV from notify-me items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fnv_query = \"\"\"SELECT DISTINCT r.item_id,\n", "                r.outlet_type\n", "FROM rpc.product_product r\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          max(created_at) AS dated\n", "   FROM rpc.product_product\n", "   GROUP BY 1) a ON a.item_id=r.item_id\n", "WHERE r.created_at=a.dated\n", "  AND r.outlet_type=1\"\"\"\n", "\n", "fnv_items = pd.read_sql_query(fnv_query, retail)\n", "\n", "notify_me_v4 = notify_me_v3.merge(fnv_items, on=[\"item_id\"], how=\"left\")\n", "notify_me_v4 = notify_me_v4[notify_me_v4[\"outlet_type\"] != 1]\n", "notify_me_v4.drop([\"outlet_type\"], axis=1, inplace=True)\n", "notify_me_v4.head(2)\n", "notify_me_v4[\"notify_me_flag\"] = 1\n", "notify_me_items = notify_me_v4[[\"ds_outlet\", \"item_id\", \"name\", \"product_type\", \"notify_me_flag\"]]\n", "notify_me_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notify_me_items.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Searches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches = \"\"\"\n", "WITH SEARCH AS\n", "  (SELECT loc.at_date_ist,\n", "          lower(loc.properties__query)::varchar AS keyword,\n", "          loc.device_uuid AS user_id,\n", "          loc.traits__merchant_name AS merchant,\n", "          loc.traits__merchant_id --count(distinct loc.custom__user_id) as users\n", "\n", "   FROM spectrum.mobile_event_data loc\n", "   WHERE loc.at_date_ist >= CURRENT_DATE\n", "     AND loc.name = 'Search Results Viewed'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5), atc AS\n", "  (SELECT loc.at_date_ist,\n", "          lower(loc.properties__search_input_keyword)::varchar AS keyword1,\n", "          lower(loc.properties__search_actual_keyword)::varchar AS keyword2,\n", "          loc.device_uuid AS user_id,\n", "          loc.traits__merchant_id -- count(distinct loc.custom__user_id) as users\n", "\n", "   FROM spectrum.mobile_event_data loc\n", "   WHERE loc.at_date_ist >= CURRENT_DATE-30\n", "     AND loc.name = 'Product Added'\n", "     AND loc.properties__page_name = 'Search List' --and loc.traits__merchant_id in ('29008','29108')\n", "\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5),\n", "                mer_map AS\n", "  (SELECT virtual_merchant_id,\n", "          real_merchant_id\n", "   FROM consumer.gr_virtual_to_real_merchant_mapping\n", "   WHERE enabled_flag = 'true')\n", "SELECT *,\n", "       (atc::float/SEARCH)*100 AS search_conv\n", "FROM\n", "  (SELECT s.at_date_ist,\n", "          merchant,\n", "          m.real_merchant_id,\n", "          s.keyword,\n", "          count(DISTINCT s.user_id) AS SEARCH,\n", "          count(DISTINCT a.user_id) AS atc, --atc * 100.0/search as search_conv,\n", " row_number() OVER (PARTITION BY merchant,\n", "                                 s.at_date_ist\n", "                    ORDER BY SEARCH DESC) AS rank_top_searched\n", "   FROM SEARCH s\n", "   LEFT JOIN atc a ON s.user_id=a.user_id\n", "   AND s.at_date_ist = a.at_date_ist\n", "   AND (s.keyword = a.keyword1\n", "        OR s.keyword = a.keyword2)\n", "   INNER JOIN mer_map m ON m.virtual_merchant_id = s.traits__merchant_id\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "WHERE rank_top_searched <=30\n", "  AND SEARCH>0\n", "\"\"\"\n", "searches_df = pd.read_sql_query(searches, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v1 = pd.merge(\n", "    searches_df[[\"real_merchant_id\", \"keyword\"]],\n", "    outlet_merchant,\n", "    how=\"left\",\n", "    left_on=[\"real_merchant_id\"],\n", "    right_on=[\"real_merchant_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v1 = searches_df_v1.dropna()\n", "searches_df_v3 = searches_df_v1[searches_df_v1[\"outlet_id\"].isin(OUTLET_ID)]\n", "searches_df_v3[\"outlet_id\"] = searches_df_v3[\"outlet_id\"].astype(\"int\")\n", "searches_df_v3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v3[\"outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searches_df_v3.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwrds = list(searches_df_v3.keyword.unique())\n", "kwrds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = []\n", "df2 = []\n", "for keyword in kwrds:\n", "    li = []\n", "    for index, row in item_product_mapping.iterrows():\n", "        pot_matches = [\n", "            row[\"name\"],\n", "            row[\"l0\"],\n", "            row[\"l1\"],\n", "            row[\"l2\"],\n", "            row[\"brand\"],\n", "            row[\"manf\"],\n", "            row[\"product_type\"],\n", "        ]\n", "        pot_matches = [x for x in pot_matches if x is not None]\n", "        # print(keyword,pot_matches)\n", "        its = difflib.get_close_matches(keyword, pot_matches, cutoff=0.75)\n", "        if its is not None:\n", "            if len(its) > 0:\n", "                li.append(row[\"item_id\"])\n", "    if len(li) > 0:\n", "        df.append({\"keyword\": keyword, \"items\": li})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3 = pd.DataFrame.from_dict(df)\n", "df3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = item_product_mapping.copy()\n", "x = []\n", "for i in df3[\"items\"]:\n", "    for a in i:\n", "        x.append(a)\n", "searches = pd.DataFrame(x, columns=[\"item_id\"])\n", "searches = searches.drop_duplicates()\n", "searches.head()\n", "search_ptypes = searches.merge(\n", "    item_product_mapping[[\"item_id\", \"product_type\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "search_ptypes.drop([\"item_id\"], axis=1, inplace=True)\n", "search_ptypes = search_ptypes.drop_duplicates()\n", "search_ptypes = search_ptypes.dropna()\n", "search_ptypes[\"search_flag\"] = 1\n", "search_ptypes.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Orders Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select product_id,outlet as outlet_id,sum(quantity) as quantity,avg(selling_price) as selling_price ,avg(landing_price) as landing_price from\n", "(select date(convert_timezone('Asia/Kolkata',od.scheduled_at)) AS delivery_date,\n", "          oi.id,\n", "          od.outlet,\n", "          od.order_id as sub_order_id,\n", "          oi.order_id as ancestor_order_id,\n", "          product_id,\n", "          (product_name || ' ' || p.unit) AS product_name,\n", "          quantity,\n", "          oi.price AS selling_price,\n", "          landing_price,\n", "          city\n", "   FROM consumer.order_item_data oi\n", "   LEFT JOIN gr_product p ON oi.product_id = p.id\n", "   left join consumer.ims_order_details od on od.ancestor=oi.order_id\n", "   WHERE date(convert_timezone('Asia/Kolkata',od.scheduled_at))between CURRENT_DATE-30 and CURRENT_DATE-1\n", "   and outlet in %(outlet)s )x1\n", "   group by 1,2\"\"\"\n", "\n", "orders = pd.read_sql(query, redshift, params={\"outlet\": cater_outlets})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["orders.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Order items-product mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_items = orders.merge(\n", "    item_product_mapping[[\"item_id\", \"product_id\", \"product_type\"]],\n", "    on=[\"product_id\"],\n", "    how=\"left\",\n", ")\n", "order_items = order_items.dropna()\n", "order_items.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### FC Available inventory (Total inventory - Blocked inventory)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select distinct \n", "          a.item_id ,\n", "   a.outlet_id,\n", "          quantity  AS actual_quantity\n", "\n", "   FROM ims.ims_item_inventory as a\n", "   inner join\n", "   ( select item_id,outlet_id,max(updated_at)as update_at from ims.ims_item_inventory group by 1,2 ) as b\n", "   on a.item_id=b.item_id\n", "   and a.outlet_id=b.outlet_id\n", "   and a.updated_at=b.update_at\n", "   where  a.outlet_id in %(outlet)s\n", "   group by 1,2,3\n", "   \"\"\"\n", "Total_inventory = pd.read_sql_query(query, retail, params={\"outlet\": cater_outlets})\n", "\n", "blocked = \"\"\"\n", "    select item_id ,\n", "     outlet_id ,\n", "     max(CASE\n", "         WHEN blocked_type = 1 THEN quantity\n", "         ELSE 0\n", "     END) AS order_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 2 THEN quantity\n", "         ELSE 0\n", "     END )AS sto_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 4 THEN quantity\n", "         ELSE 0\n", "     END )AS pro_blocked,\n", "     max(CASE\n", "         WHEN blocked_type = 5 THEN quantity\n", "         ELSE 0\n", "     END )AS sso_blocked\n", "FROM ims.ims_item_blocked_inventory\n", "where outlet_id in %(outlet)s\n", "group by 1,2\n", "             \"\"\"\n", "block_quantity = pd.read_sql_query(blocked, retail, params={\"outlet\": cater_outlets})\n", "\n", "inv_data = Total_inventory.merge(block_quantity, on=[\"outlet_id\", \"item_id\"], how=\"outer\")\n", "inv_data[\"blocked_quantity\"] = (\n", "    inv_data.order_blocked + inv_data.sto_blocked + inv_data.pro_blocked + inv_data.sso_blocked\n", ")\n", "inv_data = inv_data.drop_duplicates()\n", "inv_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### DS available inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT a.outlet_id as ds_outlet, \n", "       a.item_id, \n", "       a.quantity as ds_quantity, \n", "       b.quantity AS ds_blocked_quantity ,\n", "       city_id\n", "FROM   (SELECT i.outlet_id,\n", "        o.tax_location_id as city_id, \n", "               item_id, \n", "               quantity \n", "        FROM   ims.ims_item_inventory i\n", "        left join retail.console_outlet o on o.id=i.outlet_id\n", "        WHERE  outlet_id IN (846,849,850,919,926 ) \n", "           \n", "               AND i.active = 1) a \n", "       LEFT JOIN (SELECT item_id, \n", "                         outlet_id, \n", "                         sum(quantity) AS quantity\n", "                  FROM   ims.ims_item_blocked_inventory \n", "                  WHERE  outlet_id IN %(outlet)s\n", "                   \n", "                         AND active = 1 \n", "                         AND blocked_type in (1)  -- blocked for sto and orders\n", "                 GROUP BY 1,2) b \n", "              ON a.item_id = b.item_id \n", "                 AND a.outlet_id = b.outlet_id \"\"\"\n", "\n", "\n", "DS_available = pd.read_sql_query(query, retail, params={\"outlet\": ds_outlets})\n", "\n", "\n", "DS_available = DS_available[\n", "    [\"ds_outlet\", \"item_id\", \"ds_quantity\", \"ds_blocked_quantity\"]\n", "].drop_duplicates()\n", "\n", "ds_offline_query = \"\"\"SELECT good_inv.outlet_id as ds_outlet,\n", "       product.item_id,\n", "       sum(CASE\n", "               WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity\n", "           END) AS ds_quantity\n", "FROM ims.ims_good_inventory good_inv\n", "INNER JOIN rpc.product_product product ON product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1\n", "  AND good_inv.outlet_id IN %(outlet)s\n", "GROUP BY 1,\n", "         2\"\"\"\n", "\n", "ds_offline = pd.read_sql_query(ds_offline_query, retail, params={\"outlet\": ds_outlets})\n", "ds_offline = ds_offline.fillna(0)\n", "\n", "DS_available_final = DS_available.merge(ds_offline, on=[\"ds_outlet\", \"item_id\"], how=\"outer\")\n", "DS_available_final = DS_available_final.fillna(0)\n", "DS_available_final[\"ds_quantity\"] = (\n", "    DS_available_final[\"ds_quantity_x\"] + DS_available_final[\"ds_quantity_y\"]\n", ")\n", "DS_available_finalv2 = DS_available_final[\n", "    [\"ds_outlet\", \"item_id\", \"ds_quantity\", \"ds_blocked_quantity\"]\n", "].drop_duplicates()\n", "DS_available_finalv2[\"ds_available_qty\"] = (\n", "    DS_available_finalv2[\"ds_quantity\"] - DS_available_finalv2[\"ds_blocked_quantity\"]\n", ")\n", "DS_available_finalv2[\"ds_available_qty\"] = np.where(\n", "    DS_available_finalv2[\"ds_available_qty\"] < 0,\n", "    0,\n", "    DS_available_finalv2[\"ds_available_qty\"],\n", ")\n", "DS_available_finalv3 = DS_available_finalv2[DS_available_finalv2[\"ds_available_qty\"] > 0]\n", "DS_available_ptype = DS_available_finalv3.merge(\n", "    item_product_mapping[[\"item_id\", \"product_type\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "DS_available_ptype[\"DS_available_flag\"] = 1\n", "DS_available_ptype.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Finding CPD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select distinct wom.cloud_store_id as outlet_id, dwc.date as date,dwc.item_id, dwc.consumption as cpd \n", "    from \n", "    (select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "    date between current_date + interval 1 day\n", "    and current_date + interval 2 week) dwc\n", "    join \n", "    retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "    where wom.cloud_store_id in %(outlet)s\n", "\"\"\"\n", "cpd = pd.read_sql_query(cpd_query, retail, params={\"outlet\": cater_outlets})\n", "cpd.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Active assortment based mon the doi available in the backend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_active_doi = inv_data.copy()\n", "assortment_active_doi[\"backend_available\"] = (\n", "    assortment_active_doi[\"actual_quantity\"] - assortment_active_doi[\"blocked_quantity\"]\n", ")\n", "assortment_active_doi[\"backend_available\"] = np.where(\n", "    assortment_active_doi[\"backend_available\"] < 0,\n", "    0,\n", "    assortment_active_doi[\"backend_available\"],\n", ")\n", "assortment_active_doi.head()\n", "cpd_average = cpd.groupby([\"outlet_id\", \"item_id\"]).agg({\"cpd\": \"mean\"}).reset_index()\n", "cpd_average.head()\n", "assortment_active_doi_cpd = assortment_active_doi.merge(\n", "    cpd_average, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "assortment_active_doi_cpd = assortment_active_doi_cpd.fillna(0)\n", "assortment_active_doi_cpd[\"doi_available\"] = (\n", "    assortment_active_doi_cpd[\"backend_available\"] / assortment_active_doi_cpd[\"cpd\"]\n", ")\n", "assortment_active_doi_cpd = assortment_active_doi_cpd.fillna(0)\n", "assortment_active_doi_cpd = assortment_active_doi_cpd[\n", "    assortment_active_doi_cpd[\"doi_available\"] >= 2\n", "]\n", "\n", "assortment_active = assortment_active_doi_cpd[[\"outlet_id\", \"item_id\"]].drop_duplicates()\n", "assortment_active.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_active.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Active assortment based on master assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select distinct city_id,o.id as outlet_id,item_id,master_assortment_substate_id from \n", "rpc.product_master_assortment r\n", "inner join retail.console_outlet o on r.city_id=o.tax_location_id\n", "where o.id in %(outlet)s\n", "and master_assortment_substate_id in (1)\"\"\"\n", "\n", "active_items1 = pd.read_sql_query(query, retail, params={\"outlet\": cater_outlets})\n", "active_items1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_items = assortment_active.merge(active_items1, on=[\"outlet_id\", \"item_id\"], how=\"outer\")\n", "active_items.drop([\"city_id\"], axis=1, inplace=True)\n", "active_items.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment_ims = inv_data.merge(active_items, on=[\"outlet_id\", \"item_id\"], how=\"inner\")\n", "active_assortment_ims.fillna(0)\n", "active_assortment_ims.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment_ims.head(2)\n", "active_assortment_ims_ptype = active_assortment_ims.merge(\n", "    item_product_mapping[[\"item_id\", \"name\", \"product_type\", \"l0\"]], how=\"left\"\n", ")\n", "active_assortment_ims_ptype = active_assortment_ims_ptype[\n", "    (active_assortment_ims_ptype[\"l0\"] != \"Home & Kitchen\")\n", "    & (active_assortment_ims_ptype[\"l0\"] != \"Vegetables & Fruits\")\n", "    & (active_assortment_ims_ptype[\"l0\"] != \"Fresh & Frozen Food\")\n", "    & (active_assortment_ims_ptype[\"l0\"] != \"Specials\")\n", "]\n", "active_assortment_ims_ptype.drop([\"l0\"], axis=1, inplace=True)\n", "active_assortment_ims_ptype.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment_ims_ptype.groupby([\"outlet_id\"]).agg({\"item_id\": \"nunique\"}).reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Finding quantity to be reserved in backened facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "sheet_names = [\n", "    \"Mapping\",\n", "    \"DOI Matrix\",\n", "    \"DoI Reserve\",\n", "    \"Gurgaon(HR)\",\n", "    \"noida/ghaziabad\",\n", "    \"Emails\",\n", "    \"Truck Capacity\",\n", "]\n", "doi_reserve = pb.from_sheets(\n", "    sheet_id, sheet_names[2], service_account=\"service_account\", clear_cache=True\n", ")\n", "doi_reserve_schema = {\n", "    \"front_end\": \"float\",\n", "    \"doi_to_reserve\": \"float\",\n", "    \"item_id\": \"float\",\n", "}\n", "doi_reserve = doi_reserve.astype(doi_reserve_schema)\n", "doi_reserve.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Max 2 reservation filter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_reserve[\"doi_to_reserve\"] = np.where(\n", "    doi_reserve[\"doi_to_reserve\"] > 2, 2, doi_reserve[\"doi_to_reserve\"]\n", ")\n", "doi_reserve.rename(columns={\"front_end\": \"outlet_id\"}, inplace=True)\n", "doi_reserve.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment_ims_ptype.groupby([\"outlet_id\"]).agg({\"item_id\": \"nunique\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_cpd_backened1 = active_assortment_ims_ptype[[\"outlet_id\", \"item_id\"]].merge(\n", "    cpd, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "doi_cpd_backened2 = doi_cpd_backened1.merge(doi_reserve, on=[\"outlet_id\", \"item_id\"], how=\"left\")\n", "doi_cpd_backened2[\"doi_to_reserve\"] = doi_cpd_backened2[\"doi_to_reserve\"].fillna(2)\n", "# doi_cpd_backened2= doi_cpd_backened2.dropna()\n", "doi_cpd_backened2[\"front_end\"] = doi_cpd_backened2[\"outlet_id\"]\n", "doi_cpd_backened2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_cpd_backened = (\n", "    doi_cpd_backened2.groupby([\"outlet_id\", \"item_id\", \"doi_to_reserve\"])\n", "    .agg({\"cpd\": \"mean\"})\n", "    .reset_index()\n", ")\n", "doi_cpd_backened.head()\n", "doi_cpd_backened[\"reserved_quantity\"] = np.ceil(\n", "    doi_cpd_backened[\"doi_to_reserve\"] * doi_cpd_backened[\"cpd\"]\n", ")\n", "doi_cpd_backened = doi_cpd_backened.fillna(0)\n", "doi_cpd_backened.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_cpd_backenedv3 = doi_cpd_backened.drop([\"cpd\"], axis=1)\n", "doi_cpd_backenedv3 = doi_cpd_backenedv3.drop_duplicates()\n", "doi_cpd_backenedv3[\"total_reserved_qty\"] = np.ceil(doi_cpd_backenedv3[\"reserved_quantity\"])\n", "doi_cpd_backenedv3 = doi_cpd_backenedv3.drop_duplicates()\n", "doi_cpd_backenedv3 = doi_cpd_backenedv3.drop([\"reserved_quantity\"], axis=1)\n", "doi_cpd_backenedv3.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_assortment_ims_ptype = active_assortment_ims_ptype.drop_duplicates()\n", "active_assortment_ims_ptype.groupby([\"outlet_id\"]).agg({\"item_id\": \"nunique\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = active_assortment_ims_ptype.merge(\n", "    doi_cpd_backenedv3[[\"outlet_id\", \"item_id\", \"total_reserved_qty\"]],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "inventory = inventory.fillna(0)\n", "inventory[\"net_available_qty\"] = inventory[\"actual_quantity\"] - (\n", "    inventory[\"blocked_quantity\"] + inventory[\"total_reserved_qty\"]\n", ")\n", "inventory[\"net_available_qty\"] = np.where(\n", "    inventory[\"net_available_qty\"] < 0, 0, inventory[\"net_available_qty\"]\n", ")\n", "\n", "inventory = inventory[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"product_type\",\n", "        \"master_assortment_substate_id\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"total_reserved_qty\",\n", "        \"net_available_qty\",\n", "    ]\n", "].drop_duplicates()\n", "inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory[\"outlet_id\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Dividing inventory into GMV Buckets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_ = inventory.merge(\n", "    order_items[\n", "        [\n", "            \"outlet_id\",\n", "            \"item_id\",\n", "            \"quantity\",\n", "            \"selling_price\",\n", "            \"landing_price\",\n", "        ]\n", "    ],\n", "    on=[\"outlet_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "summary_ = summary_.drop_duplicates()\n", "summary_ = summary_.fillna(0)\n", "summary_[\"GMV\"] = round(summary_[\"quantity\"] * summary_[\"selling_price\"])\n", "summary_[\"GMV\"] = np.where(summary_[\"GMV\"] == 0, 1, summary_[\"GMV\"])\n", "summary_.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_[\"outlet_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_gmv = summary_.groupby([\"outlet_id\", \"product_type\"]).agg({\"GMV\": \"sum\"}).reset_index()\n", "net_gmv = net_gmv[net_gmv[\"GMV\"] > 0]\n", "net_gmv.rename(columns={\"GMV\": \"total_gmv\"}, inplace=True)\n", "net_gmv = net_gmv[net_gmv[\"product_type\"] != 0]\n", "net_gmv.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = summary_.merge(net_gmv, on=[\"outlet_id\", \"product_type\"], how=\"left\")\n", "summary.sort_values(\n", "    by=[\"outlet_id\", \"total_gmv\", \"product_type\", \"GMV\"], ascending=False, inplace=True\n", ")\n", "summary[\"cumsum\"] = summary.groupby([\"outlet_id\", \"product_type\"])[\"GMV\"].apply(\n", "    lambda x: x.cumsum()\n", ")\n", "summary.sort_values(\n", "    by=[\"outlet_id\", \"total_gmv\", \"product_type\", \"GMV\"], ascending=False, inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def bucket_flag(x):\n", "    if x[\"cumsum\"] / x[\"total_gmv\"] <= 0.95:\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "def updated_flag(x):\n", "    if x[\"net_available_qty\"] > 0 and x[\"gmv_flag\"] == 1:\n", "        return 1\n", "    else:\n", "        return 0\n", "\n", "\n", "summary[\"gmv_flag\"] = summary.apply(bucket_flag, axis=1)\n", "summary[\"available_flag\"] = summary.apply(updated_flag, axis=1)\n", "summary_ptype = (\n", "    summary.groupby([\"outlet_id\", \"product_type\"])\n", "    .agg({\"gmv_flag\": \"sum\", \"item_id\": \"nunique\", \"available_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "summary_ptype.rename(\n", "    columns={\n", "        \"item_id\": \"total_items\",\n", "        \"gmv_flag\": \"Total_gmv_items\",\n", "        \"available_flag\": \"Available_gmv_items\",\n", "    },\n", "    inplace=True,\n", ")\n", "summary21 = summary.merge(summary_ptype, on=[\"outlet_id\", \"product_type\"], how=\"left\")\n", "summary21.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary21.groupby([\"outlet_id\"]).agg({\"gmv_flag\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # summary2[summary2[\"outlet_id\"]==343]\n", "# sheet2 = \"1tvYnhlSwbwiW4CM6Yn9a3eIFwh70JxO6VFJlGpsUMAw\"\n", "# cater_ds_mapping = pb.from_sheets(sheet2, \"Mapping\")\n", "# cater_ds_mapping = cater_ds_mapping[[\"front_end\", \"dark_store\"]].drop_duplicates()\n", "# cater_ds_mapping.rename(\n", "#     columns={\"front_end\": \"outlet_id\", \"dark_store\": \"ds_outlet\"}, inplace=True\n", "# )\n", "# cater_ds_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cater_ds_mapping[\"outlet_id\"] = cater_ds_mapping[\"outlet_id\"].astype(\"int\")\n", "cater_ds_mapping[\"ds_outlet\"] = cater_ds_mapping[\"ds_outlet\"].astype(\"int\")\n", "\n", "summary2 = summary21.merge(cater_ds_mapping, on=[\"outlet_id\"], how=\"left\")\n", "summary2\n", "summary2.drop([\"outlet_id\"], axis=1, inplace=True)\n", "summary2 = summary2[\n", "    [\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"product_type\",\n", "        \"master_assortment_substate_id\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"total_reserved_qty\",\n", "        \"net_available_qty\",\n", "        \"quantity\",\n", "        \"selling_price\",\n", "        \"landing_price\",\n", "        \"GMV\",\n", "        \"total_gmv\",\n", "        \"cumsum\",\n", "        \"gmv_flag\",\n", "        \"available_flag\",\n", "        \"Total_gmv_items\",\n", "        \"total_items\",\n", "        \"Available_gmv_items\",\n", "    ]\n", "].drop_duplicates()\n", "summary2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DS_available_ptype_ = DS_available_ptype.merge(cater_ds_mapping, on=[\"ds_outlet\"], how=\"inner\")\n", "DS_available_ptype_.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DS_available_ptype.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### DS available items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DS_active_available = DS_available_ptype_.merge(\n", "    active_items, on=[\"outlet_id\", \"item_id\"], how=\"left\"\n", ")\n", "DS_active_available = DS_active_available[DS_active_available[\"ds_available_qty\"] > 0]\n", "DS_active_available.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### DS + Backened active available inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary3 = summary2.merge(\n", "    DS_active_available[[\"ds_outlet\", \"item_id\", \"product_type\", \"DS_available_flag\"]],\n", "    on=[\"ds_outlet\", \"item_id\", \"product_type\"],\n", "    how=\"outer\",\n", ")\n", "summary3 = summary3.fillna(0)\n", "summary3 = summary3.drop_duplicates()\n", "summary3.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Removing FnV items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary4 = summary3.merge(fnv_items, on=[\"item_id\"], how=\"left\")\n", "summary4 = summary4[summary4[\"outlet_type\"] != 1]\n", "summary4.drop([\"outlet_type\"], axis=1, inplace=True)\n", "summary4.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary5_ = summary4[summary4[\"DS_available_flag\"] > 0]\n", "available_ptypes = (\n", "    summary5_.groupby([\"ds_outlet\", \"product_type\"]).agg({\"item_id\": \"nunique\"}).reset_index()\n", ")\n", "available_ptypes.rename(columns={\"item_id\": \"ds_total_available_items\"}, inplace=True)\n", "summary6 = summary4.merge(available_ptypes, on=[\"ds_outlet\", \"product_type\"], how=\"left\")\n", "summary6[[\"DS_available_flag\", \"ds_total_available_items\"]] = summary6[\n", "    [\"DS_available_flag\", \"ds_total_available_items\"]\n", "].fillna(0)\n", "summary6[[\"DS_available_flag\", \"ds_total_available_items\"]] = summary6[\n", "    [\"DS_available_flag\", \"ds_total_available_items\"]\n", "].astype(\"int\")\n", "total_available_items = summary6[summary6[\"net_available_qty\"] > 0]\n", "total_available_items1 = (\n", "    total_available_items.groupby([\"ds_outlet\", \"product_type\"])\n", "    .agg({\"item_id\": \"nunique\"})\n", "    .reset_index()\n", ")\n", "total_available_items1.rename(columns={\"item_id\": \"total_available_items_backened\"}, inplace=True)\n", "summary7 = summary6.merge(total_available_items1, on=[\"ds_outlet\", \"product_type\"], how=\"left\")\n", "summary7 = summary7.fillna(0)\n", "summary7[\"constant\"] = 1\n", "summary7[\"rank\"] = summary7.groupby([\"ds_outlet\", \"product_type\"])[\"constant\"].apply(\n", "    lambda x: x.cumsum()\n", ")\n", "summary7.drop([\"constant\"], axis=1, inplace=True)\n", "summary7[\"Assortment_flag\"] = 0\n", "summary7[\"not_replenish_flag\"] = 0\n", "summary7 = summary7.dropna()\n", "summary7 = summary7.replace(\"\", 0)\n", "summary7.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### hardcoding items of few ptypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary7[\"Total_gmv_items\"] = np.where(\n", "    summary7[\"product_type\"] == \"Diaper\", 30, summary7[\"Total_gmv_items\"]\n", ")\n", "summary7[\"Total_gmv_items\"] = np.where(\n", "    summary7[\"product_type\"] == \"Shampoo\", 26, summary7[\"Total_gmv_items\"]\n", ")\n", "summary7[\"Total_gmv_items\"] = np.where(\n", "    summary7[\"product_type\"] == \"Juice\", 24, summary7[\"Total_gmv_items\"]\n", ")\n", "summary7[\"Total_gmv_items\"] = np.where(\n", "    summary7[\"product_type\"] == \"Health Drink\", 17, summary7[\"Total_gmv_items\"]\n", ")\n", "summary7.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Storing all ptypes in list x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet = []\n", "for a in summary7[\"ds_outlet\"]:\n", "    if a not in outlet:\n", "        outlet.append(a)\n", "\n", "outlet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = summary7.loc[:0]\n", "i = 3  ### min item defined\n", "\n", "for outlets in outlet:\n", "    x = []\n", "    df_new = summary7[summary7[\"ds_outlet\"] == outlets]\n", "    for ptypes in df_new[\"product_type\"]:\n", "        if ptypes not in x:\n", "            x.append(ptypes)\n", "    for p_types in x:\n", "        df1 = df_new[df_new[\"product_type\"] == p_types]\n", "        flag = df1.iloc[0, 17]\n", "        for index, row in df1.iterrows():\n", "\n", "            if row[\"rank\"] <= 3:\n", "                if row[\"net_available_qty\"] > 0:\n", "                    df1.set_value(index, \"Assortment_flag\", 1)\n", "\n", "                elif row[\"available_flag\"] == 0 and row[\"DS_available_flag\"] == 1:\n", "                    df1.set_value(index, \"Assortment_flag\", 1)\n", "                    df1.set_value(index, \"not_replenish_flag\", 1)\n", "\n", "            else:\n", "\n", "                if (\n", "                    row[\"gmv_flag\"] == 1\n", "                    and row[\"available_flag\"] == 1\n", "                    and row[\"Assortment_flag\"] == 0\n", "                    and row[\"DS_available_flag\"] == 1\n", "                ):\n", "                    df1.set_value(index, \"Assortment_flag\", 1)\n", "\n", "                elif (\n", "                    row[\"gmv_flag\"] == 1\n", "                    and row[\"available_flag\"] == 0\n", "                    and row[\"DS_available_flag\"] == 1\n", "                    and row[\"Assortment_flag\"] == 0\n", "                ):\n", "                    df1.set_value(index, \"Assortment_flag\", 1)\n", "                    df1.set_value(index, \"not_replenish_flag\", 1)\n", "\n", "                elif row[\"gmv_flag\"] == 0 and row[\"DS_available_flag\"] == 1:\n", "                    df1.set_value(index, \"Assortment_flag\", 1)\n", "                    df1.set_value(index, \"not_replenish_flag\", 1)\n", "\n", "        sum = df1[\"Assortment_flag\"].sum()\n", "        counter = min(flag, df1.iloc[0, 22])\n", "\n", "        if sum < counter:\n", "            while sum < counter:\n", "                for index, row1 in df1.iterrows():\n", "                    if (\n", "                        row1[\"gmv_flag\"] == 1\n", "                        and row1[\"available_flag\"] == 1\n", "                        and row1[\"Assortment_flag\"] == 0\n", "                    ):\n", "                        df1.set_value(index, \"Assortment_flag\", 1)\n", "                        sum = sum + 1\n", "                        if sum == counter:\n", "                            break\n", "\n", "                    if sum < counter:\n", "                        if (\n", "                            row1[\"gmv_flag\"] == 0\n", "                            and row1[\"net_available_qty\"] > 0\n", "                            and row1[\"Assortment_flag\"] == 0\n", "                        ):\n", "                            df1.set_value(index, \"Assortment_flag\", 1)\n", "                            sum = sum + 1\n", "                            if sum == counter:\n", "                                break\n", "\n", "        flag2 = df1[\"Assortment_flag\"].sum()\n", "        counta = min(i, df1.iloc[0, 22])\n", "        diff = min(i, df1.iloc[0, 22]) - df1[\"Assortment_flag\"].sum()\n", "\n", "        if diff > 0:\n", "            while diff > 0:\n", "                for index, row2 in df1.iterrows():\n", "                    if row2[\"net_available_qty\"] > 0 and row2[\"Assortment_flag\"] == 0:\n", "                        df1.set_value(index, \"Assortment_flag\", 1)\n", "                        diff = diff - 1\n", "                        if diff == 0:\n", "                            break\n", "\n", "        df = pd.concat([df, df1])\n", "\n", "\n", "df = df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.groupby([\"ds_outlet\"]).agg({\"Assortment_flag\": \"sum\"}).reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Final assortment with notify me included items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_skus_v2 = df.merge(\n", "    notify_me_items, on=[\"ds_outlet\", \"item_id\", \"name\", \"product_type\"], how=\"outer\"\n", ")\n", "final_skus_v2[\"notify_me_flag\"] = final_skus_v2[\"notify_me_flag\"].fillna(0)\n", "final_skus_v2[\"Assortment_flag\"] = final_skus_v2[\"Assortment_flag\"].fillna(1)\n", "# final_skus_v2[\"flag\"]=final_skus_v2[\"flag\"].fillna(0)\n", "final_skus_v2.sort_values(by=[\"notify_me_flag\"], ascending=False)\n", "\n", "\n", "def notify_me(x):\n", "    if x[\"net_available_qty\"] > 0 and x[\"notify_me_flag\"] == 1:\n", "        return 1\n", "    elif x[\"Assortment_flag\"] == 1 and x[\"not_replenish_flag\"] == 0:\n", "        return 1\n", "\n", "\n", "final_skus_v2[\"Final_replenish_flag\"] = final_skus_v2.apply(notify_me, axis=1)\n", "final_skus_v2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_skus_v2.groupby([\"ds_outlet\"]).agg({\"Assortment_flag\": \"sum\"}).reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Incorporating search ptypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = final_skus_v2.merge(search_ptypes, on=[\"product_type\"], how=\"left\")\n", "assortment[\"search_flag\"] = assortment[\"search_flag\"].fillna(0)\n", "assortment = assortment.fillna(0)\n", "assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = assortment.dropna()\n", "df5 = assortment.loc[:0]\n", "for outlets in outlet:\n", "    x = []\n", "    df_new = assortment[assortment[\"ds_outlet\"] == outlets]\n", "    for ptypes in df_new[\"product_type\"]:\n", "        if ptypes not in x:\n", "            x.append(ptypes)\n", "    for p_types in x:\n", "        df6 = df_new[df_new[\"product_type\"] == p_types]\n", "        initial = 0\n", "        available_items = df6[df6[\"net_available_qty\"] > 0][\"item_id\"].nunique()\n", "        condition = min(5, available_items)\n", "        for index, row in df6.iterrows():\n", "\n", "            if initial < condition:\n", "                if row[\"search_flag\"] == 1 and row[\"net_available_qty\"] > 0:\n", "                    df6.set_value(index, \"Assortment_flag\", 1)\n", "                    df6.set_value(index, \"not_replenish_flag\", 0)\n", "                    df6.set_value(index, \"Final_replenish_flag\", 1)\n", "                    initial = initial + 1\n", "                    if initial == condition:\n", "                        break\n", "\n", "        df5 = pd.concat([df5, df6])\n", "\n", "df5 = df5.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = df5.copy()\n", "assortment.drop(assortment.index[[0]], inplace=True)\n", "assortment = assortment[assortment[\"product_type\"] != 0]\n", "assortment = assortment[assortment[\"ds_outlet\"] != 0]\n", "assortment = assortment[assortment[\"product_type\"] != \"Toy Gun\"]\n", "assortment.groupby([\"ds_outlet\"]).agg({\"Assortment_flag\": \"sum\"}).reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CPD Transfer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = \"\"\"\n", "select distinct wom.cloud_store_id as ds_outlet, dwc.date as date,dwc.item_id, dwc.consumption as cpd \n", "    from \n", "    (select outlet_id,item_id,date,consumption from snorlax.date_wise_consumption where \n", "    date = current_date + interval 1 day) dwc\n", "    join \n", "    retail.warehouse_outlet_mapping wom on dwc.outlet_id = wom.warehouse_id\n", "    where wom.cloud_store_id in %(outlet)s\n", "\"\"\"\n", "cpd = pd.read_sql_query(cpd_query, retail, params={\"outlet\": ds_outlets})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd = cpd.groupby([\"ds_outlet\", \"item_id\"]).agg({\"cpd\": \"sum\"}).reset_index()\n", "cpd.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment[\"ds_outlet\"] = assortment[\"ds_outlet\"].astype(\"int\")\n", "cpd[\"ds_outlet\"] = cpd[\"ds_outlet\"].astype(\"int\")\n", "cannabilisation = assortment.merge(cpd, on=[\"ds_outlet\", \"item_id\"], how=\"left\")\n", "cannabilisation[\"cpd\"] = cannabilisation[\"cpd\"].fillna(0)\n", "gmv_item_cpd = cannabilisation[\n", "    (cannabilisation[\"gmv_flag\"] == 1) & (cannabilisation[\"Assortment_flag\"] == 1)\n", "]\n", "gmv_item_cpd = gmv_item_cpd.groupby([\"ds_outlet\", \"product_type\"]).agg({\"cpd\": \"sum\"}).reset_index()\n", "gmv_item_cpd.rename(columns={\"cpd\": \"gmv_cpd\"}, inplace=True)\n", "gmv_item_cpd\n", "cannabilisationv2 = cannabilisation.merge(\n", "    gmv_item_cpd, on=[\"ds_outlet\", \"product_type\"], how=\"left\"\n", ")\n", "available_item_cpd = cannabilisation[\n", "    (cannabilisation[\"gmv_flag\"] == 1) & (cannabilisation[\"Final_replenish_flag\"] == 1)\n", "]\n", "available_item_cpd = (\n", "    available_item_cpd.groupby([\"ds_outlet\", \"product_type\"]).agg({\"cpd\": \"sum\"}).reset_index()\n", ")\n", "available_item_cpd\n", "available_item_cpd.rename(columns={\"cpd\": \"available_cpd\"}, inplace=True)\n", "cannabilisationv3 = cannabilisationv2.merge(\n", "    available_item_cpd, on=[\"ds_outlet\", \"product_type\"], how=\"left\"\n", ")\n", "cannabilisationv3[\"cpd_diff\"] = cannabilisationv3[\"gmv_cpd\"] - cannabilisationv3[\"available_cpd\"]\n", "\n", "cannabilisationv3[\"difference\"] = np.ceil(\n", "    cannabilisationv3[\"available_flag\"]\n", "    * (cannabilisationv3[\"cpd\"] / cannabilisationv3[\"available_cpd\"])\n", "    * cannabilisationv3[\"cpd_diff\"]\n", ")\n", "cannabilisationv3 = cannabilisationv3.fillna(0)\n", "cannabilisationv4 = cannabilisationv3.merge(\n", "    item_product_mapping, on=[\"item_id\", \"name\", \"product_type\"], how=\"left\"\n", ")\n", "cannabilisationv5 = cannabilisationv4.merge(\n", "    DS_available_finalv2, on=[\"ds_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "cannabilisationv5[[\"ds_quantity\", \"ds_blocked_quantity\", \"ds_available_qty\"]] = cannabilisationv5[\n", "    [\"ds_quantity\", \"ds_blocked_quantity\", \"ds_available_qty\"]\n", "].fillna(0)\n", "cannabilisationv5.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Adding fnv assortment from sheet above mentioned"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fnv_perishable_assortment[\"item_id\"] = fnv_perishable_assortment[\"item_id\"].astype(\"int\")\n", "fnv_perishable_assortmentv2 = fnv_perishable_assortment.merge(\n", "    item_product_mapping[[\"item_id\", \"product_type\", \"l0\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "fnv_perishable_assortmentv2[\"type_flag\"] = np.where(\n", "    fnv_perishable_assortmentv2[\"type_flag\"] == \"Perishable \",\n", "    \"Perishable\",\n", "    fnv_perishable_assortmentv2[\"type_flag\"],\n", ")\n", "\n", "bread = [\"Bread\", \"Sandwich Bread\", \"<PERSON><PERSON><PERSON>\", \"Brown Bread\"]\n", "fnv_perishable_assortmentv2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select id as ds_outlet , \n", "name as ds_outlet_name \n", "from  retail.console_outlet\"\"\"\n", "outlet = pd.read_sql_query(query, retail)\n", "outlet.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Flat table creation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = cannabilisationv5.copy()\n", "assortment = assortment.fillna(0)\n", "\n", "run_id = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "assortment = assortment[\n", "    [\n", "        \"ds_outlet\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"product_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"master_assortment_substate_id\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"total_reserved_qty\",\n", "        \"net_available_qty\",\n", "        \"ds_quantity\",\n", "        \"ds_blocked_quantity\",\n", "        \"ds_available_qty\",\n", "        \"rank\",\n", "        \"gmv_flag\",\n", "        \"available_flag\",\n", "        \"DS_available_flag\",\n", "        \"Assortment_flag\",\n", "        \"not_replenish_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"Final_replenish_flag\",\n", "        \"difference\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "assortmentv2 = assortment[\n", "    (assortment[\"l0\"] != \"Home & Kitchen\")\n", "    & (assortment[\"l0\"] != \"Furnishing & Home Needs\")\n", "    & (assortment[\"l0\"] != \"Fashion\")\n", "    & (assortment[\"l0\"] != \"0\")\n", "    & (assortment[\"l0\"] != \"Pet Care\")\n", "]\n", "\n", "\n", "fnv_perishable_assortmentv2[\"ds_outlet\"] = fnv_perishable_assortmentv2[\"ds_outlet\"].astype(\"int\")\n", "\n", "final_assortment = assortmentv2.merge(\n", "    fnv_perishable_assortmentv2,\n", "    on=[\"ds_outlet\", \"item_id\", \"product_type\", \"l0\", \"name\"],\n", "    how=\"outer\",\n", ")\n", "final_assortment[\"type_flag\"] = final_assortment[\"type_flag\"].fillna(\"Grocery\")\n", "final_assortment[\"Final_replenish_flag\"] = final_assortment[\"Final_replenish_flag\"].fillna(0)\n", "final_assortment[\"Assortment_flag\"] = final_assortment[\"Assortment_flag\"].fillna(1)\n", "final_assortment[\"not_replenish_flag\"] = final_assortment[\"not_replenish_flag\"].fillna(1)\n", "final_assortment = final_assortment.fillna(0)\n", "final_assortment[\"run_id\"] = run_id\n", "final_assortment[\"type_flag\"] = np.where(\n", "    final_assortment[\"product_type\"].isin(bread),\n", "    \"Perishable\",\n", "    final_assortment[\"type_flag\"],\n", ")\n", "final_assortment[\"Assortment_flag\"] = np.where(\n", "    final_assortment[\"type_flag\"] == \"Perishable\",\n", "    1,\n", "    final_assortment[\"Assortment_flag\"],\n", ")\n", "final_assortment[\"Final_replenish_flag\"] = np.where(\n", "    final_assortment[\"type_flag\"] == \"Perishable\",\n", "    0,\n", "    final_assortment[\"Final_replenish_flag\"],\n", ")\n", "final_assortment[\"not_replenish_flag\"] = np.where(\n", "    final_assortment[\"type_flag\"] == \"Perishable\",\n", "    1,\n", "    final_assortment[\"not_replenish_flag\"],\n", ")\n", "final_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentv1 = final_assortment.merge(cater_ds_mapping, on=[\"ds_outlet\"], how=\"inner\")\n", "final_assortmentv1.rename(columns={\"outlet_id\": \"cater_outlet\"}, inplace=True)\n", "final_assortmentv2 = final_assortmentv1.merge(outlet, on=[\"ds_outlet\"], how=\"left\")\n", "final_assortmentv2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_assortmentv2[\n", "    [\n", "        \"master_assortment_substate_id\",\n", "        \"gmv_flag\",\n", "        \"available_flag\",\n", "        \"DS_available_flag\",\n", "        \"gmv_flag\",\n", "        \"Assortment_flag\",\n", "        \"not_replenish_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"net_available_qty\",\n", "        \"Final_replenish_flag\",\n", "        \"rank\",\n", "        \"ds_outlet\",\n", "        \"difference\",\n", "        \"cater_outlet\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"total_reserved_qty\",\n", "        \"ds_quantity\",\n", "        \"ds_blocked_quantity\",\n", "        \"ds_available_qty\",\n", "    ]\n", "] = final_assortmentv2[\n", "    [\n", "        \"master_assortment_substate_id\",\n", "        \"gmv_flag\",\n", "        \"available_flag\",\n", "        \"DS_available_flag\",\n", "        \"gmv_flag\",\n", "        \"Assortment_flag\",\n", "        \"not_replenish_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"net_available_qty\",\n", "        \"Final_replenish_flag\",\n", "        \"rank\",\n", "        \"ds_outlet\",\n", "        \"difference\",\n", "        \"cater_outlet\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"total_reserved_qty\",\n", "        \"ds_quantity\",\n", "        \"ds_blocked_quantity\",\n", "        \"ds_available_qty\",\n", "    ]\n", "].astype(\n", "    \"int\"\n", ")\n", "\n", "final_assortmentv2 = final_assortmentv2[\n", "    [\n", "        \"run_id\",\n", "        \"cater_outlet\",\n", "        \"ds_outlet\",\n", "        \"ds_outlet_name\",\n", "        \"item_id\",\n", "        \"name\",\n", "        \"product_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"master_assortment_substate_id\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"total_reserved_qty\",\n", "        \"net_available_qty\",\n", "        \"ds_quantity\",\n", "        \"ds_blocked_quantity\",\n", "        \"ds_available_qty\",\n", "        \"rank\",\n", "        \"gmv_flag\",\n", "        \"available_flag\",\n", "        \"DS_available_flag\",\n", "        \"Assortment_flag\",\n", "        \"not_replenish_flag\",\n", "        \"notify_me_flag\",\n", "        \"search_flag\",\n", "        \"Final_replenish_flag\",\n", "        \"difference\",\n", "        \"type_flag\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "final_assortmentv2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"run_id\", \"type\": \"varchar\"},\n", "    {\"name\": \"cater_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"ds_outlet\", \"type\": \"int\"},\n", "    {\"name\": \"ds_outlet_name\", \"type\": \"varchar\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"name\", \"type\": \"varchar\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\"},\n", "    {\"name\": \"brand\", \"type\": \"varchar\"},\n", "    {\"name\": \"manf\", \"type\": \"varchar\"},\n", "    {\"name\": \"master_assortment_substate_id\", \"type\": \"int\"},\n", "    {\"name\": \"actual_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"total_reserved_qty\", \"type\": \"int\"},\n", "    {\"name\": \"net_available_qty\", \"type\": \"int\"},\n", "    {\"name\": \"ds_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"ds_blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"ds_available_qty\", \"type\": \"int\"},\n", "    {\"name\": \"rank\", \"type\": \"int\"},\n", "    {\"name\": \"gmv_flag\", \"type\": \"int\"},\n", "    {\"name\": \"available_flag\", \"type\": \"int\"},\n", "    {\"name\": \"DS_available_flag\", \"type\": \"int\"},\n", "    {\"name\": \"Assortment_flag\", \"type\": \"int\"},\n", "    {\"name\": \"not_replenish_flag\", \"type\": \"int\"},\n", "    {\"name\": \"notify_me_flag\", \"type\": \"int\"},\n", "    {\"name\": \"search_flag\", \"type\": \"int\"},\n", "    {\"name\": \"Final_replenish_flag\", \"type\": \"int\"},\n", "    {\"name\": \"difference\", \"type\": \"int\"},\n", "    {\"name\": \"type_flag\", \"type\": \"varchar\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"Dark_store_assortment_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"ds_outlet\", \"item_id\", \"name\", \"product_type\"],\n", "    \"sortkey\": [\"ds_outlet\", \"product_type\", \"rank\"],\n", "    \"incremental_key\": [\"run_id\"],\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_redshift(final_assortmentv2, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}