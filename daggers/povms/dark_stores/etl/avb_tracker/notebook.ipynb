{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime\n", "import warnings\n", "import numpy as np\n", "from functools import reduce\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pytz import timezone\n", "\n", "now_india = datetime.datetime.now(timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1M-Zw_JBzGL_0QHbXOtWrNkxLTO0KNYgjj1qL6chZ8KQ\"\n", "sheet_name = \"auto_mail_config\"\n", "mail_config = pb.from_sheets(\n", "    sheet_id, sheet_name, service_account=\"service_account\", clear_cache=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["intf = [int(x) for x in mail_config.send_mail_bw[0].split(\",\")]\n", "fromt = intf[0]\n", "tot = intf[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now = datetime.datetime.now()\n", "today11 = now.replace(hour=fromt, minute=0, second=0, microsecond=0)\n", "today12 = now.replace(hour=tot, minute=0, second=0, microsecond=0)\n", "sendmail = False  # now >= today11 and now <= today12\n", "custom_send = mail_config.send_mail_bw[1] == \"True\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "    CON_SQL = pb.get_connection(\"retail\")\n", "    CON_SUPPLY = pb.get_connection(\"supply_orchestrator\")\n", "except:\n", "    CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "    CON_SQL = pb.get_connection(\"retail\")\n", "    CON_SUPPLY = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "raw", "metadata": {}, "source": ["try:\n", "    CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "    CON_SQL = pb.get_connection(\"retail\")\n", "    CON_SUPPLY = pb.get_connection(\"supply_orchestrator\")\n", "except:\n", "    CON_REDSHIFT = pb.get_connection(\"redshift\")\n", "    CON_SQL = pb.get_connection(\"retail\")\n", "    CON_SUPPLY = pb.get_connection(\"supply_orchestrator\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = \"\"\"select m.ds_outlet as dark_store,\n", "m.catering_outlet as front_end,\n", "m.item_id,type_flag\n", "from metrics.dark_store_assortment_static m \n", "inner join (SELECT max(updated_at) AS updated_at\n", "  FROM metrics.dark_store_assortment_static) a ON m.updated_at=a.updated_at\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x in range(3):\n", "    try:\n", "        assortment_df = pd.read_sql_query(sql=assortment, con=CON_REDSHIFT)\n", "        break\n", "    except:\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores_list = list(assortment_df.dark_store.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores = \", \".join([str(x) for x in dark_stores_list])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list = list(assortment_df.item_id.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = \", \".join([str(x) for x in items_list])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cater_outlets_list = list(assortment_df.front_end.unique())"]}, {"cell_type": "raw", "metadata": {}, "source": ["assortment_df = pd.read_sql_query(sql=assortment, con=CON_REDSHIFT)"]}, {"cell_type": "raw", "metadata": {}, "source": ["dark_stores_list = list(assortment_df.dark_store.unique())"]}, {"cell_type": "raw", "metadata": {}, "source": ["dark_stores = \", \".join([str(x) for x in dark_stores_list])"]}, {"cell_type": "raw", "metadata": {}, "source": ["items_list = list(assortment_df.item_id.unique())"]}, {"cell_type": "raw", "metadata": {}, "source": ["items = \", \".join([str(x) for x in items_list])"]}, {"cell_type": "raw", "metadata": {}, "source": ["cater_outlets_list = list(assortment_df.front_end.unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cater_outlets = \", \".join([str(x) for x in cater_outlets_list])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_query = \"\"\"\n", "WITH sto_max AS\n", "  (SELECT a.receiving_outlet_id as ds_outlet_id,\n", "          a.item_id,\n", "          sum(case when a.invoice_state = 'NA' then a.reserved_quantity else 0 end) +\n", "          sum(case when a.invoice_state = 'Raised' then a.billed_quantity else 0 end) as recieving_quantity\n", "   FROM metrics.esto a\n", "   WHERE a.receiving_outlet_id IN ({dark_stores}) \n", "   AND a.item_id IN ({items})\n", "   AND a.invoice_state IN ('NA','Raised')\n", "   AND a.sto_state != 'Expired'\n", "   GROUP BY \n", "   a.receiving_outlet_id,\n", "   a.item_id)\n", "select * from sto_max\n", "\"\"\".format(\n", "    dark_stores=dark_stores, items=items\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_query = \"\"\"\n", "SELECT good_inv.outlet_id AS ds_outlet_id,\n", "       product.item_id,\n", "       sum(CASE\n", "               WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity ELSE 0\n", "           END) AS Actual_Quantity,\n", "           0 as Order_Blocked_Quantity,\n", "           0 as STO_Blocked_Quantity,\n", "           sum(CASE\n", "               WHEN good_inv.inventory_update_type_id IN (96) THEN good_inv.quantity ELSE 0\n", "           END) AS Net_Quantity\n", "FROM ims.ims_good_inventory good_inv\n", "INNER JOIN rpc.product_product product ON product.variant_id = good_inv.variant_id\n", "WHERE good_inv.active = 1\n", "  AND good_inv.outlet_id IN ({outlets})\n", "  AND product.item_id IN ({items})\n", "GROUP BY 1,\n", "         2\n", "UNION\n", "SELECT iii.outlet_id as ds_outlet_id,\n", "       iii.item_id,\n", "       iii.quantity AS Actual_Quantity,\n", "       sum((CASE\n", "                WHEN iibi.blocked_type = 1 THEN iibi.quantity\n", "                ELSE 0\n", "            END)) AS Order_Blocked_Quantity,\n", "       sum((CASE\n", "                WHEN iibi.blocked_type = 2 THEN iibi.quantity\n", "                ELSE 0\n", "            END)) AS STO_Blocked_Quantity,\n", "       CASE\n", "           WHEN (iii.quantity - COALESCE(sum(iibi.quantity),0))>0 THEN (iii.quantity - COALESCE(sum(iibi.quantity),0))\n", "           ELSE 0\n", "       END AS Net_Quantity\n", "FROM ims.ims_item_inventory iii\n", "LEFT JOIN ims.ims_item_blocked_inventory iibi ON iii.item_id=iibi.item_id\n", "AND iii.outlet_id=iibi.outlet_id\n", "WHERE iibi.blocked_type IN (1,\n", "                            2)\n", "  AND iii.outlet_id IN ({outlets})\n", "  AND iii.item_id IN ({items})\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "\"\"\".format(\n", "    outlets=dark_stores, items=items\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_query = \"\"\"\n", "with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\n", "   AND rpc.item_id IN ({items})\n", "\"\"\".format(\n", "    items=items\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cms_outlet_details_query = \"\"\"\n", "select distinct cms.outlet_id,o.name as outlet,cms.cms_store\n", "from\n", "lake_oms_bifrost.oms_merchant m\n", "join lake_retail.console_outlet_cms_store cms on m.external_id = cms.cms_store\n", "join lake_retail.console_outlet o on cms.outlet_id= o.id\n", "where\n", "cms.active = 1 and cms.cms_update_active = 1\n", "\"\"\"\n", "\n", "capacity_query = \"\"\"\n", "select distinct backend_merchant_id,\n", "backend_merchant_name,\n", "delivery_date,\n", "sum(given_capacity) as planned_capacity,\n", "sum(capacity_utilised) as actual_capacity\n", "from\n", "(SELECT \n", "\twarehouse_external_id AS backend_merchant_id,\n", "\twarehouse_name AS backend_merchant_name,\n", "\t\n", "\tDATE(slot_start AT TIME ZONE 'ASIA/KOLKATA') AS delivery_date,\n", "\tTO_CHAR(slot_start AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') || ' - ' || \n", "\tTO_CHAR(slot_end AT TIME ZONE 'ASIA/KOLKATA','HH24:MI:SS') AS delivery_slot,\n", "\tslot_type,\n", "\t\n", "\twarehouse_asked AS asked_capcity,\n", "\twarehouse_planned AS given_capacity,\n", "\twarehouse_actual AS capacity_utilised,\n", "        warehouse_available AS capacity_under_utilised,\n", "\n", "        min(update_ts) AS update_ts\n", "\t\n", "FROM sco_path_capacity\n", "WHERE date(slot_start) >= date(now())\n", "and warehouse_name not like '%%Cold%%'\n", "and warehouse_planned < 9999\n", "GROUP BY 1,2,3,4,5,6,7,8,9\n", "ORDER BY 2,3,4,5) a\n", "where\n", "delivery_date between date(current_date) and date(current_date + interval '15 days')\n", "and given_capacity > 0\n", "group by 1,2,3\n", "order by 2,3;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["capacity_details = pd.read_sql_query(sql=capacity_query, con=CON_SUPPLY)\n", "cms_outlet_details = pd.read_sql_query(sql=cms_outlet_details_query, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["warehouse_capacities = (\n", "    capacity_details.merge(\n", "        cms_outlet_details,\n", "        how=\"inner\",\n", "        left_on=[\"backend_merchant_id\"],\n", "        right_on=[\"cms_store\"],\n", "    )[[\"outlet_id\", \"outlet\", \"delivery_date\", \"planned_capacity\"]]\n", "    .rename(columns={\"planned_capacity\": \"Capacity\"})\n", "    .drop_duplicates()\n", ")\n", "warehouse_capacities = warehouse_capacities[\n", "    warehouse_capacities.outlet_id.isin([int(x) for x in dark_stores.split(\",\")])\n", "]\n", "warehouse_capacities[\"delivery_date\"] = pd.to_datetime(warehouse_capacities[\"delivery_date\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1M-Zw_JBzGL_0QHbXOtWrNkxLTO0KNYgjj1qL6chZ8KQ\"\n", "sheet_name = \"order_capacity_raw_v2\"\n", "pb.to_sheets(\n", "    warehouse_capacities,\n", "    sheet_id,\n", "    sheet_name,\n", "    service_account=\"service_account\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["buckets = pd.read_sql_query(sql=buckets_query, con=CON_REDSHIFT,)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_df = pd.read_sql_query(\n", "    sql=sto_query,\n", "    con=CON_REDSHIFT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_df = pd.read_sql_query(\n", "    sql=inventory_query.replace(\"{{\", \"{\").replace(\"}}\", \"}\"),\n", "    con=CON_SQL,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_df = inventory_df.groupby([\"ds_outlet_id\", \"item_id\"]).agg(\n", "    {\n", "        \"Actual_Quantity\": \"sum\",\n", "        \"Order_Blocked_Quantity\": \"sum\",\n", "        \"STO_Blocked_Quantity\": \"sum\",\n", "        \"Net_Quantity\": \"sum\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_df = inventory_df.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_attributes_df = pd.read_sql_query(\n", "    sql=item_attributes_query,\n", "    con=CON_REDSHIFT,\n", ")\n", "item_attributes_df = item_attributes_df.groupby([\"item_id\"]).max().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v0 = pd.merge(\n", "    assortment_df,\n", "    inventory_df,\n", "    how=\"left\",\n", "    left_on=[\"dark_store\", \"item_id\"],\n", "    right_on=[\"ds_outlet_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v1 = pd.merge(\n", "    final_v0,\n", "    sto_df,\n", "    how=\"left\",\n", "    left_on=[\"dark_store\", \"item_id\"],\n", "    right_on=[\"ds_outlet_id\", \"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v1[\"Actual_Quantity\"] = final_v1[\"Actual_Quantity\"].fillna(0)\n", "final_v1[\"Order_Blocked_Quantity\"] = final_v1[\"Order_Blocked_Quantity\"].fillna(0)\n", "final_v1[\"STO_Blocked_Quantity\"] = final_v1[\"STO_Blocked_Quantity\"].fillna(0)\n", "final_v1[\"Net_Quantity\"] = final_v1[\"Net_Quantity\"].fillna(0)\n", "final_v1[\"recieving_quantity\"] = final_v1[\"recieving_quantity\"].fillna(0)\n", "final_v1[\"ds_outlet_id\"] = final_v1[\"dark_store\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v2 = pd.merge(\n", "    final_v1,\n", "    item_attributes_df,\n", "    how=\"left\",\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v2[\"after_tf_qty\"] = final_v2[\"Net_Quantity\"] + final_v2[\"recieving_quantity\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v5 = final_v2[\n", "    [\n", "        \"dark_store\",\n", "        \"front_end\",\n", "        \"item_id\",\n", "        \"type_flag\",\n", "        \"Actual_Quantity\",\n", "        \"Order_Blocked_Quantity\",\n", "        \"STO_Blocked_Quantity\",\n", "        \"Net_Quantity\",\n", "        \"recieving_quantity\",\n", "        \"ds_outlet_id\",\n", "        \"product_id\",\n", "        \"name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"brand\",\n", "        \"manf\",\n", "        \"product_type\",\n", "        \"after_tf_qty\",\n", "    ]\n", "]"]}, {"cell_type": "raw", "metadata": {}, "source": ["sheet_id = \"1DuD297m9mzK0u4gTi_-JVadU7fkLgYnS5BbMUY1hXJY\"\n", "case_error = pb.from_sheets(\n", "    sheet_id, \"case_error\", service_account=\"service_account\", clear_cache=True\n", ")\n", "in_transit = pb.from_sheets(\n", "    sheet_id, \"in_transit\", service_account=\"service_account\", clear_cache=True\n", ")\n", "stock_unavailable = pb.from_sheets(\n", "    sheet_id, \"stock_unavailable\", service_account=\"service_account\", clear_cache=True\n", ")\n", "optimization = pb.from_sheets(\n", "    sheet_id, \"optimization\", service_account=\"service_account\", clear_cache=True\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["case_error = case_error.astype({\"front_end\": int, \"item_id\": int})\n", "stock_unavailable = stock_unavailable.astype({\"front_end\": int, \"item_id\": int})"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit = in_transit.astype({\"dark_store\": int, \"item_id\": int})"]}, {"cell_type": "raw", "metadata": {}, "source": ["optimization = optimization.astype(\n", "    {\"front_end\": int, \"dark_store\": int, \"item_id\": int}\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v1 = pd.merge(\n", "    final_v5,\n", "    in_transit,\n", "    how=\"left\",\n", "    left_on=[\"item_id\", \"dark_store_outlet\"],\n", "    right_on=[\"item_id\", \"dark_store\"],\n", "    suffixes=(\"\", \"_y\"),\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v2 = pd.merge(\n", "    in_transit_v1,\n", "    case_error,\n", "    how=\"left\",\n", "    left_on=[\"item_id\", \"cater_outlet\"],\n", "    right_on=[\"item_id\", \"front_end\"],\n", "    suffixes=(\"\", \"_y\"),\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v3 = pd.merge(\n", "    in_transit_v2,\n", "    stock_unavailable,\n", "    how=\"left\",\n", "    left_on=[\"item_id\", \"cater_outlet\"],\n", "    right_on=[\"item_id\", \"front_end\"],\n", "    suffixes=(\"\", \"_y\"),\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v4 = pd.merge(\n", "    in_transit_v3,\n", "    optimization,\n", "    how=\"left\",\n", "    left_on=[\"item_id\", \"cater_outlet\", \"dark_store_outlet\"],\n", "    right_on=[\"item_id\", \"front_end\", \"dark_store\"],\n", "    suffixes=(\"\", \"_y\"),\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v4 = in_transit_v4.drop(\n", "    in_transit_v4.filter(regex=\"_y$\").columns.tolist(), axis=1\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["def stock_out_reason(x):\n", "    stock_out_reason_1 = x[\"stock_out_reason_1\"]\n", "    stock_out_reason_2 = x[\"stock_out_reason_2\"]\n", "    stock_out_reason_3 = x[\"stock_out_reason_3\"]\n", "    stock_out_reason_4 = x[\"stock_out_reason_4\"]\n", "    manual_indent = x[\"manual_indent\"]\n", "    if stock_out_reason_2 == \"In transit\":\n", "        return stock_out_reason_2\n", "    elif stock_out_reason_1 == \"Case Error\":\n", "        return stock_out_reason_1\n", "    elif manual_indent == 1:\n", "        return \"Manual Indent\"\n", "    elif stock_out_reason_3 == \"Stock Unavailable\":\n", "        return stock_out_reason_3\n", "    elif (\n", "        stock_out_reason_4 == \"Truck Size issue\"\n", "        or stock_out_reason_3 == \"Storage issue\"\n", "    ):\n", "        return stock_out_reason_4\n", "    else:\n", "        return \"Wrong Forecast\""]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v4[\"stock_out_reason\"] = in_transit_v4.apply(stock_out_reason, axis=1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v4 = in_transit_v4[in_transit_v4.Net_Quantity < 1]"]}, {"cell_type": "raw", "metadata": {}, "source": ["l = in_transit_v4[[\"dark_store\", \"stock_out_reason\", \"item_id\"]].pivot_table(\n", "    index=\"dark_store\", columns=\"stock_out_reason\", aggfunc=len, fill_value=0\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["l[\"Total OOS\"] = l.sum(axis=1, skipna=True)"]}, {"cell_type": "raw", "metadata": {}, "source": ["l1 = l.div(l.iloc[:, -1], axis=0)"]}, {"cell_type": "raw", "metadata": {}, "source": ["l1 = l1.xs(\"item_id\", axis=1, drop_level=True).reset_index()"]}, {"cell_type": "raw", "metadata": {}, "source": ["in_transit_v4.stock_out_reason.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1M-Zw_JBzGL_0QHbXOtWrNkxLTO0KNYgjj1qL6chZ8KQ\"\n", "sheet_name = \"raw_data_v2\"\n", "pb.to_sheets(final_v5, sheet_id, sheet_name, service_account=\"service_account\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v5[\"etl_timestamp_ist\"] = now_india"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v5.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x in [\"name\", \"l0\", \"l1\", \"l2\", \"brand\", \"manf\", \"product_type\"]:\n", "    final_v5[x] = final_v5[x].fillna(\"Not available\")\n", "final_v5[\"product_id\"] = final_v5[\"product_id\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v5 = final_v5.astype(\n", "    {\n", "        \"dark_store\": \"int64\",\n", "        \"front_end\": \"int64\",\n", "        \"item_id\": \"int64\",\n", "        \"type_flag\": \"object\",\n", "        \"Actual_Quantity\": \"float64\",\n", "        \"Order_Blocked_Quantity\": \"float64\",\n", "        \"STO_Blocked_Quantity\": \"float64\",\n", "        \"Net_Quantity\": \"float64\",\n", "        \"recieving_quantity\": \"float64\",\n", "        \"ds_outlet_id\": \"int64\",\n", "        \"product_id\": \"int64\",\n", "        \"name\": \"object\",\n", "        \"l0\": \"object\",\n", "        \"l1\": \"object\",\n", "        \"l2\": \"object\",\n", "        \"brand\": \"object\",\n", "        \"manf\": \"object\",\n", "        \"product_type\": \"object\",\n", "        \"after_tf_qty\": \"float64\",\n", "        \"etl_timestamp_ist\": \"datetime64[ns, Asia/Kolkata]\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def redshift_schema(df):\n", "    metadata = df.dtypes.apply(lambda x: x.name).to_dict()\n", "    ref_dict = {\n", "        \"int64\": \"int\",\n", "        \"datetime64[ns, Asia/Kolkata]\": \"timestamp\",\n", "        \"datetime64[ns]\": \"timestamp\",\n", "        \"object\": \"varchar(1000)\",\n", "        \"float64\": \"float\",\n", "    }\n", "    return [{\"name\": key, \"type\": ref_dict[value]} for key, value in metadata.items()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_stores_inventory_log\",\n", "    \"column_dtypes\": redshift_schema(final_v5),\n", "    \"primary_key\": [\"etl_timestamp_ist\"],\n", "    \"sortkey\": [\"etl_timestamp_ist\", \"dark_store\", \"front_end\", \"item_id\"],\n", "    \"incremental_key\": \"etl_timestamp_ist\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(final_v5, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def final_conc(final_v5, filter_flag):\n", "    r1 = (\n", "        final_v5.groupby([\"ds_outlet_id\", \"dark_store\"])\n", "        .agg(\n", "            {\n", "                \"item_id\": \"count\",\n", "                # \"active\": \"count\",\n", "                \"Actual_Quantity\": \"sum\",\n", "                \"Order_Blocked_Quantity\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "        .rename(\n", "            columns={\n", "                \"item_id\": \"total_skus\",\n", "                # \"active\": \"eligible_skus\",\n", "                \"Actual_Quantity\": \"actual\",\n", "                \"Order_Blocked_Quantity\": \"blocked\",\n", "            }\n", "        )\n", "    )\n", "    final_v6 = final_v5[final_v5.Net_Quantity > 0]\n", "    r2 = (\n", "        final_v6.groupby([\"ds_outlet_id\"])\n", "        .agg({\"item_id\": \"count\"})\n", "        .reset_index()\n", "        .rename(columns={\"item_id\": \"current_live\"})\n", "    )\n", "\n", "    final_v7 = final_v5[final_v5.after_tf_qty > 0]\n", "\n", "    r3 = (\n", "        final_v7.groupby([\"ds_outlet_id\"])\n", "        .agg({\"item_id\": \"count\"})\n", "        .reset_index()\n", "        .rename(columns={\"item_id\": \"after_tf_live\"})\n", "    )\n", "\n", "    r0 = pd.merge(\n", "        r1,\n", "        r2,\n", "        how=\"left\",\n", "        left_on=[\"ds_outlet_id\"],\n", "        right_on=[\"ds_outlet_id\"],\n", "    )\n", "    r = pd.merge(\n", "        r0,\n", "        r3,\n", "        how=\"left\",\n", "        left_on=[\"ds_outlet_id\"],\n", "        right_on=[\"ds_outlet_id\"],\n", "    )\n", "\n", "    r[\"filter\"] = filter_flag\n", "    return r"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_final = final_conc(final_v5, \"Overall\")\n", "\n", "df_final_list = []\n", "\n", "df_final_list.append(df_final)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["filter for gb"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_gb = final_v5[final_v5.is_pl == 1]\n", "\n", "df_final_gb = final_conc(final_gb, \"GB\")\n", "\n", "df_final_list.append(df_final_gb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["filter for gb staples"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_gb_stpl = final_v5[(final_v5.is_pl == 1) & (final_v5.l0 == \"Grocery & Staples\")]\n", "\n", "df_final_gb_stpl = final_conc(final_gb_stpl, \"GB Staples\")\n", "\n", "df_final_list.append(df_final_gb_stpl)"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_gb_A = final_v5[final_v5.bucket == \"A\"]\n", "\n", "df_final_A = final_conc(final_gb_A, \"Bucket A\")\n", "\n", "df_final_list.append(df_final_A)"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_gb_B = final_v5[final_v5.bucket == \"B\"]\n", "\n", "df_final_B = final_conc(final_gb_B, \"Bucket B\")\n", "\n", "df_final_list.append(df_final_B)"]}, {"cell_type": "raw", "metadata": {}, "source": ["final_gb_X = final_v5[final_v5.bucket_x == \"Y\"]\n", "\n", "df_final_X = final_conc(final_gb_X, \"Bucket X\")\n", "\n", "df_final_list.append(df_final_X)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_v5.type_flag.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_fnv = final_v5[final_v5.type_flag == \"Grocery\"]\n", "\n", "final_fnv = final_conc(final_fnv, \"Grocery\")\n", "\n", "df_final_list.append(final_fnv)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_fnv = final_v5[final_v5.type_flag == \"FnV\"]\n", "\n", "final_fnv = final_conc(final_fnv, \"FnV\")\n", "\n", "df_final_list.append(final_fnv)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_fnv = final_v5[(final_v5.type_flag == \"Perishable\") | (final_v5.type_flag == \"Perishable \")]\n", "\n", "final_fnv = final_conc(final_fnv, \"Perishable\")\n", "\n", "df_final_list.append(final_fnv)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = pd.concat(df_final_list)"]}, {"cell_type": "raw", "metadata": {}, "source": ["fd.sort_values(\n", "    by=[\"Prioirty\",], axis=0, ascending=True, inplace=True, na_position=\"first\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd[\"current_live_per\"] = fd[\"current_live\"] / fd[\"total_skus\"]\n", "fd[\"after_tf_live_per\"] = fd[\"after_tf_live\"] / fd[\"total_skus\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd[\"current_live\"] = fd.current_live.fillna(0)\n", "fd[\"current_live_per\"] = fd.current_live_per.fillna(0)\n", "fd[\"after_tf_live\"] = fd.after_tf_live.fillna(0)\n", "fd[\"after_tf_live_per\"] = fd.after_tf_live_per.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd.sort_values(\n", "    by=[\n", "        \"dark_store\",\n", "    ],\n", "    axis=0,\n", "    ascending=True,\n", "    inplace=True,\n", "    na_position=\"first\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd1 = fd[\n", "    [\n", "        \"ds_outlet_id\",\n", "        \"filter\",\n", "        \"total_skus\",\n", "        \"current_live\",\n", "        \"current_live_per\",\n", "        \"after_tf_live\",\n", "        \"after_tf_live_per\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pytz import timezone\n", "\n", "now_india = str(datetime.datetime.now(timezone(\"Asia/Kolkata\"))).split(\"+\")[0].split(\".\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd1[\"last_updated\"] = now_india"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd1 = fd1.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1M-Zw_JBzGL_0QHbXOtWrNkxLTO0KNYgjj1qL6chZ8KQ\"\n", "sheet_name = \"av_sum_raw\"\n", "pb.to_sheets(fd1, sheet_id, sheet_name, service_account=\"service_account\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fdm = fd.rename(\n", "    columns={\n", "        \"ds_outlet_id\": \"DS Outlet ID\",\n", "        \"filter\": \"\",\n", "        # \"total_skus\": \"Total SKUs\",\n", "        # \"eligible_skus\": \"Eligible SKUs\",\n", "        # \"actual\": \"Actual Quantity\",\n", "        # \"blocked\": \"Blocked Quantity\",\n", "        \"current_live\": \"Current Eligibe Live\",\n", "        \"current_live_per\": \"Current Availability\",\n", "        \"after_tf_live\": \"After Transfer Eligible Live\",\n", "        \"after_tf_live_per\": \"After Transfer Availability\",\n", "    }\n", ")\n", "\n", "fdm[\"Current Availability\"] = round(fdm[\"Current Availability\"] * 100, 2)\n", "fdm[\"After Transfer Availability\"] = round(fdm[\"After Transfer Availability\"] * 100, 2)\n", "\n", "\n", "def cond_for(x):\n", "    \"\"\"\n", "    https://www.rapidtables.com/web/color/RGB_Color.html\n", "    \"\"\"\n", "    if x >= 0 and x <= 20:\n", "        colour = \"#FF6666\"\n", "    elif x > 20 and x <= 40:\n", "        colour = \"#FF9999\"\n", "    elif x > 40 and x <= 50:\n", "        colour = \"#FFCCCC\"\n", "    elif x > 50 and x <= 60:\n", "        colour = \"#CCFFE5\"\n", "    elif x > 60 and x <= 80:\n", "        colour = \"#99FFCC\"\n", "    else:\n", "        colour = \"#00CC66\"\n", "    return \"_beg_\" + colour + \"_end_\" + str(x) + \" %\"\n", "\n", "\n", "fdm[\"Current Availability\"] = fdm[\"Current Availability\"].apply(cond_for)\n", "fdm[\"After Transfer Availability\"] = fdm[\"After Transfer Availability\"].apply(cond_for)\n", "fdm = fdm.groupby([\"DS Outlet ID\", \"\"]).max()"]}, {"cell_type": "raw", "metadata": {}, "source": ["def cond_for_reverse(x):\n", "    \"\"\"\n", "    https://www.rapidtables.com/web/color/RGB_Color.html\n", "    \"\"\"\n", "    y = x\n", "    x = 100 - x\n", "    if x >= 0 and x <= 20:\n", "        colour = \"#FF6666\"\n", "    elif x > 20 and x <= 40:\n", "        colour = \"#FF9999\"\n", "    elif x > 40 and x <= 50:\n", "        colour = \"#FFCCCC\"\n", "    elif x > 50 and x <= 60:\n", "        colour = \"#CCFFE5\"\n", "    elif x > 60 and x <= 80:\n", "        colour = \"#99FFCC\"\n", "    elif x == 100:\n", "        colour = \"#FFFFFF\"\n", "    else:\n", "        colour = \"#00CC66\"\n", "    return \"_beg_\" + colour + \"_end_\" + str(y) + \" %\""]}, {"cell_type": "raw", "metadata": {}, "source": ["h = l1.columns.to_list()\n", "h.remove(\"dark_store\")"]}, {"cell_type": "raw", "metadata": {}, "source": ["for x in h:\n", "    l1[x] = round(l1[x] * 100, 2)\n", "    l1[x] = l1[x].apply(cond_for_reverse)"]}, {"cell_type": "raw", "metadata": {}, "source": ["l1.set_index([\"dark_store\"], inplace=True, drop=True)"]}, {"cell_type": "raw", "metadata": {}, "source": ["l1"]}, {"cell_type": "raw", "metadata": {}, "source": ["fdm.sort_values(\n", "    by=[\"Prioirty\",], axis=0, ascending=True, inplace=True, na_position=\"first\",\n", ")\n", "fdm = fdm.drop([\"Prioirty\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fdm = fdm[\n", "    [\n", "        \"Current Eligibe Live\",\n", "        \"Current Availability\",\n", "        \"After Transfer Eligible Live\",\n", "        \"After Transfer Availability\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_head = \"\"\"<thead>\n", "    <tr style=\"text-align: center;\">\n", "      <th style=\"min-width: 100;\"></th>\n", "      <th style=\"min-width: 100;\"></th>\n", "      <th style=\"min-width: 100;\">Current Eligibe Live</th>\n", "      <th style=\"min-width: 100;\">Current Availability</th>\n", "      <th style=\"min-width: 100;\">After Transfer Eligible Live</th>\n", "      <th style=\"min-width: 100;\">After Transfer Availability</th>\n", "    </tr>\n", "    <tr>\n", "      <th style=\"min-width: 100;\">DS Outlet ID</th>\n", "      <th style=\"min-width: 100;\"></th>\n", "      <th style=\"min-width: 100;\"></th>\n", "      <th style=\"min-width: 100;\"></th>\n", "      <th style=\"min-width: 100;\"></th>\n", "      <th style=\"min-width: 100;\"></th>\n", "    </tr>\n", "  </thead>\"\"\"\n", "merged_head = \"\"\"<thead>\n", "    <tr style=\"text-align: center;\">\n", "      <th style=\"min-width: 100;\" colspan=\"2\" rowspan=\"2\">DS Outlet ID</th>\n", "      <th style=\"min-width: 100;\" colspan=\"2\">Current Inventory Status</th>\n", "      <th style=\"min-width: 100;\" colspan=\"2\">After Transfer Status</th>\n", "    </tr>\n", "    <tr style=\"text-align: center;\">\n", "      <th style=\"min-width: 100;\">Current Eligibe Live</th>\n", "      <th style=\"min-width: 100;\">Current Availability</th>\n", "      <th style=\"min-width: 100;\">After Transfer Eligible Live</th>\n", "      <th style=\"min-width: 100;\">After Transfer Availability</th>\n", "    </tr>\n", "  </thead>\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table = (\n", "    fdm.to_html(classes=\"\", justify=\"center\", col_space=100)\n", "    .replace(\"<td>\", '<td style=\"text-align: center;\">')\n", "    .replace(\n", "        '<table border=\"1\" class=\"dataframe\">',\n", "        '<table width = \"1500\" border=\"1\" class=\"dataframe\" cellspacing = \"0\">',\n", "    )\n", "    .replace(current_head, merged_head)\n", "    .replace(\"<th \", '<th valign=\"middle\" bgcolor=\"#D3D3D3\" ')\n", "    .replace(\">_beg_\", ' bgcolor=\"')\n", "    .replace(\"_end_\", '\">')\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["stock_out = (\n", "    l1.to_html(classes=\"\", justify=\"center\", col_space=100)\n", "    .replace(\"<td>\", '<td style=\"text-align: center;\">')\n", "    .replace(\n", "        '<table border=\"1\" class=\"dataframe\">',\n", "        '<table width = \"1500\" border=\"1\" class=\"dataframe\" cellspacing = \"0\">',\n", "    )\n", "    .replace(\"<th \", '<th valign=\"middle\" bgcolor=\"#D3D3D3\" ')\n", "    .replace(\">_beg_\", ' bgcolor=\"')\n", "    .replace(\"_end_\", '\">')\n", ")\n", "to_be_replaced = (\n", "    [stock_out.split(\"<thead>\")[0]] + stock_out.split(\"<thead>\")[1].split(\"</thead>\")\n", ")[1]\n", "replaced_with = to_be_replaced.split(\"<tr>\")[0].replace(\n", "    \"stock_out_reason\", \"Outlet Name\"\n", ")\n", "\n", "stock_out = stock_out.replace(to_be_replaced, replaced_with)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pre = \"\"\"\n", "Hi all,\n", "\n", "Please find below the Dark Stores - Availability report.\n", "\n", "Dark Stores Tracker :\n", "https://docs.google.com/spreadsheets/d/1M-Zw_JBzGL_0QHbXOtWrNkxLTO0KNYgjj1qL6chZ8KQ/edit#gid=1907580786\n", "\n", "\n", "\"\"\".replace(\n", "    \"\\n\", \"<br>\"\n", ")\n", "\n", "post = \"\"\"\n", "\n", "\n", "<b>Note: This is an automatically generated email, kindly <NAME_EMAIL> in case of any discrepancies.\n", "\n", "<PERSON><PERSON>,<br>\n", "<PERSON><PERSON><PERSON></b>\n", "\"\"\".replace(\n", "    \"\\n\", \"<br>\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# custom_send = True\n", "if sendmail or custom_send:\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [x.strip() for x in mail_config.email.to_list()]\n", "    subject = str(datetime.date.today()) + \" | Dark Stores - Availability\"\n", "    html_content = (\n", "        pre\n", "        + table\n", "        + \"<br>\" * 2\n", "        # + \"<b>Stock Out Analysis:</b><br><br>\"\n", "        # + stock_out\n", "        + post\n", "    )\n", "    # sendmail = True\n", "    pb.send_email(from_email, to_email, subject, html_content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}