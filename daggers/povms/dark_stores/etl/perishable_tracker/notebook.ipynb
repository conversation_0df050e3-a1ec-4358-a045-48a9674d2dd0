{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import difflib\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1uPo-oLi04fj8ivxVZ8QiMhSFShEynGWQvavkL0KtgQw\"\n", "sheet_name = \"items_list\"\n", "items_list = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "items_list.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list[[\"backend_outlet\", \"frontend_outlet\", \"item_id\"]] = items_list[\n", "    [\"backend_outlet\", \"frontend_outlet\", \"item_id\"]\n", "].astype(\"int\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet- facility mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select o.id as frontend_outlet,o.name as outlet_name,facility_id,f.name as facility_name from lake_retail.console_outlet o\n", "inner join lake_crates.facility f on f.id=o.facility_id\n", "\"\"\"\n", "outlet = pd.read_sql(query, redshift)\n", "outlet.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list_facility = items_list.merge(\n", "    outlet[[\"frontend_outlet\", \"facility_id\"]], on=[\"frontend_outlet\"], how=\"left\"\n", ")\n", "items_list_facility.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Active items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT facility_id,\n", "       item_id,\n", "       case when master_assortment_substate_id=1 then 'active'\n", "       else 'inactive' end as item_state\n", "FROM rpc.product_facility_master_assortment\n", "WHERE master_assortment_substate_id=1\n", "  AND active=1\n", "  and facility_id in %(facility)s\n", "  \"\"\"\n", "\n", "active_items = pd.read_sql_query(\n", "    query, retail, params={\"facility\": tuple(items_list_facility[\"facility_id\"])}\n", ")\n", "active_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list_facility_active = items_list_facility.merge(\n", "    active_items, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "items_list_facility_active.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frontend_outlet_list = items_list_facility_active[\"frontend_outlet\"].unique()\n", "frontend_outlet_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT ro.id AS frontend_outlet,\n", "                tag_value AS \"backend_outlet\",\n", "                item_id,\n", "                1 AS 'tea_items'\n", "FROM rpc.item_outlet_tag_mapping tag\n", "INNER JOIN retail.console_outlet ro ON ro.id=tag.outlet_id\n", "INNER JOIN retail.console_outlet r ON r.id=tag.tag_value\n", "WHERE tag_type_id=8\n", "  AND tag.active=1\n", "  and ro.id in %(outlet)s\n", "  \n", "ORDER BY 1,\n", "         2,\n", "         3\"\"\"\n", "tea_mapping = pd.read_sql_query(query, retail, params={\"outlet\": tuple(frontend_outlet_list)})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_mapping[\"frontend_outlet\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_mapping[[\"frontend_outlet\", \"backend_outlet\", \"item_id\"]] = tea_mapping[\n", "    [\"frontend_outlet\", \"backend_outlet\", \"item_id\"]\n", "].astype(\"int\")\n", "tea_mapped_items = items_list_facility_active.merge(\n", "    tea_mapping, on=[\"frontend_outlet\", \"backend_outlet\", \"item_id\"], how=\"left\"\n", ")\n", "tea_mapped_items[\"tea_items\"] = tea_mapped_items[\"tea_items\"].fillna(0)\n", "tea_mapped_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(tea_mapped_items, sheet_id, \"tea_tagged_items\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlet- facility mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select o.id as frontend_outlet,o.name as outlet_name,facility_id,f.name as facility_name from lake_retail.console_outlet o\n", "inner join lake_crates.facility f on f.id=o.facility_id\n", "\"\"\"\n", "outlet = pd.read_sql(query, redshift)\n", "outlet.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_list_facility = items_list.merge(outlet, on=[\"frontend_outlet\"], how=\"left\")\n", "item_list_facility = item_list_facility[\n", "    [\"facility_id\", \"facility_name\", \"frontend_outlet\", \"outlet_name\", \"item_id\"]\n", "].drop_duplicates()\n", "\n", "item_list_facility_active = item_list_facility.merge(\n", "    active_items, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "item_list_facility_active.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_list = item_list_facility_active[\"facility_id\"]\n", "item_list = item_list_facility_active[\"item_id\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item availability"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT date(a.order_date) AS dated,\n", "       CASE\n", "           WHEN date(a.order_date) BETWEEN CURRENT_DATE-7 AND CURRENT_DATE THEN 1\n", "           ELSE 0\n", "       END AS last_7_days,\n", "       CASE\n", "           WHEN extract(month from (a.order_date)) =extract(month from (CURRENT_DATE))-1 THEN 'last_month'\n", "           WHEN extract(month from (a.order_date)) =extract(month from (CURRENT_DATE)) THEN 'current_month'\n", "           ELSE 'extra'\n", "       END AS months_classify,\n", "       a.facility_id,\n", "       a.item_id,\n", "       actual_quantity,\n", "       blocked_quantity,\n", "       CASE\n", "           WHEN actual_quantity-blocked_quantity>0 THEN actual_quantity-blocked_quantity\n", "           ELSE 0\n", "       END AS available_quantity\n", "FROM rpc_daily_availability AS a\n", "INNER JOIN\n", "  (SELECT date(order_date) AS order_date,\n", "          max(order_date) AS maxi\n", "   FROM rpc_daily_availability\n", "   WHERE date(order_date) >=CURRENT_DATE-65\n", "   GROUP BY 1) AS b ON a.order_date = b.maxi\n", "AND b.order_date=date(a.order_date)\n", "WHERE a.item_id IN %(item)s\n", "  AND a.facility_id IN %(facility)s\"\"\"\n", "\n", "item_availability = pd.read_sql(\n", "    query, redshift, params={\"item\": tuple(item_list), \"facility\": tuple(facility_list)}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_availability.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_list_facility_active[\"item_id\"] = item_list_facility_active[\"item_id\"].astype(\"int\")\n", "item_list_facility_qty = item_list_facility_active.merge(\n", "    item_availability, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "item_list_facility_qty.sort_values(by=[\"facility_id\", \"dated\", \"item_id\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_list_facility_qty[\"updated_at\"] = now\n", "item_list_facility_qty = item_list_facility_qty.fillna(0)\n", "\n", "item_list_facility_qty = item_list_facility_qty[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"frontend_outlet\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"dated\",\n", "        \"last_7_days\",\n", "        \"months_classify\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"available_quantity\",\n", "        \"updated_at\",\n", "        \"item_state\",\n", "    ]\n", "]\n", "\n", "item_list_facility_qty.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(item_list_facility_qty, sheet_id, \"item_availability\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_month = now.month\n", "last_month = current_month - 1\n", "current_month, last_month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = now.date()\n", "end_date = now.date() - <PERSON><PERSON><PERSON>(days=7)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def orders(x):\n", "    if x[\"order_month\"] == last_month:\n", "        return \"last_month\"\n", "    elif x[\"order_month\"] == current_month:\n", "        return \"current_month\"\n", "    else:\n", "        return \"extra\"\n", "\n", "\n", "def last_seven_days(x):\n", "    if x[\"checkout_date\"] <= start_date and x[\"checkout_date\"] >= end_date:\n", "        return 1\n", "    else:\n", "        return 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Checked out orders (Total)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT facility_id,\n", "       last_7_days,\n", "       months_classify,\n", "       count(DISTINCT order_id) AS total_orders\n", "FROM\n", "  (SELECT date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) AS checkout_date,\n", "          ro.facility_id,\n", "          order_id,\n", "          CASE\n", "              WHEN date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) BETWEEN CURRENT_DATE-7 AND CURRENT_DATE THEN 1\n", "              ELSE 0\n", "          END AS 'last_7_days',\n", "          CASE\n", "              WHEN month(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) =month(CURRENT_DATE)-1 THEN 'last_month'\n", "              WHEN month(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) =month(CURRENT_DATE) THEN 'current_month'\n", "              ELSE 'extra'\n", "          END AS 'months_classify'\n", "   FROM ims.ims_order_details o\n", "   INNER JOIN retail.console_outlet ro ON ro.id=o.outlet\n", "   WHERE ro.facility_id in %(facility)s\n", "     AND date(convert_tz(o.created_at,\"+00:00\",\"+05:30\"))>=CURRENT_DATE-interval'70' DAY\n", "     AND status_id IN (1,\n", "                       2) )x1\n", "GROUP BY 1,\n", "         2,\n", "         3\n", "ORDER BY 1,\n", "         3,\n", "         2\"\"\"\n", "\n", "\n", "total_orders = pd.read_sql_query(query, retail, params={\"facility\": tuple(facility_list)})\n", "total_orders.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT facility_id,\n", "       months_classify,\n", "       last_7_days,\n", "       count(DISTINCT order_id) AS item_orders\n", "FROM\n", "  (SELECT date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) AS checkout_date,\n", "          CASE\n", "              WHEN date(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) BETWEEN CURRENT_DATE-7 AND CURRENT_DATE THEN 1\n", "              ELSE 0\n", "          END AS 'last_7_days',\n", "          CASE\n", "              WHEN month(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) =month(CURRENT_DATE)-1 THEN 'last_month'\n", "              WHEN month(convert_tz(o.created_at,\"+00:00\",\"+05:30\")) =month(CURRENT_DATE) THEN 'current_month'\n", "              ELSE 'extra'\n", "          END AS 'months_classify',\n", "          facility_id,\n", "          order_id,\n", "          item_id\n", "   FROM ims.ims_order_details o\n", "   INNER JOIN ims.ims_order_items i ON i.order_details_id=o.id\n", "   INNER JOIN retail.console_outlet ro ON ro.id=o.outlet\n", "   WHERE facility_id in %(facility)s\n", "   and item_id in %(item)s\n", "     AND date(convert_tz(o.created_at,\"+00:00\",\"+05:30\"))>=CURRENT_DATE-interval'70' DAY\n", "     AND status_id IN (1,\n", "                       2)\n", "   ORDER BY 1,\n", "            2,\n", "            3)x1\n", "GROUP BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "\n", "item_orders = pd.read_sql_query(\n", "    query, retail, params={\"item\": tuple(item_list), \"facility\": tuple(facility_list)}\n", ")\n", "item_orders.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cart_penetration = total_orders.merge(\n", "    item_orders, on=[\"facility_id\", \"months_classify\", \"last_7_days\"], how=\"left\"\n", ")\n", "# final_cart_penetration[\"cart_penetration\"]=final_cart_penetration[\"item_orders\"]/final_cart_penetration[\"total_orders\"]\n", "final_cart_penetration = final_cart_penetration.fillna(0)\n", "final_cart_penetration.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_cart_penetration, sheet_id, \"cart_penetration\", clear_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Item product mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\"\"\"\n", "\n", "item_product = pd.read_sql(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_product.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_list_product = items_list.merge(\n", "    item_product[[\"item_id\", \"product_id\"]], on=[\"item_id\"], how=\"left\"\n", ")\n", "item_list_product.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_list = item_list_product[\"product_id\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT facility_id,\n", "       last_7_days,\n", "       months_classify,\n", "       sum(selling_price*order_qty) AS gmv\n", "FROM\n", "  (SELECT DISTINCT date(convert_timezone('Asia/Kolkata',a.install_ts)) AS checkout_date,\n", "                   CASE\n", "                       WHEN date(convert_timezone('Asia/Kolkata',a.install_ts)) BETWEEN CURRENT_DATE-7 AND CURRENT_DATE THEN 1\n", "                       ELSE 0\n", "                   END AS last_7_days,\n", "                   CASE\n", "                       WHEN extract(MONTH\n", "                                    FROM (convert_timezone('Asia/Kolkata',a.install_ts))) =extract(MONTH\n", "                                                                                                   FROM CURRENT_DATE)-1 THEN 'last_month'\n", "                       WHEN extract(MONTH\n", "                                    FROM (convert_timezone('Asia/Kolkata',a.install_ts))) =extract(MONTH\n", "                                                                                                   FROM CURRENT_DATE) THEN 'current_month'\n", "                       ELSE 'extra'\n", "                   END AS months_classify,\n", "                   facility_id,\n", "                   a.id AS ancestor,\n", "                   so.id AS sub_order_id,\n", "                   oa.product_id,\n", "                   selling_price,\n", "                   oi.quantity AS order_qty\n", "   FROM lake_oms_bifrost.oms_order a\n", "   INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "   INNER JOIN lake_oms_bifrost.oms_suborder so ON so.order_id=a.id\n", "   INNER JOIN lake_oms_bifrost.oms_suborder_item oi ON so.id = oi.suborder_id\n", "   INNER JOIN lake_oms_bifrost.oms_order_item oa ON oa.id=oi.order_item_id\n", "   INNER JOIN lake_ims.ims_order_details o ON o.order_id=so.id\n", "   INNER JOIN lake_retail.console_outlet ro ON ro.id=o.outlet\n", "   AND o.status_id IN (1,\n", "                       2)\n", "   WHERE date(convert_timezone('Asia/Kolkata',a.install_ts)) >=CURRENT_DATE-70\n", "     AND a.direction ='FORWARD'\n", "     AND a.current_status != 'CANCELLED'\n", "     AND so.current_status != 'CANCELLED'\n", "     AND o.status_id IN (1,\n", "                         2)\n", "     AND facility_id in %(facility)s\n", "     and product_id in %(product)s\n", "     AND a.type IN ('RetailForwardOrder',\n", "                    'InternalForwardOrder') )x1\n", "GROUP BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "gmv = pd.read_sql(\n", "    query,\n", "    redshift,\n", "    params={\"product\": tuple(product_list), \"facility\": tuple(facility_list)},\n", ")\n", "gmv.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(gmv, sheet_id, \"gmv\", clear_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items_list[\"frontend_outlet\"] = np.where(\n", "    items_list[\"frontend_outlet\"] == 845, 846, items_list[\"frontend_outlet\"]\n", ")\n", "items_list.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = tuple(set(items_list[\"item_id\"]))\n", "outlet_list = list(set(items_list[\"frontend_outlet\"]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dump"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame()\n", "\n", "for outlet in outlet_list:\n", "\n", "    query = \"\"\"SELECT date_,\n", "           CASE\n", "               WHEN date_ BETWEEN CURRENT_DATE-interval'7' DAY AND CURRENT_DATE THEN 1\n", "               ELSE 0\n", "           END AS last_7_days,\n", "           CASE\n", "               WHEN extract(MONTH\n", "                            FROM date_) =extract(MONTH\n", "                                                 FROM CURRENT_DATE)-1 THEN 'last_month'\n", "               WHEN extract(MONTH\n", "                            FROM date_) =extract(MONTH\n", "                                                 FROM CURRENT_DATE) THEN 'current_month'\n", "               ELSE 'extra'\n", "           END AS months_classify,\n", "           ds AS frontend_outlet,\n", "           item,\n", "           sum(quantity) AS quantity,\n", "           sum(weighted_lp*quantity) AS loss_lp\n", "    FROM\n", "      (SELECT date(convert_tz(i.pos_timestamp,'+00:00','+05:30')) AS date_,\n", "              i.outlet_id AS ds,\n", "              r.item_id AS item,\n", "              r.name AS Product,\n", "              r.variant_uom_text AS grammage,\n", "              i2.name AS Update_type,\n", "              r.variant_mrp AS Mrp,\n", "              i.weighted_lp AS weighted_lp,\n", "              SUM(i.delta) AS quantity\n", "       FROM ims.ims_inventory_log i\n", "       INNER JOIN rpc.product_product r ON r.variant_id = i.variant_id\n", "       INNER JOIN retail.console_outlet ro ON ro.id = i.outlet_id\n", "       INNER JOIN ims.ims_inventory_update_type i2 ON i2.id = i.inventory_update_type_id\n", "       LEFT JOIN ims.ims_bad_inventory_update_log ibil ON i.inventory_update_id=ibil.inventory_update_id\n", "       LEFT JOIN ims.ims_bad_update_reason ibur ON ibil.reason_id=ibur.id\n", "       WHERE i.inventory_update_type_id IN (11,\n", "                                            12,\n", "                                            13,\n", "                                            64,\n", "                                            87,\n", "                                            88,\n", "                                            89)\n", "         AND date(convert_tz(i.pos_timestamp,'+00:00','+05:30')) >= CURRENT_DATE-interval'70' DAY\n", "         AND i.outlet_id =%(outlet)s\n", "        AND r.item_id IN %(item)s\n", "\n", "       GROUP BY 1,\n", "                2,\n", "                3,\n", "                4,\n", "                5,\n", "                6,\n", "                7,\n", "                8) x1\n", "    GROUP BY 1,\n", "             2,\n", "             3,\n", "             4,\n", "             5\"\"\"\n", "\n", "    dump = pd.read_sql_query(query, retail, params={\"item\": items, \"outlet\": outlet})\n", "    df = pd.concat([df, dump])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"frontend_outlet\"] = np.where(df[\"frontend_outlet\"] == 846, 845, df[\"frontend_outlet\"])\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(df, sheet_id, \"dump\", clear_cache=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}