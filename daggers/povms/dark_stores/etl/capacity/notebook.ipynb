{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "from pytz import timezone\n", "import datetime\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["now_india = str(datetime.datetime.now(timezone(\"Asia/Kolkata\"))).split(\"+\")[0].split(\".\")[0]\n", "x = 0\n", "while x < 4:\n", "    x += 1\n", "    CON_SQL = pb.get_connection(\"retail\")\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT a.outlet_id, \n", "       a.item_id, \n", "       a.quantity, \n", "       b.quantity AS blocked_quantity \n", "FROM   (SELECT outlet_id, \n", "               item_id, \n", "               quantity \n", "        FROM   ims.ims_item_inventory \n", "        WHERE  outlet_id IN (  779,795,798,846  ) \n", "               AND active = 1) a \n", "       LEFT JOIN (SELECT item_id, \n", "                         outlet_id, \n", "                         sum(quantity) AS quantity\n", "                  FROM   ims.ims_item_blocked_inventory \n", "                  WHERE  outlet_id IN (  779,795,798,846  ) \n", "                         AND active = 1 \n", "                         AND blocked_type in (1)  -- blocked for sto and orders\n", "                 GROUP BY 1,2) b \n", "              ON a.item_id = b.item_id \n", "                 AND a.outlet_id = b.outlet_id \n", "\"\"\"\n", "quantity = pd.read_sql_query(sql=query, con=CON_SQL)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qty = (\n", "    quantity.groupby([\"outlet_id\"])\n", "    .agg({\"quantity\": \"sum\", \"blocked_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "qty[\"updated_at\"] = now_india"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1M-Zw_JBzGL_0QHbXOtWrNkxLTO0KNYgjj1qL6chZ8KQ\"\n", "sheet_name = \"Availability_Raw\"\n", "pb.to_sheets(qty, sheet_id, sheet_name, service_account=\"service_account\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}