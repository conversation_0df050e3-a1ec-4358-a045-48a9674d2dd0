{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "\n", "now = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Grn Time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT receiving_outlet_id,\n", "       receiver_outlet_name,\n", "       sto_date,\n", "       sto_slot,\n", "       max(grn_created_at) AS latest_grn_time\n", "FROM\n", "  (SELECT DISTINCT sto_id,\n", "                   sender_outlet_id,\n", "                   sender_outlet_name,\n", "                   receiving_outlet_id,\n", "                   receiver_outlet_name,\n", "                   date(created_date) AS sto_date,\n", "                   created_date,\n", "                   CASE\n", "                       WHEN extract(hour\n", "                                    FROM created_date)<=10 THEN 'morning'\n", "                       ELSE 'evening'\n", "                   END AS sto_slot,\n", "                   grn_created_at\n", "   FROM metrics.esto a\n", "   INNER JOIN lake_retail.console_outlet ro ON ro.id=a.receiving_outlet_id\n", "   INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "   WHERE a.etl_timestamp_utc =\n", "       (SELECT max(etl_timestamp_utc)\n", "        FROM metrics.esto)\n", "     AND invoice_state LIKE '%%GRN%%'\n", "     AND date(created_date)>=CURRENT_DATE-7\n", "     AND receiver_outlet_name LIKE '%%ES%%'\n", "   ORDER BY created_date)x1\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4\n", "ORDER BY 1,\n", "         2,\n", "         3\"\"\"\n", "\n", "grn_time = pd.read_sql(query, redshift)\n", "\n", "query = \"\"\"select id as receiving_outlet_id,facility_id from lake_retail.console_outlet\"\"\"\n", "facility_grn = pd.read_sql(query, redshift)\n", "\n", "grn_time = grn_time.merge(facility_grn, on=[\"receiving_outlet_id\"], how=\"left\")\n", "\n", "query = \"\"\"SELECT o.facility_id,\n", "       es.merchant_name\n", "FROM consumer.express_stores es\n", "INNER JOIN lake_retail.console_outlet o ON es.ssc_id=o.id\"\"\"\n", "merchant = pd.read_sql(query, redshift)\n", "\n", "grn_time = grn_time.merge(merchant, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grn_time = grn_time[[\"sto_date\", \"merchant_name\", \"sto_slot\", \"latest_grn_time\"]].drop_duplicates()\n", "final = (\n", "    grn_time.groupby([\"sto_date\", \"merchant_name\", \"sto_slot\"])[[\"latest_grn_time\"]]\n", "    .max()\n", "    .reset_index()\n", ")\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1gwLrBTFQvrbfCoELQ_gzPrDoFHBly1_-eU53kPOH__Q\"\n", "pb.to_sheets(final, sheet_id, \"grn_time\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1Cugd1tOUaPjGnSzWXVt61N_utp3M5zwGx-4wC3_ktZw\"\n", "sheet_name = \"facility_list\"\n", "facility_name = pb.from_sheets(sheet_id, sheet_name, clear_cache=True)\n", "normal_facility = facility_name[facility_name[\"type\"] == \"normal\"]\n", "bulk_facility = facility_name[facility_name[\"type\"] == \"bulk\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nornal_facility_list = list(normal_facility[\"facility_id\"].astype(\"int\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bulk_facility_list = list(bulk_facility[\"facility_id\"].astype(\"int\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_facility = nornal_facility_list + bulk_facility_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### direct cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select facility_id,\n", "sum(consumption) as consumption \n", "from (SELECT o.facility_id,\n", "          f.name AS facility_name,\n", "          cpd.item_id,\n", "          date AS dated,\n", "          max(consumption) as consumption\n", "   FROM snorlax.date_wise_consumption cpd\n", "   INNER JOIN retail.console_outlet o ON o.id=cpd.outlet_id\n", "   INNER JOIN crates.facility f ON f.id=o.facility_id\n", "   inner join rpc.product_facility_master_assortment ass on ass.facility_id=f.id and ass.item_id=cpd.item_id\n", "   WHERE date between current_date  and '2021-01-26'\n", "     AND o.facility_id IN %(facility)s\n", "     and master_assortment_substate_id=1\n", "   group by 1,2,3,4       \n", "   ORDER BY 5,3) x1\n", "   group by 1\"\"\"\n", "\n", "direct_cpd = pd.read_sql_query(query, retail, params={\"facility\": tuple(nornal_facility_list)})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["direct_cpd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_name[\"facility_id\"] = facility_name[\"facility_id\"].astype(\"int\")\n", "facility_left = facility_name.merge(direct_cpd, on=[\"facility_id\"], how=\"left\")\n", "facility_left = facility_left[facility_left[\"consumption\"].isnull()]\n", "facility_left_list = list(facility_left[\"facility_id\"])\n", "facility_left_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility serving ds/fs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT r.facility_id,\n", "                ro.facility_id AS ds_facility,\n", "                f.name AS ds_facility_name\n", "FROM rpc.item_outlet_tag_mapping AS t\n", "INNER JOIN retail.console_outlet ro ON ro.id=t.outlet_id\n", "INNER JOIN retail.console_outlet r ON r.id=t.tag_value\n", "INNER JOIN crates.facility f ON f.id=ro.facility_id\n", "INNER JOIN crates.facility fc ON fc.id=r.facility_id\n", "WHERE (f.name like '%%es%%' or f.name like '%%dark%%')\n", "and r.facility_id IN %(facility)s\n", "and t.active=1\n", "order by 1\"\"\"\n", "\n", "mapping = pd.read_sql_query(query, retail, params={\"facility\": tuple(nornal_facility_list)})\n", "mapping.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Indirect CPD "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indirect_facility_list = list(mapping[\"ds_facility\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select facility_id as ds_facility,\n", "sum(consumption) as consumption \n", "from (SELECT o.facility_id,\n", "          f.name AS facility_name,\n", "          cpd.item_id,\n", "          date AS dated,\n", "          max(consumption) as consumption\n", "   FROM snorlax.date_wise_consumption cpd\n", "   INNER JOIN retail.console_outlet o ON o.id=cpd.outlet_id\n", "   INNER JOIN crates.facility f ON f.id=o.facility_id\n", "   inner join rpc.product_facility_master_assortment ass on ass.facility_id=f.id and ass.item_id=cpd.item_id\n", "   WHERE date between current_date  and '2021-01-26'\n", "   and master_assortment_substate_id=1\n", "     AND o.facility_id IN %(facility)s\n", "   group by 1,2,3,4       \n", "   ORDER BY 5,3) x1\n", "   group by 1\"\"\"\n", "\n", "indirect_cpd = pd.read_sql_query(query, retail, params={\"facility\": tuple(indirect_facility_list)})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Indirect cpd on backend"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indirect_cpd_backend = indirect_cpd.merge(mapping, on=[\"ds_facility\"], how=\"inner\")\n", "indirect_cpd_backend = indirect_cpd_backend[[\"facility_id\", \"consumption\"]]\n", "indirect_cpd_backend_total = (\n", "    indirect_cpd_backend.groupby([\"facility_id\"])[[\"consumption\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(columns={\"consumption\": \"indirect_consumption\"})\n", ")\n", "indirect_cpd_backend_total"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Total frontend cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_cpd = direct_cpd.merge(indirect_cpd_backend_total, on=[\"facility_id\"], how=\"outer\")\n", "total_cpd = total_cpd.fillna(0)\n", "total_cpd[\"total_sales\"] = total_cpd[\"consumption\"] + total_cpd[\"indirect_consumption\"]\n", "total_cpd = total_cpd[[\"facility_id\", \"total_sales\"]].drop_duplicates()\n", "total_cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_cpd = direct_cpd.merge(indirect_cpd_backend_total, on=[\"facility_id\"], how=\"outer\")\n", "total_cpd = total_cpd.fillna(0)\n", "total_cpd[\"total_sales\"] = total_cpd[\"consumption\"] + total_cpd[\"indirect_consumption\"]\n", "total_cpd = total_cpd[[\"facility_id\", \"total_sales\"]].drop_duplicates()\n", "total_cpd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CPD on bulk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"SELECT DISTINCT r.facility_id,\n", "                      ro.facility_id as frontend_facility,\n", "                      item_id\n", "      FROM rpc.item_outlet_tag_mapping tag\n", "      INNER JOIN retail.console_outlet ro ON ro.id=tag.outlet_id\n", "      INNER JOIN retail.console_outlet r ON r.id=tag.tag_value\n", "      WHERE tag_type_id=8\n", "        AND tag.active=1\n", "        and ro.name not like \"%%old%%\"\n", "        and r.facility_id in %(facility)s\n", "    \n", "      ORDER BY 1,\n", "               2\"\"\"\n", "\n", "item_tag = pd.read_sql_query(query, retail, params={\"facility\": tuple(bulk_facility_list)})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_tag.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select facility_id as frontend_facility,\n", "item_id,\n", "sum(consumption) as consumption \n", "from (SELECT o.facility_id,\n", "          f.name AS facility_name,\n", "          cpd.item_id,\n", "          date AS dated,\n", "          max(consumption) as consumption\n", "   FROM snorlax.date_wise_consumption cpd\n", "   INNER JOIN retail.console_outlet o ON o.id=cpd.outlet_id\n", "   INNER JOIN crates.facility f ON f.id=o.facility_id\n", "   inner join rpc.product_facility_master_assortment ass on ass.facility_id=f.id and ass.item_id=cpd.item_id\n", "   WHERE date between current_date  and '2021-01-26'\n", "    and master_assortment_substate_id=1\n", "\n", "     AND o.facility_id IN %(facility)s\n", "   group by 1,2,3,4       \n", "   ORDER BY 5,3) x1\n", "   group by 1,2\"\"\"\n", "\n", "bulk_cpd = pd.read_sql_query(\n", "    query, retail, params={\"facility\": tuple(item_tag[\"frontend_facility\"].unique())}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bulk_cpd.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bulk_direct_cpd = item_tag.merge(bulk_cpd, on=[\"frontend_facility\", \"item_id\"], how=\"left\")\n", "bulk_direct_cpd = bulk_direct_cpd.fillna(0)\n", "bulk_direct_cpd = (\n", "    bulk_direct_cpd.groupby([\"facility_id\"])[[\"consumption\"]]\n", "    .sum()\n", "    .reset_index()\n", "    .rename(columns={\"consumption\": \"total_sales\"})\n", ")\n", "bulk_direct_cpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_cpd = pd.concat([total_cpd, bulk_direct_cpd])\n", "final_cpd = final_cpd.drop_duplicates()\n", "final_cpd.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_cpd, sheet_id, \"cpd\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "select facility_id,sum(unblocked_qty) as unblocked_qty from\n", "(SELECT distinct facility_id,\n", "item_id,\n", "actual_quantity-blocked_quantity as unblocked_qty\n", " \n", "FROM rpc_daily_availability AS a\n", "INNER JOIN\n", "  (SELECT date(order_date) AS order_date,\n", "          max(order_date) AS maxi\n", "   FROM rpc_daily_availability\n", "   WHERE date(order_date) =current_date\n", "   GROUP BY 1) AS b ON a.order_date = b.maxi\n", "AND b.order_date=date(a.order_date)\n", "where facility_id in %(facility)s )x1\n", "group by 1\n", "\n", "\"\"\"\n", "inventory = pd.read_sql(query, redshift, params={\"facility\": tuple(all_facility)})\n", "inventory.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_name = \"inventory\"\n", "pb.to_sheets(inventory, sheet_id, sheet_name, clear_cache=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}