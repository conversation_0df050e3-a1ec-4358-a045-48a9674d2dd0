alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: consolidated_grn_repor
dag_type: report
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURE15HB
path: povms/finance_automation/report/consolidated_grn_repor
paused: true
project_name: finance_automation
schedule:
  end_date: '2023-06-06T00:00:00'
  interval: 30 3 * * *
  start_date: '2022-04-26T14:20:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
