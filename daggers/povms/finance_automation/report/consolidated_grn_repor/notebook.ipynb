{"cells": [{"cell_type": "code", "execution_count": null, "id": "e8905773-6bee-4529-a749-fa6cc9f997b3", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "import gc\n", "\n", "import boto3\n", "import io\n", "import os\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "CON_IMS = pb.get_connection(\"[Replica] RDS IMS\")\n", "CON_PO = pb.get_connection(\"[Replica] RDS PO\")\n", "CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")"]}, {"cell_type": "code", "execution_count": null, "id": "67d8848c-1a65-4712-8729-23b7d337edf1", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "2a8c559f-4551-47a1-aa74-ec022e9251c0", "metadata": {}, "outputs": [], "source": ["secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "bucket_name = \"grofers-retail-test\"\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "s3c = boto3.client(\"s3\", aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)"]}, {"cell_type": "code", "execution_count": null, "id": "316aa5cd-9faf-4971-9229-c1db5c57eac8", "metadata": {}, "outputs": [], "source": ["log_files_created = []\n", "\n", "\n", "def to_S3(file, name):\n", "    try:\n", "\n", "        bucket_obj.upload_file(file, f\"reports/{file.split('/')[-1]}\")\n", "        print({file.split(\"/\")[-1]})\n", "        log_files_created.append(file)\n", "        presigned_url = s3c.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": f\"reports/{file.split('/')[-1]}\"},\n", "            HttpMethod=\"GET\",\n", "            ExpiresIn=864000,\n", "        )\n", "        return presigned_url\n", "    except Exception as e:\n", "        error = f\"\"\":red_circle: *Alert*: Smart Indent - Simulation, Error uploading log file `{name}` to S3 for Run ID: `{uid}`\n", "        \\n```{str(e)}```\\n Download Link - `{file}`\"\"\"\n", "        send_to_slack(error)\n", "        raise Exception(e)"]}, {"cell_type": "code", "execution_count": null, "id": "08e4a13b-e75f-4e62-8abd-4c0623f24c40", "metadata": {}, "outputs": [], "source": ["Tag = \"finance_automation/datapulls\"\n", "cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "b67af26c-d219-480e-953a-00b9953a2251", "metadata": {}, "outputs": [], "source": ["# directories\n", "GLOBAL_BASE_DIR = cwd\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")"]}, {"cell_type": "code", "execution_count": null, "id": "58dbe3c3-71cf-49b0-85fc-05d095c7c3a5", "metadata": {}, "outputs": [], "source": ["for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "367c38a2-9b6d-403a-9a33-f4f1b6e6a15b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af24f382-f204-45ce-a160-9ce1d93874e2", "metadata": {}, "outputs": [], "source": ["# Tracker 1\n", "# https://docs.google.com/spreadsheets/d/1gkAVOValnmkrZBA6zRI5pZ5GIIwjgjLNHafclkhV_X8/edit#gid=1416775658&fvid=1026265171\n", "\n", "sheet_id = \"1gkAVOValnmkrZBA6zRI5pZ5GIIwjgjLNHafclkhV_X8\"\n", "t1_gurgaon_df = pb.from_sheets(sheet_id, \"Gurgaon\")\n", "t1_gurgaon_df.columns = t1_gurgaon_df.iloc[1]\n", "t1_gurgaon_df = t1_gurgaon_df.iloc[2:]\n", "\n", "t1_jhajjar_df = pb.from_sheets(sheet_id, \"Jhajjar\")\n", "t1_jhajjar_df.columns = t1_jhajjar_df.iloc[1]\n", "t1_jhajjar_df = t1_jhajjar_df.iloc[2:]\n", "\n", "t1_sonepat_df = pb.from_sheets(sheet_id, \"Sonepat\")\n", "t1_sonepat_df.columns = t1_sonepat_df.iloc[1]\n", "t1_sonepat_df = t1_sonepat_df.iloc[2:]"]}, {"cell_type": "code", "execution_count": null, "id": "9f70afba-125d-4c80-8f69-41faf5739001", "metadata": {}, "outputs": [], "source": ["tracker_01 = pd.concat([t1_gurgaon_df, t1_jhajjar_df, t1_sonepat_df])\n", "tracker_01[\"identifier\"] = \"HOT Grocery\"\n", "tracker_01 = tracker_01.reset_index().drop(columns={\"index\"})\n", "tracker_01 = tracker_01[tracker_01[\"City Name\"] != \"\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c4484613-0364-486a-9f6b-9ca4c7e05df7", "metadata": {}, "outputs": [], "source": ["tracker_01.columns = [\n", "    \"City Name\",\n", "    \"Outlet Name\",\n", "    \"SAP Vendor Code\",\n", "    \"Supplier Name\",\n", "    \"Manufacturer Name\",\n", "    \"Vednor GSTN as per Gpos\",\n", "    \"Vendor GSTIN NO\",\n", "    \"True/False\",\n", "    \"Vendor Location\",\n", "    \"GRN Date\",\n", "    \"GRN NO\",\n", "    \"PO NO\",\n", "    \"Priority\",\n", "    \"PO Date\",\n", "    \"Invoice Date\",\n", "    \"Invoice no as per Po<PERSON>\",\n", "    \"Total Invoice Value\",\n", "    \"TCS Value\",\n", "    \"Grn Value\",\n", "    \"Total GRN Value\",\n", "    \"Unique\",\n", "    \"GRD (Goods Rcv Date)\",\n", "    \"Credit Day's\",\n", "    \"Payment Due date\",\n", "    \"Invoice link\",\n", "    \"SAP DOC No\",\n", "    \"Phy Invoice No\",\n", "    \"Phy Invoice Date\",\n", "    \"Inv Value Before Tax\",\n", "    \"Invoice Value\",\n", "    \"Discrepancy Value\",\n", "    \"TCS Amount\",\n", "    \"Discount Value\",\n", "    \"Freight Charges\",\n", "    \"Approved Payment\",\n", "    \"GRN Diff\",\n", "    \"Invoice Check Status\",\n", "    \"Invoice OK date\",\n", "    \"Owner\",\n", "    \"Payment Comment\",\n", "    \"MSME Vendor\",\n", "    \"Purchase Return\",\n", "    \"Incentive on sale\",\n", "    \"Bargain/Rate Diff\",\n", "    \"TDS Value\",\n", "    \"Advance Paid\",\n", "    \"Paid Value\",\n", "    \"Rest Payable\",\n", "    \"Payment Status\",\n", "    \"Payment Mode\",\n", "    \"Payment Date\",\n", "    \"Payment reference No.\",\n", "    \"Remarks\",\n", "    \"identifier\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "98c93019-9db0-4d72-9a9b-f3ae445e9d7e", "metadata": {}, "outputs": [], "source": ["tracker_01[\"GRN Date\"] = pd.to_datetime(tracker_01[\"GRN Date\"])"]}, {"cell_type": "markdown", "id": "85b271c6-2690-4faf-9981-a0b5ac9aeebc", "metadata": {}, "source": ["tracker_01.to_csv('tracker_01.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "4b612d11-c1cc-4c1d-be94-d9d1a5046d59", "metadata": {}, "outputs": [], "source": ["tracker_01[tracker_01[\"City Name\"].isna()][[\"City Name\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "3d446744-ac52-490e-8b25-f7fc0a6d4500", "metadata": {}, "outputs": [], "source": ["# Tracker 2\n", "# https://docs.google.com/spreadsheets/d/1BFcfALY4yeWiKJPOadFT7UGE9gRBJHMvCzxm7njdfWY/edit#gid=1250916733&fvid=1071303896\n", "\n", "sheet_id = \"1BFcfALY4yeWiKJPOadFT7UGE9gRBJHMvCzxm7njdfWY\"\n", "t2_delhi_df = pb.from_sheets(sheet_id, \"DELHI\")\n", "t2_delhi_df.columns = t2_delhi_df.iloc[1]\n", "t2_delhi_df = t2_delhi_df.iloc[2:]\n", "print(\"Delhi completed\")\n", "\n", "\n", "t2_kolkata_df = pb.from_sheets(sheet_id, \"KOLKATA\")\n", "t2_kolkata_df.columns = t2_kolkata_df.iloc[1]\n", "t2_kolkata_df = t2_kolkata_df.iloc[2:]\n", "print(\"Kolkata completed\")\n", "\n", "t2_ghaziabad_df = pb.from_sheets(sheet_id, \"GHAZIABAD\")\n", "t2_ghaziabad_df.columns = t2_ghaziabad_df.iloc[1]\n", "t2_ghaziabad_df = t2_ghaziabad_df.iloc[2:]\n", "print(\"Ghaziabad completed\")\n", "\n", "t2_lucknow_df = pb.from_sheets(sheet_id, \"LUCKNOW\")\n", "t2_lucknow_df.columns = t2_lucknow_df.iloc[1]\n", "t2_lucknow_df = t2_lucknow_df.iloc[2:]\n", "print(\"Lucknow completed\")\n", "\n", "t2_jaipur_df = pb.from_sheets(sheet_id, \"JAIPUR\")\n", "t2_jaipur_df.columns = t2_jaipur_df.iloc[1]\n", "t2_jaipur_df = t2_jaipur_df.iloc[2:]\n", "t2_jaipur_df.columns = [\n", "    \"City Name\",\n", "    \"Outlet Name\",\n", "    \"SAP Vendor Code\",\n", "    \"Supplier Name\",\n", "    \"Manufacturer Name\",\n", "    \"Vednor GSTN as per Gpos\",\n", "    \"Vendor GSTIN NO\",\n", "    \"True/False\",\n", "    \"Vendor Location\",\n", "    \"GRN Date\",\n", "    \"GRN NO\",\n", "    \"PO NO\",\n", "    \"Priority\",\n", "    \"PO Date\",\n", "    \"Invoice Date\",\n", "    \"Invoice no as per Po<PERSON>\",\n", "    \" Total Invoice Value \",\n", "    \" TCS Value \",\n", "    \" Grn Value \",\n", "    \" Total GRN Value \",\n", "    \"Unique\",\n", "    \"GRD (Goods Rcv Date)\",\n", "    \"Credit Day's\",\n", "    \"Payment Due date\",\n", "    \"Inv\",\n", "    \"SAP DOC No\",\n", "    \"Phy Invoice No\",\n", "    \"Phy Invoice Date\",\n", "    \"Inv Value Before Tax\",\n", "    \" Invoice Value \",\n", "    \" Discrepancy Value \",\n", "    \"TCS Amount\",\n", "    \" Discount Value \",\n", "    \" Freight Charges \",\n", "    \"Approved Payment\",\n", "    \" GRN Diff \",\n", "    \"Invoice Check Status\",\n", "    \"Invoice OK date\",\n", "    \"Owner\",\n", "    \"Payment Mode\",\n", "    \"MSME Vendor\",\n", "    \" Purchase Return \",\n", "    \" Incentive on sale \",\n", "    \" Bargain/Rate Diff \",\n", "    \" TDS Value \",\n", "    \" Advance Paid \",\n", "    \" Paid Value \",\n", "    \" Rest Payable \",\n", "    \"Payment Status\",\n", "    \"Payment Mode\",\n", "    \"Payment Date\",\n", "    \"Payment reference No.\",\n", "    \"Remarks\",\n", "]\n", "print(\"Jaipur completed\")\n", "\n", "t2_mumbai_df = pb.from_sheets(sheet_id, \"MUMBAI\")\n", "t2_mumbai_df.columns = t2_mumbai_df.iloc[1]\n", "t2_mumbai_df = t2_mumbai_df.iloc[2:]\n", "print(\"Mumbai completed\")\n", "\n", "t2_pune_df = pb.from_sheets(sheet_id, \"PUNE\")\n", "t2_pune_df.columns = t2_pune_df.iloc[1]\n", "t2_pune_df = t2_pune_df.iloc[2:]\n", "print(\"Pune completed\")\n", "\n", "t2_ahmedabad_df = pb.from_sheets(sheet_id, \"AHEMEDABAD\")\n", "t2_ahmedabad_df.columns = t2_ahmedabad_df.iloc[1]\n", "t2_ahmedabad_df = t2_ahmedabad_df.iloc[2:]\n", "print(\"Ahmedabad completed\")\n", "\n", "t2_blore_df = pb.from_sheets(sheet_id, \"BANGALORE\")\n", "t2_blore_df.columns = t2_blore_df.iloc[1]\n", "t2_blore_df = t2_blore_df.iloc[2:]\n", "print(\"Bangalore completed\")\n", "\n", "t2_hyderabad_df = pb.from_sheets(sheet_id, \"HYDERABAD\")\n", "t2_hyderabad_df.columns = t2_hyderabad_df.iloc[1]\n", "t2_hyderabad_df = t2_hyderabad_df.iloc[2:]\n", "print(\"Hyderabad completed\")\n", "\n", "t2_chennai_df = pb.from_sheets(sheet_id, \"CHENNAI\")\n", "t2_chennai_df.columns = t2_chennai_df.iloc[1]\n", "t2_chennai_df = t2_chennai_df.iloc[2:]\n", "print(\"Chennai completed\")\n", "\n", "\n", "t2_hariwar_df = pb.from_sheets(sheet_id, \"Haridwar\")\n", "t2_hariwar_df.columns = t2_hariwar_df.iloc[1]\n", "t2_hariwar_df = t2_hariwar_df.iloc[2:]\n", "print(\"<PERSON><PERSON><PERSON> completed\")\n", "\n", "t2_guwahati_df = pb.from_sheets(sheet_id, \"GUWAHATI\")\n", "t2_guwahati_df.columns = t2_guwahati_df.iloc[1]\n", "t2_guwahati_df = t2_guwahati_df.iloc[3:]\n", "print(\"GUWAHATI completed\")\n", "\n", "t2_zirakpur_df = pb.from_sheets(sheet_id, \"Zirakpur\")\n", "t2_zirakpur_df.columns = t2_zirakpur_df.iloc[1]\n", "t2_zirakpur_df = t2_zirakpur_df.iloc[3:]\n", "print(\"Zirakpur completed\")\n", "\n", "t2_indore_df = pb.from_sheets(sheet_id, \"INDORE\")\n", "t2_indore_df.columns = t2_indore_df.iloc[1]\n", "t2_indore_df = t2_indore_df.iloc[3:]\n", "print(\"Indore completed\")"]}, {"cell_type": "code", "execution_count": null, "id": "9194eb43-df60-4744-811f-a113936f9df6", "metadata": {}, "outputs": [], "source": ["print(\n", "    t2_delhi_df.shape,\n", "    \"t2_delhi_df\",\n", "    t2_kolkata_df.shape,\n", "    \"t2_kolkata_df\",\n", "    t2_ghaziabad_df.shape,\n", "    \"t2_ghaziabad_df\",\n", "    t2_lucknow_df.shape,\n", "    \"t2_lucknow_df\",\n", "    t2_jaipur_df.shape,\n", "    \"t2_jaipur_df\",\n", "    t2_mumbai_df.shape,\n", "    \"t2_mumbai_df\",\n", "    t2_pune_df.shape,\n", "    \"t2_pune_df\",\n", "    t2_ahmedabad_df.shape,\n", "    \"t2_ahmedabad_df\",\n", "    t2_blore_df.shape,\n", "    \"t2_blore_df\",\n", "    t2_hyderabad_df.shape,\n", "    \"t2_hyderabad_df\",\n", "    t2_chennai_df.shape,\n", "    \"t2_chennai_df\",\n", "    t2_hariwar_df.shape,\n", "    \"t2_hariwar_df\",\n", "    t2_guwahati_df.shape,\n", "    \"t2_guwahati_df\",\n", "    t2_zirakpur_df.shape,\n", "    \"t2_zirakpur_df\",\n", "    t2_indore_df.shape,\n", "    \"t2_indore_df\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "276ae36b-afa6-45ab-938b-bf1214b4e22d", "metadata": {}, "outputs": [], "source": ["t2_delhi_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f1857165-02fe-4d9b-84f4-a244ae7051de", "metadata": {}, "outputs": [], "source": ["tracker_02 = pd.concat(\n", "    [\n", "        t2_delhi_df,\n", "        t2_kolkata_df,\n", "        t2_ghaziabad_df,\n", "        t2_lucknow_df,\n", "        t2_jaipur_df,\n", "        t2_mumbai_df,\n", "        t2_pune_df,\n", "        t2_ahmedabad_df,\n", "        t2_blore_df,\n", "        t2_hyderabad_df,\n", "        t2_chennai_df,\n", "        t2_hariwar_df,\n", "        t2_guwahati_df,\n", "        t2_zirakpur_df,\n", "        t2_indore_df,\n", "    ]\n", ")  # .reset_index()\n", "\n", "tracker_02[\"identifier\"] = \"HOT Grocery\"\n", "tracker_02 = tracker_02.reset_index().drop(columns={\"index\"})\n", "tracker_02 = tracker_02[tracker_02[\"City Name\"] != \"\"]\n", "print(tracker_02.shape)\n", "tracker_02.head()\n", "\n", "tracker_02.columns = [\n", "    \"City Name\",\n", "    \"Outlet Name\",\n", "    \"SAP Vendor Code\",\n", "    \"Supplier Name\",\n", "    \"Manufacturer Name\",\n", "    \"Vednor GSTN as per Gpos\",\n", "    \"Vendor GSTIN NO\",\n", "    \"True/False\",\n", "    \"Vendor Location\",\n", "    \"GRN Date\",\n", "    \"GRN NO\",\n", "    \"PO NO\",\n", "    \"Priority\",\n", "    \"PO Date\",\n", "    \"Invoice Date\",\n", "    \"Invoice no as per Po<PERSON>\",\n", "    \"Total Invoice Value\",\n", "    \"TCS Value\",\n", "    \"Grn Value\",\n", "    \"Total GRN Value\",\n", "    \"Unique\",\n", "    \"GRD (Goods Rcv Date)\",\n", "    \"Credit Day's\",\n", "    \"Payment Due date\",\n", "    \"Invoice link\",\n", "    \"SAP DOC No\",\n", "    \"Phy Invoice No\",\n", "    \"Phy Invoice Date\",\n", "    \"Inv Value Before Tax\",\n", "    \"Invoice Value\",\n", "    \"Discrepancy Value\",\n", "    \"TCS Amount\",\n", "    \"Discount Value\",\n", "    \"Freight Charges\",\n", "    \"Approved Payment\",\n", "    \"GRN Diff\",\n", "    \"Invoice Check Status\",\n", "    \"Invoice OK date\",\n", "    \"Owner\",\n", "    \"Payment Comment\",\n", "    \"MSME Vendor\",\n", "    \"Purchase Return\",\n", "    \"Incentive on sale\",\n", "    \"Bargain/Rate Diff\",\n", "    \"TDS Value\",\n", "    \"Advance Paid\",\n", "    \"Paid Value\",\n", "    \"Rest Payable\",\n", "    \"Payment Status\",\n", "    \"Payment Mode\",\n", "    \"Payment Date\",\n", "    \"Payment reference No.\",\n", "    \"Remarks\",\n", "    \"identifier\",\n", "]\n", "\n", "tracker_02 = tracker_02[\n", "    [\n", "        \"City Name\",\n", "        \"Outlet Name\",\n", "        \"SAP Vendor Code\",\n", "        \"Supplier Name\",\n", "        \"Manufacturer Name\",\n", "        \"Vednor GSTN as per Gpos\",\n", "        \"Vendor GSTIN NO\",\n", "        \"True/False\",\n", "        \"Vendor Location\",\n", "        \"GRN Date\",\n", "        \"GRN NO\",\n", "        \"PO NO\",\n", "        \"Priority\",\n", "        \"PO Date\",\n", "        \"Invoice Date\",\n", "        \"Invoice no as per Po<PERSON>\",\n", "        \"Total Invoice Value\",\n", "        \"TCS Value\",\n", "        \"Grn Value\",\n", "        \"Total GRN Value\",\n", "        \"Unique\",\n", "        \"GRD (Goods Rcv Date)\",\n", "        \"Credit Day's\",\n", "        \"Payment Due date\",\n", "        \"Invoice link\",\n", "        \"SAP DOC No\",\n", "        \"Phy Invoice No\",\n", "        \"Phy Invoice Date\",\n", "        \"Inv Value Before Tax\",\n", "        \"Invoice Value\",\n", "        \"Discrepancy Value\",\n", "        \"TCS Amount\",\n", "        \"Discount Value\",\n", "        \"Freight Charges\",\n", "        \"Approved Payment\",\n", "        \"GRN Diff\",\n", "        \"Invoice Check Status\",\n", "        \"Invoice OK date\",\n", "        \"Owner\",\n", "        \"Payment Comment\",\n", "        \"MSME Vendor\",\n", "        \"Purchase Return\",\n", "        \"Incentive on sale\",\n", "        \"Bargain/Rate Diff\",\n", "        \"TDS Value\",\n", "        \"Advance Paid\",\n", "        \"Paid Value\",\n", "        \"Rest Payable\",\n", "        \"Payment Status\",\n", "        \"Payment Mode\",\n", "        \"Payment Date\",\n", "        \"Payment reference No.\",\n", "        \"Remarks\",\n", "        \"identifier\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "57653d3c-638a-46f6-821a-529fb11c4daf", "metadata": {}, "outputs": [], "source": ["tracker_02[\"GRN Date\"] = pd.to_datetime(tracker_02[\"GRN Date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "be9a31ec-bc85-49f4-aff7-d947e9ca3622", "metadata": {}, "outputs": [], "source": ["# tracker_02.to_csv('tracker_02.csv')\n", "tracker_02.shape[1], tracker_01.shape[1]"]}, {"cell_type": "code", "execution_count": null, "id": "8397a9c8-dc12-454b-ad80-14ecbd2a108c", "metadata": {}, "outputs": [], "source": ["tracker_01 = pd.concat([tracker_01, tracker_02])\n", "tracker_01.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "0704aa3f-379d-49c2-9de6-b73d8d870439", "metadata": {}, "outputs": [], "source": ["# Tracker 3\n", "# https://docs.google.com/spreadsheets/d/1Dvsf15M7NGKDvTmSwyRWHOs2zfIY_fBMQHsDWGr315Q/edit#gid=434013069\n", "\n", "# Need Access\n", "sheet_id = \"1Dvsf15M7NGKDvTmSwyRWHOs2zfIY_fBMQHsDWGr315Q\"\n", "t3_hot_fnv_df = pb.from_sheets(sheet_id, \"HOT_FNV_MERGED\")\n", "t3_hot_fnv_df.columns = t3_hot_fnv_df.iloc[1]\n", "t3_hot_fnv_df = t3_hot_fnv_df.iloc[2:]\n", "t3_hot_fnv_df[\"identifier\"] = \"HOT FnV\"\n", "\n", "\n", "t3_hot_fnv_df = t3_hot_fnv_df.rename_axis(None, axis=1)\n", "\n", "t3_hot_fnv_df.columns = [\n", "    \"ENTITY\",\n", "    \"CITY\",\n", "    \"OUTLET NAME\",\n", "    \"Supplier Name\",\n", "    \"Manufacturer Name\",\n", "    \"Vendor GSTIN NO\",\n", "    \"GRN Date\",\n", "    \"GRN NO\",\n", "    \"PO NO\",\n", "    \"PO DATE\",\n", "    \"remarks\",\n", "    \"link\",\n", "    \"INVOICE DATE\",\n", "    \"Inv no\",\n", "    \"GRN VALUE\",\n", "    \"FREIGHT\",\n", "    \" BALANCE PAYABLE \",\n", "    \"unique\",\n", "    \"Goods Rcvd Date\",\n", "    \"Credit Period\",\n", "    \"Due date\",\n", "    \"Invoice link\",\n", "    \"Phy Invoice Date\",\n", "    \"Sap Code\",\n", "    \"Unq\",\n", "    \"Grn Count\",\n", "    \"Phy Bill No\",\n", "    \"Phy Bill Value\",\n", "    \"Discrepancy Value\",\n", "    \"Freight Charges\",\n", "    \"Payable Value\",\n", "    \"GRN Diff\",\n", "    \"Invoice Check Status\",\n", "    \"Team Responsible\",\n", "    \"Initiate/Invoice OK date\",\n", "    \"Owner\",\n", "    \"PRN / Others\",\n", "    \"Advance adjustment\",\n", "    \"TDS\",\n", "    \"Paid Value\",\n", "    \"Rest Payable\",\n", "    \"Payment Status\",\n", "    \"Payment mode\",\n", "    \"Payment Date\",\n", "    \"Payment reference No.\",\n", "    \"Remarks\",\n", "    \"identifier\",\n", "]\n", "\n", "\n", "t3_hot_fnv_df = t3_hot_fnv_df.rename(\n", "    columns={\n", "        \"Advance adjustment\": \"Advance Paid\",\n", "        \"TDS\": \"TDS Value\",\n", "        \"PRN / Others\": \"Purchase Return\",\n", "        \"Initiate/Invoice OK date\": \"Invoice OK date\",\n", "        \"Payable Value\": \"Approved Payment\",\n", "        \"Phy Bill Value\": \"Invoice Value\",\n", "        \"Phy Bill No\": \"Phy Invoice No\",\n", "        \"Sap Code\": \"SAP DOC No\",\n", "        \"Due date\": \"Payment Due date\",\n", "        \"Credit Period\": \"Credit Day's\",\n", "        \"Goods Rcvd Date\": \"GRD (Goods Rcv Date)\",\n", "        \"Unq\": \"Unique\",\n", "        \"GRN VALUE\": \"Grn Value\",\n", "        \" BALANCE PAYABLE \": \"Total Invoice Value\",\n", "        \"INVOICE DATE\": \"Invoice Date\",\n", "        \"PO DATE\": \"PO Date\",\n", "        \"Vendor GSTIN NO\": \"Vendor GSTIN NO\",\n", "        \"Manufacturer Name\": \"Manufacturer Name\",\n", "        \"Supplier Name\": \"Supplier Name\",\n", "        \"OUTLET NAME\": \"Outlet Name\",\n", "        \"CITY\": \"City Name\",\n", "    }\n", ")\n", "t3_hot_fnv_df[\n", "    [\n", "        \"Payment Comment\",\n", "        \"SAP Vendor Code\",\n", "        \"Vednor GSTN as per Gpos\",\n", "        \"True/False\",\n", "        \"Vendor Location\",\n", "        \"Priority\",\n", "        \"TCS Value\",\n", "        \"Total GRN Value\",\n", "        \"Inv Value Before Tax\",\n", "        \"TCS Amount\",\n", "        \"Discount Value\",\n", "        \"Payment Mode\",\n", "        \"MSME Vendor\",\n", "        \"Incentive on sale\",\n", "        \"Bargain/Rate Diff\",\n", "        \"Invoice no as per Po<PERSON>\",\n", "    ]\n", "] = 0\n", "\n", "\n", "t3_hot_fnv_df = t3_hot_fnv_df[\n", "    [\n", "        \"City Name\",\n", "        \"Outlet Name\",\n", "        \"SAP Vendor Code\",\n", "        \"Supplier Name\",\n", "        \"Manufacturer Name\",\n", "        \"Vednor GSTN as per Gpos\",\n", "        \"Vendor GSTIN NO\",\n", "        \"True/False\",\n", "        \"Vendor Location\",\n", "        \"GRN Date\",\n", "        \"GRN NO\",\n", "        \"PO NO\",\n", "        \"Priority\",\n", "        \"PO Date\",\n", "        \"Invoice Date\",\n", "        \"Invoice no as per Po<PERSON>\",\n", "        \"Total Invoice Value\",\n", "        \"TCS Value\",\n", "        \"Grn Value\",\n", "        \"Total GRN Value\",\n", "        \"Unique\",\n", "        \"GRD (Goods Rcv Date)\",\n", "        \"Credit Day's\",\n", "        \"Payment Due date\",\n", "        \"Invoice link\",\n", "        \"SAP DOC No\",\n", "        \"Phy Invoice No\",\n", "        \"Phy Invoice Date\",\n", "        \"Inv Value Before Tax\",\n", "        \"Invoice Value\",\n", "        \"Discrepancy Value\",\n", "        \"TCS Amount\",\n", "        \"Discount Value\",\n", "        \"Freight Charges\",\n", "        \"Approved Payment\",\n", "        \"GRN Diff\",\n", "        \"Invoice Check Status\",\n", "        \"Invoice OK date\",\n", "        \"Owner\",\n", "        \"Payment Comment\",\n", "        \"MSME Vendor\",\n", "        \"Purchase Return\",\n", "        \"Incentive on sale\",\n", "        \"Bargain/Rate Diff\",\n", "        \"TDS Value\",\n", "        \"Advance Paid\",\n", "        \"Paid Value\",\n", "        \"Rest Payable\",\n", "        \"Payment Status\",\n", "        \"Payment Mode\",\n", "        \"Payment Date\",\n", "        \"Payment reference No.\",\n", "        \"Remarks\",\n", "        \"identifier\",\n", "    ]\n", "]\n", "\n", "t3_hot_fnv_df[\"GRN Date\"] = pd.to_datetime(t3_hot_fnv_df[\"GRN Date\"])\n", "\n", "tracker_01.shape[1], tracker_02.shape[1], t3_hot_fnv_df.shape[1]"]}, {"cell_type": "code", "execution_count": null, "id": "011b79cf-9493-4aeb-8a3a-9ddfff5a2a8e", "metadata": {}, "outputs": [], "source": ["tracker_01 = pd.concat([tracker_01, t3_hot_fnv_df])"]}, {"cell_type": "code", "execution_count": null, "id": "4ac2d314-3aa7-4690-8e31-fa69201cd22b", "metadata": {}, "outputs": [], "source": ["# Tracker 4\n", "# https://docs.google.com/spreadsheets/d/1UfUDKck-JMgwXZd6qz6V8g05LmgyGV5LFh69jkKk5_4/edit#gid=656576535\n", "\n", "# Need Access\n", "sheet_id = \"1UfUDKck-JMgwXZd6qz6V8g05LmgyGV5LFh69jkKk5_4\"\n", "t4_90mm_df = pb.from_sheets(sheet_id, \"90Min / CC / L7A/LT\")\n", "\n", "t4_90mm_df.columns = t4_90mm_df.iloc[1]\n", "t4_90mm_df = t4_90mm_df.iloc[2:]\n", "t4_90mm_df = t4_90mm_df[t4_90mm_df[\"City\"] != \"\"]\n", "t4_90mm_df[\"identifier\"] = \"DS Grocery\"\n", "\n", "t4_90mm_df.columns = [\n", "    \"City\",\n", "    \"Entity\",\n", "    \"Outlet Name\",\n", "    \"SAP Vendor Code\",\n", "    \"Supplier Name\",\n", "    \"Manufacturer Name\",\n", "    \"Vednor GSTN as per Gpos\",\n", "    \"Vendor GSTIN NO\",\n", "    \"True/False\",\n", "    \"Vendor Location\",\n", "    \"GRN Date\",\n", "    \"GRN NO\",\n", "    \"PO NO\",\n", "    \"PO Date\",\n", "    \"Invoice Date\",\n", "    \"Invoice no as per Po<PERSON>\",\n", "    \"TCS Value\",\n", "    \"Grn Value\",\n", "    \"Total GRN Value\",\n", "    \"Unique\",\n", "    \"GRD (Goods Rcv Date)\",\n", "    \"Credit Day's\",\n", "    \"Payment Due date\",\n", "    \"Invoice link\",\n", "    \"SAP DOC No\",\n", "    \"SAP Inv Verification\",\n", "    \"Tax Rate\",\n", "    \"Tally Voucher No.\",\n", "    \"Phy Invoice No\",\n", "    \"Phy Invoice Date\",\n", "    \"Phy Value\",\n", "    \"Discrepancy Value\",\n", "    \"TCS Amount\",\n", "    \"Discount Value\",\n", "    \"Freight Charges\",\n", "    \"Approved Payment\",\n", "    \"GRN Diff\",\n", "    \"Invoice Check Status\",\n", "    \"Invoice Check Status 2\",\n", "    \"Owner\",\n", "    \"Invoice OK date\",\n", "    \"Payment Comment\",\n", "    \"MSME Vendor\",\n", "    \"Purchase Return\",\n", "    \"Incentive on sale\",\n", "    \"Bargain/Rate Diff\",\n", "    \"Advance Paid\",\n", "    \"Paid Value\",\n", "    \"Rest Payable\",\n", "    \"Payment Status\",\n", "    \"Payment Mode\",\n", "    \"Payment Date\",\n", "    \"Payment reference No.\",\n", "    \"Remarks\",\n", "    \"Active/Incative\",\n", "    \"identifier\",\n", "]\n", "\n", "t4_90mm_df[\n", "    [\n", "        \"Priority\",\n", "        \"Total Invoice Value\",\n", "        \"Inv Value Before Tax\",\n", "        \"Payment Comment\",\n", "        \"TDS Value\",\n", "    ]\n", "] = 0\n", "\n", "t4_90mm_df = t4_90mm_df.rename(\n", "    columns={\n", "        \"City\": \"City Name\",\n", "        \"Phy Value\": \"Total Invoice Value\",\n", "        \"Phy Value\": \"Invoice Value\",\n", "    }\n", ")\n", "\n", "\n", "t4_90mm_df = t4_90mm_df[\n", "    [\n", "        \"City Name\",\n", "        \"Outlet Name\",\n", "        \"SAP Vendor Code\",\n", "        \"Supplier Name\",\n", "        \"Manufacturer Name\",\n", "        \"Vednor GSTN as per Gpos\",\n", "        \"Vendor GSTIN NO\",\n", "        \"True/False\",\n", "        \"Vendor Location\",\n", "        \"GRN Date\",\n", "        \"GRN NO\",\n", "        \"PO NO\",\n", "        \"Priority\",\n", "        \"PO Date\",\n", "        \"Invoice Date\",\n", "        \"Invoice no as per Po<PERSON>\",\n", "        \"Total Invoice Value\",\n", "        \"TCS Value\",\n", "        \"Grn Value\",\n", "        \"Total GRN Value\",\n", "        \"Unique\",\n", "        \"GRD (Goods Rcv Date)\",\n", "        \"Credit Day's\",\n", "        \"Payment Due date\",\n", "        \"Invoice link\",\n", "        \"SAP DOC No\",\n", "        \"Phy Invoice No\",\n", "        \"Phy Invoice Date\",\n", "        \"Inv Value Before Tax\",\n", "        \"Invoice Value\",\n", "        \"Discrepancy Value\",\n", "        \"TCS Amount\",\n", "        \"Discount Value\",\n", "        \"Freight Charges\",\n", "        \"Approved Payment\",\n", "        \"GRN Diff\",\n", "        \"Invoice Check Status\",\n", "        \"Invoice OK date\",\n", "        \"Owner\",\n", "        \"Payment Comment\",\n", "        \"MSME Vendor\",\n", "        \"Purchase Return\",\n", "        \"Incentive on sale\",\n", "        \"Bargain/Rate Diff\",\n", "        \"TDS Value\",\n", "        \"Advance Paid\",\n", "        \"Paid Value\",\n", "        \"Rest Payable\",\n", "        \"Payment Status\",\n", "        \"Payment Mode\",\n", "        \"Payment Date\",\n", "        \"Payment reference No.\",\n", "        \"Remarks\",\n", "        \"identifier\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "25f7955f-f7e0-483a-9ea0-b53673f4ec62", "metadata": {}, "outputs": [], "source": ["t4_90mm_df[\"GRN Date\"] = pd.to_datetime(t4_90mm_df[\"GRN Date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "0033404d-74e7-4d7d-96e0-728e1222438e", "metadata": {}, "outputs": [], "source": ["t4_90mm_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f98aea8a-4b39-4017-bbf3-78b8026e5a51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e94ce87c-e2aa-4640-bd00-b97cf2b8dd38", "metadata": {}, "outputs": [], "source": ["tracker_01.shape[1], tracker_02.shape[1], t3_hot_fnv_df.shape[1], t4_90mm_df.shape[1]"]}, {"cell_type": "code", "execution_count": null, "id": "c6865615-39d4-48c6-98bf-5730660174fd", "metadata": {}, "outputs": [], "source": ["tracker_01 = pd.concat([tracker_01, t4_90mm_df])"]}, {"cell_type": "code", "execution_count": null, "id": "f52618e8-f122-4d99-85df-e35638c63c65", "metadata": {}, "outputs": [], "source": ["# Tracker 5\n", "# https://docs.google.com/spreadsheets/d/199MO5i942s3ydepYP_e7vvrwYSzJDik8GK9u9tdnE1w/edit#gid=434013069\n", "\n", "# # Need Access\n", "sheet_id = \"199MO5i942s3ydepYP_e7vvrwYSzJDik8GK9u9tdnE1w\"\n", "t5_hot_fnv_df = pb.from_sheets(sheet_id, \"HOT_FNV_MERGED\")\n", "t5_hot_fnv_df.columns = t5_hot_fnv_df.iloc[1]\n", "t5_hot_fnv_df = t5_hot_fnv_df.iloc[2:]\n", "\n", "t5_hot_fnv_df = t5_hot_fnv_df[t5_hot_fnv_df[\"CITY\"] != \"\"]\n", "t5_hot_fnv_df[\"identifier\"] = \"DS FnV\""]}, {"cell_type": "code", "execution_count": null, "id": "864edddc-fa73-4aec-91e4-c5005c615d83", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "05cc573b-bd8c-41bf-b2d6-c37fd4cc29da", "metadata": {}, "outputs": [], "source": ["t5_hot_fnv_df.columns = [\n", "    \"ENTITY\",\n", "    \"CITY\",\n", "    \"OUTLET NAME\",\n", "    \"Supplier Name\",\n", "    \"Manufacturer Name\",\n", "    \"Vendor GSTIN NO\",\n", "    \"GRN Date\",\n", "    \"GRN NO\",\n", "    \"PO NO\",\n", "    \"PO DATE\",\n", "    \"remarks\",\n", "    \"link\",\n", "    \"INVOICE DATE\",\n", "    \"Inv no\",\n", "    \"GRN VALUE\",\n", "    \"FREIGHT\",\n", "    \" BALANCE PAYABLE \",\n", "    \"unique\",\n", "    \"Goods Rcvd Date\",\n", "    \"Credit Period\",\n", "    \"Due date\",\n", "    \"Invoice link\",\n", "    \"Phy Invoice Date\",\n", "    \"Sap Code\",\n", "    \"Unq\",\n", "    \"Grn Count\",\n", "    \"Phy Bill No\",\n", "    \"Phy Bill Value\",\n", "    \"Discrepancy Value\",\n", "    \"Freight Charges\",\n", "    \"Payable Value\",\n", "    \"GRN Diff\",\n", "    \"Invoice Check Status\",\n", "    \"Team Responsible\",\n", "    \"Initiate/Invoice OK date\",\n", "    \"Owner\",\n", "    \"PRN / Others\",\n", "    \"Advance adjustment\",\n", "    \"TDS\",\n", "    \"Paid Value\",\n", "    \"Rest Payable\",\n", "    \"Payment Status\",\n", "    \"Payment mode\",\n", "    \"Payment Date\",\n", "    \"Payment reference No.\",\n", "    \"Remarks\",\n", "    \"identifier\",\n", "]\n", "\n", "t5_hot_fnv_df = t5_hot_fnv_df.rename(\n", "    columns={\n", "        \"Advance adjustment\": \"Advance Paid\",\n", "        \"TDS\": \"TDS Value\",\n", "        \"PRN / Others\": \"Purchase Return\",\n", "        \"Initiate/Invoice OK date\": \"Invoice OK date\",\n", "        \"Payable Value\": \"Approved Payment\",\n", "        \"Phy Bill Value\": \"Invoice Value\",\n", "        \"Phy Bill No\": \"Phy Invoice No\",\n", "        \"Sap Code\": \"SAP DOC No\",\n", "        \"Due date\": \"Payment Due date\",\n", "        \"Credit Period\": \"Credit Day's\",\n", "        \"Goods Rcvd Date\": \"GRD (Goods Rcv Date)\",\n", "        \"Unq\": \"Unique\",\n", "        \"GRN VALUE\": \"Grn Value\",\n", "        \" BALANCE PAYABLE \": \"Total Invoice Value\",\n", "        \"INVOICE DATE\": \"Invoice Date\",\n", "        \"PO DATE\": \"PO Date\",\n", "        \"Vendor GSTIN NO\": \"Vendor GSTIN NO\",\n", "        \"Manufacturer Name\": \"Manufacturer Name\",\n", "        \"Supplier Name\": \"Supplier Name\",\n", "        \"OUTLET NAME\": \"Outlet Name\",\n", "        \"CITY\": \"City Name\",\n", "    }\n", ")\n", "t5_hot_fnv_df[\n", "    [\n", "        \"Payment Comment\",\n", "        \"SAP Vendor Code\",\n", "        \"Vednor GSTN as per Gpos\",\n", "        \"True/False\",\n", "        \"Vendor Location\",\n", "        \"Priority\",\n", "        \"TCS Value\",\n", "        \"Total GRN Value\",\n", "        \"Inv Value Before Tax\",\n", "        \"TCS Amount\",\n", "        \"Discount Value\",\n", "        \"Payment Mode\",\n", "        \"MSME Vendor\",\n", "        \"Incentive on sale\",\n", "        \"Bargain/Rate Diff\",\n", "        \"Invoice no as per Po<PERSON>\",\n", "    ]\n", "] = 0\n", "\n", "\n", "t5_hot_fnv_df = t5_hot_fnv_df[\n", "    [\n", "        \"City Name\",\n", "        \"Outlet Name\",\n", "        \"SAP Vendor Code\",\n", "        \"Supplier Name\",\n", "        \"Manufacturer Name\",\n", "        \"Vednor GSTN as per Gpos\",\n", "        \"Vendor GSTIN NO\",\n", "        \"True/False\",\n", "        \"Vendor Location\",\n", "        \"GRN Date\",\n", "        \"GRN NO\",\n", "        \"PO NO\",\n", "        \"Priority\",\n", "        \"PO Date\",\n", "        \"Invoice Date\",\n", "        \"Invoice no as per Po<PERSON>\",\n", "        \"Total Invoice Value\",\n", "        \"TCS Value\",\n", "        \"Grn Value\",\n", "        \"Total GRN Value\",\n", "        \"Unique\",\n", "        \"GRD (Goods Rcv Date)\",\n", "        \"Credit Day's\",\n", "        \"Payment Due date\",\n", "        \"Invoice link\",\n", "        \"SAP DOC No\",\n", "        \"Phy Invoice No\",\n", "        \"Phy Invoice Date\",\n", "        \"Inv Value Before Tax\",\n", "        \"Invoice Value\",\n", "        \"Discrepancy Value\",\n", "        \"TCS Amount\",\n", "        \"Discount Value\",\n", "        \"Freight Charges\",\n", "        \"Approved Payment\",\n", "        \"GRN Diff\",\n", "        \"Invoice Check Status\",\n", "        \"Invoice OK date\",\n", "        \"Owner\",\n", "        \"Payment Comment\",\n", "        \"MSME Vendor\",\n", "        \"Purchase Return\",\n", "        \"Incentive on sale\",\n", "        \"Bargain/Rate Diff\",\n", "        \"TDS Value\",\n", "        \"Advance Paid\",\n", "        \"Paid Value\",\n", "        \"Rest Payable\",\n", "        \"Payment Status\",\n", "        \"Payment Mode\",\n", "        \"Payment Date\",\n", "        \"Payment reference No.\",\n", "        \"Remarks\",\n", "        \"identifier\",\n", "    ]\n", "]\n", "\n", "t5_hot_fnv_df[\"GRN Date\"] = pd.to_datetime(t5_hot_fnv_df[\"GRN Date\"])\n", "tracker_01.shape[1], tracker_02.shape[1], t3_hot_fnv_df.shape[1], t4_90mm_df.shape[\n", "    1\n", "], t5_hot_fnv_df.shape[1]"]}, {"cell_type": "code", "execution_count": null, "id": "ed622c3a-0df8-4117-962a-9a4eb803ad3f", "metadata": {}, "outputs": [], "source": ["tracker_01 = pd.concat([tracker_01, t5_hot_fnv_df])"]}, {"cell_type": "raw", "id": "2e8773eb-8e99-4a72-afa9-59d9828a1ed7", "metadata": {}, "source": ["t5_hot_fnv_df.to_csv('tracker_05.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "453fafc8-f2d2-44b6-9f5c-56ecf79625fe", "metadata": {}, "outputs": [], "source": ["t5_hot_fnv_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b268533a-9a43-40bb-8a82-923be96f1038", "metadata": {}, "outputs": [], "source": ["tracker_01.shape[0] * tracker_01.shape[1]"]}, {"cell_type": "markdown", "id": "136dd976-f16a-44cb-b81a-6d4a563248ba", "metadata": {}, "source": ["# Adding Additional Columns"]}, {"cell_type": "code", "execution_count": null, "id": "3c45847b-0240-48f0-b1bf-b7a7c8c7ddee", "metadata": {}, "outputs": [], "source": ["tracker_01[\"Finance Remarks\"] = tracker_01[\"Invoice Check Status\"].copy()\n", "# tracker_01"]}, {"cell_type": "code", "execution_count": null, "id": "fcda8c2e-b7cc-4df1-aae1-22f3fb44e16b", "metadata": {}, "outputs": [], "source": ["tracker_01[\"Payment Due date\"] = np.where(\n", "    (tracker_01[\"Payment Due date\"] == \"#N/A\")\n", "    | (tracker_01[\"Payment Due date\"] == \"#VALUE!\"),\n", "    \"\",\n", "    tracker_01[\"Payment Due date\"],\n", ")\n", "\n", "# VALUE!\n", "tracker_01[\"Payment Due date\"] = pd.to_datetime(\n", "    tracker_01[\"Payment Due date\"], errors=\"coerce\"\n", ")\n", "\n", "tracker_01[\"due\"] = np.where(\n", "    tracker_01[\"Payment Due date\"].isna(),\n", "    \"Missing Due Date\",\n", "    np.where(\n", "        tracker_01[\"Payment Due date\"] <= pd.to_datetime(date.today()), \"Due\", \"Not Due\"\n", "    ),\n", ")\n", "\n", "\n", "tracker_01.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "ecbfd9a2-a97a-40e3-ba12-7219fae4ff8d", "metadata": {}, "outputs": [], "source": ["tracker_0f = tracker_01.reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "a59cc993-38ee-4dca-a1ad-24b99d2ec5af", "metadata": {}, "outputs": [], "source": ["tracker_0f.shape[0], tracker_01.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "67952d48-64bf-4573-b4aa-15da75d2e3d2", "metadata": {}, "outputs": [], "source": ["run_id = str(date.today())\n", "run_id"]}, {"cell_type": "code", "execution_count": null, "id": "ba9bbe02-92e1-4fa8-9fb1-7c63131227f5", "metadata": {}, "outputs": [], "source": ["log_file_name = f\"{outputs}/consolidated_grn_report_{run_id}.csv\"\n", "tracker_01.to_csv(log_file_name, index=False)\n", "smart_attachment = to_S3(log_file_name, \"Consolidated GRN Report\")"]}, {"cell_type": "code", "execution_count": null, "id": "d7cd6910-5f5f-42fb-8474-1b829760e50a", "metadata": {}, "outputs": [], "source": ["html_content = f\"\"\"\n", "Hi,<br><br>\n", "\n", "You can download the consolidated GRN report\n", "<a href={smart_attachment}>\n", "here\n", "</a><br><br>\n", "Please note that this data will not be available for download from the above link, after 10 days.<br>\n", "<br>\n", "<PERSON><PERSON>,<br>\n", "Data Inventory\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "0fd9c180-bbe5-4cbe-9f6f-9356dd1202cf", "metadata": {}, "outputs": [], "source": ["# html_content"]}, {"cell_type": "code", "execution_count": null, "id": "c1fa6acb-8c70-413e-90e5-a6d6128c1133", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1RkShYAaPaXckJitGo7Ok6AKFuOe-moZ247p7tZzbuQM\"  # \"1psqGDgeVN1k9AEY8klniOeA05CrZXJ243BNdJZvL3lM\"\n", "rec = pb.from_sheets(sheet_id, \"emails\")\n", "mail_list = [\n", "    x\n", "    for x in rec[\"Email\"].to_list()\n", "    if \"@grofers.com\" in x or \"@handsontrades.com\" in x\n", "]\n", "mail_list"]}, {"cell_type": "code", "execution_count": null, "id": "f536a400-d7cb-41c8-b191-3fc58fe01534", "metadata": {}, "outputs": [], "source": ["cc = [\"<EMAIL>\", \"<EMAIL>\"]\n", "subject = \"Consolidated GRN Report for - \" + run_id\n", "print(subject)\n", "to_email = mail_list\n", "from_email = \"<EMAIL>\""]}, {"cell_type": "code", "execution_count": null, "id": "8f5bdbca-7bd6-4adb-a459-b3ed3b707e47", "metadata": {}, "outputs": [], "source": ["pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    cc=cc,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}