{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta, date as dt\n", "import math\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_date = datetime.now().strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = [[3, 1], [32, 1]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cluster_facilities = pd.DataFrame(data, columns=[\"cluster_facility_id\", \"cluster_city\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cluster_facilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cluster_facility_combination = (\n", "    cluster_facilities.groupby(\"cluster_city\")[\"cluster_facility_id\"].apply(list).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cluster_facility_combination"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id_list = tuple(cluster_facilities.cluster_facility_id.values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1QiJ_ShSF6431R0Zc5MWOqWC1uislFAmsD72EYWknbp8\"\n", "\n", "sheet_name = \"suggested\"\n", "df = pb.from_sheets(sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"vendor_id\"] = df[\"vendor_id\"].astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_list = tuple(df.vendor_id.values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"select fi.* from\n", "ars.final_indent fi\n", "inner join (select facility_id,max(run_id) as run_id from ars.job_run\n", "where date(started_at)=current_date\n", "and is_simulation=0\n", "and facility_id in {facility_id_list}\n", "group by 1)run\n", "on fi.physical_facility_id=run.facility_id\n", "and fi.run_id=run.run_id\n", "where aligned_vendor_id in {vendor_list}\"\"\".format(\n", "    facility_id_list=facility_id_list, vendor_list=vendor_list\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents_raw = pd.read_sql_query(sql=query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents_raw.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents = indents_raw.merge(\n", "    cluster_facilities,\n", "    left_on=[\"city\", \"physical_facility_id\"],\n", "    right_on=[\"cluster_city\", \"cluster_facility_id\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents[\"quantity_in_case\"] = np.where(\n", "    indents.case_sensitivity_type == 0,\n", "    np.where(\n", "        indents.inner_case_size > 1,\n", "        indents.final_indent_quantity / indents.inner_case_size,\n", "        np.where(\n", "            indents.outer_case_size > 1,\n", "            indents.final_indent_quantity / indents.outer_case_size,\n", "            indents.final_indent_quantity,\n", "        ),\n", "    ),\n", "    np.where(\n", "        indents.case_sensitivity_type == 1,\n", "        np.where(\n", "            indents.inner_case_size > 0,\n", "            indents.final_indent_quantity / indents.inner_case_size,\n", "            indents.final_indent_quantity,\n", "        ),\n", "        np.where(\n", "            indents.case_sensitivity_type == 2,\n", "            np.where(\n", "                indents.outer_case_size > 0,\n", "                indents.final_indent_quantity / indents.outer_case_size,\n", "                indents.final_indent_quantity,\n", "            ),\n", "            indents.final_indent_quantity,\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents[\"value\"] = indents.final_indent_quantity * indents.landing_price\n", "\n", "indents[\"weight\"] = indents.final_indent_quantity * indents.weight_in_gm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents[\"load_values\"] = np.where(\n", "    indents.load_type == 1,\n", "    indents.value,\n", "    np.where(indents.load_type == 2, indents.weight, indents.quantity_in_case),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values = (\n", "    indents.groupby(\n", "        [\"aligned_vendor_id\", \"aligned_vendor_name\", \"cluster_city\", \"load_type\", \"load_size\"]\n", "    )[\"load_values\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values = (\n", "    indents.groupby(\n", "        [\"aligned_vendor_id\", \"aligned_vendor_name\", \"cluster_city\", \"load_type\", \"load_size\"]\n", "    )\n", "    .agg({\"load_values\": \"sum\", \"error_details\": lambda x: list(x.unique())})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values[\"load_values\"] = (vendor_load_values[\"load_values\"]).apply(\n", "    lambda x: math.ceil(x)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values[\"po_cycle_revised\"] = (\n", "    vendor_load_values.load_values / vendor_load_values.load_size\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values.to_csv(\"vendor_load_values.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unamtched_vendors = vendor_load_values[\n", "    (vendor_load_values.load_values != 0) & (vendor_load_values.po_cycle_revised <= 0.9999)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["matched_vendors = vendor_load_values[\n", "    (vendor_load_values.load_values != 0) & (vendor_load_values.po_cycle_revised >= 0.9999)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unamtched_vendors.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_list = matched_vendors.aligned_vendor_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eligible_vendors_indents = indents[indents.aligned_vendor_id.isin(vendor_list)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eligible_vendors_indents.to_csv(\"cluster_delivery_eligible_vendors_indents\" + current_date + \".csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indents[indents.aligned_vendor_id.isin(unamtched_vendors.aligned_vendor_id.unique())].to_csv(\n", "    \"cluster_delivery_ineligible_vendor_indents\" + current_date + \".csv\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values[\"cluster_delivery_flag\"] = np.where(\n", "    ((vendor_load_values.po_cycle_revised >= 0.9999) & (vendor_load_values.po_cycle_revised != 0)),\n", "    \"Met load size for cluster delivery\",\n", "    np.where(\n", "        (vendor_load_values.po_cycle_revised < 0.9999) & (vendor_load_values.po_cycle_revised != 0),\n", "        \"Havent met load size\",\n", "        \"Total load size is 0\",\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_clusters = vendor_load_values.merge(\n", "    cluster_facility_combination, on=[\"cluster_city\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_load_values_df = vendor_clusters[\n", "    [\n", "        \"aligned_vendor_id\",\n", "        \"aligned_vendor_name\",\n", "        \"load_size\",\n", "        \"load_values\",\n", "        \"cluster_delivery_flag\",\n", "        \"cluster_facility_id\",\n", "        \"error_details\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["records = vendor_load_values_df.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secrets = pb.get_secret(\"retail/jhub_keys/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "\n", "# In[19]:\n", "\n", "\n", "sender = \"<EMAIL>\"\n", "\n", "to = json.loads(secrets.get(\"cluster_delivery\"))\n", "\n", "\n", "cc = [\"<EMAIL>\"]\n", "\n", "subject = \"Cluster delivery eligible vendors indents for \" + current_date\n", "\n", "\n", "email_template = \"cluster_delivery.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/povms/cluster_delivery/report/cluster_delivery/\"\n", ")\n", "# loader = jinja2.FileSystemLoader(searchpath=\"/home/<USER>/airflow-dags/daggers/povms/cluster_delivery/report/cluster_delivery/\")\n", "# loader = jinja2.FileSystemLoader(searchpath=\"/home/<USER>/Development/grofers/Cluster_delivery/version_2/\")\n", "\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(records=records)\n", "\n", "pb.send_email(\n", "    sender,\n", "    to,\n", "    subject,\n", "    html_content=rendered,\n", "    dryrun=False,\n", "    cc=cc,\n", "    bcc=None,\n", "    files=[\n", "        \"cluster_delivery_eligible_vendors_indents\" + current_date + \".csv\",\n", "        \"cluster_delivery_ineligible_vendor_indents\" + current_date + \".csv\",\n", "    ],\n", "    mime_subtype=\"mixed\",\n", "    mime_charset=\"utf-8\",\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}