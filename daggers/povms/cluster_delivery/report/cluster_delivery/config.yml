dag_name: cluster_delivery
dag_type: report
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: chaitra
  slack_id: UAFM5NR0D
path: povms/cluster_delivery/report/cluster_delivery
paused: true
pool: povms_pool
project_name: cluster_delivery
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 3 * * *
  start_date: '2019-12-25T03:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- cluster_delivery.html
tags: []
template_name: notebook
version: 2
