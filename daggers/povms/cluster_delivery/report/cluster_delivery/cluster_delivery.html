<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}
</style>
</head>
<p>Hi All,</p>
<p>Please find below performance of cluster delivery eligible vendors<br/>
Also attached is the indent file for cluster delivery vendors who met load size for raising PO
    and indent files for vendors who couldn't meet load size values in seperate files. <br/>

<br/>
</p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Vendor ID</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Vendor Name</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Final Indent load value</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Load Size</span></th>
            <th class="tg-0lax"><span style="font-weight:bold"> Cluster delivery flag</span></th>
            <th class="tg-0lax"><span style="font-weight:bold"> Clustered facilites</span></th>
            <th class="tg-0lax"><span style="font-weight:bold"> Error Details</span></th>
            
        </tr>
        {% for record in records %}
        <tr>    
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ record.aligned_vendor_id}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ record.aligned_vendor_name}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ record.load_values}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ record.load_size}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ record.cluster_delivery_flag}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ record.cluster_facility_id}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ record.error_details}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>
<p>Note: Final Indent load value=0 implies PO day for that vendor isnt today

<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>