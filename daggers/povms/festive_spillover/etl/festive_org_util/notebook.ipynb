{"cells": [{"cell_type": "code", "execution_count": null, "id": "7de327f3-4234-46d7-b378-f7ae6ff1daab", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "22fabdef-ed73-47ac-b03b-68fc264821a5", "metadata": {}, "outputs": [], "source": ["CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "a411e85b-90e2-4d3d-a7be-f1f7bdb76d0c", "metadata": {}, "outputs": [], "source": ["excluded_items = pd.read_csv(\"exclude_item_factor.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "203e8816-53ab-489d-9e67-fa9480f55af3", "metadata": {}, "outputs": [], "source": ["excluded_item_list = tuple(excluded_items.item_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "2d176336-d6f5-44ff-8bc7-34db48aa8b9a", "metadata": {}, "outputs": [], "source": ["len(excluded_item_list)"]}, {"cell_type": "code", "execution_count": null, "id": "3767bc8d-ae87-4569-8dbd-425ace0370a6", "metadata": {}, "outputs": [], "source": ["def fetch_storage_type():\n", "    sql = f\"\"\"\n", "        select \n", "            item_id,\n", "             case when (id.storage_type) in (1,8) then 'REGULAR'\n", "            when (id.storage_type) in (4,5) then 'HEAVY'\n", "            when (id.storage_type) in (2,6) then 'COLD'\n", "            when (id.storage_type) in (3,7) then 'FROZEN'\n", "            else 'REGULAR'\n", "        end as storage_type_\n", "        from\n", "            lake_rpc.item_details id\n", "        where\n", "            id.active = 1\n", "        and\n", "            id.approved = 1\n", "        and\n", "            item_id in {excluded_item_list}\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "storage_type = fetch_storage_type()"]}, {"cell_type": "code", "execution_count": null, "id": "40da8225-d0d5-4567-a0c2-58457211cbb8", "metadata": {}, "outputs": [], "source": ["storage_type.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "2af22fe5-3548-46b3-87ea-596c9d252c62", "metadata": {}, "outputs": [], "source": ["storage_type.groupby(\"storage_type_\").agg({\"item_id\": \"count\"})"]}, {"cell_type": "code", "execution_count": null, "id": "fea2de71-a5f6-4af3-9e5a-d491f3bfe171", "metadata": {}, "outputs": [], "source": ["storage_type = storage_type[storage_type[\"storage_type_\"] == \"REGULAR\"]"]}, {"cell_type": "code", "execution_count": null, "id": "f43a8a1e-f3ed-4523-86a3-5a9a2d1c1501", "metadata": {}, "outputs": [], "source": ["excluded_item_list = tuple(storage_type.item_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "e186a0a1-6c22-4d8e-9392-af9499594701", "metadata": {}, "outputs": [], "source": ["def fetch_outlet_list():\n", "    sql = f\"\"\"\n", "        select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "        group by 1,2,3,4\n", "    \"\"\"\n", "    return pd.read_sql_query(sql=sql, con=pb.get_connection(\"[Warehouse] Redshift\"))\n", "\n", "\n", "outlet_list_df = fetch_outlet_list()\n", "outlet_list = tuple(outlet_list_df.outlet_id.unique())\n", "len(outlet_list)"]}, {"cell_type": "code", "execution_count": null, "id": "7278383f-23bd-44f7-90e8-233e937dc247", "metadata": {}, "outputs": [], "source": ["def fetch_ivn():\n", "    sql = f\"\"\"\n", "        With fo as (\n", "        select facility_id, outlet_id, city_id, outlet_name from po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from retail.console_outlet where business_type_id = 7 and lake_active_record) and lake_active_record\n", "        and outlet_id not in (4146)\n", "        group by 1,2,3,4)\n", "    \n", "        select\n", "            iii.outlet_id,\n", "            iii.item_id,\n", "            CAST(iii.quantity as int) as quantity,\n", "            case when master_assortment_substate_id is null then -1\n", "                else master_assortment_substate_id\n", "                end as master_assortment_substate_id,\n", "            substate_reason_id,\n", "            reason_text\n", "        from\n", "            ims.ims_item_inventory iii\n", "        inner join\n", "            fo\n", "        on\n", "            fo.outlet_id = iii.outlet_id\n", "        left join\n", "            rpc.product_facility_master_assortment pfma\n", "        on\n", "            pfma.item_id = iii.item_id\n", "        and\n", "            pfma.facility_id = fo.facility_id\n", "        and\n", "            pfma.active = 1\n", "        and\n", "            pfma.lake_active_record\n", "        left join\n", "            lake_rpc.product_master_assortment_substate_reasons pmasr on pfma.substate_reason_id = pmasr.id and pmasr.active = 1 \n", "        where \n", "            iii.item_id in (\n", "                select distinct item_id\n", "                from rpc.item_details \n", "                where handling_type!='8'\n", "                and storage_type in ('1','8')\n", "            )\n", "        and\n", "            iii.item_id in (\n", "                select distinct item_id\n", "                from rpc.item_category_details\n", "                where l1_id not in (2039, 183)\n", "                and l0_id not in (1487)\n", "            )\n", "        and iii.outlet_id in {outlet_list}\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "inv = fetch_ivn()"]}, {"cell_type": "code", "execution_count": null, "id": "1a4b89d4-d2be-428d-a8ec-697445aaf475", "metadata": {}, "outputs": [], "source": ["def fetch_open_stos():\n", "    sql = f\"\"\"\n", "          SELECT s.merchant_outlet_id AS outlet_id,\n", "                   isi.item_id,\n", "                   sum(coalesce(isi.billed_quantity,0) - coalesce(isi.inward_quantity,0)) AS open_sto_qty\n", "            FROM lake_ims.ims_sto_item isi\n", "            INNER JOIN lake_ims.ims_sto_details s ON isi.sto_id=s.sto_id\n", "            inner join lake_rpc.item_details id on id.item_id = isi.item_id \n", "            WHERE s.sto_state IN (1,2,5)\n", "            and s.created_at >= (current_date - interval '2 days')::timestamp\n", "            group by 1,2\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "open_stos = fetch_open_stos()"]}, {"cell_type": "code", "execution_count": null, "id": "8beb00ef-0eb6-4f60-805e-df764f6d7ea3", "metadata": {}, "outputs": [], "source": ["inv = inv.merge(open_stos, how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "6477a039-092f-4c11-84fe-aae4d22f568b", "metadata": {}, "outputs": [], "source": ["inv.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "0f2486ae-41ba-4d04-bdc9-db64cd836d99", "metadata": {}, "outputs": [], "source": ["inv[\"open_sto_qty\"] = inv[\"open_sto_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "ac89c1c3-8967-4ea2-ae02-b0d24c388474", "metadata": {}, "outputs": [], "source": ["def fetch_current_if():\n", "    sql = f\"\"\"\n", "        select item_id, item_factor\n", "        from metrics.item_factor_org\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "item_factor_org = fetch_current_if()"]}, {"cell_type": "code", "execution_count": null, "id": "e4a236a3-a9df-4ff8-8311-c2674304a929", "metadata": {}, "outputs": [], "source": ["# def fetch_ptype_if():\n", "#     sql = f'''\n", "#         With base as (select if_.item_id, vfp2.name, if_.item_factor, vfp2.p_type, vfp2.vol_factor, vfp2.rpc_if\n", "#                     from metrics.item_factor_org if_\n", "#                     left join (\n", "#                         select\n", "#                             id.item_id,\n", "#                             id.name,\n", "#                             id2.p_type,\n", "#                             id.weight_in_gm/1000 as weight_in_kg,\n", "#                             (1.0*id.length_in_cm*id.breadth_in_cm*id.height_in_cm/1495.2) as rpc_if,\n", "#                             vfp.slope*(id.weight_in_gm/1000) as forecasted_vol,\n", "#                             vfp.slope*(id.weight_in_gm/1495.2) as vol_factor\n", "#                         from\n", "#                             lake_rpc.item_details id\n", "#                         left join\n", "#                             metrics.item_details id2\n", "#                         on\n", "#                             id.item_id = id2.item_id\n", "#                         left join\n", "#                             metrics.volume_forecast_ptype vfp\n", "#                         on\n", "#                             vfp.product_type = id2.p_type\n", "#                     ) vfp2 on vfp2.item_id = if_.item_id\n", "#                     )\n", "\n", "#                     select\n", "#                         item_id,\n", "#                         name,\n", "#                         item_factor,\n", "#                         vol_factor,\n", "#                         rpc_if,\n", "#                         case when vol_factor<= (\n", "#                                     case\n", "#                                         when\n", "#                                             item_factor < rpc_if then item_factor\n", "#                                     else\n", "#                                         rpc_if\n", "#                                     end)\n", "#                             then vol_factor\n", "#                         else (\n", "#                             case\n", "#                                 when\n", "#                                     item_factor < rpc_if then item_factor\n", "#                             else\n", "#                                 rpc_if\n", "#                             end\n", "#                         ) end\n", "#                             as final_item_factor\n", "#                     from\n", "#                         base\n", "#     '''\n", "#     con = pb.get_connection(\"[Warehouse] Redshift\")\n", "#     df = pd.read_sql_query(sql=sql, con=con)\n", "#     return df\n", "# ptype_if = fetch_ptype_if()"]}, {"cell_type": "code", "execution_count": null, "id": "00941da1-cb27-47b9-97d1-f4746f1a85e7", "metadata": {}, "outputs": [], "source": ["exclude_if = pd.read_csv(\"exclude_item_factor.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "f12e36eb-ddfa-4400-b27f-1494ae5f83af", "metadata": {}, "outputs": [], "source": ["exclude_if = exclude_if[[\"item_id\", \"item_factor\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "07a47e38-e588-48ac-bb1a-4b0bc100e358", "metadata": {}, "outputs": [], "source": ["item_factor_org = item_factor_org.append(exclude_if)"]}, {"cell_type": "code", "execution_count": null, "id": "8cb2d91d-af3b-4f46-9529-6e8d0a6ab8ce", "metadata": {}, "outputs": [], "source": ["item_factor_org = item_factor_org.drop_duplicates(subset=\"item_id\", keep=\"last\")"]}, {"cell_type": "code", "execution_count": null, "id": "ae93f776-8e05-4fdf-aa77-535a9a66a4e8", "metadata": {}, "outputs": [], "source": ["ptype_if = item_factor_org"]}, {"cell_type": "code", "execution_count": null, "id": "8bf57932-d421-4f87-b2c6-e6194b063a41", "metadata": {}, "outputs": [], "source": ["ptype_if.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e1c0e05e-c1cc-490e-8c14-b3e506d21101", "metadata": {}, "outputs": [], "source": ["ptype_if[ptype_if[\"item_id\"] == 10034966]"]}, {"cell_type": "code", "execution_count": null, "id": "de18ebae-4470-4b62-929a-9d0539d730d9", "metadata": {}, "outputs": [], "source": ["# ptype_if['final_item_factor'] = ptype_if['final_item_factor'].fillna(ptype_if['item_factor'])\n", "# ptype_if['final_item_factor'] = ptype_if['final_item_factor'].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "5bbf9c5e-d98e-4d77-b48c-294eeabc8eb7", "metadata": {}, "outputs": [], "source": ["# ptype_if = ptype_if[['item_id', 'final_item_factor']]\n", "# ptype_if = ptype_if.rename(columns = {'final_item_factor':'item_factor'})"]}, {"cell_type": "code", "execution_count": null, "id": "7c310959-87a5-4725-99a0-0cc616a8b29c", "metadata": {}, "outputs": [], "source": ["# item_factor_org.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0c6e9c33-486e-4cc4-adfc-1de40357e91f", "metadata": {}, "outputs": [], "source": ["# ptype_if"]}, {"cell_type": "code", "execution_count": null, "id": "c279a1bc-7217-4244-9560-096b50991f78", "metadata": {}, "outputs": [], "source": ["# item_factor_org = item_factor_org.merge(ptype_if, how = 'left')"]}, {"cell_type": "code", "execution_count": null, "id": "9fd431a8-86ff-42e0-a7b0-685cc9d8adfb", "metadata": {}, "outputs": [], "source": ["# item_factor_org = item_factor_org.fillna(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7994dc27-8b35-4b1e-9361-2c36ef5a162a", "metadata": {}, "outputs": [], "source": ["# item_factor_org['item_factor'] = np.where(item_factor_org['item_factor']<item_factor_org['item_factor_ptype'], item_factor_org['item_factor'], item_factor_org['item_factor_ptype'])"]}, {"cell_type": "code", "execution_count": null, "id": "cd3f9661-1aa7-4e2a-883f-e56571f0c8aa", "metadata": {}, "outputs": [], "source": ["ptype_if = ptype_if[[\"item_id\", \"item_factor\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "d18a0dbd-1be9-4d55-ac74-2b6713d675e5", "metadata": {}, "outputs": [], "source": ["item_factor = ptype_if"]}, {"cell_type": "code", "execution_count": null, "id": "9c221f04-35e3-4ff6-b698-b7902f2fff92", "metadata": {}, "outputs": [], "source": ["# item_factor_org.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "22315a4f-a5f3-4cde-adf1-1c8e169da760", "metadata": {}, "outputs": [], "source": ["# item_factor_org[item_factor_org['item_id']==10116220]"]}, {"cell_type": "code", "execution_count": null, "id": "9271a0a2-5e45-4d01-b9af-f0df9267529b", "metadata": {}, "outputs": [], "source": ["# item_factor = ptype_if[['item_id', 'final_item_factor']]"]}, {"cell_type": "code", "execution_count": null, "id": "6c15afe4-4436-42a5-8030-4b5fceaaebfb", "metadata": {}, "outputs": [], "source": ["item_factor = item_factor.rename(columns={\"final_item_factor\": \"item_factor\"})"]}, {"cell_type": "code", "execution_count": null, "id": "4fdeb2ff-1b15-4802-bc98-31eb2ca7de4c", "metadata": {}, "outputs": [], "source": ["inv = inv.merge(item_factor, how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "48f35306-c196-4446-9c44-ff69a3d7425b", "metadata": {}, "outputs": [], "source": ["inv[\"item_factor\"] = inv[\"item_factor\"].fillna(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9d197633-0fb1-4179-a65b-56afd11a0348", "metadata": {}, "outputs": [], "source": ["# inv['tot_inv'] =(inv['open_sto_qty']+inv['quantity'])"]}, {"cell_type": "code", "execution_count": null, "id": "2e255e0d-834b-4155-abcb-3f6eac92a0ce", "metadata": {}, "outputs": [], "source": ["# inv['tot_storage'] = inv['item_factor']*(inv['open_sto_qty']+inv['quantity'])"]}, {"cell_type": "code", "execution_count": null, "id": "d85abe4e-9f63-4f28-909e-65e109607f01", "metadata": {}, "outputs": [], "source": ["inv[\"quantity\"] = inv[\"quantity\"].fillna(0)\n", "inv[\"open_sto_qty\"] = inv[\"open_sto_qty\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "85920dd9-c909-4742-8bd6-9c45708e3b9a", "metadata": {}, "outputs": [], "source": ["inv[\"on_hand_storage\"] = inv[\"quantity\"] * inv[\"item_factor\"]\n", "inv[\"open_sto_storage\"] = inv[\"open_sto_qty\"] * inv[\"item_factor\"]"]}, {"cell_type": "code", "execution_count": null, "id": "6c14cafc-76ea-4875-8307-2fd07e00a5ac", "metadata": {}, "outputs": [], "source": ["inv[\"total_inv\"] = inv[\"quantity\"] + inv[\"open_sto_qty\"]\n", "inv[\"tot_storage\"] = inv[\"total_inv\"] * inv[\"item_factor\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c27f806f-4408-4b57-be56-3ff9ec587051", "metadata": {}, "outputs": [], "source": ["# inv[inv['outlet_id']==1024]"]}, {"cell_type": "code", "execution_count": null, "id": "e1316caa-b6ec-4cc3-a7b9-22632de5a4f5", "metadata": {"tags": []}, "outputs": [], "source": ["inv[inv[\"item_id\"].isin(excluded_item_list)].groupby(\"outlet_id\").agg(\n", "    {\"tot_storage\": \"sum\"}\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "7cbd40b8-531c-42aa-96c6-978da74634de", "metadata": {}, "outputs": [], "source": ["festive_list = pd.read_csv(\"festive_list.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "91fa2387-7311-4148-8444-3126fcfb217f", "metadata": {}, "outputs": [], "source": ["# tuple(storage_type.item_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "9e0961d4-ed42-42b8-b3b0-29124564d78b", "metadata": {}, "outputs": [], "source": ["festive_list = tuple(festive_list.item_id.unique())"]}, {"cell_type": "code", "execution_count": null, "id": "9d7e09c9-b422-41f8-864d-3e5379976a3f", "metadata": {}, "outputs": [], "source": ["excluded_item_list = festive_list"]}, {"cell_type": "code", "execution_count": null, "id": "30fe4909-8bd8-450c-a694-d8fd1d1a7dd2", "metadata": {}, "outputs": [], "source": ["inv[\"festive_flag\"] = np.where(\n", "    inv[\"item_id\"].isin(excluded_item_list), \"Festive\", \"BAU\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f6de01ef-d946-4796-8429-cec64c4d3ce2", "metadata": {}, "outputs": [], "source": ["inv.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "42ae4abc-b285-4dcd-a3a4-c0c05179b930", "metadata": {}, "outputs": [], "source": ["# open_stos = open_stos[open_stos['item_id'].isin(excluded_item_list)].merge(item_factor, how = 'left')"]}, {"cell_type": "code", "execution_count": null, "id": "50006c67-35be-4e65-88b3-d7e3fdb0700e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b0237c37-e1cc-47eb-af68-58864d3d2f59", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "17932d00-d370-45da-88f4-c291d6972fe9", "metadata": {}, "outputs": [], "source": ["# open_stos['storage'] = open_stos['open_sto_qty']*open_stos['item_factor']"]}, {"cell_type": "code", "execution_count": null, "id": "31046e74-3d96-4b1a-b886-26f875682e48", "metadata": {}, "outputs": [], "source": ["# open_stos['festive_flag'] = np.where(open_stos['item_id'].isin(excluded_item_list), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "dbe789ff-6b30-4f9c-a734-eb1134ecd05d", "metadata": {}, "outputs": [], "source": ["# open_stos.groupby(['outlet_id', 'festive_flag']).agg({'storage':'sum'}).reset_index().rename(columns = {'storage':'open_sto_storage'}).sort_values(by = 'open_sto_storage', ascending = False).head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "836dfd77-c71f-419d-9615-f7ef997b230c", "metadata": {}, "outputs": [], "source": ["# open_stos[open_stos['outlet_id']==2289].groupby('festive_flag').agg({'storage':'sum'})"]}, {"cell_type": "code", "execution_count": null, "id": "5cf65dfc-40a6-4b4a-b616-657d9e05b8c8", "metadata": {}, "outputs": [], "source": ["# open_stos['open_sto_tag'] = np.where(open_stos['festive_flag'] == 1, 'festive_sto', 'normal_sto')"]}, {"cell_type": "code", "execution_count": null, "id": "3a3eacd1-37a2-4ead-b319-add0c4964ca3", "metadata": {}, "outputs": [], "source": ["# # Creates a pivot table dataframe\n", "# table = pd.pivot_table(df, values ='A', index =['B', 'C'],\n", "# \t\t\t\t\t\tcolumns =['B'], aggfunc = np.sum)\n", "\n", "# table"]}, {"cell_type": "code", "execution_count": null, "id": "2cccb73b-8bd5-423b-a32e-7473469eff0a", "metadata": {}, "outputs": [], "source": ["# open_stos_pivot = pd.pivot_table(open_stos, values='storage', index='outlet_id', columns = ['open_sto_tag'], aggfunc = np.sum )"]}, {"cell_type": "code", "execution_count": null, "id": "4b8c2fd1-4e25-4cf0-9bc3-62e6e65a5fb0", "metadata": {}, "outputs": [], "source": ["# open_stos_pivot"]}, {"cell_type": "code", "execution_count": null, "id": "64d81ab7-bd43-4a26-a679-6fb5114d90e2", "metadata": {}, "outputs": [], "source": ["# open_stos = open_stos.groupby(['outlet_id']).agg({'storage':'sum'}).reset_index().rename(columns = {'storage':'open_sto_storage'})"]}, {"cell_type": "code", "execution_count": null, "id": "6ddb55b7-94fb-4871-9865-c9fc44a1ed7d", "metadata": {}, "outputs": [], "source": ["# open_stos.sort_values(by = 'open_sto_storage', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "3b858e4d-128f-4a40-b1fe-85743011e424", "metadata": {}, "outputs": [], "source": ["# def fetch_item_factor():\n", "#     sql = f'''\n", "#         select item_id, item_factor from metrics.item_factor_org\n", "#     '''\n", "#     con = pb.get_connection(\"[Warehouse] Redshift\")\n", "#     df = pd.read_sql_query(sql=sql, con=con)\n", "#     return df\n", "\n", "\n", "# item_factor = fetch_item_factor()\n", "# # outlet_df = outlet_df_raw.copy()\n", "# print(item_factor.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "90a37231-7a7e-4368-9f5d-ba9556b6e18c", "metadata": {}, "outputs": [], "source": ["# inv.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "97b25573-54cf-4c63-b929-76e06c76de2a", "metadata": {}, "outputs": [], "source": ["# item_factor.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "dc7c75d5-de18-4fde-a625-139da842b236", "metadata": {}, "outputs": [], "source": ["# inv = inv.merge(item_factor, how = 'left')"]}, {"cell_type": "code", "execution_count": null, "id": "5ba38765-cc10-4941-a020-a483519cb3b9", "metadata": {}, "outputs": [], "source": ["inv = inv.merge(outlet_list_df)"]}, {"cell_type": "code", "execution_count": null, "id": "100d7513-75a7-485e-bb24-c2693389f3e1", "metadata": {}, "outputs": [], "source": ["# inv['item_factor']  = inv['item_factor'].fillna(1)"]}, {"cell_type": "code", "execution_count": null, "id": "269ea37c-c4ec-4894-a89d-c71516848e5b", "metadata": {}, "outputs": [], "source": ["# inv['storage'] = inv['quantity']*inv['item_factor']\n", "# outlet_summary = inv[inv['item_id'].isin(excluded_item_list)].groupby(['facility_id', 'outlet_id', 'outlet_name']).agg({'quantity':'sum', 'storage':'sum'}).reset_index().sort_values(by='storage', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "5a061ee0-053e-45db-9f9e-9ef69130a863", "metadata": {}, "outputs": [], "source": ["inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ed3370e8-2987-486e-8f4d-fb39b9964964", "metadata": {}, "outputs": [], "source": ["# outlet_summary = inv[inv['item_id'].isin(excluded_item_list)].groupby(['facility_id', 'outlet_id', 'outlet_name', 'festive_flag']).agg({'quantity':'sum', 'opn_stos':'sum', 'tot_storage':'sum'}).reset_index().sort_values(by='storage', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "4f191289-4565-4905-aa4c-3fad375a0999", "metadata": {}, "outputs": [], "source": ["# outlet_summary.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "92aa4088-b414-4df3-a2d3-042fc8fd5cce", "metadata": {}, "outputs": [], "source": ["# item_agg = inv[inv['item_id'].isin(excluded_item_list)].groupby(['item_id']).agg({'quantity':'sum', 'storage':'sum'}).reset_index().sort_values(by='storage', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "ba444c57-7378-4fd9-afae-8852e56a59af", "metadata": {}, "outputs": [], "source": ["def fetch_item_details():\n", "    sql = f\"\"\"\n", "        select item_id, item_name, l0_category, l1_category, l2_category, p_type from metrics.item_Details\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "item_details = fetch_item_details()\n", "# outlet_df = outlet_df_raw.copy()\n", "print(item_details.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "0d6f0213-7ad6-4548-8c0c-4143fb041c27", "metadata": {}, "outputs": [], "source": ["# item_agg = item_agg.merge(item_details)"]}, {"cell_type": "code", "execution_count": null, "id": "990c785d-540b-448d-80b5-8f3478a6288e", "metadata": {}, "outputs": [], "source": ["# item_agg.columns"]}, {"cell_type": "code", "execution_count": null, "id": "6b7ca579-de53-447a-9a32-988c30f8cc19", "metadata": {}, "outputs": [], "source": ["# item_agg = item_agg[['item_id', 'item_name', 'l0_category',\n", "#        'l1_category', 'l2_category', 'p_type','quantity', 'storage']]"]}, {"cell_type": "code", "execution_count": null, "id": "114812a7-d555-4496-91e2-cb410b646e9f", "metadata": {}, "outputs": [], "source": ["# def fetch_halloween():\n", "#     sql = f'''\n", "#         SELECT distinct item_id\n", "#         FROM rpc.product_facility_master_assortment pfma\n", "#         INNER JOIN rpc.product_master_assortment_substate_reasons pmasr ON pmasr.id = pfma.substate_reason_id\n", "#         WHERE reason_text IN ('KARWA_CHAUTH',\n", "#                               'HALLOWEEN')\n", "#     '''\n", "#     con = pb.get_connection(\"[Warehouse] Trino\")\n", "#     df = pd.read_sql_query(sql=sql, con=con)\n", "#     return df\n", "\n", "# halloween = fetch_halloween()\n", "# # outlet_df = outlet_df_raw.copy()\n", "# print(halloween.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "77c2ba50-05c1-4c76-b231-982779ece084", "metadata": {}, "outputs": [], "source": ["# halloween_skus = halloween.item_id.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "de964ec9-e0bd-4bb9-8a5f-611bbf1e4667", "metadata": {}, "outputs": [], "source": ["# item_agg[item_agg.item_id.isin(halloween_skus)][['storage', 'quantity']].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "aa7079ed-ead3-4774-bd22-87bd3373b5e7", "metadata": {}, "outputs": [], "source": ["# item_agg[item_agg.item_id.isin(halloween_skus)].head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "bdcae668-8f0f-468e-9713-a3bd95a93189", "metadata": {}, "outputs": [], "source": ["def fetch_regular_storage():\n", "    sql = f\"\"\"\n", "             With fo as (\n", "                with\n", "                outlets as \n", "                    (select \n", "                        om.facility_id, pf.internal_facility_identifier as facility_name, wf.name as warehouse_name,\n", "                        om.outlet_id as hot_outlet_id, om.outlet_name as hot_outlet_name,\n", "                        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "                        rcl.name as city_name,\n", "                        bt.name as business_type_name,\n", "                        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "\n", "                            from po.physical_facility_outlet_mapping om\n", "\n", "                                join \n", "                                    retail.console_outlet rco on rco.id = om.outlet_id \n", "\n", "                                join\n", "                                    retail.console_business_type bt on bt.id = rco.business_type_id\n", "\n", "                                left join \n", "                                    po.physical_facility pf on pf.facility_id = om.facility_id\n", "\n", "                                left join \n", "                                    retail.warehouse_facility wf on wf.id = om.facility_id\n", "\n", "                                left join \n", "                                    (select distinct warehouse_id, cloud_store_id \n", "                                        from retail.warehouse_outlet_mapping where active = 1\n", "                                    ) wom on wom.warehouse_id = om.outlet_id\n", "\n", "                                left join \n", "                                    retail.console_location rcl on rcl.id = rco.tax_location_id\n", "\n", "                                    where rco.business_type_id in (1,12,7,19,20,21)\n", "                                        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                                        and om.outlet_name not like '%%SSC%%'\n", "                                        and om.outlet_name not like '%%MODI%%'\n", "                                        and om.outlet_name not like '%%hot ff%%'\n", "                                        and om.lake_active_record\n", "                                        and rcl.lake_active_record\n", "                                        and wf.lake_active_record\n", "\n", "                                        order by (case when rco.business_type_id in (1,12) then 1\n", "                                            when rco.business_type_id in (19) then 2\n", "                                            when rco.business_type_id in (20) then 3 else 4 end), om.facility_id\n", "                    )\n", "\n", "                        select \n", "                            facility_id,\n", "                            hot_outlet_name as outlet_name,\n", "                            inv_outlet_id as outlet_id,\n", "                            taggings\n", "                        from outlets\n", "                        where taggings = 'fe'),\n", "\n", "                inv as (\n", "                select \n", "                    fo.outlet_id,\n", "                    inv.facility_id,\n", "                    storage_type,\n", "                    base_storage_type,\n", "                    inventory_quantity,\n", "                    store_quantity,\n", "                    orders_consume_quantity,\n", "                    in_transit_quantity,\n", "                    adjusted_inventory_quantity,\n", "                    adjusted_store_quantity,\n", "                    adjusted_orders_consume_quantity,\n", "                    adjusted_in_transit_quantity\n", "                from\n", "                    ars.physical_facility_inventory_quantity inv\n", "                inner join\n", "                    fo\n", "                on\n", "                    fo.facility_id = inv.facility_id\n", "                and\n", "                    inv.lake_active_record),\n", "\n", "                storage as (\n", "                    select \n", "                        distinct\n", "                        pfsc.facility_id,\n", "                        pfsc.facility_name,\n", "                        pfsc.storage_type,\n", "                        pfsc.base_storage_type,\n", "                        pfsc.threshold,\n", "                        pfsc.storage_capacity as storage_cap\n", "                    from\n", "                        lake_ars.physical_facility_storage_capacity pfsc\n", "                    inner join\n", "                        (\n", "                            select \n", "                                distinct\n", "                                facility_id,\n", "                                storage_type,\n", "                                base_storage_type,\n", "                                max(updated_at) as updated_at_\n", "                            from \n", "                                lake_ars.physical_facility_storage_capacity \n", "                            group by 1,2,3\n", "                        ) sb2 on sb2.facility_id = pfsc.facility_id\n", "                                and sb2.storage_type = pfsc.storage_type\n", "                                and sb2.base_storage_type = pfsc.base_storage_type\n", "                                and sb2.updated_at_ = pfsc.updated_at\n", "                    where active \n", "                    and pfsc.lake_active_record\n", "                ),\n", "\n", "                inv_agg as (\n", "                        select\n", "                            inv.*,\n", "                            s.facility_name,\n", "                            case when s.storage_type is null then inv.base_storage_type\n", "                                else s.storage_type end as final_storage_type\n", "                        from\n", "                            inv\n", "                        inner join\n", "                            fo\n", "                        on\n", "                            fo.facility_id = inv.facility_id\n", "                        left join\n", "                            storage s\n", "                        on\n", "                            s.facility_id = inv.facility_id\n", "                        and\n", "                            s.storage_type = inv.storage_type\n", "                )\n", "                ,\n", "\n", "                final_agg as (\n", "                    select\n", "                        ia.outlet_id,\n", "                        facility_id,\n", "                        final_storage_type,\n", "                        max(ia.facility_name) as facility_name,\n", "                        sum(inventory_quantity) as inv_qty,\n", "                        sum(store_quantity) as store_qty,\n", "                        sum(orders_consume_quantity) as ocq,\n", "                        sum(in_transit_quantity) as itq,\n", "                        sum(adjusted_inventory_quantity) as adj_inv_qty,\n", "                        sum(adjusted_store_quantity) as adj_store_qty,\n", "                        sum(adjusted_orders_consume_quantity) as adj_ocq,\n", "                        sum(adjusted_in_transit_quantity) as adj_itq\n", "                    from\n", "                        inv_agg ia\n", "                    -- where\n", "                    --     facility_id = 1632\n", "                    group by\n", "                        1,2,3\n", "\n", "                )\n", "\n", "            -- reg_agg as (\n", "                select\n", "                    fa.outlet_id,\n", "                    fa.facility_id,\n", "                    fa.facility_name,\n", "                    fa.final_storage_type,\n", "                    s.threshold,\n", "                    CAST(s.storage_cap as REAL) as storage_cap,\n", "                    CAST(fa.store_qty AS REAL) as onshelf_inventory,\n", "                    CAST(fa.itq AS REAL) as in_transit_quantity,\n", "                    CAST(fa.adj_store_qty AS REAL) as scaled_onshelf_inventory,\n", "                    CAST(fa.adj_itq AS REAL) as scaled_open_po_qty,\n", "                    100.0*CAST(fa.adj_store_qty AS REAL)/CAST(s.storage_cap as REAL) as onshelf_utilisation,\n", "                    100.0*(CAST(fa.adj_store_qty AS REAL)+CAST(fa.adj_itq AS REAL) )/CAST(s.storage_cap as REAL) as utilisation_with_open_stos\n", "                from\n", "                    storage s\n", "                inner join\n", "                    final_agg fa \n", "                on\n", "                    fa.facility_id = s.facility_id\n", "                and\n", "                    fa.final_storage_type = s.storage_type\n", "                where\n", "                    s.storage_cap>0\n", "                and\n", "                    storage_type in ('REGULAR')\n", "                order by \n", "                   facility_name\n", "                --   )\n", "                   \n", "                -- select\n", "                --     outlet_id,\n", "                --     facility_id,\n", "                --     facility_name,\n", "                --     sum(CAST(storage_cap as REAL)) as storage_cap,\n", "                --     sum(CAST(onshelf_inventory AS REAL)) as onshelf_inventory,\n", "                --     sum(CAST(in_transit_quantity AS REAL)) as in_transit_quantity,\n", "                --     sum(CAST(scaled_onshelf_inventory AS REAL)) as scaled_onshelf_inventory,\n", "                --     sum(CAST(scaled_open_po_qty AS REAL)) as scaled_open_po_qty\n", "                -- from\n", "                --     reg_agg\n", "                -- where\n", "                --     final_storage_type like ('%%REGULAR%%')\n", "                -- GROUP BY 1,2,3\n", "                    \n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Trino\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "regular_storage = fetch_regular_storage()\n", "# outlet_df = outlet_df_raw.copy()\n", "print(regular_storage.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "48e2162f-2834-40f8-a215-12018168f973", "metadata": {}, "outputs": [], "source": ["regular_storage.head()"]}, {"cell_type": "code", "execution_count": null, "id": "93496802-da09-4fea-932b-ddac3c2c48d6", "metadata": {}, "outputs": [], "source": ["inv.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "fd198a8b-17aa-4a4d-871c-42f17cdf02ec", "metadata": {}, "outputs": [], "source": ["inv[inv[\"outlet_id\"] == 2578].groupby(\"festive_flag\").agg({\"tot_storage\": \"sum\"})"]}, {"cell_type": "code", "execution_count": null, "id": "709cdf4e-be7b-4b90-aa53-2079a40a8568", "metadata": {}, "outputs": [], "source": ["bau_df = (\n", "    inv[inv[\"festive_flag\"] == \"BAU\"]\n", "    .groupby([\"outlet_id\", \"facility_id\"])\n", "    .agg(\n", "        {\n", "            \"open_sto_qty\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "            \"open_sto_storage\": \"sum\",\n", "            \"on_hand_storage\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"open_sto_qty\": \"bau_open_sto_qty\",\n", "            \"quantity\": \"bau_qty\",\n", "            \"open_sto_storage\": \"bau_sto_storage\",\n", "            \"on_hand_storage\": \"bau_storage\",\n", "        }\n", "    )\n", ")\n", "festive_df = (\n", "    inv[inv[\"festive_flag\"] == \"Festive\"]\n", "    .groupby([\"outlet_id\", \"facility_id\"])\n", "    .agg(\n", "        {\n", "            \"open_sto_qty\": \"sum\",\n", "            \"quantity\": \"sum\",\n", "            \"open_sto_storage\": \"sum\",\n", "            \"on_hand_storage\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"open_sto_qty\": \"festive_open_sto_qty\",\n", "            \"quantity\": \"festive_qty\",\n", "            \"open_sto_storage\": \"festive_sto_storage\",\n", "            \"on_hand_storage\": \"festive_storage\",\n", "        }\n", "    )\n", ")\n", "# festive_df = inv[inv['festive_flag']=='Festive'].groupby(['outlet_id', 'facility_id']).agg({'tot_storage':'sum', 'tot_inv':'sum'}).reset_index().rename(columns = {'tot_storage':'festive_storage', 'tot_inv':'festive_qty'})"]}, {"cell_type": "code", "execution_count": null, "id": "f1c13eaf-a0f7-463e-9d52-7d99efa8dc0c", "metadata": {}, "outputs": [], "source": ["outlet_list_df = outlet_list_df.merge(bau_df)\n", "outlet_list_df = outlet_list_df.merge(festive_df)"]}, {"cell_type": "code", "execution_count": null, "id": "3d395427-9ce4-489a-8177-4cdf3cd088d4", "metadata": {}, "outputs": [], "source": ["outlet_list_df[outlet_list_df[\"outlet_id\"] == 2820]"]}, {"cell_type": "code", "execution_count": null, "id": "0aba1e80-458f-4510-b042-539c4839c0e2", "metadata": {}, "outputs": [], "source": ["regular_storage.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "95cf030f-c08c-4f21-8393-548a9411b548", "metadata": {}, "outputs": [], "source": ["regular_storage.columns"]}, {"cell_type": "code", "execution_count": null, "id": "64770855-b633-4fc0-b035-ac8391325e85", "metadata": {}, "outputs": [], "source": ["outlet_list_df = outlet_list_df.merge(\n", "    regular_storage[[\"outlet_id\", \"facility_id\", \"storage_cap\"]], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ad79a3dd-2053-4f9d-b3b5-b3bb145f9d11", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"osh_util\"] = (\n", "    100.0\n", "    * (outlet_list_df[\"festive_storage\"] + outlet_list_df[\"bau_storage\"])\n", "    / outlet_list_df[\"storage_cap\"]\n", ")\n", "outlet_list_df[\"total_util\"] = (\n", "    100.0\n", "    * (\n", "        outlet_list_df[\"festive_storage\"]\n", "        + outlet_list_df[\"bau_storage\"]\n", "        + outlet_list_df[\"festive_sto_storage\"]\n", "        + outlet_list_df[\"festive_sto_storage\"]\n", "    )\n", "    / outlet_list_df[\"storage_cap\"]\n", ")\n", "\n", "# outlet_list_df['total_util'] =100.0*(outlet_list_df['bau_storage']+outlet_list_df['festive_storage'])/outlet_list_df['storage_cap']"]}, {"cell_type": "code", "execution_count": null, "id": "660f5920-0899-4575-b1a3-8c89c55fa01c", "metadata": {}, "outputs": [], "source": ["outlet_list_df[outlet_list_df[\"total_util\"] > 120].sort_values(\n", "    by=\"total_util\", ascending=False\n", ").head(30)"]}, {"cell_type": "code", "execution_count": null, "id": "ed1ad4a8-92b6-42cc-92f3-7ac9c104ce14", "metadata": {}, "outputs": [], "source": ["# outlet_list_df['critical_skus_space'] = 0.3*outlet_list_df['storage_cap']"]}, {"cell_type": "code", "execution_count": null, "id": "b431f586-52a1-4068-984d-51b59030a756", "metadata": {}, "outputs": [], "source": ["# outlet_list_df['festive_util'] = 100*outlet_list_df['festive_storage']/outlet_list_df['critical_skus_space']"]}, {"cell_type": "code", "execution_count": null, "id": "3cd245f2-e62f-4d47-8503-d2201a85bf88", "metadata": {}, "outputs": [], "source": ["# outlet_list_df['festive_util'] = 200*outlet_list_df['festive_storage']/outlet_list_df['storage_cap']"]}, {"cell_type": "code", "execution_count": null, "id": "9af933c7-5ae3-4bd1-be5f-a38eb7e26027", "metadata": {}, "outputs": [], "source": ["outlet_list_df[outlet_list_df[\"total_util\"] > 110].sort_values(\n", "    by=\"total_util\", ascending=False\n", ").shape"]}, {"cell_type": "code", "execution_count": null, "id": "e1200f4e-a80d-49ff-bea3-43294fd75a4a", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\n", "    (outlet_list_df[\"total_util\"] > 110) & (outlet_list_df[\"storage_cap\"] < 25000)\n", "].sort_values(by=\"total_util\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d4cbe62b-400d-4381-9760-d35a0dddd539", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[(outlet_list_df['storage_cap']<25000)].sort_values(by = 'total_util', ascending = False).head(30)"]}, {"cell_type": "code", "execution_count": null, "id": "623b3c27-3d77-493e-8bf6-110b369a27bf", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[(outlet_list_df['total_util']>100)]"]}, {"cell_type": "code", "execution_count": null, "id": "f049890d-a207-4be5-92e0-1a45d0d66c80", "metadata": {}, "outputs": [], "source": ["floor_area_df = pd.read_csv(\"carpet_area.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "a0fe44c5-6be5-4e86-b840-9e0a5908aebc", "metadata": {}, "outputs": [], "source": ["floor_area_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "e887a694-cd33-4449-be45-8c533ad1fb6f", "metadata": {}, "outputs": [], "source": ["# floor_area_df = floor_area_df.astype({'carpet_area':int})"]}, {"cell_type": "code", "execution_count": null, "id": "68893cfb-960b-4f94-a997-0626253276b0", "metadata": {}, "outputs": [], "source": ["floor_area_df = floor_area_df.rename(\n", "    columns={\"Outlet ID\": \"outlet_id\", \"Carpet area (sq ft)\": \"carpet_area\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b8163d1b-b920-42c7-a862-a6b19a9dddb0", "metadata": {}, "outputs": [], "source": ["floor_area_df = floor_area_df[[\"outlet_id\", \"carpet_area\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "52490235-0ebb-4a4c-9f1a-fe428ec45e2d", "metadata": {}, "outputs": [], "source": ["default_area = np.round(floor_area_df[floor_area_df[\"carpet_area\"].notna()].mean()[0])"]}, {"cell_type": "code", "execution_count": null, "id": "a4f8f2cc-c5d3-41aa-bfad-cb56fed6cb0a", "metadata": {}, "outputs": [], "source": ["default_area"]}, {"cell_type": "code", "execution_count": null, "id": "7750c800-d64c-43cf-9bc9-6adc6a95ac6d", "metadata": {}, "outputs": [], "source": ["floor_area_df[\"carpet_area\"] = floor_area_df[\"carpet_area\"].fillna(default_area)"]}, {"cell_type": "code", "execution_count": null, "id": "13496e4a-70a9-4ec6-8605-5e4c617c31a0", "metadata": {}, "outputs": [], "source": ["outlet_list_df = outlet_list_df.merge(floor_area_df, how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "5424f43d-ceda-40ba-bf9b-0d30257e17cf", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"carpet_area\"] = outlet_list_df[\"carpet_area\"].fillna(default_area)"]}, {"cell_type": "code", "execution_count": null, "id": "bdc0d1c0-0d11-4629-b547-07b2d0ec2de1", "metadata": {}, "outputs": [], "source": ["outlet_list_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "84f7a47f-7660-42dd-978e-d971af382efb", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[outlet_list_df['carpet_area']=='NaN']"]}, {"cell_type": "code", "execution_count": null, "id": "59731241-73e4-4ceb-86b9-a45799f1ac7c", "metadata": {}, "outputs": [], "source": ["### Define storage bins"]}, {"cell_type": "code", "execution_count": null, "id": "762aba1e-003d-42e0-802e-bbc227a11416", "metadata": {}, "outputs": [], "source": ["### Define Carpet area bins"]}, {"cell_type": "code", "execution_count": null, "id": "8b8c2ab0-f3fe-435d-85d9-fa14edaa8f49", "metadata": {}, "outputs": [], "source": ["outlet_list_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "66855ffe-9cc2-4776-9fae-33357a6aab20", "metadata": {}, "outputs": [], "source": ["# outlet_list_df = outlet_list_df.astype({'carpet_area':float})"]}, {"cell_type": "code", "execution_count": null, "id": "4719bb06-93ae-447e-b66a-daba661672f7", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"storage_quantile\"] = pd.qcut(\n", "    outlet_list_df[\"storage_cap\"],\n", "    q=3,\n", "    labels=[\"low_storage\", \"med_storage\", \"high_storage\"],\n", ")\n", "outlet_list_df[\"carpet_area_quantile\"] = pd.qcut(\n", "    outlet_list_df[\"carpet_area\"], q=3, labels=[\"low_area\", \"med_area\", \"high_area\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4f12b161-7169-41a2-ad5b-02f78c4b6b11", "metadata": {}, "outputs": [], "source": ["outlet_list_df"]}, {"cell_type": "code", "execution_count": null, "id": "af655dd5-4904-4023-ab4f-4b0a7efec0aa", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.groupby(['carpet_area_quantile', 'storage_quantile']).agg({'storage_cap':'mean', 'carpet_area':'mean', 'outlet_id':'count', 'total_util':'mean', 'osh_util':'mean'}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "e8a58452-bf9d-4e9b-97f9-93156e668fa1", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"buffer\"] = 0.3"]}, {"cell_type": "code", "execution_count": null, "id": "57dd6013-329d-4644-a492-1a2ef551ca06", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"low_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"low_storage\"),\n", "    0.2,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"low_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"med_storage\"),\n", "    0.15,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"low_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"high_storage\"),\n", "    0.10,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"med_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"low_storage\"),\n", "    0.25,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"med_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"med_storage\"),\n", "    0.20,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"med_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"high_storage\"),\n", "    0.15,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"high_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"low_storage\"),\n", "    0.3,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"high_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"med_storage\"),\n", "    0.25,\n", "    outlet_list_df[\"buffer\"],\n", ")\n", "outlet_list_df[\"buffer\"] = np.where(\n", "    (outlet_list_df[\"carpet_area_quantile\"] == \"high_area\")\n", "    & (outlet_list_df[\"storage_quantile\"] == \"high_storage\"),\n", "    0.2,\n", "    outlet_list_df[\"buffer\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1571ef0d-2c3f-42d2-a867-5702e911efee", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"final_storage\"] = outlet_list_df[\"storage_cap\"] * (\n", "    1 + outlet_list_df[\"buffer\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b0365a4c-f1ad-4f51-b121-74e08b32de02", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"final_util\"] = (\n", "    100.0\n", "    * (outlet_list_df[\"bau_storage\"] + outlet_list_df[\"festive_storage\"])\n", "    / outlet_list_df[\"final_storage\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6a17e293-7075-4f07-9224-be7264aa983f", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.sort_values(by = 'final_util', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "f0e69e17-9e6e-4d2a-9e95-c982becee296", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.sort_values(by = 'total_util', ascending = False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "8424aaac-22b6-462a-89ad-baab5749fcf3", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[outlet_list_df['total_util']>100]"]}, {"cell_type": "code", "execution_count": null, "id": "2f3f56d1-46e0-4de1-a70d-0dd5c1fe3c78", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[outlet_list_df['total_util']>90]"]}, {"cell_type": "code", "execution_count": null, "id": "6beceb4e-5fbb-4e9a-8013-21e9720d4a8a", "metadata": {}, "outputs": [], "source": ["scaled_onshelf = regular_storage[[\"outlet_id\", \"scaled_onshelf_inventory\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "a955a1aa-c2bf-4cdd-a4ac-e0f1776f4325", "metadata": {}, "outputs": [], "source": ["outlet_list_df = outlet_list_df.merge(scaled_onshelf)"]}, {"cell_type": "code", "execution_count": null, "id": "c67f7d04-2343-4603-8668-07051e101865", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[outlet_list_df['final_util']>100].sort_values(by = 'final_util', ascending = False)"]}, {"cell_type": "code", "execution_count": null, "id": "3e406441-e3b3-407f-8024-cd22a5b55f78", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"space_avail\"] = (1 + outlet_list_df[\"buffer\"]) * 100 - outlet_list_df[\n", "    \"final_util\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "04a45cd9-3709-49fd-af00-fe19d1d4db9a", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.sort_values(by = 'space_avail').head(40)"]}, {"cell_type": "code", "execution_count": null, "id": "1d5b57f9-69c0-4c28-9930-0a0d1c8f6145", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.sort_values(by = 'total_util').head(230)"]}, {"cell_type": "code", "execution_count": null, "id": "56fa234e-89c8-4495-8c51-edbc5c94da0a", "metadata": {}, "outputs": [], "source": ["outlet_list_df[\"diff\"] = (\n", "    outlet_list_df[\"scaled_onshelf_inventory\"] - outlet_list_df[\"bau_storage\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1e9fd13e-09a6-4bf2-9cd0-0c6aca55e899", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.sort_values(by = 'diff', ascending = False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "6181621b-aa07-42bd-b568-398dca8f0942", "metadata": {}, "outputs": [], "source": ["outlet_list_df.sort_values(by=\"total_util\", ascending=False).to_csv(\"util.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "a66cc8db-4728-4e86-8fe9-be9730e9f85c", "metadata": {}, "outputs": [], "source": ["outlet_list_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "c50b3343-3bd2-4057-a755-58748b09688a", "metadata": {}, "outputs": [], "source": ["outlet_list_df = outlet_list_df[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"city_id\",\n", "        \"outlet_name\",\n", "        \"bau_open_sto_qty\",\n", "        \"bau_qty\",\n", "        \"bau_sto_storage\",\n", "        \"bau_storage\",\n", "        \"festive_open_sto_qty\",\n", "        \"festive_qty\",\n", "        \"festive_sto_storage\",\n", "        \"festive_storage\",\n", "        \"storage_cap\",\n", "        \"osh_util\",\n", "        \"total_util\",\n", "        \"carpet_area\",\n", "        \"storage_quantile\",\n", "        \"carpet_area_quantile\",\n", "        \"buffer\",\n", "        \"final_storage\",\n", "        \"final_util\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "6b95cf29-da03-420e-957f-46aa72ed184e", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "418a7b21-635b-4ecb-bbdf-e3000eefee3b", "metadata": {}, "outputs": [], "source": ["outlet_list_df.to_csv(\"festive_storage.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "2aeb115e-0efb-4687-aaea-629151aa315a", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[outlet_list_df['outlet_id']==2450]"]}, {"cell_type": "code", "execution_count": null, "id": "b03564d9-66c4-48a3-881c-253fbb84e668", "metadata": {}, "outputs": [], "source": ["# outlet_list_df[outlet_list_df['total_util']>100].head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b11591ec-a5bf-481d-b57c-e7d7b3660c9b", "metadata": {}, "outputs": [], "source": ["# outlet_list_df.sort_values(by ='total_util', ascending = False).head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7ab576f4-4632-47e8-9d23-8a2c76124f09", "metadata": {}, "outputs": [], "source": ["outlet_list_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "a0f323a8-b8f7-4c9c-ac60-7c145e0b0adc", "metadata": {}, "outputs": [], "source": ["outlet_list_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "907515e7-eafe-465f-b7cd-b44d8cc81729", "metadata": {}, "outputs": [], "source": ["data = outlet_list_df[\n", "    [\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"bau_open_sto_qty\",\n", "        \"bau_qty\",\n", "        \"bau_sto_storage\",\n", "        \"bau_storage\",\n", "        \"festive_open_sto_qty\",\n", "        \"festive_qty\",\n", "        \"festive_sto_storage\",\n", "        \"festive_storage\",\n", "        \"storage_cap\",\n", "        \"osh_util\",\n", "        \"total_util\",\n", "        \"carpet_area\",\n", "        \"storage_quantile\",\n", "        \"carpet_area_quantile\",\n", "        \"buffer\",\n", "        \"final_storage\",\n", "        \"final_util\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "f84ba5d7-4bf5-414c-9c2d-3ff8fb1e322f", "metadata": {}, "outputs": [], "source": ["data = data.astype(\n", "    {\n", "        \"bau_open_sto_qty\": int,\n", "        \"bau_qty\": int,\n", "        \"festive_open_sto_qty\": int,\n", "        \"festive_qty\": int,\n", "        \"festive_sto_storage\": float,\n", "        \"festive_storage\": float,\n", "        \"storage_cap\": int,\n", "        \"storage_quantile\": str,\n", "        \"carpet_area_quantile\": str,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "908f3519-0c70-42ea-88f2-75d5415a8cf3", "metadata": {}, "outputs": [], "source": ["outlet_list_df = outlet_list_df  ##Uploading item_factors in temp table (46 store, with one value for each item-outlet combination)\n", "\n", "\n", "def upload_storage_original(data_temp):\n", "    data = data_temp.copy()\n", "    from datetime import datetime, timedelta\n", "\n", "    data[\"updated_at\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "    kwargs_log = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"storage_consumption_org\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility_id\"},\n", "            {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet_id\"},\n", "            {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"outlet_name\"},\n", "            {\n", "                \"name\": \"bau_open_sto_qty\",\n", "                \"type\": \"int\",\n", "                \"description\": \"bau_open_sto_qty\",\n", "            },\n", "            {\"name\": \"bau_qty\", \"type\": \"int\", \"description\": \"bau_qty\"},\n", "            {\n", "                \"name\": \"bau_sto_storage\",\n", "                \"type\": \"float\",\n", "                \"description\": \"bau_sto_storage\",\n", "            },\n", "            {\"name\": \"bau_storage\", \"type\": \"float\", \"description\": \"bau_storage\"},\n", "            {\n", "                \"name\": \"festive_open_sto_qty\",\n", "                \"type\": \"int\",\n", "                \"description\": \"festive_open_sto_qty\",\n", "            },\n", "            {\"name\": \"festive_qty\", \"type\": \"int\", \"description\": \"festive_qty\"},\n", "            {\n", "                \"name\": \"festive_sto_storage\",\n", "                \"type\": \"float\",\n", "                \"description\": \"festive_sto_storage\",\n", "            },\n", "            {\n", "                \"name\": \"festive_storage\",\n", "                \"type\": \"float\",\n", "                \"description\": \"festive_storage\",\n", "            },\n", "            {\"name\": \"storage_cap\", \"type\": \"int\", \"description\": \"festive_storage\"},\n", "            {\"name\": \"osh_util\", \"type\": \"float\", \"description\": \"osh_util\"},\n", "            {\"name\": \"total_util\", \"type\": \"float\", \"description\": \"total_util\"},\n", "            {\"name\": \"carpet_area\", \"type\": \"float\", \"description\": \"carpet_area\"},\n", "            {\n", "                \"name\": \"storage_quantile\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"storage_quantile\",\n", "            },\n", "            {\n", "                \"name\": \"carpet_area_quantile\",\n", "                \"type\": \"varchar\",\n", "                \"description\": \"carpet_area_quantile\",\n", "            },\n", "            {\"name\": \"buffer\", \"type\": \"float\", \"description\": \"buffer\"},\n", "            {\"name\": \"final_storage\", \"type\": \"float\", \"description\": \"final_storage\"},\n", "            {\"name\": \"final_util\", \"type\": \"float\", \"description\": \"final_util\"},\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"Updated timestamp\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"facility_id\"],\n", "        \"sortkey\": [\"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores regular and heavy item factor as per the new tata item factor\",\n", "    }\n", "\n", "    pb.to_redshift(data, **kwargs_log)"]}, {"cell_type": "code", "execution_count": null, "id": "3424c689-2a61-4406-8dcb-d176e06ed091", "metadata": {}, "outputs": [], "source": ["upload_storage_original(data)"]}, {"cell_type": "code", "execution_count": null, "id": "e9757e0f-3bc3-4da0-8437-4f01376cfcc1", "metadata": {}, "outputs": [], "source": ["# ##Uploading item_factors in temp table (46 store, with one value for each item-outlet combination)\n", "# def upload_storage_original(data_temp):\n", "#     data = data_temp.copy()\n", "#     from datetime import datetime, timedelta\n", "\n", "#     data[\"updated_at\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "#     kwargs_log = {\n", "#         \"schema_name\": \"metrics\",\n", "#         \"table_name\": \"storage_consumption_org\",\n", "#         \"column_dtypes\": [\n", "#             {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "#             {\"name\": \"volume_factor\", \"type\": \"float\", \"description\": \"volume_factor\"},\n", "#             {\"name\": \"weight_factor\", \"type\": \"float\", \"description\": \"weight_factor\"},\n", "#             {\"name\": \"item_factor\", \"type\": \"float\", \"description\": \"Item_factor\"},\n", "#             {\n", "#                 \"name\": \"updated_at\",\n", "#                 \"type\": \"timestamp\",\n", "#                 \"description\": \"Updated timestamp\",\n", "#             },\n", "#         ],\n", "#         \"primary_key\": [\"item_id\"],\n", "#         \"sortkey\": [\"updated_at\"],\n", "#         \"incremental_key\": \"updated_at\",\n", "#         \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "#         \"table_description\": \"Stores regular and heavy item factor as per the new tata item factor\",\n", "#     }\n", "\n", "#     pb.to_redshift(data, **kwargs_log)"]}, {"cell_type": "code", "execution_count": null, "id": "60c127bf-bd19-4b41-a920-9de293a419da", "metadata": {}, "outputs": [], "source": ["# ##Uploading item_factors in temp table (46 store, with one value for each item-outlet combination)\n", "# def upload_storage_original(data_temp):\n", "#     data = data_temp.copy()\n", "#     from datetime import datetime, timedelta\n", "\n", "#     data[\"updated_at\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "#     kwargs_log = {\n", "#         \"schema_name\": \"metrics\",\n", "#         \"table_name\": \"storage_consumption_org\",\n", "#         \"column_dtypes\": [\n", "#             {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "#             {\"name\": \"volume_factor\", \"type\": \"float\", \"description\": \"volume_factor\"},\n", "#             {\"name\": \"weight_factor\", \"type\": \"float\", \"description\": \"weight_factor\"},\n", "#             {\"name\": \"item_factor\", \"type\": \"float\", \"description\": \"Item_factor\"},\n", "#             {\n", "#                 \"name\": \"updated_at\",\n", "#                 \"type\": \"timestamp\",\n", "#                 \"description\": \"Updated timestamp\",\n", "#             },\n", "#         ],\n", "#         \"primary_key\": [\"item_id\"],\n", "#         \"sortkey\": [\"updated_at\"],\n", "#         \"incremental_key\": \"updated_at\",\n", "#         \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "#         \"table_description\": \"Stores regular and heavy item factor as per the new tata item factor\",\n", "#     }\n", "\n", "#     pb.to_redshift(data, **kwargs_log)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}