{"cells": [{"cell_type": "code", "execution_count": null, "id": "1a818e8b-123f-45bc-9383-750b2563f1d3", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "import boto3\n", "import io\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "import requests\n", "from requests.exceptions import HTTPError\n", "\n", "!pip install pandasql\n", "import pandasql as ps\n", "\n", "from tqdm.notebook import tqdm\n", "\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "# CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "\n", "CON_IMS = pb.get_connection(\"[Replica] RDS IMS\")\n", "CON_WAREHOUSE = pb.get_connection(\"[Replica] RDS Warehouse Location\")\n", "\n", "secrets = pb.get_secret(\"retail/po/db/po.db.credential\")\n", "host_name = secrets.get(\"host\")\n", "user_name = secrets.get(\"db_user\")\n", "password = secrets.get(\"db_password\")\n", "CON_PO = pymysql.connect(\n", "    host=host_name, user=user_name, password=password, autocommit=True, local_infile=1\n", ")\n", "\n", "rpc_secret = pb.get_secret(\"retail/noto-reports/mysql/rpc-rds-read\")\n", "host = rpc_secret.get(\"host\")\n", "username = rpc_secret.get(\"username\")\n", "password = rpc_secret.get(\"password\")\n", "CON_RPC = pymysql.connect(host=host, user=username, password=password)\n", "\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "794844f2-cfec-4c3d-9a6f-ca2ac9face74", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4177db45-55ac-495d-80d8-99a464860605", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "bucket_name = \"grofers-retail-test\"\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "s3c = boto3.client(\"s3\", aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)"]}, {"cell_type": "code", "execution_count": null, "id": "956aa4bf-acd1-4d46-aacb-d06e449300ea", "metadata": {"tags": []}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7bd3a0d3-01ba-494b-98f1-da8825dc5988", "metadata": {}, "outputs": [], "source": ["Tag = \"datapulls\"\n", "cwd = os.getcwd()\n", "\n", "# directories\n", "GLOBAL_BASE_DIR = cwd\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")\n", "\n", "for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "71cad15e-b2f3-4811-b84d-1fdbd502870b", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "21918efe-70f7-413e-8fc6-1a880f103960", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c249d929-ae34-4ffc-afcb-cbad500b55e2", "metadata": {"tags": []}, "source": ["## Import From S3 Bucket"]}, {"cell_type": "code", "execution_count": null, "id": "3be28dbd-5a6a-47f0-9993-b847ab594da8", "metadata": {}, "outputs": [], "source": ["B = \"god_dag\"\n", "ReportB = \"reports/\" + B\n", "print(ReportB)\n", "\n", "empty_df = []\n", "for my_bucket_object in bucket_obj.objects.filter(Prefix=ReportB):\n", "    empty_df.append(my_bucket_object.key)\n", "    # print(my_bucket_object.key)\n", "\n", "B_Name = empty_df[0]\n", "print(B_Name)\n", "\n", "\n", "obj = s3c.get_object(Bucket=bucket_name, Key=B_Name)\n", "FileB = pd.read_csv(io.BytesIO(obj[\"Body\"].read()), encoding=\"utf8\")"]}, {"cell_type": "code", "execution_count": null, "id": "58e347a2-fd8d-4b73-8cca-957c847ee47a", "metadata": {}, "outputs": [], "source": ["exec(FileB.iloc[:, 0][0])"]}, {"cell_type": "code", "execution_count": null, "id": "ab5a1450-ace1-4bb0-b4dd-061b51a3be9b", "metadata": {}, "outputs": [], "source": ["print(\"end\")"]}, {"cell_type": "code", "execution_count": null, "id": "fa59efba-bc9e-4e26-88cc-f34d985b9a89", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3211c2bc-0640-488a-8704-b9adac3eabfc", "metadata": {}, "outputs": [], "source": ["df = execute_func()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}