alert_configs:
  slack:
  - channel: bl-data-inventory-pvt
dag_name: god_dag
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
  priority_weight: 5
  retries: 2
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/everything_and_anything/etl/god_dag
paused: false
pool: povms_pool
project_name: everything_and_anything
schedule:
  end_date: '2025-08-01T00:00:00'
  interval: 0 0 1 10 *
  start_date: '2023-10-23T00:00:00'
schedule_type: fixed
sla: 119 minutes
support_files: []
tags: []
template_name: notebook
version: 3
