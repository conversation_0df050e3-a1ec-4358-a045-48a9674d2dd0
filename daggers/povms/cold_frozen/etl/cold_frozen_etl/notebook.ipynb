{"cells": [{"cell_type": "code", "execution_count": null, "id": "0929aff6-6734-420c-a45b-f12d8b9cdd45", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "966ade78-990f-43f5-abd1-12f0bc91bc9a", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "code", "execution_count": null, "id": "14963271-a140-407e-9e2d-8039d44ffc8c", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "current_hour = pd.to_datetime(current_time).hour\n", "\n", "today_date = pd.to_datetime(current_time).strftime(\"%Y-%m-%d\")\n", "l1_date = pd.to_datetime(current_time - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "l7_date = pd.to_datetime(current_time - timedelta(days=7)).strftime(\"%Y-%m-%d\")\n", "\n", "today_date, l1_date, l7_date, current_hour"]}, {"cell_type": "markdown", "id": "a6faf532-0a9c-4e25-930d-54654ae328a8", "metadata": {"tags": []}, "source": ["## Cold & Frozen Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "a7eeabaa-ab57-483d-a5ba-7648b779654c", "metadata": {}, "outputs": [], "source": ["assortment_query = \"\"\"\n", "    SELECT DISTINCT item_id, l0, l1, l2, assortment_type, storage_type\n", "    FROM (\n", "        SELECT DISTINCT item_id, item_name, l0, l1, l2,\n", "        CASE\n", "            WHEN l0_id IN (1487) AND item_name NOT ILIKE ('%%frozen%%') THEN 'FnV'\n", "            WHEN l2_id IN (116, 1961, 1185, 63, 1127, 1425, 198, 31, 1097, 197, 1956, 1395, 1367, 1369, 1389, 1778, 97, 1094, 1093, 1091, 138, 33, 949, 950, 1961) AND perishable = 1 THEN 'Perishable'\n", "            ELSE 'Packaged Goods'\n", "        END AS assortment_type,\n", "        CASE \n", "            WHEN storage_type IN ('2','6') THEN 'Cold'\n", "            WHEN storage_type IN ('3','7') THEN 'Frozen'\n", "        END AS storage_type\n", "        FROM (\n", "            SELECT DISTINCT a.item_id, cd.name AS item_name, cd.l0_id, cd.l0, cd.l1_id, cd.l1, cd.l2_id, cd.l2, perishable, storage_type\n", "            FROM lake_rpc.product_product a\n", "            JOIN (\n", "                SELECT item_id, MAX(id) AS id\n", "                FROM lake_rpc.product_product\n", "                WHERE active = 1 AND approved = 1\n", "                GROUP BY 1\n", "            ) b ON a.item_id = b.item_id AND a.id = b.id\n", "            JOIN lake_rpc.item_category_details cd ON cd.item_id = a.item_id\n", "        )\n", "    )\n", "    WHERE assortment_type NOT IN ('FnV') AND storage_type IN ('Cold','Frozen')\n", "    \"\"\"\n", "assortment_df = pd.read_sql_query(assortment_query, redshift)\n", "assortment_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "1e782453-92ba-4c02-b06f-1348f12b1ea1", "metadata": {}, "outputs": [], "source": ["pg_item_list = tuple(\n", "    set(assortment_df[assortment_df[\"assortment_type\"] == \"Packaged Goods\"][\"item_id\"].to_list())\n", ")\n", "perishable_item_list = tuple(\n", "    set(assortment_df[assortment_df[\"assortment_type\"] == \"Perishable\"][\"item_id\"].to_list())\n", ")\n", "item_list = tuple(set(assortment_df[\"item_id\"].to_list()))\n", "\n", "len(pg_item_list), len(perishable_item_list), len(item_list)"]}, {"cell_type": "markdown", "id": "f6bb8d88-def6-4300-bb2d-0bfff2b53c3e", "metadata": {"tags": []}, "source": ["## Active Stores"]}, {"cell_type": "code", "execution_count": null, "id": "3e584f8b-1a1e-424a-8fb3-1995df2bd0b5", "metadata": {}, "outputs": [], "source": ["stores_query = \"\"\"\n", "    WITH outbound_base AS (\n", "        SELECT outlet AS outlet_id, COUNT(DISTINCT ancestor) AS carts \n", "        FROM lake_ims.ims_order_details od \n", "        WHERE status_id in (1,2) \n", "        AND od.created_at between current_date - 4 AND current_date - 1 \n", "        GROUP BY 1\n", "    )\n", "    SELECT facility_id, outlet_id\n", "    FROM lake_po.physical_facility_outlet_mapping pfom \n", "    WHERE  ars_active = 1 AND active = 1 AND is_primary = 1 \n", "    AND outlet_id IN (SELECT DISTINCT id FROM lake_retail.console_outlet WHERE business_type_id = 7 AND active = 1) \n", "    AND outlet_id IN (SELECT DISTINCT outlet_id FROM lake_po.bulk_facility_outlet_mapping WHERE active = 1) \n", "    AND outlet_id IN (SELECT DISTINCT outlet_id FROM outbound_base WHERE carts >= 5) \n", "    \"\"\"\n", "stores_df = pd.read_sql_query(stores_query, redshift)\n", "stores_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "414c0875-a1be-4bfc-aad0-cd422b4c9bea", "metadata": {}, "outputs": [], "source": ["facility_list = tuple(set(stores_df[\"facility_id\"].to_list()))\n", "outlet_list = tuple(set(stores_df[\"outlet_id\"].to_list()))\n", "len(facility_list), len(outlet_list)"]}, {"cell_type": "code", "execution_count": null, "id": "4704ba65-403a-417c-a10d-13cef9278ff8", "metadata": {}, "outputs": [], "source": ["fe_query = \"\"\"\n", "    SELECT DISTINCT cl.name AS city, facility_id, cf.name AS facility_name\n", "    FROM lake_retail.console_outlet co \n", "    LEFT JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "    LEFT JOIN lake_crates.facility cf ON cf.id = co.facility_id\n", "    WHERE co.active = 1 AND co.business_type_id = 7\n", "\"\"\"\n", "fe_df = pd.read_sql_query(fe_query, redshift)"]}, {"cell_type": "markdown", "id": "adb59e19-0d6a-4dea-88dc-4a4845db481c", "metadata": {"tags": []}, "source": ["## Availability Data"]}, {"cell_type": "code", "execution_count": null, "id": "1f0290dd-8cf4-45d2-bb3f-07a47008373f", "metadata": {}, "outputs": [], "source": ["availability_query = f\"\"\"\n", "    WITH rpc_pre AS (\n", "        SELECT facility_id, item_id, order_date, (actual_quantity - blocked_quantity) AS net_inv\n", "        FROM consumer.rpc_daily_availability\n", "        WHERE order_date >= current_date - 7\n", "    ),\n", "    \n", "    pg_availability AS (\n", "        SELECT facility_id, item_id, date_, SUM(is_available) AS avail_hours, COUNT(hour_) AS total_hours\n", "        FROM (\n", "            SELECT DISTINCT p.facility_id, p.item_id, DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, \n", "            CASE WHEN net_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM rpc_pre p\n", "            WHERE facility_id IN {facility_list} AND item_id IN {pg_item_list}\n", "        )\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    perishable_availability AS (\n", "        SELECT facility_id, item_id, date_, SUM(is_available) AS avail_hours, COUNT(hour_) AS total_hours\n", "        FROM (\n", "            SELECT DATE(updated_at) AS date_, EXTRACT(hour FROM updated_at) AS hour_, facility_id, item_id, \n", "            CASE WHEN current_inv > 0 THEN 1 ELSE 0 END AS is_available\n", "            FROM metrics.perishable_hourly_details_v2\n", "            WHERE facility_id IN {facility_list} AND item_id IN {perishable_item_list} AND updated_at >= current_date - 7\n", "        )\n", "        WHERE hour_ BETWEEN 6 AND 23\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    agg_summary AS (\n", "        SELECT * FROM pg_availability\n", "        UNION\n", "        SELECT * FROM perishable_availability\n", "    )\n", "\n", "    SELECT * FROM agg_summary\n", "    \"\"\"\n", "availability_df = pd.read_sql_query(availability_query, redshift)\n", "availability_df.shape"]}, {"cell_type": "markdown", "id": "b7fdfe64-be3e-4a3e-a85c-eaad99543836", "metadata": {"tags": []}, "source": ["## Sales Data"]}, {"cell_type": "code", "execution_count": null, "id": "0ebb72d1-84ae-4b2f-83f9-6fa566790269", "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT DATE(od.created_at  + interval '5.5 Hours') AS date_, ancestor, outlet AS outlet_id, oi.item_id\n", "        FROM lake_ims.ims_order_details od\n", "        LEFT JOIN lake_ims.ims_order_items oi ON od.id = oi.order_details_id\n", "        WHERE status_id IN (1,2,4)\n", "        AND od.created_at >= current_date - 7\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "\n", "    gmv_base AS (\n", "        SELECT CAST(ancestor AS varchar) AS ancestor, item_id, AVG(f_ordered_qty) AS quantity\n", "        FROM (\n", "            SELECT oi.order_id AS ancestor, oi.product_id, pl.item_id, (oi.selling_price*1.000/pl.multiplier) AS item_selling_price, \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS f_ordered_qty,\n", "            (oi.selling_price*1.000/pl.multiplier) * ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS GMV\n", "            FROM lake_oms_bifrost.oms_order_item oi\n", "            LEFT JOIN (\n", "                SELECT DISTINCT ipr.product_id, \n", "                CASE \n", "                    WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "                    ELSE ipr.item_id \n", "                END AS item_id,\n", "                CASE \n", "                    WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1)\n", "                    ELSE COALESCE(ipom_0.multiplier,1)\n", "                END AS multiplier \n", "                FROM lake_rpc.item_product_mapping ipr\n", "                LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipr.product_id = ipom.product_id AND ipr.item_id = ipom.item_id\n", "                LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipr.product_id = ipom_0.product_id\n", "            ) pl ON pl.product_id = oi.product_id\n", "            WHERE (oi.install_ts) >= current_date - 7\n", "            AND pl.item_id IS NOT NULL \n", "            AND ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) > 0\n", "        ) order_qty\n", "        WHERE ancestor IN (SELECT DISTINCT ancestor FROM base)\n", "        GROUP BY 1,2\n", "    ),\n", "    \n", "    sales AS (\n", "        SELECT b.date_, co.facility_id, b.item_id, SUM(quantity) AS sold_qty\n", "        FROM base b \n", "        LEFT JOIN gmv_base gb ON b.ancestor = gb.ancestor AND b.item_id = gb.item_id \n", "        LEFT JOIN lake_retail.console_outlet co ON co.id = b.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "        WHERE b.item_id IN {item_list} AND facility_id IN {facility_list}\n", "        GROUP BY 1,2,3\n", "    )\n", "    \n", "    SELECT * FROM sales\n", "    \"\"\"\n", "\n", "sales_df = pd.read_sql_query(sales_query, redshift)\n", "sales_df.shape"]}, {"cell_type": "markdown", "id": "d0199237-7efc-4411-9d56-3ba5d2c6b541", "metadata": {"tags": []}, "source": ["## Inventory Data"]}, {"cell_type": "code", "execution_count": null, "id": "8b168396-3072-481b-8bec-bc199bcaf8bd", "metadata": {}, "outputs": [], "source": ["inv_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT item_id, outlet_id, facility_id, actual_quantity - blocked_quantity AS net_inv \n", "        FROM (\n", "            SELECT iii.item_id, iii.outlet_id AS outlet_id, o.facility_id AS facility_id, iii.quantity AS actual_quantity, \n", "            (COALESCE(SUM(CASE WHEN iibi.blocked_type IN (1,2,5) THEN iibi.quantity ELSE 0 END),0)) AS blocked_quantity \n", "            FROM lake_ims.ims_item_inventory iii \n", "            LEFT JOIN lake_ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id \n", "            JOIN lake_retail.console_outlet o ON iii.outlet_id = o.id\n", "            WHERE iii.item_id IN {item_list} AND iii.outlet_id IN {outlet_list} \n", "            AND iii.active = 1  \n", "            GROUP BY 1,2,3,4,iii.quantity\n", "        )\n", "    )\n", "    \n", "    SELECT facility_id, item_id, SUM(net_inv) AS current_inv \n", "    FROM base \n", "    GROUP BY 1,2\n", "    \"\"\"\n", "inv_df = pd.read_sql_query(inv_query, redshift)\n", "inv_df.shape"]}, {"cell_type": "markdown", "id": "2cc9bfa1-e93f-41a6-9d2d-5e7dcbbab346", "metadata": {"tags": []}, "source": ["### Inventory Health Calculations"]}, {"cell_type": "markdown", "id": "9ba6337f-35a1-4af3-9008-1297dcaff79f", "metadata": {"tags": []}, "source": ["#### Base"]}, {"cell_type": "code", "execution_count": null, "id": "778ac9d4-7e05-4d88-94ea-7bcfb6c8ed46", "metadata": {}, "outputs": [], "source": ["calc_df = pd.merge(availability_df, sales_df, on=[\"date_\", \"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "calc_df = calc_df[pd.to_datetime(calc_df[\"date_\"]) < pd.to_datetime(today_date)]\n", "calc_df[\"live_days\"] = np.where(calc_df[\"avail_hours\"] > 0, 1, 0)\n", "\n", "calc_df = (\n", "    calc_df.groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"sold_qty\": \"sum\", \"live_days\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "calc_df[\"cpd\"] = (calc_df[\"sold_qty\"] / calc_df[\"live_days\"]).round(2)\n", "\n", "calc_df = calc_df.merge(inv_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "calc_df[\"dflag_\"] = np.where((calc_df[\"cpd\"] == 0) & (calc_df[\"current_inv\"] > 0), 1, 0)\n", "calc_df[\"eflag_\"] = np.where(\n", "    (calc_df[\"cpd\"] > 0) & (calc_df[\"current_inv\"] >= 5 * calc_df[\"cpd\"]), 1, 0\n", ")\n", "\n", "calc_df[\"dinv\"] = np.where(calc_df[\"dflag_\"] == 1, calc_df[\"current_inv\"], 0)\n", "calc_df[\"einv\"] = np.where(\n", "    calc_df[\"eflag_\"] == 1, calc_df[\"current_inv\"] - (5 * calc_df[\"cpd\"]).round(0), 0\n", ")\n", "\n", "calc_df[\"date_\"] = pd.to_datetime(today_date)\n", "\n", "calc_df = pd.merge(calc_df, assortment_df, on=[\"item_id\"], how=\"left\")\n", "calc_df.shape"]}, {"cell_type": "markdown", "id": "f3fb1f06-854e-42b1-a805-cc73f2dbdb45", "metadata": {"tags": []}, "source": ["## Upload to Table"]}, {"cell_type": "code", "execution_count": null, "id": "0add82a3-27de-4c67-9133-bd400c89b53b", "metadata": {}, "outputs": [], "source": ["df = calc_df[[\"facility_id\", \"item_id\", \"cpd\", \"current_inv\", \"date_\"]].drop_duplicates()\n", "df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "11b9e345-57c8-4ff3-97ec-1d1492348353", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    {\"name\": \"cpd\", \"type\": \"float\", \"description\": \"l7 cpd\"},\n", "    {\"name\": \"current_inv\", \"type\": \"float\", \"description\": \"current inventory\"},\n", "    {\"name\": \"date_\", \"type\": \"datetime\", \"description\": \"date\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"date/time of run\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"cold_frozen_inventory_health\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"updated_at\", \"facility_id\", \"item_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains cold and frozen skus inventory health at item level\",\n", "}\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "markdown", "id": "1bb9d61c-1558-452a-8e89-71fbd40e2f4f", "metadata": {"tags": []}, "source": ["## Daily View"]}, {"cell_type": "markdown", "id": "8f6fa34a-3403-445c-b2b3-18661607f7a3", "metadata": {"tags": []}, "source": ["#### Inventory Health"]}, {"cell_type": "code", "execution_count": null, "id": "e73734df-5fb9-4ad1-ba8a-d6c671c3c4ed", "metadata": {}, "outputs": [], "source": ["calc_inv_df = pd.merge(calc_df, fe_df, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "17e4934e-1836-4d9f-baa6-d27de9e78407", "metadata": {}, "outputs": [], "source": ["#  --- Facility L1 Aggregated --- #\n", "l1_inv_agg = (\n", "    calc_inv_df.groupby([\"date_\", \"city\", \"facility_id\", \"storage_type\", \"l1\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"cpd\": \"sum\",\n", "            \"current_inv\": \"sum\",\n", "            \"dflag_\": \"sum\",\n", "            \"dinv\": \"sum\",\n", "            \"eflag_\": \"sum\",\n", "            \"einv\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "l1_inv_agg = l1_inv_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"cpd\",\n", "        \"item_id\",\n", "        \"current_inv\",\n", "        \"dflag_\",\n", "        \"dinv\",\n", "        \"eflag_\",\n", "        \"einv\",\n", "    ]\n", "]\n", "\n", "# --- Facility Aggregated --- #\n", "facility_inv_agg = (\n", "    calc_inv_df.groupby([\"date_\", \"city\", \"facility_id\", \"storage_type\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"cpd\": \"sum\",\n", "            \"current_inv\": \"sum\",\n", "            \"dflag_\": \"sum\",\n", "            \"dinv\": \"sum\",\n", "            \"eflag_\": \"sum\",\n", "            \"einv\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "facility_inv_agg[\"l1\"] = \"All\"\n", "facility_inv_agg = facility_inv_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"cpd\",\n", "        \"item_id\",\n", "        \"current_inv\",\n", "        \"dflag_\",\n", "        \"dinv\",\n", "        \"eflag_\",\n", "        \"einv\",\n", "    ]\n", "]\n", "\n", "#  --- City L1 Aggregated --- #\n", "city_l1_inv_agg = (\n", "    calc_inv_df.groupby([\"date_\", \"city\", \"storage_type\", \"l1\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"cpd\": \"sum\",\n", "            \"current_inv\": \"sum\",\n", "            \"dflag_\": \"sum\",\n", "            \"dinv\": \"sum\",\n", "            \"eflag_\": \"sum\",\n", "            \"einv\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "city_l1_inv_agg[\"facility_id\"] = -666\n", "city_l1_inv_agg = city_l1_inv_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"cpd\",\n", "        \"item_id\",\n", "        \"current_inv\",\n", "        \"dflag_\",\n", "        \"dinv\",\n", "        \"eflag_\",\n", "        \"einv\",\n", "    ]\n", "]\n", "\n", "#  --- City Aggregated --- #\n", "city_inv_agg = (\n", "    calc_inv_df.groupby([\"date_\", \"city\", \"storage_type\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"cpd\": \"sum\",\n", "            \"current_inv\": \"sum\",\n", "            \"dflag_\": \"sum\",\n", "            \"dinv\": \"sum\",\n", "            \"eflag_\": \"sum\",\n", "            \"einv\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "city_inv_agg[\"facility_id\"] = -666\n", "city_inv_agg[\"l1\"] = \"All\"\n", "city_inv_agg = city_inv_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"cpd\",\n", "        \"item_id\",\n", "        \"current_inv\",\n", "        \"dflag_\",\n", "        \"dinv\",\n", "        \"eflag_\",\n", "        \"einv\",\n", "    ]\n", "]\n", "\n", "# --- <PERSON><PERSON>s --- #\n", "inv_summary = pd.concat([l1_inv_agg, facility_inv_agg, city_l1_inv_agg, city_inv_agg])\n", "\n", "del l1_inv_agg, facility_inv_agg, city_l1_inv_agg, city_inv_agg, calc_inv_df\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "69c26037-a98a-43ed-b244-75822102db82", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    inv_summary,\n", "    sheetid=\"1yKKp2yESDd8Arer2cSMTGCRjjgIimE4wvG7GB6CDEFY\",\n", "    sheetname=\"inventory_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "160dda43-ae9d-4fdf-855d-7fb032b60cdf", "metadata": {"tags": []}, "source": ["## STO Data"]}, {"cell_type": "code", "execution_count": null, "id": "6ec0f8fe-e30c-4f1e-81bf-ec235c3071c5", "metadata": {}, "outputs": [], "source": ["sto_query = f\"\"\"\n", "    WITH sto_base AS (\n", "        SELECT sto_id, DATE(sto_invoice_created_at) AS invoice_raised_date, receiving_outlet_id AS outlet_id, \n", "        DATE(grn_started_at) AS grn_date, item_id, reserved_quantity AS sto_quantity, inwarded_quantity AS grn_quantity\n", "        FROM metrics.esto_details es\n", "        WHERE grn_started_at >= current_date - 7 AND invoice_state IN ('DISCREPANCY_NOTE_GENERATED','GRN Partial','GRN Complete') \n", "        AND receiving_outlet_id IN {outlet_list}\n", "        AND item_id IN {item_list}\n", "    ),\n", "    \n", "    sto AS (\n", "        SELECT grn_date AS date_, facility_id, sto_id, a.item_id, SUM(sto_quantity) AS sto_qty, SUM(grn_quantity) AS grn_qty\n", "        FROM sto_base a\n", "        JOIN lake_retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "        GROUP BY 1,2,3,4\n", "    )\n", "    SELECT * FROM sto\n", "    \"\"\"\n", "sto_df = pd.read_sql_query(sto_query, redshift)\n", "sto_df.shape"]}, {"cell_type": "markdown", "id": "faec0ed5-25ed-4399-b857-22f149d1e553", "metadata": {"tags": []}, "source": ["## STO Calculations"]}, {"cell_type": "markdown", "id": "1a0d3308-9e70-41e9-b81e-d933837301ae", "metadata": {"tags": []}, "source": ["#### Base"]}, {"cell_type": "code", "execution_count": null, "id": "b8922c93-b369-45a9-af5e-b87be325cff0", "metadata": {}, "outputs": [], "source": ["calc_sto_df = pd.merge(sto_df, assortment_df, on=[\"item_id\"], how=\"left\")\n", "calc_sto_df = calc_sto_df.merge(fe_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "calc_sto_df = calc_sto_df[\n", "    calc_sto_df[\"date_\"].astype(\"datetime64[ns]\").isin([today_date, l1_date, l7_date])\n", "]\n", "calc_sto_df.head(1)"]}, {"cell_type": "markdown", "id": "f28f8d5d-6253-4190-aef8-a0b1d979ce3a", "metadata": {"tags": []}, "source": ["#### Calculations"]}, {"cell_type": "code", "execution_count": null, "id": "875b74a9-f3ad-4f60-8a2d-ccfac420a6b6", "metadata": {}, "outputs": [], "source": ["# --- Facility L1 Aggregated --- #\n", "l1_sto_agg = (\n", "    calc_sto_df.groupby([\"date_\", \"city\", \"facility_id\", \"facility_name\", \"storage_type\", \"l1\"])\n", "    .agg({\"sto_id\": \"nunique\", \"sto_qty\": \"sum\", \"grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# --- Facility Aggregated --- #\n", "facility_agg = (\n", "    calc_sto_df.groupby([\"date_\", \"city\", \"facility_id\", \"facility_name\", \"storage_type\"])\n", "    .agg({\"sto_id\": \"nunique\", \"sto_qty\": \"sum\", \"grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "facility_agg[\"l1\"] = \"All\"\n", "facility_agg = facility_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"sto_id\",\n", "        \"sto_qty\",\n", "        \"grn_qty\",\n", "    ]\n", "]\n", "\n", "# --- City L1 Aggregated --- #\n", "city_l1_sto_agg = (\n", "    calc_sto_df.groupby([\"date_\", \"city\", \"storage_type\", \"l1\"])\n", "    .agg({\"sto_id\": \"nunique\", \"sto_qty\": \"sum\", \"grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "city_l1_sto_agg[\"facility_id\"] = -666\n", "city_l1_sto_agg[\"facility_name\"] = \"All\"\n", "city_l1_sto_agg = city_l1_sto_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"sto_id\",\n", "        \"sto_qty\",\n", "        \"grn_qty\",\n", "    ]\n", "]\n", "\n", "# --- City Aggregated --- #\n", "city_sto_agg = (\n", "    calc_sto_df.groupby([\"date_\", \"city\", \"storage_type\"])\n", "    .agg({\"sto_id\": \"nunique\", \"sto_qty\": \"sum\", \"grn_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "city_sto_agg[\"l1\"] = \"All\"\n", "city_sto_agg[\"facility_id\"] = -666\n", "city_sto_agg[\"facility_name\"] = \"All\"\n", "city_sto_agg = city_sto_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"sto_id\",\n", "        \"sto_qty\",\n", "        \"grn_qty\",\n", "    ]\n", "]\n", "\n", "# --- <PERSON><PERSON> All STO --- #\n", "sto_summary = pd.concat([l1_sto_agg, facility_agg, city_l1_sto_agg, city_sto_agg])\n", "\n", "del calc_sto_df, l1_sto_agg, facility_agg, city_l1_sto_agg, city_sto_agg\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "ad92632a-b696-4e1c-bbff-d0732f937d6b", "metadata": {}, "outputs": [], "source": ["sto_summary.shape"]}, {"cell_type": "markdown", "id": "fb3c78a5-0c13-40d6-bfb0-2e7f9e90f512", "metadata": {"tags": []}, "source": ["## Availability Calculations"]}, {"cell_type": "code", "execution_count": null, "id": "73e3490d-5f4f-4f3c-9962-7295fddafa08", "metadata": {}, "outputs": [], "source": ["avail_calc_df = pd.merge(availability_df, assortment_df, on=[\"item_id\"], how=\"left\")\n", "avail_calc_df = avail_calc_df[\n", "    avail_calc_df[\"date_\"].astype(\"datetime64[ns]\").isin([today_date, l1_date, l7_date])\n", "]\n", "\n", "avail_calc_df = avail_calc_df.merge(fe_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "avail_calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "55a0e3fa-5a11-4a29-a739-4a8f48d3409d", "metadata": {}, "outputs": [], "source": ["# --- Facility L1 Aggregated --- #\n", "l1_avail_agg = (\n", "    avail_calc_df.groupby([\"date_\", \"city\", \"facility_id\", \"facility_name\", \"storage_type\", \"l1\"])\n", "    .agg({\"avail_hours\": \"sum\", \"total_hours\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# --- Facility Aggregated --- #\n", "facility_avail_agg = (\n", "    avail_calc_df.groupby([\"date_\", \"city\", \"facility_id\", \"facility_name\", \"storage_type\"])\n", "    .agg({\"avail_hours\": \"sum\", \"total_hours\": \"sum\"})\n", "    .reset_index()\n", ")\n", "facility_avail_agg[\"l1\"] = \"All\"\n", "facility_avail_agg = facility_avail_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"avail_hours\",\n", "        \"total_hours\",\n", "    ]\n", "]\n", "\n", "# --- City L1 Aggregated --- #\n", "city_l1_avail_agg = (\n", "    avail_calc_df.groupby([\"date_\", \"city\", \"storage_type\", \"l1\"])\n", "    .agg({\"avail_hours\": \"sum\", \"total_hours\": \"sum\"})\n", "    .reset_index()\n", ")\n", "city_l1_avail_agg[\"facility_id\"] = -666\n", "city_l1_avail_agg[\"facility_name\"] = \"All\"\n", "city_l1_avail_agg = city_l1_avail_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"avail_hours\",\n", "        \"total_hours\",\n", "    ]\n", "]\n", "\n", "# --- City Aggregated --- #\n", "city_avail_agg = (\n", "    avail_calc_df.groupby([\"date_\", \"city\", \"storage_type\"])\n", "    .agg({\"avail_hours\": \"sum\", \"total_hours\": \"sum\"})\n", "    .reset_index()\n", ")\n", "city_avail_agg[\"l1\"] = \"All\"\n", "city_avail_agg[\"facility_id\"] = -666\n", "city_avail_agg[\"facility_name\"] = \"All\"\n", "city_avail_agg = city_avail_agg[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"avail_hours\",\n", "        \"total_hours\",\n", "    ]\n", "]\n", "\n", "# --- <PERSON><PERSON> All STO --- #\n", "avail_summary = pd.concat([l1_avail_agg, facility_avail_agg, city_l1_avail_agg, city_avail_agg])\n", "avail_summary[\"availability\"] = avail_summary[\"avail_hours\"] / avail_summary[\"total_hours\"]\n", "\n", "del l1_avail_agg, facility_avail_agg, city_l1_avail_agg, city_avail_agg, avail_calc_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "a6211f7f-0892-42f1-bd88-413ddfc781cc", "metadata": {"tags": []}, "source": ["## Utilisation Calculations"]}, {"cell_type": "code", "execution_count": null, "id": "f4b447c0-b44b-42b7-a6ab-de8f81dd0a99", "metadata": {}, "outputs": [], "source": ["past_inv_query = f\"\"\"\n", "    WITH rpc_pre AS (\n", "        SELECT facility_id, item_id, order_date, (actual_quantity - blocked_quantity) AS net_inv\n", "        FROM consumer.rpc_daily_availability\n", "        WHERE order_date >= current_date - 7\n", "    ),\n", "    \n", "    pg_inv AS (\n", "        SELECT date_, facility_id, item_id, MAX(net_inv) AS max_inv\n", "        FROM (\n", "            SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, facility_id, a.item_id, net_inv\n", "            FROM rpc_pre a\n", "            WHERE facility_id IN {facility_list} AND item_id IN {pg_item_list}\n", "        )\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    perishable_inv AS (\n", "        SELECT date_, facility_id, item_id, MAX(current_inv) AS max_inv\n", "        FROM (\n", "            SELECT DATE(updated_at) AS date_, EXTRACT(hour FROM updated_at) AS hour_, facility_id, item_id, current_inv\n", "            FROM metrics.perishable_hourly_details_v2\n", "            WHERE facility_id IN {facility_list} AND item_id IN {perishable_item_list} AND updated_at >= current_date - 7\n", "        )\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    inventory AS (\n", "        SELECT * FROM pg_inv\n", "        UNION\n", "        SELECT * FROM perishable_inv\n", "    )\n", "    \n", "    SELECT * FROM inventory\n", "    \"\"\"\n", "past_inv_df = pd.read_sql_query(past_inv_query, redshift)\n", "\n", "storage_query = f\"\"\"\n", "    SELECT DATE(a.created_at) AS date_, a.facility_id, storage_type, total_storage_cap AS storage_capacity \n", "    FROM consumer.facility_storage_capacity_details a \n", "    JOIN (\n", "        SELECT DATE(created_at) AS date, facility_id, MAX(created_at) AS created_at\n", "        FROM consumer.facility_storage_capacity_details\n", "        GROUP BY 1,2\n", "    ) b ON a.facility_id = b.facility_id AND a.created_at = b.created_at\n", "    WHERE a.facility_id IN {facility_list} AND storage_type IN ('Frozen','Cold')\n", "    \"\"\"\n", "storage_df = pd.read_sql_query(storage_query, redshift)\n", "storage_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3730d259-5c71-4c22-b609-7764745930ec", "metadata": {}, "outputs": [], "source": ["utli_calc_df = pd.merge(past_inv_df, assortment_df, on=[\"item_id\"], how=\"left\")\n", "\n", "utli_calc_df = utli_calc_df[\n", "    utli_calc_df[\"date_\"].astype(\"datetime64[ns]\").isin([today_date, l1_date, l7_date])\n", "]\n", "\n", "utli_calc_df = (\n", "    utli_calc_df.groupby([\"date_\", \"facility_id\", \"storage_type\", \"l1\"])\n", "    .agg({\"max_inv\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "utli_calc_df = utli_calc_df.merge(\n", "    storage_df, on=[\"date_\", \"facility_id\", \"storage_type\"], how=\"left\"\n", ")\n", "\n", "l1_agg_inv = (\n", "    utli_calc_df.groupby([\"date_\", \"facility_id\", \"storage_type\"])\n", "    .agg({\"max_inv\": \"sum\", \"storage_capacity\": \"mean\"})\n", "    .reset_index()\n", ")\n", "l1_agg_inv = l1_agg_inv.rename(columns={\"max_inv\": \"l1_agg_inv\"})\n", "l1_agg_inv[\"l1\"] = \"All\"\n", "\n", "utli_calc_df = utli_calc_df.merge(\n", "    l1_agg_inv.drop(columns={\"l1\", \"storage_capacity\"}),\n", "    on=[\"date_\", \"facility_id\", \"storage_type\"],\n", "    how=\"left\",\n", ")\n", "utli_calc_df = utli_calc_df.drop(columns={\"storage_capacity\"}).rename(\n", "    columns={\"l1_agg_inv\": \"storage_capacity\"}\n", ")\n", "\n", "l1_agg_inv = l1_agg_inv[\n", "    [\"date_\", \"facility_id\", \"storage_type\", \"l1\", \"l1_agg_inv\", \"storage_capacity\"]\n", "].rename(columns={\"l1_agg_inv\": \"max_inv\"})\n", "\n", "utli_summary_df = pd.concat([utli_calc_df, l1_agg_inv])\n", "utli_summary_df = utli_summary_df.merge(\n", "    fe_df.drop(columns={\"facility_name\"}), on=[\"facility_id\"], how=\"left\"\n", ")\n", "\n", "utli_summary_df = utli_summary_df[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"max_inv\",\n", "        \"storage_capacity\",\n", "    ]\n", "]\n", "\n", "# --- City Aggregated --- #\n", "city_utli_summary = (\n", "    utli_summary_df.groupby([\"date_\", \"city\", \"storage_type\", \"l1\"])\n", "    .agg({\"max_inv\": \"sum\", \"storage_capacity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "city_utli_summary[\"facility_id\"] = -666\n", "city_utli_summary = city_utli_summary[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"max_inv\",\n", "        \"storage_capacity\",\n", "    ]\n", "]\n", "\n", "#  --- Merged --- #\n", "utli_summary = pd.concat([city_utli_summary, utli_summary_df])\n", "\n", "del city_utli_summary, utli_summary_df, l1_agg_inv\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "37c543e2-d059-4eb4-8141-e596e7861f53", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["## Summary Final"]}, {"cell_type": "code", "execution_count": null, "id": "6513ea82-bc0d-4051-b895-2d543179d111", "metadata": {"tags": []}, "outputs": [], "source": ["summary_df = pd.merge(\n", "    avail_summary,\n", "    sto_summary,\n", "    on=[\"date_\", \"city\", \"facility_id\", \"facility_name\", \"storage_type\", \"l1\"],\n", "    how=\"left\",\n", ")\n", "summary_df = summary_df.merge(\n", "    utli_summary, on=[\"date_\", \"city\", \"facility_id\", \"storage_type\", \"l1\"], how=\"left\"\n", ")\n", "\n", "summary_df[\"utilisation\"] = summary_df[\"max_inv\"] / summary_df[\"storage_capacity\"]\n", "summary_df[\"fr\"] = summary_df[\"grn_qty\"] / summary_df[\"sto_qty\"]\n", "\n", "summary_df = summary_df[\n", "    [\n", "        \"date_\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"storage_type\",\n", "        \"l1\",\n", "        \"storage_capacity\",\n", "        \"utilisation\",\n", "        \"availability\",\n", "        \"sto_id\",\n", "        \"sto_qty\",\n", "        \"fr\",\n", "    ]\n", "]\n", "\n", "summary_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e40648de-1d70-415e-af99-121876d1a488", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    summary_df,\n", "    sheetid=\"1yKKp2yESDd8Arer2cSMTGCRjjgIimE4wvG7GB6CDEFY\",\n", "    sheetname=\"store_raw\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}