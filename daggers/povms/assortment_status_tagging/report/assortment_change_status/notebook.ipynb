{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Facility and Item Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping_query = f\"\"\"SELECT distinct co.facility_id, pf.name as facility_name, cl.name as city_name\n", "FROM lake_retail.console_outlet co\n", "LEFT JOIN lake_crates.facility pf ON pf.id = co.facility_id\n", "LEFT JOIN lake_retail.console_location cl on co.tax_location_id = cl.id\n", "WHERE co.active = 1 and business_type_id = 7\"\"\"\n", "\n", "facility_mapping_df = read_sql_query(sql=facility_mapping_query, con=CON_REDSHIFT)\n", "\n", "front_facility_id_list = list(facility_mapping_df[\"facility_id\"].unique())\n", "front_facility_id_list = tuple(front_facility_id_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_details_query = f\"\"\"\n", "with\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", " it_pd as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "multiplier,\n", "updated_on,\n", "max(updated_on) over (partition by product_id) as last_updated\n", "from \n", "it_pd_log\n", "),\n", "\n", "mapping as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "coalesce(multiplier,1) as multiplier\n", "from it_pd\n", "where last_updated = updated_on\n", "),\n", "\n", "categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "P.UNIT AS Unit,\n", "C2.NAME AS L2,\n", "(case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "C.NAME AS L0,\n", "P.BRAND AS brand,\n", "P.MANUFACTURER AS manf,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "), \n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l0,\n", "cat.l1,\n", "cat.l2,\n", "cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l0) as l0,\n", "max(l1) as l1,\n", "max(l2) as l2,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", ")\n", "\n", "\n", "select distinct p.item_id,p.name as item_name, l0, l1, ptype\n", "from lake_rpc.item_details p\n", "LEFT JOIN ptype_tbl pt on p.item_id = pt.item_id\n", "where p.active = 1 and p.approved = 1\n", "\"\"\"\n", "\n", "item_details = read_sql_query(sql=item_details_query, con=CON_REDSHIFT)\n", "item_details.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Active Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Based on frontend facility\n", "master_assortment_query = f\"\"\"SELECT internal_facility_identifier AS \"facility_name\",\n", "                   ma.facility_id,\n", "                   ma.item_id AS \"item_id\",\n", "                   rpp.name,\n", "                   pmas.name AS master_assortment_substate,\n", "                   left(ma.updated_at,19) AS last_update\n", "   FROM lake_rpc.product_facility_master_assortment ma\n", "   LEFT JOIN lake_rpc.product_master_assortment_substate pmas ON ma.master_assortment_substate_id = pmas.id\n", "   LEFT JOIN lake_po.physical_facility pf ON pf.facility_id = ma.facility_id\n", "   LEFT JOIN lake_rpc.product_product rpp ON rpp.item_id = ma.item_id\n", "   INNER JOIN lake_retail.console_outlet co on co.facility_id = ma.facility_id\n", "   WHERE ma.master_assortment_substate_id = 1 \n", "   AND ma.active =1 \n", "   AND ma.facility_id in {front_facility_id_list} \n", "      -- rpp.outlet_type = 2  AND\n", "      -- Groceies\n", "     AND rpp.active = 1\n", "     AND rpp.id IN\n", "       (SELECT max(id) AS id\n", "        FROM lake_rpc.product_product pp\n", "        WHERE pp.active = 1\n", "          AND pp.approved = 1\n", "        GROUP BY item_id)\n", "    GROUP BY 1,2,3,4,5,6\"\"\"\n", "\n", "master_assortment_0 = read_sql_query(sql=master_assortment_query, con=CON_REDSHIFT)\n", "# master_assortment = master_assortment_0[[\"facility_id\", \"item_id\"]].drop_duplicates()\n", "# check_level(master_assortment, [\"facility_id\", \"item_id\"], \"master_assortment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# current_date =\n", "last_date = datetime.now() + timedelta(hours=5.5) - timed<PERSON>ta(days=1)\n", "last_date = pd.to_datetime(last_date.strftime(\"%Y-%m-%d\")) + timed<PERSON>ta(hours=9)\n", "current_date = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "current_date = pd.to_datetime(current_date.strftime(\"%Y-%m-%d\")) + timed<PERSON>ta(hours=9)\n", "current_date, last_date"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Assortment Change to Inactive after Last day 9 AM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_assortment_change_query = f\"\"\"\n", "SELECT * FROM(\n", "select created_at + interval '330 minute' as created_at, facility_id, item_id, old_substate, pmas_old.name AS old_substate_name,\n", "new_substate, pmas.name AS new_substate_name\n", "from lake_rpc.product_facility_master_assortment_log ma_log\n", "Inner JOIN lake_rpc.product_master_assortment_substate pmas ON ma_log.new_substate = pmas.id\n", "LEFT JOIN lake_rpc.product_master_assortment_substate pmas_old ON ma_log.old_substate = pmas_old.id\n", "where facility_id in {front_facility_id_list}\n", "and created_at >= current_date - interval '2 days'\n", "and pmas.name = 'Inactive')\n", "WHERE created_at >= date(current_date + interval '330 minute' - interval '1 days') + interval '9 hours'\n", "\"\"\"\n", "\n", "master_assortment_change_df = read_sql_query(sql=master_assortment_change_query, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_assortment_change_df.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Remove Currently Active from Change log Base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base = master_assortment_change_df[\n", "    (master_assortment_change_df.old_substate_name == \"Active\")\n", "    & (master_assortment_change_df.new_substate_name == \"Inactive\")\n", "]\n", "\n", "master_assortment_0 = master_assortment_0[\n", "    master_assortment_0.master_assortment_substate == \"Active\"\n", "]\n", "master_assortment_0 = master_assortment_0[\n", "    [\"facility_id\", \"item_id\", \"master_assortment_substate\"]\n", "].drop_duplicates()\n", "\n", "base = pd.merge(base, master_assortment_0, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "base = base[base.master_assortment_substate.isna() == True]\n", "base = base.drop(columns={\"master_assortment_substate\"})\n", "base = pd.merge(base, item_details, on=[\"item_id\"], how=\"left\")\n", "\n", "base[[\"old_substate\", \"new_substate\"]] = base[[\"old_substate\", \"new_substate\"]].astype(int)\n", "\n", "base = pd.merge(base, facility_mapping_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "base = base[\n", "    [\n", "        \"created_at\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"ptype\",\n", "    ]\n", "].rename(columns={\"created_at\": \"state_changed_at\"})\n", "base.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base[\"total_cases\"] = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = (\n", "    base.groupby([\"city_name\"])\n", "    .agg({\"item_id\": \"nunique\", \"facility_id\": \"nunique\", \"total_cases\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"item_id\": \"Count of Items\",\n", "            \"city_name\": \"City Name\",\n", "            \"facility_id\": \"Count of Facilities\",\n", "            \"total_cases\": \"Count of Item Facility Combinations\",\n", "        }\n", "    )\n", ")\n", "summary.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Sending Mailer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_to_html_drop(df):\n", "    html_table_summary = \"\"\"\n", "    <div class=\"mce-toc\">\n", "      <table style=\"border-collapse: collapse; width: 400px; \" border=\"1\">\n", "        <tbody>\n", "          <tr style=\"height: 5px;\">\n", "    \"\"\"\n", "\n", "    for x in df.columns:\n", "        html_table_summary += \"\"\"<td style=\"width: 100px; height: 5px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "        html_table_summary += x\n", "        html_table_summary += \"\"\"</strong></span></td>\"\"\"\n", "\n", "    html_table_summary += \"\"\"</tr>\"\"\"\n", "\n", "    for i, r in df.iterrows():\n", "        html_table_summary += \"\"\"<tr style=\"height: 10px;\">\"\"\"\n", "        for x in df.columns:\n", "            if x == \"Destination Outlet\" or x == \"Catering Facility\":\n", "                html_table_summary += \"\"\"<td style=\"width: 10px; height: 5px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "                html_table_summary += str(r[x])\n", "                html_table_summary += \"\"\"</strong></span></td>\"\"\"\n", "            else:\n", "                html_table_summary += (\n", "                    \"\"\"<td style=\"width: 300px; height: 5px; text-align: center;\">\"\"\"\n", "                )\n", "                html_table_summary += str(r[x])\n", "                html_table_summary += \"\"\"</td>\"\"\"\n", "        html_table_summary += \"\"\"</tr>\"\"\"\n", "\n", "    html_table_summary += \"\"\"\n", "        </tbody>\n", "      </table>\n", "    </div>\n", "    \"\"\"\n", "    return html_table_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_table_summary_full = df_to_html_drop(summary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "bucket_name = \"grofers-retail-test\"\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "s3c = boto3.client(\"s3\", aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "Tag = \"datapulls\"\n", "cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# directories\n", "GLOBAL_BASE_DIR = cwd\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_files_created = []\n", "\n", "\n", "def to_S3(file, name):\n", "    try:\n", "\n", "        bucket_obj.upload_file(file, f\"reports/{file.split('/')[-1]}\")\n", "        print({file.split(\"/\")[-1]})\n", "        log_files_created.append(file)\n", "        presigned_url = s3c.generate_presigned_url(\n", "            \"get_object\",\n", "            Params={\"Bucket\": bucket_name, \"Key\": f\"reports/{file.split('/')[-1]}\"},\n", "            HttpMethod=\"GET\",\n", "            ExpiresIn=86400,\n", "        )\n", "        return presigned_url\n", "    except Exception as e:\n", "        error = f\"\"\":red_circle: *Alert*: Smart Indent - Simulation, Error uploading log file `{name}` to S3 for Run ID: `{uid}`\n", "        \\n```{str(e)}```\\n Download Link - `{file}`\"\"\"\n", "        send_to_slack(error)\n", "        raise Exception(e)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_file_name = f\"{outputs}/status_change_after_{last_date}.csv\"\n", "base.to_csv(log_file_name, index=False)\n", "smart_attachment = to_S3(log_file_name, \"Assortment Status Change\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base.to_csv(\"assortment_status_change.csv\", compression='gzip')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1BzjrZYwqoCfcm6FOrPIzkvPEwgkJI6bSVmjk4uJsmYg\"\n", "rec = pb.from_sheets(sheet_id, \"Emails\")\n", "mail_list = [x for x in rec[\"Emails\"].to_list() if \"@grofers.com\" in x or \"@handsontrades.com\" in x]\n", "mail_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = mail_list\n", "subject = f\"Assortment Made Inactive after {last_date}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# to_email = ['<EMAIL>']\n", "to_email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_content = f\"\"\"\n", "Hi,<br><br>\n", "Please find the <a href={smart_attachment}>link to the report</a> capturing item facility combinations that were made inactive from active state after {last_date} hours.<br>\n", "Below is the count of item facility combinations across cities where the assortment state has changed to inactive:<br><br>\n", "\n", "{html_table_summary_full}<br><br>\n", "\n", "Do reach <NAME_EMAIL> incase of any questions or concerns\n", "<br><br>\n", "<PERSON><PERSON>,<br>\n", "Data Inventory\n", "\"\"\"\n", "\n", "html_content_no_data = f\"\"\"\n", "Hi,<br><br>\n", "No item facility combinations were made inactive from active state after {last_date} hours.<br>\n", "\n", "Do reach <NAME_EMAIL> incase of any questions or concerns\n", "<br><br>\n", "<PERSON><PERSON>,<br>\n", "Data Inventory\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if base.shape[0] > 0:\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        # files=[\"assortment_status_change.csv\"],\n", "    )\n", "else:\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content_no_data,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}