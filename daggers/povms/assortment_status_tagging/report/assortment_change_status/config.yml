alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: assortment_change_status
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURE15HB
path: povms/assortment_status_tagging/report/assortment_change_status
paused: true
pool: povms_pool
project_name: assortment_status_tagging
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 3 * * *
  start_date: '2022-03-02T12:30:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
