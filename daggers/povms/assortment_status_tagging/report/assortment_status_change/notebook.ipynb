{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date, datetime, timedelta\n", "import numpy as np\n", "import requests\n", "import json\n", "from datetime import date\n", "import datetime\n", "\n", "datetime.datetime.strptime\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import uuid\n", "import logging\n", "from copy import deepcopy\n", "import warnings\n", "import numpy as np\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "import os\n", "import boto3\n", "\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import time\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.DEBUG)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dates\n", "today = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "t_minus = today - <PERSON><PERSON><PERSON>(days=15)\n", "print(\"start date: \" + str(t_minus) + \" | end date: \" + str(today))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_q = f\"\"\"\n", "SELECT distinct item_id, facility_id, concat(facility_id,item_id) as join_key, old_substate, new_substate, (created_at) as created_at, created_by\n", "FROM rpc.product_facility_master_assortment_log AS ma_log\n", "WHERE item_id IN (SELECT distinct item_id FROM\n", "                    (SELECT distinct item_id, facility_id, old_substate, new_substate, (created_at) as created_at\n", "                    FROM rpc.product_facility_master_assortment_log\n", "                    WHERE date(created_at) between date('{str(t_minus)}') and date('{str(today)}')\n", "                    and old_substate is not null)A) \n", "                    and facility_id in (SELECT distinct facility_id FROM\n", "                    (SELECT distinct item_id, facility_id, old_substate, new_substate, (created_at) as created_at\n", "                    FROM rpc.product_facility_master_assortment_log\n", "                    WHERE date(created_at) between date('{str(t_minus)}') and date('{str(today)}')\n", "                    and old_substate is not null)B)\"\"\"\n", "\n", "ma_init_01 = read_sql_query(sql=ma_q, con=retail)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remove Inactive Stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facilitymapping_query = f\"\"\"select distinct facility_id, name as facility_name from\n", "po.physical_facility\n", "where active = 1\"\"\"\n", "facilitymapping = read_sql_query(sql=facilitymapping_query, con=retail)\n", "\n", "franchise_facility_query = (\n", "    f\"\"\"select distinct facility_id from retail.console_outlet where business_type_id=8\"\"\"\n", ")\n", "franchise_facility = read_sql_query(sql=franchise_facility_query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_facilities = facilitymapping[[\"facility_id\"]].drop_duplicates()\n", "active_facilities[\"Active_Flag\"] = 1\n", "ma_init_02 = pd.merge(\n", "    ma_init_01,\n", "    active_facilities,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma_init_02 = ma_init_02[ma_init_02.Active_Flag == 1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remove Franchise Stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["franchise_facility = franchise_facility[[\"facility_id\"]].drop_duplicates()\n", "franchise_facility[\"Franchise_Flag\"] = 1\n", "ma_init_03 = pd.merge(\n", "    ma_init_02,\n", "    franchise_facility,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma = ma_init_03[ma_init_03.Franchise_Flag.isna()]\n", "ma = ma.drop([\"Franchise_Flag\", \"Active_Flag\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def filter_date(x):\n", "    created_at_I = x[\"created_at\"]\n", "    if created_at_I.date() > today:\n", "        return 0\n", "    else:\n", "        return 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma[\"filter_date\"] = ma.apply(filter_date, axis=1)\n", "ma = ma[ma.filter_date != 0]\n", "ma = ma.drop([\"filter_date\"], axis=1)\n", "ma.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_query = f\"\"\"SELECT distinct item_id, facility_id, concat(facility_id,item_id) as join_key\n", "            FROM\n", "                (   SELECT distinct item_id, facility_id, old_substate, new_substate, (created_at) as created_at\n", "                    FROM rpc.product_facility_master_assortment_log\n", "                    WHERE date(created_at) between date('{str(t_minus)}') and date('{str(today)}')\n", "                    and old_substate is not null\n", "                ) AS A\n", "            \"\"\"\n", "list_log = read_sql_query(sql=list_query, con=retail)  # ,params={'start_date':current}) #:today})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def old_substate_flag(x):\n", "    flag = x[\"old_substate\"]\n", "    if flag == 1:\n", "        # is feeder\n", "        return \"Active\"\n", "    elif math.isnan(flag) == True:\n", "        return \"Unknown\"\n", "    else:\n", "        return \"Inactive\"\n", "\n", "\n", "def new_substate_flag(x):\n", "    flag = x[\"new_substate\"]\n", "    if flag == 1:\n", "        # is feeder\n", "        return \"Active\"\n", "    elif math.isnan(flag) == True:\n", "        return \"Unknown\"\n", "    else:\n", "        return \"Inactive\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_filter = pd.merge(\n", "    ma,\n", "    list_log[[\"join_key\"]].drop_duplicates(),\n", "    left_on=[\"join_key\"],\n", "    right_on=[\"join_key\"],\n", "    how=\"inner\",\n", ")\n", "ma_filter = ma_filter[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"old_substate\",\n", "        \"new_substate\",\n", "        \"created_at\",\n", "        \"created_by\",\n", "    ]\n", "]\n", "ma_filter[\"ID_Rank\"] = ma_filter.groupby([\"facility_id\", \"item_id\"])[\"created_at\"].rank(\n", "    ascending=False\n", ")\n", "ma_filter[\"old_substate_flag\"] = ma_filter.apply(old_substate_flag, axis=1)\n", "ma_filter[\"new_substate_flag\"] = ma_filter.apply(new_substate_flag, axis=1)\n", "ma_top_rank = ma_filter[ma_filter.ID_Rank < 3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_current_state = ma_top_rank[ma_top_rank.ID_Rank == 1.0][\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"old_substate\",\n", "        \"old_substate_flag\",\n", "        \"new_substate\",\n", "        \"new_substate_flag\",\n", "        \"created_at\",\n", "        \"created_by\",\n", "        \"ID_Rank\",\n", "    ]\n", "]\n", "ma_previous_state = ma_top_rank[ma_top_rank.ID_Rank == 2.0][\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"old_substate\",\n", "        \"old_substate_flag\",\n", "        \"new_substate\",\n", "        \"new_substate_flag\",\n", "        \"created_at\",\n", "        \"created_by\",\n", "        \"ID_Rank\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state = pd.merge(\n", "    ma_current_state,\n", "    ma_previous_state,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma_state.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"old_substate_I\",\n", "    \"old_substate_flag_I\",\n", "    \"new_substate_I\",\n", "    \"new_substate_flag_I\",\n", "    \"created_at_I\",\n", "    \"created_by_I\",\n", "    \"ID_Rank_I\",\n", "    \"old_substate_II\",\n", "    \"old_substate_flag_II\",\n", "    \"new_substate_II\",\n", "    \"new_substate_flag_II\",\n", "    \"created_at_II\",\n", "    \"created_by_II\",\n", "    \"ID_Rank_II\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state[\"old_status_since\"] = (\n", "    ma_state[\"created_at_I\"] - ma_state[\"created_at_II\"]\n", ") / np.timedelta64(\n", "    1, \"D\"\n", ")  # days"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def date_flag(x):\n", "    created_at_I = x[\"created_at_I\"]\n", "    return created_at_I.date()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state[\"date_flag\"] = ma_state.apply(date_flag, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["username_query = f\"\"\"select distinct id as created_by_I,username from retail.auth_user\"\"\"\n", "username = read_sql_query(\n", "    sql=username_query, con=retail\n", ")  # ,params={'start_date':current}) #:today})\n", "username.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_3 = pd.merge(\n", "    ma_state, username, left_on=[\"created_by_I\"], right_on=[\"created_by_I\"], how=\"left\"\n", ")\n", "ma_state_3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_4 = ma_state_3[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"old_substate_flag_I\",\n", "        \"old_status_since\",\n", "        \"created_at_I\",\n", "        \"username\",\n", "        \"new_substate_flag_I\",\n", "    ]\n", "]\n", "ma_state_4.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"old_status\",\n", "    \"since_old_status\",\n", "    \"date_of_change\",\n", "    \"requested_by\",\n", "    \"new_status\",\n", "]\n", "ma_state_4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def old_status_since(x):\n", "    since_old_status = x[\"since_old_status\"]\n", "    if since_old_status < 182.5:\n", "        return str(round(since_old_status, 0))  # + \" Days\"\n", "    elif since_old_status > 182.5:\n", "        return \"more than 6 months\"\n", "    else:\n", "        return \"first status change\"\n", "\n", "\n", "ma_state_4[\"old_status_since\"] = ma_state_4.apply(old_status_since, axis=1)\n", "ma_state_4.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_5 = ma_state_4[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"old_status\",\n", "        \"old_status_since\",\n", "        \"date_of_change\",\n", "        \"requested_by\",\n", "        \"new_status\",\n", "    ]\n", "]\n", "\n", "ma_state_5.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frontend_facility_mapping_query = f\"\"\"SELECT DISTINCT rco.facility_id,\n", "                                        pf.internal_facility_identifier AS facility_name\n", "                                        FROM lake_rpc.item_outlet_tag_mapping tm\n", "                                        LEFT JOIN lake_retail.console_outlet rco ON rco.id = tm.outlet_id\n", "                                        INNER JOIN lake_po.physical_facility pf ON pf.facility_id = rco.facility_id\n", "                                        --WHERE tag_type_id = 8\n", "                                          WHERE tm.active = 1\n", "                                        ORDER BY pf.internal_facility_identifier ASC\"\"\"\n", "frontend_facility_mapping = read_sql_query(sql=frontend_facility_mapping_query, con=CON_REDSHIFT)\n", "frontend_facility_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frontend_facility_mapping = frontend_facility_mapping[\n", "    frontend_facility_mapping.facility_name != \"C 2\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_6 = pd.merge(\n", "    ma_state_5,\n", "    frontend_facility_mapping,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma_state_6.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_mapping_query = f\"\"\"with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "  SELECT item_id,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.product_type,\n", "          brand,\n", "          manf\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\"\"\"\n", "item_mapping = read_sql_query(sql=item_mapping_query, con=CON_REDSHIFT)\n", "item_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Getting Item_Name\n", "item_name_query = f\"\"\"SELECT DISTINCT A.item_id,\n", "                max(name) as item_name\n", "FROM lake_rpc.product_product A\n", "INNER JOIN\n", "  (SELECT max(updated_at) AS updated_at,\n", "          item_id\n", "   FROM lake_rpc.product_product\n", "   WHERE item_id IN {tuple(ma_state_6.item_id.unique())}\n", "   GROUP BY 2) B ON A.updated_at = B.updated_at\n", "AND A.item_id = B.item_id\n", "GROUP BY 1\"\"\"\n", "\n", "item_name = read_sql_query(sql=item_name_query, con=CON_REDSHIFT)\n", "item_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_7 = pd.merge(\n", "    ma_state_6,\n", "    item_mapping[[\"item_id\", \"l0\"]],\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "ma_state_7.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_8 = pd.merge(ma_state_7, item_name, left_on=[\"item_id\"], right_on=[\"item_id\"], how=\"left\")\n", "ma_state_8.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_Final = ma_state_8[\n", "    [\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"old_status\",\n", "        \"old_status_since\",\n", "        \"date_of_change\",\n", "        \"requested_by\",\n", "        \"new_status\",\n", "    ]\n", "]\n", "\n", "ma_state_Final.columns = [\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"l0\",\n", "    \"facility_id\",\n", "    \"facility_name\",\n", "    \"old_status\",\n", "    \"old_status_since\",\n", "    \"date_of_change\",\n", "    \"requested_by\",\n", "    \"new_status\",\n", "]\n", "ma_state_Final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_state_Final.agg({\"count\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id_o = \"1qTjE3WcsEE37Vffx7N7xdNu7DKzYFaNl0BEFqAIMMb4\"\n", "output_sheet_d1 = \"Summary\"\n", "pb.to_sheets(ma_state_Final, sheet_id_o, output_sheet_d1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1qTjE3WcsEE37Vffx7N7xdNu7DKzYFaNl0BEFqAIMMb4\"\n", "rec = pb.from_sheets(sheet_id, \"Emails\")\n", "mail_list = [x for x in rec[\"Email\"].to_list() if \"@grofers.com\" in x or \"@handsontrades.com\" in x]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_list"]}, {"cell_type": "raw", "metadata": {}, "source": ["len = mail_list.size\n", "final_rec = []\n", "for i in range(0, len):\n", "    x = mail_list[i]\n", "    final_rec.append(x)\n", "final_rec_0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1qTjE3WcsEE37Vffx7N7xdNu7DKzYFaNl0BEFqAIMMb4\"\n", "cc_0 = pb.from_sheets(sheet_id, \"Emails_cc\")\n", "cc = [x for x in cc_0[\"Email\"].to_list() if \"@grofers.com\" in x or \"@handsontrades.com\" in x]\n", "cc"]}, {"cell_type": "raw", "metadata": {}, "source": ["cc_0 = ['<EMAIL>',\n", "      '<EMAIL>',\n", "      '<EMAIL>']\n", "cc = [x for x in cc_0 if \"@grofers.com\" in x or \"@handsontrades.com\" in x]\n", "cc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = mail_list\n", "subject = \"Assortment Status Tagging Report - \" + str(today)\n", "html_content = \"\"\"\n", "Hi,<br><br>\n", "\n", "You can download the report capturing state changes in the last 15 days\n", "<a href='https://spreadsheets.google.com/feeds/download/spreadsheets/Export?key=1qTjE3WcsEE37Vffx7N7xdNu7DKzYFaNl0BEFqAIMMb4&exportFormat=csv'>\n", "here.\n", "</a>\n", "<br><br>\n", "<PERSON><PERSON>,<br>\n", "Data Bangalore\n", "\"\"\"\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content,\n", "    cc=cc\n", "    #     , files=['raw_data.csv'],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}