{"cells": [{"cell_type": "code", "execution_count": null, "id": "d24a5f2c-b741-4f0b-becb-716a4ae2f8a9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "\n", "import gc\n", "\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "440e3696-ba73-43c7-905e-46b03f0962ad", "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'\n", "\n", "pd.set_option(\"display.max_columns\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "4803076e-a39e-46e4-971c-652a79ea9a1f", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)"]}, {"cell_type": "code", "execution_count": null, "id": "ef72c4fc-5058-4506-bb4c-4ea8414165af", "metadata": {}, "outputs": [], "source": ["try:\n", "    backfill_df = pb.from_sheets(\n", "        sheetid=\"1i5g3cY3g09iIlEdWwatqYQ4jSLK81dhYR7wXp40BvKc\",\n", "        sheetname=\"jasoos_backfill\",\n", "    )\n", "    backfill_df[\"hour\"] = backfill_df[\"hour\"].astype(int)\n", "    backfill = backfill_df.iloc[:, 0][0]\n", "except:\n", "    backfill = \"no\""]}, {"cell_type": "code", "execution_count": null, "id": "10421315-4498-4971-b804-fd1f076bb31a", "metadata": {}, "outputs": [], "source": ["if backfill == \"yes\":\n", "\n", "    today_date = backfill_df.iloc[:, 1][0]\n", "    current_hour = backfill_df.iloc[:, 2][0] + 1\n", "    current_dow = pd.to_datetime(today_date).dayofweek\n", "\n", "else:\n", "\n", "    today_date = current_time.strftime(\"%Y-%m-%d\")\n", "    current_hour = pd.to_datetime(current_time).hour\n", "    current_dow = pd.to_datetime(current_time).dayofweek\n", "\n", "today_date, current_hour, current_dow"]}, {"cell_type": "code", "execution_count": null, "id": "f3da3228-4e9d-4d78-bc52-5b79b30c2f39", "metadata": {}, "outputs": [], "source": ["hour_df = pd.DataFrame(\n", "    {\n", "        \"hour_\": [\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            11,\n", "            12,\n", "            13,\n", "            14,\n", "            15,\n", "            16,\n", "            17,\n", "            18,\n", "            19,\n", "            20,\n", "            21,\n", "            22,\n", "            23,\n", "        ],\n", "        \"flag\": 1,\n", "    }\n", ")\n", "\n", "hour_df = hour_df[hour_df[\"hour_\"] < current_hour].reset_index().drop(columns={\"index\"})"]}, {"cell_type": "markdown", "id": "39d2aa82-97dd-49da-8d0f-fad8eb71a10a", "metadata": {}, "source": ["## Base Data"]}, {"cell_type": "code", "execution_count": null, "id": "f63731b8-c906-4ab2-8602-8e0d258b60f0", "metadata": {}, "outputs": [], "source": ["base_query = f\"\"\"\n", "    WITH active_stores AS (\n", "        SELECT DISTINCT pfom.facility_id, pfom.outlet_id, bfom.facility_id AS be_facility_id\n", "        FROM po.physical_facility_outlet_mapping pfom \n", "        JOIN retail.console_outlet co ON co.id = pfom.outlet_id AND co.active = 1 AND co.business_type_id = 7\n", "        JOIN po.bulk_facility_outlet_mapping bfom ON bfom.outlet_id = pfom.outlet_id AND bfom.active = True\n", "        WHERE ars_active = 1 AND pfom.active = 1 AND pfom.is_primary = 1 \n", "    ),\n", "\n", "    milk_assortment AS (\n", "        SELECT DISTINCT cl.name AS city, a.facility_id, outlet_id, item_id, be_facility_id\n", "        FROM rpc.product_facility_master_assortment a\n", "        JOIN active_stores b ON a.facility_id = b.facility_id\n", "        JOIN retail.console_outlet co ON co.facility_id = a.facility_id AND co.active = 1 AND co.business_type_id = 7\n", "        LEFT JOIN retail.console_location cl ON cl.id = co.tax_location_id\n", "        WHERE item_id IN (SELECT DISTINCT item_id FROM rpc.item_category_details WHERE l2_id = 1185) \n", "        AND a.active = 1 AND a.master_assortment_substate_id = 1\n", "    ),\n", "\n", "    be_mapping AS (\n", "        SELECT DISTINCT item_id, outlet_id, cb.facility_id AS be_facility_id\n", "        FROM rpc.item_outlet_tag_mapping tm\n", "        JOIN retail.console_outlet cb ON cb.id = CAST(tag_value AS int) AND cb.active = 1\n", "        WHERE tm.active = 1 AND tm.tag_type_id = 8\n", "    ),\n", "\n", "    final AS (\n", "        SELECT city, facility_id, a.outlet_id, a.item_id, a.be_facility_id\n", "        FROM milk_assortment a\n", "        JOIN be_mapping b ON a.item_id = b.item_id AND a.outlet_id = b.outlet_id AND a.be_facility_id = b.be_facility_id\n", "    )\n", "\n", "    SELECT * FROM final\n", "\"\"\"\n", "base_df = pd.read_sql_query(base_query, trino)\n", "\n", "base_df[\"be_facility_id\"] = base_df[\"be_facility_id\"].fillna(0).astype(int)\n", "\n", "base_df[\"flag\"] = 1\n", "\n", "base_df = base_df.merge(hour_df, on=[\"flag\"], how=\"left\").drop(columns={\"flag\"})"]}, {"cell_type": "markdown", "id": "fcab1fe9-00df-412f-be39-0ae4fdf7eb5f", "metadata": {}, "source": ["#### Base for Weights Merge - used later in the code"]}, {"cell_type": "code", "execution_count": null, "id": "e61b6f82-2fc9-4867-bb83-f2eb31e2ed90", "metadata": {}, "outputs": [], "source": ["city_item_df = base_df[[\"city\", \"item_id\"]].drop_duplicates().reset_index().drop(columns={\"index\"})"]}, {"cell_type": "markdown", "id": "682fb6a2-bdcd-4476-8b30-51b7c1562c75", "metadata": {}, "source": ["#### Item Outlet Backend List"]}, {"cell_type": "code", "execution_count": null, "id": "7bf374fc-1bdf-4a11-aeb3-52a46ea07bc4", "metadata": {}, "outputs": [], "source": ["be_facility_id_list = tuple(set(base_df[\"be_facility_id\"].to_list()))\n", "facility_id_list = tuple(set(base_df[\"facility_id\"].to_list()))\n", "outlet_id_list = tuple(set(base_df[\"outlet_id\"].to_list()))\n", "item_id_list = tuple(set(base_df[\"item_id\"].to_list()))\n", "\n", "len(item_id_list), len(facility_id_list), len(outlet_id_list), len(be_facility_id_list)"]}, {"cell_type": "markdown", "id": "b78ba984-224b-480b-ba96-42c07c86bb9a", "metadata": {"tags": []}, "source": ["# Availability Data "]}, {"cell_type": "code", "execution_count": null, "id": "b7448b7c-03fe-486c-9e37-521b5745fc72", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT * FROM supply_etls.hourly_inventory_snapshots\n", "        WHERE date_ist = DATE('{today_date}')\n", "    ),\n", "    \n", "    output AS (\n", "        SELECT facility_id, item_id, date_ist AS date_, EXTRACT(hour FROM updated_at_ist) AS hour_, \n", "        CASE \n", "            WHEN current_inventory > 0 THEN 1 \n", "            ELSE 0 \n", "        END AS is_available\n", "        FROM base\n", "        WHERE facility_id IN {facility_id_list} AND item_id IN {item_id_list}\n", "    )\n", "    \n", "    SELECT * FROM output \n", "    WHERE hour_ BETWEEN 4 AND {current_hour} - 1\n", "    \n", "\"\"\"\n", "avail_df = pd.read_sql_query(query, trino)"]}, {"cell_type": "markdown", "id": "39952ae2-76b7-4e98-a41d-f4767a83b15c", "metadata": {}, "source": ["#### Unavailable Item Facility Combinations"]}, {"cell_type": "code", "execution_count": null, "id": "c8fe18cb-7d61-47aa-9f75-b1f3cfc83685", "metadata": {}, "outputs": [], "source": ["unavailable_df = pd.merge(\n", "    base_df,\n", "    avail_df.drop(columns={\"date_\"}),\n", "    on=[\"facility_id\", \"item_id\", \"hour_\"],\n", "    how=\"inner\",\n", ")\n", "\n", "unavailable_df = unavailable_df[\n", "    (unavailable_df[\"is_available\"] == 0) & (unavailable_df[\"hour_\"] == current_hour - 1)\n", "].reset_index()\n", "\n", "unavailable_df = unavailable_df.drop(columns={\"index\", \"is_available\", \"hour_\"})\n", "\n", "unavailable_df[\"flag\"] = 1\n", "\n", "unavailable_df = unavailable_df.merge(hour_df, on=[\"flag\"], how=\"left\").drop(columns={\"flag\"})\n", "\n", "unavailable_df = unavailable_df.merge(\n", "    avail_df.drop(columns={\"date_\"}), on=[\"facility_id\", \"item_id\", \"hour_\"], how=\"left\"\n", ")\n", "\n", "unavailable_df[\"is_available\"] = unavailable_df[\"is_available\"].fillna(0).astype(int)\n", "\n", "unavailable_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6ea18042-0337-487c-be58-123f5cc2189b", "metadata": {}, "outputs": [], "source": ["del avail_df, base_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "ce91c919-89be-4501-8c5f-29aceb25e3df", "metadata": {}, "source": ["#### OOS Outlet Item List"]}, {"cell_type": "code", "execution_count": null, "id": "821f8b1e-ea0f-4770-b700-684e20f1a1bf", "metadata": {}, "outputs": [], "source": ["oos_outlet_list = tuple(list(unavailable_df[\"outlet_id\"].unique()))\n", "\n", "oos_item_list = tuple(list(unavailable_df[\"item_id\"].unique()))\n", "\n", "len(oos_outlet_list), len(oos_item_list)"]}, {"cell_type": "markdown", "id": "fd38a476-100c-4133-990b-4f4b46068e34", "metadata": {"tags": []}, "source": ["# ARS Transfers Data"]}, {"cell_type": "code", "execution_count": null, "id": "524de55e-0a6d-4b17-94c2-7da250f4b84b", "metadata": {}, "outputs": [], "source": ["ars_sql = f\"\"\"\n", "    WITH run_ids AS (\n", "        SELECT * FROM (\n", "            SELECT jr.facility_id AS be_facility_id, (jr.run_id) AS run_id, jr.started_at + interval '330' minute,\n", "            CASE\n", "                WHEN EXTRACT(hour FROM started_at + interval '330' minute) BETWEEN 18 AND 23 THEN DATE(started_at + interval '330' minute + interval '1' day)\n", "                ELSE date(started_at + interval '330' minute)\n", "            END AS date_of_run,\n", "            CASE\n", "                WHEN EXTRACT(HOUR FROM jr.started_at + INTERVAL '330' minute) IN (6, 7, 8, 9, 10, 11, 12, 13, 14) THEN 'slot B'\n", "                ELSE 'slot A'\n", "            END AS slot\n", "            FROM ars.job_run jr\n", "            WHERE jr.started_at + interval '330' minute BETWEEN localtimestamp - interval '15' day AND localtimestamp\n", "            AND json_extract_scalar(simulation_params, '$.mode') IN ('milk')\n", "        )\n", "        WHERE date_of_run < DATE('{today_date}') + interval '1' day\n", "    ),\n", "    \n", "    inventory AS (\n", "        SELECT DISTINCT outlet_id, item_id, run_id, final_inventory \n", "        FROM ars.outlet_item_universe \n", "        WHERE (insert_ds_ist) >= cast(DATE('{today_date}') - interval '2' day AS varchar) AND (insert_ds_ist) <= cast(DATE('{today_date}') AS varchar) \n", "        AND run_id IN (SELECT DISTINCT run_id FROM run_ids)\n", "        AND item_id IN {oos_item_list}\n", "    ),\n", "    \n", "    transfers AS (\n", "        SELECT fcs.from_outlet_id AS be_outlet_id, fcs.to_outlet_id AS outlet_id, fcs.run_id AS run_id, fcs.item_id, \n", "        MAX(inv.final_inventory) AS backend_inventory,\n", "        SUM(fcs.sto_quantity) AS sto_quantity,\n", "        SUM(fcs.initial_demand_qty) AS demand_quantity\n", "        FROM ars.frontend_cycle_sto_quantity fcs\n", "        LEFT JOIN inventory inv ON inv.outlet_id = fcs.from_outlet_id AND inv.item_id = fcs.item_id AND cast(fcs.run_id AS varchar) = cast(inv.run_id AS varchar)\n", "        LEFT JOIN inventory finv ON finv.outlet_id = fcs.to_outlet_id AND finv.item_id = fcs.item_id AND cast(fcs.run_id AS varchar) = cast(finv.run_id AS varchar)\n", "        WHERE fcs.run_id IN (SELECT DISTINCT run_id FROM run_ids)\n", "        AND partition_field >= cast(DATE('{today_date}') - interval '2' day AS varchar)\n", "        AND fcs.item_id IN {oos_item_list}\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "    \n", "    merged_base AS (\n", "        SELECT a.*, b.outlet_id, item_id, backend_inventory AS be_inv, demand_quantity AS demand_qty, sto_quantity AS sto_qty\n", "        FROM run_ids a\n", "        JOIN transfers b ON a.run_id = b.run_id\n", "    )\n", "    \n", "    SELECT * FROM merged_base \n", "    WHERE date_of_run = DATE('{today_date}')\n", "    \"\"\"\n", "ars_df = pd.read_sql_query(ars_sql, trino)\n", "\n", "ars_df = (\n", "    ars_df.groupby([\"be_facility_id\", \"outlet_id\", \"slot\", \"item_id\", \"date_of_run\"])\n", "    .agg({\"be_inv\": \"sum\", \"demand_qty\": \"sum\", \"sto_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "ars_backend_list = list(ars_df[\"be_facility_id\"].unique())\n", "\n", "print(\"Total ARS..\", ars_df.shape)\n", "\n", "morning_ars_df = ars_df[ars_df[\"slot\"] == \"slot A\"].reset_index().drop(columns={\"index\", \"slot\"})\n", "\n", "morning_ars_df = morning_ars_df.rename(\n", "    columns={\"be_inv\": \"m_be_inv\", \"demand_qty\": \"m_demand_qty\", \"sto_qty\": \"m_sto_qty\"}\n", ")\n", "\n", "morning_be_inv_df = (\n", "    morning_ars_df[[\"be_facility_id\", \"item_id\", \"m_be_inv\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "morning_be_inv_df = (\n", "    morning_be_inv_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"m_be_inv\": \"sum\"}).reset_index()\n", ")\n", "\n", "print(\"Morning ARS..\", morning_ars_df.shape)\n", "\n", "evening_ars_df = ars_df[ars_df[\"slot\"] == \"slot B\"].reset_index().drop(columns={\"index\", \"slot\"})\n", "evening_ars_df = evening_ars_df.rename(\n", "    columns={\"be_inv\": \"e_be_inv\", \"demand_qty\": \"e_demand_qty\", \"sto_qty\": \"e_sto_qty\"}\n", ")\n", "\n", "evening_be_inv_df = (\n", "    evening_ars_df[[\"be_facility_id\", \"item_id\", \"e_be_inv\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "evening_be_inv_df = (\n", "    evening_be_inv_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"e_be_inv\": \"sum\"}).reset_index()\n", ")\n", "\n", "print(\"Evening ARS..\", evening_ars_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "77de576d-b625-4135-b63e-bd15ae0120ec", "metadata": {}, "outputs": [], "source": ["del ars_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "19e0a95f-bc1a-4e4e-882f-9e6eff6e4c80", "metadata": {"tags": []}, "source": ["# GRN Data"]}, {"cell_type": "code", "execution_count": null, "id": "56b0d38a-27c0-4c41-b26e-e3f7d8b85d0e", "metadata": {}, "outputs": [], "source": ["po_grn_sql = f\"\"\"\n", "    WITH po_base AS (\n", "        SELECT p.outlet_id, poi.item_id, (p.created_at + interval '330' minute) AS issue_date, \n", "        grn_time, quan AS grn_qty\n", "        FROM po.purchase_order p\n", "        LEFT JOIN po.po_schedule ps ON p.id = ps.po_id_id\n", "        INNER JOIN po.purchase_order_items poi ON p.id = poi.po_id\n", "        INNER JOIN retail.console_outlet o ON o.id = p.outlet_id and o.business_type_id = 7 and o.active = 1 AND o.lake_active_record\n", "        INNER JOIN po.purchase_order_status posa ON posa.po_id = p.id\n", "        INNER JOIN po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "        LEFT JOIN (\n", "            SELECT po_id, item_id, created_at + interval '330' minute AS grn_time, SUM(quantity) quan\n", "            FROM po.po_grn grn \n", "            WHERE insert_ds_ist >= cast(DATE('{today_date}') - interval '1' day as varchar) \n", "            GROUP BY 1,2,3\n", "        ) grn ON grn.item_id = poi.item_id AND grn.po_id = p.id\n", "        WHERE (posta.name NOT IN ('Cancelled', 'Rejected', 'Cancelled post Creation') OR (posta.name IN ('Expired'))) \n", "        AND issue_date BETWEEN DATE('{today_date}') - interval '1' day AND DATE('{today_date}') + interval '1' day\n", "        AND po_type_id <> 11\n", "        AND poi.item_id IN {oos_item_list}\n", "        AND p.outlet_id IN {oos_outlet_list}\n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "    \n", "    SELECT outlet_id, item_id, DATE(issue_date) AS date_, slot, grn_time, DATE(grn_time) AS grn_date, grn_qty\n", "    FROM (\n", "        SELECT outlet_id, item_id, \n", "        CASE\n", "            WHEN EXTRACT(hour FROM issue_date) BETWEEN 18 AND 23 THEN issue_date + interval '1' day\n", "            ELSE issue_date\n", "        END AS issue_date,\n", "        CASE\n", "            WHEN EXTRACT(hour FROM issue_date) BETWEEN 6 AND 12 THEN 'slot B' \n", "            ELSE 'slot A'\n", "        END AS slot,\n", "        grn_time, grn_qty\n", "        FROM po_base\n", "    )\n", "\"\"\"\n", "po_grn_df = pd.read_sql_query(po_grn_sql, trino)\n", "\n", "po_grn_df[\"date_\"] = pd.to_datetime(po_grn_df[\"date_\"])\n", "\n", "po_grn_df[\"delta\"] = (\n", "    pd.to_datetime(po_grn_df[\"grn_date\"]) - pd.to_datetime(po_grn_df[\"date_\"])\n", ").dt.days\n", "\n", "po_grn_df[\"slot\"] = np.where(po_grn_df[\"delta\"] > 0, \"slot A\", po_grn_df[\"slot\"])\n", "\n", "po_grn_df[\"date_\"] = po_grn_df[\"grn_date\"]\n", "\n", "po_grn_df = (\n", "    po_grn_df[po_grn_df[\"date_\"] == today_date][\n", "        [\"outlet_id\", \"item_id\", \"date_\", \"slot\", \"grn_time\", \"grn_qty\"]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "print(\"PO GRN..\", po_grn_df.shape)\n", "\n", "sto_grn_sql = f\"\"\"\n", "    WITH sto_details AS (\n", "        SELECT (created_at + interval '330' minute) AS created_time, sto_id, outlet_id AS sender_outlet_id, merchant_outlet_id AS receiver_outlet_id\n", "        FROM ims.ims_sto_details sd\n", "        WHERE created_at + interval '330' minute >= DATE('{today_date}') - interval '1' day\n", "    ),\n", "\n", "    sto_item_details AS (\n", "        SELECT created_time, si.sto_id, sender_outlet_id, receiver_outlet_id, si.item_id, reserved_quantity AS sto_quantity\n", "        FROM ims.ims_sto_item si \n", "        JOIN sto_details sd on sd.sto_id = si.sto_id\n", "        WHERE created_at+ interval '330' minute >= DATE('{today_date}') - interval '1' day\n", "        AND si.item_id IN {oos_item_list} AND receiver_outlet_id IN {oos_outlet_list}\n", "    ),\n", "    \n", "    invoice_details AS (\n", "        SELECT try_cast(grofers_order_id AS int) AS sto_id, invoice_id, pi.id, pi.outlet_id, (pos_timestamp + interval '330' minute) AS last_billed_time \n", "        FROM pos.pos_invoice pi\n", "        WHERE insert_ds_ist >= cast((DATE('{today_date}') - interval '1' day) AS varchar) \n", "        AND invoice_type_id IN (5,14,16) AND grofers_order_id != '' AND grofers_order_id IS NOT NULL\n", "    ),\n", "\n", "    invoice_item_details AS (\n", "        SELECT item_id, outlet_id, sto_id, SUM(quantity) AS billed_quantity\n", "        FROM pos.pos_invoice_product_details pd\n", "        JOIN (\n", "            SELECT DISTINCT item_id, upc \n", "            FROM rpc.product_product \n", "            WHERE id IN (SELECT MAX(id) AS id FROM rpc.product_product WHERE active = 1 AND approved = 1 GROUP BY upc)\n", "        ) rpp ON rpp.upc = pd.upc_id\n", "        JOIN invoice_details id on id.id = pd.invoice_id\n", "        WHERE insert_ds_ist >= cast((DATE('{today_date}') - interval '1' day) AS varchar)\n", "        AND item_id IN {oos_item_list}\n", "        GROUP BY 1,2,3\n", "    ),\n", "\n", "    grn_details AS (\n", "        SELECT ii.grn_id, ii.vendor_invoice_id, id.sto_id\n", "        FROM ims.ims_inward_invoice ii\n", "        JOIN invoice_details id ON id.invoice_id = ii.vendor_invoice_id\n", "        WHERE insert_ds_ist >= cast((DATE('{today_date}') - interval '1' day) AS varchar) AND source_type = 2\n", "    ),\n", "\n", "    grn_item_details AS (\n", "        SELECT item_id, outlet_id, sto_id, created_at + interval '330' minute AS inward_time, SUM(\"delta\") AS grn_qty\n", "        FROM ims.ims_inventory_stock_details sd\n", "        JOIN (\n", "            SELECT DISTINCT item_id, upc \n", "            FROM rpc.product_product\n", "            WHERE id IN (SELECT MAX(id) AS id FROM rpc.product_product WHERE active = 1 AND approved = 1 GROUP BY upc)\n", "        ) rpp ON rpp.upc = sd.upc_id\n", "        JOIN grn_details gd ON gd.grn_id = sd.grn_id\n", "        WHERE insert_ds_ist >= cast((DATE('{today_date}') - interval '1' day) AS varchar)\n", "        GROUP BY 1,2,3,4\n", "    ),\n", "        \n", "    final AS (\n", "        SELECT sid.created_time, \n", "        CASE \n", "            WHEN EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 18 AND 23 THEN DATE(CAST(sid.created_time AS timestamp) + INTERVAL '1' DAY ) \n", "            ELSE DATE(CAST(sid.created_time AS timestamp)) \n", "        END AS date_, \n", "        CASE \n", "            WHEN EXTRACT(HOUR FROM CAST(sid.created_time AS timestamp)) BETWEEN 6 AND 12 THEN 'slot B'  \n", "            ELSE 'slot A'\n", "        END AS slot, \n", "        sid.sto_id, sid.receiver_outlet_id AS outlet_id, sid.item_id, inward_time AS grn_time, grn_qty\n", "        FROM sto_item_details sid \n", "        LEFT JOIN invoice_item_details iid on iid.sto_id = sid.sto_id AND iid.item_id = sid.item_id\n", "        LEFT JOIN grn_item_details gid ON gid.sto_id = sid.sto_id AND gid.item_id = sid.item_id\n", "        JOIN retail.console_outlet co ON co.id = sid.sender_outlet_id AND co.business_type_id IN (19,20,21,1,12)\n", "        WHERE sid.item_id IN {oos_item_list}\n", "    )\n", "        \n", "    SELECT outlet_id, item_id, date_, slot, grn_time, DATE(grn_time) AS grn_date, SUM(grn_qty) AS grn_qty \n", "    FROM final\n", "    GROUP BY 1,2,3,4,5,6\n", "\"\"\"\n", "sto_grn_df = pd.read_sql_query(sto_grn_sql, trino)\n", "\n", "sto_grn_df[\"date_\"] = pd.to_datetime(sto_grn_df[\"date_\"])\n", "\n", "sto_grn_df[\"delta\"] = (\n", "    pd.to_datetime(sto_grn_df[\"grn_date\"]) - pd.to_datetime(sto_grn_df[\"date_\"])\n", ").dt.days\n", "\n", "sto_grn_df[\"slot\"] = np.where(sto_grn_df[\"delta\"] > 0, \"slot A\", sto_grn_df[\"slot\"])\n", "\n", "sto_grn_df[\"date_\"] = sto_grn_df[\"grn_date\"]\n", "\n", "sto_grn_df = (\n", "    sto_grn_df[sto_grn_df[\"date_\"] == today_date][\n", "        [\"outlet_id\", \"item_id\", \"date_\", \"slot\", \"grn_time\", \"grn_qty\"]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "print(\"STO GRN..\", sto_grn_df.shape)\n", "\n", "grn_df = pd.concat([po_grn_df, sto_grn_df]).reset_index().drop(columns={\"index\"})\n", "\n", "grn_df[\"grn_min\"] = pd.to_datetime(grn_df[\"grn_time\"]).dt.minute\n", "\n", "grn_df[\"grn_hour\"] = pd.to_datetime(grn_df[\"grn_time\"]).dt.hour\n", "\n", "grn_df[\"grn_hour\"] = np.where(grn_df[\"grn_min\"] > 40, grn_df[\"grn_hour\"] + 1, grn_df[\"grn_hour\"])\n", "\n", "grn_df = (\n", "    grn_df.groupby([\"outlet_id\", \"item_id\", \"date_\", \"slot\"])\n", "    .agg({\"grn_qty\": \"sum\", \"grn_hour\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "print(\"Total Store GRN..\", grn_df.shape)\n", "\n", "morning_grn_df = grn_df[grn_df[\"slot\"] == \"slot A\"].reset_index().drop(columns={\"index\", \"slot\"})\n", "morning_grn_df = morning_grn_df.rename(columns={\"grn_qty\": \"m_grn_qty\", \"grn_hour\": \"m_grn_hour\"})\n", "\n", "print(\"Morning Store GRN..\", morning_grn_df.shape)\n", "\n", "evening_grn_df = grn_df[grn_df[\"slot\"] == \"slot B\"].reset_index().drop(columns={\"index\", \"slot\"})\n", "evening_grn_df = evening_grn_df.rename(columns={\"grn_qty\": \"e_grn_qty\", \"grn_hour\": \"e_grn_hour\"})\n", "\n", "print(\"Evening Store GRN..\", evening_grn_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "20724a34-a7d1-407a-8469-f34b14b34cb1", "metadata": {}, "outputs": [], "source": ["del po_grn_df, sto_grn_df, grn_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "d46a1825-f58f-46b2-b49e-efdfb9693948", "metadata": {"tags": []}, "source": ["# Fill Rate"]}, {"cell_type": "code", "execution_count": null, "id": "9a4955db-a0c9-48b4-af63-418868b2bd32", "metadata": {}, "outputs": [], "source": ["fr_query = f\"\"\"\n", "    WITH wh_mapping AS ( \n", "        SELECT DISTINCT tag_value AS be_outlet_id, cb.facility_id AS backend_facility_id\n", "        FROM lake_rpc.item_outlet_tag_mapping tm\n", "        JOIN lake_retail.console_outlet cb ON cb.id = cast(tag_value as int) AND cb.active = 1\n", "        AND tm.active = 1 AND tm.tag_type_id = 8\n", "    ),\n", "\n", "    base AS (\n", "        SELECT DISTINCT p.po_number, poi.item_id AS item_id, p.outlet_id, DATE(expiry_date + interval '330' minute) AS grn_date, \n", "        DATE(issue_date + interval '330' minute) AS issue_date, units_ordered AS total_po, grn.quan AS total_grn, \n", "        CASE \n", "            WHEN EXTRACT(hour FROM max_grn_time) IN (0,1,2,3,4,5,6,7) THEN max_grn_time - interval '1' day \n", "            ELSE max_grn_time \n", "        END AS actual_grn_date, max_grn_time AS grn_time, DATE(expiry_date + interval '330' minute) AS expiry\n", "        FROM po.purchase_order p\n", "        LEFT JOIN po.po_schedule ps ON p.id = ps.po_id_id\n", "        INNER JOIN po.purchase_order_items poi ON p.id = poi.po_id AND DATE(p.issue_date + interval '330' minute) BETWEEN DATE('{today_date}') - interval '2' day AND DATE('{today_date}')\n", "        INNER JOIN retail.console_outlet o ON o.id = p.outlet_id AND o.business_type_id <> 7\n", "        INNER JOIN po.purchase_order_status posa ON posa.po_id = p.id\n", "        INNER JOIN po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "        LEFT JOIN (\n", "            SELECT po_id, item_id, SUM(quantity) AS quan, MAX(created_at + interval '330' minute) AS max_grn_time \n", "            FROM po.po_grn grn \n", "            WHERE grn.insert_ds_ist >= cast(DATE('{today_date}') - interval '2' day AS varchar)\n", "            GROUP BY 1,2\n", "        ) grn ON grn.item_id = poi.item_id AND grn.po_id = p.id\n", "        WHERE (posta.name NOT IN ('Cancelled', 'Rejected', 'Cancelled post Creation') OR (posta.name in ('Expired') AND grn.quan IS NOT NULL)) \n", "        AND p.outlet_id IN (SELECT DISTINCT cast(be_outlet_id AS int) FROM wh_mapping) AND p.po_type_id <> 11 \n", "        AND poi.item_id IN {oos_item_list}\n", "    ),\n", "\n", "    wh_base AS (\n", "        SELECT CASE WHEN DATE(actual_grn_date) IS NULL THEN date(grn_date) ELSE DATE(actual_grn_date) END AS date_,\n", "        outlet_id AS be_outlet_id, item_id, SUM(total_po) AS po, coalesce(SUM(total_grn),0) AS grn\n", "        FROM base \n", "        GROUP BY 1,2,3\n", "    ),\n", "\n", "    hp_pre_base AS (\n", "        SELECT distinct (po.created_at + interval '330' minute) AS po_created_at, po.purchase_order_number, po.is_multiple_grn_enabled, po.po_status, \n", "        po.vendor_id, ipom.outlet_id as warehouse_id, po.warehouse_code, (po.po_consideration_date + interval '330' minute) AS po_consideration_date, \n", "        poi.product_number AS hp_item_id, poi.product_name AS hp_product_name, pi.item_id AS item_id,\n", "        poi.quantity_ordered AS po_qty, (po.expected_delivery_date + interval '330' minute) AS expected_delivery_date, \n", "        pg.invoiced_quantity, pg.dock_rejected_quantity, \n", "        CASE \n", "            WHEN po.is_multiple_grn_enabled = 0 THEN coalesce(poi.quantity_delivered,0) \n", "            ELSE coalesce(pg.delivered_quantity,0) \n", "        END AS quantity_delivered, grn_created_at, pg.grn_quantity\n", "        FROM zomato.hp_wms.purchase_order po\n", "        INNER JOIN zomato.hp_wms.purchase_order_items poi on po.id = poi.purchase_order_id\n", "        INNER JOIN po.edi_integration_partner_item_mapping pi ON cast(pi.partner_item_id AS int) = poi.product_number AND pi.active = true\n", "        LEFT JOIN (\n", "            SELECT pg.grn_quantity, purchase_order_item_id, invoiced_quantity, id, delivered_quantity, dock_rejected_quantity, MAX(pg.created_at + interval '330' minute) AS grn_created_at \n", "            FROM zomato.hp_wms.po_grn_item pg\n", "            WHERE dt >= replace(CAST(DATE('{today_date}') - interval '2' day AS varchar), '-','')\n", "            GROUP BY 1,2,3,4,5,6\n", "        ) pg ON poi.id = pg.purchase_order_item_id  \n", "        JOIN zomato.hp_wms.po_grn_mapping m ON m.purchase_order_id = po.id AND m.dt >= replace(CAST(DATE('{today_date}') - interval '2' day AS varchar), '-','') AND grn_status NOT IN ('CANCELLED')\n", "        INNER JOIN po.edi_integration_partner_outlet_mapping ipom ON lower(ipom.partner_outlet_id) = lower(po.warehouse_code) AND ipom.active\n", "        WHERE po.dt >= replace(CAST(DATE('{today_date}') - interval '2' day AS varchar), '-','')\n", "        AND poi.dt >= replace(CAST(DATE('{today_date}') - interval '2' day AS varchar), '-','')\n", "        AND pi.item_id IN {oos_item_list}\n", "    ),\n", "\n", "    hp_base AS (\n", "        SELECT a.*\n", "        FROM (\n", "            SELECT DATE(po_consideration_date) AS cond_date, purchase_order_number, warehouse_id, warehouse_code, hp_item_id, item_id, DATE(grn_created_at) AS grn_date, \n", "            MAX(po_qty) AS po_quantity, SUM(dock_rejected_quantity) AS dock_rejected_qty, SUM(quantity_delivered) AS delivered_qty\n", "            FROM hp_pre_base fb \n", "            WHERE po_status in ('COMPLETED','APPROVED') AND po_consideration_date <= DATE('{today_date}') + interval '2' day\n", "            GROUP BY 1,2,3,4,5,6,7\n", "        ) a\n", "        WHERE cond_date BETWEEN DATE('{today_date}') - interval '2' day AND DATE('{today_date}') + interval '2' day\n", "    ),\n", "\n", "    final_hp AS (\n", "        SELECT CASE WHEN grn_date IS NULL THEN cond_date ELSE grn_date END AS date_, \n", "        warehouse_id AS be_outlet_id, item_id, po_quantity as po, coalesce(delivered_qty,0) AS grn\n", "        FROM hp_base\n", "        WHERE cond_date BETWEEN DATE('{today_date}') - interval '2' day AND DATE('{today_date}') + interval '2' day\n", "    ),\n", "\n", "    merged_base AS (\n", "        SELECT * FROM wh_base\n", "        UNION \n", "        SELECT * FROM final_hp\n", "    )\n", "    \n", "    SELECT a.*, b.facility_id AS be_facility_id\n", "    FROM merged_base a\n", "    JOIN retail.console_outlet b ON a.be_outlet_id = b.id AND b.active = 1\n", "    WHERE b.facility_id IN {be_facility_id_list}\n", "\"\"\"\n", "fr_df = pd.read_sql_query(fr_query, trino)\n", "\n", "fr_df[\"date_\"] = pd.to_datetime(fr_df[\"date_\"]) + timedelta(days=1)\n", "\n", "fr_df = fr_df[fr_df[\"date_\"] == today_date].reset_index().drop(columns={\"index\"})\n", "\n", "fr_df = (\n", "    fr_df[[\"be_facility_id\", \"item_id\", \"po\", \"grn\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "fr_df = fr_df.groupby([\"be_facility_id\", \"item_id\", \"po\"]).agg({\"grn\": \"sum\"}).reset_index()\n", "\n", "fr_df = fr_df.groupby([\"be_facility_id\", \"item_id\"]).agg({\"po\": \"sum\", \"grn\": \"sum\"}).reset_index()\n", "\n", "fr_df.shape"]}, {"cell_type": "markdown", "id": "7e00703c-a932-4090-b1eb-6424d7762dbf", "metadata": {}, "source": ["# Sales Data"]}, {"cell_type": "code", "execution_count": null, "id": "2c6dc676-3044-451e-a537-72db1323ddfe", "metadata": {}, "outputs": [], "source": ["sale_query = f\"\"\"\n", "    WITH item_mapping as (\n", "        SELECT DISTINCT ipr.product_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NULL THEN ipom_0.item_id \n", "            ELSE ipr.item_id \n", "        END AS item_id, \n", "        CASE \n", "            WHEN ipr.item_id IS NOT NULL THEN COALESCE(ipom.multiplier,1) \n", "            ELSE COALESCE(ipom_0.multiplier,1) \n", "        END AS multiplier\n", "        FROM rpc.item_product_mapping ipr \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom ON ipom.product_id = ipr.product_id AND ipr.item_id = ipom.item_id \n", "        LEFT JOIN dwh.dim_item_product_offer_mapping ipom_0 ON ipom_0.product_id = ipr.product_id\n", "    ),\n", "\n", "    sales AS (\n", "        SELECT (oid.cart_checkout_ts_ist) AS order_date, cl.name AS city, rco.facility_id, oid.product_id, im.item_id, oid.order_id, im.multiplier, ((oid.procured_quantity - oid.total_doorstep_return_quantity) * im.multiplier) AS sales_quantity \n", "        FROM dwh.fact_sales_order_item_details oid \n", "        JOIN item_mapping im on im.product_id = oid.product_id  \n", "        JOIN retail.console_outlet rco ON rco.id = oid.outlet_id AND business_type_id = 7 AND lake_active_record\n", "        LEFT JOIN retail.console_location cl ON cl.id = rco.tax_location_id \n", "        WHERE oid.cart_checkout_ts_ist BETWEEN DATE('{today_date}') - interval '16' day AND DATE('{today_date}') + interval '1' day\n", "        and oid.order_create_dt_ist BETWEEN DATE('{today_date}') - interval '16' day AND DATE('{today_date}') + interval '1' day\n", "        AND oid.is_internal_order = false  \n", "        AND (oid.order_type NOT LIKE '%%internal%%' OR oid.order_type IS NULL)  \n", "        AND oid.procured_quantity > 0 AND oid.order_current_status = 'DELIVERED'\n", "        AND oid.outlet_id IN {oos_outlet_list}\n", "    ),\n", "    \n", "    final_sales AS (\n", "        SELECT DATE(order_date) AS date_, EXTRACT(hour FROM order_date) AS hour_, city, facility_id, s.item_id, CAST(SUM(sales_quantity) AS int) AS quantity\n", "        FROM sales s\n", "        WHERE item_id IN {oos_item_list}\n", "        GROUP BY 1,2,3,4,5\n", "    )\n", "    \n", "    SELECT facility_id, item_id, date_, hour_, quantity \n", "    FROM final_sales\n", "    WHERE hour_ <= {current_hour} - 1\n", "    AND date_ <= DATE('{today_date}')\n", "    \"\"\"\n", "sales_base_df = pd.read_sql(sale_query, trino)\n", "\n", "sales_base_df[\"date_\"] = pd.to_datetime(sales_base_df[\"date_\"])\n", "\n", "sales_base_df.shape"]}, {"cell_type": "markdown", "id": "c3e0f850-1011-43e3-8a27-34ca14b24b43", "metadata": {}, "source": ["# Inventory Data"]}, {"cell_type": "markdown", "id": "a551b067-9586-4e4f-a287-91f52f97e479", "metadata": {}, "source": ["## Dump Data"]}, {"cell_type": "code", "execution_count": null, "id": "b2dec135-83ba-428b-876a-b11f7831c51d", "metadata": {}, "outputs": [], "source": ["dump_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT DATE(pos_timestamp + interval '330' minute) AS date_, EXTRACT(hour FROM pos_timestamp + interval '330' minute) AS hour_, outlet_id, item_id, SUM(il.\"delta\") AS dump_qty\n", "        FROM ims.ims_inventory_log il\n", "        JOIN (\n", "            SELECT DISTINCT item_id, variant_id\n", "            FROM rpc.product_product\n", "        ) rpp ON rpp.variant_id = il.variant_id\n", "        WHERE insert_ds_ist BETWEEN CAST(DATE('{today_date}') - interval '1' day AS varchar) AND CAST(DATE('{today_date}') + interval '1' day AS varchar)\n", "        AND inventory_update_type_id IN (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "        AND item_id IN {oos_item_list} AND outlet_id IN {oos_outlet_list}\n", "        GROUP BY 1,2,3,4\n", "    )\n", "    SELECT * FROM base\n", "    WHERE date_ = DATE('{today_date}')\n", "\"\"\"\n", "dump_base_df = pd.read_sql_query(dump_query, trino)\n", "dump_base_df.shape"]}, {"cell_type": "markdown", "id": "633fe644-6ebc-4284-87ee-5e892fc0e102", "metadata": {}, "source": ["## Leftover Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "8dda12bc-c973-4202-b92e-45e0165d2941", "metadata": {}, "outputs": [], "source": ["eod_query = f\"\"\"\n", "    WITH eod_inv_snap AS (\n", "        SELECT (ris.snapshot_datetime + interval '330' minute) AS created_at, EXTRACT(hour FROM ris.snapshot_datetime + interval '330' minute) AS created_at_hour, \n", "        item_id, ris.outlet_id, sum(ris.quantity) AS leftover_qty\n", "        FROM reports.reports_entity_inventory_snapshot ris\n", "        INNER JOIN (\n", "            SELECT item_id, upc\n", "            FROM rpc.product_product pp \n", "            WHERE active = 1 AND approved = 1 \n", "            GROUP BY 1,2\n", "        ) pp ON pp.upc = ris.upc_id\n", "        WHERE insert_ds_ist > CAST(DATE('{today_date}') - interval '2' day AS varchar)\n", "        AND ris.snapshot_datetime BETWEEN DATE('{today_date}') - interval '1' day AND DATE('{today_date}') + interval '1' day\n", "        AND ris.outlet_id IN {oos_outlet_list}\n", "        and pp.item_id in {oos_item_list}\n", "        GROUP BY 1,2,3,4\n", "    )\n", "    \n", "    SELECT * FROM (\n", "        SELECT DATE(a.created_at) AS date_, outlet_id, co.facility_id, item_id, SUM(leftover_qty) AS leftover_qty \n", "        FROM eod_inv_snap a\n", "        JOIN retail.console_outlet co ON co.id = outlet_id AND co.active = 1 AND co.business_type_id = 7 AND co.lake_active_record\n", "        GROUP BY 1,2,3,4\n", "    )\n", "    WHERE date_ = DATE('{today_date}') - interval '1' day\n", "\"\"\"\n", "eod_df = pd.read_sql_query(eod_query, trino)\n", "eod_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "f2ae7891-e030-4433-a0a2-c1bdf06a6f98", "metadata": {}, "outputs": [], "source": ["leftover_df = eod_df.copy()\n", "\n", "if current_hour >= 9:\n", "    leftover_check_hour = 9\n", "else:\n", "    leftover_check_hour = current_hour\n", "\n", "leftover_sales_df = (\n", "    sales_base_df[\n", "        (sales_base_df[\"hour_\"] < leftover_check_hour) & (sales_base_df[\"date_\"] == today_date)\n", "    ]\n", "    .groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "leftover_dump_df = (\n", "    dump_base_df[dump_base_df[\"hour_\"] <= leftover_check_hour]\n", "    .groupby([\"item_id\", \"outlet_id\"])\n", "    .agg({\"dump_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "leftover_df = leftover_df.merge(leftover_sales_df, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "leftover_df = leftover_df.merge(leftover_dump_df, on=[\"item_id\", \"outlet_id\"], how=\"left\")\n", "\n", "leftover_df = leftover_df.fillna(0)\n", "\n", "leftover_df[\"incorrect_dump_qty\"] = leftover_df[\"leftover_qty\"] - leftover_df[\"quantity\"]\n", "\n", "leftover_df[\"incorrect_dump_qty\"] = np.where(\n", "    leftover_df[\"incorrect_dump_qty\"] < 0, 0, leftover_df[\"incorrect_dump_qty\"]\n", ")\n", "\n", "leftover_df[\"incorrect_dump_qty\"] = leftover_df[\"dump_qty\"] - leftover_df[\"incorrect_dump_qty\"]\n", "\n", "leftover_df[\"incorrect_dump_qty\"] = np.where(\n", "    leftover_df[\"incorrect_dump_qty\"] < 0, 0, leftover_df[\"incorrect_dump_qty\"]\n", ")\n", "\n", "leftover_df = (\n", "    leftover_df[[\"outlet_id\", \"item_id\", \"incorrect_dump_qty\"]]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "dump_df = (\n", "    dump_base_df[dump_base_df[\"hour_\"] > 10]\n", "    .groupby([\"outlet_id\", \"item_id\"])\n", "    .agg({\"dump_qty\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"dump_qty\": \"incorrect_dump_qty\"})\n", ")\n", "\n", "dump_df = pd.concat([leftover_df, dump_df]).reset_index().drop(columns={\"index\"})\n", "\n", "dump_df = dump_df.groupby([\"outlet_id\", \"item_id\"]).agg({\"incorrect_dump_qty\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "8d5a0afc-8e41-4c4f-954f-92fc9c169dcc", "metadata": {}, "outputs": [], "source": ["del leftover_dump_df, leftover_sales_df, leftover_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "6e4b799e-fb7d-4dc3-9eac-aac5063c9a4b", "metadata": {}, "source": ["## Other Missing Inventory Data"]}, {"cell_type": "code", "execution_count": null, "id": "0b9656b9-ba7b-4f88-bef9-24cc4a456b74", "metadata": {}, "outputs": [], "source": ["inv_query = f\"\"\"\n", "    WITH base AS (\n", "        SELECT DATE(pos_timestamp + interval '330' minute) AS date_, EXTRACT(hour FROM pos_timestamp + interval '330' minute) AS hour_, outlet_id, item_id, SUM(il.\"delta\") AS neg_inv_qty\n", "        FROM ims.ims_inventory_log il\n", "        JOIN (\n", "            SELECT DISTINCT item_id, variant_id\n", "            FROM rpc.product_product\n", "        ) rpp ON rpp.variant_id = il.variant_id\n", "        WHERE insert_ds_ist BETWEEN CAST(DATE('{today_date}') - interval '1' day AS varchar) AND CAST(DATE('{today_date}') + interval '1' day AS varchar)\n", "        AND inventory_update_type_id IN (SELECT DISTINCT id FROM ims.ims_inventory_update_type WHERE inventory_operation IN ('-') AND id NOT IN (11,12,13,64,87,88,89,7,33,9,34,63,67,49,19,62,117,57,75,2,40))\n", "        AND item_id IN {oos_item_list} AND outlet_id IN {oos_outlet_list}\n", "        GROUP BY 1,2,3,4\n", "    )\n", "    SELECT * FROM base\n", "    WHERE date_ = DATE('{today_date}')\n", "\"\"\"\n", "neg_inv_df = pd.read_sql_query(inv_query, trino)\n", "\n", "neg_inv_df = (\n", "    neg_inv_df[(neg_inv_df[\"hour_\"] < current_hour)]\n", "    .groupby([\"item_id\", \"outlet_id\"])\n", "    .agg({\"neg_inv_qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "neg_inv_df.shape"]}, {"cell_type": "markdown", "id": "d5ea0d5b-d6c3-4803-993a-ec14e5d37713", "metadata": {}, "source": ["# 2nd Rep Data"]}, {"cell_type": "code", "execution_count": null, "id": "ccac1b37-f416-4358-8bde-217945670299", "metadata": {}, "outputs": [], "source": ["rep_query = f\"\"\"\n", "    WITH base_pre AS (\n", "        SELECT facility_id, item_id, EXTRACT(hour FROM updated_at) AS hour_, date_ist, updated_at, min_qty\n", "        FROM supply_etls.facility_item_min_max_quantity_log\n", "        WHERE date_ist BETWEEN DATE('{today_date}') - interval '2' day AND DATE('{today_date}')\n", "    ),\n", "    \n", "    base AS (\n", "        SELECT a.date_ist AS date_, a.facility_id, a.item_id, a.updated_at AS time, SUM(a.min_qty) AS min_qty\n", "        FROM base_pre a\n", "        JOIN (\n", "            SELECT facility_id, item_id, date_ist, MAX(updated_at) AS updated_at\n", "            FROM base_pre \n", "            WHERE hour_ BETWEEN 9 AND 12\n", "            GROUP BY 1,2,3\n", "        ) b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ist = b.date_ist AND a.updated_at = b.updated_at\n", "        WHERE a.item_id IN {oos_item_list}\n", "        GROUP BY 1,2,3,4\n", "    )\n", "    \n", "    SELECT * FROM base\n", "    WHERE date_ BETWEEN DATE('{today_date}') - interval '1' day AND DATE('{today_date}')\n", "\"\"\"\n", "rep_df = pd.read_sql_query(rep_query, trino)\n", "\n", "rep_df[\"is_rep\"] = 1\n", "\n", "rep_df = (\n", "    rep_df.groupby(\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"date_\",\n", "        ]\n", "    )\n", "    .agg({\"is_rep\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "if current_hour < 11:\n", "    rep_df = (\n", "        rep_df[rep_df[\"date_\"] < today_date][[\"facility_id\", \"item_id\", \"is_rep\"]]\n", "        .drop_duplicates()\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "else:\n", "    rep_df = (\n", "        rep_df[rep_df[\"date_\"] == today_date][[\"facility_id\", \"item_id\", \"is_rep\"]]\n", "        .drop_duplicates()\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "rep_df.shape"]}, {"cell_type": "markdown", "id": "a453cbba-a3da-476d-9a5a-642cfb1c1807", "metadata": {"tags": []}, "source": ["# Calculation"]}, {"cell_type": "markdown", "id": "4a17c81b-24bf-4682-b719-4bab56e8e325", "metadata": {}, "source": ["## Base DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "71039c45-5519-4d28-830e-35cae46dab70", "metadata": {}, "outputs": [], "source": ["tot_sales_df = (\n", "    sales_base_df[(sales_base_df[\"date_\"] == today_date) & (sales_base_df[\"hour_\"] >= 5)]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "base_df = pd.merge(unavailable_df, rep_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "print(base_df.shape)\n", "\n", "base_df[\"is_rep\"] = base_df[\"is_rep\"].fillna(0).astype(int)\n", "\n", "base_df = base_df.merge(\n", "    morning_ars_df.drop(columns={\"date_of_run\", \"m_be_inv\"}),\n", "    on=[\"item_id\", \"outlet_id\", \"be_facility_id\"],\n", "    how=\"left\",\n", ")\n", "print(base_df.shape)\n", "\n", "base_df = base_df.merge(morning_be_inv_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "print(base_df.shape)\n", "\n", "base_df = base_df.merge(\n", "    morning_grn_df.drop(columns={\"date_\"}), on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")\n", "print(base_df.shape)\n", "\n", "base_df = base_df.merge(\n", "    evening_ars_df.drop(columns={\"date_of_run\", \"e_be_inv\"}),\n", "    on=[\"item_id\", \"outlet_id\", \"be_facility_id\"],\n", "    how=\"left\",\n", ")\n", "print(base_df.shape)\n", "\n", "base_df = base_df.merge(evening_be_inv_df, on=[\"be_facility_id\", \"item_id\"], how=\"left\")\n", "print(base_df.shape)\n", "\n", "base_df = base_df.merge(\n", "    evening_grn_df.drop(columns={\"date_\"}), on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")\n", "print(base_df.shape)\n", "\n", "base_df = base_df.merge(tot_sales_df, on=[\"item_id\", \"facility_id\"], how=\"left\").rename(\n", "    columns={\"quantity\": \"sold_qty\"}\n", ")\n", "print(base_df.shape)\n", "\n", "base_df[\"sold_qty\"] = base_df[\"sold_qty\"].fillna(0).astype(int)\n", "\n", "base_df[[\"m_grn_qty\", \"e_grn_qty\"]] = base_df[[\"m_grn_qty\", \"e_grn_qty\"]].fillna(0).astype(int)\n", "\n", "base_df[[\"m_grn_hour\", \"e_grn_hour\"]] = (\n", "    base_df[[\"m_grn_hour\", \"e_grn_hour\"]].fillna(-66).astype(int)\n", ")\n", "\n", "base_df[[\"m_be_inv\", \"e_be_inv\"]] = base_df[[\"m_be_inv\", \"e_be_inv\"]].fillna(-66).astype(int)\n", "\n", "base_df[[\"m_demand_qty\", \"e_demand_qty\"]] = (\n", "    base_df[[\"m_demand_qty\", \"e_demand_qty\"]].fillna(-66).astype(int)\n", ")\n", "\n", "base_df[[\"m_sto_qty\", \"e_sto_qty\"]] = base_df[[\"m_sto_qty\", \"e_sto_qty\"]].fillna(-66).astype(int)\n", "\n", "base_df[\"m_sto_qty\"] = np.where(\n", "    base_df[\"be_facility_id\"].isin(ars_backend_list),\n", "    base_df[\"m_sto_qty\"],\n", "    base_df[\"m_grn_qty\"],\n", ")\n", "\n", "base_df[\"e_sto_qty\"] = np.where(\n", "    base_df[\"be_facility_id\"].isin(ars_backend_list),\n", "    base_df[\"e_sto_qty\"],\n", "    base_df[\"e_grn_qty\"],\n", ")\n", "\n", "base_df[\"m_disc_qty\"] = np.where(\n", "    base_df[\"m_sto_qty\"] - base_df[\"m_grn_qty\"] < 0,\n", "    0,\n", "    base_df[\"m_sto_qty\"] - base_df[\"m_grn_qty\"],\n", ")\n", "\n", "base_df[\"e_disc_qty\"] = np.where(\n", "    base_df[\"e_sto_qty\"] - base_df[\"e_grn_qty\"] < 0,\n", "    0,\n", "    base_df[\"e_sto_qty\"] - base_df[\"e_grn_qty\"],\n", ")\n", "\n", "base_df = base_df.merge(dump_df, on=[\"item_id\", \"outlet_id\"], how=\"left\")\n", "print(base_df.shape)\n", "\n", "base_df = base_df.merge(neg_inv_df, on=[\"item_id\", \"outlet_id\"], how=\"left\")\n", "print(base_df.shape)\n", "\n", "base_df[[\"incorrect_dump_qty\", \"neg_inv_qty\"]] = (\n", "    base_df[[\"incorrect_dump_qty\", \"neg_inv_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "base_df = base_df.merge(fr_df, on=[\"item_id\", \"be_facility_id\"], how=\"left\")\n", "print(base_df.shape)\n", "\n", "base_df[\"po\"] = np.where(\n", "    base_df[\"be_facility_id\"].isin(ars_backend_list),\n", "    base_df[\"po\"],\n", "    base_df[\"m_sto_qty\"],\n", ")\n", "\n", "base_df[\"grn\"] = np.where(\n", "    base_df[\"be_facility_id\"].isin(ars_backend_list),\n", "    base_df[\"grn\"],\n", "    base_df[\"m_grn_qty\"],\n", ")\n", "\n", "base_df[\"fr\"] = base_df[\"grn\"] / base_df[\"po\"]\n", "\n", "base_df[\"fr\"] = base_df[\"fr\"].fillna(0)\n", "\n", "base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "06bcc7a0-c739-452d-8c33-d3c528063c89", "metadata": {}, "outputs": [], "source": ["del (\n", "    rep_df,\n", "    morning_ars_df,\n", "    morning_be_inv_df,\n", "    morning_grn_df,\n", "    evening_ars_df,\n", "    evening_be_inv_df,\n", "    evening_grn_df,\n", "    tot_sales_df,\n", "    dump_df,\n", "    neg_inv_df,\n", ")\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "f16987bc-559f-41e0-80a6-d44d861b43bb", "metadata": {"tags": []}, "source": ["## ARS & Supply Issues"]}, {"cell_type": "markdown", "id": "22bf3acd-b501-440e-98e4-c25bfd46ddeb", "metadata": {"tags": []}, "source": ["### Morning Check"]}, {"cell_type": "code", "execution_count": null, "id": "e574faaf-670b-41c1-9420-3f84aa3fbfd8", "metadata": {}, "outputs": [], "source": ["calc_df = base_df[base_df[\"hour_\"] < 16].reset_index().drop(columns={\"index\"})\n", "\n", "calc_df = calc_df.drop(\n", "    columns={\n", "        \"e_demand_qty\",\n", "        \"e_sto_qty\",\n", "        \"e_be_inv\",\n", "        \"e_grn_qty\",\n", "        \"e_grn_hour\",\n", "        \"e_disc_qty\",\n", "        \"incorrect_dump_qty\",\n", "        \"neg_inv_qty\",\n", "    }\n", ")\n", "\n", "calc_df[\"m_be_inv_flag\"] = np.where(\n", "    (calc_df[\"m_be_inv\"] <= 0) & (calc_df[\"be_facility_id\"].isin(ars_backend_list)),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"m_demand_flag\"] = np.where(\n", "    (calc_df[\"m_be_inv_flag\"] == 0)\n", "    & (calc_df[\"m_demand_qty\"] < 0)\n", "    & (calc_df[\"be_facility_id\"].isin(ars_backend_list)),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"m_grn_flag\"] = np.where(\n", "    ((calc_df[\"m_grn_hour\"] < 0) | (calc_df[\"m_grn_hour\"] > calc_df[\"hour_\"]))\n", "    & (calc_df[\"m_sto_qty\"] > 0),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"m_transfer_flag\"] = np.where(\n", "    (calc_df[\"m_be_inv_flag\"] == 0)\n", "    & (calc_df[\"m_demand_flag\"] == 0)\n", "    & (calc_df[\"m_sto_qty\"] <= 0.8 * (calc_df[\"m_demand_qty\"]))\n", "    & (calc_df[\"be_facility_id\"].isin(ars_backend_list)),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"m_disc_flag\"] = np.where(\n", "    (calc_df[\"m_sto_qty\"] > 0)\n", "    & (calc_df[\"m_grn_flag\"] == 0)\n", "    & (calc_df[\"m_disc_qty\"] >= 0.15 * calc_df[\"m_sto_qty\"]),\n", "    1,\n", "    0,\n", ")\n", "\n", "morning_calc_df = calc_df.copy()\n", "\n", "morning_calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "70edb8c2-df97-42f3-8fe7-eddd2a48f263", "metadata": {}, "outputs": [], "source": ["del calc_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "ff709152-9ec0-4f76-bcf8-a3c94c3a31b5", "metadata": {"tags": []}, "source": ["### Evening Check"]}, {"cell_type": "code", "execution_count": null, "id": "6dc462f8-918f-421b-8fef-dca483b01415", "metadata": {}, "outputs": [], "source": ["calc_df = base_df[base_df[\"hour_\"] >= 16].reset_index().drop(columns={\"index\"})\n", "\n", "calc_df = calc_df.drop(\n", "    columns={\n", "        \"m_demand_qty\",\n", "        \"m_sto_qty\",\n", "        \"m_be_inv\",\n", "        \"m_grn_qty\",\n", "        \"m_grn_hour\",\n", "        \"m_disc_qty\",\n", "        \"incorrect_dump_qty\",\n", "        \"neg_inv_qty\",\n", "    }\n", ")\n", "\n", "calc_df[\"e_be_inv_flag\"] = np.where(\n", "    (calc_df[\"e_be_inv\"] <= 0)\n", "    & (calc_df[\"is_rep\"] > 0)\n", "    & (calc_df[\"be_facility_id\"].isin(ars_backend_list)),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"e_demand_flag\"] = np.where(\n", "    (calc_df[\"e_be_inv_flag\"] == 0)\n", "    & (calc_df[\"is_rep\"] > 0)\n", "    & (calc_df[\"e_demand_qty\"] < 0)\n", "    & (calc_df[\"be_facility_id\"].isin(ars_backend_list)),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"e_grn_flag\"] = np.where(\n", "    (\n", "        (calc_df[\"e_grn_hour\"] < 0)\n", "        | ((calc_df[\"hour_\"] > 16) & (calc_df[\"e_grn_hour\"] > calc_df[\"hour_\"]))\n", "    )\n", "    & (calc_df[\"is_rep\"] > 0)\n", "    & (calc_df[\"e_sto_qty\"] > 0),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"e_transfer_flag\"] = np.where(\n", "    (calc_df[\"e_be_inv_flag\"] == 0)\n", "    & (calc_df[\"e_demand_flag\"] == 0)\n", "    & (calc_df[\"e_sto_qty\"] <= 0)\n", "    & (calc_df[\"be_facility_id\"].isin(ars_backend_list))\n", "    & (calc_df[\"is_rep\"] > 0),\n", "    1,\n", "    0,\n", ")\n", "\n", "calc_df[\"e_disc_flag\"] = np.where(\n", "    (calc_df[\"e_sto_qty\"] > 0)\n", "    & (calc_df[\"e_grn_flag\"] == 0)\n", "    & (calc_df[\"e_disc_qty\"] >= 0.15 * calc_df[\"e_sto_qty\"]),\n", "    1,\n", "    0,\n", ")\n", "\n", "evening_calc_df = calc_df.copy()\n", "\n", "evening_calc_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "4413521f-894d-410f-9a7d-547023623540", "metadata": {}, "outputs": [], "source": ["del calc_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "fbdc7653-6688-41bf-8903-cf48b8835aca", "metadata": {"tags": []}, "source": ["### ARS Issues DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "c6d06028-1315-4431-9cd9-19f706271533", "metadata": {}, "outputs": [], "source": ["ars_flags = pd.merge(\n", "    unavailable_df,\n", "    morning_calc_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"hour_\",\n", "            \"m_be_inv_flag\",\n", "            \"m_demand_flag\",\n", "            \"m_grn_flag\",\n", "            \"m_transfer_flag\",\n", "            \"m_disc_flag\",\n", "        ]\n", "    ],\n", "    on=[\"facility_id\", \"item_id\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "ars_flags = ars_flags.merge(\n", "    evening_calc_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"hour_\",\n", "            \"e_be_inv_flag\",\n", "            \"e_demand_flag\",\n", "            \"e_grn_flag\",\n", "            \"e_transfer_flag\",\n", "            \"e_disc_flag\",\n", "        ]\n", "    ],\n", "    on=[\"facility_id\", \"item_id\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "ars_flags = ars_flags.fillna(0)\n", "\n", "ars_flags[\"be_inv_flag\"] = ars_flags[[\"m_be_inv_flag\", \"e_be_inv_flag\"]].max(axis=1).astype(int)\n", "\n", "ars_flags[\"demand_flag\"] = ars_flags[[\"m_demand_flag\", \"e_demand_flag\"]].max(axis=1).astype(int)\n", "\n", "ars_flags[\"grn_flag\"] = ars_flags[[\"m_grn_flag\", \"e_grn_flag\"]].max(axis=1).astype(int)\n", "\n", "ars_flags[\"transfer_flag\"] = (\n", "    ars_flags[[\"m_transfer_flag\", \"e_transfer_flag\"]].max(axis=1).astype(int)\n", ")\n", "\n", "ars_flags[\"disc_flag\"] = ars_flags[[\"m_disc_flag\", \"e_disc_flag\"]].max(axis=1).astype(int)\n", "\n", "ars_flags = ars_flags[\n", "    [\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"hour_\",\n", "        \"be_inv_flag\",\n", "        \"demand_flag\",\n", "        \"grn_flag\",\n", "        \"transfer_flag\",\n", "        \"disc_flag\",\n", "    ]\n", "]\n", "\n", "ars_flags.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7a0970ca-92b5-4d5b-b07f-aad2f5da69dd", "metadata": {}, "outputs": [], "source": ["del evening_calc_df, morning_calc_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "f9deb846-8bac-416c-9e1e-95e7f4762ff1", "metadata": {"tags": []}, "source": ["## Store Ops Issues"]}, {"cell_type": "code", "execution_count": null, "id": "f98a4454-0010-47d5-b57d-fdec5d00c7d7", "metadata": {}, "outputs": [], "source": ["base_df = base_df.merge(ars_flags, on=[\"outlet_id\", \"item_id\", \"hour_\"], how=\"left\")\n", "\n", "base_df[\"dump_flag\"] = np.where(base_df[\"incorrect_dump_qty\"] > 0.1 * (base_df[\"sold_qty\"]), 1, 0)\n", "\n", "base_df[\"neg_inv_flag\"] = np.where(base_df[\"neg_inv_qty\"] > 0.1 * (base_df[\"sold_qty\"]), 1, 0)\n", "\n", "base_df.head(1)"]}, {"cell_type": "markdown", "id": "1d4313f2-33e0-409c-bbd4-6b14afdbdc94", "metadata": {}, "source": ["## Sales Spike"]}, {"cell_type": "code", "execution_count": null, "id": "3e5d55ad-76dd-405f-960d-c65af6bb05c2", "metadata": {}, "outputs": [], "source": ["def percentile50(x):\n", "    return np.percentile(x, 50)\n", "\n", "\n", "def percentile75(x):\n", "    return np.percentile(x, 75)"]}, {"cell_type": "code", "execution_count": null, "id": "2b866f98-2a1c-4c19-83fa-8b25540239d0", "metadata": {}, "outputs": [], "source": ["def dow_deviation(df):\n", "    temp_df = (\n", "        df[(df[\"dow\"] == current_dow) & (df[\"date_\"] < today_date)]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "    temp_df[\"dow_rank\"] = temp_df.groupby([\"facility_id\", \"item_id\", \"dow\"])[\"date_\"].rank(\n", "        method=\"dense\", ascending=False\n", "    )\n", "\n", "    max_rank = (\n", "        temp_df.groupby([\"facility_id\", \"item_id\", \"dow\"])\n", "        .agg({\"dow_rank\": \"max\"})\n", "        .reset_index()\n", "        .rename(columns={\"dow_rank\": \"max_rank\"})\n", "    )\n", "\n", "    temp_df = temp_df.merge(max_rank, on=[\"facility_id\", \"item_id\", \"dow\"], how=\"left\")\n", "\n", "    temp_df[\"weight\"] = np.where(\n", "        temp_df[\"max_rank\"] == 1, 1, np.where(temp_df[\"dow_rank\"] == 1, 0.5, 0.5)\n", "    )\n", "\n", "    temp_df[\"dow_sales\"] = temp_df[\"quantity\"] * temp_df[\"weight\"]\n", "\n", "    temp_df = (\n", "        temp_df.groupby([\"facility_id\", \"item_id\", \"dow\"]).agg({\"dow_sales\": \"sum\"}).reset_index()\n", "    )\n", "\n", "    return temp_df\n", "\n", "\n", "def weekday_deviation(df):\n", "    temp_df = (\n", "        df[(df[\"dow\"].isin({0, 1, 2, 3, 4})) & (df[\"date_\"] < today_date)]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "    temp_df[\"dow_rank\"] = temp_df.groupby([\"facility_id\", \"item_id\", \"dow\"])[\"date_\"].rank(\n", "        method=\"dense\", ascending=False\n", "    )\n", "\n", "    temp_df = temp_df[temp_df[\"dow_rank\"] == 1].reset_index().drop(columns={\"index\", \"dow_rank\"})\n", "\n", "    temp_df = (\n", "        temp_df.groupby([\"facility_id\", \"item_id\"])\n", "        .agg({\"quantity\": percentile50})\n", "        .reset_index()\n", "        .rename(columns={\"quantity\": \"weekday_sales\"})\n", "    )\n", "\n", "    return temp_df\n", "\n", "\n", "def weekend_deviation(df):\n", "    temp_df = (\n", "        df[(df[\"dow\"].isin({5, 6})) & (df[\"date_\"] < today_date)]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "    temp_df = (\n", "        temp_df.groupby([\"facility_id\", \"item_id\"])\n", "        .agg({\"quantity\": percentile75})\n", "        .reset_index()\n", "        .rename(columns={\"quantity\": \"weekend_sales\"})\n", "    )\n", "\n", "    return temp_df"]}, {"cell_type": "code", "execution_count": null, "id": "6b845819-8d9a-4e67-bd95-6d7249f64d5e", "metadata": {}, "outputs": [], "source": ["unavail_hour_df = (\n", "    unavailable_df[unavailable_df[\"hour_\"] >= 5]\n", "    .groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"hour_\": \"min\"})\n", "    .reset_index()\n", "    .rename(columns={\"hour_\": \"oos_hour\"})\n", ")\n", "\n", "check_base_df = pd.merge(sales_base_df, unavail_hour_df, on=[\"facility_id\", \"item_id\"], how=\"inner\")\n", "\n", "check_base_df = check_base_df.merge(\n", "    unavailable_df.drop(columns={\"city\", \"outlet_id\", \"be_facility_id\"}),\n", "    on=[\"facility_id\", \"item_id\", \"hour_\"],\n", "    how=\"left\",\n", ")\n", "\n", "check_base_df[\"is_available\"] = check_base_df[\"is_available\"].fillna(1).astype(int)\n", "\n", "check_base_df[\"is_available\"] = np.where(\n", "    (check_base_df[\"is_available\"] == 0) & (check_base_df[\"quantity\"] > 0),\n", "    1,\n", "    check_base_df[\"is_available\"],\n", ")\n", "\n", "check_base_df = (\n", "    check_base_df[check_base_df[\"is_available\"] == 1].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "check_base_df = (\n", "    check_base_df.groupby([\"facility_id\", \"item_id\", \"date_\"])\n", "    .agg({\"quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "check_base_df[\"dow\"] = pd.to_datetime(check_base_df[\"date_\"]).dt.dayofweek\n", "\n", "dow_df = dow_deviation(check_base_df)\n", "\n", "weekday_df = weekday_deviation(check_base_df)\n", "\n", "weekend_df = weekend_deviation(check_base_df)\n", "\n", "check_base_df = (\n", "    check_base_df[check_base_df[\"date_\"] == today_date].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "check_base_df = check_base_df.merge(\n", "    dow_df, on=[\"facility_id\", \"item_id\", \"dow\"], how=\"left\"\n", ").rename(columns={\"dow_sales\": \"dow_sales\"})\n", "\n", "check_base_df = check_base_df.merge(weekday_df, on=[\"facility_id\", \"item_id\"], how=\"left\").rename(\n", "    columns={\"weekday_sales\": \"weekday_sales\"}\n", ")\n", "\n", "check_base_df = check_base_df.merge(weekend_df, on=[\"facility_id\", \"item_id\"], how=\"left\").rename(\n", "    columns={\"weekend_sales\": \"weekend_sales\"}\n", ")\n", "\n", "check_base_df[[\"dow_sales\", \"weekday_sales\", \"weekend_sales\"]] = (\n", "    check_base_df[[\"dow_sales\", \"weekday_sales\", \"weekend_sales\"]].fillna(0).astype(int)\n", ")\n", "\n", "check_base_df[\"d1\"] = (\n", "    check_base_df[\"quantity\"]\n", "    / np.where(check_base_df[\"dow_sales\"] < 1, 1, check_base_df[\"dow_sales\"])\n", "    - 1\n", ")\n", "\n", "check_base_df[\"d2\"] = (\n", "    check_base_df[\"quantity\"]\n", "    / np.where(check_base_df[\"weekday_sales\"] < 1, 1, check_base_df[\"weekday_sales\"])\n", "    - 1\n", ")\n", "\n", "check_base_df[\"d3\"] = (\n", "    check_base_df[\"quantity\"]\n", "    / np.where(check_base_df[\"weekend_sales\"] < 1, 1, check_base_df[\"weekend_sales\"])\n", "    - 1\n", ")\n", "\n", "check_base_df = (\n", "    check_base_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"dow\",\n", "            \"quantity\",\n", "            \"dow_sales\",\n", "            \"weekday_sales\",\n", "            \"weekend_sales\",\n", "            \"d1\",\n", "            \"d2\",\n", "            \"d3\",\n", "        ]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "check_base_df = check_base_df.rename(columns={\"quantity\": \"sales_qty\"})"]}, {"cell_type": "code", "execution_count": null, "id": "aff4c9f4-9fd1-4aac-898a-98ef10f6cc13", "metadata": {}, "outputs": [], "source": ["sales_flags = unavailable_df[unavailable_df[\"hour_\"] >= 5].reset_index().drop(columns={\"index\"})\n", "\n", "sales_flags = sales_flags.merge(check_base_df, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "sales_flags = sales_flags.fillna(0)\n", "\n", "sales_flags[\"threshold\"] = np.where(\n", "    (sales_flags[\"hour_\"] < 9),\n", "    0.75,\n", "    np.where(\n", "        (sales_flags[\"hour_\"] >= 9) & (sales_flags[\"hour_\"] < 13),\n", "        0.5,\n", "        np.where(\n", "            (sales_flags[\"hour_\"] >= 13) & (sales_flags[\"hour_\"] < 18),\n", "            0.25,\n", "            np.where((sales_flags[\"hour_\"] >= 18) & (sales_flags[\"hour_\"] < 21), 0.15, 0.1),\n", "        ),\n", "    ),\n", ")\n", "\n", "sales_flags[\"f1\"] = np.where(sales_flags[\"d1\"] >= sales_flags[\"threshold\"], 1, 0)\n", "\n", "sales_flags[\"f2\"] = np.where(\n", "    (sales_flags[\"dow\"].isin({0, 1, 2, 3, 4})) & (sales_flags[\"d2\"] >= sales_flags[\"threshold\"]),\n", "    1,\n", "    0,\n", ")\n", "\n", "sales_flags[\"f3\"] = np.where(\n", "    (sales_flags[\"dow\"].isin({5, 6})) & (sales_flags[\"d3\"] >= sales_flags[\"threshold\"]),\n", "    1,\n", "    0,\n", ")\n", "\n", "sales_flags[\"sales_flag\"] = sales_flags[\"f1\"] + sales_flags[\"f2\"] + sales_flags[\"f3\"]\n", "\n", "sales_flags[\"sales_flag\"] = np.where(sales_flags[\"sales_flag\"] > 1, 1, 0)\n", "\n", "sales_flags = (\n", "    sales_flags[sales_flags[\"sales_flag\"] == 1][[\"facility_id\", \"item_id\", \"hour_\", \"sales_flag\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "sales_flags.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "6b2f4de7-2ffe-45fe-892f-1167b1bb7838", "metadata": {}, "outputs": [], "source": ["del weekend_df, weekday_df, dow_df, unavail_hour_df\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "ab82d53a-daf0-483c-ba04-3db8f14b14be", "metadata": {}, "source": ["# Final Base Data"]}, {"cell_type": "code", "execution_count": null, "id": "9f4a65f7-4c62-4734-b399-790048122a46", "metadata": {}, "outputs": [], "source": ["base_df = base_df.merge(\n", "    check_base_df.drop(columns={\"dow\"}), on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "base_df = base_df.merge(sales_flags, on=[\"facility_id\", \"item_id\", \"hour_\"], how=\"left\")\n", "\n", "base_df = base_df.fillna(0)\n", "\n", "base_df[\"sales_flag\"] = base_df[\"sales_flag\"].astype(int)\n", "\n", "base_df[\"no_po_flag\"] = np.where((base_df[\"po\"] <= 0), 1, 0)\n", "\n", "base_df[\"fill_flag\"] = np.where(\n", "    (base_df[\"no_po_flag\"] == 1),\n", "    0,\n", "    np.where(\n", "        (base_df[\"hour_\"] <= 12) & (base_df[\"fr\"] <= 0.5),\n", "        1,\n", "        np.where(\n", "            (base_df[\"hour_\"] > 12) & (base_df[\"hour_\"] <= 18) & (base_df[\"fr\"] <= 0.75),\n", "            1,\n", "            np.where((base_df[\"hour_\"] > 18) & (base_df[\"fr\"] <= 0.9), 1, 0),\n", "        ),\n", "    ),\n", ")\n", "\n", "base_df[\"be_inv_flag\"] = np.where(\n", "    (base_df[\"fill_flag\"] == 1) | (base_df[\"no_po_flag\"] == 1),\n", "    0,\n", "    base_df[\"be_inv_flag\"],\n", ")\n", "\n", "base_df[\"all_flag\"] = (\n", "    base_df[\"sales_flag\"]\n", "    + base_df[\"be_inv_flag\"]\n", "    + base_df[\"demand_flag\"]\n", "    + base_df[\"grn_flag\"]\n", "    + base_df[\"transfer_flag\"]\n", "    + base_df[\"dump_flag\"]\n", "    + base_df[\"disc_flag\"]\n", "    + base_df[\"neg_inv_flag\"]\n", "    + base_df[\"fill_flag\"]\n", "    + base_df[\"no_po_flag\"]\n", ")\n", "\n", "base_df[\"forecast_flag\"] = np.where((base_df[\"all_flag\"] == 0), 1, 0)\n", "\n", "base_df[\"manual_flag\"] = 0\n", "\n", "base_df = base_df.drop(columns={\"all_flag\"})\n", "\n", "base_df = base_df[base_df[\"hour_\"] == current_hour - 1].reset_index().drop(columns={\"index\"})\n", "\n", "base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "bed41091-f9de-4399-bb35-dcef7209fbc1", "metadata": {}, "outputs": [], "source": ["del check_base_df, sales_flags\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "a45c5af2-8487-444a-b503-7e796cb60147", "metadata": {}, "outputs": [], "source": ["duplicate_check = (\n", "    base_df.groupby([\"item_id\", \"facility_id\", \"hour_\"]).agg({\"city\": \"count\"}).reset_index()\n", ")\n", "duplicate_check[duplicate_check[\"city\"] > 1].shape"]}, {"cell_type": "markdown", "id": "35355218-942a-4bd6-b4ff-4d2cdf67e401", "metadata": {}, "source": ["# Decision Tree Calculations"]}, {"cell_type": "markdown", "id": "0465df91-1974-4d98-b017-3953ec96dedb", "metadata": {}, "source": ["### Weights Data"]}, {"cell_type": "code", "execution_count": null, "id": "ec3b1f53-0f56-4f3f-bed5-002762587284", "metadata": {}, "outputs": [], "source": ["trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "city_item_wt_query = f\"\"\"\n", "    SELECT DISTINCT city, item_id, CAST(weights AS DOUBLE) AS iw \n", "    FROM supply_etls.city_item_weights\n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM supply_etls.city_item_weights WHERE DATE(updated_at) <= DATE('{today_date}'))\n", "    AND item_id IN {item_id_list}\n", "\"\"\"\n", "city_item_wt_df = pd.read_sql_query(city_item_wt_query, trino)\n", "\n", "city_item_df = city_item_df.merge(city_item_wt_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "min_df = (\n", "    city_item_df.groupby([\"city\"]).agg({\"iw\": \"min\"}).reset_index().rename(columns={\"iw\": \"min_iw\"})\n", ")\n", "\n", "city_item_df = city_item_df.merge(min_df, on=[\"city\"], how=\"left\")\n", "\n", "city_item_df[\"iw\"] = np.where(city_item_df[\"iw\"].isna(), city_item_df[\"min_iw\"], city_item_df[\"iw\"])\n", "\n", "city_item_tot_df = (\n", "    city_item_df.groupby([\"city\"]).agg({\"iw\": \"sum\"}).reset_index().rename(columns={\"iw\": \"tot_iw\"})\n", ")\n", "\n", "city_item_wt_df = pd.merge(city_item_df, city_item_tot_df, on=[\"city\"], how=\"left\")\n", "\n", "city_item_wt_df[\"iw\"] = city_item_wt_df[\"iw\"] / city_item_wt_df[\"tot_iw\"]\n", "\n", "city_item_wt_df = (\n", "    city_item_wt_df[[\"city\", \"item_id\", \"iw\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "city_hour_wt_query = f\"\"\"\n", "    SELECT city, order_hour AS hour_, CAST(weights AS DOUBLE) AS hw\n", "    FROM supply_etls.city_hour_weights\n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM supply_etls.city_hour_weights WHERE DATE(updated_at) <= DATE('{today_date}'))\n", "\"\"\"\n", "city_hour_wt_df = pd.read_sql_query(city_hour_wt_query, trino)\n", "\n", "city_store_wt_query = f\"\"\"\n", "    SELECT city, facility_id, CAST(store_weight AS DOUBLE) AS sw\n", "    FROM supply_etls.city_store_weights\n", "    WHERE updated_at = (SELECT MAX(updated_at) FROM supply_etls.city_store_weights WHERE DATE(updated_at) <= DATE('{today_date}'))\n", "\"\"\"\n", "city_store_wt_df = pd.read_sql_query(city_store_wt_query, trino)"]}, {"cell_type": "markdown", "id": "68c5db43-9c8a-4d17-af6f-1e36d5e39d66", "metadata": {}, "source": ["### Facility and Item Details"]}, {"cell_type": "code", "execution_count": null, "id": "8663f171-8173-4834-ad53-a6ff90e3a1f9", "metadata": {}, "outputs": [], "source": ["item_query = f\"\"\"\n", "    SELECT item_id, name AS item_name\n", "    FROM rpc.item_category_details \n", "    WHERE l2_id = 1185\n", "    GROUP BY 1,2\n", "\"\"\"\n", "item_df = pd.read_sql_query(item_query, trino)\n", "\n", "store_query = f\"\"\"\n", "    SELECT id AS facility_id, name AS facility_name\n", "    FROM crates.facility\n", "    WHERE lake_active_record\n", "    GROUP BY 1,2\n", "\"\"\"\n", "store_df = pd.read_sql_query(store_query, trino)"]}, {"cell_type": "code", "execution_count": null, "id": "22a372ab-6bed-48d8-992d-f3ed45a6f044", "metadata": {}, "outputs": [], "source": ["final_frame = base_df[\n", "    [\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"hour_\",\n", "        \"sales_flag\",\n", "        \"be_inv_flag\",\n", "        \"demand_flag\",\n", "        \"grn_flag\",\n", "        \"transfer_flag\",\n", "        \"disc_flag\",\n", "        \"dump_flag\",\n", "        \"neg_inv_flag\",\n", "        \"fill_flag\",\n", "        \"no_po_flag\",\n", "        \"forecast_flag\",\n", "        \"manual_flag\",\n", "    ]\n", "]\n", "\n", "final_frame = final_frame.merge(city_item_wt_df, on=[\"city\", \"item_id\"], how=\"left\")\n", "\n", "final_frame = final_frame.merge(city_hour_wt_df, on=[\"city\", \"hour_\"], how=\"left\")\n", "\n", "final_frame = final_frame.merge(city_store_wt_df, on=[\"city\", \"facility_id\"], how=\"left\")\n", "\n", "final_frame = final_frame.merge(store_df, on=[\"facility_id\"], how=\"left\")\n", "\n", "final_frame = final_frame.merge(item_df, on=[\"item_id\"], how=\"left\")\n", "\n", "final_frame[\"sw\"] = final_frame[\"sw\"].fillna(0.001)\n", "\n", "final_frame[\"date_ist\"] = pd.to_datetime(today_date)\n", "\n", "final_frame = final_frame[\n", "    [\n", "        \"date_ist\",\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"sw\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"iw\",\n", "        \"hour_\",\n", "        \"hw\",\n", "        \"sales_flag\",\n", "        \"be_inv_flag\",\n", "        \"demand_flag\",\n", "        \"grn_flag\",\n", "        \"transfer_flag\",\n", "        \"disc_flag\",\n", "        \"dump_flag\",\n", "        \"neg_inv_flag\",\n", "        \"fill_flag\",\n", "        \"no_po_flag\",\n", "        \"forecast_flag\",\n", "        \"manual_flag\",\n", "    ]\n", "]\n", "\n", "if backfill == \"yes\":\n", "    final_frame[\"updated_at_ist\"] = (\n", "        pd.to_datetime(today_date) + timedelta(hours=int(current_hour - 1)) + timedelta(minutes=55)\n", "    )\n", "else:\n", "    final_frame[\"updated_at_ist\"] = (\n", "        pd.to_datetime(today_date) + timedelta(hours=int(current_hour - 1)) + timedelta(minutes=50)\n", "    )\n", "\n", "final_frame.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a1ddb985-f7be-4e54-8843-b5cb16cf43b1", "metadata": {}, "outputs": [], "source": ["final_frame.head(1)"]}, {"cell_type": "markdown", "id": "3d98ebc6-9cc9-414e-aaef-cd436078fb6d", "metadata": {}, "source": ["# Upload to Table"]}, {"cell_type": "markdown", "id": "f156267c-6be3-4b80-bc1d-2f15a815da01", "metadata": {}, "source": ["## Decision Tree"]}, {"cell_type": "code", "execution_count": null, "id": "8c421fc5-5692-4b4e-8606-e810535b3134", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"milk_jasoos_decision_tree_v3\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"updated_at filter date\"},\n", "        {\"name\": \"city\", \"type\": \"VARCHAR\", \"description\": \"City\"},\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "        {\"name\": \"facility_name\", \"type\": \"VARCHAR\", \"description\": \"Facility Name\"},\n", "        {\"name\": \"sw\", \"type\": \"REAL\", \"description\": \"Store Weight\"},\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "        {\"name\": \"item_name\", \"type\": \"VARCHAR\", \"description\": \"Item Name\"},\n", "        {\"name\": \"iw\", \"type\": \"REAL\", \"description\": \"Item Weight\"},\n", "        {\"name\": \"hour_\", \"type\": \"INTEGER\", \"description\": \"Hour\"},\n", "        {\"name\": \"hw\", \"type\": \"REAL\", \"description\": \"Hour Weight\"},\n", "        {\"name\": \"sales_flag\", \"type\": \"INTEGER\", \"description\": \"Sales Check\"},\n", "        {\"name\": \"be_inv_flag\", \"type\": \"INTEGER\", \"description\": \"BE Inventory Check\"},\n", "        {\"name\": \"demand_flag\", \"type\": \"INTEGER\", \"description\": \"Demand Check\"},\n", "        {\"name\": \"grn_flag\", \"type\": \"INTEGER\", \"description\": \"GRN Check\"},\n", "        {\"name\": \"transfer_flag\", \"type\": \"INTEGER\", \"description\": \"Transfers Check\"},\n", "        {\n", "            \"name\": \"disc_flag\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"GRN Discrepancy Check\",\n", "        },\n", "        {\"name\": \"dump_flag\", \"type\": \"INTEGER\", \"description\": \"Dump Check\"},\n", "        {\n", "            \"name\": \"neg_inv_flag\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Negative Inventory Check\",\n", "        },\n", "        {\"name\": \"fill_flag\", \"type\": \"INTEGER\", \"description\": \"Fill Rate Check\"},\n", "        {\"name\": \"no_po_flag\", \"type\": \"INTEGER\", \"description\": \"No PO Check\"},\n", "        {\"name\": \"forecast_flag\", \"type\": \"INTEGER\", \"description\": \"Forecast Check\"},\n", "        {\"name\": \"manual_flag\", \"type\": \"INTEGER\", \"description\": \"Manual Input Check\"},\n", "        {\"name\": \"updated_at_ist\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Updated at\"},\n", "    ],\n", "    \"primary_key\": [\"date_ist\", \"hour_\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\", \"hour_\"],\n", "    \"incremental_key\": \"date_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains the decision tree output for milk rca\",\n", "}\n", "pb.to_trino(final_frame, **kwargs)"]}, {"cell_type": "markdown", "id": "d3ffc28e-89f5-41f2-b449-591a2beb0816", "metadata": {}, "source": ["## Item Store Reasons"]}, {"cell_type": "code", "execution_count": null, "id": "ab9474c7-ce75-4ef7-85c1-ea0c6df7b03f", "metadata": {}, "outputs": [], "source": ["reasons_df = base_df.copy()\n", "\n", "reasons_df[\"date_ist\"] = pd.to_datetime(today_date)\n", "\n", "reasons_df = reasons_df[\n", "    [\n", "        \"date_ist\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"hour_\",\n", "        \"is_rep\",\n", "        \"po\",\n", "        \"grn\",\n", "        \"fr\",\n", "        \"m_demand_qty\",\n", "        \"m_sto_qty\",\n", "        \"m_be_inv\",\n", "        \"m_grn_qty\",\n", "        \"m_grn_hour\",\n", "        \"e_demand_qty\",\n", "        \"e_sto_qty\",\n", "        \"e_be_inv\",\n", "        \"e_grn_qty\",\n", "        \"e_grn_hour\",\n", "        \"sold_qty\",\n", "        \"m_disc_qty\",\n", "        \"e_disc_qty\",\n", "        \"incorrect_dump_qty\",\n", "        \"neg_inv_qty\",\n", "        \"sales_qty\",\n", "        \"dow_sales\",\n", "        \"weekday_sales\",\n", "        \"weekend_sales\",\n", "        \"d1\",\n", "        \"d2\",\n", "        \"d3\",\n", "    ]\n", "]\n", "\n", "if backfill == \"yes\":\n", "    reasons_df[\"updated_at_ist\"] = (\n", "        pd.to_datetime(today_date) + timedelta(hours=int(current_hour - 1)) + timedelta(minutes=55)\n", "    )\n", "else:\n", "    reasons_df[\"updated_at_ist\"] = (\n", "        pd.to_datetime(today_date) + timedelta(hours=int(current_hour - 1)) + timedelta(minutes=50)\n", "    )\n", "\n", "reasons_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "0fbe2093-2fd3-4ef7-bfd5-ac74f3138fca", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"milk_jasoos_deep_dive_v3\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date of run\"},\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility ID\"},\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item ID\"},\n", "        {\"name\": \"hour_\", \"type\": \"INTEGER\", \"description\": \"Hour\"},\n", "        {\"name\": \"is_rep\", \"type\": \"INTEGER\", \"description\": \"Is 2nd Rep\"},\n", "        {\"name\": \"po\", \"type\": \"REAL\", \"description\": \"Po Qty\"},\n", "        {\"name\": \"grn\", \"type\": \"REAL\", \"description\": \"Grn Qty\"},\n", "        {\"name\": \"fr\", \"type\": \"REAL\", \"description\": \"Fill Rate\"},\n", "        {\n", "            \"name\": \"m_demand_qty\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Morning ARS Demand\",\n", "        },\n", "        {\"name\": \"m_sto_qty\", \"type\": \"INTEGER\", \"description\": \"Morning STO Qty\"},\n", "        {\"name\": \"m_be_inv\", \"type\": \"INTEGER\", \"description\": \"Morning BE Inventory\"},\n", "        {\"name\": \"m_grn_qty\", \"type\": \"INTEGER\", \"description\": \"Morning GRN Qty\"},\n", "        {\"name\": \"m_grn_hour\", \"type\": \"INTEGER\", \"description\": \"Morning GRN Hour\"},\n", "        {\n", "            \"name\": \"e_demand_qty\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Evening ARS Demand\",\n", "        },\n", "        {\"name\": \"e_sto_qty\", \"type\": \"INTEGER\", \"description\": \"Evening STO Qty\"},\n", "        {\"name\": \"e_be_inv\", \"type\": \"INTEGER\", \"description\": \"Evening BE Inventory\"},\n", "        {\"name\": \"e_grn_qty\", \"type\": \"INTEGER\", \"description\": \"Evening GRN Qty\"},\n", "        {\"name\": \"e_grn_hour\", \"type\": \"INTEGER\", \"description\": \"Evening GRN Hour\"},\n", "        {\"name\": \"sold_qty\", \"type\": \"INTEGER\", \"description\": \"Sales Qty\"},\n", "        {\"name\": \"m_disc_qty\", \"type\": \"INTEGER\", \"description\": \"Morning GRN Disc\"},\n", "        {\"name\": \"e_disc_qty\", \"type\": \"INTEGER\", \"description\": \"Evening GRN Disc\"},\n", "        {\n", "            \"name\": \"incorrect_dump_qty\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Incorrect Dump <PERSON>\",\n", "        },\n", "        {\n", "            \"name\": \"neg_inv_qty\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Negative Inventory Qty\",\n", "        },\n", "        {\"name\": \"sales_qty\", \"type\": \"REAL\", \"description\": \"Negative Inventory Qty\"},\n", "        {\"name\": \"dow_sales\", \"type\": \"REAL\", \"description\": \"DOW Sales\"},\n", "        {\"name\": \"weekday_sales\", \"type\": \"REAL\", \"description\": \"Weekday Sales\"},\n", "        {\"name\": \"weekend_sales\", \"type\": \"REAL\", \"description\": \"Weekend Sales\"},\n", "        {\"name\": \"d1\", \"type\": \"REAL\", \"description\": \"DOW Deviation\"},\n", "        {\"name\": \"d2\", \"type\": \"REAL\", \"description\": \"Weekday Deviation\"},\n", "        {\"name\": \"d3\", \"type\": \"REAL\", \"description\": \"Weekend Deviation\"},\n", "        {\"name\": \"updated_at_ist\", \"type\": \"TIMESTAMP(6)\", \"description\": \"Updated at\"},\n", "    ],\n", "    \"primary_key\": [\"date_ist\", \"hour_\", \"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"date_ist\", \"hour_\"],\n", "    \"incremental_key\": \"date_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains the deep dive output for milk rca\",\n", "}\n", "pb.to_trino(reasons_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "da3a323c-f10b-4eea-9c4c-45995ff2271d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "998f12c3-bb35-49a9-b6cf-3f3ebf58141a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bf9d9821-774a-40d6-95f0-a326f41fc3b9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a635dfad-53d4-42d2-a43c-98aca12fb476", "metadata": {"tags": []}, "source": ["## Manual Transfer Changes"]}, {"cell_type": "code", "execution_count": null, "id": "1f477347-5a8e-417e-a76f-1f262d131f85", "metadata": {}, "outputs": [], "source": ["# manual_query = f\"\"\"\n", "#     WITH base_pre AS (\n", "#         SELECT facility_id, item_id, EXTRACT(hour FROM updated_at) AS hour_, date_ist, updated_at, min_qty\n", "#         FROM supply_etls.facility_item_min_max_quantity_log\n", "#         WHERE date_ist BETWEEN DATE('{today_date}') - interval '2' day AND DATE('{today_date}') - interval '1' day\n", "#     ),\n", "\n", "#     initial_base AS (\n", "#         SELECT a.date_ist AS date_, a.facility_id, a.item_id, a.updated_at AS old_update_time, SUM(a.min_qty) AS initial_min_qty\n", "#         FROM base_pre a\n", "#         JOIN (\n", "#             SELECT facility_id, item_id, date_ist, MIN(updated_at) AS updated_at\n", "#             FROM base_pre\n", "#             WHERE hour_ > 18\n", "#             GROUP BY 1,2,3\n", "#         ) b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ist = b.date_ist AND a.updated_at = b.updated_at\n", "#         WHERE a.item_id IN {item_id_list}\n", "#         GROUP BY 1,2,3,4\n", "#     ),\n", "\n", "#     final_base AS (\n", "#         SELECT a.date_ist AS date_, a.facility_id, a.item_id, a.updated_at AS new_update_time, SUM(a.min_qty) AS min_qty\n", "#         FROM base_pre a\n", "#         JOIN (\n", "#             SELECT facility_id, item_id, date_ist, MAX(updated_at) AS updated_at\n", "#             FROM base_pre\n", "#             WHERE hour_ > 18\n", "#             GROUP BY 1,2,3\n", "#         ) b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ist = b.date_ist AND a.updated_at = b.updated_at\n", "#         WHERE a.item_id IN {item_id_list}\n", "#         GROUP BY 1,2,3,4\n", "#     )\n", "\n", "#     SELECT a.*, b.new_update_time, b.min_qty\n", "#     FROM initial_base a\n", "#     JOIN final_base b ON a.facility_id = b.facility_id AND a.item_id = b.item_id AND a.date_ = b.date_\n", "#     WHERE a.date_ = DATE('{today_date}') - interval '1' day\n", "# \"\"\"\n", "# manual_df = pd.read_sql_query(manual_query, trino)\n", "\n", "# manual_df['delta'] = manual_df['min_qty'] / manual_df['initial_min_qty'] - 1\n", "\n", "# manual_df['manual_flag'] = np.where(manual_df['delta'] <= -0.1, 1, 0)\n", "\n", "# manual_df = manual_df[manual_df['manual_flag'] == 1][['facility_id','item_id','manual_flag']].drop_duplicates().reset_index().drop(columns = {'index'})\n", "\n", "# manual_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "75c24815-e936-4b59-8906-cf32d7a02281", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1a73e017-688c-4c53-b670-03bb03e3c8ed", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3d92b856-641a-48d4-bfa4-43d94011da4a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "78449a17-1494-4744-9416-fae9b0bb1879", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e4842ec3-f31e-4c43-bad1-4662cdeda5fc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fd976f0c-1d73-4a2e-a3bd-c1281808e2bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4d396756-6ea4-4775-be77-ac2356b2e9f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16508dc0-64c6-4f77-9da4-c05f8adeb50a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "081342d7-6ffa-45ac-8892-725a5c26c506", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "83852940-5244-4775-a0e4-40e3b19d538c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}