alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: jasoos_milk
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/availability_jasoos/workflow/jasoos_milk
paused: false
pool: povms_pool
project_name: availability_jasoos
schedule:
  end_date: '2025-07-28T00:00:00'
  interval: 05 1-18 * * *
  start_date: '2025-05-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 6
