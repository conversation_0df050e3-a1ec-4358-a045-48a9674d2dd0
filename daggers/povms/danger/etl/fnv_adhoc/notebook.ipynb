{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pymysql"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from pencilbox.io.sheets import gspread_client\n", "from datetime import datetime\n", "import pymysql\n", "from pytz import timezone\n", "import boto3\n", "import json\n", "import os\n", "import requests\n", "import csv\n", "from pencilbox.io.sheets import gspread_client\n", "from operator import attrgetter\n", "from datetime import datetime\n", "from pencilbox.connections import get_slack_client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_list = [\n", "    (2, -2),\n", "    (12, -12),\n", "    (14, 40),\n", "    (54, 66, 110, 125, 55, 167),\n", "    (6, -6),\n", "    (5, -5),\n", "    (15, -15),\n", "    (3, -3),\n", "    (1, -1),\n", "    (7, 37, 10, 27, 9, 204, 142, 8, 19),\n", "]\n", "city_list_disc = []\n", "for i in city_list:\n", "    for item in i:\n", "        if item > 0:\n", "            city_list_disc.append(item)\n", "city_list_disc = tuple(city_list_disc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## facility_wise_request"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rpc_secret = pb.get_secret(\"retail/noto-reports/mysql/rpc-rds-read\")\n", "host = rpc_secret.get(\"host\")\n", "username = rpc_secret.get(\"username\")\n", "password = rpc_secret.get(\"password\")\n", "CON_RPC = pymysql.connect(host=host, user=username, password=password)\n", "\n", "city_ass_sql = f\"\"\"\n", "select city_id, item_id, master_assortment_substate_id as in_assortment from rpc.product_city_assortment_suggestion where active = true and \n", "(master_assortment_substate_id = 1 or master_assortment_substate_id = 3) and city_id in {city_list_disc}\n", "and item_id in (select distinct item_id from rpc.item_category_details where l0_id = 1487)\n", "group by 1,2,3\n", "\"\"\"\n", "fetch_city_assortment = pd.read_sql_query(sql=city_ass_sql, con=CON_RPC)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_items(cat):\n", "    sql = \"\"\"\n", "    with all_skus as (\n", "    select item_id, l2_id, max(name) as item_name, max(l0) as l0, max(l1) as l1, max(l2) as l2\n", "    from lake_rpc.item_category_details\n", "    group by 1,2\n", "    ),\n", "\n", "    fnv_skus as (select distinct item_id from lake_rpc.item_category_details where l0_id = 1487),\n", "    perishable_skus as (select distinct item_id from lake_rpc.product_product where perishable = 1\n", "                        and item_id not in (select item_id from fnv_skus)\n", "                        and item_id not in (select distinct item_id from all_skus where l2_id in (1127, 1961,20,63,1369))\n", "                        and item_id not in (10111076)),\n", "                        \n", "    milk_skus as (select distinct item_id from all_skus where l2_id = 1185),\n", "\n", "    giri_skus as (select distinct item_id from all_skus where item_id not in (select item_id from perishable_skus)\n", "                                                            and item_id not in (select item_id from fnv_skus)\n", "                                                            and item_id not in (select distinct item_id from all_skus\n", "                                                            where l2_id in (1185, 1367, 63, 1369, 1961, 20, 1127)))\n", "    select item_id from {input} \n", "    \"\"\".format(\n", "        input=cat\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "fnv_sku = fetch_items(\"fnv_skus\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1rIzk7YsKnlivCK3s769W-VhBH2JC8AilrwlyxJX0cV8\"\n", "sheet_name = \"Facility Level Requests\"\n", "df_facility_wise_request = pb.from_sheets(sheet_id, sheet_name)\n", "df_facility_wise_request = df_facility_wise_request[[\"item_id\", \"facility_id\", \"city_id\"]]\n", "df_facility_wise_request = df_facility_wise_request.astype(int)\n", "\n", "df_facility_wise_request = df_facility_wise_request.merge(\n", "    fetch_city_assortment, on=[\"item_id\", \"city_id\"], how=\"inner\"\n", ")\n", "df_facility_wise_request = df_facility_wise_request[\n", "    [\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]\n", "].drop_duplicates()\n", "df_facility_wise_request = df_facility_wise_request[\n", "    df_facility_wise_request[\"item_id\"].isin(fnv_sku[\"item_id\"].unique())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_facility_wise_request.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## city_wise_request"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_city_facility_df(city_id):\n", "    sql = \"\"\"\n", "        select distinct \n", "            pfom.facility_id, pfom.city_id \n", "        from \n", "            lake_po.physical_facility_outlet_mapping pfom\n", "        inner join \n", "            lake_retail.console_outlet co\n", "        on \n", "            co.facility_id=pfom.facility_id\n", "        where \n", "            co.business_type_id = 7 and\n", "            pfom.active=1 and\n", "            pfom.ars_active=1 and\n", "            pfom.single_entity=1 and \n", "            pfom.city_id in {x}\n", "        \"\"\".format(\n", "        x=city_id\n", "    )\n", "\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "city_map = {\n", "    \"NCR\": \"(9,7,37,10,27,204,142,8,19)\",\n", "    \"Lucknow\": \"(14,40)\",\n", "    \"Jaipur\": \"(12)\",\n", "    \"Kolkata\": \"(15)\",\n", "    \"Bengaluru\": \"(1)\",\n", "    \"Hyderabad\": \"(5)\",\n", "    \"Chennai\": \"(4)\",\n", "    \"Mumbai\": \"(2)\",\n", "    \"Pune\": \"(3)\",\n", "    \"Ahmedabad\": \"(6)\",\n", "    \"Punjab\": \"(54,66,110,125,55,167)\",\n", "    \"Guwahati\": \"(165)\",\n", "    \"Nagpur\": \"(41)\",\n", "    \"Ranchi\": \"(68)\",\n", "}\n", "\n", "city_id_map = {\n", "    \"NCR\": 9,\n", "    \"Lucknow\": 14,\n", "    \"Jaipur\": 12,\n", "    \"Kolkata\": 15,\n", "    \"Bengaluru\": 1,\n", "    \"Hyderabad\": 5,\n", "    \"Chennai\": 4,\n", "    \"Mumbai\": 2,\n", "    \"Pune\": 3,\n", "    \"Ahmedabad\": 6,\n", "    \"Punjab\": 54,\n", "    \"Guwahati\": 165,\n", "    \"Nagpur\": 41,\n", "    \"Ranchi\": 68,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1rIzk7YsKnlivCK3s769W-VhBH2JC8AilrwlyxJX0cV8\"\n", "sheet_name = \"City Level Requests\"\n", "df_city_wise_request = pb.from_sheets(sheet_id, sheet_name)\n", "df_city_wise_request = df_city_wise_request[list(df_city_wise_request)[:10]]\n", "df_city_wise_request = df_city_wise_request[df_city_wise_request[\"Auto Approval\"] == \"1\"]\n", "city_cluster_list = df_city_wise_request[\"City Cluster Name\"].unique()\n", "df_city_wise_request = df_city_wise_request[[\"item_id\", \"City Cluster Name\"]]\n", "df_city_wise_request[\"item_id\"] = df_city_wise_request[\"item_id\"].map(\n", "    lambda x: \"0\" if (x) == \"\" else x\n", ")\n", "df_city_wise_request[\"item_id\"] = df_city_wise_request[\"item_id\"].astype(int)\n", "\n", "facility_df = pd.DataFrame()\n", "for city in city_cluster_list:\n", "    temp_df = get_city_facility_df(city_map[city])\n", "    temp_df[\"City Cluster Name\"] = city\n", "    facility_df = pd.concat([facility_df, temp_df], ignore_index=True)\n", "facility_df = facility_df.dropna()\n", "\n", "\n", "df_city_wise_request = df_city_wise_request.merge(\n", "    facility_df, on=[\"City Cluster Name\"], how=\"inner\"\n", ")\n", "df_city_wise_request = df_city_wise_request[[\"item_id\", \"facility_id\", \"city_id\"]]\n", "\n", "df_city_wise_request = df_city_wise_request.merge(\n", "    fetch_city_assortment, on=[\"item_id\", \"city_id\"], how=\"inner\"\n", ")\n", "df_city_wise_request = df_city_wise_request[\n", "    [\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]\n", "].drop_duplicates()\n", "df_city_wise_request = df_city_wise_request[\n", "    df_city_wise_request[\"item_id\"].isin(fnv_sku[\"item_id\"].unique())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df_city_wise_request)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["len(df_city_wise_request[df_city_wise_request[\"item_id\"] == 10006477])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## new_launch_request"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_items_category_details(item_id):\n", "    sql = \"\"\"\n", "        with temp1 as (select item_id, product_type from dwh.dim_product x\n", "                        inner join lake_rpc.item_product_mapping y on x.product_id = y.product_id\n", "                        where is_current = 1 and active = 1 and offer_id is NULL and item_id = {iid}),\n", "\n", "        temp2 as (select item_id, l0, l1, l2 from lake_rpc.item_category_details where item_id = {iid}),\n", "\n", "        temp3 as (select item_id, name, weight_in_gm as weight, variant_mrp as mrp  from lake_rpc.product_product where item_id = {iid})\n", "\n", "        select temp2.*, product_type, name, weight, mrp from temp1 \n", "        inner join temp2 on temp1.item_id = temp2.item_id\n", "        inner join temp3 on temp1.item_id = temp3.item_id\n", "        group by 1,2,3,4,5,6,7,8\n", "        \"\"\".format(\n", "        iid=item_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def min_max_scaling(series):\n", "    return (series - series.min()) / (series.max() - series.min())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_facility_category_df(sales_df_, item_id, is_premium):\n", "\n", "    item_cat = fetch_items_category_details(item_id)\n", "    if len(item_cat) == 0:\n", "        return item_cat\n", "    facility_count = sales_df_[\"facility_id\"].nunique()\n", "    flow_vec = [\"product_type\", \"l2\", \"l1\", \"l0\"]\n", "    category_level = \"product_type\"\n", "    category_name = [item_cat[\"product_type\"][0]]\n", "    for i in flow_vec:\n", "        i_count = sales_df_[sales_df_[i] == item_cat[i][0]][\"facility_id\"].nunique()\n", "        if i_count > 0.8 * facility_count:\n", "            category_level = i\n", "            category_name = [item_cat[i][0]]\n", "            break\n", "\n", "    sales_df = sales_df_.copy()\n", "    sales_df[\"margin\"] = 1\n", "    facility_category = sales_df[\n", "        [\"facility_id\", category_level, \"margin\", \"quantity\", \"conversion\", \"gmv\"]\n", "    ].copy()\n", "    facility_category = facility_category[facility_category[category_level].isin(category_name)]\n", "\n", "    facility_category[\"margin\"] = facility_category[\"margin\"] * facility_category[\"gmv\"]\n", "    facility_category[\"margin\"] = facility_category.groupby([\"facility_id\", category_level])[\n", "        \"margin\"\n", "    ].transform(\"sum\") / facility_category.groupby([\"facility_id\", category_level])[\n", "        \"gmv\"\n", "    ].transform(\n", "        \"sum\"\n", "    )\n", "\n", "    facility_category[\"conversion\"] = facility_category[\"conversion\"] * facility_category[\"gmv\"]\n", "    facility_category[\"conversion\"] = facility_category.groupby([\"facility_id\", category_level])[\n", "        \"conversion\"\n", "    ].transform(\"sum\") / facility_category.groupby([\"facility_id\", category_level])[\n", "        \"gmv\"\n", "    ].transform(\n", "        \"sum\"\n", "    )\n", "\n", "    facility_category[\"quantity\"] = facility_category[\"quantity\"] * facility_category[\"gmv\"]\n", "    facility_category[\"quantity\"] = facility_category.groupby([\"facility_id\", category_level])[\n", "        \"quantity\"\n", "    ].transform(\"sum\") / facility_category.groupby([\"facility_id\", category_level])[\n", "        \"gmv\"\n", "    ].transform(\n", "        \"sum\"\n", "    )\n", "\n", "    facility_category = facility_category[\n", "        [\"facility_id\", \"quantity\", \"conversion\", \"margin\"]\n", "    ].drop_duplicates()\n", "\n", "    # facility_category = facility_category.replace(np.nan, 0)\n", "\n", "    for col in [\"quantity\", \"conversion\", \"margin\"]:\n", "        facility_category[col] = min_max_scaling(facility_category[col])\n", "\n", "    facility_category = facility_category.fillna(0)\n", "    facility_category[\"relevance_score\"] = (\n", "        0.7 * facility_category[\"quantity\"] + 0.3 * facility_category[\"conversion\"]\n", "    )\n", "\n", "    # if(is_premium == 0):\n", "    #     facility_category['relevance_score'] = (0.7*facility_category['quantity']+0.3*facility_category['conversion'])\n", "    # if(is_premium == 1):\n", "    #     facility_category['relevance_score'] = (0.5*facility_category['quantity']+0.25*facility_category['conversion']+0.25*facility_category['margin'])\n", "\n", "    #     facility_category.set_index('facility_id',inplace=True)\n", "    facility_category[\"item_id\"] = item_id\n", "    temp_bc = (\n", "        facility_category[\"relevance_score\"].max()\n", "        - 2.5 * facility_category[\"relevance_score\"].std()\n", "    )\n", "\n", "    facility_category = facility_category[facility_category[\"relevance_score\"] >= temp_bc][\n", "        [\"item_id\", \"facility_id\"]\n", "    ]\n", "\n", "    return facility_category"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "BU_AWS_CONFIG = {\n", "    \"BUCKET_NAME\": \"retail-bulk-upload\",\n", "    \"AWS_KEY\": secrets.get(\"aws_key\"),\n", "    \"AWS_SECRET\": secrets.get(\"aws_secret\"),\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pull_city_sales_data_from_s3(aws_config, city_id):\n", "    bucket_name = aws_config[\"BUCKET_NAME\"]\n", "    aws_key = aws_config[\"AWS_KEY\"]\n", "    aws_secret = aws_config[\"AWS_SECRET\"]\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "    obj_list = bucket_obj.objects.filter(\n", "        Prefix=\"assortment_recommendation/sales/{city_id}/\".format(city_id=city_id)\n", "    )\n", "    return bucket_obj, obj_list\n", "\n", "\n", "def get_latest_obj_from_list_and_download_to_local(bucket_obj, objects):\n", "    # sort the objects based on 'obj.last_modified'\n", "    sorted_objs = sorted(objects, key=attrgetter(\"last_modified\"))\n", "\n", "    # The latest version of the file (the last one in the list)\n", "    latest = sorted_objs.pop()\n", "    print(latest.key)\n", "    tmp_local_file = \"SVD_Assortment_sales_pulled.csv\"\n", "    bucket_obj.download_file(latest.key, tmp_local_file)\n", "    return tmp_local_file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["sheet_id = \"1rIzk7YsKnlivCK3s769W-VhBH2JC8AilrwlyxJX0cV8\"\n", "sheet_name = \"New Launch (niche target)\"\n", "df_new_launch = pb.from_sheets(sheet_id, sheet_name)\n", "df_new_launch = df_new_launch[\n", "    (df_new_launch[\"Auto Approval\"] == \"1\")\n", "    & (df_new_launch[\"Requesting team/L0\"] != \"Planning Team\")\n", "]\n", "df_new_launch = df_new_launch[[\"item_id\", \"City Name\"]]\n", "\n", "df_map = pb.from_sheets(sheet_id, \"map_for_production\")\n", "\n", "df_new_launch = df_new_launch.merge(df_map, on=[\"City Name\"], how=\"inner\")\n", "df_new_launch = df_new_launch[~(df_new_launch[\"item_id\"] == \"\")]\n", "df_new_launch = df_new_launch[[\"item_id\", \"city_id\"]].astype(int)\n", "\n", "df_new_launch_rec = pd.DataFrame()\n", "# city_list = df_new_launch['city_id'].unique()\n", "# citi_to_do_list = [7,37,10,9,204,8,1]\n", "for city_id in city_list:\n", "    print(\"doing for city_id = \", city_id)\n", "    # city_sales_df = fetch_city_sales(city_id)\n", "    # atc_view_conv = fetch_atc_view_conv(city_id)\n", "    # city_sales_df = city_sales_df.merge(atc_view_conv, on = ['item_id', 'facility_id'], how = 'left')\n", "\n", "    s3_bucket, objects = pull_city_sales_data_from_s3(BU_AWS_CONFIG, city_id)\n", "    local_sales_csv_file = get_latest_obj_from_list_and_download_to_local(s3_bucket, objects)\n", "    city_sales_df = pd.read_csv(local_sales_csv_file)\n", "    city_sales_df = city_sales_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"product_type\",\n", "            \"l2\",\n", "            \"l1\",\n", "            \"l0\",\n", "            \"quantity\",\n", "            \"conversion\",\n", "            \"gmv\",\n", "        ]\n", "    ]\n", "    city_sales_df = city_sales_df[city_sales_df[\"facility_id\"] != 1028]\n", "    df_new_launch_city_wise = df_new_launch[df_new_launch[\"city_id\"].isin(list(city_id))]\n", "    item_list = df_new_launch_city_wise[\"item_id\"].unique()\n", "    rec_facility_df = pd.DataFrame()\n", "    for item_id in item_list:\n", "        if item_id not in fnv_sku[\"item_id\"].unique():\n", "            continue\n", "        print(\"doing for item_id = \", item_id)\n", "        if item_id == 0:\n", "            continue\n", "        temp_generate_facility_category_df = generate_facility_category_df(\n", "            city_sales_df, item_id, 1\n", "        )\n", "        if len(temp_generate_facility_category_df) == 0:\n", "            continue\n", "        rec_facility_df = pd.concat(\n", "            [rec_facility_df, temp_generate_facility_category_df], ignore_index=True\n", "        )\n", "    if len(rec_facility_df) == 0:\n", "        continue\n", "    df_new_launch_city_wise = df_new_launch_city_wise[[\"item_id\", \"city_id\"]].merge(\n", "        rec_facility_df, on=[\"item_id\"], how=\"inner\"\n", "    )\n", "    # city_assortment = fetch_city_assortment(city_id)\n", "    df_new_launch_city_wise = df_new_launch_city_wise.merge(\n", "        fetch_city_assortment, on=[\"item_id\", \"city_id\"], how=\"inner\"\n", "    )\n", "    df_new_launch_rec = pd.concat([df_new_launch_rec, df_new_launch_city_wise], ignore_index=True)\n", "print(list(df_new_launch_rec))\n", "df_new_launch_rec = df_new_launch_rec[\n", "    [\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]\n", "].drop_duplicates()\n", "# df_new_launch_rec.to_csv('df_new_launch_rec.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df_new_launch_rec)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## solving discrepancy through adhoc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_store_sql = f\"\"\"\n", "    with fo as (select facility_id, outlet_id from lake_po.physical_facility_outlet_mapping where active=1 \n", "    and ars_active=1 and city_id in {city_list_disc}\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "    )\n", "    \n", "    SELECT * FROM fo\n", "    \"\"\"\n", "\n", "\n", "dark_store = pd.read_sql_query(dark_store_sql, pb.get_connection(\"[Warehouse] Redshift\"))\n", "dark_store_fo = tuple(list(dark_store[\"facility_id\"].unique().astype(int)))\n", "len(dark_store_fo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual_sql = f\"\"\"\n", "    select item_id, facility_id, city_id, master_assortment_substate_id \n", "               from rpc.product_facility_master_assortment\n", "        where facility_id in {dark_store_fo} and master_assortment_substate_id != 2\n", "        and item_id in (select distinct item_id from rpc.item_category_details where l0_id = 1487)\n", "        \"\"\"\n", "actual = pd.read_sql_query(actual_sql, CON_RPC)\n", "actual.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_status_sql = f\"\"\"\n", "    select item_id, city_id, master_assortment_substate_id \n", "    from rpc.product_city_assortment_suggestion where city_id in {city_list_disc}\n", "    and item_id in (select distinct item_id from rpc.item_category_details where l0_id = 1487)\n", "    \n", "    \"\"\"\n", "\n", "current_status = pd.read_sql_query(current_status_sql, CON_RPC)\n", "current_status[current_status[\"item_id\"] == 10080435].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_df = pd.merge(\n", "    actual,\n", "    current_status.rename(\n", "        columns={\"master_assortment_substate_id\": \"master_assortment_substate_id_should\"}\n", "    ),\n", "    on=[\"item_id\", \"city_id\"],\n", "    how=\"inner\",\n", ")\n", "check_df = (\n", "    check_df[\n", "        check_df[\"master_assortment_substate_id\"]\n", "        != check_df[\"master_assortment_substate_id_should\"]\n", "    ]\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"master_assortment_substate_id\"})\n", ")\n", "\n", "check_df = check_df.rename(\n", "    columns={\"master_assortment_substate_id_should\": \"master_assortment_substate_id\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["op_req_sql = f\"\"\"\n", "        select item_id, facility_id, state as master_assortment_substate_id from rpc.item_facility_state_request where status = 'OPEN'\n", "        and item_id in (select distinct item_id from rpc.item_category_details where l0_id = 1487)\n", "        \"\"\"\n", "op_req = pd.read_sql_query(op_req_sql, CON_RPC)\n", "op_req.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["except_df = pd.merge(\n", "    check_df,\n", "    op_req,\n", "    on=[\"item_id\", \"facility_id\", \"master_assortment_substate_id\"],\n", "    how=\"inner\",\n", ")\n", "except_df[\"flg\"] = 1\n", "except_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_disc_city = pd.merge(\n", "    check_df,\n", "    except_df,\n", "    on=[\"item_id\", \"facility_id\", \"master_assortment_substate_id\", \"city_id\"],\n", "    how=\"left\",\n", ")\n", "df_disc_city = df_disc_city[df_disc_city[\"flg\"] != 1].reset_index().drop(columns={\"index\", \"flg\"})\n", "df_disc_city = df_disc_city.rename(columns={\"master_assortment_substate_id\": \"in_assortment\"})\n", "# check_df.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_disc = df_disc_city.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_disc[\"item_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["adhoc_combo = pd.concat([df_facility_wise_request, df_city_wise_request], ignore_index=True)\n", "adhoc_combo = pd.concat([adhoc_combo, df_new_launch_rec], ignore_index=True)\n", "adhoc_combo = pd.concat([adhoc_combo, df_disc], ignore_index=True)\n", "adhoc_combo = adhoc_combo.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["present_status_sql = f\"\"\"\n", "select item_id, facility_id, city_id, master_assortment_substate_id as in_assortment \n", "from rpc.product_facility_master_assortment \n", "where facility_id in {dark_store_fo}\n", "and item_id in (select distinct item_id from rpc.item_category_details where l0_id = 1487)\n", "\"\"\"\n", "present_status = pd.read_sql_query(sql=present_status_sql, con=CON_RPC)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["adhoc_combo = adhoc_combo.merge(present_status, on=list(present_status), how=\"left\", indicator=True)\n", "adhoc_combo = adhoc_combo[adhoc_combo[\"_merge\"] == \"left_only\"]\n", "adhoc_combo = adhoc_combo[[\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]].astype(int)\n", "adhoc_combo = adhoc_combo[adhoc_combo[\"facility_id\"] != 1028]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["citi_list = list(city_list_disc)\n", "df_adhoc = adhoc_combo[adhoc_combo[\"city_id\"].isin(citi_list)].drop_duplicates()\n", "df_adhoc = df_adhoc[df_adhoc[\"facility_id\"] != 1028]\n", "len(df_adhoc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_adhoc = df_adhoc[df_adhoc[\"item_id\"].isin(fnv_sku[\"item_id\"].unique())]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df_adhoc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_adhoc[[\"item_id\", \"city_id\", \"in_assortment\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_adhoc[df_adhoc[\"item_id\"] == 10014188]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_adhoc[['item_id', 'city_id', 'in_assortment']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def upload_to_redash_table(data_temp):\n", "    data = data_temp.copy()\n", "    from datetime import datetime, timedelta\n", "\n", "    data.rename(columns={\"in_assortment\": \"state\"}, inplace=True)\n", "    data[\"reason\"] = 6\n", "    data[\"updated_at\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "    data = data[[\"item_id\", \"facility_id\", \"city_id\", \"state\", \"reason\", \"updated_at\"]]\n", "    kwargs_log = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"recommended_assortment_log\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "            {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "            {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"city_id\"},\n", "            {\n", "                \"name\": \"state\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"master_assortment_substate\",\n", "            },\n", "            {\"name\": \"reason\", \"type\": \"integer\", \"description\": \"reason_type_id\"},\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"Updated timestamp\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"item_id\", \"facility_id\", \"updated_at\"],\n", "        \"sortkey\": [\"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores list of recommended assortment\",\n", "    }\n", "\n", "    pb.to_redshift(data, **kwargs_log)\n", "\n", "    fac_id = tuple(data[\"facility_id\"].unique())\n", "    # wrting query to fetch current assortment\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    sql = \"\"\"\n", "    select item_id, facility_id, city_id, state, reason from metrics.dynamic_assortment_ideal_result\n", "    where facility_id in {x}\n", "    \"\"\".format(\n", "        x=fac_id\n", "    )\n", "    curr_assortment = pd.read_sql_query(sql=sql, con=con)\n", "    curr_assortment = curr_assortment[\n", "        [\"item_id\", \"facility_id\", \"city_id\", \"state\", \"reason\"]\n", "    ].astype(int)\n", "    # done fethcing curr ass\n", "\n", "    data_ideal = data[[\"item_id\", \"facility_id\", \"city_id\", \"state\", \"reason\"]].astype(int)\n", "    data_ideal = pd.concat([curr_assortment, data_ideal], axis=0).drop_duplicates(\n", "        subset=[\"item_id\", \"facility_id\"], keep=\"last\"\n", "    )\n", "\n", "    data2 = data_ideal[[\"item_id\", \"facility_id\", \"city_id\", \"state\", \"reason\"]]\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"dynamic_assortment_ideal_result\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item id\"},\n", "            {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility id\"},\n", "            {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"Facility id\"},\n", "            {\"name\": \"state\", \"type\": \"integer\", \"description\": \"Assortment state\"},\n", "            {\"name\": \"reason\", \"type\": \"integer\", \"description\": \"Reason\"},\n", "        ],\n", "        \"primary_key\": [\"item_id\", \"facility_id\"],\n", "        \"sortkey\": [\"item_id\", \"facility_id\"],\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Dynamic assortment expectations\",\n", "        \"force_upsert_without_increment_check\": True,\n", "    }\n", "\n", "    pb.to_redshift(data2, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "    payload = {\n", "        \"file\": file_path,\n", "        \"created_by\": \"<PERSON><PERSON>\",\n", "        \"user_id\": 14,\n", "        \"is_auto_po\": True,\n", "        \"upload_type_id\": upload_type,\n", "        \"content_type\": \"text/csv\",\n", "    }\n", "    response = requests.post(url, json=payload)\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["if len(df_adhoc):\n", "    # sending mail to hit<PERSON> for any adhoc delta\n", "    from_email = \"<EMAIL>\"\n", "    to_email = [\"<EMAIL>\"]\n", "    subject = \"assortment changes\"\n", "    html_content = \"adhoc refresh for fnv pan india\"\n", "    df_adhoc.to_csv(\"df_temp.csv\", index=False)\n", "    pb.send_email(from_email, to_email, subject, html_content, files=[\"df_temp.csv\"])\n", "\n", "    # creating log for all uploads\n", "    upload_to_redash_table(df_adhoc)\n", "\n", "    # doing some data frame column edits for actual ams upload\n", "    final_df = df_adhoc[[\"item_id\", \"facility_id\", \"in_assortment\"]]\n", "    final_df.rename(columns={\"in_assortment\": \"substate_id\"}, inplace=True)\n", "    final_df = final_df.rename(\n", "        columns={\n", "            \"item_id\": \"Item ID\",\n", "            \"facility_id\": \"Facility ID\",\n", "            \"substate_id\": \"Sub-state ID\",\n", "        }\n", "    )\n", "    final_df[\"City ID\"] = \"\"\n", "    final_df[\"Reason ID\"] = 6  # Only for Ad hoc assortment request\n", "    final_df[\"DS_FS_Flag\"] = 0\n", "    final_df[\"Facility ID\"] = final_df[\"Facility ID\"].astype(int)\n", "    final_df[\"Sub-state ID\"] = final_df[\"Sub-state ID\"].astype(int)\n", "    final_df[\"Item ID\"] = final_df[\"Item ID\"].astype(int)\n", "    final_df[\"Reason ID\"] = final_df[\"Reason ID\"].astype(int)\n", "    final_df[\"DS_FS_Flag\"] = final_df[\"DS_FS_Flag\"].astype(int)\n", "    bucket_name = \"retail-bulk-upload\"\n", "    secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "    aws_key = secrets.get(\"aws_key\")\n", "    aws_secret = secrets.get(\"aws_secret\")\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "\n", "    from_email = \"<EMAIL>\"\n", "    secrets_for_noto = pb.get_secret(\"retail/noto/\")\n", "    to_email = json.loads(secrets_for_noto.get(\"adhoc_assortment_request_creation\"))\n", "\n", "    final_df_facility = final_df[\n", "        [\"Item ID\", \"City ID\", \"Facility ID\", \"Sub-state ID\", \"Reason ID\", \"DS_FS_Flag\"]\n", "    ]\n", "    format = \"%Y-%m-%d_%H-%M-%S\"\n", "    now_utc = datetime.now(timezone(\"UTC\"))\n", "    now_asia = now_utc.astimezone(timezone(\"Asia/Kolkata\"))\n", "\n", "    csv_file_path = \"Master Assortment v3 Bot_{today}_adhoc.csv\".format(\n", "        today=now_asia.strftime(format)\n", "    )\n", "    local_file_path = \"{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "    processed_file = final_df_facility.to_csv(\n", "        \"{filepath}\".format(filepath=local_file_path), index=False, header=True\n", "    )\n", "    file_path = \"assortment/{csv_file_path}\".format(csv_file_path=local_file_path)\n", "\n", "    master_assortment_v3_bulk_upload_type_id = 61\n", "\n", "    if len(final_df_facility) > 0:\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "        response = upload_to_bu(file_path, master_assortment_v3_bulk_upload_type_id)\n", "        status = \"Success\" if response.status_code == 201 else \"Failed\"\n", "\n", "        subject = \"Master Assortment Adhoc Update Result {today} adhoc -\".format(\n", "            today=now_asia.strftime(format)\n", "        )\n", "        html_content = (\n", "            \"<p>Hi,</p><br/>\"\n", "            \"<p>PFA request file uploaded on workdesk for Adhoc assortment.</p><br/>\"\n", "            \"<p>Please check the file upload status <a href='https://retail.grofers.com/#/bulk-upload-status'> here:</a> Upload Type: master_assortment_v3\"\n", "            \"<p>File Upload Status: {status}</p>\".format(status=status)\n", "        )\n", "        \"<br/><p>Thanks & Regards</p>\" \"<br/><p>Retail Tech PO</p>\" \"<p>PO-VMS</p>\" \"<p>Grofers India Pvt. Ltd.</p>\"\n", "\n", "        files = [local_file_path]\n", "        pb.send_email(\n", "            from_email=from_email,\n", "            to_email=to_email,\n", "            subject=subject,\n", "            html_content=html_content,\n", "            files=files,\n", "        )\n", "        os.remove(local_file_path)\n", "    else:\n", "        print(\"Assortment update not found\")\n", "\n", "    pb.send_slack_message(\n", "        channel=\"adhoc_job_notify\",\n", "        text=str(datetime.now()) + \" job_id = \" + str(response.json()),\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "vscode": {"interpreter": {"hash": "aee8b7b246df8f9039afb4144a1f6fd8d2ca17a180786b69acc140d282b71a49"}}}, "nbformat": 4, "nbformat_minor": 4}