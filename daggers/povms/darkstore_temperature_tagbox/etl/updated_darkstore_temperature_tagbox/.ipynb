{"cells": [{"cell_type": "code", "execution_count": 1, "id": "41f41666-dbda-46a7-a8c6-6982cb5f541e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: requests in /opt/conda/lib/python3.9/site-packages (2.27.1)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in /opt/conda/lib/python3.9/site-packages (from requests) (1.26.8)\n", "Requirement already satisfied: charset-normalizer~=2.0.0 in /opt/conda/lib/python3.9/site-packages (from requests) (2.0.11)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/conda/lib/python3.9/site-packages (from requests) (2022.12.7)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/conda/lib/python3.9/site-packages (from requests) (3.3)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["!pip install requests\n", "import requests\n", "from datetime import timedelta\n", "import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "import time"]}, {"cell_type": "code", "execution_count": 2, "id": "e0f65887-f2a9-4b66-a987-dca4804507fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-02-08T08:26:10\n", "2023-02-10T08:26:10\n"]}], "source": ["from datetime import datetime, timedelta\n", "\n", "log_begin = datetime.now() - <PERSON><PERSON><PERSON>(hours=48)\n", "log_begin = log_begin.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "print(log_begin)\n", "\n", "\n", "log_now = datetime.now().strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "print(log_now)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d27a8251-0596-4367-9d16-cd5a6ab97199", "metadata": {}, "outputs": [], "source": ["tag_list = \"21000302577,21000302576,21000302579,21000302573,21000302574,21000302610,21000302609,21000302608,21000302607,21000303391,21000303400,21000303702,21000303814,21000303928,21000303924,21000303927,21000303925,21000303919,21000303922,21000303087,21000302578,21000305170,21000305442,21000305443,21000302571,21000305524,21000305525,21000305526,21000305527,21000305528,21000305529,21000305530,21000305531,21000305532,21000305533,21000305534,21000305535,21000305536,21000305537,21000305538,21000305539,21000305540,21000305541,21000305542,21000305543,21000305544,21000305545,21000305546,21000305547,21000305548,21000305549,21000305550,21000305551,21000305552,21000305553,21000305554,21000305555,21000305077,21000304982,21000305066,21000305079,21000305065,21000305075,21000305080,21000305076,21000305064,21000304968,21000305067,21000304969,21000305072\""]}, {"cell_type": "code", "execution_count": 4, "id": "d458f700-0f47-4c87-921e-e0a2189bf746", "metadata": {"tags": []}, "outputs": [], "source": ["def api_call():    \n", "    url = f\"https://apis.tagbox.in/external/telemetry/temperature?startTime={log_begin}&endTime={log_now}&tagId={tag_list}&Content-Type=default\"\n", "    print(url)\n", "    payload = \"\"\n", "    headers = {'Ocp-Apim-Subscription-Key': '86e3fdfd046a41d1bdd3c8b8a26eaa7e'}\n", "\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "    print(response)\n", "    return(response)"]}, {"cell_type": "code", "execution_count": 5, "id": "0b382cca-78ea-4ceb-9b73-3f7463b7c800", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://apis.tagbox.in/external/telemetry/temperature?startTime=2023-02-08T08:26:10&endTime=2023-02-10T08:26:10&tagId=21000302577,21000302576,21000302579,21000302573,21000302574,21000302610,21000302609,21000302608,21000302607,21000303391,21000303400,21000303702,21000303814,21000303928,21000303924,21000303927,21000303925,21000303919,21000303922,21000303087,21000302578,21000305170,21000305442,21000305443,21000302571,21000305524,21000305525,21000305526,21000305527,21000305528,21000305529,21000305530,21000305531,21000305532,21000305533,21000305534,21000305535,21000305536,21000305537,21000305538,21000305539,21000305540,21000305541,21000305542,21000305543,21000305544,21000305545,21000305546,21000305547,21000305548,21000305549,21000305550,21000305551,21000305552,21000305553,21000305554,21000305555,21000305077,21000304982,21000305066,21000305079,21000305065,21000305075,21000305080,21000305076,21000305064,21000304968,21000305067,21000304969,21000305072&Content-Type=default\n", "<Response [200]>\n"]}], "source": ["cf = api_call()"]}, {"cell_type": "code", "execution_count": 6, "id": "61e29392-722a-4e17-b5c6-764e55e164b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data Fetched with 200\n"]}], "source": ["if cf.status_code == 200:\n", "    print('Data Fetched with 200')\n", "else: \n", "    time.sleep(90)\n", "    cf = api_call()"]}, {"cell_type": "code", "execution_count": 7, "id": "a00e6657-85b1-464a-a647-4276bc13a8e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tag_id</th>\n", "      <th>ts</th>\n", "      <th>temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21000303925</td>\n", "      <td>2023-02-10T08:20:00</td>\n", "      <td>-16.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21000302574</td>\n", "      <td>2023-02-10T08:20:00</td>\n", "      <td>-14.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21000303919</td>\n", "      <td>2023-02-10T08:20:00</td>\n", "      <td>4.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21000302577</td>\n", "      <td>2023-02-10T08:20:00</td>\n", "      <td>1.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-10T08:20:00</td>\n", "      <td>-5.37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11654</th>\n", "      <td>21000302573</td>\n", "      <td>2023-02-08T08:20:00</td>\n", "      <td>-18.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11655</th>\n", "      <td>21000305546</td>\n", "      <td>2023-02-08T08:20:00</td>\n", "      <td>-16.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11656</th>\n", "      <td>21000305543</td>\n", "      <td>2023-02-08T08:20:00</td>\n", "      <td>6.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11657</th>\n", "      <td>21000302579</td>\n", "      <td>2023-02-08T08:20:00</td>\n", "      <td>-19.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11658</th>\n", "      <td>21000304968</td>\n", "      <td>2023-02-08T08:20:00</td>\n", "      <td>10.04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>11659 rows × 3 columns</p>\n", "</div>"], "text/plain": ["            tag_id                   ts   temp\n", "0      21000303925  2023-02-10T08:20:00 -16.03\n", "1      21000302574  2023-02-10T08:20:00 -14.94\n", "2      21000303919  2023-02-10T08:20:00   4.35\n", "3      21000302577  2023-02-10T08:20:00   1.44\n", "4      21000303814  2023-02-10T08:20:00  -5.37\n", "...            ...                  ...    ...\n", "11654  21000302573  2023-02-08T08:20:00 -18.49\n", "11655  21000305546  2023-02-08T08:20:00 -16.92\n", "11656  21000305543  2023-02-08T08:20:00   6.56\n", "11657  21000302579  2023-02-08T08:20:00 -19.30\n", "11658  21000304968  2023-02-08T08:20:00  10.04\n", "\n", "[11659 rows x 3 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["json_response = (cf.text)\n", "response_new = json.loads(json_response)\n", "new = response_new['data']\n", "tel_data = new[0]['telData']\n", "dfItem = pd.DataFrame.from_records(tel_data)\n", "dfItem"]}, {"cell_type": "code", "execution_count": 8, "id": "69e80e9e-cb22-486a-9471-2198cfa2e56b", "metadata": {}, "outputs": [], "source": ["historical_data = pd.read_csv('begin_capture.csv',index_col = False)\n", "update_data = historical_data.append(dfItem)\n", "update_data = update_data.drop_duplicates()\n", "update_data.to_csv('begin_capture.csv',index=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "e3c64812-de56-4919-854e-b310a142b8ed", "metadata": {}, "outputs": [], "source": ["#----------Do Not ask why this was done, some stupid bug made me do this -------------#\n", "hi = pd.read_csv('begin_capture.csv',index_col = False)\n", "hi = hi.drop_duplicates()\n", "hi.to_csv('begin_capture.csv',index=False)"]}, {"cell_type": "raw", "id": "e503d283-f79c-46f4-83ea-0dd507a51062", "metadata": {}, "source": ["PREPARING TO PUSH TO TABLE"]}, {"cell_type": "code", "execution_count": 10, "id": "77b72cb6-7dbd-453e-93b6-399528cf4ac1", "metadata": {}, "outputs": [], "source": ["RAW_DATA = pd.read_csv('begin_capture.csv',index_col = False)"]}, {"cell_type": "code", "execution_count": 11, "id": "06b5bc4c-0a40-4591-a38e-1cbae0093e28", "metadata": {}, "outputs": [], "source": ["tag_ids= RAW_DATA[['tag_id']].drop_duplicates()\n", "time_stamps= RAW_DATA[['ts']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": 12, "id": "d9391323-2fcc-4646-931b-01ad0ecd777a", "metadata": {}, "outputs": [], "source": ["tag_ids['tmp'] = 1\n", "time_stamps['tmp'] = 1"]}, {"cell_type": "code", "execution_count": 13, "id": "85b7824b-84d8-45ce-8586-c3b208ce68f4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tag_id</th>\n", "      <th>ts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:40:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:30:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:20:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:10:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22875</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-09T23:10:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22876</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-09T23:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22877</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10T08:10:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22878</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10T08:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22879</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10T08:20:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>22880 rows × 2 columns</p>\n", "</div>"], "text/plain": ["            tag_id                   ts\n", "0      21000303814  2023-02-09T21:50:00\n", "1      21000303814  2023-02-09T21:40:00\n", "2      21000303814  2023-02-09T21:30:00\n", "3      21000303814  2023-02-09T21:20:00\n", "4      21000303814  2023-02-09T21:10:00\n", "...            ...                  ...\n", "22875  21000305527  2023-02-09T23:10:00\n", "22876  21000305527  2023-02-09T23:00:00\n", "22877  21000305527  2023-02-10T08:10:00\n", "22878  21000305527  2023-02-10T08:00:00\n", "22879  21000305527  2023-02-10T08:20:00\n", "\n", "[22880 rows x 2 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dataframe = pd.merge(tag_ids, time_stamps, on=['tmp'])\n", "all_dataframe = all_dataframe.drop('tmp', axis=1)\n", "all_dataframe"]}, {"cell_type": "code", "execution_count": 14, "id": "5609d2f3-8594-45bb-a92a-92b8c17bb0aa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tag_id</th>\n", "      <th>ts</th>\n", "      <th>temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:50:00</td>\n", "      <td>-4.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:50:00</td>\n", "      <td>-4.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:40:00</td>\n", "      <td>-4.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:40:00</td>\n", "      <td>-4.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09T21:30:00</td>\n", "      <td>-4.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23644</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-09T23:10:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23645</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-09T23:00:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23646</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10T08:10:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23647</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10T08:00:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23648</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10T08:20:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>23649 rows × 3 columns</p>\n", "</div>"], "text/plain": ["            tag_id                   ts  temp\n", "0      21000303814  2023-02-09T21:50:00 -4.62\n", "1      21000303814  2023-02-09T21:50:00 -4.61\n", "2      21000303814  2023-02-09T21:40:00 -4.70\n", "3      21000303814  2023-02-09T21:40:00 -4.69\n", "4      21000303814  2023-02-09T21:30:00 -4.72\n", "...            ...                  ...   ...\n", "23644  21000305527  2023-02-09T23:10:00   NaN\n", "23645  21000305527  2023-02-09T23:00:00   NaN\n", "23646  21000305527  2023-02-10T08:10:00   NaN\n", "23647  21000305527  2023-02-10T08:00:00   NaN\n", "23648  21000305527  2023-02-10T08:20:00   NaN\n", "\n", "[23649 rows x 3 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dataframe = pd.merge(all_dataframe,RAW_DATA, on = ['tag_id','ts'], how = 'left')\n", "all_dataframe"]}, {"cell_type": "code", "execution_count": 15, "id": "06fb9a6b-6bdb-4019-848e-c937c4177d0a", "metadata": {}, "outputs": [], "source": ["all_dataframe['capture_ts'] = all_dataframe['ts'].apply(lambda x : datetime.fromisoformat(x))"]}, {"cell_type": "code", "execution_count": 16, "id": "22bba76f-b290-42cf-b37b-0ca27d581368", "metadata": {}, "outputs": [], "source": ["all_dataframe = all_dataframe[['tag_id','capture_ts','temp']]"]}, {"cell_type": "code", "execution_count": 18, "id": "945c5b84-096a-4fc0-949f-e5d79e4f85ec", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_399/2512844401.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  all_dataframe['temp'] = all_dataframe['temp'].astype(float)\n", "/tmp/ipykernel_399/2512844401.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  all_dataframe['tag_id'] = all_dataframe['tag_id'].astype('int64')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tag_id</th>\n", "      <th>capture_ts</th>\n", "      <th>temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09 21:50:00</td>\n", "      <td>-4.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09 21:50:00</td>\n", "      <td>-4.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09 21:40:00</td>\n", "      <td>-4.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09 21:40:00</td>\n", "      <td>-4.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-09 21:30:00</td>\n", "      <td>-4.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23644</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-09 23:10:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23645</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-09 23:00:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23646</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 08:10:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23647</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 08:00:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23648</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 08:20:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>23649 rows × 3 columns</p>\n", "</div>"], "text/plain": ["            tag_id          capture_ts  temp\n", "0      21000303814 2023-02-09 21:50:00 -4.62\n", "1      21000303814 2023-02-09 21:50:00 -4.61\n", "2      21000303814 2023-02-09 21:40:00 -4.70\n", "3      21000303814 2023-02-09 21:40:00 -4.69\n", "4      21000303814 2023-02-09 21:30:00 -4.72\n", "...            ...                 ...   ...\n", "23644  21000305527 2023-02-09 23:10:00   NaN\n", "23645  21000305527 2023-02-09 23:00:00   NaN\n", "23646  21000305527 2023-02-10 08:10:00   NaN\n", "23647  21000305527 2023-02-10 08:00:00   NaN\n", "23648  21000305527 2023-02-10 08:20:00   NaN\n", "\n", "[23649 rows x 3 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dataframe['temp'] = all_dataframe['temp'].astype(float)\n", "all_dataframe['tag_id'] = all_dataframe['tag_id'].astype('int64')\n", "all_dataframe"]}, {"cell_type": "code", "execution_count": 19, "id": "6082b828-ab29-4356-98b0-be69bb96603b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_399/4023074614.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  all_dataframe['capture_ts'] = all_dataframe['capture_ts'].apply(lambda x : x + timedelta(hours = 5.5))\n", "/opt/conda/lib/python3.9/site-packages/pandas/core/frame.py:5039: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  return super().rename(\n"]}], "source": ["all_dataframe['capture_ts'] = all_dataframe['capture_ts'].apply(lambda x : x + timedelta(hours = 5.5))\n", "all_dataframe.rename(columns = {'temp':'temperature'}, inplace = True)"]}, {"cell_type": "code", "execution_count": 20, "id": "3cf062c5-b69e-441a-89d7-51abca48f363", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tag_id</th>\n", "      <th>capture_ts</th>\n", "      <th>temperature</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-10 03:20:00</td>\n", "      <td>-4.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-10 03:20:00</td>\n", "      <td>-4.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-10 03:10:00</td>\n", "      <td>-4.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-10 03:10:00</td>\n", "      <td>-4.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21000303814</td>\n", "      <td>2023-02-10 03:00:00</td>\n", "      <td>-4.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23644</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 04:40:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23645</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 04:30:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23646</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 13:40:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23647</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 13:30:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23648</th>\n", "      <td>21000305527</td>\n", "      <td>2023-02-10 13:50:00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>23649 rows × 3 columns</p>\n", "</div>"], "text/plain": ["            tag_id          capture_ts  temperature\n", "0      21000303814 2023-02-10 03:20:00        -4.62\n", "1      21000303814 2023-02-10 03:20:00        -4.61\n", "2      21000303814 2023-02-10 03:10:00        -4.70\n", "3      21000303814 2023-02-10 03:10:00        -4.69\n", "4      21000303814 2023-02-10 03:00:00        -4.72\n", "...            ...                 ...          ...\n", "23644  21000305527 2023-02-10 04:40:00          NaN\n", "23645  21000305527 2023-02-10 04:30:00          NaN\n", "23646  21000305527 2023-02-10 13:40:00          NaN\n", "23647  21000305527 2023-02-10 13:30:00          NaN\n", "23648  21000305527 2023-02-10 13:50:00          NaN\n", "\n", "[23649 rows x 3 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dataframe"]}, {"cell_type": "code", "execution_count": 22, "id": "45717662-12ea-41b2-ab6c-8c99505f2cfe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Aag laga di!\n"]}], "source": ["datatypes = [\n", "    {\"name\": \"tag_id\", \"type\": \"bigint\", \"description\": \"Node ID of respective Tagnodes\"},\n", "    {\"name\": \"capture_ts\", \"type\": \"datetime\", \"description\": \"Date_time\"},\n", "    {\n", "        \"name\": \"temperature\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Temperature record for that particular node, timestamp combination\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"tagbox_darkstore_temperature_logs\",\n", "    \"column_dtypes\": datatypes,\n", "    \"primary_key\": [\"tag_id\", \"capture_ts\"],\n", "    \"sortkey\": [\"capture_ts\", \"tag_id\"],\n", "    \"incremental_key\": \"capture_ts\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Tagbox Data for all Nodes\",  # Description of the table being sent to redshift\n", "}\n", "pb.to_redshift(all_dataframe, **kwargs)\n", "print('Built Tagbox!')"]}, {"cell_type": "code", "execution_count": null, "id": "508a1f2d-e18a-4aa9-85ca-4d4e96df4026", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}