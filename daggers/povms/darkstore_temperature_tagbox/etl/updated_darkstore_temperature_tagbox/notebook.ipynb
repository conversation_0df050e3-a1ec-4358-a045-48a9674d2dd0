{"cells": [{"cell_type": "code", "execution_count": null, "id": "41f41666-dbda-46a7-a8c6-6982cb5f541e", "metadata": {}, "outputs": [], "source": ["!pip install requests\n", "import requests\n", "from datetime import timedelta\n", "import pandas as pd\n", "import pencilbox as pb\n", "import json\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "e0f65887-f2a9-4b66-a987-dca4804507fe", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "log_begin = datetime.now() - <PERSON><PERSON><PERSON>(hours=48)\n", "log_begin = log_begin.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "print(log_begin)\n", "\n", "\n", "log_now = datetime.now().strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "print(log_now)"]}, {"cell_type": "code", "execution_count": null, "id": "d27a8251-0596-4367-9d16-cd5a6ab97199", "metadata": {}, "outputs": [], "source": ["tag_list = \"21000302577,21000302576,21000302579,21000302573,21000302574,21000302610,21000302609,21000302608,21000302607,21000303391,21000303400,21000303702,21000303814,21000303928,21000303924,21000303927,21000303925,21000303919,21000303922,21000303087,21000302578,21000305170,21000305442,21000305443,21000302571,21000305524,21000305525,21000305526,21000305527,21000305528,21000305529,21000305530,21000305531,21000305532,21000305533,21000305534,21000305535,21000305536,21000305537,21000305538,21000305539,21000305540,21000305541,21000305542,21000305543,21000305544,21000305545,21000305546,21000305547,21000305548,21000305549,21000305550,21000305551,21000305552,21000305553,21000305554,21000305555,21000305077,21000304982,21000305066,21000305079,21000305065,21000305075,21000305080,21000305076,21000305064,21000304968,21000305067,21000304969,21000305072\""]}, {"cell_type": "code", "execution_count": null, "id": "d458f700-0f47-4c87-921e-e0a2189bf746", "metadata": {"tags": []}, "outputs": [], "source": ["def api_call():\n", "    url = f\"https://apis.tagbox.in/external/telemetry/temperature?startTime={log_begin}&endTime={log_now}&tagId={tag_list}&Content-Type=default\"\n", "    print(url)\n", "    payload = \"\"\n", "    headers = {\"Ocp-Apim-Subscription-Key\": \"86e3fdfd046a41d1bdd3c8b8a26eaa7e\"}\n", "\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "    print(response)\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "id": "0b382cca-78ea-4ceb-9b73-3f7463b7c800", "metadata": {}, "outputs": [], "source": ["cf = api_call()"]}, {"cell_type": "code", "execution_count": null, "id": "61e29392-722a-4e17-b5c6-764e55e164b7", "metadata": {}, "outputs": [], "source": ["if cf.status_code == 200:\n", "    print(\"Data Fetched with 200\")\n", "else:\n", "    time.sleep(90)\n", "    cf = api_call()"]}, {"cell_type": "code", "execution_count": null, "id": "a00e6657-85b1-464a-a647-4276bc13a8e2", "metadata": {}, "outputs": [], "source": ["json_response = cf.text\n", "response_new = json.loads(json_response)\n", "new = response_new[\"data\"]\n", "tel_data = new[0][\"telData\"]\n", "dfItem = pd.DataFrame.from_records(tel_data)\n", "dfItem"]}, {"cell_type": "code", "execution_count": null, "id": "69e80e9e-cb22-486a-9471-2198cfa2e56b", "metadata": {}, "outputs": [], "source": ["# historical_data = pd.read_csv(\"begin_capture.csv\", index_col=False)\n", "# update_data = historical_data.append(dfItem)\n", "# update_data = update_data.drop_duplicates()\n", "# update_data.to_csv(\"begin_capture.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "e3c64812-de56-4919-854e-b310a142b8ed", "metadata": {}, "outputs": [], "source": ["# # ----------Do Not ask why this was done, some stupid bug made me do this -------------#\n", "# hi = pd.read_csv(\"begin_capture.csv\", index_col=False)\n", "# hi = hi.drop_duplicates()\n", "# hi.to_csv(\"begin_capture.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "77b72cb6-7dbd-453e-93b6-399528cf4ac1", "metadata": {}, "outputs": [], "source": ["# RAW_DATA = pd.read_csv(\"begin_capture.csv\", index_col=False)"]}, {"cell_type": "code", "execution_count": null, "id": "06b5bc4c-0a40-4591-a38e-1cbae0093e28", "metadata": {}, "outputs": [], "source": ["# tag_ids = RAW_DATA[[\"tag_id\"]].drop_duplicates()\n", "# time_stamps = RAW_DATA[[\"ts\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "d9391323-2fcc-4646-931b-01ad0ecd777a", "metadata": {}, "outputs": [], "source": ["# tag_ids[\"tmp\"] = 1\n", "# time_stamps[\"tmp\"] = 1"]}, {"cell_type": "code", "execution_count": null, "id": "85b7824b-84d8-45ce-8586-c3b208ce68f4", "metadata": {}, "outputs": [], "source": ["# all_dataframe = pd.merge(tag_ids, time_stamps, on=[\"tmp\"])\n", "# all_dataframe = all_dataframe.drop(\"tmp\", axis=1)\n", "# all_dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "5609d2f3-8594-45bb-a92a-92b8c17bb0aa", "metadata": {}, "outputs": [], "source": ["# all_dataframe = pd.merge(all_dataframe, RAW_DATA, on=[\"tag_id\", \"ts\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "06fb9a6b-6bdb-4019-848e-c937c4177d0a", "metadata": {}, "outputs": [], "source": ["all_dataframe = dfItem\n", "all_dataframe[\"capture_ts\"] = all_dataframe[\"ts\"].apply(lambda x: datetime.fromisoformat(x))"]}, {"cell_type": "code", "execution_count": null, "id": "22bba76f-b290-42cf-b37b-0ca27d581368", "metadata": {}, "outputs": [], "source": ["all_dataframe = all_dataframe[[\"tag_id\", \"capture_ts\", \"temp\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "945c5b84-096a-4fc0-949f-e5d79e4f85ec", "metadata": {}, "outputs": [], "source": ["all_dataframe[\"temp\"] = all_dataframe[\"temp\"].astype(float)\n", "all_dataframe[\"tag_id\"] = all_dataframe[\"tag_id\"].astype(\"int64\")\n", "all_dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "6082b828-ab29-4356-98b0-be69bb96603b", "metadata": {}, "outputs": [], "source": ["all_dataframe[\"capture_ts\"] = all_dataframe[\"capture_ts\"].apply(lambda x: x + timedelta(hours=5.5))\n", "all_dataframe.rename(columns={\"temp\": \"temperature\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3cf062c5-b69e-441a-89d7-51abca48f363", "metadata": {}, "outputs": [], "source": ["all_dataframe"]}, {"cell_type": "code", "execution_count": null, "id": "45717662-12ea-41b2-ab6c-8c99505f2cfe", "metadata": {}, "outputs": [], "source": ["datatypes = [\n", "    {\n", "        \"name\": \"tag_id\",\n", "        \"type\": \"bigint\",\n", "        \"description\": \"Node ID of respective Tagnodes\",\n", "    },\n", "    {\"name\": \"capture_ts\", \"type\": \"datetime\", \"description\": \"Date_time\"},\n", "    {\n", "        \"name\": \"temperature\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Temperature record for that particular node, timestamp combination\",\n", "    },\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"tagbox_darkstore_temperature_logs\",\n", "    \"column_dtypes\": datatypes,\n", "    \"primary_key\": [\"tag_id\", \"capture_ts\"],\n", "    \"sortkey\": [\"capture_ts\", \"tag_id\"],\n", "    \"incremental_key\": \"capture_ts\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Tagbox Data for all Nodes\",  # Description of the table being sent to redshift\n", "    # \"force_upsert_without_increment_check\": True\n", "}\n", "pb.to_redshift(all_dataframe, **kwargs)\n", "print(\"Built Tagbox!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}