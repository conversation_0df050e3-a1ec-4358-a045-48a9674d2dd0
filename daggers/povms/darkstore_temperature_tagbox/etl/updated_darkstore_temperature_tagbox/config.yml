alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: updated_darkstore_temperature_tagbox
dag_type: etl
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03R8Q6UZ71
path: povms/darkstore_temperature_tagbox/etl/updated_darkstore_temperature_tagbox
paused: true
pool: povms_pool
project_name: darkstore_temperature_tagbox
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: '*/30 * * * *'
  start_date: '2023-02-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
