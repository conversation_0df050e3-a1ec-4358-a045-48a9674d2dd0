alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: dwelling_inv
dag_type: etl
escalation_priority: low
execution_timeout: 360
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07JD7JA0LR
path: povms/dwelling_inv_track/etl/dwelling_inv
paused: false
pool: povms_pool
project_name: dwelling_inv_track
schedule:
  end_date: '2025-07-14T00:00:00'
  interval: 30 20 * * *
  start_date: '2025-06-12T00:00:00'
schedule_type: fixed
sla: 180 minutes
support_files: []
tags: []
template_name: notebook
version: 10
