{"cells": [{"cell_type": "code", "execution_count": null, "id": "6f2bef81-0fbf-4c12-8651-4c0b7da196b5", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import gc\n", "import time\n", "import ast\n", "import os\n", "import json\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "be94dddd-1870-41d7-af1c-cba7fe355c4e", "metadata": {}, "outputs": [], "source": ["env = \"prod\""]}, {"cell_type": "code", "execution_count": null, "id": "a9aa2e79-fbcd-449c-a6d1-3f6619798006", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)\n", "\n", "\n", "# GSheet to DataFrame\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# DataFrame to GSheet\n", "def df_to_gsheet(df, sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_sheets(df, sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Write Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Write {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and for a dataframe of size {size}\"\n", "            )\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "# Convert Pandas DataFrame to Image\n", "def render_mpl_table(\n", "    data,\n", "    col_width=5,\n", "    row_height=0.825,\n", "    font_size=18,\n", "    header_color=\"#fed8b1\",\n", "    row_colors=[\"#f1f1f2\", \"w\"],\n", "    edge_color=\"black\",\n", "    bbox=[0, 0, 1, 1],\n", "    header_columns=0,\n", "    ax=None,\n", "    # cellLoc='center',\n", "    **kwargs,\n", "):\n", "    if ax is None:\n", "        size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array([col_width, row_height])\n", "        fig, ax = plt.subplots(figsize=size)\n", "        ax.axis(\"off\")\n", "    mpl_table = ax.table(\n", "        cellText=data.values,\n", "        bbox=bbox,\n", "        colLabels=data.columns,\n", "        cellLoc=\"center\",\n", "        **kwargs,\n", "    )\n", "\n", "    mpl_table.auto_set_font_size(False)\n", "    mpl_table.set_fontsize(font_size)\n", "\n", "    for k, cell in mpl_table._cells.items():\n", "        cell.set_edgecolor(edge_color)\n", "        if k[0] == 0 or k[1] < header_columns:\n", "            cell.set_text_props(weight=\"bold\", color=\"black\")\n", "            cell.set_facecolor(header_color)\n", "        else:\n", "            cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "    mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "    return ax.get_figure(), ax\n", "\n", "\n", "def send_slack_message(input_text, file_input=None):\n", "    for i in range(len(slack_channels)):\n", "        if file_input is None:\n", "            pb.send_slack_message(channel=slack_channels[i], text=input_text)\n", "        else:\n", "            pb.send_slack_message(channel=slack_channels[i], text=input_text, files=[file_input])\n", "\n", "\n", "def create_table_image(\n", "    df, output_path=\"dataframe_image.png\", dpi=200, title=None, colColor=\"#FFDE21\"\n", "):\n", "    \"\"\"\n", "    Generates a clean image of a DataFrame as a table with a bold header and optional title.\n", "    Output has minimal whitespace and a white background.\n", "\n", "    Parameters:\n", "    - df (pd.DataFrame): DataFrame to visualize.\n", "    - output_path (str): File path to save the image.\n", "    - dpi (int): Image resolution.\n", "    - title (str or None): Optional title displayed above the table.\n", "    \"\"\"\n", "    num_rows, num_cols = df.shape\n", "    fig_width = min(20, max(4, num_cols * 1.2))\n", "    fig_height = min(10, max(1.5, num_rows * 0.4)) + (0.5 if title else 0)\n", "\n", "    fig, ax = plt.subplots(\n", "        figsize=(fig_width, fig_height), constrained_layout=True, facecolor=\"white\"\n", "    )\n", "\n", "    ax.axis(\"off\")  # No axes\n", "\n", "    # Create table\n", "    table = ax.table(\n", "        cellText=df.values,\n", "        colLabels=[str(col) for col in df.columns],\n", "        cellLoc=\"center\",\n", "        loc=\"center\",\n", "        colColours=[colColor] * num_cols,\n", "    )\n", "    table.auto_set_font_size(False)\n", "    table.set_fontsize(10)\n", "    table.auto_set_column_width(col=list(range(num_cols)))\n", "    table.scale(1, 1.2)\n", "\n", "    # Add title if provided\n", "    if title:\n", "        fig.suptitle(title, fontsize=12, weight=\"bold\", y=1.02)\n", "\n", "    # Save with minimal padding and tight layout\n", "    plt.savefig(output_path, dpi=dpi, bbox_inches=\"tight\", facecolor=\"white\", pad_inches=0.2)\n", "\n", "    if env == \"dev\":\n", "        plt.show()\n", "\n", "    plt.close(fig)\n", "    print(f\"Image saved to {output_path}\")\n", "\n", "\n", "def human_format_rupees(num):\n", "    try:\n", "        num = float(num)\n", "    except (ValueErro<PERSON>, TypeError):\n", "        return \"-\"\n", "\n", "    if num >= 1e7:\n", "        return f\"₹{num/1e7:.2f}Cr\"\n", "    elif num >= 1e5:\n", "        return f\"₹{num/1e5:.2f}L\"\n", "    elif num >= 1e3:\n", "        return f\"₹{num/1e3:.2f}K\"\n", "    else:\n", "        return f\"₹{num:.2f}\""]}, {"cell_type": "code", "execution_count": null, "id": "5be56af3-2b30-4114-8a75-24b49015f227", "metadata": {}, "outputs": [], "source": ["if env == \"dev\":\n", "    from IPython.display import display, clear_output\n", "    import ipywidgets as widgets\n", "\n", "    def upload_and_process_csv(\n", "        expected_columns=[],\n", "        save_path=\"~/Shared/<PERSON><PERSON><PERSON>/hooks/util_functions/upload_garbage/\",\n", "    ):\n", "        save_path = os.path.expanduser(save_path)\n", "        os.makedirs(save_path, exist_ok=True)\n", "\n", "        uploader = widgets.FileUpload(accept=\".csv\", multiple=False)\n", "        display(uploader)\n", "\n", "        output = widgets.Output()\n", "        display(output)\n", "\n", "        result = {\"df\": None}\n", "\n", "        def on_upload_change(change):\n", "            with output:\n", "                clear_output(wait=True)\n", "\n", "                if not uploader.value:\n", "                    print(\"❌ No file uploaded.\")\n", "                    return\n", "\n", "                uploaded_file = next(iter(uploader.value.values()))\n", "                file_content = uploaded_file[\"content\"]\n", "\n", "                df = pd.read_csv(pd.io.common.BytesIO(file_content))\n", "                if len(expected_columns) > 0 and not set(expected_columns).issubset(df.columns):\n", "                    print(\"❌ Error: Missing expected columns in the uploaded file.\")\n", "                    return\n", "\n", "                print(\"✅ Columns validated!\")\n", "\n", "                original_file_name = uploaded_file[\"metadata\"][\"name\"]\n", "                timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "                file_name, file_ext = os.path.splitext(original_file_name)\n", "                new_file_name = f\"{file_name}_{timestamp}{file_ext}\"\n", "                full_path = os.path.join(save_path, new_file_name)\n", "\n", "                with open(full_path, \"wb\") as f:\n", "                    f.write(file_content)\n", "\n", "                print(f\"✅ File saved at: {full_path}\")\n", "\n", "                print(\"✅ File successfully processed!\")\n", "                display(df.head())\n", "                uploader.close()\n", "                result[\"df\"] = df\n", "\n", "        uploader.observe(on_upload_change, names=\"value\")\n", "        return result\n", "\n", "    print(\"\\n upload for config below\")\n", "    upload_container_config = upload_and_process_csv()"]}, {"cell_type": "code", "execution_count": null, "id": "70c79aa7-b5c8-47ed-a2f6-0ccbb27bd8bf", "metadata": {}, "outputs": [], "source": ["if env == \"dev\":\n", "    config_df = upload_container_config[\"df\"]\n", "else:\n", "    config_df = pb.from_sheets(\"1t3L1j-QxsJg6egSmtfj54VgtXeU2K8j8G-POi4KMR4Y\", \"config\")"]}, {"cell_type": "code", "execution_count": null, "id": "5a8b4e7d-a7b6-4156-873a-d9238990c8f5", "metadata": {}, "outputs": [], "source": ["config_dict = dict(zip(config_df[\"key\"], config_df[\"value\"]))\n", "slack_channels = [config_dict.get(\"slack_channels\", \"C081W042ZPS\")]"]}, {"cell_type": "code", "execution_count": null, "id": "e9179d52-06d3-43c9-b51d-ea27b001c2aa", "metadata": {}, "outputs": [], "source": ["min_store_age = int(config_dict.get(\"min_store_age\", 10))\n", "min_store_avg_carts = int(config_dict.get(\"min_store_avg_carts\", 100))\n", "festive_lookback_day = int(config_dict.get(\"festive_lookback_day\", 10))\n", "festive_forward_day = int(config_dict.get(\"festive_forward_day\", 3))\n", "dwelling_flag_depletion_days = int(config_dict.get(\"dwelling_flag_depletion_days\", 5))\n", "fetch_cpd_upper_bound = int(config_dict.get(\"fetch_cpd_upper_bound\", 1))\n", "\n", "sale_day_n = int(config_dict.get(\"sale_day_n\", 7))\n", "sale_day_aggfunc = config_dict.get(\"sale_day_aggfunc\", \"mean\")"]}, {"cell_type": "code", "execution_count": null, "id": "45be7197-8389-4f90-835c-d30b531d727f", "metadata": {}, "outputs": [], "source": ["try:\n", "    input_config = ast.literal_eval(config_dict.get(\"rsto_config\", \"{}\"))\n", "except Exception:\n", "    input_config = {\n", "        \"default_doi\": 6,\n", "        \"buffer_cap_doi\": 10,\n", "        \"buffer_quantity\": 0,\n", "        \"default_quantity\": 3,\n", "        \"qmax_default_doi_max\": 4,\n", "        \"qmax_default_quantity\": 1,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "id": "964fd4eb-0921-4083-921f-d466040052e6", "metadata": {}, "outputs": [], "source": ["full_config = {\n", "    \"rsto_comp_config\": input_config,\n", "    \"min_store_age\": min_store_age,\n", "    \"min_store_avg_carts\": min_store_avg_carts,\n", "    \"festive_lookback_day\": festive_lookback_day,\n", "    \"festive_forward_day\": festive_forward_day,\n", "    \"dwelling_flag_depletion_days\": dwelling_flag_depletion_days,\n", "    \"fetch_cpd_upper_bound\": fetch_cpd_upper_bound,\n", "    \"sale_day_n\": sale_day_n,\n", "    \"sale_day_aggfunc\": sale_day_aggfunc,\n", "}\n", "\n", "json_config_str = json.dumps(full_config)\n", "json_config_str"]}, {"cell_type": "code", "execution_count": null, "id": "8e195654-e024-4ded-bcd4-3812e97d57e1", "metadata": {}, "outputs": [], "source": ["config_pretty = \"\\n\".join(\n", "    [\n", "        f\"*_DWELLING INVENTORY CONFIG_*\",\n", "        f\"*rsto_comp_config*: `{input_config}`\",\n", "        f\"*min_store_age*: {min_store_age}\",\n", "        f\"*min_store_avg_carts*: {min_store_avg_carts}\",\n", "        f\"*festive_lookback_day*: {festive_lookback_day}\",\n", "        f\"*festive_forward_day*: {festive_forward_day}\",\n", "        f\"*dwelling_flag_depletion_days*: {dwelling_flag_depletion_days}\",\n", "        f\"*fetch_cpd_upper_bound*: {fetch_cpd_upper_bound}\",\n", "        \"\",\n", "        f\"*sale_day_n*: {sale_day_n}\",\n", "        f\"*sale_day_aggfunc*: `{sale_day_aggfunc}`\",\n", "    ]\n", ")\n", "\n", "send_slack_message(config_pretty)"]}, {"cell_type": "code", "execution_count": null, "id": "5380f89b-b70a-4d06-a5da-488e5e055e61", "metadata": {}, "outputs": [], "source": ["df_store_base = read_sql_query(\n", "    sql=f\"\"\"\n", "        with fe_sa as (-- fe store_age\n", "with sales as (\n", "    select\n", "        outlet_id,\n", "        date(cart_checkout_ts_ist) as date_,\n", "        count(distinct cart_id) as cart_count\n", "    from dwh.fact_sales_order_details\n", "    where order_create_dt_ist > current_date - interval '120' day\n", "    group by 1, 2\n", "        ),\n", "        \n", "live_day_count as (\n", "    select\n", "        outlet_id, \n", "        max(date_) as latest_date,\n", "        count(date_) as live_day_count\n", "    from sales s\n", "    where cart_count >= 5\n", "    group by 1\n", ")\n", "\n", "select \n", "    fm.facility_id, \n", "    fm.id AS outlet_id, \n", "    fm.name AS outlet_name, \n", "    fm.tax_location_id as city_id,\n", "    ldc.live_day_count as store_age,\n", "    ldc.latest_date\n", "from live_day_count ldc\n", "left join retail.console_outlet fm on fm.id = ldc.outlet_id AND business_type_id = 7 AND lake_active_record AND fm.active = 1\n", "where latest_date >= current_date - interval '3' day), fe_carts as (-- FE l7 DoD carts\n", "with dod_carts_base as (\n", "    select \n", "        insert_ds_ist as date_,\n", "        outlet_id,\n", "        count(distinct cart_id) as carts\n", "    from supply_etls.date_hourly_sales_details \n", "    where insert_ds_ist between cast(current_date - interval '8' day as varchar) and cast(current_date - interval '1' day as varchar)\n", "    group by 1,2\n", ")\n", "\n", "select outlet_id, avg(carts) as carts from dod_carts_base \n", "group by 1\n", "),\n", "    \n", "        base as (\n", "            select\n", "                fe_sa.facility_id,\n", "                fe_sa.outlet_id,\n", "                fe_sa.outlet_name,\n", "                fe_sa.store_age,\n", "                fe_carts.carts as avg_carts\n", "            from fe_sa\n", "            left join fe_carts on fe_carts.outlet_id = fe_sa.outlet_id\n", "        )\n", "        \n", "        select * from base\n", "        \n", " where store_age > {min_store_age} and avg_carts > {min_store_avg_carts}\"\"\",\n", "    con=CON_TRINO,\n", ")\n", "\n", "print(f\"outlet_count: {df_store_base.shape[0]}\")\n", "df_store_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e7beb62b-b26b-4c39-aee0-7e037c487cd5", "metadata": {}, "outputs": [], "source": ["len(df_store_base[df_store_base.duplicated(\"outlet_id\")])"]}, {"cell_type": "code", "execution_count": null, "id": "151c9c3b-9af0-4c0a-8633-9424a283595f", "metadata": {}, "outputs": [], "source": ["temp = read_sql_query(\n", "    sql=f\"\"\"\n", "        with ranked_festivals as (\n", "            select \n", "                item_id,\n", "                event_name as reason,\n", "                cast(bpec.event_id as varchar) as event_id,\n", "                assortment_launch_type,\n", "                1 as priority,\n", "                planned_qty\n", "            from ars.bulk_process_event_planner bpec\n", "            left join supply_etls.event_details ed on ed.id = bpec.event_id\n", "            inner join rpc.supply_event_info \n", "                on bpec.event_id = supply_event_info.id \n", "                and supply_event_info.active\n", "            where cast(current_date as date) >= sale_start_date - interval '{festive_lookback_day}' day\n", "              and cast(current_date as date) <= sale_end_date + interval '{festive_forward_day}' day\n", "              and bpec.active = 1\n", "              and planned_qty > 0\n", "              and partition_field is not null\n", "              and supply_event_info.lake_active_record\n", "              and bpec.lake_active_record\n", "        ),\n", "        no_rsto_items as (\n", "            select\n", "                cast(regexp_replace(item_id, '[\\\\[\\\\]]', '') as bigint) as item_id,\n", "                'no_rsto_items' as reason,\n", "                '00' as event_id,\n", "                'no_rsto' as assortment_launch_type,\n", "                2 as priority,\n", "                0 as planned_qty\n", "            from ars.ars_config, lateral( \n", "                select \n", "                    item_id\n", "                from unnest(cast(split(value, ',') as array(varchar))) as t(item_id)\n", "                )\n", "            where name = 'no_rsto_items'\n", "              and facility_id = 0\n", "              and lake_active_record\n", "        ),\n", "        base as (\n", "            select * from ranked_festivals\n", "            union all\n", "            select * from no_rsto_items\n", "        ),\n", "        aggregated as (\n", "            select \n", "                item_id,\n", "                array_agg(distinct reason) as reasons,\n", "                array_agg(distinct event_id) as event_ids,\n", "                array_agg(distinct assortment_launch_type) as assortment_types\n", "            from base\n", "            group by item_id\n", "        )\n", "        select \n", "            item_id,\n", "            array_join(reasons, ',') as reason,\n", "            array_join(event_ids, ',') as event_id,\n", "            array_join(assortment_types, ',') as assortment_launch_type\n", "        from aggregated\n", "    \"\"\",\n", "    con=CON_TRINO,\n", ")\n", "\n", "temp.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "9225fbfa-31f0-4729-bcda-451e5e4b7dcd", "metadata": {}, "outputs": [], "source": ["len(temp[temp.duplicated(\"item_id\")])"]}, {"cell_type": "code", "execution_count": null, "id": "19fb4914-ae7d-4ce5-a7f3-2da40da0e6ef", "metadata": {}, "outputs": [], "source": ["spillover_types = read_sql_query(\n", "    f\"\"\" select value from ars.ars_config where name = 'spillover_enabled_storage_types' \"\"\",\n", "    con=CON_TRINO,\n", ")\n", "spillover_types = ast.literal_eval(spillover_types.iloc[0].value)\n", "spillover_types"]}, {"cell_type": "code", "execution_count": null, "id": "781be1e3-e0ba-45b5-9990-c214a13b7c96", "metadata": {}, "outputs": [], "source": ["scan_tag_items = read_sql_query(\n", "    sql=f\"\"\" select \n", "                item_id \n", "   from rpc.item_tag_mapping\n", "   where active\n", "   AND tag_type_id = 5 AND tag_value in ('2', '3')\n", "   AND lake_active_record\"\"\",\n", "    con=CON_TRINO,\n", ")\n", "scan_tag_items = scan_tag_items.item_id.unique()\n", "print(len(scan_tag_items))\n", "\n", "sampling_items = read_sql_query(\n", "    sql=f\"\"\" With base as (\n", "  SELECT\n", "    item_id,\n", "    'FNV_PAAS_CUT' AS reason\n", "  FROM\n", "    rpc.item_category_details\n", "  WHERE\n", "    l0_id IN (1487) -- FNV_L0_ID\n", "    OR l1_id IN (2039) -- PRINTING_PAPER\n", "  UNION\n", "  SELECT\n", "    item_id,\n", "    'PACKAGING' AS reason\n", "  FROM\n", "    rpc.item_details\n", "  WHERE\n", "    handling_type IN ('8') -- PACKAGING MATERIALS\n", "  union\n", "  select\n", "    distinct item_id,\n", "    'SAMPLING' AS reason -- SAMPLING SKU\n", "  from\n", "    rpc.product_facility_master_assortment\n", "  where\n", "    substate_reason_id = 9\n", "    and master_assortment_substate_id = 3\n", "    and active = 1\n", "    and lake_active_record = true\n", "  group by\n", "    1\n", ")\n", "select\n", "  distinct item_id, max(reason) as reason\n", "from\n", "  base\n", "  group by 1\"\"\",\n", "    con=CON_TRINO,\n", ")\n", "sampling_items = sampling_items.item_id.unique()\n", "print(len(sampling_items))"]}, {"cell_type": "code", "execution_count": null, "id": "24bdbd71-2bbc-44f9-8f3c-45ca7ebabf8a", "metadata": {}, "outputs": [], "source": ["base_column_dtypes = [\n", "    {\"name\": \"created_at\", \"type\": \"timestamp(6)\", \"description\": \"Row creation timestamp\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"Outlet identifier\"},\n", "    {\"name\": \"item_id\", \"type\": \"bigint\", \"description\": \"Unique identifier for the item\"},\n", "    {\"name\": \"item_factor\", \"type\": \"double\", \"description\": \"Item factor for scaling\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"Name of the item\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"Product type\"},\n", "    {\"name\": \"inv\", \"type\": \"integer\", \"description\": \"Current inventory level\"},\n", "    {\"name\": \"scaled_inv\", \"type\": \"double\", \"description\": \"Scaled inventory value\"},\n", "    {\"name\": \"cpd\", \"type\": \"double\", \"description\": \"Consumption per day\"},\n", "    {\"name\": \"qmax\", \"type\": \"double\", \"description\": \"qmax quantity\"},\n", "    {\n", "        \"name\": \"available_days\",\n", "        \"type\": \"double\",\n", "        \"description\": \"Number of days item is available at outlet for >9 hrs in l60\",\n", "    },\n", "    {\n", "        \"name\": \"master_assortment_substate_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Assortment substate ID\",\n", "    },\n", "    {\"name\": \"cloud_store_id\", \"type\": \"integer\", \"description\": \"Cloud store identifier\"},\n", "    {\"name\": \"flush_flag\", \"type\": \"integer\", \"description\": \"Flush flag status\"},\n", "    {\"name\": \"sub_storage_type\", \"type\": \"varchar\", \"description\": \"Sub-storage type\"},\n", "    {\"name\": \"base_storage_type\", \"type\": \"varchar\", \"description\": \"Base storage type\"},\n", "    {\n", "        \"name\": \"check_storage_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Storage type used for checks\",\n", "    },\n", "    {\"name\": \"event_id\", \"type\": \"varchar\", \"description\": \"festive upload event_id\"},\n", "    {\"name\": \"reason\", \"type\": \"varchar\", \"description\": \"RSTO block reason\"},\n", "    {\"name\": \"immunity_reason\", \"type\": \"varchar\", \"description\": \"Reason for assortment immunity\"},\n", "    {\"name\": \"doi_bucket\", \"type\": \"varchar\", \"description\": \"DOI bucket\"},\n", "    {\"name\": \"is_dwelling\", \"type\": \"integer\", \"description\": \"Is the item dwelling\"},\n", "    {\"name\": \"sale_window_0\", \"type\": \"double\", \"description\": \"Sales in window 0-15 days\"},\n", "    {\"name\": \"sale_window_1\", \"type\": \"double\", \"description\": \"Sales in window 15-30 days\"},\n", "    {\"name\": \"sale_window_2\", \"type\": \"double\", \"description\": \"Sales in window 30-45 days\"},\n", "    {\"name\": \"sale_window_3\", \"type\": \"double\", \"description\": \"Sales in window 45-60 days\"},\n", "    {\"name\": \"window_min_sale_dt\", \"type\": \"timestamp(6)\", \"description\": \"last sold at date\"},\n", "    {\n", "        \"name\": \"no_sales_score\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Sales score based on recent windows\",\n", "    },\n", "    {\n", "        \"name\": \"rsto_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Rsto inv based on basic RSTO computation on cpd/qmax\",\n", "    },\n", "    {\n", "        \"name\": \"at_risk_excess\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Rsto inv based on sales aggfunc RSTO computation\",\n", "    },\n", "    {\n", "        \"name\": \"proxy_cpd\",\n", "        \"type\": \"double\",\n", "        \"description\": \"CPD proxy on n days aggfunc of sales data\",\n", "    },\n", "    {\n", "        \"name\": \"rsto_flag\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Flags based on basis of RSTO Computation\",\n", "    },\n", "    {\n", "        \"name\": \"is_pid_active\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Tells if a pid is active at the outlet\",\n", "    },\n", "    {\"name\": \"config_json\", \"type\": \"varchar\", \"description\": \"Runtime config\"},\n", "]\n", "\n", "log_column_dtypes = base_column_dtypes + [\n", "    {\"name\": \"log_date\", \"type\": \"date\", \"description\": \"Log date\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp(6)\", \"description\": \"Log insert timestamp\"},\n", "]\n", "\n", "\n", "def add_df_to_trino(df_outlet_inventory, load_type=\"append\", write_log=False):\n", "    now = datetime.now()\n", "    log_date = now.date()\n", "\n", "    df_outlet_inventory = df_outlet_inventory.rename(\n", "        columns={\"last_sold_at_date\": \"window_min_sale_dt\"}\n", "    )\n", "    df_outlet_inventory[\"created_at\"] = now\n", "    df_outlet_inventory[\"master_assortment_substate_id\"] = pd.to_numeric(\n", "        df_outlet_inventory[\"master_assortment_substate_id\"], errors=\"coerce\"\n", "    )\n", "    df_outlet_inventory[\"available_days\"] = pd.to_numeric(\n", "        df_outlet_inventory[\"available_days\"], errors=\"coerce\"\n", "    )\n", "    df_outlet_inventory[\"created_at\"] = pd.to_datetime(\n", "        df_outlet_inventory[\"created_at\"], errors=\"coerce\"\n", "    )\n", "    df_outlet_inventory[\"window_min_sale_dt\"] = pd.to_datetime(\n", "        df_outlet_inventory[\"window_min_sale_dt\"], errors=\"coerce\"\n", "    )\n", "    df_outlet_inventory[\"flush_flag\"] = pd.to_numeric(\n", "        df_outlet_inventory[\"flush_flag\"], errors=\"coerce\"\n", "    ).<PERSON>na(0)\n", "    df_outlet_inventory[\"is_dwelling\"] = pd.to_numeric(\n", "        df_outlet_inventory[\"is_dwelling\"], errors=\"coerce\"\n", "    ).<PERSON>na(0)\n", "    df_outlet_inventory[\"is_pid_active\"] = pd.to_numeric(\n", "        df_outlet_inventory[\"is_pid_active\"], errors=\"coerce\"\n", "    ).<PERSON>na(0)\n", "\n", "    main_cols = [col[\"name\"] for col in base_column_dtypes]\n", "    kwargs = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"dwelling_inventory_base\",\n", "        \"column_dtypes\": base_column_dtypes,\n", "        \"primary_key\": [\"item_id\", \"outlet_id\"],\n", "        # \"partition_key\": [\"festival_name\"],\n", "        # \"incremental_key\": \"festival_name\",\n", "        \"force_upsert_without_increment_check\": False,\n", "        \"load_type\": load_type,  # append, truncate or upsert,\n", "        \"table_description\": \"this table contains dwelling inventory data for current date\",\n", "    }\n", "\n", "    to_trino(\n", "        df_outlet_inventory[main_cols], kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs\n", "    )\n", "\n", "    if write_log:\n", "        df_log = df_outlet_inventory[main_cols].copy()\n", "        df_log[\"log_date\"] = log_date\n", "        df_log[\"updated_at\"] = now\n", "\n", "        log_cols = [col[\"name\"] for col in log_column_dtypes]\n", "\n", "        kwargs_log = {\n", "            \"schema_name\": \"supply_etls\",\n", "            \"table_name\": \"dwelling_inventory_log\",\n", "            \"column_dtypes\": log_column_dtypes,\n", "            \"partition_key\": [\"log_date\"],\n", "            \"primary_key\": [\"log_date\", \"item_id\", \"outlet_id\", \"config_json\"],\n", "            \"force_upsert_without_increment_check\": True,\n", "            \"load_type\": \"upsert\",\n", "            \"table_description\": \"Log table capturing day-wise dwelling inventory snapshot\",\n", "        }\n", "\n", "        to_trino(\n", "            df_log[log_cols],\n", "            f\"{kwargs_log['schema_name']}.{kwargs_log['table_name']}\",\n", "            **kwargs_log,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "9e0cc902-4226-46e8-ad7d-c8ca3d4ef755", "metadata": {"tags": []}, "outputs": [], "source": ["def batch_inventory(batch):\n", "    df_batch = read_sql_query(\n", "        sql=f\"\"\"\n", "            with \n", "                ii as (-- is_inv.sql texting\n", "    select try_cast(ii.item_id as int) as item_id, \n", "           try_cast(ii.outlet_id as int) as outlet_id, \n", "           sum(quantity - coalesce(blocked_inv, 0)) as inv, \n", "           coalesce(max(itf.item_factor), 1) as item_factor,\n", "           max(q.qmax_final) as qmax\n", "    from dynamodb.blinkit_store_inventory_service_oi_rt_view as ii\n", "    left join supply_etls.item_factor itf on cast(itf.item_id as varchar) = ii.item_id\n", "    left join supply_etls.ars_qmax q on cast(q.item_id as varchar) = ii.item_id and cast(q.outlet_id as varchar) = ii.outlet_id and q.updated_at is not null\n", "    left join (\n", "        select item_id, outlet_id, sum(quantity) blocked_inv \n", "        from dynamodb.blinkit_store_inventory_service_blk_rt_view\n", "        where status = 'BLOCKED' \n", "          and reference_type not in ('DL_VALIDATION_CRON', 'PNA') \n", "        group by 1, 2\n", "    ) ib on ib.item_id = ii.item_id and ib.outlet_id = ii.outlet_id\n", "    where ii.state = 'GOOD'\n", "    group by 1, 2\n", "    ),\n", "            it as (-- inv_tagging\n", "    select \n", "        item_id, \n", "        outlet_id, \n", "        pfom.facility_id,\n", "        master_assortment_substate_id,\n", "        assortment_type\n", "    from rpc.product_facility_master_assortment pfma\n", "    left join (select facility_id, outlet_id from po.physical_facility_outlet_mapping) as pfom on pfom.facility_id = pfma.facility_id\n", "    where pfma.active = 1 and pfma.lake_active_record),\n", "            icd as (-- icd_store_closure\n", "    select\n", "            distinct id.item_id,\n", "            icd.name as item_name,\n", "            icd.product_type as p_type,\n", "            icd.l0 as l0_category,\n", "            icd.l1 as l1_category,\n", "            icd.l2 as l2_category,\n", "            id.storage_type,\n", "            id.handling_type,\n", "            id.perishable\n", "    from rpc.item_category_details icd \n", "    left join rpc.item_details id on id.item_id = icd.item_id\n", "    where l1_id not in (2039, 183)\n", "        and l0 not like '%%specials%%' \n", "        and l1!= 'Cigarettes'\n", "        and l0_id not in (1487, 343)\n", "        and perishable = 0 \n", "        and handling_type not in ('8', '9') \n", "        and storage_type not in ('3', '6', '7', '9')\n", "    ),\n", "            cpd_data as (-- cpd\n", "    select \n", "        item_id,\n", "        outlet_id,\n", "        cpd, \n", "        aps_adjusted as aps_adjusted_cpd\n", "    from ars.outlet_item_aps_derived_cpd \n", "    where insert_ds_ist =  CAST(current_date - interval '1' day AS varchar)),\n", "            tt as (-- tea_tagging\n", "    select \n", "            outlet_id, \n", "            item_id, \n", "            tag_value, \n", "            wom.cloud_store_id\n", "    from rpc.item_outlet_tag_mapping iotm\n", "    left join retail.warehouse_outlet_mapping wom on wom.warehouse_id =  cast(tag_value as int)\n", "    where tag_type_id = 8\n", "    and iotm.active = 1\n", "    and iotm.lake_active_record),\n", "    \n", "            f as (select\n", "        distinct pfom.outlet_id as be_outlet_id,\n", "        item_id,\n", "        1 as flag\n", "    from ars.flushing_transfer_config ftc\n", "    left join po.physical_facility_outlet_mapping pfom on pfom.facility_id = ftc.backend_facility_id\n", "    where for_date >= current_date - interval '7' day and partition_field >= cast(current_date - interval '7' day as varchar)\n", "    and for_date <= current_date + interval '14' day and partition_field <= cast(current_date + interval '14' day as varchar)),\n", "    \n", "            ictd as (-- icd\n", "    with base_1 as (\n", "        select\n", "            distinct id.item_id,\n", "            case \n", "                when (id.storage_type) in ('1','8') then 'REGULAR'\n", "                when (id.storage_type) in ('4','5') then 'HEAVY'\n", "                when (id.storage_type) in ('2','6') then 'COLD'\n", "                when (id.storage_type) in ('3','7') then 'FROZEN'\n", "                else 'REGULAR'\n", "            END AS storage_type,\n", "            case \n", "                when itm.tag_value = '1' then 'BEAUTY'\n", "                when itm.tag_value = '2' then 'BOUQUET'\n", "                when itm.tag_value = '3' then 'PREMIUM'\n", "                when itm.tag_value = '4' then 'BOOKS'\n", "                when itm.tag_value = '5' then 'NON_VEG'\n", "                when itm.tag_value = '6' then 'ICE_CREAM'\n", "                when itm.tag_value = '7' then 'TOYS'\n", "                when itm.tag_value = '8' then 'ELECTRONICS'\n", "                -- when itm.tag_value = '9' then 'FESTIVE'\n", "                when itm.tag_value = '10' then 'VERTICAL_CHUTES'\n", "                when itm.tag_value = '11' then 'BEST_SERVED_COLD'\n", "                when itm.tag_value = '12' then 'CRITICAL_SKUS'\n", "                when itm.tag_value = '13' then 'LARGE'\n", "                when itm.tag_value = '14' then 'APPAREL'\n", "                when itm.tag_value = '15' then 'SPORTS'\n", "                when itm.tag_value = '16' then 'PET_CARE'\n", "                when itm.tag_value = '17' then 'HOME_DECOR'\n", "                when itm.tag_value = '18' then 'KITCHEN_DINING'\n", "                else 'NORMAL_GROCERY'\n", "            end as IBT,\n", "            itm.tag_value\n", "        from rpc.item_details id \n", "        left join rpc.item_tag_mapping itm on itm.item_id = id.item_id and itm.tag_type_id = 7 and itm.active = TRUE and itm.lake_active_record\n", "        ),\n", "\n", "        base_2 as (\n", "            select \n", "                base_1.*,\n", "                case when ibt = 'NORMAL_GROCERY' then base_1.storage_type else concat(IBT,'_',base_1.storage_type) end as sub_storage_type\n", "            from base_1\n", "        )\n", "\n", "        select * from base_2\n", "    ),\n", "            pfsc as (-- pfsc\n", "     select \n", "        pfsc.facility_id, \n", "        pfom.outlet_id,\n", "        storage_type,\n", "        base_storage_type,\n", "        storage_capacity,\n", "        threshold,\n", "        1 as flag\n", "    from ars.physical_facility_storage_capacity pfsc\n", "    left join po.physical_facility_outlet_mapping pfom on pfom.facility_id = pfsc.facility_id and pfom.active = 1 \n", "        and pfom.lake_active_record\n", "    where pfsc.active\n", "        and pfsc.lake_active_record\n", "        and pfsc.storage_capacity > 0),\n", "            doac as (-- cpd\n", "    select \n", "        doac.item_id, \n", "        doac.facility_id, \n", "        pfom.outlet_id,\n", "        sum(case when doac.available_hours > 9 then 1 else 0 end) as available_days\n", "    from ars.daily_orders_and_availability doac\n", "    left join po.physical_facility_outlet_mapping pfom on pfom.facility_id = doac.facility_id\n", "    where doac.insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "    group by 1,2,3),\n", "\n", "    sales_aggregations as (  \n", "            WITH sales_data AS (\n", "                SELECT\n", "                    DATE(bt.created_date + INTERVAL '5' hour + INTERVAL '30' minute) AS dt,\n", "                    icd.item_id,\n", "                    CAST(bt.site_id AS INT) AS outlet_id,\n", "                    SUM(btl.qty) AS quantity,\n", "                    AVG(btl.qty) AS avg_ipc\n", "                FROM trade_finance.biz_txn bt\n", "                INNER JOIN trade_finance.biz_txn_line btl \n", "                    ON bt.id = btl.biz_txn_id\n", "                INNER JOIN rpc.item_category_details icd \n", "                    ON icd.item_id = CAST(btl.item_id AS INT)\n", "                WHERE\n", "                    bt.created_date > CURRENT_DATE - INTERVAL '60' day\n", "                    AND btl.insert_ds_ist > CAST(CURRENT_DATE - INTERVAL '60' day AS VARCHAR)\n", "                    AND bt.biz_txn_type IN ('B2C_SALE', 'B2B_CUSTOMER_SALE')\n", "                GROUP BY 1, 2, 3\n", "            ),\n", "            \n", "            windowed_sales AS (\n", "                SELECT\n", "                    item_id,\n", "                    outlet_id,\n", "                    dt,\n", "                    quantity,\n", "                    FLOOR(EXTRACT(day FROM (CURRENT_DATE - dt)) / 15) AS window_num\n", "                FROM sales_data\n", "            ),\n", "            \n", "            aggregated_sales AS (\n", "                SELECT\n", "                    item_id,\n", "                    outlet_id,\n", "                    SUM(CASE WHEN window_num = 0 THEN quantity ELSE 0 END) AS sale_window_0,\n", "                    SUM(CASE WHEN window_num = 1 THEN quantity ELSE 0 END) AS sale_window_1,\n", "                    SUM(CASE WHEN window_num = 2 THEN quantity ELSE 0 END) AS sale_window_2,\n", "                    SUM(CASE WHEN window_num = 3 THEN quantity ELSE 0 END) AS sale_window_3,\n", "                    MAX(dt) AS last_sold_at_date\n", "                FROM windowed_sales\n", "                GROUP BY item_id, outlet_id\n", "            ),\n", "            \n", "            calendar AS (\n", "                SELECT date_column AS dt\n", "                FROM UNNEST(\n", "                    sequence(\n", "                        CURRENT_DATE - INTERVAL '{sale_day_n}' day,\n", "                        CURRENT_DATE - INTERVAL '1' day,\n", "                        INTERVAL '1' day\n", "                    )\n", "                ) AS t(date_column)\n", "            ),\n", "\n", "            item_outlets AS (\n", "                SELECT DISTINCT item_id, outlet_id\n", "                FROM sales_data\n", "            ),\n", "\n", "            date_item_outlet_grid AS (\n", "                SELECT \n", "                    c.dt,\n", "                    io.item_id,\n", "                    io.outlet_id\n", "                FROM calendar c\n", "                CROSS JOIN item_outlets io\n", "            ),\n", "\n", "            sales_backfilled AS (\n", "                SELECT\n", "                    g.dt,\n", "                    g.item_id,\n", "                    g.outlet_id,\n", "                    COALESCE(s.quantity, 0) AS quantity\n", "                FROM date_item_outlet_grid g\n", "                LEFT JOIN sales_data s \n", "                    ON g.dt = s.dt AND g.item_id = s.item_id AND g.outlet_id = s.outlet_id\n", "            ),\n", "\n", "            \n", "            last_n_days_stats AS (\n", "                SELECT\n", "                    item_id,\n", "                    outlet_id,\n", "                    SUM(quantity) AS qty_sum_n_days,\n", "                    AVG(quantity) AS qty_avg_n_days,\n", "                    MAX(quantity) AS qty_max_n_days,\n", "                    MIN(quantity) AS qty_min_n_days,\n", "                    approx_percentile(quantity, 0.5) AS qty_median_n_days\n", "                FROM sales_backfilled\n", "                WHERE dt >= CURRENT_DATE - INTERVAL '{sale_day_n}' day AND dt < CURRENT_DATE  \n", "                GROUP BY item_id, outlet_id\n", "            )\n", "\n", "            SELECT\n", "                a.*,\n", "                b.qty_sum_n_days,\n", "                b.qty_avg_n_days,\n", "                b.qty_median_n_days,\n", "                b.qty_max_n_days,\n", "                b.qty_min_n_days\n", "            FROM aggregated_sales a\n", "            LEFT JOIN last_n_days_stats b ON a.item_id = b.item_id AND a.outlet_id = b.outlet_id\n", "            ORDER BY a.item_id, a.outlet_id\n", "        ),\n", "        \n", "        pid as (\n", "             select \n", "                dipom.item_id,\n", "                a.pos_outlet_id as outlet_id,\n", "                max(case when apl.result = 'success' then 1 else 0 end) as is_pid_active\n", "            from dwh.dim_item_product_offer_mapping dipom\n", "            left join interim.app_live_indexing_details apl on apl.product_id = dipom.product_id\n", "            left join dwh.dim_merchant_outlet_facility_mapping a on a.frontend_merchant_id = apl.merchant_id\n", "            where dipom.is_current and a.is_current = true and a.is_mapping_enabled = true and a.is_current_mapping_active = true\n", "            group by 1,2\n", "        ),\n", "\n", "            base as (\n", "                select\n", "            ii.item_id,\n", "            icd.item_name,\n", "            icd.p_type,\n", "            ii.outlet_id,\n", "            ii.inv,\n", "            coalesce(cpd_data.cpd, 0) as cpd,\n", "            ii.qmax,\n", "            ii.item_factor,\n", "            it.master_assortment_substate_id, \n", "            tt.cloud_store_id,\n", "            f.flag as flush_flag,\n", "            case when pfsc.flag is null then ictd.storage_type else ictd.sub_storage_type end as sub_storage_type,\n", "            sale_window_0,\n", "            sale_window_1,\n", "            sale_window_2,\n", "            sale_window_3,\n", "            qty_sum_n_days,\n", "            qty_avg_n_days,\n", "            qty_median_n_days,\n", "            qty_max_n_days,\n", "            qty_min_n_days,\n", "            last_sold_at_date,\n", "            doac.available_days,\n", "            pid.is_pid_active\n", "\n", "                from ii\n", "                left join it on it.item_id = ii.item_id and it.outlet_id = ii.outlet_id\n", "                inner join icd on icd.item_id = ii.item_id\n", "                left join ictd on ictd.item_id = ii.item_id\n", "                left join cpd_data on cpd_data.item_id = ii.item_id and cpd_data.outlet_id = ii.outlet_id\n", "                left join tt on tt.item_id = ii.item_id and tt.outlet_id = ii.outlet_id\n", "                left join f on cast(f.be_outlet_id as varchar) = tt.tag_value and f.item_id = ii.item_id\n", "                left join pfsc on pfsc.outlet_id = ii.outlet_id and pfsc.storage_type = ictd.sub_storage_type\n", "                left join doac on doac.item_id = ii.item_id and doac.outlet_id = ii.outlet_id\n", "                left join sales_aggregations sa on sa.item_id = ii.item_id and sa.outlet_id = ii.outlet_id\n", "                left join pid on pid.item_id = ii.item_id and pid.outlet_id = ii.outlet_id\n", "            )\n", "\n", "            select * from base \n", "            where inv > 0\n", "            and outlet_id in {tuple(batch) if len(batch) > 1 else (batch[0])} \n", "            and (cpd < {fetch_cpd_upper_bound} or inv > cpd * {dwelling_flag_depletion_days} or master_assortment_substate_id in (2,4))\n", "            \"\"\",\n", "        con=CON_TRINO,\n", "    )\n", "    return df_batch"]}, {"cell_type": "code", "execution_count": null, "id": "8886529c-9fb4-4c1b-9414-79371c56a678", "metadata": {}, "outputs": [], "source": ["ars_config = pd.read_sql_query(\n", "    sql=f\"\"\" select name, value, updated_at from ars.ars_config where lake_active_record and name in ('no_truncation_items', 'rsto_upper_cap_config')\"\"\",\n", "    con=CON_TRINO,\n", ")\n", "\n", "config = (\n", "    ast.literal_eval(ars_config[ars_config[\"name\"] == \"rsto_upper_cap_config\"].iloc[0].value)\n", "    if input_config is None\n", "    else input_config\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "82e201a6-abde-4c1c-a8ed-77e1277cd216", "metadata": {}, "outputs": [], "source": ["def compute_inv_to_keep(row):\n", "    if row[\"master_assortment_substate_id\"] in [2, 4]:\n", "        return 0, \"INACTIVE\"\n", "    else:\n", "        if row[\"qmax\"] is not None and row[\"qmax\"] >= 0:\n", "            return (\n", "                np.minimum(\n", "                    row[\"inv\"],\n", "                    (\n", "                        np.maximum(\n", "                            np.maximum(row[\"cpd\"] * config[\"qmax_default_doi_max\"], row[\"qmax\"]),\n", "                            config[\"qmax_default_quantity\"],\n", "                        )\n", "                        + np.maximum(\n", "                            row[\"cpd\"] * config[\"buffer_cap_doi\"], config[\"buffer_quantity\"]\n", "                        )\n", "                    ),\n", "                ),\n", "                f\"QMAX_DEF\",\n", "            )\n", "        else:\n", "            return (\n", "                np.minimum(\n", "                    row[\"inv\"],\n", "                    (\n", "                        np.maximum(row[\"cpd\"] * config[\"default_doi\"], config[\"default_quantity\"])\n", "                        + np.maximum(\n", "                            row[\"cpd\"] * config[\"buffer_cap_doi\"], config[\"buffer_quantity\"]\n", "                        )\n", "                    ),\n", "                ),\n", "                f\"QMAX_UNDEF\",\n", "            )"]}, {"cell_type": "code", "execution_count": null, "id": "09f9cefd-8c14-4ee9-8139-70b39fb843e7", "metadata": {}, "outputs": [], "source": ["def compute_inv_at_risk(row):\n", "    if row[\"master_assortment_substate_id\"] in [2, 4]:\n", "        return 0\n", "    else:\n", "        return np.minimum(\n", "            row[\"inv\"],\n", "            (\n", "                np.maximum(row[\"proxy_cpd\"] * config[\"default_doi\"], config[\"default_quantity\"])\n", "                + np.maximum(row[\"proxy_cpd\"] * config[\"buffer_cap_doi\"], config[\"buffer_quantity\"])\n", "            ),\n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "768ee8bf-2661-4207-b11c-b3eb6a6f9b7d", "metadata": {}, "outputs": [], "source": ["blocked_line_items = read_sql_query(\n", "    f\"\"\"\n", "select try_cast(item_id as int) as item_id, try_cast(outlet_id as int) as outlet_id\n", "from supply_etls.auto_rsto_blocked_item_outlet\n", "where rsto_block = '1'\n", "\"\"\",\n", "    con=CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "80cba423-face-405b-ac7c-e305251c5268", "metadata": {}, "outputs": [], "source": ["if env == \"dev\":\n", "    df_store_base = df_store_base.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "316b8898-623f-4a0e-8a24-ef23f5818db3", "metadata": {}, "outputs": [], "source": ["batch_size = 100\n", "outlet_list = df_store_base.outlet_id.dropna().unique()\n", "total_outlets = len(outlet_list)\n", "batches = [outlet_list[i : i + batch_size] for i in range(0, total_outlets, batch_size)]\n", "\n", "for i, batch in enumerate(batches):\n", "    print(f\"\\n ENTERING ITERATION: {i+1}\")\n", "    df_outlet_inventory = pd.DataFrame()\n", "\n", "    start_time = time.time()\n", "\n", "    # 1. FETCH inventory\n", "    print(f\"Sending to fetch_outlet_inventory: {tuple(batch)} with count: {len(batch)}\")\n", "    df_outlet_inventory = batch_inventory(batch)\n", "    df_outlet_inventory = df_outlet_inventory.merge(temp, on=\"item_id\", how=\"left\")\n", "    df_outlet_inventory = df_outlet_inventory.merge(\n", "        blocked_line_items.assign(rsto_line_item_blocked=1), on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", "    )\n", "\n", "    # 2. Compute and add DOI column, defaulting to 100 for inf/0 cpd\n", "    df_outlet_inventory[\"doi\"] = np.where(\n", "        df_outlet_inventory[\"cpd\"] == 0,\n", "        100,\n", "        df_outlet_inventory[\"inv\"] / df_outlet_inventory[\"cpd\"],\n", "    )\n", "    df_outlet_inventory[\"doi\"].replace([np.inf, -np.inf], 100, inplace=True)\n", "\n", "    # 3. Add base and check storage type columns based on spillover storage\n", "    df_outlet_inventory[\"base_storage_type\"] = np.where(\n", "        df_outlet_inventory[\"sub_storage_type\"].str.contains(\"_\"),\n", "        df_outlet_inventory[\"sub_storage_type\"].str.rsplit(\"_\", n=1).str[-1],\n", "        df_outlet_inventory[\"sub_storage_type\"],\n", "    )\n", "    df_outlet_inventory[\"check_storage_type\"] = np.where(\n", "        df_outlet_inventory[\"sub_storage_type\"].isin(spillover_types),\n", "        df_outlet_inventory[\"base_storage_type\"],\n", "        df_outlet_inventory[\"sub_storage_type\"],\n", "    )\n", "\n", "    # 4. Add RSTO immunity reasons\n", "    def get_immunity_reasons(row):\n", "        reasons = []\n", "        if row[\"available_days\"] < 30:\n", "            reasons.append(\"low_available_days\")\n", "        if row[\"flush_flag\"] == 1:\n", "            reasons.append(\"flushed_recently\")\n", "        if pd.notna(row[\"event_id\"]) and row[\"event_id\"] != \"00\":\n", "            reasons.append(\"festive_block\")\n", "        if not pd.isna(row[\"event_id\"]) and \"00\" in row[\"event_id\"]:\n", "            reasons.append(\"no_rsto_block\")\n", "        if \"COLD\" in row[\"sub_storage_type\"]:\n", "            reasons.append(\"cold_inv\")\n", "        if row[\"item_id\"] in sampling_items:\n", "            reasons.append(\"sampling_items\")\n", "        if row[\"item_id\"] in scan_tag_items:\n", "            reasons.append(\"scan_tag_inv\")\n", "        if row[\"rsto_line_item_blocked\"] == 1:\n", "            reasons.append(\"rsto_line_item_block\")\n", "\n", "        return \",\".join(reasons) if reasons else \"NONE\"\n", "\n", "    df_outlet_inventory[\"immunity_reason\"] = df_outlet_inventory.apply(get_immunity_reasons, axis=1)\n", "\n", "    # 5. Computes and adds doi_bucket, dwelling flag and scaled_inv\n", "    df_outlet_inventory[\"doi_bucket\"] = pd.cut(\n", "        df_outlet_inventory[\"doi\"],\n", "        bins=[0, 5, 10, 15, 25, 50, 100, 110],\n", "        labels=[\"0-5\", \"5-10\", \"10-15\", \"15-25\", \"25-50\", \"50-100\", \"100+\"],\n", "        right=False,\n", "    )\n", "    df_outlet_inventory[\"is_dwelling\"] = (\n", "        df_outlet_inventory[\"doi\"] > dwelling_flag_depletion_days\n", "    ).astype(int)\n", "    df_outlet_inventory[\"scaled_inv\"] = (\n", "        df_outlet_inventory[\"inv\"] * df_outlet_inventory[\"item_factor\"]\n", "    )\n", "\n", "    # 6. Fill NaN values in sales data with 0\n", "    df_outlet_inventory[[\"sale_window_0\", \"sale_window_1\", \"sale_window_2\", \"sale_window_3\"]] = (\n", "        df_outlet_inventory[\n", "            [\"sale_window_0\", \"sale_window_1\", \"sale_window_2\", \"sale_window_3\"]\n", "        ].fillna(0)\n", "    )\n", "\n", "    # 7. Vectorize, compute and adjust (on available days): no sales score: MORE NEGATIVE (-4) => no sales in 60 days, 0 => some sales in l15\n", "    sale_window_cols = [f\"sale_window_{i}\" for i in range(4)]\n", "    window_data = df_outlet_inventory[sale_window_cols].values\n", "    nonzero_mask = window_data != 0\n", "    first_nonzero_idx = nonzero_mask.argmax(axis=1)\n", "\n", "    all_zero_mask = ~nonzero_mask.any(axis=1)\n", "    initial_scores = -np.where(all_zero_mask, 4, first_nonzero_idx)\n", "    initial_scores[initial_scores == 0] = 0\n", "\n", "    available_days = df_outlet_inventory[\"available_days\"].values\n", "\n", "    max_allowed_score = np.select(\n", "        [available_days >= 60, available_days >= 45, available_days >= 30, available_days >= 15],\n", "        [-4, -3, -2, -1],\n", "        default=0,\n", "    )\n", "\n", "    adjusted_scores = np.maximum(initial_scores, max_allowed_score)\n", "\n", "    df_outlet_inventory[\"no_sales_score\"] = adjusted_scores\n", "\n", "    # 8. Compute RSTO excess, basic\n", "    df_outlet_inventory[[\"inv_to_keep\", \"rsto_flag\"]] = pd.DataFrame(\n", "        df_outlet_inventory.apply(compute_inv_to_keep, axis=1).tolist(),\n", "        index=df_outlet_inventory.index,\n", "    )\n", "    df_outlet_inventory[\"inv_to_keep\"] = np.round(df_outlet_inventory[\"inv_to_keep\"], 0)\n", "    df_outlet_inventory[\"rsto_inv\"] = (\n", "        df_outlet_inventory[\"inv\"] - df_outlet_inventory[\"inv_to_keep\"]\n", "    )\n", "\n", "    # 9. Compute inv_at_risk\n", "    aggfunc_to_col = {\n", "        \"sum\": \"qty_sum_n_days\",\n", "        \"mean\": \"qty_avg_n_days\",\n", "        \"avg\": \"qty_avg_n_days\",\n", "        \"median\": \"qty_median_n_days\",\n", "        \"max\": \"qty_max_n_days\",\n", "        \"min\": \"qty_min_n_days\",\n", "    }\n", "\n", "    aggfunc_key = str(sale_day_aggfunc).strip().lower()\n", "    proxy_cpd_col = aggfunc_to_col.get(aggfunc_key, \"qty_avg_n_days\")\n", "\n", "    df_outlet_inventory[\"proxy_cpd\"] = df_outlet_inventory[proxy_cpd_col].fillna(0)\n", "\n", "    df_outlet_inventory[\"at_risk_inv_to_keep\"] = df_outlet_inventory.apply(\n", "        compute_inv_at_risk, axis=1\n", "    )\n", "\n", "    df_outlet_inventory[\"at_risk_inv_to_keep\"] = np.round(\n", "        df_outlet_inventory[\"at_risk_inv_to_keep\"], 0\n", "    )\n", "    df_outlet_inventory[\"at_risk_excess\"] = (\n", "        df_outlet_inventory[\"inv\"] - df_outlet_inventory[\"at_risk_inv_to_keep\"]\n", "    )\n", "\n", "    # 10. Push data to trino\n", "    df_outlet_inventory[\"config_json\"] = json_config_str\n", "    add_df_to_trino(df_outlet_inventory, \"truncate\" if i == 0 else \"append\", 1)\n", "\n", "    # telemetry outputs\n", "    unique_outlets_fetched = df_outlet_inventory[\"outlet_id\"].nunique()\n", "    end_time = time.time()\n", "    time_taken = end_time - start_time  # Calculate time taken\n", "\n", "    print(\n", "        f\"Batch {i+1}/{len(batches)} processed. Total rows in DataFrame: {len(df_outlet_inventory)}\"\n", "    )\n", "    print(f\"Running count of unique outlet_ids: {unique_outlets_fetched}\")\n", "\n", "    del df_outlet_inventory\n", "    gc.collect()\n", "\n", "    time.sleep(15)"]}, {"cell_type": "code", "execution_count": null, "id": "c9cbed4a-a3e4-4ef2-a7bb-8db0d0a0eeb5", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "\n", "if env == \"dev\":\n", "    display(\n", "        pd.read_sql_query(\n", "            f\"\"\" \n", "            select * from blinkit_staging.supply_etls.dwelling_inventory_log \n", "            where regexp_like(event_id, '\\\\b(,)\\\\b') and log_date >= current_date - interval '10' day\n", "            order by item_id, outlet_id\n", "            limit 1000\"\"\",\n", "            con=CON_TRINO,\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "8b437ccb-2042-42ca-b4f4-be3711e3895a", "metadata": {}, "outputs": [], "source": ["### From here, this is dead inventory territory"]}, {"cell_type": "code", "execution_count": null, "id": "47701faf-03c4-4720-b3d9-678b3c95be03", "metadata": {}, "outputs": [], "source": ["if env != \"dev\":\n", "    time.sleep(300)"]}, {"cell_type": "code", "execution_count": null, "id": "0c4febe6-b281-40a1-9d0d-b8d5f2da0a95", "metadata": {}, "outputs": [], "source": ["avails = read_sql_query(\n", "    sql=f\"\"\"\n", "WITH base_union AS (\n", "    SELECT facility_id, facility_name, date_, hour, \n", "           date_add('hour', hour, CAST(date_ AS timestamp)) AS ts,\n", "           total_sku, live_sku,\n", "           availability * 100.00 AS availability,\n", "           weighted_availability * 100.00 AS weighted_availability,\n", "           cum_weighted_availability * 100.00 AS cum_weighted_availability\n", "    FROM supply_etls.store_hourly_weighted_availability\n", "    WHERE facility_id IN {tuple(df_store_base.facility_id.unique())} \n", "      AND assortment_type = 'Packaged Goods'\n", "      AND date_ > current_date - INTERVAL '15' day\n", "\n", "    UNION ALL\n", "\n", "    SELECT facility_id, facility_name, date_, hour, \n", "           date_add('hour', hour, CAST(date_ AS timestamp)) AS ts,\n", "           total_sku, live_sku,\n", "           availability * 100.00 AS availability,\n", "           weighted_availability * 100.00 AS weighted_availability,\n", "           cum_weighted_availability * 100.00 AS cum_weighted_availability\n", "    FROM supply_etls.store_hourly_weighted_availability_logs\n", "    WHERE facility_id IN {tuple(df_store_base.facility_id.unique())} \n", "      AND assortment_type = 'Packaged Goods'\n", "      AND date_ > current_date - INTERVAL '15' day\n", "),\n", "\n", "ranked_base AS (\n", "    SELECT *, \n", "           ROW_NUMBER() OVER (PARTITION BY facility_id ORDER BY ts DESC) AS rn\n", "    FROM base_union\n", "),\n", "\n", "latest_base AS (\n", "    SELECT * \n", "    FROM ranked_base\n", "    WHERE rn = 1\n", ")\n", "\n", "SELECT \n", "    base.facility_id, \n", "    base.ts, \n", "    base.total_sku, \n", "    base.live_sku, \n", "    base.availability, \n", "    base.weighted_availability, \n", "    base.cum_weighted_availability\n", "FROM latest_base base\n", "ORDER BY base.ts DESC\n", "\"\"\",\n", "    con=CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47deaf21-2320-41ca-90e1-6e8d0329d79a", "metadata": {}, "outputs": [], "source": ["utils = read_sql_query(\n", "    sql=f\"\"\"\n", "with base_raw AS (\n", "    SELECT\n", "        hsu.updated_at,\n", "        hsu.outlet_id,\n", "        hsu.facility_id,\n", "        hsu.facility_name,\n", "        CASE \n", "            WHEN final_storage_type LIKE '%%REGULAR%%' THEN 'REGULAR'\n", "            WHEN final_storage_type LIKE '%%HEAVY%%' THEN 'HEAVY'\n", "            WHEN final_storage_type LIKE '%%COLD%%' THEN 'COLD'\n", "            WHEN final_storage_type LIKE '%%FROZEN%%' THEN 'FROZEN'\n", "        END AS base_storage_type,\n", "        SUM(storage_cap) AS storage_cap,\n", "        SUM(scaled_onshelf_inventory) AS scaled_onshelf_inventory,\n", "        100.0 * SUM(scaled_onshelf_inventory) / SUM(storage_cap) AS onshelf_util,\n", "        ROW_NUMBER() OVER (\n", "            PARTITION BY hsu.facility_id, \n", "                         CASE \n", "                            WHEN final_storage_type LIKE '%%REGULAR%%' THEN 'REGULAR'\n", "                            WHEN final_storage_type LIKE '%%HEAVY%%' THEN 'HEAVY'\n", "                            WHEN final_storage_type LIKE '%%COLD%%' THEN 'COLD'\n", "                            WHEN final_storage_type LIKE '%%FROZEN%%' THEN 'FROZEN'\n", "                         END \n", "            ORDER BY hsu.updated_at DESC\n", "        ) AS rn\n", "    FROM supply_etls.hourly_storage_util hsu\n", "    WHERE updated_at >= current_date - interval '3' day\n", "    GROUP BY 1,2,3,4,5\n", ")\n", "SELECT \n", "    facility_name,\n", "    updated_at,\n", "    outlet_id,\n", "    facility_id,\n", "    facility_name,\n", "    base_storage_type,\n", "    storage_cap,\n", "    scaled_onshelf_inventory,\n", "    onshelf_util,\n", "    100 AS base_util\n", "FROM base_raw\n", "WHERE rn = 1\n", "and outlet_id in {tuple(df_store_base.outlet_id.unique())}\n", "and base_storage_type in ('REGULAR', 'HEAVY')\n", "ORDER BY facility_id, base_storage_type\n", "\"\"\",\n", "    con=CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "215caea9-b79f-4562-8625-0929e80f6416", "metadata": {}, "outputs": [], "source": ["def flatten_column(col):\n", "    if isinstance(col, tuple):\n", "        parts = [str(c).strip() for c in col if c not in (None, \"\")]\n", "        return \"__\".join(parts)\n", "    return str(col).strip()\n", "\n", "\n", "pivot_base = pd.pivot_table(\n", "    utils.rename(columns={\"updated_at\": \"storage_ts\"}),\n", "    index=[\"facility_id\", \"storage_ts\"],\n", "    columns=\"base_storage_type\",\n", "    values=[\"storage_cap\", \"scaled_onshelf_inventory\"],\n", "    aggfunc=\"mean\",\n", ").reset_index()\n", "\n", "pivot_base.columns = [flatten_column(col) for col in pivot_base.columns]\n", "\n", "pivot_base = pivot_base.merge(\n", "    avails[[\"facility_id\", \"weighted_availability\", \"ts\"]].rename(columns={\"ts\": \"avail_ts\"}),\n", "    on=\"facility_id\",\n", "    how=\"left\",\n", ").merge(df_store_base, on=\"facility_id\", how=\"left\")\n", "\n", "pivot_base = pivot_base.drop_duplicates(subset=\"outlet_id\", keep=\"first\")\n", "\n", "pivot_base"]}, {"cell_type": "code", "execution_count": null, "id": "be904e20-629f-4edd-873b-57ef9d57781e", "metadata": {}, "outputs": [], "source": ["if env == \"dev\":\n", "    dwell_base_df = read_sql_query(\n", "        sql=f\"\"\" select * from blinkit_staging.supply_etls.dwelling_inventory_base where outlet_id in {tuple(df_store_base.outlet_id.unique())} and no_sales_score in (-3,-4)\"\"\",\n", "        con=CON_TRINO,\n", "    )\n", "else:\n", "    dwell_base_df = read_sql_query(\n", "        sql=f\"\"\" select * from supply_etls.dwelling_inventory_base where outlet_id in {tuple(df_store_base.outlet_id.unique())} and no_sales_score in (-3,-4)\"\"\",\n", "        con=CON_TRINO,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "3a9e494d-1dde-4250-8253-e1c6412376ef", "metadata": {}, "outputs": [], "source": ["unique_immunity_reasons = sorted(\n", "    pd.Series(\n", "        np.concatenate(\n", "            dwell_base_df[[\"immunity_reason\"]]\n", "            .dropna()\n", "            .apply(lambda x: sum([str(i).split(\",\") for i in x if pd.notna(i)], []), axis=1)\n", "            .values\n", "        )\n", "    )\n", "    .str.strip()\n", "    .unique()\n", ")\n", "\n", "unique_festive_reasons = sorted(\n", "    pd.Series(\n", "        np.concatenate(\n", "            dwell_base_df[[\"reason\"]]\n", "            .dropna()\n", "            .apply(lambda x: sum([str(i).split(\",\") for i in x if pd.notna(i)], []), axis=1)\n", "            .values\n", "        )\n", "    )\n", "    .str.strip()\n", "    .unique()\n", ")\n", "\n", "print(unique_immunity_reasons, \"\\n\\n\", unique_festive_reasons)"]}, {"cell_type": "code", "execution_count": null, "id": "73902524-7a18-4cbe-88c7-95c5e7922cc5", "metadata": {}, "outputs": [], "source": ["base_df = dwell_base_df[\n", "    ~dwell_base_df[\"immunity_reason\"].str.contains(\n", "        r\"\\b(cold_inv|scan_tag_inv|sampling_items)\\b\", na=False\n", "    )\n", "].query(\"base_storage_type in ('REGULAR', 'HEAVY')\")\n", "\n", "base_scan_df = dwell_base_df[\n", "    dwell_base_df[\"immunity_reason\"].str.contains(r\"\\bscan_tag_inv\\b\", na=False)\n", "].query(\"base_storage_type in ('REGULAR', 'HEAVY')\")\n", "\n", "base_sampling_df = dwell_base_df[\n", "    dwell_base_df[\"immunity_reason\"].str.contains(r\"\\bsampling_items\\b\", na=False)\n", "].query(\"base_storage_type in ('REGULAR', 'HEAVY')\")\n", "\n", "value_cols = [\"inv\", \"scaled_inv\", \"rsto_inv\"]\n", "\n", "full_summary = (\n", "    base_df.groupby([\"outlet_id\", \"base_storage_type\"])[value_cols]\n", "    .sum()\n", "    .reset_index()\n", "    .melt(id_vars=[\"outlet_id\", \"base_storage_type\"], var_name=\"metric_key\", value_name=\"value\")\n", "    .assign(reason_key=\"total\")\n", ")\n", "\n", "scan_tag_summary = (\n", "    base_scan_df.groupby([\"outlet_id\", \"base_storage_type\"])[value_cols]\n", "    .sum()\n", "    .reset_index()\n", "    .melt(id_vars=[\"outlet_id\", \"base_storage_type\"], var_name=\"metric_key\", value_name=\"value\")\n", "    .assign(reason_key=\"scan_tag_inv\")\n", ")\n", "\n", "sampling_summary = (\n", "    base_sampling_df.groupby([\"outlet_id\", \"base_storage_type\"])[value_cols]\n", "    .sum()\n", "    .reset_index()\n", "    .melt(id_vars=[\"outlet_id\", \"base_storage_type\"], var_name=\"metric_key\", value_name=\"value\")\n", "    .assign(reason_key=\"sampling_inv\")\n", ")\n", "\n", "global_summaries = pd.concat(\n", "    [full_summary, scan_tag_summary, sampling_summary], ignore_index=True\n", ").assign(\n", "    reason_value=\"NONE\",\n", ")\n", "\n", "global_summaries"]}, {"cell_type": "code", "execution_count": null, "id": "35781caa-4953-4ca8-859f-05b43ba57743", "metadata": {}, "outputs": [], "source": ["def explode_and_aggregate(df, reason_col):\n", "    return (\n", "        df[[\"outlet_id\", \"base_storage_type\"] + value_cols + [reason_col]]\n", "        .dropna(subset=[reason_col])\n", "        .assign(**{reason_col: df[reason_col].str.split(\",\")})\n", "        .explode(reason_col)\n", "        .assign(reason_value=lambda d: d[reason_col].str.strip(), reason_key=reason_col)\n", "        .groupby([\"outlet_id\", \"base_storage_type\", \"reason_value\", \"reason_key\"])[value_cols]\n", "        .sum()\n", "        .reset_index()\n", "        .melt(\n", "            id_vars=[\"outlet_id\", \"base_storage_type\", \"reason_value\", \"reason_key\"],\n", "            var_name=\"metric_key\",\n", "            value_name=\"value\",\n", "        )\n", "    )\n", "\n", "\n", "immu_expanded = explode_and_aggregate(base_df, \"immunity_reason\")\n", "fest_expanded = explode_and_aggregate(base_df, \"reason\")\n", "\n", "exploded_all = pd.concat([immu_expanded, fest_expanded, global_summaries], ignore_index=True)\n", "exploded_all"]}, {"cell_type": "code", "execution_count": null, "id": "0d497e76-225b-49a7-927c-406361aad183", "metadata": {}, "outputs": [], "source": ["exploded_all[\"reason_key\"] = (\n", "    exploded_all[\"reason_key\"]\n", "    .map({\"immunity_reason\": \"RSTO_immunity_r1\", \"reason\": \"RSTO_immunity_r2\"})\n", "    .fillna(exploded_all[\"reason_key\"])\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fa0cd7d3-1903-4db3-88c6-6334214a406f", "metadata": {}, "outputs": [], "source": ["column_dtypes = additional_column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"Outlet identifier\"},\n", "    {\n", "        \"name\": \"base_storage_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Base storage type (HEAVY or REGULAR)\",\n", "    },\n", "    {\n", "        \"name\": \"reason_value\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Value associated with the RSTO reason\",\n", "    },\n", "    {\"name\": \"reason_key\", \"type\": \"varchar\", \"description\": \"Key for RSTO reason categorization\"},\n", "    {\n", "        \"name\": \"metric_key\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"Metric key identifying the type of value (e.g., scaled inventory)\",\n", "    },\n", "    {\"name\": \"value\", \"type\": \"double\", \"description\": \"Metric value\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Facility identifier (backend store)\",\n", "    },\n", "    {\n", "        \"name\": \"storage_ts\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"Timestamp of storage measurement\",\n", "    },\n", "    {\n", "        \"name\": \"scaled_onshelf_inventory__HEAVY\",\n", "        \"type\": \"double\",\n", "        \"description\": \"Scaled onshelf inventory in HEAVY zone\",\n", "    },\n", "    {\n", "        \"name\": \"scaled_onshelf_inventory__REGULAR\",\n", "        \"type\": \"double\",\n", "        \"description\": \"Scaled onshelf inventory in REGULAR zone\",\n", "    },\n", "    {\n", "        \"name\": \"storage_cap__HEAVY\",\n", "        \"type\": \"double\",\n", "        \"description\": \"Storage capacity for HEAVY items\",\n", "    },\n", "    {\n", "        \"name\": \"storage_cap__REGULAR\",\n", "        \"type\": \"double\",\n", "        \"description\": \"Storage capacity for REGULAR items\",\n", "    },\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"double\",\n", "        \"description\": \"Weighted average availability at facility\",\n", "    },\n", "    {\n", "        \"name\": \"avail_ts\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"Timestamp of availability measurement\",\n", "    },\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"Name of the outlet\"},\n", "    {\"name\": \"store_age\", \"type\": \"integer\", \"description\": \"Age of the store in days\"},\n", "    {\"name\": \"avg_carts\", \"type\": \"double\", \"description\": \"Average number of carts processed\"},\n", "    {\"name\": \"log_date\", \"type\": \"date\", \"description\": \"Log date\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp(6)\", \"description\": \"Log insert timestamp\"},\n", "]\n", "\n", "\n", "def add_dead_df_to_trino(df_base):\n", "    now = datetime.now()\n", "    log_date = now.date()\n", "\n", "    df_base[\"log_date\"] = log_date\n", "    df_base[\"updated_at\"] = now\n", "\n", "    df_base[\"storage_ts\"] = pd.to_datetime(df_base[\"storage_ts\"], errors=\"coerce\")\n", "    df_base[\"avail_ts\"] = pd.to_datetime(df_base[\"avail_ts\"], errors=\"coerce\")\n", "    df_base[\"updated_at\"] = pd.to_datetime(df_base[\"updated_at\"], errors=\"coerce\")\n", "\n", "    log_cols = [col[\"name\"] for col in column_dtypes]\n", "\n", "    kwargs_log = {\n", "        \"schema_name\": \"supply_etls\",\n", "        \"table_name\": \"dead_inv_attr_outlet_agg_log\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"partition_key\": [\"log_date\"],\n", "        \"primary_key\": [\"log_date\", \"outlet_id\"],\n", "        \"force_upsert_without_increment_check\": True,\n", "        \"load_type\": \"upsert\",\n", "        \"table_description\": \"Log table capturing day-wise dead inventory snapshot aggregated at store level with attribution\",\n", "    }\n", "\n", "    to_trino(\n", "        df_base[log_cols], f\"{kwargs_log['schema_name']}.{kwargs_log['table_name']}\", **kwargs_log\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "db0163c1-1b4f-4cc5-bb64-1e8dde01fde4", "metadata": {}, "outputs": [], "source": ["final_df = exploded_all.merge(pivot_base, on=[\"outlet_id\"], how=\"left\")\n", "\n", "add_dead_df_to_trino(final_df)"]}, {"cell_type": "code", "execution_count": null, "id": "9772dabd-e718-43a8-8fa4-9dc1cee363af", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)\n", "\n", "if env == \"dev\":\n", "    display(\n", "        pd.read_sql_query(\n", "            f\"\"\" \n", "            select \n", "                outlet_id,\n", "                outlet_name,\n", "                store_age,\n", "                avg_carts,\n", "                weighted_availability,\n", "                storage_cap__regular,\n", "                storage_cap__heavy,\n", "                scaled_onshelf_inventory__regular,\n", "                scaled_onshelf_inventory__heavy,\n", "                base_storage_type || '___' || reason_key || '___' || reason_value || '___' || metric_key as unique_col_key,\n", "                value\n", "            from blinkit_staging.supply_etls.dead_inv_attr_outlet_agg_log \n", "            where log_date >= current_date - interval '10' day\"\"\",\n", "            con=CON_TRINO,\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "798d3de1-a8d0-4083-90a7-8ce35107cf8a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}