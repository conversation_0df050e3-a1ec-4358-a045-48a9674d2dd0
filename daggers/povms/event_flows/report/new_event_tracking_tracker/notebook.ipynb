{"cells": [{"cell_type": "code", "execution_count": null, "id": "e7745095-cc10-4952-81ee-94f83ce14886", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)\n", "import warnings"]}, {"cell_type": "code", "execution_count": null, "id": "dca8905d-8f95-41a3-b3aa-c3ae0b5c5320", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            if not df.empty:\n", "                return df\n", "            else:\n", "                print(\"DataFrame is empty, retrying...\")\n", "        except Exception as e:\n", "            print(e)\n", "\n", "        time.sleep(15)\n", "\n", "    print(\"Max retries reached, returning an empty DataFrame\")\n", "    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "8aa18f3f-ca2e-42cd-ab66-d4c31e40d06a", "metadata": {}, "outputs": [], "source": ["def sales_outlet():\n", "    sales_outlet = \"\"\"\n", "        \n", "    select\n", "    facility_id\n", "    from supply_etls.date_hourly_sales_details sd\n", "    join supply_etls.outlet_details od on od.hot_outlet_id = sd.outlet_id\n", "    where sd.insert_ds_ist > '2023-11-01'\n", "    group by 1\n", "    \n", "    \"\"\"\n", "    return read_sql_query(sales_outlet, trino)\n", "\n", "\n", "sales_outlet = sales_outlet()"]}, {"cell_type": "code", "execution_count": null, "id": "e07bf0b9-5f34-4497-9927-a2b2929731b3", "metadata": {}, "outputs": [], "source": ["print(sales_outlet.shape)\n", "sales_outlet.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a560d986-266b-418a-8657-0ef86460e614", "metadata": {}, "outputs": [], "source": ["sales_outlet_list = tuple(list(sales_outlet[\"facility_id\"].unique()))"]}, {"cell_type": "markdown", "id": "e562e0de-943b-4be8-9028-b5e9410b6d8b", "metadata": {}, "source": ["# outlet query"]}, {"cell_type": "code", "execution_count": null, "id": "4addb1b9-c118-4bbb-a990-2f85d0e6eef4", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = \"\"\"\n", "    \n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details)\n", "        \n", "            select * from outlet_details\n", "                where ars_check = 1\n", "\n", "    \"\"\"\n", "    return read_sql_query(outlets, trino)\n", "\n", "\n", "outlets = outlets()"]}, {"cell_type": "code", "execution_count": null, "id": "03da2ef8-2e96-4bda-ba78-1285d1148553", "metadata": {}, "outputs": [], "source": ["# outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "11856bfa-e949-487e-9864-4aeb7f99925d", "metadata": {}, "outputs": [], "source": ["# all inv outlets\n", "all_inv_outlet_id = list(outlets[\"inv_outlet_id\"].unique())\n", "all_inv_outlet_id = tuple(all_inv_outlet_id)\n", "# len(all_inv_outlet_id)\n", "\n", "\n", "# all hot outlets\n", "all_hot_outlet_id = list(outlets[\"hot_outlet_id\"].unique())\n", "all_hot_outlet_id = tuple(all_hot_outlet_id)\n", "# len(all_hot_outlet_id)\n", "\n", "# all hot outlets_name\n", "all_hot_name = outlets[\n", "    [\"hot_outlet_id\", \"inv_outlet_id\", \"facility_id\", \"store_type\"]\n", "].rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"inv_outlet_id\": \"be_inv_outlet_id\",\n", "        \"facility_id\": \"be_facility_id\",\n", "        \"store_type\": \"be_store_type\",\n", "    }\n", ")\n", "# all_hot_name.head()\n", "\n", "# all facility\n", "all_facility_id = list(outlets[\"facility_id\"].unique())\n", "all_facility_id = tuple(all_facility_id)\n", "# len(all_facility_id)\n", "\n", "# frontend outlets\n", "frontend_outlet_details = (\n", "    outlets[outlets[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", ")\n", "fe_outlet_details = list(frontend_outlet_details[\"hot_outlet_id\"].unique())\n", "fe_outlet_details = tuple(fe_outlet_details)\n", "# len(fe_outlet_details)\n", "\n", "# frontend facility\n", "frontend_facility_details = (\n", "    outlets[outlets[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", ")\n", "fe_facility_details = list(frontend_facility_details[\"facility_id\"].unique())\n", "fe_facility_details = tuple(fe_facility_details)\n", "# len(fe_facility_details)\n", "\n", "# backend inv outlets\n", "be_inv_outlet_id = (\n", "    outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", ")\n", "be_inv_outlet_id = list(be_inv_outlet_id[\"inv_outlet_id\"].unique())\n", "be_inv_outlet_id = tuple(be_inv_outlet_id)\n", "# len(be_inv_outlet_id)\n", "\n", "\n", "# backend hot outlets\n", "be_hot_outlet_id = (\n", "    outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", ")\n", "be_hot_outlet_id = list(be_hot_outlet_id[\"hot_outlet_id\"].unique())\n", "be_hot_outlet_id = tuple(be_hot_outlet_id)\n", "# len(be_hot_outlet_id)"]}, {"cell_type": "code", "execution_count": null, "id": "1f8d3360-336d-4893-9c0c-7652b59ad69b", "metadata": {}, "outputs": [], "source": ["item_input = pb.from_sheets(\"1BwGIi-GLK2lOVGEBJgNV0UhP9Ba_sjAERvmE09vF8Lk\", \"item_list\")\n", "\n", "item_input = item_input[~((item_input[\"item_id\"] == \"\"))]\n", "\n", "item_input[\"item_id\"] = item_input[\"item_id\"].fillna(0).astype(int)\n", "\n", "item_input.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e186fc65-eeaa-451f-945e-36151aa778ef", "metadata": {}, "outputs": [], "source": ["new_item_input = tuple(list(item_input[\"item_id\"].unique()))"]}, {"cell_type": "markdown", "id": "28b093c7-f8b6-4a25-9b31-7b912f5956fd", "metadata": {}, "source": ["# item details query"]}, {"cell_type": "code", "execution_count": null, "id": "cde73db1-20a3-4600-b521-705be621756f", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = f\"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select * from supply_etls.item_details)\n", "        \n", "            select * from item_details\n", "                where assortment_type in ('Packaged Goods') and handling_type = 'Non Packaging Material'\n", "                and item_id in {new_item_input}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(item_details, trino)\n", "\n", "\n", "item_details = item_details()"]}, {"cell_type": "code", "execution_count": null, "id": "33220b12-c3be-4a25-9057-08e59c8e8c6c", "metadata": {}, "outputs": [], "source": ["# item_details.head()"]}, {"cell_type": "markdown", "id": "689f5b05-2a4c-40e4-b105-07ff1d57bf3b", "metadata": {}, "source": ["# active assortment query"]}, {"cell_type": "code", "execution_count": null, "id": "85e53315-cb20-4e65-96e1-86a055a44cf5", "metadata": {}, "outputs": [], "source": ["def active_assortment():\n", "    active_assortment = f\"\"\"\n", "    \n", "    with\n", "    assortment as\n", "        (select * from rpc.product_facility_master_assortment)\n", "\n", "            select \n", "                item_id, \n", "                facility_id,\n", "                case when assortment_type in ('LON<PERSON><PERSON><PERSON>') then 'thirty_min' else 'ten_min' end as store_assortment_type\n", "\n", "                    from assortment\n", "\n", "                        where \n", "                            active = 1\n", "                            -- and master_assortment_substate_id in (1,3)\n", "                            and facility_id in {sales_outlet_list}\n", "                            and lake_active_record\n", "                            and item_id in {new_item_input}\n", "\n", "    \"\"\"\n", "    return read_sql_query(active_assortment, trino)\n", "\n", "\n", "active_assortment = active_assortment()"]}, {"cell_type": "code", "execution_count": null, "id": "e40ed311-37cf-4c4d-9e61-dfa34bcb60a5", "metadata": {}, "outputs": [], "source": ["# active_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a6f49f3e-4c36-4412-afd1-e1b8525526a6", "metadata": {}, "outputs": [], "source": ["active_assortment.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0cda4f0c-b707-4558-a48d-afc3a04cc0d0", "metadata": {}, "outputs": [], "source": ["adding_item_details = pd.merge(\n", "    active_assortment, item_details, on=[\"item_id\"], how=\"inner\"\n", ")\n", "\n", "adding_item_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "095637d7-e73f-499f-9bb5-8ba949195343", "metadata": {}, "outputs": [], "source": ["adding_outlet_details = pd.merge(\n", "    adding_item_details,\n", "    outlets,\n", "    on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "del adding_item_details\n", "del item_details\n", "gc.collect()\n", "\n", "adding_outlet_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "811abdc8-9407-4db2-8a97-3c63e254516f", "metadata": {}, "outputs": [], "source": ["adding_outlet_details = adding_outlet_details[\n", "    [\n", "        \"store_assortment_type\",\n", "        \"city_name\",\n", "        \"hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0_category\",\n", "        \"l1_category\",\n", "        \"l2_category\",\n", "        \"p_type\",\n", "    ]\n", "]\n", "\n", "adding_outlet_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4f55c9ad-d1ce-422c-b3b3-cc4677e02655", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(list(adding_outlet_details[\"item_id\"].unique()))\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "3e0b8796-ff1a-4390-aa51-0e2d915de5f2", "metadata": {}, "outputs": [], "source": ["del active_assortment\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "d3d07ed2-ccce-4a20-802e-aae2c0cd1c2e", "metadata": {}, "source": ["# tea taggings query"]}, {"cell_type": "code", "execution_count": null, "id": "ceafcaf4-5fcd-41fc-b556-103eebcc208e", "metadata": {}, "outputs": [], "source": ["def tea_taggings():\n", "    tea_taggings = f\"\"\"\n", "    \n", "    with\n", "    tea_taggings as\n", "        (select * from rpc.item_outlet_tag_mapping)\n", "\n", "            select \n", "                item_id,\n", "                outlet_id as hot_outlet_id,\n", "                cast(tag_value as int) as be_hot_outlet_id\n", "\n", "                    from tea_taggings\n", "\n", "                        where \n", "                            tag_type_id = 8\n", "                            and active = 1\n", "                            and lake_active_record\n", "                            and item_id in {item_id_list}\n", "\n", "    \"\"\"\n", "    return read_sql_query(tea_taggings, trino)\n", "\n", "\n", "tea_taggings = tea_taggings()"]}, {"cell_type": "code", "execution_count": null, "id": "166aa1cc-bb90-4da0-ae1a-3bfb7caaac38", "metadata": {}, "outputs": [], "source": ["# tea_taggings.head()"]}, {"cell_type": "code", "execution_count": null, "id": "42982593-0f9f-4a39-953d-467e38615fc1", "metadata": {}, "outputs": [], "source": ["adding_tea_tagging_outlet = pd.merge(\n", "    tea_taggings, all_hot_name, on=[\"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "# adding_tea_tagging_outlet.head()"]}, {"cell_type": "code", "execution_count": null, "id": "59bcff5b-44af-4452-b3d3-22aee4dccc1a", "metadata": {}, "outputs": [], "source": ["del tea_taggings\n", "gc.collect()"]}, {"cell_type": "markdown", "id": "c83261ab-c14a-4743-b368-791536832f03", "metadata": {}, "source": ["# ars mapping query"]}, {"cell_type": "code", "execution_count": null, "id": "3021fbab-6cf8-4a97-a8b3-ee6c1a62c432", "metadata": {}, "outputs": [], "source": ["def ars_mapping():\n", "    ars_mapping = \"\"\"\n", "    \n", "    with\n", "    ars_mapping as\n", "        (select facility_id as be_facility_id, outlet_id as hot_outlet_id\n", "\n", "            from po.bulk_facility_outlet_mapping\n", "\n", "                where active = true\n", "                and lake_active_record\n", "        )\n", "\n", "            select * from ars_mapping\n", "    \n", "    \"\"\"\n", "    return read_sql_query(ars_mapping, trino)\n", "\n", "\n", "ars_mapping = ars_mapping()"]}, {"cell_type": "code", "execution_count": null, "id": "e04e2606-3452-4e84-855d-7d02b285d8a9", "metadata": {}, "outputs": [], "source": ["# ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "173cd9e1-67cf-4930-82c5-99640a5eafec", "metadata": {}, "outputs": [], "source": ["adding_ars_mapping = pd.merge(\n", "    adding_tea_tagging_outlet,\n", "    ars_mapping,\n", "    on=[\"be_facility_id\", \"hot_outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "del ars_mapping\n", "del adding_tea_tagging_outlet\n", "gc.collect()\n", "\n", "# adding_ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3b1ab8ed-8f6f-44d5-ac41-656aab82aa52", "metadata": {}, "outputs": [], "source": ["be_inv_outlet_list = (\n", "    adding_ars_mapping[[\"be_inv_outlet_id\"]]\n", "    .rename(columns={\"be_inv_outlet_id\": \"inv_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "fe_inv_outlet_list = (\n", "    adding_ars_mapping[[\"hot_outlet_id\"]]\n", "    .rename(columns={\"hot_outlet_id\": \"inv_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "all_inv_outlet_list = be_inv_outlet_list.append(fe_inv_outlet_list)\n", "all_inv_outlet_list = tuple(list(all_inv_outlet_list[\"inv_outlet_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "2609395d-50e4-4559-8a2c-bcbf784ce5b7", "metadata": {}, "outputs": [], "source": ["be_hot_outlet_list = (\n", "    adding_ars_mapping[[\"be_hot_outlet_id\"]]\n", "    .rename(columns={\"be_hot_outlet_id\": \"hot_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "fe_hot_outlet_list = (\n", "    adding_ars_mapping[[\"hot_outlet_id\"]]\n", "    .rename(columns={\"hot_outlet_id\": \"hot_outlet_id\"})\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "\n", "all_hot_outlet_list = be_hot_outlet_list.append(fe_hot_outlet_list)\n", "all_hot_outlet_list = tuple(list(all_hot_outlet_list[\"hot_outlet_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "8848b131-83dc-4b2b-b67b-e189f76561b6", "metadata": {}, "outputs": [], "source": ["adding_tea_details = pd.merge(\n", "    adding_outlet_details,\n", "    adding_ars_mapping,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "del adding_outlet_details\n", "del adding_ars_mapping\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "139068d1-7c8b-49e8-9517-5ec0c55edbad", "metadata": {}, "outputs": [], "source": ["adding_tea_details[adding_tea_details[\"be_hot_outlet_id\"] == 0]"]}, {"cell_type": "code", "execution_count": null, "id": "34e62d21-1ca9-4b95-bf59-9e2976f2557a", "metadata": {}, "outputs": [], "source": ["adding_tea_details = adding_tea_details[adding_tea_details[\"be_hot_outlet_id\"] != 0]\n", "# adding_tea_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e240bbbe-0a18-4c9e-a115-d36806806476", "metadata": {}, "outputs": [], "source": ["adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]] = (\n", "    adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_tea_details = adding_tea_details.drop(columns={\"be_store_type\"})\n", "\n", "adding_tea_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "1a057e60-43dd-46f5-97d6-04e3e5db6b7a", "metadata": {}, "outputs": [], "source": ["adding_tea_details = pd.merge(\n", "    adding_tea_details,\n", "    all_hot_name,\n", "    on=[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_tea_details = adding_tea_details[adding_tea_details[\"be_store_type\"] != \"HP\"]\n", "\n", "adding_tea_details = adding_tea_details.drop(columns={\"be_store_type\"})\n", "\n", "adding_tea_details.head(1)"]}, {"cell_type": "markdown", "id": "6f1639ba-0e23-42a5-b19b-952fe79853dc", "metadata": {}, "source": ["# buckets query"]}, {"cell_type": "code", "execution_count": null, "id": "88aae646-1776-4ffa-9826-3f1f34fa9dbd", "metadata": {}, "outputs": [], "source": ["def buckets():\n", "    buckets = f\"\"\"\n", "    \n", "    with\n", "    bucket_ab as\n", "        (select item_id, outlet_id,\n", "            case when tag_value = 'A' then 'Bucket a' else 'Bucket b' end as bucket_ab\n", "\n", "                from rpc.item_outlet_tag_mapping\n", "                    where tag_type_id in (1) and active = 1\n", "                    and lake_active_record\n", "        ),\n", "\n", "    bucket_x as\n", "        (select item_id, outlet_id,\n", "            case when tag_value = 'Y' then 'Bucket x' else null end as bucket_x\n", "\n", "                from rpc.item_outlet_tag_mapping\n", "                    where tag_type_id in (6) and active = 1\n", "                    and lake_active_record\n", "        )\n", "\n", "            select ab.outlet_id as hot_outlet_id, ab.item_id,\n", "                case when x.bucket_x = 'Bucket x' then x.bucket_x else ab.bucket_ab end as buckets\n", "\n", "                from bucket_ab ab\n", "\n", "                    left join\n", "                        bucket_x x on x.item_id = ab.item_id and x.outlet_id = ab.outlet_id\n", "\n", "                        where \n", "                        ab.item_id in {item_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(buckets, trino)\n", "\n", "\n", "buckets = buckets()"]}, {"cell_type": "code", "execution_count": null, "id": "3501646b-40b1-4728-aea6-e8195d3c4011", "metadata": {}, "outputs": [], "source": ["# buckets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ac7ced8f-bb56-4105-845a-bd2b4462e0c2", "metadata": {}, "outputs": [], "source": ["adding_buckets_details = pd.merge(\n", "    adding_tea_details, buckets, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "del adding_tea_details\n", "del buckets\n", "gc.collect()\n", "\n", "adding_buckets_details[\"buckets\"] = adding_buckets_details[\"buckets\"].fillna(\"Bucket b\")\n", "\n", "# adding_buckets_details.head()"]}, {"cell_type": "markdown", "id": "43c50b2b-f86f-4fd2-8d50-88bd3ed93368", "metadata": {}, "source": ["# min max query"]}, {"cell_type": "code", "execution_count": null, "id": "d1f3e34b-6277-476e-8456-4979898f2f0e", "metadata": {}, "outputs": [], "source": ["def min_max():\n", "    min_max = f\"\"\"\n", "    \n", "    with\n", "    min_max as\n", "        (select * from ars.item_min_max_quantity)\n", "\n", "            select \n", "                item_id,\n", "                facility_id,\n", "                min_quantity,\n", "                max_quantity\n", "\n", "                    from min_max\n", "\n", "                        where \n", "                        item_id in {item_id_list}\n", "                        and lake_active_record\n", "    \n", "    \"\"\"\n", "    return read_sql_query(min_max, trino)\n", "\n", "\n", "min_max = min_max()"]}, {"cell_type": "code", "execution_count": null, "id": "4aa2f155-ccbd-4a2e-b6fb-e470dd627d5e", "metadata": {}, "outputs": [], "source": ["# min_max.head()"]}, {"cell_type": "code", "execution_count": null, "id": "43ef6a8d-52e3-4450-b3ea-a86d3e0a3a06", "metadata": {}, "outputs": [], "source": ["adding_min_max = pd.merge(\n", "    adding_buckets_details, min_max, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "del adding_buckets_details\n", "del min_max\n", "gc.collect()\n", "\n", "adding_min_max[[\"min_quantity\", \"max_quantity\"]] = (\n", "    adding_min_max[[\"min_quantity\", \"max_quantity\"]].fillna(0).astype(int)\n", ")\n", "\n", "# adding_min_max.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ef210d39-a6cc-456f-bb3a-53fa9df4f360", "metadata": {}, "outputs": [], "source": ["adding_min_max_be = (\n", "    adding_min_max.groupby([\"item_id\", \"be_hot_outlet_id\"])[\n", "        [\"min_quantity\", \"max_quantity\"]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(columns={\"min_quantity\": \"be_min_quantity\", \"max_quantity\": \"be_max_quantity\"})\n", "\n", "# adding_min_max_be.head()"]}, {"cell_type": "code", "execution_count": null, "id": "98a3cf5f-fa76-4ff6-a8d1-97426b9c9fcc", "metadata": {}, "outputs": [], "source": ["adding_min_max_backend = pd.merge(\n", "    adding_min_max, adding_min_max_be, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "del adding_min_max\n", "del adding_min_max_be\n", "gc.collect()\n", "\n", "adding_min_max_backend[[\"be_min_quantity\", \"be_max_quantity\"]] = (\n", "    adding_min_max_backend[[\"be_min_quantity\", \"be_max_quantity\"]].fillna(0).astype(int)\n", ")\n", "\n", "# adding_min_max_backend.head()"]}, {"cell_type": "markdown", "id": "9411a8db-3326-4254-9dc1-2475c7e77539", "metadata": {}, "source": ["# cpd query"]}, {"cell_type": "code", "execution_count": null, "id": "72e8e03a-4d98-4774-b5e8-84fb38de34b7", "metadata": {}, "outputs": [], "source": ["def cpd():\n", "    cpd = f\"\"\"\n", "    \n", "    with\n", "    cpd as\n", "        (select * from snorlax.default_cpd)\n", "\n", "            select \n", "                item_id,\n", "                outlet_id as hot_outlet_id,\n", "                cpd\n", "\n", "                    from cpd\n", "\n", "                        where \n", "                        item_id in {item_id_list}\n", "                        and lake_active_record    \n", "    \"\"\"\n", "    return read_sql_query(cpd, trino)\n", "\n", "\n", "cpd = cpd()"]}, {"cell_type": "code", "execution_count": null, "id": "640dac61-20f5-44ff-92de-4765136d0449", "metadata": {}, "outputs": [], "source": ["# cpd.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8bba78f7-0f52-4af7-9a62-7f307019625a", "metadata": {}, "outputs": [], "source": ["adding_cpd_details = pd.merge(\n", "    adding_min_max_backend, cpd, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "del adding_min_max_backend\n", "del cpd\n", "gc.collect()\n", "\n", "adding_cpd_details[\"cpd\"] = adding_cpd_details[\"cpd\"].fillna(0).astype(float)\n", "\n", "# adding_cpd_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d01b55d0-e2a8-4b9f-8116-4ba05977c776", "metadata": {}, "outputs": [], "source": ["adding_cpd_be = (\n", "    adding_cpd_details.groupby([\"item_id\", \"be_hot_outlet_id\"])[[\"cpd\"]]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(columns={\"cpd\": \"be_cpd\"})\n", "\n", "# adding_cpd_be.head()"]}, {"cell_type": "code", "execution_count": null, "id": "17443c4b-3876-4ba2-8c96-962a8152dfdd", "metadata": {}, "outputs": [], "source": ["adding_cpd_backend = pd.merge(\n", "    adding_cpd_details, adding_cpd_be, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "del adding_cpd_details\n", "del adding_cpd_be\n", "gc.collect()\n", "\n", "adding_cpd_backend[\"cpd\"] = adding_cpd_backend[\"cpd\"].fillna(0).astype(float)\n", "\n", "# adding_cpd_backend.head()"]}, {"cell_type": "markdown", "id": "a51ffa71-6479-4c38-9d00-2e220d0f4d58", "metadata": {}, "source": ["# inventory query"]}, {"cell_type": "code", "execution_count": null, "id": "79427409-6a0e-49ca-82eb-693a06c85874", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select * from ims.ims_item_inventory)\n", "\n", "            select \n", "                item_id,\n", "                outlet_id as hot_outlet_id,\n", "                sum(quantity) as actual_inv\n", "\n", "                    from inv\n", "\n", "                        where \n", "                        item_id in {item_id_list}\n", "                        and lake_active_record\n", "                        and active = 1\n", "\n", "                            group by 1,2\n", "    \"\"\"\n", "    return read_sql_query(inventory, trino)\n", "\n", "\n", "inventory = inventory()"]}, {"cell_type": "code", "execution_count": null, "id": "4efae247-af58-48e9-ba01-37118d4b5df4", "metadata": {}, "outputs": [], "source": ["# inventory.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9328aadc-015b-4e33-86a8-de365c77de08", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "\n", "# be_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "28a0816f-7293-4217-87f2-c23e54f14a75", "metadata": {}, "outputs": [], "source": ["adding_fe_inv_details = pd.merge(\n", "    adding_cpd_backend, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details = pd.merge(\n", "    adding_fe_inv_details, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]] = (\n", "    adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]].fillna(0).astype(int)\n", ")\n", "\n", "del adding_cpd_backend\n", "del inventory\n", "del adding_fe_inv_details\n", "del be_inv\n", "gc.collect()\n", "\n", "# adding_be_inv_details.head()"]}, {"cell_type": "markdown", "id": "006b31e0-6f14-4358-9c56-80c533308501", "metadata": {}, "source": ["# blocked inv query"]}, {"cell_type": "code", "execution_count": null, "id": "0f7ae0e9-4157-454c-bfb5-65ea25ffc24f", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select * from ims.ims_item_blocked_inventory)\n", "\n", "            select \n", "                item_id,\n", "                outlet_id as hot_outlet_id,\n", "                sum(quantity) as ttl_blocked_qty\n", "\n", "                    from blocked\n", "\n", "                        where \n", "                        item_id in {item_id_list}\n", "                        and lake_active_record\n", "                        and active = 1\n", "                        and blocked_type in (1,2,5)\n", "                        and quantity > 0\n", "\n", "                            group by 1,2\n", "                            \n", "    \"\"\"\n", "    return read_sql_query(blocked_inv, trino)\n", "\n", "\n", "blocked_inv = blocked_inv()"]}, {"cell_type": "code", "execution_count": null, "id": "1edad2c7-cb27-49e5-83e5-c1c8e697edca", "metadata": {}, "outputs": [], "source": ["# blocked_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "92e4ff28-e393-4aac-9cf2-7f6f05d79cd5", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")\n", "\n", "# be_blocked.head()"]}, {"cell_type": "code", "execution_count": null, "id": "edd8c636-2b0f-41be-8d6b-333f066df5c0", "metadata": {}, "outputs": [], "source": ["adding_fe_blocked_details = pd.merge(\n", "    adding_be_inv_details, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_blocked_details = pd.merge(\n", "    adding_fe_blocked_details,\n", "    be_blocked,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]] = (\n", "    adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "del adding_be_inv_details\n", "del blocked_inv\n", "del adding_fe_blocked_details\n", "del be_blocked\n", "gc.collect()\n", "\n", "adding_be_blocked_details[\"net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "adding_be_blocked_details[\"be_net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "\n", "# adding_be_blocked_details.head()"]}, {"cell_type": "markdown", "id": "757e875e-8595-4fa3-a835-98a1669ea8d5", "metadata": {}, "source": ["# pending putaway query"]}, {"cell_type": "code", "execution_count": null, "id": "6c84245b-585c-477c-869f-f9c13463bd4e", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    pp as\n", "        (select * from ims.ims_good_inventory),\n", "\n", "    variant_details as\n", "        (select * from rpc.product_product)\n", "\n", "            select \n", "                item_id,\n", "                outlet_id as hot_outlet_id,\n", "                sum(quantity) as pending_putaway\n", "\n", "                    from pp\n", "\n", "                        join\n", "                            variant_details vd on vd.variant_id = pp.variant_id and vd.lake_active_record\n", "\n", "                        where \n", "                        vd.item_id in {item_id_list}\n", "                        and pp.lake_active_record\n", "                        and pp.active = 1\n", "                        and pp.inventory_update_type_id in (28,76)\n", "                        and pp.quantity > 0\n", "\n", "                            group by 1,2\n", "\n", "    \"\"\"\n", "    return read_sql_query(pen_put, trino)\n", "\n", "\n", "pen_put = pen_put()"]}, {"cell_type": "code", "execution_count": null, "id": "835a9e77-44f7-4bfc-a8a5-a3ff87789647", "metadata": {}, "outputs": [], "source": ["# pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "90b5f0dc-8b9f-4667-b04f-a2579a8a3458", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")\n", "\n", "# be_pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "49746651-3df6-40ec-bce1-d822bc7f3d2b", "metadata": {}, "outputs": [], "source": ["adding_fe_pen_putaway_details = pd.merge(\n", "    adding_be_blocked_details, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.merge(\n", "    adding_fe_pen_putaway_details,\n", "    be_pen_put,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]] = (\n", "    adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "del adding_be_blocked_details\n", "del pen_put\n", "del adding_fe_pen_putaway_details\n", "del be_pen_put\n", "gc.collect()\n", "\n", "# adding_be_pen_putaway_details.head()"]}, {"cell_type": "markdown", "id": "cbb043b6-2983-474a-9252-2bbce3969b75", "metadata": {}, "source": ["# sto query"]}, {"cell_type": "code", "execution_count": null, "id": "fbf95061-e198-4c41-ad6e-95dfa83de663", "metadata": {}, "outputs": [], "source": ["def sto_details():\n", "    sto_details = f\"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details),\n", "\n", "    item_details as\n", "        (select * from supply_etls.item_details),\n", "\n", "    variant_details as\n", "        (select * from rpc.product_product\n", "            where\n", "                lake_active_record\n", "        ),\n", "\n", "    sto_details as\n", "        (select * from ims.ims_sto_details),\n", "\n", "    final_sto_details as\n", "        (select * from sto_details\n", "            where created_at >= current_date - interval '30' day\n", "            and lake_active_record\n", "        ),\n", "\n", "    sto_item_details as\n", "        (select * from po.sto_items),\n", "\n", "    final_sto_item_details as\n", "        (select * from sto_item_details\n", "            where created_at >= current_date - interval '30' day\n", "            and lake_active_record\n", "        ),\n", "\n", "    invoice_details as\n", "        (select * from pos.pos_invoice),\n", "\n", "    final_invoice_details as\n", "        (select * from invoice_details\n", "            where insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "            and lake_active_record\n", "            and invoice_type_id in (5,14,16)\n", "            and grofers_order_id is not null\n", "            and grofers_order_id != ''\n", "        ),\n", "\n", "    invoice_item_details as\n", "        (select * from pos.pos_invoice_product_details),\n", "\n", "    final_invoice_item_details as\n", "        (select * from invoice_item_details\n", "            where insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "            and lake_active_record\n", "        ),\n", "\n", "    adding_invoice_details as\n", "        (select \n", "            cast(fid.grofers_order_id as int) as sto_id,\n", "            vd.item_id,\n", "            sum(fiid.quantity) as billed_quantity\n", "\n", "                from final_invoice_details fid\n", "\n", "                    join\n", "                        final_invoice_item_details fiid on fiid.invoice_id = fid.id\n", "\n", "                    join\n", "                        variant_details vd on vd.variant_id = fiid.variant_id\n", "\n", "                        group by 1,2\n", "        ),\n", "\n", "    grn_details as\n", "        (select * from ims.ims_inward_invoice),\n", "\n", "    final_grn_detials as\n", "        (select * from grn_details\n", "            where\n", "                insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                and lake_active_record\n", "        ),\n", "\n", "    grn_item_details as\n", "        (select * from ims.ims_inventory_stock_details),\n", "\n", "    final_grn_item_details as\n", "        (select * from grn_item_details\n", "            where\n", "                insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                and lake_active_record\n", "        ),\n", "\n", "    adding_grn_details as\n", "        (select\n", "            cast(fid.grofers_order_id as int) as sto_id,\n", "            vd.item_id,\n", "            sum(fgid.\"delta\") as grn_quantity\n", "\n", "                from final_grn_item_details fgid\n", "\n", "                    join\n", "                        final_grn_detials fgd on fgd.grn_id = fgid.grn_id\n", "\n", "                    join\n", "                        variant_details vd on vd.variant_id = fgid.variant_id\n", "\n", "                    join\n", "                        final_invoice_details fid on fid.invoice_id = fgd.vendor_invoice_id\n", "\n", "                        group by 1,2\n", "        ),\n", "\n", "    discrepancy_details as\n", "        (select * from pos.discrepancy_note),\n", "\n", "    final_discrepancy_details as\n", "        (select * from discrepancy_details\n", "            where \n", "                (created_at >= current_date - interval '30' day)\n", "                and lake_active_record\n", "        ),\n", "\n", "    discrepancy_item_details as\n", "        (select * from pos.discrepancy_note_product_detail),\n", "\n", "    final_discrepancy_item_details as\n", "        (select * from discrepancy_item_details\n", "            where \n", "                lake_active_record\n", "        ),\n", "\n", "    sto_discrepancy_item_details as\n", "        (select\n", "            cast(fid.grofers_order_id as int) as sto_id,\n", "            item_id,\n", "            sum(quantity) as discrepancy_quantity\n", "\n", "                from final_discrepancy_details fdd\n", "\n", "                    join\n", "                        final_discrepancy_item_details fdid on fdid.dn_id_id = fdd.id\n", "\n", "                    join\n", "                        (select upc, item_id from variant_details group by 1,2) vd on vd.upc = fdid.upc_id\n", "\n", "                    join\n", "                        final_invoice_details fid on fid.invoice_id = fdd.vendor_invoice_id\n", "\n", "                            group by 1,2\n", "        ),\n", "\n", "    adding_all as\n", "        (select\n", "            date(fsd.created_at + interval '330' minute) as date,\n", "            fsd.outlet_id as be_inv_outlet_id,\n", "            fsd.merchant_outlet_id as hot_outlet_id,\n", "\n", "            fsd.sto_id,\n", "            fsid.item_id,\n", "\n", "            fsid.reserved_quantity as created_quantity,\n", "            coalesce(aid.billed_quantity,0) as billed_quantity,\n", "            coalesce(agd.grn_quantity,0) as grn_quantity,\n", "            coalesce(sdid.discrepancy_quantity,0) as discrepancy_quantity,\n", "\n", "            case \n", "                when ((fsid.reserved_quantity) <= (coalesce(agd.grn_quantity,0) + coalesce(sdid.discrepancy_quantity,0))) then 'expired'\n", "                when fsd.sto_state in (3,4) then 'expired'\n", "            else 'open' end as status\n", "\n", "                from final_sto_details fsd\n", "\n", "                    join\n", "                        final_sto_item_details fsid on fsid.sto_id = fsd.sto_id\n", "\n", "                    left join\n", "                        adding_invoice_details aid on aid.sto_id = fsd.sto_id and aid.item_id = fsid.item_id\n", "\n", "                    left join\n", "                        adding_grn_details agd on agd.sto_id = fsd.sto_id and agd.item_id = fsid.item_id\n", "\n", "                    left join\n", "                        sto_discrepancy_item_details sdid on sdid.sto_id = fsd.sto_id and sdid.item_id = fsid.item_id\n", "        ),\n", "\n", "    final_view as\n", "        (select\n", "            aa.hot_outlet_id,\n", "            aa.item_id,\n", "            sum(case \n", "                    when (created_quantity - (billed_quantity - (grn_quantity + discrepancy_quantity))) < 0 then 0\n", "                    else (created_quantity - (billed_quantity - (grn_quantity + discrepancy_quantity))) end\n", "                ) as open_sto_quantity,\n", "\n", "            sum(case \n", "                    when billed_quantity - (grn_quantity + discrepancy_quantity) < 0 then 0\n", "                    else billed_quantity - (grn_quantity + discrepancy_quantity) end\n", "                ) as in_transit_open_sto_quantity\n", "\n", "                from adding_all aa\n", "\n", "                    where\n", "                        status = 'open'\n", "\n", "                        group by 1,2\n", "        )\n", "\n", "            select * from final_view\n", "                where \n", "                    item_id in {item_id_list}\n", "                        \n", "    \"\"\"\n", "    return read_sql_query(sto_details, trino)\n", "\n", "\n", "sto_details = sto_details()"]}, {"cell_type": "code", "execution_count": null, "id": "ac18e8a7-b760-4cae-9f75-e8ee514a29f5", "metadata": {}, "outputs": [], "source": ["# sto_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d526a5b5-cd50-41b8-bfbb-63214fd6db8f", "metadata": {}, "outputs": [], "source": ["sto_details[[\"in_transit_open_sto_quantity\", \"open_sto_quantity\"]] = (\n", "    sto_details[[\"in_transit_open_sto_quantity\", \"open_sto_quantity\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0bf82b36-3225-4195-b428-c829776e3f63", "metadata": {}, "outputs": [], "source": ["be_sto = sto_details.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"in_transit_open_sto_quantity\": \"be_in_transit_open_sto_quantity\",\n", "        \"open_sto_quantity\": \"be_open_sto_quantity\",\n", "    }\n", ")\n", "\n", "be_sto[[\"be_in_transit_open_sto_quantity\", \"be_open_sto_quantity\"]] = (\n", "    be_sto[[\"be_in_transit_open_sto_quantity\", \"be_open_sto_quantity\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "# be_sto.head()"]}, {"cell_type": "code", "execution_count": null, "id": "205562ae-d8b8-4562-8598-2465f604aec8", "metadata": {}, "outputs": [], "source": ["adding_sto_details = pd.merge(\n", "    adding_be_pen_putaway_details,\n", "    sto_details,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_sto_details[[\"in_transit_open_sto_quantity\", \"open_sto_quantity\"]] = (\n", "    adding_sto_details[[\"in_transit_open_sto_quantity\", \"open_sto_quantity\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_sto_details = pd.merge(\n", "    adding_sto_details, be_sto, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_sto_details[[\"be_in_transit_open_sto_quantity\", \"be_open_sto_quantity\"]] = (\n", "    adding_be_sto_details[[\"be_in_transit_open_sto_quantity\", \"be_open_sto_quantity\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "del adding_be_pen_putaway_details\n", "del sto_details\n", "del adding_sto_details\n", "del be_sto\n", "gc.collect()\n", "\n", "# adding_be_sto_details.head()"]}, {"cell_type": "markdown", "id": "bfa1ddf9-e471-4fa6-a4c6-3536a7631a95", "metadata": {}, "source": ["# po query"]}, {"cell_type": "code", "execution_count": null, "id": "efdf3e7d-f91b-46cd-a646-bc5a9b8780ca", "metadata": {}, "outputs": [], "source": ["def open_po():\n", "    open_po = f\"\"\"\n", "    \n", "    with\n", "    po_details as\n", "        (select\n", "            poi.po_id,\n", "            po.po_number,\n", "            date(po.created_at + interval '330' minute) as po_issue_date,\n", "            date(po.expiry_date + interval '330' minute) as po_expiry_date,\n", "            ps.po_scheduled_date,\n", "            poi.manufacturer_id,\n", "            poi.manufacturer_name,\n", "            po.vendor_id,\n", "            po.vendor_name,\n", "            po.outlet_id as be_hot_outlet_id,\n", "            poi.item_id,\n", "            poi.name || ' ' || poi.uom_text as item_name,\n", "            poi.units_ordered as po_quantity,\n", "            case when grn.grn_quantity is null then 0 else grn.grn_quantity end as grn_quantity\n", "\n", "                from po.purchase_order_items poi\n", "\n", "                    join\n", "                        po.purchase_order po on po.id = poi.po_id\n", "                            and po.created_at >= cast(current_date as timestamp) - interval '60' day - interval '330' minute\n", "                            and po.lake_active_record\n", "\n", "                    left join\n", "                        (select po_id, po_state_id\n", "                            from po.purchase_order_status pos\n", "                                where pos.created_at >= cast(current_date as timestamp) - interval '60' day - interval '330' minute\n", "                                and lake_active_record\n", "                        ) pos on pos.po_id = po.id\n", "\n", "                    left join\n", "                        (select po_id, item_id, sum(quantity) as grn_quantity\n", "                            from po.po_grn\n", "                                where (insert_ds_ist >= cast(current_date - interval '60' day as varchar))\n", "                                and lake_active_record\n", "                                        group by 1,2\n", "                        ) grn on grn.item_id = poi.item_id and grn.po_id = poi.po_id\n", "\n", "                    left join\n", "                        (select po_id_id,  (schedule_date_time + interval '330' minute) as po_scheduled_date\n", "                            from po.po_schedule\n", "                                where lake_active_record\n", "                        ) ps on ps.po_id_id = po.id\n", "\n", "                        where (pos.po_state_id in (2,3,13,14,15) or po.is_multiple_grn = 1)\n", "                            and pos.po_state_id not in (4,5,8,10) and po.po_type_id !=11\n", "                            and (po.created_at >= cast(current_date as timestamp) - interval '60' day - interval '330' minute)\n", "                            and po.active = 1\n", "                            and poi.lake_active_record\n", "        ),\n", "\n", "    final_po_details as\n", "        (select po_number, po_issue_date, po_expiry_date, date(po_scheduled_date) as po_scheduled_date,\n", "            be_hot_outlet_id, item_id, po_quantity, grn_quantity, (po_quantity - grn_quantity) as remaining_po_quantity\n", "\n", "                from po_details\n", "        )\n", "\n", "            select be_hot_outlet_id, item_id,\n", "                sum(remaining_po_quantity) as total_po_quantity,\n", "                sum(case when po_scheduled_date = date(current_date) then remaining_po_quantity else 0 end) as t_scheduled_quantity,\n", "                sum(case when po_scheduled_date = date(current_date + interval '1' day) then remaining_po_quantity else 0 end) as t1_scheduled_quantity,\n", "                sum(case when po_scheduled_date = date(current_date + interval '2' day) then remaining_po_quantity else 0 end) as t2_scheduled_quantity\n", "\n", "                    from final_po_details\n", "\n", "                        where \n", "                        item_id in {item_id_list}\n", "\n", "                            group by 1,2\n", "                        \n", "    \"\"\"\n", "    return read_sql_query(open_po, trino)\n", "\n", "\n", "open_po = open_po()"]}, {"cell_type": "code", "execution_count": null, "id": "5d6a514d-704d-48d4-8c29-c0c5d69b0490", "metadata": {}, "outputs": [], "source": ["# open_po.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9887e6f0-fca1-4bd2-bb42-34f5df34ea88", "metadata": {}, "outputs": [], "source": ["adding_be_open_po_details = pd.merge(\n", "    adding_be_sto_details, open_po, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_open_po_details[\n", "    [\n", "        \"total_po_quantity\",\n", "        \"t_scheduled_quantity\",\n", "        \"t1_scheduled_quantity\",\n", "        \"t2_scheduled_quantity\",\n", "    ]\n", "] = (\n", "    adding_be_open_po_details[\n", "        [\n", "            \"total_po_quantity\",\n", "            \"t_scheduled_quantity\",\n", "            \"t1_scheduled_quantity\",\n", "            \"t2_scheduled_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "del adding_be_sto_details\n", "del open_po\n", "gc.collect()\n", "\n", "# adding_be_open_po_details.head()"]}, {"cell_type": "markdown", "id": "b8d1726e-4d94-43e3-a042-6f6bfc217dff", "metadata": {}, "source": ["# transfer CPD"]}, {"cell_type": "code", "execution_count": null, "id": "f7c778cd-cdd9-44cc-aaf8-37a3c3ab6df3", "metadata": {}, "outputs": [], "source": ["def transfer_cpd():\n", "    transfer_cpd = f\"\"\"\n", "    \n", "    with\n", "    transfer_cpd as\n", "        (select * from ars.outlet_item_aps_derived_cpd),\n", "\n", "    final_transfer_cpd as\n", "        (select item_id, outlet_id as hot_outlet_id, cpd as transfer_cpd\n", "            from transfer_cpd\n", "                where for_date = (select max(for_date) from transfer_cpd where insert_ds_ist >= cast(current_date - interval '2' day as varchar))\n", "                and lake_active_record\n", "        )\n", "\n", "            select * from final_transfer_cpd\n", "                where transfer_cpd > 0\n", "                and item_id in {item_id_list}\n", "    \n", "    \n", "    \"\"\"\n", "    return read_sql_query(transfer_cpd, trino)\n", "\n", "\n", "transfer_cpd = transfer_cpd()"]}, {"cell_type": "code", "execution_count": null, "id": "df38f30a-8207-48b8-a64a-54bba0749daa", "metadata": {}, "outputs": [], "source": ["# transfer_cpd.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ab728032-13f3-42b7-89f0-419334491957", "metadata": {}, "outputs": [], "source": ["adding_transfer_cpd = pd.merge(\n", "    adding_be_open_po_details, transfer_cpd, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_transfer_cpd[\"transfer_cpd\"] = (\n", "    adding_transfer_cpd[\"transfer_cpd\"].fillna(0.045).astype(float)\n", ")\n", "\n", "del adding_be_open_po_details\n", "del transfer_cpd\n", "gc.collect()\n", "\n", "# adding_transfer_cpd.head()"]}, {"cell_type": "markdown", "id": "c6150bf4-5980-445f-b4c5-1f01411c6977", "metadata": {}, "source": ["# create indicator"]}, {"cell_type": "code", "execution_count": null, "id": "0ad63a87-ab36-4045-a3f9-e644ef2a748e", "metadata": {}, "outputs": [], "source": ["# frontend demand check\n", "adding_transfer_cpd[\"frontend_demand\"] = 2 * adding_transfer_cpd[\"transfer_cpd\"]\n", "\n", "adding_transfer_cpd[\"get_required_min\"] = 1\n", "\n", "adding_transfer_cpd[\"get_required_min\"] = adding_transfer_cpd[\n", "    [\"frontend_demand\", \"get_required_min\"]\n", "].max(axis=1)\n", "\n", "adding_transfer_cpd.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "70bf48e7-0273-4114-acaf-ed5298e2fb92", "metadata": {}, "outputs": [], "source": ["# frontend inventory\n", "adding_transfer_cpd[\"fe_on_ind\"] = np.where(\n", "    adding_transfer_cpd[\"net_inventory\"] > 0, 1, 0\n", ")\n", "\n", "\n", "# frontend pending putaway\n", "adding_transfer_cpd[\"fe_pending_putaway_ind\"] = np.where(\n", "    (adding_transfer_cpd[\"net_inventory\"] + adding_transfer_cpd[\"pending_putaway\"]) > 0,\n", "    1,\n", "    0,\n", ")\n", "\n", "# frontend in-transit quantity (billed)\n", "adding_transfer_cpd[\"fe_in_transit_ind\"] = np.where(\n", "    (\n", "        adding_transfer_cpd[\"net_inventory\"]\n", "        + adding_transfer_cpd[\"pending_putaway\"]\n", "        + adding_transfer_cpd[\"in_transit_open_sto_quantity\"]\n", "    )\n", "    > 0,\n", "    1,\n", "    0,\n", ")\n", "\n", "# frontend open sto quantity (not billed)\n", "adding_transfer_cpd[\"fe_sto_ind\"] = np.where(\n", "    (\n", "        adding_transfer_cpd[\"net_inventory\"]\n", "        + adding_transfer_cpd[\"pending_putaway\"]\n", "        + adding_transfer_cpd[\"in_transit_open_sto_quantity\"]\n", "        + adding_transfer_cpd[\"open_sto_quantity\"]\n", "    )\n", "    > 0,\n", "    1,\n", "    0,\n", ")\n", "\n", "# frontend requirement\n", "adding_transfer_cpd[\"frontend_requirement\"] = np.where(\n", "    np.ceil(adding_transfer_cpd[\"get_required_min\"])\n", "    - (\n", "        adding_transfer_cpd[\"net_inventory\"]\n", "        + adding_transfer_cpd[\"pending_putaway\"]\n", "        + adding_transfer_cpd[\"in_transit_open_sto_quantity\"]\n", "        + adding_transfer_cpd[\"open_sto_quantity\"]\n", "    )\n", "    > 0,\n", "    np.ceil(adding_transfer_cpd[\"get_required_min\"])\n", "    - (\n", "        adding_transfer_cpd[\"net_inventory\"]\n", "        + adding_transfer_cpd[\"pending_putaway\"]\n", "        + adding_transfer_cpd[\"in_transit_open_sto_quantity\"]\n", "        + adding_transfer_cpd[\"open_sto_quantity\"]\n", "    ),\n", "    0,\n", ")\n", "\n", "# frontend requirment at backend\n", "backend_demand = (\n", "    adding_transfer_cpd.groupby([\"item_id\", \"be_hot_outlet_id\"])[\n", "        [\"frontend_requirement\"]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", "    .rename(columns={\"frontend_requirement\": \"backend_demand\"})\n", ")\n", "\n", "\n", "# adding backend demand\n", "adding_transfer_cpd = pd.merge(\n", "    adding_transfer_cpd, backend_demand, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_transfer_cpd[\"backend_demand\"] = (\n", "    adding_transfer_cpd[\"backend_demand\"].fillna(0).astype(int)\n", ")\n", "\n", "# backend inventory\n", "adding_transfer_cpd[\"be_on_ind\"] = np.where(\n", "    adding_transfer_cpd[\"fe_sto_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        adding_transfer_cpd[\"backend_demand\"] > adding_transfer_cpd[\"be_net_inventory\"],\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "# backend pending putaway\n", "adding_transfer_cpd[\"be_pending_putaway_ind\"] = np.where(\n", "    adding_transfer_cpd[\"be_on_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            adding_transfer_cpd[\"backend_demand\"]\n", "            > (\n", "                adding_transfer_cpd[\"be_net_inventory\"]\n", "                + adding_transfer_cpd[\"be_pending_putaway\"]\n", "            )\n", "        ),\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "\n", "# backend in-transit quantity (billed)\n", "adding_transfer_cpd[\"be_in_transit_ind\"] = np.where(\n", "    adding_transfer_cpd[\"be_on_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            adding_transfer_cpd[\"backend_demand\"]\n", "            > (\n", "                adding_transfer_cpd[\"be_net_inventory\"]\n", "                + adding_transfer_cpd[\"be_pending_putaway\"]\n", "                + adding_transfer_cpd[\"be_in_transit_open_sto_quantity\"]\n", "            )\n", "        ),\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "# backend open sto quantity (not billed)\n", "adding_transfer_cpd[\"be_sto_ind\"] = np.where(\n", "    adding_transfer_cpd[\"be_on_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            adding_transfer_cpd[\"backend_demand\"]\n", "            > (\n", "                adding_transfer_cpd[\"be_net_inventory\"]\n", "                + adding_transfer_cpd[\"be_pending_putaway\"]\n", "                + adding_transfer_cpd[\"be_in_transit_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"be_open_sto_quantity\"]\n", "            )\n", "        ),\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "# backend today scheduled\n", "adding_transfer_cpd[\"be_today_scheduled_ind\"] = np.where(\n", "    adding_transfer_cpd[\"be_on_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            adding_transfer_cpd[\"backend_demand\"]\n", "            > (\n", "                adding_transfer_cpd[\"be_net_inventory\"]\n", "                + adding_transfer_cpd[\"be_pending_putaway\"]\n", "                + adding_transfer_cpd[\"be_in_transit_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"be_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"t_scheduled_quantity\"]\n", "            )\n", "        ),\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "# backend tomorrow scheduled\n", "adding_transfer_cpd[\"be_tomorrow_scheduled_ind\"] = np.where(\n", "    adding_transfer_cpd[\"be_on_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            adding_transfer_cpd[\"backend_demand\"]\n", "            > (\n", "                adding_transfer_cpd[\"be_net_inventory\"]\n", "                + adding_transfer_cpd[\"be_pending_putaway\"]\n", "                + adding_transfer_cpd[\"be_in_transit_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"be_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"t_scheduled_quantity\"]\n", "                + adding_transfer_cpd[\"t1_scheduled_quantity\"]\n", "            )\n", "        ),\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "# backend day after tomorrow scheduled\n", "adding_transfer_cpd[\"be_day_after_tomorrow_scheduled_ind\"] = np.where(\n", "    adding_transfer_cpd[\"be_on_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            adding_transfer_cpd[\"backend_demand\"]\n", "            > (\n", "                adding_transfer_cpd[\"be_net_inventory\"]\n", "                + adding_transfer_cpd[\"be_pending_putaway\"]\n", "                + adding_transfer_cpd[\"be_in_transit_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"be_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"t_scheduled_quantity\"]\n", "                + adding_transfer_cpd[\"t1_scheduled_quantity\"]\n", "                + adding_transfer_cpd[\"t2_scheduled_quantity\"]\n", "            )\n", "        ),\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "\n", "# backend total open PO\n", "adding_transfer_cpd[\"be_open_po_ind\"] = np.where(\n", "    adding_transfer_cpd[\"be_on_ind\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            adding_transfer_cpd[\"backend_demand\"]\n", "            > (\n", "                adding_transfer_cpd[\"be_net_inventory\"]\n", "                + adding_transfer_cpd[\"be_pending_putaway\"]\n", "                + adding_transfer_cpd[\"be_in_transit_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"be_open_sto_quantity\"]\n", "                + adding_transfer_cpd[\"total_po_quantity\"]\n", "            )\n", "        ),\n", "        0,\n", "        1,\n", "    ),\n", ")\n", "\n", "# adding_transfer_cpd.head()"]}, {"cell_type": "markdown", "id": "7e5c0c61-3192-427b-92d6-c557f11ec2f6", "metadata": {}, "source": ["# backend items penetration query"]}, {"cell_type": "code", "execution_count": null, "id": "659171da-e9c4-495d-9250-147bcb795fa3", "metadata": {}, "outputs": [], "source": ["def backend_item_penetration():\n", "    backend_item_penetration = \"\"\"\n", "\n", "    with\n", "    backend_item_penetration as\n", "        (select backend_facility_id, item_id, weights, updated_at\n", "            from metrics.backend_item_cart_penetration\n", "        )\n", "\n", "            select backend_facility_id as be_facility_id, item_id, weights\n", "\n", "                from backend_item_penetration\n", "\n", "                    where updated_at = (select max(updated_at) as updated_at from metrics.backend_item_cart_penetration)\n", "    \n", "    \"\"\"\n", "    return read_sql_query(backend_item_penetration, redshift)\n", "\n", "\n", "backend_item_penetration = backend_item_penetration()"]}, {"cell_type": "code", "execution_count": null, "id": "d3905761-4359-45b0-bcb4-5cc131af8392", "metadata": {}, "outputs": [], "source": ["# backend_item_penetration.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aacb0efc-a441-44ee-a06e-86989c6fe7cb", "metadata": {}, "outputs": [], "source": ["adding_be_item_pen = pd.merge(\n", "    adding_transfer_cpd,\n", "    backend_item_penetration,\n", "    on=[\"item_id\", \"be_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "del adding_transfer_cpd\n", "del backend_item_penetration\n", "gc.collect()\n", "\n", "adding_be_item_pen[\"weights\"] = adding_be_item_pen[\"weights\"].fillna(0).astype(float)\n", "\n", "# adding_be_item_pen.head()"]}, {"cell_type": "markdown", "id": "a4c34de7-837c-4861-ac60-4b5330028948", "metadata": {}, "source": ["# backend store penetration query"]}, {"cell_type": "code", "execution_count": null, "id": "6c0d1cfc-f31e-4aca-9c2a-d82fcc228c36", "metadata": {}, "outputs": [], "source": ["def backend_store_penetration():\n", "    backend_store_penetration = \"\"\"\n", "\n", "    with\n", "    backend_store_penetration as\n", "        (select backend_facility_id, facility_id, store_weight, updated_at\n", "            from metrics.backend_store_penetration\n", "        )\n", "\n", "            select cast(backend_facility_id as int) as be_facility_id, facility_id, store_weight\n", "\n", "                from backend_store_penetration\n", "\n", "                    where updated_at = (select max(updated_at) as updated_at from backend_store_penetration)\n", "    \n", "    \"\"\"\n", "    return read_sql_query(backend_store_penetration, redshift)\n", "\n", "\n", "backend_store_penetration = backend_store_penetration()"]}, {"cell_type": "code", "execution_count": null, "id": "8041e26e-2b18-433b-aa9a-2b0475dc3ca3", "metadata": {}, "outputs": [], "source": ["# backend_store_penetration.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d987da67-b563-43ea-8892-4cc9c5bfa8f8", "metadata": {}, "outputs": [], "source": ["adding_store_penetration = pd.merge(\n", "    adding_be_item_pen,\n", "    backend_store_penetration,\n", "    on=[\"be_facility_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_store_penetration[\"store_weight\"] = (\n", "    adding_store_penetration[\"store_weight\"].fillna(0).astype(float)\n", ")\n", "\n", "del adding_be_item_pen\n", "del backend_store_penetration\n", "gc.collect()\n", "\n", "# adding_store_penetration.head()"]}, {"cell_type": "markdown", "id": "210747d1-9bbe-4d8b-a274-2ed9fe456892", "metadata": {}, "source": ["# vendor details"]}, {"cell_type": "code", "execution_count": null, "id": "ae44111c-695b-4497-b527-a2e1ec489fbb", "metadata": {}, "outputs": [], "source": ["def vendor_details():\n", "    vendor_details = f\"\"\"\n", "    \n", "    with\n", "    vendor_details as\n", "        (select vfa.facility_id as be_facility_id, vfa.item_id,\n", "            vfa.vendor_id, vfa.group_id, pl.po_cycle,\n", "            pl.load_size, pl.load_type, pl.tat_day\n", "\n", "                from vms.vms_vendor_facility_alignment vfa\n", "\n", "                    left join\n", "                        vms.vms_vendor_new_pi_logic pl on pl.facility_id = vfa.facility_id\n", "                        and pl.vendor_id = vfa.vendor_id and pl.group_id = vfa.group_id and pl.active = true and pl.lake_active_record\n", "                    \n", "                    join\n", "                        vms.vms_vendor vv on vv.id = vfa.vendor_id and vv.active = 1 and vv.lake_active_record\n", "\n", "                        \n", "\n", "                        where vfa.active = 1 and vfa.lake_active_record = true\n", "                        and vfa.lake_active_record\n", "        )\n", "\n", "            select * from vendor_details\n", "                where be_facility_id in {all_facility_id}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(vendor_details, trino)\n", "\n", "\n", "vendor_details = vendor_details()"]}, {"cell_type": "code", "execution_count": null, "id": "84748480-000c-4e26-8775-b75dccefa703", "metadata": {}, "outputs": [], "source": ["# vendor_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21bb6da9-59f8-4216-b46b-e4d8caccc8a9", "metadata": {}, "outputs": [], "source": ["adding_vendor_details = pd.merge(\n", "    adding_store_penetration,\n", "    vendor_details,\n", "    on=[\"be_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "del [[adding_store_penetration, vendor_details]]\n", "gc.collect()\n", "\n", "adding_vendor_details.head()"]}, {"cell_type": "markdown", "id": "26e3c975-9b64-4a14-bdfc-583473f744ac", "metadata": {}, "source": ["# child item details"]}, {"cell_type": "code", "execution_count": null, "id": "ed81a8ec-4ffb-42a2-b99a-a6145a6dc93e", "metadata": {}, "outputs": [], "source": ["def child_item_details():\n", "    child_item_details = \"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select * from rpc.item_details)\n", "        \n", "            select parent_item_id, item_id\n", "                from item_details\n", "                    where active = 1\n", "                    and approved = 1\n", "                    and item_id != parent_item_id\n", "                    and length(cast(parent_item_id as varchar)) > 7\n", "                    and lake_active_record\n", "    \n", "    \"\"\"\n", "    return read_sql_query(child_item_details, trino)\n", "\n", "\n", "child_item_details = child_item_details()"]}, {"cell_type": "code", "execution_count": null, "id": "f9882388-0bad-4e87-a10d-c10c9335f0cb", "metadata": {}, "outputs": [], "source": ["child_item_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "76dc66aa-ea61-48ce-88ad-5b8139a09652", "metadata": {}, "outputs": [], "source": ["adding_child_item_details = pd.merge(\n", "    adding_vendor_details, child_item_details, on=[\"item_id\"], how=\"left\"\n", ")\n", "\n", "adding_child_item_details[\"parent_item_id\"] = (\n", "    adding_child_item_details[\"parent_item_id\"]\n", "    .fillna(adding_child_item_details[\"item_id\"])\n", "    .astype(int)\n", ")\n", "\n", "del [adding_vendor_details, child_item_details]\n", "gc.collect()\n", "\n", "adding_child_item_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "31adc962-1f95-4a69-a156-babfe0f8159d", "metadata": {}, "outputs": [], "source": ["adding_child_item_details[\"updated_at\"] = pd.to_datetime(\n", "    datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "51db871a-ab2a-428f-a1b9-f27621b8a55d", "metadata": {}, "outputs": [], "source": ["def be_name():\n", "    be_name = \"\"\"\n", "    \n", "    select\n", "    hot_outlet_id as be_hot_outlet_id,\n", "    facility_name as be_facility_name\n", "    from supply_etls.outlet_details\n", "    where ars_check = 1\n", "    group by 1,2\n", "    \n", "    \"\"\"\n", "    return read_sql_query(be_name, trino)\n", "\n", "\n", "be_name = be_name()"]}, {"cell_type": "code", "execution_count": null, "id": "17a6cc33-ae48-4968-8f68-3fe4e8b6b15e", "metadata": {}, "outputs": [], "source": ["be_name.head()"]}, {"cell_type": "code", "execution_count": null, "id": "136f3f0a-bc6e-4557-9876-8c632e2e366e", "metadata": {}, "outputs": [], "source": ["adding_child_item_details = pd.merge(\n", "    adding_child_item_details, be_name, on=[\"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_child_item_details[\"be_facility_name\"] = adding_child_item_details[\n", "    \"be_facility_name\"\n", "].fillna(\"No Tea Tagging\")\n", "adding_child_item_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "84b32448-8a54-45c6-a400-5a60507b8b0e", "metadata": {}, "outputs": [], "source": ["adding_child_item_details = adding_child_item_details[\n", "    [\n", "        \"updated_at\",\n", "        \"store_assortment_type\",\n", "        \"city_name\",\n", "        \"hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0_category\",\n", "        \"l1_category\",\n", "        \"l2_category\",\n", "        \"p_type\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"cpd\",\n", "        \"transfer_cpd\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"open_sto_quantity\",\n", "        \"in_transit_open_sto_quantity\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"be_open_sto_quantity\",\n", "        \"be_in_transit_open_sto_quantity\",\n", "        \"total_po_quantity\",\n", "        \"t_scheduled_quantity\",\n", "        \"t1_scheduled_quantity\",\n", "        \"t2_scheduled_quantity\",\n", "        # \"frontend_demand\",\n", "        # \"get_required_min\",\n", "        # \"fe_on_ind\",\n", "        # \"fe_pending_putaway_ind\",\n", "        # \"fe_in_transit_ind\",\n", "        # \"fe_sto_ind\",\n", "        # \"frontend_requirement\",\n", "        # \"backend_demand\",\n", "        # \"be_on_ind\",\n", "        # \"be_pending_putaway_ind\",\n", "        # \"be_in_transit_ind\",\n", "        # \"be_sto_ind\",\n", "        # \"be_today_scheduled_ind\",\n", "        # \"be_tomorrow_scheduled_ind\",\n", "        # \"be_open_po_ind\",\n", "        # \"be_day_after_tomorrow_scheduled_ind\",\n", "        # \"weights\",\n", "        # \"store_weight\",\n", "        # \"vendor_id\",\n", "        # \"group_id\",\n", "        # \"po_cycle\",\n", "        # \"load_size\",\n", "        # \"load_type\",\n", "        # \"tat_day\",\n", "        # \"parent_item_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "a6457deb-c12e-4021-87bf-ea5553779978", "metadata": {}, "outputs": [], "source": ["adding_child_item_details.shape"]}, {"cell_type": "code", "execution_count": null, "id": "44462009-a4bd-429e-a185-086891ed181e", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    adding_child_item_details,\n", "    \"1BwGIi-GLK2lOVGEBJgNV0UhP9Ba_sjAERvmE09vF8Lk\",\n", "    \"new_raw_data\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1f945051-4c8d-486c-a556-7c86a3c65083", "metadata": {}, "outputs": [], "source": ["adding_child_item_details = pd.DataFrame()"]}, {"cell_type": "raw", "id": "12f5a4bc-b839-43f5-b605-9abe1195334c", "metadata": {}, "source": ["# p_type summary"]}, {"cell_type": "raw", "id": "b3beb9bd-5051-4da9-9f7b-80dd2f024999", "metadata": {}, "source": ["def p_type_summary():\n", "    p_type_summary = \"\"\"\n", "    \n", "    with\n", "    raw_data as\n", "        (select * from metrics.new_event_metrics_reporting),\n", "\n", "\n", "    be_all_details as\n", "        (select distinct be_facility_id, item_id, event_flag, assortment_type, p_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_all_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, p_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_all_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    be_city_details as\n", "        (select distinct city_name, be_facility_id, item_id, event_flag, assortment_type, p_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_city_view as\n", "        (select city_name, event_flag, assortment_type, p_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_city_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    fe_all_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, p_type,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    fe_city_view as\n", "        (select city_name, event_flag, assortment_type, p_type,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    combine_fe_details as\n", "        (\n", "            select * from fe_all_view\n", "                union\n", "            select * from fe_city_view\n", "        ),\n", "\n", "    combine_be_details as\n", "        (\n", "            select * from be_all_view\n", "                union\n", "            select * from be_city_view\n", "        ),\n", "\n", "    final as\n", "        (select cfd.city_name, cfd.event_flag, cfd.assortment_type, cfd.p_type,\n", "            after_be_availability, availability, after_pp_availability, after_intranist_availability,\n", "            skus_count, active_skus_count, tea_tagged_count, non_tea_count,\n", "            ds_inv, ds_pending_putaway_inv, ds_open_sto_qty, ds_ttl_po_qty, ds_ttl_po_grn_quantity, ds_ttl_open_po_quantity,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            today_sales_qty, today_sales_value, today_carts, yesterday_sales_qty, yesterday_sales_value, yesterday_carts, l7days_sales_qty, l7days_sales_value, l7days_carts, ttl_sales_qty, ttl_sales_value, ttl_carts,\n", "            today_dump_quantity, today_dump_value, yesterday_dump_quantity, yesterday_dump_value, l7days_dump_quantity, l7days_dump_value, ttl_dump_quantity, ttl_dump_value,\n", "            be_today_dump_quantity, be_today_dump_value, be_yesterday_dump_quantity, be_yesterday_dump_value, be_l7days_dump_quantity, be_l7days_dump_value, be_ttl_dump_quantity, be_ttl_dump_value,\n", "            t_v1_indent, t_v2_indent, t_total_drop_quantity, t_new_inward_drop, t_new_storage_drop, t_new_truck_load_drop, t_new_picking_capacity_sku_drop, t_new_picking_capacity_quantity_drop, t_new_loose_quantity_drop,\n", "            t1_v1_indent, t1_v2_indent, t1_total_drop_quantity, t1_new_inward_drop, t1_new_storage_drop, t1_new_truck_load_drop, t1_new_picking_capacity_sku_drop, t1_new_picking_capacity_quantity_drop, t1_new_loose_quantity_drop,\n", "            t7_v1_indent, t7_v2_indent, t7_total_drop_quantity, t7_new_inward_drop, t7_new_storage_drop, t7_new_truck_load_drop, t7_new_picking_capacity_sku_drop, t7_new_picking_capacity_quantity_drop, t7_new_loose_quantity_drop\n", "\n", "                from combine_fe_details cfd\n", "\n", "                    left join\n", "                        combine_be_details cbd on cbd.city_name = cfd.city_name and cbd.event_flag = cfd.event_flag\n", "                        and cbd.assortment_type = cfd.assortment_type and cbd.p_type = cfd.p_type\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end, \n", "                    event_flag, assortment_type, p_type\n", "\n", "    \"\"\"\n", "    return read_sql_query(p_type_summary, redshift)\n", "\n", "\n", "p_type_summary = p_type_summary()"]}, {"cell_type": "raw", "id": "63fa087f-bfe1-4db7-8bfc-45694c8fe6b1", "metadata": {}, "source": ["p_type_summary.head(1)"]}, {"cell_type": "raw", "id": "531a8214-f17c-4b33-adf2-f1c90a0a0107", "metadata": {}, "source": ["pb.to_sheets(\n", "    p_type_summary, \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\", \"city_p_type_raw\"\n", ")"]}, {"cell_type": "raw", "id": "2339cdef-fe4f-49c0-8b92-9e5913154f4e", "metadata": {}, "source": ["p_type_summary = pd.DataFrame()"]}, {"cell_type": "raw", "id": "b624e78d-afab-4c74-9821-fc254191a265", "metadata": {}, "source": ["# store wise summary"]}, {"cell_type": "raw", "id": "cc559a98-e54d-4d9d-a6b7-e20f3fd5cedd", "metadata": {}, "source": ["def store_summary():\n", "    store_summary = \"\"\"\n", "   \n", "    with\n", "    raw_data as\n", "        (select * from metrics.new_event_metrics_reporting),\n", "\n", "\n", "    be_all_details as\n", "        (select distinct facility_id, be_facility_id, item_id, event_flag, assortment_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_all_view as\n", "        (select 'All' as city_name, facility_id, be_facility_id, event_flag, assortment_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_all_details\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    be_city_details as\n", "        (select distinct city_name, facility_id, be_facility_id, item_id, event_flag, assortment_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_city_view as\n", "        (select city_name, facility_id, be_facility_id, event_flag, assortment_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_city_details\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    fe_all_view as\n", "        (select 'All' as city_name, event_flag, assortment_type,\n", "            facility_id, be_facility_id, facility_name, be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    fe_city_view as\n", "        (select city_name, event_flag, assortment_type,\n", "            facility_id, be_facility_id, facility_name, be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    combine_fe_details as\n", "        (\n", "            select * from fe_all_view\n", "                union\n", "            select * from fe_city_view\n", "        ),\n", "\n", "    combine_be_details as\n", "        (\n", "            select * from be_all_view\n", "                union\n", "            select * from be_city_view\n", "        ),\n", "\n", "    final as\n", "        (select cfd.city_name, cfd.event_flag, cfd.assortment_type,\n", "            cfd.facility_id, cfd.be_facility_id, facility_name, be_facility_name,\n", "            after_be_availability, availability, after_pp_availability, after_intranist_availability,\n", "            skus_count, active_skus_count, tea_tagged_count, non_tea_count, min_quantity, max_quantity,\n", "            ds_inv, ds_pending_putaway_inv, ds_open_sto_qty, ds_ttl_po_qty, ds_ttl_po_grn_quantity, ds_ttl_open_po_quantity,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            today_sales_qty, today_sales_value, today_carts, yesterday_sales_qty, yesterday_sales_value, yesterday_carts, l7days_sales_qty, l7days_sales_value, l7days_carts, ttl_sales_qty, ttl_sales_value, ttl_carts,\n", "            today_dump_quantity, today_dump_value, yesterday_dump_quantity, yesterday_dump_value, l7days_dump_quantity, l7days_dump_value, ttl_dump_quantity, ttl_dump_value,\n", "            be_today_dump_quantity, be_today_dump_value, be_yesterday_dump_quantity, be_yesterday_dump_value, be_l7days_dump_quantity, be_l7days_dump_value, be_ttl_dump_quantity, be_ttl_dump_value,\n", "            t_v1_indent, t_v2_indent, t_total_drop_quantity, t_new_inward_drop, t_new_storage_drop, t_new_truck_load_drop, t_new_picking_capacity_sku_drop, t_new_picking_capacity_quantity_drop, t_new_loose_quantity_drop,\n", "            t1_v1_indent, t1_v2_indent, t1_total_drop_quantity, t1_new_inward_drop, t1_new_storage_drop, t1_new_truck_load_drop, t1_new_picking_capacity_sku_drop, t1_new_picking_capacity_quantity_drop, t1_new_loose_quantity_drop,\n", "            t7_v1_indent, t7_v2_indent, t7_total_drop_quantity, t7_new_inward_drop, t7_new_storage_drop, t7_new_truck_load_drop, t7_new_picking_capacity_sku_drop, t7_new_picking_capacity_quantity_drop, t7_new_loose_quantity_drop\n", "\n", "                from combine_fe_details cfd\n", "\n", "                    left join\n", "                        combine_be_details cbd on cbd.city_name = cfd.city_name and cbd.facility_id = cfd.facility_id and cbd.be_facility_id = cfd.be_facility_id\n", "                        and cbd.event_flag = cfd.event_flag and cbd.assortment_type = cfd.assortment_type\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end, \n", "                    event_flag, assortment_type, facility_id, be_facility_id\n", "\n", "\n", "    \"\"\"\n", "    return read_sql_query(store_summary, redshift)\n", "\n", "\n", "store_summary = store_summary()"]}, {"cell_type": "raw", "id": "cb3a2710-71f2-491d-9200-0b1cac931f64", "metadata": {}, "source": ["store_summary.head(1)"]}, {"cell_type": "raw", "id": "016aadf2-c1ea-4ccf-aeac-0d6319bb42f9", "metadata": {}, "source": ["pb.to_sheets(\n", "    store_summary, \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\", \"store_wise_raw\"\n", ")"]}, {"cell_type": "raw", "id": "440904bf-9093-4503-9dd5-f0ca7a493c11", "metadata": {}, "source": ["store_summary = pd.DataFrame()"]}, {"cell_type": "raw", "id": "d22fabe1-23e7-473b-b6f5-def7873a5f94", "metadata": {}, "source": ["# backend p type summary"]}, {"cell_type": "raw", "id": "75487d6c-b1dd-4ce0-826d-dbe3017aa59e", "metadata": {}, "source": ["def be_p_type_summary():\n", "    be_p_type_summary = \"\"\"\n", "    \n", " \n", "    with\n", "    raw_data as\n", "        (select * from metrics.new_event_metrics_reporting),\n", "\n", "\n", "    be_all_details as\n", "        (select distinct be_facility_id, item_id, event_flag, assortment_type, p_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_all_view as\n", "        (select -666 as be_facility_id, event_flag, assortment_type, p_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_all_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    be_city_details as\n", "        (select distinct city_name, be_facility_id, item_id, event_flag, assortment_type, p_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_city_view as\n", "        (select be_facility_id, event_flag, assortment_type, p_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_city_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    fe_all_view as\n", "        (select event_flag, assortment_type, p_type,\n", "             -666 as be_facility_id, 'All' as be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    fe_city_view as\n", "        (select event_flag, assortment_type, p_type,\n", "            be_facility_id, be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    combine_fe_details as\n", "        (\n", "            select * from fe_all_view\n", "                union\n", "            select * from fe_city_view\n", "        ),\n", "\n", "    combine_be_details as\n", "        (\n", "            select * from be_all_view\n", "                union\n", "            select * from be_city_view\n", "        ),\n", "\n", "    final as\n", "        (select cfd.event_flag, cfd.assortment_type,\n", "            cfd.be_facility_id, be_facility_name,  cfd.p_type,\n", "            after_be_availability, availability, after_pp_availability, after_intranist_availability,\n", "            skus_count, active_skus_count, tea_tagged_count, non_tea_count, min_quantity, max_quantity,\n", "            ds_inv, ds_pending_putaway_inv, ds_open_sto_qty, ds_ttl_po_qty, ds_ttl_po_grn_quantity, ds_ttl_open_po_quantity,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            today_sales_qty, today_sales_value, today_carts, yesterday_sales_qty, yesterday_sales_value, yesterday_carts, l7days_sales_qty, l7days_sales_value, l7days_carts, ttl_sales_qty, ttl_sales_value, ttl_carts,\n", "            today_dump_quantity, today_dump_value, yesterday_dump_quantity, yesterday_dump_value, l7days_dump_quantity, l7days_dump_value, ttl_dump_quantity, ttl_dump_value,\n", "            be_today_dump_quantity, be_today_dump_value, be_yesterday_dump_quantity, be_yesterday_dump_value, be_l7days_dump_quantity, be_l7days_dump_value, be_ttl_dump_quantity, be_ttl_dump_value,\n", "            t_v1_indent, t_v2_indent, t_total_drop_quantity, t_new_inward_drop, t_new_storage_drop, t_new_truck_load_drop, t_new_picking_capacity_sku_drop, t_new_picking_capacity_quantity_drop, t_new_loose_quantity_drop,\n", "            t1_v1_indent, t1_v2_indent, t1_total_drop_quantity, t1_new_inward_drop, t1_new_storage_drop, t1_new_truck_load_drop, t1_new_picking_capacity_sku_drop, t1_new_picking_capacity_quantity_drop, t1_new_loose_quantity_drop,\n", "            t7_v1_indent, t7_v2_indent, t7_total_drop_quantity, t7_new_inward_drop, t7_new_storage_drop, t7_new_truck_load_drop, t7_new_picking_capacity_sku_drop, t7_new_picking_capacity_quantity_drop, t7_new_loose_quantity_drop\n", "\n", "                from combine_fe_details cfd\n", "\n", "                    left join\n", "                        combine_be_details cbd on cbd.be_facility_id = cfd.be_facility_id and cbd.event_flag = cfd.event_flag\n", "                        and cbd.assortment_type = cfd.assortment_type and cbd.p_type = cfd.p_type\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when be_facility_name = 'All' then 'A' end,\n", "                        event_flag, assortment_type, p_type\n", "\n", "    \"\"\"\n", "    return read_sql_query(be_p_type_summary, redshift)\n", "\n", "\n", "be_p_type_summary = be_p_type_summary()"]}, {"cell_type": "raw", "id": "6db7d9ab-0c76-47e6-aa35-93d3e7470fc7", "metadata": {}, "source": ["be_p_type_summary.head(1)"]}, {"cell_type": "raw", "id": "d8dae574-51af-4427-b850-5956724f1cab", "metadata": {}, "source": ["pb.to_sheets(\n", "    be_p_type_summary, \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\", \"be_p_type_raw\"\n", ")"]}, {"cell_type": "raw", "id": "cc281b06-6138-4a40-8f42-b2b202d68b7c", "metadata": {}, "source": ["be_p_type_summary = pd.DataFrame()"]}, {"cell_type": "raw", "id": "267956b9-2441-493a-a349-5054389203f3", "metadata": {}, "source": ["# backend l2 summary"]}, {"cell_type": "raw", "id": "4d8b7fff-e7ad-49a4-899c-f1954a723256", "metadata": {}, "source": ["def be_l2_summary():\n", "    be_l2_summary = \"\"\"\n", "    \n", "\n", "    with\n", "    raw_data as\n", "        (select * from metrics.new_event_metrics_reporting),\n", "\n", "\n", "    be_all_details as\n", "        (select distinct be_facility_id, item_id, event_flag, assortment_type, l2,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_all_view as\n", "        (select -666 as be_facility_id, event_flag, assortment_type, l2,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_all_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    be_city_details as\n", "        (select distinct city_name, be_facility_id, item_id, event_flag, assortment_type, l2,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_city_view as\n", "        (select be_facility_id, event_flag, assortment_type, l2,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_city_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    fe_all_view as\n", "        (select event_flag, assortment_type, l2,\n", "            -666 as be_facility_id, 'All' as be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    fe_city_view as\n", "        (select event_flag, assortment_type, l2,\n", "            be_facility_id, be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    combine_fe_details as\n", "        (\n", "            select * from fe_all_view\n", "                union\n", "            select * from fe_city_view\n", "        ),\n", "\n", "    combine_be_details as\n", "        (\n", "            select * from be_all_view\n", "                union\n", "            select * from be_city_view\n", "        ),\n", "\n", "    final as\n", "        (select cfd.event_flag, cfd.assortment_type,\n", "            cfd.be_facility_id, be_facility_name, cfd.l2,\n", "            after_be_availability, availability, after_pp_availability, after_intranist_availability,\n", "            skus_count, active_skus_count, tea_tagged_count, non_tea_count, min_quantity, max_quantity,\n", "            ds_inv, ds_pending_putaway_inv, ds_open_sto_qty, ds_ttl_po_qty, ds_ttl_po_grn_quantity, ds_ttl_open_po_quantity,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            today_sales_qty, today_sales_value, today_carts, yesterday_sales_qty, yesterday_sales_value, yesterday_carts, l7days_sales_qty, l7days_sales_value, l7days_carts, ttl_sales_qty, ttl_sales_value, ttl_carts,\n", "            today_dump_quantity, today_dump_value, yesterday_dump_quantity, yesterday_dump_value, l7days_dump_quantity, l7days_dump_value, ttl_dump_quantity, ttl_dump_value,\n", "            be_today_dump_quantity, be_today_dump_value, be_yesterday_dump_quantity, be_yesterday_dump_value, be_l7days_dump_quantity, be_l7days_dump_value, be_ttl_dump_quantity, be_ttl_dump_value,\n", "            t_v1_indent, t_v2_indent, t_total_drop_quantity, t_new_inward_drop, t_new_storage_drop, t_new_truck_load_drop, t_new_picking_capacity_sku_drop, t_new_picking_capacity_quantity_drop, t_new_loose_quantity_drop,\n", "            t1_v1_indent, t1_v2_indent, t1_total_drop_quantity, t1_new_inward_drop, t1_new_storage_drop, t1_new_truck_load_drop, t1_new_picking_capacity_sku_drop, t1_new_picking_capacity_quantity_drop, t1_new_loose_quantity_drop,\n", "            t7_v1_indent, t7_v2_indent, t7_total_drop_quantity, t7_new_inward_drop, t7_new_storage_drop, t7_new_truck_load_drop, t7_new_picking_capacity_sku_drop, t7_new_picking_capacity_quantity_drop, t7_new_loose_quantity_drop\n", "\n", "                from combine_fe_details cfd\n", "\n", "                    left join\n", "                        combine_be_details cbd on cbd.be_facility_id = cfd.be_facility_id and cbd.event_flag = cfd.event_flag\n", "                        and cbd.assortment_type = cfd.assortment_type and cbd.l2 = cfd.l2\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when be_facility_name = 'All' then 'A' end,\n", "                        event_flag, assortment_type, l2\n", "\n", "    \"\"\"\n", "    return read_sql_query(be_l2_summary, redshift)\n", "\n", "\n", "be_l2_summary = be_l2_summary()"]}, {"cell_type": "raw", "id": "33b91cc5-24eb-482b-a306-084b7149870b", "metadata": {}, "source": ["be_l2_summary.head(1)"]}, {"cell_type": "raw", "id": "00c981f5-fdd3-48f4-90d8-a1e6d990219c", "metadata": {}, "source": ["pb.to_sheets(be_l2_summary, \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\", \"be_l2_raw\")"]}, {"cell_type": "raw", "id": "3d6d3a28-c787-4054-a136-e697a1f82660", "metadata": {}, "source": ["be_l2_summary = pd.DataFrame()"]}, {"cell_type": "raw", "id": "d438f2a8-b635-49e5-b63a-e6db60aab9d7", "metadata": {}, "source": ["# backend store wise summary"]}, {"cell_type": "raw", "id": "f1310d2a-a4f9-478b-b5e3-26d184e05c26", "metadata": {}, "source": ["def be_store_summary():\n", "    be_store_summary = \"\"\"\n", "    \n", "    with\n", "    raw_data as\n", "        (select * from metrics.new_event_metrics_reporting),\n", "\n", "\n", "    be_all_details as\n", "        (select distinct facility_id, be_facility_id, item_id, event_flag, assortment_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_all_view as\n", "        (select facility_id, be_facility_id, event_flag, assortment_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_all_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    fe_all_view as\n", "        (select city_name, event_flag, assortment_type,\n", "            facility_id, be_facility_id, facility_name, be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    final as\n", "        (select fav.city_name, fav.event_flag, fav.assortment_type,\n", "            fav.facility_id, fav.be_facility_id, facility_name, be_facility_name,\n", "            after_be_availability, availability, after_pp_availability, after_intranist_availability,\n", "            skus_count, active_skus_count, tea_tagged_count, non_tea_count, min_quantity, max_quantity,\n", "            ds_inv, ds_pending_putaway_inv, ds_open_sto_qty, ds_ttl_po_qty, ds_ttl_po_grn_quantity, ds_ttl_open_po_quantity,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            today_sales_qty, today_sales_value, today_carts, yesterday_sales_qty, yesterday_sales_value, yesterday_carts, l7days_sales_qty, l7days_sales_value, l7days_carts, ttl_sales_qty, ttl_sales_value, ttl_carts,\n", "            today_dump_quantity, today_dump_value, yesterday_dump_quantity, yesterday_dump_value, l7days_dump_quantity, l7days_dump_value, ttl_dump_quantity, ttl_dump_value,\n", "            be_today_dump_quantity, be_today_dump_value, be_yesterday_dump_quantity, be_yesterday_dump_value, be_l7days_dump_quantity, be_l7days_dump_value, be_ttl_dump_quantity, be_ttl_dump_value,\n", "            t_v1_indent, t_v2_indent, t_total_drop_quantity, t_new_inward_drop, t_new_storage_drop, t_new_truck_load_drop, t_new_picking_capacity_sku_drop, t_new_picking_capacity_quantity_drop, t_new_loose_quantity_drop,\n", "            t1_v1_indent, t1_v2_indent, t1_total_drop_quantity, t1_new_inward_drop, t1_new_storage_drop, t1_new_truck_load_drop, t1_new_picking_capacity_sku_drop, t1_new_picking_capacity_quantity_drop, t1_new_loose_quantity_drop,\n", "            t7_v1_indent, t7_v2_indent, t7_total_drop_quantity, t7_new_inward_drop, t7_new_storage_drop, t7_new_truck_load_drop, t7_new_picking_capacity_sku_drop, t7_new_picking_capacity_quantity_drop, t7_new_loose_quantity_drop\n", "\n", "                from fe_all_view fav\n", "\n", "                    left join\n", "                        be_all_view bav on bav.facility_id = fav.facility_id and bav.be_facility_id = fav.be_facility_id\n", "                        and bav.event_flag = fav.event_flag and bav.assortment_type = fav.assortment_type \n", "        )\n", "\n", "            select * from final\n", "                order by \n", "                    case when be_facility_name = 'All' then 'A' else be_facility_name end, \n", "                    event_flag, assortment_type, facility_id\n", "\n", "    \"\"\"\n", "    return read_sql_query(be_store_summary, redshift)\n", "\n", "\n", "be_store_summary = be_store_summary()"]}, {"cell_type": "raw", "id": "9583e8ef-2ed0-46f9-8741-0183d062c530", "metadata": {}, "source": ["be_store_summary.head(1)"]}, {"cell_type": "raw", "id": "7688fde9-ee08-40d8-9ccf-4c0b002c3e6c", "metadata": {}, "source": ["pb.to_sheets(\n", "    be_store_summary,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"be_store_wise_raw\",\n", ")"]}, {"cell_type": "raw", "id": "46343a0c-9f0a-4d1f-8c2d-c68fceeb5ffd", "metadata": {}, "source": ["be_store_summary = pd.DataFrame()"]}, {"cell_type": "raw", "id": "b112aa9b-4f50-471d-b4f7-03c0b6ac4b5e", "metadata": {}, "source": ["# city backend summary"]}, {"cell_type": "raw", "id": "9bb63cef-b44f-42ab-9ee5-520f60be9914", "metadata": {}, "source": ["def city_be_summary():\n", "    city_be_summary = \"\"\"\n", "    \n", "    with\n", "    raw_data as\n", "        (select * from metrics.new_event_metrics_reporting),\n", "\n", "\n", "    be_all_details as\n", "        (select distinct city_name, be_facility_id, item_id, event_flag, assortment_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_all_view as\n", "        (select 'All' as city_name, be_facility_id, event_flag, assortment_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_all_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    be_city_details as\n", "        (select distinct city_name, be_facility_id, item_id, event_flag, assortment_type,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            be_today_dump_quantity, be_today_dump_value,\n", "            be_yesterday_dump_quantity, be_yesterday_dump_value,\n", "            be_l7days_dump_quantity, be_l7days_dump_value,\n", "            be_ttl_dump_quantity, be_ttl_dump_value\n", "\n", "                from raw_data\n", "        ),\n", "\n", "    be_city_view as\n", "        (select city_name, be_facility_id, event_flag, assortment_type,\n", "            sum(be_net_inventory) as be_net_inventory, sum(be_pending_putaway) as be_pending_putaway, sum(be_open_sto_qty) as be_open_sto_qty,\n", "            sum(be_ttl_po_quantity) as be_ttl_po_quantity, sum(be_ttl_grn_quantity) as be_ttl_grn_quantity, sum(be_ttl_open_po_quantity) as be_ttl_open_po_quantity,\n", "            sum(be_today_dump_quantity) as be_today_dump_quantity, sum(be_today_dump_value) as be_today_dump_value,\n", "            sum(be_yesterday_dump_quantity) as be_yesterday_dump_quantity, sum(be_yesterday_dump_value) as be_yesterday_dump_value,\n", "            sum(be_l7days_dump_quantity) as be_l7days_dump_quantity, sum(be_l7days_dump_value) as be_l7days_dump_value,\n", "            sum(be_ttl_dump_quantity) as be_ttl_dump_quantity, sum(be_ttl_dump_value) as be_ttl_dump_value\n", "\n", "                from be_city_details\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    fe_all_view as\n", "        (select 'All' as city_name, event_flag, assortment_type,\n", "            be_facility_id, be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    fe_city_view as\n", "        (select city_name, event_flag, assortment_type,\n", "            be_facility_id, be_facility_name,\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1\n", "                when be_net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_be_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1 else 0 end) * 1.00)/count(item_id)) as availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_pp_availability,\n", "\n", "            ((sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                when open_sto_qty > 0 then 1 else 0 end) * 1.00)/count(item_id)) as after_intranist_availability,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "\n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "\n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "\n", "                from raw_data\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    combine_fe_details as\n", "        (\n", "            select * from fe_all_view\n", "                union\n", "            select * from fe_city_view\n", "        ),\n", "\n", "    combine_be_details as\n", "        (\n", "            select * from be_all_view\n", "                union\n", "            select * from be_city_view\n", "        ),\n", "\n", "    final as\n", "        (select cfd.city_name, cfd.event_flag, cfd.assortment_type,\n", "            cfd.be_facility_id, be_facility_name,\n", "            after_be_availability, availability, after_pp_availability, after_intranist_availability,\n", "            skus_count, active_skus_count, tea_tagged_count, non_tea_count, min_quantity, max_quantity,\n", "            ds_inv, ds_pending_putaway_inv, ds_open_sto_qty, ds_ttl_po_qty, ds_ttl_po_grn_quantity, ds_ttl_open_po_quantity,\n", "            be_net_inventory, be_pending_putaway, be_open_sto_qty, be_ttl_po_quantity, be_ttl_grn_quantity, be_ttl_open_po_quantity,\n", "            today_sales_qty, today_sales_value, today_carts, yesterday_sales_qty, yesterday_sales_value, yesterday_carts, l7days_sales_qty, l7days_sales_value, l7days_carts, ttl_sales_qty, ttl_sales_value, ttl_carts,\n", "            today_dump_quantity, today_dump_value, yesterday_dump_quantity, yesterday_dump_value, l7days_dump_quantity, l7days_dump_value, ttl_dump_quantity, ttl_dump_value,\n", "            be_today_dump_quantity, be_today_dump_value, be_yesterday_dump_quantity, be_yesterday_dump_value, be_l7days_dump_quantity, be_l7days_dump_value, be_ttl_dump_quantity, be_ttl_dump_value,\n", "            t_v1_indent, t_v2_indent, t_total_drop_quantity, t_new_inward_drop, t_new_storage_drop, t_new_truck_load_drop, t_new_picking_capacity_sku_drop, t_new_picking_capacity_quantity_drop, t_new_loose_quantity_drop,\n", "            t1_v1_indent, t1_v2_indent, t1_total_drop_quantity, t1_new_inward_drop, t1_new_storage_drop, t1_new_truck_load_drop, t1_new_picking_capacity_sku_drop, t1_new_picking_capacity_quantity_drop, t1_new_loose_quantity_drop,\n", "            t7_v1_indent, t7_v2_indent, t7_total_drop_quantity, t7_new_inward_drop, t7_new_storage_drop, t7_new_truck_load_drop, t7_new_picking_capacity_sku_drop, t7_new_picking_capacity_quantity_drop, t7_new_loose_quantity_drop\n", "\n", "                from combine_fe_details cfd\n", "\n", "                    left join\n", "                        combine_be_details cbd on cbd.be_facility_id = cfd.be_facility_id and cbd.event_flag = cfd.event_flag\n", "                        and cbd.assortment_type = cfd.assortment_type and cbd.city_name = cfd.city_name\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end,\n", "                        event_flag, assortment_type, be_facility_name\n", "\n", "    \"\"\"\n", "    return read_sql_query(city_be_summary, redshift)\n", "\n", "\n", "city_be_summary = city_be_summary()"]}, {"cell_type": "raw", "id": "69e894df-1304-4fa3-9245-a478666dc51e", "metadata": {}, "source": ["city_be_summary.head(1)"]}, {"cell_type": "raw", "id": "c113c860-5a67-48b2-96ed-4c71b033ea36", "metadata": {}, "source": ["pb.to_sheets(\n", "    city_be_summary, \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\", \"be_city_raw\"\n", ")"]}, {"cell_type": "raw", "id": "252ddb69-aa0b-4e41-9714-4985d6fd7e1f", "metadata": {}, "source": ["city_be_summary = pd.DataFrame()"]}, {"cell_type": "raw", "id": "6f268b6a-9d99-4d13-a558-f9ac5db131d2", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "b5142ff9-1399-4f1c-adf2-e0c4b65720e2", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}