alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: new_event_po_sto_details
dag_type: report
escalation_priority: low
executor:
  config:
    cpu:
      limit: 2
      request: 1
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 8G
      request: 2G
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 2
owner:
  email: <EMAIL>
  slack_id: U03RR39TSTZ
path: povms/event_flows/report/new_event_po_sto_details
paused: true
project_name: event_flows
schedule:
  end_date: '2024-05-17T18:30:00'
  interval: 15 * * * *
  start_date: '2024-05-15T06:50:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
pool: povms_pool
