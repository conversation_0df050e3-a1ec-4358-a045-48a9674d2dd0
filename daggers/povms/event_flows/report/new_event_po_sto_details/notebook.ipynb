{"cells": [{"cell_type": "code", "execution_count": null, "id": "45798f5b-61f1-44f9-adff-ecc9832a7cad", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)"]}, {"cell_type": "code", "execution_count": null, "id": "26a0d47b-5fb9-468d-bae8-468ddfaf3e69", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "b5d0bf23-3398-493b-b401-c5da4781dbc3", "metadata": {}, "source": ["# PO/STO fill rate & in-Transit"]}, {"cell_type": "code", "execution_count": null, "id": "8422c0dd-829e-4bb9-9267-e39457b14a9a", "metadata": {}, "outputs": [], "source": ["def po_sto_details():\n", "    po_sto_details = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details),\n", "\n", "    item_details as\n", "        (select * from supply_etls.item_details),\n", "\n", "    variant_details as\n", "        (select * from rpc.product_product\n", "            where\n", "                lake_active_record\n", "        ),\n", "\n", "    event_details as\n", "        (select * from supply_etls.new_event_metrics_reporting),\n", "\n", "    sto_details as\n", "        (select * from ims.ims_sto_details),\n", "\n", "    final_sto_details as\n", "        (select * from sto_details\n", "            where created_at >= current_date - interval '20' day\n", "            and lake_active_record\n", "        ),\n", "\n", "    sto_item_details as\n", "        (select * from po.sto_items),\n", "\n", "    final_sto_item_details as\n", "        (select * from sto_item_details\n", "            where created_at >= current_date - interval '20' day\n", "            and lake_active_record\n", "        ),\n", "\n", "    invoice_details as\n", "        (select * from pos.pos_invoice),\n", "\n", "    final_invoice_details as\n", "        (select * from invoice_details\n", "            where insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "            and lake_active_record\n", "            and invoice_type_id in (5,14,16)\n", "            and grofers_order_id is not null\n", "            and grofers_order_id != ''\n", "            \n", "        ),\n", "\n", "    invoice_item_details as\n", "        (select * from pos.pos_invoice_product_details),\n", "\n", "    final_invoice_item_details as\n", "        (select * from invoice_item_details\n", "            where insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "            and lake_active_record\n", "        ),\n", "\n", "    adding_invoice_details as\n", "        (select \n", "            cast(fid.grofers_order_id as int) as sto_id,\n", "            vd.item_id,\n", "            sum(fiid.quantity) as billed_quantity\n", "\n", "                from final_invoice_details fid\n", "\n", "                    join\n", "                        final_invoice_item_details fiid on fiid.invoice_id = fid.id\n", "\n", "                    join\n", "                        variant_details vd on vd.variant_id = fiid.variant_id\n", "\n", "                        group by 1,2\n", "        ),\n", "\n", "    grn_details as\n", "        (select * from ims.ims_inward_invoice),\n", "\n", "    final_grn_detials as\n", "        (select * from grn_details\n", "            where\n", "                insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "                and lake_active_record\n", "        ),\n", "\n", "    grn_item_details as\n", "        (select * from ims.ims_inventory_stock_details),\n", "\n", "    final_grn_item_details as\n", "        (select * from grn_item_details\n", "            where\n", "                insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "                and lake_active_record\n", "        ),\n", "\n", "    adding_grn_details as\n", "        (select\n", "            cast(fid.grofers_order_id as int) as sto_id,\n", "            vd.item_id,\n", "            sum(fgid.\"delta\") as grn_quantity\n", "\n", "                from final_grn_item_details fgid\n", "\n", "                    join\n", "                        final_grn_detials fgd on fgd.grn_id = fgid.grn_id\n", "\n", "                    join\n", "                        variant_details vd on vd.variant_id = fgid.variant_id\n", "\n", "                    join\n", "                        final_invoice_details fid on fid.invoice_id = fgd.vendor_invoice_id\n", "\n", "                        group by 1,2\n", "        ),\n", "\n", "    discrepancy_details as\n", "        (select * from pos.discrepancy_note),\n", "\n", "    final_discrepancy_details as\n", "        (select * from discrepancy_details\n", "            where \n", "                (created_at >= current_date - interval '20' day)\n", "                and lake_active_record\n", "        ),\n", "\n", "    discrepancy_item_details as\n", "        (select * from pos.discrepancy_note_product_detail),\n", "\n", "    final_discrepancy_item_details as\n", "        (select * from discrepancy_item_details\n", "            where \n", "                lake_active_record\n", "        ),\n", "\n", "    sto_discrepancy_item_details as\n", "        (select\n", "            cast(fid.grofers_order_id as int) as sto_id,\n", "            item_id,\n", "            sum(quantity) as discrepancy_quantity\n", "\n", "                from final_discrepancy_details fdd\n", "\n", "                    join\n", "                        final_discrepancy_item_details fdid on fdid.dn_id_id = fdd.id\n", "\n", "                    join\n", "                        (select upc, item_id from variant_details group by 1,2) vd on vd.upc = fdid.upc_id\n", "\n", "                    join\n", "                        final_invoice_details fid on fid.invoice_id = fdd.vendor_invoice_id\n", "\n", "                            group by 1,2\n", "        ),\n", "\n", "    adding_all as\n", "        (select\n", "            date(fsd.created_at + interval '330' minute) as date,\n", "            case when od.hot_outlet_id is null then fsd.outlet_id else od.hot_outlet_id end as outlet_id,\n", "            od.facility_id as facility_id,\n", "            od.facility_name as facility_name,\n", "\n", "            od2.city_name,\n", "            fsd.merchant_outlet_id as frontend_outlet_id,\n", "            od2.facility_id as frontend_facility_id,\n", "            od2.facility_name as frontend_facility_name,\n", "\n", "            fsd.sto_id,\n", "            fsid.item_id,\n", "\n", "            ed.p_type as ptype,\n", "            ed.event_flag as reason_text,\n", "\n", "            fsid.reserved_quantity as created_qty,\n", "            coalesce(aid.billed_quantity,0) as billed_qty,\n", "            coalesce(agd.grn_quantity,0) as inward_qty,\n", "            coalesce(sdid.discrepancy_quantity,0) as discrepancy_qty,\n", "\n", "            case \n", "                when ((fsid.reserved_quantity) <= (coalesce(agd.grn_quantity,0) + coalesce(sdid.discrepancy_quantity,0))) then 'expired'\n", "                when fsd.sto_state in (3,4) then 'expired'\n", "            else 'open' end as status\n", "\n", "                from final_sto_details fsd\n", "\n", "                    join\n", "                        final_sto_item_details fsid on fsid.sto_id = fsd.sto_id\n", "\n", "                    left join\n", "                        adding_invoice_details aid on aid.sto_id = fsd.sto_id and aid.item_id = fsid.item_id\n", "\n", "                    left join\n", "                        adding_grn_details agd on agd.sto_id = fsd.sto_id and agd.item_id = fsid.item_id\n", "\n", "                    join\n", "                        outlet_details od on od.inv_outlet_id = fsd.outlet_id and od.taggings <> 'fe'\n", "\n", "                    join\n", "                        outlet_details od2 on od2.inv_outlet_id = fsd.merchant_outlet_id and od2.taggings = 'fe'\n", "\n", "                    join\n", "                        event_details ed on ed.item_id = fsid.item_id and ed.facility_id = od2.facility_id\n", "\n", "                    left join\n", "                        sto_discrepancy_item_details sdid on sdid.sto_id = fsd.sto_id and sdid.item_id = fsid.item_id\n", "        ),\n", "\n", "    partner_outlet_mapping as\n", "        (select * from po.edi_integration_partner_outlet_mapping),\n", "\n", "    partner_po_details as\n", "        (select * from po.edi_integration_partner_purchase_order_details),\n", "\n", "    final_partner_po_details as\n", "        (select * from partner_po_details\n", "            where \n", "                created_at >= current_date - interval '20' day\n", "                and lake_active_record\n", "                and active\n", "        ),\n", "\n", "    partner_buyer_details as\n", "        (select * from zomato.hp_consumer.buyer_order_requests),\n", "\n", "    final_partner_buyer_details as\n", "        (select * from partner_buyer_details\n", "            where dt >= replace(cast(date(current_date) - interval '20' day as varchar), '-','')\n", "        ),\n", "\n", "    partner_mapping as\n", "        (select item_id, cast(partner_item_id as int) as partner_item_id from po.edi_integration_partner_item_mapping\n", "            where lake_active_record\n", "            and active\n", "        ),\n", "\n", "    billing_details as\n", "        (select * from zomato.hp_wms.order_item\n", "            where \n", "                dt >= replace(cast(date(current_date) - interval '20' day as varchar), '-','')\n", "        ),\n", "\n", "    final_billing_details as\n", "        (select\n", "            bd.order_id,\n", "            pm.item_id,\n", "            sum(bd.picked_quantity) as billing_quantity\n", "\n", "                from billing_details bd\n", "\n", "                    join\n", "                        partner_mapping pm on pm.partner_item_id = bd.product_number\n", "\n", "                        group by 1,2\n", "\n", "        ),\n", "\n", "    po_details as\n", "        (select * from po.purchase_order),\n", "\n", "    final_po_details as\n", "        (select * from po_details\n", "            where \n", "                created_at >= current_date - interval '20' day\n", "                and lake_active_record\n", "                and vendor_id = 13280\n", "                and po_type_id != 11\n", "        ),\n", "\n", "    po_item_details as\n", "        (select * from po.purchase_order_items),\n", "\n", "    final_po_item_details as\n", "        (select * from po_item_details\n", "            where \n", "                created_at >= current_date - interval '20' day\n", "                and lake_active_record\n", "        ),\n", "\n", "    po_grn as\n", "        (select * from po.po_grn),\n", "\n", "    final_po_grn as\n", "        (select \n", "            po_id,\n", "            item_id,\n", "            sum(quantity) as quantity\n", "\n", "                from po_grn\n", "\n", "                    where \n", "                        insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "                        and lake_active_record\n", "\n", "                        group by 1,2\n", "        ),\n", "\n", "    po_status as\n", "        (select * from po.purchase_order_status),\n", "\n", "    final_po_status as\n", "        (select * from po_status\n", "            where \n", "                created_at >= (current_date - interval '20' day)\n", "                and lake_active_record\n", "        ),\n", "\n", "    po_discrepancy_item_details as\n", "        (select\n", "            po_number,\n", "            item_id,\n", "            sum(quantity) as discrepancy_quantity\n", "\n", "                from final_discrepancy_details fdd\n", "\n", "                    join\n", "                        final_discrepancy_item_details fdid on fdid.dn_id_id = fdd.id\n", "\n", "                    join\n", "                        (select upc, item_id from variant_details group by 1,2) vd on vd.upc = fdid.upc_id\n", "\n", "                        where \n", "                            po_number is not null\n", "                            and po_number != ''\n", "\n", "                            group by 1,2\n", "        ),\n", "\n", "    hp_transfer_details as\n", "        (select\n", "            date(fpd.created_at + interval '330' minute) as date,\n", "            pom.outlet_id,\n", "            od.facility_id,\n", "            od.facility_name,\n", "\n", "\n", "            od2.city_name,\n", "            fpd.outlet_id as frontend_outlet_id,\n", "            od2.facility_id as frontend_facility_id,\n", "            od2.facility_name as frontend_facility_name,\n", "            id.p_type as ptype,\n", "            ed.event_flag as reason_text,\n", "\n", "            fpbd.orderid as hp_order_id,\n", "            fpd.po_number,\n", "            fpid.item_id,\n", "\n", "            fpid.units_ordered as created_qty,\n", "            coalesce(fbd.billing_quantity,0) as billed_qty,\n", "            coalesce(fpg.quantity,0) as inward_qty,\n", "            coalesce(pdid.discrepancy_quantity,0) as discrepancy_qty,\n", "\n", "            case\n", "                when (fpid.units_ordered <= (coalesce(fpg.quantity,0) + coalesce(pdid.discrepancy_quantity,0))) then 'expired'\n", "                when (fps.po_state_id in (2,3,13,14,15) and (fps.po_state_id in (2,3,13,14,15) or fpd.is_multiple_grn = 1)) then 'open'\n", "                when (fps.po_state_id in (8,9)) then 'expired'\n", "                when (fps.po_state_id in (4,5,10)) then 'cancelled_po'\n", "                else 'pending' end as status\n", "\n", "                from final_po_item_details fpid\n", "\n", "                    join\n", "                        final_po_details fpd on fpd.id = fpid.po_id -- and fpd.po_number = '1572310003091'\n", "\n", "                    join\n", "                        final_partner_po_details fppd on fppd.po_id = fpd.id\n", "\n", "                    join\n", "                        final_partner_buyer_details fpbd on fpbd.id = fppd.partner_order_id -- and fpbd.orderid = 7594089\n", "\n", "                    join\n", "                        partner_outlet_mapping pom on lower(pom.partner_outlet_id) = lower(fpbd.warehousecode)\n", "\n", "                    left join\n", "                        final_po_grn fpg on fpg.po_id = fpid.po_id and fpg.item_id = fpid.item_id\n", "\n", "                    join\n", "                        item_details id on id.item_id = fpid.item_id\n", "\n", "                    left join\n", "                        final_billing_details fbd on fbd.order_id = fpbd.orderid and fbd.item_id = fpid.item_id\n", "\n", "                    join\n", "                        final_po_status fps on fps.po_id = fpd.id\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = pom.outlet_id\n", "\n", "                    join\n", "                        outlet_details od2 on od2.hot_outlet_id = fpd.outlet_id\n", "\n", "                    join\n", "                        event_details ed on ed.item_id = fpid.item_id and ed.facility_id = od2.facility_id\n", "\n", "                    left join\n", "                        po_discrepancy_item_details pdid on pdid.po_number = fpd.po_number and pdid.item_id = fpid.item_id\n", "        ),\n", "\n", "    final_view as\n", "        (    \n", "            select\n", "                date,\n", "                outlet_id,\n", "                facility_id,\n", "                facility_name,\n", "                city_name,\n", "                frontend_outlet_id,\n", "                frontend_facility_id,\n", "                frontend_facility_name,\n", "                ptype,\n", "                reason_text,\n", "                coalesce(sum(case when status = 'expired' then created_qty end),0) as created_qty,\n", "                coalesce(sum(case when status = 'expired' then billed_qty end),0) as billed_qty,\n", "                coalesce(sum(case when status = 'expired' then inward_qty end),0) as inward_qty,\n", "                coalesce(sum(case when status = 'expired' then discrepancy_qty end),0) as discrepancy_qty,\n", "                coalesce(sum(case when status = 'open' then (created_qty - (inward_qty + discrepancy_qty)) else 0 end),0) as open_quantity\n", "\n", "                    from adding_all\n", "\n", "                        where \n", "                            date >= (current_date - interval '10' day)\n", "                            and created_qty > 0\n", "\n", "                            group by 1,2,3,4,5,6,7,8,9,10\n", "\n", "                    union\n", "\n", "            select\n", "                date,\n", "                outlet_id,\n", "                facility_id,\n", "                facility_name,\n", "                city_name,\n", "                frontend_outlet_id,\n", "                frontend_facility_id,\n", "                frontend_facility_name,\n", "                ptype,\n", "                reason_text,\n", "                coalesce(sum(case when status = 'expired' then created_qty end),0) as created_qty,\n", "                coalesce(sum(case when status = 'expired' then billed_qty end),0) as billed_qty,\n", "                coalesce(sum(case when status = 'expired' then inward_qty end),0) as inward_qty,\n", "                coalesce(sum(case when status = 'expired' then discrepancy_qty end),0) as discrepancy_qty,\n", "                coalesce(sum(case \n", "                    when status = 'open' and billed_qty > 0 then (billed_qty - (inward_qty + discrepancy_qty))\n", "                    when status = 'open' and billed_qty = 0 then (created_qty - (inward_qty + discrepancy_qty))\n", "                    else 0 end),0) as open_quantity\n", "\n", "                    from hp_transfer_details\n", "\n", "                        where \n", "                            status <> 'cancelled_po'\n", "                            and date >= (current_date - interval '10' day)\n", "                            and created_qty > 0\n", "\n", "                            group by 1,2,3,4,5,6,7,8,9,10\n", "        )\n", "\n", "            select * from final_view\n", "\n", "    \n", "    \n", "    \"\"\"\n", "    return read_sql_query(po_sto_details, trino)\n", "\n", "\n", "po_sto_details = po_sto_details()\n", "\n", "print(po_sto_details.shape)\n", "po_sto_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "8e2a3c4e-595d-4702-874c-2cc0ed923049", "metadata": {}, "outputs": [], "source": ["po_sto_details[\"insert_ds_ist\"] = pd.to_datetime(\n", "    datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")\n", "po_sto_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "91d6d40b-8987-46ea-a028-5c362cc5a2df", "metadata": {}, "outputs": [], "source": ["po_sto_details = po_sto_details[\n", "    [\n", "        \"insert_ds_ist\",\n", "        \"date\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"city_name\",\n", "        \"frontend_outlet_id\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility_name\",\n", "        \"ptype\",\n", "        \"reason_text\",\n", "        \"created_qty\",\n", "        \"billed_qty\",\n", "        \"inward_qty\",\n", "        \"discrepancy_qty\",\n", "        \"open_quantity\",\n", "    ]\n", "]\n", "\n", "po_sto_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "0b43f6d9-126e-4167-a9ea-d3683bdd294c", "metadata": {}, "outputs": [], "source": ["po_sto_details[\n", "    (po_sto_details[\"frontend_facility_id\"] == 1610)\n", "    & (po_sto_details[\"ptype\"] == \"Mango Drink\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "93ca9d3c-70ac-4ac8-b568-27644f545a2f", "metadata": {}, "outputs": [], "source": ["po_sto_details[\n", "    [\"created_qty\", \"billed_qty\", \"inward_qty\", \"discrepancy_qty\", \"open_quantity\"]\n", "] = (\n", "    po_sto_details[\n", "        [\"created_qty\", \"billed_qty\", \"inward_qty\", \"discrepancy_qty\", \"open_quantity\"]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d5d11a20-c79d-4bb1-8965-66a30fac38e1", "metadata": {}, "outputs": [], "source": ["po_sto_details[\"insert_ds_ist\"] = pd.to_datetime(\n", "    datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")\n", "\n", "po_sto_details[\"date\"] = pd.to_datetime(po_sto_details[\"date\"])\n", "\n", "po_sto_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "50fefc9b-d5ca-4876-8dd2-e5e1f094d282", "metadata": {}, "outputs": [], "source": ["po_sto_details = po_sto_details[\n", "    [\n", "        \"insert_ds_ist\",\n", "        \"date\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"city_name\",\n", "        \"frontend_outlet_id\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility_name\",\n", "        \"ptype\",\n", "        \"reason_text\",\n", "        \"created_qty\",\n", "        \"billed_qty\",\n", "        \"inward_qty\",\n", "        \"discrepancy_qty\",\n", "        \"open_quantity\",\n", "    ]\n", "]\n", "\n", "po_sto_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a0d2a8c3-d8f8-46ee-9341-0fdfa69e6cab", "metadata": {}, "outputs": [], "source": ["po_sto_details.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "33f771be-2f31-4434-977b-2ec39c8a4f73", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"timestamp(6)\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"date\", \"type\": \"date\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"frontend_outlet_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\n", "        \"name\": \"frontend_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"ptype\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"reason_text\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"created_qty\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"billed_qty\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"inward_qty\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"discrepancy_qty\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"open_quantity\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9d86dba0-53ea-42d7-9b25-3e04e398b7e1", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"new_event_metrics_reporting_fill_rate_and_in_transit_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"frontend_facility_id\"],\n", "    # \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"in-transit sto details\",\n", "}\n", "\n", "pb.to_trino(po_sto_details, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "df14f879-ad0b-41e1-a09b-150e099a8d33", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}