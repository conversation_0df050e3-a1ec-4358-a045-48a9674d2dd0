{"cells": [{"cell_type": "code", "execution_count": null, "id": "096187ef-79dc-494c-bb7f-b28981c6a259", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from calendar import monthrange\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "\n", "\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "markdown", "id": "38d72cac-b4a7-4293-b4e2-4e9bedca1e98", "metadata": {}, "source": ["# outlet details"]}, {"cell_type": "code", "execution_count": null, "id": "e3f2013b-537f-47d9-bc52-350dd6f55949", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = \"\"\"\n", "    \n", "    select z.zone,\n", "        rcl.name as city_name,\n", "        om.outlet_id as hot_outlet_id, \n", "        om.facility_id, cf.facility_name,\n", "        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "        rco.business_type_id,\n", "        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "        \n", "            from lake_po.physical_facility_outlet_mapping om\n", "            \n", "            left join (select id, tax_location_id,\n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                    from lake_retail.console_outlet\n", "                        ) rco on rco.id = om.outlet_id\n", "            \n", "            left join (select id, name as facility_name \n", "                from lake_crates.facility\n", "                        ) cf on cf.id = om.facility_id\n", "            \n", "            left join (select distinct warehouse_id, cloud_store_id from lake_retail.warehouse_outlet_mapping \n", "                where active = 1\n", "                        ) wom on wom.warehouse_id = om.outlet_id\n", "            \n", "            left join lake_retail.console_location rcl on rcl.id = rco.tax_location_id\n", "            \n", "            left join (select distinct facility_id, zone from metrics.outlet_zone_mapping where business_type_id in (1,12,7,19,20,21)\n", "                        ) z on z.facility_id = om.facility_id\n", "            \n", "                where rco.business_type_id in (1,12,7,19,20,21) and om.outlet_id not in (0,1739)\n", "                and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                and om.outlet_name not like '%%SSC%%'\n", "                and om.outlet_name not like '%%MODI%%'\n", "                and om.outlet_name not like '%%hot ff%%'\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(outlets, redshift)\n", "\n", "\n", "outlets = outlets()"]}, {"cell_type": "code", "execution_count": null, "id": "578e6f47-3be9-4bf4-9fec-ca83374a399b", "metadata": {}, "outputs": [], "source": ["outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0ebbe1d7-3f8f-4564-9cfb-99f7c364b6c9", "metadata": {}, "outputs": [], "source": ["# all inv outlets\n", "all_inv_outlet_id = list(outlets[\"inv_outlet_id\"].unique())\n", "all_inv_outlet_id = tuple(all_inv_outlet_id)\n", "# len(all_inv_outlet_id)\n", "\n", "\n", "# all hot outlets\n", "all_hot_outlet_id = list(outlets[\"hot_outlet_id\"].unique())\n", "all_hot_outlet_id = tuple(all_hot_outlet_id)\n", "# len(all_hot_outlet_id)\n", "\n", "# all hot outlets_name\n", "all_hot_name = outlets[[\"hot_outlet_id\", \"inv_outlet_id\", \"facility_id\", \"facility_name\"]].rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"inv_outlet_id\": \"be_inv_outlet_id\",\n", "        \"facility_id\": \"be_facility_id\",\n", "        \"facility_name\": \"be_facility_name\",\n", "    }\n", ")\n", "all_hot_name.head()\n", "\n", "# all facility\n", "all_facility_id = list(outlets[\"facility_id\"].unique())\n", "all_facility_id = tuple(all_facility_id)\n", "# len(all_facility_id)\n", "\n", "# frontend outlets\n", "frontend_outlet_details = outlets[outlets[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", "fe_facility_details = list(frontend_outlet_details[\"hot_outlet_id\"].unique())\n", "fe_facility_details = tuple(fe_facility_details)\n", "# len(fe_facility_details)\n", "\n", "\n", "# backend inv outlets\n", "be_inv_outlet_id = outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", "be_inv_outlet_id = list(be_inv_outlet_id[\"inv_outlet_id\"].unique())\n", "be_inv_outlet_id = tuple(be_inv_outlet_id)\n", "# len(be_inv_outlet_id)\n", "\n", "\n", "# backend hot outlets\n", "be_hot_outlet_id = outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", "be_hot_outlet_id = list(be_hot_outlet_id[\"hot_outlet_id\"].unique())\n", "be_hot_outlet_id = tuple(be_hot_outlet_id)\n", "# len(be_hot_outlet_id)"]}, {"cell_type": "markdown", "id": "6d453a30-daae-47b4-9eb2-a2ddb9dde6c8", "metadata": {}, "source": ["# assortment details"]}, {"cell_type": "code", "execution_count": null, "id": "bd37cc7c-605a-4bfc-8169-ed2ea430eef5", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "\n", "assortment = pb.from_sheets(\n", "    \"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"Party_Input\", clear_cache=True\n", ")\n", "assortment = assortment[\n", "    [\"city\", \"item_id\", \"go_live_date\", \"event_flag\", \"assortment_type\", \"active\"]\n", "]\n", "\n", "assortment = assortment.rename(columns={\"city\": \"city_name\"})\n", "\n", "assortment = assortment[\n", "    ~(\n", "        (assortment[\"city_name\"] == \"\")\n", "        | (assortment[\"item_id\"] == \"\")\n", "        | (assortment[\"go_live_date\"] == \"\")\n", "        | (assortment[\"event_flag\"] == \"\")\n", "        | (assortment[\"assortment_type\"] == \"\")\n", "        | (assortment[\"active\"] == \"\")\n", "    )\n", "]\n", "\n", "assortment[[\"item_id\", \"active\"]] = assortment[[\"item_id\", \"active\"]].astype(int)\n", "\n", "assortment[\"go_live_date\"] = pd.to_datetime(assortment[\"go_live_date\"])\n", "\n", "assortment.dropna(inplace=True)\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "7fb0a4ec-9749-4b2e-9c3e-ef8ef272f178", "metadata": {}, "outputs": [], "source": ["assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d1bb9cc4-1a95-4510-9153-32dcf3c9a654", "metadata": {}, "outputs": [], "source": ["item_id_list = list(assortment[\"item_id\"].unique())\n", "item_id_list = tuple(item_id_list)\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "aaafeb8f-9e8b-45c1-8c3d-c51003b19a81", "metadata": {}, "outputs": [], "source": ["first_live_date = assortment.agg({\"go_live_date\": \"min\"}).reset_index().iloc[:, 1][0]\n", "first_live_date"]}, {"cell_type": "markdown", "id": "139c1b7f-33b5-429c-8deb-4d219fde9f44", "metadata": {}, "source": ["# Assortment base setup"]}, {"cell_type": "code", "execution_count": null, "id": "b84b28ee-a56f-41db-9a41-8c118951c589", "metadata": {}, "outputs": [], "source": ["final_assortment = pd.merge(frontend_outlet_details, assortment, on=[\"city_name\"], how=\"inner\")\n", "\n", "final_assortment = final_assortment[\n", "    [\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"go_live_date\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "    ]\n", "]\n", "\n", "final_assortment.head()"]}, {"cell_type": "markdown", "id": "5532183e-88df-49b7-885f-6f113624a605", "metadata": {}, "source": ["# item details"]}, {"cell_type": "code", "execution_count": null, "id": "64ff98f1-364e-451d-bc60-20605ab15f25", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = \"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select rpp.item_id, (rpp.name || ' ' || variant_description) as item_name, p_type,\n", "            \n", "            l0, l1, l2, pb.manufacturer_id, pm.name as manufacturer_name\n", "\n", "                    from lake_rpc.product_product rpp\n", "\n", "                        join lake_rpc.item_category_details cd on cd.item_id = rpp.item_id\n", "\n", "                        left join (select item_id, product_id from lake_rpc.item_product_mapping) ipm on ipm.item_id = rpp.item_id\n", "                        left join (select id, type_id from lake_cms.gr_product) gp on gp.id = ipm.product_id\n", "                        left join (select id, name as p_type from lake_cms.gr_product_type) pt on pt.id = gp.type_id\n", "\n", "                        left join lake_rpc.product_brand pb on pb.id = rpp.brand_id\n", "                        left join lake_rpc.product_manufacturer pm on pm.id = pb.manufacturer_id\n", "\n", "                            where rpp.id in (select max(id) as id from lake_rpc.product_product where active = 1 and approved = 1 group by item_id)\n", "        )\n", "        \n", "            select * from item_details\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(item_details, redshift)\n", "\n", "\n", "start = time.time()\n", "\n", "item_details = item_details()\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "fffea58e-18e4-4374-996c-b92ec1de6cfe", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "55ca1ca0-992d-4de7-a846-23a166785e85", "metadata": {}, "outputs": [], "source": ["adding_item_details = pd.merge(final_assortment, item_details, on=[\"item_id\"], how=\"left\")\n", "\n", "adding_item_details.head()"]}, {"cell_type": "markdown", "id": "5083103b-f083-40d8-a793-399de1e4400d", "metadata": {}, "source": ["# active assortment details"]}, {"cell_type": "code", "execution_count": null, "id": "4d57c318-6620-4ad8-bc8b-e78ea2ed537f", "metadata": {}, "outputs": [], "source": ["def active_assortment():\n", "    active_assortment = f\"\"\"\n", "    \n", "    with\n", "    active_assortment as\n", "        (select item_id, facility_id, master_assortment_substate_id as status\n", "            from lake_view_rpc.product_facility_master_assortment\n", "                where active = 1\n", "        )\n", "        \n", "            select * from active_assortment where item_id in {item_id_list} and facility_id in {fe_facility_details}\n", "    \"\"\"\n", "    return pd.read_sql_query(active_assortment, presto)\n", "\n", "\n", "start = time.time()\n", "active_assortment = active_assortment()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "5ccadb20-8a7d-477b-97d8-778c6e14f01d", "metadata": {}, "outputs": [], "source": ["active_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5ae090db-027e-419b-b49b-e4dcd3400abe", "metadata": {}, "outputs": [], "source": ["adding_active_assortment = pd.merge(\n", "    adding_item_details, active_assortment, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "adding_active_assortment[\"status\"] = adding_active_assortment[\"status\"].fillna(0).astype(int)\n", "\n", "adding_active_assortment[\"assortment_status\"] = np.where(\n", "    (adding_active_assortment[\"status\"] == 0),\n", "    \"not_part_of_assortment\",\n", "    \"part_of_assortment\",\n", ")\n", "\n", "adding_active_assortment.head()"]}, {"cell_type": "markdown", "id": "b1c91d1d-9bc7-4541-9a05-b7f25ab7ab9d", "metadata": {}, "source": ["# tea tagging details"]}, {"cell_type": "code", "execution_count": null, "id": "b23714db-6830-4275-887d-4200b18ea9a7", "metadata": {}, "outputs": [], "source": ["def tea_taggings():\n", "    tea_taggings = f\"\"\"\n", "    \n", "    with\n", "    tea_taggings as\n", "        (select item_id, outlet_id as hot_outlet_id, tag_value as be_hot_outlet_id\n", "            from lake_view_rpc.item_outlet_tag_mapping\n", "                where tag_type_id = 8 and active = 1\n", "        )\n", "        \n", "            select * from tea_taggings where item_id in {item_id_list} and hot_outlet_id in {fe_facility_details}\n", "    \"\"\"\n", "    return pd.read_sql_query(tea_taggings, presto)\n", "\n", "\n", "start = time.time()\n", "tea_taggings = tea_taggings()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "d646a18b-e363-4a36-803b-729fb86544e6", "metadata": {}, "outputs": [], "source": ["tea_taggings.head()"]}, {"cell_type": "code", "execution_count": null, "id": "02162187-3ca7-4dba-9b6e-4f6c5c68d840", "metadata": {}, "outputs": [], "source": ["tea_taggings[\"be_hot_outlet_id\"] = tea_taggings[\"be_hot_outlet_id\"].astype(int)\n", "\n", "tea_taggings_adding_be = pd.merge(tea_taggings, all_hot_name, on=[\"be_hot_outlet_id\"], how=\"inner\")\n", "\n", "tea_taggings_adding_be.head()"]}, {"cell_type": "markdown", "id": "db943082-d76f-42f3-9e17-1ca3915da46a", "metadata": {}, "source": ["# ARS mapping"]}, {"cell_type": "code", "execution_count": null, "id": "5b17a97d-9127-4d40-9983-922ca5e07e81", "metadata": {}, "outputs": [], "source": ["def ars_mapping():\n", "    ars_mapping = \"\"\"\n", "    \n", "    with\n", "    ars_mapping as\n", "        (select facility_id as be_facility_id, outlet_id as hot_outlet_id\n", "\n", "            from lake_view_po.bulk_facility_outlet_mapping\n", "\n", "                where active = true\n", "        )\n", "\n", "            select * from ars_mapping\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(ars_mapping, presto)\n", "\n", "\n", "start = time.time()\n", "ars_mapping = ars_mapping()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "a3c597d3-6c82-40d3-835b-943c69aa6c1b", "metadata": {}, "outputs": [], "source": ["ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e0f4534f-6a37-4fb5-a22c-dd2f4eb37d50", "metadata": {}, "outputs": [], "source": ["adding_ars_mapping = pd.merge(\n", "    tea_taggings_adding_be,\n", "    ars_mapping,\n", "    on=[\"be_facility_id\", \"hot_outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "adding_ars_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d058ab64-61d1-4a81-8b20-562ace348f28", "metadata": {}, "outputs": [], "source": ["adding_tea_details = pd.merge(\n", "    adding_active_assortment,\n", "    adding_ars_mapping,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]] = (\n", "    adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_tea_details[\"be_facility_name\"] = adding_tea_details[\"be_facility_name\"].fillna(\n", "    \"No Backend Tagged\"\n", ")\n", "\n", "adding_tea_details.head()"]}, {"cell_type": "markdown", "id": "4fef1adc-4705-475a-b8b6-1c2b753f943a", "metadata": {}, "source": ["# inventory details"]}, {"cell_type": "code", "execution_count": null, "id": "e4cac861-9c3c-41f8-83c8-5c25ec61c61f", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select item_id, outlet_id as hot_outlet_id, sum(quantity) as actual_inv\n", "            from lake_view_ims.ims_item_inventory\n", "                where active = 1\n", "                    group by 1,2\n", "        )\n", "        \n", "            select * from inv where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(inventory, presto)\n", "\n", "\n", "start = time.time()\n", "inventory = inventory()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "e913c317-63d3-4cdb-afa5-57dbe0bdfca6", "metadata": {}, "outputs": [], "source": ["inventory.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2d37fb2c-1e6a-4777-8c65-f25fb6beba74", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "be_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ebc91441-0934-4519-b589-c116e68f9660", "metadata": {}, "outputs": [], "source": ["adding_fe_inv_details = pd.merge(\n", "    adding_tea_details, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "adding_be_inv_details = pd.merge(\n", "    adding_fe_inv_details, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]] = (\n", "    adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_inv_details.head()"]}, {"cell_type": "markdown", "id": "366a8600-6d75-436f-8576-f136e4f04229", "metadata": {}, "source": ["# blocked inv details"]}, {"cell_type": "code", "execution_count": null, "id": "17b34f6d-5b52-4575-bdbc-7ff214f657ee", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select item_id, outlet_id as hot_outlet_id,\n", "            sum(case when blocked_type in (1,2,5) then quantity else 0 end) as ttl_blocked_qty\n", "                from lake_view_ims.ims_item_blocked_inventory\n", "                    where active = 1 and blocked_type in (1,2,5)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from blocked where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(blocked_inv, presto)\n", "\n", "\n", "start = time.time()\n", "blocked_inv = blocked_inv()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "3a18f4af-9ac9-4cb8-9f0b-594a43ffe5f7", "metadata": {}, "outputs": [], "source": ["blocked_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c0fee555-3f7c-46e3-a750-6324af1a7c3a", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")\n", "be_blocked.head()"]}, {"cell_type": "code", "execution_count": null, "id": "16eafa2b-2fd9-44b4-8c7e-a842560e4a2d", "metadata": {}, "outputs": [], "source": ["adding_fe_blocked_details = pd.merge(\n", "    adding_be_inv_details, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_blocked_details = pd.merge(\n", "    adding_fe_blocked_details,\n", "    be_blocked,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]] = (\n", "    adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_blocked_details[\"net_inventory\"] = np.where(\n", "    (adding_be_blocked_details[\"actual_inv\"] - adding_be_blocked_details[\"ttl_blocked_qty\"]) < 0,\n", "    0,\n", "    (adding_be_blocked_details[\"actual_inv\"] - adding_be_blocked_details[\"ttl_blocked_qty\"]),\n", ")\n", "\n", "adding_be_blocked_details[\"be_net_inventory\"] = np.where(\n", "    (adding_be_blocked_details[\"be_actual_inv\"] - adding_be_blocked_details[\"be_ttl_blocked_qty\"])\n", "    < 0,\n", "    0,\n", "    (adding_be_blocked_details[\"be_actual_inv\"] - adding_be_blocked_details[\"be_ttl_blocked_qty\"]),\n", ")\n", "\n", "\n", "adding_be_blocked_details.head()"]}, {"cell_type": "markdown", "id": "fd940752-e514-4402-851e-cd1d28ebd870", "metadata": {}, "source": ["# pending putaway"]}, {"cell_type": "code", "execution_count": null, "id": "d6d6167b-c99f-4697-8a50-21473c5da416", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    pp as\n", "        (select rpc.item_id, outlet_id as hot_outlet_id, sum(quantity) as pending_putaway\n", "            from lake_view_ims.ims_good_inventory igi\n", "                join lake_view_rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "                    where igi.active = 1 and igi.inventory_update_type_id in (28,76)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from pp where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(pen_put, presto)\n", "\n", "\n", "start = time.time()\n", "pen_put = pen_put()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "b30e429d-8781-45b5-a5bf-030db0d207d0", "metadata": {}, "outputs": [], "source": ["pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4df37983-f961-4bcc-b8a4-b147dfd69df4", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")\n", "be_pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "281814cb-5759-403c-a817-f4d9072af100", "metadata": {}, "outputs": [], "source": ["adding_fe_pen_putaway_details = pd.merge(\n", "    adding_be_blocked_details, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.merge(\n", "    adding_fe_pen_putaway_details,\n", "    be_pen_put,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]] = (\n", "    adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_pen_putaway_details.head()"]}, {"cell_type": "markdown", "id": "3b6e0357-99f1-4dcc-a952-4aa6bcc5db21", "metadata": {}, "source": ["# open STO details"]}, {"cell_type": "code", "execution_count": null, "id": "8737d026-436d-4b49-9a6c-bbdbae8f8457", "metadata": {}, "outputs": [], "source": ["def sto_details():\n", "    sto_details = f\"\"\"\n", "    \n", "    with\n", "    inward_invoice_details as\n", "        (select vendor_invoice_id from lake_view_ims.ims_inward_invoice\n", "            where cast(insert_ds_ist as date) >= current_date - interval '40' day\n", "                and source_type in (2)\n", "        ),\n", "\n", "    billed_invoice as\n", "        (select grofers_order_id as sto_id, id, outlet_id, \n", "            case when iid.vendor_invoice_id is null then 'raised' else 'grn_done' end as invoice_status\n", "\n", "                from lake_view_pos.pos_invoice pi\n", "\n", "                    left join inward_invoice_details iid on iid.vendor_invoice_id = pi.invoice_id\n", "\n", "                    where cast(insert_ds_ist as date) >= current_date - interval '40' day\n", "                        and invoice_type_id in (5,14,16)\n", "        ),\n", "\n", "    invoice_item_details as\n", "        (select item_id, outlet_id, sto_id, sum(quantity) as billed_qty\n", "\n", "            from lake_view_pos.pos_invoice_product_details pd\n", "\n", "                join (select item_id, upc from lake_view_rpc.product_product\n", "                    where id in (select max(id) as id from lake_rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                        ) rpp on rpp.upc = pd.upc_id\n", "\n", "                join billed_invoice bi on bi.id = pd.invoice_id and invoice_status in ('raised')\n", "\n", "                    where insert_ds_ist >= cast((current_date - interval '41' day) as varchar)\n", "\n", "                        group by 1,2,3\n", "        ),\n", "\n", "    sto_id as\n", "        (select sd.merchant_outlet_id, sd.sto_id, invoice_status from lake_view_ims.ims_sto_details sd\n", "            left join billed_invoice bi on cast(bi.sto_id as bigint) = sd.sto_id\n", "                where created_at between cast(current_date as timestamp)-interval '40' day - interval '330' minute and\n", "                    cast(current_date as timestamp)+interval '1' day - interval '330' minute\n", "                    and sto_state in (2,3) and invoice_status not in ('grn_done')\n", "        ),\n", "\n", "    sto_item_details as\n", "        (select sto_id, item_id, reserved_quantity as sto_quantity\n", "\n", "            from lake_view_po.sto_items\n", "\n", "                where created_at between cast(current_date as timestamp)-interval '40' day - interval '330' minute and\n", "                    cast(current_date as timestamp)+interval '1' day - interval '330' minute\n", "        ),\n", "\n", "    final as\n", "        (select sid.sto_id, si.merchant_outlet_id, sid.item_id, sid.sto_quantity, iid.billed_qty,\n", "            case when iid.sto_id is null then sid.sto_quantity else iid.billed_qty end as open_sto_qty\n", "\n", "                from sto_item_details sid\n", "\n", "                    join sto_id si on si.sto_id = sid.sto_id\n", "                    left join invoice_item_details iid on cast(iid.sto_id as int) = sid.sto_id and iid.item_id = sid.item_id\n", "        )\n", "\n", "            select merchant_outlet_id as hot_outlet_id, item_id, sum(open_sto_qty) as open_sto_qty\n", "\n", "                from final\n", "                    \n", "                    where item_id in {item_id_list} and merchant_outlet_id in {all_inv_outlet_id}\n", "                    \n", "                        group by 1,2\n", "    \"\"\"\n", "    return pd.read_sql_query(sto_details, presto)\n", "\n", "\n", "start = time.time()\n", "sto_details = sto_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "83155ffa-f176-48c2-9ef7-953e8331cca1", "metadata": {}, "outputs": [], "source": ["sto_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "83026930-a841-465d-b64b-c1e277c8106e", "metadata": {}, "outputs": [], "source": ["be_sto = sto_details.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"open_sto_qty\": \"be_open_sto_qty\"}\n", ")\n", "be_sto.head()"]}, {"cell_type": "code", "execution_count": null, "id": "725dc037-6ce0-44ee-b3a7-89349156aa6d", "metadata": {}, "outputs": [], "source": ["adding_fe_sto_details = pd.merge(\n", "    adding_be_pen_putaway_details,\n", "    sto_details,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_sto_details = pd.merge(\n", "    adding_fe_sto_details, be_sto, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_sto_details[[\"open_sto_qty\", \"be_open_sto_qty\"]] = (\n", "    adding_be_sto_details[[\"open_sto_qty\", \"be_open_sto_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_sto_details.head()"]}, {"cell_type": "markdown", "id": "3b9765be-77c0-40d4-ba32-326ea250c883", "metadata": {}, "source": ["# PO details"]}, {"cell_type": "code", "execution_count": null, "id": "eba5d363-5a67-4e7d-acb5-de07ff42371b", "metadata": {}, "outputs": [], "source": ["def po_details():\n", "    po_details = f\"\"\"\n", "    \n", "    with\n", "    open_po_details as\n", "        (select po_number, po_scheduled_date, po.outlet_id as hot_outlet_id, poi.item_id,\n", "            poi.units_ordered as po_quantity, \n", "            case when grn.grn_quantity is null then 0 else grn.grn_quantity end as grn_quantity,\n", "            case when (ppos.po_state_id in (8) or (ppos.po_state_id in (9) and po.is_multiple_grn != 1)) then 0 else 1 end po_status\n", "\n", "                from lake_view_po.purchase_order_items poi\n", "\n", "                    join lake_view_po.purchase_order po on po.id = poi.po_id\n", "\n", "                    join lake_view_po.purchase_order_status ppos on ppos.po_id = po.id\n", "\n", "                    left join (select po_id_id, date(schedule_date_time + interval '330' minute) as po_scheduled_date\n", "                        from lake_view_po.po_schedule) ps on ps.po_id_id = po.id\n", "\n", "                    left join (select po_id, item_id, sum(quantity) as grn_quantity\n", "                        from lake_view_po.po_grn\n", "                            where created_at >= cast('{first_live_date}' as timestamp) - interval '330' minute\n", "                                group by 1,2\n", "                                ) grn on grn.item_id = poi.item_id and grn.po_id = poi.po_id\n", "\n", "                        where ppos.po_state_id in (2,3,8,9,13,14,15)\n", "                            and ppos.po_state_id not in (4,5,10) and po.po_type_id != 11\n", "                            and po.created_at >= cast('{first_live_date}' as timestamp) - interval '330' minute\n", "        ),\n", "\n", "    final as\n", "        (select po_number,\n", "            case when po_status = 0 then null else po_scheduled_date end po_scheduled_date,\n", "            hot_outlet_id, item_id, po_quantity, grn_quantity,\n", "            case when po_status = 0 then 0 else (po_quantity - grn_quantity) end as open_po_quantity\n", "\n", "                from open_po_details\n", "        )\n", "\n", "            select hot_outlet_id, item_id, sum(po_quantity) as ttl_po_quantity, sum(grn_quantity) as ttl_grn_quantity,\n", "                sum(open_po_quantity) as ttl_open_po_quantity\n", "\n", "                    from final\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_hot_outlet_id}\n", "\n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(po_details, presto)\n", "\n", "\n", "start = time.time()\n", "po_details = po_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "0228ab9a-0e80-4a67-b7e2-115df103abbd", "metadata": {}, "outputs": [], "source": ["po_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6315bdc8-d241-4b0e-84f3-e619db371199", "metadata": {}, "outputs": [], "source": ["be_po_details = po_details.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"ttl_po_quantity\": \"be_ttl_po_quantity\",\n", "        \"ttl_grn_quantity\": \"be_ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\": \"be_ttl_open_po_quantity\",\n", "    }\n", ")\n", "\n", "be_po_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3fb40f19-9788-4b73-b10a-7c4e5bc6e00d", "metadata": {}, "outputs": [], "source": ["adding_fe_po_details = pd.merge(\n", "    adding_be_sto_details, po_details, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_po_details = pd.merge(\n", "    adding_fe_po_details, be_po_details, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_po_details[\n", "    [\n", "        \"ttl_po_quantity\",\n", "        \"ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\",\n", "        \"be_ttl_po_quantity\",\n", "        \"be_ttl_grn_quantity\",\n", "        \"be_ttl_open_po_quantity\",\n", "    ]\n", "] = (\n", "    adding_be_po_details[\n", "        [\n", "            \"ttl_po_quantity\",\n", "            \"ttl_grn_quantity\",\n", "            \"ttl_open_po_quantity\",\n", "            \"be_ttl_po_quantity\",\n", "            \"be_ttl_grn_quantity\",\n", "            \"be_ttl_open_po_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_po_details.head()"]}, {"cell_type": "markdown", "id": "a1c7b8ad-6dff-4cf6-bb4a-de9a5b69df8d", "metadata": {}, "source": ["# frontend min max details"]}, {"cell_type": "code", "execution_count": null, "id": "0fb55c2b-9a81-43d9-9aad-73c66241a1ed", "metadata": {}, "outputs": [], "source": ["def min_max():\n", "    min_max = f\"\"\"\n", "    \n", "    with\n", "    min_max as\n", "        (select facility_id, item_id, min_quantity, max_quantity\n", "        \n", "            from lake_view_ars.item_min_max_quantity\n", "        )\n", "        \n", "            select * from min_max where item_id in {item_id_list} and facility_id in {all_facility_id}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(min_max, presto)\n", "\n", "\n", "start = time.time()\n", "min_max = min_max()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "0aaf7b6c-72fa-4023-866f-033ad1098c4a", "metadata": {}, "outputs": [], "source": ["min_max.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0b8bf0d3-820c-446f-964e-70d889922185", "metadata": {}, "outputs": [], "source": ["adding_min_max = pd.merge(adding_be_po_details, min_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "adding_min_max[[\"min_quantity\", \"max_quantity\"]] = (\n", "    adding_min_max[[\"min_quantity\", \"max_quantity\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_min_max.head()"]}, {"cell_type": "markdown", "id": "7609ff92-ba93-44ac-b333-54be1f6c6365", "metadata": {}, "source": ["# sales details"]}, {"cell_type": "code", "execution_count": null, "id": "5a3f4ee1-4b18-4347-b00b-bb9bc9953d53", "metadata": {}, "outputs": [], "source": ["def sales():\n", "    sales = f\"\"\"\n", "    \n", "    with\n", "    sales as\n", "        (select\n", "            date(oi.install_ts + interval '5.5 Hours') as date_,\n", "            od.hot_outlet_id,\n", "            oi.product_id,\n", "            pl.item_id,\n", "            oi.selling_price,\n", "            (oi.selling_price*1.000/pl.multiplier+0.000) as item_selling_price,\n", "            oi.order_id,\n", "            pl.multiplier as combo_qty,\n", "            (oi.quantity * combo_qty) as qty,\n", "            ((oi.quantity * combo_qty) - (oi.cancelled_quantity * combo_qty)) as f_ordered_qty\n", "\n", "                from lake_oms_bifrost.oms_order_item oi\n", "\n", "                    left join (select distinct item_id, product_id, coalesce(multiplier,1) as multiplier \n", "                        from dwh.dim_item_product_offer_mapping\n", "                                ) pl on pl.product_id = oi.product_id\n", "\n", "                    left join lake_oms_bifrost.oms_order oo on oo.id = oi.order_id and oo.type in ('RetailForwardOrder','RetailSuborder','')\n", "\n", "                    join (select distinct outlet as hot_outlet_id, ancestor from lake_ims.ims_order_details od\n", "                        join lake_retail.console_outlet rco on rco.id = outlet and business_type_id in (7)\n", "                            where status_id not in (3,4,5) and od.created_at >= cast('{first_live_date}' - 2 as timestamp) - interval '5.5 Hours'\n", "                                ) od on od.ancestor = oi.order_id\n", "\n", "                    where oi.install_ts >= cast('{first_live_date}' as timestamp) - interval '5.5 Hours'\n", "        ),\n", "\n", "    final_sales as\n", "        (select order_id, date_, hot_outlet_id, item_id, sum(f_ordered_qty) as sales_qty,\n", "            avg(item_selling_price) as avg_selling_price, (sales_qty * avg_selling_price) as sales_value\n", "                from sales\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    final as\n", "        (select date_, hot_outlet_id, item_id, count(distinct order_id) as carts, sum(sales_qty) as sales_qty, sum(sales_value) as sales_value\n", "            from final_sales\n", "                group by 1,2,3\n", "        )\n", "\n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then sales_qty end) as today_sales_qty,\n", "                sum(case when date_ = current_date then sales_value end) as today_sales_value,\n", "                sum(case when date_ = current_date then carts end) as today_carts,\n", "\n", "                sum(case when date_ = current_date - 1 then sales_qty end) as yesterday_sales_qty,\n", "                sum(case when date_ = current_date - 1 then sales_value end) as yesterday_sales_value,\n", "                sum(case when date_ = current_date - 1 then carts end) as yesterday_carts,\n", "\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then sales_qty end) as l7days_sales_qty,\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then sales_value end) as l7days_sales_value,\n", "                sum(case when date_ between current_date - 7 and current_date - 1 then carts end) as l7days_carts,\n", "\n", "                sum(sales_qty) as ttl_sales_qty,\n", "                sum(sales_value) as ttl_sales_value,\n", "                sum(carts) as ttl_carts\n", "\n", "                    from final\n", "\n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "\n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(sales, redshift)\n", "\n", "\n", "start = time.time()\n", "sales = sales()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "59fbdedd-84ca-486a-88fc-c6dc21b53ef8", "metadata": {}, "outputs": [], "source": ["sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e1ee710a-4ac7-41ad-af20-04ce165b4c7b", "metadata": {}, "outputs": [], "source": ["adding_sales_details = pd.merge(adding_min_max, sales, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\")\n", "\n", "adding_sales_details[\n", "    [\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"ttl_sales_qty\",\n", "        \"ttl_sales_value\",\n", "        \"ttl_carts\",\n", "    ]\n", "] = (\n", "    adding_sales_details[\n", "        [\n", "            \"today_sales_qty\",\n", "            \"today_sales_value\",\n", "            \"today_carts\",\n", "            \"yesterday_sales_qty\",\n", "            \"yesterday_sales_value\",\n", "            \"yesterday_carts\",\n", "            \"l7days_sales_qty\",\n", "            \"l7days_sales_value\",\n", "            \"l7days_carts\",\n", "            \"ttl_sales_qty\",\n", "            \"ttl_sales_value\",\n", "            \"ttl_carts\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_sales_details.head()"]}, {"cell_type": "markdown", "id": "dfded44f-c27e-446d-a173-e54734bf014d", "metadata": {}, "source": ["# dump details"]}, {"cell_type": "code", "execution_count": null, "id": "ca0da24b-3b2b-469d-9af7-c67bdc1a1dec", "metadata": {}, "outputs": [], "source": ["def dump():\n", "    dump = f\"\"\"\n", "    \n", "    with\n", "    dump as\n", "        (select date(pos_timestamp + interval '330' minute) as date_, \n", "            outlet_id as hot_outlet_id, item_id, sum(il.\"delta\") as dump_quantity, avg(il.weighted_lp) as avg_lp\n", "\n", "                from lake_view_ims.ims_inventory_log il\n", "\n", "                    join (select distinct item_id, variant_id from lake_view_rpc.product_product) rpp on rpp.variant_id = il.variant_id\n", "\n", "                        where cast(insert_ds_ist as date) >= cast('{first_live_date}' as timestamp) - interval '330' minute\n", "                            and inventory_update_type_id in (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "\n", "                                group by 1,2,3\n", "        ),\n", "        \n", "    final as\n", "        (select *, (dump_quantity * avg_lp) as dump_value\n", "            from dump\n", "        )\n", "        \n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then dump_quantity end) as today_dump_quantity,\n", "                sum(case when date_ = current_date then dump_value end) as today_dump_value,\n", "\n", "                sum(case when date_ = current_date - interval '1' day then dump_quantity end) as yesterday_dump_quantity,\n", "                sum(case when date_ = current_date - interval '1' day then dump_value end) as yesterday_dump_value,\n", "\n", "                sum(case when date_ between current_date - interval '7' day and current_date - interval '1' day then dump_quantity end) as l7days_dump_quantity,\n", "                sum(case when date_ between current_date - interval '7' day and current_date - interval '1' day then dump_value end) as l7days_dump_value,\n", "                \n", "                sum(dump_quantity) as ttl_dump_quantity,\n", "                sum(dump_value) as ttl_dump_value\n", "                \n", "                    from final\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "                    \n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(dump, presto)\n", "\n", "\n", "start = time.time()\n", "dump = dump()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "38f0d3af-9e3a-416f-8536-11f5a13d7379", "metadata": {}, "outputs": [], "source": ["dump.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3d88ab2c-d093-4251-b54d-e0cc2ea0f059", "metadata": {}, "outputs": [], "source": ["be_dump_details = dump.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"today_dump_quantity\": \"be_today_dump_quantity\",\n", "        \"today_dump_value\": \"be_today_dump_value\",\n", "        \"yesterday_dump_quantity\": \"be_yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\": \"be_yesterday_dump_value\",\n", "        \"l7days_dump_quantity\": \"be_l7days_dump_quantity\",\n", "        \"l7days_dump_value\": \"be_l7days_dump_value\",\n", "        \"ttl_dump_quantity\": \"be_ttl_dump_quantity\",\n", "        \"ttl_dump_value\": \"be_ttl_dump_value\",\n", "    }\n", ")\n", "\n", "be_dump_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "14ebb440-cc7e-4d39-9176-41f45b582427", "metadata": {}, "outputs": [], "source": ["adding_fe_dump_details = pd.merge(\n", "    adding_sales_details, dump, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_dump_details = pd.merge(\n", "    adding_fe_dump_details,\n", "    be_dump_details,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_dump_details[\n", "    [\n", "        \"today_dump_quantity\",\n", "        \"be_today_dump_quantity\",\n", "        \"today_dump_value\",\n", "        \"be_today_dump_value\",\n", "        \"yesterday_dump_quantity\",\n", "        \"be_yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\",\n", "        \"be_yesterday_dump_value\",\n", "        \"l7days_dump_quantity\",\n", "        \"be_l7days_dump_quantity\",\n", "        \"l7days_dump_value\",\n", "        \"be_l7days_dump_value\",\n", "        \"ttl_dump_quantity\",\n", "        \"be_ttl_dump_quantity\",\n", "        \"ttl_dump_value\",\n", "        \"be_ttl_dump_value\",\n", "    ]\n", "] = (\n", "    adding_be_dump_details[\n", "        [\n", "            \"today_dump_quantity\",\n", "            \"be_today_dump_quantity\",\n", "            \"today_dump_value\",\n", "            \"be_today_dump_value\",\n", "            \"yesterday_dump_quantity\",\n", "            \"be_yesterday_dump_quantity\",\n", "            \"yesterday_dump_value\",\n", "            \"be_yesterday_dump_value\",\n", "            \"l7days_dump_quantity\",\n", "            \"be_l7days_dump_quantity\",\n", "            \"l7days_dump_value\",\n", "            \"be_l7days_dump_value\",\n", "            \"ttl_dump_quantity\",\n", "            \"be_ttl_dump_quantity\",\n", "            \"ttl_dump_value\",\n", "            \"be_ttl_dump_value\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "adding_be_dump_details.head()"]}, {"cell_type": "markdown", "id": "85eee21d-8441-498e-bba2-5453fd30f7f8", "metadata": {}, "source": ["# ARS run id"]}, {"cell_type": "code", "execution_count": null, "id": "f2d4b57f-a93c-4eea-b798-1eebb367d66d", "metadata": {}, "outputs": [], "source": ["def ars_run_id():\n", "    ars_run_id = f\"\"\"\n", "    \n", "    with\n", "    ars_run_id as\n", "        (select \n", "            extract(hour from (started_at + interval '330' minute)) as hour_,\n", "            id,\n", "            run_id,\n", "            outlet_id,\n", "            run_date,\n", "            success,\n", "            (started_at + interval '330' minute) as started_at,\n", "            (completed_at + interval '330' minute) as completed_at,\n", "            details,\n", "            facility_id,\n", "            is_simulation,\n", "            simulation_params,\n", "            extra,\n", "            json_format(json_extract(extra,'$.auto_sto_outlets')) AS ars_run,\n", "            replace(json_extract_scalar(simulation_params,'$.run'), '\"', '') as ars_run_flag,\n", "            json_extract_scalar(simulation_params, '$.manual') as manual_run,\n", "            replace(json_extract_scalar(simulation_params, '$.mode'),'\"','') as ars_mode\n", "\n", "                from lake_view_ars.job_run\n", "\n", "                    where completed_at >= cast(current_date as timestamp) - interval '10' day - interval '330' minute\n", "                        and json_format(json_extract(replace(replace(extra, '[', ''), ']', ''),'$.auto_sto_outlets')) is not null\n", "        ),\n", "\n", "    ars_frontend_outlets as\n", "        (select date(completed_at) as date_, hour_, facility_id as be_facility_id, cast(replace(replace(split_b, '[', ''), ']', '') as int) as fe_outlet_id, run_id\n", "            from ars_run_id\n", "                cross join unnest(split(ars_run,',')) AS t (split_b)\n", "\n", "                    where ars_run is not null\n", "                        -- lower(ars_mode) in ('perishable') and \n", "        ),\n", "\n", "    final_ars_run_id as\n", "        (select afo.date_, afo.hour_, afo.be_facility_id as be_facility_id, afo.fe_outlet_id as hot_outlet_id, rco.facility_id, afo.run_id\n", "\n", "            from ars_frontend_outlets afo\n", "\n", "                join lake_view_retail.console_outlet rco on rco.id = afo.fe_outlet_id\n", "        )\n", "\n", "            select * from final_ars_run_id   \n", "            \n", "    \"\"\"\n", "    return pd.read_sql_query(ars_run_id, presto)\n", "\n", "\n", "start = time.time()\n", "ars_run_id = ars_run_id()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "be0413d8-038c-4901-a01d-f9a3430e44ae", "metadata": {}, "outputs": [], "source": ["ars_run_id.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7ff26742-e953-4d6c-8c3a-dd42df681942", "metadata": {}, "outputs": [], "source": ["run_id_list = list(ars_run_id[\"run_id\"].unique())\n", "run_id_list = tuple(run_id_list)\n", "len(run_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "0e117baa-061a-4c64-8c13-487f063116c0", "metadata": {}, "outputs": [], "source": ["min_live_date = ars_run_id.agg({\"date_\": \"min\"}).reset_index().iloc[:, 1][0]\n", "min_live_date"]}, {"cell_type": "markdown", "id": "8687f3fe-bec5-4730-9bfc-101d60ed0316", "metadata": {}, "source": ["# truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "ecee6518-2e7b-4e8b-a412-2fcf70424b1f", "metadata": {}, "outputs": [], "source": ["def truncation_details():\n", "    truncation_details = f\"\"\"\n", "    \n", "    with\n", "    truncation_details as\n", "        (select backend_outlet_id as be_hot_outlet_id, frontend_outlet_id as hot_outlet_id, item_id, sto_quantity as v1_indent,\n", "            sto_quantity_post_truncation_in_case as v2_indent, run_id,\n", "            json_extract_scalar(quantity_drops, '$.inward_drop') as inward_drop,\n", "            json_extract_scalar(quantity_drops, '$.storage_drop') as storage_drop,\n", "            json_extract_scalar(quantity_drops, '$.truck_load_drop') as truck_load_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_sku_drop') as picking_capacity_sku_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_quantity_drop') as picking_capacity_quantity_drop,\n", "            (sto_quantity_post_truncation - sto_quantity_post_truncation_in_case) as loose_quantity_drop\n", "                from lake_view_ars.transfers_optimization_results_v2\n", "                    where cast(insert_ds_ist as date) >= cast('{min_live_date}' as date)\n", "        )\n", "\n", "            select be_hot_outlet_id, hot_outlet_id, item_id, cast(v1_indent as int) as v1_indent, cast(v2_indent as int) as v2_indent, run_id,\n", "                inward_drop, storage_drop, truck_load_drop, picking_capacity_sku_drop, picking_capacity_quantity_drop, loose_quantity_drop\n", "\n", "                    from truncation_details\n", "\n", "                        where run_id in {run_id_list} and item_id in {item_id_list}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(truncation_details, presto)\n", "\n", "\n", "start = time.time()\n", "truncation_details = truncation_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "1cfac128-2e4e-4209-8a98-aa2c2add0dcd", "metadata": {}, "outputs": [], "source": ["truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "27f9951d-e560-4a2b-aa7c-e5b7f2bb7271", "metadata": {}, "outputs": [], "source": ["truncation_details[\n", "    [\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"inward_drop\",\n", "        \"storage_drop\",\n", "        \"truck_load_drop\",\n", "        \"picking_capacity_sku_drop\",\n", "        \"picking_capacity_quantity_drop\",\n", "        \"loose_quantity_drop\",\n", "    ]\n", "] = (\n", "    truncation_details[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"inward_drop\",\n", "            \"storage_drop\",\n", "            \"truck_load_drop\",\n", "            \"picking_capacity_sku_drop\",\n", "            \"picking_capacity_quantity_drop\",\n", "            \"loose_quantity_drop\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_run_id = pd.merge(\n", "    truncation_details, ars_run_id, on=[\"run_id\", \"hot_outlet_id\"], how=\"inner\"\n", ")\n", "\n", "adding_run_id[\"date_\"] = pd.to_datetime(adding_run_id[\"date_\"])\n", "\n", "adding_run_id.head()"]}, {"cell_type": "code", "execution_count": null, "id": "964cefee-f6cd-443d-853d-001e2bc86664", "metadata": {}, "outputs": [], "source": ["new_truncation_details = adding_run_id.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "1e9346c9-5c67-4c65-ae4c-f04ace916a4b", "metadata": {}, "outputs": [], "source": ["new_truncation_details[\"new_inward_drop\"] = np.where(\n", "    (new_truncation_details[\"inward_drop\"] > 0)\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"inward_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_storage_drop\"] = np.where(\n", "    (new_truncation_details[\"storage_drop\"] > 0)\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"storage_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_truck_load_drop\"] = np.where(\n", "    (new_truncation_details[\"truck_load_drop\"] > 0)\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"truck_load_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_sku_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_sku_drop\"] > 0)\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_sku_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_quantity_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_loose_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"loose_quantity_drop\"] > 0)\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"loose_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"total_drop_quantity\"] = (\n", "    new_truncation_details[\"new_inward_drop\"]\n", "    + new_truncation_details[\"new_storage_drop\"]\n", "    + new_truncation_details[\"new_truck_load_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_sku_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_quantity_drop\"]\n", "    + new_truncation_details[\"new_loose_quantity_drop\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "951f9230-2928-446e-81a6-fdb6b46e0dcf", "metadata": {}, "outputs": [], "source": ["new_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "755b8da0-5a62-4bd4-9e0c-559fe04c14c5", "metadata": {}, "outputs": [], "source": ["new_truncation_details = new_truncation_details[\n", "    [\n", "        \"date_\",\n", "        \"run_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"item_id\",\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "]\n", "\n", "\n", "new_truncation_details.head()"]}, {"cell_type": "markdown", "id": "6bc90fe1-9fb6-49af-a37b-759f86ee8f51", "metadata": {}, "source": ["# frontend truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "cef7aae3-a9dc-4606-b68a-9b6f16ff6ee8", "metadata": {}, "outputs": [], "source": ["frontend_current_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_current_date_truncation = frontend_current_date_truncation[\n", "    frontend_current_date_truncation[\"date_\"] == datetime.today().strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_current_date_truncation = (\n", "    frontend_current_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t_v1_indent\",\n", "        \"v2_indent\": \"t_v2_indent\",\n", "        \"new_inward_drop\": \"t_new_inward_drop\",\n", "        \"new_storage_drop\": \"t_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_1_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_1_date_truncation = frontend_l_1_date_truncation[\n", "    frontend_l_1_date_truncation[\"date_\"]\n", "    == (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_l_1_date_truncation = (\n", "    frontend_l_1_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t1_v1_indent\",\n", "        \"v2_indent\": \"t1_v2_indent\",\n", "        \"new_inward_drop\": \"t1_new_inward_drop\",\n", "        \"new_storage_drop\": \"t1_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t1_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t1_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t1_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_7_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_7_date_truncation = frontend_l_7_date_truncation[\n", "    (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        >= (datetime.today() - pd.DateOffset(days=7)).strftime(\"%Y-%m-%d\")\n", "    )\n", "    & (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        <= (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "    )\n", "]\n", "\n", "frontend_l_7_date_truncation = (\n", "    frontend_l_7_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t7_v1_indent\",\n", "        \"v2_indent\": \"t7_v2_indent\",\n", "        \"new_inward_drop\": \"t7_new_inward_drop\",\n", "        \"new_storage_drop\": \"t7_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t7_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t7_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t7_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "new_truncation_details.shape, frontend_current_date_truncation.shape, frontend_l_1_date_truncation.shape, frontend_l_7_date_truncation.shape"]}, {"cell_type": "code", "execution_count": null, "id": "97841e33-f16e-463f-bf87-247c41331365", "metadata": {}, "outputs": [], "source": ["adding_t_truncation_details = pd.merge(\n", "    adding_be_dump_details,\n", "    frontend_current_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t1_truncation_details = pd.merge(\n", "    adding_t_truncation_details,\n", "    frontend_l_1_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t7_truncation_details = pd.merge(\n", "    adding_t1_truncation_details,\n", "    frontend_l_7_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t7_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4574b1ca-9d8b-49ef-b403-851e6f6a9a1c", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details[\n", "    [\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "    ]\n", "] = (\n", "    adding_t7_truncation_details[\n", "        [\n", "            \"t_v1_indent\",\n", "            \"t_v2_indent\",\n", "            \"t_new_inward_drop\",\n", "            \"t_new_storage_drop\",\n", "            \"t_new_truck_load_drop\",\n", "            \"t_new_picking_capacity_sku_drop\",\n", "            \"t_new_picking_capacity_quantity_drop\",\n", "            \"t_new_loose_quantity_drop\",\n", "            \"t_total_drop_quantity\",\n", "            \"t1_v1_indent\",\n", "            \"t1_v2_indent\",\n", "            \"t1_new_inward_drop\",\n", "            \"t1_new_storage_drop\",\n", "            \"t1_new_truck_load_drop\",\n", "            \"t1_new_picking_capacity_sku_drop\",\n", "            \"t1_new_picking_capacity_quantity_drop\",\n", "            \"t1_new_loose_quantity_drop\",\n", "            \"t1_total_drop_quantity\",\n", "            \"t7_v1_indent\",\n", "            \"t7_v2_indent\",\n", "            \"t7_new_inward_drop\",\n", "            \"t7_new_storage_drop\",\n", "            \"t7_new_truck_load_drop\",\n", "            \"t7_new_picking_capacity_sku_drop\",\n", "            \"t7_new_picking_capacity_quantity_drop\",\n", "            \"t7_new_loose_quantity_drop\",\n", "            \"t7_total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_t7_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "98fc2fe9-b547-4b8a-b2bc-bf5445a5da41", "metadata": {}, "outputs": [], "source": ["final = adding_t7_truncation_details[\n", "    [\n", "        \"go_live_date\",\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"p_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"status\",\n", "        \"assortment_status\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"open_sto_qty\",\n", "        \"ttl_po_quantity\",\n", "        \"ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"ttl_sales_qty\",\n", "        \"ttl_sales_value\",\n", "        \"ttl_carts\",\n", "        \"today_dump_quantity\",\n", "        \"today_dump_value\",\n", "        \"yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\",\n", "        \"l7days_dump_quantity\",\n", "        \"l7days_dump_value\",\n", "        \"ttl_dump_quantity\",\n", "        \"ttl_dump_value\",\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"be_actual_inv\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"be_open_sto_qty\",\n", "        \"be_ttl_po_quantity\",\n", "        \"be_ttl_grn_quantity\",\n", "        \"be_ttl_open_po_quantity\",\n", "        \"be_today_dump_quantity\",\n", "        \"be_today_dump_value\",\n", "        \"be_yesterday_dump_quantity\",\n", "        \"be_yesterday_dump_value\",\n", "        \"be_l7days_dump_quantity\",\n", "        \"be_l7days_dump_value\",\n", "        \"be_ttl_dump_quantity\",\n", "        \"be_ttl_dump_value\",\n", "    ]\n", "]\n", "\n", "final.head()"]}, {"cell_type": "code", "execution_count": null, "id": "57cdad56-cedd-4d45-a7af-a3479fbcf71c", "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "77d399d4-0c8f-4a67-a152-0193dd861045", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"go_live_date\", \"type\": \"date\", \"description\": \"name of zone\"},\n", "    {\"name\": \"zone\", \"type\": \"varchar\", \"description\": \"name of zone\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"name of city\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"unique identifier for DS facility\",\n", "    },\n", "    {\n", "        \"name\": \"facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"unique identifier for DS facility name\",\n", "    },\n", "    {\"name\": \"event_flag\", \"type\": \"varchar\", \"description\": \"type of assortment\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"type of event\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"unique identifier for item\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"product name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"product type\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0 category\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1 category\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2 category\"},\n", "    {\"name\": \"manufacturer_id\", \"type\": \"float\", \"description\": \"manufacturer id\"},\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"manufacturer name\",\n", "    },\n", "    {\"name\": \"status\", \"type\": \"integer\", \"description\": \"assortment flag\"},\n", "    {\n", "        \"name\": \"assortment_status\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"identify part of assortment\",\n", "    },\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\", \"description\": \"shelf inventory\"},\n", "    {\"name\": \"ttl_blocked_qty\", \"type\": \"integer\", \"description\": \"blocked inventory\"},\n", "    {\n", "        \"name\": \"net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"after blocked inventory\",\n", "    },\n", "    {\"name\": \"pending_putaway\", \"type\": \"integer\", \"description\": \"pending putaway\"},\n", "    {\"name\": \"open_sto_qty\", \"type\": \"integer\", \"description\": \"open STO quantity\"},\n", "    {\n", "        \"name\": \"ttl_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total PO raised quantity\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_grn_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total GRN quantity\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total Open PO quantity\",\n", "    },\n", "    {\"name\": \"min_quantity\", \"type\": \"integer\", \"description\": \"min quantity\"},\n", "    {\"name\": \"max_quantity\", \"type\": \"integer\", \"description\": \"max quantoty\"},\n", "    {\n", "        \"name\": \"today_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today sold quantity\",\n", "    },\n", "    {\"name\": \"today_sales_value\", \"type\": \"integer\", \"description\": \"today sold value\"},\n", "    {\"name\": \"today_carts\", \"type\": \"integer\", \"description\": \"today carts\"},\n", "    {\n", "        \"name\": \"yesterday_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday sold quantity\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_sales_value\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday sold value\",\n", "    },\n", "    {\"name\": \"yesterday_carts\", \"type\": \"integer\", \"description\": \"yesterday carts\"},\n", "    {\n", "        \"name\": \"l7days_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days sold quantity\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_sales_value\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days sold value\",\n", "    },\n", "    {\"name\": \"l7days_carts\", \"type\": \"integer\", \"description\": \"last 7 days carts\"},\n", "    {\"name\": \"ttl_sales_qty\", \"type\": \"integer\", \"description\": \"total sales quantity\"},\n", "    {\"name\": \"ttl_sales_value\", \"type\": \"integer\", \"description\": \"total sales value\"},\n", "    {\"name\": \"ttl_carts\", \"type\": \"integer\", \"description\": \"total carts\"},\n", "    {\n", "        \"name\": \"today_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"today dump quantity\",\n", "    },\n", "    {\"name\": \"today_dump_value\", \"type\": \"float\", \"description\": \"today dump value\"},\n", "    {\n", "        \"name\": \"yesterday_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"yesterday dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"yesterday dump value\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"last 7 days dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"last 7 day dump value\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total dump quantity\",\n", "    },\n", "    {\"name\": \"ttl_dump_value\", \"type\": \"float\", \"description\": \"total dump value\"},\n", "    {\n", "        \"name\": \"t_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend hot outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend inv outlet id\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"backend facility id\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\n", "        \"name\": \"be_actual_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend shelf inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend blocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend after blocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend pending putaway\",\n", "    },\n", "    {\n", "        \"name\": \"be_open_sto_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend open STO quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total PO raised quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_grn_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total GRN quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total Open PO quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend today dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend yesterday dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend yesterday dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend last 7 days dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend last 7 day dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_dump_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend total dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend total dump value\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "328864e0-8a12-41e0-83d6-c91f2871c03f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"party_event_metrics_reporting\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"city_name\"],\n", "    \"incremental_key\": \"date\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Reporting metrics for Events\",\n", "}\n", "pb.to_redshift(final, **kwargs)"]}, {"cell_type": "markdown", "id": "775d042c-fc08-49b9-a5d6-44dc96a80044", "metadata": {"tags": []}, "source": ["# p_type summary"]}, {"cell_type": "code", "execution_count": null, "id": "0834ed8d-7086-47bb-af16-c6b0d7fd205a", "metadata": {}, "outputs": [], "source": ["def p_type_summary():\n", "    p_type_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.party_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, p_type,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "            \n", "            \n", "                from base\n", "\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    city_view as\n", "        (select city_name, event_flag, assortment_type, p_type, \n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from city_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end, \n", "                    event_flag, assortment_type, p_type\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(p_type_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "p_type_summary = p_type_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "dda56732-156a-40bd-bc4a-09467ed3e312", "metadata": {}, "outputs": [], "source": ["p_type_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9a39e3e8-7804-4877-b5d3-dbe9e51975d4", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(p_type_summary, \"1IViNSyTbI7z_isnrQXPHy9prD5V5pxQ2z5bp1spb1Tg\", \"city_p_type_raw\")"]}, {"cell_type": "markdown", "id": "ea8cb14c-e3e7-4c3a-9283-bbb93e056dd2", "metadata": {"tags": []}, "source": ["# store wise summary"]}, {"cell_type": "code", "execution_count": null, "id": "e25fc2e4-b09e-46a3-a4bb-1cdef7ac1fe1", "metadata": {}, "outputs": [], "source": ["def store_summary():\n", "    store_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.party_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, \n", "            facility_id as fe_facility_id, be_facility_id, facility_name as fe_facility_name, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    facility_view as\n", "        (select city_name, event_flag, assortment_type,\n", "            facility_id as fe_facility_id, be_facility_id, facility_name as fe_facility_name, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from facility_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end, \n", "                    event_flag, assortment_type, fe_facility_id, be_facility_id\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(store_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "store_summary = store_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "3d3a16f2-c8f1-4e69-87c8-ba193f6fba9e", "metadata": {}, "outputs": [], "source": ["store_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "caf34bef-690f-4972-8ce4-b6ef38ace735", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(store_summary, \"1IViNSyTbI7z_isnrQXPHy9prD5V5pxQ2z5bp1spb1Tg\", \"store_wise_raw\")"]}, {"cell_type": "markdown", "id": "22242185-ac79-482b-93bf-c0be169e7dd9", "metadata": {}, "source": ["# backend p_type summary"]}, {"cell_type": "code", "execution_count": null, "id": "0e575c73-02a1-44db-98f3-21d544e0203d", "metadata": {}, "outputs": [], "source": ["def be_p_type_summary():\n", "    be_p_type_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.party_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select event_flag, assortment_type, \n", "            0 as be_facility_id, 'All' as be_facility_name, p_type,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    backend_view as\n", "        (select event_flag, assortment_type, \n", "            be_facility_id, be_facility_name, p_type,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from backend_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when be_facility_name = 'All' then 'A' end,\n", "                        event_flag, assortment_type, p_type\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(be_p_type_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "be_p_type_summary = be_p_type_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "c3e2b190-6230-46e3-988e-cc2d5da3bf75", "metadata": {}, "outputs": [], "source": ["be_p_type_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b4b80be9-a8c8-466c-98bd-a18c314e0653", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(be_p_type_summary, \"1IViNSyTbI7z_isnrQXPHy9prD5V5pxQ2z5bp1spb1Tg\", \"be_p_type_raw\")"]}, {"cell_type": "markdown", "id": "83fe8a59-27c6-4d4e-8bac-4efe8e8aa4b9", "metadata": {"tags": []}, "source": ["# backend store wise summary"]}, {"cell_type": "code", "execution_count": null, "id": "38b8df48-db66-4e16-9582-69f12b6fee51", "metadata": {}, "outputs": [], "source": ["def be_store_summary():\n", "    be_store_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.party_event_metrics_reporting\n", "        ),\n", "\n", "    be_facility_view as\n", "        (select city_name, event_flag, assortment_type,\n", "            facility_id as fe_facility_id, be_facility_id, facility_name as fe_facility_name, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        )\n", "\n", "            select * from be_facility_view \n", "                order by \n", "                    case when be_facility_name = 'All' then 'A' else be_facility_name end, \n", "                    event_flag, assortment_type, fe_facility_id\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(be_store_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "be_store_summary = be_store_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "e511da42-3e6b-474f-bac2-5867503357c2", "metadata": {}, "outputs": [], "source": ["be_store_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "05153150-06ca-4738-9e74-72c232582ae7", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    be_store_summary,\n", "    \"1IViNSyTbI7z_isnrQXPHy9prD5V5pxQ2z5bp1spb1Tg\",\n", "    \"be_store_wise_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "afc81be1-1b5e-4c1a-a084-844df29166c4", "metadata": {}, "source": ["# city backend summary"]}, {"cell_type": "code", "execution_count": null, "id": "96506163-75a9-415c-9658-ead41b169a0b", "metadata": {}, "outputs": [], "source": ["def city_be_summary():\n", "    city_be_summary = \"\"\"\n", "    \n", "    with\n", "    base as\n", "        (select *,\n", "            row_number() over (partition by item_id, be_hot_outlet_id order by item_id) as be_item_count\n", "            from metrics.party_event_metrics_reporting\n", "        ),\n", "\n", "    total_view as\n", "        (select 'All' as city_name, event_flag, assortment_type, \n", "            be_facility_id, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    backend_view as\n", "        (select city_name, event_flag, assortment_type, \n", "            be_facility_id, be_facility_name,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 \n", "                        when be_net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as after_be_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1 else 0 end)*1.00/count(item_id) as availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1 else 0 end)*1.00/count(item_id) as after_pp_availability,\n", "\n", "            sum(case when net_inventory > 0 then 1\n", "                when pending_putaway > 0 then 1\n", "                    when open_sto_qty > 0 then 1 else 0 end)*1.00/count(item_id) as after_intranist_availability,\n", "\n", "            count(item_id) as skus_count,\n", "            sum(case when status = 1 then 1 else 0 end) as active_skus_count,\n", "            sum(case when be_hot_outlet_id > 0 then 1 else 0 end) as tea_tagged_count,\n", "            sum(case when be_hot_outlet_id = 0 then 1 else 0 end) as non_tea_count,\n", "\n", "            sum(min_quantity) as min_quantity,\n", "            sum(max_quantity) as max_quantity,\n", "\n", "            sum(net_inventory) as ds_inv,\n", "            sum(pending_putaway) as ds_pending_putaway_inv,\n", "            sum(open_sto_qty) as ds_open_sto_qty,\n", "            sum(ttl_po_quantity) as ds_ttl_po_qty,\n", "            sum(ttl_grn_quantity) as ds_ttl_po_grn_quantity,\n", "            sum(ttl_open_po_quantity) as ds_ttl_open_po_quantity,\n", "\n", "            sum(case when be_item_count = 1 then be_net_inventory else 0 end) as be_inv,\n", "            sum(case when be_item_count = 1 then be_pending_putaway else 0 end) as be_pending_putaway_inv,\n", "            sum(case when be_item_count = 1 then be_open_sto_qty else 0 end) as be_open_sto_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_po_quantity else 0 end) as be_ttl_po_qty,\n", "            sum(case when be_item_count = 1 then be_ttl_grn_quantity else 0 end) as be_ttl_po_grn_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_open_po_quantity else 0 end) as be_ttl_open_po_quantity,\n", "\n", "            sum(today_sales_qty) as today_sales_qty,\n", "            sum(today_sales_value) as today_sales_value,\n", "            sum(today_carts) as today_carts,\n", "            sum(yesterday_sales_qty) as yesterday_sales_qty,\n", "            sum(yesterday_sales_value) as yesterday_sales_value,\n", "            sum(yesterday_carts) as yesterday_carts,\n", "            sum(l7days_sales_qty) as l7days_sales_qty,\n", "            sum(l7days_sales_value) as l7days_sales_value,\n", "            sum(l7days_carts) as l7days_carts,\n", "            sum(ttl_sales_qty) as ttl_sales_qty,\n", "            sum(ttl_sales_value) as ttl_sales_value,\n", "            sum(ttl_carts) as ttl_carts,\n", "\n", "            sum(today_dump_quantity) as today_dump_quantity,\n", "            sum(today_dump_value) as today_dump_value,\n", "            sum(yesterday_dump_quantity) as yesterday_dump_quantity,\n", "            sum(yesterday_dump_value) as yesterday_dump_value,\n", "            sum(l7days_dump_quantity) as l7days_dump_quantity,\n", "            sum(l7days_dump_value) as l7days_dump_value,\n", "            sum(ttl_dump_quantity) as ttl_dump_quantity,\n", "            sum(ttl_dump_value) as ttl_dump_value,\n", "\n", "            sum(case when be_item_count = 1 then be_today_dump_quantity else 0 end) as be_today_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_today_dump_value else 0 end) as be_today_dump_value,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_quantity else 0 end) as be_yesterday_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_yesterday_dump_value else 0 end) as be_yesterday_dump_value,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_quantity else 0 end) as be_l7days_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_l7days_dump_value else 0 end) as be_l7days_dump_value,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_quantity else 0 end) as be_ttl_dump_quantity,\n", "            sum(case when be_item_count = 1 then be_ttl_dump_value else 0 end) as be_ttl_dump_value,\n", "            \n", "            sum(t_v1_indent) as t_v1_indent,\n", "            sum(t_v2_indent) as t_v2_indent,\n", "            sum(t_total_drop_quantity) as t_total_drop_quantity,\n", "            sum(t_new_inward_drop) as t_new_inward_drop,\n", "            sum(t_new_storage_drop) as t_new_storage_drop,\n", "            sum(t_new_truck_load_drop) as t_new_truck_load_drop,\n", "            sum(t_new_picking_capacity_sku_drop) as t_new_picking_capacity_sku_drop,\n", "            sum(t_new_picking_capacity_quantity_drop) as t_new_picking_capacity_quantity_drop,\n", "            sum(t_new_loose_quantity_drop) as t_new_loose_quantity_drop,\n", "            \n", "            sum(t1_v1_indent) as t1_v1_indent,\n", "            sum(t1_v2_indent) as t1_v2_indent,\n", "            sum(t1_total_drop_quantity) as t1_total_drop_quantity,\n", "            sum(t1_new_inward_drop) as t1_new_inward_drop,\n", "            sum(t1_new_storage_drop) as t1_new_storage_drop,\n", "            sum(t1_new_truck_load_drop) as t1_new_truck_load_drop,\n", "            sum(t1_new_picking_capacity_sku_drop) as t1_new_picking_capacity_sku_drop,\n", "            sum(t1_new_picking_capacity_quantity_drop) as t1_new_picking_capacity_quantity_drop,\n", "            sum(t1_new_loose_quantity_drop) as t1_new_loose_quantity_drop,\n", "            \n", "            sum(t7_v1_indent) as t7_v1_indent,\n", "            sum(t7_v2_indent) as t7_v2_indent,\n", "            sum(t7_total_drop_quantity) as t7_total_drop_quantity,\n", "            sum(t7_new_inward_drop) as t7_new_inward_drop,\n", "            sum(t7_new_storage_drop) as t7_new_storage_drop,\n", "            sum(t7_new_truck_load_drop) as t7_new_truck_load_drop,\n", "            sum(t7_new_picking_capacity_sku_drop) as t7_new_picking_capacity_sku_drop,\n", "            sum(t7_new_picking_capacity_quantity_drop) as t7_new_picking_capacity_quantity_drop,\n", "            sum(t7_new_loose_quantity_drop) as t7_new_loose_quantity_drop\n", "\n", "                from base\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    final as\n", "        (select * from total_view\n", "\n", "            union\n", "\n", "                select * from backend_view\n", "        )\n", "\n", "            select * from final \n", "                order by \n", "                    case when city_name = 'All' then 'A' else city_name end,\n", "                        event_flag, assortment_type, be_facility_name\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(city_be_summary, redshift)\n", "\n", "\n", "start = time.time()\n", "city_be_summary = city_be_summary()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "b69fad6f-c6f6-4f77-ac90-e6eb34d038cc", "metadata": {}, "outputs": [], "source": ["city_be_summary.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8c3e2bb8-8a58-47eb-a4bf-57b1b7340846", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(city_be_summary, \"1IViNSyTbI7z_isnrQXPHy9prD5V5pxQ2z5bp1spb1Tg\", \"be_city_raw\")"]}, {"cell_type": "code", "execution_count": null, "id": "03ada922-7990-4cad-bfa6-80c8c22c2883", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5bca2034-cdca-419f-8fc6-18fd704ef4af", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}