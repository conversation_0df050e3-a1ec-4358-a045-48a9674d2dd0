{"cells": [{"cell_type": "code", "execution_count": null, "id": "cd77838b-72a2-420a-82fb-f8a2eedd36c3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "495d9d53-034e-4981-ada8-5d2a52238447", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "import warnings\n", "import math\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "20817218-3e9c-4daf-94fd-78a3035e8edd", "metadata": {}, "outputs": [], "source": ["CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "17ff8f10-8d58-4dc8-9d49-63950c03f8d5", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "35fb3674-575e-4a38-88b1-09ae764f8559", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "markdown", "id": "eccfc0c5-81f3-4c82-8989-812f0aa51f88", "metadata": {}, "source": ["# Assortment Input"]}, {"cell_type": "code", "execution_count": null, "id": "0b4675f6-6265-4f5a-aaa6-f8376a6375bf", "metadata": {}, "outputs": [], "source": ["temp = pb.from_sheets(\"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"assortment_input\")\n", "temp[\"active\"] = temp[\"active\"].astype(int)\n", "temp[\"item_id\"] = temp[\"item_id\"].astype(int)\n", "temp[\"go_live_date\"] = pd.to_datetime(temp[\"go_live_date\"])\n", "items = tuple(list(temp[\"item_id\"].unique()))\n", "temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "80114c14-7c14-4d82-929d-1929fd0b7247", "metadata": {}, "outputs": [], "source": ["temp.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b2387f8f-bdf5-4782-97d8-91faa93e0d63", "metadata": {}, "outputs": [], "source": ["len(items)"]}, {"cell_type": "markdown", "id": "195e2c25-dd88-4a2f-953c-39035d32251d", "metadata": {"tags": []}, "source": ["## First Go-Live Date for Filters"]}, {"cell_type": "code", "execution_count": null, "id": "244e1ac8-8b8b-418f-bfbd-027148f8fe11", "metadata": {}, "outputs": [], "source": ["first_live_date = temp.agg({\"go_live_date\": \"min\"}).reset_index().iloc[:, 1][0]\n", "first_live_date"]}, {"cell_type": "markdown", "id": "db2096cd-e8a4-4fb5-a5e1-f6e9f7ff53fe", "metadata": {}, "source": ["# Get ARS Active DS"]}, {"cell_type": "raw", "id": "17e64c9c-f24b-483d-a7b4-7f442e56dfce", "metadata": {}, "source": ["ars_active_ds_query = f\"\"\"\n", "SELECT\n", "      bfom.outlet_id\n", "    , pfom.outlet_name\n", "    , pfom.facility_id\n", "    , pfom.city_id\n", "    , pf.vendor_facility_identifier\n", "    , pf.name\n", "    , pf.internal_facility_identifier\n", "    , bfom.facility_id as backend_facility_id\n", "FROM\n", "    lake_po.bulk_facility_outlet_mapping as bfom\n", "    LEFT JOIN lake_po.physical_facility_outlet_mapping as pfom\n", "    ON pfom.outlet_id = bfom.outlet_id\n", "    LEFT JOIN lake_po.physical_facility as pf\n", "    ON pf.facility_id = pfom.facility_id\n", "WHERE\n", "    pfom.active = 1 AND\n", "    pfom.ars_active = 1 AND\n", "    bfom.active = 1\"\"\"\n", "ars_active_ds_df = read_sql_query(ars_active_ds_query, CON_REDSHIFT)\n", "ars_active_ds_df = ars_active_ds_df[['outlet_id', 'facility_id', 'city_id', 'backend_facility_id']].drop_duplicates()\n", "\n", "city_mapping_query = f\"\"\"\n", "SELECT \n", "    distinct id as city_id, name as city\n", "FROM\n", "    lake_retail.console_location\"\"\"\n", "city_map_df = read_sql_query(city_mapping_query, CON_REDSHIFT)\n", "\n", "ars_active_ds_df = pd.merge(ars_active_ds_df, city_map_df, on = ['city_id'], how = 'left')\n", "ars_active_ds_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "0ede0a7d-722f-4d6f-b0fd-29f7d608082c", "metadata": {}, "outputs": [], "source": ["ars_active_ds_query = f\"\"\"\n", "SELECT \n", "    distinct\n", "        pfom.facility_id,\n", "        pfom.city_id\n", "FROM\n", "    lake_view_po.physical_facility_outlet_mapping as pfom\n", "Inner join\n", "    lake_view_retail.console_outlet co on co.facility_id = pfom.facility_id and business_type_id = 7\n", "WHERE\n", "    pfom.active = 1 AND\n", "    pfom.ars_active = 1\"\"\"\n", "ars_active_ds_df = read_sql_query(ars_active_ds_query, CON_PRESTO)\n", "ars_active_ds_df = ars_active_ds_df[[\"facility_id\", \"city_id\"]].drop_duplicates()\n", "\n", "city_mapping_query = f\"\"\"\n", "SELECT \n", "    distinct id as city_id, name as city\n", "FROM\n", "    lake_view_retail.console_location\"\"\"\n", "city_map_df = read_sql_query(city_mapping_query, CON_PRESTO)\n", "\n", "ars_active_ds_df = pd.merge(ars_active_ds_df, city_map_df, on=[\"city_id\"], how=\"left\")\n", "ars_active_ds_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e2f287d4-7938-4888-a38e-60c1d8874301", "metadata": {}, "outputs": [], "source": ["# ars_active_ds_df[ars_active_ds_df['city']=='Jaipur']"]}, {"cell_type": "markdown", "id": "60eebc3f-19a7-4e2e-b811-7ff01943d8ca", "metadata": {}, "source": ["# Set up Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "6f65dec4-9326-450c-aa24-720883426115", "metadata": {}, "outputs": [], "source": ["base_df = pd.merge(temp, ars_active_ds_df, on=[\"city\"], how=\"left\")\n", "base_df.head(1)"]}, {"cell_type": "markdown", "id": "ef76e88e-682b-4795-a247-291d7d185b85", "metadata": {}, "source": ["# ARS run id"]}, {"cell_type": "code", "execution_count": null, "id": "52095d0a-9f53-4548-9b67-140949afb4fa", "metadata": {}, "outputs": [], "source": ["def ars_run_id():\n", "    ars_run_id = f\"\"\"\n", "    \n", "    with\n", "    ars_run_id as\n", "        (select \n", "            extract(hour from (started_at + interval '330' minute)) as hour_,\n", "            id,\n", "            run_id,\n", "            outlet_id,\n", "            run_date,\n", "            success,\n", "            (started_at + interval '330' minute) as started_at,\n", "            (completed_at + interval '330' minute) as completed_at,\n", "            details,\n", "            facility_id,\n", "            is_simulation,\n", "            simulation_params,\n", "            extra,\n", "            json_format(json_extract(extra,'$.auto_sto_outlets')) AS ars_run,\n", "            replace(json_extract_scalar(simulation_params,'$.run'), '\"', '') as ars_run_flag,\n", "            json_extract_scalar(simulation_params, '$.manual') as manual_run,\n", "            replace(json_extract_scalar(simulation_params, '$.mode'),'\"','') as ars_mode\n", "\n", "                from lake_view_ars.job_run\n", "\n", "                    where (completed_at + interval '330' minute) >= cast(current_date as timestamp)-interval '10' day\n", "                        and json_format(json_extract(replace(replace(extra, '[', ''), ']', ''),'$.auto_sto_outlets')) is not null\n", "        ),\n", "\n", "    ars_frontend_outlets as\n", "        (select date(completed_at) as date_, hour_, facility_id as be_facility_id, cast(replace(replace(split_b, '[', ''), ']', '') as int) as fe_outlet_id, run_id\n", "            from ars_run_id\n", "                cross join unnest(split(ars_run,',')) AS t (split_b)\n", "\n", "                    where ars_run is not null\n", "                        -- lower(ars_mode) in ('perishable') and \n", "        ),\n", "\n", "    final_ars_run_id as\n", "        (select afo.date_, afo.hour_, afo.be_facility_id as backend_facility_id, afo.fe_outlet_id, rco.facility_id as frontend_facility_id, afo.run_id\n", "\n", "            from ars_frontend_outlets afo\n", "\n", "                join lake_view_retail.console_outlet rco on rco.id = afo.fe_outlet_id\n", "        )\n", "\n", "            select * from final_ars_run_id   \n", "            \n", "    \"\"\"\n", "    return pd.read_sql_query(ars_run_id, CON_PRESTO)\n", "\n", "\n", "start = time.time()\n", "ars_run_id = ars_run_id()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "f5bf99b0-acb4-4b2a-ab81-d01bb128dc51", "metadata": {}, "outputs": [], "source": ["ars_run_id.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "262da766-cc4d-4e2d-9e85-4fd2816bd4b3", "metadata": {}, "outputs": [], "source": ["run_id_list = list(ars_run_id[\"run_id\"].unique())\n", "run_id_list = tuple(run_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "cbf3e57b-9502-466f-a1c8-9429a1f762f2", "metadata": {}, "outputs": [], "source": ["len(run_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "a1e3d924-be00-4469-b9d9-18528b7d5093", "metadata": {}, "outputs": [], "source": ["min_live_date = ars_run_id.agg({\"date_\": \"min\"}).reset_index().iloc[:, 1][0]\n", "min_live_date"]}, {"cell_type": "markdown", "id": "72d2da6d-69e6-4132-90ea-465c8bcf89c8", "metadata": {}, "source": ["# truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "68163ec0-1cce-4bdf-9079-12be4714c087", "metadata": {}, "outputs": [], "source": ["def truncation_details():\n", "    truncation_details = f\"\"\"\n", "    \n", "    with\n", "    truncation_details as\n", "        (select backend_outlet_id as be_outlet_id, frontend_outlet_id as fe_outlet_id, item_id, sto_quantity as v1_indent,\n", "            sto_quantity_post_truncation_in_case as v2_indent, run_id,\n", "            json_extract_scalar(quantity_drops, '$.inward_drop') as inward_drop,\n", "            json_extract_scalar(quantity_drops, '$.storage_drop') as storage_drop,\n", "            json_extract_scalar(quantity_drops, '$.truck_load_drop') as truck_load_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_sku_drop') as picking_capacity_sku_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_quantity_drop') as picking_capacity_quantity_drop,\n", "            (sto_quantity_post_truncation - sto_quantity_post_truncation_in_case) as loose_quantity_drop\n", "                from lake_view_ars.transfers_optimization_results_v2\n", "                    where cast(insert_ds_ist as date) >= cast('{min_live_date}' as date)\n", "        )\n", "\n", "            select be_outlet_id, fe_outlet_id, item_id, cast(v1_indent as int) as v1_indent, cast(v2_indent as int) as v2_indent, run_id,\n", "                inward_drop, storage_drop, truck_load_drop, picking_capacity_sku_drop, picking_capacity_quantity_drop, loose_quantity_drop\n", "\n", "                    from truncation_details\n", "\n", "                        where run_id in {run_id_list} and item_id in {items}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(truncation_details, CON_PRESTO)\n", "\n", "\n", "start = time.time()\n", "truncation_details = truncation_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "401e6679-020a-42ed-a134-46f67f11bff3", "metadata": {}, "outputs": [], "source": ["truncation_details.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "7248f3b2-07bc-4ad9-92b5-8153bba60db6", "metadata": {}, "outputs": [], "source": ["truncation_details[\"v1_indent\"] = truncation_details[\"v1_indent\"].fillna(0).astype(int)\n", "truncation_details[\"v2_indent\"] = truncation_details[\"v2_indent\"].fillna(0).astype(int)\n", "truncation_details[\"inward_drop\"] = truncation_details[\"inward_drop\"].fillna(0).astype(int)\n", "truncation_details[\"storage_drop\"] = truncation_details[\"storage_drop\"].fillna(0).astype(int)\n", "truncation_details[\"truck_load_drop\"] = truncation_details[\"truck_load_drop\"].fillna(0).astype(int)\n", "truncation_details[\"picking_capacity_sku_drop\"] = (\n", "    truncation_details[\"picking_capacity_sku_drop\"].fillna(0).astype(int)\n", ")\n", "truncation_details[\"picking_capacity_quantity_drop\"] = (\n", "    truncation_details[\"picking_capacity_quantity_drop\"].fillna(0).astype(int)\n", ")\n", "truncation_details[\"loose_quantity_drop\"] = (\n", "    truncation_details[\"loose_quantity_drop\"].fillna(0).astype(int)\n", ")\n", "\n", "adding_run_id = pd.merge(truncation_details, ars_run_id, on=[\"run_id\", \"fe_outlet_id\"], how=\"inner\")\n", "\n", "adding_run_id[\"date_\"] = pd.to_datetime(adding_run_id[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "afe79143-7ee0-4a42-be11-58ef2a5ca03c", "metadata": {}, "outputs": [], "source": ["adding_run_id.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d59714c0-7de2-4e6e-bc99-36afeee1399b", "metadata": {}, "outputs": [], "source": ["new_truncation_details = adding_run_id.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "f546f260-ec88-4c0e-adca-7b7c3a152260", "metadata": {}, "outputs": [], "source": ["new_truncation_details[\"new_inward_drop\"] = np.where(\n", "    (new_truncation_details[\"inward_drop\"] > 0)\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"inward_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_storage_drop\"] = np.where(\n", "    (new_truncation_details[\"storage_drop\"] > 0)\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"storage_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_truck_load_drop\"] = np.where(\n", "    (new_truncation_details[\"truck_load_drop\"] > 0)\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"truck_load_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_sku_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_sku_drop\"] > 0)\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_sku_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_quantity_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_loose_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"loose_quantity_drop\"] > 0)\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"loose_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"total_drop_quantity\"] = (\n", "    new_truncation_details[\"new_inward_drop\"]\n", "    + new_truncation_details[\"new_storage_drop\"]\n", "    + new_truncation_details[\"new_truck_load_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_sku_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_quantity_drop\"]\n", "    + new_truncation_details[\"new_loose_quantity_drop\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5d8a35b8-9cd9-4c74-9c90-bfba93e0a617", "metadata": {}, "outputs": [], "source": ["new_truncation_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "38955824-e3ab-4a04-aa62-18ee00b3f63b", "metadata": {}, "outputs": [], "source": ["new_truncation_details.shape"]}, {"cell_type": "code", "execution_count": null, "id": "318dc20f-168b-4e53-8fc3-9328ad90af1d", "metadata": {}, "outputs": [], "source": ["new_truncation_details = new_truncation_details[\n", "    [\n", "        \"date_\",\n", "        \"run_id\",\n", "        \"backend_facility_id\",\n", "        \"be_outlet_id\",\n", "        \"frontend_facility_id\",\n", "        \"fe_outlet_id\",\n", "        \"item_id\",\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "]\n", "\n", "\n", "new_truncation_details.head()"]}, {"cell_type": "markdown", "id": "bda4e883-0a6f-4d7e-a2e6-16da83bf89c3", "metadata": {}, "source": ["# frontend truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "3f6da6ab-ca48-4b6b-a0ab-f75b253e3813", "metadata": {}, "outputs": [], "source": ["frontend_current_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_current_date_truncation = frontend_current_date_truncation[\n", "    frontend_current_date_truncation[\"date_\"] == datetime.today().strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_current_date_truncation = (\n", "    frontend_current_date_truncation.groupby(\n", "        [\n", "            \"backend_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"frontend_facility_id\",\n", "            \"fe_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t_v1_indent\",\n", "        \"v2_indent\": \"t_v2_indent\",\n", "        \"new_inward_drop\": \"t_new_inward_drop\",\n", "        \"new_storage_drop\": \"t_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_1_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_1_date_truncation = frontend_l_1_date_truncation[\n", "    frontend_l_1_date_truncation[\"date_\"]\n", "    == (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_l_1_date_truncation = (\n", "    frontend_l_1_date_truncation.groupby(\n", "        [\n", "            \"backend_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"frontend_facility_id\",\n", "            \"fe_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t1_v1_indent\",\n", "        \"v2_indent\": \"t1_v2_indent\",\n", "        \"new_inward_drop\": \"t1_new_inward_drop\",\n", "        \"new_storage_drop\": \"t1_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t1_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t1_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t1_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_7_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_7_date_truncation = frontend_l_7_date_truncation[\n", "    (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        >= (datetime.today() - pd.DateOffset(days=7)).strftime(\"%Y-%m-%d\")\n", "    )\n", "    & (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        <= (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "    )\n", "]\n", "\n", "frontend_l_7_date_truncation = (\n", "    frontend_l_7_date_truncation.groupby(\n", "        [\n", "            \"backend_facility_id\",\n", "            \"be_outlet_id\",\n", "            \"frontend_facility_id\",\n", "            \"fe_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t7_v1_indent\",\n", "        \"v2_indent\": \"t7_v2_indent\",\n", "        \"new_inward_drop\": \"t7_new_inward_drop\",\n", "        \"new_storage_drop\": \"t7_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t7_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t7_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t7_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "new_truncation_details.shape, frontend_current_date_truncation.shape, frontend_l_1_date_truncation.shape, frontend_l_7_date_truncation.shape"]}, {"cell_type": "markdown", "id": "89ef4e05-a3c6-4098-8b6c-856c117e1e37", "metadata": {}, "source": ["# Date time variables"]}, {"cell_type": "code", "execution_count": null, "id": "33d0fd4e-6111-4ec2-9fc8-f24979026498", "metadata": {}, "outputs": [], "source": ["lag = 3\n", "initial_date = date.today() - <PERSON><PERSON>ta(days=lag)\n", "final_date = date.today()\n", "\n", "print(final_date, initial_date)\n", "\n", "date_list = []\n", "for i in range(0, lag):\n", "    date_list.append((initial_date + timedelta(days=i)))\n", "date_list"]}, {"cell_type": "code", "execution_count": null, "id": "65d3def3-50bc-45de-991f-c165d1fb20cb", "metadata": {}, "outputs": [], "source": ["date_list = pd.DataFrame(date_list, columns=[\"date\"])\n", "date_list.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "46cd1cc4-fe3f-4266-a5c2-9cdca9ac0639", "metadata": {}, "outputs": [], "source": ["len(items)"]}, {"cell_type": "markdown", "id": "e0b92c0a-7d35-49a4-a341-3161d59fd5f6", "metadata": {}, "source": ["## Fetch Facility, Outlet Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "894a74f5-8df3-4106-bd7a-8f0659c963ae", "metadata": {}, "outputs": [], "source": ["facility_mapping_query = f\"\"\"\n", "SELECT \n", "    distinct id as outlet_id, facility_id\n", "FROM\n", "    lake_view_retail.console_outlet\n", "WHERE\n", "    active = 1\"\"\"\n", "facility_mapping_df = read_sql_query(facility_mapping_query, CON_PRESTO)"]}, {"cell_type": "markdown", "id": "92f05e79-0ba8-4850-97b2-3a1136c6fe15", "metadata": {}, "source": ["## <PERSON><PERSON>, Ptype Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "0a7b9700-1e69-48c9-8a05-d0d488f6b1bd", "metadata": {}, "outputs": [], "source": ["product_attributes_query = f\"\"\"\n", "with categories AS\n", "(\n", "    SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "              WHEN C1.NAME = C.name THEN C2.name\n", "              ELSE C1.name\n", "          END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "    FROM lake_cms.gr_product P\n", "    INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "    AND PCM.IS_PRIMARY=TRUE\n", "    INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "    INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "    INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id\n", "),\n", "\n", "category_pre AS\n", "(\n", "    SELECT item_id,\n", "          product_id,\n", "          cat.l0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.product_type,\n", "          cat.manf\n", "    FROM lake_rpc.item_product_mapping rpc\n", "    INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "    AND rpc.offer_id IS NULL\n", "    AND rpc.item_id IS NOT NULL\n", "    AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl AS\n", "(\n", "    SELECT item_id,\n", "          max(l0) AS l0,\n", "          max(l1) AS l1,\n", "          max(l2) AS l2,\n", "          max(product_type) AS ptype,\n", "          max(manf) AS manf\n", "    FROM category_pre\n", "    GROUP BY 1\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    ptype_tbl\n", "\"\"\"\n", "product_attributes_df = read_sql_query(product_attributes_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "a5d9edfb-f5a1-43b2-99b5-e954c11ade35", "metadata": {}, "outputs": [], "source": ["product_attributes_df"]}, {"cell_type": "code", "execution_count": null, "id": "c71ace61-0800-419a-9c14-765d97aadbae", "metadata": {}, "outputs": [], "source": ["product_attributes_df[product_attributes_df[\"ptype\"] == \"Kanjak\"]"]}, {"cell_type": "code", "execution_count": null, "id": "be0c4cc1-3d9d-4b56-80d3-bbbcd24d8de9", "metadata": {}, "outputs": [], "source": ["facility_name_mapping_query = (\n", "    f\"\"\"SELECT id as facility_id, name as facility_name FROM lake_crates.facility\"\"\"\n", ")\n", "facility_name_mapping_df = read_sql_query(facility_name_mapping_query, CON_REDSHIFT)\n", "facility_name_mapping_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "5e074d14-01e1-4b16-be10-f416d9245763", "metadata": {}, "outputs": [], "source": ["item_category_details = f\"\"\"SELECT distinct item_id, l1\n", "FROM\n", "    lake_rpc.item_category_details\"\"\"\n", "item_category_details = read_sql_query(item_category_details, CON_REDSHIFT)\n", "item_category_details.head(1)"]}, {"cell_type": "markdown", "id": "ea0a3f51-556b-495e-ac62-502aec318575", "metadata": {}, "source": ["## Fetch TEA Tagging"]}, {"cell_type": "code", "execution_count": null, "id": "31a2518b-4b4f-46ec-a697-efda53953a47", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with tea_tagging as \n", "(\n", "    select \n", "        cl.name as city,\n", "        tea.item_id,\n", "        item.name as item_name,\n", "        co.facility_id as frontend_facility_id,\n", "        co2.name as frontend_facility_name,\n", "        co1.facility_id as backend_facility_id,\n", "        co3.name as backend_facility_name\n", "    from \n", "        lake_view_rpc.item_outlet_tag_mapping tea\n", "    left join  \n", "        lake_view_retail.console_outlet co on tea.outlet_id=co.id and co.business_type_id=7 and co.active=1\n", "    left join\n", "        lake_view_retail.console_location cl on co.tax_location_id = cl.id\n", "    left join\n", "        lake_view_retail.console_outlet co1 on cast(tea.tag_value as int)=cast(co1.id as int) and co1.business_type_id!=7 and co1.active=1\n", "    left join\n", "        lake_view_crates.facility co2 on co.facility_id=co2.id \n", "    left join\n", "        lake_view_crates.facility co3 on co1.facility_id=co3.id \n", "    left join\n", "        lake_view_rpc.item_details item  on tea.item_id=item.item_id\n", "    where tag_type_id=8 and tea.active=1 and tea.item_id in {items}\n", ")\n", "select * from tea_tagging\"\"\"\n", "\n", "tea_tag = read_sql_query(query, CON_PRESTO)\n", "tea_tag.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f633f7de-e005-4098-86cc-8523cfc5b231", "metadata": {}, "outputs": [], "source": ["tea_tag.shape"]}, {"cell_type": "code", "execution_count": null, "id": "73b0772a-36c6-4d24-a068-568abd44f858", "metadata": {}, "outputs": [], "source": ["## backend frontend mapping\n", "\n", "query = f\"\"\"\n", "    SELECT \n", "        distinct\n", "        pfom.facility_id as frontend_facility_id, \n", "        bfom.facility_id as backend_facility_id\n", "    FROM \n", "        lake_view_po.bulk_facility_outlet_mapping bfom\n", "    INNER JOIN \n", "    lake_view_po.physical_facility_outlet_mapping pfom ON bfom.outlet_id=pfom.outlet_id\n", "    LEFT JOIN lake_view_crates.facility cf on cf.id = bfom.facility_id \n", "    WHERE bfom.active = True\n", "    AND pfom.active = 1\n", "    AND pfom.ars_active = 1\"\"\"\n", "\n", "active_backends = read_sql_query(query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "95f1280b-0401-47e1-8bb3-7613c9930d8b", "metadata": {}, "outputs": [], "source": ["# active_backends[active_backends['backend_facility_id']==15.0]"]}, {"cell_type": "code", "execution_count": null, "id": "c63a289e-293d-48ce-8231-6e6abf4cd3b6", "metadata": {}, "outputs": [], "source": ["tea_tag = pd.merge(\n", "    tea_tag,\n", "    active_backends,\n", "    how=\"inner\",\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c6249182-8406-4e09-bc11-9b471dabf692", "metadata": {}, "outputs": [], "source": ["tea_tag.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e95d25ff-b60c-4056-8da3-d6cf7ccb8652", "metadata": {}, "outputs": [], "source": ["tea_tag = tea_tag[\n", "    (tea_tag[\"frontend_facility_id\"] != 1182) & (tea_tag[\"frontend_facility_id\"] != 1028)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0f5e524b-68bf-45b3-ae17-247b7e6d750c", "metadata": {}, "outputs": [], "source": ["# Validation\n", "# tea_tag[tea_tag['item_id'] == 10029774].groupby(['item_id', 'backend_facility_id']).agg({'frontend_facility_id':'nunique'}).reset_index()\n", "# tea_tag[['city']].drop_duplicates().sort_values(by = 'city')"]}, {"cell_type": "markdown", "id": "92eed016-38fc-4f81-ab11-97bc0af9abdc", "metadata": {}, "source": ["## <PERSON><PERSON> <PERSON>-<PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "bf125462-91a0-4360-aee6-c9f2416c416a", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"with min_max as\n", "(\n", "    select \n", "      distinct\n", "        item_id,\n", "        facility_id as frontend_facility_id,\n", "        min_quantity,\n", "        max_quantity\n", "    from \n", "        lake_ars.item_min_max_quantity mm\n", "    where item_id in {items}\n", ")\n", "select * from min_max\"\"\"\n", "\n", "min_max = read_sql_query(query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "bb339279-4db9-4945-b3c9-6ebc80e83fad", "metadata": {}, "source": ["## Fetch Sales Data"]}, {"cell_type": "code", "execution_count": null, "id": "495baf58-96cd-462f-a380-26f53728b68f", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", " with base as\n", "(\n", "    SELECT \n", "        date(od.created_at  + interval '330' Minute) as date, ancestor, outlet AS outlet_id, oi.item_id, sum(oi.quantity) as quantity\n", "    FROM \n", "        lake_view_ims.ims_order_details od\n", "    LEFT JOIN \n", "        lake_view_ims.ims_order_items oi on od.id = oi.order_details_id\n", "    WHERE \n", "        status_id in (1,2)\n", "    AND \n", "        oi.item_id in {items}\n", "    AND\n", "        od.insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    AND\n", "        oi.insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    GROUP BY \n", "        1,2,3,4\n", "),\n", "\n", "GMV_base as\n", "(\n", "    SELECT \n", "        cast(ancestor as var<PERSON>r) as ancestor, item_id, avg(GMV) as GMV\n", "    FROM \n", "    (\n", "        SELECT \n", "            oi.order_id as ancestor,\n", "            oi.product_id, pl.item_id, \n", "            (oi.selling_price*1.000/pl.multiplier) AS item_selling_price, \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS f_ordered_qty,\n", "            (oi.selling_price*1.000/pl.multiplier) * ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS GMV\n", "    \n", "        FROM \n", "            lake_view_oms_bifrost.oms_order_item oi\n", "        LEFT JOIN \n", "                (\n", "                    SELECT \n", "                        DISTINCT\n", "                            ipr.product_id, \n", "                            case \n", "                                when\n", "                                    ipr.item_id is null \n", "                                then ipom_0.item_id else ipr.item_id end as item_id,\n", "                            case \n", "                                when\n", "                                    ipr.item_id is not null\n", "                                then \n", "                                    COALESCE(ipom.multiplier,1)\n", "                                else\n", "                                    COALESCE(ipom_0.multiplier,1)\n", "                                end AS multiplier \n", "                    FROM \n", "                        lake_rpc.item_product_mapping ipr\n", "                    left join\n", "                        redshift.dwh.dim_item_product_offer_mapping ipom\n", "                    on\n", "                        ipr.product_id = ipom.product_id \n", "                    and\n", "                            ipr.item_id = ipom.item_id\n", "                    \n", "                    left join\n", "                        redshift.dwh.dim_item_product_offer_mapping ipom_0\n", "                    on\n", "                        ipr.product_id = ipom_0.product_id\n", "\n", "                ) pl ON pl.product_id = oi.product_id\n", "        WHERE \n", "            pl.item_id IS NOT NULL \n", "        AND \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) > 0\n", "        AND\n", "            insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    ) order_qty\n", "    WHERE\n", "        item_id in {items}\n", "    AND\n", "        ancestor in (select distinct cast(ancestor as bigint) as ancestor from base)\n", "    group by 1,2\n", ")\n", "\n", "\n", ", sales_with_GMV as \n", "(SELECT \n", "    b.date, \n", "    co.facility_id,\n", "    b.item_id, \n", "    count(distinct b.ancestor) as orders,\n", "    sum(quantity) as quantity,\n", "    sum(gmv) as gmv\n", "FROM base b\n", "LEFT JOIN\n", "    GMV_base gb \n", "ON\n", "    b.ancestor = gb.ancestor and  b.item_id = gb.item_id\n", "left join \n", "    lake_retail.console_outlet co on co.id = b.outlet_id and co.active = 1\n", "where  date >= cast('{first_live_date}' as timestamp)\n", "group by 1,2,3     \n", "),\n", "\n", "\n", "current_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as current_day_qty_sold, sum(gmv) as current_day_gmv, sum(orders) as current_day_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_1_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_1_qty_sold, sum(gmv) as t_1_gmv, sum(orders) as t_1_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date = date(current_timestamp + interval '330' minute) - interval '1' day --and date(getdate() + interval '330' Minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_7_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_7_qty_sold, sum(gmv) as t_7_gmv, sum(orders) as t_7_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute) - interval '7' day\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "till_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as qty_sold_till_date, sum(gmv) as gmv_till_date, sum(orders) as carts_till_date\n", "    FROM\n", "        sales_with_GMV\n", "    GROUP BY\n", "        1, 2\n", ")\n", "\n", "SELECT\n", "    td.facility_id as frontend_facility_id, td.item_id,\n", "    current_day_qty_sold, current_day_gmv, current_day_carts,\n", "    t_1_qty_sold, t_1_gmv, t_1_carts,\n", "    t_7_qty_sold, t_7_gmv, t_7_carts,\n", "    qty_sold_till_date, gmv_till_date, carts_till_date\n", "FROM\n", "    till_day td\n", "LEFT JOIN\n", "    current_day cd\n", "ON\n", "    cd.item_id = td.item_id and cd.facility_id = td.facility_id\n", "LEFT JOIN\n", "    t_1_day t1d\n", "ON\n", "    td.item_id = t1d.item_id and td.facility_id = t1d.facility_id\n", "LEFT JOIN\n", "    t_7_day t7d\n", "ON\n", "    td.item_id = t7d.item_id and td.facility_id = t7d.facility_id\n", "\"\"\"\n", "\n", "sales = read_sql_query(query, CON_PRESTO)\n", "## Change 29th"]}, {"cell_type": "raw", "id": "7220826c-fbbd-4750-bde6-4e62836cac28", "metadata": {"tags": []}, "source": ["query =f\"\"\"\n", " with base as\n", "(\n", "    SELECT \n", "        date(od.created_at  + interval '5.5 Hours') as date, ancestor, outlet AS outlet_id, oi.item_id, sum(oi.quantity) as quantity\n", "    FROM \n", "        lake_ims.ims_order_details od\n", "    LEFT JOIN \n", "        lake_ims.ims_order_items oi on od.id = oi.order_details_id\n", "    WHERE \n", "        status_id in (1,2)\n", "    AND \n", "        oi.item_id in {items}\n", "    AND\n", "        od.created_at >= date('{first_live_date}') - interval '1 days'\n", "    GROUP BY \n", "        1,2,3,4\n", "),\n", "\n", "GMV_base as\n", "(\n", "    SELECT \n", "        cast(ancestor as var<PERSON>r) as ancestor, item_id, avg(GMV) as GMV\n", "    FROM \n", "    (\n", "        SELECT \n", "            oi.order_id as ancestor,\n", "            oi.product_id, pl.item_id, \n", "            (oi.selling_price*1.000/pl.multiplier) AS item_selling_price, \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS f_ordered_qty,\n", "            (oi.selling_price*1.000/pl.multiplier) * ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS GMV\n", "    \n", "        FROM \n", "            lake_oms_bifrost.oms_order_item oi\n", "        LEFT JOIN \n", "                (\n", "                    SELECT \n", "                        DISTINCT\n", "                            ipr.product_id, \n", "                            case \n", "                                when\n", "                                    ipr.item_id is null \n", "                                then ipom_0.item_id else ipr.item_id end as item_id,\n", "                            case \n", "                                when\n", "                                    ipr.item_id is not null\n", "                                then \n", "                                    COALESCE(ipom.multiplier,1)\n", "                                else\n", "                                    COALESCE(ipom_0.multiplier,1)\n", "                                end AS multiplier \n", "                    FROM \n", "                        lake_rpc.item_product_mapping ipr\n", "                    left join\n", "                        dwh.dim_item_product_offer_mapping ipom\n", "                    on\n", "                        ipr.product_id = ipom.product_id \n", "                    and\n", "                            ipr.item_id = ipom.item_id\n", "                    \n", "                    left join\n", "                        dwh.dim_item_product_offer_mapping ipom_0\n", "                    on\n", "                        ipr.product_id = ipom_0.product_id\n", "\n", "                ) pl ON pl.product_id = oi.product_id\n", "        WHERE \n", "            pl.item_id IS NOT NULL \n", "        AND \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) > 0\n", "        AND\n", "            install_ts >= date('{first_live_date}') - interval '1 days'\n", "    ) order_qty\n", "    WHERE\n", "        item_id in {items}\n", "    AND\n", "        ancestor in (select distinct ancestor from base)\n", "    group by 1,2\n", ")\n", "\n", "\n", ", sales_with_GMV as \n", "(SELECT \n", "    b.date, \n", "    co.facility_id,\n", "    b.item_id, \n", "    count(distinct b.ancestor) as orders,\n", "    sum(quantity) as quantity,\n", "    sum(gmv) as gmv\n", "FROM base b\n", "LEFT JOIN\n", "    GMV_base gb \n", "ON\n", "    b.ancestor = gb.ancestor and  b.item_id = gb.item_id\n", "left join \n", "    lake_retail.console_outlet co on co.id = b.outlet_id and co.active = 1\n", "where  date >= date('{first_live_date}') --between ('{initial_date}' || ' 00:00:00')::timestamp and ('{final_date}' || ' 23:59:59')::timestamp\n", "group by 1,2,3     \n", "),\n", "\n", "\n", "current_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as current_day_qty_sold, sum(gmv) as current_day_gmv, sum(orders) as current_day_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(getdate() + interval '5.5 hours')\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_1_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_1_qty_sold, sum(gmv) as t_1_gmv, sum(orders) as t_1_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date = date(getdate() + interval '5.5 hours') - interval '1 days' --and date(getdate() + interval '5.5 hours')\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_7_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_7_qty_sold, sum(gmv) as t_7_gmv, sum(orders) as t_7_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(getdate() + interval '5.5 hours') - interval '7 days'\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "till_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as qty_sold_till_date, sum(gmv) as gmv_till_date, sum(orders) as carts_till_date\n", "    FROM\n", "        sales_with_GMV\n", "    GROUP BY\n", "        1, 2\n", ")\n", "\n", "SELECT\n", "    td.facility_id as frontend_facility_id, td.item_id,\n", "    current_day_qty_sold, current_day_gmv, current_day_carts,\n", "    t_1_qty_sold, t_1_gmv, t_1_carts,\n", "    t_7_qty_sold, t_7_gmv, t_7_carts,\n", "    qty_sold_till_date, gmv_till_date, carts_till_date\n", "FROM\n", "    till_day td\n", "LEFT JOIN\n", "    current_day cd\n", "ON\n", "    cd.item_id = td.item_id and cd.facility_id = td.facility_id\n", "LEFT JOIN\n", "    t_1_day t1d\n", "ON\n", "    cd.item_id = t1d.item_id and cd.facility_id = t1d.facility_id\n", "LEFT JOIN\n", "    t_7_day t7d\n", "ON\n", "    cd.item_id = t7d.item_id and cd.facility_id = t7d.facility_id\n", "\"\"\"\n", "\n", "sales=read_sql_query(query,CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "3436548e-0631-4272-879a-ea7349640887", "metadata": {}, "outputs": [], "source": ["sales[(sales.frontend_facility_id == 869) & (sales.item_id == 10042664)]"]}, {"cell_type": "markdown", "id": "72bbacec-29e8-44ac-a53b-7bd9031016ed", "metadata": {"tags": []}, "source": ["## <PERSON><PERSON> Dump"]}, {"cell_type": "code", "execution_count": null, "id": "f6008188-d371-4305-8990-da9149f2b4b4", "metadata": {}, "outputs": [], "source": ["final_date"]}, {"cell_type": "code", "execution_count": null, "id": "7274d606-d39d-4fed-b1fa-9176cf865a4d", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with base as \n", "    (\n", "    SELECT date_, hour_, outlet_id,  item_id, SUM(all_dump) as all_dump, SUM(all_dump_value) as all_dump_value\n", "    FROM (\n", "    SELECT date(a.created_at + interval '330' MINUTE) date_,\n", "           extract(hour from a.created_at + interval '330' MINUTE) as hour_, ibur_name, Reason,\n", "           a.item_id, a.outlet_id,\n", "           sum(b.delta) AS all_dump,\n", "           sum(b.delta*b.weighted_lp) AS all_dump_value,\n", "           sum(dump_expiry) AS dump_expiry,\n", "           sum(dump_nte) AS dump_nte,\n", "           sum(dump_damaged) AS dump_damaged,\n", "           sum(dump_rotten) AS dump_rotten,\n", "           sum(dump_nr) AS dump_nr,\n", "           sum(dump_others) AS dump_others\n", "    FROM\n", "      (SELECT *\n", "       FROM lake_view_ims.ims_item_inventory_log\n", "       WHERE delta < 0\n", "         AND inventory_log_id IS NOT NULL\n", "         AND insert_ds_ist between cast('2022-09-20' as varchar) and cast('{final_date}' as varchar)\n", "         ) a\n", "    INNER JOIN\n", "      (SELECT i.id,\n", "              i.inventory_update_type_id, \n", "              case \n", "                  when (ibur.name is null) and (i.inventory_update_type_id in (12)) then 'Bad Stock Expired Product' \n", "                  when (ibur.name is null) and (i.inventory_update_type_id in (64)) then 'Bad Stock Near Expiry Product' \n", "                  else ibur.name end as ibur_name,\n", "\n", "              CASE WHEN ((ibur.name is null) and (i.inventory_update_type_id in (12))) OR (ibur.name IN ('STO Expiry')) THEN 'Dump due to expiry'\n", "              WHEN ((ibur.name is null) and (i.inventory_update_type_id in (64))) OR (ibur.name IN ('STO Near Expiry')) THEN 'Dump due near to expiry'\n", "              WHEN i.inventory_update_type_id IN (11) AND ibur.name IN ('Packaging damage', 'In-Transit Damage', 'STO Damage (Source)', 'Rodent Damage', 'STO Damage (Transport)', 'Vendor damage') THEN 'Dump due to damage'\n", "              WHEN i.inventory_update_type_id IN (11) AND ibur.name IN ('Rotten') THEN 'Dump due to rotting'\n", "              WHEN i.inventory_update_type_id IN (11) AND ibur.name IN ('NR(Not Received)') THEN 'NR Dump'\n", "              ELSE 'Others' END AS Reason,\n", "\n", "              i.delta,\n", "              i.weighted_lp,\n", "\n", "              CASE WHEN ((ibur.name is null) and (i.inventory_update_type_id in (12))) OR (ibur.name IN ('STO Expiry')) THEN i.delta ELSE 0 END AS dump_expiry,\n", "              CASE WHEN ((ibur.name is null) and (i.inventory_update_type_id in (64))) OR (ibur.name IN ('STO Near Expiry')) THEN i.delta ELSE 0 END AS dump_nte,\n", "              CASE WHEN i.inventory_update_type_id IN (11) AND ibur.name IN ('Packaging damage', 'In-Transit Damage', 'STO Damage (Source)', 'Rodent Damage', 'STO Damage (Transport)', 'Vendor damage') THEN i.delta ELSE 0 END AS dump_damaged,\n", "              CASE WHEN i.inventory_update_type_id IN (11) AND ibur.name IN ('Rotten') THEN i.delta ELSE 0 END AS dump_rotten,\n", "              CASE WHEN i.inventory_update_type_id IN (11) AND ibur.name = 'NR(Not Received)' THEN i.delta ELSE 0 END AS dump_nr,\n", "              CASE WHEN i.inventory_update_type_id IN (11) AND ibur.name NOT IN ('NR(Not Received)', 'Rotten','Packaging damage', 'In-Transit Damage', 'STO Damage (Source)', 'Rodent Damage', 'STO Damage (Transport)', 'Vendor damage') THEN i.delta ELSE 0 END AS dump_others\n", "\n", "       FROM lake_view_ims.ims_inventory_log i\n", "       LEFT JOIN lake_view_ims.ims_bad_inventory_update_log ibil ON i.inventory_update_id = ibil.inventory_update_id\n", "       LEFT JOIN lake_ims.ims_bad_update_reason ibur ON ibil.reason_id = ibur.id\n", "\n", "       WHERE insert_ds_ist between cast('2022-09-20' as varchar) and cast('{final_date}' as varchar)\n", "         AND i.inventory_update_type_id IN (11, 12, 13, 64, 87, 88, 89)\n", "\n", "         ) b ON a.inventory_log_id = b.id\n", "\n", "    GROUP BY 1, 2, 3, 4, 5,6\n", "    )\n", "\n", "    GROUP BY 1,2,3,4\n", "),\n", "agg_base as \n", "(\n", "    select \n", "            date_ as date,\n", "            facility_id,\n", "            item_id,\n", "            sum(all_dump) as all_dump,\n", "            sum(all_dump_value) as all_dump_value\n", "    from\n", "        base b\n", "        inner join\n", "    lake_retail.console_outlet co on b.outlet_id=co.id and co.business_type_id=7\n", "        group by 1,2,3\n", "),\n", "\n", "current_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(all_dump) as current_day_dump, sum(all_dump_value) as current_day_dump_value\n", "    FROM\n", "        agg_base\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_1_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(all_dump) as t_1_dump, sum(all_dump_value) as t_1_dump_value\n", "    FROM\n", "        agg_base\n", "    WHERE\n", "        date = date(current_timestamp + interval '330' minute) - interval '1' day --and date(current_timestamp + interval '330' minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_7_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(all_dump) as t_7_dump, sum(all_dump_value) as t_7_dump_value\n", "    FROM\n", "        agg_base\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute) - interval '7' day\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "till_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(all_dump) as dump_till_date, sum(all_dump_value) as dump_value_till_date\n", "    FROM\n", "        agg_base\n", "    GROUP BY\n", "        1, 2\n", ")\n", "\n", "SELECT\n", "    td.facility_id as frontend_facility_id, td.item_id,\n", "    current_day_dump, current_day_dump_value,\n", "    t_1_dump, t_1_dump_value,\n", "    t_7_dump, t_7_dump_value,\n", "    dump_till_date, dump_value_till_date\n", "FROM\n", "    till_day td\n", "LEFT JOIN\n", "    current_day cd\n", "ON\n", "    cd.item_id = td.item_id and cd.facility_id = td.facility_id\n", "LEFT JOIN\n", "    t_1_day t1d\n", "ON\n", "    td.item_id = t1d.item_id and td.facility_id = t1d.facility_id\n", "LEFT JOIN\n", "    t_7_day t7d\n", "ON\n", "    td.item_id = t7d.item_id and td.facility_id = t7d.facility_id\n", "\"\"\"\n", "\n", "dump = read_sql_query(query, CON_PRESTO)\n", "# Change 29th"]}, {"cell_type": "markdown", "id": "10f7f92c-a29f-45e2-ba79-c0a0dd933ddf", "metadata": {}, "source": ["## Fetch Dark Store Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "dd61ce10-78d3-42e5-b478-117b66f830dd", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with DS_inventory  AS\n", "  (SELECT \n", "         distinct\n", "          a.item_id,\n", "          inv_outlet_id,\n", "          outlet_name,\n", "          facility_id,\n", "          facility_name,\n", "          case when (actual_quantity - blocked_quantity) < 0 then 0 else (actual_quantity - blocked_quantity) end as current_stock\n", "   FROM\n", "     (SELECT DISTINCT \n", "            a.item_id,\n", "            r.inv_outlet_id,\n", "            r.outlet_name,\n", "            r.facility_id,\n", "            cf.name AS facility_name,\n", "            (a.quantity) AS actual_quantity,\n", "            sum(CASE WHEN blocked_type IN (1,2,5) THEN b.quantity ELSE 0 END) AS blocked_quantity\n", "      FROM lake_view_ims.ims_item_inventory AS a\n", "      INNER JOIN\n", "        (select \n", "            om.outlet_id as hot_outlet_id, \n", "            om.outlet_name, \n", "            om.facility_id,\n", "            case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id\n", "         from lake_view_po.physical_facility_outlet_mapping om\n", "       left join (select id, tax_location_id,\n", "         case when id = 581 then 12 else business_type_id end as business_type_id\n", "       from lake_view_retail.console_outlet) rco on rco.id = om.outlet_id and rco.business_type_id=7\n", "       left join \n", "            (select distinct warehouse_id, cloud_store_id from lake_view_retail.warehouse_outlet_mapping \n", "                where active = 1) wom on wom.warehouse_id = om.outlet_id\n", "        where rco.business_type_id in (7) and om.outlet_id not in (0)\n", "        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "        and om.outlet_name not like '%%SSC%%'\n", "        and om.outlet_name not like '%%MODI%%'\n", "        and om.outlet_name not like '%%hot ff%%'\n", "        and om.outlet_name not like '%%CIA%%'\n", "        ) r ON a.outlet_id = r.inv_outlet_id\n", "      LEFT JOIN lake_view_ims.ims_item_blocked_inventory b ON a.outlet_id = b.outlet_id\n", "      AND a.item_id = b.item_id\n", "      LEFT JOIN lake_view_crates.facility cf ON r.facility_id = cf.id\n", "    where a.item_id in {items}\n", "      GROUP BY 1,\n", "               2,\n", "               3,4,5,6) a\n", "   )\n", "select * from DS_inventory\"\"\"\n", "\n", "DS_inventory = read_sql_query(query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "d06cc218-50cb-42a0-b8de-f27e3f6623f0", "metadata": {}, "outputs": [], "source": ["# temp[temp.item_id == 10116292]"]}, {"cell_type": "code", "execution_count": null, "id": "2a29c007-6367-4899-9202-ae5e080cb619", "metadata": {}, "outputs": [], "source": ["DS_inventory[(DS_inventory.item_id == 10116292)]"]}, {"cell_type": "markdown", "id": "3a9ba6e2-3a62-4fd1-ae81-b3b63ce7bc6f", "metadata": {}, "source": ["## Fetch Backend Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "7641b94f-a4e7-4fea-871c-4edd1cf3d1de", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with DS_inventory  AS\n", "  (SELECT \n", "         distinct\n", "          a.item_id,\n", "          inv_outlet_id,\n", "          outlet_name,\n", "          facility_id,\n", "          facility_name,\n", "          case when (actual_quantity - blocked_quantity) < 0 then 0 else (actual_quantity - blocked_quantity) end as current_stock\n", "   FROM\n", "     (SELECT DISTINCT \n", "            a.item_id,\n", "            r.inv_outlet_id,\n", "            r.outlet_name,\n", "            r.facility_id,\n", "            cf.name AS facility_name,\n", "            (a.quantity) AS actual_quantity,\n", "            sum(CASE WHEN blocked_type IN (1,2,5) THEN b.quantity ELSE 0 END) AS blocked_quantity\n", "      FROM lake_view_ims.ims_item_inventory AS a\n", "      INNER JOIN\n", "        (select \n", "            om.outlet_id as hot_outlet_id, \n", "            om.outlet_name, \n", "            om.facility_id,\n", "            case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id\n", "         from lake_view_po.physical_facility_outlet_mapping om\n", "       left join (select id, tax_location_id,\n", "         case when id = 581 then 12 else business_type_id end as business_type_id\n", "       from lake_view_retail.console_outlet) rco on rco.id = om.outlet_id\n", "       left join \n", "            (select distinct warehouse_id, cloud_store_id from lake_view_retail.warehouse_outlet_mapping \n", "                where active = 1) wom on wom.warehouse_id = om.outlet_id\n", "        where rco.business_type_id not in (7) and om.outlet_id not in (0)\n", "        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "        and om.outlet_name not like '%%SSC%%'\n", "        and om.outlet_name not like '%%MODI%%'\n", "        and om.outlet_name not like '%%hot ff%%'\n", "        and om.outlet_name not like '%%CIA%%'\n", "        ) r ON a.outlet_id = r.inv_outlet_id\n", "      LEFT JOIN lake_view_ims.ims_item_blocked_inventory b ON a.outlet_id = b.outlet_id\n", "      AND a.item_id = b.item_id\n", "      LEFT JOIN lake_view_crates.facility cf ON r.facility_id = cf.id\n", "    where a.item_id in {items}\n", "      GROUP BY 1,\n", "               2,\n", "               3,4,5,6) a\n", "   )\n", "select item_id, facility_id, facility_name, sum(current_stock) as current_stock from DS_inventory group by 1,2,3\"\"\"\n", "\n", "backend_inventory = read_sql_query(query, CON_PRESTO)\n", "\n", "## Change 29th"]}, {"cell_type": "code", "execution_count": null, "id": "bf149bef-d820-491d-b5f3-449c7cbe88e8", "metadata": {}, "outputs": [], "source": ["backend_inventory[(backend_inventory.item_id == 10116292)]"]}, {"cell_type": "markdown", "id": "2007655a-14f7-4dc0-ae8d-9f7824fc1a88", "metadata": {}, "source": ["### Inventory in Transit"]}, {"cell_type": "code", "execution_count": null, "id": "978f2408-5483-492e-874e-c4b486b7396b", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with sto_base as\n", "(\n", "    SELECT \n", "        distinct sto_id \n", "    FROM\n", "        metrics.esto_details\n", "    WHERE\n", "        item_id in {items}\n", "),\n", "\n", "base as\n", "(\n", "    select \n", "        ars_run_id, sto_id, sender_outlet_id, receiving_outlet_id, item_id, invoice_id,\n", "        \n", "        -- Creation Metrics\n", "        sto_created_at, sto_type, sto_state, expected_quantity, reserved_quantity,\n", "        \n", "        -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "        sto_invoice_created_at, billed_quantity, invoice_state,\n", "        \n", "        -- Dispatch Metrics\n", "        dispatch_time, sto_dispatched_at,\n", "        trip_started_at, trip_created_at,\n", "        \n", "        -- Arrival at DS Metrics\n", "        consignment_received_at,\n", "        \n", "        -- GRN Metrics\n", "        grn_started_at, -- Same as putaway_time in DS\n", "        putlist_creation_time,\n", "        putaway_time,\n", "        inwarded_quantity,\n", "        \n", "        -- Discrepancy Metrics\n", "        discrepancy_created_at, disc_qty\n", "        --dispatch_time = ARS Ideal Dispatch Time> Actual sto_dispatched_at\n", "    from \n", "        metrics.esto_details \n", "    where\n", "        sto_id in \n", "            (\n", "            select \n", "                distinct sto_id \n", "            from sto_base\n", "            )\n", "    and\n", "        item_id in {items}\n", "),\n", "\n", "\n", "fixing_missing_values_for_receiving_time as\n", "(\n", "    SELECT\n", "        distinct ars_run_id, receiving_outlet_id, consignment_received_at\n", "    FROM\n", "    (\n", "        SELECT \n", "            *\n", "        FROM\n", "            base\n", "        WHERE\n", "            consignment_received_at is not null\n", "    )\n", "    \n", "),\n", "\n", "fixing_missing_values_for_dispatch_time as\n", "(\n", "    SELECT\n", "        distinct ars_run_id, receiving_outlet_id, sto_dispatched_at\n", "    FROM\n", "    (\n", "        SELECT \n", "            *\n", "        FROM\n", "            base\n", "        WHERE\n", "            sto_dispatched_at is not null\n", "    )\n", "),\n", "\n", "\n", "fixing_missing_values_for_trip_start_time as\n", "(\n", "    SELECT\n", "        distinct ars_run_id, receiving_outlet_id, trip_started_at\n", "    FROM\n", "    (\n", "        SELECT \n", "            *\n", "        FROM\n", "            base\n", "        WHERE\n", "            trip_started_at is not null\n", "    )\n", "),\n", "\n", "\n", "map_missing_time_to_base as\n", "(\n", "    SELECT\n", "        b.*,\n", "        case \n", "            when (b.consignment_received_at is null) and (b.sto_invoice_created_at is not null) then fmvrt.consignment_received_at else b.consignment_received_at end as treated_consignment_received_at, -- When truck / STO is received at DS\n", "        \n", "        case \n", "            when (b.sto_dispatched_at is null) and (b.sto_invoice_created_at is not null) then fmvdt.sto_dispatched_at else b.sto_dispatched_at end as treated_sto_dispatched_at, -- When STO is scanned and loaded\n", "        \n", "        case \n", "            when (b.trip_started_at is null) and (b.sto_invoice_created_at is not null) then fmvtst.trip_started_at else b.trip_started_at end as treated_trip_started_at -- When truck has started from origin\n", "        \n", "    FROM\n", "        base b\n", "    LEFT JOIN\n", "        fixing_missing_values_for_receiving_time fmvrt\n", "    ON\n", "        b.ars_run_id = fmvrt.ars_run_id\n", "    and\n", "        b.receiving_outlet_id = fmvrt.receiving_outlet_id\n", "    \n", "    LEFT JOIN\n", "        fixing_missing_values_for_dispatch_time fmvdt\n", "    ON\n", "        b.ars_run_id = fmvdt.ars_run_id\n", "    and\n", "        b.receiving_outlet_id = fmvdt.receiving_outlet_id\n", "        \n", "    LEFT JOIN\n", "        fixing_missing_values_for_trip_start_time fmvtst\n", "    ON\n", "        b.ars_run_id = fmvtst.ars_run_id\n", "    and\n", "        b.receiving_outlet_id = fmvtst.receiving_outlet_id\n", "),\n", "\n", "treating_base_sto_dispatch as -- \n", "(\n", "    SELECT\n", "      *\n", "    FROM\n", "        map_missing_time_to_base\n", "    WHERE\n", "        treated_sto_dispatched_at is null\n", "    UNION\n", "    \n", "    \n", "    SELECT\n", "      *\n", "    FROM\n", "        map_missing_time_to_base\n", "    WHERE\n", "        (treated_sto_dispatched_at is not null)\n", "    AND\n", "        treated_sto_dispatched_at >= sto_invoice_created_at\n", "),\n", "\n", "treating_base_trip_start as -- \n", "(\n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_sto_dispatch\n", "    WHERE\n", "        treated_trip_started_at is null\n", "    UNION\n", "    \n", "    \n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_sto_dispatch\n", "    WHERE\n", "        (treated_trip_started_at is not null)\n", "    AND\n", "        treated_trip_started_at >= sto_invoice_created_at\n", "),\n", "\n", "treating_base_consignment_arrival as -- \n", "(\n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_trip_start\n", "    WHERE\n", "        treated_consignment_received_at is null\n", "    \n", "    UNION\n", "    \n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_trip_start\n", "    WHERE\n", "        (treated_consignment_received_at is not null)\n", "    AND\n", "        treated_consignment_received_at >= treated_trip_started_at\n", "),\n", "\n", "\n", "identify_correct_trip_details as\n", "\n", "(\n", "    SELECT\n", "        ars_run_id, sto_id, sender_outlet_id, receiving_outlet_id, item_id, sto_type, sto_state, invoice_id, invoice_state,\n", "        \n", "        -- quantity flow\n", "        expected_quantity, reserved_quantity, billed_quantity, inwarded_quantity, disc_qty,\n", "        \n", "        -- Creation Metrics\n", "        sto_created_at, \n", "        \n", "        -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "        sto_invoice_created_at, \n", "        \n", "        -- Dispatch Metrics\n", "        trip_started_at, trip_created_at, dispatch_time, sto_dispatched_at,\n", "        \n", "        -- Arrival at DS Metrics\n", "        consignment_received_at,\n", "        \n", "        -- GRN Metrics\n", "        grn_started_at, \n", "        putlist_creation_time,\n", "        putaway_time,\n", "        -- min(putlist_creation_time) as grn_start_time,\n", "        -- max(putaway_time) as grn_end_time,\n", "        \n", "        -- Discrepancy Metrics\n", "        discrepancy_created_at,\n", "        \n", "        min(treated_consignment_received_at) as treated_consignment_received_at, min(treated_trip_started_at) as treated_trip_started_at, min(treated_sto_dispatched_at) as treated_sto_dispatched_at\n", "    FROM\n", "        treating_base_consignment_arrival\n", "    GROUP BY\n", "        1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25\n", "\n", "\n", "),\n", "\n", "\n", "-- Base Merge to retain cases where trip details are missing\n", "getting_summary as \n", "(\n", "SELECT\n", "    b.ars_run_id, b.sto_id, b.sender_outlet_id, b.receiving_outlet_id, b.item_id, b.sto_type, b.sto_state, b.invoice_id, b.invoice_state,\n", "        \n", "    -- quantity flow\n", "    b.expected_quantity, b.reserved_quantity, b.billed_quantity, b.inwarded_quantity, b.disc_qty,\n", "    \n", "    -- Creation Metrics\n", "    b.sto_created_at, \n", "    \n", "    -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "    b.sto_invoice_created_at, \n", "    \n", "    -- Dispatch Metrics\n", "    treated_trip_started_at, treated_sto_dispatched_at,\n", "    b.trip_started_at, b.trip_created_at, b.dispatch_time, b.sto_dispatched_at,\n", "    \n", "    -- Arrival at DS Metrics\n", "    b.consignment_received_at, treated_consignment_received_at,\n", "    \n", "    -- GRN Metrics\n", "    b.grn_started_at, \n", "    b.putlist_creation_time,\n", "    b.putaway_time,\n", "\n", "    -- Discrepancy Metrics\n", "    b.discrepancy_created_at,\n", "    \n", "    case \n", "        when\n", "            extract(hour from b.sto_created_at) >= 16\n", "        then\n", "            'Slot A'\n", "        when\n", "            extract(hour from b.sto_created_at) < 7\n", "        then\n", "            'Slot A'\n", "        else\n", "            'Slot B'\n", "        end as\n", "            slot\n", "    \n", "FROM\n", "    base b\n", "LEFT JOIN\n", "    identify_correct_trip_details ictd\n", "ON\n", "    b.ars_run_id = ictd.ars_run_id and b.sto_id = ictd.sto_id and b.sender_outlet_id = ictd.sender_outlet_id and b.receiving_outlet_id = ictd.receiving_outlet_id and b.item_id = ictd.item_id and b.sto_type = ictd.sto_type and b.sto_state = ictd.sto_state and b.invoice_id = ictd.invoice_id and b.invoice_state = ictd.invoice_state\n", "),\n", "\n", "flagging_dis as\n", "(\n", "SELECT \n", "    \n", "    ars_run_id, sto_id, sender_outlet_id, receiving_outlet_id, item_id, sto_type, sto_state, invoice_id, invoice_state, slot,\n", "        \n", "    -- quantity flow\n", "    expected_quantity, reserved_quantity, billed_quantity, inwarded_quantity, disc_qty,\n", "    \n", "    -- Creation Metrics\n", "    sto_created_at, \n", "    \n", "    -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "    sto_invoice_created_at, \n", "    \n", "    -- Dispatch Metrics\n", "    treated_trip_started_at, treated_sto_dispatched_at,\n", "    trip_started_at, trip_created_at, dispatch_time, sto_dispatched_at,\n", "    \n", "    -- Arrival at DS Metrics\n", "    consignment_received_at, treated_consignment_received_at,\n", "    \n", "    -- GRN Metrics\n", "    grn_started_at, \n", "    putlist_creation_time,\n", "    putaway_time,\n", "    -- min(putlist_creation_time) as grn_start_time,\n", "    -- max(putaway_time) as grn_end_time,\n", "    \n", "    -- Discrepancy Metrics\n", "    discrepancy_created_at,\n", "    \n", "    \n", "    -- Calculate Ideal Creation to Billing Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '3.5 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '3.5 hours'\n", "        else\n", "            sto_created_at + interval '4 hours'\n", "        end\n", "            as ideal_sto_billing_time,\n", "    \n", "    \n", "    -- <PERSON><PERSON> Ideal Billing to Dispatch Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '4.5 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '4.5 hours'\n", "        else\n", "            sto_created_at + interval '7 hours'\n", "        end\n", "            as ideal_fleet_dispatch_time,\n", "    \n", "    \n", "    -- Calculate Ideal Dispatch to Arrival Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '6 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '6 hours'\n", "        else\n", "            sto_created_at + interval '11 hours'\n", "        end\n", "            as ideal_fleet_arrival_time,\n", "    \n", "    -- Calculate Ideal GRN Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '7 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '7 hours'\n", "        else\n", "            sto_created_at + interval '13 hours'\n", "        end\n", "            as ideal_sto_grn_time,\n", "    \n", "    -- Discrepancy: creation to billing, billing to dispatch, dispatch to Arrival, Arrival to GRN\n", "    \n", "    case \n", "        when ars_run_id = 0 then 'Manual' else 'Auto' end as trigger_type,\n", "    \n", "    case \n", "        when sto_created_at > sto_invoice_created_at then 1 else 0 end as creation_to_billing_discrepancy,\n", "        \n", "    case \n", "        when sto_invoice_created_at > treated_trip_started_at then 1 else 0 end as billing_to_dispatch_discrepancy,\n", "    \n", "    case \n", "        when treated_trip_started_at > treated_consignment_received_at then 1 else 0 end as dispatch_to_arrival_discrepancy,\n", "        \n", "    case \n", "        when\n", "            treated_trip_started_at is null then 1 else 0 end as missing_trip_start_time,\n", "    \n", "    case \n", "        when\n", "            treated_consignment_received_at is null then 1 else 0 end as missing_trip_arrival_time\n", "FROM \n", "    getting_summary\n", "),\n", "-- Flag GRN Pending and GRN Delay Cases\n", "\n", "flag_grn_status as \n", "(\n", "    SELECT \n", "    *,\n", "    case\n", "        when\n", "            ((grn_started_at is null) and (discrepancy_created_at is null)) and (getdate() + interval '5.5 hours' <= ideal_sto_grn_time) \n", "        then\n", "            'GRN Pending - Ideal TAT not breached'\n", "        when\n", "            ((grn_started_at is null) and (discrepancy_created_at is null)) and (getdate() + interval '5.5 hours' > ideal_sto_grn_time) \n", "        then\n", "            'GRN Pending - Ideal TAT breached'\n", "        when\n", "            (discrepancy_created_at is not null)-- and (discrepancy_created_at > ideal_sto_grn_time))\n", "        then\n", "            'DISCREPANCY_NOTE_GENERATED'\n", "        when\n", "            ((grn_started_at is not null) and (grn_started_at > ideal_sto_grn_time))\n", "        then\n", "            'GRN Completed Delay'\n", "        when\n", "            ((grn_started_at is not null) and (grn_started_at <= ideal_sto_grn_time))-- or ((grn_started_at is null) and (discrepancy_created_at is not null) and (discrepancy_created_at <= ideal_sto_grn_time))\n", "        then\n", "            'GRN Completed on-time'\n", "        else\n", "            'Others'\n", "        end \n", "            as GRN_Status,\n", "    \n", "    datediff(seconds, sto_created_at, sto_invoice_created_at)/3600.0 as actual_sto_creation_to_billing_tat,\n", "    datediff(seconds, sto_invoice_created_at, treated_trip_started_at)/3600.0 as actual_sto_billing_to_trip_start_tat,\n", "    datediff(seconds, treated_trip_started_at, treated_consignment_received_at)/3600.0 as actual_fleet_dispatch_to_arrival_tat,\n", "    datediff(seconds, treated_consignment_received_at, grn_started_at)/3600.0 as actual_fleet_arrival_to_grn_tat\n", "    \n", "    \n", "    \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, sto_created_at, sto_invoice_created_at)/3600.0\n", "    --     end \n", "    --         as actual_sto_creation_to_billing_tat,\n", "    \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, sto_invoice_created_at, treated_trip_started_at)/3600.0\n", "    --     end \n", "    --         as actual_sto_billing_to_trip_start_tat,\n", "            \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, treated_trip_started_at, treated_consignment_received_at)/3600.0\n", "    --     end \n", "    --         as actual_fleet_dispatch_to_arrival_tat,\n", "    \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, treated_consignment_received_at, grn_started_at)/3600.0\n", "    --     end \n", "    --         as actual_fleet_arrival_to_grn_tat\n", "    \n", "    FROM \n", "    flagging_dis\n", "),\n", "\n", "final_base as\n", "(\n", "    SELECT\n", "        ars_run_id, sto_id, sto_type, sto_state, sender_outlet_id, receiving_outlet_id, item_id, invoice_id, invoice_state, slot, grn_status as \"grn_status\",\n", "\n", "        case \n", "            when\n", "                grn_status = 'DISCREPANCY_NOTE_GENERATED' then 'DISCREPANCY_NOTE_GENERATED'\n", "            when\n", "                (sto_invoice_created_at is null) and (treated_trip_started_at is null) and (treated_consignment_received_at is null) and (grn_started_at is null) then 'STO Billing Pending'\n", "            when\n", "                (treated_trip_started_at is null) and (treated_consignment_received_at is null) and (grn_started_at is null) is null then 'Dispatch Pending'\n", "            when\n", "                (treated_consignment_received_at is null) and (grn_started_at is null) is null then 'Arrival at DS Pending'\n", "            when\n", "                grn_started_at is null then 'GRN Pending'\n", "            else\n", "                invoice_state end as current_status,\n", "\n", "        expected_quantity, reserved_quantity, billed_quantity, inwarded_quantity as grn_at_ds_quantity, disc_qty as discrepancy_marked,\n", "        sto_created_at as sto_creation_time, sto_invoice_created_at as sto_billed_time, treated_trip_started_at as fleet_dispatch_time, treated_consignment_received_at as fleet_arrival_time, grn_started_at as grn_time, discrepancy_created_at as discrepancy_marked_time,\n", "        actual_sto_creation_to_billing_tat, actual_sto_billing_to_trip_start_tat, actual_fleet_dispatch_to_arrival_tat, actual_fleet_arrival_to_grn_tat,\n", "        ideal_sto_billing_time, ideal_fleet_dispatch_time, ideal_fleet_arrival_time, ideal_sto_grn_time\n", "    FROM\n", "        flag_grn_status\n", "    WHERE\n", "        grn_status ilike '%%GRN Pending%%'\n", "),\n", "\n", "transit_base as \n", "(\n", "    SELECT\n", "        sender_outlet_id, receiving_outlet_id, item_id, sum(reserved_quantity) as inventory_in_transit\n", "    FROM\n", "        final_base\n", "    GROUP BY\n", "        1, 2, 3\n", "),\n", "\n", "billing_pending as\n", "(\n", "    SELECT\n", "        sender_outlet_id, receiving_outlet_id, item_id, sum(reserved_quantity) as sto_billing_pending\n", "    FROM\n", "        final_base\n", "    WHERE\n", "        current_status = 'STO Billing Pending'\n", "    GROUP BY\n", "        1, 2, 3\n", "),\n", "\n", "\n", "dispatch_pending as\n", "(\n", "    SELECT\n", "        sender_outlet_id, receiving_outlet_id, item_id, sum(reserved_quantity) as sto_dispatch_pending\n", "    FROM\n", "        final_base\n", "    WHERE\n", "        current_status = 'Dispatch Pending'\n", "    GROUP BY\n", "        1, 2, 3\n", "),\n", "\n", "arrival_pending as\n", "(\n", "    SELECT\n", "        sender_outlet_id, receiving_outlet_id, item_id, sum(reserved_quantity) as sto_arrival_pending\n", "    FROM\n", "        final_base\n", "    WHERE\n", "        current_status = 'Arrival at DS Pending'\n", "    GROUP BY\n", "        1, 2, 3\n", "),\n", "\n", "grn_pending as\n", "(\n", "    SELECT\n", "        sender_outlet_id, receiving_outlet_id, item_id, sum(reserved_quantity) as sto_grn_pending\n", "    FROM\n", "        final_base\n", "    WHERE\n", "        current_status = 'GRN Pending'\n", "    GROUP BY\n", "        1, 2, 3\n", "),\n", "\n", "discrepancy_marked as\n", "(\n", "    SELECT\n", "        sender_outlet_id, receiving_outlet_id, item_id, sum(reserved_quantity) as discrepancy_marked\n", "    FROM\n", "        final_base\n", "    WHERE\n", "        current_status = 'DISCREPANCY_NOTE_GENERATED'\n", "    GROUP BY\n", "        1, 2, 3\n", ")\n", "\n", "\n", "SELECT\n", "    tb.sender_outlet_id, tb.receiving_outlet_id, tb.item_id, inventory_in_transit, sto_billing_pending, sto_dispatch_pending, sto_arrival_pending, sto_grn_pending, discrepancy_marked\n", "    \n", "FROM\n", "    transit_base tb\n", "LEFT JOIN\n", "    billing_pending bp\n", "ON\n", "    tb.sender_outlet_id = bp.sender_outlet_id and tb.receiving_outlet_id = bp.receiving_outlet_id and tb.item_id = bp.item_id\n", "LEFT JOIN\n", "    dispatch_pending dp\n", "ON\n", "    tb.sender_outlet_id = dp.sender_outlet_id and tb.receiving_outlet_id = dp.receiving_outlet_id and tb.item_id = dp.item_id\n", "LEFT JOIN\n", "    arrival_pending ap\n", "ON\n", "    tb.sender_outlet_id = ap.sender_outlet_id and tb.receiving_outlet_id = ap.receiving_outlet_id and tb.item_id = ap.item_id\n", "LEFT JOIN\n", "    grn_pending gp\n", "ON\n", "    tb.sender_outlet_id = gp.sender_outlet_id and tb.receiving_outlet_id = gp.receiving_outlet_id and tb.item_id = gp.item_id\n", "LEFT JOIN\n", "    discrepancy_marked dm\n", "ON\n", "    tb.sender_outlet_id = dm.sender_outlet_id and tb.receiving_outlet_id = dm.receiving_outlet_id and tb.item_id = dm.item_id\n", "\n", "\"\"\"\n", "sto_data = read_sql_query(query, CON_REDSHIFT)\n", "sto_data.head(1)"]}, {"cell_type": "markdown", "id": "53aa398c-a385-419e-b2bd-cfc330a028e0", "metadata": {}, "source": ["## STO Data Rollup: Move from outlet to Facility"]}, {"cell_type": "code", "execution_count": null, "id": "c846ddd0-c135-405e-9a58-280610bc780b", "metadata": {}, "outputs": [], "source": ["sto_data = pd.merge(\n", "    sto_data,\n", "    facility_mapping_df.rename(\n", "        columns={\"outlet_id\": \"sender_outlet_id\", \"facility_id\": \"backend_facility_id\"}\n", "    ),\n", "    on=[\"sender_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "sto_data = pd.merge(\n", "    sto_data,\n", "    facility_mapping_df.rename(\n", "        columns={\n", "            \"outlet_id\": \"receiving_outlet_id\",\n", "            \"facility_id\": \"frontend_facility_id\",\n", "        }\n", "    ),\n", "    on=[\"receiving_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "sto_data[\n", "    [\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "    ]\n", "] = sto_data[\n", "    [\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "sto_data = (\n", "    sto_data.groupby([\"item_id\", \"backend_facility_id\", \"frontend_facility_id\"])\n", "    .agg(\n", "        {\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "sto_data.head(1)"]}, {"cell_type": "raw", "id": "561b95b9-dd39-486f-9f83-218b052e7af0", "metadata": {"tags": []}, "source": ["## Availability - Might Need to optimise this + Should we get current ava% ?"]}, {"cell_type": "raw", "id": "6b4eb9b4-3e28-4e5c-816e-aa4d11d55523", "metadata": {"tags": []}, "source": ["query=f\"\"\"\n", "\n", "with max_dates as\n", "(\n", "    select\n", "        max(order_date) as order_date\n", "    FROM\n", "        consumer.rpc_daily_availability\n", "    WHERE\n", "        order_date >= current_date - 1\n", "),\n", "\n", "BASE AS \n", "(\n", "select \n", "    date(order_date) as date,\n", "    extract(hours from order_date) as hour_,\n", "    item_id,\n", "    facility_id,\n", "    case when actual_quantity-blocked_quantity>0 then 1 else 0 end as available_flag\n", "from consumer.rpc_daily_availability\n", "    where\n", "        order_date IN (SELECT DISTINCT order_date FROM max_dates)\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    BASE\n", "WHERE\n", "    item_id in {items} \n", "\"\"\"\n", "\n", "availability_df = read_sql_query(query,CON_REDSHIFT)\n", "availability_df.head(2)"]}, {"cell_type": "markdown", "id": "0549d087-7f2e-4e34-896f-999f893e9372", "metadata": {}, "source": ["### Final Mapping\n"]}, {"cell_type": "code", "execution_count": null, "id": "5ab472bf-398e-4aca-a72e-74c85ad706c5", "metadata": {}, "outputs": [], "source": ["### Final Base\n", "\n", "query = f\"\"\"\n", "with active_assortment as \n", "(\n", "    select \n", "        distinct\n", "            pfma.item_id::int, \n", "            pfma.facility_id as frontend_facility_id,\n", "            master_assortment_substate_id as master_assortment_active\n", "    from \n", "        lake_rpc.product_facility_master_assortment pfma\n", "    where \n", "        pfma.master_assortment_substate_id in (1, 2, 3) and pfma.active = 1\n", "    and \n", "        pfma.item_id in {items}\n", ")\n", "select * from active_assortment\n", "\"\"\"\n", "master_assortment_substate_df = read_sql_query(query, CON_REDSHIFT)\n", "master_assortment_substate_df.head(1)"]}, {"cell_type": "raw", "id": "fad8c894-ea10-4018-a290-f5c88a6d1f15", "metadata": {}, "source": ["base=pd.merge(pd.merge(temp['item_id'].drop_duplicates().astype(int),tea_tag[['city','item_id','frontend_facility_id','backend_facility_id']],on=['item_id'],how='left'),\n", "                active_ds,left_on=['item_id','frontend_facility_id'],right_on=['item_id','active_ds'],how='left')"]}, {"cell_type": "raw", "id": "0083016b-cd96-4efa-b057-26f4fbe84e6d", "metadata": {}, "source": ["base[base['active']]"]}, {"cell_type": "code", "execution_count": null, "id": "fa3412ba-9364-4fef-8611-220ea98c506a", "metadata": {}, "outputs": [], "source": ["base_df = base_df.rename(columns={\"facility_id\": \"frontend_facility_id\"})\n", "\n", "base_df = pd.merge(\n", "    base_df,\n", "    master_assortment_substate_df,\n", "    on=[\n", "        \"item_id\",\n", "        \"frontend_facility_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "tea_tag = tea_tag[[\"item_id\", \"frontend_facility_id\", \"backend_facility_id\"]].drop_duplicates()\n", "tea_tag[\"tea_active\"] = 1\n", "\n", "\n", "## Change 29th\n", "base_df = pd.merge(\n", "    base_df,\n", "    tea_tag,\n", "    on=[\n", "        \"item_id\",\n", "        \"frontend_facility_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fd86d560-01be-4ad9-90dd-99d43e3b9c49", "metadata": {}, "outputs": [], "source": ["base_df[\"grn_at_ds\"] = pd.to_datetime(base_df[\"go_live_date\"]) - timedelta(days=1)\n", "base_df[\"transfer_to_ds\"] = pd.to_datetime(base_df[\"go_live_date\"]) - timedelta(days=2)\n", "base_df[\"grn_at_backend\"] = pd.to_datetime(base_df[\"go_live_date\"]) - timedelta(days=2)\n", "base_df[\"po_creation\"] = pd.to_datetime(base_df[\"go_live_date\"]) - timedelta(days=4)\n", "base_df[\"tea_status\"] = pd.to_datetime(base_df[\"go_live_date\"]) - timed<PERSON>ta(days=6)\n", "base_df[\"activation_status\"] = pd.to_datetime(base_df[\"go_live_date\"]) - timedelta(days=6)"]}, {"cell_type": "code", "execution_count": null, "id": "6c7f87be-0271-47d3-a976-8061951a6cbd", "metadata": {}, "outputs": [], "source": ["base_df = base_df[\n", "    [\n", "        \"city\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"go_live_date\",\n", "        \"grn_at_ds\",\n", "        \"transfer_to_ds\",\n", "        \"grn_at_backend\",\n", "        \"po_creation\",\n", "        \"tea_status\",\n", "        \"activation_status\",\n", "        \"frontend_facility_id\",\n", "        \"city_id\",\n", "        \"master_assortment_active\",\n", "        \"backend_facility_id\",\n", "        \"tea_active\",\n", "    ]\n", "]\n", "base_df.head()"]}, {"cell_type": "markdown", "id": "5120b2fd-135b-4914-99bd-683ec41293e8", "metadata": {}, "source": ["### PO Data"]}, {"cell_type": "code", "execution_count": null, "id": "0d079a00-0bb8-4c50-ba55-a89e3e2a0571", "metadata": {}, "outputs": [], "source": ["po_base_query = f\"\"\"with entity_df as\n", "(\n", "select \n", "    distinct entity_vendor_type_id, entity_vendor_id, vendor_name as entity_name\n", "from \n", "    lake_view_retail.outlet_entity_vendor_mapping base\n", "inner join \n", "    lake_view_vms.vms_vendor vms_vendor on base.entity_vendor_id = vms_vendor.id and vms_vendor.active = 1\n", "where\n", "    base.active = 1\n", "),\n", "\n", "po_base as\n", "(\n", "    select \n", "        destination_entity_vendor_id, entity_name, invoice_id,\n", "        po_number, is_open_po, po_type, po_issue_date, po_expiry_date, facility_id, facility_name, vendor_name, city, item_id, product_name, manufacturer_name,\n", "        brand_name, l0_category, l1_category, l2_category, po_qty, Cost_Price, Landing_Price, GRN_Landing_Price, po_value, PO_MRP, grn_mrp, po_status, grn_id, grn_qty, grn_value, grn_date,\n", "        po_qty - grn_qty as remaining_qty\n", "        \n", "        \n", "    from\n", "        (\n", "            select distinct\n", "                destination_entity_vendor_id, entity_name,\n", "                invoice.invoice_id,\n", "                po.id as PO_Id, po.po_number as PO_Number,\n", "                (\n", "                    case \n", "                        when po.po_type_id in (1,2) then 'Parent_PO'\n", "                        when po.po_type_id in (3,4,5) then 'Child_PO' \n", "                        else 'Internal_PO' end\n", "                ) as PO_Type,\n", "                por.parent_po_id as Parent_PO_Id,\n", "                date(po.created_at+interval '330' minute) as PO_Issue_Date,\n", "                date(po.expiry_date+interval '330' minute) as PO_Expiry_Date,  \n", "                po.outlet_id as Outlet_Id,\n", "                (\n", "                    Case \n", "                        when (poi.run_id) = '0' then 'Adhoc_PO'\n", "                        when poi.run_id is NULL then 'Adhoc_PO'\n", "                        else poi.run_id end\n", "                ) as  ARS_Id,\n", "                co.name as Outlet_Name,\n", "                \n", "                co.facility_id as facility_id,\n", "                cf.name as Facility_Name,\n", "                ipf.internal_facility_identifier as facility_identifier,\n", "                \n", "                cl.name as city, \n", "                po.vendor_id as Vendor_Id,\n", "                po.vendor_name as Vendor_Name,\n", "                poi.item_id as item_id,\n", "                poi.name as Product_Name,\n", "                poi.manufacturer_name,\n", "                poi.uom_text as VARIANT_UOM_TEXT,\n", "                poi.brand as Brand_Name,\n", "                (\n", "                    case \n", "                        when p.is_pl = 0 then 'NON_PL' else 'PL' end\n", "                ) as PL_NONPL,\n", "                poi.units_ordered as PO_QTY,\n", "                poi.cost_price as Cost_Price,\n", "                poi.landing_rate as Landing_Price,\n", "                pg.landing_price as GRN_Landing_Price,\n", "                poi.total_amount as PO_Value,\n", "                poi.mrp as PO_MRP,\n", "                pp.variant_mrp as grn_mrp,\n", "                \n", "                case \n", "                    when pos.po_state_id in (4,5,10) then 'Invalid' \n", "                    when (pos.po_state_id in (8) or po.expiry_date >= cast('{first_live_date}' as timestamp) - interval '1' day)\n", "                        and pg.po_id is null  then 'Expired' \n", "                    when pos.po_state_id not in (4,5,10) and pg.po_id is not null then 'Serviced' \n", "                    else 'Pending' \n", "                    end as PO_Status,\n", "                \n", "                (\n", "                    case \n", "                        when pos.po_state_id not in (4,5,10) then 'Not_Cancelled'\n", "                        else 'Cancelled' end\n", "                ) as Cancel_Status,\n", "                \n", "                (\n", "                    case when pos.po_state_id in (4,5,10) then 'Invalid'\n", "                    when pg.po_id is not null or pos.po_state_id in (8) then 'Closed' \n", "                    else 'Open' end\n", "                ) as Open_Status,\n", "                \n", "                case \n", "                    when pos.po_state_id not in (4,5,10) then coalesce(pg.quantity,0) \n", "                    else 0 \n", "                    end as GRN_QTY,\n", "                case \n", "                    when pos.po_state_id not in (4,5,10) then coalesce(pg.grn_val,0) \n", "                    else 0 \n", "                    end as GRN_Value,\n", "                case \n", "                    when pos.po_state_id not in (4,5,10) then date(pg.grn_d) \n", "                    end as GRN_Date,\n", "                \n", "                pg.grn_id,\n", "                l0_category,\n", "                l1_category,\n", "                l2_category,\n", "                posta.name as PO_State_Type,\n", "                \n", "                case \n", "                    when\n", "                    (posta.name in ('Created','Scheduled','Unscheduled','Rescheduled','Edit Pending') or po.is_multiple_grn = 1)\n", "                    and\n", "                    (posta.name not in ('Expired', 'Cancelled'))\n", "                    and \n", "                    (pos.po_state_id  not in (4,5,10))\n", "                    then 1\n", "                    else 0\n", "                end as is_open_po\n", "                \n", "            \n", "            from \n", "                lake_view_po.purchase_order po\n", "            \n", "            inner join \n", "                lake_view_po.purchase_order_items poi on po.id = poi.po_id\n", "            \n", "            inner join \n", "                (select distinct item_id,is_pl from lake_view_rpc.product_product where active = 1) p on poi.item_id = p.item_id\n", "            \n", "            left join \n", "                lake_view_po.purchase_order_relations por on por.child_po_id = po.id\n", "            \n", "            inner join \n", "                lake_view_po.purchase_order_status pos on pos.po_id = po.id\n", "            inner join \n", "                lake_view_po.purchase_order_state posta on posta.id = pos.po_state_id\n", "            \n", "            inner join \n", "                lake_view_retail.console_outlet co on co.id = po.outlet_id\n", "            \n", "            inner join \n", "                lake_view_retail.console_location cl on cl.id = co.tax_location_id\n", "                \n", "            left join \n", "                entity_df df on destination_entity_vendor_id = df.entity_vendor_id\n", "                \n", "            left join \n", "                (\n", "                    select\n", "                        po_number, --group_concat(invoice_id, ' | ') as invoice_id\n", "                        \n", "                        ARRAY_JOIN(ARRAY_AGG(invoice_id), ' | ') as invoice_id\n", "                        \n", "                        from (\n", "                            select\n", "                                sd.purchase_order_id as po_number, il.merchant_invoice_id as invoice_id\n", "                            from\n", "                                lake_view_ims.ims_inventory_log il\n", "                            left join \n", "                                lake_view_ims.ims_inventory_stock_details sd on sd.inventory_update_id = il.inventory_update_id\n", "                            where\n", "                                il.inventory_update_type_id in (1,28,76,90,93)\n", "                            and\n", "                                il.insert_ds_ist >= cast(cast('2022-09-10' as timestamp) - interval '1' day as varchar) --cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "                            and\n", "                                sd.insert_ds_ist >= cast(cast('2022-09-10' as timestamp) - interval '1' day as varchar) --cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "                                \n", "                            and \n", "                                sd.purchase_order_id is not null\n", "                            and\n", "                                sd.purchase_order_id <> ' '\n", "                            \n", "                            group by\n", "                                1,2\n", "                            )\n", "                        group by \n", "                            1\n", "                ) invoice on invoice.po_number = po.po_number\n", "            \n", "            left join \n", "                (\n", "                    select \n", "                        po_id, item_id, grn_id, landing_price, max(date(created_at+interval '330' minute)) as grn_d, \n", "                        sum(quantity) as quantity, sum(quantity*landing_price) as grn_val\n", "                    from \n", "                        lake_view_po.po_grn\n", "                    group by \n", "                        1,2,3,4\n", "                ) pg on pg.po_id = poi.po_id and pg.item_id = poi.item_id\n", "            \n", "            left join \n", "                lake_crates.facility cf on cf.id = co.facility_id\n", "            \n", "            left JOIN \n", "                (\n", "                    SELECT \n", "                        facility_id, MAX(internal_facility_identifier) AS internal_facility_identifier\n", "                    from \n", "                        lake_view_po.physical_facility group by 1\n", "                )  ipf on ipf.facility_id = co.facility_id\n", "            \n", "            left join \n", "                (\n", "                    select \n", "                        t1.item_id, variant_mrp \n", "                    from \n", "                        lake_view_rpc.product_product t1\n", "                    inner join \n", "                        (select item_id, max(id) as id from lake_view_rpc.product_product group by 1) t2 on t1.item_id = t2.item_id and t1.id = t2.id\n", "                ) pp on pp.item_id = poi.item_id\n", "            \n", "            left join \n", "                lake_view_rpc.item_product_mapping ipm on poi.item_id = ipm.item_id\n", "            \n", "            inner join \n", "                redshift.dwh.dim_product dp on ipm.product_id = dp.product_id and is_current -- and is_product_enabled \n", "            \n", "            where \n", "                po.created_at >= cast('2022-09-10' as timestamp) - interval '1' day--cast('{first_live_date}' as timestamp) - interval '1' day\n", "            and \n", "                poi.item_id in {items}\n", "            and \n", "                po.active = 1\n", "            and\n", "                po.po_type_id !=11\n", "        ) a\n", ")\n", "\n", "\n", "SELECT * FROM po_base\"\"\"\n", "po_base_df = read_sql_query(po_base_query, CON_PRESTO)\n", "\n", "po_base_df[[\"po_qty\", \"grn_qty\", \"remaining_qty\"]] = po_base_df[\n", "    [\"po_qty\", \"grn_qty\", \"remaining_qty\"]\n", "].astype(int)\n", "po_base_df[[\"po_value\", \"grn_value\", \"Landing_Price\"]] = po_base_df[\n", "    [\"po_value\", \"grn_value\", \"Landing_Price\"]\n", "].astype(float)\n", "\n", "# po_agg_df = po_base_df.groupby(['facility_id', 'facility_name', 'item_id', 'product_name']).agg({'po_qty':'sum', 'po_value':'sum', 'Landing_Price':'mean', 'grn_qty':'sum', 'grn_value':'sum', 'remaining_qty':'sum'}).reset_index()\n", "\n", "po_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d519bde1-780c-457c-897e-6b8bf114ffad", "metadata": {}, "outputs": [], "source": ["temp[(temp[\"event_flag\"] == \"Diwali\") & (temp[\"assortment_type\"] == \"Diwali\")][[\"city\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "9961c796-3a47-45a2-8b74-30c9c3149b9b", "metadata": {}, "outputs": [], "source": ["po_base_df"]}, {"cell_type": "code", "execution_count": null, "id": "6b439bf2-196a-456d-9be3-53ccf42cd7e7", "metadata": {}, "outputs": [], "source": ["len(po_base_df[\"grn_id\"].drop_duplicates())"]}, {"cell_type": "code", "execution_count": null, "id": "e408b23c-9223-4050-ad5e-8fdd1597854c", "metadata": {}, "outputs": [], "source": ["len(po_base_df[\"po_number\"].drop_duplicates())"]}, {"cell_type": "code", "execution_count": null, "id": "e7109920-c324-44c4-90f8-50e48f51c5e2", "metadata": {}, "outputs": [], "source": ["# Get Schedule Dates for PO"]}, {"cell_type": "code", "execution_count": null, "id": "5dcf4cf2-b8a6-4241-973d-8fc63385dd13", "metadata": {}, "outputs": [], "source": ["open_po_list = tuple(list(po_base_df[po_base_df.is_open_po == 1][\"po_number\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "d8909958-4acd-488c-9874-cf0cfbc283ff", "metadata": {}, "outputs": [], "source": ["po_schedule_query = f\"\"\"with base as\n", "(select \n", "    distinct po.id as po_id, po_number,\n", "    -- cast(date(ps.schedule_date_time + interval '330' minute) as varchar) || ' ' || cast(extract(hour from ps.schedule_date_time + interval '330' minute) as varchar) || ':' || cast(extract(minute from ps.schedule_date_time + interval '330' minute) as varchar)\n", "    cast(ps.schedule_date_time + interval '330' minute as date) as schedule_date_time\n", "from\n", "    lake_view_po.purchase_order po\n", "left join\n", "    lake_view_po.po_schedule ps on po.id = ps.po_id_id\n", "where\n", "    po.created_at >= current_date - interval '15' day\n", "and\n", "    po.po_number in {open_po_list}\n", "and\n", "    ps.created_at >= current_date - interval '15' day\n", ")\n", "\n", "SELECT\n", "    po_number, ARRAY_JOIN(ARRAY_AGG(schedule_date_time), ' | ') as schedule_date_time\n", "FROM\n", "    base\n", "GROUP BY\n", "    1\"\"\"\n", "po_schedule_df = read_sql_query(po_schedule_query, CON_PRESTO)\n", "po_schedule_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "06c060e5-8d12-43fd-85c9-991c99cb1bd5", "metadata": {}, "outputs": [], "source": ["open_po_base_df = pd.merge(\n", "    po_base_df[po_base_df.is_open_po == 1], po_schedule_df, on=[\"po_number\"], how=\"left\"\n", ")\n", "open_po_base_df = open_po_base_df[open_po_base_df.schedule_date_time.isna() == False]\n", "open_po_base_df[\"schedule_date_time\"] = open_po_base_df[\"schedule_date_time\"].astype(str)\n", "open_po_base_df[\"schedule_date_time\"] = open_po_base_df[\"schedule_date_time\"] + \"; \"\n", "open_po_base_df = open_po_base_df[\n", "    [\"item_id\", \"facility_id\", \"schedule_date_time\"]\n", "].drop_duplicates()\n", "open_po_base_df = (\n", "    open_po_base_df.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"schedule_date_time\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "open_po_base_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e898690d-0ea0-43e2-849d-d26eb3738302", "metadata": {}, "outputs": [], "source": ["po_agg_df = (\n", "    po_base_df.groupby([\"facility_id\", \"item_id\"])\n", "    .agg(\n", "        {\n", "            \"po_qty\": \"sum\",\n", "            \"po_value\": \"sum\",\n", "            \"Landing_Price\": \"mean\",\n", "            \"grn_qty\": \"sum\",\n", "            \"grn_value\": \"sum\",\n", "            \"remaining_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(columns={\"remaining_qty\": \"open_po_qty\"})\n", ")\n", "\n", "po_agg_df = pd.merge(\n", "    po_agg_df,\n", "    open_po_base_df[[\"facility_id\", \"item_id\", \"schedule_date_time\"]].rename(\n", "        columns={\"schedule_date_time\": \"po_schedule_dates\"}\n", "    ),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "po_agg_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "362bb36b-1b04-44e1-aef1-0048d2fd19dd", "metadata": {}, "outputs": [], "source": ["po_agg_df[po_agg_df[\"item_id\"] == 10012424]"]}, {"cell_type": "code", "execution_count": null, "id": "42db7b43-0581-4f08-8980-1d0aa1d5ecdb", "metadata": {}, "outputs": [], "source": ["# po_agg_df[(po_agg_df.facility_id == 264)  & (po_agg_df.item_id == 10116966)]"]}, {"cell_type": "code", "execution_count": null, "id": "367ea353-2ef7-44b9-b1a7-91fc5600832f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "699bd582-dedc-4ef3-bfe8-69bf820628ca", "metadata": {}, "outputs": [], "source": ["# # # Me<PERSON> Backend Inventory\n", "# # backend_inventory=backend_inventory.rename(columns={'current_stock':'current_stock_backend'})\n", "# # final_df = pd.merge(final_df, backend_inventory.drop(columns = {'facility_name'}), left_on=['item_id','backend_facility_id'], right_on=['item_id','facility_id'], how='left').drop(columns=['inv_outlet_id','outlet_name','facility_id'])\n", "\n", "# backend_inventory = backend_inventory.groupby(['facility_id', 'item_id']).agg({'current_stock_backend':'sum'}).reset_index()\n", "# backend_inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "64056f5f-6f95-4c35-b3a1-2464b67c50c6", "metadata": {}, "outputs": [], "source": ["# po_agg_df = po_base_df.groupby(['facility_id', 'facility_name', 'item_id', 'product_name']).agg({'po_qty':'sum', 'po_value':'sum', 'Landing_Price':'mean', 'grn_qty':'sum', 'grn_value':'sum', 'remaining_qty':'sum'}).reset_index()\n", "# po_agg_df\n", "# po_agg_df = pd.merge(po_agg_df, backend_inventory, on = ['item_id', 'facility_id'], how = 'left')\n", "\n", "\n", "# po_agg_df = po_agg_df.rename(columns = {'remaining_qty':'open_po_qty'})\n", "# po_agg_df['open_po_value'] = po_agg_df['open_po_qty'] * po_agg_df['Landing_Price']\n", "\n", "# po_agg_df = pd.merge(po_agg_df, product_attributes_df, on = ['item_id'], how = 'left')\n", "\n", "# po_ptype_agg_df = po_agg_df.groupby(['facility_id', 'facility_name', 'ptype',]).agg({'current_stock_backend':'sum', 'po_qty':'sum', 'po_value':'sum', 'grn_qty':'sum', 'grn_value':'sum', 'open_po_qty':'sum', 'open_po_value':'sum'}).reset_index()\n", "# po_ptype_agg_df"]}, {"cell_type": "code", "execution_count": null, "id": "36e86a25-090f-46c4-8d53-74e31f037bcd", "metadata": {}, "outputs": [], "source": ["# Availability Merge\n", "# final_df = pd.merge(base_df, availability_df.drop(columns = {'hour_', 'date'}).rename(columns = {'facility_id':'frontend_facility_id'}), on = ['frontend_facility_id', 'item_id'], how = 'left')\n", "\n", "# Merge DS Inventory\n", "final_df = pd.merge(\n", "    base_df,\n", "    DS_inventory.drop(columns={\"facility_name\"}),\n", "    left_on=[\"item_id\", \"frontend_facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ").drop(columns=[\"inv_outlet_id\", \"outlet_name\", \"facility_id\"])\n", "\n", "# Merge Backend Inventory\n", "backend_inventory = backend_inventory.rename(columns={\"current_stock\": \"current_stock_backend\"})\n", "final_df = pd.merge(\n", "    final_df,\n", "    backend_inventory.drop(columns={\"facility_name\"}),\n", "    left_on=[\"item_id\", \"backend_facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ").drop(columns=[\"facility_id\"])\n", "\n", "# Merge Sales\n", "final_df = pd.merge(final_df, sales, on=[\"frontend_facility_id\", \"item_id\"], how=\"left\")\n", "\n", "# Me<PERSON>mp\n", "final_df = pd.merge(final_df, dump, on=[\"frontend_facility_id\", \"item_id\"], how=\"left\")\n", "\n", "# Merge STO Data\n", "final_df = pd.merge(\n", "    final_df,\n", "    sto_data,\n", "    on=[\"frontend_facility_id\", \"item_id\", \"backend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "# Merge Truncation Data\n", "final_df = pd.merge(\n", "    final_df,\n", "    frontend_current_date_truncation,\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"be_outlet_id\", \"fe_outlet_id\"})\n", "\n", "final_df = pd.merge(\n", "    final_df,\n", "    frontend_l_1_date_truncation,\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"be_outlet_id\", \"fe_outlet_id\"})\n", "\n", "final_df = pd.merge(\n", "    final_df,\n", "    frontend_l_7_date_truncation,\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ").drop(columns={\"be_outlet_id\", \"fe_outlet_id\"})\n", "\n", "# Add Ptype\n", "final_df = pd.merge(\n", "    final_df,\n", "    product_attributes_df[[\"item_id\", \"ptype\"]].drop_duplicates(),\n", "    on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "# Fill Missing Values\n", "final_df[\n", "    [\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "] = final_df[\n", "    [\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "final_df[\"ptype\"] = final_df[\"ptype\"].fillna(\"Unknown\")\n", "final_df[\"master_assortment_active\"] = final_df[\"master_assortment_active\"].fillna(3)\n", "final_df[\"tea_active\"] = final_df[\"tea_active\"].fillna(0)\n", "\n", "final_df = final_df[\n", "    [\n", "        \"city\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"ptype\",\n", "        \"go_live_date\",\n", "        \"grn_at_ds\",\n", "        \"transfer_to_ds\",\n", "        \"grn_at_backend\",\n", "        \"po_creation\",\n", "        \"tea_status\",\n", "        \"activation_status\",\n", "        \"frontend_facility_id\",\n", "        \"city_id\",\n", "        \"master_assortment_active\",\n", "        \"backend_facility_id\",\n", "        \"tea_active\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "final_df[\n", "    [\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "] = final_df[\n", "    [\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "].astype(\n", "    float\n", ")\n", "\n", "final_df[\"available_flag\"] = np.where((final_df[\"current_stock\"] > 0), 1, 0)\n", "final_df[\"not_live_but_in_transit\"] = np.where(\n", "    (final_df[\"current_stock\"] + final_df[\"inventory_in_transit\"]) > 0, 1, 0\n", ")  ## fix this----nkittt\n", "\n", "\n", "final_df[\"go_live_date\"] = pd.to_datetime(final_df[\"go_live_date\"]).dt.date\n", "final_df[\"grn_at_ds\"] = pd.to_datetime(final_df[\"grn_at_ds\"]).dt.date\n", "final_df[\"transfer_to_ds\"] = pd.to_datetime(final_df[\"transfer_to_ds\"]).dt.date\n", "final_df[\"grn_at_backend\"] = pd.to_datetime(final_df[\"grn_at_backend\"]).dt.date\n", "final_df[\"po_creation\"] = pd.to_datetime(final_df[\"po_creation\"]).dt.date\n", "final_df[\"tea_status\"] = pd.to_datetime(final_df[\"tea_status\"]).dt.date\n", "final_df[\"activation_status\"] = pd.to_datetime(final_df[\"activation_status\"]).dt.date\n", "\n", "final_df[\"go_live_date\"] = pd.to_datetime(final_df[\"go_live_date\"])\n", "final_df[\"grn_at_ds\"] = pd.to_datetime(final_df[\"grn_at_ds\"])\n", "final_df[\"transfer_to_ds\"] = pd.to_datetime(final_df[\"transfer_to_ds\"])\n", "final_df[\"grn_at_backend\"] = pd.to_datetime(final_df[\"grn_at_backend\"])\n", "final_df[\"po_creation\"] = pd.to_datetime(final_df[\"po_creation\"])\n", "final_df[\"tea_status\"] = pd.to_datetime(final_df[\"tea_status\"])\n", "final_df[\"activation_status\"] = pd.to_datetime(final_df[\"activation_status\"])\n", "\n", "final_df = pd.merge(final_df, min_max, on=[\"item_id\", \"frontend_facility_id\"], how=\"left\")\n", "final_df = pd.merge(\n", "    final_df,\n", "    po_agg_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"po_qty\",\n", "            \"grn_qty\",\n", "            \"po_schedule_dates\",\n", "            \"open_po_qty\",\n", "        ]\n", "    ].rename(columns={\"facility_id\": \"backend_facility_id\"}),\n", "    on=[\"item_id\", \"backend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3f2b1ed1-116c-40ef-8f2b-718c49e43c0b", "metadata": {}, "outputs": [], "source": ["# final_df[~final_df.po_schedule_dates.isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "aef8a1dd-3ae7-4c7d-ad63-51cca4d67929", "metadata": {}, "outputs": [], "source": ["# Aggregated Summaries required\n", "\n", "\n", "# 1. India Ptype - Sales / Availability\n", "# 2. City Ptype - Sales / Availability\n", "\n", "# 3. Backend Store\n", "# 4. City Backend"]}, {"cell_type": "code", "execution_count": null, "id": "590e46b7-0604-4e3b-87e5-30cc32a1d640", "metadata": {}, "outputs": [], "source": ["# final_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e1a8d117-4c0a-4a61-bdf5-cf4c25d8ab43", "metadata": {}, "outputs": [], "source": ["# City Backend View - Item or Ptype?"]}, {"cell_type": "markdown", "id": "47c108ff-767e-4932-b97a-6bb93ee1050c", "metadata": {}, "source": ["# Backend Aggregated Views"]}, {"cell_type": "code", "execution_count": null, "id": "03879fa0-abb5-4c3e-8fa4-0b7e453c6990", "metadata": {}, "outputs": [], "source": ["backend_aggregated_base_df = final_df.copy()\n", "backend_aggregated_base_df[\"count_flag\"] = 1\n", "backend_aggregated_base_df[\"backend_facility_id\"] = backend_aggregated_base_df[\n", "    \"backend_facility_id\"\n", "].<PERSON>na(-666)\n", "backend_aggregated_base_df[\n", "    [\"min_quantity\", \"max_quantity\", \"open_po_qty\"]\n", "] = backend_aggregated_base_df[[\"min_quantity\", \"max_quantity\", \"open_po_qty\"]].fillna(0)\n", "backend_aggregated_base_df[[\"po_qty\", \"grn_qty\",]] = backend_aggregated_base_df[\n", "    [\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "    ]\n", "].fillna(0)\n", "\n", "backend_aggregated_base_df = pd.merge(\n", "    backend_aggregated_base_df,\n", "    facility_name_mapping_df.rename(\n", "        columns={\n", "            \"facility_id\": \"backend_facility_id\",\n", "            \"facility_name\": \"backend_facility_name\",\n", "        }\n", "    ),\n", "    on=[\"backend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "backend_aggregated_base_df[\"backend_facility_name\"] = backend_aggregated_base_df[\n", "    \"backend_facility_name\"\n", "].fillna(\"No Backend Tagged\")\n", "\n", "\n", "backend_aggregated_base_df = pd.merge(\n", "    backend_aggregated_base_df,\n", "    facility_name_mapping_df.rename(\n", "        columns={\n", "            \"facility_id\": \"frontend_facility_id\",\n", "            \"facility_name\": \"frontend_facility_name\",\n", "        }\n", "    ),\n", "    on=[\"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "backend_aggregated_base_df[\"frontend_facility_name\"] = backend_aggregated_base_df[\n", "    \"frontend_facility_name\"\n", "].fillna(\"Missing DS Name\")"]}, {"cell_type": "markdown", "id": "6e5c8746-e483-4346-9ea9-5d3e5d14eb05", "metadata": {}, "source": ["## City Backend "]}, {"cell_type": "code", "execution_count": null, "id": "3ecd7e12-8979-4117-8951-86f74920ee1d", "metadata": {}, "outputs": [], "source": ["backend_aggregated_base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "b1970d01-2b37-483d-8aae-69f70859621f", "metadata": {}, "outputs": [], "source": ["###\n", "backend_inventory.head()\n", "\n", "backend_inventory[\n", "    (backend_inventory[\"item_id\"] == 10043029) & (backend_inventory[\"facility_id\"] == 517)\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "c5b4b196-6f32-45ed-aabd-4c84698ffe7b", "metadata": {}, "outputs": [], "source": ["backend_aggregated_base_df[\n", "    (backend_aggregated_base_df[\"item_id\"] == 10043029)\n", "    & (backend_aggregated_base_df[\"frontend_facility_id\"] == 722)\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "9df8c1c4-2e34-4452-9d1e-e3e41df93613", "metadata": {}, "outputs": [], "source": ["backend_availability = backend_aggregated_base_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "backend_availability[\"availability_flag\"] = np.where(\n", "    backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability = (\n", "    backend_availability.groupby(\n", "        [\n", "            \"city\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability[\"be_availability\"] = (\n", "    backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2b8bb627-208d-4a3b-81ef-ece7fff19b4c", "metadata": {"tags": []}, "outputs": [], "source": ["# city, assortment type, backend<>\n", "\n", "\n", "city_backend_agg_view = (\n", "    backend_aggregated_base_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "            \"tea_active\": \"sum\",\n", "            \"min_quantity\": \"sum\",\n", "            \"max_quantity\": \"sum\",\n", "            # \"po_qty\": \"mean\",\n", "            # \"grn_qty\": \"mean\",\n", "            # # 'po_schedule_dates':'mean',\n", "            # \"open_po_qty\": \"mean\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(columns={\"count_flag\": \"item_facility_combinations\"})\n", ")\n", "\n", "## backend availability\n", "backend_availability = backend_aggregated_base_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "backend_availability[\"availability_flag\"] = np.where(\n", "    backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability = (\n", "    backend_availability.groupby(\n", "        [\n", "            \"city\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability[\"be_availability\"] = (\n", "    backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", ")\n", "\n", "# \"po_qty\": \"mean\",\n", "# \"grn_qty\": \"mean\",\n", "# # 'po_schedule_dates':'mean',\n", "# \"open_po_qty\": \"mean\",\n", "\n", "\n", "backend_po_addition_df = backend_aggregated_base_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "backend_po_addition_df = (\n", "    backend_po_addition_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"po_qty\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"open_po_qty\": \"sum\",\n", "            \"current_stock_backend\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "backend_po_addition_df = pd.merge(\n", "    backend_po_addition_df,\n", "    backend_availability,\n", "    how=\"left\",\n", "    on=[\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "    ],\n", ")\n", "\n", "\n", "city_backend_agg_view = pd.merge(\n", "    city_backend_agg_view,\n", "    backend_po_addition_df,\n", "    on=[\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "city_backend_agg_view[\"po_schedule_dates\"] = \"\"\n", "city_backend_agg_view[\"availability\"] = (\n", "    city_backend_agg_view[\"available_flag\"] / city_backend_agg_view[\"item_facility_combinations\"]\n", ")\n", "\n", "city_backend_agg_view[\"in_transit_availability\"] = (\n", "    city_backend_agg_view[\"not_live_but_in_transit\"]\n", "    / city_backend_agg_view[\"item_facility_combinations\"]\n", ")\n", "\n", "city_backend_agg_view[\"in_transit_availability\"] = np.where(\n", "    city_backend_agg_view[\"in_transit_availability\"] > 1,\n", "    1,\n", "    city_backend_agg_view[\"in_transit_availability\"],\n", ")\n", "\n", "city_backend_agg_view = city_backend_agg_view[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"be_availability\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"item_id\",\n", "        \"frontend_facility_id\",\n", "        \"item_facility_combinations\",\n", "        \"tea_active\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"po_schedule_dates\",\n", "        \"open_po_qty\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "##\n", "all_city_backend_agg_view = (\n", "    backend_aggregated_base_df.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "            \"tea_active\": \"sum\",\n", "            \"min_quantity\": \"sum\",\n", "            \"max_quantity\": \"sum\",\n", "            # \"po_qty\": \"mean\",\n", "            # \"grn_qty\": \"mean\",\n", "            # # 'po_schedule_dates':'sum',\n", "            # \"open_po_qty\": \"mean\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(columns={\"count_flag\": \"item_facility_combinations\"})\n", ")\n", "\n", "backend_availability = backend_aggregated_base_df[\n", "    [\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "backend_availability[\"availability_flag\"] = np.where(\n", "    backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability = (\n", "    backend_availability.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability[\"be_availability\"] = (\n", "    backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", ")\n", "\n", "# \"po_qty\": \"mean\",\n", "# \"grn_qty\": \"mean\",\n", "# # 'po_schedule_dates':'mean',\n", "# \"open_po_qty\": \"mean\",\n", "\n", "\n", "backend_po_addition_df = backend_aggregated_base_df[\n", "    [\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "backend_po_addition_df = (\n", "    backend_po_addition_df.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"po_qty\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"open_po_qty\": \"sum\",\n", "            \"current_stock_backend\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "backend_po_addition_df = pd.merge(\n", "    backend_po_addition_df,\n", "    backend_availability,\n", "    how=\"left\",\n", "    on=[\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "    ],\n", ")\n", "\n", "\n", "all_city_backend_agg_view = pd.merge(\n", "    all_city_backend_agg_view,\n", "    backend_po_addition_df,\n", "    on=[\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "## merging be availablity\n", "\n", "\n", "all_city_backend_agg_view[\"po_schedule_dates\"] = \"\"\n", "all_city_backend_agg_view[\"availability\"] = (\n", "    all_city_backend_agg_view[\"available_flag\"]\n", "    / all_city_backend_agg_view[\"item_facility_combinations\"]\n", ")\n", "all_city_backend_agg_view[\"in_transit_availability\"] = (\n", "    all_city_backend_agg_view[\"not_live_but_in_transit\"]\n", "    / all_city_backend_agg_view[\"item_facility_combinations\"]\n", ")\n", "all_city_backend_agg_view[\"in_transit_availability\"] = np.where(\n", "    all_city_backend_agg_view[\"in_transit_availability\"] > 1,\n", "    1,\n", "    all_city_backend_agg_view[\"in_transit_availability\"],\n", ")\n", "all_city_backend_agg_view[\"city\"] = \"All\"\n", "all_city_backend_agg_view = all_city_backend_agg_view[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"be_availability\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"item_id\",\n", "        \"frontend_facility_id\",\n", "        \"item_facility_combinations\",\n", "        \"tea_active\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"po_schedule_dates\",\n", "        \"open_po_qty\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "\n", "city_backend_agg_view = pd.concat([all_city_backend_agg_view, city_backend_agg_view])\n", "city_backend_agg_view.head()\n", "pb.to_sheets(\n", "    city_backend_agg_view,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"city_backend_agg_view\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c0266759-74a7-4bd5-90d3-6c452af42b60", "metadata": {}, "outputs": [], "source": ["city_backend_agg_view.head()"]}, {"cell_type": "raw", "id": "57b2abbd-1703-4c5e-a463-844d8a4bd715", "metadata": {}, "source": ["city_backend_agg_view.to_csv('city_backend_agg_view.csv')"]}, {"cell_type": "markdown", "id": "00c5661f-b27b-48b4-9871-074df0fecb44", "metadata": {}, "source": ["## City Backend Store"]}, {"cell_type": "code", "execution_count": null, "id": "b8ae2128-cdff-44ea-bc16-e9fe3e730129", "metadata": {}, "outputs": [], "source": ["# city, assortment type, backend<>\n", "\n", "\n", "city_backend_ds_agg_view = (\n", "    backend_aggregated_base_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"frontend_facility_id\",\n", "            \"backend_facility_id\",\n", "            \"frontend_facility_name\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {  #'item_id':'nunique',\n", "            \"count_flag\": \"sum\",\n", "            \"tea_active\": \"sum\",\n", "            \"min_quantity\": \"sum\",\n", "            \"max_quantity\": \"sum\",\n", "            \"po_qty\": \"mean\",\n", "            \"grn_qty\": \"mean\",\n", "            \"open_po_qty\": \"mean\",\n", "            \"current_stock_backend\": \"mean\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(columns={\"count_flag\": \"item_combinations\"})\n", ")\n", "\n", "city_backend_ds_agg_view[\"availability\"] = (\n", "    city_backend_ds_agg_view[\"available_flag\"] / city_backend_ds_agg_view[\"item_combinations\"]\n", ")\n", "\n", "city_backend_ds_agg_view[\"in_transit_availability\"] = (\n", "    city_backend_ds_agg_view[\"not_live_but_in_transit\"]\n", "    / city_backend_ds_agg_view[\"item_combinations\"]\n", ")\n", "\n", "city_backend_ds_agg_view[\"in_transit_availability\"] = np.where(\n", "    city_backend_ds_agg_view[\"in_transit_availability\"] > 1,\n", "    1,\n", "    city_backend_ds_agg_view[\"in_transit_availability\"],\n", ")\n", "\n", "\n", "## backend availability\n", "backend_availability = backend_aggregated_base_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "backend_availability[\"availability_flag\"] = np.where(\n", "    backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability = (\n", "    backend_availability.groupby(\n", "        [\n", "            \"city\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability[\"be_availability\"] = (\n", "    backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", ")\n", "\n", "city_backend_ds_agg_view = pd.merge(\n", "    city_backend_ds_agg_view,\n", "    backend_availability,\n", "    on=[\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "city_backend_ds_agg_view = city_backend_ds_agg_view[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"frontend_facility_id\",\n", "        \"backend_facility_id\",\n", "        \"frontend_facility_name\",\n", "        \"backend_facility_name\",\n", "        \"be_availability\",\n", "        \"availability\",  #'item_id',\n", "        \"in_transit_availability\",\n", "        \"item_combinations\",\n", "        \"tea_active\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "pb.to_sheets(\n", "    city_backend_ds_agg_view,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"city_backend_ds_agg_view\",\n", ")\n", "# city_backend_ds_agg_view"]}, {"cell_type": "markdown", "id": "f8935d77-ecb0-4b52-ae85-454297a48fc9", "metadata": {}, "source": ["## Backend Store"]}, {"cell_type": "code", "execution_count": null, "id": "8a0a1463-7b93-4ce8-9b05-d20465bdab02", "metadata": {}, "outputs": [], "source": ["# city, assortment type, backend<>\n", "\n", "\n", "backend_ds_agg_view = (\n", "    backend_aggregated_base_df.groupby(\n", "        [\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"frontend_facility_id\",\n", "            \"frontend_facility_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {  #'item_id':'nunique',\n", "            \"count_flag\": \"sum\",\n", "            \"tea_active\": \"sum\",\n", "            \"min_quantity\": \"sum\",\n", "            \"max_quantity\": \"sum\",\n", "            \"po_qty\": \"mean\",\n", "            \"grn_qty\": \"mean\",\n", "            \"open_po_qty\": \"mean\",\n", "            \"current_stock_backend\": \"mean\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(columns={\"count_flag\": \"item_combinations\"})\n", ")\n", "\n", "\n", "backend_ds_agg_view[\"availability\"] = (\n", "    backend_ds_agg_view[\"available_flag\"] / backend_ds_agg_view[\"item_combinations\"]\n", ")\n", "\n", "backend_ds_agg_view[\"in_transit_availability\"] = (\n", "    backend_ds_agg_view[\"not_live_but_in_transit\"] / backend_ds_agg_view[\"item_combinations\"]\n", ")\n", "\n", "backend_ds_agg_view[\"in_transit_availability\"] = np.where(\n", "    backend_ds_agg_view[\"in_transit_availability\"] > 1,\n", "    1,\n", "    backend_ds_agg_view[\"in_transit_availability\"],\n", ")\n", "\n", "## backend_availability\n", "backend_availability = backend_aggregated_base_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "backend_availability[\"availability_flag\"] = np.where(\n", "    backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability = (\n", "    backend_availability.groupby(\n", "        [\n", "            \"city\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "        ]\n", "    )\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability[\"be_availability\"] = (\n", "    backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", ")\n", "\n", "\n", "backend_ds_agg_view = pd.merge(\n", "    backend_ds_agg_view,\n", "    backend_availability,\n", "    on=[\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "backend_ds_agg_view = backend_ds_agg_view[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility_name\",\n", "        \"be_availability\",\n", "        \"availability\",  #'item_id',\n", "        \"in_transit_availability\",\n", "        \"item_combinations\",\n", "        \"tea_active\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "# pb.to_sheets(backend_ds_agg_view,\"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\"backend_ds_agg_view\")\n", "backend_ds_agg_view"]}, {"cell_type": "code", "execution_count": null, "id": "71bae2db-2026-424a-beeb-1aa62726f75d", "metadata": {}, "outputs": [], "source": ["# min_max.head(2)\n", "# final_df[(final_df.item_id >= 0) & (final_df.backend_facility_id == 554)]\n", "# final_df[['item_id']].drop_duplicates()\n", "# final_df.columns\n", "# po_agg_df"]}, {"cell_type": "raw", "id": "7ce8fd94-2181-46a1-bcf7-e5573e5ee35a", "metadata": {}, "source": ["final_df.to_csv('final_df.csv')"]}, {"cell_type": "markdown", "id": "9c1a6baa-da9a-4221-afb2-04e39d871f1a", "metadata": {}, "source": ["# Current Performance"]}, {"cell_type": "code", "execution_count": null, "id": "e6ade03d-d478-4102-a1be-f63c4e465fd5", "metadata": {}, "outputs": [], "source": ["current_df = (\n", "    final_df.copy()\n", ")  # [pd.to_datetime(final_df['go_live_date']) <= pd.to_datetime((datetime.now() + timedelta(hours = 5.5)).date())]\n", "current_df[\"count_flag\"] = 1\n", "\n", "current_df[[\"po_qty\", \"grn_qty\", \"min_quantity\", \"max_quantity\"]] = current_df[\n", "    [\"po_qty\", \"grn_qty\", \"min_quantity\", \"max_quantity\"]\n", "].fillna(0)\n", "\n", "current_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "95266510-7a5e-437c-ba48-d092e2e65bee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "3c8fc66c-12b5-45c6-afa1-ec8a185678c2", "metadata": {}, "source": ["# City Ptype Summary"]}, {"cell_type": "code", "execution_count": null, "id": "f24a88d6-9b9b-47c3-ae77-f6caa047aac3", "metadata": {}, "outputs": [], "source": ["# Store Level Availability\n", "store_ptype_availability = (\n", "    current_df.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\", \"frontend_facility_id\"])\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "store_ptype_availability[\"availability_stores\"] = (\n", "    store_ptype_availability[\"available_flag\"] / store_ptype_availability[\"count_flag\"]\n", ")\n", "\n", "store_ptype_availability[\"ava_more_than_70\"] = np.where(\n", "    store_ptype_availability[\"availability_stores\"] > 0.7, 1, 0\n", ")\n", "store_ptype_availability[\"ava_more_than_90\"] = np.where(\n", "    store_ptype_availability[\"availability_stores\"] > 0.9, 1, 0\n", ")\n", "\n", "store_ptype_availability = (\n", "    store_ptype_availability.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg({\"ava_more_than_70\": \"sum\", \"ava_more_than_90\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "# All City Metrics\n", "city_ptype_all_df = (\n", "    current_df.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"go_live_date\": \"mean\",\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"total_item_facility\",\n", "            \"item_id\": \"unique_items\",\n", "            \"frontend_facility_id\": \"unique_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "city_ptype_all_df_backend_metrics = current_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"backend_facility_id\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "    ]\n", "].drop_duplicates()\n", "city_ptype_all_df_backend_metrics = (\n", "    city_ptype_all_df_backend_metrics.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"current_stock_backend\": \"sum\",\n", "            \"po_qty\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"open_po_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "city_ptype_all_df = pd.merge(\n", "    city_ptype_all_df,\n", "    city_ptype_all_df_backend_metrics,\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "# Identify PFMA Active assortment\n", "city_ptype_master_active_df = (\n", "    current_df[current_df.master_assortment_active == 1]\n", "    .groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"master_active_item_facility\",\n", "            \"item_id\": \"unique_master_active_items\",\n", "            \"frontend_facility_id\": \"unique_master_active_facilities\",\n", "        }\n", "    )\n", ")\n", "# Identify TEA Active assortment\n", "city_ptype_tea_active_df = (\n", "    current_df[current_df.tea_active == 1]\n", "    .groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"tea_active_item_facility\",\n", "            \"item_id\": \"unique_tea_active_items\",\n", "            \"frontend_facility_id\": \"unique_tea_active_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "\n", "city_ptype_all_df = pd.merge(\n", "    city_ptype_all_df,\n", "    city_ptype_master_active_df,\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "city_ptype_all_df = pd.merge(\n", "    city_ptype_all_df,\n", "    city_ptype_tea_active_df,\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "city_ptype_all_df = pd.merge(\n", "    city_ptype_all_df,\n", "    store_ptype_availability,\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "city_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "] = city_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "city_ptype_all_df[\"availability\"] = (\n", "    city_ptype_all_df[\"available_flag\"] / city_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "city_ptype_all_df[\"in_transit_availability\"] = (\n", "    city_ptype_all_df[\"not_live_but_in_transit\"] / city_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "\n", "city_ptype_all_df[\"in_transit_availability\"] = np.where(\n", "    city_ptype_all_df[\"in_transit_availability\"] > 1,\n", "    1,\n", "    city_ptype_all_df[\"in_transit_availability\"],\n", ")\n", "\n", "\n", "city_ptype_all_df = city_ptype_all_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"go_live_date\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"available_flag\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "# city_ptype_all_df.head(4)\n", "\n", "city_ptype_all_df"]}, {"cell_type": "code", "execution_count": null, "id": "50338d3c-284b-4120-b381-8c93e478943b", "metadata": {}, "outputs": [], "source": ["# Store Level Availability\n", "all_store_ptype_availability = (\n", "    current_df.groupby([\"assortment_type\", \"event_flag\", \"ptype\", \"frontend_facility_id\"])\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "all_store_ptype_availability[\"availability_stores\"] = (\n", "    all_store_ptype_availability[\"available_flag\"] / all_store_ptype_availability[\"count_flag\"]\n", ")\n", "\n", "\n", "all_store_ptype_availability[\"ava_more_than_70\"] = np.where(\n", "    all_store_ptype_availability[\"availability_stores\"] > 0.7, 1, 0\n", ")\n", "all_store_ptype_availability[\"ava_more_than_90\"] = np.where(\n", "    all_store_ptype_availability[\"availability_stores\"] > 0.9, 1, 0\n", ")\n", "\n", "all_store_ptype_availability = (\n", "    all_store_ptype_availability.groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg({\"ava_more_than_70\": \"sum\", \"ava_more_than_90\": \"sum\"})\n", "    .reset_index()\n", ")\n", "all_store_ptype_availability[\"city\"] = \"All\"\n", "all_store_ptype_availability = all_store_ptype_availability[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "    ]\n", "]\n", "\n", "\n", "# All City Metrics\n", "pan_ptype_all_df = (\n", "    current_df.groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"go_live_date\": \"mean\",\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"total_item_facility\",\n", "            \"item_id\": \"unique_items\",\n", "            \"frontend_facility_id\": \"unique_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "\n", "pan_ptype_all_df_backend_metrics = current_df[\n", "    [\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"backend_facility_id\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "    ]\n", "].drop_duplicates()\n", "pan_ptype_all_df_backend_metrics = (\n", "    pan_ptype_all_df_backend_metrics.groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"current_stock_backend\": \"sum\",\n", "            \"po_qty\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"open_po_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "pan_ptype_all_df = pd.merge(\n", "    pan_ptype_all_df,\n", "    pan_ptype_all_df_backend_metrics,\n", "    on=[\"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "# Identify PFMA Active assortment\n", "pan_ptype_master_active_df = (\n", "    current_df[current_df.master_assortment_active == 1]\n", "    .groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"master_active_item_facility\",\n", "            \"item_id\": \"unique_master_active_items\",\n", "            \"frontend_facility_id\": \"unique_master_active_facilities\",\n", "        }\n", "    )\n", ")\n", "# Identify TEA Active assortment\n", "pan_ptype_tea_active_df = (\n", "    current_df[current_df.tea_active == 1]\n", "    .groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"tea_active_item_facility\",\n", "            \"item_id\": \"unique_tea_active_items\",\n", "            \"frontend_facility_id\": \"unique_tea_active_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "\n", "pan_ptype_all_df[\"city\"] = \"All\"\n", "pan_ptype_master_active_df[\"city\"] = \"All\"\n", "pan_ptype_tea_active_df[\"city\"] = \"All\"\n", "\n", "pan_ptype_all_df = pd.merge(\n", "    pan_ptype_all_df,\n", "    pan_ptype_master_active_df,\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "pan_ptype_all_df = pd.merge(\n", "    pan_ptype_all_df,\n", "    pan_ptype_tea_active_df,\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "pan_ptype_all_df = pd.merge(\n", "    pan_ptype_all_df,\n", "    all_store_ptype_availability,\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "pan_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "] = pan_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "pan_ptype_all_df[\"availability\"] = (\n", "    pan_ptype_all_df[\"available_flag\"] / pan_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "pan_ptype_all_df[\"in_transit_availability\"] = (\n", "    pan_ptype_all_df[\"not_live_but_in_transit\"] / pan_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "pan_ptype_all_df[\"in_transit_availability\"] = np.where(\n", "    pan_ptype_all_df[\"in_transit_availability\"] > 1,\n", "    1,\n", "    pan_ptype_all_df[\"in_transit_availability\"],\n", ")\n", "\n", "\n", "pan_ptype_all_df = pan_ptype_all_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"go_live_date\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"available_flag\",\n", "        \"not_live_but_in_transit\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "# pan_ptype_all_df.head(4)\n", "\n", "pan_ptype_all_df"]}, {"cell_type": "code", "execution_count": null, "id": "f5658f7e-bb3f-4a4a-980b-a1f30671882f", "metadata": {}, "outputs": [], "source": ["city_ptype_all_df = pd.concat([pan_ptype_all_df, city_ptype_all_df])"]}, {"cell_type": "code", "execution_count": null, "id": "e21dec24-7f6f-4aba-92f3-2a9a7c61ba01", "metadata": {}, "outputs": [], "source": ["## facility_count"]}, {"cell_type": "code", "execution_count": null, "id": "de0bff80-402b-406a-9fb7-e4afa0f89b60", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "399b1f75-3e18-423f-b634-52ee67cb1fe0", "metadata": {}, "outputs": [], "source": ["# be_fe=backend_aggregated_base_df[backend_aggregated_base_df['tea_active']==1][['city','backend_facility_id','ptype','assortment_type','event_flag','frontend_facility_id']].groupby(\n", "#     ['city','backend_facility_id','assortment_type','event_flag','ptype']).agg({'frontend_facility_id':'nunique'}).reset_index()\n", "\n", "# city_fe=backend_aggregated_base_df[backend_aggregated_base_df['tea_active']==1][['city','assortment_type','event_flag','ptype','backend_facility_id','frontend_facility_id']].groupby(\n", "#     ['city','assortment_type','event_flag','ptype']).agg({'frontend_facility_id':'nunique'}).reset_index().rename(columns={'frontend_facility_id':'city_facility'})\n", "\n", "\n", "# backend_availability = backend_aggregated_base_df[\n", "#     [\n", "#         \"city\",\n", "#         \"assortment_type\",\n", "#         \"event_flag\",\n", "#         \"backend_facility_id\",\n", "#         \"backend_facility_name\",\n", "#         \"ptype\",\n", "#         \"item_id\",\n", "#         \"current_stock_backend\",\n", "#     ]\n", "# ].drop_duplicates()\n", "\n", "\n", "# ## merging facility count\n", "# backend_availability=pd.merge(backend_availability,be_fe,on=['city','backend_facility_id','assortment_type','event_flag','ptype'],how='left')\n", "# backend_availability['frontend_facility_id']=backend_availability['frontend_facility_id'].fillna(0)\n", "\n", "\n", "# ## merging total facilities in city\n", "\n", "# backend_availability=pd.merge(backend_availability,city_fe,how='left',on=['city','assortment_type','event_flag','ptype'])\n", "# backend_availability['city_facility']=backend_availability['city_facility'].fillna(0)\n", "\n", "# backend_availability['item_weights']=backend_availability['frontend_facility_id']/backend_availability['city_facility']\n", "# backend_availability['item_weights']=backend_availability['item_weights'].fillna(0)\n", "\n", "\n", "# backend_availability['item_weights']=backend_availability['item_weights']*np.where(backend_availability[\"current_stock_backend\"] > 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "f50b4460-ed42-403f-89b0-9be08b912d30", "metadata": {}, "outputs": [], "source": ["# backend_availability = backend_aggregated_base_df[\n", "#     [\n", "#         \"city\",\n", "#         \"assortment_type\",\n", "#         \"event_flag\",\n", "#         \"backend_facility_id\",\n", "#         \"backend_facility_name\",\n", "#         \"ptype\",\n", "#         \"item_id\",\n", "#         \"current_stock_backend\",\n", "#     ]\n", "# ].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "79511097-3581-4c66-8b6f-661d819969c4", "metadata": {}, "outputs": [], "source": ["# backend_availability = (\n", "#     backend_availability.groupby(\n", "#         by=[\"city\", \"assortment_type\", \"event_flag\",\"ptype\"]\n", "#     )\n", "#     .agg({\"item_weights\": \"mean\"})\n", "#     .reset_index()\n", "# )\n", "\n", "# weights=backend_availability.groupby(\n", "#         by=[\"city\",\"assortment_type\",\"event_flag\"]\n", "#     ).agg({\"item_weights\": \"sum\"}).reset_index()\n", "\n", "\n", "# backend_availability=backend_availability.merge(backend_availability,weight,on=[])"]}, {"cell_type": "code", "execution_count": null, "id": "96bb9fe8-78d2-43a2-ba03-1e6d00c28bd7", "metadata": {}, "outputs": [], "source": ["# backend_availability.rename(columns={'item_weights':'be_availability'},inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "63877b19-2849-4194-8b94-ea1d10335c66", "metadata": {}, "outputs": [], "source": ["# backend_availability = backend_aggregated_base_df[\n", "#     [\n", "#         \"city\",\n", "#         \"assortment_type\",\n", "#         \"event_flag\",\n", "#         \"backend_facility_id\",\n", "#         \"backend_facility_name\",\n", "#         \"ptype\",\n", "#         \"frontend_facility_id\"\n", "#         \"item_id\",\n", "#         \"current_stock_backend\",\n", "#     ]\n", "# ].drop_duplicates()\n", "\n", "# backend_availability=pd.merge(backend_availability,be_fe, how='left',on=['backend_facility_id'])\n", "\n", "# backend_availablility = (\n", "#     backend_availability.groupby(\n", "#         by=[\"city\", \"assortment_type\", \"event_flag\",\"backend_facility_id\",\"ptype\", \"item_id\"]\n", "#     )\n", "#     .agg({\"current_stock_backend\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "# backend_availability[\"availability_flag\"] = np.where(\n", "#     backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", "# )\n", "# backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "# backend_availability = (\n", "#     backend_availability.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "#     .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "\n", "# backend_availability[\"be_availability\"] = (\n", "#     backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "95ae1700-bd2b-4be7-93ac-6fbbc0094ae1", "metadata": {}, "outputs": [], "source": ["# backend_availability = backend_aggregated_base_df[\n", "#     [\n", "#         \"city\",\n", "#         \"assortment_type\",\n", "#         \"event_flag\",\n", "#         \"backend_facility_id\",\n", "#         \"backend_facility_name\",\n", "#         \"ptype\",\n", "#         \"frontend_facility_id\",\n", "#         \"item_id\",\n", "#         \"current_stock_backend\",\n", "#     ]\n", "# ].drop_duplicates()\n", "\n", "# #backend_availability=pd.merge(backend_availability,be_fe, how='left',on=['backend_facility_id'])\n", "\n", "# # backend_availablility = (\n", "# #     backend_availability.groupby(\n", "# #         by=[\"city\", \"assortment_type\", \"event_flag\",\"backend_facility_id\",\"ptype\", \"item_id\"]\n", "# #     )\n", "# #     .agg({\"current_stock_backend\": \"sum\"})\n", "# #     .reset_index()\n", "# # )\n", "\n", "\n", "# backend_availability[\"availability_flag\"] = np.where(\n", "#     backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", "# )\n", "# backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "# backend_availability = (\n", "#     backend_availability.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "#     .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "\n", "# backend_availability[\"be_availability\"] = (\n", "#     backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "1c449fb8-4fc5-4043-a282-9c86a7baf29c", "metadata": {}, "outputs": [], "source": ["# backend_availability[backend_availability['ptype']=='Bindi']"]}, {"cell_type": "code", "execution_count": null, "id": "2bf6f4c7-0c76-403c-ab60-4ebd38098a4a", "metadata": {}, "outputs": [], "source": ["# ## backend_availability\n", "\n", "# # backend_availability = backend_aggregated_base_df[\n", "# #     [\n", "# #         \"city\",\n", "# #         \"assortment_type\",\n", "# #         \"event_flag\",\n", "# #         \"backend_facility_id\",\n", "# #         \"backend_facility_name\",\n", "# #         \"ptype\",\n", "# #         \"item_id\",\n", "# #         \"current_stock_backend\",\n", "# #     ]\n", "# # ].drop_duplicates()\n", "\n", "# # backend_availability = (\n", "# #     backend_availability.groupby(\n", "# #         by=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\", \"item_id\"]\n", "# #     )\n", "# #     .agg({\"current_stock_backend\": \"sum\"})\n", "# #     .reset_index()\n", "# # )\n", "\n", "\n", "# # backend_availability[\"availability_flag\"] = np.where(\n", "# #     backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", "# # )\n", "# # backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "# # backend_availability = (\n", "# #     backend_availability.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "# #     .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "# #     .reset_index()\n", "# # )\n", "\n", "# # backend_availability[\"be_availability\"] = (\n", "# #     backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", "# # )\n", "\n", "\n", "# ## backend_availability_all\n", "\n", "\n", "# backend_availability_all = backend_aggregated_base_df[\n", "#     [\n", "#         \"city\",\n", "#         \"assortment_type\",\n", "#         \"event_flag\",\n", "#         \"backend_facility_id\",\n", "#         \"backend_facility_name\",\n", "#         \"ptype\",\n", "#         \"item_id\",\n", "#         \"current_stock_backend\",\n", "#         \"frontend_facility_id\"\n", "#     ]\n", "# ].drop_duplicates()\n", "\n", "# backend_availability_all[\"city\"] = \"All\"\n", "\n", "# backend_availability_all = (\n", "#     backend_availability_all.groupby(\n", "#         by=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\", \"item_id\"]\n", "#     )\n", "#     .agg({\"current_stock_backend\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "# backend_availability_all[\"availability_flag\"] = np.where(\n", "#     backend_availability_all[\"current_stock_backend\"] > 0, 1, 0\n", "# )\n", "# backend_availability_all[\"count_backend\"] = 1\n", "\n", "\n", "# backend_availability_all = (\n", "#     backend_availability_all.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "#     .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "\n", "# backend_availability_all[\"be_availability\"] = (\n", "#     backend_availability_all[\"availability_flag\"]\n", "#     / backend_availability_all[\"count_backend\"]\n", "# )\n", "\n", "\n", "# backend_availability_all = pd.concat([backend_availability, backend_availability_all])"]}, {"cell_type": "code", "execution_count": null, "id": "3562ea0b-f1eb-4090-84ed-8be479674f3d", "metadata": {}, "outputs": [], "source": ["## final backend availability\n", "\n", "backend_availability = backend_aggregated_base_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"ptype\",\n", "        \"frontend_facility_id\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "# backend_availability=pd.merge(backend_availability,be_fe, how='left',on=['backend_facility_id'])\n", "\n", "# backend_availablility = (\n", "#     backend_availability.groupby(\n", "#         by=[\"city\", \"assortment_type\", \"event_flag\",\"backend_facility_id\",\"ptype\", \"item_id\"]\n", "#     )\n", "#     .agg({\"current_stock_backend\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "backend_availability[\"availability_flag\"] = np.where(\n", "    backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability = (\n", "    backend_availability.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability[\"be_availability\"] = (\n", "    backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", ")\n", "\n", "\n", "backend_availability_all = backend_aggregated_base_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"ptype\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "        \"frontend_facility_id\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "backend_availability_all[\"city\"] = \"All\"\n", "\n", "# backend_availability_all = (\n", "#     backend_availability_all.groupby(\n", "#         by=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\", \"item_id\"]\n", "#     )\n", "#     .agg({\"current_stock_backend\": \"sum\"})\n", "#     .reset_index()\n", "# )\n", "\n", "\n", "backend_availability_all[\"availability_flag\"] = np.where(\n", "    backend_availability_all[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability_all[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability_all = (\n", "    backend_availability_all.groupby([\"city\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability_all[\"be_availability\"] = (\n", "    backend_availability_all[\"availability_flag\"] / backend_availability_all[\"count_backend\"]\n", ")\n", "\n", "\n", "backend_availability_all = pd.concat([backend_availability, backend_availability_all])"]}, {"cell_type": "markdown", "id": "c1011bd4-1dec-437f-858a-eefd3c15c114", "metadata": {}, "source": ["# City Ptype Summary"]}, {"cell_type": "code", "execution_count": null, "id": "2ef07866-7d03-4916-9a4a-004e77c88c84", "metadata": {}, "outputs": [], "source": ["city_ptype_summary = city_ptype_all_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "    ]\n", "]\n", "\n", "## city ptype availability\n", "\n", "city_ptype_summary = pd.merge(\n", "    city_ptype_summary,\n", "    backend_availability_all,\n", "    how=\"left\",\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", ")\n", "\n", "city_ptype_summary = city_ptype_summary[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"be_availability\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "    ]\n", "]\n", "\n", "\n", "pb.to_sheets(\n", "    city_ptype_summary,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"city_ptype_summary_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f19e63d0-47a6-4a6e-8df0-4ffee44c2217", "metadata": {}, "outputs": [], "source": ["city_ptype_per_summary = city_ptype_all_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"total_item_facility\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]\n", "\n", "\n", "city_ptype_per_summary = pd.merge(\n", "    city_ptype_per_summary,\n", "    backend_availability_all,\n", "    how=\"left\",\n", "    on=[\"city\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "26c80ba2-86d4-4d2a-8dca-045bac0c85e5", "metadata": {}, "outputs": [], "source": ["city_ptype_per_summary = city_ptype_per_summary[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"be_availability\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"total_item_facility\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "530a801f-3c30-4e30-8d70-8ffc88b65f93", "metadata": {}, "outputs": [], "source": ["city_ptype_per_summary[\"in_transit_availability\"] = np.where(\n", "    city_ptype_per_summary[\"in_transit_availability\"] > 1,\n", "    1,\n", "    city_ptype_per_summary[\"in_transit_availability\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "39fd1430-1257-4225-a711-c89284c2cdec", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    city_ptype_per_summary,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"city_ptype_perf_summary_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "991d3298-caa4-4f62-b7f2-1f393836c34a", "metadata": {}, "source": ["# Backend Ptype Summary"]}, {"cell_type": "code", "execution_count": null, "id": "842658ac-7c29-474a-b795-6a7cb743e4ce", "metadata": {}, "outputs": [], "source": ["# current_df[['backend_facility_id']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "27ce4aee-a337-400c-bb10-b2a3f8876237", "metadata": {}, "outputs": [], "source": ["# Store Level Availability\n", "store_ptype_availability = (\n", "    current_df.groupby(\n", "        [\n", "            \"backend_facility_id\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"ptype\",\n", "            \"frontend_facility_id\",\n", "        ]\n", "    )\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "store_ptype_availability[\"availability_stores\"] = (\n", "    store_ptype_availability[\"available_flag\"] / store_ptype_availability[\"count_flag\"]\n", ")\n", "\n", "store_ptype_availability[\"DS_item_avl\"] = np.where(\n", "    store_ptype_availability[\"available_flag\"] > 0, 1, 0\n", ")\n", "store_ptype_availability[\"DS_item_navl\"] = np.where(\n", "    store_ptype_availability[\"available_flag\"] <= 0, 1, 0\n", ")\n", "\n", "\n", "store_ptype_availability[\"ava_more_than_70\"] = np.where(\n", "    store_ptype_availability[\"availability_stores\"] > 0.7, 1, 0\n", ")\n", "store_ptype_availability[\"ava_more_than_90\"] = np.where(\n", "    store_ptype_availability[\"availability_stores\"] > 0.9, 1, 0\n", ")\n", "\n", "store_ptype_availability = (\n", "    store_ptype_availability.groupby(\n", "        [\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"ava_more_than_70\": \"sum\",\n", "            \"ava_more_than_90\": \"sum\",\n", "            \"DS_item_avl\": \"sum\",\n", "            \"DS_item_navl\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "\n", "# All City Metrics\n", "backend_ptype_all_df = (\n", "    current_df.groupby([\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"go_live_date\": \"mean\",\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"total_item_facility\",\n", "            \"item_id\": \"unique_items\",\n", "            \"frontend_facility_id\": \"unique_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "\n", "backend_ptype_all_df_backend_metrics = current_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "    ]\n", "].drop_duplicates()\n", "backend_ptype_all_df_backend_metrics = (\n", "    backend_ptype_all_df_backend_metrics.groupby(\n", "        [\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"]\n", "    )\n", "    .agg(\n", "        {\n", "            \"current_stock_backend\": \"sum\",\n", "            \"po_qty\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"open_po_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "backend_ptype_all_df = pd.merge(\n", "    backend_ptype_all_df,\n", "    backend_ptype_all_df_backend_metrics,\n", "    on=[\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "# Identify PFMA Active assortment\n", "backend_ptype_master_active_df = (\n", "    current_df[current_df.master_assortment_active == 1]\n", "    .groupby([\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"master_active_item_facility\",\n", "            \"item_id\": \"unique_master_active_items\",\n", "            \"frontend_facility_id\": \"unique_master_active_facilities\",\n", "        }\n", "    )\n", ")\n", "# Identify TEA Active assortment\n", "backend_ptype_tea_active_df = (\n", "    current_df[current_df.tea_active == 1]\n", "    .groupby([\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"tea_active_item_facility\",\n", "            \"item_id\": \"unique_tea_active_items\",\n", "            \"frontend_facility_id\": \"unique_tea_active_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "\n", "backend_ptype_all_df = pd.merge(\n", "    backend_ptype_all_df,\n", "    backend_ptype_master_active_df,\n", "    on=[\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "backend_ptype_all_df = pd.merge(\n", "    backend_ptype_all_df,\n", "    backend_ptype_tea_active_df,\n", "    on=[\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "backend_ptype_all_df = pd.merge(\n", "    backend_ptype_all_df,\n", "    store_ptype_availability,\n", "    on=[\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "backend_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "] = backend_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "backend_ptype_all_df[\"availability\"] = (\n", "    backend_ptype_all_df[\"available_flag\"] / backend_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "backend_ptype_all_df[\"in_transit_availability\"] = (\n", "    backend_ptype_all_df[\"not_live_but_in_transit\"] / backend_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "backend_ptype_all_df[\"in_transit_availability\"] = np.where(\n", "    backend_ptype_all_df[\"in_transit_availability\"] > 1,\n", "    1,\n", "    backend_ptype_all_df[\"in_transit_availability\"],\n", ")\n", "\n", "\n", "facility_name_mapping_query = (\n", "    f\"\"\"SELECT id as facility_id, name as facility_name FROM lake_crates.facility\"\"\"\n", ")\n", "facility_name_mapping_df = read_sql_query(facility_name_mapping_query, CON_REDSHIFT)\n", "facility_name_mapping_df.head(1)\n", "\n", "backend_ptype_all_df = pd.merge(\n", "    backend_ptype_all_df,\n", "    facility_name_mapping_df.rename(\n", "        columns={\n", "            \"facility_id\": \"backend_facility_id\",\n", "            \"facility_name\": \"backend_facility_name\",\n", "        }\n", "    ),\n", "    on=[\"backend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "# Change 29th\n", "backend_ptype_all_df = backend_ptype_all_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"go_live_date\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"available_flag\",\n", "        \"not_live_but_in_transit\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"DS_item_avl\",\n", "        \"DS_item_navl\",\n", "    ]\n", "]\n", "\n", "backend_ptype_all_df"]}, {"cell_type": "code", "execution_count": null, "id": "a5fd6e56-afd0-481f-a5ca-ad099faca7bd", "metadata": {}, "outputs": [], "source": ["backend_ptype_all_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "20ce1901-654b-497d-98b3-45bf8522a2bd", "metadata": {}, "outputs": [], "source": ["all_store_ptype_availability"]}, {"cell_type": "code", "execution_count": null, "id": "f5044df3-6210-4794-8799-60250c6230c1", "metadata": {}, "outputs": [], "source": ["# Store Level Availability\n", "all_store_ptype_availability = (\n", "    current_df.groupby([\"assortment_type\", \"event_flag\", \"ptype\", \"frontend_facility_id\"])\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "all_store_ptype_availability[\"availability_stores\"] = (\n", "    all_store_ptype_availability[\"available_flag\"] / all_store_ptype_availability[\"count_flag\"]\n", ")\n", "\n", "\n", "all_store_ptype_availability[\"ava_more_than_70\"] = np.where(\n", "    all_store_ptype_availability[\"availability_stores\"] > 0.7, 1, 0\n", ")\n", "all_store_ptype_availability[\"ava_more_than_90\"] = np.where(\n", "    all_store_ptype_availability[\"availability_stores\"] > 0.9, 1, 0\n", ")\n", "\n", "all_store_ptype_availability[\"DS_item_avl\"] = np.where(\n", "    all_store_ptype_availability[\"available_flag\"] > 0, 1, 0\n", ")\n", "all_store_ptype_availability[\"DS_item_navl\"] = np.where(\n", "    all_store_ptype_availability[\"available_flag\"] <= 0, 1, 0\n", ")\n", "\n", "\n", "all_store_ptype_availability = (\n", "    all_store_ptype_availability.groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"ava_more_than_70\": \"sum\",\n", "            \"ava_more_than_90\": \"sum\",\n", "            \"DS_item_avl\": \"sum\",\n", "            \"DS_item_navl\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "all_store_ptype_availability[\"backend_facility_id\"] = -1\n", "\n", "\n", "# All City Metrics\n", "all_backend_ptype_all_df = (\n", "    current_df.groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"go_live_date\": \"mean\",\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "            \"current_stock\": \"sum\",\n", "            \"inventory_in_transit\": \"sum\",\n", "            \"sto_billing_pending\": \"sum\",\n", "            \"sto_dispatch_pending\": \"sum\",\n", "            \"sto_arrival_pending\": \"sum\",\n", "            \"sto_grn_pending\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"current_day_qty_sold\": \"sum\",\n", "            \"current_day_gmv\": \"sum\",\n", "            \"current_day_carts\": \"sum\",\n", "            \"t_1_qty_sold\": \"sum\",\n", "            \"t_1_gmv\": \"sum\",\n", "            \"t_1_carts\": \"sum\",\n", "            \"t_7_qty_sold\": \"sum\",\n", "            \"t_7_gmv\": \"sum\",\n", "            \"t_7_carts\": \"sum\",\n", "            \"qty_sold_till_date\": \"sum\",\n", "            \"gmv_till_date\": \"sum\",\n", "            \"carts_till_date\": \"sum\",\n", "            \"current_day_dump\": \"sum\",\n", "            \"current_day_dump_value\": \"sum\",\n", "            \"t_1_dump\": \"sum\",\n", "            \"t_1_dump_value\": \"sum\",\n", "            \"t_7_dump\": \"sum\",\n", "            \"t_7_dump_value\": \"sum\",\n", "            \"dump_till_date\": \"sum\",\n", "            \"dump_value_till_date\": \"sum\",\n", "            \"t_new_inward_drop\": \"sum\",\n", "            \"t_new_storage_drop\": \"sum\",\n", "            \"t_new_truck_load_drop\": \"sum\",\n", "            \"t_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t_new_loose_quantity_drop\": \"sum\",\n", "            \"t1_new_inward_drop\": \"sum\",\n", "            \"t1_new_storage_drop\": \"sum\",\n", "            \"t1_new_truck_load_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t1_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t1_new_loose_quantity_drop\": \"sum\",\n", "            \"t7_new_inward_drop\": \"sum\",\n", "            \"t7_new_storage_drop\": \"sum\",\n", "            \"t7_new_truck_load_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_sku_drop\": \"sum\",\n", "            \"t7_new_picking_capacity_quantity_drop\": \"sum\",\n", "            \"t7_new_loose_quantity_drop\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"not_live_but_in_transit\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"total_item_facility\",\n", "            \"item_id\": \"unique_items\",\n", "            \"frontend_facility_id\": \"unique_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "\n", "all_backend_ptype_all_df_backend_metrics = current_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "    ]\n", "].drop_duplicates()\n", "all_backend_ptype_all_df_backend_metrics = (\n", "    all_backend_ptype_all_df_backend_metrics.groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"current_stock_backend\": \"sum\",\n", "            \"po_qty\": \"sum\",\n", "            \"grn_qty\": \"sum\",\n", "            \"open_po_qty\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "all_backend_ptype_all_df = pd.merge(\n", "    all_backend_ptype_all_df,\n", "    all_backend_ptype_all_df_backend_metrics,\n", "    on=[\"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "# Identify PFMA Active assortment\n", "all_backend_ptype_master_active_df = (\n", "    current_df[current_df.master_assortment_active == 1]\n", "    .groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"master_active_item_facility\",\n", "            \"item_id\": \"unique_master_active_items\",\n", "            \"frontend_facility_id\": \"unique_master_active_facilities\",\n", "        }\n", "    )\n", ")\n", "# Identify TEA Active assortment\n", "all_backend_ptype_tea_active_df = (\n", "    current_df[current_df.tea_active == 1]\n", "    .groupby([\"assortment_type\", \"event_flag\", \"ptype\"])\n", "    .agg(\n", "        {\n", "            \"item_id\": \"nunique\",\n", "            \"frontend_facility_id\": \"nunique\",\n", "            \"count_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"count_flag\": \"tea_active_item_facility\",\n", "            \"item_id\": \"unique_tea_active_items\",\n", "            \"frontend_facility_id\": \"unique_tea_active_facilities\",\n", "        }\n", "    )\n", ")\n", "\n", "all_backend_ptype_all_df[\"backend_facility_id\"] = -1\n", "all_backend_ptype_master_active_df[\"backend_facility_id\"] = -1\n", "all_backend_ptype_tea_active_df[\"backend_facility_id\"] = -1\n", "\n", "\n", "all_backend_ptype_all_df = pd.merge(\n", "    all_backend_ptype_all_df,\n", "    all_backend_ptype_master_active_df,\n", "    on=[\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "all_backend_ptype_all_df = pd.merge(\n", "    all_backend_ptype_all_df,\n", "    all_backend_ptype_tea_active_df,\n", "    on=[\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "all_backend_ptype_all_df = pd.merge(\n", "    all_backend_ptype_all_df,\n", "    all_store_ptype_availability,\n", "    on=[\"backend_facility_id\", \"assortment_type\", \"event_flag\", \"ptype\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "all_backend_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "] = all_backend_ptype_all_df[\n", "    [\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "all_backend_ptype_all_df[\"availability\"] = (\n", "    all_backend_ptype_all_df[\"available_flag\"] / all_backend_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "all_backend_ptype_all_df[\"in_transit_availability\"] = (\n", "    all_backend_ptype_all_df[\"not_live_but_in_transit\"]\n", "    / all_backend_ptype_all_df[\"total_item_facility\"]\n", ")\n", "\n", "\n", "all_backend_ptype_all_df[\"in_transit_availability\"] = np.where(\n", "    all_backend_ptype_all_df[\"in_transit_availability\"] > 1,\n", "    1,\n", "    all_backend_ptype_all_df[\"in_transit_availability\"],\n", ")\n", "\n", "\n", "# facility_name_mapping_query = f\"\"\"SELECT id as facility_id, name as facility_name FROM lake_crates.facility\"\"\"\n", "# facility_name_mapping_df = read_sql_query(facility_name_mapping_query, CON_REDSHIFT)\n", "# facility_name_mapping_df.head(1)\n", "\n", "# all_backend_ptype_all_df = pd.merge(all_backend_ptype_all_df, facility_name_mapping_df.rename(columns = {'facility_id':'backend_facility_id','facility_name':'backend_facility_name'}), on = ['backend_facility_id'], how = 'left')\n", "\n", "all_backend_ptype_all_df[\"backend_facility_name\"] = \"All\"\n", "\n", "all_backend_ptype_all_df = all_backend_ptype_all_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"go_live_date\",\n", "        \"availability\",\n", "        \"in_transit_availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"available_flag\",\n", "        \"not_live_but_in_transit\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"unique_tea_active_items\",\n", "        \"unique_tea_active_facilities\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"DS_item_avl\",\n", "        \"DS_item_navl\",\n", "    ]\n", "]\n", "\n", "all_backend_ptype_all_df"]}, {"cell_type": "code", "execution_count": null, "id": "d17ce2e6-1fb9-4e1d-ab39-7577c8580b44", "metadata": {}, "outputs": [], "source": ["backend_ptype_all_df = pd.concat([all_backend_ptype_all_df, backend_ptype_all_df])"]}, {"cell_type": "markdown", "id": "7b3c7f6a-bf2a-4eac-9d91-075de60a015e", "metadata": {}, "source": ["# Backend Ptype Summary"]}, {"cell_type": "code", "execution_count": null, "id": "83712914-ee35-4f62-8652-820acdddf938", "metadata": {}, "outputs": [], "source": ["## backend_availability\n", "\n", "backend_availability = backend_aggregated_base_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "\n", "backend_availability[\"availability_flag\"] = np.where(\n", "    backend_availability[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability = (\n", "    backend_availability.groupby(\n", "        [\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"ptype\",\n", "        ]\n", "    )\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability[\"be_availability\"] = (\n", "    backend_availability[\"availability_flag\"] / backend_availability[\"count_backend\"]\n", ")\n", "\n", "\n", "## backend_availability_all\n", "\n", "\n", "backend_availability_all = backend_aggregated_base_df[\n", "    [\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"ptype\",\n", "        \"item_id\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "backend_availability_all[\"backend_facility_id\"] = -1\n", "backend_availability_all[\"backend_facility_name\"] = \"All\"\n", "\n", "\n", "backend_availability_all[\"availability_flag\"] = np.where(\n", "    backend_availability_all[\"current_stock_backend\"] > 0, 1, 0\n", ")\n", "backend_availability_all[\"count_backend\"] = 1\n", "\n", "\n", "backend_availability_all = (\n", "    backend_availability_all.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"event_flag\",\n", "            \"backend_facility_id\",\n", "            \"backend_facility_name\",\n", "            \"ptype\",\n", "        ]\n", "    )\n", "    .agg({\"availability_flag\": \"sum\", \"count_backend\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "backend_availability_all[\"be_availability\"] = (\n", "    backend_availability_all[\"availability_flag\"] / backend_availability_all[\"count_backend\"]\n", ")\n", "\n", "\n", "backend_availability_all = pd.concat([backend_availability, backend_availability_all])"]}, {"cell_type": "code", "execution_count": null, "id": "5ba8aaf2-5ad9-4631-97db-bde4252dce11", "metadata": {}, "outputs": [], "source": ["backend_availability_all"]}, {"cell_type": "code", "execution_count": null, "id": "07e97fe4-cf15-4263-a0d3-5e1b5cd67e56", "metadata": {}, "outputs": [], "source": ["backend_ptype_summary = backend_ptype_all_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"availability\",\n", "        \"inventory_in_transit\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "        \"DS_item_avl\",\n", "        \"DS_item_navl\",\n", "    ]\n", "]\n", "\n", "backend_ptype_summary = pd.merge(\n", "    backend_ptype_summary,\n", "    backend_availability_all,\n", "    on=[\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "backend_ptype_summary = backend_ptype_summary[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"be_availability\",\n", "        \"availability\",\n", "        \"inventory_in_transit\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"unique_items\",\n", "        \"unique_facilities\",\n", "        \"total_item_facility\",\n", "        \"unique_master_active_items\",\n", "        \"unique_master_active_facilities\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "        \"DS_item_avl\",\n", "        \"DS_item_navl\",\n", "    ]\n", "]\n", "\n", "pb.to_sheets(\n", "    backend_ptype_summary,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"backend_ptype_summary_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4963cd78-68ad-4d50-badc-cb1ea2738cfe", "metadata": {}, "outputs": [], "source": ["backend_ptype_per_summary = backend_ptype_all_df[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"total_item_facility\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"DS_item_avl\",\n", "        \"DS_item_navl\",\n", "    ]\n", "]\n", "\n", "backend_ptype_per_summary = pd.merge(\n", "    backend_ptype_per_summary,\n", "    backend_availability_all,\n", "    on=[\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "backend_ptype_per_summary = backend_ptype_per_summary[\n", "    [\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"assortment_type\",\n", "        \"event_flag\",\n", "        \"ptype\",\n", "        \"be_availability\",\n", "        \"availability\",\n", "        \"ava_more_than_70\",\n", "        \"ava_more_than_90\",\n", "        \"total_item_facility\",\n", "        \"master_active_item_facility\",\n", "        \"tea_active_item_facility\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"DS_item_avl\",\n", "        \"DS_item_navl\",\n", "    ]\n", "]\n", "\n", "\n", "backend_ptype_per_summary\n", "pb.to_sheets(\n", "    backend_ptype_per_summary,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"backend_ptype_perf_summary_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e8fd2a79-ca33-4f16-b314-05dd3ac0c45f", "metadata": {}, "outputs": [], "source": ["backend_ptype_per_summary"]}, {"cell_type": "markdown", "id": "2c9b5e38-2e9a-4285-9b1f-db0d33de06b2", "metadata": {}, "source": ["# City Store Ptype Availability"]}, {"cell_type": "code", "execution_count": null, "id": "aa4c21c7-3be2-48a1-b03e-083a307a4f70", "metadata": {}, "outputs": [], "source": ["current_df = pd.merge(current_df, item_category_details, on=[\"item_id\"], how=\"left\")"]}, {"cell_type": "markdown", "id": "fc4e2515-a08d-468d-93e3-8f9725df241a", "metadata": {}, "source": ["# Diwali Availability"]}, {"cell_type": "code", "execution_count": null, "id": "4b9f7d6a-d54b-4eff-b93f-d9614821010b", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"l1\",\n", "        ]\n", "    )\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_ptype_availability[\"availability_stores\"] = (\n", "    overall_ptype_availability[\"available_flag\"] / overall_ptype_availability[\"count_flag\"]\n", ")\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Diwali\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"availability_stores\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"overall_ptype_summary_raw\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "code", "execution_count": null, "id": "0eac74b8-cb8f-46d5-83d4-55289bd24a94", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby([\"assortment_type\", \"l1\", \"city\"])\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_ptype_availability[\"availability_stores\"] = (\n", "    overall_ptype_availability[\"available_flag\"] / overall_ptype_availability[\"count_flag\"]\n", ")\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Diwali\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"city\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"availability_stores\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"city\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"city_ptype_summary_raw_matrix\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "markdown", "id": "bbda2b72-f615-4ada-b818-99e273e3a554", "metadata": {}, "source": ["### Diwali Store"]}, {"cell_type": "code", "execution_count": null, "id": "ebc185f2-d78b-4363-bec5-ac388196e2ae", "metadata": {}, "outputs": [], "source": ["store_ptype_availability = (\n", "    current_df.groupby([\"city\", \"assortment_type\", \"l1\", \"frontend_facility_id\"])\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "store_ptype_availability[\"availability_stores\"] = (\n", "    store_ptype_availability[\"available_flag\"] / store_ptype_availability[\"count_flag\"]\n", ")\n", "store_ptype_availability = pd.merge(\n", "    store_ptype_availability,\n", "    facility_name_mapping_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=[\"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "store_ptype_availability = store_ptype_availability[\n", "    store_ptype_availability[\"assortment_type\"] == \"Diwali\"\n", "]\n", "store_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = store_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "store_ptype_availability = store_ptype_availability.pivot_table(\n", "    index=[\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key\",\n", "    ],\n", "    columns=[\"l1\"],\n", "    values=[\"availability_stores\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "store_ptype_availability = pd.merge(store_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\")\n", "store_ptype_availability.columns = (\n", "    [\n", "        \"dummy_key\",\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key_0\",\n", "    ]\n", "    + list(ptypes[\"l1\"])\n", "    + [\"dummy_value\"]\n", ")\n", "\n", "store_ptype_availability = store_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    store_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"store_ptype_summary_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3334a19e-d306-4c14-8a05-005efe1ea7a1", "metadata": {}, "outputs": [], "source": ["current_df.head(1)"]}, {"cell_type": "markdown", "id": "445056fe-721f-4a30-89ac-2cd590a5af26", "metadata": {}, "source": ["# Diwali SaLES"]}, {"cell_type": "code", "execution_count": null, "id": "27a225ea-cccd-4d5b-a905-6286622295e6", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"l1\",\n", "        ]\n", "    )\n", "    .agg({\"qty_sold_till_date\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Diwali\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"qty_sold_till_date\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"overall_ptype_summary_raw_sold\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "code", "execution_count": null, "id": "ab23d11d-73a0-413c-a25e-0a6f7a35d311", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby([\"assortment_type\", \"l1\", \"city\"])\n", "    .agg({\"qty_sold_till_date\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Diwali\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"city\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"qty_sold_till_date\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"city\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"city_ptype_summary_raw_matrix_sold\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "markdown", "id": "37fcaf7c-cb7e-4cb9-8e39-f7ad477bea70", "metadata": {}, "source": ["### Diwali Store"]}, {"cell_type": "code", "execution_count": null, "id": "29366bc1-94a1-4318-b131-224248585d00", "metadata": {}, "outputs": [], "source": ["store_ptype_availability = (\n", "    current_df.groupby([\"city\", \"assortment_type\", \"l1\", \"frontend_facility_id\"])\n", "    .agg({\"qty_sold_till_date\": \"sum\"})\n", "    .reset_index()\n", ")\n", "store_ptype_availability = pd.merge(\n", "    store_ptype_availability,\n", "    facility_name_mapping_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=[\"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "store_ptype_availability = store_ptype_availability[\n", "    store_ptype_availability[\"assortment_type\"] == \"Diwali\"\n", "]\n", "store_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = store_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "store_ptype_availability = store_ptype_availability.pivot_table(\n", "    index=[\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key\",\n", "    ],\n", "    columns=[\"l1\"],\n", "    values=[\"qty_sold_till_date\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "store_ptype_availability = pd.merge(store_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\")\n", "store_ptype_availability.columns = (\n", "    [\n", "        \"dummy_key\",\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key_0\",\n", "    ]\n", "    + list(ptypes[\"l1\"])\n", "    + [\"dummy_value\"]\n", ")\n", "\n", "store_ptype_availability = store_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    store_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"store_ptype_summary_raw_sold\",\n", ")"]}, {"cell_type": "markdown", "id": "e9af3442-6aa0-43a7-b208-47a763f32bfc", "metadata": {}, "source": ["### Navratri Store"]}, {"cell_type": "code", "execution_count": null, "id": "9ea0d741-dee6-457f-ab40-c28999d10060", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"l1\",\n", "        ]\n", "    )\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_ptype_availability[\"availability_stores\"] = (\n", "    overall_ptype_availability[\"available_flag\"] / overall_ptype_availability[\"count_flag\"]\n", ")\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Navratri\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"availability_stores\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"nav_overall_ptype_summary_raw\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "code", "execution_count": null, "id": "252fec06-1a69-4b0c-ba1a-70dbe05486d1", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby([\"assortment_type\", \"l1\", \"city\"])\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_ptype_availability[\"availability_stores\"] = (\n", "    overall_ptype_availability[\"available_flag\"] / overall_ptype_availability[\"count_flag\"]\n", ")\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Navratri\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"city\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"availability_stores\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"city\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"nav_city_ptype_summary_raw_matrix\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "code", "execution_count": null, "id": "27b13749-9ac1-49a1-8de5-f7789fd1852e", "metadata": {}, "outputs": [], "source": ["store_ptype_availability = (\n", "    current_df.groupby([\"city\", \"assortment_type\", \"l1\", \"frontend_facility_id\"])\n", "    .agg({\"available_flag\": \"sum\", \"count_flag\": \"sum\"})\n", "    .reset_index()\n", ")\n", "store_ptype_availability[\"availability_stores\"] = (\n", "    store_ptype_availability[\"available_flag\"] / store_ptype_availability[\"count_flag\"]\n", ")\n", "store_ptype_availability = pd.merge(\n", "    store_ptype_availability,\n", "    facility_name_mapping_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=[\"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "store_ptype_availability = store_ptype_availability[\n", "    store_ptype_availability[\"assortment_type\"] == \"Navratri\"\n", "]\n", "store_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = store_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "store_ptype_availability = store_ptype_availability.pivot_table(\n", "    index=[\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key\",\n", "    ],\n", "    columns=[\"l1\"],\n", "    values=[\"availability_stores\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "store_ptype_availability = pd.merge(store_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\")\n", "store_ptype_availability.columns = (\n", "    [\n", "        \"dummy_key\",\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key_0\",\n", "    ]\n", "    + list(ptypes[\"l1\"])\n", "    + [\"dummy_value\"]\n", ")\n", "\n", "store_ptype_availability = store_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    store_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"navratri_store_ptype_summary_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "47b013c9-fb81-4f51-aea6-3984056cc6cd", "metadata": {}, "source": ["# Navratri Sales"]}, {"cell_type": "code", "execution_count": null, "id": "64ddadd2-0ef0-44db-ab66-97a175708148", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby(\n", "        [\n", "            \"assortment_type\",\n", "            \"l1\",\n", "        ]\n", "    )\n", "    .agg({\"qty_sold_till_date\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Navratri\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"qty_sold_till_date\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"nav_overall_ptype_summary_raw_sold\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "code", "execution_count": null, "id": "480397f9-e6c7-4cd3-ae6f-7ec8b5c784f5", "metadata": {}, "outputs": [], "source": ["overall_ptype_availability = (\n", "    current_df.groupby([\"assortment_type\", \"l1\", \"city\"])\n", "    .agg({\"qty_sold_till_date\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability[\n", "    overall_ptype_availability[\"assortment_type\"] == \"Navratri\"\n", "]\n", "overall_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = overall_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "overall_ptype_availability = overall_ptype_availability.pivot_table(\n", "    index=[\"assortment_type\", \"city\", \"dummy_key\"],\n", "    columns=[\"l1\"],\n", "    values=[\"qty_sold_till_date\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "overall_ptype_availability = pd.merge(\n", "    overall_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\"\n", ")\n", "overall_ptype_availability.columns = (\n", "    [\"dummy_key\", \"assortment_type\", \"city\", \"dummy_key_0\"] + list(ptypes[\"l1\"]) + [\"dummy_value\"]\n", ")\n", "\n", "overall_ptype_availability = overall_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    overall_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"nav_city_ptype_summary_raw_matrix_sold\",\n", ")\n", "overall_ptype_availability"]}, {"cell_type": "code", "execution_count": null, "id": "ac345c20-fd27-4710-bc5e-73eaaa4c1b96", "metadata": {}, "outputs": [], "source": ["store_ptype_availability = (\n", "    current_df.groupby([\"city\", \"assortment_type\", \"l1\", \"frontend_facility_id\"])\n", "    .agg({\"qty_sold_till_date\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "store_ptype_availability = pd.merge(\n", "    store_ptype_availability,\n", "    facility_name_mapping_df.rename(columns={\"facility_id\": \"frontend_facility_id\"}),\n", "    on=[\"frontend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "store_ptype_availability = store_ptype_availability[\n", "    store_ptype_availability[\"assortment_type\"] == \"Navratri\"\n", "]\n", "store_ptype_availability[\"dummy_key\"] = 1\n", "\n", "ptypes = store_ptype_availability[[\"l1\"]].drop_duplicates().sort_values(by=\"l1\")\n", "\n", "store_ptype_availability = store_ptype_availability.pivot_table(\n", "    index=[\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key\",\n", "    ],\n", "    columns=[\"l1\"],\n", "    values=[\"qty_sold_till_date\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "dummy = (\n", "    facility_name_mapping_df[[\"facility_id\"]]\n", "    .head(1)\n", "    .copy()\n", "    .rename(columns={\"facility_id\": \"dummy_key\"})\n", ")\n", "dummy[\"dummy_key\"] = 1\n", "dummy[\"dummy_value\"] = 1\n", "\n", "store_ptype_availability = pd.merge(store_ptype_availability, dummy, on=[\"dummy_key\"], how=\"left\")\n", "store_ptype_availability.columns = (\n", "    [\n", "        \"dummy_key\",\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"frontend_facility_id\",\n", "        \"facility_name\",\n", "        \"dummy_key_0\",\n", "    ]\n", "    + list(ptypes[\"l1\"])\n", "    + [\"dummy_value\"]\n", ")\n", "\n", "store_ptype_availability = store_ptype_availability.drop(\n", "    columns={\"dummy_key\", \"dummy_key_0\", \"dummy_value\"}\n", ")\n", "\n", "pb.to_sheets(\n", "    store_ptype_availability,\n", "    \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\",\n", "    \"navratri_store_ptype_summary_raw_sold\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ddb1fe39-a8e8-48ef-a373-7d6595976912", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "83a5048c-938a-439a-b123-982423f05eaf", "metadata": {}, "outputs": [], "source": ["final_df[\"date_time\"] = pd.to_datetime(datetime.now() + timedelta(minutes=330))\n", "final_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "1bf8645b-4522-473b-badb-e57c7cce0487", "metadata": {}, "outputs": [], "source": ["final_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "d3fba4dc-9085-4ba6-9329-2cb8fcd6d12c", "metadata": {}, "outputs": [], "source": ["final_df[\n", "    [\n", "        \"city\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"ptype\",\n", "        \"go_live_date\",\n", "        \"grn_at_ds\",\n", "        \"transfer_to_ds\",\n", "        \"grn_at_backend\",\n", "        \"po_creation\",\n", "        \"tea_status\",\n", "        \"activation_status\",\n", "        \"frontend_facility_id\",\n", "        \"city_id\",\n", "        \"master_assortment_active\",\n", "        \"backend_facility_id\",\n", "        \"tea_active\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_gmv\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_gmv\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump\",\n", "        \"t_7_dump_value\",\n", "        \"dump_till_date\",\n", "        \"dump_value_till_date\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"available_flag\",\n", "        \"not_live_but_in_transit\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"open_po_qty\",\n", "        \"date_time\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "549b2376-b5d9-483f-8488-f9d133f71c74", "metadata": {}, "outputs": [], "source": ["final_df[\n", "    [\n", "        \"item_id\",\n", "        \"frontend_facility_id\",\n", "        \"city_id\",\n", "        \"master_assortment_active\",\n", "        \"backend_facility_id\",\n", "        \"tea_active\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"inventory_in_transit\",\n", "        \"sto_billing_pending\",\n", "        \"sto_dispatch_pending\",\n", "        \"sto_arrival_pending\",\n", "        \"sto_grn_pending\",\n", "        \"discrepancy_marked\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_carts\",\n", "        \"t_1_qty_sold\",\n", "        \"t_1_carts\",\n", "        \"t_7_qty_sold\",\n", "        \"t_7_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"carts_till_date\",\n", "        \"current_day_dump\",\n", "        \"t_1_dump\",\n", "        \"t_7_dump\",\n", "        \"dump_till_date\",\n", "        \"available_flag\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"po_qty\",\n", "        \"grn_qty\",\n", "        \"open_po_qty\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "    ]\n", "] = (\n", "    final_df[\n", "        [\n", "            \"item_id\",\n", "            \"frontend_facility_id\",\n", "            \"city_id\",\n", "            \"master_assortment_active\",\n", "            \"backend_facility_id\",\n", "            \"tea_active\",\n", "            \"current_stock_backend\",\n", "            \"current_stock\",\n", "            \"inventory_in_transit\",\n", "            \"sto_billing_pending\",\n", "            \"sto_dispatch_pending\",\n", "            \"sto_arrival_pending\",\n", "            \"sto_grn_pending\",\n", "            \"discrepancy_marked\",\n", "            \"current_day_qty_sold\",\n", "            \"current_day_carts\",\n", "            \"t_1_qty_sold\",\n", "            \"t_1_carts\",\n", "            \"t_7_qty_sold\",\n", "            \"t_7_carts\",\n", "            \"qty_sold_till_date\",\n", "            \"carts_till_date\",\n", "            \"current_day_dump\",\n", "            \"t_1_dump\",\n", "            \"t_7_dump\",\n", "            \"dump_till_date\",\n", "            \"available_flag\",\n", "            \"min_quantity\",\n", "            \"max_quantity\",\n", "            \"po_qty\",\n", "            \"grn_qty\",\n", "            \"open_po_qty\",\n", "            \"t_new_inward_drop\",\n", "            \"t_new_storage_drop\",\n", "            \"t_new_truck_load_drop\",\n", "            \"t_new_picking_capacity_sku_drop\",\n", "            \"t_new_picking_capacity_quantity_drop\",\n", "            \"t_new_loose_quantity_drop\",\n", "            \"t1_new_inward_drop\",\n", "            \"t1_new_storage_drop\",\n", "            \"t1_new_truck_load_drop\",\n", "            \"t1_new_picking_capacity_sku_drop\",\n", "            \"t1_new_picking_capacity_quantity_drop\",\n", "            \"t1_new_loose_quantity_drop\",\n", "            \"t7_new_inward_drop\",\n", "            \"t7_new_storage_drop\",\n", "            \"t7_new_truck_load_drop\",\n", "            \"t7_new_picking_capacity_sku_drop\",\n", "            \"t7_new_picking_capacity_quantity_drop\",\n", "            \"t7_new_loose_quantity_drop\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "\n", "final_df[\n", "    [\n", "        \"current_day_gmv\",\n", "        \"t_1_gmv\",\n", "        \"t_7_gmv\",\n", "        \"gmv_till_date\",\n", "        \"current_day_dump_value\",\n", "        \"t_1_dump_value\",\n", "        \"t_7_dump_value\",\n", "        \"dump_value_till_date\",\n", "    ]\n", "] = (\n", "    final_df[\n", "        [\n", "            \"current_day_gmv\",\n", "            \"t_1_gmv\",\n", "            \"t_7_gmv\",\n", "            \"gmv_till_date\",\n", "            \"current_day_dump_value\",\n", "            \"t_1_dump_value\",\n", "            \"t_7_dump_value\",\n", "            \"dump_value_till_date\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "\n", "final_df[\"go_live_date\"] = pd.to_datetime(final_df[\"go_live_date\"])\n", "final_df[\"grn_at_ds\"] = pd.to_datetime(final_df[\"grn_at_ds\"])\n", "final_df[\"transfer_to_ds\"] = pd.to_datetime(final_df[\"transfer_to_ds\"])\n", "final_df[\"grn_at_backend\"] = pd.to_datetime(final_df[\"grn_at_backend\"])\n", "final_df[\"po_creation\"] = pd.to_datetime(final_df[\"po_creation\"])\n", "final_df[\"tea_status\"] = pd.to_datetime(final_df[\"tea_status\"])\n", "final_df[\"activation_status\"] = pd.to_datetime(final_df[\"activation_status\"])\n", "# final_df['po_schedule_dates'] = pd.to_datetime(final_df['po_schedule_dates'])"]}, {"cell_type": "code", "execution_count": null, "id": "81f7c3a0-42e1-4a02-9a2f-ab4a1c478162", "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "dc97dcd5-d635-4a1a-9cff-3c555aab5b24", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "345e2a6e-3d15-41c9-ae8c-b36f8bc04bbe", "metadata": {}, "outputs": [], "source": ["final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "da6b9d04-2443-4788-9177-7917bc6ec588", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"name of city\"},\n", "    {\"name\": \"event_flag\", \"type\": \"varchar(10)\", \"description\": \"type of assortment\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar(200)\", \"description\": \"type of event\"},\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"unique identifier for item\"},\n", "    {\"name\": \"ptype\", \"type\": \"varchar(200)\", \"description\": \"ptype\"},\n", "    {\"name\": \"go_live_date\", \"type\": \"datetime\", \"description\": \"go live date for ds\"},\n", "    {\"name\": \"grn_at_ds\", \"type\": \"datetime\", \"description\": \"grn_at_ds\"},\n", "    {\"name\": \"transfer_to_ds\", \"type\": \"datetime\", \"description\": \"transfer_to_ds\"},\n", "    {\"name\": \"grn_at_backend\", \"type\": \"datetime\", \"description\": \"grn_at_backend\"},\n", "    {\"name\": \"po_creation\", \"type\": \"datetime\", \"description\": \"po_creation\"},\n", "    {\"name\": \"tea_status\", \"type\": \"datetime\", \"description\": \"tea_status\"},\n", "    {\n", "        \"name\": \"activation_status\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"activation_status\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for DS facility\",\n", "    },\n", "    {\"name\": \"city_id\", \"type\": \"int\", \"description\": \"unique identifier for city\"},\n", "    {\n", "        \"name\": \"master_assortment_active\",\n", "        \"type\": \"int\",\n", "        \"description\": \"master_assortment_active flag\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for backend facility\",\n", "    },\n", "    {\"name\": \"tea_active\", \"type\": \"int\", \"description\": \"tea status flag\"},\n", "    {\n", "        \"name\": \"current_stock_backend\",\n", "        \"type\": \"int\",\n", "        \"description\": \"current inventory at backend\",\n", "    },\n", "    {\"name\": \"current_stock\", \"type\": \"int\", \"description\": \"current inventory at DS\"},\n", "    {\n", "        \"name\": \"inventory_in_transit\",\n", "        \"type\": \"int\",\n", "        \"description\": \"inventory in transit\",\n", "    },\n", "    {\n", "        \"name\": \"sto_billing_pending\",\n", "        \"type\": \"int\",\n", "        \"description\": \"inventory in transit - billing pending\",\n", "    },\n", "    {\n", "        \"name\": \"sto_dispatch_pending\",\n", "        \"type\": \"int\",\n", "        \"description\": \"inventory in transit - dispatch pending\",\n", "    },\n", "    {\n", "        \"name\": \"sto_arrival_pending\",\n", "        \"type\": \"int\",\n", "        \"description\": \"inventory in transit - STO arrival pending\",\n", "    },\n", "    {\n", "        \"name\": \"sto_grn_pending\",\n", "        \"type\": \"int\",\n", "        \"description\": \"inventory in transit - STO GRN pending at DS\",\n", "    },\n", "    {\"name\": \"discrepancy_marked\", \"type\": \"int\", \"description\": \"discrepancy marked\"},\n", "    {\n", "        \"name\": \"current_day_qty_sold\",\n", "        \"type\": \"int\",\n", "        \"description\": \"qty sold current date\",\n", "    },\n", "    {\"name\": \"current_day_gmv\", \"type\": \"float\", \"description\": \"gmv current date\"},\n", "    {\n", "        \"name\": \"current_day_carts\",\n", "        \"type\": \"int\",\n", "        \"description\": \"category carts current date\",\n", "    },\n", "    {\"name\": \"t_1_qty_sold\", \"type\": \"int\", \"description\": \"qty sold yesterday\"},\n", "    {\"name\": \"t_1_gmv\", \"type\": \"float\", \"description\": \"gmv yesterday\"},\n", "    {\"name\": \"t_1_carts\", \"type\": \"int\", \"description\": \"category carts yesterday\"},\n", "    {\"name\": \"t_7_qty_sold\", \"type\": \"int\", \"description\": \"qty sold Last 7 days\"},\n", "    {\"name\": \"t_7_gmv\", \"type\": \"float\", \"description\": \"gmv  Last 7 days\"},\n", "    {\"name\": \"t_7_carts\", \"type\": \"int\", \"description\": \"category carts Last 7 days\"},\n", "    {\"name\": \"qty_sold_till_date\", \"type\": \"int\", \"description\": \"qty sold till date\"},\n", "    {\"name\": \"gmv_till_date\", \"type\": \"float\", \"description\": \"gmv till date\"},\n", "    {\n", "        \"name\": \"carts_till_date\",\n", "        \"type\": \"int\",\n", "        \"description\": \"category carts till date\",\n", "    },\n", "    {\"name\": \"current_day_dump\", \"type\": \"int\", \"description\": \"dump current date\"},\n", "    {\n", "        \"name\": \"current_day_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"dump value current date\",\n", "    },\n", "    {\"name\": \"t_1_dump\", \"type\": \"int\", \"description\": \"dump yesterday\"},\n", "    {\"name\": \"t_1_dump_value\", \"type\": \"float\", \"description\": \"dump value yesterday\"},\n", "    {\"name\": \"t_7_dump\", \"type\": \"int\", \"description\": \"dump last 7 days\"},\n", "    {\n", "        \"name\": \"t_7_dump_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"dump value last 7 days\",\n", "    },\n", "    {\"name\": \"dump_till_date\", \"type\": \"int\", \"description\": \"dump till date\"},\n", "    {\n", "        \"name\": \"dump_value_till_date\",\n", "        \"type\": \"float\",\n", "        \"description\": \"dump value till date\",\n", "    },\n", "    {\"name\": \"t_new_inward_drop\", \"type\": \"int\", \"description\": \"t_new_inward_drop\"},\n", "    {\"name\": \"t_new_storage_drop\", \"type\": \"int\", \"description\": \"t_new_storage_drop\"},\n", "    {\n", "        \"name\": \"t_new_truck_load_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t_new_truck_load_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_sku_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t_new_picking_capacity_sku_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t_new_picking_capacity_quantity_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_loose_quantity_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t_new_loose_quantity_drop\",\n", "    },\n", "    {\"name\": \"t1_new_inward_drop\", \"type\": \"int\", \"description\": \"t1_new_inward_drop\"},\n", "    {\n", "        \"name\": \"t1_new_storage_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t1_new_storage_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_truck_load_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t1_new_truck_load_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t1_new_picking_capacity_sku_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t1_new_picking_capacity_quantity_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_loose_quantity_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t1_new_loose_quantity_drop\",\n", "    },\n", "    {\"name\": \"t7_new_inward_drop\", \"type\": \"int\", \"description\": \"t7_new_inward_drop\"},\n", "    {\n", "        \"name\": \"t7_new_storage_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t7_new_storage_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_truck_load_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t7_new_truck_load_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t7_new_picking_capacity_sku_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t7_new_picking_capacity_quantity_drop\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_loose_quantity_drop\",\n", "        \"type\": \"int\",\n", "        \"description\": \"t7_new_loose_quantity_drop\",\n", "    },\n", "    {\"name\": \"available_flag\", \"type\": \"int\", \"description\": \"available_flag\"},\n", "    {\n", "        \"name\": \"not_live_but_in_transit\",\n", "        \"type\": \"int\",\n", "        \"description\": \"intransit available flag\",\n", "    },\n", "    {\"name\": \"min_quantity\", \"type\": \"int\", \"description\": \"min_quantity\"},\n", "    {\"name\": \"max_quantity\", \"type\": \"int\", \"description\": \"max_quantity\"},\n", "    {\"name\": \"po_qty\", \"type\": \"int\", \"description\": \"total po raised\"},\n", "    {\"name\": \"grn_qty\", \"type\": \"int\", \"description\": \"total grn\"},\n", "    {\n", "        \"name\": \"po_schedule_dates\",\n", "        \"type\": \"varchar(500)\",\n", "        \"description\": \"po schedule dates\",\n", "    },\n", "    {\"name\": \"open_po_qty\", \"type\": \"int\", \"description\": \"open PO qty\"},\n", "    {\"name\": \"date_time\", \"type\": \"datetime\", \"description\": \"date time for upsert\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"event_metrics_reporting\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"frontend_facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"city\"],\n", "    \"incremental_key\": \"date_time\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Reporting metrics for Events\",\n", "}\n", "pb.to_redshift(final_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "be31c236-e20b-4017-ab2d-2f47cdb3ae71", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "32062df9-ffdd-4f3a-a528-d84ef1ec685b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "479f3662-323d-4fa0-aa9e-861a99f967da", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "638d7b4a-0efb-4215-84f1-70a301cfc22d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "14b4ff80-d60f-4798-ac0c-e7e4be6fe062", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2cd3b83e-7e26-4ee4-8baa-f859c17c4349", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fa88e9e4-d97d-4a30-8427-4cd882603165", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}