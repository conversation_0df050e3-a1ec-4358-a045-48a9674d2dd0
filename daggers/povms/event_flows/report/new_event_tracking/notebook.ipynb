{"cells": [{"cell_type": "code", "execution_count": null, "id": "f52719b5-ad19-4f36-8af0-447cf7f4b134", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)"]}, {"cell_type": "code", "execution_count": null, "id": "745972ec-50c7-49eb-8f43-107644559ac7", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\""]}, {"cell_type": "code", "execution_count": null, "id": "99fa4a76-3249-4bc8-ad94-f9e9e526186d", "metadata": {}, "outputs": [], "source": ["suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            if not df.empty:\n", "                # Print the size of the DataFrame\n", "                size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "                print(\"DataFrame size: \", size)\n", "                return df\n", "            else:\n", "                print(\"DataFrame is empty, retrying...\")\n", "        except Exception as e:\n", "            print(e)\n", "\n", "        time.sleep(5)\n", "\n", "    print(\"Max retries reached, returning an empty DataFrame\")\n", "    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "569915e9-0ac8-402f-a90c-a6c266d9e4e8", "metadata": {}, "outputs": [], "source": ["def sheets(data, operation, sheet_id, sheet_name, clear_cache=True):\n", "    max_retries = 5\n", "    retry_interval = 10  # in seconds\n", "\n", "    for attempt in range(max_retries):\n", "        try:\n", "            start_time = time.time()  # Start timing\n", "\n", "            if operation == \"write\":\n", "                pb.to_sheets(data, sheet_id, sheet_name)\n", "                end_time = time.time()  # End timing\n", "                duration = end_time - start_time\n", "                formatted_duration = format_time(duration)\n", "                print(f\"Attempt {attempt + 1} for {operation}... done! Time: {formatted_duration}\")\n", "\n", "            elif operation == \"read\":\n", "                fetched_data = pb.from_sheets(sheet_id, sheet_name)\n", "                end_time = time.time()  # End timing\n", "                duration = end_time - start_time\n", "                formatted_duration = format_time(duration)\n", "                print(f\"Attempt {attempt + 1} for {operation}... done! Time: {formatted_duration}\")\n", "                return fetched_data\n", "\n", "            break  # Break out of the loop if successful\n", "\n", "        except Exception as e:\n", "            print(f\"Error: {e}\")\n", "\n", "            if attempt < max_retries - 1:\n", "                print(f\"Retrying in {retry_interval} seconds...\")\n", "                time.sleep(retry_interval)\n", "            else:\n", "                print(f\"Max retries reached. Unable to perform {operation} operation.\")\n", "                return None  # Return None if max retries reached and break out of the loop"]}, {"cell_type": "markdown", "id": "41fc856c-f09b-47db-b682-2778e368d3df", "metadata": {}, "source": ["# active ds details"]}, {"cell_type": "code", "execution_count": null, "id": "9b4e5d36-b84d-4cd5-ae1b-4f85d7cbf4a5", "metadata": {}, "outputs": [], "source": ["def active_ds():\n", "    active_ds = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details),\n", "\n", "    item_details as\n", "        (select * from supply_etls.item_details),\n", "\n", "    carts_details as\n", "        (select * from logistics_data_etls.cart_projections\n", "            where\n", "                updated_on >= current_date - interval '7' day\n", "        ),\n", "\n", "    final_carts_details as\n", "        (select * from carts_details\n", "            where (date between current_date and current_date + interval '10' day)\n", "        ),\n", "\n", "    max_details as\n", "        (select\n", "            max(updated_on) as updated_on,\n", "            outletid,\n", "            date\n", "\n", "                from final_carts_details\n", "\n", "                    group by 2,3\n", "        ),\n", "\n", "    merchant as\n", "        (select outlet_id, frontend_merchant_id, count(distinct cart_id) as cart_count from dwh.fact_sales_order_details\n", "            where order_create_dt_ist >= (current_date - interval '30' day)\n", "                group by 1,2\n", "        ),\n", "\n", "    final_merchant as\n", "        (select max(cart_count) as cart_count,\n", "            frontend_merchant_id,\n", "            outlet_id\n", "\n", "                from merchant\n", "\n", "                    group by 2,3\n", "        ),\n", "\n", "    final_active_ds as\n", "        (select\n", "            -- fcd.updated_on,\n", "            -- fcd.outletid as hot_outlet_id,\n", "            od.facility_id,\n", "            coalesce(fm.frontend_merchant_id,0) as frontend_merchant_id,\n", "            -- fcd.date as date_,\n", "            -- min(fcd.date) as outbound_start,\n", "            avg(fcd.carts) as avg_carts\n", "\n", "                from final_carts_details fcd\n", "\n", "                    join\n", "                        max_details md on md.updated_on = fcd.updated_on \n", "                        and \n", "                            md.outletid = fcd.outletid \n", "                        and \n", "                            md.date = fcd.date\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = fcd.outletid\n", "\n", "                    left join\n", "                        final_merchant fm on fm.outlet_id = fcd.outletid\n", "\n", "                        group by 1,2\n", "        )\n", "\n", "            select * from final_active_ds\n", "                where avg_carts > 10\n", "            \n", "    \"\"\"\n", "    return read_sql_query(active_ds, trino)\n", "\n", "\n", "active_ds = active_ds()\n", "\n", "print(active_ds.shape)\n", "active_ds.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e8b9f01d-d1d5-4f2d-877f-c7cc5756f8d9", "metadata": {}, "outputs": [], "source": ["active_facility_list = tuple(list(active_ds[\"facility_id\"].unique()))\n", "len(active_facility_list)"]}, {"cell_type": "code", "execution_count": null, "id": "e188c08e-46c1-444b-b8f4-25972d931ac2", "metadata": {}, "outputs": [], "source": ["state_list = sheets(None, \"read\", \"1-8E4mDVA9f8OoNPnDGMn5PnnwGszwaepjGUAFsryWKY\", \"new_base\")\n", "\n", "# state_list = pd.read_csv(\"new.csv\")\n", "\n", "state_list = state_list[\n", "    [\n", "        \"tracker\",\n", "        \"state_selected\",\n", "        \"event_name\",\n", "        \"assortment_type\",\n", "        \"go_live_date\",\n", "        \"end_date\",\n", "    ]\n", "]\n", "\n", "state_list[\n", "    [\n", "        \"tracker\",\n", "        \"state_selected\",\n", "        \"event_name\",\n", "        \"assortment_type\",\n", "        \"go_live_date\",\n", "        \"end_date\",\n", "    ]\n", "] = state_list[\n", "    [\n", "        \"tracker\",\n", "        \"state_selected\",\n", "        \"event_name\",\n", "        \"assortment_type\",\n", "        \"go_live_date\",\n", "        \"end_date\",\n", "    ]\n", "].replace(\n", "    \"\", np.nan\n", ")\n", "state_list = state_list.dropna()\n", "\n", "state_list[[\"tracker\", \"state_selected\"]] = (\n", "    state_list[[\"tracker\", \"state_selected\"]].fillna(0).astype(int)\n", ")\n", "\n", "state_list = state_list.rename(columns={\"state_selected\": \"state\"})\n", "\n", "\n", "state_list_selected = state_list[\n", "    ((state_list[\"state\"].isna() == False) & (state_list[\"state\"] != \"\"))\n", "][[\"state\"]].drop_duplicates()\n", "\n", "\n", "state_list_selected = list(state_list_selected[\"state\"].astype(int).unique())\n", "\n", "if len(state_list_selected) < 2:\n", "    state_list_selected.append(-1)\n", "    state_list_selected.append(-2)\n", "state_list_selected = tuple(state_list_selected)\n", "\n", "state_list, state_list_selected"]}, {"cell_type": "markdown", "id": "ada7cf71-16cf-4d60-b6d3-3e05c7000d7a", "metadata": {}, "source": ["# manual input assortment"]}, {"cell_type": "code", "execution_count": null, "id": "f21c4cb2-971d-4bb9-a717-e600add858ae", "metadata": {}, "outputs": [], "source": ["def manual_assortment():\n", "    manual_assortment = f\"\"\"\n", "\n", "    select * from supply_etls.special_events_input\n", "        where state in {state_list_selected}\n", "\n", "    \"\"\"\n", "    return read_sql_query(manual_assortment, trino)\n", "\n", "\n", "manual_assortment = manual_assortment()\n", "\n", "print(manual_assortment.shape)\n", "manual_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "cfef295b-b150-4d52-a002-4d98615a87f3", "metadata": {}, "outputs": [], "source": ["if manual_assortment.shape[0] > 0:\n", "    manual_item_id_list = list(manual_assortment[\"item_id\"].unique())\n", "    if len(manual_item_id_list) < 2:\n", "        manual_item_id_list.append(-1)\n", "        manual_item_id_list.append(-2)\n", "    manual_item_id_list = tuple(manual_item_id_list)\n", "    len(manual_item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "978337ec-0884-4302-b28a-b8cb5dabf68f", "metadata": {}, "outputs": [], "source": ["if manual_assortment.shape[0] > 0:\n", "    manual_facility_id_list = list(manual_assortment[\"facility_id\"].unique())\n", "    if len(manual_facility_id_list) < 2:\n", "        manual_facility_id_list.append(-1)\n", "        manual_facility_id_list.append(-2)\n", "    manual_facility_id_list = tuple(manual_facility_id_list)\n", "    len(manual_facility_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "1206d230-2ac6-489a-b5d4-1b5b4667e570", "metadata": {}, "outputs": [], "source": ["if manual_assortment.shape[0] > 0:\n", "    adding_manual_assortment_name = pd.merge(\n", "        manual_assortment, state_list, on=[\"state\"], how=\"inner\"\n", "    )\n", "\n", "    adding_manual_assortment_name = adding_manual_assortment_name[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"event_name\",\n", "            \"assortment_type\",\n", "            \"go_live_date\",\n", "            \"end_date\",\n", "        ]\n", "    ]\n", "\n", "    print(adding_manual_assortment_name.shape)\n", "    adding_manual_assortment_name.head(2)"]}, {"cell_type": "markdown", "id": "2e6a6acd-dd02-4f47-af67-4814c4080403", "metadata": {}, "source": ["# final manual input assortment base"]}, {"cell_type": "code", "execution_count": null, "id": "3a69578c-81e5-44f0-8ab3-bedc72ef6bd7", "metadata": {}, "outputs": [], "source": ["if manual_assortment.shape[0] > 0:\n", "\n", "    def final_manual_assortment():\n", "        final_manual_assortment = f\"\"\"\n", "\n", "        with\n", "        assortment as\n", "            (select * from rpc.product_facility_master_assortment),\n", "\n", "        inuput_assortment as\n", "            (select\n", "                item_id,\n", "                facility_id,\n", "                master_assortment_substate_id as status\n", "\n", "\n", "                    from assortment\n", "\n", "                        where \n", "                            facility_id in {manual_facility_id_list}\n", "                            and item_id in {manual_item_id_list}\n", "                            and lake_active_record\n", "                            and active = 1\n", "            )\n", "\n", "                select * from inuput_assortment\n", "\n", "        \"\"\"\n", "        return read_sql_query(final_manual_assortment, trino)\n", "\n", "    final_manual_assortment = final_manual_assortment()\n", "\n", "    print(final_manual_assortment.shape)\n", "    final_manual_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "5a0d5df0-9c8e-4699-a7ad-f8810df13f14", "metadata": {}, "outputs": [], "source": ["if manual_assortment.shape[0] > 0:\n", "    final_manual_assortment = pd.merge(\n", "        adding_manual_assortment_name,\n", "        final_manual_assortment,\n", "        on=[\"item_id\", \"facility_id\"],\n", "        how=\"left\",\n", "    )\n", "\n", "    final_manual_assortment[\"status\"] = final_manual_assortment[\"status\"].fillna(1111).astype(int)\n", "\n", "    print(final_manual_assortment.shape)\n", "    final_manual_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f15f41d2-0bfd-4a60-a860-a789f532c15b", "metadata": {}, "outputs": [], "source": ["pfma_assortment = sheets(\n", "    None,\n", "    \"read\",\n", "    \"1-8E4mDVA9f8OoNPnDGMn5PnnwGszwaepjGUAFsryWKY\",\n", "    \"pfma_event\",\n", ")\n", "\n", "# pfma_assortment = pd.read_csv('old.csv')\n", "\n", "pfma_assortment = pfma_assortment[\n", "    [\n", "        \"event_id\",\n", "        \"event_name\",\n", "        \"event_date\",\n", "        \"go_live_date\",\n", "        \"end_date\",\n", "    ]\n", "]\n", "\n", "pfma_assortment = pfma_assortment[\n", "    ~(\n", "        (pfma_assortment[\"event_id\"] == \"\")\n", "        | (pfma_assortment[\"event_name\"] == \"\")\n", "        | (pfma_assortment[\"event_date\"] == \"\")\n", "        | (pfma_assortment[\"go_live_date\"] == \"\")\n", "        | (pfma_assortment[\"end_date\"] == \"\")\n", "    )\n", "]\n", "\n", "pfma_assortment[\"event_id\"] = pfma_assortment[\"event_id\"].fillna(0).astype(int)\n", "\n", "pfma_assortment.dropna(inplace=True)\n", "\n", "reason_substate_list = tuple(list(pfma_assortment[\"event_id\"].unique()))\n", "if len(reason_substate_list) < 2:\n", "    reason_substate_list = list(reason_substate_list)\n", "    reason_substate_list.append(-1)\n", "    reason_substate_list = tuple(reason_substate_list)\n", "\n", "print(reason_substate_list)\n", "pfma_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "faefe879-0ffb-44fc-a834-ce13519dd777", "metadata": {}, "outputs": [], "source": ["def final_system_assortment():\n", "    final_system_assortment = f\"\"\"\n", "    \n", "    with\n", "    assortment_log as\n", "        (select * from rpc.product_facility_master_assortment_log),\n", "\n", "    final_assortment_log as\n", "        (select\n", "            item_id,\n", "            facility_id,\n", "            reason_id as event_id,\n", "            new_substate as status,\n", "            row_number() over (partition by item_id, facility_id, reason_id order by created_at desc) as rnk,\n", "            row_number() over (partition by item_id, facility_id order by created_at desc) as rnk_2\n", "\n", "                from assortment_log\n", "\n", "                    where \n", "                        facility_id in {active_facility_list}\n", "                        and reason_id in {reason_substate_list}\n", "                        and lake_active_record\n", "                        and insert_ds_ist >= cast(current_date - interval '90' day as varchar)\n", "\n", "        ),\n", "\n", "    current_assortment as\n", "        (select * from rpc.product_facility_master_assortment),\n", "\n", "    final_current_assortment as\n", "        (select\n", "            item_id,\n", "            facility_id,\n", "            substate_reason_id as event_id,\n", "            master_assortment_substate_id as status\n", "\n", "                from current_assortment\n", "\n", "                    where\n", "                        facility_id in {active_facility_list}\n", "                        and substate_reason_id in {reason_substate_list}\n", "                        and lake_active_record\n", "                        and active = 1\n", "        ),\n", "\n", "    final_base as\n", "        (   \n", "            select item_id, facility_id, event_id\n", "                from final_assortment_log\n", "                    where\n", "                        rnk = 1\n", "\n", "                union\n", "\n", "            select item_id, facility_id, event_id\n", "                from final_current_assortment\n", "        ),\n", "\n", "    final as\n", "        (select\n", "            fb.item_id,\n", "            fb.facility_id,\n", "            fb.event_id,\n", "            coalesce(fca.status,fal.status) as status\n", "\n", "                from final_base fb\n", "\n", "                    left join\n", "                        (select item_id, facility_id, status from final_current_assortment group by 1,2,3) fca on fca.item_id = fb.item_id\n", "                        and fca.facility_id = fb.facility_id\n", "\n", "                    left join\n", "                        (select item_id, facility_id, status from final_assortment_log where rnk_2 = 1 group by 1,2,3) fal on fal.item_id = fb.item_id\n", "                        and fal.facility_id = fb.facility_id\n", "        )\n", "\n", "            select * from final\n", "                \n", "    \n", "    \"\"\"\n", "    return read_sql_query(final_system_assortment, trino)\n", "\n", "\n", "final_system_assortment = final_system_assortment()\n", "\n", "print(final_system_assortment.shape)\n", "final_system_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4a8b61c9-7244-4928-a231-b1f280555899", "metadata": {}, "outputs": [], "source": ["final_system_assortment = pd.merge(\n", "    final_system_assortment, pfma_assortment, on=[\"event_id\"], how=\"inner\"\n", ")\n", "\n", "final_system_assortment[\"assortment_type\"] = final_system_assortment[\"event_name\"]\n", "\n", "final_system_assortment[\"status\"] = final_system_assortment[\"status\"].fillna(1111).astype(int)\n", "\n", "final_system_assortment = final_system_assortment[\n", "    [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"event_name\",\n", "        \"assortment_type\",\n", "        \"go_live_date\",\n", "        \"end_date\",\n", "        \"status\",\n", "    ]\n", "].drop_duplicates()\n", "\n", "print(final_system_assortment.shape)\n", "final_system_assortment.head(2)"]}, {"cell_type": "markdown", "id": "cd9abcb3-d5cd-4081-a70e-d5846397dbdd", "metadata": {}, "source": ["# FnV Assortment"]}, {"cell_type": "code", "execution_count": null, "id": "a160bb84-70d1-4cc4-9f67-41ca5a7be106", "metadata": {}, "outputs": [], "source": ["def fnv_assortment():\n", "    fnv_assortment = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details\n", "            where\n", "                ars_check = 1\n", "        ),\n", "\n", "    assortment as\n", "        (select * from supply_etls.dark_stores_fnv_assortment\n", "            where date_ist = current_date\n", "        ),\n", "\n", "    final_assortment as\n", "        (select\n", "            od.facility_id,\n", "            item_id,\n", "            'FnV' as event_name,\n", "            'FnV' as assortment_type,\n", "            date(current_date - interval '30' day) as go_live_date,\n", "            date(current_date) as end_date,\n", "            1 as status\n", "\n", "                from assortment a\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = a.ssc_id\n", "        )\n", "\n", "            select * from final_assortment\n", "\n", "    \"\"\"\n", "    return read_sql_query(fnv_assortment, trino)\n", "\n", "\n", "fnv_assortment = fnv_assortment()\n", "\n", "print(fnv_assortment.shape)\n", "fnv_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "ccb4b02d-8b17-4f1f-8aaf-5a70bbe9798b", "metadata": {}, "outputs": [], "source": ["if manual_assortment.shape[0] > 0:\n", "    combing_assortment = final_system_assortment.append(\n", "        [final_manual_assortment, fnv_assortment], ignore_index=False\n", "    ).drop_duplicates()\n", "else:\n", "    combing_assortment = final_system_assortment.append(\n", "        [fnv_assortment], ignore_index=False\n", "    ).drop_duplicates()\n", "\n", "print(combing_assortment.shape)\n", "combing_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "36de7133-515c-4fc9-88e0-49ecd154cdd5", "metadata": {}, "outputs": [], "source": ["status_details = combing_assortment[[\"item_id\", \"facility_id\", \"status\"]].copy()\n", "\n", "status_details = (\n", "    status_details.groupby([\"facility_id\", \"item_id\"])[\"status\"].agg([\"min\"]).reset_index()\n", ")\n", "\n", "status_details[\"min\"] = status_details[\"min\"].fillna(1111).astype(int)\n", "\n", "combing_assortment = pd.merge(\n", "    combing_assortment, status_details, on=[\"item_id\", \"facility_id\"], how=\"inner\"\n", ")\n", "\n", "combing_assortment = combing_assortment.drop(columns={\"status\"}).rename(columns={\"min\": \"status\"})\n", "\n", "combing_assortment[\"status\"] = np.where(\n", "    (combing_assortment[\"status\"] == 1111), 0, combing_assortment[\"status\"]\n", ")\n", "\n", "combing_assortment[\"assortment_status\"] = np.where(\n", "    (combing_assortment[\"status\"] == 0),\n", "    \"not_part_of_assortment\",\n", "    \"part_of_assortment\",\n", ")\n", "\n", "combing_assortment = combing_assortment.drop_duplicates().reset_index(drop=True)\n", "\n", "status_details = pd.DataFrame()\n", "\n", "print(combing_assortment.shape)\n", "combing_assortment.head(2)"]}, {"cell_type": "markdown", "id": "3a8fe88f-3d7f-4294-8179-92b54ed0d621", "metadata": {}, "source": ["# outlet details"]}, {"cell_type": "code", "execution_count": null, "id": "f56f6c80-79b0-4b53-9673-21bb60a3e9b3", "metadata": {}, "outputs": [], "source": ["def outlet_details():\n", "    outlet_details = \"\"\"\n", "    \n", "    with\n", "    outlet_details as \n", "        (select * from supply_etls.outlet_details)\n", "\n", "            select * from outlet_details\n", "                where ars_check = 1\n", "        \n", "    \n", "    \"\"\"\n", "    return read_sql_query(outlet_details, trino)\n", "\n", "\n", "outlet_details = outlet_details()\n", "\n", "print(outlet_details.shape)\n", "outlet_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "cfcf031e-8e93-4ecc-97a4-d48a479637c9", "metadata": {}, "outputs": [], "source": ["# all inv outlets\n", "all_inv_outlet_id_list = tuple(list(outlet_details[\"inv_outlet_id\"].unique()))\n", "\n", "# all hot outlets\n", "all_hot_outlet_id_list = tuple(list(outlet_details[\"hot_outlet_id\"].unique()))\n", "\n", "# be hot outlets\n", "all_hot_name = outlet_details[\n", "    [\"hot_outlet_id\", \"inv_outlet_id\", \"facility_id\", \"facility_name\"]\n", "].rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"inv_outlet_id\": \"be_inv_outlet_id\",\n", "        \"facility_id\": \"be_facility_id\",\n", "        \"facility_name\": \"be_facility_name\",\n", "    }\n", ")\n", "\n", "# all facility id\n", "all_facility_id_list = tuple(list(outlet_details[\"facility_id\"].unique()))\n", "\n", "# frontend outlets\n", "frontend_outlet_details = (\n", "    outlet_details[outlet_details[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "fe_facility_id_list = tuple(list(frontend_outlet_details[\"facility_id\"].unique()))\n", "\n", "hp_outlet_id_list = outlet_details[outlet_details[\"store_type\"] == \"HP\"]\n", "hp_outlet_id_list = tuple(list(hp_outlet_id_list[\"inv_outlet_id\"]))"]}, {"cell_type": "markdown", "id": "076f015e-2045-4282-ba97-8005abc836b5", "metadata": {}, "source": ["# item details"]}, {"cell_type": "code", "execution_count": null, "id": "f38f42f9-2e5d-4882-bf02-65794d0d7d1d", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = \"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select * from supply_etls.item_details)\n", "        \n", "            select\n", "                item_id,\n", "                item_name,\n", "                p_type,\n", "                l0_category as l0,\n", "                l1_category as l1,\n", "                l2_category as l2,\n", "                manufacturer_id,\n", "                manufacturer_name\n", "\n", "                    from item_details\n", "                        \n", "                        where handling_type = 'Non Packaging Material'\n", "            \n", "    \"\"\"\n", "    return read_sql_query(item_details, trino)\n", "\n", "\n", "item_details = item_details()\n", "\n", "print(item_details.shape)\n", "item_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "8b276af0-b487-440b-a087-e0b578cc0042", "metadata": {}, "outputs": [], "source": ["adding_frontend_merchant = pd.merge(\n", "    combing_assortment,\n", "    active_ds[[\"facility_id\", \"frontend_merchant_id\"]],\n", "    on=[\"facility_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "adding_outlet_details = pd.merge(\n", "    adding_frontend_merchant, outlet_details, on=[\"facility_id\"], how=\"inner\"\n", ")\n", "\n", "adding_item_details = pd.merge(adding_outlet_details, item_details, on=[\"item_id\"], how=\"inner\")\n", "\n", "adding_item_details = adding_item_details.rename(columns={\"event_name\": \"event_flag\"})\n", "\n", "adding_item_details = adding_item_details[\n", "    [\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"facility_name\",\n", "        \"frontend_merchant_id\",\n", "        \"item_id\",\n", "        \"go_live_date\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_name\",\n", "        \"p_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"status\",\n", "        \"assortment_status\",\n", "    ]\n", "]\n", "\n", "active_ds = pd.DataFrame()\n", "state_list = pd.DataFrame()\n", "state_list_selected = pd.DataFrame()\n", "manual_assortment = pd.DataFrame()\n", "adding_manual_assortment_name = pd.DataFrame()\n", "final_manual_assortment = pd.DataFrame()\n", "pfma_assortment = pd.DataFrame()\n", "final_system_assortment = pd.DataFrame()\n", "fnv_assortment = pd.DataFrame()\n", "combing_assortment = pd.DataFrame()\n", "adding_frontend_merchant = pd.DataFrame()\n", "adding_outlet_details = pd.DataFrame()\n", "\n", "print(adding_item_details.shape)\n", "adding_item_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "79d5bde6-9f92-40f7-af86-bd287b3f8ddc", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(list(adding_item_details[\"item_id\"].unique()))\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "2a51c3e4-449e-4704-ba67-20604af99ae9", "metadata": {}, "outputs": [], "source": ["first_live_date = adding_item_details.agg({\"go_live_date\": \"min\"}).reset_index().iloc[:, 1][0]\n", "first_live_date"]}, {"cell_type": "markdown", "id": "de2f8cdb-ae7d-40e9-bc08-9abcac39efb6", "metadata": {}, "source": ["# tea taggings details"]}, {"cell_type": "code", "execution_count": null, "id": "a3213d88-b513-49a5-b77a-f14df8c519cb", "metadata": {}, "outputs": [], "source": ["def tea_taggings():\n", "    tea_taggings = f\"\"\"\n", "    \n", "    with\n", "    tea_taggings as\n", "        (select * from rpc.item_outlet_tag_mapping),\n", "    \n", "    final_tea_taggings as\n", "        (select * from tea_taggings\n", "            where\n", "                active = 1\n", "                and lake_active_record\n", "                and tag_type_id = 8\n", "        )\n", "        \n", "            select\n", "                item_id,\n", "                outlet_id as hot_outlet_id,\n", "                cast(tag_value as int) as be_hot_outlet_id\n", "                \n", "                    from final_tea_taggings\n", "                        \n", "                        where \n", "                            item_id in {item_id_list}\n", "                            and outlet_id in {all_hot_outlet_id_list}\n", "    \n", "    \n", "    \"\"\"\n", "    return read_sql_query(tea_taggings, trino)\n", "\n", "\n", "tea_taggings = tea_taggings()\n", "\n", "print(tea_taggings.shape)\n", "tea_taggings.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2b3d9292-ae77-4b00-88d8-bf33b0a5b9e1", "metadata": {}, "outputs": [], "source": ["tea_taggings[\"be_hot_outlet_id\"] = tea_taggings[\"be_hot_outlet_id\"].astype(int)\n", "\n", "tea_taggings_adding_be = pd.merge(tea_taggings, all_hot_name, on=[\"be_hot_outlet_id\"], how=\"inner\")\n", "\n", "print(tea_taggings_adding_be.shape)\n", "tea_taggings_adding_be.head(2)"]}, {"cell_type": "markdown", "id": "93fc6a17-f108-4e7f-bb86-754625bf8d23", "metadata": {}, "source": ["# ARS mapping"]}, {"cell_type": "code", "execution_count": null, "id": "93d30f34-1675-44c9-8663-c58be3a18388", "metadata": {}, "outputs": [], "source": ["def ars_mapping():\n", "    ars_mapping = \"\"\"\n", "    \n", "    with\n", "    ars_mapping as\n", "        (select * from po.bulk_facility_outlet_mapping),\n", "    \n", "    final_ars_mapping as\n", "        (select \n", "            facility_id as be_facility_id,\n", "            outlet_id as hot_outlet_id\n", "\n", "                from ars_mapping\n", "\n", "                    where \n", "                        active = true\n", "                        and lake_active_record\n", "                        \n", "                        group by 1,2\n", "        )\n", "\n", "            select * from final_ars_mapping\n", "    \n", "    \"\"\"\n", "    return read_sql_query(ars_mapping, trino)\n", "\n", "\n", "ars_mapping = ars_mapping()\n", "\n", "print(ars_mapping.shape)\n", "\n", "ars_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "98174cb4-cfeb-4e2b-ac3f-37fe87e592da", "metadata": {}, "outputs": [], "source": ["adding_ars_mapping = pd.merge(\n", "    tea_taggings_adding_be,\n", "    ars_mapping,\n", "    on=[\"be_facility_id\", \"hot_outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "print(adding_ars_mapping.shape)\n", "adding_ars_mapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "2dbf0810-c5be-4a73-a39b-8c7675796a8b", "metadata": {}, "outputs": [], "source": ["adding_tea_details = pd.merge(\n", "    adding_item_details,\n", "    adding_ars_mapping,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]] = (\n", "    adding_tea_details[[\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_tea_details[\"be_facility_name\"] = adding_tea_details[\"be_facility_name\"].fillna(\n", "    \"No Backend Tagged\"\n", ")\n", "\n", "adding_item_details = pd.DataFrame()\n", "tea_taggings = pd.DataFrame()\n", "tea_taggings_adding_be = pd.DataFrame()\n", "ars_mapping = pd.DataFrame()\n", "adding_ars_mapping = pd.DataFrame()\n", "\n", "print(adding_tea_details.shape)\n", "adding_tea_details.head(2)"]}, {"cell_type": "markdown", "id": "07438917-9409-44e6-bec1-b833535e85ec", "metadata": {}, "source": ["# inventory details"]}, {"cell_type": "code", "execution_count": null, "id": "dc61bdd7-c8c6-4933-adfc-e063149ad2d0", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select * from ims.ims_item_inventory),\n", "    \n", "    final_inv as\n", "        (select\n", "            item_id,\n", "            outlet_id as hot_outlet_id,\n", "            sum(quantity) as actual_inv\n", "                \n", "                from inv\n", "                \n", "                    where \n", "                        active = 1\n", "                        and lake_active_record\n", "                        and item_id in {item_id_list}\n", "                        and outlet_id in {all_inv_outlet_id_list}\n", "                        and quantity > 0\n", "                        \n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from final_inv\n", "    \n", "    \"\"\"\n", "    return read_sql_query(inventory, trino)\n", "\n", "\n", "inventory = inventory()\n", "\n", "print(inventory.shape)\n", "inventory.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "34060416-24a5-4332-8af7-1ab2512154df", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "\n", "print(be_inv.shape)\n", "be_inv.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b78421fa-e2ff-4a31-8486-e2bcec068046", "metadata": {}, "outputs": [], "source": ["adding_fe_inv_details = pd.merge(\n", "    adding_tea_details, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "adding_be_inv_details = pd.merge(\n", "    adding_fe_inv_details, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]] = (\n", "    adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_tea_details = pd.DataFrame()\n", "inventory = pd.DataFrame()\n", "be_inv = pd.DataFrame()\n", "adding_fe_inv_details = pd.DataFrame()\n", "\n", "print(adding_be_inv_details.shape)\n", "adding_be_inv_details.head(2)"]}, {"cell_type": "markdown", "id": "7c2ff455-8f4e-4ade-908c-88ba32b37e75", "metadata": {}, "source": ["# blocked inventory"]}, {"cell_type": "code", "execution_count": null, "id": "5c7d94fc-107c-41be-9889-6871f9eaf80a", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select * from ims.ims_item_blocked_inventory),\n", "    \n", "    final_blocked as\n", "        (select\n", "            item_id,\n", "            outlet_id as hot_outlet_id,\n", "            sum(quantity) as ttl_blocked_qty\n", "            \n", "                from blocked\n", "                    \n", "                    where\n", "                        active = 1\n", "                        and lake_active_record\n", "                        and item_id in {item_id_list}\n", "                        and outlet_id in {all_inv_outlet_id_list}\n", "                        and quantity > 0\n", "                        \n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from final_blocked\n", "    \n", "    \"\"\"\n", "    return read_sql_query(blocked_inv, trino)\n", "\n", "\n", "blocked_inv = blocked_inv()\n", "\n", "print(blocked_inv.shape)\n", "blocked_inv.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "1d21f6eb-eb16-4334-b934-43ab15f4376d", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "95f340aa-75d7-4120-8df3-7953681855e0", "metadata": {}, "outputs": [], "source": ["adding_fe_blocked_details = pd.merge(\n", "    adding_be_inv_details, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_blocked_details = pd.merge(\n", "    adding_fe_blocked_details,\n", "    be_blocked,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]] = (\n", "    adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_blocked_details[\"net_inventory\"] = np.where(\n", "    (adding_be_blocked_details[\"actual_inv\"] - adding_be_blocked_details[\"ttl_blocked_qty\"]) < 0,\n", "    0,\n", "    (adding_be_blocked_details[\"actual_inv\"] - adding_be_blocked_details[\"ttl_blocked_qty\"]),\n", ")\n", "\n", "adding_be_blocked_details[\"be_net_inventory\"] = np.where(\n", "    (adding_be_blocked_details[\"be_actual_inv\"] - adding_be_blocked_details[\"be_ttl_blocked_qty\"])\n", "    < 0,\n", "    0,\n", "    (adding_be_blocked_details[\"be_actual_inv\"] - adding_be_blocked_details[\"be_ttl_blocked_qty\"]),\n", ")\n", "\n", "adding_be_inv_details = pd.DataFrame()\n", "blocked_inv = pd.DataFrame()\n", "be_blocked = pd.DataFrame()\n", "adding_fe_blocked_details = pd.DataFrame()\n", "\n", "print(adding_be_blocked_details.shape)\n", "adding_be_blocked_details.head(2)"]}, {"cell_type": "markdown", "id": "97a14e9c-6310-4a50-8696-d2f90b2b2f9c", "metadata": {}, "source": ["# adding HP inventory"]}, {"cell_type": "code", "execution_count": null, "id": "7b56fe0f-da5c-4812-997b-db5c17af4fc0", "metadata": {}, "outputs": [], "source": ["def hp_inv():\n", "    hp_inv = f\"\"\"\n", "    \n", "    with\n", "    hp_inv as\n", "        (select\n", "            updated_at_ist,\n", "            item_id,\n", "            outlet_id as be_inv_outlet_id,\n", "            current_inventory\n", "\n", "                from supply_etls.hourly_inventory_snapshots\n", "\n", "                    where\n", "                        date_ist >= current_date - interval '1' day\n", "        ),\n", "\n", "    final_inv as\n", "        (select\n", "            item_id,\n", "            be_inv_outlet_id,\n", "            current_inventory\n", "\n", "                from hp_inv\n", "\n", "                    where\n", "                        updated_at_ist = (select max(updated_at_ist) from hp_inv)\n", "                        and\n", "                            current_inventory > 0\n", "                        and\n", "                            be_inv_outlet_id in {hp_outlet_id_list}\n", "        )\n", "\n", "            select * from final_inv\n", "    \n", "    \"\"\"\n", "    return read_sql_query(hp_inv, trino)\n", "\n", "\n", "hp_inv = hp_inv()\n", "\n", "print(hp_inv.shape)\n", "hp_inv.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "a542b3d1-835e-4e7e-bf01-efd24c4b14ec", "metadata": {}, "outputs": [], "source": ["adding_hp_be_inv = pd.merge(\n", "    adding_be_blocked_details, hp_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_hp_be_inv[\"current_inventory\"] = adding_hp_be_inv[\"current_inventory\"].fillna(0).astype(int)\n", "\n", "adding_hp_be_inv[\"final_be_inv\"] = np.where(\n", "    (adding_hp_be_inv[\"be_net_inventory\"] > 0),\n", "    (adding_hp_be_inv[\"be_net_inventory\"]),\n", "    (adding_hp_be_inv[\"current_inventory\"]),\n", ")\n", "\n", "adding_hp_be_inv = adding_hp_be_inv.drop(columns={\"be_net_inventory\", \"current_inventory\"})\n", "\n", "adding_hp_be_inv = adding_hp_be_inv.rename(columns={\"final_be_inv\": \"be_net_inventory\"})\n", "\n", "adding_be_blocked_details = pd.DataFrame()\n", "hp_inv = pd.DataFrame()\n", "\n", "adding_hp_be_inv.head(2)"]}, {"cell_type": "markdown", "id": "3eadbbf4-f3a9-455d-951a-995d3b34f7b5", "metadata": {}, "source": ["# pending put-away"]}, {"cell_type": "code", "execution_count": null, "id": "6e9c9616-a6c1-48ea-9ac5-1e4e86ab4ccd", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    variant_details as\n", "        (select * from rpc.product_product),\n", "    \n", "    pp as\n", "        (select * from ims.ims_good_inventory),\n", "    \n", "    final as\n", "        (select\n", "            vd.item_id,\n", "            outlet_id as hot_outlet_id,\n", "            sum(quantity) as pending_putaway\n", "            \n", "                from pp\n", "                \n", "                    join\n", "                        variant_details vd on vd.variant_id = pp.variant_id\n", "                        and vd.lake_active_record\n", "                        \n", "                        where\n", "                            pp.active = 1\n", "                            and pp.lake_active_record\n", "                            and pp.inventory_update_type_id in (28,76)\n", "                            and vd.item_id in {item_id_list}\n", "                            and pp.outlet_id in {all_inv_outlet_id_list}\n", "                            and pp.quantity > 0\n", "                            \n", "                            group by 1,2\n", "        )\n", "        \n", "            select * from final\n", "    \n", "    \"\"\"\n", "    return read_sql_query(pen_put, trino)\n", "\n", "\n", "pen_put = pen_put()\n", "\n", "print(pen_put.shape)\n", "pen_put.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "bd4b8ba9-d43e-49e9-a5a1-eac02c8a417f", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8328aa13-0af1-4d3c-8650-7cb20110a962", "metadata": {}, "outputs": [], "source": ["adding_fe_pen_putaway_details = pd.merge(\n", "    adding_hp_be_inv, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.merge(\n", "    adding_fe_pen_putaway_details,\n", "    be_pen_put,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]] = (\n", "    adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_hp_be_inv = pd.DataFrame()\n", "pen_put = pd.DataFrame()\n", "be_pen_put = pd.DataFrame()\n", "adding_fe_pen_putaway_details = pd.DataFrame()\n", "\n", "print(adding_be_pen_putaway_details.shape)\n", "adding_be_pen_putaway_details.head(1)"]}, {"cell_type": "markdown", "id": "75d190b8-2f81-41c9-83a2-1fedd434109c", "metadata": {}, "source": ["# open sto details"]}, {"cell_type": "code", "execution_count": null, "id": "4a84b9ab-327d-451d-9d4f-030575dbde64", "metadata": {}, "outputs": [], "source": ["def sto_details():\n", "    sto_details = f\"\"\"\n", "    \n", "    with\n", "    open_sto as\n", "        (select \n", "            receiver_inv_outlet_id as hot_outlet_id,\n", "            item_id,\n", "            sum(open_sto_quantity + billed_sto_quantity) as open_sto_qty\n", "\n", "                from supply_etls.inventory_metrics_open_sto\n", "\n", "                    group by 1,2\n", "        )\n", "\n", "            select * from open_sto\n", "                where\n", "                    open_sto_qty > 0\n", "                    and\n", "                        item_id in {item_id_list}\n", "                    and\n", "                        hot_outlet_id in {all_inv_outlet_id_list}\n", "                        \n", "    \"\"\"\n", "    return read_sql_query(sto_details, trino)\n", "\n", "\n", "sto_details = sto_details()\n", "\n", "print(sto_details.shape)\n", "sto_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "777e84f7-c153-456d-bcb7-8e3f59617289", "metadata": {}, "outputs": [], "source": ["be_sto = sto_details.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"open_sto_qty\": \"be_open_sto_qty\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0a11339b-76a2-416d-bb1b-bedbe8e92c5d", "metadata": {}, "outputs": [], "source": ["adding_fe_sto_details = pd.merge(\n", "    adding_be_pen_putaway_details,\n", "    sto_details,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_sto_details = pd.merge(\n", "    adding_fe_sto_details, be_sto, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_sto_details[[\"open_sto_qty\", \"be_open_sto_qty\"]] = (\n", "    adding_be_sto_details[[\"open_sto_qty\", \"be_open_sto_qty\"]].fillna(0).astype(int)\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.DataFrame\n", "sto_details = pd.DataFrame\n", "be_sto = pd.DataFrame\n", "adding_fe_sto_details = pd.DataFrame\n", "\n", "print(adding_be_sto_details.shape)\n", "adding_be_sto_details.head(2)"]}, {"cell_type": "markdown", "id": "6b3e0810-4a20-4154-930d-2a3fe1f39fb3", "metadata": {}, "source": ["# po details"]}, {"cell_type": "code", "execution_count": null, "id": "5e7f1891-b29b-4a97-b5c4-645d22a44cf9", "metadata": {}, "outputs": [], "source": ["def po_details():\n", "    po_details = f\"\"\"\n", "    \n", "    with\n", "    open_po_details as\n", "        (select po_number, po_scheduled_date, po.outlet_id as hot_outlet_id, poi.item_id,\n", "            poi.units_ordered as po_quantity, \n", "            case when grn.grn_quantity is null then 0 else grn.grn_quantity end as grn_quantity,\n", "            case when (ppos.po_state_id in (8) or (ppos.po_state_id in (9) and po.is_multiple_grn != 1)) then 0 else 1 end po_status\n", "\n", "                from po.purchase_order_items poi\n", "\n", "                    join po.purchase_order po on po.id = poi.po_id\n", "\n", "                    join po.purchase_order_status ppos on ppos.po_id = po.id\n", "\n", "                    left join (select po_id_id, date(schedule_date_time + interval '330' minute) as po_scheduled_date\n", "                        from po.po_schedule) ps on ps.po_id_id = po.id\n", "\n", "                    left join (select po_id, item_id, sum(quantity) as grn_quantity\n", "                        from po.po_grn\n", "                            where insert_ds_ist >= cast(date('{first_live_date}') - interval '60' day as varchar)\n", "                                group by 1,2\n", "                                ) grn on grn.item_id = poi.item_id and grn.po_id = poi.po_id\n", "\n", "                        where ppos.po_state_id in (2,3,8,9,13,14,15)\n", "                            and ppos.po_state_id not in (4,5,10) and po.po_type_id != 11\n", "                            and po.created_at >= cast(date('{first_live_date}') - interval '60' day as timestamp) - interval '330' minute\n", "        ),\n", "\n", "    final as\n", "        (select po_number,\n", "            case when po_status = 0 then null else po_scheduled_date end po_scheduled_date,\n", "            hot_outlet_id, item_id, po_quantity, grn_quantity,\n", "            case when po_status = 0 then 0 else (po_quantity - grn_quantity) end as open_po_quantity\n", "\n", "                from open_po_details\n", "        )\n", "\n", "            select hot_outlet_id, item_id, sum(po_quantity) as ttl_po_quantity, sum(grn_quantity) as ttl_grn_quantity,\n", "                sum(open_po_quantity) as ttl_open_po_quantity\n", "\n", "                    from final\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_hot_outlet_id_list}\n", "\n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return read_sql_query(po_details, trino)\n", "\n", "\n", "po_details = po_details()\n", "\n", "print(po_details.shape)\n", "po_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "624eb223-303e-4ba2-8a22-1c202ebb0f2e", "metadata": {}, "outputs": [], "source": ["be_po_details = po_details.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"ttl_po_quantity\": \"be_ttl_po_quantity\",\n", "        \"ttl_grn_quantity\": \"be_ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\": \"be_ttl_open_po_quantity\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "be955dd7-aafd-4255-a2df-4b8f35724479", "metadata": {}, "outputs": [], "source": ["adding_fe_po_details = pd.merge(\n", "    adding_be_sto_details, po_details, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_po_details = pd.merge(\n", "    adding_fe_po_details, be_po_details, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_po_details[\n", "    [\n", "        \"ttl_po_quantity\",\n", "        \"ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\",\n", "        \"be_ttl_po_quantity\",\n", "        \"be_ttl_grn_quantity\",\n", "        \"be_ttl_open_po_quantity\",\n", "    ]\n", "] = (\n", "    adding_be_po_details[\n", "        [\n", "            \"ttl_po_quantity\",\n", "            \"ttl_grn_quantity\",\n", "            \"ttl_open_po_quantity\",\n", "            \"be_ttl_po_quantity\",\n", "            \"be_ttl_grn_quantity\",\n", "            \"be_ttl_open_po_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_sto_details = pd.DataFrame\n", "po_details = pd.DataFrame\n", "be_po_details = pd.DataFrame\n", "adding_fe_po_details = pd.DataFrame\n", "\n", "print(adding_be_po_details.shape)\n", "adding_be_po_details.head(2)"]}, {"cell_type": "markdown", "id": "9743ec03-4325-4258-b0f3-2fcef66c2d1c", "metadata": {}, "source": ["# frontend min max details"]}, {"cell_type": "code", "execution_count": null, "id": "9dbcf8a8-9270-4771-a7b6-49ebcde65948", "metadata": {}, "outputs": [], "source": ["def min_max():\n", "    min_max = f\"\"\"\n", "    \n", "    with\n", "    min_max as\n", "        (select facility_id, item_id, min_quantity, max_quantity\n", "        \n", "            from ars.item_min_max_quantity\n", "        )\n", "        \n", "            select * from min_max where item_id in {item_id_list} and facility_id in {fe_facility_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(min_max, trino)\n", "\n", "\n", "min_max = min_max()\n", "\n", "print(min_max.shape)\n", "min_max.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "43a12977-97fa-40f3-8fc7-980c7809cf2a", "metadata": {}, "outputs": [], "source": ["adding_min_max = pd.merge(adding_be_po_details, min_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "adding_min_max[[\"min_quantity\", \"max_quantity\"]] = (\n", "    adding_min_max[[\"min_quantity\", \"max_quantity\"]].fillna(0).astype(int)\n", ")\n", "\n", "min_max = pd.DataFrame()\n", "adding_be_po_details = pd.DataFrame()\n", "\n", "print(adding_min_max.shape)\n", "adding_min_max.head(2)"]}, {"cell_type": "markdown", "id": "51991d62-45a6-407d-bf8e-aab5aa3d29dc", "metadata": {}, "source": ["# sales details"]}, {"cell_type": "code", "execution_count": null, "id": "cbf680aa-79a6-4ead-b018-652d84c96181", "metadata": {}, "outputs": [], "source": ["def sales():\n", "    sales = f\"\"\"\n", "    \n", "    with\n", "    sales_details as\n", "        (select * from supply_etls.date_hourly_sales_details),\n", "\n", "    final_sales as\n", "        (select * from sales_details\n", "            where\n", "                insert_ds_ist >= cast('{first_live_date}' as varchar)\n", "        ),\n", "\n", "    final_view as\n", "        (select \n", "            insert_ds_ist,\n", "            outlet_id,\n", "            item_id,\n", "\n", "            count(distinct cart_id) as cart_count,\n", "            sum(sales_quantity) as sales_quantity,\n", "            sum(sales_value) as sales_value\n", "\n", "                from final_sales\n", "\n", "                    group by 1,2,3\n", "        ),\n", "\n", "    final as\n", "        (select\n", "            outlet_id as hot_outlet_id,\n", "            item_id,\n", "\n", "            sum(case when insert_ds_ist = cast(current_date as varchar) then sales_quantity end) as today_sales_qty,\n", "            sum(case when insert_ds_ist = cast(current_date as varchar) then sales_value end) as today_sales_value,\n", "            sum(case when insert_ds_ist = cast(current_date as varchar) then cart_count end) as today_carts,\n", "\n", "            sum(case when insert_ds_ist = cast(current_date - interval '1' day as varchar) then sales_quantity end) as yesterday_sales_qty,\n", "            sum(case when insert_ds_ist = cast(current_date - interval '1' day as varchar) then sales_value end) as yesterday_sales_value,\n", "            sum(case when insert_ds_ist = cast(current_date - interval '1' day as varchar) then cart_count end) as yesterday_carts,\n", "\n", "            sum(case when insert_ds_ist between cast(current_date - interval '7' day as varchar) and cast(current_date - interval '1' day as varchar) then sales_quantity end) as l7days_sales_qty,\n", "            sum(case when insert_ds_ist between cast(current_date - interval '7' day as varchar) and cast(current_date - interval '1' day as varchar) then sales_value end) as l7days_sales_value,\n", "            sum(case when insert_ds_ist between cast(current_date - interval '7' day as varchar) and cast(current_date - interval '1' day as varchar) then cart_count end) as l7days_carts,\n", "\n", "            sum(sales_quantity) as ttl_sales_qty,\n", "            sum(sales_value) as ttl_sales_value,\n", "            sum(cart_count) as ttl_carts\n", "\n", "                from final_view\n", "\n", "                    where\n", "                        outlet_id in {all_inv_outlet_id_list}\n", "                        and item_id in {item_id_list}\n", "\n", "                        group by 1,2\n", "        )\n", "\n", "            select * from final\n", "\n", "\n", "    \"\"\"\n", "    return read_sql_query(sales, trino)\n", "\n", "\n", "sales = sales()\n", "\n", "print(sales.shape)\n", "sales.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e5291916-acf1-48a7-bec5-9965f273e7a7", "metadata": {}, "outputs": [], "source": ["adding_sales_details = pd.merge(adding_min_max, sales, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\")\n", "\n", "adding_sales_details[\n", "    [\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"ttl_sales_qty\",\n", "        \"ttl_sales_value\",\n", "        \"ttl_carts\",\n", "    ]\n", "] = (\n", "    adding_sales_details[\n", "        [\n", "            \"today_sales_qty\",\n", "            \"today_sales_value\",\n", "            \"today_carts\",\n", "            \"yesterday_sales_qty\",\n", "            \"yesterday_sales_value\",\n", "            \"yesterday_carts\",\n", "            \"l7days_sales_qty\",\n", "            \"l7days_sales_value\",\n", "            \"l7days_carts\",\n", "            \"ttl_sales_qty\",\n", "            \"ttl_sales_value\",\n", "            \"ttl_carts\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_min_max = pd.DataFrame()\n", "sales = pd.DataFrame()\n", "\n", "print(adding_sales_details.shape)\n", "adding_sales_details.head(2)"]}, {"cell_type": "markdown", "id": "8a9a10c2-2041-45d2-b6f9-f41563c2e3f6", "metadata": {}, "source": ["# dump details"]}, {"cell_type": "code", "execution_count": null, "id": "197870f5-0e35-4258-80e1-618b2ac61f69", "metadata": {}, "outputs": [], "source": ["def dump():\n", "    dump = f\"\"\"\n", "    \n", "    with\n", "    dump as\n", "        (select date(pos_timestamp + interval '330' minute) as date_, \n", "            outlet_id as hot_outlet_id, item_id, sum(il.\"delta\") as dump_quantity, avg(il.weighted_lp) as avg_lp\n", "\n", "                from ims.ims_inventory_log il\n", "\n", "                    join (select distinct item_id, variant_id from rpc.product_product) rpp on rpp.variant_id = il.variant_id\n", "\n", "                        where insert_ds_ist >= cast('{first_live_date}' as varchar)\n", "                            and inventory_update_type_id in (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "\n", "                                group by 1,2,3\n", "        ),\n", "        \n", "    final as\n", "        (select *, (dump_quantity * avg_lp) as dump_value\n", "            from dump\n", "        )\n", "        \n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then dump_quantity end) as today_dump_quantity,\n", "                sum(case when date_ = current_date then dump_value end) as today_dump_value,\n", "\n", "                sum(case when date_ = current_date - interval '1' day then dump_quantity end) as yesterday_dump_quantity,\n", "                sum(case when date_ = current_date - interval '1' day then dump_value end) as yesterday_dump_value,\n", "\n", "                sum(case when date_ between current_date - interval '7' day and current_date - interval '1' day then dump_quantity end) as l7days_dump_quantity,\n", "                sum(case when date_ between current_date - interval '7' day and current_date - interval '1' day then dump_value end) as l7days_dump_value,\n", "                \n", "                sum(dump_quantity) as ttl_dump_quantity,\n", "                sum(dump_value) as ttl_dump_value\n", "                \n", "                    from final\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id_list}\n", "                    \n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return read_sql_query(dump, trino)\n", "\n", "\n", "dump = dump()\n", "\n", "print(dump.shape)\n", "dump.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "7a647b12-9978-4dc9-a384-f069a72b542f", "metadata": {}, "outputs": [], "source": ["be_dump_details = dump.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"today_dump_quantity\": \"be_today_dump_quantity\",\n", "        \"today_dump_value\": \"be_today_dump_value\",\n", "        \"yesterday_dump_quantity\": \"be_yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\": \"be_yesterday_dump_value\",\n", "        \"l7days_dump_quantity\": \"be_l7days_dump_quantity\",\n", "        \"l7days_dump_value\": \"be_l7days_dump_value\",\n", "        \"ttl_dump_quantity\": \"be_ttl_dump_quantity\",\n", "        \"ttl_dump_value\": \"be_ttl_dump_value\",\n", "    }\n", ")\n", "\n", "print(be_dump_details.shape)\n", "be_dump_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "a496b1f0-8fab-4d19-bc3b-f12bbd6a9d79", "metadata": {}, "outputs": [], "source": ["adding_fe_dump_details = pd.merge(\n", "    adding_sales_details, dump, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_dump_details = pd.merge(\n", "    adding_fe_dump_details,\n", "    be_dump_details,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_dump_details[\n", "    [\n", "        \"today_dump_quantity\",\n", "        \"be_today_dump_quantity\",\n", "        \"today_dump_value\",\n", "        \"be_today_dump_value\",\n", "        \"yesterday_dump_quantity\",\n", "        \"be_yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\",\n", "        \"be_yesterday_dump_value\",\n", "        \"l7days_dump_quantity\",\n", "        \"be_l7days_dump_quantity\",\n", "        \"l7days_dump_value\",\n", "        \"be_l7days_dump_value\",\n", "        \"ttl_dump_quantity\",\n", "        \"be_ttl_dump_quantity\",\n", "        \"ttl_dump_value\",\n", "        \"be_ttl_dump_value\",\n", "    ]\n", "] = (\n", "    adding_be_dump_details[\n", "        [\n", "            \"today_dump_quantity\",\n", "            \"be_today_dump_quantity\",\n", "            \"today_dump_value\",\n", "            \"be_today_dump_value\",\n", "            \"yesterday_dump_quantity\",\n", "            \"be_yesterday_dump_quantity\",\n", "            \"yesterday_dump_value\",\n", "            \"be_yesterday_dump_value\",\n", "            \"l7days_dump_quantity\",\n", "            \"be_l7days_dump_quantity\",\n", "            \"l7days_dump_value\",\n", "            \"be_l7days_dump_value\",\n", "            \"ttl_dump_quantity\",\n", "            \"be_ttl_dump_quantity\",\n", "            \"ttl_dump_value\",\n", "            \"be_ttl_dump_value\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "adding_sales_details = pd.DataFrame()\n", "dump = pd.DataFrame()\n", "be_dump_details = pd.DataFrame()\n", "adding_fe_dump_details = pd.DataFrame()\n", "\n", "print(adding_be_dump_details.shape)\n", "adding_be_dump_details.head(2)"]}, {"cell_type": "markdown", "id": "83c659ff-70ba-4cfd-b2df-2aded4ba0116", "metadata": {}, "source": ["# ARS run id"]}, {"cell_type": "code", "execution_count": null, "id": "5751508c-d350-4565-b277-ecdfdcee962f", "metadata": {}, "outputs": [], "source": ["def ars_run_id():\n", "    ars_run_id = f\"\"\"\n", "    \n", "    with\n", "    ars_run_id as\n", "        (select \n", "            extract(hour from (started_at + interval '330' minute)) as hour_,\n", "            id,\n", "            run_id,\n", "            outlet_id,\n", "            run_date,\n", "            success,\n", "            (started_at + interval '330' minute) as started_at,\n", "            (completed_at + interval '330' minute) as completed_at,\n", "            details,\n", "            facility_id,\n", "            is_simulation,\n", "            simulation_params,\n", "            extra,\n", "            json_format(json_extract(extra,'$.auto_sto_outlets')) AS ars_run,\n", "            replace(json_extract_scalar(simulation_params,'$.run'), '\"', '') as ars_run_flag,\n", "            json_extract_scalar(simulation_params, '$.manual') as manual_run,\n", "            replace(json_extract_scalar(simulation_params, '$.mode'),'\"','') as ars_mode\n", "\n", "                from ars.job_run\n", "\n", "                    where started_at >= cast(current_date as timestamp) - interval '10' day - interval '330' minute\n", "                        and replace(replace(json_format(json_extract(extra,'$.auto_sto_outlets')), '[',''), ']','') is not null\n", "                        and replace(replace(json_format(json_extract(extra,'$.auto_sto_outlets')), '[',''), ']','') != ''\n", "        ),\n", "\n", "    ars_frontend_outlets as\n", "        (select date(started_at) as date_, hour_, facility_id as be_facility_id, cast(replace(replace(split_b, '[', ''), ']', '') as int) as fe_outlet_id, run_id\n", "            from ars_run_id\n", "                cross join unnest(split(ars_run,',')) AS t (split_b)\n", "\n", "                    where ars_run is not null\n", "        ),\n", "\n", "    final_ars_run_id as\n", "        (select afo.date_, afo.hour_, afo.be_facility_id as be_facility_id, afo.fe_outlet_id as hot_outlet_id, rco.facility_id, afo.run_id\n", "\n", "            from ars_frontend_outlets afo\n", "\n", "                join retail.console_outlet rco on rco.id = afo.fe_outlet_id\n", "        )\n", "\n", "            select * from final_ars_run_id   \n", "            \n", "    \"\"\"\n", "    return read_sql_query(ars_run_id, trino)\n", "\n", "\n", "ars_run_id = ars_run_id()\n", "\n", "print(ars_run_id.shape)\n", "ars_run_id.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "07522730-ee2e-453a-8001-2f310335a1be", "metadata": {}, "outputs": [], "source": ["run_id_list = list(ars_run_id[\"run_id\"].unique())\n", "run_id_list = tuple(run_id_list)\n", "len(run_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "5a33685a-0e00-4996-9f24-e1d4d3a7df04", "metadata": {}, "outputs": [], "source": ["min_live_date = ars_run_id.agg({\"date_\": \"min\"}).reset_index().iloc[:, 1][0]\n", "min_live_date"]}, {"cell_type": "markdown", "id": "5592a398-e1ad-448b-8a3b-5359cac123d7", "metadata": {}, "source": ["# truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "237a3d0b-9a32-446b-aa5c-b882d1b2a95f", "metadata": {}, "outputs": [], "source": ["def truncation_details():\n", "    truncation_details = f\"\"\"\n", "    \n", "    with\n", "    truncation_details as\n", "        (select backend_outlet_id as be_hot_outlet_id, frontend_outlet_id as hot_outlet_id, item_id, sto_quantity as v1_indent,\n", "            sto_quantity_post_truncation_in_case as v2_indent, run_id,\n", "            json_extract_scalar(quantity_drops, '$.inward_drop') as inward_drop,\n", "            json_extract_scalar(quantity_drops, '$.storage_drop') as storage_drop,\n", "            json_extract_scalar(quantity_drops, '$.truck_load_drop') as truck_load_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_sku_drop') as picking_capacity_sku_drop,\n", "            json_extract_scalar(quantity_drops, '$.picking_capacity_quantity_drop') as picking_capacity_quantity_drop,\n", "            (sto_quantity_post_truncation - sto_quantity_post_truncation_in_case) as loose_quantity_drop\n", "                from ars.transfers_optimization_results_v2\n", "                    where insert_ds_ist >= cast('{min_live_date}' as varchar)\n", "        )\n", "\n", "            select be_hot_outlet_id, hot_outlet_id, item_id, cast(v1_indent as int) as v1_indent, cast(v2_indent as int) as v2_indent, run_id,\n", "                inward_drop, storage_drop, truck_load_drop, picking_capacity_sku_drop, picking_capacity_quantity_drop, loose_quantity_drop\n", "\n", "                    from truncation_details\n", "\n", "                        where run_id in {run_id_list} and item_id in {item_id_list}\n", "    \n", "    \"\"\"\n", "    return read_sql_query(truncation_details, trino)\n", "\n", "\n", "truncation_details = truncation_details()\n", "\n", "print(truncation_details.shape)\n", "truncation_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "7698f297-2590-4e2c-84e5-700c224adc2c", "metadata": {}, "outputs": [], "source": ["truncation_details[\n", "    [\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"inward_drop\",\n", "        \"storage_drop\",\n", "        \"truck_load_drop\",\n", "        \"picking_capacity_sku_drop\",\n", "        \"picking_capacity_quantity_drop\",\n", "        \"loose_quantity_drop\",\n", "    ]\n", "] = (\n", "    truncation_details[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"inward_drop\",\n", "            \"storage_drop\",\n", "            \"truck_load_drop\",\n", "            \"picking_capacity_sku_drop\",\n", "            \"picking_capacity_quantity_drop\",\n", "            \"loose_quantity_drop\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "adding_run_id = pd.merge(\n", "    truncation_details, ars_run_id, on=[\"run_id\", \"hot_outlet_id\"], how=\"inner\"\n", ")\n", "\n", "adding_run_id[\"date_\"] = pd.to_datetime(adding_run_id[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d53125c8-9252-46de-afbf-55da1b96344f", "metadata": {}, "outputs": [], "source": ["new_truncation_details = adding_run_id.copy()\n", "\n", "ars_run_id = pd.DataFrame\n", "truncation_details = pd.DataFrame\n", "adding_run_id = pd.DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "17054616-abb1-4f1b-90c4-5fb50ab1b967", "metadata": {}, "outputs": [], "source": ["new_truncation_details[\"new_inward_drop\"] = np.where(\n", "    (new_truncation_details[\"inward_drop\"] > 0)\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"inward_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"inward_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"inward_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_storage_drop\"] = np.where(\n", "    (new_truncation_details[\"storage_drop\"] > 0)\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"picking_capacity_sku_drop\"])\n", "    & (\n", "        new_truncation_details[\"storage_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"storage_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"storage_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_truck_load_drop\"] = np.where(\n", "    (new_truncation_details[\"truck_load_drop\"] > 0)\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"truck_load_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (new_truncation_details[\"truck_load_drop\"] > new_truncation_details[\"loose_quantity_drop\"]),\n", "    new_truncation_details[\"truck_load_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_sku_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_sku_drop\"] > 0)\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"picking_capacity_sku_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_sku_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_sku_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_picking_capacity_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"picking_capacity_quantity_drop\"] > 0)\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"inward_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"storage_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"truck_load_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "        > new_truncation_details[\"loose_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"picking_capacity_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"new_loose_quantity_drop\"] = np.where(\n", "    (new_truncation_details[\"loose_quantity_drop\"] > 0)\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"inward_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"storage_drop\"])\n", "    & (new_truncation_details[\"loose_quantity_drop\"] > new_truncation_details[\"truck_load_drop\"])\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_sku_drop\"]\n", "    )\n", "    & (\n", "        new_truncation_details[\"loose_quantity_drop\"]\n", "        > new_truncation_details[\"picking_capacity_quantity_drop\"]\n", "    ),\n", "    new_truncation_details[\"loose_quantity_drop\"],\n", "    0,\n", ")\n", "\n", "new_truncation_details[\"total_drop_quantity\"] = (\n", "    new_truncation_details[\"new_inward_drop\"]\n", "    + new_truncation_details[\"new_storage_drop\"]\n", "    + new_truncation_details[\"new_truck_load_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_sku_drop\"]\n", "    + new_truncation_details[\"new_picking_capacity_quantity_drop\"]\n", "    + new_truncation_details[\"new_loose_quantity_drop\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cd80eaf3-ffb3-4990-aec0-76aca85dc0e9", "metadata": {}, "outputs": [], "source": ["new_truncation_details = new_truncation_details[\n", "    [\n", "        \"date_\",\n", "        \"run_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"item_id\",\n", "        \"v1_indent\",\n", "        \"v2_indent\",\n", "        \"new_inward_drop\",\n", "        \"new_storage_drop\",\n", "        \"new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\",\n", "        \"total_drop_quantity\",\n", "    ]\n", "]\n", "\n", "print(new_truncation_details.shape)\n", "new_truncation_details.head(2)"]}, {"cell_type": "markdown", "id": "25cfb7bd-4fc6-48f1-8b87-908c94ade760", "metadata": {}, "source": ["# frontend truncation details"]}, {"cell_type": "code", "execution_count": null, "id": "6bb88cd7-fb20-41d2-b288-64dba3216ad7", "metadata": {}, "outputs": [], "source": ["frontend_current_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_current_date_truncation = frontend_current_date_truncation[\n", "    frontend_current_date_truncation[\"date_\"] == datetime.today().strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_current_date_truncation = (\n", "    frontend_current_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t_v1_indent\",\n", "        \"v2_indent\": \"t_v2_indent\",\n", "        \"new_inward_drop\": \"t_new_inward_drop\",\n", "        \"new_storage_drop\": \"t_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_1_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_1_date_truncation = frontend_l_1_date_truncation[\n", "    frontend_l_1_date_truncation[\"date_\"]\n", "    == (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "]\n", "\n", "frontend_l_1_date_truncation = (\n", "    frontend_l_1_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t1_v1_indent\",\n", "        \"v2_indent\": \"t1_v2_indent\",\n", "        \"new_inward_drop\": \"t1_new_inward_drop\",\n", "        \"new_storage_drop\": \"t1_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t1_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t1_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t1_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "frontend_l_7_date_truncation = new_truncation_details.copy()\n", "\n", "frontend_l_7_date_truncation = frontend_l_7_date_truncation[\n", "    (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        >= (datetime.today() - pd.DateOffset(days=7)).strftime(\"%Y-%m-%d\")\n", "    )\n", "    & (\n", "        frontend_l_7_date_truncation[\"date_\"]\n", "        <= (datetime.today() - pd.DateOffset(days=1)).strftime(\"%Y-%m-%d\")\n", "    )\n", "]\n", "\n", "frontend_l_7_date_truncation = (\n", "    frontend_l_7_date_truncation.groupby(\n", "        [\n", "            \"be_facility_id\",\n", "            \"be_hot_outlet_id\",\n", "            \"facility_id\",\n", "            \"hot_outlet_id\",\n", "            \"item_id\",\n", "        ]\n", "    )[\n", "        [\n", "            \"v1_indent\",\n", "            \"v2_indent\",\n", "            \"new_inward_drop\",\n", "            \"new_storage_drop\",\n", "            \"new_truck_load_drop\",\n", "            \"new_picking_capacity_sku_drop\",\n", "            \"new_picking_capacity_quantity_drop\",\n", "            \"new_loose_quantity_drop\",\n", "            \"total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .agg(\"sum\")\n", "    .reset_index()\n", ").rename(\n", "    columns={\n", "        \"v1_indent\": \"t7_v1_indent\",\n", "        \"v2_indent\": \"t7_v2_indent\",\n", "        \"new_inward_drop\": \"t7_new_inward_drop\",\n", "        \"new_storage_drop\": \"t7_new_storage_drop\",\n", "        \"new_truck_load_drop\": \"t7_new_truck_load_drop\",\n", "        \"new_picking_capacity_sku_drop\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"new_picking_capacity_quantity_drop\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"new_loose_quantity_drop\": \"t7_new_loose_quantity_drop\",\n", "        \"total_drop_quantity\": \"t7_total_drop_quantity\",\n", "    }\n", ")\n", "\n", "new_truncation_details.shape, frontend_current_date_truncation.shape, frontend_l_1_date_truncation.shape, frontend_l_7_date_truncation.shape"]}, {"cell_type": "code", "execution_count": null, "id": "806c9df9-39a5-47f6-a308-629f7984b9a3", "metadata": {}, "outputs": [], "source": ["adding_t_truncation_details = pd.merge(\n", "    adding_be_dump_details,\n", "    frontend_current_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_dump_details = pd.DataFrame()\n", "new_truncation_details = pd.DataFrame()\n", "frontend_current_date_truncation = pd.DataFrame()\n", "\n", "\n", "adding_t1_truncation_details = pd.merge(\n", "    adding_t_truncation_details,\n", "    frontend_l_1_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t_truncation_details = pd.DataFrame()\n", "frontend_l_1_date_truncation = pd.DataFrame()\n", "\n", "adding_t7_truncation_details = pd.merge(\n", "    adding_t1_truncation_details,\n", "    frontend_l_7_date_truncation,\n", "    on=[\n", "        \"item_id\",\n", "        \"be_facility_id\",\n", "        \"be_hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "    ],\n", "    how=\"left\",\n", ")\n", "\n", "adding_t1_truncation_details = pd.DataFrame()\n", "frontend_l_7_date_truncation = pd.DataFrame()\n", "\n", "print(adding_t7_truncation_details.shape)\n", "adding_t7_truncation_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "cdd275cd-949e-4ddc-8edb-7236d4c9957b", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details[\n", "    [\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "    ]\n", "] = (\n", "    adding_t7_truncation_details[\n", "        [\n", "            \"t_v1_indent\",\n", "            \"t_v2_indent\",\n", "            \"t_new_inward_drop\",\n", "            \"t_new_storage_drop\",\n", "            \"t_new_truck_load_drop\",\n", "            \"t_new_picking_capacity_sku_drop\",\n", "            \"t_new_picking_capacity_quantity_drop\",\n", "            \"t_new_loose_quantity_drop\",\n", "            \"t_total_drop_quantity\",\n", "            \"t1_v1_indent\",\n", "            \"t1_v2_indent\",\n", "            \"t1_new_inward_drop\",\n", "            \"t1_new_storage_drop\",\n", "            \"t1_new_truck_load_drop\",\n", "            \"t1_new_picking_capacity_sku_drop\",\n", "            \"t1_new_picking_capacity_quantity_drop\",\n", "            \"t1_new_loose_quantity_drop\",\n", "            \"t1_total_drop_quantity\",\n", "            \"t7_v1_indent\",\n", "            \"t7_v2_indent\",\n", "            \"t7_new_inward_drop\",\n", "            \"t7_new_storage_drop\",\n", "            \"t7_new_truck_load_drop\",\n", "            \"t7_new_picking_capacity_sku_drop\",\n", "            \"t7_new_picking_capacity_quantity_drop\",\n", "            \"t7_new_loose_quantity_drop\",\n", "            \"t7_total_drop_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_t7_truncation_details[\"updated_at_ist\"] = pd.to_datetime(\n", "    datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")\n", "\n", "print(adding_t7_truncation_details.shape)\n", "adding_t7_truncation_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "008d69a4-8ca4-4523-9b7f-9630d7680c9b", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details = adding_t7_truncation_details[\n", "    [\n", "        \"go_live_date\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"p_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"status\",\n", "        \"assortment_status\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"open_sto_qty\",\n", "        \"ttl_po_quantity\",\n", "        \"ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"ttl_sales_qty\",\n", "        \"ttl_sales_value\",\n", "        \"ttl_carts\",\n", "        \"today_dump_quantity\",\n", "        \"today_dump_value\",\n", "        \"yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\",\n", "        \"l7days_dump_quantity\",\n", "        \"l7days_dump_value\",\n", "        \"ttl_dump_quantity\",\n", "        \"ttl_dump_value\",\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"be_actual_inv\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"be_open_sto_qty\",\n", "        \"be_ttl_po_quantity\",\n", "        \"be_ttl_grn_quantity\",\n", "        \"be_ttl_open_po_quantity\",\n", "        \"be_today_dump_quantity\",\n", "        \"be_today_dump_value\",\n", "        \"be_yesterday_dump_quantity\",\n", "        \"be_yesterday_dump_value\",\n", "        \"be_l7days_dump_quantity\",\n", "        \"be_l7days_dump_value\",\n", "        \"be_ttl_dump_quantity\",\n", "        \"be_ttl_dump_value\",\n", "        \"updated_at_ist\",\n", "        \"frontend_merchant_id\",\n", "    ]\n", "]\n", "\n", "adding_t7_truncation_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "4f17e233-9409-4b31-abb0-dec402e2d151", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details[\"insert_ds_ist\"] = adding_t7_truncation_details[\"updated_at_ist\"]\n", "\n", "adding_t7_truncation_details = adding_t7_truncation_details[\n", "    [\n", "        \"insert_ds_ist\",\n", "        \"go_live_date\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"p_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"status\",\n", "        \"assortment_status\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"open_sto_qty\",\n", "        \"ttl_po_quantity\",\n", "        \"ttl_grn_quantity\",\n", "        \"ttl_open_po_quantity\",\n", "        \"min_quantity\",\n", "        \"max_quantity\",\n", "        \"today_sales_qty\",\n", "        \"today_sales_value\",\n", "        \"today_carts\",\n", "        \"yesterday_sales_qty\",\n", "        \"yesterday_sales_value\",\n", "        \"yesterday_carts\",\n", "        \"l7days_sales_qty\",\n", "        \"l7days_sales_value\",\n", "        \"l7days_carts\",\n", "        \"ttl_sales_qty\",\n", "        \"ttl_sales_value\",\n", "        \"ttl_carts\",\n", "        \"today_dump_quantity\",\n", "        \"today_dump_value\",\n", "        \"yesterday_dump_quantity\",\n", "        \"yesterday_dump_value\",\n", "        \"l7days_dump_quantity\",\n", "        \"l7days_dump_value\",\n", "        \"ttl_dump_quantity\",\n", "        \"ttl_dump_value\",\n", "        \"t_v1_indent\",\n", "        \"t_v2_indent\",\n", "        \"t_new_inward_drop\",\n", "        \"t_new_storage_drop\",\n", "        \"t_new_truck_load_drop\",\n", "        \"t_new_picking_capacity_sku_drop\",\n", "        \"t_new_picking_capacity_quantity_drop\",\n", "        \"t_new_loose_quantity_drop\",\n", "        \"t_total_drop_quantity\",\n", "        \"t1_v1_indent\",\n", "        \"t1_v2_indent\",\n", "        \"t1_new_inward_drop\",\n", "        \"t1_new_storage_drop\",\n", "        \"t1_new_truck_load_drop\",\n", "        \"t1_new_picking_capacity_sku_drop\",\n", "        \"t1_new_picking_capacity_quantity_drop\",\n", "        \"t1_new_loose_quantity_drop\",\n", "        \"t1_total_drop_quantity\",\n", "        \"t7_v1_indent\",\n", "        \"t7_v2_indent\",\n", "        \"t7_new_inward_drop\",\n", "        \"t7_new_storage_drop\",\n", "        \"t7_new_truck_load_drop\",\n", "        \"t7_new_picking_capacity_sku_drop\",\n", "        \"t7_new_picking_capacity_quantity_drop\",\n", "        \"t7_new_loose_quantity_drop\",\n", "        \"t7_total_drop_quantity\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"be_actual_inv\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"be_open_sto_qty\",\n", "        \"be_ttl_po_quantity\",\n", "        \"be_ttl_grn_quantity\",\n", "        \"be_ttl_open_po_quantity\",\n", "        \"be_today_dump_quantity\",\n", "        \"be_today_dump_value\",\n", "        \"be_yesterday_dump_quantity\",\n", "        \"be_yesterday_dump_value\",\n", "        \"be_l7days_dump_quantity\",\n", "        \"be_l7days_dump_value\",\n", "        \"be_ttl_dump_quantity\",\n", "        \"be_ttl_dump_value\",\n", "        \"frontend_merchant_id\",\n", "    ]\n", "]\n", "\n", "adding_t7_truncation_details.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "0ea39e2d-17f3-48ec-ac48-0a27ca986f49", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"timestamp(6)\", \"description\": \"name of zone\"},\n", "    {\"name\": \"go_live_date\", \"type\": \"varchar\", \"description\": \"name of zone\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"name of city\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"unique identifier for DS facility\",\n", "    },\n", "    {\n", "        \"name\": \"facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"unique identifier for DS facility name\",\n", "    },\n", "    {\"name\": \"event_flag\", \"type\": \"varchar\", \"description\": \"type of assortment\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"type of event\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"unique identifier for item\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"product name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"product type\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0 category\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar\", \"description\": \"l1 category\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar\", \"description\": \"l2 category\"},\n", "    {\"name\": \"manufacturer_id\", \"type\": \"real\", \"description\": \"manufacturer id\"},\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"manufacturer name\",\n", "    },\n", "    {\"name\": \"status\", \"type\": \"integer\", \"description\": \"assortment flag\"},\n", "    {\n", "        \"name\": \"assortment_status\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"identify part of assortment\",\n", "    },\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\", \"description\": \"shelf inventory\"},\n", "    {\"name\": \"ttl_blocked_qty\", \"type\": \"integer\", \"description\": \"blocked inventory\"},\n", "    {\n", "        \"name\": \"net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"after blocked inventory\",\n", "    },\n", "    {\"name\": \"pending_putaway\", \"type\": \"integer\", \"description\": \"pending putaway\"},\n", "    {\"name\": \"open_sto_qty\", \"type\": \"integer\", \"description\": \"open STO quantity\"},\n", "    {\n", "        \"name\": \"ttl_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total PO raised quantity\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_grn_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total GRN quantity\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total Open PO quantity\",\n", "    },\n", "    {\"name\": \"min_quantity\", \"type\": \"integer\", \"description\": \"min quantity\"},\n", "    {\"name\": \"max_quantity\", \"type\": \"integer\", \"description\": \"max quantoty\"},\n", "    {\n", "        \"name\": \"today_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today sold quantity\",\n", "    },\n", "    {\"name\": \"today_sales_value\", \"type\": \"integer\", \"description\": \"today sold value\"},\n", "    {\"name\": \"today_carts\", \"type\": \"integer\", \"description\": \"today carts\"},\n", "    {\n", "        \"name\": \"yesterday_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday sold quantity\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_sales_value\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday sold value\",\n", "    },\n", "    {\"name\": \"yesterday_carts\", \"type\": \"integer\", \"description\": \"yesterday carts\"},\n", "    {\n", "        \"name\": \"l7days_sales_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days sold quantity\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_sales_value\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days sold value\",\n", "    },\n", "    {\"name\": \"l7days_carts\", \"type\": \"integer\", \"description\": \"last 7 days carts\"},\n", "    {\"name\": \"ttl_sales_qty\", \"type\": \"integer\", \"description\": \"total sales quantity\"},\n", "    {\"name\": \"ttl_sales_value\", \"type\": \"integer\", \"description\": \"total sales value\"},\n", "    {\"name\": \"ttl_carts\", \"type\": \"integer\", \"description\": \"total carts\"},\n", "    {\n", "        \"name\": \"today_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"today dump quantity\",\n", "    },\n", "    {\"name\": \"today_dump_value\", \"type\": \"real\", \"description\": \"today dump value\"},\n", "    {\n", "        \"name\": \"yesterday_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"yesterday dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"yesterday_dump_value\",\n", "        \"type\": \"real\",\n", "        \"description\": \"yesterday dump value\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"last 7 days dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"l7days_dump_value\",\n", "        \"type\": \"real\",\n", "        \"description\": \"last 7 day dump value\",\n", "    },\n", "    {\n", "        \"name\": \"ttl_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"total dump quantity\",\n", "    },\n", "    {\"name\": \"ttl_dump_value\", \"type\": \"real\", \"description\": \"total dump value\"},\n", "    {\n", "        \"name\": \"t_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"today total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t1_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t1_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"yesterday total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v1_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days actual transfer demand\",\n", "    },\n", "    {\n", "        \"name\": \"t7_v2_indent\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days final transfer initiate\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_inward_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days inward drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_storage_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days storage drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_truck_load_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days truck load drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_sku_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days picking capacity skus drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_picking_capacity_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days picking capacity drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_new_loose_quantity_drop\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days loose drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"t7_total_drop_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"last 7 days total drop quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend hot outlet id\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend inv outlet id\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"backend facility id\"},\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\n", "        \"name\": \"be_actual_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend shelf inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend blocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend after blocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend pending putaway\",\n", "    },\n", "    {\n", "        \"name\": \"be_open_sto_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend open STO quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total PO raised quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_grn_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total GRN quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"backend total Open PO quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend today dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_today_dump_value\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend today dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend yesterday dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_yesterday_dump_value\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend yesterday dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend last 7 days dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_l7days_dump_value\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend last 7 day dump value\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_dump_quantity\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend total dump quantity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_dump_value\",\n", "        \"type\": \"real\",\n", "        \"description\": \"backend total dump value\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_merchant_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"frontend merchant id\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "645cd61d-defc-4329-b71b-bb14755d2509", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"new_event_metrics_reporting\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"event_flag\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"item details\",\n", "}\n", "\n", "pb.to_trino(adding_t7_truncation_details, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "4a6a215e-accd-48bc-ac18-d2fcb22c3a83", "metadata": {}, "outputs": [], "source": ["adding_t7_truncation_details = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "06487b7b-e4fe-4c48-8d0f-6dcc656df4f4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}