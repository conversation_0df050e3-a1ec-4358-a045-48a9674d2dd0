alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: new_event_tracking
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
  retries: 1
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/event_flows/report/new_event_tracking
paused: false
pool: povms_pool
project_name: event_flows
schedule:
  end_date: '2025-08-17T00:00:00'
  interval: 0 */2 * * *
  start_date: '2024-09-02T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 24
