{"cells": [{"cell_type": "code", "execution_count": null, "id": "8d40f3f5-f967-4718-8c33-7b2528312e6e", "metadata": {}, "outputs": [], "source": ["# Set to Refresh = or Manual\n", "# Fix Assortment --  Done\n", "# Add Planned Sale qty, Open PO, GRN qty till date -- Add\n", "# Festive and regular Filter -- Add"]}, {"cell_type": "code", "execution_count": null, "id": "bc0057ed-e6d3-4509-9c38-7f97103bc4a2", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "import warnings\n", "import math\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "fd8aaf92-9259-46b2-a5f0-8cc942341821", "metadata": {}, "outputs": [], "source": ["CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "a410fb6f-277c-485a-b6e9-8546e901e071", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e2ea1408-43a7-4e55-8179-77c35ff07db6", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "markdown", "id": "f6e2e879-afa4-433b-b36d-35a319c3beea", "metadata": {}, "source": ["## City Mapping: Demand to Supply"]}, {"cell_type": "code", "execution_count": null, "id": "6455a90f-f78c-4fa3-a966-bdaa5bcc2746", "metadata": {}, "outputs": [], "source": ["city_mapping_df = pb.from_sheets(\n", "    sheetid=\"10e6U4ez0JOMytyg_Iok_he9FsHlLJ4uZCI7hUKv4lWQ\", sheetname=\"city_mapping\"\n", ")\n", "\n", "# Add for NCR\n", "ncr_subset = city_mapping_df[city_mapping_df[\"cluster_name\"].isin([\"HR-NCR\", \"UP-NCR\"])]\n", "ncr_subset[\"cluster_name\"] = \"NCR\"\n", "\n", "# Add for Bangalore\n", "blore_subset = city_mapping_df[city_mapping_df[\"cluster_name\"].isin([\"Bengaluru\"])]\n", "blore_subset[\"cluster_name\"] = \"Bangalore\"\n", "\n", "# Add for Ludhiana\n", "ludhiana_subset = city_mapping_df.head(1).copy()\n", "ludhiana_subset[\"cluster_name\"] = \"Ludhiana\"\n", "ludhiana_subset[\"city\"] = \"Ludhiana\"\n", "\n", "# Add for Ludhiana\n", "chandigarh_subset = city_mapping_df.head(1).copy()\n", "chandigarh_subset[\"cluster_name\"] = \"Chandigarh\"\n", "chandigarh_subset[\"city\"] = \"Chandigarh\"\n", "\n", "city_mapping_df = pd.concat(\n", "    [city_mapping_df, ncr_subset, blore_subset, ludhiana_subset, chandigarh_subset]\n", ")\n", "\n", "# Add for Pan India\n", "pan_subset = city_mapping_df.copy()\n", "pan_subset[\"cluster_name\"] = \"Pan India\"\n", "pan_subset = pan_subset.drop_duplicates()\n", "\n", "city_mapping_df = pd.concat([city_mapping_df, pan_subset])"]}, {"cell_type": "markdown", "id": "b0ba0981-46bd-4794-86ef-ea560a9ffad3", "metadata": {}, "source": ["# Event SKUs - Input"]}, {"cell_type": "markdown", "id": "0925226e-f222-4c90-9778-92b26636b493", "metadata": {}, "source": ["### Diwali - Input Read + Item ID Flagging"]}, {"cell_type": "code", "execution_count": null, "id": "96f09cd8-7512-4c14-a7f9-5d57d2fdeb50", "metadata": {"tags": []}, "outputs": [], "source": ["category_input_diwali_df = pb.from_sheets(\n", "    \"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"Sheet2\"\n", ")  # \"diwali_base_input\")\n", "category_input_diwali_df.columns = [\n", "    \"L0\",\n", "    \"cluster_name\",\n", "    \"L1\",\n", "    \"Manufacture\",\n", "    \"item_id\",\n", "    \"item_name\",\n", "    \"PType\",\n", "    \"MRP\",\n", "    \"quantity_to_sell\",\n", "    \"Selling Price\",\n", "    \"1st Lot Quantity\",\n", "    \"1st Lot Go lIve Date(Backend)\",\n", "    \"2nd Lot Quantity\",\n", "    \"2nd Lot Go Live Date(Backend)\",\n", "    \"3rd Lot Quantity\",\n", "    \"3rd Lot Go Live Date(Backend)\",\n", "    \"Analytics RM\",\n", "    \"Item Classification(Fragile/Non Fragile/Fresh/Premium))\",\n", "    \"1,038,451,903\",\n", "    \"event_flag\",\n", "    \"<70% <PERSON><PERSON> ?(Y/N)\",\n", "    \"RTV(Y/N)\",\n", "    \"Remarks\",\n", "    \"Post Event Status(Y/N)\",\n", "    \"Phasing 1\",\n", "    \"Phasing 2\",\n", "    \"go_live_date\",\n", "]\n", "\n", "category_input_diwali_df = category_input_diwali_df[\n", "    [\"cluster_name\", \"item_id\", \"quantity_to_sell\", \"event_flag\", \"go_live_date\"]\n", "].drop_duplicates()\n", "category_input_diwali_df[\"quantity_to_sell\"] = category_input_diwali_df[\n", "    \"quantity_to_sell\"\n", "].str.replace(\",\", \"\")\n", "category_input_diwali_df[\"quantity_to_sell\"] = np.where(\n", "    category_input_diwali_df[\"quantity_to_sell\"] == \"\",\n", "    \"0\",\n", "    category_input_diwali_df[\"quantity_to_sell\"],\n", ")\n", "category_input_diwali_df[\"quantity_to_sell\"] = (\n", "    category_input_diwali_df[\"quantity_to_sell\"].fillna(0).astype(int)\n", ")\n", "category_input_diwali_df = category_input_diwali_df[\n", "    category_input_diwali_df.event_flag.isin([\"Diwali\", \"Regular\"])\n", "]\n", "category_input_diwali_df = category_input_diwali_df[\n", "    (~category_input_diwali_df.item_id.str.contains(\"NPI\"))\n", "    & (~category_input_diwali_df.item_id.str.contains(\"B\"))\n", "]\n", "category_input_diwali_df[\"item_id\"] = category_input_diwali_df[\"item_id\"].astype(int)\n", "\n", "# Add City Mapping\n", "category_input_diwali_df = pd.merge(\n", "    category_input_diwali_df, city_mapping_df, on=[\"cluster_name\"], how=\"left\"\n", ")\n", "category_input_diwali_df.head()"]}, {"cell_type": "raw", "id": "85315c8e-8e0f-499a-9383-03a408f9b8fa", "metadata": {}, "source": ["category_input_diwali_df[(category_input_diwali_df.cluster_name == 'NCR') & (category_input_diwali_df.item_id == 10116155)]"]}, {"cell_type": "code", "execution_count": null, "id": "9db6a2c5-4e18-4440-a292-b2f95e5580bf", "metadata": {}, "outputs": [], "source": ["planned_sales = category_input_diwali_df[\n", "    [\"city\", \"item_id\", \"go_live_date\", \"event_flag\", \"quantity_to_sell\"]\n", "].drop_duplicates()\n", "planned_sales[[\"item_id\", \"quantity_to_sell\"]] = planned_sales[\n", "    [\"item_id\", \"quantity_to_sell\"]\n", "].astype(int)\n", "planned_sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1b7b3af9-b6d3-4627-8f57-2a242ee1a795", "metadata": {}, "outputs": [], "source": ["category_input_diwali_df = category_input_diwali_df[\n", "    [\"city\", \"item_id\", \"go_live_date\", \"event_flag\"]\n", "].drop_duplicates()\n", "category_input_diwali_df[\"assortment_type\"] = \"Diwali\"\n", "# category_input_diwali_df = category_input_diwali_df.rename(columns = {'event_flag':'assortment_type'})\n", "category_input_diwali_df[\"active\"] = 1\n", "category_input_diwali_df.head(1)"]}, {"cell_type": "markdown", "id": "37b2ab93-3f98-43e6-b6b6-c3794c767725", "metadata": {}, "source": ["### Navratri - Input Read + Item ID Flagging"]}, {"cell_type": "code", "execution_count": null, "id": "f29b8d31-4f1b-445e-b29f-b9b0f1a28f44", "metadata": {}, "outputs": [], "source": ["category_input_navratri_df = pb.from_sheets(\n", "    \"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"navratri_input\"\n", ")  # \"diwali_base_input\")\n", "category_input_navratri_df[\"event_flag\"] = \"Navratri\"\n", "category_input_navratri_df = category_input_navratri_df[\n", "    [\"city\", \"item_id\", \"go_live_date\", \"event_flag\", \"assortment_type\", \"active\"]\n", "]\n", "# category_input_navratri_df['active'] = 1"]}, {"cell_type": "markdown", "id": "89865a3c-73f5-482f-8c4a-3a7a908502b6", "metadata": {}, "source": ["## Final input"]}, {"cell_type": "code", "execution_count": null, "id": "f325eede-b8eb-4460-972e-92781855a76c", "metadata": {}, "outputs": [], "source": ["assortment_input = pd.concat([category_input_diwali_df, category_input_navratri_df])\n", "assortment_input[\"item_id\"] = assortment_input[\"item_id\"].astype(int)\n", "\n", "assortment_input[\"go_live_date\"] = pd.to_datetime(assortment_input[\"go_live_date\"])\n", "assortment_input = assortment_input.drop_duplicates()\n", "\n", "# Create item tuple\n", "items = tuple(list(assortment_input[\"item_id\"].unique()))\n", "\n", "assortment_input.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8325fb2c-d959-4bed-a903-c8a00b15e1fb", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(assortment_input, \"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"assortment_input\")"]}, {"cell_type": "code", "execution_count": null, "id": "3eca75f7-773a-4bc6-9db7-88058c972f75", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(planned_sales, \"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"planned_sales\")"]}, {"cell_type": "code", "execution_count": null, "id": "0f255d69-ac36-4aff-ad7f-85c2173bacf5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}