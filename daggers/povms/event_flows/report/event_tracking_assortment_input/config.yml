alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: event_tracking_assortment_input
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURE15HB
path: povms/event_flows/report/event_tracking_assortment_input
paused: true
pool: povms_pool
project_name: event_flows
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 0 * * *
  start_date: '2022-09-24T05:30:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
