alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: assortment
dag_type: workflow
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03R91S1LKH
path: povms/availability_beauty/workflow/assortment
paused: false
project_name: availability_beauty
schedule:
  end_date: '2023-08-24T00:00:00'
  interval: 5 4,16,20 * * *
  start_date: '2023-06-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
pool: povms_pool
