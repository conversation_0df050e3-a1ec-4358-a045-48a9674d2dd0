{"cells": [{"cell_type": "code", "execution_count": null, "id": "dab348f8-cbf0-4cb2-992a-ad17589c0eb9", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "id": "c1736588-62af-461e-b430-89fba99e60df", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\n", "    \"1a9-RFpfktUtjDcY-z62dgqkUmUBWR3wz5DPWpZ4YwOY\", \"Assortment Sync\", clear_cache=True\n", ")\n", "\n", "df = df.rename(columns={\"item _id\": \"item_id\"})\n", "\n", "df = df[~((df[\"item_id\"] == \"\"))]\n", "\n", "\n", "df[\"item_id\"] = df[\"item_id\"].fillna(0).astype(float)\n", "\n", "df = df[df[\"item_id\"] >= 0]\n", "\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bb086659-0ff9-405f-9412-d6d7bfc8a7dd", "metadata": {}, "outputs": [], "source": ["df[\"item_id\"] = df[\"item_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "fc29cf95-f9fd-4e7e-98f9-40141028be0d", "metadata": {}, "outputs": [], "source": ["df[\"updated_at_ist\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "668f76dc-3379-4108-ae3b-96b73bf92e2c", "metadata": {}, "outputs": [], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "3603a96a-1ab7-493d-8868-18bf2024da01", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"unique item id\"},\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"updated at timestamp\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2d672bd2-7c1c-4e71-856c-8e35871ca89f", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"beauty_assortment_new\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\"],\n", "    \"sortkey\": [\"item_id\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"beauty Assortment\",\n", "}\n", "\n", "pb.to_redshift(df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "44666f64-39f1-42ba-b20b-4476058adbd3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}