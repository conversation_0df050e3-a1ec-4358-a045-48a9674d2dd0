{"cells": [{"cell_type": "code", "execution_count": null, "id": "675a4505-78cf-4d59-b5d6-ecd20ac26bb3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from calendar import monthrange\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "b2e70f4a-f725-49c0-98ce-a99cfdc652ea", "metadata": {}, "outputs": [], "source": ["df = pb.from_sheets(\"1a9-RFpfktUtjDcY-z62dgqkUmUBWR3wz5DPWpZ4YwOY\", \"Assortment Sync\")\n", "# df = pd.read_csv('Beauty - Assortment Sync.csv')\n", "df = df[[\"item_id\"]]\n", "\n", "df = df[~((df[\"item_id\"] == \"\"))]\n", "\n", "item_ids = tuple(list(df[\"item_id\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "27293231-93be-4825-ab46-e05a87d58bd1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "009369de-31cc-45c1-aca4-014b94625c58", "metadata": {}, "outputs": [], "source": ["def Beauty():\n", "    Beauty = f\"\"\"\n", "    \n", "      Select Distinct item_id,outlet_id,item_name,brand,manufacturer,mrp,l0,l1,l2,facility_id,outlet,master_assortment_status,backend_outlet_id,be_facility_id,be_facility\n", ",vendor_id,vendor_name,load_size,load_type,margin_percentage,actual_quantity_fe,actual_quantity_be,l_30_sales,l_7_sales,bk_open_po_qty as open_po,BE_status,landing_price,inner_case_size,outer_case_size,case_sensitivity_type,case_size,tat_days,min_quantity,max_quantity,po_days,po_cycle,L31_customer_count,L31_carts,L31_sales_quantity,L31_sales_value\n", "from \n", "    (SELECT Distinct ag.item_id, ag.outlet_id,pp.item_name,pp.brand,manufacturer,variant_mrp as mrp,l0, l1, l2,\n", "        o.facility_id,\n", "        o.name as outlet,\n", "        case when ma.master_assortment_substate_id = 1 then 'Active'\n", "        when ma.master_assortment_substate_id = 2 then 'Inactive' \n", "        when ma.master_assortment_substate_id = 3 then 'Temp-inactive' else 'not_part_of assortment' end as master_assortment_status\n", "        ,\n", "        case when mac.master_assortment_substate_id = 1 then 'Active'\n", "        when mac.master_assortment_substate_id = 2 then 'Inactive' \n", "        when mac.master_assortment_substate_id = 3 then 'Temp-inactive' else 'not_part_of assortment' end as BE_status,\n", "        tg.backend_outlet_id,fo.facility_id as BE_facility_id,vendor_facility_identifier as Be_facility ,va.vendor_id,vendor_name,load_size,load_type,tot.on_invoice_margin_value as margin_percentage,\n", "        iii.quantity as actual_quantity_FE,iis.quantity as actual_quantity_BE,bb.qty_ordered as l_30_sales,bc.qty_ordered as l_7_sales,remaining_quantity as bk_open_po_qty,(variant_mrp*(100-tot.on_invoice_margin_value)/100) as landing_price,\n", "        inner_case_size,outer_case_size,cs.case_sensitivity_type,tat_days,po_days,po_cycle,min_quantity,max_quantity,gfh.customer_count as L31_customer_count,gfh.carts as L31_carts,gfh.sales_quantity as L31_sales_quantity,gfh.sales_value as L31_sales_value,\n", "        case when cs.case_sensitivity_type = 2 then outer_case_size else inner_case_size end as case_size\n", "    FROM (select a.item_id, b.outlet_id from (select item_id from lake_rpc.product_product where item_id in {item_ids}) a\n", "    CROSS JOIN (select outlet_id from lake_po.physical_facility_outlet_mapping where outlet_id  in (4300,4252,4281,4284,4285,2340)\n", "     ) b) ag      \n", "     left join lake_ims.ims_item_inventory iii ON iii.item_id = ag.item_id AND iii.outlet_id = ag.outlet_id\n", "     LEFT JOIN lake_ims.ims_item_blocked_inventory iibi ON ag.item_id = iibi.item_id AND ag.outlet_id = iibi.outlet_id\n", "     LEFT JOIN lake_retail.console_outlet o ON ag.outlet_id = o.id\n", "     inner join\n", "        (Select Distinct item_id,max (id.name) as  item_name,max (brand) as brand,max(variant_mrp)as variant_mrp ,pb.manufacturer_id,pm.name as manufacturer,id.inner_case_size,id.outer_case_size\n", "          from lake_rpc.product_product id\n", "          left join (select id, name, manufacturer_id from lake_rpc.product_brand) pb on pb.id = id.brand_id\n", "          left join (select id, name from lake_rpc.product_manufacturer) pm on pm.id = pb.manufacturer_id\n", "          where item_id in {item_ids}\n", "          and active =1 group by 1,5,6,7,8) pp on ag.item_id = pp.item_id \n", "      left join (select Distinct item_id, max(l0)as l0,max (l1) as l1,max (12) as l2 from lake_rpc.item_category_details where item_id in \n", "                 {item_ids}\n", "                 group by 1) cd on cd.item_id = ag.item_id\n", "      left join lake_rpc.product_facility_master_assortment ma on o.facility_id = ma.facility_id and ag.item_id = ma.item_id and ma.active =1\n", "      left join (Select Distinct backend_outlet_id,frontend_outlet_id from lake_ars.backend_frontend_transfer_attributes where frontend_outlet_id in (4300,4252,4281,4284,4285,2340)  and backend_outlet_id in (1103,4167)) tg on tg.frontend_outlet_id = ag.outlet_id\n", "      left join lake_retail.warehouse_outlet_mapping io  on io.warehouse_id =tg.backend_outlet_id \n", "      left join lake_ims.ims_item_inventory iis ON iis.item_id = ag.item_id AND iis.outlet_id = io.cloud_store_id\n", "      left join (select item_id,outlet_id,\n", "        sum(case when status_id not in (3,5) then quantity else 0 end) as qty_ordered\n", "         from(select o.outlet as outlet_id, date(o.scheduled_at+interval '5.5 Hours') as delivery_date,\n", "          o.order_id, oi.item_id, oi.quantity, o.status_id, os.name as status\n", "          from lake_ims.ims_order_details o\n", "          left join lake_ims.ims_order_items oi on o.id = oi.order_details_id\n", "          left join lake_ims.ims_order_status os on o.status_id = os.id\n", "          where date(o.scheduled_at+interval '5.5 Hours') between current_date-interval '30 days' and current_date)group by 1,2) bb on ag.outlet_id = bb.outlet_id and ag.item_id = bb.item_id\n", "     left join (select item_id,outlet_id,\n", "         sum(case when status_id not in (3,5) then quantity else 0 end) as qty_ordered\n", "        from(select o.outlet as outlet_id, date(o.scheduled_at+interval '5.5 Hours') as delivery_date,\n", "    o.order_id, oi.item_id, oi.quantity, o.status_id, os.name as status\n", "    from lake_ims.ims_order_details o\n", "    left join lake_ims.ims_order_items oi on o.id = oi.order_details_id\n", "    left join lake_ims.ims_order_status os on o.status_id = os.id\n", "    where date(o.scheduled_at+interval '5.5 Hours') between current_date-interval '7 days' and current_date)group by 1,2) bc on ag.outlet_id = bc.outlet_id and ag.item_id = bc.item_id\n", "      left join (select outlet_id,facility_id from lake_po.physical_facility_outlet_mapping where outlet_id  in (435,713,1103,1623,1643,483,4167,4162,1724,1576,2648,2900,1565\n", "       )) fo on tg.backend_outlet_id = fo.outlet_id\n", "    left join lake_vms.vms_vendor_facility_alignment va on fo.facility_id = va.facility_id and ag.item_id = va.item_id\n", "    left join lake_rpc.product_facility_master_assortment mac on fo.facility_id = mac.facility_id and ag.item_id = mac.item_id and mac.active =1\n", "    left join lake_vms.vms_vendor vd on vd.id = va.vendor_id\n", "    Left join (Select item_id,facility_id,case_sensitivity_type,vendor_id, active from lake_vms.vendor_item_physical_facility_attributes) cs on ag.item_id = cs.item_id and cs.vendor_id = va.vendor_id and fo.facility_id = cs.facility_id and cs.active = 1\n", "    left join lake_vms.vendor_physical_facility_attributes vs on  fo.facility_id = vs.facility_id and va.vendor_id = vs.vendor_id\n", "    left join lake_po.physical_facility kk on fo.facility_id = kk.facility_id\n", "    left join lake_rpc.tot_margin tot on fo.facility_id = tot.facility_id and ag.item_id = tot.item_id\n", "    left join (select item_id,outlet_id,vendor_id,tat_days,active from lake_po.item_outlet_vendor_tat) tt on ag.item_id = tt.item_id and va.vendor_id = tt.vendor_id  and tg.backend_outlet_id = tt.outlet_id and tt.active =1\n", "    left join (select vendor_id,manufacturer_id,facility_id,po_days,po_cycle,active from lake_vms.vendor_manufacturer_physical_facility_attributes) mpfa on mpfa.vendor_id = va.vendor_id and mpfa.manufacturer_id = pp.manufacturer_id and mpfa.facility_id = fo.facility_id and mpfa.active = 1\n", "    left join (select facility_id,item_id,min_quantity,max_quantity from lake_ars.item_min_max_quantity) mmx on mmx.item_id = ag.item_id and o.facility_id = mmx.facility_id\n", "    Left join (select p.outlet_id,poi.item_id,sum (remaining_quantity) as remaining_quantity\n", "       from  lake_po.purchase_order_items poi\n", "     inner join lake_po.purchase_order p on p.id=poi.po_id\n", "     Left Join lake_po.po_schedule ps on p.id = ps.po_id_id\n", "     inner join lake_retail.console_outlet o on o.id=p.outlet_id\n", "     inner join lake_po.purchase_order_status posa on posa.po_id = p.id\n", "     inner join lake_po.purchase_order_state posta on posta.id = posa.po_state_id\n", "     Where  (posta.id in (2,3,4,10,13,14,15) or p.is_multiple_grn = 1)\n", "     and posta.id not in (4,5,8,10) and p.active = 1\n", "     and item_id in {item_ids} group by 1,2) op on op.outlet_id =  tg.backend_outlet_id and op.item_id = ag.item_id\n", "\n", "     left join (with\n", "item_details as\n", "    (select\n", "        rpc.item_id,\n", "        rpc.name || ' ' || variant_description as item_name,\n", "        pb.manufacturer_id,\n", "        pm.name as manufacturer_name,\n", "        variant_mrp as mrp\n", "            from lake_rpc.product_product rpc\n", "                left join lake_rpc.product_brand pb on pb.id = rpc.brand_id\n", "                left join lake_rpc.product_manufacturer pm on pm.id = pb.manufacturer_id\n", "                    where rpc.id in (select max(id) as id from lake_rpc.product_product pp where pp.active = 1 and pp.approved = 1\n", "                        group by item_id)\n", "    ),\n", "\n", "sales as\n", "    (select\n", "        (oi.install_ts + interval '5.5 Hours') as order_date,\n", "        city_name,\n", "        od.facility_id,\n", "        pl.item_id,\n", "        oi.selling_price,\n", "        (oi.selling_price*1.0000/pl.multiplier+0.000) as item_selling_price,\n", "        oi.order_id,\n", "        od.customer_id,\n", "        pl.multiplier,\n", "        ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) as sales_quantity,\n", "        ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) * (oi.selling_price*1.0000/pl.multiplier) as sales_value\n", "\n", "            from lake_oms_bifrost.oms_order_item oi\n", "\n", "                left join\n", "                    (select distinct ipr.product_id,\n", "                        case when ipr.item_id is null then ipom_0.item_id else ipr.item_id end as item_id,\n", "                        case when ipr.item_id is not null then COALESCE(ipom.multiplier,1) else COALESCE(ipom_0.multiplier,1) end as multiplier\n", "                            from lake_rpc.item_product_mapping ipr\n", "\n", "                                left join\n", "                                    dwh.dim_item_product_offer_mapping ipom on ipom.product_id = ipr.product_id\n", "                                        and ipr.item_id = ipom.item_id\n", "                                left join\n", "                                    dwh.dim_item_product_offer_mapping ipom_0 on ipom_0.product_id = ipr.product_id\n", "                    ) pl on pl.product_id = oi.product_id\n", "                \n", "                join\n", "                    lake_oms_bifrost.oms_order oo on oo.id = oi.order_id and oo.type in ('RetailForwardOrder','RetailSuborder','')\n", "                        and oo.install_ts > current_date-31\n", "\n", "                join \n", "                    (select distinct facility_id, cast(ancestor as bigint) as order_id, customer_id, cl.name as city_name\n", "                        from lake_ims.ims_order_details od\n", "\n", "                            join\n", "                                lake_retail.console_outlet rco on rco.id = outlet\n", "                            left join\n", "                                lake_retail.console_location cl on cl.id = rco.tax_location_id\n", "                                    where status_id not in (3,4,5)\n", "                                        and od.created_at > current_date-31\n", "                    ) od on od.order_id = oi.order_id\n", "\n", "                        where\n", "                            oi.install_ts  > current_date-31\n", "    ),\n", "\n", "final as\n", "    (select city_name, date(order_date) as date_, order_id, facility_id, customer_id,\n", "        s.item_id, item_name, sales_quantity, sales_value\n", "            from sales s\n", "                join \n", "                    item_details id on id.item_id = s.item_id\n", "\n", "                    where sales_quantity > 0 and s.item_id is not null\n", "    )\n", "    \n", "        select\n", "            city_name,\n", "            facility_id,\n", "            cf.name as facility_name,\n", "            item_id,\n", "            item_name,\n", "            count(distinct customer_id) as customer_count,\n", "            count(distinct order_id) as carts,\n", "            sum(sales_quantity) as sales_quantity,\n", "            sum(sales_value) as sales_value\n", "            \n", "                from final f\n", "                \n", "                join\n", "                    lake_crates.facility cf on cf.id = f.facility_id\n", "                    \n", "                \n", "\n", "                    group by 1,2,3,4,5) gfh on gfh.facility_id = o.facility_id and gfh.item_id = ag.item_id\n", "\n", "\n", "     \n", "\n", ")\n", "\n", "\n", "order by 2,5,1\n", "\n", "\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(Beauty, redshift)\n", "\n", "\n", "Beauty = Beauty()"]}, {"cell_type": "code", "execution_count": null, "id": "1fbe3afc-9d4d-4a0f-bd1c-38d3c6faa2f0", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(Beauty, \"1KB13JA6qVEmtypNGWBh4bahNtr996Mku4mkIMn_017c\", \"Sheet1\")"]}, {"cell_type": "code", "execution_count": null, "id": "a949f555-811b-4669-8c70-8bdd46fb0381", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "41be77f2-d9fa-4da0-abc7-853940fe6d9d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}