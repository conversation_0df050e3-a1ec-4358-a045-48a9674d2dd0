{"cells": [{"cell_type": "markdown", "id": "0741f887-e128-4ae4-bacd-9525e1c37975", "metadata": {}, "source": ["# Inputs - Packages, Functions"]}, {"cell_type": "code", "execution_count": null, "id": "85f88b3e-2004-4d5e-9a4a-dab9681f7a10", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "\n", "CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "c3d5d3ea-74c2-41d2-89f3-dc79635b74cc", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "34f586d8-5744-44ae-81b8-ecf1a7da79c4", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "raw", "id": "7720a31a-cee8-46dc-bd0a-dd34cfef752f", "metadata": {}, "source": ["end_date = date.today()\n", "end_date"]}, {"cell_type": "code", "execution_count": null, "id": "2e5a6bf7-c3b0-4f5b-95c0-1aef7b7fc941", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1IANj2HAdLgOVobU95NdpqExN78OV7uv8edNnD1XvymY\"\n", "backfill_setup = pb.from_sheets(sheet_id, \"cart_breaker_availability_backfill_config\")"]}, {"cell_type": "code", "execution_count": null, "id": "56afb562-a959-49bd-bad9-69c5bb107ba6", "metadata": {}, "outputs": [], "source": ["should_backfill = backfill_setup.iloc[:, 0][0]\n", "backfill_start_date = pd.to_datetime(backfill_setup.iloc[:, 1][0]).date()\n", "backfill_end_date = pd.to_datetime(backfill_setup.iloc[:, 2][0]).date()\n", "backfill_start_date, backfill_end_date, should_backfill"]}, {"cell_type": "markdown", "id": "e0afe5b3-38d8-4bf4-8836-ea017de8518a", "metadata": {}, "source": ["# Fetch Cart Breakers"]}, {"cell_type": "code", "execution_count": null, "id": "a775cd4a-e19a-47df-a566-5cf4d6949ff8", "metadata": {}, "outputs": [], "source": ["if should_backfill == \"yes\":\n", "    cart_breakers_query = f\"\"\"\n", "        WITH item_base AS (\n", "            SELECT DISTINCT p.item_id, p.name AS item_name, icd.l0, icd.l1, icd.l2\n", "            FROM lake_rpc.item_details p\n", "            LEFT JOIN lake_rpc.item_category_details icd ON p.item_id = icd.item_id\n", "            WHERE p.active = 1 AND p.approved = 1\n", "        ),\n", "\n", "        base AS (\n", "            SELECT \n", "                cl.name as city, oi.item_id, item_name, l0, l1, sum(oi.quantity) AS quantity,\n", "                SUM(CASE WHEN price IS NULL THEN 0 ELSE oi.quantity*price END) AS GMV\n", "            FROM lake_ims.ims_order_details od\n", "\n", "            LEFT JOIN lake_ims.ims_order_items oi on od.id = oi.order_details_id \n", "\n", "            LEFT JOIN lake_ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "\n", "            LEFT JOIN item_base ib on ib.item_id = oi.item_id\n", "\n", "            INNER JOIN lake_retail.console_outlet co on co.id = outlet and co.active = 1 and co.business_type_id = 7\n", "\n", "            LEFT JOIN lake_retail.console_location cl on cl.id = co.tax_location_id\n", "\n", "            WHERE status_id IN (1,2)\n", "            AND  od.created_at BETWEEN date('{backfill_start_date}') - interval '45 Days' - interval '5.5 Hours' AND date('{backfill_start_date}') - interval '1 Days' - interval '5.5 Hours'\n", "\n", "            GROUP BY 1,2,3,4,5\n", "            ORDER BY l1, quantity DESC\n", "        ),\n", "\n", "        total_l1_sales AS (\n", "            SELECT city, l1, SUM(quantity) AS total_l1_quantity, SUM(GMV) AS total_l1_GMV\n", "            FROM base\n", "            GROUP BY 1, 2\n", "        ),\n", "\n", "        item_share_on_l1 AS (\n", "            SELECT b.*, total_l1_quantity, total_l1_GMV, 1.0000*quantity / total_l1_quantity*1.0000 AS item_share_on_l1\n", "            FROM base b\n", "            LEFT JOIN total_l1_sales tls ON b.l1 = tls.l1 AND b.city = tls.city\n", "        ),\n", "\n", "        final_calculations AS (\n", "            SELECT *, SUM(item_share_on_l1) OVER (PARTITION BY city, l1 ORDER BY quantity DESC rows unbounded preceding) AS cumulative_sum\n", "            FROM item_share_on_l1\n", "        )\n", "        SELECT * FROM final_calculations\n", "        WHERE cumulative_sum <= 0.50\n", "        \"\"\"\n", "    cart_breakers_df = read_sql_query(cart_breakers_query, CON_REDSHIFT)\n", "    cartbreaker_list = tuple(\n", "        list(\n", "            cart_breakers_df[cart_breakers_df.item_id.isna() == False][\n", "                \"item_id\"\n", "            ].unique()\n", "        )\n", "    )\n", "\n", "    # Get unique cities\n", "    unique_city_df = (\n", "        cart_breakers_df[[\"city\"]]\n", "        .drop_duplicates()\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "\n", "    # Get Active DS for the City\n", "    active_ds_facility_query = f\"\"\"SELECT distinct facility_id from lake_retail.console_outlet where active = 1 and business_type_id = 7 and facility_id is not null and facility_id != 806\"\"\"\n", "    active_ds_facility_master_df = read_sql_query(\n", "        active_ds_facility_query, CON_REDSHIFT\n", "    )\n", "    print(\"Active Facility Fetch Complete\")\n", "\n", "    facility_list = list(active_ds_facility_master_df[\"facility_id\"].unique())\n", "    if active_ds_facility_master_df.shape[0] < 2:\n", "        facility_list.append(9999999)\n", "    facility_list = tuple(facility_list)\n", "\n", "    # Get Hourly Snaps for Unavailable Items (Inventory < 1)\n", "    packaged_hourly_snap_query = f\"\"\"\n", "        with remove_item_list AS (\n", "            SELECT item_id FROM lake_rpc.item_category_details\n", "            WHERE l0_id IN (1487) AND l2_id IN (116,1961,63,1127)\n", "            GROUP BY 1\n", "\n", "            UNION\n", "\n", "            SELECT distinct item_id\n", "            FROM lake_rpc.product_product\n", "            WHERE id IN (\n", "                SELECT MAX(id) AS id FROM lake_rpc.product_product\n", "                WHERE active = 1 AND approved = 1 GROUP BY item_id\n", "            ) AND outlet_type IN (1) AND perishable IN (1)\n", "            GROUP BY 1\n", "        ),\n", "\n", "\n", "        grocery_base AS (\n", "            WITH base_0 AS (\n", "                SELECT * FROM rpc_daily_availability\n", "                WHERE order_date between date('{backfill_start_date}') + interval '5 hours' and date('{backfill_start_date}') + interval '1 Days'\n", "\n", "            ),\n", "\n", "            base as \n", "            (\n", "            select * from base_0\n", "            ),\n", "\n", "            rpc_base AS (\n", "                SELECT item_id, facility_id, DATE(order_date) AS date_, actual_quantity, blocked_quantity, \n", "                EXTRACT(hour FROM order_date) AS hour_ \n", "                FROM base\n", "                WHERE facility_id IN {facility_list}\n", "                and item_id in {cartbreaker_list}\n", "                GROUP BY 1,2,3,4,5,6\n", "            ),\n", "            availability AS (\n", "                SELECT date_, hour_, facility_id, item_id, actual_quantity - blocked_quantity AS inventory_at_hour\n", "                FROM rpc_base \n", "            )\n", "            SELECT *, 'Packaged Goods' AS assortment_type FROM availability\n", "        )\n", "        SELECT * FROM grocery_base WHERE item_id NOT IN (SELECT item_id FROM remove_item_list GROUP BY 1)\n", "        \"\"\"\n", "\n", "    packaged_hourly_snap_df = read_sql_query(packaged_hourly_snap_query, CON_REDSHIFT)\n", "    print(\"Packaged Hourly Snap Fetch Complete\")\n", "\n", "    fnv_per_hourly_snap_query = f\"\"\"\n", "        WITH fnv_base AS (\n", "            SELECT *, 'FnV' AS assortment_type \n", "            FROM (\n", "                SELECT date_, hour_, facility_id, item_id, current_inv as inventory_at_hour\n", "                FROM ( \n", "                    SELECT base.facility_id, base.item_id, DATE(base.updated_at) AS date_, EXTRACT(hour FROM base.updated_at) AS hour_, base.current_inv\n", "                    FROM metrics.fnv_hourly_details base\n", "                    INNER JOIN (\n", "                        SELECT outlet_id, item_id, date(updated_at) AS date_, extract(hour FROM updated_at) AS hour, MAX(updated_at) AS updated_at\n", "                        FROM metrics.fnv_hourly_details\n", "                        WHERE updated_at between date('{backfill_start_date}') + interval '5 hours' and date('{backfill_start_date}') + interval '1 Days'\n", "                        GROUP BY 1, 2, 3, 4\n", "                    ) latest ON base.outlet_id = latest.outlet_id\n", "                   AND base.item_id = latest.item_id\n", "                   AND base.updated_at = latest.updated_at\n", "                   WHERE base.updated_at between date('{backfill_start_date}') + interval '5 hours' and date('{backfill_start_date}') + interval '1 Days'\n", "                   AND base.facility_id IN {facility_list}\n", "                   GROUP BY 1, 2, 3, 4, 5\n", "                )\n", "                WHERE item_id in {cartbreaker_list}\n", "            )\n", "\n", "        ),\n", "\n", "        persh_base AS (\n", "            SELECT *, 'Perishable' AS assortment_type \n", "            FROM (\n", "                SELECT date_, hour_, facility_id, item_id, current_inv AS inventory_at_hour\n", "                FROM ( \n", "                    SELECT base.facility_id, base.item_id, DATE(base.updated_at) AS date_, extract(HOUR FROM base.updated_at) AS hour_, base.current_inv\n", "                    FROM metrics.perishable_hourly_details_v2 base\n", "                    INNER JOIN (\n", "                        SELECT outlet_id, item_id, DATE(updated_at) AS date_, EXTRACT(hour FROM updated_at) AS hour, MAX(updated_at) AS updated_at\n", "                        FROM metrics.perishable_hourly_details_v2\n", "                        WHERE updated_at between date('{backfill_start_date}') + interval '5 hours' and date('{backfill_start_date}') + interval '1 Days'\n", "                        GROUP BY 1, 2, 3, 4 \n", "                    ) latest ON base.outlet_id = latest.outlet_id\n", "                    AND base.item_id = latest.item_id\n", "                    AND base.updated_at = latest.updated_at\n", "                    WHERE base.updated_at between date('{backfill_start_date}') + interval '5 hours' and date('{backfill_start_date}') + interval '1 Days'\n", "                    AND base.facility_id IN {facility_list}\n", "                    GROUP BY 1, 2, 3, 4, 5\n", "                )\n", "            WHERE item_id in {cartbreaker_list}\n", "            )\n", "\n", "        )\n", "        SELECT * FROM fnv_base\n", "        UNION\n", "        SELECT * FROM persh_base\"\"\"\n", "\n", "    fnv_per_hourly_snap_df = read_sql_query(fnv_per_hourly_snap_query, CON_REDSHIFT)\n", "    print(\"FnV Perishable Hourly Snap Fetch Complete\")\n", "    packaged_hourly_snap_df = packaged_hourly_snap_df[\n", "        ~packaged_hourly_snap_df.item_id.isin(\n", "            tuple(list(fnv_per_hourly_snap_df[\"item_id\"].unique()))\n", "        )\n", "    ]\n", "\n", "    hourly_snap_base_df = pd.concat([packaged_hourly_snap_df, fnv_per_hourly_snap_df])\n", "\n", "    facility_city_mapping_query = f\"\"\"\n", "        select distinct facility_id,  cl.name as city\n", "        from lake_retail.console_outlet co\n", "        left join lake_retail.console_location cl on co.tax_location_id = cl.id\n", "        where active = 1 and business_type_id = 7\n", "        \"\"\"\n", "\n", "    facility_city_mapping_df = read_sql_query(facility_city_mapping_query, CON_REDSHIFT)\n", "\n", "    city_store_score_query = f\"\"\"\n", "        select \n", "                city, facility_id, store_weight\n", "        from\n", "            metrics.city_store_penetration\n", "        where \n", "            updated_at in (select max(updated_at) updated_at from metrics.city_store_penetration where updated_at <= date('{backfill_start_date}'))\n", "        \"\"\"\n", "\n", "    city_store_score_df = read_sql_query(city_store_score_query, CON_REDSHIFT)\n", "\n", "    city_item_score_query = f\"\"\"\n", "        select \n", "            city, assortment_type, item_id, 1.0000000*cart_penetration::float as cart_penetration, 1.0000000*weights::float as weights\n", "        from \n", "            metrics.city_item_cart_penetration\n", "        where \n", "            updated_at in (select max(updated_at) as updated_at from metrics.city_item_cart_penetration where updated_at <= date('{backfill_start_date}'))\n", "        \"\"\"\n", "\n", "    city_item_score_df = read_sql_query(city_item_score_query, CON_REDSHIFT)\n", "\n", "    hourly_snap_base_df = pd.merge(\n", "        hourly_snap_base_df, facility_city_mapping_df, on=[\"facility_id\"], how=\"left\"\n", "    )\n", "    hourly_snap_base_df = pd.merge(\n", "        hourly_snap_base_df,\n", "        cart_breakers_df[[\"city\", \"item_id\", \"l0\"]].drop_duplicates(),\n", "        on=[\"city\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "    hourly_snap_base_df[\"cart_breaker_flag\"] = np.where(\n", "        hourly_snap_base_df[\"l0\"].isna(), 0, 1\n", "    )\n", "    hourly_snap_base_df = hourly_snap_base_df.drop(columns={\"l0\"})\n", "\n", "    cart_breaker_availability = hourly_snap_base_df[\n", "        hourly_snap_base_df.cart_breaker_flag == 1\n", "    ]\n", "    cart_breaker_availability[\"available_flag\"] = np.where(\n", "        cart_breaker_availability[\"inventory_at_hour\"] < 1, 0, 1\n", "    )\n", "    cart_breaker_availability = pd.merge(\n", "        cart_breaker_availability,\n", "        city_item_score_df[[\"city\", \"item_id\", \"weights\"]].drop_duplicates(),\n", "        on=[\"city\", \"item_id\"],\n", "        how=\"left\",\n", "    )\n", "    cart_breaker_availability[\"available_score\"] = (\n", "        cart_breaker_availability[\"weights\"]\n", "        * cart_breaker_availability[\"available_flag\"]\n", "    )\n", "\n", "    available_number = (\n", "        cart_breaker_availability.groupby([\"date_\", \"hour_\", \"city\", \"facility_id\"])\n", "        .agg({\"item_id\": \"nunique\", \"available_score\": \"sum\", \"weights\": \"sum\"})\n", "        .reset_index()\n", "        .rename(columns={\"available_score\": \"available_items\"})\n", "    )\n", "    available_number[\"wt_availability_facility\"] = (\n", "        available_number[\"available_items\"] / available_number[\"weights\"]\n", "    )  # Need to do this as score is not normalized.\n", "\n", "    available_number = pd.merge(\n", "        available_number, city_store_score_df, on=[\"city\", \"facility_id\"], how=\"left\"\n", "    )\n", "    available_number[\"store_weight\"] = available_number[\"store_weight\"].astype(float)\n", "    available_number[\"wt_availability_facility\"] = available_number[\n", "        \"wt_availability_facility\"\n", "    ].astype(float)\n", "    available_number[\"facility_ava_scores\"] = (\n", "        available_number[\"store_weight\"] * available_number[\"wt_availability_facility\"]\n", "    )\n", "\n", "    city_number = (\n", "        available_number.groupby([\"date_\", \"hour_\", \"city\"])\n", "        .agg(\n", "            {\n", "                \"facility_id\": \"nunique\",\n", "                \"facility_ava_scores\": \"sum\",\n", "                \"store_weight\": \"sum\",\n", "            }\n", "        )\n", "        .reset_index()\n", "        .rename(\n", "            columns={\n", "                \"facility_id\": \"count_of_ds\",\n", "                \"facility_ava_scores\": \"weighted_availability\",\n", "            }\n", "        )\n", "    )\n", "\n", "    city_number[\"weighted_availability\"] = (\n", "        city_number[\"weighted_availability\"] / city_number[\"store_weight\"]\n", "    )\n", "\n", "    backfill_setup[\"date\"] = pd.to_datetime(backfill_setup[\"date\"])\n", "    backfill_setup[\"stop_date\"] = pd.to_datetime(backfill_setup[\"stop_date\"])\n", "\n", "    if pd.to_datetime(backfill_start_date) < pd.to_datetime(backfill_end_date):\n", "        backfill_setup[\"date\"] = backfill_setup[\"date\"] + <PERSON><PERSON>ta(days=1)\n", "    else:\n", "        backfill_setup[\"backfill\"] = \"no\"\n", "\n", "    backfill_setup[\"date\"] = pd.to_datetime(backfill_setup[\"date\"]).dt.date\n", "    backfill_setup[\"stop_date\"] = pd.to_datetime(backfill_setup[\"stop_date\"]).dt.date\n", "\n", "    print(backfill_setup)\n", "    output_sheet = \"cart_breaker_availability_backfill_config\"\n", "    pb.to_sheets(backfill_setup, sheet_id, output_sheet)\n", "\n", "    city_number[\"date_time\"] = pd.to_datetime(\n", "        city_number[\"date_\"].astype(str)\n", "        + \" \"\n", "        + city_number[\"hour_\"].astype(str)\n", "        + \":00:00\"\n", "    )\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date for snapshot\"},\n", "        {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour of the day\"},\n", "        {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"city for facility\"},\n", "        {\"name\": \"count_of_ds\", \"type\": \"float\", \"description\": \"count of active DS\"},\n", "        {\n", "            \"name\": \"weighted_availability\",\n", "            \"type\": \"float\",\n", "            \"description\": \"weighted availability for cart breakers for the city\",\n", "        },\n", "        {\"name\": \"store_weight\", \"type\": \"float\", \"description\": \"city store weights\"},\n", "        {\"name\": \"date_time\", \"type\": \"timestamp\", \"description\": \"timestamp in table\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"carts_lost_cart_breaker_availability_city\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"city\", \"date_\", \"hour_\"],\n", "        \"sortkey\": [\"date_\", \"city\"],\n", "        \"incremental_key\": \"date_time\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"Table tracks hourly cart breaker weighted availability driving carts lost\",\n", "    }\n", "    pb.to_redshift(city_number, **kwargs)\n", "    print(\"city_number write complete\")\n", "\n", "    available_number[\"date_time\"] = pd.to_datetime(\n", "        available_number[\"date_\"].astype(str)\n", "        + \" \"\n", "        + available_number[\"hour_\"].astype(str)\n", "        + \":00:00\"\n", "    )\n", "    available_number = available_number.rename(\n", "        columns={\"item_id\": \"count_of_cart_breaker_items\"}\n", "    )\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date for snapshot\"},\n", "        {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour of the day\"},\n", "        {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"city for facility\"},\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"unique identifier for dark store in the city\",\n", "        },\n", "        {\n", "            \"name\": \"count_of_cart_breaker_items\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"count_of_cart_breaker_items\",\n", "        },\n", "        {\n", "            \"name\": \"available_items\",\n", "            \"type\": \"float\",\n", "            \"description\": \"availability weighted scores for cart breakers for the facility\",\n", "        },\n", "        {\n", "            \"name\": \"weights\",\n", "            \"type\": \"float\",\n", "            \"description\": \"weighted scores (not based on availability) for cart breakers for the facility\",\n", "        },\n", "        {\n", "            \"name\": \"wt_availability_facility\",\n", "            \"type\": \"float\",\n", "            \"description\": \"weighted availability for cart breakers for the facility - available_items / weights (As cart breaker scores are not normalized)\",\n", "        },\n", "        {\"name\": \"store_weight\", \"type\": \"float\", \"description\": \"city store weights\"},\n", "        {\n", "            \"name\": \"facility_ava_scores\",\n", "            \"type\": \"float\",\n", "            \"description\": \"city store weights * wt_availability_facility\",\n", "        },\n", "        {\"name\": \"date_time\", \"type\": \"timestamp\", \"description\": \"timestamp in table\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"carts_lost_cart_breaker_availability_facility\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"city\", \"date_\", \"hour_\"],\n", "        \"sortkey\": [\"date_\", \"city\"],\n", "        \"incremental_key\": \"date_time\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"Table tracks hourly cart breaker weighted availability driving carts lost\",\n", "    }\n", "    pb.to_redshift(available_number, **kwargs)\n", "    print(\"available_number write complete\")\n", "\n", "    cart_breaker_availability[\"date_time\"] = pd.to_datetime(\n", "        cart_breaker_availability[\"date_\"].astype(str)\n", "        + \" \"\n", "        + cart_breaker_availability[\"hour_\"].astype(str)\n", "        + \":00:00\"\n", "    )\n", "\n", "    column_dtypes = [\n", "        {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date for snapshot\"},\n", "        {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour of the day\"},\n", "        {\n", "            \"name\": \"facility_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"unique identifier for dark store in the city\",\n", "        },\n", "        {\n", "            \"name\": \"item_id\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"unique identifier for item\",\n", "        },\n", "        {\n", "            \"name\": \"inventory_at_hour\",\n", "            \"type\": \"float\",\n", "            \"description\": \"inventory for the hour\",\n", "        },\n", "        {\n", "            \"name\": \"assortment_type\",\n", "            \"type\": \"varchar(70)\",\n", "            \"description\": \"type of assortment\",\n", "        },\n", "        {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"city for facility\"},\n", "        {\n", "            \"name\": \"cart_breaker_flag\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"flag to identify cart breakers\",\n", "        },\n", "        {\n", "            \"name\": \"available_flag\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"flag to if SKU is available\",\n", "        },\n", "        {\n", "            \"name\": \"weights\",\n", "            \"type\": \"float\",\n", "            \"description\": \"weighted scores (not based on availability) for cart breakers for the facility\",\n", "        },\n", "        {\n", "            \"name\": \"available_score\",\n", "            \"type\": \"float\",\n", "            \"description\": \"weights * available_flag\",\n", "        },\n", "        {\"name\": \"date_time\", \"type\": \"timestamp\", \"description\": \"timestamp in table\"},\n", "    ]\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"carts_lost_cart_breaker_item_availability\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"date_\", \"hour_\", \"facility_id\", \"item_id\"],\n", "        \"sortkey\": [\"date_\", \"facility_id\"],\n", "        \"incremental_key\": \"date_time\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"Table tracks hourly cart breaker availability at SKU level\",\n", "    }\n", "    pb.to_redshift(cart_breaker_availability, **kwargs)\n", "    print(\"cart_breaker_availability write complete\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}