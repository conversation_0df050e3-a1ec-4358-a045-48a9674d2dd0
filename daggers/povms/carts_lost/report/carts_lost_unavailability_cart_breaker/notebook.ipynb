{"cells": [{"cell_type": "markdown", "id": "3a90ab8b-980e-4363-98d5-d751cd32503e", "metadata": {}, "source": ["# Inputs - Packages, Functions"]}, {"cell_type": "code", "execution_count": null, "id": "61eafb73-91a8-4151-8267-0030e853dfdc", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "0fc47c82-b3df-4675-82fc-de34d2ba1e86", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "19032fb1-1643-440a-9177-18ab5bd0819b", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "6236376f-b4f7-4774-8612-04e283f30a9c", "metadata": {}, "outputs": [], "source": ["end_date = date.today()\n", "end_date"]}, {"cell_type": "code", "execution_count": null, "id": "387ac4fe-28a7-4579-bb80-b507914291de", "metadata": {}, "outputs": [], "source": ["final_daily_carts = pd.DataFrame()\n", "final_total_carts = pd.DataFrame()"]}, {"cell_type": "markdown", "id": "0e523e3a-3498-4e69-a5cf-81bd8a9ade66", "metadata": {}, "source": ["# Fetch Cart Breakers"]}, {"cell_type": "code", "execution_count": null, "id": "76a3cb96-36a3-42e9-a334-a46075ae8681", "metadata": {}, "outputs": [], "source": ["cart_breakers_query = f\"\"\"\n", "    WITH item_base AS (\n", "        SELECT DISTINCT p.item_id, p.name AS item_name, icd.l0, icd.l1, icd.l2\n", "        FROM lake_rpc.item_details p\n", "        LEFT JOIN lake_rpc.item_category_details icd ON p.item_id = icd.item_id\n", "        WHERE p.active = 1 AND p.approved = 1\n", "    ),\n", "\n", "    base AS (\n", "        SELECT \n", "            cl.name as city, oi.item_id, item_name, l0, l1, sum(oi.quantity) AS quantity,\n", "            SUM(CASE WHEN price IS NULL THEN 0 ELSE oi.quantity*price END) AS GMV\n", "        FROM lake_ims.ims_order_details od\n", "        \n", "        LEFT JOIN lake_ims.ims_order_items oi on od.id = oi.order_details_id \n", "        \n", "        LEFT JOIN lake_ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "        \n", "        LEFT JOIN item_base ib on ib.item_id = oi.item_id\n", "        \n", "        INNER JOIN lake_retail.console_outlet co on co.id = outlet and co.active = 1 and co.business_type_id = 7\n", "        \n", "        LEFT JOIN lake_retail.console_location cl on cl.id = co.tax_location_id\n", "        \n", "        WHERE status_id IN (1,2)\n", "        AND  od.created_at BETWEEN current_date - interval '45 Days' - interval '5.5 Hours' AND current_date - interval '1 Days' - interval '5.5 Hours'\n", "        \n", "        GROUP BY 1,2,3,4,5\n", "        ORDER BY l1, quantity DESC\n", "    ),\n", "\n", "    total_l1_sales AS (\n", "        SELECT city, l1, SUM(quantity) AS total_l1_quantity, SUM(GMV) AS total_l1_GMV\n", "        FROM base\n", "        GROUP BY 1, 2\n", "    ),\n", "    \n", "    item_share_on_l1 AS (\n", "        SELECT b.*, total_l1_quantity, total_l1_GMV, 1.0000*quantity / total_l1_quantity*1.0000 AS item_share_on_l1\n", "        FROM base b\n", "        LEFT JOIN total_l1_sales tls ON b.l1 = tls.l1 AND b.city = tls.city\n", "    ),\n", "\n", "    final_calculations AS (\n", "        SELECT *, SUM(item_share_on_l1) OVER (PARTITION BY city, l1 ORDER BY quantity DESC rows unbounded preceding) AS cumulative_sum\n", "        FROM item_share_on_l1\n", "    )\n", "    SELECT * FROM final_calculations\n", "    WHERE cumulative_sum <= 0.50\n", "    \"\"\"\n", "cart_breakers_df = read_sql_query(cart_breakers_query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "d0797d80-a6d9-411c-83e3-9c301eb2e7e0", "metadata": {}, "source": ["# Unique Cities"]}, {"cell_type": "code", "execution_count": null, "id": "3c3a0b74-e5b5-447d-bd6c-7c1de9bcdea0", "metadata": {}, "outputs": [], "source": ["unique_city_df = (\n", "    cart_breakers_df[[\"city\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "# Remove\n", "# unique_city_df = unique_city_df[unique_city_df.city == 'Gurgaon'].reset_index().drop(columns = {'index'})\n", "\n", "unique_city_df"]}, {"cell_type": "markdown", "id": "9cc38d2a-4da1-431e-bcd7-bf22bd1ce9ac", "metadata": {"tags": []}, "source": ["# Base Fetch - Sales, Hourly Snaps"]}, {"cell_type": "code", "execution_count": null, "id": "85668c0e-eb20-4bfa-af18-e0b3ac45ba99", "metadata": {}, "outputs": [], "source": ["# Get Active DS for the City\n", "active_ds_facility_query = f\"\"\"SELECT distinct facility_id from lake_retail.console_outlet where active = 1 and business_type_id = 7 and facility_id is not null and facility_id != 806\"\"\"\n", "active_ds_facility_master_df = read_sql_query(active_ds_facility_query, CON_REDSHIFT)\n", "print(\"Active Facility Fetch Complete\")\n", "\n", "facility_list = list(active_ds_facility_master_df[\"facility_id\"].unique())\n", "if active_ds_facility_master_df.shape[0] < 2:\n", "    facility_list.append(9999999)\n", "facility_list = tuple(facility_list)\n", "\n", "# Get order_id and item_id for last 14 days\n", "sales_query = f\"\"\"\n", "    WITH sales AS (\n", "        SELECT date_, hour_, order_id, item_id, facility_id\n", "        FROM (\n", "            SELECT cast(oi.install_ts + interval '330' minute as date) AS date_,\n", "            EXTRACT(hour from oi.install_ts + interval '330' minute) AS hour_,\n", "\n", "            od.facility_id, pl.item_id, oi.order_id\n", "            FROM oms_bifrost.oms_order_item oi   \n", "            LEFT JOIN (\n", "                select \n", "                    distinct case when rpc_ipm.item_id is null then ipom.item_id else rpc_ipm.item_id end as item_id, rpc_ipm.product_id\n", "                from \n", "                    rpc.item_product_mapping rpc_ipm\n", "                left join \n", "                    dwh.dim_item_product_offer_mapping ipom on rpc_ipm.product_id = ipom.product_id\n", "                where active = 1\n", "            ) pl ON pl.product_id = oi.product_id\n", "            LEFT JOIN \n", "            (\n", "                select * FROM oms_bifrost.oms_order \n", "                where insert_ds_ist between cast(cast(cast(current_date as timestamp) - interval '7' day as date) as varchar) AND cast(cast(cast(current_date as timestamp) + interval '1' day as date) as varchar)\n", "            ) oo on oo.id = oi.order_id \n", "            AND oo.type IN ('RetailForwardOrder','RetailSuborder','')\n", "            JOIN \n", "                (\n", "                    SELECT DISTINCT co.facility_id, ancestor FROM ims.ims_order_details \n", "                    JOIN retail.console_outlet co ON co.id = outlet AND facility_id IN {facility_list}\n", "                    WHERE status_id NOT IN (3,4,5)\n", "                    and insert_ds_ist between cast(cast(cast(current_date as timestamp) - interval '7' day as date) as varchar) AND cast(cast(cast(current_date as timestamp) + interval '1' day as date) as varchar)\n", "                ) od ON cast(od.ancestor as int) = oi.order_id\n", "            WHERE (oi.insert_ds_ist) between cast(cast(cast(current_date as timestamp) - interval '7' day as date) as varchar) AND cast(cast(cast(current_date as timestamp) + interval '1' day as date) as varchar)\n", "            AND pl.item_id IS NOT NULL\n", "        )\n", "    )\n", "    SELECT * FROM sales where item_id is not null\n", "    \"\"\"\n", "sales_master_df = read_sql_query(sales_query, CON_TRINO)\n", "sales_master_df[\"item_id\"] = sales_master_df[\"item_id\"].astype(int)\n", "print(\"Sales Fetch Complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "8a17a9e0-d480-443c-89e7-d49308eb71b3", "metadata": {}, "outputs": [], "source": ["fetch_index = 2"]}, {"cell_type": "code", "execution_count": null, "id": "35efc76c-b317-41f8-8f70-2bdda8988da4", "metadata": {}, "outputs": [], "source": ["while fetch_index > 1:\n", "    print(fetch_index)\n", "    # Get Hourly Snaps for Unavailable Items (Inventory < 1)\n", "\n", "    packaged_hourly_snap_query = f\"\"\"\n", "        with remove_item_list AS (\n", "            SELECT item_id FROM lake_rpc.item_category_details\n", "            WHERE l0_id IN (1487) AND l2_id IN (116,1961,63,1127)\n", "            GROUP BY 1\n", "\n", "            UNION\n", "\n", "            SELECT distinct item_id\n", "            FROM lake_rpc.product_product\n", "            WHERE id IN (\n", "                SELECT MAX(id) AS id FROM lake_rpc.product_product\n", "                WHERE active = 1 AND approved = 1 GROUP BY item_id\n", "            ) AND outlet_type IN (1) AND perishable IN (1)\n", "            GROUP BY 1\n", "        ),\n", "\n", "\n", "        grocery_base AS (\n", "            WITH base_0 AS (\n", "                SELECT * FROM rpc_daily_availability\n", "                WHERE order_date >= current_date - interval '330 minutes'\n", "\n", "            ),\n", "\n", "            base as \n", "            (\n", "            select * from base_0 where order_date in (select max(order_date) from base_0)\n", "            ),\n", "\n", "            rpc_base AS (\n", "                SELECT item_id, facility_id, DATE(order_date) AS date_, actual_quantity, blocked_quantity, \n", "                EXTRACT(hour FROM order_date) AS hour_ \n", "                FROM base\n", "                WHERE facility_id IN {facility_list}\n", "                and actual_quantity - blocked_quantity < 1\n", "                GROUP BY 1,2,3,4,5,6\n", "            ),\n", "            availability AS (\n", "                SELECT date_, hour_, facility_id, item_id, actual_quantity - blocked_quantity AS inventory_at_hour\n", "                FROM rpc_base \n", "            )\n", "            SELECT *, 'Packaged Goods' AS assortment_type FROM availability\n", "        )\n", "        SELECT * FROM grocery_base WHERE item_id NOT IN (SELECT item_id FROM remove_item_list GROUP BY 1)\n", "        \"\"\"\n", "\n", "    packaged_hourly_snap_df = read_sql_query(packaged_hourly_snap_query, CON_REDSHIFT)\n", "    print(\"Packaged Hourly Snap Fetch Complete\")\n", "\n", "    fnv_per_hourly_snap_query = f\"\"\"\n", "        WITH fnv_base AS (\n", "            SELECT *, 'FnV' AS assortment_type \n", "            FROM (\n", "                SELECT date_, hour_, facility_id, item_id, current_inv as inventory_at_hour\n", "                FROM ( \n", "                    SELECT base.facility_id, base.item_id, DATE(base.updated_at) AS date_, EXTRACT(hour FROM base.updated_at) AS hour_, base.current_inv\n", "                    FROM metrics.fnv_hourly_details base\n", "                    INNER JOIN (\n", "                        SELECT outlet_id, item_id, date(updated_at) AS date_, extract(hour FROM updated_at) AS hour, MAX(updated_at) AS updated_at\n", "                        FROM metrics.fnv_hourly_details\n", "                        WHERE updated_at in (select max(updated_at) as updated_at from metrics.fnv_hourly_details)\n", "                        GROUP BY 1, 2, 3, 4\n", "                    ) latest ON base.outlet_id = latest.outlet_id\n", "                   AND base.item_id = latest.item_id\n", "                   AND base.updated_at = latest.updated_at\n", "                   WHERE base.updated_at in (select max(updated_at) as updated_at from metrics.fnv_hourly_details)\n", "                   AND base.facility_id IN {facility_list}\n", "                   GROUP BY 1, 2, 3, 4, 5\n", "                )\n", "                WHERE current_inv < 1\n", "            )\n", "\n", "        ),\n", "\n", "        persh_base AS (\n", "            SELECT *, 'Perishable' AS assortment_type \n", "            FROM (\n", "                SELECT date_, hour_, facility_id, item_id, current_inv AS inventory_at_hour\n", "                FROM ( \n", "                    SELECT base.facility_id, base.item_id, DATE(base.updated_at) AS date_, extract(HOUR FROM base.updated_at) AS hour_, base.current_inv\n", "                    FROM metrics.perishable_hourly_details_v2 base\n", "                    INNER JOIN (\n", "                        SELECT outlet_id, item_id, DATE(updated_at) AS date_, EXTRACT(hour FROM updated_at) AS hour, MAX(updated_at) AS updated_at\n", "                        FROM metrics.perishable_hourly_details_v2\n", "                        WHERE updated_at in (select max(updated_at) as updated_at from metrics.perishable_hourly_details_v2)\n", "                        GROUP BY 1, 2, 3, 4 \n", "                    ) latest ON base.outlet_id = latest.outlet_id\n", "                    AND base.item_id = latest.item_id\n", "                    AND base.updated_at = latest.updated_at\n", "                    WHERE base.updated_at in (select max(updated_at) as updated_at from metrics.perishable_hourly_details_v2)\n", "                    AND base.facility_id IN {facility_list}\n", "                    GROUP BY 1, 2, 3, 4, 5\n", "                )\n", "            WHERE current_inv < 1\n", "            )\n", "\n", "        )\n", "        SELECT * FROM fnv_base\n", "        UNION\n", "        SELECT * FROM persh_base\"\"\"\n", "\n", "    fnv_per_hourly_snap_df = read_sql_query(fnv_per_hourly_snap_query, CON_REDSHIFT)\n", "    print(\"FnV Perishable Hourly Snap Fetch Complete\")\n", "\n", "    packaged_hourly_snap_df = packaged_hourly_snap_df[\n", "        ~packaged_hourly_snap_df.item_id.isin(\n", "            tuple(list(fnv_per_hourly_snap_df[\"item_id\"].unique()))\n", "        )\n", "    ]\n", "\n", "    hourly_snap_base_df = pd.concat([packaged_hourly_snap_df, fnv_per_hourly_snap_df])\n", "    del fnv_per_hourly_snap_df, packaged_hourly_snap_df\n", "    gc.collect()\n", "\n", "    if hourly_snap_base_df[[\"date_\", \"hour_\"]].drop_duplicates().shape[0] > 1:\n", "        print(\n", "            \"df shape \",\n", "            hourly_snap_base_df[[\"date_\", \"hour_\"]].drop_duplicates().shape[0],\n", "        )\n", "        time.sleep(20)\n", "\n", "    fetch_index = hourly_snap_base_df[[\"date_\", \"hour_\"]].drop_duplicates().shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "4dbb98f3-865a-4d4b-8be6-f68c164d8be1", "metadata": {}, "outputs": [], "source": ["hourly_df = pd.DataFrame()\n", "daily_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "993ed82f-d2af-43f3-a33b-557542da1b4c", "metadata": {}, "outputs": [], "source": ["hourly_snap_base_df[\"date_\"] = pd.to_datetime(hourly_snap_base_df[\"date_\"])\n", "sales_master_df[\"date_\"] = pd.to_datetime(sales_master_df[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "170a3df2-0220-4e27-b44f-57c3c5118ee3", "metadata": {}, "outputs": [], "source": ["hourly_snap_base_df[[\"date_\", \"hour_\"]].drop_duplicates()"]}, {"cell_type": "markdown", "id": "8e7c7aad-5542-4714-8dd9-ce437532d8df", "metadata": {"tags": []}, "source": ["# Calculation for Lost Carts"]}, {"cell_type": "code", "execution_count": null, "id": "ba387044-c7f5-4c79-b1b6-0c59ad7fc6db", "metadata": {}, "outputs": [], "source": ["for index in range(unique_city_df.shape[0]):\n", "\n", "    # Get City\n", "    city = unique_city_df[unique_city_df.index == index].iloc[:, 0][index]\n", "    print(\"\\nCity \", city)\n", "\n", "    # Get Active DS for the City\n", "    active_ds_facility_query = f\"\"\"SELECT distinct facility_id from lake_retail.console_outlet where active = 1 and business_type_id = 7 and tax_location_id in (select distinct id from lake_retail.console_location where name = '{city}')\"\"\"\n", "    active_ds_facility_master_df = read_sql_query(\n", "        active_ds_facility_query, CON_REDSHIFT\n", "    )\n", "    print(\"Active Facility Fetch Complete\")\n", "\n", "    facility_list = list(active_ds_facility_master_df[\"facility_id\"].unique())\n", "    if active_ds_facility_master_df.shape[0] < 2:\n", "        facility_list.append(9999999)\n", "    facility_list = tuple(facility_list)\n", "\n", "    active_ds_facility_df = active_ds_facility_master_df.copy()\n", "    if active_ds_facility_df.shape[0] > 0:\n", "\n", "        facility_list = list(active_ds_facility_df[\"facility_id\"].unique())\n", "        if active_ds_facility_df.shape[0] < 2:\n", "            facility_list.append(9999999)\n", "        facility_list = tuple(facility_list)\n", "\n", "        del active_ds_facility_df\n", "        gc.collect()\n", "\n", "        sales_df = sales_master_df[sales_master_df.facility_id.isin(facility_list)]\n", "        hourly_snap_df = hourly_snap_base_df[\n", "            hourly_snap_base_df.facility_id.isin(facility_list)\n", "        ]\n", "\n", "        cart_breaker_list = tuple(\n", "            list(cart_breakers_df[cart_breakers_df.city == city][\"item_id\"].unique())\n", "        )\n", "\n", "        hourly_snap_df[\"city\"] = city\n", "\n", "        # Identify Cart breakers that are unavailable\n", "        hourly_cartbreaker_merged_df = pd.merge(\n", "            hourly_snap_df,\n", "            cart_breakers_df[cart_breakers_df.city == city][\n", "                [\"item_id\", \"cumulative_sum\"]\n", "            ],\n", "            on=[\"item_id\"],\n", "            how=\"left\",\n", "        )\n", "\n", "        hourly_cartbreaker_merged_df[\"cart_breaker_flag\"] = np.where(\n", "            hourly_cartbreaker_merged_df[\"cumulative_sum\"].isna() == False, 1, 0\n", "        )\n", "        hourly_cartbreaker_merged_df = hourly_cartbreaker_merged_df.drop(\n", "            columns={\"cumulative_sum\"}\n", "        )\n", "        hourly_cartbreaker_merged_df[\"availability_flag\"] = np.where(\n", "            hourly_cartbreaker_merged_df[\"inventory_at_hour\"] > 0, 1, 0\n", "        )\n", "        hourly_cartbreaker_merged_df = hourly_cartbreaker_merged_df[\n", "            [\n", "                \"date_\",\n", "                \"hour_\",\n", "                \"facility_id\",\n", "                \"item_id\",\n", "                \"assortment_type\",\n", "                \"cart_breaker_flag\",\n", "                \"inventory_at_hour\",\n", "                \"availability_flag\",\n", "            ]\n", "        ]\n", "\n", "        # Get Unavailable Cart Breaker SKUs\n", "        unavailable_cart_breaker_df = hourly_cartbreaker_merged_df[\n", "            (hourly_cartbreaker_merged_df.cart_breaker_flag == 1)\n", "            & (hourly_cartbreaker_merged_df.availability_flag == 0)\n", "        ]\n", "        del hourly_cartbreaker_merged_df, hourly_snap_df\n", "        gc.collect()\n", "\n", "        # Create Base Schema - Map Last 7 dates for Sales to Date and hours\n", "        schema_base = unavailable_cart_breaker_df[[\"date_\", \"hour_\"]].drop_duplicates()\n", "        schema_base[\"key\"] = 1\n", "        schema_sales_base = (\n", "            sales_df[[\"date_\"]]\n", "            .drop_duplicates()\n", "            .rename(columns={\"date_\": \"sales_dates\"})\n", "        )\n", "        schema_sales_base[\"key\"] = 1\n", "        l7_df = pd.merge(schema_base, schema_sales_base, on=[\"key\"], how=\"left\")\n", "        l7_df = l7_df[\n", "            (\n", "                (pd.to_datetime(l7_df.sales_dates) < pd.to_datetime(l7_df.date_))\n", "                & (\n", "                    pd.to_datetime(l7_df.sales_dates)\n", "                    >= pd.to_datetime(l7_df.date_) - <PERSON><PERSON><PERSON>(days=7)\n", "                )\n", "            )\n", "        ]\n", "        l7_df = l7_df.drop(columns={\"key\"})\n", "        l7_df = pd.merge(\n", "            l7_df,\n", "            unavailable_cart_breaker_df[\n", "                [\"date_\", \"hour_\", \"facility_id\"]\n", "            ].drop_duplicates(),\n", "            on=[\"date_\", \"hour_\"],\n", "            how=\"left\",\n", "        )\n", "        del schema_sales_base, schema_base\n", "        gc.collect()\n", "\n", "        print(l7_df.shape[0], \"Before Adding Sales Data at Order ID level\")\n", "\n", "        # Add sales data\n", "        l7_df = pd.merge(\n", "            l7_df,\n", "            sales_df.rename(columns={\"date_\": \"sales_dates\"}).drop(columns={\"hour_\"}),\n", "            on=[\"sales_dates\", \"facility_id\"],\n", "            how=\"left\",\n", "        )\n", "\n", "        print(l7_df.shape[0], \"After Adding Sales Data at Order ID level\")\n", "\n", "        # Merge to flag cart breakers in the sales data\n", "        unavailable_cart_breaker_df[\n", "            [\"hour_\", \"facility_id\", \"item_id\", \"cart_breaker_flag\"]\n", "        ] = unavailable_cart_breaker_df[\n", "            [\"hour_\", \"facility_id\", \"item_id\", \"cart_breaker_flag\"]\n", "        ].astype(\n", "            int\n", "        )\n", "\n", "        l7_df = l7_df[l7_df.order_id.isna() == False]\n", "        l7_df = l7_df[l7_df.item_id.isna() == False]\n", "        l7_df[[\"hour_\", \"facility_id\", \"item_id\"]] = l7_df[\n", "            [\"hour_\", \"facility_id\", \"item_id\"]\n", "        ].astype(int)\n", "        l7_df = pd.merge(\n", "            l7_df,\n", "            unavailable_cart_breaker_df[\n", "                [\"date_\", \"hour_\", \"facility_id\", \"item_id\", \"cart_breaker_flag\"]\n", "            ].drop_duplicates(),\n", "            on=[\"date_\", \"hour_\", \"item_id\", \"facility_id\"],\n", "            how=\"left\",\n", "        )\n", "\n", "        # Change\n", "        l7_df[\"all_cart_breaker_flag\"] = np.where(\n", "            l7_df[\"item_id\"].isin(cart_breaker_list), 1, 0\n", "        )\n", "\n", "        del unavailable_cart_breaker_df\n", "        gc.collect()\n", "\n", "        l7_df[\"cart_breaker_flag\"] = l7_df[\"cart_breaker_flag\"].fillna(0)\n", "        # Change\n", "        l7_df[\"all_cart_breaker_flag\"] = l7_df[\"all_cart_breaker_flag\"].fillna(0)\n", "\n", "        print(l7_df.shape[0], \"After adding unavailable cart breaker Data\")\n", "\n", "        # identify pure carts\n", "\n", "        # Change\n", "        aggregated_carts_df = (\n", "            l7_df.groupby([\"date_\", \"hour_\", \"facility_id\", \"order_id\"])\n", "            .agg(\n", "                {\n", "                    \"item_id\": \"count\",\n", "                    \"cart_breaker_flag\": \"sum\",\n", "                    \"all_cart_breaker_flag\": \"sum\",\n", "                }\n", "            )\n", "            .reset_index()\n", "            .rename(\n", "                columns={\n", "                    \"item_id\": \"count_of_skus\",\n", "                    \"cart_breaker_flag\": \"cart_breaker_skus\",\n", "                    \"all_cart_breaker_flag\": \"all_cart_breaker_skus\",\n", "                }\n", "            )\n", "        )\n", "        del l7_df\n", "        gc.collect()\n", "\n", "        aggregated_carts_df = aggregated_carts_df.drop_duplicates()\n", "\n", "        total_carts_agg_df = (\n", "            aggregated_carts_df.groupby([\"date_\", \"hour_\", \"facility_id\"])\n", "            .agg({\"order_id\": \"nunique\"})\n", "            .reset_index()\n", "            .rename(columns={\"order_id\": \"total_carts\"})\n", "        )\n", "\n", "        pure_carts_df = aggregated_carts_df[\n", "            aggregated_carts_df.count_of_skus == aggregated_carts_df.cart_breaker_skus\n", "        ]\n", "        pure_carts_agg_df = (\n", "            pure_carts_df.groupby([\"date_\", \"hour_\", \"facility_id\"])\n", "            .agg({\"order_id\": \"nunique\"})\n", "            .reset_index()\n", "            .rename(columns={\"order_id\": \"pure_carts\"})\n", "        )\n", "\n", "        del pure_carts_df\n", "        gc.collect()\n", "\n", "        total_carts_agg_df = pd.merge(\n", "            total_carts_agg_df,\n", "            pure_carts_agg_df,\n", "            on=[\"date_\", \"hour_\", \"facility_id\"],\n", "            how=\"left\",\n", "        )\n", "        del pure_carts_agg_df\n", "        gc.collect()\n", "\n", "        total_carts_agg_df[\"pure_carts\"] = total_carts_agg_df[\"pure_carts\"].fillna(0)\n", "\n", "        total_carts_agg_df[\"pure_carts_share\"] = (\n", "            total_carts_agg_df[\"pure_carts\"] / total_carts_agg_df[\"total_carts\"]\n", "        )\n", "\n", "        # identify mix carts\n", "\n", "        mix_carts_df = aggregated_carts_df[\n", "            (aggregated_carts_df.count_of_skus != aggregated_carts_df.cart_breaker_skus)\n", "            & (aggregated_carts_df.cart_breaker_skus > 0)\n", "        ]\n", "\n", "        del aggregated_carts_df\n", "        gc.collect()\n", "        # Change\n", "        mix_carts_agg_df = (\n", "            mix_carts_df.groupby([\"date_\", \"hour_\", \"facility_id\"])\n", "            .agg(\n", "                {\n", "                    \"order_id\": \"nunique\",\n", "                    \"count_of_skus\": \"mean\",\n", "                    \"cart_breaker_skus\": \"mean\",\n", "                    \"all_cart_breaker_skus\": \"mean\",\n", "                }\n", "            )\n", "            .reset_index()\n", "        ).rename(\n", "            columns={\n", "                \"count_of_skus\": \"total_ipc\",\n", "                \"cart_breaker_skus\": \"cb_ipc\",\n", "                \"order_id\": \"total_mixed_carts\",\n", "                \"all_cart_breaker_skus\": \"all_cb_ipc\",\n", "            }\n", "        )\n", "\n", "        mix_carts_agg_df = pd.merge(\n", "            mix_carts_agg_df,\n", "            total_carts_agg_df[\n", "                [\"date_\", \"hour_\", \"facility_id\", \"total_carts\"]\n", "            ].drop_duplicates(),\n", "            on=[\"date_\", \"hour_\", \"facility_id\"],\n", "            how=\"left\",\n", "        )\n", "\n", "        mix_carts_agg_df[\"cb_ipc_share\"] = (\n", "            mix_carts_agg_df[\"cb_ipc\"] / mix_carts_agg_df[\"total_ipc\"]\n", "        ) * (mix_carts_agg_df[\"total_mixed_carts\"] / mix_carts_agg_df[\"total_carts\"])\n", "        # Change\n", "        mix_carts_agg_df[\"realistic_cb_ipc_share\"] = (\n", "            mix_carts_agg_df[\"cb_ipc\"] / mix_carts_agg_df[\"all_cb_ipc\"]\n", "        ) * (mix_carts_agg_df[\"total_mixed_carts\"] / mix_carts_agg_df[\"total_carts\"])\n", "\n", "        total_carts_agg_df = pd.merge(\n", "            total_carts_agg_df,\n", "            mix_carts_agg_df[[\"date_\", \"hour_\", \"facility_id\", \"total_mixed_carts\"]],\n", "            on=[\"date_\", \"hour_\", \"facility_id\"],\n", "            how=\"left\",\n", "        )\n", "\n", "        total_carts_agg_df[\"mix_carts_share\"] = (\n", "            total_carts_agg_df[\"total_mixed_carts\"] / total_carts_agg_df[\"total_carts\"]\n", "        )\n", "\n", "        # Get Actual Carts for the hours\n", "\n", "        schema_base = (\n", "            sales_df.groupby([\"date_\", \"hour_\", \"facility_id\"])\n", "            .agg({\"order_id\": \"nunique\"})\n", "            .reset_index()\n", "            .rename(columns={\"order_id\": \"actual_carts\"})\n", "        )\n", "\n", "        del sales_df\n", "        gc.collect()\n", "\n", "        # Add Pure and Mix Cart Numbers -\n", "\n", "        schema_base = pd.merge(\n", "            schema_base,\n", "            total_carts_agg_df[\n", "                [\"date_\", \"hour_\", \"facility_id\", \"pure_carts_share\", \"mix_carts_share\"]\n", "            ].drop_duplicates(),\n", "            on=[\"date_\", \"hour_\", \"facility_id\"],\n", "            how=\"left\",\n", "        )\n", "\n", "        del total_carts_agg_df\n", "        gc.collect()\n", "        # Change\n", "        schema_base = pd.merge(\n", "            schema_base,\n", "            mix_carts_agg_df[\n", "                [\n", "                    \"date_\",\n", "                    \"hour_\",\n", "                    \"facility_id\",\n", "                    \"cb_ipc\",\n", "                    \"all_cb_ipc\",\n", "                    \"total_ipc\",\n", "                    \"cb_ipc_share\",\n", "                    \"realistic_cb_ipc_share\",\n", "                ]\n", "            ].drop_duplicates(),\n", "            on=[\"date_\", \"hour_\", \"facility_id\"],\n", "            how=\"left\",\n", "        )\n", "\n", "        del mix_carts_agg_df\n", "        gc.collect()\n", "\n", "        schema_base[\"pure_carts_share\"] = schema_base[\"pure_carts_share\"].fillna(0)\n", "        schema_base[\"cb_ipc_share\"] = schema_base[\"cb_ipc_share\"].fillna(0)\n", "        # Change\n", "        schema_base[\"realistic_cb_ipc_share\"] = schema_base[\n", "            \"realistic_cb_ipc_share\"\n", "        ].fillna(0)\n", "        schema_base[\"all_cb_ipc\"] = schema_base[\"all_cb_ipc\"].fillna(0)\n", "\n", "        schema_base[\"cb_ipc\"] = schema_base[\"cb_ipc\"].fillna(0)\n", "        schema_base[\"total_ipc\"] = schema_base[\"total_ipc\"].fillna(0)\n", "        schema_base[\"mix_carts_share\"] = schema_base[\"mix_carts_share\"].fillna(0)\n", "\n", "        schema_base[\"pure_carts_lost\"] = (\n", "            schema_base[\"actual_carts\"] * schema_base[\"pure_carts_share\"]\n", "        )\n", "        schema_base[\"mix_carts_lost\"] = (\n", "            schema_base[\"actual_carts\"] * schema_base[\"cb_ipc_share\"]\n", "        )\n", "        # Change\n", "        schema_base[\"realistic_mix_carts_lost\"] = (\n", "            schema_base[\"actual_carts\"] * schema_base[\"realistic_cb_ipc_share\"]\n", "        )\n", "\n", "        schema_base[\"total_carts_lost\"] = (\n", "            schema_base[\"mix_carts_lost\"] + schema_base[\"pure_carts_lost\"]\n", "        )\n", "        # Change\n", "        schema_base[\"realistic_total_carts_lost\"] = (\n", "            schema_base[\"realistic_mix_carts_lost\"] + schema_base[\"pure_carts_lost\"]\n", "        )\n", "\n", "        # schema_base['total_carts_lost'] = round(schema_base['total_carts_lost'])\n", "\n", "        schema_base[\"lost_cart_share\"] = (\n", "            schema_base[\"total_carts_lost\"] / schema_base[\"actual_carts\"]\n", "        )\n", "        # Change\n", "        schema_base[\"realistic_lost_cart_share\"] = (\n", "            schema_base[\"realistic_total_carts_lost\"] / schema_base[\"actual_carts\"]\n", "        )\n", "\n", "        schema_base[\"city\"] = city\n", "\n", "        # Aggregate carts lost\n", "        # Change\n", "        schema_base_agg_df = (\n", "            schema_base.groupby([\"date_\", \"city\", \"facility_id\"])\n", "            .agg(\n", "                {\n", "                    \"actual_carts\": \"sum\",\n", "                    \"total_carts_lost\": \"sum\",\n", "                    \"realistic_total_carts_lost\": \"sum\",\n", "                }\n", "            )\n", "            .reset_index()\n", "        )\n", "\n", "        schema_base_agg_df[\"total_carts_lost\"] = round(\n", "            schema_base_agg_df[\"total_carts_lost\"]\n", "        )\n", "        # Change\n", "        schema_base_agg_df[\"realistic_total_carts_lost\"] = round(\n", "            schema_base_agg_df[\"realistic_total_carts_lost\"]\n", "        )\n", "\n", "        schema_base_agg_df[\"lost_cart_share\"] = (\n", "            schema_base_agg_df[\"total_carts_lost\"] / schema_base_agg_df[\"actual_carts\"]\n", "        )\n", "        # Change\n", "        schema_base_agg_df[\"realistic_lost_cart_share\"] = (\n", "            schema_base_agg_df[\"realistic_total_carts_lost\"]\n", "            / schema_base_agg_df[\"actual_carts\"]\n", "        )\n", "\n", "        schema_base[\"total_carts_lost\"] = round(schema_base[\"total_carts_lost\"])\n", "        # Change\n", "\n", "        schema_base[\"realistic_total_carts_lost\"] = round(\n", "            schema_base[\"realistic_total_carts_lost\"]\n", "        )\n", "\n", "        daily_df = pd.concat([daily_df, schema_base_agg_df])\n", "        hourly_df = pd.concat([hourly_df, schema_base])\n", "\n", "        del schema_base_agg_df, schema_base\n", "        gc.collect()"]}, {"cell_type": "markdown", "id": "568704cc-5e5d-476d-90a3-b88795b36938", "metadata": {"tags": []}, "source": ["# Filter for Current Snap"]}, {"cell_type": "code", "execution_count": null, "id": "3fdf318d-47e2-4ea0-8b29-02ac011f72f4", "metadata": {}, "outputs": [], "source": ["hourly_df = pd.merge(\n", "    hourly_df,\n", "    hourly_snap_base_df[[\"date_\", \"hour_\"]].drop_duplicates(),\n", "    on=[\"date_\", \"hour_\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "markdown", "id": "cb291930-01fd-4456-b175-01bab5dfc212", "metadata": {"tags": []}, "source": ["# Create Copy for Redshift Push"]}, {"cell_type": "code", "execution_count": null, "id": "170eb173-1726-4567-bb7e-d291d86399e0", "metadata": {}, "outputs": [], "source": ["hourly_redshift_push_df = hourly_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "36008436-906b-4fbe-ae62-74722f9f2cba", "metadata": {}, "outputs": [], "source": ["hourly_redshift_push_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3ac27817-395d-46b6-a04a-50ec408fd451", "metadata": {}, "outputs": [], "source": ["hourly_redshift_push_df[hourly_redshift_push_df.facility_id == 219]"]}, {"cell_type": "code", "execution_count": null, "id": "6a129be3-94ec-4b19-88d1-a32790aa3291", "metadata": {}, "outputs": [], "source": ["hourly_redshift_push_df[hourly_redshift_push_df.city == \"Gurgaon\"].groupby(\n", "    [\"date_\", \"city\"]\n", ").agg(\n", "    {\n", "        \"actual_carts\": \"sum\",\n", "        \"total_carts_lost\": \"sum\",\n", "        \"realistic_total_carts_lost\": \"sum\",\n", "    }\n", ").reset_index()"]}, {"cell_type": "markdown", "id": "d6131d54-8d14-49d3-a07a-24527048772b", "metadata": {"tags": []}, "source": ["# Get Past Data"]}, {"cell_type": "code", "execution_count": null, "id": "d3b4be9c-2935-4d7d-b4f7-82bd0b945a58", "metadata": {}, "outputs": [], "source": ["past_snap_query = f\"\"\"\n", "with base as \n", "(\n", "    select *\n", "    from metrics.carts_lost_for_cart_breakers_overall\n", "    where date_ >= current_date - 8\n", ")\n", "\n", "select * from base\n", "\"\"\"\n", "\n", "past_snap_df = read_sql_query(past_snap_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "118a57a3-e0b8-4e71-a206-af8e6b3d5495", "metadata": {}, "outputs": [], "source": ["past_snap_df = past_snap_df.drop(columns={\"date_time\"})\n", "past_snap_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b6ecea97-29a0-4a7e-ad8a-9e3649e1e6d2", "metadata": {}, "outputs": [], "source": ["hourly_df = pd.concat([hourly_df, past_snap_df])"]}, {"cell_type": "markdown", "id": "a5d97a08-0197-41f6-9d0b-bb3a27eec71e", "metadata": {"tags": []}, "source": ["# Filter for last 7 days and after 6th Hour"]}, {"cell_type": "code", "execution_count": null, "id": "5da7cf1a-5143-469b-94e0-bbed8813f51d", "metadata": {}, "outputs": [], "source": ["hourly_city_agg_df = (\n", "    hourly_df.groupby([\"city\", \"date_\", \"hour_\"])\n", "    .agg(\n", "        {\n", "            \"actual_carts\": \"sum\",\n", "            \"total_carts_lost\": \"sum\",\n", "            \"realistic_total_carts_lost\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "hourly_city_agg_df[\"cart_lost_share\"] = (\n", "    hourly_city_agg_df[\"total_carts_lost\"] / hourly_city_agg_df[\"actual_carts\"]\n", ")\n", "\n", "hourly_city_agg_df[\"realistic_cart_lost_share\"] = (\n", "    hourly_city_agg_df[\"realistic_total_carts_lost\"]\n", "    / hourly_city_agg_df[\"actual_carts\"]\n", ")\n", "\n", "\n", "hourly_city_agg_df = hourly_city_agg_df[\n", "    pd.to_datetime(hourly_city_agg_df.date_)\n", "    >= pd.to_datetime(\n", "        (datetime.now() + timedelta(hours=5.5)).date() - timedelta(days=7)\n", "    )\n", "]\n", "hourly_city_agg_df = hourly_city_agg_df[hourly_city_agg_df.hour_ >= 6]\n", "hourly_city_agg_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "57c98416-aa67-475e-86ab-c3883140b9d2", "metadata": {}, "outputs": [], "source": ["hourly_city_agg_df[hourly_city_agg_df.city == \"Delhi\"].sort_values(\n", "    by=[\"date_\", \"hour_\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3007318b-094f-442c-b22d-875e0533ad54", "metadata": {}, "outputs": [], "source": ["hourly_city_agg_df"]}, {"cell_type": "markdown", "id": "1ec79c19-862b-431e-a2f0-38765cf77399", "metadata": {"tags": []}, "source": ["# Create Summary Views"]}, {"cell_type": "code", "execution_count": null, "id": "c4a1fa12-96da-4e25-ba07-294afcc93dcc", "metadata": {}, "outputs": [], "source": ["hourly_actual_carts = hourly_city_agg_df.pivot_table(\n", "    index=[\"city\", \"date_\"],\n", "    columns=[\"hour_\"],\n", "    values=[\"actual_carts\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "hourly_lost_carts = hourly_city_agg_df.pivot_table(\n", "    index=[\"city\", \"date_\"],\n", "    columns=[\"hour_\"],\n", "    values=[\"total_carts_lost\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "hourly_lost_cart_share = hourly_city_agg_df.pivot_table(\n", "    index=[\"city\", \"date_\"],\n", "    columns=[\"hour_\"],\n", "    values=[\"cart_lost_share\"],\n", "    fill_value=0,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "34ae18d4-4c35-4572-bbe5-24e9a5723e00", "metadata": {}, "outputs": [], "source": ["hourly_lost_cart_share"]}, {"cell_type": "code", "execution_count": null, "id": "6564e022-5814-47cc-b96a-15201d043c20", "metadata": {}, "outputs": [], "source": ["if hourly_city_agg_df.shape[0] > 0:\n", "    sheet_id = \"1IANj2HAdLgOVobU95NdpqExN78OV7uv8edNnD1XvymY\"\n", "    destination = pb.from_sheets(sheet_id, \"destination\")\n", "    destination.iloc[:, 0][0]"]}, {"cell_type": "code", "execution_count": null, "id": "52498fe6-de6f-4fe0-a074-8e5035f43724", "metadata": {}, "outputs": [], "source": ["if hourly_city_agg_df.shape[0] > 0:\n", "    sheet_id = destination.iloc[:, 0][0]\n", "    output_sheet = \"hourly_lost_carts_raw\"\n", "    pb.to_sheets(hourly_lost_carts, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "a989eb54-47da-4bb0-b486-628ca6852746", "metadata": {}, "outputs": [], "source": ["if hourly_city_agg_df.shape[0] > 0:\n", "    sheet_id = destination.iloc[:, 0][0]\n", "    output_sheet = \"hourly_actual_carts_raw\"\n", "    pb.to_sheets(hourly_actual_carts, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "cf8e2920-93eb-43ed-9773-0bcf52728e1c", "metadata": {}, "outputs": [], "source": ["if hourly_city_agg_df.shape[0] > 0:\n", "    sheet_id = destination.iloc[:, 0][0]\n", "    output_sheet = \"city_hourly_lost_share_raw\"\n", "    pb.to_sheets(hourly_lost_cart_share, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "fd5f1df8-6c72-47e0-819a-c0a55189958b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "58092469-34c3-43cc-a3d6-1d6e92823af3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b119348d-7193-4c51-8db5-47eeed784b11", "metadata": {}, "outputs": [], "source": ["hourly_lost_carts = hourly_city_agg_df.pivot_table(\n", "    index=[\"city\", \"date_\"],\n", "    columns=[\"hour_\"],\n", "    values=[\"realistic_total_carts_lost\"],\n", "    fill_value=0,\n", ").reset_index()\n", "\n", "hourly_lost_cart_share = hourly_city_agg_df.pivot_table(\n", "    index=[\"city\", \"date_\"],\n", "    columns=[\"hour_\"],\n", "    values=[\"realistic_cart_lost_share\"],\n", "    fill_value=0,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "000614b9-0874-44fb-821f-e0d7da02dcc7", "metadata": {}, "outputs": [], "source": ["hourly_lost_cart_share"]}, {"cell_type": "code", "execution_count": null, "id": "bf3c5ece-56c8-49eb-adca-56b7209cc238", "metadata": {}, "outputs": [], "source": ["if hourly_city_agg_df.shape[0] > 0:\n", "    sheet_id = destination.iloc[:, 0][0]\n", "    output_sheet = \"realistic_hourly_lost_carts_raw\"\n", "    pb.to_sheets(hourly_lost_carts, sheet_id, output_sheet)"]}, {"cell_type": "code", "execution_count": null, "id": "601d2874-3064-45d6-80c9-8077ba138b88", "metadata": {}, "outputs": [], "source": ["if hourly_city_agg_df.shape[0] > 0:\n", "    sheet_id = destination.iloc[:, 0][0]\n", "    output_sheet = \"realistic_city_hourly_lost_share_raw\"\n", "    pb.to_sheets(hourly_lost_cart_share, sheet_id, output_sheet)"]}, {"cell_type": "markdown", "id": "48f7f18d-f77d-4930-a46e-b966df1ff057", "metadata": {}, "source": ["# Redshift Update"]}, {"cell_type": "code", "execution_count": null, "id": "c5c79bbb-32a7-4861-9818-61da9267d866", "metadata": {}, "outputs": [], "source": ["hourly_redshift_push_df[\"date_time\"] = pd.to_datetime(\n", "    hourly_redshift_push_df[\"date_\"].astype(str)\n", "    + \" \"\n", "    + hourly_redshift_push_df[\"hour_\"].astype(str)\n", "    + \":00:00\"\n", ")\n", "hourly_redshift_push_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "baccdbe4-46bd-4be8-a41e-63e269d4da77", "metadata": {}, "outputs": [], "source": ["hourly_redshift_push_df[[\"date_\", \"hour_\", \"date_time\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "5ef67d56-706c-4769-8535-ebbf08083fd9", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date for snapshot\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour of the day\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"unique identifier for facility\",\n", "    },\n", "    {\n", "        \"name\": \"actual_carts\",\n", "        \"type\": \"float\",\n", "        \"description\": \"actual carts for the hour\",\n", "    },\n", "    {\n", "        \"name\": \"pure_carts_share\",\n", "        \"type\": \"float\",\n", "        \"description\": \"share of pure carts in last 7 days for the cart breaker unavailable in the hour\",\n", "    },\n", "    {\n", "        \"name\": \"mix_carts_share\",\n", "        \"type\": \"float\",\n", "        \"description\": \"share of mixed carts in last 7 days for the cart breaker unavailable in the hour\",\n", "    },\n", "    {\n", "        \"name\": \"cb_ipc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"average ipc for unavailable cart breaker\",\n", "    },\n", "    {\n", "        \"name\": \"all_cb_ipc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"average ipc for all cart breaker\",\n", "    },\n", "    {\n", "        \"name\": \"total_ipc\",\n", "        \"type\": \"float\",\n", "        \"description\": \"average ipc for last 7 carts\",\n", "    },\n", "    {\n", "        \"name\": \"cb_ipc_share\",\n", "        \"type\": \"float\",\n", "        \"description\": \"share of cart breaker ipc wrt total ipc carts in last 7 days for the cart breaker unavailable in the hour\",\n", "    },\n", "    {\n", "        \"name\": \"realistic_cb_ipc_share\",\n", "        \"type\": \"float\",\n", "        \"description\": \"share of cart breaker ipc wrt all cart breakers ipc carts in last 7 days for the cart breaker unavailable in the hour\",\n", "    },\n", "    {\"name\": \"pure_carts_lost\", \"type\": \"float\", \"description\": \"pure carts lost\"},\n", "    {\"name\": \"mix_carts_lost\", \"type\": \"float\", \"description\": \"mixed carts lost\"},\n", "    {\n", "        \"name\": \"realistic_mix_carts_lost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"realistic mixed carts lost\",\n", "    },\n", "    {\n", "        \"name\": \"total_carts_lost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total carts lost - mix + pure, ceiled\",\n", "    },\n", "    {\n", "        \"name\": \"realistic_total_carts_lost\",\n", "        \"type\": \"float\",\n", "        \"description\": \"realistic total carts lost - realistic mix + pure, ceiled\",\n", "    },\n", "    {\n", "        \"name\": \"lost_cart_share\",\n", "        \"type\": \"float\",\n", "        \"description\": \"carts lost / actual carts\",\n", "    },\n", "    {\n", "        \"name\": \"realistic_lost_cart_share\",\n", "        \"type\": \"float\",\n", "        \"description\": \"realistic carts lost / actual carts\",\n", "    },\n", "    {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"city for facility\"},\n", "    {\"name\": \"date_time\", \"type\": \"timestamp\", \"description\": \"timestamp in table\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4f948efc-40fd-420c-827f-ab427e4dd5f1", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"carts_lost_for_cart_breakers_overall\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"date_\", \"hour_\"],\n", "    \"sortkey\": [\"date_\", \"facility_id\", \"city\"],\n", "    \"incremental_key\": \"date_time\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Table tracks hourly carts lost due to unavailable cart breakers\",\n", "}\n", "pb.to_redshift(hourly_redshift_push_df, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}