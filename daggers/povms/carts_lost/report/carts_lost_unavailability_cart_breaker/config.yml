alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: carts_lost_unavailability_cart_breaker
dag_type: report
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 12G
      request: 4G
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U057BEV1H8Q
path: povms/carts_lost/report/carts_lost_unavailability_cart_breaker
paused: false
project_name: carts_lost
schedule:
  end_date: '2023-06-06T00:00:00'
  interval: 40 0-19 * * *
  start_date: '2023-01-10T05:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 7
pool: povms_pool
