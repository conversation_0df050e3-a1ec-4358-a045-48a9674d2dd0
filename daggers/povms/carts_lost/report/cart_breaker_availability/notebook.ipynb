{"cells": [{"cell_type": "markdown", "id": "22150f4f-cae5-4d06-9094-2f69d95e9d9e", "metadata": {}, "source": ["# Inputs - Packages, Functions"]}, {"cell_type": "code", "execution_count": null, "id": "42258548-e16a-443a-acd8-521be6bf0507", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "\n", "CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "a78af634-ccf0-4f0f-8633-34e542e46ffe", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "id": "46dadc36-f1c8-4b29-b670-bf603a1cc9b1", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "markdown", "id": "17eac6e9-dfac-4f35-8ed6-4e8b78cd34c5", "metadata": {}, "source": ["# Fetch Cart Breakers"]}, {"cell_type": "code", "execution_count": null, "id": "f16a18bf-d325-463b-93f5-703d7ace4889", "metadata": {}, "outputs": [], "source": ["cart_breakers_query = f\"\"\"\n", "    WITH item_base AS (\n", "        SELECT DISTINCT p.item_id, p.name AS item_name, icd.l0, icd.l1, icd.l2\n", "        FROM lake_rpc.item_details p\n", "        LEFT JOIN lake_rpc.item_category_details icd ON p.item_id = icd.item_id\n", "        WHERE p.active = 1 AND p.approved = 1\n", "    ),\n", "\n", "    base AS (\n", "        SELECT \n", "            cl.name as city, oi.item_id, item_name, l0, l1, sum(oi.quantity) AS quantity,\n", "            SUM(CASE WHEN price IS NULL THEN 0 ELSE oi.quantity*price END) AS GMV\n", "        FROM lake_ims.ims_order_details od\n", "\n", "        LEFT JOIN lake_ims.ims_order_items oi on od.id = oi.order_details_id \n", "\n", "        LEFT JOIN lake_ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "\n", "        LEFT JOIN item_base ib on ib.item_id = oi.item_id\n", "\n", "        INNER JOIN lake_retail.console_outlet co on co.id = outlet and co.active = 1 and co.business_type_id = 7\n", "\n", "        LEFT JOIN lake_retail.console_location cl on cl.id = co.tax_location_id\n", "\n", "        WHERE status_id IN (1,2)\n", "        AND  od.created_at BETWEEN current_date - interval '45 Days' - interval '5.5 Hours' AND current_date - interval '1 Days' - interval '5.5 Hours'\n", "\n", "        GROUP BY 1,2,3,4,5\n", "        ORDER BY l1, quantity DESC\n", "    ),\n", "\n", "    total_l1_sales AS (\n", "        SELECT city, l1, SUM(quantity) AS total_l1_quantity, SUM(GMV) AS total_l1_GMV\n", "        FROM base\n", "        GROUP BY 1, 2\n", "    ),\n", "\n", "    item_share_on_l1 AS (\n", "        SELECT b.*, total_l1_quantity, total_l1_GMV, 1.0000*quantity / total_l1_quantity*1.0000 AS item_share_on_l1\n", "        FROM base b\n", "        LEFT JOIN total_l1_sales tls ON b.l1 = tls.l1 AND b.city = tls.city\n", "    ),\n", "\n", "    final_calculations AS (\n", "        SELECT *, SUM(item_share_on_l1) OVER (PARTITION BY city, l1 ORDER BY quantity DESC rows unbounded preceding) AS cumulative_sum\n", "        FROM item_share_on_l1\n", "    )\n", "    SELECT * FROM final_calculations\n", "    WHERE cumulative_sum <= 0.50\n", "    \"\"\"\n", "cart_breakers_df = read_sql_query(cart_breakers_query, CON_REDSHIFT)\n", "cartbreaker_list = tuple(\n", "    list(cart_breakers_df[cart_breakers_df.item_id.isna() == False][\"item_id\"].unique())\n", ")\n", "\n", "# Get unique cities\n", "unique_city_df = (\n", "    cart_breakers_df[[\"city\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "\n", "\n", "# Get Active DS for the City\n", "active_ds_facility_query = f\"\"\"SELECT distinct facility_id from lake_retail.console_outlet where active = 1 and business_type_id = 7 and facility_id is not null and facility_id != 806\"\"\"\n", "active_ds_facility_master_df = read_sql_query(active_ds_facility_query, CON_REDSHIFT)\n", "print(\"Active Facility Fetch Complete\")\n", "\n", "facility_list = list(active_ds_facility_master_df[\"facility_id\"].unique())\n", "if active_ds_facility_master_df.shape[0] < 2:\n", "    facility_list.append(9999999)\n", "facility_list = tuple(facility_list)"]}, {"cell_type": "code", "execution_count": null, "id": "9d66e028-9ef2-4460-95c4-cd01ed2fe5ca", "metadata": {}, "outputs": [], "source": ["fetch_index = 2"]}, {"cell_type": "code", "execution_count": null, "id": "5a661df7-030c-48a4-85cf-9f1b40531336", "metadata": {}, "outputs": [], "source": ["while fetch_index > 1:\n", "    # Get Hourly Snaps for Unavailable Items (Inventory < 1)\n", "    print(fetch_index)\n", "    packaged_hourly_snap_query = f\"\"\"\n", "        with remove_item_list AS (\n", "            SELECT item_id FROM lake_rpc.item_category_details\n", "            WHERE l0_id IN (1487) AND l2_id IN (116,1961,63,1127)\n", "            GROUP BY 1\n", "\n", "            UNION\n", "\n", "            SELECT distinct item_id\n", "            FROM lake_rpc.product_product\n", "            WHERE id IN (\n", "                SELECT MAX(id) AS id FROM lake_rpc.product_product\n", "                WHERE active = 1 AND approved = 1 GROUP BY item_id\n", "            ) AND outlet_type IN (1) AND perishable IN (1)\n", "            GROUP BY 1\n", "        ),\n", "\n", "\n", "        grocery_base AS (\n", "            WITH base_0 AS (\n", "                SELECT * FROM rpc_daily_availability\n", "                WHERE order_date >= current_date - interval '330 minutes'\n", "\n", "            ),\n", "\n", "            base as \n", "            (\n", "            select * from base_0 where order_date in (select max(order_date) from base_0)\n", "            ),\n", "\n", "            rpc_base AS (\n", "                SELECT item_id, facility_id, DATE(order_date) AS date_, actual_quantity, blocked_quantity, \n", "                EXTRACT(hour FROM order_date) AS hour_ \n", "                FROM base\n", "                WHERE facility_id IN {facility_list}\n", "                and item_id in {cartbreaker_list}\n", "                GROUP BY 1,2,3,4,5,6\n", "            ),\n", "            availability AS (\n", "                SELECT date_, hour_, facility_id, item_id, actual_quantity - blocked_quantity AS inventory_at_hour\n", "                FROM rpc_base \n", "            )\n", "            SELECT *, 'Packaged Goods' AS assortment_type FROM availability\n", "        )\n", "        SELECT * FROM grocery_base WHERE item_id NOT IN (SELECT item_id FROM remove_item_list GROUP BY 1)\n", "        \"\"\"\n", "\n", "    packaged_hourly_snap_df = read_sql_query(packaged_hourly_snap_query, CON_REDSHIFT)\n", "    print(\"Packaged Hourly Snap Fetch Complete\")\n", "\n", "    fnv_per_hourly_snap_query = f\"\"\"\n", "        WITH fnv_base AS (\n", "            SELECT *, 'FnV' AS assortment_type \n", "            FROM (\n", "                SELECT date_, hour_, facility_id, item_id, current_inv as inventory_at_hour\n", "                FROM ( \n", "                    SELECT base.facility_id, base.item_id, DATE(base.updated_at) AS date_, EXTRACT(hour FROM base.updated_at) AS hour_, base.current_inv\n", "                    FROM metrics.fnv_hourly_details base\n", "                    INNER JOIN (\n", "                        SELECT outlet_id, item_id, date(updated_at) AS date_, extract(hour FROM updated_at) AS hour, MAX(updated_at) AS updated_at\n", "                        FROM metrics.fnv_hourly_details\n", "                        WHERE updated_at in (select max(updated_at) as updated_at from metrics.fnv_hourly_details)\n", "                        GROUP BY 1, 2, 3, 4\n", "                    ) latest ON base.outlet_id = latest.outlet_id\n", "                   AND base.item_id = latest.item_id\n", "                   AND base.updated_at = latest.updated_at\n", "                   WHERE base.updated_at in (select max(updated_at) as updated_at from metrics.fnv_hourly_details)\n", "                   AND base.facility_id IN {facility_list}\n", "                   GROUP BY 1, 2, 3, 4, 5\n", "                )\n", "                WHERE item_id in {cartbreaker_list}\n", "            )\n", "\n", "        ),\n", "\n", "        persh_base AS (\n", "            SELECT *, 'Perishable' AS assortment_type \n", "            FROM (\n", "                SELECT date_, hour_, facility_id, item_id, current_inv AS inventory_at_hour\n", "                FROM ( \n", "                    SELECT base.facility_id, base.item_id, DATE(base.updated_at) AS date_, extract(HOUR FROM base.updated_at) AS hour_, base.current_inv\n", "                    FROM metrics.perishable_hourly_details_v2 base\n", "                    INNER JOIN (\n", "                        SELECT outlet_id, item_id, DATE(updated_at) AS date_, EXTRACT(hour FROM updated_at) AS hour, MAX(updated_at) AS updated_at\n", "                        FROM metrics.perishable_hourly_details_v2\n", "                        WHERE updated_at in (select max(updated_at) as updated_at from metrics.perishable_hourly_details_v2)\n", "                        GROUP BY 1, 2, 3, 4 \n", "                    ) latest ON base.outlet_id = latest.outlet_id\n", "                    AND base.item_id = latest.item_id\n", "                    AND base.updated_at = latest.updated_at\n", "                    WHERE base.updated_at in (select max(updated_at) as updated_at from metrics.perishable_hourly_details_v2)\n", "                    AND base.facility_id IN {facility_list}\n", "                    GROUP BY 1, 2, 3, 4, 5\n", "                )\n", "            WHERE item_id in {cartbreaker_list}\n", "            )\n", "\n", "        )\n", "        SELECT * FROM fnv_base\n", "        UNION\n", "        SELECT * FROM persh_base\"\"\"\n", "\n", "    fnv_per_hourly_snap_df = read_sql_query(fnv_per_hourly_snap_query, CON_REDSHIFT)\n", "    print(\"FnV Perishable Hourly Snap Fetch Complete\")\n", "\n", "    packaged_hourly_snap_df = packaged_hourly_snap_df[\n", "        ~packaged_hourly_snap_df.item_id.isin(\n", "            tuple(list(fnv_per_hourly_snap_df[\"item_id\"].unique()))\n", "        )\n", "    ]\n", "\n", "    hourly_snap_base_df = pd.concat([packaged_hourly_snap_df, fnv_per_hourly_snap_df])\n", "\n", "    if hourly_snap_base_df[[\"date_\", \"hour_\"]].drop_duplicates().shape[0] > 1:\n", "        time.sleep(10)\n", "\n", "    fetch_index = hourly_snap_base_df[[\"date_\", \"hour_\"]].drop_duplicates().shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "6a5ed2a9-e689-4212-a1e2-e0af15907704", "metadata": {}, "outputs": [], "source": ["facility_city_mapping_query = f\"\"\"\n", "    select distinct facility_id,  cl.name as city\n", "    from lake_retail.console_outlet co\n", "    left join lake_retail.console_location cl on co.tax_location_id = cl.id\n", "    where active = 1 and business_type_id = 7\n", "    \"\"\"\n", "\n", "facility_city_mapping_df = read_sql_query(facility_city_mapping_query, CON_REDSHIFT)\n", "\n", "city_store_score_query = f\"\"\"\n", "    select \n", "            city, facility_id, store_weight\n", "    from\n", "        metrics.city_store_penetration\n", "    where \n", "        updated_at in (select max(updated_at) updated_at from metrics.city_store_penetration)\n", "    \"\"\"\n", "\n", "city_store_score_df = read_sql_query(city_store_score_query, CON_REDSHIFT)\n", "\n", "city_item_score_query = f\"\"\"\n", "    select \n", "        city, assortment_type, item_id, 1.0000000*cart_penetration::float as cart_penetration, 1.0000000*weights::float as weights\n", "    from \n", "        metrics.city_item_cart_penetration\n", "    where \n", "        updated_at in (select max(updated_at) as updated_at from metrics.city_item_cart_penetration)\n", "    \"\"\"\n", "\n", "city_item_score_df = read_sql_query(city_item_score_query, CON_REDSHIFT)\n", "\n", "hourly_snap_base_df = pd.merge(\n", "    hourly_snap_base_df, facility_city_mapping_df, on=[\"facility_id\"], how=\"left\"\n", ")\n", "hourly_snap_base_df = pd.merge(\n", "    hourly_snap_base_df,\n", "    cart_breakers_df[[\"city\", \"item_id\", \"l0\"]].drop_duplicates(),\n", "    on=[\"city\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "hourly_snap_base_df[\"cart_breaker_flag\"] = np.where(\n", "    hourly_snap_base_df[\"l0\"].isna(), 0, 1\n", ")\n", "hourly_snap_base_df = hourly_snap_base_df.drop(columns={\"l0\"})\n", "\n", "\n", "cart_breaker_availability = hourly_snap_base_df[\n", "    hourly_snap_base_df.cart_breaker_flag == 1\n", "]\n", "cart_breaker_availability[\"available_flag\"] = np.where(\n", "    cart_breaker_availability[\"inventory_at_hour\"] < 1, 0, 1\n", ")\n", "cart_breaker_availability = pd.merge(\n", "    cart_breaker_availability,\n", "    city_item_score_df[[\"city\", \"item_id\", \"weights\"]].drop_duplicates(),\n", "    on=[\"city\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "cart_breaker_availability[\"available_score\"] = (\n", "    cart_breaker_availability[\"weights\"] * cart_breaker_availability[\"available_flag\"]\n", ")\n", "\n", "del hourly_snap_base_df, cart_breakers_df\n", "\n", "available_number = (\n", "    cart_breaker_availability.groupby([\"date_\", \"hour_\", \"city\", \"facility_id\"])\n", "    .agg({\"item_id\": \"nunique\", \"available_score\": \"sum\", \"weights\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"available_score\": \"available_items\"})\n", ")\n", "available_number[\"wt_availability_facility\"] = (\n", "    available_number[\"available_items\"] / available_number[\"weights\"]\n", ")  # Need to do this as score is not normalized.\n", "\n", "available_number = pd.merge(\n", "    available_number, city_store_score_df, on=[\"city\", \"facility_id\"], how=\"left\"\n", ")\n", "available_number[\"store_weight\"] = available_number[\"store_weight\"].astype(float)\n", "available_number[\"wt_availability_facility\"] = available_number[\n", "    \"wt_availability_facility\"\n", "].astype(float)\n", "available_number[\"facility_ava_scores\"] = (\n", "    available_number[\"store_weight\"] * available_number[\"wt_availability_facility\"]\n", ")\n", "\n", "city_number = (\n", "    available_number.groupby([\"date_\", \"hour_\", \"city\"])\n", "    .agg(\n", "        {\"facility_id\": \"nunique\", \"facility_ava_scores\": \"sum\", \"store_weight\": \"sum\"}\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"facility_id\": \"count_of_ds\",\n", "            \"facility_ava_scores\": \"weighted_availability\",\n", "        }\n", "    )\n", ")\n", "\n", "city_number[\"weighted_availability\"] = (\n", "    city_number[\"weighted_availability\"] / city_number[\"store_weight\"]\n", ")\n", "\n", "city_number[\"date_time\"] = pd.to_datetime(\n", "    city_number[\"date_\"].astype(str) + \" \" + city_number[\"hour_\"].astype(str) + \":00:00\"\n", ")\n", "\n", "column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date for snapshot\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour of the day\"},\n", "    {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"city for facility\"},\n", "    {\"name\": \"count_of_ds\", \"type\": \"float\", \"description\": \"count of active DS\"},\n", "    {\n", "        \"name\": \"weighted_availability\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weighted availability for cart breakers for the city\",\n", "    },\n", "    {\"name\": \"store_weight\", \"type\": \"float\", \"description\": \"city store weights\"},\n", "    {\"name\": \"date_time\", \"type\": \"timestamp\", \"description\": \"timestamp in table\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"carts_lost_cart_breaker_availability_city\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"city\", \"date_\", \"hour_\"],\n", "    \"sortkey\": [\"date_\", \"city\"],\n", "    \"incremental_key\": \"date_time\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Table tracks hourly cart breaker weighted availability driving carts lost\",\n", "}\n", "pb.to_redshift(city_number, **kwargs)\n", "print(\"city_number write complete\")\n", "\n", "available_number[\"date_time\"] = pd.to_datetime(\n", "    available_number[\"date_\"].astype(str)\n", "    + \" \"\n", "    + available_number[\"hour_\"].astype(str)\n", "    + \":00:00\"\n", ")\n", "available_number = available_number.rename(\n", "    columns={\"item_id\": \"count_of_cart_breaker_items\"}\n", ")\n", "\n", "column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date for snapshot\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour of the day\"},\n", "    {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"city for facility\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"unique identifier for dark store in the city\",\n", "    },\n", "    {\n", "        \"name\": \"count_of_cart_breaker_items\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"count_of_cart_breaker_items\",\n", "    },\n", "    {\n", "        \"name\": \"available_items\",\n", "        \"type\": \"float\",\n", "        \"description\": \"availability weighted scores for cart breakers for the facility\",\n", "    },\n", "    {\n", "        \"name\": \"weights\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weighted scores (not based on availability) for cart breakers for the facility\",\n", "    },\n", "    {\n", "        \"name\": \"wt_availability_facility\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weighted availability for cart breakers for the facility - available_items / weights (As cart breaker scores are not normalized)\",\n", "    },\n", "    {\"name\": \"store_weight\", \"type\": \"float\", \"description\": \"city store weights\"},\n", "    {\n", "        \"name\": \"facility_ava_scores\",\n", "        \"type\": \"float\",\n", "        \"description\": \"city store weights * wt_availability_facility\",\n", "    },\n", "    {\"name\": \"date_time\", \"type\": \"timestamp\", \"description\": \"timestamp in table\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"carts_lost_cart_breaker_availability_facility\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"city\", \"date_\", \"hour_\"],\n", "    \"sortkey\": [\"date_\", \"city\"],\n", "    \"incremental_key\": \"date_time\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Table tracks hourly cart breaker weighted availability driving carts lost\",\n", "}\n", "pb.to_redshift(available_number, **kwargs)\n", "print(\"available_number write complete\")\n", "\n", "\n", "cart_breaker_availability[\"date_time\"] = pd.to_datetime(\n", "    cart_breaker_availability[\"date_\"].astype(str)\n", "    + \" \"\n", "    + cart_breaker_availability[\"hour_\"].astype(str)\n", "    + \":00:00\"\n", ")\n", "\n", "column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date for snapshot\"},\n", "    {\"name\": \"hour_\", \"type\": \"integer\", \"description\": \"hour of the day\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"unique identifier for dark store in the city\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"unique identifier for item\"},\n", "    {\n", "        \"name\": \"inventory_at_hour\",\n", "        \"type\": \"float\",\n", "        \"description\": \"inventory for the hour\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(70)\",\n", "        \"description\": \"type of assortment\",\n", "    },\n", "    {\"name\": \"city\", \"type\": \"varchar(100)\", \"description\": \"city for facility\"},\n", "    {\n", "        \"name\": \"cart_breaker_flag\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"flag to identify cart breakers\",\n", "    },\n", "    {\n", "        \"name\": \"available_flag\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"flag to if SKU is available\",\n", "    },\n", "    {\n", "        \"name\": \"weights\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weighted scores (not based on availability) for cart breakers for the facility\",\n", "    },\n", "    {\n", "        \"name\": \"available_score\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weights * available_flag\",\n", "    },\n", "    {\"name\": \"date_time\", \"type\": \"timestamp\", \"description\": \"timestamp in table\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"carts_lost_cart_breaker_item_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"date_\", \"hour_\", \"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"date_\", \"facility_id\"],\n", "    \"incremental_key\": \"date_time\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Table tracks hourly cart breaker availability at SKU level\",\n", "}\n", "pb.to_redshift(cart_breaker_availability, **kwargs)\n", "print(\"cart_breaker_availability write complete\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}