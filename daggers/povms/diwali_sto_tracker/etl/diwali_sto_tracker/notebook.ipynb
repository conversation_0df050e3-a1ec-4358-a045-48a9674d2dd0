{"cells": [{"cell_type": "code", "execution_count": null, "id": "d4926c10-621b-497f-b668-a3b0f39efdf6", "metadata": {}, "outputs": [], "source": ["!pip install matplotlib\n", "!pip install pandasql\n", "!pip install pymysql\n", "import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, timedelta, datetime\n", "import datetime as dt\n", "import json\n", "import shutil\n", "import pymysql\n", "import sys\n", "\n", "import boto3\n", "import io\n", "import warnings\n", "\n", "pd.set_option(\"display.max_colwidth\", None)\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "import matplotlib.pyplot as plt\n", "import pandasql as ps"]}, {"cell_type": "code", "execution_count": null, "id": "a3ecf355-1ecf-4b0d-872a-da22b359e3de", "metadata": {}, "outputs": [], "source": ["presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "a2e6445f-4c25-4158-ae5b-794f2811b3ee", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(0, max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "markdown", "id": "f2cff8e3-2779-4acd-bb09-967352c9d4db", "metadata": {}, "source": ["#### Diwali SKUs"]}, {"cell_type": "code", "execution_count": null, "id": "bfa823c8-d636-4cf5-bd93-5319c10183d1", "metadata": {}, "outputs": [], "source": ["temp = pb.from_sheets(\"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"assortment_input\")\n", "temp = temp[temp[\"assortment_type\"] == \"Diwali\"]\n", "items = tuple(np.unique(temp[\"item_id\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "3c164588-29f7-4d89-b863-cfd9477065b4", "metadata": {}, "outputs": [], "source": ["len(items)"]}, {"cell_type": "code", "execution_count": null, "id": "52ec4bee-fa0f-4fb6-a25b-0f0b2bee5100", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "\n", "with sto_base as\n", "(\n", "    SELECT \n", "        distinct sto_id \n", "    FROM\n", "        metrics.esto_details\n", "    WHERE\n", "        sto_created_at >= date(getdate() + interval '5.5 hours') - 1\n", "--        between current_date - 5 and current_date - 3\n", "    AND\n", "        receiving_outlet_id in \n", "        (\n", "            select \n", "                distinct id \n", "            from lake_retail.console_outlet where active = 1 and business_type_id = 7 \n", "        )\n", "    AND item_id in {items}\n", "),\n", "\n", "base as\n", "(\n", "    select \n", "        ars_run_id, sto_id, sender_outlet_id, receiving_outlet_id, item_id, invoice_id,\n", "        \n", "        -- Creation Metrics\n", "        sto_created_at, sto_type, sto_state, expected_quantity, reserved_quantity,\n", "        \n", "        -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "        sto_invoice_created_at, billed_quantity, invoice_state,\n", "        \n", "        -- Dispatch Metrics\n", "        dispatch_time, sto_dispatched_at,\n", "        trip_started_at, trip_created_at,\n", "        \n", "        -- Arrival at DS Metrics\n", "        consignment_received_at,\n", "        \n", "        -- GRN Metrics\n", "        grn_started_at, -- Same as putaway_time in DS\n", "        putlist_creation_time,\n", "        putaway_time,\n", "        inwarded_quantity,\n", "        \n", "        -- Discrepancy Metrics\n", "        discrepancy_created_at, disc_qty\n", "        --dispatch_time = ARS Ideal Dispatch Time> Actual sto_dispatched_at\n", "    from \n", "        metrics.esto_details \n", "    where\n", "        sto_id in \n", "            (\n", "            select \n", "                distinct sto_id \n", "            from sto_base\n", "            )\n", "        and item_id in {items}\n", "),\n", "\n", "\n", "fixing_missing_values_for_receiving_time as\n", "(\n", "    SELECT\n", "        distinct ars_run_id, receiving_outlet_id, consignment_received_at\n", "    FROM\n", "    (\n", "        SELECT \n", "            *\n", "        FROM\n", "            base\n", "        WHERE\n", "            consignment_received_at is not null\n", "    )\n", "    \n", "),\n", "\n", "fixing_missing_values_for_dispatch_time as\n", "(\n", "    SELECT\n", "        distinct ars_run_id, receiving_outlet_id, sto_dispatched_at\n", "    FROM\n", "    (\n", "        SELECT \n", "            *\n", "        FROM\n", "            base\n", "        WHERE\n", "            sto_dispatched_at is not null\n", "    )\n", "),\n", "\n", "\n", "fixing_missing_values_for_trip_start_time as\n", "(\n", "    SELECT\n", "        distinct ars_run_id, receiving_outlet_id, trip_started_at\n", "    FROM\n", "    (\n", "        SELECT \n", "            *\n", "        FROM\n", "            base\n", "        WHERE\n", "            trip_started_at is not null\n", "    )\n", "),\n", "\n", "\n", "map_missing_time_to_base as\n", "(\n", "    SELECT\n", "        b.*,\n", "        case \n", "            when (b.consignment_received_at is null) and (b.sto_invoice_created_at is not null) then fmvrt.consignment_received_at else b.consignment_received_at end as treated_consignment_received_at, -- When truck / STO is received at DS\n", "        \n", "        case \n", "            when (b.sto_dispatched_at is null) and (b.sto_invoice_created_at is not null) then fmvdt.sto_dispatched_at else b.sto_dispatched_at end as treated_sto_dispatched_at, -- When STO is scanned and loaded\n", "        \n", "        case \n", "            when (b.trip_started_at is null) and (b.sto_invoice_created_at is not null) then fmvtst.trip_started_at else b.trip_started_at end as treated_trip_started_at -- When truck has started from origin\n", "        \n", "    FROM\n", "        base b\n", "    LEFT JOIN\n", "        fixing_missing_values_for_receiving_time fmvrt\n", "    ON\n", "        b.ars_run_id = fmvrt.ars_run_id\n", "    and\n", "        b.receiving_outlet_id = fmvrt.receiving_outlet_id\n", "    \n", "    LEFT JOIN\n", "        fixing_missing_values_for_dispatch_time fmvdt\n", "    ON\n", "        b.ars_run_id = fmvdt.ars_run_id\n", "    and\n", "        b.receiving_outlet_id = fmvdt.receiving_outlet_id\n", "        \n", "    LEFT JOIN\n", "        fixing_missing_values_for_trip_start_time fmvtst\n", "    ON\n", "        b.ars_run_id = fmvtst.ars_run_id\n", "    and\n", "        b.receiving_outlet_id = fmvtst.receiving_outlet_id\n", "),\n", "\n", "treating_base_sto_dispatch as -- \n", "(\n", "    SELECT\n", "      *\n", "    FROM\n", "        map_missing_time_to_base\n", "    WHERE\n", "        treated_sto_dispatched_at is null\n", "    UNION\n", "    \n", "    \n", "    SELECT\n", "      *\n", "    FROM\n", "        map_missing_time_to_base\n", "    WHERE\n", "        (treated_sto_dispatched_at is not null)\n", "    AND\n", "        treated_sto_dispatched_at >= sto_invoice_created_at\n", "),\n", "\n", "treating_base_trip_start as -- \n", "(\n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_sto_dispatch\n", "    WHERE\n", "        treated_trip_started_at is null\n", "    UNION\n", "    \n", "    \n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_sto_dispatch\n", "    WHERE\n", "        (treated_trip_started_at is not null)\n", "    AND\n", "        treated_trip_started_at >= sto_invoice_created_at\n", "),\n", "\n", "treating_base_consignment_arrival as -- \n", "(\n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_trip_start\n", "    WHERE\n", "        treated_consignment_received_at is null\n", "    \n", "    UNION\n", "    \n", "    SELECT\n", "      *\n", "    FROM\n", "        treating_base_trip_start\n", "    WHERE\n", "        (treated_consignment_received_at is not null)\n", "    AND\n", "        treated_consignment_received_at >= treated_trip_started_at\n", "),\n", "\n", "\n", "identify_correct_trip_details as\n", "\n", "(\n", "    SELECT\n", "        ars_run_id, sto_id, sender_outlet_id, receiving_outlet_id, item_id, sto_type, sto_state, invoice_id, invoice_state,\n", "        \n", "        -- quantity flow\n", "        expected_quantity, reserved_quantity, billed_quantity, inwarded_quantity, disc_qty,\n", "        \n", "        -- Creation Metrics\n", "        sto_created_at, \n", "        \n", "        -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "        sto_invoice_created_at, \n", "        \n", "        -- Dispatch Metrics\n", "        trip_started_at, trip_created_at, dispatch_time, sto_dispatched_at,\n", "        \n", "        -- Arrival at DS Metrics\n", "        consignment_received_at,\n", "        \n", "        -- GRN Metrics\n", "        grn_started_at, \n", "        putlist_creation_time,\n", "        putaway_time,\n", "        -- min(putlist_creation_time) as grn_start_time,\n", "        -- max(putaway_time) as grn_end_time,\n", "        \n", "        -- Discrepancy Metrics\n", "        discrepancy_created_at,\n", "        \n", "        min(treated_consignment_received_at) as treated_consignment_received_at, min(treated_trip_started_at) as treated_trip_started_at, min(treated_sto_dispatched_at) as treated_sto_dispatched_at\n", "    FROM\n", "        treating_base_consignment_arrival\n", "    GROUP BY\n", "        1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25\n", "\n", "\n", "),\n", "\n", "\n", "-- Base Merge to retain cases where trip details are missing\n", "getting_summary as \n", "(\n", "SELECT\n", "    b.ars_run_id, b.sto_id, b.sender_outlet_id, b.receiving_outlet_id, b.item_id, b.sto_type, b.sto_state, b.invoice_id, b.invoice_state,\n", "        \n", "    -- quantity flow\n", "    b.expected_quantity, b.reserved_quantity, b.billed_quantity, b.inwarded_quantity, b.disc_qty,\n", "    \n", "    -- Creation Metrics\n", "    b.sto_created_at, \n", "    \n", "    -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "    b.sto_invoice_created_at, \n", "    \n", "    -- Dispatch Metrics\n", "    treated_trip_started_at, treated_sto_dispatched_at,\n", "    b.trip_started_at, b.trip_created_at, b.dispatch_time, b.sto_dispatched_at,\n", "    \n", "    -- Arrival at DS Metrics\n", "    b.consignment_received_at, treated_consignment_received_at,\n", "    \n", "    -- GRN Metrics\n", "    b.grn_started_at, \n", "    b.putlist_creation_time,\n", "    b.putaway_time,\n", "\n", "    -- Discrepancy Metrics\n", "    b.discrepancy_created_at,\n", "    \n", "    case \n", "        when\n", "            extract(hour from b.sto_created_at) >= 16\n", "        then\n", "            'Slot A'\n", "        when\n", "            extract(hour from b.sto_created_at) < 7\n", "        then\n", "            'Slot A'\n", "        else\n", "            'Slot B'\n", "        end as\n", "            slot\n", "    \n", "FROM\n", "    base b\n", "LEFT JOIN\n", "    identify_correct_trip_details ictd\n", "ON\n", "    b.ars_run_id = ictd.ars_run_id and b.sto_id = ictd.sto_id and b.sender_outlet_id = ictd.sender_outlet_id and b.receiving_outlet_id = ictd.receiving_outlet_id and b.item_id = ictd.item_id and b.sto_type = ictd.sto_type and b.sto_state = ictd.sto_state and b.invoice_id = ictd.invoice_id and b.invoice_state = ictd.invoice_state\n", "),\n", "\n", "flagging_dis as\n", "(\n", "SELECT \n", "    \n", "    ars_run_id, sto_id, sender_outlet_id, receiving_outlet_id, item_id, sto_type, sto_state, invoice_id, invoice_state, slot,\n", "        \n", "    -- quantity flow\n", "    expected_quantity, reserved_quantity, billed_quantity, inwarded_quantity, disc_qty,\n", "    \n", "    -- Creation Metrics\n", "    sto_created_at, \n", "    \n", "    -- Billing Metrics: sto_invoice_created_at = STO Billed AT\n", "    sto_invoice_created_at, \n", "    \n", "    -- Dispatch Metrics\n", "    treated_trip_started_at, treated_sto_dispatched_at,\n", "    trip_started_at, trip_created_at, dispatch_time, sto_dispatched_at,\n", "    \n", "    -- Arrival at DS Metrics\n", "    consignment_received_at, treated_consignment_received_at,\n", "    \n", "    -- GRN Metrics\n", "    grn_started_at, \n", "    putlist_creation_time,\n", "    putaway_time,\n", "    -- min(putlist_creation_time) as grn_start_time,\n", "    -- max(putaway_time) as grn_end_time,\n", "    \n", "    -- Discrepancy Metrics\n", "    discrepancy_created_at,\n", "    \n", "    \n", "    -- Calculate Ideal Creation to Billing Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '3.5 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '3.5 hours'\n", "        else\n", "            sto_created_at + interval '4 hours'\n", "        end\n", "            as ideal_sto_billing_time,\n", "    \n", "    \n", "    -- <PERSON><PERSON> Ideal Billing to Dispatch Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '4.5 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '4.5 hours'\n", "        else\n", "            sto_created_at + interval '7 hours'\n", "        end\n", "            as ideal_fleet_dispatch_time,\n", "    \n", "    \n", "    -- Calculate Ideal Dispatch to Arrival Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '6 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '6 hours'\n", "        else\n", "            sto_created_at + interval '11 hours'\n", "        end\n", "            as ideal_fleet_arrival_time,\n", "    \n", "    -- Calculate Ideal GRN Time\n", "    case \n", "        when\n", "            extract(hour from sto_created_at) >= 16\n", "        then\n", "            date(sto_created_at) + interval '1 days' + interval '7 hours'\n", "        when\n", "            extract(hour from sto_created_at) < 6\n", "        then\n", "            date(sto_created_at) + interval '7 hours'\n", "        else\n", "            sto_created_at + interval '13 hours'\n", "        end\n", "            as ideal_sto_grn_time,\n", "    \n", "    -- Discrepancy: creation to billing, billing to dispatch, dispatch to Arrival, Arrival to GRN\n", "    \n", "    case \n", "        when ars_run_id = 0 then 'Manual' else 'Auto' end as trigger_type,\n", "    \n", "    case \n", "        when sto_created_at > sto_invoice_created_at then 1 else 0 end as creation_to_billing_discrepancy,\n", "        \n", "    case \n", "        when sto_invoice_created_at > treated_trip_started_at then 1 else 0 end as billing_to_dispatch_discrepancy,\n", "    \n", "    case \n", "        when treated_trip_started_at > treated_consignment_received_at then 1 else 0 end as dispatch_to_arrival_discrepancy,\n", "        \n", "    case \n", "        when\n", "            treated_trip_started_at is null then 1 else 0 end as missing_trip_start_time,\n", "    \n", "    case \n", "        when\n", "            treated_consignment_received_at is null then 1 else 0 end as missing_trip_arrival_time\n", "FROM \n", "    getting_summary\n", "),\n", "-- Flag GRN Pending and GRN Delay Cases\n", "\n", "flag_grn_status as \n", "(\n", "    SELECT \n", "    *,\n", "    case\n", "        when\n", "            ((grn_started_at is null) and (discrepancy_created_at is null)) and (getdate() + interval '5.5 hours' <= ideal_sto_grn_time) \n", "        then\n", "            'GRN Pending - Ideal TAT not breached'\n", "        when\n", "            ((grn_started_at is null) and (discrepancy_created_at is null)) and (getdate() + interval '5.5 hours' > ideal_sto_grn_time) \n", "        then\n", "            'GRN Pending - Ideal TAT breached'\n", "        when\n", "            (discrepancy_created_at is not null)-- and (discrepancy_created_at > ideal_sto_grn_time))\n", "        then\n", "            'DISCREPANCY_NOTE_GENERATED'\n", "        when\n", "            ((grn_started_at is not null) and (grn_started_at > ideal_sto_grn_time))\n", "        then\n", "            'GRN Completed Delay'\n", "        when\n", "            ((grn_started_at is not null) and (grn_started_at <= ideal_sto_grn_time))-- or ((grn_started_at is null) and (discrepancy_created_at is not null) and (discrepancy_created_at <= ideal_sto_grn_time))\n", "        then\n", "            'GRN Completed on-time'\n", "        else\n", "            'Others'\n", "        end \n", "            as GRN_Status,\n", "    \n", "    datediff(seconds, sto_created_at, sto_invoice_created_at)/3600.0 as actual_sto_creation_to_billing_tat,\n", "    datediff(seconds, sto_invoice_created_at, treated_trip_started_at)/3600.0 as actual_sto_billing_to_trip_start_tat,\n", "    datediff(seconds, treated_trip_started_at, treated_consignment_received_at)/3600.0 as actual_fleet_dispatch_to_arrival_tat,\n", "    datediff(seconds, treated_consignment_received_at, grn_started_at)/3600.0 as actual_fleet_arrival_to_grn_tat\n", "    \n", "    \n", "    \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, sto_created_at, sto_invoice_created_at)/3600.0\n", "    --     end \n", "    --         as actual_sto_creation_to_billing_tat,\n", "    \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, sto_invoice_created_at, treated_trip_started_at)/3600.0\n", "    --     end \n", "    --         as actual_sto_billing_to_trip_start_tat,\n", "            \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, treated_trip_started_at, treated_consignment_received_at)/3600.0\n", "    --     end \n", "    --         as actual_fleet_dispatch_to_arrival_tat,\n", "    \n", "    -- case\n", "    --     when\n", "    --         GRN_Status in ('GRN Delay', 'GRN Pending - Ideal TAT breached')\n", "    --     then\n", "    --         datediff(seconds, treated_consignment_received_at, grn_started_at)/3600.0\n", "    --     end \n", "    --         as actual_fleet_arrival_to_grn_tat\n", "    \n", "    FROM \n", "    flagging_dis\n", ")\n", "\n", "SELECT\n", "    ars_run_id, sto_id, --sto_type, \n", "    sto_state, sender_outlet_id, sender.name as sender_outlet_name, receiving_outlet_id, receiver.name as receiving_outlet_name, base.item_id,catg.name as item_name, invoice_id, invoice_state, slot,\n", "    \n", "    case \n", "        when\n", "            grn_status = 'DISCREPANCY_NOTE_GENERATED' then 'DISCREPANCY_NOTE_GENERATED'\n", "        when\n", "            (sto_invoice_created_at is null) and (treated_trip_started_at is null) and (treated_consignment_received_at is null) and (grn_started_at is null) then 'STO Billing Pending'\n", "        when\n", "            (treated_trip_started_at is null) and (treated_consignment_received_at is null) and (grn_started_at is null) is null then 'Dispatch Pending'\n", "        when\n", "            (treated_consignment_received_at is null) and (grn_started_at is null) is null then 'Arrival at DS Pending'\n", "        when\n", "            grn_started_at is null then 'GRN Pending'\n", "        else\n", "            invoice_state end as current_status,\n", "    expected_quantity, reserved_quantity, billed_quantity, inwarded_quantity as grn_at_ds_quantity, disc_qty as discrepancy_marked,\n", "    sto_created_at as sto_creation_time, sto_invoice_created_at as sto_billed_time, treated_trip_started_at as fleet_dispatch_time, treated_consignment_received_at as fleet_arrival_time, grn_started_at as grn_time, discrepancy_created_at as discrepancy_marked_time,\n", "    actual_sto_creation_to_billing_tat, actual_sto_billing_to_trip_start_tat, actual_fleet_dispatch_to_arrival_tat, actual_fleet_arrival_to_grn_tat,\n", "    ideal_sto_billing_time, ideal_fleet_dispatch_time, ideal_fleet_arrival_time, ideal_sto_grn_time\n", "FROM\n", "    flag_grn_status base\n", "LEFT JOIN\n", "    lake_retail.console_outlet receiver\n", "ON\n", "    receiver.id = base.receiving_outlet_id and receiver.business_type_id in (7)\n", "\n", "LEFT JOIN\n", "    lake_retail.console_outlet sender\n", "ON\n", "    sender.id = base.sender_outlet_id and sender.business_type_id in (1,12,19,20,21)\n", "LEFT JOIN\n", "    lake_rpc.item_category_details catg on base.item_id=catg.item_id \n", "\"\"\"\n", "\n", "sto_tracker = read_sql_query(query, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "01842213-2c2b-4ad0-a279-be81357dff88", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(sto_tracker, \"17yNLuFwhn0qlv8UPsp-l3R1sgc60xEcss5gviygHens\", \"raw_data_sto_item\")"]}, {"cell_type": "code", "execution_count": null, "id": "bea78195-a0c9-4538-b748-d98d7da01641", "metadata": {}, "outputs": [], "source": ["sto_tracker.columns"]}, {"cell_type": "code", "execution_count": null, "id": "478e2b89-7807-46f6-8e90-8734270a0788", "metadata": {}, "outputs": [], "source": ["sto_level = (\n", "    sto_tracker.groupby(\n", "        by=[\n", "            \"sto_id\",\n", "            \"sto_state\",\n", "            \"sender_outlet_id\",\n", "            \"sender_outlet_name\",\n", "            \"receiving_outlet_id\",\n", "            \"receiving_outlet_name\",\n", "            \"slot\",\n", "            \"current_status\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"expected_quantity\": \"sum\",\n", "            \"reserved_quantity\": \"sum\",\n", "            \"billed_quantity\": \"sum\",\n", "            \"grn_at_ds_quantity\": \"sum\",\n", "            \"discrepancy_marked\": \"sum\",\n", "            \"sto_creation_time\": \"min\",\n", "            \"sto_billed_time\": \"min\",\n", "            \"fleet_dispatch_time\": \"min\",\n", "            \"fleet_arrival_time\": \"min\",\n", "            \"grn_time\": \"max\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "49fb06b9-6a00-4c4b-a9c1-903a56f20573", "metadata": {}, "outputs": [], "source": ["sto_level[\"Total TAT\"] = (sto_level[\"grn_time\"] - sto_level[\"sto_billed_time\"]).dt.seconds\n", "sto_level[\"Invoice to Dispatch\"] = (\n", "    sto_level[\"fleet_dispatch_time\"] - sto_level[\"sto_billed_time\"]\n", ").dt.seconds\n", "sto_level[\"Dispatch to Arrival\"] = (\n", "    sto_level[\"fleet_arrival_time\"] - sto_level[\"fleet_dispatch_time\"]\n", ").dt.seconds\n", "sto_level[\"Arrival to GRN\"] = (sto_level[\"grn_time\"] - sto_level[\"fleet_arrival_time\"]).dt.seconds"]}, {"cell_type": "code", "execution_count": null, "id": "84b78633-862f-4a4c-a205-7bb1a4f997f1", "metadata": {}, "outputs": [], "source": ["sto_level[sto_level[\"sto_id\"] == 6482232]"]}, {"cell_type": "code", "execution_count": null, "id": "0f555bbb-c99e-4a1e-af14-cdc7a8d7c128", "metadata": {}, "outputs": [], "source": ["time = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "sto_level[\"current_time\"] = time"]}, {"cell_type": "code", "execution_count": null, "id": "7c0ffeb6-3aa5-457c-b714-0cf4c730facb", "metadata": {}, "outputs": [], "source": ["sto_level[\"Total TAT\"] = np.where(\n", "    (sto_level[\"grn_time\"].isna()) & (sto_level[\"sto_billed_time\"].notnull()),\n", "    (sto_level[\"current_time\"] - sto_level[\"sto_billed_time\"]).dt.seconds,\n", "    sto_level[\"Total TAT\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7905f9ea-04bc-493d-af87-5efcef6b49fd", "metadata": {}, "outputs": [], "source": ["sto_level = sto_level[\n", "    (sto_level[\"current_status\"] != \"GRN Complete\")\n", "    & (sto_level[\"current_status\"] != \"DISCREPANCY_NOTE_GENERATED\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0a119d8f-fbf5-4ce2-9982-54b937578306", "metadata": {}, "outputs": [], "source": ["sto_level = sto_level.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "f5cee238-6423-4dd6-a73a-bfcfd588994c", "metadata": {}, "outputs": [], "source": ["sto_level[\"Total TAT Hour\"] = (\n", "    (sto_level[\"Total TAT\"] / (3600)).astype(int).astype(str)\n", "    + \"h \"\n", "    + (sto_level[\"Total TAT\"] % (3600) / 60).astype(int).astype(str)\n", "    + \"m \"\n", ")\n", "\n", "sto_level[\"Invoice to Dispatch Hour\"] = (\n", "    (sto_level[\"Invoice to Dispatch\"] / (3600)).astype(int).astype(str)\n", "    + \"h \"\n", "    + (sto_level[\"Invoice to Dispatch\"] % (3600) / 60).astype(int).astype(str)\n", "    + \"m \"\n", ")\n", "\n", "sto_level[\"Dispatch to Arrival Hour\"] = (\n", "    (sto_level[\"Dispatch to Arrival\"] / (3600)).astype(int).astype(str)\n", "    + \"h \"\n", "    + (sto_level[\"Dispatch to Arrival\"] % (3600) / 60).astype(int).astype(str)\n", "    + \"m \"\n", ")\n", "\n", "sto_level[\"Arrival to GRN Hour\"] = (\n", "    (sto_level[\"Arrival to GRN\"] / (3600)).astype(int).astype(str)\n", "    + \"h \"\n", "    + (sto_level[\"Arrival to GRN\"] % (3600) / 60).astype(int).astype(str)\n", "    + \"m \"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "37fde090-1c8f-4e80-87df-1f627c4f6c5d", "metadata": {}, "outputs": [], "source": ["sto_level[sto_level[\"sto_id\"] == 6505443]"]}, {"cell_type": "code", "execution_count": null, "id": "6e3e7a03-25f0-4bf7-9f70-431fceff4484", "metadata": {}, "outputs": [], "source": ["sto_level = sto_level[\n", "    [\n", "        \"sto_id\",\n", "        \"sto_state\",\n", "        \"sender_outlet_id\",\n", "        \"sender_outlet_name\",\n", "        \"receiving_outlet_id\",\n", "        \"receiving_outlet_name\",\n", "        \"slot\",\n", "        \"current_status\",\n", "        \"expected_quantity\",\n", "        \"reserved_quantity\",\n", "        \"billed_quantity\",\n", "        \"grn_at_ds_quantity\",\n", "        \"discrepancy_marked\",\n", "        \"sto_creation_time\",\n", "        \"sto_billed_time\",\n", "        \"fleet_dispatch_time\",\n", "        \"fleet_arrival_time\",\n", "        \"grn_time\",\n", "        \"Total TAT Hour\",\n", "        \"Invoice to Dispatch Hour\",\n", "        \"Dispatch to Arrival Hour\",\n", "        \"Arrival to GRN Hour\",\n", "        \"Total TAT\",\n", "        \"Invoice to Dispatch\",\n", "        \"Dispatch to Arrival\",\n", "        \"Arrival to GRN\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3f29bcfc-2599-4b61-9b23-438880fb1051", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(sto_level, \"17yNLuFwhn0qlv8UPsp-l3R1sgc60xEcss5gviygHens\", \"raw_data_sto\")"]}, {"cell_type": "code", "execution_count": null, "id": "55809760-46b0-4c2e-aec2-73fc65788609", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "00a0f45f-0dcd-43dc-a8f8-9fd918414130", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}