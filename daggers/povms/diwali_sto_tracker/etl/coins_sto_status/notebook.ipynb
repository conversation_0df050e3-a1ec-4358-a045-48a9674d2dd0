{"cells": [{"cell_type": "code", "execution_count": null, "id": "4c2024cb-1932-4c15-99f0-af1b549c9f05", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "CON_IMS = pb.get_connection(\"[Replica] RDS IMS\")\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "id": "f89a8584-7f5d-4033-8719-769b4fac102d", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "markdown", "id": "52c60e10-4f74-4ad5-a3e1-023434193bc4", "metadata": {}, "source": ["### STO IDs Input"]}, {"cell_type": "code", "execution_count": null, "id": "3cd73859-8a5e-4109-bad4-80dc86d8af63", "metadata": {}, "outputs": [], "source": ["sto_ids = pb.from_sheets(\"1yAYqOjs_MU4e5Qg2djZOei6koh-PdAuV_lU-gVVSMg4\", \"sto id input\")\n", "sto_ids = sto_ids[[\"sto_id\"]].astype(int)\n", "sto_id_list = tuple(list(sto_ids[\"sto_id\"].unique()))\n", "sto_id_list"]}, {"cell_type": "code", "execution_count": null, "id": "4f9aff80-eea3-452e-bfd2-07a8363e2541", "metadata": {}, "outputs": [], "source": ["sto_status_sql = f\"\"\"\n", "    SELECT \n", "        isd.outlet_id as sender_outlet_id, isd.merchant_outlet_id as receiver_outlet_id,\n", "        ist.item_id, \n", "        iss.sto_id, iss.sto_state,\n", "        CASE WHEN iss.sto_state = 1 THEN 'CREATED'\n", "        WHEN iss.sto_state = 2 THEN 'BILLED'\n", "        WHEN iss.sto_state = 3 THEN 'EXPIRED'\n", "        WHEN iss.sto_state = 4 THEN 'INWARD'\n", "        WHEN iss.sto_state = 5 THEN 'PARTIAL_BILLED' END as sto_state_name, \n", "        (iss.created_at + interval 330 minute) as created_at_ist\n", "    FROM ims.ims_sto_state_log iss\n", "    LEFT JOIN ims.ims_sto_details isd ON isd.sto_id = iss.sto_id\n", "    LEFT JOIN ims.ims_sto_item ist ON ist.sto_id = iss.sto_id\n", "    WHERE iss.sto_id in {sto_id_list}\n", "\n", "    \"\"\"\n", "\n", "sto_status = read_sql_query(sto_status_sql, CON_IMS)\n", "sto_status[\"rank\"] = sto_status.groupby([\"sto_id\"])[\"created_at_ist\"].rank(\n", "    method=\"first\", ascending=False\n", ")\n", "sto_status = sto_status[sto_status[\"rank\"] == 1].reset_index().drop(columns={\"index\", \"rank\"})\n", "\n", "\n", "sto_status.insert(0, \"updated_at_ist\", str(datetime.today() + timedelta(hours=5.5)))"]}, {"cell_type": "code", "execution_count": null, "id": "1c4e0df1-88bc-48a1-9074-67d5a133f40a", "metadata": {}, "outputs": [], "source": ["sto_status"]}, {"cell_type": "code", "execution_count": null, "id": "caf747e8-0b62-4a6a-86a5-fcf6782fa857", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(sto_status, \"1yAYqOjs_MU4e5Qg2djZOei6koh-PdAuV_lU-gVVSMg4\", \"STO States\")"]}, {"cell_type": "code", "execution_count": null, "id": "c9cade5a-814c-4b13-9e22-d44336decb82", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}