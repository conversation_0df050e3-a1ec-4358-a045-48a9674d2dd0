{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### 01. Get ars_active_facilities - ars_active_outlet, ars_active_facilities\n", "#### 02. Get Items from Master Assortment to these facilities - master_assortment\n", "#### 03. Get Vendor Details - vendor_mapping or Manufacturer and PO Cycle Details\n", "\n", "## Process\n", "#### 01. Merge master assortment with po_cycle and console_outlet (business_type_id = 1,12)\n", "#### 02. Add Landing price, replace NA with mrp*0.75 \n", "#### 03. Add UOM\n", "#### 04. Add TEA Mapping to identify if item is transfer SKU\n", "#### 05. Add buffer_doi, replace NA with default fillrate from fill_rate_buffer_default, add default_days\n", "#### 06. Add default_days with fill_rate_buffer_doi to get vendor_doi\n", "#### 07. Add vendor_tat\n", "#### 08. Add b2b_vendor_tat and b2b_transfer_doi\n", "#### 09. Add Dark Store Transfer TAT & Threshold DOI\n", "#### 10. Add Unblocked Quantity - backend (be), b2b (self), ds (darkstore, frontend)\n", "#### 11. Add CPD - b2b (self), ds (darkstore, frontend)\n", "#### 12. Add intransit Quantity - Open STO (b2b to DS), Open PO -> <Also, add STOs coming to the facility>\n", "\n", "## Calculations\n", "#### 13. Calculate Final Inventory - b2b_inv, dark_store_inv, open_sto, open PO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "\n", "import boto3\n", "import io\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "CON_IMS = pb.get_connection(\"[Replica] RDS IMS\")\n", "CON_PO = pb.get_connection(\"[Replica] RDS PO\")\n", "\n", "\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "from tabulate import tabulate\n", "\n", "\n", "def check_level(df, cols, identifier):\n", "    if df.shape[0] != df[cols].drop_duplicates().shape[0]:\n", "        query = \" and \".join([x + \" > 1\" for x in list(df.columns) if x not in cols])\n", "        temp_df = df.groupby(cols).count().reset_index()\n", "        sample = tabulate(temp_df.query(query), tablefmt=\"pipe\", headers=\"keys\")\n", "        error = f\"\"\":red_circle: *Alert*: Duplication found while extracting `{identifier}` for Run ID: `{Tag}`\\n\n", "        ```Count on grouped by {\", \".join(cols)}:\\n\\n{sample}```\"\"\"\n", "        print(error)\n", "        # raise Exception(\"Sorry, level does not match\", identifier)\n", "    else:\n", "        print(\"No errors\")\n", "        log_file_name = f\"{logs}/{Tag}_{identifier}_extracted_data.csv\"\n", "        df.to_csv(log_file_name, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Tag = \"datapulls\"\n", "cwd = os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# directories\n", "GLOBAL_BASE_DIR = cwd\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from slack_sdk import WebClient\n", "\n", "\n", "def send_to_slack(\n", "    text,\n", "    channel=\"#script-error-tracking-logs\",\n", "    token=pb.get_secret(\"retail/slack_secrets/\")[\"wh_inv_slack\"],\n", "):\n", "    client = WebClient(token=token)\n", "    try:\n", "        response = client.chat_postMessage(channel=channel, text=text)\n", "    except SlackApiError as e:\n", "        assert e.response[\"ok\"] is False\n", "        assert e.response[\"error\"]  # str like 'invalid_auth', 'channel_not_found'\n", "        print(f\"Got an error: {e.response['error']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inputs - \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_active_outlet_query = f\"\"\"select case when om.facility_id in (24,34,50,63,240,360,376,428,429,430,431,516,517,616,765,720,721,722,723,887,854,855,856,857,858,859,860,861,\n", "724,863,1057,1060,1058,1059,1122,1168,1210) then 'East'\n", "\n", "when om.facility_id in (1,22,48,56,149,301,387,388,392,415,416,435,436,437,438,439,440,513,542,543,599,740,739,738,910,911,912,741,742,968,969,970,971,\n", "748,749,941,942,1166) then 'West'\n", "\n", "when om.facility_id in (3,32,42,43,116,179,213,241,375,377,417,418,419,420,422,476,477,423,725,731,732,733,734,766,730,421,726,727,913,914,983,984,\n", "1019,1020,1021,915,916, 728, 729,917,918,919,986,987,989,990,991,992,1022,1023,1182,1183,1184,1185,1186,1187,1188,1212,988) then 'South'\n", "\n", "when om.facility_id in (12,15,26,29,30,49,54,92,135,136,137,140,142,152,161,177,184,194,197,201,202,209,212,214,219,\n", "223,225,227,228,232,233,236,237,238,242,246,247,259,262,263,264,268,271,272,274,275,281,285,291,299,315,316,318,319,\n", "327,328,338,369,370,374,389,391,394,397,398,402,403,404,409,424,425,432,433,434,441,442,443,444,445,446,447,448,449,\n", "450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,478,479,480,\n", "481,482,483,494,497,498,554,555,574,598,602,603,614,762,773,766,681,807,767,786,682,701,702,704,768,769,770,778,779,\n", "785,787,790,791,795,796,853,789,906,788,792,747,210,781,864,866,867,1009,1015,1016,703,705,865,1010,1025,1111,366,1144, 680, 771,772,793,794,797,868,869,870,871,932,994,996,1011,1018,1026,1035,1067,1093,1094,1102,1181,1206,1209,1211) then 'North' end as zone,\n", "\n", "om.outlet_id AS hot_outlet_id,\n", "om.outlet_name AS hot_outlet_name,\n", "CASE\n", "    WHEN wom.cloud_store_id IS NULL THEN om.outlet_id\n", "    ELSE wom.cloud_store_id\n", "                                   END AS inv_outlet_id,\n", "CASE\n", "    WHEN wom.cloud_store_id IS NULL THEN om.outlet_name\n", "    ELSE rco2.name\n", "                                                         END AS inv_outlet_name,\n", "om.facility_id,\n", "pf.internal_facility_identifier AS facility_name,\n", "wf.name AS warehouse_name,\n", "bt.name AS business_type_name\n", "FROM lake_po.physical_facility_outlet_mapping om\n", "LEFT JOIN\n", "  (SELECT id,\n", "          name,\n", "          facility_id,\n", "          CASE\n", "              WHEN id = 581 THEN 12\n", "              ELSE business_type_id\n", "          END AS business_type_id\n", "   FROM lake_retail.console_outlet) rco ON rco.id = om.outlet_id\n", "LEFT JOIN lake_retail.console_business_type bt ON bt.id = rco.business_type_id\n", "LEFT JOIN lake_po.physical_facility pf ON pf.facility_id = om.facility_id\n", "LEFT JOIN lake_retail.warehouse_facility wf ON wf.id = om.facility_id\n", "LEFT JOIN lake_retail.warehouse_outlet_mapping wom ON wom.warehouse_id = om.outlet_id\n", "LEFT JOIN lake_retail.console_outlet rco2 ON rco2.id = wom.cloud_store_id\n", "WHERE om.active = 1\n", "  AND om.ars_active = 1\n", "ORDER BY ZONE,\n", "         om.facility_id\"\"\"\n", "\n", "ars_active_outlet = read_sql_query(sql=ars_active_outlet_query, con=CON_REDSHIFT)\n", "ars_active_outlet.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ars_active_facilities = \", \".join(map(str, list(ars_active_outlet.facility_id.unique()))).replace(\n", "    \", nan\", \"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting Overall Inventory\n", "query = f\"\"\"\n", "SELECT base.facility_id, base.item_id, case when master_assortment_substate_id = 1 then 'active' else 'in-active' end as assortment_status\n", "FROM\n", "(SELECT a.item_id, \n", "                a.outlet_id, facility_id,\n", "                CASE\n", "                    WHEN sum(quantity) > 0 THEN sum(quantity)\n", "                    ELSE 0\n", "                END AS available_quantity\n", "FROM lake_view_ims.ims_item_inventory AS a\n", "INNER JOIN\n", "  (SELECT item_id,\n", "          outlet_id,\n", "          max(updated_at)AS update_at\n", "   FROM lake_view_ims.ims_item_inventory\n", "   GROUP BY 1,\n", "            2) AS b ON a.item_id=b.item_id\n", "AND a.outlet_id=b.outlet_id\n", "AND a.updated_at=b.update_at\n", "\n", "LEFT JOIN lake_view_retail.console_outlet co on co.id = a.outlet_id\n", "\n", "GROUP BY 1,\n", "         2,3) base\n", "\n", "\n", "LEFT JOIN lake_view_rpc.product_facility_master_assortment ma on ma.facility_id = base.facility_id and ma.item_id = base.item_id\n", "\n", "group by 1,2,3\n", "\"\"\"\n", "\n", "inventory_ma = read_sql_query(query, CON_PRESTO)\n", "# check_level(inventory_ma,['item_id', 'facility_id'],\"inventory_ma\")\n", "inventory_ma.head(2)\n", "\n", "inventory_ma = inventory_ma[inventory_ma.facility_id.isna() == False]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_ma.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting Overall Inventory\n", "query = f\"\"\"\n", "SELECT a.item_id, a.outlet_id,\n", "       CASE WHEN sum(a.quantity) > 0 THEN sum(a.quantity) ELSE 0 END AS available_quantity, blocked_quantity\n", "FROM lake_view_ims.ims_item_inventory AS a\n", "LEFT JOIN\n", "  (SELECT item_id, outlet_id, CASE WHEN blocked_quantity < 0 THEN 0 ELSE blocked_quantity END AS blocked_quantity\n", "   FROM\n", "     (SELECT item_id, i.outlet_id, sum(quantity) AS blocked_quantity\n", "      FROM lake_view_ims.ims_item_blocked_inventory i\n", "      WHERE blocked_type IN (1, 2, 4, 5)\n", "        AND i.active = 1\n", "      GROUP BY 1, 2) t1) iibi ON a.item_id = iibi.item_id\n", "AND a.outlet_id = iibi.outlet_id\n", "INNER JOIN\n", "  (SELECT item_id, outlet_id, max(updated_at)AS update_at\n", "   FROM lake_view_ims.ims_item_inventory\n", "   GROUP BY 1, 2) AS b ON a.item_id=b.item_id\n", "AND a.outlet_id=b.outlet_id\n", "AND a.updated_at=b.update_at\n", "GROUP BY 1, 2, blocked_quantity\n", "\"\"\"\n", "\n", "\n", "total_inventory = read_sql_query(query, CON_PRESTO)\n", "check_level(total_inventory, [\"item_id\", \"outlet_id\"], \"total_inventory\")\n", "# total_inventory.head(2)\n", "\n", "# Joining blocked and unblocked inventory\n", "inv_data = total_inventory.copy()\n", "inv_data = inv_data.drop_duplicates().fillna(0)\n", "inv_data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Parameters\n", "\n", "inv_item_list = list(total_inventory[\"item_id\"].unique())\n", "inv_item_id_list = tuple(inv_item_list)\n", "\n", "inv_outlet_list = list(total_inventory[\"outlet_id\"].unique())\n", "inv_outlet_id_list = tuple(inv_outlet_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["console_outlet_query = f\"\"\"\n", "select distinct facility_id, id as outlet_id from lake_view_retail.console_outlet where business_type_id in (1,12,7)\n", "\"\"\"\n", "\n", "console_outlet = read_sql_query(sql=console_outlet_query, con=CON_PRESTO)\n", "check_level(console_outlet, [\"outlet_id\"], \"console_outlet\")\n", "\n", "console_outlet_mapping = console_outlet.copy()\n", "console_outlet = console_outlet[[\"facility_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Update this to be at vendor level - (hence, item facility combination will have vendor as well) - completed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buffer_doi_query = f\"\"\"\n", "SELECT base.vendor_id, base.item_id, base.outlet_id, fill_rate_buffer_doi, facility_id, base.updated_at\n", "FROM lake_view_vms.iov_fill_rate_buffer_doi base\n", "INNER JOIN\n", "  (SELECT item_id, outlet_id, vendor_id, max(updated_at) AS updated_at\n", "   FROM lake_view_vms.iov_fill_rate_buffer_doi\n", "   GROUP BY 1,\n", "            2, 3) latest_data ON base.item_id = latest_data.item_id\n", "AND base.outlet_id = latest_data.outlet_id\n", "AND base.updated_at = latest_data.updated_at\n", "AND base.vendor_id = latest_data.vendor_id\n", "LEFT JOIN (SELECT distinct facility_id, id from lake_view_retail.console_outlet where active = 1) fac_mapping ON fac_mapping.id = base.outlet_id\n", "\n", "GROUP BY 1,2,3,4,5,6\n", "\"\"\"\n", "\n", "buffer_doi = read_sql_query(sql=buffer_doi_query, con=CON_PRESTO)\n", "# check_level(buffer_doi,['outlet_id','item_id'],\"buffer_doi\")\n", "buffer_doi.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buffer_facility_updated = (\n", "    buffer_doi.groupby([\"facility_id\", \"item_id\", \"vendor_id\"])\n", "    .agg({\"updated_at\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "buffer_facility_id = pd.merge(\n", "    buffer_doi,\n", "    buffer_facility_updated,\n", "    on=[\"facility_id\", \"item_id\", \"updated_at\", \"vendor_id\"],\n", "    how=\"inner\",\n", ")\n", "buffer_facility_id = buffer_facility_id[\n", "    [\"facility_id\", \"item_id\", \"vendor_id\", \"fill_rate_buffer_doi\"]\n", "].drop_duplicates()\n", "\n", "# check_level(\n", "#     buffer_facility_id, [\"facility_id\", \"item_id\", \"vendor_id\"], \"buffer_facility_id\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_tat_query = f\"\"\"\n", "SELECT base.item_id, base.outlet_id, base.vendor_id, base.tat_days, base.updated_at, facility_id\n", "FROM lake_view_po.item_outlet_vendor_tat base\n", "LEFT JOIN\n", "  (SELECT item_id, outlet_id, vendor_id, max(updated_at) AS updated_at\n", "   FROM lake_view_po.item_outlet_vendor_tat\n", "   GROUP BY 1, 2, 3) latest_data ON latest_data.item_id = base.item_id\n", "AND latest_data.outlet_id = base.outlet_id\n", "AND latest_data.updated_at = base.updated_at\n", "AND latest_data.vendor_id = base.vendor_id\n", "LEFT JOIN\n", "  (SELECT id, facility_id\n", "   FROM lake_view_retail.console_outlet\n", "   WHERE active = 1\n", "   GROUP BY 1, 2) fac_mapping ON fac_mapping.id = base.outlet_id\n", "\"\"\"\n", "\n", "vendor_tat = read_sql_query(sql=vendor_tat_query, con=CON_PRESTO)\n", "# check_level(vendor_tat,['outlet_id','item_id'],\"vendor_tat\")\n", "vendor_tat.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_tat_facility_updated = (\n", "    vendor_tat.groupby([\"facility_id\", \"item_id\", \"vendor_id\"])\n", "    .agg({\"updated_at\": \"max\"})\n", "    .reset_index()\n", ")\n", "\n", "vendor_tat_facility_id = pd.merge(\n", "    vendor_tat,\n", "    vendor_tat_facility_updated,\n", "    on=[\"facility_id\", \"item_id\", \"updated_at\", \"vendor_id\"],\n", "    how=\"inner\",\n", ")\n", "vendor_tat_facility_id = vendor_tat_facility_id[\n", "    [\"facility_id\", \"item_id\", \"vendor_id\", \"tat_days\"]\n", "].drop_duplicates()\n", "vendor_tat_facility_id = vendor_tat_facility_id.rename(columns={\"tat_days\": \"vendor_tat\"})\n", "check_level(\n", "    vendor_tat_facility_id,\n", "    [\"facility_id\", \"item_id\", \"vendor_id\"],\n", "    \"vendor_tat_facility_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_mapping_query = f\"\"\"\n", "    select facility_id, item_id, vendor_id, vendor_name \n", "    from (\n", "        select\n", "        a.facility_id,\n", "        item_id,\n", "        vendor_id,\n", "        c.vendor_name,\n", "        row_number() over (partition by a.facility_id, item_id order by a.updated_at desc) as row_rank\n", "        from  \n", "        lake_vms.vms_vendor_facility_alignment a\n", "        left join lake_vms.vms_vendor c on a.vendor_id = c.id\n", "        where a.active = true\n", "        and a.facility_id in ({ars_active_facilities})\n", "        )\n", "    where row_rank = 1\n", "    group by 1,2,3,4\"\"\"\n", "\n", "vendor_mapping = read_sql_query(sql=vendor_mapping_query, con=CON_REDSHIFT)\n", "check_level(vendor_mapping, [\"facility_id\", \"item_id\"], \"vendor_mapping\")\n", "vendor_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores_query = f\"\"\"\n", "    select facility_id\n", "    from lake_retail.console_outlet\n", "    where business_type_id = 7\n", "    group by 1\"\"\"\n", "\n", "dark_stores = read_sql_query(sql=dark_stores_query, con=CON_REDSHIFT)\n", "check_level(dark_stores, [\"facility_id\"], \"dark_stores\")\n", "dark_stores.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_darkstores = \", \".join(map(str, list(dark_stores.facility_id.unique()))).replace(\", nan\", \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_query = f\"\"\"WITH vendor_name_tbl AS\n", "  (SELECT facility_id,\n", "          item_id,\n", "          vendor_id,\n", "          vendor_name\n", "   FROM\n", "     (SELECT a.facility_id,\n", "             item_id,\n", "             vendor_id,\n", "             c.vendor_name,\n", "             row_number() OVER (PARTITION BY a.facility_id,\n", "                                             item_id\n", "                                ORDER BY a.updated_at DESC) AS row_rank\n", "      FROM lake_vms.vms_vendor_facility_alignment a\n", "      LEFT JOIN lake_vms.vms_vendor c ON a.vendor_id = c.id\n", "      WHERE a.active = TRUE AND \n", "        facility_id in ({ars_active_facilities}))\n", "   WHERE row_rank=1),\n", "     po_cycle AS\n", "  (SELECT *\n", "   FROM\n", "     (SELECT facility_id,\n", "             vendor_id,\n", "             a.manufacturer_id,\n", "             manufacturer_name,\n", "             po_days,\n", "             po_cycle,\n", "             pt.identifier AS po_cycle_type,\n", "             row_number() OVER (PARTITION BY facility_id,\n", "                                             vendor_id\n", "                                ORDER BY a.updated_at DESC) AS row_rank\n", "      FROM lake_vms.vendor_manufacturer_physical_facility_attributes a\n", "      LEFT JOIN lake_vms.po_cycle_types pt ON pt.id=a.po_cycle_type_id\n", "      LEFT JOIN lake_vms.vms_manufacturer_contact_details man ON man.manufacturer_id=a.manufacturer_id\n", "      WHERE facility_id in ({ars_active_facilities})\n", "        AND a.active=TRUE\n", "      ORDER BY 1,\n", "               2,\n", "               7)\n", "   WHERE row_rank=1 ),\n", "     LOAD AS\n", "  (SELECT *\n", "   FROM\n", "     (SELECT facility_id,\n", "             vendor_id,\n", "             load_type,\n", "             load_size,\n", "             active,\n", "             row_number() OVER (PARTITION BY facility_id,\n", "                                             vendor_id\n", "                                ORDER BY updated_at DESC) AS row_rank\n", "      FROM lake_vms.vendor_physical_facility_attributes\n", "      WHERE facility_id in ({ars_active_facilities})\n", "        AND active=TRUE) x1\n", "   WHERE row_rank=1)\n", "SELECT vendor.*,\n", "       po.manufacturer_name,\n", "       po.po_days,\n", "       po.po_cycle,\n", "       po.po_cycle_type\n", "FROM vendor_name_tbl vendor\n", "LEFT JOIN po_cycle po ON vendor.facility_id=po.facility_id\n", "AND vendor.vendor_id=po.vendor_id\n", "LEFT JOIN LOAD l ON vendor.facility_id=l.facility_id\n", "AND vendor.vendor_id=l.vendor_id\"\"\"\n", "po_cycle = read_sql_query(sql=po_cycle_query, con=CON_REDSHIFT)\n", "# po_cycle.rename(columns={\"facility_id\":\"backend_facility\"},inplace=True)\n", "po_cycle.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# --item_id in ({missing_lp_item}) and facility_id in ({missing_lp_facility})\n", "lp_query = f\"\"\"select distinct\n", "    m.item_id,\n", "    m.facility_id as facility_id,\n", "    case when m.landing_price is null or m.landing_price = 0 then lp.landing_rate else m.landing_price end as lp\n", "    \n", "        from (select item_id, facility_id, landing_price, updated_at from lake_rpc.tot_margin \n", "        group by 1,2,3,4) m\n", "        \n", "        join ( select item_id, facility_id , max(updated_at) as updated_at from lake_rpc.tot_margin\n", "        group by 1,2 ) m2 on m2.item_id = m.item_id and m2.facility_id = m.facility_id and m2.updated_at = m.updated_at\n", "        \n", "            left join (select\n", "            poi.item_id,\n", "            rco.facility_id,\n", "            landing_rate\n", "            \n", "                from lake_po.purchase_order_items poi\n", "                \n", "                left join lake_po.purchase_order po on po.id = poi.po_id\n", "                left join lake_retail.console_outlet rco on rco.id = po.outlet_id\n", "                \n", "                    join (select item_id, rco.facility_id, max(poi.updated_at) as updated_at from lake_po.purchase_order_items poi\n", "                    left join lake_po.purchase_order po on po.id = poi.po_id\n", "                    left join lake_retail.console_outlet rco on rco.id = po.outlet_id\n", "                    group by 1,2) poi2 on poi2.item_id = poi.item_id and poi2.facility_id = rco.facility_id and poi2.updated_at = poi.updated_at\n", "                    ) lp on lp.item_id = m.item_id and lp.facility_id = m.facility_id \n", "                    \"\"\"\n", "lp = read_sql_query(sql=lp_query, con=CON_REDSHIFT)\n", "check_level(lp, [\"facility_id\", \"item_id\"], \"lp\")\n", "lp = lp[~lp.lp.isna()]\n", "lp.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uom_mapping_query = f\"\"\"WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.UNIT AS Unit\n", "   from lake_cms.gr_product P\n", "   GROUP BY 1,2\n", "   ) \n", "\n", "SELECT rpc.item_id,\n", "       cat.unit as UOM\n", "from  lake_rpc.item_product_mapping  rpc\n", "inner join (select distinct item_id, brand_id from lake_rpc.product_product) rpb on rpb.item_id = rpc.item_id\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "group by 1,2\"\"\"\n", "\n", "uom_mapping = read_sql_query(sql=uom_mapping_query, con=CON_REDSHIFT)\n", "check_level(uom_mapping, [\"item_id\", \"uom\"], \"uom_mapping\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fill_rate_buffer_default_query = f\"\"\"select identifier as po_cycle_type, fill_rate_buffer, default_days\n", "from lake_vms.po_cycle_types\n", "group by 1,2,3\"\"\"\n", "\n", "fill_rate_buffer_default = read_sql_query(sql=fill_rate_buffer_default_query, con=CON_REDSHIFT)\n", "check_level(fill_rate_buffer_default, [\"po_cycle_type\"], \"fill_rate_buffer_default\")\n", "fill_rate_buffer_default.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["threshold_doi_query = f\"\"\"SELECT facility_id, item_id, threshold_doi, updated_at\n", "FROM lake_po.outlet_item_threshold_doi base\n", "LEFT JOIN\n", "  (SELECT id, facility_id\n", "   FROM lake_retail.console_outlet\n", "   WHERE active = 1\n", "   GROUP BY 1,\n", "            2) AS fac_map ON base.outlet_id = fac_map.id\n", "WHERE active = 1\n", "GROUP BY 1,2,3,4\"\"\"\n", "\n", "threshold_doi = read_sql_query(sql=threshold_doi_query, con=CON_REDSHIFT)\n", "\n", "# Remove duplicates\n", "threshold_doi_latest = (\n", "    threshold_doi.groupby([\"facility_id\", \"item_id\"]).agg({\"updated_at\": \"max\"}).reset_index()\n", ")\n", "threshold_doi = pd.merge(\n", "    threshold_doi,\n", "    threshold_doi_latest,\n", "    on=[\"facility_id\", \"item_id\", \"updated_at\"],\n", "    how=\"inner\",\n", ")\n", "threshold_doi = threshold_doi[[\"facility_id\", \"item_id\", \"threshold_doi\"]]\n", "check_level(threshold_doi, [\"facility_id\", \"item_id\"], \"threshold_doi\")\n", "threshold_doi = (\n", "    threshold_doi.groupby([\"facility_id\", \"item_id\"]).agg({\"threshold_doi\": \"max\"}).reset_index()\n", ")\n", "threshold_doi.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_facility = pd.concat([ars_active_outlet[[\"facility_id\"]].drop_duplicates(), dark_stores])\n", "all_facility_list = \", \".join(map(str, list(all_facility.facility_id.unique()))).replace(\n", "    \", nan\", \"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_query = f\"\"\"select facility_id, item_id, avg(consumption) as cpd \n", "from lake_snorlax.date_wise_consumption base\n", "left join (select id, facility_id from lake_retail.console_outlet where active = 1 group by 1,2) fac_mapping\n", "on base.outlet_id = fac_mapping.id\n", "where date between current_date and dateadd(day, 14, current_date) and active = 1\n", "and facility_id in ({all_facility_list})\n", "group by 1,2\"\"\"\n", "\n", "cpd = read_sql_query(sql=cpd_query, con=CON_REDSHIFT)\n", "\n", "# Remove duplicates\n", "\n", "check_level(cpd, [\"facility_id\", \"item_id\"], \"cpd\")\n", "cpd.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["default_cpd_query = f\"\"\"select facility_id, base.item_id, max(cpd) as cpd\n", "from lake_snorlax.default_cpd base\n", "inner join\n", "(select outlet_id, item_id, max(updated_at) as updated_at\n", "from lake_snorlax.default_cpd\n", "group by 1,2) latest\n", "on base.outlet_id = latest.outlet_id and base.item_id = latest.item_id and base.updated_at = latest.updated_at\n", "left join lake_retail.console_outlet as fac_mapping on fac_mapping.active = 1 and fac_mapping.id = base.outlet_id\n", "and facility_id in ({all_facility_list})\n", "group by 1,2\"\"\"\n", "\n", "default_cpd = read_sql_query(sql=default_cpd_query, con=CON_REDSHIFT)\n", "default_cpd = default_cpd[default_cpd.facility_id.isna() == False]\n", "# Remove duplicates\n", "\n", "# check_level(default_cpd, [\"facility_id\", \"item_id\"], \"default_cpd\")\n", "default_cpd.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_mapping_query = f\"\"\"\n", "with zone_tbl_treatment as\n", "(\n", "select\n", "city_name,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", ")\n", "\n", "\n", "select facility_id, city_id, t2.city_name, zone\n", "from (\n", "select *, rank() over (partition by facility_id order by outlet_count desc) as tank\n", "from (\n", "select o.facility_id as facility_id,  o.tax_location_id AS city_id, l.name AS city_name, count(o.id) as outlet_count\n", "from lake_retail.console_outlet o\n", "left join lake_retail.console_location l ON o.tax_location_id = l.id\n", "where active = 1 and o.facility_id in ({all_facility_list})\n", "group by 1,2,3\n", "order by 4 desc)t1 ) t2\n", "left join zone_tbl_treatment as zone on t2.city_name = zone.city_name\n", "\n", "where tank = 1\"\"\"\n", "\n", "city_mapping = read_sql_query(sql=city_mapping_query, con=CON_REDSHIFT)\n", "\n", "# Remove duplicates\n", "\n", "check_level(city_mapping, [\"facility_id\"], \"city_mapping\")\n", "city_mapping.head(1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["transfer_tat_query = f\"\"\"\n", "select \n", "bom.fe_outlet_id, bom.fe_outlet_name, bom.fe_facility_id, pf.internal_facility_identifier as fe_facility_name,\n", "bom.fe_business_type, tea_skus_count,\n", "pom.be_outlet_id, pom.be_outlet_name, bom.be_facility_id, pf2.internal_facility_identifier as be_facility_name,\n", "bt.name as be_business_type,\n", "td.transfer_days, td.case_sensitivity_type, td.transfer_tat,\n", "bom.updated_at,\n", "bom.updated_by\n", "\n", "from\n", "    (select\n", "    bom.facility_id as be_facility_id,\n", "    outlet_id as fe_outlet_id,\n", "    outlet_name as fe_outlet_name,\n", "    rco.facility_id as fe_facility_id,\n", "    bt.name as fe_business_type,\n", "    case when outlet_name like '%%Cold%%' then 2 else 1 end as storage_check,\n", "    bom.updated_at,\n", "    concat(first_name, ' ',last_name) as updated_by\n", "    \n", "        from lake_po.bulk_facility_outlet_mapping bom\n", "        \n", "            left join (select id, name, facility_id, \n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                from lake_retail.console_outlet) rco on rco.id = bom.outlet_id\n", "                    inner join lake_retail.console_business_type bt on bt.id = rco.business_type_id\n", "            \n", "            left join lake_retail.auth_user au on au.id = bom.updated_by\n", "            \n", "                where bom.active = 1) bom\n", "            \n", "left join\n", "        (select\n", "        facility_id as be_facility_id,\n", "        outlet_id as be_outlet_id,\n", "        outlet_name be_outlet_name,\n", "        case when outlet_name like '%%Cold%%' then 2 else 1 end as storage_check\n", "        \n", "            from lake_po.physical_facility_outlet_mapping\n", "            \n", "                where active = 1 and ars_active = 1 and is_primary = 1) pom on pom.be_facility_id = bom.be_facility_id and pom.storage_check = bom.storage_check\n", "            \n", "left join lake_po.bulk_facility_transfer_days td on td.backend_outlet_id = pom.be_outlet_id and td.frontend_outlet_id = bom.fe_outlet_id\n", "\n", "left join lake_po.physical_facility pf on pf.facility_id = bom.fe_facility_id\n", "    \n", "left join lake_po.physical_facility pf2 on pf2.facility_id = bom.be_facility_id\n", "\n", "left join (select id, \n", "    case when id = 581 then 12 else business_type_id end as business_type_id\n", "    from lake_retail.console_outlet) rco on rco.id = pom.be_outlet_id\n", "        left join lake_retail.console_business_type bt on bt.id = rco.business_type_id\n", "    \n", "left join\n", "    (select count(item_id) as tea_skus_count, outlet_id, tag_value \n", "        from lake_rpc.item_outlet_tag_mapping where tag_type_id = 8 and active = 1\n", "            group by 2,3) tm on tm.outlet_id = bom.fe_outlet_id and tm.tag_value = pom.be_outlet_id\n", "        \n", "    where pom.be_outlet_id is not null\n", "    -- and bom.be_facility_id = 483\n", "    \n", "    order by pf2.internal_facility_identifier, pf.internal_facility_identifier\"\"\"\n", "\n", "transfer_tat_0 = read_sql_query(sql=transfer_tat_query, con=CON_REDSHIFT)\n", "transfer_tat_0.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_tat_query = f\"\"\"\n", "SELECT distinct be_facility_id, be_facility_name, fe_facility_id, fe_facility_name, transfer_tat\n", "FROM(\n", "SELECT \n", "    base.backend_outlet_id, co_back.facility_id as be_facility_id, pf.internal_facility_identifier as be_facility_name,\n", "    base.frontend_outlet_id, co_front.facility_id as fe_facility_id, pf2.internal_facility_identifier as fe_facility_name,\n", "    transfer_tat\n", "FROM lake_po.bulk_facility_transfer_days base\n", "INNER JOIN \n", "(\n", "    SELECT backend_outlet_id, frontend_outlet_id, max(updated_at) as updated_at\n", "    FROM lake_po.bulk_facility_transfer_days\n", "    GROUP BY 1,2\n", ") td_max\n", "ON base.backend_outlet_id = td_max.backend_outlet_id and base.frontend_outlet_id = td_max.frontend_outlet_id and base.updated_at = td_max.updated_at\n", "LEFT JOIN lake_retail.console_outlet co_back on base.backend_outlet_id = co_back.id and co_back.active = 1\n", "LEFT JOIN lake_retail.console_outlet co_front on base.backend_outlet_id = co_front.id and co_front.active = 1\n", "left join lake_po.physical_facility pf on pf.facility_id = co_back.facility_id\n", "left join lake_po.physical_facility pf2 on pf2.facility_id = co_front.facility_id\n", ")\n", "\"\"\"\n", "\n", "transfer_tat_0 = read_sql_query(sql=transfer_tat_query, con=CON_REDSHIFT)\n", "transfer_tat_0.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    transfer_tat_0[\n", "        [\n", "            \"fe_facility_id\",\n", "            \"fe_facility_name\",\n", "            \"be_facility_id\",\n", "            \"be_facility_name\",\n", "            \"transfer_tat\",\n", "        ]\n", "    ].shape[0],\n", "    transfer_tat_0[\n", "        [\n", "            \"fe_facility_id\",\n", "            \"fe_facility_name\",\n", "            \"be_facility_id\",\n", "            \"be_facility_name\",\n", "            \"transfer_tat\",\n", "        ]\n", "    ]\n", "    .drop_duplicates()\n", "    .shape[0],\n", ")\n", "transfer_tat = transfer_tat_0[\n", "    [\n", "        \"fe_facility_id\",\n", "        \"fe_facility_name\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"transfer_tat\",\n", "    ]\n", "].drop_duplicates()\n", "transfer_tat.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_tat.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_mapping_query = f\"\"\"SELECT DISTINCT item_id,\n", "                rco.facility_id as physical_facility_id,\n", "                pf.internal_facility_identifier AS frontend_name,\n", "                tag_value AS ordering_outlet,\n", "                rcob.name AS ordering_outlet_name,\n", "                rcob.facility_id as backend_facility_id\n", "FROM lake_rpc.item_outlet_tag_mapping tm\n", "LEFT JOIN lake_retail.console_outlet rco ON rco.id = tm.outlet_id\n", "INNER JOIN lake_po.physical_facility pf ON pf.facility_id = rco.facility_id\n", "LEFT JOIN lake_retail.console_outlet rcob ON rcob.id = tm.tag_value\n", "INNER JOIN lake_po.physical_facility pfb ON pfb.facility_id = rcob.facility_id\n", "WHERE tag_type_id = 8 and\n", "tm.active = 1 --and frontend_name not in ('Super Store Chennai C2 - Warehouse','Kolkata_ES2')\n", "ORDER BY pf.internal_facility_identifier ASC\"\"\"\n", "tea_mapping = read_sql_query(sql=tea_mapping_query, con=CON_REDSHIFT)\n", "tea_mapping = tea_mapping[\n", "    [\"physical_facility_id\", \"item_id\", \"backend_facility_id\"]\n", "].drop_duplicates()\n", "\n", "variable = (\n", "    tea_mapping.groupby([\"physical_facility_id\", \"item_id\"])\n", "    .agg({\"backend_facility_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "variable = variable[variable.backend_facility_id > 1]\n", "variable[\"key\"] = (\n", "    variable[\"physical_facility_id\"].astype(str) + \"_\" + variable[\"item_id\"].astype(str)\n", ")\n", "variable = variable[[\"key\"]]\n", "\n", "tea_mapping[\"o_key\"] = (\n", "    tea_mapping[\"physical_facility_id\"].astype(str) + \"_\" + tea_mapping[\"item_id\"].astype(str)\n", ")\n", "\n", "tea_mapping_temp = pd.merge(tea_mapping, variable, left_on=[\"o_key\"], right_on=[\"key\"], how=\"left\")\n", "\n", "tea_mapping_non_dup = tea_mapping_temp[tea_mapping_temp.key.isna()]\n", "tea_mapping_dup = tea_mapping_temp[~tea_mapping_temp.key.isna()]\n", "\n", "duplicate_set = tea_mapping_dup[\n", "    [\"physical_facility_id\", \"backend_facility_id\"]\n", "].drop_duplicates()  # 517 for 428, 299?,  140?\n", "\n", "req_back = (\n", "    tea_mapping_non_dup.groupby([\"physical_facility_id\", \"backend_facility_id\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "# req_back[req_back.facility_id.isin([428, 299, 140])]\n", "\n", "req_back = pd.merge(\n", "    req_back,\n", "    duplicate_set,\n", "    on=[\"physical_facility_id\", \"backend_facility_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "req_back[\"rank\"] = req_back.groupby([\"physical_facility_id\"])[\"item_id\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "req_back = req_back[req_back[\"rank\"] == 1]\n", "req_back = req_back[[\"physical_facility_id\", \"backend_facility_id\"]].drop_duplicates()\n", "\n", "tea_mapping_dup = pd.merge(\n", "    tea_mapping_dup,\n", "    req_back,\n", "    on=[\"physical_facility_id\", \"backend_facility_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "tea_mapping = pd.concat([tea_mapping_dup, tea_mapping_non_dup])\n", "tea_mapping = tea_mapping.drop(columns={\"o_key\", \"key\"})\n", "check_level(tea_mapping, [\"physical_facility_id\", \"item_id\"], \"tea_mapping\")\n", "\n", "tea_mapping = tea_mapping.rename(columns={\"physical_facility_id\": \"facility_id\"})\n", "tea_mapping.head(2)"]}, {"cell_type": "raw", "metadata": {}, "source": ["ma_add_po.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Merge MA with PO Cycle\n", "\n", "# base = pd.merge(total_inventory[['outlet_id','item_id']].drop_duplicates(), console_outlet, on = ['outlet_id'], how = 'inner')\n", "\n", "# base = base[['facility_id','item_id']].drop_duplicates()\n", "\n", "# ma_0 = pd.merge(base, master_assortment, on = ['facility_id','item_id'], how = 'left')\n", "\n", "# inventory_ma_copy = inventory_ma[inventory_ma.facility_id.isin([32,116,1144,42,63,24])]\n", "\n", "\n", "ma_add_po = pd.merge(inventory_ma, po_cycle, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "\n", "# Filter for business_type_id 1,12 -\n", "ma_add_po = pd.merge(ma_add_po, console_outlet, on=[\"facility_id\"], how=\"inner\")\n", "\n", "# Add City Mapping\n", "ma_add_po = pd.merge(ma_add_po, city_mapping, on=[\"facility_id\"], how=\"left\")\n", "\n", "ma_add_po = ma_add_po[\n", "    [\n", "        \"zone\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"assortment_status\",\n", "        \"vendor_id\",\n", "        \"vendor_name\",\n", "        \"manufacturer_name\",\n", "        \"po_days\",\n", "        \"po_cycle\",\n", "        \"po_cycle_type\",\n", "    ]\n", "]\n", "\n", "## What is to be done for NAs - ma_add_po[ma_add_po.vendor_id.isna()] ? # Minor reduction on adding in_active po_cycle data.\n", "print(ma_add_po[ma_add_po.vendor_id.isna()].head(1).shape[0])\n", "\n", "# Add landing Price\n", "ma_add_lp = pd.merge(ma_add_po, lp, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "ma_add_lp.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ma_add_lp[ma_add_lp.lp.isna()] # Need to remove dark stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["missing_lp_facility = ma_add_lp[ma_add_lp.lp.isna()][\"facility_id\"].unique()\n", "missing_lp_facility = \", \".join(map(str, list(missing_lp_facility))).replace(\", nan\", \"\")\n", "\n", "missing_lp_item = ma_add_lp[ma_add_lp.lp.isna()][\"item_id\"].unique()\n", "missing_lp_item = \", \".join(map(str, list(missing_lp_item))).replace(\", nan\", \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mrp_query = f\"\"\"SELECT rpc.item_id,\n", "       CASE\n", "           WHEN rpc.variant_mrp IS NULL THEN 100\n", "           ELSE rpc.variant_mrp\n", "       END AS mrp\n", "FROM lake_rpc.product_product rpc\n", "LEFT JOIN lake_rpc.product_brand pb ON pb.id = rpc.brand_id\n", "LEFT JOIN lake_rpc.product_manufacturer pm ON pm.id = pb.manufacturer_id\n", "WHERE rpc.id IN\n", "    (SELECT max(id) AS id\n", "     FROM lake_rpc.product_product pp\n", "     WHERE pp.active = 1\n", "       AND pp.approved = 1\n", "     GROUP BY item_id)\n", "  AND rpc.active = 1\n", "  AND rpc.approved = 1\n", "  AND rpc.outlet_type NOT IN (1)\n", "  AND rpc.item_id in ({missing_lp_item})\"\"\"\n", "mrp = read_sql_query(sql=mrp_query, con=CON_REDSHIFT)\n", "check_level(mrp, [\"item_id\"], \"mrp\")\n", "mrp = mrp[~mrp.mrp.isna()]\n", "mrp[\"lp\"] = mrp[\"mrp\"] * 0.75\n", "mrp = mrp.drop(columns={\"mrp\"})\n", "mrp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tea_mapping[tea_mapping.backend_facility_id == 1144]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add mrp * 0.75 to replace NA lp\n", "\n", "ma_add_lp_sub = pd.merge(\n", "    ma_add_lp, mrp.rename(columns={\"lp\": \"mrp_lp\"}), on=[\"item_id\"], how=\"left\"\n", ")\n", "ma_add_lp_sub[\"lp\"] = np.where(\n", "    ma_add_lp_sub[\"lp\"].isna(), ma_add_lp_sub[\"mrp_lp\"], ma_add_lp_sub[\"lp\"]\n", ")\n", "ma_add_lp_sub = ma_add_lp_sub.drop(columns={\"mrp_lp\"})\n", "\n", "print(\n", "    \"\\nMissing landing Price (lp): \",\n", "    ma_add_lp[ma_add_lp.lp.isna()].shape[0],\n", "    \", Missing lp after mrp*.75: \",\n", "    ma_add_lp_sub[ma_add_lp_sub.lp.isna()].shape[0],\n", ")\n", "\n", "\n", "# Add UOM for Items\n", "ma_add_uom = pd.merge(ma_add_lp_sub, uom_mapping, on=[\"item_id\"], how=\"left\")\n", "print(\"\\nUOM Missing: \", ma_add_uom[ma_add_uom.uom.isna()].shape[0])\n", "\n", "# Add TEA Mapping to identify if item is transfer SKU\n", "ma_add_transfer_sku = pd.merge(ma_add_uom, tea_mapping, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "ma_add_transfer_sku[\"is_transfer_sku\"] = np.where(\n", "    ma_add_transfer_sku[\"backend_facility_id\"].isna(), 0, 1\n", ")\n", "\n", "# Add buffer_doi\n", "ma_add_buffer_doi = pd.merge(\n", "    ma_add_transfer_sku,\n", "    buffer_facility_id,\n", "    on=[\"facility_id\", \"item_id\", \"vendor_id\"],\n", "    how=\"left\",\n", ")\n", "print(\n", "    \"\\nFill Rate Buffer DOI: \",\n", "    ma_add_buffer_doi[ma_add_buffer_doi.fill_rate_buffer_doi.isna()].shape[0],\n", "    \"\\n\",\n", ")\n", "\n", "\n", "# Add fillrate_buffer and default_days\n", "ma_add_buffer_default = pd.merge(\n", "    ma_add_buffer_doi, fill_rate_buffer_default, on=[\"po_cycle_type\"], how=\"left\"\n", ")\n", "ma_add_buffer_default[\"fill_rate_buffer_doi\"] = np.where(\n", "    ma_add_buffer_default[\"fill_rate_buffer_doi\"].isna(),\n", "    ma_add_buffer_default[\"fill_rate_buffer\"],\n", "    ma_add_buffer_default[\"fill_rate_buffer_doi\"],\n", ")\n", "\n", "# Calculate vendor_doi - add default_days and fill_rate_buffer_doi\n", "ma_add_buffer_default[\"vendor_doi\"] = (\n", "    ma_add_buffer_default[\"fill_rate_buffer_doi\"] + ma_add_buffer_default[\"default_days\"]\n", ")\n", "\n", "# Add vendor_tat\n", "ma_add_vendor_tat = pd.merge(\n", "    ma_add_buffer_default,\n", "    vendor_tat_facility_id,\n", "    on=[\"facility_id\", \"item_id\", \"vendor_id\"],\n", "    how=\"left\",\n", ")\n", "ma_add_vendor_tat.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ma_add_vendor_tat[ma_add_vendor_tat.facility_id == 32]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ma_add_vendor_tat[ma_add_vendor_tat.fill_rate_buffer_doi.isna() == True]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Pending Putaway -> Add to net inevntory | Completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting blocked inventory\n", "\n", "all_facilities = \", \".join(map(str, list(ma_add_vendor_tat.facility_id.unique()))).replace(\n", "    \", nan\", \"\"\n", ")\n", "all_items = \", \".join(map(str, list(ma_add_vendor_tat.item_id.unique()))).replace(\", nan\", \"\")\n", "\n", "# blocked = f\"\"\"select item_id ,\n", "#      i.outlet_id ,\n", "#      case when sum(quantity) > 0 then sum(quantity) else 0 end as blocked_quantity\n", "# FROM ims.ims_item_blocked_inventory i\n", "# where blocked_type in (1,2,4,5) and i.active = 1\n", "# group by 1,2\n", "# \"\"\"\n", "\n", "# block_quantity = read_sql_query(blocked, CON_RETAIL)\n", "# check_level(block_quantity,['item_id', 'outlet_id'],\"block_quantity\")\n", "# block_quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add transfer_tat\n", "ma_add_b2b_transfer_tat = pd.merge(\n", "    ma_add_vendor_tat,\n", "    transfer_tat.rename(\n", "        columns={\n", "            \"fe_facility_id\": \"facility_id\",\n", "            \"be_facility_id\": \"backend_facility_id\",\n", "            \"transfer_tat\": \"b2b_transfer_tat\",\n", "        }\n", "    ),\n", "    on=[\"facility_id\", \"backend_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "# Add b2b_transfer_doi\n", "ma_add_b2b_transfer_doi = pd.merge(\n", "    ma_add_b2b_transfer_tat,\n", "    threshold_doi.rename(columns={\"threshold_doi\": \"b2b_transfer_doi\"}),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "# Get unique facility and item combinations\n", "get_all_item_facility_combinations = ma_add_b2b_transfer_doi[\n", "    [\"facility_id\", \"item_id\"]\n", "].drop_duplicates()\n", "tea_ds = pd.merge(tea_mapping, dark_stores, on=[\"facility_id\"], how=\"inner\")\n", "tea_ds = tea_ds.rename(\n", "    columns={\"facility_id\": \"darkstores_mapped\", \"backend_facility_id\": \"facility_id\"}\n", ")\n", "\n", "ds_frontends = pd.merge(\n", "    get_all_item_facility_combinations,\n", "    tea_ds,\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "# Add Dark Store Transfer TAT & Threshold DOI\n", "ds_frontends_doi_added = pd.merge(\n", "    ds_frontends,\n", "    transfer_tat[[\"fe_facility_id\", \"be_facility_id\", \"transfer_tat\"]].rename(\n", "        columns={\n", "            \"fe_facility_id\": \"darkstores_mapped\",\n", "            \"be_facility_id\": \"facility_id\",\n", "            \"transfer_tat\": \"dark_store_transfer_tat\",\n", "        }\n", "    ),\n", "    on=[\"facility_id\", \"darkstores_mapped\"],\n", "    how=\"left\",\n", ")\n", "ds_frontends_doi_added = ds_frontends_doi_added[\n", "    ds_frontends_doi_added.darkstores_mapped.isna() == False\n", "]\n", "ds_frontends_threshold_added = pd.merge(\n", "    ds_frontends_doi_added,\n", "    threshold_doi.rename(\n", "        columns={\n", "            \"facility_id\": \"darkstores_mapped\",\n", "            \"threshold_doi\": \"dark_store_transfer_doi\",\n", "        }\n", "    ),\n", "    on=[\"darkstores_mapped\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "ds_frontends_threshold_added[\"dark_store_transfer_tat\"] = ds_frontends_threshold_added[\n", "    \"dark_store_transfer_tat\"\n", "].fillna(0)\n", "ds_frontends_threshold_added[\"dark_store_transfer_doi\"] = ds_frontends_threshold_added[\n", "    \"dark_store_transfer_doi\"\n", "].fillna(0)\n", "ds_frontends_avg = (\n", "    ds_frontends_threshold_added.groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"dark_store_transfer_tat\": \"mean\", \"dark_store_transfer_doi\": \"mean\"})\n", "    .reset_index()\n", ")\n", "\n", "# Add to base data\n", "ma_add_ds_transfer = pd.merge(\n", "    ma_add_b2b_transfer_doi, ds_frontends_avg, on=[\"facility_id\", \"item_id\"], how=\"left\"\n", ")\n", "\n", "\n", "# Add Inventory Data - backend (be), b2b (self), ds (darkstore, frontend)\n", "inv_mapped = pd.merge(inv_data, console_outlet_mapping, on=[\"outlet_id\"], how=\"left\")\n", "all_facility = pd.concat(\n", "    [ars_active_outlet[[\"facility_id\"]].drop_duplicates(), dark_stores]\n", ").drop_duplicates()\n", "\n", "inv_mapped = pd.merge(inv_mapped, all_facility, on=[\"facility_id\"], how=\"inner\")\n", "inv_mapped = (\n", "    inv_mapped.groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"available_quantity\": \"sum\", \"blocked_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "inv_mapped[\"unblocked_qty\"] = np.where(\n", "    (inv_mapped[\"available_quantity\"] - inv_mapped[\"blocked_quantity\"]) < 0,\n", "    0,\n", "    inv_mapped[\"available_quantity\"] - inv_mapped[\"blocked_quantity\"],\n", ")\n", "\n", "ma_be_inv_added = pd.merge(\n", "    ma_add_ds_transfer,\n", "    inv_mapped[[\"facility_id\", \"item_id\", \"unblocked_qty\"]].rename(\n", "        columns={\n", "            \"facility_id\": \"backend_facility_id\",\n", "            \"unblocked_qty\": \"be_inv_unb_qty\",\n", "        }\n", "    ),\n", "    on=[\"backend_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "ma_b2b_inv_added = pd.merge(\n", "    ma_be_inv_added,\n", "    inv_mapped[[\"facility_id\", \"item_id\", \"unblocked_qty\"]].rename(\n", "        columns={\"unblocked_qty\": \"b2b_inv_unb_qty\"}\n", "    ),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "ds_inv = pd.merge(\n", "    ds_frontends_threshold_added[[\"facility_id\", \"item_id\", \"darkstores_mapped\"]].drop_duplicates(),\n", "    inv_mapped[[\"facility_id\", \"item_id\", \"unblocked_qty\"]].rename(\n", "        columns={\n", "            \"facility_id\": \"darkstores_mapped\",\n", "            \"unblocked_qty\": \"ds_unblocked_qty\",\n", "        }\n", "    ),\n", "    on=[\"darkstores_mapped\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "ds_inv[\"ds_unblocked_qty\"] = ds_inv[\"ds_unblocked_qty\"].fillna(0)\n", "ds_inv_agg = (\n", "    ds_inv.groupby([\"facility_id\", \"item_id\"]).agg({\"ds_unblocked_qty\": \"sum\"}).reset_index()\n", ")\n", "\n", "ma_ds_inv_added = pd.merge(ma_b2b_inv_added, ds_inv_agg, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "\n", "# Add CPD - b2b, and DarkStore\n", "ma_b2b_cpd = pd.merge(\n", "    ma_ds_inv_added,\n", "    cpd.rename(columns={\"cpd\": \"b2b_cpd\"}),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "ds_cpd_0 = pd.merge(\n", "    cpd.rename(columns={\"facility_id\": \"darkstores_mapped\"}),\n", "    default_cpd.rename(columns={\"facility_id\": \"darkstores_mapped\"}),\n", "    on=[\"darkstores_mapped\", \"item_id\"],\n", "    how=\"left\",\n", ")  # .head(1)\n", "ds_cpd_0[\"cpd_x\"] = np.where(ds_cpd_0[\"cpd_x\"].isna(), ds_cpd_0[\"cpd_y\"], ds_cpd_0[\"cpd_x\"])\n", "ds_cpd_0 = ds_cpd_0.drop(columns={\"cpd_y\"})\n", "ds_cpd_0.columns = [\"darkstores_mapped\", \"item_id\", \"cpd\"]\n", "\n", "ds_cpd = pd.merge(\n", "    ds_frontends_threshold_added[[\"facility_id\", \"item_id\", \"darkstores_mapped\"]].drop_duplicates(),\n", "    ds_cpd_0,\n", "    on=[\"darkstores_mapped\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "ds_cpd[\"cpd\"] = ds_cpd[\"cpd\"].fillna(0)\n", "ds_cpd = (\n", "    ds_cpd.groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"cpd\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"cpd\": \"dark_store_cpd\"})\n", ")\n", "\n", "ma_ds_cpd = pd.merge(ma_b2b_cpd, ds_cpd, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "\n", "# Add Default CPD to replace NAs in b2b_cpd\n", "ma_default_cpd = pd.merge(ma_ds_cpd, default_cpd, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "ma_default_cpd[\"b2b_cpd\"] = np.where(\n", "    ma_default_cpd[\"b2b_cpd\"].isna(), ma_default_cpd[\"cpd\"], ma_default_cpd[\"b2b_cpd\"]\n", ")\n", "ma_default_cpd = ma_default_cpd.drop(columns={\"cpd\"})\n", "\n", "\n", "ma_default_cpd[\"dark_store_cpd\"] = ma_default_cpd[\"dark_store_cpd\"].fillna(0)\n", "ma_default_cpd[\"ds_unblocked_qty\"] = ma_default_cpd[\"ds_unblocked_qty\"].fillna(0)\n", "ma_default_cpd[\"b2b_cpd\"] = ma_default_cpd[\"b2b_cpd\"].fillna(0)\n", "ma_default_cpd[\"ds_unblocked_qty\"] = ma_default_cpd[\"ds_unblocked_qty\"].fillna(0)\n", "ma_default_cpd[\"b2b_inv_unb_qty\"] = ma_default_cpd[\"b2b_inv_unb_qty\"].fillna(0)\n", "ma_default_cpd[\"be_inv_unb_qty\"] = ma_default_cpd[\"be_inv_unb_qty\"].fillna(0)\n", "ma_default_cpd[\"dark_store_transfer_tat\"] = ma_default_cpd[\"dark_store_transfer_tat\"].fillna(0)\n", "ma_default_cpd[\"dark_store_transfer_doi\"] = ma_default_cpd[\"dark_store_transfer_doi\"].fillna(0)\n", "ma_default_cpd[\"b2b_transfer_doi\"] = ma_default_cpd[\"b2b_transfer_doi\"].fillna(0)\n", "ma_default_cpd[\"vendor_tat\"] = ma_default_cpd[\"vendor_tat\"].fillna(0)\n", "ma_default_cpd[\"vendor_doi\"] = ma_default_cpd[\"vendor_doi\"].fillna(0)\n", "ma_default_cpd[\"default_days\"] = ma_default_cpd[\"default_days\"].fillna(0)\n", "ma_default_cpd[\"fill_rate_buffer\"] = ma_default_cpd[\"fill_rate_buffer\"].fillna(0)\n", "ma_default_cpd[\"fill_rate_buffer_doi\"] = ma_default_cpd[\"fill_rate_buffer_doi\"].fillna(0)\n", "ma_default_cpd[\"b2b_transfer_doi\"] = ma_default_cpd[\"b2b_transfer_doi\"].fillna(0)\n", "ma_default_cpd[\"b2b_transfer_doi\"] = ma_default_cpd[\"b2b_transfer_doi\"].fillna(0)\n", "\n", "ma_default_cpd.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ma_default_cpd.to_csv('ma_default_cpd_filtered.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_default_cpd.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ma_default_cpd[ma_default_cpd.fill_rate_buffer_doi.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_facilities = \", \".join(map(str, list(ma_default_cpd.facility_id.unique()))).replace(\", nan\", \"\")\n", "all_items = \", \".join(map(str, list(ma_default_cpd.item_id.unique()))).replace(\", nan\", \"\")\n", "\n", "# Extracting open STOs\n", "sto_query = f\"\"\"\n", "SELECT facility_id,\n", "       item_id,\n", "       sum(quantity) AS open_sto\n", "FROM lake_view_ims.ims_sto_mapping_inventory base\n", "LEFT JOIN lake_view_retail.console_outlet fac_mapping on base.outlet_id = fac_mapping.id and fac_mapping.active = 1\n", "WHERE sto_type IN (1,2)\n", "  AND inventory_type IN (1,2)\n", "  AND facility_id in ({all_facilities})\n", "  AND item_id in ({all_items})\n", "GROUP BY 1,\n", "         2\"\"\"\n", "stos_grouped = read_sql_query(sql=sto_query, con=CON_PRESTO)\n", "check_level(stos_grouped, [\"facility_id\", \"item_id\"], \"stos_grouped\")\n", "stos_grouped.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ()\n", "\n", "# igi on igi.outlet_id = iii.outlet_id and igi.item_id = iii.item_id\n", "\n", "# Extracting open STOs\n", "pending_putaway_query = f\"\"\"\n", "select facility_id, rpc.item_id, sum(igi.quantity) as pending_putaway from lake_view_ims.ims_good_inventory igi\n", "left join lake_view_rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "left join lake_view_retail.console_outlet fac_map on outlet_id = fac_map.id\n", "where igi.inventory_update_type_id in (28,76) and igi.active = 1 and rpc.active = 1 and rpc.approved = 1 AND facility_id in ({all_facilities})\n", "  AND item_id in ({all_items})\n", "group by 1,2\"\"\"\n", "pending_putaway = read_sql_query(sql=pending_putaway_query, con=CON_PRESTO)\n", "check_level(pending_putaway, [\"facility_id\", \"item_id\"], \"pending_putaway\")\n", "pending_putaway.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting open POs\n", "open_PO_query = f\"\"\"\n", "SELECT facility_id, item_id, sum(expected_po_qty) as expected_po_qty\n", "FROM (\n", "    SELECT DISTINCT o.facility_id, date(issue_date) AS issue_date, date(expiry_date) AS expiry_date, \n", "    cast(ps.schedule_date_time + interval '330' Minute as date) as scheduled_date, poi.item_id, poi.units_ordered AS expected_po_qty\n", "    FROM lake_view_po.purchase_order p\n", "    LEFT JOIN lake_view_po.po_schedule ps ON p.id=ps.po_id_id\n", "    INNER JOIN lake_view_po.purchase_order_items poi ON p.id=poi.po_id\n", "    INNER JOIN lake_view_retail.console_outlet o ON o.id=p.outlet_id\n", "    INNER JOIN lake_view_po.purchase_order_status posa ON posa.po_id = p.id\n", "    INNER JOIN lake_view_po.purchase_order_state posta ON posta.id = posa.po_state_id\n", "    LEFT JOIN lake_view_po.po_grn grn ON p.id = grn.po_id\n", "    AND poi.item_id = grn.item_id\n", "    WHERE posta.name IN ('Created',\n", "                         'Scheduled',\n", "                         'Rescheduled',\n", "                         'Fulfilled')\n", "      AND grn.grn_id IS NULL\n", "      AND date(ps.schedule_date_time) = CURRENT_DATE\n", "      AND poi.item_id IN ({all_items})\n", "      AND o.facility_id IN ({all_facilities})\n", "      ) O1\n", "GROUP BY 1,2\"\"\"\n", "open_PO = read_sql_query(sql=open_PO_query, con=CON_PRESTO)\n", "check_level(open_PO, [\"facility_id\", \"item_id\"], \"open_PO\")\n", "open_PO.head(2)"]}, {"cell_type": "raw", "metadata": {}, "source": ["# Extracting open POs\n", "max_map_query = f\"\"\"\n", "SELECT backend_facility as facility_id, item_id, sum(max_qty) as max_qty\n", "FROM (\n", "SELECT base.backend_facility, base.frontend_outlet, base.item_id, max_qty\n", "FROM metrics.item_outlet_min_max_logs base\n", "INNER JOIN\n", "  (SELECT backend_facility, frontend_outlet, item_id, max(updated_at) AS updated_at\n", "   FROM metrics.item_outlet_min_max_logs\n", "   WHERE backend_facility in ({all_facilities}) AND item_id in ({all_items})\n", "   GROUP BY 1, 2, 3) latest_data ON base.backend_facility = latest_data.backend_facility\n", "AND base.frontend_outlet = latest_data.frontend_outlet\n", "AND base.item_id = latest_data.item_id\n", "AND base.updated_at = latest_data.updated_at\n", "WHERE base.backend_facility in ({all_facilities}) AND base.item_id in ({all_items})\n", ") T1\n", "GROUP BY 1,2\"\"\"\n", "max_map = read_sql_query(sql=max_map_query,con=CON_REDSHIFT)\n", "# check_level(max_qty,['facility_id','item_id'],\"max_qty\")\n", "# max_map['max_qty'] = max_map['max_qty'].apply(np.ceil)\n", "max_map.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting open POs\n", "max_qty_query = f\"\"\"\n", "select base.facility_id, base.item_id, sum(max_quantity) as max_qty\n", "from lake_view_ars.item_min_max_quantity base\n", "inner join\n", "(\n", "    select facility_id, item_id, max(updated_at) as updated_at\n", "    from lake_view_ars.item_min_max_quantity\n", "    WHERE facility_id in ({all_facilities}) AND item_id in ({all_items})\n", "    group by 1,2\n", ") latest\n", "on base.facility_id = latest.facility_id and base.item_id = latest.item_id and base.updated_at = latest.updated_at\n", "WHERE base.facility_id in ({all_facilities}) AND base.item_id in ({all_items})\n", "\n", "group by 1,2\"\"\"\n", "max_qty_0 = read_sql_query(sql=max_qty_query, con=CON_PRESTO)\n", "# check_level(max_qty,['facility_id','item_id'],\"max_qty\")\n", "# max_map['max_qty'] = max_map['max_qty'].apply(np.ceil)\n", "max_qty_0.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Extracting open POs\n", "# max_map_query = f\"\"\"\n", "# SELECT backend_facility, frontend_outlet, item_id, sum(max_qty) as max_qty\n", "# FROM (\n", "# SELECT base.backend_facility, base.frontend_outlet, base.item_id, max_qty\n", "# FROM metrics.item_outlet_min_max_logs base\n", "# INNER JOIN\n", "#   (SELECT backend_facility, frontend_outlet, item_id, max(updated_at) AS updated_at\n", "#    FROM metrics.item_outlet_min_max_logs\n", "#    WHERE backend_facility in ({all_facilities}) AND item_id in ({all_items})\n", "#    GROUP BY 1, 2, 3) latest_data ON base.backend_facility = latest_data.backend_facility\n", "# AND base.frontend_outlet = latest_data.frontend_outlet\n", "# AND base.item_id = latest_data.item_id\n", "# AND base.updated_at = latest_data.updated_at\n", "# WHERE base.backend_facility in ({all_facilities}) AND base.item_id in ({all_items})\n", "# ) T1\n", "# GROUP BY 1,2,3\"\"\"\n", "# max_map = read_sql_query(sql=max_map_query, con=CON_REDSHIFT)\n", "# # check_level(max_qty,['facility_id','item_id'],\"max_qty\")\n", "# # max_map['max_qty'] = max_map['max_qty'].apply(np.ceil)\n", "# max_map = max_map[[\"backend_facility\", \"frontend_outlet\", \"item_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# max_map.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["console_outlet_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# max_map = pd.merge(\n", "#     max_map,\n", "#     console_outlet_mapping.rename(columns={\"outlet_id\": \"frontend_outlet\"}),\n", "#     on=[\"frontend_outlet\"],\n", "#     how=\"left\",\n", "# )\n", "max_map = (\n", "    tea_mapping[[\"backend_facility_id\", \"facility_id\", \"item_id\"]]\n", "    .drop_duplicates()\n", "    .rename(columns={\"backend_facility_id\": \"backend_facility\"})\n", ")\n", "\n", "max_map = pd.merge(max_map, max_qty_0, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "max_qty = (\n", "    max_map.groupby([\"backend_facility\", \"item_id\"])\n", "    .agg({\"max_qty\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"backend_facility\": \"facility_id\"})\n", ")\n", "max_qty.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine Open STO and Open PO\n", "in_transit = pd.merge(stos_grouped, open_PO, on=[\"facility_id\", \"item_id\"], how=\"outer\")\n", "in_transit = pd.merge(in_transit, pending_putaway, on=[\"facility_id\", \"item_id\"], how=\"outer\")\n", "in_transit[\"open_sto\"] = in_transit[\"open_sto\"].fillna(0)\n", "in_transit[\"expected_po_qty\"] = in_transit[\"expected_po_qty\"].fillna(0)\n", "in_transit[\"pending_putaway\"] = in_transit[\"pending_putaway\"].fillna(0)\n", "\n", "# Add Open STO and Open PO\n", "ma_intransit = pd.merge(ma_default_cpd, in_transit, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "\n", "# Add Max\n", "ma_intransit = pd.merge(ma_intransit, max_qty, on=[\"facility_id\", \"item_id\"], how=\"left\")\n", "ma_intransit.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_intransit.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Calculations\n", "#'backend_facility_id',\n", "\n", "ma_intransit[\n", "    [\n", "        \"po_cycle\",\n", "        \"lp\",\n", "        \"uom\",\n", "        \"is_transfer_sku\",\n", "        \"fill_rate_buffer_doi\",\n", "        \"fill_rate_buffer\",\n", "        \"vendor_doi\",\n", "        \"vendor_tat\",\n", "        \"b2b_transfer_tat\",\n", "        \"b2b_transfer_doi\",\n", "        \"dark_store_transfer_tat\",\n", "        \"dark_store_transfer_doi\",\n", "        \"be_inv_unb_qty\",\n", "        \"b2b_inv_unb_qty\",\n", "        \"ds_unblocked_qty\",\n", "        \"b2b_cpd\",\n", "        \"dark_store_cpd\",\n", "        \"open_sto\",\n", "        \"expected_po_qty\",\n", "        \"pending_putaway\",\n", "    ]\n", "] = ma_intransit[\n", "    [\n", "        \"po_cycle\",\n", "        \"lp\",\n", "        \"uom\",\n", "        \"is_transfer_sku\",\n", "        \"fill_rate_buffer_doi\",\n", "        \"fill_rate_buffer\",\n", "        \"vendor_doi\",\n", "        \"vendor_tat\",\n", "        \"b2b_transfer_tat\",\n", "        \"b2b_transfer_doi\",\n", "        \"dark_store_transfer_tat\",\n", "        \"dark_store_transfer_doi\",\n", "        \"be_inv_unb_qty\",\n", "        \"b2b_inv_unb_qty\",\n", "        \"ds_unblocked_qty\",\n", "        \"b2b_cpd\",\n", "        \"dark_store_cpd\",\n", "        \"open_sto\",\n", "        \"expected_po_qty\",\n", "        \"pending_putaway\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "\n", "# Calculate Final Inventory - b2b_inv, dark_store_inv, open_sto, open PO\n", "ma_intransit[\"final_inv\"] = (\n", "    ma_intransit[\"b2b_inv_unb_qty\"]\n", "    + ma_intransit[\"ds_unblocked_qty\"]\n", "    + ma_intransit[\"open_sto\"]\n", "    + ma_intransit[\"expected_po_qty\"]\n", ")\n", "\n", "ma_intransit[\"final_doi_to_be_made\"] = np.where(\n", "    ma_intransit[\"is_transfer_sku\"] == 0,\n", "    ma_intransit[\"vendor_doi\"]\n", "    + ma_intransit[\"vendor_tat\"]\n", "    + ma_intransit[\"dark_store_transfer_doi\"]\n", "    + ma_intransit[\"dark_store_transfer_tat\"],\n", "    ma_intransit[\"b2b_transfer_doi\"]\n", "    + ma_intransit[\"b2b_transfer_tat\"]\n", "    + ma_intransit[\"dark_store_transfer_doi\"]\n", "    + ma_intransit[\"dark_store_transfer_tat\"],\n", ")\n", "\n", "ma_intransit[\"final_ordering_qty\"] = (\n", "    ma_intransit[\"final_doi_to_be_made\"]\n", "    * (ma_intransit[\"b2b_cpd\"] + ma_intransit[\"dark_store_cpd\"])\n", "    + ma_intransit[\"max_qty\"]\n", ")\n", "ma_intransit[\"b2b_doi\"] = np.where(\n", "    ma_intransit[\"b2b_cpd\"] <= 0,\n", "    0,\n", "    ma_intransit[\"b2b_inv_unb_qty\"] / ma_intransit[\"b2b_cpd\"],\n", ")\n", "\n", "ma_intransit[\"excess_inventory\"] = np.where(\n", "    (ma_intransit[\"final_inv\"] - ma_intransit[\"final_ordering_qty\"]) < 0,\n", "    0,\n", "    ma_intransit[\"final_inv\"] - ma_intransit[\"final_ordering_qty\"],\n", ")\n", "ma_intransit[\"shortfall_inventory\"] = np.where(\n", "    (ma_intransit[\"final_ordering_qty\"] - ma_intransit[\"final_inv\"]) < 0,\n", "    0,\n", "    ma_intransit[\"final_ordering_qty\"] - ma_intransit[\"final_inv\"],\n", ")\n", "\n", "ma_intransit.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ma_intransit[(ma_intransit['item_id'] == 10020343) & (ma_intransit['facility_id'] == 747)]\n", "\n", "ma_intransit[ma_intransit[\"b2b_doi\"] == np.inf]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# City Wise Calculations\n", "city_wise = (\n", "    ma_intransit.groupby([\"city_id\", \"city_name\", \"item_id\"])\n", "    .agg(\n", "        {\n", "            \"excess_inventory\": \"sum\",\n", "            \"shortfall_inventory\": \"sum\",\n", "            \"b2b_cpd\": \"sum\",\n", "            \"dark_store_cpd\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"excess_inventory\": \"city_wise_excess_inventory\",\n", "            \"shortfall_inventory\": \"city_wise_shortfall_inventory\",\n", "        }\n", "    )\n", ")\n", "city_wise[\"city_wise_cpd\"] = city_wise[\"b2b_cpd\"] + city_wise[\"dark_store_cpd\"]\n", "\n", "city_wise = city_wise.drop(columns={\"b2b_cpd\", \"dark_store_cpd\"})\n", "\n", "# Zone Wise Calculations\n", "zone_wise = (\n", "    ma_intransit.groupby([\"zone\", \"item_id\"])\n", "    .agg(\n", "        {\n", "            \"excess_inventory\": \"sum\",\n", "            \"shortfall_inventory\": \"sum\",\n", "            \"b2b_cpd\": \"sum\",\n", "            \"dark_store_cpd\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"excess_inventory\": \"zone_wise_excess_inventory\",\n", "            \"shortfall_inventory\": \"zone_wise_shortfall_inventory\",\n", "        }\n", "    )\n", ")\n", "zone_wise[\"zone_wise_cpd\"] = zone_wise[\"b2b_cpd\"] + zone_wise[\"dark_store_cpd\"]\n", "\n", "zone_wise = zone_wise.drop(columns={\"b2b_cpd\", \"dark_store_cpd\"})\n", "\n", "# Pan India Calculations\n", "pan_india_wise = (\n", "    ma_intransit.groupby([\"item_id\"])\n", "    .agg(\n", "        {\n", "            \"excess_inventory\": \"sum\",\n", "            \"shortfall_inventory\": \"sum\",\n", "            \"b2b_cpd\": \"sum\",\n", "            \"dark_store_cpd\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"excess_inventory\": \"pan_india_wise_excess_inventory\",\n", "            \"shortfall_inventory\": \"pan_india_wise_shortfall_inventory\",\n", "        }\n", "    )\n", ")\n", "pan_india_wise[\"pan_india_wise_cpd\"] = pan_india_wise[\"b2b_cpd\"] + pan_india_wise[\"dark_store_cpd\"]\n", "\n", "pan_india_wise = pan_india_wise.drop(columns={\"b2b_cpd\", \"dark_store_cpd\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_city = pd.merge(ma_intransit, city_wise, on=[\"city_id\", \"city_name\", \"item_id\"], how=\"left\")\n", "ma_city[\"city_wise_transfer_quantity\"] = np.where(\n", "    ((ma_city[\"final_doi_to_be_made\"] - ma_city[\"b2b_doi\"]) * ma_city[\"city_wise_cpd\"]) < 0,\n", "    0,\n", "    ((ma_city[\"final_doi_to_be_made\"] - ma_city[\"b2b_doi\"]) * ma_city[\"city_wise_cpd\"]),\n", ")\n", "\n", "ma_zone = pd.merge(ma_city, zone_wise, on=[\"zone\", \"item_id\"], how=\"left\")\n", "ma_zone[\"zone_wise_transfer_quantity\"] = np.where(\n", "    ((ma_zone[\"final_doi_to_be_made\"] - ma_zone[\"b2b_doi\"]) * ma_zone[\"zone_wise_cpd\"]) < 0,\n", "    0,\n", "    ((ma_zone[\"final_doi_to_be_made\"] - ma_zone[\"b2b_doi\"]) * ma_zone[\"zone_wise_cpd\"]),\n", ")\n", "\n", "ma_pan = pd.merge(ma_zone, pan_india_wise, on=[\"item_id\"], how=\"left\")\n", "ma_pan[\"pan_india_wise_transfer_quantity\"] = np.where(\n", "    ((ma_pan[\"final_doi_to_be_made\"] - ma_pan[\"b2b_doi\"]) * ma_pan[\"pan_india_wise_cpd\"]) < 0,\n", "    0,\n", "    ((ma_pan[\"final_doi_to_be_made\"] - ma_pan[\"b2b_doi\"]) * ma_pan[\"pan_india_wise_cpd\"]),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_default_cpd[ma_default_cpd.facility_id == 1].agg({\"ds_unblocked_qty\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan[ma_pan.facility_id == 1].agg({\"ds_unblocked_qty\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan = ma_pan.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan[ma_pan.facility_id == 1].agg({\"ds_unblocked_qty\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan = ma_pan[\n", "    ~(\n", "        (ma_pan.b2b_inv_unb_qty == 0)\n", "        & (ma_pan.open_sto == 0)\n", "        & (ma_pan.expected_po_qty == 0)\n", "        & (ma_pan.pending_putaway == 0)\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# check_level(ma_pan.drop_duplicates(), ['facility_id','item_id'], 'ma_pan')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan[\"backend_facility_id\"] = ma_pan[\"backend_facility_id\"].fillna(0).astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_stores[\"flag\"] = 1\n", "dark_stores.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan = pd.merge(ma_pan, dark_stores, on=[\"facility_id\"], how=\"left\")\n", "ma_pan = ma_pan[ma_pan.flag.isna()]\n", "ma_pan.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan = ma_pan.drop(columns={\"flag\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan.head(1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["ma_pan.to_csv('ma_pan.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"zone\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"zone of facility\",\n", "    },\n", "    {\n", "        \"name\": \"city_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"unique identifier for city\",\n", "    },\n", "    {\n", "        \"name\": \"city_name\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"name of the city for backend facility\",\n", "    },\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"unique identifier for facility\",\n", "    },\n", "    {\n", "        \"name\": \"item_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for item\",\n", "    },\n", "    {\n", "        \"name\": \"assortment_status\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"assortment status for item\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"unique identifier for vendor\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_name\",\n", "        \"type\": \"varchar(300)\",\n", "        \"description\": \"name of the vendor\",\n", "    },\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar(300)\",\n", "        \"description\": \"name of the manufacturer\",\n", "    },\n", "    {\n", "        \"name\": \"po_days\",\n", "        \"type\": \"varchar(300)\",\n", "        \"description\": \"PO Days for indents\",\n", "    },\n", "    {\n", "        \"name\": \"po_cycle\",\n", "        \"type\": \"float\",\n", "        \"description\": \"PO Cycle for indents\",\n", "    },\n", "    {\n", "        \"name\": \"po_cycle_type\",\n", "        \"type\": \"varchar(200)\",\n", "        \"description\": \"PO Cycle Type for indents\",\n", "    },\n", "    {\n", "        \"name\": \"lp\",\n", "        \"type\": \"float\",\n", "        \"description\": \"landing price of the item for the facility\",\n", "    },\n", "    {\n", "        \"name\": \"uom\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"Unit of measurement\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for backend facility\",\n", "    },\n", "    {\n", "        \"name\": \"is_transfer_sku\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier to understand if a SKU is transfer eligble from backend facility or bought to b2b through direct PO\",\n", "    },\n", "    {\n", "        \"name\": \"fill_rate_buffer_doi\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Fillrate buffer DOI for item\",\n", "    },\n", "    {\n", "        \"name\": \"fill_rate_buffer\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Fillrate buffer for item\",\n", "    },\n", "    {\n", "        \"name\": \"default_days\",\n", "        \"type\": \"float\",\n", "        \"description\": \"default days\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_doi\",\n", "        \"type\": \"float\",\n", "        \"description\": \"vendor doi for item\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_tat\",\n", "        \"type\": \"float\",\n", "        \"description\": \"vendor tat for item\",\n", "    },\n", "    {\n", "        \"name\": \"fe_facility_name\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"frontend facility name\",\n", "    },\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"backend facility name\",\n", "    },\n", "    {\n", "        \"name\": \"b2b_transfer_tat\",\n", "        \"type\": \"float\",\n", "        \"description\": \"transfer tat for transfers from backend facility to b2b\",\n", "    },\n", "    {\n", "        \"name\": \"b2b_transfer_doi\",\n", "        \"type\": \"float\",\n", "        \"description\": \"DOI maintains at b2b for transfers from backend facility \",\n", "    },\n", "    {\n", "        \"name\": \"dark_store_transfer_tat\",\n", "        \"type\": \"float\",\n", "        \"description\": \"transfer tat for transfers from b2b to frontend darkstore\",\n", "    },\n", "    {\n", "        \"name\": \"dark_store_transfer_doi\",\n", "        \"type\": \"float\",\n", "        \"description\": \"DOI maintains at frontend darkstore for transfers from b2b\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_unb_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend unblocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"b2b_inv_unb_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"b2b unblocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"ds_unblocked_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"darkstore unblocked inventory\",\n", "    },\n", "    {\n", "        \"name\": \"b2b_cpd\",\n", "        \"type\": \"float\",\n", "        \"description\": \"b2b cpd maintained \",\n", "    },\n", "    {\n", "        \"name\": \"dark_store_cpd\",\n", "        \"type\": \"float\",\n", "        \"description\": \"darkstore cpd maintained \",\n", "    },\n", "    {\n", "        \"name\": \"open_sto\",\n", "        \"type\": \"float\",\n", "        \"description\": \"open stos to b2b from backend\",\n", "    },\n", "    {\n", "        \"name\": \"expected_po_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"expectd PO quantity for item\",\n", "    },\n", "    {\n", "        \"name\": \"pending_putaway\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pending putaway at the darkstore\",\n", "    },\n", "    {\n", "        \"name\": \"max_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"maximum quantity for transfers or indent at b2b\",\n", "    },\n", "    {\n", "        \"name\": \"final_inv\",\n", "        \"type\": \"float\",\n", "        \"description\": \"total of b2b_inv_unb_qty, ds_unblocked_qty, open_sto, expected_po_qty\",\n", "    },\n", "    {\n", "        \"name\": \"final_doi_to_be_made\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Final DOI to be made based on current inventory\",\n", "    },\n", "    {\n", "        \"name\": \"final_ordering_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"final quantity to be ordered or transferred at b2b based on DOI to be maintained\",\n", "    },\n", "    {\n", "        \"name\": \"b2b_doi\",\n", "        \"type\": \"float\",\n", "        \"description\": \"DOI to be maintained at b2b\",\n", "    },\n", "    {\n", "        \"name\": \"excess_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"excess inventory at b2b\",\n", "    },\n", "    {\n", "        \"name\": \"shortfall_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"shortfall inventory at b2b\",\n", "    },\n", "    {\n", "        \"name\": \"city_wise_excess_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"citywise excess inventory for item\",\n", "    },\n", "    {\n", "        \"name\": \"city_wise_shortfall_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"citywise shortfall inventory for item\",\n", "    },\n", "    {\n", "        \"name\": \"city_wise_cpd\",\n", "        \"type\": \"float\",\n", "        \"description\": \"citywise cpd for the item\",\n", "    },\n", "    {\n", "        \"name\": \"city_wise_transfer_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"city wise transfer quantity for the item\",\n", "    },\n", "    {\n", "        \"name\": \"zone_wise_excess_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"zone excess inventory for item\",\n", "    },\n", "    {\n", "        \"name\": \"zone_wise_shortfall_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"zone shortfall inventory for item\",\n", "    },\n", "    {\n", "        \"name\": \"zone_wise_cpd\",\n", "        \"type\": \"float\",\n", "        \"description\": \"zone cpd for the item\",\n", "    },\n", "    {\n", "        \"name\": \"zone_wise_transfer_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"zone transfer quantity for the item\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_wise_excess_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pan india excess inventory for item\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_wise_shortfall_inventory\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pan india shortfall inventory for item\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_wise_cpd\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pan india cpd for the item\",\n", "    },\n", "    {\n", "        \"name\": \"pan_india_wise_transfer_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pan india transfer quantity for the item\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"excess_cost_impact_raw_data\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"sortkey\": [\"facility_id\", \"item_id\"],\n", "    \"incremental_key\": \"facility_id\",\n", "    \"load_type\": \"rebuild\",  # , # append, rebuild, truncate or upsert\n", "    \"table_description\": \"dataset calculating excess across facilities\",  # Description of the table being sent to redshift\n", "}\n", "if ma_pan.shape[0] > 0:\n", "    start_r = time.time()\n", "    pb.to_redshift(ma_pan, **kwargs)\n", "    end_r = time.time()\n", "    print(round((end_r - start_r) / 60, 2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Unique Items - Inv, STO, PO: -  Remove these"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_pan.agg({\"ds_unblocked_qty\": \"sum\", \"dark_store_cpd\": \"sum\"})"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}