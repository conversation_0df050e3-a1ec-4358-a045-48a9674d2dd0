{"cells": [{"cell_type": "code", "execution_count": null, "id": "b2a5f385-0838-471c-8708-253a14f0a830", "metadata": {}, "outputs": [], "source": ["!pip install pytz"]}, {"cell_type": "code", "execution_count": null, "id": "4b92dbe8-6d0d-4fe0-85e2-4a57a1551627", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "from tqdm import tqdm\n", "\n", "# import datetime\n", "import pytz\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "a686d37d-baa4-4536-9d8a-aec394edad14", "metadata": {}, "source": ["### IMP FUNCTIONS"]}, {"cell_type": "code", "execution_count": null, "id": "3845571a-c47f-4acd-998c-20982c642436", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)\n", "\n", "\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "55cb8a54-e96c-4183-874d-f9e78a9abc5a", "metadata": {}, "outputs": [], "source": ["manual = False"]}, {"cell_type": "markdown", "id": "90c9e13d-5b8e-48d1-b829-c21577101afb", "metadata": {}, "source": ["### Backend Avl views"]}, {"cell_type": "markdown", "id": "3c36d158-29d1-4641-b4bd-9f9aabbf696b", "metadata": {}, "source": ["read the data through sheets"]}, {"cell_type": "markdown", "id": "8e8dcc2b-bf39-40c9-b9ea-25c80e78c6b2", "metadata": {}, "source": ["cpd_date\n", "sale_start\n", "sale_end\n", "festival_name\n", "actual_date"]}, {"cell_type": "markdown", "id": "6f06d5f1-0d49-4499-bd29-98421fb24a4b", "metadata": {}, "source": ["interim_1 for festive_flags -- parameters (-- festival_name -- critical_spike -- festive_core_spike -- CY or PY spike value -- sale_start and sale_end_date to know whether to consider CY or PY spike)\n", "interim_2 for festive_in_out -- (cpd_date, sale_start, sale_end_date) for current_festival it has to be from last year values\n", "interim_3 for be_pan_india (not required for city_weights computation) (just tea date)"]}, {"cell_type": "markdown", "id": "82824f08-f385-4fe1-b273-48cb8a35f2ce", "metadata": {}, "source": ["For every festival I need to create 3 interims these interims can be used across be_avl and city_avl"]}, {"cell_type": "code", "execution_count": null, "id": "dc3b5009-a440-4fe7-a4ca-f89ce4bcec4a", "metadata": {}, "outputs": [], "source": ["if manual:\n", "    sheet = pd.read_csv(\"input_backfil_avl.csv\")\n", "else:\n", "    sheet = gsheet_to_df(\"1zeYlST37g5ig8u5DwCvSVoGZtI6VgfJMWgIGCW-w3Cs\", \"festive_list_for_avl\")"]}, {"cell_type": "code", "execution_count": null, "id": "4ed6eed3-93b0-48a9-b825-63faa2193d76", "metadata": {}, "outputs": [], "source": ["sheet.head()"]}, {"cell_type": "code", "execution_count": null, "id": "86024ea5-1965-42e2-8827-a5a105f6bcd2", "metadata": {}, "outputs": [], "source": ["os.getcwd()"]}, {"cell_type": "markdown", "id": "893b0ab5-2749-4779-b751-972abf9872f4", "metadata": {}, "source": ["#### <span style=\"color:red\">Converting time to ist</span>"]}, {"cell_type": "code", "execution_count": null, "id": "e86a221a-e010-47e3-ab3f-31c7ca2ef28d", "metadata": {}, "outputs": [], "source": ["ist_timezone = pytz.timezone(\"Asia/Kolkata\")\n", "ist_now = datetime.now(ist_timezone)"]}, {"cell_type": "code", "execution_count": null, "id": "2ea2ef49-2411-43ab-b8a7-b5b10cbef290", "metadata": {}, "outputs": [], "source": ["current_date = ist_now.date()\n", "current_date_str = current_date.strftime(\"%Y-%m-%d\")\n", "yesterday_date = current_date - <PERSON><PERSON><PERSON>(days=1)\n", "yesterday_date_str = yesterday_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "fc5db238-dbd1-4201-96b8-0b86bb379c00", "metadata": {}, "outputs": [], "source": ["sheet"]}, {"cell_type": "code", "execution_count": null, "id": "b57a952f-f234-4a89-8d38-a1abd712a02f", "metadata": {}, "outputs": [], "source": ["festivals = tuple(sheet[\"festival\"].to_numpy())"]}, {"cell_type": "code", "execution_count": null, "id": "8b3ee9f0-76dc-4d26-a898-d991b5268f37", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    #     Creating interim table-1\n", "    #     Need to check if data is present or not if not available then no data\n", "    if row[\"be\"] == \"No\":\n", "        print(f\"skipping for {row['festival']}\")\n", "        continue\n", "    festival_name = row[\"festival\"]\n", "    cpd_date = row[\"cpd_date\"]\n", "    sale_start_date = row[\"sale_start_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    festive_start_date = row[\"festive_start_date\"]\n", "    festive_end_date = row[\"festive_end_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "    column_dtypes_1 = [\n", "        {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Name of the festival\"},\n", "        {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Type of the product\"},\n", "        {\"name\": \"cy_search_spike\", \"type\": \"real\", \"description\": \"Current year search spike\"},\n", "        {\"name\": \"py_search_spike\", \"type\": \"real\", \"description\": \"Previous year search spike\"},\n", "        {\n", "            \"name\": \"critical_flag\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Flag indicating if the item is critical\",\n", "        },\n", "        {\n", "            \"name\": \"festive_core_flag\",\n", "            \"type\": \"integer\",\n", "            \"description\": \"Flag indicating if the item is part of the festive core\",\n", "        },\n", "    ]\n", "    kwargs_1 = {\n", "        \"schema_name\": \"interim\",\n", "        \"table_name\": \"festive_flags\",\n", "        \"column_dtypes\": column_dtypes_1,\n", "        \"primary_key\": [\"festival_name\", \"product_type\"],\n", "        \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "        \"table_description\": \"interim\",\n", "    }\n", "    sql_1 = f\"\"\"\n", "    with raw as (\n", "    select\n", "      festival_name,\n", "      product_type,\n", "      try(\n", "        sum(cy_search_device_count_festival) / sum(cy_search_device_count_bau)\n", "      ) as cy_search_spike,\n", "      try(\n", "        sum(py_search_device_count_festival) / sum(py_search_device_count_bau)\n", "      ) as py_search_spike\n", "    from\n", "      blinkit_iceberg.supply_etls.festival_forecast_final_date_city_ptype\n", "    where\n", "      festival_name = '{festival_name}'\n", "    group by\n", "      1,\n", "      2\n", "  ),\n", "  item_flag as (\n", "    select\n", "      festival_name,\n", "      product_type,\n", "      cy_search_spike,\n", "      py_search_spike,\n", "      case\n", "        when cy_search_spike >= 1.5 then 1\n", "        else 0\n", "      end as critical_flag,\n", "      case\n", "        when cy_search_spike >= 3 then 1\n", "        else 0\n", "      end as festive_core_flag\n", "    from\n", "      raw\n", "  )\n", "  select\n", "    *\n", "  from\n", "    item_flag  \n", "    \"\"\"\n", "    try:\n", "        to_trino(sql_1, **kwargs_1)\n", "        print(f\"pushed data to interim_table_festival_flags for festival {festival_name}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival_name}\")\n", "        break\n", "\n", "    column_dtypes_2 = [\n", "        {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Type of the product\"},\n", "    ]\n", "    kwargs_2 = {\n", "        \"schema_name\": \"interim\",\n", "        \"table_name\": \"festive_in_out\",\n", "        \"column_dtypes\": column_dtypes_2,\n", "        \"primary_key\": [\"product_type\"],\n", "        \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "        \"table_description\": \"interim\",\n", "    }\n", "    sql_2 = f\"\"\"\n", "        with product_item_mapping as (\n", "    select\n", "      product_id,\n", "      count(distinct item_id) as sku_count\n", "    from\n", "      blinkit_iceberg.dwh.dim_item_product_offer_mapping\n", "    where\n", "      is_current = true\n", "    group by\n", "      1\n", "    having\n", "      count(distinct item_id) = 1\n", "  ),\n", "  pid_to_ptype as (\n", "    select\n", "      distinct dp.product_id,\n", "      dp.product_type_id,\n", "      product_type\n", "    from\n", "      dwh.dim_product dp\n", "      inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "    where\n", "      is_current = true\n", "      and product_type not in ('combo', 'Combo')\n", "  ),\n", "  carts_ptype as (\n", "    select\n", "      order_create_dt_ist as date_,\n", "      product_type,\n", "      count(distinct cart_id) as carts\n", "    from\n", "      dwh.fact_sales_order_item_details x\n", "      left join pid_to_ptype dp on x.product_id = dp.product_id\n", "      left join supply_etls.outlet_details od on x.outlet_id = od.inv_outlet_id\n", "    where\n", "      (\n", "        (\n", "          order_create_dt_ist between date '{festive_start_date}'\n", "          and date '{festive_end_date}'\n", "        ) \n", "        or (\n", "          order_create_dt_ist between date '{bau_start_date}'\n", "          and date '{bau_end_date}'\n", "        )\n", "      ) -- bau period is cpd_Date - interval '10' days\n", "      and order_current_status = 'DELIVERED'\n", "      and order_type not in (\n", "        'InternalForwardOrder',\n", "        'InternalReverseOrder',\n", "        'DropShippingInternalReverseOrder'\n", "      )\n", "    group by\n", "      1,\n", "      2\n", "  ),\n", "  festive_tagging as (\n", "    select\n", "      product_type\n", "    from\n", "      carts_ptype\n", "    group by\n", "      1\n", "    having\n", "      (\n", "        coalesce(\n", "          avg(\n", "            case\n", "              when date_ between date '{bau_start_date}'\n", "              and date '{bau_end_date}' then carts\n", "            end\n", "          ),\n", "          0\n", "        ) / nullif(\n", "          max(\n", "            case\n", "              when date_ between date '{festive_start_date}'\n", "              and date '{festive_end_date}'\n", "              then carts\n", "            end\n", "          ),\n", "          0\n", "        )\n", "      ) <= 0.15\n", "      and max(\n", "        case\n", "          when date_ between date '{festive_start_date}'\n", "              and date '{festive_end_date}'\n", "          then carts\n", "        end\n", "      ) >= 1000\n", "      and coalesce(\n", "        avg(\n", "          case\n", "            when date_ between date '{bau_start_date}'\n", "              and date '{bau_end_date}' then carts\n", "          end\n", "        ),\n", "        0\n", "      ) < 1000\n", "  )\n", "  select\n", "    *\n", "  from\n", "    festive_tagging\n", "        \n", "    \"\"\"\n", "    try:\n", "        to_trino(sql_2, **kwargs_2)\n", "        print(f\"pushed data to interim_table_festival_in_out for festival {festival_name}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival_name}\")\n", "        break\n", "\n", "    column_dtypes_3 = [\n", "        {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "        {\"name\": \"weight\", \"type\": \"double\", \"description\": \"Type of the product\"},\n", "    ]\n", "    kwargs_3 = {\n", "        \"schema_name\": \"interim\",\n", "        \"table_name\": \"festive_be_pan_india\",\n", "        \"column_dtypes\": column_dtypes_3,\n", "        \"primary_key\": [\"be_facility_id\"],\n", "        \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "        \"table_description\": \"interim\",\n", "    }\n", "    sql_3 = f\"\"\"\n", "      with raw as (\n", "        select\n", "          irm.insert_ds_ist,\n", "          irm.hour_,\n", "          irm.be_facility_id,\n", "          irm.be_facility_name,\n", "          irm.fe_inv_outlet_id,\n", "          irm.fe_facility_id,\n", "          irm.fe_inv_outlet_name,\n", "          irm.item_id,\n", "          sum(coalesce(oiadc.aps_adjusted, 0)) as cpd\n", "        from\n", "          supply_etls.inventory_replenishment_metrics irm\n", "          left join ars.outlet_item_aps_derived_cpd oiadc on irm.fe_inv_outlet_id = oiadc.outlet_id\n", "          and irm.item_id = oiadc.item_id\n", "        where\n", "          irm.insert_ds_ist = date '{sale_start_date}'\n", "          and oiadc.insert_ds_ist = '{sale_start_date}'\n", "          and hour_ = (select max(hour_) from supply_etls.inventory_replenishment_metrics where insert_ds_ist = date '{sale_start_date}')\n", "          and (\n", "            active_outlet_flag = 1\n", "            or active_outlet_flag is null\n", "          )\n", "          and item_substate in (1, 3)\n", "        group by\n", "          1,2,3,4,5,6,7,8\n", "      ),\n", "      be_raw as (\n", "        select\n", "          be_facility_id,\n", "          sum(cpd) as final_ob\n", "        from\n", "          raw\n", "        group by\n", "          1\n", "      ),\n", "      be_tot as (\n", "        select\n", "          sum(final_ob) as final_tot\n", "        from\n", "          be_raw\n", "      )\n", "      select\n", "        be_facility_id,\n", "        final_ob / final_tot as weight\n", "      from\n", "        be_raw br\n", "        cross join be_tot bt\n", "      group by\n", "        1,\n", "        2\n", "    \"\"\"\n", "    try:\n", "        to_trino(sql_3, **kwargs_3)\n", "        print(f\"pushed data to interim_table_pan_india_weights for festival {festival_name}\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", str(e))\n", "        print(f\"failed for festival {festival_name}\")\n", "        break\n", "\n", "    print(f\"all interim tables are uploaded successfully\")\n", "\n", "    start = datetime.strptime(sale_start_date, \"%Y-%m-%d\")\n", "    end = datetime.strptime(sale_end_date, \"%Y-%m-%d\") + timed<PERSON>ta(days=1)\n", "\n", "    if pd.isna(row[\"be_pick_date\"]):\n", "        current = start\n", "    else:\n", "        print(f\"started for pick_date {row['be_pick_date']}+{festival_name}\")\n", "        current = datetime.strptime(row[\"be_pick_date\"], \"%Y-%m-%d\")\n", "\n", "    # current = start\n", "    while current <= end:\n", "        date_str = current.strftime(\"%Y-%m-%d\")\n", "\n", "        sql = f\"\"\"\n", "                    with bhw as (\n", "          select\n", "            cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "            cast(order_hour as integer) as order_hour,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_hour_weights\n", "          where\n", "            updated_at =(\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_hour_weights\n", "              where\n", "                updated_at <= date '{cpd_date}'  -- cpd_date\n", "            )\n", "        ),\n", "        bhw_fall as -- If there is no hour weight available then consider minimum weight and also it helps to elimate cases where there are no weights (In that case min_weight will be null) in those cases all the hours are given equal weight\n", "        (\n", "          select\n", "            backend_facility_id,\n", "            min(weights) as min_weight\n", "          from\n", "            bhw\n", "          group by\n", "            1\n", "        ),\n", "        bsw as (\n", "          select\n", "            cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "            facility_id,\n", "            cast(store_weight as double) as store_weight\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_store_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_store_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "        ),\n", "        bsw_fall_check as -- similar logic as why bhw_fall is there\n", "        (\n", "          select\n", "            backend_facility_id,\n", "            min(store_weight) as min_store_weight\n", "          from\n", "            bsw\n", "          group by\n", "            1\n", "        ),\n", "        chw as (\n", "          select\n", "            city,\n", "            order_hour,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_hour_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_hour_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "        ),\n", "        chw_fall as (\n", "          select\n", "            city,\n", "            min(weights) as min_weight\n", "          from\n", "            chw\n", "          group by\n", "            1\n", "        ),\n", "        csw as (\n", "          select\n", "            city,\n", "            facility_id,\n", "            cast(store_weight as double) as weight\n", "          from\n", "            blinkit_iceberg.supply_etls.city_store_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_store_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' -- cpd_date\n", "            )\n", "        ),\n", "        csw_fall as (\n", "          select\n", "            city,\n", "            min(weight) as min_store_weight\n", "          from\n", "            csw\n", "          group by\n", "            1\n", "        ),\n", "        -- This cte gives cy_year search spikes and tags few articles as both critical and festive_core\n", "        -- Some tweaks has to be done while computing current festival avl view\n", "        item_prop as (\n", "          select * from \n", "          blinkit_iceberg.interim.festive_flags\n", "        ),\n", "        -- This is to filter festive_in_out  articles\n", "        festive_in_out as (\n", "          select * from \n", "          blinkit_iceberg.interim.festive_in_out\n", "        ),\n", "        biw as (\n", "          select\n", "            backend_facility_id,\n", "            item_id,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.backend_item_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_item_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        ciw as (\n", "          select\n", "            city,\n", "            item_id,\n", "            cast(weights as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_item_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_item_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight as (\n", "          select\n", "            city,\n", "            cast(weight as double) as weights\n", "          from\n", "            blinkit_iceberg.supply_etls.city_weights\n", "          where\n", "            updated_at = (\n", "              select\n", "                max(updated_at)\n", "              from\n", "                blinkit_iceberg.supply_etls.city_weights\n", "              where\n", "                updated_at <= date '{cpd_date}' --cpd_date\n", "            )\n", "        ),\n", "        city_weight_fall as (\n", "          select\n", "            min(weights) as min_weight\n", "          from\n", "            city_weight\n", "        ),\n", "        be_pan_india as (\n", "          select * from\n", "          blinkit_iceberg.interim.festive_be_pan_india\n", "        ),\n", "        be_pan_india_fall as (\n", "          select\n", "            min(weight) as min_weight\n", "          from\n", "            be_pan_india\n", "        ),\n", "        raw_filtered as \n", "        (SELECT \n", "                insert_ds_ist,\n", "            hour_,\n", "            be_facility_id,\n", "            fe_facility_id,\n", "            item_id,\n", "            be_facility_name,\n", "            fe_city_name,\n", "            fe_inv_outlet_id,\n", "            fe_inv_outlet_name,\n", "            express_longtail,\n", "            l0_id,\n", "            l0_category,\n", "            l1_id,\n", "            l1_category,\n", "            l2_id,\n", "            l2_category,\n", "            p_type,\n", "            item_substate,\n", "            fe_avail_flag,\n", "            sto_cpd,\n", "            be_inv\n", "            --      ROW_NUMBER() OVER (PARTITION BY \n", "            --      insert_ds_ist,\n", "            -- hour_,\n", "            -- be_facility_id,\n", "            -- fe_facility_id,\n", "            -- item_id\n", "            --      ORDER BY fe_inv) AS rn\n", "          FROM blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "          where\n", "            insert_ds_ist = date '{date_str}' -- date '2025-03-15' --actual_date\n", "            -- = date '2025-03-15'\n", "            -- insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "            and (\n", "              active_outlet_flag = 1\n", "              or active_outlet_flag is null\n", "            )\n", "            and item_substate in (1, 3)\n", "            and ((fastival_tag = '{festival_name}') or (fastival_tag = 'BAU')) -- festival_name\n", "        ),\n", "        fmtt as (\n", "          select\n", "            insert_ds_ist,\n", "            hour_,\n", "            be_facility_id,\n", "            fe_facility_id,\n", "            item_id,\n", "            be_facility_name,\n", "            fe_city_name,\n", "            fe_inv_outlet_id,\n", "            fe_inv_outlet_name,\n", "            express_longtail,\n", "            l0_id,\n", "            l0_category,\n", "            l1_id,\n", "            l1_category,\n", "            l2_id,\n", "            l2_category,\n", "            p_type,\n", "            case\n", "              when item_substate = 1 then 'active'\n", "              when item_substate = 2 then 'inactive'\n", "              when item_substate = 3 then 'temp_inactive'\n", "              else 'discontinued'\n", "            end as assortment,\n", "            fe_avail_flag,\n", "            sto_cpd,\n", "            be_inv,\n", "            case\n", "              when fe_avail_flag = 0\n", "              and (2 * sto_cpd) <= 1 then 1\n", "              when fe_avail_flag = 0\n", "              and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "              else 0 end as req_inv\n", "          from\n", "            raw_filtered\n", "        --   WHERE rn = 1\n", "            -- insert_ds_ist = date '2025-03-06' -- date '2025-03-15'\n", "            -- -- = date '2025-03-15'\n", "            -- -- insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "            -- and (\n", "            --   active_outlet_flag = 1\n", "            --   or active_outlet_flag is null\n", "            -- )\n", "            -- and item_substate in (1, 3)\n", "            -- group by \n", "            -- 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21\n", "        ),\n", "        bebe_req_core as (\n", "          select\n", "            insert_ds_ist,\n", "            hour_,\n", "            be_facility_id,\n", "            item_id,\n", "            sum(req_inv) as req_inv\n", "          from\n", "            fmtt\n", "          group by\n", "            1,2,3,4\n", "        ),\n", "        fmta as (\n", "          select\n", "            fmtt.*,\n", "            case\n", "              when bc.req_inv <= be_inv then 1\n", "              else fe_avail_flag\n", "            end as be_fe_avail_flag\n", "          from\n", "            fmtt\n", "            left join bebe_req_core bc on bc.be_facility_id = fmtt.be_facility_id\n", "            and bc.item_id = fmtt.item_id\n", "            and bc.insert_ds_ist = fmtt.insert_ds_ist\n", "            and bc.hour_ = fmtt.hour_\n", "        ),\n", "        festive_weigths_ciw as (\n", "        select city_name, item_id, weight\n", "        from blinkit_iceberg.supply_etls.festive_city_item_weight\n", "        where event_name = '{festival_name}'\n", "        and event_name is not null\n", "        ),\n", "        festive_weights_biw as (\n", "        select be_facility_id, item_id, weight\n", "        from blinkit_iceberg.supply_etls.festive_backend_item_weight\n", "        where event_name = '{festival_name}'\n", "        and event_name is not null\n", "        ),\n", "\n", "        final_raw as \n", "        (\n", "          select\n", "            fmta.*,\n", "            ip.critical_flag,\n", "            ip.festive_core_flag,\n", "            coalesce(biw.weights, 0) as item_weight,\n", "            coalesce(bhw.weights, bf.min_weight, 1) as hour_weight,\n", "            case\n", "              when bfc.min_store_weight is not null then coalesce(bsw.store_weight, 0)\n", "              else 1\n", "            end as store_weight,\n", "            coalesce(fwb.weight, 0) as festive_item_weight,\n", "            case\n", "              when fio.product_type is null then 0\n", "              else 1\n", "            end as fes_in_out_flag,\n", "            coalesce(fwc.weight, 0) as festive_city_item_weight,\n", "            coalesce(ciw.weights, 0) as city_item_weight,\n", "            coalesce(chw.weights, cf.min_weight, 1) as city_hour_weight,\n", "            coalesce(csw.weight, cfc.min_store_weight, 1) as city_store_weight,\n", "            coalesce(cw.weights, cwf.min_weight, 0) as pan_city_weight,\n", "            coalesce(bpi.weight, bpif.min_weight, 0) as pan_be_weight\n", "          from\n", "            fmta\n", "            left join item_prop ip on fmta.p_type = ip.product_type\n", "            left join biw on biw.backend_facility_id = fmta.be_facility_id\n", "            and biw.item_id = fmta.item_id\n", "            left join bhw on bhw.backend_facility_id = fmta.be_facility_id\n", "            and bhw.order_hour = fmta.hour_\n", "            left join bsw on bsw.backend_facility_id = fmta.be_facility_id\n", "            and bsw.facility_id = fmta.fe_facility_id\n", "            left join bhw_fall bf on bf.backend_facility_id = fmta.be_facility_id\n", "            left join festive_weights_biw fwb on fwb.be_facility_id = fmta.be_facility_id\n", "            and fwb.item_id = fmta.item_id\n", "            left join bsw_fall_check bfc on bfc.backend_facility_id = fmta.be_facility_id\n", "            left join festive_in_out fio on fio.product_type = fmta.p_type\n", "            left join festive_weigths_ciw fwc on fwc.city_name = fmta.fe_city_name\n", "            and fwc.item_id = fmta.item_id\n", "            left join ciw on ciw.city = fmta.fe_city_name\n", "            and ciw.item_id = fmta.item_id\n", "            left join chw on chw.city = fmta.fe_city_name\n", "            and chw.order_hour = fmta.hour_\n", "            left join csw on csw.city = fmta.fe_city_name\n", "            and csw.facility_id = fmta.fe_facility_id\n", "            left join chw_fall cf on cf.city = fmta.fe_city_name\n", "            left join csw_fall cfc on cfc.city = fmta.fe_city_name\n", "            left join city_weight cw on cw.city = fmta.fe_city_name\n", "            left join be_pan_india bpi on fmta.be_facility_id = bpi.be_facility_id\n", "            cross join be_pan_india_fall bpif\n", "            cross join city_weight_fall cwf\n", "        ),\n", "        avl_raw as\n", "        (select be_facility_id, hour_, insert_ds_ist, \n", "        fe_facility_id, store_weight, hour_weight,\n", "        case when sum(item_weight) = 0 then 0 else\n", "        sum(fe_avail_flag*item_weight)/sum(item_weight) end as fe_avl,\n", "        case when sum((1-critical_flag)*item_weight) = 0 then 0 else\n", "        sum(fe_avail_flag*(1-critical_flag)*item_weight)/sum((1-critical_flag)*item_weight) end as non_festive_avl,\n", "        case when sum((critical_flag-greatest(festive_core_flag,fes_in_out_flag))*item_weight) =0 then 0 else\n", "        sum(fe_avail_flag*(critical_flag-greatest(festive_core_flag,fes_in_out_flag))*item_weight)/nullif(sum((critical_flag-greatest(festive_core_flag,fes_in_out_flag))*item_weight),0) end as critical_avl,\n", "        case when sum((festive_core_flag-fes_in_out_flag)*festive_item_weight) = 0 then 0 else\n", "        sum(fe_avail_flag*(festive_core_flag-fes_in_out_flag)*festive_item_weight)/nullif(sum((festive_core_flag-fes_in_out_flag)*festive_item_weight),0) end as festive_core_avl,\n", "        case when sum(fes_in_out_flag*festive_item_weight) = 0 then 0 else\n", "        sum(fes_in_out_flag*fe_avail_flag*festive_item_weight)/nullif(sum(fes_in_out_flag*festive_item_weight),0) end as festive_in_out_avl\n", "        from final_raw \n", "        group by 1,2,3,4,5,6),\n", "\n", "        avl_be_raw as\n", "        (\n", "            select be_facility_id, hour_, insert_ds_ist,hour_weight\n", "            , sum(fe_avl*store_weight)/sum(store_weight) as avl,\n", "            sum(non_festive_avl*store_weight)/sum(store_weight) as non_festive_avl,\n", "            sum(critical_avl*store_weight)/sum(store_weight) as critical_avl,\n", "            sum(festive_core_avl*store_weight)/sum(store_weight) as festive_core_avl,\n", "            sum(festive_in_out_avl*store_weight)/sum(store_weight) as festive_in_out_avl\n", "            from avl_raw group by 1,2,3,4\n", "\n", "        ),\n", "\n", "        avl_be as\n", "        (\n", "            select '{festival_name}' as festive_name, be_facility_id, insert_ds_ist, sum(avl*hour_weight)/sum(hour_weight) as avl,\n", "            sum(non_festive_avl*hour_weight)/sum(hour_weight) as non_festive_avl,\n", "            sum(critical_avl*hour_weight)/sum(hour_weight) as critical_avl,\n", "            sum(festive_core_avl*hour_weight)/sum(hour_weight) as festive_core_avl,\n", "            sum(festive_in_out_avl*hour_weight)/sum(hour_weight) as festive_in_out_avl\n", "            from avl_be_raw\n", "            group by 1,2,3\n", "        )\n", "        select * from avl_be\n", "                \"\"\"\n", "        column_dtypes = [\n", "            {\"name\": \"festive_name\", \"type\": \"varchar\", \"description\": \"festive_name\"},\n", "            {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"BE facility identifier\"},\n", "            {\n", "                \"name\": \"insert_ds_ist\",\n", "                \"type\": \"date\",\n", "                \"description\": \"Data snapshot date (yyyy-mm-dd)\",\n", "            },\n", "            {\"name\": \"avl\", \"type\": \"double\", \"description\": \"avl\"},\n", "            {\"name\": \"non_festive_avl\", \"type\": \"double\", \"description\": \"non_festive_avl\"},\n", "            {\"name\": \"critical_avl\", \"type\": \"double\", \"description\": \"critical_avl\"},\n", "            {\"name\": \"festive_core_avl\", \"type\": \"double\", \"description\": \"festive_core_avl\"},\n", "            {\"name\": \"festive_in_out_avl\", \"type\": \"double\", \"description\": \"festive_in_out_avl\"},\n", "        ]\n", "\n", "        kwargs = {\n", "            \"schema_name\": \"supply_etls\",\n", "            \"table_name\": \"festive_be_avl\",\n", "            \"column_dtypes\": column_dtypes,\n", "            \"primary_key\": [\"festive_name\", \"be_facility_id\", \"insert_ds_ist\"],\n", "            \"partition_key\": [\"festive_name\"],\n", "            \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "            \"table_description\": \"festive_be_avl\",\n", "        }\n", "\n", "        try:\n", "            to_trino(sql, **kwargs)\n", "            print(f\"pushed data to be_festive_table for date {date_str}\")\n", "        except Exception as e:\n", "            print(\"An error occurred:\", str(e))\n", "            print(f\"failed for festival {festival_name}+{date_str}\")\n", "            break\n", "\n", "        current += <PERSON><PERSON><PERSON>(days=1)"]}, {"cell_type": "markdown", "id": "503c787c-683b-44d4-b3a9-2d37977f3f2e", "metadata": {}, "source": ["### City Level Views"]}, {"cell_type": "code", "execution_count": null, "id": "03eb9cf9-0715-4c1c-9673-daf03708e2e2", "metadata": {}, "outputs": [], "source": ["for index, row in sheet.iterrows():\n", "    #     Creating interim table-1\n", "    #     Need to check if data is present or not if not available then no data\n", "\n", "    if row[\"city\"] == \"No\":\n", "        print(f\"skipping for {row['festival']}\")\n", "        continue\n", "\n", "    festival_name = row[\"festival\"]\n", "    print(f\"started for {festival_name}\")\n", "    cpd_date = row[\"cpd_date\"]\n", "    sale_start_date = row[\"sale_start_date\"]\n", "    sale_end_date = row[\"sale_end_date\"]\n", "    festive_start_date = row[\"festive_start_date\"]\n", "    festive_end_date = row[\"festive_end_date\"]\n", "    bau_start_date = row[\"bau_start_date\"]\n", "    bau_end_date = row[\"bau_end_date\"]\n", "\n", "    start = datetime.strptime(sale_start_date, \"%Y-%m-%d\")\n", "    end = datetime.strptime(sale_end_date, \"%Y-%m-%d\") + timed<PERSON>ta(days=1)\n", "\n", "    if pd.isna(row[\"city_pick_date\"]):\n", "        current = start\n", "    else:\n", "        print(f\"started for pick_date {row['city_pick_date']}+{festival_name}\")\n", "        try:\n", "            current = datetime.strptime(row[\"city_pick_date\"], \"%Y-%m-%d\")\n", "        except Exception as e:\n", "            print(\"An error occurred: while accessing pick_date\", str(e))\n", "            current = start\n", "            print(f\"Now date is {current}\")\n", "\n", "    # current = start\n", "    while current <= end:\n", "        date_str = current.strftime(\"%Y-%m-%d\")\n", "        print(f\"reading sql for {festival_name}+{date_str}\")\n", "        sql_city = f\"\"\"\n", "                                with bhw as (\n", "              select\n", "                cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "                cast(order_hour as integer) as order_hour,\n", "                cast(weights as double) as weights\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_hour_weights\n", "              where\n", "                updated_at =(\n", "                  select\n", "                    max(updated_at)\n", "                  from\n", "                    blinkit_iceberg.supply_etls.backend_hour_weights\n", "                  where\n", "                    updated_at <= date '{cpd_date}'  -- cpd_date\n", "                )\n", "            ),\n", "            bhw_fall as -- If there is no hour weight available then consider minimum weight and also it helps to elimate cases where there are no weights (In that case min_weight will be null) in those cases all the hours are given equal weight\n", "            (\n", "              select\n", "                backend_facility_id,\n", "                min(weights) as min_weight\n", "              from\n", "                bhw\n", "              group by\n", "                1\n", "            ),\n", "            bsw as (\n", "              select\n", "                cast(cast(backend_facility_id as double) as integer) as backend_facility_id,\n", "                facility_id,\n", "                cast(store_weight as double) as store_weight\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_store_weights\n", "              where\n", "                updated_at = (\n", "                  select\n", "                    max(updated_at)\n", "                  from\n", "                    blinkit_iceberg.supply_etls.backend_store_weights\n", "                  where\n", "                    updated_at <= date '{cpd_date}' -- cpd_date\n", "                )\n", "            ),\n", "            bsw_fall_check as -- similar logic as why bhw_fall is there\n", "            (\n", "              select\n", "                backend_facility_id,\n", "                min(store_weight) as min_store_weight\n", "              from\n", "                bsw\n", "              group by\n", "                1\n", "            ),\n", "            chw as (\n", "              select\n", "                city,\n", "                order_hour,\n", "                cast(weights as double) as weights\n", "              from\n", "                blinkit_iceberg.supply_etls.city_hour_weights\n", "              where\n", "                updated_at = (\n", "                  select\n", "                    max(updated_at)\n", "                  from\n", "                    blinkit_iceberg.supply_etls.city_hour_weights\n", "                  where\n", "                    updated_at <= date '{cpd_date}' -- cpd_date\n", "                )\n", "            ),\n", "            chw_fall as (\n", "              select\n", "                city,\n", "                min(weights) as min_weight\n", "              from\n", "                chw\n", "              group by\n", "                1\n", "            ),\n", "            csw as (\n", "              select\n", "                city,\n", "                facility_id,\n", "                cast(store_weight as double) as weight\n", "              from\n", "                blinkit_iceberg.supply_etls.city_store_weights\n", "              where\n", "                updated_at = (\n", "                  select\n", "                    max(updated_at)\n", "                  from\n", "                    blinkit_iceberg.supply_etls.city_store_weights\n", "                  where\n", "                    updated_at <= date '{cpd_date}' -- cpd_date\n", "                )\n", "            ),\n", "            csw_fall as (\n", "              select\n", "                city,\n", "                min(weight) as min_store_weight\n", "              from\n", "                csw\n", "              group by\n", "                1\n", "            ),\n", "            -- This cte gives cy_year search spikes and tags few articles as both critical and festive_core\n", "            -- Some tweaks has to be done while computing current festival avl view\n", "            item_prop as (\n", "              select * from \n", "              blinkit_iceberg.interim.festive_flags\n", "            ),\n", "            -- This is to filter festive_in_out  articles\n", "            festive_in_out as (\n", "              select * from \n", "              blinkit_iceberg.interim.festive_in_out\n", "            ),\n", "            biw as (\n", "              select\n", "                backend_facility_id,\n", "                item_id,\n", "                cast(weights as double) as weights\n", "              from\n", "                blinkit_iceberg.supply_etls.backend_item_weights\n", "              where\n", "                updated_at = (\n", "                  select\n", "                    max(updated_at)\n", "                  from\n", "                    blinkit_iceberg.supply_etls.backend_item_weights\n", "                  where\n", "                    updated_at <= date '{cpd_date}' --cpd_date\n", "                )\n", "            ),\n", "            ciw as (\n", "              select\n", "                city,\n", "                item_id,\n", "                cast(weights as double) as weights\n", "              from\n", "                blinkit_iceberg.supply_etls.city_item_weights\n", "              where\n", "                updated_at = (\n", "                  select\n", "                    max(updated_at)\n", "                  from\n", "                    blinkit_iceberg.supply_etls.city_item_weights\n", "                  where\n", "                    updated_at <= date '{cpd_date}' --cpd_date\n", "                )\n", "            ),\n", "            city_weight as (\n", "              select\n", "                city,\n", "                cast(weight as double) as weights\n", "              from\n", "                blinkit_iceberg.supply_etls.city_weights\n", "              where\n", "                updated_at = (\n", "                  select\n", "                    max(updated_at)\n", "                  from\n", "                    blinkit_iceberg.supply_etls.city_weights\n", "                  where\n", "                    updated_at <= date '{cpd_date}' --cpd_date\n", "                )\n", "            ),\n", "            city_weight_fall as (\n", "              select\n", "                min(weights) as min_weight\n", "              from\n", "                city_weight\n", "            ),\n", "            be_pan_india as (\n", "              select * from\n", "              blinkit_iceberg.interim.festive_be_pan_india\n", "            ),\n", "            be_pan_india_fall as (\n", "              select\n", "                min(weight) as min_weight\n", "              from\n", "                be_pan_india\n", "            ),\n", "            raw_filtered as \n", "            (SELECT \n", "                    insert_ds_ist,\n", "                hour_,\n", "                be_facility_id,\n", "                fe_facility_id,\n", "                item_id,\n", "                be_facility_name,\n", "                fe_city_name,\n", "                fe_inv_outlet_id,\n", "                fe_inv_outlet_name,\n", "                express_longtail,\n", "                l0_id,\n", "                l0_category,\n", "                l1_id,\n", "                l1_category,\n", "                l2_id,\n", "                l2_category,\n", "                p_type,\n", "                item_substate,\n", "                fe_avail_flag,\n", "                sto_cpd,\n", "                be_inv\n", "                --      ROW_NUMBER() OVER (PARTITION BY \n", "                --      insert_ds_ist,\n", "                -- hour_,\n", "                -- be_facility_id,\n", "                -- fe_facility_id,\n", "                -- item_id\n", "                --      ORDER BY fe_inv) AS rn\n", "              FROM blinkit_iceberg.supply_etls.inventory_replenishment_metrics\n", "              where\n", "                insert_ds_ist = date '{date_str}' -- date '2025-03-15' --actual_date\n", "                -- = date '2025-03-15'\n", "                -- insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "                and (\n", "                  active_outlet_flag = 1\n", "                  or active_outlet_flag is null\n", "                )\n", "                and item_substate in (1, 3)\n", "                and ((fastival_tag = '{festival_name}') or (fastival_tag = 'BAU')) -- festival_name\n", "            ),\n", "            fmtt as (\n", "              select\n", "                insert_ds_ist,\n", "                hour_,\n", "                be_facility_id,\n", "                fe_facility_id,\n", "                item_id,\n", "                be_facility_name,\n", "                fe_city_name,\n", "                fe_inv_outlet_id,\n", "                fe_inv_outlet_name,\n", "                express_longtail,\n", "                l0_id,\n", "                l0_category,\n", "                l1_id,\n", "                l1_category,\n", "                l2_id,\n", "                l2_category,\n", "                p_type,\n", "                case\n", "                  when item_substate = 1 then 'active'\n", "                  when item_substate = 2 then 'inactive'\n", "                  when item_substate = 3 then 'temp_inactive'\n", "                  else 'discontinued'\n", "                end as assortment,\n", "                fe_avail_flag,\n", "                sto_cpd,\n", "                be_inv,\n", "                case\n", "                  when fe_avail_flag = 0\n", "                  and (2 * sto_cpd) <= 1 then 1\n", "                  when fe_avail_flag = 0\n", "                  and (2 * sto_cpd) > 1 then (2 * sto_cpd)\n", "                  else 0 end as req_inv\n", "              from\n", "                raw_filtered\n", "            --   WHERE rn = 1\n", "                -- insert_ds_ist = date '2025-03-06' -- date '2025-03-15'\n", "                -- -- = date '2025-03-15'\n", "                -- -- insert_ds_ist = cast(current_timestamp - interval '55' minute as date)\n", "                -- and (\n", "                --   active_outlet_flag = 1\n", "                --   or active_outlet_flag is null\n", "                -- )\n", "                -- and item_substate in (1, 3)\n", "                -- group by \n", "                -- 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21\n", "            ),\n", "            bebe_req_core as (\n", "              select\n", "                insert_ds_ist,\n", "                hour_,\n", "                be_facility_id,\n", "                item_id,\n", "                sum(req_inv) as req_inv\n", "              from\n", "                fmtt\n", "              group by\n", "                1,2,3,4\n", "            ),\n", "            fmta as (\n", "              select\n", "                fmtt.*,\n", "                case\n", "                  when bc.req_inv <= be_inv then 1\n", "                  else fe_avail_flag\n", "                end as be_fe_avail_flag\n", "              from\n", "                fmtt\n", "                left join bebe_req_core bc on bc.be_facility_id = fmtt.be_facility_id\n", "                and bc.item_id = fmtt.item_id\n", "                and bc.insert_ds_ist = fmtt.insert_ds_ist\n", "                and bc.hour_ = fmtt.hour_\n", "            ),\n", "            festive_weigths_ciw as (\n", "            select city_name, item_id, weight\n", "            from blinkit_iceberg.supply_etls.festive_city_item_weight\n", "            where event_name = '{festival_name}'\n", "            and event_name is not null\n", "            ),\n", "            festive_weights_biw as (\n", "            select be_facility_id, item_id, weight\n", "            from blinkit_iceberg.supply_etls.festive_backend_item_weight\n", "            where event_name = '{festival_name}'\n", "            and event_name is not null\n", "            ),\n", "\n", "            final_raw as \n", "            (\n", "              select\n", "                fmta.*,\n", "                ip.critical_flag,\n", "                ip.festive_core_flag,\n", "                coalesce(biw.weights, 0) as item_weight,\n", "                coalesce(bhw.weights, bf.min_weight, 1) as hour_weight,\n", "                case\n", "                  when bfc.min_store_weight is not null then coalesce(bsw.store_weight, 0)\n", "                  else 1\n", "                end as store_weight,\n", "                coalesce(fwb.weight, 0) as festive_item_weight,\n", "                case\n", "                  when fio.product_type is null then 0\n", "                  else 1\n", "                end as fes_in_out_flag,\n", "                coalesce(fwc.weight, 0) as festive_city_item_weight,\n", "                coalesce(ciw.weights, 0) as city_item_weight,\n", "                coalesce(chw.weights, cf.min_weight, 1) as city_hour_weight,\n", "                coalesce(csw.weight, cfc.min_store_weight, 1) as city_store_weight,\n", "                coalesce(cw.weights, cwf.min_weight, 0) as pan_city_weight,\n", "                coalesce(bpi.weight, bpif.min_weight, 0) as pan_be_weight\n", "              from\n", "                fmta\n", "                left join item_prop ip on fmta.p_type = ip.product_type\n", "                left join biw on biw.backend_facility_id = fmta.be_facility_id\n", "                and biw.item_id = fmta.item_id\n", "                left join bhw on bhw.backend_facility_id = fmta.be_facility_id\n", "                and bhw.order_hour = fmta.hour_\n", "                left join bsw on bsw.backend_facility_id = fmta.be_facility_id\n", "                and bsw.facility_id = fmta.fe_facility_id\n", "                left join bhw_fall bf on bf.backend_facility_id = fmta.be_facility_id\n", "                left join festive_weights_biw fwb on fwb.be_facility_id = fmta.be_facility_id\n", "                and fwb.item_id = fmta.item_id\n", "                left join bsw_fall_check bfc on bfc.backend_facility_id = fmta.be_facility_id\n", "                left join festive_in_out fio on fio.product_type = fmta.p_type\n", "                left join festive_weigths_ciw fwc on fwc.city_name = fmta.fe_city_name\n", "                and fwc.item_id = fmta.item_id\n", "                left join ciw on ciw.city = fmta.fe_city_name\n", "                and ciw.item_id = fmta.item_id\n", "                left join chw on chw.city = fmta.fe_city_name\n", "                and chw.order_hour = fmta.hour_\n", "                left join csw on csw.city = fmta.fe_city_name\n", "                and csw.facility_id = fmta.fe_facility_id\n", "                left join chw_fall cf on cf.city = fmta.fe_city_name\n", "                left join csw_fall cfc on cfc.city = fmta.fe_city_name\n", "                left join city_weight cw on cw.city = fmta.fe_city_name\n", "                left join be_pan_india bpi on fmta.be_facility_id = bpi.be_facility_id\n", "                cross join be_pan_india_fall bpif\n", "                cross join city_weight_fall cwf\n", "            ),            \n", "            avl_raw as\n", "            (select fe_city_name, hour_, insert_ds_ist, \n", "            fe_facility_id, city_store_weight, city_hour_weight,\n", "            case when sum(city_item_weight) = 0 then 0 else\n", "            sum(fe_avail_flag*city_item_weight)/sum(city_item_weight) end as fe_avl,\n", "            case when sum((1-critical_flag)*city_item_weight) = 0 then 0 else\n", "            sum(fe_avail_flag*(1-critical_flag)*city_item_weight)/sum((1-critical_flag)*city_item_weight) end as non_festive_avl,\n", "            case when sum((critical_flag-greatest(festive_core_flag,fes_in_out_flag))*city_item_weight) =0 then 0 else\n", "            sum(fe_avail_flag*(critical_flag-greatest(festive_core_flag,fes_in_out_flag))*city_item_weight)/nullif(sum((critical_flag-greatest(festive_core_flag,fes_in_out_flag))*city_item_weight),0) end as critical_avl,\n", "            case when sum((festive_core_flag-fes_in_out_flag)*festive_city_item_weight) = 0 then 0 else\n", "            sum(fe_avail_flag*(festive_core_flag-fes_in_out_flag)*festive_city_item_weight)/nullif(sum((festive_core_flag-fes_in_out_flag)*festive_city_item_weight),0) end as festive_core_avl,\n", "            case when sum(fes_in_out_flag*festive_city_item_weight) = 0 then 0 else\n", "            sum(fes_in_out_flag*fe_avail_flag*festive_city_item_weight)/nullif(sum(fes_in_out_flag*festive_city_item_weight),0) end as festive_in_out_avl\n", "            from final_raw \n", "            group by 1,2,3,4,5,6),\n", "\n", "            avl_be_raw as\n", "            (\n", "                select fe_city_name, hour_, insert_ds_ist,city_hour_weight\n", "                , sum(fe_avl*city_store_weight)/sum(city_store_weight) as avl,\n", "                sum(non_festive_avl*city_store_weight)/sum(city_store_weight) as non_festive_avl,\n", "                sum(critical_avl*city_store_weight)/sum(city_store_weight) as critical_avl,\n", "                sum(festive_core_avl*city_store_weight)/sum(city_store_weight) as festive_core_avl,\n", "                sum(festive_in_out_avl*city_store_weight)/sum(city_store_weight) as festive_in_out_avl\n", "                from avl_raw group by 1,2,3,4\n", "\n", "            ),\n", "\n", "            avl_be as\n", "            (\n", "                select '{festival_name}' as festive_name, fe_city_name, insert_ds_ist, sum(avl*city_hour_weight)/sum(city_hour_weight) as avl,\n", "                sum(non_festive_avl*city_hour_weight)/sum(city_hour_weight) as non_festive_avl,\n", "                sum(critical_avl*city_hour_weight)/sum(city_hour_weight) as critical_avl,\n", "                sum(festive_core_avl*city_hour_weight)/sum(city_hour_weight) as festive_core_avl,\n", "                sum(festive_in_out_avl*city_hour_weight)/sum(city_hour_weight) as festive_in_out_avl\n", "                from avl_be_raw\n", "                group by 1,2,3\n", "            )\n", "            select * from avl_be\n", "            \n", "    \"\"\"\n", "        column_dtypes = [\n", "            {\"name\": \"festive_name\", \"type\": \"varchar\", \"description\": \"festive_name\"},\n", "            {\"name\": \"fe_city_name\", \"type\": \"varchar \", \"description\": \"BE facility identifier\"},\n", "            {\n", "                \"name\": \"insert_ds_ist\",\n", "                \"type\": \"date\",\n", "                \"description\": \"Data snapshot date (yyyy-mm-dd)\",\n", "            },\n", "            {\"name\": \"avl\", \"type\": \"double\", \"description\": \"avl\"},\n", "            {\"name\": \"non_festive_avl\", \"type\": \"double\", \"description\": \"non_festive_avl\"},\n", "            {\"name\": \"critical_avl\", \"type\": \"double\", \"description\": \"critical_avl\"},\n", "            {\"name\": \"festive_core_avl\", \"type\": \"double\", \"description\": \"festive_core_avl\"},\n", "            {\"name\": \"festive_in_out_avl\", \"type\": \"double\", \"description\": \"festive_in_out_avl\"},\n", "        ]\n", "\n", "        kwargs = {\n", "            \"schema_name\": \"supply_etls\",\n", "            \"table_name\": \"festive_city_avl\",\n", "            \"column_dtypes\": column_dtypes,\n", "            \"primary_key\": [\"festive_name\", \"fe_city_name\", \"insert_ds_ist\"],\n", "            \"partition_key\": [\"festive_name\"],\n", "            \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "            \"table_description\": \"festive_city_avl\",\n", "        }\n", "\n", "        try:\n", "            to_trino(sql_city, **kwargs)\n", "            print(f\"pushed data to be_festive_table for date {date_str}\")\n", "        except Exception as e:\n", "            print(\"An error occurred:\", str(e))\n", "            print(f\"failed for festival {festival_name}+{date_str}\")\n", "            break\n", "\n", "        current += <PERSON><PERSON><PERSON>(days=1)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}