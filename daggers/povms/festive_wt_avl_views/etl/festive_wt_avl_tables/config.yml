alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: festive_wt_avl_tables
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U07F2C300H5
path: povms/festive_wt_avl_views/etl/festive_wt_avl_tables
paused: false
pool: povms_pool
project_name: festive_wt_avl_views
schedule:
  end_date: '2025-09-02T00:00:00'
  interval: '@once'
  start_date: '2025-06-04T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
