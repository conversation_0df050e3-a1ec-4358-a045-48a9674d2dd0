{"cells": [{"cell_type": "code", "execution_count": null, "id": "8764eb67-2f57-49cc-b855-5b55964022b4", "metadata": {}, "outputs": [], "source": ["!pip install pytz"]}, {"cell_type": "code", "execution_count": null, "id": "727ecf8d-3d4d-4946-91df-f6227689bafb", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "from tqdm import tqdm\n", "\n", "# import datetime\n", "import pytz\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "346a3e46-5af2-48e5-a77c-df1faa7ea3b6", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)\n", "\n", "\n", "def to_trino(df_to_trino, **kwargs):\n", "    start = time.time()\n", "    pb.to_trino(df_to_trino, **kwargs)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Data pushed in table in: \", end - start, \"s\")\n", "\n", "\n", "def gsheet_to_df(sheet_id, tab_name):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            df = pb.from_sheets(sheet_id, tab_name)\n", "            end_time = time.time()\n", "            # Read Runtime\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)  # Runtime Formatted\n", "            # DataFrame Size\n", "            memory_bytes = df.memory_usage(index=True, deep=True).sum()\n", "            size = humansize(memory_bytes)  # Bytes to KB, MB, etc...\n", "            print(\n", "                f\"Read {tab_name} successfully in {attempt+1} attempt with runtime of {formatted_duration} and created a dataframe of size {size}\"\n", "            )\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "17b1829f-65f0-44f6-b1d7-0fb21b4e5291", "metadata": {}, "outputs": [], "source": ["manual = False"]}, {"cell_type": "code", "execution_count": null, "id": "743c49ac-b976-47a0-a2f1-80dfc0ac4db3", "metadata": {}, "outputs": [], "source": ["if manual:\n", "    sheet = pd.read_csv(\"input_backfil_avl.csv\")\n", "else:\n", "    sheet = gsheet_to_df(\"1zeYlST37g5ig8u5DwCvSVoGZtI6VgfJMWgIGCW-w3Cs\", \"festive_list_for_tables\")"]}, {"cell_type": "code", "execution_count": null, "id": "57308209-6d40-4ff7-9184-8dc1b6c58fe5", "metadata": {}, "outputs": [], "source": ["sheet"]}, {"cell_type": "code", "execution_count": null, "id": "aa3eccda-52af-430f-80aa-b30207158682", "metadata": {}, "outputs": [], "source": ["column_dtypes_sql1 = [\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product category/type\"},\n", "    {\"name\": \"cy_search_spike\", \"type\": \"double\", \"description\": \"Current year search spike\"},\n", "    {\"name\": \"py_search_spike\", \"type\": \"double\", \"description\": \"Previous year search spike\"},\n", "    {\"name\": \"critical_flag\", \"type\": \"integer\", \"description\": \"Flag for critical spike\"},\n", "    {\"name\": \"festive_core_flag\", \"type\": \"integer\", \"description\": \"Flag for festive core spike\"},\n", "]\n", "\n", "kwargs_sql1 = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_search_spike_flags\",\n", "    \"column_dtypes\": column_dtypes_sql1,\n", "    \"primary_key\": [\"festival_name\", \"product_type\"],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Flags based on search spikes for each product_type in a festival\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1ecfdbbf-dace-4c16-86b0-43441b56e04b", "metadata": {}, "outputs": [], "source": ["column_dtypes_sql2 = [\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival name\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar\", \"description\": \"Product category/type\"},\n", "]\n", "\n", "kwargs_sql2 = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_in_out\",\n", "    \"column_dtypes\": column_dtypes_sql2,\n", "    \"primary_key\": [\"product_type\", \"festival_name\"],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Product types tagged as in_out\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "3a06d65d-83ed-4195-ac2d-bc40428b7f1a", "metadata": {}, "outputs": [], "source": ["column_dtypes_sql3 = [\n", "    {\"name\": \"festival_name\", \"type\": \"varchar\", \"description\": \"Festival name\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Backend facility identifier\"},\n", "    {\n", "        \"name\": \"weight\",\n", "        \"type\": \"double\",\n", "        \"description\": \"Proportional inventory share at BE facility\",\n", "    },\n", "]\n", "\n", "kwargs_sql3 = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"festive_be_facility_weights\",\n", "    \"column_dtypes\": column_dtypes_sql3,\n", "    \"primary_key\": [\"be_facility_id\", \"festival_name\"],\n", "    \"partition_key\": [\"festival_name\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Relative weights of backend facilities for each festival--Pan India\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "84f9d23f-d408-41b9-b3f7-c04f0c6cbff5", "metadata": {}, "outputs": [], "source": ["sql_1_template = \"\"\"\n", "with raw as (\n", "  select\n", "    festival_name,\n", "    product_type,\n", "    try(\n", "      sum(cy_search_device_count_festival) / sum(cy_search_device_count_bau)\n", "    ) as cy_search_spike,\n", "    try(\n", "      sum(py_search_device_count_festival) / sum(py_search_device_count_bau)\n", "    ) as py_search_spike\n", "  from\n", "    blinkit_iceberg.supply_etls.festival_forecast_final_date_city_ptype\n", "  where\n", "    festival_name = '{festival_name}'\n", "  group by\n", "    1,\n", "    2\n", "),\n", "item_flag as (\n", "  select\n", "    festival_name,\n", "    product_type,\n", "    cy_search_spike,\n", "    py_search_spike,\n", "    case when cy_search_spike >= 1.5 then 1 else 0 end as critical_flag,\n", "    case when cy_search_spike >= 3 then 1 else 0 end as festive_core_flag\n", "  from\n", "    raw\n", ")\n", "select\n", "  *\n", "from\n", "  item_flag  \n", "\"\"\"\n", "\n", "sql_2_template = \"\"\"\n", "with product_item_mapping as (\n", "  select\n", "    product_id,\n", "    count(distinct item_id) as sku_count\n", "  from\n", "    blinkit_iceberg.dwh.dim_item_product_offer_mapping\n", "  where\n", "    is_current = true\n", "  group by\n", "    1\n", "  having\n", "    count(distinct item_id) = 1\n", "),\n", "pid_to_ptype as (\n", "  select\n", "    distinct dp.product_id,\n", "    dp.product_type_id,\n", "    product_type\n", "  from\n", "    dwh.dim_product dp\n", "    inner join product_item_mapping pim on dp.product_id = pim.product_id\n", "  where\n", "    is_current = true\n", "    and product_type not in ('combo', 'Combo')\n", "),\n", "carts_ptype as (\n", "  select\n", "    order_create_dt_ist as date_,\n", "    product_type,\n", "    count(distinct cart_id) as carts\n", "  from\n", "    dwh.fact_sales_order_item_details x\n", "    left join pid_to_ptype dp on x.product_id = dp.product_id\n", "    left join supply_etls.outlet_details od on x.outlet_id = od.inv_outlet_id\n", "  where\n", "    (\n", "      (\n", "        order_create_dt_ist between date '{festive_start_date}'\n", "        and date '{festive_end_date}'\n", "      ) \n", "      or (\n", "        order_create_dt_ist between date '{bau_start_date}'\n", "        and date '{bau_end_date}'\n", "      )\n", "    )\n", "    and order_current_status = 'DELIVERED'\n", "    and order_type not in (\n", "      'InternalForwardOrder',\n", "      'InternalReverseOrder',\n", "      'DropShippingInternalReverseOrder'\n", "    )\n", "  group by\n", "    1,\n", "    2\n", "),\n", "festive_tagging as (\n", "  select\n", "    product_type\n", "  from\n", "    carts_ptype\n", "  group by\n", "    1\n", "  having\n", "    (\n", "      coalesce(\n", "        avg(\n", "          case\n", "            when date_ between date '{bau_start_date}'\n", "            and date '{bau_end_date}' then carts\n", "          end\n", "        ),\n", "        0\n", "      ) / nullif(\n", "        max(\n", "          case\n", "            when date_ between date '{festive_start_date}'\n", "            and date '{festive_end_date}'\n", "            then carts\n", "          end\n", "        ),\n", "        0\n", "      )\n", "    ) <= 0.15\n", "    and max(\n", "      case\n", "        when date_ between date '{festive_start_date}'\n", "            and date '{festive_end_date}'\n", "        then carts\n", "      end\n", "    ) >= 1000\n", "    and coalesce(\n", "      avg(\n", "        case\n", "          when date_ between date '{bau_start_date}'\n", "            and date '{bau_end_date}' then carts\n", "        end\n", "      ),\n", "      0\n", "    ) < 1000\n", ")\n", "select\n", "  '{festival_name}' as festival_name,\n", "  *\n", "from\n", "  festive_tagging\n", "\"\"\"\n", "\n", "sql_3_template = \"\"\"\n", "with raw as (\n", "  select\n", "    irm.insert_ds_ist,\n", "    irm.hour_,\n", "    irm.be_facility_id,\n", "    irm.be_facility_name,\n", "    irm.fe_inv_outlet_id,\n", "    irm.fe_facility_id,\n", "    irm.fe_inv_outlet_name,\n", "    irm.item_id,\n", "    sum(coalesce(oiadc.aps_adjusted, 0)) as cpd\n", "  from\n", "    supply_etls.inventory_replenishment_metrics irm\n", "    left join ars.outlet_item_aps_derived_cpd oiadc on irm.fe_inv_outlet_id = oiadc.outlet_id\n", "    and irm.item_id = oiadc.item_id\n", "  where\n", "    irm.insert_ds_ist = date '{sale_start_date}'\n", "    and oiadc.insert_ds_ist = '{sale_start_date}'\n", "    and hour_ = (\n", "      select max(hour_)\n", "      from supply_etls.inventory_replenishment_metrics\n", "      where insert_ds_ist = date '{sale_start_date}'\n", "    )\n", "    and (\n", "      active_outlet_flag = 1\n", "      or active_outlet_flag is null\n", "    )\n", "    and item_substate in (1, 3)\n", "  group by\n", "    1,2,3,4,5,6,7,8\n", "),\n", "be_raw as (\n", "  select\n", "    be_facility_id,\n", "    sum(cpd) as final_ob\n", "  from\n", "    raw\n", "  group by\n", "    1\n", "),\n", "be_tot as (\n", "  select\n", "    sum(final_ob) as final_tot\n", "  from\n", "    be_raw\n", ")\n", "select\n", "  '{festival_name}' as festival_name,\n", "  be_facility_id,\n", "  final_ob / final_tot as weight\n", "  \n", "from\n", "  be_raw br\n", "  cross join be_tot bt\n", "group by\n", "  1,\n", "  2,\n", "  3\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "34d0cc80-d39a-4587-b1d5-b7cdbc2cacf6", "metadata": {}, "outputs": [], "source": ["for _, row in sheet.iterrows():\n", "    print(f\"started for {row['festival_name']}\")\n", "    sql1 = sql_1_template.format(festival_name=row[\"festival_name\"])\n", "    sql2 = sql_2_template.format(\n", "        festive_start_date=row[\"festive_start_date\"],\n", "        festive_end_date=row[\"festive_end_date\"],\n", "        bau_start_date=row[\"bau_start_date\"],\n", "        bau_end_date=row[\"bau_end_date\"],\n", "        festival_name=row[\"festival_name\"],\n", "    )\n", "    sql3 = sql_3_template.format(\n", "        sale_start_date=row[\"sale_start_date\"], festival_name=row[\"festival_name\"]\n", "    )\n", "    try:\n", "        to_trino(sql1, **kwargs_sql1)\n", "        print(\"😎😎😎 Data successfully pushed into the flag_table_successfully! ) 🚀 \")\n", "        to_trino(sql2, **kwargs_sql2)\n", "        print(\"😎😎😎 Data successfully pushed into the in_out_table! ) 🚀 \")\n", "        to_trino(sql3, **kwargs_sql3)\n", "        print(\"😎😎😎 Data successfully pushed into the be_pan_India_table! ) 🚀 \")\n", "    except Exception as e:\n", "        print(f\"failed for festival {row['festival_name']}\")\n", "        print(f\"😞 Something went wrong: {e} ❌\")\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "id": "d8cadf9b-56aa-4791-b1ad-29220508d2d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5eaa206f-55e0-497c-b4c8-b179f965f4df", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6109cbbb-ee83-4c71-a3db-a644991b14da", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f2ca5129-363c-4f6b-b301-d7580ff2ac7e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cbdc8030-f71f-4cca-89a1-bd82038f8dd2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9aad43b7-c171-41bd-a9dc-5cd2952a62ac", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}