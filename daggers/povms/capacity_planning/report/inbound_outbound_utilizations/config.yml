alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: inbound_outbound_utilizations
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
  retries: 2
  retry_delay_in_seconds: 30
owner:
  email: <EMAIL>
  slack_id: U07JJLG6KN0
path: povms/capacity_planning/report/inbound_outbound_utilizations
paused: false
pool: povms_pool
project_name: capacity_planning
schedule:
  end_date: '2025-08-15T00:00:00'
  interval: 30 0,6 * * *
  start_date: '2025-05-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 12
