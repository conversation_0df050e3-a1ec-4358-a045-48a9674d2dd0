{"cells": [{"cell_type": "code", "execution_count": null, "id": "419abc9c-531c-4409-972f-4b24ba371aaa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "# !pip install pymysql\n", "# import pymysql\n", "\n", "import logging\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "ce73f677-9026-4ba8-b732-412777624bc1", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\""]}, {"cell_type": "code", "execution_count": null, "id": "a6543071-7fde-4a07-ba00-defef16c75aa", "metadata": {}, "outputs": [], "source": ["suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "fddb2ae3-7fec-4c5f-a3e0-90ccc31f8c1d", "metadata": {}, "outputs": [], "source": ["def sheets(data, operation, sheet_id, sheet_name, clear_cache=True):\n", "    max_retries = 5\n", "    retry_interval = 10  # in seconds\n", "\n", "    for attempt in range(max_retries):\n", "        try:\n", "            start_time = time.time()  # Start timing\n", "\n", "            if operation == \"write\":\n", "                pb.to_sheets(data, sheet_id, sheet_name)\n", "                end_time = time.time()  # End timing\n", "                duration = end_time - start_time\n", "                formatted_duration = format_time(duration)\n", "                print(f\"Attempt {attempt + 1} for {operation}... done! Time: {formatted_duration}\")\n", "\n", "            elif operation == \"read\":\n", "                fetched_data = pb.from_sheets(sheet_id, sheet_name)\n", "                end_time = time.time()  # End timing\n", "                duration = end_time - start_time\n", "                formatted_duration = format_time(duration)\n", "                print(f\"Attempt {attempt + 1} for {operation}... done! Time: {formatted_duration}\")\n", "                return fetched_data\n", "\n", "            break  # Break out of the loop if successful\n", "\n", "        except Exception as e:\n", "            logger.error(f\"{e}\")\n", "\n", "            if attempt < max_retries - 1:\n", "                print(f\"Retrying in {retry_interval} seconds...\")\n", "                time.sleep(retry_interval)\n", "            else:\n", "                print(f\"Max retries reached. Unable to perform {operation} operation.\")\n", "                return None  # Return None if max retries reached and break out of the loop"]}, {"cell_type": "code", "execution_count": null, "id": "8b32d46e-9cde-401c-949b-6e8f40ff8614", "metadata": {}, "outputs": [], "source": ["updated_at_ist = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "date = pd.to_datetime((datetime.now() + timedelta(hours=5.5)).date())\n", "print(updated_at_ist), print(date)"]}, {"cell_type": "markdown", "id": "5f012cc0-fee4-47d5-9a3f-3a5bf8273b3d", "metadata": {}, "source": ["# outlet details"]}, {"cell_type": "code", "execution_count": null, "id": "fc908047-0f0f-464a-8c86-91751d517224", "metadata": {}, "outputs": [], "source": ["def outlet_details():\n", "    outlet_details = \"\"\"\n", "    \n", "    select\n", "        hot_outlet_id as outlet_id,\n", "        facility_id,\n", "        facility_name,\n", "        store_type,\n", "        taggings\n", "        \n", "            from supply_etls.outlet_details\n", "                \n", "                group by 1,2,3,4,5\n", "    \n", "    \"\"\"\n", "    return read_sql_query(outlet_details, trino)\n", "\n", "\n", "outlet_details = outlet_details()\n", "\n", "print(outlet_details.shape)\n", "outlet_details.head(2)"]}, {"cell_type": "markdown", "id": "6827af3a-dd10-4fdb-9f1c-d8c8ab69347f", "metadata": {}, "source": ["# capacity input details"]}, {"cell_type": "code", "execution_count": null, "id": "09aab025-6c60-4921-966a-e728893702f6", "metadata": {}, "outputs": [], "source": ["capacity = sheets(\n", "    None,\n", "    \"read\",\n", "    \"119qJiIDwY7aaB2_amXvZbSMkRK8Y0OOZjRCyzCFba44\",\n", "    \"capacity_planning\",\n", ")\n", "\n", "capacity.to_csv(\"capacity.csv\", index=False)\n", "\n", "capacity = pd.read_csv(\"capacity.csv\", skiprows=1)\n", "\n", "capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6016cc01-bd01-426f-ae80-6b1f5340a490", "metadata": {}, "outputs": [], "source": ["capacity = capacity.melt(\n", "    id_vars=[\n", "        \"outlet_id\",\n", "        \"assortment_type\",\n", "        \"type\",\n", "        \"total_capacity\",\n", "    ],\n", "    var_name=\"insert_ds_ist\",\n", "    value_name=\"daily_capacity\",\n", ")\n", "\n", "capacity[\"insert_ds_ist\"] = pd.to_datetime(capacity[\"insert_ds_ist\"])\n", "\n", "capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "ae52edfc-dfb1-4b9b-9a40-637ad62d3557", "metadata": {}, "outputs": [], "source": ["adding_new_capacity = capacity.copy()\n", "\n", "adding_new_capacity = adding_new_capacity[\n", "    adding_new_capacity[\"assortment_type\"].isin([\"perishable\", \"fnv\", \"milk\"])\n", "]\n", "\n", "adding_new_capacity = adding_new_capacity.replace([\"po_inbound\"], [\"ds_transfer\"])\n", "\n", "capacity = capacity[\n", "    capacity[\"assortment_type\"].isin([\"packaged goods\", \"frozen\", \"perishable\", \"fnv\", \"milk\"])\n", "]\n", "\n", "final_base = capacity.append(adding_new_capacity)\n", "\n", "final_base.columns = [\n", "    \"outlet_id\",\n", "    \"assortment_type\",\n", "    \"type_\",\n", "    \"monthly_capacity\",\n", "    \"insert_ds_ist\",\n", "    \"daily_capacity\",\n", "]\n", "\n", "# adding facility id\n", "final_base = pd.merge(\n", "    final_base,\n", "    outlet_details[[\"outlet_id\", \"facility_id\"]],\n", "    on=[\"outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "print(final_base.shape)\n", "final_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e63f9080-2791-4bfe-8c6c-03f3c254dcd9", "metadata": {}, "outputs": [], "source": ["final_base[[\"outlet_id\", \"monthly_capacity\", \"daily_capacity\"]] = (\n", "    final_base[[\"outlet_id\", \"monthly_capacity\", \"daily_capacity\"]].fillna(0).astype(int)\n", ")\n", "\n", "final_base[\"updated_at_ist\"] = updated_at_ist\n", "\n", "final_base = final_base[\n", "    [\n", "        \"updated_at_ist\",\n", "        \"insert_ds_ist\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"type_\",\n", "        \"monthly_capacity\",\n", "        \"daily_capacity\",\n", "    ]\n", "]\n", "\n", "final_base = final_base.replace([\"skus_inbound_po\"], [\"po_inbound_skus\"])\n", "final_base = final_base.replace([\"skus_inbound_sto\"], [\"sto_inbound_skus\"])\n", "\n", "cols_to_update = [\"monthly_capacity\", \"daily_capacity\"]\n", "final_base[cols_to_update] = final_base[cols_to_update].fillna(0).astype(int)\n", "\n", "print(final_base.shape)\n", "final_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "564be96e-fb28-4f4d-aec2-717220d9b370", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"table updated timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"insert_ds_ist\",\n", "        \"type\": \"date\",\n", "        \"description\": \"capacity consumption date/partition key\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"outlet facility id\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"items relate to which category\",\n", "    },\n", "    {\"name\": \"type_\", \"type\": \"varchar\", \"description\": \"inbound/outbound description\"},\n", "    {\n", "        \"name\": \"monthly_capacity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total month capacity\",\n", "    },\n", "    {\"name\": \"daily_capacity\", \"type\": \"integer\", \"description\": \"day wise capacity\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7fee7a62-85ab-488c-a1d9-c04f92e048dd", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inbound_and_outbound_capacity\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"outlet_id\", \"facility_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"inward and outward capacity details\",\n", "}\n", "\n", "pb.to_trino(final_base, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "ee627f41-887e-46f9-9cb9-86305abee8d0", "metadata": {}, "outputs": [], "source": ["del capacity, adding_new_capacity, final_base"]}, {"cell_type": "markdown", "id": "bda8f003-81cb-477a-9f9f-29a8a88a1b8b", "metadata": {}, "source": ["# inward-outward quantity"]}, {"cell_type": "markdown", "id": "7d948cb9-2428-42d0-9f58-395f02f8d7db", "metadata": {}, "source": ["# hp inward details"]}, {"cell_type": "code", "execution_count": null, "id": "0bcc119a-07f0-4f8d-b9d3-530b1f2eff37", "metadata": {}, "outputs": [], "source": ["def hp_inward():\n", "    hp_inward = \"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select\n", "            item_id,\n", "            assortment_type,\n", "            handling_type\n", "\n", "                from supply_etls.item_details\n", "        ),\n", "\n", "    partner_mapping as\n", "        (select \n", "            item_id, \n", "            cast(partner_item_id as int) as partner_item_id \n", "\n", "                from po.edi_integration_partner_item_mapping\n", "\n", "                    where \n", "                        active\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "\n", "    partner_outlet_mapping as\n", "        (select\n", "            partner_outlet_id,\n", "            outlet_id\n", "\n", "                from po.edi_integration_partner_outlet_mapping\n", "\n", "                    where \n", "                        active\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "\n", "    po_details as\n", "        (select\n", "            id,\n", "            warehouse_code\n", "\n", "                from zomato.hp_wms.purchase_order\n", "\n", "                    where \n", "                        dt >= replace(cast(date(current_date) - interval '30' day as varchar), '-','')\n", "        ),\n", "\n", "    po_item_details as\n", "        (select\n", "            id,\n", "            purchase_order_id,\n", "            product_number\n", "\n", "                from zomato.hp_wms.purchase_order_items\n", "\n", "                    where\n", "                        dt >= replace(cast(date(current_date) - interval '30' day as varchar), '-','')\n", "        ),\n", "\n", "    po_grn as\n", "        (select\n", "            created_at,\n", "            purchase_order_item_id,\n", "            delivered_quantity\n", "\n", "                from zomato.hp_wms.po_grn_item\n", "\n", "                    where\n", "                        dt >= replace(cast(date(current_date) - interval '30' day as varchar), '-','')\n", "        ),\n", "\n", "    po_grn_status as\n", "        (select\n", "            id\n", "\n", "                from zomato.hp_wms.po_grn_mapping\n", "\n", "                    where\n", "                        dt >= replace(cast(date(current_date) - interval '30' day as varchar), '-','')\n", "                        and\n", "                            grn_status = 'COMPLETED'\n", "        ),\n", "\n", "    adding as\n", "        (select\n", "            case\n", "                when (id.assortment_type in ('Frozen','Ice Cream') and extract(hour from (pg.created_at + interval '330' minute)) >= 19) then (date(pg.created_at + interval '330' minute) + interval '1' day)\n", "                else date(pg.created_at + interval '330' minute)\n", "            end as insert_ds_ist,\n", "\n", "            pom.outlet_id,\n", "\n", "            'grn' as business_type,\n", "\n", "            id.handling_type,\n", "\n", "            'po_inbound' as type_,\n", "\n", "            case \n", "                when id.assortment_type in ('Meat') then 'perishable'\n", "                when id.assortment_type in ('Ice Cream') then 'frozen'\n", "                else lower(id.assortment_type)\n", "            end as assortment_type,\n", "\n", "            'auto' as trigger_status,\n", "\n", "            count(distinct pm.item_id) as unique_skus_count,\n", "            count(pm.item_id) as skus_count,\n", "            sum(pg.delivered_quantity) as quantity,\n", "            count(distinct case when extract(hour from pg.created_at + interval '330' minute) in (0,1,2,3,4,5) then pm.item_id end) as midnight_unique_skus_count,\n", "            count(case when extract(hour from pg.created_at + interval '330' minute) in (0,1,2,3,4,5) then pm.item_id end) as midnight_skus_count,\n", "            sum(case when extract(hour from pg.created_at + interval '330' minute) in (0,1,2,3,4,5) then pg.delivered_quantity else 0 end) as midnight_quantity\n", "\n", "                from po_grn pg\n", "\n", "                    join\n", "                        po_item_details pid on pid.id = pg.purchase_order_item_id\n", "\n", "                    join\n", "                        po_details pd on pd.id = pid.purchase_order_id\n", "\n", "                    join\n", "                        partner_mapping pm on pm.partner_item_id = pid.product_number\n", "\n", "                    join\n", "                        item_details id on id.item_id = pm.item_id\n", "\n", "                    join\n", "                        partner_outlet_mapping pom on pom.partner_outlet_id = pd.warehouse_code\n", "\n", "                        group by 1,2,3,4,5,6,7\n", "        )\n", "\n", "            select *,\n", "                0 AS from_be_unique_skus_count,\n", "                0 AS from_be_skus_count,\n", "                0 AS from_be_quantity,\n", "                0 AS midnight_from_be_unique_skus_count,\n", "                0 AS midnight_from_be_skus_count,\n", "                0 AS midnight_from_be_quantity,\n", "                0 AS courier_unique_skus_count,\n", "                0 AS courier_skus_count,\n", "                0 AS courier_quantity,\n", "                0 AS courier_midnight_skus_count,\n", "                0 AS courier_midnight_unique_skus_count,\n", "                0 AS courier_midnight_quantity\n", "                from adding\n", "                -- where\n", "                    -- REGEXP_REPLACE(to_char(insert_ds_ist, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '30' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "    \n", "    \"\"\"\n", "    return read_sql_query(hp_inward, trino)\n", "\n", "\n", "hp_inward = hp_inward()\n", "\n", "print(hp_inward.shape)\n", "hp_inward.head(2)"]}, {"cell_type": "markdown", "id": "975a45c2-9d82-4677-9b2e-f215d92311dd", "metadata": {}, "source": ["# ds transfer (create and billing)"]}, {"cell_type": "code", "execution_count": null, "id": "acd7bb64-251e-4eb1-93ee-bd3357134e0d", "metadata": {}, "outputs": [], "source": ["def ds_transfer():\n", "    ds_transfer = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select\n", "            hot_outlet_id,\n", "            inv_outlet_id,\n", "            taggings\n", "\n", "                from supply_etls.outlet_details\n", "        ),\n", "\n", "    item_details as\n", "        (select\n", "            item_id,\n", "            assortment_type,\n", "            handling_type\n", "\n", "                from supply_etls.item_details\n", "        ),\n", "\n", "    variant_details as\n", "        (select\n", "            item_id,\n", "            variant_id\n", "\n", "                from rpc.product_product\n", "        ),\n", "\n", "    run_details as\n", "        (select\n", "            run_id\n", "\n", "                from ars.job_run\n", "\n", "                    where \n", "                        started_at > current_date - interval '30' day\n", "                        and \n", "                            lake_active_record\n", "                        and \n", "                            success = 1\n", "                        and \n", "                            json_extract_scalar(simulation_params,'$.run') = 'ars_lite'\n", "                        and \n", "                            json_extract_scalar(simulation_params,'$.manual') = 'true'\n", "\n", "        ),\n", "\n", "    sto_details as\n", "        (select\n", "            sto_id,\n", "            outlet_id,\n", "            created_at\n", "\n", "                from ims.ims_sto_details\n", "\n", "                    where\n", "                        created_at >= current_date - interval '30' day\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "\n", "    sto_item_details as\n", "        (select\n", "            sto_id,\n", "            run_id,\n", "            item_id,\n", "            reserved_quantity\n", "\n", "                from po.sto_items\n", "\n", "                    where\n", "                        created_at >= current_date - interval '30' day\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "\n", "    invoice_details as\n", "        (select\n", "            id,\n", "            grofers_order_id\n", "\n", "                from pos.pos_invoice\n", "\n", "                    where \n", "                        insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                        and \n", "                            lake_active_record\n", "                        and \n", "                            invoice_type_id in (5,14,16)\n", "                        and \n", "                            grofers_order_id != ''\n", "                        and \n", "                            grofers_order_id is not null\n", "    ),\n", "\n", "    invoice_item_details as\n", "        (select\n", "            invoice_id,\n", "            variant_id,\n", "            quantity\n", "\n", "                from pos.pos_invoice_product_details\n", "\n", "                 where \n", "                    insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                    and \n", "                        lake_active_record\n", "        ),\n", "\n", "    adding_invoice_details as\n", "        (select \n", "            cast(grofers_order_id as bigint) as sto_id,\n", "            item_id,\n", "            sum(quantity) as quantity\n", "\n", "                from invoice_details id\n", "\n", "                    join\n", "                        invoice_item_details iid on iid.invoice_id = id.id\n", "\n", "                    join\n", "                        variant_details vd on vd.variant_id = iid.variant_id\n", "\n", "                        group by 1,2\n", "        ),\n", "\n", "    adding as\n", "        (select\n", "            case\n", "                when (id.assortment_type in ('Frozen','Ice Cream') and extract(hour from (sd.created_at + interval '330' minute)) >= 19) then (date(sd.created_at + interval '330' minute) + interval '1' day)\n", "                else date(sd.created_at + interval '330' minute)\n", "            end as insert_ds_ist,\n", "\n", "            case \n", "                when od.hot_outlet_id is null then sd.outlet_id \n", "                else od.hot_outlet_id \n", "            end as outlet_id,\n", "\n", "            id.handling_type,\n", "\n", "            'ds_transfer' as type_,\n", "\n", "            case \n", "                when id.assortment_type in ('Meat') then 'perishable'\n", "                when id.assortment_type in ('Ice Cream') then 'frozen'\n", "                else lower(id.assortment_type) \n", "            end as assortment_type,\n", "\n", "            case when sid.run_id = '0' then 'manual'\n", "                when rd.run_id is not null then 'manual'\n", "                else 'auto' end as trigger_status,\n", "\n", "            count(distinct sid.item_id) as unique_skus_count,\n", "            count(sid.item_id) as skus_count,\n", "            sum(sid.reserved_quantity) as quantity,\n", "\n", "            count(distinct aid.item_id) as unique_billed_skus_count,\n", "            count(aid.item_id) as billed_skus_count,\n", "            sum(aid.quantity) as billed_quantity,\n", "            \n", "            count(distinct case when od.taggings = 'be' then sid.item_id end) as from_be_unique_skus_count,\n", "            count(case when od.taggings = 'be' then sid.item_id end) as from_be_skus_count,\n", "            sum(case when od.taggings = 'be' then sid.reserved_quantity else 0 end) as from_be_quantity,\n", "\n", "            count(distinct case when od.taggings = 'be' then aid.item_id end) as from_be_unique_billed_skus_count,\n", "            count(case when od.taggings = 'be' then aid.item_id end) as from_be_billed_skus_count,\n", "            sum(case when od.taggings = 'be' then aid.quantity else 0 end) as from_be_billed_quantity,\n", "            \n", "            count(distinct case when extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then sid.item_id end) as midnight_unique_skus_count,\n", "            count(case when extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then sid.item_id end) as midnight_skus_count,\n", "            sum(case when extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then sid.reserved_quantity else 0 end) as midnight_quantity,\n", "            \n", "            count(distinct case when extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then aid.item_id end) as midnight_unique_billed_skus_count,\n", "            count(case when extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then aid.item_id end) as midnight_billed_skus_count,\n", "            sum(case when extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then aid.quantity else 0 end) as midnight_billed_quantity,\n", "            \n", "            count(distinct case when od.taggings = 'be' and extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then sid.item_id end) as midnight_from_be_unique_skus_count,\n", "            count(case when od.taggings = 'be' and extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then sid.item_id end) as midnight_from_be_skus_count,\n", "            sum(case when od.taggings = 'be' and extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then sid.reserved_quantity else 0 end) as midnight_from_be_quantity,\n", "            \n", "            count(distinct case when od.taggings = 'be' and extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then aid.item_id end) as midnight_from_be_unique_billed_skus_count,\n", "            count(case when od.taggings = 'be' and extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then aid.item_id end) as midnight_from_be_billed_skus_count,\n", "            sum(case when od.taggings = 'be' and extract(hour from sd.created_at + interval '330' minute) in (0,1,2,3,4,5) then aid.quantity else 0 end) as midnight_from_be_billed_quantity\n", "\n", "                from sto_details sd\n", "\n", "                    join\n", "                        sto_item_details sid on sid.sto_id = sd.sto_id\n", "\n", "                    left join\n", "                        run_details rd on rd.run_id = sid.run_id\n", "\n", "                    left join\n", "                        adding_invoice_details aid on aid.sto_id = sd.sto_id and aid.item_id = sid.item_id\n", "\n", "                    join\n", "                        item_details id on id.item_id = sid.item_id\n", "\n", "                    join\n", "                        outlet_details od on od.inv_outlet_id = sd.outlet_id\n", "\n", "                        group by 1,2,3,4,5,6\n", "        ),\n", "\n", "    final_view as\n", "        (\n", "            select\n", "                insert_ds_ist,\n", "                outlet_id,\n", "                'create' as business_type,\n", "                handling_type,\n", "                type_,\n", "                assortment_type,\n", "                trigger_status,\n", "                unique_skus_count,\n", "                skus_count,\n", "                quantity,\n", "                from_be_unique_skus_count,\n", "                from_be_skus_count,\n", "                from_be_quantity,\n", "                midnight_unique_skus_count,\n", "                midnight_skus_count,\n", "                midnight_quantity,\n", "                midnight_from_be_unique_skus_count,\n", "                midnight_from_be_skus_count,\n", "                midnight_from_be_quantity\n", "\n", "                    from adding\n", "\n", "                union all\n", "\n", "            select\n", "                insert_ds_ist,\n", "                outlet_id,\n", "                'billed' as business_type,\n", "                handling_type,\n", "                type_,\n", "                assortment_type,\n", "                trigger_status,\n", "                unique_billed_skus_count as unique_skus_count,\n", "                billed_skus_count as skus_count,\n", "                billed_quantity as quantity,\n", "                from_be_unique_billed_skus_count as from_be_unique_skus_count,\n", "                from_be_billed_skus_count as from_be_skus_count,\n", "                from_be_billed_quantity as from_be_quantity,\n", "                midnight_unique_billed_skus_count as midnight_unique_skus_count,\n", "                midnight_billed_skus_count as midnight_skus_count,\n", "                midnight_billed_quantity as midnight_quantity,\n", "                midnight_from_be_unique_billed_skus_count as midnight_from_be_unique_skus_count,\n", "                midnight_from_be_billed_skus_count as midnight_from_be_skus_count,\n", "                midnight_from_be_billed_quantity as midnight_from_be_quantity\n", "\n", "                    from adding\n", "        )\n", "\n", "            select *,\n", "                0 AS courier_unique_skus_count,\n", "                0 AS courier_skus_count,\n", "                0 AS courier_quantity,\n", "                0 AS courier_midnight_skus_count,\n", "                0 AS courier_midnight_unique_skus_count,\n", "                0 AS courier_midnight_quantity\n", "                    from final_view\n", "                        where \n", "                            -- REGEXP_REPLACE(to_char(insert_ds_ist, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '30' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "                            -- and\n", "                                (unique_skus_count + skus_count + quantity) > 0\n", "\n", "    \"\"\"\n", "    return read_sql_query(ds_transfer, trino)\n", "\n", "\n", "ds_transfer = ds_transfer()\n", "\n", "print(ds_transfer.shape)\n", "ds_transfer.head(2)"]}, {"cell_type": "markdown", "id": "ed7cb42e-cbfa-4d6b-bf11-99d4a748d60e", "metadata": {}, "source": ["# non hp backend inward"]}, {"cell_type": "code", "execution_count": null, "id": "0a2d0f87-71f7-4d1b-b53c-12495e02dff6", "metadata": {}, "outputs": [], "source": ["def backend_inward():\n", "    backend_inward = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select\n", "            hot_outlet_id,\n", "            inv_outlet_id\n", "\n", "                from supply_etls.outlet_details\n", "        ),\n", "\n", "    item_details as\n", "        (select\n", "            item_id,\n", "            assortment_type,\n", "            handling_type\n", "\n", "                from supply_etls.item_details\n", "        ),\n", "\n", "    variant_details as\n", "        (select\n", "            item_id,\n", "            variant_id\n", "\n", "                from rpc.product_product\n", "        ),\n", "\n", "    inward_invoice_details as\n", "        (select\n", "            grn_id,\n", "            created_at,\n", "            outlet_id,\n", "            po_id,\n", "            valid_po,\n", "            source_type,\n", "            vendor_invoice_id\n", "\n", "                from ims.ims_inward_invoice\n", "\n", "                    where \n", "                        insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                        and\n", "                            lake_active_record\n", "                        and\n", "                            source_type in (1,2)\n", "        ),\n", "\n", "    inward_item_details as\n", "        (select\n", "            grn_id,\n", "            variant_id,\n", "            \"delta\"\n", "\n", "                from ims.ims_inventory_stock_details\n", "\n", "                    where \n", "                        insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "\n", "    po_details as\n", "        (select\n", "            po_number,\n", "            delivery_type\n", "\n", "                from po.purchase_order\n", "\n", "                    where \n", "                        created_at >= current_date - interval '100' day\n", "                        and\n", "                            lake_active_record\n", "                        and\n", "                            po_type_id not in (11,9)\n", "                        and\n", "                            active = 1\n", "        ),\n", "        \n", "    sto_tagging_details as \n", "        (select \n", "            od.taggings as from_transfer_taggings,\n", "            sto.outlet_id as from_transfer_outlet_id,\n", "            pi.invoice_id\n", "                from supply_etls.outlet_details as od\n", "                join po.sto as sto on od.inv_outlet_id = sto.outlet_id\n", "                join pos.pos_invoice as pi on cast(sto.id as varchar) = pi.grofers_order_id\n", "                    where od.active = 1 and sto.active = 1\n", "                        and sto.lake_active_record and pi.lake_active_record\n", "                        and pi.insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                        and sto.created_at >= current_date - interval '30' day\n", "        ),\n", "\n", "    adding as\n", "        (select\n", "            case\n", "                when (id.assortment_type in ('Frozen','Ice Cream') and extract(hour from (iii.created_at + interval '330' minute)) >= 19) then (date(iii.created_at + interval '330' minute) + interval '1' day)\n", "                else date(iii.created_at + interval '330' minute)\n", "            end as insert_ds_ist,\n", "\n", "            case \n", "                when od.inv_outlet_id is null then iii.outlet_id\n", "                else od.hot_outlet_id\n", "            end as outlet_id,\n", "\n", "            'grn' as business_type,\n", "\n", "            id.handling_type,\n", "\n", "            case \n", "                when (iii.po_id is not null and iii.valid_po = 1) then 'po_inbound'\n", "                when iii.source_type = 2 then 'sto_inbound'\n", "                else null \n", "            end as type_,\n", "\n", "            case \n", "                when id.assortment_type in ('Meat') then 'perishable'\n", "                when id.assortment_type in ('Ice Cream') then 'frozen'\n", "                else lower(id.assortment_type)\n", "            end as assortment_type,\n", "\n", "            'auto' as trigger_status,\n", "\n", "            count(distinct vd.item_id) as unique_skus_count,\n", "            count(vd.item_id) as skus_count,\n", "            sum(iid.\"delta\") as quantity,\n", "\n", "            count(distinct case when std.from_transfer_taggings = 'be' then vd.item_id end) as from_be_unique_skus_count,\n", "            count(case when std.from_transfer_taggings = 'be' then vd.item_id end) as from_be_skus_count,\n", "            sum(case when std.from_transfer_taggings = 'be' then iid.\"delta\" else 0 end) as from_be_quantity,\n", "\n", "            count(distinct case when extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) then vd.item_id end) as midnight_unique_skus_count,\n", "            count(case when extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) then vd.item_id end) as midnight_skus_count,\n", "            sum(case when extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) then iid.\"delta\" end) as midnight_quantity,\n", "\n", "            count(distinct case when std.from_transfer_taggings = 'be' and extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) then vd.item_id end) as midnight_from_be_unique_skus_count,\n", "            count(case when std.from_transfer_taggings = 'be' and extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) then vd.item_id end) as midnight_from_be_skus_count,\n", "            sum(case when std.from_transfer_taggings = 'be' and extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) then iid.\"delta\" else 0 end) as midnight_from_be_quantity,\n", "            \n", "            count(distinct case when pd.delivery_type in (2,3) then vd.item_id end) as courier_unique_skus_count,\n", "            count(case when pd.delivery_type in (2,3) then vd.item_id end) as courier_skus_count,\n", "            sum(case when pd.delivery_type in (2,3) then iid.\"delta\" end) as courier_quantity,\n", "\n", "            count(distinct case when extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) and pd.delivery_type in (2,3) then vd.item_id end) as courier_midnight_unique_skus_count,\n", "            count(case when extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) and pd.delivery_type in (2,3) then vd.item_id end) as courier_midnight_skus_count,\n", "            sum(case when extract(hour from iii.created_at + interval '330' minute) in (0,1,2,3,4,5) and pd.delivery_type in (2,3) then iid.\"delta\" end) as courier_midnight_quantity\n", "\n", "                from inward_invoice_details iii\n", "\n", "                    left join\n", "                        outlet_details od on od.inv_outlet_id = iii.outlet_id\n", "\n", "                    join\n", "                        inward_item_details iid on iid.grn_id = iii.grn_id\n", "\n", "                    join\n", "                        variant_details vd on vd.variant_id = iid.variant_id\n", "\n", "                    left join\n", "                        po_details pd on pd.po_number = iii.po_id\n", "\n", "                    join\n", "                        item_details id on id.item_id = vd.item_id and id.handling_type = 'Non Packaging Material'\n", "                    left join\n", "                        sto_tagging_details as std on iii.vendor_invoice_id = std.invoice_id\n", "\n", "                        group by 1,2,3,4,5,6,7\n", "        )\n", "\n", "            select *\n", "                    from adding\n", "                        where\n", "                            -- REGEXP_REPLACE(to_char(insert_ds_ist, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '30' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "                            -- and\n", "                                type_ is not null\n", "\n", "    \"\"\"\n", "    return read_sql_query(backend_inward, trino)\n", "\n", "\n", "backend_inward = backend_inward()\n", "\n", "print(backend_inward.shape)\n", "backend_inward.head(2)"]}, {"cell_type": "markdown", "id": "94220e30-3ca9-49b8-912a-1e648149ac78", "metadata": {}, "source": ["# hp transfer details"]}, {"cell_type": "code", "execution_count": null, "id": "f5a1ffcd-f585-4c5e-aade-edf8738ccd0c", "metadata": {}, "outputs": [], "source": ["def hp_transfer():\n", "    hp_transfer = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select\n", "            hot_outlet_id,\n", "            inv_outlet_id\n", "    \n", "                from supply_etls.outlet_details\n", "        ),\n", "    \n", "    item_details as\n", "        (select\n", "            item_id,\n", "            assortment_type,\n", "            handling_type\n", "    \n", "                from supply_etls.item_details\n", "        ),\n", "    \n", "    partner_outlet_mapping as\n", "        (select\n", "            outlet_id,\n", "            partner_outlet_id\n", "    \n", "                from po.edi_integration_partner_outlet_mapping\n", "    \n", "                    where\n", "                        active\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "    \n", "    partner_po_details as\n", "        (select\n", "            outlet_id,\n", "            po_id,\n", "            partner_order_id\n", "    \n", "                from po.edi_integration_partner_purchase_order_details\n", "    \n", "                    where\n", "                        created_at >= current_date - interval '30' day\n", "                        and\n", "                            active\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "    \n", "    partner_buyer_details as\n", "        (select\n", "            id,\n", "            warehousecode\n", "    \n", "                from zomato.hp_consumer.buyer_order_requests\n", "    \n", "                    where \n", "                        dt >= replace(cast(date(current_date) - interval '30' day as varchar), '-','')\n", "        ),\n", "    \n", "    \n", "    po_details as\n", "        (select\n", "            id,\n", "            po_number,\n", "            created_at\n", "    \n", "            from po.purchase_order\n", "    \n", "                where \n", "                    created_at >= current_date - interval '30' day\n", "                    and \n", "                        lake_active_record\n", "                    and \n", "                        vendor_id = 13280\n", "                    and \n", "                        po_type_id not in (11,9)\n", "                    and\n", "                        active = 1\n", "        ),\n", "    \n", "    po_item_details as\n", "        (select\n", "            po_id,\n", "            item_id,\n", "            units_ordered\n", "    \n", "                from po.purchase_order_items\n", "    \n", "                    where \n", "                        created_at >= current_date - interval '30' day\n", "                        and\n", "                            lake_active_record\n", "        ),\n", "    \n", "    po_status as\n", "        (select\n", "            po_id,\n", "            po_state_id\n", "            \n", "                from po.purchase_order_status\n", "                \n", "                    where\n", "                        lake_active_record\n", "                        and\n", "                            po_state_id not in (1,4,5,6,7,10,17)\n", "        ),\n", "    \n", "    po_grn as\n", "        (select \n", "            po_id,\n", "            item_id,\n", "            sum(quantity) as quantity\n", "    \n", "                from po.po_grn\n", "    \n", "                    where \n", "                        insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "                        and \n", "                            lake_active_record\n", "    \n", "                        group by 1,2\n", "        ),\n", "    \n", "    adding as\n", "        (select\n", "            case\n", "                when (id.assortment_type in ('Frozen','Ice Cream') and extract(hour from (pd.created_at + interval '330' minute)) >= 19) then (date(pd.created_at + interval '330' minute) + interval '1' day)\n", "                else date(pd.created_at + interval '330' minute)\n", "            end as insert_ds_ist,\n", "    \n", "            pom.outlet_id,\n", "    \n", "            id.handling_type,\n", "    \n", "            'ds_transfer' as type_,\n", "    \n", "            case when id.assortment_type in ('Meat') then 'perishable'\n", "                when id.assortment_type in ('Ice Cream') then 'frozen'\n", "                else lower(id.assortment_type) end as assortment_type,\n", "    \n", "            'auto' as trigger_status,\n", "    \n", "            count(distinct pid.item_id) as unique_skus_count,\n", "            count(pid.item_id) as skus_count,\n", "            sum(pid.units_ordered) as quantity,\n", "    \n", "            count(distinct pg.item_id) as unique_grn_skus_count,\n", "            count(pg.item_id) as grn_skus_count,\n", "            sum(pg.quantity) as grn_quantity,\n", "    \n", "            count(distinct case when extract(hour from pd.created_at + interval '330' minute) in (0,1,2,3,4,5) then pid.item_id end) as midnight_unique_skus_count,\n", "            count( case when extract(hour from pd.created_at + interval '330' minute) in (0,1,2,3,4,5) then pid.item_id end) as midnight_skus_count,\n", "            sum( case when extract(hour from pd.created_at + interval '330' minute) in (0,1,2,3,4,5) then pid.units_ordered end) as midnight_quantity,\n", "    \n", "            count(distinct case when extract(hour from pd.created_at + interval '330' minute) in (0,1,2,3,4,5) then pg.item_id end) as midnight_unique_grn_skus_count,\n", "            count( case when extract(hour from pd.created_at + interval '330' minute) in (0,1,2,3,4,5) then pg.item_id end) as midnight_grn_skus_count,\n", "            sum( case when extract(hour from pd.created_at + interval '330' minute) in (0,1,2,3,4,5) then pg.quantity end) as midnight_grn_quantity\n", "    \n", "                from po_item_details pid\n", "    \n", "                    join\n", "                        po_details pd on pd.id = pid.po_id\n", "    \n", "                    join\n", "                        partner_po_details ppd on ppd.po_id = pd.id\n", "    \n", "                    join\n", "                        partner_buyer_details pbd on pbd.id = ppd.partner_order_id\n", "    \n", "                    join\n", "                        partner_outlet_mapping pom on lower(pom.partner_outlet_id) = lower(pbd.warehousecode)\n", "    \n", "                    left join\n", "                        po_grn pg on pg.po_id = pid.po_id and pg.item_id = pid.item_id\n", "    \n", "                    join\n", "                        item_details id on id.item_id = pid.item_id\n", "                    \n", "                    join\n", "                        po_status ps on ps.po_id = pid.po_id\n", "    \n", "                        group by 1,2,3,4,5,6\n", "    \n", "        ),\n", "    \n", "    final_view as\n", "        (\n", "            select\n", "                insert_ds_ist,\n", "                outlet_id,\n", "                'create' as business_type,\n", "                handling_type,\n", "                type_,\n", "                assortment_type,\n", "                trigger_status,\n", "                unique_skus_count,\n", "                skus_count,\n", "                quantity,\n", "                midnight_unique_skus_count,\n", "                midnight_skus_count,\n", "                midnight_quantity\n", "    \n", "                    from adding\n", "    \n", "                union all\n", "    \n", "            select\n", "                insert_ds_ist,\n", "                outlet_id,\n", "                'grn' as business_type,\n", "                handling_type,\n", "                type_,\n", "                assortment_type,\n", "                trigger_status,\n", "                unique_grn_skus_count as unique_skus_count,\n", "                grn_skus_count as skus_count,\n", "                grn_quantity as quantity,\n", "                midnight_unique_grn_skus_count as midnight_unique_skus_count,\n", "                midnight_grn_skus_count as midnight_skus_count,\n", "                midnight_grn_quantity as midnight_quantity\n", "    \n", "                    from adding\n", "        )\n", "    \n", "            select *,\n", "            0 AS from_be_unique_skus_count,\n", "            0 AS from_be_skus_count,\n", "            0 AS from_be_quantity,\n", "            0 AS midnight_from_be_unique_skus_count,\n", "            0 AS midnight_from_be_skus_count,\n", "            0 AS midnight_from_be_quantity,\n", "            0 AS courier_unique_skus_count,\n", "            0 AS courier_skus_count,\n", "            0 AS courier_quantity,\n", "            0 AS courier_midnight_skus_count,\n", "            0 AS courier_midnight_unique_skus_count,\n", "            0 AS courier_midnight_quantity\n", "            from final_view\n", "                where\n", "                    -- REGEXP_REPLACE(to_char(insert_ds_ist, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '30' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "                    -- and\n", "                        (unique_skus_count + skus_count + quantity) > 0\n", "\n", "    \"\"\"\n", "    return read_sql_query(hp_transfer, trino)\n", "\n", "\n", "hp_transfer = hp_transfer()\n", "\n", "print(hp_transfer.shape)\n", "hp_transfer.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3b3b3205-9785-4bdd-a8f3-2d859365c510", "metadata": {}, "outputs": [], "source": ["# Aligning Columns\n", "cols = list(hp_inward.columns)\n", "ds_transfer = ds_transfer[cols]\n", "backend_inward = backend_inward[cols]\n", "hp_transfer = hp_transfer[cols]"]}, {"cell_type": "code", "execution_count": null, "id": "43bd2233-0f3a-412a-88ba-28f64d9e5ad8", "metadata": {}, "outputs": [], "source": ["final_view = hp_inward.append([ds_transfer, backend_inward, hp_transfer]).drop_duplicates()\n", "\n", "final_view[\"updated_at_ist\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "\n", "final_view[\"insert_ds_ist\"] = pd.to_datetime(final_view[\"insert_ds_ist\"])\n", "\n", "print(final_view.shape)\n", "final_view.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f70ac337-2fec-4982-adcd-7c491a85a7d3", "metadata": {}, "outputs": [], "source": ["final_view = pd.merge(\n", "    final_view, outlet_details[[\"outlet_id\", \"taggings\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "\n", "final_view.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "108eb4f0-9c20-44d7-8d59-defc5f909818", "metadata": {}, "outputs": [], "source": ["final_view[\"insert_ds_ist\"].min()"]}, {"cell_type": "code", "execution_count": null, "id": "b1ee8130-fd72-4fc6-beff-fcfb06bdbb31", "metadata": {}, "outputs": [], "source": ["final_view = final_view[\n", "    [\n", "        \"updated_at_ist\",\n", "        \"insert_ds_ist\",\n", "        \"taggings\",\n", "        \"outlet_id\",\n", "        \"business_type\",\n", "        \"handling_type\",\n", "        \"type_\",\n", "        \"assortment_type\",\n", "        \"trigger_status\",\n", "        \"unique_skus_count\",\n", "        \"skus_count\",\n", "        \"quantity\",\n", "        \"from_be_unique_skus_count\",\n", "        \"from_be_skus_count\",\n", "        \"from_be_quantity\",\n", "        \"midnight_unique_skus_count\",\n", "        \"midnight_skus_count\",\n", "        \"midnight_quantity\",\n", "        \"midnight_from_be_unique_skus_count\",\n", "        \"midnight_from_be_skus_count\",\n", "        \"midnight_from_be_quantity\",\n", "        \"courier_unique_skus_count\",\n", "        \"courier_skus_count\",\n", "        \"courier_quantity\",\n", "        \"courier_midnight_unique_skus_count\",\n", "        \"courier_midnight_skus_count\",\n", "        \"courier_midnight_quantity\",\n", "    ]\n", "]\n", "\n", "cols_to_update = [\n", "    \"unique_skus_count\",\n", "    \"skus_count\",\n", "    \"quantity\",\n", "    \"from_be_unique_skus_count\",\n", "    \"from_be_skus_count\",\n", "    \"from_be_quantity\",\n", "    \"midnight_unique_skus_count\",\n", "    \"midnight_skus_count\",\n", "    \"midnight_quantity\",\n", "    \"midnight_from_be_unique_skus_count\",\n", "    \"midnight_from_be_skus_count\",\n", "    \"midnight_from_be_quantity\",\n", "    \"courier_unique_skus_count\",\n", "    \"courier_skus_count\",\n", "    \"courier_quantity\",\n", "    \"courier_midnight_unique_skus_count\",\n", "    \"courier_midnight_skus_count\",\n", "    \"courier_midnight_quantity\",\n", "]\n", "\n", "final_view[cols_to_update] = final_view[cols_to_update].fillna(0).astype(int)\n", "\n", "print(final_view.shape)\n", "final_view.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c0395d15-6a32-4ee9-817f-a8c674eea68b", "metadata": {}, "outputs": [], "source": ["final_view.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "b5041bde-793d-4399-ae5e-b8335661cfbe", "metadata": {"tags": []}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"table updated at\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"item relate to\"},\n", "    {\"name\": \"taggings\", \"type\": \"varchar\", \"description\": \"fe-be tagging\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"business_type\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"handling_type\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"type_\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"items relate to which category\"},\n", "    {\"name\": \"trigger_status\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"unique_skus_count\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"skus_count\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"quantity\", \"type\": \"integer\", \"description\": \"specific date capacity\"},\n", "    {\n", "        \"name\": \"from_be_unique_skus_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"from be unique skus count\",\n", "    },\n", "    {\"name\": \"from_be_skus_count\", \"type\": \"integer\", \"description\": \"from be skus count\"},\n", "    {\"name\": \"from_be_quantity\", \"type\": \"integer\", \"description\": \"from be quantity\"},\n", "    {\n", "        \"name\": \"midnight_unique_skus_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"0 to 6 hour unique skus count\",\n", "    },\n", "    {\"name\": \"midnight_skus_count\", \"type\": \"integer\", \"description\": \"0 to 6 hour skus count\"},\n", "    {\"name\": \"midnight_quantity\", \"type\": \"integer\", \"description\": \"0 to 6 hour quantity\"},\n", "    {\n", "        \"name\": \"midnight_from_be_unique_skus_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"o to 6 hour from be unique skus count\",\n", "    },\n", "    {\n", "        \"name\": \"midnight_from_be_skus_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"0 to 6 hour from be skus count\",\n", "    },\n", "    {\n", "        \"name\": \"midnight_from_be_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"0 to 6 hour from be quantity\",\n", "    },\n", "    {\n", "        \"name\": \"courier_unique_skus_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"courier_unique_skus_count\",\n", "    },\n", "    {\"name\": \"courier_skus_count\", \"type\": \"integer\", \"description\": \"courier_skus_count\"},\n", "    {\"name\": \"courier_quantity\", \"type\": \"integer\", \"description\": \"courier_quantity\"},\n", "    {\n", "        \"name\": \"courier_midnight_unique_skus_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"courier 0 to 6 hour unique skus count\",\n", "    },\n", "    {\n", "        \"name\": \"courier_midnight_skus_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"courier 0 to 6 hour skus count\",\n", "    },\n", "    {\n", "        \"name\": \"courier_midnight_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"courier 0 to 6 hour quantity\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "92afe5d0-37f2-4268-99ab-219bfafa5e57", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inbound_and_outbound_quantity\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"outlet_id\",\n", "        \"business_type\",\n", "        \"handling_type\",\n", "        \"type_\",\n", "        \"assortment_type\",\n", "        \"trigger_status\",\n", "    ],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"upsert\",  # append, truncate or upsert,\n", "    \"table_description\": \"inward and outward quantity details\",\n", "}\n", "\n", "pb.to_trino(final_view, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "markdown", "id": "ecb6c572-d72b-4f31-8027-3efa9d29c997", "metadata": {}, "source": ["# utilization"]}, {"cell_type": "markdown", "id": "9f4d3f35-2c89-4716-8154-ebec757028bd", "metadata": {}, "source": ["# capacity"]}, {"cell_type": "code", "execution_count": null, "id": "3338f0cc-2826-4172-83e0-1a29139c0b27", "metadata": {}, "outputs": [], "source": ["def capacity():\n", "    capacity = f\"\"\"\n", "    \n", "    with\n", "    raw_data as\n", "        (select * from supply_etls.inbound_and_outbound_capacity\n", "            where\n", "                insert_ds_ist >= (current_date - interval '120' day)\n", "        ),\n", "\n", "    max_updated_at as\n", "        (select\n", "            max(updated_at_ist) as updated_at_ist,\n", "            insert_ds_ist\n", "\n", "                from raw_data\n", "\n", "                    group by 2\n", "        ),\n", "\n", "    final_view as\n", "        (select\n", "            -- REGEXP_REPLACE(DATE_FORMAT(rd.insert_ds_ist, '%%M'), ' ', '') as month,\n", "            rd.insert_ds_ist as date_,\n", "            rd.outlet_id,\n", "            rd.assortment_type,\n", "            rd.type_,\n", "            rd.daily_capacity\n", "\n", "                from raw_data rd\n", "\n", "                    join\n", "                        max_updated_at mua on mua.updated_at_ist = rd.updated_at_ist\n", "                        and\n", "                            mua.insert_ds_ist = rd.insert_ds_ist\n", "        )\n", "\n", "            select * from final_view\n", "                where\n", "                    REGEXP_REPLACE(to_char(date_, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '70' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "    \n", "    \"\"\"\n", "    return read_sql_query(capacity, trino)\n", "\n", "\n", "capacity = capacity()\n", "\n", "print(capacity.shape)\n", "capacity.head(2)"]}, {"cell_type": "markdown", "id": "a8f5b74f-af06-41a5-ba2a-bac55a358955", "metadata": {}, "source": ["# inward quantity"]}, {"cell_type": "code", "execution_count": null, "id": "0cf27a86-7542-4cb3-86ce-f07ce733c83c", "metadata": {}, "outputs": [], "source": ["def inward_quantity():\n", "    inward_quantity = \"\"\"\n", "    \n", "    with\n", "    raw_data as\n", "        (select *,\n", "            lead(midnight_quantity) over(partition by outlet_id, business_type, handling_type, type_, assortment_type, trigger_status order by insert_ds_ist asc) as next_midnight_quantity,\n", "            lead(midnight_from_be_quantity) over(partition by outlet_id, business_type, handling_type, type_, assortment_type, trigger_status order by insert_ds_ist asc) as next_midnight_from_be_quantity,\n", "            lead(courier_midnight_quantity) over(partition by outlet_id, business_type, handling_type, type_, assortment_type, trigger_status order by insert_ds_ist asc) as next_courier_midnight_quantity\n", "            from supply_etls.inbound_and_outbound_quantity\n", "            where\n", "                insert_ds_ist >= (current_date - interval '120' day)\n", "                and\n", "                    taggings = 'be'\n", "        ),\n", "\n", "    inward as\n", "        (select\n", "            insert_ds_ist as date_,\n", "            outlet_id,\n", "            assortment_type,\n", "            type_,\n", "            \n", "            case\n", "                when type_ = 'po_inbound' then coalesce(quantity, 0) - coalesce(midnight_quantity, 0) + coalesce(next_midnight_quantity, 0)\n", "                else 0 end as po_inward,\n", "\n", "            case\n", "                when type_ = 'sto_inbound' then coalesce(quantity, 0) - coalesce(midnight_quantity, 0) + coalesce(next_midnight_quantity, 0)\n", "                else 0 end as sto_inward,\n", "            \n", "            case\n", "                when type_ = 'sto_inbound' then coalesce(from_be_quantity, 0) - coalesce(midnight_from_be_quantity, 0) + coalesce(next_midnight_from_be_quantity, 0)\n", "                else 0 end as from_be_sto_inward,\n", "            \n", "            case\n", "                when type_ = 'po_inbound' then coalesce(courier_quantity, 0) - coalesce(courier_midnight_quantity, 0) + coalesce(next_courier_midnight_quantity, 0)\n", "                else 0 end as courier_po_inward\n", "\n", "                from raw_data\n", "\n", "                    where\n", "                        business_type = 'grn'\n", "                        and\n", "                            handling_type = 'Non Packaging Material'\n", "        )\n", "\n", "            select * from inward\n", "                where\n", "                    REGEXP_REPLACE(to_char(date_, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '70' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "    \n", "    \"\"\"\n", "    return read_sql_query(inward_quantity, trino)\n", "\n", "\n", "inward_quantity = inward_quantity()\n", "\n", "print(inward_quantity.shape)\n", "inward_quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3072a3dd-9376-4b1e-8949-3438f0e2b03c", "metadata": {}, "outputs": [], "source": ["inward_capacity = capacity.copy()\n", "inward_capacity = inward_capacity[inward_capacity[\"type_\"] == \"po_inbound\"]\n", "inward_capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "37191d4e-d587-46bd-b852-98b16e56332f", "metadata": {}, "outputs": [], "source": ["inward_capacity[\"date_\"] = pd.to_datetime(inward_capacity[\"date_\"])\n", "inward_quantity[\"date_\"] = pd.to_datetime(inward_quantity[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "dcb2d58b-e1c3-45e0-9298-7c5787ba7dc7", "metadata": {}, "outputs": [], "source": ["final_inward = pd.merge(\n", "    inward_capacity,\n", "    inward_quantity,\n", "    on=[\"date_\", \"outlet_id\", \"assortment_type\", \"type_\"],\n", "    how=\"outer\",\n", ")\n", "\n", "# outlet information\n", "final_inward = pd.merge(final_inward, outlet_details, on=[\"outlet_id\"], how=\"left\")\n", "\n", "final_inward[\n", "    [\"daily_capacity\", \"po_inward\", \"sto_inward\", \"from_be_sto_inward\", \"courier_po_inward\"]\n", "] = (\n", "    final_inward[\n", "        [\"daily_capacity\", \"po_inward\", \"sto_inward\", \"from_be_sto_inward\", \"courier_po_inward\"]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "final_inward[\"month_\"] = final_inward[\"date_\"].apply(lambda x: x.strftime(\"%B\"))\n", "\n", "final_inward = final_inward[\n", "    [\n", "        \"month_\",\n", "        \"date_\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"store_type\",\n", "        \"assortment_type\",\n", "        \"type_\",\n", "        \"daily_capacity\",\n", "        \"po_inward\",\n", "        \"sto_inward\",\n", "        \"from_be_sto_inward\",\n", "        \"courier_po_inward\",\n", "    ]\n", "]\n", "\n", "final_inward[\"date_\"] = final_inward[\"date_\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "\n", "final_inward.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3b665fa1-fd91-411f-b494-c84d49381243", "metadata": {}, "outputs": [], "source": ["sheets(\n", "    final_inward,\n", "    \"write\",\n", "    \"119qJiIDwY7aaB2_amXvZbSMkRK8Y0OOZjRCyzCFba44\",\n", "    \"inward_quantity_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "371780a3-33bb-4528-8257-e81552e5a8e1", "metadata": {}, "source": ["# outward quantity"]}, {"cell_type": "code", "execution_count": null, "id": "70da9576-8077-4563-9d02-350dab9bab2c", "metadata": {}, "outputs": [], "source": ["def outward_quantity():\n", "    outward_quantity = \"\"\"\n", "    \n", "    with\n", "    raw_data as\n", "        (select * from supply_etls.inbound_and_outbound_quantity\n", "            where\n", "                insert_ds_ist >= (current_date - interval '120' day)\n", "                and\n", "                    taggings = 'be'\n", "        ),\n", "\n", "    outward as\n", "        (select\n", "            insert_ds_ist as date_,\n", "            outlet_id,\n", "            assortment_type,\n", "            type_,\n", "            trigger_status,\n", "\n", "            sum(case\n", "                when business_type in ('create') then quantity\n", "                else 0\n", "            end) as create_quantity,\n", "\n", "            sum(case\n", "                when business_type in ('grn','billed') then quantity\n", "                else 0\n", "            end) as billed_quantity\n", "\n", "                from raw_data\n", "\n", "                    where\n", "                        handling_type = 'Non Packaging Material'\n", "                        and\n", "                            type_ = 'ds_transfer'\n", "\n", "                        group by 1,2,3,4,5\n", "        )\n", "\n", "            select * from outward\n", "                where\n", "                    REGEXP_REPLACE(to_char(date_, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '70' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "    \n", "    \"\"\"\n", "    return read_sql_query(outward_quantity, trino)\n", "\n", "\n", "outward_quantity = outward_quantity()\n", "\n", "print(outward_quantity.shape)\n", "outward_quantity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "e2639dfe-7287-490a-b9d5-da424fd4327e", "metadata": {}, "outputs": [], "source": ["outward_capacity = capacity.copy()\n", "outward_capacity = outward_capacity[outward_capacity[\"type_\"] == \"ds_transfer\"]\n", "outward_capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "6585cdd7-ce08-4e7e-a1a5-568708185b57", "metadata": {}, "outputs": [], "source": ["outward_capacity[\"date_\"] = pd.to_datetime(outward_capacity[\"date_\"])\n", "outward_quantity[\"date_\"] = pd.to_datetime(outward_quantity[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "c5d0fb4c-f700-4ba3-9baa-8fdf8fab0a1b", "metadata": {}, "outputs": [], "source": ["final_outward = pd.merge(\n", "    outward_capacity,\n", "    outward_quantity,\n", "    on=[\"date_\", \"outlet_id\", \"assortment_type\", \"type_\"],\n", "    how=\"outer\",\n", ")\n", "\n", "# outlet information\n", "final_outward = pd.merge(final_outward, outlet_details, on=[\"outlet_id\"], how=\"inner\")\n", "\n", "final_outward[[\"daily_capacity\", \"create_quantity\", \"billed_quantity\"]] = (\n", "    final_outward[[\"daily_capacity\", \"create_quantity\", \"billed_quantity\"]].fillna(0).astype(int)\n", ")\n", "\n", "final_outward[\"month_\"] = final_outward[\"date_\"].apply(lambda x: x.strftime(\"%B\"))\n", "\n", "final_outward = final_outward[\n", "    [\n", "        \"month_\",\n", "        \"date_\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"store_type\",\n", "        \"assortment_type\",\n", "        \"type_\",\n", "        \"trigger_status\",\n", "        \"daily_capacity\",\n", "        \"create_quantity\",\n", "        \"billed_quantity\",\n", "    ]\n", "]\n", "\n", "final_outward[\"date_\"] = final_outward[\"date_\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "\n", "final_outward[\"trigger_status\"] = final_outward[\"trigger_status\"].fillna(\"auto\")\n", "\n", "final_outward.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3e1d9b3a-c187-43eb-af8c-f972c527d3d6", "metadata": {}, "outputs": [], "source": ["sheets(\n", "    final_outward,\n", "    \"write\",\n", "    \"119qJiIDwY7aaB2_amXvZbSMkRK8Y0OOZjRCyzCFba44\",\n", "    \"outward_quantity_raw\",\n", ")"]}, {"cell_type": "markdown", "id": "c702351b-ad1f-4fef-858c-55ef9cabcc36", "metadata": {}, "source": ["# sku capacity"]}, {"cell_type": "code", "execution_count": null, "id": "b50051c8-2888-4ca6-a811-e3742bd802a2", "metadata": {}, "outputs": [], "source": ["skus_capacity = capacity.copy()\n", "skus_capacity = skus_capacity[skus_capacity[\"type_\"].isin([\"po_inbound_skus\", \"sto_inbound_skus\"])]\n", "skus_capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "eb1c6130-6de9-4ed7-83a8-022cd4db6380", "metadata": {"tags": []}, "outputs": [], "source": ["def skus_count():\n", "    skus_count = \"\"\"\n", "    \n", "    with\n", "    raw_data as\n", "        (select *,\n", "            lead(midnight_skus_count) over(partition by outlet_id, business_type, handling_type, type_, assortment_type, trigger_status order by insert_ds_ist asc) as next_midnight_skus_count,\n", "            lead(courier_midnight_skus_count) over(partition by outlet_id, business_type, handling_type, type_, assortment_type, trigger_status order by insert_ds_ist asc) as next_courier_midnight_skus_count\n", "        from supply_etls.inbound_and_outbound_quantity\n", "            where\n", "                insert_ds_ist >= (current_date - interval '120' day)\n", "                and\n", "                    taggings = 'be'\n", "        ),\n", "\n", "    skus_count as\n", "        (select\n", "            insert_ds_ist as date_,\n", "            outlet_id,\n", "            assortment_type,\n", "            \n", "            case\n", "                when type_ = 'po_inbound' then 'po_inbound_skus'\n", "                when type_ = 'sto_inbound' then 'sto_inbound_skus'\n", "                else null\n", "            end as type_,\n", "            \n", "            case\n", "                when type_ = 'po_inbound' then coalesce(skus_count, 0) - coalesce(midnight_skus_count, 0) + coalesce(next_midnight_skus_count, 0)\n", "                when type_ = 'sto_inbound' then coalesce(skus_count, 0) - coalesce(midnight_skus_count, 0) + coalesce(next_midnight_skus_count, 0)\n", "                else 0\n", "            end as skus_count,\n", "            \n", "            case\n", "                when type_ = 'po_inbound' then coalesce(courier_skus_count, 0) - coalesce(courier_midnight_skus_count, 0) + coalesce(next_courier_midnight_skus_count, 0)\n", "                else 0\n", "            end as courier_skus_count\n", "\n", "                from raw_data\n", "\n", "                    where\n", "                        business_type = 'grn'\n", "                        and\n", "                            handling_type = 'Non Packaging Material'\n", "        )\n", "\n", "            select * from skus_count\n", "                where\n", "                    REGEXP_REPLACE(to_char(date_, 'yy-mm'), ' ','') between REGEXP_REPLACE(to_char((current_date - interval '70' day), 'yy-mm'), ' ','') and REGEXP_REPLACE(to_char(current_date, 'yy-mm'), ' ','')\n", "                    and\n", "                        type_ is not null\n", "                \n", "    \n", "    \"\"\"\n", "    return read_sql_query(skus_count, trino)\n", "\n", "\n", "skus_count = skus_count()\n", "\n", "print(skus_count.shape)\n", "skus_count.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3711be9c-cf2a-4ea6-b7fa-8bb1a028bd8c", "metadata": {}, "outputs": [], "source": ["skus_capacity[\"date_\"] = pd.to_datetime(skus_capacity[\"date_\"])\n", "skus_count[\"date_\"] = pd.to_datetime(skus_count[\"date_\"])"]}, {"cell_type": "code", "execution_count": null, "id": "d7bdb6f1-e893-4a49-a14d-07bbdb25ff82", "metadata": {}, "outputs": [], "source": ["final_skus = pd.merge(\n", "    skus_capacity,\n", "    skus_count,\n", "    on=[\"date_\", \"outlet_id\", \"assortment_type\", \"type_\"],\n", "    how=\"outer\",\n", ")\n", "\n", "# outlet information\n", "final_skus = pd.merge(final_skus, outlet_details, on=[\"outlet_id\"], how=\"inner\")\n", "\n", "final_skus[[\"daily_capacity\", \"skus_count\", \"courier_skus_count\"]] = (\n", "    final_skus[[\"daily_capacity\", \"skus_count\", \"courier_skus_count\"]].fillna(0).astype(int)\n", ")\n", "\n", "final_skus[\"month_\"] = final_skus[\"date_\"].apply(lambda x: x.strftime(\"%B\"))\n", "\n", "final_skus = final_skus[\n", "    [\n", "        \"month_\",\n", "        \"date_\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"store_type\",\n", "        \"assortment_type\",\n", "        \"type_\",\n", "        \"daily_capacity\",\n", "        \"skus_count\",\n", "        \"courier_skus_count\",\n", "    ]\n", "]\n", "\n", "final_skus[\"date_\"] = final_skus[\"date_\"].apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "\n", "final_skus.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "3b2f10cc-7cfe-421f-9323-8dd6df77a22d", "metadata": {}, "outputs": [], "source": ["sheets(\n", "    final_skus,\n", "    \"write\",\n", "    \"119qJiIDwY7aaB2_amXvZbSMkRK8Y0OOZjRCyzCFba44\",\n", "    \"inward_skus_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "17d8062f-7e2f-4c05-b8b8-c817b3ebc6f2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}