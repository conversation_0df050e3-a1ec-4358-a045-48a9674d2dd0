{"cells": [{"cell_type": "code", "execution_count": null, "id": "f901b6e3-07c3-4647-ab8a-f551694e5053", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "ebadf26f-85cf-4111-a819-1e72b1073e71", "metadata": {}, "outputs": [], "source": ["suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(\"DataFrame size: \", size)\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "60b4138f-46e6-4f61-bccd-d07ad1875223", "metadata": {}, "outputs": [], "source": ["updated_at_ist = pd.to_datetime(datetime.today() + timedelta(hours=5.5))\n", "updated_at_ist"]}, {"cell_type": "markdown", "id": "323250a6-2f3d-488c-9461-3cc0780012ff", "metadata": {}, "source": ["# active ds list"]}, {"cell_type": "code", "execution_count": null, "id": "a6efc544-e058-48b0-9a30-5391059e371c", "metadata": {}, "outputs": [], "source": ["def outlet_details():\n", "    outlet_details = \"\"\"\n", "    \n", "    select\n", "        hot_outlet_id as outlet_id,\n", "        facility_id,\n", "        hot_outlet_name as outlet_name\n", "        \n", "            from supply_etls.outlet_details\n", "                \n", "                group by 1,2,3\n", "    \n", "    \"\"\"\n", "    return read_sql_query(outlet_details, trino)\n", "\n", "\n", "outlet_details = outlet_details()\n", "\n", "print(outlet_details.shape)\n", "outlet_details.head(2)"]}, {"cell_type": "markdown", "id": "4d6f8c9d-16b4-4cce-8a13-f07b7e138d29", "metadata": {}, "source": ["# capacity input details"]}, {"cell_type": "code", "execution_count": null, "id": "b2125714-ced2-40be-ae08-3ab25045ac98", "metadata": {}, "outputs": [], "source": ["capacity = pb.from_sheets(\n", "    \"119qJiIDwY7aaB2_amXvZbSMkRK8Y0OOZjRCyzCFba44\",\n", "    \"capacity_planning\",\n", "    clear_cache=True,\n", ")\n", "\n", "capacity.to_csv(\"capacity.csv\", index=False)\n", "\n", "capacity = pd.read_csv(\"capacity.csv\", skiprows=1)\n", "\n", "capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "13e33a84-d4e4-4429-a9a3-20aa2a019f29", "metadata": {}, "outputs": [], "source": ["capacity = capacity.melt(\n", "    id_vars=[\n", "        \"outlet_id\",\n", "        \"assortment_type\",\n", "        \"type\",\n", "        \"total_capacity\",\n", "    ],\n", "    var_name=\"insert_ds_ist\",\n", "    value_name=\"daily_capacity\",\n", ")\n", "\n", "capacity[\"insert_ds_ist\"] = pd.to_datetime(capacity[\"insert_ds_ist\"])\n", "\n", "capacity.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "0fde4431-ac3b-48ac-a524-b07a854efe65", "metadata": {}, "outputs": [], "source": ["adding_new_capacity = capacity.copy()\n", "\n", "adding_new_capacity = adding_new_capacity[\n", "    adding_new_capacity[\"assortment_type\"].isin([\"perishable\", \"fnv\", \"milk\"])\n", "]\n", "\n", "adding_new_capacity = adding_new_capacity.replace([\"po_inbound\"], [\"ds_transfer\"])\n", "\n", "capacity = capacity[\n", "    capacity[\"assortment_type\"].isin(\n", "        [\"packaged goods\", \"frozen\", \"perishable\", \"fnv\", \"milk\"]\n", "    )\n", "]\n", "\n", "final_base = capacity.append(adding_new_capacity)\n", "\n", "final_base.columns = [\n", "    \"outlet_id\",\n", "    \"assortment_type\",\n", "    \"type_\",\n", "    \"monthly_capacity\",\n", "    \"insert_ds_ist\",\n", "    \"daily_capacity\",\n", "]\n", "\n", "# adding facility id\n", "final_base = pd.merge(\n", "    final_base,\n", "    outlet_details[[\"outlet_id\", \"facility_id\"]],\n", "    on=[\"outlet_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "print(final_base.shape)\n", "final_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "c386e985-7416-4b83-ab67-c3f5af6f2674", "metadata": {}, "outputs": [], "source": ["final_base[[\"outlet_id\", \"monthly_capacity\", \"daily_capacity\"]] = (\n", "    final_base[[\"outlet_id\", \"monthly_capacity\", \"daily_capacity\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "final_base[\"updated_at_ist\"] = updated_at_ist\n", "\n", "final_base = final_base[\n", "    [\n", "        \"updated_at_ist\",\n", "        \"insert_ds_ist\",\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"assortment_type\",\n", "        \"type_\",\n", "        \"monthly_capacity\",\n", "        \"daily_capacity\",\n", "    ]\n", "]\n", "\n", "final_base = final_base.replace([\"skus_inbound_po\"], [\"po_inbound_skus\"])\n", "final_base = final_base.replace([\"skus_inbound_sto\"], [\"sto_inbound_skus\"])\n", "\n", "print(final_base.shape)\n", "final_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "69339cbf-43ba-49c5-a9f4-3efe455e5993", "metadata": {}, "outputs": [], "source": ["final_base.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "54d9b08e-bdf7-4876-be8d-fb1a1020987e", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"table updated timestamp\",\n", "    },\n", "    {\n", "        \"name\": \"insert_ds_ist\",\n", "        \"type\": \"date\",\n", "        \"description\": \"capacity consumption date/partition key\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"outlet facility id\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"items relate to which category\",\n", "    },\n", "    {\"name\": \"type_\", \"type\": \"varchar\", \"description\": \"inbound/outbound description\"},\n", "    {\n", "        \"name\": \"monthly_capacity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total month capacity\",\n", "    },\n", "    {\"name\": \"daily_capacity\", \"type\": \"integer\", \"description\": \"day wise capacity\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b46bc6c6-dc99-4e10-a426-6296d4285866", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inbound_and_outbound_capacity\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"outlet_id\", \"facility_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"inward and outward capacity details\",\n", "}\n", "\n", "pb.to_trino(final_base, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "markdown", "id": "0d489078-bc13-41ee-aea5-e343543b7df0", "metadata": {}, "source": ["# redshift table update"]}, {"cell_type": "code", "execution_count": null, "id": "f3d6858f-08c3-4aae-a05e-824e36fbd74d", "metadata": {}, "outputs": [], "source": ["final_base = final_base.replace([\"po_inbound_skus\"], [\"skus_inbound_po\"])\n", "final_base = final_base.replace([\"sto_inbound_skus\"], [\"skus_inbound_sto\"])\n", "\n", "# adding outlet name\n", "final_base = pd.merge(\n", "    final_base, outlet_details, on=[\"outlet_id\", \"facility_id\"], how=\"inner\"\n", ")\n", "\n", "final_base.columns = [\n", "    \"updated_at\",\n", "    \"date\",\n", "    \"outlet_id\",\n", "    \"facility_id\",\n", "    \"assortment_type\",\n", "    \"type\",\n", "    \"total_capacity\",\n", "    \"capacity\",\n", "    \"outlet_name\",\n", "]\n", "\n", "final_base = final_base[\n", "    [\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"outlet_name\",\n", "        \"assortment_type\",\n", "        \"type\",\n", "        \"total_capacity\",\n", "        \"date\",\n", "        \"capacity\",\n", "        \"updated_at\",\n", "    ]\n", "]\n", "\n", "print(final_base.shape)\n", "final_base.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "05a14422-c0de-4197-8ef6-17f8b088f120", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"stores facility id\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"stores outlet name\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"items relate to which category\",\n", "    },\n", "    {\"name\": \"type\", \"type\": \"varchar\", \"description\": \"inbound/outbound description\"},\n", "    {\n", "        \"name\": \"total_capacity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"total month capacity\",\n", "    },\n", "    {\"name\": \"date\", \"type\": \"datetime\", \"description\": \"item relate to\"},\n", "    {\"name\": \"capacity\", \"type\": \"integer\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"updated_at\", \"type\": \"datetime\", \"description\": \"table updated at\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "213a1221-5a63-4c2b-afc0-625fd40f712b", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"inbound_and_outbound_capacity\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"type\", \"date\"],\n", "    \"sortkey\": [\"date\", \"facility_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"inbound and outbound capacity\",\n", "}\n", "\n", "pb.to_redshift(final_base, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}