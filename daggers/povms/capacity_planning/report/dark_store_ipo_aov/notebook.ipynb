{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date, datetime, timedelta\n", "import numpy as np\n", "import requests\n", "import json\n", "import uuid\n", "import calendar\n", "import os\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "import math\n", "import logging\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "import os\n", "import time\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.DEBUG)\n", "\n", "\n", "# Connection\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "\n", "# Dates\n", "today = date.today() - <PERSON><PERSON><PERSON>(days=0)\n", "difference = 0\n", "today_trick = today - <PERSON><PERSON><PERSON>(days=difference)\n", "current = today  # - <PERSON><PERSON><PERSON>(days=2)\n", "current_half = current - <PERSON><PERSON><PERSON>(days=16)\n", "print(\n", "    \"dates- today:\",\n", "    today,\n", "    \" | today_trick:\",\n", "    today_trick,\n", "    \" | current:\",\n", "    current,\n", "    \" | current_half:\",\n", "    current_half,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = date.today() - <PERSON><PERSON><PERSON>(days=0)\n", "current = date.today() - <PERSON><PERSON><PERSON>(days=30)\n", "current"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo_input_query = f\"\"\"SELECT date(convert_timezone('IST',scheduled_at))  as dates,\n", "po.Facility_id,\n", "       po.facility AS Facility, \n", "       CASE WHEN PP.outlet_type = 1 THEN 'FnV' ELSE 'Groceries' END AS outlet_type,\n", "        sum(i.quantity) AS Item_quantity,\n", "         count(i.item_id) AS SKU_Count,\n", "         count(DISTINCT o.order_id) AS Sub_orders,\n", "         round(sum(i.quantity)*1.0/count(DISTINCT o.order_id)*1.0,2) AS items_per_order,\n", "         round(count(i.item_id)*1.0/count(DISTINCT o.order_id)*1.0,2) AS SKU_per_order -- , count(map.order_id) as orders_with_picklist, count(pick_list_id) as pick_list_count\n", "FROM lake_ims.ims_order_details o\n", "INNER JOIN lake_ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN lake_retail.console_outlet r ON o.outlet = r.id\n", "LEFT JOIN\n", "  (SELECT id AS Outlet_id,\n", "          facility,\n", "          facility_id,\n", "          rnk\n", "   FROM\n", "     (SELECT m.*,\n", "             o.facility,\n", "             row_number() over(PARTITION BY updated_at,id\n", "                               ORDER BY updated_at DESC) AS rnk\n", "      FROM lake_retail.console_outlet m\n", "      LEFT JOIN\n", "        (SELECT DISTINCT facility,\n", "                         facility_id\n", "         FROM consumer.city_facility_split) o ON o.facility_id= m.facility_id\n", "      WHERE active =1\n", "      ORDER BY updated_at)\n", "   WHERE rnk =1\n", "     AND facility IS NOT NULL) po ON po.Outlet_id= r.id\n", "     \n", "    LEFT JOIN (select distinct item_id, outlet_type from lake_rpc.product_product where outlet_type != 0) PP ON i.item_id = PP.item_id\n", "WHERE date(convert_timezone('IST',scheduled_at)) BETWEEN \n", "dateadd(month, -3,date(current_date)) and date(current_date)\n", "--date('2021-03-01') and date('2021-03-31')\n", "  AND status_id NOT IN (3,\n", "                        4,\n", "                        5)\n", "  AND po.Facility_id IN (135,\n", "                         197,\n", "                         209,\n", "                         238,\n", "                         219,\n", "                         223,\n", "                         227,\n", "                         237,\n", "                         247,\n", "                         246,\n", "                         285,\n", "                         271,\n", "                         201,\n", "                         161,\n", "                         228,\n", "                         319,\n", "                         370,1317,\n", "                         375,377,388,376,387,391,397,392)\n", "GROUP BY 1,\n", "         2,3,4\n", "ORDER BY 1\"\"\"\n", "\n", "ipo_input = pd.read_sql_query(\n", "    sql=ipo_input_query, con=CON_REDSHIFT\n", ")  # ,params={'start_date':current}) #:today})\n", "ipo_input.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_ipo_input_query = f\"\"\"SELECT date(convert_timezone('IST',scheduled_at))  as dates,\n", "po.Facility_id,\n", "       po.facility AS Facility, \n", "       --CASE WHEN PP.outlet_type = 1 THEN 'FnV' ELSE 'Groceries' END AS outlet_type,\n", "        sum(i.quantity) AS Item_quantity,\n", "         count(i.item_id) AS SKU_Count,\n", "         count(DISTINCT o.order_id) AS Sub_orders,\n", "         round(sum(i.quantity)*1.0/count(DISTINCT o.order_id)*1.0,2) AS items_per_order,\n", "         round(count(i.item_id)*1.0/count(DISTINCT o.order_id)*1.0,2) AS SKU_per_order -- , count(map.order_id) as orders_with_picklist, count(pick_list_id) as pick_list_count\n", "FROM lake_ims.ims_order_details o\n", "INNER JOIN lake_ims.ims_order_items i ON o.id = i.order_details_id\n", "INNER JOIN lake_retail.console_outlet r ON o.outlet = r.id\n", "LEFT JOIN\n", "  (SELECT id AS Outlet_id,\n", "          facility,\n", "          facility_id,\n", "          rnk\n", "   FROM\n", "     (SELECT m.*,\n", "             o.facility,\n", "             row_number() over(PARTITION BY updated_at,id\n", "                               ORDER BY updated_at DESC) AS rnk\n", "      FROM lake_retail.console_outlet m\n", "      LEFT JOIN\n", "        (SELECT DISTINCT facility,\n", "                         facility_id\n", "         FROM consumer.city_facility_split) o ON o.facility_id= m.facility_id\n", "      WHERE active =1\n", "      ORDER BY updated_at)\n", "   WHERE rnk =1\n", "     AND facility IS NOT NULL) po ON po.Outlet_id= r.id\n", "     \n", "    LEFT JOIN (select distinct item_id, outlet_type from lake_rpc.product_product where outlet_type != 0) PP ON i.item_id = PP.item_id\n", "WHERE date(convert_timezone('IST',scheduled_at)) BETWEEN \n", "dateadd(month, -3,date(current_date)) and date(current_date)\n", "--date('2021-03-01') and date('2021-03-31')\n", "  AND status_id NOT IN (3,\n", "                        4,\n", "                        5)\n", "  AND po.Facility_id IN (135,\n", "                         197,\n", "                         209,\n", "                         238,\n", "                         219,\n", "                         223,\n", "                         227,\n", "                         237,\n", "                         247,\n", "                         246,\n", "                         285,\n", "                         271,\n", "                         201,\n", "                         161,\n", "                         228,\n", "                         319,\n", "                         370,1317,\n", "                         375,377,388,376,387,391,397,392)\n", "GROUP BY 1,\n", "         2,3\n", "ORDER BY 1\"\"\"\n", "\n", "overall_ipo_input = pd.read_sql_query(\n", "    sql=overall_ipo_input_query, con=CON_REDSHIFT\n", ")  # ,params={'start_date':current}) #:today})\n", "overall_ipo_input.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print((ipo_input.dates.drop_duplicates().max()), (ipo_input.dates.drop_duplicates().min()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["carts_query = f\"\"\" with \n", "\n", "it_pd as\n", "(\n", "select distinct item_id, product_id,multiplier, updated_on, max(updated_on) over (partition by product_id) as last_updated \n", "from it_pd_log\n", "),\n", "\n", "mapping as (select distinct item_id, product_id,coalesce(multiplier,1) as multiplier from it_pd\n", "    where last_updated = updated_on ),\n", "\n", "tbl_treatment as\n", "    (select a.*,date(a.install_ts + interval '5.5 Hours') as checkout_date,om.city_name,b.product_id,c.item_id,om.external_id as virtual_merchant_id\n", "    from lake_oms_bifrost.oms_order a\n", "    left join lake_oms_bifrost.view_oms_merchant om on a.merchant_id = om.id\n", "    left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and date(install_ts + interval '5.5 Hours') \n", "    between date(dateadd(day,1,last_day(dateadd(month, -2, LAST_DAY(current_date))))) and last_day(current_date)\n", "    -- (SELECT date(dateadd(month , -1,DATEADD(day , 1,LAST_DAY(current_date))))) +0 and last_day(current_date)\n", "    ) b on a.id = b.order_id\n", "    left join mapping c on b.product_id = c.product_id\n", "    left join  lake_rpc.item_details  d on c.item_id = d.item_id\n", "    where (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "        and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "        and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)),\n", "\n", "merchant_and_store_join as (\n", "    select a.*,b.real_merchant_id,c.outlet_id,d.warehouse_id as hot_outlet_id,e.facility_id,f.name as facility_name from tbl_treatment a\n", "    left join (select * from lake_cms.gr_virtual_to_real_merchant_mapping where priority_order = 1 AND enabled_flag = true) b\n", "    on a.virtual_merchant_id = b.virtual_merchant_id\n", "    left join (select * from  lake_retail.console_outlet_cms_store  where active = true) c on b.real_merchant_id = c.cms_store\n", "    left join  lake_retail.warehouse_outlet_mapping  d on c.outlet_id = d.cloud_store_id\n", "    left join  lake_retail.console_outlet  e on d.warehouse_id = e.id\n", "    left join  lake_crates.facility  f on e.facility_id = f.id),\n", "   \n", " Carts as (select checkout_date,virtual_merchant_id, count(distinct id) as orders from merchant_and_store_join\n", " where date(checkout_date) \n", " between dateadd(month,-1 ,date(current_date))\n", "and date(current_date)\n", "group by 1,2),\n", "   \n", "   \n", "   f_data as (\n", "select * from ( select *, row_number() over (partition by merchant_name,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts)\n", "where rnk =1 and date(f_date) BETWEEN  \n", "(SELECT date(dateadd(month , -2,DATEADD(day , 1,LAST_DAY(current_date))))) +0 \n", "and dateadd(month, 2, last_day(current_date))),\n", "\n", "actual_data as( SELECT date(checkout_date) as consumption_date,virtual_merchant_id as merchant_id,orders AS total_carts FROM carts),\n", " \n", " fin_data as (select date(f_date) as Date_ ,f.merchant_id,facility_id,facility,forecast_carts,aa.total_carts\n", "    from f_data f\n", "    left join actual_data aa on aa.merchant_id=f.merchant_id and f.f_date=aa.consumption_date),\n", "    \n", "    zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "    \n", "final_view as\n", "(\n", "select \n", "date(Date_) as date,\n", "b.zone,\n", "a.facility_id,facility,\n", "sum(forecast_carts) as forecast_qty,\n", "sum(total_carts) as actual_qty\n", " from fin_data a\n", " left join zone_tbl_treatment b on a.facility_id = b.facility_id\n", " group by 1,2,3,4\n", " )\n", " \n", "\n", "SELECT facility_id, facility, dateadd(month,-1 ,date(current_date)) as start,date(current_date) as end, SUM(actual_qty) as actual_carts\n", "FROM final_view\n", "where date between dateadd(month,-1 ,date(current_date))\n", "and date(current_date)\n", "and facility_id in (135,\n", "                         197,\n", "                         209,\n", "                         238,\n", "                         219,\n", "                         223,\n", "                         227,\n", "                         237,\n", "                         247,\n", "                         246,\n", "                         285,\n", "                         271,\n", "                         201,\n", "                         161,\n", "                         228,\n", "                         319,\n", "                         370,1317,\n", "                         375,377,388,376,387,391,397,392)\n", "group by 1,2\n", "\n", "\"\"\"\n", "\n", "\n", "carts = pd.read_sql_query(sql=carts_query, con=CON_REDSHIFT)\n", "carts.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_query = f\"\"\"with\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", " it_pd as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "multiplier,\n", "updated_on,\n", "max(updated_on) over (partition by product_id) as last_updated\n", "from \n", "it_pd_log\n", "),\n", "\n", "mapping as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "coalesce(multiplier,1) as multiplier\n", "from it_pd\n", "where last_updated = updated_on\n", "),\n", "\n", "categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "P.UNIT AS Unit,\n", "C2.NAME AS L2,\n", "(case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "C.NAME AS L0,\n", "P.BRAND AS brand,\n", "P.MANUFACTURER AS manf,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "), \n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l0,\n", "cat.l1,\n", "cat.l2,\n", "cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l0) as l0,\n", "max(l1) as l1,\n", "max(l2) as l2,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", "),\n", "\n", "\n", "tbl_treatment as\n", "(\n", "select\n", "a.*,\n", "date(a.install_ts + interval '5.5 Hours') as checkout_date,\n", "om.city_name,\n", "b.product_id,\n", "c.item_id,\n", "d.is_pl,\n", "b.quantity*multiplier as quantity,\n", "multiplier,\n", "b.selling_price,\n", "om.external_id as virtual_merchant_id,\n", "d.variant_mrp,\n", "sum(d.variant_mrp) over (partition by a.id, b.product_id) as total_mrp\n", "from lake_oms_bifrost.oms_order a\n", "left join lake_oms_bifrost.oms_merchant om on a.merchant_id = om.id\n", "left join (select * from lake_oms_bifrost.oms_order_item where freebie_id is null and date(install_ts + interval '5.5 Hours') between current_date - 220 AND current_date) b \n", "on a.id = b.order_id\n", "left join mapping c on b.product_id = c.product_id\n", "left join (select * from item_level_info where row_rank = 1) d on c.item_id = d.item_id\n", "left join ptype_tbl e on c.item_id = e.item_id\n", "left  join (select * from all_margins lq where lq_margin >= 0.01 )lq on lq.pid = b.product_id and lq.city = om.city_name and \n", "(a.install_ts + interval '5.5 Hours') >= lq.start_date and date(a.install_ts + interval '5.5 Hours') <= lq.end_date\n", "where\n", "date(a.install_ts + interval '5.5 Hours') between dateadd(month,-1 ,date(current_date))\n", "and date(current_date)\n", "and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "AND om.city_name not ilike '%%b2b%%'\n", "--and current_status = 'CANCELLED'\n", "and lq.pid is null\n", "and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)\n", "-- l0 not in ('Fruits & Vegetables', 'Vegetables & Fruits')\n", "),\n", "\n", "merchant_and_store_join as\n", "(\n", "select \n", "a.*,\n", "selling_price*variant_mrp*1.000/(total_mrp*multiplier) as item_selling_price,\n", "bb.facility_id\n", "from tbl_treatment a\n", "left join ( select *, row_number() over (partition by merchant_name,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) bb on a.virtual_merchant_id = bb.merchant_id and date(a.checkout_date) = bb.f_date\n", "where rnk =1\n", "),\n", "\n", "final_sales as\n", "(\n", "select \n", "a.facility_id,\n", "a.item_id,\n", "sum(a.quantity) as qty,\n", "sum(a.quantity*item_selling_price) as gmv\n", "from merchant_and_store_join a\n", "--where a.checkout_date date(dateadd(day,1,last_day(dateadd(month, -2, LAST_DAY(current_date)))))\n", "--and date(dateadd(day,1,last_day(dateadd(month, -2, LAST_DAY(current_date))))) + 30\n", "group by 1,2\n", "),\n", "\n", "price_history as (\n", "select\n", "date(gp.install_ts+interval '5.5 hours') as datee,\n", "bb.facility_id,\n", "c.item_id,\n", "gp.product_id\n", "from gr_product_price_history gp\n", "left join mapping c on gp.product_id = c.product_id\n", "left join ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) bb on gp.merchant_id = bb.merchant_id and date(gp.install_ts+interval '5.5 hours') = bb.f_date\n", "where rnk =1\n", "and  date(gp.install_ts+interval '5.5 hours') BETWEEN dateadd(month,-1 ,date(current_date))\n", "and date(current_date)\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "\n", "final_live_days as\n", "(\n", "select item_id, facility_id, dateadd(month,-1 ,date(current_date)) as start_date,\n", "date(current_date) as end_date,\n", "count(distinct case when (datee between dateadd(month,-1 ,date(current_date))\n", "and date(current_date) ) Then datee end ) as live_days\n", "from price_history\n", "group by 1,2\n", ")\n", "\n", "select \n", "a.facility_id, start_date, end_date,\n", "--FF.NAME as facility_name,\n", "a.item_id,\n", "--if.item_name,\n", "--if.variant_description,\n", "--e.l0,\n", "--e.l1,\n", "--.l2,\n", "--if.is_pl,\n", "--e.ptype,\n", "--if.brand,\n", "--if.manufacturer,\n", "sum(live_days) as live_days,\n", "sum(qty) as qty,\n", "sum(gmv) as gmv\n", "from final_live_days a\n", "left join final_sales b on a. item_id = b.item_id and a.facility_id = b.facility_id\n", "left join ptype_tbl e on a.item_id = e.item_id\n", "left join (select * from item_level_info where row_rank = 1) if on if.item_id = a.item_id\n", "left join  lake_crates.facility  ff on ff.id = a.facility_id\n", "where  a.item_id is not null\n", "and a.facility_id in (135,\n", "                         197,\n", "                         209,\n", "                         238,\n", "                         219,\n", "                         223,\n", "                         227,\n", "                         237,\n", "                         247,\n", "                         246,\n", "                         285,\n", "                         271,\n", "                         201,\n", "                         161,\n", "                         228,\n", "                         319,\n", "                         370,1317,\n", "                         375,377,388,376,387,391,397,392)\n", "                         \n", "                         \n", "                         \n", "--and a.item_id in ()\n", "\n", "--where  a.merchant_id IN (25568,25813,26012,26015,26016,26049,26084,26168)\n", "--ande a.item_id = '10000624'\n", "--and e.l0 in ('Vegetables & Fruits' , 'Fruits & Vegetables')\n", "group by 1,2,3,4\n", "\n", "\"\"\"\n", "\n", "\n", "gmv = pd.read_sql_query(sql=gmv_query, con=CON_REDSHIFT)\n", "gmv.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_grouped = gmv.groupby([\"facility_id\"]).agg({\"gmv\": \"sum\", \"qty\": \"sum\"}).reset_index()\n", "gmv_grouped.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["carts_gmv = pd.merge(\n", "    carts, gmv_grouped, left_on=[\"facility_id\"], right_on=[\"facility_id\"], how=\"left\"\n", ")\n", "carts_gmv.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["carts_gmv[\"AOV\"] = carts_gmv[\"gmv\"] / carts_gmv[\"actual_carts\"]\n", "carts_gmv.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo_input[\"month\"] = pd.DatetimeIndex(ipo_input[\"dates\"]).month\n", "ipo_input[\"year\"] = pd.DatetimeIndex(ipo_input[\"dates\"]).year\n", "ipo_input[\"day\"] = pd.DatetimeIndex(ipo_input[\"dates\"]).day\n", "ipo_input.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_avg = ipo_input[[\"dates\", \"facility_id\", \"facility\", \"outlet_type\", \"items_per_order\"]]\n", "\n", "ipo_current_avg_daily = (\n", "    daily_avg.groupby([\"facility_id\", \"facility\", \"outlet_type\"]).quantile(0.90).reset_index()\n", ")\n", "ipo_current_avg_daily[\"Flag\"] = \"current_daily_avg\"\n", "ipo_current_avg_daily = ipo_current_avg_daily[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "ipo_current_avg_daily.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo_inbound_1_10 = ipo_input[ipo_input.day <= 10]\n", "inbound_1_10 = ipo_inbound_1_10[\n", "    [\"dates\", \"facility_id\", \"facility\", \"outlet_type\", \"items_per_order\"]\n", "]\n", "\n", "ipo_inbound_1_10_agg = (\n", "    inbound_1_10.groupby([\"facility_id\", \"facility\", \"outlet_type\"]).quantile(0.90).reset_index()\n", ")\n", "ipo_inbound_1_10_agg[\"Flag\"] = \"inbound_1_10\"\n", "ipo_inbound_1_10_agg = ipo_inbound_1_10_agg[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "ipo_inbound_1_10_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo_inbound_11_23 = ipo_input[(ipo_input.day > 10) & (ipo_input.day <= 23)]\n", "inbound_11_23 = ipo_inbound_11_23[\n", "    [\"dates\", \"facility_id\", \"facility\", \"outlet_type\", \"items_per_order\"]\n", "]\n", "\n", "ipo_inbound_11_23_agg = (\n", "    inbound_11_23.groupby([\"facility_id\", \"facility\", \"outlet_type\"]).quantile(0.90).reset_index()\n", ")\n", "ipo_inbound_11_23_agg[\"Flag\"] = \"inbound_11_23\"\n", "ipo_inbound_11_23_agg = ipo_inbound_11_23_agg[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "ipo_inbound_11_23_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo_inbound_23_end = ipo_input[(ipo_input.day > 23)]\n", "\n", "inbound_23_end = ipo_inbound_23_end[\n", "    [\"dates\", \"facility_id\", \"facility\", \"outlet_type\", \"items_per_order\"]\n", "]\n", "\n", "ipo_inbound_23_end_agg = (\n", "    inbound_23_end.groupby([\"facility_id\", \"facility\", \"outlet_type\"]).quantile(0.90).reset_index()\n", ")\n", "ipo_inbound_23_end_agg[\"Flag\"] = \"inbound_23_end\"\n", "ipo_inbound_23_end_agg = ipo_inbound_23_end_agg[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "ipo_inbound_23_end_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_ipo_input[\"month\"] = pd.DatetimeIndex(overall_ipo_input[\"dates\"]).month\n", "overall_ipo_input[\"year\"] = pd.DatetimeIndex(overall_ipo_input[\"dates\"]).year\n", "overall_ipo_input[\"day\"] = pd.DatetimeIndex(overall_ipo_input[\"dates\"]).day\n", "overall_ipo_input.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_avg_0 = (\n", "    ipo_input.groupby([\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"])\n", "    .agg({\"item_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_avg = pd.merge(\n", "    overall_avg_0,\n", "    overall_ipo_input[[\"dates\", \"facility_id\", \"facility\", \"sub_orders\", \"month\", \"year\", \"day\"]],\n", "    left_on=[\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"],\n", "    right_on=[\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"],\n", "    how=\"left\",\n", ")\n", "overall_avg.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_avg_0 = (\n", "    ipo_input.groupby([\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"])\n", "    .agg({\"item_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_avg = pd.merge(\n", "    overall_avg_0,\n", "    overall_ipo_input[[\"dates\", \"facility_id\", \"facility\", \"sub_orders\", \"month\", \"year\", \"day\"]],\n", "    left_on=[\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"],\n", "    right_on=[\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"],\n", "    how=\"left\",\n", ")\n", "overall_avg[\"items_per_order\"] = overall_avg[\"item_quantity\"] / overall_avg[\"sub_orders\"]\n", "\n", "overall_percentile = overall_avg[[\"dates\", \"facility_id\", \"facility\", \"items_per_order\"]]\n", "\n", "overall_percentile_agg = (\n", "    overall_percentile.groupby([\"facility_id\", \"facility\"]).quantile(0.90).reset_index()\n", ")\n", "overall_percentile_agg[\"Flag\"] = \"overall\"\n", "overall_percentile_agg[\"outlet_type\"] = \"overall\"\n", "overall_percentile_agg = overall_percentile_agg[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "overall_percentile_agg.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_values_0 = (\n", "    ipo_input.groupby([\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"])\n", "    .agg({\"item_quantity\": \"sum\"})\n", "    .reset_index()\n", ")\n", "overall_values = pd.merge(\n", "    overall_values_0,\n", "    overall_ipo_input[[\"dates\", \"facility_id\", \"facility\", \"sub_orders\", \"month\", \"year\", \"day\"]],\n", "    left_on=[\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"],\n", "    right_on=[\"dates\", \"facility_id\", \"facility\", \"month\", \"year\", \"day\"],\n", "    how=\"left\",\n", ")\n", "overall_values[\"items_per_order\"] = overall_values[\"item_quantity\"] / overall_values[\"sub_orders\"]\n", "overall_values = overall_values[\n", "    [\"dates\", \"facility_id\", \"facility\", \"items_per_order\", \"month\", \"year\", \"day\"]\n", "]\n", "overall_values.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overall_1_10 = overall_values[overall_values.day <= 10]\n", "\n", "overall_1_10_agg = overall_1_10.groupby([\"facility_id\", \"facility\"]).quantile(0.90).reset_index()\n", "overall_1_10_agg[\"Flag\"] = \"overall_1_10\"\n", "overall_1_10_agg[\"outlet_type\"] = \"overall\"\n", "overall_1_10_agg = overall_1_10_agg[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "\n", "\n", "overall_11_23 = overall_values[(overall_values.day > 10) & (overall_values.day <= 23)]\n", "overall_11_23_agg = overall_11_23.groupby([\"facility_id\", \"facility\"]).quantile(0.90).reset_index()\n", "overall_11_23_agg[\"Flag\"] = \"overall_11_23\"\n", "overall_11_23_agg[\"outlet_type\"] = \"overall\"\n", "overall_11_23_agg = overall_11_23_agg[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "\n", "\n", "overall_23_end = overall_values[(overall_values.day > 23)]\n", "overall_23_end_agg = (\n", "    overall_23_end.groupby([\"facility_id\", \"facility\"]).quantile(0.90).reset_index()\n", ")\n", "overall_23_end_agg[\"Flag\"] = \"overall_23_end\"\n", "overall_23_end_agg[\"outlet_type\"] = \"overall\"\n", "overall_23_end_agg = overall_23_end_agg[\n", "    [\"facility_id\", \"facility\", \"outlet_type\", \"Flag\", \"items_per_order\"]\n", "]\n", "\n", "\n", "overall_time = pd.concat([overall_1_10_agg, overall_11_23_agg, overall_23_end_agg])\n", "overall_time.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo_out = pd.concat(\n", "    [\n", "        ipo_current_avg_daily,\n", "        ipo_inbound_1_10_agg,\n", "        ipo_inbound_11_23_agg,\n", "        ipo_inbound_23_end_agg,\n", "        overall_percentile_agg,\n", "        overall_time,\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ipo_out[\"Key\"] = ipo_out[\"facility_id\"].astype(\"str\") + ipo_out[\"Flag\"] + ipo_out[\"outlet_type\"]\n", "ipo_out = ipo_out[[\"Key\", \"facility_id\", \"facility\", \"Flag\", \"outlet_type\", \"items_per_order\"]]\n", "ipo_out[\"items_per_order\"] = round(ipo_out[\"items_per_order\"], 1)\n", "ipo_out.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id_o = \"1aQfgk7HPXg0-fxYdXOYGrERqYI6Zuf0CwdCaFItQwI8\"\n", "output_sheet_d1 = \"IPO Data\"\n", "pb.to_sheets(ipo_out, sheet_id_o, output_sheet_d1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_sheet_d2 = \"AOV Inputs\"\n", "pb.to_sheets(carts_gmv, sheet_id_o, output_sheet_d2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}