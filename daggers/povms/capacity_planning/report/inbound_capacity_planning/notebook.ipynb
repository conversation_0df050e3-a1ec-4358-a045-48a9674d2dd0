{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date, datetime, timedelta\n", "import numpy as np\n", "import requests\n", "import json\n", "import uuid\n", "import calendar\n", "import os\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "import math\n", "import logging\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "import os\n", "import time\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.DEBUG)\n", "\n", "\n", "# Connection\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")\n", "\n", "# Dates\n", "today = date.today() - <PERSON><PERSON><PERSON>(days=0)\n", "difference = 0\n", "today_trick = today - <PERSON><PERSON><PERSON>(days=difference)\n", "current = today  # - <PERSON><PERSON><PERSON>(days=2)\n", "current_half = current - <PERSON><PERSON><PERSON>(days=16)\n", "print(\n", "    \"dates- today:\",\n", "    today,\n", "    \" | today_trick:\",\n", "    today_trick,\n", "    \" | current:\",\n", "    current,\n", "    \" | current_half:\",\n", "    current_half,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uuid"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Tag = \"22_Jul\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# directories\n", "\n", "GLOBAL_BASE_DIR = \"/shared/Shared/ankit_kumar/Automation/Capacity_Planning/V06\"\n", "logs = os.path.join(GLOBAL_BASE_DIR, Tag, \"logs\")\n", "outputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"outputs\")\n", "inputs = os.path.join(GLOBAL_BASE_DIR, Tag, \"inputs\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for _dir in [GLOBAL_BASE_DIR, logs, outputs, inputs]:\n", "    try:\n", "        os.makedirs(_dir)\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "from tabulate import tabulate\n", "\n", "\n", "def check_level(df, cols, identifier):\n", "    if df.shape[0] != df[cols].drop_duplicates().shape[0]:\n", "        query = \" and \".join([x + \" > 1\" for x in list(df.columns) if x not in cols])\n", "        temp_df = df.groupby(cols).count().reset_index()\n", "        sample = tabulate(temp_df.query(query), tablefmt=\"pipe\", headers=\"keys\")\n", "        error = f\"\"\":red_circle: *Alert*: Duplication found while extracting `{identifier}` for Run ID: `{Tag}`\\n\n", "        ```Count on grouped by {\", \".join(cols)}:\\n\\n{sample}```\"\"\"\n", "        print(error)\n", "        # raise Exception(\"Sorry, level does not match\", identifier)\n", "    else:\n", "        print(\"No errors\")\n", "        log_file_name = f\"{logs}/{Tag}_{identifier}_extracted_data.csv\"\n", "        df.to_csv(log_file_name, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Input Bulk Facility Transfer DOI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1EoQKedk5AmDCRZ-6Pk-Tenw0dvpy2BToVSALrHpb0ig\"\n", "bulk_facility_doi = pb.from_sheets(sheet_id, \"Sheet2\")\n", "bulk_facility_doi[[\"DOI\"]] = bulk_facility_doi[[\"DOI\"]].astype(\"int64\")\n", "bulk_facility_doi[[\"facility_id\"]] = bulk_facility_doi[[\"facility_id\"]].astype(\"float\")\n", "bulk_facility_doi.head(2)"]}, {"cell_type": "raw", "metadata": {}, "source": ["[602, 603, 483,387,513,494,415,517,416,375,417,418,419,420,421,228,238,246,247,271,285,319,370,403,404,135,209,219,223,227,237,377,422,423,397,376,391,392,435,436,437,388,438,439,440,464,465,466,467,468,469,470,471,472,473,474,475,458,459,460,461,462,463,478,441,479,442,480,443,444,445,446,447,161,451,452,453,454,455,456,457,424,425,428,429,430,431,432,433,434,481,482,201,197,\n", "1,3,12,13,15,22,24,26,29,30,32,34,42,43,48,49,50,56,63,92,116,139,140,149,158,159,212,213,236,240,264,268,269,299,291,906,555,807,398,387,415,416,375,417,418,419,420,421,228,238,246,247,271,285,319,370,403,404,135,209,219,223,227,237,377,422,423,397,376,391,392,435,436,437,388,438,439,440,464,465,466,467,468,469,470,471,472,473,474,475,458,459,460,461,462,463,478,441,479,442,480,443,444,445,446,447,161,451,452,453,454,455,456,457,424,425,428,429,430,431,432,433,434,481,482,201,197]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Pulling Input Data"]}, {"cell_type": "raw", "metadata": {}, "source": ["facility = list(set([602, 969, 603, 483,387,513,494,415,517,416,375,417,418,419,420,421,228,238,246,247,271,285,319,370,403,404,135,209,219,223,227,237,377,422,423,397,376,391,392,435,436,437,388,438,439,440,464,465,466,467,468,469,470,471,472,473,474,475,458,459,460,461,462,463,478,441,479,442,480,443,444,445,446,447,161,451,452,453,454,455,456,457,424,425,428,429,430,431,432,433,434,481,482,201,197,1,3,12,13,15,22,24,26,29,30,32,34,42,43,48,49,50,56,63,92,116,139,140,149,158,159,212,213,236,240,264,268,269,299,291,906,555,807,398, 387,415,416,375,417,418,419,420,421,228,238,246,247,271,285,319,370,403,404,135,209,219,223,227,237,377,422,423,397,376,391,392,435,436,437,388,438,439,440,464,465,466,467,468,469,470,471,472,473,474,475,458,459,460,461,462,463,478,441,479,442,480,443,444,445,446,447,161,451,452,453,454,455,456,457,424,425,428,429,430,431,432,433,434,481,482,201,197]))\n", "all_facilities = \", \".join(map(str,list(facility)\n", "                        )).replace(', nan','')"]}, {"cell_type": "raw", "metadata": {}, "source": ["#orig\n", "facility = list(set([602, 969, 603, 483,387,513,494,415,517,416,375,417,418,419,420,421,228,238,246,247,271,285,319,370,403,404,135,209,219,223,227,237,377,422,423,397,376,391,392,435,436,437,388,438,439,440,464,465,466,467,468,469,470,471,472,473,474,475,458,459,460,461,462,463,478,441,479,442,480,443,444,445,446,447,161,451,452,453,454,455,456,457,424,425,428,429,430,431,432,433,434,481,482,201,197,1,3,12,13,15,22,24,26,29,30,32,34,42,43,48,49,50,56,63,92,116,139,140,149,158,159,212,213,236,240,264,268,269,299,291,906,555,807,398, 387,415,416,375,417,418,419,420,421,228,238,246,247,271,285,319,370,403,404,135,209,219,223,227,237,377,422,423,397,376,391,392,435,436,437,388,438,439,440,464,465,466,467,468,469,470,471,472,473,474,475,458,459,460,461,462,463,478,441,479,442,480,443,444,445,446,447,161,451,452,453,454,455,456,457,424,425,428,429,430,431,432,433,434,481,482,201,197]))\n", "all_facilities = \", \".join(map(str,list(facility)\n", "                        )).replace(', nan','')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility = list(\n", "    set(\n", "        [\n", "            602,\n", "            969,\n", "            603,\n", "            483,\n", "            387,\n", "            513,\n", "            494,\n", "            415,\n", "            517,\n", "            416,\n", "            375,\n", "            417,\n", "            418,\n", "            419,\n", "            420,\n", "            421,\n", "            228,\n", "            238,\n", "            246,\n", "            247,\n", "            271,\n", "            285,\n", "            319,\n", "            370,\n", "            403,\n", "            404,\n", "            135,\n", "            209,\n", "            219,\n", "            223,\n", "            227,\n", "            237,\n", "            377,\n", "            422,\n", "            423,\n", "            397,\n", "            376,\n", "            391,\n", "            392,\n", "            435,\n", "            436,\n", "            437,\n", "            388,\n", "            438,\n", "            439,\n", "            440,\n", "            464,\n", "            465,\n", "            466,\n", "            467,\n", "            468,\n", "            469,\n", "            470,\n", "            471,\n", "            472,\n", "            473,\n", "            474,\n", "            475,\n", "            458,\n", "            459,\n", "            460,\n", "            461,\n", "            462,\n", "            463,\n", "            478,\n", "            441,\n", "            479,\n", "            442,\n", "            480,\n", "            443,\n", "            444,\n", "            445,\n", "            446,\n", "            447,\n", "            161,\n", "            451,\n", "            452,\n", "            453,\n", "            454,\n", "            455,\n", "            456,\n", "            457,\n", "            424,\n", "            425,\n", "            428,\n", "            429,\n", "            430,\n", "            431,\n", "            432,\n", "            433,\n", "            434,\n", "            481,\n", "            482,\n", "            201,\n", "            197,\n", "            1,\n", "            3,\n", "            12,\n", "            13,\n", "            15,\n", "            22,\n", "            24,\n", "            26,\n", "            29,\n", "            30,\n", "            32,\n", "            34,\n", "            42,\n", "            43,\n", "            48,\n", "            49,\n", "            50,\n", "            56,\n", "            63,\n", "            92,\n", "            116,\n", "            139,\n", "            140,\n", "            149,\n", "            158,\n", "            159,\n", "            212,\n", "            213,\n", "            236,\n", "            240,\n", "            264,\n", "            268,\n", "            269,\n", "            299,\n", "            291,\n", "            906,\n", "            555,\n", "            807,\n", "            398,\n", "            387,\n", "            415,\n", "            416,\n", "            375,\n", "            417,\n", "            418,\n", "            419,\n", "            420,\n", "            421,\n", "            228,\n", "            238,\n", "            246,\n", "            247,\n", "            271,\n", "            285,\n", "            319,\n", "            370,\n", "            403,\n", "            404,\n", "            135,\n", "            209,\n", "            219,\n", "            223,\n", "            227,\n", "            237,\n", "            377,\n", "            422,\n", "            423,\n", "            397,\n", "            376,\n", "            391,\n", "            392,\n", "            435,\n", "            436,\n", "            437,\n", "            388,\n", "            438,\n", "            439,\n", "            440,\n", "            464,\n", "            465,\n", "            466,\n", "            467,\n", "            468,\n", "            469,\n", "            470,\n", "            471,\n", "            472,\n", "            473,\n", "            474,\n", "            475,\n", "            458,\n", "            459,\n", "            460,\n", "            461,\n", "            462,\n", "            463,\n", "            478,\n", "            441,\n", "            479,\n", "            442,\n", "            480,\n", "            443,\n", "            444,\n", "            445,\n", "            446,\n", "            447,\n", "            161,\n", "            451,\n", "            452,\n", "            453,\n", "            454,\n", "            455,\n", "            456,\n", "            457,\n", "            424,\n", "            425,\n", "            428,\n", "            429,\n", "            430,\n", "            431,\n", "            432,\n", "            433,\n", "            434,\n", "            481,\n", "            482,\n", "            201,\n", "            197,\n", "            415,\n", "            387,\n", "            416,\n", "            375,\n", "            730,\n", "            731,\n", "            732,\n", "            733,\n", "            417,\n", "            911,\n", "            419,\n", "            734,\n", "            910,\n", "            418,\n", "            912,\n", "            420,\n", "            228,\n", "            473,\n", "            441,\n", "            468,\n", "            469,\n", "            474,\n", "            704,\n", "            1009,\n", "            1015,\n", "            1016,\n", "            442,\n", "            465,\n", "            470,\n", "            472,\n", "            471,\n", "            479,\n", "            480,\n", "            459,\n", "            466,\n", "            463,\n", "            478,\n", "            475,\n", "            462,\n", "            467,\n", "            701,\n", "            461,\n", "            796,\n", "            464,\n", "            778,\n", "            444,\n", "            779,\n", "            446,\n", "            447,\n", "            443,\n", "            786,\n", "            319,\n", "            197,\n", "            404,\n", "            285,\n", "            448,\n", "            219,\n", "            788,\n", "            237,\n", "            370,\n", "            227,\n", "            223,\n", "            449,\n", "            271,\n", "            450,\n", "            785,\n", "            238,\n", "            789,\n", "            246,\n", "            445,\n", "            247,\n", "            787,\n", "            403,\n", "            209,\n", "            725,\n", "            422,\n", "            377,\n", "            769,\n", "            770,\n", "            768,\n", "            425,\n", "            397,\n", "            720,\n", "            859,\n", "            723,\n", "            429,\n", "            724,\n", "            430,\n", "            854,\n", "            376,\n", "            856,\n", "            428,\n", "            722,\n", "            864,\n", "            434,\n", "            433,\n", "            391,\n", "            432,\n", "            867,\n", "            437,\n", "            392,\n", "            742,\n", "            542,\n", "            738,\n", "            436,\n", "            435,\n", "            599,\n", "            543,\n", "            439,\n", "            438,\n", "            388,\n", "            790,\n", "            201,\n", "            681,\n", "            456,\n", "            161,\n", "            455,\n", "            451,\n", "            457,\n", "            791,\n", "            453,\n", "            792,\n", "            452,\n", "            454,\n", "            50,\n", "            63,\n", "            34,\n", "            517,\n", "            24,\n", "            483,\n", "            236,\n", "            291,\n", "            906,\n", "            92,\n", "            268,\n", "            264,\n", "            26,\n", "            29,\n", "            12,\n", "            554,\n", "            555,\n", "            15,\n", "            494,\n", "            807,\n", "            398,\n", "            299,\n", "            747,\n", "            116,\n", "            3,\n", "            42,\n", "            43,\n", "            1,\n", "            48,\n", "            56,\n", "            513,\n", "            22,\n", "        ]\n", "    )\n", ")\n", "all_facilities = \", \".join(map(str, list(facility))).replace(\", nan\", \"\")"]}, {"cell_type": "raw", "metadata": {}, "source": ["facility = [602, 603, 483,387,513,494,415,517,416,375,417,418,419,420,421,228,238,246,247,271,285,319,370,403,404,135,209,219,223,227,237,377,422,423,397,376,391,392,435,436,437,388,438,439,440,464,465,466,467,468,469,470,471,472,473,474,475,458,459,460,461,462,463,478,441,479,442,480,443,444,445,446,447,161,451,452,453,454,455,456,457,424,425,428,429,430,431,432,433,434,481,482,201,197,\n", "1,3,12,13,15,22,24,26,29,30,32,34,42,43,48,49,50,56,63,92,116,139,140,149,158,159,212,213,236,240,264,268,269,299]\n", "all_facilities = \", \".join(map(str,list(facility)\n", "                        )).replace(', nan','')"]}, {"cell_type": "raw", "metadata": {}, "source": ["facility = [50,63,34,517,24,483,236,291,906,92,268,264,26,29,12,554,555,15,494,807,398,299,747,116,3,42,43,1,48,56,513,22]\n", "all_facilities = \", \".join(map(str,list(facility)\n", "                        )).replace(', nan','')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. Get CPD\n", "\n", "# 1a: master_assortment - Active\n", "master_assortment_query = f\"\"\"SELECT DISTINCT facility_name,\n", "                facility_id,\n", "                item_id\n", "FROM\n", "  (SELECT DISTINCT internal_facility_identifier AS \"facility_name\",\n", "                   ma.facility_id,\n", "                   ma.item_id AS \"item_id\",\n", "                   rpp.name,\n", "                   (CASE\n", "                        WHEN rpp.outlet_type = 1 THEN \"F&V type\"\n", "                        WHEN rpp.outlet_type = 2 THEN \"Grocery type\"\n", "                        ELSE 'Not Assigned'\n", "                    END) AS \"Item Type\",\n", "                   pmas.name AS master_assortment_substate,\n", "                   left(ma.updated_at,19) AS last_update\n", "   FROM rpc.product_facility_master_assortment ma\n", "   LEFT JOIN rpc.product_master_assortment_substate pmas ON ma.master_assortment_substate_id = pmas.id\n", "   LEFT JOIN po.physical_facility pf ON pf.facility_id = ma.facility_id\n", "   LEFT JOIN rpc.product_product rpp ON rpp.item_id = ma.item_id\n", "   WHERE ma.facility_id IN ({all_facilities})\n", "     AND master_assortment_substate_id = 1\n", "     AND rpp.outlet_type = 2  #Groceies\n", "     AND rpp.active = 1\n", "     AND rpp.id IN\n", "       (SELECT max(id) AS id\n", "        FROM rpc.product_product pp\n", "        WHERE pp.active = 1\n", "          AND pp.approved = 1\n", "        GROUP BY item_id)\n", "   ORDER BY internal_facility_identifier ASC) AS a\"\"\"\n", "\n", "master_assortment_0 = read_sql_query(sql=master_assortment_query, con=retail)\n", "check_level(master_assortment_0, [\"facility_id\", \"item_id\"], \"master_assortment\")\n", "master_assortment_0.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_assortment = master_assortment_0[[\"facility_id\", \"item_id\"]]\n", "master_assortment.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ordering_mapping_query = f\"\"\"SELECT DISTINCT item_id,\n", "                rco.facility_id,\n", "                pf.internal_facility_identifier AS frontend_name,\n", "                tag_value AS ordering_outlet,\n", "                rcob.name AS ordering_outlet_name,\n", "                rcob.facility_id as backend_facility_id,\n", "                pfb.internal_facility_identifier AS ordering_name\n", "FROM lake_rpc.item_outlet_tag_mapping tm\n", "LEFT JOIN lake_retail.console_outlet rco ON rco.id = tm.outlet_id\n", "INNER JOIN lake_po.physical_facility pf ON pf.facility_id = rco.facility_id\n", "LEFT JOIN lake_retail.console_outlet rcob ON rcob.id = tm.tag_value\n", "INNER JOIN lake_po.physical_facility pfb ON pfb.facility_id = rcob.facility_id\n", "WHERE tag_type_id = 8 and\n", "tm.active = 1 and frontend_name not in ('Super Store Chennai C2 - Warehouse','Kolkata_ES2')\n", "ORDER BY pf.internal_facility_identifier ASC\"\"\"\n", "backend_facility = read_sql_query(sql=ordering_mapping_query, con=CON_REDSHIFT)\n", "backend_facility = backend_facility[\n", "    [\"facility_id\", \"item_id\", \"backend_facility_id\", \"ordering_name\"]\n", "].drop_duplicates()\n", "check_level(\n", "    backend_facility,\n", "    [\"facility_id\", \"item_id\", \"backend_facility_id\"],\n", "    \"backend_facility\",\n", ")\n", "backend_facility.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["variable = (\n", "    backend_facility.groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"backend_facility_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "variable = variable[variable.backend_facility_id > 1]\n", "variable[\"key\"] = (\n", "    variable[\"facility_id\"].astype(str) + \"_\" + variable[\"item_id\"].astype(str)\n", ")\n", "variable = variable[[\"key\"]]\n", "\n", "backend_facility[\"o_key\"] = (\n", "    backend_facility[\"facility_id\"].astype(str)\n", "    + \"_\"\n", "    + backend_facility[\"item_id\"].astype(str)\n", ")\n", "\n", "backend_facility_temp = pd.merge(\n", "    backend_facility, variable, left_on=[\"o_key\"], right_on=[\"key\"], how=\"left\"\n", ")\n", "\n", "backend_facility_non_dup = backend_facility_temp[backend_facility_temp.key.isna()]\n", "backend_facility_dup = backend_facility_temp[~backend_facility_temp.key.isna()]\n", "\n", "duplicate_set = backend_facility_dup[\n", "    [\"facility_id\", \"backend_facility_id\"]\n", "].drop_duplicates()  # 517 for 428, 299?,  140?\n", "\n", "req_back = (\n", "    backend_facility_non_dup.groupby([\"facility_id\", \"backend_facility_id\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "# req_back[req_back.facility_id.isin([428, 299, 140])]\n", "\n", "req_back = pd.merge(\n", "    req_back, duplicate_set, on=[\"facility_id\", \"backend_facility_id\"], how=\"inner\"\n", ")\n", "\n", "req_back[\"rank\"] = req_back.groupby([\"facility_id\"])[\"item_id\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "req_back = req_back[req_back[\"rank\"] == 1]\n", "req_back = req_back[[\"facility_id\", \"backend_facility_id\"]].drop_duplicates()\n", "\n", "backend_facility_dup = pd.merge(\n", "    backend_facility_dup,\n", "    req_back,\n", "    on=[\"facility_id\", \"backend_facility_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "backend_facility = pd.concat([backend_facility_dup, backend_facility_non_dup])\n", "backend_facility = backend_facility.drop(columns={\"o_key\", \"key\"})\n", "backend_facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(backend_facility, [\"facility_id\", \"item_id\"], \"backend_facility\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# frontend_facility_name_mapping\n", "\n", "frontend_facility_mapping_query = f\"\"\"SELECT DISTINCT rco.facility_id,\n", "                                        pf.internal_facility_identifier AS frontend_name\n", "                                        FROM lake_rpc.item_outlet_tag_mapping tm\n", "                                        LEFT JOIN lake_retail.console_outlet rco ON rco.id = tm.outlet_id\n", "                                        INNER JOIN lake_po.physical_facility pf ON pf.facility_id = rco.facility_id\n", "                                        --WHERE tag_type_id = 8\n", "                                          WHERE tm.active = 1 and frontend_name not in ('Super Store Chennai C2 - Warehouse','Kolkata_ES2')\n", "                                        ORDER BY pf.internal_facility_identifier ASC\"\"\"\n", "frontend_facility_mapping = read_sql_query(\n", "    sql=frontend_facility_mapping_query, con=CON_REDSHIFT\n", ")\n", "check_level(frontend_facility_mapping, [\"facility_id\"], \"frontend_facility_mapping\")\n", "frontend_facility_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1c: FacilityID_OutletID_Mapping\n", "\n", "mapp_query = f\"\"\"SELECT id AS outlet_id,facility_id\n", "                FROM\n", "                 (SELECT m.*,\n", "                         row_number() over(PARTITION BY updated_at,id ORDER BY updated_at DESC) AS rnk\n", "                  FROM lake_retail.console_outlet m)\n", "               WHERE rnk =1\"\"\"\n", "mapp = read_sql_query(sql=mapp_query, con=CON_REDSHIFT)\n", "check_level(mapp, [\"outlet_id\"], \"mapp\")\n", "mapp.head(1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["(435,413,466,476,852,855,713,410,432,436,521,979,604,483,1002,434,1103,\n", "427,445,527,437,1158,1052,513,581,906,429,1000,1111,1118,682,1146,448,419,479,861,864,715,418,433,\n", "440,523,606,484,529,428,446,611,1161,515,684,908,431,1113)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv_query = f\"\"\"with\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "name as item_name,\n", "brand,\n", "brand_id,\n", "manufacturer,\n", "variant_description,\n", "is_pl,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", " it_pd as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "multiplier,\n", "updated_on,\n", "max(updated_on) over (partition by product_id) as last_updated\n", "from \n", "it_pd_log\n", "),\n", "\n", "mapping as\n", "(\n", "select \n", "distinct\n", "item_id,\n", "product_id,\n", "coalesce(multiplier,1) as multiplier\n", "from it_pd\n", "where last_updated = updated_on\n", "),\n", "\n", "categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "P.UNIT AS Unit,\n", "C2.NAME AS L2,\n", "(case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "C.NAME AS L0,\n", "P.BRAND AS brand,\n", "P.MANUFACTURER AS manf,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "), \n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l0,\n", "cat.l1,\n", "cat.l2,\n", "cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l0) as l0,\n", "max(l1) as l1,\n", "max(l2) as l2,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", "),\n", "\n", "pre_oms as\n", "(\n", "select * from\n", "lake_oms_bifrost.oms_order\n", "where install_ts >= current_date-120\n", "),\n", "\n", "pre_oms_IT as\n", "(\n", "select * from\n", "lake_oms_bifrost.oms_order_item\n", "where install_ts >= current_date-120\n", "),\n", "\n", "tbl_treatment as\n", "(\n", "select\n", "a.*,\n", "date(a.install_ts + interval '5.5 Hours') as checkout_date,\n", "om.city_name,\n", "b.product_id,\n", "c.item_id,\n", "d.is_pl,\n", "b.quantity*multiplier as quantity,\n", "multiplier,\n", "b.selling_price,\n", "om.external_id as virtual_merchant_id,\n", "d.variant_mrp,\n", "sum(d.variant_mrp) over (partition by a.id, b.product_id) as total_mrp\n", "from pre_oms a\n", "left join lake_oms_bifrost.oms_merchant om on a.merchant_id = om.id\n", "left join (select * from pre_oms_IT where freebie_id is null and date(install_ts + interval '5.5 Hours') between current_date - 220 AND current_date) b \n", "on a.id = b.order_id\n", "left join mapping c on b.product_id = c.product_id\n", "left join (select * from item_level_info where row_rank = 1) d on c.item_id = d.item_id\n", "left join ptype_tbl e on c.item_id = e.item_id\n", "left  join (select * from all_margins lq where lq_margin >= 0.01 )lq on lq.pid = b.product_id and lq.city = om.city_name and \n", "(a.install_ts + interval '5.5 Hours') >= lq.start_date and date(a.install_ts + interval '5.5 Hours') <= lq.end_date\n", "where\n", "date(a.install_ts + interval '5.5 Hours') between dateadd(month,-1 ,dateadd(day,-{difference},date(current_date)))\n", "and dateadd(day,-{difference},date(current_date))\n", "and (a.\"type\" is null or a.\"type\" in ('RetailForwardOrder','RetailSuborder'))\n", "and om.city_name not in ('Not in service area', 'Hapur', 'Test city')\n", "AND om.city_name not ilike '%%b2b%%'\n", "--and current_status = 'CANCELLED'\n", "and lq.pid is null\n", "and b.product_id not in (413201,413202,413203,413204,413205,413206,413207,413225,413226,413227,413228,413229,413230,413231,413232,413233,413234,413264,413279)\n", "-- l0 not in ('Fruits & Vegetables', 'Vegetables & Fruits')\n", "),\n", "\n", "merchant_and_store_join as\n", "(\n", "select \n", "a.*,\n", "selling_price*variant_mrp*1.000/(total_mrp*multiplier) as item_selling_price,\n", "bb.facility_id\n", "from tbl_treatment a\n", "left join ( select *, row_number() over (partition by merchant_name,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) bb on a.virtual_merchant_id = bb.merchant_id and date(a.checkout_date) = bb.f_date\n", "where rnk =1\n", "),\n", "\n", "final_sales as\n", "(\n", "select \n", "a.facility_id,\n", "a.item_id, avg(item_selling_price) as Avg_SP,\n", "sum(a.quantity) as qty,\n", "sum(a.quantity*item_selling_price) as gmv\n", "from merchant_and_store_join a\n", "--where a.checkout_date date(dateadd(day,1,last_day(dateadd(month, -2, LAST_DAY(current_date)))))\n", "--and date(dateadd(day,1,last_day(dateadd(month, -2, LAST_DAY(current_date))))) + 30\n", "group by 1,2\n", "),\n", "\n", "price_history as (\n", "select\n", "date(gp.install_ts+interval '5.5 hours') as datee,\n", "bb.facility_id,\n", "c.item_id,\n", "gp.product_id\n", "from gr_product_price_history gp\n", "left join mapping c on gp.product_id = c.product_id\n", "left join ( select *, row_number() over (partition by merchant_id,f_date order by update_at desc) as rnk\n", "                from Merchant_forecast_carts) bb on gp.merchant_id = bb.merchant_id and date(gp.install_ts+interval '5.5 hours') = bb.f_date\n", "where rnk =1\n", "and  date(gp.install_ts+interval '5.5 hours') BETWEEN dateadd(month,-1 ,dateadd(day,-{difference},date(current_date)))\n", "and dateadd(day,-{difference},date(current_date))\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "\n", "final_live_days as\n", "(\n", "select item_id, facility_id, dateadd(month,-1 ,dateadd(day,-{difference},date(current_date))) as start_date,\n", "dateadd(day,-{difference},date(current_date)) as end_date,\n", "count(distinct case when (datee between dateadd(month,-1 ,dateadd(day,-{difference},date(current_date)))\n", "and dateadd(day,-{difference},date(current_date)) ) Then datee end ) as live_days\n", "from price_history\n", "group by 1,2\n", ")\n", "\n", "select \n", "a.facility_id, start_date, end_date,\n", "--FF.NAME as facility_name,\n", "a.item_id,\n", "--if.item_name,\n", "--if.variant_description,\n", "--e.l0,\n", "--e.l1,\n", "--.l2,\n", "--if.is_pl,\n", "--e.ptype,\n", "--if.brand,\n", "--if.manufacturer,\n", "Avg_SP,\n", "sum(live_days) as live_days,\n", "sum(qty) as qty,\n", "sum(gmv) as gmv,\n", "sum(gmv) / sum(qty) as ASP\n", "from final_live_days a\n", "left join final_sales b on a. item_id = b.item_id and a.facility_id = b.facility_id\n", "left join ptype_tbl e on a.item_id = e.item_id\n", "left join (select * from item_level_info where row_rank = 1) if on if.item_id = a.item_id\n", "left join  lake_crates.facility  ff on ff.id = a.facility_id\n", "where  a.item_id is not null\n", "and a.facility_id in ({all_facilities})\n", "group by 1,2,3,4,5\n", "\n", "\"\"\"\n", "\n", "\n", "gmv = read_sql_query(sql=gmv_query, con=CON_REDSHIFT)\n", "check_level(gmv, [\"facility_id\", \"item_id\"], \"gmv\")\n", "gmv.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gmv.item_id = gmv.item_id.astype(\"int\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["consumption_query = f\"\"\"select\n", "a.item_id ,\n", "facility_id,\n", "CASE WHEN date(date) BETWEEN date(%(start_date)s) AND date(LAST_DAY(date(%(start_date)s))) THEN \n", "       CONCAT(cast(%(start_date)s AS date),CONCAT(' : ', CAST(LAST_DAY(cast(%(start_date)s  AS date)) AS DATE)))\n", "       WHEN cast(date AS date) BETWEEN CAST(DATEADD(DAY,1,LAST_DAY(cast(%(start_date)s AS date))) AS DATE) AND  last_day(dateadd(day,1,last_day(cast(%(start_date)s AS date))))\n", "       THEN CONCAT(CAST(DATEADD(DAY,1,LAST_DAY(cast(%(start_date)s AS date))) AS DATE),CONCAT(' : ', last_day(dateadd(day,1,last_day(cast(%(start_date)s AS date))))))\n", "       ELSE 'Other' END AS DateRange,\n", "sum(consumption) as consumption\n", "from  lake_snorlax.date_wise_consumption  a\n", "left join lake_retail.view_console_outlet b on a.outlet_id = b.id\n", "where\n", "--date(date) between date(%(start_date)s) and last_day(dateadd(month,1,last_day(date(%(start_date)s))))\n", "date(date) between cast(%(start_date)s AS date) AND  last_day(dateadd(day,1,last_day(cast(%(start_date)s AS date))))\n", "group by 1,2,3\"\"\"\n", "\n", "consumption = pd.read_sql_query(\n", "    sql=consumption_query, con=CON_REDSHIFT, params={\"start_date\": today_trick}\n", ")\n", "check_level(consumption, [\"facility_id\", \"item_id\", \"daterange\"], \"consumption\")\n", "consumption.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DOI_Input_query = f\"\"\"select distinct item_id, facility_id, TEA_tag, ordering_facility, buying_cycle, DOI_cycle\n", "FROM (\n", "select hot.*, grof.vendor_load_type, grof.load_size, grof.po_days, grof.buying_cycle, grof.DOI_cycle\n", "from\n", "(\n", "select tuv.*, kuv.vendor_name, kuv.vendor_id, kuv.case_sensitivity_type, kuv.tat_days\n", "from\n", "\n", "(select x.*,y.ordering_facility_id,\n", "if(y.ordering_facility_id is null or y.ordering_facility_id = \"\", \"Non-TEA\",\"TEA\") as TEA_tag,\n", "if(y.ordering_facility_id is null or y.ordering_facility_id = \"\", x.facility_id,y.ordering_facility_id) as ordering_facility\n", "from\n", "(select a.item_id,a.name as Item_name, b.facility_id,c.name as manufacturer_name,a.manufacturer_id from\n", "(select distinct p.item_id,p.name,p.weight_in_gm, p.inner_case_size, p.outer_case_size,q.manufacturer_id, max(p.updated_at) from rpc.item_details p\n", "inner join\n", "rpc.product_brand q\n", "on p.brand_id = q.id\n", "where p.active = 1 and p.approved = 1\n", "group by 1,2,3,4,5,6) a\n", "inner join \n", "(select item_id,city_id,facility_id,max(updated_at) from rpc.product_facility_master_assortment\n", "where master_assortment_substate_id\t = 1 and facility_id in ({all_facilities})\n", "and item_id not in (10063805,10063810,10045801)\n", "group by 1,2,3) b\n", "on a.item_id=b.item_id\n", "inner join rpc.product_manufacturer c on a.manufacturer_id = c.id\n", "group by 1,2,3,4,5) x\n", "left join\n", "    (select distinct ab.item_id, cd.name as frontend_facility, ab.facility_id, ab.ordering_outlet,\n", "     ab.ordering_facility,ab.ordering_facility_id, ab.active\n", "     from\n", "     (select a.item_id, b.name as facility_name, b.facility_id,a.tag_value as ordering_outlet,\n", "     a.ordering_facility,a.ordering_facility_id, a.active\n", "     from\n", "     (select c.item_id, c.outlet_id, c.tag_value,d.name as ordering_facility,d.facility_id as ordering_facility_id,\n", "     c.active from rpc.item_outlet_tag_mapping c\n", "     inner join\n", "     retail.console_outlet d\n", "     on c.tag_value = d.id\n", "     where c.tag_type_id = 8 and c.active = 1 and c.outlet_id not in (582)\n", "     and d.facility_id is not null\n", "     group by 1,2,3,4,5,6) a\n", "     inner join \n", "     (select id, name, facility_id from retail.console_outlet\n", "     where facility_id is not null) b\n", "     on\n", "     a.outlet_id = b.id\n", "     group by 3,1,2,4,5,6) ab\n", "     inner join\n", "     retail.warehouse_facility cd\n", "     on \n", "     ab.facility_id = cd.id ) y\n", "on x.item_id = y.item_id\n", "and x.facility_id = y.facility_id ) tuv\n", "\n", "left join\n", "\n", "     (select  ab.item_id,ab.facility_id,ab.vendor_name, ab.vendor_id,ab.case_sensitivity_type,bc.tat_days\n", "     from \n", "     \n", "     (select k.*,p.vendor_name\n", "     from \n", "     (select h.item_id,h.facility_id,h.vendor_id,i.case_sensitivity_type, max(h.updated_at), max(i.updated_at) from \n", "     vms.vms_vendor_facility_alignment h\n", "          left join\n", "     vms.vendor_item_physical_facility_attributes i\n", "     on h.item_id = i.item_id\n", "     and h.facility_id = i.facility_id\n", "     and h.vendor_id = i.vendor_id\n", "     group by 1,2,3,4) k inner join vms.vms_vendor p on k.vendor_id = p.id ) ab\n", "\n", "left join \n", "\n", "(select a.item_id,b.facility_id,a.vendor_id,a.tat_days, max(a.updated_at)\n", "from\n", "po.item_outlet_vendor_tat a\n", "inner join\n", "retail.console_outlet b\n", "on a.outlet_id = b.id\n", "/*where a.outlet_id in (435,413,466,476,852,855,713,410,432,436,521,979,604,483,1002,434,1103,\n", "427,445,527,437,1158,1052,513,581,906,429,1000,1111,1118,682,1146,448,419,479,861,864,715,418,433,\n", "440,523,606,484,529,428,446,611,1161,515,684,908,431,1113)*/\n", "group by 1,2,3,4) bc\n", "\n", "on ab.vendor_id = bc.vendor_id\n", "and ab.facility_id = bc.facility_id\n", "and ab.item_id = bc.item_id ) kuv\n", "\n", "on tuv.item_id = kuv.item_id\n", "and tuv.ordering_facility = kuv.facility_id ) hot\n", "\n", "left join\n", "\n", "  (select x.vendor_id,x.facility_id,x.manufacturer_id, y.load_size,y.vendor_load_type, x.po_days,x.buying_cycle,x.DOI_cycle\n", "   from \n", "  (select a.vendor_id, a.facility_id, a.manufacturer_id,  a. po_days, a.po_cycle, case when a.po_cycle = 1 then 'Daily'\n", "   when a.po_cycle = 2 then 'Alternate days'\n", "   when a.po_cycle = 3 then 'Biweekly'\n", "   when a.po_cycle = 4 then 'Biweekly' \n", "   when a.po_cycle = 5 then 'Biweekly'\n", "   when a.po_cycle = 6 then 'Weekly'\n", "   when a.po_cycle = 7 then 'Weekly'\n", "   when a.po_cycle = 11 then 'Fortnightly'\n", "   when a.po_cycle = 13 then 'Fortnightly'\n", "   when a.po_cycle = 14 then 'Fortnightly'\n", "   when a.po_cycle = 15 then 'Fortnightly'\n", "   when a.po_cycle = 21 then 'Every-21-days'\n", "   when a.po_cycle = 22 then 'Every-21-days'\n", "   when a.po_cycle = 30 then 'Monthly'\n", "   when a.po_cycle = 31 then 'Monthly' end as buying_cycle , \n", "   \n", "   case when a.po_cycle = 1 then 5\n", "   when a.po_cycle = 2 then 7\n", "   when a.po_cycle = 3 then 9\n", "   when a.po_cycle = 4 then 9\n", "   when a.po_cycle = 5 then 9\n", "   when a.po_cycle = 6 then 19\n", "   when a.po_cycle = 7 then 19\n", "   when a.po_cycle = 11 then 22\n", "   when a.po_cycle = 13 then 22\n", "   when a.po_cycle = 14 then 22\n", "   when a.po_cycle = 15 then 22\n", "   when a.po_cycle = 21 then 26\n", "   when a.po_cycle = 22 then 26\n", "   when a.po_cycle = 30 then 30\n", "   when a.po_cycle = 31 then 30 end as DOI_cycle ,\n", "   max(a.updated_at) as mfg_updated_at\n", "   from vms.vendor_manufacturer_physical_facility_attributes a\n", "   where  po_model = '2'\n", "   group by 1,2,3,4,5,6\n", "   order by 1,2,5 asc) x\n", "    inner join \n", "    (select a.vendor_id, a.facility_id, a.load_type, case when a.load_type = 1 then 'VALUE'\n", "      when a.load_type = 2 then 'WEIGHT'\n", "      when a.load_type = 3 then 'CASE' end as vendor_load_type, a.load_size, max(a.updated_at) as vendor_updated_at\n", "      from vms.vendor_physical_facility_attributes a\n", "      group by 1,2,3)y  \n", "   on x.vendor_id =y.vendor_id\n", "   and x.facility_id = y.facility_id ) grof\n", "\n", "on  hot.vendor_id = grof.vendor_id\n", "and hot.ordering_facility = grof.facility_id\n", "and hot.manufacturer_id = grof.manufacturer_id\n", "\n", ") AS Tab_1\"\"\"\n", "\n", "DOI_Input = read_sql_query(\n", "    sql=DOI_Input_query, con=retail\n", ")  # ,params={'start_date':today})\n", "DOI_Input.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["variable = (\n", "    DOI_Input.groupby([\"facility_id\", \"item_id\"])\n", "    .agg({\"ordering_facility\": \"count\"})\n", "    .reset_index()\n", ")\n", "variable = variable[variable.ordering_facility > 1]\n", "variable[\"key\"] = (\n", "    variable[\"facility_id\"].astype(str) + \"_\" + variable[\"item_id\"].astype(str)\n", ")\n", "variable = variable[[\"key\"]]\n", "\n", "DOI_Input[\"o_key\"] = (\n", "    DOI_Input[\"facility_id\"].astype(str) + \"_\" + DOI_Input[\"item_id\"].astype(str)\n", ")\n", "\n", "DOI_Input_temp = pd.merge(\n", "    DOI_Input, variable, left_on=[\"o_key\"], right_on=[\"key\"], how=\"left\"\n", ")\n", "\n", "DOI_Input_non_dup = DOI_Input_temp[DOI_Input_temp.key.isna()]\n", "DOI_Input_dup = DOI_Input_temp[~DOI_Input_temp.key.isna()]\n", "\n", "duplicate_set = DOI_Input_dup[\n", "    [\"facility_id\", \"ordering_facility\"]\n", "].drop_duplicates()  # 517 for 428, 299?,  140?\n", "\n", "req_back = (\n", "    DOI_Input_non_dup.groupby([\"facility_id\", \"ordering_facility\"])\n", "    .agg({\"item_id\": \"count\"})\n", "    .reset_index()\n", ")\n", "# req_back[req_back.facility_id.isin([428, 299, 140])]\n", "\n", "req_back = pd.merge(\n", "    req_back, duplicate_set, on=[\"facility_id\", \"ordering_facility\"], how=\"inner\"\n", ")\n", "\n", "req_back[\"rank\"] = req_back.groupby([\"facility_id\"])[\"item_id\"].rank(\n", "    method=\"dense\", ascending=False\n", ")\n", "req_back = req_back[req_back[\"rank\"] == 1]\n", "req_back = req_back[[\"facility_id\", \"ordering_facility\"]].drop_duplicates()\n", "\n", "DOI_Input_dup = pd.merge(\n", "    DOI_Input_dup, req_back, on=[\"facility_id\", \"ordering_facility\"], how=\"inner\"\n", ")\n", "\n", "DOI_Input = pd.concat([DOI_Input_dup, DOI_Input_non_dup])\n", "DOI_Input = DOI_Input.drop(columns={\"o_key\", \"key\"})\n", "DOI_Input.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(DOI_Input, [\"facility_id\", \"item_id\"], \"DOI_Input\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_fac_ids = list(\n", "    set(\n", "        [\n", "            387,\n", "            415,\n", "            416,\n", "            375,\n", "            417,\n", "            418,\n", "            419,\n", "            420,\n", "            421,\n", "            228,\n", "            238,\n", "            246,\n", "            247,\n", "            271,\n", "            285,\n", "            319,\n", "            370,\n", "            403,\n", "            404,\n", "            135,\n", "            209,\n", "            219,\n", "            223,\n", "            227,\n", "            237,\n", "            377,\n", "            422,\n", "            423,\n", "            397,\n", "            376,\n", "            391,\n", "            392,\n", "            435,\n", "            436,\n", "            437,\n", "            388,\n", "            438,\n", "            439,\n", "            440,\n", "            464,\n", "            465,\n", "            466,\n", "            467,\n", "            468,\n", "            469,\n", "            470,\n", "            471,\n", "            472,\n", "            473,\n", "            474,\n", "            475,\n", "            458,\n", "            459,\n", "            460,\n", "            461,\n", "            462,\n", "            463,\n", "            478,\n", "            441,\n", "            479,\n", "            442,\n", "            480,\n", "            443,\n", "            444,\n", "            445,\n", "            446,\n", "            447,\n", "            161,\n", "            451,\n", "            452,\n", "            453,\n", "            454,\n", "            455,\n", "            456,\n", "            457,\n", "            424,\n", "            425,\n", "            428,\n", "            429,\n", "            430,\n", "            431,\n", "            432,\n", "            433,\n", "            434,\n", "            481,\n", "            482,\n", "            201,\n", "            197,\n", "            415,\n", "            387,\n", "            416,\n", "            375,\n", "            730,\n", "            731,\n", "            732,\n", "            733,\n", "            417,\n", "            911,\n", "            419,\n", "            734,\n", "            910,\n", "            418,\n", "            912,\n", "            420,\n", "            228,\n", "            473,\n", "            441,\n", "            468,\n", "            469,\n", "            474,\n", "            704,\n", "            1009,\n", "            1015,\n", "            1016,\n", "            442,\n", "            465,\n", "            470,\n", "            472,\n", "            471,\n", "            479,\n", "            480,\n", "            459,\n", "            466,\n", "            463,\n", "            478,\n", "            475,\n", "            462,\n", "            467,\n", "            701,\n", "            461,\n", "            796,\n", "            464,\n", "            778,\n", "            444,\n", "            779,\n", "            446,\n", "            447,\n", "            443,\n", "            786,\n", "            319,\n", "            197,\n", "            404,\n", "            285,\n", "            448,\n", "            219,\n", "            788,\n", "            237,\n", "            370,\n", "            227,\n", "            223,\n", "            449,\n", "            271,\n", "            450,\n", "            785,\n", "            238,\n", "            789,\n", "            246,\n", "            445,\n", "            247,\n", "            787,\n", "            403,\n", "            209,\n", "            725,\n", "            422,\n", "            377,\n", "            769,\n", "            770,\n", "            768,\n", "            425,\n", "            397,\n", "            720,\n", "            859,\n", "            723,\n", "            429,\n", "            724,\n", "            430,\n", "            854,\n", "            376,\n", "            856,\n", "            428,\n", "            722,\n", "            864,\n", "            434,\n", "            433,\n", "            391,\n", "            432,\n", "            867,\n", "            437,\n", "            392,\n", "            742,\n", "            542,\n", "            738,\n", "            436,\n", "            435,\n", "            599,\n", "            543,\n", "            439,\n", "            438,\n", "            388,\n", "            790,\n", "            201,\n", "            681,\n", "            456,\n", "            161,\n", "            455,\n", "            451,\n", "            457,\n", "            791,\n", "            453,\n", "            792,\n", "            452,\n", "            454,\n", "        ]\n", "    )\n", ")\n", "dark_facilities = \", \".join(map(str, list(dark_fac_ids))).replace(\", nan\", \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["threshold_doi_query = f\"\"\"select distinct facility_id, thresholddoi.outlet_id, item_id, threshold_doi as DOI_cycle\n", "from  po.outlet_item_threshold_doi as thresholddoi\n", "left join po.physical_facility_outlet_mapping as pfmap\n", "on thresholddoi.outlet_id = pfmap.outlet_id\n", "where facility_id in ({dark_facilities})\"\"\"\n", "\n", "threshold_doi = read_sql_query(\n", "    sql=threshold_doi_query, con=retail\n", ")  # ,params={'start_date':today})\n", "threshold_doi = threshold_doi[threshold_doi.outlet_id != 1405]\n", "check_level(threshold_doi, [\"facility_id\", \"item_id\"], \"threshold_doi\")\n", "threshold_doi.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_size_query = f\"\"\"SELECT item_id,\n", "       max(case_size) as case_size\n", "FROM\n", "  (SELECT item_id,\n", "          case_size,\n", "          counts,\n", "          rank() OVER (PARTITION BY item_id\n", "                       ORDER BY counts DESC) AS rank\n", "   FROM\n", "     (SELECT item_id,\n", "             outer_case_size as case_size,\n", "             count(*) AS counts\n", "      FROM lake_rpc.product_product\n", "      GROUP BY 1,\n", "               2\n", "      ORDER BY 3 DESC) A) B\n", "WHERE rank = 1\n", "group by 1\"\"\"\n", "\n", "case_size = read_sql_query(\n", "    sql=case_size_query, con=CON_REDSHIFT\n", ")  # ,params={'start_date':today})\n", "check_level(case_size, [\"item_id\"], \"case_size\")\n", "case_size.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting inputs for Inbound, Storage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Inbound Capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["monthname = str(calendar.month_name[today_trick.month])[0:3]\n", "year_name = str(today_trick.year)\n", "month = str(today_trick.month).zfill(2)\n", "\n", "sheet_id3 = \"18xyQddtRB65CduWDSIlGJ8c3Bc6f9KtyFRVJqWnkOps\"\n", "input_sheet3 = pb.from_sheets(sheet_id3, \"current_month\")\n", "A3 = input_sheet3.values.tolist()\n", "B3 = pd.DataFrame(A3)\n", "new_header_ip3 = B3.iloc[0]\n", "C3 = B3[1:]\n", "C3.columns = new_header_ip3\n", "C3.head(1)\n", "Headers_name = list(C3.columns)\n", "Headers_name[0] = \"Facility\"\n", "Headers_name[1] = \"Facility Name\"\n", "Headers_name[2] = \"Total_Storage\"\n", "C3.columns = Headers_name\n", "\n", "second_day = str((today_trick.day))\n", "Inbound_date = C3[\n", "    [\"Facility\", \"Facility Name\", \"8-\" + monthname, second_day + \"-\" + monthname]\n", "]  # Final Data: Inbound Data\n", "# check_level(Inbound_date,['Facility'],\"Inbound Capacity\") # take for month 16th day\n", "# Inbound_date.to_csv(\"Inbound.csv\")\n", "Inbound_date = Inbound_date.rename(\n", "    columns={\n", "        \"8-\" + monthname: year_name + \"-\" + month + \"-08\",\n", "        second_day + \"-\" + monthname: year_name + \"-\" + month + \"-\" + second_day,\n", "    }\n", ")\n", "Inbound_date[\"Facility\"] = np.where(\n", "    Inbound_date[\"Facility\"] == \"M 2 Bulk\",\n", "    \"Mumbai M6\",\n", "    np.where(\n", "        Inbound_date[\"Facility\"] == \"B 2 Bulk\",\n", "        \"B 2_Bulk\",\n", "        np.where(\n", "            Inbound_date[\"Facility\"] == \"Kolkata Feeder\",\n", "            \"Feeder-Kolkata\",\n", "            np.where(\n", "                Inbound_date[\"Facility\"] == \"Farukh Nagar\",\n", "                \"Farukhnagar_Bulk\",\n", "                np.where(\n", "                    Inbound_date[\"Facility\"] == \"Dhaturi Bulk\",\n", "                    \"Bulk_Dhaturi\",\n", "                    np.where(\n", "                        Inbound_date[\"Facility\"] == \"Hyderabad Bulk\",\n", "                        \"Feeder-GM Bulk Hyderabad\",\n", "                        np.where(\n", "                            Inbound_date[\"Facility\"] == \"Kundli GM\",\n", "                            \"Feeder-GM Bulk Kundli\",\n", "                            np.where(\n", "                                Inbound_date[\"Facility\"] == \"Lucknow L3\",\n", "                                \"Lucknow-L3\",\n", "                                np.where(\n", "                                    Inbound_date[\"Facility\"] == \"Lucknow 3SC\",\n", "                                    \"Bulk Lucknow 3SC\",\n", "                                    np.where(\n", "                                        Inbound_date[\"Facility\"] == \"Lucknow Feeder\",\n", "                                        \"Lucknow - SR Feeder Warehouse\",\n", "                                        np.where(\n", "                                            Inbound_date[\"Facility\"] == \"G 5\",\n", "                                            \"G 4-B\",\n", "                                            Inbound_date[\"Facility\"],\n", "                                        ),\n", "                                    ),\n", "                                ),\n", "                            ),\n", "                        ),\n", "                    ),\n", "                ),\n", "            ),\n", "        ),\n", "    ),\n", ")\n", "\n", "Inbound_date.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Total Storage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id1 = \"187pSrU3kWupZcbhpPLtT4XigR1V8n_wPP0_TG8iA3nA\"\n", "input_sheet1 = pb.from_sheets(sheet_id1, \"Summary\")\n", "A = input_sheet1.values.tolist()\n", "B = pd.Data<PERSON>rame(A)\n", "new_header = B.iloc[0]\n", "C = B[2:]\n", "C.columns = new_header\n", "Total_storage = C.iloc[:, [0, 1, 2]]\n", "Total_storage = Total_storage.rename(\n", "    columns={\n", "        \"\": \"facility\",\n", "        \"Facility Id\": \"facility_id\",\n", "        \"Total Storage Cap\": \"Total_Storage_Cap\",\n", "    }\n", ")\n", "Total_storage[\"Total_Storage_Cap\"] = (\n", "    Total_storage[\"Total_Storage_Cap\"]\n", "    .str.replace(\",\", \"\")\n", "    .replace(\"\", \"0\")\n", "    .astype(float)\n", ")\n", "Total_storage.to_csv(\"s.csv\")  # Final Data: Total Storage Capacity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# C[C['Facility Id'] == \"494\"].iloc[:,[0,1,2]]\n", "Total_storage[Total_storage.facility_id == \"494\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Unblocked_Quantity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Base Assortment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "with base AS\n", "((SELECT distinct\n", "    item_id,\n", "    city_id,\n", "    facility_id,\n", "    f.name as facility_name,\n", "    date(a.created_at) as recent_assortment,\n", "    date(a.updated_at) as assortment_date,\n", "    master_assortment_substate_id as new_substate,\n", "    case when master_assortment_substate_id in (1) then 'active' else 'inactive' end as active_flag\n", "from lake_rpc.product_facility_master_assortment a\n", "left join lake_crates.facility f on a.facility_id=f.id\n", "where f.name not ilike '%%dark%%')\n", "\n", "union\n", "\n", "(SELECT distinct \n", "s.item_id, \n", "o.tax_location_id as city_id,\n", "o.facility_id, \n", "f.name as facility_name, \n", "date(master.created_at) as recent_assortment,\n", "date(s.updated_at) as assortment_date,\n", "s.master_assortment_substate_id as new_substate,\n", "case when s.master_assortment_substate_id in (1) then 'active' else 'inactive' end as active_flag\n", "\n", "FROM metrics.dark_store_assortment_static s\n", "INNER JOIN\n", "  (SELECT ds_outlet,\n", "          max(updated_at) AS updated_at\n", "   FROM metrics.dark_store_assortment_static\n", "   GROUP BY 1)a\n", "ON a.ds_outlet=s.ds_outlet AND a.updated_at=s.updated_at\n", "\n", "LEFT JOIN lake_retail.console_outlet o on o.id = s.ds_outlet\n", "LEFT JOIN lake_crates.facility f on o.facility_id = f.id\n", "\n", "LEFT JOIN lake_rpc.product_facility_master_assortment master on s.item_id = master.item_id and s.catering_facility = master.facility_id\n", "\n", "WHERE f.name ilike '%%dark%%')),\n", "\n", "assortment_daily as \n", "(select sf.*,\n", "      case when (aa.date_of_activation is not null) then aa.date_of_activation\n", "      else sf.assortment_date+21 end as date_of_activation \n", "\n", "from base sf                           \n", "            \n", "left join (Select * from \n", "            (Select assortment_facility_id, \n", "              assortment_item_id, \n", "              date_of_activation, \n", "              assortment_date,\n", "              new_substate,\n", "              rank () over (partition by assortment_facility_id, assortment_item_id order by date_of_run desc) as rnk\n", "                from consumer.rpc_facility_assortment_activation) aaa\n", "        where rnk =1\n", "        ) aa\n", "on sf.facility_id=aa.assortment_facility_id and sf.item_id=aa.assortment_item_id and sf.new_substate=aa.new_substate),\n", "\n", "valid_assortment as\n", "(\n", "    select distinct \n", "    a.item_id,\n", "    a.recent_assortment,\n", "    a.assortment_date,\n", "    a.new_substate,\n", "    a.active_flag,\n", "    a.facility_id,\n", "    a.facility_name,\n", "    date_of_activation\n", "    from assortment_daily as a\n", "    inner join lake_rpc.product_product as b\n", "    on a.item_id=b.item_id\n", "),\n", "\n", "-- E3 and GM activation logic\n", "ds_activation as (\n", "select * from\n", "    (select item_id, o2.facility_id as sender_facility_id, o1.facility_id as receiving_facility_id, sto.created_date, sto.grn_created_at, sto.delivery_date,\n", "    rank() over (partition by item_id, o1.facility_id order by sto.created_date asc) as rnk\n", "    from metrics.esto_details sto\n", "    left join lake_retail.console_outlet o1 on sto.receiving_outlet_id = o1.id\n", "    left join lake_retail.console_outlet o2 on sto.sender_outlet_id = o2.id\n", "    where (sto_state not ilike '%%expired%%' or sto_state not ilike '%%expiry%%')\n", "    and (o1.name ilike '%%dark%%' or o1.name ilike '%% ES%%')\n", "    )\n", "where rnk = 1 \n", "),\n", "\n", "final_assortment as (select \n", "        a.item_id,\n", "        a.recent_assortment,\n", "        a.assortment_date,\n", "        a.new_substate,\n", "        a.active_flag,\n", "        a.facility_id,\n", "        a.facility_name, (case when c.grn_created_at is not null then c.grn_created_at\n", "        when c.delivery_date is not null then c.delivery_date\n", "        else (case when (facility_name ilike '%%dark%%' or facility_name ilike '%% ES%%') then current_date+interval '10 days' else a.date_of_activation end)\n", "        end) as date_of_activation\n", "from valid_assortment a\n", "left join ds_activation c\n", "on a.item_id = c.item_id and a.facility_id = c.receiving_facility_id and a.new_substate = 1)\n", "\n", "select * from final_assortment\n", "\"\"\"\n", "\n", "assortment = read_sql_query(sql=query, con=CON_REDSHIFT)\n", "check_level(assortment, [\"item_id\", \"facility_id\"], \"assortment\")\n", "assortment.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.active_flag.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get app live status"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "with item_merchant as  (\n", "    select distinct mpm.product_id,\n", "    mpm.merchant_id\n", "    from lake_cms.gr_merchant_product_mapping as mpm \n", "    where mpm.inventory_limit>0 \n", "    and mpm.enabled_flag=1\n", "    and IS_AVAILABLE = 1),\n", "\n", "merchant_mapping as\n", "(\n", "    select \n", "        frontend.id frontend_id, \n", "        frontend.name frontend_name,\n", "        backend.id backend_id,\n", "        backend.name backend_name\n", "    from lake_cms.view_gr_merchant frontend\n", "    join lake_cms.gr_virtual_to_real_merchant_mapping vmm\n", "        on frontend.id=vmm.virtual_merchant_id \n", "    join lake_cms.view_gr_merchant backend\n", "        on backend.id=vmm.real_merchant_id\n", "    where \n", "        frontend.enabled_flag =1\n", "        and vmm.enabled_flag=1\n", "\n", "    and backend.name not ilike '%%Smart Bachat Club%%'\n", "    and backend.name not ilike '%%Donation%%'\n", "    and backend.name not ilike '%%Collection%%'\n", "    and backend.name not ilike '%%Collect Return%%'\n", "    and backend.name not ilike '%%Dummy%%'\n", "    and backend.name not ilike '%%test%%'\n", "),\n", "\n", "final_data as\n", "(\n", "    select distinct  a.*\n", "    from item_merchant a \n", "    inner join merchant_mapping m\n", "    on m.backend_id=a.merchant_id\n", "),\n", "\n", "item_outlet as\n", "(\n", "    select distinct\n", "    current_date as order_date,\n", "    mpm.product_id,\n", "    pos.facility_id\n", "    from final_data mpm\n", "    inner join lake_retail.console_outlet_cms_store rt on mpm.merchant_id=rt.cms_store\n", "    inner join (select distinct facility_id,id from lake_retail.console_outlet where active=1) pos\n", "    on rt.outlet_id=pos.id\n", "    where rt.Active=1\n", "    group by 1,2,3),\n", "\n", "product_offer_mapping as\n", "(\n", "    select \n", "    item_id,\n", "    a.product_id,\n", "    facility_id,\n", "    order_date,\n", "    offer_id\n", "    from item_outlet as a\n", "    left join lake_rpc.item_product_mapping as b\n", "    on a.product_id=b.product_id\n", "    where b.active=1\n", "),\n", "\n", "item_not_null as\n", "(\n", "    select \n", "    item_id,\n", "    order_date,\n", "    facility_id\n", "    from product_offer_mapping\n", "    where item_id is not null\n", "),\n", "\n", "item_null as\n", "(\n", "    select *\n", "    from product_offer_mapping\n", "    where item_id is null\n", "),\n", "\n", "offer_details AS\n", "  (SELECT DISTINCT offer_reference_id :: varchar,\n", "                  cast(field_val AS int) AS item_id\n", "  FROM lake_offer_master.offer\n", "  LEFT JOIN lake_offer_master.offer_field o_field ON o_field.offer_id=offer.id\n", "  LEFT JOIN lake_offer_master.offer_type_field otf ON o_field.offer_type_field_id = otf.id \n", "  WHERE otf.field_name LIKE '%%product%%'),\n", "\n", "item_offer_joining as\n", "(\n", "    select \n", "    b.item_id,\n", "    product_id,\n", "    facility_id,\n", "    order_date,\n", "    offer_id\n", "    from item_null as a\n", "    left join offer_details as b\n", "    on a.offer_id=b.offer_reference_id\n", "),\n", "\n", "item_facility_final as\n", "(\n", "    select \n", "    item_id,\n", "    order_date,\n", "    facility_id\n", "    from item_offer_joining\n", "    group by 1,2,3\n", "),\n", "\n", "union_table as\n", "(SELECT * FROM\n", "     ( SELECT *\n", "      FROM item_not_null )\n", "  UNION \n", "     ( SELECT  *\n", "      FROM item_facility_final))\n", "      \n", "\n", "select *\n", "from union_table \n", "where item_id is not null\n", "\"\"\"\n", "\n", "app_live = read_sql_query(sql=query, con=CON_REDSHIFT)\n", "check_level(app_live, [\"item_id\", \"facility_id\"], \"app_live\")\n", "app_live.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_app_live = assortment.merge(\n", "    app_live, how=\"left\", on=[\"item_id\", \"facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_app_live[\"app_live\"] = np.where(~assortment_app_live.order_date.isna(), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_app_live[\"order_date\"] = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "assortment_app_live.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_app_live.active_flag.unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get inventory data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actual_inv_query = \"\"\"\n", "SELECT DISTINCT a.item_id,\n", "                a.outlet_id,\n", "                r.facility_id,\n", "                quantity AS actual_quantity,\n", "                threshold_quantity\n", "FROM ims.ims_item_inventory AS a\n", "INNER JOIN (select distinct facility_id,id \n", "        from retail.console_outlet\n", "        where active=1\n", "        and ((name like '%%SSC%%' or name like '%%MODI%%'))\n", "        and name not like '%%liquidation%%'\n", "        and name not like '%%hogwarts%%'\n", "        and name not like '%%training%%'\n", "        and name not like '%%donate%%'\n", "        and name not like '%%orange%%'\n", "        and name not like '%%not in service area%%'\n", "        or (name like '%% ES%%' and (name like '%%SSC%%' or name like '%%CC%%' or name like '%%NM%%' or name like '%%FNV%%'))\n", "        or (name like '%% dark%%' and name not like '%%infra%%')\n", ") r \n", "ON a.outlet_id = r.id\n", "GROUP BY 1,\n", "         2,\n", "         3,\n", "         4,\n", "         5\n", "\"\"\"\n", "actual_quantity = read_sql_query(sql=actual_inv_query, con=retail)\n", "check_level(actual_quantity, [\"item_id\", \"outlet_id\"], \"actual_quantity\")\n", "actual_quantity.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["block_quantity = \"\"\"\n", "select item_id ,\n", "                             outlet_id ,\n", "                             max(CASE\n", "                                 WHEN blocked_type = 1 THEN quantity\n", "                                 ELSE 0\n", "                             END) AS order_blocked,\n", "                             max(CASE\n", "                                 WHEN blocked_type = 2 THEN quantity\n", "                                 ELSE 0\n", "                             END )AS sto_blocked,\n", "                             max(CASE\n", "                                 WHEN blocked_type = 4 THEN quantity\n", "                                 ELSE 0\n", "                             END )AS pro_blocked,\n", "                             max(CASE\n", "                                 WHEN blocked_type = 5 THEN quantity\n", "                                 ELSE 0\n", "                             END )AS sso_blocked\n", "                     FROM ims.ims_item_blocked_inventory\n", "                     group by 1,2\n", "                     \"\"\"\n", "\n", "block_quantity_data = read_sql_query(sql=block_quantity, con=retail)\n", "check_level(block_quantity_data, [\"item_id\", \"outlet_id\"], \"block_quantity_data\")\n", "block_quantity_data.head(1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["block_quantity_data[(block_quantity_data.item_id == 10046643) & (block_quantity_data.outlet_id == 846)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_data = actual_quantity.merge(\n", "    block_quantity_data, on=[\"item_id\", \"outlet_id\"], how=\"left\"\n", ")\n", "\n", "inv_data = inv_data.drop_duplicates([\"item_id\", \"outlet_id\"])\n", "\n", "inv_data[\"blocked_quantity\"] = (\n", "    inv_data.order_blocked\n", "    + inv_data.sto_blocked\n", "    + inv_data.pro_blocked\n", "    + inv_data.sso_blocked\n", ")\n", "\n", "inv_data.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_facility_group = (\n", "    inv_data.groupby([\"item_id\", \"facility_id\"])[\n", "        [\"actual_quantity\", \"blocked_quantity\"]\n", "    ]\n", "    .max()\n", "    .reset_index()\n", ")\n", "\n", "\n", "inv_facility_group[\"actual_quantity\"] = np.where(\n", "    (\n", "        (inv_facility_group.actual_quantity.isna())\n", "        | (inv_facility_group.actual_quantity < 0)\n", "    ),\n", "    0,\n", "    inv_facility_group.actual_quantity,\n", ")\n", "\n", "\n", "inv_facility_group[\"blocked_quantity\"] = np.where(\n", "    (\n", "        (inv_facility_group.blocked_quantity.isna())\n", "        | (inv_facility_group.blocked_quantity < 0)\n", "    ),\n", "    0,\n", "    inv_facility_group.blocked_quantity,\n", ")\n", "\n", "\n", "inv_facility_group[\"inv_flag\"] = np.where(\n", "    (inv_facility_group.actual_quantity - inv_facility_group.blocked_quantity) > 0, 1, 0\n", ")\n", "\n", "\n", "inv_facility_group[\"availability\"] = np.where(\n", "    (inv_facility_group.actual_quantity - inv_facility_group.blocked_quantity) > 0,\n", "    \"live\",\n", "    \"not live\",\n", ")\n", "\n", "inv_facility_group.head(1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["inv_facility_group.groupby(['facility_id','availability']).agg({'actual_quantity':'sum','blocked_quantity':'sum'}).reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Join assortment and inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory = inv_facility_group.drop_duplicates([\"item_id\", \"facility_id\"])\n", "inventory.item_id = inventory.item_id.astype(int)\n", "inventory.facility_id = inventory.facility_id.astype(float)\n", "\n", "avail_naming = assortment_app_live.merge(\n", "    inventory, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "avail_naming[\"inv_flag\"] = np.where(\n", "    avail_naming.inv_flag.isna(), 0, avail_naming.inv_flag\n", ")\n", "\n", "avail_naming[\"availability\"] = np.where(\n", "    avail_naming.availability.isna(), \"not live\", avail_naming.availability\n", ")\n", "\n", "\n", "avail_naming.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Remove inactive and not live items. This helps in removing negative inventories.\n", "\n", "avail_naming[\"flag\"] = (\n", "    # (avail_naming.active_flag == \"inactive\")&\n", "    (avail_naming.availability == \"not live\")\n", "    & (avail_naming.app_live == 0)\n", ")\n", "\n", "avail_naming.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avail_naming.active_flag.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_live = avail_naming[avail_naming.flag == False]\n", "active_live.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_live.active_flag.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To check if there are any items which do not have any master assortment substate id for orderable and unorderable\n", "active_live[active_live.active_flag.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability = active_live[~active_live.active_flag.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["availability.head(1)\n", "\n", "Check = availability\n", "Check.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Master Assortment -> Add frontend_name, TEA_Status, Consumption, ASP & GMV "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1a. Splitting current and next month consumption into different columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filters = (\n", "    consumption.groupby([\"daterange\"]).agg({\"consumption\": \"sum\"}).reset_index()\n", ")\n", "date_filters = date_filters[[\"daterange\"]]\n", "\n", "consumption_levels = consumption[[\"item_id\", \"facility_id\"]].drop_duplicates()\n", "\n", "current_period_0 = date_filters.iloc[[0]]\n", "current_period = current_period_0.values[0]\n", "current_period = str(current_period).replace(\"[\", \"\").replace(\"'\", \"\").replace(\"]\", \"\")\n", "\n", "ma_current = pd.merge(\n", "    consumption,\n", "    current_period_0,\n", "    left_on=[\"daterange\"],\n", "    right_on=[\"daterange\"],\n", "    how=\"inner\",\n", ")\n", "ma_current = (\n", "    ma_current.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"consumption\": \"sum\"})\n", "    .reset_index()\n", ")\n", "ma_current.columns = [\"item_id\", \"facility_id\", \"current_period\"]\n", "\n", "next_period_0 = date_filters.iloc[[1]]\n", "next_period = next_period_0.values[0]\n", "next_period = str(next_period).replace(\"[\", \"\").replace(\"'\", \"\").replace(\"]\", \"\")\n", "\n", "ma_next = pd.merge(\n", "    consumption,\n", "    next_period_0,\n", "    left_on=[\"daterange\"],\n", "    right_on=[\"daterange\"],\n", "    how=\"inner\",\n", ")\n", "\n", "ma_next = (\n", "    ma_next.groupby([\"item_id\", \"facility_id\"])\n", "    .agg({\"consumption\": \"sum\"})\n", "    .reset_index()\n", ")\n", "ma_next.columns = [\"item_id\", \"facility_id\", \"next_period\"]\n", "\n", "monthly_split_0 = pd.merge(\n", "    consumption_levels,\n", "    ma_current,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "monthly_split = pd.merge(\n", "    monthly_split_0,\n", "    ma_next,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "monthly_split = monthly_split.fillna(0.0)\n", "monthly_split.head(2)  # Consumption"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1b. Adding frontend_name, TEA_Status, Consumption"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_tea_tag = pd.merge(\n", "    master_assortment,\n", "    backend_facility[\n", "        [\"item_id\", \"facility_id\", \"backend_facility_id\", \"ordering_name\"]\n", "    ].drop_duplicates(),\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma_front_name = pd.merge(\n", "    ma_tea_tag,\n", "    frontend_facility_mapping,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id_in_details = \"1U4hz84bPZCGl70-CgGWK3JH_Zbn66KJH4y76C_ntrPY\"\n", "additional = pb.from_sheets(sheet_id_in_details, \"detail (1)\")\n", "\n", "new_frontend_query = f\"\"\"select id as facility_id, name as frontend_name\n", "from retail.warehouse_facility\n", "where enabled = 1\"\"\"\n", "new_frontend = read_sql_query(sql=new_frontend_query, con=retail)\n", "check_level(new_frontend, [\"facility_id\"], \"new_frontend\")\n", "\n", "additional = additional[\n", "    [\"item_id\", \"frontend_facility_id\", \"backend_facility_id\"]\n", "].drop_duplicates()\n", "\n", "additional[[\"item_id\", \"frontend_facility_id\"]] = additional[\n", "    [\"item_id\", \"frontend_facility_id\"]\n", "].astype(int)\n", "additional[[\"backend_facility_id\"]] = additional[[\"backend_facility_id\"]].astype(float)\n", "\n", "additional = additional[[\"frontend_facility_id\", \"item_id\", \"backend_facility_id\"]]\n", "additional.columns = [\"facility_id\", \"item_id\", \"backend_facility_id\"]\n", "\n", "\n", "backend_mapping_name = frontend_facility_mapping.copy()\n", "backend_mapping_name.columns = [\"backend_facility_id\", \"ordering_name\"]\n", "\n", "additional_b_name = pd.merge(\n", "    additional, backend_mapping_name, on=[\"backend_facility_id\"], how=\"left\"\n", ")\n", "\n", "additional_f_name = pd.merge(\n", "    additional_b_name, new_frontend, on=[\"facility_id\"], how=\"left\"\n", ")\n", "additional_f_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_front_name = pd.concat([ma_front_name, additional_f_name])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_consumption_0 = pd.merge(\n", "    ma_front_name,\n", "    monthly_split,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma_consumption_0.ordering_name = ma_consumption_0.ordering_name.fillna(\"Direct\")\n", "ma_consumption_0.backend_facility_id = ma_consumption_0.backend_facility_id.fillna(0)\n", "ma_consumption = ma_consumption_0[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "    ]\n", "]\n", "\n", "# Remove\n", "ma_consumption = ma_consumption[ma_consumption.facility_id != 139]  # Remove Bamnoli\n", "ma_consumption = ma_consumption[ma_consumption.facility_id != 240]  # Remove Kolkata1\n", "ma_consumption = ma_consumption[ma_consumption.facility_id != 13]  # Remove G2\n", "# ma_consumption = ma_consumption[ma_consumption.facility_id != 428] ## Remove 428 due to dduplication\n", "\n", "ma_consumption = ma_consumption[\n", "    ~((ma_consumption.facility_id == 92) & (ma_consumption.backend_facility_id == 212))\n", "]\n", "# Remove G4-B | G3\n", "ma_consumption = ma_consumption[\n", "    ~((ma_consumption.facility_id == 49) & (ma_consumption.backend_facility_id == 26))\n", "]\n", "# Remove Lucknow | Bulk Dhaturi\n", "ma_consumption = ma_consumption[\n", "    ~((ma_consumption.facility_id == 30) & (ma_consumption.backend_facility_id == 269))\n", "]\n", "# Remove Lucknow - L 3 | Bulk Dhaturi\n", "ma_consumption = ma_consumption[\n", "    ~((ma_consumption.facility_id == 299) & (ma_consumption.backend_facility_id == 269))\n", "]\n", "# Remove Lucknow - L 3 | Direct\n", "ma_consumption = ma_consumption[\n", "    ~((ma_consumption.facility_id == 299) & (ma_consumption.backend_facility_id == 0))\n", "]\n", "\n", "# ma_consumption['ordering_name'] = np.where(ma_consumption['facility_id'].isin([236,264]),\"Direct\",ma_consumption['ordering_name'])\n", "# ma_consumption['backend_facility_id'] = np.where(ma_consumption['facility_id'].isin([236,264]),0.0,ma_consumption['backend_facility_id'])\n", "\n", "ma_consumption[\"frontend_name\"] = np.where(\n", "    (ma_consumption[\"frontend_name\"].isna()) & (ma_consumption[\"facility_id\"] == 494),\n", "    \"<PERSON><PERSON><PERSON><PERSON> av<PERSON>hya Bulk\",\n", "    ma_consumption[\"frontend_name\"],\n", ")\n", "\n", "ma_consumption = ma_consumption[\n", "    ~((ma_consumption.facility_id == 428) & (ma_consumption.backend_facility_id == 24))\n", "]\n", "check_level(ma_consumption, [\"item_id\", \"facility_id\"], \"ma_consumption\")\n", "ma_consumption.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_storage_agg = (\n", "    Total_storage.groupby(\"facility_id\").agg({\"Total_Storage_Cap\": \"sum\"}).reset_index()\n", ")\n", "total_storage_agg.loc[total_storage_agg[\"facility_id\"] == \"\", \"facility_id\"] = -1\n", "total_storage_agg.loc[\n", "    total_storage_agg[\"facility_id\"] == \"50 /158 /159\", \"facility_id\"\n", "] = 50\n", "total_storage_agg = total_storage_agg[total_storage_agg.facility_id != \"-\"]\n", "total_storage_agg.facility_id = total_storage_agg.facility_id.astype(\"int\")\n", "\n", "total_storage_agg = pd.merge(\n", "    total_storage_agg,\n", "    ma_consumption[[\"facility_id\", \"frontend_name\"]].drop_duplicates(),\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "total_storage_agg[[\"frontend_name\", \"Total_Storage_Cap\"]].to_csv(\n", "    outputs + \"/total_storage_agg.csv\", index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_storage_agg[total_storage_agg.facility_id == 494]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    \"monthly_split -  Consumption Input: \",\n", "    monthly_split.shape[0],\n", "    \" | master_assortment: \",\n", "    master_assortment.shape[0],\n", "    \" | ma_tea_tag: \",\n", "    ma_tea_tag.shape[0],\n", "    \" | ma_front_name: \",\n", "    ma_front_name.shape[0],\n", "    \" | ma_consumption_0: \",\n", "    ma_consumption_0.shape[0],\n", "    \" | ma_consumption: \",\n", "    ma_consumption.shape[0],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1c. Adding ASP -> Calculate GMV\n", "#### How to get ASP for Bulk?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# check_level(gmv[['item_id','facility_id','avg_sp']].drop_duplicates(),['item_id','facility_id'],\"gmv_fil\")\n", "gmv[[\"facility_id\", \"item_id\"]] = gmv[[\"facility_id\", \"item_id\"]].astype(\"int\")\n", "ma_asp_0 = pd.merge(\n", "    ma_consumption,\n", "    gmv[[\"item_id\", \"facility_id\", \"avg_sp\"]].drop_duplicates(),\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma_asp_0.avg_sp = ma_asp_0.avg_sp.fillna(0.0)\n", "ma_asp_0[\"GMV:current_period\"] = ma_asp_0[\"current_period\"] * ma_asp_0[\"avg_sp\"]\n", "ma_asp_0[\"GMV:next_period\"] = ma_asp_0[\"next_period\"] * ma_asp_0[\"avg_sp\"]\n", "ma_asp_0 = ma_asp_0.fillna(0.0)\n", "# check_level(ma_asp_0,['item_id','facility_id'],\"ma_asp_0\")\n", "print(\n", "    \" | ma_consumption: \", ma_consumption.shape[0], \" | ma_asp_0: \", ma_asp_0.shape[0]\n", ")\n", "ma_asp_0.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["multi_node_calculation = ma_asp_0[\n", "    [\"item_id\", \"facility_id\", \"backend_facility_id\", \"ordering_name\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_bulk = (\n", "    ma_asp_0.groupby([\"item_id\", \"backend_facility_id\", \"ordering_name\"])\n", "    .agg(\n", "        {\n", "            \"current_period\": \"sum\",\n", "            \"next_period\": \"sum\",\n", "            \"GMV:current_period\": \"sum\",\n", "            \"GMV:next_period\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "ma_bulk.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"frontend_name\",\n", "    \"current_period\",\n", "    \"next_period\",\n", "    \"GMV:current_period\",\n", "    \"GMV:next_period\",\n", "]\n", "ma_bulk = ma_bulk[ma_bulk.frontend_name != \"Direct\"]\n", "check_level(ma_bulk, [\"item_id\", \"facility_id\"], \"ma_bulk\")\n", "\n", "ma_bulk = pd.merge(\n", "    ma_bulk,\n", "    multi_node_calculation,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "print(\n", "    \"ma_bulk[ma_bulk.backend_facility_id.isna()] = \",\n", "    ma_bulk[ma_bulk.backend_facility_id.isna()].shape[0],\n", ")\n", "ma_bulk[\"backend_facility_id\"] = ma_bulk[\"backend_facility_id\"].fillna(0.0)\n", "ma_bulk[\"ordering_name\"] = ma_bulk[\"ordering_name\"].fillna(\"Direct\")\n", "\n", "ma_bulk = ma_bulk[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "        \"GMV:current_period\",\n", "        \"GMV:next_period\",\n", "    ]\n", "]\n", "check_level(ma_bulk, [\"item_id\", \"facility_id\"], \"ma_bulk\")\n", "ma_bulk.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_bulk_0 = (\n", "    ma_bulk.groupby([\"item_id\", \"backend_facility_id\", \"ordering_name\"])\n", "    .agg(\n", "        {\n", "            \"current_period\": \"sum\",\n", "            \"next_period\": \"sum\",\n", "            \"GMV:current_period\": \"sum\",\n", "            \"GMV:next_period\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "ma_bulk_0.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"frontend_name\",\n", "    \"current_period\",\n", "    \"next_period\",\n", "    \"GMV:current_period\",\n", "    \"GMV:next_period\",\n", "]\n", "ma_bulk_0 = ma_bulk_0[ma_bulk_0.frontend_name != \"Direct\"]\n", "# check_level(ma_bulk_0,['item_id','facility_id'],\"ma_bulk_0\")\n", "\n", "ma_bulk_0 = pd.merge(\n", "    ma_bulk_0,\n", "    multi_node_calculation,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "print(\n", "    \"ma_bulk_0[ma_bulk_0.backend_facility_id.isna()] = \",\n", "    ma_bulk_0[ma_bulk_0.backend_facility_id.isna()].shape[0],\n", ")\n", "ma_bulk_0[\"backend_facility_id\"] = ma_bulk_0[\"backend_facility_id\"].fillna(0.0)\n", "ma_bulk_0[\"ordering_name\"] = ma_bulk_0[\"ordering_name\"].fillna(\"Direct\")\n", "\n", "ma_bulk_0 = ma_bulk_0[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "        \"GMV:current_period\",\n", "        \"GMV:next_period\",\n", "    ]\n", "]\n", "# check_level(ma_bulk_0,['item_id','facility_id'],\"ma_bulk\")\n", "ma_bulk_0.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_bulk_0[[\"backend_facility_id\", \"ordering_name\"]].drop_duplicates()  # .shape[0] > 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_bulk_1 = (\n", "    ma_bulk_0.groupby([\"item_id\", \"backend_facility_id\", \"ordering_name\"])\n", "    .agg(\n", "        {\n", "            \"current_period\": \"sum\",\n", "            \"next_period\": \"sum\",\n", "            \"GMV:current_period\": \"sum\",\n", "            \"GMV:next_period\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "ma_bulk_1.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"frontend_name\",\n", "    \"current_period\",\n", "    \"next_period\",\n", "    \"GMV:current_period\",\n", "    \"GMV:next_period\",\n", "]\n", "ma_bulk_1 = ma_bulk_1[ma_bulk_1.frontend_name != \"Direct\"]\n", "# check_level(ma_bulk_1,['item_id','facility_id'],\"ma_bulk_1\")\n", "\n", "ma_bulk_1 = pd.merge(\n", "    ma_bulk_1,\n", "    multi_node_calculation,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "print(\n", "    \"ma_bulk_1[ma_bulk_1.backend_facility_id.isna()] = \",\n", "    ma_bulk_1[ma_bulk_1.backend_facility_id.isna()].shape[0],\n", ")\n", "ma_bulk_1[\"backend_facility_id\"] = ma_bulk_1[\"backend_facility_id\"].fillna(0.0)\n", "ma_bulk_1[\"ordering_name\"] = ma_bulk_1[\"ordering_name\"].fillna(\"Direct\")\n", "\n", "ma_bulk_1 = ma_bulk_1[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "        \"GMV:current_period\",\n", "        \"GMV:next_period\",\n", "    ]\n", "]\n", "# check_level(ma_bulk_1,['item_id','facility_id'],\"ma_bulk_1\")\n", "ma_bulk_1.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_bulk_1[[\"backend_facility_id\", \"ordering_name\"]].drop_duplicates()  # .shape[0] > 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_bulk_2 = (\n", "    ma_bulk_1.groupby([\"item_id\", \"backend_facility_id\", \"ordering_name\"])\n", "    .agg(\n", "        {\n", "            \"current_period\": \"sum\",\n", "            \"next_period\": \"sum\",\n", "            \"GMV:current_period\": \"sum\",\n", "            \"GMV:next_period\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "ma_bulk_2.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"frontend_name\",\n", "    \"current_period\",\n", "    \"next_period\",\n", "    \"GMV:current_period\",\n", "    \"GMV:next_period\",\n", "]\n", "ma_bulk_2 = ma_bulk_2[ma_bulk_2.frontend_name != \"Direct\"]\n", "# check_level(ma_bulk_2,['item_id','facility_id'],\"ma_bulk_2\")\n", "\n", "ma_bulk_2 = pd.merge(\n", "    ma_bulk_2,\n", "    multi_node_calculation,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "print(\n", "    \"ma_bulk_2[ma_bulk_2.backend_facility_id.isna()] = \",\n", "    ma_bulk_2[ma_bulk_2.backend_facility_id.isna()].shape[0],\n", ")\n", "ma_bulk_2[\"backend_facility_id\"] = ma_bulk_2[\"backend_facility_id\"].fillna(0.0)\n", "ma_bulk_2[\"ordering_name\"] = ma_bulk_2[\"ordering_name\"].fillna(\"Direct\")\n", "\n", "ma_bulk_2 = ma_bulk_2[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "        \"GMV:current_period\",\n", "        \"GMV:next_period\",\n", "    ]\n", "]\n", "# check_level(ma_bulk_2,['item_id','facility_id'],\"ma_bulk_2\")\n", "ma_bulk_2.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_bulk_2[[\"backend_facility_id\", \"ordering_name\"]].drop_duplicates()  # .shape[0] > 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_asp_0 = ma_asp_0.drop([\"avg_sp\"], axis=1)\n", "ma_asp = pd.concat([ma_asp_0, ma_bulk, ma_bulk_0, ma_bulk_1, ma_bulk_2])\n", "ma_asp = (\n", "    ma_asp.groupby(\n", "        [\n", "            \"item_id\",\n", "            \"facility_id\",\n", "            \"frontend_name\",\n", "            \"backend_facility_id\",\n", "            \"ordering_name\",\n", "        ]\n", "    )\n", "    .agg(\n", "        {\n", "            \"current_period\": \"sum\",\n", "            \"next_period\": \"sum\",\n", "            \"GMV:current_period\": \"sum\",\n", "            \"GMV:next_period\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "ma_asp.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"frontend_name\",\n", "    \"backend_facility_id\",\n", "    \"ordering_name\",\n", "    \"current_period\",\n", "    \"next_period\",\n", "    \"GMV_current_period\",\n", "    \"GMV_next_period\",\n", "]\n", "ma_asp.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_asp[ma_asp.facility_id == 428].sort_values(by=\"item_id\").head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_level(ma_asp, [\"item_id\", \"facility_id\"], \"ma_asp\")\n", "print(\n", "    \" | ma_consumption: \",\n", "    ma_consumption.shape[0],\n", "    \" | ma_asp_0: \",\n", "    ma_asp_0.shape[0],\n", "    \" | ma_bulk: \",\n", "    ma_bulk.shape[0],\n", "    \" | ma_bulk_0: \",\n", "    ma_bulk_0.shape[0],\n", "    \" | ma_bulk_1: \",\n", "    ma_bulk_1.shape[0],\n", "    \" | ma_bulk_2: \",\n", "    ma_bulk_2.shape[0],\n", "    \" | ma_asp: \",\n", "    ma_asp.shape[0],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1d. Validation - Aggregate Consumption & GMV"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_asp.columns = [\n", "    \"item_id\",\n", "    \"facility_id\",\n", "    \"frontend_name\",\n", "    \"backend_facility_id\",\n", "    \"ordering_name\",\n", "    \"current_period\",\n", "    \"next_period\",\n", "    \"GMV_current_period\",\n", "    \"GMV_next_period\",\n", "]\n", "# check_level(ma_asp,['item_id','facility_id'],\"ma_asp\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_asp[\"Facility_Flag\"] = np.where(\n", "    ma_asp[\"facility_id\"].isin(dark_fac_ids), \"DS\", \"NDD\"\n", ")\n", "ma_grouped = (\n", "    ma_asp.groupby([\"frontend_name\", \"ordering_name\", \"Facility_Flag\"])\n", "    .agg(\n", "        {\n", "            \"current_period\": \"sum\",\n", "            \"next_period\": \"sum\",\n", "            \"GMV_current_period\": \"sum\",\n", "            \"GMV_next_period\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "ma_grouped = ma_grouped.rename(columns={\"next_period\": next_period})\n", "ma_grouped = ma_grouped.rename(columns={\"current_period\": current_period})\n", "ma_grouped = ma_grouped.rename(columns={\"GMV_next_period\": \"GMV:\" + next_period})\n", "ma_grouped = ma_grouped.rename(columns={\"GMV_current_period\": \"GMV:\" + current_period})\n", "ma_grouped = ma_grouped.rename(columns={\"ordering_name\": \"TEA_Status\"})\n", "ma_grouped[\"Key\"] = ma_grouped[\"frontend_name\"].astype(\"str\") + ma_grouped[\n", "    \"TEA_Status\"\n", "].astype(\"str\")\n", "ma_grouped = ma_grouped[\n", "    [\n", "        \"Key\",\n", "        \"frontend_name\",\n", "        \"TEA_Status\",\n", "        current_period,\n", "        next_period,\n", "        \"GMV:\" + current_period,\n", "        \"GMV:\" + next_period,\n", "        \"Facility_Flag\",\n", "    ]\n", "]\n", "ma_asp = ma_asp.drop([\"Facility_Flag\"], axis=1)\n", "ma_grouped.head(1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["outputs"]}, {"cell_type": "raw", "metadata": {}, "source": ["ma_grouped.to_csv(outputs+'/CPD_GMV.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_asp.shape[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Adding DOI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2a. DOI on Buying Cycle"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["asp_nondark = ma_asp[~ma_asp.facility_id.isin(dark_fac_ids)]\n", "asp_dark = ma_asp[ma_asp.facility_id.isin(dark_fac_ids)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_dark_ma_doi = pd.merge(\n", "    asp_nondark,\n", "    DOI_Input[\n", "        [\"item_id\", \"facility_id\", \"ordering_facility\", \"buying_cycle\", \"DOI_cycle\"]\n", "    ].drop_duplicates(),\n", "    left_on=[\"item_id\", \"facility_id\", \"backend_facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"ordering_facility\"],\n", "    how=\"left\",\n", ")\n", "non_dark_ma_doi_02 = pd.merge(\n", "    non_dark_ma_doi,\n", "    DOI_Input[\n", "        [\"item_id\", \"facility_id\", \"ordering_facility\", \"buying_cycle\", \"DOI_cycle\"]\n", "    ].drop_duplicates(),\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "non_dark_ma_doi_02.ordering_facility_x = non_dark_ma_doi_02.ordering_facility_x.fillna(\n", "    non_dark_ma_doi_02.ordering_facility_y\n", ")\n", "non_dark_ma_doi_02.buying_cycle_x = non_dark_ma_doi_02.buying_cycle_x.fillna(\n", "    non_dark_ma_doi_02.buying_cycle_y\n", ")\n", "non_dark_ma_doi_02.DOI_cycle_x = non_dark_ma_doi_02.DOI_cycle_x.fillna(\n", "    non_dark_ma_doi_02.DOI_cycle_y\n", ")\n", "non_dark_ma_doi_02 = non_dark_ma_doi_02.rename(\n", "    columns={\n", "        \"ordering_facility_x\": \"ordering_facility\",\n", "        \"buying_cycle_x\": \"buying_cycle\",\n", "        \"DOI_cycle_x\": \"DOI_cycle\",\n", "    }\n", ")\n", "non_dark_ma_doi_03 = non_dark_ma_doi_02[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "        \"GMV_current_period\",\n", "        \"GMV_next_period\",\n", "        \"ordering_facility\",\n", "        \"buying_cycle\",\n", "        \"DOI_cycle\",\n", "    ]\n", "]\n", "non_dark_ma_doi_03.loc[\n", "    (non_dark_ma_doi_03[\"next_period\"] > 0)\n", "    & (non_dark_ma_doi_03[\"DOI_cycle\"].isna() == True),\n", "    \"DOI_cycle\",\n", "] = 19\n", "non_dark_ma_doi_03.loc[\n", "    (non_dark_ma_doi_03[\"next_period\"] <= 0)\n", "    & (non_dark_ma_doi_03[\"DOI_cycle\"].isna() == True),\n", "    \"DOI_cycle\",\n", "] = 0\n", "non_dark_ma_doi_03.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dark_ma_doi = pd.merge(\n", "    asp_dark,\n", "    threshold_doi[[\"facility_id\", \"item_id\", \"DOI_cycle\"]],\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "dark_ma_doi[\"ordering_facility\"] = dark_ma_doi[\"backend_facility_id\"]\n", "dark_ma_doi[\"buying_cycle\"] = \"dark_stores\"\n", "# dark_ma_doi['DOI_cycle'] = dark_ma_doi['DOI_cycle'].fillna(5)\n", "dark_ma_doi_03 = dark_ma_doi[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "        \"GMV_current_period\",\n", "        \"GMV_next_period\",\n", "        \"ordering_facility\",\n", "        \"buying_cycle\",\n", "        \"DOI_cycle\",\n", "    ]\n", "]\n", "dark_ma_doi_03.loc[\n", "    (dark_ma_doi_03[\"next_period\"] > 0) & (dark_ma_doi_03[\"DOI_cycle\"].isna() == True),\n", "    \"DOI_cycle\",\n", "] = 5\n", "dark_ma_doi_03.loc[\n", "    (dark_ma_doi_03[\"next_period\"] <= 0) & (dark_ma_doi_03[\"DOI_cycle\"].isna() == True),\n", "    \"DOI_cycle\",\n", "] = 0\n", "dark_ma_doi_03.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_doi_03 = pd.concat([non_dark_ma_doi_03, dark_ma_doi_03])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# check_level(ma_doi_03,['item_id','facility_id'],\"ma_doi_03\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"ma_asp: \", ma_asp.shape[0], \"ma_doi_03: \", ma_doi_03.shape[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2b. DOI on transfer facility"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_nondark = ma_doi_03[~ma_doi_03.facility_id.isin(dark_fac_ids)]\n", "ma_tea_combined_d = ma_doi_03[ma_doi_03.facility_id.isin(dark_fac_ids)]\n", "ma_tea_combined_d[\"Tr_DOI\"] = ma_tea_combined_d[\"DOI_cycle\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_tea_combined_nd = pd.merge(\n", "    doi_nondark,\n", "    bulk_facility_doi,\n", "    left_on=[\"backend_facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "ma_tea_combined_nd.loc[\n", "    (ma_tea_combined_nd[\"ordering_name\"] != \"Direct\")\n", "    & (ma_tea_combined_nd[\"DOI\"].isna() == True),\n", "    \"DOI\",\n", "] = 5\n", "ma_tea_combined_nd.loc[\n", "    (ma_tea_combined_nd[\"ordering_name\"] == \"Direct\")\n", "    & (ma_tea_combined_nd[\"DOI\"].isna() == True),\n", "    \"DOI\",\n", "] = ma_tea_combined_nd[\"DOI_cycle\"]\n", "\n", "ma_tea_combined_nd = ma_tea_combined_nd.rename(columns={\"facility_id_x\": \"facility_id\"})\n", "ma_tea_combined_nd = ma_tea_combined_nd[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"frontend_name\",\n", "        \"backend_facility_id\",\n", "        \"ordering_name\",\n", "        \"current_period\",\n", "        \"next_period\",\n", "        \"GMV_current_period\",\n", "        \"GMV_next_period\",\n", "        \"ordering_facility\",\n", "        \"buying_cycle\",\n", "        \"DOI_cycle\",\n", "        \"DOI\",\n", "    ]\n", "]\n", "ma_tea_combined_nd = ma_tea_combined_nd.rename(columns={\"DOI\": \"Tr_DOI\"})\n", "# ma_tea_combined.Tr_DOI = ma_tea_combined.Tr_DOI.fillna(0)\n", "# ma_tea_combined.DOI_cycle = ma_tea_combined.DOI_cycle.fillna(0)\n", "ma_tea_combined = pd.concat([ma_tea_combined_nd, ma_tea_combined_d])\n", "ma_tea_combined.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_tea_combined[ma_tea_combined.backend_facility_id == 483]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# check_level(ma_tea_combined,['item_id','facility_id','ordering_name'],\"ma_tea_combined\")\n", "ma_tea_combined.shape[0]"]}, {"cell_type": "raw", "metadata": {}, "source": ["ma_tea_combined.frontend_name.drop_duplicates()"]}, {"cell_type": "raw", "metadata": {}, "source": ["ma_tea_combined[ma_tea_combined.frontend_name == \"Bilaspur feeder\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2c. Add Unblocked Quantity & Case Size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Check_1 = Check[\n", "    [\"item_id\", \"facility_id\", \"active_flag\", \"actual_quantity\", \"blocked_quantity\"]\n", "].drop_duplicates()\n", "Check_1.agg({\"item_id\": \"count\"}).reset_index()\n", "Check_1.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_quantity = (Check_1[Check_1.active_flag == \"active\"])[\n", "    [\"item_id\", \"facility_id\", \"actual_quantity\", \"blocked_quantity\"]\n", "].drop_duplicates()\n", "all_quantity[\"Unblocked_Qty\"] = np.where(\n", "    all_quantity[\"blocked_quantity\"] > all_quantity[\"actual_quantity\"],\n", "    0,\n", "    all_quantity[\"actual_quantity\"] - all_quantity[\"blocked_quantity\"],\n", ")\n", "ma_qty = pd.merge(\n", "    ma_tea_combined,\n", "    all_quantity,\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "ma_qty.Unblocked_Qty = ma_qty.Unblocked_Qty.fillna(0)\n", "ma_case = pd.merge(\n", "    ma_qty, case_size, left_on=[\"item_id\"], right_on=[\"item_id\"], how=\"left\"\n", ")\n", "ma_case[\"case_size\"] = ma_case[\"case_size\"].fillna(30)\n", "ma_case.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# check_level(ma_case,['item_id','facility_id'],\"ma_case\")\n", "ma_case.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case.groupby([\"facility_id\", \"frontend_name\", \"ordering_name\"]).agg(\n", "    {\"item_id\": \"count\"}\n", ").reset_index().to_csv(\"items.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2d. Holding Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def holding_condition(x):\n", "    case_size = x[\"case_size\"]\n", "    Weighted_Num_0 = x[\"Weighted_Num_0\"]\n", "    ordering_name = x[\"ordering_name\"]\n", "    if ordering_name == \"Direct\":\n", "        return max(case_size, Weighted_Num_0)\n", "    else:\n", "        return Weighted_Num_0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[\"Weighted_Num_0\"] = (ma_case[\"next_period\"] / 30) * ma_case[\"Tr_DOI\"] * 1.3\n", "ma_case[\"Weighted_Num\"] = ma_case.apply(holding_condition, axis=1)\n", "# ma_case[\"Weighted_Num\"] = np.where(ma_case[\"current_period\"] == 0\n", "#                                ,0,ma_case[\"Weighted_Num\"])\n", "ma_case = ma_case.drop([\"Weighted_Num_0\"], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[ma_case.frontend_name == \"Bilaspur feeder\"].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2e. Weighted DOI Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[\"next_period_doiCalc\"] = ma_case[\"next_period\"] / 31\n", "ma_aggdoi = (\n", "    ma_case.groupby([\"frontend_name\", \"ordering_name\"])\n", "    .agg({\"Weighted_Num\": \"sum\", \"next_period_doiCalc\": \"sum\", \"next_period\": \"sum\"})\n", "    .reset_index()\n", ")\n", "ma_case = ma_case.drop([\"next_period_doiCalc\"], axis=1)\n", "ma_aggdoi[\"DOI\"] = round(\n", "    ma_aggdoi[\"Weighted_Num\"] / ma_aggdoi[\"next_period_doiCalc\"], 2\n", ")\n", "\n", "ma_aggdoi.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_aggdoi[ma_aggdoi.frontend_name == \"Bilaspur feeder\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_aggdoi_00 = ma_aggdoi[[\"frontend_name\", \"ordering_name\", \"DOI\"]]\n", "ma_aggdoi_00[\"Key\"] = ma_aggdoi_00[\"frontend_name\"].astype(str) + ma_aggdoi_00[\n", "    \"ordering_name\"\n", "].astype(str)\n", "ma_aggdoi_00.columns = [\"frontend_name\", \"TEA_Status\", \"DOI\", \"Key\"]\n", "ma_aggdoi_00 = ma_aggdoi_00[[\"Key\", \"DOI\"]]\n", "\n", "ma_grouped_00 = ma_grouped.drop_duplicates()\n", "ma_grouped_00.columns = [\n", "    \"Key\",\n", "    \"frontend_name\",\n", "    \"TEA_Status\",\n", "    \"current_period\",\n", "    \"next_period\",\n", "    \"GMV_current\",\n", "    \"GMV_next\",\n", "    \"Facility_Flag\",\n", "]\n", "ma_grouped_00 = ma_grouped_00[\n", "    [\"Key\", \"frontend_name\", \"TEA_Status\", \"current_period\", \"Facility_Flag\"]\n", "]\n", "ma_grouped_00.head(2)\n", "\n", "days_left = (\n", "    datetime.strptime(current_period.split(\":\")[1].strip(), \"%Y-%m-%d\").day\n", "    - datetime.strptime(current_period.split(\":\")[0].strip(), \"%Y-%m-%d\").day\n", "    + 1\n", ")\n", "ma_holding_00 = pd.merge(\n", "    ma_grouped_00, ma_aggdoi_00, left_on=[\"Key\"], right_on=[\"Key\"], how=\"left\"\n", ")\n", "ma_holding_00[\"holding_qty\"] = (\n", "    ma_holding_00[\"current_period\"] * ma_holding_00[\"DOI\"]\n", ") / days_left\n", "ma_total_holding_00 = (\n", "    ma_holding_00.groupby(\"frontend_name\")\n", "    .agg({\"holding_qty\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"holding_qty\": \"total_holding\"})\n", ")\n", "ma_holding_00 = pd.merge(\n", "    ma_holding_00,\n", "    ma_total_holding_00,\n", "    left_on=[\"frontend_name\"],\n", "    right_on=[\"frontend_name\"],\n", "    how=\"left\",\n", ")\n", "ma_holding_00 = pd.merge(\n", "    ma_holding_00,\n", "    total_storage_agg[[\"frontend_name\", \"Total_Storage_Cap\"]],\n", "    left_on=[\"frontend_name\"],\n", "    right_on=[\"frontend_name\"],\n", "    how=\"left\",\n", ")\n", "ma_holding_00[\"storage_available_qty\"] = (\n", "    (ma_holding_00[\"holding_qty\"] * ma_holding_00[\"Total_Storage_Cap\"])\n", "    / ma_holding_00[\"total_holding\"]\n", "    / 1.23\n", ")\n", "ma_holding_00[\"storage_ratio\"] = (\n", "    ma_holding_00[\"storage_available_qty\"] / ma_holding_00[\"holding_qty\"]\n", ")\n", "\n", "ma_holding_00[\"storage_ratio\"] = np.where(\n", "    ma_holding_00[\"storage_ratio\"] >= 1,\n", "    1,\n", "    np.where(\n", "        (ma_holding_00[\"storage_ratio\"] <= 0)\n", "        | (ma_holding_00[\"storage_ratio\"].isna() == True),\n", "        1,\n", "        ma_holding_00[\"storage_ratio\"],\n", "    ),\n", ")\n", "ma_holding_00 = ma_holding_00[ma_holding_00.Key != \"0.0Direct\"]\n", "storage_ratio_raw = ma_holding_00"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_holding_00 = ma_holding_00[[\"Key\", \"storage_ratio\"]]\n", "ma_holding_00.head(1)"]}, {"cell_type": "raw", "metadata": {}, "source": ["total_storage_agg.to_csv('total_storage_agg.csv')"]}, {"cell_type": "raw", "metadata": {}, "source": ["ma_holding_00.to_csv('x.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_holding_00[ma_holding_00.Key == \"Bilaspur feederDirect\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[\"Key\"] = ma_case[\"frontend_name\"].astype(str) + ma_case[\"ordering_name\"].astype(\n", "    str\n", ")\n", "ma_case = pd.merge(\n", "    ma_case, ma_holding_00, left_on=[\"Key\"], right_on=[\"Key\"], how=\"left\"\n", ")\n", "\n", "ma_case[\"Weighted_Num_01\"] = ma_case[\"Weighted_Num\"] * ma_case[\"storage_ratio\"]\n", "ma_case = ma_case.drop([\"Key\", \"Weighted_Num\", \"storage_ratio\"], axis=1)\n", "ma_case = ma_case.rename(columns={\"Weighted_Num_01\": \"Weighted_Num\"})\n", "\n", "ma_case[\"next_period_doiCalc\"] = ma_case[\"next_period\"] / 31\n", "\n", "ma_aggdoi = (\n", "    ma_case.groupby([\"frontend_name\", \"ordering_name\"])\n", "    .agg({\"Weighted_Num\": \"sum\", \"next_period_doiCalc\": \"sum\", \"next_period\": \"sum\"})\n", "    .reset_index()\n", ")\n", "ma_case = ma_case.drop([\"next_period_doiCalc\"], axis=1)\n", "ma_aggdoi[\"DOI\"] = round(\n", "    ma_aggdoi[\"Weighted_Num\"] / ma_aggdoi[\"next_period_doiCalc\"], 2\n", ")\n", "\n", "# DOI Dataset\n", "ma_aggdoi.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_aggdoi[ma_aggdoi.frontend_name == \"Bilaspur feeder\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_aggdoi.to_csv(outputs + \"/DOI.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 Calculating Shortfall"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[\"Zero\"] = 0\n", "ma_case[\"next_period_doiCalc\"] = ma_case[\"next_period\"] / 31\n", "\n", "ma_case[\"curr_shortfall_0\"] = (\n", "    ma_case[\"Weighted_Num\"] + ma_case[\"current_period\"] - ma_case[\"Unblocked_Qty\"]\n", ")\n", "ma_case[\"curr_shortfall\"] = ma_case[[\"curr_shortfall_0\", \"Zero\"]].max(axis=1)\n", "ma_case[\"next_shortfall_0\"] = (\n", "    1.1 * ma_case[\"Weighted_Num\"]\n", "    + 31 * ma_case[\"next_period_doiCalc\"]\n", "    + ma_case[\"current_period\"]\n", "    - ma_case[\"Unblocked_Qty\"]\n", "    - ma_case[\"curr_shortfall\"]\n", ")\n", "ma_case[\"next_shortfall\"] = ma_case[[\"next_shortfall_0\", \"Zero\"]].max(axis=1)\n", "ma_case = ma_case.drop(\n", "    [\"next_shortfall_0\", \"curr_shortfall_0\", \"next_period_doiCalc\"], axis=1\n", ")\n", "ma_case.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[(ma_case.backend_facility_id == 29) | (ma_case.facility_id == 29)].to_csv(\n", "    \"G_4.csv\"\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["sheet_id = \"1I4sjgXXPibxNx3HWyVMXrb2E8UfqmPh0LxKdtkpEaeg\"\n", "\n", "os = \"Bilaspur feeder\"\n", "pb.to_sheets(ma_case[ma_case.frontend_name == \"Bilaspur feeder\"], sheet_id, os)\n", "\n", "\n", "os = \"<PERSON><PERSON>jjar avvashya Bulk\"\n", "pb.to_sheets(ma_case[ma_case.facility_id == 494], sheet_id, os)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# check_level(ma_case,['item_id','facility_id'],\"ma_case\")\n", "ma_case.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shortfall = (\n", "    ma_case.groupby([\"frontend_name\", \"ordering_name\"])\n", "    .agg({\"curr_shortfall\": \"sum\", \"next_shortfall\": \"sum\"})\n", "    .reset_index()\n", ")\n", "shortfall[\"Key\"] = shortfall[\"frontend_name\"].astype(\"str\") + shortfall[\n", "    \"ordering_name\"\n", "].astype(\"str\")\n", "shortfall = shortfall[\n", "    [\"Key\", \"frontend_name\", \"ordering_name\", \"curr_shortfall\", \"next_shortfall\"]\n", "]\n", "shortfall.columns = [\n", "    \"Key\",\n", "    \"frontend_name\",\n", "    \"TEA_Status\",\n", "    \"curr_shortfall\",\n", "    \"next_shortfall\",\n", "]\n", "shortfall.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import calendar\n", "\n", "from dateutil.relativedelta import relativedelta\n", "\n", "thisDate = datetime.datetime.now()  # date.today()\n", "\n", "last_day_of_the_month = datetime.datetime(\n", "    thisDate.year, (thisDate + relativedelta(months=1)).month, 1\n", ") - datetime.<PERSON><PERSON><PERSON>(days=1)\n", "# print (last_day_of_the_month)\n", "\n", "\n", "def weekday_count(start, end):\n", "    start_date = start  # datetime.datetime.strptime(start, '%d/%m/%Y')\n", "    end_date = end  # datetime.datetime.strptime(end, '%d/%m/%Y')\n", "    week = {}\n", "    for i in range((end_date - start_date).days):\n", "        day = calendar.day_name[(start_date + datetime.timedelta(days=i + 1)).weekday()]\n", "        week[day] = week[day] + 1 if day in week else 1\n", "    return week\n", "\n", "\n", "total_sunday = weekday_count(thisDate, last_day_of_the_month)\n", "total_sunday"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_sunday[\"Friday\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_days = (last_day_of_the_month - thisDate).days + 1 - total_sunday[\"Sunday\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_shortfall = shortfall[shortfall.Key != \"0.0Direct\"][\n", "    [\"Key\", \"frontend_name\", \"TEA_Status\", \"curr_shortfall\"]\n", "]\n", "current_shortfall.loc[:, \"curr_shortfall\"] = (\n", "    current_shortfall.loc[:, \"curr_shortfall\"] / active_days\n", ")"]}, {"cell_type": "raw", "metadata": {}, "source": ["shortfall.to_csv(outputs+'/shortfall.csv',index = False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[ma_case.facility_id == 1].agg(\n", "    {\"curr_shortfall\": \"sum\", \"next_shortfall\": \"sum\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case[ma_case.frontend_name == \"Gurgaon\"].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case_redshift = ma_case.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "runid = str(uuid.uuid4()).replace(\"-\", \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "ma_case_redshift[\"created_at\"] = datetime.now() + <PERSON><PERSON>ta(hours=5.5)\n", "ma_case_redshift[\"run_id\"] = runid\n", "ma_case_redshift.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ma_case_redshift.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"unique identifier for items\"},\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"unique identifier for frontend facility\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_name\",\n", "        \"type\": \"varchar(200)\",\n", "        \"description\": \"name of the frontend facility\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"float\",\n", "        \"description\": \"unique identifier for backend facility based on TEA Tag\",\n", "    },\n", "    {\n", "        \"name\": \"ordering_name\",\n", "        \"type\": \"varchar(200)\",\n", "        \"description\": \"name of the backend facility\",\n", "    },\n", "    {\n", "        \"name\": \"current_period\",\n", "        \"type\": \"float\",\n", "        \"description\": \"consumption for current month based on days available\",\n", "    },\n", "    {\n", "        \"name\": \"next_period\",\n", "        \"type\": \"float\",\n", "        \"description\": \"consumption for next month\",\n", "    },\n", "    {\n", "        \"name\": \"GMV_current_period\",\n", "        \"type\": \"float\",\n", "        \"description\": \"GMV based on onconsumption for current month, on days available\",\n", "    },\n", "    {\n", "        \"name\": \"GMV_next_period\",\n", "        \"type\": \"float\",\n", "        \"description\": \"GMV based on onconsumption for next month\",\n", "    },\n", "    {\n", "        \"name\": \"ordering_facility\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend facility as captured in DOI data\",\n", "    },\n", "    {\n", "        \"name\": \"buying_cycle\",\n", "        \"type\": \"varchar(200)\",\n", "        \"description\": \"buying cycle of item\",\n", "    },\n", "    {\n", "        \"name\": \"DOI_cycle\",\n", "        \"type\": \"float\",\n", "        \"description\": \"DOI for item\",\n", "    },\n", "    {\n", "        \"name\": \"Tr_DO<PERSON>\",\n", "        \"type\": \"float\",\n", "        \"description\": \"transfer DOI of backend facility\",\n", "    },\n", "    {  ##\n", "        \"name\": \"actual_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"inventory available at frontend\",\n", "    },\n", "    {\n", "        \"name\": \"blocked_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"blocked inventory at frontend\",\n", "    },\n", "    {\n", "        \"name\": \"Unblocked_Qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Unblocked inventory available at frontend\",\n", "    },\n", "    {\n", "        \"name\": \"case_size\",\n", "        \"type\": \"float\",\n", "        \"description\": \"case size for item\",\n", "    },\n", "    {\n", "        \"name\": \"Weighted_Num\",\n", "        \"type\": \"float\",\n", "        \"description\": \"holding calculated\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON>\",\n", "        \"type\": \"int\",\n", "        \"description\": \"dummy column for calculations\",\n", "    },\n", "    {\n", "        \"name\": \"curr_shortfall\",\n", "        \"type\": \"float\",\n", "        \"description\": \"current month shortfall\",\n", "    },\n", "    {\n", "        \"name\": \"next_shortfall\",\n", "        \"type\": \"float\",\n", "        \"description\": \"next month shortfall\",\n", "    },\n", "    {\n", "        \"name\": \"created_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"time of execution in ist\",\n", "    },\n", "    {\n", "        \"name\": \"run_id\",\n", "        \"type\": \"varchar(100)\",\n", "        \"description\": \"unique identifier for run\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"capacity_planning_ndd_item_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"run_id\", \"created_at\"],\n", "    \"sortkey\": [\"run_id\", \"created_at\"],\n", "    \"incremental_key\": \"run_id\",\n", "    \"load_type\": \"upsert\",  # , # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Table to capture sto request ids raised by multi replenishment feeder script\",  # Description of the table being sent to redshift\n", "}\n", "if ma_case.shape[0] > 0:\n", "    start_r = time.time()\n", "    pb.to_redshift(ma_case_redshift, **kwargs)\n", "    end_r = time.time()\n", "    print(round((end_r - start_r) / 60, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Unblocked Quantity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start\n", "total_qty = (Check_1)[\n", "    [\"active_flag\", \"item_id\", \"facility_id\", \"actual_quantity\", \"blocked_quantity\"]\n", "].drop_duplicates()\n", "total_qty[\"Unblocked_Qty\"] = np.where(\n", "    total_qty[\"blocked_quantity\"] > total_qty[\"actual_quantity\"],\n", "    0,\n", "    total_qty[\"actual_quantity\"] - total_qty[\"blocked_quantity\"],\n", ")\n", "check_level(total_qty, [\"item_id\", \"facility_id\"], \"total_qty\")\n", "\n", "Check_3 = pd.merge(\n", "    ma_consumption,\n", "    total_qty,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "Check_3 = Check_3.drop([\"current_period\", \"next_period\"], axis=1)\n", "Check_3[\"ordering_name\"] = Check_3[\"ordering_name\"].fillna(\"Direct\")\n", "Check_3 = Check_3.rename(columns={\"ordering_name\": \"TEA_Status\"})\n", "Check_3.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Unblocked_Qty_01 = (\n", "    Check_3.groupby([\"frontend_name\", \"TEA_Status\", \"active_flag\"])\n", "    .agg({\"actual_quantity\": \"sum\", \"blocked_quantity\": \"sum\", \"Unblocked_Qty\": \"sum\"})\n", "    .reset_index()\n", ")\n", "Unblocked_Qty_01.head(2)  # Difference due to aggregation of negative blocked inventory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Unblocked_Qty_02 = Unblocked_Qty_01[\n", "    [\"frontend_name\", \"TEA_Status\", \"active_flag\", \"Unblocked_Qty\"]\n", "]\n", "Unblocked_Qty_02.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Unblocked_Qty_active = Unblocked_Qty_02[Unblocked_Qty_02.active_flag == \"active\"]\n", "Unblocked_Qty_active = Unblocked_Qty_active[\n", "    [\"frontend_name\", \"TEA_Status\", \"Unblocked_Qty\"]\n", "]\n", "Unblocked_Qty_active = Unblocked_Qty_active.rename(\n", "    columns={\"Unblocked_Qty\": \"Active_Unblocked_Qty\"}\n", ")\n", "Unblocked_Qty_inactive = Unblocked_Qty_02[Unblocked_Qty_02.active_flag == \"inactive\"]\n", "Unblocked_Qty_inactive = Unblocked_Qty_inactive[\n", "    [\"frontend_name\", \"TEA_Status\", \"Unblocked_Qty\"]\n", "]\n", "Unblocked_Qty_inactive = Unblocked_Qty_inactive.rename(\n", "    columns={\"Unblocked_Qty\": \"Inactive_Unblocked_Qty\"}\n", ")\n", "Unblocked_Qty_active.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Unblocked_items_list = Unblocked_Qty_02[\n", "    [\"frontend_name\", \"TEA_Status\"]\n", "].drop_duplicates()\n", "Unblocked_01 = pd.merge(\n", "    Unblocked_items_list,\n", "    Unblocked_Qty_active,\n", "    left_on=[\"frontend_name\", \"TEA_Status\"],\n", "    right_on=[\"frontend_name\", \"TEA_Status\"],\n", "    how=\"left\",\n", ")\n", "Unblocked_Qty_Final = pd.merge(\n", "    Unblocked_01,\n", "    Unblocked_Qty_inactive,\n", "    left_on=[\"frontend_name\", \"TEA_Status\"],\n", "    right_on=[\"frontend_name\", \"TEA_Status\"],\n", "    how=\"left\",\n", ")\n", "\n", "Unblocked_Qty_Final[\"Key\"] = (\n", "    Unblocked_Qty_Final[\"frontend_name\"] + Unblocked_Qty_Final[\"TEA_Status\"]\n", ")\n", "Unblocked_Qty_Final = Unblocked_Qty_Final[\n", "    [\n", "        \"Key\",\n", "        \"frontend_name\",\n", "        \"TEA_Status\",\n", "        \"Active_Unblocked_Qty\",\n", "        \"Inactive_Unblocked_Qty\",\n", "    ]\n", "]\n", "Unblocked_Qty_Final.to_csv(outputs + \"/Unblocked_Qty.csv\", index=False)\n", "Unblocked_Qty_Final.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Unblocked_Qty_Final[\n", "    (Unblocked_Qty_Final.frontend_name == \"Jaipur F18\")\n", "    | (Unblocked_Qty_Final.frontend_name == \"Jaipur J2\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Inbound_date[\n", "    (Inbound_date.Facility == \"Jaipur F18\") | (Inbound_date.Facility == \"Jaipur J2\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1I4sjgXXPibxNx3HWyVMXrb2E8UfqmPh0LxKdtkpEaeg\"\n", "os_shortfall = \"Current Month Inbound\"\n", "pb.to_sheets(current_shortfall, sheet_id, os_shortfall)\n", "\n", "os_storage_ratio_raw = \"Storage_Ratio_Calculated\"\n", "pb.to_sheets(storage_ratio_raw, sheet_id, os_storage_ratio_raw)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping_out = ma_case[[\"frontend_name\", \"facility_id\"]].drop_duplicates()\n", "os_map = \"facility_id_mapping\"\n", "pb.to_sheets(facility_mapping_out, sheet_id, os_map)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1I4sjgXXPibxNx3HWyVMXrb2E8UfqmPh0LxKdtkpEaeg\"\n", "\n", "consumption_final = ma_grouped[ma_grouped.Key != \"0.0Direct\"]\n", "os_CPD = \"Input-Consumption\"\n", "pb.to_sheets(consumption_final, sheet_id, os_CPD)\n", "\n", "ma_aggdoi[\"Key\"] = ma_aggdoi[\"frontend_name\"].astype(\"str\") + ma_aggdoi[\n", "    \"ordering_name\"\n", "].astype(\"str\")\n", "DOI_final = ma_aggdoi[ma_aggdoi.Key != \"0.0Direct\"]\n", "DOI_final = DOI_final[[\"Key\", \"frontend_name\", \"ordering_name\", \"DOI\"]]\n", "os_DOI = \"Input-DOI\"\n", "pb.to_sheets(DOI_final, sheet_id, os_DOI)\n", "\n", "Inbound_date = Inbound_date.drop([\"Facility Name\"], axis=1)\n", "os_inbound = \"Inbound-lookup\"\n", "pb.to_sheets(Inbound_date, sheet_id, os_inbound)\n", "\n", "shortfall_final = shortfall[shortfall.Key != \"0.0Direct\"]\n", "os_shortfall = \"Input-Requirement\"\n", "pb.to_sheets(shortfall_final, sheet_id, os_shortfall)\n", "\n", "Storage_lookup = total_storage_agg[[\"frontend_name\", \"Total_Storage_Cap\"]]\n", "Storage_lookup[\"frontend_name\"] = np.where(\n", "    Storage_lookup[\"frontend_name\"] == \"Jhajjar avvashya Bulk\",\n", "    \"\",\n", "    Storage_lookup[\"frontend_name\"],\n", ")\n", "\n", "os_storage = \"Storage-lookup\"\n", "pb.to_sheets(Storage_lookup, sheet_id, os_storage)\n", "\n", "\n", "os_unblocked = \"Input-UnblockedQuantity\"\n", "pb.to_sheets(Unblocked_Qty_Final, sheet_id, os_unblocked)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}