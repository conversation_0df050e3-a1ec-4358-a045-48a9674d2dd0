alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: inbound_capacity_planning
dag_type: report
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 16G
      request: 8G
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURE15HB
path: povms/capacity_planning/report/inbound_capacity_planning
paused: true
project_name: capacity_planning
schedule:
  end_date: '2023-06-06T00:00:00'
  interval: 0 8 1 1 *
  start_date: '2021-09-23T08:10:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
pool: povms_pool
