dag_name: event_based_sku
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
    - mountPath: /var/tmp/feeder-runs
      name: airflow-dags
      subPath: event-based-sku-feeder
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
nobuild: true
owner:
  email: <EMAIL>
  slack_id: U03RURE15HB
path: povms/darkstore/workflow/event_based_sku
paused: true
pool: povms_pool
project_name: darkstore
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: '*/30 * * * *'
  start_date: '2021-12-21T08:10:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: dummy
version: 1
