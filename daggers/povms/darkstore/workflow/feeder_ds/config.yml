dag_name: feeder_ds
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
    - mountPath: /var/tmp/feeder-runs
      name: airflow-dags
      subPath: feeder-runs
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
nobuild: true
owner:
  email: <EMAIL>
  slack_id: USKH64U85
path: povms/darkstore/workflow/feeder_ds
paused: true
pool: povms_pool
project_name: darkstore
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: '*/30 * * * *'
  start_date: '2021-01-11T05:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: dummy
version: 1
