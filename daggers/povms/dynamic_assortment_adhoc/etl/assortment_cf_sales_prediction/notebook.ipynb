{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from pencilbox.io.sheets import gspread_client\n", "from datetime import datetime\n", "from pytz import timezone\n", "import boto3\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install git+https://github.com/grofers/funk-svd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from funk_svd import SVD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_city_assortment(city_id):\n", "    sql = \"\"\"\n", "    select item_id, master_assortment_substate_id as state from lake_rpc.product_city_assortment_suggestion where active = true and \n", "    (master_assortment_substate_id = 1 or master_assortment_substate_id = 3) and city_id = {x}\n", "    group by 1,2\n", "    \"\"\".format(\n", "        x=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_atc_view_conv(city_id):\n", "    sql = \"\"\"\n", "        with dekha as (\n", "            select facility_id, properties__widget_id as product_id, count(properties__widget_id) as views from spectrum.mobile_impression_data x\n", "            inner join dwh.dim_merchant_outlet_facility_mapping y on x.traits__merchant_id = y.frontend_merchant_id\n", "            where (at_date_ist between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            and traits__merchant_id in (select frontend_merchant_id from dwh.dim_merchant_outlet_facility_mapping where pos_outlet_city_id = {c})\n", "                and name = 'Product Shown'\n", "                and properties__page_name in ('Search List', 'Search Page')\n", "                and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id  = 7)\n", "            group by 1,2\n", "        ),\n", "\n", "        k<PERSON><PERSON> as (\n", "            select facility_id, properties__widget_id as product_id, count(properties__widget_id) as atc from spectrum.mobile_event_data x\n", "            inner join dwh.dim_merchant_outlet_facility_mapping y on x.traits__merchant_id = y.frontend_merchant_id\n", "            where (at_date_ist between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "                and traits__merchant_id in (select frontend_merchant_id from dwh.dim_merchant_outlet_facility_mapping where pos_outlet_city_id = {c})\n", "                and name = 'Product Added'\n", "                and properties__page_name in ('Search List', 'Search Page')\n", "                and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id  = 7)\n", "            group by 1,2\n", "        )\n", "\n", "        select facility_id, item_id, sum(v) as views, sum(a) as atc, 1.00*sum(a)/sum(v) as conversion from (\n", "        select dekha.facility_id, item_id, dekha.views as v, khareeda.atc as a from dekha\n", "        inner join khareeda on dekha.facility_id = khareeda.facility_id and dekha.product_id = khareeda.product_id\n", "        inner join dwh.dim_item_product_offer_mapping x on x.product_id = dekha.product_id)\n", "        group by 1,2\n", "\n", "        \"\"\".format(\n", "        c=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_city_sos_assortment(city_id):\n", "    sql = \"\"\"\n", "    select item_id from metrics.perishable_item_city where city in (select name from lake_retail.console_location where id = {id}) and flag = 'sos'\n", "    \"\"\".format(\n", "        id=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    df[\"sos\"] = 1\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_xpress_ptype():\n", "    sql = \"\"\"\n", "    select distinct(product_type) from dwh.dim_product\n", "    where product_type in ('<PERSON><PERSON><PERSON><PERSON>', 'Sauce', 'Hair Colour', 'Henna', 'Hair Serum', 'Face Pack', 'Face Mask', 'Chocolate',\n", "    'Dark Chocolate', 'Chocolate Bar', 'Dark Chocolate Bar', 'Gift Pack', 'Chocolate', 'Chocolate Bar', 'Milk Chocolate', \n", "    'Candy', 'White Chocolate', 'Chocolate', 'Chocolate', 'Chocolate', 'Wafer Bar', 'Chocolate', 'Candy', 'Green Tea Bags', \n", "    'Chana', 'Peanuts', 'Liquid Stain Remover', 'Liquid Fabric Whitener', 'Powdered Stain Remover', 'Powdered Fabric Stiffener',\n", "    'Manual Brush', 'Mouth Spray', 'Dental Floss', 'Condom', 'Lubricant', 'Spray', 'Balm & Ointment', 'Tablets', 'Crepe Bandage',\n", "    'Oils for pain & massage', 'Band-Aid', 'Hand Cream', 'Antifungal Cream', 'Antiseptic Cream', 'Foot Cream', 'Cream', \n", "    'Adhesive Bandage', 'Antacid', 'ORS', 'Digestive Tablets', 'Drops', 'Tablets', 'Psyllium Husk', 'Capsules', 'Health Syrup',\n", "    'Multipurpose Oil', 'Hand Sanitizer', 'Chest rubs & balms', 'Inhaler', 'Cough Syrup', 'Mask', 'COVID self test kit',\n", "    'Thermometer', 'Pregnancy Test Kit', 'Antiseptic Liquid', 'Ear Buds', 'Cotton Balls', 'Toffee', 'Chips', 'Potato Chips',\n", "    'Wafers', '<PERSON>ris<PERSON>', 'Chips', '<PERSON><PERSON>en', 'Cheese Balls', 'Chips', 'Taco Shells', 'Chips', 'Dip', 'Sanitary Pads', 'Tampons',\n", "    'Women''s <PERSON><PERSON>', 'Hair Removal Cream', 'Wax Strips', 'Razor Cartridge', 'Period Panty', 'Urination Device', 'Tampons',\n", "    'Menstrual Cup', 'Cramp Relief Patch', 'Panty Liners', 'Intimate <PERSON><PERSON>', '<PERSON><PERSON>', 'Drink', 'Cocktail Mix', 'Soda', 'Tonic Water',\n", "    'Cocktail Mix', 'Coconut Water', 'Long Notebook', 'Multipurpose Paper', 'Notepad', 'Tape', 'Glue Stick', 'Adhesive', 'Cutter',\n", "    'Stapler', 'Eraser', 'Adhesive', 'Envelope', 'Modelling Clay Set', 'Glue Drops', 'Staples', 'Adhesive Kit', 'Gel Pen', 'Ball Pen',\n", "    'Na<PERSON>kins', 'Paper Cups', 'Toothpick', 'Disposable Spoon', 'Paper Plates', 'Disposable Fork', 'Bowl', 'Parchment Paper', \n", "    'Cling Wrap', 'Tissue Roll', 'Disposable Bowl', 'Food Wrapping Paper', 'Kitchen Gloves', 'Garbage Bag', 'Toilet Roll', \n", "    'Face Tissue', 'Tissue', '<PERSON><PERSON><PERSON>', 'Tissue Roll', 'Sponge Scrub', '<PERSON><PERSON><PERSON>', 'Ready to Eat', '<PERSON>i <PERSON>', 'Soup',\n", "    'Instant Pasta', 'Butter', 'Milk', 'Fresh Milk', 'Noodles', 'Noodles', 'Cold Coffee', 'Cold Coffee', 'Soft Drink', \n", "    'Beer', 'Soft Drink', 'Mosquito Repellent', 'Mosquito Coil', 'Mosquito Killer Machine', 'Mosquito Repellent Spray', \n", "    'Mosquito Bat', 'Mosquito Repellent Cream', 'Diaper', 'Baby Wipes', 'Candy', 'Lollipop', 'Tablets', 'Digestive Tablets',\n", "    'Mouth Freshener', 'Chewing Gum', 'Infant Formula', 'Follow up Formula', 'Baby Milk Powder', 'Infant Formula', 'Baby Cereal', \n", "    'Follow up Formula', 'Baby Food', 'Baby Cereal', 'Infant Formula', 'Baby Milk Powder', 'Baby Cereal', 'Infant Formula',\n", "    'Baby Milk Powder', '<PERSON> Lighter', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>lip Set', 'Tong/Pakad', 'Lighter & Knife Set', 'Knife', \n", "    'Matchbox', '<PERSON>ur<PERSON>', 'Greek Yogurt', 'Flavoured Yogurt', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Puffs',\n", "    'Snack', 'Peanuts', 'Mix', 'Green Peas', 'Cashews', 'Cocktail Nuts', 'Sunflower Seeds', 'Combo', 'Flavoured Cashew', \n", "    'Dried Mango', 'Almonds', 'Flavoured Almonds', 'Broom', 'Battery', 'LED Bulb', 'Spike Adaptor', 'Type C Cable', 'Power Bank',\n", "    'Men''s <PERSON><PERSON>', '<PERSON><PERSON>', 'Shaving Cream', 'Shaving Foam', 'Momos', 'Fries', 'Nuggets', 'Kebab', 'Nachos', 'Combo',\n", "    'Nachos & Salsa', 'Chips', 'Wrap', 'Taco Shell', 'Nutrition Bar', 'Protein Bar', 'Wafer Bar', 'Energy Bar', 'Breakfast Bar',\n", "    'Bar', 'Bread', 'Buns', '<PERSON><PERSON><PERSON>', 'Pav', '<PERSON><PERSON><PERSON>', 'Pizza Base', 'Bread Crumbs', 'Burger Bun', 'Bread', 'Brown Bread', \n", "    'Sandwich Bread', 'Multigrain Breads', 'Disinfectant Spray', 'Disinfectant', 'Ready to Eat', 'Brown Eggs', 'Speciality Eggs',\n", "    'Protein Rich Eggs', 'Pullet Eggs', 'White Eggs', 'Cage Free Eggs', 'Treat', 'Energy Drink', 'Sports Drink', 'Lip Balm', \n", "    '<PERSON><PERSON><PERSON>', 'Sticky Notes', 'Tonic Water', '<PERSON>c', 'Ale', 'Drink', 'Soda', 'Packaged Water', 'Mineral Water', 'Muffin', \n", "    'Bottle Opener', 'Nail clipper', 'Ice Cream', 'Ice Cube')\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    df[\"xpress_ptype\"] = 1\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_city_sales(city_id):\n", "    sql = \"\"\"   \n", "    with fo as (select facility_id, outlet_id from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1 and city_id = {c}\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "    ),\n", "\n", "    order_sales as (\n", "        select ancestor, outlet as outlet_id, x.item_id, x.product_id, quantity, price, max(product_name) as item_name, max(product_type) as product_type, max(l0) as l0, max(l1) as l1, max(l2) as l2, max(storage_type) as storage_type, min(weight_in_gm) as weight, \n", "           min(shelf_life) as shelf_life\n", "        from lake_ims.ims_order_actuals x\n", "        left join lake_ims.ims_order_details y on y.id = x.order_details_id and status_id in (1,2) and y.created_at between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp\n", "        left join lake_rpc.item_category_details z on x.item_id = z.item_id\n", "        left join dwh.dim_product p on x.product_id = p.product_id\n", "        left join lake_rpc.product_product pp on x.item_id = pp.item_id\n", "        where outlet in (select outlet_id from fo)\n", "        group by 1,2,3,4,5,6\n", "        ),\n", "\n", "    order_detail as (\n", "        select facility_id, item_id, item_name, product_type, l0, l1, l2, storage_type, weight, shelf_life,\n", "        sum(quantity) as quantity, sum(case when price is null then 0 else quantity*price end) as gmv\n", "        from order_sales x\n", "        inner join fo y on x.outlet_id = y.outlet_id\n", "        group by 1,2,3,4,5,6,7,8,9,10\n", "        ),\n", "\n", "    temp_non_perish as (\n", "        select facility_id, item_id, count(distinct(m_order_date)) as n_days from \n", "            (select facility_id, item_id, date(order_date) as order_dt, max(order_date)as m_order_date from consumer.rpc_daily_availability\n", "            where  (inv_flag = 1 or app_live = 1) and (order_date between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            group by 1,2,3)\n", "        group by 1,2\n", "        ),\n", "\n", "    temp_fnv as (\n", "        select facility_id, item_id, count(distinct(m_order_date)) as n_days from\n", "            (select facility_id, item_id, date(updated_at) as order_dt, max(updated_at)as m_order_date from metrics.fnv_hourly_details\n", "            where current_inv > 0 and (updated_at between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            group by 1,2,3\n", "            )\n", "            group by 1,2\n", "        ),\n", "\n", "    temp_perish as (\n", "        select facility_id, item_id, count(distinct(m_order_date)) as n_days from\n", "            (select facility_id, item_id, date(updated_at) as order_dt, max(updated_at)as m_order_date from metrics.perishable_hourly_details_v2\n", "            where is_perishable = 1 and (updated_at between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            group by 1,2,3\n", "            )\n", "            group by 1,2\n", "        ),\n", "    avail_days_count as (\n", "        select *, 'non_perish' as category from temp_non_perish \n", "        where item_id not in (select distinct item_id from temp_fnv) and item_id not in (select distinct item_id from temp_perish)\n", "        union\n", "        select *, 'perish' as category from temp_perish\n", "        union\n", "        select *, 'fnv' as category from temp_fnv\n", "        ),\n", "\n", "    sales_final as (\n", "        select x.facility_id, x.item_id, max(item_name) as item_name, max(product_type) as product_type, max(l0) as l0, max(l1) as l1, max(l2) as l2, \n", "        max(category) as category, max(storage_type) as storage_type, min(weight) as weight, sum(1.00*quantity/n_days) as quantity,\n", "        sum(gmv/n_days) as gmv, max(n_days) as available_days_count, min(shelf_life) as shelf_life\n", "        from order_detail x\n", "        inner join avail_days_count y on x.facility_id = y.facility_id and x.item_id = y.item_id\n", "        group by 1,2\n", "        )\n", "    select * from sales_final\n", "    \"\"\".format(\n", "        c=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def add_ptype_depth_classified_by_xpress(df):\n", "    data = df.groupby(\"product_type\").agg({\"quantity\": \"sum\", \"gmv\": \"sum\", \"xpress_ptype\": \"min\"})\n", "    data.sort_values(by=[\"xpress_ptype\", \"gmv\"], ascending=[True, False], inplace=True)\n", "    data[\"ptype_depth_gmv\"] = data[\"gmv\"] / data.groupby(data[\"xpress_ptype\"])[\"gmv\"].transform(\n", "        \"sum\"\n", "    )\n", "    data[\"ptype_depth_gmv\"] = data.groupby(\"xpress_ptype\")[\"ptype_depth_gmv\"].cumsum()\n", "\n", "    data.sort_values(by=[\"xpress_ptype\", \"quantity\"], ascending=[True, False], inplace=True)\n", "    data[\"ptype_depth_quant\"] = data[\"gmv\"] / data.groupby(data[\"xpress_ptype\"])[\n", "        \"quantity\"\n", "    ].transform(\"sum\")\n", "    data[\"ptype_depth_quant\"] = data.groupby(\"xpress_ptype\")[\"ptype_depth_quant\"].cumsum()\n", "    data = data.drop([\"gmv\", \"quantity\", \"xpress_ptype\"], axis=1)\n", "    df = pd.merge(df, data, on=\"product_type\")\n", "    return df\n", "\n", "\n", "def add_item_depth(data):\n", "    data.sort_values(by=[\"product_type\", \"gmv\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_gmv\"] = data[\"gmv\"] / data.groupby(data[\"product_type\"])[\"gmv\"].transform(\n", "        \"sum\"\n", "    )\n", "    data[\"item_depth_gmv\"] = data.groupby(\"product_type\")[\"item_depth_gmv\"].cumsum()\n", "\n", "    data.sort_values(by=[\"product_type\", \"quantity\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_quant\"] = data[\"quantity\"] / data.groupby(data[\"product_type\"])[\n", "        \"quantity\"\n", "    ].transform(\"sum\")\n", "    data[\"item_depth_quant\"] = data.groupby(\"product_type\")[\"item_depth_quant\"].cumsum()\n", "\n", "    data[\"ptype_avg_weight\"] = data.groupby(data[\"product_type\"])[\"weight\"].transform(\"mean\")\n", "    data[\"item_count_in_ptype\"] = data.groupby(data[\"product_type\"])[\"item_id\"].transform(\"count\")\n", "    data[\"item_count_in_l0\"] = data.groupby(data[\"l0\"])[\"item_id\"].transform(\"count\")\n", "    data[\"ptype_std\"] = data.groupby(\"product_type\")[\"quantity\"].transform(\"std\")\n", "    data[\"ptype_mean\"] = data.groupby(\"product_type\")[\"quantity\"].transform(\"mean\")\n", "\n", "\n", "def add_item_depth_classified_by_weight(data):\n", "    data[\"weight_type\"] = 3\n", "    data[\"weight_type\"][data[\"weight\"] < 1001] = 2\n", "    data[\"weight_type\"][data[\"weight\"] < 251] = 1\n", "    data.sort_values(by=[\"weight_type\", \"quantity\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_classified_by_weight\"] = data[\"quantity\"] / data.groupby(data[\"weight_type\"])[\n", "        \"quantity\"\n", "    ].transform(\"sum\")\n", "    data[\"item_depth_classified_by_weight\"] = data.groupby(\"weight_type\")[\n", "        \"item_depth_classified_by_weight\"\n", "    ].cumsum()\n", "\n", "\n", "def add_item_depth_l0(data):\n", "    data.sort_values(by=[\"l0\", \"gmv\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_gmv_l0\"] = data[\"gmv\"] / data.groupby(data[\"l0\"])[\"gmv\"].transform(\"sum\")\n", "    data[\"item_depth_gmv_l0\"] = data.groupby(\"l0\")[\"item_depth_gmv_l0\"].cumsum()\n", "\n", "    data.sort_values(by=[\"l0\", \"quantity\"], ascending=[True, False], inplace=True)\n", "    data[\"item_depth_quant_l0\"] = data[\"quantity\"] / data.groupby(data[\"l0\"])[\"quantity\"].transform(\n", "        \"sum\"\n", "    )\n", "    data[\"item_depth_quant_l0\"] = data.groupby(\"l0\")[\"item_depth_quant_l0\"].cumsum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# changed here below: add a new function: repair_shelf_life\n", "\n", "\n", "def repair_shelf_life(data):\n", "    data[\"slife_l2\"] = data.groupby(\"l2\")[\"shelf_life\"].transform(\"median\")\n", "    data[\"slife_l0\"] = data.groupby(\"l0\")[\"shelf_life\"].transform(\"median\")\n", "    data[\"slife_l2\"][data[\"slife_l2\"] > 500] = data[\"slife_l0\"]\n", "    data[\"shelf_life\"][data[\"shelf_life\"] > 500] = data[\"slife_l2\"]\n", "    del data[\"slife_l2\"], data[\"slife_l0\"]\n", "    return data\n", "\n", "\n", "def must_keep_perishable(actual_data):\n", "    data = actual_data.copy()\n", "    data[\"sales_in_slife\"] = data[\"quantity\"] * data[\"shelf_life\"]\n", "    temp_item = data[data[\"sales_in_slife\"] >= 1][\"item_id\"].value_counts()\n", "    must_keep_items = temp_item[temp_item > 0.8 * len(data[\"facility_id\"].unique())].index\n", "    data[\"keep\"] = 1\n", "    return data[data[\"item_id\"].isin(must_keep_items)][\n", "        [\"item_id\", \"product_type\", \"keep\"]\n", "    ].drop_duplicates()\n", "\n", "\n", "def once_generate(final_ass):\n", "    final_ass = add_ptype_depth_classified_by_xpress(final_ass)\n", "    add_item_depth(final_ass)\n", "    add_item_depth_classified_by_weight(final_ass)\n", "    add_item_depth_l0(final_ass)\n", "    final_ass[\"item_count_rank\"] = final_ass.groupby(\"facility_id\")[\"quantity\"].rank(\n", "        ascending=False, method=\"min\"\n", "    )\n", "    final_ass[\"item_gmv_rank\"] = final_ass.groupby(\"facility_id\")[\"quantity\"].rank(\n", "        ascending=False, method=\"min\"\n", "    )\n", "    final_ass[\"item_count_rank_ptype\"] = final_ass.groupby([\"facility_id\", \"product_type\"])[\n", "        \"quantity\"\n", "    ].rank(ascending=False, method=\"min\")\n", "    final_ass[\"item_count_rank_l0\"] = final_ass.groupby([\"facility_id\", \"l0\"])[\"quantity\"].rank(\n", "        ascending=False, method=\"min\"\n", "    )\n", "    final_ass[\"item_gmv_rank_ptype\"] = final_ass.groupby([\"facility_id\", \"product_type\"])[\n", "        \"gmv\"\n", "    ].rank(ascending=False, method=\"min\")\n", "    final_ass[\"item_weight_rank_ptype\"] = final_ass.groupby([\"facility_id\", \"product_type\"])[\n", "        \"weight\"\n", "    ].rank(ascending=True, method=\"max\")\n", "    #     final_ass[final_ass['weight'] > 9999]['gmv'] = 0\n", "    #     final_ass[final_ass['weight'] > 9999]['quantity'] = 0\n", "    final_ass[\"sales_in_slife\"] = final_ass[\"quantity\"] * final_ass[\"shelf_life\"]\n", "    return final_ass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameterized Input "]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameterized Cell\n", "\n", "city_id = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if city_id is None:\n", "    raise Exception(\"City ID can't be null\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def item_rank_by(df, group_by_field, field):\n", "#     col_name = 'item_rating_by_' + group_by_field + '_wrt_' + field\n", "#     df[col_name] = df.groupby(['facility_id', group_by_field])[field].rank(ascending=True, method='average')\n", "#     df[col_name] = 9*(df[col_name]-1)/(df.groupby(['facility_id', group_by_field])[col_name].transform('max')-1) + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Accomodated availablity factor\n", "def train_recommender_against(x, city_assortment_sales, svd_assortment):\n", "    for variable in x:\n", "        print(\"Training for\", variable)\n", "        train_assortment_combo = pd.DataFrame(columns=[\"u_id\", \"i_id\", \"rating\"])\n", "        train_assortment_combo[\"u_id\"] = city_assortment_sales[\"facility_id\"]\n", "        train_assortment_combo[\"i_id\"] = city_assortment_sales[\"item_id\"]\n", "        train_assortment_combo[\"rating\"] = city_assortment_sales[variable]\n", "        train_assortment_combo = train_assortment_combo.dropna()\n", "        train_assortment = train_assortment_combo.sample(frac=0.8, random_state=7)\n", "        val_assortment = train_assortment_combo.drop(train_assortment.index.tolist())\n", "        svd_assortment[variable] = SVD(\n", "            lr=5.0\n", "            * (0.001 + city_assortment_sales[variable].min())\n", "            / city_assortment_sales[variable].max(),\n", "            reg=0.5,\n", "            n_epochs=3000,\n", "            n_factors=10,\n", "            early_stopping=True,\n", "            shuffle=False,\n", "            min_rating=city_assortment_sales[variable].min(),\n", "            max_rating=city_assortment_sales[variable].max(),\n", "        )\n", "\n", "        svd_assortment[variable].fit(X=train_assortment, X_val=val_assortment)\n", "        print(\"Training done for\", variable)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def store_wise_sales(facility_id, city_assortment_sales):\n", "    cols = [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"quantity\",\n", "        \"gmv\",\n", "        \"views\",\n", "        \"atc\",\n", "        \"conversion\",\n", "        \"active_days\",\n", "    ]\n", "    return city_assortment_sales[city_assortment_sales[\"facility_id\"] == facility_id][cols]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Upload SVD City wise Assortment sales csv to Amazon S3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def make_file(predicted_assortment_sales_df, city_id):\n", "    format = \"%Y-%m-%d_%H-%M-%S\"\n", "    now_utc = datetime.now(timezone(\"UTC\"))\n", "    now_asia = now_utc.astimezone(timezone(\"Asia/Kolkata\"))\n", "    csv_file_path = \"SVD_Assortment_Sales_{today}.csv\".format(today=now_asia.strftime(format))\n", "\n", "    local_file_path = \"{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "    processed_file = predicted_assortment_sales_df.to_csv(\n", "        \"{filepath}\".format(filepath=local_file_path), index=False, header=True\n", "    )\n", "    file_path = \"assortment_recommendation/sales/{city_id}/{csv_file_path}\".format(\n", "        city_id=city_id, csv_file_path=local_file_path\n", "    )\n", "\n", "    secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "    bucket_name = \"retail-bulk-upload\"\n", "    aws_key = secrets.get(\"aws_key\")\n", "    aws_secret = secrets.get(\"aws_secret\")\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "    return (bucket_obj, local_file_path, file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_assortment = fetch_city_assortment(city_id)\n", "city_assortment_list = city_assortment[\"item_id\"].unique()\n", "print(len(city_assortment_list))\n", "print(\"fetched city assortment\")\n", "\n", "city_sales_df = fetch_city_sales(city_id)\n", "print(\"Fetched sales data\")\n", "\n", "city_sales_df = city_sales_df[city_sales_df[\"item_id\"].isin(city_assortment_list)]\n", "\n", "atc_view_conv = fetch_atc_view_conv(city_id)\n", "print(\"Fetched item facility conversion\")\n", "city_sales_df = city_sales_df.merge(atc_view_conv, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "# repairing shelf life bugs\n", "city_sales_df = repair_shelf_life(city_sales_df)\n", "city_sales_df = city_sales_df.fillna(0)\n", "\n", "# removed all heavy SKUs\n", "city_sales_df = city_sales_df[city_sales_df[\"storage_type\"] != 5].drop_duplicates()\n", "\n", "svd_assortment = dict()\n", "variables_to_predict = [\"quantity\", \"gmv\"]\n", "train_recommender_against(variables_to_predict, city_sales_df, svd_assortment)\n", "print(\"Model training done\")\n", "\n", "\n", "xpress_ptype_df = fetch_xpress_ptype()\n", "city_sales_df = city_sales_df.merge(xpress_ptype_df, on=[\"product_type\"], how=\"left\")\n", "city_sales_df = city_sales_df.fillna(0)\n", "facility_list = city_sales_df[\"facility_id\"].unique()\n", "sku_cat = city_sales_df[\n", "    [\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"product_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"category\",\n", "        \"storage_type\",\n", "        \"weight\",\n", "        \"xpress_ptype\",\n", "        \"shelf_life\",\n", "    ]\n", "].drop_duplicates()\n", "whole_sale_data = pd.DataFrame()\n", "for facility_id in facility_list:\n", "    facility_data_sold = city_sales_df[\n", "        city_sales_df[\"facility_id\"] == facility_id\n", "    ].drop_duplicates()\n", "    facility_data_sold[\"predicted\"] = False\n", "\n", "    facility_data_never_sold = sku_cat[\n", "        ~sku_cat[\"item_id\"].isin(facility_data_sold[\"item_id\"].unique())\n", "    ].drop_duplicates()\n", "    facility_data_never_sold[\"facility_id\"] = facility_id\n", "    facility_data_never_sold[\"predicted\"] = True\n", "    facility_data_never_sold[\"u_id\"] = facility_data_never_sold[\"facility_id\"]\n", "    facility_data_never_sold[\"i_id\"] = facility_data_never_sold[\"item_id\"]\n", "\n", "    for variable in variables_to_predict:\n", "        facility_data_never_sold[variable] = svd_assortment[variable].predict(\n", "            facility_data_never_sold\n", "        )\n", "\n", "    del facility_data_never_sold[\"u_id\"], facility_data_never_sold[\"i_id\"]\n", "    facility_data_never_sold = facility_data_never_sold.fillna(0)\n", "    facility_data = pd.concat([facility_data_sold, facility_data_never_sold], axis=0)\n", "    whole_sale_data = pd.concat([whole_sale_data, facility_data])\n", "    print(\"done for facility=\", facility_id)\n", "\n", "whole_sale_data = whole_sale_data.drop_duplicates()\n", "whole_sale_data = once_generate(whole_sale_data)\n", "whole_sale_data = whole_sale_data.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if len(whole_sale_data) > 0:\n", "    bucket_obj, local_file_path, file_path = make_file(whole_sale_data, city_id)\n", "    bucket_obj.upload_file(local_file_path, file_path)\n", "    os.remove(local_file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["whole_sale_data.head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}