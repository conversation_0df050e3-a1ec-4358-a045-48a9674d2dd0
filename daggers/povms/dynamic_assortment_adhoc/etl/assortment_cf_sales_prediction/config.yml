alert_configs:
  slack:
  - channel: tech-retail-po
dag_name: assortment_cf_sales_prediction
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 7
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 37
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 10
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 27
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 9
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 204
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 142
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 8
  tag: level1
- executor_config:
    load_type: tiny
    node_type: spot
  name: notebook
  parameters:
    city_id: 19
  tag: level1
owner:
  email: <EMAIL>
  slack_id: U03S5JZDVEX
path: povms/dynamic_assortment_adhoc/etl/assortment_cf_sales_prediction
paused: true
pool: povms_pool
project_name: dynamic_assortment_adhoc
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 5 * * 3
  start_date: '2022-04-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
concurrency: 3
