{"cells": [{"cell_type": "code", "execution_count": null, "id": "4d1c7614-a988-43ad-bcde-e2ee86a89c67", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pencilbox.io.sheets import gspread_client\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "id": "0d58fa3f-a834-498e-899e-1c404d3de7c5", "metadata": {}, "outputs": [], "source": ["import pymysql\n", "from datetime import datetime\n", "from pytz import timezone\n", "import boto3\n", "import json\n", "import os\n", "import requests"]}, {"cell_type": "markdown", "id": "c6d52011-537a-48c3-8c81-61983b39a3f9", "metadata": {}, "source": ["## facility_wise_request"]}, {"cell_type": "code", "execution_count": null, "id": "1c77f5b9-5ac3-4ad5-b916-74e3204e4240", "metadata": {}, "outputs": [], "source": ["def fetch_city_assortment(city_id):\n", "    actual_city = city_id\n", "    city_with_same_master_assortment_g = [9, 37, 10, 8, 7, 19, 204, 27]\n", "    city_with_same_master_assortment_p = [54, 66, 110, 125, 55, 167]\n", "    city_with_same_master_assortment_l = [14, 40]\n", "    if city_with_same_master_assortment_g.count(city_id) > 0:\n", "        city_id = city_with_same_master_assortment_g[0]\n", "    if city_with_same_master_assortment_p.count(city_id) > 0:\n", "        city_id = city_with_same_master_assortment_p[0]\n", "    if city_with_same_master_assortment_l.count(city_id) > 0:\n", "        city_id = city_with_same_master_assortment_l[0]\n", "\n", "    sql = \"\"\"\n", "    select city_id, item_id, master_assortment_substate_id as in_assortment from lake_rpc.product_city_assortment_suggestion where active = true and \n", "    (master_assortment_substate_id = 1 or master_assortment_substate_id = 3) and city_id = {x}\n", "    group by 1,2,3\n", "    \"\"\".format(\n", "        x=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    df[\"city_id\"] = actual_city\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "e72afeb5-1298-4ed0-85f0-56a8a3fe3cb1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9f7730ac-c715-4f3b-91fc-c2b7029e688f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ddcff313-ea4b-4d66-805c-965862404d25", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1rIzk7YsKnlivCK3s769W-VhBH2JC8AilrwlyxJX0cV8\"\n", "sheet_name = \"Facility Level Requests\"\n", "df_facility_wise_request = pb.from_sheets(sheet_id, sheet_name)\n", "df_facility_wise_request = df_facility_wise_request[[\"item_id\", \"facility_id\", \"city_id\"]]\n", "df_facility_wise_request = df_facility_wise_request.astype(int)\n", "\n", "city_master_assortment = pd.DataFrame()\n", "city_list = df_facility_wise_request[\"city_id\"].unique()\n", "for city_id in city_list:\n", "    city_master_assortment = pd.concat(\n", "        [city_master_assortment, fetch_city_assortment(city_id)], ignore_index=True\n", "    )\n", "\n", "df_facility_wise_request = df_facility_wise_request.merge(\n", "    city_master_assortment, on=[\"item_id\", \"city_id\"], how=\"inner\"\n", ")\n", "df_facility_wise_request = df_facility_wise_request[\n", "    [\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "802d1863-cf05-465b-a5e2-d9eb9e7723f8", "metadata": {}, "outputs": [], "source": ["df_facility_wise_request.head()"]}, {"cell_type": "markdown", "id": "ba2a637c-fd16-400d-b956-8c7e243d9934", "metadata": {}, "source": ["## city_wise_request"]}, {"cell_type": "code", "execution_count": null, "id": "7a21c7fb-fbec-4f9e-aa3f-17739fc3fe30", "metadata": {}, "outputs": [], "source": ["def get_city_facility_df(city_id):\n", "    sql = \"\"\"\n", "        select distinct \n", "            pfom.facility_id, pfom.city_id \n", "        from \n", "            lake_po.physical_facility_outlet_mapping pfom\n", "        inner join \n", "            lake_retail.console_outlet co\n", "        on \n", "            co.facility_id=pfom.facility_id\n", "        where \n", "            co.business_type_id = 7 and\n", "            pfom.active=1 and\n", "            pfom.ars_active=1 and\n", "            pfom.single_entity=1 and \n", "            pfom.city_id in {x}\n", "        \"\"\".format(\n", "        x=city_id\n", "    )\n", "\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "city_map = {\n", "    \"NCR\": \"(9,7,37,10,27,204,142,8,19)\",\n", "    \"Lucknow\": \"(14,40)\",\n", "    \"Jaipur\": \"(12)\",\n", "    \"Kolkata\": \"(15)\",\n", "    \"Bengaluru\": \"(1)\",\n", "    \"Hyderabad\": \"(5)\",\n", "    \"Chennai\": \"(4)\",\n", "    \"Mumbai\": \"(2)\",\n", "    \"Pune\": \"(3)\",\n", "    \"Ahmedabad\": \"(6)\",\n", "    \"Punjab\": \"(54,66,110,125,55,167)\",\n", "    \"Guwahati\": \"(165)\",\n", "    \"Nagpur\": \"(41)\",\n", "    \"Ranchi\": \"(68)\",\n", "}\n", "\n", "city_id_map = {\n", "    \"NCR\": 9,\n", "    \"Lucknow\": 14,\n", "    \"Jaipur\": 12,\n", "    \"Kolkata\": 15,\n", "    \"Bengaluru\": 1,\n", "    \"Hyderabad\": 5,\n", "    \"Chennai\": 4,\n", "    \"Mumbai\": 2,\n", "    \"Pune\": 3,\n", "    \"Ahmedabad\": 6,\n", "    \"Punjab\": 54,\n", "    \"Guwahati\": 165,\n", "    \"Nagpur\": 41,\n", "    \"Ranchi\": 68,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b660c817-dbed-4067-bde9-b56656cd6ed2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1rIzk7YsKnlivCK3s769W-VhBH2JC8AilrwlyxJX0cV8\"\n", "sheet_name = \"City Level Requests\"\n", "df_city_wise_request = pb.from_sheets(sheet_id, sheet_name)\n", "df_city_wise_request = df_city_wise_request[list(df_city_wise_request)[:10]]\n", "df_city_wise_request = df_city_wise_request[df_city_wise_request[\"Auto Approval\"] == \"1\"]\n", "city_list = df_city_wise_request[\"City Cluster Name\"].unique()\n", "df_city_wise_request = df_city_wise_request[[\"item_id\", \"City Cluster Name\"]]\n", "df_city_wise_request[\"item_id\"] = df_city_wise_request[\"item_id\"].map(\n", "    lambda x: \"0\" if (x) == \"\" else x\n", ")\n", "df_city_wise_request[\"item_id\"] = df_city_wise_request[\"item_id\"].astype(int)\n", "\n", "facility_df = pd.DataFrame()\n", "for city in city_list:\n", "    temp_df = get_city_facility_df(city_map[city])\n", "    temp_df[\"City Cluster Name\"] = city\n", "    facility_df = pd.concat([facility_df, temp_df], ignore_index=True)\n", "facility_df = facility_df.dropna()\n", "\n", "\n", "df_city_wise_request = df_city_wise_request.merge(\n", "    facility_df, on=[\"City Cluster Name\"], how=\"inner\"\n", ")\n", "df_city_wise_request = df_city_wise_request[[\"item_id\", \"facility_id\", \"city_id\"]]\n", "\n", "city_master_assortment = pd.DataFrame()\n", "city_list = df_city_wise_request[\"city_id\"].unique()\n", "for city_id in city_list:\n", "    city_master_assortment = pd.concat(\n", "        [city_master_assortment, fetch_city_assortment(city_id)], ignore_index=True\n", "    )\n", "\n", "df_city_wise_request = df_city_wise_request.merge(\n", "    city_master_assortment, on=[\"item_id\", \"city_id\"], how=\"inner\"\n", ")\n", "df_city_wise_request = df_city_wise_request[\n", "    [\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "66959af1-25a1-4e4f-a014-13ebc4baad36", "metadata": {}, "outputs": [], "source": ["len(df_city_wise_request)"]}, {"cell_type": "markdown", "id": "30557173-bdb2-44ca-94ec-0a6dad30308c", "metadata": {}, "source": ["## new_launch_request"]}, {"cell_type": "code", "execution_count": null, "id": "00b94613-e55a-4fca-85ec-88bfbabb8f71", "metadata": {}, "outputs": [], "source": ["def fetch_items_category_details(item_id):\n", "    sql = \"\"\"\n", "        with temp1 as (select item_id, product_type from dwh.dim_product x\n", "                        inner join lake_rpc.item_product_mapping y on x.product_id = y.product_id\n", "                        where is_current = 1 and active = 1 and offer_id is NULL and item_id = {iid}),\n", "\n", "        temp2 as (select item_id, l0, l1, l2 from lake_rpc.item_category_details where item_id = {iid}),\n", "\n", "        temp3 as (select item_id, name, weight_in_gm as weight, variant_mrp as mrp  from lake_rpc.product_product where item_id = {iid})\n", "\n", "        select temp2.*, product_type, name, weight, mrp from temp1 \n", "        inner join temp2 on temp1.item_id = temp2.item_id\n", "        inner join temp3 on temp1.item_id = temp3.item_id\n", "        group by 1,2,3,4,5,6,7,8\n", "        \"\"\".format(\n", "        iid=item_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def fetch_city_sales(city_id):\n", "    sql = \"\"\"   \n", "    with fo as (select facility_id, outlet_id from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1 and city_id = {c}\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "    ),\n", "\n", "    order_sales as (\n", "        select ancestor, outlet as outlet_id, x.item_id, x.product_id, quantity, price, mrp, max(product_name) as item_name, max(product_type) as product_type, max(l0) as l0, max(l1) as l1, max(l2) as l2, max(storage_type) as storage_type, min(weight_in_gm) as weight, \n", "           min(shelf_life) as shelf_life\n", "        from lake_ims.ims_order_actuals x\n", "        left join lake_ims.ims_order_details y on y.id = x.order_details_id and status_id in (1,2) and y.created_at between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp\n", "        left join lake_rpc.item_category_details z on x.item_id = z.item_id\n", "        left join dwh.dim_product p on x.product_id = p.product_id\n", "        left join lake_rpc.product_product pp on x.item_id = pp.item_id\n", "        where outlet in (select outlet_id from fo)\n", "        group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    order_detail as (\n", "        select facility_id, item_id, item_name, product_type, l0, l1, l2, storage_type, weight, shelf_life, unit_retained_margin as margin,\n", "        sum(quantity) as quantity, sum(case when price is null then 0 else quantity*price end) as gmv, median(mrp) as mrp\n", "        from order_sales x\n", "        inner join dwh.fact_sales_order_item_details m on m.order_id = x.ancestor and m.product_id = x.product_id\n", "        inner join fo y on x.outlet_id = y.outlet_id\n", "        group by 1,2,3,4,5,6,7,8,9,10,11\n", "        ),\n", "\n", "    temp_non_perish as (\n", "        select facility_id, item_id, count(distinct(m_order_date)) as n_days from \n", "            (select facility_id, item_id, date(order_date) as order_dt, max(order_date)as m_order_date from consumer.rpc_daily_availability\n", "            where  (inv_flag = 1 or app_live = 1) and (order_date between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            group by 1,2,3)\n", "        group by 1,2\n", "        ),\n", "\n", "    temp_fnv as (\n", "        select facility_id, item_id, count(distinct(m_order_date)) as n_days from\n", "            (select facility_id, item_id, date(updated_at) as order_dt, max(updated_at)as m_order_date from metrics.fnv_hourly_details\n", "            where current_inv > 0 and (updated_at between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            group by 1,2,3\n", "            )\n", "            group by 1,2\n", "        ),\n", "\n", "    temp_perish as (\n", "        select facility_id, item_id, count(distinct(m_order_date)) as n_days from\n", "            (select facility_id, item_id, date(updated_at) as order_dt, max(updated_at)as m_order_date from metrics.perishable_hourly_details_v2\n", "            where is_perishable = 1 and (updated_at between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            group by 1,2,3\n", "            )\n", "            group by 1,2\n", "        ),\n", "    avail_days_count as (\n", "        select *, 'non_perish' as category from temp_non_perish \n", "        where item_id not in (select distinct item_id from temp_fnv) and item_id not in (select distinct item_id from temp_perish)\n", "        union\n", "        select *, 'perish' as category from temp_perish\n", "        union\n", "        select *, 'fnv' as category from temp_fnv\n", "        ),\n", "\n", "    sales_final as (\n", "        select x.facility_id, x.item_id, max(item_name) as item_name, max(product_type) as product_type, max(l0) as l0, max(l1) as l1, max(l2) as l2, \n", "        max(category) as category, max(storage_type) as storage_type, min(weight) as weight, sum(1.00*quantity/n_days) as quantity, median(mrp) as mrp,\n", "        sum(gmv/n_days) as gmv, max(n_days) as available_days_count, min(shelf_life) as shelf_life, avg(margin) as margin\n", "        from order_detail x\n", "        inner join avail_days_count y on x.facility_id = y.facility_id and x.item_id = y.item_id\n", "        group by 1,2\n", "        )\n", "    select * from sales_final\n", "    \"\"\".format(\n", "        c=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def fetch_atc_view_conv(city_id):\n", "    sql = \"\"\"\n", "        with dekha as (\n", "            select facility_id, properties__widget_id as product_id, count(properties__widget_id) as views from spectrum.mobile_impression_data x\n", "            inner join dwh.dim_merchant_outlet_facility_mapping y on x.traits__merchant_id = y.frontend_merchant_id\n", "            where (at_date_ist between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "            and traits__merchant_id in (select frontend_merchant_id from dwh.dim_merchant_outlet_facility_mapping where pos_outlet_city_id = {c})\n", "                and name = 'Product Shown'\n", "                and properties__page_name in ('Search List', 'Search Page')\n", "                and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id  = 7)\n", "            group by 1,2\n", "        ),\n", "\n", "        k<PERSON><PERSON> as (\n", "            select facility_id, properties__widget_id as product_id, count(properties__widget_id) as atc from spectrum.mobile_event_data x\n", "            inner join dwh.dim_merchant_outlet_facility_mapping y on x.traits__merchant_id = y.frontend_merchant_id\n", "            where (at_date_ist between (current_date - 60 || ' 00:00:00')::timestamp and (current_date || ' 00:00:00')::timestamp)\n", "                and traits__merchant_id in (select frontend_merchant_id from dwh.dim_merchant_outlet_facility_mapping where pos_outlet_city_id = {c})\n", "                and name = 'Product Added'\n", "                and properties__page_name in ('Search List', 'Search Page')\n", "                and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id  = 7)\n", "            group by 1,2\n", "        )\n", "\n", "        select facility_id, item_id, sum(v) as views, sum(a) as atc, 1.00*sum(a)/sum(v) as conversion from (\n", "        select dekha.facility_id, item_id, dekha.views as v, khareeda.atc as a from dekha\n", "        inner join khareeda on dekha.facility_id = khareeda.facility_id and dekha.product_id = khareeda.product_id\n", "        inner join dwh.dim_item_product_offer_mapping x on x.product_id = dekha.product_id)\n", "        group by 1,2\n", "\n", "        \"\"\".format(\n", "        c=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "def min_max_scaling(series):\n", "    return (series - series.min()) / (series.max() - series.min())"]}, {"cell_type": "code", "execution_count": null, "id": "48a7ac61-d6a0-4464-b9ca-2cfc0db4def6", "metadata": {}, "outputs": [], "source": ["def generate_facility_category_df(sales_df_, item_id, is_premium):\n", "\n", "    item_cat = fetch_items_category_details(item_id)\n", "    facility_count = city_sales_df[\"facility_id\"].nunique()\n", "    flow_vec = [\"product_type\", \"l2\", \"l1\", \"l0\"]\n", "    category_level = \"product_type\"\n", "    category_name = [item_cat[\"product_type\"][0]]\n", "    for i in flow_vec:\n", "        i_count = city_sales_df[city_sales_df[i] == item_cat[i][0]][\"facility_id\"].nunique()\n", "        if i_count > 0.8 * facility_count:\n", "            category_level = i\n", "            category_name = [item_cat[i][0]]\n", "            break\n", "\n", "    sales_df = sales_df_.copy()\n", "\n", "    facility_category = sales_df[\n", "        [\"facility_id\", category_level, \"margin\", \"quantity\", \"conversion\", \"gmv\"]\n", "    ].copy()\n", "    facility_category = facility_category[facility_category[category_level].isin(category_name)]\n", "\n", "    facility_category[\"margin\"] = facility_category[\"margin\"] * facility_category[\"gmv\"]\n", "    facility_category[\"margin\"] = facility_category.groupby([\"facility_id\", category_level])[\n", "        \"margin\"\n", "    ].transform(\"sum\") / facility_category.groupby([\"facility_id\", category_level])[\n", "        \"gmv\"\n", "    ].transform(\n", "        \"sum\"\n", "    )\n", "\n", "    facility_category[\"conversion\"] = facility_category[\"conversion\"] * facility_category[\"gmv\"]\n", "    facility_category[\"conversion\"] = facility_category.groupby([\"facility_id\", category_level])[\n", "        \"conversion\"\n", "    ].transform(\"sum\") / facility_category.groupby([\"facility_id\", category_level])[\n", "        \"gmv\"\n", "    ].transform(\n", "        \"sum\"\n", "    )\n", "\n", "    facility_category[\"quantity\"] = facility_category[\"quantity\"] * facility_category[\"gmv\"]\n", "    facility_category[\"quantity\"] = facility_category.groupby([\"facility_id\", category_level])[\n", "        \"quantity\"\n", "    ].transform(\"sum\") / facility_category.groupby([\"facility_id\", category_level])[\n", "        \"gmv\"\n", "    ].transform(\n", "        \"sum\"\n", "    )\n", "\n", "    facility_category = facility_category[\n", "        [\"facility_id\", \"quantity\", \"conversion\", \"margin\"]\n", "    ].drop_duplicates()\n", "\n", "    for col in [\"quantity\", \"conversion\", \"margin\"]:\n", "        facility_category[col] = min_max_scaling(facility_category[col])\n", "\n", "    if is_premium == 0:\n", "        facility_category[\"relevance_score\"] = (\n", "            0.7 * facility_category[\"quantity\"] + 0.3 * facility_category[\"conversion\"]\n", "        )\n", "    if is_premium == 1:\n", "        facility_category[\"relevance_score\"] = (\n", "            0.5 * facility_category[\"quantity\"]\n", "            + 0.25 * facility_category[\"conversion\"]\n", "            + 0.25 * facility_category[\"margin\"]\n", "        )\n", "\n", "        #     facility_category.set_index('facility_id',inplace=True)\n", "    facility_category[\"item_id\"] = item_id\n", "\n", "    temp_bc = (\n", "        facility_category[\"relevance_score\"].max()\n", "        - 2.5 * facility_category[\"relevance_score\"].std()\n", "    )\n", "\n", "    facility_category = facility_category[facility_category[\"relevance_score\"] >= temp_bc][\n", "        [\"item_id\", \"facility_id\"]\n", "    ]\n", "\n", "    return facility_category"]}, {"cell_type": "code", "execution_count": null, "id": "160d37d8-d752-4b68-9d89-e1e099072fa8", "metadata": {"tags": []}, "outputs": [], "source": ["sheet_id = \"1rIzk7YsKnlivCK3s769W-VhBH2JC8AilrwlyxJX0cV8\"\n", "sheet_name = \"New Launch (niche target)\"\n", "df_new_launch = pb.from_sheets(sheet_id, sheet_name)\n", "df_new_launch = df_new_launch[df_new_launch[\"Auto Approval\"] == \"1\"]\n", "df_new_launch = df_new_launch[[\"item_id\", \"City Name\"]]\n", "\n", "df_map = pb.from_sheets(sheet_id, \"map_for_production\")\n", "\n", "df_new_launch = df_new_launch.merge(df_map, on=[\"City Name\"], how=\"inner\")\n", "df_new_launch = df_new_launch[[\"item_id\", \"city_id\"]].astype(int)\n", "\n", "df_new_launch_rec = pd.DataFrame()\n", "city_list = df_new_launch[\"city_id\"].unique()\n", "for city_id in city_list:\n", "    print(\"doing for city_id = \", city_id)\n", "    city_sales_df = fetch_city_sales(city_id)\n", "    atc_view_conv = fetch_atc_view_conv(city_id)\n", "    city_sales_df = city_sales_df.merge(atc_view_conv, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "    df_new_launch_city_wise = df_new_launch[df_new_launch[\"city_id\"] == city_id]\n", "    item_list = df_new_launch_city_wise[\"item_id\"].unique()\n", "    rec_facility_df = pd.DataFrame()\n", "    for item_id in item_list:\n", "        print(\"doing for item_id = \", item_id)\n", "        if item_id == 0:\n", "            continue\n", "        rec_facility_df = pd.concat(\n", "            [rec_facility_df, generate_facility_category_df(city_sales_df, item_id, 1)],\n", "            ignore_index=True,\n", "        )\n", "\n", "    df_new_launch_city_wise = df_new_launch_city_wise[[\"item_id\", \"city_id\"]].merge(\n", "        rec_facility_df, on=[\"item_id\"], how=\"inner\"\n", "    )\n", "    city_assortment = fetch_city_assortment(city_id)\n", "    df_new_launch_city_wise = df_new_launch_city_wise.merge(\n", "        city_assortment, on=[\"item_id\", \"city_id\"], how=\"inner\"\n", "    )\n", "    df_new_launch_rec = pd.concat([df_new_launch_rec, df_new_launch_city_wise], ignore_index=True)\n", "\n", "df_new_launch_rec = df_new_launch_rec[\n", "    [\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]\n", "].drop_duplicates()\n", "df_new_launch_rec.to_csv(\"df_new_launch_rec.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a97ae51-0990-427c-9d96-0f357b5b7968", "metadata": {}, "outputs": [], "source": ["len(df_new_launch_rec)"]}, {"cell_type": "code", "execution_count": null, "id": "9cb0ec7f-2c3c-4b7f-91d4-ea1e1432fd08", "metadata": {}, "outputs": [], "source": ["# df_new_launch_rec = pd.read_csv('df_new_launch_rec.csv')\n", "df_new_launch_rec.head()"]}, {"cell_type": "markdown", "id": "4a4b994a-a889-4435-ae68-8ee4d887e185", "metadata": {}, "source": ["## solving discrepancy through adhoc"]}, {"cell_type": "code", "execution_count": null, "id": "03b95d11-c3b5-4973-b825-4ff6233e3fdf", "metadata": {}, "outputs": [], "source": ["def fetch_adhoc_disc_data(city_id):\n", "    sql = \"\"\"\n", "    with fo as (select facility_id, outlet_id from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1 and city_id in {clist}\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "        and facility_id in (select facility_id from metrics.recommended_assortment)\n", "    ),\n", "\n", "\n", "    actual as (select item_id, facility_id, city_id, master_assortment_substate_id from lake_rpc.product_facility_master_assortment \n", "    where facility_id in (select facility_id from fo) and master_assortment_substate_id != 2),\n", "\n", "    should as (select item_id, city_id, master_assortment_substate_id from lake_rpc.product_city_assortment_suggestion where city_id in {clist}),\n", "\n", "    df as (\n", "    select actual.item_id, facility_id, actual.city_id, should.master_assortment_substate_id as in_assortment from actual\n", "    inner join should on actual.item_id = should.item_id and actual.city_id = should.city_id and actual.master_assortment_substate_id != should.master_assortment_substate_id\n", "    and actual.item_id not in (select distinct item_id from metrics.fnv_hourly_details)\n", "    ),\n", "\n", "    op_req as (select item_id, facility_id from lake_rpc.item_facility_state_request where status = 'OPEN')\n", "\n", "    select * from df except (select df.* from df inner join op_req on op_req.item_id = df.item_id and op_req.facility_id = df.facility_id)\n", "\n", "    \"\"\".format(\n", "        clist=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "df_disc = fetch_adhoc_disc_data(\"(7,37,10,27,9,204,142,8,19,1)\")"]}, {"cell_type": "code", "execution_count": null, "id": "3ca7eccf-02cb-4245-a80a-b9ccc5a996af", "metadata": {}, "outputs": [], "source": ["adhoc_combo = pd.concat([df_facility_wise_request, df_city_wise_request], ignore_index=True)\n", "adhoc_combo = pd.concat([adhoc_combo, df_new_launch_rec], ignore_index=True)\n", "adhoc_combo = pd.concat([adhoc_combo, df_disc], ignore_index=True)\n", "adhoc_combo = adhoc_combo.drop_duplicates()\n", "adhoc_combo.to_csv(\"adhoc_combo.csv\")\n", "sheet_id = \"1vyREM94SBuiGp0BE6Ssr9JlsT-FyN87bV1KSQ3I7sfY\"\n", "sheet_name = \"Adhoc_Assortment\"\n", "pb.to_sheets(adhoc_combo, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "4bef809f-986c-43a7-8ff6-c625959cf594", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1vyREM94SBuiGp0BE6Ssr9JlsT-FyN87bV1KSQ3I7sfY\"\n", "pb.to_sheets(adhoc_combo, sheet_id, \"actual\")"]}, {"cell_type": "code", "execution_count": null, "id": "67538c7c-a189-45bb-aeba-fd795bb54c45", "metadata": {}, "outputs": [], "source": ["len(adhoc_combo)"]}, {"cell_type": "code", "execution_count": null, "id": "0dcbce4a-a9ed-4050-9d05-4bc0d143f4bd", "metadata": {}, "outputs": [], "source": ["def fetch_present_status(city_id):\n", "    sql = \"\"\"\n", "    with fo as (select facility_id, outlet_id from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1 and city_id in {clist}\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "    )\n", "    select item_id, facility_id, city_id, master_assortment_substate_id as in_assortment from lake_rpc.product_facility_master_assortment where facility_id in (select facility_id from fo)\n", "    \"\"\".format(\n", "        clist=city_id\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    df = pd.read_sql_query(sql=sql, con=con)\n", "    return df\n", "\n", "\n", "present_status = fetch_present_status(\"(7,37,10,27,9,204,142,8,19,1)\")"]}, {"cell_type": "code", "execution_count": null, "id": "e3ae63bf-9040-42b1-a4b2-dc4266ed0c7f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6b88790e-0e15-4f4a-83c2-7171ba4df584", "metadata": {}, "outputs": [], "source": ["adhoc_combo = adhoc_combo.merge(present_status, on=list(present_status), how=\"left\", indicator=True)\n", "adhoc_combo = adhoc_combo[adhoc_combo[\"_merge\"] == \"left_only\"]\n", "adhoc_combo = adhoc_combo[[\"item_id\", \"facility_id\", \"city_id\", \"in_assortment\"]].astype(int)\n", "adhoc_combo = adhoc_combo[adhoc_combo[\"facility_id\"] != 1028]"]}, {"cell_type": "code", "execution_count": null, "id": "c6d74cc2-c232-4be6-98d2-35d15e071317", "metadata": {}, "outputs": [], "source": ["len(adhoc_combo)"]}, {"cell_type": "code", "execution_count": null, "id": "316de2bf-e91a-4ed7-92eb-23879ec4ebd2", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1vyREM94SBuiGp0BE6Ssr9JlsT-FyN87bV1KSQ3I7sfY\"\n", "sheet_name = \"delta\"\n", "pb.to_sheets(adhoc_combo, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "862cb2a3-21c2-4f04-9b3e-2324d5a2129c", "metadata": {}, "outputs": [], "source": ["citi_list = [7, 37, 10, 27, 9, 204, 142, 8, 19, 1]\n", "df_adhoc = adhoc_combo[adhoc_combo[\"city_id\"].isin(citi_list)].drop_duplicates()\n", "df_adhoc = df_adhoc[df_adhoc[\"facility_id\"] != 1028]\n", "len(df_adhoc)"]}, {"cell_type": "code", "execution_count": null, "id": "3396e6c8-026a-4da9-a48f-211e7f3907c0", "metadata": {}, "outputs": [], "source": ["df_adhoc.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a8e48b58-2f2d-4e69-92c9-d2edff5b4a0e", "metadata": {}, "outputs": [], "source": ["def upload_to_redash_table(data_temp):\n", "    data = data_temp.copy()\n", "    from datetime import datetime, timedelta\n", "\n", "    data.rename(columns={\"in_assortment\": \"state\"}, inplace=True)\n", "    df_adhoc[\"reason\"] = 6\n", "    data[\"updated_at\"] = pd.to_datetime(datetime.now() + timedelta(hours=5.5))\n", "    data = data[[\"item_id\", \"facility_id\", \"city_id\", \"state\", \"reason\", \"updated_at\"]]\n", "    kwargs_log = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"recommended_assortment_log\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "            {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "            {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"city_id\"},\n", "            {\n", "                \"name\": \"state\",\n", "                \"type\": \"integer\",\n", "                \"description\": \"master_assortment_substate\",\n", "            },\n", "            {\"name\": \"reason\", \"type\": \"integer\", \"description\": \"reason_type_id\"},\n", "            {\n", "                \"name\": \"updated_at\",\n", "                \"type\": \"timestamp\",\n", "                \"description\": \"Updated timestamp\",\n", "            },\n", "        ],\n", "        \"primary_key\": [\"item_id\", \"facility_id\", \"updated_at\"],\n", "        \"sortkey\": [\"updated_at\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Stores list of recommended assortment\",\n", "    }\n", "\n", "    pb.to_redshift(data, **kwargs_log)\n", "\n", "    data2 = data[[\"item_id\", \"facility_id\", \"city_id\", \"state\", \"reason\"]]\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"dynamic_assortment_ideal_result\",\n", "        \"column_dtypes\": [\n", "            {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item id\"},\n", "            {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility id\"},\n", "            {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"Facility id\"},\n", "            {\"name\": \"state\", \"type\": \"integer\", \"description\": \"Assortment state\"},\n", "            {\"name\": \"reason\", \"type\": \"integer\", \"description\": \"Reason\"},\n", "        ],\n", "        \"primary_key\": [\"item_id\", \"facility_id\"],\n", "        \"sortkey\": [\"item_id\", \"facility_id\"],\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert,\n", "        \"table_description\": \"Dynamic assortment expectations\",\n", "        \"force_upsert_without_increment_check\": True,\n", "    }\n", "\n", "    pb.to_redshift(data2, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "aac02a83-d8b8-4b51-90c3-ca8889fcf07d", "metadata": {"tags": []}, "outputs": [], "source": ["upload_to_redash_table(df_adhoc)"]}, {"cell_type": "code", "execution_count": null, "id": "6f8038ff-6a9a-4496-a2c2-8b74aea7652a", "metadata": {}, "outputs": [], "source": ["final_df = df_adhoc[[\"item_id\", \"facility_id\", \"state\"]]\n", "final_df.rename(columns={\"state\": \"substate_id\"}, inplace=True)\n", "final_df = final_df.rename(\n", "    columns={\n", "        \"item_id\": \"Item ID\",\n", "        \"facility_id\": \"Facility ID\",\n", "        \"substate_id\": \"Sub-state ID\",\n", "    }\n", ")\n", "final_df[\"City ID\"] = \"\"\n", "final_df[\"Reason ID\"] = 6  # Only for Ad hoc assortment request\n", "final_df[\"DS_FS_Flag\"] = 0"]}, {"cell_type": "code", "execution_count": null, "id": "b54cd6c6-df98-4403-821e-d4280e4d7763", "metadata": {}, "outputs": [], "source": ["final_df[\"Facility ID\"] = final_df[\"Facility ID\"].astype(int)\n", "final_df[\"Sub-state ID\"] = final_df[\"Sub-state ID\"].astype(int)\n", "final_df[\"Item ID\"] = final_df[\"Item ID\"].astype(int)\n", "final_df[\"Reason ID\"] = final_df[\"Reason ID\"].astype(int)\n", "final_df[\"DS_FS_Flag\"] = final_df[\"DS_FS_Flag\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9337ef3a-362a-4ec2-bc59-2fd189a48e77", "metadata": {}, "outputs": [], "source": ["bucket_name = \"retail-bulk-upload\"\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)"]}, {"cell_type": "code", "execution_count": null, "id": "c454d472-f5fc-4e06-91f3-ca2c65ad468b", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "    payload = {\n", "        \"file\": file_path,\n", "        \"created_by\": \"<PERSON><PERSON>\",\n", "        \"user_id\": 14,\n", "        \"is_auto_po\": True,\n", "        \"upload_type_id\": upload_type,\n", "        \"content_type\": \"text/csv\",\n", "    }\n", "    response = requests.post(url, json=payload)\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "id": "0808fa76-13cc-404c-b072-7d6d6062dbf1", "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "secrets_for_noto = pb.get_secret(\"retail/noto/\")\n", "to_email = json.loads(secrets_for_noto.get(\"adhoc_assortment_request_creation\"))"]}, {"cell_type": "code", "execution_count": null, "id": "bde6de7c-bc4b-47a6-88e9-6cef3c59207b", "metadata": {}, "outputs": [], "source": ["final_df_facility = final_df[\n", "    [\"Item ID\", \"City ID\", \"Facility ID\", \"Sub-state ID\", \"Reason ID\", \"DS_FS_Flag\"]\n", "]\n", "format = \"%Y-%m-%d_%H-%M-%S\"\n", "now_utc = datetime.now(timezone(\"UTC\"))\n", "now_asia = now_utc.astimezone(timezone(\"Asia/Kolkata\"))\n", "\n", "csv_file_path = \"Master Assortment v3 Bot_{today}_adhoc.csv\".format(today=now_asia.strftime(format))\n", "local_file_path = \"{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "processed_file = final_df_facility.to_csv(\n", "    \"{filepath}\".format(filepath=local_file_path), index=False, header=True\n", ")\n", "file_path = \"assortment/{csv_file_path}\".format(csv_file_path=local_file_path)\n", "\n", "master_assortment_v3_bulk_upload_type_id = 61\n", "\n", "if len(final_df_facility) > 0:\n", "    bucket_obj.upload_file(local_file_path, file_path)\n", "    response = upload_to_bu(file_path, master_assortment_v3_bulk_upload_type_id)\n", "    status = \"Success\" if response.status_code == 201 else \"Failed\"\n", "\n", "    subject = \"Master Assortment Adhoc Update Result {today} adhoc -\".format(\n", "        today=now_asia.strftime(format)\n", "    )\n", "    html_content = (\n", "        \"<p>Hi,</p><br/>\"\n", "        \"<p>PFA request file uploaded on workdesk for Adhoc assortment.</p><br/>\"\n", "        \"<p>Please check the file upload status <a href='https://retail.grofers.com/#/bulk-upload-status'> here:</a> Upload Type: master_assortment_v3\"\n", "        \"<p>File Upload Status: {status}</p>\".format(status=status)\n", "    )\n", "    \"<br/><p>Thanks & Regards</p>\" \"<br/><p>Retail Tech PO</p>\" \"<p>PO-VMS</p>\" \"<p>Grofers India Pvt. Ltd.</p>\"\n", "\n", "    files = [local_file_path]\n", "    pb.send_email(\n", "        from_email=from_email,\n", "        to_email=to_email,\n", "        subject=subject,\n", "        html_content=html_content,\n", "        files=files,\n", "    )\n", "    os.remove(local_file_path)\n", "else:\n", "    print(\"Assortment update not found\")\n", "\n", "pb.send_slack_message(\n", "    channel=\"adhoc_job_notify\",\n", "    text=str(datetime.now()) + \" job_id = \" + str({\"id\": 701181}),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c289dd5e-943a-4b31-92f9-bb7a97157756", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a47ff660-e3ce-4ac6-8d29-ad9d373c8cd0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3165b916-c319-4463-957f-b2eb9a1d398e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ef640ea5-a990-4262-a4d5-8e58e2cd7509", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}