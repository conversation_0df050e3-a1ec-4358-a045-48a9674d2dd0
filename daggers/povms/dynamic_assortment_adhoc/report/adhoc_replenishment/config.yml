alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: adhoc_replenishment
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03S5JZDVEX
path: povms/dynamic_assortment_adhoc/report/adhoc_replenishment
paused: true
pool: povms_pool
project_name: dynamic_assortment_adhoc
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 9 * * *
  start_date: '2022-04-14T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
