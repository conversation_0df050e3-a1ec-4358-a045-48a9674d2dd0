{"cells": [{"cell_type": "code", "execution_count": null, "id": "d7f10fd2-e59e-4b9b-8670-2d8c18c07168", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pencilbox.io.sheets import gspread_client\n", "\n", "from pytz import timezone\n", "from datetime import datetime, timedelta\n", "import warnings\n", "import requests\n", "import boto3"]}, {"cell_type": "code", "execution_count": null, "id": "e2c6434b-0ce5-459c-a6b5-00cfe8c575f1", "metadata": {}, "outputs": [], "source": ["def fetch_open_requests():\n", "    sql = \"\"\"\n", "    select item_id, facility_id as fe_facility_id from lake_rpc.item_facility_state_request\n", "    where status = 'OPEN' and state != 2 and city_id != 4 and facility_id != 1028\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "open_requests_df = fetch_open_requests()\n", "open_requests_df.to_csv(\"temp_dump.csv\")\n", "open_requests_df = open_requests_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "285714fa-fbc7-43f4-9a27-5eaf5047b34e", "metadata": {}, "outputs": [], "source": ["def fetch_items(cat):\n", "    sql = \"\"\"\n", "    with all_skus as (\n", "    select item_id, max(name) as item_name, max(l0) as l0, max(l1) as l1, max(l2) as l2\n", "    from lake_rpc.item_category_details\n", "    group by 1\n", "    ),\n", "\n", "    fnv_skus as (select distinct item_id from lake_rpc.item_category_details where l0 = 'vegetables & fruits' and l1 != 'frozen veg'),\n", "    perishable_skus as (select distinct item_id from lake_rpc.product_product where perishable = 1\n", "                        and item_id not in (select distinct item_id from all_skus where l2 = 'fresh milk' or\n", "                                                                                        l2 = 'fresh chicken' or\n", "                                                                                        l2 = 'fresh fish & seafood' or\n", "                                                                                        l2 = 'fresh mutton' or\n", "                                                                                        l2 = 'ice cream' or\n", "                                                                                        l2 = 'frozen desserts' or\n", "                                                                                        l2 = 'plant based meat')),\n", "    milk_skus as (select distinct item_id from all_skus where l2 = 'fresh milk'),\n", "\n", "    giri_skus as (select distinct item_id from all_skus where item_id not in (select item_id from perishable_skus)\n", "                                                            and item_id not in (select item_id from fnv_skus)\n", "                                                            and item_id not in (select distinct item_id from all_skus where l2 = 'fresh milk' or\n", "                                                                                        l2 = 'fresh chicken' or\n", "                                                                                        l2 = 'fresh fish & seafood' or\n", "                                                                                        l2 = 'fresh mutton' or\n", "                                                                                        l2 = 'ice cream' or\n", "                                                                                        l2 = 'frozen desserts' or\n", "                                                                                        l2 = 'plant based meat'))\n", "    select item_id from {input} \n", "    \"\"\".format(\n", "        input=cat\n", "    )\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "4c673ae9-8c53-4737-a227-cd1a202b3cdc", "metadata": {}, "outputs": [], "source": ["def fetch_already_tagged():\n", "    sql = \"\"\"\n", "    select item_id, outlet_id, tag_value, active from lake_rpc.item_outlet_tag_mapping where tag_type_id = 8 and active = 1\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "    return pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "737ef4db-0946-41f8-97c1-49b9a078cad9", "metadata": {}, "outputs": [], "source": ["list_tea = [\"giri_skus\", \"perishable_skus\", \"milk_skus\", \"fnv_skus\"]\n", "# list_tea = ['giri_skus']\n", "sheet_id = \"1TYKEDBu3yvzyyMfZTf_23AGiE9UnCGpPaQnT28-M0EI\""]}, {"cell_type": "code", "execution_count": null, "id": "3fc71a45-3852-421a-ba4f-6ec9b65135cb", "metadata": {}, "outputs": [], "source": ["tea_df = pd.DataFrame()\n", "for i in list_tea:\n", "    print(i)\n", "    item_df = fetch_items(i)\n", "    item_facility_df = open_requests_df[\n", "        open_requests_df[\"item_id\"].isin(item_df[\"item_id\"].unique())\n", "    ]\n", "    data = pb.from_sheets(sheet_id, i)\n", "    data_temp = data[[\"fe_facility_id\", \"fe_outlet_id\", \"be_outlet_id\"]].astype(int)\n", "    data_temp = data_temp.drop_duplicates()\n", "    tea_df = pd.concat(\n", "        [tea_df, item_facility_df.merge(data_temp, on=[\"fe_facility_id\"], how=\"inner\")],\n", "        ignore_index=False,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "5ef9109f-2700-455e-93fc-88c4acb32351", "metadata": {"tags": []}, "outputs": [], "source": ["bread_mess = pb.from_sheets(sheet_id, \"Bamnoli_breads\")\n", "bread_mess = bread_mess[list(tea_df)].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "332523b1-4970-4bab-8146-299764b3879d", "metadata": {}, "outputs": [], "source": ["bread_mess_temp = bread_mess[[\"item_id\", \"fe_outlet_id\"]].merge(\n", "    tea_df, on=[\"item_id\", \"fe_outlet_id\"], how=\"inner\"\n", ")\n", "bread_mess_temp = bread_mess_temp[list(tea_df)]\n", "# tea_df = tea_df[~tea_df.isin(bread_mess_temp)]\n", "# tea_df = pd.concat([tea_df, bread_mess], ignore_index = False)"]}, {"cell_type": "code", "execution_count": null, "id": "354f18c8-1ff8-4165-95aa-1373a93e741e", "metadata": {}, "outputs": [], "source": ["out1 = pd.concat(\n", "    [tea_df[[\"item_id\", \"fe_outlet_id\"]], bread_mess[[\"item_id\", \"fe_outlet_id\"]]]\n", ").drop_duplicates(keep=False)\n", "tea_df = tea_df.merge(out1, on=[\"item_id\", \"fe_outlet_id\"], how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "id": "fb30df96-d006-4be2-9537-d01d245a4e14", "metadata": {}, "outputs": [], "source": ["len(tea_df)"]}, {"cell_type": "code", "execution_count": null, "id": "78618e9c-03ea-47ab-ae04-7267ed61d75e", "metadata": {}, "outputs": [], "source": ["tea_df.rename(\n", "    columns={\n", "        \"item_id\": \"Item ID\",\n", "        \"fe_outlet_id\": \"Outlet ID\",\n", "        \"fe_facility_id\": \"Facility ID\",\n", "        \"be_outlet_id\": \"Tag Value\",\n", "    },\n", "    inplace=True,\n", ")\n", "tea_df[\"Active\"] = 1\n", "tea_df = tea_df.astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "d87da562-c409-461e-a4c6-2bd221a4616c", "metadata": {}, "outputs": [], "source": ["tagged_df = fetch_already_tagged()\n", "tagged_df.rename(\n", "    columns={\n", "        \"item_id\": \"Item ID\",\n", "        \"outlet_id\": \"Outlet ID\",\n", "        \"active\": \"Active\",\n", "        \"tag_value\": \"Tag Value\",\n", "    },\n", "    inplace=True,\n", ")\n", "tagged_df = tagged_df.astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "18e27b45-76af-4750-abc3-3d03f6652aed", "metadata": {}, "outputs": [], "source": ["tea_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b4c98e6c-dc2a-4004-aceb-0d0329dc3f2f", "metadata": {}, "outputs": [], "source": ["tagged_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e9322208-53b3-4385-8005-0aeb5182e05b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "31ceb45d-7936-4b1d-a896-dacdf5296a27", "metadata": {}, "outputs": [], "source": ["tea_df = tea_df.merge(tagged_df, on=list(tagged_df), how=\"outer\", indicator=True)\n", "tea_df = tea_df[tea_df[\"_merge\"] == \"left_only\"]\n", "tea_df = tea_df[[\"Item ID\", \"Outlet ID\", \"Facility ID\", \"Tag Value\", \"Active\"]].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "6e192021-c027-4a8e-ac3b-e360e3e42b83", "metadata": {}, "outputs": [], "source": ["# tea_df.to_csv('tea_11-april.csv')\n", "len(tea_df)"]}, {"cell_type": "code", "execution_count": null, "id": "a41a63b0-534f-4779-bf4a-c2c5cabcd525", "metadata": {"tags": []}, "outputs": [], "source": ["tea_df[tea_df[\"Facility ID\"] == 1028]"]}, {"cell_type": "code", "execution_count": null, "id": "7ef62111-6fa3-4e48-848f-09169252ceb4", "metadata": {}, "outputs": [], "source": ["tea_df"]}, {"cell_type": "code", "execution_count": null, "id": "4cf63f2f-1a29-4e6d-a7fd-88aa6f12814c", "metadata": {}, "outputs": [], "source": ["fin_upload = tea_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "ff9d3d8a-9aa8-4c61-8125-32e010977a85", "metadata": {}, "outputs": [], "source": ["fin_upload[\"Outlet ID\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "id": "c1464c9d-6efe-49f2-8104-fea0108cd698", "metadata": {}, "outputs": [], "source": ["fin_upload.head()"]}, {"cell_type": "code", "execution_count": null, "id": "66abcb00-4809-4484-8f02-af49c0313ddf", "metadata": {}, "outputs": [], "source": ["# pb.to_sheets(tea_df, '1F-PNUc9vcwA2Vwjf-5akUQsBGXoAf4zxXuDz36WeKm4', 'Sheet1')"]}, {"cell_type": "code", "execution_count": null, "id": "5b8b0b83-3e34-44f3-afe6-c1ad23911725", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "    payload = {\n", "        \"file\": file_path,\n", "        \"created_by\": \"<PERSON><PERSON>\",\n", "        \"user_id\": 14,\n", "        \"is_auto_po\": True,\n", "        \"upload_type_id\": upload_type,\n", "        \"content_type\": \"text/csv\",\n", "        \"params\": {\"tag_type_id\": 8, \"is_auto_po\": True},\n", "    }\n", "    response = requests.post(url, json=payload)\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "id": "4d3f3261-f83a-45c5-bc64-ced79aa67e9c", "metadata": {}, "outputs": [], "source": ["format = \"%Y-%m-%d_%H-%M-%S\"\n", "now_utc = datetime.now(timezone(\"UTC\"))\n", "now_asia = now_utc.astimezone(timezone(\"Asia/Kolkata\"))\n", "csv_file_path = \"Tea Tag v1 Bot_{today}.csv\".format(today=now_asia.strftime(format))\n", "local_file_path = \"{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "processed_file = fin_upload.to_csv(\n", "    \"{filepath}\".format(filepath=local_file_path), index=False, header=True\n", ")\n", "file_path = \"outlet_item_tagging/{csv_file_path}\".format(csv_file_path=local_file_path)\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "bucket_name = \"retail-bulk-upload\"\n", "aws_key = secrets.get(\"aws_key\")\n", "\n", "aws_secret = secrets.get(\"aws_secret\")\n", "\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "\n", "s3 = session.resource(\"s3\")\n", "\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "if len(fin_upload) > 0:\n", "    bucket_obj.upload_file(local_file_path, file_path)\n", "    response = upload_to_bu(file_path, 42)\n", "\n", "status = \"Success\" if response.status_code == 201 else \"Failed\"\n", "print(status)\n", "\n", "from_email = \"<EMAIL>\"\n", "\n", "# email = pb.from_sheets(sheet_id, \"mail list\")\n", "# to_email = email.email_id.to_list()\n", "\n", "subject = \"TEA Tags Update Result {today}\".format(today=now_asia.strftime(format))\n", "\n", "html_content = (\n", "    \"<p>Hi,</p><br/>\"\n", "    \"<p>PFA request file uploaded on workdesk for TEA.</p><br/>\"\n", "    \"<p>Please check the file upload status <a href='https://retail.grofers.com/#/bulk-upload-status'> here:</a> Upload Type: outlet_item_tag\"\n", "    \"<p>File Upload Status: {status}</p>\".format(status=status)\n", ")\n", "\"<br/><p>Thanks & Regards</p>\" \"<br/><p>Retail Tech PO</p>\" \"<p>PO-VMS</p>\" \"<p>Grofers India Pvt. Ltd.</p>\"\n", "\n", "files = [local_file_path]\n", "\n", "pb.send_email(\n", "    from_email=from_email,\n", "    to_email=[\"<EMAIL>\", \"<EMAIL>\"],\n", "    subject=\"tea_df\",\n", "    html_content=html_content,\n", "    files=files,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7b0af318-8f61-4a4c-86fe-ecdb7c73bece", "metadata": {}, "outputs": [], "source": ["# len(open_requests_df)"]}, {"cell_type": "code", "execution_count": null, "id": "23467e37-c1ce-4795-8392-efad99476268", "metadata": {}, "outputs": [], "source": ["# import datetime"]}, {"cell_type": "code", "execution_count": null, "id": "8476f8bd-b74a-46d3-a17e-e9f761ab7a2b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0cabb946-4ae1-43b8-8709-a83e87169758", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}