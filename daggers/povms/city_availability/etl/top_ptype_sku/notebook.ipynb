{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prepare base\n", "\n", "##### Top 80% items per ptype basis GMV"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_sql = \"\"\"with mer_outlet_mapping as (select frontend_merchant_id, frontend_merchant_name,\n", "backend_merchant_id, backend_merchant_name,\n", "pos_outlet_id as outlet_id, pos_outlet_name as store,\n", "facility_id, pos_outlet_city_name as city\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True),\n", "\n", "\n", "sales as (\n", "select city, product_type, item_id, avg(gmv) as gmv from(\n", "    SELECT mom.city,\n", "        lower(dp.product_type) as product_type,\n", "        ipm.item_id,\n", "        date(o.install_ts + interval '5.5 Hours') as order_date,\n", "        sum(quantity) as quantity,\n", "        sum(quantity*selling_price) as gmv    \n", "    FROM lake_oms_bifrost.oms_order o\n", "    INNER JOIN lake_oms_bifrost.oms_merchant M ON o.MERCHANT_ID = M.ID\n", "    INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "    LEFT JOIN lake_rpc.item_product_mapping ipm ON oi.product_id = ipm.product_id AND ipm.active=1\n", "    inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "    INNER JOIN mer_outlet_mapping mom on m.external_id = mom.frontend_merchant_id\n", "    \n", "    WHERE date(o.install_ts + interval '5.5 Hours') between current_date-interval '30 days' and current_date-interval '1 days'\n", "    AND o.type='RetailForwardOrder'\n", "    AND o.current_Status <> 'CANCELLED'\n", "    AND (\"type\" IS NULL OR \"type\" IN ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder'))\n", "    \n", "    GROUP BY 1,2,3,4\n", ")\n", "where item_id is not null\n", "group by 1,2,3)\n", "\n", "select city, product_type, item_id, gmv, \n", "sum(gmv) over (partition by city, product_type order by gmv desc rows unbounded preceding) as cumulative_gmv,\n", "sum(gmv) over (partition by city, product_type) as total_gmv,\n", "(case when total_gmv = 0 then 0 else cumulative_gmv*1.00/total_gmv end) as cumulative_perc\n", "from sales\n", "\n", "order by city, product_type, gmv desc\n", "\n", "\"\"\"\n", "\n", "sales = read_sql_query(sales_sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_sku = sales[sales.cumulative_perc <= 0.8][[\"city\", \"product_type\", \"item_id\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_sku.item_id = top_sku.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_sku.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_sku[\"updated_at\"] = datetime.now() + <PERSON><PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_sku.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar(100)\", \"description\": \"product type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"date/time of run\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"city_top_sku\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"city\", \"item_id\"],\n", "    \"sortkey\": [\"updated_at\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains top 80% SKUs on city level (basis ptype)\",\n", "}\n", "pb.to_redshift(top_sku, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}