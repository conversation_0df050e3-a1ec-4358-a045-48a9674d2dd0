{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "from datetime import date, datetime, timedelta\n", "\n", "\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    if (end - start) > 60:\n", "        print(\"Time: \", (end - start) / 60, \"min\")\n", "    else:\n", "        print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eligible_facilities_query = f\"\"\"\n", "select distinct facility_id from(\n", "select facility_id, count(distinct order_id) as order_count\n", "from lake_ims.ims_order_details a\n", "left join lake_retail.console_outlet o on a.outlet = o.id\n", "where date(a.created_at) between current_date-interval '1 days' and current_date\n", "and a.status_id <> 5\n", "and business_type not ilike '%%b2b%%'\n", "group by 1\n", "having order_count > 10\n", ")\n", "\"\"\"\n", "\n", "eligible_facilities = read_sql_query(sql=eligible_facilities_query, con=CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_id_list = list(eligible_facilities[\"facility_id\"].unique())\n", "facility_id_list = tuple(facility_id_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.now() + timedelta(days=0, hours=5.5)).date()\n", "end_date = (datetime.now() + timedelta(days=0, hours=5.5)).date()\n", "\n", "start_date, end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(facility_id_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grocery_query = f\"\"\"with mer_outlet_mapping as (select \n", "facility_id, pos_outlet_city_name as city\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "\n", "group by 1,2)\n", "\n", "-- item_base as (\n", "-- select p.item_id,p.name as item_name, l0_category as l0, l2_category as l2\n", "-- from lake_rpc.item_details p\n", "-- LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON p.item_id = ipm.item_id -- AND ipm.active=1\n", "-- inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "\n", "-- where p.active = 1 and p.approved = 1\n", "\n", "-- group by 1,2,3,4\n", "-- )\n", "\n", "select date_, hour, city, facility_id, facility_name, count(distinct case when net_inv>0 then item_id end) as live_sku, count(distinct item_id) as total_sku, live_sku*1.00/total_sku as availability\n", "FROM\n", "    (\n", "    select \n", "        date(base.order_date) as date_, \n", "        extract(hour from base.order_date) as hour, \n", "        base.item_id,\n", "        dp.l0_category as l0, dp.l2_category as l2,\n", "        base.facility_id, \n", "        base.facility_name,\n", "        mom.city,\n", "        case when actual_quantity - blocked_quantity < 0 then 0 else actual_quantity - blocked_quantity end as net_inv,\n", "        app_live\n", "    from \n", "    (SELECT order_date, item_id, facility_id, facility_name, actual_quantity, blocked_quantity, app_live FROM \n", "    consumer.rpc_daily_availability\n", "    where order_date between ('{start_date}' || ' 00:00:00')::timestamp and  ('{end_date}' || ' 23:59:59')::timestamp\n", "    and extract(hour from order_date) between 6 and 23\n", "    and active_flag = 'active'\n", "    and facility_id in {facility_id_list}\n", "    ) base\n", "    \n", "    left join mer_outlet_mapping mom on base.facility_id = mom.facility_id\n", "    inner join (select distinct item_id, city from\n", "        metrics.city_top_sku\n", "        where updated_at = (select max(updated_at) from metrics.city_top_sku)) sku on base.item_id = sku.item_id and mom.city = sku.city\n", "    \n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON base.item_id = ipm.item_id -- AND ipm.active=1\n", "    inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "    \n", "    where lower(dp.l2_category) not in (\n", "        'tofu',\n", "        'fresh milk',\n", "        'lassi & chaach',\n", "        'yogurt',\n", "        'curd',\n", "        'buns, pavs & pizza base',\n", "        'speciality milk',\n", "        'paneer',\n", "        'farm-fresh eggs',\n", "        'batter',\n", "        'high protein & brown eggs',\n", "        'fresh chicken',\n", "        'fresh mutton',\n", "        'fresh fish & seafood',\n", "        'fresh sausage, salami, & ham',\n", "        'brown & multigrain breads',\n", "        'milk & white breads',\n", "        'speciality breads'\n", "        )\n", "    and (lower(dp.l0_category) != 'vegetables & fruits' or lower(dp.l2_category) ='frozen veg' ) \n", "        )\n", "\n", "GROUP BY 1,2,3,4,5\n", "\"\"\"\n", "grocery_df = read_sql_query(grocery_query, CON_REDSHIFT)\n", "grocery_df[\"assortment_type\"] = \"Packaged Goods\"\n", "grocery_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fnv_query = f\"\"\"\n", "\n", "with mer_outlet_mapping as (select \n", "facility_id, facility_name, pos_outlet_city_name as city\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "group by 1,2,3)\n", "\n", "-- item_base as (\n", "-- select distinct p.item_id,p.name as item_name, l0_category as l0, l2_category as l2\n", "-- from lake_rpc.item_details p\n", "-- LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON p.item_id = ipm.item_id -- AND ipm.active=1\n", "-- inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "-- where p.active = 1 and p.approved = 1\n", "-- )\n", "\n", "select date_, hour, city, facility_id, facility_name, count(distinct case when net_inv>0 then item_id end) as live_sku, count(distinct item_id) as total_sku, live_sku*1.00/total_sku as availability\n", "FROM \n", "(SELECT base.facility_id,\n", "           base.item_id,\n", "           mom.facility_name,\n", "           mom.city,\n", "           dp.l0_category as l0,\n", "           dp.l2_category as l2,\n", "           date(base.updated_at) AS date_,\n", "           extract(hour from base.updated_at) as hour,\n", "           case when base.current_inv < 0 then 0 else base.current_inv end as net_inv\n", "   \n", "   from\n", "   (select updated_at, facility_id, item_id, current_inv FROM metrics.fnv_hourly_details\n", "   WHERE updated_at between ('{start_date}' || ' 00:00:00')::timestamp and  ('{end_date}' || ' 23:59:59')::timestamp\n", "    and extract(hour from updated_at) between 6 and 23\n", "    and facility_id in {facility_id_list}) base\n", "   \n", "    LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON base.item_id = ipm.item_id -- AND ipm.active=1\n", "    inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "    \n", "   left join mer_outlet_mapping mom on base.facility_id = mom.facility_id\n", "    inner join (select distinct item_id, city from\n", "        metrics.city_top_sku\n", "        where updated_at = (select max(updated_at) from metrics.city_top_sku)) sku on base.item_id = sku.item_id and mom.city = sku.city\n", "    \n", "   WHERE lower(dp.l0_category) = 'vegetables & fruits'and lower(dp.l2_category) !='frozen veg'\n", "   \n", "   GROUP BY 1,2,3,4,5,6,7,8,9\n", ")\n", "GROUP BY 1,2,3,4,5\n", "\"\"\"\n", "fnv_df = read_sql_query(fnv_query, CON_REDSHIFT)\n", "fnv_df[\"assortment_type\"] = \"FnV\"\n", "fnv_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fnv_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishable_query = f\"\"\"with mer_outlet_mapping as (select\n", "facility_id, facility_name, pos_outlet_city_name as city\n", "from dwh.dim_merchant_outlet_facility_mapping\n", "where valid_to_utc >= current_date\n", "and is_current = True\n", "and is_mapping_enabled = True\n", "group by 1,2,3)\n", "\n", "-- item_base as (\n", "-- select distinct p.item_id,p.name as item_name, l0_category as l0, l2_category as l2\n", "-- from lake_rpc.item_details p\n", "-- LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON p.item_id = ipm.item_id -- AND ipm.active=1\n", "-- inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "-- where p.active = 1 and p.approved = 1\n", "-- )\n", "\n", "select date_, hour, city, facility_id, facility_name, count(distinct case when net_inv>0 then item_id end) as live_sku, count(distinct item_id) as total_sku, live_sku*1.00/total_sku as availability\n", "FROM \n", "(SELECT base.facility_id,\n", "            mom.facility_name,\n", "            mom.city,\n", "           base.item_id,\n", "           dp.l0_category as l0,\n", "           dp.l2_category as l2,\n", "           date(base.updated_at) AS date_,\n", "           extract(hour from base.updated_at) as hour,\n", "           case when base.current_inv < 0 then 0 else base.current_inv end as net_inv\n", "   FROM (select updated_at, facility_id, item_id, current_inv\n", "       from metrics.perishable_hourly_details_v2 \n", "       WHERE updated_at between ('{start_date}' || ' 00:00:00')::timestamp and  ('{end_date}' || ' 23:59:59')::timestamp\n", "       and extract(hour from updated_at) between 6 and 23\n", "       AND is_perishable = 1\n", "       AND facility_id in {facility_id_list}) base\n", "\n", "   LEFT JOIN dwh.dim_item_product_offer_mapping ipm ON base.item_id = ipm.item_id -- AND ipm.active=1\n", "   inner join dwh.dim_product dp on ipm.product_id = dp.product_id and is_current and is_product_enabled\n", "    \n", "   left join mer_outlet_mapping mom on base.facility_id = mom.facility_id\n", "    inner join (select distinct item_id, city from\n", "        metrics.city_top_sku\n", "        where updated_at = (select max(updated_at) from metrics.city_top_sku)) sku on base.item_id = sku.item_id and mom.city = sku.city\n", "        \n", "   where lower(dp.l2_category) in (\n", "        'tofu',\n", "        'fresh milk',\n", "        'lassi & chaach',\n", "        'yogurt',\n", "        'curd',\n", "        'buns, pavs & pizza base',\n", "        'speciality milk',\n", "        'paneer',\n", "        'farm-fresh eggs',\n", "        'batter',\n", "        'high protein & brown eggs',\n", "        'fresh chicken',\n", "        'fresh mutton',\n", "        'fresh fish & seafood',\n", "        'fresh sausage, salami, & ham',\n", "        'brown & multigrain breads',\n", "        'milk & white breads',\n", "        'speciality breads'\n", "        )\n", "   GROUP BY 1,2,3,4,5,6,7,8,9\n", ")\n", "GROUP BY 1,2,3,4,5\n", "\"\"\"\n", "perishable_df = read_sql_query(perishable_query, CON_REDSHIFT)\n", "# fnv_df = fnv_df.groupby(['date_','facility_id','item_id']).agg({'net_inv':'sum'}).reset_index()\n", "perishable_df[\"assortment_type\"] = \"Perishable\"\n", "perishable_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perishable_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df = pd.concat([grocery_df, fnv_df, perishable_df])\n", "base_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df = base_df[\n", "    [\n", "        \"city\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"assortment_type\",\n", "        \"date_\",\n", "        \"hour\",\n", "        \"live_sku\",\n", "        \"total_sku\",\n", "        \"availability\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df = base_df.sort_values(\n", "    by=[\"city\", \"facility_name\", \"assortment_type\", \"date_\", \"hour\"],\n", "    ascending=[True, True, True, False, True],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df = base_df.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df[\"facility_id\"] = 0\n", "city_df[\"facility_name\"] = \"Overall\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df = (\n", "    city_df.groupby(\n", "        [\n", "            \"city\",\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"assortment_type\",\n", "            \"date_\",\n", "            \"hour\",\n", "        ]\n", "    )\n", "    .agg({\"live_sku\": \"sum\", \"total_sku\": \"sum\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df[\"availability\"] = city_df[\"live_sku\"] * 1.00 / city_df[\"total_sku\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df = city_df.sort_values(\n", "    by=[\"city\", \"facility_name\", \"assortment_type\", \"date_\", \"hour\"],\n", "    ascending=[True, True, True, False, True],\n", ").reset_index()\n", "city_df.drop(columns=[\"index\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = pd.concat([city_df, base_df])\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_df[[\"date_\", \"hour\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(city_df, \"1uVhcGf0zDO-rS85i8cJljMuGWFd2Cds8QawzF0u9RnE\", \"Raw\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df[\"updated_at\"] = datetime.today() + timedelta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(100)\", \"description\": \"facility name\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\n", "        \"name\": \"live_sku\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"count of SKUs with positive inventory\",\n", "    },\n", "    {\"name\": \"total_sku\", \"type\": \"integer\", \"description\": \"distinct count of SKUs\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"store_hourly_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"sortkey\": [\"facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains current date store level hourly availability for city top SKUs\",\n", "}\n", "pb.to_redshift(final_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"city\", \"type\": \"varchar(50)\", \"description\": \"city\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"facility id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(100)\", \"description\": \"facility name\"},\n", "    {\n", "        \"name\": \"assortment_type\",\n", "        \"type\": \"varchar(20)\",\n", "        \"description\": \"fnv/perishable/packaged_goods\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"timestamp\", \"description\": \"date of snapshot\"},\n", "    {\"name\": \"hour\", \"type\": \"integer\", \"description\": \"hour of snapshot\"},\n", "    {\n", "        \"name\": \"live_sku\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"count of SKUs with positive inventory\",\n", "    },\n", "    {\"name\": \"total_sku\", \"type\": \"integer\", \"description\": \"distinct count of SKUs\"},\n", "    {\"name\": \"availability\", \"type\": \"float\", \"description\": \"live_sku/total_sku\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"timestamp of run\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)).hour >= 20:\n", "    t_df = final_df[final_df.date_ == final_df.date_.max()]\n", "\n", "    print(t_df.shape)\n", "\n", "    kwargs = {\n", "        \"schema_name\": \"metrics\",\n", "        \"table_name\": \"store_hourly_availability_logs\",\n", "        \"column_dtypes\": column_dtypes,\n", "        \"primary_key\": [\"facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "        \"sortkey\": [\"facility_id\", \"assortment_type\", \"date_\", \"hour\"],\n", "        \"incremental_key\": \"updated_at\",\n", "        \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "        \"table_description\": \"This table contains logs for store level hourly availability for city top SKUs\",\n", "    }\n", "    pb.to_redshift(t_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# last_snap_df = t_df[t_df.hour == t_df.hour.max()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# last_snap_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# last_snap_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}