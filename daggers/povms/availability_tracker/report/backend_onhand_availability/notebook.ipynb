{"cells": [{"cell_type": "code", "execution_count": null, "id": "ef1bf019-e08b-4052-8818-8fc41ee259ab", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime"]}, {"cell_type": "code", "execution_count": null, "id": "0a373681-84ee-4caa-863a-a083d8f2d92e", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 5\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6b9c85b9-2587-445b-9fe8-8eba77d018c3", "metadata": {}, "outputs": [], "source": ["CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "48ddd5f8-dcc0-4188-badc-a514aa11c58e", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "92f36ae0-e0d8-4f52-ba47-5b1664f25cbc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1901676b-7ad7-46e2-b0d0-5530a8171198", "metadata": {}, "outputs": [], "source": ["bucket_mapping_query = f\"\"\"\n", "with\n", "\n", "bucket_info as (\n", "    select \n", "        item_id, \n", "        facility_id,\n", "        count(distinct case when tag_value = 'Y' then tag_value end) as tag_x,  \n", "        count(distinct case when tag_value = 'A' then tag_value end) as tag_A,\n", "        count(distinct case when tag_value = 'B' then tag_value end) as tag_b,\n", "        count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "    from ( \n", "        Select \n", "            b.item_id, \n", "            po.facility_id,\n", "            tag_value,\n", "            tag_type_id \n", "        from   lake_rpc.item_outlet_tag_mapping  b\n", "        join lake_retail.console_outlet po ON b.outlet_id = po.id\n", "        where b.tag_type_id in (1,6) and b.active = 1\n", "        group by 1,2,3,4\n", "        )\n", "    Group By 1,2\n", "),\n", "\n", "bucket_final as (\n", "    select \n", "        item_id,    \n", "        facility_id,\n", "        case when tag_A = 1 then 'A'\n", "             when tag_b = 1 then 'B'\n", "             else 'None' end as bucket_a_b,\n", "        case when tag_x = 1 then 'X' else 'None' end as bucket_x\n", "    from bucket_info\n", ")\n", "\n", "select * from bucket_final\n", "\n", "\"\"\"\n", "bucket_mapping_df = read_sql_query(sql=bucket_mapping_query, con=CON_REDSHIFT)\n", "bucket_mapping_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "d45f2238-148b-4ab4-9ecc-fae7e88f6cf8", "metadata": {}, "outputs": [], "source": ["item_level_info_query = f\"\"\"\n", "select\n", "    item_id,\n", "    name as item_name,\n", "    brand,\n", "    brand_id,\n", "    manufacturer,\n", "    variant_description,\n", "    is_pl,\n", "    variant_mrp,\n", "    perishable,\n", "    row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from\n", "    rpc.product_product\n", "\"\"\"\n", "item_level_info_df = read_sql_query(sql=item_level_info_query, con=CON_TRINO)\n", "item_level_info_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ca889982-7a5d-4d52-ab82-c6a84f84a367", "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "with base as\n", "(\n", "    SELECT \n", "        date(od.created_at  + interval '330' Minute) as date, outlet AS outlet_id, oi.item_id, sum(oi.quantity) as quantity,\n", "        sum(case when price is null then 0 else oi.quantity*price end) as GMV\n", "    FROM \n", "        ims.ims_order_details od\n", "    LEFT JOIN \n", "        ims.ims_order_items oi on od.id = oi.order_details_id\n", "        AND oi.insert_ds_ist between cast(cast(current_date as timestamp) - interval '32' day as varchar) and cast(current_date as varchar)\n", "    LEFT JOIN \n", "        ims.ims_order_actuals oa on od.id = oa.order_details_id and oi.item_id=oa.item_id\n", "    AND \n", "        oa.insert_ds_ist between cast(cast(current_date as timestamp) - interval '32' day as varchar) and cast(current_date as varchar)\n", "    WHERE \n", "        status_id in (1,2)\n", "    AND \n", "        od.insert_ds_ist between cast(cast(current_date as timestamp) - interval '32' day as varchar) and cast(current_date as varchar)\n", "\n", "\n", "    GROUP BY 1,2,3\n", ")\n", "\n", "SELECT \n", "    co.facility_id, b.item_id, sum(quantity) as last30_act_qty, sum(quantity) / count(distinct date) as cpd\n", "FROM \n", "    base b\n", "left join \n", "    lake_retail.console_outlet co on co.id = b.outlet_id and co.active = 1\n", "where \n", "    b.date between cast(current_date as timestamp) -  interval '30' day and cast(current_date as timestamp) - interval - '1' day\n", "group by 1,2\n", "\n", "\"\"\"\n", "sales_df = read_sql_query(sql=sales_query, con=CON_TRINO)\n", "sales_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c112f447-7568-41e5-ac95-cd1dcc6b3a81", "metadata": {}, "outputs": [], "source": ["tea_mapping_query = f\"\"\"\n", "\n", "select\n", "    a.item_id,\n", "    c.facility_id as receiver_facility_id,\n", "    min(b.facility_id) as sender_facility_id\n", "from \n", "    rpc.item_outlet_tag_mapping a\n", "left join \n", "    (select id, facility_id from retail.console_outlet group by 1,2) b on a.tag_value = cast(b.id as varchar)\n", "left join \n", "    (select id, facility_id from retail.console_outlet group by 1,2) c on a.outlet_id = c.id\n", "where \n", "    a.tag_type_id = 8 and a.active = 1\n", "group by 1,2\n", "\n", "\"\"\"\n", "tea_mapping_df = read_sql_query(sql=tea_mapping_query, con=CON_TRINO)\n", "tea_mapping_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "86a8af63-5ff0-46be-8926-b0722b2fd1bd", "metadata": {}, "outputs": [], "source": ["esto_query = f\"\"\"\n", "with pre_esto as\n", "(\n", "select a.*,\n", "b.facility_id as sender_facility_id, cf.name as sender_facility_name,\n", "c.facility_id as receiver_facility_id, f.name as receiver_facility_name\n", "from metrics.esto_details a\n", "\n", "left join (select id, max(facility_id) as facility_id, max(name) as name from lake_retail.console_outlet group by 1) b on a.sender_outlet_id = b.id\n", "left join (select id, max(name) as name from lake_crates.facility group by 1) cf on cf.id = b.facility_id\n", "\n", "left join (select id, max(facility_id) as facility_id, max(name) as name from lake_retail.console_outlet group by 1) c on a.receiving_outlet_id = c.id\n", "left join (select id, max(name) as name from lake_crates.facility group by 1) f on f.id = c.facility_id\n", "where b.name not like '%%Infra%%'\n", "),\n", "\n", "final_esto as\n", "(\n", "select sender_outlet_name, receiver_outlet_name,\n", "sto_id,\tsender_facility_id,\tsender_facility_name, receiver_facility_id, receiver_facility_name,\n", "item_id,\n", "MAX(date(sto_created_at)) as sto_created_at ,\n", "MAX(date(sto_invoice_created_at)) as sto_invoice_created_at , \n", "MAX(date(delivery_date)) as delivery_date_expiry,\n", "MAX(date(grn_started_at)) as grn_started_at, \n", "\n", "max(sto_type) as sto_type, \n", "max(sto_state) as sto_state, \n", "max(invoice_state) as invoice_state,\n", "\n", "sum(reserved_quantity) as reserved_qty ,\n", "sum(billed_quantity) as billed_quantity ,\n", "sum(inwarded_quantity) as inwarded_qty,\n", "\n", "sum(reserved_quantity) as req_qty_sf,\n", "\n", "max(cast(sto_created_at as time)) as sto_created_tm ,\n", "max(cast(grn_started_at as time)) as grn_created_tm,\n", "max(invoice_id) as invoice_id ,\n", "DATEDIFF(hour, max(sto_created_at) , max(grn_started_at)) as avg_hour\n", "\n", "from pre_esto a\n", "where date(sto_created_at) between current_date-5 and current_date\n", "AND STO_STATE NOT IN ('Manual Expiry')\n", "group by 1,2,3,4,5,6,7,8\n", "),\n", "\n", "final_status as\n", "(\n", "select * , \n", "case \n", "when invoice_state = 'Raised' then 'In-Transit'\n", "when invoice_state != ''  then 'Delivered'\n", "when sto_state = 'Expired' then 'Expired before Delivery'\n", "when sto_state = 'Created' then 'Need to Pick' else 'other' end as final_status\n", "from final_esto a\n", "left join (select distinct facility_id, business_type_id from lake_retail.console_outlet) bc on a.receiver_facility_id = bc.facility_id\n", "where\n", "sender_facility_id not in (153)\n", "and bc.business_type_id = 7\n", "),\n", "\n", "summary_esto as\n", "(\n", "select\n", "receiver_facility_id,\n", "item_id,\n", "sum(case when sto_created_at between current_date-4 and current_date and final_status in ('In-Transit')  then billed_quantity end) as l4_in_transit,\n", "sum(case when sto_created_at between current_date-4 and current_date and final_status  in ('Need to Pick')  then reserved_qty end ) as l4_need_to_pick,\n", "\n", "sum(case when grn_started_at between current_date-5 and current_date and final_status  in ('Delivered')  then inwarded_qty end ) as l30_inwarded_qty\n", "\n", "from final_status a\n", "group by 1,2\n", ")\n", "\n", "select * from summary_esto\n", "\n", "\"\"\"\n", "esto_df = read_sql_query(sql=esto_query, con=CON_REDSHIFT)\n", "esto_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "f57e3cea-64d5-4466-84b1-852924731afa", "metadata": {}, "outputs": [], "source": ["po_query = f\"\"\"\n", "with pre_open_po as\n", "(\n", "    select * from po.purchase_order p\n", "    where p.issue_date >= cast(current_date as timestamp) - interval '40' day\n", ")\n", "\n", "select \n", "    o.facility_id,\n", "    poi.item_id as Item_Id,\n", "    max(p.po_number) as max_po_number,\n", "    count(distinct p.po_number) as num_open_po,\n", "    sum(poi.remaining_quantity) as open_po_qty,\n", "    sum(poi.landing_rate*poi.remaining_quantity) as open_po_val,\n", "    sum(case when date(ps.schedule_date_time  + interval '330' Minute) = current_date then remaining_quantity end) as today_scht,\n", "    sum(case when date(ps.schedule_date_time  + interval '330' Minute) = cast(current_date as timestamp) + interval '1' day then remaining_quantity end) as today_scht1,\n", "    sum(case when date(ps.schedule_date_time  + interval '330' Minute) is not null then remaining_quantity end) as total_scht\n", "from pre_open_po p\n", "Left Join \n", "    po.po_schedule ps on p.id = ps.po_id_id\n", "inner join \n", "    po.purchase_order_items poi on p.id=poi.po_id\n", "inner join \n", "    retail.console_outlet o on o.id=p.outlet_id\n", "inner join \n", "    po.purchase_order_status posa on posa.po_id = p.id\n", "inner join \n", "    po.purchase_order_state posta on posta.id = posa.po_state_id\n", "left join \n", "    (select po_id, item_id, sum(quantity) as grn_qty from po.po_grn group by 1,2)pg \n", "on pg.po_id = poi.po_id and pg.item_id = poi.item_id\n", "where\n", "    (posta.name in ('Created','Scheduled','Unscheduled','Rescheduled') or p.is_multiple_grn = 1) and posta.name <> 'Expired'\n", "and \n", "    date(p.issue_date + interval '330' Minute) between cast(current_date as timestamp) - interval '40' day and current_date\n", "and \n", "    posta.name <> 'Cancelled'\n", "and \n", "    posa.po_state_id  Not in (4,5,10)\n", "group by \n", "    1,2\n", "\"\"\"\n", "po_df = read_sql_query(sql=po_query, con=CON_TRINO)\n", "po_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3a0c03d8-cb41-4fb7-ae19-edf4830468ea", "metadata": {}, "outputs": [], "source": ["rpc_daily_av_query = f\"\"\"\n", "with \n", "pre_rpc as\n", "(\n", "    select max(order_date) as m_order_date from consumer.rpc_daily_availability where order_date > current_date - 1\n", "),\n", "\n", "\n", "\n", "rpc_tbl_treatment as\n", "(\n", "select\n", "a.facility_id,\n", "a.item_id,\n", "coalesce(actual_quantity,0) - coalesce(blocked_quantity,0) as net_inv_qty,\n", "inv_flag,\n", "app_live,\n", "new_substate\n", "from \n", "rpc_daily_availability a\n", "where a.order_date = (select distinct m_order_date from pre_rpc)\n", ")\n", "\n", "select \n", "item_id, facility_id,\n", "coalesce(sum(net_inv_qty),0) as net_inv_qty,\n", "max(inv_flag) as inv_flag,\n", "max(app_live) as app_live,\n", "max(new_substate) as new_substate\n", "from rpc_tbl_treatment\n", "group by 1,2\n", "\n", "\n", "\"\"\"\n", "rpc_daily_av_df = read_sql_query(sql=rpc_daily_av_query, con=CON_REDSHIFT)\n", "rpc_daily_av_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7e909417-087e-47d9-8ead-f49bb7c27801", "metadata": {}, "outputs": [], "source": ["vendor_details_query = f\"\"\"\n", "select vv.item_id, vv.facility_id , vn.vendor_name\n", "from  lake_vms.vms_vendor_facility_alignment vv\n", "left join lake_vms.vms_vendor vn on vn.id = vv.vendor_id \n", "where vv.active = 1 \n", "group by 1,2,3\n", "\n", "\"\"\"\n", "vendor_details_df = read_sql_query(sql=vendor_details_query, con=CON_REDSHIFT)\n", "vendor_details_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "e1245e78-ff54-466b-b17d-d0c9d562aef7", "metadata": {}, "outputs": [], "source": ["min_max_query = f\"\"\"\n", "select facility_id, a.item_id,\n", "min(min_quantity) as min_min_qty, \n", "max(max_quantity) as max_max_qty\n", "from\n", "lake_ars.item_min_max_quantity a\n", "group by 1,2\n", "\n", "\"\"\"\n", "min_max_df = read_sql_query(sql=min_max_query, con=CON_REDSHIFT)\n", "min_max_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "10c43d0a-5905-45ed-a935-618e8b22494f", "metadata": {}, "outputs": [], "source": ["sales_del_query = f\"\"\"\n", "\n", "with pre_sales_del as\n", "(\n", "    select\n", "        (created_at + interval '330' Minute) as created_at, order_id,outlet, business_type \n", "    from \n", "        ims.ims_order_details\n", "    where \n", "        insert_ds_ist between cast(cast(current_date as timestamp) - interval '2' day as varchar) and cast(cast(current_date as timestamp) + interval '1' day as varchar)\n", "    and \n", "        status_id <> 5\n", ")\n", "\n", "select  \n", "    facility_id, count(distinct order_id) as order_count\n", "from \n", "    pre_sales_del a\n", "left join \n", "    retail.console_outlet o on a.outlet = o.id\n", "where \n", "    date(a.created_at) = cast(current_date as timestamp) - interval '1' day\n", "and \n", "    business_type not like '%%b2b%%'\n", "and \n", "    o.business_type_id in (7)\n", "and \n", "    facility_id not in (29,139,26)\n", "group by \n", "    1\n", "having \n", "    count(distinct order_id) > 5\n", "\n", "\"\"\"\n", "sales_del_df = read_sql_query(sql=sales_del_query, con=CON_TRINO)\n", "sales_del_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "2b983e94-beac-43ed-9cd8-8ce31d34ad56", "metadata": {}, "outputs": [], "source": ["item_base_query = \"\"\"\n", "\n", "with\n", "fe_details as\n", "(select ma.facility_id as fe_facility_id, ma.item_id, category\n", "from metrics.perishable_assortment ma\n", "\n", "join (select max(updated_at) as updated_at, item_id, facility_id from metrics.perishable_assortment\n", "where date(updated_at) between current_date - 8 and current_date\n", "group by 2,3\n", ") pa on pa.item_id = ma.item_id and pa.facility_id = ma.facility_id and pa.updated_at = ma.updated_at\n", "),\n", "\n", "item_details as\n", "(select distinct rpc.item_id,\n", "case when l0_id = 1487 then 'FnV'\n", "when l2_id in (1185,1367,63,1732,1733,1734,1369,31,108,198,1097,1956,1425,1827,1093,949,882,1389,1778,1395,1091,36,950,138,882,1094,106) then 'Perishable'\n", "when perishable = 1 then 'Perishable'\n", "when fd.item_id is not null then 'Perishable' else 'Packaged Goods' end as assortment_type\n", "\n", "from lake_rpc.product_product rpc\n", "\n", "join (select item_id, l0_id, l2_id from lake_rpc.item_category_details) cd on cd.item_id = rpc.item_id\n", "left join (select item_id from fe_details) fd on fd.item_id = rpc.item_id\n", "\n", "left join (select distinct item_id from lake_rpc.item_tag_mapping where active = true\n", "and cast(tag_value as int) = 3 and tag_type_id = 3\n", ") i on i.item_id = rpc.item_id\n", "\n", "where id in (select max(id) as id from lake_rpc.product_product pp where pp.active = 1 and pp.approved = 1 group by item_id)\n", "and assortment_type = 'Packaged Goods' and i.item_id is null\n", ")\n", "\n", "select distinct item_id from item_details\n", "\n", "\"\"\"\n", "item_base_df = read_sql_query(sql=item_base_query, con=CON_REDSHIFT)\n", "item_base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7179cedd-0360-43aa-be9a-216f959d3190", "metadata": {}, "outputs": [], "source": ["master_assortment_query = f\"\"\"\n", "\n", "select \n", "    distinct facility_id, item_id\n", "from \n", "    rpc.product_facility_master_assortment \n", "where \n", "    master_assortment_substate_id=1\n", "\n", "\"\"\"\n", "master_assortment_df = read_sql_query(sql=master_assortment_query, con=CON_TRINO)\n", "master_assortment_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "42626646-193b-413d-b230-42e703a4696a", "metadata": {}, "outputs": [], "source": ["master_assortment_df = pd.merge(master_assortment_df, item_base_df, on=[\"item_id\"], how=\"inner\")\n", "master_assortment_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "5f11329d-2a33-4308-8560-997a4c622e4b", "metadata": {}, "outputs": [], "source": ["facility_mapping_query = f\"\"\"\n", "\n", "select distinct id as facility_id, name as facility_name\n", "from lake_crates.facility\n", "\n", "\"\"\"\n", "facility_mapping_df = read_sql_query(sql=facility_mapping_query, con=CON_REDSHIFT)\n", "facility_mapping_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "8873d929-cc8c-41ac-a0ad-a3d599efb16f", "metadata": {}, "outputs": [], "source": ["ptype_tbl_query = f\"\"\"\n", "\n", "with categories as\n", "(\n", "SELECT \n", "P.ID AS PID,\n", "P.NAME AS PRODUCT,\n", "P.UNIT AS Unit,\n", "C2.NAME AS L2,\n", "(case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "C.NAME AS L0,\n", "P.BRAND AS brand,\n", "P.MANUFACTURER AS manf,\n", "pt.name as product_type\n", "from lake_cms.gr_product P\n", "INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "AND PCM.IS_PRIMARY=TRUE\n", "INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "), \n", "\n", "category_pre as\n", "(\n", "SELECT \n", "item_id,\n", "product_id,\n", "cat.l0,\n", "cat.l1,\n", "cat.l2,\n", "cat.product_type\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl as\n", "(\n", "select \n", "item_id,\n", "max(l0) as l0,\n", "max(l1) as l1,\n", "max(l2) as l2,\n", "max(product_type) as ptype\n", "from category_pre\n", "group by 1\n", ")\n", "\n", "select * from ptype_tbl\n", "\n", "\"\"\"\n", "ptype_tbl_df = read_sql_query(sql=ptype_tbl_query, con=CON_REDSHIFT)\n", "ptype_tbl_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "06b488df-db5b-4c0e-93f2-ce3984419f80", "metadata": {}, "outputs": [], "source": ["facility_list = tuple(\n", "    list(\n", "        master_assortment_df[master_assortment_df.facility_id.isna() == False][\n", "            \"facility_id\"\n", "        ].unique()\n", "    )\n", ")\n", "item_list = tuple(\n", "    list(master_assortment_df[master_assortment_df.item_id.isna() == False][\"item_id\"].unique())\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b3631d08-1003-4933-9ff0-1a3c8ef34919", "metadata": {}, "outputs": [], "source": ["facility_outlet_map_query = f\"\"\"select distinct facility_id, id as outlet_id\n", "from lake_retail.console_outlet\n", "where active = 1 and business_type_id = 7\n", "and facility_id in {facility_list}\n", "\"\"\"\n", "facility_outlet_map_df = read_sql_query(sql=facility_outlet_map_query, con=CON_REDSHIFT)\n", "facility_outlet_map_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "045a2ad9-f797-4c15-a4f2-e01d90bc906e", "metadata": {}, "outputs": [], "source": ["outlet_list = tuple(\n", "    list(\n", "        facility_outlet_map_df[facility_outlet_map_df.outlet_id.isna() == False][\n", "            \"outlet_id\"\n", "        ].unique()\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2d417d06-a6fa-4bc0-a4c2-956cbcfeb62a", "metadata": {}, "outputs": [], "source": ["default_cpd_query = f\"\"\"select item_id, outlet_id, max(cpd) as default_cpd\n", "from \n", "    lake_snorlax.default_cpd cpd\n", "where outlet_id in {outlet_list}\n", "group by 1,2\n", "\"\"\"\n", "default_cpd_df = read_sql_query(sql=default_cpd_query, con=CON_REDSHIFT)\n", "default_cpd_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3d988ba9-5d12-46db-bbbf-72ba96ee12ea", "metadata": {}, "outputs": [], "source": ["default_cpd_df = pd.merge(default_cpd_df, facility_outlet_map_df, on=[\"outlet_id\"], how=\"left\")\n", "default_cpd_df = default_cpd_df[default_cpd_df.facility_id.isna() == False][\n", "    [\"item_id\", \"facility_id\", \"default_cpd\"]\n", "]\n", "default_cpd_df = (\n", "    default_cpd_df.groupby([\"item_id\", \"facility_id\"]).agg({\"default_cpd\": \"max\"}).reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a75c43b0-3fc9-4c7b-884a-281793ac5169", "metadata": {}, "outputs": [], "source": ["# Merges"]}, {"cell_type": "code", "execution_count": null, "id": "7c33b250-1f5b-486a-9de9-a4778f0146ef", "metadata": {}, "outputs": [], "source": ["copy = master_assortment_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "095b42ef-928d-4567-9a6e-76007bb871fd", "metadata": {}, "outputs": [], "source": ["item_level_info_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "489b52a1-8940-4073-8614-eee22004d3e7", "metadata": {}, "outputs": [], "source": ["master_assortment_df = copy.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "b53b328e-8ae2-40cf-a9b8-98c48036acd7", "metadata": {}, "outputs": [], "source": ["import gc\n", "\n", "del copy\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "b6916d2d-382f-4c1c-9dab-03979851e5f4", "metadata": {}, "outputs": [], "source": ["master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    rpc_daily_av_df[[\"item_id\", \"facility_id\", \"net_inv_qty\"]].rename(\n", "        columns={\"net_inv_qty\": \"net_inv_qty_fr\"}\n", "    ),\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df[\"net_inv_qty_fr\"] = master_assortment_df[\"net_inv_qty_fr\"].fillna(0)\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    esto_df[[\"item_id\", \"receiver_facility_id\", \"l4_in_transit\", \"l4_need_to_pick\"]].rename(\n", "        columns={\n", "            \"receiver_facility_id\": \"facility_id\",\n", "            \"l4_in_transit\": \"esto_in_transit_fr\",\n", "            \"l4_need_to_pick\": \"esto_need_to_pick_fr\",\n", "        }\n", "    ),\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "master_assortment_df[[\"esto_in_transit_fr\", \"esto_need_to_pick_fr\"]] = master_assortment_df[\n", "    [\"esto_in_transit_fr\", \"esto_need_to_pick_fr\"]\n", "].fillna(0)\n", "\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    tea_mapping_df.rename(columns={\"receiver_facility_id\": \"facility_id\"}),\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    esto_df[[\"item_id\", \"receiver_facility_id\", \"l4_in_transit\", \"l4_need_to_pick\"]].rename(\n", "        columns={\n", "            \"receiver_facility_id\": \"sender_facility_id\",\n", "            \"l4_in_transit\": \"esto_in_transit_bk\",\n", "            \"l4_need_to_pick\": \"esto_need_to_pick_bk\",\n", "        }\n", "    ),\n", "    on=[\"item_id\", \"sender_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "master_assortment_df[[\"esto_in_transit_bk\", \"esto_need_to_pick_bk\"]] = master_assortment_df[\n", "    [\"esto_in_transit_bk\", \"esto_need_to_pick_bk\"]\n", "].fillna(0)\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    rpc_daily_av_df[[\"item_id\", \"facility_id\", \"net_inv_qty\"]].rename(\n", "        columns={\"net_inv_qty\": \"net_inv_qtybk\", \"facility_id\": \"sender_facility_id\"}\n", "    ),\n", "    on=[\"item_id\", \"sender_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "master_assortment_df[\"net_inv_qtybk\"] = master_assortment_df[\"net_inv_qtybk\"].fillna(0)\n", "\n", "\n", "po_agg_df = (\n", "    po_df.groupby([\"Item_Id\", \"facility_id\"])\n", "    .agg({\"open_po_qty\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"Item_Id\": \"item_id\"})\n", ")\n", "\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    po_agg_df.rename(\n", "        columns={\"facility_id\": \"sender_facility_id\", \"open_po_qty\": \"open_po_qty_bk\"}\n", "    ),\n", "    on=[\"item_id\", \"sender_facility_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df[\"open_po_qty_bk\"] = master_assortment_df[\"open_po_qty_bk\"].fillna(0)\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    po_agg_df.rename(columns={\"open_po_qty\": \"open_po_qty_fr\"}),\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df[\"open_po_qty_fr\"] = master_assortment_df[\"open_po_qty_fr\"].fillna(0)\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    min_max_df.rename(columns={\"min_min_qty\": \"min_qty_fr\", \"max_max_qty\": \"max_qty_fr\"}),\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "item_level_info_df = item_level_info_df[item_level_info_df.row_rank == 1]\n", "item_level_info_df = (\n", "    item_level_info_df.groupby([\"item_id\", \"item_name\"])\n", "    .agg({\"manufacturer\": \"min\", \"perishable\": \"min\"})\n", "    .reset_index()\n", ")\n", "\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df, item_level_info_df, on=[\"item_id\"], how=\"left\"\n", ")\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    sales_df.rename(columns={\"last30_act_qty\": \"last30_chk_qty_ds\"}),\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    vendor_details_df.rename(\n", "        columns={\"vendor_name\": \"vendor_name_bk\", \"facility_id\": \"sender_facility_id\"}\n", "    ),\n", "    on=[\"item_id\", \"sender_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    sales_del_df[[\"facility_id\"]].drop_duplicates(),\n", "    on=[\"facility_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    facility_mapping_df.rename(columns={\"facility_name\": \"facility_fr\"}),\n", "    on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    facility_mapping_df.rename(\n", "        columns={\n", "            \"facility_name\": \"sender_facility\",\n", "            \"facility_id\": \"sender_facility_id\",\n", "        }\n", "    ),\n", "    on=[\"sender_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "master_assortment_df = master_assortment_df[master_assortment_df.sender_facility_id.isna() == False]"]}, {"cell_type": "code", "execution_count": null, "id": "203522ac-0a98-4d5f-bd1c-4b54c1eda7b3", "metadata": {}, "outputs": [], "source": ["facility_list = pd.concat(\n", "    [\n", "        master_assortment_df[master_assortment_df.sender_facility_id.isna() == False][\n", "            [\"sender_facility_id\"]\n", "        ]\n", "        .drop_duplicates()\n", "        .rename(columns={\"sender_facility_id\": \"facility_id\"}),\n", "        master_assortment_df[[\"facility_id\"]].drop_duplicates(),\n", "    ]\n", ")\n", "facility_list = list(facility_list[\"facility_id\"].unique())\n", "facility_list = tuple(facility_list)"]}, {"cell_type": "code", "execution_count": null, "id": "ad3a432c-afee-472b-b33c-a12d0e2105e7", "metadata": {}, "outputs": [], "source": ["item_list = list(master_assortment_df[\"item_id\"].unique())\n", "item_list = tuple(item_list)"]}, {"cell_type": "code", "execution_count": null, "id": "28186f06-e27b-475f-9080-c6deaf2490fa", "metadata": {}, "outputs": [], "source": ["# Extracting Pending Putaway\n", "ppaway_query = f\"\"\"\n", "\n", "SELECT item_id, frontend_facility_id as facility_id, SUM(pending_putaway_qty) as pending_putaway_qty\n", "FROM\n", "(\n", "SELECT \n", "    pp.item_id, co.facility_id as frontend_facility_id,\n", "    (x.putaway_qty) as pending_putaway_qty, cast(x.putaway_time + interval '5' hour + interval '30' minute as date) as putaway_start_time,\n", "    max(case when i.pos_timestamp < x.putaway_time then (i.pos_timestamp + interval '5' hour + interval '30' minute) end) AS grn_time\n", "    FROM \n", "    (\n", "        SELECT\n", "            il.variant_id, il.delta, il.inventory_update_id, il.pos_timestamp\n", "        FROM \n", "            ims.ims_inventory_log il\n", "        INNER JOIN \n", "            retail.console_outlet co ON il.outlet_id = co.id\n", "        WHERE \n", "            inventory_update_type_id IN (28,76)\n", "        AND \n", "            co.facility_id in {facility_list}\n", "        AND \n", "            il.insert_ds_ist >= cast(current_date -interval '7' day as varchar)\n", "        AND \n", "            delta > 0\n", "    ) i\n", "    INNER JOIN ims.ims_inventory_stock_details i3 ON i3.inventory_update_id = i.inventory_update_id\n", "    INNER JOIN rpc.product_product pp ON pp.variant_id=i3.variant_id AND pp.active=1\n", "    INNER JOIN retail.console_outlet co ON i3.outlet_id=co.id\n", "    LEFT JOIN (\n", "        select putlist_id,item_id,outlet_id,variant_id,wpli.updated_at as putaway_time, wpli.quantity as putaway_qty, item_state_id as item_putlist_state\n", "                from warehouse_location.warehouse_put_list_item wpli\n", "                inner join retail.console_outlet co on wpli.outlet_id = co.id\n", "                where wpli.item_id in {item_list}\n", "                and co.facility_id in {facility_list}\n", "                and wpli.updated_at >= current_timestamp -interval '7' day\n", "                and wpli.item_state_id in (1,2,3)\n", "                )x\n", "    ON x.variant_id=pp.variant_id AND x.outlet_id=i3.outlet_id\n", "    WHERE x.putaway_qty > 0\n", "    AND item_putlist_state in (1,2,3)\n", "    GROUP BY 1,2,3,4\n", ")\n", "GROUP BY 1,2\n", "\"\"\"\n", "ppaway_grouped = read_sql_query(sql=ppaway_query, con=CON_TRINO)\n", "ppaway_grouped.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "687180e8-4a31-49ae-ab2c-bd8d63255ec4", "metadata": {}, "outputs": [], "source": ["master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    ppaway_grouped.rename(columns={\"pending_putaway_qty\": \"pending_putaway_qty_fr\"}),\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df[\"pending_putaway_qty_fr\"] = master_assortment_df[\n", "    \"pending_putaway_qty_fr\"\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "7054e05a-de4d-422f-9c91-42e2ecab8787", "metadata": {}, "outputs": [], "source": ["bucket_mapping_df[\"final_bucket\"] = np.where(\n", "    bucket_mapping_df[\"bucket_x\"] == \"X\",\n", "    \"X\",\n", "    np.where(\n", "        bucket_mapping_df[\"bucket_a_b\"] == \"A\",\n", "        \"A\",\n", "        np.where(bucket_mapping_df[\"bucket_a_b\"] == \"B\", \"B\", \"B\"),\n", "    ),\n", ")\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    bucket_mapping_df[[\"facility_id\", \"item_id\", \"final_bucket\"]].drop_duplicates(),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df[\"final_bucket\"] = master_assortment_df[\"final_bucket\"].fillna(\"B\")\n", "master_assortment_df[\"cpd\"] = master_assortment_df[\"cpd\"].fillna(0.04)\n", "# master_assortment_df['max_qty_fr'] = master_assortment_df['max_qty_fr'].fillna(2)\n", "# master_assortment_df['min_qty_fr'] = master_assortment_df['min_qty_fr'].fillna(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7ddfee28-9a9c-4c0c-86dc-0cc2621069e7", "metadata": {}, "outputs": [], "source": ["master_assortment_df = pd.merge(\n", "    master_assortment_df, default_cpd_df, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "442fd8fb-1970-42bb-9a16-b2ff383f9ef2", "metadata": {}, "outputs": [], "source": ["master_assortment_df[\"oh_ind\"] = np.where(master_assortment_df[\"net_inv_qty_fr\"] >= 1, 1, 0)\n", "\n", "master_assortment_df[\"IN_TRANSIT_IND\"] = np.where(\n", "    (master_assortment_df[\"net_inv_qty_fr\"] + master_assortment_df[\"esto_in_transit_fr\"]) >= 1,\n", "    1,\n", "    0,\n", ")\n", "\n", "master_assortment_df[\"STO_IND\"] = np.where(\n", "    (\n", "        master_assortment_df[\"net_inv_qty_fr\"]\n", "        + master_assortment_df[\"esto_in_transit_fr\"]\n", "        + master_assortment_df[\"esto_need_to_pick_fr\"]\n", "        + master_assortment_df[\"pending_putaway_qty_fr\"]\n", "    )\n", "    >= 1,\n", "    1,\n", "    0,\n", ")\n", "\n", "master_assortment_df[\"default_cpd\"] = 7 * master_assortment_df[\"default_cpd\"]\n", "\n", "master_assortment_df[\"get_required_min\"] = master_assortment_df[[\"default_cpd\", \"max_qty_fr\"]].min(\n", "    axis=1\n", ")\n", "\n", "master_assortment_df[\"get_required_min\"] = np.where(\n", "    master_assortment_df[\"get_required_min\"] < 1,\n", "    1,\n", "    master_assortment_df[\"get_required_min\"],\n", ")\n", "\n", "# master_assortment_df[\"Item_Store_Demand\"] = np.where(\n", "#     master_assortment_df[\"final_bucket\"] != \"B\",\n", "#     np.where(\n", "#         master_assortment_df[\"max_qty_fr\"]\n", "#         - (\n", "#             master_assortment_df[\"net_inv_qty_fr\"]\n", "#             + master_assortment_df[\"esto_in_transit_fr\"]\n", "#             + master_assortment_df[\"esto_need_to_pick_fr\"]\n", "#         )\n", "#         > 0,\n", "#         master_assortment_df[\"max_qty_fr\"]\n", "#         - (\n", "#             master_assortment_df[\"net_inv_qty_fr\"]\n", "#             + master_assortment_df[\"esto_in_transit_fr\"]\n", "#             + master_assortment_df[\"esto_need_to_pick_fr\"]\n", "#         ),\n", "#         0,\n", "#     ),\n", "#     np.where(\n", "#         np.ceil(master_assortment_df[\"cpd\"] * 3)\n", "#         - (\n", "#             master_assortment_df[\"net_inv_qty_fr\"]\n", "#             + master_assortment_df[\"esto_in_transit_fr\"]\n", "#             + master_assortment_df[\"esto_need_to_pick_fr\"]\n", "#         )\n", "#         > 0,\n", "#         np.ceil(master_assortment_df[\"cpd\"] * 3)\n", "#         - (\n", "#             master_assortment_df[\"net_inv_qty_fr\"]\n", "#             + master_assortment_df[\"esto_in_transit_fr\"]\n", "#             + master_assortment_df[\"esto_need_to_pick_fr\"]\n", "#         ),\n", "#         0,\n", "#     ),\n", "# )\n", "\n", "\n", "master_assortment_df[\"Item_Store_Demand\"] = np.where(\n", "    np.ceil(master_assortment_df[\"get_required_min\"])\n", "    - (\n", "        master_assortment_df[\"net_inv_qty_fr\"]\n", "        + master_assortment_df[\"esto_in_transit_fr\"]\n", "        + master_assortment_df[\"esto_need_to_pick_fr\"]\n", "    )\n", "    > 0,\n", "    np.ceil(master_assortment_df[\"get_required_min\"])\n", "    - (\n", "        master_assortment_df[\"net_inv_qty_fr\"]\n", "        + master_assortment_df[\"esto_in_transit_fr\"]\n", "        + master_assortment_df[\"esto_need_to_pick_fr\"]\n", "    ),\n", "    0,\n", ")\n", "\n", "backend_demand = (\n", "    master_assortment_df.groupby([\"sender_facility_id\", \"item_id\"])\n", "    .agg({\"Item_Store_Demand\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"Item_Store_Demand\": \"Backend_Demand\"})\n", ")\n", "\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    backend_demand,\n", "    on=[\"item_id\", \"sender_facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "master_assortment_df[\"Backend_OH_IND\"] = np.where(\n", "    master_assortment_df[\"STO_IND\"] == 1,\n", "    1,\n", "    np.where(\n", "        master_assortment_df[\"Backend_Demand\"] < master_assortment_df[\"net_inv_qtybk\"],\n", "        1,\n", "        0,\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a4c1dfef-7191-4ea8-9819-f3f186687f56", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9230d85d-4afb-45f1-b4ed-46081564af6d", "metadata": {}, "outputs": [], "source": ["master_assortment_df = pd.merge(master_assortment_df, ptype_tbl_df, on=[\"item_id\"], how=\"left\")\n", "master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    bucket_mapping_df[[\"item_id\", \"facility_id\", \"bucket_a_b\", \"bucket_x\"]],\n", "    on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "master_assortment_df[\"bucket_a_b\"] = master_assortment_df[\"bucket_a_b\"].fillna(\"None\")\n", "\n", "master_assortment_df[\"bucket_x\"] = master_assortment_df[\"bucket_x\"].fillna(\"None\")"]}, {"cell_type": "code", "execution_count": null, "id": "4ac18544-867a-4ed3-9e3b-d1fa5e8fe412", "metadata": {}, "outputs": [], "source": ["sender_facility_list = list(\n", "    master_assortment_df[master_assortment_df.sender_facility_id.isna() == False][\n", "        \"sender_facility_id\"\n", "    ].unique()\n", ")\n", "sender_facility_list = tuple(sender_facility_list)"]}, {"cell_type": "code", "execution_count": null, "id": "d8d95203-f37c-426b-bd10-21826ae49902", "metadata": {}, "outputs": [], "source": ["backend_master_assortment_query = f\"\"\"\n", "\n", "select \n", "    distinct facility_id as sender_facility_id, item_id, master_assortment_substate_id as bkend_asst_status\n", "from \n", "    rpc.product_facility_master_assortment \n", "where \n", "    facility_id in {sender_facility_list}\n", "\n", "\"\"\"\n", "backend_master_assortment_df = read_sql_query(sql=backend_master_assortment_query, con=CON_TRINO)\n", "backend_master_assortment_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "061047ab-578c-4bbc-b1b1-ff20a0f30eb2", "metadata": {}, "outputs": [], "source": ["master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    ppaway_grouped.rename(\n", "        columns={\n", "            \"pending_putaway_qty\": \"pending_putaway_qty_bk\",\n", "            \"facility_id\": \"sender_facility_id\",\n", "        }\n", "    ),\n", "    on=[\"item_id\", \"sender_facility_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df[\"pending_putaway_qty_bk\"] = master_assortment_df[\n", "    \"pending_putaway_qty_bk\"\n", "].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "e9b5f568-1821-4ace-b049-6d9ab54231be", "metadata": {}, "outputs": [], "source": ["master_assortment_df = pd.merge(\n", "    master_assortment_df,\n", "    backend_master_assortment_df,\n", "    on=[\"sender_facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "master_assortment_df[\"Backend_ON_Order_IND\"] = np.where(\n", "    master_assortment_df[\"Backend_OH_IND\"] == 1,\n", "    1,\n", "    np.where(\n", "        (\n", "            master_assortment_df[\"Backend_Demand\"]\n", "            < (\n", "                master_assortment_df[\"net_inv_qtybk\"]\n", "                + master_assortment_df[\"open_po_qty_bk\"]\n", "                + master_assortment_df[\"esto_in_transit_bk\"]\n", "                + master_assortment_df[\"pending_putaway_qty_bk\"]\n", "            )\n", "        ),\n", "        1,\n", "        0,\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bdbd672a-15c2-46d5-a41b-d415ba76af07", "metadata": {}, "outputs": [], "source": ["master_assortment_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f24ca982-6c1d-4ec2-9001-8b5a8bca3b20", "metadata": {}, "outputs": [], "source": ["master_assortment_df = master_assortment_df.drop(columns={\"final_bucket\", \"cpd\", \"default_cpd\"})"]}, {"cell_type": "code", "execution_count": null, "id": "367295fe-f687-4b0e-9ee3-f78589f05f40", "metadata": {}, "outputs": [], "source": ["master_assortment_df[\n", "    [\n", "        \"net_inv_qty_fr\",\n", "        \"esto_in_transit_fr\",\n", "        \"esto_need_to_pick_fr\",\n", "        \"sender_facility_id\",\n", "        \"esto_in_transit_bk\",\n", "        \"esto_need_to_pick_bk\",\n", "        \"net_inv_qtybk\",\n", "        \"open_po_qty_bk\",\n", "        \"open_po_qty_fr\",\n", "        \"min_qty_fr\",\n", "        \"max_qty_fr\",\n", "        \"last30_chk_qty_ds\",\n", "        \"pending_putaway_qty_fr\",\n", "        \"oh_ind\",\n", "        \"IN_TRANSIT_IND\",\n", "        \"STO_IND\",\n", "        \"Item_Store_Demand\",\n", "        \"Backend_Demand\",\n", "        \"Backend_OH_IND\",\n", "        \"pending_putaway_qty_bk\",\n", "    ]\n", "] = master_assortment_df[\n", "    [\n", "        \"net_inv_qty_fr\",\n", "        \"esto_in_transit_fr\",\n", "        \"esto_need_to_pick_fr\",\n", "        \"sender_facility_id\",\n", "        \"esto_in_transit_bk\",\n", "        \"esto_need_to_pick_bk\",\n", "        \"net_inv_qtybk\",\n", "        \"open_po_qty_bk\",\n", "        \"open_po_qty_fr\",\n", "        \"min_qty_fr\",\n", "        \"max_qty_fr\",\n", "        \"last30_chk_qty_ds\",\n", "        \"pending_putaway_qty_fr\",\n", "        \"oh_ind\",\n", "        \"IN_TRANSIT_IND\",\n", "        \"STO_IND\",\n", "        \"Item_Store_Demand\",\n", "        \"Backend_Demand\",\n", "        \"Backend_OH_IND\",\n", "        \"pending_putaway_qty_bk\",\n", "    ]\n", "].fillna(\n", "    0\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "390fdc4c-49a3-4851-af59-9bf51619fa5a", "metadata": {}, "outputs": [], "source": ["master_assortment_df[[\"item_id\", \"facility_id\", \"sender_facility_id\"]] = master_assortment_df[\n", "    [\"item_id\", \"facility_id\", \"sender_facility_id\"]\n", "].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "aa116f20-5234-4e04-8ac8-5ebfa0d2b3cd", "metadata": {}, "outputs": [], "source": ["master_assortment_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "53b4fa34-79a3-4452-b880-f14c00a98d1c", "metadata": {}, "outputs": [], "source": ["master_assortment_df[\"updated_at\"] = datetime.now() + timed<PERSON>ta(hours=5.5)"]}, {"cell_type": "code", "execution_count": null, "id": "0f45fc0d-64cc-4ac0-be5b-fb79d0344b08", "metadata": {}, "outputs": [], "source": ["master_assortment_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "8a2f4b8a-3573-4216-b255-31c849373f59", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for facility \",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"unique identifier for item \"},\n", "    {\n", "        \"name\": \"net_inv_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"net inventory at frontend\",\n", "    },\n", "    {\n", "        \"name\": \"esto_in_transit_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity in transit to frontend\",\n", "    },\n", "    {\n", "        \"name\": \"esto_need_to_pick_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity to be picked for transfer to frontend\",\n", "    },\n", "    {\n", "        \"name\": \"sender_facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for sender facility \",\n", "    },\n", "    {\n", "        \"name\": \"esto_in_transit_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity in transit to backend\",\n", "    },\n", "    {\n", "        \"name\": \"esto_need_to_pick_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity to be picked for transfer to backend\",\n", "    },\n", "    {\n", "        \"name\": \"net_inv_qtybk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"net inventory at backend\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_qty_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"open PO quantity at backend\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"open PO quantity at frontend\",\n", "    },\n", "    {\n", "        \"name\": \"min_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"minimum quantity for frontend\",\n", "    },\n", "    {\n", "        \"name\": \"max_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"maximum quantity for frontend\",\n", "    },\n", "    {\"name\": \"item_name\", \"type\": \"varchar(400)\", \"description\": \"name of item\"},\n", "    {\n", "        \"name\": \"manufacturer\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of manufacturer\",\n", "    },\n", "    {\n", "        \"name\": \"perishable\",\n", "        \"type\": \"float\",\n", "        \"description\": \"flag for identifying perishables\",\n", "    },\n", "    {\n", "        \"name\": \"last30_chk_qty_ds\",\n", "        \"type\": \"float\",\n", "        \"description\": \"last 30 day sales at darkstore\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_name_bk\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of vendor for item at backend\",\n", "    },\n", "    {\n", "        \"name\": \"facility_fr\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of frontend facility\",\n", "    },\n", "    {\n", "        \"name\": \"sender_facility\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of backend facility\",\n", "    },\n", "    {\n", "        \"name\": \"pending_putaway_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pending putaway at frontend\",\n", "    },\n", "    {\"name\": \"oh_ind\", \"type\": \"float\", \"description\": \"onhand flag\"},\n", "    {\"name\": \"IN_TRANSIT_IND\", \"type\": \"float\", \"description\": \"intransit flag\"},\n", "    {\"name\": \"STO_IND\", \"type\": \"float\", \"description\": \"sto flag\"},\n", "    {\n", "        \"name\": \"get_required_min\",\n", "        \"type\": \"float\",\n", "        \"description\": \"min of (max quantity and 7*default CPD)\",\n", "    },\n", "    {\n", "        \"name\": \"Item_Store_Demand\",\n", "        \"type\": \"float\",\n", "        \"description\": \"demand for frontend of item\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON>end_Demand\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend total demand for item\",\n", "    },\n", "    {\n", "        \"name\": \"Backend_OH_IND\",\n", "        \"type\": \"float\",\n", "        \"description\": \"onhand flag for item at backend\",\n", "    },\n", "    {\"name\": \"l0\", \"type\": \"varchar(200)\", \"description\": \"l0\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(200)\", \"description\": \"l1\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(200)\", \"description\": \"l2\"},\n", "    {\"name\": \"ptype\", \"type\": \"varchar(200)\", \"description\": \"ptype\"},\n", "    {\n", "        \"name\": \"bucket_a_b\",\n", "        \"type\": \"varchar(200)\",\n", "        \"description\": \"bucket flag for A and B\",\n", "    },\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar(200)\", \"description\": \"bucket flag for X\"},\n", "    {\n", "        \"name\": \"pending_putaway_qty_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pending putaway at backend\",\n", "    },\n", "    {\n", "        \"name\": \"bkend_asst_status\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend assortment status (master assortment state)\",\n", "    },\n", "    {\n", "        \"name\": \"Backend_ON_Order_IND\",\n", "        \"type\": \"float\",\n", "        \"description\": \"on hand flag final\",\n", "    },\n", "    {\n", "        \"name\": \"updated_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"updated time stamp in IST\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "2068707d-e09e-4dfb-bdf6-f425fd3ec1f2", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"backend_on_hand_availability_df\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"sender_facility_id\"],\n", "    \"sortkey\": [\"sender_facility_id\", \"facility_id\", \"item_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"rebuild\",  # , # append, rebuild, truncate or upsert\n", "    \"table_description\": \"on hand availability data\",  # Description of the table being sent to redshift\n", "}\n", "if master_assortment_df.shape[0] > 0:\n", "    start_r = time.time()\n", "    pb.to_redshift(master_assortment_df, **kwargs)\n", "    end_r = time.time()\n", "    print(round((end_r - start_r) / 60, 2))"]}, {"cell_type": "code", "execution_count": null, "id": "353a357b-974b-4d2c-8fbb-2334d570794e", "metadata": {}, "outputs": [], "source": ["(datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)).hour"]}, {"cell_type": "code", "execution_count": null, "id": "f8932ed5-d642-42a0-8861-fe4b9b603ac4", "metadata": {}, "outputs": [], "source": ["master_assortment_df = master_assortment_df.drop(columns={\"get_required_min\"})"]}, {"cell_type": "code", "execution_count": null, "id": "c767cb0f-e7bb-4a71-9fe8-08032668a5ee", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for facility \",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"unique identifier for item \"},\n", "    {\n", "        \"name\": \"net_inv_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"net inventory at frontend\",\n", "    },\n", "    {\n", "        \"name\": \"esto_in_transit_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity in transit to frontend\",\n", "    },\n", "    {\n", "        \"name\": \"esto_need_to_pick_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity to be picked for transfer to frontend\",\n", "    },\n", "    {\n", "        \"name\": \"sender_facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"unique identifier for sender facility \",\n", "    },\n", "    {\n", "        \"name\": \"esto_in_transit_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity in transit to backend\",\n", "    },\n", "    {\n", "        \"name\": \"esto_need_to_pick_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sto quantity to be picked for transfer to backend\",\n", "    },\n", "    {\n", "        \"name\": \"net_inv_qtybk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"net inventory at backend\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_qty_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"open PO quantity at backend\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"open PO quantity at frontend\",\n", "    },\n", "    {\n", "        \"name\": \"min_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"minimum quantity for frontend\",\n", "    },\n", "    {\n", "        \"name\": \"max_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"maximum quantity for frontend\",\n", "    },\n", "    {\"name\": \"item_name\", \"type\": \"varchar(400)\", \"description\": \"name of item\"},\n", "    {\n", "        \"name\": \"manufacturer\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of manufacturer\",\n", "    },\n", "    {\n", "        \"name\": \"perishable\",\n", "        \"type\": \"float\",\n", "        \"description\": \"flag for identifying perishables\",\n", "    },\n", "    {\n", "        \"name\": \"last30_chk_qty_ds\",\n", "        \"type\": \"float\",\n", "        \"description\": \"last 30 day sales at darkstore\",\n", "    },\n", "    {\n", "        \"name\": \"vendor_name_bk\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of vendor for item at backend\",\n", "    },\n", "    {\n", "        \"name\": \"facility_fr\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of frontend facility\",\n", "    },\n", "    {\n", "        \"name\": \"sender_facility\",\n", "        \"type\": \"varchar(400)\",\n", "        \"description\": \"name of backend facility\",\n", "    },\n", "    {\n", "        \"name\": \"pending_putaway_qty_fr\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pending putaway at frontend\",\n", "    },\n", "    {\"name\": \"oh_ind\", \"type\": \"float\", \"description\": \"onhand flag\"},\n", "    {\"name\": \"IN_TRANSIT_IND\", \"type\": \"float\", \"description\": \"intransit flag\"},\n", "    {\"name\": \"STO_IND\", \"type\": \"float\", \"description\": \"sto flag\"},\n", "    {\n", "        \"name\": \"Item_Store_Demand\",\n", "        \"type\": \"float\",\n", "        \"description\": \"demand for frontend of item\",\n", "    },\n", "    {\n", "        \"name\": \"<PERSON>end_Demand\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend total demand for item\",\n", "    },\n", "    {\n", "        \"name\": \"Backend_OH_IND\",\n", "        \"type\": \"float\",\n", "        \"description\": \"onhand flag for item at backend\",\n", "    },\n", "    {\"name\": \"l0\", \"type\": \"varchar(200)\", \"description\": \"l0\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(200)\", \"description\": \"l1\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(200)\", \"description\": \"l2\"},\n", "    {\"name\": \"ptype\", \"type\": \"varchar(200)\", \"description\": \"ptype\"},\n", "    {\n", "        \"name\": \"bucket_a_b\",\n", "        \"type\": \"varchar(200)\",\n", "        \"description\": \"bucket flag for A and B\",\n", "    },\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar(200)\", \"description\": \"bucket flag for X\"},\n", "    {\n", "        \"name\": \"pending_putaway_qty_bk\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pending putaway at backend\",\n", "    },\n", "    {\n", "        \"name\": \"bkend_asst_status\",\n", "        \"type\": \"float\",\n", "        \"description\": \"backend assortment status (master assortment state)\",\n", "    },\n", "    {\n", "        \"name\": \"Backend_ON_Order_IND\",\n", "        \"type\": \"float\",\n", "        \"description\": \"on hand flag final\",\n", "    },\n", "    {\n", "        \"name\": \"updated_at\",\n", "        \"type\": \"datetime\",\n", "        \"description\": \"updated time stamp in IST\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "c70740d6-aa11-49db-93f0-ad3272ae06da", "metadata": {}, "outputs": [], "source": ["(datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)).hour"]}, {"cell_type": "code", "execution_count": null, "id": "39e2f8a3-aaa9-4006-bc00-4ccd4cb30cd7", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"backend_on_hand_availability_df_log\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"sender_facility_id\", \"updated_at\"],\n", "    \"sortkey\": [\"updated_at\", \"sender_facility_id\", \"facility_id\", \"item_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"upsert\",  # , # append, rebuild, truncate or upsert\n", "    \"table_description\": \"on hand availability data\",  # Description of the table being sent to redshift\n", "}\n", "if (master_assortment_df.shape[0] > 0) & (\n", "    ((datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)).hour == 21)\n", "    | ((datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)).hour == 9)\n", "):\n", "    start_r = time.time()\n", "    pb.to_redshift(master_assortment_df, **kwargs)\n", "    end_r = time.time()\n", "    print(round((end_r - start_r) / 60, 2))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}