{"cells": [{"cell_type": "code", "execution_count": null, "id": "472c5910-f511-4086-b5ea-bdb36851882d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "\n", "\n", "presto = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "rds_po = pb.get_connection(\"[Replica] RDS PO\")\n", "rds_ims = pb.get_connection(\"[Replica] RDS IMS\")\n", "\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "markdown", "id": "ecf6a308-08d1-44bd-ae3c-110509e311a0", "metadata": {}, "source": ["# outlet details"]}, {"cell_type": "code", "execution_count": null, "id": "866f8c9d-2ce5-47ce-a4b6-96b948b802c8", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = \"\"\"\n", "    \n", "    select z.zone,\n", "        rcl.name as city_name,\n", "        om.outlet_id as hot_outlet_id, om.outlet_name, \n", "        om.facility_id, pf.internal_facility_identifier as facility_name,\n", "        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "        rco.business_type_id,\n", "        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "        \n", "            from lake_po.physical_facility_outlet_mapping om\n", "            \n", "            left join (select id, tax_location_id,\n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                    from lake_retail.console_outlet\n", "                        ) rco on rco.id = om.outlet_id\n", "            \n", "            left join (select pf.* from lake_po.physical_facility pf\n", "                join (select facility_id, max(updated_at) as updated_at from lake_po.physical_facility group by 1\n", "                        ) pf2 on pf2.facility_id = pf.facility_id and pf2.updated_at = pf.updated_at\n", "                        ) pf on pf.facility_id = om.facility_id\n", "            \n", "            left join (select distinct warehouse_id, cloud_store_id from lake_retail.warehouse_outlet_mapping \n", "                where active = 1\n", "                        ) wom on wom.warehouse_id = om.outlet_id\n", "            \n", "            left join lake_retail.console_location rcl on rcl.id = rco.tax_location_id\n", "            \n", "            left join (select distinct facility_id, zone from metrics.outlet_zone_mapping where business_type_id in (1,12,7,19,20,21)\n", "                        ) z on z.facility_id = om.facility_id\n", "            \n", "                where rco.business_type_id in (7) and om.outlet_id not in (0,1739)\n", "                and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                and om.outlet_name not like '%%SSC%%'\n", "                and om.outlet_name not like '%%MODI%%'\n", "                and om.outlet_name not like '%%hot ff%%'\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(outlets, redshift)\n", "\n", "\n", "start = time.time()\n", "outlets = outlets()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "1c7a7b15-2632-4d8e-aba1-b61593cfd0ef", "metadata": {}, "outputs": [], "source": ["outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "974c4399-8abc-46aa-889a-eab5001d2d02", "metadata": {}, "outputs": [], "source": ["frontend_outlets = outlets[outlets[\"taggings\"] == \"fe\"].rename(\n", "    columns={\"inv_outlet_id\": \"outlet_id\"}\n", ")\n", "fe_outlet_id_list = list(frontend_outlets[\"outlet_id\"].unique())\n", "fe_outlet_id_list = tuple(fe_outlet_id_list)\n", "len(fe_outlet_id_list)"]}, {"cell_type": "markdown", "id": "ca7bd9a8-edac-474f-b1b4-9c68eecfd803", "metadata": {}, "source": ["# cluster_mapping"]}, {"cell_type": "code", "execution_count": null, "id": "d20e974b-529d-439e-bdbe-85fb1f481291", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "\n", "cluster_mapping = pb.from_sheets(\n", "    \"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"new\", clear_cache=True\n", ")\n", "cluster_mapping = cluster_mapping[[\"city_name\", \"cluster_mapping\"]]\n", "\n", "cluster_mapping = cluster_mapping[\n", "    ~((cluster_mapping[\"city_name\"] == \"\") | (cluster_mapping[\"cluster_mapping\"] == \"\"))\n", "]\n", "\n", "cluster_mapping.dropna(inplace=True)\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "d2652805-b181-46fd-be2e-3c45d54ae2dd", "metadata": {}, "outputs": [], "source": ["cluster_mapping.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8e150928-ece2-4924-9075-c8eda209aae4", "metadata": {}, "outputs": [], "source": ["final_city_frontend_outlets = pd.merge(\n", "    frontend_outlets, cluster_mapping, on=[\"city_name\"], how=\"inner\"\n", ")\n", "\n", "final_city_frontend_outlets.shape, outlets.shape"]}, {"cell_type": "code", "execution_count": null, "id": "ad31beea-1434-466b-96b6-1847ca7ee7a2", "metadata": {}, "outputs": [], "source": ["final_city_frontend_outlets.groupby([\"cluster_mapping\"])[[\"city_name\"]].agg(\"count\").reset_index()"]}, {"cell_type": "markdown", "id": "2f2edaab-3e01-4925-a3bb-98e44b69801a", "metadata": {}, "source": ["# assortment"]}, {"cell_type": "code", "execution_count": null, "id": "9c0c9c43-80a3-400e-aed4-3ac93aff0428", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "\n", "assortment = pb.from_sheets(\n", "    \"1Yr4UHSc8_90x6AqTD20XjPeAhGyNGvxj1HQ2_uR3tV4\", \"assortment\", clear_cache=True\n", ")\n", "assortment = assortment[[\"cluster_mapping\", \"item_id\", \"assortment_type\"]]\n", "\n", "assortment = assortment[\n", "    ~(\n", "        (assortment[\"item_id\"] == \"\")\n", "        | (assortment[\"cluster_mapping\"] == \"\")\n", "        | (assortment[\"assortment_type\"] == \"\")\n", "    )\n", "]\n", "assortment[\"item_id\"] = assortment[\"item_id\"].astype(int)\n", "\n", "assortment.dropna(inplace=True)\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "88516857-20b3-47d0-924e-2aa5f74a2b9f", "metadata": {}, "outputs": [], "source": ["assortment.shape"]}, {"cell_type": "code", "execution_count": null, "id": "63545056-80c4-44c5-a013-586788344bd4", "metadata": {}, "outputs": [], "source": ["city_assortment = pd.merge(\n", "    assortment, final_city_frontend_outlets, on=[\"cluster_mapping\"], how=\"inner\"\n", ")\n", "\n", "city_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "74dc7ffd-2ece-4280-9dfd-1738cdf06275", "metadata": {}, "outputs": [], "source": ["city_assortment.shape"]}, {"cell_type": "code", "execution_count": null, "id": "652612fa-9344-43fc-a9c6-f4a7be196a07", "metadata": {}, "outputs": [], "source": ["final_assortment = city_assortment[\n", "    [\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"assortment_type\",\n", "    ]\n", "]\n", "\n", "final_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e5b7bc4e-ee2c-44b0-a29c-1a70137969a6", "metadata": {}, "outputs": [], "source": ["final_assortment[\n", "    (final_assortment[\"item_id\"] == 10018689) & (final_assortment[\"facility_id\"] == 1250)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e2937606-32c5-45ec-b830-3df66ea9e800", "metadata": {}, "outputs": [], "source": ["final_assortment.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8e11ac65-3bbb-4ab2-b091-32bbe8de4f99", "metadata": {}, "outputs": [], "source": ["final_assortment = pd.merge(final_assortment, cluster_mapping, on=[\"city_name\"], how=\"inner\")\n", "\n", "final_assortment.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bd5b8a3c-e409-4eb2-9ce9-d281a1752290", "metadata": {}, "outputs": [], "source": ["final_assortment[\"check\"] = np.where(\n", "    (final_assortment[\"city_name\"] == final_assortment[\"cluster_mapping\"]), 1, 0\n", ")\n", "\n", "final_assortment = (\n", "    final_assortment[final_assortment[\"check\"] == 1]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\", \"check\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "84454584-4456-4628-890e-4cab0443255c", "metadata": {}, "outputs": [], "source": ["final_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "84fbc255-4bb0-4a76-8d51-18bc801c0e11", "metadata": {}, "outputs": [], "source": ["final_assortment[\n", "    (final_assortment[\"item_id\"] == 10018689) & (final_assortment[\"facility_id\"] == 1250)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "43304708-2a8e-4478-940d-e264858af1f2", "metadata": {}, "outputs": [], "source": ["final_assortment.shape"]}, {"cell_type": "code", "execution_count": null, "id": "bcad60c6-ce42-4b71-a6b3-385d0973f013", "metadata": {}, "outputs": [], "source": ["item_id_list = list(final_assortment[\"item_id\"].unique())\n", "item_id_list = tuple(item_id_list)\n", "len(item_id_list)"]}, {"cell_type": "markdown", "id": "24a60330-4eb2-47cf-a890-315dfd24310b", "metadata": {}, "source": ["# item details"]}, {"cell_type": "code", "execution_count": null, "id": "49e46706-e9c3-4b3e-8860-0f7a70a9b211", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = f\"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select rpp.item_id, (rpp.name || ' ' || variant_description) as item_name, l1, p_type\n", "\n", "            from lake_rpc.product_product rpp\n", "\n", "                left join lake_rpc.item_category_details cd on cd.item_id = rpp.item_id\n", "\n", "                left join (select item_id, product_id from lake_rpc.item_product_mapping) ipm on ipm.item_id = rpp.item_id\n", "                left join (select id, type_id from lake_cms.gr_product) gp on gp.id = ipm.product_id\n", "                left join (select id, name as p_type from lake_cms.gr_product_type) pt on pt.id = gp.type_id\n", "\n", "                    where rpp.id in (select max(id) as id from lake_rpc.product_product where active = 1 and approved = 1 group by item_id)\n", "        )\n", "\n", "            select * from item_details where item_id in {item_id_list}\n", "\n", "    \"\"\"\n", "    return pd.read_sql_query(item_details, redshift)\n", "\n", "\n", "start = time.time()\n", "item_details = item_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "db2897f9-5270-47be-b438-b0a1d846e0e9", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "markdown", "id": "3874df17-d7a7-4189-afbc-eb966a2f76b7", "metadata": {}, "source": ["# inventoty details"]}, {"cell_type": "code", "execution_count": null, "id": "33f9941f-1bf6-403f-a657-0b36755aceb2", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select item_id, outlet_id, sum(quantity) as actual_inv\n", "            from ims.ims_item_inventory\n", "                where active = 1\n", "                    group by 1,2\n", "        )\n", "        \n", "            select * from inv where item_id in {item_id_list} and outlet_id in {fe_outlet_id_list}\n", "    \"\"\"\n", "    return pd.read_sql_query(inventory, presto)\n", "\n", "\n", "start = time.time()\n", "inventory = inventory()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "c45e3b82-3bae-4c5f-b3d7-68b90627a3ef", "metadata": {}, "outputs": [], "source": ["inventory.head()"]}, {"cell_type": "markdown", "id": "d391a5f5-9fe1-4acf-b7c9-4b36a28e74c1", "metadata": {}, "source": ["# inv blocked details"]}, {"cell_type": "code", "execution_count": null, "id": "3721d3b3-049a-4143-994f-ce721538225b", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select item_id, outlet_id,\n", "            sum(case when blocked_type in (1,2,5) then quantity else 0 end) as ttl_blocked_qty\n", "                from ims.ims_item_blocked_inventory\n", "                    where active = 1 and blocked_type in (1,2,5)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from blocked where item_id in {item_id_list} and outlet_id in {fe_outlet_id_list}\n", "    \"\"\"\n", "    return pd.read_sql_query(blocked_inv, presto)\n", "\n", "\n", "start = time.time()\n", "blocked_inv = blocked_inv()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "5b52adc5-9328-48ef-99cc-04f27dbc9ec9", "metadata": {}, "outputs": [], "source": ["blocked_inv.head()"]}, {"cell_type": "markdown", "id": "b7e978ff-4969-45bb-8732-534f24deed34", "metadata": {}, "source": ["# pending putaway"]}, {"cell_type": "code", "execution_count": null, "id": "1f53e058-0aef-4def-8c89-9fd256920d0d", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    pp as\n", "        (select rpc.item_id, outlet_id, sum(quantity) as pending_putaway\n", "            from ims.ims_good_inventory igi\n", "                join rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "                    where igi.active = 1 and igi.inventory_update_type_id in (28,76)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from pp where item_id in {item_id_list} and outlet_id in {fe_outlet_id_list}\n", "    \"\"\"\n", "    return pd.read_sql_query(pen_put, presto)\n", "\n", "\n", "start = time.time()\n", "pen_put = pen_put()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "18969314-b577-4b3b-9e49-033a659d2b29", "metadata": {}, "outputs": [], "source": ["pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a3c0b1a9-ea79-4b48-88a4-73064d4a2e8a", "metadata": {}, "outputs": [], "source": ["adding_inv = pd.merge(final_assortment, inventory, on=[\"item_id\", \"outlet_id\"], how=\"left\").fillna(\n", "    0\n", ")\n", "\n", "adding_blocked = pd.merge(adding_inv, blocked_inv, on=[\"item_id\", \"outlet_id\"], how=\"left\").fillna(\n", "    0\n", ")\n", "\n", "adding_blocked[\"net_inv\"] = np.where(\n", "    adding_blocked[\"actual_inv\"] < 0,\n", "    0,\n", "    (adding_blocked[\"actual_inv\"] - adding_blocked[\"ttl_blocked_qty\"]),\n", ")\n", "\n", "adding_put = pd.merge(adding_blocked, pen_put, on=[\"item_id\", \"outlet_id\"], how=\"left\").fillna(0)\n", "\n", "adding_item_details = pd.merge(adding_put, item_details, on=[\"item_id\"], how=\"left\")\n", "\n", "adding_item_details[\"avai_flag\"] = np.where(adding_item_details[\"net_inv\"] > 0, 1, 0)\n", "\n", "adding_item_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "614839e4-efc7-40aa-9e50-c128fb7927dc", "metadata": {}, "outputs": [], "source": ["adding_item_details = adding_item_details[\n", "    [\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l1\",\n", "        \"p_type\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inv\",\n", "        \"pending_putaway\",\n", "        \"avai_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "1575b545-65f9-4390-8cbc-8b88786edd29", "metadata": {}, "outputs": [], "source": ["adding_item_details = adding_item_details.sort_values(\n", "    [\"zone\", \"city_name\", \"facility_id\"], ascending=[True, True, True]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9c454e39-2ae5-4f38-8d92-293033023c39", "metadata": {}, "outputs": [], "source": ["adding_item_details.shape"]}, {"cell_type": "code", "execution_count": null, "id": "30e59f95-0e5d-4974-8b95-fb5efe65192b", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    adding_item_details,\n", "    sheetid=\"1Yr4UHSc8_90x6AqTD20XjPeAhGyNGvxj1HQ2_uR3tV4\",\n", "    sheetname=\"raw_data\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "57d7b8fc-b17c-482a-a843-564f60f422ec", "metadata": {}, "outputs": [], "source": ["adding_item_details[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "a354f18b-e262-42c6-8f61-c6b8cd2ff900", "metadata": {}, "outputs": [], "source": ["final_updated_at = adding_item_details.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "75c9a3f6-ca3f-4538-8bb1-f21c54d632c0", "metadata": {}, "outputs": [], "source": ["final_updated_at = (\n", "    final_updated_at[[\"updated_at\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "80fac227-4cf1-4080-b56d-394e6b7cf13e", "metadata": {}, "outputs": [], "source": ["final_updated_at"]}, {"cell_type": "code", "execution_count": null, "id": "9ca0efb4-6b4c-4799-b2d0-5232d8280c46", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    final_updated_at,\n", "    sheetid=\"1Yr4UHSc8_90x6AqTD20XjPeAhGyNGvxj1HQ2_uR3tV4\",\n", "    sheetname=\"updated_at\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c1cf2d6a-418e-4aaf-94f5-f0816c3f3497", "metadata": {}, "outputs": [], "source": ["final = adding_item_details[\n", "    [\n", "        \"outlet_id\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"p_type\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inv\",\n", "        \"pending_putaway\",\n", "        \"updated_at\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "b0ed520c-e58d-4d1d-8e09-cec75ca6796e", "metadata": {}, "outputs": [], "source": ["final.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "17c0fd0c-8b41-476f-aad8-79d8f6a7fec9", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"outlet id\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"assortment type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"p type name\"},\n", "    {\"name\": \"actual_inv\", \"type\": \"varchar\", \"description\": \"actual inventory\"},\n", "    {\"name\": \"ttl_blocked_qty\", \"type\": \"varchar\", \"description\": \"blocked quantity\"},\n", "    {\"name\": \"net_inv\", \"type\": \"varchar\", \"description\": \"net inventory\"},\n", "    {\"name\": \"pending_putaway\", \"type\": \"varchar\", \"description\": \"pending put-away\"},\n", "    {\"name\": \"updated_at\", \"type\": \"timestamp\", \"description\": \"date of run\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "82bc5e7e-f457-4d50-9cb0-16ee2d9905e8", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"festive_season_assortment_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"updated_at\", \"outlet_id\", \"item_id\"],\n", "    \"sortkey\": [\"updated_at\", \"outlet_id\", \"item_id\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"load_type\": \"append\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"This table contains list of festive/season assortment availability given in input trackers\",\n", "}\n", "pb.to_redshift(final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "50bf1150-b4bd-43cd-ae34-306c95ab71c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}