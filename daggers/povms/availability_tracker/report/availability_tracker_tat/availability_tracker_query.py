import pandas as pd
import numpy as np
import pencilbox as pb
import os
import time
from datetime import datetime,timedelta
import calendar
import pytz

redshift = pb.get_connection("[Warehouse] Redshift")
    
## To get ARS active dark stores
def get_ars_active_facility():
    facility_id_list_query="""
    SELECT DISTINCT facility_id FROM lake_retail.console_outlet co
    LEFT JOIN metrics.default_cpd_log log ON co.id = log.frontend_outlet_id
    WHERE forecast_run_id = (SELECT forecast_run_id FROM metrics.forecast_job_run ORDER BY updated_at DESC LIMIT 1)
    AND co.active = 1 AND business_type_id = 7
    """  
    return pd.read_sql_query(facility_id_list_query, redshift)

def get_min_max_facility_date_level(facility_id):
    min_max_query=f""" 
    WITH date_range AS (
        SELECT DISTINCT DATE(od.created_at + interval '5.5 Hours') AS date 
        FROM lake_ims.ims_order_details od 
        WHERE od.created_at BETWEEN current_date - 7 AND current_date - 1 
    ),

    combination AS  (
        SELECT DISTINCT item_id, facility_id, dr.date 
        FROM (SELECT DISTINCT item_id, facility_id FROM lake_ars.item_min_max_quantity_log WHERE facility_id IN {*facility_id,}) 
        CROSS JOIN date_range dr
    ),
    
    min_max_base_ranked AS (
        SELECT *, row_rank + 1 AS next_rank
        FROM
        (
            SELECT *, ROW_NUMBER () OVER (PARTITION BY facility_id, item_id ORDER BY date) AS row_rank
            FROM
            (
                SELECT DISTINCT item_id, facility_id, min_quantity, max_quantity, DATE(created_at) AS date
                FROM lake_ars.item_min_max_quantity_log
                WHERE DATE(created_at) BETWEEN current_date - 7 AND current_date - 1 
                AND facility_id IN {*facility_id,} 
            )
        )
    ),
    
    min_max_base AS (
        SELECT b.facility_id, b.item_id, b.min_quantity, b.max_quantity, b.date, 
        CASE WHEN next.date IS NULL THEN current_date::date ELSE next.date::date end AS next_date
        FROM min_max_base_ranked b
        LEFT JOIN min_max_base_ranked next on b.facility_id = next.facility_id and b.item_id = next.item_id and next.row_rank = b.next_rank
    )
    
    SELECT c.item_id, c.facility_id AS outlet, c.date, b.min_quantity, b.max_quantity FROM combination c
    LEFT JOIN min_max_base b ON
    b.item_id=c.item_id AND b.facility_id = c.facility_id AND c.date>=b.date AND c.date<b.next_date
    """
    return pd.read_sql_query(min_max_query, redshift)


def get_current_min_max(facility_id):
    min_max_query=f"""
    SELECT item_id, facility_id, bucket AS curr_bucket, min_quantity AS curr_min_qty, max_quantity AS curr_max_qty 
    FROM lake_ars.item_min_max_quantity WHERE facility_id IN {*facility_id,} 
    """
    return pd.read_sql_query(min_max_query, redshift)

def fetch_all_data(facility_id):
    fetch_all_data = f"""
    with bucket_x_items AS ( 
        SELECT co.facility_id, a.item_id, MAX(CASE WHEN tag_type_id=6 then tag_value END) AS bucket_x FROM lake_rpc.item_outlet_tag_mapping a 
        INNER JOIN lake_retail.console_outlet AS co 
        ON a.outlet_id=co.id and co.active = 1
        WHERE tag_type_id in (1,6) AND a.active=1 AND co.active=1 AND co.facility_id IN {*facility_id,}
        AND a.item_id NOT IN (SELECT DISTINCT id.item_id FROM lake_rpc.item_details id WHERE id.active = 1 AND id.approved = 1 AND id.perishable = 1)
        GROUP BY 1,2
    ),

    max_inv_item AS (
        SELECT rpc_daily.item_id, rpc_daily.facility_id, DATE(order_date) AS date, MAX(actual_quantity) AS max_inv
        FROM consumer.rpc_daily_availability rpc_daily
        INNER JOIN bucket_x_items bx on rpc_daily.facility_id = bx.facility_id and rpc_daily.item_id = bx.item_id and bx.bucket_x='Y'
        WHERE DATE(order_date) BETWEEN DATE(current_date - 7) AND DATE(current_date - 1)
        AND cast(order_date as time) BETWEEN '07:00:00' AND '23:00:00'
        AND rpc_daily.facility_id IN {*facility_id,}
        GROUP BY 1,2,3
    ),
    
    mer_outlet_mapping AS (
        SELECT frontend_merchant_id, backend_merchant_id, pos_outlet_id as outlet_id, facility_id FROM dwh.dim_merchant_outlet_facility_mapping
        WHERE valid_to_utc >= current_date AND is_current = True AND is_mapping_enabled = True
    ),

    cpd_base AS (
        SELECT mom.facility_id, ipm.item_id, DATE(o.install_ts + interval '5.5 Hours') AS date, SUM(quantity) AS cpd 
        FROM lake_oms_bifrost.oms_order o
        INNER JOIN lake_oms_bifrost.oms_merchant M ON o.MERCHANT_ID = M.ID
        INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id
        LEFT JOIN lake_rpc.item_product_mapping ipm ON oi.product_id = ipm.product_id AND ipm.active=1
        INNER JOIN mer_outlet_mapping mom ON m.external_id = mom.frontend_merchant_id
        INNER JOIN bucket_x_items bx on mom.facility_id = bx.facility_id and ipm.item_id = bx.item_id and bx.bucket_x='Y'
        WHERE DATE(o.install_ts + interval '5.5 Hours') BETWEEN DATE(current_date - 7) AND DATE(current_date - 1)
        AND mom.facility_id IN {*facility_id,}
        AND ("type" IS NULL OR "type" IN ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder'))
        GROUP BY 1,2,3
    )

    SELECT m.facility_id, m.date, m.item_id, m.max_inv, cb.cpd FROM max_inv_item m
    LEFT JOIN cpd_base cb ON m.item_id = cb.item_id AND m.facility_id = cb.facility_id AND m.date = cb.date
    ORDER BY 1,3
    """
    return pd.read_sql_query(fetch_all_data, redshift)

def get_facility_city_name_mapping():
    get_fac_city_name=f""" 
    SELECT DISTINCT facility_id, name, city_name FROM lake_retail.console_outlet co 
    LEFT JOIN metrics.default_cpd_log log ON co.id = log.frontend_outlet_id
    WHERE forecast_run_id = (SELECT forecast_run_id FROM metrics.forecast_job_run ORDER BY updated_at DESC LIMIT 1)
    AND co.active = 1
    """
    return pd.read_sql_query(get_fac_city_name, redshift)