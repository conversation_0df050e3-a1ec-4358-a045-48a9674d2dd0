{"cells": [{"cell_type": "code", "execution_count": null, "id": "c2b8efed-95bd-4090-889f-d20dafb6271f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import json\n", "\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "markdown", "id": "8f4c1af2-c5cf-4c9c-86ae-04a4e826469b", "metadata": {}, "source": ["# Queries"]}, {"cell_type": "code", "execution_count": null, "id": "66d92e55-e8cf-4b27-a04a-90e23d1a9cd4", "metadata": {}, "outputs": [], "source": ["def get_ars_active_facility():\n", "    facility_id_list_query = \"\"\"\n", "    SELECT DISTINCT facility_id FROM lake_retail.console_outlet co\n", "    LEFT JOIN metrics.default_cpd_log log ON co.id = log.frontend_outlet_id\n", "    WHERE forecast_run_id = (SELECT forecast_run_id FROM metrics.forecast_job_run ORDER BY updated_at DESC LIMIT 1)\n", "    AND co.active = 1 AND business_type_id = 7\n", "    \"\"\"\n", "    return pd.read_sql_query(facility_id_list_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "f513a730-0b1c-4a4d-b423-41b420b1c476", "metadata": {}, "outputs": [], "source": ["def get_min_max_facility_date_level(facility_id):\n", "    min_max_query = f\"\"\" \n", "    WITH date_range AS (\n", "        SELECT DISTINCT DATE(od.created_at + interval '5.5 Hours') AS date \n", "        FROM lake_ims.ims_order_details od \n", "        WHERE od.created_at BETWEEN current_date - 7 AND current_date - 1 \n", "    ),\n", "\n", "    combination AS  (\n", "        SELECT DISTINCT item_id, facility_id, dr.date \n", "        FROM (SELECT DISTINCT item_id, facility_id FROM lake_ars.item_min_max_quantity_log WHERE facility_id IN {*facility_id,}) \n", "        CROSS JOIN date_range dr\n", "    ),\n", "    \n", "    min_max_base_ranked AS (\n", "        SELECT *, row_rank + 1 AS next_rank\n", "        FROM\n", "        (\n", "            SELECT *, ROW_NUMBER () OVER (PARTITION BY facility_id, item_id ORDER BY date) AS row_rank\n", "            FROM\n", "            (\n", "                SELECT DISTINCT item_id, facility_id, min_quantity, max_quantity, DATE(created_at) AS date\n", "                FROM lake_ars.item_min_max_quantity_log\n", "                WHERE DATE(created_at) BETWEEN current_date - 7 AND current_date - 1 \n", "                AND facility_id IN {*facility_id,} \n", "            )\n", "        )\n", "    ),\n", "    \n", "    min_max_base AS (\n", "        SELECT b.facility_id, b.item_id, b.min_quantity, b.max_quantity, b.date, \n", "        CASE WHEN next.date IS NULL THEN current_date::date ELSE next.date::date end AS next_date\n", "        FROM min_max_base_ranked b\n", "        LEFT JOIN min_max_base_ranked next on b.facility_id = next.facility_id and b.item_id = next.item_id and next.row_rank = b.next_rank\n", "    )\n", "    \n", "    SELECT c.item_id, c.facility_id AS outlet, c.date, b.min_quantity, b.max_quantity FROM combination c\n", "    LEFT JOIN min_max_base b ON\n", "    b.item_id=c.item_id AND b.facility_id = c.facility_id AND c.date>=b.date AND c.date<b.next_date\n", "    \"\"\"\n", "    return pd.read_sql_query(min_max_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "id": "26776157-15d4-406e-b369-8e89bcded63d", "metadata": {}, "outputs": [], "source": ["def get_current_min_max(facility_id):\n", "    min_max_query = f\"\"\"\n", "    SELECT item_id, facility_id, bucket AS curr_bucket, min_quantity AS curr_min_qty, max_quantity AS curr_max_qty \n", "    FROM lake_ars.item_min_max_quantity WHERE facility_id IN {*facility_id,} \n", "    \"\"\"\n", "    return pd.read_sql_query(min_max_query, redshift)\n", "\n", "\n", "def fetch_all_data(facility_id):\n", "    fetch_all_data = f\"\"\"\n", "    with bucket_x_items AS ( \n", "        SELECT co.facility_id, a.item_id, MAX(CASE WHEN tag_type_id=6 then tag_value END) AS bucket_x FROM lake_rpc.item_outlet_tag_mapping a \n", "        INNER JOIN lake_retail.console_outlet AS co \n", "        ON a.outlet_id=co.id and co.active = 1\n", "        WHERE tag_type_id in (1,6) AND a.active=1 AND co.active=1 AND co.facility_id IN {*facility_id,}\n", "        AND a.item_id NOT IN (SELECT DISTINCT id.item_id FROM lake_rpc.item_details id WHERE id.active = 1 AND id.approved = 1 AND id.perishable = 1)\n", "        GROUP BY 1,2\n", "    ),\n", "\n", "    max_inv_item AS (\n", "        SELECT rpc_daily.item_id, rpc_daily.facility_id, DATE(order_date) AS date, MAX(actual_quantity) AS max_inv\n", "        FROM consumer.rpc_daily_availability rpc_daily\n", "        INNER JOIN bucket_x_items bx on rpc_daily.facility_id = bx.facility_id and rpc_daily.item_id = bx.item_id and bx.bucket_x='Y'\n", "        WHERE DATE(order_date) BETWEEN DATE(current_date - 7) AND DATE(current_date - 1)\n", "        AND cast(order_date as time) BETWEEN '07:00:00' AND '23:00:00'\n", "        AND rpc_daily.facility_id IN {*facility_id,}\n", "        GROUP BY 1,2,3\n", "    ),\n", "    \n", "    mer_outlet_mapping AS (\n", "        SELECT frontend_merchant_id, backend_merchant_id, pos_outlet_id as outlet_id, facility_id FROM dwh.dim_merchant_outlet_facility_mapping\n", "        WHERE valid_to_utc >= current_date AND is_current = True AND is_mapping_enabled = True\n", "    ),\n", "\n", "    cpd_base AS (\n", "        SELECT mom.facility_id, ipm.item_id, DATE(o.install_ts + interval '5.5 Hours') AS date, SUM(quantity) AS cpd \n", "        FROM lake_oms_bifrost.oms_order o\n", "        INNER JOIN lake_oms_bifrost.oms_merchant M ON o.MERCHANT_ID = M.ID\n", "        INNER JOIN lake_oms_bifrost.oms_order_item oi ON oi.order_id=o.id\n", "        LEFT JOIN lake_rpc.item_product_mapping ipm ON oi.product_id = ipm.product_id AND ipm.active=1\n", "        INNER JOIN mer_outlet_mapping mom ON m.external_id = mom.frontend_merchant_id\n", "        INNER JOIN bucket_x_items bx on mom.facility_id = bx.facility_id and ipm.item_id = bx.item_id and bx.bucket_x='Y'\n", "        WHERE DATE(o.install_ts + interval '5.5 Hours') BETWEEN DATE(current_date - 7) AND DATE(current_date - 1)\n", "        AND mom.facility_id IN {*facility_id,}\n", "        AND (\"type\" IS NULL OR \"type\" IN ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder'))\n", "        GROUP BY 1,2,3\n", "    )\n", "\n", "    SELECT m.facility_id, m.date, m.item_id, m.max_inv, cb.cpd FROM max_inv_item m\n", "    LEFT JOIN cpd_base cb ON m.item_id = cb.item_id AND m.facility_id = cb.facility_id AND m.date = cb.date\n", "    ORDER BY 1,3\n", "    \"\"\"\n", "    return pd.read_sql_query(fetch_all_data, redshift)\n", "\n", "\n", "def get_facility_city_name_mapping():\n", "    get_fac_city_name = f\"\"\" \n", "    SELECT DISTINCT facility_id, name, city_name FROM lake_retail.console_outlet co \n", "    LEFT JOIN metrics.default_cpd_log log ON co.id = log.frontend_outlet_id\n", "    WHERE forecast_run_id = (SELECT forecast_run_id FROM metrics.forecast_job_run ORDER BY updated_at DESC LIMIT 1)\n", "    AND co.active = 1\n", "    \"\"\"\n", "    return pd.read_sql_query(get_fac_city_name, redshift)"]}, {"cell_type": "markdown", "id": "386627be-e4d8-4c02-b483-8bdb6bb978a3", "metadata": {}, "source": ["# Fetch Data"]}, {"cell_type": "code", "execution_count": null, "id": "a72154a6-e2ff-4445-a7a1-29cf44b33161", "metadata": {"tags": []}, "outputs": [], "source": ["facility = get_ars_active_facility()\n", "facility_list = facility[\"facility_id\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "3e6c168a-6676-4ed1-89f4-e0c2b09ced9b", "metadata": {}, "outputs": [], "source": ["min_max_df = get_current_min_max(facility_list)"]}, {"cell_type": "code", "execution_count": null, "id": "a463cea7-585a-4ebd-bc45-5d935b17d2b8", "metadata": {"tags": []}, "outputs": [], "source": ["data_df = fetch_all_data(facility_list)"]}, {"cell_type": "code", "execution_count": null, "id": "0bcd7882-7693-4dc3-9461-8502408e246d", "metadata": {}, "outputs": [], "source": ["city_outlet_mapping_df = get_facility_city_name_mapping()"]}, {"cell_type": "markdown", "id": "685a2b75-6193-4b53-9c76-c4ba56278042", "metadata": {}, "source": ["# Data Manipulation"]}, {"cell_type": "code", "execution_count": null, "id": "a9240845-3833-4acb-8c52-669cf36f86c6", "metadata": {}, "outputs": [], "source": ["min_max_facility_df = min_max_df.groupby([\"facility_id\"]).agg({\"curr_max_qty\": \"sum\"}).reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "71333edb-d9a9-4cab-8076-bac3838d6934", "metadata": {}, "outputs": [], "source": ["min_max_facility_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "23d4f59e-3622-41b6-a71d-9f52481c6caa", "metadata": {}, "outputs": [], "source": ["data_df = data_df.fillna(0)"]}, {"cell_type": "code", "execution_count": null, "id": "496aa55e-982a-4343-aef6-8ee174c3a873", "metadata": {}, "outputs": [], "source": ["data_df.head(14)"]}, {"cell_type": "code", "execution_count": null, "id": "ff0cee58-0c68-404d-b50e-8026bc144fe5", "metadata": {}, "outputs": [], "source": ["data_df[\"av_flag\"] = np.where(data_df[\"max_inv\"] > 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "475aa4b1-28df-455e-b0f6-3f671215efcf", "metadata": {}, "outputs": [], "source": ["data_log_df = (\n", "    data_df.groupby([\"facility_id\", \"date\"])\n", "    .agg({\"max_inv\": \"sum\", \"cpd\": \"sum\", \"av_flag\": \"sum\", \"item_id\": \"count\"})\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f7aeb45e-b5e6-4d81-b04d-16265221b05f", "metadata": {}, "outputs": [], "source": ["data_log_df = data_log_df.rename(columns={\"item_id\": \"total_items\", \"av_flag\": \"avail_items\"})"]}, {"cell_type": "code", "execution_count": null, "id": "47750544-5c0c-4614-8ed2-7f7d490f5124", "metadata": {}, "outputs": [], "source": ["data_log_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2d4da408-2410-4026-8474-291b76689d6e", "metadata": {}, "outputs": [], "source": ["data_log_df[\"doi\"] = data_log_df[\"max_inv\"] / data_log_df[\"cpd\"]\n", "data_log_df[\"availability\"] = data_log_df[\"avail_items\"] / data_log_df[\"total_items\"]"]}, {"cell_type": "code", "execution_count": null, "id": "3294a7e6-aece-4023-928b-11da077a219e", "metadata": {}, "outputs": [], "source": ["data_log_df_1 = data_log_df[data_log_df[\"cpd\"] > 0]"]}, {"cell_type": "code", "execution_count": null, "id": "72d0b3a2-d225-42be-be53-7da88540367d", "metadata": {}, "outputs": [], "source": ["data_final = pd.merge(data_log_df_1, min_max_facility_df, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "130b803a-8de7-40c5-9d29-78a5aff4d172", "metadata": {}, "outputs": [], "source": ["data_final.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "b6c594b1-ee94-47c7-9985-fbb525f481cf", "metadata": {}, "outputs": [], "source": ["tracker_df = pd.merge(data_final, city_outlet_mapping_df, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "id": "b555aed0-2c35-44c1-b6ad-6074b58740e0", "metadata": {}, "outputs": [], "source": ["tracker_df.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "64373f25-b8d7-4e1b-8da8-c6d6c96f7069", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    tracker_df,\n", "    sheetid=\"1wR6j-ZlU0h8_ggyio2R7HtF6uDtI7Q4emqjAQ_FsOpQ\",\n", "    sheetname=\"raw_data\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "65991eb1-6649-49b7-bda1-74fc6637647b", "metadata": {}, "outputs": [], "source": ["combined_tracker = tracker_df.pivot_table(\n", "    index=[\"facility_id\", \"name\", \"curr_max_qty\"],\n", "    columns=[\"date\"],\n", "    values=[\"availability\", \"doi\", \"total_items\", \"avail_items\", \"cpd\", \"max_inv\"],\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "f357d90f-5615-489b-8a10-35e4418d23d5", "metadata": {}, "outputs": [], "source": ["combined_tracker"]}, {"cell_type": "code", "execution_count": null, "id": "e571bc68-d9ff-416d-9cc6-380cd3e27235", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    combined_tracker,\n", "    sheetid=\"1wR6j-ZlU0h8_ggyio2R7HtF6uDtI7Q4emqjAQ_FsOpQ\",\n", "    sheetname=\"raw_data_combined\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}