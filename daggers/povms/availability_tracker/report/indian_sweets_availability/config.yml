alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: indian_sweets_availability
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RR39TSTZ
path: povms/availability_tracker/report/indian_sweets_availability
paused: true
pool: povms_pool
project_name: availability_tracker
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 */1 * * *
  start_date: '2022-11-07T11:07:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 4
