{"cells": [{"cell_type": "code", "execution_count": null, "id": "38707979-8ebc-4830-9d9b-4dc5f9564392", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import math\n", "import warnings\n", "import time\n", "\n", "\n", "presto = pb.get_connection(\"[Warehouse] Presto\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "markdown", "id": "fa520701-8d84-46d1-afce-dc312c1eda18", "metadata": {}, "source": ["# outlet details"]}, {"cell_type": "code", "execution_count": null, "id": "1fd44651-2184-44cd-8b9b-b6ea90d68717", "metadata": {}, "outputs": [], "source": ["def outlets():\n", "    outlets = \"\"\"\n", "    \n", "    select z.zone,\n", "        rcl.name as city_name,\n", "        om.outlet_id as hot_outlet_id, om.outlet_name, \n", "        om.facility_id, pf.internal_facility_identifier as facility_name,\n", "        case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id,\n", "        rco.business_type_id,\n", "        case when rco.business_type_id !=7 then 'be' else 'fe' end as taggings\n", "        \n", "            from lake_po.physical_facility_outlet_mapping om\n", "            \n", "            left join (select id, tax_location_id,\n", "                case when id = 581 then 12 else business_type_id end as business_type_id\n", "                    from lake_retail.console_outlet\n", "                        ) rco on rco.id = om.outlet_id\n", "            \n", "            left join (select pf.* from lake_po.physical_facility pf\n", "                join (select facility_id, max(updated_at) as updated_at from lake_po.physical_facility group by 1\n", "                        ) pf2 on pf2.facility_id = pf.facility_id and pf2.updated_at = pf.updated_at\n", "                        ) pf on pf.facility_id = om.facility_id\n", "            \n", "            left join (select distinct warehouse_id, cloud_store_id from lake_retail.warehouse_outlet_mapping \n", "                where active = 1\n", "                        ) wom on wom.warehouse_id = om.outlet_id\n", "            \n", "            left join lake_retail.console_location rcl on rcl.id = rco.tax_location_id\n", "            \n", "            left join (select distinct facility_id, zone from metrics.outlet_zone_mapping where business_type_id in (1,12,7,19,20)\n", "                        ) z on z.facility_id = om.facility_id\n", "            \n", "                where rco.business_type_id in (1,12,7,19,20,21) and om.outlet_id not in (0,1739)\n", "                and om.active = 1 and ars_active = 1 and is_primary = 1\n", "                and om.outlet_name not like '%%SSC%%'\n", "                and om.outlet_name not like '%%MODI%%'\n", "                and om.outlet_name not like '%%hot ff%%'\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(outlets, redshift)\n", "\n", "\n", "start = time.time()\n", "outlets = outlets()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "353f6e85-8fea-496b-b00e-00ea034892f7", "metadata": {}, "outputs": [], "source": ["outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1b7cf4bc-69ac-4e75-9a49-541dd3f0ef0f", "metadata": {}, "outputs": [], "source": ["# all inv outlets\n", "all_inv_outlet_id = list(outlets[\"inv_outlet_id\"].unique())\n", "all_inv_outlet_id = tuple(all_inv_outlet_id)\n", "# len(all_inv_outlet_id)\n", "\n", "\n", "# all hot outlets\n", "all_hot_outlet_id = list(outlets[\"hot_outlet_id\"].unique())\n", "all_hot_outlet_id = tuple(all_hot_outlet_id)\n", "# len(all_hot_outlet_id)\n", "\n", "# all hot outlets_name\n", "all_hot_name = outlets[[\"hot_outlet_id\", \"inv_outlet_id\", \"facility_id\", \"facility_name\"]].rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "        \"inv_outlet_id\": \"be_inv_outlet_id\",\n", "        \"facility_id\": \"be_facility_id\",\n", "        \"facility_name\": \"be_facility_name\",\n", "    }\n", ")\n", "\n", "# all_hot_name['be_hot_outlet_id'] = all_hot_name['be_hot_outlet_id'].astype(object)\n", "# all_hot_name.head()\n", "\n", "# all facility\n", "all_facility_id = list(outlets[\"facility_id\"].unique())\n", "all_facility_id = tuple(all_facility_id)\n", "# len(all_facility_id)\n", "\n", "# frontend outlets\n", "frontend_outlet_details = outlets[outlets[\"taggings\"] == \"fe\"].reset_index().drop(columns={\"index\"})\n", "fe_facility_details = list(frontend_outlet_details[\"hot_outlet_id\"].unique())\n", "fe_facility_details = tuple(fe_facility_details)\n", "# len(fe_facility_details)\n", "\n", "\n", "# backend inv outlets\n", "be_inv_outlet_id = outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", "be_inv_outlet_id = list(be_inv_outlet_id[\"inv_outlet_id\"].unique())\n", "be_inv_outlet_id = tuple(be_inv_outlet_id)\n", "# len(be_inv_outlet_id)\n", "\n", "\n", "# backend hot outlets\n", "be_hot_outlet_id = outlets[outlets[\"taggings\"] == \"be\"].reset_index().drop(columns={\"index\"})\n", "be_hot_outlet_id = list(be_hot_outlet_id[\"hot_outlet_id\"].unique())\n", "be_hot_outlet_id = tuple(be_hot_outlet_id)\n", "# len(be_hot_outlet_id)"]}, {"cell_type": "markdown", "id": "1caef3ae-08e1-4e35-bc86-277db2406962", "metadata": {}, "source": ["# cluster_mapping"]}, {"cell_type": "code", "execution_count": null, "id": "5fe9ca79-9466-4cf2-9d74-0192dbdfc027", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "\n", "cluster_mapping = pb.from_sheets(\n", "    \"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"city_mapping\", clear_cache=True\n", ")\n", "cluster_mapping = cluster_mapping[[\"city_name\", \"cluster_mapping\"]]\n", "\n", "cluster_mapping = cluster_mapping[\n", "    ~((cluster_mapping[\"city_name\"] == \"\") | (cluster_mapping[\"cluster_mapping\"] == \"\"))\n", "]\n", "\n", "cluster_mapping.dropna(inplace=True)\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "44931b6a-b5de-4a92-9403-85ea1ba6699c", "metadata": {}, "outputs": [], "source": ["cluster_mapping.head()"]}, {"cell_type": "markdown", "id": "5e85f2a4-573e-4291-82da-430e5846afa1", "metadata": {}, "source": ["# assortment 1"]}, {"cell_type": "code", "execution_count": null, "id": "27f5f959-92b9-4c64-a916-892da757de34", "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "\n", "new_assortment = pb.from_sheets(\n", "    \"1XE5ypk84w10ZiGjnoEAurRCRM-xC7IP9muZ3BtYWqOQ\", \"assortment\", clear_cache=True\n", ")\n", "new_assortment = new_assortment[[\"cluster_mapping\", \"item_id\"]]\n", "\n", "new_assortment = new_assortment[\n", "    ~((new_assortment[\"item_id\"] == \"\") | (new_assortment[\"cluster_mapping\"] == \"\"))\n", "]\n", "new_assortment[\"item_id\"] = new_assortment[\"item_id\"].astype(int)\n", "\n", "new_assortment.dropna(inplace=True)\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "5e520f63-0ae2-490a-b7a6-80c2689f5b62", "metadata": {}, "outputs": [], "source": ["new_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bc35f8a4-e7e3-4ebd-b414-d341bcc9ecd3", "metadata": {}, "outputs": [], "source": ["item_id_list = list(new_assortment[\"item_id\"].unique())\n", "item_id_list = tuple(item_id_list)\n", "len(item_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "7316860e-a619-4a79-913f-d84e7fb50a39", "metadata": {}, "outputs": [], "source": ["final_city_frontend_outlets = pd.merge(\n", "    cluster_mapping, new_assortment, on=[\"cluster_mapping\"], how=\"inner\"\n", ")\n", "final_city_frontend_outlets.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60d0b63d-7ce0-4227-a8f6-6155aa703bdd", "metadata": {}, "outputs": [], "source": ["city_assortment = pd.merge(\n", "    final_city_frontend_outlets, frontend_outlet_details, on=[\"city_name\"], how=\"inner\"\n", ")\n", "\n", "city_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21aea1fa-3778-4fa9-af93-8d3fbd4cc854", "metadata": {}, "outputs": [], "source": ["city_assortment.shape"]}, {"cell_type": "markdown", "id": "cb742908-bdc9-4ac6-bae1-b4cd79e01a41", "metadata": {}, "source": ["# item details"]}, {"cell_type": "code", "execution_count": null, "id": "c2ff177a-798a-4cf9-8cac-aefd0276032f", "metadata": {}, "outputs": [], "source": ["def item_details():\n", "    item_details = f\"\"\"\n", "    \n", "    with\n", "    item_details as\n", "        (select rpp.item_id, (rpp.name || ' ' || variant_description) as item_name, lower(p_type) as p_type,\n", "            \n", "            l0, pb.manufacturer_id, pm.name as manufacturer_name\n", "\n", "                    from lake_view_rpc.product_product rpp\n", "\n", "                        join lake_view_rpc.item_category_details cd on cd.item_id = rpp.item_id\n", "\n", "                        left join (select item_id, product_id from lake_view_rpc.item_product_mapping) ipm on ipm.item_id = rpp.item_id\n", "                        left join (select id, type_id from lake_view_cms.gr_product) gp on gp.id = ipm.product_id\n", "                        left join (select id, name as p_type from lake_view_cms.gr_product_type) pt on pt.id = gp.type_id\n", "\n", "                        left join lake_view_rpc.product_brand pb on pb.id = rpp.brand_id\n", "                        left join lake_view_rpc.product_manufacturer pm on pm.id = pb.manufacturer_id\n", "\n", "                            where rpp.id in (select max(id) as id from lake_view_rpc.product_product where active = 1 and approved = 1 group by item_id)\n", "        )\n", "\n", "            select * from item_details where item_id in {item_id_list}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(item_details, presto)\n", "\n", "\n", "start = time.time()\n", "item_details = item_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "d0c733e4-18c4-498c-ba20-9f0bb9384526", "metadata": {}, "outputs": [], "source": ["item_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "996c74a6-0b83-4e73-9355-e019117c5445", "metadata": {}, "outputs": [], "source": ["final_assortment = pd.merge(city_assortment, item_details, on=[\"item_id\"], how=\"inner\")\n", "\n", "final_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9c773810-80ed-4f3e-a681-73cdd409b98c", "metadata": {}, "outputs": [], "source": ["final_assortment.shape"]}, {"cell_type": "markdown", "id": "504fb777-df92-4444-91bf-a0c55c9775be", "metadata": {}, "source": ["# active assortment details"]}, {"cell_type": "code", "execution_count": null, "id": "b04cca93-e4ee-4f32-97cd-7b5df22f1464", "metadata": {}, "outputs": [], "source": ["def active_assortment():\n", "    active_assortment = f\"\"\"\n", "    \n", "    with\n", "    active_assortment as\n", "        (select item_id, facility_id, master_assortment_substate_id as status\n", "            from lake_view_rpc.product_facility_master_assortment\n", "                where active = 1\n", "        )\n", "        \n", "            select * from active_assortment where item_id in {item_id_list} and facility_id in {all_facility_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(active_assortment, presto)\n", "\n", "\n", "start = time.time()\n", "active_assortment = active_assortment()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "1dcc913a-84d2-4748-8414-1fc10059b026", "metadata": {}, "outputs": [], "source": ["active_assortment.head()"]}, {"cell_type": "markdown", "id": "fb25af76-127e-4493-b1c8-579942d0bab7", "metadata": {}, "source": ["# tea taggings details"]}, {"cell_type": "code", "execution_count": null, "id": "60a5b742-73b4-4ecc-991b-acc6aaabed6c", "metadata": {}, "outputs": [], "source": ["def tea_taggings():\n", "    tea_taggings = f\"\"\"\n", "    \n", "    with\n", "    tea_taggings as\n", "        (select item_id, outlet_id as hot_outlet_id, tag_value as backend_outlet_id\n", "            from lake_view_rpc.item_outlet_tag_mapping\n", "                where tag_type_id = 8 and active = 1\n", "        )\n", "        \n", "            select * from tea_taggings where item_id in {item_id_list} and hot_outlet_id in {all_hot_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(tea_taggings, presto)\n", "\n", "\n", "start = time.time()\n", "tea_taggings = tea_taggings()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "02137687-c3c6-484c-aa0f-b3bdbeab3186", "metadata": {"tags": []}, "outputs": [], "source": ["tea_taggings.head()"]}, {"cell_type": "markdown", "id": "a946caee-f481-4853-b288-9a5228e3fe3c", "metadata": {}, "source": ["# inventory details"]}, {"cell_type": "code", "execution_count": null, "id": "6e37ac7e-d8e0-4cca-9958-9f2be53392dc", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select item_id, outlet_id as hot_outlet_id, sum(quantity) as actual_inv\n", "            from lake_view_ims.ims_item_inventory\n", "                where active = 1\n", "                    group by 1,2\n", "        )\n", "        \n", "            select * from inv where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(inventory, presto)\n", "\n", "\n", "start = time.time()\n", "inventory = inventory()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "77f970b8-9b90-4bc8-aa15-0b0226744510", "metadata": {}, "outputs": [], "source": ["inventory.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e62f7818-f8fc-4c00-8245-a0358b8c81f1", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "be_inv.head()"]}, {"cell_type": "markdown", "id": "b640bf6b-77c1-4abc-9e8e-c68474a117d1", "metadata": {}, "source": ["# inv blocked details"]}, {"cell_type": "code", "execution_count": null, "id": "c6addd65-be79-4d72-bd06-2e0f31dea69b", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select item_id, outlet_id as hot_outlet_id,\n", "            sum(case when blocked_type in (1,2,5) then quantity else 0 end) as ttl_blocked_qty\n", "                from lake_view_ims.ims_item_blocked_inventory\n", "                    where active = 1 and blocked_type in (1,2,5)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from blocked where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(blocked_inv, presto)\n", "\n", "\n", "start = time.time()\n", "blocked_inv = blocked_inv()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "fb010cb7-9347-40c3-8213-ae0534cab6e5", "metadata": {}, "outputs": [], "source": ["blocked_inv.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1cd3860b-725f-45f9-b7ca-d485809fd3e5", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")\n", "be_blocked.head()"]}, {"cell_type": "markdown", "id": "b5539d8b-185f-4fe9-b66f-4fc3f27a9e29", "metadata": {}, "source": ["# pending putaway"]}, {"cell_type": "code", "execution_count": null, "id": "6dbb4c07-71a0-4e20-a7bf-b0d801485110", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    pp as\n", "        (select rpc.item_id, outlet_id as hot_outlet_id, sum(quantity) as pending_putaway\n", "            from lake_view_ims.ims_good_inventory igi\n", "                join lake_view_rpc.product_product rpc on rpc.upc = igi.upc_id and igi.variant_id = rpc.variant_id\n", "                    where igi.active = 1 and igi.inventory_update_type_id in (28,76)\n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from pp where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(pen_put, presto)\n", "\n", "\n", "start = time.time()\n", "pen_put = pen_put()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "4ec6c247-17ed-41fc-920d-528d21744b73", "metadata": {}, "outputs": [], "source": ["pen_put.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a696ef4e-1715-4fbb-b9cc-a05d592062ef", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")\n", "be_pen_put.head()"]}, {"cell_type": "markdown", "id": "5fd0444c-bc72-43fb-b7c8-6561a1777442", "metadata": {}, "source": ["# open po details"]}, {"cell_type": "code", "execution_count": null, "id": "930b2854-04eb-4386-aeae-5c7bbc5eefe9", "metadata": {}, "outputs": [], "source": ["def open_po():\n", "    open_po = f\"\"\"\n", "    \n", "    with\n", "    open_po_details as\n", "        (select po_number, po_scheduled_date, po.outlet_id as hot_outlet_id, item_id, remaining_quantity as open_po_quantity\n", "\n", "            from lake_view_po.purchase_order_items poi\n", "\n", "                join lake_view_po.purchase_order po on po.id = poi.po_id\n", "\n", "                join lake_view_po.purchase_order_status ppos on ppos.po_id = po.id\n", "\n", "                left join (select po_id_id, date(schedule_date_time + interval '330' minute) as po_scheduled_date\n", "                    from lake_view_po.po_schedule) ps on ps.po_id_id = po.id\n", "\n", "                    where (ppos.po_state_id in (2,3,13,14,15) or is_multiple_grn = 1)\n", "                        and ppos.po_state_id not in (4,5,8,10) and po.po_type_id != 11\n", "                        and (po.created_at between cast(current_date as timestamp)-interval '60' day - interval '330' minute and\n", "                            cast(current_date as timestamp)+interval '1' day - interval '330' minute)\n", "        )\n", "        \n", "            select * from open_po_details where item_id in {item_id_list} and hot_outlet_id in {all_hot_outlet_id}\n", "    \"\"\"\n", "    return pd.read_sql_query(open_po, presto)\n", "\n", "\n", "start = time.time()\n", "open_po = open_po()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "77741b97-32fa-4440-9196-5b4a724b0680", "metadata": {}, "outputs": [], "source": ["open_po.head()"]}, {"cell_type": "code", "execution_count": null, "id": "41199631-60a2-49fe-86e7-58700684707e", "metadata": {}, "outputs": [], "source": ["open_po[\"po_scheduled_date\"] = pd.to_datetime(open_po[\"po_scheduled_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "79c8f16c-fa1a-48ad-9273-177cb91d6236", "metadata": {}, "outputs": [], "source": ["ttl_open_po = (\n", "    open_po.groupby([\"hot_outlet_id\", \"item_id\"]).agg({\"open_po_quantity\": \"sum\"}).reset_index()\n", ")\n", "ttl_open_po.head()"]}, {"cell_type": "code", "execution_count": null, "id": "434b3bd4-0ea0-4097-b653-256a80463ace", "metadata": {}, "outputs": [], "source": ["be_open_po = (\n", "    open_po.groupby([\"hot_outlet_id\", \"item_id\"])\n", "    .agg({\"open_po_quantity\": \"sum\"})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "            \"open_po_quantity\": \"be_open_po_quantity\",\n", "        }\n", "    )\n", ")\n", "be_open_po.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2040257c-9043-4e00-a489-80d4d706d6ce", "metadata": {}, "outputs": [], "source": ["scheduling_open_po = (\n", "    open_po.groupby([\"hot_outlet_id\", \"item_id\"])\n", "    .agg({\"po_scheduled_date\": np.min, \"open_po_quantity\": np.sum})\n", "    .reset_index()\n", "    .rename(\n", "        columns={\n", "            \"hot_outlet_id\": \"be_hot_outlet_id\",\n", "            \"open_po_quantity\": \"be_scheduled_po_qty\",\n", "        }\n", "    )\n", ")\n", "\n", "scheduling_open_po.head()"]}, {"cell_type": "code", "execution_count": null, "id": "251965c3-e58a-4b14-b4f4-10a43fd45c73", "metadata": {}, "outputs": [], "source": ["# scheduling_open_po[(scheduling_open_po['item_id'] == 10007824) & (scheduling_open_po['be_hot_outlet_id'] == 483) & (scheduling_open_po['po_scheduled_date'].notnull())]"]}, {"cell_type": "markdown", "id": "cadf455e-e9cb-4b94-a698-0006a6af8d25", "metadata": {}, "source": ["# open sto details"]}, {"cell_type": "code", "execution_count": null, "id": "4279d272-a632-40b3-8857-be5f0568f103", "metadata": {}, "outputs": [], "source": ["def sto_details():\n", "    sto_details = f\"\"\"\n", "    \n", "    with\n", "    inward_invoice_details as\n", "        (select vendor_invoice_id from lake_view_ims.ims_inward_invoice\n", "            where cast(insert_ds_ist as date) >= current_date - interval '40' day\n", "                and source_type in (2)\n", "        ),\n", "\n", "    billed_invoice as\n", "        (select grofers_order_id as sto_id, id, outlet_id, \n", "            case when iid.vendor_invoice_id is null then 'raised' else 'grn_done' end as invoice_status\n", "\n", "                from lake_view_pos.pos_invoice pi\n", "\n", "                    left join inward_invoice_details iid on iid.vendor_invoice_id = pi.invoice_id\n", "\n", "                    where cast(insert_ds_ist as date) >= current_date - interval '40' day\n", "                        and invoice_type_id in (5,14,16)\n", "        ),\n", "\n", "    invoice_item_details as\n", "        (select item_id, outlet_id, sto_id, sum(quantity) as billed_qty\n", "\n", "            from lake_view_pos.pos_invoice_product_details pd\n", "\n", "                join (select item_id, upc from lake_view_rpc.product_product\n", "                    where id in (select max(id) as id from lake_rpc.product_product where active = 1 and approved = 1 group by upc)\n", "                        ) rpp on rpp.upc = pd.upc_id\n", "\n", "                join billed_invoice bi on bi.id = pd.invoice_id and invoice_status in ('raised')\n", "\n", "                    where insert_ds_ist >= cast((current_date - interval '41' day) as varchar)\n", "\n", "                        group by 1,2,3\n", "        ),\n", "\n", "    sto_id as\n", "        (select sd.merchant_outlet_id, sd.sto_id, invoice_status from lake_view_ims.ims_sto_details sd\n", "            left join billed_invoice bi on cast(bi.sto_id as bigint) = sd.sto_id\n", "                where created_at between cast(current_date as timestamp)-interval '40' day - interval '330' minute and\n", "                    cast(current_date as timestamp)+interval '1' day - interval '330' minute\n", "                    and sto_state in (2,3) and invoice_status not in ('grn_done')\n", "        ),\n", "\n", "    sto_item_details as\n", "        (select sto_id, item_id, reserved_quantity as sto_quantity\n", "\n", "            from lake_view_po.sto_items\n", "\n", "                where created_at between cast(current_date as timestamp)-interval '40' day - interval '330' minute and\n", "                    cast(current_date as timestamp)+interval '1' day - interval '330' minute\n", "        ),\n", "\n", "    final as\n", "        (select sid.sto_id, si.merchant_outlet_id, sid.item_id, sid.sto_quantity, iid.billed_qty,\n", "            case when iid.sto_id is null then sid.sto_quantity else iid.billed_qty end as open_sto_qty\n", "\n", "                from sto_item_details sid\n", "\n", "                    join sto_id si on si.sto_id = sid.sto_id\n", "                    left join invoice_item_details iid on cast(iid.sto_id as int) = sid.sto_id and iid.item_id = sid.item_id\n", "        )\n", "\n", "            select merchant_outlet_id as hot_outlet_id, item_id, sum(open_sto_qty) as open_sto_qty\n", "\n", "                from final\n", "                    \n", "                    where item_id in {item_id_list} and merchant_outlet_id in {all_inv_outlet_id}\n", "                    \n", "                        group by 1,2\n", "    \"\"\"\n", "    return pd.read_sql_query(sto_details, presto)\n", "\n", "\n", "start = time.time()\n", "sto_details = sto_details()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "9090b6fe-c32c-4e79-8eaa-86188a6b917a", "metadata": {}, "outputs": [], "source": ["sto_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cf31caad-2885-4c57-a0de-078e45572585", "metadata": {}, "outputs": [], "source": ["be_sto = sto_details.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"open_sto_qty\": \"be_open_sto_qty\"}\n", ")\n", "be_sto.head()"]}, {"cell_type": "markdown", "id": "170e5643-4b06-4e97-aeac-c4d4bd5c9d59", "metadata": {}, "source": ["# min max details"]}, {"cell_type": "code", "execution_count": null, "id": "942c8975-0ebf-4ea6-b075-9e6addc7e2ae", "metadata": {}, "outputs": [], "source": ["def min_max():\n", "    min_max = f\"\"\"\n", "    \n", "    with\n", "    min_max as\n", "        (select facility_id, item_id, min_quantity, max_quantity\n", "        \n", "            from lake_view_ars.item_min_max_quantity\n", "        )\n", "        \n", "            select * from min_max where item_id in {item_id_list} and facility_id in {all_facility_id}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(min_max, presto)\n", "\n", "\n", "start = time.time()\n", "min_max = min_max()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "a49eac23-e45e-4527-8ee7-b95dd7263db2", "metadata": {}, "outputs": [], "source": ["min_max.head()"]}, {"cell_type": "markdown", "id": "75b70268-0831-44dd-840f-1bb008448d7e", "metadata": {}, "source": ["# sales details"]}, {"cell_type": "code", "execution_count": null, "id": "88c65f90-cd7e-4879-a543-3f4bcd41f3e2", "metadata": {}, "outputs": [], "source": ["def sales():\n", "    sales = f\"\"\"\n", "    \n", "    with\n", "    sales as\n", "        (select\n", "            date(oi.install_ts + interval '5.5 Hours') as date_,\n", "            od.hot_outlet_id,\n", "            oi.product_id,\n", "            pl.item_id,\n", "            oi.selling_price,\n", "            (oi.selling_price*1.000/pl.multiplier+0.000) as item_selling_price,\n", "            oi.order_id,\n", "            pl.multiplier as combo_qty,\n", "            (oi.quantity * combo_qty) as qty,\n", "            ((oi.quantity * combo_qty) - (oi.cancelled_quantity * combo_qty)) as f_ordered_qty\n", "\n", "                from lake_oms_bifrost.oms_order_item oi\n", "\n", "                    left join (select distinct item_id, product_id, coalesce(multiplier,1) as multiplier \n", "                        from consumer.mv_tbl__demand_forecasting_view_it_pd_log__0\n", "                                ) pl on pl.product_id = oi.product_id\n", "\n", "                    left join lake_oms_bifrost.oms_order oo on oo.id = oi.order_id and oo.type in ('RetailForwardOrder','RetailSuborder','')\n", "\n", "                    join (select distinct outlet as hot_outlet_id, ancestor from lake_ims.ims_order_details \n", "                        join lake_retail.console_outlet rco on rco.id = outlet and business_type_id in (7)\n", "                            where status_id not in (3,4,5)\n", "                                ) od on od.ancestor = oi.order_id\n", "\n", "                    where\n", "                        (oi.install_ts) between ('2022-09-25' || ' 00:00:00')::timestamp - interval '5.5 Hours' and (current_date || ' 23:59:59')::timestamp - interval '5.5 Hours'\n", "                            and pl.item_id is not null and f_ordered_qty >0\n", "        ),\n", "    \n", "    final_sales as\n", "        (select date_, hot_outlet_id, item_id, sum(f_ordered_qty) as sales_qty\n", "        \n", "            from sales\n", "            \n", "                group by 1,2,3\n", "        )\n", "            \n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then sales_qty end) as today_sales,\n", "                sum(case when date_ = current_date -1 then sales_qty end) as l1_ttl_sales,\n", "                sum(case when date_ between current_date - 3 and current_date -1 then sales_qty end) as l3_ttl_sales,\n", "                sum(case when date_ between current_date - 7 and current_date -1 then sales_qty end) as l7_ttl_sales,\n", "                sum(case when date_ between '2022-09-25' and current_date -1 then sales_qty end) as ttl_sales\n", "\n", "                    from final_sales\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "                    \n", "                            group by 1,2\n", "\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(sales, redshift)\n", "\n", "\n", "start = time.time()\n", "sales = sales()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "f2c44386-6ccb-44d3-b3f9-4203b5e00f10", "metadata": {}, "outputs": [], "source": ["sales.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d251ae40-b0d4-4949-9056-856466bf42b1", "metadata": {}, "outputs": [], "source": ["sales[\"item_id\"] = sales[\"item_id\"].astype(int)"]}, {"cell_type": "markdown", "id": "3c4679a3-5683-4c8e-8818-e2d22186fda7", "metadata": {}, "source": ["# dump details"]}, {"cell_type": "code", "execution_count": null, "id": "6bf3ec1c-e144-4eac-b342-fd580670f75b", "metadata": {}, "outputs": [], "source": ["def dump():\n", "    dump = f\"\"\"\n", "    \n", "    with\n", "    dump as\n", "        (select date(pos_timestamp + interval '330' minute) as date_, \n", "            outlet_id as hot_outlet_id, item_id, sum(il.\"delta\") as dump_quantity\n", "\n", "                from lake_view_ims.ims_inventory_log il\n", "\n", "                    join (select distinct item_id, variant_id from lake_view_rpc.product_product) rpp on rpp.variant_id = il.variant_id\n", "\n", "                        where cast(insert_ds_ist as date) >= cast('2022-09-25' as date)\n", "                            and inventory_update_type_id in (11,12,13,64,87,88,89,7,33,9,34,63,67)\n", "\n", "                                group by 1,2,3\n", "        )\n", "        \n", "            select hot_outlet_id, item_id,\n", "                sum(case when date_ = current_date then dump_quantity end) as today_dump,\n", "                sum(case when date_ = (current_date - interval '1' day) then dump_quantity end) as l1_ttl_dump,\n", "                sum(case when date_ between (current_date - interval '3' day) and (current_date - interval '1' day) then dump_quantity end) as l3_ttl_dump,\n", "                sum(case when date_ between (current_date - interval '7' day) and (current_date - interval '1' day) then dump_quantity end) as l7_ttl_dump,\n", "                sum(case when date_ between cast('2022-09-25' as date) and (current_date - interval '1' day) then dump_quantity end) as ttl_dump_quantity\n", "\n", "                    from dump\n", "                    \n", "                        where item_id in {item_id_list} and hot_outlet_id in {all_inv_outlet_id}\n", "                    \n", "                            group by 1,2\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(dump, presto)\n", "\n", "\n", "start = time.time()\n", "dump = dump()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "cc16af3f-3788-4afd-a4a7-20ec06ab5f39", "metadata": {}, "outputs": [], "source": ["dump.head()"]}, {"cell_type": "code", "execution_count": null, "id": "891b2d31-af6f-4c51-b8d4-0759273f5ba0", "metadata": {}, "outputs": [], "source": ["be_dump = dump.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"today_dump\": \"be_today_dump\",\n", "        \"l1_ttl_dump\": \"be_l1_ttl_dump\",\n", "        \"l3_ttl_dump\": \"be_l3_ttl_dump\",\n", "        \"l7_ttl_dump\": \"be_l7_ttl_dump\",\n", "        \"ttl_dump_quantity\": \"be_ttl_dump_quantity\",\n", "    }\n", ")\n", "be_dump.head()"]}, {"cell_type": "markdown", "id": "5c147126-f081-45c0-8ab2-71e5602ae514", "metadata": {}, "source": ["# city item penetration"]}, {"cell_type": "code", "execution_count": null, "id": "ff3c60a8-e314-42cd-87e1-ac851a77f696", "metadata": {}, "outputs": [], "source": ["def city_weights():\n", "    city_weights = f\"\"\"\n", "    \n", "    with\n", "    city_weights as\n", "    (select city as city_name, item_id, weights\n", "    \n", "            from metrics.city_item_cart_penetration\n", "                    \n", "                    where updated_at = (select max(updated_at) as updated_at from metrics.city_item_cart_penetration)\n", "    )\n", "    \n", "        select * from city_weights\n", "        \n", "            where item_id in {item_id_list}\n", "    \n", "    \"\"\"\n", "    return pd.read_sql_query(city_weights, redshift)\n", "\n", "\n", "start = time.time()\n", "city_weights = city_weights()\n", "\n", "end = time.time()\n", "print(end - start)"]}, {"cell_type": "code", "execution_count": null, "id": "8bdc94de-0f22-424d-a67c-922403201082", "metadata": {}, "outputs": [], "source": ["city_weights.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c51c21e0-3937-435a-8961-0ff79b6304c4", "metadata": {}, "outputs": [], "source": ["final_frontend_assortment = pd.merge(\n", "    final_assortment, active_assortment, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")\n", "\n", "final_frontend_assortment[\"assortment_status\"] = np.where(\n", "    final_frontend_assortment[\"status\"].isnull(),\n", "    \"not_part_of_assortment\",\n", "    \"part_of_assortment\",\n", ")\n", "\n", "final_frontend_assortment = pd.merge(\n", "    final_frontend_assortment, tea_taggings, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "final_frontend_assortment[\"be_hot_outlet_id\"] = (\n", "    final_frontend_assortment[\"backend_outlet_id\"].fillna(0).astype(int)\n", ")\n", "\n", "final_frontend_assortment = pd.merge(\n", "    final_frontend_assortment, all_hot_name, on=[\"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "final_frontend_assortment[\"be_inv_outlet_id\"] = (\n", "    final_frontend_assortment[\"be_inv_outlet_id\"].fillna(0).astype(int)\n", ")\n", "final_frontend_assortment[\"be_facility_id\"] = (\n", "    final_frontend_assortment[\"be_facility_id\"].fillna(0).astype(int)\n", ")\n", "final_frontend_assortment[\"be_facility_name\"] = final_frontend_assortment[\n", "    \"be_facility_name\"\n", "].fillna(0)\n", "\n", "final_frontend_assortment = final_frontend_assortment[\n", "    [\n", "        \"zone\",\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"hot_outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"l0\",\n", "        \"p_type\",\n", "        \"status\",\n", "        \"assortment_status\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "    ]\n", "]\n", "\n", "final_frontend_assortment.head()"]}, {"cell_type": "code", "execution_count": null, "id": "18daac88-4b66-4b40-9bd0-a9eb13338838", "metadata": {}, "outputs": [], "source": ["final_frontend_assortment[\n", "    [\"be_hot_outlet_id\", \"be_inv_outlet_id\", \"be_facility_id\", \"be_facility_name\"]\n", "].drop_duplicates()"]}, {"cell_type": "markdown", "id": "d4cd708c-e0ca-4b4c-87c0-c59ed769864a", "metadata": {}, "source": ["# adding frontend inventory, sto, po"]}, {"cell_type": "code", "execution_count": null, "id": "22d19bc2-32c5-45d2-873a-0933a760a9a3", "metadata": {}, "outputs": [], "source": ["adding_actual_qty = pd.merge(\n", "    final_frontend_assortment, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_blocked_qty = pd.merge(\n", "    adding_actual_qty, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_blocked_qty[\"net_inv\"] = np.where(\n", "    adding_blocked_qty[\"actual_inv\"] < 0,\n", "    0,\n", "    (adding_blocked_qty[\"actual_inv\"] - adding_blocked_qty[\"ttl_blocked_qty\"]),\n", ")\n", "\n", "adding_pen_put_qty = pd.merge(\n", "    adding_blocked_qty, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_sto_qty = pd.merge(\n", "    adding_pen_put_qty, sto_details, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_open_po_qty = pd.merge(\n", "    adding_sto_qty, ttl_open_po, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_open_po_qty.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3e0ddc01-7362-4054-bd63-7846e23ae9ec", "metadata": {}, "outputs": [], "source": ["adding_min_max = pd.merge(adding_open_po_qty, min_max, on=[\"item_id\", \"facility_id\"], how=\"left\")\n", "\n", "adding_sales = pd.merge(adding_min_max, sales, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\")\n", "\n", "adding_dump = pd.merge(adding_sales, dump, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\")\n", "adding_dump.head()"]}, {"cell_type": "markdown", "id": "77b368df-af8a-41ee-9a02-0acdfcc99bfa", "metadata": {}, "source": ["# adding backend inventory, sto, po"]}, {"cell_type": "code", "execution_count": null, "id": "4c21316f-7849-40ae-ac8c-396471269e8e", "metadata": {}, "outputs": [], "source": ["adding_backend_inv = pd.merge(\n", "    adding_dump, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_backend_blocked = pd.merge(\n", "    adding_backend_inv, be_blocked, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_backend_blocked[\"be_net_inv\"] = np.where(\n", "    adding_backend_blocked[\"be_actual_inv\"] < 0,\n", "    0,\n", "    (adding_backend_blocked[\"be_actual_inv\"] - adding_backend_blocked[\"be_ttl_blocked_qty\"]),\n", ")\n", "\n", "adding_backend_pen_put = (\n", "    pd.merge(\n", "        adding_backend_blocked,\n", "        be_pen_put,\n", "        on=[\"item_id\", \"be_inv_outlet_id\"],\n", "        how=\"left\",\n", "    )\n", "    .fillna(0)\n", "    .drop(columns={\"be_ttl_blocked_qty\"})\n", ")\n", "\n", "adding_backend_sto_qty = pd.merge(\n", "    adding_backend_pen_put, be_sto, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_backend_open_po = pd.merge(\n", "    adding_backend_sto_qty, be_open_po, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ").<PERSON>na(0)\n", "\n", "adding_backend_dump = pd.merge(\n", "    adding_backend_open_po, be_dump, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "adding_backend_dump[\n", "    [\n", "        \"be_today_dump\",\n", "        \"be_l1_ttl_dump\",\n", "        \"be_l3_ttl_dump\",\n", "        \"be_l7_ttl_dump\",\n", "        \"be_ttl_dump_quantity\",\n", "    ]\n", "] = (\n", "    adding_backend_dump[\n", "        [\n", "            \"be_today_dump\",\n", "            \"be_l1_ttl_dump\",\n", "            \"be_l3_ttl_dump\",\n", "            \"be_l7_ttl_dump\",\n", "            \"be_ttl_dump_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(float)\n", ")\n", "\n", "adding_backend_dump.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3f30565d-be62-4dd7-9b98-712462e8f81f", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    adding_backend_dump,\n", "    sheetid=\"1XE5ypk84w10ZiGjnoEAurRCRM-xC7IP9muZ3BtYWqOQ\",\n", "    sheetname=\"availability_raw\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d6c7d39f-1158-41df-a9c7-24ec1c0cce6b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}