{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d90a11e0-cedd-4644-81ff-fdf9b846b674", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "import warnings\n", "import math\n", "\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "36a26ad1-7b62-419b-97e6-242f4cadea3f", "metadata": {}, "outputs": [], "source": ["!pip install pillow==8.3.1\n", "!pip install --upgrade --force-reinstall reportlab==3.6.1"]}, {"cell_type": "code", "execution_count": null, "id": "928e3812-abd1-4703-8e44-774543600b76", "metadata": {}, "outputs": [], "source": ["from reportlab.lib.styles import getSampleStyleSheet\n", "from reportlab.platypus import *\n", "\n", "import random\n", "from reportlab.lib import colors\n", "\n", "PATH_OUT = \"./\""]}, {"cell_type": "code", "execution_count": null, "id": "cf85114e-dea3-4894-8abc-82f92f181cef", "metadata": {}, "outputs": [], "source": ["CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "90162760-8517-478d-bacc-b65dd2914427", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "3439a22e-a59b-4bff-a2aa-9df04060d3d8", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "markdown", "id": "aac6e55f-cd23-45f7-bf90-32ac6810c2ef", "metadata": {}, "source": ["### Assortment Input"]}, {"cell_type": "code", "execution_count": null, "id": "e10ec067-70b0-4a06-9374-f7e6b018501c", "metadata": {}, "outputs": [], "source": ["temp = pb.from_sheets(\"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"Fresh_Sweets\")\n", "\n", "# temp[\"active\"] = temp[\"active\"].astype(int)\n", "temp[\"item_id\"] = temp[\"item_id\"].astype(int)\n", "# temp[\"go_live_date\"] = pd.to_datetime(temp[\"go_live_date\"])\n", "items = tuple(list(temp[\"item_id\"].unique()))\n", "temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "81089b36-bef2-4dac-87b1-1d326f79bbc7", "metadata": {}, "outputs": [], "source": ["# first_live_date = temp.agg({\"go_live_date\": \"min\"}).reset_index().iloc[:, 1][0]\n", "first_live_date = pd.to_datetime(\"2021-09-01\")"]}, {"cell_type": "code", "execution_count": null, "id": "a0d848a2-eab9-4dd3-a455-d9dfdfecdf8a", "metadata": {}, "outputs": [], "source": ["ars_active_ds_query = f\"\"\"\n", "SELECT \n", "    distinct\n", "        pfom.facility_id,\n", "        pfom.city_id\n", "FROM\n", "    lake_view_po.physical_facility_outlet_mapping as pfom\n", "Inner join\n", "    lake_view_retail.console_outlet co on co.facility_id = pfom.facility_id and business_type_id = 7\n", "WHERE\n", "    pfom.active = 1 AND\n", "    pfom.ars_active = 1\"\"\"\n", "ars_active_ds_df = read_sql_query(ars_active_ds_query, CON_PRESTO)\n", "ars_active_ds_df = ars_active_ds_df[[\"facility_id\", \"city_id\"]].drop_duplicates()\n", "\n", "city_mapping_query = f\"\"\"\n", "SELECT \n", "    distinct id as city_id, name as city\n", "FROM\n", "    lake_view_retail.console_location\"\"\"\n", "city_map_df = read_sql_query(city_mapping_query, CON_PRESTO)\n", "\n", "ars_active_ds_df = pd.merge(ars_active_ds_df, city_map_df, on=[\"city_id\"], how=\"left\")\n", "ars_active_ds_df.head(1)"]}, {"cell_type": "markdown", "id": "1359101c-7f51-4260-8095-b861ab4a32bb", "metadata": {}, "source": ["## Set up Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "775067a6-72be-43d9-a9c3-1c8a35796571", "metadata": {}, "outputs": [], "source": ["# base_df = pd.merge(temp, ars_active_ds_df, on=[\"city\"], how=\"left\")\n", "# base_df.head(1)"]}, {"cell_type": "markdown", "id": "28d414e5-e04a-40cb-8546-3100efc4c4ec", "metadata": {}, "source": ["### Facility Outlet Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "1911ec66-ae57-4498-81ca-9a59fcfddbff", "metadata": {}, "outputs": [], "source": ["facility_mapping_query = f\"\"\"\n", "SELECT \n", "    distinct id as outlet_id, facility_id\n", "FROM\n", "    lake_view_retail.console_outlet\n", "WHERE\n", "    active = 1\"\"\"\n", "facility_mapping_df = read_sql_query(facility_mapping_query, CON_PRESTO)"]}, {"cell_type": "markdown", "id": "0c3c2948-fa59-4bcc-8984-66e85580452f", "metadata": {}, "source": ["## <PERSON><PERSON>, Ptype Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "6249014e-f802-4136-9fed-34454181b2c2", "metadata": {}, "outputs": [], "source": ["product_attributes_query = f\"\"\"\n", "with categories AS\n", "(\n", "    SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "              WHEN C1.NAME = C.name THEN C2.name\n", "              ELSE C1.name\n", "          END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "    FROM lake_cms.gr_product P\n", "    INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "    AND PCM.IS_PRIMARY=TRUE\n", "    INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "    INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "    INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id\n", "),\n", "\n", "category_pre AS\n", "(\n", "    SELECT item_id,\n", "          product_id,\n", "          cat.l0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.product_type,\n", "          cat.manf\n", "    FROM lake_rpc.item_product_mapping rpc\n", "    INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "    AND rpc.offer_id IS NULL\n", "    AND rpc.item_id IS NOT NULL\n", "    AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl AS\n", "(\n", "    SELECT item_id,\n", "          max(l0) AS l0,\n", "          max(l1) AS l1,\n", "          max(l2) AS l2,\n", "          max(product_type) AS ptype,\n", "          max(manf) AS manf\n", "    FROM category_pre\n", "    GROUP BY 1\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    ptype_tbl\n", "\"\"\"\n", "product_attributes_df = read_sql_query(product_attributes_query, CON_REDSHIFT)"]}, {"cell_type": "markdown", "id": "556f4e3d-2a03-4d18-b96b-169c43b08ba7", "metadata": {}, "source": ["### Fetch Tea Tagging"]}, {"cell_type": "code", "execution_count": null, "id": "2f328f18-507f-42a6-818d-cad4fcf12dac", "metadata": {}, "outputs": [], "source": ["type(items)"]}, {"cell_type": "code", "execution_count": null, "id": "4eadf78f-ad0f-47a0-8a46-0a80f813a1b7", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with tea_tagging as \n", "(\n", "    select \n", "        cl.name as city,\n", "        tea.item_id,\n", "        item.name as item_name,\n", "        co.facility_id as frontend_facility_id,\n", "        co2.name as frontend_facility_name,\n", "        co1.facility_id as backend_facility_id,\n", "        co3.name as backend_facility_name\n", "    from \n", "        lake_view_rpc.item_outlet_tag_mapping tea\n", "    left join  \n", "        lake_view_retail.console_outlet co on tea.outlet_id=co.id and co.business_type_id=7 and co.active=1\n", "    left join\n", "        lake_view_retail.console_location cl on co.tax_location_id = cl.id\n", "    left join\n", "        lake_view_retail.console_outlet co1 on cast(tea.tag_value as int)=cast(co1.id as int) and co1.business_type_id!=7 and co1.active=1\n", "    left join\n", "        lake_view_crates.facility co2 on co.facility_id=co2.id \n", "    left join\n", "        lake_view_crates.facility co3 on co1.facility_id=co3.id \n", "    left join\n", "        lake_view_rpc.item_details item  on tea.item_id=item.item_id\n", "    where tag_type_id=8 and tea.active=1 and tea.item_id in  {items}\n", ")\n", "select * from tea_tagging\"\"\"\n", "\n", "tea_tag = read_sql_query(query, CON_PRESTO)\n", "# tea_tag.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bdf8ec1d-fb3b-484f-a6bb-966f694eb609", "metadata": {}, "outputs": [], "source": ["## backend frontend mapping\n", "\n", "query = f\"\"\"\n", "    SELECT \n", "        distinct\n", "        pfom.facility_id as frontend_facility_id, \n", "        bfom.facility_id as backend_facility_id\n", "    FROM \n", "        lake_view_po.bulk_facility_outlet_mapping bfom\n", "    INNER JOIN \n", "    lake_view_po.physical_facility_outlet_mapping pfom ON bfom.outlet_id=pfom.outlet_id\n", "    LEFT JOIN lake_view_crates.facility cf on cf.id = bfom.facility_id \n", "    WHERE bfom.active = True\n", "    AND pfom.active = 1\n", "    AND pfom.ars_active = 1\"\"\"\n", "\n", "active_backends = read_sql_query(query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "cf35a878-51fb-490c-abb0-aec581dd158d", "metadata": {}, "outputs": [], "source": ["tea_tag = pd.merge(\n", "    tea_tag,\n", "    active_backends,\n", "    how=\"inner\",\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ecae3eea-be7d-4713-8f11-093796ab26e0", "metadata": {}, "outputs": [], "source": ["tea_tag.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c9c5dff2-c6bb-408b-9b7d-34cc137d9c45", "metadata": {}, "outputs": [], "source": ["tea_tag = tea_tag[\n", "    (tea_tag[\"frontend_facility_id\"] != 1182)\n", "    & (tea_tag[\"frontend_facility_id\"] != 1028)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "964741d0-e000-46e2-aa39-3a04c86a33a3", "metadata": {}, "outputs": [], "source": ["tea_tag.columns"]}, {"cell_type": "markdown", "id": "2e3599c0-9e55-4519-81e8-fbccb8ce499e", "metadata": {}, "source": ["### Sales Data"]}, {"cell_type": "code", "execution_count": null, "id": "ec8429a2-d84d-4711-ab55-88a3af7c8cb7", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", " with base as\n", "(\n", "    SELECT \n", "        date(od.created_at  + interval '330' Minute) as date, ancestor, outlet AS outlet_id, oi.item_id, sum(oi.quantity) as quantity\n", "    FROM \n", "        lake_view_ims.ims_order_details od\n", "    LEFT JOIN \n", "        lake_view_ims.ims_order_items oi on od.id = oi.order_details_id\n", "    WHERE \n", "        status_id in (1,2)\n", "    AND \n", "        oi.item_id in {items}\n", "    AND\n", "        od.insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    AND\n", "        oi.insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    GROUP BY \n", "        1,2,3,4\n", "),\n", "\n", "GMV_base as\n", "(\n", "    SELECT \n", "        cast(ancestor as var<PERSON>r) as ancestor, item_id, avg(GMV) as GMV\n", "    FROM \n", "    (\n", "        SELECT \n", "            oi.order_id as ancestor,\n", "            oi.product_id, pl.item_id, \n", "            (oi.selling_price*1.000/pl.multiplier) AS item_selling_price, \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS f_ordered_qty,\n", "            (oi.selling_price*1.000/pl.multiplier) * ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS GMV\n", "    \n", "        FROM \n", "            lake_view_oms_bifrost.oms_order_item oi\n", "        LEFT JOIN \n", "                (\n", "                    SELECT \n", "                        DISTINCT\n", "                            ipr.product_id, \n", "                            case \n", "                                when\n", "                                    ipr.item_id is null \n", "                                then ipom_0.item_id else ipr.item_id end as item_id,\n", "                            case \n", "                                when\n", "                                    ipr.item_id is not null\n", "                                then \n", "                                    COALESCE(ipom.multiplier,1)\n", "                                else\n", "                                    COALESCE(ipom_0.multiplier,1)\n", "                                end AS multiplier \n", "                    FROM \n", "                        lake_rpc.item_product_mapping ipr\n", "                    left join\n", "                        redshift.dwh.dim_item_product_offer_mapping ipom\n", "                    on\n", "                        ipr.product_id = ipom.product_id \n", "                    and\n", "                            ipr.item_id = ipom.item_id\n", "                    \n", "                    left join\n", "                        redshift.dwh.dim_item_product_offer_mapping ipom_0\n", "                    on\n", "                        ipr.product_id = ipom_0.product_id\n", "\n", "                ) pl ON pl.product_id = oi.product_id\n", "        WHERE \n", "            pl.item_id IS NOT NULL \n", "        AND \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) > 0\n", "        AND\n", "            insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    ) order_qty\n", "    WHERE\n", "        item_id in {items}\n", "    AND\n", "        ancestor in (select distinct cast(ancestor as bigint) as ancestor from base)\n", "    group by 1,2\n", ")\n", "\n", "\n", ", sales_with_GMV as \n", "(SELECT \n", "    b.date, \n", "    co.facility_id,\n", "    b.item_id, \n", "    count(distinct b.ancestor) as orders,\n", "    sum(quantity) as quantity,\n", "    sum(gmv) as gmv\n", "FROM base b\n", "LEFT JOIN\n", "    GMV_base gb \n", "ON\n", "    b.ancestor = gb.ancestor and  b.item_id = gb.item_id\n", "left join \n", "    lake_retail.console_outlet co on co.id = b.outlet_id and co.active = 1\n", "where  date >= cast('{first_live_date}' as timestamp)\n", "group by 1,2,3     \n", "),\n", "\n", "\n", "current_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as current_day_qty_sold, sum(gmv) as current_day_gmv, sum(orders) as current_day_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_1_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_1_qty_sold, sum(gmv) as t_1_gmv, sum(orders) as t_1_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date = date(current_timestamp + interval '330' minute) - interval '1' day --and date(getdate() + interval '330' Minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_7_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_7_qty_sold, sum(gmv) as t_7_gmv, sum(orders) as t_7_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute) - interval '7' day\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "till_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as qty_sold_till_date, sum(gmv) as gmv_till_date, sum(orders) as carts_till_date\n", "    FROM\n", "        sales_with_GMV\n", "    GROUP BY\n", "        1, 2\n", ")\n", "\n", "SELECT\n", "    td.facility_id as frontend_facility_id, td.item_id,\n", "    current_day_qty_sold, current_day_gmv, current_day_carts,\n", "    t_1_qty_sold, t_1_gmv, t_1_carts,\n", "    t_7_qty_sold, t_7_gmv, t_7_carts,\n", "    qty_sold_till_date, gmv_till_date, carts_till_date\n", "FROM\n", "    till_day td\n", "LEFT JOIN\n", "    current_day cd\n", "ON\n", "    cd.item_id = td.item_id and cd.facility_id = td.facility_id\n", "LEFT JOIN\n", "    t_1_day t1d\n", "ON\n", "    td.item_id = t1d.item_id and td.facility_id = t1d.facility_id\n", "LEFT JOIN\n", "    t_7_day t7d\n", "ON\n", "    td.item_id = t7d.item_id and td.facility_id = t7d.facility_id\n", "\"\"\"\n", "\n", "sales = read_sql_query(query, CON_PRESTO)\n", "## Change 29th"]}, {"cell_type": "markdown", "id": "27dba309-76a8-4f7e-bcae-26356f389f8a", "metadata": {}, "source": ["### Fetch Dark Store Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "718bee2d-f965-4b5f-8bd0-d7c0220a0157", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with DS_inventory  AS\n", "  (SELECT \n", "         distinct\n", "          a.item_id,\n", "          inv_outlet_id,\n", "          outlet_name,\n", "          facility_id,\n", "          facility_name,\n", "          case when (actual_quantity - blocked_quantity) < 0 then 0 else (actual_quantity - blocked_quantity) end as current_stock\n", "   FROM\n", "     (SELECT DISTINCT \n", "            a.item_id,\n", "            r.inv_outlet_id,\n", "            r.outlet_name,\n", "            r.facility_id,\n", "            cf.name AS facility_name,\n", "            (a.quantity) AS actual_quantity,\n", "            sum(CASE WHEN blocked_type IN (1,2,5) THEN b.quantity ELSE 0 END) AS blocked_quantity\n", "      FROM lake_view_ims.ims_item_inventory AS a\n", "      INNER JOIN\n", "        (select \n", "            om.outlet_id as hot_outlet_id, \n", "            om.outlet_name, \n", "            om.facility_id,\n", "            case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id\n", "         from lake_view_po.physical_facility_outlet_mapping om\n", "       left join (select id, tax_location_id,\n", "         case when id = 581 then 12 else business_type_id end as business_type_id\n", "       from lake_view_retail.console_outlet) rco on rco.id = om.outlet_id and rco.business_type_id=7\n", "       left join \n", "            (select distinct warehouse_id, cloud_store_id from lake_view_retail.warehouse_outlet_mapping \n", "                where active = 1) wom on wom.warehouse_id = om.outlet_id\n", "        where rco.business_type_id in (7) and om.outlet_id not in (0)\n", "        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "        and om.outlet_name not like '%%SSC%%'\n", "        and om.outlet_name not like '%%MODI%%'\n", "        and om.outlet_name not like '%%hot ff%%'\n", "        and om.outlet_name not like '%%CIA%%'\n", "        ) r ON a.outlet_id = r.inv_outlet_id\n", "      LEFT JOIN lake_view_ims.ims_item_blocked_inventory b ON a.outlet_id = b.outlet_id\n", "      AND a.item_id = b.item_id\n", "      LEFT JOIN lake_view_crates.facility cf ON r.facility_id = cf.id\n", "    where a.item_id in {items}\n", "      GROUP BY 1,\n", "               2,\n", "               3,4,5,6) a\n", "   )\n", "select * from DS_inventory\"\"\"\n", "\n", "DS_inventory = read_sql_query(query, CON_PRESTO)"]}, {"cell_type": "markdown", "id": "3dc21149-f262-4880-94d6-c88379400491", "metadata": {}, "source": ["### Backend Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "e94a72b7-efd3-4463-9bd7-e2f79bb5a4dc", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with DS_inventory  AS\n", "  (SELECT \n", "         distinct\n", "          a.item_id,\n", "          inv_outlet_id,\n", "          outlet_name,\n", "          facility_id,\n", "          facility_name,\n", "          case when (actual_quantity - blocked_quantity) < 0 then 0 else (actual_quantity - blocked_quantity) end as current_stock\n", "   FROM\n", "     (SELECT DISTINCT \n", "            a.item_id,\n", "            r.inv_outlet_id,\n", "            r.outlet_name,\n", "            r.facility_id,\n", "            cf.name AS facility_name,\n", "            (a.quantity) AS actual_quantity,\n", "            sum(CASE WHEN blocked_type IN (1,2,5) THEN b.quantity ELSE 0 END) AS blocked_quantity\n", "      FROM lake_view_ims.ims_item_inventory AS a\n", "      INNER JOIN\n", "        (select \n", "            om.outlet_id as hot_outlet_id, \n", "            om.outlet_name, \n", "            om.facility_id,\n", "            case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id\n", "         from lake_view_po.physical_facility_outlet_mapping om\n", "       left join (select id, tax_location_id,\n", "         case when id = 581 then 12 else business_type_id end as business_type_id\n", "       from lake_view_retail.console_outlet) rco on rco.id = om.outlet_id\n", "       left join \n", "            (select distinct warehouse_id, cloud_store_id from lake_view_retail.warehouse_outlet_mapping \n", "                where active = 1) wom on wom.warehouse_id = om.outlet_id\n", "        where rco.business_type_id not in (7) and om.outlet_id not in (0)\n", "        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "        and om.outlet_name not like '%%SSC%%'\n", "        and om.outlet_name not like '%%MODI%%'\n", "        and om.outlet_name not like '%%hot ff%%'\n", "        and om.outlet_name not like '%%CIA%%'\n", "        ) r ON a.outlet_id = r.inv_outlet_id\n", "      LEFT JOIN lake_view_ims.ims_item_blocked_inventory b ON a.outlet_id = b.outlet_id\n", "      AND a.item_id = b.item_id\n", "      LEFT JOIN lake_view_crates.facility cf ON r.facility_id = cf.id\n", "    where a.item_id in {items}\n", "      GROUP BY 1,\n", "               2,\n", "               3,4,5,6) a\n", "   )\n", "select item_id, facility_id, facility_name, sum(current_stock) as current_stock from DS_inventory group by 1,2,3\"\"\"\n", "\n", "backend_inventory = read_sql_query(query, CON_PRESTO)\n", "\n", "## Change 29th"]}, {"cell_type": "markdown", "id": "8fb939c4-658c-4f34-9170-ecbaa60c5789", "metadata": {}, "source": ["### Final DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "eac08e12-f9f7-433b-961d-b43897a1e057", "metadata": {}, "outputs": [], "source": ["# backend_inventory[backend_inventory['facility_id']==1398]"]}, {"cell_type": "code", "execution_count": null, "id": "315b9d0a-5d0a-4be4-b59e-da86e385ea47", "metadata": {}, "outputs": [], "source": ["## backend stock\n", "tea_tag = (\n", "    pd.merge(\n", "        tea_tag,\n", "        backend_inventory[[\"item_id\", \"facility_id\", \"current_stock\"]],\n", "        left_on=[\"item_id\", \"backend_facility_id\"],\n", "        right_on=[\"item_id\", \"facility_id\"],\n", "        how=\"left\",\n", "    )\n", "    .drop(columns={\"facility_id\"})\n", "    .rename(columns={\"current_stock\": \"backend_stock\"})\n", ")\n", "tea_tag.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3d73961f-8faf-4621-abd1-69b1a689b621", "metadata": {}, "outputs": [], "source": ["## frontend stock\n", "\n", "tea_tag = (\n", "    pd.merge(\n", "        tea_tag,\n", "        DS_inventory[[\"item_id\", \"facility_id\", \"current_stock\"]],\n", "        left_on=[\"item_id\", \"frontend_facility_id\"],\n", "        right_on=[\"item_id\", \"facility_id\"],\n", "        how=\"left\",\n", "    )\n", "    .drop(columns={\"facility_id\"})\n", "    .rename(columns={\"current_stock\": \"frontend_stock\"})\n", ")\n", "\n", "tea_tag.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c7d52f39-964f-4309-8331-58828bb7a13f", "metadata": {}, "outputs": [], "source": ["tea_tag = pd.merge(\n", "    tea_tag,\n", "    sales[[\"frontend_facility_id\", \"item_id\", \"current_day_gmv\", \"gmv_till_date\"]],\n", "    on=[\"item_id\", \"frontend_facility_id\"],\n", "    how=\"left\",\n", ").rename(\n", "    columns={\n", "        \"backend_stock\": \"Backend Stock\",\n", "        \"frontend_stock\": \"DS Stock\",\n", "        \"current_day_gmv\": \"GMV Today\",\n", "        \"gmv_till_date\": \"GMV Till Date\",\n", "    }\n", ")\n", "tea_tag.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "75af1157-7f1b-498f-a534-a9dc4cfc9942", "metadata": {}, "outputs": [], "source": ["final = tea_tag[\n", "    [\n", "        \"city\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"backend_facility_name\",\n", "        \"frontend_facility_name\",\n", "        \"Backend Stock\",\n", "        \"DS Stock\",\n", "        \"GMV Today\",\n", "        \"GMV Till Date\",\n", "    ]\n", "]\n", "\n", "for i in [\"Backend Stock\", \"DS Stock\", \"GMV Today\", \"GMV Till Date\"]:\n", "    final[i] = final[i].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "cd6f7c98-cc61-458e-83ba-ea454a4fe064", "metadata": {}, "outputs": [], "source": ["final"]}, {"cell_type": "code", "execution_count": null, "id": "cf844fe8-b64d-4176-a506-e74eae0433cb", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final, \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\", \"Fresh Sweets\")"]}, {"cell_type": "code", "execution_count": null, "id": "87353c1d-ff08-4653-b389-bad2a362958f", "metadata": {}, "outputs": [], "source": ["final[\"available_flag\"] = np.where(final[\"DS Stock\"] > 0, 1, 0)\n", "final[\"count\"] = 1\n", "final[\"backend_available_flag\"] = np.where(final[\"Backend Stock\"] > 0, 1, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "46497054-12a4-4a8e-b131-37423dc4ab6f", "metadata": {}, "outputs": [], "source": ["final1 = (\n", "    final.groupby([\"city\", \"item_id\", \"item_name\"])\n", "    .agg(\n", "        {\n", "            \"Backend Stock\": \"sum\",\n", "            \"DS Stock\": \"sum\",\n", "            \"GMV Today\": \"sum\",\n", "            \"GMV Till Date\": \"sum\",\n", "            \"available_flag\": \"sum\",\n", "            \"count\": \"sum\",\n", "            \"backend_available_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7360ef69-dc4b-4e1b-823f-02141fd2de27", "metadata": {}, "outputs": [], "source": ["final1[\"fe_availability\"] = (final1[\"available_flag\"] / final1[\"count\"]) * 100\n", "final1[\"be_availability\"] = (final1[\"backend_available_flag\"] / final1[\"count\"]) * 100"]}, {"cell_type": "code", "execution_count": null, "id": "9e298a68-c5b2-4e59-a346-679405ce1bab", "metadata": {}, "outputs": [], "source": ["final1.drop(columns=[\"available_flag\", \"count\", \"backend_available_flag\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e716948d-fe04-4aee-a282-8ed59161097a", "metadata": {}, "outputs": [], "source": ["temp = final1.copy(deep=True)"]}, {"cell_type": "code", "execution_count": null, "id": "610b9c44-b3fc-43f7-9952-25f3d9d679ef", "metadata": {}, "outputs": [], "source": ["temp = temp.round({\"fe_availability\": 2, \"be_availability\": 2})\n", "temp[\"fe_availability\"] = temp[\"fe_availability\"].astype(str) + \"%\"\n", "temp[\"be_availability\"] = temp[\"be_availability\"].astype(str) + \"%\""]}, {"cell_type": "code", "execution_count": null, "id": "11a85309-a3f6-4b46-a971-58757ec66776", "metadata": {}, "outputs": [], "source": ["temp.head(5)"]}, {"cell_type": "markdown", "id": "7f3f14f8-30f6-4aef-a07a-d1ec1ab9e82e", "metadata": {}, "source": ["### <PERSON><PERSON>s"]}, {"cell_type": "code", "execution_count": null, "id": "c8ef98ad-a22d-4165-91b8-d11dbf9a036d", "metadata": {}, "outputs": [], "source": ["slack_channel = pb.from_sheets(\n", "    \"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\"\n", ")\n", "slack_channel = (\n", "    slack_channel[slack_channel[\"alert\"] == \"fresh_sweets\"].reset_index().iloc[:, 2][0]\n", ")\n", "# slack_channel = \"bl-test-ch\"\n", "\n", "\n", "# def render_mpl_table(\n", "#     data,\n", "#     col_width=4.0,\n", "#     row_height=0.625,\n", "#     font_size=12,\n", "#     header_color=\"#E96125\",\n", "#     row_colors=[\"#f1f1f2\", \"w\"],\n", "#     edge_color=\"black\",\n", "#     bbox=[0, 0, 1, 1],\n", "#     header_columns=0,\n", "#     ax=None,\n", "#     **kwargs\n", "# ):\n", "#     if ax is None:\n", "#         size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "#             [col_width, row_height]\n", "#         )\n", "#         fig, ax = plt.subplots(figsize=size)\n", "#         ax.axis(\"off\")\n", "#     mpl_table = ax.table(\n", "#         cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs,\n", "#         cellLoc=\"center\"\n", "#     )\n", "#     mpl_table.auto_set_font_size(False)\n", "#     mpl_table.set_fontsize(font_size)\n", "\n", "#     for k, cell in mpl_table._cells.items():\n", "#         cell.set_edgecolor(edge_color)\n", "#         if k[0] == 0 or k[1] < header_columns:\n", "#             cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "#             cell.set_facecolor(header_color)\n", "#         else:\n", "#             cell.set_text_props(ma=\"left\")\n", "#             # cell.set_facecolor('#f1f1f2')\n", "#     mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "#     return ax.get_figure(), ax\n", "\n", "\n", "# fig, ax = render_mpl_table(temp, header_columns=0)\n", "# fig.savefig(\"fresh_sweets.png\")\n", "\n", "# current_hour = pd.to_datetime(datetime.now() + timedelta(hours=5.5)).strftime(\n", "#     \"%Y-%m-%d\"\n", "# )\n", "\n", "# channel = slack_channel\n", "# if temp.shape[0] > 0:\n", "#     text_req = \"Fresh Sweets \" + current_hour\n", "#     pb.send_slack_message(\n", "#         channel=channel, text=text_req, files=[\"./fresh_sweets.png\"]\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "2f7c2d3e-6191-44dd-be80-3ed9c6a73f13", "metadata": {}, "outputs": [], "source": ["temp.columns"]}, {"cell_type": "code", "execution_count": null, "id": "bb46b04d-03ab-48c5-86ec-476edc4180db", "metadata": {}, "outputs": [], "source": ["name = \"Fresh_Sweets\" + \".pdf\""]}, {"cell_type": "code", "execution_count": null, "id": "f0e6b03b-2270-4a6a-81b8-ba1052db744d", "metadata": {}, "outputs": [], "source": ["final_data_pdf = temp[\n", "    [\n", "        \"city\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"Backend Stock\",\n", "        \"DS Stock\",\n", "        \"GMV Today\",\n", "        \"GMV Till Date\",\n", "        \"fe_availability\",\n", "        \"be_availability\",\n", "    ]\n", "]\n", "\n", "# var = final_data_pdf.groupby(['city'])\n", "\n", "# list_df = []\n", "# for city, df in var:\n", "#     list_df.append(df)"]}, {"cell_type": "code", "execution_count": null, "id": "575d252b-c554-4cf7-8d75-b0d196f85c7e", "metadata": {}, "outputs": [], "source": ["lista = [\n", "    final_data_pdf.columns[\n", "        :,\n", "    ]\n", "    .values.astype(str)\n", "    .tolist()\n", "] + final_data_pdf.values.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "cf0abcb5-784d-45c3-9faa-b6382fa7ae0a", "metadata": {}, "outputs": [], "source": ["current_hour = pd.to_datetime(datetime.now() + timedelta(hours=5.5)).strftime(\n", "    \"%Y-%m-%d\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c7e7d90f-3879-401b-ab19-a00f0f4f54f9", "metadata": {}, "outputs": [], "source": ["def generate_pdf(file_name, final_data_pdf):\n", "    elements = []\n", "    styles = getSampleStyleSheet()\n", "    doc = SimpleDocTemplate(PATH_OUT + file_name)\n", "    elements.append(Paragraph(\"Fresh Sweets\", styles[\"Title\"]))\n", "    lista = [\n", "        final_data_pdf.columns[\n", "            :,\n", "        ]\n", "        .values.astype(str)\n", "        .tolist()\n", "    ] + final_data_pdf.values.tolist()\n", "    ts = [\n", "        (\"GRID\", (0, 0), (-1, -1), 1, colors.black),\n", "        (\"FONTNAME\", (0, 0), (-1, 0), \"Courier-Bold\"),\n", "        (\"FONTSIZE\", (0, 0), (-1, -1), 5),\n", "    ]\n", "\n", "    table = Table(lista, style=ts)\n", "    elements.append(table)\n", "    elements.append(PageBreak())\n", "\n", "    doc.build(elements)\n", "\n", "\n", "generate_pdf(name, final_data_pdf)"]}, {"cell_type": "code", "execution_count": null, "id": "abbef8aa-75f9-468b-9f57-a928cf9d2d16", "metadata": {}, "outputs": [], "source": ["channel = slack_channel\n", "if final.shape[0] > 0:\n", "    text_req = (\n", "        f\"Fresh Sweets Summary \"\n", "        + current_hour\n", "        + \"\\n\"\n", "        + f\"For details please refer this <https://docs.google.com/spreadsheets/d/1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM/edit#gid=1285182808 Sheet\"\n", "        + \"\\n\"\n", "    )\n", "    pb.send_slack_message(\n", "        channel=slack_channel, text=text_req, files=[\"Fresh_Sweets.pdf\"]\n", "    )"]}, {"cell_type": "code", "execution_count": 8, "id": "6a1ca18b-8a41-4b17-9b0b-4fa0210f353b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rm: cannot remove 'Fresh_Sweets.pdf': No such file or directory\n"]}], "source": ["try:\n", "    !rm Fresh_Sweets.pdf\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "1c947ef3-1b31-4deb-9922-7b1614513dc1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}