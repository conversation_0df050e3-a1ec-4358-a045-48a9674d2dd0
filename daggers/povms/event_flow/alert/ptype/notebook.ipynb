{"cells": [{"cell_type": "code", "execution_count": null, "id": "cd77838b-72a2-420a-82fb-f8a2eedd36c3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "495d9d53-034e-4981-ada8-5d2a52238447", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime, date\n", "import gc\n", "import warnings\n", "import math\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "!pip install pillow==8.3.1\n", "!pip install --upgrade --force-reinstall reportlab==3.6.1\n", "from reportlab.lib.styles import getSampleStyleSheet\n", "from reportlab.platypus import *\n", "\n", "import random\n", "from reportlab.lib import colors\n", "\n", "PATH_OUT = \"./\""]}, {"cell_type": "code", "execution_count": null, "id": "20817218-3e9c-4daf-94fd-78a3035e8edd", "metadata": {}, "outputs": [], "source": ["CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "17ff8f10-8d58-4dc8-9d49-63950c03f8d5", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "35fb3674-575e-4a38-88b1-09ae764f8559", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "markdown", "id": "eccfc0c5-81f3-4c82-8989-812f0aa51f88", "metadata": {}, "source": ["# Assortment Input"]}, {"cell_type": "code", "execution_count": null, "id": "0b4675f6-6265-4f5a-aaa6-f8376a6375bf", "metadata": {}, "outputs": [], "source": ["temp = pb.from_sheets(\"1Mng5N67HSoWkcasmZc2-AMrE3oGMx5khTvPN8j017TE\", \"assortment_input\")\n", "temp[\"active\"] = temp[\"active\"].astype(int)\n", "temp[\"item_id\"] = temp[\"item_id\"].astype(int)\n", "temp[\"go_live_date\"] = pd.to_datetime(temp[\"go_live_date\"])\n", "items = tuple(list(temp[\"item_id\"].unique()))\n", "temp.head()"]}, {"cell_type": "code", "execution_count": null, "id": "20d321b4-94b6-472e-9c4c-cf879a645dcb", "metadata": {}, "outputs": [], "source": ["temp = temp[(temp[\"event_flag\"] == \"Navratri\") & (temp[\"assortment_type\"] == \"Navratri\")]"]}, {"cell_type": "markdown", "id": "195e2c25-dd88-4a2f-953c-39035d32251d", "metadata": {"tags": []}, "source": ["## First Go-Live Date for Filters"]}, {"cell_type": "code", "execution_count": null, "id": "244e1ac8-8b8b-418f-bfbd-027148f8fe11", "metadata": {}, "outputs": [], "source": ["first_live_date = temp.agg({\"go_live_date\": \"min\"}).reset_index().iloc[:, 1][0]\n", "first_live_date"]}, {"cell_type": "code", "execution_count": null, "id": "8829804a-57ee-4bda-b10d-02668dc5a56e", "metadata": {}, "outputs": [], "source": ["first_live_date = pd.to_datetime(\"2022-09-01\")"]}, {"cell_type": "markdown", "id": "db2096cd-e8a4-4fb5-a5e1-f6e9f7ff53fe", "metadata": {}, "source": ["# Get ARS Active DS"]}, {"cell_type": "code", "execution_count": null, "id": "0ede0a7d-722f-4d6f-b0fd-29f7d608082c", "metadata": {}, "outputs": [], "source": ["ars_active_ds_query = f\"\"\"\n", "SELECT \n", "    distinct\n", "        pfom.facility_id,\n", "        pfom.city_id\n", "FROM\n", "    lake_view_po.physical_facility_outlet_mapping as pfom\n", "Inner join\n", "    lake_view_retail.console_outlet co on co.facility_id = pfom.facility_id and business_type_id = 7\n", "WHERE\n", "    pfom.active = 1 AND\n", "    pfom.ars_active = 1\"\"\"\n", "ars_active_ds_df = read_sql_query(ars_active_ds_query, CON_PRESTO)\n", "ars_active_ds_df = ars_active_ds_df[[\"facility_id\", \"city_id\"]].drop_duplicates()\n", "\n", "city_mapping_query = f\"\"\"\n", "SELECT \n", "    distinct id as city_id, name as city\n", "FROM\n", "    lake_view_retail.console_location\"\"\"\n", "city_map_df = read_sql_query(city_mapping_query, CON_PRESTO)\n", "\n", "ars_active_ds_df = pd.merge(ars_active_ds_df, city_map_df, on=[\"city_id\"], how=\"left\")\n", "ars_active_ds_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "804ed6b2-4381-4671-b4f0-3844874210d4", "metadata": {}, "outputs": [], "source": ["ars_active_ds_df.shape"]}, {"cell_type": "markdown", "id": "60eebc3f-19a7-4e2e-b811-7ff01943d8ca", "metadata": {}, "source": ["# Set up Assortment Base"]}, {"cell_type": "code", "execution_count": null, "id": "6f65dec4-9326-450c-aa24-***********5", "metadata": {}, "outputs": [], "source": ["base_df = pd.merge(temp, ars_active_ds_df, on=[\"city\"], how=\"inner\")\n", "base_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "da6040a4-ef54-41b2-86f0-e79619f81ece", "metadata": {}, "outputs": [], "source": ["base_df.shape"]}, {"cell_type": "markdown", "id": "e0b92c0a-7d35-49a4-a341-3161d59fd5f6", "metadata": {}, "source": ["## Fetch Facility, Outlet Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "894a74f5-8df3-4106-bd7a-8f0659c963ae", "metadata": {}, "outputs": [], "source": ["facility_mapping_query = f\"\"\"\n", "SELECT \n", "    distinct id as outlet_id, facility_id\n", "FROM\n", "    lake_view_retail.console_outlet\n", "WHERE\n", "    active = 1\"\"\"\n", "facility_mapping_df = read_sql_query(facility_mapping_query, CON_PRESTO)"]}, {"cell_type": "markdown", "id": "92f05e79-0ba8-4850-97b2-3a1136c6fe15", "metadata": {}, "source": ["## <PERSON><PERSON>, Ptype Mapping"]}, {"cell_type": "code", "execution_count": null, "id": "0a7b9700-1e69-48c9-8a05-d0d488f6b1bd", "metadata": {}, "outputs": [], "source": ["product_attributes_query = f\"\"\"\n", "with categories AS\n", "(\n", "    SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "              WHEN C1.NAME = C.name THEN C2.name\n", "              ELSE C1.name\n", "          END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "    FROM lake_cms.gr_product P\n", "    INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "    INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "    AND PCM.IS_PRIMARY=TRUE\n", "    INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "    INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "    INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id\n", "),\n", "\n", "category_pre AS\n", "(\n", "    SELECT item_id,\n", "          product_id,\n", "          cat.l0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.product_type,\n", "          cat.manf\n", "    FROM lake_rpc.item_product_mapping rpc\n", "    INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "    AND rpc.offer_id IS NULL\n", "    AND rpc.item_id IS NOT NULL\n", "    AND rpc.product_id IS NOT NULL\n", "),\n", "\n", "ptype_tbl AS\n", "(\n", "    SELECT item_id,\n", "          max(l0) AS l0,\n", "          max(l1) AS l1,\n", "          max(l2) AS l2,\n", "          max(product_type) AS ptype,\n", "          max(manf) AS manf\n", "    FROM category_pre\n", "    GROUP BY 1\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    ptype_tbl\n", "\"\"\"\n", "product_attributes_df = read_sql_query(product_attributes_query, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "843dbd93-3294-45cc-88f3-92e615bd5d10", "metadata": {}, "outputs": [], "source": ["## fecthing navaratri specific ptypes\n", "\n", "temp1 = pb.from_sheets(\"1OK-KLr5Pq-L0CSvFy_TRLglySKprSEssYniR8gfSW3k\", \"Sheet6\")\n", "temp1 = temp1[(temp1[\"Flags\"] == \"Pooja\") | (temp1[\"Flags\"] == \"Spec\")][\"ptype\"].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "a5d9edfb-f5a1-43b2-99b5-e954c11ade35", "metadata": {}, "outputs": [], "source": ["product_attributes_df = pd.merge(product_attributes_df, temp1, how=\"inner\", on=[\"ptype\"])"]}, {"cell_type": "code", "execution_count": null, "id": "e5a3deb7-5aff-473c-9654-6f5ff8bd5612", "metadata": {}, "outputs": [], "source": ["product_attributes_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "be0c4cc1-3d9d-4b56-80d3-bbbcd24d8de9", "metadata": {}, "outputs": [], "source": ["facility_name_mapping_query = (\n", "    f\"\"\"SELECT distinct id as facility_id, name as facility_name FROM lake_crates.facility\"\"\"\n", ")\n", "facility_name_mapping_df = read_sql_query(facility_name_mapping_query, CON_REDSHIFT)\n", "facility_name_mapping_df.head(1)"]}, {"cell_type": "markdown", "id": "ea0a3f51-556b-495e-ac62-502aec318575", "metadata": {}, "source": ["## Fetch TEA Tagging"]}, {"cell_type": "code", "execution_count": null, "id": "31a2518b-4b4f-46ec-a697-efda53953a47", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with tea_tagging as \n", "(\n", "    select \n", "        cl.name as city,\n", "        tea.item_id,\n", "        item.name as item_name,\n", "        co.facility_id as frontend_facility_id,\n", "        co2.name as frontend_facility_name,\n", "        co1.facility_id as backend_facility_id,\n", "        co3.name as backend_facility_name\n", "    from \n", "        lake_view_rpc.item_outlet_tag_mapping tea\n", "    left join  \n", "        lake_view_retail.console_outlet co on tea.outlet_id=co.id and co.business_type_id=7 and co.active=1\n", "    left join\n", "        lake_view_retail.console_location cl on co.tax_location_id = cl.id\n", "    left join\n", "        lake_view_retail.console_outlet co1 on cast(tea.tag_value as int)=cast(co1.id as int) and co1.business_type_id!=7 and co1.active=1\n", "    left join\n", "        lake_view_crates.facility co2 on co.facility_id=co2.id \n", "    left join\n", "        lake_view_crates.facility co3 on co1.facility_id=co3.id \n", "    left join\n", "        lake_view_rpc.item_details item  on tea.item_id=item.item_id\n", "    where tag_type_id=8 and tea.active=1 and tea.item_id in {items}\n", ")\n", "select * from tea_tagging\"\"\"\n", "\n", "tea_tag = read_sql_query(query, CON_PRESTO)\n", "tea_tag.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f633f7de-e005-4098-86cc-8523cfc5b231", "metadata": {}, "outputs": [], "source": ["tea_tag = tea_tag.dropna()"]}, {"cell_type": "code", "execution_count": null, "id": "9a32daea-16d2-415b-bfd9-feb1bd69df29", "metadata": {}, "outputs": [], "source": ["tea_tag.shape"]}, {"cell_type": "code", "execution_count": null, "id": "73b0772a-36c6-4d24-a068-568abd44f858", "metadata": {}, "outputs": [], "source": ["## backend frontend mapping\n", "\n", "query = f\"\"\"\n", "    SELECT \n", "        distinct\n", "        pfom.facility_id as frontend_facility_id, \n", "        bfom.facility_id as backend_facility_id\n", "    FROM \n", "        lake_view_po.bulk_facility_outlet_mapping bfom\n", "    INNER JOIN \n", "    lake_view_po.physical_facility_outlet_mapping pfom ON bfom.outlet_id=pfom.outlet_id\n", "    LEFT JOIN lake_view_crates.facility cf on cf.id = bfom.facility_id \n", "    WHERE bfom.active = True\n", "    AND pfom.active = 1\n", "    AND pfom.ars_active = 1\"\"\"\n", "\n", "active_backends = read_sql_query(query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "95f1280b-0401-47e1-8bb3-7613c9930d8b", "metadata": {}, "outputs": [], "source": ["# active_backends[active_backends['backend_facility_id']==15.0]"]}, {"cell_type": "code", "execution_count": null, "id": "c63a289e-293d-48ce-8231-6e6abf4cd3b6", "metadata": {}, "outputs": [], "source": ["tea_tag = pd.merge(\n", "    tea_tag,\n", "    active_backends,\n", "    how=\"inner\",\n", "    on=[\"frontend_facility_id\", \"backend_facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c6249182-8406-4e09-bc11-9b471dabf692", "metadata": {}, "outputs": [], "source": ["tea_tag.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "08c0cd9e-da81-449e-b765-b48a59a77f30", "metadata": {}, "outputs": [], "source": ["tea_tag.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e95d25ff-b60c-4056-8da3-d6cf7ccb8652", "metadata": {}, "outputs": [], "source": ["tea_tag = tea_tag[\n", "    (tea_tag[\"frontend_facility_id\"] != 1182) & (tea_tag[\"frontend_facility_id\"] != 1028)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "68a016f0-3d91-4478-8a0e-7c1576f59a87", "metadata": {}, "outputs": [], "source": ["tea_tag.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0f5e524b-68bf-45b3-ae17-247b7e6d750c", "metadata": {}, "outputs": [], "source": ["# Validation\n", "# tea_tag[tea_tag['item_id'] == 10029774].groupby(['item_id', 'backend_facility_id']).agg({'frontend_facility_id':'nunique'}).reset_index()\n", "# tea_tag[['city']].drop_duplicates().sort_values(by = 'city')"]}, {"cell_type": "markdown", "id": "bb339279-4db9-4945-b3c9-6ebc80e83fad", "metadata": {}, "source": ["## Fetch Sales Data"]}, {"cell_type": "code", "execution_count": null, "id": "495baf58-96cd-462f-a380-26f53728b68f", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", " with base as\n", "(\n", "    SELECT \n", "        date(od.created_at  + interval '330' Minute) as date, ancestor, outlet AS outlet_id, oi.item_id, sum(oi.quantity) as quantity\n", "    FROM \n", "        lake_view_ims.ims_order_details od\n", "    LEFT JOIN \n", "        lake_view_ims.ims_order_items oi on od.id = oi.order_details_id\n", "    WHERE \n", "        status_id in (1,2)\n", "    AND \n", "        oi.item_id in {items}\n", "    AND\n", "        od.insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    AND\n", "        oi.insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    GROUP BY \n", "        1,2,3,4\n", "),\n", "\n", "GMV_base as\n", "(\n", "    SELECT \n", "        cast(ancestor as var<PERSON>r) as ancestor, item_id, avg(GMV) as GMV\n", "    FROM \n", "    (\n", "        SELECT \n", "            oi.order_id as ancestor,\n", "            oi.product_id, pl.item_id, \n", "            (oi.selling_price*1.000/pl.multiplier) AS item_selling_price, \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS f_ordered_qty,\n", "            (oi.selling_price*1.000/pl.multiplier) * ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) AS GMV\n", "    \n", "        FROM \n", "            lake_view_oms_bifrost.oms_order_item oi\n", "        LEFT JOIN \n", "                (\n", "                    SELECT \n", "                        DISTINCT\n", "                            ipr.product_id, \n", "                            case \n", "                                when\n", "                                    ipr.item_id is null \n", "                                then ipom_0.item_id else ipr.item_id end as item_id,\n", "                            case \n", "                                when\n", "                                    ipr.item_id is not null\n", "                                then \n", "                                    COALESCE(ipom.multiplier,1)\n", "                                else\n", "                                    COALESCE(ipom_0.multiplier,1)\n", "                                end AS multiplier \n", "                    FROM \n", "                        lake_rpc.item_product_mapping ipr\n", "                    left join\n", "                        redshift.dwh.dim_item_product_offer_mapping ipom\n", "                    on\n", "                        ipr.product_id = ipom.product_id \n", "                    and\n", "                            ipr.item_id = ipom.item_id\n", "                    \n", "                    left join\n", "                        redshift.dwh.dim_item_product_offer_mapping ipom_0\n", "                    on\n", "                        ipr.product_id = ipom_0.product_id\n", "\n", "                ) pl ON pl.product_id = oi.product_id\n", "        WHERE \n", "            pl.item_id IS NOT NULL \n", "        AND \n", "            ((oi.quantity * pl.multiplier) - (oi.cancelled_quantity * pl.multiplier)) > 0\n", "        AND\n", "            insert_ds_ist >= cast(cast('{first_live_date}' as timestamp) - interval '1' day as varchar)\n", "    ) order_qty\n", "    WHERE\n", "        item_id in {items}\n", "    AND\n", "        ancestor in (select distinct cast(ancestor as bigint) as ancestor from base)\n", "    group by 1,2\n", ")\n", "\n", "\n", ", sales_with_GMV as \n", "(SELECT \n", "    b.date, \n", "    co.facility_id,\n", "    b.item_id, \n", "    count(distinct b.ancestor) as orders,\n", "    sum(quantity) as quantity,\n", "    sum(gmv) as gmv\n", "FROM base b\n", "LEFT JOIN\n", "    GMV_base gb \n", "ON\n", "    b.ancestor = gb.ancestor and  b.item_id = gb.item_id\n", "left join \n", "    lake_retail.console_outlet co on co.id = b.outlet_id and co.active = 1\n", "where  date >= cast('{first_live_date}' as timestamp)\n", "group by 1,2,3     \n", "),\n", "\n", "\n", "current_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as current_day_qty_sold, sum(gmv) as current_day_gmv, sum(orders) as current_day_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_1_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_1_qty_sold, sum(gmv) as t_1_gmv, sum(orders) as t_1_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date = date(current_timestamp + interval '330' minute) - interval '1' day --and date(getdate() + interval '330' Minute)\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "t_7_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as t_7_qty_sold, sum(gmv) as t_7_gmv, sum(orders) as t_7_carts\n", "    FROM\n", "        sales_with_GMV\n", "    WHERE\n", "        date >= date(current_timestamp + interval '330' minute) - interval '7' day\n", "    GROUP BY\n", "        1, 2\n", "),\n", "\n", "till_day as\n", "(\n", "    SELECT \n", "        facility_id, item_id, sum(quantity) as qty_sold_till_date, sum(gmv) as gmv_till_date, sum(orders) as carts_till_date\n", "    FROM\n", "        sales_with_GMV\n", "    GROUP BY\n", "        1, 2\n", ")\n", "\n", "SELECT\n", "    td.facility_id as frontend_facility_id, td.item_id,\n", "    current_day_qty_sold, current_day_gmv, current_day_carts,\n", "    t_1_qty_sold, t_1_gmv, t_1_carts,\n", "    t_7_qty_sold, t_7_gmv, t_7_carts,\n", "    qty_sold_till_date, gmv_till_date, carts_till_date\n", "FROM\n", "    till_day td\n", "LEFT JOIN\n", "    current_day cd\n", "ON\n", "    cd.item_id = td.item_id and cd.facility_id = td.facility_id\n", "LEFT JOIN\n", "    t_1_day t1d\n", "ON\n", "    td.item_id = t1d.item_id and td.facility_id = t1d.facility_id\n", "LEFT JOIN\n", "    t_7_day t7d\n", "ON\n", "    td.item_id = t7d.item_id and td.facility_id = t7d.facility_id\n", "\"\"\"\n", "\n", "sales = read_sql_query(query, CON_PRESTO)\n", "## Change 29th"]}, {"cell_type": "markdown", "id": "10f7f92c-a29f-45e2-ba79-c0a0dd933ddf", "metadata": {}, "source": ["## Fetch Dark Store Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "dd61ce10-78d3-42e5-b478-117b66f830dd", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with DS_inventory  AS\n", "  (SELECT \n", "         distinct\n", "          a.item_id,\n", "          inv_outlet_id,\n", "          outlet_name,\n", "          facility_id,\n", "          facility_name,\n", "          case when (actual_quantity - blocked_quantity) < 0 then 0 else (actual_quantity - blocked_quantity) end as current_stock\n", "   FROM\n", "     (SELECT DISTINCT \n", "            a.item_id,\n", "            r.inv_outlet_id,\n", "            r.outlet_name,\n", "            r.facility_id,\n", "            cf.name AS facility_name,\n", "            (a.quantity) AS actual_quantity,\n", "            sum(CASE WHEN blocked_type IN (1,2,5) THEN b.quantity ELSE 0 END) AS blocked_quantity\n", "      FROM lake_view_ims.ims_item_inventory AS a\n", "      INNER JOIN\n", "        (select \n", "            om.outlet_id as hot_outlet_id, \n", "            om.outlet_name, \n", "            om.facility_id,\n", "            case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id\n", "         from lake_view_po.physical_facility_outlet_mapping om\n", "       left join (select id, tax_location_id,\n", "         case when id = 581 then 12 else business_type_id end as business_type_id\n", "       from lake_view_retail.console_outlet) rco on rco.id = om.outlet_id and rco.business_type_id=7\n", "       left join \n", "            (select distinct warehouse_id, cloud_store_id from lake_view_retail.warehouse_outlet_mapping \n", "                where active = 1) wom on wom.warehouse_id = om.outlet_id\n", "        where rco.business_type_id in (7) and om.outlet_id not in (0)\n", "        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "        and om.outlet_name not like '%%SSC%%'\n", "        and om.outlet_name not like '%%MODI%%'\n", "        and om.outlet_name not like '%%hot ff%%'\n", "        and om.outlet_name not like '%%CIA%%'\n", "        ) r ON a.outlet_id = r.inv_outlet_id\n", "      LEFT JOIN lake_view_ims.ims_item_blocked_inventory b ON a.outlet_id = b.outlet_id\n", "      AND a.item_id = b.item_id\n", "      LEFT JOIN lake_view_crates.facility cf ON r.facility_id = cf.id\n", "    where a.item_id in {items}\n", "      GROUP BY 1,\n", "               2,\n", "               3,4,5,6) a\n", "   )\n", "select * from DS_inventory\"\"\"\n", "\n", "DS_inventory = read_sql_query(query, CON_PRESTO)"]}, {"cell_type": "code", "execution_count": null, "id": "ede3aaec-b245-44f4-ae69-354bbb9e4864", "metadata": {}, "outputs": [], "source": ["DS_inventory.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d06cc218-50cb-42a0-b8de-f27e3f6623f0", "metadata": {}, "outputs": [], "source": ["# temp[temp.item_id == 10116292]"]}, {"cell_type": "markdown", "id": "3a9ba6e2-3a62-4fd1-ae81-b3b63ce7bc6f", "metadata": {}, "source": ["## Fetch Backend Inventory"]}, {"cell_type": "code", "execution_count": null, "id": "7641b94f-a4e7-4fea-871c-4edd1cf3d1de", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "with DS_inventory  AS\n", "  (SELECT \n", "         distinct\n", "          a.item_id,\n", "          inv_outlet_id,\n", "          outlet_name,\n", "          facility_id,\n", "          facility_name,\n", "          case when (actual_quantity - blocked_quantity) < 0 then 0 else (actual_quantity - blocked_quantity) end as current_stock\n", "   FROM\n", "     (SELECT DISTINCT \n", "            a.item_id,\n", "            r.inv_outlet_id,\n", "            r.outlet_name,\n", "            r.facility_id,\n", "            cf.name AS facility_name,\n", "            (a.quantity) AS actual_quantity,\n", "            sum(CASE WHEN blocked_type IN (1,2,5) THEN b.quantity ELSE 0 END) AS blocked_quantity\n", "      FROM lake_view_ims.ims_item_inventory AS a\n", "      INNER JOIN\n", "        (select \n", "            om.outlet_id as hot_outlet_id, \n", "            om.outlet_name, \n", "            om.facility_id,\n", "            case when wom.cloud_store_id is null then om.outlet_id else wom.cloud_store_id end as inv_outlet_id\n", "         from lake_view_po.physical_facility_outlet_mapping om\n", "       left join (select id, tax_location_id,\n", "         case when id = 581 then 12 else business_type_id end as business_type_id\n", "       from lake_view_retail.console_outlet) rco on rco.id = om.outlet_id\n", "       left join \n", "            (select distinct warehouse_id, cloud_store_id from lake_view_retail.warehouse_outlet_mapping \n", "                where active = 1) wom on wom.warehouse_id = om.outlet_id\n", "        where rco.business_type_id not in (7) and om.outlet_id not in (0)\n", "        and om.active = 1 and ars_active = 1 and is_primary = 1\n", "        and om.outlet_name not like '%%SSC%%'\n", "        and om.outlet_name not like '%%MODI%%'\n", "        and om.outlet_name not like '%%hot ff%%'\n", "        and om.outlet_name not like '%%CIA%%'\n", "        ) r ON a.outlet_id = r.inv_outlet_id\n", "      LEFT JOIN lake_view_ims.ims_item_blocked_inventory b ON a.outlet_id = b.outlet_id\n", "      AND a.item_id = b.item_id\n", "      LEFT JOIN lake_view_crates.facility cf ON r.facility_id = cf.id\n", "    where a.item_id in {items}\n", "      GROUP BY 1,\n", "               2,\n", "               3,4,5,6) a\n", "   )\n", "select item_id, facility_id, facility_name, sum(current_stock) as current_stock from DS_inventory group by 1,2,3\"\"\"\n", "\n", "backend_inventory = read_sql_query(query, CON_PRESTO)\n", "\n", "## Change 29th"]}, {"cell_type": "code", "execution_count": null, "id": "f0781613-d870-42a3-be3b-bd1079e791fd", "metadata": {}, "outputs": [], "source": ["backend_inventory.shape"]}, {"cell_type": "code", "execution_count": null, "id": "0f205f51-4688-4e15-8483-4c1701c3b1bd", "metadata": {}, "outputs": [], "source": ["base_df.shape"]}, {"cell_type": "markdown", "id": "0549d087-7f2e-4e34-896f-999f893e9372", "metadata": {}, "source": ["### Final Mapping\n"]}, {"cell_type": "code", "execution_count": null, "id": "fa3412ba-9364-4fef-8611-220ea98c506a", "metadata": {}, "outputs": [], "source": ["base_df = base_df.rename(columns={\"facility_id\": \"frontend_facility_id\"})\n", "\n", "tea_tag = tea_tag[\n", "    [\"item_id\", \"item_name\", \"frontend_facility_id\", \"backend_facility_id\"]\n", "].drop_duplicates()\n", "tea_tag[\"tea_active\"] = 1\n", "\n", "\n", "## Change 29th\n", "base_df = pd.merge(\n", "    base_df,\n", "    tea_tag,\n", "    on=[\n", "        \"item_id\",\n", "        \"frontend_facility_id\",\n", "    ],\n", "    how=\"inner\",\n", ")\n", "base_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "53995d0a-bc77-4cc2-86f5-78170fee9ce8", "metadata": {}, "outputs": [], "source": ["base_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e8ec7a79-8ec7-4b79-bf42-b92d8c6b92b1", "metadata": {}, "outputs": [], "source": ["base_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "6c7f87be-0271-47d3-a976-8061951a6cbd", "metadata": {}, "outputs": [], "source": ["base_df = base_df[\n", "    [\n", "        \"city\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"go_live_date\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"frontend_facility_id\",\n", "        \"city_id\",\n", "        \"backend_facility_id\",\n", "        \"tea_active\",\n", "    ]\n", "]\n", "base_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "75a0c8d3-535e-42a1-a425-c8e5eaab8392", "metadata": {}, "outputs": [], "source": ["product_attributes_df"]}, {"cell_type": "code", "execution_count": null, "id": "36e86a25-090f-46c4-8d53-74e31f037bcd", "metadata": {}, "outputs": [], "source": ["# Merge DS Inventory\n", "\n", "final_df = pd.merge(\n", "    base_df,\n", "    DS_inventory.drop(columns={\"facility_name\"}),\n", "    left_on=[\"item_id\", \"frontend_facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ").drop(columns=[\"inv_outlet_id\", \"outlet_name\", \"facility_id\"])\n", "\n", "# Merge Backend Inventory\n", "\n", "backend_inventory = backend_inventory.rename(columns={\"current_stock\": \"current_stock_backend\"})\n", "final_df = pd.merge(\n", "    final_df,\n", "    backend_inventory.drop(columns={\"facility_name\"}),\n", "    left_on=[\"item_id\", \"backend_facility_id\"],\n", "    right_on=[\"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ").drop(columns=[\"facility_id\"])\n", "\n", "# Merge Sales\n", "final_df = pd.merge(final_df, sales, on=[\"frontend_facility_id\", \"item_id\"], how=\"left\")\n", "\n", "\n", "# Add Ptype\n", "final_df = pd.merge(\n", "    final_df,\n", "    product_attributes_df[[\"item_id\", \"ptype\"]].drop_duplicates(),\n", "    on=[\"item_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "## filterting p type\n", "\n", "\n", "# Fill Missing Values\n", "final_df[\n", "    [\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "    ]\n", "] = final_df[\n", "    [\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"current_day_carts\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "        \"current_stock\",\n", "        \"current_stock_backend\",\n", "    ]\n", "].fillna(\n", "    0\n", ")\n", "\n", "final_df[\"ptype\"] = final_df[\"ptype\"].fillna(\"Unknown\")\n", "final_df[\"tea_active\"] = final_df[\"tea_active\"].fillna(0)\n", "\n", "final_df = final_df[\n", "    [\n", "        \"city\",\n", "        \"event_flag\",\n", "        \"assortment_type\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"ptype\",\n", "        \"go_live_date\",\n", "        \"frontend_facility_id\",\n", "        \"backend_facility_id\",\n", "        \"tea_active\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"current_day_qty_sold\",\n", "        \"current_day_gmv\",\n", "        \"qty_sold_till_date\",\n", "        \"gmv_till_date\",\n", "    ]\n", "]\n", "\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "be31c236-e20b-4017-ab2d-2f47cdb3ae71", "metadata": {}, "outputs": [], "source": ["final_df[\"backend_facility_id\"] = np.where(\n", "    final_df[\"backend_facility_id\"].isna(), -666, final_df[\"backend_facility_id\"]\n", ")\n", "final_df = final_df[~final_df[\"frontend_facility_id\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "40285623-1d59-4400-ace6-32b1ae2b0ed3", "metadata": {}, "outputs": [], "source": ["facility_name_mapping_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "06d16ed2-3106-4bf9-9b1a-32d9865d01bd", "metadata": {}, "outputs": [], "source": ["final_df = (\n", "    pd.merge(\n", "        final_df,\n", "        facility_name_mapping_df,\n", "        left_on=[\"backend_facility_id\"],\n", "        right_on=[\"facility_id\"],\n", "        how=\"inner\",\n", "    )\n", "    .drop(columns=[\"facility_id\"])\n", "    .rename(columns={\"facility_name\": \"backend_facility_name\"})\n", ")\n", "\n", "\n", "final_df = (\n", "    pd.merge(\n", "        final_df,\n", "        facility_name_mapping_df,\n", "        left_on=[\"frontend_facility_id\"],\n", "        right_on=[\"facility_id\"],\n", "        how=\"inner\",\n", "    )\n", "    .drop(columns=[\"facility_id\"])\n", "    .rename(columns={\"facility_name\": \"frontend_facility_name\"})\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "81974246-0e3e-4750-a419-975c56bb80ba", "metadata": {}, "outputs": [], "source": ["final_df = final_df[\n", "    [\n", "        \"ptype\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"frontend_facility_name\",\n", "        \"backend_facility_name\",\n", "        \"current_stock_backend\",\n", "        \"current_stock\",\n", "        \"current_day_gmv\",\n", "        \"gmv_till_date\",\n", "    ]\n", "].rename(columns={\"current_stock_backend\": \"Warehouse Stock\", \"current_stock\": \"DS Stock\"})"]}, {"cell_type": "code", "execution_count": null, "id": "32e1b78a-6e96-4088-b82f-50aa808e13d9", "metadata": {}, "outputs": [], "source": ["final_df.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fe98dfc7-e846-4fd9-bb1f-0bf5491052c4", "metadata": {}, "outputs": [], "source": ["final_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "e6eecbb9-ce5a-4cb6-8f87-a2707362c5c6", "metadata": {}, "outputs": [], "source": ["np.sum(\n", "    final_df[\n", "        (final_df[\"ptype\"] == \"Aasan\")\n", "        & (final_df[\"backend_facility_name\"].str.contains(\"Farukhnagar\"))\n", "    ][\"gmv_till_date\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "61b708ec-5d54-44fb-9ca5-56b80b86ca05", "metadata": {}, "outputs": [], "source": ["np.sum(final_df[(final_df[\"ptype\"] == \"Aasan\")][\"gmv_till_date\"])"]}, {"cell_type": "code", "execution_count": null, "id": "38446a05-616d-4f3f-a6c0-e137b454acc6", "metadata": {}, "outputs": [], "source": ["final_df = final_df[final_df[\"DS Stock\"] <= 0].sort_values(\n", "    by=[\"ptype\", \"backend_facility_name\", \"frontend_facility_name\", \"item_id\"],\n", "    ascending=[True, True, True, True],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c36da342-673b-4273-8c97-b520f8c56532", "metadata": {}, "outputs": [], "source": ["np.unique(final_df[final_df[\"ptype\"] == \"Aasan\"][\"item_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "04173980-f443-4c4c-92fe-a3dc5fbde40b", "metadata": {}, "outputs": [], "source": ["pb.to_sheets(final_df, \"1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM\", \"navratri ptypes\")"]}, {"cell_type": "markdown", "id": "13f1ab1a-d8bb-40f5-aa83-2d289f156250", "metadata": {}, "source": ["### <PERSON><PERSON>s"]}, {"cell_type": "code", "execution_count": null, "id": "4114dfb2-1721-4441-b9e2-923296ea6ddb", "metadata": {}, "outputs": [], "source": ["slack_channel = pb.from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "slack_channel = slack_channel[slack_channel[\"alert\"] == \"ptype\"].reset_index().iloc[:, 2][0]"]}, {"cell_type": "code", "execution_count": null, "id": "22be65d7-b0ce-44e0-bbfa-c1d64c0aee7c", "metadata": {}, "outputs": [], "source": ["name = \"P_Type_Performance\" + \".pdf\""]}, {"cell_type": "code", "execution_count": null, "id": "2e835196-e7f5-46ba-bc2f-5381c5f66c35", "metadata": {}, "outputs": [], "source": ["current_hour = pd.to_datetime(datetime.now() + timedelta(hours=5.5)).strftime(\"%m/%d/%Y, %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "3c314005-2eef-4aa2-b262-318fcdcad47d", "metadata": {}, "outputs": [], "source": ["stocked = final_df.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "6817197d-0912-4b5d-9e44-f2242e643847", "metadata": {}, "outputs": [], "source": ["current_hour"]}, {"cell_type": "code", "execution_count": null, "id": "994d178e-f8e0-4f04-8b57-3273fe88ef1f", "metadata": {}, "outputs": [], "source": ["def generate_pdf(file_name, final_data_pdf):\n", "    elements = []\n", "    styles = getSampleStyleSheet()\n", "    doc = SimpleDocTemplate(PATH_OUT + file_name)\n", "    elements.append(Paragraph(\"Ptypes\", styles[\"Title\"]))\n", "    lista = [\n", "        final_df.columns[\n", "            :,\n", "        ]\n", "        .values.astype(str)\n", "        .tolist()\n", "    ] + final_df.values.tolist()\n", "    ts = [\n", "        (\"GRID\", (0, 0), (-1, -1), 1, colors.black),\n", "        (\"FONTNAME\", (0, 0), (-1, 0), \"Courier-Bold\"),\n", "        (\"FONTSIZE\", (0, 0), (-1, -1), 4),\n", "    ]\n", "\n", "    table = Table(lista, style=ts)\n", "    elements.append(table)\n", "    elements.append(PageBreak())\n", "\n", "    doc.build(elements)\n", "\n", "\n", "generate_pdf(name, final_df)"]}, {"cell_type": "code", "execution_count": null, "id": "1581214c-669a-4b5f-860d-638498d0b315", "metadata": {}, "outputs": [], "source": ["channel = slack_channel\n", "if final_df.shape[0] > 0:\n", "    text_req = (\n", "        f\"Navratri Ptype Summary \"\n", "        + current_hour\n", "        + \"\\n\"\n", "        + str(stocked)\n", "        + \" Item DS combination are out of stock\\n\"\n", "        + \"For details please refer to this <https://docs.google.com/spreadsheets/d/1PEPry7krFqnqa-u6Oispj5kh8QGOVonypUpbh1ZFYpM/edit#gid=1824481987|Sheet >\"\n", "        + \"\\n\"\n", "    )\n", "    pb.send_slack_message(channel=slack_channel, text=text_req, files=[\"P_Type_Performance.pdf\"])"]}, {"cell_type": "code", "execution_count": null, "id": "89e8e53b-e107-4a50-b7de-4578bcf6b72d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}