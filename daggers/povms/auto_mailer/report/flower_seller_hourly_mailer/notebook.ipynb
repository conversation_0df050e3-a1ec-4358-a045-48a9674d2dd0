{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["! pip install pandasql"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import time\n", "import pandasql as ps\n", "import calendar\n", "from datetime import datetime as dt\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "from jinja2 import Template\n", "\n", "IST = pytz.timezone(\"Asia/Kolkata\")\n", "\n", "current_time = datetime.now(IST)\n", "today_date = current_time.strftime(\"%Y-%m-%d\")\n", "\n", "switch = \"external\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.options.mode.chained_assignment = None  # default='warn'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ITEM DETAILS FETCH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SKU's\n", "\n", "sheet_id = \"1O2RGf8tsyLYrEmXWv-MPFtzXaFeQcKLMV1OpqxYCHgs\"\n", "input_skus = pb.from_sheets(sheet_id, \"items\")\n", "# input_skus = pd.read_csv('items.csv')\n", "input_skus = input_skus[\n", "    (input_skus[\"item_id\"].isna() == False) | (input_skus[\"item_id\"] != \"\")\n", "]\n", "input_skus = input_skus[[\"item_id\", \"brand_name\"]].drop_duplicates()\n", "input_skus[\"item_id\"] = input_skus[\"item_id\"].astype(int)\n", "input_skus_list = tuple(list(input_skus[\"item_id\"].unique()))\n", "input_skus.head()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## STORE DETAILS FETCH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Stores\n", "\n", "input_stores_query = f\"\"\"select distinct id as outlet_id from lake_retail.console_outlet where active = 1 and business_type_id = 7\"\"\"\n", "input_stores = read_sql_query(input_stores_query, CON_REDSHIFT)\n", "input_stores = input_stores[\n", "    (input_stores[\"outlet_id\"].isna() == False) | (input_stores[\"outlet_id\"] != \"\")\n", "]\n", "input_stores[\"outlet_id\"] = input_stores[\"outlet_id\"].astype(int)\n", "input_stores_list = tuple(list(input_stores[\"outlet_id\"].unique()))\n", "input_stores.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## FACILITY MAPPING DETAILS FETCH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping_query = f\"\"\"\n", "       SELECT \n", "                distinct co.id, facility_id, co.name, tax_location_id as city_id, cl.name as city\n", "            FROM \n", "                lake_retail.console_outlet co\n", "            LEFT JOIN\n", "                lake_retail.console_location cl\n", "            ON\n", "                co.tax_location_id = cl.id\n", "            WHERE \n", "                co.active = 1 \n", "            and\n", "                co.business_type_id = 7\n", "            and\n", "                co.id in {input_stores_list}\n", "            and facility_id is not null\n", "    \"\"\"\n", "print(\"\\nfetching facility_mapping_df\")\n", "facility_mapping_df = read_sql_query(facility_mapping_query, CON_REDSHIFT)\n", "facility_mapping_df = facility_mapping_df.rename(columns={\"id\": \"outlet_id\"})\n", "facility_mapping_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## SALES DETAILS FETCH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_query = f\"\"\"\n", "WITH sales AS (\n", "    SELECT od.created_at, cl.name AS city, od.outlet AS outlet_id, co.name AS outlet_name, oi.item_id, SUM(oi.quantity) AS quantity\n", "    FROM lake_ims.ims_order_details od\n", "    LEFT JOIN lake_ims.ims_order_items oi ON od.id = oi.order_details_id\n", "    LEFT JOIN lake_ims.ims_order_actuals oa ON od.id = oa.order_details_id AND oi.item_id=oa.item_id\n", "    INNER JOIN lake_retail.console_outlet co ON co.id = od.outlet AND business_type_id = 7 AND co.active = 1 AND co.id IN {input_stores_list} AND facility_id is not null\n", "    INNER JOIN lake_retail.console_location cl ON cl.id = co.tax_location_id\n", "    INNER JOIN lake_rpc.item_category_details icd ON icd.item_id = oi.item_id AND icd.item_id IN {input_skus_list}\n", "    WHERE status_id in (1,2,4)\n", "    AND (od.created_at + interval '5.5 Hours') BETWEEN ((current_timestamp + interval '5.5 Hours') - interval '90 Minutes') AND ((current_timestamp + interval '5.5 Hours') - interval '30 Minutes')\n", "    GROUP BY 1,2,3,4,5\n", "),\n", "\n", "sales_agg AS (\n", "    SELECT outlet_id, outlet_name, city, date_, hour_,item_id, quantity\n", "    FROM ( SELECT outlet_id, outlet_name, city, DATE(created_at + interval '5.5 Hours') AS date_, EXTRACT(hour FROM (created_at + interval '5.5 Hours')) AS hour_, item_id, SUM(quantity) AS quantity\n", "        FROM sales\n", "        GROUP BY 1,2,3,4,5,6\n", "        ORDER BY city ASC )\n", "    )\n", "\n", "SELECT outlet_id, outlet_name, city AS city, date_, hour_, item_id, quantity\n", "FROM sales_agg\"\"\"\n", "print(\"\\nfetching sales_df\")\n", "sales_df = read_sql_query(sales_query, CON_REDSHIFT)\n", "sales_df = (\n", "    sales_df.groupby(by=[\"outlet_id\", \"outlet_name\", \"city\", \"item_id\"])[\"quantity\"]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "sales_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## INVENTORY DETAILS FETCH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_query = f\"\"\"\n", "with base as\n", "(\n", "    SELECT \n", "        item_id, item_name,outlet_id,outlet_name,\n", "        actual_quantity, blocked_quantity,\n", "        case \n", "            when\n", "                actual_quantity-blocked_quantity < 0 \n", "            then 0 else \n", "            actual_quantity-blocked_quantity end as net_inv \n", "    from\n", "    (\n", "        SELECT \n", "                iii.item_id, \n", "                icd.name as item_name, \n", "                iii.outlet_id as outlet_id, \n", "                o.name as outlet_name,\n", "                max(iii.updated_at + interval '5.5 Hours') as updated_at,\n", "                iii.quantity as actual_quantity,\n", "                (COALESCE(sum(case when iibi.blocked_type in (1,2,5) then iibi.quantity else 0 end),0)) as blocked_quantity\n", "        FROM \n", "            lake_ims.ims_item_inventory iii\n", "        LEFT JOIN \n", "            lake_ims.ims_item_blocked_inventory iibi ON iii.item_id = iibi.item_id AND iii.outlet_id = iibi.outlet_id\n", "        INNER JOIN \n", "            lake_retail.console_outlet o ON iii.outlet_id = o.id and o.active = 1 and business_type_id in (1, 12, 19, 20, 7)\n", "        INNER JOIN \n", "            lake_rpc.item_category_details icd on icd.item_id = iii.item_id\n", "        WHERE \n", "            iii.outlet_id in {input_stores_list} \n", "            AND\n", "            iii.item_id in {input_skus_list}\n", "            AND\n", "            iii.active = 1\n", "            AND\n", "            iii.updated_at <= ((iii.updated_at + interval '5.5 Hours') - interval '30 Minutes')\n", "        GROUP BY \n", "            1,2,3,4,iii.updated_at,iii.quantity\n", "        ORDER BY iii.updated_at\n", "        )\n", ")\n", "\n", "\n", "select * from\n", "(\n", "    select b.outlet_id,b.outlet_name, b.item_id, pp.name as item_name,\n", "    sum(b.net_inv) as net_inv\n", "    from \n", "        base b\n", "    JOIN\n", "        lake_rpc.product_product pp\n", "    ON\n", "        b.item_id = pp.item_id\n", "    group by 1,2,3,4\n", ")\"\"\"\n", "print(\"\\nfetching inventory_df\")\n", "inventory_df = read_sql_query(inventory_query, CON_REDSHIFT)\n", "inventory_df = inventory_df.merge(\n", "    facility_mapping_df[[\"outlet_id\", \"city\"]], on=[\"outlet_id\"], how=\"left\"\n", ")\n", "inventory_df = (\n", "    inventory_df.groupby(\n", "        by=[\"outlet_id\", \"outlet_name\", \"city\", \"item_id\", \"item_name\"]\n", "    )[\"net_inv\"]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "inventory_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CREATING FINAL BASE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df = (\n", "    inventory_df[\n", "        [\"outlet_id\", \"outlet_name\", \"city\", \"item_id\", \"item_name\", \"net_inv\"]\n", "    ]\n", "    .merge(sales_df, on=[\"outlet_id\", \"outlet_name\", \"city\", \"item_id\"], how=\"left\")\n", "    .fillna(0)\n", ")\n", "\n", "final_df = final_df.merge(\n", "    input_skus[[\"item_id\", \"brand_name\"]], on=[\"item_id\"], how=\"left\"\n", ").drop_duplicates()\n", "final_df = final_df.loc[\n", "    :,\n", "    [\n", "        \"brand_name\",\n", "        \"city\",\n", "        \"outlet_id\",\n", "        \"outlet_name\",\n", "        \"item_name\",\n", "        \"net_inv\",\n", "        \"quantity\",\n", "    ],\n", "]\n", "final_df = final_df.rename(\n", "    columns={\n", "        \"brand_name\": \"Brand_Name\",\n", "        \"city\": \"City\",\n", "        \"outlet_id\": \"Outlet_Id\",\n", "        \"outlet_name\": \"Outlet_Name\",\n", "        \"item_name\": \"Item_Name\",\n", "        \"net_inv\": \"Current_Inventory\",\n", "        \"quantity\": \"Quantity_Sold\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df.head()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Store Mapping "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"18y36yyJANAcv2dWeO5Q2IQ2MBj-DbWsxmLiqJ7uXCpg\"\n", "store_mapping = pb.from_sheets(sheet_id, \"Store_Mapping\")\n", "# store_mapping = pd.read_csv('Store_Mapping.csv')\n", "store_mapping = store_mapping[\n", "    (store_mapping[\"Store ID\"].isna() == False) | (store_mapping[\"Store ID\"] != \"\")\n", "]\n", "store_mapping = store_mapping[[\"Store ID\", \"Brand\"]].drop_duplicates()\n", "store_mapping[\"Store ID\"] = store_mapping[\"Store ID\"].astype(int)\n", "store_mapping.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Email Input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1ubuZx5WCAAkhfLFue4JWMhFmhc8NoiJvlLLAI3reqkE\"\n", "mail_to_df = pb.from_sheets(sheet_id, \"external\")\n", "# mail_to_df = pd.read_csv('mail_external.csv')\n", "mail_to_df = mail_to_df[\n", "    (mail_to_df[\"email_id\"].isna() == False) | (mail_to_df[\"email_id\"] != \"\")\n", "]\n", "mail_to_df = mail_to_df[[\"brand_name\", \"email_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def df_to_html_drop(df):\n", "    html_table_summary = \"\"\"\n", "    <div class=\"mce-toc\">\n", "      <table style=\"border-collapse: collapse; width: 200px; \" border=\"1\">\n", "        <tbody>\n", "          <tr style=\"height: 5px;\">\n", "    \"\"\"\n", "\n", "    for x in df.columns:\n", "        html_table_summary += \"\"\"<td style=\"width: 200px; height: 5px; text-align: center;\"><span style=\"color: #000080;\"><strong>\"\"\"\n", "        html_table_summary += x\n", "        html_table_summary += \"\"\"</strong></span></td>\"\"\"\n", "\n", "    html_table_summary += \"\"\"</tr>\"\"\"\n", "\n", "    for i, r in df.iterrows():\n", "        html_table_summary += \"\"\"<tr style=\"height: 10px;\">\"\"\"\n", "        for x in df.columns:\n", "            html_table_summary += (\n", "                \"\"\"<td style=\"width: 200px; height: 5px; text-align: center;\">\"\"\"\n", "            )\n", "            html_table_summary += str(r[x])\n", "            html_table_summary += \"\"\"</td>\"\"\"\n", "        html_table_summary += \"\"\"</tr>\"\"\"\n", "    html_table_summary += \"\"\"\n", "        </tbody>\n", "      </table>\n", "    </div>\n", "    \"\"\"\n", "    return html_table_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_func(x, final_df, mail_to_df, switch=False):\n", "\n", "    store_mapping_list = tuple(\n", "        set(store_mapping[store_mapping[\"Brand\"] == str(x)][\"Store ID\"].to_list())\n", "    )\n", "    test_df = (\n", "        final_df[\n", "            (final_df[\"Brand_Name\"] == str(x))\n", "            & (final_df[\"Outlet_Id\"].isin(store_mapping_list))\n", "        ]\n", "        .reset_index()\n", "        .drop(columns={\"index\"})\n", "    )\n", "    test_df = test_df.sort_values(by=[\"City\"], ascending=[False])\n", "\n", "    Item_level_report = test_df[\n", "        [\n", "            \"Brand_Name\",\n", "            \"City\",\n", "            \"Outlet_Name\",\n", "            \"Item_Name\",\n", "            \"Current_Inventory\",\n", "            \"Quantity_Sold\",\n", "        ]\n", "    ]\n", "    Item_level_report.to_csv(\"Item_level_report.csv\", index=False)\n", "\n", "    test_df = (\n", "        test_df.groupby(by=[\"Brand_Name\", \"City\", \"Outlet_Name\"])\n", "        .agg({\"Current_Inventory\": \"sum\", \"Quantity_Sold\": \"sum\"})\n", "        .reset_index()\n", "    )\n", "\n", "    if switch == True:\n", "        # to_email = list({\"<EMAIL>\"})\n", "        # cc_email = list({'<EMAIL>','<EMAIL> '})\n", "        to_email = list(\n", "            set(mail_to_df[mail_to_df[\"brand_name\"] == str(x)][\"email_id\"].to_list())\n", "        )\n", "        # cc_email = tuple(set(mail_cc_df[mail_cc_df[\"manf_id\"] == x]['cc_to'].to_list()))\n", "\n", "    if switch == False:\n", "        to_email = list({\"<EMAIL>\"})\n", "    # cc_email = list({'<EMAIL>'})\n", "    # to_email = list({'shu<PERSON><PERSON><PERSON><PERSON>ban<PERSON>@zomato.com'}) ##,'<EMAIL>','<EMAIL>'})\n", "    # cc_email = list({'shu<PERSON><PERSON><PERSON><PERSON>ban<PERSON>@zomato.com'}) ##,'<EMAIL>','<EMAIL>'})\n", "\n", "    return test_df, to_email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "start_date = pd.to_datetime(datetime.datetime.now()) + timed<PERSON>ta(hours=4)\n", "end_date = pd.to_datetime(datetime.datetime.now()) + timed<PERSON>ta(hours=5)\n", "\n", "\n", "def ampm(date):\n", "    if int(datetime.datetime.strftime(start_date, \"%H\")) <= 11:\n", "        return \" AM \"\n", "    else:\n", "        return \" PM \"\n", "\n", "\n", "start_date_str = ampm(start_date)\n", "end_date_str = ampm(end_date)\n", "\n", "start_date_ = datetime.datetime.strftime(start_date, \"%d-%b-%Y %H\") + start_date_str\n", "end_date_ = datetime.datetime.strftime(end_date, \"%d-%b-%Y %H\") + end_date_str"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_flag = \" \" + str(start_date_) + \" to \" + str(end_date_)\n", "date_flag"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in list(input_skus[\"brand_name\"].unique()):\n", "    print(i)\n", "    from_email = \"<EMAIL>\"\n", "    summary = pd.DataFrame()\n", "    summary, to_email = get_func(i, final_df, mail_to_df, switch=True)\n", "    subject = str(i) + \" - Blinkit Sales and Inventory Report \"\n", "    files = [\"Item_level_report.csv\"]\n", "    print(to_email)\n", "    html_table_summary = df_to_html_drop(summary)\n", "    html_content = f\"Hi Team,<br>Blinkit has developed a report to measure the sales and inventory of the brand across the platform. The intention behind this is to provide brand with a constant update across the products Last 3 Hours Sales and Current Inventory.<br>Please refer below the aggreated details of sales between {date_flag} across various cities and its current inventory after the sales.<br><br>{html_table_summary}<br><br>Best Regards,<br>Data Inventory Team\"\n", "    pb.send_email(from_email, to_email, subject, html_content=html_content, files=files)\n", "    time.sleep(10)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}