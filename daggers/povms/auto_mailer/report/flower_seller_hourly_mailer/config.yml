alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: flower_seller_hourly_mailer
dag_type: report
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-primary-eks-role
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U057BEV1H8Q
path: povms/auto_mailer/report/flower_seller_hourly_mailer
paused: false
project_name: auto_mailer
schedule:
  end_date: '2023-10-22T00:00:00'
  interval: 30 */1 * * *
  start_date: '2023-08-05T05:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 9
pool: povms_pool
