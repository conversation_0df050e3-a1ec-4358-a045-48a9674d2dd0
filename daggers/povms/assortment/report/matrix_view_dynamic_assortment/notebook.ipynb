{"cells": [{"cell_type": "code", "execution_count": null, "id": "642663e5-99ca-4838-8b55-2507b050c314", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import datetime as dt\n", "import time\n", "import numpy as np\n", "from calendar import monthrange\n", "from datetime import timedelta, datetime"]}, {"cell_type": "code", "execution_count": null, "id": "57a422f8-0fdd-4a03-8f09-586944941400", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(20)"]}, {"cell_type": "code", "execution_count": null, "id": "9d04c315-5a14-489b-85f3-d10a3a9562f3", "metadata": {}, "outputs": [], "source": ["CON_PRESTO = pb.get_connection(\"[Warehouse] Presto\")\n", "# retail = pb.get_connection(\"retail\")\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "id": "4731d699-d87b-4d21-bc86-0f2e5bec6f97", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "80c1fabf-2f49-47f9-98cb-ad639d45f5a3", "metadata": {}, "outputs": [], "source": ["city_mapping_sql = f\"\"\"select distinct id as city_id, name as city_name\n", "from lake_retail.console_location\"\"\"\n", "city_mapping = read_sql_query(city_mapping_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "f4209c3b-d1fd-4e0d-9ee5-62fa5441c8cb", "metadata": {}, "outputs": [], "source": ["facility_mapping_sql = f\"\"\"select distinct id as facility_id, name as facility_name\n", "from lake_crates.facility\"\"\"\n", "facility_mapping = read_sql_query(facility_mapping_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "d6aaed27-404a-4715-bfb3-122060ef16f5", "metadata": {}, "outputs": [], "source": ["item_details_sql = \"\"\"\n", "    WITH item_level_info AS (\n", "        SELECT item_id, name AS item_name, brand, brand_id, manufacturer, variant_description, is_pl, variant_mrp,\n", "        ROW_NUMBER() OVER (PARTITION BY item_id ORDER BY updated_at DESC) AS row_rank\n", "        FROM lake_rpc.product_product\n", "    ),\n", "    categories AS (\n", "        SELECT P.ID AS PID, P.NAME AS PRODUCT, P.UNIT AS Unit,\n", "        C2.NAME AS L2,\n", "        (CASE \n", "            WHEN C1.NAME = C.name THEN C2.name \n", "            ELSE C1.name \n", "        END) AS L1,\n", "        C.NAME AS L0, P.BRAND AS brand, P.MANUFACTURER AS manf, pt.name AS product_type\n", "        FROM lake_cms.gr_product P\n", "        INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "        INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "        AND PCM.IS_PRIMARY=TRUE\n", "        INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "        INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "        INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id\n", "    ), \n", "\n", "    category_pre AS (\n", "        SELECT item_id, product_id, brand, cat.l0, cat.l1, cat.l2, cat.product_type, manf \n", "        FROM lake_rpc.item_product_mapping  rpc\n", "        INNER JOIN categories cat ON rpc.product_id=cat.pid\n", "        AND rpc.offer_id IS NULL\n", "        AND rpc.item_id IS NOT NULL\n", "        AND rpc.product_id IS NOT NULL\n", "    ),\n", "\n", "    ptype_tbl AS (\n", "        SELECT item_id, MAX(brand) AS brand, MAX(l0) AS l0, MAX(l1) AS l1,\n", "        MAX(l2) AS l2, MAX(product_type) AS ptype,\n", "        MAX(manf) AS manufacturer\n", "        FROM category_pre\n", "        GROUP BY 1\n", "    )\n", "\n", "    SELECT DISTINCT p.item_id, p.name AS item_name,l0, l1, l2, manufacturer\n", "    FROM lake_rpc.item_details p\n", "    LEFT JOIN ptype_tbl pt ON p.item_id = pt.item_id\n", "    WHERE p.active = 1 AND p.approved = 1\n", "    \"\"\"\n", "item_details = read_sql_query(item_details_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "16d600ba-1843-4dd3-9e46-bb46a2c65964", "metadata": {}, "outputs": [], "source": ["master_assortment_sql = f\"\"\"select distinct city_id, facility_id, item_id, master_assortment_substate_id\n", "from lake_rpc.product_facility_master_assortment\n", "where active = 1\n", "and facility_id in (select distinct facility_id from lake_retail.console_outlet where active = 1 and business_type_id = 7)\n", "and city_id in (1,9,7,37,10,142,8)\"\"\"\n", "master_assortment = read_sql_query(master_assortment_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "35b75806-9368-45ea-9523-2ba121e21db0", "metadata": {}, "outputs": [], "source": ["city_master_assortment_sql = f\"\"\"select distinct\n", "    item_id, city_id, master_assortment_substate_id as city_assortment_state\n", "from \n", "    lake_rpc.product_city_assortment_suggestion a\n", "left join\n", "    lake_retail.console_location cl on cl.id = a.city_id\n", "where\n", "    master_assortment_substate_id !=2 \n", "and \n", "    a.city_id in (1,9,7,37,10,142,8)\"\"\"\n", "city_master_assortment = read_sql_query(city_master_assortment_sql, CON_REDSHIFT)"]}, {"cell_type": "code", "execution_count": null, "id": "f7156e33-f15d-4fe1-ba2c-f17d082dacc3", "metadata": {}, "outputs": [], "source": ["item_mapping_sql = f\"\"\"with base as \n", "(\n", "    SELECT \n", "        distinct item_id, 'FnV' as assortment_type\n", "    FROM \n", "        lake_rpc.item_category_details\n", "    WHERE l0_id = 1487\n", "\n", "    UNION ALL\n", "\n", "    SELECT\n", "        distinct item_id, 'Perishable' as assortment_type\n", "    FROM\n", "        lake_rpc.item_details\n", "    WHERE\n", "       perishable = 1\n", "    AND item_id not in \n", "        (SELECT \n", "            distinct item_id\n", "        FROM \n", "            lake_rpc.item_category_details\n", "        WHERE l0_id = 1487)\n", ")\n", "\n", "SELECT * FROM base\"\"\"\n", "item_mapping = read_sql_query(item_mapping_sql, CON_REDSHIFT)"]}, {"cell_type": "raw", "id": "5a89c846-5630-40da-bd98-82f62df53fb8", "metadata": {}, "source": ["item_mapping_sql = f\"\"\"\n", "SELECT \n", "        distinct item_id, l0\n", "    FROM \n", "        lake_rpc.item_category_details\"\"\"\n", "item_mapping = read_sql_query(item_mapping_sql, CON_REDSHIFT)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9cb7801b-fdb4-4a82-b225-6bb0f87c2fde", "metadata": {}, "outputs": [], "source": ["# item_mapping[['l0']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "58e9c398-003d-4baa-a303-ec21d0829c81", "metadata": {}, "outputs": [], "source": ["master_assortment.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "bd1a474e-c338-4918-a921-d3598260c134", "metadata": {}, "outputs": [], "source": ["x = pd.merge(\n", "    city_master_assortment,\n", "    master_assortment[[\"city_id\", \"facility_id\"]].drop_duplicates(),\n", "    on=[\"city_id\"],\n", "    how=\"left\",\n", ")\n", "x = pd.merge(\n", "    x,\n", "    master_assortment.drop(columns={\"city_id\"}),\n", "    on=[\"facility_id\", \"item_id\"],\n", "    how=\"left\",\n", ")\n", "x.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "f5f2ae0e-065a-41fd-af09-b3a3df6afef5", "metadata": {}, "outputs": [], "source": ["x.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "7a7443a1-fe62-4eff-8da0-3cad55918751", "metadata": {}, "outputs": [], "source": ["city_master_assortment.shape[0], master_assortment.shape[0], x.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "eeba0ab3-f034-45be-a966-4ede4ab8066d", "metadata": {}, "outputs": [], "source": ["x[x.city_id == 7][[\"item_id\", \"city_assortment_state\"]].drop_duplicates().groupby(\n", "    [\"city_assortment_state\"]\n", ").agg({\"item_id\": \"nunique\"}).reset_index()"]}, {"cell_type": "raw", "id": "8698a2ac-a53e-430b-8de3-b925f6b3c693", "metadata": {}, "source": ["master_assortment = pd.merge(master_assortment, master_assortment[master_assortment.master_assortment_substate_id == 1][['city_id','item_id']].drop_duplicates(),\n", "                             on = ['city_id','item_id'], how = 'inner')"]}, {"cell_type": "code", "execution_count": null, "id": "ad07e0a2-f97e-493e-a0cd-fc3a4853cdc3", "metadata": {}, "outputs": [], "source": ["master_assortment = x.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e1c95b96-4fbc-4218-9702-2152618c7124", "metadata": {}, "outputs": [], "source": ["master_assortment[\"master_assortment_substate_id\"] = master_assortment[\n", "    \"master_assortment_substate_id\"\n", "].<PERSON>na(-9)"]}, {"cell_type": "code", "execution_count": null, "id": "3f873272-c852-47ce-b138-5a3e645ea845", "metadata": {}, "outputs": [], "source": ["master_assortment = pd.merge(master_assortment, city_mapping, on=[\"city_id\"], how=\"left\")\n", "master_assortment = pd.merge(master_assortment, item_details, on=[\"item_id\"], how=\"left\")\n", "master_assortment = pd.merge(master_assortment, facility_mapping, on=[\"facility_id\"], how=\"left\")\n", "master_assortment[\"assortment_state\"] = np.where(\n", "    master_assortment[\"master_assortment_substate_id\"] == 1,\n", "    \"Active\",\n", "    np.where(\n", "        (master_assortment[\"master_assortment_substate_id\"] == 2)\n", "        | (master_assortment[\"master_assortment_substate_id\"] == -9),\n", "        \"Inactive\",\n", "        np.where(\n", "            master_assortment[\"master_assortment_substate_id\"] == 3,\n", "            \"Temp Inactive\",\n", "            \"Inactive\",\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "246bf061-043e-4e62-961c-8f3110ab5394", "metadata": {}, "outputs": [], "source": ["master_assortment.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "3f6b9993-5d1d-4e0f-b0a5-f9eb07ea52d1", "metadata": {}, "outputs": [], "source": ["master_assortment[master_assortment[\"master_assortment_substate_id\"] == -9]"]}, {"cell_type": "code", "execution_count": null, "id": "d0df55bf-b732-4c76-97f5-fe8fcf7fa114", "metadata": {}, "outputs": [], "source": ["master_assortment[\"facility_id\"] = master_assortment[\"facility_id\"].astype(int)\n", "\n", "master_assortment[\"facility_name\"] = (\n", "    master_assortment[\"facility_id\"].astype(str) + \" - \" + master_assortment[\"facility_name\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fc38b70a-2d9b-4e21-a9db-6d1bf521bd8f", "metadata": {}, "outputs": [], "source": ["unique_cities = (\n", "    master_assortment[[\"city_name\"]].drop_duplicates().reset_index().drop(columns={\"index\"})\n", ")\n", "# unique_cities = unique_cities[unique_cities.city_name == 'Bengaluru'].reset_index().drop(columns = {'index'})\n", "unique_cities\n", "unique_cities"]}, {"cell_type": "code", "execution_count": null, "id": "1ff9969c-a08a-4060-b302-f39de2298720", "metadata": {}, "outputs": [], "source": ["input_sheet = \"1bjxsC4u2E4iFBDkrBIue4lUEH9ZnMv7vRCdw9CvQvew\"\n", "sheet_city_mapping = pb.from_sheets(input_sheet, \"input\")\n", "sheet_city_mapping"]}, {"cell_type": "code", "execution_count": null, "id": "f1ac9ce3-6885-4ca7-85a1-62b81e821f35", "metadata": {}, "outputs": [], "source": ["item_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "de52f184-a300-4c36-821b-3cb374e6113e", "metadata": {}, "outputs": [], "source": ["city_master_assortment[\n", "    (city_master_assortment.item_id == 10002144) & (city_master_assortment.city_id == 7)\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "7d73ed86-4916-43ca-b591-84dddf9dd751", "metadata": {}, "outputs": [], "source": ["master_assortment[\"city_assortment_state\"] = np.where(\n", "    master_assortment[\"city_assortment_state\"] == 1,\n", "    \"Active\",\n", "    np.where(\n", "        (master_assortment[\"city_assortment_state\"] == 2)\n", "        | (master_assortment[\"city_assortment_state\"] == -9),\n", "        \"Inactive\",\n", "        np.where(master_assortment[\"city_assortment_state\"] == 3, \"Temp Inactive\", \"Inactive\"),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35d7650b-14ea-420b-aad6-d34565d5ea59", "metadata": {}, "outputs": [], "source": ["master_assortment"]}, {"cell_type": "code", "execution_count": null, "id": "b609c490-1f29-4052-9b3a-519cc5ca3811", "metadata": {}, "outputs": [], "source": ["master_assortment = pd.merge(master_assortment, item_mapping, on=[\"item_id\"], how=\"left\")\n", "master_assortment[\"assortment_type\"] = master_assortment[\"assortment_type\"].fillna(\"Packaged Goods\")"]}, {"cell_type": "code", "execution_count": null, "id": "281e3c0f-43d6-49d2-8349-48307b85623f", "metadata": {}, "outputs": [], "source": ["x = master_assortment[master_assortment.city_name == \"Delhi\"][\n", "    [\n", "        \"city_name\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"manufacturer\",\n", "        \"city_assortment_state\",\n", "        \"master_assortment_substate_id\",\n", "        \"assortment_state\",\n", "        \"assortment_type\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ef09568a-d67b-46c9-bb93-9878467ad1c4", "metadata": {}, "outputs": [], "source": ["x[(x.item_id == 10002144)]"]}, {"cell_type": "code", "execution_count": null, "id": "e29fc740-0cbd-4944-a414-9706a39dfe17", "metadata": {}, "outputs": [], "source": ["master_assortment.columns"]}, {"cell_type": "code", "execution_count": null, "id": "d3335b9f-c311-4967-a88b-2ff2560a83b8", "metadata": {}, "outputs": [], "source": ["# x"]}, {"cell_type": "code", "execution_count": null, "id": "5385f9b3-2154-47a1-8962-20d44cf6edb1", "metadata": {}, "outputs": [], "source": ["for i in range(unique_cities.shape[0]):\n", "    city_name = unique_cities.iloc[:, 0][i]\n", "    print(\"Running for \", city_name)\n", "    x = master_assortment[master_assortment.city_name == city_name][\n", "        [\n", "            \"city_name\",\n", "            \"facility_id\",\n", "            \"facility_name\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"manufacturer\",\n", "            \"city_assortment_state\",\n", "            \"master_assortment_substate_id\",\n", "            \"assortment_state\",\n", "            \"assortment_type\",\n", "        ]\n", "    ]\n", "\n", "    x = x.pivot_table(\n", "        index=[\n", "            \"city_name\",\n", "            \"assortment_type\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"manufacturer\",\n", "            \"city_assortment_state\",\n", "        ],\n", "        columns=[\"facility_name\"],\n", "        values=[\"assortment_state\"],\n", "        aggfunc=lambda x: \" \".join(x),\n", "        fill_value=\"Inactive\",\n", "    ).reset_index()\n", "    print(x.shape[0], x.shape[0] * x.shape[1])\n", "    sheet_id = \"1krGK31To7_M-BR9EMxqbBdIVX0_n6aCaUqi3byApQ3w\"  # sheet_city_mapping[sheet_city_mapping.City == city_name].reset_index().drop(columns = {'index'}).iloc[:, 1][0]\n", "    print(\"write to sheet\\n\")\n", "    pb.to_sheets(x, sheet_id, city_name)"]}, {"cell_type": "raw", "id": "e7ea7caf-aaa8-4188-b13e-57f8b870ff3d", "metadata": {}, "source": ["502488 + "]}, {"cell_type": "code", "execution_count": null, "id": "a9c00e60-036c-4fa7-9db7-ce299f5e4182", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}