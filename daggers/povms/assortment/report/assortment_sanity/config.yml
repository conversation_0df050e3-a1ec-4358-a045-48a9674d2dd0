dag_name: assortment_sanity
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U01JWCA6AJG
path: povms/assortment/report/assortment_sanity
paused: true
pool: povms_pool
project_name: assortment
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 6 * * *
  start_date: '2021-12-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- assortment_sanity_email.html
tags: []
template_name: notebook
version: 22
