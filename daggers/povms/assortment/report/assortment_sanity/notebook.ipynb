{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ignoring warning\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing Libraries\n", "import sqlalchemy as sqla\n", "import pandas as pd\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "\n", "\n", "# import pandas_redshift as pr\n", "# from gr_mailer import send_email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "con = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_skip_df = pb.from_sheets(\n", "    sheetid=\"1jqAHMcON3SoTBj2HtBYwQqUhc-U6KejAem2mthPfv_c\",\n", "    sheetname=\"assortment _sanity_facility_skip\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exclusion_facility_list = facility_skip_df[\"facility_id\"].astype(int).unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["map_query = \"\"\"\n", "select distinct map.*,\n", "case when map.facility_id in (116,41,46) then 'bulk' else 'frontend' end as facility_flag\n", "from \n", "(\n", "select distinct final.*,\n", "case when trf.tag_type_id=8 then 'yes' else 'no' end as transfer_flag\n", "from\n", "(\n", "select distinct imf.*,\n", "load_size,\n", "case_sensitivity_type\n", "from\n", "\n", "(\n", "select distinct item_maf.*,\n", "po_cycle,\n", "po_days\n", "from\n", "\n", "(\n", "select distinct\n", "ven_fi.*,\n", "manufacturer_id\n", "from\n", "\n", "(\n", "select distinct\n", "base.*,\n", "vendor_id\n", "from\n", "(\n", "select \n", "a.item_id,\n", "a.facility_id\n", "FROM rpc.product_facility_master_assortment AS a\n", "left join retail.console_outlet as b\n", "left join retail.console_company_type as d on b.company_type_id=d.id\n", "on a.facility_id=b.facility_id\n", "left join (select  item_id, max(active) active, max(approved) approved \n", "                    from  rpc.product_product \n", "                    where active=1 group by 1) as c on c.item_id=a.item_id\n", "where b.active=1\n", "and b.device_id<>47\n", "and upper(d.entity_type) like '%%B2B_INDEPENDENT%%'\n", "and c.active=1\n", "and c.approved=1\n", "and a.master_assortment_substate_id=1\n", "group by 1,2\n", ") as base\n", "left join \n", "(\n", "  select distinct\n", "ven.item_id,\n", "ven.facility_id,\n", "ven.vendor_id\n", "from\n", "(\n", "select \n", "item_id,\n", "a.facility_id,\n", "vendor_id,\n", "a.updated_at\n", "from vms.vms_vendor_facility_alignment as a\n", "where a.active=1\n", ") as ven\n", "inner join \n", "(\n", "select \n", "item_id,\n", "a.facility_id,\n", "max(a.updated_at)as updated_at\n", "from vms.vms_vendor_facility_alignment as a\n", "where a.active=1\n", "group by 1,2) as inn\n", "on ven.item_id=inn.item_id\n", "and ven.facility_id=inn.facility_id\n", "and ven.updated_at=inn.updated_at\n", "  ) as vendor\n", "  on base.item_id=vendor.item_id\n", "  and base.facility_id=vendor.facility_id\n", "  )as ven_fi\n", "  left join \n", "(\n", "select \n", "      p.item_id,pm.id as manufacturer_id\n", "FROM\n", "    (select distinct item_id, active, brand_id from rpc.product_product where active=1) p\n", "INNER JOIN rpc.product_brand as pb on pb.id=p.brand_id\n", "INNER JOIN rpc.product_manufacturer as pm on pm.id=pb.manufacturer_id\n", "WHERE\n", "     pb.active=1\n", "    and pm.active=1\n", "    group by 1,2\n", "    ) as b\n", "    on ven_fi.item_id=b.item_id\n", "    )item_maf\n", "    left join vms.vendor_manufacturer_physical_facility_attributes as po\n", "on item_maf.vendor_id=po.vendor_id\n", "and item_maf.manufacturer_id=po.manufacturer_id\n", "and item_maf.facility_id=po.facility_id\n", "and po.active=1  \n", ")as imf\n", "left join \n", "(\n", "select \n", "a.vendor_id,\n", "a.facility_id,\n", "a.load_size\n", "from vms.vendor_physical_facility_attributes as a\n", "inner join  (select vendor_id,facility_id,max(updated_at) as updated_at \n", "from vms.vendor_physical_facility_attributes\n", "group by 1,2)as b\n", "on a.vendor_id=b.vendor_id\n", "and a.facility_id=b.facility_id\n", "and a.updated_at=b.updated_at \n", ")as vpf\n", "on imf.vendor_id=vpf.vendor_id\n", "and imf.facility_id=vpf.facility_id\n", "left join vms.vendor_item_physical_facility_attributes as vip\n", "on imf.item_id=vip.item_id\n", "and imf.vendor_id=vip.vendor_id\n", "and imf.facility_id=vip.facility_id\n", "-- where vpf.active=1\n", "-- and vip.active=1\n", ")final\n", "left join\n", "(\n", "select distinct item_id,\n", "facility_id,\n", "tag_type_id\n", "from rpc.item_outlet_tag_mapping as rio \n", "left join retail.console_outlet as rco \n", "on rio.outlet_id=rco.id\n", "where tag_type_id=8\n", "and rco.active=1\n", "and rio.active=1\n", ")trf\n", "on final.item_id=trf.item_id\n", "and final.facility_id=trf.facility_id\n", ")map\n", "where map.facility_id not in %(exclusion_facility_list)s\n", "and map.item_id not in (10000403,\n", "10002306,\n", "10003405,\n", "10004424,\n", "10004714,\n", "10005631,\n", "10006522,\n", "10007279,\n", "10007393,\n", "10007977,\n", "10008320,\n", "10009072,\n", "10012292,\n", "10016597,\n", "10016670,\n", "10016685,\n", "10016707,\n", "10016928,\n", "10017044,\n", "10017839,\n", "10018126,\n", "10019942,\n", "10021772,\n", "10022989,\n", "10022991,\n", "10022992,\n", "10022993,\n", "10022994,\n", "10023320,\n", "10024011,\n", "10024018,\n", "10024024,\n", "10024304,\n", "10026418,\n", "10026679,\n", "10029481,\n", "10030957,\n", "10032309,\n", "10033229,\n", "10033606,\n", "10034108,\n", "10034118,\n", "10034425,\n", "10034533,\n", "10034885,\n", "10034930,\n", "10034954,\n", "10034983,\n", "10036062,\n", "10036679,\n", "10036739,\n", "10036741,\n", "10036742,\n", "10037170,\n", "10037426,\n", "10037730,\n", "10037899,\n", "10038587,\n", "10039726,\n", "10039727,\n", "10039730,\n", "10039731,\n", "10039733,\n", "10039738,\n", "10039740,\n", "10040040,\n", "10040789,\n", "10041839,\n", "10041979,\n", "10041980,\n", "10041981,\n", "10043075,\n", "10043427,\n", "10043428,\n", "10043429,\n", "10043430,\n", "10043732,\n", "10044227,\n", "10044458,\n", "10045801,\n", "10046500)\n", "group by 1,2,3,4,5,6,7,8,9,10\n", "\"\"\"\n", "\n", "map_data = pd.read_sql_query(\n", "    map_query,\n", "    con=con,\n", "    params={\"exclusion_facility_list\": tuple(exclusion_facility_list)},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_query = \"\"\"select distinct\n", "inn.item_id,\n", "inn.vendor_id,\n", "inn.facility_id,\n", "inn.tat_days\n", "from\n", "(\n", "select distinct\n", "item_id,\n", "vendor_id,\n", "facility_id,\n", "tat_days,\n", "a.updated_at\n", "from po.item_outlet_vendor_tat as a\n", "left join retail.console_outlet as b\n", "on a.outlet_id=b.id\n", "where b.active=1\n", ") inn\n", "inner join \n", "(\n", "select distinct\n", "item_id,\n", "vendor_id,\n", "facility_id,\n", "max(a.updated_at) as updated_at\n", "from po.item_outlet_vendor_tat as a\n", "left join retail.console_outlet as b\n", "on a.outlet_id=b.id\n", "where b.active=1\n", "group by 1,2,3\n", ") as up\n", "on inn.item_id=up.item_id\n", "and inn.facility_id=up.facility_id\n", "and inn.vendor_id=up.vendor_id\n", "and inn.updated_at=up.updated_at\n", "\"\"\"\n", "\n", "tat_data = pd.read_sql_query(tat_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_1 = pd.merge(\n", "    map_data, tat_data, on=[\"facility_id\", \"item_id\", \"vendor_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"\n", "select \n", "b.*,\n", "a.facility_id\n", "from\n", "(\n", "select a.item_id,\n", "b.id as outlet_id,\n", "a.facility_id\n", "from rpc.product_facility_master_assortment as a\n", "left join retail.console_outlet as b\n", "on b.facility_id=a.facility_id\n", "where b.active=1\n", "and device_id!=47\n", "and company_type_id=4\n", "and master_assortment_substate_id=1\n", "and a.facility_id not in %(exclusion_facility_list)s\n", "group by 1,2\n", ")as a\n", "left join\n", "(\n", "select \n", "item_id,\n", "outlet_id,\n", "cpd,\n", "updated_at\n", "from\n", "(\n", "select \n", "a.item_id,\n", "a.outlet_id,\n", "b.name as outlet_name,\n", "a.updated_at,\n", "a.cpd,\n", "case when upper(b.name) like '%%COLD%%' then \"Cold\" \n", "                                     else \"Regular\" end as outlet_storage_type,\n", "case when c.storage_type in (2,3,6,7) then \"Cold\"\n", "\t\t\t\t\t\t\t\t\telse \"Regular\"\n", "\t\t\t\t\t\t\t\tend as item_storage_type\n", "from snorlax.default_cpd as a\n", "inner join\n", "(\n", "select \n", "item_id,\n", "outlet_id,\n", "max(updated_at) as updated_at\n", "from snorlax.default_cpd as a\n", "group by 1,2\n", ") as d on a.item_id=d.item_id\n", "and a.outlet_id=d.outlet_id\n", "and a.updated_at=d.updated_at\n", "left join retail.console_outlet as b\n", "on a.outlet_id=b.id\n", "left join (select  rr.item_id, active, approved, storage_type\n", "                    from  rpc.product_product rr\n", "                    join (select  item_id, max(id) id from rpc.product_product group by 1) tt on rr.item_id=tt.item_id and rr.id=tt.id\n", "                    where active=1) as c \n", "on c.item_id=a.item_id\n", "where b.active=1\n", "and c.active=1\n", "and c.approved=1\n", "and  b.device_id<>47\n", "and b.company_type_id=4\n", "group by 1,2,3,4,5,6,7\n", ") as inn\n", "where outlet_storage_type=item_storage_type\n", "and item_id not in (10000403,\n", "10002306,\n", "10003405,\n", "10004424,\n", "10004714,\n", "10005631,\n", "10006522,\n", "10007279,\n", "10007393,\n", "10007977,\n", "10008320,\n", "10009072,\n", "10012292,\n", "10016597,\n", "10016670,\n", "10016685,\n", "10016707,\n", "10016928,\n", "10017044,\n", "10017839,\n", "10018126,\n", "10019942,\n", "10021772,\n", "10022989,\n", "10022991,\n", "10022992,\n", "10022993,\n", "10022994,\n", "10023320,\n", "10024011,\n", "10024018,\n", "10024024,\n", "10024304,\n", "10026418,\n", "10026679,\n", "10029481,\n", "10030957,\n", "10032309,\n", "10033229,\n", "10033606,\n", "10034108,\n", "10034118,\n", "10034425,\n", "10034533,\n", "10034885,\n", "10034930,\n", "10034954,\n", "10034983,\n", "10036062,\n", "10036679,\n", "10036739,\n", "10036741,\n", "10036742,\n", "10037170,\n", "10037426,\n", "10037730,\n", "10037899,\n", "10038587,\n", "10039726,\n", "10039727,\n", "10039730,\n", "10039731,\n", "10039733,\n", "10039738,\n", "10039740,\n", "10040040,\n", "10040789,\n", "10041839,\n", "10041979,\n", "10041980,\n", "10041981,\n", "10043075,\n", "10043427,\n", "10043428,\n", "10043429,\n", "10043430,\n", "10043732,\n", "10044227,\n", "10044458,\n", "10045801,\n", "10046500)\n", "group by 1,2,3,4\n", ") as b\n", "on a.item_id=b.item_id\n", "and a.outlet_id=b.outlet_id\n", "group by 1,2,3,4,5\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value2 = pd.read_sql_query(\n", "    sql=query2,\n", "    con=con,\n", "    params={\"exclusion_facility_list\": tuple(exclusion_facility_list)},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value2[\"group_rank\"] = assortment_value2.groupby([\"item_id\", \"facility_id\"])[\n", "    \"updated_at\"\n", "].rank(\n", "    ascending=0, method=\"dense\"\n", ")  # .reset_index()\n", "# assortment_value2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final = assortment_value2[assortment_value2.group_rank == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_final1 = cpd_final.drop_duplicates([\"item_id\", \"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value = assortment_value_1.merge(cpd_final1, on=[\"item_id\", \"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_without_na = assortment_value[assortment_value.po_days.notna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_with_na = assortment_value[assortment_value.po_days.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_without_na[\"po_days_length\"] = assortment_value_po_without_na.apply(\n", "    lambda x: len(x[\"po_days\"].split(\",\")), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_full = pd.concat([assortment_value_po_without_na, assortment_value_po_with_na])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total = assortment_value.groupby([\"facility_id\"])[\"item_id\"].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total = total.rename(columns={\"item_id\": \"total_item\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero = (\n", "    assortment_value[\n", "        (assortment_value.cpd == 0)\n", "        & (assortment_value.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero = cpd_zero.rename(columns={\"item_id\": \"cpd_zero\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_per = total.merge(cpd_zero, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_per[\"cpd_zero_per\"] = round(((cpd_zero_per.cpd_zero / cpd_zero_per.total_item) * 100), 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_transfer = (\n", "    assortment_value[(assortment_value.cpd == 0) & (assortment_value.facility_flag == \"frontend\")]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_transfer = cpd_zero_transfer.rename(columns={\"item_id\": \"cpd_zero_transfer\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_per_transfer = cpd_zero_per.merge(cpd_zero_transfer, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_per_transfer[\"cpd_zero_transfer_per\"] = round(\n", "    ((cpd_zero_per_transfer.cpd_zero_transfer / cpd_zero_per_transfer.total_item) * 100),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing = (\n", "    assortment_value[\n", "        (assortment_value.cpd.isna())\n", "        & (assortment_value.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing = cpd_missing.rename(columns={\"item_id\": \"cpd_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_merge = cpd_zero_per_transfer.merge(cpd_missing, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_merge[\"cpd_missing_per\"] = round(\n", "    ((cpd_missing_merge.cpd_missing / cpd_missing_merge.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_transfer = (\n", "    assortment_value[(assortment_value.cpd.isna()) & (assortment_value.facility_flag == \"frontend\")]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_transfer = cpd_missing_transfer.rename(columns={\"item_id\": \"cpd_missing_transfer\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_merge_transfer = cpd_missing_merge.merge(\n", "    cpd_missing_transfer, on=[\"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_merge_transfer[\"cpd_missing_per_transfer\"] = round(\n", "    (\n", "        (cpd_missing_merge_transfer.cpd_missing_transfer / cpd_missing_merge_transfer.total_item)\n", "        * 100\n", "    ),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aligned_vendor = (\n", "    assortment_value[\n", "        (assortment_value.vendor_id.isna())\n", "        & (assortment_value.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aligned_vendor = aligned_vendor.rename(columns={\"item_id\": \"aligned_vendor_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_missing_per = cpd_missing_merge_transfer.merge(aligned_vendor, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_missing_per[\"aligned_vendor_missing_per\"] = round(\n", "    ((vendor_missing_per.aligned_vendor_missing / vendor_missing_per.total_item) * 100),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size = (\n", "    assortment_value[\n", "        (assortment_value.load_size.isna())\n", "        & (assortment_value.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size = load_size.rename(columns={\"item_id\": \"load_size_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_missing_per = vendor_missing_per.merge(load_size, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_missing_per[\"load_size_missing_per\"] = round(\n", "    ((load_size_missing_per.load_size_missing / load_size_missing_per.total_item) * 100),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type = (\n", "    assortment_value[\n", "        (assortment_value.case_sensitivity_type.isna()) & (assortment_value.transfer_flag == \"no\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type = case_sensitivity_type.rename(\n", "    columns={\"item_id\": \"case_sensitivity_type_missing\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_per = load_size_missing_per.merge(\n", "    case_sensitivity_type, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_per[\"case_sensitivity_missing_per\"] = round(\n", "    (\n", "        (\n", "            case_sensitivity_type_per.case_sensitivity_type_missing\n", "            / case_sensitivity_type_per.total_item\n", "        )\n", "        * 100\n", "    ),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days = (\n", "    assortment_value[\n", "        (assortment_value.tat_days.isna())\n", "        & (assortment_value.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days = tat_days.rename(columns={\"item_id\": \"tat_days_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_per = case_sensitivity_type_per.merge(tat_days, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_per[\"tat_days_missing_per\"] = round(\n", "    ((tat_days_per.tat_days_missing / tat_days_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle = (\n", "    assortment_value[\n", "        (assortment_value.po_cycle.isna())\n", "        & (assortment_value.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle = po_cycle.rename(columns={\"item_id\": \"po_cycle_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_per = tat_days_per.merge(po_cycle, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_per[\"po_cycle_missing_per\"] = round(\n", "    ((po_cycle_per.po_cycle_missing / po_cycle_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly = (\n", "    assortment_full[\n", "        (assortment_full.po_days.isna())\n", "        & (assortment_full.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "        | ((assortment_full.po_cycle >= 5) & (assortment_full.po_days_length < 1))\n", "        | ((assortment_full.po_cycle == 4) & (assortment_full.po_days_length < 2))\n", "        | ((assortment_full.po_cycle == 3) & (assortment_full.po_days_length < 2))\n", "        | ((assortment_full.po_cycle == 2) & (assortment_full.po_days_length < 3))\n", "        | ((assortment_full.po_cycle == 1) & (assortment_full.po_days_length < 5))\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly = po_days_missing_anomaly.rename(columns={\"item_id\": \"po_day_anomaly\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly_per = po_cycle_per.merge(\n", "    po_days_missing_anomaly, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly_per[\"po_days_missing_per_anomaly\"] = round(\n", "    ((po_days_missing_anomaly_per.po_day_anomaly / po_days_missing_anomaly_per.total_item) * 100),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing = (\n", "    assortment_full[\n", "        (assortment_full.po_days.isna())\n", "        & (assortment_full.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing = po_days_missing.rename(columns={\"item_id\": \"po_day_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_per = po_days_missing_anomaly_per.merge(\n", "    po_days_missing, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_per[\"po_days_missing_per\"] = round(\n", "    ((po_days_missing_per.po_day_missing / po_days_missing_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero = (\n", "    assortment_full[\n", "        (assortment_full.case_sensitivity_type == 0)\n", "        & (assortment_full.transfer_flag == \"no\")\n", "        & (assortment_value.facility_flag == \"frontend\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero = case_sensitivity_zero.rename(columns={\"item_id\": \"case_sensitivity_zero\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero_per = po_days_missing_per.merge(\n", "    case_sensitivity_zero, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero_per[\"case_sensitivity_zero_per\"] = round(\n", "    (\n", "        (case_sensitivity_zero_per.case_sensitivity_zero / case_sensitivity_zero_per.total_item)\n", "        * 100\n", "    ),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con1 = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_facility_names = \"\"\"\n", "select distinct facility_id ,wf.name as facility_name from lake_retail.console_outlet co\n", "left join lake_retail.warehouse_facility wf\n", "on co.facility_id=wf.id\n", "         \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_facility_mapping = pd.read_sql_query(sql=city_facility_names, con=con1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity = case_sensitivity_zero_per.merge(\n", "    city_facility_mapping, on=[\"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity.facility_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["internal_facility_identifier_query = \"\"\"\n", "select facility_id,internal_facility_identifier\n", "from po.physical_facility\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["internal_facility_identifier = pd.read_sql_query(sql=internal_facility_identifier_query, con=retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity = assortment_sanity.merge(\n", "    internal_facility_identifier, on=[\"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity[\"final_facility\"] = np.where(\n", "    assortment_sanity.internal_facility_identifier.isna(),\n", "    assortment_sanity.facility_name,\n", "    assortment_sanity.internal_facility_identifier,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity.facility_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option(\"max_columns\", 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_1 = assortment_sanity[\n", "    ~assortment_sanity.facility_id.isin([64, 125, 133, 135, 132])\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india = assortment_sanity_1[\n", "    [\n", "        \"total_item\",\n", "        \"cpd_zero\",\n", "        \"cpd_zero_per\",\n", "        \"cpd_zero_transfer\",\n", "        \"cpd_zero_transfer_per\",\n", "        \"cpd_missing\",\n", "        \"cpd_missing_per\",\n", "        \"cpd_missing_transfer\",\n", "        \"cpd_missing_per_transfer\",\n", "        \"aligned_vendor_missing\",\n", "        \"aligned_vendor_missing_per\",\n", "        \"load_size_missing\",\n", "        \"load_size_missing_per\",\n", "        \"case_sensitivity_type_missing\",\n", "        \"case_sensitivity_missing_per\",\n", "        \"tat_days_missing\",\n", "        \"tat_days_missing_per\",\n", "        \"po_cycle_missing\",\n", "        \"po_cycle_missing_per\",\n", "        \"po_day_anomaly\",\n", "        \"po_days_missing_per_anomaly\",\n", "        \"po_day_missing\",\n", "        \"po_days_missing_per\",\n", "        \"case_sensitivity_zero\",\n", "        \"case_sensitivity_zero_per\",\n", "        \"final_facility\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india[\"final_facility\"] = \"Pan India\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup = round(\n", "    pan_india.groupby([\"final_facility\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"cpd_zero\": \"sum\",\n", "            \"cpd_zero_per\": \"mean\",\n", "            \"cpd_zero_transfer\": \"sum\",\n", "            \"cpd_zero_transfer_per\": \"mean\",\n", "            \"cpd_missing\": \"sum\",\n", "            \"cpd_missing_per\": \"mean\",\n", "            \"cpd_missing_transfer\": \"sum\",\n", "            \"cpd_missing_per_transfer\": \"mean\",\n", "            \"aligned_vendor_missing\": \"sum\",\n", "            \"aligned_vendor_missing_per\": \"mean\",\n", "            \"load_size_missing\": \"sum\",\n", "            \"load_size_missing_per\": \"mean\",\n", "            \"case_sensitivity_type_missing\": \"sum\",\n", "            \"case_sensitivity_missing_per\": \"mean\",\n", "            \"tat_days_missing\": \"sum\",\n", "            \"tat_days_missing_per\": \"mean\",\n", "            \"po_cycle_missing\": \"sum\",\n", "            \"po_cycle_missing_per\": \"mean\",\n", "            \"po_day_anomaly\": \"sum\",\n", "            \"po_days_missing_per_anomaly\": \"mean\",\n", "            \"po_day_missing\": \"sum\",\n", "            \"po_days_missing_per\": \"mean\",\n", "            \"case_sensitivity_zero\": \"sum\",\n", "            \"case_sensitivity_zero_per\": \"mean\",\n", "        }\n", "    ),\n", "    2,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_3 = assortment_sanity_1.append(pan_india_rollup)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_3[\"aligned_vendor_missing\"] = assortment_sanity_3.aligned_vendor_missing.fillna(0)\n", "assortment_sanity_3.cpd_zero_per = assortment_sanity_3.cpd_zero_per.fillna(0)\n", "assortment_sanity_3.cpd_missing_per = assortment_sanity_3.cpd_missing_per.fillna(0)\n", "assortment_sanity_3.case_sensitivity_type_missing = (\n", "    assortment_sanity_3.case_sensitivity_type_missing.fillna(0)\n", ")\n", "assortment_sanity_3.cpd_missing = assortment_sanity_3.cpd_missing.fillna(0)\n", "assortment_sanity_3.cpd_zero = assortment_sanity_3.cpd_zero.fillna(0)\n", "assortment_sanity_3.load_size_missing = assortment_sanity_3.load_size_missing.fillna(0)\n", "assortment_sanity_3.load_size_missing_per = assortment_sanity_3.load_size_missing_per.fillna(0)\n", "assortment_sanity_3.case_sensitivity_missing_per = (\n", "    assortment_sanity_3.case_sensitivity_missing_per.fillna(0)\n", ")\n", "assortment_sanity_3.tat_days_missing_per = assortment_sanity_3.tat_days_missing_per.fillna(0)\n", "assortment_sanity_3.tat_days_missing = assortment_sanity_3.tat_days_missing.fillna(0)\n", "assortment_sanity_3.po_cycle_missing_per = assortment_sanity_3.po_cycle_missing_per.fillna(0)\n", "assortment_sanity_3.po_cycle_missing = assortment_sanity_3.po_cycle_missing.fillna(0)\n", "assortment_sanity_3.po_days_missing_per = assortment_sanity_3.po_days_missing_per.fillna(0)\n", "assortment_sanity_3.po_day_missing = assortment_sanity_3.po_day_missing.fillna(0)\n", "assortment_sanity_3.aligned_vendor_missing_per = (\n", "    assortment_sanity_3.aligned_vendor_missing_per.fillna(0)\n", ")\n", "assortment_sanity_3.cpd_missing_transfer = assortment_sanity_3.cpd_missing_transfer.fillna(0)\n", "assortment_sanity_3.cpd_zero_transfer = assortment_sanity_3.cpd_zero_transfer.fillna(0)\n", "assortment_sanity_3.cpd_zero_transfer_per = assortment_sanity_3.cpd_zero_transfer_per.fillna(0)\n", "assortment_sanity_3.cpd_missing_per_transfer = assortment_sanity_3.cpd_missing_per_transfer.fillna(\n", "    0\n", ")\n", "assortment_sanity_3.po_day_anomaly = assortment_sanity_3.po_day_anomaly.fillna(0)\n", "assortment_sanity_3.po_days_missing_per_anomaly = (\n", "    assortment_sanity_3.po_days_missing_per_anomaly.fillna(0)\n", ")\n", "assortment_sanity_3.case_sensitivity_zero = assortment_sanity_3.case_sensitivity_zero.fillna(0)\n", "assortment_sanity_3.case_sensitivity_zero_per = (\n", "    assortment_sanity_3.case_sensitivity_zero_per.fillna(0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_3.cpd_zero_per = assortment_sanity_3.cpd_zero_per.astype(str) + \"%\"\n", "assortment_sanity_3.cpd_missing_per = assortment_sanity_3.cpd_missing_per.astype(str) + \"%\"\n", "assortment_sanity_3.load_size_missing_per = (\n", "    assortment_sanity_3.load_size_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.case_sensitivity_missing_per = (\n", "    assortment_sanity_3.case_sensitivity_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.tat_days_missing_per = (\n", "    assortment_sanity_3.tat_days_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.po_cycle_missing_per = (\n", "    assortment_sanity_3.po_cycle_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.po_days_missing_per = assortment_sanity_3.po_days_missing_per.astype(str) + \"%\"\n", "assortment_sanity_3.aligned_vendor_missing_per = (\n", "    assortment_sanity_3.aligned_vendor_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.cpd_zero_transfer_per = (\n", "    assortment_sanity_3.cpd_zero_transfer_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.cpd_missing_per_transfer = (\n", "    assortment_sanity_3.cpd_missing_per_transfer.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.po_days_missing_per_anomaly = (\n", "    assortment_sanity_3.po_days_missing_per_anomaly.astype(str) + \"%\"\n", ")\n", "assortment_sanity_3.case_sensitivity_zero_per = (\n", "    assortment_sanity_3.case_sensitivity_zero_per.astype(str) + \"%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row_1 = assortment_value.merge(city_facility_mapping, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = assortment_row_1[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"transfer_flag\",\n", "        \"cpd\",\n", "        \"vendor_id\",\n", "        \"load_size\",\n", "        \"case_sensitivity_type\",\n", "        \"po_cycle\",\n", "        \"po_days\",\n", "        \"tat_days\",\n", "        \"manufacturer_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row_1 = d[\n", "    (d.cpd.isna())\n", "    | (d.cpd == 0)\n", "    | (d.vendor_id.isna())\n", "    | (d.load_size.isna())\n", "    | (d.case_sensitivity_type.isna())\n", "    | (d.case_sensitivity_type == 0)\n", "    | (d.po_cycle.isna())\n", "    | (d.po_days.isna())\n", "    | (d.tat_days.isna())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_4 = assortment_sanity[\n", "    assortment_sanity.facility_id.isin([64, 125, 132, 133, 135])\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_business = assortment_sanity_4[\n", "    [\n", "        \"total_item\",\n", "        \"cpd_zero\",\n", "        \"cpd_zero_per\",\n", "        \"cpd_zero_transfer\",\n", "        \"cpd_zero_transfer_per\",\n", "        \"cpd_missing\",\n", "        \"cpd_missing_per\",\n", "        \"cpd_missing_transfer\",\n", "        \"cpd_missing_per_transfer\",\n", "        \"aligned_vendor_missing\",\n", "        \"aligned_vendor_missing_per\",\n", "        \"load_size_missing\",\n", "        \"load_size_missing_per\",\n", "        \"case_sensitivity_type_missing\",\n", "        \"case_sensitivity_missing_per\",\n", "        \"tat_days_missing\",\n", "        \"tat_days_missing_per\",\n", "        \"po_cycle_missing\",\n", "        \"po_cycle_missing_per\",\n", "        \"po_day_anomaly\",\n", "        \"po_days_missing_per_anomaly\",\n", "        \"po_day_missing\",\n", "        \"po_days_missing_per\",\n", "        \"case_sensitivity_zero\",\n", "        \"case_sensitivity_zero_per\",\n", "        \"final_facility\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_business[\"final_facility\"] = \"Pan India\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup_business = round(\n", "    pan_india_business.groupby([\"final_facility\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"cpd_zero\": \"sum\",\n", "            \"cpd_zero_per\": \"mean\",\n", "            \"cpd_zero_transfer\": \"sum\",\n", "            \"cpd_zero_transfer_per\": \"mean\",\n", "            \"cpd_missing\": \"sum\",\n", "            \"cpd_missing_per\": \"mean\",\n", "            \"cpd_missing_transfer\": \"sum\",\n", "            \"cpd_missing_per_transfer\": \"mean\",\n", "            \"aligned_vendor_missing\": \"sum\",\n", "            \"aligned_vendor_missing_per\": \"mean\",\n", "            \"load_size_missing\": \"sum\",\n", "            \"load_size_missing_per\": \"mean\",\n", "            \"case_sensitivity_type_missing\": \"sum\",\n", "            \"case_sensitivity_missing_per\": \"mean\",\n", "            \"tat_days_missing\": \"sum\",\n", "            \"tat_days_missing_per\": \"mean\",\n", "            \"po_cycle_missing\": \"sum\",\n", "            \"po_cycle_missing_per\": \"mean\",\n", "            \"po_day_anomaly\": \"sum\",\n", "            \"po_days_missing_per_anomaly\": \"mean\",\n", "            \"po_day_missing\": \"sum\",\n", "            \"po_days_missing_per\": \"mean\",\n", "            \"case_sensitivity_zero\": \"sum\",\n", "            \"case_sensitivity_zero_per\": \"mean\",\n", "        }\n", "    ),\n", "    2,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_5 = assortment_sanity_4.append(pan_india_rollup_business)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_5[\"aligned_vendor_missing\"] = assortment_sanity_5.aligned_vendor_missing.fillna(0)\n", "assortment_sanity_5.cpd_zero_per = assortment_sanity_5.cpd_zero_per.fillna(0)\n", "assortment_sanity_5.cpd_missing_per = assortment_sanity_5.cpd_missing_per.fillna(0)\n", "assortment_sanity_5.case_sensitivity_type_missing = (\n", "    assortment_sanity_5.case_sensitivity_type_missing.fillna(0)\n", ")\n", "assortment_sanity_5.cpd_missing = assortment_sanity_5.cpd_missing.fillna(0)\n", "assortment_sanity_5.cpd_zero = assortment_sanity_5.cpd_zero.fillna(0)\n", "assortment_sanity_5.load_size_missing = assortment_sanity_5.load_size_missing.fillna(0)\n", "assortment_sanity_5.load_size_missing_per = assortment_sanity_5.load_size_missing_per.fillna(0)\n", "assortment_sanity_5.case_sensitivity_missing_per = (\n", "    assortment_sanity_5.case_sensitivity_missing_per.fillna(0)\n", ")\n", "assortment_sanity_5.tat_days_missing_per = assortment_sanity_5.tat_days_missing_per.fillna(0)\n", "assortment_sanity_5.tat_days_missing = assortment_sanity_5.tat_days_missing.fillna(0)\n", "assortment_sanity_5.po_cycle_missing_per = assortment_sanity_5.po_cycle_missing_per.fillna(0)\n", "assortment_sanity_5.po_cycle_missing = assortment_sanity_5.po_cycle_missing.fillna(0)\n", "assortment_sanity_5.po_days_missing_per = assortment_sanity_5.po_days_missing_per.fillna(0)\n", "assortment_sanity_5.po_day_missing = assortment_sanity_5.po_day_missing.fillna(0)\n", "assortment_sanity_5.aligned_vendor_missing_per = (\n", "    assortment_sanity_5.aligned_vendor_missing_per.fillna(0)\n", ")\n", "assortment_sanity_5.cpd_missing_transfer = assortment_sanity_5.cpd_missing_transfer.fillna(0)\n", "assortment_sanity_5.cpd_zero_transfer = assortment_sanity_5.cpd_zero_transfer.fillna(0)\n", "assortment_sanity_5.cpd_zero_transfer_per = assortment_sanity_5.cpd_zero_transfer_per.fillna(0)\n", "assortment_sanity_5.cpd_missing_per_transfer = assortment_sanity_5.cpd_missing_per_transfer.fillna(\n", "    0\n", ")\n", "assortment_sanity_5.po_day_anomaly = assortment_sanity_5.po_day_anomaly.fillna(0)\n", "assortment_sanity_5.po_days_missing_per_anomaly = (\n", "    assortment_sanity_5.po_days_missing_per_anomaly.fillna(0)\n", ")\n", "assortment_sanity_5.case_sensitivity_zero = assortment_sanity_5.case_sensitivity_zero.fillna(0)\n", "assortment_sanity_5.case_sensitivity_zero_per = (\n", "    assortment_sanity_5.case_sensitivity_zero_per.fillna(0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_5.cpd_zero_per = assortment_sanity_5.cpd_zero_per.astype(str) + \"%\"\n", "assortment_sanity_5.cpd_missing_per = assortment_sanity_5.cpd_missing_per.astype(str) + \"%\"\n", "assortment_sanity_5.load_size_missing_per = (\n", "    assortment_sanity_5.load_size_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.case_sensitivity_missing_per = (\n", "    assortment_sanity_5.case_sensitivity_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.tat_days_missing_per = (\n", "    assortment_sanity_5.tat_days_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.po_cycle_missing_per = (\n", "    assortment_sanity_5.po_cycle_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.po_days_missing_per = assortment_sanity_5.po_days_missing_per.astype(str) + \"%\"\n", "assortment_sanity_5.aligned_vendor_missing_per = (\n", "    assortment_sanity_5.aligned_vendor_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.cpd_zero_transfer_per = (\n", "    assortment_sanity_5.cpd_zero_transfer_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.cpd_missing_per_transfer = (\n", "    assortment_sanity_5.cpd_missing_per_transfer.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.po_days_missing_per_anomaly = (\n", "    assortment_sanity_5.po_days_missing_per_anomaly.astype(str) + \"%\"\n", ")\n", "assortment_sanity_5.case_sensitivity_zero_per = (\n", "    assortment_sanity_5.case_sensitivity_zero_per.astype(str) + \"%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final1_query = \"\"\"\n", "select distinct final.*\n", "from \n", "(\n", "select distinct imf.*,\n", "load_size,\n", "case_sensitivity_type\n", "from\n", "\n", "(\n", "select distinct item_maf.*,\n", "po_cycle,\n", "po_days\n", "from\n", "\n", "(\n", "select distinct\n", "ven_fi.*,\n", "manufacturer_id\n", "from\n", "\n", "(\n", "select distinct\n", "base.*,\n", "vendor_id\n", "from\n", "(\n", "select\n", "a.item_id,\n", "b.facility_id\n", "from\n", "(\n", "select \n", "item_id,\n", "tag_value as outlet_id\n", "from rpc.item_outlet_tag_mapping as a\n", "where tag_type_id =8\n", "and active=1\n", "group by 1,2\n", ") as a\n", "left join retail.console_outlet as b\n", "on a.outlet_id=b.id\n", "left join retail.console_company_type as d on b.company_type_id=d.id\n", "left join (select  item_id, max(active) active, max(approved) approved \n", "                    from  rpc.product_product \n", "                    where active=1 group by 1) as c\n", "on a.item_id=c.item_id\n", "where b.active=1\n", "and c.active=1\n", "and c.approved=1\n", "and b.device_id<>47\n", "and b.company_type_id=4\n", "and facility_id in (41,116,56)\n", "group by 1,2\n", ") as base\n", "left join \n", "(\n", "  select distinct\n", "ven.item_id,\n", "ven.facility_id,\n", "ven.vendor_id\n", "from\n", "(\n", "select \n", "item_id,\n", "facility_id,\n", "vendor_id,\n", "a.updated_at\n", "from vms.vms_vendor_facility_alignment as a\n", "where a.active=1\n", ") as ven\n", "inner join \n", "(\n", "select \n", "item_id,\n", "facility_id,\n", "max(a.updated_at)as updated_at\n", "from vms.vms_vendor_facility_alignment as a\n", "where a.active=1\n", "group by 1,2) as inn\n", "on ven.item_id=inn.item_id\n", "and ven.facility_id=inn.facility_id\n", "and ven.updated_at=inn.updated_at\n", "  ) as vendor\n", "  on base.item_id=vendor.item_id\n", "  and base.facility_id=vendor.facility_id\n", "  )as ven_fi\n", "  left join \n", "(\n", "select \n", "      p.item_id,pm.id as manufacturer_id\n", "FROM\n", "    (select distinct item_id, active, brand_id from rpc.product_product where active=1) p\n", "INNER JOIN rpc.product_brand as pb on pb.id=p.brand_id\n", "INNER JOIN rpc.product_manufacturer as pm on pm.id=pb.manufacturer_id\n", "WHERE\n", "    p.active = 1 \n", "    group by 1,2\n", "    ) as b\n", "    on ven_fi.item_id=b.item_id\n", "    )item_maf\n", "    left join vms.vendor_manufacturer_physical_facility_attributes as po\n", "on item_maf.vendor_id=po.vendor_id\n", "and item_maf.manufacturer_id=po.manufacturer_id\n", "and item_maf.facility_id=po.facility_id\n", "and po.active=1  \n", ")as imf\n", "left join \n", "(\n", "select \n", "a.vendor_id,\n", "a.facility_id,\n", "a.load_size\n", "from vms.vendor_physical_facility_attributes as a\n", "inner join  (select vendor_id,facility_id,max(updated_at) as updated_at \n", "from vms.vendor_physical_facility_attributes\n", "group by 1,2)as b\n", "on a.vendor_id=b.vendor_id\n", "and a.facility_id=b.facility_id\n", "and a.updated_at=b.updated_at \n", ")as vpf\n", "on imf.vendor_id=vpf.vendor_id\n", "and imf.facility_id=vpf.facility_id\n", "left join vms.vendor_item_physical_facility_attributes as vip\n", "on imf.item_id=vip.item_id\n", "and imf.vendor_id=vip.vendor_id\n", "and imf.facility_id=vip.facility_id\n", "-- where vpf.active=1\n", "-- and vip.active=1\n", ")final\n", "where final.item_id not in (10000403,\n", "10002306,\n", "10003405,\n", "10004424,\n", "10004714,\n", "10005631,\n", "10006522,\n", "10007279,\n", "10007393,\n", "10007977,\n", "10008320,\n", "10009072,\n", "10012292,\n", "10016597,\n", "10016670,\n", "10016685,\n", "10016707,\n", "10016928,\n", "10017044,\n", "10017839,\n", "10018126,\n", "10019942,\n", "10021772,\n", "10022989,\n", "10022991,\n", "10022992,\n", "10022993,\n", "10022994,\n", "10023320,\n", "10024011,\n", "10024018,\n", "10024024,\n", "10024304,\n", "10026418,\n", "10026679,\n", "10029481,\n", "10030957,\n", "10032309,\n", "10033229,\n", "10033606,\n", "10034108,\n", "10034118,\n", "10034425,\n", "10034533,\n", "10034885,\n", "10034930,\n", "10034954,\n", "10034983,\n", "10036062,\n", "10036679,\n", "10036739,\n", "10036741,\n", "10036742,\n", "10037170,\n", "10037426,\n", "10037730,\n", "10037899,\n", "10038587,\n", "10039726,\n", "10039727,\n", "10039730,\n", "10039731,\n", "10039733,\n", "10039738,\n", "10039740,\n", "10040040,\n", "10040789,\n", "10041839,\n", "10041979,\n", "10041980,\n", "10041981,\n", "10043075,\n", "10043427,\n", "10043428,\n", "10043429,\n", "10043430,\n", "10043732,\n", "10044227,\n", "10044458,\n", "10045801,\n", "10046500)\n", "group by 1,2,3,4,5,6,7,8\n", "\"\"\"\n", "final1 = pd.read_sql_query(sql=final1_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transfer_full = pd.merge(final1, tat_data, on=[\"facility_id\", \"item_id\", \"vendor_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value = transfer_full"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_without_na_bulk = assortment_value[assortment_value.po_days.notna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_with_na_bulk = assortment_value[assortment_value.po_days.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_without_na_bulk[\"po_days_length\"] = assortment_value_po_without_na_bulk.apply(\n", "    lambda x: len(x[\"po_days\"].split(\",\")), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_full_bulk = pd.concat(\n", "    [assortment_value_po_without_na_bulk, assortment_value_po_with_na_bulk]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_bulk = assortment_value.groupby([\"facility_id\"])[\"item_id\"].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_bulk.facility_id.unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_bulk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_bulk = total_bulk.rename(columns={\"item_id\": \"total_item\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aligned_vendor_bulk = (\n", "    assortment_value[(assortment_value.vendor_id.isna())]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aligned_vendor_bulk = aligned_vendor_bulk.rename(columns={\"item_id\": \"aligned_vendor_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_missing_per_bulk = total_bulk.merge(aligned_vendor_bulk, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_missing_per_bulk[\"aligned_vendor_missing_per\"] = round(\n", "    ((vendor_missing_per_bulk.aligned_vendor_missing / vendor_missing_per_bulk.total_item) * 100),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_bulk = (\n", "    assortment_value[(assortment_value.load_size.isna())]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_bulk = load_size_bulk.rename(columns={\"item_id\": \"load_size_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_bulk_missing_per = vendor_missing_per_bulk.merge(\n", "    load_size_bulk, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_bulk_missing_per[\"load_size_missing_per\"] = round(\n", "    ((load_size_bulk_missing_per.load_size_missing / load_size_bulk_missing_per.total_item) * 100),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_bulk = (\n", "    assortment_value[(assortment_value.case_sensitivity_type.isna())]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_bulk = case_sensitivity_type_bulk.rename(\n", "    columns={\"item_id\": \"case_sensitivity_type_missing\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_per_bulk = load_size_bulk_missing_per.merge(\n", "    case_sensitivity_type_bulk, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_per_bulk[\"case_sensitivity_missing_per\"] = round(\n", "    (\n", "        (\n", "            case_sensitivity_type_per_bulk.case_sensitivity_type_missing\n", "            / case_sensitivity_type_per_bulk.total_item\n", "        )\n", "        * 100\n", "    ),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_bulk = (\n", "    assortment_value[(assortment_value.tat_days.isna())]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_bulk = tat_days_bulk.rename(columns={\"item_id\": \"tat_days_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_bulk_per = case_sensitivity_type_per_bulk.merge(\n", "    tat_days_bulk, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_bulk_per[\"tat_days_missing_per\"] = round(\n", "    ((tat_days_bulk_per.tat_days_missing / tat_days_bulk_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_bulk = (\n", "    assortment_value[(assortment_value.po_cycle.isna())]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_bulk = po_cycle_bulk.rename(columns={\"item_id\": \"po_cycle_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_bulk_per = tat_days_bulk_per.merge(po_cycle_bulk, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_bulk_per[\"po_cycle_missing_per\"] = round(\n", "    ((po_cycle_bulk_per.po_cycle_missing / po_cycle_bulk_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly_bulk = (\n", "    assortment_full_bulk[\n", "        (assortment_full_bulk.po_days.isna())\n", "        & ((assortment_full_bulk.po_cycle >= 5) & (assortment_full_bulk.po_days_length < 1))\n", "        | ((assortment_full_bulk.po_cycle == 4) & (assortment_full_bulk.po_days_length < 2))\n", "        | ((assortment_full_bulk.po_cycle == 3) & (assortment_full_bulk.po_days_length < 2))\n", "        | ((assortment_full_bulk.po_cycle == 2) & (assortment_full_bulk.po_days_length < 3))\n", "        | ((assortment_full_bulk.po_cycle == 1) & (assortment_full_bulk.po_days_length < 5))\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly_bulk = po_days_missing_anomaly_bulk.rename(\n", "    columns={\"item_id\": \"po_day_anomaly\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly_bulk_per = po_cycle_bulk_per.merge(\n", "    po_days_missing_anomaly_bulk, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_anomaly_bulk_per[\"po_days_missing_per_anomaly\"] = round(\n", "    (\n", "        (\n", "            po_days_missing_anomaly_bulk_per.po_day_anomaly\n", "            / po_days_missing_anomaly_bulk_per.total_item\n", "        )\n", "        * 100\n", "    ),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_bulk = (\n", "    assortment_full[(assortment_full.po_days.isna())]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_bulk = po_days_missing_bulk.rename(columns={\"item_id\": \"po_day_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_bulk_per = po_days_missing_anomaly_bulk_per.merge(\n", "    po_days_missing_bulk, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_bulk_per[\"po_days_missing_per\"] = round(\n", "    ((po_days_missing_bulk_per.po_day_missing / po_days_missing_bulk_per.total_item) * 100),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero_bulk = (\n", "    assortment_full[(assortment_full.case_sensitivity_type == 0)]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero_bulk = case_sensitivity_zero_bulk.rename(\n", "    columns={\"item_id\": \"case_sensitivity_zero\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero_per_bulk = po_days_missing_bulk_per.merge(\n", "    case_sensitivity_zero_bulk, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_zero_per_bulk[\"case_sensitivity_zero_per\"] = round(\n", "    (\n", "        (\n", "            case_sensitivity_zero_per_bulk.case_sensitivity_zero\n", "            / case_sensitivity_zero_per_bulk.total_item\n", "        )\n", "        * 100\n", "    ),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con1 = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_bulk = case_sensitivity_zero_per_bulk.merge(\n", "    city_facility_mapping, on=[\"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_bulk = assortment_sanity_bulk.merge(\n", "    internal_facility_identifier, on=[\"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_bulk[\"final_facility\"] = np.where(\n", "    assortment_sanity_bulk.internal_facility_identifier.isna(),\n", "    assortment_sanity_bulk.facility_name,\n", "    assortment_sanity_bulk.internal_facility_identifier,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_bulk = assortment_sanity_bulk[\n", "    assortment_sanity_bulk.facility_id.isin([41, 116, 56])\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_bulk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_bulk = assortment_sanity_bulk[\n", "    [\n", "        \"total_item\",\n", "        \"aligned_vendor_missing\",\n", "        \"aligned_vendor_missing_per\",\n", "        \"load_size_missing\",\n", "        \"load_size_missing_per\",\n", "        \"case_sensitivity_type_missing\",\n", "        \"case_sensitivity_missing_per\",\n", "        \"tat_days_missing\",\n", "        \"tat_days_missing_per\",\n", "        \"po_cycle_missing\",\n", "        \"po_cycle_missing_per\",\n", "        \"po_day_anomaly\",\n", "        \"po_days_missing_per_anomaly\",\n", "        \"po_day_missing\",\n", "        \"po_days_missing_per\",\n", "        \"case_sensitivity_zero\",\n", "        \"case_sensitivity_zero_per\",\n", "        \"final_facility\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_bulk[\"final_facility\"] = \"Pan India\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup_bulk = round(\n", "    pan_india_bulk.groupby([\"final_facility\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"aligned_vendor_missing\": \"sum\",\n", "            \"aligned_vendor_missing_per\": \"mean\",\n", "            \"load_size_missing\": \"sum\",\n", "            \"load_size_missing_per\": \"mean\",\n", "            \"case_sensitivity_type_missing\": \"sum\",\n", "            \"case_sensitivity_missing_per\": \"mean\",\n", "            \"tat_days_missing\": \"sum\",\n", "            \"tat_days_missing_per\": \"mean\",\n", "            \"po_cycle_missing\": \"sum\",\n", "            \"po_cycle_missing_per\": \"mean\",\n", "            \"po_day_anomaly\": \"sum\",\n", "            \"po_days_missing_per_anomaly\": \"mean\",\n", "            \"po_day_missing\": \"sum\",\n", "            \"po_days_missing_per\": \"mean\",\n", "            \"case_sensitivity_zero\": \"sum\",\n", "            \"case_sensitivity_zero_per\": \"mean\",\n", "        }\n", "    ),\n", "    2,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_2 = assortment_sanity_bulk.append(pan_india_rollup_bulk)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_2.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_2[\"aligned_vendor_missing\"] = assortment_sanity_2.aligned_vendor_missing.fillna(0)\n", "assortment_sanity_2.case_sensitivity_type_missing = (\n", "    assortment_sanity_2.case_sensitivity_type_missing.fillna(0)\n", ")\n", "assortment_sanity_2.load_size_missing = assortment_sanity_2.load_size_missing.fillna(0)\n", "assortment_sanity_2.load_size_missing_per = assortment_sanity_2.load_size_missing_per.fillna(0)\n", "assortment_sanity_2.case_sensitivity_missing_per = (\n", "    assortment_sanity_2.case_sensitivity_missing_per.fillna(0)\n", ")\n", "assortment_sanity_2.tat_days_missing_per = assortment_sanity_2.tat_days_missing_per.fillna(0)\n", "assortment_sanity_2.tat_days_missing = assortment_sanity_2.tat_days_missing.fillna(0)\n", "assortment_sanity_2.po_cycle_missing_per = assortment_sanity_2.po_cycle_missing_per.fillna(0)\n", "assortment_sanity_2.po_cycle_missing = assortment_sanity_2.po_cycle_missing.fillna(0)\n", "assortment_sanity_2.po_days_missing_per = assortment_sanity_2.po_days_missing_per.fillna(0)\n", "assortment_sanity_2.po_day_missing = assortment_sanity_2.po_day_missing.fillna(0)\n", "assortment_sanity_2.aligned_vendor_missing_per = (\n", "    assortment_sanity_2.aligned_vendor_missing_per.fillna(0)\n", ")\n", "assortment_sanity_2.po_day_anomaly = assortment_sanity_2.po_day_anomaly.fillna(0)\n", "assortment_sanity_2.po_days_missing_per_anomaly = (\n", "    assortment_sanity_2.po_days_missing_per_anomaly.fillna(0)\n", ")\n", "assortment_sanity_2.case_sensitivity_zero = assortment_sanity_2.case_sensitivity_zero.fillna(0)\n", "assortment_sanity_2.case_sensitivity_zero_per = (\n", "    assortment_sanity_2.case_sensitivity_zero_per.fillna(0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_2.load_size_missing_per = (\n", "    assortment_sanity_2.load_size_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_2.case_sensitivity_missing_per = (\n", "    assortment_sanity_2.case_sensitivity_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_2.tat_days_missing_per = (\n", "    assortment_sanity_2.tat_days_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_2.po_cycle_missing_per = (\n", "    assortment_sanity_2.po_cycle_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_2.po_days_missing_per = assortment_sanity_2.po_days_missing_per.astype(str) + \"%\"\n", "assortment_sanity_2.aligned_vendor_missing_per = (\n", "    assortment_sanity_2.aligned_vendor_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_2.po_days_missing_per_anomaly = (\n", "    assortment_sanity_2.po_days_missing_per_anomaly.astype(str) + \"%\"\n", ")\n", "assortment_sanity_2.case_sensitivity_zero_per = (\n", "    assortment_sanity_2.case_sensitivity_zero_per.astype(str) + \"%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row_2 = assortment_value.merge(city_facility_mapping, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row_2[\"cpd\"] = \"null\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row_2[\"transfer_flag\"] = \"null\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = assortment_row_2[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"transfer_flag\",\n", "        \"cpd\",\n", "        \"vendor_id\",\n", "        \"load_size\",\n", "        \"case_sensitivity_type\",\n", "        \"po_cycle\",\n", "        \"po_days\",\n", "        \"tat_days\",\n", "        \"manufacturer_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row_2 = d[\n", "    (d.vendor_id.isna())\n", "    | (d.load_size.isna())\n", "    | (d.case_sensitivity_type.isna())\n", "    | (d.case_sensitivity_type == 0)\n", "    | (d.po_cycle.isna())\n", "    | (d.po_days.isna())\n", "    | (d.tat_days.isna())\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["todays_date = datetime.now().strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row_2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row = pd.concat([assortment_row_1, assortment_row_2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row.to_csv(\n", "    \"assortment_sanity_report-{}.csv\".format(todays_date), encoding=\"utf8\", index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = assortment_sanity_3.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items1 = assortment_sanity_2.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items2 = assortment_sanity_5.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pwd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "import os\n", "import jinja2\n", "import json\n", "\n", "df_mail = pb.from_sheets(\n", "    sheetid=\"1jqAHMcON3SoTBj2HtBYwQqUhc-U6KejAem2mthPfv_c\",\n", "    sheetname=\"assortment_sanity_mail_list\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mail_list = list(df_mail[\"email\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import pencilbox as pb\n", "\n", "# # # from gr_mailer import send_email\n", "# import os\n", "# import jinja2\n", "\n", "# from_email = \"<EMAIL>\"\n", "# # to_email=json.loads(secrets.get(\"doi_report_emails\"))\n", "# # # ,'<EMAIL>','<EMAIL>',\n", "# # # '<EMAIL>',\n", "# # # '<EMAIL>',\n", "# # # '<EMAIL>',\n", "# # # '<EMAIL>',\n", "# # # '<EMAIL>',\n", "# # # '<EMAIL>']\n", "# # to_email =json.loads(secrets.get(\"facility_wise_assortment_sanity_report_emails\"))\n", "\n", "# to_email = [\"<EMAIL>\"]\n", "# subject = \"Facilitywise Assortment Sanity\" + \" - \" + todays_date\n", "\n", "# email_template = \"assortment_sanity_email.html\"\n", "# # loader = jinja2.FileSystemLoader(searchpath=\"/opt/grofers/airflow/dags/metrics/notebooks\")\n", "# # loader = jinja2.FileSystemLoader(searchpath=\"/home/<USER>/Hinal/DOI\")\n", "# loader = jinja2.FileSystemLoader(\n", "#     searchpath=\"/home/<USER>/Hinal/PO_VMS/Assortment_Sanity/final_notebook\"\n", "# )\n", "\n", "\n", "# tmpl_environ = jinja2.Environment(loader=loader)\n", "# template = tmpl_environ.get_template(email_template)\n", "\n", "# rendered = template.render(\n", "#     products=items,\n", "#     products1=items1,\n", "#     products2=items2,\n", "#     time_period_considered=todays_date,\n", "# )\n", "\n", "# # send_email(sender, to, subject, html_content = rendered,\n", "# #                     dryrun=False, cc=cc, bcc=None,\n", "# #                     mime_subtype='mixed', mime_charset='utf-8')\n", "\n", "\n", "# # pb.send_email(from_email, to_email, subject, html_content=rendered\n", "# pb.send_email(\n", "#     from_email,\n", "#     to_email,\n", "#     subject,\n", "#     html_content=rendered,\n", "#     files=[\"assortment_sanity_report-{}.csv\".format(todays_date)],\n", "# )\n", "\n", "# print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "# from gr_mailer import send_email\n", "import os\n", "from jinja2 import Template\n", "\n", "\n", "from_email = \"<EMAIL>\"\n", "to_email = mail_list\n", "\n", "# to_email=\"<EMAIL>\"\n", "\n", "subject = \"Facilitywise Assortment Sanity\" + \" - \" + todays_date\n", "\n", "\n", "# loader = jinja2.FileSystemLoader(searchpath=\"/opt/grofers/airflow/dags/metrics/notebooks\")\n", "with open(os.path.join(cwd, \"assortment_sanity_email.html\"), \"r\") as f:\n", "    t = Template(f.read())\n", "\n", "\n", "rendered = t.render(\n", "    products=items,\n", "    products1=items1,\n", "    products2=items2,\n", "    time_period_considered=todays_date,\n", ")\n", "\n", "# send_email(sender, to, subject, html_content = rendered,\n", "#                     dryrun=False, cc=cc, bcc=None,\n", "#                     mime_subtype='mixed', mime_charset='utf-8')\n", "\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content=rendered,\n", "    files=[\"assortment_sanity_report-{}.csv\".format(todays_date)],\n", ")\n", "\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}