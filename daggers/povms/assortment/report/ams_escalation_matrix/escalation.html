<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: center;
    vertical-align: center
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: center;
    vertical-align: center
}

</style>
</head>
<p>Hi All,<br/>
    <br/>
Please find the below the items in pending status across different checks for a timeframe of {{slab}} from {{time_period_considered}}. <br/>
<p>  
Additionally, for tracking the items across different L0 categories, please refer to the tracker :<br/>
Link:
<a href={{link}}>Escalation Matrix Tracker</a><br/>
<p>     
</p>

<p><b> Summary </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">L0 Category</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Slab</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Vendor Check</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Tax Check</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Margin Check</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">CPD Check</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Approval Check</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Pending Items</span></th>
        </tr>
        {% for product in summary %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.l0}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.slab}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.vendor}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.tax}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.margin}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.cpd}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.approval}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
             <b>{{ product.total_pending}}</b>
           </td>

           
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>

<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>