dag_name: ams_escalation_matrix
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURE15HB
path: povms/assortment/report/ams_escalation_matrix
paused: true
pool: povms_pool
project_name: assortment
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 4 * * *
  start_date: '2021-05-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files:
- escalation.html
tags: []
template_name: notebook
version: 7
