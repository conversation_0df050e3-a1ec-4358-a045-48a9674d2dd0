{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AMS Escalation Matrix "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "from datetime import date, datetime, timedelta\n", "import numpy as np\n", "import requests\n", "import json\n", "import uuid\n", "import os\n", "\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 100)\n", "import math\n", "import logging\n", "from IPython.display import clear_output\n", "from pytz import timezone\n", "from time import sleep\n", "import os\n", "import time\n", "\n", "\n", "# Connection\n", "CON_REDSHIFT = pb.get_connection(\"[Warehouse] Redshift\")\n", "CON_RETAIL = pb.get_connection(\"retail\")\n", "\n", "# Dates\n", "today = date.today() - <PERSON><PERSON><PERSON>(days=1)\n", "difference = 12\n", "today_trick = today - <PERSON><PERSON><PERSON>(days=difference)\n", "current = today  # - <PERSON><PERSON><PERSON>(days=2)\n", "current_half = current - <PERSON><PERSON><PERSON>(days=16)\n", "print(\n", "    \"dates- today:\",\n", "    today,\n", "    \" | today_trick:\",\n", "    today_trick,\n", "    \" | current:\",\n", "    current,\n", "    \" | current_half:\",\n", "    current_half,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Functions\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tabulate\n", "from tabulate import tabulate\n", "\n", "\n", "def check_level(df, cols, identifier):\n", "    if df.shape[0] != df[cols].drop_duplicates().shape[0]:\n", "        query = \" and \".join([x + \" > 1\" for x in list(df.columns) if x not in cols])\n", "        temp_df = df.groupby(cols).count().reset_index()\n", "        sample = tabulate(temp_df.query(query), tablefmt=\"pipe\", headers=\"keys\")\n", "        error = f\"\"\":red_circle: *Alert*: Duplication found while extracting `{identifier}` for Run ID: \\n\n", "        ```Count on grouped by {\", \".join(cols)}:\\n\\n{sample}```\"\"\"\n", "        print(error)\n", "        # raise Exception(\"Sorry, level does not match\", identifier)\n", "    else:\n", "        print(\"No errors\")\n", "        log_file_name = f\"{identifier}_extracted_data.csv\"\n", "        df.to_csv(log_file_name, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def slab_priority(x):\n", "    slab = x[\"slab\"]\n", "    if slab == \"Greater than 11 Days\":\n", "        return \"High Priority\"\n", "    elif slab == \"7 - 11 Days\":\n", "        return \"Medium Priority\"\n", "    elif slab == \"4 - 6 Days\":\n", "        return \"Low Priority\"\n", "    elif slab == \"Less than 4 Days\":\n", "        return \"Very Low Priority\"\n", "    else:\n", "        return \"other\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    start = time.time()\n", "    df = pd.read_sql_query(sql, con)\n", "    end = time.time()\n", "    print(\"Time: \", end - start, \"s\")\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Input - Open Request"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state_request_query = f\"\"\"select id,item_id,facility_id,\n", "case when vendor_alignment_check = 1 then 'Approved'\n", "when vendor_alignment_check = 0 then 'Pending'\n", "when vendor_alignment_check is null then 'Not Required' else 'other' end AS vendor_alignment_check,\n", "\n", "case when tax_check = 1 then 'Approved'\n", "when tax_check = 0 then 'Pending'\n", "when tax_check is null then 'Not Required' else 'other' end AS tax_check,\n", "\n", "case when margin_check = 1 then 'Approved'\n", "when margin_check = 0 then 'Pending'\n", "when margin_check is null then 'Not Required' else 'other' end AS margin_check,\n", "\n", "case when cpd_check = 1 then 'Approved'\n", "when cpd_check = 0 then 'Pending'\n", "when cpd_check is null then 'Not Required' else 'other' end AS cpd_check,\n", "\n", "case when approval_check = 1 then 'Approved'\n", "when approval_check = 0 then 'Pending'\n", "when approval_check is null then 'Not Required' else 'other' end AS approval_check,\n", "\n", "active, date(created_at) as created_at, date_add(date(current_date), INTERVAL -1 Day) as today, datediff(date_add(date(current_date), INTERVAL -1 Day), date(created_at)) as open_since\n", "from rpc.item_facility_state_request \n", "where status = 'OPEN' and active = 1\"\"\"\n", "state_request = read_sql_query(sql=state_request_query, con=CON_RETAIL)\n", "check_level(state_request, [\"item_id\", \"facility_id\"], \"state_request\")\n", "state_request = state_request[state_request.open_since >= 0]\n", "state_request.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Input - <PERSON><PERSON> - Name & L0\n", " \n", "  "]}, {"cell_type": "raw", "metadata": {}, "source": ["item_mapping_query = f\"\"\"with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "  SELECT item_id,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.product_type,\n", "          brand,\n", "          manf\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL\"\"\"\n", "item_mapping = read_sql_query(sql=item_mapping_query,con=CON_REDSHIFT)\n", "check_level(item_mapping,['item_id'],\"item_mapping\")\n", "item_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item_mapping_query = f\"\"\"select distinct item_id, l0 from rpc.item_category_details\"\"\"\n", "item_mapping = read_sql_query(sql=item_mapping_query, con=CON_RETAIL)\n", "check_level(item_mapping, [\"item_id\"], \"item_mapping\")\n", "item_mapping.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Getting Item_Name\n", "item_name_query = f\"\"\"SELECT DISTINCT A.item_id,\n", "                max(name) as item_name\n", "FROM lake_rpc.product_product A\n", "INNER JOIN\n", "  (SELECT max(updated_at) AS updated_at,\n", "          item_id\n", "   FROM lake_rpc.product_product\n", "   WHERE item_id IN {tuple(state_request.item_id.unique())}\n", "   GROUP BY 2) B ON A.updated_at = B.updated_at\n", "AND A.item_id = B.item_id\n", "GROUP BY 1\"\"\"\n", "\n", "item_name = read_sql_query(sql=item_name_query, con=CON_REDSHIFT)\n", "check_level(item_name, [\"item_id\"], \"item_name\")\n", "item_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Getting Item_Name\n", "facilitymapping_query = f\"\"\"select distinct facility_id, name as facility_name from\n", "po.physical_facility\n", "where active = 1\"\"\"\n", "facilitymapping = read_sql_query(sql=facilitymapping_query, con=CON_RETAIL)\n", "check_level(facilitymapping, [\"facility_id\"], \"facilitymapping\")\n", "facilitymapping.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["franchise_facility_query = (\n", "    f\"\"\"select distinct facility_id from retail.console_outlet where business_type_id=8\"\"\"\n", ")\n", "franchise_facility = read_sql_query(sql=franchise_facility_query, con=CON_RETAIL)\n", "check_level(franchise_facility, [\"facility_id\"], \"franchise_facility\")\n", "franchise_facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["manufac_name_query = f\"\"\"SELECT\n", "     distinct p.item_id, pm.name as manufacturer_name, pm.id as manufacturer_id\n", "FROM\n", "    rpc.product_product p\n", "INNER JOIN rpc.product_brand as pb on pb.id=p.brand_id\n", "INNER JOIN rpc.product_manufacturer as pm on pm.id=pb.manufacturer_id\n", "WHERE\n", "    p.active = 1 AND\n", "    p.item_id IN {tuple(state_request.item_id.unique())}\"\"\"\n", "manufac_name = read_sql_query(sql=manufac_name_query, con=CON_RETAIL)\n", "check_level(manufac_name, [\"item_id\", \"manufacturer_name\", \"manufacturer_id\"], \"manufac_name\")\n", "manufac_name.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_mapping_query = f\"\"\"select distinct facility_id, city_id from po.physical_facility_outlet_mapping where active = 1 and concat(facility_id,city_id) != 29201\"\"\"\n", "city_mapping = read_sql_query(sql=city_mapping_query, con=CON_RETAIL)\n", "check_level(city_mapping, [\"facility_id\"], \"city_mapping\")\n", "city_mapping.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add 'slab' - Open Request Flag"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_request = state_request[\n", "    [\n", "        \"id\",\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"vendor_alignment_check\",\n", "        \"tax_check\",\n", "        \"margin_check\",\n", "        \"cpd_check\",\n", "        \"approval_check\",\n", "        \"created_at\",\n", "        \"today\",\n", "        \"open_since\",\n", "    ]\n", "]\n", "open_request.loc[open_request[\"open_since\"] < 4, \"slab\"] = \"Less than 4 Days\"\n", "open_request.loc[\n", "    (4 <= open_request[\"open_since\"]) & (open_request[\"open_since\"] <= 6), \"slab\"\n", "] = \"4 - 6 Days\"\n", "open_request.loc[\n", "    (7 <= open_request[\"open_since\"]) & (open_request[\"open_since\"] <= 11), \"slab\"\n", "] = \"7 - 11 Days\"\n", "open_request.loc[(11 < open_request[\"open_since\"]), \"slab\"] = \"Greater than 11 Days\"\n", "open_request.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remove Inactive Stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["active_facilities = facilitymapping[[\"facility_id\"]].drop_duplicates()\n", "active_facilities[\"Active_Flag\"] = 1\n", "open_request = pd.merge(\n", "    open_request,\n", "    active_facilities,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "open_request = open_request[open_request.Active_Flag == 1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remove Franchise Stores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["franchise_facility = franchise_facility[[\"facility_id\"]].drop_duplicates()\n", "franchise_facility[\"Franchise_Flag\"] = 1\n", "open_request = pd.merge(\n", "    open_request,\n", "    franchise_facility,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "open_request = open_request[open_request.Franchise_Flag.isna()]\n", "open_request = open_request.drop([\"Franchise_Flag\", \"Active_Flag\"], axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Item Details - Name & L0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_request_0 = pd.merge(\n", "    open_request,\n", "    item_mapping[[\"item_id\", \"l0\"]],\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "open_request_1 = pd.merge(\n", "    open_request_0, item_name, left_on=[\"item_id\"], right_on=[\"item_id\"], how=\"left\"\n", ")\n", "open_request_2 = open_request_1[\n", "    [\n", "        \"id\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0\",\n", "        \"created_at\",\n", "        \"facility_id\",\n", "        \"vendor_alignment_check\",\n", "        \"margin_check\",\n", "        \"cpd_check\",\n", "        \"tax_check\",\n", "        \"approval_check\",\n", "        \"today\",\n", "        \"open_since\",\n", "        \"slab\",\n", "    ]\n", "]\n", "open_request_2.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Open request across L0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Total open, pending requests and their difference"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_request_2.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# total open requests\n", "total_requests_l0 = (\n", "    open_request_2.groupby([\"l0\", \"slab\"]).agg({\"id\": \"count\"}).reset_index().sort_values(by=[\"l0\"])\n", ")\n", "total_requests_l0 = total_requests_l0.rename(columns={\"id\": \"total_items\"})\n", "\n", "# total pending requests\n", "total_pending = open_request_2[open_request_2.id.isna() == False]\n", "total_pending.loc[\n", "    (total_pending[\"vendor_alignment_check\"] == \"Pending\")\n", "    | (total_pending[\"margin_check\"] == \"Pending\")\n", "    | (total_pending[\"cpd_check\"] == \"Pending\")\n", "    | (total_pending[\"tax_check\"] == \"Pending\")\n", "    | (total_pending[\"approval_check\"] == \"Pending\"),\n", "    \"flag_pending\",\n", "] = 1\n", "total_pending.flag_pending = total_pending.flag_pending.fillna(0)\n", "\n", "total_pending_agg = (\n", "    total_pending.groupby([\"l0\", \"slab\"])\n", "    .agg({\"flag_pending\": \"sum\"})\n", "    .reset_index()\n", "    .sort_values(by=[\"l0\"])\n", ")\n", "total_pending_agg = total_pending_agg.rename(columns={\"flag_pending\": \"total_pending\"})\n", "\n", "# total items\n", "total_items = pd.merge(\n", "    total_requests_l0,\n", "    total_pending_agg,\n", "    left_on=[\"l0\", \"slab\"],\n", "    right_on=[\"l0\", \"slab\"],\n", "    how=\"left\",\n", ")\n", "total_items[\"Difference\"] = total_items[\"total_items\"] - total_items[\"total_pending\"]\n", "total_items.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_pending[total_pending.slab == \"4 - 6 Days\"].head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_pending[total_pending.flag_pending == 0].tax_check.drop_duplicates()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Aggregate items across various check flags"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aggregate_status = (\n", "    open_request_2.groupby(\n", "        [\n", "            \"l0\",\n", "            \"slab\",\n", "            \"vendor_alignment_check\",\n", "            \"margin_check\",\n", "            \"cpd_check\",\n", "            \"tax_check\",\n", "            \"approval_check\",\n", "        ]\n", "    )\n", "    .agg({\"id\": \"count\"})\n", "    .reset_index()\n", ")\n", "aggregate_status.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Pending Requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pending_vendor = (\n", "    aggregate_status[aggregate_status.vendor_alignment_check == \"Pending\"]\n", "    .groupby([\"l0\", \"slab\"])\n", "    .agg({\"id\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"id\": \"vendor\"})\n", ")\n", "vendor_add = pd.merge(\n", "    total_items,\n", "    pending_vendor,\n", "    left_on=[\"l0\", \"slab\"],\n", "    right_on=[\"l0\", \"slab\"],\n", "    how=\"left\",\n", ")\n", "vendor_add.vendor = vendor_add.vendor.fillna(0)\n", "\n", "pending_margin = (\n", "    aggregate_status[aggregate_status.margin_check == \"Pending\"]\n", "    .groupby([\"l0\", \"slab\"])\n", "    .agg({\"id\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"id\": \"margin\"})\n", ")\n", "margin_add = pd.merge(\n", "    vendor_add,\n", "    pending_margin,\n", "    left_on=[\"l0\", \"slab\"],\n", "    right_on=[\"l0\", \"slab\"],\n", "    how=\"left\",\n", ")\n", "margin_add.margin = margin_add.margin.fillna(0)\n", "\n", "pending_cpd = (\n", "    aggregate_status[aggregate_status.cpd_check == \"Pending\"]\n", "    .groupby([\"l0\", \"slab\"])\n", "    .agg({\"id\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"id\": \"cpd\"})\n", ")\n", "cpd_add = pd.merge(\n", "    margin_add, pending_cpd, left_on=[\"l0\", \"slab\"], right_on=[\"l0\", \"slab\"], how=\"left\"\n", ")\n", "cpd_add.cpd = cpd_add.cpd.fillna(0)\n", "\n", "pending_tax = (\n", "    aggregate_status[aggregate_status.tax_check == \"Pending\"]\n", "    .groupby([\"l0\", \"slab\"])\n", "    .agg({\"id\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"id\": \"tax\"})\n", ")\n", "tax_add = pd.merge(\n", "    cpd_add, pending_tax, left_on=[\"l0\", \"slab\"], right_on=[\"l0\", \"slab\"], how=\"left\"\n", ")\n", "tax_add.tax = tax_add.tax.fillna(0)\n", "\n", "pending_approval = (\n", "    aggregate_status[aggregate_status.approval_check == \"Pending\"]\n", "    .groupby([\"l0\", \"slab\"])\n", "    .agg({\"id\": \"sum\"})\n", "    .reset_index()\n", "    .rename(columns={\"id\": \"approval\"})\n", ")\n", "approval_add = pd.merge(\n", "    tax_add,\n", "    pending_approval,\n", "    left_on=[\"l0\", \"slab\"],\n", "    right_on=[\"l0\", \"slab\"],\n", "    how=\"left\",\n", ")\n", "approval_add.approval = approval_add.approval.fillna(0)\n", "approval_add.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Split based on Slab priority"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Final Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_aggregate = approval_add[\n", "    [\"l0\", \"slab\", \"vendor\", \"tax\", \"margin\", \"cpd\", \"approval\", \"total_pending\"]\n", "]\n", "final_aggregate[[\"vendor\", \"tax\", \"margin\", \"cpd\", \"approval\", \"total_pending\"]] = final_aggregate[\n", "    [\"vendor\", \"tax\", \"margin\", \"cpd\", \"approval\", \"total_pending\"]\n", "].astype(int)\n", "very_low = final_aggregate[final_aggregate.slab == \"Less than 4 Days\"].sort_values(by=\"l0\")\n", "low = final_aggregate[final_aggregate.slab == \"4 - 6 Days\"].sort_values(by=\"l0\")\n", "medium = final_aggregate[final_aggregate.slab == \"7 - 11 Days\"].sort_values(by=\"l0\")\n", "high = final_aggregate[final_aggregate.slab == \"Greater than 11 Days\"].sort_values(by=\"l0\")\n", "high.head(2)"]}, {"cell_type": "raw", "metadata": {}, "source": ["total_pending.head(2) # Final Dataset -> remove id, today, open_since,,,,,rename flag_pending as Pending_Status"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_summary = open_request_2[\n", "    [\n", "        \"l0\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"facility_id\",\n", "        \"created_at\",\n", "        \"vendor_alignment_check\",\n", "        \"margin_check\",\n", "        \"cpd_check\",\n", "        \"tax_check\",\n", "        \"approval_check\",\n", "        \"slab\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perc_status = (\n", "    total_items.groupby(\"slab\")\n", "    .agg({\"total_items\": \"sum\", \"total_pending\": \"sum\", \"Difference\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "perc_status[\"Perc_pending_of_total_open_request\"] = (\n", "    round((1 - (perc_status[\"Difference\"] / perc_status[\"total_pending\"])) * 100, 2).astype(\"str\")\n", "    + \" %\"\n", ")\n", "perc_status[\"perc_pending_share\"] = (\n", "    round(100 * perc_status[\"total_pending\"] / perc_status[\"total_pending\"].sum(), 2).astype(\"str\")\n", "    + \" %\"\n", ")\n", "perc_status"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mails for slab priority - low, medium, high"]}, {"cell_type": "raw", "metadata": {}, "source": ["import os\n", "from jinja2 import Template\n", "\n", "cwd = os.getcwd()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Getting recepient emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1L1Bx-OmMbxTBt6ZCuy0O5zFE5W2e4T5WiuDrL1U5RRU\"\n", "all_emails = pb.from_sheets(sheet_id, \"Sheet1\")\n", "new_header = all_emails.iloc[0]  # grab the first row for the header\n", "all_emails = all_emails[1:]  # take the data less the header row\n", "all_emails.columns = new_header  # set the header row as the df header\n", "all_emails.columns = [\n", "    \"L0_Category\",\n", "    \"Category_manager_email_ID\",\n", "    \"Requestor_email_ID\",\n", "    \"Category_Lead_email_ID\",\n", "    \"Function_Lead_email_ID\",\n", "]\n", "all_emails.head(2)"]}, {"cell_type": "raw", "metadata": {}, "source": ["category_manager = \"','\".join(map(str,list(all_emails.Category_manager_email_ID.unique()))).replace(', nan','')\n", "requestor_email = \"','\".join(map(str,list(all_emails.Requestor_email_ID.unique()))).replace(', nan','')\n", "category_lead = \"','\".join(map(str,list(all_emails.Category_Lead_email_ID.unique()))).replace(', nan','')\n", "function_lead = \"','\".join(map(str,list(all_emails.Function_Lead_email_ID.unique()))).replace(', nan','')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_manager = list(all_emails.Category_manager_email_ID.unique())\n", "category_manager = filter(None, category_manager)\n", "category_manager = filter(bool, category_manager)\n", "category_manager = filter(len, category_manager)\n", "category_manager = filter(lambda item: item, category_manager)\n", "category_manager = list(filter(None, category_manager))\n", "category_manager = [x.replace(\" \", \"\") for x in category_manager if \" \" in x or \"\" in x]\n", "\n", "requestor_email = list(all_emails.Requestor_email_ID.unique())\n", "requestor_email = filter(None, requestor_email)\n", "requestor_email = filter(bool, requestor_email)\n", "requestor_email = filter(len, requestor_email)\n", "requestor_email = filter(lambda item: item, requestor_email)\n", "requestor_email = list(filter(None, requestor_email))\n", "requestor_email = [x.replace(\" \", \"\") for x in requestor_email if \" \" in x or \"\" in x]\n", "\n", "category_lead = list(all_emails.Category_Lead_email_ID.unique())\n", "category_lead = filter(None, category_lead)\n", "category_lead = filter(bool, category_lead)\n", "category_lead = filter(len, category_lead)\n", "category_lead = filter(lambda item: item, category_lead)\n", "category_lead = list(filter(None, category_lead))\n", "category_lead = [x.replace(\" \", \"\") for x in category_lead if \" \" in x or \"\" in x]\n", "\n", "function_lead = list(all_emails.Function_Lead_email_ID.unique())\n", "function_lead = filter(None, function_lead)\n", "function_lead = filter(bool, function_lead)\n", "function_lead = filter(len, function_lead)\n", "function_lead = filter(lambda item: item, function_lead)\n", "function_lead = list(filter(None, function_lead))\n", "function_lead = [x.replace(\" \", \"\") for x in function_lead if \" \" in x or \"\" in x]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["low_priority_email_list_0 = category_manager + requestor_email\n", "low_priority_email_list = [\n", "    x for x in low_priority_email_list_0 if \"@grofers.com\" in x or \"@handsontrades.com\" in x\n", "]\n", "med_priority_email_list_0 = category_manager + requestor_email + category_lead\n", "med_priority_email_list = [\n", "    x for x in med_priority_email_list_0 if \"@grofers.com\" in x or \"@handsontrades.com\" in x\n", "]\n", "high_priority_email_list_0 = category_manager + requestor_email + category_lead + function_lead\n", "high_priority_email_list = [\n", "    x for x in high_priority_email_list_0 if \"@grofers.com\" in x or \"@handsontrades.com\" in x\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cc_0_h = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "cc_h = [x for x in cc_0_h if \"@grofers.com\" in x or \"@handsontrades.com\" in x]\n", "cc_h"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "from jinja2 import Template"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "weekday = datetime.datetime.today().weekday()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = high.to_dict(orient=\"rows\")\n", "slab_head = \"Greater than 11 days\"\n", "slab = \"greater than 11 days\"\n", "link = (\n", "    \"https://docs.google.com/spreadsheets/d/1ihcBoKgiH2R9mDg7vImqDtHj2P_bi4SxKS9bDQMRb1I/edit#gid=0\"\n", ")\n", "from_email = \"<EMAIL>\"\n", "to_email = high_priority_email_list\n", "subject = \"AMS Escalation Matrix Report: \" + slab_head + \" - \" + str(today)\n", "\n", "email_template = \"escalation.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/povms/assortment/report/ams_escalation_matrix/\"\n", ")\n", "\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(summary=summary, time_period_considered=today, link=link, slab=slab)\n", "if weekday == 0:\n", "    pb.send_email(from_email, to_email, subject, html_content=rendered, cc=cc_h)\n", "    print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = medium.to_dict(orient=\"rows\")\n", "slab_head = \"7 - 11 days\"\n", "slab = \"7 - 11 days\"\n", "link = \"https://docs.google.com/spreadsheets/d/1ihcBoKgiH2R9mDg7vImqDtHj2P_bi4SxKS9bDQMRb1I/edit#gid=470831896\"\n", "from_email = \"<EMAIL>\"\n", "to_email = med_priority_email_list\n", "subject = (\n", "    \"AMS Escalation Matrix Report: \" + slab_head + \" - \" + str(today)\n", ")  # \"T-1: Escalation Matrix: \" + slab_head + \" - \" + str(today)\n", "\n", "email_template = \"escalation.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/povms/assortment/report/ams_escalation_matrix/\"\n", ")\n", "\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(summary=summary, time_period_considered=today, link=link, slab=slab)\n", "if weekday == 0 or weekday == 4:\n", "    pb.send_email(from_email, to_email, subject, html_content=rendered, cc=cc_h)\n", "    print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = low.to_dict(orient=\"rows\")\n", "slab_head = \"4 - 6 days\"\n", "slab = \"4 - 6 days\"\n", "link = \"https://docs.google.com/spreadsheets/d/1ihcBoKgiH2R9mDg7vImqDtHj2P_bi4SxKS9bDQMRb1I/edit#gid=920936326\"\n", "from_email = \"<EMAIL>\"\n", "to_email = low_priority_email_list\n", "subject = (\n", "    \"AMS Escalation Matrix Report: \" + slab_head + \" - \" + str(today)\n", ")  # \"T-1: Escalation Matrix: \" + slab_head + \" - \" + str(today)\n", "\n", "email_template = \"escalation.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/povms/assortment/report/ams_escalation_matrix/\"\n", ")\n", "\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(summary=summary, time_period_considered=today, link=link, slab=slab)\n", "\n", "pb.send_email(from_email, to_email, subject, html_content=rendered, cc=cc_h)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dates = open_request_2[[\"created_at\", \"slab\"]].drop_duplicates()\n", "dates[\"Priority\"] = dates.apply(slab_priority, axis=1)\n", "dates.columns = [\"Request Raised on\", \"Slab\", \"Priority\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["perc_status.columns = [\n", "    \"Slab\",\n", "    \"Total Open Request\",\n", "    \"Total Pending Request\",\n", "    \"Difference\",\n", "    \"Perc <PERSON>ding (of Total Open Request)\",\n", "    \"Perc Pending Share\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_summary_01 = pd.merge(\n", "    raw_summary,\n", "    facilitymapping,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "raw_summary_02 = pd.merge(\n", "    raw_summary_01,\n", "    manufac_name,\n", "    left_on=[\"item_id\"],\n", "    right_on=[\"item_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "raw_summary_03 = pd.merge(\n", "    raw_summary_02,\n", "    city_mapping,\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"facility_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "raw_summary_final = raw_summary_03[\n", "    [\n", "        \"l0\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"manufacturer_name\",\n", "        \"facility_id\",\n", "        \"city_id\",\n", "        \"facility_name\",\n", "        \"created_at\",\n", "        \"vendor_alignment_check\",\n", "        \"margin_check\",\n", "        \"cpd_check\",\n", "        \"tax_check\",\n", "        \"approval_check\",\n", "        \"slab\",\n", "    ]\n", "]\n", "raw_summary_final.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id_o = \"1ihcBoKgiH2R9mDg7vImqDtHj2P_bi4SxKS9bDQMRb1I\"\n", "sheet_high_priority = \"High Priority\"\n", "high.columns = [\n", "    \"L0_Category\",\n", "    \"Slab\",\n", "    \"Vendor Check\",\n", "    \"Tax Check\",\n", "    \"Margin Check\",\n", "    \"CPD Check\",\n", "    \"Approval Check\",\n", "    \"Total Pending Items\",\n", "]\n", "pb.to_sheets(high, sheet_id_o, sheet_high_priority)\n", "\n", "sheet_medium_priority = \"Medium Priority\"\n", "medium.columns = [\n", "    \"L0_Category\",\n", "    \"Slab\",\n", "    \"Vendor Check\",\n", "    \"Tax Check\",\n", "    \"Margin Check\",\n", "    \"CPD Check\",\n", "    \"Approval Check\",\n", "    \"Total Pending Items\",\n", "]\n", "pb.to_sheets(medium, sheet_id_o, sheet_medium_priority)\n", "\n", "sheet_low_priority = \"Low Priority\"\n", "low.columns = [\n", "    \"L0_Category\",\n", "    \"Slab\",\n", "    \"Vendor Check\",\n", "    \"Tax Check\",\n", "    \"Margin Check\",\n", "    \"CPD Check\",\n", "    \"Approval Check\",\n", "    \"Total Pending Items\",\n", "]\n", "pb.to_sheets(low, sheet_id_o, sheet_low_priority)\n", "\n", "sheet_very_low_priority = \"Very Low Priority\"\n", "very_low.columns = [\n", "    \"L0_Category\",\n", "    \"Slab\",\n", "    \"Vendor Check\",\n", "    \"Tax Check\",\n", "    \"Margin Check\",\n", "    \"CPD Check\",\n", "    \"Approval Check\",\n", "    \"Total Pending Items\",\n", "]\n", "pb.to_sheets(very_low, sheet_id_o, sheet_very_low_priority)\n", "\n", "sheet_raw_summary_priority = \"Raw Data\"\n", "pb.to_sheets(raw_summary_final, sheet_id_o, sheet_raw_summary_priority)\n", "\n", "sheet_perc_status_priority = \"Summary\"\n", "pb.to_sheets(perc_status, sheet_id_o, sheet_perc_status_priority)\n", "\n", "sheet_perc_status_priority = \"Priority Definition\"\n", "pb.to_sheets(dates, sheet_id_o, sheet_perc_status_priority)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}