dag_name: sanity
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: sonal
  slack_id: UABB9AK9V
path: povms/assortment/report/sanity
paused: true
pool: povms_pool
project_name: assortment
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 6 * * *
  start_date: '2019-08-01T06:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- email_template.html
tags: []
template_name: notebook
version: 1
