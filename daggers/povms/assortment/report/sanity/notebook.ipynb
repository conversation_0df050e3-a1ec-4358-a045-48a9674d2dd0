{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ignoring warnings\n", "\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing Libraries\n", "\n", "import sqlalchemy as sqla\n", "import pandas as pd\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select\n", "base.*,\n", "tat_days\n", "from \n", "(\n", "select po.*,\n", "po_days\n", "from \n", "(\n", "select \n", "maf.*,\n", "po_cycle\n", "from \n", "(\n", "select \n", "sen.*,\n", "manufacturer_id\n", "from\n", "(\n", "select \n", "phy.*,\n", "case_sensitivity_type\n", "from \n", "(\n", "select\n", "vendor.*,\n", "load_size\n", "from \n", "(\n", "select cpd.*,\n", "aligned_vendor_id\n", "from \n", "(\n", "select \n", "tag_mapping.*,\n", "cpd\n", "from \n", "(\n", "select univ.*,\n", "case when map.tag_type_id=8 then 'yes' else 'no' end as transfer_flag\n", "from \n", "(\n", "select outlet.*,\n", "item_id\n", "from \n", "(\n", "select job_run.*,\n", "o.outlet_id,\n", "o.name\n", "from \n", "(\n", "select facility_id,date(started_at) as started_at,max(run_id) as run_id from ars_job_run \n", "where date(started_at)=current_date\n", "and is_simulation=0 \n", "group by facility_id,date(started_at)\n", ")job_run\n", "inner join ars_outlet as o\n", "on job_run.facility_id=o.physical_facility_id\n", "and job_run.run_id=o.run_id\n", ") as outlet\n", "inner join ars_outlet_item_universe as uni\n", "on outlet.run_id=uni.run_id\n", "and outlet.outlet_id=uni.outlet_id\n", ") as univ\n", "left join rpc_item_outlet_tag_mapping as map\n", "on univ.item_id=map.item_id\n", "and univ.outlet_id=map.outlet_id\n", "and tag_type_id=8\n", ")tag_mapping\n", "left join ars_outlet_item_cpd as cpd\n", "on cpd.run_id=tag_mapping.run_id\n", "and cpd.item_id=tag_mapping.item_id\n", "and cpd.outlet_id=tag_mapping.outlet_id\n", ") cpd\n", " left join ars_city_item_alignment as ali\n", " on cpd.run_id=ali.run_id\n", " and cpd.item_id=ali.item_id\n", ") vendor\n", "left join ars_physical_facility_vendor_item_details as phys\n", "on phys.run_id=vendor.run_id\n", "and phys.item_id=vendor.item_id\n", "and phys.aligned_vendor_id=vendor.aligned_vendor_id\n", ")phy\n", "left join ars_physical_facility_vendor_item_details as phys\n", "on phys.run_id=phy.run_id\n", "and phys.item_id=phy.item_id\n", "and phys.aligned_vendor_id=phy.aligned_vendor_id\n", ")sen\n", "inner join ars_item_details as itm\n", "on itm.item_id=sen.item_id\n", "and itm.run_id=sen.run_id\n", ") as maf\n", "left join ars_physical_facility_vendor_manufacturer_details as mf\n", "on maf.run_id=mf.run_id\n", "and maf.manufacturer_id=mf.manufacturer_id\n", "and  maf.aligned_vendor_id=mf.aligned_vendor_id\n", ")po\n", "left join ars_physical_facility_vendor_manufacturer_details as mf\n", "on po.run_id=mf.run_id\n", "and po.manufacturer_id=mf.manufacturer_id\n", "and  po.aligned_vendor_id=mf.aligned_vendor_id\n", ")base \n", "left join ars_outlet_vendor_item_tat_days as tat\n", "on base.item_id=tat.item_id\n", "and base.run_id=tat.run_id\n", "and base.outlet_id=tat.outlet_id\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value = pd.read_sql_query(query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_without_na = assortment_value[assortment_value.po_days.notna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_with_na = assortment_value[assortment_value.po_days.isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_value_po_without_na[\"po_days_length\"] = assortment_value_po_without_na.apply(\n", "    lambda x: len(x[\"po_days\"].split(\",\")), axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_full = pd.concat([assortment_value_po_without_na, assortment_value_po_with_na])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total = (\n", "    assortment_value[assortment_value.transfer_flag == \"no\"]\n", "    .groupby([\"facility_id\"])[\"item_id\"]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total = total.rename(columns={\"item_id\": \"total_item\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero = (\n", "    assortment_value[(assortment_value.cpd == 0) & (assortment_value.transfer_flag == \"no\")]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero = cpd_zero.rename(columns={\"item_id\": \"cpd_zero\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_per = total.merge(cpd_zero, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_zero_per[\"cpd_zero_per\"] = round(((cpd_zero_per.cpd_zero / cpd_zero_per.total_item) * 100), 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing = (\n", "    assortment_value[(assortment_value.cpd.isna()) & (assortment_value.transfer_flag == \"no\")]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing = cpd_missing.rename(columns={\"item_id\": \"cpd_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_merge = cpd_zero_per.merge(cpd_missing, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpd_missing_merge[\"cpd_missing_per\"] = round(\n", "    ((cpd_missing_merge.cpd_missing / cpd_missing_merge.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aligned_vendor = (\n", "    assortment_value[\n", "        (assortment_value.aligned_vendor_id.isna()) & (assortment_value.transfer_flag == \"no\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aligned_vendor = aligned_vendor.rename(columns={\"item_id\": \"aligned_vendor_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_missing_per = cpd_missing_merge.merge(aligned_vendor, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_missing_per[\"aligned_vendor_missing_per\"] = round(\n", "    ((vendor_missing_per.aligned_vendor_missing / vendor_missing_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size = (\n", "    assortment_value[(assortment_value.load_size.isna()) & (assortment_value.transfer_flag == \"no\")]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size = load_size.rename(columns={\"item_id\": \"load_size_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_missing_per = vendor_missing_per.merge(load_size, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_size_missing_per[\"load_size_missing_per\"] = round(\n", "    ((load_size_missing_per.load_size_missing / load_size_missing_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type = (\n", "    assortment_value[\n", "        (assortment_value.case_sensitivity_type.isna()) & (assortment_value.transfer_flag == \"no\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type = case_sensitivity_type.rename(\n", "    columns={\"item_id\": \"case_sensitivity_type_missing\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_per = load_size_missing_per.merge(\n", "    case_sensitivity_type, on=\"facility_id\", how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_sensitivity_type_per[\"case_sensitivity_missing_per\"] = round(\n", "    (\n", "        (\n", "            case_sensitivity_type_per.case_sensitivity_type_missing\n", "            / case_sensitivity_type_per.total_item\n", "        )\n", "        * 100\n", "    ),\n", "    2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days = (\n", "    assortment_value[\n", "        (assortment_value.case_sensitivity_type.isna()) & (assortment_value.transfer_flag == \"no\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days = tat_days.rename(columns={\"item_id\": \"tat_days_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_per = case_sensitivity_type_per.merge(tat_days, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tat_days_per[\"tat_days_missing_per\"] = round(\n", "    ((tat_days_per.tat_days_missing / tat_days_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle = (\n", "    assortment_value[\n", "        (assortment_value.case_sensitivity_type.isna()) & (assortment_value.transfer_flag == \"no\")\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle = po_cycle.rename(columns={\"item_id\": \"po_cycle_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_per = tat_days_per.merge(po_cycle, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_cycle_per[\"po_cycle_missing_per\"] = round(\n", "    ((po_cycle_per.po_cycle_missing / po_cycle_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing = (\n", "    assortment_full[\n", "        (assortment_full.po_days.isna()) & (assortment_full.transfer_flag == \"no\")\n", "        | ((assortment_full.po_cycle == 7) & (assortment_full.po_days_length < 1))\n", "        | ((assortment_full.po_cycle == 6) & (assortment_full.po_days_length < 1))\n", "        | ((assortment_full.po_cycle == 5) & (assortment_full.po_days_length < 1))\n", "        | ((assortment_full.po_cycle == 4) & (assortment_full.po_days_length < 2))\n", "        | ((assortment_full.po_cycle == 3) & (assortment_full.po_days_length < 2))\n", "        | ((assortment_full.po_cycle == 2) & (assortment_full.po_days_length < 3))\n", "        | ((assortment_full.po_cycle == 1) & (assortment_full.po_days_length < 5))\n", "    ]\n", "    .groupby([\"facility_id\"])[[\"item_id\"]]\n", "    .nunique()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing = po_days_missing.rename(columns={\"item_id\": \"po_days_missing\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_per = po_cycle_per.merge(po_days_missing, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_days_missing_per[\"po_days_missing_per\"] = round(\n", "    ((po_days_missing_per.po_days_missing / po_days_missing_per.total_item) * 100), 2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_facility_names = \"\"\"\n", "select distinct facility_id ,cf.name as facility_name from pos_console_outlet pco\n", "left join crates_facility cf\n", "on pco.facility_id=cf.id\n", "where upper(pco.name) LIKE '%%SSC%%'\n", "        AND upper(pco.name) NOT LIKE '%%AC%%'\n", "        AND upper(pco.name) NOT LIKE '%%HOT%%'\n", "         \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["city_facility_mapping = pd.read_sql_query(sql=city_facility_names, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity = po_days_missing_per.merge(city_facility_mapping, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india = assortment_sanity[\n", "    [\n", "        \"total_item\",\n", "        \"cpd_zero\",\n", "        \"cpd_zero_per\",\n", "        \"cpd_missing\",\n", "        \"cpd_missing_per\",\n", "        \"aligned_vendor_missing\",\n", "        \"aligned_vendor_missing_per\",\n", "        \"load_size_missing\",\n", "        \"load_size_missing_per\",\n", "        \"case_sensitivity_type_missing\",\n", "        \"case_sensitivity_missing_per\",\n", "        \"tat_days_missing\",\n", "        \"tat_days_missing_per\",\n", "        \"po_cycle_missing\",\n", "        \"po_cycle_missing_per\",\n", "        \"po_days_missing\",\n", "        \"po_days_missing_per\",\n", "        \"facility_name\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india[\"facility_name\"] = \"Pan India\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup = round(\n", "    pan_india.groupby([\"facility_name\"]).agg(\n", "        {\n", "            \"total_item\": \"sum\",\n", "            \"cpd_zero\": \"sum\",\n", "            \"cpd_zero_per\": \"mean\",\n", "            \"cpd_missing\": \"sum\",\n", "            \"cpd_missing_per\": \"mean\",\n", "            \"aligned_vendor_missing\": \"sum\",\n", "            \"aligned_vendor_missing_per\": \"mean\",\n", "            \"load_size_missing\": \"sum\",\n", "            \"load_size_missing_per\": \"mean\",\n", "            \"case_sensitivity_type_missing\": \"sum\",\n", "            \"case_sensitivity_missing_per\": \"mean\",\n", "            \"tat_days_missing\": \"sum\",\n", "            \"tat_days_missing_per\": \"mean\",\n", "            \"po_cycle_missing\": \"sum\",\n", "            \"po_cycle_missing_per\": \"mean\",\n", "            \"po_days_missing\": \"sum\",\n", "            \"po_days_missing_per\": \"mean\",\n", "        }\n", "    ),\n", "    2,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_1 = assortment_sanity.append(pan_india_rollup)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_sanity_1.cpd_zero_per = assortment_sanity_1.cpd_zero_per.astype(str) + \"%\"\n", "assortment_sanity_1.cpd_missing_per = assortment_sanity_1.cpd_missing_per.astype(str) + \"%\"\n", "assortment_sanity_1.load_size_missing_per = (\n", "    assortment_sanity_1.load_size_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_1.case_sensitivity_missing_per = (\n", "    assortment_sanity_1.case_sensitivity_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_1.tat_days_missing_per = (\n", "    assortment_sanity_1.tat_days_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_1.po_cycle_missing_per = (\n", "    assortment_sanity_1.po_cycle_missing_per.astype(str) + \"%\"\n", ")\n", "assortment_sanity_1.po_days_missing_per = assortment_sanity_1.po_days_missing_per.astype(str) + \"%\"\n", "assortment_sanity_1.aligned_vendor_missing_per = (\n", "    assortment_sanity_1.aligned_vendor_missing_per.astype(str) + \"%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = assortment_value[\n", "    [\n", "        \"facility_id\",\n", "        \"run_id\",\n", "        \"outlet_id\",\n", "        \"item_id\",\n", "        \"transfer_flag\",\n", "        \"cpd\",\n", "        \"aligned_vendor_id\",\n", "        \"load_size\",\n", "        \"case_sensitivity_type\",\n", "        \"po_cycle\",\n", "        \"po_days\",\n", "        \"tat_days\",\n", "        \"manufacturer_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row = d[\n", "    d.cpd.isna() | d.cpd\n", "    == 0\n", "    | d.aligned_vendor_id.isna()\n", "    | d.load_size.isna()\n", "    | d.case_sensitivity_type.isna()\n", "    | d.po_cycle.isna()\n", "    | d.po_days.isna()\n", "    | d.tat_days.isna()\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["todays_date = datetime.now().strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_row.to_csv(\n", "    \"/tmp/assortment_sanity_report-{}.csv\".format(todays_date), encoding=\"utf8\", index=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = assortment_sanity_1.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "\n", "\n", "from_email = \"<EMAIL>\"\n", "to_email = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "# to_email=[\"<EMAIL>\"]\n", "subject = \"Facility wise Assortment Sanity Report\"\n", "\n", "# to_email=[\"<EMAIL>\"]\n", "#           ,\"<EMAIL>\",\"<EMAIL>\"]\n", "\n", "\n", "email_template = \"email_template.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/povms/assortment/report/sanity\"\n", ")\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(products=items)\n", "\n", "# send_email(sender, to, subject, html_content = rendered,\n", "#                     dryrun=False, cc=cc, bcc=None,\n", "#                     mime_subtype='mixed', mime_charset='utf-8')\n", "\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    html_content=rendered,\n", "    files=[\"/tmp/assortment_sanity_report-{}.csv\".format(todays_date)],\n", ")\n", "\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 2}