<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}
</style>
</head>
<p>Hi All,<br/>
Mentioned below is the Assortment Sanity Report for today.<br/>
Please find attach copy for row data.<br/>
</p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Total Items</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">cpd_zero</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">cpd_zero(in %)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">cpd_missing</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">cpd_missing(in %)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">po_cycle_missing</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">po_cycle_missing(in %)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">po_days_missing</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">po_days_missing(in %)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">aligned_vendor_id_missing</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">aligned_vendor_id_missing(in %)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">load_size_missing</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">load_size_missing(in %)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">case_sensitivity_type_missing</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">case_sensitivity_type_missing(in %)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">tat_days_missing</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">tat_days_missing(in %)</span></th>
        </tr>
        {% for product in products %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.facility_name}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.total_item}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.cpd_zero}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.cpd_zero_per}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.cpd_missing}}</b>
            </td>
             <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.cpd_missing_per}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.po_cycle_missing}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.po_cycle_missing_per}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.po_days_missing}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.po_days_missing_per}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.aligned_vendor_missing}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.aligned_vendor_missing_per}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.load_size_missing}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.load_size_missing_per}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.case_sensitivity_type_missing}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.case_sensitivity_missing_per}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.tat_days_missing}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.tat_days_missing_per}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>



<p>Note:We Exculded the transfered articles </p>

<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>