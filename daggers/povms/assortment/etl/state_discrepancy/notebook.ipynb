{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pencilbox as pb\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1f2tHZcJiTrqSw-BMG1bKIbEPaXXbWBkCx6Er4N0n9zY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_eligible_facs = \"\"\"select distinct facility_id from\n", "    (select o.facility_id, count(distinct order_id) as order_count\n", "    from lake_ims.ims_order_details a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where date(a.created_at) between current_date-interval '2 days' and current_date-interval '1 days'\n", "    and a.status_id <> 5\n", "    and business_type not ilike '%%b2b%%'\n", "    and business_type_id in (7)\n", "    group by 1) b\n", "where order_count > 10\"\"\"\n", "\n", "eligible_fes = pd.read_sql(sql_eligible_facs, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["keep_facility_id_list = list(eligible_fes[\"facility_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remove_facility_id = pb.from_sheets(sheet_id, \"Non-operational Dark store\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remove_facility_id_list = list(remove_facility_id[\"frontend_facility_id\"].astype(int).unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_list = [x for x in keep_facility_id_list if (x not in remove_facility_id_list)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_active_fe = \"\"\"\n", "WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          (case when C1.NAME = C.name then C2.id else C1.id end) AS L1_id,\n", "          C.ID AS L0_id,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ),\n", "  \n", "item_details as (SELECT item_id,\n", "       cat.product as item_name,\n", "       cat.unit as UOM,\n", "       cat.L0_id,\n", "       cat.L0,\n", "       cat.L1_id,\n", "       cat.L1\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL),\n", "\n", "bucket_info as\n", "(\n", "select \n", "item_id, \n", "facility_id,\n", "count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "from\n", "( \n", "Select \n", "b.item_id, \n", "po.facility_id,\n", "tag_value,\n", "tag_type_id \n", "from  lake_rpc.item_outlet_tag_mapping b\n", "join lake_retail.console_outlet po ON b.outlet_id = po.id\n", "where b.active = 1\n", "group by 1,2,3,4\n", ")\n", "group by 1,2\n", "),\n", "\n", "bucket_tf as\n", "(\n", "select \n", "item_id,\n", "facility_id,\n", "case when tag_transfer >= 1 then 'transfer' else 'None' end as transfer_type\n", "from bucket_info\n", "),\n", "\n", "fe_be_mapping as (\n", "SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    ro.tax_location_id AS frontend_city_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    r.tax_location_id AS backend_city_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND ro.business_type_id <> 8),\n", "\n", "tax_check as (select a.item_id, cif.facility_id , a.igst, a.cgst, a.sgst\n", "from  lake_rpc.product_tax a\n", "left join (select tax_location_id as city_id, facility_id from lake_retail.console_outlet group by 1,2) cif on cif.city_id = a.location\n", "group by 1,2,3,4,5),\n", "\n", "margin_check as (select a.item_id,a.facility_id ,a.landing_price, a.on_invoice_margin_value \n", "from  lake_rpc.tot_margin a\n", "inner join (select item_id, facility_id , max(updated_at) as m_updated_at from lake_rpc.tot_margin group by 1,2)b \n", "on a.item_id = b.item_id and a.facility_id = b.facility_id and a.updated_at = b.m_updated_at\n", "group by 1,2,3,4),\n", "\n", "cpd_check as (select \n", "a.item_id ,\n", "b.facility_id,\n", "sum(cpd) as fc_qty\n", "from lake_snorlax.default_cpd a\n", "left join lake_retail.view_console_outlet b on a.outlet_id = b.id \n", "group by 1,2),\n", "\n", "vendor_check as (select item_id, facility_id , vendor_id, vn.vendor_name\n", "from  lake_vms.vms_vendor_facility_alignment a\n", "left join  lake_vms.vms_vendor vn on vn.id = a. vendor_id \n", "where a.active = 1  \n", "group by 1,2,3,4),\n", "\n", "assortment_status as (\n", "select a.item_id, a.facility_id, a.city_id, a.active, a.master_assortment_substate_id, a.updated_at as activation_date\n", "    from lake_rpc.product_facility_master_assortment a\n", "    inner join \n", "    (select item_id, facility_id, max(updated_at) as max_updated_at\n", "    from lake_rpc.product_facility_master_assortment\n", "    group by 1,2) b\n", "    on a.item_id = b.item_id and a.facility_id = b.facility_id and a.updated_at = b.max_updated_at\n", "where a.active=1\n", ")\n", "\n", "select \n", "distinct a.item_id, (case when itm.item_name is null then itm2.name else itm.item_name end) as item_name,\n", "(case when itm.uom is null then itm2.variant_description else itm.uom end) as uom, \n", "itm.l0_id, itm.l0, itm.l1_id, itm.l1, \n", "itm2.storage_type,\n", "(case when itm2.storage_type = 1 then 'Regular'\n", "when itm2.storage_type = 2 then 'Fridge'\n", "when itm2.storage_type = 3 then 'Freezer'\n", "when itm2.storage_type = 4 then 'Large'\n", "when itm2.storage_type = 5 then 'Heavy'\n", "when itm2.storage_type = 6 then 'Non Veg Cold'\n", "when itm2.storage_type = 7 then 'Non Veg Frozen'\n", "when itm2.storage_type = 8 then 'Fast moving'\n", "end) as storage_type_name,\n", "b.frontend_facility_name, b.frontend_facility_id, (case when a.city_id is null then b.frontend_city_id else a.city_id end) as frontend_city_id,\n", "b.backend_facility_name, b.backend_facility_id, (case when c.city_id is null then b.backend_city_id else c.city_id end) as backend_city_id,\n", "a.activation_date as frontend_activation_date,\n", "(case when a.master_assortment_substate_id = 1 then 'active' else 'inactive' end) as frontend_state, \n", "(case when c.master_assortment_substate_id = 1 then 'active' else 'inactive' end) as backend_state,\n", "(case\n", "when (tax.igst >= 0)  then 'Y'\n", "else  'N' end) as Backend_TAX,\n", "\n", "(case \n", "when (ven.vendor_id is null or ven.vendor_id = 0) then 'N'  \n", "else 'Y' end) as <PERSON><PERSON>_<PERSON>endor,\n", "\n", "(case\n", "when ((margin.landing_price > 0 or margin.on_invoice_margin_value > 0)) then 'Y'\n", "else  'N' end) as <PERSON><PERSON>_<PERSON>,\n", "\n", "(case when cpd.fc_qty>0 then 'Y' else 'N' end) as Backend_Cpd,\n", "\n", "itm2.perishable as perishable_flag\n", "\n", "-- (case when tea.transfer_type = 'transfer' then 'Y' else 'N' end) as TEA\n", "\n", "from assortment_status a\n", "inner join fe_be_mapping b on a.item_id = b.item_id and a.facility_id = b.frontend_facility_id\n", "inner join lake_po.physical_facility_outlet_mapping pfom on b.backend_facility_id = pfom.facility_id and pfom.active = 1 and pfom.ars_active = 1\n", "inner join lake_po.physical_facility_outlet_mapping pfom1 on b.frontend_facility_id = pfom1.facility_id and pfom1.active = 1 and pfom1.ars_active = 1\n", "left join assortment_status c on a.item_id = c.item_id and b.backend_facility_id = c.facility_id\n", "left join item_details itm on a.item_id = itm.item_id\n", "left join (select * from(\n", "    select *, rank() over (partition by item_id order by id desc) as rnk \n", "    from lake_rpc.product_product\n", ") where rnk =1) itm2 on a.item_id = itm2.item_id\n", "-- left join bucket_tf tea on a.item_id = tea.item_id and a.facility_id = tea.facility_id\n", "left join tax_check tax on a.item_id = tax.item_id and b.backend_facility_id = tax.facility_id\n", "left join margin_check margin on a.item_id = margin.item_id and b.backend_facility_id = margin.facility_id \n", "left join cpd_check cpd on a.item_id = cpd.item_id and b.backend_facility_id = cpd.facility_id\n", "left join vendor_check ven on a.item_id = ven.item_id and b.backend_facility_id = ven.facility_id\n", "\n", "where a.master_assortment_substate_id=1\n", "and (c.master_assortment_substate_id<>1 or c.master_assortment_substate_id is null)\n", "and b.frontend_facility_id in ({final_list})\n", "\n", "order by a.facility_id, a.item_id \n", "\"\"\".format(\n", "    final_list=\", \".join(str(y) for y in final_list)\n", ")\n", "\n", "itm_fe = pd.read_sql(sql_active_fe, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_fe[(itm_fe.item_id == 10082866) & (itm_fe.frontend_facility_id == 246)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# itm_active_fe = itm_fe.merge(\n", "#     mapping, how=\"inner\", on=[\"frontend_facility_id\", \"backend_facility_id\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_fe.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_fe.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_active_fe = itm_fe.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repellent = itm_active_fe[\n", "    (itm_active_fe.frontend_facility_id.isin([140, 149, 264])) & (itm_active_fe.l1_id == 926)\n", "]\n", "# 139,"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repellent.drop(\n", "    [\n", "        \"backend_facility_name\",\n", "        \"backend_facility_id\",\n", "        \"backend_city_id\",\n", "        \"frontend_state\",\n", "        \"backend_state\",\n", "        \"backend_tax\",\n", "        \"backend_vendor\",\n", "        \"backend_margin\",\n", "        \"backend_cpd\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(repellent, sheet_id, \"L1 repellents\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# repellent.to_csv('repellent.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_active_fe.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove l1 repellent\n", "\n", "itm_active_fe = itm_active_fe[\n", "    (~itm_active_fe.frontend_facility_id.isin([140, 149, 264])) | (itm_active_fe.l1_id != 926)\n", "]\n", "# 139,"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove cold from 63\n", "\n", "itm_active_fe = itm_active_fe[\n", "    (~itm_active_fe.frontend_facility_id.isin([63]))\n", "    | (~itm_active_fe.storage_type.isin([\"2\", \"6\"]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove freezer, non veg frozen from 63,140,149\n", "\n", "itm_active_fe = itm_active_fe[\n", "    (~itm_active_fe.frontend_facility_id.isin([63, 140, 149]))\n", "    | (~itm_active_fe.storage_type.isin([\"3\", \"7\"]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove GM heavy from dark stores mapped to G4\n", "itm_active_fe = itm_active_fe[\n", "    (~itm_active_fe.l0_id.isin([343, 909, 1047, 1379, 1560, 1616, 1710, 1878, 1879]))\n", "    | (itm_active_fe.storage_type != \"Heavy\")\n", "    | (~itm_active_fe.frontend_facility_name.str.contains(\" ES\"))\n", "    | (~itm_active_fe.backend_facility_id.isin([29]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_active_fe.drop(\n", "    [\n", "        \"backend_city_id\",\n", "        \"frontend_city_id\",\n", "        \"l0_id\",\n", "        \"l1_id\",\n", "        \"l1\",\n", "        \"storage_type\",\n", "        \"storage_type_name\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_active_fe.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(itm_active_fe, sheet_id, \"Items active in FE not active in BE\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# itm_active_fe.to_csv('Items active in FE not active in BE.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_active_be = \"\"\"WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          (case when C1.NAME = C.name then C2.id else C1.id end) AS L1_id,\n", "          C.ID AS L0_id,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ),\n", "  \n", "item_details as (SELECT item_id,\n", "       cat.product as item_name,\n", "       cat.unit as UOM,\n", "       cat.L0_id,\n", "       cat.L0,\n", "       cat.L1_id,\n", "       cat.L1\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL),\n", "\n", "bucket_info as\n", "(\n", "select \n", "item_id, \n", "facility_id,\n", "count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "from\n", "( \n", "Select \n", "b.item_id, \n", "po.facility_id,\n", "tag_value,\n", "tag_type_id \n", "from  lake_rpc.item_outlet_tag_mapping b\n", "join lake_retail.console_outlet po ON b.outlet_id = po.id\n", "where b.active = 1\n", "group by 1,2,3,4\n", ")\n", "group by 1,2\n", "),\n", "\n", "bucket_tf as\n", "(\n", "select \n", "item_id,\n", "facility_id,\n", "case when tag_transfer >= 1 then 'transfer' else 'None' end as transfer_type\n", "from bucket_info\n", "),\n", "\n", "fe_be_mapping as (\n", "SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    ro.tax_location_id AS frontend_city_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    r.tax_location_id AS backend_city_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND ro.business_type_id <> 8),\n", "\n", "tax_check as (select a.item_id, cif.facility_id , a.igst, a.cgst, a.sgst\n", "from  lake_rpc.product_tax a\n", "left join (select tax_location_id as city_id, facility_id from lake_retail.console_outlet group by 1,2) cif on cif.city_id = a.location\n", "group by 1,2,3,4,5),\n", "\n", "margin_check as (select a.item_id,a.facility_id ,a.landing_price, a.on_invoice_margin_value \n", "from  lake_rpc.tot_margin a\n", "inner join (select item_id, facility_id , max(updated_at) as m_updated_at from lake_rpc.tot_margin group by 1,2)b \n", "on a.item_id = b.item_id and a.facility_id = b.facility_id and a.updated_at = b.m_updated_at\n", "group by 1,2,3,4),\n", "\n", "cpd_check as (select \n", "a.item_id ,\n", "b.facility_id,\n", "sum(cpd) as fc_qty\n", "from lake_snorlax.default_cpd a\n", "left join lake_retail.view_console_outlet b on a.outlet_id = b.id \n", "group by 1,2),\n", "\n", "vendor_check as (select item_id, facility_id , vendor_id, vn.vendor_name\n", "from  lake_vms.vms_vendor_facility_alignment a\n", "left join  lake_vms.vms_vendor vn on vn.id = a. vendor_id \n", "where a.active = 1  \n", "group by 1,2,3,4),\n", "\n", "assortment_status as (\n", "select a.item_id, a.facility_id, a.city_id, a.active, a.master_assortment_substate_id, a.updated_at as activation_date\n", "    from lake_rpc.product_facility_master_assortment a\n", "    inner join \n", "    (select item_id, facility_id, max(updated_at) as max_updated_at\n", "    from lake_rpc.product_facility_master_assortment\n", "    group by 1,2) b\n", "    on a.item_id = b.item_id and a.facility_id = b.facility_id and a.updated_at = b.max_updated_at\n", "where a.active=1\n", ")\n", "\n", "select *, (case when frontend_facility_name ilike '%% ES%%' then Backend_Vendor else Frontend_Vendor end) as Vendor_Check\n", "from (select \n", "distinct a.item_id, (case when itm.item_name is null then itm2.name else itm.item_name end) as item_name,\n", "(case when itm.uom is null then itm2.variant_description else itm.uom end) as uom,\n", "itm.l0_id, itm.l0, itm.l1_id, itm.l1, \n", "itm2.perishable as perishable_flag,\n", "itm2.storage_type,\n", "(case when itm2.storage_type = 1 then 'Regular'\n", "when itm2.storage_type = 2 then 'Fridge'\n", "when itm2.storage_type = 3 then 'Freezer'\n", "when itm2.storage_type = 4 then 'Large'\n", "when itm2.storage_type = 5 then 'Heavy'\n", "when itm2.storage_type = 6 then 'Non Veg Cold'\n", "when itm2.storage_type = 7 then 'Non Veg Frozen'\n", "when itm2.storage_type = 8 then 'Fast moving'\n", "end) as storage_type_name,\n", "b.backend_facility_name, b.backend_facility_id, (case when c.city_id is null then b.backend_city_id else c.city_id end) as backend_city_id,\n", "b.frontend_facility_name, b.frontend_facility_id, (case when a.city_id is null then b.frontend_city_id else a.city_id end) as frontend_city_id,\n", "-- a.master_assortment_substate_id as frontend_substate, \n", "-- c.master_assortment_substate_id as backend_substate,\n", "a.activation_date as backend_activation_date,\n", "(case\n", "when (tax.igst >= 0)  then 'Y'\n", "else  'N' end) as Frontend_TAX,\n", "(case when tea.transfer_type = 'transfer' then 'Y' else 'N' end) as Frontend_TEA,\n", "(case\n", "when (margin.landing_price > 0 or margin.on_invoice_margin_value > 0) then 'Y'\n", "else  'N' end) as <PERSON><PERSON>_<PERSON>,\n", "(case when cpd.fc_qty>0 then 'Y' else 'N' end) as Frontend_CPD,\n", "(case \n", "when tea.transfer_type = 'transfer' and (backend_ven.vendor_id is null or backend_ven.vendor_id = 0) then 'N'  \n", "else 'Y' end) as <PERSON><PERSON>_<PERSON>endor,\n", "(case \n", "when tea.transfer_type = 'transfer' and (frontend_ven.vendor_id is null or frontend_ven.vendor_id = 0) then 'N'  \n", "else 'Y' end) as <PERSON><PERSON>_<PERSON>endor\n", "\n", "-- (case\n", "-- when (be_margin.landing_price > 0 or be_margin.on_invoice_margin_value > 0) then 'Y'\n", "-- else  'N' end) as <PERSON><PERSON>_<PERSON><PERSON>,\n", "\n", "\n", "\n", "from assortment_status a\n", "inner join fe_be_mapping b on a.item_id = b.item_id and a.facility_id = b.backend_facility_id\n", "inner join lake_po.physical_facility_outlet_mapping pfom on b.backend_facility_id = pfom.facility_id and pfom.active = 1 and pfom.ars_active = 1\n", "inner join lake_po.physical_facility_outlet_mapping pfom1 on b.frontend_facility_id = pfom1.facility_id and pfom1.active = 1 and pfom1.ars_active = 1\n", "left join assortment_status c on a.item_id = c.item_id and b.frontend_facility_id = c.facility_id\n", "left join item_details itm on a.item_id = itm.item_id\n", "left join (select * from(\n", "    select *, rank() over (partition by item_id order by id desc) as rnk \n", "    from lake_rpc.product_product\n", ") where rnk =1) itm2 on a.item_id = itm2.item_id\n", "left join bucket_tf tea on a.item_id = tea.item_id and b.frontend_facility_id = tea.facility_id\n", "left join tax_check tax on a.item_id = tax.item_id and b.frontend_facility_id = tax.facility_id\n", "left join margin_check margin on a.item_id = margin.item_id and b.frontend_facility_id = margin.facility_id \n", "-- left join margin_check be_margin on a.item_id = be_margin.item_id and b.backend_facility_id = be_margin.facility_id \n", "left join cpd_check cpd on a.item_id = cpd.item_id and b.frontend_facility_id = cpd.facility_id\n", "left join vendor_check backend_ven on a.item_id = backend_ven.item_id and b.backend_facility_id = backend_ven.facility_id\n", "left join vendor_check frontend_ven on a.item_id = frontend_ven.item_id and b.frontend_facility_id = frontend_ven.facility_id\n", "\n", "where a.master_assortment_substate_id=1\n", "and (c.master_assortment_substate_id<>1 or c.master_assortment_substate_id is null)\n", "and b.frontend_facility_id in ({final_list})\n", "\n", "order by b.backend_facility_id, a.item_id)\n", "\"\"\".format(\n", "    final_list=\", \".join(str(y) for y in final_list)\n", ")\n", "\n", "itm_be = pd.read_sql(sql_active_be, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# itm_active_be = itm_be.merge(\n", "#     mapping, how=\"inner\", on=[\"frontend_facility_id\", \"backend_facility_id\"]\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_be[(itm_be.item_id == 10002663) & (itm_be.frontend_facility_id == 441)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_be.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_active_be = itm_be.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove l1 repellent\n", "\n", "itm_active_be = itm_active_be[\n", "    (~itm_active_be.frontend_facility_id.isin([140, 149, 264])) | (itm_active_be.l1_id != 926)\n", "]\n", "\n", "# 139,"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove cold from 63\n", "\n", "itm_active_be = itm_active_be[\n", "    (~itm_active_be.frontend_facility_id.isin([63]))\n", "    | (~itm_active_be.storage_type.isin([\"2\", \"6\"]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove freezer, non veg frozen from 63,140,149\n", "\n", "itm_active_be = itm_active_be[\n", "    (~itm_active_be.frontend_facility_id.isin([63, 140, 149]))\n", "    | (~itm_active_be.storage_type.isin([\"3\", \"7\"]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## remove GM heavy from dark stores mapped to G4\n", "itm_active_be = itm_active_be[\n", "    (~itm_active_be.l0_id.isin([343, 909, 1047, 1379, 1560, 1616, 1710, 1878, 1879]))\n", "    | (itm_active_be.storage_type != \"Heavy\")\n", "    | (~itm_active_be.frontend_facility_name.str.contains(\" ES\"))\n", "    | (~itm_active_be.backend_facility_id.isin([29]))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_active_be.drop(\n", "    [\n", "        \"backend_city_id\",\n", "        \"frontend_city_id\",\n", "        \"frontend_tea\",\n", "        \"l0_id\",\n", "        \"l1_id\",\n", "        \"l1\",\n", "        \"storage_type\",\n", "        \"storage_type_name\",\n", "        \"backend_vendor\",\n", "        \"frontend_vendor\",\n", "    ],\n", "    axis=1,\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["itm_active_be.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(itm_active_be, sheet_id, \"Items active in BE but not active in FE\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# itm_active_be.to_csv('Items active in BE but not active in FE.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_mapping = pd.concat(\n", "    [\n", "        itm_active_fe[\n", "            [\n", "                \"frontend_facility_id\",\n", "                \"frontend_facility_name\",\n", "                \"backend_facility_id\",\n", "                \"backend_facility_name\",\n", "            ]\n", "        ].drop_duplicates(),\n", "        itm_active_be[\n", "            [\n", "                \"frontend_facility_id\",\n", "                \"frontend_facility_name\",\n", "                \"backend_facility_id\",\n", "                \"backend_facility_name\",\n", "            ]\n", "        ].drop_duplicates(),\n", "    ]\n", ").drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(fin_mapping, sheet_id, \"Mapping\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "WITH PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "          (case when C1.NAME = C.name then C2.id else C1.id end) AS L1_id,\n", "          C.ID AS L0_id,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name as product_type\n", "   from lake_cms.gr_product P\n", "   INNER join lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER join lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER join lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER join lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "   \n", "   ),\n", "  \n", "item_details as (SELECT item_id,\n", "       cat.product as item_name,\n", "       cat.unit as UOM,\n", "       cat.L0_id,\n", "       cat.L0,\n", "       cat.L1_id,\n", "       cat.L1\n", "from  lake_rpc.item_product_mapping  rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL),\n", "\n", "bucket_info as\n", "(\n", "select \n", "item_id, \n", "facility_id,\n", "count(distinct case when tag_type_id = 8 then tag_value end) as tag_transfer  \n", "from\n", "( \n", "Select \n", "b.item_id, \n", "po.facility_id,\n", "tag_value,\n", "tag_type_id \n", "from  lake_rpc.item_outlet_tag_mapping b\n", "join lake_retail.console_outlet po ON b.outlet_id = po.id\n", "where b.active = 1\n", "group by 1,2,3,4\n", ")\n", "group by 1,2\n", "),\n", "\n", "bucket_tf as\n", "(\n", "select \n", "item_id,\n", "facility_id,\n", "case when tag_transfer >= 1 then 'transfer' else 'None' end as transfer_type\n", "from bucket_info\n", "),\n", "\n", "fe_be_mapping as (\n", "SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    ro.tax_location_id AS frontend_city_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    r.tax_location_id AS backend_city_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND ro.business_type_id <> 8),\n", "\n", "tax_check as (select a.item_id, cif.facility_id , a.igst, a.cgst, a.sgst\n", "from  lake_rpc.product_tax a\n", "left join (select tax_location_id as city_id, facility_id from lake_retail.console_outlet group by 1,2) cif on cif.city_id = a.location\n", "group by 1,2,3,4,5),\n", "\n", "margin_check as (select a.item_id,a.facility_id ,a.landing_price, a.on_invoice_margin_value \n", "from  lake_rpc.tot_margin a\n", "inner join (select item_id, facility_id , max(updated_at) as m_updated_at from lake_rpc.tot_margin group by 1,2)b \n", "on a.item_id = b.item_id and a.facility_id = b.facility_id and a.updated_at = b.m_updated_at\n", "group by 1,2,3,4),\n", "\n", "cpd_check as (select \n", "a.item_id ,\n", "b.facility_id,\n", "sum(cpd) as fc_qty\n", "from lake_snorlax.default_cpd  a\n", "left join lake_retail.view_console_outlet b on a.outlet_id = b.id \n", "group by 1,2),\n", "\n", "vendor_check as (select item_id, facility_id , vendor_id, vn.vendor_name\n", "from  lake_vms.vms_vendor_facility_alignment a\n", "left join  lake_vms.vms_vendor vn on vn.id = a. vendor_id \n", "where a.active = 1  \n", "group by 1,2,3,4),\n", "\n", "assortment_status as (\n", "select a.item_id, a.facility_id, a.city_id, a.active, a.master_assortment_substate_id, o.business_type_id, a.updated_at as activation_date\n", "from lake_rpc.product_facility_master_assortment a\n", "inner join \n", "(select item_id, facility_id, max(updated_at) as max_updated_at\n", "from lake_rpc.product_facility_master_assortment\n", "group by 1,2) b\n", "on a.item_id = b.item_id and a.facility_id = b.facility_id and a.updated_at = b.max_updated_at\n", "left join lake_retail.console_outlet o on a.facility_id=o.facility_id    \n", "where a.active=1\n", "and a.master_assortment_substate_id=1\n", "and o.business_type_id in (1,7)\n", "),\n", "\n", "be_assortment_status as (\n", "select a.item_id, a.facility_id, a.active, a.master_assortment_substate_id\n", "from lake_rpc.product_facility_master_assortment a\n", "inner join \n", "(select item_id, facility_id, max(updated_at) as max_updated_at\n", "from lake_rpc.product_facility_master_assortment\n", "group by 1,2) b\n", "on a.item_id = b.item_id and a.facility_id = b.facility_id and a.updated_at = b.max_updated_at\n", "where a.facility_id in (select distinct backend_facility_id from fe_be_mapping)\n", ")\n", "\n", "select \n", "distinct a.item_id, (case when itm.item_name is null then itm2.name else itm.item_name end) as item_name,\n", "--(case when itm.uom is null then itm2.variant_description else itm.uom end) as uom, \n", "itm.l0_id, itm.l0,\n", "-- itm.l1_id, itm.l1, \n", "--itm2.storage_type,\n", "--(case when itm2.storage_type = 1 then 'Regular'\n", "--when itm2.storage_type = 2 then 'Fridge'\n", "--when itm2.storage_type = 3 then 'Freezer'\n", "--when itm2.storage_type = 4 then 'Large'\n", "--when itm2.storage_type = 5 then 'Heavy'\n", "--when itm2.storage_type = 6 then 'Non Veg Cold'\n", "--when itm2.storage_type = 7 then 'Non Veg Frozen'\n", "--when itm2.storage_type = 8 then 'Fast moving'\n", "--end) as storage_type_name,\n", "b.frontend_facility_name, b.frontend_facility_id, \n", "--(case when a.city_id is null then b.frontend_city_id else a.city_id end) as frontend_city_id,\n", "b.backend_facility_name, b.backend_facility_id,\n", "business_type_id,\n", "(case when b.backend_facility_id is null then 'N' else 'Y' end) as tea_tag,\n", "a.activation_date as frontend_activation_date,\n", "(case when a.master_assortment_substate_id = 1 and a.active=1 then 'active' else 'inactive' end) as frontend_state,\n", "(case when bes.master_assortment_substate_id = 1 and bes.active = 1 then 'active' else 'inactive' end) as backend_state,\n", "(case\n", "when (tax.igst >= 0)  then 'Y'\n", "else  'N' end) backend_TAX,\n", "\n", "(case \n", "when (ven.vendor_id is null or ven.vendor_id = 0) then 'N'  \n", "else 'Y' end) as backend_vendor,\n", "\n", "(case\n", "when ((margin.landing_price > 0 or margin.on_invoice_margin_value > 0)) then 'Y'\n", "else  'N' end) backend_margin,\n", "\n", "(case when cpd.fc_qty>0 then 'Y' else 'N' end) as frontend_cpd,\n", "\n", "-- (case when tea.transfer_type = 'transfer' then 'Y' else 'N' end) as TEA,\n", "\n", "itm2.perishable as perishable_flag\n", "\n", "from assortment_status a\n", "left join fe_be_mapping b on a.item_id = b.item_id and a.facility_id = b.frontend_facility_id\n", "inner join lake_po.physical_facility_outlet_mapping pfom on b.backend_facility_id = pfom.facility_id and pfom.active = 1 and pfom.ars_active = 1\n", "inner join lake_po.physical_facility_outlet_mapping pfom1 on b.frontend_facility_id = pfom1.facility_id and pfom1.active = 1 and pfom1.ars_active = 1\n", "left join be_assortment_status bes on b.backend_facility_id = bes.facility_id and a.item_id = bes.item_id\n", "\n", "left join item_details itm on a.item_id = itm.item_id\n", "left join (select * from(\n", "    select *, rank() over (partition by item_id order by id desc) as rnk \n", "    from lake_rpc.product_product\n", ") where rnk =1) itm2 on a.item_id = itm2.item_id\n", "\n", "left join tax_check tax on a.item_id = tax.item_id and b.backend_facility_id = tax.facility_id\n", "left join margin_check margin on a.item_id = margin.item_id and b.backend_facility_id = margin.facility_id \n", "left join cpd_check cpd on a.item_id = cpd.item_id and b.frontend_facility_id = cpd.facility_id\n", "left join vendor_check ven on a.item_id = ven.item_id and b.backend_facility_id = ven.facility_id\n", "\n", "where b.frontend_facility_id in ({final_list})\n", "\n", "order by a.facility_id, a.item_id \n", "\n", "\n", "\"\"\".format(\n", "    final_list=\", \".join(str(y) for y in final_list)\n", ")\n", "\n", "all_active = pd.read_sql(sql, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_active = all_active[\n", "    (all_active.tea_tag == \"N\")\n", "    | (all_active.frontend_cpd == \"N\")\n", "    | (all_active.backend_vendor == \"N\")\n", "    | (all_active.backend_margin == \"N\")\n", "    | (all_active.backend_tax == \"N\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_active.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(all_active, sheet_id, \"All active at FE\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}