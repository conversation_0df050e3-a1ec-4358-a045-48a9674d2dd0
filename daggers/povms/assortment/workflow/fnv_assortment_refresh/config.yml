alert_configs:
  slack:
  - channel: bl-fnv-indent-check
dag_name: fnv_assortment_refresh
dag_type: workflow
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 12G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/assortment/workflow/fnv_assortment_refresh
paused: true
project_name: assortment
schedule:
  end_date: '2024-02-12T00:00:00'
  interval: 30 14 */15 * *
  start_date: '2023-03-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
