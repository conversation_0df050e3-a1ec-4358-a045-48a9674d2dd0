{"cells": [{"cell_type": "code", "execution_count": null, "id": "b8d821db-5edc-4ee0-9fac-5aaf75ae572e", "metadata": {}, "outputs": [], "source": ["import boto3\n", "import pencilbox as pb\n", "import os\n", "import csv\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# from pencilbox.io.sheets import gspread_client\n", "from operator import attrgetter\n", "from datetime import datetime\n", "from pytz import timezone\n", "import requests\n", "import pymysql\n", "\n", "# from pencilbox.connections import get_slack_client\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "964fc8fe-4876-4193-a462-be0585fa8ef6", "metadata": {}, "outputs": [], "source": ["rpc_secret = pb.get_secret(\"retail/noto-reports/mysql/rpc-rds-read\")\n", "host = rpc_secret.get(\"host\")\n", "username = rpc_secret.get(\"username\")\n", "password = rpc_secret.get(\"password\")\n", "con_rpc = pymysql.connect(host=host, user=username, password=password)"]}, {"cell_type": "code", "execution_count": null, "id": "26e37b19-f5f6-4e80-b9bc-2ffd3205334c", "metadata": {}, "outputs": [], "source": ["! pip install boto==2.44.0"]}, {"cell_type": "code", "execution_count": null, "id": "05507e91-3b50-460e-a634-0778d43b6a7e", "metadata": {}, "outputs": [], "source": ["from boto.s3.connection import S3Connection"]}, {"cell_type": "code", "execution_count": null, "id": "1fcb0df9-cebc-42ed-8766-1dfa0d0fe761", "metadata": {}, "outputs": [], "source": ["secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "BU_AWS_CONFIG = {\n", "    \"BUCKET_NAME\": \"retail-bulk-upload\",\n", "    \"AWS_KEY\": secrets.get(\"aws_key\"),\n", "    \"AWS_SECRET\": secrets.get(\"aws_secret\"),\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "c32d9050-7d1a-4bd5-9691-eb759054f7a5", "metadata": {}, "outputs": [], "source": ["def pull_city_sales_data_from_s3(aws_config, city_id):\n", "    bucket_name = aws_config[\"BUCKET_NAME\"]\n", "    aws_key = aws_config[\"AWS_KEY\"]\n", "    aws_secret = aws_config[\"AWS_SECRET\"]\n", "    session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "    s3 = session.resource(\"s3\")\n", "    bucket_obj = s3.Bucket(bucket_name)\n", "    obj_list = bucket_obj.objects.filter(\n", "        Prefix=\"assortment_recommendation/sales/{city_id}/\".format(city_id=city_id)\n", "    )\n", "    return bucket_obj, obj_list\n", "\n", "\n", "def get_latest_obj_from_list_and_download_to_local(bucket_obj, objects):\n", "    # sort the objects based on 'obj.last_modified'\n", "    sorted_objs = sorted(objects, key=attrgetter(\"last_modified\"))\n", "\n", "    # The latest version of the file (the last one in the list)\n", "    latest = sorted_objs.pop()\n", "    print(latest.key)\n", "    tmp_local_file = \"SVD_Assortment_sales_pulled.csv\"\n", "    bucket_obj.download_file(latest.key, tmp_local_file)\n", "    return tmp_local_file"]}, {"cell_type": "code", "execution_count": null, "id": "e8f0320c-7a0e-4f40-8beb-007aa9727baa", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "    payload = {\n", "        \"file\": file_path,\n", "        \"created_by\": \"<PERSON><PERSON>\",\n", "        \"user_id\": 14,\n", "        \"is_auto_po\": True,\n", "        \"upload_type_id\": upload_type,\n", "        \"content_type\": \"text/csv\",\n", "    }\n", "    response = requests.post(url, json=payload)\n", "    return response\n", "\n", "\n", "def make_file_and_upload(\n", "    facility_final_assortment_df, facility_id, facility_bu_job_map\n", "):\n", "    if len(facility_final_assortment_df) > 0:\n", "        bucket_obj, local_file_path, file_path = make_file(\n", "            facility_final_assortment_df, facility_id\n", "        )\n", "        bucket_obj.upload_file(local_file_path, file_path)\n", "\n", "        RECOMMENDATION_BULK_UPLOAD_TYPE_ID = 78\n", "        response = upload_to_bu(file_path, RECOMMENDATION_BULK_UPLOAD_TYPE_ID)\n", "\n", "        if response.status_code == 201:\n", "            status = \"Success\"\n", "            job_id = json.loads(response.text).get(\"id\")\n", "            facility_bu_job_map[facility_id] = job_id\n", "            print(\"Recommeded Assortment Requests Created for facility:\", facility_id)\n", "        else:\n", "            status = \"Failed\""]}, {"cell_type": "code", "execution_count": null, "id": "8427a988-91ff-4795-91e5-e3ab65dca53b", "metadata": {}, "outputs": [], "source": ["# # Noto RDS connection\n", "\n", "# noto_secret = pb.get_secret(\"retail/noto/db/noto.db.credential\")\n", "# noto_host = noto_secret.get('host')\n", "# noto_username = noto_secret.get('db_user')\n", "# noto_password = noto_secret.get('db_password')\n", "# noto_write_conn = pymysql.connect(host=noto_host, user=noto_username, password=noto_password, autocommit=True, local_infile=1)"]}, {"cell_type": "code", "execution_count": null, "id": "695ce5fd-17bd-4e21-911a-f42605db492b", "metadata": {}, "outputs": [], "source": ["def update_noto_request(facility_bu_job_map):\n", "    facility_bu_job_json = json.dumps(facility_bu_job_map)\n", "    sql_query = \"UPDATE noto.noto_request SET comments = '{}' WHERE id = {}\".format(\n", "        facility_bu_job_json, noto_request_id\n", "    )\n", "    noto_write_connection.ping(reconnect=True)\n", "    cursor = noto_write_conn.cursor()\n", "    cursor.execute(sql_query)"]}, {"cell_type": "code", "execution_count": null, "id": "b669ca83-5786-41f6-91f4-47e2da310497", "metadata": {}, "outputs": [], "source": ["# Parameterized Cell\n", "noto_request_id = None\n", "\n", "city_list = [\n", "    (7, 37, 10, 27, 9, 204, 142, 8, 19),\n", "    (1, -1),\n", "    (5, -5),\n", "    (4, -4),\n", "    (15, -15),\n", "    (3, -3),\n", "    (54, 66, 110, 125, 55, 167, 87, 230),\n", "    (14, 40),\n", "    (12, -12),\n", "    (2, -2),\n", "    (6, -6),\n", "]\n", "\n", "# city_list = [(5,-5), (4,-4)]\n", "\n", "facility_ids = []"]}, {"cell_type": "code", "execution_count": null, "id": "6cb70655-a8c9-4af8-bae7-e2548193dda2", "metadata": {}, "outputs": [], "source": ["def handle_fnv(df):\n", "\n", "    df[\"keep\"][df[\"sales_in_slife\"] > df[\"cut_off\"]] = 1\n", "\n", "    df[\"bc\"] = (\n", "        df[df[\"keep\"] == 1].groupby(\"facility_id\")[\"conversion\"].transform(\"median\")\n", "    )\n", "\n", "    df[\"keep\"][\n", "        (df[\"predicted\"] == False)\n", "        & (df[\"available_days_count\"] < 10)\n", "        & (df[\"available_days_count\"] > 0)\n", "    ] = 1\n", "\n", "    df[\"keep\"][\n", "        (df[\"sales_in_slife\"] <= df[\"cut_off\"])\n", "        & (df[\"conversion\"] > df[\"bc\"])\n", "        & (df[\"predicted\"] == False)\n", "    ] = 1\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "5666f024-293c-4e5b-9550-fcf12bc5e3cd", "metadata": {}, "outputs": [], "source": ["input_df = pd.DataFrame()\n", "for citi in city_list:\n", "    s3_bucket, objects = pull_city_sales_data_from_s3(BU_AWS_CONFIG, citi)\n", "    local_sales_csv_file = get_latest_obj_from_list_and_download_to_local(\n", "        s3_bucket, objects\n", "    )\n", "    df = pd.read_csv(local_sales_csv_file)\n", "    df = df[(df[\"category\"] == \"fnv\") | (df[\"l0\"] == \"Vegetables & Fruits\")]\n", "    df[\"keep\"] = 0\n", "    input_df = pd.concat([input_df, df])\n", "\n", "input_df = input_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "8dee24b1-8831-4893-9246-928a5c8e2e42", "metadata": {}, "outputs": [], "source": ["input_df"]}, {"cell_type": "code", "execution_count": null, "id": "6dfc80d3-bc37-4904-986a-b8afbf95dcad", "metadata": {}, "outputs": [], "source": ["def fetch_cutoff():\n", "    sql = \"\"\"\n", "    with fo as (select x.facility_id, outlet_id, pos_outlet_city_name as city, outlet_name from lake_po.physical_facility_outlet_mapping x\n", "        inner join dwh.dim_merchant_outlet_facility_mapping y on x.facility_id = y.facility_id and is_current=true\n", "        where active=1 and ars_active=1\n", "        and x.facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "        and outlet_id in (select distinct outlet_id from dwh.fact_sales_order_details where cart_checkout_ts_ist > current_date-30)\n", "        ),\n", "\n", "    cart as (select extract(week from cart_checkout_ts_ist) as dt, city, outlet_name, facility_id, count(distinct order_id) as n_carts from dwh.fact_sales_order_details x\n", "    inner join fo on fo.outlet_id = x.outlet_id\n", "    where order_current_status = 'DELIVERED' and (cart_checkout_ts_ist between current_date-100 and current_date)\n", "    group by 1,2,3,4\n", "    ),\n", "\n", "    carts as (\n", "    select dt, city, outlet_name, facility_id, n_carts, 1.00*n_carts/yest_n_carts-1 as ret from (\n", "    select dt, city, outlet_name,  facility_id, n_carts, lag(n_carts,1) over(partition by facility_id order by dt) as yest_n_carts from cart)\n", "    where yest_n_carts is not null\n", "    ),\n", "\n", "    av as (select city, outlet_name, facility_id, avg(n_carts) as n_carts, avg(ret) as ret from carts group by 1,2,3),\n", "\n", "    temp as(\n", "    select city, outlet_name, facility_id, n_carts, ret, concat(store, growth) as store_cat from (\n", "    select *, case when n_carts>(select avg(n_carts) from av) then 'high_carts&' else 'low_carts&' end as store, \n", "    case when ret>(select avg(ret) from av) then 'high_growth' else 'low_growth' end as growth from av\n", "    ))\n", "\n", "    select city, facility_id, outlet_name, store_cat,n_carts,ret, case when store_cat = 'high_carts&low_growth' then 2 else\n", "    case when store_cat in ('high_carts&high_growth', 'low_carts&low_growth') then 1.5 else 1 end end as cut_off from temp\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "df_cut_off = fetch_cutoff()\n", "df_cut_off = df_cut_off.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "776fba57-6487-48a3-b4b2-01ac4597be5f", "metadata": {"tags": []}, "outputs": [], "source": ["input_df = input_df.merge(df_cut_off, on=[\"facility_id\"], how=\"inner\")\n", "input_df[\"cut_off\"] = input_df[\"cut_off\"].fillna(0.99)"]}, {"cell_type": "code", "execution_count": null, "id": "f6e3e541-bc5a-4370-a32b-4a6c0f937288", "metadata": {}, "outputs": [], "source": ["input_df[(input_df[\"item_id\"] == 10005424) & (input_df[\"facility_id\"] == 1270)]"]}, {"cell_type": "code", "execution_count": null, "id": "1f064510-06e1-4ae6-8b12-5f823d6204b2", "metadata": {}, "outputs": [], "source": ["df_cut_off[df_cut_off[\"facility_id\"] == 1270]"]}, {"cell_type": "code", "execution_count": null, "id": "4ae469f1-2030-4338-b2d5-6d4c13b4e40d", "metadata": {}, "outputs": [], "source": ["input_df[(input_df[\"item_id\"] == 10002235) & (input_df[\"city_id\"] == 3)]"]}, {"cell_type": "code", "execution_count": null, "id": "90728613-da85-4ca6-bb10-ebc3c8703d21", "metadata": {}, "outputs": [], "source": ["input_df[\"keep\"] = 0\n", "x = input_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "1747618d-ba6f-4eba-8442-60441ba58f32", "metadata": {}, "outputs": [], "source": ["df_fnv = handle_fnv(x)"]}, {"cell_type": "code", "execution_count": null, "id": "063f1700-9c60-483f-aead-13ff162122c7", "metadata": {}, "outputs": [], "source": ["# def fetch_new_closed_stores():\n", "#     sql = \"\"\"\n", "#     with fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "#         and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "#     )\n", "\n", "#     select * from\n", "#     (select facility_id, date(max(updated_at)) as m, min(updated_at) as n from metrics.fnv_hourly_details group by 1)\n", "#     where (m != current_date or n > current_date-15) and facility_id in (select facility_id from fo)\n", "#     \"\"\"\n", "#     con = pb.get_connection(\"[Warehouse] Redshift\")\n", "#     return(pd.read_sql_query(sql=sql, con=con))\n", "\n", "# df_dont_touch_stores = fetch_new_closed_stores()['facility_id'].unique()\n", "# # df_dont_touch_stores = np.append(df_dont_touch_stores, 1028)\n", "# # df_dont_touch_stores = np.append(df_dont_touch_stores, 1181)"]}, {"cell_type": "code", "execution_count": null, "id": "62aec698-f328-4537-924a-3a83bfcd106c", "metadata": {}, "outputs": [], "source": ["def fetch_store_status():\n", "    sql = \"\"\"\n", "    with fo as (select facility_id, outlet_id, city_id, outlet_name from lake_po.physical_facility_outlet_mapping where active=1 and ars_active=1\n", "        and facility_id in (select facility_id from lake_retail.console_outlet where business_type_id = 7)\n", "    ),\n", "    temp as (select facility_id, date(max(cart_checkout_ts_ist)) m, date(min(cart_checkout_ts_ist)) n from dwh.fact_sales_order_details x\n", "    inner join fo on fo.outlet_id = x.outlet_id\n", "    where order_current_status = 'DELIVERED'\n", "    group by 1)\n", "\n", "    select facility_id, m, n, case when m != current_date then 'close_store' else case when n > current_date-15 then 'new_store' else 'normal_store' end end\n", "    as store_flag from temp\n", "    \"\"\"\n", "    con = pb.get_connection(\"[Warehouse] Redshift\")\n", "    return pd.read_sql_query(sql=sql, con=con)\n", "\n", "\n", "df_stores_stat = fetch_store_status()"]}, {"cell_type": "code", "execution_count": null, "id": "41810e00-5c69-478d-a105-0a1aebce2bbd", "metadata": {}, "outputs": [], "source": ["# df_stores_stat['store_flag'][df_stores_stat['facility_id'].isin([1862,1996])] = 'new_store'\n", "# df_stores_stat['store_flag'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ea58b298-7df8-4ae7-977c-6cbcfd6e910b", "metadata": {}, "outputs": [], "source": ["f_df = df_fnv[df_fnv[\"keep\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "id": "d66d1069-2dc5-4e4f-93a5-2f37485e103e", "metadata": {}, "outputs": [], "source": ["con_rpc.ping()\n", "city_ass_query = f\"\"\"\n", "SELECT item_id,\n", "       city_id,\n", "       master_assortment_substate_id\n", "FROM rpc.product_city_assortment_suggestion\n", "WHERE master_assortment_substate_id in (1,3)  and (reason_id != 7 or reason_id is null)\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "city_ass_df = pd.read_sql_query(city_ass_query, con_rpc)\n", "\n", "f_df = f_df.merge(city_ass_df, on=[\"item_id\", \"city_id\"])"]}, {"cell_type": "code", "execution_count": null, "id": "583fa86d-55ec-4ae9-b112-5f5abc8f8f2a", "metadata": {"tags": []}, "outputs": [], "source": ["f_df = f_df[[\"item_id\", \"facility_id\", \"master_assortment_substate_id\", \"cut_off\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "8c042cbb-e527-4456-bdf5-5f9de44a514a", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "home_directory = os.path.expanduser(\"~\")\n", "print(home_directory)"]}, {"cell_type": "code", "execution_count": null, "id": "27b38432-1f18-4769-be6d-83095ecedcb5", "metadata": {}, "outputs": [], "source": ["f_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f60ab4cf-313c-41a8-a63c-03b2e18d9266", "metadata": {}, "outputs": [], "source": ["# df_adhoc = pd.read_csv(f'/home/<USER>/airflow-dags/daggers/povms/danger/etl/fnv_adhoc/fnv_adhoc_combo.csv').astype(int)\n", "df_adhoc = pb.from_sheets(\n", "    \"1BNGjtuTfKqssnUU-CERZ4KyvDFeNoaaGRF8mVYg6Io4\", \"fnv_adhoc_combo\"\n", ")\n", "df_adhoc = df_adhoc.astype(int)\n", "df_adhoc = df_adhoc[df_adhoc[\"in_assortment\"].isin([1, 3])]\n", "df_adhoc[\"master_assortment_substate_id\"] = df_adhoc[\"in_assortment\"]\n", "df_adhoc[\"cut_off\"] = 0\n", "df_adhoc = df_adhoc[df_adhoc[\"item_id\"].isin(df_fnv[\"item_id\"].unique())]\n", "df_adhoc = df_adhoc[list(f_df)]"]}, {"cell_type": "code", "execution_count": null, "id": "b42a89fa-5537-4910-95b2-2d19ea8b198a", "metadata": {}, "outputs": [], "source": ["f_df = pd.concat([f_df, df_adhoc]).drop_duplicates(\n", "    subset=[\"item_id\", \"facility_id\"], keep=\"last\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3e308892-7c86-4475-b61e-784800cfc603", "metadata": {}, "outputs": [], "source": ["con_rpc.ping()\n", "current_ass_query = f\"\"\"\n", "SELECT item_id,\n", "       facility_id,\n", "       master_assortment_substate_id\n", "FROM rpc.product_facility_master_assortment\n", "where item_id in (select distinct item_id from rpc.item_category_details where l0_id = 1487)\n", "and master_assortment_substate_id in (1,3)\n", ";\n", "\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Redshift\")\n", "current_ass_df = pd.read_sql_query(current_ass_query, con_rpc)\n", "current_ass_df = current_ass_df[\n", "    current_ass_df[\"facility_id\"].isin(df_fnv[\"facility_id\"].unique())\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "178c49a3-5bb1-4d0a-9cdf-97e58d5945c4", "metadata": {"tags": []}, "outputs": [], "source": ["final_df = f_df.merge(current_ass_df, on=[\"item_id\", \"facility_id\"], how=\"outer\")\n", "final_df = final_df[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"master_assortment_substate_id_x\",\n", "        \"master_assortment_substate_id_y\",\n", "        \"cut_off\",\n", "    ]\n", "]\n", "final_df = final_df.query(\n", "    \"master_assortment_substate_id_x != master_assortment_substate_id_y\"\n", ")\n", "final_df = final_df[final_df[\"facility_id\"].isin(df_fnv[\"facility_id\"].unique())]\n", "\n", "new_store_listing = final_df[\n", "    (\n", "        final_df[\"facility_id\"].isin(\n", "            df_stores_stat[df_stores_stat[\"store_flag\"] == \"new_store\"][\n", "                \"facility_id\"\n", "            ].unique()\n", "        )\n", "    )\n", "    & (final_df[\"master_assortment_substate_id_y\"].isna())\n", "    & (final_df[\"master_assortment_substate_id_x\"] == 1)\n", "]\n", "final_df = final_df[\n", "    final_df[\"facility_id\"].isin(\n", "        df_stores_stat[df_stores_stat[\"store_flag\"] == \"normal_store\"][\n", "            \"facility_id\"\n", "        ].unique()\n", "    )\n", "]\n", "final_df = pd.concat([final_df, new_store_listing])"]}, {"cell_type": "code", "execution_count": null, "id": "c54cb71d-636f-447f-a61a-a93e2bbe56cc", "metadata": {}, "outputs": [], "source": ["final_df_facility = final_df.copy()"]}, {"cell_type": "code", "execution_count": null, "id": "e1723dcf-906c-4fe8-95fc-c38a81efee4b", "metadata": {}, "outputs": [], "source": ["final_df_facility[\"master_assortment_substate_id_x\"] = final_df_facility[\n", "    \"master_assortment_substate_id_x\"\n", "].<PERSON>na(2)\n", "final_df_facility[\"master_assortment_substate_id_x\"] = final_df_facility[\n", "    \"master_assortment_substate_id_x\"\n", "].astype(int)\n", "final_df_facility.rename(\n", "    columns={\n", "        \"master_assortment_substate_id_x\": \"Sub-state ID\",\n", "        \"item_id\": \"Item ID\",\n", "        \"facility_id\": \"Facility ID\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "final_df_facility[\"City ID\"] = \"\"\n", "final_df_facility[\"Reason ID\"] = 5\n", "final_df_facility[\"DS_FS_Flag\"] = 0\n", "\n", "final_df_facility = final_df_facility.rename(columns={\"facility_id\": \"Facility ID\"})\n", "final_df_facility = final_df_facility[\n", "    [\"Item ID\", \"City ID\", \"Facility ID\", \"Sub-state ID\", \"Reason ID\", \"DS_FS_Flag\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d75b9e13-4f8a-4f3e-9158-b64d3793b038", "metadata": {"tags": []}, "outputs": [], "source": ["x = final_df.copy()\n", "x[\"master_assortment_substate_id_y\"] = (\n", "    x[\"master_assortment_substate_id_y\"].fillna(2).astype(int)\n", ")\n", "x[\"master_assortment_substate_id_x\"] = (\n", "    x[\"master_assortment_substate_id_x\"].fillna(2).astype(int)\n", ")\n", "x = x.merge(\n", "    df_fnv[[\"facility_id\", \"city\", \"outlet_name\", \"store_cat\"]].drop_duplicates(),\n", "    on=[\"facility_id\"],\n", "    how=\"inner\",\n", ")\n", "x = x.merge(\n", "    df_fnv[\n", "        [\"item_id\", \"item_name\", \"l0\", \"l1\", \"l2\", \"product_type\"]\n", "    ].drop_duplicates(),\n", "    on=[\"item_id\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3b40c44a-df3d-497e-96a4-09809266200f", "metadata": {}, "outputs": [], "source": ["x[\"city\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "d339e397-887e-42b9-9707-4854c608c772", "metadata": {}, "outputs": [], "source": ["x[\n", "    (x[\"master_assortment_substate_id_x\"] != 2)\n", "    & (x[\"master_assortment_substate_id_y\"] == 2)\n", "][\"outlet_name\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "70d772b5-86e6-49fd-b781-da57ca93ff67", "metadata": {}, "outputs": [], "source": ["x[\n", "    (x[\"master_assortment_substate_id_x\"] == 2)\n", "    & (x[\"master_assortment_substate_id_y\"] != 2)\n", "][\"outlet_name\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "514354df-2443-441e-9600-4058301d15be", "metadata": {}, "outputs": [], "source": ["x[\n", "    (x[\"master_assortment_substate_id_x\"] == 2)\n", "    & (x[\"master_assortment_substate_id_y\"] != 2)\n", "][\"facility_id\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "8966279e-f380-41eb-9b49-a69413ba2843", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1PW17lYFeDOZ8TNQbZSTF58v490rA1F2AppTBLYpVp1s\"\n", "sheet_name = \"active_to_inactive_updated\"\n", "pb.to_sheets(\n", "    x[\n", "        (x[\"master_assortment_substate_id_x\"] != 1)\n", "        & (x[\"master_assortment_substate_id_y\"] == 1)\n", "    ][\n", "        [\n", "            \"city\",\n", "            \"outlet_name\",\n", "            \"store_cat\",\n", "            \"cut_off\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"product_type\",\n", "        ]\n", "    ],\n", "    sheet_id,\n", "    sheet_name,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8d66a4bf-a4bc-4d96-885f-268598bb72ac", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1PW17lYFeDOZ8TNQbZSTF58v490rA1F2AppTBLYpVp1s\"\n", "sheet_name = \"inactive_to_active_updated\"\n", "pb.to_sheets(\n", "    x[\n", "        (x[\"master_assortment_substate_id_x\"] == 1)\n", "        & (x[\"master_assortment_substate_id_y\"] != 1)\n", "    ][\n", "        [\n", "            \"city\",\n", "            \"outlet_name\",\n", "            \"store_cat\",\n", "            \"cut_off\",\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"item_name\",\n", "            \"l0\",\n", "            \"l1\",\n", "            \"l2\",\n", "            \"product_type\",\n", "        ]\n", "    ],\n", "    sheet_id,\n", "    sheet_name,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a817c8ed-d9e7-49cc-a7c1-404514f007bc", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1PW17lYFeDOZ8TNQbZSTF58v490rA1F2AppTBLYpVp1s\"\n", "sheet_name = \"store_cut_offs\"\n", "pb.to_sheets(df_cut_off, sheet_id, sheet_name)"]}, {"cell_type": "code", "execution_count": null, "id": "16288a10-1246-49c3-a276-ae572bb449f2", "metadata": {}, "outputs": [], "source": ["len(final_df_facility)"]}, {"cell_type": "code", "execution_count": null, "id": "a422a15d-2efc-4b80-b412-c5481ae8cf88", "metadata": {}, "outputs": [], "source": ["final_df_facility[\"Sub-state ID\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "decce934-c1a4-4bda-aebd-a04fde50553f", "metadata": {}, "outputs": [], "source": ["final_df_facility[\"Sub-state ID\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "94fb95e1-22aa-49a4-94b8-689481a57262", "metadata": {}, "outputs": [], "source": ["def upload_to_bu(file_path, upload_type):\n", "    url = \"https://retail-internal.grofer.io/bulk_upload/v1/upload_jobs\"\n", "    payload = {\n", "        \"file\": file_path,\n", "        \"created_by\": \"<PERSON><PERSON>\",\n", "        \"user_id\": 14,\n", "        \"is_auto_po\": True,\n", "        \"upload_type_id\": upload_type,\n", "        \"content_type\": \"text/csv\",\n", "    }\n", "    response = requests.post(url, json=payload)\n", "    return response\n", "\n", "\n", "bucket_name = \"retail-bulk-upload\"\n", "secrets = pb.get_secret(\"retail/jhub_keys/\")\n", "\n", "aws_key = secrets.get(\"aws_key\")\n", "aws_secret = secrets.get(\"aws_secret\")\n", "session = boto3.Session(aws_access_key_id=aws_key, aws_secret_access_key=aws_secret)\n", "s3 = session.resource(\"s3\")\n", "bucket_obj = s3.Bucket(bucket_name)\n", "\n", "format = \"%Y-%m-%d_%H-%M-%S\"\n", "now_utc = datetime.now(timezone(\"UTC\"))\n", "now_asia = now_utc.astimezone(timezone(\"Asia/Kolkata\"))\n", "\n", "csv_file_path = \"Master Assortment v3 Bot_{today}_fnv_pan_india.csv\".format(\n", "    today=now_asia.strftime(format)\n", ")\n", "local_file_path = \"{csv_file_path}\".format(csv_file_path=csv_file_path)\n", "processed_file = final_df_facility.to_csv(\n", "    \"{filepath}\".format(filepath=local_file_path), index=False, header=True\n", ")\n", "file_path = \"assortment/{csv_file_path}\".format(csv_file_path=local_file_path)\n", "\n", "master_assortment_v3_bulk_upload_type_id = 61\n", "\n", "if len(final_df_facility) > 0:\n", "    bucket_obj.upload_file(local_file_path, file_path)\n", "    response = upload_to_bu(file_path, master_assortment_v3_bulk_upload_type_id)\n", "    status = \"Success\" if response.status_code == 201 else \"Failed\"\n", "\n", "    subject = \"Master Assortment Adhoc Update Result {today} -\".format(\n", "        today=now_asia.strftime(format)\n", "    )\n", "    html_content = (\n", "        \"<p>Hi,</p><br/>\"\n", "        \"<p>PFA request file uploaded on workdesk for Adhoc assortment.</p><br/>\"\n", "        \"<p>Please check the file upload status <a href='https://retail.grofers.com/#/bulk-upload-status'> here:</a> Upload Type: master_assortment_v3\"\n", "        \"<p>File Upload Status: {status}</p>\".format(status=status)\n", "    )\n", "    \"<br/><p>Thanks & Regards</p>\" \"<br/><p>Retail Tech PO</p>\" \"<p>PO-VMS</p>\" \"<p>Grofers India Pvt. Ltd.</p>\"\n", "\n", "    files = [local_file_path]\n", "    os.remove(local_file_path)\n", "else:\n", "    print(\"abcd\")\n", "    # print(\"Assortment update not found for city_id: {}\".format(city_id))"]}, {"cell_type": "code", "execution_count": null, "id": "25e199db-bd22-4a3f-9db0-9b093a5c301d", "metadata": {}, "outputs": [], "source": ["response.json()"]}, {"cell_type": "code", "execution_count": null, "id": "20abc6cc-e8ea-4f23-82e0-a05e389ecf40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7089ab57-e64e-40e8-a74c-efd39b2156e5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6449432c-fe11-413b-a486-6688739203f0", "metadata": {}, "outputs": [], "source": ["slack_channel = pb.from_sheets(\n", "    \"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\"\n", ")\n", "slack_channel = (\n", "    slack_channel[slack_channel[\"alert\"] == \"fnv_assortment_refresh\"]\n", "    .reset_index()\n", "    .iloc[:, 2][0]\n", ")\n", "\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "0b0a35e2-9ac8-46a1-a578-275a1011ba0e", "metadata": {}, "outputs": [], "source": ["slack_channel = list(slack_channel.split(\",\"))\n", "slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "3adec831-a99a-43d8-b207-e8c40715a7bb", "metadata": {}, "outputs": [], "source": ["for i in range(len(slack_channel)):\n", "    channel = slack_channel[i]\n", "    if len(final_df_facility) > 0:\n", "        text_req = (\n", "            f\"<!channel> FnV Assortment Refreshed :leafy_green: :crossed_fingers:\"\n", "            + \"\\n\"\n", "            + f\"For details please refer this <https://docs.google.com/spreadsheets/d/1PW17lYFeDOZ8TNQbZSTF58v490rA1F2AppTBLYpVp1s| sheet>\"\n", "        )\n", "        pb.send_slack_message(channel=channel, text=text_req)\n", "    else:\n", "        print(\"No Indent Created\")"]}, {"cell_type": "code", "execution_count": null, "id": "1e6cb959-fbfd-4cd5-bb69-d7c020c3071e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9b38b149-e6bf-4c41-be9d-6f7dcfadba4b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}