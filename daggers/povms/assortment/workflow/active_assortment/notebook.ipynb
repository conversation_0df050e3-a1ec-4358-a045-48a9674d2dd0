{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date as dt\n", "from datetime import timedelta\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "redshift.schema = \"consumer\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "end_date = (datetime.now()).strftime(\"%Y-%m-%d\")\n", "\n", "\n", "# start_date='2019-01-01'\n", "# end_date='2019-07-10'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"--Item -Outlet Assortment\n", "\n", "WITH dates AS\n", "  (SELECT DISTINCT date(install_ts) AS order_date\n", "                   \n", "   FROM oms_suborder\n", "   WHERE date(install_ts) BETWEEN '{start_date}' AND '{end_date}'),\n", "     --Step 2a:Cross join with product_master_Assortment_log for getting latest substate change\n", "\n", "     substate AS\n", "  (SELECT item_id,\n", "          city_id ,\n", "          created_at,\n", "          order_date,\n", "          new_substate\n", "   FROM product_master_Assortment_log\n", "   JOIN dates ON 1=1),\n", "     --Step 2b:Getting latest substate change for every item\n", "\n", "     item_city_assortment_plan AS\n", "  (SELECT item_id,\n", "          city_id,\n", "          order_date,\n", "          max(created_at)AS recent_assortment\n", "   FROM substate\n", "   WHERE order_date-created_at>=0 \n", "   GROUP BY 1,\n", "            2,\n", "            3),\n", "\n", "     --Step 3:With latest change,getting item-outlet whose substate is 1 which is active\n", "\n", "     substate_id AS\n", "  (SELECT ica.*,date(recent_assortment) as assortment_date,\n", "          new_substate,\n", "          case when new_substate in (1,4) then 'active'\n", "when new_substate in (2) then 'inactive' else null end as active_flag\n", "   FROM item_city_assortment_plan ica\n", "   LEFT JOIN product_master_Assortment_log pma ON ica.item_id=pma.item_id\n", "   AND ica.city_id=pma.city_id\n", "   AND ica.recent_assortment=pma.created_at\n", " )\n", "--  select * from substate_id limit 100\n", "                          \n", "                         ,\n", "                         \n", "substate_assort as\n", "(\n", "select distinct\n", "item_id,\n", "city_id,\n", "order_date,\n", "date(created_at) as recent_assortment,\n", "date(recent_assortment) as assortment_date,\n", "master_assortment_substate_id as new_substate,\n", "case when master_assortment_substate_id in (1,4) then 'active'\n", "when master_assortment_substate_id in (2) then 'inactive' else null end as active_flag\n", "from consumer.product_master_assortment\n", "join dates on 1=1\n", "and order_date-created_at>=0 )\n", "\n", "-- select * from substate_assort limit 100\n", ",\n", "\n", "union_assortment as\n", "(\n", "select a.*\n", "from substate_id as a\n", "union \n", "select b.*\n", "from substate_assort as b\n", ")\n", ",\n", "\n", "\n", "assortment_level as \n", "(\n", "select a.*\n", "from union_assortment as a\n", "inner join\n", "(select item_id,city_id, min(recent_assortment) as min_assortment from union_assortment group by 1,2) as b\n", "on a.item_id=b.item_id\n", "and a.city_id=b.city_id\n", "and a.recent_assortment=b.min_assortment\n", "where active_flag is not null\n", ")\n", "\n", "-- select count(*),count(distinct item_id||city_id||order_date) from assortment_level---939,652\t939,652\n", ",\n", "city_facility as \n", "  (select tax_location_id as city_id,facility_id from pos_console_outlet\n", "    where upper(name) LIKE '%%SSC%%'\n", "        AND upper(name) NOT LIKE '%%AC%%'\n", "        AND upper(name) NOT LIKE '%%HOT%%'\n", "        and active='true'\n", "        group by 1,2)\n", "        ,\n", "        \n", "substate_facility as \n", "                  (select si.*,facility_id from assortment_level si\n", "                  inner join city_facility cf\n", "                  on si.city_id=cf.city_id\n", "                  )\n", "                  ,\n", "\n", "assortment_daily as \n", "                 ( select sf.*,\n", "                 case when date_of_activation is null then sf.assortment_date+21 else  date_of_activation end as date_of_activation \n", "                 from substate_facility sf\n", "                  left join consumer.rpc_assortment_activation aa\n", "                  on sf.city_id=aa.assortment_city_id\n", "                  and sf.facility_id=aa.facility_id\n", "                  and sf.item_id=aa.ASSORTMENT_ITEM_ID \n", "                  and sf.assortment_date=aa.assortment_date\n", "                  and sf.new_substate=aa.new_substate)\n", "                  \n", "                --   select count(*),count(distinct item_id||facility_id||order_date) from assortment_daily--1,442,092\t1,442,092\n", "                \n", "            select * from assortment_daily\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment = pandas.read_sql_query(sql=query, con=redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment[\"order_date\"] = pandas.to_datetime(assortment.order_date)\n", "assortment[\"date_of_activation\"] = pandas.to_datetime(assortment.date_of_activation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_final = assortment[\n", "    (assortment.new_substate.isin([1, 4]))\n", "    & (assortment.order_date >= assortment.date_of_activation)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assortment_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"integer\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\"},\n", "    {\"name\": \"order_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"recent_assortment\", \"type\": \"timestamp\"},\n", "    {\"name\": \"assortment_date\", \"type\": \"timestamp\"},\n", "    {\"name\": \"new_substate\", \"type\": \"smallint\"},\n", "    {\"name\": \"active_flag\", \"type\": \"varchar(500)\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\"},\n", "    {\"name\": \"date_of_activation\", \"type\": \"timestamp\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"consumer\",\n", "    \"table_name\": \"metrics_active_Assortment\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"order_date\"],\n", "    \"sortkey\": [\"order_date\", \"item_id\", \"facility_id\"],\n", "    \"incremental_key\": \"order_date\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(assortment_final, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}