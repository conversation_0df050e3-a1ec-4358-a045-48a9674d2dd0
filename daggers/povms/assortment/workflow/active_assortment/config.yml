dag_name: active_assortment
dag_type: workflow
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: chaitra
  slack_id: UAFM5NR0D
path: povms/assortment/workflow/active_assortment
paused: true
pool: povms_pool
project_name: assortment
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 1 * * *
  start_date: '2019-10-01T01:30:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
