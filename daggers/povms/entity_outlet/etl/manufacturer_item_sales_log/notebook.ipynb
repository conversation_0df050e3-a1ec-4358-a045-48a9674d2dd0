{"cells": [{"cell_type": "code", "execution_count": null, "id": "236e6641-fef2-4538-9cde-f884e48c6522", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "407e203e-f78c-4c7e-b8f7-de61a8168e57", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0378dfbb-5c32-4ded-a3ff-591cb80b85a5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "2349a2e6-ed69-4e99-97fb-fdcf177948e7", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\""]}, {"cell_type": "code", "execution_count": null, "id": "966cf05f-68ea-4f0b-afc3-b8cdf0ac7095", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "2871981d-b309-4cb3-8c7a-5ff1f47d0608", "metadata": {}, "outputs": [], "source": ["sales_summary_log = f\"\"\"         \n", "SELECT \n", "    cast(current_timestamp as timestamp) as updated_at_ist,\n", "    date(p.insert_ds_ist) as insert_ds_ist,\n", "    p.outlet_id,\n", "    r.name AS outlet_name,\n", "    l1.name AS city,\n", "    cti.legal_name,\n", "    rp.item_id,\n", "    pm.id AS manufacturer_id,\n", "    pm.name AS manufacturer_name,\n", "    SUM(pi.quantity) AS total_quantity,\n", "    SUM(CASE WHEN p.invoice_type_id = 1 THEN pi.selling_price * pi.quantity ELSE 0 END) AS total_sales_value,\n", "    SUM(CASE WHEN p.invoice_type_id = 2 THEN pi.selling_price * pi.quantity ELSE 0 END) AS total_return_value\n", "FROM \n", "    pos.pos_invoice p\n", "INNER JOIN \n", "    pos.pos_invoice_product_details pi ON pi.invoice_id = p.id\n", "INNER JOIN \n", "    retail.console_outlet r ON r.id = p.outlet_id AND r.business_type_id = 7\n", "INNER JOIN \n", "    retail.console_location l1 ON l1.id = r.tax_location_id\n", "INNER JOIN \n", "    rpc.product_product rp ON rp.variant_id = pi.variant_id\n", "LEFT JOIN \n", "    rpc.item_details id ON rp.item_id = id.item_id\n", "LEFT JOIN \n", "    rpc.product_manufacturer pm ON id.manufacturer_id = pm.id\n", "LEFT JOIN \n", "    vms.vms_vendor_city_mapping cm ON cm.vendor_id = p.source_entity_vendor_id AND cm.active = 1\n", "LEFT JOIN \n", "    vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = cm.id AND cti.active = 1\n", "LEFT JOIN \n", "    vms.vms_vendor v ON v.id = p.source_entity_vendor_id\n", "WHERE \n", "    p.insert_ds_ist > '2024-01-01'\n", "    AND pi.insert_ds_ist > '2024-01-01'\n", "    AND p.invoice_type_id IN (1, 2)\n", "    AND pi.store_offer_id IS NULL\n", "GROUP BY \n", "    p.insert_ds_ist,\n", "    p.outlet_id,\n", "    r.name,\n", "    l1.name,\n", "    cti.legal_name,\n", "    rp.item_id,\n", "    pm.id,\n", "    pm.name\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "dfcea298-d774-471a-8be4-18a1d3df15fa", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\n", "        \"name\": \"manufacturer_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"legal_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"total_quantity\", \"type\": \"double\", \"description\": \"run date in IST\"},\n", "    {\n", "        \"name\": \"total_sales_value\",\n", "        \"type\": \"double\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"total_return_value\", \"type\": \"double\", \"description\": \"run date in IST\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3a4b082c-00a3-4e69-a73e-f0841a9ad241", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"vendor_entity_item_manufacturer_sales_log\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"insert_ds_ist\",\n", "        \"item_id\",\n", "        \"city\",\n", "        \"manufacturer_id\",\n", "    ],\n", "    \"sortkey\": [\"outlet_id\", \"insert_ds_ist\", \"item_name\", \"city\", \"manufacturer_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"vendor_item_sales_summary_log\",\n", "}\n", "\n", "\n", "max_tries = 3\n", "for attempt in range(max_tries):\n", "    try:\n", "        start_time = time.time()\n", "        trino = pb.get_connection(\"[Warehouse] Trino\")\n", "        pb.to_trino(data_obj=sales_summary_log, **kwargs)\n", "        end_time = time.time()\n", "        duration = end_time - start_time\n", "        formatted_duration = format_time(duration)\n", "\n", "        print(f\"Time: {formatted_duration} and data pushed in table\")\n", "\n", "        break\n", "    except BaseException as e:\n", "        print(e)"]}, {"cell_type": "code", "execution_count": null, "id": "6807214c-b8a6-472c-8b88-eea1b92ff860", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "31f677b7-8243-4528-8af5-98e4f5805ebf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2073e826-9412-4787-a916-16115a12e92e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8c53b978-fe00-454f-a007-953037b68f12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5154a721-c70a-40c5-b350-d3f0f886c246", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2529d35b-b09b-4b51-912b-d03375d1bd72", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}