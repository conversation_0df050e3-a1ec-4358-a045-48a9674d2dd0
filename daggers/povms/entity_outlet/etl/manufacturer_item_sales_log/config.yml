alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: manufacturer_item_sales_log
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/entity_outlet/etl/manufacturer_item_sales_log
paused: false
pool: povms_pool
project_name: entity_outlet
schedule:
  end_date: '2025-09-17T00:00:00'
  interval: 0 */6 * * *
  start_date: '2024-07-19T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 1
