{"cells": [{"cell_type": "code", "execution_count": null, "id": "2275b575-1513-4915-8180-b152f9cc6398", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "39cd9d0b-7f4e-4084-bdb1-a82127c953af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cc320268-1aac-452a-bd80-4a4e4035b3be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "66bb1166-c92a-4e0b-b724-d1ff6621cdba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d56e807b-9a10-4458-a38a-8646dd9498a9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a7fca7f-69fb-424f-a197-231c8d5c1a14", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a3723912-07ad-4113-a7f7-7eb7c9dd37af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5abe8c2e-beba-4170-aeee-52736ae28776", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e538ab33-05c7-4096-90cf-969a7ea064eb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "5fb5cb30-8414-4a15-81f0-4bd8aacde1a8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\""]}, {"cell_type": "code", "execution_count": null, "id": "ee99be83-67d5-4950-b2d7-a4f8115af3d3", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e19f6516-b9d4-4f2a-acc9-0e42f10f72cd", "metadata": {}, "outputs": [], "source": ["sales_summary_log = f\"\"\"\n", "          \n", "WITH filtered_invoices AS (\n", "    SELECT id, outlet_id, outlet_name, source_entity_vendor_id, invoice_type_id, customer_city, insert_ds_ist\n", "    FROM pos.pos_invoice\n", "    WHERE \n", "        invoice_type_id IN (1, 2)\n", "        AND insert_ds_ist  > cast (current_date - interval '1' day as varchar)\n", "),\n", "filtered_invoice_product_details AS (\n", "    SELECT invoice_id, variant_id, cgst_value, sgst_value, igst_value, cess_value, additional_cess_value, selling_price, quantity\n", "    FROM lake_pos.pos_invoice_product_details\n", "    WHERE \n", "        store_offer_id IS NULL\n", "        AND insert_ds_ist  > cast (current_date - interval '1' day as varchar)\n", "),\n", "vendor_city_mapping AS (\n", "    SELECT vendor_id, id\n", "    FROM lake_vms.vms_vendor_city_mapping\n", "    WHERE active = 1\n", "),\n", "vendor_city_tax_info AS (\n", "    SELECT vendor_city_id, legal_name\n", "    FROM lake_vms.vms_vendor_city_tax_info\n", "    WHERE active = 1\n", "),\n", "vendor_info AS (\n", "    SELECT id, vendor_name\n", "    FROM lake_vms.vms_vendor\n", "),\n", "outlet_info AS (\n", "    SELECT id, tax_location_id\n", "    FROM lake_retail.console_outlet\n", "),\n", "location_info AS (\n", "    SELECT id\n", "    FROM lake_retail.console_location\n", "),\n", "product_info AS (\n", "    SELECT variant_id\n", "    FROM lake_rpc.product_product\n", ")\n", "\n", "SELECT \n", "    cast(current_timestamp as timestamp) as updated_at_ist,\n", "    p.outlet_id,\n", "    p.outlet_name,\n", "    v.vendor_name AS source_entity_vendor_name,\n", "    p.source_entity_vendor_id,\n", "    cti.legal_name,\n", "    date(TRY_CAST(p.insert_ds_ist AS date)) as insert_ds_ist,\n", "    SUM(\n", "        COALESCE(pi.cgst_value, 0) +\n", "        COALESCE(pi.sgst_value, 0) +\n", "        COALESCE(pi.igst_value, 0) +\n", "        COALESCE(pi.cess_value, 0) +\n", "        COALESCE(pi.additional_cess_value, 0)\n", "    ) AS total_tax,\n", "    SUM(\n", "        CASE\n", "            WHEN p.invoice_type_id = 1 THEN pi.selling_price * pi.quantity\n", "            ELSE 0\n", "        END\n", "    ) AS total_gross_bill_amount,\n", "    SUM(\n", "        CASE\n", "            WHEN p.invoice_type_id = 2 THEN pi.selling_price * pi.quantity\n", "            ELSE 0\n", "        END\n", "    ) AS total_return_amount\n", "FROM filtered_invoices p\n", "INNER JOIN filtered_invoice_product_details pi ON pi.invoice_id = p.id\n", "LEFT JOIN vendor_city_mapping cm ON cm.vendor_id = p.source_entity_vendor_id\n", "LEFT JOIN vendor_city_tax_info cti ON cti.vendor_city_id = cm.id\n", "LEFT JOIN vendor_info v ON v.id = p.source_entity_vendor_id\n", "INNER JOIN outlet_info r ON r.id = p.outlet_id\n", "INNER JOIN location_info l1 ON l1.id = r.tax_location_id\n", "INNER JOIN location_info l2 ON l2.id = p.customer_city\n", "INNER JOIN product_info rp ON rp.variant_id = pi.variant_id\n", "GROUP BY p.outlet_id, p.outlet_name, v.vendor_name, p.source_entity_vendor_id, cti.legal_name, p.insert_ds_ist\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6c824815-abae-4d81-bc74-fd4e060a8f7d", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\n", "        \"name\": \"source_entity_vendor_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\n", "        \"name\": \"source_entity_vendor_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"legal_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"total_tax\", \"type\": \"double\", \"description\": \"run date in IST\"},\n", "    {\n", "        \"name\": \"total_gross_bill_amount\",\n", "        \"type\": \"double\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"total_return_amount\", \"type\": \"double\", \"description\": \"run date in IST\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "d971d7a4-6093-4dd4-995f-afccaaf66fb3", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"vendor_entity_sales_summary_log\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"source_entity_vendor_id\",\n", "        \"insert_ds_ist\",\n", "        \"outlet_name\",\n", "    ],\n", "    \"sortkey\": [\"outlet_id\", \"source_entity_vendor_id\", \"insert_ds_ist\", \"outlet_name\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"vendor_sales_summary_log\",\n", "}\n", "\n", "\n", "max_tries = 3\n", "for attempt in range(max_tries):\n", "    try:\n", "        start_time = time.time()\n", "        trino = pb.get_connection(\"[Warehouse] Trino\")\n", "        pb.to_trino(data_obj=sales_summary_log, **kwargs)\n", "        end_time = time.time()\n", "        duration = end_time - start_time\n", "        formatted_duration = format_time(duration)\n", "\n", "        print(f\"Time: {formatted_duration} and data pushed in table\")\n", "\n", "        break\n", "    except BaseException as e:\n", "        print(e)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}