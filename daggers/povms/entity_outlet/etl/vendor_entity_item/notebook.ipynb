{"cells": [{"cell_type": "code", "execution_count": null, "id": "9ff6455b-971d-4d4e-9414-4df23186aa27", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "68b777a3-0d66-40e3-b880-d7fcfa410868", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95a4b44b-bd52-4519-b9ee-5ee5ecd29df5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "657437c9-db18-407b-aa7d-134996e04e3a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import datetime as dt\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "!pip install matplotlib==3.8\n", "import matplotlib.pyplot as plt\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "\n", "pd.set_option(\"display.float_format\", lambda x: \"%.3f\" % x)\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "59886e44-24ac-4194-8a05-e1de9b54b3e0", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\""]}, {"cell_type": "code", "execution_count": null, "id": "94c74c6b-f936-4e1b-a9bd-b2bde66f0e9b", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 1\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "771569bc-0764-4dbc-ac1e-c06c2165246d", "metadata": {}, "outputs": [], "source": ["vendor_entity = f\"\"\"\n", "       SELECT \n", "       distinct v.outlet_id,\n", "       cast(current_timestamp as timestamp) as updated_at_ist,\n", "       o.name AS outlet_name,\n", "       v.entity_vendor_id,\n", "       vm.vendor_name AS entity_vendor_name,\n", "       v.item_id,\n", "       p.name as item_name,\n", "       case \n", "       when a.active = 0 THEN 'No tea tag'\n", "       when a.active = 1 then 'STO transfer'\n", "       else 'PO'\n", "       end as sourcing_type,\n", "       p.handling_type,\n", "       i.manufacturer_id,\n", "       m.name as manufacturer_name,\n", "       vm2.vendor_name AS PO_vendor_name,\n", "       v.active,\n", "       v.created_by,\n", "       v.updated_at,\n", "       v.updated_by,\n", "       CASE\n", "           WHEN vm.type_id = 2 THEN 'Outlet_Vendor'\n", "           WHEN vm.type_id = 5 THEN 'Distributor'\n", "           Else  'HOT'\n", "       END AS vendor_type,\n", "       CASE\n", "           WHEN d.outlet_id IS NOT NULL THEN 1\n", "           ELSE 0\n", "       END AS is_default_vendor,\n", "       log.reason\n", "FROM rpc.outlet_entity_vendor_item v\n", "left JOIN vms.vms_vendor vm ON v.entity_vendor_id=vm.id\n", "inner JOIN retail.console_outlet o ON v.outlet_id=o.id\n", "left join rpc.product_product p on v.item_id=p.item_id\n", "left join rpc.item_details i on v.item_id=i.item_id\n", "left join lake_rpc.item_outlet_tag_mapping a on v.item_id=a.item_id\n", "         and a.tag_type_id=8\n", "         and a.outlet_id = o.id\n", "left join rpc.product_manufacturer m on i.manufacturer_id=m.id\n", "left join vms.vms_vendor_facility_alignment vm on o.facility_id=vm.facility_id and v.item_id=vm.item_id\n", "left join vms.vms_vendor vm2 ON vm.vendor_id=vm2.id\n", "left join retail.console_outlet_default_entity_vendor d ON v.outlet_id = d.outlet_id\n", "     AND v.entity_vendor_id = d.default_entity_vendor_id\n", "left join rpc.outlet_entity_vendor_item_log log ON v.outlet_id = log.outlet_id\n", "     AND v.item_id = log.item_id\n", "     AND v.entity_vendor_id = log.entity_vendor_id\n", "     AND log.insert_ds_ist > '2023-01-01'\n", "WHERE vm.type_id in (5,2) \n", "and v.active=1\n", "and o.business_type_id in (1,12,7,19,20)\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c30d303c-f93d-463e-9a32-10b0e5fd933e", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"entity_vendor_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"entity_vendor_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"handling_type\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"sourcing_type\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"manufacturer_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"manufacturer_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"active\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"created_by\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"updated_by\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"vendor_type\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"is_default_vendor\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"reason\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "05542c2c-f9b3-47a0-bc88-9152b13507ad", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"vendor_entity_item_details\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"entity_vendor_id\",\n", "        \"item_id\",\n", "        \"manufacturer_id\",\n", "    ],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"outlet_entity_mapping\",\n", "}\n", "\n", "\n", "max_tries = 3\n", "for attempt in range(max_tries):\n", "    try:\n", "        start_time = time.time()\n", "        trino = pb.get_connection(\"[Warehouse] Trino\")\n", "        pb.to_trino(data_obj=vendor_entity, **kwargs)\n", "        end_time = time.time()\n", "        duration = end_time - start_time\n", "        formatted_duration = format_time(duration)\n", "\n", "        print(f\"Time: {formatted_duration} and data pushed in table\")\n", "\n", "        break\n", "    except BaseException as e:\n", "        print(e)"]}, {"cell_type": "code", "execution_count": null, "id": "1a5de419-971c-4fda-b3cc-5a5ff15f59b9", "metadata": {}, "outputs": [], "source": ["sales_table = f\"\"\"\n", "   SELECT  \n", "      cast(current_timestamp as timestamp) as updated_at_ist,\n", "       a.outlet_id,\n", "       a.facility_id,\n", "       a.outlet_name,\n", "       a.city,\n", "       a.entity_vendor_id,\n", "       a.entity_name,\n", "       a.gst_tin,\n", "       a.legal_name,\n", "       a.item_id,\n", "       a.item_name,\n", "       a.cms_food_type,\n", "       a.handling_type,\n", "       a.variant_id,\n", "       a.upc,\n", "       a.variant_mrp,\n", "       a.landing_price,\n", "       a.item_quantity AS quantity\n", "FROM\n", "  ( \n", "   SELECT e.outlet_id,\n", "  con.facility_id,\n", "  cl.name as city,\n", "          e.entity_vendor_id,\n", "          cti.legal_name,\n", "          v1.vendor_name AS entity_name,\n", "          cti.gst_tin,\n", "          con.name AS outlet_name,\n", "          pp.item_id,\n", "          pp.upc,\n", "          id.name AS item_name,\n", "          pp.cms_food_type,\n", "          pp.handling_type,\n", "          id.manufacturer_id,\n", "          e.variant_id,\n", "          pp.variant_mrp,\n", "          lp.landing_price,\n", "          sum(e.quantity) AS item_quantity\n", "   FROM ims.ims_entity_vendor_inventory e\n", "   LEFT JOIN retail.console_outlet con ON con.id = e.outlet_id\n", "   AND con.active = 1\n", "   and con.device_id <>47\n", "   left join lake_retail.console_location cl on con.tax_location_id=cl.id\n", "   LEFT JOIN rpc.product_product pp ON pp.variant_id = e.variant_id\n", "   AND pp.active = 1\n", "   LEFT JOIN rpc.item_details id ON id.item_id = pp.item_id\n", "   LEFT JOIN vms.vms_vendor v1 ON v1.id = e.entity_vendor_id\n", "   LEFT JOIN retail.outlet_entity_vendor_mapping v ON v.outlet_id = e.id\n", "   LEFT JOIN vms.vms_vendor_city_mapping vcm ON vcm.vendor_id = e.entity_vendor_id\n", "   AND vcm.active = 1\n", "   LEFT JOIN vms.vms_vendor_city_tax_info cti ON cti.vendor_city_id = vcm.id\n", "   AND cti.active = 1\n", "   left join lake_ims.ims_inventory_landing_price lp on e.variant_id=lp.variant_id and e.outlet_id=lp.outlet_id\n", "   WHERE v1.type_id in (2,5)\n", "and con.business_type_id in (1,12,7,19,20) \n", "and con.device_id<>47\n", "GROUP BY 1, 2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17) AS a\n", "WHERE a.item_quantity>0\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "353c5226-2f06-4534-bb53-9c0d9b7a6d3f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"run date in IST\",\n", "    },\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"outlet_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"city\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"entity_vendor_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"entity_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"gst_tin\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"legal_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"cms_food_type\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"handling_type\", \"type\": \"integer\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"variant_id\", \"type\": \"varchar\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"upc\", \"type\": \"int\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"variant_mrp\", \"type\": \"double\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"landing_price\", \"type\": \"double\", \"description\": \"run date in IST\"},\n", "    {\"name\": \"quantity\", \"type\": \"int\", \"description\": \"run date in IST\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "27344211-7a0a-4019-957c-473ee34dfa20", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"vendor_entity_item_inventory\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"outlet_id\",\n", "        \"facility_id\",\n", "        \"item_id\",\n", "        \"entity_vendor_id\",\n", "        \"variant_id\",\n", "    ],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"outlet_entity_inventory\",\n", "}\n", "\n", "\n", "max_tries = 3\n", "for attempt in range(max_tries):\n", "    try:\n", "        start_time = time.time()\n", "        trino = pb.get_connection(\"[Warehouse] Trino\")\n", "        pb.to_trino(data_obj=sales_table, **kwargs)\n", "        end_time = time.time()\n", "        duration = end_time - start_time\n", "        formatted_duration = format_time(duration)\n", "\n", "        print(f\"Time: {formatted_duration} and data pushed in table\")\n", "\n", "        break\n", "    except BaseException as e:\n", "        print(e)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}