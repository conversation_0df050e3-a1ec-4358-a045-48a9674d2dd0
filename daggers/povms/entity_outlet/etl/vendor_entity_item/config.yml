alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: vendor_entity_item
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/entity_outlet/etl/vendor_entity_item
paused: false
pool: povms_pool
project_name: entity_outlet
schedule:
  end_date: '2025-08-24T00:00:00'
  interval: '30 1 * * * '
  start_date: '2024-06-27T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
