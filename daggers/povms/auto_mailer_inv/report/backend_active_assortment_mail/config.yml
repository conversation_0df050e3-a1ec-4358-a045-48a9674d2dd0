alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: backend_active_assortment_mail
dag_type: report
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 2
owner:
  email: <EMAIL>
  slack_id: U03RR39TSTZ
path: povms/auto_mailer_inv/report/backend_active_assortment_mail
paused: false
project_name: auto_mailer_inv
schedule:
  end_date: '2024-03-20T00:00:00'
  interval: 00 6 * * MON
  start_date: '2024-03-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
pool: povms_pool
