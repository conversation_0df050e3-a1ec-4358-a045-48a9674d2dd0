{"cells": [{"cell_type": "code", "execution_count": null, "id": "67e0026a-33ce-4dcb-9e22-83f4eb840ff3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "a17aca1f-b195-4296-a236-b4350d805c16", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            if not df.empty:\n", "                return df\n", "            else:\n", "                print(\"DataFrame is empty, retrying...\")\n", "        except Exception as e:\n", "            print(e)\n", "\n", "        time.sleep(5)\n", "\n", "    print(\"Max retries reached, returning an empty DataFrame\")\n", "    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "7bbb2cf5-9097-48d6-9bf1-47b1f7d7ff8a", "metadata": {}, "outputs": [], "source": ["start_date = pd.to_datetime(datetime.today() + timedelta(hours=5.5)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "# start_date = start_date[0]\n", "start_date"]}, {"cell_type": "code", "execution_count": null, "id": "e655b292-2372-4480-95d8-80cf4cd85039", "metadata": {}, "outputs": [], "source": ["# mail_input = pd.read_csv('mail_input.csv')\n", "\n", "mail_input = pb.from_sheets(\n", "    \"18CKhT6nDQNsZ1AHCv45xVGFg9mgvaskFfs2f090qwNc\",\n", "    \"mail_input\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "594c8657-29cb-401d-b3ab-f9664d8b1075", "metadata": {}, "outputs": [], "source": ["mail_input = mail_input[\n", "    ~(\n", "        (mail_input[\"rank\"] == \"\")\n", "        | (mail_input[\"manufacturer_id\"] == \"\")\n", "        | (mail_input[\"new_manufacturer_name\"] == \"\")\n", "        | (mail_input[\"outlet_id\"] == \"\")\n", "        | (mail_input[\"outlet_id\"] == \"\")\n", "        | (mail_input[\"internal_mail_id\"] == \"\")\n", "        | (mail_input[\"external_mail_id\"] == \"\")\n", "        | (mail_input[\"enable_flag\"] == \"\")\n", "    )\n", "]\n", "\n", "mail_input[[\"rank\", \"enable_flag\"]] = (\n", "    mail_input[[\"rank\", \"enable_flag\"]].fillna(0).astype(int)\n", ")\n", "\n", "mail_input = mail_input[mail_input[\"enable_flag\"] > 0]\n", "\n", "mail_input = mail_input[\n", "    [\n", "        \"rank\",\n", "        \"manufacturer_id\",\n", "        \"new_manufacturer_name\",\n", "        \"outlet_id\",\n", "        \"internal_mail_id\",\n", "        \"external_mail_id\",\n", "        \"enable_flag\",\n", "    ]\n", "]\n", "\n", "\n", "mail_input.dropna(inplace=True)\n", "\n", "mail_input.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21d6031c-69fc-4981-9a65-c67fdfe24b24", "metadata": {}, "outputs": [], "source": ["manufacturer_sr_details = mail_input[[\"rank\", \"manufacturer_id\"]].copy()\n", "\n", "manufacturer_sr_details = (\n", "    manufacturer_sr_details.set_index([\"rank\"])\n", "    .apply(lambda col: col.str.split(\",\"))\n", "    .explode([\"manufacturer_id\"])\n", "    .reset_index()\n", "    .reindex(manufacturer_sr_details.columns, axis=1)\n", ")\n", "\n", "manufacturer_sr_details = manufacturer_sr_details.drop_duplicates()\n", "\n", "manufacturer_sr_details[[\"rank\", \"manufacturer_id\"]] = manufacturer_sr_details[\n", "    [\"rank\", \"manufacturer_id\"]\n", "].astype(int)\n", "\n", "manufacturer_sr_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b7309483-3ff1-4caf-afb9-93310f0b1bf2", "metadata": {}, "outputs": [], "source": ["# outlet_sr_details = mail_input[['rank','outlet_id']].copy()\n", "\n", "# outlet_sr_details = (outlet_sr_details.set_index(['rank'])\n", "#        .apply(lambda col: col.str.split(','))\n", "#        .explode(['outlet_id'])\n", "#        .reset_index()\n", "#        .reindex(outlet_sr_details.columns, axis=1))\n", "\n", "# outlet_sr_details = outlet_sr_details.drop_duplicates()\n", "\n", "# outlet_sr_details[['rank','outlet_id']] = outlet_sr_details[['rank','outlet_id']].astype(int)\n", "\n", "# outlet_sr_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "186e7e2d-5111-49e7-9b12-f1ca31bcc366", "metadata": {}, "outputs": [], "source": ["# manufacturer_outlet_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     outlet_sr_details,\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "# adding_new_manufacturer_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     mail_input['rank'],\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "adding_new_manufacturer_details = manufacturer_sr_details.copy().drop_duplicates()\n", "\n", "adding_new_manufacturer_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d9b7a8ce-04b2-42aa-b03c-1b1192dd8c56", "metadata": {}, "outputs": [], "source": ["# outlet_id_list = tuple(list(adding_new_manufacturer_details['outlet_id'].unique()))\n", "# if len(outlet_id_list) < 2:\n", "#     outlet_id_list = list(outlet_id_list)\n", "#     outlet_id_list.append(-1)\n", "#     outlet_id_list = tuple(outlet_id_list)\n", "# len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "397badce-0bf7-4783-95f1-5ec777af6b18", "metadata": {}, "outputs": [], "source": ["manufacturer_id_list = tuple(\n", "    list(adding_new_manufacturer_details[\"manufacturer_id\"].unique())\n", ")\n", "if len(manufacturer_id_list) < 2:\n", "    manufacturer_id_list = list(manufacturer_id_list)\n", "    manufacturer_id_list.append(-1)\n", "    manufacturer_id_list = tuple(manufacturer_id_list)\n", "len(manufacturer_id_list)"]}, {"cell_type": "markdown", "id": "12570da3-adac-47a5-8397-1dea67d5bb28", "metadata": {}, "source": ["## Assortent Query"]}, {"cell_type": "code", "execution_count": null, "id": "6af3635e-d2d0-4715-acf4-906bf3821d30", "metadata": {}, "outputs": [], "source": ["def active_assortment():\n", "    active_assortment = f\"\"\"\n", "   with\n", "run_id_details as\n", "    (select facility_id, run_id, date(completed_at + interval '330' minute) as run_date,\n", "        row_number() over(partition by facility_id, date(completed_at + interval '330' minute) order by (started_at + interval '330' minute)) as rnk\n", "            from ars.job_run\n", "                where started_at >= cast(current_date - interval '1' day as date)\n", "                    and json_extract_scalar(simulation_params, '$.run') = 'multi_business'\n", "                    -- and json_extract_path_text(simulation_params, 'type',true) = 'any_day_po'\n", "                    and success = 1\n", "    ),\n", "    item_assortment_type as\n", "        (select be_facility_id,item_id,case when assortment_type>0 then 10 else 30 end assortment_type\n", "        from\n", "        (select co.facility_id be_facility_id,ma.item_id,sum(assortment_type) assortment_type\n", "        from\n", "        (select item_id,facility_id fe_facility_id,case when substate_reason_id not in (7) then 1 else 0 end assortment_type\n", "        from lake_rpc.product_facility_master_assortment\n", "        where active = 1 and  master_assortment_substate_id =1) ma\n", "        join supply_etls.outlet_details rco on rco.facility_id = ma.fe_facility_id and rco.business_type_id in (7)\n", "                and rco.active=1 and rco.ars_active=1\n", "        join lake_rpc.item_outlet_tag_mapping itm on itm.item_id = ma.item_id and itm.outlet_id  = rco.hot_outlet_id\n", "        join supply_etls.outlet_details co on co.hot_outlet_id = cast(itm.tag_value as int) and co.business_type_id in (1,12,20) \n", "                    and co.active=1 and co.ars_active=1 and itm.tag_type_id in (8)\n", "        where  itm.active = 1\n", "        group by 1,2)),\n", "    final_indent as\n", "    (select fi.outlet_id,\n", "        fi.physical_facility_id as facility_id, fi.facility_name, fi.item_id,rid.run_date,max(fi.run_id) as run_id\n", "            from ars.final_indent fi\n", "            join item_assortment_type it on it.item_id = fi.item_id and it.be_facility_id = fi.physical_facility_id \n", "            join run_id_details rid on rid.run_id =  fi.run_id\n", "                where fi.run_id in (select distinct run_id from run_id_details where rnk = 1)\n", "                and run_date = current_date and assortment_type in (10)\n", "                    group by 1,2,3,4,5\n", "    )\n", "    \n", "    select fi.facility_name,id.manufacturer_id,id.manufacturer_name,id.item_id,id.item_name,ipr.product_id p_id,(pp.weight_in_gm*1.00) uom,pp.variant_mrp mrp,pp.upc\n", "    from final_indent fi\n", "    join rpc.product_product pp on pp.item_id = fi.item_id and pp.id in (select max(id) from rpc.product_product group by item_id)\n", "    join supply_etls.item_details id on id.item_id = fi.item_id\n", "    join lake_rpc.item_product_mapping ipr on ipr.item_id = fi.item_id\n", "    where id.assortment_type in ('Packaged Goods') and id.handling_type in ('Non Packaging Material') \n", "    order by fi.facility_name\n", "    \n", "    \"\"\"\n", "    return read_sql_query(active_assortment, trino)\n", "\n", "\n", "active_assortment = active_assortment()"]}, {"cell_type": "code", "execution_count": null, "id": "3f55fc45-3d1b-4638-96db-0c2a5f2d1993", "metadata": {}, "outputs": [], "source": ["active_assortment"]}, {"cell_type": "code", "execution_count": null, "id": "63e5730e-6a76-4390-a20a-dbb63dca9da5", "metadata": {}, "outputs": [], "source": ["active_assortments = pd.merge(\n", "    active_assortment,\n", "    adding_new_manufacturer_details,\n", "    on=[\"manufacturer_id\"],\n", "    how=\"inner\",\n", ")\n", "\n", "active_assortments.head()"]}, {"cell_type": "code", "execution_count": null, "id": "04f1e5d8-5d79-4a8d-bcc0-896bfcf1ead9", "metadata": {}, "outputs": [], "source": ["active_assortments = active_assortments[\n", "    [\n", "        \"facility_name\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"p_id\",\n", "        \"uom\",\n", "        \"mrp\",\n", "        \"upc\",\n", "        \"rank\",\n", "    ]\n", "].drop_duplicates()\n", "active_assortments"]}, {"cell_type": "code", "execution_count": null, "id": "5af4a81d-a568-4181-ab6c-3bffca8b0aee", "metadata": {}, "outputs": [], "source": ["final_manufacturer_list = tuple(list(active_assortments[\"rank\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "d28870ea-d381-43ba-b663-140d98fc8662", "metadata": {}, "outputs": [], "source": ["for sr_no in final_manufacturer_list:\n", "    required_df_render = active_assortments[active_assortments[\"rank\"] == sr_no]\n", "    manufacturer_name = required_df_render[[\"manufacturer_name\"]].drop_duplicates()\n", "    final_manufacturer_name = list(manufacturer_name[\"manufacturer_name\"].unique())\n", "\n", "    required_df_attach = active_assortments[active_assortments[\"rank\"] == sr_no].drop(\n", "        columns={\"rank\"}\n", "    )\n", "    required_df_attach.to_csv(\n", "        f\"{final_manufacturer_name}_Active_Assortment_{start_date}.csv\", index=False\n", "    )\n", "    from_email = \"<EMAIL>\"\n", "    email_id_sheet = mail_input\n", "\n", "    email_id_internal = email_id_sheet[\n", "        email_id_sheet[\"rank\"].astype(int) == sr_no\n", "    ].reset_index()[\"internal_mail_id\"][0]\n", "    final_internal = list(email_id_internal.split(\",\"))\n", "\n", "    email_id_external = email_id_sheet[\n", "        email_id_sheet[\"rank\"].astype(int) == sr_no\n", "    ].reset_index()[\"external_mail_id\"][0]\n", "    final_external = list(email_id_external.split(\",\"))\n", "\n", "    to_email = final_external + final_internal\n", "    subject = f\"Active Assortment for {final_manufacturer_name}_{start_date}\"\n", "    html_content = f\"Hi Team, <br><br> Please find the attached sheet for  Active Assortment Sku in our Backend Facilities.<br><br>Please let us know if any further help required from our side.\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[f\"{final_manufacturer_name}_Active_Assortment_{start_date}.csv\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "ea572f74-cb6e-4c38-bc8b-8a0125e4c854", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}