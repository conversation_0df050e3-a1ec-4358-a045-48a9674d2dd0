{"cells": [{"cell_type": "code", "execution_count": null, "id": "67e0026a-33ce-4dcb-9e22-83f4eb840ff3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "a17aca1f-b195-4296-a236-b4350d805c16", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            if not df.empty:\n", "                return df\n", "            else:\n", "                print(\"DataFrame is empty, retrying...\")\n", "        except Exception as e:\n", "            print(e)\n", "\n", "        time.sleep(5)\n", "\n", "    print(\"Max retries reached, returning an empty DataFrame\")\n", "    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "7bbb2cf5-9097-48d6-9bf1-47b1f7d7ff8a", "metadata": {}, "outputs": [], "source": ["start_date = pd.to_datetime(datetime.today() + timedelta(hours=5.5)).strftime(\n", "    \"%Y-%m-%d\"\n", ")\n", "start_date"]}, {"cell_type": "code", "execution_count": null, "id": "e655b292-2372-4480-95d8-80cf4cd85039", "metadata": {}, "outputs": [], "source": ["# mail_input = pd.read_csv('mail_input.csv')\n", "\n", "mail_input = pb.from_sheets(\n", "    \"18CKhT6nDQNsZ1AHCv45xVGFg9mgvaskFfs2f090qwNc\",\n", "    \"MDM\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "594c8657-29cb-401d-b3ab-f9664d8b1075", "metadata": {}, "outputs": [], "source": ["mail_input = mail_input[\n", "    ~(\n", "        (mail_input[\"rank\"] == \"\")\n", "        | (mail_input[\"manufacturer_id\"] == \"\")\n", "        | (mail_input[\"new_manufacturer_name\"] == \"\")\n", "        | (mail_input[\"internal_mail_id\"] == \"\")\n", "        | (mail_input[\"external_mail_id\"] == \"\")\n", "        | (mail_input[\"enable_flag\"] == \"\")\n", "    )\n", "]\n", "\n", "mail_input[[\"rank\", \"enable_flag\"]] = (\n", "    mail_input[[\"rank\", \"enable_flag\"]].fillna(0).astype(int)\n", ")\n", "\n", "mail_input = mail_input[mail_input[\"enable_flag\"] > 0]\n", "\n", "mail_input = mail_input[\n", "    [\n", "        \"rank\",\n", "        \"manufacturer_id\",\n", "        \"new_manufacturer_name\",\n", "        \"internal_mail_id\",\n", "        \"external_mail_id\",\n", "        \"enable_flag\",\n", "    ]\n", "]\n", "\n", "\n", "mail_input.dropna(inplace=True)\n", "\n", "mail_input.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21d6031c-69fc-4981-9a65-c67fdfe24b24", "metadata": {}, "outputs": [], "source": ["manufacturer_sr_details = mail_input[\n", "    [\"rank\", \"manufacturer_id\", \"new_manufacturer_name\"]\n", "].copy()\n", "\n", "manufacturer_sr_details[\n", "    [\"manufacturer_id\", \"new_manufacturer_name\"]\n", "] = manufacturer_sr_details[[\"manufacturer_id\", \"new_manufacturer_name\"]].astype(str)\n", "\n", "manufacturer_sr_details = (\n", "    manufacturer_sr_details.set_index([\"rank\", \"manufacturer_id\"])\n", "    .apply(lambda col: col.str.split(\",\"))\n", "    .explode([\"new_manufacturer_name\"])\n", "    .reset_index()\n", ")\n", "\n", "manufacturer_sr_details = manufacturer_sr_details.drop_duplicates()\n", "\n", "manufacturer_sr_details[[\"rank\", \"manufacturer_id\"]] = manufacturer_sr_details[\n", "    [\"rank\", \"manufacturer_id\"]\n", "].astype(int)\n", "\n", "manufacturer_sr_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "186e7e2d-5111-49e7-9b12-f1ca31bcc366", "metadata": {}, "outputs": [], "source": ["# manufacturer_outlet_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     outlet_sr_details,\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "# adding_new_manufacturer_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     mail_input['rank'],\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "adding_new_manufacturer_details = manufacturer_sr_details.copy().drop_duplicates()\n", "\n", "adding_new_manufacturer_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d9b7a8ce-04b2-42aa-b03c-1b1192dd8c56", "metadata": {}, "outputs": [], "source": ["# outlet_id_list = tuple(list(adding_new_manufacturer_details['outlet_id'].unique()))\n", "# if len(outlet_id_list) < 2:\n", "#     outlet_id_list = list(outlet_id_list)\n", "#     outlet_id_list.append(-1)\n", "#     outlet_id_list = tuple(outlet_id_list)\n", "# len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "397badce-0bf7-4783-95f1-5ec777af6b18", "metadata": {}, "outputs": [], "source": ["manufacturer_id_list = tuple(\n", "    list(adding_new_manufacturer_details[\"manufacturer_id\"].unique())\n", ")\n", "if len(manufacturer_id_list) < 2:\n", "    manufacturer_id_list = list(manufacturer_id_list)\n", "    manufacturer_id_list.append(-1)\n", "    manufacturer_id_list = tuple(manufacturer_id_list)\n", "len(manufacturer_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "5af4a81d-a568-4181-ab6c-3bffca8b0aee", "metadata": {}, "outputs": [], "source": ["final_manufacturer_list = tuple(list(adding_new_manufacturer_details[\"rank\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "d28870ea-d381-43ba-b663-140d98fc8662", "metadata": {}, "outputs": [], "source": ["for sr_no in final_manufacturer_list:\n", "    required_df_render = adding_new_manufacturer_details[\n", "        adding_new_manufacturer_details[\"rank\"] == sr_no\n", "    ]\n", "    manufacturer_name = required_df_render[[\"new_manufacturer_name\"]].drop_duplicates()\n", "    final_manufacturer_name = list(manufacturer_name[\"new_manufacturer_name\"].unique())\n", "\n", "    # required_df_attach = adding_new_manufacturer_details[adding_new_manufacturer_details[\"rank\"]==sr_no].drop(columns={'rank'})\n", "    # required_df_attach.to_csv(f\"{final_manufacturer_name}_Active_Assortment_{start_date}.csv\",index=False)\n", "    from_email = \"<EMAIL>\"\n", "    email_id_sheet = mail_input\n", "\n", "    email_id_internal = email_id_sheet[\n", "        email_id_sheet[\"rank\"].astype(int) == sr_no\n", "    ].reset_index()[\"internal_mail_id\"][0]\n", "    final_internal = list(email_id_internal.split(\",\"))\n", "\n", "    email_id_external = email_id_sheet[\n", "        email_id_sheet[\"rank\"].astype(int) == sr_no\n", "    ].reset_index()[\"external_mail_id\"][0]\n", "    final_external = list(email_id_external.split(\",\"))\n", "\n", "    to_email = final_external + final_internal\n", "    subject = f\"Requirement for Updated Assortment Master_{final_manufacturer_name}_{start_date}\"\n", "    html_content = f\"\"\"\n", "    \n", "     <h4>Dear Team,</h4>\n", "\n", "<p>Changes in the master data of SKUs (UPC, MRP, Grammage & Images) are a regular part of our business. However, in the current construct, both our teams spend considerable amounts of time and effort in resolving such cases to maintain availability of the SKUs.</p>\n", "\n", "<p>We are implementing a simple process to update our master data on a fortnightly basis. For this, we will seek the information from you.</p>\n", "\n", "<h3>What is required from your end?</h3>\n", "<p>Share your updated master data on a fortnightly basis with us.</p>\n", "\n", "<p>You will receive an email every alternate Tuesday from our side. Please share the updated master details with us by Thursday of the same week. Our team will update it in our systems subsequently.</p>\n", "\n", "<h3>Benefits of streamlining this process</h3>\n", "<ul>\n", "<li>POs will be released for the latest SKUs with correct data. This will help in quicker allocation and improved fill rates.</li>\n", "<li>Faster GRN at the warehouse.</li>\n", "<li>Quicker go-live date on the platform.</li>\n", "<li>Better availability for the customers.</li>\n", "<li>Reduced loss of sales.</li>\n", "</ul>\n", "\n", "<p>Looking forward to continued partnership to add to the business growth & improve efficiency for both our teams.</p>\n", "\n", "<p>Please reach out to us for any issues. <br> <PERSON><PERSON>, <br> Partner Relations Team</p>\n", "\n", "    \n", "    \n", "    \"\"\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "    )\n", "    #               files=[f\"{final_manufacturer_name}_Active_Assortment_{start_date}.csv\"]\n", "    #              )"]}, {"cell_type": "code", "execution_count": null, "id": "ea572f74-cb6e-4c38-bc8b-8a0125e4c854", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}