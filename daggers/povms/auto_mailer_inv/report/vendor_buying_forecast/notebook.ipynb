{"cells": [{"cell_type": "code", "execution_count": null, "id": "67e0026a-33ce-4dcb-9e22-83f4eb840ff3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "import calendar\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "a17aca1f-b195-4296-a236-b4350d805c16", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            if not df.empty:\n", "                return df\n", "            else:\n", "                print(\"DataFrame is empty, retrying...\")\n", "        except Exception as e:\n", "            print(e)\n", "\n", "        time.sleep(5)\n", "\n", "    print(\"Max retries reached, returning an empty DataFrame\")\n", "    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "7bbb2cf5-9097-48d6-9bf1-47b1f7d7ff8a", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now()\n", "\n", "# Calculate the first day of the next month\n", "first_day_next_month = (current_date.replace(day=1) + <PERSON><PERSON><PERSON>(days=32)).replace(day=1)\n", "\n", "# Get the name of the next month\n", "next_month_name = first_day_next_month.strftime(\"%B\")\n", "next_month_name"]}, {"cell_type": "code", "execution_count": null, "id": "15cf5c46-2848-4156-82e9-e5da378db775", "metadata": {}, "outputs": [], "source": ["def buying_forecast():\n", "    buying_forecast = f\"\"\"\n", "    \n", "    select vb.date_,vb.facility_id,vb.facility_name,vb.manufacturer_id,vb.manufacturer_name,vb.item_id,vb.item_name,id.weight_in_kg uom,vb.lp,vb.case_sensitivity,vb.case_size,vb.fe_cpd,\n", "                               vb.max_demand,vb.be_excess,vb.f_demand,vb.cm_demand_forremaining_days,\n", "                              vb.be_excess_after_cm_demand,need,vb.final_need,final_need_rounded\n", "    from interim.N_1_month_vendor_buying_v2 vb\n", "    join supply_etls.item_details id on id.item_id = vb.item_id\n", "        where date_ is not null\n", "    order by vb.facility_id,item_id\n", "    \n", "    \"\"\"\n", "    return read_sql_query(buying_forecast, trino)\n", "\n", "\n", "buying_forecast = buying_forecast()"]}, {"cell_type": "code", "execution_count": null, "id": "c3a6528d-2855-4024-bc7b-741d176eb23c", "metadata": {}, "outputs": [], "source": ["buying_forecast.head(5)"]}, {"cell_type": "code", "execution_count": null, "id": "5d11a535-decd-496b-b229-82866cad3677", "metadata": {}, "outputs": [], "source": ["filter_data = buying_forecast[\n", "    [\n", "        \"date_\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"uom\",\n", "        \"lp\",\n", "        \"case_sensitivity\",\n", "        \"case_size\",\n", "        \"fe_cpd\",\n", "        \"max_demand\",\n", "        \"be_excess\",\n", "        \"f_demand\",\n", "        \"cm_demand_forremaining_days\",\n", "        \"be_excess_after_cm_demand\",\n", "        \"need\",\n", "        \"final_need\",\n", "        \"final_need_rounded\",\n", "    ]\n", "]\n", "filter_data"]}, {"cell_type": "code", "execution_count": null, "id": "da074a9e-e185-4597-8abc-12dadc4e04f8", "metadata": {}, "outputs": [], "source": ["filter_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "7ad1a07c-0c87-4b59-8106-f42e37fe5ae5", "metadata": {}, "outputs": [], "source": ["final_data = filter_data.copy()\n", "char_conv = [\"manufacturer_name\", \"item_name\", \"facility_name\"]\n", "final_data[char_conv] = final_data[char_conv].astype(str).replace(\"nan\", np.nan)\n", "\n", "int_conv = [\n", "    \"facility_id\",\n", "    \"item_id\",\n", "    \"manufacturer_id\",\n", "    \"case_sensitivity\",\n", "    \"case_size\",\n", "]\n", "final_data[int_conv] = final_data[int_conv].apply(pd.to_numeric, errors=\"coerce\")\n", "\n", "date_conv = [\"date_\"]\n", "final_data[date_conv] = final_data[date_conv].apply(pd.to_datetime, errors=\"coerce\")\n", "\n", "float_conv = [\n", "    \"uom\",\n", "    \"lp\",\n", "    \"fe_cpd\",\n", "    \"max_demand\",\n", "    \"be_excess\",\n", "    \"f_demand\",\n", "    \"cm_demand_forremaining_days\",\n", "    \"be_excess_after_cm_demand\",\n", "    \"need\",\n", "    \"final_need\",\n", "    \"final_need_rounded\",\n", "]\n", "final_data[float_conv] = final_data[float_conv].astype(float).replace(\"nan\", np.nan)\n", "\n", "final_data.loc[:, \"updated_at_ist\"] = pd.to_datetime(\n", "    datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "011a2baa-e8c7-4b4d-ab64-dd7e9b5ae661", "metadata": {}, "outputs": [], "source": ["final_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "95fb42a7-39ee-46c5-9478-1231c42d70de", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"DATE\", \"description\": \"Day of fetching Query\"},\n", "    {\"name\": \"facility_id\", \"type\": \"Integer\", \"description\": \"facility_id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"facility_name\"},\n", "    {\"name\": \"manufacturer_id\", \"type\": \"Integer\", \"description\": \"Manufacturer id\"},\n", "    {\n", "        \"name\": \"manufacturer_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"manufacturer name\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"Integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"uom\", \"type\": \"Real\", \"description\": \"weight in kg\"},\n", "    {\"name\": \"lp\", \"type\": \"real\", \"description\": \"Landing Price\"},\n", "    {\"name\": \"case_sensitivity\", \"type\": \"Integer\", \"description\": \"case sensitivity\"},\n", "    {\"name\": \"case_size\", \"type\": \"Integer\", \"description\": \"item qty in single case\"},\n", "    {\"name\": \"fe_cpd\", \"type\": \"Real\", \"description\": \"frontend store cpd\"},\n", "    {\"name\": \"max_demand\", \"type\": \"Real\", \"description\": \"Maximum demand\"},\n", "    {\"name\": \"be_excess\", \"type\": \"Real\", \"description\": \"excess qty in backend\"},\n", "    {\"name\": \"f_demand\", \"type\": \"Real\", \"description\": \"final demand\"},\n", "    {\n", "        \"name\": \"cm_demand_forremaining_days\",\n", "        \"type\": \"Real\",\n", "        \"description\": \"current month demand for remining day\",\n", "    },\n", "    {\n", "        \"name\": \"be_excess_after_cm_demand\",\n", "        \"type\": \"Real\",\n", "        \"description\": \"excess after current month demand\",\n", "    },\n", "    {\"name\": \"need\", \"type\": \"Real\", \"description\": \"need\"},\n", "    {\"name\": \"final_need\", \"type\": \"Real\", \"description\": \"final_need\"},\n", "    {\n", "        \"name\": \"final_need_rounded\",\n", "        \"type\": \"Real\",\n", "        \"description\": \"rounding up final need\",\n", "    },\n", "    {\"name\": \"updated_at_ist\", \"type\": \"TIMESTAMP(6)\", \"description\": \"update time\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "e0e6e4ac-1c9b-4dc9-9223-5c1e918a807c", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"vendor_buying_forecast\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"updated_at_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"Log for tracking vendor buying forecast\",\n", "}\n", "\n", "pb.to_trino(final_data, **kwargs)\n", "\n", "print(\"final base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "e655b292-2372-4480-95d8-80cf4cd85039", "metadata": {}, "outputs": [], "source": ["# mail_input = pd.read_csv('mail_input.csv')\n", "\n", "mail_input = pb.from_sheets(\n", "    \"18CKhT6nDQNsZ1AHCv45xVGFg9mgvaskFfs2f090qwNc\",\n", "    \"vendor_buying_forecast\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "594c8657-29cb-401d-b3ab-f9664d8b1075", "metadata": {}, "outputs": [], "source": ["mail_input = mail_input[\n", "    ~(\n", "        (mail_input[\"rank\"] == \"\")\n", "        | (mail_input[\"manufacturer_id\"] == \"\")\n", "        | (mail_input[\"new_manufacturer_name\"] == \"\")\n", "        | (mail_input[\"outlet_id\"] == \"\")\n", "        | (mail_input[\"outlet_id\"] == \"\")\n", "        | (mail_input[\"internal_mail_id\"] == \"\")\n", "        | (mail_input[\"external_mail_id\"] == \"\")\n", "        | (mail_input[\"enable_flag\"] == \"\")\n", "    )\n", "]\n", "\n", "mail_input[[\"rank\", \"enable_flag\"]] = (\n", "    mail_input[[\"rank\", \"enable_flag\"]].fillna(0).astype(int)\n", ")\n", "\n", "mail_input = mail_input[mail_input[\"enable_flag\"] > 0]\n", "\n", "mail_input = mail_input[\n", "    [\n", "        \"rank\",\n", "        \"manufacturer_id\",\n", "        \"new_manufacturer_name\",\n", "        \"outlet_id\",\n", "        \"internal_mail_id\",\n", "        \"external_mail_id\",\n", "        \"enable_flag\",\n", "    ]\n", "]\n", "\n", "\n", "mail_input.dropna(inplace=True)\n", "\n", "mail_input.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21d6031c-69fc-4981-9a65-c67fdfe24b24", "metadata": {}, "outputs": [], "source": ["manufacturer_sr_details = mail_input[[\"rank\", \"manufacturer_id\"]].copy()\n", "\n", "manufacturer_sr_details = (\n", "    manufacturer_sr_details.set_index([\"rank\"])\n", "    .apply(lambda col: col.str.split(\",\"))\n", "    .explode([\"manufacturer_id\"])\n", "    .reset_index()\n", "    .reindex(manufacturer_sr_details.columns, axis=1)\n", ")\n", "\n", "manufacturer_sr_details = manufacturer_sr_details.drop_duplicates()\n", "\n", "manufacturer_sr_details[[\"rank\", \"manufacturer_id\"]] = manufacturer_sr_details[\n", "    [\"rank\", \"manufacturer_id\"]\n", "].astype(int)\n", "\n", "manufacturer_sr_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "186e7e2d-5111-49e7-9b12-f1ca31bcc366", "metadata": {}, "outputs": [], "source": ["# manufacturer_outlet_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     outlet_sr_details,\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "# adding_new_manufacturer_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     mail_input['rank'],\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "adding_new_manufacturer_details = manufacturer_sr_details.copy().drop_duplicates()\n", "\n", "adding_new_manufacturer_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "397badce-0bf7-4783-95f1-5ec777af6b18", "metadata": {}, "outputs": [], "source": ["manufacturer_id_list = tuple(\n", "    list(adding_new_manufacturer_details[\"manufacturer_id\"].unique())\n", ")\n", "if len(manufacturer_id_list) < 2:\n", "    manufacturer_id_list = list(manufacturer_id_list)\n", "    manufacturer_id_list.append(-1)\n", "    manufacturer_id_list = tuple(manufacturer_id_list)\n", "len(manufacturer_id_list)"]}, {"cell_type": "markdown", "id": "12570da3-adac-47a5-8397-1dea67d5bb28", "metadata": {}, "source": ["## Forecast Data"]}, {"cell_type": "code", "execution_count": null, "id": "44d97c8b-13fe-4c7c-832d-a39731690580", "metadata": {}, "outputs": [], "source": ["final_data.rename(columns={\"final_need_rounded\": \"qty\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "89f49c38-f5f8-4940-aca1-fede9201159c", "metadata": {}, "outputs": [], "source": ["final_data = final_data.sort_values(by=[\"facility_id\", \"item_id\"])\n", "# df_sorted = df.sort_values(by='date')"]}, {"cell_type": "code", "execution_count": null, "id": "4769c551-5a3c-45e0-9c5d-abf1102389c8", "metadata": {}, "outputs": [], "source": ["mail_data = final_data[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"qty\",\n", "    ]\n", "].drop_duplicates()\n", "mail_data"]}, {"cell_type": "code", "execution_count": null, "id": "63e5730e-6a76-4390-a20a-dbb63dca9da5", "metadata": {}, "outputs": [], "source": ["active_data = pd.merge(\n", "    mail_data, adding_new_manufacturer_details, on=[\"manufacturer_id\"], how=\"inner\"\n", ")\n", "\n", "active_data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5af4a81d-a568-4181-ab6c-3bffca8b0aee", "metadata": {}, "outputs": [], "source": ["final_manufacturer_list = tuple(list(active_data[\"rank\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "d28870ea-d381-43ba-b663-140d98fc8662", "metadata": {}, "outputs": [], "source": ["for sr_no in final_manufacturer_list:\n", "    required_df_render = active_data[active_data[\"rank\"] == sr_no]\n", "    manufacturer_name = required_df_render[[\"manufacturer_name\"]].drop_duplicates()\n", "    final_manufacturer_name = list(manufacturer_name[\"manufacturer_name\"].unique())\n", "\n", "    required_df_attach = active_data[active_data[\"rank\"] == sr_no].drop(\n", "        columns={\"rank\"}\n", "    )\n", "    required_df_attach.to_csv(\n", "        f\"{final_manufacturer_name}_Vendor_forecast_{next_month_name}.csv\", index=False\n", "    )\n", "    from_email = \"<EMAIL>\"\n", "    email_id_sheet = mail_input\n", "\n", "    email_id_internal = email_id_sheet[\n", "        email_id_sheet[\"rank\"].astype(int) == sr_no\n", "    ].reset_index()[\"internal_mail_id\"][0]\n", "    final_internal = list(email_id_internal.split(\",\"))\n", "\n", "    email_id_external = email_id_sheet[\n", "        email_id_sheet[\"rank\"].astype(int) == sr_no\n", "    ].reset_index()[\"external_mail_id\"][0]\n", "    final_external = list(email_id_external.split(\",\"))\n", "\n", "    to_email = final_external + final_internal\n", "    subject = f\"Vendor_forecast_for_{final_manufacturer_name}_{next_month_name}\"\n", "    html_content = f\"Hi Team, <br><br> Please find the attached sheet for tentative vendor forecast for {next_month_name} month.\"\n", "\n", "    # print(html_content)\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[f\"{final_manufacturer_name}_Vendor_forecast_{next_month_name}.csv\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "ea572f74-cb6e-4c38-bc8b-8a0125e4c854", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}