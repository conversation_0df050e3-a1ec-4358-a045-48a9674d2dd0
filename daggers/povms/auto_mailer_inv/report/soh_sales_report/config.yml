alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: soh_sales_report
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/auto_mailer_inv/report/soh_sales_report
paused: true
pool: povms_pool
project_name: auto_mailer_inv
schedule:
  end_date: '2024-11-30T18:30:00'
  interval: 0 5 * * MON
  start_date: '2024-09-28T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
