{"cells": [{"cell_type": "code", "execution_count": null, "id": "67e0026a-33ce-4dcb-9e22-83f4eb840ff3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "a17aca1f-b195-4296-a236-b4350d805c16", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "\n", "            if not df.empty:\n", "                return df\n", "            else:\n", "                print(\"DataFrame is empty, retrying...\")\n", "        except Exception as e:\n", "            print(e)\n", "\n", "        time.sleep(5)\n", "\n", "    print(\"Max retries reached, returning an empty DataFrame\")\n", "    return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "7bbb2cf5-9097-48d6-9bf1-47b1f7d7ff8a", "metadata": {}, "outputs": [], "source": ["start_date = pd.to_datetime(datetime.today() + timedelta(hours=5.5)).strftime(\"%Y-%m-%d\")\n", "# start_date = start_date[0]\n", "start_date"]}, {"cell_type": "code", "execution_count": null, "id": "e655b292-2372-4480-95d8-80cf4cd85039", "metadata": {}, "outputs": [], "source": ["# mail_input = pd.read_csv('mail_input.csv')\n", "\n", "mail_input = pb.from_sheets(\n", "    \"18CKhT6nDQNsZ1AHCv45xVGFg9mgvaskFfs2f090qwNc\",\n", "    \"mail_input_Soh&Sales\",\n", "    clear_cache=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "594c8657-29cb-401d-b3ab-f9664d8b1075", "metadata": {}, "outputs": [], "source": ["mail_input = mail_input[\n", "    ~(\n", "        (mail_input[\"rank\"] == \"\")\n", "        | (mail_input[\"manufacturer_id\"] == \"\")\n", "        | (mail_input[\"new_manufacturer_name\"] == \"\")\n", "        | (mail_input[\"outlet_id\"] == \"\")\n", "        | (mail_input[\"outlet_id\"] == \"\")\n", "        | (mail_input[\"internal_mail_id\"] == \"\")\n", "        | (mail_input[\"external_mail_id\"] == \"\")\n", "        | (mail_input[\"enable_flag\"] == \"\")\n", "    )\n", "]\n", "\n", "mail_input[[\"rank\", \"enable_flag\"]] = mail_input[[\"rank\", \"enable_flag\"]].fillna(0).astype(int)\n", "\n", "mail_input = mail_input[mail_input[\"enable_flag\"] > 0]\n", "\n", "mail_input = mail_input[\n", "    [\n", "        \"rank\",\n", "        \"manufacturer_id\",\n", "        \"new_manufacturer_name\",\n", "        \"outlet_id\",\n", "        \"internal_mail_id\",\n", "        \"external_mail_id\",\n", "        \"enable_flag\",\n", "    ]\n", "]\n", "\n", "\n", "mail_input.dropna(inplace=True)\n", "\n", "mail_input.head()"]}, {"cell_type": "code", "execution_count": null, "id": "21d6031c-69fc-4981-9a65-c67fdfe24b24", "metadata": {}, "outputs": [], "source": ["manufacturer_sr_details = mail_input[[\"rank\", \"manufacturer_id\"]].copy()\n", "\n", "manufacturer_sr_details = (\n", "    manufacturer_sr_details.set_index([\"rank\"])\n", "    .apply(lambda col: col.str.split(\",\"))\n", "    .explode([\"manufacturer_id\"])\n", "    .reset_index()\n", "    .reindex(manufacturer_sr_details.columns, axis=1)\n", ")\n", "\n", "manufacturer_sr_details = manufacturer_sr_details.drop_duplicates()\n", "\n", "manufacturer_sr_details[[\"rank\", \"manufacturer_id\"]] = manufacturer_sr_details[\n", "    [\"rank\", \"manufacturer_id\"]\n", "].astype(int)\n", "\n", "manufacturer_sr_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b7309483-3ff1-4caf-afb9-93310f0b1bf2", "metadata": {}, "outputs": [], "source": ["# outlet_sr_details = mail_input[['rank','outlet_id']].copy()\n", "\n", "# outlet_sr_details = (outlet_sr_details.set_index(['rank'])\n", "#        .apply(lambda col: col.str.split(','))\n", "#        .explode(['outlet_id'])\n", "#        .reset_index()\n", "#        .reindex(outlet_sr_details.columns, axis=1))\n", "\n", "# outlet_sr_details = outlet_sr_details.drop_duplicates()\n", "\n", "# outlet_sr_details[['rank','outlet_id']] = outlet_sr_details[['rank','outlet_id']].astype(int)\n", "\n", "# outlet_sr_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "186e7e2d-5111-49e7-9b12-f1ca31bcc366", "metadata": {}, "outputs": [], "source": ["# manufacturer_outlet_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     outlet_sr_details,\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "# adding_new_manufacturer_details = pd.merge(\n", "#     manufacturer_sr_details,\n", "#     mail_input['rank'],\n", "#     on = ['rank'],\n", "#     how = 'inner'\n", "# )\n", "\n", "adding_new_manufacturer_details = manufacturer_sr_details.copy().drop_duplicates()\n", "\n", "adding_new_manufacturer_details.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d9b7a8ce-04b2-42aa-b03c-1b1192dd8c56", "metadata": {}, "outputs": [], "source": ["# outlet_id_list = tuple(list(adding_new_manufacturer_details['outlet_id'].unique()))\n", "# if len(outlet_id_list) < 2:\n", "#     outlet_id_list = list(outlet_id_list)\n", "#     outlet_id_list.append(-1)\n", "#     outlet_id_list = tuple(outlet_id_list)\n", "# len(outlet_id_list)"]}, {"cell_type": "code", "execution_count": null, "id": "397badce-0bf7-4783-95f1-5ec777af6b18", "metadata": {}, "outputs": [], "source": ["manufacturer_id_list = tuple(list(adding_new_manufacturer_details[\"manufacturer_id\"].unique()))\n", "if len(manufacturer_id_list) < 2:\n", "    manufacturer_id_list = list(manufacturer_id_list)\n", "    manufacturer_id_list.append(-1)\n", "    manufacturer_id_list = tuple(manufacturer_id_list)\n", "len(manufacturer_id_list)"]}, {"cell_type": "markdown", "id": "12570da3-adac-47a5-8397-1dea67d5bb28", "metadata": {}, "source": ["## Assortment Query"]}, {"cell_type": "code", "execution_count": null, "id": "6af3635e-d2d0-4715-acf4-906bf3821d30", "metadata": {}, "outputs": [], "source": ["def soh_query():\n", "    soh_query = f\"\"\"\n", "    with\n", "outlet_details as\n", "    (select * from supply_etls.outlet_details),\n", "\n", "item_details as\n", "    (select * from supply_etls.item_details),\n", "\n", "raw_data as\n", "    (select * from supply_etls.partnersbiz_frontend_backend_inventory_log\n", "        where\n", "            insert_ds_ist >= current_date - interval '3' day\n", "    ),\n", "\n", "final_raw_data as\n", "    (select * from raw_data\n", "        where\n", "            updated_at_ist = (select max(updated_at_ist) from raw_data)\n", "    ),\n", "\n", "final_view as\n", "    (select\n", "        od.facility_id,\n", "        od.facility_name,\n", "        frd.item_id,\n", "        id.item_name,\n", "        id.manufacturer_id,\n", "        id.manufacturer_name,\n", "        sum(be_net_inventory + be_pending_put_away_quantity + be_open_sto_quantity + fe_open_sto_quantity) as backend_inv_qty,\n", "        sum(fe_net_inventory + fe_pending_put_away_quantity) as frontend_inv_qty\n", "        \n", "            from final_raw_data frd\n", "            \n", "                join\n", "                    item_details id on id.item_id = frd.item_id\n", "                \n", "                join\n", "                    outlet_details od on od.hot_outlet_id = frd.hot_outlet_id\n", "                where od.active in (1) and od.ars_active in (1) and od.facility_id not in (154)\n", "                and od.business_type_id in (1,12)\n", "                    \n", "                    group by 1,2,3,4,5,6\n", "    )\n", "    \n", "        select * from final_view\n", "    \n", "    \"\"\"\n", "    return read_sql_query(soh_query, trino)\n", "\n", "\n", "soh_query = soh_query()"]}, {"cell_type": "code", "execution_count": null, "id": "3f55fc45-3d1b-4638-96db-0c2a5f2d1993", "metadata": {}, "outputs": [], "source": ["soh_query"]}, {"cell_type": "code", "execution_count": null, "id": "63e5730e-6a76-4390-a20a-dbb63dca9da5", "metadata": {}, "outputs": [], "source": ["soh_query = pd.merge(\n", "    soh_query, adding_new_manufacturer_details, on=[\"manufacturer_id\"], how=\"inner\"\n", ")\n", "\n", "soh_query.head()"]}, {"cell_type": "code", "execution_count": null, "id": "04f1e5d8-5d79-4a8d-bcc0-896bfcf1ead9", "metadata": {}, "outputs": [], "source": ["soh_query = soh_query[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"backend_inv_qty\",\n", "        \"frontend_inv_qty\",\n", "        \"rank\",\n", "    ]\n", "].drop_duplicates()\n", "soh_query"]}, {"cell_type": "code", "execution_count": null, "id": "5af4a81d-a568-4181-ab6c-3bffca8b0aee", "metadata": {}, "outputs": [], "source": ["final_manufacturer_list = tuple(list(soh_query[\"rank\"].unique()))"]}, {"cell_type": "markdown", "id": "53d1ba4d-7077-4916-b08f-50a54d111b7e", "metadata": {}, "source": ["## Sales Query"]}, {"cell_type": "code", "execution_count": null, "id": "065d6ebb-7670-4ac9-aaf9-a9ffbc70d98e", "metadata": {}, "outputs": [], "source": ["def sales_query():\n", "    sales_query = f\"\"\"\n", "     with\n", "sales as\n", "    (select \n", "        order_create_date_ist as order_date,\n", "        iid.order_id,\n", "        iid.pos_outlet_id,\n", "        sd.city_name as city_name,\n", "        iid.item_id,sid.item_name,sid.manufacturer_id,sid.manufacturer_name,sid.p_type,sid.l0_category l0,\n", "        total_forward_mrp mrp,\n", "        (total_forward_quantity - total_returned_quantity) as sales_quantity\n", "        \n", "        from dwh.fact_sales_invoice_item_details iid\n", "        join supply_etls.outlet_details sd on sd.hot_outlet_id = iid.pos_outlet_id and sd.business_type_id in (7)\n", "        join supply_etls.item_details sid on sid.item_id = iid.item_id \n", "                \n", "            join dwh.fact_sales_order_details sod on sod.cart_id = iid.cart_id and sod.order_id = iid.order_id\n", "            and sod.order_create_dt_ist >= current_date - interval'45' day\n", "                    \n", "                    where (iid.order_create_date_ist >= current_date - interval'45' day)\n", "                        and sod.is_internal_order = false\n", "                        and (sod.order_type not like '%%Internal%%' or sod.order_type is null)\n", "                        and sod.order_current_status = 'DELIVERED'\n", "                        and  sid.assortment_type in ('Packaged Goods') and sid.handling_type in ('Non Packaging Material')\n", "                        group by 1,2,3,4,5,6,7,8,9,10,11,12\n", "    )\n", "    \n", "        select order_date,city_name,l0,manufacturer_id,manufacturer_name,s.item_id,item_name,round(mrp,0) mrp,sum(sales_quantity) sale_qty\n", "        from sales s\n", "        left join lake_rpc.item_outlet_tag_mapping itm on itm.item_id = s.item_id and itm.outlet_id = s.pos_outlet_id and itm.tag_type_id =8\n", "        join lake_retail.console_outlet co on co.id = cast(itm.tag_value as int) and co.business_type_id in (1,12,20)\n", "         WHERE order_date between current_date - interval '7' day and current_date - interval '1' day\n", "        group by 1,2,3,4,5,6,7,8\n", "        order by order_date\n", "    \n", "    \"\"\"\n", "    return read_sql_query(sales_query, trino)\n", "\n", "\n", "sales_query = sales_query()"]}, {"cell_type": "code", "execution_count": null, "id": "5d69136b-7397-47a1-a69f-5a22c6ffe124", "metadata": {}, "outputs": [], "source": ["sales_query"]}, {"cell_type": "code", "execution_count": null, "id": "2a03a4cb-07bd-4514-a607-18565e6dfbfa", "metadata": {}, "outputs": [], "source": ["sales_query = pd.merge(\n", "    sales_query, adding_new_manufacturer_details, on=[\"manufacturer_id\"], how=\"inner\"\n", ")\n", "\n", "sales_query.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1e581e4e-f13a-4825-bf73-e8695e109c59", "metadata": {}, "outputs": [], "source": ["sales_query = sales_query[\n", "    [\n", "        \"order_date\",\n", "        \"city_name\",\n", "        \"l0\",\n", "        \"manufacturer_id\",\n", "        \"manufacturer_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"mrp\",\n", "        \"sale_qty\",\n", "        \"rank\",\n", "    ]\n", "].drop_duplicates()\n", "sales_query"]}, {"cell_type": "code", "execution_count": null, "id": "5e6b3f01-b36e-4a29-8345-e5f5a1a30906", "metadata": {}, "outputs": [], "source": ["final_manufacturer_list_1 = tuple(list(sales_query[\"rank\"].unique()))"]}, {"cell_type": "markdown", "id": "3101098b-8908-4c79-8f5f-0df85cce7e03", "metadata": {}, "source": ["## SOH Report Mail"]}, {"cell_type": "code", "execution_count": null, "id": "41f7428f-52f5-482a-b7d2-cd59284921ed", "metadata": {}, "outputs": [], "source": ["for sr_no in final_manufacturer_list:\n", "    required_df_render = soh_query[soh_query[\"rank\"] == sr_no]\n", "    manufacturer_name = required_df_render[[\"manufacturer_name\"]].drop_duplicates()\n", "    final_manufacturer_name = list(manufacturer_name[\"manufacturer_name\"].unique())\n", "\n", "    required_df_attach = soh_query[soh_query[\"rank\"] == sr_no].drop(columns={\"rank\"})\n", "    required_df_attach.to_csv(f\"{final_manufacturer_name}_soh_report_{start_date}.csv\", index=False)\n", "    from_email = \"<EMAIL>\"\n", "    email_id_sheet = mail_input\n", "\n", "    email_id_internal = email_id_sheet[email_id_sheet[\"rank\"].astype(int) == sr_no].reset_index()[\n", "        \"internal_mail_id\"\n", "    ][0]\n", "    final_internal = list(email_id_internal.split(\",\"))\n", "\n", "    email_id_external = email_id_sheet[email_id_sheet[\"rank\"].astype(int) == sr_no].reset_index()[\n", "        \"external_mail_id\"\n", "    ][0]\n", "    final_external = list(email_id_external.split(\",\"))\n", "\n", "    to_email = final_external + final_internal\n", "    subject = f\"SOH report for {final_manufacturer_name}_{start_date}\"\n", "    html_content = f\"Hi Team, <br><br> Please find the attached sheet for  SOH report across our facilities.<br><br>Please let us know if any further help required from our side.<br><br>Regards\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject,\n", "        html_content,\n", "        files=[f\"{final_manufacturer_name}_soh_report_{start_date}.csv\"],\n", "    )"]}, {"cell_type": "markdown", "id": "c538dfcd-8d4a-4493-bfd8-36640922fea2", "metadata": {}, "source": ["## Sales Report"]}, {"cell_type": "code", "execution_count": null, "id": "a8989c35-9e3f-4040-85cf-325c89febf85", "metadata": {}, "outputs": [], "source": ["for sr_no1 in final_manufacturer_list_1:\n", "    required_df_render = sales_query[sales_query[\"rank\"] == sr_no1]\n", "    manufacturer_name = required_df_render[[\"manufacturer_name\"]].drop_duplicates()\n", "    final_manufacturer_name = list(manufacturer_name[\"manufacturer_name\"].unique())\n", "\n", "    required_df_attach_1 = sales_query[sales_query[\"rank\"] == sr_no1].drop(columns={\"rank\"})\n", "    required_df_attach_1.to_csv(\n", "        f\"{final_manufacturer_name}_sales_report_{start_date}.csv\", index=False\n", "    )\n", "    from_email = \"<EMAIL>\"\n", "    email_id_sheet = mail_input\n", "\n", "    email_id_internal = email_id_sheet[email_id_sheet[\"rank\"].astype(int) == sr_no1].reset_index()[\n", "        \"internal_mail_id\"\n", "    ][0]\n", "    final_internal = list(email_id_internal.split(\",\"))\n", "\n", "    email_id_external = email_id_sheet[email_id_sheet[\"rank\"].astype(int) == sr_no1].reset_index()[\n", "        \"external_mail_id\"\n", "    ][0]\n", "    final_external = list(email_id_external.split(\",\"))\n", "\n", "    to_email = final_external + final_internal\n", "    subject1 = f\"Sales Report for {final_manufacturer_name}_{start_date}\"\n", "    html_content1 = f\"Hi Team, <br><br> Please find the attached sheet for  day level sales report<br><br>Please let us know if any further help required from our side.<br><br>Regards\"\n", "\n", "    pb.send_email(\n", "        from_email,\n", "        to_email,\n", "        subject1,\n", "        html_content1,\n", "        files=[f\"{final_manufacturer_name}_sales_report_{start_date}.csv\"],\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "eefd9aa8-e180-4a5d-9625-e93454a1a305", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c047de4e-f603-4712-aff0-7004f1be8a72", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ea572f74-cb6e-4c38-bc8b-8a0125e4c854", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}