dag_name: new_doi_facility_eco
dag_type: report
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U01JWCA6AJG
path: povms/doi/report/new_doi_facility_eco
paused: true
pool: povms_pool
project_name: doi
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 35 3 * * *
  start_date: '2022-02-03T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- new_doi_email_template.html
tags: []
template_name: notebook
version: 5
