<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
    }    
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}
.tg .tg-0lax {
    text-align: center;
    vertical-align: center
}
.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: center;
    vertical-align: center
}
.tg .tg-hmp3 {
  background-color: #faf8eb;
  text-align: center;
  vertical-align: center
}

</style>
Hi All,<br/>
Please find below DOI report :
<p>
For the definition of Facility DOI and Ecosystem DOI refer following link : 
<a href="https://docs.google.com/document/d/1WYYO5yT_eU8NIb7dZ2lDWFzUcXQdLTFPrtmJPqoApwo/edit#heading=h.2xfa73ozhktx">New DOI definition</a>
    <br/>
    

    
<p><b> NDD </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Zone</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">FID</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>

            <th class="tg-0lax"><span style="font-weight:bold">Facility DOI</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Ecosystem Facility DOI</span></th>

            <th class="tg-0lax"><span style="font-weight:bold">Facility DOI bucket X</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Ecosystem Facility DOI bucket X</span></th>

            <th class="tg-0lax"><span style="font-weight:bold">Facility DOI bucket A</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Ecosystem Facility DOI bucket A</span></th>
            
        </tr>
        {% for product in ndd_summary %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["zone"] }}</b>
            </td>
           
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_id"] }}</b>
            </td>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["n0_facility_name"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_doi"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["ecosystem_doi"] }}</b>
            </td>

            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_doi_X"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["ecosystem_doi_X"] }}</b>
            </td>

            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_doi_A"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["ecosystem_doi_A"] }}</b>
            </td>
            
            
        
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<br/>
<!-- <p><b> Dark Stores </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Zone</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">FID</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>

            <th class="tg-0lax"><span style="font-weight:bold">Facility DOI</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Ecosystem Facility DOI</span></th>

            <th class="tg-0lax"><span style="font-weight:bold">Facility DOI bucket X</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Ecosystem Facility DOI bucket X</span></th>

            <th class="tg-0lax"><span style="font-weight:bold">Facility DOI bucket A</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Ecosystem Facility DOI bucket A</span></th>
            
        </tr>
        {% for product in darkstore_summary %}
        <tr>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["zone"] }}</b>
            </td>
           
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_id"] }}</b>
            </td>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["n0_facility_name"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_doi"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["ecosystem_doi"] }}</b>
            </td>

            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_doi_X"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["ecosystem_doi_X"] }}</b>
            </td>

            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["facility_doi_A"] }}</b>
            </td>

            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product["ecosystem_doi_A"] }}</b>
            </td>
            
            
        
        </tr>
        {% endfor %}

    </tbody>
</table>
 -->
    <p></p>
    <p></p>
    <p></p>


<p> For base numbers of above report please refer <a href="https://docs.google.com/spreadsheets/d/1PvhbEQEuO8QcwKlHRhb2R_RFeDAy3CTafhKLNdjMm30/edit#gid=1175046204">New_DOI_Framework_NDD and New_DOI_Framework_DarkStore </a>
<br />
    In case you have doubts with DOI numbers <NAME_EMAIL><br /> <br /> 
</p>
<p>Best Regards,<br />
Inventory Data Team

</p>
<p></p>    
