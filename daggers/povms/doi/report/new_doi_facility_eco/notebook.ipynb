{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ndd_facility_df = pb.from_sheets(\n", "    sheetid=\"1xj9YGix9Q_PUMGWtZKXEeoel8AY2jQ-ctoZgzFB8z6A\", sheetname=\"facility_list\"\n", ")\n", "\n", "ndd_facility_list = list(ndd_facility_df[\"facility_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_data_query = \"\"\"\n", "with \n", "\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "lp_treatment_tbl as\n", "(\n", "select \n", "a.item_id,\n", "outlet_id,\n", "b.warehouse_id,\n", "facility_id,\n", "landing_price,\n", "row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "row_number() over (partition by a.item_id order by dt_ist desc) as row_rank1\n", "from weighted_landing_price a\n", "left join (select * from  lake_retail.warehouse_outlet_mapping  where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "left join (select * from  lake_retail.view_console_outlet  where active = true) c on b.warehouse_id = c.id\n", "where landing_price is not null\n", ")\n", "\n", "\n", ",\n", "\n", "itm_availability as\n", "(\n", "select distinct * from \n", "\t(\n", "\t\tselect e.zone,\n", "\t\ta.facility_id,\n", "\t\ta.facility_name,\n", "\t\t(case when facility_name ilike '%% es%%' then 'Dark' when facility_name ilike '%%dark%%' then 'Franchise' else 'NDD' end) as store_type,\n", "\t\ta.item_id,\n", "\t\tdate(order_date) as order_date,\n", "\t\tavailability,\n", "\t\tapp_live,\n", "\t\tinv_flag,\n", "\t\tl0,\n", "\t\tcase when bucket_x = 'Y' then 'X' when buckets = 'A' then 'A' else 'B' end as buckets,\n", "\t\tactual_quantity,\n", "\t\tcoalesce(actual_quantity,0) - coalesce(blocked_quantity,0) as unblocked_quantity,\n", "\t\tcoalesce(coalesce(b.landing_price,c.landing_price),d.variant_mrp*0.7000) as landing_price,\n", "\t\trow_number() over (partition by a.item_id, a.facility_id, date(order_date) order by order_date desc) as row_rank\n", "\t\tfrom(\n", "\t\tSELECT r.*\n", "\t\tFROM consumer.rpc_daily_availability r\n", "\t\tINNER JOIN\n", "\t\t  (SELECT facility_id,\n", "\t\t          date(order_date) AS order_date,\n", "\t\t          max(order_date) AS maxi\n", "\t\t   FROM consumer.rpc_daily_availability\n", "\t\t   where date(order_date) = current_date-1\n", "\t\t   GROUP BY 1,\n", "\t\t            2) x1 ON r.facility_id=x1.facility_id\n", "\t\tAND x1.maxi=r.order_date )a\n", "\t\tleft join zone_tbl_treatment e on a.facility_id = e.facility_id\n", "\t\tleft join (select * from lp_treatment_tbl where row_rank = 1) b on a.facility_id = b.facility_id  AND a.item_id = b.item_id\n", "\t\tleft join (select * from lp_treatment_tbl where row_rank1 = 1) c on a.item_id = c.item_id\n", "\t\tleft join (select * from item_level_info where row_rank = 1) d on a.item_id = d.item_id\n", "\t\twhere date(order_date) = current_date-1\n", "        and new_substate=1\n", "\n", "\t) x1\n", "where \n", "row_rank=1\n", ")\n", ",\n", "\n", "avg_30days_forecast as (select item_id, facility_id, avg(forecast_quantity) as avg_30_days_forecast_qty from\n", "    (select item_id, facility_id, date, sum(consumption) as forecast_quantity from(\n", "        (select item_id, outlet_id, date, consumption, active, create_ts, update_ts from(\n", "            select item_id, outlet_id, date, consumption, active, created_at as create_ts, updated_at as update_ts,\n", "            rank() over (partition by item_id, outlet_id, date order by updated_at desc) as rnk\n", "            from lake_snorlax.date_wise_consumption\n", "            where date between current_date + interval '1 days' and current_date + interval '30 days')\n", "        where rnk = 1) a\n", "        left join\n", "        (select id, facility_id from lake_retail.console_outlet) b\n", "        on a.outlet_id = b.id\n", "    )\n", "    group by 1,2,3)\n", "group by 1,2)\n", "\n", "select a.*, b.avg_30_days_forecast_qty from itm_availability a\n", "left join avg_30days_forecast b on a.facility_id=b.facility_id and a.item_id=b.item_id\n", "\"\"\"\n", "base_df = pd.read_sql(base_data_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_tag_query = \"\"\"\n", "SELECT DISTINCT ro.facility_id AS frontend_facility,\n", "                      t.outlet_id as frontend_outlet,\n", "                      ro.name as frontend_outlet_name,\n", "                      r.facility_id as backend_facility,\n", "                      tag_value AS backend_outlet,\n", "                      r.name AS backend_outlet_name,\n", "                      item_id,\n", "                      1 as st_flag\n", "      FROM lake_rpc.item_outlet_tag_mapping t\n", "      INNER JOIN lake_retail.console_outlet ro ON ro.id=t.outlet_id\n", "      INNER JOIN lake_retail.console_outlet r ON r.id=t.tag_value\n", "      WHERE tag_type_id=8\n", "        AND t.active=1\n", "        and ro.name not like '%%old%%'\n", "\"\"\"\n", "tea_tag_df = pd.read_sql(tea_tag_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_type_query = \"\"\"select distinct facility_id \n", ", case when business_type_id=7 then 'DarkStore'\n", "        when business_type_id=8 then 'FranchiseStore'\n", "        else 'NDD' end as store_type\n", "from lake_retail.console_outlet\n", "where active =1\"\"\"\n", "\n", "facility_type_df = pd.read_sql(facility_type_query, redshift)\n", "\n", "facility_type_df1 = facility_type_df[\n", "    (facility_type_df[\"store_type\"] == \"DarkStore\")\n", "    | (facility_type_df[\"facility_id\"].isin(ndd_facility_list))\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# facility_type_df1.groupby(['store_type'])['facility_id'].nunique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_df.fillna(value={\"actual_quantity\": 0}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tea_tag_df2 = tea_tag_df[[\"frontend_facility\", \"backend_facility\", \"item_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_n0 = base_df[\n", "    [\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"item_id\",\n", "        \"buckets\",\n", "        \"actual_quantity\",\n", "        \"unblocked_quantity\",\n", "        \"landing_price\",\n", "        \"avg_30_days_forecast_qty\",\n", "    ]\n", "].add_prefix(\"n0_\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# backend_df0= df_n0[df_n0['n0_facility_id'].isin(tea_tag_df2['backend_facility'].unique())]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_df0 = pd.merge(\n", "    facility_type_df1, df_n0, left_on=[\"facility_id\"], right_on=[\"n0_facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_df1 = pd.merge(\n", "    pd.merge(\n", "        backend_df0,\n", "        tea_tag_df2.rename(\n", "            columns={\n", "                \"backend_facility\": \"n0_facility_id\",\n", "                \"frontend_facility\": \"n1_facility_id\",\n", "                \"item_id\": \"n1_item_id\",\n", "            }\n", "        ),\n", "        left_on=[\"n0_facility_id\", \"n0_item_id\"],\n", "        right_on=[\"n0_facility_id\", \"n1_item_id\"],\n", "        how=\"left\",\n", "    ),\n", "    base_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"buckets\",\n", "            \"unblocked_quantity\",\n", "            \"landing_price\",\n", "            \"avg_30_days_forecast_qty\",\n", "        ]\n", "    ].add_prefix(\"n1_\"),\n", "    on=[\"n1_facility_id\", \"n1_item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_df2 = pd.merge(\n", "    pd.merge(\n", "        backend_df1,\n", "        tea_tag_df2.rename(\n", "            columns={\n", "                \"backend_facility\": \"n1_facility_id\",\n", "                \"frontend_facility\": \"n2_facility_id\",\n", "                \"item_id\": \"n2_item_id\",\n", "            }\n", "        ),\n", "        left_on=[\"n1_facility_id\", \"n1_item_id\"],\n", "        right_on=[\"n1_facility_id\", \"n2_item_id\"],\n", "        how=\"left\",\n", "    ),\n", "    base_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"buckets\",\n", "            \"unblocked_quantity\",\n", "            \"landing_price\",\n", "            \"avg_30_days_forecast_qty\",\n", "        ]\n", "    ].add_prefix(\"n2_\"),\n", "    on=[\"n2_facility_id\", \"n2_item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_df3 = pd.merge(\n", "    pd.merge(\n", "        backend_df2,\n", "        tea_tag_df2.rename(\n", "            columns={\n", "                \"backend_facility\": \"n2_facility_id\",\n", "                \"frontend_facility\": \"n3_facility_id\",\n", "                \"item_id\": \"n3_item_id\",\n", "            }\n", "        ),\n", "        left_on=[\"n2_facility_id\", \"n2_item_id\"],\n", "        right_on=[\"n2_facility_id\", \"n3_item_id\"],\n", "        how=\"left\",\n", "    ),\n", "    base_df[\n", "        [\n", "            \"facility_id\",\n", "            \"item_id\",\n", "            \"buckets\",\n", "            \"unblocked_quantity\",\n", "            \"landing_price\",\n", "            \"avg_30_days_forecast_qty\",\n", "        ]\n", "    ].add_prefix(\"n3_\"),\n", "    on=[\"n3_facility_id\", \"n3_item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["backend_df3.fillna({\"n3_unblocked_quantity\": 0, \"n3_avg_30_days_forecast_qty\": 0}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def backend_doi_cacl(x, node_number):\n", "    values = {\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_unblocked_net_quantity\": x[\"n\" + str(node_number) + \"_unblocked_quantity\"]\n", "        .fillna(0)\n", "        .sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_net_worth\": (\n", "            x[\"n\" + str(node_number) + \"_unblocked_quantity\"].fillna(0)\n", "            * x[\"n\" + str(node_number) + \"_landing_price\"].fillna(0)\n", "        ).sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_forecast_sales\": (\n", "            x[\"n\" + str(node_number) + \"_avg_30_days_forecast_qty\"].fillna(0)\n", "            * x[\"n\" + str(node_number) + \"_landing_price\"].fillna(0)\n", "        ).sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_forecast_quantity\": x[\"n\" + str(node_number) + \"_avg_30_days_forecast_qty\"]\n", "        .fillna(0)\n", "        .sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_unblocked_quantity_bucketX\": x[x[\"n\" + str(node_number) + \"_buckets\"] == \"X\"][\n", "            \"n\" + str(node_number) + \"_unblocked_quantity\"\n", "        ]\n", "        .fillna(0)\n", "        .sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_net_worth_bucketX\": (\n", "            x[x[\"n\" + str(node_number) + \"_buckets\"] == \"X\"][\n", "                \"n\" + str(node_number) + \"_unblocked_quantity\"\n", "            ].fillna(0)\n", "            * x[x[\"n\" + str(node_number) + \"_buckets\"] == \"X\"][\n", "                \"n\" + str(node_number) + \"_landing_price\"\n", "            ].fillna(0)\n", "        ).sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_forecast_sales_bucketX\": (\n", "            x[x[\"n\" + str(node_number) + \"_buckets\"] == \"X\"][\n", "                \"n\" + str(node_number) + \"_avg_30_days_forecast_qty\"\n", "            ].fillna(0)\n", "            * x[x[\"n\" + str(node_number) + \"_buckets\"] == \"X\"][\n", "                \"n\" + str(node_number) + \"_landing_price\"\n", "            ].fillna(0)\n", "        ).sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_forecast_quantity_bucketX\": x[x[\"n\" + str(node_number) + \"_buckets\"] == \"X\"][\n", "            \"n\" + str(node_number) + \"_avg_30_days_forecast_qty\"\n", "        ]\n", "        .fillna(0)\n", "        .sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_unblocked_quantity_bucketA\": x[x[\"n\" + str(node_number) + \"_buckets\"] == \"A\"][\n", "            \"n\" + str(node_number) + \"_unblocked_quantity\"\n", "        ]\n", "        .fillna(0)\n", "        .sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_net_worth_bucketA\": (\n", "            x[x[\"n\" + str(node_number) + \"_buckets\"] == \"A\"][\n", "                \"n\" + str(node_number) + \"_unblocked_quantity\"\n", "            ].fillna(0)\n", "            * x[x[\"n\" + str(node_number) + \"_buckets\"] == \"A\"][\n", "                \"n\" + str(node_number) + \"_landing_price\"\n", "            ].fillna(0)\n", "        ).sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_forecast_sales_bucketA\": (\n", "            x[x[\"n\" + str(node_number) + \"_buckets\"] == \"A\"][\n", "                \"n\" + str(node_number) + \"_avg_30_days_forecast_qty\"\n", "            ].fillna(0)\n", "            * x[x[\"n\" + str(node_number) + \"_buckets\"] == \"A\"][\n", "                \"n\" + str(node_number) + \"_landing_price\"\n", "            ].fillna(0)\n", "        ).sum(),\n", "        \"n\"\n", "        + str(node_number)\n", "        + \"_forecast_quantity_bucketA\": x[x[\"n\" + str(node_number) + \"_buckets\"] == \"A\"][\n", "            \"n\" + str(node_number) + \"_avg_30_days_forecast_qty\"\n", "        ]\n", "        .fillna(0)\n", "        .sum(),\n", "    }\n", "    return pd.Series(values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# backend_df3.columns\n", "# backend_df3['n'+str(3)+'_unblocked_quantity'].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# backend_df3.groupby(['n0_facility_id', 'n0_facility_name'])['n'+str(3)+'_unblocked_quantity'].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["node3_aggregated = (\n", "    backend_df3.groupby([\"store_type\", \"n0_facility_id\", \"n0_facility_name\"])\n", "    .apply(backend_doi_cacl, node_number=3)\n", "    .reset_index()\n", ")\n", "node2_aggregated = (\n", "    backend_df2.groupby([\"store_type\", \"n0_facility_id\", \"n0_facility_name\"])\n", "    .apply(backend_doi_cacl, node_number=2)\n", "    .reset_index()\n", ")\n", "node1_aggregated = (\n", "    backend_df1.groupby([\"store_type\", \"n0_facility_id\", \"n0_facility_name\"])\n", "    .apply(backend_doi_cacl, node_number=1)\n", "    .reset_index()\n", ")\n", "node0_aggregated = (\n", "    backend_df0.groupby([\"store_type\", \"n0_facility_id\", \"n0_facility_name\"])\n", "    .apply(backend_doi_cacl, node_number=0)\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["node0_aggregated.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.merge(\n", "    pd.merge(\n", "        pd.merge(\n", "            node0_aggregated[[\"n0_facility_id\"]],\n", "            node1_aggregated.drop([\"n0_facility_name\", \"store_type\"], axis=1),\n", "            on=[\"n0_facility_id\"],\n", "            how=\"left\",\n", "        ),\n", "        node2_aggregated.drop([\"n0_facility_name\", \"store_type\"], axis=1),\n", "        on=[\"n0_facility_id\"],\n", "        how=\"left\",\n", "    ),\n", "    node3_aggregated.drop([\"n0_facility_name\", \"store_type\"], axis=1),\n", "    on=[\"n0_facility_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"frontend_net_worth\"] = df[\"n1_net_worth\"] + df[\"n2_net_worth\"] + df[\"n3_net_worth\"]\n", "df[\"frontend_net_unblocked_quantity\"] = (\n", "    df[\"n1_unblocked_net_quantity\"]\n", "    + df[\"n2_unblocked_net_quantity\"]\n", "    + df[\"n2_unblocked_net_quantity\"]\n", ")\n", "df[\"frontend_forecast_sales\"] = (\n", "    df[\"n1_forecast_sales\"] + df[\"n2_forecast_sales\"] + df[\"n3_forecast_sales\"]\n", ")\n", "df[\"frontend_forecast_quantity\"] = (\n", "    df[\"n1_forecast_quantity\"] + df[\"n2_forecast_quantity\"] + df[\"n3_forecast_quantity\"]\n", ")\n", "\n", "df[\"frontend_net_worth_X\"] = (\n", "    df[\"n1_net_worth_bucketX\"] + df[\"n2_net_worth_bucketX\"] + df[\"n3_net_worth_bucketX\"]\n", ")\n", "df[\"frontend_net_unblocked_quantity_X\"] = (\n", "    df[\"n1_unblocked_quantity_bucketX\"]\n", "    + df[\"n2_unblocked_quantity_bucketX\"]\n", "    + df[\"n2_unblocked_quantity_bucketX\"]\n", ")\n", "df[\"frontend_forecast_sales_X\"] = (\n", "    df[\"n1_forecast_sales_bucketX\"]\n", "    + df[\"n2_forecast_sales_bucketX\"]\n", "    + df[\"n3_forecast_sales_bucketX\"]\n", ")\n", "df[\"frontend_forecast_quantity_X\"] = (\n", "    df[\"n1_forecast_quantity_bucketX\"]\n", "    + df[\"n2_forecast_quantity_bucketX\"]\n", "    + df[\"n3_forecast_quantity_bucketX\"]\n", ")\n", "\n", "df[\"frontend_net_worth_A\"] = (\n", "    df[\"n1_net_worth_bucketA\"] + df[\"n2_net_worth_bucketA\"] + df[\"n3_net_worth_bucketA\"]\n", ")\n", "df[\"frontend_net_unblocked_quantity_A\"] = (\n", "    df[\"n1_unblocked_quantity_bucketA\"]\n", "    + df[\"n2_unblocked_quantity_bucketA\"]\n", "    + df[\"n2_unblocked_quantity_bucketA\"]\n", ")\n", "df[\"frontend_forecast_sales_A\"] = (\n", "    df[\"n1_forecast_sales_bucketA\"]\n", "    + df[\"n2_forecast_sales_bucketA\"]\n", "    + df[\"n3_forecast_sales_bucketA\"]\n", ")\n", "df[\"frontend_forecast_quantity_A\"] = (\n", "    df[\"n1_forecast_quantity_bucketA\"]\n", "    + df[\"n2_forecast_quantity_bucketA\"]\n", "    + df[\"n3_forecast_quantity_bucketA\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_frame = base_df[[\"zone\", \"facility_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frontend_frame = df[\n", "    [\n", "        \"n0_facility_id\",\n", "        \"frontend_net_worth\",\n", "        \"frontend_net_unblocked_quantity\",\n", "        \"frontend_forecast_sales\",\n", "        \"frontend_forecast_quantity\",\n", "        \"frontend_net_worth_X\",\n", "        \"frontend_net_unblocked_quantity_X\",\n", "        \"frontend_forecast_sales_X\",\n", "        \"frontend_forecast_quantity_X\",\n", "        \"frontend_net_worth_A\",\n", "        \"frontend_net_unblocked_quantity_A\",\n", "        \"frontend_forecast_sales_A\",\n", "        \"frontend_forecast_quantity_A\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame = pd.merge(\n", "    zone_frame,\n", "    pd.merge(node0_aggregated, frontend_frame, on=\"n0_facility_id\", how=\"left\"),\n", "    left_on=[\"facility_id\"],\n", "    right_on=[\"n0_facility_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# exlusion_list=pb.from_sheets(sheetid='1xj9YGix9Q_PUMGWtZKXEeoel8AY2jQ-ctoZgzFB8z6A'\n", "#               ,sheetname='facility_exclusion_list')\n", "# master_frame= master_frame[~master_frame['n0_facility_id'].isin(list(exlusion_list['facility_id']))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame[\"store_type\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame[\"zone\"] = pd.Categorical(master_frame[\"zone\"], [\"North\", \"East\", \"South\", \"West\"])\n", "master_frame.sort_values(by=[\"zone\"], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame_facility = master_frame[\n", "    [\n", "        \"zone\",\n", "        \"store_type\",\n", "        \"facility_id\",\n", "        \"n0_facility_name\",\n", "        \"n0_net_worth\",\n", "        \"n0_net_worth_bucketA\",\n", "        \"n0_net_worth_bucketX\",\n", "        \"n0_forecast_sales\",\n", "        \"n0_forecast_sales_bucketA\",\n", "        \"n0_forecast_sales_bucketX\",\n", "        \"frontend_net_worth\",\n", "        \"frontend_net_worth_A\",\n", "        \"frontend_net_worth_X\",\n", "        \"frontend_forecast_sales\",\n", "        \"frontend_forecast_sales_A\",\n", "        \"frontend_forecast_sales_X\",\n", "    ]\n", "    # +\n", "    #  [       'n0_unblocked_net_quantity',\n", "    #        'n0_unblocked_quantity_bucketA',\n", "    #        'n0_unblocked_quantity_bucketX',\n", "    #        'n0_forecast_quantity',\n", "    #        'n0_forecast_quantity_bucketA',\n", "    #        'n0_forecast_quantity_bucketX',\n", "    #        'frontend_net_unblocked_quantity',\n", "    #        'frontend_net_unblocked_quantity_A',\n", "    #        'frontend_net_unblocked_quantity_X',\n", "    #        'frontend_forecast_quantity',\n", "    #        'frontend_forecast_quantity_A',\n", "    #        'frontend_forecast_quantity_X']\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(master_frame_facility.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame_zone = (\n", "    master_frame.groupby([\"zone\", \"store_type\"])[\n", "        \"n0_net_worth\",\n", "        \"n0_net_worth_bucketA\",\n", "        \"n0_net_worth_bucketX\",\n", "        \"n0_forecast_sales\",\n", "        \"n0_forecast_sales_bucketA\",\n", "        \"n0_forecast_sales_bucketX\",\n", "        \"frontend_net_worth\",\n", "        \"frontend_net_worth_A\",\n", "        \"frontend_net_worth_X\",\n", "        \"frontend_forecast_sales\",\n", "        \"frontend_forecast_sales_A\",\n", "        \"frontend_forecast_sales_X\",\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "\n", "master_frame_zone[\"facility_id\"] = \"\"\n", "master_frame_zone[\"n0_facility_name\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(master_frame_zone.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame[\"pan_india\"] = \"Pan India\"\n", "\n", "master_frame_pan_india = (\n", "    master_frame.groupby([\"pan_india\", \"store_type\"])[\n", "        \"n0_net_worth\",\n", "        \"n0_net_worth_bucketA\",\n", "        \"n0_net_worth_bucketX\",\n", "        \"n0_forecast_sales\",\n", "        \"n0_forecast_sales_bucketA\",\n", "        \"n0_forecast_sales_bucketX\",\n", "        \"frontend_net_worth\",\n", "        \"frontend_net_worth_A\",\n", "        \"frontend_net_worth_X\",\n", "        \"frontend_forecast_sales\",\n", "        \"frontend_forecast_sales_A\",\n", "        \"frontend_forecast_sales_X\",\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame_pan_india.rename(columns={\"pan_india\": \"zone\"}, inplace=True)\n", "master_frame_pan_india[\"facility_id\"] = \"\"\n", "master_frame_pan_india[\"n0_facility_name\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(master_frame_pan_india.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame = pd.concat([master_frame_pan_india, master_frame_zone, master_frame_facility])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame = master_frame[\n", "    [\n", "        \"zone\",\n", "        \"store_type\",\n", "        \"facility_id\",\n", "        \"n0_facility_name\",\n", "        \"n0_net_worth\",\n", "        \"n0_net_worth_bucketA\",\n", "        \"n0_net_worth_bucketX\",\n", "        \"n0_forecast_sales\",\n", "        \"n0_forecast_sales_bucketA\",\n", "        \"n0_forecast_sales_bucketX\",\n", "        \"frontend_net_worth\",\n", "        \"frontend_net_worth_A\",\n", "        \"frontend_net_worth_X\",\n", "        \"frontend_forecast_sales\",\n", "        \"frontend_forecast_sales_A\",\n", "        \"frontend_forecast_sales_X\",\n", "    ]\n", "    # +\n", "    #  [       'n0_unblocked_net_quantity',\n", "    #        'n0_unblocked_quantity_bucketA',\n", "    #        'n0_unblocked_quantity_bucketX',\n", "    #        'n0_forecast_quantity',\n", "    #        'n0_forecast_quantity_bucketA',\n", "    #        'n0_forecast_quantity_bucketX',\n", "    #        'frontend_net_unblocked_quantity',\n", "    #        'frontend_net_unblocked_quantity_A',\n", "    #        'frontend_net_unblocked_quantity_X',\n", "    #        'frontend_forecast_quantity',\n", "    #        'frontend_forecast_quantity_A',\n", "    #        'frontend_forecast_quantity_X']\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# master_frame[master_frame['store_type']=='NDD']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# master_frame[master_frame[\"store_type\"].isin([\"NDD\"])].drop(\n", "#     \"store_type\", axis=1\n", "# ).to_csv(\"validate_doi_data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    master_frame[master_frame[\"store_type\"].isin([\"NDD\"])].drop(\"store_type\", axis=1),\n", "    sheetid=\"1PvhbEQEuO8QcwKlHRhb2R_RFeDAy3CTafhKLNdjMm30\",\n", "    sheetname=\"NewDOI_raw_data\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_sheets(\n", "    master_frame[master_frame[\"store_type\"].isin([\"DarkStore\"])].drop(\"store_type\", axis=1),\n", "    sheetid=\"1PvhbEQEuO8QcwKlHRhb2R_RFeDAy3CTafhKLNdjMm30\",\n", "    sheetname=\"NewDOI_raw_data_darkstore\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame[\"facility_doi\"] = np.where(\n", "    master_frame[\"n0_forecast_sales\"].fillna(0) + master_frame[\"frontend_forecast_sales\"].fillna(0)\n", "    > 0,\n", "    (master_frame[\"n0_net_worth\"])\n", "    / (\n", "        master_frame[\"n0_forecast_sales\"].fillna(0)\n", "        + master_frame[\"frontend_forecast_sales\"].fillna(0)\n", "    ),\n", "    np.nan,\n", ")\n", "\n", "\n", "master_frame[\"ecosystem_doi\"] = np.where(\n", "    master_frame[\"n0_forecast_sales\"].fillna(0) + master_frame[\"frontend_forecast_sales\"].fillna(0)\n", "    > 0,\n", "    (master_frame[\"n0_net_worth\"] + master_frame[\"frontend_net_worth\"])\n", "    / (\n", "        master_frame[\"n0_forecast_sales\"].fillna(0)\n", "        + master_frame[\"frontend_forecast_sales\"].fillna(0)\n", "    ),\n", "    np.nan,\n", ")\n", "\n", "\n", "master_frame[\"facility_doi_X\"] = np.where(\n", "    master_frame[\"n0_forecast_sales_bucketX\"].fillna(0)\n", "    + master_frame[\"frontend_forecast_sales_X\"].fillna(0)\n", "    > 0,\n", "    (master_frame[\"n0_net_worth_bucketX\"])\n", "    / (\n", "        master_frame[\"n0_forecast_sales_bucketX\"].fillna(0)\n", "        + master_frame[\"frontend_forecast_sales_X\"].fillna(0)\n", "    ),\n", "    np.nan,\n", ")\n", "\n", "\n", "master_frame[\"ecosystem_doi_X\"] = np.where(\n", "    master_frame[\"n0_forecast_sales_bucketX\"].fillna(0)\n", "    + master_frame[\"frontend_forecast_sales_X\"].fillna(0)\n", "    > 0,\n", "    (master_frame[\"n0_net_worth_bucketX\"] + master_frame[\"frontend_net_worth_X\"])\n", "    / (\n", "        master_frame[\"n0_forecast_sales_bucketX\"].fillna(0)\n", "        + master_frame[\"frontend_forecast_sales_X\"].fillna(0)\n", "    ),\n", "    np.nan,\n", ")\n", "\n", "\n", "master_frame[\"facility_doi_A\"] = np.where(\n", "    master_frame[\"n0_forecast_sales_bucketA\"].fillna(0)\n", "    + master_frame[\"frontend_forecast_sales_A\"].fillna(0)\n", "    > 0,\n", "    (master_frame[\"n0_net_worth_bucketA\"])\n", "    / (\n", "        master_frame[\"n0_forecast_sales_bucketA\"].fillna(0)\n", "        + master_frame[\"frontend_forecast_sales_A\"].fillna(0)\n", "    ),\n", "    np.nan,\n", ")\n", "\n", "\n", "master_frame[\"ecosystem_doi_A\"] = np.where(\n", "    master_frame[\"n0_forecast_sales_bucketA\"].fillna(0)\n", "    + master_frame[\"frontend_forecast_sales_A\"].fillna(0)\n", "    > 0,\n", "    (master_frame[\"n0_net_worth_bucketA\"] + master_frame[\"frontend_net_worth_A\"])\n", "    / (\n", "        master_frame[\"n0_forecast_sales_bucketA\"].fillna(0)\n", "        + master_frame[\"frontend_forecast_sales_A\"].fillna(0)\n", "    ),\n", "    np.nan,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame_ndd = master_frame[master_frame[\"store_type\"].isin([\"NDD\"])].reset_index()\n", "# master_frame_ds= master_frame[master_frame[\"store_type\"].isin([\"DarkStore\"])].reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame_ndd_email = master_frame_ndd[\n", "    [\n", "        \"zone\",\n", "        \"facility_id\",\n", "        \"n0_facility_name\",\n", "        \"facility_doi\",\n", "        \"ecosystem_doi\",\n", "        \"facility_doi_X\",\n", "        \"ecosystem_doi_X\",\n", "        \"facility_doi_A\",\n", "        \"ecosystem_doi_A\",\n", "    ]\n", "]\n", "# master_frame_ds_email= master_frame_ds[['zone', 'facility_id','n0_facility_name' ,'facility_doi', 'ecosystem_doi', 'facility_doi_X', 'ecosystem_doi_X', 'facility_doi_A', 'ecosystem_doi_A']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["master_frame_ndd_email = master_frame_ndd_email.round()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ndd_summary = master_frame_ndd_email.to_dict(orient=\"rows\")\n", "# darkstore_summary= master_frame_ds_email.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email_list_df = pb.from_sheets(\n", "    \"1jqAHMcON3SoTBj2HtBYwQqUhc-U6KejAem2mthPfv_c\", sheetname=\"new_doi_mail\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import os\n", "from jinja2 import Template\n", "\n", "tday = (datetime.now()).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from_email = \"<EMAIL>\"\n", "to_email = list(email_list_df[\"email_id\"])\n", "\n", "subject = \"DOI report \" + tday\n", "\n", "# cwd = \"/home/<USER>/airflow-dags/daggers/povms/doi/report/new_doi_facility_eco\"\n", "cwd = \"/usr/local/airflow/dags/repo/dags/povms/doi/report/new_doi_facility_eco\"\n", "\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"new_doi_email_template.html\",\n", "    ),\n", "    \"r\",\n", ") as f:\n", "    t = Template(f.read())\n", "\n", "rendered = t.render(\n", "    ndd_summary=ndd_summary,\n", ")\n", "\n", "pb.send_email(from_email, to_email, subject, html_content=rendered)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}