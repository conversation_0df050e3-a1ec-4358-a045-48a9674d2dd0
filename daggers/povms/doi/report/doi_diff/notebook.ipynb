{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["end_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "start_date = (datetime.now() - timedelta(days=2)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con1 = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select count(*) total_count,sum(actual_quantity) as actual_quantity from reports.reports_item_inventory_snapshot\n", "where date(snapshot_datetime) =current_date-1\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_prod = pandas.read_sql_query(sql=query, con=con1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_prod"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if ims_prod.total_count.sum() == 0:\n", "    sender = \"<EMAIL>\"\n", "    to = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = \"[DOI]Alert-'reports.reports_item_inventory_snapshot' table is not populated\"\n", "    message_text = \"\"\"\n", "\n", "    Hi,\n", "\n", "    This is the alert related to DOI table, as prod table is not populated. \n", "    that's why we are not able sent today's mail and not able to populate table as well.\n", "    \n", "    @Debadatta Mishra \n", "    \n", "    \n", "    Thanks and Regards,\n", "    <PERSON><PERSON>\n", "    \n", "    \"\"\"\n", "\n", "    print(\"Sending message\")\n", "\n", "    pb.send_email(\n", "        sender,\n", "        to,\n", "        subject,\n", "        message_text,\n", "        #                files=['data/output/active_inactive_alert.csv'],\n", "        dryrun=False,\n", "        cc=None,\n", "        mime_subtype=\"mixed\",\n", "        mime_charset=\"utf-8\",\n", "    )\n", "else:\n", "\n", "    print(\"no mailer sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select count(*) total_count from ims.ims_order_actuals \n", "where created_at between '{start_date}' and '{end_date}'\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_sales = pandas.read_sql_query(sql=query, con=con1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_sales.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["red_query = \"\"\"\n", "select count(*) redshift_count from consumer.ims_ims_order_actuals \n", "where created_at between '{start_date}' and '{end_date}'\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_redshift = pandas.read_sql_query(sql=red_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_redshift.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_diff = ims_sales.total_count.sum() == ims_redshift.redshift_count.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if ims_diff == False:\n", "    sender = \"<EMAIL>\"\n", "    to = [\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "        \"<EMAIL>\",\n", "    ]\n", "    subject = (\n", "        \"[DOI]Alert-difference in ims_order_actuals and replica table consumer.ims_ims_order_actuals for \"\n", "        + start_date\n", "    )\n", "    message_text = \"\"\"\n", "\n", "    Hi,\n", "     \n", "   This is the alert related to DOI table ,Difference in ims_order_actuals and replica table consumer.ims_ims_order_actuals for T-1 day.\n", "   \n", "   So,DOI Table will not populate for today.\n", "   \n", "   @Deepu please look into this issue.\n", "   \n", "   Thanks and Regards,\n", "   <PERSON><PERSON>\n", "  \n", "    \"\"\"\n", "\n", "    print(\"Sending message\")\n", "\n", "    pb.send_email(\n", "        sender,\n", "        to,\n", "        subject,\n", "        message_text,\n", "        #                files=['data/output/active_inactive_alert.csv'],\n", "        dryrun=False,\n", "        cc=None,\n", "        mime_subtype=\"mixed\",\n", "        mime_charset=\"utf-8\",\n", "    )\n", "\n", "else:\n", "\n", "    print(\"no mailer sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 4}