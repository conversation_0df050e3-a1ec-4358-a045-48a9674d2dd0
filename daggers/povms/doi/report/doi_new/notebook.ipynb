{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas as pd\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import warnings\n", "import pencilbox as pb\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Report at Facility level"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inventory worth"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with\n", "\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "lp_treatment_tbl as\n", "(\n", "select \n", "a.item_id,\n", "outlet_id,\n", "b.warehouse_id,\n", "facility_id,\n", "landing_price,\n", "row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "row_number() over (partition by a.item_id order by dt_ist desc) as row_rank1\n", "from weighted_landing_price a\n", "left join (select * from  lake_retail.warehouse_outlet_mapping  where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "left join (select * from  lake_retail.view_console_outlet  where active = true) c on b.warehouse_id = c.id\n", "where landing_price is not null\n", "),\n", "\n", "availability as\n", "(\n", "select distinct * from \n", "(select e.zone,\n", "a.facility_id,\n", "a.facility_name,\n", "(case when facility_name ilike '%% es%%' then 'Dark' when facility_name ilike '%%dark%%' then 'Franchise' else 'NDD' end) as store_type,\n", "a.item_id,\n", "date(order_date) as order_date,\n", "availability,\n", "app_live,\n", "inv_flag,\n", "l0,\n", "actual_quantity,\n", "coalesce(actual_quantity,0) - coalesce(blocked_quantity,0) as unblocked_quantity,\n", "case when is_gb = 'TRUE' then 1 else 0 end as gb_flag,\n", "case when bucket_x = 'Y' then 'X' when buckets = 'A' then 'A' else 'B' end as buckets,\n", "case when l0 in ('Home Furnishing and Decor','Home Improvement and Accessories','Kitchen & Dining needs','Home Appliances',\n", "'Home & Kitchen', 'Furnishing & Home Needs', 'Home and Kitchen','Kitchen and Dining Needs', \n", "'Vegetables & Fruits', 'Specials','Home Appliances','Fashion and Lifestyle', 'Best Value')\n", "then 1 else 0 end as gm_flag,\n", "coalesce(coalesce(b.landing_price,c.landing_price),d.variant_mrp*0.7000) as landing_price,\n", "row_number() over (partition by a.item_id, a.facility_id, date(order_date) order by order_date desc) as row_rank\n", "from(\n", "SELECT r.*\n", "FROM consumer.rpc_daily_availability r\n", "INNER JOIN\n", "  (SELECT facility_id,\n", "          date(order_date) AS order_date,\n", "          max(order_date) AS maxi\n", "   FROM consumer.rpc_daily_availability\n", "   where date(order_date) between current_date- interval'30 days' and current_date- interval'1 days'\n", "   GROUP BY 1,\n", "            2) x1 ON r.facility_id=x1.facility_id\n", "AND x1.maxi=r.order_date )a\n", "left join zone_tbl_treatment e on a.facility_id = e.facility_id\n", "left join (select * from lp_treatment_tbl where row_rank = 1) b on a.facility_id = b.facility_id  AND a.item_id = b.item_id\n", "left join (select * from lp_treatment_tbl where row_rank1 = 1) c on a.item_id = c.item_id\n", "left join (select * from item_level_info where row_rank = 1) d on a.item_id = d.item_id\n", "where date(order_date) between current_date- interval'30 days' and current_date- interval'1 days'\n", "\n", ") x1\n", "where row_rank=1\n", "and store_type != 'Franchise'\n", "),\n", "\n", "fe_be_mapping as (SELECT DISTINCT\n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND f1.name not ilike '%%dark%%'),\n", "\n", "summary as (select zone,\n", "item_id,\n", "facility_id,\n", "facility_name,\n", "sum(case when order_date=current_date-1 then (actual_quantity*landing_price) end) as net_worth_eod,\n", "sum(case when (order_date<=current_date- interval'0 days' and order_date >=current_date- interval'30 days') then (actual_quantity*landing_price)end) as net_worth_last_30days\n", "\n", "FROM availability a\n", "GROUP BY 1,2,3,4)\n", "\n", "\n", "select zone, facility_id, facility_name, sum(net_worth_eod+frontend_net_worth_eod) as net_worth_eod, sum(net_worth_last_30days+frontend_net_worth_last_30days) as net_worth_last_30days\n", "from (select item_id, zone, facility_id, facility_name, coalesce(net_worth_eod,0) as net_worth_eod, coalesce(net_worth_last_30days,0) as net_worth_last_30days, sum(frontend_net_worth_eod) as frontend_net_worth_eod, sum(frontend_net_worth_last_30days) as frontend_net_worth_last_30days\n", "    from (SELECT a.*, mp.frontend_facility_id, \n", "        (case when b.net_worth_eod is null then 0 else b.net_worth_eod end) as frontend_net_worth_eod,\n", "        (case when b.net_worth_last_30days is null then 0 else b.net_worth_last_30days end) as frontend_net_worth_last_30days\n", "        FROM summary a\n", "        LEFT JOIN fe_be_mapping mp ON a.facility_id = mp.backend_facility_id AND a.item_id = mp.item_id\n", "        LEFT JOIN summary b ON mp.frontend_facility_id = b.facility_id AND mp.item_id = b.item_id)\n", "    group by 1,2,3,4,5,6)\n", "group by 1,2,3\n", "\"\"\"\n", "\n", "inventory_worth = pd.read_sql(query, redshift)\n", "inventory_worth = inventory_worth[inventory_worth[\"net_worth_eod\"] > 0]\n", "inventory_worth.dropna(inplace=True)\n", "inventory_worth.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH it_pd AS\n", "  (SELECT DISTINCT item_id,\n", "                   product_id,\n", "                   multiplier,\n", "                   updated_on,\n", "                   max(updated_on) OVER (PARTITION BY product_id) AS last_updated\n", "   FROM it_pd_log),\n", "     item_level_info AS\n", "  (SELECT item_id,\n", "          variant_mrp,\n", "          row_number() OVER (PARTITION BY item_id\n", "                             ORDER BY updated_at DESC) AS row_rank\n", "   FROM lake_rpc.product_product),\n", "     mapping AS\n", "  (SELECT DISTINCT item_id,\n", "                   product_id,\n", "                   coalesce(multiplier,1) AS multiplier\n", "   FROM it_pd\n", "   WHERE last_updated = updated_on ),\n", "     orders AS\n", "  (SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "                   date(convert_timezone('Asia/Kolkata',a.install_ts)) AS checkout_date,\n", "                   o.facility_id,\n", "                   o.facility_name,\n", "                   (case when o.facility_name ilike '%% es%%' then 'Dark' when o.facility_name ilike '%%dark%%' then 'Franchise' else 'NDD' end) as store_type,\n", "                   a.id AS ancestor,\n", "                   a.current_status,\n", "                   oi.product_id,\n", "                   c.item_id,\n", "                   oi.selling_price,\n", "                   d.variant_mrp,\n", "                   oi.quantity*multiplier AS quantity,\n", "                   multiplier,\n", "                   sum(d.variant_mrp) OVER (PARTITION BY a.id,\n", "                                                         oi.product_id) AS total_mrp\n", "   FROM lake_oms_bifrost.oms_order a\n", "   INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM lake_oms_bifrost.oms_order_item\n", "      WHERE freebie_id IS NULL\n", "        AND date(install_ts + interval '5.5 Hours') BETWEEN CURRENT_DATE- interval'30 days' AND CURRENT_DATE- interval'1 days') oi ON a.id = oi.order_id\n", "   LEFT JOIN mapping c ON oi.product_id = c.product_id\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM item_level_info\n", "      WHERE row_rank = 1) d ON c.item_id = d.item_id\n", "   INNER JOIN\n", "     (SELECT DISTINCT ancestor,\n", "                      facility_id,\n", "                      f.name AS facility_name\n", "      FROM lake_ims.ims_order_details o\n", "      INNER JOIN lake_retail.console_outlet ro ON ro.id=o.outlet\n", "      INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "      WHERE date(o.created_at) BETWEEN CURRENT_DATE- interval'32 days' AND CURRENT_DATE- interval'0 days') o ON o.ancestor=a.id\n", "   WHERE date(convert_timezone('Asia/Kolkata',a.install_ts)) BETWEEN CURRENT_DATE- interval'30 days' AND CURRENT_DATE- interval'1 days'\n", "     AND a.direction ='FORWARD'\n", "     AND b.city_name NOT IN ('Not in service area',\n", "                             'Hapur',\n", "                             'Test city')\n", "     AND b.city_name NOT ILIKE '%%b2b%%'\n", "     AND a.current_status <> 'CANCELLED'\n", "     AND a.type IN ('RetailForwardOrder',\n", "                    'InternalForwardOrder')\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            11,\n", "            12,\n", "            13),\n", "     merchant_and_store_join AS\n", "  (SELECT a.*,\n", "          selling_price*variant_mrp*1.000/(total_mrp*multiplier) AS item_selling_price\n", "   FROM orders a\n", "   WHERE store_type != 'Franchise'),\n", "   \n", "fe_be_mapping as (SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND f1.name not ilike '%%dark%%'),\n", "\n", "summary as (SELECT \n", "        item_id,\n", "        facility_id,\n", "       facility_name,\n", "       sum(item_selling_price*quantity) AS sales\n", "FROM merchant_and_store_join\n", "GROUP BY 1,\n", "         2,\n", "         3),\n", "         \n", "fin as (select a.*, mp.backend_facility_id, mp.backend_facility_name, coalesce(b.sales,0) as backend_sales\n", "from summary a\n", "left join fe_be_mapping mp on a.facility_id = mp.frontend_facility_id and a.item_id = mp.item_id\n", "left join summary b on b.facility_id = mp.backend_facility_id and b.item_id = mp.item_id\n", "\n", "where a.item_id is not null)\n", "\n", "\n", "select facility_id, facility_name, sum(sales) as sales\n", "from fin\n", "group by 1,2\n", "\n", "union\n", "\n", "select backend_facility_id, backend_facility_name, sum(sales)\n", "from fin\n", "where backend_facility_id is not null\n", "group by 1,2\n", "\n", "\n", "\"\"\"\n", "\n", "\n", "sales = pd.read_sql(query, redshift)\n", "sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales = sales.groupby([\"facility_id\", \"facility_name\"])[\"sales\"].sum().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility Forecast Sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_30_days_forecast_query = \"\"\"\n", "\n", "with \n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "lp_treatment_tbl as\n", "(\n", "select \n", "a.item_id,\n", "outlet_id,\n", "b.warehouse_id,\n", "facility_id,\n", "landing_price,\n", "row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "row_number() over (partition by a.item_id order by dt_ist desc) as row_rank1\n", "from weighted_landing_price a\n", "left join (select * from  lake_retail.warehouse_outlet_mapping  where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "left join (select * from  lake_retail.console_outlet where active = true) c on b.warehouse_id = c.id\n", "where landing_price is not null\n", "),\n", "\n", "forecast as (select item_id, facility_id, avg(forecast_quantity) as avg_30_days_forecast_qty from\n", "    (select item_id, facility_id, date, sum(consumption) as forecast_quantity from(\n", "        (select item_id, outlet_id, date, consumption, active, create_ts, update_ts from(\n", "            select item_id, outlet_id, date, consumption, active, created_at as create_ts, updated_at as update_ts,\n", "            rank() over (partition by item_id, outlet_id, date order by updated_at desc) as rnk\n", "            from lake_snorlax.date_wise_consumption\n", "            where date between current_date + interval '1 days' and current_date + interval '30 days')\n", "        where rnk = 1) a\n", "        left join\n", "        (select id, facility_id from lake_retail.console_outlet) b\n", "        on a.outlet_id = b.id\n", "    )\n", "    group by 1,2,3)\n", "group by 1,2),\n", "\n", "fe_be_mapping as (SELECT DISTINCT\n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND f1.name not ilike '%%dark%%'),\n", "\n", "summary as (select item_id, facility_id, sum(avg_30_days_forecast_qty*landing_price) as avg_30_days_forecast_sales from\n", "    (select a.item_id, a.facility_id,\n", "    coalesce(coalesce(b.landing_price,c.landing_price),d.variant_mrp*0.7000) as landing_price,\n", "    avg_30_days_forecast_qty\n", "    from forecast a\n", "    inner join lake_rpc.product_facility_master_assortment pfma on a.item_id = pfma.item_id and a.facility_id = pfma.facility_id and pfma.master_assortment_substate_id=1\n", "    left join (select * from lp_treatment_tbl where row_rank = 1) b on a.facility_id = b.facility_id  AND a.item_id = b.item_id\n", "    left join (select * from lp_treatment_tbl where row_rank1 = 1) c on a.item_id = c.item_id\n", "    left join (select * from item_level_info where row_rank = 1) d on a.item_id = d.item_id)\n", "group by 1,2),\n", "\n", "fin as (select a.*, mp.backend_facility_id, coalesce(b.avg_30_days_forecast_sales,0) as backend_avg_30_days_forecast_sales\n", "from summary a\n", "left join fe_be_mapping mp on a.facility_id = mp.frontend_facility_id and a.item_id = mp.item_id\n", "left join summary b on b.facility_id = mp.backend_facility_id and b.item_id = mp.item_id\n", "\n", "where a.item_id is not null)\n", "\n", "\n", "select facility_id, sum(avg_30_days_forecast_sales) as avg_30_days_forecast_sales\n", "from fin\n", "group by 1\n", "\n", "union\n", "\n", "select backend_facility_id, sum(avg_30_days_forecast_sales)\n", "from fin\n", "where backend_facility_id is not null\n", "group by 1\n", "\n", "\"\"\"\n", "\n", "next_30_days_forecast = pd.read_sql(next_30_days_forecast_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_30_days_forecast = (\n", "    next_30_days_forecast.groupby([\"facility_id\"])[\"avg_30_days_forecast_sales\"].sum().reset_index()\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merging inventory with sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_mailer_data1 = inventory_worth.merge(sales, on=[\"facility_id\", \"facility_name\"], how=\"left\")\n", "\n", "doi_mailer_data = doi_mailer_data1.merge(next_30_days_forecast, on=[\"facility_id\"], how=\"left\")\n", "doi_mailer_data = doi_mailer_data.fillna(0)\n", "\n", "doi_mailer_data.sort_values(by=[\"net_worth_eod\"], ascending=False, inplace=True)\n", "doi_mailer_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility identifier"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["names = doi_mailer_data.facility_name.unique()\n", "facility_mapping = pd.DataFrame({\"facility_name\": names})\n", "for i in range(0, len(facility_mapping)):\n", "    facility_mapping.loc[i, \"Facility\"] = (\n", "        facility_mapping.loc[i, \"facility_name\"]\n", "        .replace(\"Super Store \", \"\")\n", "        .replace(\"Warehouse\", \"\")\n", "        .replace(\" - \", \"\")\n", "    )\n", "\n", "doi_facility = doi_mailer_data.merge(facility_mapping, on=[\"facility_name\"], how=\"inner\")\n", "\n", "doi_facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_facility = doi_facility[\n", "    ~doi_facility.facility_name.isin(\n", "        [\n", "            \"Super Store Delhi P3 - Warehouse\",\n", "            \"Super Store Chennai - Warehouse\",\n", "            \"Super Store Delhi P1 - Warehouse\",\n", "            \"<PERSON><PERSON><PERSON> - Kolkata\",\n", "            \"Delhi Warehouse Bulk Bamnoli\",\n", "            \"Bulk NCR PL Warehouse\",\n", "        ]\n", "    )\n", "]\n", "\n", "\n", "doi_facility[\"facility_name\"] = doi_facility[\"facility_name\"].astype(\"str\")\n", "doi_facility.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Facility Identification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def facility_identification(x):\n", "    if \"kundli\" in x[\"facility_name\"].lower():\n", "        return \"other\"\n", "\n", "    elif \"es\" in x[\"facility_name\"].lower():\n", "        return \"Dark_store\"\n", "    elif \"dark\" in x[\"facility_name\"].lower():\n", "        return \"Franchise\"\n", "    elif x[\"facility_id\"] == 29:\n", "        return \"Dark_store\"\n", "    else:\n", "        return \"NDD\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_facility[\"facility_type\"] = doi_facility.apply(facility_identification, axis=1)\n", "doi_facility.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### NDD Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_ndd = doi_facility[doi_facility[\"facility_type\"] == \"NDD\"]\n", "\n", "doi_full_facility_ndd = doi_full_facility_ndd[\n", "    ~doi_full_facility_ndd.facility_name.isin(\n", "        [\n", "            \"Super Store Delhi P3 - Warehouse\",\n", "            \"Super Store Chennai - Warehouse\",\n", "            \"Super Store Delhi P1 - Warehouse\",\n", "            \"<PERSON><PERSON><PERSON> - Kolkata\",\n", "            \"Delhi Warehouse Bulk Bamnoli\",\n", "            \"Bulk NCR PL Warehouse\",\n", "        ]\n", "    )\n", "]\n", "\n", "doi_full_facility_ndd[\"doi\"] = (\n", "    doi_full_facility_ndd.net_worth_last_30days / doi_full_facility_ndd.sales\n", ")\n", "doi_full_facility_ndd[\"forward_looking_doi\"] = doi_full_facility_ndd.net_worth_last_30days / (\n", "    doi_full_facility_ndd.avg_30_days_forecast_sales * 30\n", ")\n", "\n", "doi_full_facility_ndd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Changing Datatypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_ndd[\"doi\"] = doi_full_facility_ndd.doi.astype(float)\n", "doi_full_facility_ndd[\"forward_looking_doi\"] = doi_full_facility_ndd.forward_looking_doi.astype(\n", "    float\n", ")\n", "doi_full_facility_ndd[\"net_worth_eod\"] = doi_full_facility_ndd.net_worth_eod.astype(float)\n", "doi_full_facility_ndd[\"sales\"] = doi_full_facility_ndd.sales.astype(float)\n", "doi_full_facility_ndd[\"net_worth_last_30days\"] = doi_full_facility_ndd.net_worth_last_30days.astype(\n", "    float\n", ")\n", "doi_full_facility_ndd[\n", "    \"avg_30_days_forecast_sales\"\n", "] = doi_full_facility_ndd.avg_30_days_forecast_sales.astype(float)\n", "\n", "doi_full_facility_ndd.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PAN India NDD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_ndd = doi_full_facility_ndd[\n", "    [\n", "        \"facility_id\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"Facility\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "\n", "pan_india_ndd[\"Facility\"] = \"Pan India\"\n", "\n", "pan_india_rollup_ndd = round(\n", "    pan_india_ndd.groupby([\"Facility\"]).agg(\n", "        {\n", "            \"net_worth_eod\": \"sum\",\n", "            \"net_worth_last_30days\": \"sum\",\n", "            \"sales\": \"sum\",\n", "            \"avg_30_days_forecast_sales\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "pan_india_rollup_ndd[\"doi\"] = round(\n", "    pan_india_rollup_ndd.net_worth_last_30days / pan_india_rollup_ndd.sales, 2\n", ")\n", "pan_india_rollup_ndd[\"forward_looking_doi\"] = round(\n", "    pan_india_rollup_ndd.net_worth_last_30days\n", "    / (pan_india_rollup_ndd.avg_30_days_forecast_sales * 30),\n", "    2,\n", ")\n", "\n", "pan_india_rollup_ndd.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### NDD Faciltiy summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_ndd_new = doi_full_facility_ndd[\n", "    [\n", "        \"Facility\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "doi_full_facility_ndd_new = pan_india_rollup_ndd.append(doi_full_facility_ndd_new)\n", "doi_full_facility_ndd_new = doi_full_facility_ndd_new[\n", "    doi_full_facility_ndd_new[\"net_worth_eod\"] > 0\n", "]\n", "doi_full_facility_ndd_new.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DS Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_DS = doi_facility[doi_facility[\"facility_type\"] == \"Dark_store\"]\n", "doi_full_facility_DS[\"doi\"] = (\n", "    doi_full_facility_DS.net_worth_last_30days / doi_full_facility_DS.sales\n", ")\n", "doi_full_facility_DS[\"forward_looking_doi\"] = doi_full_facility_DS.net_worth_last_30days / (\n", "    doi_full_facility_DS.avg_30_days_forecast_sales * 30\n", ")\n", "doi_full_facility_DS.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_DS[\"doi\"] = doi_full_facility_DS.doi.astype(float)\n", "doi_full_facility_DS[\"forward_looking_doi\"] = doi_full_facility_DS.forward_looking_doi.astype(float)\n", "doi_full_facility_DS[\"net_worth_eod\"] = doi_full_facility_DS.net_worth_eod.astype(float)\n", "doi_full_facility_DS[\"sales\"] = doi_full_facility_DS.sales.astype(float)\n", "doi_full_facility_DS[\"net_worth_last_30days\"] = doi_full_facility_DS.net_worth_last_30days.astype(\n", "    float\n", ")\n", "doi_full_facility_DS[\n", "    \"avg_30_days_forecast_sales\"\n", "] = doi_full_facility_DS.avg_30_days_forecast_sales.astype(float)\n", "\n", "doi_full_facility_DS.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PAN India DS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_DS = doi_full_facility_DS[\n", "    [\n", "        \"facility_id\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"Facility\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "\n", "pan_india_DS[\"Facility\"] = \"Pan India\"\n", "\n", "pan_india_rollup_DS = round(\n", "    pan_india_DS.groupby([\"Facility\"]).agg(\n", "        {\n", "            \"net_worth_eod\": \"sum\",\n", "            \"net_worth_last_30days\": \"sum\",\n", "            \"sales\": \"sum\",\n", "            \"avg_30_days_forecast_sales\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "pan_india_rollup_DS[\"doi\"] = round(\n", "    pan_india_rollup_DS.net_worth_last_30days / pan_india_rollup_DS.sales, 2\n", ")\n", "pan_india_rollup_DS[\"forward_looking_doi\"] = round(\n", "    pan_india_rollup_DS.net_worth_last_30days\n", "    / (pan_india_rollup_DS.avg_30_days_forecast_sales * 30),\n", "    2,\n", ")\n", "\n", "pan_india_rollup_DS.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DS Final Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_DS_new = doi_full_facility_DS[\n", "    [\n", "        \"Facility\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "doi_full_facility_DS_new = pan_india_rollup_DS.append(doi_full_facility_DS_new)\n", "doi_full_facility_DS_new.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Franchise summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_fS = doi_facility[doi_facility[\"facility_type\"] == \"Franchise\"]\n", "\n", "\n", "doi_full_facility_fS[\"doi\"] = (\n", "    doi_full_facility_fS.net_worth_last_30days / doi_full_facility_fS.sales\n", ")\n", "doi_full_facility_fS[\"forward_looking_doi\"] = doi_full_facility_fS.net_worth_last_30days / (\n", "    doi_full_facility_fS.avg_30_days_forecast_sales * 30\n", ")\n", "\n", "doi_full_facility_fS[\"Facility\"] = doi_full_facility_fS[\"facility_name\"]\n", "doi_full_facility_fS.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_fS[\"doi\"] = doi_full_facility_fS.doi.astype(float)\n", "doi_full_facility_fS[\"forward_looking_doi\"] = doi_full_facility_fS.forward_looking_doi.astype(float)\n", "doi_full_facility_fS[\"net_worth_eod\"] = doi_full_facility_fS.net_worth_eod.astype(float)\n", "doi_full_facility_fS[\"sales\"] = doi_full_facility_fS.sales.astype(float)\n", "doi_full_facility_fS[\"net_worth_last_30days\"] = doi_full_facility_fS.net_worth_last_30days.astype(\n", "    float\n", ")\n", "doi_full_facility_fS[\n", "    \"avg_30_days_forecast_sales\"\n", "] = doi_full_facility_fS.avg_30_days_forecast_sales.astype(float)\n", "\n", "doi_full_facility_fS.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PAN India FS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_fS = doi_full_facility_fS[\n", "    [\n", "        \"facility_id\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"Facility\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "\n", "pan_india_fS[\"Facility\"] = \"Pan India\"\n", "\n", "pan_india_rollup_fS = round(\n", "    pan_india_fS.groupby([\"Facility\"]).agg(\n", "        {\n", "            \"net_worth_eod\": \"sum\",\n", "            \"net_worth_last_30days\": \"sum\",\n", "            \"sales\": \"sum\",\n", "            \"avg_30_days_forecast_sales\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "pan_india_rollup_fS[\"doi\"] = round(\n", "    pan_india_rollup_fS.net_worth_last_30days / pan_india_rollup_fS.sales, 2\n", ")\n", "pan_india_rollup_fS[\"forward_looking_doi\"] = round(\n", "    pan_india_rollup_fS.net_worth_last_30days\n", "    / (pan_india_rollup_fS.avg_30_days_forecast_sales * 30),\n", "    2,\n", ")\n", "\n", "pan_india_rollup_fS.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FS Final summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_fS = doi_full_facility_fS[\n", "    [\n", "        \"Facility\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "doi_full_facility_fS_new = pan_india_rollup_fS.append(doi_full_facility_fS)\n", "doi_full_facility_fS_new.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Zone level summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_ndd = doi_facility[doi_facility[\"facility_type\"] == \"NDD\"]\n", "zone_ndd = (\n", "    zone_ndd.groupby([\"zone\"])[\n", "        [\n", "            \"net_worth_eod\",\n", "            \"net_worth_last_30days\",\n", "            \"sales\",\n", "            \"avg_30_days_forecast_sales\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "\n", "li = {\n", "    \"North\": \"North - NDD\",\n", "    \"South\": \"South - NDD\",\n", "    \"West\": \"West - NDD\",\n", "    \"East\": \"East - NDD\",\n", "}\n", "\n", "zone_ndd[\"zone\"] = zone_ndd[\"zone\"].map(li)\n", "\n", "zone_ndd.rename(columns={\"zone\": \"Facility\"}, inplace=True)\n", "zone_ndd[\"doi\"] = zone_ndd.net_worth_last_30days / zone_ndd.sales\n", "zone_ndd[\"forward_looking_doi\"] = zone_ndd.net_worth_last_30days / (\n", "    zone_ndd.avg_30_days_forecast_sales * 30\n", ")\n", "zone_ndd.sort_values(by=[\"net_worth_eod\"], ascending=False, inplace=True)\n", "zone_ndd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_ndd[\"doi\"] = zone_ndd.doi.astype(float)\n", "zone_ndd[\"forward_looking_doi\"] = zone_ndd.forward_looking_doi.astype(float)\n", "zone_ndd[\"net_worth_eod\"] = zone_ndd.net_worth_eod.astype(float)\n", "zone_ndd[\"sales\"] = zone_ndd.sales.astype(float)\n", "zone_ndd[\"net_worth_last_30days\"] = zone_ndd.net_worth_last_30days.astype(float)\n", "zone_ndd[\"avg_30_days_forecast_sales\"] = zone_ndd.avg_30_days_forecast_sales.astype(float)\n", "\n", "zone_ndd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Final summary at zone level"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = pan_india_rollup_ndd.append(zone_ndd)\n", "final[\"Facility\"] = np.where(final[\"Facility\"] == \"Pan India\", \"Pan India - NDD\", final[\"Facility\"])\n", "final = final.append(pan_india_rollup_DS)\n", "final[\"Facility\"] = np.where(\n", "    final[\"Facility\"] == \"Pan India\", \"Pan India - Dark Stores\", final[\"Facility\"]\n", ")\n", "final = final.append(pan_india_rollup_fS)\n", "final[\"Facility\"] = np.where(\n", "    final[\"Facility\"] == \"Pan India\", \"Pan India - Franchise Stores\", final[\"Facility\"]\n", ")\n", "final"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Report at L0 Level"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Worth at L0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with\n", "\n", "zone_tbl_treatment as\n", "(\n", "select\n", "facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "lp_treatment_tbl as\n", "(\n", "select \n", "a.item_id,\n", "outlet_id,\n", "b.warehouse_id,\n", "facility_id,\n", "landing_price,\n", "row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "row_number() over (partition by a.item_id order by dt_ist desc) as row_rank1\n", "from weighted_landing_price a\n", "left join (select * from  lake_retail.warehouse_outlet_mapping  where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "left join (select * from  lake_retail.view_console_outlet  where active = true) c on b.warehouse_id = c.id\n", "where landing_price is not null\n", "),\n", "\n", "availability as\n", "(\n", "select distinct * from \n", "(select e.zone,\n", "a.facility_id,\n", "a.facility_name,\n", "(case when facility_name ilike '%% es%%' then 'Dark' when facility_name ilike '%%dark%%' then 'Franchise' else 'NDD' end) as store_type,\n", "a.item_id,\n", "date(order_date) as order_date,\n", "availability,\n", "app_live,\n", "inv_flag,\n", "l0,\n", "actual_quantity,\n", "coalesce(actual_quantity,0) - coalesce(blocked_quantity,0) as unblocked_quantity,\n", "case when is_gb = 'TRUE' then 1 else 0 end as gb_flag,\n", "case when bucket_x = 'Y' then 'X' when buckets = 'A' then 'A' else 'B' end as buckets,\n", "case when l0 in ('Home Furnishing and Decor','Home Improvement and Accessories','Kitchen & Dining needs','Home Appliances',\n", "'Home & Kitchen', 'Furnishing & Home Needs', 'Home and Kitchen','Kitchen and Dining Needs', \n", "'Vegetables & Fruits', 'Specials','Home Appliances','Fashion and Lifestyle', 'Best Value')\n", "then 1 else 0 end as gm_flag,\n", "coalesce(coalesce(b.landing_price,c.landing_price),d.variant_mrp*0.7000) as landing_price,\n", "row_number() over (partition by a.item_id, a.facility_id, date(order_date) order by order_date desc) as row_rank\n", "from(\n", "SELECT r.*\n", "FROM consumer.rpc_daily_availability r\n", "INNER JOIN\n", "  (SELECT facility_id,\n", "          date(order_date) AS order_date,\n", "          max(order_date) AS maxi\n", "   FROM consumer.rpc_daily_availability\n", "   where date(order_date) between current_date- interval'30 days' and current_date- interval'1 days'\n", "   GROUP BY 1,\n", "            2) x1 ON r.facility_id=x1.facility_id\n", "AND x1.maxi=r.order_date )a\n", "left join zone_tbl_treatment e on a.facility_id = e.facility_id\n", "left join (select * from lp_treatment_tbl where row_rank = 1) b on a.facility_id = b.facility_id  AND a.item_id = b.item_id\n", "left join (select * from lp_treatment_tbl where row_rank1 = 1) c on a.item_id = c.item_id\n", "left join (select * from item_level_info where row_rank = 1) d on a.item_id = d.item_id\n", "where date(order_date) between current_date- interval'30 days' and current_date- interval'1 days'\n", "\n", ") x1\n", "where row_rank=1\n", "and store_type != 'Franchise'\n", "),\n", "\n", "fe_be_mapping as (SELECT DISTINCT\n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND f1.name not ilike '%%dark%%'),\n", "\n", "summary as (select zone,\n", "facility_id,\n", "facility_name,\n", "l0,\n", "item_id,\n", "sum(case when order_date=current_date-1 then (actual_quantity*landing_price) end) as net_worth_eod,\n", "sum(case when (order_date<=current_date- interval'0 days' and order_date >=current_date- interval'30 days') then (actual_quantity*landing_price)end) as net_worth_last_30days\n", "\n", "FROM availability a\n", "GROUP BY 1,2,3,4,5)\n", "\n", "\n", "select zone, facility_id, facility_name, l0, sum(net_worth_eod+frontend_net_worth_eod) as net_worth_eod, sum(net_worth_last_30days+frontend_net_worth_last_30days) as net_worth_last_30days\n", "from (select zone, facility_id, facility_name, l0, item_id, coalesce(net_worth_eod,0) as net_worth_eod, coalesce(net_worth_last_30days,0) as net_worth_last_30days, sum(frontend_net_worth_eod) as frontend_net_worth_eod, sum(frontend_net_worth_last_30days) as frontend_net_worth_last_30days\n", "    from (SELECT a.*, mp.frontend_facility_id, \n", "        (case when b.net_worth_eod is null then 0 else b.net_worth_eod end) as frontend_net_worth_eod,\n", "        (case when b.net_worth_last_30days is null then 0 else b.net_worth_last_30days end) as frontend_net_worth_last_30days\n", "        FROM summary a\n", "        LEFT JOIN fe_be_mapping mp ON a.facility_id = mp.backend_facility_id and a.item_id = mp.item_id\n", "        LEFT JOIN summary b ON mp.frontend_facility_id = b.facility_id AND mp.item_id = b.item_id)\n", "    group by 1,2,3,4,5,6,7)\n", "group by 1,2,3,4\n", "\"\"\"\n", "\n", "inventory_worth_l0 = pd.read_sql(query, redshift)\n", "inventory_worth_l0 = inventory_worth_l0[inventory_worth_l0[\"net_worth_eod\"] > 0]\n", "inventory_worth_l0.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sales at l0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"WITH it_pd AS\n", "  (SELECT DISTINCT item_id,\n", "                   product_id,\n", "                   multiplier,\n", "                   updated_on,\n", "                   max(updated_on) OVER (PARTITION BY product_id) AS last_updated\n", "   FROM it_pd_log),\n", "     item_level_info AS\n", "  (SELECT item_id,\n", "          variant_mrp,\n", "          row_number() OVER (PARTITION BY item_id\n", "                             ORDER BY updated_at DESC) AS row_rank\n", "   FROM lake_rpc.product_product),\n", "     mapping AS\n", "  (SELECT DISTINCT item_id,\n", "                   product_id,\n", "                   coalesce(multiplier,1) AS multiplier\n", "   FROM it_pd\n", "   WHERE last_updated = updated_on ),\n", "   \n", "   l0_category as (with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL),\n", "   \n", "   \n", "     orders AS\n", "  (SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "                   date(convert_timezone('Asia/Kolkata',a.install_ts)) AS checkout_date,\n", "                   o.facility_id,\n", "                   o.facility_name,\n", "                   (case when o.facility_name ilike '%% es%%' then 'Dark' when o.facility_name ilike '%%dark%%' then 'Franchise' else 'NDD' end) as store_type,\n", "                   a.id AS ancestor,\n", "                   a.current_status,\n", "                   oi.product_id,\n", "                   l0.l0,\n", "                   c.item_id,\n", "                   oi.selling_price,\n", "                   d.variant_mrp,\n", "                   oi.quantity*multiplier AS quantity,\n", "                   multiplier,\n", "                   sum(d.variant_mrp) OVER (PARTITION BY a.id,\n", "                                                         oi.product_id) AS total_mrp\n", "   FROM lake_oms_bifrost.oms_order a\n", "   INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM lake_oms_bifrost.oms_order_item\n", "      WHERE freebie_id IS NULL\n", "        AND date(install_ts + interval '5.5 Hours') BETWEEN CURRENT_DATE- interval'30 days' AND CURRENT_DATE- interval'1 days') oi ON a.id = oi.order_id\n", "   LEFT JOIN mapping c ON oi.product_id = c.product_id\n", "   left join l0_category l0 on l0.item_id=c.item_id\n", "   LEFT JOIN\n", "     (SELECT *\n", "      FROM item_level_info\n", "      WHERE row_rank = 1) d ON c.item_id = d.item_id\n", "   INNER JOIN\n", "     (SELECT DISTINCT ancestor,\n", "                      facility_id,\n", "                      f.name AS facility_name\n", "      FROM lake_ims.ims_order_details o\n", "      INNER JOIN lake_retail.console_outlet ro ON ro.id=o.outlet\n", "      INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "      WHERE date(o.created_at) BETWEEN CURRENT_DATE- interval'32 days' AND CURRENT_DATE- interval'0 days') o ON o.ancestor=a.id\n", "   WHERE date(convert_timezone('Asia/Kolkata',a.install_ts)) BETWEEN CURRENT_DATE- interval'30 days' AND CURRENT_DATE- interval'1 days'\n", "     AND a.direction ='FORWARD'\n", "     AND b.city_name NOT IN ('Not in service area',\n", "                             'Hapur',\n", "                             'Test city')\n", "     AND b.city_name NOT ILIKE '%%b2b%%'\n", "     AND a.current_status <> 'CANCELLED'\n", "     AND a.type IN ('RetailForwardOrder',\n", "                    'InternalForwardOrder')\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,\n", "            9,\n", "            10,\n", "            11,\n", "            12,\n", "            13,\n", "            14),\n", "     merchant_and_store_join AS\n", "  (SELECT a.*,\n", "          selling_price*variant_mrp*1.000/(total_mrp*multiplier) AS item_selling_price\n", "   FROM orders a\n", "   WHERE store_type != 'Franchise'),\n", "   \n", "   fe_be_mapping as (SELECT DISTINCT \n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND f1.name not ilike '%%dark%%'),\n", "\n", "summary as (SELECT item_id,\n", "        facility_id,\n", "       facility_name,\n", "       l0,\n", "       sum(item_selling_price*quantity) AS sales\n", "FROM merchant_and_store_join\n", "GROUP BY 1,\n", "         2,3,4),\n", "         \n", "fin as (select a.*, mp.backend_facility_id, mp.backend_facility_name, coalesce(b.sales,0) as backend_sales\n", "from summary a\n", "left join fe_be_mapping mp on a.facility_id = mp.frontend_facility_id and a.item_id = mp.item_id\n", "left join summary b on b.facility_id = mp.backend_facility_id and b.item_id = mp.item_id\n", "\n", "where a.item_id is not null)\n", "\n", "\n", "select facility_id, facility_name, l0, sum(sales) as sales\n", "from fin\n", "group by 1,2,3\n", "\n", "union\n", "\n", "select backend_facility_id, backend_facility_name, l0, sum(sales)\n", "from fin\n", "where backend_facility_id is not null\n", "group by 1,2,3\"\"\"\n", "\n", "\n", "sales_l0 = pd.read_sql(query, redshift)\n", "sales_l0.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_l0 = sales_l0.groupby([\"facility_id\", \"facility_name\", \"l0\"])[\"sales\"].sum().reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Forecast sales at l0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_30_days_forecast_l0_query = \"\"\"\n", "\n", "with \n", "item_level_info as\n", "(\n", "select\n", "item_id,\n", "variant_mrp,\n", "row_number() over (partition by item_id order by updated_at desc) as row_rank\n", "from \n", "lake_rpc.product_product\n", "),\n", "\n", "l0_category as (with PRODUCT_Category AS\n", "  (SELECT P.ID AS PID,\n", "          P.NAME AS PRODUCT,\n", "          P.UNIT AS Unit,\n", "          C2.NAME AS L2,\n", "          (CASE\n", "               WHEN C1.NAME = C.name THEN C2.name\n", "               ELSE C1.name\n", "           END) AS L1,\n", "          C.NAME AS L0,\n", "          P.BRAND AS brand,\n", "          P.MANUFACTURER AS manf,\n", "          pt.name AS product_type\n", "   FROM lake_cms.gr_product P\n", "   INNER JOIN lake_cms.gr_product_category_mapping PCM ON P.ID = PCM.PRODUCT_ID\n", "   INNER JOIN lake_cms.gr_category C2 ON PCM.CATEGORY_ID = C2.ID\n", "   AND PCM.IS_PRIMARY=TRUE\n", "   INNER JOIN lake_cms.gr_category C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "   INNER JOIN lake_cms.gr_category C ON C1.PARENT_CATEGORY_ID = C.ID\n", "   INNER JOIN lake_cms.gr_product_type pt ON p.type_id=pt.id )\n", "\n", "  SELECT item_id,\n", "          product_id,\n", "          (cat.product || ' ' || cat.unit) AS name,\n", "          cat.L0,\n", "          cat.l1,\n", "          cat.l2,\n", "          cat.brand,\n", "          cat.manf,\n", "          cat.product_type\n", "   FROM lake_rpc.item_product_mapping rpc\n", "   INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "   AND rpc.offer_id IS NULL\n", "   AND rpc.item_id IS NOT NULL\n", "   AND rpc.product_id IS NOT NULL),\n", "   \n", "lp_treatment_tbl as\n", "(\n", "select \n", "a.item_id,\n", "outlet_id,\n", "b.warehouse_id,\n", "facility_id,\n", "landing_price,\n", "row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "row_number() over (partition by a.item_id order by dt_ist desc) as row_rank1\n", "from weighted_landing_price a\n", "left join (select * from  lake_retail.warehouse_outlet_mapping  where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "left join (select * from  lake_retail.console_outlet where active = true) c on b.warehouse_id = c.id\n", "where landing_price is not null\n", "),\n", "\n", "forecast as (select item_id, facility_id, avg(forecast_quantity) as avg_30_days_forecast_qty from\n", "    (select item_id, facility_id, date, sum(consumption) as forecast_quantity from(\n", "        (select item_id, outlet_id, date, consumption, active, create_ts, update_ts from(\n", "            select item_id, outlet_id, date, consumption, active, created_at as create_ts, updated_at as update_ts,\n", "            rank() over (partition by item_id, outlet_id, date order by updated_at desc) as rnk\n", "            from lake_snorlax.date_wise_consumption\n", "            where date between current_date + interval '1 days' and current_date + interval '30 days')\n", "        where rnk = 1) a\n", "        left join\n", "        (select id, facility_id from lake_retail.console_outlet) b\n", "        on a.outlet_id = b.id\n", "    )\n", "    group by 1,2,3)\n", "group by 1,2),\n", "\n", "fe_be_mapping as (SELECT DISTINCT\n", "    a.item_id,\n", "    ro.facility_id AS frontend_facility_id,\n", "    f1.name as frontend_facility_name,\n", "    r.facility_id AS backend_facility_id,\n", "    f2.name as backend_facility_name\n", "FROM lake_rpc.item_outlet_tag_mapping a\n", "INNER JOIN lake_retail.console_outlet ro ON ro.id=a.outlet_id\n", "INNER JOIN lake_retail.console_outlet r ON r.id=a.tag_value\n", "left join lake_crates.facility f1 on ro.facility_id = f1.id\n", "left join lake_crates.facility f2 on r.facility_id = f2.id\n", "WHERE tag_type_id=8\n", "AND a.active=1\n", "AND ro.name not ilike '%%old%%'\n", "AND f1.name not ilike '%%dark%%'),\n", "\n", "summary as (select item_id, facility_id, l0, sum(avg_30_days_forecast_qty*landing_price) as avg_30_days_forecast_sales from\n", "    (select a.item_id, a.facility_id, l0,\n", "    coalesce(coalesce(b.landing_price,c.landing_price),d.variant_mrp*0.7000) as landing_price,\n", "    avg_30_days_forecast_qty\n", "    from forecast a\n", "    inner join lake_rpc.product_facility_master_assortment pfma on a.item_id = pfma.item_id and a.facility_id = pfma.facility_id and pfma.master_assortment_substate_id=1\n", "    left join (select * from lp_treatment_tbl where row_rank = 1) b on a.facility_id = b.facility_id  AND a.item_id = b.item_id\n", "    left join (select * from lp_treatment_tbl where row_rank1 = 1) c on a.item_id = c.item_id\n", "    left join (select * from item_level_info where row_rank = 1) d on a.item_id = d.item_id\n", "    left join l0_category l0 on l0.item_id = a.item_id)\n", "group by 1,2,3),\n", "\n", "fin as (select a.*, mp.backend_facility_id, coalesce(b.avg_30_days_forecast_sales,0) as backend_avg_30_days_forecast_sales\n", "from summary a\n", "left join fe_be_mapping mp on a.facility_id = mp.frontend_facility_id and a.item_id = mp.item_id\n", "left join summary b on b.facility_id = mp.backend_facility_id and b.item_id = mp.item_id\n", "\n", "where a.item_id is not null)\n", "\n", "\n", "select facility_id, l0, sum(avg_30_days_forecast_sales) as avg_30_days_forecast_sales\n", "from fin\n", "group by 1,2\n", "\n", "union\n", "\n", "select backend_facility_id, l0, sum(avg_30_days_forecast_sales)\n", "from fin\n", "where backend_facility_id is not null\n", "group by 1,2\n", "\n", "\n", "\"\"\"\n", "\n", "next_30_days_forecast_l0 = pd.read_sql(next_30_days_forecast_l0_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_30_days_forecast_l0 = (\n", "    next_30_days_forecast_l0.groupby([\"facility_id\", \"l0\"])[\"avg_30_days_forecast_sales\"]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_l0[\"l0\"] = np.where(sales_l0[\"l0\"].isnull(), \"Not Specified\", sales_l0[\"l0\"])\n", "inventory_worth_l0[\"l0\"] = np.where(\n", "    inventory_worth_l0[\"l0\"] == \"\", \"Not Specified\", inventory_worth_l0[\"l0\"]\n", ")\n", "next_30_days_forecast_l0[\"l0\"] = np.where(\n", "    next_30_days_forecast_l0[\"l0\"] == \"\",\n", "    \"Not Specified\",\n", "    next_30_days_forecast_l0[\"l0\"],\n", ")\n", "inventory_worth_l0.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_mailer_data_l0_1 = inventory_worth_l0.merge(\n", "    sales_l0, on=[\"facility_id\", \"facility_name\", \"l0\"], how=\"left\"\n", ")\n", "doi_mailer_data_l0 = doi_mailer_data_l0_1.merge(\n", "    next_30_days_forecast_l0, on=[\"facility_id\", \"l0\"], how=\"left\"\n", ")\n", "doi_mailer_data_l0 = doi_mailer_data_l0.fillna(0)\n", "doi_mailer_data_l0.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ref_dict = {\n", "    \"Biscuits, Namkeen & Chocolates\": \"Biscuits, Snacks & Chocolates\",\n", "    \"Fresh & Frozen Food\": \"Frozen Food\",\n", "    \"Fresh and Frozen Food\": \"Frozen Food\",\n", "    \"Fruits & Vegetables\": \"Vegetables & Fruits\",\n", "    \"Household Needs\": \"Household Items\",\n", "}\n", "for key, value in ref_dict.items():\n", "    doi_mailer_data_l0[\"l0\"] = doi_mailer_data_l0[\"l0\"].replace(key, value)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_l0 = doi_mailer_data_l0.merge(facility_mapping, on=\"facility_name\", how=\"left\")\n", "\n", "doi_l0 = doi_l0[\n", "    ~doi_l0.facility_name.isin(\n", "        [\n", "            \"Super Store Delhi P3 - Warehouse\",\n", "            \"Super Store Chennai - Warehouse\",\n", "            \"Super Store Delhi P1 - Warehouse\",\n", "            \"<PERSON><PERSON><PERSON> - Kolkata\",\n", "            \"Delhi Warehouse Bulk Bamnoli\",\n", "            \"Bulk NCR PL Warehouse\",\n", "        ]\n", "    )\n", "]\n", "\n", "\n", "doi_l0[\"facility_name\"] = doi_l0[\"facility_name\"].astype(\"str\")\n", "doi_l0.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_l0[\"facility_type\"] = doi_l0.apply(facility_identification, axis=1)\n", "doi_l0.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### NDD L0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_l0_ndd = doi_l0[doi_l0[\"facility_type\"] == \"NDD\"]\n", "\n", "doi_l0_ndd = doi_l0_ndd[\n", "    ~doi_l0_ndd.facility_name.isin(\n", "        [\n", "            \"Super Store Delhi P3 - Warehouse\",\n", "            \"Super Store Chennai - Warehouse\",\n", "            \"Super Store Delhi P1 - Warehouse\",\n", "            \"<PERSON><PERSON><PERSON> - Kolkata\",\n", "            \"Delhi Warehouse Bulk Bamnoli\",\n", "            \"Bulk NCR PL Warehouse\",\n", "        ]\n", "    )\n", "]\n", "\n", "\n", "doi_l0_ndd = (\n", "    doi_l0_ndd.groupby([\"l0\"])[\n", "        [\n", "            \"net_worth_eod\",\n", "            \"net_worth_last_30days\",\n", "            \"sales\",\n", "            \"avg_30_days_forecast_sales\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "doi_l0_ndd[\"doi\"] = doi_l0_ndd.net_worth_last_30days / doi_l0_ndd.sales\n", "doi_l0_ndd[\"forward_looking_doi\"] = doi_l0_ndd.net_worth_last_30days / (\n", "    doi_l0_ndd.avg_30_days_forecast_sales * 30\n", ")\n", "doi_l0_ndd.sort_values(by=[\"net_worth_eod\"], ascending=False, inplace=True)\n", "\n", "doi_l0_ndd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_l0_ndd[\"l0\"].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### NDD Final summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_ndd_l0 = doi_l0_ndd[\n", "    [\n", "        \"l0\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "    ]\n", "]\n", "\n", "pan_india_ndd_l0[\"l0\"] = \"Pan India\"\n", "\n", "pan_india_ndd_l0 = round(\n", "    pan_india_ndd_l0.groupby([\"l0\"]).agg(\n", "        {\n", "            \"net_worth_eod\": \"sum\",\n", "            \"net_worth_last_30days\": \"sum\",\n", "            \"sales\": \"sum\",\n", "            \"avg_30_days_forecast_sales\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "pan_india_ndd_l0[\"doi\"] = pan_india_ndd_l0.net_worth_last_30days / pan_india_ndd_l0.sales\n", "pan_india_ndd_l0[\"forward_looking_doi\"] = pan_india_ndd_l0.net_worth_last_30days / (\n", "    pan_india_ndd_l0.avg_30_days_forecast_sales * 30\n", ")\n", "\n", "pan_india_ndd_l0 = pan_india_ndd_l0[\n", "    [\n", "        \"l0\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "pan_india_ndd_l0_new = pan_india_ndd_l0.append(doi_l0_ndd)\n", "\n", "pan_india_ndd_l0_new[\"doi\"] = pan_india_ndd_l0_new.doi.astype(float)\n", "pan_india_ndd_l0_new[\"forward_looking_doi\"] = pan_india_ndd_l0_new.forward_looking_doi.astype(float)\n", "pan_india_ndd_l0_new[\"net_worth_eod\"] = pan_india_ndd_l0_new.net_worth_eod.astype(float)\n", "pan_india_ndd_l0_new[\"sales\"] = pan_india_ndd_l0_new.sales.astype(float)\n", "pan_india_ndd_l0_new[\"net_worth_last_30days\"] = pan_india_ndd_l0_new.net_worth_last_30days.astype(\n", "    float\n", ")\n", "pan_india_ndd_l0_new[\n", "    \"avg_30_days_forecast_sales\"\n", "] = pan_india_ndd_l0_new.avg_30_days_forecast_sales.astype(float)\n", "\n", "pan_india_ndd_l0_new = pan_india_ndd_l0_new[pan_india_ndd_l0_new[\"net_worth_eod\"] > 0]\n", "pan_india_ndd_l0_new"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DS L0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_DS_l0 = doi_l0[doi_l0[\"facility_type\"] == \"Dark_store\"]\n", "\n", "doi_full_facility_DS_l0 = (\n", "    doi_full_facility_DS_l0.groupby([\"l0\"])[\n", "        [\n", "            \"net_worth_eod\",\n", "            \"net_worth_last_30days\",\n", "            \"sales\",\n", "            \"avg_30_days_forecast_sales\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "\n", "doi_full_facility_DS_l0[\"doi\"] = (\n", "    doi_full_facility_DS_l0.net_worth_last_30days / doi_full_facility_DS_l0.sales\n", ")\n", "doi_full_facility_DS_l0[\"forward_looking_doi\"] = doi_full_facility_DS_l0.net_worth_last_30days / (\n", "    doi_full_facility_DS_l0.avg_30_days_forecast_sales * 30\n", ")\n", "\n", "doi_full_facility_DS_l0.sort_values(by=[\"net_worth_eod\"], ascending=False, inplace=True)\n", "doi_full_facility_DS_l0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DS L0 Final Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_ds_l0 = doi_full_facility_DS_l0[\n", "    [\n", "        \"l0\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "    ]\n", "]\n", "\n", "pan_india_ds_l0[\"l0\"] = \"Pan India\"\n", "\n", "pan_india_ds_l0 = round(\n", "    pan_india_ds_l0.groupby([\"l0\"]).agg(\n", "        {\n", "            \"net_worth_eod\": \"sum\",\n", "            \"net_worth_last_30days\": \"sum\",\n", "            \"sales\": \"sum\",\n", "            \"avg_30_days_forecast_sales\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "pan_india_ds_l0[\"doi\"] = pan_india_ds_l0.net_worth_last_30days / pan_india_ds_l0.sales\n", "pan_india_ds_l0[\"forward_looking_doi\"] = pan_india_ds_l0.net_worth_last_30days / (\n", "    pan_india_ds_l0.avg_30_days_forecast_sales * 30\n", ")\n", "\n", "pan_india_ds_l0 = pan_india_ds_l0[\n", "    [\n", "        \"l0\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "pan_india_ds_l0_new = pan_india_ds_l0.append(doi_full_facility_DS_l0)\n", "\n", "pan_india_ds_l0_new[\"doi\"] = pan_india_ds_l0_new.doi.astype(float)\n", "pan_india_ds_l0_new[\"forward_looking_doi\"] = pan_india_ds_l0_new.forward_looking_doi.astype(float)\n", "pan_india_ds_l0_new[\"net_worth_eod\"] = pan_india_ds_l0_new.net_worth_eod.astype(float)\n", "pan_india_ds_l0_new[\"sales\"] = pan_india_ds_l0_new.sales.astype(float)\n", "pan_india_ds_l0_new[\n", "    \"avg_30_days_forecast_sales\"\n", "] = pan_india_ds_l0_new.avg_30_days_forecast_sales.astype(float)\n", "pan_india_ds_l0_new[\"net_worth_last_30days\"] = pan_india_ds_l0_new.net_worth_last_30days.astype(\n", "    float\n", ")\n", "\n", "\n", "pan_india_ds_l0_new = pan_india_ds_l0_new[pan_india_ds_l0_new[\"net_worth_eod\"] > 0]\n", "\n", "pan_india_ds_l0_new"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FS L0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_FS_l0 = doi_l0[doi_l0[\"facility_type\"] == \"Franchise\"]\n", "\n", "doi_full_facility_FS_l0 = (\n", "    doi_full_facility_FS_l0.groupby([\"l0\"])[\n", "        [\n", "            \"net_worth_eod\",\n", "            \"net_worth_last_30days\",\n", "            \"sales\",\n", "            \"avg_30_days_forecast_sales\",\n", "        ]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")\n", "\n", "doi_full_facility_FS_l0[\"doi\"] = (\n", "    doi_full_facility_FS_l0.net_worth_last_30days / doi_full_facility_FS_l0.sales\n", ")\n", "doi_full_facility_FS_l0[\"forward_looking_doi\"] = doi_full_facility_FS_l0.net_worth_last_30days / (\n", "    doi_full_facility_FS_l0.avg_30_days_forecast_sales * 30\n", ")\n", "\n", "doi_full_facility_FS_l0.sort_values(by=[\"net_worth_eod\"], ascending=False, inplace=True)\n", "\n", "doi_full_facility_FS_l0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_fs_l0 = doi_full_facility_FS_l0[\n", "    [\n", "        \"l0\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "    ]\n", "]\n", "\n", "pan_india_fs_l0[\"l0\"] = \"Pan India\"\n", "\n", "pan_india_fs_l0 = round(\n", "    pan_india_fs_l0.groupby([\"l0\"]).agg(\n", "        {\n", "            \"net_worth_eod\": \"sum\",\n", "            \"net_worth_last_30days\": \"sum\",\n", "            \"sales\": \"sum\",\n", "            \"avg_30_days_forecast_sales\": \"sum\",\n", "        }\n", "    ),\n", "    1,\n", ").reset_index()\n", "pan_india_fs_l0[\"doi\"] = pan_india_fs_l0.net_worth_last_30days / pan_india_fs_l0.sales\n", "pan_india_fs_l0[\"forward_looking_doi\"] = (\n", "    pan_india_fs_l0.net_worth_last_30days / pan_india_fs_l0.avg_30_days_forecast_sales\n", ")\n", "\n", "pan_india_fs_l0 = pan_india_fs_l0[\n", "    [\n", "        \"l0\",\n", "        \"net_worth_eod\",\n", "        \"net_worth_last_30days\",\n", "        \"sales\",\n", "        \"avg_30_days_forecast_sales\",\n", "        \"doi\",\n", "        \"forward_looking_doi\",\n", "    ]\n", "]\n", "pan_india_fs_l0_new = pan_india_fs_l0.append(doi_full_facility_FS_l0)\n", "\n", "pan_india_fs_l0_new[\"doi\"] = pan_india_fs_l0_new.doi.astype(float)\n", "pan_india_fs_l0_new[\"forward_looking_doi\"] = pan_india_fs_l0_new.forward_looking_doi.astype(float)\n", "pan_india_fs_l0_new[\"net_worth_eod\"] = pan_india_fs_l0_new.net_worth_eod.astype(float)\n", "pan_india_fs_l0_new[\"sales\"] = pan_india_fs_l0_new.sales.astype(float)\n", "pan_india_fs_l0_new[\"net_worth_last_30days\"] = pan_india_fs_l0_new.net_worth_last_30days.astype(\n", "    float\n", ")\n", "pan_india_fs_l0_new[\n", "    \"avg_30_days_forecast_sales\"\n", "] = pan_india_fs_l0_new.avg_30_days_forecast_sales.astype(float)\n", "\n", "\n", "pan_india_fs_l0_new = pan_india_fs_l0_new[pan_india_fs_l0_new[\"net_worth_eod\"] > 0]\n", "\n", "\n", "pan_india_fs_l0_new"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Formating the tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def values_net_worth_eod(x):\n", "    if x[\"net_worth_eod\"] >= 10000000:\n", "        return str(round(x[\"net_worth_eod\"] / 10000000, 1)) + \"Cr\"\n", "\n", "    elif x[\"net_worth_eod\"] >= 100000:\n", "        return str(round(x[\"net_worth_eod\"] / 100000, 1)) + \"L\"\n", "\n", "    elif x[\"net_worth_eod\"] >= 1000:\n", "        return str(round(x[\"net_worth_eod\"] / 1000, 1)) + \"K\"\n", "\n", "    else:\n", "        return str(round(x[\"net_worth_eod\"], 1))\n", "\n", "\n", "def values_net_worth_last_30days(x):\n", "    if x[\"net_worth_last_30days\"] / 30 >= 10000000:\n", "        return str(round(x[\"net_worth_last_30days\"] / 300000000, 1)) + \"Cr\"\n", "\n", "    elif x[\"net_worth_last_30days\"] / 30 >= 100000:\n", "        return str(round(x[\"net_worth_last_30days\"] / 3000000, 1)) + \"L\"\n", "\n", "    elif x[\"net_worth_last_30days\"] / 30 >= 1000:\n", "        return str(round(x[\"net_worth_last_30days\"] / 30000, 1)) + \"K\"\n", "\n", "    else:\n", "        return str(round(x[\"net_worth_last_30days\"], 1))\n", "\n", "\n", "def values_sales(x):\n", "    if x[\"sales\"] / 30 >= 10000000:\n", "        return str(round(x[\"sales\"] / 300000000, 1)) + \"Cr\"\n", "\n", "    elif x[\"sales\"] / 30 >= 100000:\n", "        return str(round(x[\"sales\"] / 3000000, 1)) + \"L\"\n", "\n", "    elif x[\"sales\"] / 30 >= 1000:\n", "        return str(round(x[\"sales\"] / 30000, 1)) + \"K\"\n", "\n", "    else:\n", "        return str(round(x[\"sales\"], 1))\n", "\n", "\n", "def values_forecast_sales(x):\n", "    if x[\"avg_30_days_forecast_sales\"] >= 10000000:\n", "        return str(round(x[\"avg_30_days_forecast_sales\"] / 10000000, 1)) + \"Cr\"\n", "\n", "    elif x[\"avg_30_days_forecast_sales\"] >= 100000:\n", "        return str(round(x[\"avg_30_days_forecast_sales\"] / 100000, 1)) + \"L\"\n", "\n", "    elif x[\"avg_30_days_forecast_sales\"] >= 1000:\n", "        return str(round(x[\"avg_30_days_forecast_sales\"] / 1000, 1)) + \"K\"\n", "\n", "    else:\n", "        return str(round(x[\"avg_30_days_forecast_sales\"], 1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### NDD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final[\"values_net_worth_eod\"] = final.apply(values_net_worth_eod, axis=1)\n", "final[\"values_net_worth_last_30days\"] = final.apply(values_net_worth_last_30days, axis=1)\n", "final[\"values_sales\"] = final.apply(values_sales, axis=1)\n", "final[\"values_forecast_sales\"] = final.apply(values_forecast_sales, axis=1)\n", "final[\"doi\"] = round(final[\"doi\"], 1)\n", "final[\"forward_looking_doi\"] = round(final[\"forward_looking_doi\"], 1)\n", "\n", "doi_full_facility_ndd_new[\"values_net_worth_eod\"] = doi_full_facility_ndd_new.apply(\n", "    values_net_worth_eod, axis=1\n", ")\n", "doi_full_facility_ndd_new[\"values_net_worth_last_30days\"] = doi_full_facility_ndd_new.apply(\n", "    values_net_worth_last_30days, axis=1\n", ")\n", "doi_full_facility_ndd_new[\"values_sales\"] = doi_full_facility_ndd_new.apply(values_sales, axis=1)\n", "doi_full_facility_ndd_new[\"values_forecast_sales\"] = doi_full_facility_ndd_new.apply(\n", "    values_forecast_sales, axis=1\n", ")\n", "doi_full_facility_ndd_new[\"doi\"] = round(doi_full_facility_ndd_new[\"doi\"], 1)\n", "doi_full_facility_ndd_new[\"doi\"] = np.where(\n", "    doi_full_facility_ndd_new[\"doi\"] >= 1000, \"-\", doi_full_facility_ndd_new[\"doi\"]\n", ")\n", "doi_full_facility_ndd_new[\"forward_looking_doi\"] = round(\n", "    doi_full_facility_ndd_new[\"forward_looking_doi\"], 1\n", ")\n", "doi_full_facility_ndd_new[\"forward_looking_doi\"] = np.where(\n", "    doi_full_facility_ndd_new[\"forward_looking_doi\"] >= 1000,\n", "    \"-\",\n", "    doi_full_facility_ndd_new[\"forward_looking_doi\"],\n", ")\n", "doi_full_facility_ndd_new.replace([np.inf, -np.inf], \"-\", inplace=True)\n", "\n", "pan_india_ndd_l0_new[\"values_net_worth_eod\"] = pan_india_ndd_l0_new.apply(\n", "    values_net_worth_eod, axis=1\n", ")\n", "pan_india_ndd_l0_new[\"values_net_worth_last_30days\"] = pan_india_ndd_l0_new.apply(\n", "    values_net_worth_last_30days, axis=1\n", ")\n", "pan_india_ndd_l0_new[\"values_sales\"] = pan_india_ndd_l0_new.apply(values_sales, axis=1)\n", "pan_india_ndd_l0_new[\"values_forecast_sales\"] = pan_india_ndd_l0_new.apply(\n", "    values_forecast_sales, axis=1\n", ")\n", "pan_india_ndd_l0_new[\"doi\"] = round(pan_india_ndd_l0_new[\"doi\"], 1)\n", "pan_india_ndd_l0_new[\"doi\"] = np.where(\n", "    pan_india_ndd_l0_new[\"doi\"] >= 1000, \"-\", pan_india_ndd_l0_new[\"doi\"]\n", ")\n", "pan_india_ndd_l0_new[\"forward_looking_doi\"] = round(pan_india_ndd_l0_new[\"forward_looking_doi\"], 1)\n", "pan_india_ndd_l0_new[\"forward_looking_doi\"] = np.where(\n", "    pan_india_ndd_l0_new[\"forward_looking_doi\"] >= 1000,\n", "    \"-\",\n", "    pan_india_ndd_l0_new[\"forward_looking_doi\"],\n", ")\n", "pan_india_ndd_l0_new.replace([np.inf, -np.inf], \"-\", inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_full_facility_DS_new[\"values_net_worth_eod\"] = doi_full_facility_DS_new.apply(\n", "    values_net_worth_eod, axis=1\n", ")\n", "doi_full_facility_DS_new[\"values_net_worth_last_30days\"] = doi_full_facility_DS_new.apply(\n", "    values_net_worth_last_30days, axis=1\n", ")\n", "doi_full_facility_DS_new[\"values_sales\"] = doi_full_facility_DS_new.apply(values_sales, axis=1)\n", "doi_full_facility_DS_new[\"values_forecast_sales\"] = doi_full_facility_DS_new.apply(\n", "    values_forecast_sales, axis=1\n", ")\n", "doi_full_facility_DS_new[\"doi\"] = round(doi_full_facility_DS_new[\"doi\"], 1)\n", "doi_full_facility_DS_new[\"doi\"] = np.where(\n", "    doi_full_facility_DS_new[\"doi\"] >= 1000, \"-\", doi_full_facility_DS_new[\"doi\"]\n", ")\n", "doi_full_facility_DS_new[\"forward_looking_doi\"] = round(\n", "    doi_full_facility_DS_new[\"forward_looking_doi\"], 1\n", ")\n", "doi_full_facility_DS_new[\"forward_looking_doi\"] = np.where(\n", "    doi_full_facility_DS_new[\"forward_looking_doi\"] >= 1000,\n", "    \"-\",\n", "    doi_full_facility_DS_new[\"forward_looking_doi\"],\n", ")\n", "doi_full_facility_DS_new.replace([np.inf, -np.inf], \"-\", inplace=True)\n", "\n", "pan_india_ds_l0_new[\"values_net_worth_eod\"] = pan_india_ds_l0_new.apply(\n", "    values_net_worth_eod, axis=1\n", ")\n", "pan_india_ds_l0_new[\"values_net_worth_last_30days\"] = pan_india_ds_l0_new.apply(\n", "    values_net_worth_last_30days, axis=1\n", ")\n", "pan_india_ds_l0_new[\"values_sales\"] = pan_india_ds_l0_new.apply(values_sales, axis=1)\n", "pan_india_ds_l0_new[\"values_forecast_sales\"] = pan_india_ds_l0_new.apply(\n", "    values_forecast_sales, axis=1\n", ")\n", "pan_india_ds_l0_new[\"doi\"] = round(pan_india_ds_l0_new[\"doi\"], 1)\n", "pan_india_ds_l0_new[\"doi\"] = np.where(\n", "    pan_india_ds_l0_new[\"doi\"] >= 1000, \"-\", pan_india_ds_l0_new[\"doi\"]\n", ")\n", "pan_india_ds_l0_new[\"forward_looking_doi\"] = round(pan_india_ds_l0_new[\"forward_looking_doi\"], 1)\n", "pan_india_ds_l0_new[\"forward_looking_doi\"] = np.where(\n", "    pan_india_ds_l0_new[\"forward_looking_doi\"] >= 1000,\n", "    \"-\",\n", "    pan_india_ds_l0_new[\"forward_looking_doi\"],\n", ")\n", "pan_india_ds_l0_new.replace([np.inf, -np.inf], \"-\", inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting up mailer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# doi_full_facility_fS_new[\"values_net_worth_eod\"] = doi_full_facility_fS_new.apply(\n", "#     values_net_worth_eod, axis=1\n", "# )\n", "# doi_full_facility_fS_new[\n", "#     \"values_net_worth_last_30days\"\n", "# ] = doi_full_facility_fS_new.apply(values_net_worth_last_30days, axis=1)\n", "# doi_full_facility_fS_new[\"values_sales\"] = doi_full_facility_fS_new.apply(\n", "#     values_sales, axis=1\n", "# )\n", "# doi_full_facility_fS_new[\"values_forecast_sales\"] = doi_full_facility_fS_new.apply(values_forecast_sales, axis=1)\n", "# doi_full_facility_fS_new[\"doi\"] = round(doi_full_facility_fS_new[\"doi\"], 1)\n", "# doi_full_facility_fS_new[\"doi\"] = np.where(\n", "#     doi_full_facility_fS_new[\"doi\"] >= 1000, \"-\", doi_full_facility_fS_new[\"doi\"]\n", "# )\n", "# doi_full_facility_fS_new[\"forward_looking_doi\"] = round(doi_full_facility_fS_new[\"forward_looking_doi\"], 1)\n", "# doi_full_facility_fS_new[\"forward_looking_doi\"] = np.where(\n", "#     doi_full_facility_fS_new[\"forward_looking_doi\"] >= 1000, \"-\", doi_full_facility_fS_new[\"forward_looking_doi\"]\n", "# )\n", "# doi_full_facility_fS_new.replace([np.inf, -np.inf], \"-\", inplace=True)\n", "\n", "# pan_india_fs_l0_new[\"values_net_worth_eod\"] = pan_india_fs_l0_new.apply(\n", "#     values_net_worth_eod, axis=1\n", "# )\n", "# pan_india_fs_l0_new[\"values_net_worth_last_30days\"] = pan_india_fs_l0_new.apply(\n", "#     values_net_worth_last_30days, axis=1\n", "# )\n", "# pan_india_fs_l0_new[\"values_sales\"] = pan_india_fs_l0_new.apply(values_sales, axis=1)\n", "# pan_india_fs_l0_new[\"values_forecast_sales\"] = pan_india_fs_l0_new.apply(values_forecast_sales, axis=1)\n", "# pan_india_fs_l0_new[\"doi\"] = round(pan_india_fs_l0_new[\"doi\"], 1)\n", "# pan_india_fs_l0_new[\"doi\"] = np.where(\n", "#     pan_india_fs_l0_new[\"doi\"] >= 1000, \"-\", pan_india_fs_l0_new[\"doi\"]\n", "# )\n", "# pan_india_fs_l0_new[\"forward_looking_doi\"] = round(pan_india_fs_l0_new[\"forward_looking_doi\"], 1)\n", "# pan_india_fs_l0_new[\"forward_looking_doi\"] = np.where(\n", "#     pan_india_fs_l0_new[\"forward_looking_doi\"] >= 1000, \"-\", pan_india_fs_l0_new[\"forward_looking_doi\"]\n", "# )\n", "# pan_india_fs_l0_new.replace([np.inf, -np.inf], \"-\", inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final = final[final[\"Facility\"] != \"Pan India - Franchise Stores\"]\n", "final"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### NDD Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone = final.to_dict(orient=\"rows\")\n", "summary_ndd = doi_full_facility_ndd_new.to_dict(orient=\"rows\")\n", "summary_ndd_l0 = pan_india_ndd_l0_new.to_dict(orient=\"rows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### DS Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_DS = doi_full_facility_DS_new.to_dict(orient=\"rows\")\n", "summary_DS_l0 = pan_india_ds_l0_new.to_dict(orient=\"rows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### FS Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary_FS = doi_full_facility_fS_new.to_dict(orient=\"rows\")\n", "summary_FS_l0 = pan_india_fs_l0_new.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_date = (datetime.now() - timedelta(days=0)).strftime(\"%Y-%m-%d\")\n", "\n", "po_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "po_date_30 = (datetime.now() - <PERSON><PERSON><PERSON>(days=30)).strftime(\"%Y-%m-%d\")\n", "time_period_considered = po_date_30 + \" to \" + po_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["email_list = pb.from_sheets(\"1PvhbEQEuO8QcwKlHRhb2R_RFeDAy3CTafhKLNdjMm30\", \"emails\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_email = list(filter(None, email_list[\"email\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "import os\n", "from jinja2 import Template\n", "\n", "\n", "from_email = \"<EMAIL>\"\n", "\n", "# to_email = json.loads(secrets.get(\"doi_report_emails\"))\n", "\n", "# to_email = [\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "#     \"<EMAIL>\",\n", "# ]\n", "# cwd = os.getcwd()\n", "\n", "subject = \"DOI report for \" + current_date\n", "\n", "with open(os.path.join(cwd, \"po_vms_doi_daily_report.html\"), \"r\") as f:\n", "    t = Template(f.read())\n", "\n", "\n", "with open(\n", "    os.path.join(\n", "        cwd,\n", "        \"po_vms_doi_daily_report.html\",\n", "    ),\n", "    \"r\",\n", ") as f:\n", "    t = Template(f.read())\n", "\n", "\n", "rendered = t.render(\n", "    products=zone,\n", "    products1=summary_ndd,\n", "    products2=summary_ndd_l0,\n", "    products3=summary_DS,\n", "    products4=summary_DS_l0,\n", "    products5=summary_FS,\n", "    products6=summary_FS_l0,\n", "    time_period_considered=time_period_considered,\n", ")\n", "\n", "\n", "pb.send_email(\n", "    from_email,\n", "    to_email,\n", "    subject,\n", "    bcc=[\"<EMAIL>\"],\n", "    html_content=rendered,\n", ")\n", "\n", "print(\"mail sent\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}