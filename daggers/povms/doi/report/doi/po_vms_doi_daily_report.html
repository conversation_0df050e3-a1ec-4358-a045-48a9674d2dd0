<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}
    
</style>
</head>
<p>Hi All,</p>
<p>Please find below DOI (Days of Inventory) metric, which is calculated as followed :<br/>
    <b>DOI = Average Inventory Value for last 30 Days/Average Delivered GMV for last 30 days</b><br/>
<br/>Time period in consideration for today's report : {{time_period_considered}}
    
    
<!-- <br/>For Detailed DOI data Please go to the Tableau : -->
<!-- <br/>Link: https://tableau.grofer.io/#/views/InventoryDashboard/FacilitywiseDOI?:iid=1 -->
</p>

<p><b> Summary </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">EOD Net Worth</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(Last 30 Days)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Average Sales (Last 30 Days) </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_eod}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>


    <p></p>
    <p></p>
    <p></p>
    <p></p>
    <p></p>

<p><b> NDD Facility level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">EOD Net Worth</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(Last 30 Days)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Average Sales (Last 30 Days) </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products1 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
               <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_eod}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<p><b> NDD L0 level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">l0</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">EOD Net Worth</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(Last 30 Days)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Average Sales (Last 30 Days) </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products2 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.l0}}</b>
            </td>
               <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_eod}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

 <p></p>
    <p></p>
    <p></p>
    <p></p>

<p><b> E3 Facility level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">EOD Net Worth</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(Last 30 Days)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Average Sales (Last 30 Days) </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products3 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_eod}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

 <p></p>
    <p></p>
    <p></p>
    <p></p>

<p><b> E3 L0 level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">l0</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">EOD Net Worth</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(Last 30 Days)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Average Sales (Last 30 Days) </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products4 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.l0}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_eod}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

 <p></p>
    <p></p>
    <p></p>
    <p></p>


<p><b> GM Facility level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">EOD Net Worth</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(Last 30 Days)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Average Sales (Last 30 Days) </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products5 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_eod}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

    <p></p>
    <p></p>
    <p></p>
    <p></p>

<p><b> GM L0 level </b></p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">l0</span></th>
           <th class="tg-0lax"><span style="font-weight:bold">EOD Net Worth</span></th> 
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(Last 30 Days)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Average Sales (Last 30 Days) </span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products6 %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.l0}}</b>
            </td>
               <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_eod}}</b>
            </td> 
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.values_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>




<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>