dag_name: doi
dag_type: report
escalation_priority: high
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  name: sonal
  slack_id: UABB9AK9V
path: povms/doi/report/doi
paused: true
pool: povms_pool
project_name: doi
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 4 * * *
  start_date: '2020-12-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- po_vms_doi_daily_report.html
tags: []
template_name: notebook
version: 18
