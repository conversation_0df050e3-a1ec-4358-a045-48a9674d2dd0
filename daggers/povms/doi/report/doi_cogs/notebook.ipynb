{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "from datetime import datetime, date\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redash = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_date = (datetime.now() - timed<PERSON>ta(days=2)).strftime(\"%Y-%m-%d\")\n", "start_date = (datetime.now() - timed<PERSON>ta(days=31)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select facility_id,sum(actual_quantity) as actual_quantity ,sum(delivered_sales_quantity)  as delivered_sales_quantity,\n", "sum(actual_quantity*average_landing_price) as value_lp,sum(delivered_sales_quantity*average_landing_price) as cogs_value \n", "from metrics.doi_detail_report\n", "where date(date_of_snapshot) between '{start_date}' and '{end_date}'\n", "group by 1\n", "\"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = pd.read_sql(sql=query, con=redash)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping = pd.read_sql_query(\n", "    sql=\"\"\"\n", "select distinct facility_id,name,internal_facility_identifier from po.physical_facility\"\"\",\n", "    con=retail,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[\"facility_id\"] = np.where(results[\"facility_id\"] == 52, 48, results[\"facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility = results.merge(facility_mapping, on=[\"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility_df = results_Facility[\n", "    ~results_Facility.name.isin(\n", "        [\n", "            \"Super Store Delhi P1 - Warehouse\",\n", "            \"<PERSON><PERSON><PERSON> - Kolkata\",\n", "            \"Delhi Warehouse Bulk Bamnoli\",\n", "            \"Bulk NCR PL Warehouse\",\n", "        ]\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility_df = (\n", "    results_Facility_df.groupby([\"facility_id\", \"name\", \"internal_facility_identifier\"])[\n", "        \"actual_quantity\", \"delivered_sales_quantity\", \"value_lp\", \"cogs_value\"\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility_df.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility_df[\"doi_qty\"] = (\n", "    results_Facility_df.actual_quantity / results_Facility_df.delivered_sales_quantity\n", ")\n", "results_Facility_df[\"cogs\"] = results_Facility_df.value_lp / results_Facility_df.cogs_value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility_df.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_Facility_df_final = results_Facility_df[\n", "    [\n", "        \"internal_facility_identifier\",\n", "        \"actual_quantity\",\n", "        \"delivered_sales_quantity\",\n", "        \"value_lp\",\n", "        \"cogs_value\",\n", "        \"doi_qty\",\n", "        \"cogs\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india = results_Facility_df[\n", "    [\n", "        \"internal_facility_identifier\",\n", "        \"actual_quantity\",\n", "        \"delivered_sales_quantity\",\n", "        \"value_lp\",\n", "        \"cogs_value\",\n", "        \"doi_qty\",\n", "        \"cogs\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india[\"internal_facility_identifier\"] = \"Pan India\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india[\"actual_quantity\"] = results_Facility_df_final.actual_quantity.sum()\n", "pan_india[\"delivered_sales_quantity\"] = results_Facility_df_final.delivered_sales_quantity.sum()\n", "pan_india[\"value_lp\"] = results_Facility_df_final.value_lp.sum()\n", "pan_india[\"cogs_value\"] = results_Facility_df_final.cogs_value.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india[\"doi_qty\"] = pan_india.actual_quantity / pan_india.delivered_sales_quantity\n", "pan_india[\"cogs\"] = pan_india.value_lp / pan_india.cogs_value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india.drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df_doi = results_Facility_df_final.append(pan_india)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df_doi[\"actual_quantity_avg\"] = final_df_doi.actual_quantity / 30\n", "final_df_doi[\"actual_quantity_in_lakhs\"] = round(final_df_doi[\"actual_quantity_avg\"] / 100000, 2)\n", "\n", "final_df_doi[\"delivered_sales_quantity_avg\"] = final_df_doi.delivered_sales_quantity / 30\n", "final_df_doi[\"delivered_sales_quantity_in_lakhs\"] = round(\n", "    final_df_doi[\"delivered_sales_quantity_avg\"] / 100000, 2\n", ")\n", "\n", "final_df_doi[\"value_lp_avg\"] = final_df_doi.value_lp / 30\n", "final_df_doi[\"value_lp_in_Cr\"] = round(final_df_doi[\"value_lp_avg\"] / 10000000, 2)\n", "\n", "final_df_doi[\"cogs_value_avg\"] = final_df_doi.cogs_value / 30\n", "final_df_doi[\"cogs_value_in_Cr\"] = round(final_df_doi[\"cogs_value_avg\"] / 10000000, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df_doi[\"doi_qty\"] = np.where(\n", "    final_df_doi.delivered_sales_quantity == 0, \"-\", round(final_df_doi[\"doi_qty\"], 2)\n", ")\n", "final_df_doi[\"cogs\"] = np.where(\n", "    final_df_doi.delivered_sales_quantity == 0, \"-\", round(final_df_doi[\"cogs\"], 2)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df_doi.head(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_df_doi_final = final_df_doi[\n", "    [\n", "        \"internal_facility_identifier\",\n", "        \"actual_quantity_in_lakhs\",\n", "        \"delivered_sales_quantity_in_lakhs\",\n", "        \"value_lp_in_Cr\",\n", "        \"cogs_value_in_Cr\",\n", "        \"doi_qty\",\n", "        \"cogs\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = final_df_doi_final.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# start_date_str=start_date.strftime('%Y-%m-%d')\n", "# end_date_str=end_date.strftime('%Y-%m-%d')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_period_considered = start_date + \" and \" + end_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_period_considered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "\n", "# In[19]:\n", "\n", "\n", "sender = \"<EMAIL>\"\n", "\n", "# to=[\"<EMAIL>\"]\n", "to = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "cc = [\"<EMAIL>\"]\n", "\n", "subject = \"DOI in terms of COGS and Quantity \" + time_period_considered\n", "\n", "email_template = \"doi_cogs_quantity.html\"\n", "loader = jinja2.FileSystemLoader(\n", "    searchpath=\"/usr/local/airflow/dags/repo/dags/povms/doi/report/doi_cogs/\"\n", ")\n", "\n", "# loader = jinja2.FileSystemLoader(searchpath=\"/home/<USER>/Development/grofers/Reports/cogs_reports/\")\n", "tmpl_environ = jinja2.Environment(loader=loader)\n", "template = tmpl_environ.get_template(email_template)\n", "\n", "rendered = template.render(\n", "    products=items,\n", "    #     pan_india_doi=pan_india_doi,\n", "    time_period_considered=time_period_considered,\n", ")\n", "\n", "pb.send_email(\n", "    sender,\n", "    to,\n", "    subject,\n", "    html_content=rendered,\n", "    dryrun=False,\n", "    cc=cc,\n", "    bcc=None,\n", "    mime_subtype=\"mixed\",\n", "    mime_charset=\"utf-8\",\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}