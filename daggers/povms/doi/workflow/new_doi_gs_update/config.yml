alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: new_doi_gs_update
dag_type: workflow
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U01JWCA6AJG
path: povms/doi/workflow/new_doi_gs_update
paused: true
pool: povms_pool
project_name: doi
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 48 3 * * *
  start_date: '2021-08-11T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
