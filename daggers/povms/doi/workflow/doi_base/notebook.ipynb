{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# start_date='2019-06-01'\n", "# end_date='2019-06-01'\n", "\n", "end_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "start_date = (datetime.now() - timedelta(days=2)).strftime(\"%Y-%m-%d\")\n", "\n", "print(end_date, start_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping = pandas.read_sql(\n", "    sql=\"\"\"select id as retail_outlet_id,facility_id from pos_console_outlet\"\"\", con=con\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["query = \"\"\"\n", "-- select max(date_of_snapshot) from metrics.doi_daily_report\n", " WITH item_inv_outlet AS\n", "  (SELECT item_id,\n", "          date(snapshot_datetime) AS date_of_snapshot,\n", "          outlet_id,\n", "          metering,\n", "          sum(actual_quantity) AS actual_quantity,\n", "          sum(blocked_quantity) AS blocked_quantity,\n", "          sum(actual_networth) AS actual_networth,\n", "          sum(blocked_networth) AS blocked_networth\n", "   FROM reports_reports_item_inventory_snapshot ris\n", "   WHERE date(snapshot_datetime + interval '330 minute') BETWEEN '{start_date}' AND '{end_date}'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4),\n", "      outlet_filtering AS\n", "  (SELECT date_of_snapshot,\n", "          outlet_id,\n", "          facility_id\n", "   FROM\n", "     (SELECT date_of_snapshot,\n", "             outlet_id,\n", "             sum(actual_quantity) AS qty\n", "      FROM item_inv_outlet\n", "      GROUP BY 1,\n", "               2\n", "      HAVING sum(actual_quantity)>0)a\n", "   INNER JOIN\n", "     (SELECT id,\n", "             facility_id\n", "      FROM pos_console_outlet\n", "      WHERE upper(name) LIKE '%%SSC%%'\n", "        AND upper(name) NOT LIKE '%%AC%%'\n", "        AND upper(name) NOT LIKE '%%HOT%%'\n", "      GROUP BY 1,\n", "               2)pco ON a.outlet_id=pco.id) --   select outlet_id,date_of_snapshot from outlet_filtering group by 1,2 having count(*)>1;\n", " ,\n", "      item_inv AS\n", "  (SELECT item_id,\n", "          ijo.date_of_snapshot,\n", "          of.facility_id,\n", "          metering,\n", "          sum(actual_quantity) AS actual_quantity,\n", "          sum(blocked_quantity) AS blocked_quantity,\n", "          sum(actual_networth) AS actual_networth,\n", "          sum(blocked_networth) AS blocked_networth\n", "   FROM item_inv_outlet ijo\n", "   INNER JOIN outlet_filtering OF ON ijo.outlet_id=OF.outlet_id\n", "   AND ijo.date_of_snapshot=of.date_of_snapshot\n", "   WHERE actual_quantity>0\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4),\n", "      base_detail AS\n", "  (SELECT ii.*,\n", "          cf.name AS facility_name,\n", "          CASE\n", "              WHEN actual_networth>0 THEN actual_networth/actual_quantity\n", "              WHEN blocked_networth>0 THEN blocked_networth/blocked_quantity\n", "              ELSE 0\n", "          END AS avergae_landing_price\n", "   FROM item_inv ii\n", "   LEFT JOIN crates_facility cf ON ii.facility_id=cf.id)\n", "   ,\n", "      \n", "      level_of_table AS\n", "  (SELECT a.*\n", "   FROM\n", "     (SELECT a.*,\n", "             rank() over(PARTITION BY item_id,date_of_snapshot,facility_id\n", "                         ORDER BY metering DESC) AS rank_1\n", "      FROM base_detail AS a) AS a\n", "   WHERE rank_1=1 ),\n", "   \n", "      item_vendor AS\n", "  (SELECT item_id,\n", "          a.vendor_id,\n", "          facility_id,\n", "          (created_at+ interval '330 minute') as created_at\n", "   FROM consumer.vms_vendor_item_physical_facility_attributes_log as a\n", "   where active=1\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4),\n", "            \n", "      products_updated_dates AS\n", "  (SELECT a.item_id,\n", "          a.facility_id,\n", "          date_of_snapshot,\n", "          MAX(created_at+ interval '330 minute') AS created_list\n", "   FROM\n", "     (SELECT item_id,\n", "             facility_id,\n", "             date_of_snapshot\n", "      FROM level_of_table\n", "      GROUP BY 1,\n", "               2,\n", "               3)A\n", "   LEFT JOIN consumer.vms_vendor_item_physical_facility_attributes_log ip ON (a.item_id)=(ip.item_id)\n", "   AND (a.facility_id)=(ip.facility_id)\n", "   WHERE date(date_of_snapshot)-date(created_at+ interval '330 minute')>=0---to get latest item,product mapping of given the order date\n", "   GROUP BY 1,\n", "            2,\n", "            3\n", "            ),\n", "            \n", "      order_item_mapping AS\n", "  (SELECT DISTINCT od.*,\n", "                   ipm.vendor_id,\n", "                   created_list\n", "   FROM\n", "     (SELECT *\n", "      FROM level_of_table) od\n", "   INNER JOIN products_updated_dates prod \n", "   ON od.item_id=prod.item_id\n", "   AND od.facility_id=prod.facility_id\n", "   AND date(od.date_of_snapshot)=date(prod.date_of_snapshot)\n", "   INNER JOIN item_vendor ipm \n", "   ON prod.item_id=ipm.item_id\n", "   AND prod.facility_id=ipm.facility_id\n", "   AND date(prod.created_list)=date(ipm.created_at)\n", "   )\n", "   ,\n", "  item_vendor_level as \n", "  (\n", "  select a.*\n", "  from\n", "  (\n", "  select a.*,\n", "  row_number() over(partition by item_id,date_of_snapshot,facility_id order by created_list desc) as rank_2\n", "  from order_item_mapping as a\n", "  )as a\n", "  where rank_2=1\n", "  )\n", " ,\n", "  item_po_details as\n", " (\n", " select distinct\n", " vendor_id,\n", " facility_id,\n", " (created_at +interval '330 minute') as created_at,\n", " po_cycle\n", " from vms_vendor_manufacturer_physical_facility_attributes_log\n", " where active=1\n", "group by 1,2,3,4 )\n", " ,\n", "      vendor_po_cycle AS\n", "  (SELECT a.vendor_id,\n", "          a.facility_id,\n", "          date_of_snapshot,\n", "          MAX(ip.created_at+ interval '330 minute') AS created_list\n", "  FROM\n", "     (SELECT vendor_id,\n", "              facility_id,\n", "             date_of_snapshot\n", "      FROM item_vendor_level\n", "      GROUP BY 1,2,3)A\n", "  LEFT JOIN vms_vendor_manufacturer_physical_facility_attributes_log ip ON (a.vendor_id)=(ip.vendor_id) \n", "  and (a.facility_id)=(ip.facility_id)\n", "  WHERE date(date_of_snapshot)-date(created_at+ interval '330 minute')>=0---to get latest item,product mapping of given the order date\n", "  GROUP BY 1,2,3)\n", "    ,\n", "final_item_po_details  AS\n", "  (select a.* \n", "  from\n", "  (SELECT distinct od.*,\n", "  ipm.po_cycle,\n", "  ipm.created_at as created_at,\n", "  row_number()over(partition by od.item_id,ipm.facility_id,od.date_of_snapshot,ipm.vendor_id order by ipm.created_at desc) as rank_3\n", "  FROM\n", "     (SELECT * from item_vendor_level ) od\n", "  INNER JOIN vendor_po_cycle prod ON od.vendor_id=prod.vendor_id and od.facility_id=prod.facility_id\n", "  and date(od.date_of_snapshot)=date(prod.date_of_snapshot)\n", "  INNER JOIN item_po_details ipm ON prod.vendor_id=ipm.vendor_id and prod.facility_id=ipm.facility_id\n", "  AND date(prod.created_list)=date(ipm.created_at)\n", "  )as a\n", "  where rank_3=1\n", "  )\n", "  \n", "  \n", " ,\n", "  item_bucket_details as\n", "(\n", "select\n", "ivp.*,\n", "buckets,\n", "bucket_x,\n", "is_gb\n", "from final_item_po_details as ivp\n", "left join\n", "(\n", "select\n", "a.*\n", "from\n", "(\n", "SELECT\n", "item_id,\n", "facility_id,\n", "date(order_date) as order_date,\n", "buckets,\n", "bucket_x,\n", "is_gb,\n", "dense_rank()over(partition by date(order_date) order by order_date desc)as rank_1\n", "FROM rpc_daily_availability AS a\n", "WHERE date(a.order_date) BETWEEN '{start_date}' AND '{end_date}'\n", ") as a\n", "where rank_1=1\n", ") as buk\n", "on ivp.item_id=buk.item_id\n", "and ivp.facility_id=buk.facility_id\n", "and date(ivp.date_of_snapshot)=date(buk.order_date)\n", ")\n", ",\n", "  item_vendor_name as\n", "(\n", "select\n", "doi.*,\n", "vendor_name\n", "from item_bucket_details as doi\n", "left join\n", "(\n", "select vv. id as vendor_id,vendor_name,facility_id\n", "from vms_vendor as vv\n", "left join pos_console_outlet as pco on vv.outlet_id=pco.id\n", ") vn\n", "on doi.vendor_id=vn.vendor_id\n", "and doi.facility_id=vn.facility_id\n", ")\n", "  \n", "\n", ",\n", "\n", "  item_brand_details as\n", "(\n", "select\n", "ivl.*,\n", "item_name,\n", "brand_id,\n", "brand_name,\n", "manufacture_name,\n", "manufacture_id as manufacture_id_final\n", "from  item_vendor_name ivl\n", "left join\n", "(\n", "select\n", "*\n", "from\n", "(\n", "select\n", "Distinct\n", "item_id,\n", "rpp.name as item_name,\n", "row_number() over(partition by item_id order by item_name desc) as rank_1,\n", "brand_id,\n", "rpb.name as brand_name,\n", "rpm.name as manufacture_name,\n", "rpm.id as manufacture_id\n", "from rpc_product_product as rpp\n", "inner join rpc_product_brand as rpb on rpp.brand_id=rpb.id\n", "inner join consumer.rpc_product_manufacturer as rpm on rpm.id=rpb.manufacturer_id\n", ")item_details\n", "where rank_1=1\n", ")item_details\n", "on ivl.item_id=item_details.item_id\n", ")\n", "  \n", "select * from item_brand_details \n", "\n", "\n", "            \n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values = pandas.read_sql(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values[\"date_of_snapshot\"] = pandas.to_datetime(inventory_values.date_of_snapshot)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con1 = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "\n", "with sto_detail_table as\n", "(\n", "select\n", "  a.outlet_id as outlet_id,\n", "  a.item_id,\n", "   date(a.updated_at) as updated_at,\n", "  a.blocked_quantity as sto_quantity\n", "  from ims_ims_sto_blocked_item_log as a\n", "  inner join \n", "  (select outlet_id, item_id, date(max(updated_at)) as updated_at from ims_ims_sto_blocked_item_log \n", "   where date(updated_at+ interval '330 minute') between '{start_date}' and '{end_date}'\n", "  group by outlet_id,item_id\n", "  ) b\n", "  on a.outlet_id=b.outlet_id\n", "  and a.item_id=b.item_id\n", "  and a.updated_at=b.updated_at\n", "  where date(a.updated_at+ interval '330 minute') between '{start_date}' and '{end_date}'\n", "  group by 1,2,3,4\n", "  )\n", "  \n", " \n", " \n", "  select b.facility_id,\n", "  item_id,\n", "  a.updated_at,\n", "  sum(sto_quantity) as sto_quantity\n", "  from sto_detail_table as a\n", "  left join pos_console_outlet  as b on a.outlet_id=b.id\n", "  group by 1,2,3\n", "  \n", "  \n", " \"\"\".format(\n", "    start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data = pandas.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility = sto_data.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility.item_id = sto_data_facility.item_id.astype(int)\n", "sto_data_facility.facility_id = sto_data_facility.facility_id.astype(int)\n", "inventory_values.item_id = inventory_values.item_id.astype(int)\n", "inventory_values.facility_id = inventory_values.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_data = inventory_values.merge(\n", "    sto_data_facility,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"updated_at\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["ims_selling_price_query = \"\"\"with dates as \n", "(SELECT DISTINCT date(install_ts+ interval '330 minute') AS order_date\n", "FROM oms_suborder\n", "   WHERE date(install_ts+ interval '330 minute') BETWEEN '{start_date}' AND '{end_date}'),\n", "selling_price_hist as \n", "(select inventory_id,pii.selling_price,effective_from,effective_till,order_date,variant_id,threshold_quantity,outlet_id from \n", "dates d\n", "left join consumer.pos_ims_selling_price_history  pisph\n", "on d.order_date>=pisph.effective_from\n", "and d.order_date<=pisph.effective_till\n", "left join consumer.pos_ims_inventory pii\n", "on pisph.inventory_id=pii.id),\n", "\n", "item_sp_details as \n", "(select item_id,order_date,outlet_id,avg(selling_price) as selling_price,max(threshold_quantity) as threshold_quantity from \n", "(select sph.*,item_id from selling_price_hist sph\n", "left join pos_product_product ppp\n", "on sph.variant_id=ppp.variant_id)a\n", "group by 1,2,3)\n", "\n", "select item_id,facility_id,order_date,avg(selling_price) as selling_price,max(threshold_quantity) as threshold_quantity from item_sp_details spd\n", "left join pos_console_outlet pco\n", "on spd.outlet_id=pco.id\n", "group by 1,2,3\n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_selling_price_values = pandas.read_sql_query(sql=ims_selling_price_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_engine_uri = \"******************************************************************************************************************************/pricing\"\n", "pricing_engine = sqla.create_engine(pricing_engine_uri)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["pricing_selling_price_query = \"\"\"WITH max_rows AS (\n", "    SELECT\n", "      cms_store_id,\n", "      cms_product_id,\n", "      max(id) AS max_id\n", "    FROM pricing_pricing_domain_productchangelog\n", "    WHERE date(install_ts+ interval '330 minute') between'{start_date}' and  '{end_date}'\n", "    GROUP BY 1, 2)\n", "    ,\n", " \n", "all_filled as\n", "(\n", "SELECT \n", "retail_outlet_id,\n", "retail_item_id,\n", "max_rows.cms_store_id,\n", "max_rows.cms_product_id,\n", "new_price,\n", "new_mrp,\n", "install_ts,\n", "(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(evaluation_log,'rule'),'start_date')) as start_date,\n", "(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(evaluation_log,'rule'),'end_date')) as end_date\n", "FROM pricing_pricing_domain_productchangelog pdp\n", "  INNER JOIN max_rows ON max_rows.max_id = pdp.id\n", "  )\n", "  \n", "  select * from all_filled\n", "\n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values = pandas.read_sql(sql=pricing_selling_price_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values[\"start_date\"] = pandas.to_datetime(\n", "    pricing_selling_price_values.start_date\n", ")\n", "pricing_selling_price_values[\"end_date\"] = pandas.to_datetime(pricing_selling_price_values.end_date)\n", "\n", "\n", "# po_date=(datetime.now()-timed<PERSON>ta(days=2)).strftime('%Y-%m-%d')\n", "# po_date_30=(datetime.now()-timed<PERSON>ta(days=3)).strftime('%Y-%m-%d')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["delivered_sales_query = \"\"\"\n", "  WITH delivered_orders AS\n", "  (SELECT DISTINCT \n", "    os.id AS grofers_order_id,\n", "    date(oo.update_ts+ interval '330 minute') as order_date\n", "   FROM oms_suborder os\n", "   INNER JOIN oms_order oo ON os.order_id=oo.id\n", "   WHERE oo.current_status='DELIVERED'\n", "     AND os.current_status='AT_DELIVERY_CENTER'\n", "     AND date(oo.update_ts+ interval '330 minute') BETWEEN '{start_date}' AND '{end_date}') \n", "     ,\n", "     \n", "    invoice AS\n", "  (SELECT id ,\n", "          outlet_id,\n", "          order_date\n", "  FROM pos_pos_invoice as a\n", "  inner join delivered_orders as b\n", "  on a.grofers_order_id=b.grofers_order_id\n", "  WHERE invoice_type_id=1\n", "     )\n", ",\n", "\n", "     \n", "     \n", "      product_invoice_details AS\n", "  (SELECT variant_id,\n", "          invoice_id,\n", "          sum(quantity) AS sales_qty,\n", "          sum(quantity* selling_price) AS sales\n", "  FROM pos_pos_invoice_product_details pd\n", "  where store_offer_id is null\n", "  GROUP BY 1,\n", "            2)\n", "            ,\n", "            -- select count(*),count(distinct invoice_id||variant_id ) from  product_invoice_details\n", "        invoice_product_detail as\n", "        (\n", "        select distinct \n", "        OUTLET_ID,\n", "        order_date,\n", "        VARIANT_ID,\n", "        sum(SALES_QTY) as SALES_QTY ,\n", "        sum(SALES) as SALES\n", "        from invoice as a\n", "        inner join product_invoice_details as b\n", "        on a.id =b.invoice_id \n", "        group by 1,2,3\n", "        )\n", "        ,\n", "        \n", "      sales AS\n", "  (SELECT ppp.item_id,\n", "          i.order_date,\n", "          pco.facility_id,\n", "          sum(i.sales_qty) AS sales_qty,\n", "          sum(i.sales) AS sales\n", "  FROM invoice_product_detail as i\n", "  INNER JOIN pos_product_product ppp ON i.variant_id=ppp.variant_id\n", "  LEFT JOIN pos_console_outlet pco ON i.outlet_id=pco.id\n", " WHERE facility_id is not null\n", "  GROUP BY 1,\n", "            2,\n", "            3)\n", "select * from sales\n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales = pandas.read_sql(sql=delivered_sales_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales[\"order_date\"] = pandas.to_datetime(delivered_sales.order_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["checkout_sales_query = \"\"\"\n", " WITH order_details_without_offer AS\n", " (SELECT DISTINCT order_id ,\n", "                   date(a.created_at+ interval '330 minute') AS order_date,\n", "                   cast(outlet AS int) AS outlet ,\n", "                  item_id ::int,\n", "                   product_id ,\n", "                   quantity ,\n", "                   offer_id ,\n", "                   mrp,\n", "                   price\n", "   FROM consumer.ims_order_details AS a\n", "   LEFT JOIN consumer.ims_ims_order_actuals AS b ON a.id=b.order_details_id\n", "   WHERE date(a.created_at+ interval '330 minute') between '{start_date}' and '{end_date}'\n", "     AND (offer_id IS NULL\n", "          OR offer_id='') \n", "          and item_id is not null\n", "        ) \n", "      \n", "    \n", ",\n", "     order_details_without_offer_facility AS\n", "  ( SELECT order_id,\n", "          order_date,\n", "          item_id,\n", "          facility_id,\n", "          sum(quantity) as quantity,\n", "          avg(mrp) as mrp,\n", "          avg(price) as price\n", "  FROM order_details_without_offer AS a\n", "  LEFT JOIN consumer.pos_console_outlet AS b ON a.outlet=b.id\n", "  group by 1,2,3,4\n", "  )\n", "  \n", "\n", "  ,\n", "   \n", "     order_details_with_offer AS\n", "  ( SELECT DISTINCT order_id ,\n", "                  date(a.created_at+ interval '330 minute') AS order_date,\n", "                  cast(outlet AS int) AS outlet ,\n", "                  item_id ::int,\n", "                  product_id ,\n", "                  quantity ,\n", "                  offer_id ,\n", "                  mrp,\n", "                  price\n", "  FROM consumer.ims_order_details AS a\n", "  LEFT JOIN consumer.ims_ims_order_actuals AS b ON a.id=b.order_details_id\n", "WHERE date(a.created_at+ interval '330 minute') between '{start_date}' and '{end_date}'\n", "     AND b.offer_id IS NOT NULL\n", "     AND b.offer_id <> ''\n", " ) \n", "            ,\n", "       \n", "  order_details_with_offer_facility AS\n", "  ( SELECT a.*,\n", "  facility_id\n", "  FROM order_details_with_offer AS a\n", "  LEFT JOIN consumer.pos_console_outlet AS b ON a.outlet=b.id\n", ")\n", "  \n", "--   select * from order_details_with_offer_facility\n", ",\n", "\n", "                         \n", "                         offer_details AS\n", "  (SELECT DISTINCT offer_reference_id :: varchar,\n", "                  cast(field_val AS int) AS item_id\n", "  FROM consumer.offer\n", "  LEFT JOIN consumer.offer_field o_field ON o_field.offer_id=offer.id\n", "  LEFT JOIN consumer.offer_type_field otf ON o_field.offer_type_field_id = otf.id \n", "  WHERE otf.field_name LIKE '%%product%%')\n", " \n", "  \n", "  ,\n", "                         \n", "item_offer_details AS\n", "  ( SELECT distinct\n", "    cast(a.order_id AS bigint) AS order_id ,\n", "          cast(a.order_date AS date) AS order_date,\n", "          cast(a.outlet AS int) AS outlet ,\n", "          cast(coalesce(b.item_id,0) AS int) AS item_id,\n", "          cast(a.product_id AS int) AS product_id ,\n", "          cast(a.quantity AS int) AS quantity ,\n", "          cast(a.offer_id AS varchar) AS offer_id ,\n", "          cast(a.mrp AS float) AS mrp,\n", "          cast(a.price AS float) AS price\n", "  FROM order_details_with_offer AS a\n", "  LEFT JOIN offer_details AS b ON a.offer_id=b.offer_reference_id \n", "  ) \n", "  \n", "\n", "  \n", "  ,\n", "  \n", "\n", "  item_mrp as\n", "  (\n", "  select distinct\n", "  a.*,\n", "  avg(variant_mrp) as item_mrp\n", "  from item_offer_details a\n", "  left join rpc_product_product b\n", "  on a.item_id=b.item_id \n", "  group by 1,2,3,4,5,6,7,8,9\n", "  )\n", "  ,\n", "  \n", "offer_detail as \n", "(\n", "select distinct\n", "offer_id,\n", "offer_type_id,\n", "quantity_field_val,\n", "offer_reference_id ,\n", "offer_type_identifier,\n", "offer_name,\n", "description,\n", "store_id as outlet,\n", "facility_id,\n", "percentage_discount\n", "from\n", "(\n", "select distinct\n", "offer.id as offer_id,\n", "offer.offer_type_id,\n", "offer_field.field_val as quantity_field_val,\n", "offer_reference_id ,\n", "offer_type_identifier,\n", "offer_type.name as offer_name,\n", "offer_type.description,\n", "store_offer.store_id,\n", "store_offer.facility_id,\n", "store_offer_field.field_val as percentage_discount,\n", "offer_type_field.field_name,\n", "offer_type_field.field_type,\n", "row_number() over(partition by offer.offer_reference_id,store_offer.store_id order by offer_field.field_val ) rank_1\n", "from \n", "(select distinct id,offer_type_id,offer_reference_id \n", "from offer \n", "where offer.offer_reference_id in (select distinct offer_id from item_mrp)\n", "and id is not null) as offer\n", "left join \n", "(select distinct offer_id,field_val,offer_type_field_id,id from offer_field\n", "where offer_field.offer_type_field_id IN\n", "    (SELECT id\n", "     FROM offer_type_field\n", "     WHERE field_name like '%%quantity%%' group by 1)\n", "      OR offer_field.id IS NULL)  as offer_field on offer.id=offer_field.offer_id\n", "left join \n", "(select distinct id,offer_type_identifier,name,description \n", "from offer_type ) as offer_type on offer.offer_type_id=offer_type.id\n", "left join (select distinct store_offer.id,store_id,offer_id ,facility_id\n", "from store_offer\n", "left join consumer.pos_console_outlet pco\n", "on pco.id=store_offer.store_id \n", "and store_offer.id is not null) as store_offer on offer.id=store_offer.offer_id\n", "left join (select distinct field_val,\n", "store_offer_id,offer_type_field_id \n", "from store_offer_field) as store_offer_field \n", "on store_offer.id = store_offer_field.store_offer_id\n", "left join (select distinct id,field_name,field_type from offer_type_field) as offer_type_field  on store_offer_field.offer_type_field_id=offer_type_field.id\n", " group by 1,2,3,4,5,6,7,8,9,10,11,12) as a\n", "where rank_1=1\n", ")\n", "--  select * from offer_detail where offer_reference_id='G00115805'\n", "  \n", "-- -- -- -- -- select outlet,offer_reference_id from offer_detail group by 1,2 having count(*)>1\n", "  \n", "  ,\n", "base_info as\n", "(\n", "    select base.*, \n", "        case when total_item_mrp = 0 \n", "                or total_item_mrp is null then 0 \n", "            else cast(item_mrp as decimal(18,2))/cast(total_item_mrp as decimal(18,2)) \n", "        end as item_part,\n", "        case when offer_type_id=1 then quantity::int\n", "        when offer_type_id in (2,3,4,5,6,7,8) then (quantity_field_val::int*quantity::int)::int end as item_quantity,\n", "        case when offer_type_id=1 then price\n", "        when offer_type_id=2 then (item_part::float*price::float)/quantity_field_val::int\n", "        when offer_type_id=3 then (mrp::float-(mrp*10)::float/100)/quantity_field_val::int\n", "        when offer_type_id=4 then (item_part::float*price::float)::float/quantity_field_val::int\n", "        when offer_type_id=5 then (price::float/quantity_field_val::int)::float\n", "        when offer_type_id=6 then (item_part::float*price::float)::float\n", "        when offer_type_id=7 then (item_part::float*price::float)::float\n", "        when offer_type_id=8 then (item_part::float*price::float)::float end as item_price\n", "    from\n", "    (\n", "        select distinct \n", "            a.*,\n", "            sum(item_mrp)over(partition by order_id,product_id,a.offer_id) as total_item_mrp,\n", "            b.offer_type_id,\n", "            quantity_field_val,\n", "            offer_reference_id,\n", "            offer_name,\n", "            description,\n", "            percentage_discount,\n", "            facility_id\n", "        from item_mrp as a\n", "        left join offer_detail as b\n", "        on a.offer_id=b.offer_reference_id\n", "        and a.outlet=b.outlet\n", "    ) base \n", ")\n", "\n", "-- select * from base_info\n", "-- -- -- -- select order_id,offer_id,item_id from base_info group by 1,2,3 having count(*)>1\n", "\n", ",\n", "final_info as\n", "(\n", "select distinct\n", "order_id,\n", "order_date,\n", "item_id,\n", "facility_id,\n", "sum(item_quantity) as quantity,\n", "avg(item_mrp) as mrp,\n", "avg(item_price) as price\n", "from base_info\n", "group by 1,2,3,4\n", ")\n", "\n", "  ,\n", "  \n", " final_item_details AS\n", "  (SELECT *\n", "  FROM\n", "     ( SELECT *\n", "      FROM order_details_without_offer_facility )\n", "  UNION all\n", "     ( SELECT  *\n", "      FROM final_info) ) \n", "      \n", "      \n", "      ,\n", "-- -- -- -- select item_id,order_id from final_item_details group by 1,2 having count(*)>1\n", "\n", "final_sales_table as\n", "(\n", "select distinct\n", "order_date,\n", "item_id,\n", "facility_id,\n", "sum(quantity) as quantity,\n", "avg(mrp) as mrp,\n", "avg(price) as price\n", "from final_item_details\n", "group by 1,2,3\n", ")\n", "\n", "select * from final_sales_table \n", "\n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales = pandas.read_sql(sql=checkout_sales_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales[\"order_date\"] = pandas.to_datetime(checkout_sales.order_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility = checkout_sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["billed_sales_query = \"\"\" \n", " with invoice AS\n", "  (SELECT id ,\n", "          outlet_id,\n", "          grofers_order_id,\n", "          date(created_at+ interval '330 minute') AS pos_timestamp\n", "   FROM pos_pos_invoice\n", "   WHERE invoice_type_id=1\n", "     AND date(created_at+ interval '330 minute') between '{start_date}' AND '{end_date}'),\n", "      delivered_orders AS\n", "  (SELECT os.id AS grofers_order_id\n", "   FROM oms_suborder os\n", "   INNER JOIN oms_order oo ON os.order_id=oo.id),\n", "      product_invoice_details AS\n", "  (SELECT variant_id,\n", "          invoice_id,\n", "          sum(quantity) AS sales_qty,\n", "          sum(quantity* selling_price) AS sales\n", "   FROM pos_pos_invoice_product_details pd\n", "   GROUP BY 1,\n", "            2),\n", "      sales AS\n", "  (SELECT ppp.item_id,\n", "          i.pos_timestamp,\n", "          pco.facility_id,\n", "          sum(pid.sales_qty) AS sales_qty,\n", "          sum(pid.sales) AS sales\n", "   FROM delivered_orders\n", "   INNER JOIN invoice i ON delivered_orders.grofers_order_id=i.grofers_order_id\n", "   INNER JOIN product_invoice_details pid ON i.id=pid.invoice_id\n", "   INNER JOIN pos_product_product ppp ON pid.variant_id=ppp.variant_id\n", "   LEFT JOIN pos_console_outlet pco ON i.outlet_id=pco.id\n", "   GROUP BY 1,\n", "            2,\n", "            3)\n", "            select * from sales\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales = pandas.read_sql(sql=billed_sales_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales[\"pos_timestamp\"] = pandas.to_datetime(billed_sales.pos_timestamp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_data.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_data[\"item_id\"] = inventory_values.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales[\"item_id\"] = delivered_sales.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered = inventory_data.merge(\n", "    delivered_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_df = doi_delivered[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"actual_networth\",\n", "        \"blocked_networth\",\n", "        \"avergae_landing_price\",\n", "        \"facility_name\",\n", "        \"sales_qty\",\n", "        \"sales\",\n", "        \"sto_quantity\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id_final\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_df[\"delivered_selling_price\"] = doi_delivered_df.sales / doi_delivered_df.sales_qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_df = doi_delivered_df.rename(columns={\"sales_qty\": \"delviered_sales_quantity\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility[\"item_id\"] = checkout_sales_facility.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout = doi_delivered_df.merge(\n", "    checkout_sales_facility,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout = doi_delivered_Checkout.rename(\n", "    columns={\"quantity\": \"checkout_sales\", \"price\": \"checkout_sales_price\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_df = doi_delivered_Checkout[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"actual_networth\",\n", "        \"blocked_networth\",\n", "        \"avergae_landing_price\",\n", "        \"facility_name\",\n", "        \"delviered_sales_quantity\",\n", "        \"order_date\",\n", "        \"delivered_selling_price\",\n", "        \"checkout_sales\",\n", "        \"sto_quantity\",\n", "        \"checkout_sales_price\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id_final\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales[\"item_id\"] = billed_sales.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed = doi_delivered_Checkout_df.merge(\n", "    billed_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"pos_timestamp\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed = doi_delivered_Checkout_billed[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"actual_networth\",\n", "        \"blocked_networth\",\n", "        \"avergae_landing_price\",\n", "        \"facility_name\",\n", "        \"delviered_sales_quantity\",\n", "        \"delivered_selling_price\",\n", "        \"checkout_sales\",\n", "        \"checkout_sales_price\",\n", "        \"sales_qty\",\n", "        \"sales\",\n", "        \"sto_quantity\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id_final\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"billed_selling_price\"] = (\n", "    doi_delivered_Checkout_billed.sales / doi_delivered_Checkout_billed.sales_qty\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed = doi_delivered_Checkout_billed.rename(\n", "    columns={\"sales_qty\": \"billed_Sale_quantity\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed_df = doi_delivered_Checkout_billed[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"avergae_landing_price\",\n", "        \"delviered_sales_quantity\",\n", "        \"billed_Sale_quantity\",\n", "        \"billed_selling_price\",\n", "        \"delivered_selling_price\",\n", "        \"checkout_sales\",\n", "        \"checkout_sales_price\",\n", "        \"sto_quantity\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id_final\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed_df.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values[\"start_date\"] = pandas.to_datetime(\n", "    pricing_selling_price_values.start_date\n", ")\n", "pricing_selling_price_values[\"end_date\"] = pandas.to_datetime(pricing_selling_price_values.end_date)\n", "pricing_selling_price_values[\"install_ts\"] = pandas.to_datetime(\n", "    pricing_selling_price_values.install_ts\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility = pricing_selling_price_values.merge(\n", "    facility_mapping, on=[\"retail_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility = pricing_selling_price_facility.rename(\n", "    columns={\"new_price\": \"selling_price\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility = pricing_selling_price_facility[\n", "    [\n", "        \"retail_outlet_id\",\n", "        \"retail_item_id\",\n", "        \"install_ts\",\n", "        \"selling_price\",\n", "        \"facility_id\",\n", "        \"start_date\",\n", "        \"end_date\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp = doi_delivered_Checkout_billed_df.merge(\n", "    pricing_selling_price_facility,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"retail_item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df = doi_sp[\n", "    (doi_sp.date_of_snapshot <= doi_sp.end_date) & (doi_sp.date_of_snapshot >= doi_sp.start_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup = (\n", "    doi_sp_df.groupby([\"item_id\", \"date_of_snapshot\", \"facility_id\"])[[\"install_ts\"]]\n", "    .max()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_filtered = doi_sp_df[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"avergae_landing_price\",\n", "        \"delviered_sales_quantity\",\n", "        \"delivered_selling_price\",\n", "        \"billed_Sale_quantity\",\n", "        \"checkout_sales\",\n", "        \"checkout_sales_price\",\n", "        \"selling_price\",\n", "        \"start_date\",\n", "        \"end_date\",\n", "        \"install_ts\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup.install_ts = pandas.to_datetime(doi_sp_df_rollup.install_ts, utc=True).dt.date\n", "doi_sp_df_filtered.install_ts = pandas.to_datetime(doi_sp_df_filtered.install_ts, utc=True).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if doi_sp_df_rollup.shape[0] == 0:\n", "    doi_selling_price_merge = doi_sp_df_filtered\n", "else:\n", "    doi_selling_price_merge = doi_sp_df_filtered.merge(\n", "        doi_sp_df_rollup,\n", "        on=[\"item_id\", \"facility_id\", \"date_of_snapshot\", \"install_ts\"],\n", "        how=\"inner\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge = doi_selling_price_merge[\n", "    [\"date_of_snapshot\", \"item_id\", \"facility_id\", \"selling_price\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_pricing_check = doi_delivered_Checkout_billed_df.merge(\n", "    doi_selling_price_merge,\n", "    on=[\"date_of_snapshot\", \"item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_selling_price_values = ims_selling_price_values.rename(\n", "    columns={\"selling_price\": \"ims_selling_price\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_selling_price_values[\"order_date\"] = pandas.to_datetime(ims_selling_price_values.order_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge_ims = doi_pricing_check.merge(\n", "    ims_selling_price_values,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge_ims.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge_ims[\"final_selling_price\"] = np.where(\n", "    doi_selling_price_merge_ims.checkout_sales_price.isna(),\n", "    np.where(\n", "        doi_selling_price_merge_ims.delivered_selling_price.isna(),\n", "        np.where(\n", "            doi_selling_price_merge_ims.billed_selling_price.isna(),\n", "            np.where(\n", "                doi_selling_price_merge_ims.ims_selling_price.isna(),\n", "                doi_selling_price_merge_ims.selling_price,\n", "                doi_selling_price_merge_ims.ims_selling_price,\n", "            ),\n", "            doi_selling_price_merge_ims.billed_selling_price,\n", "        ),\n", "        doi_selling_price_merge_ims.delivered_selling_price,\n", "    ),\n", "    doi_selling_price_merge_ims.checkout_sales_price,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge_ims.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge_ims.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final = doi_selling_price_merge_ims[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"avergae_landing_price\",\n", "        \"delviered_sales_quantity\",\n", "        \"billed_Sale_quantity\",\n", "        \"checkout_sales\",\n", "        \"final_selling_price\",\n", "        \"sto_quantity\",\n", "        \"threshold_quantity\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id_final\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final = doi_df_final.rename(\n", "    columns={\n", "        \"delviered_sales_quantity\": \"delivered_sales_quantity\",\n", "        \"checkout_sales\": \"checkout_sales_quantity\",\n", "        \"avergae_landing_price\": \"average_landing_price\",\n", "        \"manufacture_id_final\": \"manufacture_id\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final[\"threshold_quantity\"] = doi_df_final[\"threshold_quantity\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_query = \"\"\"WITH PRODUCT_Category AS\n", " (SELECT P.ID AS PID,\n", "         P.NAME AS PRODUCT,\n", "         P.UNIT AS Unit,\n", "         C2.NAME AS L2,\n", "         (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "         C.NAME AS L0,\n", "         P.BRAND AS brand,\n", "         P.MANUFACTURER AS manf,\n", "         pt.name as product_type\n", "  FROM GR_PRODUCT P\n", "  INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "  INNER JOIN GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "  AND PCM.IS_PRIMARY=TRUE\n", "  INNER JOIN GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "  INNER JOIN GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "  inner join gr_product_type pt on p.type_id=pt.id\n", "\n", "  )\n", "\n", "SELECT item_id,\n", "      product_id,\n", "      (cat.product || ' ' || cat.unit) AS name,\n", "      cat.L0,\n", "      cat.l1,\n", "      cat.l2 ,\n", "      cat.brand,\n", "      cat.manf,\n", "      cat.product_type\n", "FROM rpc_item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_mapping = pandas.read_sql(sql=catg_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_mapping.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg = doi_df_final.merge(\n", "    catg_mapping[[\"item_id\", \"l0\", \"l1\", \"l2\", \"product_type\"]],\n", "    on=[\"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["doi_df_final is the dataframe which is to be populated in the table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg = doi_df_final_catg[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"average_landing_price\",\n", "        \"delivered_sales_quantity\",\n", "        \"billed_Sale_quantity\",\n", "        \"checkout_sales_quantity\",\n", "        \"final_selling_price\",\n", "        \"threshold_quantity\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"sto_quantity\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final = doi_df_final_catg.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final[\"brand_id\"] = doi_df_final_catg_final.brand_id.fillna(0)\n", "doi_df_final_catg_final[\"manufacture_id\"] = doi_df_final_catg_final.manufacture_id.fillna(0)\n", "doi_df_final_catg_final[\"brand_id\"] = doi_df_final_catg_final.brand_id.astype(int)\n", "doi_df_final_catg_final[\"manufacture_id\"] = doi_df_final_catg_final.manufacture_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final[\"brand_name\"] = doi_df_final_catg_final.brand_name.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"date_of_snapshot\", \"type\": \"timestamp\"},\n", "    {\"name\": \"facility_id\", \"type\": \"int\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"metering\", \"type\": \"boolean\"},\n", "    {\"name\": \"actual_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"average_landing_price\", \"type\": \"float\"},\n", "    {\"name\": \"delivered_sales_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"billed_Sale_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"checkout_sales_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"final_selling_price\", \"type\": \"float\"},\n", "    {\"name\": \"threshold_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"sto_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"vendor_id\", \"type\": \"int\"},\n", "    {\"name\": \"po_cycle\", \"type\": \"int\"},\n", "    {\"name\": \"buckets\", \"type\": \"varchar(100)\"},\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar(100)\"},\n", "    {\"name\": \"is_gb\", \"type\": \"boolean\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"brand_id\", \"type\": \"int\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"manufacture_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"manufacture_id\", \"type\": \"int\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import pencilbox as pb\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"doi_detail_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"date_of_snapshot\", \"item_id\", \"facility_id\"],\n", "    \"sortkey\": [\"date_of_snapshot\"],\n", "    \"incremental_key\": \"date_of_snapshot\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(doi_df_final_catg_final, **kwargs)\n", "# # # # or\n", "# # # pb.to_redshift(local_filename, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "select \n", "facility_id,\n", "facility_name,\n", "sum(case when date_of_snapshot=current_date-1 then (average_landing_price*actual_quantity)end) as net_worth,\n", "sum(case when (date_of_snapshot<=current_date-1 and date_of_snapshot >=current_date-30) then (average_landing_price*actual_quantity)end) as net_worth_last_30days,\n", "sum(case when (date_of_snapshot<=current_date-1 and date_of_snapshot >=current_date-30) then final_selling_price*delivered_sales_quantity end ) as last_30_sales,\n", "sum(case when (date_of_snapshot<=current_date-1 and date_of_snapshot >=current_date-30) then (average_landing_price*actual_quantity) end)/sum(case when (date_of_snapshot<=current_date-1 and date_of_snapshot >=current_date-30) then final_selling_price*delivered_sales_quantity end ) as doi\n", "from \n", "metrics.doi_detail_report\n", "where facility_id != 27\n", "group by 1,2\n", "order by 1,2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_facility = pandas.read_sql_query(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_facility.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping = pandas.DataFrame(\n", "    {\n", "        \"Facility\": [\n", "            \"Ahmedabad\",\n", "            \"Bangalore\",\n", "            \"Bangalore B2\",\n", "            \"Chennai \",\n", "            \"Chennai C2\",\n", "            \"<PERSON><PERSON> \",\n", "            \"Delhi P1\",\n", "            \"Delhi P3 \",\n", "            \"Faridabad\",\n", "            \"Gurgaon G1 \",\n", "            \"Gurgaon G2 \",\n", "            \"Gurgaon G3 \",\n", "            \"Gurgaon G4 \",\n", "            \"Gurgaon G4 B\",\n", "            \"Training\",\n", "            \"Hyderabad\",\n", "            \"Hyderabad H2\",\n", "            \"Jaipur\",\n", "            \"<PERSON><PERSON><PERSON>\",\n", "            \"Kolkata\",\n", "            \"Kolkata K2\",\n", "            \"Ku<PERSON><PERSON>\",\n", "            \"Lucknow\",\n", "            \"Mumbai\",\n", "            \"Mumbai M2\",\n", "            \"Mumbai M2 B\",\n", "            \"Mumbai M4\",\n", "            \"Mumbai M5 \",\n", "            \"Noida \",\n", "            \"Noida N2\",\n", "            \"Pune\",\n", "            \"B2B Bamnoli\",\n", "            \"Bulk PL\",\n", "            \"Bilaspur\",\n", "            \"Bulk Bamnoli\",\n", "            \"Feeder-Ahmedabad\",\n", "            \"Feeder-Hyderabad\",\n", "            \"Feeder-Kolkata\",\n", "            \"Feeder<PERSON><PERSON><PERSON><PERSON>\",\n", "            \"Feeder-Lucknow\",\n", "            \"Feeder-Pune\",\n", "            \"<PERSON><PERSON>\",\n", "            \"Pan India\",\n", "        ],\n", "        \"facility_name\": [\n", "            \"Super Store Ahmedabad - Warehouse\",\n", "            \"Super Store Bangalore - Warehouse\",\n", "            \"Super Store Bangalore B2 - Warehouse\",\n", "            \"Super Store Chennai - Warehouse\",\n", "            \"Super Store Chennai C2 - Warehouse\",\n", "            \"Super Store Dasna - Warehoue\",\n", "            \"Super Store Delhi P1 - Warehouse\",\n", "            \"Super Store Delhi P3 - Warehouse\",\n", "            \"Super Store Faridabad - Warehouse\",\n", "            \"Super Store Gurgaon G1 - Warehouse\",\n", "            \"Super Store Gurgaon G2 - Warehouse\",\n", "            \"Super Store Gurgaon G3 - Warehouse\",\n", "            \"Super Store Gurgaon G4 - Warehouse\",\n", "            \"Super Store Gurgaon G4-B - Warehouse\",\n", "            \"Super Store Hogwarts Training - Warehouse\",\n", "            \"Super Store Hyderabad - Warehouse\",\n", "            \"Super Store Hyderabad H2 - Warehouse\",\n", "            \"Super Store Jaipur - Warehouse\",\n", "            \"Super Store Kapashera Warehouse\",\n", "            \"Super Store Kolkata - Warehouse\",\n", "            \"Super Store Kolkata K2 - Warehouse\",\n", "            \"Super Store Kundli Warehouse\",\n", "            \"Super Store Lucknow - Warehouse\",\n", "            \"Super Store Mumbai - Warehouse\",\n", "            \"Super Store Mumbai M2 - Warehouse\",\n", "            \"Super Store Mumbai M2-B - Warehouse\",\n", "            \"Super Store Mumbai M4 - Warehouse\",\n", "            \"Super Store Mumbai M5 - Warehouse\",\n", "            \"Super Store Noida - Warehouse\",\n", "            \"Super Store Noida N2 - Warehouse\",\n", "            \"Super Store Pune - Warehouse\",\n", "            \"B2B Super Store Delhi Bamnoli\",\n", "            \"Bulk NCR PL Warehouse\",\n", "            \"Bulk Warehouse Bilaspur\",\n", "            \"Delhi Warehouse Bulk Bamnoli\",\n", "            \"Feeder Warehouse - Ahmedabad\",\n", "            \"Feeder Warehouse - Hyderabad\",\n", "            \"Feeder Warehouse - Kolkata\",\n", "            \"Feeder Warehouse - Kundli\",\n", "            \"Feeder Warehouse - Lucknow\",\n", "            \"Feeder Warehouse - Pune\",\n", "            \"Super Store Dasna - Warehouse\",\n", "            \"Pan India\",\n", "        ],\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi = doi_facility.merge(facility_mapping, on=[\"facility_name\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi = doi[\n", "    [\n", "        \"facility_id\",\n", "        \"net_worth\",\n", "        \"net_worth_last_30days\",\n", "        \"last_30_sales\",\n", "        \"doi\",\n", "        \"Facility\",\n", "        \"facility_name\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi[\"Facility\"] = np.where(doi.facility_id == 56, \"Bulk Mumbai M2\", doi.Facility)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi[\"Facility\"] = np.where(doi.Facility.isna(), doi.facility_name, doi.Facility)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india = doi[\n", "    [\n", "        \"facility_id\",\n", "        \"net_worth\",\n", "        \"net_worth_last_30days\",\n", "        \"last_30_sales\",\n", "        \"doi\",\n", "        \"Facility\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india[\"Facility\"] = \"Pan India\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup = round(\n", "    pan_india.groupby([\"Facility\"]).agg(\n", "        {\"net_worth\": \"sum\", \"net_worth_last_30days\": \"sum\", \"last_30_sales\": \"sum\"}\n", "    ),\n", "    1,\n", ").reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup[\"doi\"] = pan_india_rollup.net_worth_last_30days / pan_india_rollup.last_30_sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi[\"net_worth\"] = round(doi.net_worth / 10000000, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi[\"net_worth_last_30days\"] = round(doi.net_worth_last_30days / 300000000, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi[\"last_30_sales\"] = round(doi.last_30_sales / 10000000, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi[\"doi\"] = round(doi.doi, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup[\"net_worth\"] = round(pan_india_rollup.net_worth / 10000000, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup[\"net_worth_last_30days\"] = round(\n", "    pan_india_rollup.net_worth_last_30days / 300000000, 1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup[\"last_30_sales\"] = round(pan_india_rollup.last_30_sales / 10000000, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup[\"doi\"] = round(pan_india_rollup.doi, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi = doi[[\"Facility\", \"net_worth\", \"net_worth_last_30days\", \"last_30_sales\", \"doi\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pan_india_rollup = pan_india_rollup[\n", "    [\"Facility\", \"net_worth\", \"net_worth_last_30days\", \"last_30_sales\", \"doi\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_data_set = doi.append(pan_india_rollup)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_data_set.last_30_sales = np.where(\n", "    doi_data_set.last_30_sales.isna(), \"- \", doi_data_set.last_30_sales\n", ")\n", "doi_data_set.doi = np.where(doi_data_set.doi.isna(), \"- \", doi_data_set.doi)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["po_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "po_date_30 = (datetime.now() - <PERSON><PERSON><PERSON>(days=30)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_period_considered = po_date_30 + \" to \" + po_date\n", "doi_data_set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_period_considered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["items = doi_data_set.to_dict(orient=\"rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from jinja2 import Template\n", "\n", "\n", "sender = \"<EMAIL>\"\n", "to = [\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "    \"<EMAIL>\",\n", "]\n", "\n", "subject = \"Facility wise DOI report between \" + po_date_30 + \" and \" + po_date\n", "\n", "with open(os.path.join(cwd, \"po_vms_doi_daily_report.html\"), \"r\") as f:\n", "    t = Template(f.read())\n", "\n", "\n", "rendered = t.render(\n", "    products=items,\n", "    #     pan_india_doi=pan_india_doi,\n", "    time_period_considered=time_period_considered,\n", "    po_date=po_date,\n", ")\n", "\n", "\n", "pb.send_email(\n", "    sender,\n", "    to,\n", "    subject,\n", "    html_content=rendered,\n", "    dryrun=False,\n", "    cc=None,\n", "    bcc=None,\n", "    mime_subtype=\"mixed\",\n", "    mime_charset=\"utf-8\",\n", ")\n", "print(\"mail sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 4}