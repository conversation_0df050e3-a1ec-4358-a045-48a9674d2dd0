<head>
<style type="text/css">
.tg {
    border-collapse: collapse;
    border-spacing: 0;
    border-color: #aaa;
    width: 100%;
    max-width: 690px;
}

.tg td {
    font-family: Roboto, sans-serif;
    font-size: 12px;
    padding: 5px 5px;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #333;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #fff;
}

.tg tr:last-child td{
    background-color: #aaa;
}
.tg th {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    padding: 5px 5px;
    border-style: solid;
    border-width: 6px;
    overflow: hidden;
    word-break: normal;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-color: #aaa;
    color: #fff;
    border-left-width:1px;
    border-right-width:1px;
    background-color: #f38630;
}

.tg .tg-0lax {
    text-align: left;
    vertical-align: top
}

.tg .tg-dg7a {
    background-color: #F0F0F0;
    text-align: left;
    vertical-align: top
}
</style>
</head>
<p>Hi All,</p>
<p>Please find below DOI values which is calculated as follows:<br/>
DOI=Sum(Net Inventory worth based on weighted landing price for 30 days) / Sum(Delivered Gross Merchandise Value for 30 days)<br/>
<br/>Time period considered :{{time_period_considered}}
<br/>Facility wise DOI :
</p>
<table class="tg" border="1">
    
    <tbody>
        <tr>
            <th class="tg-0lax"><span style="font-weight:bold">Facility Name</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">{{po_date}} EOD Net Worth(In Cr)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">Average Net Worth(In Cr)</span></th>
          <th class="tg-0lax"><span style="font-weight:bold">Last 30 days Sale(In Cr)</span></th>
            <th class="tg-0lax"><span style="font-weight:bold">DOI</span></th>
        </tr>
        {% for product in products %}
        <tr>
            
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.Facility}}</b>
            </td>
              <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.net_worth}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.net_worth_last_30days}}</b>
            </td>
           <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.last_30_sales}}</b>
            </td>
            <td class="{{ loop.cycle('tg-0lax', 'tg-dg7a') }}">
              <b>{{ product.doi}}</b>
            </td>
        </tr>
        {% endfor %}

    </tbody>
</table>

<p>Best Regards,<br />
Data Bangalore
</p>
<p></p>