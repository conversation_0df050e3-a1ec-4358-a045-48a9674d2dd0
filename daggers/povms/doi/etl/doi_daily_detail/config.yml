dag_name: doi_daily_detail
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: low
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U03RURMNJJH
path: povms/doi/etl/doi_daily_detail
paused: true
pool: povms_pool
project_name: doi
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 30 1 * * *
  start_date: '2021-07-13T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 12
