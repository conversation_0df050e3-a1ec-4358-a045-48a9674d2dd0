{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Importing libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas as pd\n", "from collections import defaultdict\n", "from datetime import datetime, date, time\n", "from datetime import timedelta\n", "import numpy as np\n", "import warnings\n", "import pencilbox as pb\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option(\"display.max_columns\", 50)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing connections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Date Input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATE = (datetime.today() - timedelta(days=0)).date()\n", "\n", "DATE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "start_time = time.time()\n", "start_time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Report at Facility level"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inventory worth"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"with\n", "\n", "zone_tbl_treatment as\n", "(\n", "    select\n", "    facility_id,\n", "    max(zone) as zone\n", "    from\n", "    metrics.outlet_zone_mapping\n", "    group by 1\n", "),\n", "\n", "item_level_info as\n", "(\n", "    select item_id, variant_id, variant_mrp, outlet_type, storage_type\n", "    from lake_rpc.product_product pp2 \n", "    where pp2.id in (SELECT max(id) as id FROM lake_rpc.product_product pp\n", "    WHERE pp.active=1 and pp.approved=1 \n", "    GROUP BY item_id)\n", "),\n", "\n", "lp_treatment_tbl as\n", "(\n", "    select \n", "    a.item_id,\n", "    outlet_id,\n", "    b.warehouse_id,\n", "    facility_id,\n", "    landing_price,\n", "    row_number() over (partition by facility_id, a.item_id order by dt_ist desc) as row_rank,\n", "    row_number() over (partition by a.item_id order by dt_ist desc) as row_rank1\n", "    from weighted_landing_price a\n", "    left join (select * from  lake_retail.warehouse_outlet_mapping  where cloud_store_id is not null) b on a.outlet_id = b.cloud_store_id\n", "    left join (select * from  lake_retail.view_console_outlet  where active = true) c on b.warehouse_id = c.id\n", "    where landing_price is not null\n", "),\n", "\n", "availability as\n", "(\n", "select distinct e.zone,\n", "    a.*,\n", "    d.outlet_type,\n", "    d.storage_type,\n", "    d.variant_mrp as mrp,\n", "    coalesce(coalesce(b.landing_price,c.landing_price),d.variant_mrp*0.8000) as average_landing_price\n", "from metrics.rpc_availability_snapshot a\n", "left join zone_tbl_treatment e on a.facility_id = e.facility_id\n", "left join (select * from lp_treatment_tbl where row_rank = 1) b on a.facility_id = b.facility_id  AND a.item_id = b.item_id\n", "left join (select * from lp_treatment_tbl where row_rank1 = 1) c on a.item_id = c.item_id\n", "left join item_level_info d on a.item_id = d.item_id\n", "where\n", "order_date = dateadd(day, -1, '{date}')\n", ")\n", "\n", "select *\n", "FROM availability\n", "\n", "\"\"\".format(\n", "    date=DATE\n", ")\n", "\n", "inventory_worth = pd.read_sql(query, redshift)\n", "\n", "inventory_worth.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Delivered sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH it_pd AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               multiplier,\n", "               updated_on,\n", "               max(updated_on) OVER (PARTITION BY product_id) AS last_updated\n", "FROM it_pd_log),\n", " item_level_info AS\n", "(SELECT item_id,\n", "      variant_mrp,\n", "      row_number() OVER (PARTITION BY item_id\n", "                         ORDER BY updated_at DESC) AS row_rank\n", "FROM lake_rpc.product_product),\n", " mapping AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               coalesce(multiplier,1) AS multiplier\n", "FROM it_pd\n", "WHERE last_updated = updated_on ),\n", " orders AS\n", "(SELECT DISTINCT DATE(TIMESTAMP 'epoch' + (((a.slot_end-a.slot_start)/2)+a.slot_start)*INTERVAL '1 Second' +INTERVAL '5.5 hour') AS delivery_date,\n", "               date(convert_timezone('Asia/Kolkata',a.install_ts)) AS checkout_date,\n", "               o.facility_id,\n", "               o.facility_name,\n", "               a.id AS ancestor,\n", "               a.current_status,\n", "               oi.product_id,\n", "               c.item_id,\n", "               oi.selling_price,\n", "               d.variant_mrp,\n", "               oi.quantity*multiplier AS quantity,\n", "               multiplier,\n", "               sum(d.variant_mrp) OVER (PARTITION BY a.id,\n", "                                                     oi.product_id) AS total_mrp\n", "FROM lake_oms_bifrost.oms_order a\n", "INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM lake_oms_bifrost.oms_order_item\n", "  WHERE freebie_id IS NULL\n", "    AND install_ts BETWEEN dateadd(day, -7, '{date}') AND dateadd(day, -1, '{date}')) oi ON a.id = oi.order_id\n", "LEFT JOIN mapping c ON oi.product_id = c.product_id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM item_level_info\n", "  WHERE row_rank = 1) d ON c.item_id = d.item_id\n", "INNER JOIN\n", " (SELECT DISTINCT ancestor,\n", "                  facility_id,\n", "                  f.name AS facility_name\n", "  FROM lake_ims.ims_order_details o\n", "  INNER JOIN lake_retail.console_outlet ro ON ro.id=o.outlet\n", "  INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "  WHERE o.created_at BETWEEN dateadd(day, -9, '{date}') AND '{date}') o ON o.ancestor=a.id\n", "WHERE a.install_ts BETWEEN dateadd(day, -7, '{date}') AND dateadd(day, -1, '{date}')\n", " AND a.direction ='FORWARD'\n", " AND b.city_name NOT IN ('Not in service area',\n", "                         'Hapur',\n", "                         'Test city')\n", " AND b.city_name NOT ILIKE '%%b2b%%'\n", " AND a.current_status ='DELIVERED'\n", " AND a.type IN ('RetailForwardOrder',\n", "                'InternalForwardOrder')\n", "GROUP BY 1,\n", "        2,\n", "        3,\n", "        4,\n", "        5,\n", "        6,\n", "        7,\n", "        8,\n", "        9,\n", "        10,\n", "        11,\n", "        12),\n", " merchant_and_store_join AS\n", "(SELECT a.*,\n", "      selling_price*variant_mrp*1.000/(total_mrp*multiplier) AS item_selling_price\n", "FROM orders a)\n", "\n", "SELECT item_id,\n", "    facility_id,\n", "    delivery_date,\n", "    count(distinct ancestor) as delivered_carts,\n", "    sum(quantity) as delivered_sales_quantity,\n", "    sum(item_selling_price*quantity) AS delivered_sales\n", "FROM merchant_and_store_join\n", "WHERE delivery_date = dateadd(day, -1, '{date}')\n", "GROUP BY 1,2,3\"\"\".format(\n", "    date=DATE\n", ")\n", "\n", "delivered_sales = pd.read_sql(query, redshift)\n", "delivered_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales = delivered_sales[~delivered_sales.item_id.isna()]\n", "\n", "delivered_sales.item_id = delivered_sales.item_id.astype(int)\n", "delivered_sales.facility_id = delivered_sales.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_worth_del = inventory_worth.merge(\n", "    delivered_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"delivery_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_worth_del.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Checkout sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH it_pd AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               multiplier,\n", "               updated_on,\n", "               max(updated_on) OVER (PARTITION BY product_id) AS last_updated\n", "FROM it_pd_log),\n", " item_level_info AS\n", "(SELECT item_id,\n", "      variant_mrp,\n", "      row_number() OVER (PARTITION BY item_id\n", "                         ORDER BY updated_at DESC) AS row_rank\n", "FROM lake_rpc.product_product),\n", " mapping AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               coalesce(multiplier,1) AS multiplier\n", "FROM it_pd\n", "WHERE last_updated = updated_on ),\n", " orders AS\n", "(SELECT DISTINCT date(convert_timezone('Asia/Kolkata',a.install_ts)) AS checkout_date,\n", "               o.facility_id,\n", "               o.facility_name,\n", "               a.id AS ancestor,\n", "               a.current_status,\n", "               oi.product_id,\n", "               c.item_id,\n", "               oi.selling_price,\n", "               d.variant_mrp,\n", "               oi.quantity*multiplier AS quantity,\n", "               multiplier,\n", "               sum(d.variant_mrp) OVER (PARTITION BY a.id,\n", "                                                     oi.product_id) AS total_mrp\n", "FROM lake_oms_bifrost.oms_order a\n", "INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM lake_oms_bifrost.oms_order_item\n", "  WHERE freebie_id IS NULL\n", "    AND install_ts >= dateadd(day, -1, '{date}')) oi ON a.id = oi.order_id\n", "LEFT JOIN mapping c ON oi.product_id = c.product_id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM item_level_info\n", "  WHERE row_rank = 1) d ON c.item_id = d.item_id\n", "INNER JOIN\n", " (SELECT DISTINCT ancestor,\n", "                  facility_id,\n", "                  f.name AS facility_name\n", "  FROM lake_ims.ims_order_details o\n", "  INNER JOIN lake_retail.console_outlet ro ON ro.id=o.outlet\n", "  INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "  WHERE o.created_at BETWEEN dateadd(day, -2, '{date}') AND '{date}') o ON o.ancestor=a.id\n", "WHERE a.install_ts >= dateadd(day, -1, '{date}')\n", " AND a.direction ='FORWARD'\n", " AND b.city_name NOT IN ('Not in service area',\n", "                         'Hapur',\n", "                         'Test city')\n", " AND b.city_name NOT ILIKE '%%b2b%%'\n", " AND a.current_status <> 'CANCELLED'\n", " AND a.type IN ('RetailForwardOrder',\n", "                'InternalForwardOrder')\n", "GROUP BY 1,\n", "        2,\n", "        3,\n", "        4,\n", "        5,\n", "        6,\n", "        7,\n", "        8,\n", "        9,\n", "        10,\n", "        11),\n", " merchant_and_store_join AS\n", "(SELECT a.*,\n", "      selling_price*variant_mrp*1.000/(total_mrp*multiplier) AS item_selling_price\n", "FROM orders a)\n", "\n", "SELECT a.*, b.fac_checkout_carts FROM(\n", "    (SELECT item_id,\n", "        facility_id,\n", "        checkout_date,\n", "        count(distinct ancestor) as checkout_carts,\n", "        sum(quantity) as checkout_sales_quantity,\n", "        sum(item_selling_price*quantity) AS checkout_sales\n", "    FROM merchant_and_store_join\n", "    WHERE checkout_date = dateadd(day, -1, '{date}')\n", "    GROUP BY 1,2,3) a\n", "    \n", "    LEFT JOIN\n", "    \n", "    (SELECT facility_id,\n", "        checkout_date,\n", "        count(distinct ancestor) as fac_checkout_carts\n", "    FROM merchant_and_store_join\n", "    WHERE checkout_date = dateadd(day, -1, '{date}')\n", "    GROUP BY 1,2) b\n", "\n", "ON a.facility_id = b.facility_id and a.checkout_date = b.checkout_date)\"\"\".format(\n", "    date=DATE\n", ")\n", "\n", "checkout_sales = pd.read_sql(query, redshift)\n", "checkout_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales = checkout_sales[~checkout_sales.item_id.isna()]\n", "\n", "checkout_sales.item_id = checkout_sales.item_id.astype(int)\n", "checkout_sales.facility_id = checkout_sales.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_del_checkout = inventory_worth_del.merge(\n", "    checkout_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"checkout_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_del_checkout.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Billed Sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH it_pd AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               multiplier,\n", "               updated_on,\n", "               max(updated_on) OVER (PARTITION BY product_id) AS last_updated\n", "FROM it_pd_log),\n", " item_level_info AS\n", "(SELECT item_id,\n", "      variant_mrp,\n", "      row_number() OVER (PARTITION BY item_id\n", "                         ORDER BY updated_at DESC) AS row_rank\n", "FROM lake_rpc.product_product),\n", " mapping AS\n", "(SELECT DISTINCT item_id,\n", "               product_id,\n", "               coalesce(multiplier,1) AS multiplier\n", "FROM it_pd\n", "WHERE last_updated = updated_on ),\n", " orders AS\n", "(SELECT DISTINCT date(convert_timezone('Asia/Kolkata',a.install_ts)) AS billed_date,\n", "               o.facility_id,\n", "               o.facility_name,\n", "               a.id AS ancestor,\n", "               a.current_status,\n", "               oi.product_id,\n", "               c.item_id,\n", "               oi.selling_price,\n", "               d.variant_mrp,\n", "               oi.quantity*multiplier AS quantity,\n", "               multiplier,\n", "               sum(d.variant_mrp) OVER (PARTITION BY a.id,\n", "                                                     oi.product_id) AS total_mrp\n", "FROM lake_oms_bifrost.oms_order a\n", "INNER JOIN lake_oms_bifrost.oms_merchant b ON a.merchant_id = b.id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM lake_oms_bifrost.oms_order_item\n", "  WHERE freebie_id IS NULL\n", "    AND install_ts >= dateadd(day, -1, '{date}')) oi ON a.id = oi.order_id\n", "LEFT JOIN mapping c ON oi.product_id = c.product_id\n", "LEFT JOIN\n", " (SELECT *\n", "  FROM item_level_info\n", "  WHERE row_rank = 1) d ON c.item_id = d.item_id\n", "INNER JOIN\n", " (SELECT DISTINCT ancestor,\n", "                  facility_id,\n", "                  f.name AS facility_name\n", "  FROM lake_ims.ims_order_details o\n", "  INNER JOIN lake_retail.console_outlet ro ON ro.id=o.outlet\n", "  INNER JOIN lake_crates.facility f ON f.id=ro.facility_id\n", "  WHERE o.created_at BETWEEN dateadd(day, -2, '{date}') AND '{date}') o ON o.ancestor=a.id\n", "INNER JOIN lake_oms_bifrost.oms_order_event ooe on ooe.order_id=a.id\n", "\n", "WHERE a.install_ts >= dateadd(day, -1, '{date}')\n", " AND a.direction ='FORWARD'\n", " AND b.city_name NOT IN ('Not in service area',\n", "                         'Hapur',\n", "                         'Test city')\n", " AND b.city_name NOT ILIKE '%%b2b%%'\n", " AND json_extract_path_text(ooe.extra,'to',true)='BILLED'\n", " AND a.type IN ('RetailForwardOrder',\n", "                'InternalForwardOrder')\n", "GROUP BY 1,\n", "        2,\n", "        3,\n", "        4,\n", "        5,\n", "        6,\n", "        7,\n", "        8,\n", "        9,\n", "        10,\n", "        11),\n", " merchant_and_store_join AS\n", "(SELECT a.*,\n", "      selling_price*variant_mrp*1.000/(total_mrp*multiplier) AS item_selling_price\n", "FROM orders a)\n", "\n", "SELECT item_id,\n", "    facility_id,\n", "    billed_date,\n", "    count(distinct ancestor) as billed_carts,\n", "    sum(quantity) as billed_sales_quantity,\n", "    sum(item_selling_price*quantity) AS billed_sales\n", "FROM merchant_and_store_join\n", "WHERE billed_date = dateadd(day, -1, '{date}')\n", "GROUP BY 1,2,3\"\"\".format(\n", "    date=DATE\n", ")\n", "\n", "billed_sales = pd.read_sql(query, redshift)\n", "billed_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales = billed_sales[~billed_sales.item_id.isna()]\n", "\n", "billed_sales.item_id = billed_sales.item_id.astype(int)\n", "billed_sales.facility_id = billed_sales.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_del_checkout_billed = inventory_del_checkout.merge(\n", "    billed_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"billed_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_del_checkout_billed.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Query took %s seconds to run\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_del_checkout_billed[\n", "    (inventory_del_checkout_billed.item_id == 10003912)\n", "    & (inventory_del_checkout_billed.facility_id == 26)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_del_checkout_billed[\"date_of_snapshot\"] = inventory_del_checkout_billed[\"order_date\"]\n", "inventory_del_checkout_billed.transfer_flag = np.where(\n", "    inventory_del_checkout_billed.transfer_flag == \"\",\n", "    \"no\",\n", "    inventory_del_checkout_billed.transfer_flag,\n", ")\n", "\n", "inventory_del_checkout_billed[\"is_gb\"] = np.where(\n", "    inventory_del_checkout_billed.is_gb == \"TRUE\", 1, 0\n", ")\n", "inventory_del_checkout_billed[\"bucket\"] = np.where(\n", "    inventory_del_checkout_billed.bucket_x == \"Y\",\n", "    \"X\",\n", "    np.where(\n", "        inventory_del_checkout_billed.buckets == \"A\",\n", "        \"A\",\n", "        np.where(inventory_del_checkout_billed.buckets == \"B\", \"B\", \"NA\"),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_data = inventory_del_checkout_billed[\n", "    (inventory_del_checkout_billed.checkout_sales_quantity != 0)\n", "    & (inventory_del_checkout_billed.delivered_sales_quantity != 0)\n", "    & (inventory_del_checkout_billed.billed_sales_quantity != 0)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_data[\"checkout_selling_price\"] = (\n", "    doi_data.checkout_sales / doi_data.checkout_sales_quantity\n", ").astype(float)\n", "\n", "doi_data[\"delivered_selling_price\"] = (\n", "    doi_data.delivered_sales / doi_data.delivered_sales_quantity\n", ").astype(float)\n", "\n", "doi_data[\"billed_selling_price\"] = (doi_data.billed_sales / doi_data.billed_sales_quantity).astype(\n", "    float\n", ")\n", "\n", "selling_price = doi_data.groupby([\"item_id\", \"facility_id\"]).agg(\n", "    {\n", "        \"checkout_selling_price\": \"mean\",\n", "        \"delivered_selling_price\": \"mean\",\n", "        \"billed_selling_price\": \"mean\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_sales_price = inventory_del_checkout_billed.merge(\n", "    selling_price, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_sales_price[\"final_selling_price\"] = np.where(\n", "    inventory_sales_price.delivered_selling_price.isna(),\n", "    np.where(\n", "        inventory_sales_price.billed_selling_price.isna(),\n", "        np.where(\n", "            inventory_sales_price.checkout_selling_price.isna(),\n", "            inventory_sales_price.mrp * 0.8000,\n", "            inventory_sales_price.checkout_selling_price,\n", "        ),\n", "        inventory_sales_price.billed_selling_price,\n", "    ),\n", "    inventory_sales_price.delivered_selling_price,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Forecast sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forecast_query = \"\"\"\n", "\n", "with forecast as (select item_id, facility_id, date, sum(consumption) as forecast_quantity from(\n", "    (select item_id, outlet_id, date, consumption, active, create_ts, update_ts from(\n", "        select item_id, outlet_id, date, consumption, active, created_at as create_ts, updated_at as update_ts,\n", "        rank() over (partition by item_id, outlet_id, date order by updated_at desc) as rnk\n", "        from lake_snorlax.date_wise_consumption\n", "        where date between dateadd(day, 1, '{date}') and dateadd(day, 7, '{date}'))\n", "    where rnk = 1) a\n", "    left join\n", "    (select id, facility_id from lake_retail.console_outlet) b\n", "    on a.outlet_id = b.id\n", ")\n", "group by 1,2,3)\n", "\n", "select item_id, facility_id, avg(forecast_quantity) as forecast_quantity\n", "from forecast\n", "group by 1,2  \n", "\"\"\".format(\n", "    date=DATE\n", ")\n", "\n", "forecast_cpd = pd.read_sql(forecast_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forecast_cpd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_30_days_forecast_query = \"\"\"\n", "\n", "with forecast as (select item_id, facility_id, date, sum(consumption) as forecast_quantity from(\n", "    (select item_id, outlet_id, date, consumption, active, create_ts, update_ts from(\n", "        select item_id, outlet_id, date, consumption, active, created_at as create_ts, updated_at as update_ts,\n", "        rank() over (partition by item_id, outlet_id, date order by updated_at desc) as rnk\n", "        from lake_snorlax.date_wise_consumption\n", "        where date between dateadd(day, 1, '{date}') and dateadd(day, 30, '{date}'))\n", "    where rnk = 1) a\n", "    left join\n", "    (select id, facility_id from lake_retail.console_outlet) b\n", "    on a.outlet_id = b.id\n", ")\n", "group by 1,2,3)\n", "\n", "select item_id, facility_id, avg(forecast_quantity) as avg_30_days_forecast_qty\n", "from forecast\n", "group by 1,2\n", "\"\"\".format(\n", "    date=DATE\n", ")\n", "\n", "next_30_days_forecast_cpd = pd.read_sql(next_30_days_forecast_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_30_days_forecast_cpd.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_sales_price_forecast = inventory_sales_price.merge(\n", "    forecast_cpd, how=\"left\", on=[\"item_id\", \"facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_sales_price_forecast_30days = inventory_sales_price_forecast.merge(\n", "    next_30_days_forecast_cpd, how=\"left\", on=[\"item_id\", \"facility_id\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_sales_price_forecast_30days[\"forecast_sales\"] = (\n", "    inventory_sales_price_forecast_30days[\"forecast_quantity\"]\n", "    * inventory_sales_price_forecast_30days[\"final_selling_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_sales_price_forecast_30days[\"avg_30_days_forecast_sales\"] = (\n", "    inventory_sales_price_forecast_30days[\"avg_30_days_forecast_qty\"]\n", "    * inventory_sales_price_forecast_30days[\"final_selling_price\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_sales_price_forecast_30days[\n", "    \"forecast_qty\"\n", "] = inventory_sales_price_forecast_30days.forecast_quantity.fillna(0).astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_data = inventory_sales_price_forecast_30days[\n", "    [\n", "        \"item_id\",\n", "        \"item_name \",\n", "        \"date_of_snapshot\",\n", "        \"date_of_activation\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"new_substate\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"app_live\",\n", "        \"average_landing_price\",\n", "        \"outlet_type\",\n", "        \"storage_type\",\n", "        \"delivered_carts\",\n", "        \"delivered_sales_quantity\",\n", "        \"delivered_sales\",\n", "        \"billed_carts\",\n", "        \"billed_sales_quantity\",\n", "        \"billed_sales\",\n", "        \"checkout_carts\",\n", "        \"checkout_sales_quantity\",\n", "        \"checkout_sales\",\n", "        \"forecast_quantity\",\n", "        \"forecast_sales\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"bucket\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacture_id\",\n", "        \"transfer_flag\",\n", "        \"fac_checkout_carts\",\n", "        \"final_selling_price\",\n", "        \"forecast_qty\",\n", "        \"avg_30_days_forecast_qty\",\n", "        \"avg_30_days_forecast_sales\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fin_data.rename(columns={\"item_name \": \"item_name\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table = fin_data.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table[\"date_of_activation\"] = pd.to_datetime(doi_final_table.date_of_activation)\n", "\n", "doi_final_table[\"delivered_carts\"] = doi_final_table.delivered_carts.fillna(0).astype(int)\n", "doi_final_table[\"delivered_sales_quantity\"] = doi_final_table.delivered_sales_quantity.fillna(\n", "    0\n", ").astype(int)\n", "doi_final_table[\"delivered_sales\"] = doi_final_table.delivered_sales.fillna(0).astype(float)\n", "\n", "doi_final_table[\"checkout_carts\"] = doi_final_table.checkout_carts.fillna(0).astype(int)\n", "doi_final_table[\"checkout_sales_quantity\"] = doi_final_table.checkout_sales_quantity.fillna(\n", "    0\n", ").astype(int)\n", "doi_final_table[\"checkout_sales\"] = doi_final_table.checkout_sales.fillna(0).astype(float)\n", "\n", "doi_final_table[\"billed_carts\"] = doi_final_table.billed_carts.fillna(0).astype(int)\n", "doi_final_table[\"billed_sales_quantity\"] = doi_final_table.billed_sales_quantity.fillna(0).astype(\n", "    int\n", ")\n", "doi_final_table[\"billed_sales\"] = doi_final_table.billed_sales.fillna(0).astype(float)\n", "\n", "# incorrect\n", "doi_final_table[\"forecast_quantity\"] = doi_final_table.forecast_quantity.fillna(0).astype(int)\n", "\n", "\n", "doi_final_table[\"forecast_sales\"] = doi_final_table.forecast_sales.fillna(0).astype(float)\n", "\n", "doi_final_table[\"actual_quantity\"] = doi_final_table.actual_quantity.fillna(0).astype(int)\n", "doi_final_table[\"blocked_quantity\"] = doi_final_table.blocked_quantity.fillna(0).astype(int)\n", "doi_final_table[\"outlet_type\"] = doi_final_table.outlet_type.fillna(0).astype(int)\n", "doi_final_table[\"facility_id\"] = doi_final_table.facility_id.astype(int)\n", "\n", "doi_final_table[\"po_cycle\"] = doi_final_table.po_cycle.fillna(0).astype(int)\n", "doi_final_table[\"vendor_id\"] = doi_final_table.vendor_id.fillna(0).astype(int)\n", "\n", "doi_final_table[\"fac_checkout_carts\"] = doi_final_table.fac_checkout_carts.fillna(0).astype(int)\n", "\n", "doi_final_table[\"avg_30_days_forecast_qty\"] = doi_final_table.avg_30_days_forecast_qty.fillna(\n", "    0\n", ").astype(float)\n", "\n", "doi_final_table[\"avg_30_days_forecast_sales\"] = doi_final_table.avg_30_days_forecast_sales.fillna(\n", "    0\n", ").astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"Item ID\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar(1000)\", \"description\": \"Item Name\"},\n", "    {\n", "        \"name\": \"date_of_snapshot\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"date_of_activation\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Activation date of Item\",\n", "    },\n", "    {\"name\": \"facility_id\", \"type\": \"int\", \"description\": \"Facility ID\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(1000)\", \"description\": \"Facility Name\"},\n", "    {\"name\": \"new_substate\", \"type\": \"int\", \"description\": \"Sub-state ID\"},\n", "    {\"name\": \"actual_quantity\", \"type\": \"int\", \"description\": \"Item's actual quantity\"},\n", "    {\n", "        \"name\": \"blocked_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"Item's blocked quantity\",\n", "    },\n", "    {\"name\": \"app_live\", \"type\": \"int\", \"description\": \"status on app\"},\n", "    {\n", "        \"name\": \"average_landing_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"weighted landing price\",\n", "    },\n", "    {\"name\": \"outlet_type\", \"type\": \"int\", \"description\": \"outlet type\"},\n", "    {\"name\": \"storage_type\", \"type\": \"int\", \"description\": \"storage type\"},\n", "    {\n", "        \"name\": \"delivered_carts\",\n", "        \"type\": \"int\",\n", "        \"description\": \"carts delivered on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"delivered_sales_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"quantity delivered on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"delivered_sales\",\n", "        \"type\": \"float\",\n", "        \"description\": \"delivered sale on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"billed_carts\",\n", "        \"type\": \"int\",\n", "        \"description\": \"carts billed on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"billed_sales_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"quantity billed on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"billed_sales\",\n", "        \"type\": \"float\",\n", "        \"description\": \"billed sale on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"checkout_carts\",\n", "        \"type\": \"int\",\n", "        \"description\": \"carts checked out on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"checkout_sales_quantity\",\n", "        \"type\": \"int\",\n", "        \"description\": \"quantity checked out on date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"checkout_sales\",\n", "        \"type\": \"float\",\n", "        \"description\": \"checked out sale on date of analysis\",\n", "    },\n", "    {\"name\": \"forecast_quantity\", \"type\": \"int\", \"description\": \"DO NOT USE\"},\n", "    {\n", "        \"name\": \"forecast_sales\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Next 7 days average forecast sale from date of analysis\",\n", "    },\n", "    {\"name\": \"l0\", \"type\": \"varchar(1000)\", \"description\": \"level 0 category\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(1000)\", \"description\": \"level 1 category\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(1000)\", \"description\": \"level 2 category\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar(1000)\", \"description\": \"product type\"},\n", "    {\"name\": \"vendor_id\", \"type\": \"int\", \"description\": \"vendor id\"},\n", "    {\"name\": \"po_cycle\", \"type\": \"int\", \"description\": \"po cycle\"},\n", "    {\"name\": \"bucket\", \"type\": \"varchar(10)\", \"description\": \"bucket X/A/B\"},\n", "    {\"name\": \"is_gb\", \"type\": \"int\", \"description\": \"is grofer's brand\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar(1000)\", \"description\": \"vendor name\"},\n", "    {\"name\": \"brand_id\", \"type\": \"int\", \"description\": \"brand id\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar(1000)\", \"description\": \"brand name\"},\n", "    {\n", "        \"name\": \"manufacture_name\",\n", "        \"type\": \"varchar(1000)\",\n", "        \"description\": \"manufacturer name\",\n", "    },\n", "    {\"name\": \"manufacture_id\", \"type\": \"int\", \"description\": \"manufacturer id\"},\n", "    {\"name\": \"transfer_flag\", \"type\": \"varchar\", \"description\": \"transfer flag\"},\n", "    {\n", "        \"name\": \"fac_checkout_carts\",\n", "        \"type\": \"int\",\n", "        \"description\": \"total checked out carts on facility\",\n", "    },\n", "    {\n", "        \"name\": \"final_selling_price\",\n", "        \"type\": \"float\",\n", "        \"description\": \"average selling price\",\n", "    },\n", "    {\n", "        \"name\": \"forecast_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Next 7 days average forecast quantity from date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"avg_30_days_forecast_qty\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Next 30 days average forecast quantity from date of analysis\",\n", "    },\n", "    {\n", "        \"name\": \"avg_30_days_forecast_sales\",\n", "        \"type\": \"float\",\n", "        \"description\": \"Next 30 days average forecast sale from date of analysis\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"doi_daily_detail\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"date_of_snapshot\", \"item_id\", \"facility_id\"],\n", "    \"sortkey\": [\"date_of_snapshot\"],\n", "    \"distkey\": \"item_id\",\n", "    \"incremental_key\": \"date_of_snapshot\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Stores inventory and sales values for DOI computation\",\n", "}\n", "\n", "pb.to_redshift(doi_final_table, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}