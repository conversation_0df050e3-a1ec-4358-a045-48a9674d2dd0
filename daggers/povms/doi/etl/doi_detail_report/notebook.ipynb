{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(format=\"%(asctime)s - %(message)s\")\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "import sys\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np\n", "import time\n", "import pencilbox as pb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Setting up connection to retail\")\n", "con1 = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["query = \"\"\"\n", "\n", "SELECT distinct ii.*,  \n", "                CASE \n", "                  WHEN actual_networth > 0 THEN \n", "                  actual_networth / actual_quantity \n", "                  WHEN blocked_networth > 0 THEN \n", "                  blocked_networth / blocked_quantity \n", "                  ELSE 0 \n", "                END     AS avergae_landing_price ,\n", "                outlet_type\n", "                from\n", "(\n", "select item_id,\n", "date_of_snapshot,\n", "facility_id,\n", "metering,\n", "Sum(actual_quantity)  AS actual_quantity, \n", "                Sum(blocked_quantity) AS blocked_quantity, \n", "                Sum(actual_networth)  AS actual_networth, \n", "                Sum(blocked_networth) AS blocked_networth \n", "                from\n", "      (SELECT item_id, \n", "                Date(snapshot_datetime) AS date_of_snapshot, \n", "                ris.outlet_id, \n", "                facility_id,\n", "                metering, \n", "                Sum(actual_quantity)    AS actual_quantity, \n", "                Sum(blocked_quantity)   AS blocked_quantity, \n", "                Sum(actual_networth)    AS actual_networth, \n", "                Sum(blocked_networth)   AS blocked_networth \n", "         FROM   reports.reports_item_inventory_snapshot ris \n", "         left join (select pco.id as outlet_id,facility_id\n", "                   from retail.console_outlet pco \n", "                INNER JOIN retail.console_company_type \n", "                           rcc \n", "                        ON pco.company_type_id = rcc.id \n", "                            WHERE  Upper(rcc.entity_type) LIKE \n", "                                   '%%B2B_INDEPENDENT%%'\n", "                                   group by 1,2)  as a\n", "                                   on ris.outlet_id=a.outlet_id\n", "         where  Date(snapshot_datetime) = current_date-1 \n", "                AND metering = false \n", "         GROUP  BY 1, \n", "                   2, \n", "                   3, \n", "                   4,\n", "                   5\n", "        having  Sum(actual_quantity) >0 ) as inn\n", "                 group by 1,2,3,4\n", "                 having sum(actual_quantity) > 0 \n", "                 ) as ii\n", "                 left join \n", "                 (\n", "                 select \n", "                 a.item_id,\n", "                 max(outlet_type) as outlet_type\n", "                 from rpc.product_product as a\n", "                 inner join (select item_id, max(updated_at)as max_updated \n", "                 from rpc.product_product group by 1 )as b\n", "                 on a.item_id=b.item_id\n", "                 and a.updated_at=b.max_updated\n", "                 group by 1\n", "                 )as tp\n", "                 on ii.item_id=tp.item_id\n", "                 where ii.item_id is not null\n", "                 and facility_id is not null\n", "                 \n", "    \n", "                  \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pandas.read_sql_query(sql=query, con=con1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"\n", "select item_id,\n", "facility_id,\n", "               vendor_id        AS vendor_id_iv, \n", "                item_name        AS item_name_iv, \n", "                brand_id         AS brand_id_iv, \n", "                brand_name       AS brand_name_iv, \n", "                manufacture_name AS manufacture_name_iv, \n", "                manufacturer_id  AS manufacturer_id_iv, \n", "                is_gb            AS is_gb_iv, \n", "                po_cycle         AS po_cycle_iv, \n", "                vendor_name      AS vendor_name_iv, \n", "                Max(updated_at) as max_updated_at , \n", "                Max(bucket)      AS buckets_iv, \n", "                Max(bucket_x)    AS bucket_x_iv, \n", "                Max(CASE \n", "                      WHEN transfer_article IS NOT NULL THEN 'yes' \n", "                      ELSE 'no' \n", "                    END)         AS transfer_flag_iv \n", "                    from \n", "\n", "(\n", "                    select vn.*,\n", "                    iot.updated_at, \n", "                                 CASE \n", "                                  WHEN iot.tag_type_id = 1 THEN tag_value \n", "                                 END AS bucket, \n", "                                 CASE \n", "                                  WHEN iot.tag_type_id = 6 THEN tag_value \n", "                                 END AS bucket_X, \n", "                                 CASE \n", "                                  WHEN iot.tag_type_id = 8 THEN tag_value \n", "                                 END AS transfer_article \n", "                                 from\n", "                    \n", "(\n", "\n", "select \n", "b.item_id,\n", "b.facility_id,\n", "b.vendor_id,\n", " item_name , \n", "                brand_id  , \n", "                brand_name     , \n", "                manufacture_name , \n", "                item_details.manufacturer_id  , \n", "                is_gb   , \n", "                po_cycle  , \n", "                vendor_name \n", "\n", "from (select item_id,facility_id,vendor_id from vms.vms_vendor_facility_alignment where active=1 group by 1,2,3) as b\n", "left join \n", "(SELECT DISTINCT rpp.item_id, \n", "                                \n", "    \n", "                                 is_pl                        AS is_gb \n", "                                 , \n", "                                 \n", "                                 rpb.NAME                     AS brand_name, \n", "                                 rpm.NAME                     AS \n", "                                 manufacture_name, \n", "                                 rpm.id                       AS \n", "                                 manufacturer_id ,\n", "                                 rpp.brand_id,\n", "                                  rpp.NAME                     AS item_name\n", "                 FROM (select item_id,is_pl,name,brand_id,variant_id from  rpc.product_product group by 1,2,3,4,5) AS rpp \n", "               inner join  (select item_id,max(variant_id)  as variant_id from rpc.product_product group by 1) as inn\n", "               on rpp.variant_id=inn.variant_id\n", "               and rpp.item_id=inn.item_id\n", "                    INNER JOIN (select id,name,manufacturer_id from rpc.product_brand group by 1,2,3) AS rpb \n", "                                ON rpp.brand_id = rpb.id \n", "                        INNER JOIN (select id,name from rpc.product_manufacturer group by 1,2) AS rpm \n", "                                ON rpm.id = rpb.manufacturer_id\n", "                                group by 1,2,3,4,5,6,7) as item_details\n", "                      ON b.item_id = item_details.item_id\n", "                      left join (select vendor_id,manufacturer_id,facility_id,po_cycle from vms.vendor_manufacturer_physical_facility_attributes group by 1,2,3,4) pfa\n", "                       ON b.facility_id = pfa.facility_id \n", "              AND item_details.manufacturer_id = pfa.manufacturer_id \n", "              AND b.vendor_id = pfa.vendor_id\n", "              left join ( select id,vendor_name from vms.vms_vendor vv where active = 1 group by 1,2) as vv\n", "              ON b.vendor_id = vv.id \n", "                          )vn\n", "                      LEFT JOIN (select id,facility_id from retail.console_outlet  where active = 1  group by 1,2) pco\n", "                              ON vn.facility_id = pco.facility_id \n", "                        LEFT JOIN (select item_id,outlet_id,tag_value,tag_type_id,updated_at from rpc.item_outlet_tag_mapping iot where  iot.tag_type_id IN ( 1, 6, 8 ) \n", "                        AND iot.active = 1 group by 1,2,3,4,5) iot\n", "                              ON iot.outlet_id = pco.id \n", "                                  AND vn.item_id = iot.item_id \n", "                        ) x\n", "                        where item_id is not null\n", "                        group by item_id, \n", "                  facility_id, \n", "                  vendor_id, \n", "                  item_name, \n", "                  brand_id, \n", "                  brand_name, \n", "                  manufacture_name, \n", "                  manufacturer_id, \n", "                  is_gb, \n", "                  po_cycle, \n", "                  vendor_name\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1 = pandas.read_sql_query(sql=query1, con=con1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[\"item_id\"] = data.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data1[\"item_id\"] = data1.item_id.astype(int)\n", "data[\"facility_id\"] = data.facility_id.astype(int)\n", "data1[\"facility_id\"] = data1.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values = data.merge(data1, on=[\"item_id\", \"facility_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values[\"date_of_snapshot\"] = pandas.to_datetime(inventory_values.date_of_snapshot)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Setting up connection with retail\")\n", "con1 = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["sql = \"\"\"\n", "\n", "\n", "with sto_detail_table as\n", "(\n", "select\n", "  a.outlet_id as outlet_id,\n", "  a.item_id,\n", "   date(a.updated_at) as updated_at,\n", "  a.blocked_quantity as sto_quantity\n", "  from lake_ims.ims_sto_blocked_item_log as a\n", "  inner join \n", "  (select outlet_id, item_id, date(max(updated_at)) as updated_at from lake_ims.ims_sto_blocked_item_log \n", "   where date(updated_at) =current_date-1\n", "  group by outlet_id,item_id\n", "  ) b\n", "  on a.outlet_id=b.outlet_id\n", "  and a.item_id=b.item_id\n", "  and a.updated_at=b.updated_at\n", "  where date(a.updated_at) =current_date-1\n", "  group by 1,2,3,4\n", "  )\n", "  \n", " \n", " \n", "  select b.facility_id,\n", "  item_id,\n", "  a.updated_at,\n", "  sum(sto_quantity) as sto_quantity\n", "  from sto_detail_table as a\n", "  left join lake_retail.console_outlet  as b on a.outlet_id=b.id\n", "  group by 1,2,3\n", "  \n", "  \n", " \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Running sto_data query\")\n", "start_time = time.time()\n", "sto_data = pandas.read_sql_query(sql=sql, con=con)\n", "print(\"Query took %s seconds to run\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility = sto_data.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility.item_id = sto_data_facility.item_id.astype(int)\n", "sto_data_facility.facility_id = sto_data_facility.facility_id.astype(int)\n", "inventory_values.item_id = inventory_values.item_id.astype(int)\n", "inventory_values.facility_id = inventory_values.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["delivered_sales_query = \"\"\"\n", "\n", "WITH delivered_orders AS \n", "( \n", "                SELECT DISTINCT os.*, \n", "                                facility_id, \n", "                                co.id AS outlet_id, \n", "                                cocs.active \n", "                FROM            ( \n", "                                           SELECT     os.id AS grofers_order_id, \n", "                                                      os.type, \n", "                                                      osi.product_id, \n", "                                                      osoi.procured_quantity, \n", "                                                      osoi.procurement_price, \n", "                                                      Date(ooe.install_ts) AS delivered_date, \n", "                                                      os.procurement_amount, \n", "                                                      backend_merchant_id \n", "                                           FROM       lake_oms_bifrost.oms_suborder os \n", "                                           INNER JOIN lake_oms_bifrost.oms_order oo \n", "                                           ON         os.order_id=oo.id \n", "                                           INNER JOIN lake_oms_bifrost.oms_order_event ooe \n", "                                           ON         ooe.order_id=oo.id \n", "                                           INNER JOIN lake_oms_bifrost.oms_order_item osi \n", "                                           ON         oo.id=osi.order_id \n", "                                           INNER JOIN lake_oms_bifrost.oms_suborder_item osoi \n", "                                           ON         osoi.order_item_id=osi.id \n", "                                           AND        osoi.suborder_id=os.id \n", "                                           WHERE      oo.current_status='DELIVERED' \n", "                                           AND        Json_extract_path_text(ooe.extra,'to')='DELIVERED'\n", "                                           AND        os.current_status='AT_DELIVERY_CENTER' \n", "                                           AND        Date(ooe.install_ts)=current_date-1\n", "                                           AND        os.type='RetailSuborder' \n", "                                           GROUP BY   os.id, \n", "                                                      os.type, \n", "                                                      ooe.install_ts, \n", "                                                      backend_merchant_id, \n", "                                                      os.procurement_amount, \n", "                                                      osoi.procured_quantity, \n", "                                                      osi.product_id, \n", "                                                      osoi.procurement_price )os \n", "                LEFT JOIN       lake_oms_bifrost.view_oms_merchant om\n", "                ON              os.backend_merchant_id=om.id \n", "                LEFT JOIN       lake_retail.console_outlet_cms_store cocs \n", "                ON              om.external_id=cocs.cms_store \n", "                LEFT JOIN       lake_retail.console_outlet co \n", "                ON              cocs.outlet_id = co.id \n", "                LEFT JOIN       lake_retail.console_location cl \n", "                ON              co.tax_location_id = cl.id \n", "                WHERE           cocs.active=true \n", "                AND             cl.NAME NOT LIKE '%%B2B%%' ) ,\n", "                order_details_procured AS \n", "(\n", "                SELECT DISTINCT a.order_id , \n", "                                c.grofers_order_id, \n", "                                delivered_date, \n", "                                Cast(outlet AS INT) AS outlet , \n", "                                item_id ::             int, \n", "                                c.product_id , \n", "                                procured_quantity AS quantity , \n", "                                offer_id , \n", "                                mrp, \n", "                                procurement_price          AS price \n", "                FROM            lake_ims.ims_order_details AS a \n", "                INNER JOIN      delivered_orders           AS c \n", "                ON              a.order_id=c.grofers_order_id \n", "                INNER JOIN      lake_ims.ims_order_actuals AS b \n", "                ON              a.id=b.order_details_id \n", "                AND             c.product_id=b.product_id ) , order_details_without_offer AS \n", "( \n", "       SELECT * \n", "       FROM   order_details_procured \n", "       WHERE  ( \n", "                     offer_id IS NULL \n", "              OR     offer_id='') \n", "       AND    item_id IS NOT NULL ) , order_details_without_offer_facility AS \n", "( \n", "           SELECT     delivered_date, \n", "                      item_id, \n", "                      facility_id, \n", "                      Sum(quantity) AS quantity, \n", "                      Avg(mrp)      AS mrp, \n", "                      Sum( \n", "                      CASE \n", "                                 WHEN price=1 THEN 0 \n", "                                 ELSE quantity*price \n", "                      END)                        AS sales \n", "           FROM       order_details_without_offer AS a \n", "           LEFT JOIN  lake_retail.console_outlet AS pco \n", "           ON         a.outlet=pco.id \n", "           INNER JOIN lake_retail.console_company_type rcc \n", "           ON         pco.company_type_id=rcc.id \n", "           WHERE      Upper(rcc.entity_type) LIKE '%%B2B_INDEPENDENT%%' \n", "           GROUP BY   1, \n", "                      2, \n", "                      3 ) , order_details_with_offer AS \n", "( \n", "       SELECT * \n", "       FROM   order_details_procured \n", "       WHERE  offer_id IS NOT NULL \n", "       AND    offer_id <> '' ) , order_details_with_offer_facility AS \n", "( \n", "           SELECT     a.*, \n", "                      facility_id \n", "           FROM       order_details_with_offer    AS a \n", "           LEFT JOIN  lake_retail.console_outlet AS pco \n", "           ON         a.outlet=pco.id \n", "           INNER JOIN lake_retail.console_company_type rcc \n", "           ON         pco.company_type_id=rcc.id \n", "           WHERE      Upper(rcc.entity_type) LIKE '%%B2B_INDEPENDENT%%' ) \n", "--   select * from order_details_with_offer_facility \n", ", \n", "\n", "\n", "\n", "\n", "offer_details AS \n", "( \n", "                SELECT DISTINCT offer_reference_id ::     varchar, \n", "                                cast(field_val AS int) as item_id \n", "                FROM            lake_offer_master.offer \n", "                LEFT JOIN       lake_offer_master.offer_field o_field \n", "                ON              o_field.offer_id=offer.id \n", "                LEFT JOIN       lake_offer_master.offer_type_field otf \n", "                ON              o_field.offer_type_field_id = otf.id \n", "                WHERE           otf.field_name LIKE '%%product%%') , \n", "                \n", "                \n", "                \n", "item_offer_details AS \n", "( \n", "                SELECT DISTINCT cast(a.order_id AS bigint) AS order_id , \n", "                                delivered_date, \n", "                                cast(a.outlet AS              int)     AS outlet , \n", "                                cast(facility_id as int) as facility_id,\n", "                                cast(COALESCE(b.item_id,0) AS int)     AS item_id, \n", "                                cast(a.product_id AS          int)     AS product_id , \n", "                                cast(a.quantity AS            int)     AS quantity , \n", "                                cast(a.offer_id AS            varchar) AS offer_id , \n", "                                cast(a.mrp AS float)                   AS mrp, \n", "                                cast(a.price AS float)                 AS price \n", "                FROM            order_details_with_offer_facility               AS a \n", "                LEFT JOIN       offer_details                          AS b \n", "                ON              a.offer_id=b.offer_reference_id ) ,\n", "\n", "\n", "\n", "item_mrp AS \n", "( \n", "                SELECT DISTINCT a.*, \n", "                                avg(variant_mrp) AS item_mrp \n", "                FROM            item_offer_details a \n", "                LEFT JOIN       lake_rpc.product_product b \n", "                ON              a.item_id=b.item_id \n", "                GROUP BY        1, \n", "                                2, \n", "                                3, \n", "                                4, \n", "                                5, \n", "                                6, \n", "                                7, \n", "                                8, \n", "                                9,10 ) , \n", "                                \n", "                                \n", "\n", "\n", "offer_detail AS \n", "( \n", "                SELECT DISTINCT offer_id, \n", "                                offer_type_id, \n", "                                quantity_field_val, \n", "                                offer_reference_id , \n", "                                offer_type_identifier, \n", "                                offer_name, \n", "                                description, \n", "                                store_id AS outlet, \n", "                                facility_id, \n", "                                percentage_discount, \n", "                                field_name, \n", "                                field_type \n", "                FROM            ( \n", "                                                SELECT DISTINCT offer.id AS offer_id, \n", "                                                                offer.offer_type_id, \n", "                                                                quantity_field_val, \n", "                                                                offer_reference_id , \n", "                                                                offer_type_identifier, \n", "                                                                offer_type.NAME AS offer_name, \n", "                                                                offer_type.description, \n", "                                                                store_offer.store_id, \n", "                                                                store_offer.facility_id, \n", "                                                                store_offer_field.field_val AS percentage_discount,\n", "                                                                offer_type_field.field_name, \n", "                                                                offer_type_field.field_type \n", "                                                FROM            ( \n", "                                                                                SELECT DISTINCT id,\n", "                                                                                                offer_type_id,\n", "                                                                                                offer_reference_id\n", "                                                                                FROM            lake_offer_master.offer\n", "                                                                                WHERE           offer.offer_reference_id IN\n", "                                                                                                                             (\n", "                                                                                                                             SELECT DISTINCT offer_id\n", "                                                                                                                             FROM            item_mrp)\n", "                                                                                AND             id IS NOT NULL) AS offer\n", "                                                LEFT JOIN \n", "                                                                ( \n", "                                                                                SELECT DISTINCT offer_id,\n", "                                                                                                field_val AS quantity_field_val,\n", "                                                                                                offer_type_field_id,\n", "                                                                                                id\n", "                                                                                FROM            lake_offer_master.offer_field\n", "                                                                                WHERE           offer_field.offer_type_field_id IN\n", "                                                                                                ( \n", "                                                                                                         SELECT   id\n", "                                                                                                         FROM     lake_offer_master.offer_type_field\n", "                                                                                                         WHERE    field_name LIKE '%%quantity%%'\n", "                                                                                                         GROUP BY 1) )AS offer_field\n", "                                                ON              offer.id=offer_field.offer_id \n", "                                                LEFT JOIN \n", "                                                                ( \n", "                                                                                SELECT DISTINCT id,\n", "                                                                                                offer_type_identifier,\n", "                                                                                                NAME,\n", "                                                                                                description\n", "                                                                                FROM            lake_offer_master.offer_type ) AS offer_type\n", "                                                ON              offer.offer_type_id=offer_type.id \n", "                                                LEFT JOIN \n", "                                                                ( \n", "                                                                                SELECT DISTINCT store_offer.id,\n", "                                                                                                store_id,\n", "                                                                                                offer_id ,\n", "                                                                                                facility_id\n", "                                                                                FROM            lake_offer_master.store_offer\n", "                                                                                LEFT JOIN       lake_retail.console_outlet pco\n", "                                                                                ON              pco.id=store_offer.store_id\n", "                                                                                AND             store_offer.id IS NOT NULL) AS store_offer\n", "                                                ON              offer.id=store_offer.offer_id \n", "                                                LEFT JOIN \n", "                                                                ( \n", "                                                                                SELECT DISTINCT field_val,\n", "                                                                                                store_offer_id,\n", "                                                                                                offer_type_field_id\n", "                                                                                FROM            lake_offer_master.store_offer_field) AS store_offer_field\n", "                                                ON              store_offer.id = store_offer_field.store_offer_id\n", "                                                LEFT JOIN \n", "                                                                ( \n", "                                                                                SELECT DISTINCT id,\n", "                                                                                                field_name,\n", "                                                                                                field_type\n", "                                                                                FROM            lake_offer_master.offer_type_field) AS offer_type_field\n", "                                                ON              store_offer_field.offer_type_field_id=offer_type_field.id\n", "                                                GROUP BY        1, \n", "                                                                2, \n", "                                                                3, \n", "                                                                4, \n", "                                                                5, \n", "                                                                6, \n", "                                                                7, \n", "                                                                8, \n", "                                                                9, \n", "                                                                10, \n", "                                                                11, \n", "                                                                12 ) AS a ) \n", "--  select * from offer_detail where offer_reference_id='G00115805' \n", "-- select outlet,offer_reference_id,quantity_field_val from offer_detail group by 1,2,3 having count(*)>1\n", ", \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "base_info AS \n", "( \n", "       SELECT order_id, \n", "              delivered_date, \n", "              outlet, \n", "              item_id, \n", "              fac as facility_id, \n", "              field_name, \n", "              product_id, \n", "              offer_id, \n", "              quantity, \n", "              mrp, \n", "              price, \n", "              item_mrp, \n", "              quantity_check, \n", "              sum(item_mrp)OVER(partition BY order_id,product_id,a.offer_id,quantity_check) AS total_item_mrp,\n", "              CASE \n", "                     WHEN total_item_mrp = 0 \n", "                     OR     total_item_mrp IS NULL THEN 0 \n", "                     ELSE cast(item_mrp AS decimal(18,2))/cast(total_item_mrp AS decimal(18,2)) \n", "              END AS item_part, \n", "              item_quantity, \n", "              CASE \n", "                     WHEN offer_type_id=1 THEN price \n", "                     WHEN offer_type_id=2 \n", "                     AND    quantity_check=1 THEN (item_part::float*price::float)/(quantity_check::int*2)\n", "                     WHEN offer_type_id=2 \n", "                     AND    quantity_check<>1 THEN (item_part::float*price::float)/(quantity_check)\n", "                     WHEN offer_type_id=3 THEN (mrp::float          -(mrp*10)::float/100)/quantity_check::int \n", "                     WHEN offer_type_id=4 THEN (item_part::float    *price::float)::float/quantity_check::int\n", "                     WHEN offer_type_id=5 THEN (price::float        /quantity_check::int)::float \n", "                     WHEN offer_type_id=6 THEN (item_part::float    *price::float)::float/quantity_check::int\n", "                     WHEN offer_type_id=7 THEN (item_part::float    *price::float)::float/quantity_check::int\n", "                     WHEN offer_type_id=8 THEN (item_part::float    *price::float)::float/quantity_check::int\n", "              END AS item_price \n", "       FROM   ( \n", "                              SELECT DISTINCT a.*, \n", "                                              sum(quantity_field_val) OVER(partition BY order_id,item_id,a.offer_id) AS quantity_check,\n", "                                              CASE \n", "                                                              WHEN offer_type_id=1 THEN quantity::int\n", "                                                              WHEN offer_type_id = 2 \n", "                                                              AND             quantity_check=1 THEN (quantity_check*2*quantity)\n", "                                                              WHEN offer_type_id = 2 \n", "                                                              AND             quantity_check<>1 THEN (quantity_check*quantity)\n", "                                                              WHEN offer_type_id IN (3,4,5,6,7,8) THEN (quantity_check::int*quantity::int)\n", "                                              END AS item_quantity, \n", "                                              b.offer_type_id, \n", "                                              offer_reference_id, \n", "                                              offer_name, \n", "                                              description, \n", "                                              percentage_discount, \n", "                                              a.facility_id as fac, \n", "                                              field_name, \n", "                                              field_type \n", "                              FROM            item_mrp     AS a \n", "                              LEFT JOIN       offer_detail AS b \n", "                              ON              a.offer_id=b.offer_reference_id \n", "                              AND             a.outlet=b.outlet ) AS a ) , final_info AS \n", "( \n", "                SELECT DISTINCT order_id, \n", "                                delivered_date, \n", "                                item_id, \n", "                                facility_id, \n", "                                sum(item_quantity) AS quantity, \n", "                                avg(item_mrp)      AS mrp, \n", "                                sum( \n", "                                CASE \n", "                                                WHEN price<=1 THEN 0 \n", "                                                ELSE item_quantity*item_price \n", "                                END) AS sales \n", "                FROM            base_info \n", "                GROUP BY        1, \n", "                                2, \n", "                                3, \n", "                                4 ) \n", "\n", ", \n", "\n", "\n", "\n", "\n", "order_date_level AS \n", "( \n", "                SELECT DISTINCT delivered_date, \n", "                                item_id, \n", "                                facility_id, \n", "                                sum(quantity) AS quantity, \n", "                                avg(mrp)      AS mrp, \n", "                                sum(sales)    AS sales \n", "                FROM            final_info \n", "                GROUP BY        1, \n", "                                2, \n", "                                3 ) \n", "--  select sum(sales) from order_date_level where facility_id in (3,32)---20,526,076.25 \n", ", \n", "\n", "\n", "\n", "final_item_details AS \n", "( \n", "       SELECT * \n", "       FROM   ( \n", "                     SELECT * \n", "                     FROM   order_details_without_offer_facility ) \n", "       UNION ALL \n", "                 ( \n", "                        SELECT * \n", "                        FROM   order_date_level) ) , \n", "-- -- -- -- -- -- select item_id,order_id from final_item_details group by 1,2 having count(*)>1 \n", "\n", "\n", "\n", "final_sales_table AS \n", "( \n", "                SELECT DISTINCT delivered_date AS date_of_snapshot, \n", "                                item_id, \n", "                                facility_id, \n", "                                sum(quantity) AS delivered_quantity, \n", "                                avg(mrp)      AS mrp, \n", "                                sum(sales)    AS delivered_sales \n", "                FROM            final_item_details \n", "                GROUP BY        1, \n", "                                2, \n", "                                3 ) \n", "                                , \n", "                                \n", "                                item_vendor_facility AS \n", "( \n", "select a.*,\n", "b.vendor_id\n", "from final_sales_table as a\n", "left join lake_vms.vms_vendor_facility_alignment as b\n", "on a.item_id=b.item_id\n", "and a.facility_id=b.facility_id\n", "and b.active=1\n", "),\n", "\n", "-- select count(distinct item_id||facility_id),count(*) from final_inventory_vendor --76,608 76,608 \n", "\n", "\n", "item_brand_details AS \n", "( \n", "          SELECT    ivl.*, \n", "                    item_name, \n", "                    brand_id, \n", "                    brand_name, \n", "                    manufacture_name, \n", "                    manufacturer_id, \n", "                    is_gb \n", "          FROM      item_vendor_facility ivl \n", "          LEFT JOIN \n", "                    ( \n", "                          SELECT * \n", "                          FROM   ( \n", "                                                  SELECT DISTINCT item_id, \n", "                                                                  rpp.NAME                                                        AS item_name,\n", "                                                                  row_number() OVER(partition BY item_id ORDER BY item_name DESC) AS rank_1,\n", "                                                                  is_pl                                                           AS is_gb,\n", "                                                                  brand_id, \n", "                                                                  rpb.NAME            AS brand_name,\n", "                                                                  rpm.NAME            AS manufacture_name,\n", "                                                                  rpm.id              AS manufacturer_id\n", "                                                  FROM            lake_rpc.product_product AS rpp \n", "                                                  INNER JOIN      lake_rpc.product_brand   AS rpb \n", "                                                  ON              rpp.brand_id=rpb.id \n", "                                                  INNER JOIN      lake_rpc.product_manufacturer AS rpm\n", "                                                  ON              rpm.id=rpb.manufacturer_id )item_details\n", "                          WHERE  rank_1=1 )item_details \n", "          ON        ivl.item_id=item_details.item_id ) , po_details AS \n", "( \n", "          SELECT    ibd.*, \n", "                    pfa.po_cycle \n", "          FROM      item_brand_details ibd \n", "          LEFT JOIN lake_vms.vendor_manufacturer_physical_facility_attributes pfa \n", "          ON        ibd.facility_id = pfa.facility_id \n", "          AND       ibd.manufacturer_id = pfa.manufacturer_id \n", "          AND       ibd.vendor_id = pfa.vendor_id) , vendor_name AS \n", "( \n", "          SELECT     po.*, \n", "                      vv.vendor_name \n", "          FROM       po_details po \n", "          LEFT JOIN lake_vms.vms_vendor vv \n", "          ON         po.vendor_id = vv.id \n", "          AND        vv.active = 1),\n", "-- select * from vendor_name; \n", "\n", "\n", "item_bucket_final AS \n", "( \n", "         SELECT   item_id, \n", "                  date(date_of_snapshot) as delivered_date, \n", "                  facility_id, \n", "                  delivered_quantity,\n", "                  delivered_sales as delivered_sales,\n", "                  vendor_id        AS vendor_id_de, \n", "                  item_name        AS item_name_de, \n", "                  brand_id         AS brand_id_de, \n", "                  brand_name       AS brand_name_de, \n", "                  manufacture_name AS manufacture_name_de, -- colnames to be changed \n", "                  manufacturer_id  AS manufacturer_id_de, \n", "                  is_gb            AS is_gb_de, \n", "                  po_cycle         AS po_cycle_de, \n", "                  vendor_name      AS vendor_name_de , \n", "                  max(bucket)      AS buckets_de, \n", "                  max(bucket_x)    AS bucket_x_de, \n", "                  max( \n", "                  CASE \n", "                          WHEN transfer_article IS NOT NULL THEN 'yes' \n", "                          ELSE 'no' \n", "                  END) AS transfer_flag_de \n", "         FROM     ( \n", "                                  SELECT DISTINCT vn.*, \n", "                                                  iot.updated_at, \n", "                                                  CASE \n", "                                                                  WHEN iot.tag_type_id = 1 THEN tag_value\n", "                                                  END AS bucket, \n", "                                                  CASE \n", "                                                                  WHEN iot.tag_type_id = 6 THEN tag_value\n", "                                                  END AS bucket_x, \n", "                                                  CASE \n", "                                                                  WHEN iot.tag_type_id = 8 THEN tag_value\n", "                                                  END AS transfer_article \n", "                                  FROM            vendor_name vn \n", "                                  LEFT JOIN       lake_retail.console_outlet pco \n", "                                  ON              vn.facility_id = pco.facility_id \n", "                                  LEFT JOIN       lake_rpc.item_outlet_tag_mapping iot \n", "                                  ON              iot.outlet_id = pco.id \n", "                                  AND             vn.item_id = iot.item_id \n", "                                  AND          iot.tag_type_id IN (1,6,8) \n", "                                  AND             pco.active = 1\n", "                                  AND             iot.active = 1) x \n", "         GROUP BY item_id, \n", "                  delivered_sales,\n", "                  delivered_quantity,\n", "                  date_of_snapshot, \n", "                  facility_id, \n", "                  vendor_id, \n", "                  item_name, \n", "                  brand_id, \n", "                  brand_name, \n", "                  manufacture_name, \n", "                  manufacturer_id, \n", "                  is_gb, \n", "                  po_cycle, \n", "                  vendor_name ) \n", "SELECT *\n", "FROM   item_bucket_final\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Running delivered_sales query\")\n", "start_time = time.time()\n", "delivered_sales = pandas.read_sql(sql=delivered_sales_query, con=con)\n", "print(\"Query took %s seconds to run\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales[\"delivered_date\"] = pandas.to_datetime(delivered_sales.delivered_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["checkout_sales_query = \"\"\"\n", "\n", "with checkout_details as\n", "(\n", "select \n", "oo.id as order_id,\n", "os.grofer_order_id\n", "from\n", "(\n", "select \n", "order_id,\n", "grofer_order_id\n", "from\n", "(\n", "select \n", "order_id,\n", "id as grofer_order_id,\n", "dense_rank() over(partition by a.order_id order by a.update_ts::timestamp desc) as rnk\n", "from\n", "  (\n", "  select distinct order_id,\n", "  id, \n", "  to_timestamp(os.update_ts, 'YYYY-MM-DD HH24:MI') as update_ts \n", "  from lake_oms_bifrost.oms_suborder as os\n", "  where os.type='RetailSuborder' \n", "  and os.current_status !='REPROCURED'\n", "  and \n", "  date(os.install_ts)= current_date-1\n", "     ) as a\n", "     )as b\n", "     where rnk=1\n", "     group by 1,2\n", "     )os\n", "     inner join lake_oms_bifrost.oms_order as oo\n", "     on os.order_id=oo.id\n", "     where\n", "      date(oo.install_ts) =current_date-1\n", "      and oo.type='RetailForwardOrder'\n", "      and oo.current_status !='REPROCURED'\n", "      )\n", "     ,\n", " order_details_without_offer AS\n", " (SELECT DISTINCT a.order_id ,\n", "                  date(a.created_at) AS order_date,\n", "                  cast(outlet AS int) AS outlet ,\n", "                  facility_id,\n", "                  item_id ::int,\n", "                  product_id ,\n", "                  quantity ,\n", "                  offer_id ,\n", "                  mrp,\n", "                  price\n", "  FROM lake_ims.ims_order_details AS a\n", "  inner join checkout_details as c on a.order_id=c.grofer_order_id\n", "  LEFT JOIN lake_ims.ims_order_actuals AS b ON a.id=b.order_details_id\n", " inner JOIN lake_retail.console_outlet AS pco ON a.outlet=pco.id\n", "inner join lake_retail.console_company_type rcc on pco.company_type_id=rcc.id\n", "where upper(rcc.entity_type) like '%%B2B_INDEPENDENT%%'\n", "and pco.active=1\n", "and pco.device_id!=47\n", "    and  (offer_id IS NULL\n", "          OR offer_id='') \n", "          and item_id is not null\n", "        ) \n", "      \n", "      \n", "    \n", ",\n", "     order_details_without_offer_facility AS\n", "  ( SELECT\n", "          order_date,\n", "          item_id,\n", "          facility_id,\n", "          sum(quantity) as quantity,\n", "          avg(mrp) as mrp,\n", "          sum(case when price=1 then 0 else quantity*price end) as sales\n", "  FROM order_details_without_offer AS a\n", "  group by 1,2,3\n", "  )\n", "  \n", "\n", "  ,\n", "   \n", "     order_details_with_offer AS\n", "  ( SELECT DISTINCT \n", "                  a.order_id ,\n", "                  date(a.created_at) AS order_date,\n", "                  cast(outlet AS int) AS outlet ,\n", "                  facility_id,\n", "                  item_id ::int,\n", "                  product_id ,\n", "                  quantity ,\n", "                  offer_id ,\n", "                  mrp,\n", "                  price\n", "  FROM lake_ims.ims_order_details AS a\n", "  inner join checkout_details as c on a.order_id=c.grofer_order_id\n", "  LEFT JOIN lake_ims.ims_order_actuals AS b ON a.id=b.order_details_id\n", "  inner JOIN lake_retail.console_outlet AS pco ON a.outlet=pco.id\n", "inner join lake_retail.console_company_type rcc on pco.company_type_id=rcc.id\n", "where upper(rcc.entity_type) like '%%B2B_INDEPENDENT%%'\n", "and pco.active=1\n", "and pco.device_id!=47\n", "     and b.offer_id IS NOT NULL\n", "     AND b.offer_id <> ''\n", " ) \n", "      \n", "  \n", "-- -- --   select * from order_details_with_offer_facility\n", ",\n", "\n", "                         \n", "                         offer_details AS\n", "  (SELECT DISTINCT offer_reference_id :: varchar,\n", "                  cast(field_val AS int) AS item_id\n", "  FROM lake_offer_master.offer\n", "  LEFT JOIN lake_offer_master.offer_field o_field ON o_field.offer_id=offer.id\n", "  LEFT JOIN lake_offer_master.offer_type_field otf ON o_field.offer_type_field_id = otf.id \n", "  WHERE otf.field_name LIKE '%%product%%')\n", " \n", "  \n", "  ,\n", "                         \n", "item_offer_details AS\n", "  ( SELECT distinct\n", "    cast(a.order_id AS bigint) AS order_id ,\n", "          cast(a.order_date AS date) AS order_date,\n", "          cast(a.outlet AS int) AS outlet ,\n", "          cast(a.facility_id as int) as facility_id,\n", "          cast(coalesce(b.item_id,0) AS int) AS item_id,\n", "          cast(a.product_id AS int) AS product_id ,\n", "          cast(a.quantity AS int) AS quantity ,\n", "          cast(a.offer_id AS varchar) AS offer_id ,\n", "          cast(a.mrp AS float) AS mrp,\n", "          cast(a.price AS float) AS price\n", "  FROM order_details_with_offer AS a\n", "  LEFT JOIN offer_details AS b ON a.offer_id=b.offer_reference_id \n", "  ) \n", "  \n", "\n", "  \n", "  ,\n", "  \n", "\n", "  item_mrp as\n", "  (\n", "  select distinct\n", "  a.*,\n", "  avg(variant_mrp) as item_mrp\n", "  from item_offer_details a\n", "  left join lake_rpc.product_product b\n", "  on a.item_id=b.item_id \n", "  group by 1,2,3,4,5,6,7,8,9,10\n", "  )\n", "  ,\n", "  \n", "offer_detail as \n", "(\n", "select distinct\n", "offer_id,\n", "offer_type_id,\n", "quantity_field_val,\n", "offer_reference_id ,\n", "offer_type_identifier,\n", "offer_name,\n", "description,\n", "store_id as outlet,\n", "facility_id,\n", "percentage_discount,\n", "field_name,\n", "field_type\n", "from\n", "(\n", "select distinct\n", "offer.id as offer_id,\n", "offer.offer_type_id,\n", "quantity_field_val,\n", "offer_reference_id ,\n", "offer_type_identifier,\n", "offer_type.name as offer_name,\n", "offer_type.description,\n", "store_offer.store_id,\n", "store_offer.facility_id,\n", "store_offer_field.field_val as percentage_discount,\n", "offer_type_field.field_name,\n", "offer_type_field.field_type\n", "from \n", "(select distinct id,offer_type_id,offer_reference_id \n", "from lake_offer_master.offer \n", "where offer.offer_reference_id in (select distinct offer_id from item_mrp)\n", "and id is not null) as offer\n", "left join \n", "(select distinct \n", "offer_id,\n", "field_val as quantity_field_val,\n", "offer_type_field_id,\n", "id \n", "from lake_offer_master.offer_field\n", "where offer_field.offer_type_field_id IN\n", "    (\n", "    SELECT id\n", "     from lake_offer_master.offer_type_field\n", "     WHERE  field_name like '%%quantity%%' group by 1)\n", "     )as offer_field on offer.id=offer_field.offer_id\n", "left join \n", "(select distinct id,offer_type_identifier,name,description \n", "from lake_offer_master.offer_type ) as offer_type on offer.offer_type_id=offer_type.id\n", "left join (select distinct store_offer.id,store_id,offer_id ,facility_id\n", "from lake_offer_master.store_offer\n", "left join lake_retail.console_outlet pco\n", "on pco.id=store_offer.store_id \n", "and store_offer.id is not null) as store_offer on offer.id=store_offer.offer_id\n", "left join (select distinct field_val,\n", "store_offer_id,offer_type_field_id \n", "from lake_offer_master.store_offer_field) as store_offer_field \n", "on store_offer.id = store_offer_field.store_offer_id\n", "left join (select distinct id,field_name,field_type from lake_offer_master.offer_type_field) as offer_type_field \n", "on store_offer_field.offer_type_field_id=offer_type_field.id\n", " group by 1,2,3,4,5,6,7,8,9,10,11,12\n", " ) as a\n", ")\n", "--  select * from offer_detail where offer_reference_id='G00115805'\n", "  \n", "-- select outlet,offer_reference_id,quantity_field_val from offer_detail group by 1,2,3 having count(*)>1\n", "  \n", "  ,\n", "base_info as (\n", "select\n", "order_id,\n", "order_date,\n", "outlet,\n", "item_id,\n", "facility_id,\n", "field_name,\n", "product_id,\n", "offer_id,\n", "quantity,\n", "mrp,\n", "price,\n", "item_mrp,\n", "quantity_check,\n", "         sum(item_mrp)over(partition by order_id,product_id,a.offer_id,quantity_check) as total_item_mrp,\n", "        case when total_item_mrp = 0 \n", "                or total_item_mrp is null then 0 \n", "            else cast(item_mrp as decimal(18,2))/cast(total_item_mrp as decimal(18,2)) \n", "        end as item_part,\n", "         item_quantity,\n", "        case when offer_type_id=1 then price\n", "        when offer_type_id=2 and quantity_check=1 then (item_part::float*price::float)/(quantity_check::int*2)\n", "        when offer_type_id=2 and quantity_check<>1 then (item_part::float*price::float)/(quantity_check)\n", "        when offer_type_id=3 then (mrp::float-(mrp*10)::float/100)/quantity_check::int\n", "        when offer_type_id=4 then (item_part::float*price::float)::float/quantity_check::int\n", "        when offer_type_id=5 then (price::float/quantity_check::int)::float\n", "        when offer_type_id=6 then (item_part::float*price::float)::float/quantity_check::int\n", "        when offer_type_id=7 then (item_part::float*price::float)::float/quantity_check::int\n", "        when offer_type_id=8 then  (item_part::float*price::float)::float/quantity_check::int end as item_price\n", "from \n", "(\n", "\n", "        select distinct \n", "            a.*, \n", "            sum(quantity_field_val) over(partition by order_id,item_id,a.offer_id) as quantity_check,\n", "              case when offer_type_id=1 then quantity::int\n", "        when offer_type_id = 2 and quantity_check=1 then (quantity_check*2*quantity)\n", "        when offer_type_id = 2 and quantity_check<>1 then (quantity_check*quantity)\n", "        when offer_type_id in (3,4,5,6,7,8) then (quantity_check::int*quantity::int) end as item_quantity,\n", "            b.offer_type_id,\n", "            offer_reference_id,\n", "            offer_name,\n", "            description,\n", "            percentage_discount,\n", "            field_name,\n", "            field_type\n", "        from item_mrp as a\n", "        left join offer_detail as b\n", "        on a.offer_id=b.offer_reference_id\n", "        and a.outlet=b.outlet\n", "        ) as a\n", "        )\n", "        \n", "        ,\n", "final_info as\n", "(\n", "select distinct\n", "order_id,\n", "order_date,\n", "item_id,\n", "facility_id,\n", "sum(item_quantity) as quantity,\n", "avg(item_mrp) as mrp,\n", "sum(case when price<=1 then 0 else item_quantity*item_price end) as sales \n", "from base_info\n", "group by 1,2,3,4\n", ")\n", "\n", "-- -- -- select sum(sales) from final_info where facility_id in (3,32)--20,526,076.25\n", "\n", "  ,\n", "  \n", "  order_date_level as\n", "(\n", "select distinct\n", "order_date,\n", "item_id,\n", "facility_id,\n", "sum(quantity) as quantity,\n", "avg(mrp) as mrp,\n", "sum(sales) as sales\n", "from final_info\n", "group by 1,2,3\n", ")\n", "-- --  select sum(sales) from order_date_level where facility_id in (3,32)---20,526,076.25\n", "\n", ",\n", "\n", " final_item_details AS\n", "  (SELECT *\n", "  FROM\n", "     ( SELECT *\n", "      FROM order_details_without_offer_facility )\n", "  UNION all\n", "     ( SELECT  *\n", "      FROM order_date_level) ) \n", "      \n", "      \n", "      ,\n", "-- -- -- -- -- -- -- select item_id,order_id from final_item_details group by 1,2 having count(*)>1\n", "\n", "final_sales_table as\n", "(\n", "select distinct\n", "order_date as date_of_snapshot,\n", "item_id,\n", "facility_id,\n", "sum(quantity) as checkout_quantity,\n", "avg(mrp) as mrp,\n", "sum(sales) as checkout_sales\n", "from final_item_details\n", "where item_id is not null\n", "and item_id<>0\n", "group by 1,2,3\n", ")\n", "\n", "\n", ",\n", "\n", "-- Flag by <PERSON><PERSON><PERSON>\n", "item_vendor_facility as\n", " (\n", " select a.*,\n", " b.vendor_id\n", " from final_sales_table as a\n", " left join lake_vms.vms_vendor_facility_alignment as b\n", " on a.item_id=b.item_id\n", " and a.facility_id=b.facility_id\n", " and b.active=1\n", ")\n", ",\n", "\n", "-- select count(distinct item_id||facility_id),count(*) from final_inventory_vendor --76,608 76,608\n", "\n", "\n", "item_brand_details AS \n", "( \n", "          SELECT    ivl.*, \n", "                    item_name, \n", "                    brand_id, \n", "                    brand_name, \n", "                    manufacture_name, \n", "                    manufacturer_id, \n", "                    is_gb \n", "          FROM      item_vendor_facility ivl \n", "          LEFT JOIN \n", "                    ( \n", "                          SELECT * \n", "                          FROM   ( \n", "                                                  SELECT DISTINCT item_id, \n", "                                                                  rpp.NAME                                                        AS item_name,\n", "                                                                  row_number() OVER(partition BY item_id ORDER BY item_name DESC) AS rank_1,\n", "                                                                  is_pl                                                           AS is_gb,\n", "                                                                  brand_id, \n", "                                                                  rpb.NAME            AS brand_name,\n", "                                                                  rpm.NAME            AS manufacture_name,\n", "                                                                  rpm.id              AS manufacturer_id\n", "                                                  FROM            lake_rpc.product_product AS rpp \n", "                                                  INNER JOIN      lake_rpc.product_brand   AS rpb \n", "                                                  ON              rpp.brand_id=rpb.id \n", "                                                  INNER JOIN      lake_rpc.product_manufacturer AS rpm\n", "                                                  ON              rpm.id=rpb.manufacturer_id )item_details\n", "                          WHERE  rank_1=1 )item_details \n", "          ON        ivl.item_id=item_details.item_id )\n", "          ,\n", "        \n", "\n", "\n", "po_details AS \n", "(select \n", "    ibd.*,\n", "    pfa.po_cycle\n", "from \n", "    item_brand_details ibd\n", "left join\n", "    lake_vms.vendor_manufacturer_physical_facility_attributes pfa\n", "on \n", "    ibd.facility_id = pfa.facility_id\n", "and \n", "    ibd.manufacturer_id = pfa.manufacturer_id\n", "and \n", "    ibd.vendor_id = pfa.vendor_id)\n", "    \n", ",\n", "\n", "vendor_name AS\n", " (select \n", "    po.*,\n", "    vv.vendor_name\n", " from\n", "    po_details po \n", "LEFT join\n", "     lake_vms.vms_vendor vv\n", "on po.vendor_id = vv.id\n", "and vv.active = 1),\n", "    \n", "-- select * from vendor_name;\n", "\n", "\n", "\n", "item_bucket_final AS\n", "( select \n", "item_id, \n", "date_of_snapshot as order_date, \n", "facility_id,\n", "checkout_quantity,\n", "checkout_sales,\n", "vendor_id as vendor_id_ch,  \n", "item_name as item_name_ch,  \n", "brand_id as brand_id_ch,  \n", "brand_name as brand_name_ch,  \n", "manufacture_name as manufacture_name_ch,  -- colnames to be changed\n", "manufacturer_id as manufacturer_id_ch,\n", "is_gb as is_gb_ch,  \n", "po_cycle as po_cycle_ch,  \n", "vendor_name as vendor_name_ch ,\n", "max(bucket) as buckets_ch,\n", "max(bucket_X) as bucket_x_ch,\n", "max(case when transfer_article is not null then 'yes' else 'no' end) as transfer_flag_ch\n", "from\n", "(select distinct\n", "vn.*,\n", "iot.updated_at,\n", "CASE WHEN iot.tag_type_id = 1 THEN tag_value end as bucket,\n", "CASE WHEN iot.tag_type_id = 6 THEN tag_value end as bucket_X,\n", "CASE WHEN iot.tag_type_id = 8 THEN tag_value end as transfer_article\n", "from \n", "vendor_name vn\n", "LEFT JOIN\n", "lake_retail.console_outlet pco\n", "on \n", "vn.facility_id = pco.facility_id\n", "LEFT JOIN\n", " lake_rpc.item_outlet_tag_mapping iot\n", "on\n", "iot.outlet_id = pco.id \n", "AND vn.item_id = iot.item_id \n", "AND iot.tag_type_id IN (1,6,8)\n", "and pco.active = 1\n", "and iot.active = 1) x\n", "group by \n", "item_id, \n", "checkout_quantity,\n", "checkout_sales,\n", "date_of_snapshot, \n", "facility_id,\n", "vendor_id,  \n", "item_name,  \n", "brand_id, \n", "brand_name, \n", "manufacture_name, \n", "manufacturer_id,\n", "is_gb,  \n", "po_cycle, \n", "vendor_name\n", ")\n", "\n", "select * from item_bucket_final\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Running checkout_sales query\")\n", "start_time = time.time()\n", "checkout_sales = pandas.read_sql(sql=checkout_sales_query, con=con)\n", "print(\"Query took %s seconds to run\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales[\"order_date\"] = pandas.to_datetime(checkout_sales.order_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility = checkout_sales"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["billed_sales_query = \"\"\" \n", "WITH delivered_orders AS\n", "(select distinct os.*,facility_id,co.id as outlet_id,cocs.active from (SELECT os.id AS grofers_order_id,\n", "       os.T<PERSON>,\n", "       osi.product_id,\n", "       osoi.procured_quantity,\n", "    osoi.procurement_price,\n", "date(ooe.install_ts) as delivered_date,\n", "       os.procurement_amount,\n", "       backend_merchant_id\n", "FROM lake_oms_bifrost.oms_suborder os\n", "INNER JOIN lake_oms_bifrost.oms_order oo ON os.order_id=oo.id\n", "inner join lake_oms_bifrost.oms_order_event ooe\n", "   on ooe.order_id=oo.id\n", "   inner join lake_oms_bifrost.oms_order_item osi\n", "   on oo.id=osi.ordeR_id\n", "   INNER JOIN lake_oms_bifrost.oms_suborder_item osoi ON osoi.order_item_id=osi.id\n", "   AND osoi.suborder_id=os.id\n", "WHERE \n", "json_extract_path_text(ooe.extra,'to')='BILLED'\n", "     AND date(ooe.install_ts) = current_date-1\n", "  AND os.TYPE='RetailSuborder'\n", "GROUP BY os.id,\n", "         os.T<PERSON>,\n", "ooe.install_ts,\n", "         backend_merchant_id,os.procurement_amount,\n", "         osoi.procured_quantity,\n", "          osi.product_id,\n", "    osoi.procurement_price )os\n", "LEFT JOIN lake_oms_bifrost.oms_merchant om--\n", " ON os.backend_merchant_id=om.id\n", "LEFT JOIN lake_retail.console_outlet_cms_store cocs ON om.external_id=cocs.cms_store\n", "left JOIN lake_retail.console_outlet co ON cocs.outlet_id = co.id\n", "left JOIN lake_retail.console_location cl ON co.tax_location_id = cl.id\n", "where cocs.active=true\n", "and cl.name not like '%%B2B%%'\n", "        ),\n", "                     order_details_procured AS\n", "  (SELECT DISTINCT a.order_id ,\n", "                   c.grofers_order_id,\n", "                   delivered_date,\n", "                   cast(outlet AS int) AS outlet ,\n", "                   item_id ::int,\n", "                   c.product_id ,\n", "                   procured_quantity AS quantity ,\n", "                   offer_id ,\n", "                   mrp,\n", "                   procurement_price AS price\n", "   FROM lake_ims.ims_order_details AS a\n", "   INNER JOIN delivered_orders AS c ON a.order_id=c.grofers_order_id\n", "   INNER JOIN lake_ims.ims_order_actuals AS b ON a.id=b.order_details_id\n", "   AND c.product_id=b.product_id )\n", "     ,\n", " order_details_without_offer AS\n", " (SELECT * from order_details_procured\n", " where \n", "     (offer_id IS NULL\n", "          OR offer_id='') \n", "          and item_id is not null\n", "        ) ,\n", "     order_details_without_offer_facility AS\n", "  ( SELECT\n", "          delivered_date,\n", "          item_id,\n", "          facility_id,\n", "          sum(quantity) as quantity,\n", "          avg(mrp) as mrp,\n", "          sum(case when price=1 then 0 else quantity*price end) as sales\n", "  FROM order_details_without_offer AS a\n", "  LEFT JOIN lake_retail.console_outlet AS pco ON a.outlet=pco.id\n", "  inner join lake_retail.console_company_type rcc on pco.company_type_id=rcc.id\n", "  where upper(rcc.entity_type) like '%%B2B_INDEPENDENT%%'\n", "  group by 1,2,3\n", "  )\n", "  ,\n", "     order_details_with_offer AS\n", "  ( SELECT * from order_details_procured\n", "WHERE \n", "      offer_id IS NOT NULL\n", "     AND offer_id <> ''\n", " ) \n", "            ,\n", "  order_details_with_offer_facility AS\n", "  ( SELECT a.*,\n", "  facility_id\n", "  FROM order_details_with_offer AS a\n", "  LEFT JOIN lake_retail.console_outlet AS pco ON a.outlet=pco.id\n", "  inner join lake_retail.console_company_type rcc on pco.company_type_id=rcc.id\n", "  where upper(rcc.entity_type) like '%%B2B_INDEPENDENT%%'\n", "  )\n", "--   select * from order_details_with_offer_facility\n", ",\n", "                         offer_details AS\n", "  (SELECT DISTINCT offer_reference_id :: varchar,\n", "                  cast(field_val AS int) AS item_id\n", "  FROM lake_offer_master.offer\n", "  LEFT JOIN lake_offer_master.offer_field o_field ON o_field.offer_id=offer.id\n", "  LEFT JOIN lake_offer_master.offer_type_field otf ON o_field.offer_type_field_id = otf.id \n", "  WHERE otf.field_name LIKE '%%product%%')\n", "  ,\n", "item_offer_details AS\n", "  ( SELECT distinct\n", "    cast(a.order_id AS bigint) AS order_id ,\n", "         delivered_date,\n", "         cast(a.facility_id as int) as facility_id,\n", "          cast(a.outlet AS int) AS outlet ,\n", "          cast(coalesce(b.item_id,0) AS int) AS item_id,\n", "          cast(a.product_id AS int) AS product_id ,\n", "          cast(a.quantity AS int) AS quantity ,\n", "          cast(a.offer_id AS varchar) AS offer_id ,\n", "          cast(a.mrp AS float) AS mrp,\n", "          cast(a.price AS float) AS price\n", "  FROM order_details_with_offer_facility AS a\n", "  LEFT JOIN offer_details AS b ON a.offer_id=b.offer_reference_id \n", "  ) \n", "  ,\n", "  item_mrp as\n", "  (\n", "  select distinct\n", "  a.*,\n", "  avg(variant_mrp) as item_mrp\n", "  from item_offer_details a\n", "  left join lake_rpc.product_product b\n", "  on a.item_id=b.item_id \n", "  group by 1,2,3,4,5,6,7,8,9,10\n", "  )\n", "  ,\n", "offer_detail as \n", "(\n", "select distinct\n", "offer_id,\n", "offer_type_id,\n", "quantity_field_val,\n", "offer_reference_id ,\n", "offer_type_identifier,\n", "offer_name,\n", "description,\n", "store_id as outlet,\n", "facility_id,\n", "percentage_discount,\n", "field_name,\n", "field_type\n", "from\n", "(\n", "select distinct\n", "offer.id as offer_id,\n", "offer.offer_type_id,\n", "quantity_field_val,\n", "offer_reference_id ,\n", "offer_type_identifier,\n", "offer_type.name as offer_name,\n", "offer_type.description,\n", "store_offer.store_id,\n", "store_offer.facility_id,\n", "store_offer_field.field_val as percentage_discount,\n", "offer_type_field.field_name,\n", "offer_type_field.field_type\n", "from \n", "(select distinct id,offer_type_id,offer_reference_id \n", "from lake_offer_master.offer \n", "where offer.offer_reference_id in (select distinct offer_id from item_mrp)\n", "and id is not null) as offer\n", "left join \n", "(select distinct \n", "offer_id,\n", "field_val as quantity_field_val,\n", "offer_type_field_id,\n", "id \n", "from lake_offer_master.offer_field\n", "where offer_field.offer_type_field_id IN\n", "    (\n", "    SELECT id\n", "     from lake_offer_master.offer_type_field\n", "     WHERE  field_name like '%%quantity%%' group by 1)\n", "     )as offer_field on offer.id=offer_field.offer_id\n", "left join \n", "(select distinct id,offer_type_identifier,name,description \n", "from lake_offer_master.offer_type ) as offer_type on offer.offer_type_id=offer_type.id\n", "left join (select distinct store_offer.id,store_id,offer_id ,facility_id\n", "from lake_offer_master.store_offer\n", "left join lake_retail.console_outlet pco\n", "on pco.id=store_offer.store_id \n", "and store_offer.id is not null) as store_offer on offer.id=store_offer.offer_id\n", "left join (select distinct field_val,\n", "store_offer_id,offer_type_field_id \n", "from lake_offer_master.store_offer_field) as store_offer_field \n", "on store_offer.id = store_offer_field.store_offer_id\n", "left join (select distinct id,field_name,field_type from lake_offer_master.offer_type_field) as offer_type_field \n", "on store_offer_field.offer_type_field_id=offer_type_field.id\n", " group by 1,2,3,4,5,6,7,8,9,10,11,12\n", " ) as a\n", ")\n", "--  select * from lake_offer_master.offer_detail where offer_reference_id='G00115805'\n", "-- select outlet,offer_reference_id,quantity_field_val from lake_offer_master.offer_detail group by 1,2,3 having count(*)>1\n", "  ,\n", "base_info as (\n", "select\n", "order_id,\n", "delivered_date,\n", "outlet,\n", "item_id,\n", "fac as facility_id,\n", "field_name,\n", "product_id,\n", "offer_id,\n", "quantity,\n", "mrp,\n", "price,\n", "item_mrp,\n", "quantity_check,\n", "         sum(item_mrp)over(partition by order_id,product_id,a.offer_id,quantity_check) as total_item_mrp,\n", "        case when total_item_mrp = 0 \n", "                or total_item_mrp is null then 0 \n", "            else cast(item_mrp as decimal(18,2))/cast(total_item_mrp as decimal(18,2)) \n", "        end as item_part,\n", "         item_quantity,\n", "        case when offer_type_id=1 then price\n", "        when offer_type_id=2 and quantity_check=1 then (item_part::float*price::float)/(quantity_check::int*2)\n", "        when offer_type_id=2 and quantity_check<>1 then (item_part::float*price::float)/(quantity_check)\n", "        when offer_type_id=3 then (mrp::float-(mrp*10)::float/100)/quantity_check::int\n", "        when offer_type_id=4 then (item_part::float*price::float)::float/quantity_check::int\n", "        when offer_type_id=5 then (price::float/quantity_check::int)::float\n", "        when offer_type_id=6 then (item_part::float*price::float)::float/quantity_check::int\n", "        when offer_type_id=7 then (item_part::float*price::float)::float/quantity_check::int\n", "        when offer_type_id=8 then  (item_part::float*price::float)::float/quantity_check::int end as item_price\n", "from \n", "(\n", "        select distinct \n", "            a.*, \n", "            sum(quantity_field_val) over(partition by order_id,item_id,a.offer_id) as quantity_check,\n", "              case when offer_type_id=1 then quantity::int\n", "        when offer_type_id = 2 and quantity_check=1 then (quantity_check*2*quantity)\n", "        when offer_type_id = 2 and quantity_check<>1 then (quantity_check*quantity)\n", "        when offer_type_id in (3,4,5,6,7,8) then (quantity_check::int*quantity::int) end as item_quantity,\n", "            b.offer_type_id,\n", "            offer_reference_id,\n", "            offer_name,\n", "            description,\n", "            percentage_discount,\n", "            a.facility_id as fac,\n", "            field_name,\n", "            field_type\n", "        from item_mrp as a\n", "        left join offer_detail as b\n", "        on a.offer_id=b.offer_reference_id\n", "        and a.outlet=b.outlet\n", "        ) as a\n", "        )\n", "        ,\n", "final_info as\n", "(\n", "select distinct\n", "order_id,\n", "delivered_date,\n", "item_id,\n", "facility_id,\n", "sum(item_quantity) as quantity,\n", "avg(item_mrp) as mrp,\n", "sum(case when price<=1 then 0 else item_quantity*item_price end) as sales \n", "from base_info\n", "group by 1,2,3,4\n", ")\n", "-- select sum(sales) from final_info where facility_id in (3,32)--20,526,076.25\n", "  ,\n", "  order_date_level as\n", "(\n", "select distinct\n", "delivered_date,\n", "item_id,\n", "facility_id,\n", "sum(quantity) as quantity,\n", "avg(mrp) as mrp,\n", "sum(sales) as sales\n", "from final_info\n", "group by 1,2,3\n", ")\n", "--  select sum(sales) from order_date_level where facility_id in (3,32)---20,526,076.25\n", ",\n", " final_item_details AS\n", "  (SELECT *\n", "  FROM\n", "     ( SELECT *\n", "      FROM order_details_without_offer_facility )\n", "  UNION all\n", "     ( SELECT  *\n", "      FROM order_date_level) ) \n", "      ,\n", "-- -- -- -- -- -- select item_id,order_id from final_item_details group by 1,2 having count(*)>1\n", "final_sales_table as\n", "(\n", "select distinct\n", "delivered_date as date_of_snapshot,\n", "item_id,\n", "facility_id,\n", "sum(quantity) as billed_quantity,\n", "avg(mrp) as mrp,\n", "sum(sales) as billed_sales\n", "from final_item_details\n", "group by 1,2,3\n", ")\n", "-- -- -- -- -- -- select item_id,order_id from final_item_details group by 1,2 having count(*)>1\n", "\n", ",\n", "\n", "-- Flag by su<PERSON>t - billed\n", "\n", "\n", "item_vendor_facility as\n", "  (select\n", " a.*,\n", " vendor_id\n", " from final_sales_table as a\n", " left join lake_vms.vms_vendor_facility_alignment as b\n", " on a.item_id=b.item_id\n", " and a.facility_id=b.facility_id\n", " and active=1\n", ")\n", ",\n", "\n", "-- select count(distinct item_id||facility_id),count(*) from final_inventory_vendor --76,608 76,608\n", "\n", "\n", "item_brand_details AS \n", "( \n", "          SELECT    ivl.*, \n", "                    item_name, \n", "                    brand_id, \n", "                    brand_name, \n", "                    manufacture_name, \n", "                    manufacturer_id, \n", "                    is_gb \n", "          FROM      item_vendor_facility ivl \n", "          LEFT JOIN \n", "                    ( \n", "                          SELECT * \n", "                          FROM   ( \n", "                                                  SELECT DISTINCT item_id, \n", "                                                                  rpp.NAME                                                        AS item_name,\n", "                                                                  row_number() OVER(partition BY item_id ORDER BY item_name DESC) AS rank_1,\n", "                                                                  is_pl                                                           AS is_gb,\n", "                                                                  brand_id, \n", "                                                                  rpb.NAME            AS brand_name,\n", "                                                                  rpm.NAME            AS manufacture_name,\n", "                                                                  rpm.id              AS manufacturer_id\n", "                                                  FROM            lake_rpc.product_product AS rpp \n", "                                                  INNER JOIN      lake_rpc.product_brand   AS rpb \n", "                                                  ON              rpp.brand_id=rpb.id \n", "                                                  INNER JOIN      lake_rpc.product_manufacturer AS rpm\n", "                                                  ON              rpm.id=rpb.manufacturer_id )item_details\n", "                          WHERE  rank_1=1 )item_details \n", "          ON        ivl.item_id=item_details.item_id )\n", "          ,\n", "        \n", "\n", "\n", "po_details AS \n", "(select \n", "    ibd.*,\n", "    pfa.po_cycle\n", "from \n", "    item_brand_details ibd\n", "left join\n", "    lake_vms.vendor_manufacturer_physical_facility_attributes pfa\n", "on \n", "    ibd.facility_id = pfa.facility_id\n", "and \n", "    ibd.manufacturer_id = pfa.manufacturer_id\n", "and \n", "    ibd.vendor_id = pfa.vendor_id)\n", "    \n", ",\n", "\n", "vendor_name AS\n", " (select \n", "    po.*,\n", "    vv.vendor_name\n", " from\n", "    po_details po \n", "LEFT join\n", "     lake_vms.vms_vendor vv\n", "on po.vendor_id = vv.id\n", "and vv.active = 1),\n", "    \n", "-- select * from vendor_name;\n", "\n", "\n", "\n", "item_bucket_final AS\n", "( select \n", "item_id, \n", "date_of_snapshot as billed_date, \n", "facility_id,\n", "billed_quantity,\n", "billed_sales,\n", "vendor_id as vendor_id_bi,  \n", "item_name as item_name_bi,  \n", "brand_id as brand_id_bi,  \n", "brand_name as brand_name_bi,  \n", "manufacture_name as manufacture_name_bi,  -- colnames to be changed\n", "manufacturer_id as manufacturer_id_bi,\n", "is_gb as is_gb_bi,  \n", "po_cycle as po_cycle_bi,  \n", "vendor_name as vendor_name_bi ,\n", "max(bucket) as buckets_bi,\n", "max(bucket_X) as bucket_x_bi,\n", "max(case when transfer_article is not null then 'yes' else 'no' end) as transfer_flag_bi\n", "from\n", "(select distinct\n", "vn.*,\n", "iot.updated_at,\n", "CASE WHEN iot.tag_type_id = 1 THEN tag_value end as bucket,\n", "CASE WHEN iot.tag_type_id = 6 THEN tag_value end as bucket_X,\n", "CASE WHEN iot.tag_type_id = 8 THEN tag_value end as transfer_article\n", "from \n", "vendor_name vn\n", "LEFT JOIN\n", "lake_retail.console_outlet pco\n", "on \n", "vn.facility_id = pco.facility_id\n", "LEFT JOIN\n", " lake_rpc.item_outlet_tag_mapping iot\n", "on\n", "iot.outlet_id = pco.id \n", "AND vn.item_id = iot.item_id \n", "AND iot.tag_type_id IN (1,6,8)\n", "and pco.active = 1\n", "and iot.active = 1) x\n", "group by \n", "item_id, \n", "billed_quantity,\n", "billed_sales,\n", "date_of_snapshot, \n", "facility_id,\n", "vendor_id,  \n", "item_name,  \n", "brand_id, \n", "brand_name, \n", "manufacture_name, \n", "manufacturer_id,\n", "is_gb,  \n", "po_cycle, \n", "vendor_name\n", ")\n", "\n", "select * from item_bucket_final \n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Running delivered_sales query\")\n", "start_time = time.time()\n", "\n", "billed_sales = pandas.read_sql(sql=billed_sales_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"billed_sales shape\")\n", "print(billed_sales.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales[\"billed_date\"] = pandas.to_datetime(billed_sales.billed_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"delivered_sales dtypes\")\n", "print(delivered_sales.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales[\"item_id\"] = delivered_sales.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility.item_id = sto_data_facility.item_id.astype(int)\n", "sto_data_facility.facility_id = sto_data_facility.facility_id.astype(int)\n", "inventory_values.item_id = inventory_values.item_id.astype(int)\n", "inventory_values.facility_id = inventory_values.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values[\"date_of_snapshot\"] = pandas.to_datetime(inventory_values.date_of_snapshot)\n", "sto_data_facility[\"updated_at\"] = pandas.to_datetime(sto_data_facility.updated_at)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_data = inventory_values.merge(\n", "    sto_data_facility,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"updated_at\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"inventory_data shape\")\n", "print(inventory_data.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.item_id = delivered_sales.item_id.astype(int)\n", "delivered_sales.facility_id = delivered_sales.facility_id.astype(int)\n", "inventory_data.item_id = inventory_data.item_id.astype(int)\n", "inventory_data.facility_id = inventory_data.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_data[\"date_of_snapshot\"] = pandas.to_datetime(inventory_data.date_of_snapshot)\n", "delivered_sales[\"delivered_date\"] = pandas.to_datetime(delivered_sales.delivered_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered = inventory_data.merge(\n", "    delivered_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"delivered_date\"],\n", "    how=\"outer\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f = doi_delivered[doi_delivered.delivered_date.isna()]\n", "f.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"doi_delivered shape\")\n", "print(doi_delivered.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"doi_delivered dtypes\")\n", "print(doi_delivered.dtypes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_df = doi_delivered.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility[\"item_id\"] = checkout_sales_facility.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout = doi_delivered_df.merge(\n", "    checkout_sales_facility,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    how=\"outer\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"doi_delivered_Checkout shape\")\n", "print(doi_delivered_Checkout.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"doi_delivered_Checkout head\")\n", "doi_delivered_Checkout.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_df = doi_delivered_Checkout.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales[\"item_id\"] = billed_sales.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"billed_sales head\")\n", "billed_sales.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales[\"billed_date\"] = pandas.to_datetime(billed_sales.billed_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed = doi_delivered_Checkout_df.merge(\n", "    billed_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"billed_date\"],\n", "    how=\"outer\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pandas.set_option(\"max_columns\", 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed = doi_delivered_Checkout_billed.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"date_of_snap\"] = np.where(\n", "    doi_delivered_Checkout_billed.date_of_snapshot.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.delivered_date.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.order_date.isna(),\n", "            doi_delivered_Checkout_billed.billed_date,\n", "            doi_delivered_Checkout_billed.order_date,\n", "        ),\n", "        doi_delivered_Checkout_billed.delivered_date,\n", "    ),\n", "    doi_delivered_Checkout_billed.date_of_snapshot,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"item_name\"] = np.where(\n", "    doi_delivered_Checkout_billed.item_name_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.item_name_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.item_name_ch.isna(),\n", "            doi_delivered_Checkout_billed.item_name_bi,\n", "            doi_delivered_Checkout_billed.item_name_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.item_name_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.item_name_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"brand_id\"] = np.where(\n", "    doi_delivered_Checkout_billed.brand_id_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.brand_id_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.brand_id_ch.isna(),\n", "            doi_delivered_Checkout_billed.brand_id_bi,\n", "            doi_delivered_Checkout_billed.brand_id_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.brand_id_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.brand_id_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"vendor_id\"] = np.where(\n", "    doi_delivered_Checkout_billed.vendor_id_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.vendor_id_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.vendor_id_ch.isna(),\n", "            doi_delivered_Checkout_billed.vendor_id_bi,\n", "            doi_delivered_Checkout_billed.vendor_id_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.vendor_id_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.vendor_id_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"vendor_name\"] = np.where(\n", "    doi_delivered_Checkout_billed.vendor_name_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.vendor_name_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.vendor_name_ch.isna(),\n", "            doi_delivered_Checkout_billed.vendor_name_bi,\n", "            doi_delivered_Checkout_billed.vendor_name_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.vendor_name_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.vendor_name_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"brand_name\"] = np.where(\n", "    doi_delivered_Checkout_billed.brand_name_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.brand_name_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.brand_name_ch.isna(),\n", "            doi_delivered_Checkout_billed.brand_name_bi,\n", "            doi_delivered_Checkout_billed.brand_name_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.brand_name_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.brand_name_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"po_cycle\"] = np.where(\n", "    doi_delivered_Checkout_billed.po_cycle_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.po_cycle_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.po_cycle_ch.isna(),\n", "            doi_delivered_Checkout_billed.po_cycle_bi,\n", "            doi_delivered_Checkout_billed.po_cycle_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.po_cycle_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.po_cycle_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"manufacturer_id\"] = np.where(\n", "    doi_delivered_Checkout_billed.manufacturer_id_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.manufacturer_id_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.manufacturer_id_ch.isna(),\n", "            doi_delivered_Checkout_billed.manufacturer_id_bi,\n", "            doi_delivered_Checkout_billed.manufacturer_id_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.manufacturer_id_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.manufacturer_id_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"manufacture_name\"] = np.where(\n", "    doi_delivered_Checkout_billed.manufacture_name_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.manufacture_name_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.manufacture_name_ch.isna(),\n", "            doi_delivered_Checkout_billed.manufacture_name_bi,\n", "            doi_delivered_Checkout_billed.manufacture_name_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.manufacture_name_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.manufacture_name_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"is_gb\"] = np.where(\n", "    doi_delivered_Checkout_billed.is_gb_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.is_gb_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.is_gb_ch.isna(),\n", "            doi_delivered_Checkout_billed.is_gb_bi,\n", "            doi_delivered_Checkout_billed.is_gb_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.is_gb_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.is_gb_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"buckets\"] = np.where(\n", "    doi_delivered_Checkout_billed.buckets_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.buckets_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.buckets_ch.isna(),\n", "            doi_delivered_Checkout_billed.buckets_bi,\n", "            doi_delivered_Checkout_billed.buckets_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.buckets_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.buckets_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"bucket_x\"] = np.where(\n", "    doi_delivered_Checkout_billed.bucket_x_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.bucket_x_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.bucket_x_ch.isna(),\n", "            doi_delivered_Checkout_billed.bucket_x_bi,\n", "            doi_delivered_Checkout_billed.bucket_x_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.bucket_x_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.bucket_x_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"transfer_flag\"] = np.where(\n", "    doi_delivered_Checkout_billed.transfer_flag_iv.isna(),\n", "    np.where(\n", "        doi_delivered_Checkout_billed.transfer_flag_de.isna(),\n", "        np.where(\n", "            doi_delivered_Checkout_billed.transfer_flag_ch.isna(),\n", "            doi_delivered_Checkout_billed.transfer_flag_bi,\n", "            doi_delivered_Checkout_billed.transfer_flag_ch,\n", "        ),\n", "        doi_delivered_Checkout_billed.transfer_flag_de,\n", "    ),\n", "    doi_delivered_Checkout_billed.transfer_flag_iv,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_query = \"\"\"\n", "select id as facility_id,\n", "name as facility_name\n", "from lake_retail.warehouse_facility\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_name = pandas.read_sql_query(sql=facility_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_joines = doi_delivered_Checkout_billed.merge(facility_name, on=\"facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_joines = doi_joines[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snap\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"actual_networth\",\n", "        \"blocked_networth\",\n", "        \"avergae_landing_price\",\n", "        \"sto_quantity\",\n", "        \"outlet_type\",\n", "        \"delivered_date\",\n", "        \"delivered_quantity\",\n", "        \"delivered_sales\",\n", "        \"order_date\",\n", "        \"checkout_quantity\",\n", "        \"checkout_sales\",\n", "        \"billed_date\",\n", "        \"billed_quantity\",\n", "        \"billed_sales\",\n", "        \"vendor_id\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacturer_id\",\n", "        \"po_cycle\",\n", "        \"vendor_name\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"transfer_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_joines.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_query = \"\"\"\n", "WITH PRODUCT_Category AS\n", " (SELECT P.ID AS PID,\n", "         P.NAME AS PRODUCT,\n", "         P.UNIT AS Unit,\n", "         C2.NAME AS L2,\n", "         (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "         C.NAME AS L0,\n", "         P.BRAND AS brand,\n", "         P.MANUFACTURER AS manf,\n", "         pt.name as product_type\n", "  FROM lake_cms.GR_PRODUCT P\n", "  INNER JOIN lake_cms.GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "  INNER JOIN lake_cms.GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "  AND PCM.IS_PRIMARY=TRUE\n", "  INNER JOIN lake_cms.GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "  INNER JOIN lake_cms.GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "  inner join lake_cms.gr_product_type pt on p.type_id=pt.id\n", "\n", "  )\n", "\n", "SELECT item_id,\n", "      product_id,\n", "      (cat.product || ' ' || cat.unit) AS name,\n", "      cat.L0,\n", "      cat.l1,\n", "      cat.l2 ,\n", "      cat.brand,\n", "      cat.manf,\n", "      cat.product_type\n", "FROM lake_rpc.item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_mapping = pandas.read_sql(sql=catg_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_mapping.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg = doi_joines.merge(\n", "    catg_mapping[[\"item_id\", \"l0\", \"l1\", \"l2\", \"product_type\"]],\n", "    on=[\"item_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg.dtypes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["doi_df_final is the dataframe which is to be populated in the table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg = doi_df_final_catg[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snap\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"sto_quantity\",\n", "        \"avergae_landing_price\",\n", "        \"outlet_type\",\n", "        \"delivered_quantity\",\n", "        \"delivered_sales\",\n", "        \"billed_quantity\",\n", "        \"billed_sales\",\n", "        \"checkout_quantity\",\n", "        \"checkout_sales\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacturer_id\",\n", "        \"transfer_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final = doi_df_final_catg.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final[\"brand_id\"] = doi_df_final_catg_final.brand_id.fillna(0)\n", "doi_df_final_catg_final[\"manufacturer_id\"] = doi_df_final_catg_final.manufacturer_id.fillna(0)\n", "doi_df_final_catg_final[\"brand_id\"] = doi_df_final_catg_final.brand_id.astype(int)\n", "doi_df_final_catg_final[\"manufacturer_id\"] = doi_df_final_catg_final.manufacturer_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final[\"sto_quantity\"] = doi_df_final_catg_final.sto_quantity.fillna(0)\n", "doi_df_final_catg_final[\"sto_quantity\"] = doi_df_final_catg_final.sto_quantity.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final[\"brand_name\"] = doi_df_final_catg_final.brand_name.astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_list = doi_df_final_catg_final[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snap\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"sto_quantity\",\n", "        \"avergae_landing_price\",\n", "        \"outlet_type\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacturer_id\",\n", "        \"transfer_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final_group = (\n", "    doi_df_final_catg_final.groupby([\"item_id\", \"date_of_snap\", \"facility_id\"])[\n", "        [\n", "            \"delivered_quantity\",\n", "            \"delivered_sales\",\n", "            \"billed_quantity\",\n", "            \"billed_sales\",\n", "            \"checkout_quantity\",\n", "            \"checkout_sales\",\n", "        ]\n", "    ]\n", "    .max()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final_group.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_list = doi_final_list.drop_duplicates([\"item_id\", \"facility_id\", \"date_of_snap\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_list.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg_final_group.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table = doi_final_list.merge(\n", "    doi_df_final_catg_final_group,\n", "    on=[\"item_id\", \"facility_id\", \"date_of_snap\"],\n", "    how=\"inner\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table = doi_final_table.rename(\n", "    columns={\n", "        \"date_of_snap\": \"date_of_snapshot\",\n", "        \"avergae_landing_price\": \"average_landing_price\",\n", "        \"checkout_quantity\": \"checkout_sales_quantity\",\n", "        \"billed_quantity\": \"billed_sales_quantity\",\n", "        \"delivered_quantity\": \"delivered_sales_quantity\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table[\"po_cycle\"] = doi_final_table.po_cycle.fillna(0)\n", "doi_final_table[\"vendor_id\"] = doi_final_table.vendor_id.fillna(0)\n", "doi_final_table[\"delivered_sales_quantity\"] = doi_final_table.delivered_sales_quantity.fillna(0)\n", "doi_final_table[\"delivered_sales\"] = doi_final_table.delivered_sales.fillna(0)\n", "doi_final_table[\"checkout_sales_quantity\"] = doi_final_table.checkout_sales_quantity.fillna(0)\n", "doi_final_table[\"checkout_sales\"] = doi_final_table.checkout_sales.fillna(0)\n", "doi_final_table[\"billed_sales_quantity\"] = doi_final_table.billed_sales_quantity.fillna(0)\n", "doi_final_table[\"billed_sales\"] = doi_final_table.billed_sales.fillna(0)\n", "doi_final_table[\"actual_quantity\"] = doi_final_table.actual_quantity.fillna(0)\n", "doi_final_table[\"blocked_quantity\"] = doi_final_table.blocked_quantity.fillna(0)\n", "doi_final_table[\"outlet_type\"] = doi_final_table.outlet_type.fillna(0)\n", "doi_final_table[\"facility_id\"] = doi_final_table.facility_id.astype(int)\n", "doi_final_table[\"checkout_sales_quantity\"] = doi_final_table.checkout_sales_quantity.astype(int)\n", "doi_final_table[\"checkout_sales\"] = doi_final_table.checkout_sales.astype(float)\n", "doi_final_table[\"delivered_sales\"] = doi_final_table.delivered_sales.astype(float)\n", "doi_final_table[\"delivered_sales_quantity\"] = doi_final_table.delivered_sales_quantity.astype(int)\n", "doi_final_table[\"billed_sales\"] = doi_final_table.billed_sales.astype(float)\n", "doi_final_table[\"billed_sales_quantity\"] = doi_final_table.billed_sales_quantity.astype(int)\n", "doi_final_table[\"vendor_id\"] = doi_final_table.vendor_id.astype(int)\n", "doi_final_table[\"po_cycle\"] = doi_final_table.po_cycle.astype(int)\n", "doi_final_table[\"actual_quantity\"] = doi_final_table.actual_quantity.astype(int)\n", "doi_final_table[\"blocked_quantity\"] = doi_final_table.blocked_quantity.astype(int)\n", "doi_final_table[\"outlet_type\"] = doi_final_table.outlet_type.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table[\"metering\"] = doi_final_table.metering.astype(bool)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table[\"is_gb\"] = doi_final_table.is_gb.astype(bool)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table = doi_final_table[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"sto_quantity\",\n", "        \"average_landing_price\",\n", "        \"outlet_type\",\n", "        \"delivered_sales_quantity\",\n", "        \"delivered_sales\",\n", "        \"billed_sales_quantity\",\n", "        \"billed_sales\",\n", "        \"checkout_sales_quantity\",\n", "        \"checkout_sales\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"vendor_id\",\n", "        \"po_cycle\",\n", "        \"buckets\",\n", "        \"bucket_x\",\n", "        \"is_gb\",\n", "        \"vendor_name\",\n", "        \"item_name\",\n", "        \"brand_id\",\n", "        \"brand_name\",\n", "        \"manufacture_name\",\n", "        \"manufacturer_id\",\n", "        \"transfer_flag\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"date_of_snapshot\", \"type\": \"timestamp\"},\n", "    {\"name\": \"facility_id\", \"type\": \"int\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"metering\", \"type\": \"boolean\"},\n", "    {\"name\": \"actual_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"sto_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"average_landing_price\", \"type\": \"float\"},\n", "    {\"name\": \"outlet_type\", \"type\": \"int\"},\n", "    {\"name\": \"delivered_sales_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"delivered_sales\", \"type\": \"float\"},\n", "    {\"name\": \"billed_sales_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"billed_sales\", \"type\": \"float\"},\n", "    {\"name\": \"checkout_sales_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"checkout_sales\", \"type\": \"float\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"vendor_id\", \"type\": \"int\"},\n", "    {\"name\": \"po_cycle\", \"type\": \"int\"},\n", "    {\"name\": \"buckets\", \"type\": \"varchar(100)\"},\n", "    {\"name\": \"bucket_x\", \"type\": \"varchar(100)\"},\n", "    {\"name\": \"is_gb\", \"type\": \"boolean\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"brand_id\", \"type\": \"int\"},\n", "    {\"name\": \"brand_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"manufacture_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"manufacture_id\", \"type\": \"int\"},\n", "    {\"name\": \"transfer_flag\", \"type\": \"varchar\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table1 = doi_final_table[\n", "    [\n", "        \"item_id\",\n", "        \"facility_id\",\n", "        \"vendor_id\",\n", "        \"delivered_sales_quantity\",\n", "        \"delivered_sales\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.groupby([\"item_id\", \"facility_id\", \"vendor_id\", \"date_of_snapshot\"]).count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_final_table.groupby([\"item_id\", \"facility_id\", \"vendor_id\"])[\n", "    \"date_of_snapshot\"\n", "].max().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import pencilbox as pb\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"doi_detail_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"date_of_snapshot\", \"item_id\", \"facility_id\"],\n", "    \"sortkey\": [\"date_of_snapshot\"],\n", "    \"distkey\": \"item_id\",\n", "    \"incremental_key\": \"date_of_snapshot\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(doi_final_table, **kwargs)\n", "# # # # or\n", "# # # pb.to_redshift(local_filename, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# check_query = \"\"\"WITH item_inv_outlet AS\n", "#   (SELECT item_id,\n", "#           date(snapshot_datetime) AS date_of_snapshot,\n", "#           outlet_id,\n", "#           metering,\n", "#           sum(actual_quantity) AS actual_quantity,\n", "#           sum(blocked_quantity) AS blocked_quantity,\n", "#           sum(actual_networth) AS actual_networth,\n", "#           sum(blocked_networth) AS blocked_networth\n", "#    FROM reports_reports_item_inventory_snapshot ris\n", "#    WHERE date(snapshot_datetime) =current_date-1\n", "#    and metering=false\n", "#    GROUP BY 1,\n", "#             2,\n", "#             3,\n", "#             4),\n", "#       outlet_filtering AS\n", "#   (SELECT date_of_snapshot,\n", "#           outlet_id,\n", "#           facility_id\n", "#    FROM\n", "#      (SELECT date_of_snapshot,\n", "#              outlet_id,\n", "#              sum(actual_quantity) AS qty\n", "#       FROM item_inv_outlet\n", "#       GROUP BY 1,\n", "#                2\n", "#       HAVING sum(actual_quantity)>0)a\n", "#    INNER JOIN\n", "#      (SELECT pco.id,\n", "#              facility_id\n", "#       FROM lake_retail.console_outlet pco\n", "#       inner join lake_retail.console_company_type rcc on pco.company_type_id=rcc.id\n", "#       left join pos_console_location pcl\n", "#       on pco.tax_location_id=pcl.id\n", "#       WHERE upper(rcc.entity_type) like '%%B2B_INDEPENDENT%%'\n", "#       GROUP BY 1,\n", "#                2)pco ON a.outlet_id=pco.id) --   select outlet_id,date_of_snapshot from outlet_filtering group by 1,2 having count(*)>1;\n", "#  ,\n", "#       item_inv AS\n", "#   (SELECT item_id,\n", "#           ijo.date_of_snapshot,\n", "#           of.facility_id,\n", "#           metering,\n", "#           sum(actual_quantity) AS actual_quantity,\n", "#           sum(blocked_quantity) AS blocked_quantity,\n", "#           sum(actual_networth) AS actual_networth,\n", "#           sum(blocked_networth) AS blocked_networth\n", "#    FROM item_inv_outlet ijo\n", "#    INNER JOIN outlet_filtering OF ON ijo.outlet_id=OF.outlet_id\n", "#    AND ijo.date_of_snapshot=of.date_of_snapshot\n", "#    WHERE actual_quantity>0\n", "#    GROUP BY 1,\n", "#             2,\n", "#             3,\n", "#             4),\n", "#       base_detail AS\n", "#   (SELECT ii.*,\n", "#           cf.name AS facility_name,\n", "#           CASE\n", "#               WHEN actual_networth>0 THEN actual_networth/actual_quantity\n", "#               WHEN blocked_networth>0 THEN blocked_networth/blocked_quantity\n", "#               ELSE 0\n", "#           END AS avergae_landing_price\n", "#    FROM item_inv ii\n", "#    LEFT JOIN lake_po.physical_facility cf ON ii.facility_id=cf.facility_id),\n", "#    item_outlet_type as\n", "#    (select * from (select item_id,outlet_type,row_number() over (partition by item_id order by updated_at) as row_\n", "#    from pos_product_product\n", "#    )a\n", "#    where row_=1\n", "#    )\n", "#    ,\n", "#    final_inventory as\n", "#    (\n", "#    select  bd.*, outlet_type from base_detail bd\n", "#    left join item_outlet_type pp\n", "#    on bd.item_id=pp.item_id\n", "#    )\n", "\n", "#    select * from final_inventory\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inv_query = pandas.read_sql_query(sql=check_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inv_query.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inv_actual_quantity = inv_query.actual_quantity.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# table_query = \"\"\"\n", "# select sum(actual_quantity)as actual_quantity from metrics.doi_detail_report\n", "# where date(date_of_snapshot) = current_date-1\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# table_quantity = pandas.read_sql_query(sql=table_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# a = table_quantity.actual_quantity.sum() == inv_query.actual_quantity.sum()\n", "# a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import jinja2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if a == False:\n", "#     sender = \"<EMAIL>\"\n", "#     # to=[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"]\n", "#     to = [\n", "#         \"<EMAIL>\",\n", "#         \"<EMAIL>\",\n", "#         \"<EMAIL>\",\n", "#     ]\n", "#     subject = \"[DOI]Alert-difference in actual quantity in base table and doi table\"\n", "#     message_text = \"\"\"\n", "\n", "#     Hi,\n", "\n", "#     This is the alert related to DOI table actual quantity is differnt then the prod table.\n", "#     \"\"\"\n", "\n", "#     print(\"Sending message\")\n", "\n", "#     pb.send_email(\n", "#         sender,\n", "#         to,\n", "#         subject,\n", "#         message_text,\n", "#         #                files=['data/output/active_inactive_alert.csv'],\n", "#         dryrun=False,\n", "#         cc=None,\n", "#         mime_subtype=\"mixed\",\n", "#         mime_charset=\"utf-8\",\n", "#     )\n", "\n", "# else:\n", "\n", "#     print(\"no mailer sent\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}