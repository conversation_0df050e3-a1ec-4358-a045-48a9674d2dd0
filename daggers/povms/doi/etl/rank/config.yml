dag_name: rank
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
    - mountPath: /usr/local/airflow/tmp
      name: airflow-dags
      subPath: tmp
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
owner:
  name: sonal
  slack_id: UABB9AK9V
path: povms/doi/etl/rank
paused: true
pool: povms_pool
project_name: doi
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 0 4 * * *
  start_date: '2019-09-19T04:00:00'
schedule_type: fixed
sink:
  column_dtypes:
  - name: city
    type: varchar(30)
  - name: facility_id
    type: integer
  - name: facility_name
    type: varchar(256)
  - name: city_id
    type: integer
  - name: rank
    type: varchar(256)
  - name: date_of_snapshot
    type: date
  - name: inv_qty
    type: bigint
  - name: pd_qty
    type: bigint
  - name: doi_qty
    type: bigint
  - name: net_worth
    type: float8
  - name: sales_value
    type: float8
  - name: doi_revenue
    type: float8
  conn_id: redshift_consumer
  conn_type: postgres
  incremental_key: date_of_snapshot
  load_type: upsert
  primary_key:
  - date_of_snapshot
  - facility_id
  - rank
  schema: metrics
  sortkey:
  - date_of_snapshot
  - city
  - facility_name
  - rank
  table: doi_rank
sla: 120 minutes
source:
  bulk:
    parameters:
      where_condition: ' >= ''2019-05-15'' '
  conn_id: redshift_consumer
  conn_type: postgres
  incremental:
    parameters:
      where_condition: ' BETWEEN ''{{ macros.ds_add(ds, -1) }}'' AND ''{{ ds }}'' '
  infer:
    parameters:
      where_condition: ' >= ''2019-08-28'' '
support_files: []
tags: []
template_name: sql
version: 1
