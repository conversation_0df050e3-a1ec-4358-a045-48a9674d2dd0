WITH item_inv_outlet AS
  (SELECT item_id,
          date_of_snapshot,
          outlet_id,
          sum(quantity) AS quantity,
          max(threshold_quantity) AS threshold_quantity,
          (sum(quantity)-max(threshold_quantity))AS availabile_quantity,
          sum(net_worth) as net_worth
   FROM
     (SELECT variant_id,
             date(snapshot_datetime) AS date_of_snapshot ,
             outlet_id,
             net_worth,
             quantity,
             threshold_quantity
      FROM reports_inventory_snapshot
      WHERE date(snapshot_datetime) {{ where_condition }}
      GROUP BY 1,
               2,
               3,
               4,
               5,6)a
   INNER JOIN
     (SELECT variant_id,
             item_id
      FROM pos_product_product
      WHERE outlet_type!=1) ppp ON a.variant_id=ppp.variant_id
   GROUP BY 1,
            2,
            3),
     outlet_filtering AS
  (SELECT date_of_snapshot,
          outlet_id,
          tax_location_id,
          facility_id
   FROM
     (SELECT date(snapshot_datetime) AS date_of_snapshot ,
             outlet_id,
             sum(quantity) AS qty
      FROM reports_inventory_snapshot
      WHERE date(snapshot_datetime) {{ where_condition }}
      GROUP BY 1,
               2 HAVING sum(quantity)>0)a
   INNER JOIN
     (SELECT id,
             tax_location_id,
             facility_id
      FROM pos_console_outlet
      WHERE upper(name) LIKE '%%SUPER STORE%%(SSC)%%'   and
    upper(name) not like '%%AC%%' and upper(name) not like '%%HOT%%'
 )pco ON a.outlet_id=pco.id ),
     item_inv AS
  (SELECT item_id,
          ijo.date_of_snapshot,
          of.facility_id,
          pcl.name AS city,
          sum(availabile_quantity) AS availabile_quantity,
          sum(net_worth) as net_worth
   FROM item_inv_outlet ijo
   INNER JOIN outlet_filtering OF ON ijo.outlet_id=OF.outlet_id
   AND ijo.date_of_snapshot=of.date_of_snapshot
   LEFT JOIN pos_console_location pcl ON OF.tax_location_id=pcl.id
   WHERE availabile_quantity>0
   GROUP BY 1,
            2,
            3,
            4),
     pos_details AS
  (SELECT item_id,
          facility_id,
          pos_timestamp,
          pcl.name AS city,
          sum(sales) AS sales,
          sum(sales_qty) AS sales_qty
   FROM
     (SELECT item_id,
             pos_timestamp,
             outlet_id ,
             sum(sales_qty) AS sales_qty,
             sum(sales) AS sales
      FROM
        (SELECT variant_id,
                pos_timestamp,
                selling_price,
                outlet_id,
                sum(quantity) AS sales_qty,
                (sum(quantity)* selling_price) AS sales
         FROM
           (SELECT variant_id ,
                   quantity,
                   selling_price ,
                   invoice_id
            FROM pos_pos_invoice_product_details
            GROUP BY variant_id ,
                     quantity,
                     selling_price ,
                     invoice_id)a
         RIGHT JOIN
           (SELECT id ,
                   outlet_id,
                   date(created_at) AS pos_timestamp
            FROM pos_pos_invoice
            WHERE invoice_type_id=1
              AND date(created_at) {{ where_condition }}) b ON a.invoice_id=b.id
         GROUP BY 1,
                  2,
                  3,
                  4)a
      LEFT JOIN pos_product_product b ON a.variant_id=b.variant_id
      GROUP BY 1,
               2,
               3) a
   INNER JOIN pos_console_outlet pco ON a.outlet_id=pco.id
   LEFT JOIN pos_console_location pcl ON pco.tax_location_id=pcl.id
   GROUP BY 1,
            2,
            3,
            4 --  ,5
 )
SELECT inv.city,
       inv.facility_id,
      ofc.facility_name,
      ofc.city_id,
       inv.rank,
       inv.date_of_snapshot,
       inv_qty,
       pd_qty,
       inv_qty/pd_qty AS DOI_Qty,
       net_worth,
       sales_value,
       net_worth/sales_value as doi_revenue
FROM
  (SELECT city,
          rank,
          date_of_snapshot,
          facility_id,
          sum(availabile_quantity) AS inv_qty,
          sum(net_worth) as net_worth
   FROM
     (SELECT inv.*,
             rank
      FROM item_inv inv
      LEFT JOIN  (select item_id,rank,L0 from metrics.item_details group by 1,2,l0) cat ON inv.item_id=cat.item_id
      WHERE L0!='Fruits & Vegetables')a
   GROUP BY 1,
            2,
            3,
            4)inv
LEFT JOIN
  (SELECT city,
          rank,
          facility_id,
          pos_timestamp,
          sum(sales_qty) AS pd_qty,
          sum(Sales) as sales_value
   FROM
     (SELECT pd.*,
           rank 
      FROM pos_details pd
      LEFT JOIN 
      (select item_id,rank,L0 from metrics.item_details group by 1,2,l0) cat ON pd.item_id=cat.item_id
      WHERE L0!='Fruits & Vegetables')a
   GROUP BY 1,
            2,
            3,
            4)pd ON inv.city=pd.city
AND inv.facility_id=pd.facility_id
AND inv.rank=pd.rank
AND inv.date_of_snapshot=pd.pos_timestamp
left join 
(select facility_id,facility_name,city_id,city_name from metrics.outlet_facility_city_mapping group by 1,2,3,4) ofc
on inv.facility_id=ofc.facility_id 