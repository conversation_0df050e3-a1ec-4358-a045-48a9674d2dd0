{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy as sqla\n", "import pandas\n", "from collections import defaultdict\n", "from datetime import datetime, date\n", "from datetime import timedelta\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "con = pb.get_connection(\"redshift\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["end_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "start_date = (datetime.now() - timedelta(days=2)).strftime(\"%Y-%m-%d\")\n", "\n", "\n", "print(end_date, start_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_mapping = pandas.read_sql(\n", "    sql=\"\"\"select id as retail_outlet_id,facility_id from pos_console_outlet\"\"\", con=con\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "--This query calculates doi based on end of the day inv qty / delivered sales\n", " WITH item_inv_outlet AS\n", "  (SELECT item_id,\n", "          date(snapshot_datetime) AS date_of_snapshot,\n", "          outlet_id,\n", "          metering,\n", "          sum(actual_quantity) AS actual_quantity,\n", "          sum(blocked_quantity) AS blocked_quantity,\n", "          sum(actual_networth) AS actual_networth,\n", "          sum(blocked_networth) AS blocked_networth\n", "   FROM reports_reports_item_inventory_snapshot ris\n", "   WHERE date(snapshot_datetime) BETWEEN '{start_date}' AND '{end_date}'\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4) -- select item_id,outlet_id,date_of_snapshot from item_inv_outlet group by 1,2,3 having count(*)>1;\n", "\n", "              ,\n", "              outlet_filtering AS\n", "  (SELECT date_of_snapshot,\n", "          outlet_id,\n", "          tax_location_id,\n", "          facility_id\n", "   FROM\n", "     (SELECT date_of_snapshot ,\n", "             outlet_id,\n", "             sum(actual_quantity) AS qty\n", "      FROM item_inv_outlet\n", "      GROUP BY 1,\n", "               2 HAVING sum(actual_quantity)>0)a\n", "   INNER JOIN\n", "     (SELECT id,\n", "             tax_location_id,\n", "             facility_id\n", "      FROM pos_console_outlet\n", "      WHERE upper(name) LIKE '%%SSC%%'\n", "        AND upper(name) NOT LIKE '%%AC%%'\n", "        AND upper(name) NOT LIKE '%%HOT%%'\n", "      GROUP BY 1,\n", "               2,\n", "               3)pco ON a.outlet_id=pco.id)\n", "               --   select outlet_id,date_of_snapshot from outlet_filtering group by 1,2 having count(*)>1;\n", "\n", "                                           ,\n", "                                           item_inv AS\n", "  (SELECT item_id,\n", "          ijo.date_of_snapshot,\n", "          of.facility_id,\n", "          tax_location_id AS city_id,\n", "          pcl.name AS city,\n", "          metering,\n", "          sum(actual_quantity) AS actual_quantity,\n", "          sum(blocked_quantity) AS blocked_quantity,\n", "          sum(actual_networth) AS actual_networth,\n", "          sum(blocked_networth) AS blocked_networth\n", "   FROM item_inv_outlet ijo\n", "   INNER JOIN outlet_filtering OF ON ijo.outlet_id=OF.outlet_id\n", "   AND ijo.date_of_snapshot=of.date_of_snapshot\n", "   LEFT JOIN pos_console_location pcl ON OF.tax_location_id=pcl.id\n", "   WHERE actual_quantity>0\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6) \n", "            ,\n", "            base_detail AS\n", "  ( SELECT ii.*,\n", "           cf.name AS facility_name,\n", "           CASE\n", "               WHEN actual_networth>0 THEN actual_networth/actual_quantity\n", "               WHEN blocked_networth>0 THEN blocked_networth/blocked_quantity\n", "               ELSE 0\n", "           END AS avergae_landing_price\n", "   FROM item_inv ii\n", "   LEFT JOIN crates_facility cf ON ii.facility_id=cf.id) \n", "  ,\n", "  level_of_table as\n", "  (\n", "   select a.*\n", "   from\n", "   (\n", "   select a.*,\n", "   rank() over(partition  by item_id,date_of_snapshot,facility_id order by metering desc ) as rank_1\n", "   from  base_detail as a\n", "  ) as a\n", "  where rank_1=1\n", "   )\n", "   \n", "   select * from level_of_table\n", "            \n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values = pandas.read_sql(sql=query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_values[\"date_of_snapshot\"] = pandas.to_datetime(inventory_values.date_of_snapshot)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["con1 = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "select\n", "  a.outlet_id as retail_outlet_id,\n", "  a.item_id,\n", "  a.blocked_quantity as sto_quantity\n", "  from ims.ims_sto_blocked_item_log as a\n", "  inner join \n", "  (select outlet_id, item_id, max(updated_at) as updated_at from ims.ims_sto_blocked_item_log \n", "   where convert_tz(updated_at,\"+00:00\",\"+05:30\")<=DATE_SUB(NOW(), INTERVAL 1 DAY)\n", "  group by outlet_id,item_id\n", "  ) b\n", "  on a.outlet_id=b.outlet_id\n", "  and a.item_id=b.item_id\n", "  and a.updated_at=b.updated_at\n", "  where convert_tz(a.updated_at,\"+00:00\",\"+05:30\")<=DATE_SUB(NOW(), INTERVAL 1 DAY)\n", " \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data = pandas.read_sql_query(sql=sql, con=con1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility = sto_data.merge(facility_mapping, on=\"retail_outlet_id\", how=\"inner\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility = sto_data_facility[[\"item_id\", \"facility_id\", \"sto_quantity\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility = sto_data_facility.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility.item_id = sto_data_facility.item_id.astype(int)\n", "sto_data_facility.facility_id = sto_data_facility.facility_id.astype(int)\n", "inventory_values.item_id = inventory_values.item_id.astype(int)\n", "inventory_values.facility_id = inventory_values.facility_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility_level = (\n", "    sto_data_facility.groupby([\"item_id\", \"facility_id\"])[[\"sto_quantity\"]].max().reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto_data_facility_level.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_data = inventory_values.merge(\n", "    sto_data_facility_level, on=[\"item_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_selling_price_query = \"\"\"with dates as \n", "(SELECT DISTINCT date(install_ts) AS order_date\n", "FROM oms_suborder\n", "   WHERE date(install_ts) BETWEEN '{start_date}' AND '{end_date}'),\n", "selling_price_hist as \n", "(select inventory_id,pii.selling_price,effective_from,effective_till,order_date,variant_id,threshold_quantity,outlet_id from \n", "dates d\n", "left join consumer.pos_ims_selling_price_history  pisph\n", "on d.order_date>=pisph.effective_from\n", "and d.order_date<=pisph.effective_till\n", "left join consumer.pos_ims_inventory pii\n", "on pisph.inventory_id=pii.id),\n", "\n", "item_sp_details as \n", "(select item_id,order_date,outlet_id,avg(selling_price) as selling_price,max(threshold_quantity) as threshold_quantity from \n", "(select sph.*,item_id from selling_price_hist sph\n", "left join pos_product_product ppp\n", "on sph.variant_id=ppp.variant_id)a\n", "group by 1,2,3)\n", "\n", "select item_id,facility_id,order_date,avg(selling_price) as selling_price,max(threshold_quantity) as threshold_quantity from item_sp_details spd\n", "left join pos_console_outlet pco\n", "on spd.outlet_id=pco.id\n", "group by 1,2,3\n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_selling_price_values = pandas.read_sql_query(sql=ims_selling_price_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_engine_uri = \"******************************************************************************************************************************/pricing\"\n", "pricing_engine = sqla.create_engine(pricing_engine_uri)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_query = \"\"\"WITH max_rows AS (\n", "    SELECT\n", "      cms_store_id,\n", "      cms_product_id,\n", "      max(id) AS max_id\n", "    FROM pricing_pricing_domain_productchangelog\n", "    WHERE date(install_ts) between'{start_date}' and  '{end_date}'\n", "    GROUP BY 1, 2)\n", "    ,\n", " \n", "all_filled as\n", "(\n", "SELECT \n", "retail_outlet_id,\n", "retail_item_id,\n", "max_rows.cms_store_id,\n", "max_rows.cms_product_id,\n", "new_price,\n", "new_mrp,\n", "install_ts,\n", "(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(evaluation_log,'rule'),'start_date')) as start_date,\n", "(JSON_EXTRACT_PATH_TEXT(JSON_EXTRACT_PATH_TEXT(evaluation_log,'rule'),'end_date')) as end_date\n", "FROM pricing_pricing_domain_productchangelog pdp\n", "  INNER JOIN max_rows ON max_rows.max_id = pdp.id\n", "  )\n", "  \n", "  select * from all_filled\n", "\n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values = pandas.read_sql(sql=pricing_selling_price_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values[\"start_date\"] = pandas.to_datetime(\n", "    pricing_selling_price_values.start_date\n", ")\n", "pricing_selling_price_values[\"end_date\"] = pandas.to_datetime(pricing_selling_price_values.end_date)\n", "\n", "\n", "# po_date=(datetime.now()-timed<PERSON>ta(days=2)).strftime('%Y-%m-%d')\n", "# po_date_30=(datetime.now()-timed<PERSON>ta(days=3)).strftime('%Y-%m-%d')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales_query = \"\"\"\n", "with invoice AS\n", "  (SELECT id ,\n", "          outlet_id,\n", "          grofers_order_id,\n", "          date(created_at) AS pos_timestamp\n", "   FROM pos_pos_invoice\n", "   WHERE invoice_type_id=1\n", "     AND date(created_at) between '{start_date}' AND '{end_date}'),\n", "      delivered_orders AS\n", "  (SELECT os.id AS grofers_order_id\n", "   FROM oms_suborder os\n", "   INNER JOIN oms_order oo ON os.order_id=oo.id\n", "   WHERE oo.current_status='DELIVERED'\n", "     AND os.current_status='AT_DELIVERY_CENTER'),\n", "      product_invoice_details AS\n", "  (SELECT variant_id,\n", "          invoice_id,\n", "          sum(quantity) AS sales_qty,\n", "          sum(quantity* selling_price) AS sales\n", "   FROM pos_pos_invoice_product_details pd\n", "   GROUP BY 1,\n", "            2),\n", "      sales AS\n", "  (SELECT ppp.item_id,\n", "          i.pos_timestamp,\n", "          pco.facility_id,\n", "          tax_location_id AS city_id,\n", "          sum(pid.sales_qty) AS sales_qty,\n", "          sum(pid.sales) AS sales\n", "   FROM delivered_orders\n", "   INNER JOIN invoice i ON delivered_orders.grofers_order_id=i.grofers_order_id\n", "   INNER JOIN product_invoice_details pid ON i.id=pid.invoice_id\n", "   INNER JOIN pos_product_product ppp ON pid.variant_id=ppp.variant_id\n", "   LEFT JOIN pos_console_outlet pco ON i.outlet_id=pco.id\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "            select * from sales\n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales = pandas.read_sql(sql=delivered_sales_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales[\"pos_timestamp\"] = pandas.to_datetime(delivered_sales.pos_timestamp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_query = \"\"\"\n", " with order_details AS\n", "  (SELECT id,\n", "          product_id,\n", "          order_date,\n", "          backend_merchant_id,\n", "          external_id,\n", "          city,\n", "          outlet_id,\n", "          facility_id,\n", "          sum(quantity) AS prod_quantity\n", "   FROM\n", "     (SELECT os.id,\n", "             ooi.product_id,\n", "             date(os.install_ts) AS order_date,\n", "             os.backend_merchant_id,\n", "             om.external_id,\n", "             cocs.outlet_id,\n", "             cl.name AS city,\n", "             co.facility_id,\n", "             oss.quantity\n", "      FROM\n", "        (SELECT id,\n", "                install_ts,\n", "                TYPE ,\n", "                backend_merchant_id\n", "         FROM oms_suborder\n", "         WHERE date(install_ts)  between '{start_date}' AND '{end_date}'\n", "           AND TYPE='RetailSuborder'\n", "         GROUP BY id,\n", "                  install_ts,\n", "                  TYPE,\n", "                  backend_merchant_id)os\n", "      LEFT JOIN oms_suborder_item oss ON os.id=oss.suborder_id\n", "      INNER JOIN oms_order_item ooi ON oss.order_item_id=ooi.id\n", "      LEFT JOIN oms_merchant om--\n", " ON os.backend_merchant_id=om.id\n", "      LEFT JOIN rt_console_outlet_cms_store cocs ON om.external_id=cocs.cms_store\n", "      INNER JOIN pos_console_outlet co ON cocs.outlet_id = co.id\n", "      INNER JOIN pos_console_location cl ON co.tax_location_id = cl.id\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,\n", "               5,\n", "               6,\n", "               7,\n", "               8,9)a\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,8) ,\n", "     item_product_mapping AS --to get item_id and product id mapping\n", "\n", "  (SELECT product_id,\n", "          item_id,\n", "          multiplier ,\n", "          updated_on\n", "   FROM it_pd_log), products_updated_dates AS\n", "  (SELECT a.product_id,\n", "          order_date,\n", "          MAX(updated_on) AS updated_list\n", "   FROM\n", "     (SELECT product_id,\n", "             ordeR_date\n", "      FROM order_details\n", "      GROUP BY product_id,\n", "               order_date)A\n", "   LEFT JOIN it_pd_log ip ON (a.product_id)=(ip.product_id)\n", "   WHERE date(order_date)-date(updated_on)>=0---to get latest item,product mapping of given the order date\n", "\n", "   GROUP BY a.product_id,\n", "            order_date),\n", "                    order_item_mapping AS ---using product item mapping and mapping it to orders\n", "\n", "  (SELECT od.order_date,\n", "          city,\n", "          outlet_id,\n", "          od.facility_id,\n", "          od.product_id,\n", "          prod_quantity,\n", "          updated_list ,\n", "          item_id,\n", "          (prod_quantity*coalesce(multiplier,1)) AS item_quantity\n", "   FROM\n", "     (SELECT product_id,\n", "             order_date,\n", "             city,\n", "             facility_id,\n", "             outlet_id,\n", "             sum(prod_quantity) AS prod_quantity\n", "      FROM order_details\n", "      GROUP BY 1,\n", "               2,\n", "               3,\n", "               4,5) od\n", "   INNER JOIN products_updated_dates prod ON od.product_id=prod.product_id\n", "   AND date(od.order_date)=date(prod.order_date)\n", "   INNER JOIN item_product_mapping ipm ON prod.product_id=ipm.product_id\n", "   AND date(prod.updated_list)=date(ipm.updated_on)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6,\n", "            7,\n", "            8,9), cpd AS\n", "  (SELECT city,\n", "          item_id,\n", "          facility_id,\n", "          outlet_id,\n", "          order_date,\n", "          sum(item_quantity) AS total_item_quantity\n", "   FROM order_item_mapping oi\n", "   GROUP BY 1,\n", "            2,\n", "            3,4,5 --calculating total items ordered in a city at daily level\n", ")\n", "\n", "select cpd.* from cpd\n", "left join pos_console_outlet pco \n", "on cpd.outlet_id=pco.id\n", "WHERE upper(pco.name) like '%%(SSC)%%' \n", "\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales = pandas.read_sql(sql=checkout_sales_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales[\"order_date\"] = pandas.to_datetime(checkout_sales.order_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility = (\n", "    checkout_sales.groupby([\"city\", \"item_id\", \"facility_id\", \"order_date\"])[\n", "        [\"total_item_quantity\"]\n", "    ]\n", "    .sum()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales_query = \"\"\"with invoice AS\n", "  (SELECT id ,\n", "          outlet_id,\n", "          grofers_order_id,\n", "          date(created_at) AS pos_timestamp\n", "   FROM pos_pos_invoice\n", "   WHERE invoice_type_id=1\n", "     AND date(created_at) between '{start_date}' AND '{end_date}'),\n", "      delivered_orders AS\n", "  (SELECT os.id AS grofers_order_id\n", "   FROM oms_suborder os\n", "   INNER JOIN oms_order oo ON os.order_id=oo.id),\n", "      product_invoice_details AS\n", "  (SELECT variant_id,\n", "          invoice_id,\n", "          sum(quantity) AS sales_qty,\n", "          sum(quantity* selling_price) AS sales\n", "   FROM pos_pos_invoice_product_details pd\n", "   GROUP BY 1,\n", "            2),\n", "      sales AS\n", "  (SELECT ppp.item_id,\n", "          i.pos_timestamp,\n", "          pco.facility_id,\n", "          tax_location_id AS city_id,\n", "          sum(pid.sales_qty) AS sales_qty,\n", "          sum(pid.sales) AS sales\n", "   FROM delivered_orders\n", "   INNER JOIN invoice i ON delivered_orders.grofers_order_id=i.grofers_order_id\n", "   INNER JOIN product_invoice_details pid ON i.id=pid.invoice_id\n", "   INNER JOIN pos_product_product ppp ON pid.variant_id=ppp.variant_id\n", "   LEFT JOIN pos_console_outlet pco ON i.outlet_id=pco.id\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "            select * from sales\"\"\".format(\n", "    end_date=end_date, start_date=start_date\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales = pandas.read_sql(sql=billed_sales_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["billed_sales[\"pos_timestamp\"] = pandas.to_datetime(billed_sales.pos_timestamp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delivered_sales[\"pos_timestamp\"] = pandas.to_datetime(delivered_sales.pos_timestamp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_data[\"item_id\"] = inventory_values.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered = inventory_data.merge(\n", "    delivered_sales,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"pos_timestamp\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_df = doi_delivered[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"city_id_x\",\n", "        \"city\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"actual_networth\",\n", "        \"blocked_networth\",\n", "        \"avergae_landing_price\",\n", "        \"facility_name\",\n", "        \"sales_qty\",\n", "        \"sales\",\n", "        \"sto_quantity\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_df[\"delivered_selling_price\"] = doi_delivered_df.sales / doi_delivered_df.sales_qty"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkout_sales_facility[\"item_id\"] = checkout_sales_facility.item_id.astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout = doi_delivered_df.merge(\n", "    checkout_sales_facility,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout = doi_delivered_Checkout.rename(\n", "    columns={\n", "        \"city_id_x\": \"city_id\",\n", "        \"city_x\": \"city_name\",\n", "        \"total_item_quantity\": \"checkout_sales\",\n", "        \"sales_qty\": \"delviered_sales_quantity\",\n", "        \"sales\": \"delivered_sales\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_df = doi_delivered_Checkout[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"actual_networth\",\n", "        \"blocked_networth\",\n", "        \"avergae_landing_price\",\n", "        \"facility_name\",\n", "        \"delviered_sales_quantity\",\n", "        \"delivered_sales\",\n", "        \"delivered_selling_price\",\n", "        \"order_date\",\n", "        \"checkout_sales\",\n", "        \"sto_quantity\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed = doi_delivered_Checkout_df.merge(\n", "    billed_sales[[\"item_id\", \"facility_id\", \"pos_timestamp\", \"sales_qty\", \"sales\"]],\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"pos_timestamp\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed = doi_delivered_Checkout_billed.rename(\n", "    columns={\n", "        \"sales_qty\": \"billed_Sale_quantity\",\n", "        \"sales\": \"billed_sales\",\n", "        \"avergae_landing_price\": \"average_landing_price\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed[\"billed_selling_price\"] = (\n", "    doi_delivered_Checkout_billed.billed_sales / doi_delivered_Checkout_billed.billed_Sale_quantity\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed_df = doi_delivered_Checkout_billed[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"billed_selling_price\",\n", "        \"facility_name\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"average_landing_price\",\n", "        \"delviered_sales_quantity\",\n", "        \"delivered_sales\",\n", "        \"delivered_selling_price\",\n", "        \"billed_Sale_quantity\",\n", "        \"checkout_sales\",\n", "        \"sto_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_delivered_Checkout_billed_df.sample(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values[\"start_date\"] = pandas.to_datetime(\n", "    pricing_selling_price_values.start_date\n", ")\n", "pricing_selling_price_values[\"end_date\"] = pandas.to_datetime(pricing_selling_price_values.end_date)\n", "pricing_selling_price_values[\"install_ts\"] = pandas.to_datetime(\n", "    pricing_selling_price_values.install_ts\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_values.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility = pricing_selling_price_values.merge(\n", "    facility_mapping, on=[\"retail_outlet_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility = pricing_selling_price_facility.rename(\n", "    columns={\"new_price\": \"selling_price\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pricing_selling_price_facility = pricing_selling_price_facility[\n", "    [\n", "        \"retail_outlet_id\",\n", "        \"retail_item_id\",\n", "        \"install_ts\",\n", "        \"selling_price\",\n", "        \"facility_id\",\n", "        \"start_date\",\n", "        \"end_date\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp = doi_delivered_Checkout_billed_df.merge(\n", "    pricing_selling_price_facility,\n", "    left_on=[\"item_id\", \"facility_id\"],\n", "    right_on=[\"retail_item_id\", \"facility_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df = doi_sp[\n", "    (doi_sp.date_of_snapshot <= doi_sp.end_date) & (doi_sp.date_of_snapshot >= doi_sp.start_date)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup = (\n", "    doi_sp_df.groupby([\"item_id\", \"date_of_snapshot\", \"facility_id\"])[[\"install_ts\"]]\n", "    .max()\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_filtered = doi_sp_df[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"metering\",\n", "        \"billed_selling_price\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"average_landing_price\",\n", "        \"delviered_sales_quantity\",\n", "        \"delivered_selling_price\",\n", "        \"billed_Sale_quantity\",\n", "        \"checkout_sales\",\n", "        \"selling_price\",\n", "        \"start_date\",\n", "        \"end_date\",\n", "        \"install_ts\",\n", "    ]\n", "].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup.install_ts = pandas.to_datetime(doi_sp_df_rollup.install_ts, utc=True).dt.date\n", "doi_sp_df_filtered.install_ts = pandas.to_datetime(doi_sp_df_filtered.install_ts, utc=True).dt.date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_sp_df_rollup.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if doi_sp_df_rollup.shape[0] == 0:\n", "    doi_selling_price_merge = doi_sp_df_filtered\n", "else:\n", "    doi_selling_price_merge = doi_sp_df_filtered.merge(\n", "        doi_sp_df_rollup,\n", "        on=[\"item_id\", \"facility_id\", \"date_of_snapshot\", \"install_ts\"],\n", "        how=\"inner\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge = doi_selling_price_merge[\n", "    [\"date_of_snapshot\", \"item_id\", \"facility_id\", \"selling_price\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_pricing_check = doi_delivered_Checkout_billed_df.merge(\n", "    doi_selling_price_merge, on=[\"date_of_snapshot\", \"item_id\", \"facility_id\"], how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_selling_price_values = ims_selling_price_values.rename(\n", "    columns={\"selling_price\": \"ims_selling_price\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ims_selling_price_values[\"order_date\"] = pandas.to_datetime(ims_selling_price_values.order_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge_ims = doi_pricing_check.merge(\n", "    ims_selling_price_values,\n", "    left_on=[\"item_id\", \"facility_id\", \"date_of_snapshot\"],\n", "    right_on=[\"item_id\", \"facility_id\", \"order_date\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_selling_price_merge_ims[\"final_selling_price\"] = np.where(\n", "    doi_selling_price_merge_ims.delivered_selling_price.isna(),\n", "    np.where(\n", "        doi_selling_price_merge_ims.billed_selling_price.isna(),\n", "        np.where(\n", "            doi_selling_price_merge_ims.ims_selling_price.isna(),\n", "            doi_selling_price_merge_ims.selling_price,\n", "            doi_selling_price_merge_ims.ims_selling_price,\n", "        ),\n", "        doi_selling_price_merge_ims.billed_selling_price,\n", "    ),\n", "    doi_selling_price_merge_ims.delivered_selling_price,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final = doi_selling_price_merge_ims[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"average_landing_price\",\n", "        \"delviered_sales_quantity\",\n", "        \"billed_Sale_quantity\",\n", "        \"checkout_sales\",\n", "        \"final_selling_price\",\n", "        \"sto_quantity\",\n", "        \"threshold_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final = doi_df_final.rename(\n", "    columns={\n", "        \"delviered_sales_quantity\": \"delivered_sales_quantity\",\n", "        \"checkout_sales\": \"checkout_sales_quantity\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final[\"threshold_quantity\"] = doi_df_final[\"threshold_quantity\"].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_query = \"\"\"WITH PRODUCT_Category AS\n", " (SELECT P.ID AS PID,\n", "         P.NAME AS PRODUCT,\n", "         P.UNIT AS Unit,\n", "         C2.NAME AS L2,\n", "         (case when C1.NAME = C.name then C2.name else C1.name end) AS L1,\n", "         C.NAME AS L0,\n", "         P.BRAND AS brand,\n", "         P.MANUFACTURER AS manf,\n", "         pt.name as product_type\n", "  FROM GR_PRODUCT P\n", "  INNER JOIN GR_PRODUCT_CATEGORY_MAPPING PCM ON P.ID = PCM.PRODUCT_ID\n", "  INNER JOIN GR_CATEGORY C2 ON PCM.CATEGORY_ID = C2.ID\n", "  AND PCM.IS_PRIMARY=TRUE\n", "  INNER JOIN GR_CATEGORY C1 ON C2.PARENT_CATEGORY_ID = C1.ID\n", "  INNER JOIN GR_CATEGORY C ON C1.PARENT_CATEGORY_ID = C.ID\n", "  inner join gr_product_type pt on p.type_id=pt.id\n", "\n", "  )\n", "\n", "SELECT item_id,\n", "      product_id,\n", "      (cat.product || ' ' || cat.unit) AS name,\n", "      cat.L0,\n", "      cat.l1,\n", "      cat.l2 ,\n", "      cat.brand,\n", "      cat.manf,\n", "      cat.product_type\n", "FROM rpc_item_product_mapping rpc\n", "INNER JOIN product_category cat ON rpc.product_id=cat.pid\n", "AND rpc.offer_id IS NULL\n", "AND rpc.item_id IS NOT NULL\n", "AND rpc.product_id IS NOT NULL\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_mapping = pandas.read_sql(sql=catg_query, con=con)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["catg_mapping.sample()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg = doi_df_final.merge(\n", "    catg_mapping[[\"item_id\", \"l0\", \"l1\", \"l2\", \"product_type\"]], on=[\"item_id\"], how=\"left\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["doi_df_final is the dataframe which is to be populated in the table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg = doi_df_final_catg[\n", "    [\n", "        \"item_id\",\n", "        \"date_of_snapshot\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"metering\",\n", "        \"actual_quantity\",\n", "        \"blocked_quantity\",\n", "        \"average_landing_price\",\n", "        \"delivered_sales_quantity\",\n", "        \"billed_Sale_quantity\",\n", "        \"checkout_sales_quantity\",\n", "        \"final_selling_price\",\n", "        \"threshold_quantity\",\n", "        \"l0\",\n", "        \"l1\",\n", "        \"l2\",\n", "        \"product_type\",\n", "        \"sto_quantity\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doi_df_final_catg.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"item_id\", \"type\": \"int\"},\n", "    {\"name\": \"date_of_snapshot\", \"type\": \"date\"},\n", "    {\"name\": \"facility_id\", \"type\": \"int\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"city_id\", \"type\": \"int\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar(100)\"},\n", "    {\"name\": \"metering\", \"type\": \"boolean\"},\n", "    {\"name\": \"actual_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"blocked_quantity\", \"type\": \"int\"},\n", "    {\"name\": \"average_landing_price\", \"type\": \"float\"},\n", "    {\"name\": \"delivered_sales_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"billed_Sale_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"checkout_sales_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"final_selling_price\", \"type\": \"float\"},\n", "    {\"name\": \"threshold_quantity\", \"type\": \"float\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"l1\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"l2\", \"type\": \"varchar(1000)\"},\n", "    {\"name\": \"product_type\", \"type\": \"varchar(100)\"},\n", "    {\"name\": \"sto_quantity\", \"type\": \"float\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"doi_daily_report\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"date_of_snapshot\", \"item_id\", \"facility_id\"],\n", "    \"sortkey\": \"date_of_snapshot\",\n", "    \"incremental_key\": \"date_of_snapshot\",\n", "    \"distkey\": \"item_id\",\n", "    \"load_type\": \"upsert\",  # append, rebuild, truncate or upsert\n", "}\n", "\n", "pb.to_redshift(doi_df_final_catg, **kwargs)\n", "# # # # or\n", "# # # pb.to_redshift(local_filename, **kwargs)"]}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "pycharm": {"stem_cell": {"cell_type": "raw", "metadata": {"collapsed": false}, "source": []}}}, "nbformat": 4, "nbformat_minor": 2}