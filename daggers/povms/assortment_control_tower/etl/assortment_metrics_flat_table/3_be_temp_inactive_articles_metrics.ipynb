{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "                break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "19eddc0e-2085-4b16-9165-62f8d129e835", "metadata": {}, "outputs": [], "source": ["be_active_to_temp_inactive_article_query = read_sql_query(\n", "    f\"\"\"\n", "\n", "WITH active_transfers AS (\n", "SELECT      facility_id, item_id, master_assortment_substate_id, updated_at\n", "FROM        rpc.product_facility_master_assortment a\n", "WHERE       a.active = 1 and a.lake_active_record and a.master_assortment_substate_id IN (1,3) ),\n", "\n", "transfer_tea_tagging AS (\n", "SELECT outlet_id, item_id, CAST(tag_value AS int) AS be_outlet_id\n", "FROM  rpc.item_outlet_tag_mapping b\n", "WHERE b.active = 1 and b.lake_active_record AND b.tag_type_id = 8 and tag_value != '0' ),\n", "\n", "fe AS (\n", "SELECT pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "be AS (\n", "SELECT      pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name, od.inv_outlet_id\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id NOT IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "INNER JOIN  supply_etls.outlet_details od\n", "            ON  od.hot_outlet_id = pfom.outlet_id and od.store_type = 'Packaged Goods' and od.active = 1 \n", "                    and od.ars_active = 1 and od.ars_check = 1 and od.grocery_active_count > 0\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "latest_facility_item_combination_activity AS (\n", "SELECT  facility_id, item_id, MAX(created_at) AS latest_action_date\n", "FROM    rpc.product_facility_master_assortment_log\n", "WHERE   insert_ds_ist >= CAST(CURRENT_DATE - INTERVAL '1' YEAR AS VARCHAR) AND lake_active_record\n", "GROUP BY    1,2 ),\n", "\n", "assort_shift_from_active_to_tempinactive AS (\n", "SELECT      a.facility_id, a.item_id, 'Active_to_Temp_Inactive' AS action_status, a.created_at\n", "FROM        rpc.product_facility_master_assortment_log a\n", "INNER JOIN  latest_facility_item_combination_activity c\n", "            ON  c.facility_id = a.facility_id AND c.item_id = a.item_id AND c.latest_action_date = a.created_at \n", "                and a.insert_ds_ist >= CAST(CURRENT_DATE - INTERVAL '1' YEAR AS VARCHAR) AND a.lake_active_record\n", "                and a.new_substate = 3 and a.old_substate = 1 \n", "GROUP BY    1,2,3,4 ),\n", "\n", "be_temp_inactive_item_comb_base AS (\n", "SELECT     t.be_outlet_id, be.facility_id AS be_facility_id, be.inv_outlet_id AS be_inv_outlet_id, be.outlet_name AS be_outlet_name\n", "            , it.l0_category, it.p_type, i.item_id, it.item_name\n", "            , MAX(created_at) AS article_temp_inactive_ts, COUNT(DISTINCT i.facility_id) as transfer_stores_cnt\n", "            , COUNT(DISTINCT CASE WHEN action_status = 'Active_to_Temp_Inactive' THEN i.facility_id END) as store_cnt_with_assort_shift\n", "            , ((COUNT(DISTINCT CASE WHEN action_status = 'Active_to_Temp_Inactive' THEN i.facility_id END) * 100) \n", "            / count(DISTINCT i.facility_id)) as store_cnt_with_assort_shift_per\n", "            , SUM(CASE WHEN action_status = 'Active_to_Temp_Inactive' THEN fs.total_selling_price END) AS gmv\n", "\n", "FROM        dwh.fact_sales_order_item_details fs\n", "INNER JOIN  dwh.dim_item_product_offer_mapping ip \n", "            ON ip.product_id = fs.product_id and ip.is_current \n", "             and date_trunc('second', fs.order_create_ts_ist) >= date_trunc('second', ip.valid_from_ts_ist)\n", "             and date_trunc('second', fs.order_create_ts_ist) < date_trunc('second', ip.valid_to_ts_ist) AND fs.total_selling_price > 0\n", "INNER JOIN  transfer_tea_tagging t \n", "            ON t.outlet_id = fs.outlet_id AND t.item_id = ip.item_id\n", "INNER JOIN  fe\n", "            ON fe.outlet_id = t.outlet_id \n", "INNER JOIN  be\n", "            ON be.outlet_id = t.be_outlet_id \n", "INNER JOIN  active_transfers i\n", "            ON fe.facility_id = i.facility_id AND i.item_id = t.item_id\n", "INNER JOIN  supply_etls.item_details it\n", "            ON  it.assortment_type = 'Packaged Goods' AND it.item_id = t.item_id \n", "                AND it.l0_category NOT IN ('No Mapping', 'Specials', 'Ice Creams & Frozen Desserts', 'HP', 'Trial new tree', 'Chicken, Meat & Fish')\n", "LEFT JOIN   assort_shift_from_active_to_tempinactive o\n", "            ON i.facility_id = o.facility_id AND i.item_id = o.item_id \n", "WHERE   fs.order_create_dt_ist >= current_date - interval '30' day AND fs.cart_checkout_ts_ist >= current_date - interval '30' day\n", "        AND fs.order_create_dt_ist < current_date AND fs.cart_checkout_ts_ist < current_date \n", "        AND fs.order_current_status = 'DELIVERED' and is_internal_order = false AND org_channel_name = 'BLINKIT' and 1=1\n", "GROUP BY    1,2,3,4,5,6,7,8 ),\n", "\n", "be_temp_inactive_articles AS (\n", "SELECT  a.be_outlet_id, a.be_inv_outlet_id, a.be_facility_id, a.be_outlet_name, a.item_id, a.item_name, a.p_type, a.l0_category\n", "        , MAX(a.article_temp_inactive_ts) as article_temp_inactive_ts, MAX(transfer_stores_cnt) AS connected_stores, SUM(gmv) AS gmv\n", "        , COALESCE(SUM(remaining_po_quantity),0) AS open_po_units, SUM(CAST(COALESCE(remaining_po_quantity,0) AS DOUBLE) * COALESCE(po_landing_price,0)) AS open_po_value\n", "\n", "FROM        be_temp_inactive_item_comb_base a\n", "LEFT JOIN   supply_etls.inventory_metrics_open_po b\n", "            ON  a.be_facility_id = b.facility_id AND a.item_id = b.item_id -- AND b.po_created_at_ist >= CURRENT_DATE - INTERVAL '6' DAY \n", "                AND b.po_created_at_ist >= a.article_temp_inactive_ts and remaining_po_quantity > 0 \n", "WHERE       a.store_cnt_with_assort_shift_per = 100 \n", "GROUP BY    1,2,3,4,5,6,7,8 )\n", "\n", "SELECT      CURRENT_DATE AS insert_ds_ist, be_outlet_id, be_inv_outlet_id, be_facility_id, be_outlet_name, item_id, item_name, p_type, l0_category\n", "            , 'temp-inactive article' AS metric_grain, gmv -- gmv_from_temp_inactive_articles_at_connected_stores\n", "            , connected_stores, open_po_units, open_po_value, article_temp_inactive_ts\n", "FROM        be_temp_inactive_articles \n", "\n", "\"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e399f958-0b5b-426c-b0a8-5f990b371af7", "metadata": {}, "outputs": [], "source": ["be_active_to_temp_inactive_article_query[\"be_outlet_id\"].count()"]}, {"cell_type": "code", "execution_count": null, "id": "3936d657-e150-4d12-987d-d23a60daf62b", "metadata": {}, "outputs": [], "source": ["be_active_to_temp_inactive_article_query[\"insert_ds_ist\"] = pd.to_datetime(\n", "    be_active_to_temp_inactive_article_query[\"insert_ds_ist\"]\n", ").dt.date\n", "be_active_to_temp_inactive_article_query[\"article_temp_inactive_ts\"] = pd.to_datetime(\n", "    be_active_to_temp_inactive_article_query[\"article_temp_inactive_ts\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a5fdd3a4-af9a-4a5d-89cd-625e2f7941b8", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"insert_ds_ist\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_inv_outlet_id\", \"type\": \"integer\", \"description\": \"be_inv_outlet_id\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"be_outlet_name\", \"type\": \"varchar\", \"description\": \"be_outlet_name\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"p_type\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"l0_category\"},\n", "    {\"name\": \"metric_grain\", \"type\": \"varchar\", \"description\": \"metric_grain\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"gmv\"},\n", "    {\"name\": \"connected_stores\", \"type\": \"integer\", \"description\": \"connected_stores\"},\n", "    {\"name\": \"open_po_units\", \"type\": \"integer\", \"description\": \"open_po_units\"},\n", "    {\"name\": \"open_po_value\", \"type\": \"real\", \"description\": \"open_po_value\"},\n", "    {\n", "        \"name\": \"article_temp_inactive_ts\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"article_temp_inactive_ts\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "39276ba8-23b4-4de8-af34-27430a40a163", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_temp_inactive_articles_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"be_outlet_id\", \"item_id\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": 'date_',\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"table shows the gmv contributed by temp-inactice articles from each backend \",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "93c99266-7e08-4695-9693-b1108bbd9bfd", "metadata": {}, "outputs": [], "source": ["to_trino(\n", "    be_active_to_temp_inactive_article_query,\n", "    kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"],\n", "    **kwargs\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2ee68964-faf9-4e11-b1b5-cf47196cd4ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "toc-autonumbering": true, "toc-showmarkdowntxt": false}, "nbformat": 4, "nbformat_minor": 5}