{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "                break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "19eddc0e-2085-4b16-9165-62f8d129e835", "metadata": {}, "outputs": [], "source": ["be_inactive_article_query = read_sql_query(\n", "    f\"\"\"\n", "\n", "WITH transfer_tea_tagging AS (\n", "SELECT outlet_id, item_id, CAST(tag_value AS INT) AS be_outlet_id\n", "FROM  rpc.item_outlet_tag_mapping b\n", "WHERE b.active = 1 and b.lake_active_record AND b.tag_type_id = 8 and tag_value != '0' ),\n", "\n", "fe AS (\n", "SELECT pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "be AS (\n", "SELECT      pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name, od.inv_outlet_id\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id NOT IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "INNER JOIN  supply_etls.outlet_details od\n", "            ON  od.hot_outlet_id = pfom.outlet_id and od.store_type = 'Packaged Goods' and od.active = 1 \n", "                    and od.ars_active = 1 and od.ars_check = 1 and od.grocery_active_count > 0\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "block_invt AS (\n", "SELECT  outlet_id, item_id, SUM(quantity) AS blocked_inventory\n", "FROM    ims.ims_item_blocked_inventory\n", "WHERE   lake_active_record = true AND active = 1 and quantity > 0\n", "GROUP BY    1,2 ),\n", "\n", "outlet_inventory_info AS (\n", "SELECT  i.outlet_id AS inv_outlet_id, i.item_id\n", "        , SUM(CASE WHEN (i.quantity - coalesce(b.blocked_inventory,0) > 0) THEN (i.quantity - coalesce(b.blocked_inventory,0)) ELSE 0 END) \n", "        AS net_onshelf_invt\n", "\n", "FROM        ims.ims_item_inventory i\n", "LEFT JOIN   block_invt b \n", "            ON i.item_id = b.item_id AND i.outlet_id = b.outlet_id\n", "WHERE i.active = 1 and i.lake_active_record = true\n", "GROUP BY    1,2 ),\n", "\n", "item_transfer_status AS (\n", "SELECT      facility_id, item_id, master_assortment_substate_id, updated_at, created_at\n", "FROM        rpc.product_facility_master_assortment a\n", "WHERE       a.active = 1 and a.lake_active_record ),\n", "\n", "be_inactive_item_comb_base AS (\n", "SELECT      be.outlet_id AS be_outlet_id, be.inv_outlet_id AS be_inv_outlet_id, be.facility_id AS be_facility_id, be.outlet_name AS be_outlet_name\n", "            , inv.item_id, it.item_name, it.p_type, it.l0_category, COUNT(tea.outlet_id) AS connected_stores\n", "            , COUNT(CASE WHEN (its.master_assortment_substate_id IN (2,4) OR its.master_assortment_substate_id IS NULL) THEN tea.outlet_id END) AS inactive_stores\n", "            , ROUND(( (CAST(COUNT(CASE WHEN (its.master_assortment_substate_id IN (2,4) OR its.master_assortment_substate_id IS NULL)\n", "                    THEN tea.outlet_id END) AS DOUBLE) * 100) / COUNT(tea.outlet_id)),1) AS inactive_stores_per\n", "            , COALESCE(ROUND(MAX(inv.net_onshelf_invt),1),0) AS be_inv_units\n", "            , DATE_DIFF('DAY', MAX(DATE(its.updated_at)), CURRENT_DATE) AS num_past_gap_days\n", "            \n", "FROM        outlet_inventory_info inv\n", "INNER JOIN  supply_etls.item_details it\n", "            ON  it.assortment_type = 'Packaged Goods' AND it.item_id = inv.item_id \n", "                AND it.l0_category NOT IN ('No Mapping', 'Specials', 'Ice Creams & Frozen Desserts', 'HP', 'Trial new tree', 'Chicken, Meat & Fish')\n", "INNER JOIN  be\n", "            ON be.inv_outlet_id = inv.inv_outlet_id\n", "INNER JOIN  transfer_tea_tagging tea\n", "            ON tea.item_id = inv.item_id AND be.outlet_id = tea.be_outlet_id\n", "INNER JOIN  fe\n", "            ON fe.outlet_id = tea.outlet_id\n", "LEFT JOIN   item_transfer_status its \n", "            ON its.item_id = inv.item_id AND its.facility_id = fe.facility_id and 1=1\n", "WHERE       inv.net_onshelf_invt > 0 \n", "GROUP BY    1,2,3,4,5,6,7,8 ),\n", "\n", "be_var_lp AS (\n", "select\n", "    distinct \n", "    outlet_id AS be_inv_outlet_id,\n", "    variant_id,\n", "    round(landing_price,2) landing_price\n", "from (\n", "        select distinct \n", "            outlet_id, \n", "            ims.variant_id, \n", "            landing_price, \n", "            ims.updated_at, \n", "            row_number() over(partition by outlet_id,variant_id order by (ims.updated_at + interval '330' minute) desc) as rnk\n", "        from ims.ims_entity_vendor_inventory_landing_price ims\n", "        WHERE active AND lake_active_record \n", "    )\n", "where rnk = 1 ),\n", "\n", "latest_ts_be_item_comb AS (\n", "SELECT      v.be_inv_outlet_id, m.item_id, m.variant_id -- , MAX(m.created_at) AS latest_created_ts \n", "            , DENSE_RANK() OVER (PARTITION BY v.be_inv_outlet_id, m.item_id ORDER BY m.created_at DESC, m.variant_mrp DESC, m.approved_at DESC) AS rnk\n", "FROM        rpc.product_product m\n", "INNER JOIN  be_var_lp v\n", "            ON v.variant_id = m.variant_id AND m.active = 1 AND m.lake_active_record ),\n", "\n", "be_inactive_articles AS (\n", "SELECT      b.be_outlet_id, b.be_inv_outlet_id, b.be_facility_id, b.be_outlet_name, b.item_id, b.item_name, b.p_type, b.l0_category\n", "            , b.connected_stores, b.inactive_stores, b.inactive_stores_per, v.landing_price, l.variant_id\n", "            , b.be_inv_units, b.num_past_gap_days, ( CAST(b.be_inv_units AS DOUBLE) * COALESCE(v.landing_price,0)) AS cost_price\n", "\n", "FROM        be_inactive_item_comb_base b\n", "LEFT JOIN   latest_ts_be_item_comb l\n", "            ON b.be_inv_outlet_id = l.be_inv_outlet_id AND b.item_id = l.item_id AND rnk = 1\n", "LEFT JOIN   rpc.product_product m\n", "            ON m.item_id = l.item_id AND l.variant_id = m.variant_id AND m.active = 1 AND m.lake_active_record  \n", "LEFT JOIN   be_var_lp v\n", "            ON v.variant_id = m.variant_id AND v.be_inv_outlet_id = l.be_inv_outlet_id \n", "WHERE       b.inactive_stores_per = 100 AND b.num_past_gap_days >= 30 )\n", "\n", "SELECT      CURRENT_DATE AS insert_ds_ist, be_outlet_id, be_inv_outlet_id, be_facility_id, be_outlet_name, item_id, item_name, p_type, l0_category\n", "            , 'inactive article' AS metric_grain, connected_stores, be_inv_units, cost_price, num_past_gap_days\n", "            --, b.inactive_stores, b.inactive_stores_per, b.landing_price, b.variant_id\n", "FROM        be_inactive_articles\n", "\n", "\"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b86e9618-ad7f-4d94-81f2-5bdd68878954", "metadata": {}, "outputs": [], "source": ["be_inactive_article_query[\"insert_ds_ist\"] = pd.to_datetime(\n", "    be_inactive_article_query[\"insert_ds_ist\"]\n", ").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "b21b5cda-7bc8-42f6-bcc2-1e3f57879e9a", "metadata": {}, "outputs": [], "source": ["be_inactive_article_query[\"be_outlet_id\"].count()"]}, {"cell_type": "code", "execution_count": null, "id": "a5fdd3a4-af9a-4a5d-89cd-625e2f7941b8", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"insert_ds_ist\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"be_outlet_id\"},\n", "    {\"name\": \"be_inv_outlet_id\", \"type\": \"integer\", \"description\": \"be_inv_outlet_id\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"be_facility_id\"},\n", "    {\"name\": \"be_outlet_name\", \"type\": \"varchar\", \"description\": \"be_outlet_name\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"p_type\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"l0_category\"},\n", "    {\"name\": \"metric_grain\", \"type\": \"varchar\", \"description\": \"metric_grain\"},\n", "    {\"name\": \"connected_stores\", \"type\": \"integer\", \"description\": \"connected_stores\"},\n", "    {\"name\": \"be_inv_units\", \"type\": \"integer\", \"description\": \"be_inv_units\"},\n", "    {\"name\": \"cost_price\", \"type\": \"real\", \"description\": \"cost_price\"},\n", "    {\"name\": \"num_past_gap_days\", \"type\": \"integer\", \"description\": \"num_past_gap_days\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "39276ba8-23b4-4de8-af34-27430a40a163", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_inactive_articles_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"be_outlet_id\", \"item_id\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": 'date_',\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"table shows the inventory holding cost of inactive articles across backend\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "93c99266-7e08-4695-9693-b1108bbd9bfd", "metadata": {}, "outputs": [], "source": ["to_trino(be_inactive_article_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "2ee68964-faf9-4e11-b1b5-cf47196cd4ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "toc-autonumbering": true, "toc-showmarkdowntxt": false}, "nbformat": 4, "nbformat_minor": 5}