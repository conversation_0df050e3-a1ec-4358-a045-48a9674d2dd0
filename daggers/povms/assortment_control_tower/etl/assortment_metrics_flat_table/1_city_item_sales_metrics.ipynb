{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "                break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "19eddc0e-2085-4b16-9165-62f8d129e835", "metadata": {}, "outputs": [], "source": ["city_item_sales_query = read_sql_query(\n", "    f\"\"\"\n", "\n", "WITH active_transfers AS (\n", "SELECT      facility_id, item_id, master_assortment_substate_id, updated_at, created_at\n", "FROM        rpc.product_facility_master_assortment a\n", "WHERE       a.active = 1 and a.lake_active_record and master_assortment_substate_id IN (1,3) ),\n", "\n", "transfer_tea_tagging AS (\n", "SELECT outlet_id, item_id, CAST(tag_value AS DOUBLE) AS be_outlet_id\n", "FROM  rpc.item_outlet_tag_mapping b\n", "WHERE b.active = 1 and b.lake_active_record AND b.tag_type_id = 8 and tag_value != '0' ),\n", "\n", "fe AS (\n", "SELECT pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "be AS (\n", "SELECT pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name, od.inv_outlet_id\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id NOT IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "INNER JOIN  supply_etls.outlet_details od\n", "            ON  od.hot_outlet_id = pfom.outlet_id and od.store_type = 'Packaged Goods' and od.active = 1 \n", "                    and od.ars_active = 1 and od.ars_check = 1 and od.grocery_active_count > 0\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "-- active_and_tea_tag\n", "city_item_lvl_sales AS (\n", "SELECT      fe.city_id, fe.city_name, it.l0_category, it.p_type, it.item_id, it.item_name\n", "            , COALESCE(SUM(CASE \n", "                    WHEN fs.order_create_dt_ist >= current_date - interval '30' day and fs.order_create_dt_ist < current_date \n", "                            THEN fs.total_selling_price \n", "                  END),0) AS gmv_last_30d_to_curr_day\n", "            , COALESCE(SUM(CASE \n", "                      WHEN fs.order_create_dt_ist >= current_date - interval '60' day \n", "                            and fs.order_create_dt_ist < current_date - interval '30' day THEN fs.total_selling_price \n", "                  END),0) AS gmv_last_60d_to_30d\n", "                  \n", "FROM        dwh.fact_sales_order_item_details fs\n", "INNER JOIN  dwh.dim_item_product_offer_mapping ip \n", "            ON ip.product_id = fs.product_id and ip.is_current \n", "             and date_trunc('second', fs.order_create_ts_ist) >= date_trunc('second', ip.valid_from_ts_ist)\n", "            and date_trunc('second', fs.order_create_ts_ist) < date_trunc('second', ip.valid_to_ts_ist)\n", "INNER JOIN  transfer_tea_tagging t \n", "            ON t.outlet_id = fs.outlet_id AND t.item_id = ip.item_id \n", "INNER JOIN  fe\n", "            ON fe.outlet_id = t.outlet_id\n", "INNER JOIN  be\n", "            ON be.outlet_id = t.be_outlet_id \n", "INNER JOIN  active_transfers i\n", "            ON fe.facility_id = i.facility_id AND i.item_id = t.item_id\n", "INNER JOIN  supply_etls.item_details it\n", "            ON  it.assortment_type = 'Packaged Goods' AND it.item_id = t.item_id \n", "                AND it.l0_category NOT IN ('No Mapping', 'Specials', 'Ice Creams & Frozen Desserts', 'HP', 'Trial new tree', 'Chicken, Meat & Fish')\n", "WHERE   fs.order_create_dt_ist >= current_date - interval '60' day AND fs.cart_checkout_ts_ist >= current_date - interval '60' day \n", "        and fs.order_create_dt_ist < current_date AND fs.cart_checkout_ts_ist < current_date \n", "        AND fs.order_current_status = 'DELIVERED' and is_internal_order = false AND org_channel_name = 'BLINKIT'\n", "        AND fs.total_selling_price > 0\n", "GROUP BY    1,2,3,4,5,6 ),\n", "\n", "universe_data AS (\n", "SELECT      city_id, city_name, l0_category, p_type, item_id, item_name, gmv_last_30d_to_curr_day, gmv_last_60d_to_30d\n", "FROM        city_item_lvl_sales\n", "\n", "UNION ALL\n", "\n", "SELECT      0 AS city_id, 'Pan India' AS city_name, l0_category, p_type, item_id, item_name, SUM(gmv_last_30d_to_curr_day) AS gmv_last_30d_to_curr_day\n", "            , SUM(gmv_last_60d_to_30d) AS gmv_last_60d_to_30d\n", "FROM        city_item_lvl_sales \n", "GROUP BY    1,2,3,4,5,6 ),\n", "\n", "city_l0_item_performance AS (\n", "SELECT      city_id, city_name, l0_category, p_type, item_id, item_name, gmv_last_30d_to_curr_day, gmv_last_60d_to_30d\n", "\n", "            , ((CAST(SUM(gmv_last_30d_to_curr_day) OVER (PARTITION BY city_name ORDER BY gmv_last_30d_to_curr_day DESC) AS DOUBLE) * 100) / \n", "                SUM(gmv_last_30d_to_curr_day) OVER (PARTITION BY city_name)) AS gmv_contri_last_30d_to_curr_day\n", "            , ( CASE  \n", "                    WHEN gmv_last_30d_to_curr_day = 0 THEN NULL\n", "                    ELSE DENSE_RANK() OVER (PARTITION BY city_name ORDER BY gmv_last_30d_to_curr_day DESC) \n", "                END) AS gmv_last_30d_to_curr_day_rnk\n", "\n", "            , ((CAST(SUM(gmv_last_60d_to_30d) OVER (PARTITION BY city_name ORDER BY gmv_last_60d_to_30d DESC) AS DOUBLE) * 100) / \n", "                SUM(gmv_last_60d_to_30d) OVER(PARTITION BY city_name)) AS gmv_contri_last_60d_to_30d\n", "            , (CASE WHEN gmv_last_60d_to_30d = 0 THEN NULL\n", "                    ELSE DENSE_RANK() OVER (PARTITION BY city_name ORDER BY gmv_last_60d_to_30d DESC) \n", "                END) AS gmv_last_60d_to_30d_rnk\n", "\n", "FROM        universe_data )\n", "\n", "SELECT      CAST(current_date AS DATE) AS insert_ds_ist, city_id, city_name, l0_category, p_type, item_id, item_name\n", "            -- , ( CASE\n", "            --         WHEN gmv_last_30d_to_curr_day >= 10000000 THEN '₹ '|| CAST(ROUND(CAST(gmv_last_30d_to_curr_day AS DECIMAL(18,1)) / 10000000, 1) AS VARCHAR) || ' Cr' \n", "            --         WHEN gmv_last_30d_to_curr_day >= 100000 THEN '₹ '|| CAST(ROUND(CAST(gmv_last_30d_to_curr_day AS DECIMAL(18,1)) / 100000, 1) AS VARCHAR) || ' L' \n", "            --         WHEN gmv_last_30d_to_curr_day >= 1000 THEN '₹ '|| CAST(ROUND(CAST(gmv_last_30d_to_curr_day AS DECIMAL(18,1)) / 1000, 1) AS VARCHAR) || ' K' \n", "            --         ELSE '₹ '|| CAST(gmv_last_30d_to_curr_day AS VARCHAR)\n", "            --     END) AS gmv_last_30d_to_curr_day\n", "            , gmv_last_30d_to_curr_day AS current_gmv\n", "            , (CASE \n", "                WHEN gmv_last_30d_to_curr_day = 0 THEN 'No Sale Activity'\n", "                WHEN gmv_contri_last_30d_to_curr_day <= 40 THEN 'First 40%% GMV Contribution'\n", "                WHEN gmv_contri_last_30d_to_curr_day <= 80 THEN 'Next 40%%-80%% GMV Contribution'\n", "                WHEN gmv_contri_last_30d_to_curr_day <= 95 THEN 'Next 80%%-95%% GMV Contribution'\n", "                WHEN gmv_contri_last_30d_to_curr_day <= 99 THEN 'Next 95%%-99%% GMV Contribution'\n", "                WHEN gmv_contri_last_30d_to_curr_day <= 100 THEN 'Last 1%% GMV Contribution'\n", "            END ) AS current_gmv_bucket -- gmv_bucket_last_30d_to_curr_day\n", "            , gmv_last_30d_to_curr_day_rnk AS current_gmv_rnk\n", "            \n", "            -- , ( CASE\n", "            --         WHEN gmv_last_60d_to_30d >= 10000000 THEN '₹ '|| CAST(ROUND(CAST(gmv_last_60d_to_30d AS DECIMAL(18,1)) / 10000000, 1) AS VARCHAR) || ' Cr' \n", "            --         WHEN gmv_last_60d_to_30d >= 100000 THEN '₹ '|| CAST(ROUND(CAST(gmv_last_60d_to_30d AS DECIMAL(18,1)) / 100000, 1) AS VARCHAR) || ' L' \n", "            --         WHEN gmv_last_60d_to_30d >= 1000 THEN '₹ '|| CAST(ROUND(CAST(gmv_last_60d_to_30d AS DECIMAL(18,1)) / 1000, 1) AS VARCHAR) || ' K' \n", "            --         ELSE '₹ '|| CAST(gmv_last_60d_to_30d AS VARCHAR)\n", "            --     END) AS gmv_last_60d_to_30d\n", "            , gmv_last_60d_to_30d AS previous_gmv\n", "            , (CASE \n", "                WHEN gmv_last_60d_to_30d = 0 THEN 'No Sale Activity'\n", "                WHEN gmv_contri_last_60d_to_30d <= 40 THEN 'First 40%% GMV Contribution'\n", "                WHEN gmv_contri_last_60d_to_30d <= 80 THEN 'Next 40%%-80%% GMV Contribution'\n", "                WHEN gmv_contri_last_60d_to_30d <= 95 THEN 'Next 80%%-95%% GMV Contribution'\n", "                WHEN gmv_contri_last_60d_to_30d <= 99 THEN 'Next 95%%-99%% GMV Contribution'\n", "                WHEN gmv_contri_last_60d_to_30d <= 100 THEN 'Last 1%% GMV Contribution'\n", "            END ) AS previous_gmv_bucket -- gmv_bucket_last_60d_to_30d\n", "            , gmv_last_60d_to_30d_rnk AS previous_gmv_rnk\n", "\n", "FROM        city_l0_item_performance \n", "-- ORDER BY    gmv_contri_last_30d_to_curr_day ASC\n", "\n", "\"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "980d53bc-287a-4b28-b999-f55b2eeb7d59", "metadata": {}, "outputs": [], "source": ["city_item_sales_query[\"city_id\"].count()"]}, {"cell_type": "code", "execution_count": null, "id": "c95979aa-de6c-4352-b047-2dcb7e76b873", "metadata": {}, "outputs": [], "source": ["city_item_sales_query[\"insert_ds_ist\"] = pd.to_datetime(\n", "    city_item_sales_query[\"insert_ds_ist\"]\n", ").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "02b42004-691c-492d-b57e-bd07513efc6d", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \" insert_ds_ist\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \" city_id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \" city_name\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \" l0_category\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \" p_type\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \" item_id\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \" item_name\"},\n", "    {\"name\": \"current_gmv\", \"type\": \"real\", \"description\": \" current_gmv\"},\n", "    {\"name\": \"current_gmv_bucket\", \"type\": \"varchar\", \"description\": \" current_gmv_bucket\"},\n", "    {\"name\": \"current_gmv_rnk\", \"type\": \"real\", \"description\": \" current_gmv_rnk\"},\n", "    {\"name\": \"previous_gmv\", \"type\": \"real\", \"description\": \" previous_gmv\"},\n", "    {\"name\": \"previous_gmv_bucket\", \"type\": \"varchar\", \"description\": \" previous_gmv_bucket\"},\n", "    {\"name\": \"previous_gmv_rnk\", \"type\": \"real\", \"description\": \" previous_gmv_rnk\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "9ac08723-6fdc-4e7e-ace3-ec22ae99e715", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"city_item_sales_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"insert_ds_ist\", \"city_id\", \"city_name\", \"item_id\", \"item_name\"],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": 'date_',\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains gmv change in two different window period\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0a839266-98dd-45f2-b30c-c5d0b047c0fd", "metadata": {}, "outputs": [], "source": ["to_trino(city_item_sales_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "2292b2da-b76f-4b1a-8f49-241f1d91b92f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "toc-autonumbering": true, "toc-showmarkdowntxt": false}, "nbformat": 4, "nbformat_minor": 5}