{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "                break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "19eddc0e-2085-4b16-9165-62f8d129e835", "metadata": {}, "outputs": [], "source": ["city_l0_sales_query = read_sql_query(\n", "    f\"\"\"\n", "\n", "WITH universe_data_with_sales_flag AS (\n", "SELECT city_id, city_name, l0_category, p_type, item_id, item_name, 'Current Window' AS sales_window, current_gmv AS gmv\n", "FROM  supply_etls.city_item_sales_metrics\n", "WHERE current_gmv > 0\n", "\n", "UNION ALL\n", "\n", "SELECT city_id, city_name, l0_category, p_type, item_id, item_name, 'Previous Window' AS sales_window, previous_gmv AS gmv\n", "FROM  supply_etls.city_item_sales_metrics\n", "WHERE previous_gmv > 0 ),\n", "\n", "cum_gmv AS (\n", "SELECT  city_id, city_name, l0_category, p_type, item_id, item_name, sales_window, gmv,\n", "        SUM(gmv) OVER (PARTITION BY city_name, sales_window ORDER BY gmv DESC) AS city_item_cum_gmv,\n", "        SUM(gmv) OVER (PARTITION BY city_name, sales_window) AS city_item_total_gmv,\n", "        DENSE_RANK() OVER (PARTITION BY city_name, sales_window ORDER BY gmv DESC) AS city_item_gmv_rnk,\n", "\n", "        SUM(gmv) OVER (PARTITION BY city_name, l0_category, sales_window ORDER BY gmv DESC) AS city_l0_item_cum_gmv,\n", "        SUM(gmv) OVER (PARTITION BY city_name, l0_category, sales_window) AS city_l0_item_total_gmv,\n", "        DENSE_RANK() OVER (PARTITION BY city_name, l0_category, sales_window ORDER BY gmv DESC) AS city_l0_item_gmv_rnk\n", "\n", "FROM universe_data_with_sales_flag ),\n", "\n", "gmv_contribution AS (\n", "SELECT  *, ROUND(((CAST(city_l0_item_cum_gmv AS DOUBLE) * 100) / city_l0_item_total_gmv), 1) AS city_l0_item_cum_gmv_contri \n", "        , ROUND(((CAST(city_item_cum_gmv AS DOUBLE) * 100) / city_item_total_gmv), 1) AS city_item_cum_gmv_contri \n", "FROM    cum_gmv ),\n", "\n", "gmv_rule AS (\n", "-- city_l0_gmv_rule \n", "SELECT  city_id, city_name, l0_category, sales_window, 'city-l0-level' AS metric_grain,\n", "        (CASE \n", "            WHEN city_l0_item_cum_gmv_contri <= 40 THEN 'First 40%% GMV Contribution'\n", "            WHEN city_l0_item_cum_gmv_contri <= 80 THEN 'Next 40%%-80%% GMV Contribution'\n", "            WHEN city_l0_item_cum_gmv_contri <= 95 THEN 'Next 80%%-95%% GMV Contribution'\n", "            WHEN city_l0_item_cum_gmv_contri <= 99 THEN 'Next 95%%-99%% GMV Contribution'\n", "            WHEN city_l0_item_cum_gmv_contri <= 100 THEN 'Last 1%% GMV Contribution'\n", "        END) AS gmv_bucket,\n", "        SUM(gmv) AS gmv,\n", "        COUNT(DISTINCT item_id) AS articles_in_bucket\n", "\n", "FROM    gmv_contribution\n", "GROUP BY 1,2,3,4,5,6 \n", "\n", "UNION ALL\n", "\n", "-- city_gmv_rule \n", "SELECT  city_id, city_name, '-' AS l0_category, sales_window, 'city-level' AS metric_grain,\n", "        (CASE \n", "            WHEN city_item_cum_gmv_contri <= 40 THEN 'First 40%% GMV Contribution'\n", "            WHEN city_item_cum_gmv_contri <= 80 THEN 'Next 40%%-80%% GMV Contribution'\n", "            WHEN city_item_cum_gmv_contri <= 95 THEN 'Next 80%%-95%% GMV Contribution'\n", "            WHEN city_item_cum_gmv_contri <= 99 THEN 'Next 95%%-99%% GMV Contribution'\n", "            WHEN city_item_cum_gmv_contri <= 100 THEN 'Last 1%% GMV Contribution'\n", "        END) AS gmv_bucket,\n", "        SUM(gmv) AS gmv,\n", "        COUNT(DISTINCT item_id) AS articles_in_bucket\n", "\n", "FROM    gmv_contribution\n", "GROUP BY 1,2,3,4,5,6 ),\n", "\n", "metric_view AS (\n", "SELECT  city_id, city_name, l0_category, sales_window, gmv_bucket, metric_grain, gmv, articles_in_bucket\n", "        , ROUND(((CAST(articles_in_bucket AS DOUBLE) * 100) / \n", "            SUM(articles_in_bucket) OVER (PARTITION BY city_name, l0_category, sales_window)),1) AS article_contri\n", "FROM    gmv_rule )\n", "\n", "\n", "SELECT  CAST(current_date AS DATE) AS insert_ds_ist, city_id, city_name, l0_category, sales_window, gmv_bucket, metric_grain, gmv, articles_in_bucket, article_contri\n", "FROM    metric_view\n", "\n", "UNION ALL\n", "\n", "SELECT  CAST(current_date AS DATE) AS insert_ds_ist, city_id, city_name, l0_category, sales_window, 'OVERALL' AS gmv_bucket, metric_grain, SUM(gmv) AS gmv\n", "        , SUM(articles_in_bucket) AS articles_in_bucket\n", "        , ROUND(((CAST(SUM(articles_in_bucket) AS DOUBLE) * 100) / SUM(articles_in_bucket)),1) AS article_contri\n", "FROM    metric_view\n", "GROUP BY 1,2,3,4,5,6,7\n", "\n", "\"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fe13fbc4-15d3-453a-ae42-9933e91eda48", "metadata": {}, "outputs": [], "source": ["city_l0_sales_query[\"insert_ds_ist\"] = pd.to_datetime(city_l0_sales_query[\"insert_ds_ist\"]).dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "e399f958-0b5b-426c-b0a8-5f990b371af7", "metadata": {}, "outputs": [], "source": ["city_l0_sales_query[\"city_id\"].count()"]}, {"cell_type": "code", "execution_count": null, "id": "a5fdd3a4-af9a-4a5d-89cd-625e2f7941b8", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \" insert_ds_ist\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \" city_id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \" city_name\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \" l0_category\"},\n", "    {\"name\": \"sales_window\", \"type\": \"varchar\", \"description\": \" sales_window\"},\n", "    {\"name\": \"gmv_bucket\", \"type\": \"varchar\", \"description\": \" gmv_bucket\"},\n", "    {\"name\": \"metric_grain\", \"type\": \"varchar\", \"description\": \" metric_grain\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \" gmv\"},\n", "    {\"name\": \"articles_in_bucket\", \"type\": \"integer\", \"description\": \" articles_in_bucket\"},\n", "    {\"name\": \"article_contri\", \"type\": \"real\", \"description\": \" article_contri\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "39276ba8-23b4-4de8-af34-27430a40a163", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"city_l0_sales_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"city_id\",\n", "        \"city_name\",\n", "        \"l0_category\",\n", "        \"sales_window\",\n", "        \"gmv_bucket\",\n", "    ],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": 'date_',\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains gmv change in two different window period\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "93c99266-7e08-4695-9693-b1108bbd9bfd", "metadata": {}, "outputs": [], "source": ["to_trino(city_l0_sales_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "2ee68964-faf9-4e11-b1b5-cf47196cd4ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "toc-autonumbering": true, "toc-showmarkdowntxt": false}, "nbformat": 4, "nbformat_minor": 5}