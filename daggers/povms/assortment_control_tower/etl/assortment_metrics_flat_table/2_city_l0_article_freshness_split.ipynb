{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc2a3835-1bc6-43cf-b28c-fafe81e4038f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e717d3fd-582e-49e1-9235-1d7852b43887", "metadata": {"tags": []}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "57bdc0aa-5ff7-4153-a57b-6c5af2c836c8", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d41b0124-d898-4660-9eee-ea465f506abb", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "                break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "19eddc0e-2085-4b16-9165-62f8d129e835", "metadata": {}, "outputs": [], "source": ["city_l0_article_freshness_split = read_sql_query(\n", "    f\"\"\"\n", "\n", "WITH active_transfers AS (\n", "SELECT      facility_id, item_id, master_assortment_substate_id, created_at, updated_at\n", "FROM        rpc.product_facility_master_assortment a\n", "WHERE       a.active = 1 and a.lake_active_record and master_assortment_substate_id IN (1,3) ),\n", "\n", "transfer_tea_tagging AS (\n", "SELECT outlet_id, item_id, CAST(tag_value AS DOUBLE) AS be_outlet_id\n", "FROM  rpc.item_outlet_tag_mapping b\n", "WHERE b.active = 1 and b.lake_active_record AND b.tag_type_id = 8 and tag_value != '0' ),\n", "\n", "fe AS (\n", "SELECT pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "be AS (\n", "SELECT pfom.facility_id, pfom.outlet_id, pfom.outlet_name, pfom.city_id, c.name AS city_name, od.inv_outlet_id\n", "FROM        po.physical_facility_outlet_mapping pfom\n", "INNER JOIN  retail.console_outlet rco ON rco.id = pfom.outlet_id AND rco.business_type_id NOT IN (7)\n", "INNER JOIN  retail.console_location c\n", "            ON pfom.city_id = c.id\n", "INNER JOIN  supply_etls.outlet_details od\n", "            ON  od.hot_outlet_id = pfom.outlet_id and od.store_type = 'Packaged Goods' and od.active = 1 \n", "                    and od.ars_active = 1 and od.ars_check = 1 and od.grocery_active_count > 0\n", "WHERE pfom.ars_active = 1 and pfom.active = 1 and pfom.lake_active_record ),\n", "\n", "fe_inv_snapshot AS (\n", "SELECT  fe_hot_outlet_id, item_id, MAX(CASE WHEN fe_inv > 0 THEN 1 ELSE 0 END) AS fe_inv_flag\n", "FROM    supply_etls.inventory_replenishment_metrics\n", "WHERE   insert_ds_ist = current_date \n", "group by 1,2 ),\n", "\n", "city_item_article_status AS (\n", "SELECT      fe.city_id, fe.city_name, it.l0_category, it.p_type, it.item_id, MIN(date(i.created_at)) AS first_active_date\n", "            , DATE_DIFF('DAY', MIN(date(i.created_at)), CURRENT_DATE) AS no_of_days_item_active\n", "            , MAX(COALESCE(fe_inv.fe_inv_flag,0)) AS available_article\n", "\n", "FROM        transfer_tea_tagging t \n", "INNER JOIN  fe\n", "            ON fe.outlet_id = t.outlet_id -- AND t.outlet_id = fs.outlet_id AND t.item_id = ip.item_id \n", "INNER JOIN  be\n", "            ON be.outlet_id = t.be_outlet_id \n", "INNER JOIN  active_transfers i\n", "            ON fe.facility_id = i.facility_id AND i.item_id = t.item_id\n", "INNER JOIN  supply_etls.item_details it\n", "            ON  it.assortment_type = 'Packaged Goods' AND it.item_id = t.item_id \n", "                AND it.l0_category NOT IN ('No Mapping', 'Specials', 'Ice Creams & Frozen Desserts', 'HP', 'Trial new tree', 'Chicken, Meat & Fish')\n", "LEFT JOIN   fe_inv_snapshot fe_inv\n", "            ON fe_inv.fe_hot_outlet_id = t.outlet_id AND fe_inv.item_id = t.item_id and 1=1\n", "GROUP BY    1,2,3,4,5 ),\n", "\n", "universe_active_status AS (\n", "SELECT  city_id, city_name, l0_category, p_type, item_id, no_of_days_item_active, available_article\n", "FROM    city_item_article_status\n", "\n", "UNION ALL\n", "\n", "SELECT      0 AS city_id, 'Pan India' AS city_name, l0_category, p_type, item_id\n", "            , DATE_DIFF('DAY', MIN(first_active_date), CURRENT_DATE) AS no_of_days_item_active\n", "            , COALESCE(MAX(available_article),0) AS available_article\n", "FROM        city_item_article_status\n", "GROUP BY    1,2,3,4,5 ),\n", "\n", "article_status AS (\n", "SELECT      city_id, city_name, l0_category, 'Active Article' AS article_status_type\n", "            , ( CASE \n", "                    WHEN no_of_days_item_active <= 90 THEN 'FRESH'\n", "                    WHEN no_of_days_item_active > 90 THEN 'MATURE'\n", "                END) AS article_age_category\n", "            , COUNT(DISTINCT item_id) AS article_cnt\n", "\n", "FROM        universe_active_status\n", "GROUP BY    1,2,3,4,5 \n", "\n", "UNION ALL\n", "\n", "SELECT      city_id, city_name, l0_category, 'Active & Available Article' AS article_status_type\n", "            , ( CASE \n", "                    WHEN no_of_days_item_active <= 90 THEN 'FRESH'\n", "                    WHEN no_of_days_item_active > 90 THEN 'MATURE'\n", "                END) AS article_age_category\n", "            , COUNT(DISTINCT item_id) AS article_cnt\n", "\n", "FROM        universe_active_status\n", "WHERE       available_article > 0\n", "GROUP BY    1,2,3,4,5 )\n", "\n", "SELECT  CURRENT_DATE AS insert_ds_ist, city_id, city_name, article_status_type, l0_category, article_age_category, article_cnt\n", "        , ROUND(((CAST(article_cnt AS DOUBLE) * 100) / SUM(article_cnt) OVER (PARTITION BY city_name, article_status_type, l0_category)),1) AS article_split\n", "FROM    article_status\n", "\n", "\"\"\",\n", "    CON_TRINO,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "82176e97-0089-4227-ac5a-29c54fe74cb2", "metadata": {}, "outputs": [], "source": ["city_l0_article_freshness_split[\"insert_ds_ist\"] = pd.to_datetime(\n", "    city_l0_article_freshness_split[\"insert_ds_ist\"]\n", ").dt.date"]}, {"cell_type": "code", "execution_count": null, "id": "e399f958-0b5b-426c-b0a8-5f990b371af7", "metadata": {}, "outputs": [], "source": ["city_l0_article_freshness_split[\"city_id\"].count()"]}, {"cell_type": "code", "execution_count": null, "id": "a5fdd3a4-af9a-4a5d-89cd-625e2f7941b8", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \" insert_ds_ist\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \" city_id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \" city_name\"},\n", "    {\"name\": \"article_status_type\", \"type\": \"varchar\", \"description\": \" article_status_type\"},\n", "    {\"name\": \"article_age_category\", \"type\": \"varchar\", \"description\": \" article_age_category\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \" l0_category\"},\n", "    {\"name\": \"article_cnt\", \"type\": \"integer\", \"description\": \" article_cnt\"},\n", "    {\"name\": \"article_split\", \"type\": \"real\", \"description\": \" article_split\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "39276ba8-23b4-4de8-af34-27430a40a163", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"city_l0_article_freshness_split\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"city_id\",\n", "        \"l0_category\",\n", "        \"article_status_type\",\n", "        \"article_age_category\",\n", "    ],\n", "    # \"partition_key\": [\"date_\"],\n", "    # \"incremental_key\": 'date_',\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"table shows the split between new and old articles within each category\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "93c99266-7e08-4695-9693-b1108bbd9bfd", "metadata": {}, "outputs": [], "source": ["to_trino(\n", "    city_l0_article_freshness_split, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2ee68964-faf9-4e11-b1b5-cf47196cd4ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "toc-autonumbering": true, "toc-showmarkdowntxt": false}, "nbformat": 4, "nbformat_minor": 5}