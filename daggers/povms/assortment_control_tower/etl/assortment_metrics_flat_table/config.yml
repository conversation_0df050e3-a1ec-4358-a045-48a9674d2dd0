alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: assortment_metrics_flat_table
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: medium
    node_type: spot
  name: 1_city_item_sales_metrics
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 2_city_l0_article_freshness_split
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 3_be_temp_inactive_articles_metrics
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 4_be_inactive_articles_metrics
  parameters: null
  retries: 2
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: 5_city_l0_sales_metrics
  parameters: null
  retries: 2
  tag: second
owner:
  email: <EMAIL>
  slack_id: U06LLMJHFCK
path: povms/assortment_control_tower/etl/assortment_metrics_flat_table
paused: false
pool: povms_pool
project_name: assortment_control_tower
schedule:
  end_date: '2025-09-16T00:00:00'
  interval: 15 4,11 * * *
  start_date: '2025-06-18T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 2
