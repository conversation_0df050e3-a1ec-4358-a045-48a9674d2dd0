{"cells": [{"cell_type": "code", "execution_count": null, "id": "b9e948ff-96e9-4483-b74a-6c05edf6d3c6", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pencilbox as pb\n", "import os\n", "import json\n", "\n", "connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "cd1ba8f1-a057-4597-9aaf-f1330d09e807", "metadata": {"tags": []}, "outputs": [], "source": ["get_partitions = f\"\"\"SELECT DISTINCT facility_id FROM ars_etls.daily_orders_and_availability_search_exp_jaipur\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "0f3bdaea-8cbe-4b5e-990b-2837a01838b6", "metadata": {}, "outputs": [], "source": ["res = pd.read_sql_query(sql=get_partitions, con=connection)"]}, {"cell_type": "code", "execution_count": null, "id": "e3089773-ef4d-4356-b851-796bb274400f", "metadata": {}, "outputs": [], "source": ["partition_chunks = [res[i : i + 200] for i in range(0, len(res), 200)]"]}, {"cell_type": "code", "execution_count": null, "id": "597b9ca6-e385-46b4-920a-e28d6100804f", "metadata": {}, "outputs": [], "source": ["chunk_count = len(partition_chunks)"]}, {"cell_type": "code", "execution_count": null, "id": "3a79ea9c-4187-4d0f-bed2-3cfddb942d37", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"ars_etls\",\n", "    \"table_name\": \"daily_orders_and_availability_search\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"item_id\", \"type\": \"INTEGER\", \"description\": \"Item Id\"},\n", "        {\"name\": \"facility_id\", \"type\": \"INTEGER\", \"description\": \"Facility Id\"},\n", "        {\"name\": \"order_date\", \"type\": \"DATE\", \"description\": \"order_date\"},\n", "        {\"name\": \"orders\", \"type\": \"INTEGER\", \"description\": \"orders\"},\n", "        {\"name\": \"order_quantity\", \"type\": \"INTEGER\", \"description\": \"order_quantity\"},\n", "        {\"name\": \"available_hours\", \"type\": \"DOUBLE\", \"description\": \"available_hours\"},\n", "        {\n", "            \"name\": \"potential_order_quantity\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"potential_order_quantity\",\n", "        },\n", "        {\n", "            \"name\": \"availability_factor\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"availability_factor\",\n", "        },\n", "        {\"name\": \"run_id\", \"type\": \"VARCHAR\", \"description\": \"run_id\"},\n", "        {\"name\": \"created_at\", \"type\": \"TIMESTAMP(6)\", \"description\": \"created_at\"},\n", "    ],\n", "    \"primary_key\": [\"facility_id\", \"item_id\", \"order_date\"],\n", "    \"partition_key\": [\"facility_id\"],\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Updates Daily Orders and availability.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "318db864-0090-402a-974f-e77d27756e10", "metadata": {}, "outputs": [], "source": ["for i in range(0, chunk_count):\n", "    offset_value = i * (200)\n", "    sql = f\"\"\"SELECT * FROM ars_etls.daily_orders_and_availability_search_exp_jaipur\n", "       WHERE (facility_id) IN\n", "       (\n", "         SELECT DISTINCT facility_id FROM ars_etls.daily_orders_and_availability_search_exp_jaipur\n", "          ORDER BY facility_id OFFSET {offset_value} LIMIT 200\n", "       )\"\"\"\n", "    pb.to_trino(data_obj=sql, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "3f864537-63bb-4f4c-9899-78e6229e4040", "metadata": {"tags": ["parameter"]}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}