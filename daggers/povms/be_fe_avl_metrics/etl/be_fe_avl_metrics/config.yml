alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: be_fe_avl_metrics
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_fe_run_lvl_avl_metrics
  parameters: null
  tag: parallel
- executor_config:
    load_type: tiny
    node_type: spot
  name: be_sales_trasfer_metrics
  parameters: null
  tag: parallel
- executor_config:
    load_type: tiny
    node_type: spot
  name: store_run_details
  parameters: null
  tag: parallel
owner:
  email: <EMAIL>
  slack_id: U07F2C300H5
path: povms/be_fe_avl_metrics/etl/be_fe_avl_metrics
paused: false
pool: povms_pool
project_name: be_fe_avl_metrics
schedule:
  end_date: '2025-09-01T00:00:00'
  interval: 55 */1 * * *
  start_date: '2025-02-08T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 5
