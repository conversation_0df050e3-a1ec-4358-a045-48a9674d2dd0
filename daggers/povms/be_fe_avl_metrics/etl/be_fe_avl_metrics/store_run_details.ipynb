{"cells": [{"cell_type": "code", "execution_count": null, "id": "a02acf7e-a0e0-4d72-b955-0dca5d7620a1", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, timedelta\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "2081de8b-77f0-4616-b79b-7eb9899b877b", "metadata": {}, "outputs": [], "source": ["# Trino Connection\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e1abba2a-6caa-4c06-a643-f59e394e694e", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", round((end - start) / 60, 2), \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "1f89180d-200f-49e4-86bc-a4cce60209d3", "metadata": {}, "outputs": [], "source": ["# DataFrame or SQL to Trino\n", "def to_trino(data_obj_to_trino, **kwargs):\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "    print(\"<PERSON><PERSON> Connected Successfully!!!\")\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_trino(data_obj_to_trino, **kwargs)\n", "            end_time = time.time()\n", "            # Runtime\n", "            duration = end_time - start_time\n", "\n", "            print(f\"Data pushed in table in {attempt + 1} attempt with runtime of {duration}\")\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            if attempt == max_tries - 1:\n", "                raise Exception(f\"Operation failed after {max_tries} retries: {e}\")\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "40670150-313e-4fac-927d-ca8227098287", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "WITH runs AS\n", "(   SELECT run_id, case when simulation_params like '%%item_id_list%%' then 'Item level' else 'Normal' end run_type\n", "    FROM ars.job_run\n", "    where date(started_at+interval'5'hour+interval'30'minute) >= current_date-interval'10'day\n", "    AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "    AND simulation_params not like '%%mode%%'\n", "    AND simulation_params not like '%%hyperpure_run%%'\n", "    AND simulation_params not like '%%any_day_po%%'    \n", "    AND simulation_params not like '%%spr_migration_simulation%%' \n", "    order by 1),\n", "     \n", "base AS\n", "  (SELECT torv.sto_date, \n", "          torv.run_id,\n", "          run_type,\n", "          torv.backend_outlet_id,\n", "          torv.frontend_outlet_id,\n", "          min(extract(hour from created_at+interval'5'hour+interval'30'minute)) as hour,\n", "          sum(sto_quantity) as sto_quantity,\n", "          sum(item_details.weight_in_gm * sto_quantity/1000.00) as v1_weight,\n", "          sum(case when sto_quantity>0 then 1 end) as v1_line_items,\n", "          sum(normalised_score) as cpd,\n", "          sum(case when sto_quantity_post_truncation_in_case>0 then 1 end) as v2_line_items,\n", "          sum(item_details.weight_in_gm * sto_quantity_post_truncation_in_case/1000.00) as v2_weight,\n", "          sum(sto_quantity_post_truncation_in_case) as sto_quantity_post_truncation_in_case,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.inward_drop') AS INTEGER)) AS inward_drop,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER)) AS storage_drop,\n", "          sum(case when storage_type = 'Fast_Moving' then TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER) end) AS fast_storage_drop,\n", "          sum(case when storage_type = 'Freezer' then TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER) end) AS freezer_storage_drop,\n", "          sum(case when storage_type = 'Fridge' then TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER) end) AS cold_storage_drop,\n", "          sum(case when storage_type = 'Heavy' then TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER) end) AS heavy_storage_drop,\n", "          sum(case when storage_type = 'Regular' then TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER) end) AS regular_storage_drop,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.truck_load_drop') AS INTEGER)) AS truck_load_drop,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_quantity_drop') AS INTEGER)) AS picking_capacity_quantity_drop,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_sku_drop') AS INTEGER)) AS picking_capacity_sku_drop\n", "   FROM ars.transfers_optimization_results_v2 torv\n", "   inner join runs on runs.run_id = torv.run_id\n", "    LEFT JOIN\n", "    ars.item_details AS item_details \n", "    ON item_details.run_id = torv.run_id \n", "    AND item_details.item_id = torv.item_id \n", "    AND item_details.run_id in (select run_id from runs)\n", "   WHERE torv.run_id in (select run_id from runs) \n", "     AND insert_ds_ist >= CAST(current_date-interval '10' day AS VARCHAR)\n", "     AND torv.lake_active_record = TRUE\n", "    group by 1,2,3,4,5),\n", "    \n", "ffv as (\n", "select ffv.run_id, frontend_outlet_id, vehicle_run_type AS  vehicle_type,\n", "max(dispatch_time) dispatch_time, sum(capacity_in_kg) truck_capacity\n", "    from ars.frontend_facility_vehicle_constraints ffv\n", "    inner join runs on runs.run_id = ffv.run_id\n", "    where insert_ds_ist >= CAST(current_date-interval '10' day AS VARCHAR)\n", "    and is_current_slot = true\n", "    group by 1,2,3)\n", "\n", "select pfom_be.outlet_name as warehouse, cast(sto_date as DATE) AS sto_date, \n", "ffc.shift_key, run_type , base.run_id, CAST(hour AS INTEGER) AS hour\n", ", CAST(base.frontend_outlet_id AS INTEGER) as frontend_outlet_id ,\n", "pfom.outlet_name,\n", "CAST(inward_capacity_percent AS DOUBLE) AS inward_capacity_percent , \n", "CAST(disruption_percentage AS DOUBLE) AS disruption_percentage, \n", "CAST(ffc.inward_capacity AS INTEGER) AS inward_capacity,\n", "CAST(dispatch_time AS VARCHAR) AS dispatch_time, \n", "CAST(truck_capacity AS INTEGER) as truck_capacity, \n", "vehicle_type,\n", "CAST(sum(sto_quantity) AS INTEGER) AS v1,\n", "CAST(sum(v1_weight) AS DOUBLE) AS v1_weight,\n", "CAST(sum(v1_line_items) AS INTEGER) AS v1_line_items,\n", "CAST(sum(sto_quantity_post_truncation_in_case) AS INTEGER) AS v2,\n", "CAST(sum(v2_line_items) AS INTEGER) AS v2_line_items,\n", "CAST(sum(v2_weight) AS DOUBLE) AS v2_weight,\n", "CAST(sum(v2_weight) * 100.00 / sum(truck_capacity) AS DOUBLE) AS Vehicle_util_Perc,\n", "CAST(sum(sto_quantity_post_truncation_in_case) * 1.00 / sum(v2_line_items) AS DOUBLE) AS qpl,\n", "CAST(sum(sto_quantity_post_truncation_in_case) / sum(v2_weight) AS DOUBLE) AS v2_qty_by_v2_weight,\n", "CAST(sum(inward_drop) AS INTEGER) AS inward_drop,\n", "CAST(sum(storage_drop) AS INTEGER) AS storage_drop,\n", "CAST(sum(fast_storage_drop) AS INTEGER) AS fast_storage_drop,\n", "CAST(sum(freezer_storage_drop) AS INTEGER) AS freezer_storage_drop,\n", "CAST(sum(cold_storage_drop) AS INTEGER) AS cold_storage_drop,\n", "CAST(sum(heavy_storage_drop) AS INTEGER) AS heavy_storage_drop,\n", "CAST(sum(regular_storage_drop) AS INTEGER) AS regular_storage_drop,\n", "CAST(sum(truck_load_drop) AS INTEGER) AS truck_load_drop,\n", "CAST(sum(picking_capacity_quantity_drop) AS INTEGER) AS picking_capacity_quantity_drop,\n", "CAST(sum(picking_capacity_sku_drop) AS INTEGER) AS picking_capacity_sku_drop,\n", "current_timestamp AS updated_at,\n", "current_date AS date_ist\n", "\n", "from base\n", "inner join ars.frontend_facility_constraints ffc on ffc.frontend_outlet_id = base.frontend_outlet_id and ffc.run_id = base.run_id \n", "            and ffc.insert_ds_ist >= CAST(current_date-interval '10' day AS VARCHAR) and is_current_slot = true\n", "left join ffv on ffv.run_id = base.run_id and ffv.frontend_outlet_id = base.frontend_outlet_id\n", "left join po.physical_facility_outlet_mapping pfom on pfom.outlet_id = base.frontend_outlet_id\n", "left join po.physical_facility_outlet_mapping pfom_be on pfom_be.outlet_id = base.backend_outlet_id\n", "group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14\n", "order by 5 desc\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "id": "83be40cc-2e7e-461e-8b99-c22c323efe5a", "metadata": {}, "source": ["df = read_sql_query(sql, trino)"]}, {"cell_type": "markdown", "id": "98d426b8-d05e-48e7-a220-eaa7e8f71b0b", "metadata": {}, "source": ["df.dtypes"]}, {"cell_type": "markdown", "id": "def8f8bc-1756-4ae0-8b09-e46832bf7d53", "metadata": {}, "source": ["df[\"v2_qty_by_v2_weight\"] = df[\"v2_qty_by_v2_weight\"].astype(\"float\")"]}, {"cell_type": "code", "execution_count": null, "id": "87d2b9ee-f2a8-44a7-a7a5-f1e63935e703", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"store_run_indent_details_v2\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"warehouse\", \"type\": \"VARCHAR\", \"description\": \"warehouse\"},\n", "        {\"name\": \"sto_date\", \"type\": \"DATE\", \"description\": \"sto_date\"},\n", "        {\"name\": \"shift_key\", \"type\": \"VARCHAR\", \"description\": \"shift_key\"},\n", "        {\"name\": \"run_type\", \"type\": \"VARCHAR\", \"description\": \"run_type\"},\n", "        {\"name\": \"run_id\", \"type\": \"VARCHAR\", \"description\": \"run_id\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"hour\"},\n", "        {\"name\": \"frontend_outlet_id\", \"type\": \"INTEGER\", \"description\": \"frontend_outlet_id\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"VARCHAR\", \"description\": \"outlet_name\"},\n", "        {\n", "            \"name\": \"inward_capacity_percent\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"inward_capacity_percent\",\n", "        },\n", "        {\"name\": \"disruption_percentage\", \"type\": \"DOUBLE\", \"description\": \"disruption_percentage\"},\n", "        {\"name\": \"inward_capacity\", \"type\": \"INTEGER\", \"description\": \"inward_capacity\"},\n", "        {\"name\": \"dispatch_time\", \"type\": \"VARCHAR\", \"description\": \"dispatch_time\"},\n", "        {\"name\": \"truck_capacity\", \"type\": \"INTEGER\", \"description\": \"truck_capacity\"},\n", "        {\"name\": \"vehicle_type\", \"type\": \"VARCHAR\", \"description\": \"vehicle_type\"},\n", "        {\"name\": \"v1\", \"type\": \"INTEGER\", \"description\": \"v1\"},\n", "        {\"name\": \"v1_weight\", \"type\": \"DOUBLE\", \"description\": \"v1_weight\"},\n", "        {\"name\": \"v1_line_items\", \"type\": \"INTEGER\", \"description\": \"v1_line_items\"},\n", "        {\"name\": \"v2\", \"type\": \"INTEGER\", \"description\": \"v2\"},\n", "        {\"name\": \"v2_line_items\", \"type\": \"INTEGER\", \"description\": \"v2_line_items\"},\n", "        {\"name\": \"v2_weight\", \"type\": \"DOUBLE\", \"description\": \"v2_weight\"},\n", "        {\"name\": \"Vehicle_util_Perc\", \"type\": \"DOUBLE\", \"description\": \"Vehicle_util_Perc\"},\n", "        {\"name\": \"qpl\", \"type\": \"DOUBLE\", \"description\": \"qpl\"},\n", "        {\"name\": \"v2_qty_by_v2_weight\", \"type\": \"DOUBLE\", \"description\": \"V2_QTY_by_V2_WEIGHT\"},\n", "        {\"name\": \"inward_drop\", \"type\": \"INTEGER\", \"description\": \"inward_drop\"},\n", "        {\"name\": \"storage_drop\", \"type\": \"INTEGER\", \"description\": \"storage_drop\"},\n", "        {\"name\": \"fast_storage_drop\", \"type\": \"INTEGER\", \"description\": \"fast_storage_drop\"},\n", "        {\"name\": \"freezer_storage_drop\", \"type\": \"INTEGER\", \"description\": \"freezer_storage_drop\"},\n", "        {\"name\": \"cold_storage_drop\", \"type\": \"INTEGER\", \"description\": \"cold_storage_drop\"},\n", "        {\"name\": \"heavy_storage_drop\", \"type\": \"INTEGER\", \"description\": \"heavy_storage_drop\"},\n", "        {\"name\": \"regular_storage_drop\", \"type\": \"INTEGER\", \"description\": \"regular_storage_drop\"},\n", "        {\"name\": \"truck_load_drop\", \"type\": \"INTEGER\", \"description\": \"truck_load_drop\"},\n", "        {\n", "            \"name\": \"picking_capacity_quantity_drop\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"picking_capacity_quantity_drop\",\n", "        },\n", "        {\n", "            \"name\": \"picking_capacity_sku_drop\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"picking_capacity_sku_drop\",\n", "        },\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(3)\", \"description\": \"Last updated timestamp\"},\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date_ist\"},\n", "    ],\n", "    \"primary_key\": [\"run_id\", \"frontend_outlet_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"store run level indents\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "8ad3c285-714c-459a-be68-98ef31972e5a", "metadata": {}, "outputs": [], "source": ["to_trino(sql, **kwargs)"]}, {"cell_type": "markdown", "id": "b10b7cba-6808-4fe9-8377-3d82c9d431d5", "metadata": {}, "source": ["df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "14c210f4-e501-47a8-bad0-4c87629cc0f2", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"store_run_indent_details_logs\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"warehouse\", \"type\": \"VARCHAR\", \"description\": \"warehouse\"},\n", "        {\"name\": \"sto_date\", \"type\": \"DATE\", \"description\": \"sto_date\"},\n", "        {\"name\": \"shift_key\", \"type\": \"VARCHAR\", \"description\": \"shift_key\"},\n", "        {\"name\": \"run_type\", \"type\": \"VARCHAR\", \"description\": \"run_type\"},\n", "        {\"name\": \"run_id\", \"type\": \"VARCHAR\", \"description\": \"run_id\"},\n", "        {\"name\": \"hour\", \"type\": \"INTEGER\", \"description\": \"hour\"},\n", "        {\"name\": \"frontend_outlet_id\", \"type\": \"INTEGER\", \"description\": \"frontend_outlet_id\"},\n", "        {\"name\": \"outlet_name\", \"type\": \"VARCHAR\", \"description\": \"outlet_name\"},\n", "        {\n", "            \"name\": \"inward_capacity_percent\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"inward_capacity_percent\",\n", "        },\n", "        {\"name\": \"disruption_percentage\", \"type\": \"DOUBLE\", \"description\": \"disruption_percentage\"},\n", "        {\"name\": \"inward_capacity\", \"type\": \"INTEGER\", \"description\": \"inward_capacity\"},\n", "        {\"name\": \"dispatch_time\", \"type\": \"VARCHAR\", \"description\": \"dispatch_time\"},\n", "        {\"name\": \"truck_capacity\", \"type\": \"INTEGER\", \"description\": \"truck_capacity\"},\n", "        {\"name\": \"vehicle_type\", \"type\": \"VARCHAR\", \"description\": \"vehicle_type\"},\n", "        {\"name\": \"v1\", \"type\": \"INTEGER\", \"description\": \"v1\"},\n", "        {\"name\": \"v1_weight\", \"type\": \"DOUBLE\", \"description\": \"v1_weight\"},\n", "        {\"name\": \"v1_line_items\", \"type\": \"INTEGER\", \"description\": \"v1_line_items\"},\n", "        {\"name\": \"v2\", \"type\": \"INTEGER\", \"description\": \"v2\"},\n", "        {\"name\": \"v2_line_items\", \"type\": \"INTEGER\", \"description\": \"v2_line_items\"},\n", "        {\"name\": \"v2_weight\", \"type\": \"DOUBLE\", \"description\": \"v2_weight\"},\n", "        {\"name\": \"Vehicle_util_Perc\", \"type\": \"DOUBLE\", \"description\": \"Vehicle_util_Perc\"},\n", "        {\"name\": \"qpl\", \"type\": \"DOUBLE\", \"description\": \"qpl\"},\n", "        {\"name\": \"v2_qty_by_v2_weight\", \"type\": \"DOUBLE\", \"description\": \"V2_QTY_by_V2_WEIGHT\"},\n", "        {\"name\": \"inward_drop\", \"type\": \"INTEGER\", \"description\": \"inward_drop\"},\n", "        {\"name\": \"storage_drop\", \"type\": \"INTEGER\", \"description\": \"storage_drop\"},\n", "        {\"name\": \"fast_storage_drop\", \"type\": \"INTEGER\", \"description\": \"fast_storage_drop\"},\n", "        {\"name\": \"freezer_storage_drop\", \"type\": \"INTEGER\", \"description\": \"freezer_storage_drop\"},\n", "        {\"name\": \"cold_storage_drop\", \"type\": \"INTEGER\", \"description\": \"cold_storage_drop\"},\n", "        {\"name\": \"heavy_storage_drop\", \"type\": \"INTEGER\", \"description\": \"heavy_storage_drop\"},\n", "        {\"name\": \"regular_storage_drop\", \"type\": \"INTEGER\", \"description\": \"regular_storage_drop\"},\n", "        {\"name\": \"truck_load_drop\", \"type\": \"INTEGER\", \"description\": \"truck_load_drop\"},\n", "        {\n", "            \"name\": \"picking_capacity_quantity_drop\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"picking_capacity_quantity_drop\",\n", "        },\n", "        {\n", "            \"name\": \"picking_capacity_sku_drop\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"picking_capacity_sku_drop\",\n", "        },\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(3)\", \"description\": \"Last updated timestamp\"},\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date_ist\"},\n", "    ],\n", "    \"primary_key\": [\"run_id\", \"frontend_outlet_id\"],\n", "    \"partition_key\": [\"sto_date\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"store run level indents\",\n", "}"]}, {"cell_type": "markdown", "id": "ed7d389d-8556-4881-8e1a-4cedac767f1e", "metadata": {}, "source": ["to_trino(sql, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "6746f996-4fcb-4e20-bb0a-5448d7e560d5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}