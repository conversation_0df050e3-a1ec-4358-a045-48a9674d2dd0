{"cells": [{"cell_type": "code", "execution_count": null, "id": "e92b7f67-3b6d-42ec-86b4-88a3485a5b4e", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, timedelta\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "13d66319-d477-4edd-ba7b-bf498ecb426c", "metadata": {}, "outputs": [], "source": ["# Trino Connection\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "08ff86f3-05e1-41af-8ce3-80fc79cefc81", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", round((end - start) / 60, 2), \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "598c0b11-a12c-43ee-9a50-2da1774861bb", "metadata": {}, "outputs": [], "source": ["# DataFrame or SQL to Trino\n", "def to_trino(data_obj_to_trino, **kwargs):\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "    print(\"<PERSON><PERSON> Connected Successfully!!!\")\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_trino(data_obj_to_trino, **kwargs)\n", "            end_time = time.time()\n", "            # Runtime\n", "            duration = end_time - start_time\n", "\n", "            print(f\"Data pushed in table in {attempt + 1} attempt with runtime of {duration}\")\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            if attempt == max_tries - 1:\n", "                raise Exception(f\"Operation failed after {max_tries} retries: {e}\")\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "0cd783d4-22e8-4561-a792-557c2896d065", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with \n", "active_outlets as (\n", "    select hot_outlet_id as outlet_id, facility_id\n", "    from supply_etls.outlet_details\n", "    where ars_check = 1\n", "    and business_type_id = 7\n", "),\n", "\n", "\n", "pg_ass as (\n", "    select item_id from supply_etls.item_details where assortment_type = 'Packaged Goods'\n", "),\n", "    \n", "    -- Assortment Details of only packaged goods and active outlets\n", "pfma_ass as (\n", "    select pfom.facility_id, pfom.outlet_id, pcma.item_id\n", "    from \n", "    (select item_id,facility_id from rpc.product_facility_master_assortment \n", "    where master_assortment_substate_id in (1) and active = 1) pcma \n", "        inner join\n", "    pg_ass on pg_ass.item_id = pcma.item_id\n", "        inner join \n", "    (select * from po.physical_facility_outlet_mapping where active = 1) pfom on pcma.facility_id = pfom.facility_id\n", "        inner join\n", "    active_outlets ao on ao.outlet_id = pfom.outlet_id\n", "   \n", "),\n", "cpd_details as \n", "(\n", "select outlet_id, item_id, max_by(aps_adjusted,updated_at) as aps\n", "from ars.outlet_item_aps_derived_cpd where lake_active_record and insert_ds_ist >= cast(current_date as varchar)\n", "group by 1,2\n", "),\n", "\n", "be_fe_item as\n", "(select iotm.outlet_id,iotm.item_id,cast(iotm.tag_value as integer) as be_outlet_id,pa.facility_id,pfom.facility_id as be_facility_id,\n", "    cd.aps\n", "    \n", "    from \n", "    (select item_id,tag_value,outlet_id\n", "    from rpc.item_outlet_tag_mapping where tag_type_id = 8 and active = 1 and lake_active_record = True) iotm \n", "        inner join \n", "    pfma_ass pa on pa.outlet_id = iotm.outlet_id and pa.item_id = iotm.item_id\n", "        inner join\n", "    (select * from po.physical_facility_outlet_mapping where active = 1) pfom on  cast(iotm.tag_value as integer) = pfom.outlet_id\n", "        left join\n", "    cpd_details cd on cd.outlet_id = iotm.outlet_id and cd.item_id = iotm.item_id\n", "),\n", "pid_item_id as (\n", "select dipom.product_id, dipom.item_id, bfi.outlet_id, bfi.be_outlet_id,dipom.multiplier, bfi.aps from \n", "(select product_id, item_id, multiplier from \n", "dwh.dim_item_product_offer_mapping where is_current\n", "and valid_to_ts_ist >= current_date\n", "group by 1,2,3) dipom\n", "inner join\n", "be_fe_item bfi on bfi.item_id = dipom.item_id\n", "),\n", "sto_details as (\n", "select sto_id, outlet_id, merchant_outlet_id as front_end_outlet_id, sto_state, date(created_at + interval '330' minute) as created_at_ist\n", "from ims.ims_sto_details where \n", "    -- case \n", "    -- when created_at + interval '330' minute >= current_date - interval '2' day then sto_state in (1,2,4,5)\n", "    -- else sto_state in (4,5) end\n", "   created_at >= current_date - interval '5' day \n", "    and lake_active_record\n", "order by created_at \n", "\n", "),\n", "\n", "sto_item_level as (\n", "select isi.item_id, sd.outlet_id as be_outlet_id, sd.front_end_outlet_id as outlet_id, sd.created_at_ist as date_ist,\n", "od.hot_outlet_id as be_hot_outlet_id,\n", "sum(billed_quantity) as total_qty_ob\n", "from ims.ims_sto_item isi inner join sto_details sd on sd.sto_id =  isi.sto_id and \n", "isi.lake_active_record and isi.created_at >= current_date - interval '5' day \n", "inner join active_outlets ao on ao.outlet_id = sd.front_end_outlet_id\n", "inner join be_fe_item bfi on bfi.item_id = isi.item_id and bfi.outlet_id = sd.front_end_outlet_id\n", "inner join \n", "(\n", "select hot_outlet_id, inv_outlet_id from supply_etls.outlet_details where \n", "ars_check = 1\n", "    and business_type_id <> 7\n", ") od on od.inv_outlet_id = sd.outlet_id\n", "group by 1,2,3,4,5 \n", "),\n", "-- select * from sto_item_level order by date_ist desc limit 100\n", "sto_be_level as (\n", "select date_ist, be_hot_outlet_id, sum(total_qty_ob) as ob_qty,\n", "sum(case when total_qty_ob >0 then 1 else 0 end) as ob_line_items\n", "from sto_item_level \n", "group by 1,2\n", "),\n", "sale as (\n", "select day,pii.be_outlet_id, pii.outlet_id, pii.item_id, \n", "sum(sales.qty_sold*pii.multiplier) as final_qty_sold\n", "from\n", "(select date(order_create_dt_ist) as day,outlet_id, product_id, sum(procured_quantity) as qty_sold\n", "from dwh.fact_sales_order_item_details\n", "where order_create_dt_ist >= current_date - interval '5' day\n", "                and order_current_status = 'DELIVERED' \n", "                and order_type not in ('InternalForwardOrder', 'InternalReverseOrder', 'DropShippingInternalReverseOrder')\n", "                and not is_internal_order\n", "group by 1,2,3) sales \n", "inner join \n", "pid_item_id pii on pii.product_id = sales.product_id and sales.outlet_id = pii.outlet_id\n", "group by 1,2,3,4\n", "-- order by sales.qty_sold*pii.multiplier desc\n", "),\n", "sale_be_level as\n", "(\n", "select day, be_outlet_id, sum(final_qty_sold) as sales,\n", "sum(case when final_qty_sold >0 then 1 else 0 end) as sales_line_items\n", "from sale \n", "group by 1,2\n", "),\n", "runs as \n", "(\n", "select run_id, date(completed_at  + interval '330' minute) as time_stamp_ist,facility_id from ars.job_run\n", "where started_at >= current_date-interval '5' day\n", "        AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "    -- this gives all manual runs both trigger and item_level runs\n", "    -- AND simulation_params not like '%%manual%%'\n", "        -- AND simulation_params not like '%%item_id_list%%' (use this filter to remove all item level runs in manual runs)\n", "        AND simulation_params not like '%%mode%%'\n", "        AND simulation_params not like '%%hyperpure_run%%'\n", "        AND simulation_params not like '%%test%%'\n", "        AND simulation_params not like '%%any_day_po%%'    \n", "        AND simulation_params not like '%%spr_migration_simulation%%'\n", "-- day, be, sum(caps assigned for the run) and also use this runs for calculating v1 and v2\n", "),\n", "opti_results as \n", "(\n", "    select backend_outlet_id, \n", "    date(created_at + interval '330' minute) as day,\n", "    sum(sto_quantity) as v1, sum(sto_quantity_post_truncation_in_case) as v2,\n", "    sum(case when sto_quantity > 0 then 1 else 0  end) as v1_line_items,\n", "    sum(case when sto_quantity_post_truncation_in_case > 0 then 1 else 0 end) as v2_line_items\n", "    from ars.transfers_optimization_results_v2 torv\n", "    inner join runs on runs.run_id = torv.run_id\n", "    where insert_ds_ist >= cast(current_date-interval '5' day as varchar)\n", "    group by 1,2\n", "),\n", "\n", "flush_filter as \n", "(select run_id, from_outlet_id, to_outlet_id, item_id\n", "from ars.frontend_cycle_sto_quantity where\n", "json_extract_scalar(comments, '$.flush_inventory') = 'true' and lake_active_record\n", "and partition_field >= cast(current_date - interval '5' day as varchar)\n", "),\n", "\n", "flush_qty_v2 as \n", "(\n", "select torv.day, ff.from_outlet_id, \n", "sum(torv.sto_quantity_post_truncation_in_case) as v2_flush_qty, sum(ind.billed_quantity) as ob_flush_qty\n", "from flush_filter ff \n", "inner join runs on runs.run_id = ff.run_id\n", "inner join \n", "    (\n", "    select date(created_at) as day,backend_outlet_id, frontend_outlet_id, item_id, sto_quantity_post_truncation_in_case, run_id\n", "    from ars.transfers_optimization_results_v2 \n", "    where lake_active_record and insert_ds_ist >= cast(current_date-interval '5' day as varchar)\n", "    ) torv on torv.backend_outlet_id = ff.from_outlet_id and torv.frontend_outlet_id = ff.to_outlet_id\n", "    and torv.item_id = ff.item_id and torv.run_id = ff.run_id\n", "left join\n", "    (select si.run_id, si.item_id, isi.billed_quantity, od.hot_outlet_id, isd.merchant_outlet_id as frontend_outlet_id\n", "    from po.sto_items si inner join ims.ims_sto_item isi on isi.sto_id = si.sto_id and isi.item_id = si.item_id\n", "    inner join ims.ims_sto_details isd on isd.sto_id = si.sto_id\n", "    inner join (\n", "    select hot_outlet_id, inv_outlet_id from supply_etls.outlet_details where \n", "    ars_check = 1\n", "        and business_type_id <> 7\n", "    ) od on od.inv_outlet_id = isd.outlet_id ) ind on ind.run_id = torv.run_id and ind.item_id = torv.item_id and \n", "    ind.hot_outlet_id = torv.backend_outlet_id and ind.frontend_outlet_id = torv.frontend_outlet_id\n", "group by 1,2\n", "),\n", "\n", "be_caps as \n", "(\n", "    select pfom.outlet_id as be_hot_outlet_id, time_stamp_ist,\n", "    sum(picking_capacity_assigned) as pick_cap, sum(picking_sku_assigned) as pick_sku\n", "    from ars.backend_facility_transfer_constraints bftc inner join\n", "    runs  on runs.run_id = bftc.run_id and bftc.insert_ds_ist >= cast(current_date-interval '5' day as varchar) \n", "    inner join \n", "    (select * from po.physical_facility_outlet_mapping where active = 1) pfom on  runs.facility_id = pfom.facility_id\n", "    group by 1,2\n", ")\n", "select sbl.day,CAST(be_outlet_id AS INTEGER) AS be_outlet_id,\n", "od.hot_outlet_name,CAST(sales as INTEGER) as sales, \n", "CAST(ob_qty AS INTEGER ) as ob_qty,\n", "CAST(fqv.ob_flush_qty AS INTEGER) as ob_flush_qty, \n", "CAST(v1 AS INTEGER) as v1,CAST(v2 AS INTEGER) AS v2,\n", "CAST(fqv.v2_flush_qty AS INTEGER) as v2_flush_qty,\n", "CAST(pick_cap AS INTEGER) as pick_cap,\n", "CAST(sales_line_items AS INTEGER) AS sales_line_items,\n", "CAST(ob_line_items AS INTEGER) AS ob_line_items,\n", "CAST(v1_line_items AS INTEGER) AS v1_line_items,\n", "CAST(v2_line_items AS INTEGER) AS v2_line_items,\n", "CAST(pick_sku AS INTEGER) AS pick_sku,\n", "\n", "current_timestamp as updated_at, current_date as date_ist from sale_be_level sbl \n", "left join sto_be_level stbl on sbl.be_outlet_id = stbl.be_hot_outlet_id and sbl.day = stbl.date_ist\n", "left join opti_results ors on ors.backend_outlet_id = sbl.be_outlet_id and ors.day = sbl.day\n", "left join be_caps bc on bc.be_hot_outlet_id = sbl.be_outlet_id and bc.time_stamp_ist = sbl.day\n", "left join\n", "(\n", "select hot_outlet_id, inv_outlet_id, hot_outlet_name from supply_etls.outlet_details where \n", "ars_check = 1\n", "    and business_type_id <> 7\n", ") od on od.hot_outlet_id = sbl.be_outlet_id\n", "left join flush_qty_v2 fqv on sbl.be_outlet_id = fqv.from_outlet_id and fqv.day = sbl.day\n", "\"\"\""]}, {"cell_type": "markdown", "id": "d885aee9-3a3b-4f3e-a0a7-b329b8ba01e5", "metadata": {}, "source": ["df = read_sql_query(sql, trino)"]}, {"cell_type": "markdown", "id": "3824f97e-5fcd-4a1b-86c3-96741af0915a", "metadata": {}, "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "9548e5ab-605b-4f18-b7e4-10e9e8d5d665", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_sales_transfer_metrics\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"day\", \"type\": \"DATE\", \"description\": \"day\"},\n", "        {\"name\": \"be_outlet_id\", \"type\": \"INTEGER\", \"description\": \"be_outlet_id\"},\n", "        {\"name\": \"hot_outlet_name\", \"type\": \"VARCHAR\", \"description\": \"hot_outlet_name\"},\n", "        {\"name\": \"sales\", \"type\": \"INTEGER\", \"description\": \"sales\"},\n", "        {\"name\": \"ob_qty\", \"type\": \"INTEGER\", \"description\": \"ob_qty\"},\n", "        {\"name\": \"ob_flush_qty\", \"type\": \"INTEGER\", \"description\": \"ob_flush_qty\"},\n", "        {\"name\": \"v1\", \"type\": \"INTEGER\", \"description\": \"v1\"},\n", "        {\"name\": \"v2\", \"type\": \"INTEGER\", \"description\": \"v2\"},\n", "        {\"name\": \"v2_flush_qty\", \"type\": \"INTEGER\", \"description\": \"v2_flush_qty\"},\n", "        {\"name\": \"pick_cap\", \"type\": \"INTEGER\", \"description\": \"pick_cap\"},\n", "        {\"name\": \"sales_line_items\", \"type\": \"INTEGER\", \"description\": \"sales_line_items\"},\n", "        {\"name\": \"ob_line_items\", \"type\": \"INTEGER\", \"description\": \"ob_line_items\"},\n", "        {\"name\": \"v1_line_items\", \"type\": \"INTEGER\", \"description\": \"v1_line_items\"},\n", "        {\"name\": \"v2_line_items\", \"type\": \"INTEGER\", \"description\": \"v2_line_items\"},\n", "        {\"name\": \"pick_sku\", \"type\": \"INTEGER\", \"description\": \"pick_sku\"},\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(3)\", \"description\": \"Last updated timestamp\"},\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date_ist\"},\n", "    ],\n", "    \"primary_key\": [\"be_outlet_id\", \"day\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"this table contains data about warehouse level transfer metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ef0e6416-0d19-4286-acdb-c0c864c4a86a", "metadata": {}, "outputs": [], "source": ["to_trino(sql, **kwargs)"]}, {"cell_type": "markdown", "id": "45e0f7d4-8611-41af-8be6-d2f13f9a7846", "metadata": {}, "source": ["df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "4483782d-a0c5-497f-abaf-6b28849aa8da", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_sales_transfer_metrics_logs\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"day\", \"type\": \"DATE\", \"description\": \"day\"},\n", "        {\"name\": \"be_outlet_id\", \"type\": \"INTEGER\", \"description\": \"be_outlet_id\"},\n", "        {\"name\": \"hot_outlet_name\", \"type\": \"VARCHAR\", \"description\": \"hot_outlet_name\"},\n", "        {\"name\": \"sales\", \"type\": \"INTEGER\", \"description\": \"sales\"},\n", "        {\"name\": \"ob_qty\", \"type\": \"INTEGER\", \"description\": \"ob_qty\"},\n", "        {\"name\": \"ob_flush_qty\", \"type\": \"INTEGER\", \"description\": \"ob_flush_qty\"},\n", "        {\"name\": \"v1\", \"type\": \"INTEGER\", \"description\": \"v1\"},\n", "        {\"name\": \"v2\", \"type\": \"INTEGER\", \"description\": \"v2\"},\n", "        {\"name\": \"v2_flush_qty\", \"type\": \"INTEGER\", \"description\": \"v2_flush_qty\"},\n", "        {\"name\": \"pick_cap\", \"type\": \"INTEGER\", \"description\": \"pick_cap\"},\n", "        {\"name\": \"sales_line_items\", \"type\": \"INTEGER\", \"description\": \"sales_line_items\"},\n", "        {\"name\": \"ob_line_items\", \"type\": \"INTEGER\", \"description\": \"ob_line_items\"},\n", "        {\"name\": \"v1_line_items\", \"type\": \"INTEGER\", \"description\": \"v1_line_items\"},\n", "        {\"name\": \"v2_line_items\", \"type\": \"INTEGER\", \"description\": \"v2_line_items\"},\n", "        {\"name\": \"pick_sku\", \"type\": \"INTEGER\", \"description\": \"pick_sku\"},\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(3)\", \"description\": \"Last updated timestamp\"},\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date_ist\"},\n", "    ],\n", "    \"primary_key\": [\"be_outlet_id\", \"day\"],\n", "    \"partition_key\": [\"day\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"this table contains data about warehouse level transfer metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b3e683e7-ac83-453e-afc4-d7cab50fdb99", "metadata": {}, "outputs": [], "source": ["to_trino(sql, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "9edcf614-039d-481a-9e58-a096c46b53e8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}