{"cells": [{"cell_type": "code", "execution_count": null, "id": "ed04e154-7a37-46e8-81a9-3e9bf2bf9c72", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from datetime import datetime, timedelta\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "62fa8a45-c734-4c3b-9a0b-1646c2bc87be", "metadata": {}, "outputs": [], "source": ["# Trino Connection\n", "trino = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "bd5e95ca-25d3-4038-92e7-d01ab868d525", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", round((end - start) / 60, 2), \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "7db05cbc-a823-4891-810b-9715c791e673", "metadata": {}, "outputs": [], "source": ["# DataFrame or SQL to Trino\n", "def to_trino(data_obj_to_trino, **kwargs):\n", "    trino = pb.get_connection(\"[Warehouse] Trino\")\n", "    print(\"<PERSON><PERSON> Connected Successfully!!!\")\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start_time = time.time()\n", "            pb.to_trino(data_obj_to_trino, **kwargs)\n", "            end_time = time.time()\n", "            # Runtime\n", "            duration = end_time - start_time\n", "\n", "            print(f\"Data pushed in table in {attempt + 1} attempt with runtime of {duration}\")\n", "            break\n", "        except Exception as e:\n", "            print(e)\n", "            if attempt == max_tries - 1:\n", "                raise Exception(f\"Operation failed after {max_tries} retries: {e}\")\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "c1054bc9-382b-4c1f-978b-054f40ef8ece", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "with \n", "active_outlets as (\n", "    select hot_outlet_id as outlet_id, facility_id, hot_outlet_name\n", "    from supply_etls.outlet_details\n", "    where ars_check = 1\n", "    and business_type_id = 7\n", "),\n", "packaged_goods as (\n", "    SELECT item_id,item_name,l0_category ,l1_category ,l2_category,p_type\n", "    FROM supply_etls.item_details where assortment_type = 'Packaged Goods'\n", "),\n", "pp as (\n", "    select id, item_id, name as item_name, variant_id, variant_mrp, cast(weight_in_gm * 0.001 as real) as weight_in_kg,storage_type\n", "    from rpc.product_product\n", "    where lake_active_record and active = 1 \n", "),\n", "\n", "pp_core as (\n", "    select pp.item_id, pp.item_name, pp.variant_mrp, pp.weight_in_kg,pp.storage_type\n", "    from pp\n", "    inner join (select item_id, max(id) as id from pp group by 1) p on p.item_id = pp.item_id and p.id = pp.id\n", "),\n", "pg_items as (\n", "select id.item_id, id.item_name, \n", "CASE\n", "                 WHEN pp.storage_type IN ('1',\n", "                                          '8',\n", "                                          '11') THEN 'REGULAR'\n", "                 WHEN pp.storage_type IN ('4',\n", "                                          '5') THEN 'HEAVY'\n", "                 WHEN pp.storage_type IN ('2',\n", "                                          '6') THEN 'COLD'\n", "                 WHEN pp.storage_type IN ('3',\n", "                                          '7') THEN 'FROZEN'\n", "                 ELSE 'REGULAR'\n", "             END AS storage_type,\n", " CASE\n", "                             WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                             WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                             WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                             WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                             WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                             WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                             WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                             WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                             WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                             WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                             WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                             WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                             WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                             WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                             WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                             WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                             WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                             WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                             WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                             WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                             ELSE 'UNKNOWN_CONFIG'\n", "                         END AS   custom_storage_type \n", " from \n", "packaged_goods id\n", "INNER JOIN \n", "pp_core pp  ON pp.item_id = id.item_id\n", "LEFT JOIN \n", "rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "      AND itm.active = TRUE\n", "      AND itm.lake_active_record\n", "      AND itm.item_id = id.item_id\n", "\n", "),\n", "active_as as (\n", "select pfom_fe.outlet_id, pfma.item_id, assortment_state , pfma.facility_id as fe_facility_id, pi.storage_type, pi.custom_storage_type, \n", "concat(pi.custom_storage_type,'_',pi.storage_type) as final_storage_type  from\n", "(select facility_id,item_id,case  \n", "    when master_assortment_substate_id = 1 then 'active'\n", "    when master_assortment_substate_id = 3 then 'temp-inactive'\n", "    end as assortment_state \n", "    from rpc.product_facility_master_assortment\n", "        where master_assortment_substate_id in (1,3) and active = 1 and lake_active_record ) pfma \n", "    inner join \n", "    (select outlet_id,facility_id from po.physical_facility_outlet_mapping where active = 1 and lake_active_record ) pfom_fe\n", "    on pfom_fe.facility_id = pfma.facility_id\n", "    inner join \n", "    active_outlets ao on ao.outlet_id = pfom_fe.outlet_id\n", "    inner join pg_items pi on pi.item_id = pfma.item_id \n", "),\n", "-- Filtering only ars runs\n", "runs as (\n", "SELECT run_id,run_date,completed_at, started_at\n", "        FROM ars.job_run\n", "        where started_at >= current_date-interval '3' day\n", "        AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "        -- AND simulation_params not like '%%mode%%'\n", "        -- AND simulation_params not like '%%hyperpure_run%%'\n", "        -- AND simulation_params not like '%%test%%'\n", "        -- AND simulation_params not like '%%any_day_po%%'    \n", "        -- AND simulation_params not like '%%spr_migration_simulation%%'\n", "        AND simulation_params not like '%%mode%%'\n", "        AND simulation_params not like '%%hyperpure_run%%'\n", "        AND simulation_params not like '%%test%%'\n", "        AND simulation_params not like '%%type%%'\n", "        AND simulation_params not like '%%manual%%'\n", "        AND simulation_params not like '%%any_day_po%%'    \n", "        AND simulation_params not like '%%spr_migration_simulation%%'\n", "        \n", "),\n", "\n", "-- run_level backend universe and inventory\n", "backend as \n", "(select be.be_outlet_id, oiu_be.item_id,be.run_id,(oiu_be.current_inventory - oiu_be.blocked_inventory) as be_inv, pfom.facility_id as be_facility_id\n", "from \n", "(select outlet_id as be_outlet_id, run_id  from ars.outlet where is_bulk = 1) be\n", "inner join \n", "(select outlet_id, current_inventory,blocked_inventory,final_inventory,item_id,run_id  from ars.outlet_item_universe\n", "where insert_ds_ist >= cast(current_date - interval '5' day as varchar)) oiu_be\n", "on oiu_be.run_id = be.run_id and be.be_outlet_id = oiu_be.outlet_id\n", "inner join \n", "packaged_goods pg on pg.item_id = oiu_be.item_id\n", "inner join \n", "(select outlet_id,facility_id from po.physical_facility_outlet_mapping where active = 1 and lake_active_record ) pfom\n", "on pfom.outlet_id = oiu_be.outlet_id\n", "),\n", "\n", "-- Frontend_cycle_sto (v1 calculation part)\n", "cycle_start as \n", "(\n", "SELECT run_id, from_outlet_id, to_outlet_id, item_id, sto_quantity as v1, max_transfer_quantity,\n", "    cycle_start_inventory, initial_demand_qty, cpd_sum as max_doi, moq, threshold_doi as qmax\n", "    from ars.frontend_cycle_sto_quantity where partition_field >= cast(current_date - interval '5' day as varchar)\n", "),\n", "-- )\n", "-- -- This gives me all details about be<>item case \n", "-- select backend_facility_id,item_id,case_size,case_flag,moq\n", "-- from ars.transfer_case_size where lake_active_record and case_flag in ('OUTER','INNER','CUSTOM') and case_size <> 1 and case_size <> 0\n", "-- )\n", "-- Truncations (v1->v2 transition)\n", "truncations as (\n", "SELECT  \n", "          torv.run_id,\n", "          frontend_outlet_id,\n", "          backend_outlet_id,\n", "          item_id,\n", "          sum(sto_quantity) as sto_quantity,\n", "          approx_percentile(normalised_score,0.5) as cpd_median,\n", "          sum(sto_quantity_post_truncation) as sto_quantity_post_truncation_in_case,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.inward_drop') AS INTEGER)) AS id,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER)) AS sd,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.truck_load_drop') AS INTEGER)) AS tld,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_quantity_drop') AS INTEGER)) AS pcd,\n", "          sum(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_sku_drop') AS INTEGER)) AS psd\n", "     FROM (select * from ars.transfers_optimization_results_v2 where insert_ds_ist >= CAST(current_date-interval '2' day AS VARCHAR)\n", "     AND lake_active_record = TRUE) torv inner join \n", "     runs on runs.run_id = torv.run_id\n", "     inner join active_outlets ao on ao.outlet_id = torv.frontend_outlet_id \n", "    group by 1,2,3,4\n", "),\n", "\n", "\n", "frontend_facility_constraints as (\n", "select frontend_outlet_id as outlet_id,is_current_slot, run_id, inward_capacity, inward_capacity_percent, shift_key, slot, max_sale, slot_count  from ars.frontend_facility_constraints\n", "where insert_ds_ist >= cast(current_date - interval '5' day as varchar) and is_current_slot and lake_active_record\n", "),\n", "backend_constraints as (\n", "select run_id, picking_capacity_assigned as pca,\n", "picking_capacity_for_shift as pcs,picking_capacity_total pct,picking_sku_assigned as psa, \n", "picking_sku_for_shift as pss,picking_sku_total as pst\n", "from ars.backend_facility_transfer_constraints where insert_ds_ist >= cast(current_date - interval '10' day as varchar) and lake_active_record\n", "),\n", "ffv as (\n", "select ffv.run_id, frontend_outlet_id, max(dispatch_time) dispatch_time, sum(capacity_in_kg) truck_capacity, \n", "    'MILK: ' || cast(count(case when vehicle_run_type = 'MILK' then 1 end) as varchar)  || ' DEDICATED: ' || cast(count(case when vehicle_run_type = 'DEDICATED' then 1 end) as varchar) vehicle_type\n", "    from ars.frontend_facility_vehicle_constraints ffv\n", "    inner join runs on runs.run_id = ffv.run_id\n", "    where insert_ds_ist >= CAST(current_date-interval '10' day AS VARCHAR)\n", "    and is_current_slot = true\n", "    group by 1,2),\n", "run_cpd as (\n", "select cpd, item_id, outlet_id, run_id from ars.outlet_item_cpd where insert_ds_ist >= cast(current_date - interval '5' day as varchar)\n", "),\n", "run_open_sto as (\n", "select item_id, outlet_id, run_id, \n", "sum(reserved_quantity - (inward_quantity + released_billed_quantity + released_reserved_quantity)) as sto_quantity \n", "from ars.outlet_item_open_sto where insert_ds_ist >= cast(current_date - interval '5' day as varchar)\n", "group by 1,2,3\n", "),\n", "    \n", "seq_inv as (\n", "select oiu.outlet_id,oiu.run_id, oiu.item_id, inv,oiu.final_inventory, runs.completed_at,runs.started_at,\n", "b.be_outlet_id,ffc.is_current_slot,b.be_inv,coalesce(b.be_facility_id,cast(substr(oiu.run_id, 9, length(oiu.run_id) - 11) as integer)) as be_facility_id\n", ",aa.fe_facility_id, ffc.inward_capacity, ffc.inward_capacity_percent, cast(ffc.inward_capacity as real) as inward_cap,\n", "aa.storage_type, aa.final_storage_type, ffv.truck_capacity, ffv.dispatch_time, coalesce(rc.cpd,0) as cpd, coalesce(ros.sto_quantity,0) as sto_qty,\n", "sum(2*coalesce(rc.cpd,0)) over (partition by oiu.run_id,b.be_outlet_id,oiu.item_id) as be_min_req_qty \n", "from \n", "(select outlet_id, item_id, run_id, final_inventory from ars.outlet_item_universe\n", "        where insert_ds_ist >= cast(current_date - interval '2' day as varchar) ) oiu  \n", "        inner join \n", "        runs on runs.run_id = oiu.run_id\n", "        inner join \n", "        active_as aa on aa.outlet_id = oiu.outlet_id and aa.item_id = oiu.item_id \n", "        inner join frontend_facility_constraints ffc on ffc.outlet_id = oiu.outlet_id and ffc.run_id = oiu.run_id\n", "        inner join ffv on ffv.frontend_outlet_id = oiu.outlet_id and ffv.run_id = oiu.run_id\n", "        -- inner join backend_constraints bc on bc.run_id = oiu.run_id \n", "        left join backend b on b.run_id = oiu.run_id and b.item_id = oiu.item_id\n", "        left join \n", "(select *, least(final_inventory,sequenced_inventory) as inv from ars.sequenced_outlet_item_universe where \n", "        insert_ds_ist >= cast(current_date - interval '2' day as varchar) and lake_active_record) si \n", "        on oiu.run_id = si.run_id and oiu.outlet_id = si.outlet_id and oiu.item_id = si.item_id\n", "        left join\n", "        run_cpd rc on rc.item_id = oiu.item_id and rc.outlet_id = oiu.outlet_id and rc.run_id = oiu.run_id \n", "        left join \n", "        run_open_sto ros on ros.run_id = oiu.run_id and ros.outlet_id = oiu.outlet_id and ros.item_id = oiu.item_id\n", "        \n", "),\n", "sto_details as (\n", "select si.item_id, run_id, merchant_outlet_id as outlet_id , isd.outlet_id as be_outlet_id,\n", "sum(si.indent_quantity) as indent_quantity\n", "from (select * from ims.ims_sto_details where sto_state in (1,2,4,5) and lake_active_record) isd\n", "inner join\n", "(select sto_id,run_id,item_id,reserved_quantity,indent_quantity from po.sto_items where active = 1 and lake_active_record) si on \n", "isd.sto_id = si.sto_id \n", "inner join \n", "active_outlets ao on ao.outlet_id = isd.merchant_outlet_id\n", "inner join \n", "packaged_goods pg on pg.item_id = si.item_id\n", "group by 1,2,3,4\n", "),\n", "\n", "\n", "-- storage_caps as (\n", "-- select pfom.outlet_id, fsc.storage_capacity, fsc.threshold_capacity, fsc.storage_type, fsc.run_id from \n", "-- (select facility_id, storage_capacity, threshold_capacity, storage_type, run_id \n", "-- from ars.facility_storage_capacity\n", "-- where insert_ds_ist >= cast(current_date - interval '10' day as varchar) and lake_active_record\n", "-- ) fsc\n", "-- inner join \n", "-- (select outlet_id,facility_id from po.physical_facility_outlet_mapping where active = 1 and lake_active_record ) pfom\n", "-- on pfom.facility_id = fsc.facility_id\n", "-- inner join \n", "-- active_outlets ao on ao.outlet_id = pfom.outlet_id\n", "-- ),\n", "\n", "\n", "-- This is complete raw data (includes data from item_universe -> sequencing -> cycle_start -> sto_raised (includes truncations) (all caps except storage cap))\n", "sequenced_inv as (      \n", "select si.outlet_id, si.item_id, si.final_inventory, si.is_current_slot,\n", "    case when si.final_inventory >0 then 1 else 0 end as ci_flag,\n", "    si.inv, case when si.inv >0 and si.inv is not null then 1 else 0 end as seq_flag,\n", "    si.run_id, sd.indent_quantity, case when sd.indent_quantity > 0 and sd.indent_quantity is not null then 1 else 0 end as ind_flag\n", "    ,si.completed_at+ interval '330' minute as run_finish_ts,\n", "    case \n", "    when greatest(coalesce(si.inv,0),0)+GREATEST(COALESCE(sd.indent_quantity,0),coalesce(t.sto_quantity,0)) >0 then 1 \n", "    when si.be_inv > 0 and si.be_inv is not null and si.be_inv >= si.be_min_req_qty then 1\n", "    else 0 end as be_inv_flag,\n", "    date(si.started_at+ interval '330' minute) as run_date_final,\n", "    si.be_facility_id,si.fe_facility_id,\n", "    t.sto_quantity as v1,\n", "    si.inward_cap,\n", "    cs.v1 as v1_bfr_sto,\n", "    case when cs.v1>0 and cs.v1 is not null then 1 else 0 end as v1_bfr_sto_flag,\n", "    case when t.sto_quantity>0 and t.sto_quantity is not null then 1 else 0 end as v1_flag,\n", "    t.sto_quantity_post_truncation_in_case as v2,\n", "    item_details.weight_in_gm * sto_quantity_post_truncation_in_case/1000.00 as v2_weight,\n", "    si.truck_capacity, si.dispatch_time,\n", "    CASE \n", "        WHEN COALESCE(t.sto_quantity - t.sd, 0) > 0 and si.storage_type = 'COLD'  THEN 1\n", "        WHEN COALESCE(t.sto_quantity , 0) > 0 AND si.storage_type <> 'COLD' THEN 1\n", "        ELSE 0 \n", "        END AS sd_cold,\n", "    CASE \n", "        WHEN COALESCE(t.sto_quantity - t.sd, 0) > 0 AND si.storage_type = 'REGULAR' THEN 1 \n", "        WHEN COALESCE(t.sto_quantity , 0) > 0 AND si.storage_type <> 'REGULAR' THEN 1 \n", "        ELSE 0 \n", "        END AS sd_regular,\n", "    CASE \n", "        WHEN COALESCE(t.sto_quantity - t.sd, 0) > 0 AND si.storage_type = 'HEAVY' THEN 1 \n", "        WHEN COALESCE(t.sto_quantity , 0) > 0 AND si.storage_type <> 'HEAVY' THEN 1 \n", "        ELSE 0 \n", "        END AS sd_heavy,\n", "    CASE \n", "        WHEN COALESCE(t.sto_quantity - t.sd, 0) > 0 AND si.storage_type = 'FROZEN' THEN 1 \n", "        WHEN COALESCE(t.sto_quantity, 0) > 0 AND si.storage_type <> 'FROZEN' THEN 1 \n", "        ELSE 0 \n", "        END AS sd_frozen,\n", "    CASE WHEN COALESCE(t.sto_quantity - t.id, 0) > 0 THEN 1 ELSE 0 END AS id, \n", "    CASE WHEN COALESCE(t.sto_quantity - t.sd, 0) > 0 THEN 1 ELSE 0 END AS sd, \n", "    CASE WHEN COALESCE(t.sto_quantity - t.tld, 0) > 0 THEN 1 ELSE 0 END AS tld, \n", "    CASE WHEN COALESCE(t.sto_quantity - t.pcd, 0) > 0 THEN 1 ELSE 0 END AS pcd, \n", "    CASE WHEN COALESCE(t.sto_quantity - t.psd, 0) > 0 THEN 1 ELSE 0 END AS psd\n", "    from seq_inv si \n", "    left join cycle_start cs on\n", "    cs.run_id = si.run_id and cs.item_id = si.item_id and si.outlet_id = cs.to_outlet_id\n", "    left join truncations t on \n", "    t.run_id = si.run_id and t.item_id = si.item_id and t.frontend_outlet_id = si.outlet_id\n", "    left join sto_details sd on \n", "    sd.outlet_id = si.outlet_id and si.run_id = sd.run_id and si.item_id = sd.item_id\n", "    left join\n", "    ars.item_details AS item_details \n", "    ON item_details.run_id = si.run_id \n", "    AND item_details.item_id = si.item_id \n", "    \n", "    \n", "    \n", "),\n", "be_weight as (\n", "select backend_facility_id,item_id,cast(weights as real) as weight from supply_etls.backend_item_weights where\n", "    updated_at = (select max(updated_at) from supply_etls.backend_item_weights)\n", ")\n", "-- 202412112010004\n", "SELECT CAST(run_date_final AS DATE) AS run_date_final,\n", "       CAST(run_finish_ts AS TIMESTAMP(3)) AS run_finish_ts,\n", "       CAST(A.run_id AS VARCHAR) AS run_id,\n", "       CAST(pfom.outlet_id AS INTEGER) AS be_outlet_id,\n", "       CAST(A.outlet_id AS INTEGER) AS outlet_id,\n", "       CAST(be.hot_outlet_name AS VARCHAR) AS be_name,\n", "       CAST(ao.hot_outlet_name AS VARCHAR) AS fe_name,\n", "       CAST(normal_avl AS DOUBLE) AS normal_avl,\n", "       CAST(be_avl AS DOUBLE) AS be_avl,\n", "       CAST(curr_avl AS DOUBLE) AS curr_avl,\n", "       CAST(t2_avl AS DOUBLE) AS t2_avl,\n", "       CAST(post_sto_connect_avl AS DOUBLE) AS post_sto_connect_avl,\n", "       CAST(max_avl_possible AS DOUBLE) AS max_avl_possible,\n", "       CAST(max_avl_possible - sto_max_possible AS DOUBLE) AS moq_qmax_drop,\n", "       CAST(sto_max_possible - id_avl AS DOUBLE) AS id_drop_in_avl,\n", "       CAST(sto_max_possible - sd_avl AS DOUBLE) AS sd_drop_in_avl,\n", "       CAST(sto_max_possible - sd_regular_avl AS DOUBLE) AS sd_reg_drop_in_avl,\n", "       CAST(sto_max_possible - sd_cold_avl AS DOUBLE) AS sd_cold_drop_in_avl,\n", "       CAST(sto_max_possible - sd_frozen_avl AS DOUBLE) AS sd_frozen_drop_in_avl,\n", "       CAST(sto_max_possible - sd_heavy_avl AS DOUBLE) AS sd_heavy_drop_in_avl,\n", "       CAST(sto_max_possible - tld_avl AS DOUBLE) AS tld_drop_in_avl,\n", "       CAST(sto_max_possible - pcd_avl AS DOUBLE) AS pcd_drop_in_avl,\n", "       CAST(sto_max_possible - psd_avl AS DOUBLE) AS psd_drop_in_avl,\n", "       CAST(A.inward_cap_util AS DOUBLE) AS inward_cap_util,\n", "       CAST(A.inward_cap_avl AS DOUBLE) AS inward_cap_avl,\n", "       CAST(pick_cap_assigned AS INTEGER) AS pick_cap_assigned,\n", "       CAST(perc_pick_cap_used AS DOUBLE) AS perc_pick_cap_used,\n", "       CAST(pick_sku_assigned AS INTEGER) AS pick_sku_assigned,\n", "       CAST(perc_sku_cap_used AS DOUBLE) AS perc_sku_cap_used,\n", "       CAST(CAST(A.inward_qty AS REAL) / CAST(A.v2 AS REAL) AS DOUBLE) AS V2_fill_rate,\n", "       CAST(A.v2 AS INTEGER) AS v2,\n", "       CAST(A.vehicle_util AS DOUBLE) AS vehicle_util,\n", "       CAST(A.truck_dispatch_time AS VARCHAR) AS truck_dispatch_time,\n", "       CURRENT_TIMESTAMP AS updated_at,\n", "       CURRENT_DATE AS date_ist\n", "\n", "from\n", "(select run_date_final,si.run_finish_ts,si.run_id,be_facility_id,outlet_id,\n", "sum(coalesce(si.indent_quantity,0))/NULLIF(max(si.inward_cap),0) as inward_cap_util,\n", "sum(coalesce(si.indent_quantity,0)) as inward_qty,\n", "sum(coalesce(si.v2,0)) as v2,\n", "cast(sum(coalesce(si.v2_weight,0)) as real )/cast(max(si.truck_capacity) as real) as vehicle_util,\n", "max(si.dispatch_time) as truck_dispatch_time, \n", "max(si.inward_cap) as inward_cap_avl,\n", "max(bc.pca) as pick_cap_assigned, \n", "max(bc.psa) as pick_sku_assigned, \n", "(cast(sum(coalesce(si.indent_quantity,0)) as real)/cast(max(bc.pca) as real))*100 as perc_pick_cap_used,\n", "sum(case when coalesce(si.indent_quantity,0) >0 then 1 else 0 end ) as line_items,\n", "(cast (sum(case when coalesce(si.indent_quantity,0) >0 then 1 else 0 end) as real)/cast(max(bc.psa)  as real))*100 as perc_sku_cap_used,\n", "cast(sum(ci_flag) as real)/cast(sum(1) as real) as normal_avl,\n", "cast(sum(ci_flag) as real) as nu,\n", "cast(sum(1) as real) as denom,\n", "sum(si.be_inv_flag*coalesce(weight,0))/NULLIF(sum(coalesce(weight,0)),0) as be_avl,\n", "sum(coalesce(weight,0)*ci_flag)/NULLIF(sum(coalesce(weight,0)),0) AS curr_avl,\n", "sum((case when inv is null then ci_flag else seq_flag end)*coalesce(weight,0))/NULLIF(sum(coalesce(weight,0)),0) as t2_avl,\n", "sum((case \n", "        when indent_quantity is null and inv is null then ci_flag\n", "        when indent_quantity is null and inv is not null then seq_flag\n", "        when indent_quantity is not null and inv is not null then greatest(seq_flag,ind_flag)\n", "        when indent_quantity is not null and inv is null then greatest(ci_flag,ind_flag)\n", "        end\n", ")*coalesce(weight,0))/NULLIF(sum(coalesce(weight,0)),0) as post_sto_connect_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND id IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND id IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND id IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND id IS NOT NULL THEN GREATEST(ci_flag, id)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND id IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND id IS NOT NULL THEN GREATEST(ci_flag, id, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND id IS NOT NULL THEN GREATEST(seq_flag, id)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND id IS NOT NULL THEN GREATEST(seq_flag, ind_flag, id)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as id_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd IS NOT NULL THEN GREATEST(ci_flag, sd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd IS NOT NULL THEN GREATEST(ci_flag, sd, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd IS NOT NULL THEN GREATEST(seq_flag, sd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as sd_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd IS NOT NULL THEN GREATEST(ci_flag, sd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd IS NOT NULL THEN GREATEST(ci_flag, sd, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd IS NOT NULL THEN GREATEST(seq_flag, sd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd)\n", "END)) as sd_count,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_regular IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_regular IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_regular IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_regular IS NOT NULL THEN GREATEST(ci_flag, sd_regular)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_regular IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_regular IS NOT NULL THEN GREATEST(ci_flag, sd_regular, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_regular IS NOT NULL THEN GREATEST(seq_flag, sd_regular)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_regular IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_regular)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as sd_regular_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_regular IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_regular IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_regular IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_regular IS NOT NULL THEN GREATEST(ci_flag, sd_regular)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_regular IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_regular IS NOT NULL THEN GREATEST(ci_flag, sd_regular, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_regular IS NOT NULL THEN GREATEST(seq_flag, sd_regular)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_regular IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_regular)\n", "END)) as sd_regular_count,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_cold IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_cold IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_cold IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_cold IS NOT NULL THEN GREATEST(ci_flag, sd_cold)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_cold IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_cold IS NOT NULL THEN GREATEST(ci_flag, sd_cold, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_cold IS NOT NULL THEN GREATEST(seq_flag, sd_cold)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_cold IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_cold)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as sd_cold_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_cold IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_cold IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_cold IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_cold IS NOT NULL THEN GREATEST(ci_flag, sd_cold)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_cold IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_cold IS NOT NULL THEN GREATEST(ci_flag, sd_cold, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_cold IS NOT NULL THEN GREATEST(seq_flag, sd_cold)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_cold IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_cold)\n", "END)) as sd_cold_count,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_heavy IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_heavy IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_heavy IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_heavy IS NOT NULL THEN GREATEST(ci_flag, sd_heavy)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_heavy IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_heavy IS NOT NULL THEN GREATEST(ci_flag, sd_heavy, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_heavy IS NOT NULL THEN GREATEST(seq_flag, sd_heavy)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_heavy IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_heavy)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as sd_heavy_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_heavy IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_heavy IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_heavy IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_heavy IS NOT NULL THEN GREATEST(ci_flag, sd_heavy)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_heavy IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_heavy IS NOT NULL THEN GREATEST(ci_flag, sd_heavy, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_heavy IS NOT NULL THEN GREATEST(seq_flag, sd_heavy)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_heavy IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_heavy)\n", "END)) as sd_heavy_count,\n", "\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_frozen IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_frozen IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_frozen IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_frozen IS NOT NULL THEN GREATEST(ci_flag, sd_frozen)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_frozen IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_frozen IS NOT NULL THEN GREATEST(ci_flag, sd_frozen, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_frozen IS NOT NULL THEN GREATEST(seq_flag, sd_frozen)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_frozen IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_frozen)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as sd_frozen_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_frozen IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_frozen IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_frozen IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND sd_frozen IS NOT NULL THEN GREATEST(ci_flag, sd_frozen)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_frozen IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND sd_frozen IS NOT NULL THEN GREATEST(ci_flag, sd_frozen, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND sd_frozen IS NOT NULL THEN GREATEST(seq_flag, sd_frozen)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND sd_frozen IS NOT NULL THEN GREATEST(seq_flag, ind_flag, sd_frozen)\n", "END)) as sd_frozen_count,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND tld IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND tld IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND tld IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND tld IS NOT NULL THEN GREATEST(ci_flag, tld)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND tld IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND tld IS NOT NULL THEN GREATEST(ci_flag, tld, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND tld IS NOT NULL THEN GREATEST(seq_flag, tld)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND tld IS NOT NULL THEN GREATEST(seq_flag, ind_flag, tld)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as tld_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND pcd IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND pcd IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND pcd IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND pcd IS NOT NULL THEN GREATEST(ci_flag, pcd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND pcd IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND pcd IS NOT NULL THEN GREATEST(ci_flag, pcd, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND pcd IS NOT NULL THEN GREATEST(seq_flag, pcd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND pcd IS NOT NULL THEN GREATEST(seq_flag, ind_flag, pcd)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as pcd_avl,\n", "sum((CASE \n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND psd IS NULL THEN ci_flag\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND psd IS NULL THEN GREATEST(ci_flag, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND psd IS NULL THEN seq_flag\n", "    WHEN indent_quantity IS NULL AND inv IS NULL AND psd IS NOT NULL THEN GREATEST(ci_flag, psd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND psd IS NULL THEN GREATEST(seq_flag, ind_flag)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NULL AND psd IS NOT NULL THEN GREATEST(ci_flag, psd, ind_flag)\n", "    WHEN indent_quantity IS NULL AND inv IS NOT NULL AND psd IS NOT NULL THEN GREATEST(seq_flag, psd)\n", "    WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND psd IS NOT NULL THEN GREATEST(seq_flag, ind_flag, psd)\n", "END)*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as psd_avl,\n", "\n", "sum((case \n", "        WHEN indent_quantity IS NULL AND inv IS NULL AND v1 IS NULL THEN ci_flag\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NULL AND v1 IS NULL THEN greatest(ci_flag,ind_flag)\n", "        WHEN indent_quantity IS NULL AND inv IS NOT NULL AND v1 IS NULL THEN seq_flag\n", "        WHEN indent_quantity IS NULL AND inv IS NULL AND v1 IS NOT NULL THEN greatest(ci_flag,v1_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND v1 IS NULL THEN greatest(seq_flag,ind_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NULL AND v1 IS NOT NULL THEN greatest(ci_flag,v1_flag,ind_flag)\n", "        WHEN indent_quantity IS NULL AND inv IS NOT NULL AND v1 IS NOT NULL THEN greatest(seq_flag,v1_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND v1 IS NOT NULL THEN greatest(seq_flag,ind_flag,v1_flag)\n", "\n", "        end\n", ")*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as sto_max_possible,\n", "sum((case \n", "        WHEN indent_quantity IS NULL AND inv IS NULL AND v1_bfr_sto IS NULL THEN ci_flag\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NULL AND v1_bfr_sto IS NULL THEN greatest(ci_flag,ind_flag)\n", "        WHEN indent_quantity IS NULL AND inv IS NOT NULL AND v1_bfr_sto IS NULL THEN seq_flag\n", "        WHEN indent_quantity IS NULL AND inv IS NULL AND v1_bfr_sto IS NOT NULL THEN greatest(ci_flag,v1_bfr_sto_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND v1_bfr_sto IS NULL THEN greatest(seq_flag,ind_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NULL AND v1_bfr_sto IS NOT NULL THEN greatest(ci_flag,v1_bfr_sto_flag,ind_flag)\n", "        WHEN indent_quantity IS NULL AND inv IS NOT NULL AND v1_bfr_sto IS NOT NULL THEN greatest(seq_flag,v1_bfr_sto_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND v1_bfr_sto IS NOT NULL THEN greatest(seq_flag,ind_flag,v1_bfr_sto_flag)\n", "\n", "        end\n", ")*coalesce(weight,0)\n", ")/NULLIF(sum(coalesce(weight,0)),0) as max_avl_possible,\n", "\n", "sum((case \n", "        WHEN indent_quantity IS NULL AND inv IS NULL AND v1 IS NULL THEN ci_flag\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NULL AND v1 IS NULL THEN greatest(ci_flag,ind_flag)\n", "        WHEN indent_quantity IS NULL AND inv IS NOT NULL AND v1 IS NULL THEN seq_flag\n", "        WHEN indent_quantity IS NULL AND inv IS NULL AND v1 IS NOT NULL THEN greatest(ci_flag,v1_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND v1 IS NULL THEN greatest(seq_flag,ind_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NULL AND v1 IS NOT NULL THEN greatest(ci_flag,v1_flag,ind_flag)\n", "        WHEN indent_quantity IS NULL AND inv IS NOT NULL AND v1 IS NOT NULL THEN greatest(seq_flag,v1_flag)\n", "        WHEN indent_quantity IS NOT NULL AND inv IS NOT NULL AND v1 IS NOT NULL THEN greatest(seq_flag,ind_flag,v1_flag)\n", "\n", "        end\n", ")) as max_count\n", "\n", "from sequenced_inv si\n", "inner join backend_constraints bc on bc.run_id = si.run_id\n", "left join be_weight bw on bw.backend_facility_id = si.be_facility_id and bw.item_id = si.item_id\n", "group by 1,2,3,4,5) A \n", "inner join \n", "(select outlet_id,facility_id from po.physical_facility_outlet_mapping where active = 1 and lake_active_record ) pfom\n", "on pfom.facility_id = A.be_facility_id\n", "inner join active_outlets ao on ao.outlet_id = A.outlet_id\n", "left join (select hot_outlet_id, hot_outlet_name from supply_etls.outlet_details where ars_active = 1 and business_type_id != 7 ) be on be.hot_outlet_id = pfom.outlet_id\n", "order by run_id desc\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "id": "9c82255b-91e5-446a-8320-db4e752b8619", "metadata": {}, "source": ["df = read_sql_query(sql, trino)"]}, {"cell_type": "markdown", "id": "8dae7103-b25c-4a34-854a-ec4d3a0fd79c", "metadata": {}, "source": ["df.dtypes"]}, {"cell_type": "markdown", "id": "367b5214-5f1a-4deb-8a00-06357c5c11b7", "metadata": {}, "source": ["df[\"v2_fill_rate\"] = df[\"v2_fill_rate\"].fillna(0)"]}, {"cell_type": "markdown", "id": "4f38c78b-85c1-42fe-adb9-9e5567036f97", "metadata": {}, "source": ["df = df.replace(\"NaN\", 0)"]}, {"cell_type": "code", "execution_count": null, "id": "7b22bce2-ef1d-4100-9ee0-3bbe4198af21", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_fe_run_lvl_metrics\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"run_date_final\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Final run date of the record\",\n", "        },\n", "        {\n", "            \"name\": \"run_finish_ts\",\n", "            \"type\": \"TIMESTAMP(3)\",\n", "            \"description\": \"Timestamp when the run finished\",\n", "        },\n", "        {\"name\": \"run_id\", \"type\": \"VARCHAR\", \"description\": \"Unique identifier for each run\"},\n", "        {\"name\": \"be_outlet_id\", \"type\": \"INTEGER\", \"description\": \"Business outlet identifier\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Outlet identifier\"},\n", "        {\"name\": \"be_name\", \"type\": \"VARCHAR\", \"description\": \"Name of the business outlet\"},\n", "        {\"name\": \"fe_name\", \"type\": \"VARCHAR\", \"description\": \"Name of the front-end outlet\"},\n", "        {\"name\": \"normal_avl\", \"type\": \"DOUBLE\", \"description\": \"Normal availability\"},\n", "        {\"name\": \"be_avl\", \"type\": \"DOUBLE\", \"description\": \"Business outlet availability\"},\n", "        {\"name\": \"curr_avl\", \"type\": \"DOUBLE\", \"description\": \"Current availability\"},\n", "        {\"name\": \"t2_avl\", \"type\": \"DOUBLE\", \"description\": \"T2 availability\"},\n", "        {\n", "            \"name\": \"post_sto_connect_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Post-store connection availability\",\n", "        },\n", "        {\n", "            \"name\": \"max_avl_possible\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Maximum possible availability\",\n", "        },\n", "        {\"name\": \"moq_qmax_drop\", \"type\": \"DOUBLE\", \"description\": \"MOQ Qmax drop\"},\n", "        {\"name\": \"id_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"ID drop in availability\"},\n", "        {\n", "            \"name\": \"sd_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_reg_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation regular drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_cold_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation cold drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_frozen_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation frozen drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_heavy_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation heavy drop in availability\",\n", "        },\n", "        {\"name\": \"tld_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"TLD drop in availability\"},\n", "        {\"name\": \"pcd_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"PCD drop in availability\"},\n", "        {\"name\": \"psd_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"PSD drop in availability\"},\n", "        {\"name\": \"inward_cap_util\", \"type\": \"DOUBLE\", \"description\": \"Inward capacity utilization\"},\n", "        {\"name\": \"inward_cap_avl\", \"type\": \"DOUBLE\", \"description\": \"Inward capacity availability\"},\n", "        {\"name\": \"pick_cap_assigned\", \"type\": \"INTEGER\", \"description\": \"Assigned pick capacity\"},\n", "        {\n", "            \"name\": \"perc_pick_cap_used\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Percentage of pick capacity used\",\n", "        },\n", "        {\"name\": \"pick_sku_assigned\", \"type\": \"INTEGER\", \"description\": \"Assigned pick SKU count\"},\n", "        {\n", "            \"name\": \"perc_sku_cap_used\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Percentage of SKU capacity used\",\n", "        },\n", "        {\"name\": \"v2_fill_rate\", \"type\": \"double\", \"description\": \"V2 fill rate\"},\n", "        {\"name\": \"v2\", \"type\": \"INTEGER\", \"description\": \"V2 value\"},\n", "        {\"name\": \"vehicle_util\", \"type\": \"DOUBLE\", \"description\": \"Vehicle utilization rate\"},\n", "        {\n", "            \"name\": \"truck_dispatch_time\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Truck dispatch timestamp\",\n", "        },\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(3)\", \"description\": \"Last updated timestamp\"},\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date_ist\"},\n", "    ],\n", "    \"primary_key\": [\"run_id\", \"be_outlet_id\", \"outlet_id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Contains data about run processes, availability metrics, and vehicle utilization.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "18bae72c-74d5-45ce-a865-a774105fdedf", "metadata": {}, "outputs": [], "source": ["to_trino(sql, **kwargs)"]}, {"cell_type": "markdown", "id": "19cf5134-8754-401d-9efb-69357a356ff5", "metadata": {}, "source": ["df[\"updated_at\"] = pd.to_datetime(datetime.today() + timedelta(hours=5.5))"]}, {"cell_type": "code", "execution_count": null, "id": "5349ca60-ca83-4e49-a703-614924d97fd1", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"be_fe_run_lvl_metrics_logs\",\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"run_date_final\",\n", "            \"type\": \"DATE\",\n", "            \"description\": \"Final run date of the record\",\n", "        },\n", "        {\n", "            \"name\": \"run_finish_ts\",\n", "            \"type\": \"TIMESTAMP(3)\",\n", "            \"description\": \"Timestamp when the run finished\",\n", "        },\n", "        {\"name\": \"run_id\", \"type\": \"VARCHAR\", \"description\": \"Unique identifier for each run\"},\n", "        {\"name\": \"be_outlet_id\", \"type\": \"INTEGER\", \"description\": \"Business outlet identifier\"},\n", "        {\"name\": \"outlet_id\", \"type\": \"INTEGER\", \"description\": \"Outlet identifier\"},\n", "        {\"name\": \"be_name\", \"type\": \"VARCHAR\", \"description\": \"Name of the business outlet\"},\n", "        {\"name\": \"fe_name\", \"type\": \"VARCHAR\", \"description\": \"Name of the front-end outlet\"},\n", "        {\"name\": \"normal_avl\", \"type\": \"DOUBLE\", \"description\": \"Normal availability\"},\n", "        {\"name\": \"be_avl\", \"type\": \"DOUBLE\", \"description\": \"Business outlet availability\"},\n", "        {\"name\": \"curr_avl\", \"type\": \"DOUBLE\", \"description\": \"Current availability\"},\n", "        {\"name\": \"t2_avl\", \"type\": \"DOUBLE\", \"description\": \"T2 availability\"},\n", "        {\n", "            \"name\": \"post_sto_connect_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Post-store connection availability\",\n", "        },\n", "        {\n", "            \"name\": \"max_avl_possible\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Maximum possible availability\",\n", "        },\n", "        {\"name\": \"moq_qmax_drop\", \"type\": \"DOUBLE\", \"description\": \"MOQ Qmax drop\"},\n", "        {\"name\": \"id_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"ID drop in availability\"},\n", "        {\n", "            \"name\": \"sd_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_reg_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation regular drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_cold_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation cold drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_frozen_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation frozen drop in availability\",\n", "        },\n", "        {\n", "            \"name\": \"sd_heavy_drop_in_avl\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Standard deviation heavy drop in availability\",\n", "        },\n", "        {\"name\": \"tld_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"TLD drop in availability\"},\n", "        {\"name\": \"pcd_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"PCD drop in availability\"},\n", "        {\"name\": \"psd_drop_in_avl\", \"type\": \"DOUBLE\", \"description\": \"PSD drop in availability\"},\n", "        {\"name\": \"inward_cap_util\", \"type\": \"DOUBLE\", \"description\": \"Inward capacity utilization\"},\n", "        {\"name\": \"inward_cap_avl\", \"type\": \"DOUBLE\", \"description\": \"Inward capacity availability\"},\n", "        {\"name\": \"pick_cap_assigned\", \"type\": \"INTEGER\", \"description\": \"Assigned pick capacity\"},\n", "        {\n", "            \"name\": \"perc_pick_cap_used\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Percentage of pick capacity used\",\n", "        },\n", "        {\"name\": \"pick_sku_assigned\", \"type\": \"INTEGER\", \"description\": \"Assigned pick SKU count\"},\n", "        {\n", "            \"name\": \"perc_sku_cap_used\",\n", "            \"type\": \"DOUBLE\",\n", "            \"description\": \"Percentage of SKU capacity used\",\n", "        },\n", "        {\"name\": \"v2_fill_rate\", \"type\": \"double\", \"description\": \"V2 fill rate\"},\n", "        {\"name\": \"v2\", \"type\": \"INTEGER\", \"description\": \"V2 value\"},\n", "        {\"name\": \"vehicle_util\", \"type\": \"DOUBLE\", \"description\": \"Vehicle utilization rate\"},\n", "        {\n", "            \"name\": \"truck_dispatch_time\",\n", "            \"type\": \"VARCHAR\",\n", "            \"description\": \"Truck dispatch timestamp\",\n", "        },\n", "        {\"name\": \"updated_at\", \"type\": \"TIMESTAMP(3)\", \"description\": \"Last updated timestamp\"},\n", "        {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"date_ist\"},\n", "    ],\n", "    \"primary_key\": [\"run_id\", \"be_outlet_id\", \"outlet_id\"],\n", "    \"partition_key\": [\"run_date_final\"],\n", "    \"incremental_key\": \"updated_at\",\n", "    \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": \"Contains data about run processes, availability metrics, and vehicle utilization.\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "4943f388-d35f-4178-bc7e-c28374465e14", "metadata": {}, "outputs": [], "source": ["to_trino(sql, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "3d35fa33-cfcf-4093-85f3-c7737e949ed7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}