{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## <PERSON><PERSON><PERSON>'s input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import math\n", "import time\n", "import random\n", "import logging\n", "import warnings\n", "from collections import defaultdict\n", "from datetime import datetime, date, timedelta\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "import pencilbox as pb\n", "import sqlalchemy as sqla\n", "\n", "from jinja2 import Template\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n", "pd.set_option(\"display.max_rows\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "retail = pb.get_connection(\"retail\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stockout_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "# end_date = (datetime.now() - timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "# start_date = (datetime.now() - timed<PERSON>ta(days=7)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Actual vs forecast"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "with \n", "base_rpc_daily_availability as\n", "    (select * from rpc_daily_availability\n", "    where order_Date>current_date-7),\n", "\n", "morning_availability as\n", "(SELECT a.*\n", "           FROM base_rpc_daily_availability AS a\n", "       INNER JOIN\n", "         (SELECT  date(order_date),max(order_date) AS maxi\n", "          FROM base_rpc_daily_availability\n", "          group by 1) AS b ON a.order_date = b.maxi\n", "          )\n", "          \n", ", facility_names_db AS\n", "  (SELECT facility_id,\n", "        p.id outlet_id,\n", "          op.name facility_name,\n", "          max(updated_at)\n", "   FROM lake_retail.console_outlet p\n", "   LEFT JOIN\n", "     (SELECT id,\n", "             name,\n", "             max(updated_at)\n", "      FROM lake_crates.facility\n", "      GROUP BY 1,\n", "               2) op ON op.id= p.facility_id\n", "   WHERE active =1 and op.name like '%%ES%%'\n", "     AND facility_id IS NOT NULL\n", "             and facility_id!=29\n", "   GROUP BY 1,\n", "            2,3)          \n", "\n", "\n", ", stockout_items AS\n", "  (SELECT DISTINCT x.item_id,\n", "                   x.facility_id,\n", "                   l0,\n", "                    is_gb,\n", "                    \"item_name \" as item_name,\n", "                    case when bucket_x = 'Y' then 'X' when buckets = 'A' then 'A-X' else 'B' end as bucket\n", "\n", "   FROM morning_availability  x\n", "    join (select distinct facility_id from  facility_names_db) nm on x.facility_id=nm.facility_id\n", "    left join (\n", "    select \n", "    item_id,\n", "    1 as is_persh\n", "    from metrics.ap_inventory_perishable_items\n", "    group by 1,2) pr on x.item_id=pr.item_id\n", "\n", "     WHERE date(order_date) = %(stockout_date)s\n", "     AND new_substate=1\n", "     AND inv_flag = 0\n", "     and (l0 not in ('Fruits & Vegetables', 'Vegetables & Fruits')\n", "                or pr.is_persh not in (1))\n", "     )\n", "\n", "\n", "\n", ", last_available_date_data as\n", "(select facility_id, item_id, max(date(order_date)) stockout_date\n", "  from rpc_daily_availability\n", "  where app_live=1 or inv_flag=1\n", " group by 1,2)\n", "\n", "\n", "\n", "\n", "\n", ", sales_data as\n", "  (select k.*, stockout_date\n", "    \n", "\n", "  from (select distinct R.facility_id, R.item_id, stockout_date from stockout_items r\n", "        left join last_available_date_data yt on r.item_id=yt.item_id AND r.facility_id=yt.facility_id ) base\n", "\n", "  left join \n", "      (select date(convert_timezone('Asia/Kolkata', b.created_at)) order_Date\n", "          , item_id        \n", "          , ot.facility_id\n", "          , sum(quantity) quantity\n", "\n", "FROM lake_ims.ims_order_items a\n", "  left join lake_ims.ims_order_details b on b.id=a.order_Details_id\n", "  left join lake_retail.console_outlet ot on b.outlet= ot.id\n", "  where b.status_id in (1,2)\n", "        GROUP BY 1,2,3) k on base.item_id=k.item_id and base.facility_id=k.facility_id\n", "      where stockout_date>=order_Date and datediff(day, order_date, stockout_date) between 0 and 3\n", "     \n", "  )\n", "\n", ", adjusted_forecast as\n", "(select  r.*, f_date, adjustment_date, forecast_qty from\n", "  (select distinct uy.facility_id, uy.item_id, stockout_date from stockout_items uy\n", "  left join last_available_date_data yt on uy.item_id=yt.item_id AND uy.facility_id=yt.facility_id ) r\n", "  left join\n", "    (select date(date) as f_date,\n", "    date(a.updated_at) as adjustment_date, \n", "    item_id ,\n", "    facility_id,\n", "    sum(consumption) as forecast_qty\n", "    from  lake_snorlax.date_wise_consumption  a\n", "    left join lake_retail.view_console_outlet b on a.outlet_id = b.id \n", "    group by 1,2,3,4\n", "    ) xt on r.item_id=xt.item_id and r.facility_id=xt.facility_id\n", "    where stockout_date>=f_date and datediff(day, f_date, stockout_date) between 0 and 3)\n", "\n", "\n", "\n", ", live_days_flag as\n", "(\n", "select tr.facility_id, tr.item_id, tr.stockout_date, mr.live_date, app_live from\n", "(select distinct uy.facility_id, uy.item_id, stockout_date from stockout_items uy\n", "  left join last_available_date_data yt on uy.item_id=yt.item_id AND uy.facility_id=yt.facility_id ) tr\n", "  left join \n", "  (\n", "    select facility_id, item_id, date(order_Date) live_date, 1 as app_live\n", "    from morning_availability\n", "    where new_substate=1 and (app_live=1 or app_live=1 )\n", "  ) mr on tr.facility_id=mr.facility_id and tr.item_id=mr.item_id\n", "    where stockout_date>=live_date and datediff(day, live_date, stockout_date) between 0 and 3)    \n", "\n", ", transfer_tag as\n", " (SELECT DISTINCT ro.facility_id AS frontend_facility,\n", "                      t.outlet_id as frontend_outlet,\n", "                      ro.name as frontend_outlet_name,\n", "                      r.facility_id as backend_facility,\n", "                      tag_value AS backend_outlet,\n", "                      r.name AS backend_outlet_name,\n", "                      item_id,\n", "                      1 as st_flag\n", "      FROM lake_rpc.item_outlet_tag_mapping t\n", "      INNER JOIN lake_retail.console_outlet ro ON ro.id=t.outlet_id\n", "      INNER JOIN lake_retail.console_outlet r ON r.id=t.tag_value\n", "      WHERE tag_type_id=8\n", "        AND t.active=1\n", "        and ro.name not like '%%old%%')    \n", "\n", ", table1 as\n", "(\n", "  select l1.live_date date_\n", "  , l1.facility_id\n", "  , l1.item_id\n", "  , l1.stockout_date\n", "  , count(distinct case when app_live=1 or l2.order_date is not null then l1.live_date end) live_days\n", "  , sum(coalesce(quantity,0)) AS sales_qty\n", "  \n", "  from live_days_flag l1\n", "  left join sales_data l2 on l1.live_date=l2.order_date and l1.facility_id=l2.facility_id and l1.item_id=l2.item_id\n", "  group by 1,2,3,4\n", ")\n", "\n", ", final_y as\n", "(select * from \n", "    (select  date_\n", "      , sales_qty\n", "      , si.facility_id\n", "      , si.item_id\n", "      , live_days\n", "      , r.facility_name\n", "    , coalesce(si.bucket, 'B') final_bucket\n", "    , af.forecast_qty adjusted_forecast_qty\n", "                    ,l0,\n", "                    is_gb,\n", "                    item_name,\n", "                    adjustment_date,\n", "                    la.stockout_date\n", "    from stockout_items si\n", "    left join table1 t1 on si.item_id=t1.item_id and si.facility_id=t1.facility_id\n", "--    left join bucket_size bs on si.item_id=bs.item_id and si.facility_id=bs.facility_id\n", "    left join adjusted_forecast af on t1.facility_id=af.facility_id and t1.item_id=af.item_id and t1.date_= af.f_date\n", "    left join (select distinct facility_id, facility_name from facility_names_db) r on si.facility_id=r.facility_id\n", "    left join last_available_date_data la on si.item_id=la.item_id and si.facility_id=la.facility_id\n", "\n", "    ) y\n", "  ) \n", "\n", "\n", "\n", "  ,  final1 AS\n", "  (select facility_id\n", "    , facility_name\n", "    , item_id,bucket\n", "    , adjusted_forecast_qty_avg\n", "    , case when live_days=0 then 0 else sales_qty/live_days end as  total_sales\n", "    , live_days\n", "    ,l0,\n", "                    is_gb,\n", "                    item_name,\n", "                    adjustment_date, stockout_date\n", "    from\n", "    (  SELECT e.facility_id,\n", "              e.facility_name,\n", "            item_id,\n", "            final_bucket bucket,\n", "            l0,\n", "\n", "                    is_gb,\n", "                    item_name,\n", "                    stockout_date,\n", "            avg(adjusted_forecast_qty) AS adjusted_forecast_qty_avg,\n", "            sum(coalesce(sales_qty,0)) sales_qty ,sum(coalesce(live_days,0)) AS live_days\n", "            , max(adjustment_date) adjustment_date\n", "     FROM final_y e\n", "     GROUP BY 1,2,3,4,5,6,7,8\n", "              ))\n", "\n", "    select distinct facility_id\n", ", facility_name\n", ", item_id\n", ", bucket\n", ", l0\n", ", is_gb\n", ", item_name\n", ", live_days\n", ", total_sales\n", ", adjusted_forecast_qty_avg\n", ", case when adjusted_forecast_qty_avg!=0 then total_sales/adjusted_forecast_qty_avg end sale_to_adj_forecast_ratio\n", ", adjustment_date\n", ", stockout_date\n", ", case  When total_sales>adjusted_forecast_qty_avg*2 then 'a. greater_than_200'\n", "            WHEN total_sales>adjusted_forecast_qty_avg*1.2\n", "                               AND total_sales<=adjusted_forecast_qty_avg*2 then 'b. range_120_200'\n", "            WHEN total_sales>adjusted_forecast_qty_avg*0.8\n", "                               AND total_sales<=adjusted_forecast_qty_avg*1.2 then 'c. range_80_120'\n", "            WHEN total_sales>adjusted_forecast_qty_avg*0.5\n", "                               AND total_sales<=adjusted_forecast_qty_avg*0.8 then 'd. range_50_80'\n", "            WHEN total_sales<=adjusted_forecast_qty_avg*0.5 then 'e. range_0_50' end as adjusted_forecast_Deviation\n", "            , st_flag\n", "from final1 a\n", "left join\n", "(select frontend_facility, item_id item_id2, st_flag\n", "from transfer_tag) b on a.facility_id=b.frontend_facility and a.item_id=b.item_id2\n", "join (select distinct facility_id facility_id2 \n", "        from consumer.merchant_forecast_carts  \n", "        where date(f_date)=%(stockout_date)s) fc on a.facility_id=fc.facility_id2\n", "\n", "\"\"\"\n", "\n", "\n", "act_forecast_df = pd.read_sql_query(query, redshift, params={\"stockout_date\": stockout_date})\n", "\n", "\n", "# act_forecast_df_temp= pd.read_sql_query(query\n", "#                                            , redshift\n", "#                                            , params={\"stockout_date\": stockout_date})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yester_day_live_store_query = \"\"\"\n", "select distinct facility_id, 1 as flag from\n", "    (select facility_id, count(distinct order_id) as order_count\n", "    from lake_ims.ims_order_details a\n", "    left join lake_retail.console_outlet o on a.outlet = o.id\n", "    where date(a.created_at) = current_date-interval '1 days'\n", "    and a.status_id <> 5\n", "    and business_type not ilike '%%b2b%%'\n", "    -- and business_type_id in (7)\n", "    group by 1) where order_count > 1\n", "\"\"\"\n", "\n", "yester_day_live_store = pd.read_sql_query(yester_day_live_store_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["act_forecast_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["act_forecast_df = act_forecast_df[act_forecast_df[\"st_flag\"] == 1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check in open STO data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sto1 = \"\"\"\n", "select\n", "distinct s.id sto_id\n", ", outlet_id\n", ", outlet_name\n", "\n", ", merchant_id\n", ", merchant_name\n", "\n", ", si.created_at\n", ", ss.name sto_state\n", ", ty.name sto_type\n", ", si.item_id\n", ", indent_quantity\n", ", reserved_quantity\n", ", run_id\n", "from\n", "po.sto_items si\n", "left join \n", "(select id, outlet_id, outlet_name, merchant_id, merchant_name, created_at, created_by,sto_state_id,sto_type_id, delivery_date\n", "from po.sto a1\n", ") s on s.id=si.sto_id \n", "left join po.sto_state ss on s.sto_state_id=ss.id\n", "left join po.sto_type ty on s.sto_type_id= ty.id\n", "where sto_state_id in (2,3)\n", "and date(s.delivery_date)>= %(today)s\n", "\n", "\"\"\"\n", "\n", "\n", "sto_data = pd.read_sql_query(sto1, retail, params={\"today\": stockout_date})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_list = list(sto_data[~sto_data[\"run_id\"].isna()][\"run_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outlet_facility_query = \"\"\"\n", "select id  outlet_id, facility_id from lake_retail.console_outlet\n", "where active=1\n", "\"\"\"\n", "\n", "outlet_facility_mapping = pd.read_sql_query(outlet_facility_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["facility_name_query = \"\"\"SELECT facility_id,\n", "          op.name,\n", "          max(updated_at)\n", "   FROM lake_retail.console_outlet p\n", "   LEFT JOIN\n", "     (SELECT id,\n", "             name,\n", "             max(updated_at)\n", "      FROM lake_crates.facility\n", "      GROUP BY 1,\n", "               2) op ON op.id= p.facility_id\n", "   WHERE active =1\n", "     AND facility_id IS NOT NULL\n", "   GROUP BY 1,\n", "            2\"\"\"\n", "facility_name = pd.read_sql_query(facility_name_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merchant_facility_query = \"\"\"\n", "select a.*, b.facility_id from\n", "(select id as merchant_id, outlet_id from retail.console_merchant\n", "where outlet_id is not null) a\n", "left join (select id outlet_id, facility_id from retail.console_outlet) b on a.outlet_id=b.outlet_id\n", "\n", "\"\"\"\n", "merchant_facility_mapping = pd.read_sql_query(merchant_facility_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# backend outlet_id to facility_id and facility name\n", "d1 = pd.merge(sto_data, outlet_facility_mapping.drop_duplicates(), how=\"left\", on=\"outlet_id\")\n", "d1.rename(columns={\"facility_id\": \"backend_facility_id\"}, inplace=True)\n", "d2 = pd.merge(\n", "    d1,\n", "    facility_name[[\"facility_id\", \"name\"]].rename(\n", "        columns={\"facility_id\": \"backend_facility_id\", \"name\": \"backend_facility_name\"}\n", "    ),\n", "    how=\"left\",\n", "    on=\"backend_facility_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#  front end merchant to front end facility and name\n", "d3 = pd.merge(\n", "    d2,\n", "    merchant_facility_mapping[[\"merchant_id\", \"facility_id\"]].rename(\n", "        columns={\"facility_id\": \"frontend_facility_id\"}\n", "    ),\n", "    how=\"left\",\n", "    on=\"merchant_id\",\n", ")\n", "\n", "d4 = pd.merge(\n", "    d3,\n", "    facility_name[[\"facility_id\", \"name\"]].rename(\n", "        columns={\n", "            \"facility_id\": \"frontend_facility_id\",\n", "            \"name\": \"frontend_facility_name\",\n", "        }\n", "    ),\n", "    how=\"left\",\n", "    on=\"frontend_facility_id\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d4[\"facility_item\"] = d4[\"frontend_facility_id\"].astype(int).astype(str) + d4[\"item_id\"].astype(\n", "    int\n", ").astype(str)\n", "sto_facility_item_list = list(d4[\"facility_item\"].unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Current Inventory\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["act_forecast_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st1 = act_forecast_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st1[\"facility_item\"] = st1[\"facility_id\"].astype(str) + st1[\"item_id\"].astype(str)\n", "st1[\"in_open_sto\"] = np.where(st1[\"facility_item\"].isin(sto_facility_item_list), 1, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# st2 = pd.merge(st1, last_available_df.rename(columns={\"last_available_date\":\"fe_last_available_date\"}),on=['facility_id', 'item_id'], how='left' )\n", "st2 = st1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fe_be_query = \"\"\"\n", "select\n", "a.item_id,\n", "c.facility_id as receiver_facility_id,\n", "cf2.name as rec_fac_name,\n", "b.facility_id as backend_facility_id,\n", "cf.name as backend_facility_name\n", "from lake_rpc.item_outlet_tag_mapping a\n", "left join (select id, facility_id from lake_retail.console_outlet group by 1,2) b on a.tag_value = b.id\n", "left join (select id, facility_id from lake_retail.console_outlet group by 1,2) c on a.outlet_id = c.id\n", "left join lake_crates.facility cf on cf.id = b.facility_id\n", "left join lake_crates.facility cf2 on cf2.id = c.facility_id\n", "where a.tag_type_id = 8 and a.active = 1\n", "group by 1,2,3,4,5\"\"\"\n", "\n", "febe_df = pd.read_sql_query(fe_be_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["st3 = pd.merge(\n", "    st2,\n", "    febe_df,\n", "    how=\"left\",\n", "    left_on=[\"facility_id\", \"item_id\"],\n", "    right_on=[\"receiver_facility_id\", \"item_id\"],\n", ")\n", "# st3.drop(['receiver_facility_id', 'rec_fac_name'],axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["in_open_sto_df = st3[st3[\"in_open_sto\"] == 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# st3.groupby([\"facility_id\", \"facility_name\"]).agg(\n", "#     {\"item_id\": \"nunique\", \"in_open_sto\": \"sum\"}\n", "# ).reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# st3.fillna(value={\"adjusted_forecast_deviation\": \"na\"}).pivot_table(\n", "#     index=[\"facility_id\", \"facility_name\"],\n", "#     columns=\"adjusted_forecast_deviation\",\n", "#     values=\"item_id\",\n", "#     aggfunc=pd.Series.nunique,\n", "# ).reset_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Steps for items not in open STO\n", "1. For run ID you can refer ars job_run table. But to get run id you need to have the facility id of the backend.\n", "2. Backend outlet id from ars outlet table filter on run_id and is_bulk flag = 1.\n", "3. bulk facility frontend demand table from ars db. Filter on run id and backend outlet and frontend outlet.\n", "4. outlet item universe table filter on run id outlet id item id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Step 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d1 = st3[st3[\"in_open_sto\"] != 1].rename(\n", "    columns={\n", "        \"facility_id\": \"frontend_facility_id\",\n", "        \"facility_name\": \"frontend_facility_name\",\n", "    }\n", ")\n", "# febe_df2= d1[['frontend_facility_id', 'frontend_facility_name', 'backend_facility_id','backend_facility_id' ,'item_id']].drop_duplicates()\n", "febe_df2 = d1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# febe_df2.groupby([\"frontend_facility_id\", \"frontend_facility_name\"])[\n", "#     \"item_id\"\n", "# ].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_run_id_query = \"\"\"\n", "select distinct facility_id backend_facility_id ,  run_date, run_id\n", "from ars.job_run\n", "where facility_id is not null\n", "and (\n", "    (JSON_EXTRACT(simulation_params, '$.run') in (\"ars_lite\")\n", "    or  JSON_EXTRACT(simulation_params, '$.run') in (\"multi_node_ordering\")\n", "    or  JSON_EXTRACT(simulation_params, '$.run') in (\"multi_business\")\n", "    )\n", "    and JSON_EXTRACT(simulation_params, '$.test') is null\n", ")\n", "\"\"\"\n", "\n", "run_id_df = pd.read_sql_query(get_run_id_query, retail)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_df1 = run_id_df.sort_values(by=[\"run_date\"], ascending=False)\n", "run_id_df2 = run_id_df1.drop_duplicates(subset=[\"backend_facility_id\"], keep=\"first\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d2 = pd.merge(febe_df2, run_id_df2, on=\"backend_facility_id\", how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d2[\"year\"] = d2[\"run_id\"].astype(str).str[0:6]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_id_list2 = list(d2[~d2[\"run_id\"].isna()][\"run_id\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## step 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_backend_outlet_query = \"\"\"\n", "select distinct outlet_id backend_outlet_id, run_id, physical_facility_id physical_facility_id_be from ars.outlet\n", "where is_bulk=1\n", "and run_id in %(run_id_list)s\"\"\"\n", "\n", "backend_outlet_df = pd.read_sql_query(\n", "    get_backend_outlet_query, retail, params={\"run_id_list\": run_id_list2}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_frontend_outlet_query = \"\"\"\n", "select distinct outlet_id frontend_outlet_id, run_id, physical_facility_id frontend_facility_id\n", "from ars.outlet\n", "where is_bulk=0\n", "and run_id in %(run_id_list)s\n", "\"\"\"\n", "\n", "frontend_outlet_df = pd.read_sql_query(\n", "    get_frontend_outlet_query, retail, params={\"run_id_list\": run_id_list2}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d3 = pd.merge(d2, backend_outlet_df, on=\"run_id\", how=\"left\")\n", "d4 = pd.merge(d3, frontend_outlet_df, on=[\"run_id\", \"frontend_facility_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dtemp= pd.merge(d3,frontend_outlet_df, on=['run_id','frontend_facility_id'], how='left')\n", "# dtemp.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## step 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_demand_query = \"\"\"\n", "select item_id,be_outlet_id as backend_outlet_id,fe_outlet_id as frontend_outlet_id,demand_quantity, run_id \n", "from ars.bulk_facility_frontend_demand\n", "where run_id in %(run_id_list)s\"\"\"\n", "\n", "demand_df = pd.read_sql_query(get_demand_query, retail, params={\"run_id_list\": run_id_list2})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["demand_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d6 = pd.merge(\n", "    d4,\n", "    demand_df,\n", "    on=[\"item_id\", \"backend_outlet_id\", \"frontend_outlet_id\", \"run_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# d6.groupby([\"frontend_facility_id\"])[\"item_id\"].nunique().reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d6[\"demand_generated\"] = np.where(d6[\"demand_quantity\"].isna(), 0, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## step 4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inventory_run_id_query = \"\"\"select item_id, outlet_id as backend_outlet_id, current_inventory, run_id\n", "from ars.outlet_item_universe\n", "where run_id in %(run_id_list)s\n", "\"\"\"\n", "\n", "inv_present = pd.read_sql_query(\n", "    inventory_run_id_query, retail, params={\"run_id_list\": run_id_list2}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d7 = pd.merge(d6, inv_present, on=[\"item_id\", \"backend_outlet_id\", \"run_id\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(d6.shape, d7.shape)\n", "d7.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get pre_truncation post_truncation data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pre_post_truncation_query = \"\"\"\n", "select * from ars.transfers_optimization_results_v2 t1\n", "where run_id in %(run_id)s\n", "\"\"\"\n", "pre_post_trunc_data = pd.read_sql_query(\n", "    pre_post_truncation_query, retail, params={\"run_id\": run_id_list2}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pre_post_trunc_data = pre_post_trunc_data[\n", "    [\n", "        \"item_id\",\n", "        \"sto_quantity\",\n", "        \"backend_outlet_id\",\n", "        \"frontend_outlet_id\",\n", "        \"frontend_facility_id\",\n", "        \"sto_quantity_post_truncation_in_case\",\n", "        \"run_id\",\n", "    ]\n", "].rename(\n", "    columns={\n", "        \"sto_quantity\": \"pre_truncation_quantity\",\n", "        \"frontend_facility_id\": \"frontend_facility_id_val\",\n", "        \"sto_quantity_post_truncation_in_case\": \"post_truncation_quantity\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d8 = pd.merge(\n", "    d7,\n", "    pre_post_trunc_data,\n", "    on=[\"item_id\", \"backend_outlet_id\", \"frontend_outlet_id\", \"run_id\"],\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d8.fillna(\n", "    value={\n", "        \"demand_quantity\": 0,\n", "        \"current_inventory\": 0,\n", "        \"pre_truncation_quantity\": 0,\n", "        \"post_truncation_quantity\": 0,\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Damaged during transit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["damage_quant_query = \"\"\"select pp.item_id, a2.quantity,a1.outlet_id,receiving_outlet_id,frontend_facility_id   from lake_pos.discrepancy_note a1\n", "left join lake_pos.discrepancy_note_product_detail a2 on a1.id=a2.dn_id_id\n", "left join (select distinct item_id, upc upc_id from lake_rpc.product_product) pp  on a2.upc_id=pp.upc_id\n", " join \n", "(select distinct invoice_id,  receiving_outlet_id, frontend_facility_id from metrics.esto_details x\n", "        left join (select id outlet_id, facility_id frontend_facility_id from lake_retail.console_outlet) y on x.receiving_outlet_id=y.outlet_id\n", "                    inner join (select distinct sto_id from lake_po.sto_items where run_id in %(run_id_list)s ) z on x.sto_id=z.sto_id\n", "        ) v1 on a1.vendor_invoice_id=v1.invoice_id\n", "        where a2.remark like '%%damage%%' or remark like '%%Damage%%'                \n", "\"\"\"\n", "\n", "damaged_items_df = pd.read_sql_query(\n", "    damage_quant_query, redshift, params={\"run_id_list\": tuple(run_id_list2)}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["group_list = [\n", "    \"frontend_facility_name\",\n", "    \"item_id\",\n", "    \"frontend_facility_id\",\n", "    \"sale_to_adj_forecast_ratio\",\n", "    \"bucket\",\n", "    \"l0\",\n", "    \"is_gb\",\n", "    \"adjusted_forecast_deviation\",\n", "    \"facility_item\",\n", "    \"in_open_sto\",\n", "    \"receiver_facility_id\",\n", "    \"rec_fac_name\",\n", "    \"backend_facility_id\",\n", "    \"backend_facility_name\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["not_in_open_sto_df = (\n", "    d8.fillna(value={\"sale_to_adj_forecast_ratio\": \"\", \"adjusted_forecast_deviation\": \"na\"})\n", "    .groupby(group_list)\n", "    .agg(\n", "        {\n", "            \"demand_generated\": \"max\",\n", "            \"demand_quantity\": \"sum\",\n", "            \"current_inventory\": \"sum\",\n", "            \"pre_truncation_quantity\": \"sum\",\n", "            \"post_truncation_quantity\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(\n", "    in_open_sto_df[\"demand_generated\"],\n", "    in_open_sto_df[\"demand_quantity\"],\n", "    in_open_sto_df[\"current_inventory\"],\n", "    in_open_sto_df[\"pre_truncation_quantity\"],\n", "    in_open_sto_df[\"post_truncation_quantity\"],\n", "    in_open_sto_df[\"not_present_in_backend\"],\n", "    in_open_sto_df[\"not_present_in_v1\"],\n", "    in_open_sto_df[\"not_present_in_v2\"],\n", ") = [np.nan, np.nan, np.nan, np.nan, np.nan, np.nan, np.nan, np.nan]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["in_open_sto_df.rename(\n", "    columns={\n", "        \"facility_name\": \"frontend_facility_name\",\n", "        \"facility_id\": \"frontend_facility_id\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["not_in_open_sto_df[\"not_present_in_backend\"] = np.where(\n", "    not_in_open_sto_df[\"current_inventory\"] == 0, 1, 0\n", ")\n", "not_in_open_sto_df[\"not_present_in_v1\"] = np.where(\n", "    not_in_open_sto_df[\"pre_truncation_quantity\"] == 0, 1, 0\n", ")\n", "not_in_open_sto_df[\"not_present_in_v2\"] = np.where(\n", "    not_in_open_sto_df[\"post_truncation_quantity\"] == 0, 1, 0\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# not_in_open_sto_df.groupby(['frontend_facility_id' ,'frontend_facility_name']).agg({\"item_id\":\"nunique\"\n", "#                                                                                     , \"not_present_in_backend\":\"sum\"\n", "#                                                                                    , \"not_present_in_v1\":\"sum\"\n", "#                                                                                    , \"not_present_in_v2\": \"sum\"}).reset_index().rename(columns={'item_id':'stockedout_items'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_raw_data = pd.concat(\n", "    [\n", "        in_open_sto_df.fillna(\n", "            value={\n", "                \"sale_to_adj_forecast_ratio\": \"\",\n", "                \"adjusted_forecast_deviation\": \"na\",\n", "            }\n", "        ),\n", "        not_in_open_sto_df,\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zone_query = \"\"\"select\n", "facility_id frontend_facility_id,\n", "max(zone) as zone\n", "from\n", "metrics.outlet_zone_mapping\n", "group by 1\n", "\"\"\"\n", "\n", "zone_df = pd.read_sql_query(zone_query, redshift)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_raw_data = pd.merge(\n", "    final_raw_data,\n", "    zone_df.astype({\"frontend_facility_id\": \"int32\"}),\n", "    on=\"frontend_facility_id\",\n", "    how=\"left\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["created_at = datetime.now() + <PERSON><PERSON><PERSON>(hours=5.5)\n", "created_at1 = created_at.strftime(\"%Y-%m-%d %H:%m:%s\")\n", "date = created_at.strftime(\"%Y-%m-%d\")\n", "final_raw_data[\"created_at\"] = created_at1\n", "final_raw_data[\"date\"] = date"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_raw_data.drop(\"created_at\", axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_raw_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_raw_data = final_raw_data[\n", "    [\n", "        \"zone\",\n", "        \"frontend_facility_id\",\n", "        \"frontend_facility_name\",\n", "        \"backend_facility_id\",\n", "        \"backend_facility_name\",\n", "        \"item_id\",\n", "        \"is_gb\",\n", "        \"bucket\",\n", "        \"l0\",\n", "        \"sale_to_adj_forecast_ratio\",\n", "        \"adjusted_forecast_deviation\",\n", "        \"in_open_sto\",\n", "        \"demand_generated\",\n", "        \"demand_quantity\",\n", "        \"current_inventory\",\n", "        \"pre_truncation_quantity\",\n", "        \"post_truncation_quantity\",\n", "        \"not_present_in_backend\",\n", "        \"not_present_in_v1\",\n", "        \"not_present_in_v2\",\n", "        \"date\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# col_df1=final_raw_data.dtypes.reset_index()\n", "# col_df1.rename(columns={'index':'name', 0:'type'}, inplace=True)\n", "# col_df1['description']=col_df1['name']\n", "# col_df2= col_df1[~col_df1['name'].isin(['facility_item', 'fe_last_available_date', 'receiver_facility_id', 'rec_fac_name']) ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_raw_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"zone\", \"type\": \"varchar\", \"description\": \"zone\"},\n", "    {\n", "        \"name\": \"frontend_facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"frontend_facility_id\",\n", "    },\n", "    {\n", "        \"name\": \"frontend_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"frontend_facility_name\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_id\",\n", "        \"type\": \"int\",\n", "        \"description\": \"backend_facility_id\",\n", "    },\n", "    {\n", "        \"name\": \"backend_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"backend_facility_name\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item_id\"},\n", "    {\"name\": \"is_gb\", \"type\": \"varchar\", \"description\": \"is_gb\"},\n", "    {\"name\": \"bucket\", \"type\": \"varchar\", \"description\": \"bucket\"},\n", "    {\"name\": \"l0\", \"type\": \"varchar\", \"description\": \"l0\"},\n", "    {\n", "        \"name\": \"sale_to_adj_forecast_ratio\",\n", "        \"type\": \"float\",\n", "        \"description\": \"sale_to_adj_forecast_ratio\",\n", "    },\n", "    {\n", "        \"name\": \"adjusted_forecast_deviation\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"adjusted_forecast_deviation\",\n", "    },\n", "    {\"name\": \"in_open_sto\", \"type\": \"int\", \"description\": \"in_open_sto\"},\n", "    {\"name\": \"demand_generated\", \"type\": \"float\", \"description\": \"demand_generated\"},\n", "    {\"name\": \"demand_quantity\", \"type\": \"float\", \"description\": \"demand_quantity\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"float\", \"description\": \"current_inventory\"},\n", "    {\n", "        \"name\": \"pre_truncation_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"pre_truncation_quantity\",\n", "    },\n", "    {\n", "        \"name\": \"post_truncation_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"post_truncation_quantity\",\n", "    },\n", "    {\n", "        \"name\": \"not_present_in_backend\",\n", "        \"type\": \"float\",\n", "        \"description\": \"not_present_in_backend\",\n", "    },\n", "    {\"name\": \"not_present_in_v1\", \"type\": \"float\", \"description\": \"not_present_in_v1\"},\n", "    {\"name\": \"not_present_in_v2\", \"type\": \"float\", \"description\": \"not_present_in_v2\"},\n", "    #     {\"name\": \"created_at\", \"type\": \"varchar\", \"description\": \"created_at\"},\n", "    {\"name\": \"date\", \"type\": \"varchar\", \"description\": \"date\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"dark_store_stockout_attribution_master_table_temp\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"frontend_facility_id\", \"item_id\", \"date\"],\n", "    \"sortkey\": [\"frontend_facility_id\", \"item_id\"],\n", "    \"incremental_key\": [\"item_id\"],\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert\n", "    \"table_description\": \"Dark Store stockout data \",  # Description of the table being sent to redshift\n", "}\n", "\n", "pb.to_redshift(final_raw_data, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_north = final_raw_data[final_raw_data[\"zone\"].isin([\"North\"])]\n", "df_north1 = df_north[df_north[\"frontend_facility_id\"] < 450]\n", "df_north2 = df_north[df_north[\"frontend_facility_id\"] >= 450]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id1 = \"1NxBNV2vwmAV28FlLDpnke2Rb6Fr-BnwkfivUdCOCkd0\"\n", "sheet_name1 = \"North-Article level data1\"\n", "pb.to_sheets(df_north1, sheet_id1, sheet_name1)\n", "\n", "sheet_id2 = \"1nR2gOSQDS2NaExAKyxp74-WzJKFDL2JN5BG7WFSlkuw\"\n", "sheet_name2 = \"North-Article level data2\"\n", "pb.to_sheets(df_north2, sheet_id2, sheet_name2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fr1 = pd.DataFrame(run_id_list2)\n", "sheet_name3 = \"run_id_list\"\n", "pb.to_sheets(fr1, sheet_id1, sheet_name3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_south = final_raw_data[final_raw_data[\"zone\"].isin([\"South\"])]\n", "shee_id_south = \"1SemEQNp8AFbsONFyk71WiyD6dl9IuPTFDbp5W8RX_H8\"\n", "sheet_name_south = \"South-Article level data\"\n", "pb.to_sheets(df_south, shee_id_south, sheet_name_south)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_east = final_raw_data[final_raw_data[\"zone\"].isin([\"East\"])]\n", "shee_id_east = \"1qrzRmfTt2QuJchYWpzfYDsZ18UJnSb445MqBWT6hRO0\"\n", "sheet_name_east = \"East-Article level data\"\n", "pb.to_sheets(df_east, shee_id_east, sheet_name_east)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_west = final_raw_data[final_raw_data[\"zone\"].isin([\"East\"])]\n", "shee_id_west = \"1JlkQJn5dKDnlvPIleYGQg5Db9XIsJhbUKD24c15vnBc\"\n", "sheet_name_west = \"West-Article level data\"\n", "pb.to_sheets(df_west, shee_id_west, sheet_name_west)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}