dag_name: ds_stockout_dashboard_update
dag_type: workflow
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: povms
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U01JWCA6AJG
path: povms/ds_stockout_dashboard/workflow/ds_stockout_dashboard_update
paused: true
pool: povms_pool
project_name: ds_stockout_dashboard
schedule:
  end_date: '2024-01-01T00:00:00'
  interval: 23 3 * * *
  start_date: '2021-12-07T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: bl-data-airflow-alerts
support_files: []
tags: []
template_name: notebook
version: 5
