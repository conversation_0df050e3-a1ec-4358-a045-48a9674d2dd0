{"cells": [{"cell_type": "code", "execution_count": null, "id": "95091d8b-03b2-42ca-b778-ec36d4fb374f", "metadata": {"tags": []}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import json\n", "import warnings\n", "from IPython.display import Javascript, display\n", "from IPython.display import display, Markdown, clear_output\n", "\n", "import ast"]}, {"cell_type": "code", "execution_count": null, "id": "044f5883-3037-4e66-ae87-8aa2c8cab453", "metadata": {"tags": []}, "outputs": [], "source": ["warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")\n", "con0 = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "\n", "sql0 = \"\"\"\n", "SELECT b.facility_id AS be_facility_id,\n", "       CASE\n", "           WHEN sender_facility_name = ('') THEN sender_outlet_name\n", "           WHEN a.sender_outlet_id = 1001 THEN 'Feeder-GM Bulk Kundli'\n", "           ELSE sender_facility_name\n", "       END AS sender_facility_name,\n", "       a.receiving_outlet_id,\n", "       receiver_outlet_name,\n", "       d.facility_id AS Receiver_FC_id,\n", "       CASE\n", "           WHEN receiver_facility_name = ('') THEN receiver_outlet_name\n", "           WHEN a.receiving_outlet_id = 1001 THEN 'Feeder-GM Bulk Kundli'\n", "           ELSE receiver_facility_name\n", "       END AS receiver_facility_name,\n", "       a.item_id,\n", "       a.item_name,\n", "       a.sto_type,\n", "       a.sto_state,\n", "       a.invoice_state,\n", "       sum(a.reserved_quantity) AS sto_raised_quantity,\n", "       sum(a.billed_quantity) AS billed_quantity,\n", "       sum(a.inwarded_quantity) AS GRN_quantity\n", "FROM metrics.esto_details a\n", "LEFT JOIN\n", "  (SELECT b.id,\n", "          b.name,\n", "          CASE\n", "              WHEN b.id IN (980, 979) THEN 'Bulk_G 2'\n", "              WHEN c.internal_facility_identifier IS NULL THEN ''\n", "              ELSE c.internal_facility_identifier\n", "          END AS sender_facility_name,\n", "          b.facility_id\n", "   FROM lake_retail.console_outlet b\n", "   LEFT JOIN lake_po.physical_facility c ON c.facility_id = b.facility_id\n", "   GROUP BY 1, 2, 3, 4) b ON b.id = a.sender_outlet_id\n", "LEFT JOIN\n", "  (SELECT d.id,\n", "          d.name,\n", "          CASE\n", "              WHEN d.id IN (980, 979) THEN 'Bulk_G 2'\n", "              WHEN e.internal_facility_identifier IS NULL THEN ''\n", "              ELSE e.internal_facility_identifier\n", "          END AS receiver_facility_name,\n", "          d.facility_id\n", "   FROM lake_retail.console_outlet d\n", "   LEFT JOIN lake_po.physical_facility e ON e.facility_id = d.facility_id\n", "   GROUP BY 1, 2, 3, 4)d ON d.id = a.receiving_outlet_id\n", "\n", "WHERE date(a.grn_started_at) IS NULL\n", "  AND date(a.sto_invoice_created_at)>CURRENT_DATE - 10\n", "  AND a.sto_state IN ('Created', 'Billed', 'Partial-Billed')\n", "  AND a.item_id IN\n", "    (SELECT item_id\n", "     FROM metrics.item_base_file_festival\n", "     GROUP BY 1)\n", "  AND b.facility_id IN (1, 43, 92, 264, 517, 555, 603, 1206, 1320, 1872, 1873, 1876, 1983, 1964, 2006)\n", "GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "\n", "df1 = pd.read_sql_query(sql=sql0, con=con0)\n", "df1"]}, {"cell_type": "code", "execution_count": null, "id": "f8c0a872-850a-4c33-a4b2-8f7875bef16a", "metadata": {}, "outputs": [], "source": ["df1.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "fb025a1d-f2ed-45e8-b18e-ead4faa602be", "metadata": {"tags": []}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"playground\",  # Redshift schema name\n", "    \"table_name\": \"festive_sto\",  # Redshift table name\n", "    \"column_dtypes\": [\n", "        {\n", "            \"name\": \"be_facility_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"be_facility_id\",\n", "        },\n", "        {\n", "            \"name\": \"sender_facility_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"sender_facility_name\",\n", "        },\n", "        {\n", "            \"name\": \"receiving_outlet_id\",\n", "            \"type\": \"int\",\n", "            \"description\": \"receiving_outlet_id\",\n", "        },\n", "        {\n", "            \"name\": \"receiver_outlet_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"sender_facility_name\",\n", "        },\n", "        {\"name\": \"receiver_fc_id\", \"type\": \"int\", \"description\": \"receiver_fc_id\"},\n", "        {\n", "            \"name\": \"receiver_facility_name\",\n", "            \"type\": \"varchar\",\n", "            \"description\": \"receiver_facility_name\",\n", "        },\n", "        {\"name\": \"item_id\", \"type\": \"int\", \"description\": \"item_id\"},\n", "        {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"item_name\"},\n", "        {\"name\": \"sto_type\", \"type\": \"varchar\", \"description\": \"sto_type\"},\n", "        {\"name\": \"sto_state\", \"type\": \"varchar\", \"description\": \"sto_state\"},\n", "        {\"name\": \"invoice_state\", \"type\": \"varchar\", \"description\": \"invoice_state\"},\n", "        {\n", "            \"name\": \"sto_raised_quantity\",\n", "            \"type\": \"int\",\n", "            \"description\": \"sto_raised_quantity\",\n", "        },\n", "        {\"name\": \"billed_quantity\", \"type\": \"float\", \"description\": \"billed_quantity\"},\n", "        {\"name\": \"grn_quantity\", \"type\": \"varchar\", \"description\": \"grn_quantity\"},\n", "    ],\n", "    \"primary_key\": [\"be_facility_id\"],\n", "    \"sortkey\": [\"be_facility_id\"],\n", "    \"incremental_key\": \"be_facility_id\",\n", "    \"load_type\": \"rebuild\",\n", "    \"table_description\": \"festive sto\",  # Description of the table being sent to redshift\n", "}\n", "\n", "\n", "pb.to_redshift(df1, **kwargs)\n", "print(\"done\")"]}, {"cell_type": "code", "execution_count": null, "id": "c048990f-da9f-4008-a51d-e525ea9fb9b1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}