alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: festival_sto
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: registry.grofer.io/data/airflow-common:stable
    memory:
      limit: 8G
      request: 1G
    node_selectors:
      nodetype: spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: registry.grofer.io/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 2
owner:
  email: <EMAIL>
  slack_id: U056F5X9Q5U
path: povms/festival_sto_dag/etl/festival_sto
paused: false
project_name: festival_sto_dag
schedule:
  end_date: '2023-12-25T00:00:00'
  interval: 0 * * * *
  start_date: '2023-10-25T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
pool: povms_pool
