{"cells": [{"cell_type": "code", "execution_count": null, "id": "c00de6db-016f-43d9-9d2d-942bce30f4cb", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "8cdf9a14-6048-41a9-8ce3-c2ffb6cdeff4", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "bfbdaa00-b1db-41c6-a2f0-ca40f11933ea", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d607bb42-1b0a-4fa1-937c-8d083f32d736", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "3a2ef39d-5459-4efd-be1b-1d4f3e02ad01", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\"name\": \"be_outlet_id\", \"type\": \"integer\", \"description\": \"BE Facility ID\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"FE Facility ID\"},\n", "    {\"name\": \"be_name\", \"type\": \"varchar\", \"description\": \"FE P-Type Availability\"},\n", "    {\"name\": \"be_availability\", \"type\": \"real\", \"description\": \"FE Facility ID\"},\n", "    {\"name\": \"fe_availability\", \"type\": \"real\", \"description\": \"FE P-Type Availability\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "cb278900-ab63-49b2-9fa3-1ba625b184f8", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"bexfe_ptype_availability\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"be_facility_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"dt_hour\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains BExFE p-type level availability for Inventory Core Metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "79b9fa82-2b2c-483b-b550-22ca683d9510", "metadata": {}, "outputs": [], "source": ["backfill_flag = 0\n", "\n", "\n", "def normal():\n", "    bexfe_ptype_avail_query = f\"\"\"\n", "WITH fo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          mo_map.frontend_merchant_id AS merchant_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id IN (7)\n", "   AND rco.company_type_id NOT IN (771)\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "   AND rco.business_type_id = 7\n", "   AND is_current\n", "   AND is_current_mapping_active\n", "   AND is_backend_merchant_active\n", "   AND is_frontend_merchant_active\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.outlet_name NOT LIKE '%%Draft%%'\n", "     AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "     AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "     AND om.facility_id != 273 ),\n", "     catalog_ AS\n", "  (SELECT *\n", "   FROM\n", "     (SELECT icd.item_id,\n", "             icd.name,\n", "             icd.l0,\n", "             icd.l1,\n", "             icd.l2,\n", "             icd.product_type,\n", "             CASE\n", "                 WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "                 ELSE 'PACKAGED'\n", "             END AS item_type,\n", "             id.storage_type AS storage_type_raw,\n", "             CASE\n", "                 WHEN id.storage_type IN ('1',\n", "                                          '8',\n", "                                          '11') THEN 'REGULAR'\n", "                 WHEN id.storage_type IN ('4',\n", "                                          '5') THEN 'HEAVY'\n", "                 WHEN id.storage_type IN ('2',\n", "                                          '6') THEN 'COLD'\n", "                 WHEN id.storage_type IN ('3',\n", "                                          '7') THEN 'FROZEN'\n", "                 ELSE 'REGULAR'\n", "             END AS storage_type,\n", "             CASE\n", "                 WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "                 WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "                 ELSE 'REGULAR'\n", "             END AS handling_type,\n", "             id.variant_mrp AS mrp,\n", "             id.variant_description,\n", "             id.weight_in_gm,\n", "             id.length_in_cm,\n", "             id.height_in_cm,\n", "             id.breadth_in_cm,\n", "             id.shelf_life,\n", "             coalesce(itf.item_factor, 0.01) AS item_factor,\n", "             DATE_DIFF('DAY',date(icd.created_at), CURRENT_DATE-INTERVAL '1' DAY) AS item_catalog_age,       --\n", "             rank() OVER (PARTITION BY id.item_id\n", "                          ORDER BY id.id DESC) AS variant_rank,\n", "                         itm.tag_value AS custom_storage_type_raw,\n", "                         CASE\n", "                             WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                             WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                             WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                             WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                             WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                             WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                             WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                             WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                             WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                             WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                             WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                             WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                             WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                             WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                             WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                             WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                             WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                             WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                             WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                             WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                             ELSE 'UNKNOWN_CONFIG'\n", "                         END AS custom_storage_type,\n", "                         pb.manufacturer_id,\n", "                         id.manufacturer AS manufacturer_name,\n", "                         pb.name AS brand_name,\n", "                         id.outer_case_size,\n", "                         id.inner_case_size,\n", "                         CASE\n", "                             WHEN itm2.tag_value = '1' THEN TRUE\n", "                             ELSE FALSE\n", "                         END AS is_high_value,\n", "                         itm2.tag_value AS high_value_tag_raw\n", "      FROM rpc.item_category_details icd\n", "      INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "      AND id.active = 1\n", "      AND id.approved = 1\n", "      AND id.lake_active_record\n", "      LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "      LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "      AND itm.active = TRUE\n", "      AND itm.lake_active_record\n", "      AND itm.item_id = icd.item_id\n", "      LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "      AND pb.lake_active_record\n", "      AND pb.active = 1\n", "      LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "      AND itm2.active\n", "      AND itm2.tag_type_id = 3\n", "      AND itm2.lake_active_record\n", "      WHERE icd.lake_active_record\n", "        AND perishable != 1\n", "        AND id.handling_type != '8'\n", "        AND id.storage_type NOT IN ('3',\n", "                                    '7')\n", "        AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "\n", "        AND icd.l0 NOT IN ('wholesale store',\n", "                           'Trial NEW tree',\n", "                           'Specials')-- removing test and flyer/freebie l0s\n", ") AS x\n", "   WHERE variant_rank = 1),\n", "     \n", "     \n", "    runs AS(\n", "    SELECT run_id,\n", "          max(started_at) AS started_at\n", "   FROM ars.job_run\n", "   WHERE started_at+interval'5'hour+interval'30'MINUTE BETWEEN CURRENT_DATE-interval'3'DAY  AND CURRENT_DATE        --\n", "     AND simulation_params NOT LIKE '%%MODE%%'\n", "     AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "     AND simulation_params NOT LIKE '%%hyperpure_run%%'\n", "     AND simulation_params NOT LIKE '%%test%%'\n", "     AND simulation_params NOT LIKE '%%TYPE%%'\n", "     AND simulation_params NOT LIKE '%%MANUAL%%'\n", "     AND simulation_params NOT LIKE '%%any_day_po%%'\n", "     AND simulation_params NOT LIKE '%%spr_migration_simulation%%'\n", "   GROUP BY 1),\n", "   \n", "    run_outlet_combination AS\n", "  (WITH base AS\n", "     (SELECT run_id,\n", "             CAST(SPLIT(replace(replace(auto_sto_outlets, '[', ''), ']', ''), ',') AS ARRAY<VARCHAR>) AS outlet_ids\n", "      FROM ars.backend_facility_slot_details\n", "      WHERE run_id IN\n", "          (SELECT run_id\n", "           FROM runs)\n", "        AND is_current_slot = TRUE\n", "        AND insert_ds_ist BETWEEN cast(CURRENT_DATE - INTERVAL '3' DAY AS varchar) AND cast(CURRENT_DATE as varchar)) \n", "        SELECT run_id,        \n", "        CAST(NULLIF(outlet_id, '') AS INTEGER) AS outlet_id\n", "   FROM base\n", "   CROSS JOIN UNNEST(outlet_ids) AS t(outlet_id)),\n", "   \n", "    runs_data AS\n", "    (SELECT fcs.sto_date,\n", "          fcs.from_outlet_id AS be_outlet_id,\n", "          pfom.outlet_name AS be_name,\n", "          pfom.facility_id AS be_facility_id,\n", "          fcs.to_outlet_id AS fe_outlet_id,\n", "          fo.outlet_name AS fe_name,\n", "          fcs.item_id,\n", "          fcs.run_id,\n", "          runs.started_at,\n", "          date_trunc('hour', runs.started_at) AS truncated_run_started_at,\n", "          coalesce(fcs.cycle_start_inventory,0) AS cycle_start_inventory,\n", "          coalesce(fcs.max_transfer_quantity,0) AS max_transfer_quantity,\n", "          pfma.assortment_type,\n", "          coalesce(torv.sto_quantity,0) AS v1,\n", "          coalesce(sto_quantity_post_truncation,0) AS v2,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.inward_drop') AS INTEGER),0) AS inward_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER),0) AS storage_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.truck_load_drop') AS INTEGER),0) AS truck_load_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_quantity_drop') AS INTEGER),0) AS picking_capacity_quantity_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_sku_drop') AS INTEGER),0) AS picking_capacity_sku_drop,\n", "          rank() OVER (PARTITION BY fcs.from_outlet_id,\n", "                                    fcs.to_outlet_id\n", "                       ORDER BY date_trunc('hour', runs.started_at) DESC) AS rank_\n", "   FROM ars.frontend_cycle_sto_quantity fcs\n", "   INNER JOIN runs ON runs.run_id = fcs.run_id\n", "   INNER JOIN run_outlet_combination roc ON roc.run_id = fcs.run_id\n", "   AND roc.outlet_id = fcs.to_outlet_id\n", "   LEFT JOIN ars.transfers_optimization_results_v2 torv ON torv.insert_ds_ist BETWEEN cast(CURRENT_DATE - interval '2' DAY AS varchar) AND cast(CURRENT_DATE as varchar) --\n", "   AND fcs.run_id = torv.run_id\n", "   AND fcs.to_outlet_id = torv.frontend_outlet_id\n", "   AND fcs.item_id = torv.item_id\n", "   AND fcs.from_outlet_id = torv.backend_outlet_id\n", "   LEFT JOIN po.physical_facility_outlet_mapping pfom ON pfom.outlet_id = fcs.from_outlet_id\n", "   AND pfom.lake_active_record\n", "   AND pfom.active = 1\n", "   INNER JOIN fo ON fo.outlet_id = fcs.to_outlet_id\n", "   INNER JOIN rpc.product_facility_master_assortment pfma ON pfma.facility_id = fo.facility_id\n", "   AND pfma.item_id = fcs.item_id\n", "   AND pfma.active = 1\n", "   AND pfma.master_assortment_substate_id IN (1)\n", "   AND pfma.lake_active_record\n", "   INNER JOIN catalog_ c ON c.item_id = fcs.item_id\n", "   WHERE fcs.partition_field BETWEEN cast(CURRENT_DATE - interval '2' DAY AS varchar) AND cast(CURRENT_DATE as varchar) ),           --\n", "     \n", "     cpd_data AS(\n", "     SELECT cpd.*\n", "       FROM ars.outlet_item_aps_derived_cpd cpd\n", "       WHERE insert_ds_ist = cast(CURRENT_DATE-INTERVAL '1' DAY AS varchar)         ---\n", "         AND aps_adjusted > 0\n", "         AND lake_active_record ),\n", "     \n", "    base AS (\n", "    SELECT rd.*,\n", "              coalesce(c.aps_adjusted, 0.045) AS cpd,\n", "              (coalesce(rd.cycle_start_inventory,0) * 1.0000) / coalesce(c.aps_adjusted, 0.045) AS doi_pre_run,\n", "              ((coalesce(rd.cycle_start_inventory,0) + coalesce(v2, 0)) * 1.0000) / coalesce(c.aps_adjusted, 0.045) AS doi_post_run,\n", "              coalesce(greatest(inward_drop,storage_drop,truck_load_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop),0) AS max_truncation,\n", "              CASE\n", "                  WHEN coalesce(rd.cycle_start_inventory,0) = 0\n", "                       AND coalesce(greatest(inward_drop,storage_drop,truck_load_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop),0) = 0\n", "                       AND v2 = 0\n", "                       AND max_transfer_quantity > 0 THEN 1\n", "                  ELSE coalesce(greatest(inward_drop,storage_drop,truck_load_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop),0)\n", "              END AS revised_truncation\n", "       FROM runs_data rd\n", "       LEFT JOIN cpd_data c ON c.outlet_id = rd.fe_outlet_id\n", "       AND rd.item_id = c.item_id\n", "       WHERE rank_ = 1 ),\n", "   \n", "    av_weights AS(\n", "    SELECT cast(av.item_id AS int) AS item_id,\n", "              cast(backend_facility_id AS int) AS backend_facility_id,\n", "              max(cast(weights AS DOUBLE)) AS av_weight\n", "       FROM supply_etls.backend_item_weights av\n", "       INNER JOIN base ON base.be_facility_id = cast(backend_facility_id AS int)\n", "       AND base.item_id = cast(av.item_id AS int)\n", "       WHERE cast(weights AS DOUBLE) > 0\n", "         AND av.assortment_type = 'Packaged Goods'\n", "         AND updated_at =\n", "           (SELECT max(updated_at)\n", "            FROM supply_etls.backend_item_weights)\n", "       GROUP BY 1,\n", "                2),\n", "            \n", "    normalized_weights AS\n", "      (SELECT item_id,\n", "              backend_facility_id,\n", "              av_weight / SUM(av_weight) OVER (PARTITION BY backend_facility_id) AS normalized_av_weight\n", "       FROM av_weights\n", "       ORDER BY 3 DESC),\n", "   \n", "    cumulative_weights AS\n", "      (SELECT item_id,\n", "              backend_facility_id,\n", "              normalized_av_weight,\n", "              SUM(normalized_av_weight) OVER (PARTITION BY backend_facility_id\n", "                                              ORDER BY normalized_av_weight DESC) AS cumulative_weight\n", "       FROM normalized_weights),\n", "   \n", "current_fe_inv AS\n", "      (SELECT try_cast(ii.item_id AS int) AS item_id,\n", "              try_cast(ii.outlet_id AS int) AS outlet_id,\n", "              sum(quantity - coalesce(blocked_inv, 0)) AS inv,\n", "              coalesce(max(itf.item_factor), 1) AS item_factor\n", "       FROM dynamodb.blinkit_store_inventory_service_oi_rt_view AS ii\n", "       LEFT JOIN supply_etls.item_factor itf ON cast(itf.item_id AS varchar) = ii.item_id\n", "       LEFT JOIN\n", "         (SELECT item_id,\n", "                 outlet_id,\n", "                 sum(quantity) blocked_inv\n", "          FROM dynamodb.blinkit_store_inventory_service_blk_rt_view\n", "          WHERE status = 'BLOCKED'\n", "            AND reference_type NOT IN ('DL_VALIDATION_CRON',\n", "                                       'PNA')\n", "          GROUP BY 1,\n", "                   2) ib ON ib.item_id = ii.item_id\n", "       AND ib.outlet_id = ii.outlet_id\n", "       WHERE ii.state = 'GOOD'\n", "       GROUP BY 1,\n", "                2),\n", "            \n", "sto_open AS\n", "  (SELECT item_id,\n", "          receiver_inv_outlet_id,\n", "          sum(open_sto_quantity) AS open_sto_quantity,\n", "          sum(billed_sto_quantity) AS billed_sto_quantity\n", "   FROM supply_etls.inventory_metrics_open_sto\n", "   WHERE sto_status = 'open'\n", "   GROUP BY 1,\n", "            2),\n", "            \n", "            \n", "    active_carts AS\n", "      (SELECT outlet_id,\n", "              count(DISTINCT order_id) orders\n", "       FROM dwh.fact_sales_order_details\n", "       WHERE order_create_dt_ist BETWEEN CURRENT_DATE - interval '3' DAY AND CURRENT_DATE ---\n", "       GROUP BY 1\n", "       HAVING count(DISTINCT order_id) > 50),\n", "   \n", "final_base AS\n", "  (SELECT be_outlet_id,\n", "          be_facility_id,\n", "          be_name,\n", "          fe_outlet_id,\n", "          fe_name,\n", "          product_type,\n", "          sum(max_transfer_quantity) max_transfer_quantity,\n", "          sum(current_inventory) current_inventory\n", "   FROM\n", "     (SELECT base.*,\n", "             CASE\n", "                 WHEN inward_drop = revised_truncation THEN 'inward_drop'\n", "                 WHEN storage_drop = revised_truncation THEN 'storage_drop'\n", "                 WHEN truck_load_drop = revised_truncation THEN 'truck_load_drop'\n", "                 WHEN picking_capacity_quantity_drop = revised_truncation THEN 'picking_capacity_quantity_drop'\n", "                 WHEN picking_capacity_sku_drop = revised_truncation THEN 'picking_capacity_sku_drop'\n", "                 ELSE 'need_to_check_truncation - possibly be oos or thin dois'\n", "             END AS truncation_reason,\n", "             c.name,\n", "             c.l0,\n", "             c.product_type,\n", "             c.item_factor,\n", "             c.storage_type,\n", "             c.custom_storage_type,\n", "             cw.normalized_av_weight,\n", "             (coalesce(cfi.inv,0) + coalesce(sop.open_sto_quantity,0)) AS current_inventory\n", "      FROM base\n", "      INNER JOIN catalog_ c ON c.item_id = base.item_id\n", "      INNER JOIN cumulative_weights cw ON cw.item_id = base.item_id\n", "      AND cw.backend_facility_id = base.be_facility_id \n", "      INNER JOIN active_carts ac on base.fe_outlet_id = ac.outlet_id\n", "\n", "      LEFT JOIN current_fe_inv cfi ON base.fe_outlet_id = cfi.outlet_id\n", "      AND base.item_id = cfi.item_id\n", "      LEFT JOIN sto_open sop ON base.fe_outlet_id = sop.receiver_inv_outlet_id\n", "      AND base.item_id = sop.item_id)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6),\n", "            \n", "final_final AS\n", "  (SELECT be_outlet_id,\n", "          be_facility_id,\n", "          be_name,\n", "          count(DISTINCT CASE\n", "                             WHEN soh_flag = 1 THEN (fe_outlet_id,product_type)\n", "                         END)*100.00/count(DISTINCT (fe_outlet_id,product_type)) AS be_availability,\n", "          count(DISTINCT CASE\n", "                             WHEN fe_flag = 1 THEN (fe_outlet_id,product_type)\n", "                         END)*100.00/count(DISTINCT (fe_outlet_id,product_type)) AS fe_availability\n", "   FROM\n", "     (SELECT be_outlet_id,\n", "             be_facility_id,\n", "             be_name,\n", "             fe_outlet_id,\n", "             product_type,\n", "             CASE\n", "                 WHEN (max_transfer_quantity + current_inventory) > 0 THEN 1\n", "                 ELSE 0\n", "             END AS soh_flag,\n", "             CASE\n", "                 WHEN max_transfer_quantity > 0 THEN 1\n", "                 ELSE 0\n", "             END AS be_flag,\n", "             CASE\n", "                 WHEN current_inventory > 0 THEN 1\n", "                 ELSE 0\n", "             END AS fe_flag\n", "      FROM final_base)\n", "   GROUP BY 1,\n", "            2,\n", "            3)\n", "            \n", "            \n", "SELECT CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "        be_outlet_id,\n", "        be_facility_id,\n", "        be_name,\n", "        be_availability,\n", "        fe_availability,\n", "       be_availability - fe_availability AS be_fe_delta\n", "FROM final_final\n", "ORDER BY 6 DESC\n", "        \"\"\"\n", "    to_trino(bexfe_ptype_avail_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "4f701af0-5560-418f-ab3e-6848e1ca769d", "metadata": {}, "outputs": [], "source": ["def backfill():\n", "\n", "    today = datetime.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=53)\n", "\n", "    for i in range(4):\n", "        backfill_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        print(f\"Backfilling data for: {backfill_date.strftime('%Y-%m-%d')}\")\n", "        # Convert backfill_date into the format 'YYYY-MM-DD'\n", "        formatted_date = backfill_date.strftime(\"%Y-%m-%d\")\n", "\n", "        bexfe_ptype_avail_query = f\"\"\"\n", "WITH fo AS\n", "  (SELECT om.facility_id,\n", "          om.outlet_id AS outlet_id,\n", "          mo_map.frontend_merchant_id AS merchant_id,\n", "          om.outlet_name AS outlet_name,\n", "          rcl.name AS city_name,\n", "          rcs.name AS state\n", "   FROM po.physical_facility_outlet_mapping om\n", "   INNER JOIN retail.console_outlet rco ON rco.id = om.outlet_id\n", "   AND rco.business_type_id IN (7)\n", "   AND rco.company_type_id NOT IN (771)\n", "   AND rco.lake_active_record\n", "   INNER JOIN retail.console_location rcl ON rcl.id = rco.tax_location_id\n", "   AND rcl.lake_active_record\n", "   INNER JOIN dwh.dim_merchant_outlet_facility_mapping mo_map ON mo_map.facility_id = om.facility_id\n", "   AND rco.business_type_id = 7\n", "   AND is_current\n", "   AND is_current_mapping_active\n", "   AND is_backend_merchant_active\n", "   AND is_frontend_merchant_active\n", "   INNER JOIN retail.console_state AS rcs ON rcs.id = rcl.state_id\n", "   AND rcs.lake_active_record\n", "   WHERE om.active = 1\n", "     AND om.ars_active = 1\n", "     AND om.lake_active_record\n", "     AND lower(om.outlet_name) NOT LIKE '%%test%%'\n", "     AND om.outlet_name NOT LIKE '%%Draft%%'\n", "     AND om.outlet_name NOT LIKE '%%Test Store%%'\n", "     AND om.outlet_name NOT LIKE '%%Dummy%%'\n", "     AND om.facility_id != 273 ),\n", "     catalog_ AS\n", "  (SELECT *\n", "   FROM\n", "     (SELECT icd.item_id,\n", "             icd.name,\n", "             icd.l0,\n", "             icd.l1,\n", "             icd.l2,\n", "             icd.product_type,\n", "             CASE\n", "                 WHEN id.perishable = 1 THEN 'PERISHABLE'\n", "                 ELSE 'PACKAGED'\n", "             END AS item_type,\n", "             id.storage_type AS storage_type_raw,\n", "             CASE\n", "                 WHEN id.storage_type IN ('1',\n", "                                          '8',\n", "                                          '11') THEN 'REGULAR'\n", "                 WHEN id.storage_type IN ('4',\n", "                                          '5') THEN 'HEAVY'\n", "                 WHEN id.storage_type IN ('2',\n", "                                          '6') THEN 'COLD'\n", "                 WHEN id.storage_type IN ('3',\n", "                                          '7') THEN 'FROZEN'\n", "                 ELSE 'REGULAR'\n", "             END AS storage_type,\n", "             CASE\n", "                 WHEN id.handling_type IN ('8') THEN 'PACKAGING MATERIAL'\n", "                 WHEN id.handling_type IN ('6') THEN 'MEDICINAL'\n", "                 ELSE 'REGULAR'\n", "             END AS handling_type,\n", "             id.variant_mrp AS mrp,\n", "             id.variant_description,\n", "             id.weight_in_gm,\n", "             id.length_in_cm,\n", "             id.height_in_cm,\n", "             id.breadth_in_cm,\n", "             id.shelf_life,\n", "             coalesce(itf.item_factor, 0.01) AS item_factor,\n", "             DATE_DIFF('DAY',date(icd.created_at), date('{formatted_date}')-INTERVAL '1' DAY) AS item_catalog_age,       --\n", "             rank() OVER (PARTITION BY id.item_id\n", "                          ORDER BY id.id DESC) AS variant_rank,\n", "                         itm.tag_value AS custom_storage_type_raw,\n", "                         CASE\n", "                             WHEN itm.tag_value = '1' THEN 'BEAUTY'\n", "                             WHEN itm.tag_value = '2' THEN 'BOUQUET'\n", "                             WHEN itm.tag_value = '3' THEN 'PREMIUM'\n", "                             WHEN itm.tag_value = '4' THEN 'BOOKS'\n", "                             WHEN itm.tag_value = '5' THEN 'NON_VEG'\n", "                             WHEN itm.tag_value = '6' THEN 'ICE_CREAM'\n", "                             WHEN itm.tag_value = '7' THEN 'TOYS'\n", "                             WHEN itm.tag_value = '8' THEN 'ELECTRONICS' -- when itm.tag_value = '9' then 'FESTIVE'\n", "\n", "                             WHEN itm.tag_value = '10' THEN 'VERTICAL_CHUTES'\n", "                             WHEN itm.tag_value = '11' THEN 'BEST_SERVED_COLD'\n", "                             WHEN itm.tag_value = '12' THEN 'CRITICAL_SKUS'\n", "                             WHEN itm.tag_value = '13' THEN 'LARGE'\n", "                             WHEN itm.tag_value = '14' THEN 'APPAREL'\n", "                             WHEN itm.tag_value = '15' THEN 'SPORTS'\n", "                             WHEN itm.tag_value = '16' THEN 'PET_CARE'\n", "                             WHEN itm.tag_value = '17' THEN 'HOME_DECOR'\n", "                             WHEN itm.tag_value = '18' THEN 'KITCHEN_DINING'\n", "                             WHEN itm.tag_value = '19' THEN 'HOME_FURNISHING'\n", "                             WHEN itm.tag_value = '20' THEN 'LONGTAIL_OTHERS'\n", "                             WHEN itm.tag_value IS NULL THEN 'NO_CONFIG'\n", "                             ELSE 'UNKNOWN_CONFIG'\n", "                         END AS custom_storage_type,\n", "                         pb.manufacturer_id,\n", "                         id.manufacturer AS manufacturer_name,\n", "                         pb.name AS brand_name,\n", "                         id.outer_case_size,\n", "                         id.inner_case_size,\n", "                         CASE\n", "                             WHEN itm2.tag_value = '1' THEN TRUE\n", "                             ELSE FALSE\n", "                         END AS is_high_value,\n", "                         itm2.tag_value AS high_value_tag_raw\n", "      FROM rpc.item_category_details icd\n", "      INNER JOIN rpc.product_product id ON id.item_id = icd.item_id\n", "      AND id.active = 1\n", "      AND id.approved = 1\n", "      AND id.lake_active_record\n", "      LEFT JOIN supply_etls.item_factor itf ON itf.item_id = icd.item_id\n", "      LEFT JOIN rpc.item_tag_mapping itm ON itm.tag_type_id = 7\n", "      AND itm.active = TRUE\n", "      AND itm.lake_active_record\n", "      AND itm.item_id = icd.item_id\n", "      LEFT JOIN rpc.product_brand pb ON id.brand_id = pb.id\n", "      AND pb.lake_active_record\n", "      AND pb.active = 1\n", "      LEFT JOIN rpc.item_tag_mapping itm2 ON itm2.item_id = icd.item_id\n", "      AND itm2.active\n", "      AND itm2.tag_type_id = 3\n", "      AND itm2.lake_active_record\n", "      WHERE icd.lake_active_record\n", "        AND perishable != 1\n", "        AND id.handling_type != '8'\n", "        AND id.storage_type NOT IN ('3',\n", "                                    '7')\n", "        AND icd.l0_id != 1487 -- removing vegetables and fruits\n", "\n", "        AND icd.l0 NOT IN ('wholesale store',\n", "                           'Trial NEW tree',\n", "                           'Specials')-- removing test and flyer/freebie l0s\n", ") AS x\n", "   WHERE variant_rank = 1),\n", "     runs AS\n", "  (SELECT run_id,\n", "          max(started_at) AS started_at\n", "   FROM ars.job_run\n", "   WHERE started_at+interval'5'hour+interval'30'MINUTE BETWEEN date('{formatted_date}')-interval'3'DAY  AND date('{formatted_date}')          -->= tha phle 2 days interval\n", "     AND simulation_params NOT LIKE '%%MODE%%'\n", "     AND json_extract_scalar(simulation_params, '$.run') = 'ars_lite'\n", "     AND simulation_params NOT LIKE '%%hyperpure_run%%'\n", "     AND simulation_params NOT LIKE '%%test%%'\n", "     AND simulation_params NOT LIKE '%%TYPE%%'\n", "     AND simulation_params NOT LIKE '%%MANUAL%%'\n", "     AND simulation_params NOT LIKE '%%any_day_po%%'\n", "     AND simulation_params NOT LIKE '%%spr_migration_simulation%%'\n", "   GROUP BY 1),\n", "     run_outlet_combination AS\n", "  (WITH base AS\n", "     (SELECT run_id,\n", "             CAST(SPLIT(replace(replace(auto_sto_outlets, '[', ''), ']', ''), ',') AS ARRAY<VARCHAR>) AS outlet_ids\n", "      FROM ars.backend_facility_slot_details\n", "      WHERE run_id IN\n", "          (SELECT run_id\n", "           FROM runs)\n", "        AND is_current_slot = TRUE\n", "        AND insert_ds_ist BETWEEN cast(date('{formatted_date}') - INTERVAL '3' DAY AS varchar) AND cast(date('{formatted_date}') as varchar)) SELECT run_id,         --\n", "                                                                                      CAST(NULLIF(outlet_id, '') AS INTEGER) AS outlet_id\n", "   FROM base\n", "   CROSS JOIN UNNEST(outlet_ids) AS t(outlet_id)),\n", "     runs_data AS\n", "  (SELECT fcs.sto_date,\n", "          fcs.from_outlet_id AS be_outlet_id,\n", "          pfom.outlet_name AS be_name,\n", "          pfom.facility_id AS be_facility_id,\n", "          fcs.to_outlet_id AS fe_outlet_id,\n", "          fo.outlet_name AS fe_name,\n", "          fcs.item_id,\n", "          fcs.run_id,\n", "          runs.started_at,\n", "          date_trunc('hour', runs.started_at) AS truncated_run_started_at,\n", "          coalesce(fcs.cycle_start_inventory,0) AS cycle_start_inventory,\n", "          coalesce(fcs.max_transfer_quantity,0) AS max_transfer_quantity,\n", "          pfma.assortment_type,\n", "          coalesce(torv.sto_quantity,0) AS v1,\n", "          coalesce(sto_quantity_post_truncation,0) AS v2,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.inward_drop') AS INTEGER),0) AS inward_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.storage_drop') AS INTEGER),0) AS storage_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.truck_load_drop') AS INTEGER),0) AS truck_load_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_quantity_drop') AS INTEGER),0) AS picking_capacity_quantity_drop,\n", "          coalesce(TRY_CAST(JSON_EXTRACT(quantity_drops, '$.picking_capacity_sku_drop') AS INTEGER),0) AS picking_capacity_sku_drop,\n", "          rank() OVER (PARTITION BY fcs.from_outlet_id,\n", "                                    fcs.to_outlet_id\n", "                       ORDER BY date_trunc('hour', runs.started_at) DESC) AS rank_\n", "   FROM ars.frontend_cycle_sto_quantity fcs\n", "   INNER JOIN runs ON runs.run_id = fcs.run_id\n", "   INNER JOIN run_outlet_combination roc ON roc.run_id = fcs.run_id\n", "   AND roc.outlet_id = fcs.to_outlet_id\n", "   LEFT JOIN ars.transfers_optimization_results_v2 torv ON torv.insert_ds_ist BETWEEN cast(date('{formatted_date}') - interval '2' DAY AS varchar) AND cast(date('{formatted_date}') as varchar) --\n", "   AND fcs.run_id = torv.run_id\n", "   AND fcs.to_outlet_id = torv.frontend_outlet_id\n", "   AND fcs.item_id = torv.item_id\n", "   AND fcs.from_outlet_id = torv.backend_outlet_id\n", "   LEFT JOIN po.physical_facility_outlet_mapping pfom ON pfom.outlet_id = fcs.from_outlet_id\n", "   AND pfom.lake_active_record\n", "   AND pfom.active = 1\n", "   INNER JOIN fo ON fo.outlet_id = fcs.to_outlet_id\n", "   INNER JOIN rpc.product_facility_master_assortment pfma ON pfma.facility_id = fo.facility_id\n", "   AND pfma.item_id = fcs.item_id\n", "   AND pfma.active = 1\n", "   AND pfma.master_assortment_substate_id IN (1)\n", "   AND pfma.lake_active_record\n", "   INNER JOIN catalog_ c ON c.item_id = fcs.item_id\n", "   WHERE fcs.partition_field BETWEEN cast(date('{formatted_date}') - interval '2' DAY AS varchar) AND cast(date('{formatted_date}') as varchar) ),           --\n", "     cpd_data AS\n", "  (SELECT cpd.*\n", "   FROM ars.outlet_item_aps_derived_cpd cpd\n", "   WHERE insert_ds_ist = cast(date('{formatted_date}')-INTERVAL '1' DAY AS varchar)         ---\n", "     AND aps_adjusted > 0\n", "     AND lake_active_record ),\n", "     base AS\n", "  (SELECT rd.*,\n", "          coalesce(c.aps_adjusted, 0.045) AS cpd,\n", "          (coalesce(rd.cycle_start_inventory,0) * 1.0000) / coalesce(c.aps_adjusted, 0.045) AS doi_pre_run,\n", "          ((coalesce(rd.cycle_start_inventory,0) + coalesce(v2, 0)) * 1.0000) / coalesce(c.aps_adjusted, 0.045) AS doi_post_run,\n", "          coalesce(greatest(inward_drop,storage_drop,truck_load_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop),0) AS max_truncation,\n", "          CASE\n", "              WHEN coalesce(rd.cycle_start_inventory,0) = 0\n", "                   AND coalesce(greatest(inward_drop,storage_drop,truck_load_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop),0) = 0\n", "                   AND v2 = 0\n", "                   AND max_transfer_quantity > 0 THEN 1\n", "              ELSE coalesce(greatest(inward_drop,storage_drop,truck_load_drop,picking_capacity_quantity_drop,picking_capacity_sku_drop),0)\n", "          END AS revised_truncation\n", "   FROM runs_data rd\n", "   LEFT JOIN cpd_data c ON c.outlet_id = rd.fe_outlet_id\n", "   AND rd.item_id = c.item_id\n", "   WHERE rank_ = 1 ),\n", "     av_weights AS\n", "  (SELECT cast(av.item_id AS int) AS item_id,\n", "          cast(backend_facility_id AS int) AS backend_facility_id,\n", "          max(cast(weights AS DOUBLE)) AS av_weight\n", "   FROM supply_etls.backend_item_weights av\n", "   INNER JOIN base ON base.be_facility_id = cast(backend_facility_id AS int)\n", "   AND base.item_id = cast(av.item_id AS int)\n", "   WHERE cast(weights AS DOUBLE) > 0\n", "     AND av.assortment_type = 'Packaged Goods'\n", "     AND updated_at =\n", "       (SELECT max(updated_at)\n", "        FROM supply_etls.backend_item_weights)\n", "   GROUP BY 1,\n", "            2),\n", "     normalized_weights AS\n", "  (SELECT item_id,\n", "          backend_facility_id,\n", "          av_weight / SUM(av_weight) OVER (PARTITION BY backend_facility_id) AS normalized_av_weight\n", "   FROM av_weights\n", "   ORDER BY 3 DESC),\n", "     cumulative_weights AS\n", "  (SELECT item_id,\n", "          backend_facility_id,\n", "          normalized_av_weight,\n", "          SUM(normalized_av_weight) OVER (PARTITION BY backend_facility_id\n", "                                          ORDER BY normalized_av_weight DESC) AS cumulative_weight\n", "   FROM normalized_weights),\n", "   \n", "current_fe_inv AS\n", "  (SELECT try_cast(ii.item_id AS int) AS item_id,\n", "          try_cast(ii.outlet_id AS int) AS outlet_id,\n", "          sum(quantity - coalesce(blocked_inv, 0)) AS inv,\n", "          coalesce(max(itf.item_factor), 1) AS item_factor\n", "   FROM dynamodb.blinkit_store_inventory_service_oi_rt_view AS ii\n", "   LEFT JOIN supply_etls.item_factor itf ON cast(itf.item_id AS varchar) = ii.item_id\n", "   LEFT JOIN\n", "     (SELECT item_id,\n", "             outlet_id,\n", "             sum(quantity) blocked_inv\n", "      FROM dynamodb.blinkit_store_inventory_service_blk_rt_view\n", "      WHERE status = 'BLOCKED'\n", "        AND reference_type NOT IN ('DL_VALIDATION_CRON',\n", "                                   'PNA')\n", "      GROUP BY 1,\n", "               2) ib ON ib.item_id = ii.item_id\n", "   AND ib.outlet_id = ii.outlet_id\n", "   WHERE ii.state = 'GOOD'\n", "   GROUP BY 1,\n", "            2),\n", "            \n", "sto_open AS\n", "  (SELECT item_id,\n", "          receiver_inv_outlet_id,\n", "          sum(open_sto_quantity) AS open_sto_quantity,\n", "          sum(billed_sto_quantity) AS billed_sto_quantity\n", "   FROM supply_etls.inventory_metrics_open_sto\n", "   WHERE sto_status = 'open'\n", "   GROUP BY 1,\n", "            2),\n", "     active_carts AS\n", "  (SELECT outlet_id,\n", "          count(DISTINCT order_id) orders\n", "   FROM dwh.fact_sales_order_details\n", "   WHERE order_create_dt_ist BETWEEN date('{formatted_date}') - interval '3' DAY AND date('{formatted_date}') ---\n", "   GROUP BY 1\n", "   HAVING count(DISTINCT order_id) > 50),\n", "   \n", "final_base AS\n", "  (SELECT be_outlet_id,\n", "          be_facility_id,\n", "          be_name,\n", "          fe_outlet_id,\n", "          fe_name,\n", "          product_type,\n", "          sum(max_transfer_quantity) max_transfer_quantity,\n", "          sum(current_inventory) current_inventory\n", "   FROM\n", "     (SELECT base.*,\n", "             CASE\n", "                 WHEN inward_drop = revised_truncation THEN 'inward_drop'\n", "                 WHEN storage_drop = revised_truncation THEN 'storage_drop'\n", "                 WHEN truck_load_drop = revised_truncation THEN 'truck_load_drop'\n", "                 WHEN picking_capacity_quantity_drop = revised_truncation THEN 'picking_capacity_quantity_drop'\n", "                 WHEN picking_capacity_sku_drop = revised_truncation THEN 'picking_capacity_sku_drop'\n", "                 ELSE 'need_to_check_truncation - possibly be oos or thin dois'\n", "             END AS truncation_reason,\n", "             c.name,\n", "             c.l0,\n", "             c.product_type,\n", "             c.item_factor,\n", "             c.storage_type,\n", "             c.custom_storage_type,\n", "             cw.normalized_av_weight,\n", "             (coalesce(cfi.inv,0) + coalesce(sop.open_sto_quantity,0)) AS current_inventory\n", "      FROM base\n", "      INNER JOIN catalog_ c ON c.item_id = base.item_id\n", "      INNER JOIN cumulative_weights cw ON cw.item_id = base.item_id\n", "      AND cw.backend_facility_id = base.be_facility_id \n", "      INNER JOIN active_carts ac on base.fe_outlet_id = ac.outlet_id\n", "\n", "      LEFT JOIN current_fe_inv cfi ON base.fe_outlet_id = cfi.outlet_id\n", "      AND base.item_id = cfi.item_id\n", "      LEFT JOIN sto_open sop ON base.fe_outlet_id = sop.receiver_inv_outlet_id\n", "      AND base.item_id = sop.item_id)\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4,\n", "            5,\n", "            6),\n", "            \n", "final_final AS\n", "  (SELECT be_outlet_id,\n", "          be_facility_id,\n", "          be_name,\n", "          count(DISTINCT CASE\n", "                             WHEN soh_flag = 1 THEN (fe_outlet_id,product_type)\n", "                         END)*100.00/count(DISTINCT (fe_outlet_id,product_type)) AS be_availability,\n", "          count(DISTINCT CASE\n", "                             WHEN fe_flag = 1 THEN (fe_outlet_id,product_type)\n", "                         END)*100.00/count(DISTINCT (fe_outlet_id,product_type)) AS fe_availability\n", "   FROM\n", "     (SELECT be_outlet_id,\n", "             be_facility_id,\n", "             be_name,\n", "             fe_outlet_id,\n", "             product_type,\n", "             CASE\n", "                 WHEN (max_transfer_quantity + current_inventory) > 0 THEN 1\n", "                 ELSE 0\n", "             END AS soh_flag,\n", "             CASE\n", "                 WHEN max_transfer_quantity > 0 THEN 1\n", "                 ELSE 0\n", "             END AS be_flag,\n", "             CASE\n", "                 WHEN current_inventory > 0 THEN 1\n", "                 ELSE 0\n", "             END AS fe_flag\n", "      FROM final_base)\n", "   GROUP BY 1,\n", "            2,\n", "            3)\n", "SELECT date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "        be_outlet_id,\n", "        be_facility_id,\n", "        be_name,\n", "        be_availability,\n", "        fe_availability,\n", "       be_availability - fe_availability AS be_fe_delta\n", "FROM final_final\n", "ORDER BY 6 DESC\n", "        \"\"\"\n", "\n", "        to_trino(\n", "            bexfe_ptype_avail_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs\n", "        )\n", "\n", "        print(f\"Backfilled data for: {backfill_date.strftime('%Y-%m-%d')}\")"]}, {"cell_type": "code", "execution_count": null, "id": "8746fc32-6457-4123-b6f9-54d203781601", "metadata": {}, "outputs": [], "source": ["if backfill_flag == 0:\n", "    normal()\n", "else:\n", "    backfill()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}