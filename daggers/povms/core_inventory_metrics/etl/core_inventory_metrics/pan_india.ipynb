{"cells": [{"cell_type": "code", "execution_count": null, "id": "3e3fd5ec-90ea-43d7-85ab-835ce19c9a1c", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "e657c13a-2938-483b-b7f6-12997f292832", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "584d50dd-db13-42c6-9f2e-43e97b0e9d5d", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "635ca1fc-2160-48a9-8677-d6c46291b7c6", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "dc0c3e5b-6b5a-4ed5-a470-2880e7ee94c1", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"City ID\"},\n", "    {\n", "        \"name\": \"city_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"City Name\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Backend Facility ID\"},\n", "    {\"name\": \"be_inv_outlet_id\", \"type\": \"integer\", \"description\": \"Backend Inventory Outlet ID\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"Backend Name\"},\n", "    {\"name\": \"no_of_ds_live\", \"type\": \"integer\", \"description\": \"Count of Dark Stores live\"},\n", "    {\n", "        \"name\": \"no_of_ds_live_express\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of Express Dark Stores live\",\n", "    },\n", "    {\n", "        \"name\": \"no_of_ds_live_longtail\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of LongTail Dark Stores live\",\n", "    },\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"Dark Store Facility ID\"},\n", "    {\n", "        \"name\": \"fe_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store Inventory Outlet ID\",\n", "    },\n", "    {\"name\": \"fe_facility_name\", \"type\": \"varchar\", \"description\": \"Dark Store Facility Name\"},\n", "    {\"name\": \"total_loss\", \"type\": \"real\", \"description\": \"GMV Loss\"},\n", "    {\"name\": \"city_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_indent\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_indent_wt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_total_con_cost\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_per_unit_total_con_cost\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"esto_excess_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"esto_excess_value\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"sto_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"v1_line_items\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"v2_line_items\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"potential_order_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_qty_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_qty_cap\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_qty_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_sku_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_sku_cap\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_sku_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inward_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inward_cap_in_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inward_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"truck_load_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"truck_load_cap_in_kg\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"truck_load_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_fr\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dispatch_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_created_qty_FE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_picked_qty_FE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_packaged_qty_FE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_sorted_qty_FE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_dispatched_qty_FE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_created_qty_BE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_picked_qty_BE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_packaged_qty_BE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_sorted_qty_BE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_dispatched_qty_BE\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"indent_raised_fotif\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"total_trips\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"perfect_trips\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_vehicle_report\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_load_start\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_dispatch\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_ds_report\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_ds_for_indent\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"early_ds_report\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"early_ds_report_for_indent\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"sto_raised_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_grn_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"delayed_grn_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cpd\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_weight\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cpd\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_weight\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_cpd\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_weight\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_qty_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dispatch_qty_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_qty_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"sto_raised_qty_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_active_skus_fe\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_temp_inactive_skus_fe\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lines_active\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lines_temp_inactive_skus\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_active_skus_be\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_inactive_skus_be\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"planned_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"delayed_grn_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_grn_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"avg_out_of_stock_duration\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"avg_out_of_stock_duration_weighted\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"planned_quantity_be_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_orders\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"total_dn_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_short_qty\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_damage_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"transfer_loss_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"transfer_loss_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"total_dn_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_short_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_damage_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_excess_item_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_excess_item_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_exp_nte_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_exp_nte_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_freebie_missing_qty\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_freebie_missing_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_misc_qty\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_misc_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_quality_issue_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_quality_issue_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_upc_not_scannable_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_upc_not_scannable_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_variant_mismatch_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_variant_mismatch_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_wrong_item_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_wrong_item_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_positive_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_positive_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_negative_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_negative_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_active_skus_pan\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_inactive_skus_pan\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_hybrid_stores\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_express_only\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_longtail_only\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"b2b_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"rsto_dump_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"net_inv_present_fe\", \"type\": \"real\", \"description\": \"Net Inventory\"},\n", "    {\"name\": \"net_inv_exposure_fe\", \"type\": \"real\", \"description\": \"Inventory Value\"},\n", "    {\"name\": \"net_inv_volume_fe\", \"type\": \"real\", \"description\": \"Inventory Volume\"},\n", "    {\"name\": \"doi_sto_cpd_basis_fe\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"doi_po_cpd_basis_fe\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"sto_cpd_fe\", \"type\": \"real\", \"description\": \"STO Consumption per day\"},\n", "    {\"name\": \"po_cpd_fe\", \"type\": \"real\", \"description\": \"PO Consumption per day\"},\n", "    {\"name\": \"excess_inv_fe\", \"type\": \"real\", \"description\": \"Excess inventory\"},\n", "    {\n", "        \"name\": \"excess_inv_exposure_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Excess inventory Exposure\",\n", "    },\n", "    {\"name\": \"excess_inv_volume_fe\", \"type\": \"real\", \"description\": \"Excess inventory Volume\"},\n", "    {\"name\": \"no_sale_inv_fe\", \"type\": \"real\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_exposure_fe\", \"type\": \"real\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_volume_fe\", \"type\": \"real\", \"description\": \"No sale Inventory\"},\n", "    {\n", "        \"name\": \"cpd_depleted_inv_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_exposure_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_volume_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_exposure_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_volume_fe\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\"name\": \"overall_dead_fe\", \"type\": \"real\", \"description\": \"overall dead Inventory\"},\n", "    {\"name\": \"overall_dead_exposure_fe\", \"type\": \"real\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"overall_dead_volume_fe\", \"type\": \"real\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"net_inv_present_be\", \"type\": \"real\", \"description\": \"Net Inventory\"},\n", "    {\"name\": \"net_inv_exposure_be\", \"type\": \"real\", \"description\": \"Inventory Value\"},\n", "    {\"name\": \"net_inv_volume_be\", \"type\": \"real\", \"description\": \"Inventory Volume\"},\n", "    {\"name\": \"doi_sto_cpd_basis_be\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"doi_po_cpd_basis_be\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"sto_cpd_be\", \"type\": \"real\", \"description\": \"STO Consumption per day\"},\n", "    {\"name\": \"po_cpd_be\", \"type\": \"real\", \"description\": \"PO Consumption per day\"},\n", "    {\"name\": \"excess_inv_be\", \"type\": \"real\", \"description\": \"Excess inventory\"},\n", "    {\n", "        \"name\": \"excess_inv_exposure_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Excess inventory Exposure\",\n", "    },\n", "    {\"name\": \"excess_inv_volume_be\", \"type\": \"real\", \"description\": \"Excess inventory Volume\"},\n", "    {\"name\": \"no_sale_inv_be\", \"type\": \"real\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_exposure_be\", \"type\": \"real\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_volume_be\", \"type\": \"real\", \"description\": \"No sale Inventory\"},\n", "    {\n", "        \"name\": \"cpd_depleted_inv_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_exposure_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_volume_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_exposure_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_volume_be\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\"name\": \"overall_dead_be\", \"type\": \"real\", \"description\": \"overall dead Inventory\"},\n", "    {\"name\": \"overall_dead_exposure_be\", \"type\": \"real\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"overall_dead_volume_be\", \"type\": \"real\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"tea_coverage\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_be_availability_ptype\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_fe_availability_ptype\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_availability_ptype\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_ds_for_indent_grn\", \"type\": \"real\", \"description\": \"Sample\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ec431776-5fca-43ac-89e8-ca46cc169555", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inventory_core_metrics_pan_india_v5\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"be_facility_id\",\n", "        \"fe_facility_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"dt_hour\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains PAN India Inventory Core Metrics for Movement and Replenishment\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "e33b88de-3ab7-4932-a248-19f34a56003d", "metadata": {}, "outputs": [], "source": ["backfill_flag = 0\n", "\n", "\n", "def normal():\n", "    inventory_metrics_query = f\"\"\"\n", " WITH ds_pan_india as(\n", "    select CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "    '.PAN India' as facility_name,\n", "    0 as facility_id,\n", "    count(distinct tea.fe_facility_id) as no_of_ds_live,\n", "    count(distinct case when tea.store_assortment_type = 'EXPRESS' then tea.fe_facility_id end) as no_of_ds_live_express,\n", "    count(distinct case when tea.store_assortment_type = 'LONGTAIL' then tea.fe_facility_id end) as no_of_ds_live_longtail\n", "    from supply_etls.outlet_details od\n", "    left join supply_etls.inventory_metrics_tea_tagging tea on tea.be_facility_id = od.facility_id\n", "    where od.ars_check=1 \n", "        and od.taggings = 'be' \n", "        and (od.grocery_active_count >= 1 or od.facility_id = 2576) \n", "        and od.store_type = 'Packaged Goods'\n", "        and tea.flag = 'correct_tea' and tea.be_facility_id <> tea.fe_facility_id\n", "        and tea.assortment_status_id in (1,3)\n", "),\n", "\n", "ds_pan_india_detailed as(\n", "select CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "        sum(case when exp = 1 and lt=1 THEN 1 END) as count_hybrid_stores,\n", "        sum(case when exp = 1 and lt=0 THEN 1 END) as count_express_only,\n", "        sum(case when exp = 0 and lt=1 THEN 1 END) as count_longtail_only\n", "    from(\n", "        select DISTINCT fe_facility_id,\n", "                    sum(case when store_assortment_type = 'EXPRESS' then 1 else 0 end) as exp,\n", "                    sum(case when store_assortment_type = 'LONGTAIL' then 1 else 0 end) as lt\n", "                        from\n", "                            (select DISTINCT tea.fe_facility_id,tea.store_assortment_type\n", "                            from supply_etls.outlet_details od\n", "                            left join supply_etls.inventory_metrics_tea_tagging tea on tea.be_facility_id = od.facility_id\n", "                            where od.ars_check=1 \n", "                                and od.taggings = 'be' \n", "                                and (od.grocery_active_count >= 1 or od.facility_id = 2576) \n", "                                and od.store_type = 'Packaged Goods'\n", "                                and tea.flag = 'correct_tea' and tea.be_facility_id <> tea.fe_facility_id\n", "                                and tea.assortment_status_id in (1,3)\n", "                              )\n", "                              group by 1\n", "    )\n", "    group by 1\n", "),\n", "\n", "tea_tagging_item AS (\n", "    SELECT  \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, store_assortment_type,\n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id, fe_city_id, fe_city_name, assortment_status_id\n", "    FROM supply_etls.inventory_metrics_tea_tagging\n", "    WHERE flag = 'correct_tea' AND be_facility_id <> fe_facility_id\n", "),\n", "\n", "sku_complexity_pan_india as ( \n", "    select CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "        sum(case when master_assortment_substate_id = 1 THEN 1 END) as count_active_skus_pan,\n", "        sum(case when master_assortment_substate_id = 3 THEN 1 END) as count_inactive_skus_pan\n", "\n", "    from(\n", "        select DISTINCT item_id,\n", "            case when sum(case when master_assortment_substate_id = 1 then 1 else 0 end) >= 1 then 1\n", "                when sum(case when master_assortment_substate_id = 3 then 1 else 0 end) >= 1 then 3\n", "                end as master_assortment_substate_id\n", "        from(\n", "            select tea.item_id, tea.be_facility_id, tea.fe_facility_id, master_assortment_substate_id\n", "            from tea_tagging_item tea\n", "            join rpc.product_facility_master_assortment ma on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "    )    \n", "    group by 1)\n", "    group by 1\n", "),\n", "\n", "dau_breakup as(\n", "    select * from supply_etls.dau_coverage\n", "    where insert_ds_ist=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "city_overall as(\n", "    select DISTINCT \n", "        date_,\n", "        city_id,\n", "        city_weight*city_active_express_weight_avail as city_active_express_weight_avail,\n", "        city_weight*city_active_express_binary_avail as city_active_express_binary_avail,\n", "        city_weight*city_active_longtail_weight_avail as city_active_longtail_weight_avail,\n", "        city_weight*city_active_longtail_binary_avail as city_active_longtail_binary_avail,\n", "        city_weight*city_temp_inactive_express_weight_avail as city_temp_inactive_express_weight_avail,\n", "        city_weight*city_temp_inactive_express_binary_avail as city_temp_inactive_express_binary_avail,\n", "        city_weight*city_temp_inactive_longtail_weight_avail as city_temp_inactive_longtail_weight_avail,\n", "        city_weight*city_temp_inactive_longtail_binary_avail as city_temp_inactive_longtail_binary_avail,\n", "        city_weight*city_overall_overall_weight_avail as city_overall_overall_weight_avail,\n", "        city_weight*city_overall_overall_binary_avail as city_overall_overall_binary_avail\n", "        from supply_etls.inventory_core_metrics_v6 icm\n", "        where icm.date_=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "city_overall_final as(\n", "    select date_,\n", "    SUM(city_active_express_weight_avail) as city_active_express_weight_avail,\n", "    SUM(city_active_express_binary_avail) as city_active_express_binary_avail,    \n", "    SUM(city_active_longtail_weight_avail) as city_active_longtail_weight_avail,\n", "    SUM(city_active_longtail_binary_avail) as city_active_longtail_binary_avail,\n", "    SUM(city_temp_inactive_express_weight_avail) as city_temp_inactive_express_weight_avail,\n", "    SUM(city_temp_inactive_express_binary_avail) as city_temp_inactive_express_binary_avail,\n", "    SUM(city_temp_inactive_longtail_weight_avail) as city_temp_inactive_longtail_weight_avail,\n", "    SUM(city_temp_inactive_longtail_binary_avail) as city_temp_inactive_longtail_binary_avail,\n", "    SUM(city_overall_overall_weight_avail) as city_overall_overall_weight_avail,\n", "    SUM(city_overall_overall_binary_avail) as city_overall_overall_binary_avail\n", "    from city_overall\n", "    group by 1\n", "),\n", "\n", "fe_overall as(\n", "select DISTINCT\n", "    date_,\n", "    fe_facility_id,\n", "    current_inventory,\n", "    planned_quantity,\n", "    qty_sold,\n", "    potential_order_quantity,\n", "    planned_quantity_be_avail,\n", "    count_orders,\n", "    gmv,\n", "    fe_regular_storage_capacity,\n", "    fe_regular_onshelf_inventory,\n", "    fe_regular_in_transit_quantity,\n", "    fe_regular_scaled_onshelf_inventory,\n", "    fe_regular_scaled_open_po_qty,\n", "    fe_regular_onshelf_utilisation,\n", "    fe_regular_utilisation_with_open_stos,\n", "    fe_cold_storage_capacity,\n", "    fe_cold_onshelf_inventory,\n", "    fe_cold_in_transit_quantity,\n", "    fe_cold_scaled_onshelf_inventory,\n", "    fe_cold_scaled_open_po_qty,\n", "    fe_cold_onshelf_utilisation,\n", "    fe_cold_utilisation_with_open_stos,\n", "    fe_heavy_storage_capacity,\n", "    fe_heavy_onshelf_inventory,\n", "    fe_heavy_in_transit_quantity,\n", "    fe_heavy_scaled_onshelf_inventory,\n", "    fe_heavy_scaled_open_po_qty,\n", "    fe_heavy_onshelf_utilisation,\n", "    fe_heavy_utilisation_with_open_stos,\n", "    fe_frozen_storage_capacity,\n", "    fe_frozen_onshelf_inventory,\n", "    fe_frozen_in_transit_quantity,\n", "    fe_frozen_scaled_onshelf_inventory,\n", "    fe_frozen_scaled_open_po_qty,\n", "    fe_frozen_onshelf_utilisation,\n", "    fe_frozen_utilisation_with_open_stos,\n", "    \n", "    fe_weight*fe_active_express_weight_avail AS fe_active_express_weight_avail,\n", "    fe_weight*fe_active_express_binary_avail AS fe_active_express_binary_avail,\n", "    fe_weight*fe_active_longtail_weight_avail AS fe_active_longtail_weight_avail,\n", "    fe_weight*fe_active_longtail_binary_avail AS fe_active_longtail_binary_avail,\n", "    fe_weight*fe_temp_inactive_express_weight_avail AS fe_temp_inactive_express_weight_avail,\n", "    fe_weight*fe_temp_inactive_express_binary_avail AS fe_temp_inactive_express_binary_avail,\n", "    fe_weight*fe_temp_inactive_longtail_weight_avail AS fe_temp_inactive_longtail_weight_avail,\n", "    fe_weight*fe_temp_inactive_longtail_binary_avail AS fe_temp_inactive_longtail_binary_avail,\n", "    fe_weight*fe_overall_overall_weight_avail AS fe_overall_overall_weight_avail,\n", "    fe_weight*fe_overall_overall_binary_avail AS fe_overall_overall_binary_avail,\n", "    \n", "    fe_weight*fe_availability_ptype AS fe_availability_ptype,\n", "    \n", "    \n", "    avg_out_of_stock_duration,\n", "    avg_out_of_stock_duration_weighted,\n", "    \n", "    net_inv_present_fe,\n", "    net_inv_exposure_fe,\n", "    net_inv_volume_fe,\n", "    doi_sto_cpd_basis_fe,\n", "    doi_po_cpd_basis_fe,\n", "    sto_cpd_fe,\n", "    po_cpd_fe,\n", "    excess_inv_fe,\n", "    excess_inv_exposure_fe,\n", "    excess_inv_volume_fe,\n", "    no_sale_inv_fe,\n", "    no_sale_inv_exposure_fe,\n", "    no_sale_inv_volume_fe,\n", "    cpd_depleted_inv_fe,\n", "    cpd_depleted_inv_exposure_fe,\n", "    cpd_depleted_inv_volume_fe,\n", "    expiring_inv_fe,\n", "    expiring_inv_exposure_fe,\n", "    expiring_inv_volume_fe,\n", "    overall_dead_fe,\n", "    overall_dead_exposure_fe,\n", "    overall_dead_volume_fe\n", "    \n", "    from supply_etls.inventory_core_metrics_v6 icm\n", "    where icm.date_=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "fe_overall_final as(\n", "    select date_,\n", "    SUM(current_inventory) as current_inventory,\n", "    SUM(planned_quantity) as planned_quantity,\n", "    SUM(qty_sold) as qty_sold,\n", "    SUM(potential_order_quantity) as potential_order_quantity,\n", "    SUM(planned_quantity_be_avail) as planned_quantity_be_avail,\n", "    SUM(count_orders) as count_orders,\n", "    SUM(gmv) as gmv,\n", "    SUM(fe_regular_storage_capacity) as fe_regular_storage_capacity,\n", "    SUM(fe_regular_onshelf_inventory) as fe_regular_onshelf_inventory,\n", "    SUM(fe_regular_in_transit_quantity) as fe_regular_in_transit_quantity,\n", "    SUM(fe_regular_scaled_onshelf_inventory) as fe_regular_scaled_onshelf_inventory,\n", "    AVG(fe_regular_onshelf_utilisation) as fe_regular_onshelf_utilisation,\n", "    SUM(fe_regular_scaled_open_po_qty) as fe_regular_scaled_open_po_qty,\n", "    AVG(fe_regular_utilisation_with_open_stos) as fe_regular_utilisation_with_open_stos,\n", "    SUM(fe_cold_storage_capacity) as fe_cold_storage_capacity,\n", "    SUM(fe_cold_onshelf_inventory) as fe_cold_onshelf_inventory,\n", "    SUM(fe_cold_in_transit_quantity) as fe_cold_in_transit_quantity,\n", "    SUM(fe_cold_scaled_onshelf_inventory) as fe_cold_scaled_onshelf_inventory,\n", "    SUM(fe_cold_scaled_open_po_qty) as fe_cold_scaled_open_po_qty,\n", "    AVG(fe_cold_onshelf_utilisation) as fe_cold_onshelf_utilisation,\n", "    AVG(fe_cold_utilisation_with_open_stos) as fe_cold_utilisation_with_open_stos,\n", "    SUM(fe_heavy_storage_capacity) as fe_heavy_storage_capacity,\n", "    SUM(fe_heavy_onshelf_inventory) as fe_heavy_onshelf_inventory,\n", "    SUM(fe_heavy_in_transit_quantity) as fe_heavy_in_transit_quantity,\n", "    SUM(fe_heavy_scaled_onshelf_inventory) as fe_heavy_scaled_onshelf_inventory,\n", "    SUM(fe_heavy_scaled_open_po_qty) as fe_heavy_scaled_open_po_qty,\n", "    AVG(fe_heavy_onshelf_utilisation) as fe_heavy_onshelf_utilisation,\n", "    AVG(fe_heavy_utilisation_with_open_stos) as fe_heavy_utilisation_with_open_stos,\n", "    SUM(fe_frozen_storage_capacity) as fe_frozen_storage_capacity,\n", "    SUM(fe_frozen_onshelf_inventory) as fe_frozen_onshelf_inventory,\n", "    SUM(fe_frozen_in_transit_quantity) as fe_frozen_in_transit_quantity,\n", "    SUM(fe_frozen_scaled_onshelf_inventory) as fe_frozen_scaled_onshelf_inventory,\n", "    SUM(fe_frozen_scaled_open_po_qty) as fe_frozen_scaled_open_po_qty,\n", "    AVG(fe_frozen_onshelf_utilisation) as fe_frozen_onshelf_utilisation,\n", "    AVG(fe_frozen_utilisation_with_open_stos) as fe_frozen_utilisation_with_open_stos,\n", "    \n", "    SUM(fe_active_express_weight_avail) AS fe_active_express_weight_avail,\n", "    SUM(fe_active_express_binary_avail) AS fe_active_express_binary_avail,\n", "    SUM(fe_active_longtail_weight_avail) AS fe_active_longtail_weight_avail,\n", "    SUM(fe_active_longtail_binary_avail) AS fe_active_longtail_binary_avail,\n", "    SUM(fe_temp_inactive_express_weight_avail) AS fe_temp_inactive_express_weight_avail,\n", "    SUM(fe_temp_inactive_express_binary_avail) AS fe_temp_inactive_express_binary_avail,\n", "    SUM(fe_temp_inactive_longtail_weight_avail) AS fe_temp_inactive_longtail_weight_avail,\n", "    SUM(fe_temp_inactive_longtail_binary_avail) AS fe_temp_inactive_longtail_binary_avail,\n", "    SUM(fe_overall_overall_weight_avail) AS fe_overall_overall_weight_avail,\n", "    SUM(fe_overall_overall_binary_avail) AS fe_overall_overall_binary_avail,\n", "    \n", "    SUM(fe_availability_ptype) AS fe_availability_ptype,\n", "    \n", "    AVG(avg_out_of_stock_duration) as avg_out_of_stock_duration,\n", "    AVG(avg_out_of_stock_duration_weighted) as avg_out_of_stock_duration_weighted,\n", "    \n", "    SUM(net_inv_present_fe) as net_inv_present_fe, \n", "    SUM(net_inv_exposure_fe) as net_inv_exposure_fe,\n", "    SUM(net_inv_volume_fe) as net_inv_volume_fe,\n", "    AVG(doi_sto_cpd_basis_fe) as doi_sto_cpd_basis_fe,\n", "    AVG(doi_po_cpd_basis_fe) as doi_po_cpd_basis_fe,\n", "    SUM(sto_cpd_fe) as sto_cpd_fe,\n", "    SUM(po_cpd_fe) as po_cpd_fe,\n", "    SUM(excess_inv_fe) as excess_inv_fe,\n", "    SUM(excess_inv_exposure_fe) as excess_inv_exposure_fe,\n", "    SUM(excess_inv_volume_fe) as excess_inv_volume_fe,\n", "    SUM(no_sale_inv_fe) as no_sale_inv_fe,\n", "    SUM(no_sale_inv_exposure_fe) as no_sale_inv_exposure_fe,\n", "    SUM(no_sale_inv_volume_fe) as no_sale_inv_volume_fe,\n", "    SUM(cpd_depleted_inv_fe) as cpd_depleted_inv_fe,\n", "    SUM(cpd_depleted_inv_exposure_fe) as cpd_depleted_inv_exposure_fe,\n", "    SUM(cpd_depleted_inv_volume_fe) as cpd_depleted_inv_volume_fe,\n", "    SUM(expiring_inv_fe) as expiring_inv_fe,\n", "    SUM(expiring_inv_exposure_fe) as expiring_inv_exposure_fe,\n", "    SUM(expiring_inv_volume_fe) as expiring_inv_volume_fe,\n", "    SUM(overall_dead_fe) as overall_dead_fe,\n", "    SUM(overall_dead_exposure_fe) as overall_dead_exposure_fe,\n", "    SUM(overall_dead_volume_fe) as overall_dead_volume_fe\n", "\n", "    from fe_overall\n", "    group by 1\n", "),\n", "\n", "be_overall as(\n", "select DISTINCT\n", "    CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "    be_facility_id,\n", "    picking_qty_util,\n", "    picking_qty_cap,\n", "    picking_qty_util_per,\n", "    picking_sku_util,\n", "    picking_sku_cap,\n", "    picking_sku_util_per,\n", "    be_regular_storage_capacity,\n", "    be_regular_onshelf_inventory,\n", "    be_regular_in_transit_quantity,\n", "    be_regular_scaled_onshelf_inventory,\n", "    be_regular_onshelf_utilisation,\n", "    be_regular_scaled_open_po_qty,\n", "    be_regular_utilisation_with_open_stos,\n", "    be_cold_storage_capacity,\n", "    be_cold_onshelf_inventory,\n", "    be_cold_in_transit_quantity,\n", "    be_cold_scaled_onshelf_inventory,\n", "    be_cold_scaled_open_po_qty,\n", "    be_cold_onshelf_utilisation,\n", "    be_cold_utilisation_with_open_stos,\n", "    be_heavy_storage_capacity,\n", "    be_heavy_onshelf_inventory,\n", "    be_heavy_in_transit_quantity,\n", "    be_heavy_scaled_onshelf_inventory,\n", "    be_heavy_scaled_open_po_qty,\n", "    be_heavy_onshelf_utilisation,\n", "    be_heavy_utilisation_with_open_stos,\n", "    be_frozen_storage_capacity,\n", "    be_frozen_onshelf_inventory,\n", "    be_frozen_in_transit_quantity,\n", "    be_frozen_scaled_onshelf_inventory,\n", "    be_frozen_scaled_open_po_qty,\n", "    be_frozen_onshelf_utilisation,\n", "    be_frozen_utilisation_with_open_stos,\n", "    wotif_created_qty_FE,\n", "    wotif_on_time_picked_qty_FE,\n", "    wotif_on_time_packaged_qty_FE,\n", "    wotif_on_time_sorted_qty_FE,\n", "    wotif_on_time_dispatched_qty_FE,\n", "    wotif_created_qty_BE,\n", "    wotif_on_time_picked_qty_BE,\n", "    wotif_on_time_packaged_qty_BE,\n", "    wotif_on_time_sorted_qty_BE,\n", "    wotif_on_time_dispatched_qty_BE,\n", "    \n", "    be_weight*be_active_express_weight_avail AS be_active_express_weight_avail,\n", "    be_weight*be_active_express_binary_avail AS be_active_express_binary_avail,\n", "    be_weight*be_active_longtail_weight_avail AS be_active_longtail_weight_avail,\n", "    be_weight*be_active_longtail_binary_avail AS be_active_longtail_binary_avail,\n", "    be_weight*be_temp_inactive_express_weight_avail AS be_temp_inactive_express_weight_avail,\n", "    be_weight*be_temp_inactive_express_binary_avail AS be_temp_inactive_express_binary_avail,\n", "    be_weight*be_temp_inactive_longtail_weight_avail AS be_temp_inactive_longtail_weight_avail,\n", "    be_weight*be_temp_inactive_longtail_binary_avail AS be_temp_inactive_longtail_binary_avail,\n", "    be_weight*be_overall_overall_weight_avail AS be_overall_overall_weight_avail,\n", "    be_weight*be_overall_overall_binary_avail AS be_overall_overall_binary_avail,\n", "    \n", "    be_weight*bexfe_active_express_weight_avail AS bexfe_active_express_weight_avail,\n", "    be_weight*bexfe_active_express_binary_avail AS bexfe_active_express_binary_avail,\n", "    be_weight*bexfe_active_longtail_weight_avail AS bexfe_active_longtail_weight_avail,\n", "    be_weight*bexfe_active_longtail_binary_avail AS bexfe_active_longtail_binary_avail,\n", "    be_weight*bexfe_temp_inactive_express_weight_avail AS bexfe_temp_inactive_express_weight_avail,\n", "    be_weight*bexfe_temp_inactive_express_binary_avail AS bexfe_temp_inactive_express_binary_avail,\n", "    be_weight*bexfe_temp_inactive_longtail_weight_avail AS bexfe_temp_inactive_longtail_weight_avail,\n", "    be_weight*bexfe_temp_inactive_longtail_binary_avail AS bexfe_temp_inactive_longtail_binary_avail,\n", "    be_weight*bexfe_overall_overall_weight_avail AS bexfe_overall_overall_weight_avail,\n", "    be_weight*bexfe_overall_overall_binary_avail AS bexfe_overall_overall_binary_avail,\n", "    \n", "    be_weight*be_fe_active_express_weight_avail AS be_fe_active_express_weight_avail,\n", "    be_weight*be_fe_active_express_binary_avail AS be_fe_active_express_binary_avail,\n", "    be_weight*be_fe_active_longtail_weight_avail AS be_fe_active_longtail_weight_avail,\n", "    be_weight*be_fe_active_longtail_binary_avail AS be_fe_active_longtail_binary_avail,\n", "    be_weight*be_fe_temp_inactive_express_weight_avail AS be_fe_temp_inactive_express_weight_avail,\n", "    be_weight*be_fe_temp_inactive_express_binary_avail AS be_fe_temp_inactive_express_binary_avail,\n", "    be_weight*be_fe_temp_inactive_longtail_weight_avail AS be_fe_temp_inactive_longtail_weight_avail,\n", "    be_weight*be_fe_temp_inactive_longtail_binary_avail AS be_fe_temp_inactive_longtail_binary_avail,\n", "    be_weight*be_fe_overall_overall_weight_avail AS be_fe_overall_overall_weight_avail,\n", "    be_weight*be_fe_overall_overall_binary_avail AS be_fe_overall_overall_binary_avail,\n", "    \n", "    be_weight*bexfe_be_availability_ptype AS bexfe_be_availability_ptype,\n", "    be_weight*bexfe_fe_availability_ptype AS bexfe_fe_availability_ptype,\n", "    \n", "    lines_active,\n", "    lines_temp_inactive_skus,\n", "    count_active_skus_be,\n", "    count_inactive_skus_be,\n", "    \n", "    net_inv_present_be,\n", "    net_inv_exposure_be,\n", "    net_inv_volume_be,\n", "    doi_sto_cpd_basis_be,\n", "    doi_po_cpd_basis_be,\n", "    sto_cpd_be,\n", "    po_cpd_be,\n", "    excess_inv_be,\n", "    excess_inv_exposure_be,\n", "    excess_inv_volume_be,\n", "    no_sale_inv_be,\n", "    no_sale_inv_exposure_be,\n", "    no_sale_inv_volume_be,\n", "    cpd_depleted_inv_be,\n", "    cpd_depleted_inv_exposure_be,\n", "    cpd_depleted_inv_volume_be,\n", "    expiring_inv_be,\n", "    expiring_inv_exposure_be,\n", "    expiring_inv_volume_be,\n", "    overall_dead_be,\n", "    overall_dead_exposure_be,\n", "    overall_dead_volume_be\n", "    \n", "    from supply_etls.inventory_core_metrics_v6 icm\n", "    where icm.date_=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "be_overall_final as(\n", "    select date_,\n", "    SUM(picking_qty_util) AS picking_qty_util,\n", "    SUM(picking_qty_cap) AS picking_qty_cap,\n", "    AVG(picking_qty_util_per) AS picking_qty_util_per,\n", "    SUM(picking_sku_util) AS picking_sku_util,\n", "    SUM(picking_sku_cap) AS picking_sku_cap,\n", "    AVG(picking_sku_util_per) AS picking_sku_util_per,\n", "    SUM(be_regular_storage_capacity) AS be_regular_storage_capacity,\n", "    SUM(be_regular_onshelf_inventory) AS be_regular_onshelf_inventory,\n", "    SUM(be_regular_in_transit_quantity) AS be_regular_in_transit_quantity,\n", "    SUM(be_regular_scaled_onshelf_inventory) AS be_regular_scaled_onshelf_inventory,\n", "    AVG(be_regular_onshelf_utilisation) AS be_regular_onshelf_utilisation,\n", "    SUM(be_regular_scaled_open_po_qty) AS be_regular_scaled_open_po_qty,\n", "    AVG(be_regular_utilisation_with_open_stos) AS be_regular_utilisation_with_open_stos,\n", "    SUM(be_cold_storage_capacity) AS be_cold_storage_capacity,\n", "    SUM(be_cold_onshelf_inventory) AS be_cold_onshelf_inventory,\n", "    SUM(be_cold_in_transit_quantity) AS be_cold_in_transit_quantity,\n", "    SUM(be_cold_scaled_onshelf_inventory) AS be_cold_scaled_onshelf_inventory,\n", "    SUM(be_cold_scaled_open_po_qty) AS be_cold_scaled_open_po_qty,\n", "    AVG(be_cold_onshelf_utilisation) AS be_cold_onshelf_utilisation,\n", "    AVG(be_cold_utilisation_with_open_stos) AS be_cold_utilisation_with_open_stos,\n", "    SUM(be_heavy_storage_capacity) AS be_heavy_storage_capacity,\n", "    SUM(be_heavy_onshelf_inventory) AS be_heavy_onshelf_inventory,\n", "    SUM(be_heavy_in_transit_quantity) AS be_heavy_in_transit_quantity,\n", "    SUM(be_heavy_scaled_onshelf_inventory) AS be_heavy_scaled_onshelf_inventory,\n", "    SUM(be_heavy_scaled_open_po_qty) AS be_heavy_scaled_open_po_qty,\n", "    AVG(be_heavy_onshelf_utilisation) AS be_heavy_onshelf_utilisation,\n", "    AVG(be_heavy_utilisation_with_open_stos) AS be_heavy_utilisation_with_open_stos,\n", "    SUM(be_frozen_storage_capacity) AS be_frozen_storage_capacity,\n", "    SUM(be_frozen_onshelf_inventory) AS be_frozen_onshelf_inventory,\n", "    SUM(be_frozen_in_transit_quantity) AS be_frozen_in_transit_quantity,\n", "    SUM(be_frozen_scaled_onshelf_inventory) AS be_frozen_scaled_onshelf_inventory,\n", "    SUM(be_frozen_scaled_open_po_qty) AS be_frozen_scaled_open_po_qty,\n", "    AVG(be_frozen_onshelf_utilisation) AS be_frozen_onshelf_utilisation,\n", "    AVG(be_frozen_utilisation_with_open_stos) AS be_frozen_utilisation_with_open_stos,\n", "    SUM(wotif_created_qty_FE) AS wotif_created_qty_FE,\n", "    SUM(wotif_on_time_picked_qty_FE) AS wotif_on_time_picked_qty_FE,\n", "    SUM(wotif_on_time_packaged_qty_FE) AS wotif_on_time_packaged_qty_FE,\n", "    SUM(wotif_on_time_sorted_qty_FE) AS wotif_on_time_sorted_qty_FE,\n", "    SUM(wotif_on_time_dispatched_qty_FE) AS wotif_on_time_dispatched_qty_FE,\n", "    SUM(wotif_created_qty_BE) AS wotif_created_qty_BE,\n", "    SUM(wotif_on_time_picked_qty_BE) AS wotif_on_time_picked_qty_BE,\n", "    SUM(wotif_on_time_packaged_qty_BE) AS wotif_on_time_packaged_qty_BE,\n", "    SUM(wotif_on_time_sorted_qty_BE) AS wotif_on_time_sorted_qty_BE,\n", "    SUM(wotif_on_time_dispatched_qty_BE) AS wotif_on_time_dispatched_qty_BE,\n", "    SUM(be_active_express_weight_avail) AS be_active_express_weight_avail,\n", "    SUM(be_active_express_binary_avail) AS be_active_express_binary_avail,\n", "    SUM(be_active_longtail_weight_avail) AS be_active_longtail_weight_avail,\n", "    SUM(be_active_longtail_binary_avail) AS be_active_longtail_binary_avail,\n", "    SUM(be_temp_inactive_express_weight_avail) AS be_temp_inactive_express_weight_avail,\n", "    SUM(be_temp_inactive_express_binary_avail) AS be_temp_inactive_express_binary_avail,\n", "    SUM(be_temp_inactive_longtail_weight_avail) AS be_temp_inactive_longtail_weight_avail,\n", "    SUM(be_temp_inactive_longtail_binary_avail) AS be_temp_inactive_longtail_binary_avail,\n", "    SUM(be_overall_overall_weight_avail) AS be_overall_overall_weight_avail,\n", "    SUM(be_overall_overall_binary_avail) AS be_overall_overall_binary_avail,\n", "    \n", "    SUM(be_fe_active_express_weight_avail) AS be_fe_active_express_weight_avail,\n", "    SUM(be_fe_active_express_binary_avail) AS be_fe_active_express_binary_avail,\n", "    SUM(be_fe_active_longtail_weight_avail) AS be_fe_active_longtail_weight_avail,\n", "    SUM(be_fe_active_longtail_binary_avail) AS be_fe_active_longtail_binary_avail,\n", "    SUM(be_fe_temp_inactive_express_weight_avail) AS be_fe_temp_inactive_express_weight_avail,\n", "    SUM(be_fe_temp_inactive_express_binary_avail) AS be_fe_temp_inactive_express_binary_avail,\n", "    SUM(be_fe_temp_inactive_longtail_weight_avail) AS be_fe_temp_inactive_longtail_weight_avail,\n", "    SUM(be_fe_temp_inactive_longtail_binary_avail) AS be_fe_temp_inactive_longtail_binary_avail,\n", "    SUM(be_fe_overall_overall_weight_avail) AS be_fe_overall_overall_weight_avail,\n", "    SUM(be_fe_overall_overall_binary_avail) AS be_fe_overall_overall_binary_avail,\n", "    \n", "    SUM(bexfe_active_express_weight_avail) AS bexfe_active_express_weight_avail,\n", "    SUM(bexfe_active_express_binary_avail) AS bexfe_active_express_binary_avail,\n", "    SUM(bexfe_active_longtail_weight_avail) AS bexfe_active_longtail_weight_avail,\n", "    SUM(bexfe_active_longtail_binary_avail) AS bexfe_active_longtail_binary_avail,\n", "    SUM(bexfe_temp_inactive_express_weight_avail) AS bexfe_temp_inactive_express_weight_avail,\n", "    SUM(bexfe_temp_inactive_express_binary_avail) AS bexfe_temp_inactive_express_binary_avail,\n", "    SUM(bexfe_temp_inactive_longtail_weight_avail) AS bexfe_temp_inactive_longtail_weight_avail,\n", "    SUM(bexfe_temp_inactive_longtail_binary_avail) AS bexfe_temp_inactive_longtail_binary_avail,\n", "    SUM(bexfe_overall_overall_weight_avail) AS bexfe_overall_overall_weight_avail,\n", "    SUM(bexfe_overall_overall_binary_avail) AS bexfe_overall_overall_binary_avail,\n", "    \n", "    SUM(bexfe_be_availability_ptype) AS bexfe_be_availability_ptype,\n", "    SUM(bexfe_fe_availability_ptype) AS bexfe_fe_availability_ptype,\n", "    \n", "    SUM(lines_active) as lines_active,\n", "    SUM(lines_temp_inactive_skus) as lines_temp_inactive_skus,\n", "    SUM(count_active_skus_be) as count_active_skus_be,\n", "    SUM(count_inactive_skus_be) as count_inactive_skus_be,\n", "    \n", "    SUM(net_inv_present_be) as net_inv_present_be, \n", "    SUM(net_inv_exposure_be) as net_inv_exposure_be,\n", "    SUM(net_inv_volume_be) as net_inv_volume_be,\n", "    AVG(doi_sto_cpd_basis_be) as doi_sto_cpd_basis_be,\n", "    AVG(doi_po_cpd_basis_be) as doi_po_cpd_basis_be,\n", "    SUM(sto_cpd_be) as sto_cpd_be,\n", "    SUM(po_cpd_be) as po_cpd_be,\n", "    SUM(excess_inv_be) as excess_inv_be,\n", "    SUM(excess_inv_exposure_be) as excess_inv_exposure_be,\n", "    SUM(excess_inv_volume_be) as excess_inv_volume_be,\n", "    SUM(no_sale_inv_be) as no_sale_inv_be,\n", "    SUM(no_sale_inv_exposure_be) as no_sale_inv_exposure_be,\n", "    SUM(no_sale_inv_volume_be) as no_sale_inv_volume_be,\n", "    SUM(cpd_depleted_inv_be) as cpd_depleted_inv_be,\n", "    SUM(cpd_depleted_inv_exposure_be) as cpd_depleted_inv_exposure_be,\n", "    SUM(cpd_depleted_inv_volume_be) as cpd_depleted_inv_volume_be,\n", "    SUM(expiring_inv_be) as expiring_inv_be,\n", "    SUM(expiring_inv_exposure_be) as expiring_inv_exposure_be,\n", "    SUM(expiring_inv_volume_be) as expiring_inv_volume_be,\n", "    SUM(overall_dead_be) as overall_dead_be,\n", "    SUM(overall_dead_exposure_be) as overall_dead_exposure_be,\n", "    SUM(overall_dead_volume_be) as overall_dead_volume_be\n", "    \n", "    from be_overall\n", "    group by 1\n", "),\n", "    \n", "pan_india as(\n", "    select  \n", "            CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "            0 as city_id,\n", "            '.PAN India' as city_name,\n", "            0 as be_facility_id,\n", "            0 as be_inv_outlet_id,\n", "            '.PAN India' as be_facility_name,\n", "            MAX(dpa.no_of_ds_live) as no_of_ds_live,\n", "            MAX(dpa.no_of_ds_live_express) as no_of_ds_live_express,\n", "            MAX(dpa.no_of_ds_live_longtail) as no_of_ds_live_longtail,\n", "            0 as fe_facility_id,\n", "            0 as fe_outlet_id,\n", "            '.PAN India' as fe_facility_name,\n", "    \n", "            SUM(icm.total_loss) as total_loss,\n", "            \n", "            0 as fe_cpd,\n", "            0 as fe_weight,\n", "            0 as be_cpd,\n", "            0 as be_weight,\n", "            0 as city_cpd,\n", "            0 as city_weight,\n", "    \n", "    MAX(co.city_active_express_weight_avail) as city_active_express_weight_avail,\n", "    MAX(co.city_active_express_binary_avail) as city_active_express_binary_avail,\n", "    MAX(co.city_active_longtail_weight_avail) as city_active_longtail_weight_avail,\n", "    MAX(co.city_active_longtail_binary_avail) as city_active_longtail_binary_avail,\n", "    MAX(co.city_temp_inactive_express_weight_avail) as city_temp_inactive_express_weight_avail,\n", "    MAX(co.city_temp_inactive_express_binary_avail) as city_temp_inactive_express_binary_avail,\n", "    MAX(co.city_temp_inactive_longtail_weight_avail) as city_temp_inactive_longtail_weight_avail,\n", "    MAX(co.city_temp_inactive_longtail_binary_avail) as city_temp_inactive_longtail_binary_avail,\n", "    MAX(co.city_overall_overall_weight_avail) as city_overall_overall_weight_avail,\n", "    MAX(co.city_overall_overall_binary_avail) as city_overall_overall_binary_avail,\n", "\n", "    MAX(bo.be_active_express_weight_avail) as be_active_express_weight_avail,\n", "    MAX(bo.be_active_express_binary_avail) as be_active_express_binary_avail,\n", "    MAX(bo.be_active_longtail_weight_avail) as be_active_longtail_weight_avail,\n", "    MAX(bo.be_active_longtail_binary_avail) as be_active_longtail_binary_avail,\n", "    MAX(bo.be_temp_inactive_express_weight_avail) as be_temp_inactive_express_weight_avail,\n", "    MAX(bo.be_temp_inactive_express_binary_avail) as be_temp_inactive_express_binary_avail,\n", "    MAX(bo.be_temp_inactive_longtail_weight_avail) as be_temp_inactive_longtail_weight_avail,\n", "    MAX(bo.be_temp_inactive_longtail_binary_avail) as be_temp_inactive_longtail_binary_avail,\n", "    MAX(bo.be_overall_overall_weight_avail) as be_overall_overall_weight_avail,\n", "    MAX(bo.be_overall_overall_binary_avail) as be_overall_overall_binary_avail,\n", "\n", "    MAX(fo.fe_active_express_weight_avail) as fe_active_express_weight_avail,\n", "    MAX(fo.fe_active_express_binary_avail) as fe_active_express_binary_avail,\n", "    MAX(fo.fe_active_longtail_weight_avail) as fe_active_longtail_weight_avail,\n", "    MAX(fo.fe_active_longtail_binary_avail) as fe_active_longtail_binary_avail,\n", "    MAX(fo.fe_temp_inactive_express_weight_avail) as fe_temp_inactive_express_weight_avail,\n", "    MAX(fo.fe_temp_inactive_express_binary_avail) as fe_temp_inactive_express_binary_avail,\n", "    MAX(fo.fe_temp_inactive_longtail_weight_avail) as fe_temp_inactive_longtail_weight_avail,\n", "    MAX(fo.fe_temp_inactive_longtail_binary_avail) as fe_temp_inactive_longtail_binary_avail,\n", "    MAX(fo.fe_overall_overall_weight_avail) as fe_overall_overall_weight_avail,\n", "    MAX(fo.fe_overall_overall_binary_avail) as fe_overall_overall_binary_avail,\n", "\n", "    MAX(bo.be_fe_active_express_weight_avail) as be_fe_active_express_weight_avail,\n", "    MAX(bo.be_fe_active_express_binary_avail) as be_fe_active_express_binary_avail,\n", "    MAX(bo.be_fe_active_longtail_weight_avail) as be_fe_active_longtail_weight_avail,\n", "    MAX(bo.be_fe_active_longtail_binary_avail) as be_fe_active_longtail_binary_avail,\n", "    MAX(bo.be_fe_temp_inactive_express_weight_avail) as be_fe_temp_inactive_express_weight_avail,\n", "    MAX(bo.be_fe_temp_inactive_express_binary_avail) as be_fe_temp_inactive_express_binary_avail,\n", "    MAX(bo.be_fe_temp_inactive_longtail_weight_avail) as be_fe_temp_inactive_longtail_weight_avail,\n", "    MAX(bo.be_fe_temp_inactive_longtail_binary_avail) as be_fe_temp_inactive_longtail_binary_avail,\n", "    MAX(bo.be_fe_overall_overall_weight_avail) as be_fe_overall_overall_weight_avail,\n", "    MAX(bo.be_fe_overall_overall_binary_avail) as be_fe_overall_overall_binary_avail,\n", "    \n", "    MAX(bo.bexfe_active_express_weight_avail) as bexfe_active_express_weight_avail,\n", "    MAX(bo.bexfe_active_express_binary_avail) as bexfe_active_express_binary_avail,\n", "    MAX(bo.bexfe_active_longtail_weight_avail) as bexfe_active_longtail_weight_avail,\n", "    MAX(bo.bexfe_active_longtail_binary_avail) as bexfe_active_longtail_binary_avail,\n", "    MAX(bo.bexfe_temp_inactive_express_weight_avail) as bexfe_temp_inactive_express_weight_avail,\n", "    MAX(bo.bexfe_temp_inactive_express_binary_avail) as bexfe_temp_inactive_express_binary_avail,\n", "    MAX(bo.bexfe_temp_inactive_longtail_weight_avail) as bexfe_temp_inactive_longtail_weight_avail,\n", "    MAX(bo.bexfe_temp_inactive_longtail_binary_avail) as bexfe_temp_inactive_longtail_binary_avail,\n", "    MAX(bo.bexfe_overall_overall_weight_avail) as bexfe_overall_overall_weight_avail,\n", "    MAX(bo.bexfe_overall_overall_binary_avail) as bexfe_overall_overall_binary_avail,\n", "    \n", "    MAX(fo.current_inventory) as current_inventory,\n", "    \n", "    SUM(icm.total_dn_qty) as total_dn_qty,\n", "    SUM(icm.dn_short_qty) as dn_short_qty,\n", "    SUM(icm.dn_other_damage_qty) as dn_other_damage_qty,\n", "    SUM(icm.transfer_loss_qty) as transfer_loss_qty,\n", "    SUM(icm.transfer_loss_amt) as transfer_loss_amt,\n", "    SUM(icm.total_dn_amt) as total_dn_amt,\n", "    SUM(icm.dn_short_amt) as dn_short_amt,\n", "    SUM(icm.dn_other_damage_amt) as dn_other_damage_amt,\n", "    SUM(icm.dn_other_qty) as dn_other_qty,\n", "    SUM(icm.dn_other_amt) as dn_other_amt,\n", "    SUM(icm.dn_other_excess_item_qty) as dn_other_excess_item_qty,\n", "    SUM(icm.dn_other_excess_item_amt) as dn_other_excess_item_amt,\n", "    SUM(icm.dn_other_exp_nte_qty) as dn_other_exp_nte_qty,\n", "    SUM(icm.dn_other_exp_nte_amt) as dn_other_exp_nte_amt,\n", "    SUM(icm.dn_other_freebie_missing_qty) as dn_other_freebie_missing_qty,\n", "    SUM(icm.dn_other_freebie_missing_amt) as dn_other_freebie_missing_amt,\n", "    SUM(icm.dn_other_misc_qty) as dn_other_misc_qty,\n", "    SUM(icm.dn_other_misc_amt) as dn_other_misc_amt,\n", "    SUM(icm.dn_other_quality_issue_qty) as dn_other_quality_issue_qty,\n", "    SUM(icm.dn_other_quality_issue_amt) as dn_other_quality_issue_amt,\n", "    SUM(icm.dn_other_upc_not_scannable_qty) as dn_other_upc_not_scannable_qty,\n", "    SUM(icm.dn_other_upc_not_scannable_amt) as dn_other_upc_not_scannable_amt,\n", "    SUM(icm.dn_other_variant_mismatch_qty) as dn_other_variant_mismatch_qty,\n", "    SUM(icm.dn_other_variant_mismatch_amt) as dn_other_variant_mismatch_amt,\n", "    SUM(icm.dn_other_wrong_item_qty) as dn_other_wrong_item_qty,\n", "    SUM(icm.dn_other_wrong_item_amt) as dn_other_wrong_item_amt,\n", "    SUM(icm.lost_in_transit_positive_qty) as lost_in_transit_positive_qty,\n", "    SUM(icm.lost_in_transit_positive_amt) as lost_in_transit_positive_amt,\n", "    SUM(icm.lost_in_transit_negative_qty) as lost_in_transit_negative_qty,\n", "    SUM(icm.lost_in_transit_negative_amt) as lost_in_transit_negative_amt, \n", "    SUM(icm.b2b_amt) as b2b_amt,\n", "    SUM(icm.rsto_dump_amt) as rsto_dump_amt,\n", "    SUM(icm.grn_amt) as grn_amt,\n", "    SUM(icm.billed_amt) as billed_amt, \n", "    \n", "    SUM(icm.fvcpu_indent) as fvcpu_indent,\n", "    SUM(icm.fvcpu_indent_wt) as fvcpu_indent_wt,\n", "    SUM(icm.fvcpu_total_con_cost) as fvcpu_total_con_cost,\n", "    SUM(icm.fvcpu_per_unit_total_con_cost) as fvcpu_per_unit_total_con_cost,\n", "    \n", "    SUM(icm.esto_excess_qty) as esto_excess_qty,\n", "    SUM(icm.esto_excess_value) as esto_excess_value,\n", "    \n", "    SUM(icm.sto_quantity) as sto_quantity,\n", "    SUM(icm.v1_line_items) as v1_line_items,\n", "    SUM(icm.v2_line_items) as v2_line_items,\n", "    \n", "    MAX(fo.planned_quantity) as planned_quantity,\n", "    MAX(fo.qty_sold) as qty_sold,\n", "    MAX(fo.potential_order_quantity) as potential_order_quantity,\n", "    MAX(fo.planned_quantity_be_avail) as planned_quantity_be_avail,\n", "    MAX(fo.count_orders) as count_orders,\n", "    MAX(fo.gmv) as gmv,\n", "    \n", "    MAX(bo.picking_qty_util) as picking_qty_util,\n", "    MAX(bo.picking_qty_cap) as picking_qty_cap,\n", "    MAX(bo.picking_qty_util_per) as picking_qty_util_per,\n", "    MAX(bo.picking_sku_util) as picking_sku_util,\n", "    MAX(bo.picking_sku_cap) as picking_sku_cap,\n", "    MAX(bo.picking_sku_util_per) as picking_sku_util_per,\n", "    \n", "    SUM(icm.inward_util) as inward_util,\n", "    SUM(icm.inward_cap_in_qty) as inward_cap_in_qty,\n", "    AVG(icm.inward_util_per) as inward_util_per,\n", "    SUM(icm.truck_load_util) as truck_load_util,\n", "    SUM(icm.truck_load_cap_in_kg) as truck_load_cap_in_kg,\n", "    AVG(icm.truck_load_util_per) as truck_load_util_per,\n", "    \n", "    MAX(bo.be_regular_storage_capacity) as be_regular_storage_capacity,\n", "    MAX(bo.be_regular_onshelf_inventory) as be_regular_onshelf_inventory,\n", "    MAX(bo.be_regular_in_transit_quantity) as be_regular_in_transit_quantity,\n", "    MAX(bo.be_regular_scaled_onshelf_inventory) as be_regular_scaled_onshelf_inventory,\n", "    MAX(bo.be_regular_onshelf_utilisation) as be_regular_onshelf_utilisation,\n", "    MAX(bo.be_regular_scaled_open_po_qty) as be_regular_scaled_open_po_qty,\n", "    MAX(bo.be_regular_utilisation_with_open_stos) as be_regular_utilisation_with_open_stos,\n", "    MAX(bo.be_cold_storage_capacity) as be_cold_storage_capacity,\n", "    MAX(bo.be_cold_onshelf_inventory) as be_cold_onshelf_inventory,\n", "    MAX(bo.be_cold_in_transit_quantity) as be_cold_in_transit_quantity,\n", "    MAX(bo.be_cold_scaled_onshelf_inventory) as be_cold_scaled_onshelf_inventory,\n", "    MAX(bo.be_cold_scaled_open_po_qty) as be_cold_scaled_open_po_qty,\n", "    MAX(bo.be_cold_onshelf_utilisation) as be_cold_onshelf_utilisation,\n", "    MAX(bo.be_cold_utilisation_with_open_stos) as be_cold_utilisation_with_open_stos,\n", "    MAX(bo.be_heavy_storage_capacity) as be_heavy_storage_capacity,\n", "    MAX(bo.be_heavy_onshelf_inventory) as be_heavy_onshelf_inventory,\n", "    MAX(bo.be_heavy_in_transit_quantity) as be_heavy_in_transit_quantity,\n", "    MAX(bo.be_heavy_scaled_onshelf_inventory) as be_heavy_scaled_onshelf_inventory,\n", "    MAX(bo.be_heavy_scaled_open_po_qty) as be_heavy_scaled_open_po_qty,\n", "    MAX(bo.be_heavy_onshelf_utilisation) as be_heavy_onshelf_utilisation,\n", "    MAX(bo.be_heavy_utilisation_with_open_stos) as be_heavy_utilisation_with_open_stos,\n", "    MAX(bo.be_frozen_storage_capacity) as be_frozen_storage_capacity,\n", "    MAX(bo.be_frozen_onshelf_inventory) as be_frozen_onshelf_inventory,\n", "    MAX(bo.be_frozen_in_transit_quantity) as be_frozen_in_transit_quantity,\n", "    MAX(bo.be_frozen_scaled_onshelf_inventory) as be_frozen_scaled_onshelf_inventory,\n", "    MAX(bo.be_frozen_scaled_open_po_qty) as be_frozen_scaled_open_po_qty,\n", "    MAX(bo.be_frozen_onshelf_utilisation) as be_frozen_onshelf_utilisation,\n", "    MAX(bo.be_frozen_utilisation_with_open_stos) as be_frozen_utilisation_with_open_stos,\n", "    \n", "    MAX(fo.fe_regular_storage_capacity) AS fe_regular_storage_capacity,\n", "    MAX(fo.fe_regular_onshelf_inventory) AS fe_regular_onshelf_inventory,\n", "    MAX(fo.fe_regular_in_transit_quantity) AS fe_regular_in_transit_quantity,\n", "    MAX(fo.fe_regular_scaled_onshelf_inventory) AS fe_regular_scaled_onshelf_inventory,\n", "    MAX(fo.fe_regular_onshelf_utilisation) AS fe_regular_onshelf_utilisation,\n", "    MAX(fo.fe_regular_scaled_open_po_qty) AS fe_regular_scaled_open_po_qty,\n", "    MAX(fo.fe_regular_utilisation_with_open_stos) AS fe_regular_utilisation_with_open_stos,\n", "    MAX(fo.fe_cold_storage_capacity) AS fe_cold_storage_capacity,\n", "    MAX(fo.fe_cold_onshelf_inventory) AS fe_cold_onshelf_inventory,\n", "    MAX(fo.fe_cold_in_transit_quantity) AS fe_cold_in_transit_quantity,\n", "    MAX(fo.fe_cold_scaled_onshelf_inventory) AS fe_cold_scaled_onshelf_inventory,\n", "    MAX(fo.fe_cold_scaled_open_po_qty) AS fe_cold_scaled_open_po_qty,\n", "    MAX(fo.fe_cold_onshelf_utilisation) AS fe_cold_onshelf_utilisation,\n", "    MAX(fo.fe_cold_utilisation_with_open_stos) AS fe_cold_utilisation_with_open_stos,\n", "    MAX(fo.fe_heavy_storage_capacity) AS fe_heavy_storage_capacity,\n", "    MAX(fo.fe_heavy_onshelf_inventory) AS fe_heavy_onshelf_inventory,\n", "    MAX(fo.fe_heavy_in_transit_quantity) AS fe_heavy_in_transit_quantity,\n", "    MAX(fo.fe_heavy_scaled_onshelf_inventory) AS fe_heavy_scaled_onshelf_inventory,\n", "    MAX(fo.fe_heavy_scaled_open_po_qty) AS fe_heavy_scaled_open_po_qty,\n", "    MAX(fo.fe_heavy_onshelf_utilisation) AS fe_heavy_onshelf_utilisation,\n", "    MAX(fo.fe_heavy_utilisation_with_open_stos) AS fe_heavy_utilisation_with_open_stos,\n", "    MAX(fo.fe_frozen_storage_capacity) AS fe_frozen_storage_capacity,\n", "    MAX(fo.fe_frozen_onshelf_inventory) AS fe_frozen_onshelf_inventory,\n", "    MAX(fo.fe_frozen_in_transit_quantity) AS fe_frozen_in_transit_quantity,\n", "    MAX(fo.fe_frozen_scaled_onshelf_inventory) AS fe_frozen_scaled_onshelf_inventory,\n", "    MAX(fo.fe_frozen_scaled_open_po_qty) AS fe_frozen_scaled_open_po_qty,\n", "    MAX(fo.fe_frozen_onshelf_utilisation) AS fe_frozen_onshelf_utilisation,\n", "    MAX(fo.fe_frozen_utilisation_with_open_stos) AS fe_frozen_utilisation_with_open_stos,\n", "    \n", "    SUM(icm.billed_qty_fr) as billed_qty_fr,      \n", "    SUM(icm.dispatch_qty_fr) as dispatch_qty_fr,     \n", "    SUM(icm.grn_qty_fr) as grn_qty_fr,\n", "    SUM(icm.sto_raised_qty_fr) as sto_raised_qty_fr,\n", "    AVG(icm.billed_fr) as billed_fr,\n", "    AVG(icm.dispatch_fr) as dispatch_fr,\n", "    AVG(icm.grn_fr) as grn_fr,\n", "    \n", "    MAX(bo.wotif_created_qty_FE) as wotif_created_qty_FE,\n", "    MAX(bo.wotif_on_time_picked_qty_FE) as wotif_on_time_picked_qty_FE,\n", "    MAX(bo.wotif_on_time_packaged_qty_FE) as wotif_on_time_packaged_qty_FE,\n", "    MAX(bo.wotif_on_time_sorted_qty_FE) as wotif_on_time_sorted_qty_FE,\n", "    MAX(bo.wotif_on_time_dispatched_qty_FE) as wotif_on_time_dispatched_qty_FE,\n", "    MAX(bo.wotif_created_qty_BE) as wotif_created_qty_BE,\n", "    MAX(bo.wotif_on_time_picked_qty_BE) as wotif_on_time_picked_qty_BE,\n", "    MAX(bo.wotif_on_time_packaged_qty_BE) as wotif_on_time_packaged_qty_BE,\n", "    MAX(bo.wotif_on_time_sorted_qty_BE) as wotif_on_time_sorted_qty_BE,\n", "    MAX(bo.wotif_on_time_dispatched_qty_BE) as wotif_on_time_dispatched_qty_BE,\n", "    \n", "    SUM(icm.indent_raised_fotif) as indent_raised_fotif,\n", "    SUM(icm.total_trips) as total_trips,\n", "    SUM(icm.perfect_trips) as perfect_trips,\n", "    AVG(icm.on_time_vehicle_report) as on_time_vehicle_report,\n", "    AVG(icm.on_time_load_start) as on_time_load_start,\n", "    AVG(icm.on_time_dispatch) as on_time_dispatch,\n", "    AVG(icm.on_time_ds_report) as on_time_ds_report,\n", "    SUM(icm.on_time_ds_for_indent) as on_time_ds_for_indent,\n", "    SUM(icm.on_time_ds_for_indent_grn) as on_time_ds_for_indent_grn,\n", "    AVG(icm.early_ds_report) as early_ds_report,\n", "    SUM(icm.early_ds_report_for_indent) as early_ds_report_for_indent,\n", "\n", "    SUM(icm.sto_raised_qty) as sto_raised_qty,\n", "    SUM(icm.billed_qty) as billed_qty,\n", "    SUM(icm.grn_qty) as grn_qty,\n", "    AVG(icm.on_time_grn_quantity) as on_time_grn_quantity,\n", "    AVG(icm.delayed_grn_quantity) as delayed_grn_quantity,\n", "    SUM(icm.delayed_grn_qty) as delayed_grn_qty,\n", "    SUM(icm.on_time_grn_qty) as on_time_grn_qty,\n", "    SUM(icm.count_active_skus_fe) as count_active_skus_fe,\n", "    SUM(icm.count_temp_inactive_skus_fe) as count_temp_inactive_skus_fe,\n", "    MAX(bo.lines_active) as lines_active,\n", "    MAX(bo.lines_temp_inactive_skus) as lines_temp_inactive_skus,\n", "    MAX(bo.count_active_skus_be) as count_active_skus_be,\n", "    MAX(bo.count_inactive_skus_be) as count_inactive_skus_be,\n", "    \n", "    MAX(fo.avg_out_of_stock_duration) as avg_out_of_stock_duration,\n", "    MAX(fo.avg_out_of_stock_duration_weighted) as avg_out_of_stock_duration_weighted,\n", "    \n", "    MAX(spi.count_active_skus_pan) as count_active_skus_pan,\n", "    MAX(spi.count_inactive_skus_pan) as count_inactive_skus_pan,\n", "    \n", "    MAX(dpi.count_hybrid_stores) as count_hybrid_stores,\n", "    MAX(dpi.count_express_only) as count_express_only,\n", "    MAX(dpi.count_longtail_only) as count_longtail_only,\n", "    \n", "    max(bo.net_inv_present_be) as net_inv_present_be, \n", "    max(bo.net_inv_exposure_be) as net_inv_exposure_be,\n", "    max(bo.net_inv_volume_be) as net_inv_volume_be,\n", "    max(bo.doi_sto_cpd_basis_be) as doi_sto_cpd_basis_be,\n", "    max(bo.doi_po_cpd_basis_be) as doi_po_cpd_basis_be,\n", "    max(bo.sto_cpd_be) as sto_cpd_be,\n", "    max(bo.po_cpd_be) as po_cpd_be,\n", "    max(bo.excess_inv_be) as excess_inv_be,\n", "    max(bo.excess_inv_exposure_be) as excess_inv_exposure_be,\n", "    max(bo.excess_inv_volume_be) as excess_inv_volume_be,\n", "    max(bo.no_sale_inv_be) as no_sale_inv_be,\n", "    max(bo.no_sale_inv_exposure_be) as no_sale_inv_exposure_be,\n", "    max(bo.no_sale_inv_volume_be) as no_sale_inv_volume_be,\n", "    max(bo.cpd_depleted_inv_be) as cpd_depleted_inv_be,\n", "    max(bo.cpd_depleted_inv_exposure_be) as cpd_depleted_inv_exposure_be,\n", "    max(bo.cpd_depleted_inv_volume_be) as cpd_depleted_inv_volume_be,\n", "    max(bo.expiring_inv_be) as expiring_inv_be,\n", "    max(bo.expiring_inv_exposure_be) as expiring_inv_exposure_be,\n", "    max(bo.expiring_inv_volume_be) as expiring_inv_volume_be,\n", "    max(bo.overall_dead_be) as overall_dead_be,\n", "    max(bo.overall_dead_exposure_be) as overall_dead_exposure_be,\n", "    max(bo.overall_dead_volume_be) as overall_dead_volume_be,\n", "    \n", "    max(fo.net_inv_present_fe) as net_inv_present_fe, \n", "    max(fo.net_inv_exposure_fe) as net_inv_exposure_fe,\n", "    max(fo.net_inv_volume_fe) as net_inv_volume_fe,\n", "    max(fo.doi_sto_cpd_basis_fe) as doi_sto_cpd_basis_fe,\n", "    max(fo.doi_po_cpd_basis_fe) as doi_po_cpd_basis_fe,\n", "    max(fo.sto_cpd_fe) as sto_cpd_fe,\n", "    max(fo.po_cpd_fe) as po_cpd_fe,\n", "    max(fo.excess_inv_fe) as excess_inv_fe,\n", "    max(fo.excess_inv_exposure_fe) as excess_inv_exposure_fe,\n", "    max(fo.excess_inv_volume_fe) as excess_inv_volume_fe,\n", "    max(fo.no_sale_inv_fe) as no_sale_inv_fe,\n", "    max(fo.no_sale_inv_exposure_fe) as no_sale_inv_exposure_fe,\n", "    max(fo.no_sale_inv_volume_fe) as no_sale_inv_volume_fe,\n", "    max(fo.cpd_depleted_inv_fe) as cpd_depleted_inv_fe,\n", "    max(fo.cpd_depleted_inv_exposure_fe) as cpd_depleted_inv_exposure_fe,\n", "    max(fo.cpd_depleted_inv_volume_fe) as cpd_depleted_inv_volume_fe,\n", "    max(fo.expiring_inv_fe) as expiring_inv_fe,\n", "    max(fo.expiring_inv_exposure_fe) as expiring_inv_exposure_fe,\n", "    max(fo.expiring_inv_volume_fe) as expiring_inv_volume_fe,\n", "    max(fo.overall_dead_fe) as overall_dead_fe,\n", "    max(fo.overall_dead_exposure_fe) as overall_dead_exposure_fe,\n", "    max(fo.overall_dead_volume_fe) as overall_dead_volume_fe,\n", "    \n", "    MAX(db.tea_coverage) as tea_coverage,\n", "    MAX(db.inv_coverage) as inv_coverage,\n", "    MAX(db.tea_coverage_be_avail) as tea_coverage_be_avail,\n", "    MAX(db.inv_coverage_be_avail) as inv_coverage_be_avail,\n", "    MAX(db.tea_coverage_exp) as tea_coverage_exp,\n", "    MAX(db.inv_coverage_exp) as inv_coverage_exp,\n", "    MAX(db.tea_coverage_be_avail_exp) as tea_coverage_be_avail_exp,\n", "    MAX(db.inv_coverage_be_avail_exp) as inv_coverage_be_avail_exp,\n", "    MAX(db.tea_coverage_lt) as tea_coverage_lt,\n", "    MAX(db.inv_coverage_lt) as inv_coverage_lt,\n", "    MAX(db.tea_coverage_be_avail_lt) as tea_coverage_be_avail_lt,\n", "    MAX(db.inv_coverage_be_avail_lt) as inv_coverage_be_avail_lt,\n", "    \n", "    MAX(bo.bexfe_be_availability_ptype) as bexfe_be_availability_ptype,\n", "    MAX(bo.bexfe_fe_availability_ptype) as bexfe_fe_availability_ptype,\n", "    \n", "    MAX(fo.fe_availability_ptype) as fe_availability_ptype\n", "    \n", "            from ds_pan_india dpa\n", "            left join supply_etls.inventory_core_metrics_v6 icm on dpa.date_=icm.date_\n", "            left join fe_overall_final fo on fo.date_=icm.date_\n", "            left join be_overall_final bo on fo.date_=icm.date_\n", "            left join city_overall_final co on co.date_=icm.date_\n", "            left join sku_complexity_pan_india spi on spi.date_=icm.date_\n", "            left join ds_pan_india_detailed dpi on dpi.date_=icm.date_\n", "            left join dau_breakup db on db.be_facility_name=dpa.facility_name\n", ")\n", "\n", "select * from pan_india\n", "\n", "        \"\"\"\n", "    to_trino(inventory_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "e8de2230-e94f-4dfb-92ac-14914890eb87", "metadata": {}, "outputs": [], "source": ["def backfill():\n", "\n", "    today = datetime.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=53)\n", "\n", "    for i in range(4):\n", "        backfill_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        print(f\"Backfilling data for: {backfill_date.strftime('%Y-%m-%d')}\")\n", "        # Convert backfill_date into the format 'YYYY-MM-DD'\n", "        formatted_date = backfill_date.strftime(\"%Y-%m-%d\")\n", "\n", "        inventory_metrics_query = f\"\"\" \n", "\n", "WITH ds_pan_india as(\n", "    select date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "    '.PAN India' as facility_name,\n", "    0 as facility_id,\n", "    count(distinct tea.fe_facility_id) as no_of_ds_live,\n", "    count(distinct case when tea.store_assortment_type = 'EXPRESS' then tea.fe_facility_id end) as no_of_ds_live_express,\n", "    count(distinct case when tea.store_assortment_type = 'LONGTAIL' then tea.fe_facility_id end) as no_of_ds_live_longtail\n", "    from supply_etls.outlet_details od\n", "    left join supply_etls.inventory_metrics_tea_tagging tea on tea.be_facility_id = od.facility_id\n", "    where od.ars_check=1 \n", "        and od.taggings = 'be' \n", "        and (od.grocery_active_count >= 1 or od.facility_id = 2576) \n", "        and od.store_type = 'Packaged Goods'\n", "        and tea.flag = 'correct_tea' and tea.be_facility_id <> tea.fe_facility_id\n", "        and tea.assortment_status_id in (1,3)\n", "),\n", "\n", "ds_pan_india_detailed as(\n", "select date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "        sum(case when exp = 1 and lt=1 THEN 1 END) as count_hybrid_stores,\n", "        sum(case when exp = 1 and lt=0 THEN 1 END) as count_express_only,\n", "        sum(case when exp = 0 and lt=1 THEN 1 END) as count_longtail_only\n", "    from(\n", "        select DISTINCT fe_facility_id,\n", "                    sum(case when store_assortment_type = 'EXPRESS' then 1 else 0 end) as exp,\n", "                    sum(case when store_assortment_type = 'LONGTAIL' then 1 else 0 end) as lt\n", "                        from\n", "                            (select DISTINCT tea.fe_facility_id,tea.store_assortment_type\n", "                            from supply_etls.outlet_details od\n", "                            left join supply_etls.inventory_metrics_tea_tagging tea on tea.be_facility_id = od.facility_id\n", "                            where od.ars_check=1 \n", "                                and od.taggings = 'be' \n", "                                and (od.grocery_active_count >= 1 or od.facility_id = 2576) \n", "                                and od.store_type = 'Packaged Goods'\n", "                                and tea.flag = 'correct_tea' and tea.be_facility_id <> tea.fe_facility_id\n", "                                and tea.assortment_status_id in (1,3)\n", "                              )\n", "                              group by 1\n", "    )\n", "    group by 1\n", "),\n", "\n", "tea_tagging_item AS (\n", "    SELECT  \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, store_assortment_type,\n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id, fe_city_id, fe_city_name, assortment_status_id\n", "    FROM supply_etls.inventory_metrics_tea_tagging\n", "    WHERE flag = 'correct_tea' AND be_facility_id <> fe_facility_id\n", "),\n", "\n", "sku_complexity_pan_india as ( \n", "    select date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "        sum(case when master_assortment_substate_id = 1 THEN 1 END) as count_active_skus_pan,\n", "        sum(case when master_assortment_substate_id = 3 THEN 1 END) as count_inactive_skus_pan\n", "\n", "    from(\n", "        select DISTINCT item_id,\n", "            case when sum(case when master_assortment_substate_id = 1 then 1 else 0 end) >= 1 then 1\n", "                when sum(case when master_assortment_substate_id = 3 then 1 else 0 end) >= 1 then 3\n", "                end as master_assortment_substate_id\n", "        from(\n", "            select tea.item_id, tea.be_facility_id, tea.fe_facility_id, master_assortment_substate_id\n", "            from tea_tagging_item tea\n", "            join rpc.product_facility_master_assortment ma on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "    )    \n", "    group by 1)\n", "    group by 1\n", "),\n", "\n", "dau_breakup as(\n", "    select * from supply_etls.dau_coverage\n", "    where insert_ds_ist=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "city_overall as(\n", "    select DISTINCT \n", "        date_,\n", "        city_id,\n", "        city_weight*city_active_express_weight_avail as city_active_express_weight_avail,\n", "        city_weight*city_active_express_binary_avail as city_active_express_binary_avail,\n", "        city_weight*city_active_longtail_weight_avail as city_active_longtail_weight_avail,\n", "        city_weight*city_active_longtail_binary_avail as city_active_longtail_binary_avail,\n", "        city_weight*city_temp_inactive_express_weight_avail as city_temp_inactive_express_weight_avail,\n", "        city_weight*city_temp_inactive_express_binary_avail as city_temp_inactive_express_binary_avail,\n", "        city_weight*city_temp_inactive_longtail_weight_avail as city_temp_inactive_longtail_weight_avail,\n", "        city_weight*city_temp_inactive_longtail_binary_avail as city_temp_inactive_longtail_binary_avail,\n", "        city_weight*city_overall_overall_weight_avail as city_overall_overall_weight_avail,\n", "        city_weight*city_overall_overall_binary_avail as city_overall_overall_binary_avail\n", "        from supply_etls.inventory_core_metrics_v6 icm\n", "        where icm.date_=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "city_overall_final as(\n", "    select date_,\n", "    SUM(city_active_express_weight_avail) as city_active_express_weight_avail,\n", "    SUM(city_active_express_binary_avail) as city_active_express_binary_avail,    \n", "    SUM(city_active_longtail_weight_avail) as city_active_longtail_weight_avail,\n", "    SUM(city_active_longtail_binary_avail) as city_active_longtail_binary_avail,\n", "    SUM(city_temp_inactive_express_weight_avail) as city_temp_inactive_express_weight_avail,\n", "    SUM(city_temp_inactive_express_binary_avail) as city_temp_inactive_express_binary_avail,\n", "    SUM(city_temp_inactive_longtail_weight_avail) as city_temp_inactive_longtail_weight_avail,\n", "    SUM(city_temp_inactive_longtail_binary_avail) as city_temp_inactive_longtail_binary_avail,\n", "    SUM(city_overall_overall_weight_avail) as city_overall_overall_weight_avail,\n", "    SUM(city_overall_overall_binary_avail) as city_overall_overall_binary_avail\n", "    from city_overall\n", "    group by 1\n", "),\n", "\n", "fe_overall as(\n", "select DISTINCT\n", "    date_,\n", "    fe_facility_id,\n", "    current_inventory,\n", "    planned_quantity,\n", "    qty_sold,\n", "    potential_order_quantity,\n", "    planned_quantity_be_avail,\n", "    count_orders,\n", "    gmv,\n", "    fe_regular_storage_capacity,\n", "    fe_regular_onshelf_inventory,\n", "    fe_regular_in_transit_quantity,\n", "    fe_regular_scaled_onshelf_inventory,\n", "    fe_regular_scaled_open_po_qty,\n", "    fe_regular_onshelf_utilisation,\n", "    fe_regular_utilisation_with_open_stos,\n", "    fe_cold_storage_capacity,\n", "    fe_cold_onshelf_inventory,\n", "    fe_cold_in_transit_quantity,\n", "    fe_cold_scaled_onshelf_inventory,\n", "    fe_cold_scaled_open_po_qty,\n", "    fe_cold_onshelf_utilisation,\n", "    fe_cold_utilisation_with_open_stos,\n", "    fe_heavy_storage_capacity,\n", "    fe_heavy_onshelf_inventory,\n", "    fe_heavy_in_transit_quantity,\n", "    fe_heavy_scaled_onshelf_inventory,\n", "    fe_heavy_scaled_open_po_qty,\n", "    fe_heavy_onshelf_utilisation,\n", "    fe_heavy_utilisation_with_open_stos,\n", "    fe_frozen_storage_capacity,\n", "    fe_frozen_onshelf_inventory,\n", "    fe_frozen_in_transit_quantity,\n", "    fe_frozen_scaled_onshelf_inventory,\n", "    fe_frozen_scaled_open_po_qty,\n", "    fe_frozen_onshelf_utilisation,\n", "    fe_frozen_utilisation_with_open_stos,\n", "    \n", "    fe_weight*fe_active_express_weight_avail AS fe_active_express_weight_avail,\n", "    fe_weight*fe_active_express_binary_avail AS fe_active_express_binary_avail,\n", "    fe_weight*fe_active_longtail_weight_avail AS fe_active_longtail_weight_avail,\n", "    fe_weight*fe_active_longtail_binary_avail AS fe_active_longtail_binary_avail,\n", "    fe_weight*fe_temp_inactive_express_weight_avail AS fe_temp_inactive_express_weight_avail,\n", "    fe_weight*fe_temp_inactive_express_binary_avail AS fe_temp_inactive_express_binary_avail,\n", "    fe_weight*fe_temp_inactive_longtail_weight_avail AS fe_temp_inactive_longtail_weight_avail,\n", "    fe_weight*fe_temp_inactive_longtail_binary_avail AS fe_temp_inactive_longtail_binary_avail,\n", "    fe_weight*fe_overall_overall_weight_avail AS fe_overall_overall_weight_avail,\n", "    fe_weight*fe_overall_overall_binary_avail AS fe_overall_overall_binary_avail,\n", "    \n", "    fe_weight*fe_availability_ptype AS fe_availability_ptype,\n", "    \n", "    \n", "    avg_out_of_stock_duration,\n", "    avg_out_of_stock_duration_weighted,\n", "    \n", "    net_inv_present_fe,\n", "    net_inv_exposure_fe,\n", "    net_inv_volume_fe,\n", "    doi_sto_cpd_basis_fe,\n", "    doi_po_cpd_basis_fe,\n", "    sto_cpd_fe,\n", "    po_cpd_fe,\n", "    excess_inv_fe,\n", "    excess_inv_exposure_fe,\n", "    excess_inv_volume_fe,\n", "    no_sale_inv_fe,\n", "    no_sale_inv_exposure_fe,\n", "    no_sale_inv_volume_fe,\n", "    cpd_depleted_inv_fe,\n", "    cpd_depleted_inv_exposure_fe,\n", "    cpd_depleted_inv_volume_fe,\n", "    expiring_inv_fe,\n", "    expiring_inv_exposure_fe,\n", "    expiring_inv_volume_fe,\n", "    overall_dead_fe,\n", "    overall_dead_exposure_fe,\n", "    overall_dead_volume_fe\n", "    \n", "    from supply_etls.inventory_core_metrics_v6 icm\n", "    where icm.date_=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "fe_overall_final as(\n", "    select date_,\n", "    SUM(current_inventory) as current_inventory,\n", "    SUM(planned_quantity) as planned_quantity,\n", "    SUM(qty_sold) as qty_sold,\n", "    SUM(potential_order_quantity) as potential_order_quantity,\n", "    SUM(planned_quantity_be_avail) as planned_quantity_be_avail,\n", "    SUM(count_orders) as count_orders,\n", "    SUM(gmv) as gmv,\n", "    SUM(fe_regular_storage_capacity) as fe_regular_storage_capacity,\n", "    SUM(fe_regular_onshelf_inventory) as fe_regular_onshelf_inventory,\n", "    SUM(fe_regular_in_transit_quantity) as fe_regular_in_transit_quantity,\n", "    SUM(fe_regular_scaled_onshelf_inventory) as fe_regular_scaled_onshelf_inventory,\n", "    AVG(fe_regular_onshelf_utilisation) as fe_regular_onshelf_utilisation,\n", "    SUM(fe_regular_scaled_open_po_qty) as fe_regular_scaled_open_po_qty,\n", "    AVG(fe_regular_utilisation_with_open_stos) as fe_regular_utilisation_with_open_stos,\n", "    SUM(fe_cold_storage_capacity) as fe_cold_storage_capacity,\n", "    SUM(fe_cold_onshelf_inventory) as fe_cold_onshelf_inventory,\n", "    SUM(fe_cold_in_transit_quantity) as fe_cold_in_transit_quantity,\n", "    SUM(fe_cold_scaled_onshelf_inventory) as fe_cold_scaled_onshelf_inventory,\n", "    SUM(fe_cold_scaled_open_po_qty) as fe_cold_scaled_open_po_qty,\n", "    AVG(fe_cold_onshelf_utilisation) as fe_cold_onshelf_utilisation,\n", "    AVG(fe_cold_utilisation_with_open_stos) as fe_cold_utilisation_with_open_stos,\n", "    SUM(fe_heavy_storage_capacity) as fe_heavy_storage_capacity,\n", "    SUM(fe_heavy_onshelf_inventory) as fe_heavy_onshelf_inventory,\n", "    SUM(fe_heavy_in_transit_quantity) as fe_heavy_in_transit_quantity,\n", "    SUM(fe_heavy_scaled_onshelf_inventory) as fe_heavy_scaled_onshelf_inventory,\n", "    SUM(fe_heavy_scaled_open_po_qty) as fe_heavy_scaled_open_po_qty,\n", "    AVG(fe_heavy_onshelf_utilisation) as fe_heavy_onshelf_utilisation,\n", "    AVG(fe_heavy_utilisation_with_open_stos) as fe_heavy_utilisation_with_open_stos,\n", "    SUM(fe_frozen_storage_capacity) as fe_frozen_storage_capacity,\n", "    SUM(fe_frozen_onshelf_inventory) as fe_frozen_onshelf_inventory,\n", "    SUM(fe_frozen_in_transit_quantity) as fe_frozen_in_transit_quantity,\n", "    SUM(fe_frozen_scaled_onshelf_inventory) as fe_frozen_scaled_onshelf_inventory,\n", "    SUM(fe_frozen_scaled_open_po_qty) as fe_frozen_scaled_open_po_qty,\n", "    AVG(fe_frozen_onshelf_utilisation) as fe_frozen_onshelf_utilisation,\n", "    AVG(fe_frozen_utilisation_with_open_stos) as fe_frozen_utilisation_with_open_stos,\n", "    \n", "    SUM(fe_active_express_weight_avail) AS fe_active_express_weight_avail,\n", "    SUM(fe_active_express_binary_avail) AS fe_active_express_binary_avail,\n", "    SUM(fe_active_longtail_weight_avail) AS fe_active_longtail_weight_avail,\n", "    SUM(fe_active_longtail_binary_avail) AS fe_active_longtail_binary_avail,\n", "    SUM(fe_temp_inactive_express_weight_avail) AS fe_temp_inactive_express_weight_avail,\n", "    SUM(fe_temp_inactive_express_binary_avail) AS fe_temp_inactive_express_binary_avail,\n", "    SUM(fe_temp_inactive_longtail_weight_avail) AS fe_temp_inactive_longtail_weight_avail,\n", "    SUM(fe_temp_inactive_longtail_binary_avail) AS fe_temp_inactive_longtail_binary_avail,\n", "    SUM(fe_overall_overall_weight_avail) AS fe_overall_overall_weight_avail,\n", "    SUM(fe_overall_overall_binary_avail) AS fe_overall_overall_binary_avail,\n", "    \n", "    SUM(fe_availability_ptype) AS fe_availability_ptype,\n", "    \n", "    AVG(avg_out_of_stock_duration) as avg_out_of_stock_duration,\n", "    AVG(avg_out_of_stock_duration_weighted) as avg_out_of_stock_duration_weighted,\n", "    \n", "    SUM(net_inv_present_fe) as net_inv_present_fe, \n", "    SUM(net_inv_exposure_fe) as net_inv_exposure_fe,\n", "    SUM(net_inv_volume_fe) as net_inv_volume_fe,\n", "    AVG(doi_sto_cpd_basis_fe) as doi_sto_cpd_basis_fe,\n", "    AVG(doi_po_cpd_basis_fe) as doi_po_cpd_basis_fe,\n", "    SUM(sto_cpd_fe) as sto_cpd_fe,\n", "    SUM(po_cpd_fe) as po_cpd_fe,\n", "    SUM(excess_inv_fe) as excess_inv_fe,\n", "    SUM(excess_inv_exposure_fe) as excess_inv_exposure_fe,\n", "    SUM(excess_inv_volume_fe) as excess_inv_volume_fe,\n", "    SUM(no_sale_inv_fe) as no_sale_inv_fe,\n", "    SUM(no_sale_inv_exposure_fe) as no_sale_inv_exposure_fe,\n", "    SUM(no_sale_inv_volume_fe) as no_sale_inv_volume_fe,\n", "    SUM(cpd_depleted_inv_fe) as cpd_depleted_inv_fe,\n", "    SUM(cpd_depleted_inv_exposure_fe) as cpd_depleted_inv_exposure_fe,\n", "    SUM(cpd_depleted_inv_volume_fe) as cpd_depleted_inv_volume_fe,\n", "    SUM(expiring_inv_fe) as expiring_inv_fe,\n", "    SUM(expiring_inv_exposure_fe) as expiring_inv_exposure_fe,\n", "    SUM(expiring_inv_volume_fe) as expiring_inv_volume_fe,\n", "    SUM(overall_dead_fe) as overall_dead_fe,\n", "    SUM(overall_dead_exposure_fe) as overall_dead_exposure_fe,\n", "    SUM(overall_dead_volume_fe) as overall_dead_volume_fe\n", "\n", "    from fe_overall\n", "    group by 1\n", "),\n", "\n", "be_overall as(\n", "select DISTINCT\n", "    date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "    be_facility_id,\n", "    picking_qty_util,\n", "    picking_qty_cap,\n", "    picking_qty_util_per,\n", "    picking_sku_util,\n", "    picking_sku_cap,\n", "    picking_sku_util_per,\n", "    be_regular_storage_capacity,\n", "    be_regular_onshelf_inventory,\n", "    be_regular_in_transit_quantity,\n", "    be_regular_scaled_onshelf_inventory,\n", "    be_regular_onshelf_utilisation,\n", "    be_regular_scaled_open_po_qty,\n", "    be_regular_utilisation_with_open_stos,\n", "    be_cold_storage_capacity,\n", "    be_cold_onshelf_inventory,\n", "    be_cold_in_transit_quantity,\n", "    be_cold_scaled_onshelf_inventory,\n", "    be_cold_scaled_open_po_qty,\n", "    be_cold_onshelf_utilisation,\n", "    be_cold_utilisation_with_open_stos,\n", "    be_heavy_storage_capacity,\n", "    be_heavy_onshelf_inventory,\n", "    be_heavy_in_transit_quantity,\n", "    be_heavy_scaled_onshelf_inventory,\n", "    be_heavy_scaled_open_po_qty,\n", "    be_heavy_onshelf_utilisation,\n", "    be_heavy_utilisation_with_open_stos,\n", "    be_frozen_storage_capacity,\n", "    be_frozen_onshelf_inventory,\n", "    be_frozen_in_transit_quantity,\n", "    be_frozen_scaled_onshelf_inventory,\n", "    be_frozen_scaled_open_po_qty,\n", "    be_frozen_onshelf_utilisation,\n", "    be_frozen_utilisation_with_open_stos,\n", "    wotif_created_qty_FE,\n", "    wotif_on_time_picked_qty_FE,\n", "    wotif_on_time_packaged_qty_FE,\n", "    wotif_on_time_sorted_qty_FE,\n", "    wotif_on_time_dispatched_qty_FE,\n", "    wotif_created_qty_BE,\n", "    wotif_on_time_picked_qty_BE,\n", "    wotif_on_time_packaged_qty_BE,\n", "    wotif_on_time_sorted_qty_BE,\n", "    wotif_on_time_dispatched_qty_BE,\n", "    \n", "    be_weight*be_active_express_weight_avail AS be_active_express_weight_avail,\n", "    be_weight*be_active_express_binary_avail AS be_active_express_binary_avail,\n", "    be_weight*be_active_longtail_weight_avail AS be_active_longtail_weight_avail,\n", "    be_weight*be_active_longtail_binary_avail AS be_active_longtail_binary_avail,\n", "    be_weight*be_temp_inactive_express_weight_avail AS be_temp_inactive_express_weight_avail,\n", "    be_weight*be_temp_inactive_express_binary_avail AS be_temp_inactive_express_binary_avail,\n", "    be_weight*be_temp_inactive_longtail_weight_avail AS be_temp_inactive_longtail_weight_avail,\n", "    be_weight*be_temp_inactive_longtail_binary_avail AS be_temp_inactive_longtail_binary_avail,\n", "    be_weight*be_overall_overall_weight_avail AS be_overall_overall_weight_avail,\n", "    be_weight*be_overall_overall_binary_avail AS be_overall_overall_binary_avail,\n", "    \n", "    be_weight*bexfe_active_express_weight_avail AS bexfe_active_express_weight_avail,\n", "    be_weight*bexfe_active_express_binary_avail AS bexfe_active_express_binary_avail,\n", "    be_weight*bexfe_active_longtail_weight_avail AS bexfe_active_longtail_weight_avail,\n", "    be_weight*bexfe_active_longtail_binary_avail AS bexfe_active_longtail_binary_avail,\n", "    be_weight*bexfe_temp_inactive_express_weight_avail AS bexfe_temp_inactive_express_weight_avail,\n", "    be_weight*bexfe_temp_inactive_express_binary_avail AS bexfe_temp_inactive_express_binary_avail,\n", "    be_weight*bexfe_temp_inactive_longtail_weight_avail AS bexfe_temp_inactive_longtail_weight_avail,\n", "    be_weight*bexfe_temp_inactive_longtail_binary_avail AS bexfe_temp_inactive_longtail_binary_avail,\n", "    be_weight*bexfe_overall_overall_weight_avail AS bexfe_overall_overall_weight_avail,\n", "    be_weight*bexfe_overall_overall_binary_avail AS bexfe_overall_overall_binary_avail,\n", "    \n", "    be_weight*be_fe_active_express_weight_avail AS be_fe_active_express_weight_avail,\n", "    be_weight*be_fe_active_express_binary_avail AS be_fe_active_express_binary_avail,\n", "    be_weight*be_fe_active_longtail_weight_avail AS be_fe_active_longtail_weight_avail,\n", "    be_weight*be_fe_active_longtail_binary_avail AS be_fe_active_longtail_binary_avail,\n", "    be_weight*be_fe_temp_inactive_express_weight_avail AS be_fe_temp_inactive_express_weight_avail,\n", "    be_weight*be_fe_temp_inactive_express_binary_avail AS be_fe_temp_inactive_express_binary_avail,\n", "    be_weight*be_fe_temp_inactive_longtail_weight_avail AS be_fe_temp_inactive_longtail_weight_avail,\n", "    be_weight*be_fe_temp_inactive_longtail_binary_avail AS be_fe_temp_inactive_longtail_binary_avail,\n", "    be_weight*be_fe_overall_overall_weight_avail AS be_fe_overall_overall_weight_avail,\n", "    be_weight*be_fe_overall_overall_binary_avail AS be_fe_overall_overall_binary_avail,\n", "    \n", "    be_weight*bexfe_be_availability_ptype AS bexfe_be_availability_ptype,\n", "    be_weight*bexfe_fe_availability_ptype AS bexfe_fe_availability_ptype,\n", "    \n", "    lines_active,\n", "    lines_temp_inactive_skus,\n", "    count_active_skus_be,\n", "    count_inactive_skus_be,\n", "    \n", "    net_inv_present_be,\n", "    net_inv_exposure_be,\n", "    net_inv_volume_be,\n", "    doi_sto_cpd_basis_be,\n", "    doi_po_cpd_basis_be,\n", "    sto_cpd_be,\n", "    po_cpd_be,\n", "    excess_inv_be,\n", "    excess_inv_exposure_be,\n", "    excess_inv_volume_be,\n", "    no_sale_inv_be,\n", "    no_sale_inv_exposure_be,\n", "    no_sale_inv_volume_be,\n", "    cpd_depleted_inv_be,\n", "    cpd_depleted_inv_exposure_be,\n", "    cpd_depleted_inv_volume_be,\n", "    expiring_inv_be,\n", "    expiring_inv_exposure_be,\n", "    expiring_inv_volume_be,\n", "    overall_dead_be,\n", "    overall_dead_exposure_be,\n", "    overall_dead_volume_be\n", "    \n", "    from supply_etls.inventory_core_metrics_v6 icm\n", "    where icm.date_=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "be_overall_final as(\n", "    select date_,\n", "    SUM(picking_qty_util) AS picking_qty_util,\n", "    SUM(picking_qty_cap) AS picking_qty_cap,\n", "    AVG(picking_qty_util_per) AS picking_qty_util_per,\n", "    SUM(picking_sku_util) AS picking_sku_util,\n", "    SUM(picking_sku_cap) AS picking_sku_cap,\n", "    AVG(picking_sku_util_per) AS picking_sku_util_per,\n", "    SUM(be_regular_storage_capacity) AS be_regular_storage_capacity,\n", "    SUM(be_regular_onshelf_inventory) AS be_regular_onshelf_inventory,\n", "    SUM(be_regular_in_transit_quantity) AS be_regular_in_transit_quantity,\n", "    SUM(be_regular_scaled_onshelf_inventory) AS be_regular_scaled_onshelf_inventory,\n", "    AVG(be_regular_onshelf_utilisation) AS be_regular_onshelf_utilisation,\n", "    SUM(be_regular_scaled_open_po_qty) AS be_regular_scaled_open_po_qty,\n", "    AVG(be_regular_utilisation_with_open_stos) AS be_regular_utilisation_with_open_stos,\n", "    SUM(be_cold_storage_capacity) AS be_cold_storage_capacity,\n", "    SUM(be_cold_onshelf_inventory) AS be_cold_onshelf_inventory,\n", "    SUM(be_cold_in_transit_quantity) AS be_cold_in_transit_quantity,\n", "    SUM(be_cold_scaled_onshelf_inventory) AS be_cold_scaled_onshelf_inventory,\n", "    SUM(be_cold_scaled_open_po_qty) AS be_cold_scaled_open_po_qty,\n", "    AVG(be_cold_onshelf_utilisation) AS be_cold_onshelf_utilisation,\n", "    AVG(be_cold_utilisation_with_open_stos) AS be_cold_utilisation_with_open_stos,\n", "    SUM(be_heavy_storage_capacity) AS be_heavy_storage_capacity,\n", "    SUM(be_heavy_onshelf_inventory) AS be_heavy_onshelf_inventory,\n", "    SUM(be_heavy_in_transit_quantity) AS be_heavy_in_transit_quantity,\n", "    SUM(be_heavy_scaled_onshelf_inventory) AS be_heavy_scaled_onshelf_inventory,\n", "    SUM(be_heavy_scaled_open_po_qty) AS be_heavy_scaled_open_po_qty,\n", "    AVG(be_heavy_onshelf_utilisation) AS be_heavy_onshelf_utilisation,\n", "    AVG(be_heavy_utilisation_with_open_stos) AS be_heavy_utilisation_with_open_stos,\n", "    SUM(be_frozen_storage_capacity) AS be_frozen_storage_capacity,\n", "    SUM(be_frozen_onshelf_inventory) AS be_frozen_onshelf_inventory,\n", "    SUM(be_frozen_in_transit_quantity) AS be_frozen_in_transit_quantity,\n", "    SUM(be_frozen_scaled_onshelf_inventory) AS be_frozen_scaled_onshelf_inventory,\n", "    SUM(be_frozen_scaled_open_po_qty) AS be_frozen_scaled_open_po_qty,\n", "    AVG(be_frozen_onshelf_utilisation) AS be_frozen_onshelf_utilisation,\n", "    AVG(be_frozen_utilisation_with_open_stos) AS be_frozen_utilisation_with_open_stos,\n", "    SUM(wotif_created_qty_FE) AS wotif_created_qty_FE,\n", "    SUM(wotif_on_time_picked_qty_FE) AS wotif_on_time_picked_qty_FE,\n", "    SUM(wotif_on_time_packaged_qty_FE) AS wotif_on_time_packaged_qty_FE,\n", "    SUM(wotif_on_time_sorted_qty_FE) AS wotif_on_time_sorted_qty_FE,\n", "    SUM(wotif_on_time_dispatched_qty_FE) AS wotif_on_time_dispatched_qty_FE,\n", "    SUM(wotif_created_qty_BE) AS wotif_created_qty_BE,\n", "    SUM(wotif_on_time_picked_qty_BE) AS wotif_on_time_picked_qty_BE,\n", "    SUM(wotif_on_time_packaged_qty_BE) AS wotif_on_time_packaged_qty_BE,\n", "    SUM(wotif_on_time_sorted_qty_BE) AS wotif_on_time_sorted_qty_BE,\n", "    SUM(wotif_on_time_dispatched_qty_BE) AS wotif_on_time_dispatched_qty_BE,\n", "    SUM(be_active_express_weight_avail) AS be_active_express_weight_avail,\n", "    SUM(be_active_express_binary_avail) AS be_active_express_binary_avail,\n", "    SUM(be_active_longtail_weight_avail) AS be_active_longtail_weight_avail,\n", "    SUM(be_active_longtail_binary_avail) AS be_active_longtail_binary_avail,\n", "    SUM(be_temp_inactive_express_weight_avail) AS be_temp_inactive_express_weight_avail,\n", "    SUM(be_temp_inactive_express_binary_avail) AS be_temp_inactive_express_binary_avail,\n", "    SUM(be_temp_inactive_longtail_weight_avail) AS be_temp_inactive_longtail_weight_avail,\n", "    SUM(be_temp_inactive_longtail_binary_avail) AS be_temp_inactive_longtail_binary_avail,\n", "    SUM(be_overall_overall_weight_avail) AS be_overall_overall_weight_avail,\n", "    SUM(be_overall_overall_binary_avail) AS be_overall_overall_binary_avail,\n", "    \n", "    SUM(be_fe_active_express_weight_avail) AS be_fe_active_express_weight_avail,\n", "    SUM(be_fe_active_express_binary_avail) AS be_fe_active_express_binary_avail,\n", "    SUM(be_fe_active_longtail_weight_avail) AS be_fe_active_longtail_weight_avail,\n", "    SUM(be_fe_active_longtail_binary_avail) AS be_fe_active_longtail_binary_avail,\n", "    SUM(be_fe_temp_inactive_express_weight_avail) AS be_fe_temp_inactive_express_weight_avail,\n", "    SUM(be_fe_temp_inactive_express_binary_avail) AS be_fe_temp_inactive_express_binary_avail,\n", "    SUM(be_fe_temp_inactive_longtail_weight_avail) AS be_fe_temp_inactive_longtail_weight_avail,\n", "    SUM(be_fe_temp_inactive_longtail_binary_avail) AS be_fe_temp_inactive_longtail_binary_avail,\n", "    SUM(be_fe_overall_overall_weight_avail) AS be_fe_overall_overall_weight_avail,\n", "    SUM(be_fe_overall_overall_binary_avail) AS be_fe_overall_overall_binary_avail,\n", "    \n", "    SUM(bexfe_active_express_weight_avail) AS bexfe_active_express_weight_avail,\n", "    SUM(bexfe_active_express_binary_avail) AS bexfe_active_express_binary_avail,\n", "    SUM(bexfe_active_longtail_weight_avail) AS bexfe_active_longtail_weight_avail,\n", "    SUM(bexfe_active_longtail_binary_avail) AS bexfe_active_longtail_binary_avail,\n", "    SUM(bexfe_temp_inactive_express_weight_avail) AS bexfe_temp_inactive_express_weight_avail,\n", "    SUM(bexfe_temp_inactive_express_binary_avail) AS bexfe_temp_inactive_express_binary_avail,\n", "    SUM(bexfe_temp_inactive_longtail_weight_avail) AS bexfe_temp_inactive_longtail_weight_avail,\n", "    SUM(bexfe_temp_inactive_longtail_binary_avail) AS bexfe_temp_inactive_longtail_binary_avail,\n", "    SUM(bexfe_overall_overall_weight_avail) AS bexfe_overall_overall_weight_avail,\n", "    SUM(bexfe_overall_overall_binary_avail) AS bexfe_overall_overall_binary_avail,\n", "    \n", "    SUM(bexfe_be_availability_ptype) AS bexfe_be_availability_ptype,\n", "    SUM(bexfe_fe_availability_ptype) AS bexfe_fe_availability_ptype,\n", "    \n", "    SUM(lines_active) as lines_active,\n", "    SUM(lines_temp_inactive_skus) as lines_temp_inactive_skus,\n", "    SUM(count_active_skus_be) as count_active_skus_be,\n", "    SUM(count_inactive_skus_be) as count_inactive_skus_be,\n", "    \n", "    SUM(net_inv_present_be) as net_inv_present_be, \n", "    SUM(net_inv_exposure_be) as net_inv_exposure_be,\n", "    SUM(net_inv_volume_be) as net_inv_volume_be,\n", "    AVG(doi_sto_cpd_basis_be) as doi_sto_cpd_basis_be,\n", "    AVG(doi_po_cpd_basis_be) as doi_po_cpd_basis_be,\n", "    SUM(sto_cpd_be) as sto_cpd_be,\n", "    SUM(po_cpd_be) as po_cpd_be,\n", "    SUM(excess_inv_be) as excess_inv_be,\n", "    SUM(excess_inv_exposure_be) as excess_inv_exposure_be,\n", "    SUM(excess_inv_volume_be) as excess_inv_volume_be,\n", "    SUM(no_sale_inv_be) as no_sale_inv_be,\n", "    SUM(no_sale_inv_exposure_be) as no_sale_inv_exposure_be,\n", "    SUM(no_sale_inv_volume_be) as no_sale_inv_volume_be,\n", "    SUM(cpd_depleted_inv_be) as cpd_depleted_inv_be,\n", "    SUM(cpd_depleted_inv_exposure_be) as cpd_depleted_inv_exposure_be,\n", "    SUM(cpd_depleted_inv_volume_be) as cpd_depleted_inv_volume_be,\n", "    SUM(expiring_inv_be) as expiring_inv_be,\n", "    SUM(expiring_inv_exposure_be) as expiring_inv_exposure_be,\n", "    SUM(expiring_inv_volume_be) as expiring_inv_volume_be,\n", "    SUM(overall_dead_be) as overall_dead_be,\n", "    SUM(overall_dead_exposure_be) as overall_dead_exposure_be,\n", "    SUM(overall_dead_volume_be) as overall_dead_volume_be\n", "    \n", "    from be_overall\n", "    group by 1\n", "),\n", "    \n", "pan_india as(\n", "    select  \n", "            date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "            0 as city_id,\n", "            '.PAN India' as city_name,\n", "            0 as be_facility_id,\n", "            0 as be_inv_outlet_id,\n", "            '.PAN India' as be_facility_name,\n", "            MAX(dpa.no_of_ds_live) as no_of_ds_live,\n", "            MAX(dpa.no_of_ds_live_express) as no_of_ds_live_express,\n", "            MAX(dpa.no_of_ds_live_longtail) as no_of_ds_live_longtail,\n", "            0 as fe_facility_id,\n", "            0 as fe_outlet_id,\n", "            '.PAN India' as fe_facility_name,\n", "    \n", "            SUM(icm.total_loss) as total_loss,\n", "            \n", "            0 as fe_cpd,\n", "            0 as fe_weight,\n", "            0 as be_cpd,\n", "            0 as be_weight,\n", "            0 as city_cpd,\n", "            0 as city_weight,\n", "    \n", "    MAX(co.city_active_express_weight_avail) as city_active_express_weight_avail,\n", "    MAX(co.city_active_express_binary_avail) as city_active_express_binary_avail,\n", "    MAX(co.city_active_longtail_weight_avail) as city_active_longtail_weight_avail,\n", "    MAX(co.city_active_longtail_binary_avail) as city_active_longtail_binary_avail,\n", "    MAX(co.city_temp_inactive_express_weight_avail) as city_temp_inactive_express_weight_avail,\n", "    MAX(co.city_temp_inactive_express_binary_avail) as city_temp_inactive_express_binary_avail,\n", "    MAX(co.city_temp_inactive_longtail_weight_avail) as city_temp_inactive_longtail_weight_avail,\n", "    MAX(co.city_temp_inactive_longtail_binary_avail) as city_temp_inactive_longtail_binary_avail,\n", "    MAX(co.city_overall_overall_weight_avail) as city_overall_overall_weight_avail,\n", "    MAX(co.city_overall_overall_binary_avail) as city_overall_overall_binary_avail,\n", "\n", "    MAX(bo.be_active_express_weight_avail) as be_active_express_weight_avail,\n", "    MAX(bo.be_active_express_binary_avail) as be_active_express_binary_avail,\n", "    MAX(bo.be_active_longtail_weight_avail) as be_active_longtail_weight_avail,\n", "    MAX(bo.be_active_longtail_binary_avail) as be_active_longtail_binary_avail,\n", "    MAX(bo.be_temp_inactive_express_weight_avail) as be_temp_inactive_express_weight_avail,\n", "    MAX(bo.be_temp_inactive_express_binary_avail) as be_temp_inactive_express_binary_avail,\n", "    MAX(bo.be_temp_inactive_longtail_weight_avail) as be_temp_inactive_longtail_weight_avail,\n", "    MAX(bo.be_temp_inactive_longtail_binary_avail) as be_temp_inactive_longtail_binary_avail,\n", "    MAX(bo.be_overall_overall_weight_avail) as be_overall_overall_weight_avail,\n", "    MAX(bo.be_overall_overall_binary_avail) as be_overall_overall_binary_avail,\n", "\n", "    MAX(fo.fe_active_express_weight_avail) as fe_active_express_weight_avail,\n", "    MAX(fo.fe_active_express_binary_avail) as fe_active_express_binary_avail,\n", "    MAX(fo.fe_active_longtail_weight_avail) as fe_active_longtail_weight_avail,\n", "    MAX(fo.fe_active_longtail_binary_avail) as fe_active_longtail_binary_avail,\n", "    MAX(fo.fe_temp_inactive_express_weight_avail) as fe_temp_inactive_express_weight_avail,\n", "    MAX(fo.fe_temp_inactive_express_binary_avail) as fe_temp_inactive_express_binary_avail,\n", "    MAX(fo.fe_temp_inactive_longtail_weight_avail) as fe_temp_inactive_longtail_weight_avail,\n", "    MAX(fo.fe_temp_inactive_longtail_binary_avail) as fe_temp_inactive_longtail_binary_avail,\n", "    MAX(fo.fe_overall_overall_weight_avail) as fe_overall_overall_weight_avail,\n", "    MAX(fo.fe_overall_overall_binary_avail) as fe_overall_overall_binary_avail,\n", "\n", "    MAX(bo.be_fe_active_express_weight_avail) as be_fe_active_express_weight_avail,\n", "    MAX(bo.be_fe_active_express_binary_avail) as be_fe_active_express_binary_avail,\n", "    MAX(bo.be_fe_active_longtail_weight_avail) as be_fe_active_longtail_weight_avail,\n", "    MAX(bo.be_fe_active_longtail_binary_avail) as be_fe_active_longtail_binary_avail,\n", "    MAX(bo.be_fe_temp_inactive_express_weight_avail) as be_fe_temp_inactive_express_weight_avail,\n", "    MAX(bo.be_fe_temp_inactive_express_binary_avail) as be_fe_temp_inactive_express_binary_avail,\n", "    MAX(bo.be_fe_temp_inactive_longtail_weight_avail) as be_fe_temp_inactive_longtail_weight_avail,\n", "    MAX(bo.be_fe_temp_inactive_longtail_binary_avail) as be_fe_temp_inactive_longtail_binary_avail,\n", "    MAX(bo.be_fe_overall_overall_weight_avail) as be_fe_overall_overall_weight_avail,\n", "    MAX(bo.be_fe_overall_overall_binary_avail) as be_fe_overall_overall_binary_avail,\n", "    \n", "    MAX(bo.bexfe_active_express_weight_avail) as bexfe_active_express_weight_avail,\n", "    MAX(bo.bexfe_active_express_binary_avail) as bexfe_active_express_binary_avail,\n", "    MAX(bo.bexfe_active_longtail_weight_avail) as bexfe_active_longtail_weight_avail,\n", "    MAX(bo.bexfe_active_longtail_binary_avail) as bexfe_active_longtail_binary_avail,\n", "    MAX(bo.bexfe_temp_inactive_express_weight_avail) as bexfe_temp_inactive_express_weight_avail,\n", "    MAX(bo.bexfe_temp_inactive_express_binary_avail) as bexfe_temp_inactive_express_binary_avail,\n", "    MAX(bo.bexfe_temp_inactive_longtail_weight_avail) as bexfe_temp_inactive_longtail_weight_avail,\n", "    MAX(bo.bexfe_temp_inactive_longtail_binary_avail) as bexfe_temp_inactive_longtail_binary_avail,\n", "    MAX(bo.bexfe_overall_overall_weight_avail) as bexfe_overall_overall_weight_avail,\n", "    MAX(bo.bexfe_overall_overall_binary_avail) as bexfe_overall_overall_binary_avail,\n", "    \n", "    MAX(fo.current_inventory) as current_inventory,\n", "    \n", "    SUM(icm.total_dn_qty) as total_dn_qty,\n", "    SUM(icm.dn_short_qty) as dn_short_qty,\n", "    SUM(icm.dn_other_damage_qty) as dn_other_damage_qty,\n", "    SUM(icm.transfer_loss_qty) as transfer_loss_qty,\n", "    SUM(icm.transfer_loss_amt) as transfer_loss_amt,\n", "    SUM(icm.total_dn_amt) as total_dn_amt,\n", "    SUM(icm.dn_short_amt) as dn_short_amt,\n", "    SUM(icm.dn_other_damage_amt) as dn_other_damage_amt,\n", "    SUM(icm.dn_other_qty) as dn_other_qty,\n", "    SUM(icm.dn_other_amt) as dn_other_amt,\n", "    SUM(icm.dn_other_excess_item_qty) as dn_other_excess_item_qty,\n", "    SUM(icm.dn_other_excess_item_amt) as dn_other_excess_item_amt,\n", "    SUM(icm.dn_other_exp_nte_qty) as dn_other_exp_nte_qty,\n", "    SUM(icm.dn_other_exp_nte_amt) as dn_other_exp_nte_amt,\n", "    SUM(icm.dn_other_freebie_missing_qty) as dn_other_freebie_missing_qty,\n", "    SUM(icm.dn_other_freebie_missing_amt) as dn_other_freebie_missing_amt,\n", "    SUM(icm.dn_other_misc_qty) as dn_other_misc_qty,\n", "    SUM(icm.dn_other_misc_amt) as dn_other_misc_amt,\n", "    SUM(icm.dn_other_quality_issue_qty) as dn_other_quality_issue_qty,\n", "    SUM(icm.dn_other_quality_issue_amt) as dn_other_quality_issue_amt,\n", "    SUM(icm.dn_other_upc_not_scannable_qty) as dn_other_upc_not_scannable_qty,\n", "    SUM(icm.dn_other_upc_not_scannable_amt) as dn_other_upc_not_scannable_amt,\n", "    SUM(icm.dn_other_variant_mismatch_qty) as dn_other_variant_mismatch_qty,\n", "    SUM(icm.dn_other_variant_mismatch_amt) as dn_other_variant_mismatch_amt,\n", "    SUM(icm.dn_other_wrong_item_qty) as dn_other_wrong_item_qty,\n", "    SUM(icm.dn_other_wrong_item_amt) as dn_other_wrong_item_amt,\n", "    SUM(icm.lost_in_transit_positive_qty) as lost_in_transit_positive_qty,\n", "    SUM(icm.lost_in_transit_positive_amt) as lost_in_transit_positive_amt,\n", "    SUM(icm.lost_in_transit_negative_qty) as lost_in_transit_negative_qty,\n", "    SUM(icm.lost_in_transit_negative_amt) as lost_in_transit_negative_amt, \n", "    SUM(icm.b2b_amt) as b2b_amt,\n", "    SUM(icm.rsto_dump_amt) as rsto_dump_amt,\n", "    SUM(icm.grn_amt) as grn_amt,\n", "    SUM(icm.billed_amt) as billed_amt, \n", "    \n", "    SUM(icm.fvcpu_indent) as fvcpu_indent,\n", "    SUM(icm.fvcpu_indent_wt) as fvcpu_indent_wt,\n", "    SUM(icm.fvcpu_total_con_cost) as fvcpu_total_con_cost,\n", "    SUM(icm.fvcpu_per_unit_total_con_cost) as fvcpu_per_unit_total_con_cost,\n", "    \n", "    SUM(icm.esto_excess_qty) as esto_excess_qty,\n", "    SUM(icm.esto_excess_value) as esto_excess_value,\n", "    \n", "    SUM(icm.sto_quantity) as sto_quantity,\n", "    SUM(icm.v1_line_items) as v1_line_items,\n", "    SUM(icm.v2_line_items) as v2_line_items,\n", "    \n", "    MAX(fo.planned_quantity) as planned_quantity,\n", "    MAX(fo.qty_sold) as qty_sold,\n", "    MAX(fo.potential_order_quantity) as potential_order_quantity,\n", "    MAX(fo.planned_quantity_be_avail) as planned_quantity_be_avail,\n", "    MAX(fo.count_orders) as count_orders,\n", "    MAX(fo.gmv) as gmv,\n", "    \n", "    MAX(bo.picking_qty_util) as picking_qty_util,\n", "    MAX(bo.picking_qty_cap) as picking_qty_cap,\n", "    MAX(bo.picking_qty_util_per) as picking_qty_util_per,\n", "    MAX(bo.picking_sku_util) as picking_sku_util,\n", "    MAX(bo.picking_sku_cap) as picking_sku_cap,\n", "    MAX(bo.picking_sku_util_per) as picking_sku_util_per,\n", "    \n", "    SUM(icm.inward_util) as inward_util,\n", "    SUM(icm.inward_cap_in_qty) as inward_cap_in_qty,\n", "    AVG(icm.inward_util_per) as inward_util_per,\n", "    SUM(icm.truck_load_util) as truck_load_util,\n", "    SUM(icm.truck_load_cap_in_kg) as truck_load_cap_in_kg,\n", "    AVG(icm.truck_load_util_per) as truck_load_util_per,\n", "    \n", "    MAX(bo.be_regular_storage_capacity) as be_regular_storage_capacity,\n", "    MAX(bo.be_regular_onshelf_inventory) as be_regular_onshelf_inventory,\n", "    MAX(bo.be_regular_in_transit_quantity) as be_regular_in_transit_quantity,\n", "    MAX(bo.be_regular_scaled_onshelf_inventory) as be_regular_scaled_onshelf_inventory,\n", "    MAX(bo.be_regular_onshelf_utilisation) as be_regular_onshelf_utilisation,\n", "    MAX(bo.be_regular_scaled_open_po_qty) as be_regular_scaled_open_po_qty,\n", "    MAX(bo.be_regular_utilisation_with_open_stos) as be_regular_utilisation_with_open_stos,\n", "    MAX(bo.be_cold_storage_capacity) as be_cold_storage_capacity,\n", "    MAX(bo.be_cold_onshelf_inventory) as be_cold_onshelf_inventory,\n", "    MAX(bo.be_cold_in_transit_quantity) as be_cold_in_transit_quantity,\n", "    MAX(bo.be_cold_scaled_onshelf_inventory) as be_cold_scaled_onshelf_inventory,\n", "    MAX(bo.be_cold_scaled_open_po_qty) as be_cold_scaled_open_po_qty,\n", "    MAX(bo.be_cold_onshelf_utilisation) as be_cold_onshelf_utilisation,\n", "    MAX(bo.be_cold_utilisation_with_open_stos) as be_cold_utilisation_with_open_stos,\n", "    MAX(bo.be_heavy_storage_capacity) as be_heavy_storage_capacity,\n", "    MAX(bo.be_heavy_onshelf_inventory) as be_heavy_onshelf_inventory,\n", "    MAX(bo.be_heavy_in_transit_quantity) as be_heavy_in_transit_quantity,\n", "    MAX(bo.be_heavy_scaled_onshelf_inventory) as be_heavy_scaled_onshelf_inventory,\n", "    MAX(bo.be_heavy_scaled_open_po_qty) as be_heavy_scaled_open_po_qty,\n", "    MAX(bo.be_heavy_onshelf_utilisation) as be_heavy_onshelf_utilisation,\n", "    MAX(bo.be_heavy_utilisation_with_open_stos) as be_heavy_utilisation_with_open_stos,\n", "    MAX(bo.be_frozen_storage_capacity) as be_frozen_storage_capacity,\n", "    MAX(bo.be_frozen_onshelf_inventory) as be_frozen_onshelf_inventory,\n", "    MAX(bo.be_frozen_in_transit_quantity) as be_frozen_in_transit_quantity,\n", "    MAX(bo.be_frozen_scaled_onshelf_inventory) as be_frozen_scaled_onshelf_inventory,\n", "    MAX(bo.be_frozen_scaled_open_po_qty) as be_frozen_scaled_open_po_qty,\n", "    MAX(bo.be_frozen_onshelf_utilisation) as be_frozen_onshelf_utilisation,\n", "    MAX(bo.be_frozen_utilisation_with_open_stos) as be_frozen_utilisation_with_open_stos,\n", "    \n", "    MAX(fo.fe_regular_storage_capacity) AS fe_regular_storage_capacity,\n", "    MAX(fo.fe_regular_onshelf_inventory) AS fe_regular_onshelf_inventory,\n", "    MAX(fo.fe_regular_in_transit_quantity) AS fe_regular_in_transit_quantity,\n", "    MAX(fo.fe_regular_scaled_onshelf_inventory) AS fe_regular_scaled_onshelf_inventory,\n", "    MAX(fo.fe_regular_onshelf_utilisation) AS fe_regular_onshelf_utilisation,\n", "    MAX(fo.fe_regular_scaled_open_po_qty) AS fe_regular_scaled_open_po_qty,\n", "    MAX(fo.fe_regular_utilisation_with_open_stos) AS fe_regular_utilisation_with_open_stos,\n", "    MAX(fo.fe_cold_storage_capacity) AS fe_cold_storage_capacity,\n", "    MAX(fo.fe_cold_onshelf_inventory) AS fe_cold_onshelf_inventory,\n", "    MAX(fo.fe_cold_in_transit_quantity) AS fe_cold_in_transit_quantity,\n", "    MAX(fo.fe_cold_scaled_onshelf_inventory) AS fe_cold_scaled_onshelf_inventory,\n", "    MAX(fo.fe_cold_scaled_open_po_qty) AS fe_cold_scaled_open_po_qty,\n", "    MAX(fo.fe_cold_onshelf_utilisation) AS fe_cold_onshelf_utilisation,\n", "    MAX(fo.fe_cold_utilisation_with_open_stos) AS fe_cold_utilisation_with_open_stos,\n", "    MAX(fo.fe_heavy_storage_capacity) AS fe_heavy_storage_capacity,\n", "    MAX(fo.fe_heavy_onshelf_inventory) AS fe_heavy_onshelf_inventory,\n", "    MAX(fo.fe_heavy_in_transit_quantity) AS fe_heavy_in_transit_quantity,\n", "    MAX(fo.fe_heavy_scaled_onshelf_inventory) AS fe_heavy_scaled_onshelf_inventory,\n", "    MAX(fo.fe_heavy_scaled_open_po_qty) AS fe_heavy_scaled_open_po_qty,\n", "    MAX(fo.fe_heavy_onshelf_utilisation) AS fe_heavy_onshelf_utilisation,\n", "    MAX(fo.fe_heavy_utilisation_with_open_stos) AS fe_heavy_utilisation_with_open_stos,\n", "    MAX(fo.fe_frozen_storage_capacity) AS fe_frozen_storage_capacity,\n", "    MAX(fo.fe_frozen_onshelf_inventory) AS fe_frozen_onshelf_inventory,\n", "    MAX(fo.fe_frozen_in_transit_quantity) AS fe_frozen_in_transit_quantity,\n", "    MAX(fo.fe_frozen_scaled_onshelf_inventory) AS fe_frozen_scaled_onshelf_inventory,\n", "    MAX(fo.fe_frozen_scaled_open_po_qty) AS fe_frozen_scaled_open_po_qty,\n", "    MAX(fo.fe_frozen_onshelf_utilisation) AS fe_frozen_onshelf_utilisation,\n", "    MAX(fo.fe_frozen_utilisation_with_open_stos) AS fe_frozen_utilisation_with_open_stos,\n", "    \n", "    SUM(icm.billed_qty_fr) as billed_qty_fr,      \n", "    SUM(icm.dispatch_qty_fr) as dispatch_qty_fr,     \n", "    SUM(icm.grn_qty_fr) as grn_qty_fr,\n", "    SUM(icm.sto_raised_qty_fr) as sto_raised_qty_fr,\n", "    AVG(icm.billed_fr) as billed_fr,\n", "    AVG(icm.dispatch_fr) as dispatch_fr,\n", "    AVG(icm.grn_fr) as grn_fr,\n", "    \n", "    MAX(bo.wotif_created_qty_FE) as wotif_created_qty_FE,\n", "    MAX(bo.wotif_on_time_picked_qty_FE) as wotif_on_time_picked_qty_FE,\n", "    MAX(bo.wotif_on_time_packaged_qty_FE) as wotif_on_time_packaged_qty_FE,\n", "    MAX(bo.wotif_on_time_sorted_qty_FE) as wotif_on_time_sorted_qty_FE,\n", "    MAX(bo.wotif_on_time_dispatched_qty_FE) as wotif_on_time_dispatched_qty_FE,\n", "    MAX(bo.wotif_created_qty_BE) as wotif_created_qty_BE,\n", "    MAX(bo.wotif_on_time_picked_qty_BE) as wotif_on_time_picked_qty_BE,\n", "    MAX(bo.wotif_on_time_packaged_qty_BE) as wotif_on_time_packaged_qty_BE,\n", "    MAX(bo.wotif_on_time_sorted_qty_BE) as wotif_on_time_sorted_qty_BE,\n", "    MAX(bo.wotif_on_time_dispatched_qty_BE) as wotif_on_time_dispatched_qty_BE,\n", "    \n", "    SUM(icm.indent_raised_fotif) as indent_raised_fotif,\n", "    SUM(icm.total_trips) as total_trips,\n", "    SUM(icm.perfect_trips) as perfect_trips,\n", "    AVG(icm.on_time_vehicle_report) as on_time_vehicle_report,\n", "    AVG(icm.on_time_load_start) as on_time_load_start,\n", "    AVG(icm.on_time_dispatch) as on_time_dispatch,\n", "    AVG(icm.on_time_ds_report) as on_time_ds_report,\n", "    SUM(icm.on_time_ds_for_indent) as on_time_ds_for_indent,\n", "    SUM(icm.on_time_ds_for_indent_grn) as on_time_ds_for_indent_grn,\n", "    AVG(icm.early_ds_report) as early_ds_report,\n", "    SUM(icm.early_ds_report_for_indent) as early_ds_report_for_indent,\n", "\n", "    SUM(icm.sto_raised_qty) as sto_raised_qty,\n", "    SUM(icm.billed_qty) as billed_qty,\n", "    SUM(icm.grn_qty) as grn_qty,\n", "    AVG(icm.on_time_grn_quantity) as on_time_grn_quantity,\n", "    AVG(icm.delayed_grn_quantity) as delayed_grn_quantity,\n", "    SUM(icm.delayed_grn_qty) as delayed_grn_qty,\n", "    SUM(icm.on_time_grn_qty) as on_time_grn_qty,\n", "    SUM(icm.count_active_skus_fe) as count_active_skus_fe,\n", "    SUM(icm.count_temp_inactive_skus_fe) as count_temp_inactive_skus_fe,\n", "    MAX(bo.lines_active) as lines_active,\n", "    MAX(bo.lines_temp_inactive_skus) as lines_temp_inactive_skus,\n", "    MAX(bo.count_active_skus_be) as count_active_skus_be,\n", "    MAX(bo.count_inactive_skus_be) as count_inactive_skus_be,\n", "    \n", "    MAX(fo.avg_out_of_stock_duration) as avg_out_of_stock_duration,\n", "    MAX(fo.avg_out_of_stock_duration_weighted) as avg_out_of_stock_duration_weighted,\n", "    \n", "    MAX(spi.count_active_skus_pan) as count_active_skus_pan,\n", "    MAX(spi.count_inactive_skus_pan) as count_inactive_skus_pan,\n", "    \n", "    MAX(dpi.count_hybrid_stores) as count_hybrid_stores,\n", "    MAX(dpi.count_express_only) as count_express_only,\n", "    MAX(dpi.count_longtail_only) as count_longtail_only,\n", "    \n", "    max(bo.net_inv_present_be) as net_inv_present_be, \n", "    max(bo.net_inv_exposure_be) as net_inv_exposure_be,\n", "    max(bo.net_inv_volume_be) as net_inv_volume_be,\n", "    max(bo.doi_sto_cpd_basis_be) as doi_sto_cpd_basis_be,\n", "    max(bo.doi_po_cpd_basis_be) as doi_po_cpd_basis_be,\n", "    max(bo.sto_cpd_be) as sto_cpd_be,\n", "    max(bo.po_cpd_be) as po_cpd_be,\n", "    max(bo.excess_inv_be) as excess_inv_be,\n", "    max(bo.excess_inv_exposure_be) as excess_inv_exposure_be,\n", "    max(bo.excess_inv_volume_be) as excess_inv_volume_be,\n", "    max(bo.no_sale_inv_be) as no_sale_inv_be,\n", "    max(bo.no_sale_inv_exposure_be) as no_sale_inv_exposure_be,\n", "    max(bo.no_sale_inv_volume_be) as no_sale_inv_volume_be,\n", "    max(bo.cpd_depleted_inv_be) as cpd_depleted_inv_be,\n", "    max(bo.cpd_depleted_inv_exposure_be) as cpd_depleted_inv_exposure_be,\n", "    max(bo.cpd_depleted_inv_volume_be) as cpd_depleted_inv_volume_be,\n", "    max(bo.expiring_inv_be) as expiring_inv_be,\n", "    max(bo.expiring_inv_exposure_be) as expiring_inv_exposure_be,\n", "    max(bo.expiring_inv_volume_be) as expiring_inv_volume_be,\n", "    max(bo.overall_dead_be) as overall_dead_be,\n", "    max(bo.overall_dead_exposure_be) as overall_dead_exposure_be,\n", "    max(bo.overall_dead_volume_be) as overall_dead_volume_be,\n", "    \n", "    max(fo.net_inv_present_fe) as net_inv_present_fe, \n", "    max(fo.net_inv_exposure_fe) as net_inv_exposure_fe,\n", "    max(fo.net_inv_volume_fe) as net_inv_volume_fe,\n", "    max(fo.doi_sto_cpd_basis_fe) as doi_sto_cpd_basis_fe,\n", "    max(fo.doi_po_cpd_basis_fe) as doi_po_cpd_basis_fe,\n", "    max(fo.sto_cpd_fe) as sto_cpd_fe,\n", "    max(fo.po_cpd_fe) as po_cpd_fe,\n", "    max(fo.excess_inv_fe) as excess_inv_fe,\n", "    max(fo.excess_inv_exposure_fe) as excess_inv_exposure_fe,\n", "    max(fo.excess_inv_volume_fe) as excess_inv_volume_fe,\n", "    max(fo.no_sale_inv_fe) as no_sale_inv_fe,\n", "    max(fo.no_sale_inv_exposure_fe) as no_sale_inv_exposure_fe,\n", "    max(fo.no_sale_inv_volume_fe) as no_sale_inv_volume_fe,\n", "    max(fo.cpd_depleted_inv_fe) as cpd_depleted_inv_fe,\n", "    max(fo.cpd_depleted_inv_exposure_fe) as cpd_depleted_inv_exposure_fe,\n", "    max(fo.cpd_depleted_inv_volume_fe) as cpd_depleted_inv_volume_fe,\n", "    max(fo.expiring_inv_fe) as expiring_inv_fe,\n", "    max(fo.expiring_inv_exposure_fe) as expiring_inv_exposure_fe,\n", "    max(fo.expiring_inv_volume_fe) as expiring_inv_volume_fe,\n", "    max(fo.overall_dead_fe) as overall_dead_fe,\n", "    max(fo.overall_dead_exposure_fe) as overall_dead_exposure_fe,\n", "    max(fo.overall_dead_volume_fe) as overall_dead_volume_fe,\n", "    \n", "    MAX(db.tea_coverage) as tea_coverage,\n", "    MAX(db.inv_coverage) as inv_coverage,\n", "    MAX(db.tea_coverage_be_avail) as tea_coverage_be_avail,\n", "    MAX(db.inv_coverage_be_avail) as inv_coverage_be_avail,\n", "    MAX(db.tea_coverage_exp) as tea_coverage_exp,\n", "    MAX(db.inv_coverage_exp) as inv_coverage_exp,\n", "    MAX(db.tea_coverage_be_avail_exp) as tea_coverage_be_avail_exp,\n", "    MAX(db.inv_coverage_be_avail_exp) as inv_coverage_be_avail_exp,\n", "    MAX(db.tea_coverage_lt) as tea_coverage_lt,\n", "    MAX(db.inv_coverage_lt) as inv_coverage_lt,\n", "    MAX(db.tea_coverage_be_avail_lt) as tea_coverage_be_avail_lt,\n", "    MAX(db.inv_coverage_be_avail_lt) as inv_coverage_be_avail_lt,\n", "    \n", "    MAX(bo.bexfe_be_availability_ptype) as bexfe_be_availability_ptype,\n", "    MAX(bo.bexfe_fe_availability_ptype) as bexfe_fe_availability_ptype,\n", "    \n", "    MAX(fo.fe_availability_ptype) as fe_availability_ptype\n", "    \n", "            from ds_pan_india dpa\n", "            left join supply_etls.inventory_core_metrics_v6 icm on dpa.date_=icm.date_\n", "            left join fe_overall_final fo on fo.date_=icm.date_\n", "            left join be_overall_final bo on fo.date_=icm.date_\n", "            left join city_overall_final co on co.date_=icm.date_\n", "            left join sku_complexity_pan_india spi on spi.date_=icm.date_\n", "            left join ds_pan_india_detailed dpi on dpi.date_=icm.date_\n", "            left join dau_breakup db on db.be_facility_name=dpa.facility_name\n", ")\n", "\n", "select * from pan_india\n", "\n", "\n", "\n", "        \"\"\"\n", "\n", "        to_trino(\n", "            inventory_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs\n", "        )\n", "\n", "        print(f\"Backfilled data for: {backfill_date.strftime('%Y-%m-%d')}\")"]}, {"cell_type": "code", "execution_count": null, "id": "13b56c62-dea1-47b1-89ca-309ad1bea073", "metadata": {}, "outputs": [], "source": ["if backfill_flag == 0:\n", "    normal()\n", "else:\n", "    backfill()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}