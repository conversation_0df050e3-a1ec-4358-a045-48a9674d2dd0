{"cells": [{"cell_type": "code", "execution_count": null, "id": "8aa9cd18-226d-432f-aab4-1788697d9a87", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "83931079-17b8-4ce7-94ff-c5013cdaf0c3", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "12f09977-852d-4962-9e16-************", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "fed53ce5-5be7-412e-882d-703eef06c28f", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "8908e4bc-de0d-4c99-8fc5-333c75425ee8", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\"name\": \"be_facility_id\", \"type\": \"varchar\", \"description\": \"Backend Facility ID\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"Backend Name\"},\n", "    {\"name\": \"tea_coverage\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "0142cdce-691d-45c5-b7cc-4cc9d9e90a21", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"dau_coverage\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"insert_ds_ist\",\n", "        \"be_facility_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"insert_ds_ist\",\n", "    ],\n", "    # \"incremental_key\": \"dt_hour\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains DAU Coverage\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "33d080de-f186-4a5e-b9d7-36a7680c2daf", "metadata": {}, "outputs": [], "source": ["backfill_flag = 0\n", "\n", "\n", "def normal():\n", "    dau_coverage_query = f\"\"\"\n", "with outlet_details as (\n", "        select\n", "            city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "            inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "        from supply_etls.outlet_details\n", "        where ars_check=1\n", "        and grocery_active_count >= 1\n", "        and store_type in ('Dark Store')\n", "    ),\n", "    \n", "    item_product_mapping as (\n", "        select item_id, product_id, multiplier\n", "        from dwh.dim_item_product_offer_mapping\n", "        where is_current\n", "    ),\n", "    \n", "    outlet_breakup as (\n", "    select DISTINCT tea.fe_facility_id, tea.fe_outlet_id, tea.store_assortment_type\n", "                            from supply_etls.outlet_details od\n", "                            left join supply_etls.inventory_metrics_tea_tagging tea on tea.be_facility_id = od.facility_id\n", "                            where od.ars_check=1 \n", "                                and od.taggings = 'be' \n", "                                and (od.grocery_active_count >= 1 or od.facility_id = 2576) \n", "                                and od.store_type = 'Packaged Goods'\n", "                                and tea.flag = 'correct_tea' and tea.be_facility_id <> tea.fe_facility_id\n", "                                and tea.assortment_status_id in (1,3)\n", "    ),\n", "    \n", "    item_details as (\n", "            select item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type = 'Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "            and l0_category <> 'Specials'\n", "    ),\n", "    \n", "    outlet_details_be as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        --and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Packaged Goods')))\n", "        or facility_id in (2960,3127,3213)\n", "    ),\n", "    \n", "    assortment_status as (\n", "        select item_id, be_facility_id, fe_facility_id,\n", "            case when sum(case when assortment_type = 'EXPRESS' then 1 else 0 end) >= 1 then 'EXPRESS'\n", "                when sum(case when assortment_type = 'LONGTAIL' then 1 else 0 end) >= 1 then 'LONGTAIL'\n", "                 end as assortment_type\n", "        from(\n", "            select tea.item_id, tea.be_facility_id, tea.fe_facility_id, ma.assortment_type\n", "            from supply_etls.inventory_metrics_tea_tagging tea\n", "            join rpc.product_facility_master_assortment ma on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "        )    \n", "        group by 1,2,3\n", "    ),\n", "    \n", "    tea_tagging_item as (\n", "        select fe_outlet_id, fe_facility_id, item_id, item_name, assortment_type, store_assortment_type,\n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id,be_facility_name,fe_city_id,fe_city_name, assortment_status_id as item_substate\n", "        from supply_etls.inventory_metrics_tea_tagging\n", "        where flag = 'correct_tea' and be_facility_id <> fe_facility_id and assortment_status_id in (1,3)\n", "    ),\n", "    \n", "    sales AS (\n", "    select \n", "        order_create_dt_ist,\n", "        fs.outlet_id, \n", "        ip.item_id, \n", "        ass.assortment_type,\n", "        sum(procured_quantity * multiplier) as qty_sold\n", "    from dwh.fact_sales_order_item_details fs\n", "    join item_product_mapping ip on ip.product_id = fs.product_id\n", "    join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "    join item_details id on ip.item_id=id.item_id\n", "    left join assortment_status ass on ass.fe_facility_id = od.facility_id \n", "                                       and ass.item_id = ip.item_id\n", "    where order_create_dt_ist BETWEEN (current_date - interval '16' day) AND (current_date - interval '1' day)\n", "      AND is_internal_order = false \n", "      AND order_current_status = 'DELIVERED'\n", "    group by 1, 2, 3, 4\n", "),\n", "\n", "    hourly_inv_snapshot as(\n", "    select i.item_id,\n", "            outlet_id,\n", "            sum(current_inventory) as current_inventory\n", "            from supply_etls.hourly_inventory_snapshots i\n", "            join item_details id on id.item_id=i.item_id\n", "            where date_ist = CURRENT_DATE - INTERVAL '1' DAY\n", "            and hour(updated_at_ist)= (select max(hour(updated_at_ist)) from supply_etls.hourly_inventory_snapshots where date_ist = CURRENT_DATE - INTERVAL '1' DAY)\n", "             and substate_id IN (1,3)\n", "            group by 1,2\n", "    ),\n", "    \n", "    avail_flag_be as (\n", "        select outlet_id,item_id,\n", "                CASE WHEN current_inventory>0 THEN 1 ELSE 0 END as be_avail_flag\n", "        from hourly_inv_snapshot\n", "    ),\n", "    \n", "    avail_flag_fe as (\n", "        select outlet_id,item_id,\n", "                CASE WHEN current_inventory>0 THEN 1 ELSE 0 END as fe_avail_flag\n", "        from hourly_inv_snapshot\n", "    ),\n", "    \n", "    tea_tagging_be_fe as (\n", "        select distinct \n", "            fe_outlet_id, fe_facility_id,\n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id\n", "        from supply_etls.inventory_metrics_tea_tagging\n", "        where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "        and assortment_status_id in (1,3)\n", "    ),\n", "    \n", "    sales_item AS (               \n", "    select \n", "        item_id,\n", "        sum(qty_sold) as item_total_sales,\n", "        SUM(CASE WHEN assortment_type='EXPRESS' THEN qty_sold END) AS item_total_sales_exp,\n", "        SUM(CASE WHEN assortment_type='LONGTAIL' THEN qty_sold END) AS item_total_sales_lt\n", "    from sales\n", "    group by 1\n", "    ),\n", "\n", "    sales_pan_india as (           \n", "        select \n", "            sum(qty_sold) as pan_india_total_sales, \n", "            SUM(CASE WHEN assortment_type='EXPRESS' THEN qty_sold END) AS pan_india_total_sales_exp,\n", "            SUM(CASE WHEN assortment_type='LONGTAIL' THEN qty_sold END) AS pan_india_total_sales_lt\n", "        from sales s\n", "    ),\n", "    \n", "    item_weight as (\n", "        select\n", "            item_id,\n", "            1.000000*item_total_sales/nullif(pan_india_total_sales,0) as item_weight,\n", "            1.000000*item_total_sales_exp/nullif(pan_india_total_sales_exp,0) as item_weight_exp,\n", "            1.000000*item_total_sales_lt/nullif(pan_india_total_sales_lt,0) as item_weight_lt\n", "        from sales_item cross join sales_pan_india\n", "    ),\n", "    \n", "    sales_be_item as (   \n", "        select \n", "            be_facility_id, s.item_id, sum(qty_sold) as item_total_sales,\n", "            SUM(CASE WHEN s.assortment_type='EXPRESS' THEN qty_sold END) AS item_total_sales_exp,\n", "            SUM(CASE WHEN s.assortment_type='LONGTAIL' THEN qty_sold END) AS item_total_sales_lt\n", "        from sales s\n", "        join tea_tagging_item tea \n", "            on s.outlet_id=tea.fe_outlet_id \n", "            and s.item_id=tea.item_id\n", "            and tea.item_substate in (1,3)\n", "        group by 1,2\n", "    ),\n", "\n", "    sales_be as (           \n", "        select \n", "            be_facility_id, sum(qty_sold) as be_total_sales,\n", "            SUM(CASE WHEN s.assortment_type='EXPRESS' THEN qty_sold END) AS be_total_sales_exp,\n", "            SUM(CASE WHEN s.assortment_type='LONGTAIL' THEN qty_sold END) AS be_total_sales_lt\n", "        from sales s\n", "        join tea_tagging_item tea \n", "            on s.outlet_id=tea.fe_outlet_id \n", "            and s.item_id=tea.item_id\n", "            and tea.item_substate in (1,3)\n", "        group by 1\n", "    ),\n", "    \n", "    be_item_weight as (\n", "        select\n", "            sbi.be_facility_id,\n", "            item_id,\n", "            1.000000*item_total_sales/nullif(be_total_sales,0) as item_weight,\n", "            1.000000*item_total_sales_exp/nullif(be_total_sales_exp,0) as item_weight_exp,\n", "            1.000000*item_total_sales_lt/nullif(be_total_sales_lt,0) as item_weight_lt\n", "        from sales_be_item  sbi\n", "        join sales_be sb\n", "            on sbi.be_facility_id=sb.be_facility_id\n", "    ),\n", "    \n", "    be_fe_dau_weight as (\n", "        with outlet_dau as (\n", "            with dau as (\n", "                select \n", "                    * \n", "                from dwh.agg_daily_consumer_conversion_details dau \n", "                where snapshot_date_ist = current_date - interval '1' day\n", "                    and snapshot_hour_ist=24 and city<>'Overall' and dau.merchant_id<>'Overall'\n", "            )\n", "            select\n", "                od.hot_outlet_id,\n", "                sum(daily_active_users) as daily_active_users\n", "            from dau\n", "            join dwh.dim_merchant_outlet_facility_mapping od_merch on dau.merchant_id=cast(od_merch.frontend_merchant_id as varchar)  \n", "                and od_merch.is_current and od_merch.is_mapping_enabled\n", "            join outlet_details od on od.hot_outlet_id=od_merch.pos_outlet_id\n", "            group by 1\n", "        )\n", "        select \n", "        hot_outlet_id,\n", "        be_facility_id,\n", "        store_assortment_type,\n", "        1.000000 * daily_active_users / sum(daily_active_users) over (partition by be_facility_id) as dau_weight,\n", "        1.000000 * daily_active_users / sum(daily_active_users) over (partition by be_facility_id, store_assortment_type) as dau_weight_breakup\n", "    from outlet_dau od\n", "    join tea_tagging_be_fe tea on od.hot_outlet_id = tea.fe_outlet_id\n", "    join outlet_breakup ob\n", "        on tea.fe_facility_id = ob.fe_facility_id\n", "    ),\n", "\n", "    dau_weight as (\n", "        with outlet_dau as (\n", "            with dau as (\n", "                select \n", "                    * \n", "                from dwh.agg_daily_consumer_conversion_details dau \n", "                where snapshot_date_ist = current_date - interval '1' day\n", "                    and snapshot_hour_ist=24 and city<>'Overall' and dau.merchant_id<>'Overall'\n", "            )\n", "            select\n", "                od.hot_outlet_id,\n", "                sum(daily_active_users) as daily_active_users\n", "            from dau\n", "            join dwh.dim_merchant_outlet_facility_mapping od_merch on dau.merchant_id=cast(od_merch.frontend_merchant_id as varchar)  \n", "                and od_merch.is_current and od_merch.is_mapping_enabled\n", "            join outlet_details od on od.hot_outlet_id=od_merch.pos_outlet_id\n", "            group by 1\n", "        )\n", "        select \n", "            hot_outlet_id,\n", "            store_assortment_type,\n", "            1.000000*daily_active_users/sum(daily_active_users) over () as dau_weight,\n", "            1.000000 * daily_active_users / sum(daily_active_users) over (partition by store_assortment_type) as dau_weight_breakup\n", "        from outlet_dau od\n", "        left join outlet_breakup ob on ob.fe_outlet_id=od.hot_outlet_id\n", "    ),\n", "\n", "    item_coverage_be_overall as (\n", "        SELECT \n", "            CURRENT_DATE - INTERVAL '1' DAY as insert_ds_ist, \n", "            a.be_facility_id,\n", "            a.be_facility_name,\n", "            a.item_id,\n", "            a.item_name,\n", "            sum(case when item_substate in (1,3) then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage,\n", "            sum(case when item_substate in (1,3) and be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage_be_avail,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_exp,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONGTAIL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_lt\n", "            \n", "        FROM tea_tagging_item a\n", "        join item_details id on id.item_id=a.item_id\n", "        join outlet_details od on od.facility_id=a.fe_facility_id\n", "        join outlet_details_be odbe on odbe.facility_id=a.be_facility_id\n", "        left join be_fe_dau_weight b on\n", "             a.fe_outlet_id=b.hot_outlet_id\n", "             and a.be_facility_id=b.be_facility_id\n", "        left join assortment_status ass on ass.be_facility_id=a.be_facility_id and ass.fe_facility_id=a.fe_facility_id and ass.item_id=a.item_id\n", "        left join avail_flag_be afb on afb.outlet_id=a.be_inv_outlet_id and afb.item_id=a.item_id\n", "        left join avail_flag_fe aff on aff.outlet_id=a.fe_outlet_id and aff.item_id=a.item_id\n", "        GROUP BY 1, 2, 3, 4, 5\n", "    ),\n", "    \n", "    item_coverage_overall as (\n", "        SELECT \n", "            CURRENT_DATE - INTERVAL '1' DAY as insert_ds_ist, \n", "            a.item_id,\n", "            a.item_name,\n", "            \n", "            sum(case when item_substate in (1,3) then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage,\n", "            sum(case when item_substate in (1,3) and be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage_be_avail,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 and afb.be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 and afb.be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_exp,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONGTAIL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_lt\n", "        FROM tea_tagging_item a\n", "        join item_details id on id.item_id=a.item_id\n", "        join outlet_details od on od.facility_id=a.fe_facility_id\n", "        join outlet_details_be odbe on odbe.facility_id=a.be_facility_id\n", "        left join dau_weight b on\n", "             a.fe_outlet_id=b.hot_outlet_id\n", "        left join assortment_status ass on ass.be_facility_id=a.be_facility_id and ass.fe_facility_id=a.fe_facility_id and ass.item_id=a.item_id\n", "        left join avail_flag_be afb on afb.outlet_id=a.be_inv_outlet_id and afb.item_id=a.item_id\n", "        left join avail_flag_fe aff on aff.outlet_id=a.fe_outlet_id and aff.item_id=a.item_id\n", "        GROUP BY 1, 2, 3\n", "    )\n", "\n", "    select\n", "        insert_ds_ist,\n", "        cast(0 as varchar) as be_facility_id,\n", "        '.PAN India' as be_facility_name,\n", "        sum(item_coverage * item_weight)/nullif(sum(item_weight),0) as tea_coverage,\n", "        sum(item_inv_coverage * item_weight)/nullif(sum(item_weight),0) as inv_coverage,\n", "        sum(item_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as tea_coverage_be_avail,\n", "        sum(item_inv_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as inv_coverage_be_avail,\n", "        \n", "        sum(item_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_exp,\n", "        sum(item_inv_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_exp,\n", "        sum(item_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_be_avail_exp,\n", "        sum(item_inv_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_be_avail_exp,\n", "        \n", "        sum(item_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_lt,\n", "        sum(item_inv_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_lt,\n", "        sum(item_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_be_avail_lt,\n", "        sum(item_inv_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_be_avail_lt\n", "        \n", "    from item_coverage_overall ic\n", "    join item_weight iw on ic.item_id=iw.item_id\n", "    group by 1,2,3\n", "    UNION\n", "    select\n", "        insert_ds_ist,\n", "        CAST(ic.be_facility_id AS varchar)  as be_facility_id,\n", "        be_facility_name,\n", "        sum(item_coverage * item_weight)/nullif(sum(item_weight),0) as tea_coverage,\n", "        sum(item_inv_coverage * item_weight)/nullif(sum(item_weight),0) as inv_coverage,\n", "        sum(item_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as tea_coverage_be_avail,\n", "        sum(item_inv_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as inv_coverage_be_avail,\n", "        \n", "        sum(item_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_exp,\n", "        sum(item_inv_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_exp,\n", "        sum(item_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_be_avail_exp,\n", "        sum(item_inv_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_be_avail_exp,\n", "        \n", "        sum(item_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_lt,\n", "        sum(item_inv_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_lt,\n", "        sum(item_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_be_avail_lt,\n", "        sum(item_inv_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_be_avail_lt\n", "\n", "    from item_coverage_be_overall ic\n", "    join be_item_weight iw \n", "        on ic.item_id=iw.item_id \n", "        and ic.be_facility_id=iw.be_facility_id\n", "    group by 1,2,3\n", "    \n", "    \n", "\n", "\n", "\n", "        \"\"\"\n", "    to_trino(dau_coverage_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "33566bce-1360-4226-b015-2d9274866eb0", "metadata": {}, "outputs": [], "source": ["def backfill():\n", "\n", "    today = datetime.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=53)\n", "\n", "    for i in range(4):\n", "        backfill_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        print(f\"Backfilling data for: {backfill_date.strftime('%Y-%m-%d')}\")\n", "        # Convert backfill_date into the format 'YYYY-MM-DD'\n", "        formatted_date = backfill_date.strftime(\"%Y-%m-%d\")\n", "\n", "        dau_coverage_query = f\"\"\"\n", "with outlet_details as (\n", "        select\n", "            city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "            inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings \n", "        from supply_etls.outlet_details\n", "        where ars_check=1\n", "        and grocery_active_count >= 1\n", "        and store_type in ('Dark Store')\n", "    ),\n", "    \n", "    item_product_mapping as (\n", "        select item_id, product_id, multiplier\n", "        from dwh.dim_item_product_offer_mapping\n", "        where is_current\n", "    ),\n", "    \n", "    outlet_breakup as (\n", "    select DISTINCT tea.fe_facility_id, tea.fe_outlet_id, tea.store_assortment_type\n", "                            from supply_etls.outlet_details od\n", "                            left join supply_etls.inventory_metrics_tea_tagging tea on tea.be_facility_id = od.facility_id\n", "                            where od.ars_check=1 \n", "                                and od.taggings = 'be' \n", "                                and (od.grocery_active_count >= 1 or od.facility_id = 2576) \n", "                                and od.store_type = 'Packaged Goods'\n", "                                and tea.flag = 'correct_tea' and tea.be_facility_id <> tea.fe_facility_id\n", "                                and tea.assortment_status_id in (1,3)\n", "    ),\n", "    \n", "    item_details as (\n", "            select item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type = 'Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "            and l0_category <> 'Specials'\n", "    ),\n", "    \n", "    outlet_details_be as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        --and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Packaged Goods')))\n", "        or facility_id in (2960,3127,3213)\n", "    ),\n", "    \n", "    assortment_status as (\n", "        select item_id, be_facility_id, fe_facility_id,\n", "            case when sum(case when assortment_type = 'EXPRESS' then 1 else 0 end) >= 1 then 'EXPRESS'\n", "                when sum(case when assortment_type = 'LONGTAIL' then 1 else 0 end) >= 1 then 'LONGTAIL'\n", "                 end as assortment_type\n", "        from(\n", "            select tea.item_id, tea.be_facility_id, tea.fe_facility_id, ma.assortment_type\n", "            from supply_etls.inventory_metrics_tea_tagging tea\n", "            join rpc.product_facility_master_assortment ma on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "        )    \n", "        group by 1,2,3\n", "    ),\n", "    \n", "    tea_tagging_item as (\n", "        select fe_outlet_id, fe_facility_id, item_id, item_name, assortment_type, store_assortment_type,\n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id,be_facility_name,fe_city_id,fe_city_name, assortment_status_id as item_substate\n", "        from supply_etls.inventory_metrics_tea_tagging\n", "        where flag = 'correct_tea' and be_facility_id <> fe_facility_id and assortment_status_id in (1,3)\n", "    ),\n", "    \n", "    sales AS (\n", "    select \n", "        order_create_dt_ist,\n", "        fs.outlet_id, \n", "        ip.item_id, \n", "        ass.assortment_type,\n", "        sum(procured_quantity * multiplier) as qty_sold\n", "    from dwh.fact_sales_order_item_details fs\n", "    join item_product_mapping ip on ip.product_id = fs.product_id\n", "    join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "    join item_details id on ip.item_id=id.item_id\n", "    left join assortment_status ass on ass.fe_facility_id = od.facility_id \n", "                                       and ass.item_id = ip.item_id\n", "    where order_create_dt_ist BETWEEN (date('{formatted_date}') - interval '16' day) AND (date('{formatted_date}') - interval '1' day)\n", "      AND is_internal_order = false \n", "      AND order_current_status = 'DELIVERED'\n", "    group by 1, 2, 3, 4\n", "),\n", "\n", "    hourly_inv_snapshot as(\n", "    select i.item_id,\n", "            outlet_id,\n", "            sum(current_inventory) as current_inventory\n", "            from supply_etls.hourly_inventory_snapshots i\n", "            join item_details id on id.item_id=i.item_id\n", "            where date_ist = date('{formatted_date}') - INTERVAL '1' DAY\n", "            and hour(updated_at_ist)=(select max(hour(updated_at_ist)) from supply_etls.hourly_inventory_snapshots where date_ist = date('{formatted_date}') - INTERVAL '1' DAY)\n", "             and substate_id IN (1,3)\n", "            group by 1,2\n", "    ),\n", "    \n", "    avail_flag_be as (\n", "        select outlet_id,item_id,\n", "                CASE WHEN current_inventory>0 THEN 1 ELSE 0 END as be_avail_flag\n", "        from hourly_inv_snapshot\n", "    ),\n", "    \n", "    avail_flag_fe as (\n", "        select outlet_id,item_id,\n", "                CASE WHEN current_inventory>0 THEN 1 ELSE 0 END as fe_avail_flag\n", "        from hourly_inv_snapshot\n", "    ),\n", "    \n", "    tea_tagging_be_fe as (\n", "        select distinct \n", "            fe_outlet_id, fe_facility_id,\n", "            be_hot_outlet_id, be_inv_outlet_id, be_facility_id\n", "        from supply_etls.inventory_metrics_tea_tagging\n", "        where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "        and assortment_status_id in (1,3)\n", "    ),\n", "    \n", "    sales_item AS (               \n", "    select \n", "        item_id,\n", "        sum(qty_sold) as item_total_sales,\n", "        SUM(CASE WHEN assortment_type='EXPRESS' THEN qty_sold END) AS item_total_sales_exp,\n", "        SUM(CASE WHEN assortment_type='LONGTAIL' THEN qty_sold END) AS item_total_sales_lt\n", "    from sales\n", "    group by 1\n", "    ),\n", "\n", "    sales_pan_india as (           \n", "        select \n", "            sum(qty_sold) as pan_india_total_sales, \n", "            SUM(CASE WHEN assortment_type='EXPRESS' THEN qty_sold END) AS pan_india_total_sales_exp,\n", "            SUM(CASE WHEN assortment_type='LONGTAIL' THEN qty_sold END) AS pan_india_total_sales_lt\n", "        from sales s\n", "    ),\n", "    \n", "    item_weight as (\n", "        select\n", "            item_id,\n", "            1.000000*item_total_sales/nullif(pan_india_total_sales,0) as item_weight,\n", "            1.000000*item_total_sales_exp/nullif(pan_india_total_sales_exp,0) as item_weight_exp,\n", "            1.000000*item_total_sales_lt/nullif(pan_india_total_sales_lt,0) as item_weight_lt\n", "        from sales_item cross join sales_pan_india\n", "    ),\n", "    \n", "    sales_be_item as (   \n", "        select \n", "            be_facility_id, s.item_id, sum(qty_sold) as item_total_sales,\n", "            SUM(CASE WHEN s.assortment_type='EXPRESS' THEN qty_sold END) AS item_total_sales_exp,\n", "            SUM(CASE WHEN s.assortment_type='LONGTAIL' THEN qty_sold END) AS item_total_sales_lt\n", "        from sales s\n", "        join tea_tagging_item tea \n", "            on s.outlet_id=tea.fe_outlet_id \n", "            and s.item_id=tea.item_id\n", "            and tea.item_substate in (1,3)\n", "        group by 1,2\n", "    ),\n", "\n", "    sales_be as (           \n", "        select \n", "            be_facility_id, sum(qty_sold) as be_total_sales,\n", "            SUM(CASE WHEN s.assortment_type='EXPRESS' THEN qty_sold END) AS be_total_sales_exp,\n", "            SUM(CASE WHEN s.assortment_type='LONGTAIL' THEN qty_sold END) AS be_total_sales_lt\n", "        from sales s\n", "        join tea_tagging_item tea \n", "            on s.outlet_id=tea.fe_outlet_id \n", "            and s.item_id=tea.item_id\n", "            and tea.item_substate in (1,3)\n", "        group by 1\n", "    ),\n", "    \n", "    be_item_weight as (\n", "        select\n", "            sbi.be_facility_id,\n", "            item_id,\n", "            1.000000*item_total_sales/nullif(be_total_sales,0) as item_weight,\n", "            1.000000*item_total_sales_exp/nullif(be_total_sales_exp,0) as item_weight_exp,\n", "            1.000000*item_total_sales_lt/nullif(be_total_sales_lt,0) as item_weight_lt\n", "        from sales_be_item  sbi\n", "        join sales_be sb\n", "            on sbi.be_facility_id=sb.be_facility_id\n", "    ),\n", "    \n", "    be_fe_dau_weight as (\n", "        with outlet_dau as (\n", "            with dau as (\n", "                select \n", "                    * \n", "                from dwh.agg_daily_consumer_conversion_details dau \n", "                where snapshot_date_ist = date('{formatted_date}') - interval '1' day\n", "                    and snapshot_hour_ist=24 and city<>'Overall' and dau.merchant_id<>'Overall'\n", "            )\n", "            select\n", "                od.hot_outlet_id,\n", "                sum(daily_active_users) as daily_active_users\n", "            from dau\n", "            join dwh.dim_merchant_outlet_facility_mapping od_merch on dau.merchant_id=cast(od_merch.frontend_merchant_id as varchar)  \n", "                and od_merch.is_current and od_merch.is_mapping_enabled\n", "            join outlet_details od on od.hot_outlet_id=od_merch.pos_outlet_id\n", "            group by 1\n", "        )\n", "        select \n", "        hot_outlet_id,\n", "        be_facility_id,\n", "        store_assortment_type,\n", "        1.000000 * daily_active_users / sum(daily_active_users) over (partition by be_facility_id) as dau_weight,\n", "        1.000000 * daily_active_users / sum(daily_active_users) over (partition by be_facility_id, store_assortment_type) as dau_weight_breakup\n", "    from outlet_dau od\n", "    join tea_tagging_be_fe tea on od.hot_outlet_id = tea.fe_outlet_id\n", "    join outlet_breakup ob\n", "        on tea.fe_facility_id = ob.fe_facility_id\n", "    ),\n", "\n", "    dau_weight as (\n", "        with outlet_dau as (\n", "            with dau as (\n", "                select \n", "                    * \n", "                from dwh.agg_daily_consumer_conversion_details dau \n", "                where snapshot_date_ist = date('{formatted_date}') - interval '1' day\n", "                    and snapshot_hour_ist=24 and city<>'Overall' and dau.merchant_id<>'Overall'\n", "            )\n", "            select\n", "                od.hot_outlet_id,\n", "                sum(daily_active_users) as daily_active_users\n", "            from dau\n", "            join dwh.dim_merchant_outlet_facility_mapping od_merch on dau.merchant_id=cast(od_merch.frontend_merchant_id as varchar)  \n", "                and od_merch.is_current and od_merch.is_mapping_enabled\n", "            join outlet_details od on od.hot_outlet_id=od_merch.pos_outlet_id\n", "            group by 1\n", "        )\n", "        select \n", "            hot_outlet_id,\n", "            store_assortment_type,\n", "            1.000000*daily_active_users/sum(daily_active_users) over () as dau_weight,\n", "            1.000000 * daily_active_users / sum(daily_active_users) over (partition by store_assortment_type) as dau_weight_breakup\n", "        from outlet_dau od\n", "        left join outlet_breakup ob on ob.fe_outlet_id=od.hot_outlet_id\n", "    ),\n", "    \n", "    item_coverage_be_overall as (\n", "        SELECT \n", "            date('{formatted_date}') - INTERVAL '1' DAY as insert_ds_ist, \n", "            a.be_facility_id,\n", "            a.be_facility_name,\n", "            a.item_id,\n", "            a.item_name,\n", "            sum(case when item_substate in (1,3) then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage,\n", "            sum(case when item_substate in (1,3) and be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage_be_avail,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_exp,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONGTAIL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_lt\n", "            \n", "        FROM tea_tagging_item a\n", "        join item_details id on id.item_id=a.item_id\n", "        join outlet_details od on od.facility_id=a.fe_facility_id\n", "        join outlet_details_be odbe on odbe.facility_id=a.be_facility_id\n", "        left join be_fe_dau_weight b on\n", "             a.fe_outlet_id=b.hot_outlet_id\n", "             and a.be_facility_id=b.be_facility_id\n", "        left join assortment_status ass on ass.be_facility_id=a.be_facility_id and ass.fe_facility_id=a.fe_facility_id and ass.item_id=a.item_id\n", "        left join avail_flag_be afb on afb.outlet_id=a.be_inv_outlet_id and afb.item_id=a.item_id\n", "        left join avail_flag_fe aff on aff.outlet_id=a.fe_outlet_id and aff.item_id=a.item_id\n", "        GROUP BY 1, 2, 3, 4, 5\n", "    ),\n", "    \n", "    item_coverage_overall as (\n", "        SELECT \n", "            date('{formatted_date}') - INTERVAL '1' DAY as insert_ds_ist, \n", "            a.item_id,\n", "            a.item_name,\n", "            \n", "            sum(case when item_substate in (1,3) then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage,\n", "            sum(case when item_substate in (1,3) and be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_coverage_be_avail,\n", "            sum(case when item_substate in (1,3) and fe_avail_flag=1 and afb.be_avail_flag=1 then b.dau_weight* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_exp,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'EXPRESS' and b.store_assortment_type = 'EXPRESS' and fe_avail_flag=1 and afb.be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_exp,\n", "            \n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONG<PERSON>IL' and b.store_assortment_type = 'LONGTAIL' and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_coverage_be_avail_lt,\n", "            sum(case when item_substate in (1,3) and ass.assortment_type = 'LONGTAIL' and b.store_assortment_type = 'LONGTAIL' and fe_avail_flag=1 and be_avail_flag=1 then b.dau_weight_breakup* 1.00000000000 else 0 end) AS item_inv_coverage_be_avail_lt\n", "        FROM tea_tagging_item a\n", "        join item_details id on id.item_id=a.item_id\n", "        join outlet_details od on od.facility_id=a.fe_facility_id\n", "        join outlet_details_be odbe on odbe.facility_id=a.be_facility_id\n", "        left join dau_weight b on\n", "             a.fe_outlet_id=b.hot_outlet_id\n", "        left join assortment_status ass on ass.be_facility_id=a.be_facility_id and ass.fe_facility_id=a.fe_facility_id and ass.item_id=a.item_id\n", "        left join avail_flag_be afb on afb.outlet_id=a.be_inv_outlet_id and afb.item_id=a.item_id\n", "        left join avail_flag_fe aff on aff.outlet_id=a.fe_outlet_id and aff.item_id=a.item_id\n", "        GROUP BY 1, 2, 3\n", "    )\n", "\n", "    select\n", "        insert_ds_ist,\n", "        cast(0 as varchar) as be_facility_id,\n", "        '.PAN India' as be_facility_name,\n", "        sum(item_coverage * item_weight)/nullif(sum(item_weight),0) as tea_coverage,\n", "        sum(item_inv_coverage * item_weight)/nullif(sum(item_weight),0) as inv_coverage,\n", "        sum(item_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as tea_coverage_be_avail,\n", "        sum(item_inv_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as inv_coverage_be_avail,\n", "        \n", "        sum(item_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_exp,\n", "        sum(item_inv_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_exp,\n", "        sum(item_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_be_avail_exp,\n", "        sum(item_inv_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_be_avail_exp,\n", "        \n", "        sum(item_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_lt,\n", "        sum(item_inv_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_lt,\n", "        sum(item_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_be_avail_lt,\n", "        sum(item_inv_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_be_avail_lt\n", "        \n", "    from item_coverage_overall ic\n", "    join item_weight iw on ic.item_id=iw.item_id\n", "    group by 1,2,3\n", "    UNION\n", "    select\n", "        insert_ds_ist,\n", "        CAST(ic.be_facility_id AS varchar)  as be_facility_id,\n", "        be_facility_name,\n", "        sum(item_coverage * item_weight)/nullif(sum(item_weight),0) as tea_coverage,\n", "        sum(item_inv_coverage * item_weight)/nullif(sum(item_weight),0) as inv_coverage,\n", "        sum(item_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as tea_coverage_be_avail,\n", "        sum(item_inv_coverage_be_avail * item_weight)/nullif(sum(item_weight),0) as inv_coverage_be_avail,\n", "        \n", "        sum(item_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_exp,\n", "        sum(item_inv_coverage_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_exp,\n", "        sum(item_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as tea_coverage_be_avail_exp,\n", "        sum(item_inv_coverage_be_avail_exp * item_weight_exp)/nullif(sum(item_weight_exp),0) as inv_coverage_be_avail_exp,\n", "        \n", "        sum(item_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_lt,\n", "        sum(item_inv_coverage_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_lt,\n", "        sum(item_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as tea_coverage_be_avail_lt,\n", "        sum(item_inv_coverage_be_avail_lt * item_weight_lt)/nullif(sum(item_weight_lt),0) as inv_coverage_be_avail_lt\n", "\n", "    from item_coverage_be_overall ic\n", "    join be_item_weight iw \n", "        on ic.item_id=iw.item_id \n", "        and ic.be_facility_id=iw.be_facility_id\n", "    group by 1,2,3\n", "        \"\"\"\n", "\n", "        to_trino(dau_coverage_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "        print(f\"Backfilled data for: {backfill_date.strftime('%Y-%m-%d')}\")"]}, {"cell_type": "code", "execution_count": null, "id": "28808aa1-d706-4ed2-a5aa-83cc4ce5a590", "metadata": {}, "outputs": [], "source": ["if backfill_flag == 0:\n", "    normal()\n", "else:\n", "    backfill()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}