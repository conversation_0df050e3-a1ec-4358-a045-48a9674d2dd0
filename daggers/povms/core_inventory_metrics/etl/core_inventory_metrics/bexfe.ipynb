{"cells": [{"cell_type": "code", "execution_count": null, "id": "2f3ac44c-1957-41ce-ae72-b91b354951d2", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "d151f361-5dca-4d19-bfe0-334d810d588e", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "c976376a-e2d9-4fbd-ab5c-72d678f6c8ac", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "ff06883f-287f-47d2-961d-b67d700a381d", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "248b0882-63d6-4b9d-be7a-c4c03866b60f", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\"name\": \"city_id\", \"type\": \"integer\", \"description\": \"City ID\"},\n", "    {\n", "        \"name\": \"city_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"City Name\",\n", "    },\n", "    {\"name\": \"be_facility_id\", \"type\": \"integer\", \"description\": \"Backend Facility ID\"},\n", "    {\"name\": \"be_inv_outlet_id\", \"type\": \"integer\", \"description\": \"Backend Inventory Outlet ID\"},\n", "    {\"name\": \"be_facility_name\", \"type\": \"varchar\", \"description\": \"Backend Name\"},\n", "    {\"name\": \"no_of_ds_live\", \"type\": \"integer\", \"description\": \"Count of Dark Stores live\"},\n", "    {\n", "        \"name\": \"no_of_ds_live_express\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of Express Dark Stores live\",\n", "    },\n", "    {\n", "        \"name\": \"no_of_ds_live_longtail\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Count of LongTail Dark Stores live\",\n", "    },\n", "    {\"name\": \"fe_facility_id\", \"type\": \"integer\", \"description\": \"Dark Store Facility ID\"},\n", "    {\n", "        \"name\": \"fe_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Dark Store Inventory Outlet ID\",\n", "    },\n", "    {\"name\": \"fe_facility_name\", \"type\": \"varchar\", \"description\": \"Dark Store Facility Name\"},\n", "    {\"name\": \"total_loss\", \"type\": \"real\", \"description\": \"GMV Loss\"},\n", "    {\"name\": \"city_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_fe_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_active_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_express_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_express_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_longtail_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_temp_inactive_longtail_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_overall_overall_weight_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_overall_overall_binary_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"current_inventory\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_indent\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_indent_wt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_total_con_cost\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fvcpu_per_unit_total_con_cost\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"esto_excess_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"esto_excess_value\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"sto_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"v1_line_items\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"v2_line_items\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"qty_sold\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"potential_order_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_qty_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_qty_cap\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_qty_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_sku_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_sku_cap\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"picking_sku_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inward_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inward_cap_in_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inward_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"truck_load_util\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"truck_load_cap_in_kg\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"truck_load_util_per\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_regular_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cold_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_heavy_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_frozen_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_storage_capacity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_in_transit_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_scaled_onshelf_inventory\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_onshelf_utilisation\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_utilisation_with_open_stos\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_regular_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cold_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_heavy_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_frozen_scaled_open_po_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_fr\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dispatch_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_fr\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_created_qty_FE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_picked_qty_FE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_packaged_qty_FE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_sorted_qty_FE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_dispatched_qty_FE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_created_qty_BE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_picked_qty_BE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_packaged_qty_BE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_sorted_qty_BE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"wotif_on_time_dispatched_qty_BE\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"indent_raised_fotif\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"total_trips\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"perfect_trips\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_vehicle_report\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_load_start\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_dispatch\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_ds_report\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_ds_for_indent\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_ds_for_indent_grn\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"early_ds_report\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"early_ds_report_for_indent\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"sto_raised_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_grn_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"delayed_grn_quantity\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_cpd\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_weight\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_cpd\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"be_weight\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_cpd\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"city_weight\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_qty_fr\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"dispatch_qty_fr\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_qty_fr\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"sto_raised_qty_fr\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_active_skus_fe\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_temp_inactive_skus_fe\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"lines_active\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"lines_temp_inactive_skus\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_active_skus_be\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_inactive_skus_be\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"planned_quantity\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"delayed_grn_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"on_time_grn_qty\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"avg_out_of_stock_duration\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"avg_out_of_stock_duration_weighted\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"planned_quantity_be_avail\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"count_orders\", \"type\": \"integer\", \"description\": \"Sample\"},\n", "    {\"name\": \"gmv\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"total_dn_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_short_qty\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_damage_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"transfer_loss_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"transfer_loss_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"total_dn_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_short_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_damage_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_excess_item_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_excess_item_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_exp_nte_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_exp_nte_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_freebie_missing_qty\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_freebie_missing_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_misc_qty\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_misc_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"dn_other_quality_issue_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_quality_issue_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_upc_not_scannable_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_upc_not_scannable_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_variant_mismatch_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_variant_mismatch_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_wrong_item_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"dn_other_wrong_item_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_positive_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_positive_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_negative_qty\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"lost_in_transit_negative_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"b2b_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"rsto_dump_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"grn_amt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"billed_amt\", \"type\": \"real\", \"description\": \"<PERSON>ple\"},\n", "    {\"name\": \"net_inv_present_fe\", \"type\": \"integer\", \"description\": \"Net Inventory\"},\n", "    {\"name\": \"net_inv_exposure_fe\", \"type\": \"integer\", \"description\": \"Inventory Value\"},\n", "    {\"name\": \"net_inv_volume_fe\", \"type\": \"integer\", \"description\": \"Inventory Volume\"},\n", "    {\"name\": \"doi_sto_cpd_basis_fe\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"doi_po_cpd_basis_fe\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"sto_cpd_fe\", \"type\": \"real\", \"description\": \"STO Consumption per day\"},\n", "    {\"name\": \"po_cpd_fe\", \"type\": \"real\", \"description\": \"PO Consumption per day\"},\n", "    {\"name\": \"excess_inv_fe\", \"type\": \"integer\", \"description\": \"Excess inventory\"},\n", "    {\n", "        \"name\": \"excess_inv_exposure_fe\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Excess inventory Exposure\",\n", "    },\n", "    {\"name\": \"excess_inv_volume_fe\", \"type\": \"integer\", \"description\": \"Excess inventory Volume\"},\n", "    {\"name\": \"no_sale_inv_fe\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_exposure_fe\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_volume_fe\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\n", "        \"name\": \"cpd_depleted_inv_fe\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_exposure_fe\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_volume_fe\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_fe\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_exposure_fe\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_volume_fe\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\"name\": \"overall_dead_fe\", \"type\": \"integer\", \"description\": \"overall dead Inventory\"},\n", "    {\"name\": \"overall_dead_exposure_fe\", \"type\": \"integer\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"overall_dead_volume_fe\", \"type\": \"integer\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"net_inv_present_be\", \"type\": \"integer\", \"description\": \"Net Inventory\"},\n", "    {\"name\": \"net_inv_exposure_be\", \"type\": \"integer\", \"description\": \"Inventory Value\"},\n", "    {\"name\": \"net_inv_volume_be\", \"type\": \"integer\", \"description\": \"Inventory Volume\"},\n", "    {\"name\": \"doi_sto_cpd_basis_be\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"doi_po_cpd_basis_be\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"sto_cpd_be\", \"type\": \"real\", \"description\": \"STO Consumption per day\"},\n", "    {\"name\": \"po_cpd_be\", \"type\": \"real\", \"description\": \"PO Consumption per day\"},\n", "    {\"name\": \"excess_inv_be\", \"type\": \"integer\", \"description\": \"Excess inventory\"},\n", "    {\n", "        \"name\": \"excess_inv_exposure_be\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Excess inventory Exposure\",\n", "    },\n", "    {\"name\": \"excess_inv_volume_be\", \"type\": \"integer\", \"description\": \"Excess inventory Volume\"},\n", "    {\"name\": \"no_sale_inv_be\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_exposure_be\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_volume_be\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\n", "        \"name\": \"cpd_depleted_inv_be\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_exposure_be\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_volume_be\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_be\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_exposure_be\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_volume_be\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\"name\": \"overall_dead_be\", \"type\": \"integer\", \"description\": \"overall dead Inventory\"},\n", "    {\"name\": \"overall_dead_exposure_be\", \"type\": \"integer\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"overall_dead_volume_be\", \"type\": \"integer\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"tea_coverage\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail_exp\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"tea_coverage_be_avail_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"inv_coverage_be_avail_lt\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_be_availability_ptype\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"bexfe_fe_availability_ptype\", \"type\": \"real\", \"description\": \"Sample\"},\n", "    {\"name\": \"fe_availability_ptype\", \"type\": \"real\", \"description\": \"Sample\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "3f3681f4-125c-4ae2-9643-7a37fbcaa0db", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inventory_core_metrics_v6\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"be_facility_id\",\n", "        \"fe_facility_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"dt_hour\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains Inventory Core Metrics for Movement and Replenishment\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ae94960e-5c11-4122-a6d3-d01987ee9bec", "metadata": {}, "outputs": [], "source": ["backfill_flag = 0\n", "\n", "\n", "def normal():\n", "    inventory_metrics_query = f\"\"\"\n", "With tea_tagging as (\n", "            select DISTINCT fe_city_id, fe_city_name, fe_facility_id, fe_facility_name, fe_outlet_id, be_hot_outlet_id, be_inv_outlet_id, be_facility_id, be_facility_name\n", "            from supply_etls.inventory_metrics_tea_tagging\n", "            where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "    ),\n", "    \n", "item_details as (\n", "            select --current_date as updated_at, \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "),\n", "        \n", "tea_tagging_item as (\n", "    select --current_date as updated_at, \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, store_assortment_type,\n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id,fe_city_id,fe_city_name, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "sku_complexity_fe as(\n", "    select \n", "    tea.be_facility_id, tea.fe_facility_id,\n", "    COUNT(CASE WHEN master_assortment_substate_id = 1 THEN tea.item_id END) AS count_active_skus_fe,\n", "    COUNT(CASE WHEN master_assortment_substate_id = 3 THEN tea.item_id END) AS count_temp_inactive_skus_fe\n", "    from tea_tagging_item tea\n", "    join item_details id \n", "        on tea.item_id = id.item_id\n", "    join rpc.product_facility_master_assortment ma \n", "        on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "    where master_assortment_substate_id = 1 or master_assortment_substate_id = 3\n", "    group by 1,2\n", "),\n", "\n", "be_line_items as (\n", "    select be_facility_id, sum(count_active_skus_fe) as lines_active, sum(count_temp_inactive_skus_fe) as lines_temp_inactive_skus\n", "    from sku_complexity_fe\n", "    group by 1\n", "),\n", "\n", "sku_complexity_be as (\n", "    select be_facility_id, \n", "        sum(case when master_assortment_substate_id = 1 THEN 1 END) as count_active_skus_be,\n", "        sum(case when master_assortment_substate_id = 3 THEN 1 END) as count_inactive_skus_be\n", "\n", "    from(\n", "        select item_id, be_facility_id,\n", "            case when sum(case when master_assortment_substate_id = 1 then 1 else 0 end) >= 1 then 1\n", "                when sum(case when master_assortment_substate_id = 3 then 1 else 0 end) >= 1 then 3\n", "                end as master_assortment_substate_id\n", "        from(\n", "            select tea.item_id, tea.be_facility_id, tea.fe_facility_id, master_assortment_substate_id\n", "            from tea_tagging_item tea\n", "            join rpc.product_facility_master_assortment ma on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "    )    \n", "    group by 1,2)\n", "    group by 1\n", "),\n", "\n", "item_product_mapping as (\n", "    select item_id, product_id, multiplier, avg_selling_price_ratio\n", "    from dwh.dim_item_product_offer_mapping\n", "    where is_current\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        --and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Packaged Goods', 'Dark Store')))\n", "        or facility_id in (2960,3127,3213)\n", "),\n", "\n", "sto_transfer_cpd_fe as (\n", "            select outlet_id,\n", "                    cp.item_id,\n", "                    cpd as fe_cpd\n", "            from ars.outlet_item_aps_derived_cpd cp\n", "            join tea_tagging_item tea on tea.item_id=cp.item_id and tea.fe_outlet_id=cp.outlet_id\n", "            where insert_ds_ist = cast(CURRENT_DATE-INTERVAL '1' DAY as varchar)\n", "            and lake_active_record\n", "            and for_date =CURRENT_DATE -INTERVAL '1' DAY\n", "            group by 1,2,3\n", "),\n", "\n", "\n", "sto_transfer_cpd_fe_final as(\n", "    select CURRENT_DATE-INTERVAL '1' DAY as date_,outlet_id, sum(fe_cpd) as fe_cpd\n", "    from sto_transfer_cpd_fe\n", "    group by 1,2\n", "),\n", "\n", "sto_transfer_cpd_be as (\n", "    select  CURRENT_DATE-INTERVAL '1' DAY as date_,be_facility_id, sum(fe_cpd) as be_cpd\n", "    from tea_tagging_item tea \n", "    join sto_transfer_cpd_fe cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "    group by 1,2\n", "),\n", "\n", "be_weights as (\n", "    select be_facility_id, be_cpd,\n", "        (be_cpd * 1.0000) / (sum(be_cpd) over(partition by date_)) as be_weight\n", "    from sto_transfer_cpd_be\n", "),\n", "\n", "fe_weights as (\n", "    select outlet_id, fe_cpd,\n", "        (fe_cpd * 1.0000) / (sum(fe_cpd) over(partition by date_)) as fe_weight\n", "    from sto_transfer_cpd_fe_final\n", "),\n", "\n", "ds_tagged as(\n", "    select \n", "        distinct\n", "        facility_id, \n", "        count(distinct tea.fe_facility_id) as no_of_ds_live,\n", "        count(distinct case when tea.store_assortment_type = 'EXPRESS' then tea.fe_facility_id end) as no_of_ds_live_express,\n", "        count(distinct case when tea.store_assortment_type = 'LONGTAIL' then tea.fe_facility_id end) as no_of_ds_live_longtail\n", "    from outlet_details od\n", "    left join tea_tagging_item tea on tea.be_facility_id = od.facility_id      \n", "        group by 1\n", "),  \n", "\n", "gmv_loss_unavail_one as(    \n", "    select \n", "        at_date_ist,\n", "        city,\n", "        be_hot_outlet,\n", "        be_name,\n", "        outlet_name,\n", "        facility_id,\n", "        outlet_id,\n", "        sum(total_loss) as total_loss\n", "        from consumer_etls.hourly_merchant_ptype_supply_availability_loss\n", "        where at_date_ist=CURRENT_DATE-INTERVAL '1' DAY \n", "        AND assortment_type IN ('packaged goods')\n", "        group by 1,2,3,4,5,6,7\n", "),\n", "\n", "gmv_loss_unavail_two as(    \n", "    select \n", "        at_date_ist,\n", "        city,\n", "        be_hot_outlet,\n", "        be_name,\n", "        outlet_name,\n", "        facility_id,\n", "        outlet_id,\n", "        sum(total_loss) as total_loss\n", "        from consumer_etls.hourly_merchant_ptype_supply_availability_loss_new_baseline\n", "        where at_date_ist=CURRENT_DATE-INTERVAL '1' DAY \n", "        AND assortment_type IN ('packaged goods')\n", "        group by 1,2,3,4,5,6,7\n", "),\n", "\n", "be_avail as(\n", "    select be_facility_id, be_facility_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_inv_weight_avail END) AS be_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_inv_binary_avail END) AS be_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_inv_weight_avail END) AS be_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_inv_binary_avail END) AS be_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_inv_weight_avail END) AS be_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_inv_binary_avail END) AS be_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_inv_weight_avail END) AS be_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_inv_binary_avail END) AS be_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_inv_weight_avail END) AS be_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_inv_binary_avail END) AS be_overall_overall_binary_avail\n", "    from supply_etls.be_inv_weighted_availability\n", "    where insert_ds_ist=CURRENT_DATE - INTERVAL '1' DAY\n", "    group  by 1,2\n", "),\n", "\n", "fe_avail as(\n", "    select fe_city_name, fe_inv_outlet_id, fe_inv_outlet_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN fe_weight_avail END) AS fe_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN fe_binary_avail END) AS fe_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN fe_weight_avail END) AS fe_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN fe_binary_avail END) AS fe_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN fe_weight_avail END) AS fe_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN fe_binary_avail END) AS fe_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN fe_weight_avail END) AS fe_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN fe_binary_avail END) AS fe_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN fe_weight_avail END) AS fe_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN fe_binary_avail END) AS fe_overall_overall_binary_avail\n", "    from supply_etls.fe_weighted_availability\n", "    where insert_ds_ist=CURRENT_DATE-INTERVAL '1' DAY\n", "    group by 1,2,3\n", "),\n", "\n", "be_fe_avail as(\n", "    select be_facility_id, be_facility_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_fe_weight_avail END) AS be_fe_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_fe_binary_avail END) AS be_fe_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_fe_weight_avail END) AS be_fe_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_fe_binary_avail END) AS be_fe_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_fe_weight_avail END) AS be_fe_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_fe_binary_avail END) AS be_fe_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_fe_weight_avail END) AS be_fe_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_fe_binary_avail END) AS be_fe_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_fe_weight_avail END) AS be_fe_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_fe_binary_avail END) AS be_fe_overall_overall_binary_avail\n", "    from supply_etls.be_fe_inv_weighted_availability\n", "    where insert_ds_ist=CURRENT_DATE-INTERVAL '1' DAY\n", "    group by 1,2\n", "),\n", "\n", "bexfe_avail as(\n", "    select be_facility_id, be_facility_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_weight_avail END) AS bexfe_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_binary_avail END) AS bexfe_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_weight_avail END) AS bexfe_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_binary_avail END) AS bexfe_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_weight_avail END) AS bexfe_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_binary_avail END) AS bexfe_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_weight_avail END) AS bexfe_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_binary_avail END) AS bexfe_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_weight_avail END) AS bexfe_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_binary_avail END) AS bexfe_overall_overall_binary_avail\n", "    from supply_etls.backend_x_frontend_weighted_availability\n", "    where insert_ds_ist=CURRENT_DATE-INTERVAL '1' DAY\n", "    group by 1,2\n", "),\n", "\n", "esto_loss as(\n", "    SELECT\n", "        outlet_id,\n", "        outlet_name,\n", "        sender_outlet_type,   --BE AND FE BOTH\n", "        receiver_outlet_id,\n", "        receiver_outlet_name,\n", "        receiver_outlet_type,    --BE AND FE BOTH\n", "        sender_outlet_city,\n", "        receiver_outlet_city,\n", "        transaction_type,    --3 way\n", "        sender_new,\n", "        receiver_new,\n", "        SUM(esto_excess_qty) AS esto_excess_qty,\n", "        SUM(esto_excess_value) AS esto_excess_value\n", "        FROM\n", "        storeops_etls.agg_daily_sender_receiver_esto_net_transfer_loss\n", "        WHERE                       \n", "        invoice_billed_date_ist = current_date - interval '1' day                 \n", "        and sender_outlet_type = 'BE' and receiver_outlet_type='FE'\n", "        group by\n", "        1,2,3,4,5,6,7,8,9,10,11\n", "),\n", "\n", "potential_sales as(\n", "    select facility_id,\n", "    sum(order_quantity) as order_quantity,sum(orders) as orders, sum(potential_order_quantity) as potential_order_quantity \n", "    from ars.daily_orders_and_availability doa\n", "    join item_details id on id.item_id=doa.item_id  \n", "    where lake_active_record and insert_ds_ist=cast(CURRENT_DATE-INTERVAL '1' DAY as varchar)\n", "    and order_date=CURRENT_DATE-INTERVAL '1' DAY\n", "    group by 1\n", "),\n", "\n", "sales as (\n", "    select fs.outlet_id, count(order_id) as count_orders,\n", "            sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price*avg_selling_price_ratio) as gmv\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            inner join item_details id on id.item_id=ip.item_id\n", "            where fs.order_create_dt_ist >= CURRENT_DATE-INTERVAL '1' DAY  \n", "            and fs.order_create_dt_ist <  CURRENT_DATE\n", "            and cart_checkout_ts_ist >= CURRENT_DATE-INTERVAL '1' DAY\n", "            and cart_checkout_ts_ist<CURRENT_DATE\n", "            and fs.order_current_status = 'DELIVERED'\n", "            group by 1\n", "),\n", "\n", "be_cap_util as(\n", "    select be_facility_id, be_facility_name, picking_qty_util, picking_qty_cap, picking_qty_util_per,\n", "            picking_sku_util, picking_sku_cap, picking_sku_util_per\n", "    from supply_etls.festive_metrics_temp_viz_be_cap_util\n", "    where insert_ds_ist=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "fe_cap_util as(\n", "    select be_facility_name, be_facility_id, fe_facility_name, fe_facility_id,\n", "            inward_util, inward_cap_in_qty, inward_util_per, truck_load_util, truck_load_cap_in_kg, truck_load_util_per\n", "    from supply_etls.festive_metrics_temp_viz_fe_cap_util\n", "    where insert_ds_ist=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "store_cap_util AS (\n", "SELECT      s.facility_id, s.facility_name, final_storage_type,\n", "        MAX(storage_cap) as storage_cap,\n", "        MAX(onshelf_inventory) as onshelf_inventory,\n", "        MAX(in_transit_quantity) as in_transit_quantity,\n", "        MAX(scaled_onshelf_inventory) as scaled_onshelf_inventory,\n", "        MAX(scaled_open_po_qty) as scaled_open_po_qty,\n", "        MAX(onshelf_utilisation) as onshelf_utilisation,\n", "        MAX(utilisation_with_open_stos) as utilisation_with_open_stos\n", "FROM    supply_etls.hourly_storage_util s\n", "WHERE   date(updated_at) = CURRENT_DATE - INTERVAL '1' DAY \n", "GROUP BY 1,2,3 ),\n", "\n", "be_storage_cap_util as(    \n", "    SELECT \n", "        s.facility_id,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN storage_cap END) AS be_regular_storage_capacity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN onshelf_inventory END) AS be_regular_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN in_transit_quantity END) AS be_regular_in_transit_quantity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN scaled_onshelf_inventory END) AS be_regular_scaled_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN scaled_open_po_qty END) AS be_regular_scaled_open_po_qty,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN onshelf_utilisation END) AS be_regular_onshelf_utilisation,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN utilisation_with_open_stos END) AS be_regular_utilisation_with_open_stos,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN storage_cap END) AS be_cold_storage_capacity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN onshelf_inventory END) AS be_cold_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN in_transit_quantity END) AS be_cold_in_transit_quantity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN scaled_onshelf_inventory END) AS be_cold_scaled_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN scaled_open_po_qty END) AS be_cold_scaled_open_po_qty,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN onshelf_utilisation END) AS be_cold_onshelf_utilisation,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN utilisation_with_open_stos END) AS be_cold_utilisation_with_open_stos,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN storage_cap END) AS be_heavy_storage_capacity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN onshelf_inventory END) AS be_heavy_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN in_transit_quantity END) AS be_heavy_in_transit_quantity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN scaled_onshelf_inventory END) AS be_heavy_scaled_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN scaled_open_po_qty END) AS be_heavy_scaled_open_po_qty,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN onshelf_utilisation END) AS be_heavy_onshelf_utilisation,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN utilisation_with_open_stos END) AS be_heavy_utilisation_with_open_stos,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN storage_cap END),0) AS be_frozen_storage_capacity,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN onshelf_inventory END),0) AS be_frozen_onshelf_inventory,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN in_transit_quantity END),0) AS be_frozen_in_transit_quantity,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN scaled_onshelf_inventory END),0) AS be_frozen_scaled_onshelf_inventory,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN scaled_open_po_qty END),0) AS be_frozen_scaled_open_po_qty,\n", "        COALESCE(MAX(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN onshelf_utilisation END),0) AS be_frozen_onshelf_utilisation,\n", "        COALESCE(MAX(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN utilisation_with_open_stos END),0) AS be_frozen_utilisation_with_open_stos\n", "    FROM \n", "        store_cap_util s\n", "    GROUP BY 1\n", "),\n", "\n", "fill_rate as(     \n", "select\n", "    be_outlet_id,\n", "    fe_outlet_id,\n", "    SUM(open_sto_qty) open_sto_qty_pre,   \n", "    sum(indent_raised_qty) as sto_raised_qty_fr,  \n", "    sum(intransit_qty) as dispatch_qty_fr,\n", "    sum(grn_qty) as grn_qty_fr,\n", "    sum(billed_qty) as billed_qty_fr,\n", "    SUM(intransit_qty - grn_qty) AS pending_grn_quantity,\n", "    SUM(dn_qty) AS dn_quantity_fr,\n", "    SUM(indent_raised_qty - grn_qty) AS open_sto_quantity_fr,\n", "    0 as billed_fr,\n", "    0 as dispatch_fr,\n", "    0 as grn_fr\n", "FROM\n", "     dwh.fact_ars_inventory_transfer_details ars\n", "    join item_details id on id.item_id =ars.item_id\n", "\n", "WHERE\n", "    ars_created_dt_ist>=CURRENT_DATE - INTERVAL '7' DAY\n", "    AND\n", "    indent_raised_ts_ist >= CURRENT_DATE - INTERVAL '15' DAY\n", "            AND indent_raised_ts_ist IS NOT NULL \n", "    AND (indent_raised_qty > 0 or  billed_qty > 0 or intransit_qty > 0 or \n", "                        grn_qty > 0 or (intransit_qty - grn_qty) > 0 or dn_qty > 0 or (indent_raised_qty - grn_qty) > 0)\n", "    group by 1,2\n", "),\n", "\n", "warehouse_otif as(  \n", "    with base as (\n", "        select\n", "            sto_created_date as date_, \n", "            fid.facility_name facility_name,\n", "            fid.facility_type,\n", "            fid.facility_id facility_id,\n", "            case when o.business_type_id = 7 then 'FE' else 'BE' end as flag,\n", "            (case when hour(sto_created_at - interval '330' minute) between 0 and 11 then 'Morning' else 'Evening' end) as shift,\n", "            sum(created_qty) as created_qty,\n", "            sum(picked_qty) as picked_qty,\n", "            sum(packaged_qty) as packaged_qty,\n", "            sum(case when min_sorted_at is not null then packaged_qty else 0 end) as sorted_qty,\n", "            sum(dispatched_qty) as dispatched_qty,\n", "            sum(case when max_picked_at is not null and max_picked_at <= sch_dispatch_time - interval '90' minute then picked_qty else 0 end) as on_time_picked_qty,\n", "            sum(case when max_packaged_at is not null and max_packaged_at <= sch_dispatch_time - interval '60' minute then packaged_qty else 0 end) as on_time_packaged_qty,\n", "            sum(case when min_sorted_at is not null and min_sorted_at <= sch_dispatch_time - interval '30' minute then packaged_qty else 0 end) as on_time_sorted_qty,\n", "            sum(case when max_dispatched_at is not null and max_dispatched_at <= sch_dispatch_time + interval '15' minute then dispatched_qty else 0 end) as on_time_dispatched_qty\n", "        from warehouse_etls.wh_analytics_sto_item_level_details si\n", "        join item_details id on id.item_id=si.item_id\n", "        left join warehouse_etls.wh_analytic_metrics_facility_ids fid on fid.outlet_id = si.sendor_outlet_id\n", "        left JOIN crates.facility f ON f.id = fid.facility_id\n", "        left JOIN retail.console_outlet o ON o.id = si.receiving_outlet_id\n", "        where sto_created_date >= CURRENT_DATE-INTERVAL '7' DAY     \n", "        and date(min_dispatched_at) = CURRENT_DATE - INTERVAL '1' DAY           \n", "        and date(min_grn_at) >= CURRENT_DATE-INTERVAL '1' DAY\n", "        group by 1,2,3,4,5,6\n", "    )\n", "        select \n", "            facility_id,\n", "            facility_name,\n", "            SUM(CASE WHEN flag = 'FE' THEN created_qty END) AS wotif_created_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_picked_qty END) AS wotif_on_time_picked_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_packaged_qty END) AS wotif_on_time_packaged_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_sorted_qty END) AS wotif_on_time_sorted_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_dispatched_qty END) AS wotif_on_time_dispatched_qty_FE,\n", "            SUM(CASE WHEN flag = 'BE' THEN created_qty END) AS wotif_created_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_picked_qty END) AS wotif_on_time_picked_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_packaged_qty END) AS wotif_on_time_packaged_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_sorted_qty END) AS wotif_on_time_sorted_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_dispatched_qty END) AS wotif_on_time_dispatched_qty_BE\n", "        from base\n", "        group by 1,2\n", "),\n", "\n", "fleet_vcpu as(\n", "select \n", "        a.wh_outlet_id,\n", "        a.wh_outlet_name,\n", "        a.ds_outlet_id,\n", "        a.ds_outlet_name,\n", "        sum(a.indent) as indent,\n", "        sum(a.indent_wt) as indent_wt,\n", "        sum(b.total_con_cost) as total_con_cost,\n", "        sum(b.total_con_cost)/sum(nullif(a.indent,0)) as per_unit_total_con_cost\n", "from supply_etls.fleet_master_data a\n", "join supply_etls.fleet_cms_cost b on a.consignment_id=b.consignment_id\n", "where a.trip_date = cast(current_date - interval '1' day as date)\n", "    and b.trip_creation_date >= cast(current_date - interval '15' day as date)\n", "    and a.dispatch_date=current_date - interval '1' day\n", "    and date(a.ds_reached) >= CURRENT_DATE-INTERVAl '1' DAY\n", "     and con_type = 'Grocery'\n", "group by 1,2,3,4\n", "),\n", "\n", "sto_cons_mapping as(\n", "    SELECT  s.id as sto_id,\n", "            td.consignment_id AS csmt_id\n", "    FROM pos.pos_invoice pi\n", "    JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "    JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "    JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "    left join supply_etls.fleet_trips ft on td.consignment_id=ft.consignment_id\n", "    AND co.business_type_id IN (1,12,19,20)\n", "    WHERE pi.insert_ds_ist between cast((CURRENT_DATE - interval '21' DAY) as varchar) and cast((CURRENT_DATE + interval '1' DAY) as varchar)\n", "      AND td.insert_ds_ist between cast((CURRENT_DATE - interval '21' DAY) as varchar) and cast((CURRENT_DATE + interval '1' DAY) as varchar)\n", "      and ft.trip_date between cast(current_date - interval '21' day as varchar) and cast((CURRENT_DATE + interval '1' DAY) as varchar)\n", "    AND invoice_type_id IN (5,14,16)\n", "    group by 1,2\n", "),\n", "\n", "fleet_otif as(              \n", "    with fleet_data as(\n", "    select fmd.consignment_id,\n", "            con_type,\n", "            fmd.trip_id,\n", "            trip_date,\n", "            trip_type,\n", "            source_node_id,\n", "            facility_id,\n", "            fmd.wh_outlet_id,\n", "            fmd.wh_outlet_name,\n", "            fmd.ds_node_id,\n", "            fmd.ds_outlet_id,\n", "            fmd.ds_outlet_name,\n", "            fmd.city,\n", "            fmd.indent,\n", "            fmd.indent_wt,\n", "            fmd.indent_volume,\n", "            ds_reached,\n", "            exp_ds_reach_system,\n", "            exp_ds_reach_manual,\n", "            load_start_status,\n", "            dispatch_delay_status,\n", "            frwd_enroute_breach_status,\n", "            vehicle_report_status,\n", "            trip_success,\n", "            ds_reporting_status,\n", "            sum(sto_raised_qty) as sto_raised_qty,\n", "            sum(grn_qty) as grn_qty,\n", "            min(min_grn_ts_ist) as min_grn_ts_ist,\n", "            max(max_ds_vehicle_arrival_at_ist) as max_ds_vehicle_arrival_at_ist   \n", "    from supply_etls.fleet_master_data fmd\n", "    join supply_etls.fleet_cms_cost b on fmd.consignment_id=b.consignment_id\n", "    left join sto_cons_mapping scm on scm.csmt_id=fmd.consignment_id\n", "    left join dwh.fact_inventory_transfer_details itd on itd.sto_id=scm.sto_id\n", "    inner join item_details id on id.item_id=itd.item_id\n", "    where date(ds_reached) >= current_date -interval '1' day \n", "    and trip_date>=cast(current_date - interval '1' day as date)\n", "    and dispatch_date=current_date - interval '1' day\n", "    and itd.invoice_billed_date_ist>=CURRENT_DATE-INTERVAL '7' DAY \n", "                    and date(sto_raised_date_ist) >= CURRENT_DATE-INTERVAL '7' DAY\n", "                    and date(min_ds_vehicle_arrival_at_ist) >= CURRENT_DATE-INTERVAL '1' DAY   \n", "                    and b.trip_creation_date >= cast(current_date - interval '15' day as date)\n", "                     and con_type = 'Grocery'\n", "                    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25\n", "    )\n", "    SELECT \n", "            facility_id,\n", "            wh_outlet_name,\n", "            ds_outlet_id,\n", "            ds_outlet_name,\n", "            sum(sto_raised_qty) as sto_raised_qty,\n", "            sum(indent) as indent_raised_fotif,\n", "           COUNT(DISTINCT trip_id) AS total_trips,\n", "           count(distinct CASE\n", "                              WHEN trip_success='perfect_trip' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS perfect_trips,\n", "           count(distinct case\n", "                              when vehicle_report_status='reporting_ontime' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS on_time_vehicle_report,\n", "           count(DISTINCT case\n", "                              when load_start_status='load_start_ontime' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS on_time_load_start,\n", "           count(DISTINCT case\n", "                              when dispatch_delay_status='dispatch_ontime' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS on_time_dispatch,\n", "           count(case\n", "                     when ds_reporting_status='On-time' then trip_id\n", "                 end)*1.000/nullif(COUNT(trip_id),0) AS on_time_ds_report,\n", "            sum(case\n", "                     when ds_reporting_status='On-time' then indent           \n", "                 end) AS on_time_ds_for_indent,\n", "             sum(case\n", "                 when ds_reporting_status='On-time' and min_grn_ts_ist < max_ds_vehicle_arrival_at_ist + interval '360' minute then indent\n", "             end) AS on_time_ds_for_indent_grn,\n", "           count(case\n", "                     when ds_reporting_status='Early' then trip_id\n", "                 end)*1.000/nullif(COUNT(trip_id),0) AS early_ds_report,\n", "            sum(case\n", "                     when ds_reporting_status='Early' then indent\n", "                 end) AS early_ds_report_for_indent\n", "    FROM fleet_data\n", "    GROUP BY 1,2,3,4\n", "),\n", "\n", "losses_and_grn as(                  \n", "            select \n", "            sender_outlet_id, receiver_outlet_id, \n", "            sum(sto_raised_qty) as sto_raised_qty,\n", "            sum(billed_qty) as billed_qty,\n", "            sum(dispatch_qty) as dispatch_qty,\n", "            sum(grn_qty) as grn_qty,\n", "            sum(b2b_amt) as b2b_amt,\n", "            sum(rsto_dump_amt) as rsto_dump_amt,\n", "            sum(grn_amt) as grn_amt,\n", "            sum(billed_amt) as billed_amt,\n", "            sum(total_dn_qty) as total_dn_qty,\n", "            sum(dn_short_qty) as dn_short_qty,\n", "            sum(dn_other_damage_qty) as dn_other_damage_qty,\n", "            sum(transfer_loss_qty) as transfer_loss_qty,\n", "            sum(transfer_loss_amt) as transfer_loss_amt,\n", "            sum(total_dn_amt) as total_dn_amt,       \n", "            sum(dn_short_amt) as dn_short_amt,\n", "            sum(dn_other_damage_amt) as dn_other_damage_amt,\n", "            sum(dn_other_qty) as  dn_other_qty,sum(dn_other_amt) as dn_other_amt,\n", "            sum(dn_other_excess_item_qty) as dn_other_excess_item_qty,sum(dn_other_excess_item_amt) as dn_other_excess_item_amt,\n", "            sum(dn_other_exp_nte_qty) as  dn_other_exp_nte_qty,sum(dn_other_exp_nte_amt) as dn_other_exp_nte_amt,\n", "            sum(dn_other_freebie_missing_qty) as  dn_other_freebie_missing_qty,sum(dn_other_freebie_missing_amt) as dn_other_freebie_missing_amt,\n", "            sum(dn_other_misc_qty) as  dn_other_misc_qty,sum(dn_other_misc_amt) as dn_other_misc_amt,\n", "            sum(dn_other_quality_issue_qty) as  dn_other_quality_issue_qty,sum(dn_other_quality_issue_amt) as dn_other_quality_issue_amt,\n", "            sum(dn_other_upc_not_scannable_qty) as  dn_other_upc_not_scannable_qty,sum(dn_other_upc_not_scannable_amt) as dn_other_upc_not_scannable_amt,\n", "            sum(dn_other_variant_mismatch_qty) as  dn_other_variant_mismatch_qty,sum(dn_other_variant_mismatch_amt) as dn_other_variant_mismatch_amt,\n", "            sum(dn_other_wrong_item_qty) as  dn_other_wrong_item_qty,sum(dn_other_wrong_item_amt) as dn_other_wrong_item_amt,\n", "            sum(lost_in_transit_positive_qty) as  lost_in_transit_positive_qty,sum(lost_in_transit_positive_amt) as lost_in_transit_positive_amt,\n", "            sum(lost_in_transit_negative_qty) as  lost_in_transit_negative_qty,sum(lost_in_transit_negative_amt) as lost_in_transit_negative_amt,\n", "            sum(case \n", "            when min_grn_ts_ist > max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end)*1.00/nullif(sum(sto_raised_qty),0) as delayed_grn_quantity,\n", "            sum(case \n", "            when min_grn_ts_ist > max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end) as delayed_grn_qty,\n", "            sum(case when min_grn_ts_ist <= max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end) as on_time_grn_qty,\n", "            sum(case when min_grn_ts_ist <= max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end)*1.00/nullif(sum(sto_raised_qty),0) as on_time_grn_quantity\n", "            from dwh.fact_inventory_transfer_details itd \n", "            join item_details id on id.item_id=itd.item_id\n", "            where itd.invoice_billed_date_ist>=CURRENT_DATE-INTERVAL '7' DAY \n", "                and date(sto_raised_date_ist) >= CURRENT_DATE-INTERVAL '7' DAY\n", "                and date(min_ds_vehicle_arrival_at_ist) = CURRENT_DATE-INTERVAL '1' DAY\n", "            group by 1,2\n", "),\n", "\n", "rsto as(                  \n", "            select \n", "            sender_outlet_id, receiver_outlet_id, \n", "            sum(rsto_dump_amt) as rsto_dump_amt\n", "            from dwh.fact_inventory_transfer_details itd \n", "            join item_details id on id.item_id=itd.item_id\n", "            where itd.invoice_billed_date_ist>=CURRENT_DATE-INTERVAL '7' DAY \n", "                and date(sto_raised_date_ist) >= CURRENT_DATE-INTERVAL '7' DAY\n", "                and date(min_ds_vehicle_arrival_at_ist) = CURRENT_DATE-INTERVAL '1' DAY\n", "            group by 1,2\n", "),\n", "\n", "planned_quantity_item as (\n", "select outlet_id,oiu.item_id, final_inventory\n", "    from ars.sequenced_outlet_item_universe oiu\n", "    where final_inventory<0\n", "    and date(created_at)=CURRENT_DATE-INTERVAL '1' DAY\n", "    and insert_ds_ist= cast(CURRENT_DATE-INTERVAL '1' DAY as varchar)\n", "),\n", "\n", "planned_quantity_final as(\n", "    select outlet_id, sum(abs(final_inventory)) as planned_quantity\n", "    from planned_quantity_item\n", "    group by 1\n", "),\n", "\n", "planned_quantity_item_be_avail as (\n", "select to_outlet_id, item_id, sum(abs(cycle_start_inventory)) as cycle_start_inventory\n", "    from ars.frontend_cycle_sto_quantity\n", "    where cycle_start_inventory<0\n", "    and partition_field= cast(CURRENT_DATE-INTERVAL '1' DAY as varchar)\n", "    and sto_date = CURRENT_DATE-INTERVAL '1' DAY\n", "    and lake_active_record\n", "    group by 1,2\n", "),\n", "\n", "planned_quantity_final_be_avail as(\n", "    select to_outlet_id, sum(abs(cycle_start_inventory)) as planned_quantity_be_avail\n", "    from planned_quantity_item_be_avail\n", "    group by 1\n", "),\n", "\n", "--oos_tat as(\n", "--    select * from supply_etls.inventory_core_metrics_oos_tat\n", "--    where date_=CURRENT_DATE-INTERVAL '1' DAY\n", "--),\n", "\n", "inventory_health as(\n", "    select * from supply_etls.inventory_health\n", "    where date_=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "dau_breakup as(\n", "    select * from supply_etls.dau_coverage\n", "    where insert_ds_ist=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "bexfe_ptype_avail as(\n", "    select * from supply_etls.bexfe_ptype_availability\n", "    where date_=CURRENT_DATE-INTERVAL '1' DAY\n", "    ),\n", "\n", "fe_ptype_avail as(\n", "    select * from supply_etls.fe_ptype_availability\n", "    where date_=CURRENT_DATE-INTERVAL '1' DAY\n", "),\n", "\n", "\n", "final as(\n", "    select  CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "            tea.fe_city_id as city_id,\n", "            tea.fe_city_name as city_name,\n", "            od.facility_id as be_facility_id,\n", "            od.inv_outlet_id as be_inv_outlet_id,\n", "            od.facility_name as be_facility_name,\n", "            \n", "            ds.no_of_ds_live,\n", "            ds.no_of_ds_live_express,\n", "            ds.no_of_ds_live_longtail,\n", "            \n", "            tea.fe_facility_id as fe_facility_id,\n", "            tea.fe_outlet_id as fe_outlet_id,\n", "            tea.fe_facility_name as fe_facility_name,\n", "            \n", "            greatest(coalesce(gmv_one.total_loss,0),coalesce(gmv_two.total_loss,0)) as total_loss,\n", "            \n", "            fw.fe_cpd,\n", "            fw.fe_weight,\n", "            bw.be_cpd,\n", "            bw.be_weight,\n", "            0 as city_cpd,\n", "            0 as city_weight,\n", "            \n", "            db.tea_coverage as tea_coverage,\n", "            db.inv_coverage as inv_coverage,\n", "            db.tea_coverage_be_avail as tea_coverage_be_avail,\n", "            db.inv_coverage_be_avail as inv_coverage_be_avail,\n", "            db.tea_coverage_exp as tea_coverage_exp,\n", "            db.inv_coverage_exp as inv_coverage_exp,\n", "            db.tea_coverage_be_avail_exp as tea_coverage_be_avail_exp,\n", "            db.inv_coverage_be_avail_exp as inv_coverage_be_avail_exp,\n", "            db.tea_coverage_lt as tea_coverage_lt,\n", "            db.inv_coverage_lt as inv_coverage_lt,\n", "            db.tea_coverage_be_avail_lt as tea_coverage_be_avail_lt,\n", "            db.inv_coverage_be_avail_lt as inv_coverage_be_avail_lt,\n", "            \n", "            0 as city_active_express_weight_avail,\n", "            0 as city_active_express_binary_avail,\n", "            0 as city_active_longtail_weight_avail,\n", "            0 as city_active_longtail_binary_avail,\n", "            0 as city_temp_inactive_express_weight_avail,\n", "            0 as city_temp_inactive_express_binary_avail,\n", "            0 as city_temp_inactive_longtail_weight_avail,\n", "            0 as city_temp_inactive_longtail_binary_avail,\n", "            0 as city_overall_overall_weight_avail,\n", "            0 as city_overall_overall_binary_avail,\n", "            \n", "            bea.be_active_express_weight_avail,\n", "            bea.be_active_express_binary_avail,\n", "            bea.be_active_longtail_weight_avail,\n", "            bea.be_active_longtail_binary_avail,\n", "            bea.be_temp_inactive_express_weight_avail,\n", "            bea.be_temp_inactive_express_binary_avail,\n", "            bea.be_temp_inactive_longtail_weight_avail,\n", "            bea.be_temp_inactive_longtail_binary_avail,\n", "            bea.be_overall_overall_weight_avail,\n", "            bea.be_overall_overall_binary_avail,\n", "            \n", "            fa.fe_active_express_weight_avail,\n", "            fa.fe_active_express_binary_avail,\n", "            fa.fe_active_longtail_weight_avail,\n", "            fa.fe_active_longtail_binary_avail,\n", "            fa.fe_temp_inactive_express_weight_avail,\n", "            fa.fe_temp_inactive_express_binary_avail,\n", "            fa.fe_temp_inactive_longtail_weight_avail,\n", "            fa.fe_temp_inactive_longtail_binary_avail,\n", "            fa.fe_overall_overall_weight_avail,\n", "            fa.fe_overall_overall_binary_avail,\n", "            \n", "            befea.be_fe_active_express_weight_avail,\n", "            befea.be_fe_active_express_binary_avail,\n", "            befea.be_fe_active_longtail_weight_avail,\n", "            befea.be_fe_active_longtail_binary_avail,\n", "            befea.be_fe_temp_inactive_express_weight_avail,\n", "            befea.be_fe_temp_inactive_express_binary_avail,\n", "            befea.be_fe_temp_inactive_longtail_weight_avail,\n", "            befea.be_fe_temp_inactive_longtail_binary_avail,\n", "            befea.be_fe_overall_overall_weight_avail,\n", "            befea.be_fe_overall_overall_binary_avail,\n", "            \n", "            befe.bexfe_active_express_weight_avail,\n", "            befe.bexfe_active_express_binary_avail,\n", "            befe.bexfe_active_longtail_weight_avail,\n", "            befe.bexfe_active_longtail_binary_avail,\n", "            befe.bexfe_temp_inactive_express_weight_avail,\n", "            befe.bexfe_temp_inactive_express_binary_avail,\n", "            befe.bexfe_temp_inactive_longtail_weight_avail,\n", "            befe.bexfe_temp_inactive_longtail_binary_avail,\n", "            befe.bexfe_overall_overall_weight_avail,\n", "            befe.bexfe_overall_overall_binary_avail,\n", "            \n", "            0 as current_inventory,\n", "            \n", "            log.total_dn_qty,\n", "            log.dn_short_qty,\n", "            log.dn_other_damage_qty,\n", "            log.transfer_loss_qty,\n", "            log.transfer_loss_amt,\n", "            log.total_dn_amt,\n", "            log.dn_short_amt,\n", "            log.dn_other_damage_amt,\n", "            log.dn_other_qty,\n", "            log.dn_other_amt,\n", "            log.dn_other_excess_item_qty,\n", "            log.dn_other_excess_item_amt,\n", "            log.dn_other_exp_nte_qty,\n", "            log.dn_other_exp_nte_amt,\n", "            log.dn_other_freebie_missing_qty,\n", "            log.dn_other_freebie_missing_amt,\n", "            log.dn_other_misc_qty,\n", "            log.dn_other_misc_amt,\n", "            log.dn_other_quality_issue_qty,\n", "            log.dn_other_quality_issue_amt,\n", "            log.dn_other_upc_not_scannable_qty,\n", "            log.dn_other_upc_not_scannable_amt,\n", "            log.dn_other_variant_mismatch_qty,\n", "            log.dn_other_variant_mismatch_amt,\n", "            log.dn_other_wrong_item_qty,\n", "            log.dn_other_wrong_item_amt,\n", "            log.lost_in_transit_positive_qty,\n", "            log.lost_in_transit_positive_amt,\n", "            log.lost_in_transit_negative_qty,\n", "            log.lost_in_transit_negative_amt,\n", "            log.b2b_amt,\n", "            rsto.rsto_dump_amt,\n", "            log.grn_amt,\n", "            log.billed_amt,\n", "            \n", "            fvcpu.indent as fvcpu_indent,\n", "            fvcpu.indent_wt as fvcpu_indent_wt,\n", "            fvcpu.total_con_cost as fvcpu_total_con_cost,\n", "            fvcpu.per_unit_total_con_cost as fvcpu_per_unit_total_con_cost,\n", "            \n", "            eloss.esto_excess_qty,\n", "            eloss.esto_excess_value,\n", "            \n", "            0 as sto_quantity,\n", "            0 as v1_line_items,\n", "            0 as v2_line_items,\n", "            \n", "            pqf.planned_quantity,              \n", "            pqfba.planned_quantity_be_avail,   \n", "            \n", "            \n", "            s.count_orders,   \n", "            s.gmv,\n", "            s.qty_sold,\n", "            ps.potential_order_quantity,                \n", "            \n", "            becu.picking_qty_util,\n", "            becu.picking_qty_cap,\n", "            becu.picking_qty_util_per,\n", "            becu.picking_sku_util,\n", "            becu.picking_sku_cap,\n", "            becu.picking_sku_util_per,\n", "            \n", "            fecu.inward_util,\n", "            fecu.inward_cap_in_qty,\n", "            fecu.inward_util_per,\n", "            fecu.truck_load_util,\n", "            fecu.truck_load_cap_in_kg,\n", "            fecu.truck_load_util_per,\n", "            \n", "            bescu.be_regular_storage_capacity,\n", "            bescu.be_regular_onshelf_inventory,\n", "            bescu.be_regular_in_transit_quantity,\n", "            bescu.be_regular_scaled_onshelf_inventory,\n", "            bescu.be_regular_onshelf_utilisation,\n", "            bescu.be_regular_scaled_open_po_qty,\n", "            bescu.be_regular_utilisation_with_open_stos,\n", "            bescu.be_cold_storage_capacity,\n", "            bescu.be_cold_onshelf_inventory,\n", "            bescu.be_cold_in_transit_quantity,\n", "            bescu.be_cold_scaled_onshelf_inventory,\n", "            bescu.be_cold_scaled_open_po_qty,\n", "            bescu.be_cold_onshelf_utilisation,\n", "            bescu.be_cold_utilisation_with_open_stos,\n", "            bescu.be_heavy_storage_capacity,\n", "            bescu.be_heavy_onshelf_inventory,\n", "            bescu.be_heavy_in_transit_quantity,\n", "            bescu.be_heavy_scaled_onshelf_inventory,\n", "            bescu.be_heavy_scaled_open_po_qty,\n", "            bescu.be_heavy_onshelf_utilisation,\n", "            bescu.be_heavy_utilisation_with_open_stos,\n", "            bescu.be_frozen_storage_capacity,\n", "            bescu.be_frozen_onshelf_inventory,\n", "            bescu.be_frozen_in_transit_quantity,\n", "            bescu.be_frozen_scaled_onshelf_inventory,\n", "            bescu.be_frozen_scaled_open_po_qty,\n", "            bescu.be_frozen_onshelf_utilisation,\n", "            bescu.be_frozen_utilisation_with_open_stos,\n", "            \n", "            fescu.be_regular_storage_capacity as fe_regular_storage_capacity,\n", "            fescu.be_regular_onshelf_inventory as fe_regular_onshelf_inventory,\n", "            fescu.be_regular_in_transit_quantity as fe_regular_in_transit_quantity,\n", "            fescu.be_regular_scaled_onshelf_inventory as fe_regular_scaled_onshelf_inventory,\n", "            fescu.be_regular_onshelf_utilisation as fe_regular_onshelf_utilisation,\n", "            fescu.be_regular_scaled_open_po_qty as fe_regular_scaled_open_po_qty,\n", "            fescu.be_regular_utilisation_with_open_stos as fe_regular_utilisation_with_open_stos,\n", "            fescu.be_cold_storage_capacity as fe_cold_storage_capacity,\n", "            fescu.be_cold_onshelf_inventory as fe_cold_onshelf_inventory,\n", "            fescu.be_cold_in_transit_quantity as fe_cold_in_transit_quantity,\n", "            fescu.be_cold_scaled_onshelf_inventory as fe_cold_scaled_onshelf_inventory,\n", "            fescu.be_cold_scaled_open_po_qty as fe_cold_scaled_open_po_qty,\n", "            fescu.be_cold_onshelf_utilisation as fe_cold_onshelf_utilisation,\n", "            fescu.be_cold_utilisation_with_open_stos as fe_cold_utilisation_with_open_stos,\n", "            fescu.be_heavy_storage_capacity as fe_heavy_storage_capacity,\n", "            fescu.be_heavy_onshelf_inventory as fe_heavy_onshelf_inventory,\n", "            fescu.be_heavy_in_transit_quantity as fe_heavy_in_transit_quantity,\n", "            fescu.be_heavy_scaled_onshelf_inventory as fe_heavy_scaled_onshelf_inventory,\n", "            fescu.be_heavy_scaled_open_po_qty as fe_heavy_scaled_open_po_qty,\n", "            fescu.be_heavy_onshelf_utilisation as fe_heavy_onshelf_utilisation,\n", "            fescu.be_heavy_utilisation_with_open_stos as fe_heavy_utilisation_with_open_stos,\n", "            fescu.be_frozen_storage_capacity as fe_frozen_storage_capacity,\n", "            fescu.be_frozen_onshelf_inventory as fe_frozen_onshelf_inventory,\n", "            fescu.be_frozen_in_transit_quantity as fe_frozen_in_transit_quantity,\n", "            fescu.be_frozen_scaled_onshelf_inventory as fe_frozen_scaled_onshelf_inventory,\n", "            fescu.be_frozen_scaled_open_po_qty as fe_frozen_scaled_open_po_qty,\n", "            fescu.be_frozen_onshelf_utilisation as fe_frozen_onshelf_utilisation,\n", "            fescu.be_frozen_utilisation_with_open_stos as fe_frozen_utilisation_with_open_stos,\n", "            \n", "            fr.billed_qty_fr,\n", "            fr.dispatch_qty_fr,\n", "            fr.grn_qty_fr,\n", "            fr.sto_raised_qty_fr,\n", "            fr.billed_fr,\n", "            fr.dispatch_fr,\n", "            fr.grn_fr,\n", "            \n", "            wotif.wotif_created_qty_FE,\n", "            wotif.wotif_on_time_picked_qty_FE,\n", "            wotif.wotif_on_time_packaged_qty_FE,\n", "            wotif.wotif_on_time_sorted_qty_FE,\n", "            wotif.wotif_on_time_dispatched_qty_FE,\n", "            wotif.wotif_created_qty_BE,\n", "            wotif.wotif_on_time_picked_qty_BE,\n", "            wotif.wotif_on_time_packaged_qty_BE,\n", "            wotif.wotif_on_time_sorted_qty_BE,\n", "            wotif.wotif_on_time_dispatched_qty_BE,\n", "\n", "            fotif.indent_raised_fotif,         \n", "            fotif.total_trips,\n", "            fotif.perfect_trips,\n", "            fotif.on_time_vehicle_report,\n", "            fotif.on_time_load_start,\n", "            fotif.on_time_dispatch,\n", "            fotif.on_time_ds_report,\n", "            fotif.on_time_ds_for_indent,\n", "            fotif.on_time_ds_for_indent_grn,\n", "            fotif.early_ds_report,\n", "            fotif.early_ds_report_for_indent,\n", "            log.sto_raised_qty,\n", "            log.billed_qty,\n", "            log.grn_qty,\n", "            log.on_time_grn_quantity,\n", "            log.delayed_grn_quantity,\n", "            log.delayed_grn_qty,\n", "            log.on_time_grn_qty,\n", "            \n", "            scf.count_active_skus_fe,\n", "            scf.count_temp_inactive_skus_fe,\n", "            bli.lines_active,\n", "            bli.lines_temp_inactive_skus,\n", "            scb.count_active_skus_be,\n", "            scb.count_inactive_skus_be,\n", "            \n", "            0 as avg_out_of_stock_duration,\n", "            0 as avg_out_of_stock_duration_weighted,\n", "            \n", "            ih_one.net_inv_present as net_inv_present_be,\n", "            ih_one.net_inv_exposure as net_inv_exposure_be,\n", "            ih_one.net_inv_volume as net_inv_volume_be,\n", "            ih_one.doi_sto_cpd_basis as doi_sto_cpd_basis_be,\n", "            ih_one.doi_po_cpd_basis as doi_po_cpd_basis_be,\n", "            ih_one.sto_cpd as sto_cpd_be,\n", "            ih_one.po_cpd as po_cpd_be,\n", "            ih_one.excess_inv as excess_inv_be,\n", "            ih_one.excess_inv_exposure as excess_inv_exposure_be,\n", "            ih_one.excess_inv_volume as excess_inv_volume_be,\n", "            ih_one.no_sale_inv as no_sale_inv_be,\n", "            ih_one.no_sale_inv_exposure as no_sale_inv_exposure_be,\n", "            ih_one.no_sale_inv_volume as no_sale_inv_volume_be,\n", "            ih_one.cpd_depleted_inv as cpd_depleted_inv_be,\n", "            ih_one.cpd_depleted_inv_exposure as cpd_depleted_inv_exposure_be,\n", "            ih_one.cpd_depleted_inv_volume as cpd_depleted_inv_volume_be,\n", "            ih_one.expiring_inv as expiring_inv_be,\n", "            ih_one.expiring_inv_exposure as expiring_inv_exposure_be,\n", "            ih_one.expiring_inv_volume as expiring_inv_volume_be,\n", "            ih_one.overall_dead as overall_dead_be,\n", "            ih_one.overall_dead_exposure as overall_dead_exposure_be,\n", "            ih_one.overall_dead_volume as overall_dead_volume_be,\n", "            \n", "            ih_two.net_inv_present as net_inv_present_fe,\n", "            ih_two.net_inv_exposure as net_inv_exposure_fe,\n", "            ih_two.net_inv_volume as net_inv_volume_fe,\n", "            ih_two.doi_sto_cpd_basis as doi_sto_cpd_basis_fe,\n", "            ih_two.doi_po_cpd_basis as doi_po_cpd_basis_fe,\n", "            ih_two.sto_cpd as sto_cpd_fe,\n", "            ih_two.po_cpd as po_cpd_fe,\n", "            ih_two.excess_inv as excess_inv_fe,\n", "            ih_two.excess_inv_exposure as excess_inv_exposure_fe,\n", "            ih_two.excess_inv_volume as excess_inv_volume_fe,\n", "            ih_two.no_sale_inv as no_sale_inv_fe,\n", "            ih_two.no_sale_inv_exposure as no_sale_inv_exposure_fe,\n", "            ih_two.no_sale_inv_volume as no_sale_inv_volume_fe,\n", "            ih_two.cpd_depleted_inv as cpd_depleted_inv_fe,\n", "            ih_two.cpd_depleted_inv_exposure as cpd_depleted_inv_exposure_fe,\n", "            ih_two.cpd_depleted_inv_volume as cpd_depleted_inv_volume_fe,\n", "            ih_two.expiring_inv as expiring_inv_fe,\n", "            ih_two.expiring_inv_exposure as expiring_inv_exposure_fe,\n", "            ih_two.expiring_inv_volume as expiring_inv_volume_fe,\n", "            ih_two.overall_dead as overall_dead_fe,\n", "            ih_two.overall_dead_exposure as overall_dead_exposure_fe,\n", "            ih_two.overall_dead_volume as overall_dead_volume_fe,\n", "            \n", "            bexfepa.be_availability as bexfe_be_availability_ptype,\n", "            bexfepa.fe_availability as bexfe_fe_availability_ptype,\n", "            \n", "            fepa.fe_availability as fe_availability_ptype\n", "            \n", "        from\n", "            outlet_details od\n", "        join tea_tagging tea on tea.be_facility_id=od.facility_id \n", "        left join ds_tagged ds on ds.facility_id=od.facility_id\n", "        left join gmv_loss_unavail_one gmv_one on gmv_one.facility_id=tea.fe_facility_id and cast(gmv_one.be_hot_outlet as int)=tea.be_hot_outlet_id\n", "        left join gmv_loss_unavail_two gmv_two on gmv_two.facility_id=tea.fe_facility_id and cast(gmv_two.be_hot_outlet as int)=tea.be_hot_outlet_id\n", "        left join be_avail bea on bea.be_facility_id=od.facility_id\n", "        left join fe_avail fa on fa.fe_inv_outlet_id=tea.fe_outlet_id\n", "        left join be_fe_avail befea on befea.be_facility_id=od.facility_id\n", "        left join bexfe_avail befe on befe.be_facility_id=od.facility_id\n", "        left join fleet_vcpu fvcpu on fvcpu.wh_outlet_id=od.inv_outlet_id and fvcpu.ds_outlet_id=tea.fe_outlet_id\n", "        left join esto_loss eloss on eloss.outlet_id=od.inv_outlet_id and eloss.receiver_outlet_id=tea.fe_outlet_id\n", "        left join sales s on s.outlet_id=tea.fe_outlet_id\n", "        left join potential_sales ps on ps.facility_id=tea.fe_facility_id\n", "        left join be_cap_util becu on becu.be_facility_id=od.facility_id\n", "        left join fe_cap_util fecu on fecu.be_facility_id=od.facility_id and fecu.fe_facility_id=tea.fe_facility_id\n", "        left join be_storage_cap_util bescu on bescu.facility_id=tea.be_facility_id \n", "        left join be_storage_cap_util fescu on fescu.facility_id=tea.fe_facility_id   \n", "        left join fill_rate fr on fr.be_outlet_id=od.hot_outlet_id and fr.fe_outlet_id=tea.fe_outlet_id       \n", "        left join warehouse_otif wotif on wotif.facility_id=od.facility_id\n", "        left join losses_and_grn log on log.sender_outlet_id=od.inv_outlet_id and log.receiver_outlet_id=tea.fe_outlet_id\n", "        left join rsto on rsto.sender_outlet_id=tea.fe_outlet_id and rsto.receiver_outlet_id=od.inv_outlet_id\n", "        left join fleet_otif fotif on fotif.facility_id=od.facility_id and fotif.ds_outlet_id=tea.fe_outlet_id\n", "        left join fe_weights fw on fw.outlet_id=tea.fe_outlet_id\n", "        left join be_weights bw on bw.be_facility_id=od.facility_id\n", "        left join sku_complexity_fe scf on scf.be_facility_id=od.facility_id and scf.fe_facility_id=tea.fe_facility_id\n", "        left join sku_complexity_be scb on scb.be_facility_id=od.facility_id\n", "        left join be_line_items bli on bli.be_facility_id=od.facility_id\n", "        left join planned_quantity_final pqf on pqf.outlet_id=tea.fe_outlet_id\n", "        left join planned_quantity_final_be_avail pqfba on pqfba.to_outlet_id=tea.fe_outlet_id\n", "        --left join oos_tat oost on oost.facility_id=tea.fe_facility_id\n", "        left join inventory_health ih_one on ih_one.facility_id=tea.be_facility_id\n", "        left join inventory_health ih_two on ih_two.facility_id=tea.fe_facility_id\n", "        left join dau_breakup db on db.be_facility_id=cast(tea.be_facility_id as varchar)\n", "        left join bexfe_ptype_avail bexfepa on bexfepa.be_facility_id=tea.be_facility_id\n", "        left join fe_ptype_avail fepa on fepa.be_facility_id=tea.be_facility_id and fepa.fe_facility_id=tea.fe_facility_id\n", ")\n", "\n", "select \n", "    *\n", "from final\n", "\n", "        \"\"\"\n", "    to_trino(inventory_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "f0ff06da-2aad-4dcd-a38a-e5d247e261b3", "metadata": {}, "outputs": [], "source": ["def backfill():\n", "\n", "    today = datetime.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=53)\n", "\n", "    for i in range(4):\n", "        backfill_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        print(f\"Backfilling data for: {backfill_date.strftime('%Y-%m-%d')}\")\n", "        # Convert backfill_date into the format 'YYYY-MM-DD'\n", "        formatted_date = backfill_date.strftime(\"%Y-%m-%d\")\n", "\n", "        inventory_metrics_query = f\"\"\"\n", "With tea_tagging as (\n", "            select DISTINCT fe_city_id, fe_city_name, fe_facility_id, fe_facility_name, fe_outlet_id, be_hot_outlet_id, be_inv_outlet_id, be_facility_id, be_facility_name\n", "            from supply_etls.inventory_metrics_tea_tagging\n", "            where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "    ),\n", "    \n", "item_details as (\n", "            select --current_date as updated_at, \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "),\n", "        \n", "tea_tagging_item as (\n", "    select --current_date as updated_at, \n", "        fe_outlet_id, fe_facility_id, item_id, assortment_type, store_assortment_type,\n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id,fe_city_id,fe_city_name, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "sku_complexity_fe as(\n", "    select \n", "    tea.be_facility_id, tea.fe_facility_id,\n", "    COUNT(CASE WHEN master_assortment_substate_id = 1 THEN tea.item_id END) AS count_active_skus_fe,\n", "    COUNT(CASE WHEN master_assortment_substate_id = 3 THEN tea.item_id END) AS count_temp_inactive_skus_fe\n", "    from tea_tagging_item tea\n", "    join item_details id \n", "        on tea.item_id = id.item_id\n", "    join rpc.product_facility_master_assortment ma \n", "        on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "    where master_assortment_substate_id = 1 or master_assortment_substate_id = 3\n", "    group by 1,2\n", "),\n", "\n", "be_line_items as (\n", "    select be_facility_id, sum(count_active_skus_fe) as lines_active, sum(count_temp_inactive_skus_fe) as lines_temp_inactive_skus\n", "    from sku_complexity_fe\n", "    group by 1\n", "),\n", "\n", "sku_complexity_be as (\n", "    select be_facility_id, \n", "        sum(case when master_assortment_substate_id = 1 THEN 1 END) as count_active_skus_be,\n", "        sum(case when master_assortment_substate_id = 3 THEN 1 END) as count_inactive_skus_be\n", "\n", "    from(\n", "        select item_id, be_facility_id,\n", "            case when sum(case when master_assortment_substate_id = 1 then 1 else 0 end) >= 1 then 1\n", "                when sum(case when master_assortment_substate_id = 3 then 1 else 0 end) >= 1 then 3\n", "                end as master_assortment_substate_id\n", "        from(\n", "            select tea.item_id, tea.be_facility_id, tea.fe_facility_id, master_assortment_substate_id\n", "            from tea_tagging_item tea\n", "            join rpc.product_facility_master_assortment ma on tea.item_id = ma.item_id and tea.fe_facility_id = ma.facility_id\n", "    )    \n", "    group by 1,2)\n", "    group by 1\n", "),\n", "\n", "item_product_mapping as (\n", "    select item_id, product_id, multiplier, avg_selling_price_ratio\n", "    from dwh.dim_item_product_offer_mapping\n", "    where is_current\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        --and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Packaged Goods', 'Dark Store')))\n", "        or facility_id in (2960,3127,3213)\n", "),\n", "\n", "sto_transfer_cpd_fe as (\n", "            select outlet_id,\n", "                    cp.item_id,\n", "                    cpd as fe_cpd\n", "            from ars.outlet_item_aps_derived_cpd cp\n", "            join tea_tagging_item tea on tea.item_id=cp.item_id and tea.fe_outlet_id=cp.outlet_id\n", "            where insert_ds_ist = cast(date('{formatted_date}')-INTERVAL '1' DAY as varchar)\n", "            and lake_active_record\n", "            and for_date =date('{formatted_date}') -INTERVAL '1' DAY\n", "            group by 1,2,3\n", "),\n", "\n", "\n", "sto_transfer_cpd_fe_final as(\n", "    select date('{formatted_date}')-INTERVAL '1' DAY as date_,outlet_id, sum(fe_cpd) as fe_cpd\n", "    from sto_transfer_cpd_fe\n", "    group by 1,2\n", "),\n", "\n", "sto_transfer_cpd_be as (\n", "    select  date('{formatted_date}')-INTERVAL '1' DAY as date_,be_facility_id, sum(fe_cpd) as be_cpd\n", "    from tea_tagging_item tea \n", "    join sto_transfer_cpd_fe cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "    group by 1,2\n", "),\n", "\n", "be_weights as (\n", "    select be_facility_id, be_cpd,\n", "        (be_cpd * 1.0000) / (sum(be_cpd) over(partition by date_)) as be_weight\n", "    from sto_transfer_cpd_be\n", "),\n", "\n", "fe_weights as (\n", "    select outlet_id, fe_cpd,\n", "        (fe_cpd * 1.0000) / (sum(fe_cpd) over(partition by date_)) as fe_weight\n", "    from sto_transfer_cpd_fe_final\n", "),\n", "\n", "ds_tagged as(\n", "    select \n", "        distinct\n", "        facility_id, \n", "        count(distinct tea.fe_facility_id) as no_of_ds_live,\n", "        count(distinct case when tea.store_assortment_type = 'EXPRESS' then tea.fe_facility_id end) as no_of_ds_live_express,\n", "        count(distinct case when tea.store_assortment_type = 'LONGTAIL' then tea.fe_facility_id end) as no_of_ds_live_longtail\n", "    from outlet_details od\n", "    left join tea_tagging_item tea on tea.be_facility_id = od.facility_id      \n", "        group by 1\n", "),  \n", "\n", "gmv_loss_unavail_one as(    \n", "    select \n", "        at_date_ist,\n", "        city,\n", "        be_hot_outlet,\n", "        be_name,\n", "        outlet_name,\n", "        facility_id,\n", "        outlet_id,\n", "        sum(total_loss) as total_loss\n", "        from consumer_etls.hourly_merchant_ptype_supply_availability_loss\n", "        where at_date_ist=date('{formatted_date}')-INTERVAL '1' DAY \n", "        AND assortment_type IN ('packaged goods')\n", "        group by 1,2,3,4,5,6,7\n", "),\n", "\n", "gmv_loss_unavail_two as(    \n", "    select \n", "        at_date_ist,\n", "        city,\n", "        be_hot_outlet,\n", "        be_name,\n", "        outlet_name,\n", "        facility_id,\n", "        outlet_id,\n", "        sum(total_loss) as total_loss\n", "        from consumer_etls.hourly_merchant_ptype_supply_availability_loss_new_baseline\n", "        where at_date_ist=date('{formatted_date}')-INTERVAL '1' DAY \n", "        AND assortment_type IN ('packaged goods')\n", "        group by 1,2,3,4,5,6,7\n", "),\n", "\n", "be_avail as(\n", "    select be_facility_id, be_facility_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_inv_weight_avail END) AS be_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_inv_binary_avail END) AS be_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_inv_weight_avail END) AS be_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_inv_binary_avail END) AS be_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_inv_weight_avail END) AS be_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_inv_binary_avail END) AS be_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_inv_weight_avail END) AS be_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_inv_binary_avail END) AS be_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_inv_weight_avail END) AS be_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_inv_binary_avail END) AS be_overall_overall_binary_avail\n", "    from supply_etls.be_inv_weighted_availability\n", "    where insert_ds_ist=date('{formatted_date}') - INTERVAL '1' DAY\n", "    group  by 1,2\n", "),\n", "\n", "fe_avail as(\n", "    select fe_city_name, fe_inv_outlet_id, fe_inv_outlet_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN fe_weight_avail END) AS fe_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN fe_binary_avail END) AS fe_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN fe_weight_avail END) AS fe_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN fe_binary_avail END) AS fe_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN fe_weight_avail END) AS fe_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN fe_binary_avail END) AS fe_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN fe_weight_avail END) AS fe_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN fe_binary_avail END) AS fe_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN fe_weight_avail END) AS fe_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN fe_binary_avail END) AS fe_overall_overall_binary_avail\n", "    from supply_etls.fe_weighted_availability\n", "    where insert_ds_ist=date('{formatted_date}')-INTERVAL '1' DAY\n", "    group by 1,2,3\n", "),\n", "\n", "be_fe_avail as(\n", "    select be_facility_id, be_facility_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_fe_weight_avail END) AS be_fe_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_fe_binary_avail END) AS be_fe_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_fe_weight_avail END) AS be_fe_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_fe_binary_avail END) AS be_fe_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_fe_weight_avail END) AS be_fe_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_fe_binary_avail END) AS be_fe_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_fe_weight_avail END) AS be_fe_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_fe_binary_avail END) AS be_fe_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_fe_weight_avail END) AS be_fe_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_fe_binary_avail END) AS be_fe_overall_overall_binary_avail\n", "    from supply_etls.be_fe_inv_weighted_availability\n", "    where insert_ds_ist=date('{formatted_date}')-INTERVAL '1' DAY\n", "    group by 1,2\n", "),\n", "\n", "bexfe_avail as(\n", "    select be_facility_id, be_facility_name,\n", "    -- Active + Express\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_weight_avail END) AS bexfe_active_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'EXPRESS' THEN backend_binary_avail END) AS bexfe_active_express_binary_avail,\n", "    -- Active + Longtail\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_weight_avail END) AS bexfe_active_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'active' AND express_longtail = 'LONGTAIL' THEN backend_binary_avail END) AS bexfe_active_longtail_binary_avail,\n", "    -- Temp Inactive + Express\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_weight_avail END) AS bexfe_temp_inactive_express_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'EXPRESS' THEN backend_binary_avail END) AS bexfe_temp_inactive_express_binary_avail,\n", "    -- Temp Inactive + Longtail\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_weight_avail END) AS bexfe_temp_inactive_longtail_weight_avail,\n", "    SUM(CASE WHEN assortment = 'temp_inactive' AND express_longtail = 'LONGTAIL' THEN backend_binary_avail END) AS bexfe_temp_inactive_longtail_binary_avail,\n", "    -- Overall + Overall\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_weight_avail END) AS bexfe_overall_overall_weight_avail,\n", "    SUM(CASE WHEN assortment = 'overall' AND express_longtail = 'overall' THEN backend_binary_avail END) AS bexfe_overall_overall_binary_avail\n", "    from supply_etls.backend_x_frontend_weighted_availability\n", "    where insert_ds_ist=date('{formatted_date}')-INTERVAL '1' DAY\n", "    group by 1,2\n", "),\n", "\n", "esto_loss as(\n", "    SELECT\n", "        outlet_id,\n", "        outlet_name,\n", "        sender_outlet_type,   --BE AND FE BOTH\n", "        receiver_outlet_id,\n", "        receiver_outlet_name,\n", "        receiver_outlet_type,    --BE AND FE BOTH\n", "        sender_outlet_city,\n", "        receiver_outlet_city,\n", "        transaction_type,    --3 way\n", "        sender_new,\n", "        receiver_new,\n", "        SUM(esto_excess_qty) AS esto_excess_qty,\n", "        SUM(esto_excess_value) AS esto_excess_value\n", "        FROM\n", "        storeops_etls.agg_daily_sender_receiver_esto_net_transfer_loss\n", "        WHERE                       \n", "        invoice_billed_date_ist = date('{formatted_date}') - interval '1' day                 \n", "        and sender_outlet_type = 'BE' and receiver_outlet_type='FE'\n", "        group by\n", "        1,2,3,4,5,6,7,8,9,10,11\n", "),\n", "\n", "potential_sales as(\n", "    select facility_id,\n", "    sum(order_quantity) as order_quantity,sum(orders) as orders, sum(potential_order_quantity) as potential_order_quantity \n", "    from ars.daily_orders_and_availability doa\n", "    join item_details id on id.item_id=doa.item_id  \n", "    where lake_active_record and insert_ds_ist=cast(date('{formatted_date}')-INTERVAL '1' DAY as varchar)\n", "    and order_date=date('{formatted_date}')-INTERVAL '1' DAY\n", "    group by 1\n", "),\n", "\n", "sales as (\n", "    select fs.outlet_id, count(order_id) as count_orders,\n", "            sum(procured_quantity * multiplier) as qty_sold, sum(total_procurement_price*avg_selling_price_ratio) as gmv\n", "            from dwh.fact_sales_order_item_details fs\n", "            inner join item_product_mapping ip on ip.product_id = fs.product_id\n", "            inner join outlet_details od on od.hot_outlet_id = fs.outlet_id\n", "            inner join item_details id on id.item_id=ip.item_id\n", "            where fs.order_create_dt_ist >= date('{formatted_date}')-INTERVAL '1' DAY  \n", "            and fs.order_create_dt_ist <  date('{formatted_date}')\n", "            and cart_checkout_ts_ist >= date('{formatted_date}')-INTERVAL '1' DAY\n", "            and cart_checkout_ts_ist<date('{formatted_date}')\n", "            and fs.order_current_status = 'DELIVERED'\n", "            group by 1\n", "),\n", "\n", "be_cap_util as(\n", "    select be_facility_id, be_facility_name, picking_qty_util, picking_qty_cap, picking_qty_util_per,\n", "            picking_sku_util, picking_sku_cap, picking_sku_util_per\n", "    from supply_etls.festive_metrics_temp_viz_be_cap_util\n", "    where insert_ds_ist=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "fe_cap_util as(\n", "    select be_facility_name, be_facility_id, fe_facility_name, fe_facility_id,\n", "            inward_util, inward_cap_in_qty, inward_util_per, truck_load_util, truck_load_cap_in_kg, truck_load_util_per\n", "    from supply_etls.festive_metrics_temp_viz_fe_cap_util\n", "    where insert_ds_ist=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "store_cap_util AS (\n", "SELECT      s.facility_id, s.facility_name, final_storage_type,\n", "        MAX(storage_cap) as storage_cap,\n", "        MAX(onshelf_inventory) as onshelf_inventory,\n", "        MAX(in_transit_quantity) as in_transit_quantity,\n", "        MAX(scaled_onshelf_inventory) as scaled_onshelf_inventory,\n", "        MAX(scaled_open_po_qty) as scaled_open_po_qty,\n", "        MAX(onshelf_utilisation) as onshelf_utilisation,\n", "        MAX(utilisation_with_open_stos) as utilisation_with_open_stos\n", "FROM    supply_etls.hourly_storage_util s\n", "WHERE   date(updated_at) = date('{formatted_date}') - INTERVAL '1' DAY \n", "GROUP BY 1,2,3 ),\n", "\n", "be_storage_cap_util as(    \n", "    SELECT \n", "        s.facility_id,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN storage_cap END) AS be_regular_storage_capacity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN onshelf_inventory END) AS be_regular_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN in_transit_quantity END) AS be_regular_in_transit_quantity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN scaled_onshelf_inventory END) AS be_regular_scaled_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN scaled_open_po_qty END) AS be_regular_scaled_open_po_qty,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN onshelf_utilisation END) AS be_regular_onshelf_utilisation,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%REGULAR%%' THEN utilisation_with_open_stos END) AS be_regular_utilisation_with_open_stos,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN storage_cap END) AS be_cold_storage_capacity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN onshelf_inventory END) AS be_cold_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN in_transit_quantity END) AS be_cold_in_transit_quantity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN scaled_onshelf_inventory END) AS be_cold_scaled_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN scaled_open_po_qty END) AS be_cold_scaled_open_po_qty,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN onshelf_utilisation END) AS be_cold_onshelf_utilisation,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%COLD%%' THEN utilisation_with_open_stos END) AS be_cold_utilisation_with_open_stos,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN storage_cap END) AS be_heavy_storage_capacity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN onshelf_inventory END) AS be_heavy_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN in_transit_quantity END) AS be_heavy_in_transit_quantity,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN scaled_onshelf_inventory END) AS be_heavy_scaled_onshelf_inventory,\n", "        SUM(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN scaled_open_po_qty END) AS be_heavy_scaled_open_po_qty,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN onshelf_utilisation END) AS be_heavy_onshelf_utilisation,\n", "        MAX(CASE WHEN final_storage_type LIKE '%%HEAVY%%' THEN utilisation_with_open_stos END) AS be_heavy_utilisation_with_open_stos,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN storage_cap END),0) AS be_frozen_storage_capacity,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN onshelf_inventory END),0) AS be_frozen_onshelf_inventory,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN in_transit_quantity END),0) AS be_frozen_in_transit_quantity,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN scaled_onshelf_inventory END),0) AS be_frozen_scaled_onshelf_inventory,\n", "        COALESCE(SUM(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN scaled_open_po_qty END),0) AS be_frozen_scaled_open_po_qty,\n", "        COALESCE(MAX(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN onshelf_utilisation END),0) AS be_frozen_onshelf_utilisation,\n", "        COALESCE(MAX(CASE WHEN final_storage_type LIKE '%%FROZEN%%' THEN utilisation_with_open_stos END),0) AS be_frozen_utilisation_with_open_stos\n", "    FROM \n", "        store_cap_util s\n", "    GROUP BY 1\n", "),\n", "\n", "fill_rate as(     \n", "select\n", "    be_outlet_id,\n", "    fe_outlet_id,\n", "    SUM(open_sto_qty) open_sto_qty_pre,   \n", "    sum(indent_raised_qty) as sto_raised_qty_fr,  \n", "    sum(intransit_qty) as dispatch_qty_fr,\n", "    sum(grn_qty) as grn_qty_fr,\n", "    sum(billed_qty) as billed_qty_fr,\n", "    SUM(intransit_qty - grn_qty) AS pending_grn_quantity,\n", "    SUM(dn_qty) AS dn_quantity_fr,\n", "    SUM(indent_raised_qty - grn_qty) AS open_sto_quantity_fr,\n", "    0 as billed_fr,\n", "    0 as dispatch_fr,\n", "    0 as grn_fr\n", "FROM\n", "     dwh.fact_ars_inventory_transfer_details ars\n", "    join item_details id on id.item_id =ars.item_id\n", "\n", "WHERE\n", "    ars_created_dt_ist BETWEEN date('{formatted_date}') - INTERVAL '7' DAY AND date('{formatted_date}')\n", "    AND\n", "    indent_raised_ts_ist BETWEEN date('{formatted_date}') - INTERVAL '15' DAY AND date('{formatted_date}')\n", "            AND indent_raised_ts_ist IS NOT NULL \n", "    AND (indent_raised_qty > 0 or  billed_qty > 0 or intransit_qty > 0 or \n", "                        grn_qty > 0 or (intransit_qty - grn_qty) > 0 or dn_qty > 0 or (indent_raised_qty - grn_qty) > 0)\n", "    group by 1,2\n", "),\n", "\n", "warehouse_otif as(  \n", "    with base as (\n", "        select\n", "            sto_created_date as date_, \n", "            fid.facility_name facility_name,\n", "            fid.facility_type,\n", "            fid.facility_id facility_id,\n", "            case when o.business_type_id = 7 then 'FE' else 'BE' end as flag,\n", "            (case when hour(sto_created_at - interval '330' minute) between 0 and 11 then 'Morning' else 'Evening' end) as shift,\n", "            sum(created_qty) as created_qty,\n", "            sum(picked_qty) as picked_qty,\n", "            sum(packaged_qty) as packaged_qty,\n", "            sum(case when min_sorted_at is not null then packaged_qty else 0 end) as sorted_qty,\n", "            sum(dispatched_qty) as dispatched_qty,\n", "            sum(case when max_picked_at is not null and max_picked_at <= sch_dispatch_time - interval '90' minute then picked_qty else 0 end) as on_time_picked_qty,\n", "            sum(case when max_packaged_at is not null and max_packaged_at <= sch_dispatch_time - interval '60' minute then packaged_qty else 0 end) as on_time_packaged_qty,\n", "            sum(case when min_sorted_at is not null and min_sorted_at <= sch_dispatch_time - interval '30' minute then packaged_qty else 0 end) as on_time_sorted_qty,\n", "            sum(case when max_dispatched_at is not null and max_dispatched_at <= sch_dispatch_time + interval '15' minute then dispatched_qty else 0 end) as on_time_dispatched_qty\n", "        from warehouse_etls.wh_analytics_sto_item_level_details si\n", "        join item_details id on id.item_id=si.item_id\n", "        left join warehouse_etls.wh_analytic_metrics_facility_ids fid on fid.outlet_id = si.sendor_outlet_id\n", "        left JOIN crates.facility f ON f.id = fid.facility_id\n", "        left JOIN retail.console_outlet o ON o.id = si.receiving_outlet_id\n", "        where sto_created_date BETWEEN date('{formatted_date}')-INTERVAL '7' DAY AND date('{formatted_date}')\n", "        and date(min_dispatched_at) = date('{formatted_date}') - INTERVAL '1' DAY\n", "        and date(min_grn_at) BETWEEN date('{formatted_date}')-INTERVAL '1' DAY AND date('{formatted_date}') \n", "        group by 1,2,3,4,5,6\n", "    )\n", "        select \n", "            facility_id,\n", "            facility_name,\n", "            SUM(CASE WHEN flag = 'FE' THEN created_qty END) AS wotif_created_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_picked_qty END) AS wotif_on_time_picked_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_packaged_qty END) AS wotif_on_time_packaged_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_sorted_qty END) AS wotif_on_time_sorted_qty_FE,\n", "            SUM(CASE WHEN flag = 'FE' THEN on_time_dispatched_qty END) AS wotif_on_time_dispatched_qty_FE,\n", "            SUM(CASE WHEN flag = 'BE' THEN created_qty END) AS wotif_created_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_picked_qty END) AS wotif_on_time_picked_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_packaged_qty END) AS wotif_on_time_packaged_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_sorted_qty END) AS wotif_on_time_sorted_qty_BE,\n", "            SUM(CASE WHEN flag = 'BE' THEN on_time_dispatched_qty END) AS wotif_on_time_dispatched_qty_BE\n", "        from base\n", "        group by 1,2\n", "),\n", "\n", "fleet_vcpu as(\n", "select \n", "        a.wh_outlet_id,\n", "        a.wh_outlet_name,\n", "        a.ds_outlet_id,\n", "        a.ds_outlet_name,\n", "        sum(a.indent) as indent,\n", "        sum(a.indent_wt) as indent_wt,\n", "        sum(b.total_con_cost) as total_con_cost,\n", "        sum(b.total_con_cost)/sum(nullif(a.indent,0)) as per_unit_total_con_cost\n", "from supply_etls.fleet_master_data a\n", "inner join supply_etls.fleet_cms_cost b on a.consignment_id=b.consignment_id\n", "where a.trip_date = date('{formatted_date}')-INTERVAl '1' DAY\n", "    and b.trip_creation_date BETWEEN cast(date('{formatted_date}') - interval '15' day as date) AND date('{formatted_date}')\n", "    and date(a.ds_reached) BETWEEN date('{formatted_date}')-INTERVAl '1' DAY AND date('{formatted_date}')\n", "     and con_type = 'Grocery'\n", "group by 1,2,3,4\n", "),\n", "\n", "sto_cons_mapping as(\n", "    SELECT  s.id as sto_id,\n", "            td.consignment_id AS csmt_id\n", "    FROM pos.pos_invoice pi\n", "    JOIN transit_server.transit_consignment_document td ON td.external_id = pi.invoice_id\n", "    JOIN po.sto s ON s.id = cast(pi.grofers_order_id AS int)\n", "    JOIN retail.console_outlet co ON co.id = pi.outlet_id\n", "    left join supply_etls.fleet_trips ft on td.consignment_id=ft.consignment_id\n", "    AND co.business_type_id IN (1,12,19,20)\n", "    WHERE pi.insert_ds_ist between cast((date('{formatted_date}') - interval '21' DAY) as varchar) and cast((date('{formatted_date}') + interval '1' DAY) as varchar)\n", "      AND td.insert_ds_ist between cast((date('{formatted_date}') - interval '21' DAY) as varchar) and cast((date('{formatted_date}') + interval '1' DAY) as varchar)\n", "      and ft.trip_date between cast(date('{formatted_date}') - interval '21' day as varchar) and cast((date('{formatted_date}') + interval '1' DAY) as varchar)\n", "    AND invoice_type_id IN (5,14,16)\n", "    group by 1,2\n", "),\n", "\n", "fleet_otif as(              \n", "\n", "    with fleet_data as(         \n", "    select fmd.consignment_id,\n", "            con_type,\n", "            fmd.trip_id,\n", "            trip_date,\n", "            trip_type,\n", "            source_node_id,\n", "            facility_id,\n", "            fmd.wh_outlet_id,\n", "            fmd.wh_outlet_name,\n", "            fmd.ds_node_id,\n", "            fmd.ds_outlet_id,\n", "            fmd.ds_outlet_name,\n", "            fmd.city,\n", "            fmd.indent,\n", "            fmd.indent_wt,\n", "            fmd.indent_volume,\n", "            ds_reached,\n", "            exp_ds_reach_system,\n", "            exp_ds_reach_manual,\n", "            load_start_status,\n", "            dispatch_delay_status,\n", "            frwd_enroute_breach_status,\n", "            vehicle_report_status,\n", "            trip_success,\n", "            ds_reporting_status,\n", "            sum(sto_raised_qty) as sto_raised_qty,\n", "            sum(grn_qty) as grn_qty,\n", "            min(min_grn_ts_ist) as min_grn_ts_ist,\n", "            max(max_ds_vehicle_arrival_at_ist) as max_ds_vehicle_arrival_at_ist             \n", "    from supply_etls.fleet_master_data fmd\n", "    join supply_etls.fleet_cms_cost b on fmd.consignment_id=b.consignment_id\n", "    left join sto_cons_mapping scm on scm.csmt_id=fmd.consignment_id\n", "    left join dwh.fact_inventory_transfer_details itd on itd.sto_id=scm.sto_id\n", "    join item_details id on id.item_id=itd.item_id\n", "    where date(ds_reached) BETWEEN date('{formatted_date}') -interval '1' day AND date('{formatted_date}')\n", "    and trip_date BETWEEN cast(date('{formatted_date}') - interval '1' day as date) and cast(date('{formatted_date}') as date)\n", "    and dispatch_date=date('{formatted_date}') - interval '1' day\n", "    and itd.invoice_billed_date_ist BETWEEN date('{formatted_date}')-INTERVAL '7' DAY AND date('{formatted_date}')\n", "    and date(sto_raised_date_ist) BETWEEN date('{formatted_date}')-INTERVAL '7' DAY AND date('{formatted_date}')\n", "    and date(min_ds_vehicle_arrival_at_ist) BETWEEN date('{formatted_date}')-INTERVAL '1' DAY AND date('{formatted_date}')\n", "    and b.trip_creation_date BETWEEN date('{formatted_date}')-INTERVAL '15' DAY AND date('{formatted_date}')    \n", "     and con_type = 'Grocery'\n", "    group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25\n", "    )\n", "    SELECT \n", "            facility_id,\n", "            wh_outlet_name,\n", "            ds_outlet_id,\n", "            ds_outlet_name,\n", "            sum(sto_raised_qty) as sto_raised_qty,\n", "            sum(indent) as indent_raised_fotif,\n", "           COUNT(DISTINCT trip_id) AS total_trips,\n", "           count(distinct CASE\n", "                              WHEN trip_success='perfect_trip' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS perfect_trips,\n", "           count(distinct case\n", "                              when vehicle_report_status='reporting_ontime' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS on_time_vehicle_report,\n", "           count(DISTINCT case\n", "                              when load_start_status='load_start_ontime' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS on_time_load_start,\n", "           count(DISTINCT case\n", "                              when dispatch_delay_status='dispatch_ontime' then trip_id\n", "                          end)*1.000/nullif(COUNT(DISTINCT trip_id),0) AS on_time_dispatch,\n", "           count(case\n", "                     when ds_reporting_status='On-time' then trip_id\n", "                 end)*1.000/nullif(COUNT(trip_id),0) AS on_time_ds_report,\n", "            sum(case\n", "                     when ds_reporting_status='On-time' then indent           \n", "                 end) AS on_time_ds_for_indent,\n", "             sum(case\n", "                 when ds_reporting_status='On-time' and min_grn_ts_ist < max_ds_vehicle_arrival_at_ist + interval '360' minute then indent\n", "             end) AS on_time_ds_for_indent_grn,\n", "           count(case\n", "                     when ds_reporting_status='Early' then trip_id\n", "                 end)*1.000/nullif(COUNT(trip_id),0) AS early_ds_report,\n", "            sum(case\n", "                     when ds_reporting_status='Early' then indent\n", "                 end) AS early_ds_report_for_indent\n", "    FROM fleet_data\n", "    GROUP BY 1,2,3,4\n", "),\n", "\n", "losses_and_grn as(                  \n", "            select \n", "            sender_outlet_id, receiver_outlet_id, \n", "            sum(sto_raised_qty) as sto_raised_qty,\n", "            sum(billed_qty) as billed_qty,\n", "            sum(dispatch_qty) as dispatch_qty,\n", "            sum(grn_qty) as grn_qty,\n", "            sum(b2b_amt) as b2b_amt,\n", "            sum(rsto_dump_amt) as rsto_dump_amt,\n", "            sum(grn_amt) as grn_amt,\n", "            sum(billed_amt) as billed_amt,\n", "            sum(total_dn_qty) as total_dn_qty,\n", "            sum(dn_short_qty) as dn_short_qty,\n", "            sum(dn_other_damage_qty) as dn_other_damage_qty,\n", "            sum(transfer_loss_qty) as transfer_loss_qty,\n", "            sum(transfer_loss_amt) as transfer_loss_amt,\n", "            sum(total_dn_amt) as total_dn_amt,       \n", "            sum(dn_short_amt) as dn_short_amt,\n", "            sum(dn_other_damage_amt) as dn_other_damage_amt,\n", "            sum(dn_other_qty) as  dn_other_qty,sum(dn_other_amt) as dn_other_amt,\n", "            sum(dn_other_excess_item_qty) as dn_other_excess_item_qty,sum(dn_other_excess_item_amt) as dn_other_excess_item_amt,\n", "            sum(dn_other_exp_nte_qty) as  dn_other_exp_nte_qty,sum(dn_other_exp_nte_amt) as dn_other_exp_nte_amt,\n", "            sum(dn_other_freebie_missing_qty) as  dn_other_freebie_missing_qty,sum(dn_other_freebie_missing_amt) as dn_other_freebie_missing_amt,\n", "            sum(dn_other_misc_qty) as  dn_other_misc_qty,sum(dn_other_misc_amt) as dn_other_misc_amt,\n", "            sum(dn_other_quality_issue_qty) as  dn_other_quality_issue_qty,sum(dn_other_quality_issue_amt) as dn_other_quality_issue_amt,\n", "            sum(dn_other_upc_not_scannable_qty) as  dn_other_upc_not_scannable_qty,sum(dn_other_upc_not_scannable_amt) as dn_other_upc_not_scannable_amt,\n", "            sum(dn_other_variant_mismatch_qty) as  dn_other_variant_mismatch_qty,sum(dn_other_variant_mismatch_amt) as dn_other_variant_mismatch_amt,\n", "            sum(dn_other_wrong_item_qty) as  dn_other_wrong_item_qty,sum(dn_other_wrong_item_amt) as dn_other_wrong_item_amt,\n", "            sum(lost_in_transit_positive_qty) as  lost_in_transit_positive_qty,sum(lost_in_transit_positive_amt) as lost_in_transit_positive_amt,\n", "            sum(lost_in_transit_negative_qty) as  lost_in_transit_negative_qty,sum(lost_in_transit_negative_amt) as lost_in_transit_negative_amt,\n", "            sum(case \n", "            when min_grn_ts_ist > max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end)*1.00/nullif(sum(sto_raised_qty),0) as delayed_grn_quantity,\n", "            sum(case \n", "            when min_grn_ts_ist > max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end) as delayed_grn_qty,\n", "            sum(case when min_grn_ts_ist <= max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end) as on_time_grn_qty,\n", "            sum(case when min_grn_ts_ist <= max_ds_vehicle_arrival_at_ist + interval '360' minute then grn_qty end)*1.00/nullif(sum(sto_raised_qty),0) as on_time_grn_quantity\n", "            from dwh.fact_inventory_transfer_details itd \n", "            join item_details id on id.item_id=itd.item_id\n", "            where itd.invoice_billed_date_ist BETWEEN date('{formatted_date}')-INTERVAL '7' DAY AND date('{formatted_date}')\n", "                and date(sto_raised_date_ist) BETWEEN date('{formatted_date}')-INTERVAL '7' DAY AND date('{formatted_date}')\n", "                and date(min_ds_vehicle_arrival_at_ist) = date('{formatted_date}')-INTERVAL '1' DAY\n", "            group by 1,2\n", "),\n", "\n", "rsto as(                  \n", "    select\n", "            sender_outlet_id, receiver_outlet_id,\n", "            sum(rsto_dump_amt) as rsto_dump_amt\n", "            from dwh.fact_inventory_transfer_details itd\n", "            join item_details id on id.item_id=itd.item_id\n", "            where itd.invoice_billed_date_ist BETWEEN date('{formatted_date}')-INTERVAL '7' DAY AND date('{formatted_date}')\n", "                and date(sto_raised_date_ist) BETWEEN date('{formatted_date}')-INTERVAL '7' DAY AND date('{formatted_date}')\n", "                and date(min_ds_vehicle_arrival_at_ist) = date('{formatted_date}')-INTERVAL '1' DAY\n", "            group by 1,2\n", "),\n", "\n", "planned_quantity_item as (\n", "select outlet_id,oiu.item_id, final_inventory\n", "    from ars.sequenced_outlet_item_universe oiu\n", "    where final_inventory<0\n", "    and date(created_at)=date('{formatted_date}')-INTERVAL '1' DAY\n", "    and insert_ds_ist= cast(date('{formatted_date}')-INTERVAL '1' DAY as varchar)\n", "),\n", "\n", "planned_quantity_final as(\n", "    select outlet_id, sum(abs(final_inventory)) as planned_quantity\n", "    from planned_quantity_item\n", "    group by 1\n", "),\n", "\n", "planned_quantity_item_be_avail as (\n", "select to_outlet_id, item_id, sum(abs(cycle_start_inventory)) as cycle_start_inventory\n", "    from ars.frontend_cycle_sto_quantity\n", "    where cycle_start_inventory<0\n", "    and partition_field= cast(date('{formatted_date}')-INTERVAL '1' DAY as varchar)\n", "    and sto_date = date('{formatted_date}')-INTERVAL '1' DAY\n", "    and lake_active_record\n", "    group by 1,2\n", "),\n", "\n", "planned_quantity_final_be_avail as(\n", "    select to_outlet_id, sum(abs(cycle_start_inventory)) as planned_quantity_be_avail\n", "    from planned_quantity_item_be_avail\n", "    group by 1\n", "),\n", "\n", "--oos_tat as(\n", "--    select * from supply_etls.inventory_core_metrics_oos_tat\n", "--    where date_=date('{formatted_date}')-INTERVAL '1' DAY\n", "--),\n", "\n", "inventory_health as(\n", "    select * from supply_etls.inventory_health\n", "    where date_=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "dau_breakup as(\n", "    select * from supply_etls.dau_coverage\n", "    where insert_ds_ist=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "bexfe_ptype_avail as(\n", "    select * from supply_etls.bexfe_ptype_availability\n", "    where date_=date('{formatted_date}')-INTERVAL '1' DAY\n", "    ),\n", "\n", "fe_ptype_avail as(\n", "    select * from supply_etls.fe_ptype_availability\n", "    where date_=date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "\n", "final as(\n", "    select  date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "            tea.fe_city_id as city_id,\n", "            tea.fe_city_name as city_name,\n", "            od.facility_id as be_facility_id,\n", "            od.inv_outlet_id as be_inv_outlet_id,\n", "            od.facility_name as be_facility_name,\n", "            \n", "            ds.no_of_ds_live,\n", "            ds.no_of_ds_live_express,\n", "            ds.no_of_ds_live_longtail,\n", "            \n", "            tea.fe_facility_id as fe_facility_id,\n", "            tea.fe_outlet_id as fe_outlet_id,\n", "            tea.fe_facility_name as fe_facility_name,\n", "            \n", "            greatest(coalesce(gmv_one.total_loss,0),coalesce(gmv_two.total_loss,0)) as total_loss,\n", "            \n", "            fw.fe_cpd,\n", "            fw.fe_weight,\n", "            bw.be_cpd,\n", "            bw.be_weight,\n", "            0 as city_cpd,\n", "            0 as city_weight,\n", "            \n", "            db.tea_coverage as tea_coverage,\n", "            db.inv_coverage as inv_coverage,\n", "            db.tea_coverage_be_avail as tea_coverage_be_avail,\n", "            db.inv_coverage_be_avail as inv_coverage_be_avail,\n", "            db.tea_coverage_exp as tea_coverage_exp,\n", "            db.inv_coverage_exp as inv_coverage_exp,\n", "            db.tea_coverage_be_avail_exp as tea_coverage_be_avail_exp,\n", "            db.inv_coverage_be_avail_exp as inv_coverage_be_avail_exp,\n", "            db.tea_coverage_lt as tea_coverage_lt,\n", "            db.inv_coverage_lt as inv_coverage_lt,\n", "            db.tea_coverage_be_avail_lt as tea_coverage_be_avail_lt,\n", "            db.inv_coverage_be_avail_lt as inv_coverage_be_avail_lt,\n", "            \n", "            0 as city_active_express_weight_avail,\n", "            0 as city_active_express_binary_avail,\n", "            0 as city_active_longtail_weight_avail,\n", "            0 as city_active_longtail_binary_avail,\n", "            0 as city_temp_inactive_express_weight_avail,\n", "            0 as city_temp_inactive_express_binary_avail,\n", "            0 as city_temp_inactive_longtail_weight_avail,\n", "            0 as city_temp_inactive_longtail_binary_avail,\n", "            0 as city_overall_overall_weight_avail,\n", "            0 as city_overall_overall_binary_avail,\n", "            \n", "            bea.be_active_express_weight_avail,\n", "            bea.be_active_express_binary_avail,\n", "            bea.be_active_longtail_weight_avail,\n", "            bea.be_active_longtail_binary_avail,\n", "            bea.be_temp_inactive_express_weight_avail,\n", "            bea.be_temp_inactive_express_binary_avail,\n", "            bea.be_temp_inactive_longtail_weight_avail,\n", "            bea.be_temp_inactive_longtail_binary_avail,\n", "            bea.be_overall_overall_weight_avail,\n", "            bea.be_overall_overall_binary_avail,\n", "            \n", "            fa.fe_active_express_weight_avail,\n", "            fa.fe_active_express_binary_avail,\n", "            fa.fe_active_longtail_weight_avail,\n", "            fa.fe_active_longtail_binary_avail,\n", "            fa.fe_temp_inactive_express_weight_avail,\n", "            fa.fe_temp_inactive_express_binary_avail,\n", "            fa.fe_temp_inactive_longtail_weight_avail,\n", "            fa.fe_temp_inactive_longtail_binary_avail,\n", "            fa.fe_overall_overall_weight_avail,\n", "            fa.fe_overall_overall_binary_avail,\n", "            \n", "            befea.be_fe_active_express_weight_avail,\n", "            befea.be_fe_active_express_binary_avail,\n", "            befea.be_fe_active_longtail_weight_avail,\n", "            befea.be_fe_active_longtail_binary_avail,\n", "            befea.be_fe_temp_inactive_express_weight_avail,\n", "            befea.be_fe_temp_inactive_express_binary_avail,\n", "            befea.be_fe_temp_inactive_longtail_weight_avail,\n", "            befea.be_fe_temp_inactive_longtail_binary_avail,\n", "            befea.be_fe_overall_overall_weight_avail,\n", "            befea.be_fe_overall_overall_binary_avail,\n", "            \n", "            befe.bexfe_active_express_weight_avail,\n", "            befe.bexfe_active_express_binary_avail,\n", "            befe.bexfe_active_longtail_weight_avail,\n", "            befe.bexfe_active_longtail_binary_avail,\n", "            befe.bexfe_temp_inactive_express_weight_avail,\n", "            befe.bexfe_temp_inactive_express_binary_avail,\n", "            befe.bexfe_temp_inactive_longtail_weight_avail,\n", "            befe.bexfe_temp_inactive_longtail_binary_avail,\n", "            befe.bexfe_overall_overall_weight_avail,\n", "            befe.bexfe_overall_overall_binary_avail,\n", "            \n", "            0 as current_inventory,\n", "            \n", "            log.total_dn_qty,\n", "            log.dn_short_qty,\n", "            log.dn_other_damage_qty,\n", "            log.transfer_loss_qty,\n", "            log.transfer_loss_amt,\n", "            log.total_dn_amt,\n", "            log.dn_short_amt,\n", "            log.dn_other_damage_amt,\n", "            log.dn_other_qty,\n", "            log.dn_other_amt,\n", "            log.dn_other_excess_item_qty,\n", "            log.dn_other_excess_item_amt,\n", "            log.dn_other_exp_nte_qty,\n", "            log.dn_other_exp_nte_amt,\n", "            log.dn_other_freebie_missing_qty,\n", "            log.dn_other_freebie_missing_amt,\n", "            log.dn_other_misc_qty,\n", "            log.dn_other_misc_amt,\n", "            log.dn_other_quality_issue_qty,\n", "            log.dn_other_quality_issue_amt,\n", "            log.dn_other_upc_not_scannable_qty,\n", "            log.dn_other_upc_not_scannable_amt,\n", "            log.dn_other_variant_mismatch_qty,\n", "            log.dn_other_variant_mismatch_amt,\n", "            log.dn_other_wrong_item_qty,\n", "            log.dn_other_wrong_item_amt,\n", "            log.lost_in_transit_positive_qty,\n", "            log.lost_in_transit_positive_amt,\n", "            log.lost_in_transit_negative_qty,\n", "            log.lost_in_transit_negative_amt,\n", "            log.b2b_amt,\n", "            rsto.rsto_dump_amt,\n", "            log.grn_amt,\n", "            log.billed_amt,\n", "            \n", "            fvcpu.indent as fvcpu_indent,\n", "            fvcpu.indent_wt as fvcpu_indent_wt,\n", "            fvcpu.total_con_cost as fvcpu_total_con_cost,\n", "            fvcpu.per_unit_total_con_cost as fvcpu_per_unit_total_con_cost,\n", "            \n", "            eloss.esto_excess_qty,\n", "            eloss.esto_excess_value,\n", "            \n", "            0 as sto_quantity,\n", "            0 as v1_line_items,\n", "            0 as v2_line_items,\n", "            \n", "            pqf.planned_quantity,              \n", "            pqfba.planned_quantity_be_avail,   \n", "            \n", "            \n", "            s.count_orders,   \n", "            s.gmv,\n", "            s.qty_sold,\n", "            ps.potential_order_quantity,                \n", "            \n", "            becu.picking_qty_util,\n", "            becu.picking_qty_cap,\n", "            becu.picking_qty_util_per,\n", "            becu.picking_sku_util,\n", "            becu.picking_sku_cap,\n", "            becu.picking_sku_util_per,\n", "            \n", "            fecu.inward_util,\n", "            fecu.inward_cap_in_qty,\n", "            fecu.inward_util_per,\n", "            fecu.truck_load_util,\n", "            fecu.truck_load_cap_in_kg,\n", "            fecu.truck_load_util_per,\n", "            \n", "            bescu.be_regular_storage_capacity,\n", "            bescu.be_regular_onshelf_inventory,\n", "            bescu.be_regular_in_transit_quantity,\n", "            bescu.be_regular_scaled_onshelf_inventory,\n", "            bescu.be_regular_onshelf_utilisation,\n", "            bescu.be_regular_scaled_open_po_qty,\n", "            bescu.be_regular_utilisation_with_open_stos,\n", "            bescu.be_cold_storage_capacity,\n", "            bescu.be_cold_onshelf_inventory,\n", "            bescu.be_cold_in_transit_quantity,\n", "            bescu.be_cold_scaled_onshelf_inventory,\n", "            bescu.be_cold_scaled_open_po_qty,\n", "            bescu.be_cold_onshelf_utilisation,\n", "            bescu.be_cold_utilisation_with_open_stos,\n", "            bescu.be_heavy_storage_capacity,\n", "            bescu.be_heavy_onshelf_inventory,\n", "            bescu.be_heavy_in_transit_quantity,\n", "            bescu.be_heavy_scaled_onshelf_inventory,\n", "            bescu.be_heavy_scaled_open_po_qty,\n", "            bescu.be_heavy_onshelf_utilisation,\n", "            bescu.be_heavy_utilisation_with_open_stos,\n", "            bescu.be_frozen_storage_capacity,\n", "            bescu.be_frozen_onshelf_inventory,\n", "            bescu.be_frozen_in_transit_quantity,\n", "            bescu.be_frozen_scaled_onshelf_inventory,\n", "            bescu.be_frozen_scaled_open_po_qty,\n", "            bescu.be_frozen_onshelf_utilisation,\n", "            bescu.be_frozen_utilisation_with_open_stos,\n", "            \n", "            fescu.be_regular_storage_capacity as fe_regular_storage_capacity,\n", "            fescu.be_regular_onshelf_inventory as fe_regular_onshelf_inventory,\n", "            fescu.be_regular_in_transit_quantity as fe_regular_in_transit_quantity,\n", "            fescu.be_regular_scaled_onshelf_inventory as fe_regular_scaled_onshelf_inventory,\n", "            fescu.be_regular_onshelf_utilisation as fe_regular_onshelf_utilisation,\n", "            fescu.be_regular_scaled_open_po_qty as fe_regular_scaled_open_po_qty,\n", "            fescu.be_regular_utilisation_with_open_stos as fe_regular_utilisation_with_open_stos,\n", "            fescu.be_cold_storage_capacity as fe_cold_storage_capacity,\n", "            fescu.be_cold_onshelf_inventory as fe_cold_onshelf_inventory,\n", "            fescu.be_cold_in_transit_quantity as fe_cold_in_transit_quantity,\n", "            fescu.be_cold_scaled_onshelf_inventory as fe_cold_scaled_onshelf_inventory,\n", "            fescu.be_cold_scaled_open_po_qty as fe_cold_scaled_open_po_qty,\n", "            fescu.be_cold_onshelf_utilisation as fe_cold_onshelf_utilisation,\n", "            fescu.be_cold_utilisation_with_open_stos as fe_cold_utilisation_with_open_stos,\n", "            fescu.be_heavy_storage_capacity as fe_heavy_storage_capacity,\n", "            fescu.be_heavy_onshelf_inventory as fe_heavy_onshelf_inventory,\n", "            fescu.be_heavy_in_transit_quantity as fe_heavy_in_transit_quantity,\n", "            fescu.be_heavy_scaled_onshelf_inventory as fe_heavy_scaled_onshelf_inventory,\n", "            fescu.be_heavy_scaled_open_po_qty as fe_heavy_scaled_open_po_qty,\n", "            fescu.be_heavy_onshelf_utilisation as fe_heavy_onshelf_utilisation,\n", "            fescu.be_heavy_utilisation_with_open_stos as fe_heavy_utilisation_with_open_stos,\n", "            fescu.be_frozen_storage_capacity as fe_frozen_storage_capacity,\n", "            fescu.be_frozen_onshelf_inventory as fe_frozen_onshelf_inventory,\n", "            fescu.be_frozen_in_transit_quantity as fe_frozen_in_transit_quantity,\n", "            fescu.be_frozen_scaled_onshelf_inventory as fe_frozen_scaled_onshelf_inventory,\n", "            fescu.be_frozen_scaled_open_po_qty as fe_frozen_scaled_open_po_qty,\n", "            fescu.be_frozen_onshelf_utilisation as fe_frozen_onshelf_utilisation,\n", "            fescu.be_frozen_utilisation_with_open_stos as fe_frozen_utilisation_with_open_stos,\n", "            \n", "            fr.billed_qty_fr,\n", "            fr.dispatch_qty_fr,\n", "            fr.grn_qty_fr,\n", "            fr.sto_raised_qty_fr,\n", "            fr.billed_fr,\n", "            fr.dispatch_fr,\n", "            fr.grn_fr,\n", "            \n", "            wotif.wotif_created_qty_FE,\n", "            wotif.wotif_on_time_picked_qty_FE,\n", "            wotif.wotif_on_time_packaged_qty_FE,\n", "            wotif.wotif_on_time_sorted_qty_FE,\n", "            wotif.wotif_on_time_dispatched_qty_FE,\n", "            wotif.wotif_created_qty_BE,\n", "            wotif.wotif_on_time_picked_qty_BE,\n", "            wotif.wotif_on_time_packaged_qty_BE,\n", "            wotif.wotif_on_time_sorted_qty_BE,\n", "            wotif.wotif_on_time_dispatched_qty_BE,\n", "\n", "            fotif.indent_raised_fotif,         \n", "            fotif.total_trips,\n", "            fotif.perfect_trips,\n", "            fotif.on_time_vehicle_report,\n", "            fotif.on_time_load_start,\n", "            fotif.on_time_dispatch,\n", "            fotif.on_time_ds_report,\n", "            fotif.on_time_ds_for_indent,\n", "            fotif.on_time_ds_for_indent_grn,\n", "            fotif.early_ds_report,\n", "            fotif.early_ds_report_for_indent,\n", "            log.sto_raised_qty,\n", "            log.billed_qty,\n", "            log.grn_qty,\n", "            log.on_time_grn_quantity,\n", "            log.delayed_grn_quantity,\n", "            log.delayed_grn_qty,\n", "            log.on_time_grn_qty,\n", "            \n", "            scf.count_active_skus_fe,\n", "            scf.count_temp_inactive_skus_fe,\n", "            bli.lines_active,\n", "            bli.lines_temp_inactive_skus,\n", "            scb.count_active_skus_be,\n", "            scb.count_inactive_skus_be,\n", "            \n", "            0 as avg_out_of_stock_duration,\n", "            0 as avg_out_of_stock_duration_weighted,\n", "            \n", "            ih_one.net_inv_present as net_inv_present_be,\n", "            ih_one.net_inv_exposure as net_inv_exposure_be,\n", "            ih_one.net_inv_volume as net_inv_volume_be,\n", "            ih_one.doi_sto_cpd_basis as doi_sto_cpd_basis_be,\n", "            ih_one.doi_po_cpd_basis as doi_po_cpd_basis_be,\n", "            ih_one.sto_cpd as sto_cpd_be,\n", "            ih_one.po_cpd as po_cpd_be,\n", "            ih_one.excess_inv as excess_inv_be,\n", "            ih_one.excess_inv_exposure as excess_inv_exposure_be,\n", "            ih_one.excess_inv_volume as excess_inv_volume_be,\n", "            ih_one.no_sale_inv as no_sale_inv_be,\n", "            ih_one.no_sale_inv_exposure as no_sale_inv_exposure_be,\n", "            ih_one.no_sale_inv_volume as no_sale_inv_volume_be,\n", "            ih_one.cpd_depleted_inv as cpd_depleted_inv_be,\n", "            ih_one.cpd_depleted_inv_exposure as cpd_depleted_inv_exposure_be,\n", "            ih_one.cpd_depleted_inv_volume as cpd_depleted_inv_volume_be,\n", "            ih_one.expiring_inv as expiring_inv_be,\n", "            ih_one.expiring_inv_exposure as expiring_inv_exposure_be,\n", "            ih_one.expiring_inv_volume as expiring_inv_volume_be,\n", "            ih_one.overall_dead as overall_dead_be,\n", "            ih_one.overall_dead_exposure as overall_dead_exposure_be,\n", "            ih_one.overall_dead_volume as overall_dead_volume_be,\n", "            \n", "            ih_two.net_inv_present as net_inv_present_fe,\n", "            ih_two.net_inv_exposure as net_inv_exposure_fe,\n", "            ih_two.net_inv_volume as net_inv_volume_fe,\n", "            ih_two.doi_sto_cpd_basis as doi_sto_cpd_basis_fe,\n", "            ih_two.doi_po_cpd_basis as doi_po_cpd_basis_fe,\n", "            ih_two.sto_cpd as sto_cpd_fe,\n", "            ih_two.po_cpd as po_cpd_fe,\n", "            ih_two.excess_inv as excess_inv_fe,\n", "            ih_two.excess_inv_exposure as excess_inv_exposure_fe,\n", "            ih_two.excess_inv_volume as excess_inv_volume_fe,\n", "            ih_two.no_sale_inv as no_sale_inv_fe,\n", "            ih_two.no_sale_inv_exposure as no_sale_inv_exposure_fe,\n", "            ih_two.no_sale_inv_volume as no_sale_inv_volume_fe,\n", "            ih_two.cpd_depleted_inv as cpd_depleted_inv_fe,\n", "            ih_two.cpd_depleted_inv_exposure as cpd_depleted_inv_exposure_fe,\n", "            ih_two.cpd_depleted_inv_volume as cpd_depleted_inv_volume_fe,\n", "            ih_two.expiring_inv as expiring_inv_fe,\n", "            ih_two.expiring_inv_exposure as expiring_inv_exposure_fe,\n", "            ih_two.expiring_inv_volume as expiring_inv_volume_fe,\n", "            ih_two.overall_dead as overall_dead_fe,\n", "            ih_two.overall_dead_exposure as overall_dead_exposure_fe,\n", "            ih_two.overall_dead_volume as overall_dead_volume_fe,\n", "            \n", "            bexfepa.be_availability as bexfe_be_availability_ptype,\n", "            bexfepa.fe_availability as bexfe_fe_availability_ptype,\n", "            \n", "            fepa.fe_availability as fe_availability_ptype\n", "            \n", "        from\n", "            outlet_details od\n", "        join tea_tagging tea on tea.be_facility_id=od.facility_id \n", "        left join ds_tagged ds on ds.facility_id=od.facility_id\n", "        left join gmv_loss_unavail_one gmv_one on gmv_one.facility_id=tea.fe_facility_id and cast(gmv_one.be_hot_outlet as int)=tea.be_hot_outlet_id\n", "        left join gmv_loss_unavail_two gmv_two on gmv_two.facility_id=tea.fe_facility_id and cast(gmv_two.be_hot_outlet as int)=tea.be_hot_outlet_id\n", "        left join be_avail bea on bea.be_facility_id=od.facility_id\n", "        left join fe_avail fa on fa.fe_inv_outlet_id=tea.fe_outlet_id\n", "        left join be_fe_avail befea on befea.be_facility_id=od.facility_id\n", "        left join bexfe_avail befe on befe.be_facility_id=od.facility_id\n", "        left join fleet_vcpu fvcpu on fvcpu.wh_outlet_id=od.inv_outlet_id and fvcpu.ds_outlet_id=tea.fe_outlet_id\n", "        left join esto_loss eloss on eloss.outlet_id=od.inv_outlet_id and eloss.receiver_outlet_id=tea.fe_outlet_id\n", "        left join sales s on s.outlet_id=tea.fe_outlet_id\n", "        left join potential_sales ps on ps.facility_id=tea.fe_facility_id\n", "        left join be_cap_util becu on becu.be_facility_id=od.facility_id\n", "        left join fe_cap_util fecu on fecu.be_facility_id=od.facility_id and fecu.fe_facility_id=tea.fe_facility_id\n", "        left join be_storage_cap_util bescu on bescu.facility_id=tea.be_facility_id \n", "        left join be_storage_cap_util fescu on fescu.facility_id=tea.fe_facility_id   \n", "        left join fill_rate fr on fr.be_outlet_id=od.hot_outlet_id and fr.fe_outlet_id=tea.fe_outlet_id       \n", "        left join warehouse_otif wotif on wotif.facility_id=od.facility_id\n", "        left join losses_and_grn log on log.sender_outlet_id=od.inv_outlet_id and log.receiver_outlet_id=tea.fe_outlet_id\n", "        left join rsto on rsto.sender_outlet_id=tea.fe_outlet_id and rsto.receiver_outlet_id=od.inv_outlet_id\n", "        left join fleet_otif fotif on fotif.facility_id=od.facility_id and fotif.ds_outlet_id=tea.fe_outlet_id\n", "        left join fe_weights fw on fw.outlet_id=tea.fe_outlet_id\n", "        left join be_weights bw on bw.be_facility_id=od.facility_id\n", "        left join sku_complexity_fe scf on scf.be_facility_id=od.facility_id and scf.fe_facility_id=tea.fe_facility_id\n", "        left join sku_complexity_be scb on scb.be_facility_id=od.facility_id\n", "        left join be_line_items bli on bli.be_facility_id=od.facility_id\n", "        left join planned_quantity_final pqf on pqf.outlet_id=tea.fe_outlet_id\n", "        left join planned_quantity_final_be_avail pqfba on pqfba.to_outlet_id=tea.fe_outlet_id\n", "        --left join oos_tat oost on oost.facility_id=tea.fe_facility_id\n", "        left join inventory_health ih_one on ih_one.facility_id=tea.be_facility_id\n", "        left join inventory_health ih_two on ih_two.facility_id=tea.fe_facility_id\n", "        left join dau_breakup db on db.be_facility_id=cast(tea.be_facility_id as varchar)\n", "        left join bexfe_ptype_avail bexfepa on bexfepa.be_facility_id=tea.be_facility_id\n", "        left join fe_ptype_avail fepa on fepa.be_facility_id=tea.be_facility_id and fepa.fe_facility_id=tea.fe_facility_id\n", ")\n", "select \n", "    *\n", "from final\n", "        \"\"\"\n", "\n", "        to_trino(\n", "            inventory_metrics_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs\n", "        )\n", "\n", "        print(f\"Backfilled data for: {backfill_date.strftime('%Y-%m-%d')}\")"]}, {"cell_type": "code", "execution_count": null, "id": "a3cbd3ef-0003-4642-9161-9d77e11094aa", "metadata": {}, "outputs": [], "source": ["if backfill_flag == 0:\n", "    normal()\n", "else:\n", "    backfill()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}