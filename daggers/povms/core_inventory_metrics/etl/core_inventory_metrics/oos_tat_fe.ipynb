{"cells": [{"cell_type": "code", "execution_count": null, "id": "3fa2bd7a-5cb7-43c2-86ae-b42875a15e30", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "7c0e6b71-eb07-44bd-be42-19a249968c55", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "fdcdd224-5c98-41cd-aee7-255e3bc25d48", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "9e6f9e4c-99a9-4e2a-9c79-850eb8decee5", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "e2f322aa-3eff-4676-9438-dd02bc42d92a", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility ID\"},\n", "    {\n", "        \"name\": \"avg_out_of_stock_duration\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Average Out of Stock Duration\",\n", "    },\n", "    {\n", "        \"name\": \"avg_out_of_stock_duration_weighted\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Weighted Out of Stock Duration\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "dd52062c-b99a-4613-888c-8e07334e7b7b", "metadata": {}, "outputs": [], "source": ["column_dtypes_two = [\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility ID\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"Item ID\"},\n", "    {\n", "        \"name\": \"current_inventory\",\n", "        \"type\": \"real\",\n", "        \"description\": \"Current Inventory\",\n", "    },\n", "    {\n", "        \"name\": \"updated_at_ist\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Updated at IST\",\n", "    },\n", "    {\n", "        \"name\": \"next_updated_at_ist\",\n", "        \"type\": \"timestamp\",\n", "        \"description\": \"Next Updated at IST\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "ca1bab06-18cc-4d0b-b32d-531189b28802", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inventory_core_metrics_oos_tat\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"facility_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"dt_hour\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains OOS replenishment TAT at facilities\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "67050f82-69a5-4b50-9e88-7dd9dd7f63a3", "metadata": {}, "outputs": [], "source": ["kwargstwo = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"oos_tat_icm\",\n", "    \"column_dtypes\": column_dtypes_two,\n", "    \"primary_key\": [\n", "        \"facility_id\",\n", "        \"item_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"facility_id\",\n", "    ],\n", "    # \"incremental_key\": \"dt_hour\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains OOS replenishment TAT at facilities\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "dbeac722-f05a-45fb-9049-4ecc62f99335", "metadata": {}, "outputs": [], "source": ["backfill_flag = 1\n", "\n", "if backfill_flag == 0:\n", "\n", "    def normal():\n", "        oos_tat_query_one = f\"\"\"\n", "with active_outlets as(\n", "    select outlet_id\n", "                from dwh.dim_outlet \n", "                where is_current\n", "                and live_date >= date '2005-01-01'\n", "                and valid_to_ts_ist >= CURRENT_DATE - INTERVAL '1' DAY\n", "                and live_date <= CURRENT_DATE - INTERVAL '1' DAY \n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        --and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Dark Store')))\n", "),\n", "\n", "item_details as (\n", "            select --current_date as updated_at, \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "            and l0_category <> 'Special'\n", "),\n", "\n", "previous_day_data AS (              \n", "    SELECT                                                                 \n", "        hi.facility_id,            \n", "        hi.item_id,\n", "        hi.current_inventory,\n", "        hi.updated_at_ist,\n", "        LEAD(hi.updated_at_ist) OVER (\n", "            PARTITION BY hi.facility_id, hi.item_id\n", "            ORDER BY hi.updated_at_ist \n", "        ) AS next_updated_at_ist\n", "    FROM \n", "        supply_etls.hourly_inventory_snapshots hi\n", "    JOIN item_details id on id.item_id=hi.item_id\n", "    JOIN outlet_details od on od.facility_id=hi.facility_id\n", "    JOIN active_outlets ao on ao.outlet_id=hi.outlet_id\n", "    WHERE hi.date_ist >= CURRENT_DATE - INTERVAL '30' DAY\n", ")\n", "\n", "select * from previous_day_data\n", "\n", "\n", "    \"\"\"\n", "        to_trino(\n", "            oos_tat_query_one, kwargstwo[\"schema_name\"] + \".\" + kwargstwo[\"table_name\"], **kwargstwo\n", "        )\n", "\n", "        oos_tat_query_two = f\"\"\"\n", "with city_hour_weights as (\n", "    select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.*********** as ch_weights\n", "    from supply_etls.city_hour_weights\n", "    group by 1,2\n", "),\n", "\n", "city_item_weights as (\n", "    select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.*********** as ci_weights\n", "    from supply_etls.city_item_weights\n", "    group by 1,2,3\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        --and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Dark Store')))\n", "),\n", "\n", "out_of_stock_periods AS (\n", "    SELECT \n", "        pd.facility_id,\n", "        pd.item_id,\n", "        updated_at_ist AS out_of_stock_start,\n", "        next_updated_at_ist AS out_of_stock_end,\n", "        (DATE_DIFF('minute', updated_at_ist, next_updated_at_ist) * 1.000000) as duration_minutes\n", "    FROM \n", "         interim.oos_tat_icm pd\n", "    JOIN outlet_details od on od.facility_id=pd.facility_id\n", "    WHERE \n", "        current_inventory = 0\n", "        AND next_updated_at_ist IS NOT NULL\n", "        AND pd.facility_id>0\n", "),\n", "\n", "fmt as(\n", "   select pfma.city_id, pfma.facility_id, pfma.item_id, pfma.assortment_type, pfma.master_assortment_substate_id,\n", "            ci_weights\n", "            from rpc.product_facility_master_assortment pfma\n", "            left join outlet_details o on pfma.city_id=o.city_id\n", "            left join city_item_weights ci on o.city_name = ci.city and pfma.item_id = ci.item_id    \n", "            where lake_active_record and active=1 and master_assortment_substate_id in (1,3)\n", "),\n", "\n", "-- Calculate LAG separately\n", "lagged_periods AS (\n", "    SELECT\n", "        facility_id,\n", "        item_id,\n", "        out_of_stock_start,\n", "        out_of_stock_end,\n", "        duration_minutes,\n", "        LAG(out_of_stock_end) OVER (\n", "            PARTITION BY facility_id, item_id ORDER BY out_of_stock_start\n", "        ) AS previous_out_of_stock_end\n", "    FROM\n", "        out_of_stock_periods\n", "),\n", "\n", "-- Assign group_id based on consecutive periods\n", "grouped_periods AS (\n", "    SELECT\n", "        facility_id,\n", "        item_id,\n", "        out_of_stock_start,\n", "        out_of_stock_end,\n", "        duration_minutes,\n", "        CASE \n", "            WHEN previous_out_of_stock_end = out_of_stock_start THEN 0 ELSE 1 \n", "        END AS is_new_group\n", "    FROM\n", "        lagged_periods\n", "),\n", "\n", "-- Calculate cumulative group_id\n", "cumulative_groups AS (\n", "    SELECT\n", "        facility_id,\n", "        item_id,\n", "        out_of_stock_start,\n", "        out_of_stock_end,\n", "        duration_minutes,\n", "        SUM(is_new_group) OVER (\n", "            PARTITION BY facility_id, item_id ORDER BY out_of_stock_start\n", "        ) AS group_id\n", "    FROM\n", "        grouped_periods\n", "),\n", "\n", "-- Aggregate intervals within groups\n", "aggregated_intervals AS (\n", "    SELECT\n", "        cg.facility_id,\n", "        cg.item_id,\n", "        cg.group_id,\n", "        MIN(out_of_stock_start) AS group_start,\n", "        MAX(out_of_stock_end) AS group_end,\n", "        SUM(duration_minutes) AS total_duration_minutes,\n", "        SUM(duration_minutes) AS total_duration_minutes_weighted\n", "    FROM\n", "        cumulative_groups cg\n", "    GROUP BY\n", "        1,2,3\n", "),\n", "\n", "-- Calculate the average out-of-stock duration per item_id\n", "average_out_of_stock AS (\n", "    SELECT  ag.facility_id,\n", "        ag.item_id,\n", "        fmt.ci_weights,\n", "        AVG(total_duration_minutes) AS avg_out_of_stock_duration,\n", "        AVG(total_duration_minutes_weighted) AS avg_out_of_stock_duration_weighted\n", "    FROM\n", "        aggregated_intervals ag\n", "      inner join fmt on fmt.facility_id=ag.facility_id and fmt.item_id=ag.item_id\n", "    GROUP BY\n", "        1,2,3\n", "),\n", "\n", "final AS(\n", "    SELECT current_date-INTERVAL '1' DAY as date_,\n", "        facility_id,\n", "        AVG(avg_out_of_stock_duration) AS avg_out_of_stock_duration,\n", "        SUM(avg_out_of_stock_duration_weighted*ci_weights*1.00)/nullif(sum(ci_weights),0) AS avg_out_of_stock_duration_weighted\n", "    FROM average_out_of_stock\n", "    GROUP BY\n", "    1,2\n", ")\n", "\n", "select * from final\n", "    \"\"\"\n", "        to_trino(oos_tat_query_two, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "90f2efdb-afa3-4c0e-87ad-d015412def94", "metadata": {}, "outputs": [], "source": ["if backfill_flag == 1:\n", "    today = datetime.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=30)\n", "\n", "    for i in range(30):\n", "        backfill_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        print(f\"Backfilling data for 1st section: {backfill_date.strftime('%Y-%m-%d')}\")\n", "        formatted_date = backfill_date.strftime(\"%Y-%m-%d\")\n", "        oos_tat_query = f\"\"\"\n", "        with active_outlets as(\n", "            select outlet_id\n", "                        from dwh.dim_outlet \n", "                        where is_current\n", "                        and live_date BETWEEN date '2005-01-01' AND date('{formatted_date}')\n", "                        and valid_to_ts_ist >= date('{formatted_date}')\n", "                        and live_date <= date('{formatted_date}') - INTERVAL '1' DAY\n", "        ),\n", "\n", "        outlet_details as (\n", "            select \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings\n", "            from supply_etls.outlet_details\n", "            where (ars_check=1 \n", "                and (grocery_active_count >= 1)\n", "                and (store_type in ('Dark Store')))\n", "        ),\n", "\n", "        item_details as (\n", "                    select \n", "                        item_id, item_name, l0_id, l0_category, \n", "                                l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "                    from supply_etls.item_details\n", "                    where assortment_type='Packaged Goods'\n", "                    and handling_type = 'Non Packaging Material'\n", "                    and l0_category <> 'Special'\n", "        ),\n", "\n", "        previous_day_data AS (              \n", "            SELECT                                                                 \n", "                hi.facility_id,            \n", "                hi.item_id,\n", "                hi.current_inventory,\n", "                hi.updated_at_ist,\n", "                LEAD(hi.updated_at_ist) OVER (\n", "                    PARTITION BY hi.facility_id, hi.item_id\n", "                    ORDER BY hi.updated_at_ist \n", "                ) AS next_updated_at_ist\n", "            FROM \n", "                supply_etls.hourly_inventory_snapshots hi\n", "            JOIN item_details id on id.item_id=hi.item_id\n", "            JOIN outlet_details od on od.facility_id=hi.facility_id\n", "            JOIN active_outlets ao on ao.outlet_id=hi.outlet_id\n", "            WHERE hi.date_ist BETWEEN date('{formatted_date}') - INTERVAL '30' DAY AND date('{formatted_date}')\n", "        )\n", "\n", "        select * from previous_day_data\n", "        \"\"\"\n", "\n", "        to_trino(\n", "            oos_tat_query, kwargstwo[\"schema_name\"] + \".\" + kwargstwo[\"table_name\"], **kwargstwo\n", "        )\n", "        print(f\"Data inserted for 1st section : {backfill_date.strftime('%Y-%m-%d')}\")\n", "\n", "        print(f\"Backfilling data for 2nd section: {backfill_date.strftime('%Y-%m-%d')}\")\n", "        oos_tat_query_two = f\"\"\"\n", "        With city_hour_weights as (\n", "            select city, order_hour, cast(max_by(weights,updated_at) as real) * 1.*********** as ch_weights\n", "            from supply_etls.city_hour_weights\n", "            where date(updated_at) <= date('{formatted_date}') - INTERVAL '1' DAY\n", "            group by 1,2\n", "        ),\n", "\n", "        city_item_weights as (\n", "            select city, assortment_type, item_id, cast(max_by(weights,updated_at) as real) * 1.*********** as ci_weights\n", "            from supply_etls.city_item_weights\n", "            where date(updated_at) <= date('{formatted_date}') - INTERVAL '1' DAY\n", "            group by 1,2,3\n", "        ),\n", "\n", "        outlet_details as (\n", "            select \n", "                city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "                inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id, taggings\n", "            from supply_etls.outlet_details\n", "            where (ars_check=1 \n", "                and (grocery_active_count >= 1) \n", "                and (store_type in ('Dark Store')))\n", "        ),\n", "\n", "        out_of_stock_periods AS (\n", "            SELECT \n", "                pd.facility_id,\n", "                pd.item_id,\n", "                updated_at_ist AS out_of_stock_start,\n", "                next_updated_at_ist AS out_of_stock_end,\n", "                (DATE_DIFF('minute', updated_at_ist, next_updated_at_ist) * 1.000000) as duration_minutes\n", "            FROM \n", "                 interim.oos_tat_icm pd\n", "            JOIN outlet_details od on od.facility_id=pd.facility_id\n", "            WHERE \n", "                current_inventory = 0\n", "                AND next_updated_at_ist IS NOT NULL\n", "                AND pd.facility_id>0\n", "        ),\n", "\n", "        fmt as(\n", "            select pfma.city_id, pfma.facility_id, pfma.item_id, pfma.assortment_type, pfma.master_assortment_substate_id,\n", "                    ci_weights\n", "            from rpc.product_facility_master_assortment pfma\n", "            left join outlet_details o on pfma.city_id=o.city_id\n", "            left join city_item_weights ci on o.city_name = ci.city and pfma.item_id = ci.item_id    \n", "            where lake_active_record and active=1 and master_assortment_substate_id in (1,3)\n", "        ),\n", "\n", "        lagged_periods AS (\n", "            SELECT\n", "                facility_id,\n", "                item_id,\n", "                out_of_stock_start,\n", "                out_of_stock_end,\n", "                duration_minutes,\n", "                LAG(out_of_stock_end) OVER (\n", "                    PARTITION BY facility_id, item_id ORDER BY out_of_stock_start\n", "                ) AS previous_out_of_stock_end\n", "            FROM\n", "                out_of_stock_periods\n", "        ),\n", "\n", "        grouped_periods AS (\n", "            SELECT\n", "                facility_id,\n", "                item_id,\n", "                out_of_stock_start,\n", "                out_of_stock_end,\n", "                duration_minutes,\n", "                CASE \n", "                    WHEN previous_out_of_stock_end = out_of_stock_start THEN 0 ELSE 1 \n", "                END AS is_new_group\n", "            FROM\n", "                lagged_periods\n", "        ),\n", "\n", "        cumulative_groups AS (\n", "            SELECT\n", "                facility_id,\n", "                item_id,\n", "                out_of_stock_start,\n", "                out_of_stock_end,\n", "                duration_minutes,\n", "                SUM(is_new_group) OVER (\n", "                    PARTITION BY facility_id, item_id ORDER BY out_of_stock_start\n", "                ) AS group_id\n", "            FROM\n", "                grouped_periods\n", "        ),\n", "\n", "        aggregated_intervals AS (\n", "            SELECT\n", "                cg.facility_id,\n", "                cg.item_id,\n", "                cg.group_id,\n", "                MIN(out_of_stock_start) AS group_start,\n", "                MAX(out_of_stock_end) AS group_end,\n", "                SUM(duration_minutes) AS total_duration_minutes,\n", "                SUM(duration_minutes) AS total_duration_minutes_weighted\n", "            FROM\n", "                cumulative_groups cg\n", "            GROUP BY\n", "                1,2,3\n", "        ),\n", "\n", "        average_out_of_stock AS (\n", "            SELECT  ag.facility_id,\n", "                    ag.item_id,\n", "                    fmt.ci_weights,\n", "                    AVG(total_duration_minutes) AS avg_out_of_stock_duration,\n", "                    AVG(total_duration_minutes_weighted) AS avg_out_of_stock_duration_weighted\n", "            FROM\n", "                aggregated_intervals ag\n", "              inner join fmt on fmt.facility_id=ag.facility_id and fmt.item_id=ag.item_id\n", "            GROUP BY\n", "                1,2,3\n", "        ),\n", "\n", "        final AS(\n", "            SELECT date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "                    facility_id,\n", "                    AVG(avg_out_of_stock_duration) AS avg_out_of_stock_duration,\n", "                    SUM(avg_out_of_stock_duration_weighted*ci_weights*1.00)/nullif(sum(ci_weights),0) AS avg_out_of_stock_duration_weighted\n", "            FROM average_out_of_stock\n", "            GROUP BY\n", "            1,2\n", "        )\n", "\n", "        select * from final\n", "        \"\"\"\n", "\n", "        # Execute second query (processing data from the table)\n", "        to_trino(oos_tat_query_two, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "        print(f\"Data processed for 2nd section: {backfill_date.strftime('%Y-%m-%d')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}