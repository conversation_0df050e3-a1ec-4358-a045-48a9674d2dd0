alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: core_inventory_metrics
dag_type: etl
escalation_priority: low
execution_timeout: 4000
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- executor_config:
    load_type: tiny
    node_type: spot
  name: inventory_health
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: dau_coverage
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: bexfe_ptype_avail
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: fe_ptype_avail
  parameters: null
  tag: first
- executor_config:
    load_type: tiny
    node_type: spot
  name: bexfe
  parameters: null
  tag: second
- executor_config:
    load_type: tiny
    node_type: spot
  name: pan_india
  parameters: null
  tag: third
owner:
  email: <EMAIL>
  slack_id: U07R194002D
path: povms/core_inventory_metrics/etl/core_inventory_metrics
paused: false
pool: povms_pool
project_name: core_inventory_metrics
schedule:
  end_date: '2025-06-30T00:00:00'
  interval: 0 3 * * *
  start_date: '2025-05-20T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 60
