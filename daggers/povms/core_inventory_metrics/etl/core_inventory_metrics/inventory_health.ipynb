{"cells": [{"cell_type": "code", "execution_count": null, "id": "ca1a0214-1e97-48f6-a104-cd6b6b7e1c29", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "import json\n", "import shutil\n", "\n", "import boto3\n", "import io\n", "\n", "import uuid\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import gc\n", "import os\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "65f362e6-018a-4fbc-af45-a365dcb7b7de", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 200)\n", "pd.set_option(\"display.max_rows\", 200)"]}, {"cell_type": "code", "execution_count": null, "id": "6b5d5720-aa48-4902-b6c5-205dfe8b8f50", "metadata": {}, "outputs": [], "source": ["def format_time(duration):\n", "    hours = int(duration // 3600)\n", "    minutes = int((duration % 3600) // 60)\n", "    seconds = int(duration % 60)\n", "    return f\"{hours} hour {minutes} min {seconds} sec\"\n", "\n", "\n", "suffixes = [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\"]\n", "\n", "\n", "def humansize(nbytes):\n", "    i = 0\n", "    while nbytes >= 1024 and i < len(suffixes) - 1:\n", "        nbytes /= 1024.0\n", "        i += 1\n", "    f = (\"%.2f\" % nbytes).rstrip(\"0\").rstrip(\".\")\n", "    return \"%s %s\" % (f, suffixes[i])\n", "\n", "\n", "def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start_time = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end_time = time.time()\n", "            duration = end_time - start_time\n", "            formatted_duration = format_time(duration)\n", "\n", "            size = humansize(df.memory_usage(index=True, deep=True).sum())\n", "            print(f\"DataFrame size: , {size}, Time: {formatted_duration}\")\n", "\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "id": "8224971f-45cb-495e-ad4c-f5aef00476fc", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-festive-metrics\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "50276352-6fe5-4910-a3ed-b5a2bead090d", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"date_\", \"type\": \"date\", \"description\": \"Actual Date of Data\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"Facility ID\"},\n", "    {\"name\": \"outlet_id\", \"type\": \"integer\", \"description\": \"Outlet ID\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"Facility Name\"},\n", "    {\"name\": \"net_inv_present\", \"type\": \"integer\", \"description\": \"Net Inventory\"},\n", "    {\"name\": \"net_inv_exposure\", \"type\": \"integer\", \"description\": \"Inventory Value\"},\n", "    {\"name\": \"net_inv_volume\", \"type\": \"integer\", \"description\": \"Inventory Volume\"},\n", "    {\"name\": \"doi_sto_cpd_basis\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"doi_po_cpd_basis\", \"type\": \"real\", \"description\": \"Days of inventory\"},\n", "    {\"name\": \"sto_cpd\", \"type\": \"real\", \"description\": \"STO Consumption per day\"},\n", "    {\"name\": \"po_cpd\", \"type\": \"real\", \"description\": \"PO Consumption per day\"},\n", "    {\"name\": \"excess_inv\", \"type\": \"integer\", \"description\": \"Excess inventory\"},\n", "    {\n", "        \"name\": \"excess_inv_exposure\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Excess inventory Exposure\",\n", "    },\n", "    {\"name\": \"excess_inv_volume\", \"type\": \"integer\", \"description\": \"Excess inventory Volume\"},\n", "    {\"name\": \"no_sale_inv\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_exposure\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\"name\": \"no_sale_inv_volume\", \"type\": \"integer\", \"description\": \"No sale Inventory\"},\n", "    {\n", "        \"name\": \"cpd_depleted_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_exposure\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"cpd_depleted_inv_volume\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"CPD eroded inventory\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_exposure\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\n", "        \"name\": \"expiring_inv_volume\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"Expiring quantity at FE\",\n", "    },\n", "    {\"name\": \"overall_dead\", \"type\": \"integer\", \"description\": \"overall dead Inventory\"},\n", "    {\"name\": \"overall_dead_exposure\", \"type\": \"integer\", \"description\": \"dead Inventory\"},\n", "    {\"name\": \"overall_dead_volume\", \"type\": \"integer\", \"description\": \"dead Inventory\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "341cb546-d9f1-464b-850f-842744102019", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"inventory_health\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\n", "        \"date_\",\n", "        \"facility_id\",\n", "    ],\n", "    \"partition_key\": [\n", "        \"date_\",\n", "    ],\n", "    # \"incremental_key\": \"dt_hour\",\n", "    # \"force_upsert_without_increment_check\": False,\n", "    \"load_type\": \"partition_overwrite\",  # append, truncate or upsert,\n", "    \"table_description\": \"this table contains Inventory Health for Inventory Core Metrics\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "102d4e86-b167-400b-8fbd-0efc3d23a391", "metadata": {}, "outputs": [], "source": ["backfill_flag = 0\n", "\n", "\n", "def normal():\n", "    inventory_health_query = f\"\"\"\n", "WITH tea_tagging_item as (\n", "    select --current_date as updated_at, \n", "        fe_outlet_id, fe_facility_id,fe_facility_name, item_id, assortment_type, store_assortment_type,\n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id,be_facility_name,fe_city_id,fe_city_name, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "item_details as (\n", "            select --current_date as updated_at, \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "            and l0_category<>'Specials'\n", "),\n", "\n", "sto_transfer_cpd_fe as (\n", "            select for_date,\n", "                    outlet_id,\n", "                    cp.item_id,\n", "                    cpd\n", "            from ars.outlet_item_aps_derived_cpd cp\n", "            join item_details id on id.item_id=cp.item_id\n", "            join tea_tagging_item tea on tea.item_id=cp.item_id and tea.fe_outlet_id=cp.outlet_id   \n", "            where insert_ds_ist = cast(CURRENT_DATE - INTERVAL '1' DAY as varchar)          \n", "            and lake_active_record            \n", "            and for_date = CURRENT_DATE - INTERVAL '1' DAY\n", "            group by 1,2,3,4\n", "),\n", "\n", "cpd_final_fe as(\n", "    select for_date,\n", "            outlet_id,\n", "            sum(cpd) as cpd\n", "            from sto_transfer_cpd_fe\n", "            group by 1,2\n", "),\n", "\n", "sto_transfer_cpd_be_item as(         \n", "select  for_date, be_facility_id, tea.item_id, sum(cpd) as be_cpd\n", "    from tea_tagging_item tea \n", "    join sto_transfer_cpd_fe cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "    group by 1,2,3\n", "),\n", "\n", "cpd_final_be as (             \n", "    select  for_date, be_facility_id, sum(be_cpd) as be_cpd\n", "    from sto_transfer_cpd_be_item\n", "    group by 1,2\n", "),\n", "\n", "outlet_details_be as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Packaged Goods')))\n", "        or facility_id in (2960,3127,3213)\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where ars_check=1 \n", "        and taggings = 'fe'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ( 'Dark Store'))\n", "),\n", "    \n", "    inventory as(\n", "    select i.item_id,\n", "            outlet_id,\n", "            sum(current_inventory) as net_inv\n", "            from supply_etls.hourly_inventory_snapshots i\n", "            join item_details id on id.item_id=i.item_id\n", "            where date_ist = CURRENT_DATE - INTERVAL '1' DAY\n", "            and hour(updated_at_ist)=(select max(hour(updated_at_ist)) from supply_etls.hourly_inventory_snapshots where date_ist = CURRENT_DATE - INTERVAL '1' DAY)\n", "             and substate_id IN (1,3)\n", "            group by 1,2\n", "    ),\n", "    \n", "    inventory_be as(\n", "    select DISTINCT backend_outlet_id, i.item_id,\n", "            sum(current_inventory) as net_inv\n", "            from supply_etls.hourly_inventory_snapshots i\n", "            join item_details id on id.item_id=i.item_id\n", "            where date_ist = CURRENT_DATE - INTERVAL '1' DAY\n", "            and hour(updated_at_ist)=(select max(hour(updated_at_ist)) from supply_etls.hourly_inventory_snapshots where date_ist = CURRENT_DATE - INTERVAL '1' DAY)\n", "             and substate_id IN (1,3)\n", "            group by 1,2\n", "    ),\n", "     \n", "    po_ordering_cpd as (\n", "            select cp.item_id, outlet_id, cpd\n", "            from ars.default_cpd cp\n", "            join item_details id on id.item_id=cp.item_id\n", "            join tea_tagging_item tea on tea.item_id=cp.item_id and tea.fe_outlet_id=cp.outlet_id   \n", "            where active and lake_active_record\n", "    ),\n", "    \n", "    po_ordering_cpd_agg as (\n", "            select outlet_id, sum(cpd) as cpd\n", "            from po_ordering_cpd\n", "            group by 1\n", "    ),\n", "    \n", "    po_ordering_cpd_be_item as(         \n", "        select  be_facility_id, tea.item_id, sum(cpd) as be_cpd\n", "            from tea_tagging_item tea \n", "            join po_ordering_cpd cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "            group by 1,2\n", "    ),\n", "    \n", "    po_cpd_final_be as (             \n", "    select be_facility_id, sum(be_cpd) as be_cpd\n", "    from po_ordering_cpd_be_item\n", "    group by 1\n", "    ),\n", "\n", "\n", " landing_price as(\n", "        WITH invoice_details AS (\n", "    SELECT\n", "        outlet_id, grofers_order_id, created_at, merchant_id, invoice_id, id\n", "    FROM pos.pos_invoice\n", "    WHERE lake_active_record\n", "        AND insert_ds_ist >= CAST(current_date - INTERVAL '100' day AS VARCHAR)              \n", "        AND invoice_type_id IN (5,14,16)\n", "        AND grofers_order_id IS NOT NULL AND grofers_order_id != ''\n", "    ),\n", "\n", "    pipd AS (\n", "        SELECT \n", "            variant_id, quantity, invoice_id, pos_timestamp\n", "        FROM pos.pos_invoice_product_details\n", "        WHERE lake_active_record\n", "            AND insert_ds_ist >= CAST(current_date - INTERVAL '100' day AS VARCHAR)              \n", "    ),\n", "    \n", "    pipl AS (\n", "        SELECT landing_price, variant_id, invoice_id\n", "        FROM pos.pos_invoice_product_lp\n", "        WHERE lake_active_record\n", "    ),\n", "    \n", "    variant AS (\n", "        SELECT item_id, variant_id \n", "        FROM rpc.product_product \n", "        WHERE lake_active_record = TRUE\n", "        GROUP BY 1,2\n", "    ),\n", "    \n", "    invoice_aggregated AS (\n", "        SELECT \n", "            id.outlet_id, \n", "            v.item_id,\n", "            SUM(pipl.landing_price * pipd.quantity) * 1.00 / NULLIF(SUM(pipd.quantity), 0) AS avg_landing_price\n", "        FROM invoice_details id\n", "        LEFT JOIN pipd ON pipd.invoice_id = id.id\n", "        LEFT JOIN pipl ON pipl.invoice_id = id.id AND pipl.variant_id = pipd.variant_id\n", "        LEFT JOIN variant v ON pipl.variant_id = v.variant_id\n", "        GROUP BY 1,2\n", "    )\n", "    \n", "    SELECT       \n", "        item_id,\n", "        AVG(avg_landing_price) AS landing_price\n", "    FROM invoice_aggregated\n", "    group by 1\n", " ),\n", "\n", "qmax as (                               \n", "    select  item_id, outlet_id, max(qmax_final) qmax_final\n", "    from supply_etls.ars_qmax\n", "    where date(updated_at)>=current_date-interval'1'day             \n", "    group by 1,2\n", "),\n", "\n", "festive_details as (\n", "        select\n", "        f.be_facility_id,\n", "        tea.fe_outlet_id,\n", "        f.item_id,\n", "        id.item_name,\n", "        event_id,\n", "        festival_name as festival,\n", "        assortment_reason_type as festival_bau,    \n", "        sum(planned_quantity) as planned_qty\n", "        from ars_etls.final_festival_planning f\n", "        join item_details id on id.item_id=f.item_id\n", "        join tea_tagging_item tea on tea.item_id= f.item_id and tea.be_facility_id=f.be_facility_id\n", "        where festival_name is not null\n", "        and sale_end_date BETWEEN CURRENT_DATE - INTERVAL '45' DAY AND CURRENT_DATE-INTERVAL '15' DAY\n", "        and tea.fe_outlet_id IS NOT NULL               \n", "        group by 1,2,3,4,5,6,7\n", "),\n", "\n", "cpd_depletion as(\n", "    WITH peak_cpd AS (\n", "    SELECT \n", "        cp.outlet_id,\n", "        cp.item_id,\n", "        fd.item_name,\n", "        fd.event_id,\n", "        fd.festival,\n", "        MAX(aps_adjusted) AS peak_cpd                   \n", "    FROM ars.outlet_item_aps_derived_cpd cp\n", "    JOIN festive_details fd ON fd.item_id = cp.item_id and fd.fe_outlet_id=cp.outlet_id\n", "    WHERE \n", "        cp.insert_ds_ist BETWEEN CAST(CURRENT_DATE - INTERVAL '90' DAY AS VARCHAR) AND CAST(CURRENT_DATE - INTERVAL '15' DAY AS VARCHAR)\n", "        AND cp.lake_active_record\n", "        AND cp.for_date BETWEEN CURRENT_DATE - INTERVAL '90' DAY AND CURRENT_DATE - INTERVAL '15' DAY\n", "    GROUP BY \n", "        1,2,3,4,5\n", "    )\n", "    SELECT\n", "        cp.outlet_id,\n", "        cp.item_id,\n", "        1 as flag\n", "    FROM ars.outlet_item_aps_derived_cpd cp\n", "    JOIN peak_cpd p ON p.item_id = cp.item_id and p.outlet_id =cp.outlet_id\n", "    WHERE cp.insert_ds_ist = CAST(CURRENT_DATE - INTERVAL '1' DAY AS VARCHAR)\n", "        AND cp.lake_active_record\n", "        AND cp.for_date =CURRENT_DATE-INTERVAL '1' DAY  \n", "        AND cp.aps_adjusted <= 0.2 * p.peak_cpd \n", "        AND cp.aps_adjusted < 1\n", "),\n", "\n", "cpd_depletion_be as(\n", "    select DISTINCT tti.item_id, be_facility_id\n", "    from tea_tagging_item tti \n", "    join cpd_depletion cd on cd.outlet_id=tti.fe_outlet_id and cd.item_id=tti.item_id\n", "    group by 1,2\n", "    having max(flag)=1 \n", "),\n", "\n", "item_product_mapping as (\n", "    select item_id, product_id, multiplier\n", "    from dwh.dim_item_product_offer_mapping\n", "    where is_current\n", "),\n", "\n", "sales as (\n", "        select DISTINCT fs.outlet_id, ip.item_id, procured_quantity   \n", "        from dwh.fact_sales_order_item_details fs\n", "        join item_product_mapping ip on ip.product_id = fs.product_id\n", "        where order_create_dt_ist BETWEEN CURRENT_DATE-INTERVAL '60' DAY AND CURRENT_DATE-INTERVAL '1' DAY\n", "        AND is_internal_order = false AND order_current_status = 'DELIVERED'\n", "        group by 1,2,3\n", "    ),\n", "    \n", "-- be_leftover as (            --items not existing in tea_tagging          \n", "-- select distinct i.item_id as item_id, net_inv, od.facility_id, od.facility_name\n", "--     from inventory i\n", "--     join outlet_details_be od on od.inv_outlet_id = i.outlet_id \n", "--     join item_details id on id.item_id=i.item_id\n", "--     where (i.item_id,i.outlet_id) NOT IN (select item_id,be_inv_outlet_id from tea_tagging_item)\n", "--     group by 1,2,3,4\n", "-- ),\n", "\n", "final_inv_fe_sale as(\n", "    select od.facility_id, od.facility_name, i.item_id, net_inv,\n", "            CASE WHEN s.item_id>0 THEN 1 ELSE 0 END as sale_flag\n", "    from inventory i\n", "    join item_details id on id.item_id=i.item_id\n", "    join tea_tagging_item tea on tea.item_id=i.item_id and tea.fe_outlet_id=i.outlet_id\n", "    join outlet_details od on od.hot_outlet_id=i.outlet_id\n", "    left join sales s on i.item_id=s.item_id and i.outlet_id=s.outlet_id\n", "    group by 1,2,3,4,5\n", "), \n", "\n", "final_inv_be_sale as(\n", "    select od.facility_id as be_facility_id, od.facility_name as be_facility_name, i.item_id, i.net_inv\n", "    from inventory_be i\n", "    join item_details id on id.item_id=i.item_id\n", "    join outlet_details_be od on od.hot_outlet_id=i.backend_outlet_id\n", "    join tea_tagging_item tti on tti.be_facility_id = od.facility_id and tti.item_id = id.item_id\n", "    left join final_inv_fe_sale ifs on ifs.facility_id=tti.fe_facility_id and ifs.item_id=i.item_id\n", "    group by 1,2,3,4\n", "    having max(sale_flag)=0\n", "),\n", "\n", "final_inv_fe as(\n", "    select DISTINCT item_id, net_inv, facility_id, facility_name\n", "    from final_inv_fe_sale fi\n", "    group by 1,2,3,4\n", "    having max(sale_flag) = 0\n", "),\n", "\n", "ars AS ( \n", "    SELECT \n", "        insert_ds_ist, \n", "        facility_id, \n", "        id.item_id,\n", "        CASE \n", "            WHEN available_hours > 0 THEN 1  \n", "            ELSE 0 \n", "        END AS avail_flag\n", "    FROM ars.daily_orders_and_availability ars\n", "    JOIN item_details id ON id.item_id = ars.item_id\n", "    JOIN tea_tagging_item tea ON tea.item_id = ars.item_id AND tea.fe_facility_id = ars.facility_id\n", "    WHERE order_date BETWEEN CURRENT_DATE - INTERVAL '45' DAY AND CURRENT_DATE - INTERVAL '1' DAY        \n", "        AND CAST(insert_ds_ist AS DATE) BETWEEN CURRENT_DATE - INTERVAL '45' DAY AND CURRENT_DATE - INTERVAL '1' DAY\n", "),\n", "\n", "ars_be AS ( \n", "    SELECT \n", "        insert_ds_ist, \n", "        be_facility_id,\n", "        facility_id, \n", "        id.item_id,\n", "        CASE \n", "            WHEN available_hours > 0 THEN 1  \n", "            ELSE 0 \n", "        END AS avail_flag\n", "    FROM ars.daily_orders_and_availability ars\n", "    JOIN item_details id ON id.item_id = ars.item_id\n", "    JOIN tea_tagging_item tea ON tea.item_id = ars.item_id AND tea.fe_facility_id = ars.facility_id\n", "    WHERE order_date BETWEEN CURRENT_DATE - INTERVAL '45' DAY AND CURRENT_DATE - INTERVAL '1' DAY        \n", "        AND CAST(insert_ds_ist AS DATE) BETWEEN CURRENT_DATE - INTERVAL '45' DAY AND CURRENT_DATE - INTERVAL '1' DAY\n", "),\n", "\n", "ars_be_two AS ( \n", "    SELECT \n", "        insert_ds_ist, \n", "        be_facility_id,\n", "        item_id,\n", "        max(avail_flag) AS avail_flag\n", "    FROM ars_be\n", "    group by 1,2,3\n", "),\n", "\n", "ars_available_days AS (\n", "    SELECT \n", "        facility_id,\n", "        item_id,\n", "        COUNT(DISTINCT CAST(insert_ds_ist AS DATE)) AS available_days_count\n", "    FROM ars\n", "    WHERE avail_flag = 1 \n", "    GROUP BY 1,2\n", "),\n", "\n", "ars_available_days_be AS (\n", "    SELECT \n", "        be_facility_id,\n", "        item_id,\n", "        COUNT(DISTINCT CAST(insert_ds_ist AS DATE)) AS available_days_count\n", "    FROM ars_be_two\n", "    where avail_flag = 1    \n", "    GROUP BY 1,2\n", "),\n", "\n", "ars_final_fe AS (\n", "    SELECT DISTINCT\n", "        facility_id, \n", "        item_id\n", "    FROM ars_available_days\n", "    WHERE available_days_count >= 30  \n", "),\n", "\n", "ars_final_be AS (\n", "    SELECT DISTINCT\n", "        be_facility_id, \n", "        item_id\n", "    FROM ars_available_days_be\n", "    WHERE available_days_count >= 30  \n", "),\n", "\n", "fe_final_no_sale as (\n", "    select f.facility_id , f.facility_name,f.item_id, net_inv\n", "        from final_inv_fe f\n", "        join ars_final_fe a on a.facility_id=f.facility_id and a.item_id=f.item_id\n", "),\n", "\n", "be_final_no_sale as(\n", "    select f.be_facility_id,\n", "            f.be_facility_name,\n", "            f.item_id,\n", "            net_inv\n", "        from final_inv_be_sale f\n", "        join ars_final_be a on a.be_facility_id=f.be_facility_id and a.item_id=f.item_id\n", "),\n", "\n", "shelf_life as(\n", "    select DISTINCT item_id, shelf_life\n", "    from rpc.product_product\n", "    where active=1 and lake_active_record\n", "),\n", "\n", "expiring_fe as(\n", "    select cast(s.outlet_id as int) as outlet_id,\n", "            cast(s.item_id as int) as item_id, s.quantity,\n", "            EXTRACT(DAY FROM (cast(batch_expiry as date) - CURRENT_DATE)) * 1.0000 / nullif(shelf_life,0) as per_left\n", "    from dynamodb.blinkit_store_inventory_service_li_snapshot s\n", "    left join shelf_life sl on s.item_id=cast(sl.item_id as varchar)\n", "    where state='GOOD' \n", "    and quantity>0\n", "),\n", "\n", "expiring_fe_final as(                                        \n", "    select outlet_id, item_id, quantity\n", "    from expiring_fe\n", "    where per_left<=0.3\n", "),\n", "\n", "variant AS (\n", "        SELECT item_id, variant_id \n", "        FROM rpc.product_product \n", "        WHERE lake_active_record = TRUE\n", "        GROUP BY 1,2\n", "),\n", "    \n", "expiring_be as(\n", "    select outlet_id, v.item_id, delta as quantity, \n", "            EXTRACT(DAY FROM (cast(expiry_date as date) - CURRENT_DATE)) * 1.0000 / nullif(shelf_life,0) as per_left\n", "    from ims.ims_inventory_stock_details isd\n", "    join variant v on v.variant_id=isd.variant_id\n", "    left join shelf_life sl on v.item_id=sl.item_id\n", "    join outlet_details_be od on od.inv_outlet_id=isd.outlet_id\n", "    where lake_active_record = TRUE and insert_ds_ist=cast(CURRENT_DATE as varchar)\n", "),\n", "\n", "expiring_be_final as(\n", "    select outlet_id, item_id, quantity\n", "    from expiring_be\n", "    where per_left<=0.3\n", "),\n", "\n", "fe_aggregated as(                                       \n", "select od.facility_id,\n", "        i.outlet_id,\n", "        od.facility_name,\n", "        i.item_id,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS net_inv_present ,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*landing_price ELSE 0 END) AS net_inv_exposure,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*item_factor ELSE 0 END) AS net_inv_volume,\n", "        (CASE WHEN i.item_id IS NOT NULL and i.net_inv> (qmax_final+(3*coalesce(cpd,0))) THEN i.net_inv-(qmax_final+(3*coalesce(cpd,0))) ELSE 0 END) as excess_inv,\n", "        (CASE WHEN i.item_id IS NOT NULL and i.net_inv> (qmax_final+(3*coalesce(cpd,0))) THEN (i.net_inv-(qmax_final+(3*coalesce(cpd,0))))*landing_price ELSE 0 END) as excess_inv_exposure,\n", "        (CASE WHEN i.item_id IS NOT NULL and i.net_inv> (qmax_final+(3*coalesce(cpd,0))) THEN (i.net_inv-(qmax_final+(3*coalesce(cpd,0))))*item_factor ELSE 0 END) as excess_inv_volume,\n", "        (CASE WHEN cd.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS cpd_depleted_inv,\n", "        (CASE WHEN cd.item_id IS NOT NULL THEN i.net_inv*landing_price ELSE 0 END) AS cpd_depleted_inv_exposure,\n", "        (CASE WHEN cd.item_id IS NOT NULL THEN i.net_inv*item_factor ELSE 0 END) AS cpd_depleted_inv_volume,\n", "        (CASE WHEN fif.item_id IS NOT NULL THEN fif.net_inv ELSE 0 END) AS no_sale_inv,\n", "        (CASE WHEN fif.item_id IS NOT NULL THEN fif.net_inv*landing_price ELSE 0 END) AS no_sale_inv_exposure,\n", "        (CASE WHEN fif.item_id IS NOT NULL THEN fif.net_inv*item_factor ELSE 0 END) AS no_sale_inv_volume,\n", "        (CASE WHEN ef.item_id IS NOT NULL THEN ef.quantity ELSE 0 END) as expiring_inv,\n", "        (CASE WHEN ef.item_id IS NOT NULL THEN ef.quantity*landing_price ELSE 0 END) as expiring_inv_exposure,\n", "        (CASE WHEN ef.item_id IS NOT NULL THEN ef.quantity*item_factor ELSE 0 END) as expiring_inv_volume,\n", "            (\n", "            CASE WHEN fif.item_id IS NOT NULL OR cd.item_id IS NOT NULL THEN i.net_inv\n", "                WHEN i.net_inv > (qmax_final + (3 * COALESCE(cpd, 0))) OR ef.item_id IS NOT NULL THEN GREATEST(COALESCE(ef.quantity, 0), (i.net_inv - qmax_final + (3 * COALESCE(cpd, 0)))) \n", "                ELSE 0 END) AS overall_dead,\n", "            (\n", "            CASE WHEN fif.item_id IS NOT NULL OR cd.item_id IS NOT NULL THEN i.net_inv * landing_price\n", "                WHEN i.net_inv > (qmax_final + (3 * COALESCE(cpd, 0))) OR ef.item_id IS NOT NULL THEN GREATEST(COALESCE(ef.quantity, 0), (i.net_inv - qmax_final + (3 * COALESCE(cpd, 0))))*landing_price \n", "                ELSE 0 END) AS overall_dead_exposure,\n", "            (\n", "            CASE WHEN fif.item_id IS NOT NULL OR cd.item_id IS NOT NULL THEN i.net_inv * item_factor\n", "            WHEN i.net_inv > (qmax_final + (3 * COALESCE(cpd, 0))) OR ef.item_id IS NOT NULL THEN GREATEST(COALESCE(ef.quantity, 0), (i.net_inv - qmax_final + (3 * COALESCE(cpd, 0))))*item_factor \n", "            ELSE 0 END) AS overall_dead_volume\n", "\n", "    from inventory i\n", "    join outlet_details od on od.hot_outlet_id=i.outlet_id\n", "    join item_details id on id.item_id=i.item_id\n", "    join tea_tagging_item tti on tti.item_id=i.item_id and tti.fe_outlet_id=i.outlet_id \n", "    left join landing_price lp\n", "                            on lp.item_id=i.item_id\n", "    left join sto_transfer_cpd_fe cf on cf.outlet_id=i.outlet_id and cf.item_id=i.item_id and cf.for_date=CURRENT_DATE-INTERVAL '1' DAY   \n", "    left join qmax q on q.outlet_id=i.outlet_id and q.item_id=i.item_id\n", "    left join cpd_depletion cd on cd.outlet_id=i.outlet_id and cd.item_id=i.item_id           \n", "    left join fe_final_no_sale fif on fif.facility_id=od.facility_id and fif.item_id=i.item_id \n", "    left join expiring_fe_final ef on ef.outlet_id=i.outlet_id and ef.item_id=i.item_id\n", "    left join supply_etls.item_factor ifac on ifac.item_id=i.item_id\n", "),\n", "\n", "fe_aggregated_final as(\n", "    select  CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "            facility_id,\n", "            fa.outlet_id,\n", "            facility_name,\n", "            SUM(net_inv_present) as net_inv_present,\n", "            MAX(cff.cpd) as sto_cpd,\n", "            MAX(pff.cpd) as po_cpd,\n", "            SUM(net_inv_present)*1.000000/MAX(cff.cpd) as doi_sto_cpd_basis,\n", "            SUM(net_inv_present)*1.000000/MAX(pff.cpd) as doi_po_cpd_basis,\n", "            SUM(net_inv_exposure) as net_inv_exposure,\n", "            SUM(net_inv_volume) as net_inv_volume,\n", "            SUM(excess_inv) as excess_inv,\n", "            SUM(excess_inv_exposure) as excess_inv_exposure,\n", "            SUM(excess_inv_volume) as excess_inv_volume,\n", "            SUM(cpd_depleted_inv) as cpd_depleted_inv,\n", "            SUM(cpd_depleted_inv_exposure) as cpd_depleted_inv_exposure,\n", "            SUM(cpd_depleted_inv_volume) as cpd_depleted_inv_volume,\n", "            SUM(no_sale_inv) as no_sale_inv,\n", "            SUM(no_sale_inv_exposure) as no_sale_inv_exposure,\n", "            SUM(no_sale_inv_volume) as no_sale_inv_volume,\n", "            SUM(expiring_inv) as expiring_inv,\n", "            SUM(expiring_inv_exposure) as expiring_inv_exposure,\n", "            SUM(expiring_inv_volume) as expiring_inv_volume,\n", "            SUM(overall_dead) as overall_dead,\n", "            SUM(overall_dead_exposure) as overall_dead_exposure,\n", "            SUM(overall_dead_volume) as overall_dead_volume\n", "            from fe_aggregated fa\n", "            left join cpd_final_fe cff on cff.outlet_id=fa.outlet_id and cff.for_date=CURRENT_DATE-INTERVAL '1' DAY\n", "            left join po_ordering_cpd_agg pff on pff.outlet_id=fa.outlet_id\n", "            group by 1,2,3,4\n", "),\n", "\n", "be_aggregated as( \n", "select DISTINCT od.facility_id,\n", "        od.inv_outlet_id as outlet_id,\n", "        od.facility_name,\n", "        i.item_id,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS net_inv_present ,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*landing_price ELSE 0 END) AS net_inv_exposure,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*item_factor ELSE 0 END) AS net_inv_volume,\n", "        (CASE WHEN bif.item_id IS NOT NULL THEN bif.net_inv ELSE 0 END) AS no_sale_inv,\n", "        (CASE WHEN bif.item_id IS NOT NULL THEN bif.net_inv*landing_price ELSE 0 END) AS no_sale_inv_exposure,\n", "        (CASE WHEN bif.item_id IS NOT NULL THEN bif.net_inv*item_factor ELSE 0 END) AS no_sale_inv_volume,\n", "        (CASE WHEN cb.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS cpd_depleted_inv,\n", "        (CASE WHEN cb.item_id IS NOT NULL THEN i.net_inv*landing_price ELSE 0 END) AS cpd_depleted_inv_exposure,\n", "        (CASE WHEN cb.item_id IS NOT NULL THEN i.net_inv*item_factor ELSE 0 END) AS cpd_depleted_inv_volume,\n", "        (CASE WHEN ebf.item_id IS NOT NULL THEN ebf.quantity ELSE 0 END) AS expiring_inv,\n", "        (CASE WHEN ebf.item_id IS NOT NULL THEN ebf.quantity*landing_price ELSE 0 END) AS expiring_inv_exposure,\n", "        (CASE WHEN ebf.item_id IS NOT NULL THEN ebf.quantity*item_factor ELSE 0 END) AS expiring_inv_volume,\n", "         (\n", "            CASE WHEN bif.item_id IS NOT NULL OR cb.item_id IS NOT NULL THEN i.net_inv\n", "                 WHEN ebf.item_id IS NOT NULL THEN COALESCE(ebf.quantity,0) \n", "                ELSE 0 END) AS overall_dead,\n", "            (\n", "            CASE WHEN bif.item_id IS NOT NULL OR cb.item_id IS NOT NULL THEN i.net_inv * landing_price\n", "                 WHEN ebf.item_id IS NOT NULL THEN COALESCE(ebf.quantity,0)*landing_price\n", "            ELSE 0 END) AS overall_dead_exposure,\n", "            (\n", "            CASE WHEN bif.item_id IS NOT NULL OR cb.item_id IS NOT NULL THEN i.net_inv * item_factor\n", "                 WHEN ebf.item_id IS NOT NULL THEN COALESCE(ebf.quantity,0)*item_factor\n", "            ELSE 0 END) AS overall_dead_volume\n", "    from inventory_be i\n", "    join outlet_details_be od on od.hot_outlet_id=i.backend_outlet_id\n", "    join item_details id on id.item_id=i.item_id\n", "    left join landing_price lp\n", "                            on lp.item_id=i.item_id\n", "    left join sto_transfer_cpd_be_item bi on bi.be_facility_id=od.facility_id and bi.item_id=i.item_id and bi.for_date=CURRENT_DATE - INTERVAL '1' DAY\n", "    left join supply_etls.item_factor ifac on ifac.item_id=i.item_id\n", "    left join be_final_no_sale bif on bif.be_facility_id=od.facility_id and bif.item_id=i.item_id\n", "    left join cpd_depletion_be cb on cb.be_facility_id=od.facility_id and cb.item_id=i.item_id\n", "    left join expiring_be_final ebf on ebf.outlet_id=od.inv_outlet_id and ebf.item_id=i.item_id            \n", "),\n", "\n", "be_aggregated_final as(\n", "    select  CURRENT_DATE-INTERVAL '1' DAY as date_,\n", "            facility_id,\n", "            fa.outlet_id,\n", "            facility_name,\n", "            SUM(net_inv_present) as net_inv_present,\n", "            MAX(cfb.be_cpd) as sto_cpd,\n", "            MAX(pocpd.be_cpd) as po_cpd,\n", "            SUM(net_inv_present)*1.000000/MAX(cfb.be_cpd) as doi_sto_cpd_basis,\n", "            SUM(net_inv_present)*1.000000/MAX(pocpd.be_cpd) as doi_po_cpd_basis,\n", "            SUM(net_inv_exposure) as net_inv_exposure,\n", "            SUM(net_inv_volume) as net_inv_volume,\n", "            0 as excess_inv,\n", "            0 as excess_inv_exposure,\n", "            0 as excess_inv_volume,\n", "            SUM(cpd_depleted_inv) as cpd_depleted_inv,\n", "            SUM(cpd_depleted_inv_exposure) as cpd_depleted_inv_exposure,\n", "            SUM(cpd_depleted_inv_volume) as cpd_depleted_inv_volume,\n", "            SUM(no_sale_inv) as no_sale_inv,\n", "            SUM(no_sale_inv_exposure) as no_sale_inv_exposure,\n", "            SUM(no_sale_inv_volume) as no_sale_inv_volume,\n", "            SUM(expiring_inv) as expiring_inv,\n", "            SUM(expiring_inv_exposure) as expiring_inv_exposure,\n", "            SUM(expiring_inv_volume) as expiring_inv_volume,\n", "            SUM(overall_dead) as overall_dead,\n", "            SUM(overall_dead_exposure) as overall_dead_exposure,\n", "            SUM(overall_dead_volume) as overall_dead_volume\n", "            from be_aggregated fa\n", "            left join cpd_final_be cfb on cfb.be_facility_id=fa.facility_id and cfb.for_date=CURRENT_DATE - INTERVAL '1' DAY\n", "            left join po_cpd_final_be pocpd on fa.facility_id=pocpd.be_facility_id\n", "            group by 1,2,3,4\n", ")\n", "\n", "select * from be_aggregated_final\n", "UNION ALL \n", "select * from fe_aggregated_final\n", "\n", "\n", "        \"\"\"\n", "    to_trino(inventory_health_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "ee75832c-106e-45ca-ab34-73a89464dd25", "metadata": {}, "outputs": [], "source": ["def backfill():\n", "\n", "    today = datetime.today()\n", "    start_date = today - <PERSON><PERSON><PERSON>(days=53)\n", "\n", "    for i in range(4):\n", "        backfill_date = today - <PERSON><PERSON><PERSON>(days=i)\n", "        print(f\"Backfilling data for: {backfill_date.strftime('%Y-%m-%d')}\")\n", "        # Convert backfill_date into the format 'YYYY-MM-DD'\n", "        formatted_date = backfill_date.strftime(\"%Y-%m-%d\")\n", "\n", "        inventory_health_query = f\"\"\" \n", "\n", "WITH active_outlets as(\n", "    select outlet_id\n", "                from dwh.dim_outlet \n", "                where is_current\n", "                and live_date >= date '2005-01-01'\n", "                and valid_to_ts_ist >= date('{formatted_date}')-INTERVAL '1' DAY\n", "                and live_date <= date('{formatted_date}')-INTERVAL '1' DAY\n", "),\n", "\n", "tea_tagging_item as (\n", "    select --date('{formatted_date}') as updated_at, \n", "        fe_outlet_id, fe_facility_id,fe_facility_name, item_id, assortment_type, store_assortment_type,\n", "        be_hot_outlet_id, be_inv_outlet_id, be_facility_id,be_facility_name,fe_city_id,fe_city_name, assortment_status_id\n", "    from supply_etls.inventory_metrics_tea_tagging\n", "    where flag = 'correct_tea' and be_facility_id <> fe_facility_id\n", "),\n", "\n", "item_details as (\n", "            select --date('{formatted_date}') as updated_at, \n", "                item_id, item_name, l0_id, l0_category, \n", "                        l1_id, l1_category, l2_id, l2_category, p_type, assortment_type \n", "            from supply_etls.item_details\n", "            where assortment_type='Packaged Goods'\n", "            and handling_type = 'Non Packaging Material'\n", "            and l0_category<>'Specials'\n", "),\n", "\n", "sto_transfer_cpd_fe as (\n", "            select for_date,\n", "                    outlet_id,\n", "                    cp.item_id,\n", "                    cpd\n", "            from ars.outlet_item_aps_derived_cpd cp\n", "            join item_details id on id.item_id=cp.item_id\n", "            join tea_tagging_item tea on tea.item_id=cp.item_id and tea.fe_outlet_id=cp.outlet_id   \n", "            where insert_ds_ist = cast(date('{formatted_date}') - INTERVAL '1' DAY as varchar)          \n", "            and lake_active_record            \n", "            and for_date = date('{formatted_date}') - INTERVAL '1' DAY\n", "            group by 1,2,3,4\n", "),\n", "\n", "cpd_final_fe as(\n", "    select for_date,\n", "            outlet_id,\n", "            sum(cpd) as cpd\n", "            from sto_transfer_cpd_fe\n", "            group by 1,2\n", "),\n", "\n", "sto_transfer_cpd_be_item as(         \n", "select  for_date, be_facility_id, tea.item_id, sum(cpd) as be_cpd\n", "    from tea_tagging_item tea \n", "    join sto_transfer_cpd_fe cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "    group by 1,2,3\n", "),\n", "\n", "cpd_final_be as (             \n", "    select  for_date, be_facility_id, sum(be_cpd) as be_cpd\n", "    from sto_transfer_cpd_be_item\n", "    group by 1,2\n", "),\n", "\n", "outlet_details_be as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where (ars_check=1 \n", "        and taggings = 'be'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ('Packaged Goods')))\n", "        or facility_id in (2960,3127,3213)\n", "),\n", "\n", "outlet_details as (\n", "    select \n", "        city_id, city_name, hot_outlet_id, hot_outlet_name, \n", "        inv_outlet_id, inv_outlet_name, facility_id, facility_name, business_type_id,taggings\n", "    from supply_etls.outlet_details\n", "    where ars_check=1 \n", "        and taggings = 'fe'\n", "        and (grocery_active_count >= 1) --'Packaged Goods'\n", "        and (store_type in ( 'Dark Store'))\n", "),\n", "    \n", "    inventory as(\n", "    select i.item_id,\n", "            outlet_id,\n", "            sum(current_inventory) as net_inv\n", "            from supply_etls.hourly_inventory_snapshots i\n", "            join item_details id on id.item_id=i.item_id\n", "            where date_ist = date('{formatted_date}') - INTERVAL '1' DAY\n", "            and hour(updated_at_ist)=(select max(hour(updated_at_ist)) from supply_etls.hourly_inventory_snapshots where date_ist = date('{formatted_date}') - INTERVAL '1' DAY)\n", "             and substate_id IN (1,3)\n", "            group by 1,2\n", "    ),\n", "    \n", "    inventory_be as(\n", "    select DISTINCT backend_outlet_id, i.item_id,\n", "            sum(current_inventory) as net_inv\n", "            from supply_etls.hourly_inventory_snapshots i\n", "            join item_details id on id.item_id=i.item_id\n", "            where date_ist = date('{formatted_date}') - INTERVAL '1' DAY\n", "            and hour(updated_at_ist)=(select max(hour(updated_at_ist)) from supply_etls.hourly_inventory_snapshots where date_ist = date('{formatted_date}') - INTERVAL '1' DAY)\n", "             and substate_id IN (1,3)\n", "            group by 1,2\n", "    ),\n", "     \n", "    po_ordering_cpd as (\n", "            select cp.item_id, outlet_id, cpd\n", "            from ars.default_cpd cp\n", "            join item_details id on id.item_id=cp.item_id\n", "            join tea_tagging_item tea on tea.item_id=cp.item_id and tea.fe_outlet_id=cp.outlet_id   \n", "            where active and lake_active_record\n", "    ),\n", "    \n", "    po_ordering_cpd_agg as (\n", "            select outlet_id, sum(cpd) as cpd\n", "            from po_ordering_cpd\n", "            group by 1\n", "    ),\n", "    \n", "    po_ordering_cpd_be_item as(         \n", "        select  be_facility_id, tea.item_id, sum(cpd) as be_cpd\n", "            from tea_tagging_item tea \n", "            join po_ordering_cpd cpd_fe on cpd_fe.item_id=tea.item_id and cpd_fe.outlet_id=tea.fe_outlet_id\n", "            group by 1,2\n", "    ),\n", "    \n", "    po_cpd_final_be as (             \n", "    select be_facility_id, sum(be_cpd) as be_cpd\n", "    from po_ordering_cpd_be_item\n", "    group by 1\n", "    ),\n", "\n", "\n", " landing_price as(\n", "        WITH invoice_details AS (\n", "    SELECT\n", "        outlet_id, grofers_order_id, created_at, merchant_id, invoice_id, id\n", "    FROM pos.pos_invoice\n", "    WHERE lake_active_record\n", "        AND insert_ds_ist BETWEEN CAST(date('{formatted_date}') - INTERVAL '100' day AS VARCHAR) AND CAST(date('{formatted_date}') as varchar)           \n", "        AND invoice_type_id IN (5,14,16)\n", "        AND grofers_order_id IS NOT NULL AND grofers_order_id != ''\n", "    ),\n", "\n", "    pipd AS (\n", "        SELECT \n", "            variant_id, quantity, invoice_id, pos_timestamp\n", "        FROM pos.pos_invoice_product_details\n", "        WHERE lake_active_record\n", "            AND insert_ds_ist BETWEEN CAST(date('{formatted_date}') - INTERVAL '100' day AS VARCHAR) AND CAST(date('{formatted_date}') as varchar)              \n", "    ),\n", "    \n", "    pipl AS (\n", "        SELECT landing_price, variant_id, invoice_id\n", "        FROM pos.pos_invoice_product_lp\n", "        WHERE lake_active_record\n", "    ),\n", "    \n", "    variant AS (\n", "        SELECT item_id, variant_id \n", "        FROM rpc.product_product \n", "        WHERE lake_active_record = TRUE\n", "        GROUP BY 1,2\n", "    ),\n", "    \n", "    invoice_aggregated AS (\n", "        SELECT \n", "            id.outlet_id, \n", "            v.item_id,\n", "            SUM(pipl.landing_price * pipd.quantity) * 1.00 / NULLIF(SUM(pipd.quantity), 0) AS avg_landing_price\n", "        FROM invoice_details id\n", "        LEFT JOIN pipd ON pipd.invoice_id = id.id\n", "        LEFT JOIN pipl ON pipl.invoice_id = id.id AND pipl.variant_id = pipd.variant_id\n", "        LEFT JOIN variant v ON pipl.variant_id = v.variant_id\n", "        GROUP BY 1,2\n", "    )\n", "    \n", "    SELECT       \n", "        item_id,\n", "        AVG(avg_landing_price) AS landing_price\n", "    FROM invoice_aggregated\n", "    group by 1\n", " ),\n", "\n", "qmax as (                               \n", "    select  item_id, outlet_id, max(qmax_final) qmax_final\n", "    from supply_etls.ars_qmax_log_v2\n", "    where date(updated_at) BETWEEN date('{formatted_date}')-interval'1'day AND date('{formatted_date}')            \n", "    group by 1,2\n", "),\n", "\n", "festive_details as (\n", "        select\n", "        f.be_facility_id,\n", "        tea.fe_outlet_id,\n", "        f.item_id,\n", "        id.item_name,\n", "        event_id,\n", "        festival_name as festival,\n", "        assortment_reason_type as festival_bau,    \n", "        sum(planned_quantity) as planned_qty\n", "        from ars_etls.final_festival_planning f\n", "        join item_details id on id.item_id=f.item_id\n", "        join tea_tagging_item tea on tea.item_id= f.item_id and tea.be_facility_id=f.be_facility_id\n", "        where festival_name is not null\n", "        and sale_end_date BETWEEN date('{formatted_date}') - INTERVAL '45' DAY AND date('{formatted_date}')-INTERVAL '15' DAY\n", "        and tea.fe_outlet_id IS NOT NULL               \n", "        group by 1,2,3,4,5,6,7\n", "),\n", "\n", "cpd_depletion as(\n", "    WITH peak_cpd AS (\n", "    SELECT \n", "        cp.outlet_id,\n", "        cp.item_id,\n", "        fd.item_name,\n", "        fd.event_id,\n", "        fd.festival,\n", "        MAX(aps_adjusted) AS peak_cpd                   \n", "    FROM ars.outlet_item_aps_derived_cpd cp\n", "    JOIN festive_details fd ON fd.item_id = cp.item_id and fd.fe_outlet_id=cp.outlet_id\n", "    WHERE \n", "        cp.insert_ds_ist BETWEEN CAST(date('{formatted_date}') - INTERVAL '90' DAY AS VARCHAR) AND CAST(date('{formatted_date}') - INTERVAL '15' DAY AS VARCHAR)\n", "        AND cp.lake_active_record\n", "        AND cp.for_date BETWEEN date('{formatted_date}') - INTERVAL '90' DAY AND date('{formatted_date}') - INTERVAL '15' DAY\n", "    GROUP BY \n", "        1,2,3,4,5\n", "    )\n", "    SELECT\n", "        cp.outlet_id,\n", "        cp.item_id,\n", "        1 as flag\n", "    FROM ars.outlet_item_aps_derived_cpd cp\n", "    JOIN peak_cpd p ON p.item_id = cp.item_id and p.outlet_id =cp.outlet_id\n", "    WHERE cp.insert_ds_ist = CAST(date('{formatted_date}') - INTERVAL '1' DAY AS VARCHAR)\n", "        AND cp.lake_active_record\n", "        AND cp.for_date =date('{formatted_date}')-INTERVAL '1' DAY  \n", "        AND cp.aps_adjusted <= 0.2 * p.peak_cpd \n", "        AND cp.aps_adjusted < 1\n", "),\n", "\n", "cpd_depletion_be as(\n", "    select DISTINCT tti.item_id, be_facility_id\n", "    from tea_tagging_item tti \n", "    join cpd_depletion cd on cd.outlet_id=tti.fe_outlet_id and cd.item_id=tti.item_id\n", "    group by 1,2\n", "    having max(flag)=1 \n", "),\n", "\n", "item_product_mapping as (\n", "    select item_id, product_id, multiplier\n", "    from dwh.dim_item_product_offer_mapping\n", "    where is_current\n", "),\n", "\n", "sales as (\n", "        select DISTINCT fs.outlet_id, ip.item_id, procured_quantity   \n", "        from dwh.fact_sales_order_item_details fs\n", "        join item_product_mapping ip on ip.product_id = fs.product_id\n", "        where order_create_dt_ist BETWEEN date('{formatted_date}')-INTERVAL '60' DAY AND date('{formatted_date}')-INTERVAL '1' DAY\n", "        AND is_internal_order = false AND order_current_status = 'DELIVERED'\n", "        group by 1,2,3\n", "    ),\n", "\n", "final_inv_fe_sale as(\n", "    select od.facility_id, od.facility_name, i.item_id, net_inv,\n", "            CASE WHEN s.item_id>0 THEN 1 ELSE 0 END as sale_flag\n", "    from inventory i\n", "    join item_details id on id.item_id=i.item_id\n", "    join tea_tagging_item tea on tea.item_id=i.item_id and tea.fe_outlet_id=i.outlet_id\n", "    join outlet_details od on od.hot_outlet_id=i.outlet_id\n", "    left join sales s on i.item_id=s.item_id and i.outlet_id=s.outlet_id\n", "    group by 1,2,3,4,5\n", "), \n", "\n", "final_inv_be_sale as(\n", "    select od.facility_id as be_facility_id, od.facility_name as be_facility_name, i.item_id, i.net_inv\n", "    from inventory_be i\n", "    join item_details id on id.item_id=i.item_id\n", "    join outlet_details_be od on od.hot_outlet_id=i.backend_outlet_id\n", "    join tea_tagging_item tti on tti.be_facility_id = od.facility_id and tti.item_id = id.item_id\n", "    left join final_inv_fe_sale ifs on ifs.facility_id=tti.fe_facility_id and ifs.item_id=i.item_id\n", "    group by 1,2,3,4\n", "    having max(sale_flag)=0\n", "),\n", "\n", "final_inv_fe as(\n", "    select DISTINCT item_id, net_inv, facility_id, facility_name\n", "    from final_inv_fe_sale fi\n", "    group by 1,2,3,4\n", "    having max(sale_flag) = 0\n", "),\n", "\n", "ars AS ( \n", "    SELECT \n", "        insert_ds_ist, \n", "        facility_id, \n", "        id.item_id,\n", "        CASE \n", "            WHEN available_hours > 0 THEN 1  \n", "            ELSE 0 \n", "        END AS avail_flag\n", "    FROM ars.daily_orders_and_availability ars\n", "    JOIN item_details id ON id.item_id = ars.item_id\n", "    JOIN tea_tagging_item tea ON tea.item_id = ars.item_id AND tea.fe_facility_id = ars.facility_id\n", "    WHERE order_date BETWEEN date('{formatted_date}') - INTERVAL '45' DAY and date('{formatted_date}') - INTERVAL '1' DAY            \n", "        AND CAST(insert_ds_ist AS DATE) BETWEEN date('{formatted_date}') - INTERVAL '45' DAY and date('{formatted_date}') - INTERVAL '1' DAY     \n", "),\n", "\n", "ars_be AS ( \n", "    SELECT \n", "        insert_ds_ist, \n", "        be_facility_id,\n", "        facility_id, \n", "        id.item_id,\n", "        CASE \n", "            WHEN available_hours > 0 THEN 1  \n", "            ELSE 0 \n", "        END AS avail_flag\n", "    FROM ars.daily_orders_and_availability ars\n", "    JOIN item_details id ON id.item_id = ars.item_id\n", "    JOIN tea_tagging_item tea ON tea.item_id = ars.item_id AND tea.fe_facility_id = ars.facility_id\n", "    WHERE order_date BETWEEN date('{formatted_date}') - INTERVAL '45' DAY and date('{formatted_date}') - INTERVAL '1' DAY            \n", "        AND CAST(insert_ds_ist AS DATE) BETWEEN date('{formatted_date}') - INTERVAL '45' DAY and date('{formatted_date}') - INTERVAL '1' DAY     \n", "),\n", "\n", "ars_be_two AS ( \n", "    SELECT \n", "        insert_ds_ist, \n", "        be_facility_id,\n", "        item_id,\n", "        max(avail_flag) AS avail_flag\n", "    FROM ars_be\n", "    group by 1,2,3\n", "),\n", "\n", "ars_available_days AS (\n", "    SELECT \n", "        facility_id,\n", "        item_id,\n", "        COUNT(DISTINCT CAST(insert_ds_ist AS DATE)) AS available_days_count\n", "    FROM ars\n", "    WHERE avail_flag = 1 \n", "    GROUP BY 1,2\n", "),\n", "\n", "ars_available_days_be AS (\n", "    SELECT \n", "        be_facility_id,\n", "        item_id,\n", "        COUNT(DISTINCT CAST(insert_ds_ist AS DATE)) AS available_days_count\n", "    FROM ars_be_two\n", "    where avail_flag = 1    \n", "    GROUP BY 1,2\n", "),\n", "\n", "ars_final_fe AS (\n", "    SELECT DISTINCT\n", "        facility_id, \n", "        item_id\n", "    FROM ars_available_days\n", "    WHERE available_days_count >= 30  \n", "),\n", "\n", "ars_final_be AS (\n", "    SELECT DISTINCT\n", "        be_facility_id, \n", "        item_id\n", "    FROM ars_available_days_be\n", "    WHERE available_days_count >= 30  \n", "),\n", "\n", "fe_final_no_sale as (\n", "    select f.facility_id , f.facility_name,f.item_id, net_inv\n", "        from final_inv_fe f\n", "        join ars_final_fe a on a.facility_id=f.facility_id and a.item_id=f.item_id\n", "),\n", "\n", "be_final_no_sale as(\n", "    select f.be_facility_id,\n", "            f.be_facility_name,\n", "            f.item_id,\n", "            net_inv\n", "        from final_inv_be_sale f\n", "        join ars_final_be a on a.be_facility_id=f.be_facility_id and a.item_id=f.item_id\n", "),\n", "\n", "shelf_life as(\n", "    select DISTINCT item_id, shelf_life\n", "    from rpc.product_product\n", "    where active=1 and lake_active_record\n", "),\n", "\n", "expiring_fe as(\n", "    select cast(s.outlet_id as int) as outlet_id,\n", "            cast(s.item_id as int) as item_id, s.quantity,\n", "            EXTRACT(DAY FROM (cast(batch_expiry as date) - date('{formatted_date}'))) * 1.0000 / nullif(shelf_life,0) as per_left\n", "    from dynamodb.blinkit_store_inventory_service_li_snapshot s\n", "    left join shelf_life sl on s.item_id=cast(sl.item_id as varchar)\n", "    where state='GOOD' \n", "    and quantity>0\n", "),\n", "\n", "expiring_fe_final as(                                        \n", "    select outlet_id, item_id, quantity\n", "    from expiring_fe\n", "    where per_left<=0.3\n", "),\n", "\n", "variant AS (\n", "        SELECT item_id, variant_id \n", "        FROM rpc.product_product \n", "        WHERE lake_active_record = TRUE\n", "        GROUP BY 1,2\n", "),\n", "    \n", "expiring_be as(\n", "    select outlet_id, v.item_id, delta as quantity, \n", "            EXTRACT(DAY FROM (cast(expiry_date as date) - date('{formatted_date}'))) * 1.0000 / nullif(shelf_life,0) as per_left\n", "    from ims.ims_inventory_stock_details isd\n", "    join variant v on v.variant_id=isd.variant_id\n", "    left join shelf_life sl on v.item_id=sl.item_id\n", "    join outlet_details_be od on od.inv_outlet_id=isd.outlet_id\n", "    where lake_active_record = TRUE and insert_ds_ist=cast(date('{formatted_date}') as varchar)\n", "),\n", "\n", "expiring_be_final as(\n", "    select outlet_id, item_id, quantity\n", "    from expiring_be\n", "    where per_left<=0.3\n", "),\n", "\n", "fe_aggregated as(                                       \n", "select od.facility_id,\n", "        i.outlet_id,\n", "        od.facility_name,\n", "        i.item_id,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS net_inv_present ,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*landing_price ELSE 0 END) AS net_inv_exposure,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*item_factor ELSE 0 END) AS net_inv_volume,\n", "        (CASE WHEN i.item_id IS NOT NULL and i.net_inv> (qmax_final+(3*coalesce(cpd,0))) THEN (i.net_inv-(qmax_final+(3*coalesce(cpd,0)))) ELSE 0 END) as excess_inv,\n", "        (CASE WHEN i.item_id IS NOT NULL and i.net_inv> (qmax_final+(3*coalesce(cpd,0))) THEN (i.net_inv-(qmax_final+(3*coalesce(cpd,0))))*landing_price ELSE 0 END) as excess_inv_exposure,\n", "        (CASE WHEN i.item_id IS NOT NULL and i.net_inv> (qmax_final+(3*coalesce(cpd,0))) THEN (i.net_inv-(qmax_final+(3*coalesce(cpd,0))))*item_factor ELSE 0 END) as excess_inv_volume,\n", "        (CASE WHEN cd.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS cpd_depleted_inv,\n", "        (CASE WHEN cd.item_id IS NOT NULL THEN i.net_inv*landing_price ELSE 0 END) AS cpd_depleted_inv_exposure,\n", "        (CASE WHEN cd.item_id IS NOT NULL THEN i.net_inv*item_factor ELSE 0 END) AS cpd_depleted_inv_volume,\n", "        (CASE WHEN fif.item_id IS NOT NULL THEN fif.net_inv ELSE 0 END) AS no_sale_inv,\n", "        (CASE WHEN fif.item_id IS NOT NULL THEN fif.net_inv*landing_price ELSE 0 END) AS no_sale_inv_exposure,\n", "        (CASE WHEN fif.item_id IS NOT NULL THEN fif.net_inv*item_factor ELSE 0 END) AS no_sale_inv_volume,\n", "        (CASE WHEN ef.item_id IS NOT NULL THEN ef.quantity ELSE 0 END) as expiring_inv,\n", "        (CASE WHEN ef.item_id IS NOT NULL THEN ef.quantity*landing_price ELSE 0 END) as expiring_inv_exposure,\n", "        (CASE WHEN ef.item_id IS NOT NULL THEN ef.quantity*item_factor ELSE 0 END) as expiring_inv_volume,\n", "            (\n", "            CASE WHEN fif.item_id IS NOT NULL OR cd.item_id IS NOT NULL THEN i.net_inv\n", "                WHEN i.net_inv > (qmax_final + (3 * COALESCE(cpd, 0))) OR ef.item_id IS NOT NULL THEN GREATEST(COALESCE(ef.quantity, 0), (i.net_inv - qmax_final + (3 * COALESCE(cpd, 0)))) \n", "                ELSE 0 END) AS overall_dead,\n", "            (\n", "            CASE WHEN fif.item_id IS NOT NULL OR cd.item_id IS NOT NULL THEN i.net_inv * landing_price\n", "                WHEN i.net_inv > (qmax_final + (3 * COALESCE(cpd, 0))) OR ef.item_id IS NOT NULL THEN GREATEST(COALESCE(ef.quantity, 0), (i.net_inv - qmax_final + (3 * COALESCE(cpd, 0))))*landing_price \n", "                ELSE 0 END) AS overall_dead_exposure,\n", "            (\n", "            CASE WHEN fif.item_id IS NOT NULL OR cd.item_id IS NOT NULL THEN i.net_inv * item_factor\n", "            WHEN i.net_inv > (qmax_final + (3 * COALESCE(cpd, 0))) OR ef.item_id IS NOT NULL THEN GREATEST(COALESCE(ef.quantity, 0), (i.net_inv - qmax_final + (3 * COALESCE(cpd, 0))))*item_factor \n", "            ELSE 0 END) AS overall_dead_volume\n", "\n", "    from inventory i\n", "    join outlet_details od on od.hot_outlet_id=i.outlet_id\n", "    join item_details id on id.item_id=i.item_id\n", "    join tea_tagging_item tti on tti.item_id=i.item_id and tti.fe_outlet_id=i.outlet_id \n", "    join active_outlets ao on ao.outlet_id=i.outlet_id\n", "    left join landing_price lp\n", "                            on lp.item_id=i.item_id\n", "    left join sto_transfer_cpd_fe cf on cf.outlet_id=i.outlet_id and cf.item_id=i.item_id and cf.for_date=date('{formatted_date}')-INTERVAL '1' DAY   \n", "    left join qmax q on q.outlet_id=i.outlet_id and q.item_id=i.item_id\n", "    left join cpd_depletion cd on cd.outlet_id=i.outlet_id and cd.item_id=i.item_id           \n", "    left join fe_final_no_sale fif on fif.facility_id=od.facility_id and fif.item_id=i.item_id \n", "    left join expiring_fe_final ef on ef.outlet_id=i.outlet_id and ef.item_id=i.item_id\n", "    left join supply_etls.item_factor ifac on ifac.item_id=i.item_id\n", "),\n", "\n", "fe_aggregated_final as(\n", "    select  date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "            facility_id,\n", "            fa.outlet_id,\n", "            facility_name,\n", "            SUM(net_inv_present) as net_inv_present,\n", "            MAX(cff.cpd) as sto_cpd,\n", "            MAX(pff.cpd) as po_cpd,\n", "            SUM(net_inv_present)*1.000000/MAX(cff.cpd) as doi_sto_cpd_basis,\n", "            SUM(net_inv_present)*1.000000/MAX(pff.cpd) as doi_po_cpd_basis,\n", "            SUM(net_inv_exposure) as net_inv_exposure,\n", "            SUM(net_inv_volume) as net_inv_volume,\n", "            SUM(excess_inv) as excess_inv,\n", "            SUM(excess_inv_exposure) as excess_inv_exposure,\n", "            SUM(excess_inv_volume) as excess_inv_volume,\n", "            SUM(cpd_depleted_inv) as cpd_depleted_inv,\n", "            SUM(cpd_depleted_inv_exposure) as cpd_depleted_inv_exposure,\n", "            SUM(cpd_depleted_inv_volume) as cpd_depleted_inv_volume,\n", "            SUM(no_sale_inv) as no_sale_inv,\n", "            SUM(no_sale_inv_exposure) as no_sale_inv_exposure,\n", "            SUM(no_sale_inv_volume) as no_sale_inv_volume,\n", "            SUM(expiring_inv) as expiring_inv,\n", "            SUM(expiring_inv_exposure) as expiring_inv_exposure,\n", "            SUM(expiring_inv_volume) as expiring_inv_volume,\n", "            SUM(overall_dead) as overall_dead,\n", "            SUM(overall_dead_exposure) as overall_dead_exposure,\n", "            SUM(overall_dead_volume) as overall_dead_volume\n", "            from fe_aggregated fa\n", "            left join cpd_final_fe cff on cff.outlet_id=fa.outlet_id and cff.for_date=date('{formatted_date}')-INTERVAL '1' DAY\n", "            left join po_ordering_cpd_agg pff on pff.outlet_id=fa.outlet_id\n", "            group by 1,2,3,4\n", "),\n", "\n", "be_aggregated as( \n", "select DISTINCT od.facility_id,\n", "        od.inv_outlet_id as outlet_id,\n", "        od.facility_name,\n", "        i.item_id,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS net_inv_present ,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*landing_price ELSE 0 END) AS net_inv_exposure,\n", "        (CASE WHEN i.item_id IS NOT NULL THEN  i.net_inv*item_factor ELSE 0 END) AS net_inv_volume,\n", "        (CASE WHEN bif.item_id IS NOT NULL THEN bif.net_inv ELSE 0 END) AS no_sale_inv,\n", "        (CASE WHEN bif.item_id IS NOT NULL THEN bif.net_inv*landing_price ELSE 0 END) AS no_sale_inv_exposure,\n", "        (CASE WHEN bif.item_id IS NOT NULL THEN bif.net_inv*item_factor ELSE 0 END) AS no_sale_inv_volume,\n", "        (CASE WHEN cb.item_id IS NOT NULL THEN i.net_inv ELSE 0 END) AS cpd_depleted_inv,\n", "        (CASE WHEN cb.item_id IS NOT NULL THEN i.net_inv*landing_price ELSE 0 END) AS cpd_depleted_inv_exposure,\n", "        (CASE WHEN cb.item_id IS NOT NULL THEN i.net_inv*item_factor ELSE 0 END) AS cpd_depleted_inv_volume,\n", "        (CASE WHEN ebf.item_id IS NOT NULL THEN ebf.quantity ELSE 0 END) AS expiring_inv,\n", "        (CASE WHEN ebf.item_id IS NOT NULL THEN ebf.quantity*landing_price ELSE 0 END) AS expiring_inv_exposure,\n", "        (CASE WHEN ebf.item_id IS NOT NULL THEN ebf.quantity*item_factor ELSE 0 END) AS expiring_inv_volume,\n", "         (\n", "            CASE WHEN bif.item_id IS NOT NULL OR cb.item_id IS NOT NULL THEN i.net_inv\n", "                 WHEN ebf.item_id IS NOT NULL THEN COALESCE(ebf.quantity,0) \n", "                ELSE 0 END) AS overall_dead,\n", "            (\n", "            CASE WHEN bif.item_id IS NOT NULL OR cb.item_id IS NOT NULL THEN i.net_inv * landing_price\n", "                 WHEN ebf.item_id IS NOT NULL THEN COALESCE(ebf.quantity,0)*landing_price\n", "            ELSE 0 END) AS overall_dead_exposure,\n", "            (\n", "            CASE WHEN bif.item_id IS NOT NULL OR cb.item_id IS NOT NULL THEN i.net_inv * item_factor\n", "                 WHEN ebf.item_id IS NOT NULL THEN COALESCE(ebf.quantity,0)*item_factor\n", "            ELSE 0 END) AS overall_dead_volume\n", "    from inventory_be i\n", "    join outlet_details_be od on od.hot_outlet_id=i.backend_outlet_id\n", "    join item_details id on id.item_id=i.item_id\n", "    left join landing_price lp\n", "                            on lp.item_id=i.item_id\n", "    left join sto_transfer_cpd_be_item bi on bi.be_facility_id=od.facility_id and bi.item_id=i.item_id and bi.for_date=date('{formatted_date}')-INTERVAL '1' DAY\n", "    left join supply_etls.item_factor ifac on ifac.item_id=i.item_id\n", "    left join be_final_no_sale bif on bif.be_facility_id=od.facility_id and bif.item_id=i.item_id\n", "    left join cpd_depletion_be cb on cb.be_facility_id=od.facility_id and cb.item_id=i.item_id\n", "    left join expiring_be_final ebf on ebf.outlet_id=od.inv_outlet_id and ebf.item_id=i.item_id\n", "),\n", "\n", "be_aggregated_final as(\n", "    select  date('{formatted_date}')-INTERVAL '1' DAY as date_,\n", "            facility_id,\n", "            fa.outlet_id,\n", "            facility_name,\n", "            SUM(net_inv_present) as net_inv_present,\n", "            MAX(cfb.be_cpd) as sto_cpd,\n", "            MAX(pocpd.be_cpd) as po_cpd,\n", "            SUM(net_inv_present)*1.000000/MAX(cfb.be_cpd) as doi_sto_cpd_basis,\n", "            SUM(net_inv_present)*1.000000/MAX(pocpd.be_cpd) as doi_po_cpd_basis,\n", "            SUM(net_inv_exposure) as net_inv_exposure,\n", "            SUM(net_inv_volume) as net_inv_volume,\n", "            0 as excess_inv,\n", "            0 as excess_inv_exposure,\n", "            0 as excess_inv_volume,\n", "            SUM(cpd_depleted_inv) as cpd_depleted_inv,\n", "            SUM(cpd_depleted_inv_exposure) as cpd_depleted_inv_exposure,\n", "            SUM(cpd_depleted_inv_volume) as cpd_depleted_inv_volume,\n", "            SUM(no_sale_inv) as no_sale_inv,\n", "            SUM(no_sale_inv_exposure) as no_sale_inv_exposure,\n", "            SUM(no_sale_inv_volume) as no_sale_inv_volume,\n", "            SUM(expiring_inv) as expiring_inv,\n", "            SUM(expiring_inv_exposure) as expiring_inv_exposure,\n", "            SUM(expiring_inv_volume) as expiring_inv_volume,\n", "            SUM(overall_dead) as overall_dead,\n", "            SUM(overall_dead_exposure) as overall_dead_exposure,\n", "            SUM(overall_dead_volume) as overall_dead_volume\n", "            from be_aggregated fa\n", "            left join cpd_final_be cfb on cfb.be_facility_id=fa.facility_id and cfb.for_date=date('{formatted_date}')-INTERVAL '1' DAY\n", "            left join po_cpd_final_be pocpd on fa.facility_id=pocpd.be_facility_id\n", "            group by 1,2,3,4\n", ")\n", "\n", "select * from be_aggregated_final\n", "UNION ALL \n", "select * from fe_aggregated_final\n", "\n", "\n", "        \"\"\"\n", "\n", "        to_trino(\n", "            inventory_health_query, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs\n", "        )\n", "\n", "        print(f\"Backfilled data for: {backfill_date.strftime('%Y-%m-%d')}\")"]}, {"cell_type": "code", "execution_count": null, "id": "0f322735-a247-4b33-a6f6-4f538d0a8293", "metadata": {}, "outputs": [], "source": ["if backfill_flag == 0:\n", "    normal()\n", "else:\n", "    backfill()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}