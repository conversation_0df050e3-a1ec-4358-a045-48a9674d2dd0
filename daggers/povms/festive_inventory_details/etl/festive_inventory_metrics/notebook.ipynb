{"cells": [{"cell_type": "code", "execution_count": null, "id": "ebc9f3a9-66c6-40ab-be97-974a950bf723", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pencilbox as pb\n", "import numpy as np\n", "import uuid\n", "from datetime import datetime\n", "from datetime import datetime, date, timedelta\n", "from calendar import monthrange\n", "import math\n", "import warnings\n", "import time\n", "import gc\n", "\n", "!pip install pymysql\n", "import pymysql\n", "\n", "\n", "trino = pb.get_connection(\"[Warehouse] Trino\")\n", "redshift = pb.get_connection(\"[Warehouse] Redshift\")\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)"]}, {"cell_type": "code", "execution_count": null, "id": "cd39069a-54c0-431a-8b61-dda8ca73fd4c", "metadata": {}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        print(f\"Read attempt: {attempt}...\")\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "f73125a8-c9d4-43e8-9cc6-0b4bab1900d1", "metadata": {}, "outputs": [], "source": ["scheduling_input = pb.from_sheets(\n", "    \"1p2395jSUe1Wv4P6rs2lMW14A9hBhRH9_KS47cFeLlvE\",\n", "    \"final_scheduling\",\n", "    clear_cache=True,\n", ")\n", "\n", "# scheduling_input = pd.read_csv('new.csv')\n", "\n", "scheduling_input = scheduling_input[\n", "    [\n", "        \"date_\",\n", "        \"facility_id\",\n", "        \"vendor_id\",\n", "        \"scheduled_quantity\",\n", "        \"vendor_name\",\n", "    ]\n", "]\n", "\n", "scheduling_input = scheduling_input[\n", "    ~(\n", "        (scheduling_input[\"date_\"] == \"\")\n", "        | (scheduling_input[\"facility_id\"] == \"\")\n", "        | (scheduling_input[\"vendor_id\"] == \"\")\n", "        | (scheduling_input[\"scheduled_quantity\"] == \"\")\n", "        | (scheduling_input[\"vendor_name\"] == \"\")\n", "    )\n", "]\n", "\n", "scheduling_input[\"insert_ds_ist\"] = pd.to_datetime(\n", "    datetime.today() + <PERSON><PERSON><PERSON>(hours=5.5)\n", ")\n", "\n", "scheduling_input[[\"facility_id\", \"vendor_id\", \"scheduled_quantity\"]] = (\n", "    scheduling_input[[\"facility_id\", \"vendor_id\", \"scheduled_quantity\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "scheduling_input = scheduling_input[scheduling_input[\"scheduled_quantity\"] > 0]\n", "\n", "scheduling_input = scheduling_input[\n", "    [\n", "        \"insert_ds_ist\",\n", "        \"date_\",\n", "        \"facility_id\",\n", "        \"vendor_id\",\n", "        \"scheduled_quantity\",\n", "        \"vendor_name\",\n", "    ]\n", "]\n", "\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"insert_ds_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"updated date details\",\n", "    },\n", "    {\"name\": \"date_\", \"type\": \"varchar\", \"description\": \"updated date details\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    {\"name\": \"vendor_id\", \"type\": \"integer\", \"description\": \"product mrp\"},\n", "    {\"name\": \"scheduled_quantity\", \"type\": \"integer\", \"description\": \"brand id\"},\n", "    {\"name\": \"vendor_name\", \"type\": \"varchar\", \"description\": \"brand name\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"multi_grn_schedule\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"vendor_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"item details\",\n", "}\n", "\n", "pb.to_trino(scheduling_input, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "aeab11e9-c11a-42e4-913e-684016753372", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3180f6ee-ca47-40f7-aad0-b255a528ce8e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6447932b-8b9b-4fc3-92bb-d867ad0e7a41", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9bcb4094-476b-4cb4-b218-00f209242dc4", "metadata": {}, "outputs": [], "source": ["def festive_details():\n", "    festive_details = \"\"\"\n", "    \n", "    select\n", "        updated_at_ist as insert_ds_ist,\n", "        od.hot_outlet_id,\n", "        od.inv_outlet_id,\n", "        a.facility_id,\n", "        od.facility_name,\n", "        a.item_id,\n", "        type as category_type,\n", "        id.p_type,\n", "        festival as event_name,\n", "        min(date(a.sale_start)) as event_start_date,\n", "        min(date(a.sale_end)) as event_end_date,\n", "        max(final_qty) as final_qty,\n", "        min(date(a.cutt_off)) as cut_off_date\n", "\n", "            from metrics.festival_final_qty a\n", "\n", "                join\n", "                    metrics.item_details id on id.item_id = a.item_id\n", "                join\n", "                    metrics.outlet_details od on od.facility_id = a.facility_id and od.ars_check = 1\n", "\n", "                    where\n", "                        a.sale_start is not null\n", "                        and a.sale_end is not null\n", "                        and a.cutt_off is not null\n", "                        and a.sale_start >= current_date - 45\n", "\n", "                        group by 1,2,3,4,5,6,7,8,9\n", "    \n", "    \"\"\"\n", "    return read_sql_query(festive_details, redshift)\n", "\n", "\n", "festive_details = festive_details()\n", "\n", "festive_details.head()\n", "\n", "festive_details.dtypes\n", "\n", "column_dtypes = [\n", "    {\n", "        \"name\": \"insert_ds_ist\",\n", "        \"type\": \"timestamp(6)\",\n", "        \"description\": \"updated date details\",\n", "    },\n", "    {\"name\": \"hot_outlet_id\", \"type\": \"integer\", \"description\": \"item id\"},\n", "    {\"name\": \"inv_outlet_id\", \"type\": \"integer\", \"description\": \"product mrp\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"brand id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"brand name\"},\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"l0 id\"},\n", "    {\"name\": \"category_type\", \"type\": \"varchar\", \"description\": \"l0 category\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"l1 id\"},\n", "    {\"name\": \"event_name\", \"type\": \"varchar\", \"description\": \"l1 id\"},\n", "    {\"name\": \"event_start_date\", \"type\": \"date\", \"description\": \"l1 id\"},\n", "    {\"name\": \"event_end_date\", \"type\": \"date\", \"description\": \"l1 id\"},\n", "    {\"name\": \"final_qty\", \"type\": \"integer\", \"description\": \"l1 category\"},\n", "    {\"name\": \"cut_off_date\", \"type\": \"date\", \"description\": \"l1 id\"},\n", "]\n", "\n", "kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"festival_final_qty\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"facility_id\", \"item_id\"],\n", "    \"partition_key\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"item details\",\n", "}\n", "\n", "pb.to_trino(festive_details, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "markdown", "id": "028d4efb-050e-473d-891b-ca195e7d64b6", "metadata": {}, "source": ["# assortment details"]}, {"cell_type": "code", "execution_count": null, "id": "08760295-7813-4f2f-ac89-c807d3cf2615", "metadata": {}, "outputs": [], "source": ["def frontend_backend_details():\n", "    frontend_backend_details = f\"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details),\n", "\n", "    item_details as\n", "        (select * from supply_etls.item_details),\n", "\n", "    assortment as\n", "        (select * from interim.festival_final_qty),\n", "\n", "    final_assortment as\n", "        (select\n", "            event_start_date,\n", "            event_end_date,\n", "            cut_off_date,\n", "            event_name,\n", "            category_type as assortment_type,\n", "            cast(hot_outlet_id as varchar) as hot_outlet_id,\n", "            item_id,\n", "            sum(final_qty) as planned_quantity\n", "\n", "                from assortment\n", "\n", "                    group by 1,2,3,4,5,6,7\n", "        ),\n", "\n", "    carts_details as\n", "        (select * from logistics_data_etls.cart_projections),\n", "\n", "    final_carts_details as\n", "        (select * from carts_details\n", "            where (date >= current_date - interval '10' day)\n", "            and carts > 0\n", "        ),\n", "\n", "    max_details as\n", "        (select\n", "            max(updated_on) as updated_on,\n", "            outletid,\n", "            date\n", "\n", "                from final_carts_details\n", "\n", "                    group by 2,3\n", "        ),\n", "\n", "    final_active_ds as\n", "        (select\n", "            -- fcd.updated_on,\n", "            fcd.outletid as outlet_id,\n", "            -- od.city_name,\n", "            -- od.facility_id,\n", "            -- fcd.date as date_,\n", "            -- min(fcd.date) as outbound_start,\n", "            sum(fcd.carts) as total_carts\n", "\n", "                from final_carts_details fcd\n", "\n", "                    join\n", "                        max_details md on md.updated_on = fcd.updated_on and md.outletid = fcd.outletid and md.date = fcd.date\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = fcd.outletid\n", "\n", "                        group by 1\n", "        ),\n", "\n", "    tag_mapping as\n", "        (select * from rpc.item_outlet_tag_mapping),\n", "\n", "    final_tea_taggings as\n", "        (select * from tag_mapping\n", "            where \n", "                active = 1 and\n", "                lake_active_record and\n", "                tag_type_id = 8\n", "        ),\n", "\n", "    ars_mapping as\n", "        (select * from po.bulk_facility_outlet_mapping),\n", "\n", "    final_ars_mapping as\n", "        (select * from ars_mapping\n", "            where\n", "                active and \n", "                lake_active_record\n", "        ),\n", "\n", "    active_assortment_adding as\n", "        (select\n", "            cast(current_timestamp as timestamp) as insert_ds_ist,\n", "            fa.event_start_date,\n", "            fa.event_end_date,\n", "            fa.cut_off_date,\n", "            fa.assortment_type,\n", "            fa.event_name,\n", "            od.city_name,\n", "            ftt.outlet_id as hot_outlet_id,\n", "            od.facility_id,\n", "            od.facility_name,\n", "            cast(ftt.tag_value as int) as be_hot_outlet_id,\n", "            od2.inv_outlet_id as be_inv_outlet_id,\n", "            od2.facility_id as be_facility_id,\n", "            od2.facility_name as be_facility_name,\n", "            ftt.item_id,\n", "            id.item_name,\n", "            id.l0_category,\n", "            id.l1_category,\n", "            id.l2_category,\n", "            id.p_type,\n", "            fa.planned_quantity\n", "\n", "                from final_tea_taggings ftt\n", "\n", "                    join\n", "                        item_details id on id.item_id = ftt.item_id\n", "                        and id.handling_type = 'Non Packaging Material'\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = ftt.outlet_id and od.taggings = 'fe'\n", "\n", "                    join\n", "                        outlet_details od2 on od2.hot_outlet_id = cast(ftt.tag_value as int) and od2.taggings = 'be'\n", "\n", "                    join\n", "                        final_ars_mapping fam on fam.facility_id = od2.facility_id and fam.outlet_id = ftt.outlet_id\n", "\n", "                    join\n", "                        final_assortment fa on fa.item_id = ftt.item_id and fa.hot_outlet_id = ftt.tag_value\n", "\n", "                    join\n", "                        final_active_ds fad on fad.outlet_id = ftt.outlet_id and fad.total_carts > 10\n", "        )\n", "\n", "            select * from active_assortment_adding\n", "\n", "            \n", "    \"\"\"\n", "    return read_sql_query(frontend_backend_details, trino)\n", "\n", "\n", "frontend_backend_details = frontend_backend_details()"]}, {"cell_type": "code", "execution_count": null, "id": "5b1b1b92-d60d-48a7-bb0f-6c24a0603ad1", "metadata": {}, "outputs": [], "source": ["frontend_backend_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "33129c2b-9582-4b27-bcf5-bcc4dd9600d6", "metadata": {}, "outputs": [], "source": ["item_id_list = tuple(list(frontend_backend_details[\"item_id\"].unique()))\n", "facility_id_list = tuple(list(frontend_backend_details[\"facility_id\"].unique()))\n", "be_facility_id_list = tuple(list(frontend_backend_details[\"be_facility_id\"].unique()))\n", "len(item_id_list), len(facility_id_list), len(be_facility_id_list)"]}, {"cell_type": "markdown", "id": "cce23d7d-ae9a-4f76-ba7e-45b1aba266cc", "metadata": {}, "source": ["# inventory details"]}, {"cell_type": "code", "execution_count": null, "id": "64787958-929d-4d86-9229-2eeb88cf2844", "metadata": {}, "outputs": [], "source": ["def inventory():\n", "    inventory = f\"\"\"\n", "    \n", "    with\n", "    inv as\n", "        (select * from ims.ims_item_inventory),\n", "    \n", "    final_inv as\n", "        (select\n", "            item_id,\n", "            outlet_id as hot_outlet_id,\n", "            sum(quantity) as actual_inv\n", "                \n", "                from inv\n", "                \n", "                    where \n", "                        active = 1\n", "                        and lake_active_record\n", "                        and item_id in {item_id_list}\n", "                        and quantity > 0\n", "                        \n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from final_inv\n", "    \n", "    \"\"\"\n", "    return read_sql_query(inventory, trino)\n", "\n", "\n", "inventory = inventory()"]}, {"cell_type": "code", "execution_count": null, "id": "38bcb0c9-5cdb-473a-a5a1-67ed54603fc2", "metadata": {}, "outputs": [], "source": ["inventory.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "35791236-a3d5-4227-9d94-43a1605d10f7", "metadata": {}, "outputs": [], "source": ["be_inv = inventory.rename(\n", "    columns={\"hot_outlet_id\": \"be_inv_outlet_id\", \"actual_inv\": \"be_actual_inv\"}\n", ")\n", "be_inv.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "a5dcf75f-3b7b-442e-8c07-7138a6c9d11d", "metadata": {}, "outputs": [], "source": ["adding_fe_inv_details = pd.merge(\n", "    frontend_backend_details, inventory, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details = pd.merge(\n", "    adding_fe_inv_details, be_inv, on=[\"item_id\", \"be_inv_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]] = (\n", "    adding_be_inv_details[[\"actual_inv\", \"be_actual_inv\"]].fillna(0).astype(int)\n", ")\n", "\n", "frontend_backend_details = pd.DataFrame()\n", "inventory = pd.DataFrame()\n", "be_inv = pd.DataFrame()\n", "adding_fe_inv_details = pd.DataFrame()\n", "\n", "adding_be_inv_details.head(1)"]}, {"cell_type": "markdown", "id": "a09621bb-a08b-4d2a-a2c0-4c44a78580cd", "metadata": {}, "source": ["# blocked inventory"]}, {"cell_type": "code", "execution_count": null, "id": "3d616bc6-512f-413c-8c26-fd19f4cc1a2e", "metadata": {}, "outputs": [], "source": ["def blocked_inv():\n", "    blocked_inv = f\"\"\"\n", "    \n", "    with\n", "    blocked as\n", "        (select * from ims.ims_item_blocked_inventory),\n", "    \n", "    final_blocked as\n", "        (select\n", "            item_id,\n", "            outlet_id as hot_outlet_id,\n", "            sum(quantity) as ttl_blocked_qty\n", "            \n", "                from blocked\n", "                    \n", "                    where\n", "                        active = 1\n", "                        and lake_active_record\n", "                        and blocked_type in (1,2,5)\n", "                        and item_id in {item_id_list}\n", "                        and quantity > 0\n", "                        \n", "                        group by 1,2\n", "        )\n", "        \n", "            select * from final_blocked\n", "    \n", "    \"\"\"\n", "    return read_sql_query(blocked_inv, trino)\n", "\n", "\n", "blocked_inv = blocked_inv()"]}, {"cell_type": "code", "execution_count": null, "id": "25750ca2-09ef-4e9a-b844-a43b51dd08aa", "metadata": {}, "outputs": [], "source": ["blocked_inv.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "52b420b4-2b58-4dbd-af4c-6dec8da60ba1", "metadata": {}, "outputs": [], "source": ["be_blocked = blocked_inv.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"ttl_blocked_qty\": \"be_ttl_blocked_qty\",\n", "    }\n", ")\n", "be_blocked.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "951a0bc3-1a5a-4ea7-ac05-e87af8326027", "metadata": {}, "outputs": [], "source": ["adding_fe_blocked_details = pd.merge(\n", "    adding_be_inv_details, blocked_inv, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_blocked_details = pd.merge(\n", "    adding_fe_blocked_details,\n", "    be_blocked,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]] = (\n", "    adding_be_blocked_details[[\"ttl_blocked_qty\", \"be_ttl_blocked_qty\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_blocked_details[\"net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"actual_inv\"]\n", "        - adding_be_blocked_details[\"ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "adding_be_blocked_details[\"be_net_inventory\"] = np.where(\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    )\n", "    < 0,\n", "    0,\n", "    (\n", "        adding_be_blocked_details[\"be_actual_inv\"]\n", "        - adding_be_blocked_details[\"be_ttl_blocked_qty\"]\n", "    ),\n", ")\n", "\n", "adding_be_inv_details = pd.DataFrame()\n", "blocked_inv = pd.DataFrame()\n", "be_blocked = pd.DataFrame()\n", "adding_fe_blocked_details = pd.DataFrame()\n", "\n", "adding_be_blocked_details.head(1)"]}, {"cell_type": "markdown", "id": "8deda81b-48e7-4e70-8bef-6ed1367ec0af", "metadata": {}, "source": ["# pending put-away"]}, {"cell_type": "code", "execution_count": null, "id": "1a14c2ba-e6ba-41d6-95dd-ea4bbf1a6521", "metadata": {}, "outputs": [], "source": ["def pen_put():\n", "    pen_put = f\"\"\"\n", "    \n", "    with\n", "    variant_details as\n", "        (select * from rpc.product_product),\n", "    \n", "    pp as\n", "        (select * from ims.ims_good_inventory),\n", "    \n", "    final as\n", "        (select\n", "            vd.item_id,\n", "            outlet_id as hot_outlet_id,\n", "            sum(quantity) as pending_putaway\n", "            \n", "                from pp\n", "                \n", "                    join\n", "                        variant_details vd on vd.variant_id = pp.variant_id\n", "                        and vd.lake_active_record\n", "                        \n", "                        where\n", "                            pp.active = 1\n", "                            and pp.lake_active_record\n", "                            and pp.inventory_update_type_id in (28,76)\n", "                            and vd.item_id in {item_id_list}\n", "                            and pp.quantity > 0\n", "                            \n", "                            group by 1,2\n", "        )\n", "        \n", "            select * from final\n", "    \n", "    \"\"\"\n", "    return read_sql_query(pen_put, trino)\n", "\n", "\n", "pen_put = pen_put()"]}, {"cell_type": "code", "execution_count": null, "id": "2884e239-f48b-45f0-9864-f95af9e5205e", "metadata": {}, "outputs": [], "source": ["pen_put.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "4d8662b3-c793-4832-981b-1a17b47c4448", "metadata": {}, "outputs": [], "source": ["be_pen_put = pen_put.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"pending_putaway\": \"be_pending_putaway\",\n", "    }\n", ")\n", "be_pen_put.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "6b8f99ba-8f96-47f0-a468-14b8a3c3cb75", "metadata": {}, "outputs": [], "source": ["adding_fe_pen_putaway_details = pd.merge(\n", "    adding_be_blocked_details, pen_put, on=[\"item_id\", \"hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.merge(\n", "    adding_fe_pen_putaway_details,\n", "    be_pen_put,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]] = (\n", "    adding_be_pen_putaway_details[[\"pending_putaway\", \"be_pending_putaway\"]]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_blocked_details = pd.DataFrame()\n", "pen_put = pd.DataFrame()\n", "be_pen_put = pd.DataFrame()\n", "adding_fe_pen_putaway_details = pd.DataFrame()\n", "\n", "adding_be_pen_putaway_details.head(1)"]}, {"cell_type": "markdown", "id": "beb1f2f1-da5b-46fc-a7d9-0d3ffc8e171f", "metadata": {}, "source": ["# open sto details"]}, {"cell_type": "code", "execution_count": null, "id": "bb41b1a0-c75b-4586-8a02-b3be793947fa", "metadata": {}, "outputs": [], "source": ["def open_sto():\n", "    open_sto = f\"\"\"\n", "    \n", "with\n", "outlet_details as\n", "    (select * from supply_etls.outlet_details),\n", "\n", "item_details as\n", "    (select * from supply_etls.item_details),\n", "\n", "variant_details as\n", "    (select * from rpc.product_product\n", "        where\n", "            lake_active_record\n", "    ),\n", "\n", "sto_details as\n", "    (select * from ims.ims_sto_details\n", "        where \n", "            created_at >= current_date - interval '30' day\n", "            and \n", "                lake_active_record\n", "    ),\n", "\n", "sto_item_details as\n", "    (select * from po.sto_items\n", "        where \n", "            created_at >= current_date - interval '30' day\n", "            and\n", "                lake_active_record\n", "    ),\n", "\n", "invoice_details as\n", "    (select * from pos.pos_invoice\n", "        where \n", "            insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "            and\n", "                lake_active_record\n", "            and \n", "                invoice_type_id in (5,14,16)\n", "            and \n", "                grofers_order_id is not null\n", "            and \n", "                grofers_order_id != ''\n", "    ),\n", "\n", "invoice_item_details as\n", "    (select * from pos.pos_invoice_product_details\n", "        where \n", "            insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "            and\n", "                lake_active_record\n", "    ),\n", "\n", "final_invoice_details as\n", "    (select \n", "        cast(id.grofers_order_id as int) as sto_id,\n", "        vd.item_id,\n", "        min(iid.pos_timestamp + interval '330' minute) as invoice_timestamp,\n", "        sum(iid.quantity) as billed_quantity\n", "\n", "            from invoice_details id\n", "\n", "                join\n", "                    invoice_item_details iid on iid.invoice_id = id.id\n", "\n", "                join\n", "                    variant_details vd on vd.variant_id = iid.variant_id\n", "\n", "                    group by 1,2\n", "    ),\n", "\n", "grn_detials as\n", "    (select * from ims.ims_inward_invoice\n", "        where\n", "            insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "            and \n", "                lake_active_record\n", "    ),\n", "\n", "grn_item_details as\n", "    (select * from ims.ims_inventory_stock_details\n", "        where\n", "            insert_ds_ist >= cast(current_date - interval '30' day as varchar)\n", "            and \n", "                lake_active_record\n", "    ),\n", "\n", "final_grn_details as\n", "    (select\n", "        cast(id.grofers_order_id as int) as sto_id,\n", "        vd.item_id,\n", "        min(gid.created_at + interval '330' minute) as grn_timestamp,\n", "        sum(gid.\"delta\") as grn_quantity\n", "\n", "            from grn_item_details gid\n", "\n", "                join\n", "                    grn_detials gd on gd.grn_id = gid.grn_id\n", "\n", "                join\n", "                    variant_details vd on vd.variant_id = gid.variant_id\n", "\n", "                join\n", "                    invoice_details id on id.invoice_id = gd.vendor_invoice_id\n", "\n", "                    group by 1,2\n", "    ),\n", "\n", "discrepancy_details as\n", "    (select * from pos.discrepancy_note\n", "        where \n", "            (created_at >= current_date - interval '30' day)\n", "            and \n", "                lake_active_record\n", "    ),\n", "\n", "discrepancy_item_details as\n", "    (select * from pos.discrepancy_note_product_detail\n", "        where \n", "            lake_active_record\n", "    ),\n", "\n", "final_discrepancy_details as\n", "    (select\n", "        cast(id.grofers_order_id as int) as sto_id,\n", "        item_id,\n", "        min(dd.created_at + interval '330' minute) as discrepancy_timestamp,\n", "        sum(did.quantity) as discrepancy_quantity\n", "\n", "            from discrepancy_details dd\n", "\n", "                join\n", "                    discrepancy_item_details did on did.dn_id_id = dd.id\n", "\n", "                join\n", "                    (select upc, item_id from variant_details group by 1,2) vd on vd.upc = did.upc_id\n", "\n", "                join\n", "                    invoice_details id on id.invoice_id = dd.vendor_invoice_id\n", "\n", "                        group by 1,2\n", "    ),\n", "\n", "adding_all as\n", "    (select\n", "        date(sd.created_at + interval '330' minute) as date_,\n", "        sd.outlet_id as be_inv_outlet_id,\n", "        sd.merchant_outlet_id as outlet_id,\n", "\n", "        sd.sto_id,\n", "        sid.item_id,\n", "\n", "        sid.reserved_quantity as created_quantity,\n", "        invoice_timestamp,\n", "        coalesce(fid.billed_quantity,0) as billed_quantity,\n", "        grn_timestamp,\n", "        coalesce(fgd.grn_quantity,0) as grn_quantity,\n", "        discrepancy_timestamp,\n", "        coalesce(fdd.discrepancy_quantity,0) as discrepancy_quantity,\n", "        \n", "        case\n", "            when (sid.reserved_quantity - coalesce(fid.billed_quantity,0)) < 1 then 0\n", "            else (sid.reserved_quantity - coalesce(fid.billed_quantity,0))\n", "        end as open_sto_quantity,\n", "\n", "        case\n", "            when (coalesce(fid.billed_quantity,0) - (coalesce(fgd.grn_quantity,0) + coalesce(fdd.discrepancy_quantity,0))) < 1 then 0\n", "            else (coalesce(fid.billed_quantity,0) - (coalesce(fgd.grn_quantity,0) + coalesce(fdd.discrepancy_quantity,0)))\n", "        end as in_transit_open_sto_quantity,\n", "\n", "        case \n", "            when ((sid.reserved_quantity) <= (coalesce(fgd.grn_quantity,0) + coalesce(fdd.discrepancy_quantity,0))) then 'expired'\n", "            when sd.sto_state in (3,4) then 'expired'\n", "        else 'open' end as status\n", "\n", "            from sto_details sd\n", "\n", "                join\n", "                    sto_item_details sid on sid.sto_id = sd.sto_id\n", "\n", "                left join\n", "                    final_invoice_details fid on fid.sto_id = sd.sto_id and fid.item_id = sid.item_id\n", "\n", "                left join\n", "                    final_grn_details fgd on fgd.sto_id = sd.sto_id and fgd.item_id = sid.item_id\n", "\n", "                left join\n", "                    final_discrepancy_details fdd on fdd.sto_id = sd.sto_id and fdd.item_id = sid.item_id\n", "    ),\n", "\n", "final_view as\n", "    (select\n", "        aa.outlet_id,\n", "        od.facility_id,\n", "        od.facility_name,\n", "        aa.item_id,\n", "        id.item_name,\n", "        \n", "        sum(open_sto_quantity) as open_sto_quantity,\n", "        sum(in_transit_open_sto_quantity) as in_transit_open_sto_quantity,\n", "        sum(open_sto_quantity + in_transit_open_sto_quantity) as total_open_sto_quantity\n", "\n", "            from adding_all aa\n", "            \n", "                join\n", "                    outlet_details od on od.inv_outlet_id = aa.outlet_id\n", "                \n", "                join\n", "                    item_details id on id.item_id = aa.item_id\n", "\n", "                where\n", "                    status = 'open'\n", "\n", "                    group by 1,2,3,4,5\n", "    ),\n", "\n", "final as\n", "    (select\n", "        outlet_id as hot_outlet_id,\n", "        --facility_id,\n", "        --facility_name,\n", "        item_id,\n", "        --item_name,\n", "        open_sto_quantity,\n", "        in_transit_open_sto_quantity\n", "        \n", "            from final_view\n", "            \n", "                where\n", "                    total_open_sto_quantity > 0\n", "                and item_id in {item_id_list}\n", "    )\n", "    \n", "        select * from final\n", "\n", "\n", "\n", "    \n", "    \"\"\"\n", "    return read_sql_query(open_sto, trino)\n", "\n", "\n", "open_sto = open_sto()"]}, {"cell_type": "code", "execution_count": null, "id": "8275484a-8d22-4e63-853e-c05d1a87b1ad", "metadata": {}, "outputs": [], "source": ["open_sto.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "5ec3678d-391f-432e-a7a6-c11455e32fa6", "metadata": {}, "outputs": [], "source": ["be_open_sto = open_sto.rename(\n", "    columns={\n", "        \"hot_outlet_id\": \"be_inv_outlet_id\",\n", "        \"open_sto_quantity\": \"be_open_sto_quantity\",\n", "        \"in_transit_open_sto_quantity\": \"be_in_transit_open_sto_quantity\",\n", "    }\n", ")\n", "be_open_sto.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "174409b0-a2ce-4212-ba85-36f37fa0154b", "metadata": {}, "outputs": [], "source": ["adding_fe_open_sto_details = pd.merge(\n", "    adding_be_pen_putaway_details,\n", "    open_sto,\n", "    on=[\"item_id\", \"hot_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_open_sto_details = pd.merge(\n", "    adding_fe_open_sto_details,\n", "    be_open_sto,\n", "    on=[\"item_id\", \"be_inv_outlet_id\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_be_open_sto_details[\n", "    [\n", "        \"open_sto_quantity\",\n", "        \"in_transit_open_sto_quantity\",\n", "        \"be_open_sto_quantity\",\n", "        \"be_in_transit_open_sto_quantity\",\n", "    ]\n", "] = (\n", "    adding_be_open_sto_details[\n", "        [\n", "            \"open_sto_quantity\",\n", "            \"in_transit_open_sto_quantity\",\n", "            \"be_open_sto_quantity\",\n", "            \"be_in_transit_open_sto_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_pen_putaway_details = pd.DataFrame\n", "open_sto = pd.DataFrame\n", "be_open_sto = pd.DataFrame\n", "adding_fe_open_sto_details = pd.DataFrame\n", "\n", "adding_be_open_sto_details.head(1)"]}, {"cell_type": "markdown", "id": "5fbf73bf-2763-4eb8-b7fb-36d1576a5cc0", "metadata": {}, "source": ["# open po details"]}, {"cell_type": "code", "execution_count": null, "id": "7f8f26a6-88f0-4ae0-9aa4-c8310560c139", "metadata": {}, "outputs": [], "source": ["def po_details():\n", "    po_details = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details),\n", "\n", "    item_details as\n", "        (select * from supply_etls.item_details),\n", "\n", "    assortment as\n", "        (select * from interim.festival_final_qty),\n", "\n", "    final_assortment as\n", "        (select\n", "            cut_off_date,\n", "            event_start_date,\n", "            event_end_date,\n", "            hot_outlet_id,\n", "            item_id\n", "\n", "                from assortment\n", "\n", "                    group by 1,2,3,4,5\n", "        ),\n", "\n", "    po_details as\n", "        (select * from po.purchase_order),\n", "\n", "    final_po_details as\n", "        (select * from po_details\n", "            where created_at >= (current_date - interval '62' day)\n", "            and lake_active_record\n", "            and active = 1\n", "        ),\n", "\n", "    po_item_details as\n", "        (select * from po.purchase_order_items),\n", "\n", "    final_po_item_details as\n", "        (select * from po_item_details\n", "            where created_at >= (current_date - interval '62' day)\n", "            and lake_active_record\n", "        ),\n", "\n", "    po_schedule_details as\n", "        (select * from po.po_schedule),\n", "\n", "    final_po_schedule_details as\n", "        (select * from po_schedule_details\n", "            where\n", "                created_at >= current_date - interval '62' day\n", "                and lake_active_record\n", "                and active = 1\n", "        ),\n", "\n", "    po_status as\n", "        (select * from po.purchase_order_status),\n", "\n", "    final_po_status as\n", "        (select * from po_status\n", "            where created_at >= (current_date - interval '62' day) and\n", "            lake_active_record = true\n", "        ),\n", "\n", "    grn_details as\n", "        (select * from po.po_grn),\n", "\n", "    final_grn_details as\n", "        (select * from grn_details\n", "            where insert_ds_ist >= cast(current_date - interval '62' day as varchar)\n", "            and lake_active_record\n", "        ),\n", "\n", "    max_grn_details as\n", "        (select \n", "            po_id,\n", "            date(max(insert_ds_ist)) as po_grn_date\n", "\n", "                from final_grn_details\n", "\n", "                    group by 1\n", "        ),\n", "\n", "    final_po_grn_details as\n", "        (select\n", "            po_id,\n", "            item_id,\n", "            sum(quantity) as grn_quantity,\n", "            sum((quantity * 1.00) * landing_price) as grn_value\n", "\n", "                from final_grn_details\n", "\n", "                    group by 1,2\n", "        ),\n", "\n", "    adding as\n", "        (select \n", "            od.city_name,\n", "            od.facility_id,\n", "            fpd.outlet_id as be_hot_outlet_id,\n", "            od.facility_name,\n", "            fpd.po_number,\n", "\n", "            fa.event_start_date,\n", "            fa.event_end_date,\n", "            fa.cut_off_date,\n", "\n", "            date(fpd.created_at + interval '330' minute) as po_issue_date,\n", "            date(fpd.expiry_date + interval '330' minute) as po_expiry_date,\n", "            date(case when (schedule_date_time + interval '330' minute) is null then (fpd.expiry_date + interval '330' minute) else (schedule_date_time + interval '330' minute) end) as po_scheduled_date,\n", "            (case when (schedule_date_time + interval '330' minute) is not null then 1 else 0 end) as schedule_flag,\n", "            fpid.item_id,\n", "            id.item_name,\n", "            id.weight_in_kg as uom,\n", "            id.l0_category,\n", "            fpd.vendor_id,\n", "            fpd.vendor_name,\n", "            id.manufacturer_id,\n", "            id.manufacturer_name,\n", "            fpid.units_ordered as po_quantity,\n", "            coalesce(fpgd.grn_quantity,0) as grn_quantity,\n", "            (case \n", "                when (fpid.units_ordered - coalesce(fpgd.grn_quantity,0)) < 0 then 0\n", "                else (fpid.units_ordered - coalesce(fpgd.grn_quantity,0)) end\n", "            ) as open_po_quantity\n", "\n", "                from final_po_details fpd\n", "\n", "                    join\n", "                        final_po_item_details fpid on fpid.po_id = fpd.id\n", "\n", "                    join\n", "                        final_po_status fps on fps.po_id = fpd.id\n", "\n", "                    left join\n", "                        final_po_schedule_details fpsd on fpsd.po_id_id = fpd.id\n", "\n", "                    left join\n", "                        final_po_grn_details fpgd on fpgd.item_id = fpid.item_id and fpgd.po_id = fpid.po_id\n", "\n", "                    left join\n", "                        max_grn_details mgd on mgd.po_id = fpd.id\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = fpd.outlet_id\n", "\n", "                    join\n", "                        item_details id on id.item_id = fpid.item_id \n", "                        and id.handling_type = 'Non Packaging Material'\n", "                        and id.assortment_type = 'Packaged Goods'\n", "\n", "                    join\n", "                        final_assortment fa on fa.item_id = fpid.item_id and fa.hot_outlet_id = fpd.outlet_id\n", "\n", "                        where \n", "                            (fps.po_state_id in (2,3,13,14,15) and (fps.po_state_id in (2,3,13,14,15) or fpd.is_multiple_grn = 1))\n", "                            and (fps.po_state_id not in (1,4,5,6,7,8,10))\n", "        ),\n", "\n", "    be_unscheduled_count as\n", "        (select\n", "            event_start_date,\n", "            event_end_date,\n", "            be_hot_outlet_id,\n", "            count(distinct po_number) as total_po_count,\n", "            count(distinct case when schedule_flag = 1 then po_number end) as unschedule_po_count,\n", "            sum(case when schedule_flag = 1 then open_po_quantity else 0 end) as unschedule_po_quantity\n", "\n", "                from adding\n", "\n", "                    where \n", "                        (cut_off_date >= po_scheduled_date)\n", "                        and open_po_quantity > 0\n", "\n", "                        group by 1,2,3\n", "        ),\n", "\n", "    final_view as\n", "        (select\n", "            a.event_start_date,\n", "            a.event_end_date,\n", "            a.be_hot_outlet_id,\n", "            item_id,\n", "            sum(open_po_quantity) as open_po_quantity,\n", "            sum(case \n", "                    when schedule_flag = 1 then open_po_quantity \n", "                    else 0 end\n", "                ) as scheduled_po_quantity,\n", "\n", "            buc.total_po_count,\n", "            buc.unschedule_po_count,\n", "            buc.unschedule_po_quantity\n", "\n", "\n", "                from adding a\n", "\n", "                    join\n", "                        be_unscheduled_count buc on buc.be_hot_outlet_id = a.be_hot_outlet_id\n", "                        and buc.event_start_date = a.event_start_date\n", "                        and buc.event_end_date = a.event_end_date\n", "\n", "                        where\n", "                            (a.cut_off_date >= po_scheduled_date)\n", "                            and open_po_quantity > 0\n", "\n", "                            group by 1,2,3,4,7,8,9\n", "        )\n", "\n", "            select * from final_view\n", "    \n", "    \"\"\"\n", "    return read_sql_query(po_details, trino)\n", "\n", "\n", "po_details = po_details()"]}, {"cell_type": "code", "execution_count": null, "id": "0687559f-a0cd-4018-bd95-d2c34ab67754", "metadata": {}, "outputs": [], "source": ["po_details.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "335f2eb2-abaf-45f6-a0b5-f4c4765fcc9b", "metadata": {}, "outputs": [], "source": ["adding_po_details = pd.merge(\n", "    adding_be_open_sto_details,\n", "    po_details,\n", "    on=[\"item_id\", \"be_hot_outlet_id\", \"event_start_date\", \"event_end_date\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_po_details[\n", "    [\n", "        \"open_po_quantity\",\n", "        \"scheduled_po_quantity\",\n", "        \"total_po_count\",\n", "        \"unschedule_po_count\",\n", "        \"unschedule_po_quantity\",\n", "    ]\n", "] = (\n", "    adding_po_details[\n", "        [\n", "            \"open_po_quantity\",\n", "            \"scheduled_po_quantity\",\n", "            \"total_po_count\",\n", "            \"unschedule_po_count\",\n", "            \"unschedule_po_quantity\",\n", "        ]\n", "    ]\n", "    .fillna(0)\n", "    .astype(int)\n", ")\n", "\n", "adding_be_open_sto_details = pd.DataFrame()\n", "po_details = pd.DataFrame()\n", "\n", "adding_po_details.head()"]}, {"cell_type": "markdown", "id": "78c258a5-fbf7-4e44-a495-fd589f25f782", "metadata": {}, "source": ["# sales details"]}, {"cell_type": "code", "execution_count": null, "id": "58d04efe-df14-4817-858a-19cf2727e189", "metadata": {}, "outputs": [], "source": ["def sales():\n", "    sales = \"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details),\n", "\n", "    item_details as\n", "        (select * from supply_etls.item_details),\n", "\n", "    assortment as\n", "        (select * from interim.festival_final_qty),\n", "\n", "    final_assortment as\n", "        (select\n", "            event_start_date,\n", "            event_end_date,\n", "            hot_outlet_id,\n", "            item_id\n", "\n", "                from assortment\n", "\n", "                    group by 1,2,3,4\n", "        ),\n", "\n", "    tag_mapping as\n", "        (select * from rpc.item_outlet_tag_mapping),\n", "\n", "    final_tea_taggings as\n", "        (select * from tag_mapping\n", "            where \n", "                active = 1 and\n", "                lake_active_record and\n", "                tag_type_id = 8\n", "        ),\n", "\n", "    ars_mapping as\n", "        (select * from po.bulk_facility_outlet_mapping),\n", "\n", "    final_ars_mapping as\n", "        (select * from ars_mapping\n", "            where\n", "                active and \n", "                lake_active_record\n", "        ),\n", "\n", "    active_assortment_adding as\n", "        (select \n", "            fa.event_start_date,\n", "            fa.event_end_date,\n", "            ftt.item_id,\n", "            ftt.outlet_id,\n", "            cast(ftt.tag_value as int) as be_hot_outlet_id\n", "\n", "                from final_tea_taggings ftt\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = ftt.outlet_id and od.taggings = 'fe'\n", "\n", "                    join\n", "                        outlet_details od2 on od2.hot_outlet_id = cast(ftt.tag_value as int) and od2.taggings = 'be'\n", "\n", "                    join\n", "                        final_ars_mapping fam on fam.facility_id = od2.facility_id and fam.outlet_id = ftt.outlet_id\n", "\n", "                    join\n", "                        final_assortment fa on fa.item_id = ftt.item_id and fa.hot_outlet_id = cast(ftt.tag_value as int)\n", "\n", "        ),\n", "\n", "    sub_order_details as\n", "        (select * from oms_bifrost.oms_suborder),\n", "\n", "    final_sub_order_details as\n", "        (select\n", "            date(insert_ds_ist) as order_date,\n", "            id as sub_order_id,\n", "            order_id\n", "\n", "                from sub_order_details\n", "\n", "                    where\n", "                        (insert_ds_ist >= cast(current_date - interval '45' day as varchar))\n", "                        and lake_active_record\n", "        ),\n", "\n", "    oms_order_details as\n", "        (select * from oms_bifrost.oms_order),\n", "\n", "    final_oms_order_details as\n", "        (select\n", "            id as order_id,\n", "            cart_id,\n", "            current_status,\n", "            type as type_,\n", "            external_id as order_number\n", "\n", "                from oms_order_details\n", "\n", "                    where\n", "                        (insert_ds_ist >= cast(current_date - interval '45' day as varchar))\n", "                        and lake_active_record\n", "        ),\n", "\n", "    invoice_details as\n", "        (select * from pos.pos_invoice),\n", "\n", "    final_invoice_details as\n", "        (select \n", "            id as invoice_id,\n", "            grofers_order_id as sub_order_id,\n", "            outlet_id,\n", "            invoice_type_id,\n", "            invoice_id as invoice_number\n", "\n", "                from invoice_details\n", "\n", "                    where \n", "                        (insert_ds_ist >= cast(current_date - interval '45' day as varchar))\n", "                        and lake_active_record\n", "        ),\n", "\n", "    adding_invoice_details as\n", "        (select\n", "            od.order_date,\n", "            id.invoice_id,\n", "            id.invoice_number,\n", "            id.sub_order_id,\n", "            ood.cart_id,\n", "            od.order_id,\n", "            id.outlet_id,\n", "            ood.order_number,\n", "            id.invoice_type_id\n", "\n", "\n", "                from final_invoice_details id\n", "\n", "                    join\n", "                        final_sub_order_details od on cast(od.sub_order_id as varchar) = id.sub_order_id\n", "\n", "                    join\n", "                        final_oms_order_details ood on ood.order_id = od.order_id\n", "\n", "                        where \n", "                            id.invoice_type_id in (1,7,2,8) \n", "                            and ood.current_status = 'DELIVERED' \n", "                            and ood.type_ not like '%%Internal%%'\n", "        ),\n", "\n", "    invoice_item_details as\n", "        (select * from pos.pos_invoice_product_details),\n", "\n", "    final_invoice_item_details as\n", "        (select\n", "            invoice_id,\n", "            upc_id,\n", "            variant_id,\n", "            quantity,\n", "            unit_rate as mrp,\n", "            selling_price\n", "\n", "                from invoice_item_details\n", "\n", "                    where \n", "                        (insert_ds_ist >= cast(current_date - interval '45' day as varchar))\n", "                        and lake_active_record\n", "        ),\n", "\n", "    variant_details as\n", "        (select * from rpc.product_product\n", "            where\n", "                lake_active_record\n", "        ),\n", "\n", "    adding_item_invoice_details as\n", "        (select \n", "            aid.order_date,\n", "            aid.invoice_id,\n", "            aid.invoice_number,\n", "            aid.sub_order_id,\n", "            aid.cart_id,\n", "            aid.order_id,\n", "            aid.order_number,\n", "            aid.outlet_id,\n", "            aid.invoice_type_id,\n", "            vd.item_id,\n", "            fiid.upc_id,\n", "            fiid.variant_id,\n", "            fiid.quantity,\n", "            fiid.mrp,\n", "            fiid.selling_price\n", "\n", "                from final_invoice_item_details fiid\n", "\n", "                    join\n", "                        adding_invoice_details aid on aid.invoice_id = fiid.invoice_id\n", "\n", "                    join\n", "                        variant_details vd on vd.variant_id = fiid.variant_id\n", "        ),\n", "\n", "    return_details as\n", "        (select\n", "            cart_id,\n", "            item_id,\n", "            -- upc_id,\n", "            -- variant_id,\n", "            sum(quantity) as return_quantity,\n", "            sum(quantity * selling_price) as return_selling_price\n", "\n", "                from adding_item_invoice_details\n", "\n", "                    where invoice_type_id in (2,8)\n", "\n", "                        group by 1,2\n", "        ),\n", "\n", "    final_sales as\n", "        (select\n", "            order_date as date_,\n", "            outlet_id,\n", "            item_id,\n", "            cart_id,\n", "            sum(quantity) as quantity,\n", "            sum(quantity * selling_price) as selling_price\n", "\n", "                from adding_item_invoice_details\n", "\n", "                    where invoice_type_id in (1,7)\n", "\n", "                        group by 1,2,3,4\n", "        ),\n", "\n", "    final_view as\n", "        (select\n", "            aaa.event_start_date,\n", "            aaa.event_end_date,\n", "            fs.date_,\n", "            fs.outlet_id,\n", "            aaa.be_hot_outlet_id,\n", "            fs.item_id,\n", "            (fs.quantity - coalesce(rd.return_quantity,0)) as sales_quantity,\n", "            (fs.selling_price - coalesce(rd.return_selling_price,0)) as sales_value\n", "\n", "                from final_sales fs\n", "\n", "                    left join\n", "                        return_details rd on rd.cart_id = fs.cart_id and rd.item_id = fs.item_id\n", "\n", "                    join\n", "                        active_assortment_adding aaa on aaa.item_id = fs.item_id and aaa.outlet_id = fs.outlet_id\n", "\n", "                        where\n", "                            (fs.date_ between (aaa.event_start_date - interval '3' day) and aaa.event_end_date)\n", "        )\n", "\n", "            select\n", "                event_start_date,\n", "                event_end_date,\n", "                outlet_id as hot_outlet_id,\n", "                item_id,\n", "                sum(sales_quantity) as total_sales_quantity,\n", "                sum(sales_value) as total_sales_value\n", "\n", "                    from final_view\n", "\n", "                        where sales_quantity > 0\n", "\n", "                            group by 1,2,3,4\n", "    \n", "    \"\"\"\n", "    return read_sql_query(sales, trino)\n", "\n", "\n", "sales = sales()"]}, {"cell_type": "code", "execution_count": null, "id": "a46b5e01-452c-49b3-b1ae-5261bd297c5b", "metadata": {}, "outputs": [], "source": ["sales.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ba172b56-b01b-46ed-aa05-336c6c7b7790", "metadata": {}, "outputs": [], "source": ["adding_sales_details = pd.merge(\n", "    adding_po_details,\n", "    sales,\n", "    on=[\"item_id\", \"hot_outlet_id\", \"event_start_date\", \"event_end_date\"],\n", "    how=\"left\",\n", ")\n", "\n", "adding_sales_details[\"total_sales_quantity\"] = (\n", "    adding_sales_details[\"total_sales_quantity\"].fillna(0).astype(int)\n", ")\n", "\n", "adding_sales_details[\"total_sales_value\"] = (\n", "    adding_sales_details[\"total_sales_value\"].fillna(0).astype(float)\n", ")\n", "\n", "adding_po_details = pd.DataFrame()\n", "sales = pd.DataFrame()\n", "\n", "adding_sales_details.head(1)"]}, {"cell_type": "markdown", "id": "8a2c1da4-dd6c-4d44-9e1c-2c0b291e57dd", "metadata": {}, "source": ["# po fill rate"]}, {"cell_type": "code", "execution_count": null, "id": "c098bed1-2b1e-446c-b3e6-d5f81651aee1", "metadata": {}, "outputs": [], "source": ["def po_fil_rate():\n", "    po_fil_rate = f\"\"\"\n", "    \n", "    with\n", "    outlet_details as\n", "        (select * from supply_etls.outlet_details),\n", "\n", "    item_details as\n", "        (select * from supply_etls.item_details),\n", "\n", "    po_details as\n", "        (select * from po.purchase_order),\n", "\n", "    final_po_details as\n", "        (select * from po_details\n", "            where \n", "                (created_at >= current_date - interval '62' day)\n", "                and active = 1 \n", "                and lake_active_record\n", "                and po_type_id != 11\n", "        ),\n", "\n", "    po_item_details as\n", "        (select * from po.purchase_order_items),\n", "\n", "    final_po_item_details as\n", "        (select * from po_item_details\n", "            where \n", "                (created_at >= current_date - interval '62' day)\n", "                and active = 1 \n", "                and lake_active_record\n", "        ),\n", "\n", "    po_status as\n", "        (select * from po.purchase_order_status),\n", "\n", "    final_po_status as\n", "        (select * from po_status\n", "            where \n", "                (created_at >= current_date - interval '62' day)\n", "                and lake_active_record\n", "        ),\n", "\n", "    po_grn_details as\n", "        (select * from po.po_grn),\n", "\n", "    final_po_grn_details as\n", "        (select * from po_grn_details\n", "            where\n", "                insert_ds_ist >= cast(current_date - interval '60' day as varchar)\n", "                and lake_active_record\n", "        ),\n", "\n", "    final_grn_details as\n", "        (select po_id,\n", "            item_id,\n", "            sum(quantity) as grn_quantity,\n", "            avg(landing_price) as avg_lp\n", "\n", "                from final_po_grn_details\n", "\n", "                    group by 1,2\n", "        ),\n", "\n", "    last_grn_details as\n", "        (select po_id,\n", "            max(created_at + interval '330' minute) as last_grn\n", "\n", "                from final_po_grn_details\n", "\n", "                    group by 1\n", "        ),\n", "\n", "    adding as\n", "        (select \n", "            od.city_name,\n", "            od.facility_id,\n", "            fpd.outlet_id,\n", "            od.facility_name,\n", "            fpd.po_number,\n", "            date(fpd.created_at + interval '330' minute) as po_issue_date,\n", "            date(fpd.expiry_date + interval '330' minute) as po_expiry_date,\n", "            case when lgd.last_grn is null then (fpd.expiry_date + interval '330' minute) else lgd.last_grn end as last_grn,\n", "            fpid.item_id,\n", "            id.item_name,\n", "            id.l0_category,\n", "            id.l1_category,\n", "            id.l2_category,\n", "\n", "            fpd.vendor_id,\n", "            fpd.vendor_name,\n", "            id.manufacturer_id,\n", "            id.manufacturer_name,\n", "            fpid.units_ordered as po_quantity,\n", "            coalesce(fpgd.grn_quantity,0) as grn_quantity\n", "\n", "\n", "                from final_po_details fpd\n", "\n", "                    join\n", "                        final_po_item_details fpid on fpid.po_id = fpd.id\n", "\n", "                    join\n", "                        final_po_status fps on fps.po_id = fpd.id\n", "\n", "                    left join\n", "                        final_grn_details fpgd on fpgd.item_id = fpid.item_id and fpgd.po_id = fpid.po_id\n", "\n", "                    left join\n", "                        last_grn_details lgd on lgd.po_id = fpd.id\n", "\n", "                    join\n", "                        outlet_details od on od.hot_outlet_id = fpd.outlet_id\n", "\n", "                    join\n", "                        item_details id on id.item_id = fpid.item_id \n", "                        and id.handling_type = 'Non Packaging Material'\n", "                        and id.assortment_type = 'Packaged Goods'\n", "                        and id.item_id in {item_id_list}\n", "\n", "                        where (fps.po_state_id in (8) or (fps.po_state_id in (9) and fpd.is_multiple_grn != 1))\n", "        ),\n", "\n", "    ranking as\n", "        (select *, date(last_grn) as last_grn_date, \n", "            row_number() over (partition by outlet_id, item_id order by last_grn desc) as rnk\n", "\n", "                from adding\n", "        ),\n", "\n", "    final_view as\n", "        (select\n", "            outlet_id as be_hot_outlet_id,\n", "            item_id,\n", "            max(rnk) as po_count,\n", "            sum(case when rnk in (1,2,3) then (po_quantity) end) as last_3_po_quantity,\n", "            sum(case when rnk in (1,2,3) then (grn_quantity) end) as last_3_grn_quantity,\n", "\n", "            (sum(case when rnk in (1) then (grn_quantity * 1.00) end)/nullif(sum(case when rnk in (1) then (po_quantity) end),0)) as total_last1_fill_rate,\n", "            (sum(case when rnk in (1,2) then (grn_quantity * 1.00) end)/nullif(sum(case when rnk in (1,2) then (po_quantity) end),0)) as total_last2_fill_rate,\n", "            (sum(case when rnk in (1,2,3) then (grn_quantity * 1.00) end)/nullif(sum(case when rnk in (1,2,3) then (po_quantity) end),0)) as total_last3_fill_rate\n", "\n", "                from ranking\n", "\n", "                    where \n", "                        rnk < 4\n", "\n", "                        group by 1,2\n", "        )\n", "\n", "            select * from final_view\n", "    \"\"\"\n", "    return read_sql_query(po_fil_rate, trino)\n", "\n", "\n", "po_fil_rate = po_fil_rate()"]}, {"cell_type": "code", "execution_count": null, "id": "e894fb51-2d4b-481e-9dda-a9a6af831a54", "metadata": {}, "outputs": [], "source": ["po_fil_rate.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "9195dcbe-67ab-43c9-a59c-30eb47d9e6d5", "metadata": {}, "outputs": [], "source": ["adding_po_fill_rate = pd.merge(\n", "    adding_sales_details, po_fil_rate, on=[\"item_id\", \"be_hot_outlet_id\"], how=\"left\"\n", ")\n", "\n", "adding_po_fill_rate[\n", "    [\n", "        \"po_count\",\n", "        \"last_3_po_quantity\",\n", "        \"last_3_grn_quantity\",\n", "        \"total_last1_fill_rate\",\n", "        \"total_last2_fill_rate\",\n", "        \"total_last3_fill_rate\",\n", "    ]\n", "] = adding_po_fill_rate[\n", "    [\n", "        \"po_count\",\n", "        \"last_3_po_quantity\",\n", "        \"last_3_grn_quantity\",\n", "        \"total_last1_fill_rate\",\n", "        \"total_last2_fill_rate\",\n", "        \"total_last3_fill_rate\",\n", "    ]\n", "].astype(\n", "    float\n", ")\n", "\n", "adding_sales_details = pd.DataFrame()\n", "po_fil_rate = pd.DataFrame()\n", "\n", "adding_po_fill_rate.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "1722f71e-739d-4c8a-ba21-e9c49ba2806a", "metadata": {}, "outputs": [], "source": ["adding_po_fill_rate = adding_po_fill_rate[\n", "    [\n", "        \"insert_ds_ist\",\n", "        \"event_start_date\",\n", "        \"event_end_date\",\n", "        \"cut_off_date\",\n", "        \"assortment_type\",\n", "        \"event_name\",\n", "        \"city_name\",\n", "        \"hot_outlet_id\",\n", "        \"facility_id\",\n", "        \"facility_name\",\n", "        \"be_hot_outlet_id\",\n", "        \"be_inv_outlet_id\",\n", "        \"be_facility_id\",\n", "        \"be_facility_name\",\n", "        \"item_id\",\n", "        \"item_name\",\n", "        \"l0_category\",\n", "        \"l1_category\",\n", "        \"l2_category\",\n", "        \"p_type\",\n", "        \"planned_quantity\",\n", "        \"actual_inv\",\n", "        \"ttl_blocked_qty\",\n", "        \"net_inventory\",\n", "        \"pending_putaway\",\n", "        \"open_sto_quantity\",\n", "        \"in_transit_open_sto_quantity\",\n", "        \"be_actual_inv\",\n", "        \"be_ttl_blocked_qty\",\n", "        \"be_net_inventory\",\n", "        \"be_pending_putaway\",\n", "        \"be_open_sto_quantity\",\n", "        \"be_in_transit_open_sto_quantity\",\n", "        \"open_po_quantity\",\n", "        \"scheduled_po_quantity\",\n", "        \"total_po_count\",\n", "        \"unschedule_po_count\",\n", "        \"unschedule_po_quantity\",\n", "        \"total_sales_quantity\",\n", "        \"total_sales_value\",\n", "        \"po_count\",\n", "        \"last_3_po_quantity\",\n", "        \"last_3_grn_quantity\",\n", "        \"total_last1_fill_rate\",\n", "        \"total_last2_fill_rate\",\n", "        \"total_last3_fill_rate\",\n", "    ]\n", "]\n", "\n", "adding_po_fill_rate.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "ac779365-efac-42b5-b0a8-bc861fb2119a", "metadata": {}, "outputs": [], "source": ["adding_po_fill_rate[\"insert_ds_ist\"] = adding_po_fill_rate[\"insert_ds_ist\"].astype(\n", "    \"datetime64[ns]\"\n", ")\n", "adding_po_fill_rate[\"event_start_date\"] = adding_po_fill_rate[\n", "    \"event_start_date\"\n", "].astype(\"datetime64[ns]\")\n", "adding_po_fill_rate[\"event_end_date\"] = adding_po_fill_rate[\"event_end_date\"].astype(\n", "    \"datetime64[ns]\"\n", ")\n", "adding_po_fill_rate[\"cut_off_date\"] = adding_po_fill_rate[\"cut_off_date\"].astype(\n", "    \"datetime64[ns]\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b446ddc3-b3ac-4295-9ba9-9c25a16ec61f", "metadata": {}, "outputs": [], "source": ["adding_po_fill_rate.dtypes"]}, {"cell_type": "markdown", "id": "2d801982-bd5a-4693-86a9-99b11df9c9da", "metadata": {}, "source": ["# Redshift"]}, {"cell_type": "code", "execution_count": null, "id": "c9bbc389-2455-493d-9e33-80b97a709799", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\"name\": \"insert_ds_ist\", \"type\": \"datetime\", \"description\": \"table updated at\"},\n", "    {\"name\": \"event_start_date\", \"type\": \"date\", \"description\": \"item relate to\"},\n", "    {\"name\": \"event_end_date\", \"type\": \"date\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"cut_off_date\", \"type\": \"date\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"event_name\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"hot_outlet_id\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\n", "        \"name\": \"planned_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\", \"description\": \"specific date capacity\"},\n", "    {\n", "        \"name\": \"ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"in_transit_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_actual_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_in_transit_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_po_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"unschedule_po_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"unschedule_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_sales_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_sales_value\",\n", "        \"type\": \"float\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\"name\": \"po_count\", \"type\": \"float\", \"description\": \"specific date capacity\"},\n", "    {\n", "        \"name\": \"last_3_po_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"last_3_grn_quantity\",\n", "        \"type\": \"float\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_last1_fill_rate\",\n", "        \"type\": \"float\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_last2_fill_rate\",\n", "        \"type\": \"float\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_last3_fill_rate\",\n", "        \"type\": \"float\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4b17c392-25d9-46f4-a911-8ec6baa00954", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"metrics\",\n", "    \"table_name\": \"festive_inventory_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"assortment_type\", \"event_name\"],\n", "    \"sortkey\": [\"insert_ds_ist\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"rebuild\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"festive_inventory_metrics\",\n", "}\n", "\n", "pb.to_redshift(adding_po_fill_rate, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "markdown", "id": "9e242c93-f4b6-4469-b3c3-1ac50b281ca1", "metadata": {}, "source": ["# Trino"]}, {"cell_type": "code", "execution_count": null, "id": "a09908f8-d763-4d8b-8de0-483edc7b8a56", "metadata": {}, "outputs": [], "source": ["# adding_po_fill_rate.dtypes\n", "adding_po_fill_rate[\"insert_ds_ist\"] = adding_po_fill_rate[\"insert_ds_ist\"].astype(\n", "    \"datetime64[ns]\"\n", ")\n", "adding_po_fill_rate[\"event_start_date\"] = adding_po_fill_rate[\n", "    \"event_start_date\"\n", "].astype(\"datetime64[ns]\")\n", "adding_po_fill_rate[\"event_end_date\"] = adding_po_fill_rate[\"event_end_date\"].astype(\n", "    \"datetime64[ns]\"\n", ")\n", "adding_po_fill_rate[\"cut_off_date\"] = adding_po_fill_rate[\"cut_off_date\"].astype(\n", "    \"datetime64[ns]\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "609b53f2-a1b4-47b6-ad5f-50604ff5ee32", "metadata": {}, "outputs": [], "source": ["column_dtypes = [\n", "    {\n", "        \"name\": \"insert_ds_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"table updated at\",\n", "    },\n", "    {\"name\": \"event_start_date\", \"type\": \"date\", \"description\": \"item relate to\"},\n", "    {\"name\": \"event_end_date\", \"type\": \"date\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"cut_off_date\", \"type\": \"date\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"assortment_type\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"event_name\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"city_name\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"hot_outlet_id\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"facility_id\", \"type\": \"integer\", \"description\": \"stores outlet id\"},\n", "    {\"name\": \"facility_name\", \"type\": \"varchar\", \"description\": \"stores outlet id\"},\n", "    {\n", "        \"name\": \"be_hot_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_inv_outlet_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_facility_id\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_facility_name\",\n", "        \"type\": \"varchar\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\"name\": \"item_id\", \"type\": \"integer\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"item_name\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"l0_category\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"l1_category\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"l2_category\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\"name\": \"p_type\", \"type\": \"varchar\", \"description\": \"specific date capacity\"},\n", "    {\n", "        \"name\": \"planned_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\"name\": \"actual_inv\", \"type\": \"integer\", \"description\": \"specific date capacity\"},\n", "    {\n", "        \"name\": \"ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"in_transit_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_actual_inv\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_ttl_blocked_qty\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_net_inventory\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_pending_putaway\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"be_in_transit_open_sto_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"open_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"scheduled_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_po_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"unschedule_po_count\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"unschedule_po_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_sales_quantity\",\n", "        \"type\": \"integer\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_sales_value\",\n", "        \"type\": \"REAL\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\"name\": \"po_count\", \"type\": \"REAL\", \"description\": \"specific date capacity\"},\n", "    {\n", "        \"name\": \"last_3_po_quantity\",\n", "        \"type\": \"REAL\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"last_3_grn_quantity\",\n", "        \"type\": \"REAL\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_last1_fill_rate\",\n", "        \"type\": \"REAL\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_last2_fill_rate\",\n", "        \"type\": \"REAL\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "    {\n", "        \"name\": \"total_last3_fill_rate\",\n", "        \"type\": \"REAL\",\n", "        \"description\": \"specific date capacity\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "4d503739-8a28-4a2b-b654-970c4800d6d2", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"festive_inventory_metrics\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"item_id\", \"facility_id\", \"assortment_type\", \"event_name\"],\n", "    \"incremental_key\": \"insert_ds_ist\",\n", "    \"load_type\": \"truncate\",  # append, rebuild, truncate or upsert,\n", "    \"table_description\": \"festive_inventory_metrics\",\n", "}\n", "\n", "pb.to_trino(adding_po_fill_rate, **kwargs)\n", "\n", "print(\"final_base write complete\")"]}, {"cell_type": "code", "execution_count": null, "id": "bed95d19-b1a4-4741-99b9-1d17a6292a3b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}