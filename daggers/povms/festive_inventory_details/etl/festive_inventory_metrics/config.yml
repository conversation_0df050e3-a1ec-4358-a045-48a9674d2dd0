alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: festive_inventory_metrics
dag_type: etl
escalation_priority: low
executor:
  config:
    cpu:
      limit: 1
      request: 0.5
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    memory:
      limit: 4G
      request: 500M
    node_selectors:
      nodetype: airflow-dags-spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
namespace: povms
notebook:
  parameters: null
  retries: 1
owner:
  email: <EMAIL>
  slack_id: U05F1R698RL
path: povms/festive_inventory_details/etl/festive_inventory_metrics
paused: true
project_name: festive_inventory_details
schedule:
  end_date: '2024-01-10T00:00:00'
  interval: 0 */2 * * *
  start_date: '2023-12-21T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 3
pool: povms_pool
