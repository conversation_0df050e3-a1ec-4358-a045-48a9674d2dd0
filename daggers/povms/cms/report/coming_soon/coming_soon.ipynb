{"cells": [{"cell_type": "code", "execution_count": null, "id": "3d011487-a7c8-422b-a303-19e3f0807005", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "from tabulate import tabulate\n", "from tqdm.notebook import tqdm\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "64c1619d-3090-4c29-8fce-1f5525967714", "metadata": {"tags": []}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            df = pb.from_sheets(sheet_id, sheet_name)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "110287c4-101d-41ae-b36f-779868d897e1", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-data-inventory-pvt\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "4f1cc818-9f00-490e-991c-594291ff167e", "metadata": {}, "outputs": [], "source": ["def send_slack_alert(error_message):\n", "\n", "    slack_channel = from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "    slack_channel = (\n", "        slack_channel[slack_channel[\"alert\"] == \"alerting_anuvrat\"].reset_index().iloc[:, 2][0]\n", "    )\n", "    error_message = error_message + \"\\ncc <@U03RJ5FRXFC>\"\n", "\n", "    pb.send_slack_message(channel=slack_channel, text=error_message)"]}, {"cell_type": "code", "execution_count": null, "id": "f83ae21f-e164-44e3-9dff-a808e25c22eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3da8255d-639f-44cb-a666-1e8e4535849e", "metadata": {}, "outputs": [], "source": ["alert_slack_channel = from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "alert_slack_channel = (\n", "    alert_slack_channel[alert_slack_channel[\"alert\"] == \"alerting_anuvrat\"]\n", "    .reset_index()\n", "    .iloc[:, 2][0]\n", ")\n", "alert_slack_channel"]}, {"cell_type": "code", "execution_count": null, "id": "453cbe91-24d5-4648-84d8-be97a1723640", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "7d504e54-d994-4b09-a85e-50d1feb21785", "metadata": {}, "source": ["### coming soon"]}, {"cell_type": "code", "execution_count": null, "id": "19a32a0a-0caa-4cfc-bd06-498c8581d3d9", "metadata": {}, "outputs": [], "source": ["add_l2s = from_sheets(\"1ByGzXEuhlpBzLFRr_6vTpPnsYCAfUNg3Y9CXyoSes8Y\", \"Category data \")\n", "add_l2s = (\n", "    add_l2s[[\"L2_ID\", \"Coming soon needed (Yes/No)\"]]\n", "    .drop_duplicates()\n", "    .reset_index()\n", "    .drop(columns={\"index\"})\n", ")\n", "add_l2s = tuple(\n", "    list(add_l2s[add_l2s[\"Coming soon needed (Yes/No)\"] == \"Yes\"][\"L2_ID\"].astype(int).unique())\n", ")\n", "# add_l2s"]}, {"cell_type": "code", "execution_count": null, "id": "e61d8ebd-89ce-4f16-ba81-8af5031abfda", "metadata": {}, "outputs": [], "source": ["remove_pids = from_sheets(\"1ByGzXEuhlpBzLFRr_6vTpPnsYCAfUNg3Y9CXyoSes8Y\", \"Remove PIDS\")\n", "remove_pids = remove_pids[[\"product_id\"]].drop_duplicates().reset_index(drop=True)\n", "remove_pids = tuple(list(remove_pids[\"product_id\"].astype(int).unique()) + [-1] + [-2])\n", "remove_pids"]}, {"cell_type": "code", "execution_count": null, "id": "eab649ee-07f5-433b-9acf-e4adeb0a2a55", "metadata": {}, "outputs": [], "source": ["add_pids = from_sheets(\"1ByGzXEuhlpBzLFRr_6vTpPnsYCAfUNg3Y9CXyoSes8Y\", \"Add PIDS\")\n", "add_pids[\"start_date\"] = pd.to_datetime(add_pids[\"start_date\"])\n", "add_pids[\"end_date\"] = add_pids[\"start_date\"] + pd.Timedel<PERSON>(days=7)\n", "add_pids[\"today_date\"] = pd.to_datetime(\n", "    (pd.Timestamp.today() + timed<PERSON>ta(hours=5.5)).strftime(\"%Y-%m-%d\")\n", ")\n", "add_pids = (\n", "    add_pids[add_pids[\"today_date\"].between(add_pids[\"start_date\"], add_pids[\"end_date\"])][\n", "        [\"product_id\"]\n", "    ]\n", "    .drop_duplicates()\n", "    .reset_index(drop=True)\n", ")\n", "add_pids = tuple(list(add_pids[\"product_id\"].astype(int).unique()) + [-1] + [-2])\n", "add_pids"]}, {"cell_type": "code", "execution_count": null, "id": "362c24b5-e502-463e-ab55-954c59c36166", "metadata": {}, "outputs": [], "source": ["coming_soon = f\"\"\"\n", "\n", "            with outlet_details as (\n", "                select hot_outlet_id, inv_outlet_id, hot_outlet_id = inv_outlet_id, facility_id, facility_name, business_type_id\n", "                from supply_etls.outlet_details\n", "                where ars_check = 1\n", "            ),\n", "\n", "            mid_od as (\n", "                select pos_outlet_id as outlet_id, frontend_merchant_id as merchant_id\n", "                from dwh.dim_merchant_outlet_facility_mapping\n", "                where is_pos_outlet_active = 1\n", "                and is_mapping_enabled\n", "                and is_current\n", "                group by 1,2\n", "            ),\n", "\n", "            item_details as (\n", "                select item_id, item_name, l0_category as l0, l1_category as l1, l2_category as l2, p_type as ptype\n", "                from supply_etls.item_details\n", "                where l2_id in {add_l2s}\n", "            ),\n", "\n", "            ipm as (\n", "                select item_id, product_id\n", "                from dwh.dim_item_product_offer_mapping\n", "                where is_current\n", "                and is_offer = False\n", "            ),\n", "\n", "            pfma as (\n", "                select facility_id, item_id, master_assortment_substate_id\n", "                from rpc.product_facility_master_assortment\n", "                where lake_active_record\n", "                and master_assortment_substate_id in (1,3) and active = 1\n", "            ),\n", "\n", "            iotm as (\n", "                select cast(tag_value as int) as backend_outlet_id, od.inv_outlet_id as be_inv_outlet_id, od.facility_id as backend_facility_id, od.facility_name as backend_facility_name, outlet_id, o.facility_id, item_id\n", "                from rpc.item_outlet_tag_mapping ii\n", "                inner join outlet_details od on cast(od.hot_outlet_id as varchar) = tag_value\n", "                inner join outlet_details o on o.hot_outlet_id = outlet_id\n", "                where lake_active_record and tag_type_id = 8 and active = 1\n", "            ),\n", "\n", "            actual_inv_base as (\n", "                select cast(si.item_id as int) as item_id, cast(outlet_id as int) as outlet_id, max(si.updated_at + interval '330' minute) as inv_updated_at_ist, sum(quantity) as net_inv\n", "                from dynamodb.blinkit_store_inventory_service_oi_rt_view_v2 si\n", "                where outlet_id not in ('4146')\n", "                and state = 'GOOD'\n", "                group by 1,2\n", "            ),\n", "\n", "            blocked_inv_base as (\n", "                select cast(item_id as int) as item_id, cast(outlet_id as int) as outlet_id, max(si.updated_at + interval '330' minute) as updated_at_ist, sum(quantity) as blocked_inv\n", "                from dynamodb.blinkit_store_inventory_service_blk_rt_view_v2 si\n", "                where outlet_id not in ('4146') and status = 'BLOCKED'\n", "                group by 1,2\n", "            ),\n", "\n", "            item_inv_base as (\n", "                select iib.outlet_id, iib.item_id, inv_updated_at_ist, (case when net_inv - coalesce(blocked_inv,0) > 0 then net_inv - coalesce(blocked_inv,0) else 0 end) as net_inv\n", "                from actual_inv_base iib\n", "                left join blocked_inv_base bib on bib.item_id = iib.item_id and bib.outlet_id = iib.outlet_id\n", "            ),\n", "\n", "\n", "            iii as (\n", "                select outlet_id, item_id, quantity as actual_quantity\n", "                from ims.ims_item_inventory\n", "                where lake_active_record\n", "                and active = 1\n", "            ),\n", "\n", "            iibi as (\n", "                select outlet_id, item_id, sum(quantity) as blocked_quantity\n", "                from ims.ims_item_blocked_inventory\n", "                where lake_active_record and active = 1\n", "                group by 1,2\n", "            ),\n", "\n", "            be_inv as (\n", "                select iii.outlet_id, iii.item_id, \n", "                        case when (actual_quantity - coalesce(blocked_quantity,0)) < 0 then 0 else actual_quantity - coalesce(blocked_quantity,0) end as be_inv\n", "                from iii\n", "                left join iibi on iibi.item_id = iii.item_id and iibi.outlet_id = iii.outlet_id\n", "            ),\n", "\n", "\n", "            po as (\n", "                select id as po_id, po_number, created_by, updated_by, created_at, expiry_date, is_multiple_grn, po_type_id, outlet_id, vendor_id, vendor_name, meta, delivery_type, destination_entity_vendor_id\n", "                from po.purchase_order\n", "                where created_at >= current_date - interval '20' day\n", "                and active = 1\n", "                and lake_active_record\n", "                and po_type_id not in (9,11)\n", "            ),\n", "\n", "            poi as (\n", "                select po_id, run_id, item_id, upc, variant_id, name, uom_text, units_ordered, mrp, landing_rate, is_bucket_x, bucket_type, is_pl, is_critical, remaining_quantity\n", "                from po.purchase_order_items\n", "                where created_at >= current_date - interval '20' day\n", "                and lake_active_record\n", "            ),\n", "\n", "            pos as (\n", "                select outlet_id, po_id, po_state_id\n", "                from po.purchase_order_status\n", "                where lake_active_record\n", "                and created_at >= current_date - interval '20' day\n", "            ),\n", "\n", "            pg as (\n", "                select po_id, item_id, sum(quantity) as grn_quantity\n", "                from po.po_grn\n", "                where lake_active_record\n", "                and insert_ds_ist >= cast(current_date - interval '20' day as varchar)\n", "                group by 1,2\n", "            ),\n", "            \n", "            ps as (\n", "                select po_id_id as po_id, schedule_date_time + interval '330' minute as po_schedule_ts\n", "                from po.po_schedule\n", "                where lake_active_record\n", "                and active = 1\n", "            ),\n", "\n", "            store_ages as (\n", "                with fs as (\n", "                            select outlet_id, min(date(cart_checkout_ts_ist)) as min_cart_date, current_date as today_date\n", "                            from dwh.fact_sales_order_details\n", "                            where order_create_dt_ist >= current_date - interval '60' day\n", "                            and order_current_status <> 'CANCELLED'\n", "                            group by 1,3\n", "                        )\n", "\n", "                        select outlet_id, date_diff('day', min_cart_date, today_date) as store_age\n", "                        from fs\n", "            ),\n", "\n", "            open_po_raw as (\n", "                select po.outlet_id, poi.item_id, min(created_at) as min_created_at, min(po_schedule_ts) as min_po_schedule_ts, min(expiry_date) as min_expiry_date,\n", "                        sum(case when (pos.po_state_id in (2,3,13,14,15) or (pos.po_state_id = 9 and po.is_multiple_grn = 1)) then (units_ordered - coalesce(grn_quantity,0)) \n", "                            else 0\n", "                        end) as in_transit_po_quantity\n", "                from po\n", "                inner join poi on poi.po_id = po.po_id\n", "                inner join pos on pos.po_id = po.po_id and pos.outlet_id = po.outlet_id\n", "                left join ps on ps.po_id = po.po_id\n", "                left join pg on pg.item_id = poi.item_id and poi.po_id = pg.po_id\n", "                where (pos.po_state_id in (2,3,13,14,15) or (pos.po_state_id = 9 and po.is_multiple_grn = 1))\n", "                group by 1,2\n", "            ),\n", "\n", "            open_po as (\n", "                select outlet_id, item_id, min_created_at, min_po_schedule_ts, min_expiry_date,\n", "                        coalesce(min_po_schedule_ts, min_expiry_date) as possible_landing_date,\n", "                        date_diff('day',current_date, coalesce(min_po_schedule_ts, min_expiry_date)) as days_to_land,\n", "                        in_transit_po_quantity\n", "                from open_po_raw\n", "            ),\n", "\n", "            active_cs_tag as (    \n", "                select merchant_id, product_id, 'enable' as cs_flag\n", "                from iotm\n", "                inner join pfma on pfma.facility_id = iotm.facility_id and iotm.item_id = pfma.item_id\n", "                inner join outlet_details od on od.hot_outlet_id = iotm.outlet_id and od.business_type_id = 7\n", "                left join mid_od mo on mo.outlet_id = iotm.outlet_id\n", "                inner join item_details id on id.item_id = iotm.item_id\n", "                inner join ipm on ipm.item_id = iotm.item_id\n", "                left join open_po op on op.item_id = iotm.item_id and op.outlet_id = iotm.backend_outlet_id\n", "                left join be_inv bi on bi.item_id = iotm.item_id and bi.outlet_id = iotm.be_inv_outlet_id\n", "                left join store_ages sa on sa.outlet_id = iotm.outlet_id\n", "                left join item_inv_base iib on iib.item_id = iotm.item_id and iib.outlet_id = iotm.outlet_id\n", "                where store_age > 30\n", "                -- and ((in_transit_po_quantity > 0 and min_created_at >= current_date - interval '15' day) or be_inv > 0)\n", "                and ((in_transit_po_quantity > 0 and days_to_land >= 0 and days_to_land <= 5) or be_inv > 0)\n", "                and (inv_updated_at_ist < current_date - interval '180' day or inv_updated_at_ist is null)\n", "                and (net_inv = 0 or net_inv is null)\n", "            ),\n", "            \n", "            add_pids_cs_tag as (\n", "                select mo.merchant_id, ipm.product_id, 'enable' as cs_flag\n", "                from pfma\n", "                inner join outlet_details od on od.facility_id = pfma.facility_id and od.business_type_id = 7\n", "                inner join mid_od mo on mo.outlet_id = od.hot_outlet_id\n", "                inner join ipm on ipm.item_id = pfma.item_id\n", "                left join item_inv_base iib on iib.item_id = pfma.item_id and iib.outlet_id = od.hot_outlet_id\n", "                left join store_ages sa on sa.outlet_id = od.hot_outlet_id\n", "                where store_age > 30\n", "                and (inv_updated_at_ist < current_date - interval '90' day or inv_updated_at_ist is null)\n", "                and ipm.product_id in {add_pids}\n", "            ),\n", "\n", "            ---------------------\n", "            coming_soon_tag as (\n", "                select mapping_id\n", "                from cms.gr_merchant_product_tag_mapping\n", "                where lake_active_record\n", "                and tag_id in (13948) -- coming_soon_tag\n", "            ),\n", "\n", "\n", "            mid_pid_mapping as (\n", "                select mp.merchant_id, mp.product_id, 'enable' as system_tag\n", "                from cms.gr_merchant_product_mapping mp\n", "                inner join coming_soon_tag cs on cs.mapping_id = mp.id\n", "                where lake_active_record and enabled_flag\n", "            ),\n", "\n", "            all_mappings as (\n", "                select merchant_id, product_id\n", "                from active_cs_tag\n", "\n", "                union\n", "                \n", "                select merchant_id, product_id\n", "                from add_pids_cs_tag\n", "\n", "                union\n", "\n", "                select merchant_id, product_id\n", "                from mid_pid_mapping\n", "            ),\n", "\n", "            final_cs_tag as (\n", "                select am.*, coalesce(coalesce(act.cs_flag, apct.cs_flag), 'disable') as cs_flag\n", "                from all_mappings am\n", "                left join active_cs_tag act on act.merchant_id = am.merchant_id and act.product_id = am.product_id\n", "                left join add_pids_cs_tag apct on apct.merchant_id = am.merchant_id and apct.product_id = am.product_id\n", "            ),\n", "\n", "            diff_base as (\n", "                select fc.product_id, fc.merchant_id, cs_flag\n", "                from final_cs_tag fc\n", "                left join mid_pid_mapping mp on mp.merchant_id = fc.merchant_id and mp.product_id = fc.product_id\n", "                where (cs_flag != system_tag or system_tag is null)\n", "            )\n", "\n", "            select product_id, merchant_id, cs_flag, cast(current_timestamp as timestamp) as updated_at_ist\n", "            from diff_base\n", "            where product_id not in {remove_pids}\n", "\n", "            \"\"\"\n", "# , CON_TRINO)\n", "\n", "\n", "column_dtypes = [\n", "    {\"name\": \"product_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"merchant_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"cs_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "]\n", "\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"mid_pid_coming_soon_tag\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"product_id\", \"merchant_id\"],\n", "    # \"partition_key\": [\"insert_ds_ist\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"mid_pid_coming_soon\",\n", "}\n", "\n", "to_trino(coming_soon, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "2312eb19-0b59-47b8-9342-dce491aaa6c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}