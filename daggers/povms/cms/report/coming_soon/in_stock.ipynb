{"cells": [{"cell_type": "code", "execution_count": null, "id": "03fe4835-8bcf-433f-9398-3c18a5feb379", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import os\n", "from datetime import date, datetime, timedelta\n", "from tabulate import tabulate\n", "from tqdm.notebook import tqdm\n", "\n", "!pip install matplotlib==3.8\n", "\n", "CON_TRINO = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "abeaa616-f6f8-4f01-b96d-9196c8b5511c", "metadata": {"tags": []}, "outputs": [], "source": ["def read_sql_query(sql, con):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        print(\n", "            f\"Read attempt: {attempt}... \\nRun started at {datetime.now() + timed<PERSON>ta(hours = 5.5)}\"\n", "        )\n", "        try:\n", "            start = time.time()\n", "            df = pd.read_sql_query(sql, con)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Time: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Time: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "98a9306a-6cdc-451f-808b-7c10c4378f97", "metadata": {"tags": []}, "outputs": [], "source": ["def from_sheets(sheet_id, sheet_name):\n", "    max_tries = 2\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            df = pb.from_sheets(sheet_id, sheet_name)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pulled from sheet in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pulled from sheet in: \", end - start, \"s\")\n", "            return df\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "c74dd00c-1733-40ce-8d40-b1acdafd5f42", "metadata": {}, "outputs": [], "source": ["def to_trino(df_to_trino, name, **kwargs):\n", "    max_tries = 3\n", "    for attempt in range(max_tries):\n", "        try:\n", "            start = time.time()\n", "            pb.to_trino(df_to_trino, **kwargs)\n", "            end = time.time()\n", "            if (end - start) > 60:\n", "                print(\"Data pushed in table in: \", (end - start) / 60, \"min\")\n", "            else:\n", "                print(\"Data pushed in table in: \", end - start, \"s\")\n", "            break\n", "        except BaseException as e:\n", "            print(e)\n", "            pb.send_slack_message(channel=\"bl-data-inventory-pvt\", text=f\"{name} - upload failed\")\n", "            time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "id": "6fb70d8d-0c6f-4b97-8e44-c15b93e23005", "metadata": {}, "outputs": [], "source": ["def send_slack_alert(error_message):\n", "\n", "    slack_channel = from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "    slack_channel = (\n", "        slack_channel[slack_channel[\"alert\"] == \"alerting_anuvrat\"].reset_index().iloc[:, 2][0]\n", "    )\n", "    error_message = error_message + \"\\ncc <@U03RJ5FRXFC>\"\n", "\n", "    pb.send_slack_message(channel=slack_channel, text=error_message)"]}, {"cell_type": "code", "execution_count": null, "id": "c14f0518-322c-44a8-b032-416a1efef7db", "metadata": {}, "outputs": [], "source": ["alert_slack_channel = from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "alert_slack_channel = (\n", "    alert_slack_channel[alert_slack_channel[\"alert\"] == \"alerting_anuvrat\"]\n", "    .reset_index()\n", "    .iloc[:, 2][0]\n", ")\n", "alert_slack_channel"]}, {"cell_type": "markdown", "id": "7f1df3a5-8fce-405d-84a7-5097d0c49538", "metadata": {}, "source": ["### in stock by "]}, {"cell_type": "code", "execution_count": null, "id": "5ba0e3b9-decd-4e86-8d20-a0e8c081a610", "metadata": {}, "outputs": [], "source": ["in_stock_by = f\"\"\"\n", "            with od as (\n", "                select city_name, hot_outlet_id, inv_outlet_id, facility_id, facility_name, business_type_id, taggings, store_type\n", "                from supply_etls.outlet_details\n", "                where ars_active = 1\n", "            ),\n", "\n", "            mid_od as (\n", "                select pos_outlet_id as outlet_id, frontend_merchant_id as merchant_id\n", "                from dwh.dim_merchant_outlet_facility_mapping\n", "                where is_pos_outlet_active = 1\n", "                and is_mapping_enabled\n", "                and is_current\n", "                group by 1,2\n", "            ),\n", "\n", "            id as (\n", "                select item_id, item_name, l0_category as l0, l1_category as l1, l2_category as l2, p_type as ptype\n", "                from supply_etls.item_details\n", "                where handling_type_id not in (8)\n", "            ),\n", "\n", "            ipm as (\n", "                select item_id, product_id\n", "                from dwh.dim_item_product_offer_mapping\n", "                where is_current\n", "                and is_offer = False\n", "            ),\n", "\n", "            iotm as (\n", "                select o.facility_id, outlet_id, item_id, od.hot_outlet_id as be_hot_outlet_id, od.inv_outlet_id as be_inv_outlet_id, od.facility_id as be_facility_id\n", "                from rpc.item_outlet_tag_mapping i\n", "                inner join od on cast(od.hot_outlet_id as varchar) = i.tag_value\n", "                inner join od o on o.hot_outlet_id = i.outlet_id\n", "                where lake_active_record\n", "                and tag_type_id = 8\n", "                and i.active = 1\n", "            ),\n", "\n", "            pfma as (\n", "                select facility_id, item_id\n", "                from rpc.product_facility_master_assortment\n", "                where lake_active_record\n", "                and master_assortment_substate_id in (1,3)\n", "                and active = 1\n", "            ),\n", "\n", "            base_assortment as (\n", "                select be_hot_outlet_id, be_inv_outlet_id, be_facility_id, iotm.facility_id, iotm.outlet_id, merchant_id, iotm.item_id, product_id\n", "                from iotm\n", "                inner join pfma on pfma.facility_id = iotm.facility_id and pfma.item_id = iotm.item_id\n", "                inner join mid_od mo on mo.outlet_id = iotm.outlet_id\n", "                inner join ipm on ipm.item_id = iotm.item_id\n", "            ),\n", "\n", "            open_sto as (\n", "                select sto_id, sender_facility_id, receiver_facility_id, item_id, sum(open_sto_quantity + billed_sto_quantity) as total_open_sto\n", "                from supply_etls.inventory_metrics_open_sto\n", "                group by 1,2,3,4\n", "            ),\n", "\n", "            sto_dt as (\n", "                select id as sto_id, dispatch_time + interval '330' minute as dispatch_ts_ist, created_at + interval '330' minute as sto_created_at_ist\n", "                from po.sto\n", "                where lake_active_record\n", "                and created_at > current_date - interval '100' day\n", "            ),\n", "\n", "            node_facility_mapping as (\n", "                select cast(logistic_node_id as varchar) as store_id,\n", "                        facility_id, f.name as facility_name\n", "                from retail.console_outlet_logistic_mapping lm\n", "                inner join retail.console_outlet co on co.id = lm.outlet_id\n", "                inner join retail.warehouse_facility f on f.id = co.facility_id\n", "                where lm.lake_active_record and co.lake_active_record and f.lake_active_record\n", "            ),\n", "\n", "            in_transit_time as (\n", "                select consignment_id, source_store_id, destination_store_id, \n", "                        date_diff('hour', min(logs.install_ts), max(logs.install_ts)) + 3 + 5 as delivery_tat ---- add 3 for grn_time and 5 hours for 90% accuracy\n", "                from transit_server.transit_consignment cons\n", "                inner join transit_server.transit_consignment_state_logs logs on cons.id = logs.consignment_id\n", "                where logs.lake_active_record and cons.lake_active_record\n", "                and cons.insert_ds_ist >= cast(current_date - interval '14' day as varchar)\n", "                and logs.insert_ds_ist >= cast(current_date - interval '14' day as varchar)\n", "                and cons.state = 'COMPLETED'\n", "                and logs.to_state in ('COMPLETED','READY_FOR_DISPATCH')\n", "                GROUP BY 1,2,3\n", "                HAVING count(*) > 1\n", "                AND date_diff('hour', min(logs.install_ts), max(logs.install_ts)) > 0\n", "            ),\n", "\n", "            outliers as (\n", "                select source_store_id, destination_store_id, \n", "                        approx_percentile(delivery_tat, 0.8) as top_outlier,\n", "                        approx_percentile(delivery_tat, 0.1) as bottom_outlier,\n", "                        count(*) as data_points\n", "                from in_transit_time\n", "                group by 1,2\n", "            ),\n", "\n", "            cleaned_data as (\n", "                select iit.source_store_id, iit.destination_store_id, \n", "                        max(delivery_tat) as max_tat,\n", "                        min(delivery_tat) as min_tat,\n", "                        avg(delivery_tat) as avg_tat,\n", "                        approx_percentile(delivery_tat, 0.5) as median_tat,\n", "                        count(*) as data_points\n", "                from in_transit_time iit\n", "                inner join outliers o on iit.source_store_id = o.source_store_id and iit.destination_store_id = o.destination_store_id \n", "                            and delivery_tat <= top_outlier and delivery_tat >= bottom_outlier\n", "                group by 1,2\n", "            ),\n", "\n", "            mid_pid_mapping as (\n", "                select mp.merchant_id, mp.product_id, inventory_limit\n", "                from cms.gr_merchant_product_mapping mp\n", "                where lake_active_record and enabled_flag\n", "            ),\n", "\n", "            final_tt as (\n", "                select sn.facility_id as sender_facility_id, sn.facility_name as sender_facility_name, \n", "                        dn.facility_id as destination_facility_id, sn.facility_name as destination_facility_name, \n", "                        max_tat, min_tat, avg_tat, median_tat, data_points\n", "                from cleaned_data cd\n", "                inner join node_facility_mapping sn on sn.store_id = cd.source_store_id\n", "                inner join node_facility_mapping dn on dn.store_id = cd.destination_store_id\n", "                -- inner join retail.console_outlet co on cast(co.id as varchar) = dn.store_id and co.lake_active_record and co.active = 1 and co.business_type_id = 7\n", "            ),\n", "\n", "            store_ages as (\n", "                with fs as (\n", "                            select outlet_id, min(date(cart_checkout_ts_ist)) as min_cart_date, current_date as today_date\n", "                            from dwh.fact_sales_order_details\n", "                            where order_create_dt_ist is not null\n", "                            and order_current_status <> 'CANCELLED'\n", "                            group by 1,3\n", "                        )\n", "\n", "                        select outlet_id, date_diff('day', min_cart_date, today_date) as store_age\n", "                        from fs\n", "            ),\n", "\n", "            collated_data as (\n", "                select ba.facility_id, ba.outlet_id, ba.merchant_id, ba.item_id, ba.product_id,\n", "                        os.sto_id, total_open_sto, os.sender_facility_id, os.receiver_facility_id,\n", "                        dispatch_ts_ist, sto_created_at_ist, avg_tat, inventory_limit,\n", "                        DATE_ADD('hour', cast(ceil(avg_tat) as int), dispatch_ts_ist) AS exp_arrival_ts\n", "                from base_assortment ba\n", "                inner join open_sto os on os.receiver_facility_id = ba.facility_id and ba.item_id = os.item_id\n", "                left join sto_dt sd on sd.sto_id = os.sto_id\n", "                left join final_tt ft on ft.sender_facility_id = os.sender_facility_id and ft.destination_facility_id = os.receiver_facility_id\n", "                left join mid_pid_mapping mp on mp.product_id = ba.product_id and mp.merchant_id = ba.merchant_id\n", "                inner join od on od.inv_outlet_id = ba.outlet_id and od.taggings = 'fe'\n", "                inner join od o on o.facility_id = os.sender_facility_id and o.taggings = 'be'\n", "                left join store_ages sa on sa.outlet_id = ba.outlet_id\n", "                where (inventory_limit = 0 or inventory_limit is null) and avg_tat is not null\n", "                and sa.store_age > 15\n", "                and ba.merchant_id is not null\n", "                and ba.product_id is not null\n", "            ),\n", "\n", "            cd_rank as (\n", "                select *, row_number() over(partition by merchant_id, product_id order by exp_arrival_ts asc) as rnk\n", "                from collated_data\n", "                where exp_arrival_ts >= current_timestamp + interval '0' hour\n", "            ),\n", "\n", "            in_stock_tag as (\n", "                select mapping_id\n", "                from cms.gr_merchant_product_tag_mapping\n", "                where lake_active_record\n", "                and tag_id in (15225) -- coming_soon_tag\n", "            ),\n", "\n", "\n", "            property_mapping as (\n", "                select mapping_id, property_value, cast(from_iso8601_timestamp(property_value) as timestamp) + interval '330' minute as cms_exp_ts\n", "                from cms.merchant_product_property_mapping\n", "                where lake_active_record and enabled_flag\n", "                and insert_ds_ist >= cast(current_date - interval '5' day as varchar)\n", "                and property_id = 67\n", "            ),\n", "\n", "\n", "            mid_pid_mapping_check as (\n", "                select mp.merchant_id, mp.product_id, cms_exp_ts\n", "                from cms.gr_merchant_product_mapping mp\n", "                inner join in_stock_tag cs on cs.mapping_id = mp.id\n", "                inner join property_mapping pm on pm.mapping_id = mp.id\n", "                where lake_active_record and enabled_flag\n", "            ),\n", "\n", "            all_combos as (\n", "                select merchant_id, product_id\n", "                from mid_pid_mapping_check\n", "                group by 1,2\n", "\n", "                union\n", "\n", "                select merchant_id, product_id\n", "                from cd_rank where rnk = 1\n", "                group by 1,2\n", "            )\n", "\n", "            select ac.merchant_id, ac.product_id, cr.sto_id,\n", "                    exp_arrival_ts, cms_exp_ts, \n", "                    date_diff('hour', cms_exp_ts, exp_arrival_ts) as diff_hour,\n", "                        case when (date_diff('hour', cms_exp_ts, exp_arrival_ts) > 8) then exp_arrival_ts\n", "                        when (date_diff('hour', cms_exp_ts, exp_arrival_ts) < 8) then cms_exp_ts\n", "                        when exp_arrival_ts is null then cms_exp_ts\n", "                        else exp_arrival_ts\n", "                    end final_exp_ts,\n", "                    case when (date_diff('hour', cms_exp_ts, exp_arrival_ts) > 8) then 'enable'\n", "                        when (date_diff('hour', cms_exp_ts, exp_arrival_ts) < 8) then 'ignore'\n", "                        when (exp_arrival_ts is null) then 'disable'\n", "                        else 'enable'\n", "                    end as in_stock_flag,\n", "                    cast(current_date as date) as date_ist,\n", "                    cast(current_timestamp as timestamp) as updated_at_ist\n", "\n", "            from all_combos ac\n", "            left join cd_rank cr on cr.merchant_id = ac.merchant_id and cr.product_id = ac.product_id and cr.rnk = 1\n", "            left join mid_pid_mapping_check mpm on mpm.merchant_id = ac.merchant_id and mpm.product_id = ac.product_id\n", "            where exp_arrival_ts is not null\n", "            \"\"\"\n", "# , CON_TRINO)\n", "\n", "####### BASE TABLE\n", "column_dtypes = [\n", "    {\"name\": \"merchant_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"product_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"sto_id\", \"type\": \"integer\", \"description\": \"sample description\"},\n", "    {\"name\": \"exp_arrival_ts\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"cms_exp_ts\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"diff_hour\", \"type\": \"real\", \"description\": \"sample description\"},\n", "    {\"name\": \"final_exp_ts\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "    {\"name\": \"in_stock_flag\", \"type\": \"varchar\", \"description\": \"sample description\"},\n", "    {\"name\": \"date_ist\", \"type\": \"DATE\", \"description\": \"sample description\"},\n", "    {\"name\": \"updated_at_ist\", \"type\": \"timestamp(6)\", \"description\": \"sample description\"},\n", "]\n", "\n", "\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"mid_pid_in_stock_estimate\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"merchant_id\", \"product_id\"],\n", "    # \"partition_key\": [\"insert_ds_ist\"],\n", "    # \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"truncate\",  # append, truncate or upsert,\n", "    \"table_description\": \"mid_pid_in_stock_estimate\",\n", "}\n", "\n", "to_trino(in_stock_by, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)\n", "\n", "\n", "####### LOG TABLE\n", "kwargs = {\n", "    \"schema_name\": \"supply_etls\",\n", "    \"table_name\": \"mid_pid_in_stock_estimate_log\",\n", "    \"column_dtypes\": column_dtypes,\n", "    \"primary_key\": [\"merchant_id\", \"product_id\"],\n", "    \"partition_key\": [\"date_ist\"],\n", "    \"incremental_key\": \"updated_at_ist\",\n", "    \"load_type\": \"append\",  # append, truncate or upsert,\n", "    \"table_description\": \"mid_pid_in_stock_estimate_log\",\n", "}\n", "\n", "to_trino(in_stock_by, kwargs[\"schema_name\"] + \".\" + kwargs[\"table_name\"], **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "01469ec6-fbe4-4eaa-a155-08db47cc4455", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8fb64095-eb39-433e-933a-84f84df83517", "metadata": {}, "source": ["## in stock accuracy"]}, {"cell_type": "code", "execution_count": null, "id": "0d5de9c0-7f0c-4f1b-96d8-ef26c6dff1d4", "metadata": {}, "outputs": [], "source": ["if datetime.now().hour in [10, 11, 12]:\n", "    print(\"sending alert\")\n", "\n", "    lookback_days = 10\n", "\n", "    in_stock_acc = read_sql_query(\n", "        f\"\"\"\n", "                        with base_in_stock as (\n", "                            select date_ist, updated_at_ist, merchant_id, product_id, sto_id, final_exp_ts\n", "                            from supply_etls.mid_pid_in_stock_estimate_log\n", "                            where date_ist >= current_date - interval '{lookback_days}' day and date_ist < current_date\n", "                            and final_exp_ts < current_timestamp\n", "                        ),\n", "\n", "                        latest_ts as (\n", "                            select date_ist, merchant_id, product_id, sto_id, max(updated_at_ist) as updated_at_ist\n", "                            from base_in_stock\n", "                            group by 1,2,3,4\n", "                        ),\n", "\n", "                        adjusted_in_stock_ts as (\n", "                            select bis.date_ist, bis.updated_at_ist, bis.merchant_id, bis.product_id, bis.sto_id, bis.final_exp_ts\n", "                            from base_in_stock bis\n", "                            inner join latest_ts lt on lt.date_ist = bis.date_ist and lt.updated_at_ist = bis.updated_at_ist and lt.merchant_id = bis.merchant_id \n", "                                                        and lt.product_id = bis.product_id and lt.sto_id = bis.sto_id\n", "                        ),\n", "\n", "                        ipm as (\n", "                            select item_id, product_id\n", "                            from dwh.dim_item_product_offer_mapping\n", "                            where is_current\n", "                            and is_offer = False\n", "                        ),\n", "\n", "                        mid_od as (\n", "                            select pos_outlet_id as outlet_id, frontend_merchant_id as merchant_id\n", "                            from dwh.dim_merchant_outlet_facility_mapping\n", "                            where is_pos_outlet_active = 1\n", "                            and is_mapping_enabled\n", "                            and is_current\n", "                            group by 1,2\n", "                        ),\n", "\n", "                        pp as (\n", "                            select id, item_id, name as item_name, variant_id, variant_mrp, cast(weight_in_gm * 0.001 as real) as weight_in_kg\n", "                            from rpc.product_product\n", "                            where lake_active_record and active = 1 and approved = 1\n", "                        ),\n", "\n", "\n", "                        invoice_details as (\n", "                            select outlet_id, grofers_order_id, created_at, merchant_id, invoice_id, id -- , json_extract_scalar(invoice_meta, '$.source_entity.id') as source_entity_id\n", "                            from pos.pos_invoice\n", "                            where lake_active_record\n", "                            and insert_ds_ist >= cast(current_date - interval '{lookback_days}' day as varchar)\n", "                            and invoice_type_id in (5,14,16)\n", "                            and grofers_order_id is not null and grofers_order_id != ''\n", "                        ),\n", "\n", "\n", "                        gd as (\n", "                            select outlet_id, po_id, grn_id, vendor_invoice_id, entity_vendor_id\n", "                            from ims.ims_inward_invoice\n", "                            where lake_active_record\n", "                            and insert_ds_ist >= cast(current_date - interval '{lookback_days}' day as varchar)\n", "                        ),\n", "\n", "\n", "                        gid as (\n", "                            select variant_id, outlet_id, grn_id, \"delta\" as delta_core, created_at\n", "                            from ims.ims_inventory_stock_details\n", "                            where lake_active_record\n", "                            and insert_ds_ist >= cast(current_date - interval '{lookback_days}' day as varchar)\n", "                        ),\n", "\n", "\n", "                        fgd as (\n", "                            select try_cast(id.grofers_order_id as int) as sto_id, pp.item_id, --entity_vendor_id,\n", "                                    min(gid.created_at + interval '330' minute) as grn_timestamp,\n", "                                    sum(gid.delta_core) as grn_quantity\n", "                            from gid\n", "                            inner join gd on gd.grn_id = gid.grn_id\n", "                            inner join pp on pp.variant_id = gid.variant_id\n", "                            inner join invoice_details id on id.invoice_id = gd.vendor_invoice_id\n", "                            group by 1,2--,3\n", "                        ),\n", "\n", "                        store_ages as (\n", "                            with fs as (\n", "                                        select outlet_id, min(date(cart_checkout_ts_ist)) as min_cart_date, current_date as today_date\n", "                                        from dwh.fact_sales_order_details\n", "                                        where order_create_dt_ist is not null\n", "                                        and order_current_status <> 'CANCELLED'\n", "                                        group by 1,3\n", "                                    )\n", "\n", "                                    select outlet_id, date_diff('day', min_cart_date, today_date) as store_age\n", "                                    from fs\n", "                        ),\n", "\n", "                        accuracy_base as (\n", "                            select ai.*, ipm.item_id, mid_od.outlet_id, store_age, grn_timestamp, date_diff('hour', final_exp_ts, grn_timestamp) as diff_hour, \n", "                                    case when (store_age < 30) then '<30 days'\n", "                                         when store_age between 30 and 60 then '30-60 days'\n", "                                         else '60 + days'\n", "                                    end as store_bucket,\n", "                                    case when date_diff('hour', final_exp_ts, grn_timestamp) <= 0 then 'accurate🥳' else 'not accurate😔' end as flag\n", "                            from adjusted_in_stock_ts ai\n", "                            inner join ipm on ipm.product_id = ai.product_id\n", "                            inner join mid_od on mid_od.merchant_id = ai.merchant_id\n", "                            left join fgd on fgd.sto_id = ai.sto_id and fgd.item_id = ipm.item_id\n", "                            left join store_ages sa on sa.outlet_id = mid_od.outlet_id\n", "                        )\n", "\n", "\n", "                        select date_ist, -- store_bucket as \"store_bucket::multi-filter\",\n", "                                count(case when flag = 'accurate🥳' then final_exp_ts end) as accurate_cases,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour <=1) then final_exp_ts end) as non_accurate_cases_1,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour <=3) then final_exp_ts end) as non_accurate_cases_3,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour <=6) then final_exp_ts end) as non_accurate_cases_6,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour > 6) then final_exp_ts end) as non_accurate_cases_6_plus,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour is null) then final_exp_ts end) as non_accurate_cases_didnt_reach,\n", "                                count(*) as all_cases,\n", "                                count(case when flag = 'accurate🥳' then final_exp_ts end) * 1.0000 / count(*) as \"accuracy_perc\"\n", "                        from accuracy_base\n", "                        group by 1-- ,2\n", "                        order by 1 desc\n", "\n", "                        /*\n", "                        union\n", "\n", "                        select date_ist, 'all-stores' as \"store_bucket::multi-filter\",\n", "                                count(case when flag = 'accurate🥳' then final_exp_ts end) as accurate_cases,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour <=1) then final_exp_ts end) as non_accurate_cases_1,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour <=3) then final_exp_ts end) as non_accurate_cases_3,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour <=6) then final_exp_ts end) as non_accurate_cases_6,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour > 6) then final_exp_ts end) as non_accurate_cases_6_plus,\n", "                                count(case when (flag = 'not accurate😔') and (diff_hour is null) then final_exp_ts end) as non_accurate_cases_didnt_reach,\n", "                                count(*) as all_cases,\n", "                                count(case when flag = 'accurate🥳' then final_exp_ts end) * 1.0000 / count(*) as \"accuracy_perc\"\n", "                        from accuracy_base\n", "                        group by 1,2\n", "                        */\n", "                    \"\"\",\n", "        CON_TRINO,\n", "    )\n", "\n", "    in_stock_acc_df = (\n", "        in_stock_acc[\n", "            [\n", "                \"date_ist\",\n", "                \"accurate_cases\",\n", "                \"non_accurate_cases_3\",\n", "                \"non_accurate_cases_6\",\n", "                \"non_accurate_cases_6_plus\",\n", "                \"non_accurate_cases_didnt_reach\",\n", "                \"all_cases\",\n", "                \"accuracy_perc\",\n", "            ]\n", "        ]\n", "        .sort_values(by=[\"date_ist\"], ascending=False)\n", "        .reset_index(drop=True)\n", "    )\n", "    in_stock_acc_df.columns = [\n", "        \"Date\",\n", "        \"Accurate Cases\",\n", "        \"Cases Delayed By 3 Hrs\",\n", "        \"Cases Delayed By 6 Hrs\",\n", "        \"Cases Delayed By 6+ Hrs\",\n", "        \"Cases Didn't Reach Store\",\n", "        \"All Cases\",\n", "        \"Accuracy%\",\n", "    ]\n", "    in_stock_acc_df[\"Accuracy%\"] = (in_stock_acc_df[\"Accuracy%\"].astype(float) * 100).round(\n", "        2\n", "    ).astype(str) + \"%\"\n", "    in_stock_acc_df\n", "\n", "    import matplotlib.pyplot as plt\n", "\n", "    def render_mpl_table(\n", "        data,\n", "        col_width=4.5,\n", "        row_height=0.625,\n", "        font_size=20,\n", "        header_color=\"#E96125\",\n", "        row_colors=[\"#f1f1f2\", \"w\"],\n", "        edge_color=\"black\",\n", "        bbox=[0, 0, 1, 1],\n", "        header_columns=0,\n", "        ax=None,\n", "        **kwargs,\n", "    ):\n", "        if ax is None:\n", "            size = (np.array(data.shape[::-1]) + np.array([0, 1])) * np.array(\n", "                [col_width, row_height]\n", "            )\n", "            fig, ax = plt.subplots(figsize=size)\n", "            ax.axis(\"off\")\n", "        mpl_table = ax.table(cellText=data.values, bbox=bbox, colLabels=data.columns, **kwargs)\n", "        mpl_table.auto_set_font_size(False)\n", "        mpl_table.set_fontsize(font_size)\n", "\n", "        for k, cell in mpl_table._cells.items():\n", "            cell.set_edgecolor(edge_color)\n", "            if k[0] == 0 or k[1] < header_columns:\n", "                cell.set_text_props(weight=\"extra bold\", color=\"w\")\n", "                cell.set_facecolor(header_color)\n", "            #         elif k[0] == data.shape[0]:\n", "            #             cell.set_text_props(weight=\"bold\", ma=\"center\")\n", "            # cell.set_facecolor('#f1f1f2')\n", "            else:\n", "                cell.set_facecolor(row_colors[k[0] % len(row_colors)])\n", "        mpl_table.auto_set_column_width(col=list(range(len(data.columns))))\n", "        return ax.get_figure(), ax\n", "\n", "    if in_stock_acc_df.shape[0] > 0:\n", "        fig, ax = render_mpl_table(in_stock_acc_df, header_columns=0)\n", "        fig.savefig(\"in_stock_accuracy.png\")\n", "\n", "    accuracy_channel = from_sheets(\"1AdjnAHCBHCCVX6RZ_xx5rbdsICeTwpJy1KFbee9AsJM\", \"alert_channel\")\n", "    accuracy_channel = (\n", "        accuracy_channel[accuracy_channel[\"alert\"] == \"in_stock_accuracy\"]\n", "        .reset_index()\n", "        .iloc[:, 2][0]\n", "    )\n", "    accuracy_channel\n", "\n", "    text_message = f\"Summary of last 10 days of In-Stock by predictions:stonks:\\nFor details refer to this <https://redash-queries.grofer.io/queries/356858/source| query>\"\n", "    pb.send_slack_message(\n", "        channel=accuracy_channel,\n", "        text=text_message,\n", "        files=[f\"./in_stock_accuracy.png\"],\n", "    )\n", "else:\n", "    print(\"skipping alert\")"]}, {"cell_type": "code", "execution_count": null, "id": "a11afb4f-93ea-4676-896d-e570362b1a90", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}