alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
concurrency: 3
dag_name: coming_soon
dag_type: report
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    service_account_name: blinkit-prod-airflow-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: povms
notebooks:
- alias: coming_soon
  executor_config:
    load_type: tiny
    node_type: spot
  name: coming_soon
  parameters: null
  tag: parallel
- alias: in_stock
  executor_config:
    load_type: tiny
    node_type: spot
  name: in_stock
  parameters: null
  tag: parallel
owner:
  email: <EMAIL>
  slack_id: U03RJ5FRXFC
path: povms/cms/report/coming_soon
paused: false
pool: povms_pool
project_name: cms
schedule:
  end_date: '2025-09-22T00:00:00'
  interval: 0 2,6,10,14,18,22 * * *
  start_date: '2025-05-12T00:00:00'
schedule_type: fixed
sla: 130 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
